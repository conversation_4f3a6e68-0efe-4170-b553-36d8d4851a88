{"ast": null, "code": "import { env as n } from './env.js';\nfunction o(r) {\n  return n.isServer ? null : r instanceof Node ? r.ownerDocument : r != null && r.hasOwnProperty(\"current\") && r.current instanceof Node ? r.current.ownerDocument : document;\n}\nexport { o as getOwnerDocument };", "map": {"version": 3, "names": ["env", "n", "o", "r", "isServer", "Node", "ownerDocument", "hasOwnProperty", "current", "document", "getOwnerDocument"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/utils/owner.js"], "sourcesContent": ["import{env as n}from'./env.js';function o(r){return n.isServer?null:r instanceof Node?r.ownerDocument:r!=null&&r.hasOwnProperty(\"current\")&&r.current instanceof Node?r.current.ownerDocument:document}export{o as getOwnerDocument};\n"], "mappings": "AAAA,SAAOA,GAAG,IAAIC,CAAC,QAAK,UAAU;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,OAAOF,CAAC,CAACG,QAAQ,GAAC,IAAI,GAACD,CAAC,YAAYE,IAAI,GAACF,CAAC,CAACG,aAAa,GAACH,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACI,cAAc,CAAC,SAAS,CAAC,IAAEJ,CAAC,CAACK,OAAO,YAAYH,IAAI,GAACF,CAAC,CAACK,OAAO,CAACF,aAAa,GAACG,QAAQ;AAAA;AAAC,SAAOP,CAAC,IAAIQ,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}