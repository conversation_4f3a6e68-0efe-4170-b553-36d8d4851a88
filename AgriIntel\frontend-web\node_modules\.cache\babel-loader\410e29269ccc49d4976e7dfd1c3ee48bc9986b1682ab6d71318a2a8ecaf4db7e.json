{"ast": null, "code": "import { GRID_CHECKBOX_SELECTION_COL_DEF } from '../../../../colDef';\nimport { buildWarning } from '../../../../utils/warning';\nfunction sanitizeCellValue(value, csvOptions) {\n  if (typeof value === 'string') {\n    if (csvOptions.shouldAppendQuotes || csvOptions.escapeFormulas) {\n      const escapedValue = value.replace(/\"/g, '\"\"');\n      // Make sure value containing delimiter or line break won't be split into multiple cells\n      if ([csvOptions.delimiter, '\\n', '\\r', '\"'].some(delimiter => value.includes(delimiter))) {\n        return `\"${escapedValue}\"`;\n      }\n      if (csvOptions.escapeFormulas) {\n        // See https://owasp.org/www-community/attacks/CSV_Injection\n        if (['=', '+', '-', '@', '\\t', '\\r'].includes(escapedValue[0])) {\n          return `'${escapedValue}`;\n        }\n      }\n      return escapedValue;\n    }\n    return value;\n  }\n  return value;\n}\nexport const serializeCellValue = (cellParams, options) => {\n  const {\n    csvOptions,\n    ignoreValueFormatter\n  } = options;\n  let value;\n  if (ignoreValueFormatter) {\n    var _cellParams$value2;\n    const columnType = cellParams.colDef.type;\n    if (columnType === 'number') {\n      value = String(cellParams.value);\n    } else if (columnType === 'date' || columnType === 'dateTime') {\n      var _cellParams$value;\n      value = (_cellParams$value = cellParams.value) == null ? void 0 : _cellParams$value.toISOString();\n    } else if (typeof ((_cellParams$value2 = cellParams.value) == null ? void 0 : _cellParams$value2.toString) === 'function') {\n      value = cellParams.value.toString();\n    } else {\n      value = cellParams.value;\n    }\n  } else {\n    value = cellParams.formattedValue;\n  }\n  return sanitizeCellValue(value, csvOptions);\n};\nconst objectFormattedValueWarning = buildWarning(['MUI: When the value of a field is an object or a `renderCell` is provided, the CSV export might not display the value correctly.', 'You can provide a `valueFormatter` with a string representation to be used.']);\nclass CSVRow {\n  constructor(options) {\n    this.options = void 0;\n    this.rowString = '';\n    this.isEmpty = true;\n    this.options = options;\n  }\n  addValue(value) {\n    if (!this.isEmpty) {\n      this.rowString += this.options.csvOptions.delimiter;\n    }\n    if (value === null || value === undefined) {\n      this.rowString += '';\n    } else if (typeof this.options.sanitizeCellValue === 'function') {\n      this.rowString += this.options.sanitizeCellValue(value, this.options.csvOptions);\n    } else {\n      this.rowString += value;\n    }\n    this.isEmpty = false;\n  }\n  getRowString() {\n    return this.rowString;\n  }\n}\nconst serializeRow = ({\n  id,\n  columns,\n  getCellParams,\n  csvOptions,\n  ignoreValueFormatter\n}) => {\n  const row = new CSVRow({\n    csvOptions\n  });\n  columns.forEach(column => {\n    const cellParams = getCellParams(id, column.field);\n    if (process.env.NODE_ENV !== 'production') {\n      if (String(cellParams.formattedValue) === '[object Object]') {\n        objectFormattedValueWarning();\n      }\n    }\n    row.addValue(serializeCellValue(cellParams, {\n      ignoreValueFormatter,\n      csvOptions\n    }));\n  });\n  return row.getRowString();\n};\nexport function buildCSV(options) {\n  const {\n    columns,\n    rowIds,\n    csvOptions,\n    ignoreValueFormatter,\n    apiRef\n  } = options;\n  const CSVBody = rowIds.reduce((acc, id) => `${acc}${serializeRow({\n    id,\n    columns,\n    getCellParams: apiRef.current.getCellParams,\n    ignoreValueFormatter,\n    csvOptions\n  })}\\r\\n`, '').trim();\n  if (!csvOptions.includeHeaders) {\n    return CSVBody;\n  }\n  const filteredColumns = columns.filter(column => column.field !== GRID_CHECKBOX_SELECTION_COL_DEF.field);\n  const headerRows = [];\n  if (csvOptions.includeColumnGroupsHeaders) {\n    const columnGroupLookup = apiRef.current.unstable_getAllGroupDetails();\n    let maxColumnGroupsDepth = 0;\n    const columnGroupPathsLookup = filteredColumns.reduce((acc, column) => {\n      const columnGroupPath = apiRef.current.unstable_getColumnGroupPath(column.field);\n      acc[column.field] = columnGroupPath;\n      maxColumnGroupsDepth = Math.max(maxColumnGroupsDepth, columnGroupPath.length);\n      return acc;\n    }, {});\n    for (let i = 0; i < maxColumnGroupsDepth; i += 1) {\n      const headerGroupRow = new CSVRow({\n        csvOptions,\n        sanitizeCellValue\n      });\n      headerRows.push(headerGroupRow);\n      filteredColumns.forEach(column => {\n        const columnGroupId = (columnGroupPathsLookup[column.field] || [])[i];\n        const columnGroup = columnGroupLookup[columnGroupId];\n        headerGroupRow.addValue(columnGroup ? columnGroup.headerName || columnGroup.groupId : '');\n      });\n    }\n  }\n  const mainHeaderRow = new CSVRow({\n    csvOptions,\n    sanitizeCellValue\n  });\n  filteredColumns.forEach(column => {\n    mainHeaderRow.addValue(column.headerName || column.field);\n  });\n  headerRows.push(mainHeaderRow);\n  const CSVHead = `${headerRows.map(row => row.getRowString()).join('\\r\\n')}\\r\\n`;\n  return `${CSVHead}${CSVBody}`.trim();\n}", "map": {"version": 3, "names": ["GRID_CHECKBOX_SELECTION_COL_DEF", "buildWarning", "sanitizeCellValue", "value", "csvOptions", "shouldAppendQuotes", "escapeFormulas", "escapedValue", "replace", "delimiter", "some", "includes", "serializeCellValue", "cellParams", "options", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_cellParams$value2", "columnType", "colDef", "type", "String", "_cellParams$value", "toISOString", "toString", "formattedValue", "objectFormattedValueWarning", "CSVRow", "constructor", "rowString", "isEmpty", "addValue", "undefined", "getRowString", "serializeRow", "id", "columns", "getCellParams", "row", "for<PERSON>ach", "column", "field", "process", "env", "NODE_ENV", "buildCSV", "rowIds", "apiRef", "CSVBody", "reduce", "acc", "current", "trim", "includeHeaders", "filteredColumns", "filter", "headerRows", "includeColumnGroupsHeaders", "columnGroupLookup", "unstable_getAllGroupDetails", "maxColumnGroupsDepth", "columnGroupPathsLookup", "columnGroupPath", "unstable_getColumnGroupPath", "Math", "max", "length", "i", "headerGroupRow", "push", "columnGroupId", "columnGroup", "headerName", "groupId", "mainHeaderRow", "CSVHead", "map", "join"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/x-data-grid/hooks/features/export/serializers/csvSerializer.js"], "sourcesContent": ["import { GRID_CHECKBOX_SELECTION_COL_DEF } from '../../../../colDef';\nimport { buildWarning } from '../../../../utils/warning';\nfunction sanitizeCellValue(value, csvOptions) {\n  if (typeof value === 'string') {\n    if (csvOptions.shouldAppendQuotes || csvOptions.escapeFormulas) {\n      const escapedValue = value.replace(/\"/g, '\"\"');\n      // Make sure value containing delimiter or line break won't be split into multiple cells\n      if ([csvOptions.delimiter, '\\n', '\\r', '\"'].some(delimiter => value.includes(delimiter))) {\n        return `\"${escapedValue}\"`;\n      }\n      if (csvOptions.escapeFormulas) {\n        // See https://owasp.org/www-community/attacks/CSV_Injection\n        if (['=', '+', '-', '@', '\\t', '\\r'].includes(escapedValue[0])) {\n          return `'${escapedValue}`;\n        }\n      }\n      return escapedValue;\n    }\n    return value;\n  }\n  return value;\n}\nexport const serializeCellValue = (cellParams, options) => {\n  const {\n    csvOptions,\n    ignoreValueFormatter\n  } = options;\n  let value;\n  if (ignoreValueFormatter) {\n    var _cellParams$value2;\n    const columnType = cellParams.colDef.type;\n    if (columnType === 'number') {\n      value = String(cellParams.value);\n    } else if (columnType === 'date' || columnType === 'dateTime') {\n      var _cellParams$value;\n      value = (_cellParams$value = cellParams.value) == null ? void 0 : _cellParams$value.toISOString();\n    } else if (typeof ((_cellParams$value2 = cellParams.value) == null ? void 0 : _cellParams$value2.toString) === 'function') {\n      value = cellParams.value.toString();\n    } else {\n      value = cellParams.value;\n    }\n  } else {\n    value = cellParams.formattedValue;\n  }\n  return sanitizeCellValue(value, csvOptions);\n};\nconst objectFormattedValueWarning = buildWarning(['MUI: When the value of a field is an object or a `renderCell` is provided, the CSV export might not display the value correctly.', 'You can provide a `valueFormatter` with a string representation to be used.']);\nclass CSVRow {\n  constructor(options) {\n    this.options = void 0;\n    this.rowString = '';\n    this.isEmpty = true;\n    this.options = options;\n  }\n  addValue(value) {\n    if (!this.isEmpty) {\n      this.rowString += this.options.csvOptions.delimiter;\n    }\n    if (value === null || value === undefined) {\n      this.rowString += '';\n    } else if (typeof this.options.sanitizeCellValue === 'function') {\n      this.rowString += this.options.sanitizeCellValue(value, this.options.csvOptions);\n    } else {\n      this.rowString += value;\n    }\n    this.isEmpty = false;\n  }\n  getRowString() {\n    return this.rowString;\n  }\n}\nconst serializeRow = ({\n  id,\n  columns,\n  getCellParams,\n  csvOptions,\n  ignoreValueFormatter\n}) => {\n  const row = new CSVRow({\n    csvOptions\n  });\n  columns.forEach(column => {\n    const cellParams = getCellParams(id, column.field);\n    if (process.env.NODE_ENV !== 'production') {\n      if (String(cellParams.formattedValue) === '[object Object]') {\n        objectFormattedValueWarning();\n      }\n    }\n    row.addValue(serializeCellValue(cellParams, {\n      ignoreValueFormatter,\n      csvOptions\n    }));\n  });\n  return row.getRowString();\n};\nexport function buildCSV(options) {\n  const {\n    columns,\n    rowIds,\n    csvOptions,\n    ignoreValueFormatter,\n    apiRef\n  } = options;\n  const CSVBody = rowIds.reduce((acc, id) => `${acc}${serializeRow({\n    id,\n    columns,\n    getCellParams: apiRef.current.getCellParams,\n    ignoreValueFormatter,\n    csvOptions\n  })}\\r\\n`, '').trim();\n  if (!csvOptions.includeHeaders) {\n    return CSVBody;\n  }\n  const filteredColumns = columns.filter(column => column.field !== GRID_CHECKBOX_SELECTION_COL_DEF.field);\n  const headerRows = [];\n  if (csvOptions.includeColumnGroupsHeaders) {\n    const columnGroupLookup = apiRef.current.unstable_getAllGroupDetails();\n    let maxColumnGroupsDepth = 0;\n    const columnGroupPathsLookup = filteredColumns.reduce((acc, column) => {\n      const columnGroupPath = apiRef.current.unstable_getColumnGroupPath(column.field);\n      acc[column.field] = columnGroupPath;\n      maxColumnGroupsDepth = Math.max(maxColumnGroupsDepth, columnGroupPath.length);\n      return acc;\n    }, {});\n    for (let i = 0; i < maxColumnGroupsDepth; i += 1) {\n      const headerGroupRow = new CSVRow({\n        csvOptions,\n        sanitizeCellValue\n      });\n      headerRows.push(headerGroupRow);\n      filteredColumns.forEach(column => {\n        const columnGroupId = (columnGroupPathsLookup[column.field] || [])[i];\n        const columnGroup = columnGroupLookup[columnGroupId];\n        headerGroupRow.addValue(columnGroup ? columnGroup.headerName || columnGroup.groupId : '');\n      });\n    }\n  }\n  const mainHeaderRow = new CSVRow({\n    csvOptions,\n    sanitizeCellValue\n  });\n  filteredColumns.forEach(column => {\n    mainHeaderRow.addValue(column.headerName || column.field);\n  });\n  headerRows.push(mainHeaderRow);\n  const CSVHead = `${headerRows.map(row => row.getRowString()).join('\\r\\n')}\\r\\n`;\n  return `${CSVHead}${CSVBody}`.trim();\n}"], "mappings": "AAAA,SAASA,+BAA+B,QAAQ,oBAAoB;AACpE,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,iBAAiBA,CAACC,KAAK,EAAEC,UAAU,EAAE;EAC5C,IAAI,OAAOD,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAIC,UAAU,CAACC,kBAAkB,IAAID,UAAU,CAACE,cAAc,EAAE;MAC9D,MAAMC,YAAY,GAAGJ,KAAK,CAACK,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC;MAC9C;MACA,IAAI,CAACJ,UAAU,CAACK,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,GAAG,CAAC,CAACC,IAAI,CAACD,SAAS,IAAIN,KAAK,CAACQ,QAAQ,CAACF,SAAS,CAAC,CAAC,EAAE;QACxF,OAAO,IAAIF,YAAY,GAAG;MAC5B;MACA,IAAIH,UAAU,CAACE,cAAc,EAAE;QAC7B;QACA,IAAI,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,IAAI,EAAE,IAAI,CAAC,CAACK,QAAQ,CAACJ,YAAY,CAAC,CAAC,CAAC,CAAC,EAAE;UAC9D,OAAO,IAAIA,YAAY,EAAE;QAC3B;MACF;MACA,OAAOA,YAAY;IACrB;IACA,OAAOJ,KAAK;EACd;EACA,OAAOA,KAAK;AACd;AACA,OAAO,MAAMS,kBAAkB,GAAGA,CAACC,UAAU,EAAEC,OAAO,KAAK;EACzD,MAAM;IACJV,UAAU;IACVW;EACF,CAAC,GAAGD,OAAO;EACX,IAAIX,KAAK;EACT,IAAIY,oBAAoB,EAAE;IACxB,IAAIC,kBAAkB;IACtB,MAAMC,UAAU,GAAGJ,UAAU,CAACK,MAAM,CAACC,IAAI;IACzC,IAAIF,UAAU,KAAK,QAAQ,EAAE;MAC3Bd,KAAK,GAAGiB,MAAM,CAACP,UAAU,CAACV,KAAK,CAAC;IAClC,CAAC,MAAM,IAAIc,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,UAAU,EAAE;MAC7D,IAAII,iBAAiB;MACrBlB,KAAK,GAAG,CAACkB,iBAAiB,GAAGR,UAAU,CAACV,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGkB,iBAAiB,CAACC,WAAW,CAAC,CAAC;IACnG,CAAC,MAAM,IAAI,QAAQ,CAACN,kBAAkB,GAAGH,UAAU,CAACV,KAAK,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGa,kBAAkB,CAACO,QAAQ,CAAC,KAAK,UAAU,EAAE;MACzHpB,KAAK,GAAGU,UAAU,CAACV,KAAK,CAACoB,QAAQ,CAAC,CAAC;IACrC,CAAC,MAAM;MACLpB,KAAK,GAAGU,UAAU,CAACV,KAAK;IAC1B;EACF,CAAC,MAAM;IACLA,KAAK,GAAGU,UAAU,CAACW,cAAc;EACnC;EACA,OAAOtB,iBAAiB,CAACC,KAAK,EAAEC,UAAU,CAAC;AAC7C,CAAC;AACD,MAAMqB,2BAA2B,GAAGxB,YAAY,CAAC,CAAC,kIAAkI,EAAE,6EAA6E,CAAC,CAAC;AACrQ,MAAMyB,MAAM,CAAC;EACXC,WAAWA,CAACb,OAAO,EAAE;IACnB,IAAI,CAACA,OAAO,GAAG,KAAK,CAAC;IACrB,IAAI,CAACc,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACf,OAAO,GAAGA,OAAO;EACxB;EACAgB,QAAQA,CAAC3B,KAAK,EAAE;IACd,IAAI,CAAC,IAAI,CAAC0B,OAAO,EAAE;MACjB,IAAI,CAACD,SAAS,IAAI,IAAI,CAACd,OAAO,CAACV,UAAU,CAACK,SAAS;IACrD;IACA,IAAIN,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK4B,SAAS,EAAE;MACzC,IAAI,CAACH,SAAS,IAAI,EAAE;IACtB,CAAC,MAAM,IAAI,OAAO,IAAI,CAACd,OAAO,CAACZ,iBAAiB,KAAK,UAAU,EAAE;MAC/D,IAAI,CAAC0B,SAAS,IAAI,IAAI,CAACd,OAAO,CAACZ,iBAAiB,CAACC,KAAK,EAAE,IAAI,CAACW,OAAO,CAACV,UAAU,CAAC;IAClF,CAAC,MAAM;MACL,IAAI,CAACwB,SAAS,IAAIzB,KAAK;IACzB;IACA,IAAI,CAAC0B,OAAO,GAAG,KAAK;EACtB;EACAG,YAAYA,CAAA,EAAG;IACb,OAAO,IAAI,CAACJ,SAAS;EACvB;AACF;AACA,MAAMK,YAAY,GAAGA,CAAC;EACpBC,EAAE;EACFC,OAAO;EACPC,aAAa;EACbhC,UAAU;EACVW;AACF,CAAC,KAAK;EACJ,MAAMsB,GAAG,GAAG,IAAIX,MAAM,CAAC;IACrBtB;EACF,CAAC,CAAC;EACF+B,OAAO,CAACG,OAAO,CAACC,MAAM,IAAI;IACxB,MAAM1B,UAAU,GAAGuB,aAAa,CAACF,EAAE,EAAEK,MAAM,CAACC,KAAK,CAAC;IAClD,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIvB,MAAM,CAACP,UAAU,CAACW,cAAc,CAAC,KAAK,iBAAiB,EAAE;QAC3DC,2BAA2B,CAAC,CAAC;MAC/B;IACF;IACAY,GAAG,CAACP,QAAQ,CAAClB,kBAAkB,CAACC,UAAU,EAAE;MAC1CE,oBAAoB;MACpBX;IACF,CAAC,CAAC,CAAC;EACL,CAAC,CAAC;EACF,OAAOiC,GAAG,CAACL,YAAY,CAAC,CAAC;AAC3B,CAAC;AACD,OAAO,SAASY,QAAQA,CAAC9B,OAAO,EAAE;EAChC,MAAM;IACJqB,OAAO;IACPU,MAAM;IACNzC,UAAU;IACVW,oBAAoB;IACpB+B;EACF,CAAC,GAAGhC,OAAO;EACX,MAAMiC,OAAO,GAAGF,MAAM,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEf,EAAE,KAAK,GAAGe,GAAG,GAAGhB,YAAY,CAAC;IAC/DC,EAAE;IACFC,OAAO;IACPC,aAAa,EAAEU,MAAM,CAACI,OAAO,CAACd,aAAa;IAC3CrB,oBAAoB;IACpBX;EACF,CAAC,CAAC,MAAM,EAAE,EAAE,CAAC,CAAC+C,IAAI,CAAC,CAAC;EACpB,IAAI,CAAC/C,UAAU,CAACgD,cAAc,EAAE;IAC9B,OAAOL,OAAO;EAChB;EACA,MAAMM,eAAe,GAAGlB,OAAO,CAACmB,MAAM,CAACf,MAAM,IAAIA,MAAM,CAACC,KAAK,KAAKxC,+BAA+B,CAACwC,KAAK,CAAC;EACxG,MAAMe,UAAU,GAAG,EAAE;EACrB,IAAInD,UAAU,CAACoD,0BAA0B,EAAE;IACzC,MAAMC,iBAAiB,GAAGX,MAAM,CAACI,OAAO,CAACQ,2BAA2B,CAAC,CAAC;IACtE,IAAIC,oBAAoB,GAAG,CAAC;IAC5B,MAAMC,sBAAsB,GAAGP,eAAe,CAACL,MAAM,CAAC,CAACC,GAAG,EAAEV,MAAM,KAAK;MACrE,MAAMsB,eAAe,GAAGf,MAAM,CAACI,OAAO,CAACY,2BAA2B,CAACvB,MAAM,CAACC,KAAK,CAAC;MAChFS,GAAG,CAACV,MAAM,CAACC,KAAK,CAAC,GAAGqB,eAAe;MACnCF,oBAAoB,GAAGI,IAAI,CAACC,GAAG,CAACL,oBAAoB,EAAEE,eAAe,CAACI,MAAM,CAAC;MAC7E,OAAOhB,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,KAAK,IAAIiB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,oBAAoB,EAAEO,CAAC,IAAI,CAAC,EAAE;MAChD,MAAMC,cAAc,GAAG,IAAIzC,MAAM,CAAC;QAChCtB,UAAU;QACVF;MACF,CAAC,CAAC;MACFqD,UAAU,CAACa,IAAI,CAACD,cAAc,CAAC;MAC/Bd,eAAe,CAACf,OAAO,CAACC,MAAM,IAAI;QAChC,MAAM8B,aAAa,GAAG,CAACT,sBAAsB,CAACrB,MAAM,CAACC,KAAK,CAAC,IAAI,EAAE,EAAE0B,CAAC,CAAC;QACrE,MAAMI,WAAW,GAAGb,iBAAiB,CAACY,aAAa,CAAC;QACpDF,cAAc,CAACrC,QAAQ,CAACwC,WAAW,GAAGA,WAAW,CAACC,UAAU,IAAID,WAAW,CAACE,OAAO,GAAG,EAAE,CAAC;MAC3F,CAAC,CAAC;IACJ;EACF;EACA,MAAMC,aAAa,GAAG,IAAI/C,MAAM,CAAC;IAC/BtB,UAAU;IACVF;EACF,CAAC,CAAC;EACFmD,eAAe,CAACf,OAAO,CAACC,MAAM,IAAI;IAChCkC,aAAa,CAAC3C,QAAQ,CAACS,MAAM,CAACgC,UAAU,IAAIhC,MAAM,CAACC,KAAK,CAAC;EAC3D,CAAC,CAAC;EACFe,UAAU,CAACa,IAAI,CAACK,aAAa,CAAC;EAC9B,MAAMC,OAAO,GAAG,GAAGnB,UAAU,CAACoB,GAAG,CAACtC,GAAG,IAAIA,GAAG,CAACL,YAAY,CAAC,CAAC,CAAC,CAAC4C,IAAI,CAAC,MAAM,CAAC,MAAM;EAC/E,OAAO,GAAGF,OAAO,GAAG3B,OAAO,EAAE,CAACI,IAAI,CAAC,CAAC;AACtC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}