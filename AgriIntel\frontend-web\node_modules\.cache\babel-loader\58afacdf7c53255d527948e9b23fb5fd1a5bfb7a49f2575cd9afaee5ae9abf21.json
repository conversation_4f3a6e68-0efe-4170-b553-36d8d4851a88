{"ast": null, "code": "export { CancelledError } from './retryer';\nexport { QueryCache } from './queryCache';\nexport { QueryClient } from './queryClient';\nexport { QueryObserver } from './queryObserver';\nexport { QueriesObserver } from './queriesObserver';\nexport { InfiniteQueryObserver } from './infiniteQueryObserver';\nexport { MutationCache } from './mutationCache';\nexport { MutationObserver } from './mutationObserver';\nexport { setLogger } from './logger';\nexport { notifyManager } from './notifyManager';\nexport { focusManager } from './focusManager';\nexport { onlineManager } from './onlineManager';\nexport { hashQueryKey, isError } from './utils';\nexport { isCancelledError } from './retryer';\nexport { dehydrate, hydrate } from './hydration'; // Types\n\nexport * from './types';", "map": {"version": 3, "names": ["CancelledError", "Query<PERSON>ache", "QueryClient", "QueryObserver", "QueriesObserver", "InfiniteQueryObserver", "MutationCache", "MutationObserver", "<PERSON><PERSON><PERSON><PERSON>", "notify<PERSON><PERSON>ger", "focusManager", "onlineManager", "hashQuery<PERSON>ey", "isError", "isCancelledError", "dehydrate", "hydrate"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-query/es/core/index.js"], "sourcesContent": ["export { CancelledError } from './retryer';\nexport { QueryCache } from './queryCache';\nexport { QueryClient } from './queryClient';\nexport { QueryObserver } from './queryObserver';\nexport { QueriesObserver } from './queriesObserver';\nexport { InfiniteQueryObserver } from './infiniteQueryObserver';\nexport { MutationCache } from './mutationCache';\nexport { MutationObserver } from './mutationObserver';\nexport { setLogger } from './logger';\nexport { notifyManager } from './notifyManager';\nexport { focusManager } from './focusManager';\nexport { onlineManager } from './onlineManager';\nexport { hashQueryKey, isError } from './utils';\nexport { isCancelledError } from './retryer';\nexport { dehydrate, hydrate } from './hydration'; // Types\n\nexport * from './types';"], "mappings": "AAAA,SAASA,cAAc,QAAQ,WAAW;AAC1C,SAASC,UAAU,QAAQ,cAAc;AACzC,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,qBAAqB,QAAQ,yBAAyB;AAC/D,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,gBAAgB,QAAQ,oBAAoB;AACrD,SAASC,SAAS,QAAQ,UAAU;AACpC,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,EAAEC,OAAO,QAAQ,SAAS;AAC/C,SAASC,gBAAgB,QAAQ,WAAW;AAC5C,SAASC,SAAS,EAAEC,OAAO,QAAQ,aAAa,CAAC,CAAC;;AAElD,cAAc,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}