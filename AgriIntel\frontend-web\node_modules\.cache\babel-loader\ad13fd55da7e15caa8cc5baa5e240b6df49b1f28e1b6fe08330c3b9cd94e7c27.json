{"ast": null, "code": "\"use strict\";\n\nconst punycode = require(\"punycode/\");\nconst regexes = require(\"./lib/regexes.js\");\nconst mappingTable = require(\"./lib/mappingTable.json\");\nconst {\n  STATUS_MAPPING\n} = require(\"./lib/statusMapping.js\");\nfunction containsNonASCII(str) {\n  return /[^\\x00-\\x7F]/u.test(str);\n}\nfunction findStatus(val) {\n  let start = 0;\n  let end = mappingTable.length - 1;\n  while (start <= end) {\n    const mid = Math.floor((start + end) / 2);\n    const target = mappingTable[mid];\n    const min = Array.isArray(target[0]) ? target[0][0] : target[0];\n    const max = Array.isArray(target[0]) ? target[0][1] : target[0];\n    if (min <= val && max >= val) {\n      return target.slice(1);\n    } else if (min > val) {\n      end = mid - 1;\n    } else {\n      start = mid + 1;\n    }\n  }\n  return null;\n}\nfunction mapChars(domainName, {\n  transitionalProcessing\n}) {\n  let processed = \"\";\n  for (const ch of domainName) {\n    const [status, mapping] = findStatus(ch.codePointAt(0));\n    switch (status) {\n      case STATUS_MAPPING.disallowed:\n        processed += ch;\n        break;\n      case STATUS_MAPPING.ignored:\n        break;\n      case STATUS_MAPPING.mapped:\n        if (transitionalProcessing && ch === \"ẞ\") {\n          processed += \"ss\";\n        } else {\n          processed += mapping;\n        }\n        break;\n      case STATUS_MAPPING.deviation:\n        if (transitionalProcessing) {\n          processed += mapping;\n        } else {\n          processed += ch;\n        }\n        break;\n      case STATUS_MAPPING.valid:\n        processed += ch;\n        break;\n    }\n  }\n  return processed;\n}\nfunction validateLabel(label, {\n  checkHyphens,\n  checkBidi,\n  checkJoiners,\n  transitionalProcessing,\n  useSTD3ASCIIRules,\n  isBidi\n}) {\n  // \"must be satisfied for a non-empty label\"\n  if (label.length === 0) {\n    return true;\n  }\n\n  // \"1. The label must be in Unicode Normalization Form NFC.\"\n  if (label.normalize(\"NFC\") !== label) {\n    return false;\n  }\n  const codePoints = Array.from(label);\n\n  // \"2. If CheckHyphens, the label must not contain a U+002D HYPHEN-MINUS character in both the\n  // third and fourth positions.\"\n  //\n  // \"3. If CheckHyphens, the label must neither begin nor end with a U+002D HYPHEN-MINUS character.\"\n  if (checkHyphens) {\n    if (codePoints[2] === \"-\" && codePoints[3] === \"-\" || label.startsWith(\"-\") || label.endsWith(\"-\")) {\n      return false;\n    }\n  }\n\n  // \"4. If not CheckHyphens, the label must not begin with “xn--”.\"\n  if (!checkHyphens) {\n    if (label.startsWith(\"xn--\")) {\n      return false;\n    }\n  }\n\n  // \"5. The label must not contain a U+002E ( . ) FULL STOP.\"\n  if (label.includes(\".\")) {\n    return false;\n  }\n\n  // \"6. The label must not begin with a combining mark, that is: General_Category=Mark.\"\n  if (regexes.combiningMarks.test(codePoints[0])) {\n    return false;\n  }\n\n  // \"7. Each code point in the label must only have certain Status values according to Section 5\"\n  for (const ch of codePoints) {\n    const codePoint = ch.codePointAt(0);\n    const [status] = findStatus(codePoint);\n    if (transitionalProcessing) {\n      // \"For Transitional Processing (deprecated), each value must be valid.\"\n      if (status !== STATUS_MAPPING.valid) {\n        return false;\n      }\n    } else if (status !== STATUS_MAPPING.valid && status !== STATUS_MAPPING.deviation) {\n      // \"For Nontransitional Processing, each value must be either valid or deviation.\"\n      return false;\n    }\n    // \"In addition, if UseSTD3ASCIIRules=true and the code point is an ASCII code point (U+0000..U+007F), then it must\n    // be a lowercase letter (a-z), a digit (0-9), or a hyphen-minus (U+002D). (Note: This excludes uppercase ASCII\n    // A-Z which are mapped in UTS #46 and disallowed in IDNA2008.)\"\n    if (useSTD3ASCIIRules && codePoint <= 0x7F) {\n      if (!/^(?:[a-z]|[0-9]|-)$/u.test(ch)) {\n        return false;\n      }\n    }\n  }\n\n  // \"8. If CheckJoiners, the label must satisify the ContextJ rules\"\n  // https://tools.ietf.org/html/rfc5892#appendix-A\n  if (checkJoiners) {\n    let last = 0;\n    for (const [i, ch] of codePoints.entries()) {\n      if (ch === \"\\u200C\" || ch === \"\\u200D\") {\n        if (i > 0) {\n          if (regexes.combiningClassVirama.test(codePoints[i - 1])) {\n            continue;\n          }\n          if (ch === \"\\u200C\") {\n            // TODO: make this more efficient\n            const next = codePoints.indexOf(\"\\u200C\", i + 1);\n            const test = next < 0 ? codePoints.slice(last) : codePoints.slice(last, next);\n            if (regexes.validZWNJ.test(test.join(\"\"))) {\n              last = i + 1;\n              continue;\n            }\n          }\n        }\n        return false;\n      }\n    }\n  }\n\n  // \"9. If CheckBidi, and if the domain name is a Bidi domain name, then the label must satisfy...\"\n  // https://tools.ietf.org/html/rfc5893#section-2\n  if (checkBidi && isBidi) {\n    let rtl;\n\n    // 1\n    if (regexes.bidiS1LTR.test(codePoints[0])) {\n      rtl = false;\n    } else if (regexes.bidiS1RTL.test(codePoints[0])) {\n      rtl = true;\n    } else {\n      return false;\n    }\n    if (rtl) {\n      // 2-4\n      if (!regexes.bidiS2.test(label) || !regexes.bidiS3.test(label) || regexes.bidiS4EN.test(label) && regexes.bidiS4AN.test(label)) {\n        return false;\n      }\n    } else if (!regexes.bidiS5.test(label) || !regexes.bidiS6.test(label)) {\n      // 5-6\n      return false;\n    }\n  }\n  return true;\n}\nfunction isBidiDomain(labels) {\n  const domain = labels.map(label => {\n    if (label.startsWith(\"xn--\")) {\n      try {\n        return punycode.decode(label.substring(4));\n      } catch {\n        return \"\";\n      }\n    }\n    return label;\n  }).join(\".\");\n  return regexes.bidiDomain.test(domain);\n}\nfunction processing(domainName, options) {\n  // 1. Map.\n  let string = mapChars(domainName, options);\n\n  // 2. Normalize.\n  string = string.normalize(\"NFC\");\n\n  // 3. Break.\n  const labels = string.split(\".\");\n  const isBidi = isBidiDomain(labels);\n\n  // 4. Convert/Validate.\n  let error = false;\n  for (const [i, origLabel] of labels.entries()) {\n    let label = origLabel;\n    let transitionalProcessingForThisLabel = options.transitionalProcessing;\n    if (label.startsWith(\"xn--\")) {\n      if (containsNonASCII(label)) {\n        error = true;\n        continue;\n      }\n      try {\n        label = punycode.decode(label.substring(4));\n      } catch {\n        if (!options.ignoreInvalidPunycode) {\n          error = true;\n          continue;\n        }\n      }\n      labels[i] = label;\n      if (label === \"\" || !containsNonASCII(label)) {\n        error = true;\n      }\n      transitionalProcessingForThisLabel = false;\n    }\n\n    // No need to validate if we already know there is an error.\n    if (error) {\n      continue;\n    }\n    const validation = validateLabel(label, {\n      ...options,\n      transitionalProcessing: transitionalProcessingForThisLabel,\n      isBidi\n    });\n    if (!validation) {\n      error = true;\n    }\n  }\n  return {\n    string: labels.join(\".\"),\n    error\n  };\n}\nfunction toASCII(domainName, {\n  checkHyphens = false,\n  checkBidi = false,\n  checkJoiners = false,\n  useSTD3ASCIIRules = false,\n  verifyDNSLength = false,\n  transitionalProcessing = false,\n  ignoreInvalidPunycode = false\n} = {}) {\n  const result = processing(domainName, {\n    checkHyphens,\n    checkBidi,\n    checkJoiners,\n    useSTD3ASCIIRules,\n    transitionalProcessing,\n    ignoreInvalidPunycode\n  });\n  let labels = result.string.split(\".\");\n  labels = labels.map(l => {\n    if (containsNonASCII(l)) {\n      try {\n        return `xn--${punycode.encode(l)}`;\n      } catch {\n        result.error = true;\n      }\n    }\n    return l;\n  });\n  if (verifyDNSLength) {\n    const total = labels.join(\".\").length;\n    if (total > 253 || total === 0) {\n      result.error = true;\n    }\n    for (let i = 0; i < labels.length; ++i) {\n      if (labels[i].length > 63 || labels[i].length === 0) {\n        result.error = true;\n        break;\n      }\n    }\n  }\n  if (result.error) {\n    return null;\n  }\n  return labels.join(\".\");\n}\nfunction toUnicode(domainName, {\n  checkHyphens = false,\n  checkBidi = false,\n  checkJoiners = false,\n  useSTD3ASCIIRules = false,\n  transitionalProcessing = false,\n  ignoreInvalidPunycode = false\n} = {}) {\n  const result = processing(domainName, {\n    checkHyphens,\n    checkBidi,\n    checkJoiners,\n    useSTD3ASCIIRules,\n    transitionalProcessing,\n    ignoreInvalidPunycode\n  });\n  return {\n    domain: result.string,\n    error: result.error\n  };\n}\nmodule.exports = {\n  toASCII,\n  toUnicode\n};", "map": {"version": 3, "names": ["punycode", "require", "regexes", "mappingTable", "STATUS_MAPPING", "containsNonASCII", "str", "test", "findStatus", "val", "start", "end", "length", "mid", "Math", "floor", "target", "min", "Array", "isArray", "max", "slice", "mapChars", "domainName", "transitionalProcessing", "processed", "ch", "status", "mapping", "codePointAt", "disallowed", "ignored", "mapped", "deviation", "valid", "validate<PERSON><PERSON><PERSON>", "label", "checkHyphens", "checkBidi", "checkJoiners", "useSTD3ASCIIRules", "isBidi", "normalize", "codePoints", "from", "startsWith", "endsWith", "includes", "combiningMarks", "codePoint", "last", "i", "entries", "combiningClassVirama", "next", "indexOf", "validZWNJ", "join", "rtl", "bidiS1LTR", "bidiS1RTL", "bidiS2", "bidiS3", "bidiS4EN", "bidiS4AN", "bidiS5", "bidiS6", "isBidiDomain", "labels", "domain", "map", "decode", "substring", "bidiDomain", "processing", "options", "string", "split", "error", "origLabel", "transitionalProcessingForThisLabel", "ignoreInvalidPunycode", "validation", "toASCII", "verifyD<PERSON><PERSON><PERSON><PERSON>", "result", "l", "encode", "total", "toUnicode", "module", "exports"], "sources": ["C:/Users/<USER>/node_modules/mongodb-connection-string-url/node_modules/tr46/index.js"], "sourcesContent": ["\"use strict\";\n\nconst punycode = require(\"punycode/\");\nconst regexes = require(\"./lib/regexes.js\");\nconst mappingTable = require(\"./lib/mappingTable.json\");\nconst { STATUS_MAPPING } = require(\"./lib/statusMapping.js\");\n\nfunction containsNonASCII(str) {\n  return /[^\\x00-\\x7F]/u.test(str);\n}\n\nfunction findStatus(val) {\n  let start = 0;\n  let end = mappingTable.length - 1;\n\n  while (start <= end) {\n    const mid = Math.floor((start + end) / 2);\n\n    const target = mappingTable[mid];\n    const min = Array.isArray(target[0]) ? target[0][0] : target[0];\n    const max = Array.isArray(target[0]) ? target[0][1] : target[0];\n\n    if (min <= val && max >= val) {\n      return target.slice(1);\n    } else if (min > val) {\n      end = mid - 1;\n    } else {\n      start = mid + 1;\n    }\n  }\n\n  return null;\n}\n\nfunction mapChars(domainName, { transitionalProcessing }) {\n  let processed = \"\";\n\n  for (const ch of domainName) {\n    const [status, mapping] = findStatus(ch.codePointAt(0));\n\n    switch (status) {\n      case STATUS_MAPPING.disallowed:\n        processed += ch;\n        break;\n      case STATUS_MAPPING.ignored:\n        break;\n      case STATUS_MAPPING.mapped:\n        if (transitionalProcessing && ch === \"ẞ\") {\n          processed += \"ss\";\n        } else {\n          processed += mapping;\n        }\n        break;\n      case STATUS_MAPPING.deviation:\n        if (transitionalProcessing) {\n          processed += mapping;\n        } else {\n          processed += ch;\n        }\n        break;\n      case STATUS_MAPPING.valid:\n        processed += ch;\n        break;\n    }\n  }\n\n  return processed;\n}\n\nfunction validateLabel(label, {\n  checkHyphens,\n  checkBidi,\n  checkJoiners,\n  transitionalProcessing,\n  useSTD3ASCIIRules,\n  isBidi\n}) {\n  // \"must be satisfied for a non-empty label\"\n  if (label.length === 0) {\n    return true;\n  }\n\n  // \"1. The label must be in Unicode Normalization Form NFC.\"\n  if (label.normalize(\"NFC\") !== label) {\n    return false;\n  }\n\n  const codePoints = Array.from(label);\n\n  // \"2. If CheckHyphens, the label must not contain a U+002D HYPHEN-MINUS character in both the\n  // third and fourth positions.\"\n  //\n  // \"3. If CheckHyphens, the label must neither begin nor end with a U+002D HYPHEN-MINUS character.\"\n  if (checkHyphens) {\n    if ((codePoints[2] === \"-\" && codePoints[3] === \"-\") ||\n        (label.startsWith(\"-\") || label.endsWith(\"-\"))) {\n      return false;\n    }\n  }\n\n  // \"4. If not CheckHyphens, the label must not begin with “xn--”.\"\n  if (!checkHyphens) {\n    if (label.startsWith(\"xn--\")) {\n      return false;\n    }\n  }\n\n  // \"5. The label must not contain a U+002E ( . ) FULL STOP.\"\n  if (label.includes(\".\")) {\n    return false;\n  }\n\n  // \"6. The label must not begin with a combining mark, that is: General_Category=Mark.\"\n  if (regexes.combiningMarks.test(codePoints[0])) {\n    return false;\n  }\n\n  // \"7. Each code point in the label must only have certain Status values according to Section 5\"\n  for (const ch of codePoints) {\n    const codePoint = ch.codePointAt(0);\n    const [status] = findStatus(codePoint);\n    if (transitionalProcessing) {\n      // \"For Transitional Processing (deprecated), each value must be valid.\"\n      if (status !== STATUS_MAPPING.valid) {\n        return false;\n      }\n    } else if (status !== STATUS_MAPPING.valid && status !== STATUS_MAPPING.deviation) {\n      // \"For Nontransitional Processing, each value must be either valid or deviation.\"\n      return false;\n    }\n    // \"In addition, if UseSTD3ASCIIRules=true and the code point is an ASCII code point (U+0000..U+007F), then it must\n    // be a lowercase letter (a-z), a digit (0-9), or a hyphen-minus (U+002D). (Note: This excludes uppercase ASCII\n    // A-Z which are mapped in UTS #46 and disallowed in IDNA2008.)\"\n    if (useSTD3ASCIIRules && codePoint <= 0x7F) {\n      if (!/^(?:[a-z]|[0-9]|-)$/u.test(ch)) {\n        return false;\n      }\n    }\n  }\n\n  // \"8. If CheckJoiners, the label must satisify the ContextJ rules\"\n  // https://tools.ietf.org/html/rfc5892#appendix-A\n  if (checkJoiners) {\n    let last = 0;\n    for (const [i, ch] of codePoints.entries()) {\n      if (ch === \"\\u200C\" || ch === \"\\u200D\") {\n        if (i > 0) {\n          if (regexes.combiningClassVirama.test(codePoints[i - 1])) {\n            continue;\n          }\n          if (ch === \"\\u200C\") {\n            // TODO: make this more efficient\n            const next = codePoints.indexOf(\"\\u200C\", i + 1);\n            const test = next < 0 ? codePoints.slice(last) : codePoints.slice(last, next);\n            if (regexes.validZWNJ.test(test.join(\"\"))) {\n              last = i + 1;\n              continue;\n            }\n          }\n        }\n        return false;\n      }\n    }\n  }\n\n  // \"9. If CheckBidi, and if the domain name is a Bidi domain name, then the label must satisfy...\"\n  // https://tools.ietf.org/html/rfc5893#section-2\n  if (checkBidi && isBidi) {\n    let rtl;\n\n    // 1\n    if (regexes.bidiS1LTR.test(codePoints[0])) {\n      rtl = false;\n    } else if (regexes.bidiS1RTL.test(codePoints[0])) {\n      rtl = true;\n    } else {\n      return false;\n    }\n\n    if (rtl) {\n      // 2-4\n      if (!regexes.bidiS2.test(label) ||\n          !regexes.bidiS3.test(label) ||\n          (regexes.bidiS4EN.test(label) && regexes.bidiS4AN.test(label))) {\n        return false;\n      }\n    } else if (!regexes.bidiS5.test(label) ||\n               !regexes.bidiS6.test(label)) { // 5-6\n      return false;\n    }\n  }\n\n  return true;\n}\n\nfunction isBidiDomain(labels) {\n  const domain = labels.map(label => {\n    if (label.startsWith(\"xn--\")) {\n      try {\n        return punycode.decode(label.substring(4));\n      } catch {\n        return \"\";\n      }\n    }\n    return label;\n  }).join(\".\");\n  return regexes.bidiDomain.test(domain);\n}\n\nfunction processing(domainName, options) {\n  // 1. Map.\n  let string = mapChars(domainName, options);\n\n  // 2. Normalize.\n  string = string.normalize(\"NFC\");\n\n  // 3. Break.\n  const labels = string.split(\".\");\n  const isBidi = isBidiDomain(labels);\n\n  // 4. Convert/Validate.\n  let error = false;\n  for (const [i, origLabel] of labels.entries()) {\n    let label = origLabel;\n    let transitionalProcessingForThisLabel = options.transitionalProcessing;\n    if (label.startsWith(\"xn--\")) {\n      if (containsNonASCII(label)) {\n        error = true;\n        continue;\n      }\n\n      try {\n        label = punycode.decode(label.substring(4));\n      } catch {\n        if (!options.ignoreInvalidPunycode) {\n          error = true;\n          continue;\n        }\n      }\n      labels[i] = label;\n\n      if (label === \"\" || !containsNonASCII(label)) {\n        error = true;\n      }\n\n      transitionalProcessingForThisLabel = false;\n    }\n\n    // No need to validate if we already know there is an error.\n    if (error) {\n      continue;\n    }\n    const validation = validateLabel(label, {\n      ...options,\n      transitionalProcessing: transitionalProcessingForThisLabel,\n      isBidi\n    });\n    if (!validation) {\n      error = true;\n    }\n  }\n\n  return {\n    string: labels.join(\".\"),\n    error\n  };\n}\n\nfunction toASCII(domainName, {\n  checkHyphens = false,\n  checkBidi = false,\n  checkJoiners = false,\n  useSTD3ASCIIRules = false,\n  verifyDNSLength = false,\n  transitionalProcessing = false,\n  ignoreInvalidPunycode = false\n} = {}) {\n  const result = processing(domainName, {\n    checkHyphens,\n    checkBidi,\n    checkJoiners,\n    useSTD3ASCIIRules,\n    transitionalProcessing,\n    ignoreInvalidPunycode\n  });\n  let labels = result.string.split(\".\");\n  labels = labels.map(l => {\n    if (containsNonASCII(l)) {\n      try {\n        return `xn--${punycode.encode(l)}`;\n      } catch {\n        result.error = true;\n      }\n    }\n    return l;\n  });\n\n  if (verifyDNSLength) {\n    const total = labels.join(\".\").length;\n    if (total > 253 || total === 0) {\n      result.error = true;\n    }\n\n    for (let i = 0; i < labels.length; ++i) {\n      if (labels[i].length > 63 || labels[i].length === 0) {\n        result.error = true;\n        break;\n      }\n    }\n  }\n\n  if (result.error) {\n    return null;\n  }\n  return labels.join(\".\");\n}\n\nfunction toUnicode(domainName, {\n  checkHyphens = false,\n  checkBidi = false,\n  checkJoiners = false,\n  useSTD3ASCIIRules = false,\n  transitionalProcessing = false,\n  ignoreInvalidPunycode = false\n} = {}) {\n  const result = processing(domainName, {\n    checkHyphens,\n    checkBidi,\n    checkJoiners,\n    useSTD3ASCIIRules,\n    transitionalProcessing,\n    ignoreInvalidPunycode\n  });\n\n  return {\n    domain: result.string,\n    error: result.error\n  };\n}\n\nmodule.exports = {\n  toASCII,\n  toUnicode\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,QAAQ,GAAGC,OAAO,CAAC,WAAW,CAAC;AACrC,MAAMC,OAAO,GAAGD,OAAO,CAAC,kBAAkB,CAAC;AAC3C,MAAME,YAAY,GAAGF,OAAO,CAAC,yBAAyB,CAAC;AACvD,MAAM;EAAEG;AAAe,CAAC,GAAGH,OAAO,CAAC,wBAAwB,CAAC;AAE5D,SAASI,gBAAgBA,CAACC,GAAG,EAAE;EAC7B,OAAO,eAAe,CAACC,IAAI,CAACD,GAAG,CAAC;AAClC;AAEA,SAASE,UAAUA,CAACC,GAAG,EAAE;EACvB,IAAIC,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAGR,YAAY,CAACS,MAAM,GAAG,CAAC;EAEjC,OAAOF,KAAK,IAAIC,GAAG,EAAE;IACnB,MAAME,GAAG,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,KAAK,GAAGC,GAAG,IAAI,CAAC,CAAC;IAEzC,MAAMK,MAAM,GAAGb,YAAY,CAACU,GAAG,CAAC;IAChC,MAAMI,GAAG,GAAGC,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;IAC/D,MAAMI,GAAG,GAAGF,KAAK,CAACC,OAAO,CAACH,MAAM,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGA,MAAM,CAAC,CAAC,CAAC;IAE/D,IAAIC,GAAG,IAAIR,GAAG,IAAIW,GAAG,IAAIX,GAAG,EAAE;MAC5B,OAAOO,MAAM,CAACK,KAAK,CAAC,CAAC,CAAC;IACxB,CAAC,MAAM,IAAIJ,GAAG,GAAGR,GAAG,EAAE;MACpBE,GAAG,GAAGE,GAAG,GAAG,CAAC;IACf,CAAC,MAAM;MACLH,KAAK,GAAGG,GAAG,GAAG,CAAC;IACjB;EACF;EAEA,OAAO,IAAI;AACb;AAEA,SAASS,QAAQA,CAACC,UAAU,EAAE;EAAEC;AAAuB,CAAC,EAAE;EACxD,IAAIC,SAAS,GAAG,EAAE;EAElB,KAAK,MAAMC,EAAE,IAAIH,UAAU,EAAE;IAC3B,MAAM,CAACI,MAAM,EAAEC,OAAO,CAAC,GAAGpB,UAAU,CAACkB,EAAE,CAACG,WAAW,CAAC,CAAC,CAAC,CAAC;IAEvD,QAAQF,MAAM;MACZ,KAAKvB,cAAc,CAAC0B,UAAU;QAC5BL,SAAS,IAAIC,EAAE;QACf;MACF,KAAKtB,cAAc,CAAC2B,OAAO;QACzB;MACF,KAAK3B,cAAc,CAAC4B,MAAM;QACxB,IAAIR,sBAAsB,IAAIE,EAAE,KAAK,GAAG,EAAE;UACxCD,SAAS,IAAI,IAAI;QACnB,CAAC,MAAM;UACLA,SAAS,IAAIG,OAAO;QACtB;QACA;MACF,KAAKxB,cAAc,CAAC6B,SAAS;QAC3B,IAAIT,sBAAsB,EAAE;UAC1BC,SAAS,IAAIG,OAAO;QACtB,CAAC,MAAM;UACLH,SAAS,IAAIC,EAAE;QACjB;QACA;MACF,KAAKtB,cAAc,CAAC8B,KAAK;QACvBT,SAAS,IAAIC,EAAE;QACf;IACJ;EACF;EAEA,OAAOD,SAAS;AAClB;AAEA,SAASU,aAAaA,CAACC,KAAK,EAAE;EAC5BC,YAAY;EACZC,SAAS;EACTC,YAAY;EACZf,sBAAsB;EACtBgB,iBAAiB;EACjBC;AACF,CAAC,EAAE;EACD;EACA,IAAIL,KAAK,CAACxB,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,IAAI;EACb;;EAEA;EACA,IAAIwB,KAAK,CAACM,SAAS,CAAC,KAAK,CAAC,KAAKN,KAAK,EAAE;IACpC,OAAO,KAAK;EACd;EAEA,MAAMO,UAAU,GAAGzB,KAAK,CAAC0B,IAAI,CAACR,KAAK,CAAC;;EAEpC;EACA;EACA;EACA;EACA,IAAIC,YAAY,EAAE;IAChB,IAAKM,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,UAAU,CAAC,CAAC,CAAC,KAAK,GAAG,IAC9CP,KAAK,CAACS,UAAU,CAAC,GAAG,CAAC,IAAIT,KAAK,CAACU,QAAQ,CAAC,GAAG,CAAE,EAAE;MAClD,OAAO,KAAK;IACd;EACF;;EAEA;EACA,IAAI,CAACT,YAAY,EAAE;IACjB,IAAID,KAAK,CAACS,UAAU,CAAC,MAAM,CAAC,EAAE;MAC5B,OAAO,KAAK;IACd;EACF;;EAEA;EACA,IAAIT,KAAK,CAACW,QAAQ,CAAC,GAAG,CAAC,EAAE;IACvB,OAAO,KAAK;EACd;;EAEA;EACA,IAAI7C,OAAO,CAAC8C,cAAc,CAACzC,IAAI,CAACoC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;IAC9C,OAAO,KAAK;EACd;;EAEA;EACA,KAAK,MAAMjB,EAAE,IAAIiB,UAAU,EAAE;IAC3B,MAAMM,SAAS,GAAGvB,EAAE,CAACG,WAAW,CAAC,CAAC,CAAC;IACnC,MAAM,CAACF,MAAM,CAAC,GAAGnB,UAAU,CAACyC,SAAS,CAAC;IACtC,IAAIzB,sBAAsB,EAAE;MAC1B;MACA,IAAIG,MAAM,KAAKvB,cAAc,CAAC8B,KAAK,EAAE;QACnC,OAAO,KAAK;MACd;IACF,CAAC,MAAM,IAAIP,MAAM,KAAKvB,cAAc,CAAC8B,KAAK,IAAIP,MAAM,KAAKvB,cAAc,CAAC6B,SAAS,EAAE;MACjF;MACA,OAAO,KAAK;IACd;IACA;IACA;IACA;IACA,IAAIO,iBAAiB,IAAIS,SAAS,IAAI,IAAI,EAAE;MAC1C,IAAI,CAAC,sBAAsB,CAAC1C,IAAI,CAACmB,EAAE,CAAC,EAAE;QACpC,OAAO,KAAK;MACd;IACF;EACF;;EAEA;EACA;EACA,IAAIa,YAAY,EAAE;IAChB,IAAIW,IAAI,GAAG,CAAC;IACZ,KAAK,MAAM,CAACC,CAAC,EAAEzB,EAAE,CAAC,IAAIiB,UAAU,CAACS,OAAO,CAAC,CAAC,EAAE;MAC1C,IAAI1B,EAAE,KAAK,QAAQ,IAAIA,EAAE,KAAK,QAAQ,EAAE;QACtC,IAAIyB,CAAC,GAAG,CAAC,EAAE;UACT,IAAIjD,OAAO,CAACmD,oBAAoB,CAAC9C,IAAI,CAACoC,UAAU,CAACQ,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;YACxD;UACF;UACA,IAAIzB,EAAE,KAAK,QAAQ,EAAE;YACnB;YACA,MAAM4B,IAAI,GAAGX,UAAU,CAACY,OAAO,CAAC,QAAQ,EAAEJ,CAAC,GAAG,CAAC,CAAC;YAChD,MAAM5C,IAAI,GAAG+C,IAAI,GAAG,CAAC,GAAGX,UAAU,CAACtB,KAAK,CAAC6B,IAAI,CAAC,GAAGP,UAAU,CAACtB,KAAK,CAAC6B,IAAI,EAAEI,IAAI,CAAC;YAC7E,IAAIpD,OAAO,CAACsD,SAAS,CAACjD,IAAI,CAACA,IAAI,CAACkD,IAAI,CAAC,EAAE,CAAC,CAAC,EAAE;cACzCP,IAAI,GAAGC,CAAC,GAAG,CAAC;cACZ;YACF;UACF;QACF;QACA,OAAO,KAAK;MACd;IACF;EACF;;EAEA;EACA;EACA,IAAIb,SAAS,IAAIG,MAAM,EAAE;IACvB,IAAIiB,GAAG;;IAEP;IACA,IAAIxD,OAAO,CAACyD,SAAS,CAACpD,IAAI,CAACoC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;MACzCe,GAAG,GAAG,KAAK;IACb,CAAC,MAAM,IAAIxD,OAAO,CAAC0D,SAAS,CAACrD,IAAI,CAACoC,UAAU,CAAC,CAAC,CAAC,CAAC,EAAE;MAChDe,GAAG,GAAG,IAAI;IACZ,CAAC,MAAM;MACL,OAAO,KAAK;IACd;IAEA,IAAIA,GAAG,EAAE;MACP;MACA,IAAI,CAACxD,OAAO,CAAC2D,MAAM,CAACtD,IAAI,CAAC6B,KAAK,CAAC,IAC3B,CAAClC,OAAO,CAAC4D,MAAM,CAACvD,IAAI,CAAC6B,KAAK,CAAC,IAC1BlC,OAAO,CAAC6D,QAAQ,CAACxD,IAAI,CAAC6B,KAAK,CAAC,IAAIlC,OAAO,CAAC8D,QAAQ,CAACzD,IAAI,CAAC6B,KAAK,CAAE,EAAE;QAClE,OAAO,KAAK;MACd;IACF,CAAC,MAAM,IAAI,CAAClC,OAAO,CAAC+D,MAAM,CAAC1D,IAAI,CAAC6B,KAAK,CAAC,IAC3B,CAAClC,OAAO,CAACgE,MAAM,CAAC3D,IAAI,CAAC6B,KAAK,CAAC,EAAE;MAAE;MACxC,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb;AAEA,SAAS+B,YAAYA,CAACC,MAAM,EAAE;EAC5B,MAAMC,MAAM,GAAGD,MAAM,CAACE,GAAG,CAAClC,KAAK,IAAI;IACjC,IAAIA,KAAK,CAACS,UAAU,CAAC,MAAM,CAAC,EAAE;MAC5B,IAAI;QACF,OAAO7C,QAAQ,CAACuE,MAAM,CAACnC,KAAK,CAACoC,SAAS,CAAC,CAAC,CAAC,CAAC;MAC5C,CAAC,CAAC,MAAM;QACN,OAAO,EAAE;MACX;IACF;IACA,OAAOpC,KAAK;EACd,CAAC,CAAC,CAACqB,IAAI,CAAC,GAAG,CAAC;EACZ,OAAOvD,OAAO,CAACuE,UAAU,CAAClE,IAAI,CAAC8D,MAAM,CAAC;AACxC;AAEA,SAASK,UAAUA,CAACnD,UAAU,EAAEoD,OAAO,EAAE;EACvC;EACA,IAAIC,MAAM,GAAGtD,QAAQ,CAACC,UAAU,EAAEoD,OAAO,CAAC;;EAE1C;EACAC,MAAM,GAAGA,MAAM,CAAClC,SAAS,CAAC,KAAK,CAAC;;EAEhC;EACA,MAAM0B,MAAM,GAAGQ,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC;EAChC,MAAMpC,MAAM,GAAG0B,YAAY,CAACC,MAAM,CAAC;;EAEnC;EACA,IAAIU,KAAK,GAAG,KAAK;EACjB,KAAK,MAAM,CAAC3B,CAAC,EAAE4B,SAAS,CAAC,IAAIX,MAAM,CAAChB,OAAO,CAAC,CAAC,EAAE;IAC7C,IAAIhB,KAAK,GAAG2C,SAAS;IACrB,IAAIC,kCAAkC,GAAGL,OAAO,CAACnD,sBAAsB;IACvE,IAAIY,KAAK,CAACS,UAAU,CAAC,MAAM,CAAC,EAAE;MAC5B,IAAIxC,gBAAgB,CAAC+B,KAAK,CAAC,EAAE;QAC3B0C,KAAK,GAAG,IAAI;QACZ;MACF;MAEA,IAAI;QACF1C,KAAK,GAAGpC,QAAQ,CAACuE,MAAM,CAACnC,KAAK,CAACoC,SAAS,CAAC,CAAC,CAAC,CAAC;MAC7C,CAAC,CAAC,MAAM;QACN,IAAI,CAACG,OAAO,CAACM,qBAAqB,EAAE;UAClCH,KAAK,GAAG,IAAI;UACZ;QACF;MACF;MACAV,MAAM,CAACjB,CAAC,CAAC,GAAGf,KAAK;MAEjB,IAAIA,KAAK,KAAK,EAAE,IAAI,CAAC/B,gBAAgB,CAAC+B,KAAK,CAAC,EAAE;QAC5C0C,KAAK,GAAG,IAAI;MACd;MAEAE,kCAAkC,GAAG,KAAK;IAC5C;;IAEA;IACA,IAAIF,KAAK,EAAE;MACT;IACF;IACA,MAAMI,UAAU,GAAG/C,aAAa,CAACC,KAAK,EAAE;MACtC,GAAGuC,OAAO;MACVnD,sBAAsB,EAAEwD,kCAAkC;MAC1DvC;IACF,CAAC,CAAC;IACF,IAAI,CAACyC,UAAU,EAAE;MACfJ,KAAK,GAAG,IAAI;IACd;EACF;EAEA,OAAO;IACLF,MAAM,EAAER,MAAM,CAACX,IAAI,CAAC,GAAG,CAAC;IACxBqB;EACF,CAAC;AACH;AAEA,SAASK,OAAOA,CAAC5D,UAAU,EAAE;EAC3Bc,YAAY,GAAG,KAAK;EACpBC,SAAS,GAAG,KAAK;EACjBC,YAAY,GAAG,KAAK;EACpBC,iBAAiB,GAAG,KAAK;EACzB4C,eAAe,GAAG,KAAK;EACvB5D,sBAAsB,GAAG,KAAK;EAC9ByD,qBAAqB,GAAG;AAC1B,CAAC,GAAG,CAAC,CAAC,EAAE;EACN,MAAMI,MAAM,GAAGX,UAAU,CAACnD,UAAU,EAAE;IACpCc,YAAY;IACZC,SAAS;IACTC,YAAY;IACZC,iBAAiB;IACjBhB,sBAAsB;IACtByD;EACF,CAAC,CAAC;EACF,IAAIb,MAAM,GAAGiB,MAAM,CAACT,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC;EACrCT,MAAM,GAAGA,MAAM,CAACE,GAAG,CAACgB,CAAC,IAAI;IACvB,IAAIjF,gBAAgB,CAACiF,CAAC,CAAC,EAAE;MACvB,IAAI;QACF,OAAO,OAAOtF,QAAQ,CAACuF,MAAM,CAACD,CAAC,CAAC,EAAE;MACpC,CAAC,CAAC,MAAM;QACND,MAAM,CAACP,KAAK,GAAG,IAAI;MACrB;IACF;IACA,OAAOQ,CAAC;EACV,CAAC,CAAC;EAEF,IAAIF,eAAe,EAAE;IACnB,MAAMI,KAAK,GAAGpB,MAAM,CAACX,IAAI,CAAC,GAAG,CAAC,CAAC7C,MAAM;IACrC,IAAI4E,KAAK,GAAG,GAAG,IAAIA,KAAK,KAAK,CAAC,EAAE;MAC9BH,MAAM,CAACP,KAAK,GAAG,IAAI;IACrB;IAEA,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,MAAM,CAACxD,MAAM,EAAE,EAAEuC,CAAC,EAAE;MACtC,IAAIiB,MAAM,CAACjB,CAAC,CAAC,CAACvC,MAAM,GAAG,EAAE,IAAIwD,MAAM,CAACjB,CAAC,CAAC,CAACvC,MAAM,KAAK,CAAC,EAAE;QACnDyE,MAAM,CAACP,KAAK,GAAG,IAAI;QACnB;MACF;IACF;EACF;EAEA,IAAIO,MAAM,CAACP,KAAK,EAAE;IAChB,OAAO,IAAI;EACb;EACA,OAAOV,MAAM,CAACX,IAAI,CAAC,GAAG,CAAC;AACzB;AAEA,SAASgC,SAASA,CAAClE,UAAU,EAAE;EAC7Bc,YAAY,GAAG,KAAK;EACpBC,SAAS,GAAG,KAAK;EACjBC,YAAY,GAAG,KAAK;EACpBC,iBAAiB,GAAG,KAAK;EACzBhB,sBAAsB,GAAG,KAAK;EAC9ByD,qBAAqB,GAAG;AAC1B,CAAC,GAAG,CAAC,CAAC,EAAE;EACN,MAAMI,MAAM,GAAGX,UAAU,CAACnD,UAAU,EAAE;IACpCc,YAAY;IACZC,SAAS;IACTC,YAAY;IACZC,iBAAiB;IACjBhB,sBAAsB;IACtByD;EACF,CAAC,CAAC;EAEF,OAAO;IACLZ,MAAM,EAAEgB,MAAM,CAACT,MAAM;IACrBE,KAAK,EAAEO,MAAM,CAACP;EAChB,CAAC;AACH;AAEAY,MAAM,CAACC,OAAO,GAAG;EACfR,OAAO;EACPM;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}