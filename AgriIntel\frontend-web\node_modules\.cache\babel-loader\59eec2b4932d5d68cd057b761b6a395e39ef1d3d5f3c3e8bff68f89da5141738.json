{"ast": null, "code": "if (typeof Object.create === 'function') {\n  // implementation from standard node.js 'util' module\n  module.exports = function inherits(ctor, superCtor) {\n    ctor.super_ = superCtor;\n    ctor.prototype = Object.create(superCtor.prototype, {\n      constructor: {\n        value: ctor,\n        enumerable: false,\n        writable: true,\n        configurable: true\n      }\n    });\n  };\n} else {\n  // old school shim for old browsers\n  module.exports = function inherits(ctor, superCtor) {\n    ctor.super_ = superCtor;\n    var TempCtor = function () {};\n    TempCtor.prototype = superCtor.prototype;\n    ctor.prototype = new TempCtor();\n    ctor.prototype.constructor = ctor;\n  };\n}", "map": {"version": 3, "names": ["Object", "create", "module", "exports", "inherits", "ctor", "superCtor", "super_", "prototype", "constructor", "value", "enumerable", "writable", "configurable", "TempCtor"], "sources": ["C:/Users/<USER>/node_modules/util/node_modules/inherits/inherits_browser.js"], "sourcesContent": ["if (typeof Object.create === 'function') {\n  // implementation from standard node.js 'util' module\n  module.exports = function inherits(ctor, superCtor) {\n    ctor.super_ = superCtor\n    ctor.prototype = Object.create(superCtor.prototype, {\n      constructor: {\n        value: ctor,\n        enumerable: false,\n        writable: true,\n        configurable: true\n      }\n    });\n  };\n} else {\n  // old school shim for old browsers\n  module.exports = function inherits(ctor, superCtor) {\n    ctor.super_ = superCtor\n    var TempCtor = function () {}\n    TempCtor.prototype = superCtor.prototype\n    ctor.prototype = new TempCtor()\n    ctor.prototype.constructor = ctor\n  }\n}\n"], "mappings": "AAAA,IAAI,OAAOA,MAAM,CAACC,MAAM,KAAK,UAAU,EAAE;EACvC;EACAC,MAAM,CAACC,OAAO,GAAG,SAASC,QAAQA,CAACC,IAAI,EAAEC,SAAS,EAAE;IAClDD,IAAI,CAACE,MAAM,GAAGD,SAAS;IACvBD,IAAI,CAACG,SAAS,GAAGR,MAAM,CAACC,MAAM,CAACK,SAAS,CAACE,SAAS,EAAE;MAClDC,WAAW,EAAE;QACXC,KAAK,EAAEL,IAAI;QACXM,UAAU,EAAE,KAAK;QACjBC,QAAQ,EAAE,IAAI;QACdC,YAAY,EAAE;MAChB;IACF,CAAC,CAAC;EACJ,CAAC;AACH,CAAC,MAAM;EACL;EACAX,MAAM,CAACC,OAAO,GAAG,SAASC,QAAQA,CAACC,IAAI,EAAEC,SAAS,EAAE;IAClDD,IAAI,CAACE,MAAM,GAAGD,SAAS;IACvB,IAAIQ,QAAQ,GAAG,SAAAA,CAAA,EAAY,CAAC,CAAC;IAC7BA,QAAQ,CAACN,SAAS,GAAGF,SAAS,CAACE,SAAS;IACxCH,IAAI,CAACG,SAAS,GAAG,IAAIM,QAAQ,CAAC,CAAC;IAC/BT,IAAI,CAACG,SAAS,CAACC,WAAW,GAAGJ,IAAI;EACnC,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}