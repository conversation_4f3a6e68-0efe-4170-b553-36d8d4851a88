{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.LimitedSizeDocument = void 0;\nexports.makeClientMetadata = makeClientMetadata;\nexports.addContainerMetadata = addContainerMetadata;\nexports.getFAASEnv = getFAASEnv;\nconst os = require(\"os\");\nconst process = require(\"process\");\nconst bson_1 = require(\"../../bson\");\nconst error_1 = require(\"../../error\");\nconst utils_1 = require(\"../../utils\");\n// eslint-disable-next-line @typescript-eslint/no-require-imports\nconst NODE_DRIVER_VERSION = require('../../../package.json').version;\n/** @internal */\nclass LimitedSizeDocument {\n  constructor(maxSize) {\n    this.maxSize = maxSize;\n    this.document = new Map();\n    /** BSON overhead: Int32 + Null byte */\n    this.documentSize = 5;\n  }\n  /** Only adds key/value if the bsonByteLength is less than MAX_SIZE */\n  ifItFitsItSits(key, value) {\n    // The BSON byteLength of the new element is the same as serializing it to its own document\n    // subtracting the document size int32 and the null terminator.\n    const newElementSize = bson_1.BSON.serialize(new Map().set(key, value)).byteLength - 5;\n    if (newElementSize + this.documentSize > this.maxSize) {\n      return false;\n    }\n    this.documentSize += newElementSize;\n    this.document.set(key, value);\n    return true;\n  }\n  toObject() {\n    return bson_1.BSON.deserialize(bson_1.BSON.serialize(this.document), {\n      promoteLongs: false,\n      promoteBuffers: false,\n      promoteValues: false,\n      useBigInt64: false\n    });\n  }\n}\nexports.LimitedSizeDocument = LimitedSizeDocument;\n/**\n * From the specs:\n * Implementors SHOULD cumulatively update fields in the following order until the document is under the size limit:\n * 1. Omit fields from `env` except `env.name`.\n * 2. Omit fields from `os` except `os.type`.\n * 3. Omit the `env` document entirely.\n * 4. Truncate `platform`. -- special we do not truncate this field\n */\nfunction makeClientMetadata(options) {\n  const metadataDocument = new LimitedSizeDocument(512);\n  const {\n    appName = ''\n  } = options;\n  // Add app name first, it must be sent\n  if (appName.length > 0) {\n    const name = Buffer.byteLength(appName, 'utf8') <= 128 ? options.appName : Buffer.from(appName, 'utf8').subarray(0, 128).toString('utf8');\n    metadataDocument.ifItFitsItSits('application', {\n      name\n    });\n  }\n  const {\n    name = '',\n    version = '',\n    platform = ''\n  } = options.driverInfo;\n  const driverInfo = {\n    name: name.length > 0 ? `nodejs|${name}` : 'nodejs',\n    version: version.length > 0 ? `${NODE_DRIVER_VERSION}|${version}` : NODE_DRIVER_VERSION\n  };\n  if (!metadataDocument.ifItFitsItSits('driver', driverInfo)) {\n    throw new error_1.MongoInvalidArgumentError('Unable to include driverInfo name and version, metadata cannot exceed 512 bytes');\n  }\n  let runtimeInfo = getRuntimeInfo();\n  if (platform.length > 0) {\n    runtimeInfo = `${runtimeInfo}|${platform}`;\n  }\n  if (!metadataDocument.ifItFitsItSits('platform', runtimeInfo)) {\n    throw new error_1.MongoInvalidArgumentError('Unable to include driverInfo platform, metadata cannot exceed 512 bytes');\n  }\n  // Note: order matters, os.type is last so it will be removed last if we're at maxSize\n  const osInfo = new Map().set('name', process.platform).set('architecture', process.arch).set('version', os.release()).set('type', os.type());\n  if (!metadataDocument.ifItFitsItSits('os', osInfo)) {\n    for (const key of osInfo.keys()) {\n      osInfo.delete(key);\n      if (osInfo.size === 0) break;\n      if (metadataDocument.ifItFitsItSits('os', osInfo)) break;\n    }\n  }\n  const faasEnv = getFAASEnv();\n  if (faasEnv != null) {\n    if (!metadataDocument.ifItFitsItSits('env', faasEnv)) {\n      for (const key of faasEnv.keys()) {\n        faasEnv.delete(key);\n        if (faasEnv.size === 0) break;\n        if (metadataDocument.ifItFitsItSits('env', faasEnv)) break;\n      }\n    }\n  }\n  return metadataDocument.toObject();\n}\nlet dockerPromise;\n/** @internal */\nasync function getContainerMetadata() {\n  const containerMetadata = {};\n  dockerPromise ??= (0, utils_1.fileIsAccessible)('/.dockerenv');\n  const isDocker = await dockerPromise;\n  const {\n    KUBERNETES_SERVICE_HOST = ''\n  } = process.env;\n  const isKubernetes = KUBERNETES_SERVICE_HOST.length > 0 ? true : false;\n  if (isDocker) containerMetadata.runtime = 'docker';\n  if (isKubernetes) containerMetadata.orchestrator = 'kubernetes';\n  return containerMetadata;\n}\n/**\n * @internal\n * Re-add each metadata value.\n * Attempt to add new env container metadata, but keep old data if it does not fit.\n */\nasync function addContainerMetadata(originalMetadata) {\n  const containerMetadata = await getContainerMetadata();\n  if (Object.keys(containerMetadata).length === 0) return originalMetadata;\n  const extendedMetadata = new LimitedSizeDocument(512);\n  const extendedEnvMetadata = {\n    ...originalMetadata?.env,\n    container: containerMetadata\n  };\n  for (const [key, val] of Object.entries(originalMetadata)) {\n    if (key !== 'env') {\n      extendedMetadata.ifItFitsItSits(key, val);\n    } else {\n      if (!extendedMetadata.ifItFitsItSits('env', extendedEnvMetadata)) {\n        // add in old data if newer / extended metadata does not fit\n        extendedMetadata.ifItFitsItSits('env', val);\n      }\n    }\n  }\n  if (!('env' in originalMetadata)) {\n    extendedMetadata.ifItFitsItSits('env', extendedEnvMetadata);\n  }\n  return extendedMetadata.toObject();\n}\n/**\n * Collects FaaS metadata.\n * - `name` MUST be the last key in the Map returned.\n */\nfunction getFAASEnv() {\n  const {\n    AWS_EXECUTION_ENV = '',\n    AWS_LAMBDA_RUNTIME_API = '',\n    FUNCTIONS_WORKER_RUNTIME = '',\n    K_SERVICE = '',\n    FUNCTION_NAME = '',\n    VERCEL = '',\n    AWS_LAMBDA_FUNCTION_MEMORY_SIZE = '',\n    AWS_REGION = '',\n    FUNCTION_MEMORY_MB = '',\n    FUNCTION_REGION = '',\n    FUNCTION_TIMEOUT_SEC = '',\n    VERCEL_REGION = ''\n  } = process.env;\n  const isAWSFaaS = AWS_EXECUTION_ENV.startsWith('AWS_Lambda_') || AWS_LAMBDA_RUNTIME_API.length > 0;\n  const isAzureFaaS = FUNCTIONS_WORKER_RUNTIME.length > 0;\n  const isGCPFaaS = K_SERVICE.length > 0 || FUNCTION_NAME.length > 0;\n  const isVercelFaaS = VERCEL.length > 0;\n  // Note: order matters, name must always be the last key\n  const faasEnv = new Map();\n  // When isVercelFaaS is true so is isAWSFaaS; Vercel inherits the AWS env\n  if (isVercelFaaS && !(isAzureFaaS || isGCPFaaS)) {\n    if (VERCEL_REGION.length > 0) {\n      faasEnv.set('region', VERCEL_REGION);\n    }\n    faasEnv.set('name', 'vercel');\n    return faasEnv;\n  }\n  if (isAWSFaaS && !(isAzureFaaS || isGCPFaaS || isVercelFaaS)) {\n    if (AWS_REGION.length > 0) {\n      faasEnv.set('region', AWS_REGION);\n    }\n    if (AWS_LAMBDA_FUNCTION_MEMORY_SIZE.length > 0 && Number.isInteger(+AWS_LAMBDA_FUNCTION_MEMORY_SIZE)) {\n      faasEnv.set('memory_mb', new bson_1.Int32(AWS_LAMBDA_FUNCTION_MEMORY_SIZE));\n    }\n    faasEnv.set('name', 'aws.lambda');\n    return faasEnv;\n  }\n  if (isAzureFaaS && !(isGCPFaaS || isAWSFaaS || isVercelFaaS)) {\n    faasEnv.set('name', 'azure.func');\n    return faasEnv;\n  }\n  if (isGCPFaaS && !(isAzureFaaS || isAWSFaaS || isVercelFaaS)) {\n    if (FUNCTION_REGION.length > 0) {\n      faasEnv.set('region', FUNCTION_REGION);\n    }\n    if (FUNCTION_MEMORY_MB.length > 0 && Number.isInteger(+FUNCTION_MEMORY_MB)) {\n      faasEnv.set('memory_mb', new bson_1.Int32(FUNCTION_MEMORY_MB));\n    }\n    if (FUNCTION_TIMEOUT_SEC.length > 0 && Number.isInteger(+FUNCTION_TIMEOUT_SEC)) {\n      faasEnv.set('timeout_sec', new bson_1.Int32(FUNCTION_TIMEOUT_SEC));\n    }\n    faasEnv.set('name', 'gcp.func');\n    return faasEnv;\n  }\n  return null;\n}\n/**\n * @internal\n * Get current JavaScript runtime platform\n *\n * NOTE: The version information fetching is intentionally written defensively\n * to avoid having a released driver version that becomes incompatible\n * with a future change to these global objects.\n */\nfunction getRuntimeInfo() {\n  if ('Deno' in globalThis) {\n    const version = typeof Deno?.version?.deno === 'string' ? Deno?.version?.deno : '0.0.0-unknown';\n    return `Deno v${version}, ${os.endianness()}`;\n  }\n  if ('Bun' in globalThis) {\n    const version = typeof Bun?.version === 'string' ? Bun?.version : '0.0.0-unknown';\n    return `Bun v${version}, ${os.endianness()}`;\n  }\n  return `Node.js ${process.version}, ${os.endianness()}`;\n}", "map": {"version": 3, "names": ["exports", "makeClientMetadata", "addContainerMetadata", "getFAASEnv", "os", "require", "process", "bson_1", "error_1", "utils_1", "NODE_DRIVER_VERSION", "version", "LimitedSizeDocument", "constructor", "maxSize", "document", "Map", "documentSize", "ifItFitsItSits", "key", "value", "newElementSize", "BSON", "serialize", "set", "byteLength", "toObject", "deserialize", "promoteLongs", "promoteBuffers", "promoteValues", "useBigInt64", "options", "metadataDocument", "appName", "length", "name", "<PERSON><PERSON><PERSON>", "from", "subarray", "toString", "platform", "driverInfo", "MongoInvalidArgumentError", "runtimeInfo", "getRuntimeInfo", "osInfo", "arch", "release", "type", "keys", "delete", "size", "faasEnv", "docker<PERSON><PERSON><PERSON>", "getContainerMetadata", "containerMetadata", "fileIsAccessible", "is<PERSON>ock<PERSON>", "KUBERNETES_SERVICE_HOST", "env", "isKubernetes", "runtime", "orchestrator", "originalMetadata", "Object", "extendedMetadata", "extendedEnvMetadata", "container", "val", "entries", "AWS_EXECUTION_ENV", "AWS_LAMBDA_RUNTIME_API", "FUNCTIONS_WORKER_RUNTIME", "K_SERVICE", "FUNCTION_NAME", "VERCEL", "AWS_LAMBDA_FUNCTION_MEMORY_SIZE", "AWS_REGION", "FUNCTION_MEMORY_MB", "FUNCTION_REGION", "FUNCTION_TIMEOUT_SEC", "VERCEL_REGION", "isAWSFaaS", "startsWith", "isAzureFaaS", "isGCPFaaS", "isVercelFaaS", "Number", "isInteger", "Int32", "globalThis", "<PERSON><PERSON>", "deno", "endianness", "<PERSON>un"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\handshake\\client_metadata.ts"], "sourcesContent": ["import * as os from 'os';\nimport * as process from 'process';\n\nimport { BSON, type Document, Int32 } from '../../bson';\nimport { MongoInvalidArgumentError } from '../../error';\nimport type { MongoOptions } from '../../mongo_client';\nimport { fileIsAccessible } from '../../utils';\n\n// eslint-disable-next-line @typescript-eslint/no-require-imports\nconst NODE_DRIVER_VERSION = require('../../../package.json').version;\n\n/**\n * @public\n * @see https://github.com/mongodb/specifications/blob/master/source/mongodb-handshake/handshake.md#hello-command\n */\nexport interface ClientMetadata {\n  driver: {\n    name: string;\n    version: string;\n  };\n  os: {\n    type: string;\n    name?: NodeJS.Platform;\n    architecture?: string;\n    version?: string;\n  };\n  platform: string;\n  application?: {\n    name: string;\n  };\n  /** FaaS environment information */\n  env?: {\n    name: 'aws.lambda' | 'gcp.func' | 'azure.func' | 'vercel';\n    timeout_sec?: Int32;\n    memory_mb?: Int32;\n    region?: string;\n    url?: string;\n  };\n}\n\n/** @public */\nexport interface ClientMetadataOptions {\n  driverInfo?: {\n    name?: string;\n    version?: string;\n    platform?: string;\n  };\n  appName?: string;\n}\n\n/** @internal */\nexport class LimitedSizeDocument {\n  private document = new Map();\n  /** BSON overhead: Int32 + Null byte */\n  private documentSize = 5;\n  constructor(private maxSize: number) {}\n\n  /** Only adds key/value if the bsonByteLength is less than MAX_SIZE */\n  public ifItFitsItSits(key: string, value: Record<string, any> | string): boolean {\n    // The BSON byteLength of the new element is the same as serializing it to its own document\n    // subtracting the document size int32 and the null terminator.\n    const newElementSize = BSON.serialize(new Map().set(key, value)).byteLength - 5;\n\n    if (newElementSize + this.documentSize > this.maxSize) {\n      return false;\n    }\n\n    this.documentSize += newElementSize;\n\n    this.document.set(key, value);\n\n    return true;\n  }\n\n  toObject(): Document {\n    return BSON.deserialize(BSON.serialize(this.document), {\n      promoteLongs: false,\n      promoteBuffers: false,\n      promoteValues: false,\n      useBigInt64: false\n    });\n  }\n}\n\ntype MakeClientMetadataOptions = Pick<MongoOptions, 'appName' | 'driverInfo'>;\n/**\n * From the specs:\n * Implementors SHOULD cumulatively update fields in the following order until the document is under the size limit:\n * 1. Omit fields from `env` except `env.name`.\n * 2. Omit fields from `os` except `os.type`.\n * 3. Omit the `env` document entirely.\n * 4. Truncate `platform`. -- special we do not truncate this field\n */\nexport function makeClientMetadata(options: MakeClientMetadataOptions): ClientMetadata {\n  const metadataDocument = new LimitedSizeDocument(512);\n\n  const { appName = '' } = options;\n  // Add app name first, it must be sent\n  if (appName.length > 0) {\n    const name =\n      Buffer.byteLength(appName, 'utf8') <= 128\n        ? options.appName\n        : Buffer.from(appName, 'utf8').subarray(0, 128).toString('utf8');\n    metadataDocument.ifItFitsItSits('application', { name });\n  }\n\n  const { name = '', version = '', platform = '' } = options.driverInfo;\n\n  const driverInfo = {\n    name: name.length > 0 ? `nodejs|${name}` : 'nodejs',\n    version: version.length > 0 ? `${NODE_DRIVER_VERSION}|${version}` : NODE_DRIVER_VERSION\n  };\n\n  if (!metadataDocument.ifItFitsItSits('driver', driverInfo)) {\n    throw new MongoInvalidArgumentError(\n      'Unable to include driverInfo name and version, metadata cannot exceed 512 bytes'\n    );\n  }\n\n  let runtimeInfo = getRuntimeInfo();\n  if (platform.length > 0) {\n    runtimeInfo = `${runtimeInfo}|${platform}`;\n  }\n\n  if (!metadataDocument.ifItFitsItSits('platform', runtimeInfo)) {\n    throw new MongoInvalidArgumentError(\n      'Unable to include driverInfo platform, metadata cannot exceed 512 bytes'\n    );\n  }\n\n  // Note: order matters, os.type is last so it will be removed last if we're at maxSize\n  const osInfo = new Map()\n    .set('name', process.platform)\n    .set('architecture', process.arch)\n    .set('version', os.release())\n    .set('type', os.type());\n\n  if (!metadataDocument.ifItFitsItSits('os', osInfo)) {\n    for (const key of osInfo.keys()) {\n      osInfo.delete(key);\n      if (osInfo.size === 0) break;\n      if (metadataDocument.ifItFitsItSits('os', osInfo)) break;\n    }\n  }\n\n  const faasEnv = getFAASEnv();\n  if (faasEnv != null) {\n    if (!metadataDocument.ifItFitsItSits('env', faasEnv)) {\n      for (const key of faasEnv.keys()) {\n        faasEnv.delete(key);\n        if (faasEnv.size === 0) break;\n        if (metadataDocument.ifItFitsItSits('env', faasEnv)) break;\n      }\n    }\n  }\n  return metadataDocument.toObject() as ClientMetadata;\n}\n\nlet dockerPromise: Promise<boolean>;\n/** @internal */\nasync function getContainerMetadata() {\n  const containerMetadata: Record<string, any> = {};\n  dockerPromise ??= fileIsAccessible('/.dockerenv');\n  const isDocker = await dockerPromise;\n\n  const { KUBERNETES_SERVICE_HOST = '' } = process.env;\n  const isKubernetes = KUBERNETES_SERVICE_HOST.length > 0 ? true : false;\n\n  if (isDocker) containerMetadata.runtime = 'docker';\n  if (isKubernetes) containerMetadata.orchestrator = 'kubernetes';\n\n  return containerMetadata;\n}\n\n/**\n * @internal\n * Re-add each metadata value.\n * Attempt to add new env container metadata, but keep old data if it does not fit.\n */\nexport async function addContainerMetadata(originalMetadata: ClientMetadata) {\n  const containerMetadata = await getContainerMetadata();\n  if (Object.keys(containerMetadata).length === 0) return originalMetadata;\n\n  const extendedMetadata = new LimitedSizeDocument(512);\n\n  const extendedEnvMetadata = { ...originalMetadata?.env, container: containerMetadata };\n\n  for (const [key, val] of Object.entries(originalMetadata)) {\n    if (key !== 'env') {\n      extendedMetadata.ifItFitsItSits(key, val);\n    } else {\n      if (!extendedMetadata.ifItFitsItSits('env', extendedEnvMetadata)) {\n        // add in old data if newer / extended metadata does not fit\n        extendedMetadata.ifItFitsItSits('env', val);\n      }\n    }\n  }\n\n  if (!('env' in originalMetadata)) {\n    extendedMetadata.ifItFitsItSits('env', extendedEnvMetadata);\n  }\n\n  return extendedMetadata.toObject();\n}\n\n/**\n * Collects FaaS metadata.\n * - `name` MUST be the last key in the Map returned.\n */\nexport function getFAASEnv(): Map<string, string | Int32> | null {\n  const {\n    AWS_EXECUTION_ENV = '',\n    AWS_LAMBDA_RUNTIME_API = '',\n    FUNCTIONS_WORKER_RUNTIME = '',\n    K_SERVICE = '',\n    FUNCTION_NAME = '',\n    VERCEL = '',\n    AWS_LAMBDA_FUNCTION_MEMORY_SIZE = '',\n    AWS_REGION = '',\n    FUNCTION_MEMORY_MB = '',\n    FUNCTION_REGION = '',\n    FUNCTION_TIMEOUT_SEC = '',\n    VERCEL_REGION = ''\n  } = process.env;\n\n  const isAWSFaaS =\n    AWS_EXECUTION_ENV.startsWith('AWS_Lambda_') || AWS_LAMBDA_RUNTIME_API.length > 0;\n  const isAzureFaaS = FUNCTIONS_WORKER_RUNTIME.length > 0;\n  const isGCPFaaS = K_SERVICE.length > 0 || FUNCTION_NAME.length > 0;\n  const isVercelFaaS = VERCEL.length > 0;\n\n  // Note: order matters, name must always be the last key\n  const faasEnv = new Map();\n\n  // When isVercelFaaS is true so is isAWSFaaS; Vercel inherits the AWS env\n  if (isVercelFaaS && !(isAzureFaaS || isGCPFaaS)) {\n    if (VERCEL_REGION.length > 0) {\n      faasEnv.set('region', VERCEL_REGION);\n    }\n\n    faasEnv.set('name', 'vercel');\n    return faasEnv;\n  }\n\n  if (isAWSFaaS && !(isAzureFaaS || isGCPFaaS || isVercelFaaS)) {\n    if (AWS_REGION.length > 0) {\n      faasEnv.set('region', AWS_REGION);\n    }\n\n    if (\n      AWS_LAMBDA_FUNCTION_MEMORY_SIZE.length > 0 &&\n      Number.isInteger(+AWS_LAMBDA_FUNCTION_MEMORY_SIZE)\n    ) {\n      faasEnv.set('memory_mb', new Int32(AWS_LAMBDA_FUNCTION_MEMORY_SIZE));\n    }\n\n    faasEnv.set('name', 'aws.lambda');\n    return faasEnv;\n  }\n\n  if (isAzureFaaS && !(isGCPFaaS || isAWSFaaS || isVercelFaaS)) {\n    faasEnv.set('name', 'azure.func');\n    return faasEnv;\n  }\n\n  if (isGCPFaaS && !(isAzureFaaS || isAWSFaaS || isVercelFaaS)) {\n    if (FUNCTION_REGION.length > 0) {\n      faasEnv.set('region', FUNCTION_REGION);\n    }\n\n    if (FUNCTION_MEMORY_MB.length > 0 && Number.isInteger(+FUNCTION_MEMORY_MB)) {\n      faasEnv.set('memory_mb', new Int32(FUNCTION_MEMORY_MB));\n    }\n\n    if (FUNCTION_TIMEOUT_SEC.length > 0 && Number.isInteger(+FUNCTION_TIMEOUT_SEC)) {\n      faasEnv.set('timeout_sec', new Int32(FUNCTION_TIMEOUT_SEC));\n    }\n\n    faasEnv.set('name', 'gcp.func');\n    return faasEnv;\n  }\n\n  return null;\n}\n\n/**\n * @internal\n * This type represents the global Deno object and the minimal type contract we expect it to satisfy.\n */\ndeclare const Deno: { version?: { deno?: string } } | undefined;\n\n/**\n * @internal\n * This type represents the global Bun object and the minimal type contract we expect it to satisfy.\n */\ndeclare const Bun: { (): void; version?: string } | undefined;\n\n/**\n * @internal\n * Get current JavaScript runtime platform\n *\n * NOTE: The version information fetching is intentionally written defensively\n * to avoid having a released driver version that becomes incompatible\n * with a future change to these global objects.\n */\nfunction getRuntimeInfo(): string {\n  if ('Deno' in globalThis) {\n    const version = typeof Deno?.version?.deno === 'string' ? Deno?.version?.deno : '0.0.0-unknown';\n\n    return `Deno v${version}, ${os.endianness()}`;\n  }\n\n  if ('Bun' in globalThis) {\n    const version = typeof Bun?.version === 'string' ? Bun?.version : '0.0.0-unknown';\n\n    return `Bun v${version}, ${os.endianness()}`;\n  }\n\n  return `Node.js ${process.version}, ${os.endianness()}`;\n}\n"], "mappings": ";;;;;;AA6FAA,OAAA,CAAAC,kBAAA,GAAAA,kBAAA;AAsFAD,OAAA,CAAAE,oBAAA,GAAAA,oBAAA;AA8BAF,OAAA,CAAAG,UAAA,GAAAA,UAAA;AAjNA,MAAAC,EAAA,GAAAC,OAAA;AACA,MAAAC,OAAA,GAAAD,OAAA;AAEA,MAAAE,MAAA,GAAAF,OAAA;AACA,MAAAG,OAAA,GAAAH,OAAA;AAEA,MAAAI,OAAA,GAAAJ,OAAA;AAEA;AACA,MAAMK,mBAAmB,GAAGL,OAAO,CAAC,uBAAuB,CAAC,CAACM,OAAO;AAyCpE;AACA,MAAaC,mBAAmB;EAI9BC,YAAoBC,OAAe;IAAf,KAAAA,OAAO,GAAPA,OAAO;IAHnB,KAAAC,QAAQ,GAAG,IAAIC,GAAG,EAAE;IAC5B;IACQ,KAAAC,YAAY,GAAG,CAAC;EACc;EAEtC;EACOC,cAAcA,CAACC,GAAW,EAAEC,KAAmC;IACpE;IACA;IACA,MAAMC,cAAc,GAAGd,MAAA,CAAAe,IAAI,CAACC,SAAS,CAAC,IAAIP,GAAG,EAAE,CAACQ,GAAG,CAACL,GAAG,EAAEC,KAAK,CAAC,CAAC,CAACK,UAAU,GAAG,CAAC;IAE/E,IAAIJ,cAAc,GAAG,IAAI,CAACJ,YAAY,GAAG,IAAI,CAACH,OAAO,EAAE;MACrD,OAAO,KAAK;IACd;IAEA,IAAI,CAACG,YAAY,IAAII,cAAc;IAEnC,IAAI,CAACN,QAAQ,CAACS,GAAG,CAACL,GAAG,EAAEC,KAAK,CAAC;IAE7B,OAAO,IAAI;EACb;EAEAM,QAAQA,CAAA;IACN,OAAOnB,MAAA,CAAAe,IAAI,CAACK,WAAW,CAACpB,MAAA,CAAAe,IAAI,CAACC,SAAS,CAAC,IAAI,CAACR,QAAQ,CAAC,EAAE;MACrDa,YAAY,EAAE,KAAK;MACnBC,cAAc,EAAE,KAAK;MACrBC,aAAa,EAAE,KAAK;MACpBC,WAAW,EAAE;KACd,CAAC;EACJ;;AA9BF/B,OAAA,CAAAY,mBAAA,GAAAA,mBAAA;AAkCA;;;;;;;;AAQA,SAAgBX,kBAAkBA,CAAC+B,OAAkC;EACnE,MAAMC,gBAAgB,GAAG,IAAIrB,mBAAmB,CAAC,GAAG,CAAC;EAErD,MAAM;IAAEsB,OAAO,GAAG;EAAE,CAAE,GAAGF,OAAO;EAChC;EACA,IAAIE,OAAO,CAACC,MAAM,GAAG,CAAC,EAAE;IACtB,MAAMC,IAAI,GACRC,MAAM,CAACZ,UAAU,CAACS,OAAO,EAAE,MAAM,CAAC,IAAI,GAAG,GACrCF,OAAO,CAACE,OAAO,GACfG,MAAM,CAACC,IAAI,CAACJ,OAAO,EAAE,MAAM,CAAC,CAACK,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC;IACpEP,gBAAgB,CAACf,cAAc,CAAC,aAAa,EAAE;MAAEkB;IAAI,CAAE,CAAC;EAC1D;EAEA,MAAM;IAAEA,IAAI,GAAG,EAAE;IAAEzB,OAAO,GAAG,EAAE;IAAE8B,QAAQ,GAAG;EAAE,CAAE,GAAGT,OAAO,CAACU,UAAU;EAErE,MAAMA,UAAU,GAAG;IACjBN,IAAI,EAAEA,IAAI,CAACD,MAAM,GAAG,CAAC,GAAG,UAAUC,IAAI,EAAE,GAAG,QAAQ;IACnDzB,OAAO,EAAEA,OAAO,CAACwB,MAAM,GAAG,CAAC,GAAG,GAAGzB,mBAAmB,IAAIC,OAAO,EAAE,GAAGD;GACrE;EAED,IAAI,CAACuB,gBAAgB,CAACf,cAAc,CAAC,QAAQ,EAAEwB,UAAU,CAAC,EAAE;IAC1D,MAAM,IAAIlC,OAAA,CAAAmC,yBAAyB,CACjC,iFAAiF,CAClF;EACH;EAEA,IAAIC,WAAW,GAAGC,cAAc,EAAE;EAClC,IAAIJ,QAAQ,CAACN,MAAM,GAAG,CAAC,EAAE;IACvBS,WAAW,GAAG,GAAGA,WAAW,IAAIH,QAAQ,EAAE;EAC5C;EAEA,IAAI,CAACR,gBAAgB,CAACf,cAAc,CAAC,UAAU,EAAE0B,WAAW,CAAC,EAAE;IAC7D,MAAM,IAAIpC,OAAA,CAAAmC,yBAAyB,CACjC,yEAAyE,CAC1E;EACH;EAEA;EACA,MAAMG,MAAM,GAAG,IAAI9B,GAAG,EAAE,CACrBQ,GAAG,CAAC,MAAM,EAAElB,OAAO,CAACmC,QAAQ,CAAC,CAC7BjB,GAAG,CAAC,cAAc,EAAElB,OAAO,CAACyC,IAAI,CAAC,CACjCvB,GAAG,CAAC,SAAS,EAAEpB,EAAE,CAAC4C,OAAO,EAAE,CAAC,CAC5BxB,GAAG,CAAC,MAAM,EAAEpB,EAAE,CAAC6C,IAAI,EAAE,CAAC;EAEzB,IAAI,CAAChB,gBAAgB,CAACf,cAAc,CAAC,IAAI,EAAE4B,MAAM,CAAC,EAAE;IAClD,KAAK,MAAM3B,GAAG,IAAI2B,MAAM,CAACI,IAAI,EAAE,EAAE;MAC/BJ,MAAM,CAACK,MAAM,CAAChC,GAAG,CAAC;MAClB,IAAI2B,MAAM,CAACM,IAAI,KAAK,CAAC,EAAE;MACvB,IAAInB,gBAAgB,CAACf,cAAc,CAAC,IAAI,EAAE4B,MAAM,CAAC,EAAE;IACrD;EACF;EAEA,MAAMO,OAAO,GAAGlD,UAAU,EAAE;EAC5B,IAAIkD,OAAO,IAAI,IAAI,EAAE;IACnB,IAAI,CAACpB,gBAAgB,CAACf,cAAc,CAAC,KAAK,EAAEmC,OAAO,CAAC,EAAE;MACpD,KAAK,MAAMlC,GAAG,IAAIkC,OAAO,CAACH,IAAI,EAAE,EAAE;QAChCG,OAAO,CAACF,MAAM,CAAChC,GAAG,CAAC;QACnB,IAAIkC,OAAO,CAACD,IAAI,KAAK,CAAC,EAAE;QACxB,IAAInB,gBAAgB,CAACf,cAAc,CAAC,KAAK,EAAEmC,OAAO,CAAC,EAAE;MACvD;IACF;EACF;EACA,OAAOpB,gBAAgB,CAACP,QAAQ,EAAoB;AACtD;AAEA,IAAI4B,aAA+B;AACnC;AACA,eAAeC,oBAAoBA,CAAA;EACjC,MAAMC,iBAAiB,GAAwB,EAAE;EACjDF,aAAa,KAAK,IAAA7C,OAAA,CAAAgD,gBAAgB,EAAC,aAAa,CAAC;EACjD,MAAMC,QAAQ,GAAG,MAAMJ,aAAa;EAEpC,MAAM;IAAEK,uBAAuB,GAAG;EAAE,CAAE,GAAGrD,OAAO,CAACsD,GAAG;EACpD,MAAMC,YAAY,GAAGF,uBAAuB,CAACxB,MAAM,GAAG,CAAC,GAAG,IAAI,GAAG,KAAK;EAEtE,IAAIuB,QAAQ,EAAEF,iBAAiB,CAACM,OAAO,GAAG,QAAQ;EAClD,IAAID,YAAY,EAAEL,iBAAiB,CAACO,YAAY,GAAG,YAAY;EAE/D,OAAOP,iBAAiB;AAC1B;AAEA;;;;;AAKO,eAAetD,oBAAoBA,CAAC8D,gBAAgC;EACzE,MAAMR,iBAAiB,GAAG,MAAMD,oBAAoB,EAAE;EACtD,IAAIU,MAAM,CAACf,IAAI,CAACM,iBAAiB,CAAC,CAACrB,MAAM,KAAK,CAAC,EAAE,OAAO6B,gBAAgB;EAExE,MAAME,gBAAgB,GAAG,IAAItD,mBAAmB,CAAC,GAAG,CAAC;EAErD,MAAMuD,mBAAmB,GAAG;IAAE,GAAGH,gBAAgB,EAAEJ,GAAG;IAAEQ,SAAS,EAAEZ;EAAiB,CAAE;EAEtF,KAAK,MAAM,CAACrC,GAAG,EAAEkD,GAAG,CAAC,IAAIJ,MAAM,CAACK,OAAO,CAACN,gBAAgB,CAAC,EAAE;IACzD,IAAI7C,GAAG,KAAK,KAAK,EAAE;MACjB+C,gBAAgB,CAAChD,cAAc,CAACC,GAAG,EAAEkD,GAAG,CAAC;IAC3C,CAAC,MAAM;MACL,IAAI,CAACH,gBAAgB,CAAChD,cAAc,CAAC,KAAK,EAAEiD,mBAAmB,CAAC,EAAE;QAChE;QACAD,gBAAgB,CAAChD,cAAc,CAAC,KAAK,EAAEmD,GAAG,CAAC;MAC7C;IACF;EACF;EAEA,IAAI,EAAE,KAAK,IAAIL,gBAAgB,CAAC,EAAE;IAChCE,gBAAgB,CAAChD,cAAc,CAAC,KAAK,EAAEiD,mBAAmB,CAAC;EAC7D;EAEA,OAAOD,gBAAgB,CAACxC,QAAQ,EAAE;AACpC;AAEA;;;;AAIA,SAAgBvB,UAAUA,CAAA;EACxB,MAAM;IACJoE,iBAAiB,GAAG,EAAE;IACtBC,sBAAsB,GAAG,EAAE;IAC3BC,wBAAwB,GAAG,EAAE;IAC7BC,SAAS,GAAG,EAAE;IACdC,aAAa,GAAG,EAAE;IAClBC,MAAM,GAAG,EAAE;IACXC,+BAA+B,GAAG,EAAE;IACpCC,UAAU,GAAG,EAAE;IACfC,kBAAkB,GAAG,EAAE;IACvBC,eAAe,GAAG,EAAE;IACpBC,oBAAoB,GAAG,EAAE;IACzBC,aAAa,GAAG;EAAE,CACnB,GAAG5E,OAAO,CAACsD,GAAG;EAEf,MAAMuB,SAAS,GACbZ,iBAAiB,CAACa,UAAU,CAAC,aAAa,CAAC,IAAIZ,sBAAsB,CAACrC,MAAM,GAAG,CAAC;EAClF,MAAMkD,WAAW,GAAGZ,wBAAwB,CAACtC,MAAM,GAAG,CAAC;EACvD,MAAMmD,SAAS,GAAGZ,SAAS,CAACvC,MAAM,GAAG,CAAC,IAAIwC,aAAa,CAACxC,MAAM,GAAG,CAAC;EAClE,MAAMoD,YAAY,GAAGX,MAAM,CAACzC,MAAM,GAAG,CAAC;EAEtC;EACA,MAAMkB,OAAO,GAAG,IAAIrC,GAAG,EAAE;EAEzB;EACA,IAAIuE,YAAY,IAAI,EAAEF,WAAW,IAAIC,SAAS,CAAC,EAAE;IAC/C,IAAIJ,aAAa,CAAC/C,MAAM,GAAG,CAAC,EAAE;MAC5BkB,OAAO,CAAC7B,GAAG,CAAC,QAAQ,EAAE0D,aAAa,CAAC;IACtC;IAEA7B,OAAO,CAAC7B,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC;IAC7B,OAAO6B,OAAO;EAChB;EAEA,IAAI8B,SAAS,IAAI,EAAEE,WAAW,IAAIC,SAAS,IAAIC,YAAY,CAAC,EAAE;IAC5D,IAAIT,UAAU,CAAC3C,MAAM,GAAG,CAAC,EAAE;MACzBkB,OAAO,CAAC7B,GAAG,CAAC,QAAQ,EAAEsD,UAAU,CAAC;IACnC;IAEA,IACED,+BAA+B,CAAC1C,MAAM,GAAG,CAAC,IAC1CqD,MAAM,CAACC,SAAS,CAAC,CAACZ,+BAA+B,CAAC,EAClD;MACAxB,OAAO,CAAC7B,GAAG,CAAC,WAAW,EAAE,IAAIjB,MAAA,CAAAmF,KAAK,CAACb,+BAA+B,CAAC,CAAC;IACtE;IAEAxB,OAAO,CAAC7B,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC;IACjC,OAAO6B,OAAO;EAChB;EAEA,IAAIgC,WAAW,IAAI,EAAEC,SAAS,IAAIH,SAAS,IAAII,YAAY,CAAC,EAAE;IAC5DlC,OAAO,CAAC7B,GAAG,CAAC,MAAM,EAAE,YAAY,CAAC;IACjC,OAAO6B,OAAO;EAChB;EAEA,IAAIiC,SAAS,IAAI,EAAED,WAAW,IAAIF,SAAS,IAAII,YAAY,CAAC,EAAE;IAC5D,IAAIP,eAAe,CAAC7C,MAAM,GAAG,CAAC,EAAE;MAC9BkB,OAAO,CAAC7B,GAAG,CAAC,QAAQ,EAAEwD,eAAe,CAAC;IACxC;IAEA,IAAID,kBAAkB,CAAC5C,MAAM,GAAG,CAAC,IAAIqD,MAAM,CAACC,SAAS,CAAC,CAACV,kBAAkB,CAAC,EAAE;MAC1E1B,OAAO,CAAC7B,GAAG,CAAC,WAAW,EAAE,IAAIjB,MAAA,CAAAmF,KAAK,CAACX,kBAAkB,CAAC,CAAC;IACzD;IAEA,IAAIE,oBAAoB,CAAC9C,MAAM,GAAG,CAAC,IAAIqD,MAAM,CAACC,SAAS,CAAC,CAACR,oBAAoB,CAAC,EAAE;MAC9E5B,OAAO,CAAC7B,GAAG,CAAC,aAAa,EAAE,IAAIjB,MAAA,CAAAmF,KAAK,CAACT,oBAAoB,CAAC,CAAC;IAC7D;IAEA5B,OAAO,CAAC7B,GAAG,CAAC,MAAM,EAAE,UAAU,CAAC;IAC/B,OAAO6B,OAAO;EAChB;EAEA,OAAO,IAAI;AACb;AAcA;;;;;;;;AAQA,SAASR,cAAcA,CAAA;EACrB,IAAI,MAAM,IAAI8C,UAAU,EAAE;IACxB,MAAMhF,OAAO,GAAG,OAAOiF,IAAI,EAAEjF,OAAO,EAAEkF,IAAI,KAAK,QAAQ,GAAGD,IAAI,EAAEjF,OAAO,EAAEkF,IAAI,GAAG,eAAe;IAE/F,OAAO,SAASlF,OAAO,KAAKP,EAAE,CAAC0F,UAAU,EAAE,EAAE;EAC/C;EAEA,IAAI,KAAK,IAAIH,UAAU,EAAE;IACvB,MAAMhF,OAAO,GAAG,OAAOoF,GAAG,EAAEpF,OAAO,KAAK,QAAQ,GAAGoF,GAAG,EAAEpF,OAAO,GAAG,eAAe;IAEjF,OAAO,QAAQA,OAAO,KAAKP,EAAE,CAAC0F,UAAU,EAAE,EAAE;EAC9C;EAEA,OAAO,WAAWxF,OAAO,CAACK,OAAO,KAAKP,EAAE,CAAC0F,UAAU,EAAE,EAAE;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}