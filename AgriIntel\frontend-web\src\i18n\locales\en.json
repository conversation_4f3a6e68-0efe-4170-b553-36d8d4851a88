{"common": {"loading": "Loading...", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "export": "Export", "import": "Import", "refresh": "Refresh", "back": "Back", "next": "Next", "previous": "Previous", "submit": "Submit", "reset": "Reset", "clear": "Clear", "close": "Close", "open": "Open", "view": "View", "download": "Download", "upload": "Upload", "print": "Print", "share": "Share", "copy": "Copy", "cut": "Cut", "paste": "Paste", "select": "Select", "selectAll": "Select All", "none": "None", "all": "All", "yes": "Yes", "no": "No", "ok": "OK", "confirm": "Confirm", "warning": "Warning", "error": "Error", "success": "Success", "info": "Information", "required": "Required", "optional": "Optional", "name": "Name", "description": "Description", "date": "Date", "time": "Time", "status": "Status", "type": "Type", "category": "Category", "notes": "Notes", "actions": "Actions"}, "auth": {"login": "<PERSON><PERSON>", "logout": "Logout", "register": "Register", "username": "Username", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "firstName": "First Name", "lastName": "Last Name", "phoneNumber": "Phone Number", "department": "Department", "role": "Role", "forgotPassword": "Forgot Password?", "resetPassword": "Reset Password", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "loginSuccess": "Login successful", "loginError": "<PERSON><PERSON> failed", "logoutSuccess": "Logout successful", "registerSuccess": "Registration successful", "registerError": "Registration failed", "passwordChangeSuccess": "Password changed successfully", "passwordChangeError": "Password change failed", "invalidCredentials": "Invalid username or password", "accountDeactivated": "Account is deactivated", "sessionExpired": "Session expired. Please login again.", "welcomeBack": "Welcome back", "signInToContinue": "Sign in to continue to AMPD Livestock Management", "dontHaveAccount": "Don't have an account?", "alreadyHaveAccount": "Already have an account?", "createAccount": "Create Account", "signIn": "Sign In"}, "navigation": {"dashboard": "Dashboard", "animals": "Animals", "breeding": "Breeding", "health": "Health", "feeding": "Feeding", "financial": "Financial", "compliance": "Compliance", "inventory": "Inventory", "analytics": "Analytics", "reports": "Reports", "settings": "Settings", "profile": "Profile", "users": "Users", "help": "Help", "support": "Support"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome to AMPD Livestock Management", "overview": "Overview", "quickStats": "Quick Statistics", "recentActivity": "Recent Activity", "alerts": "<PERSON><PERSON><PERSON>", "upcomingTasks": "Upcoming Tasks", "totalAnimals": "Total Animals", "healthyAnimals": "Healthy Animals", "pregnantAnimals": "Pregnant Animals", "upcomingBirths": "Upcoming Births", "vaccinationsDue": "Vaccinations Due", "healthAlerts": "Health Alerts", "feedingAlerts": "Feeding Alerts", "breedingAlerts": "Breeding Alerts", "animalsBySpecies": "Animals by Species", "healthStatusOverview": "Health Status Overview", "breedingStatus": "Breeding Status", "monthlyGrowth": "Monthly Growth", "financialSummary": "Financial Summary"}, "animals": {"title": "Animal Management", "addAnimal": "Add Animal", "editAnimal": "Edit Animal", "animalDetails": "Animal Details", "animalList": "Animal List", "tagNumber": "Tag Number", "species": "Species", "breed": "Breed", "gender": "Gender", "dateOfBirth": "Date of Birth", "age": "Age", "weight": "Weight", "color": "Color", "markings": "Markings", "location": "Location", "healthStatus": "Health Status", "breedingStatus": "Breeding Status", "productionType": "Production Type", "sire": "<PERSON>e", "dam": "Dam", "rfidTag": "RFID Tag", "earTag": "Ear Tag", "microchip": "Microchip ID", "purchasePrice": "Purchase Price", "purchaseDate": "Purchase Date", "currentValue": "Current Value", "weightHistory": "Weight History", "productionRecords": "Production Records", "images": "Images", "documents": "Documents", "male": "Male", "female": "Female", "cattle": "Cattle", "sheep": "Sheep", "goat": "Goa<PERSON>", "pig": "Pig", "chicken": "Chicken", "horse": "Horse", "other": "Other", "active": "Active", "sold": "Sold", "deceased": "Deceased", "transferred": "Transferred", "quarantine": "Quarantine", "healthy": "Healthy", "sick": "Sick", "injured": "Injured", "recovering": "Recovering", "available": "Available", "pregnant": "Pregnant", "lactating": "<PERSON><PERSON>", "breeding": "Breeding", "retired": "Retired"}, "health": {"title": "Health Management", "healthRecords": "Health Records", "addHealthRecord": "Add Health Record", "editHealthRecord": "Edit Health Record", "healthAlerts": "Health Alerts", "vaccinationSchedule": "Vaccination Schedule", "recordType": "Record Type", "veterinarian": "Veterinarian", "diagnosis": "Diagnosis", "treatment": "Treatment", "medication": "Medication", "dosage": "Dosage", "frequency": "Frequency", "duration": "Duration", "symptoms": "Symptoms", "temperature": "Temperature", "heartRate": "Heart Rate", "respiratoryRate": "Respiratory Rate", "bodyConditionScore": "Body Condition Score", "followUpRequired": "Follow-up Required", "followUpDate": "Follow-up Date", "withdrawalPeriod": "<PERSON><PERSON>wal Period", "cost": "Cost", "vaccination": "Vaccination", "checkup": "Checkup", "illness": "Illness", "injury": "Injury", "surgery": "Surgery", "test": "Test", "vaccine": "Vaccine", "manufacturer": "Manufacturer", "batchNumber": "Batch Number", "expirationDate": "Expiration Date", "route": "Route", "intramuscular": "Intramuscular", "subcutaneous": "Subcutaneous", "oral": "Oral", "nasal": "<PERSON><PERSON>", "intravenous": "Intravenous"}, "breeding": {"title": "Breeding Management", "breedingRecords": "Breeding Records", "birthRecords": "Birth Records", "heatDetection": "Heat Detection", "addBreedingRecord": "Add Breeding Record", "editBreedingRecord": "Edit Breeding Record", "addBirthRecord": "Add Birth Record", "editBirthRecord": "Edit Birth Record", "breedingDate": "Breeding Date", "breedingMethod": "Breeding Method", "expectedDueDate": "Expected Due Date", "actualBirthDate": "Actual Birth Date", "pregnancyConfirmed": "Pregnancy Confirmed", "pregnancyConfirmationDate": "Pregnancy Confirmation Date", "pregnancyConfirmationMethod": "Pregnancy Confirmation Method", "gestationPeriod": "Gestation Period", "birthWeight": "Birth Weight", "birthType": "Birth Type", "complications": "Complications", "offspring": "Offspring", "placentaExpelled": "Placenta Expelled", "postBirthTreatment": "Post-Birth Treatment", "natural": "Natural", "artificialInsemination": "Artificial Insemination", "embryoTransfer": "Embryo Transfer", "assisted": "Assisted", "cesarean": "Cesar<PERSON>", "ultrasound": "Ultrasound", "bloodTest": "Blood Test", "physicalExam": "Physical Exam", "planned": "Planned", "completed": "Completed", "failed": "Failed", "aborted": "Aborted", "heatDate": "Heat Date", "heatIntensity": "Heat Intensity", "heatDuration": "Heat Duration", "behaviorSigns": "Behavior Signs", "physicalSigns": "Physical Signs", "breedingRecommended": "Breeding Recommended", "breedingWindow": "Breeding Window", "bred": "<PERSON><PERSON>", "weak": "Weak", "moderate": "Moderate", "strong": "Strong"}, "feeding": {"title": "Feed Management", "feedingRecords": "Feeding Records", "feedInventory": "Feed Inventory", "feedingPlans": "Feeding Plans", "addFeedingRecord": "Add Feeding Record", "editFeedingRecord": "Edit Feeding Record", "feedType": "Feed Type", "quantity": "Quantity", "unit": "Unit", "feedingTime": "Feeding Time", "feedCost": "Feed Cost", "nutritionAnalysis": "Nutrition Analysis", "protein": "<PERSON><PERSON>", "carbohydrates": "Carbohydrates", "fat": "Fat", "fiber": "Fiber", "vitamins": "Vitamins", "minerals": "Minerals", "calories": "Calories", "stockLevel": "Stock Level", "reorderLevel": "Reorder Level", "supplier": "Supplier", "orderDate": "Order Date", "deliveryDate": "Delivery Date", "kg": "kg", "lbs": "lbs", "tons": "tons", "bags": "bags", "liters": "liters", "gallons": "gallons"}, "financial": {"title": "Financial Management", "transactions": "Transactions", "income": "Income", "expenses": "Expenses", "budget": "Budget", "reports": "Reports", "addTransaction": "Add Transaction", "editTransaction": "Edit Transaction", "transactionType": "Transaction Type", "amount": "Amount", "transactionDate": "Transaction Date", "paymentMethod": "Payment Method", "reference": "Reference", "vendor": "<PERSON><PERSON><PERSON>", "customer": "Customer", "invoice": "Invoice", "receipt": "Receipt", "profit": "Profit", "loss": "Loss", "revenue": "Revenue", "costs": "Costs", "roi": "Return on Investment", "cash": "Cash", "check": "Check", "creditCard": "Credit Card", "bankTransfer": "Bank Transfer", "animalSale": "Animal Sale", "productSale": "Product Sale", "feedPurchase": "Feed Purchase", "veterinaryExpense": "Veterinary Expense", "equipmentPurchase": "Equipment Purchase", "laborCost": "Labor Cost", "utilities": "Utilities", "insurance": "Insurance", "taxes": "Taxes", "other": "Other"}, "reports": {"title": "Reports & Analytics", "generateReport": "Generate Report", "reportType": "Report Type", "dateRange": "Date Range", "animalReport": "Animal Report", "healthReport": "Health Report", "breedingReport": "Breeding Report", "financialReport": "Financial Report", "productionReport": "Production Report", "inventoryReport": "Inventory Report", "customReport": "Custom Report", "exportToPDF": "Export to PDF", "exportToExcel": "Export to Excel", "exportToCSV": "Export to CSV", "printReport": "Print Report", "scheduleReport": "Schedule Report", "reportScheduled": "Report Scheduled", "daily": "Daily", "weekly": "Weekly", "monthly": "Monthly", "quarterly": "Quarterly", "yearly": "Yearly", "custom": "Custom"}, "settings": {"title": "Settings", "generalSettings": "General Settings", "userSettings": "User Settings", "systemSettings": "System Settings", "language": "Language", "theme": "Theme", "notifications": "Notifications", "privacy": "Privacy", "security": "Security", "backup": "Backup", "restore": "Rest<PERSON>", "lightTheme": "Light Theme", "darkTheme": "Dark Theme", "emailNotifications": "Email Notifications", "pushNotifications": "Push Notifications", "smsNotifications": "SMS Notifications", "twoFactorAuth": "Two-Factor Authentication", "passwordPolicy": "Password Policy", "sessionTimeout": "Session Timeout", "dataRetention": "Data Retention", "auditLog": "<PERSON>t Log"}, "languages": {"en": "English", "af": "Afrikaans", "st": "<PERSON><PERSON><PERSON><PERSON>", "tn": "Setswana", "zu": "isiZulu"}, "roles": {"admin": "Administrator", "manager": "Manager", "staff": "Staff", "veterinarian": "Veterinarian", "viewer": "Viewer"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Minimum length is {{min}} characters", "maxLength": "Maximum length is {{max}} characters", "numeric": "Please enter a valid number", "positive": "Please enter a positive number", "date": "Please enter a valid date", "phone": "Please enter a valid phone number", "passwordMatch": "Passwords do not match", "passwordStrength": "Password must contain at least one uppercase letter, one lowercase letter, and one number"}, "messages": {"saveSuccess": "Data saved successfully", "saveError": "Failed to save data", "deleteSuccess": "Data deleted successfully", "deleteError": "Failed to delete data", "updateSuccess": "Data updated successfully", "updateError": "Failed to update data", "loadError": "Failed to load data", "networkError": "Network error. Please check your connection.", "serverError": "Server error. Please try again later.", "confirmDelete": "Are you sure you want to delete this item?", "unsavedChanges": "You have unsaved changes. Are you sure you want to leave?", "noData": "No data available", "noResults": "No results found", "searchPlaceholder": "Search...", "selectOption": "Select an option", "uploadSuccess": "File uploaded successfully", "uploadError": "Failed to upload file", "fileSizeError": "File size exceeds the maximum limit", "fileTypeError": "Invalid file type"}}