{"ast": null, "code": "import _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nexport default function makeStyles() {\n  throw new Error(process.env.NODE_ENV !== \"production\" ? \"MUI: makeStyles is no longer exported from @mui/material/styles.\\nYou have to import it from @mui/styles.\\nSee https://mui.com/r/migration-v4/#mui-material-styles for more details.\" : _formatMuiErrorMessage(14));\n}", "map": {"version": 3, "names": ["_formatMuiErrorMessage", "makeStyles", "Error", "process", "env", "NODE_ENV"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/material/styles/makeStyles.js"], "sourcesContent": ["import _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nexport default function makeStyles() {\n  throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: makeStyles is no longer exported from @mui/material/styles.\nYou have to import it from @mui/styles.\nSee https://mui.com/r/migration-v4/#mui-material-styles for more details.` : _formatMuiErrorMessage(14));\n}"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,kCAAkC;AACrE,eAAe,SAASC,UAAUA,CAAA,EAAG;EACnC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,4LAEsBL,sBAAsB,CAAC,EAAE,CAAC,CAAC;AACxG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}