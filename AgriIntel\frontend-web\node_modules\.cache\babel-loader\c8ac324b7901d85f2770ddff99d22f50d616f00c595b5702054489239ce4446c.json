{"ast": null, "code": "import { microTask as i } from './micro-task.js';\nfunction o() {\n  let n = [],\n    r = {\n      addEventListener(e, t, s, a) {\n        return e.addEventListener(t, s, a), r.add(() => e.removeEventListener(t, s, a));\n      },\n      requestAnimationFrame(...e) {\n        let t = requestAnimationFrame(...e);\n        return r.add(() => cancelAnimationFrame(t));\n      },\n      nextFrame(...e) {\n        return r.requestAnimationFrame(() => r.requestAnimationFrame(...e));\n      },\n      setTimeout(...e) {\n        let t = setTimeout(...e);\n        return r.add(() => clearTimeout(t));\n      },\n      microTask(...e) {\n        let t = {\n          current: !0\n        };\n        return i(() => {\n          t.current && e[0]();\n        }), r.add(() => {\n          t.current = !1;\n        });\n      },\n      style(e, t, s) {\n        let a = e.style.getPropertyValue(t);\n        return Object.assign(e.style, {\n          [t]: s\n        }), this.add(() => {\n          Object.assign(e.style, {\n            [t]: a\n          });\n        });\n      },\n      group(e) {\n        let t = o();\n        return e(t), this.add(() => t.dispose());\n      },\n      add(e) {\n        return n.push(e), () => {\n          let t = n.indexOf(e);\n          if (t >= 0) for (let s of n.splice(t, 1)) s();\n        };\n      },\n      dispose() {\n        for (let e of n.splice(0)) e();\n      }\n    };\n  return r;\n}\nexport { o as disposables };", "map": {"version": 3, "names": ["microTask", "i", "o", "n", "r", "addEventListener", "e", "t", "s", "a", "add", "removeEventListener", "requestAnimationFrame", "cancelAnimationFrame", "next<PERSON><PERSON><PERSON>", "setTimeout", "clearTimeout", "current", "style", "getPropertyValue", "Object", "assign", "group", "dispose", "push", "indexOf", "splice", "disposables"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/utils/disposables.js"], "sourcesContent": ["import{microTask as i}from'./micro-task.js';function o(){let n=[],r={addEventListener(e,t,s,a){return e.addEventListener(t,s,a),r.add(()=>e.removeEventListener(t,s,a))},requestAnimationFrame(...e){let t=requestAnimationFrame(...e);return r.add(()=>cancelAnimationFrame(t))},nextFrame(...e){return r.requestAnimationFrame(()=>r.requestAnimationFrame(...e))},setTimeout(...e){let t=setTimeout(...e);return r.add(()=>clearTimeout(t))},microTask(...e){let t={current:!0};return i(()=>{t.current&&e[0]()}),r.add(()=>{t.current=!1})},style(e,t,s){let a=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:s}),this.add(()=>{Object.assign(e.style,{[t]:a})})},group(e){let t=o();return e(t),this.add(()=>t.dispose())},add(e){return n.push(e),()=>{let t=n.indexOf(e);if(t>=0)for(let s of n.splice(t,1))s()}},dispose(){for(let e of n.splice(0))e()}};return r}export{o as disposables};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,QAAK,iBAAiB;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,IAAIC,CAAC,GAAC,EAAE;IAACC,CAAC,GAAC;MAACC,gBAAgBA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;QAAC,OAAOH,CAAC,CAACD,gBAAgB,CAACE,CAAC,EAACC,CAAC,EAACC,CAAC,CAAC,EAACL,CAAC,CAACM,GAAG,CAAC,MAAIJ,CAAC,CAACK,mBAAmB,CAACJ,CAAC,EAACC,CAAC,EAACC,CAAC,CAAC,CAAC;MAAA,CAAC;MAACG,qBAAqBA,CAAC,GAAGN,CAAC,EAAC;QAAC,IAAIC,CAAC,GAACK,qBAAqB,CAAC,GAAGN,CAAC,CAAC;QAAC,OAAOF,CAAC,CAACM,GAAG,CAAC,MAAIG,oBAAoB,CAACN,CAAC,CAAC,CAAC;MAAA,CAAC;MAACO,SAASA,CAAC,GAAGR,CAAC,EAAC;QAAC,OAAOF,CAAC,CAACQ,qBAAqB,CAAC,MAAIR,CAAC,CAACQ,qBAAqB,CAAC,GAAGN,CAAC,CAAC,CAAC;MAAA,CAAC;MAACS,UAAUA,CAAC,GAAGT,CAAC,EAAC;QAAC,IAAIC,CAAC,GAACQ,UAAU,CAAC,GAAGT,CAAC,CAAC;QAAC,OAAOF,CAAC,CAACM,GAAG,CAAC,MAAIM,YAAY,CAACT,CAAC,CAAC,CAAC;MAAA,CAAC;MAACP,SAASA,CAAC,GAAGM,CAAC,EAAC;QAAC,IAAIC,CAAC,GAAC;UAACU,OAAO,EAAC,CAAC;QAAC,CAAC;QAAC,OAAOhB,CAAC,CAAC,MAAI;UAACM,CAAC,CAACU,OAAO,IAAEX,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAAA,CAAC,CAAC,EAACF,CAAC,CAACM,GAAG,CAAC,MAAI;UAACH,CAAC,CAACU,OAAO,GAAC,CAAC,CAAC;QAAA,CAAC,CAAC;MAAA,CAAC;MAACC,KAAKA,CAACZ,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;QAAC,IAAIC,CAAC,GAACH,CAAC,CAACY,KAAK,CAACC,gBAAgB,CAACZ,CAAC,CAAC;QAAC,OAAOa,MAAM,CAACC,MAAM,CAACf,CAAC,CAACY,KAAK,EAAC;UAAC,CAACX,CAAC,GAAEC;QAAC,CAAC,CAAC,EAAC,IAAI,CAACE,GAAG,CAAC,MAAI;UAACU,MAAM,CAACC,MAAM,CAACf,CAAC,CAACY,KAAK,EAAC;YAAC,CAACX,CAAC,GAAEE;UAAC,CAAC,CAAC;QAAA,CAAC,CAAC;MAAA,CAAC;MAACa,KAAKA,CAAChB,CAAC,EAAC;QAAC,IAAIC,CAAC,GAACL,CAAC,CAAC,CAAC;QAAC,OAAOI,CAAC,CAACC,CAAC,CAAC,EAAC,IAAI,CAACG,GAAG,CAAC,MAAIH,CAAC,CAACgB,OAAO,CAAC,CAAC,CAAC;MAAA,CAAC;MAACb,GAAGA,CAACJ,CAAC,EAAC;QAAC,OAAOH,CAAC,CAACqB,IAAI,CAAClB,CAAC,CAAC,EAAC,MAAI;UAAC,IAAIC,CAAC,GAACJ,CAAC,CAACsB,OAAO,CAACnB,CAAC,CAAC;UAAC,IAAGC,CAAC,IAAE,CAAC,EAAC,KAAI,IAAIC,CAAC,IAAIL,CAAC,CAACuB,MAAM,CAACnB,CAAC,EAAC,CAAC,CAAC,EAACC,CAAC,CAAC,CAAC;QAAA,CAAC;MAAA,CAAC;MAACe,OAAOA,CAAA,EAAE;QAAC,KAAI,IAAIjB,CAAC,IAAIH,CAAC,CAACuB,MAAM,CAAC,CAAC,CAAC,EAACpB,CAAC,CAAC,CAAC;MAAA;IAAC,CAAC;EAAC,OAAOF,CAAC;AAAA;AAAC,SAAOF,CAAC,IAAIyB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}