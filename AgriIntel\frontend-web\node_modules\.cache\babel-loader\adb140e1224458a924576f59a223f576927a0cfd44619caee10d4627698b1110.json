{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.LegacyAWSTemporaryCredentialProvider = exports.AWSSDKCredentialProvider = exports.AWSTemporaryCredentialProvider = void 0;\nconst deps_1 = require(\"../../deps\");\nconst error_1 = require(\"../../error\");\nconst utils_1 = require(\"../../utils\");\nconst AWS_RELATIVE_URI = 'http://169.254.170.2';\nconst AWS_EC2_URI = 'http://169.254.169.254';\nconst AWS_EC2_PATH = '/latest/meta-data/iam/security-credentials';\n/**\n * @internal\n *\n * Fetches temporary AWS credentials.\n */\nclass AWSTemporaryCredentialProvider {\n  static get awsSDK() {\n    AWSTemporaryCredentialProvider._awsSDK ??= (0, deps_1.getAwsCredentialProvider)();\n    return AWSTemporaryCredentialProvider._awsSDK;\n  }\n  static get isAWSSDKInstalled() {\n    return !('kModuleError' in AWSTemporaryCredentialProvider.awsSDK);\n  }\n}\nexports.AWSTemporaryCredentialProvider = AWSTemporaryCredentialProvider;\n/** @internal */\nclass AWSSDKCredentialProvider extends AWSTemporaryCredentialProvider {\n  /**\n   * Create the SDK credentials provider.\n   * @param credentialsProvider - The credentials provider.\n   */\n  constructor(credentialsProvider) {\n    super();\n    if (credentialsProvider) {\n      this._provider = credentialsProvider;\n    }\n  }\n  /**\n   * The AWS SDK caches credentials automatically and handles refresh when the credentials have expired.\n   * To ensure this occurs, we need to cache the `provider` returned by the AWS sdk and re-use it when fetching credentials.\n   */\n  get provider() {\n    if ('kModuleError' in AWSTemporaryCredentialProvider.awsSDK) {\n      throw AWSTemporaryCredentialProvider.awsSDK.kModuleError;\n    }\n    if (this._provider) {\n      return this._provider;\n    }\n    let {\n      AWS_STS_REGIONAL_ENDPOINTS = '',\n      AWS_REGION = ''\n    } = process.env;\n    AWS_STS_REGIONAL_ENDPOINTS = AWS_STS_REGIONAL_ENDPOINTS.toLowerCase();\n    AWS_REGION = AWS_REGION.toLowerCase();\n    /** The option setting should work only for users who have explicit settings in their environment, the driver should not encode \"defaults\" */\n    const awsRegionSettingsExist = AWS_REGION.length !== 0 && AWS_STS_REGIONAL_ENDPOINTS.length !== 0;\n    /**\n     * The following regions use the global AWS STS endpoint, sts.amazonaws.com, by default\n     * https://docs.aws.amazon.com/sdkref/latest/guide/feature-sts-regionalized-endpoints.html\n     */\n    const LEGACY_REGIONS = new Set(['ap-northeast-1', 'ap-south-1', 'ap-southeast-1', 'ap-southeast-2', 'aws-global', 'ca-central-1', 'eu-central-1', 'eu-north-1', 'eu-west-1', 'eu-west-2', 'eu-west-3', 'sa-east-1', 'us-east-1', 'us-east-2', 'us-west-1', 'us-west-2']);\n    /**\n     * If AWS_STS_REGIONAL_ENDPOINTS is set to regional, users are opting into the new behavior of respecting the region settings\n     *\n     * If AWS_STS_REGIONAL_ENDPOINTS is set to legacy, then \"old\" regions need to keep using the global setting.\n     * Technically the SDK gets this wrong, it reaches out to 'sts.us-east-1.amazonaws.com' when it should be 'sts.amazonaws.com'.\n     * That is not our bug to fix here. We leave that up to the SDK.\n     */\n    const useRegionalSts = AWS_STS_REGIONAL_ENDPOINTS === 'regional' || AWS_STS_REGIONAL_ENDPOINTS === 'legacy' && !LEGACY_REGIONS.has(AWS_REGION);\n    this._provider = awsRegionSettingsExist && useRegionalSts ? AWSTemporaryCredentialProvider.awsSDK.fromNodeProviderChain({\n      clientConfig: {\n        region: AWS_REGION\n      }\n    }) : AWSTemporaryCredentialProvider.awsSDK.fromNodeProviderChain();\n    return this._provider;\n  }\n  async getCredentials() {\n    /*\n     * Creates a credential provider that will attempt to find credentials from the\n     * following sources (listed in order of precedence):\n     *\n     * - Environment variables exposed via process.env\n     * - SSO credentials from token cache\n     * - Web identity token credentials\n     * - Shared credentials and config ini files\n     * - The EC2/ECS Instance Metadata Service\n     */\n    try {\n      const creds = await this.provider();\n      return {\n        AccessKeyId: creds.accessKeyId,\n        SecretAccessKey: creds.secretAccessKey,\n        Token: creds.sessionToken,\n        Expiration: creds.expiration\n      };\n    } catch (error) {\n      throw new error_1.MongoAWSError(error.message, {\n        cause: error\n      });\n    }\n  }\n}\nexports.AWSSDKCredentialProvider = AWSSDKCredentialProvider;\n/**\n * @internal\n * Fetches credentials manually (without the AWS SDK), as outlined in the [Obtaining Credentials](https://github.com/mongodb/specifications/blob/master/source/auth/auth.md#obtaining-credentials)\n * section of the Auth spec.\n */\nclass LegacyAWSTemporaryCredentialProvider extends AWSTemporaryCredentialProvider {\n  async getCredentials() {\n    // If the environment variable AWS_CONTAINER_CREDENTIALS_RELATIVE_URI\n    // is set then drivers MUST assume that it was set by an AWS ECS agent\n    if (process.env.AWS_CONTAINER_CREDENTIALS_RELATIVE_URI) {\n      return await (0, utils_1.request)(`${AWS_RELATIVE_URI}${process.env.AWS_CONTAINER_CREDENTIALS_RELATIVE_URI}`);\n    }\n    // Otherwise assume we are on an EC2 instance\n    // get a token\n    const token = await (0, utils_1.request)(`${AWS_EC2_URI}/latest/api/token`, {\n      method: 'PUT',\n      json: false,\n      headers: {\n        'X-aws-ec2-metadata-token-ttl-seconds': 30\n      }\n    });\n    // get role name\n    const roleName = await (0, utils_1.request)(`${AWS_EC2_URI}/${AWS_EC2_PATH}`, {\n      json: false,\n      headers: {\n        'X-aws-ec2-metadata-token': token\n      }\n    });\n    // get temp credentials\n    const creds = await (0, utils_1.request)(`${AWS_EC2_URI}/${AWS_EC2_PATH}/${roleName}`, {\n      headers: {\n        'X-aws-ec2-metadata-token': token\n      }\n    });\n    return creds;\n  }\n}\nexports.LegacyAWSTemporaryCredentialProvider = LegacyAWSTemporaryCredentialProvider;", "map": {"version": 3, "names": ["deps_1", "require", "error_1", "utils_1", "AWS_RELATIVE_URI", "AWS_EC2_URI", "AWS_EC2_PATH", "AWSTemporaryCredentialProvider", "awsSDK", "_awsSDK", "getAwsCredentialProvider", "isAWSSDKInstalled", "exports", "AWSSDKCredentialProvider", "constructor", "<PERSON><PERSON><PERSON><PERSON>", "_provider", "provider", "kModuleError", "AWS_STS_REGIONAL_ENDPOINTS", "AWS_REGION", "process", "env", "toLowerCase", "awsRegionSettingsExist", "length", "LEGACY_REGIONS", "Set", "useRegionalSts", "has", "fromNodeProviderChain", "clientConfig", "region", "getCredentials", "creds", "AccessKeyId", "accessKeyId", "SecretAccess<PERSON>ey", "secretAccessKey", "Token", "sessionToken", "Expiration", "expiration", "error", "MongoAWSError", "message", "cause", "LegacyAWSTemporaryCredentialProvider", "AWS_CONTAINER_CREDENTIALS_RELATIVE_URI", "request", "token", "method", "json", "headers", "<PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\auth\\aws_temporary_credentials.ts"], "sourcesContent": ["import { type AWSCredentials, getAwsCredentialProvider } from '../../deps';\nimport { MongoAWSError } from '../../error';\nimport { request } from '../../utils';\n\nconst AWS_RELATIVE_URI = 'http://169.254.170.2';\nconst AWS_EC2_URI = 'http://169.254.169.254';\nconst AWS_EC2_PATH = '/latest/meta-data/iam/security-credentials';\n\n/**\n * @internal\n * This interface matches the final result of fetching temporary credentials manually, outlined\n * in the spec [here](https://github.com/mongodb/specifications/blob/master/source/auth/auth.md#ec2-endpoint).\n *\n * When we use the AWS SDK, we map the response from the SDK to conform to this interface.\n */\nexport interface AWSTempCredentials {\n  AccessKeyId?: string;\n  SecretAccessKey?: string;\n  Token?: string;\n  RoleArn?: string;\n  Expiration?: Date;\n}\n\n/** @public **/\nexport type AWSCredentialProvider = () => Promise<AWSCredentials>;\n\n/**\n * @internal\n *\n * Fetches temporary AWS credentials.\n */\nexport abstract class AWSTemporaryCredentialProvider {\n  abstract getCredentials(): Promise<AWSTempCredentials>;\n  private static _awsSDK: ReturnType<typeof getAwsCredentialProvider>;\n  protected static get awsSDK() {\n    AWSTemporaryCredentialProvider._awsSDK ??= getAwsCredentialProvider();\n    return AWSTemporaryCredentialProvider._awsSDK;\n  }\n\n  static get isAWSSDKInstalled(): boolean {\n    return !('kModuleError' in AWSTemporaryCredentialProvider.awsSDK);\n  }\n}\n\n/** @internal */\nexport class AWSSDKCredentialProvider extends AWSTemporaryCredentialProvider {\n  private _provider?: AWSCredentialProvider;\n\n  /**\n   * Create the SDK credentials provider.\n   * @param credentialsProvider - The credentials provider.\n   */\n  constructor(credentialsProvider?: AWSCredentialProvider) {\n    super();\n\n    if (credentialsProvider) {\n      this._provider = credentialsProvider;\n    }\n  }\n\n  /**\n   * The AWS SDK caches credentials automatically and handles refresh when the credentials have expired.\n   * To ensure this occurs, we need to cache the `provider` returned by the AWS sdk and re-use it when fetching credentials.\n   */\n  private get provider(): () => Promise<AWSCredentials> {\n    if ('kModuleError' in AWSTemporaryCredentialProvider.awsSDK) {\n      throw AWSTemporaryCredentialProvider.awsSDK.kModuleError;\n    }\n    if (this._provider) {\n      return this._provider;\n    }\n    let { AWS_STS_REGIONAL_ENDPOINTS = '', AWS_REGION = '' } = process.env;\n    AWS_STS_REGIONAL_ENDPOINTS = AWS_STS_REGIONAL_ENDPOINTS.toLowerCase();\n    AWS_REGION = AWS_REGION.toLowerCase();\n\n    /** The option setting should work only for users who have explicit settings in their environment, the driver should not encode \"defaults\" */\n    const awsRegionSettingsExist =\n      AWS_REGION.length !== 0 && AWS_STS_REGIONAL_ENDPOINTS.length !== 0;\n\n    /**\n     * The following regions use the global AWS STS endpoint, sts.amazonaws.com, by default\n     * https://docs.aws.amazon.com/sdkref/latest/guide/feature-sts-regionalized-endpoints.html\n     */\n    const LEGACY_REGIONS = new Set([\n      'ap-northeast-1',\n      'ap-south-1',\n      'ap-southeast-1',\n      'ap-southeast-2',\n      'aws-global',\n      'ca-central-1',\n      'eu-central-1',\n      'eu-north-1',\n      'eu-west-1',\n      'eu-west-2',\n      'eu-west-3',\n      'sa-east-1',\n      'us-east-1',\n      'us-east-2',\n      'us-west-1',\n      'us-west-2'\n    ]);\n    /**\n     * If AWS_STS_REGIONAL_ENDPOINTS is set to regional, users are opting into the new behavior of respecting the region settings\n     *\n     * If AWS_STS_REGIONAL_ENDPOINTS is set to legacy, then \"old\" regions need to keep using the global setting.\n     * Technically the SDK gets this wrong, it reaches out to 'sts.us-east-1.amazonaws.com' when it should be 'sts.amazonaws.com'.\n     * That is not our bug to fix here. We leave that up to the SDK.\n     */\n    const useRegionalSts =\n      AWS_STS_REGIONAL_ENDPOINTS === 'regional' ||\n      (AWS_STS_REGIONAL_ENDPOINTS === 'legacy' && !LEGACY_REGIONS.has(AWS_REGION));\n\n    this._provider =\n      awsRegionSettingsExist && useRegionalSts\n        ? AWSTemporaryCredentialProvider.awsSDK.fromNodeProviderChain({\n            clientConfig: { region: AWS_REGION }\n          })\n        : AWSTemporaryCredentialProvider.awsSDK.fromNodeProviderChain();\n\n    return this._provider;\n  }\n\n  override async getCredentials(): Promise<AWSTempCredentials> {\n    /*\n     * Creates a credential provider that will attempt to find credentials from the\n     * following sources (listed in order of precedence):\n     *\n     * - Environment variables exposed via process.env\n     * - SSO credentials from token cache\n     * - Web identity token credentials\n     * - Shared credentials and config ini files\n     * - The EC2/ECS Instance Metadata Service\n     */\n    try {\n      const creds = await this.provider();\n      return {\n        AccessKeyId: creds.accessKeyId,\n        SecretAccessKey: creds.secretAccessKey,\n        Token: creds.sessionToken,\n        Expiration: creds.expiration\n      };\n    } catch (error) {\n      throw new MongoAWSError(error.message, { cause: error });\n    }\n  }\n}\n\n/**\n * @internal\n * Fetches credentials manually (without the AWS SDK), as outlined in the [Obtaining Credentials](https://github.com/mongodb/specifications/blob/master/source/auth/auth.md#obtaining-credentials)\n * section of the Auth spec.\n */\nexport class LegacyAWSTemporaryCredentialProvider extends AWSTemporaryCredentialProvider {\n  override async getCredentials(): Promise<AWSTempCredentials> {\n    // If the environment variable AWS_CONTAINER_CREDENTIALS_RELATIVE_URI\n    // is set then drivers MUST assume that it was set by an AWS ECS agent\n    if (process.env.AWS_CONTAINER_CREDENTIALS_RELATIVE_URI) {\n      return await request(\n        `${AWS_RELATIVE_URI}${process.env.AWS_CONTAINER_CREDENTIALS_RELATIVE_URI}`\n      );\n    }\n\n    // Otherwise assume we are on an EC2 instance\n\n    // get a token\n    const token = await request(`${AWS_EC2_URI}/latest/api/token`, {\n      method: 'PUT',\n      json: false,\n      headers: { 'X-aws-ec2-metadata-token-ttl-seconds': 30 }\n    });\n\n    // get role name\n    const roleName = await request(`${AWS_EC2_URI}/${AWS_EC2_PATH}`, {\n      json: false,\n      headers: { 'X-aws-ec2-metadata-token': token }\n    });\n\n    // get temp credentials\n    const creds = await request(`${AWS_EC2_URI}/${AWS_EC2_PATH}/${roleName}`, {\n      headers: { 'X-aws-ec2-metadata-token': token }\n    });\n\n    return creds;\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,MAAA,GAAAC,OAAA;AACA,MAAAC,OAAA,GAAAD,OAAA;AACA,MAAAE,OAAA,GAAAF,OAAA;AAEA,MAAMG,gBAAgB,GAAG,sBAAsB;AAC/C,MAAMC,WAAW,GAAG,wBAAwB;AAC5C,MAAMC,YAAY,GAAG,4CAA4C;AAoBjE;;;;;AAKA,MAAsBC,8BAA8B;EAGxC,WAAWC,MAAMA,CAAA;IACzBD,8BAA8B,CAACE,OAAO,KAAK,IAAAT,MAAA,CAAAU,wBAAwB,GAAE;IACrE,OAAOH,8BAA8B,CAACE,OAAO;EAC/C;EAEA,WAAWE,iBAAiBA,CAAA;IAC1B,OAAO,EAAE,cAAc,IAAIJ,8BAA8B,CAACC,MAAM,CAAC;EACnE;;AAVFI,OAAA,CAAAL,8BAAA,GAAAA,8BAAA;AAaA;AACA,MAAaM,wBAAyB,SAAQN,8BAA8B;EAG1E;;;;EAIAO,YAAYC,mBAA2C;IACrD,KAAK,EAAE;IAEP,IAAIA,mBAAmB,EAAE;MACvB,IAAI,CAACC,SAAS,GAAGD,mBAAmB;IACtC;EACF;EAEA;;;;EAIA,IAAYE,QAAQA,CAAA;IAClB,IAAI,cAAc,IAAIV,8BAA8B,CAACC,MAAM,EAAE;MAC3D,MAAMD,8BAA8B,CAACC,MAAM,CAACU,YAAY;IAC1D;IACA,IAAI,IAAI,CAACF,SAAS,EAAE;MAClB,OAAO,IAAI,CAACA,SAAS;IACvB;IACA,IAAI;MAAEG,0BAA0B,GAAG,EAAE;MAAEC,UAAU,GAAG;IAAE,CAAE,GAAGC,OAAO,CAACC,GAAG;IACtEH,0BAA0B,GAAGA,0BAA0B,CAACI,WAAW,EAAE;IACrEH,UAAU,GAAGA,UAAU,CAACG,WAAW,EAAE;IAErC;IACA,MAAMC,sBAAsB,GAC1BJ,UAAU,CAACK,MAAM,KAAK,CAAC,IAAIN,0BAA0B,CAACM,MAAM,KAAK,CAAC;IAEpE;;;;IAIA,MAAMC,cAAc,GAAG,IAAIC,GAAG,CAAC,CAC7B,gBAAgB,EAChB,YAAY,EACZ,gBAAgB,EAChB,gBAAgB,EAChB,YAAY,EACZ,cAAc,EACd,cAAc,EACd,YAAY,EACZ,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,EACX,WAAW,CACZ,CAAC;IACF;;;;;;;IAOA,MAAMC,cAAc,GAClBT,0BAA0B,KAAK,UAAU,IACxCA,0BAA0B,KAAK,QAAQ,IAAI,CAACO,cAAc,CAACG,GAAG,CAACT,UAAU,CAAE;IAE9E,IAAI,CAACJ,SAAS,GACZQ,sBAAsB,IAAII,cAAc,GACpCrB,8BAA8B,CAACC,MAAM,CAACsB,qBAAqB,CAAC;MAC1DC,YAAY,EAAE;QAAEC,MAAM,EAAEZ;MAAU;KACnC,CAAC,GACFb,8BAA8B,CAACC,MAAM,CAACsB,qBAAqB,EAAE;IAEnE,OAAO,IAAI,CAACd,SAAS;EACvB;EAES,MAAMiB,cAAcA,CAAA;IAC3B;;;;;;;;;;IAUA,IAAI;MACF,MAAMC,KAAK,GAAG,MAAM,IAAI,CAACjB,QAAQ,EAAE;MACnC,OAAO;QACLkB,WAAW,EAAED,KAAK,CAACE,WAAW;QAC9BC,eAAe,EAAEH,KAAK,CAACI,eAAe;QACtCC,KAAK,EAAEL,KAAK,CAACM,YAAY;QACzBC,UAAU,EAAEP,KAAK,CAACQ;OACnB;IACH,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,MAAM,IAAIzC,OAAA,CAAA0C,aAAa,CAACD,KAAK,CAACE,OAAO,EAAE;QAAEC,KAAK,EAAEH;MAAK,CAAE,CAAC;IAC1D;EACF;;AAnGF/B,OAAA,CAAAC,wBAAA,GAAAA,wBAAA;AAsGA;;;;;AAKA,MAAakC,oCAAqC,SAAQxC,8BAA8B;EAC7E,MAAM0B,cAAcA,CAAA;IAC3B;IACA;IACA,IAAIZ,OAAO,CAACC,GAAG,CAAC0B,sCAAsC,EAAE;MACtD,OAAO,MAAM,IAAA7C,OAAA,CAAA8C,OAAO,EAClB,GAAG7C,gBAAgB,GAAGiB,OAAO,CAACC,GAAG,CAAC0B,sCAAsC,EAAE,CAC3E;IACH;IAEA;IAEA;IACA,MAAME,KAAK,GAAG,MAAM,IAAA/C,OAAA,CAAA8C,OAAO,EAAC,GAAG5C,WAAW,mBAAmB,EAAE;MAC7D8C,MAAM,EAAE,KAAK;MACbC,IAAI,EAAE,KAAK;MACXC,OAAO,EAAE;QAAE,sCAAsC,EAAE;MAAE;KACtD,CAAC;IAEF;IACA,MAAMC,QAAQ,GAAG,MAAM,IAAAnD,OAAA,CAAA8C,OAAO,EAAC,GAAG5C,WAAW,IAAIC,YAAY,EAAE,EAAE;MAC/D8C,IAAI,EAAE,KAAK;MACXC,OAAO,EAAE;QAAE,0BAA0B,EAAEH;MAAK;KAC7C,CAAC;IAEF;IACA,MAAMhB,KAAK,GAAG,MAAM,IAAA/B,OAAA,CAAA8C,OAAO,EAAC,GAAG5C,WAAW,IAAIC,YAAY,IAAIgD,QAAQ,EAAE,EAAE;MACxED,OAAO,EAAE;QAAE,0BAA0B,EAAEH;MAAK;KAC7C,CAAC;IAEF,OAAOhB,KAAK;EACd;;AA/BFtB,OAAA,CAAAmC,oCAAA,GAAAA,oCAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}