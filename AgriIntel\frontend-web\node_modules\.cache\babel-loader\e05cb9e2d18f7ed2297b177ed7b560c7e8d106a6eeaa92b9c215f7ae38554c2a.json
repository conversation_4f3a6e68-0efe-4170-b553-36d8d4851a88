{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.buildReplaceOneOperation = exports.buildUpdateManyOperation = exports.buildUpdateOneOperation = exports.buildDeleteManyOperation = exports.buildDeleteOneOperation = exports.buildInsertOneOperation = exports.ClientBulkWriteCommandBuilder = void 0;\nexports.buildOperation = buildOperation;\nconst bson_1 = require(\"../../bson\");\nconst commands_1 = require(\"../../cmap/commands\");\nconst error_1 = require(\"../../error\");\nconst utils_1 = require(\"../../utils\");\n/**\n * The bytes overhead for the extra fields added post command generation.\n */\nconst MESSAGE_OVERHEAD_BYTES = 1000;\n/** @internal */\nclass ClientBulkWriteCommandBuilder {\n  /**\n   * Create the command builder.\n   * @param models - The client write models.\n   */\n  constructor(models, options, pkFactory) {\n    this.models = models;\n    this.options = options;\n    this.pkFactory = pkFactory ?? utils_1.DEFAULT_PK_FACTORY;\n    this.currentModelIndex = 0;\n    this.previousModelIndex = 0;\n    this.lastOperations = [];\n    this.isBatchRetryable = true;\n  }\n  /**\n   * Gets the errorsOnly value for the command, which is the inverse of the\n   * user provided verboseResults option. Defaults to true.\n   */\n  get errorsOnly() {\n    if ('verboseResults' in this.options) {\n      return !this.options.verboseResults;\n    }\n    return true;\n  }\n  /**\n   * Determines if there is another batch to process.\n   * @returns True if not all batches have been built.\n   */\n  hasNextBatch() {\n    return this.currentModelIndex < this.models.length;\n  }\n  /**\n   * When we need to retry a command we need to set the current\n   * model index back to its previous value.\n   */\n  resetBatch() {\n    this.currentModelIndex = this.previousModelIndex;\n    return true;\n  }\n  /**\n   * Build a single batch of a client bulk write command.\n   * @param maxMessageSizeBytes - The max message size in bytes.\n   * @param maxWriteBatchSize - The max write batch size.\n   * @returns The client bulk write command.\n   */\n  buildBatch(maxMessageSizeBytes, maxWriteBatchSize, maxBsonObjectSize) {\n    // We start by assuming the batch has no multi-updates, so it is retryable\n    // until we find them.\n    this.isBatchRetryable = true;\n    let commandLength = 0;\n    let currentNamespaceIndex = 0;\n    const command = this.baseCommand();\n    const namespaces = new Map();\n    // In the case of retries we need to mark where we started this batch.\n    this.previousModelIndex = this.currentModelIndex;\n    while (this.currentModelIndex < this.models.length) {\n      const model = this.models[this.currentModelIndex];\n      const ns = model.namespace;\n      const nsIndex = namespaces.get(ns);\n      // Multi updates are not retryable.\n      if (model.name === 'deleteMany' || model.name === 'updateMany') {\n        this.isBatchRetryable = false;\n      }\n      if (nsIndex != null) {\n        // Build the operation and serialize it to get the bytes buffer.\n        const operation = buildOperation(model, nsIndex, this.pkFactory);\n        let operationBuffer;\n        try {\n          operationBuffer = bson_1.BSON.serialize(operation);\n        } catch (cause) {\n          throw new error_1.MongoInvalidArgumentError(`Could not serialize operation to BSON`, {\n            cause\n          });\n        }\n        validateBufferSize('ops', operationBuffer, maxBsonObjectSize);\n        // Check if the operation buffer can fit in the command. If it can,\n        // then add the operation to the document sequence and increment the\n        // current length as long as the ops don't exceed the maxWriteBatchSize.\n        if (commandLength + operationBuffer.length < maxMessageSizeBytes && command.ops.documents.length < maxWriteBatchSize) {\n          // Pushing to the ops document sequence returns the total byte length of the document sequence.\n          commandLength = MESSAGE_OVERHEAD_BYTES + command.ops.push(operation, operationBuffer);\n          // Increment the builder's current model index.\n          this.currentModelIndex++;\n        } else {\n          // The operation cannot fit in the current command and will need to\n          // go in the next batch. Exit the loop.\n          break;\n        }\n      } else {\n        // The namespace is not already in the nsInfo so we will set it in the map, and\n        // construct our nsInfo and ops documents and buffers.\n        namespaces.set(ns, currentNamespaceIndex);\n        const nsInfo = {\n          ns: ns\n        };\n        const operation = buildOperation(model, currentNamespaceIndex, this.pkFactory);\n        let nsInfoBuffer;\n        let operationBuffer;\n        try {\n          nsInfoBuffer = bson_1.BSON.serialize(nsInfo);\n          operationBuffer = bson_1.BSON.serialize(operation);\n        } catch (cause) {\n          throw new error_1.MongoInvalidArgumentError(`Could not serialize ns info to BSON`, {\n            cause\n          });\n        }\n        validateBufferSize('nsInfo', nsInfoBuffer, maxBsonObjectSize);\n        validateBufferSize('ops', operationBuffer, maxBsonObjectSize);\n        // Check if the operation and nsInfo buffers can fit in the command. If they\n        // can, then add the operation and nsInfo to their respective document\n        // sequences and increment the current length as long as the ops don't exceed\n        // the maxWriteBatchSize.\n        if (commandLength + nsInfoBuffer.length + operationBuffer.length < maxMessageSizeBytes && command.ops.documents.length < maxWriteBatchSize) {\n          // Pushing to the ops document sequence returns the total byte length of the document sequence.\n          commandLength = MESSAGE_OVERHEAD_BYTES + command.nsInfo.push(nsInfo, nsInfoBuffer) + command.ops.push(operation, operationBuffer);\n          // We've added a new namespace, increment the namespace index.\n          currentNamespaceIndex++;\n          // Increment the builder's current model index.\n          this.currentModelIndex++;\n        } else {\n          // The operation cannot fit in the current command and will need to\n          // go in the next batch. Exit the loop.\n          break;\n        }\n      }\n    }\n    // Set the last operations and return the command.\n    this.lastOperations = command.ops.documents;\n    return command;\n  }\n  baseCommand() {\n    const command = {\n      bulkWrite: 1,\n      errorsOnly: this.errorsOnly,\n      ordered: this.options.ordered ?? true,\n      ops: new commands_1.DocumentSequence('ops'),\n      nsInfo: new commands_1.DocumentSequence('nsInfo')\n    };\n    // Add bypassDocumentValidation if it was present in the options.\n    if (this.options.bypassDocumentValidation != null) {\n      command.bypassDocumentValidation = this.options.bypassDocumentValidation;\n    }\n    // Add let if it was present in the options.\n    if (this.options.let) {\n      command.let = this.options.let;\n    }\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (this.options.comment !== undefined) {\n      command.comment = this.options.comment;\n    }\n    return command;\n  }\n}\nexports.ClientBulkWriteCommandBuilder = ClientBulkWriteCommandBuilder;\nfunction validateBufferSize(name, buffer, maxBsonObjectSize) {\n  if (buffer.length > maxBsonObjectSize) {\n    throw new error_1.MongoInvalidArgumentError(`Client bulk write operation ${name} of length ${buffer.length} exceeds the max bson object size of ${maxBsonObjectSize}`);\n  }\n}\n/**\n * Build the insert one operation.\n * @param model - The insert one model.\n * @param index - The namespace index.\n * @returns the operation.\n */\nconst buildInsertOneOperation = (model, index, pkFactory) => {\n  const document = {\n    insert: index,\n    document: model.document\n  };\n  document.document._id = model.document._id ?? pkFactory.createPk();\n  return document;\n};\nexports.buildInsertOneOperation = buildInsertOneOperation;\n/**\n * Build the delete one operation.\n * @param model - The insert many model.\n * @param index - The namespace index.\n * @returns the operation.\n */\nconst buildDeleteOneOperation = (model, index) => {\n  return createDeleteOperation(model, index, false);\n};\nexports.buildDeleteOneOperation = buildDeleteOneOperation;\n/**\n * Build the delete many operation.\n * @param model - The delete many model.\n * @param index - The namespace index.\n * @returns the operation.\n */\nconst buildDeleteManyOperation = (model, index) => {\n  return createDeleteOperation(model, index, true);\n};\nexports.buildDeleteManyOperation = buildDeleteManyOperation;\n/**\n * Creates a delete operation based on the parameters.\n */\nfunction createDeleteOperation(model, index, multi) {\n  const document = {\n    delete: index,\n    multi: multi,\n    filter: model.filter\n  };\n  if (model.hint) {\n    document.hint = model.hint;\n  }\n  if (model.collation) {\n    document.collation = model.collation;\n  }\n  return document;\n}\n/**\n * Build the update one operation.\n * @param model - The update one model.\n * @param index - The namespace index.\n * @returns the operation.\n */\nconst buildUpdateOneOperation = (model, index) => {\n  return createUpdateOperation(model, index, false);\n};\nexports.buildUpdateOneOperation = buildUpdateOneOperation;\n/**\n * Build the update many operation.\n * @param model - The update many model.\n * @param index - The namespace index.\n * @returns the operation.\n */\nconst buildUpdateManyOperation = (model, index) => {\n  return createUpdateOperation(model, index, true);\n};\nexports.buildUpdateManyOperation = buildUpdateManyOperation;\n/**\n * Validate the update document.\n * @param update - The update document.\n */\nfunction validateUpdate(update) {\n  if (!(0, utils_1.hasAtomicOperators)(update)) {\n    throw new error_1.MongoAPIError('Client bulk write update models must only contain atomic modifiers (start with $) and must not be empty.');\n  }\n}\n/**\n * Creates a delete operation based on the parameters.\n */\nfunction createUpdateOperation(model, index, multi) {\n  // Update documents provided in UpdateOne and UpdateMany write models are\n  // required only to contain atomic modifiers (i.e. keys that start with \"$\").\n  // Drivers MUST throw an error if an update document is empty or if the\n  // document's first key does not start with \"$\".\n  validateUpdate(model.update);\n  const document = {\n    update: index,\n    multi: multi,\n    filter: model.filter,\n    updateMods: model.update\n  };\n  if (model.hint) {\n    document.hint = model.hint;\n  }\n  if (model.upsert) {\n    document.upsert = model.upsert;\n  }\n  if (model.arrayFilters) {\n    document.arrayFilters = model.arrayFilters;\n  }\n  if (model.collation) {\n    document.collation = model.collation;\n  }\n  return document;\n}\n/**\n * Build the replace one operation.\n * @param model - The replace one model.\n * @param index - The namespace index.\n * @returns the operation.\n */\nconst buildReplaceOneOperation = (model, index) => {\n  if ((0, utils_1.hasAtomicOperators)(model.replacement)) {\n    throw new error_1.MongoAPIError('Client bulk write replace models must not contain atomic modifiers (start with $) and must not be empty.');\n  }\n  const document = {\n    update: index,\n    multi: false,\n    filter: model.filter,\n    updateMods: model.replacement\n  };\n  if (model.hint) {\n    document.hint = model.hint;\n  }\n  if (model.upsert) {\n    document.upsert = model.upsert;\n  }\n  if (model.collation) {\n    document.collation = model.collation;\n  }\n  return document;\n};\nexports.buildReplaceOneOperation = buildReplaceOneOperation;\n/** @internal */\nfunction buildOperation(model, index, pkFactory) {\n  switch (model.name) {\n    case 'insertOne':\n      return (0, exports.buildInsertOneOperation)(model, index, pkFactory);\n    case 'deleteOne':\n      return (0, exports.buildDeleteOneOperation)(model, index);\n    case 'deleteMany':\n      return (0, exports.buildDeleteManyOperation)(model, index);\n    case 'updateOne':\n      return (0, exports.buildUpdateOneOperation)(model, index);\n    case 'updateMany':\n      return (0, exports.buildUpdateManyOperation)(model, index);\n    case 'replaceOne':\n      return (0, exports.buildReplaceOneOperation)(model, index);\n  }\n}", "map": {"version": 3, "names": ["exports", "buildOperation", "bson_1", "require", "commands_1", "error_1", "utils_1", "MESSAGE_OVERHEAD_BYTES", "ClientBulkWriteCommandBuilder", "constructor", "models", "options", "pkFactory", "DEFAULT_PK_FACTORY", "currentModelIndex", "previousModelIndex", "lastOperations", "isBatchRetryable", "errorsOnly", "verboseResults", "hasNextBatch", "length", "resetBatch", "buildBatch", "maxMessageSizeBytes", "maxWriteBatchSize", "maxBsonObjectSize", "commandLength", "currentNamespaceIndex", "command", "baseCommand", "namespaces", "Map", "model", "ns", "namespace", "nsIndex", "get", "name", "operation", "operationBuffer", "BSON", "serialize", "cause", "MongoInvalidArgumentError", "validateBufferSize", "ops", "documents", "push", "set", "nsInfo", "nsInfoBuffer", "bulkWrite", "ordered", "DocumentSequence", "bypassDocumentValidation", "let", "comment", "undefined", "buffer", "buildInsertOneOperation", "index", "document", "insert", "_id", "createPk", "buildDeleteOneOperation", "createDeleteOperation", "buildDeleteManyOperation", "multi", "delete", "filter", "hint", "collation", "buildUpdateOneOperation", "createUpdateOperation", "buildUpdateManyOperation", "validateUpdate", "update", "hasAtomicOperators", "MongoAPIError", "updateMods", "upsert", "arrayFilters", "buildReplaceOneOperation", "replacement"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\client_bulk_write\\command_builder.ts"], "sourcesContent": ["import { BSON, type Document } from '../../bson';\nimport { DocumentSequence } from '../../cmap/commands';\nimport { MongoAPIError, MongoInvalidArgumentError } from '../../error';\nimport { type PkFactory } from '../../mongo_client';\nimport type { Filter, OptionalId, UpdateFilter, WithoutId } from '../../mongo_types';\nimport { DEFAULT_PK_FACTORY, hasAtomicOperators } from '../../utils';\nimport { type CollationOptions } from '../command';\nimport { type Hint } from '../operation';\nimport type {\n  AnyClientBulkWriteModel,\n  ClientBulkWriteOptions,\n  ClientDeleteManyModel,\n  ClientDeleteOneModel,\n  ClientInsertOneModel,\n  ClientReplaceOneModel,\n  ClientUpdateManyModel,\n  ClientUpdateOneModel\n} from './common';\n\n/** @internal */\nexport interface ClientBulkWriteCommand {\n  bulkWrite: 1;\n  errorsOnly: boolean;\n  ordered: boolean;\n  ops: DocumentSequence;\n  nsInfo: DocumentSequence;\n  bypassDocumentValidation?: boolean;\n  let?: Document;\n  comment?: any;\n}\n\n/**\n * The bytes overhead for the extra fields added post command generation.\n */\nconst MESSAGE_OVERHEAD_BYTES = 1000;\n\n/** @internal */\nexport class ClientBulkWriteCommandBuilder {\n  models: ReadonlyArray<AnyClientBulkWriteModel<Document>>;\n  options: ClientBulkWriteOptions;\n  pkFactory: PkFactory;\n  /** The current index in the models array that is being processed. */\n  currentModelIndex: number;\n  /** The model index that the builder was on when it finished the previous batch. Used for resets when retrying. */\n  previousModelIndex: number;\n  /** The last array of operations that were created. Used by the results merger for indexing results. */\n  lastOperations: Document[];\n  /** Returns true if the current batch being created has no multi-updates. */\n  isBatchRetryable: boolean;\n\n  /**\n   * Create the command builder.\n   * @param models - The client write models.\n   */\n  constructor(\n    models: ReadonlyArray<AnyClientBulkWriteModel<Document>>,\n    options: ClientBulkWriteOptions,\n    pkFactory?: PkFactory\n  ) {\n    this.models = models;\n    this.options = options;\n    this.pkFactory = pkFactory ?? DEFAULT_PK_FACTORY;\n    this.currentModelIndex = 0;\n    this.previousModelIndex = 0;\n    this.lastOperations = [];\n    this.isBatchRetryable = true;\n  }\n\n  /**\n   * Gets the errorsOnly value for the command, which is the inverse of the\n   * user provided verboseResults option. Defaults to true.\n   */\n  get errorsOnly(): boolean {\n    if ('verboseResults' in this.options) {\n      return !this.options.verboseResults;\n    }\n    return true;\n  }\n\n  /**\n   * Determines if there is another batch to process.\n   * @returns True if not all batches have been built.\n   */\n  hasNextBatch(): boolean {\n    return this.currentModelIndex < this.models.length;\n  }\n\n  /**\n   * When we need to retry a command we need to set the current\n   * model index back to its previous value.\n   */\n  resetBatch(): boolean {\n    this.currentModelIndex = this.previousModelIndex;\n    return true;\n  }\n\n  /**\n   * Build a single batch of a client bulk write command.\n   * @param maxMessageSizeBytes - The max message size in bytes.\n   * @param maxWriteBatchSize - The max write batch size.\n   * @returns The client bulk write command.\n   */\n  buildBatch(\n    maxMessageSizeBytes: number,\n    maxWriteBatchSize: number,\n    maxBsonObjectSize: number\n  ): ClientBulkWriteCommand {\n    // We start by assuming the batch has no multi-updates, so it is retryable\n    // until we find them.\n    this.isBatchRetryable = true;\n    let commandLength = 0;\n    let currentNamespaceIndex = 0;\n    const command: ClientBulkWriteCommand = this.baseCommand();\n    const namespaces = new Map<string, number>();\n    // In the case of retries we need to mark where we started this batch.\n    this.previousModelIndex = this.currentModelIndex;\n\n    while (this.currentModelIndex < this.models.length) {\n      const model = this.models[this.currentModelIndex];\n      const ns = model.namespace;\n      const nsIndex = namespaces.get(ns);\n\n      // Multi updates are not retryable.\n      if (model.name === 'deleteMany' || model.name === 'updateMany') {\n        this.isBatchRetryable = false;\n      }\n\n      if (nsIndex != null) {\n        // Build the operation and serialize it to get the bytes buffer.\n        const operation = buildOperation(model, nsIndex, this.pkFactory);\n        let operationBuffer;\n        try {\n          operationBuffer = BSON.serialize(operation);\n        } catch (cause) {\n          throw new MongoInvalidArgumentError(`Could not serialize operation to BSON`, { cause });\n        }\n\n        validateBufferSize('ops', operationBuffer, maxBsonObjectSize);\n\n        // Check if the operation buffer can fit in the command. If it can,\n        // then add the operation to the document sequence and increment the\n        // current length as long as the ops don't exceed the maxWriteBatchSize.\n        if (\n          commandLength + operationBuffer.length < maxMessageSizeBytes &&\n          command.ops.documents.length < maxWriteBatchSize\n        ) {\n          // Pushing to the ops document sequence returns the total byte length of the document sequence.\n          commandLength = MESSAGE_OVERHEAD_BYTES + command.ops.push(operation, operationBuffer);\n          // Increment the builder's current model index.\n          this.currentModelIndex++;\n        } else {\n          // The operation cannot fit in the current command and will need to\n          // go in the next batch. Exit the loop.\n          break;\n        }\n      } else {\n        // The namespace is not already in the nsInfo so we will set it in the map, and\n        // construct our nsInfo and ops documents and buffers.\n        namespaces.set(ns, currentNamespaceIndex);\n        const nsInfo = { ns: ns };\n        const operation = buildOperation(model, currentNamespaceIndex, this.pkFactory);\n        let nsInfoBuffer;\n        let operationBuffer;\n        try {\n          nsInfoBuffer = BSON.serialize(nsInfo);\n          operationBuffer = BSON.serialize(operation);\n        } catch (cause) {\n          throw new MongoInvalidArgumentError(`Could not serialize ns info to BSON`, { cause });\n        }\n\n        validateBufferSize('nsInfo', nsInfoBuffer, maxBsonObjectSize);\n        validateBufferSize('ops', operationBuffer, maxBsonObjectSize);\n\n        // Check if the operation and nsInfo buffers can fit in the command. If they\n        // can, then add the operation and nsInfo to their respective document\n        // sequences and increment the current length as long as the ops don't exceed\n        // the maxWriteBatchSize.\n        if (\n          commandLength + nsInfoBuffer.length + operationBuffer.length < maxMessageSizeBytes &&\n          command.ops.documents.length < maxWriteBatchSize\n        ) {\n          // Pushing to the ops document sequence returns the total byte length of the document sequence.\n          commandLength =\n            MESSAGE_OVERHEAD_BYTES +\n            command.nsInfo.push(nsInfo, nsInfoBuffer) +\n            command.ops.push(operation, operationBuffer);\n          // We've added a new namespace, increment the namespace index.\n          currentNamespaceIndex++;\n          // Increment the builder's current model index.\n          this.currentModelIndex++;\n        } else {\n          // The operation cannot fit in the current command and will need to\n          // go in the next batch. Exit the loop.\n          break;\n        }\n      }\n    }\n    // Set the last operations and return the command.\n    this.lastOperations = command.ops.documents;\n    return command;\n  }\n\n  private baseCommand(): ClientBulkWriteCommand {\n    const command: ClientBulkWriteCommand = {\n      bulkWrite: 1,\n      errorsOnly: this.errorsOnly,\n      ordered: this.options.ordered ?? true,\n      ops: new DocumentSequence('ops'),\n      nsInfo: new DocumentSequence('nsInfo')\n    };\n    // Add bypassDocumentValidation if it was present in the options.\n    if (this.options.bypassDocumentValidation != null) {\n      command.bypassDocumentValidation = this.options.bypassDocumentValidation;\n    }\n    // Add let if it was present in the options.\n    if (this.options.let) {\n      command.let = this.options.let;\n    }\n\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (this.options.comment !== undefined) {\n      command.comment = this.options.comment;\n    }\n\n    return command;\n  }\n}\n\nfunction validateBufferSize(name: string, buffer: Uint8Array, maxBsonObjectSize: number) {\n  if (buffer.length > maxBsonObjectSize) {\n    throw new MongoInvalidArgumentError(\n      `Client bulk write operation ${name} of length ${buffer.length} exceeds the max bson object size of ${maxBsonObjectSize}`\n    );\n  }\n}\n\n/** @internal */\ninterface ClientInsertOperation {\n  insert: number;\n  document: OptionalId<Document>;\n}\n\n/**\n * Build the insert one operation.\n * @param model - The insert one model.\n * @param index - The namespace index.\n * @returns the operation.\n */\nexport const buildInsertOneOperation = (\n  model: ClientInsertOneModel<Document>,\n  index: number,\n  pkFactory: PkFactory\n): ClientInsertOperation => {\n  const document: ClientInsertOperation = {\n    insert: index,\n    document: model.document\n  };\n  document.document._id = model.document._id ?? pkFactory.createPk();\n  return document;\n};\n\n/** @internal */\nexport interface ClientDeleteOperation {\n  delete: number;\n  multi: boolean;\n  filter: Filter<Document>;\n  hint?: Hint;\n  collation?: CollationOptions;\n}\n\n/**\n * Build the delete one operation.\n * @param model - The insert many model.\n * @param index - The namespace index.\n * @returns the operation.\n */\nexport const buildDeleteOneOperation = (\n  model: ClientDeleteOneModel<Document>,\n  index: number\n): Document => {\n  return createDeleteOperation(model, index, false);\n};\n\n/**\n * Build the delete many operation.\n * @param model - The delete many model.\n * @param index - The namespace index.\n * @returns the operation.\n */\nexport const buildDeleteManyOperation = (\n  model: ClientDeleteManyModel<Document>,\n  index: number\n): Document => {\n  return createDeleteOperation(model, index, true);\n};\n\n/**\n * Creates a delete operation based on the parameters.\n */\nfunction createDeleteOperation(\n  model: ClientDeleteOneModel<Document> | ClientDeleteManyModel<Document>,\n  index: number,\n  multi: boolean\n): ClientDeleteOperation {\n  const document: ClientDeleteOperation = {\n    delete: index,\n    multi: multi,\n    filter: model.filter\n  };\n  if (model.hint) {\n    document.hint = model.hint;\n  }\n  if (model.collation) {\n    document.collation = model.collation;\n  }\n  return document;\n}\n\n/** @internal */\nexport interface ClientUpdateOperation {\n  update: number;\n  multi: boolean;\n  filter: Filter<Document>;\n  updateMods: UpdateFilter<Document> | Document[];\n  hint?: Hint;\n  upsert?: boolean;\n  arrayFilters?: Document[];\n  collation?: CollationOptions;\n}\n\n/**\n * Build the update one operation.\n * @param model - The update one model.\n * @param index - The namespace index.\n * @returns the operation.\n */\nexport const buildUpdateOneOperation = (\n  model: ClientUpdateOneModel<Document>,\n  index: number\n): ClientUpdateOperation => {\n  return createUpdateOperation(model, index, false);\n};\n\n/**\n * Build the update many operation.\n * @param model - The update many model.\n * @param index - The namespace index.\n * @returns the operation.\n */\nexport const buildUpdateManyOperation = (\n  model: ClientUpdateManyModel<Document>,\n  index: number\n): ClientUpdateOperation => {\n  return createUpdateOperation(model, index, true);\n};\n\n/**\n * Validate the update document.\n * @param update - The update document.\n */\nfunction validateUpdate(update: Document) {\n  if (!hasAtomicOperators(update)) {\n    throw new MongoAPIError(\n      'Client bulk write update models must only contain atomic modifiers (start with $) and must not be empty.'\n    );\n  }\n}\n\n/**\n * Creates a delete operation based on the parameters.\n */\nfunction createUpdateOperation(\n  model: ClientUpdateOneModel<Document> | ClientUpdateManyModel<Document>,\n  index: number,\n  multi: boolean\n): ClientUpdateOperation {\n  // Update documents provided in UpdateOne and UpdateMany write models are\n  // required only to contain atomic modifiers (i.e. keys that start with \"$\").\n  // Drivers MUST throw an error if an update document is empty or if the\n  // document's first key does not start with \"$\".\n  validateUpdate(model.update);\n  const document: ClientUpdateOperation = {\n    update: index,\n    multi: multi,\n    filter: model.filter,\n    updateMods: model.update\n  };\n  if (model.hint) {\n    document.hint = model.hint;\n  }\n  if (model.upsert) {\n    document.upsert = model.upsert;\n  }\n  if (model.arrayFilters) {\n    document.arrayFilters = model.arrayFilters;\n  }\n  if (model.collation) {\n    document.collation = model.collation;\n  }\n  return document;\n}\n\n/** @internal */\nexport interface ClientReplaceOneOperation {\n  update: number;\n  multi: boolean;\n  filter: Filter<Document>;\n  updateMods: WithoutId<Document>;\n  hint?: Hint;\n  upsert?: boolean;\n  collation?: CollationOptions;\n}\n\n/**\n * Build the replace one operation.\n * @param model - The replace one model.\n * @param index - The namespace index.\n * @returns the operation.\n */\nexport const buildReplaceOneOperation = (\n  model: ClientReplaceOneModel<Document>,\n  index: number\n): ClientReplaceOneOperation => {\n  if (hasAtomicOperators(model.replacement)) {\n    throw new MongoAPIError(\n      'Client bulk write replace models must not contain atomic modifiers (start with $) and must not be empty.'\n    );\n  }\n\n  const document: ClientReplaceOneOperation = {\n    update: index,\n    multi: false,\n    filter: model.filter,\n    updateMods: model.replacement\n  };\n  if (model.hint) {\n    document.hint = model.hint;\n  }\n  if (model.upsert) {\n    document.upsert = model.upsert;\n  }\n  if (model.collation) {\n    document.collation = model.collation;\n  }\n  return document;\n};\n\n/** @internal */\nexport function buildOperation(\n  model: AnyClientBulkWriteModel<Document>,\n  index: number,\n  pkFactory: PkFactory\n): Document {\n  switch (model.name) {\n    case 'insertOne':\n      return buildInsertOneOperation(model, index, pkFactory);\n    case 'deleteOne':\n      return buildDeleteOneOperation(model, index);\n    case 'deleteMany':\n      return buildDeleteManyOperation(model, index);\n    case 'updateOne':\n      return buildUpdateOneOperation(model, index);\n    case 'updateMany':\n      return buildUpdateManyOperation(model, index);\n    case 'replaceOne':\n      return buildReplaceOneOperation(model, index);\n  }\n}\n"], "mappings": ";;;;;;AAicAA,OAAA,CAAAC,cAAA,GAAAA,cAAA;AAjcA,MAAAC,MAAA,GAAAC,OAAA;AACA,MAAAC,UAAA,GAAAD,OAAA;AACA,MAAAE,OAAA,GAAAF,OAAA;AAGA,MAAAG,OAAA,GAAAH,OAAA;AA0BA;;;AAGA,MAAMI,sBAAsB,GAAG,IAAI;AAEnC;AACA,MAAaC,6BAA6B;EAaxC;;;;EAIAC,YACEC,MAAwD,EACxDC,OAA+B,EAC/BC,SAAqB;IAErB,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,SAAS,GAAGA,SAAS,IAAIN,OAAA,CAAAO,kBAAkB;IAChD,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACC,cAAc,GAAG,EAAE;IACxB,IAAI,CAACC,gBAAgB,GAAG,IAAI;EAC9B;EAEA;;;;EAIA,IAAIC,UAAUA,CAAA;IACZ,IAAI,gBAAgB,IAAI,IAAI,CAACP,OAAO,EAAE;MACpC,OAAO,CAAC,IAAI,CAACA,OAAO,CAACQ,cAAc;IACrC;IACA,OAAO,IAAI;EACb;EAEA;;;;EAIAC,YAAYA,CAAA;IACV,OAAO,IAAI,CAACN,iBAAiB,GAAG,IAAI,CAACJ,MAAM,CAACW,MAAM;EACpD;EAEA;;;;EAIAC,UAAUA,CAAA;IACR,IAAI,CAACR,iBAAiB,GAAG,IAAI,CAACC,kBAAkB;IAChD,OAAO,IAAI;EACb;EAEA;;;;;;EAMAQ,UAAUA,CACRC,mBAA2B,EAC3BC,iBAAyB,EACzBC,iBAAyB;IAEzB;IACA;IACA,IAAI,CAACT,gBAAgB,GAAG,IAAI;IAC5B,IAAIU,aAAa,GAAG,CAAC;IACrB,IAAIC,qBAAqB,GAAG,CAAC;IAC7B,MAAMC,OAAO,GAA2B,IAAI,CAACC,WAAW,EAAE;IAC1D,MAAMC,UAAU,GAAG,IAAIC,GAAG,EAAkB;IAC5C;IACA,IAAI,CAACjB,kBAAkB,GAAG,IAAI,CAACD,iBAAiB;IAEhD,OAAO,IAAI,CAACA,iBAAiB,GAAG,IAAI,CAACJ,MAAM,CAACW,MAAM,EAAE;MAClD,MAAMY,KAAK,GAAG,IAAI,CAACvB,MAAM,CAAC,IAAI,CAACI,iBAAiB,CAAC;MACjD,MAAMoB,EAAE,GAAGD,KAAK,CAACE,SAAS;MAC1B,MAAMC,OAAO,GAAGL,UAAU,CAACM,GAAG,CAACH,EAAE,CAAC;MAElC;MACA,IAAID,KAAK,CAACK,IAAI,KAAK,YAAY,IAAIL,KAAK,CAACK,IAAI,KAAK,YAAY,EAAE;QAC9D,IAAI,CAACrB,gBAAgB,GAAG,KAAK;MAC/B;MAEA,IAAImB,OAAO,IAAI,IAAI,EAAE;QACnB;QACA,MAAMG,SAAS,GAAGtC,cAAc,CAACgC,KAAK,EAAEG,OAAO,EAAE,IAAI,CAACxB,SAAS,CAAC;QAChE,IAAI4B,eAAe;QACnB,IAAI;UACFA,eAAe,GAAGtC,MAAA,CAAAuC,IAAI,CAACC,SAAS,CAACH,SAAS,CAAC;QAC7C,CAAC,CAAC,OAAOI,KAAK,EAAE;UACd,MAAM,IAAItC,OAAA,CAAAuC,yBAAyB,CAAC,uCAAuC,EAAE;YAAED;UAAK,CAAE,CAAC;QACzF;QAEAE,kBAAkB,CAAC,KAAK,EAAEL,eAAe,EAAEd,iBAAiB,CAAC;QAE7D;QACA;QACA;QACA,IACEC,aAAa,GAAGa,eAAe,CAACnB,MAAM,GAAGG,mBAAmB,IAC5DK,OAAO,CAACiB,GAAG,CAACC,SAAS,CAAC1B,MAAM,GAAGI,iBAAiB,EAChD;UACA;UACAE,aAAa,GAAGpB,sBAAsB,GAAGsB,OAAO,CAACiB,GAAG,CAACE,IAAI,CAACT,SAAS,EAAEC,eAAe,CAAC;UACrF;UACA,IAAI,CAAC1B,iBAAiB,EAAE;QAC1B,CAAC,MAAM;UACL;UACA;UACA;QACF;MACF,CAAC,MAAM;QACL;QACA;QACAiB,UAAU,CAACkB,GAAG,CAACf,EAAE,EAAEN,qBAAqB,CAAC;QACzC,MAAMsB,MAAM,GAAG;UAAEhB,EAAE,EAAEA;QAAE,CAAE;QACzB,MAAMK,SAAS,GAAGtC,cAAc,CAACgC,KAAK,EAAEL,qBAAqB,EAAE,IAAI,CAAChB,SAAS,CAAC;QAC9E,IAAIuC,YAAY;QAChB,IAAIX,eAAe;QACnB,IAAI;UACFW,YAAY,GAAGjD,MAAA,CAAAuC,IAAI,CAACC,SAAS,CAACQ,MAAM,CAAC;UACrCV,eAAe,GAAGtC,MAAA,CAAAuC,IAAI,CAACC,SAAS,CAACH,SAAS,CAAC;QAC7C,CAAC,CAAC,OAAOI,KAAK,EAAE;UACd,MAAM,IAAItC,OAAA,CAAAuC,yBAAyB,CAAC,qCAAqC,EAAE;YAAED;UAAK,CAAE,CAAC;QACvF;QAEAE,kBAAkB,CAAC,QAAQ,EAAEM,YAAY,EAAEzB,iBAAiB,CAAC;QAC7DmB,kBAAkB,CAAC,KAAK,EAAEL,eAAe,EAAEd,iBAAiB,CAAC;QAE7D;QACA;QACA;QACA;QACA,IACEC,aAAa,GAAGwB,YAAY,CAAC9B,MAAM,GAAGmB,eAAe,CAACnB,MAAM,GAAGG,mBAAmB,IAClFK,OAAO,CAACiB,GAAG,CAACC,SAAS,CAAC1B,MAAM,GAAGI,iBAAiB,EAChD;UACA;UACAE,aAAa,GACXpB,sBAAsB,GACtBsB,OAAO,CAACqB,MAAM,CAACF,IAAI,CAACE,MAAM,EAAEC,YAAY,CAAC,GACzCtB,OAAO,CAACiB,GAAG,CAACE,IAAI,CAACT,SAAS,EAAEC,eAAe,CAAC;UAC9C;UACAZ,qBAAqB,EAAE;UACvB;UACA,IAAI,CAACd,iBAAiB,EAAE;QAC1B,CAAC,MAAM;UACL;UACA;UACA;QACF;MACF;IACF;IACA;IACA,IAAI,CAACE,cAAc,GAAGa,OAAO,CAACiB,GAAG,CAACC,SAAS;IAC3C,OAAOlB,OAAO;EAChB;EAEQC,WAAWA,CAAA;IACjB,MAAMD,OAAO,GAA2B;MACtCuB,SAAS,EAAE,CAAC;MACZlC,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BmC,OAAO,EAAE,IAAI,CAAC1C,OAAO,CAAC0C,OAAO,IAAI,IAAI;MACrCP,GAAG,EAAE,IAAI1C,UAAA,CAAAkD,gBAAgB,CAAC,KAAK,CAAC;MAChCJ,MAAM,EAAE,IAAI9C,UAAA,CAAAkD,gBAAgB,CAAC,QAAQ;KACtC;IACD;IACA,IAAI,IAAI,CAAC3C,OAAO,CAAC4C,wBAAwB,IAAI,IAAI,EAAE;MACjD1B,OAAO,CAAC0B,wBAAwB,GAAG,IAAI,CAAC5C,OAAO,CAAC4C,wBAAwB;IAC1E;IACA;IACA,IAAI,IAAI,CAAC5C,OAAO,CAAC6C,GAAG,EAAE;MACpB3B,OAAO,CAAC2B,GAAG,GAAG,IAAI,CAAC7C,OAAO,CAAC6C,GAAG;IAChC;IAEA;IACA;IACA,IAAI,IAAI,CAAC7C,OAAO,CAAC8C,OAAO,KAAKC,SAAS,EAAE;MACtC7B,OAAO,CAAC4B,OAAO,GAAG,IAAI,CAAC9C,OAAO,CAAC8C,OAAO;IACxC;IAEA,OAAO5B,OAAO;EAChB;;AA7LF7B,OAAA,CAAAQ,6BAAA,GAAAA,6BAAA;AAgMA,SAASqC,kBAAkBA,CAACP,IAAY,EAAEqB,MAAkB,EAAEjC,iBAAyB;EACrF,IAAIiC,MAAM,CAACtC,MAAM,GAAGK,iBAAiB,EAAE;IACrC,MAAM,IAAIrB,OAAA,CAAAuC,yBAAyB,CACjC,+BAA+BN,IAAI,cAAcqB,MAAM,CAACtC,MAAM,wCAAwCK,iBAAiB,EAAE,CAC1H;EACH;AACF;AAQA;;;;;;AAMO,MAAMkC,uBAAuB,GAAGA,CACrC3B,KAAqC,EACrC4B,KAAa,EACbjD,SAAoB,KACK;EACzB,MAAMkD,QAAQ,GAA0B;IACtCC,MAAM,EAAEF,KAAK;IACbC,QAAQ,EAAE7B,KAAK,CAAC6B;GACjB;EACDA,QAAQ,CAACA,QAAQ,CAACE,GAAG,GAAG/B,KAAK,CAAC6B,QAAQ,CAACE,GAAG,IAAIpD,SAAS,CAACqD,QAAQ,EAAE;EAClE,OAAOH,QAAQ;AACjB,CAAC;AAXY9D,OAAA,CAAA4D,uBAAuB,GAAAA,uBAAA;AAsBpC;;;;;;AAMO,MAAMM,uBAAuB,GAAGA,CACrCjC,KAAqC,EACrC4B,KAAa,KACD;EACZ,OAAOM,qBAAqB,CAAClC,KAAK,EAAE4B,KAAK,EAAE,KAAK,CAAC;AACnD,CAAC;AALY7D,OAAA,CAAAkE,uBAAuB,GAAAA,uBAAA;AAOpC;;;;;;AAMO,MAAME,wBAAwB,GAAGA,CACtCnC,KAAsC,EACtC4B,KAAa,KACD;EACZ,OAAOM,qBAAqB,CAAClC,KAAK,EAAE4B,KAAK,EAAE,IAAI,CAAC;AAClD,CAAC;AALY7D,OAAA,CAAAoE,wBAAwB,GAAAA,wBAAA;AAOrC;;;AAGA,SAASD,qBAAqBA,CAC5BlC,KAAuE,EACvE4B,KAAa,EACbQ,KAAc;EAEd,MAAMP,QAAQ,GAA0B;IACtCQ,MAAM,EAAET,KAAK;IACbQ,KAAK,EAAEA,KAAK;IACZE,MAAM,EAAEtC,KAAK,CAACsC;GACf;EACD,IAAItC,KAAK,CAACuC,IAAI,EAAE;IACdV,QAAQ,CAACU,IAAI,GAAGvC,KAAK,CAACuC,IAAI;EAC5B;EACA,IAAIvC,KAAK,CAACwC,SAAS,EAAE;IACnBX,QAAQ,CAACW,SAAS,GAAGxC,KAAK,CAACwC,SAAS;EACtC;EACA,OAAOX,QAAQ;AACjB;AAcA;;;;;;AAMO,MAAMY,uBAAuB,GAAGA,CACrCzC,KAAqC,EACrC4B,KAAa,KACY;EACzB,OAAOc,qBAAqB,CAAC1C,KAAK,EAAE4B,KAAK,EAAE,KAAK,CAAC;AACnD,CAAC;AALY7D,OAAA,CAAA0E,uBAAuB,GAAAA,uBAAA;AAOpC;;;;;;AAMO,MAAME,wBAAwB,GAAGA,CACtC3C,KAAsC,EACtC4B,KAAa,KACY;EACzB,OAAOc,qBAAqB,CAAC1C,KAAK,EAAE4B,KAAK,EAAE,IAAI,CAAC;AAClD,CAAC;AALY7D,OAAA,CAAA4E,wBAAwB,GAAAA,wBAAA;AAOrC;;;;AAIA,SAASC,cAAcA,CAACC,MAAgB;EACtC,IAAI,CAAC,IAAAxE,OAAA,CAAAyE,kBAAkB,EAACD,MAAM,CAAC,EAAE;IAC/B,MAAM,IAAIzE,OAAA,CAAA2E,aAAa,CACrB,0GAA0G,CAC3G;EACH;AACF;AAEA;;;AAGA,SAASL,qBAAqBA,CAC5B1C,KAAuE,EACvE4B,KAAa,EACbQ,KAAc;EAEd;EACA;EACA;EACA;EACAQ,cAAc,CAAC5C,KAAK,CAAC6C,MAAM,CAAC;EAC5B,MAAMhB,QAAQ,GAA0B;IACtCgB,MAAM,EAAEjB,KAAK;IACbQ,KAAK,EAAEA,KAAK;IACZE,MAAM,EAAEtC,KAAK,CAACsC,MAAM;IACpBU,UAAU,EAAEhD,KAAK,CAAC6C;GACnB;EACD,IAAI7C,KAAK,CAACuC,IAAI,EAAE;IACdV,QAAQ,CAACU,IAAI,GAAGvC,KAAK,CAACuC,IAAI;EAC5B;EACA,IAAIvC,KAAK,CAACiD,MAAM,EAAE;IAChBpB,QAAQ,CAACoB,MAAM,GAAGjD,KAAK,CAACiD,MAAM;EAChC;EACA,IAAIjD,KAAK,CAACkD,YAAY,EAAE;IACtBrB,QAAQ,CAACqB,YAAY,GAAGlD,KAAK,CAACkD,YAAY;EAC5C;EACA,IAAIlD,KAAK,CAACwC,SAAS,EAAE;IACnBX,QAAQ,CAACW,SAAS,GAAGxC,KAAK,CAACwC,SAAS;EACtC;EACA,OAAOX,QAAQ;AACjB;AAaA;;;;;;AAMO,MAAMsB,wBAAwB,GAAGA,CACtCnD,KAAsC,EACtC4B,KAAa,KACgB;EAC7B,IAAI,IAAAvD,OAAA,CAAAyE,kBAAkB,EAAC9C,KAAK,CAACoD,WAAW,CAAC,EAAE;IACzC,MAAM,IAAIhF,OAAA,CAAA2E,aAAa,CACrB,0GAA0G,CAC3G;EACH;EAEA,MAAMlB,QAAQ,GAA8B;IAC1CgB,MAAM,EAAEjB,KAAK;IACbQ,KAAK,EAAE,KAAK;IACZE,MAAM,EAAEtC,KAAK,CAACsC,MAAM;IACpBU,UAAU,EAAEhD,KAAK,CAACoD;GACnB;EACD,IAAIpD,KAAK,CAACuC,IAAI,EAAE;IACdV,QAAQ,CAACU,IAAI,GAAGvC,KAAK,CAACuC,IAAI;EAC5B;EACA,IAAIvC,KAAK,CAACiD,MAAM,EAAE;IAChBpB,QAAQ,CAACoB,MAAM,GAAGjD,KAAK,CAACiD,MAAM;EAChC;EACA,IAAIjD,KAAK,CAACwC,SAAS,EAAE;IACnBX,QAAQ,CAACW,SAAS,GAAGxC,KAAK,CAACwC,SAAS;EACtC;EACA,OAAOX,QAAQ;AACjB,CAAC;AA1BY9D,OAAA,CAAAoF,wBAAwB,GAAAA,wBAAA;AA4BrC;AACA,SAAgBnF,cAAcA,CAC5BgC,KAAwC,EACxC4B,KAAa,EACbjD,SAAoB;EAEpB,QAAQqB,KAAK,CAACK,IAAI;IAChB,KAAK,WAAW;MACd,OAAO,IAAAtC,OAAA,CAAA4D,uBAAuB,EAAC3B,KAAK,EAAE4B,KAAK,EAAEjD,SAAS,CAAC;IACzD,KAAK,WAAW;MACd,OAAO,IAAAZ,OAAA,CAAAkE,uBAAuB,EAACjC,KAAK,EAAE4B,KAAK,CAAC;IAC9C,KAAK,YAAY;MACf,OAAO,IAAA7D,OAAA,CAAAoE,wBAAwB,EAACnC,KAAK,EAAE4B,KAAK,CAAC;IAC/C,KAAK,WAAW;MACd,OAAO,IAAA7D,OAAA,CAAA0E,uBAAuB,EAACzC,KAAK,EAAE4B,KAAK,CAAC;IAC9C,KAAK,YAAY;MACf,OAAO,IAAA7D,OAAA,CAAA4E,wBAAwB,EAAC3C,KAAK,EAAE4B,KAAK,CAAC;IAC/C,KAAK,YAAY;MACf,OAAO,IAAA7D,OAAA,CAAAoF,wBAAwB,EAACnD,KAAK,EAAE4B,KAAK,CAAC;EACjD;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}