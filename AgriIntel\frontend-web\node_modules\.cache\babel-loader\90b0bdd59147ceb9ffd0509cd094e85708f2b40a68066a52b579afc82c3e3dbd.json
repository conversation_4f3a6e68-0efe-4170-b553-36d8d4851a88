{"ast": null, "code": "import { useCallback as n, useState as f } from \"react\";\nimport { useIsMounted as i } from './use-is-mounted.js';\nfunction c() {\n  let a = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : 0;\n  let [l, r] = f(a),\n    t = i(),\n    o = n(e => {\n      t.current && r(u => u | e);\n    }, [l, t]),\n    m = n(e => Boolean(l & e), [l]),\n    s = n(e => {\n      t.current && r(u => u & ~e);\n    }, [r, t]),\n    g = n(e => {\n      t.current && r(u => u ^ e);\n    }, [r]);\n  return {\n    flags: l,\n    addFlag: o,\n    hasFlag: m,\n    removeFlag: s,\n    toggleFlag: g\n  };\n}\nexport { c as useFlags };", "map": {"version": 3, "names": ["useCallback", "n", "useState", "f", "useIsMounted", "i", "c", "a", "arguments", "length", "undefined", "l", "r", "t", "o", "e", "current", "u", "m", "Boolean", "s", "g", "flags", "addFlag", "hasFlag", "removeFlag", "toggleFlag", "useFlags"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/hooks/use-flags.js"], "sourcesContent": ["import{useCallback as n,useState as f}from\"react\";import{useIsMounted as i}from'./use-is-mounted.js';function c(a=0){let[l,r]=f(a),t=i(),o=n(e=>{t.current&&r(u=>u|e)},[l,t]),m=n(e=>Boolean(l&e),[l]),s=n(e=>{t.current&&r(u=>u&~e)},[r,t]),g=n(e=>{t.current&&r(u=>u^e)},[r]);return{flags:l,addFlag:o,hasFlag:m,removeFlag:s,toggleFlag:g}}export{c as useFlags};\n"], "mappings": "AAAA,SAAOA,WAAW,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,YAAY,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAASC,CAACA,CAAA,EAAK;EAAA,IAAJC,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAC,CAAC;EAAE,IAAG,CAACG,CAAC,EAACC,CAAC,CAAC,GAACT,CAAC,CAACI,CAAC,CAAC;IAACM,CAAC,GAACR,CAAC,CAAC,CAAC;IAACS,CAAC,GAACb,CAAC,CAACc,CAAC,IAAE;MAACF,CAAC,CAACG,OAAO,IAAEJ,CAAC,CAACK,CAAC,IAAEA,CAAC,GAACF,CAAC,CAAC;IAAA,CAAC,EAAC,CAACJ,CAAC,EAACE,CAAC,CAAC,CAAC;IAACK,CAAC,GAACjB,CAAC,CAACc,CAAC,IAAEI,OAAO,CAACR,CAAC,GAACI,CAAC,CAAC,EAAC,CAACJ,CAAC,CAAC,CAAC;IAACS,CAAC,GAACnB,CAAC,CAACc,CAAC,IAAE;MAACF,CAAC,CAACG,OAAO,IAAEJ,CAAC,CAACK,CAAC,IAAEA,CAAC,GAAC,CAACF,CAAC,CAAC;IAAA,CAAC,EAAC,CAACH,CAAC,EAACC,CAAC,CAAC,CAAC;IAACQ,CAAC,GAACpB,CAAC,CAACc,CAAC,IAAE;MAACF,CAAC,CAACG,OAAO,IAAEJ,CAAC,CAACK,CAAC,IAAEA,CAAC,GAACF,CAAC,CAAC;IAAA,CAAC,EAAC,CAACH,CAAC,CAAC,CAAC;EAAC,OAAM;IAACU,KAAK,EAACX,CAAC;IAACY,OAAO,EAACT,CAAC;IAACU,OAAO,EAACN,CAAC;IAACO,UAAU,EAACL,CAAC;IAACM,UAAU,EAACL;EAAC,CAAC;AAAA;AAAC,SAAOf,CAAC,IAAIqB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}