{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"forwardedRef\"];\nimport { createElement, forwardRef as forwardRefReact } from 'react';\nimport { useTranslation } from './useTranslation.js';\nimport { getDisplayName } from './utils.js';\nexport function withTranslation(ns) {\n  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return function Extend(WrappedComponent) {\n    function I18nextWithTranslation(_ref) {\n      let {\n          forwardedRef\n        } = _ref,\n        rest = _objectWithoutProperties(_ref, _excluded);\n      const [t, i18n, ready] = useTranslation(ns, _objectSpread(_objectSpread({}, rest), {}, {\n        keyPrefix: options.keyPrefix\n      }));\n      const passDownProps = _objectSpread(_objectSpread({}, rest), {}, {\n        t,\n        i18n,\n        tReady: ready\n      });\n      if (options.withRef && forwardedRef) {\n        passDownProps.ref = forwardedRef;\n      } else if (!options.withRef && forwardedRef) {\n        passDownProps.forwardedRef = forwardedRef;\n      }\n      return createElement(WrappedComponent, passDownProps);\n    }\n    I18nextWithTranslation.displayName = \"withI18nextTranslation(\".concat(getDisplayName(WrappedComponent), \")\");\n    I18nextWithTranslation.WrappedComponent = WrappedComponent;\n    const forwardRef = (props, ref) => createElement(I18nextWithTranslation, Object.assign({}, props, {\n      forwardedRef: ref\n    }));\n    return options.withRef ? forwardRefReact(forwardRef) : I18nextWithTranslation;\n  };\n}", "map": {"version": 3, "names": ["createElement", "forwardRef", "forwardRefReact", "useTranslation", "getDisplayName", "withTranslation", "ns", "options", "arguments", "length", "undefined", "Extend", "WrappedComponent", "I18nextWithTranslation", "_ref", "forwardedRef", "rest", "_objectWithoutProperties", "_excluded", "t", "i18n", "ready", "_objectSpread", "keyPrefix", "passDownProps", "tReady", "with<PERSON>ef", "ref", "displayName", "concat", "props", "Object", "assign"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-i18next/dist/es/withTranslation.js"], "sourcesContent": ["import { createElement, forwardRef as forwardRefReact } from 'react';\nimport { useTranslation } from './useTranslation.js';\nimport { getDisplayName } from './utils.js';\nexport function withTranslation(ns) {\n  let options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  return function Extend(WrappedComponent) {\n    function I18nextWithTranslation(_ref) {\n      let {\n        forwardedRef,\n        ...rest\n      } = _ref;\n      const [t, i18n, ready] = useTranslation(ns, {\n        ...rest,\n        keyPrefix: options.keyPrefix\n      });\n      const passDownProps = {\n        ...rest,\n        t,\n        i18n,\n        tReady: ready\n      };\n      if (options.withRef && forwardedRef) {\n        passDownProps.ref = forwardedRef;\n      } else if (!options.withRef && forwardedRef) {\n        passDownProps.forwardedRef = forwardedRef;\n      }\n      return createElement(WrappedComponent, passDownProps);\n    }\n    I18nextWithTranslation.displayName = `withI18nextTranslation(${getDisplayName(WrappedComponent)})`;\n    I18nextWithTranslation.WrappedComponent = WrappedComponent;\n    const forwardRef = (props, ref) => createElement(I18nextWithTranslation, Object.assign({}, props, {\n      forwardedRef: ref\n    }));\n    return options.withRef ? forwardRefReact(forwardRef) : I18nextWithTranslation;\n  };\n}"], "mappings": ";;;AAAA,SAASA,aAAa,EAAEC,UAAU,IAAIC,eAAe,QAAQ,OAAO;AACpE,SAASC,cAAc,QAAQ,qBAAqB;AACpD,SAASC,cAAc,QAAQ,YAAY;AAC3C,OAAO,SAASC,eAAeA,CAACC,EAAE,EAAE;EAClC,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpF,OAAO,SAASG,MAAMA,CAACC,gBAAgB,EAAE;IACvC,SAASC,sBAAsBA,CAACC,IAAI,EAAE;MACpC,IAAI;UACFC;QAEF,CAAC,GAAGD,IAAI;QADHE,IAAI,GAAAC,wBAAA,CACLH,IAAI,EAAAI,SAAA;MACR,MAAM,CAACC,CAAC,EAAEC,IAAI,EAAEC,KAAK,CAAC,GAAGlB,cAAc,CAACG,EAAE,EAAAgB,aAAA,CAAAA,aAAA,KACrCN,IAAI;QACPO,SAAS,EAAEhB,OAAO,CAACgB;MAAS,EAC7B,CAAC;MACF,MAAMC,aAAa,GAAAF,aAAA,CAAAA,aAAA,KACdN,IAAI;QACPG,CAAC;QACDC,IAAI;QACJK,MAAM,EAAEJ;MAAK,EACd;MACD,IAAId,OAAO,CAACmB,OAAO,IAAIX,YAAY,EAAE;QACnCS,aAAa,CAACG,GAAG,GAAGZ,YAAY;MAClC,CAAC,MAAM,IAAI,CAACR,OAAO,CAACmB,OAAO,IAAIX,YAAY,EAAE;QAC3CS,aAAa,CAACT,YAAY,GAAGA,YAAY;MAC3C;MACA,OAAOf,aAAa,CAACY,gBAAgB,EAAEY,aAAa,CAAC;IACvD;IACAX,sBAAsB,CAACe,WAAW,6BAAAC,MAAA,CAA6BzB,cAAc,CAACQ,gBAAgB,CAAC,MAAG;IAClGC,sBAAsB,CAACD,gBAAgB,GAAGA,gBAAgB;IAC1D,MAAMX,UAAU,GAAGA,CAAC6B,KAAK,EAAEH,GAAG,KAAK3B,aAAa,CAACa,sBAAsB,EAAEkB,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEF,KAAK,EAAE;MAChGf,YAAY,EAAEY;IAChB,CAAC,CAAC,CAAC;IACH,OAAOpB,OAAO,CAACmB,OAAO,GAAGxB,eAAe,CAACD,UAAU,CAAC,GAAGY,sBAAsB;EAC/E,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}