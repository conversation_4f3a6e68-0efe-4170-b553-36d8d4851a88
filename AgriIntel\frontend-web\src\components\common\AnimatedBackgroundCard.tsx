import React, { useMemo } from 'react';
import CustomButton from './CustomButton';
import { Box, Card, CardContent, Typography, IconButton, useTheme, alpha, CardProps, Chip, Skeleton, Tooltip, Badge, CircularProgress, Divider } from '@mui/material';
import LazyImage from './LazyImage';
import { motion, AnimatePresence } from 'framer-motion';
import { getSelectableStyles } from '../../utils/selectionUtils';

export interface AnimatedBackgroundCardProps extends Omit<CardProps, 'children' | 'content'> {
  title: string;
  subtitle?: string;
  backgroundImage?: string;
  module?: string;
  uniqueId?: string;
  icon?: React.ReactNode;
  accentColor?: string;
  secondaryColor?: string;
  actionIcon?: React.ReactNode;
  actionLabel?: string;
  onAction?: () => void;
  action?: React.ReactNode;
  content?: React.ReactNode;
  delay?: number;
  height?: string | number;
  children?: React.ReactNode;
  overlay?: 'light' | 'dark' | 'gradient' | 'none';
  loading?: boolean;
  error?: boolean;
  errorMessage?: string;
  cardVariant?: 'default' | 'outlined' | 'elevated';
  badges?: Array<{
    content: React.ReactNode | string | number;
    color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
    tooltip?: string;
  }>;
  tags?: Array<{
    label: string;
    color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
  }>;
  progress?: number;
  showProgressBar?: boolean;
  expandable?: boolean;
  headerAction?: React.ReactNode;
  footerContent?: React.ReactNode;
  hoverEffect?: 'lift' | 'glow' | 'border' | 'zoom' | 'none';
  secondaryAction?: {
    label: string;
    onClick: (e: React.MouseEvent) => void;
    icon?: React.ReactNode;
  };
}

const AnimatedBackgroundCard: React.FC<AnimatedBackgroundCardProps> = ({
  title,
  subtitle,
  backgroundImage: propBackgroundImage,
  module,
  uniqueId,
  icon,
  accentColor,
  secondaryColor,
  actionIcon,
  actionLabel,
  onAction,
  action,
  content,
  delay = 0,
  height = 200,
  children,
  overlay = 'gradient',
  loading = false,
  error = false,
  errorMessage = 'An error occurred',
  cardVariant = 'default',
  badges,
  tags,
  progress,
  showProgressBar = false,
  expandable = false,
  headerAction,
  footerContent,
  hoverEffect = 'lift',
  secondaryAction,
  ...cardProps
}) => {
  const theme = useTheme();

  // This function is no longer used as we're using the useMemo approach below
  // Keeping the function signature for reference but not using it
  const getModuleBackgroundImage = () => {
    return '';
  };

  // Check if this is an excluded module (dashboard, login, commercial)
  const isExcludedModule = module ? ['dashboard', 'login', 'commercial'].includes(module.toLowerCase()) : false;

  // Use metallic blue for all modules except excluded ones
  const defaultPrimary = isExcludedModule ? theme.palette.primary.main : '#4A6FA5';
  const defaultSecondary = isExcludedModule ? theme.palette.primary.dark : '#3A5A8C';

  // Use current theme colors or fallback to provided colors if explicitly specified
  const cardColor = accentColor || defaultPrimary;
  const cardSecondaryColor = secondaryColor || defaultSecondary;

  // Toggle expanded state is handled by the expandable prop

  // Card style variants
  const cardVariants = {
    default: {
      boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
      border: 'none',
      backgroundColor: alpha(cardColor, 0.03),
    },
    outlined: {
      boxShadow: 'none',
      border: `1px solid ${alpha(theme.palette.divider, 0.8)}`,
      backgroundColor: alpha(cardColor, 0.05),
    },
    elevated: {
      boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
      border: 'none',
      backgroundColor: alpha(cardColor, 0.02),
    },
  };

  // Hover effect styles
  const getHoverStyles = () => {
    switch (hoverEffect) {
      case 'lift':
        return {
          transform: 'translateY(-5px)',
          boxShadow: '0 8px 25px rgba(0,0,0,0.15)'
        };
      case 'glow':
        return {
          boxShadow: `0 0 20px ${alpha(cardColor, 0.5)}`
        };
      case 'border':
        return {
          borderColor: cardColor
        };
      case 'zoom':
        return {
          transform: 'scale(1.02)'
        };
      case 'none':
      default:
        return {};
    }
  };

  // Variables are already declared above

  // Use the provided background image or get one based on the module
  const backgroundImage = useMemo(() => {
    if (propBackgroundImage) {
      return propBackgroundImage;
    }

    // If no background image is provided but a module is, use a default image for that module
    if (module) {
      const moduleMap: Record<string, string> = {
        animals: '/images/modules/animals/cattle-1.jpeg', // Cows 1.jpeg
        health: '/images/modules/health/health-main.png', // health Managemnt.png
        breeding: '/images/modules/breeding/breeding-main.png', // breending and pregnancy.png
        feeding: '/images/modules/feeding/feed-main.jpeg', // Feed managemnt.jpeg
        feed: '/images/modules/feeding/feed-main.jpeg', // Feed managemnt.jpeg
        commercial: '/images/modules/commercial/commercial-main.webp', // commercial products.webp
        financial: '/images/modules/financial/financial-main.jpg', // Financial dashboard
        compliance: '/images/modules/commercial/commercial-2.jpeg', // commercial products 2.web.jpeg
        rfid: '/images/modules/rfid/rfid-1.webp', // RFID 1.webp
        reports: '/images/modules/reports/reports-main.jpg', // Reports dashboard
        settings: '/images/modules/animals/cattle-1.jpeg', // Use cattle image as fallback
        login: '/images/login/login-bg.jpeg', // log in page.jpeg
        dashboard: '/images/modules/animals/cattle-1.jpeg', // Use cattle image as fallback
        resources: '/images/modules/health/veterinary-1.jpg', // Use veterinary image as fallback
      };

      return moduleMap[module.toLowerCase()] || '/images/modules/animals/cattle-1.jpeg';
    }

    return propBackgroundImage || '';
  }, [propBackgroundImage, module]);

  const getOverlayStyles = () => {
    switch (overlay) {
      case 'light':
        return {
          background: alpha('#ffffff', 0.7),
          backdropFilter: 'blur(5px)',
        };
      case 'dark':
        return {
          background: alpha('#000000', 0.6),
          backdropFilter: 'blur(5px)',
        };
      case 'gradient':
        return {
          background: `linear-gradient(135deg, ${alpha(cardColor, 0.7)}, ${alpha(cardSecondaryColor, 0.6)})`,
          backdropFilter: 'blur(5px)',
          animation: 'gradientAnimation 15s ease infinite',
          backgroundSize: '200% 200%',
        };
      case 'none':
      default:
        return {};
    }
  };

  // Handle action click with stopPropagation
  const handleActionClick = (e: React.MouseEvent, callback?: () => void) => {
    e.stopPropagation();
    callback && callback();
  };

  // Handle secondary action click
  const handleSecondaryActionClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    secondaryAction?.onClick && secondaryAction.onClick(e);
  };

  // Render loading state
  if (loading) {
    return (
      <Box
        sx={{ height: '100%' }}
      >
        <Card
          {...cardProps}
          sx={{
            borderRadius: '12px',
            overflow: 'hidden',
            height: '100%',
            position: 'relative',
            ...cardVariants[cardVariant],
            ...cardProps.sx
          }}
        >
          <CardContent sx={{ p: 3 }}>
            <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
              <Box width="70%">
                <Skeleton variant="text" width="80%" height={32} />
                <Skeleton variant="text" width="60%" height={20} />
              </Box>
              <Skeleton variant="rounded" width={48} height={48} />
            </Box>
            <Box sx={{ mt: 2 }}>
              <Skeleton variant="rounded" width="100%" height={100} />
            </Box>
            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>
              <Skeleton variant="rounded" width={100} height={36} />
            </Box>
          </CardContent>
        </Card>
      </Box>
    );
  }

  // Render error state
  if (error) {
    return (
      <Box
        sx={{ height: '100%' }}
      >
        <Card
          {...cardProps}
          sx={{
            borderRadius: '12px',
            overflow: 'hidden',
            height: '100%',
            position: 'relative',
            border: `1px solid ${theme.palette.error.main}`,
            ...cardProps.sx
          }}
        >
          <CardContent
            sx={{
              position: 'relative',
              zIndex: 2,
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              p: 3,
              color: theme.palette.error.main
            }}
          >
            <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
              <Typography
                variant="h6"
                fontWeight="bold"
                gutterBottom
                sx={{
                  fontSize: { xs: '1.1rem', sm: '1.2rem' },
                  letterSpacing: '-0.3px',
                }}
              >
                {title}
              </Typography>
            </Box>
            <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center', flexDirection: 'column' }}>
              <Typography variant="body2" color="error" align="center">
                {errorMessage}
              </Typography>
              {onAction && (
                <CustomButton
                  variant="outlined"
                  color="error"
                  size="small"
                  onClick={onAction}
                  sx={{ mt: 2 }}
                >
                  Retry
                </CustomButton>
              )}
            </Box>
          </CardContent>
        </Card>
      </Box>
    );
  }

  // Render normal state
  return (
    <Box
      sx={{ height: '100%', ...(getSelectableStyles() || {}) }}
    >
      <Card
        {...cardProps}
        sx={{
          borderRadius: '12px',
          overflow: 'hidden',
          height: '100%',
          position: 'relative',
          transition: 'all 0.3s ease',
          cursor: onAction ? 'pointer' : 'default',
          '&:hover': hoverEffect !== 'none' ? getHoverStyles() : {},
          '&::before': {
            content: '""',
            position: 'absolute',
            top: 0,
            left: 0,
            width: '100%',
            height: '4px',
            background: `linear-gradient(to right, ${cardColor}, ${cardSecondaryColor})`,
            zIndex: 3,
            opacity: 0.9
          },
          ...cardVariants[cardVariant],
          ...cardProps.sx,
          ...getSelectableStyles()
        }}
        onClick={onAction}
      >
        {/* Progress Bar */}
        {showProgressBar && progress !== undefined && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              width: `${progress}%`,
              height: '4px',
              background: theme.palette.success.main,
              zIndex: 4,
              transition: 'width 0.5s ease-in-out'
            }}
          />
        )}

        {/* Solid Color Background with Overlay */}
        <Box
          sx={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: alpha(cardColor, 0.05),
            zIndex: 0,
            '&::after': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: `linear-gradient(135deg, ${alpha(cardColor, 0.15)}, ${alpha(cardSecondaryColor, 0.1)})`,
              zIndex: 1
            }
          }}
        />

        {/* Background Image (low opacity) */}
        {backgroundImage && (
          <Box
            sx={{
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              backgroundImage: `url(${backgroundImage})`,
              backgroundSize: 'cover',
              backgroundPosition: 'center',
              opacity: 0.05,
              zIndex: 0
            }}
          />
        )}

        {/* Card Content */}
        <CardContent
          sx={{
            position: 'relative',
            zIndex: 2,
            height: '100%',
            display: 'flex',
            flexDirection: 'column',
            p: 3,
            color: overlay === 'none' ? theme.palette.text.primary : theme.palette.getContrastText(cardColor),
            ...getSelectableStyles()
          }}
        >
          {/* Header with Title, Subtitle, Icon, and Badges */}
          <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={2}>
            <Box sx={{ flex: 1 }}>
              <Box display="flex" alignItems="center" flexWrap="wrap">
                <Typography
                  variant="h6"
                  fontWeight="bold"
                  sx={{
                    fontSize: { xs: '1.1rem', sm: '1.2rem' },
                    letterSpacing: '-0.3px',
                    color: overlay === 'none' ? cardColor : 'inherit',
                    mr: 1
                  }}
                >
                  {title}
                </Typography>

                {/* Badges */}
                {badges && badges.length > 0 && (
                  <Box display="flex" alignItems="center" flexWrap="wrap" gap={0.5}>
                    {badges.map((badge, index) => (
                      <Tooltip key={index} title={badge.tooltip || ''} arrow placement="top">
                        <Badge
                          badgeContent={badge.content}
                          color={badge.color || 'primary'}
                          sx={{
                            '& .MuiBadge-badge': {
                              fontSize: '0.7rem',
                              height: '18px',
                              minWidth: '18px'
                            }
                          }}
                        />
                      </Tooltip>
                    ))}
                  </Box>
                )}

                {/* Header Action */}
                {headerAction && (
                  <Box ml="auto">
                    {headerAction}
                  </Box>
                )}
              </Box>

              {subtitle && (
                <Typography
                  variant="body2"
                  sx={{
                    opacity: 0.9,
                    fontSize: { xs: '0.85rem', sm: '0.9rem' },
                    color: overlay === 'none' ? theme.palette.text.secondary : 'inherit',
                    mt: 0.5
                  }}
                >
                  {subtitle}
                </Typography>
              )}

              {/* Tags */}
              {tags && tags.length > 0 && (
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>
                  {tags.map((tag, index) => (
                    <Chip
                      key={index}
                      label={tag.label}
                      size="small"
                      color={tag.color || 'primary'}
                      variant="outlined"
                      sx={{
                        height: '20px',
                        fontSize: '0.7rem',
                        backgroundColor: alpha(theme.palette[tag.color || 'primary'].main, 0.1),
                        color: overlay === 'none' ? theme.palette[tag.color || 'primary'].main : 'white',
                        borderColor: alpha(theme.palette[tag.color || 'primary'].main, 0.3),
                      }}
                    />
                  ))}
                </Box>
              )}
            </Box>

            {/* Icon */}
            {icon && (
              <Box
                sx={{
                  p: 1.5,
                  borderRadius: '12px',
                  backgroundColor: overlay === 'none'
                    ? alpha(cardColor, 0.1)
                    : 'rgba(255, 255, 255, 0.2)',
                  color: overlay === 'none' ? cardColor : 'white',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  boxShadow: '0 2px 8px rgba(0,0,0,0.05)',
                  ml: 2
                }}
              >
                {icon}
              </Box>
            )}
          </Box>

          {/* Progress Indicator */}
          {progress !== undefined && !showProgressBar && (
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <CircularProgress
                variant="determinate"
                value={progress}
                size={24}
                thickness={5}
                sx={{ color: theme.palette.success.main }}
              />
              <Typography variant="body2" sx={{ ml: 1, opacity: 0.9 }}>
                {progress}% Complete
              </Typography>
            </Box>
          )}

          {/* Main Content */}
          <AnimatePresence>
            {(
              <Box
                component={motion.div}
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                transition={{ duration: 0.3 }}
                sx={{ flex: 1 }}
              >
                {children && (
                  <Box sx={{ flex: 1 }}>
                    {children}
                  </Box>
                )}
              </Box>
            )}
          </AnimatePresence>

          {/* Footer Content */}
          {footerContent && (
            <Box mt={2}>
              <Divider sx={{ my: 1, opacity: 0.2 }} />
              {footerContent}
            </Box>
          )}

          {/* Action Buttons */}
          {(actionIcon || actionLabel || secondaryAction) && (
            <Box mt="auto" pt={2} display="flex" justifyContent="flex-end" alignItems="center">
              {/* Secondary Action Button */}
              {secondaryAction && (
                <CustomButton
                  variant="text"
                  size="small"
                  startIcon={secondaryAction.icon}
                  onClick={handleSecondaryActionClick}
                  sx={{
                    mr: 'auto',
                    color: overlay === 'none' ? theme.palette.text.secondary : 'rgba(255, 255, 255, 0.7)',
                    '&:hover': {
                      backgroundColor: overlay === 'none' ? 'rgba(0, 0, 0, 0.05)' : 'rgba(255, 255, 255, 0.1)'
                    }
                  }}
                >
                  {secondaryAction.label}
                </CustomButton>
              )}

              {/* Icon Button */}
              {actionIcon && (
                <IconButton
                  onClick={(e) => handleActionClick(e, onAction)}
                  sx={{
                    color: overlay === 'none' ? cardColor : 'white',
                    backgroundColor: overlay === 'none' ? alpha(cardColor, 0.1) : 'rgba(255, 255, 255, 0.1)',
                    '&:hover': {
                      backgroundColor: overlay === 'none' ? alpha(cardColor, 0.2) : 'rgba(255, 255, 255, 0.2)'
                    }
                  }}
                >
                  {actionIcon}
                </IconButton>
              )}

              {/* Primary Action Button */}
              {actionLabel && (
                <CustomButton
                  variant="outlined"
                  size="small"
                  onClick={(e) => handleActionClick(e, onAction)}
                  sx={{
                    ml: actionIcon ? 1 : 0,
                    color: overlay === 'none' ? cardColor : 'white',
                    borderColor: overlay === 'none' ? alpha(cardColor, 0.5) : 'rgba(255, 255, 255, 0.5)',
                    '&:hover': {
                      borderColor: overlay === 'none' ? cardColor : 'white',
                      backgroundColor: overlay === 'none' ? alpha(cardColor, 0.1) : 'rgba(255, 255, 255, 0.1)'
                    }
                  }}
                >
                  {actionLabel}
                </CustomButton>
              )}
            </Box>
          )}
        </CardContent>
      </Card>
    </Box>
  );
};

export default AnimatedBackgroundCard;
