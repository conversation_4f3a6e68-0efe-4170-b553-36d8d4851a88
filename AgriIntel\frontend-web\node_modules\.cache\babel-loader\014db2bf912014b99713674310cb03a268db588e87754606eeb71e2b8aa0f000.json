{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.CommandOperation = void 0;\nconst error_1 = require(\"../error\");\nconst explain_1 = require(\"../explain\");\nconst read_concern_1 = require(\"../read_concern\");\nconst server_selection_1 = require(\"../sdam/server_selection\");\nconst utils_1 = require(\"../utils\");\nconst write_concern_1 = require(\"../write_concern\");\nconst operation_1 = require(\"./operation\");\n/** @internal */\nclass CommandOperation extends operation_1.AbstractOperation {\n  constructor(parent, options) {\n    super(options);\n    this.options = options ?? {};\n    // NOTE: this was explicitly added for the add/remove user operations, it's likely\n    //       something we'd want to reconsider. Perhaps those commands can use `Admin`\n    //       as a parent?\n    const dbNameOverride = options?.dbName || options?.authdb;\n    if (dbNameOverride) {\n      this.ns = new utils_1.MongoDBNamespace(dbNameOverride, '$cmd');\n    } else {\n      this.ns = parent ? parent.s.namespace.withCollection('$cmd') : new utils_1.MongoDBNamespace('admin', '$cmd');\n    }\n    this.readConcern = read_concern_1.ReadConcern.fromOptions(options);\n    this.writeConcern = write_concern_1.WriteConcern.fromOptions(options);\n    if (this.hasAspect(operation_1.Aspect.EXPLAINABLE)) {\n      this.explain = explain_1.Explain.fromOptions(options);\n      if (this.explain) (0, explain_1.validateExplainTimeoutOptions)(this.options, this.explain);\n    } else if (options?.explain != null) {\n      throw new error_1.MongoInvalidArgumentError(`Option \"explain\" is not supported on this command`);\n    }\n  }\n  get canRetryWrite() {\n    if (this.hasAspect(operation_1.Aspect.EXPLAINABLE)) {\n      return this.explain == null;\n    }\n    return super.canRetryWrite;\n  }\n  async executeCommand(server, session, cmd, timeoutContext, responseType) {\n    this.server = server;\n    const options = {\n      ...this.options,\n      ...this.bsonOptions,\n      timeoutContext,\n      readPreference: this.readPreference,\n      session\n    };\n    const serverWireVersion = (0, utils_1.maxWireVersion)(server);\n    const inTransaction = this.session && this.session.inTransaction();\n    if (this.readConcern && (0, utils_1.commandSupportsReadConcern)(cmd) && !inTransaction) {\n      Object.assign(cmd, {\n        readConcern: this.readConcern\n      });\n    }\n    if (this.trySecondaryWrite && serverWireVersion < server_selection_1.MIN_SECONDARY_WRITE_WIRE_VERSION) {\n      options.omitReadPreference = true;\n    }\n    if (this.writeConcern && this.hasAspect(operation_1.Aspect.WRITE_OPERATION) && !inTransaction) {\n      write_concern_1.WriteConcern.apply(cmd, this.writeConcern);\n    }\n    if (options.collation && typeof options.collation === 'object' && !this.hasAspect(operation_1.Aspect.SKIP_COLLATION)) {\n      Object.assign(cmd, {\n        collation: options.collation\n      });\n    }\n    if (typeof options.maxTimeMS === 'number') {\n      cmd.maxTimeMS = options.maxTimeMS;\n    }\n    if (this.hasAspect(operation_1.Aspect.EXPLAINABLE) && this.explain) {\n      cmd = (0, explain_1.decorateWithExplain)(cmd, this.explain);\n    }\n    return await server.command(this.ns, cmd, options, responseType);\n  }\n}\nexports.CommandOperation = CommandOperation;", "map": {"version": 3, "names": ["error_1", "require", "explain_1", "read_concern_1", "server_selection_1", "utils_1", "write_concern_1", "operation_1", "CommandOperation", "AbstractOperation", "constructor", "parent", "options", "dbNameOverride", "dbN<PERSON>", "authdb", "ns", "MongoDBNamespace", "s", "namespace", "withCollection", "readConcern", "ReadConcern", "fromOptions", "writeConcern", "WriteConcern", "hasAspect", "Aspect", "EXPLAINABLE", "explain", "Explain", "validateExplainTimeoutOptions", "MongoInvalidArgumentError", "canRetryWrite", "executeCommand", "server", "session", "cmd", "timeoutContext", "responseType", "bsonOptions", "readPreference", "serverWireVersion", "maxWireVersion", "inTransaction", "commandSupportsReadConcern", "Object", "assign", "trySecondaryWrite", "MIN_SECONDARY_WRITE_WIRE_VERSION", "omitReadPreference", "WRITE_OPERATION", "apply", "collation", "SKIP_COLLATION", "maxTimeMS", "decorateWithExplain", "command", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\command.ts"], "sourcesContent": ["import type { BSONSerializeOptions, Document } from '../bson';\nimport { type MongoDBResponseConstructor } from '../cmap/wire_protocol/responses';\nimport { MongoInvalidArgumentError } from '../error';\nimport {\n  decorateWithExplain,\n  Explain,\n  type ExplainOptions,\n  validateExplainTimeoutOptions\n} from '../explain';\nimport { ReadConcern } from '../read_concern';\nimport type { ReadPreference } from '../read_preference';\nimport type { Server } from '../sdam/server';\nimport { MIN_SECONDARY_WRITE_WIRE_VERSION } from '../sdam/server_selection';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { commandSupportsReadConcern, maxWireVersion, MongoDBNamespace } from '../utils';\nimport { WriteConcern, type WriteConcernOptions } from '../write_concern';\nimport type { ReadConcernLike } from './../read_concern';\nimport { AbstractOperation, Aspect, type OperationOptions } from './operation';\n\n/** @public */\nexport interface CollationOptions {\n  locale: string;\n  caseLevel?: boolean;\n  caseFirst?: string;\n  strength?: number;\n  numericOrdering?: boolean;\n  alternate?: string;\n  maxVariable?: string;\n  backwards?: boolean;\n  normalization?: boolean;\n}\n\n/** @public */\nexport interface CommandOperationOptions\n  extends OperationOptions,\n    WriteConcernOptions,\n    ExplainOptions {\n  /** Specify a read concern and level for the collection. (only MongoDB 3.2 or higher supported) */\n  readConcern?: ReadConcernLike;\n  /** Collation */\n  collation?: CollationOptions;\n  /**\n   * maxTimeMS is a server-side time limit in milliseconds for processing an operation.\n   */\n  maxTimeMS?: number;\n  /**\n   * Comment to apply to the operation.\n   *\n   * In server versions pre-4.4, 'comment' must be string.  A server\n   * error will be thrown if any other type is provided.\n   *\n   * In server versions 4.4 and above, 'comment' can be any valid BSON type.\n   */\n  comment?: unknown;\n  /** Should retry failed writes */\n  retryWrites?: boolean;\n\n  // Admin command overrides.\n  dbName?: string;\n  authdb?: string;\n  noResponse?: boolean;\n}\n\n/** @internal */\nexport interface OperationParent {\n  s: { namespace: MongoDBNamespace };\n  readConcern?: ReadConcern;\n  writeConcern?: WriteConcern;\n  readPreference?: ReadPreference;\n  bsonOptions?: BSONSerializeOptions;\n  timeoutMS?: number;\n}\n\n/** @internal */\nexport abstract class CommandOperation<T> extends AbstractOperation<T> {\n  override options: CommandOperationOptions;\n  readConcern?: ReadConcern;\n  writeConcern?: WriteConcern;\n  explain?: Explain;\n\n  constructor(parent?: OperationParent, options?: CommandOperationOptions) {\n    super(options);\n    this.options = options ?? {};\n\n    // NOTE: this was explicitly added for the add/remove user operations, it's likely\n    //       something we'd want to reconsider. Perhaps those commands can use `Admin`\n    //       as a parent?\n    const dbNameOverride = options?.dbName || options?.authdb;\n    if (dbNameOverride) {\n      this.ns = new MongoDBNamespace(dbNameOverride, '$cmd');\n    } else {\n      this.ns = parent\n        ? parent.s.namespace.withCollection('$cmd')\n        : new MongoDBNamespace('admin', '$cmd');\n    }\n\n    this.readConcern = ReadConcern.fromOptions(options);\n    this.writeConcern = WriteConcern.fromOptions(options);\n\n    if (this.hasAspect(Aspect.EXPLAINABLE)) {\n      this.explain = Explain.fromOptions(options);\n      if (this.explain) validateExplainTimeoutOptions(this.options, this.explain);\n    } else if (options?.explain != null) {\n      throw new MongoInvalidArgumentError(`Option \"explain\" is not supported on this command`);\n    }\n  }\n\n  override get canRetryWrite(): boolean {\n    if (this.hasAspect(Aspect.EXPLAINABLE)) {\n      return this.explain == null;\n    }\n    return super.canRetryWrite;\n  }\n\n  public async executeCommand<T extends MongoDBResponseConstructor>(\n    server: Server,\n    session: ClientSession | undefined,\n    cmd: Document,\n    timeoutContext: TimeoutContext,\n    responseType: T | undefined\n  ): Promise<typeof responseType extends undefined ? Document : InstanceType<T>>;\n\n  public async executeCommand(\n    server: Server,\n    session: ClientSession | undefined,\n    cmd: Document,\n    timeoutContext: TimeoutContext\n  ): Promise<Document>;\n\n  async executeCommand(\n    server: Server,\n    session: ClientSession | undefined,\n    cmd: Document,\n    timeoutContext: TimeoutContext,\n    responseType?: MongoDBResponseConstructor\n  ): Promise<Document> {\n    this.server = server;\n\n    const options = {\n      ...this.options,\n      ...this.bsonOptions,\n      timeoutContext,\n      readPreference: this.readPreference,\n      session\n    };\n\n    const serverWireVersion = maxWireVersion(server);\n    const inTransaction = this.session && this.session.inTransaction();\n\n    if (this.readConcern && commandSupportsReadConcern(cmd) && !inTransaction) {\n      Object.assign(cmd, { readConcern: this.readConcern });\n    }\n\n    if (this.trySecondaryWrite && serverWireVersion < MIN_SECONDARY_WRITE_WIRE_VERSION) {\n      options.omitReadPreference = true;\n    }\n\n    if (this.writeConcern && this.hasAspect(Aspect.WRITE_OPERATION) && !inTransaction) {\n      WriteConcern.apply(cmd, this.writeConcern);\n    }\n\n    if (\n      options.collation &&\n      typeof options.collation === 'object' &&\n      !this.hasAspect(Aspect.SKIP_COLLATION)\n    ) {\n      Object.assign(cmd, { collation: options.collation });\n    }\n\n    if (typeof options.maxTimeMS === 'number') {\n      cmd.maxTimeMS = options.maxTimeMS;\n    }\n\n    if (this.hasAspect(Aspect.EXPLAINABLE) && this.explain) {\n      cmd = decorateWithExplain(cmd, this.explain);\n    }\n\n    return await server.command(this.ns, cmd, options, responseType);\n  }\n}\n"], "mappings": ";;;;;;AAEA,MAAAA,OAAA,GAAAC,OAAA;AACA,MAAAC,SAAA,GAAAD,OAAA;AAMA,MAAAE,cAAA,GAAAF,OAAA;AAGA,MAAAG,kBAAA,GAAAH,OAAA;AAGA,MAAAI,OAAA,GAAAJ,OAAA;AACA,MAAAK,eAAA,GAAAL,OAAA;AAEA,MAAAM,WAAA,GAAAN,OAAA;AAwDA;AACA,MAAsBO,gBAAoB,SAAQD,WAAA,CAAAE,iBAAoB;EAMpEC,YAAYC,MAAwB,EAAEC,OAAiC;IACrE,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAI,EAAE;IAE5B;IACA;IACA;IACA,MAAMC,cAAc,GAAGD,OAAO,EAAEE,MAAM,IAAIF,OAAO,EAAEG,MAAM;IACzD,IAAIF,cAAc,EAAE;MAClB,IAAI,CAACG,EAAE,GAAG,IAAIX,OAAA,CAAAY,gBAAgB,CAACJ,cAAc,EAAE,MAAM,CAAC;IACxD,CAAC,MAAM;MACL,IAAI,CAACG,EAAE,GAAGL,MAAM,GACZA,MAAM,CAACO,CAAC,CAACC,SAAS,CAACC,cAAc,CAAC,MAAM,CAAC,GACzC,IAAIf,OAAA,CAAAY,gBAAgB,CAAC,OAAO,EAAE,MAAM,CAAC;IAC3C;IAEA,IAAI,CAACI,WAAW,GAAGlB,cAAA,CAAAmB,WAAW,CAACC,WAAW,CAACX,OAAO,CAAC;IACnD,IAAI,CAACY,YAAY,GAAGlB,eAAA,CAAAmB,YAAY,CAACF,WAAW,CAACX,OAAO,CAAC;IAErD,IAAI,IAAI,CAACc,SAAS,CAACnB,WAAA,CAAAoB,MAAM,CAACC,WAAW,CAAC,EAAE;MACtC,IAAI,CAACC,OAAO,GAAG3B,SAAA,CAAA4B,OAAO,CAACP,WAAW,CAACX,OAAO,CAAC;MAC3C,IAAI,IAAI,CAACiB,OAAO,EAAE,IAAA3B,SAAA,CAAA6B,6BAA6B,EAAC,IAAI,CAACnB,OAAO,EAAE,IAAI,CAACiB,OAAO,CAAC;IAC7E,CAAC,MAAM,IAAIjB,OAAO,EAAEiB,OAAO,IAAI,IAAI,EAAE;MACnC,MAAM,IAAI7B,OAAA,CAAAgC,yBAAyB,CAAC,mDAAmD,CAAC;IAC1F;EACF;EAEA,IAAaC,aAAaA,CAAA;IACxB,IAAI,IAAI,CAACP,SAAS,CAACnB,WAAA,CAAAoB,MAAM,CAACC,WAAW,CAAC,EAAE;MACtC,OAAO,IAAI,CAACC,OAAO,IAAI,IAAI;IAC7B;IACA,OAAO,KAAK,CAACI,aAAa;EAC5B;EAiBA,MAAMC,cAAcA,CAClBC,MAAc,EACdC,OAAkC,EAClCC,GAAa,EACbC,cAA8B,EAC9BC,YAAyC;IAEzC,IAAI,CAACJ,MAAM,GAAGA,MAAM;IAEpB,MAAMvB,OAAO,GAAG;MACd,GAAG,IAAI,CAACA,OAAO;MACf,GAAG,IAAI,CAAC4B,WAAW;MACnBF,cAAc;MACdG,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCL;KACD;IAED,MAAMM,iBAAiB,GAAG,IAAArC,OAAA,CAAAsC,cAAc,EAACR,MAAM,CAAC;IAChD,MAAMS,aAAa,GAAG,IAAI,CAACR,OAAO,IAAI,IAAI,CAACA,OAAO,CAACQ,aAAa,EAAE;IAElE,IAAI,IAAI,CAACvB,WAAW,IAAI,IAAAhB,OAAA,CAAAwC,0BAA0B,EAACR,GAAG,CAAC,IAAI,CAACO,aAAa,EAAE;MACzEE,MAAM,CAACC,MAAM,CAACV,GAAG,EAAE;QAAEhB,WAAW,EAAE,IAAI,CAACA;MAAW,CAAE,CAAC;IACvD;IAEA,IAAI,IAAI,CAAC2B,iBAAiB,IAAIN,iBAAiB,GAAGtC,kBAAA,CAAA6C,gCAAgC,EAAE;MAClFrC,OAAO,CAACsC,kBAAkB,GAAG,IAAI;IACnC;IAEA,IAAI,IAAI,CAAC1B,YAAY,IAAI,IAAI,CAACE,SAAS,CAACnB,WAAA,CAAAoB,MAAM,CAACwB,eAAe,CAAC,IAAI,CAACP,aAAa,EAAE;MACjFtC,eAAA,CAAAmB,YAAY,CAAC2B,KAAK,CAACf,GAAG,EAAE,IAAI,CAACb,YAAY,CAAC;IAC5C;IAEA,IACEZ,OAAO,CAACyC,SAAS,IACjB,OAAOzC,OAAO,CAACyC,SAAS,KAAK,QAAQ,IACrC,CAAC,IAAI,CAAC3B,SAAS,CAACnB,WAAA,CAAAoB,MAAM,CAAC2B,cAAc,CAAC,EACtC;MACAR,MAAM,CAACC,MAAM,CAACV,GAAG,EAAE;QAAEgB,SAAS,EAAEzC,OAAO,CAACyC;MAAS,CAAE,CAAC;IACtD;IAEA,IAAI,OAAOzC,OAAO,CAAC2C,SAAS,KAAK,QAAQ,EAAE;MACzClB,GAAG,CAACkB,SAAS,GAAG3C,OAAO,CAAC2C,SAAS;IACnC;IAEA,IAAI,IAAI,CAAC7B,SAAS,CAACnB,WAAA,CAAAoB,MAAM,CAACC,WAAW,CAAC,IAAI,IAAI,CAACC,OAAO,EAAE;MACtDQ,GAAG,GAAG,IAAAnC,SAAA,CAAAsD,mBAAmB,EAACnB,GAAG,EAAE,IAAI,CAACR,OAAO,CAAC;IAC9C;IAEA,OAAO,MAAMM,MAAM,CAACsB,OAAO,CAAC,IAAI,CAACzC,EAAE,EAAEqB,GAAG,EAAEzB,OAAO,EAAE2B,YAAY,CAAC;EAClE;;AAxGFmB,OAAA,CAAAlD,gBAAA,GAAAA,gBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}