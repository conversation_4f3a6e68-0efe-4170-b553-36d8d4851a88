{"ast": null, "code": "import { useEffect as s, useRef as f } from \"react\";\nimport { useEvent as i } from './use-event.js';\nfunction m(u, t) {\n  let e = f([]),\n    r = i(u);\n  s(() => {\n    let o = [...e.current];\n    for (let [n, a] of t.entries()) if (e.current[n] !== a) {\n      let l = r(t, o);\n      return e.current = t, l;\n    }\n  }, [r, ...t]);\n}\nexport { m as useWatch };", "map": {"version": 3, "names": ["useEffect", "s", "useRef", "f", "useEvent", "i", "m", "u", "t", "e", "r", "o", "current", "n", "a", "entries", "l", "useWatch"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/hooks/use-watch.js"], "sourcesContent": ["import{useEffect as s,useRef as f}from\"react\";import{useEvent as i}from'./use-event.js';function m(u,t){let e=f([]),r=i(u);s(()=>{let o=[...e.current];for(let[n,a]of t.entries())if(e.current[n]!==a){let l=r(t,o);return e.current=t,l}},[r,...t])}export{m as useWatch};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,gBAAgB;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACN,CAAC,CAAC,EAAE,CAAC;IAACO,CAAC,GAACL,CAAC,CAACE,CAAC,CAAC;EAACN,CAAC,CAAC,MAAI;IAAC,IAAIU,CAAC,GAAC,CAAC,GAAGF,CAAC,CAACG,OAAO,CAAC;IAAC,KAAI,IAAG,CAACC,CAAC,EAACC,CAAC,CAAC,IAAGN,CAAC,CAACO,OAAO,CAAC,CAAC,EAAC,IAAGN,CAAC,CAACG,OAAO,CAACC,CAAC,CAAC,KAAGC,CAAC,EAAC;MAAC,IAAIE,CAAC,GAACN,CAAC,CAACF,CAAC,EAACG,CAAC,CAAC;MAAC,OAAOF,CAAC,CAACG,OAAO,GAACJ,CAAC,EAACQ,CAAC;IAAA;EAAC,CAAC,EAAC,CAACN,CAAC,EAAC,GAAGF,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOF,CAAC,IAAIW,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}