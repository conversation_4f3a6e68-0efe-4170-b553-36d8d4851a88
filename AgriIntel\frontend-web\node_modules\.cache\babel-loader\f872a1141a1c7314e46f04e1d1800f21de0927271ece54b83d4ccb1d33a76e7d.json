{"ast": null, "code": "import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport toDate from \"../toDate/index.js\";\nimport setMonth from \"../setMonth/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name set\n * @category Common Helpers\n * @summary Set date values to a given date.\n *\n * @description\n * Set date values to a given date.\n *\n * Sets time values to date from object `values`.\n * A value is not set if it is undefined or null or doesn't exist in `values`.\n *\n * Note about bundle size: `set` does not internally use `setX` functions from date-fns but instead opts\n * to use native `Date#setX` methods. If you use this function, you may not want to include the\n * other `setX` functions that date-fns provides if you are concerned about the bundle size.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Object} values - an object with options\n * @param {Number} [values.year] - the number of years to be set\n * @param {Number} [values.month] - the number of months to be set\n * @param {Number} [values.date] - the number of days to be set\n * @param {Number} [values.hours] - the number of hours to be set\n * @param {Number} [values.minutes] - the number of minutes to be set\n * @param {Number} [values.seconds] - the number of seconds to be set\n * @param {Number} [values.milliseconds] - the number of milliseconds to be set\n * @returns {Date} the new date with options set\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `values` must be an object\n *\n * @example\n * // Transform 1 September 2014 into 20 October 2015 in a single line:\n * const result = set(new Date(2014, 8, 20), { year: 2015, month: 9, date: 20 })\n * //=> Tue Oct 20 2015 00:00:00\n *\n * @example\n * // Set 12 PM to 1 September 2014 01:23:45 to 1 September 2014 12:00:00:\n * const result = set(new Date(2014, 8, 1, 1, 23, 45), { hours: 12 })\n * //=> Mon Sep 01 2014 12:23:45\n */\nexport default function set(dirtyDate, values) {\n  requiredArgs(2, arguments);\n  if (_typeof(values) !== 'object' || values === null) {\n    throw new RangeError('values parameter must be an object');\n  }\n  var date = toDate(dirtyDate);\n\n  // Check if date is Invalid Date because Date.prototype.setFullYear ignores the value of Invalid Date\n  if (isNaN(date.getTime())) {\n    return new Date(NaN);\n  }\n  if (values.year != null) {\n    date.setFullYear(values.year);\n  }\n  if (values.month != null) {\n    date = setMonth(date, values.month);\n  }\n  if (values.date != null) {\n    date.setDate(toInteger(values.date));\n  }\n  if (values.hours != null) {\n    date.setHours(toInteger(values.hours));\n  }\n  if (values.minutes != null) {\n    date.setMinutes(toInteger(values.minutes));\n  }\n  if (values.seconds != null) {\n    date.setSeconds(toInteger(values.seconds));\n  }\n  if (values.milliseconds != null) {\n    date.setMilliseconds(toInteger(values.milliseconds));\n  }\n  return date;\n}", "map": {"version": 3, "names": ["_typeof", "toDate", "setMonth", "toInteger", "requiredArgs", "set", "dirtyDate", "values", "arguments", "RangeError", "date", "isNaN", "getTime", "Date", "NaN", "year", "setFullYear", "month", "setDate", "hours", "setHours", "minutes", "setMinutes", "seconds", "setSeconds", "milliseconds", "setMilliseconds"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/date-fns/esm/set/index.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport toDate from \"../toDate/index.js\";\nimport setMonth from \"../setMonth/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name set\n * @category Common Helpers\n * @summary Set date values to a given date.\n *\n * @description\n * Set date values to a given date.\n *\n * Sets time values to date from object `values`.\n * A value is not set if it is undefined or null or doesn't exist in `values`.\n *\n * Note about bundle size: `set` does not internally use `setX` functions from date-fns but instead opts\n * to use native `Date#setX` methods. If you use this function, you may not want to include the\n * other `setX` functions that date-fns provides if you are concerned about the bundle size.\n *\n * @param {Date|Number} date - the date to be changed\n * @param {Object} values - an object with options\n * @param {Number} [values.year] - the number of years to be set\n * @param {Number} [values.month] - the number of months to be set\n * @param {Number} [values.date] - the number of days to be set\n * @param {Number} [values.hours] - the number of hours to be set\n * @param {Number} [values.minutes] - the number of minutes to be set\n * @param {Number} [values.seconds] - the number of seconds to be set\n * @param {Number} [values.milliseconds] - the number of milliseconds to be set\n * @returns {Date} the new date with options set\n * @throws {TypeError} 2 arguments required\n * @throws {RangeError} `values` must be an object\n *\n * @example\n * // Transform 1 September 2014 into 20 October 2015 in a single line:\n * const result = set(new Date(2014, 8, 20), { year: 2015, month: 9, date: 20 })\n * //=> Tue Oct 20 2015 00:00:00\n *\n * @example\n * // Set 12 PM to 1 September 2014 01:23:45 to 1 September 2014 12:00:00:\n * const result = set(new Date(2014, 8, 1, 1, 23, 45), { hours: 12 })\n * //=> Mon Sep 01 2014 12:23:45\n */\nexport default function set(dirtyDate, values) {\n  requiredArgs(2, arguments);\n  if (_typeof(values) !== 'object' || values === null) {\n    throw new RangeError('values parameter must be an object');\n  }\n  var date = toDate(dirtyDate);\n\n  // Check if date is Invalid Date because Date.prototype.setFullYear ignores the value of Invalid Date\n  if (isNaN(date.getTime())) {\n    return new Date(NaN);\n  }\n  if (values.year != null) {\n    date.setFullYear(values.year);\n  }\n  if (values.month != null) {\n    date = setMonth(date, values.month);\n  }\n  if (values.date != null) {\n    date.setDate(toInteger(values.date));\n  }\n  if (values.hours != null) {\n    date.setHours(toInteger(values.hours));\n  }\n  if (values.minutes != null) {\n    date.setMinutes(toInteger(values.minutes));\n  }\n  if (values.seconds != null) {\n    date.setSeconds(toInteger(values.seconds));\n  }\n  if (values.milliseconds != null) {\n    date.setMilliseconds(toInteger(values.milliseconds));\n  }\n  return date;\n}"], "mappings": "AAAA,OAAOA,OAAO,MAAM,mCAAmC;AACvD,OAAOC,MAAM,MAAM,oBAAoB;AACvC,OAAOC,QAAQ,MAAM,sBAAsB;AAC3C,OAAOC,SAAS,MAAM,4BAA4B;AAClD,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,GAAGA,CAACC,SAAS,EAAEC,MAAM,EAAE;EAC7CH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIR,OAAO,CAACO,MAAM,CAAC,KAAK,QAAQ,IAAIA,MAAM,KAAK,IAAI,EAAE;IACnD,MAAM,IAAIE,UAAU,CAAC,oCAAoC,CAAC;EAC5D;EACA,IAAIC,IAAI,GAAGT,MAAM,CAACK,SAAS,CAAC;;EAE5B;EACA,IAAIK,KAAK,CAACD,IAAI,CAACE,OAAO,CAAC,CAAC,CAAC,EAAE;IACzB,OAAO,IAAIC,IAAI,CAACC,GAAG,CAAC;EACtB;EACA,IAAIP,MAAM,CAACQ,IAAI,IAAI,IAAI,EAAE;IACvBL,IAAI,CAACM,WAAW,CAACT,MAAM,CAACQ,IAAI,CAAC;EAC/B;EACA,IAAIR,MAAM,CAACU,KAAK,IAAI,IAAI,EAAE;IACxBP,IAAI,GAAGR,QAAQ,CAACQ,IAAI,EAAEH,MAAM,CAACU,KAAK,CAAC;EACrC;EACA,IAAIV,MAAM,CAACG,IAAI,IAAI,IAAI,EAAE;IACvBA,IAAI,CAACQ,OAAO,CAACf,SAAS,CAACI,MAAM,CAACG,IAAI,CAAC,CAAC;EACtC;EACA,IAAIH,MAAM,CAACY,KAAK,IAAI,IAAI,EAAE;IACxBT,IAAI,CAACU,QAAQ,CAACjB,SAAS,CAACI,MAAM,CAACY,KAAK,CAAC,CAAC;EACxC;EACA,IAAIZ,MAAM,CAACc,OAAO,IAAI,IAAI,EAAE;IAC1BX,IAAI,CAACY,UAAU,CAACnB,SAAS,CAACI,MAAM,CAACc,OAAO,CAAC,CAAC;EAC5C;EACA,IAAId,MAAM,CAACgB,OAAO,IAAI,IAAI,EAAE;IAC1Bb,IAAI,CAACc,UAAU,CAACrB,SAAS,CAACI,MAAM,CAACgB,OAAO,CAAC,CAAC;EAC5C;EACA,IAAIhB,MAAM,CAACkB,YAAY,IAAI,IAAI,EAAE;IAC/Bf,IAAI,CAACgB,eAAe,CAACvB,SAAS,CAACI,MAAM,CAACkB,YAAY,CAAC,CAAC;EACtD;EACA,OAAOf,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}