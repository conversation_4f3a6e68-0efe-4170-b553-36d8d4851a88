{"ast": null, "code": "function e(i = {}, s = null, t = []) {\n  for (let [r, n] of Object.entries(i)) o(t, f(s, r), n);\n  return t;\n}\nfunction f(i, s) {\n  return i ? i + \"[\" + s + \"]\" : s;\n}\nfunction o(i, s, t) {\n  if (Array.isArray(t)) for (let [r, n] of t.entries()) o(i, f(s, r.toString()), n);else t instanceof Date ? i.push([s, t.toISOString()]) : typeof t == \"boolean\" ? i.push([s, t ? \"1\" : \"0\"]) : typeof t == \"string\" ? i.push([s, t]) : typeof t == \"number\" ? i.push([s, `${t}`]) : t == null ? i.push([s, \"\"]) : e(t, s, i);\n}\nfunction p(i) {\n  var t, r;\n  let s = (t = i == null ? void 0 : i.form) != null ? t : i.closest(\"form\");\n  if (s) {\n    for (let n of s.elements) if (n !== i && (n.tagName === \"INPUT\" && n.type === \"submit\" || n.tagName === \"BUTTON\" && n.type === \"submit\" || n.nodeName === \"INPUT\" && n.type === \"image\")) {\n      n.click();\n      return;\n    }\n    (r = s.requestSubmit) == null || r.call(s);\n  }\n}\nexport { p as attemptSubmit, e as objectToFormEntries };", "map": {"version": 3, "names": ["e", "i", "s", "t", "r", "n", "Object", "entries", "o", "f", "Array", "isArray", "toString", "Date", "push", "toISOString", "p", "form", "closest", "elements", "tagName", "type", "nodeName", "click", "requestSubmit", "call", "attemptSubmit", "objectToFormEntries"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/utils/form.js"], "sourcesContent": ["function e(i={},s=null,t=[]){for(let[r,n]of Object.entries(i))o(t,f(s,r),n);return t}function f(i,s){return i?i+\"[\"+s+\"]\":s}function o(i,s,t){if(Array.isArray(t))for(let[r,n]of t.entries())o(i,f(s,r.toString()),n);else t instanceof Date?i.push([s,t.toISOString()]):typeof t==\"boolean\"?i.push([s,t?\"1\":\"0\"]):typeof t==\"string\"?i.push([s,t]):typeof t==\"number\"?i.push([s,`${t}`]):t==null?i.push([s,\"\"]):e(t,s,i)}function p(i){var t,r;let s=(t=i==null?void 0:i.form)!=null?t:i.closest(\"form\");if(s){for(let n of s.elements)if(n!==i&&(n.tagName===\"INPUT\"&&n.type===\"submit\"||n.tagName===\"BUTTON\"&&n.type===\"submit\"||n.nodeName===\"INPUT\"&&n.type===\"image\")){n.click();return}(r=s.requestSubmit)==null||r.call(s)}}export{p as attemptSubmit,e as objectToFormEntries};\n"], "mappings": "AAAA,SAASA,CAACA,CAACC,CAAC,GAAC,CAAC,CAAC,EAACC,CAAC,GAAC,IAAI,EAACC,CAAC,GAAC,EAAE,EAAC;EAAC,KAAI,IAAG,CAACC,CAAC,EAACC,CAAC,CAAC,IAAGC,MAAM,CAACC,OAAO,CAACN,CAAC,CAAC,EAACO,CAAC,CAACL,CAAC,EAACM,CAAC,CAACP,CAAC,EAACE,CAAC,CAAC,EAACC,CAAC,CAAC;EAAC,OAAOF,CAAC;AAAA;AAAC,SAASM,CAACA,CAACR,CAAC,EAACC,CAAC,EAAC;EAAC,OAAOD,CAAC,GAACA,CAAC,GAAC,GAAG,GAACC,CAAC,GAAC,GAAG,GAACA,CAAC;AAAA;AAAC,SAASM,CAACA,CAACP,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAGO,KAAK,CAACC,OAAO,CAACR,CAAC,CAAC,EAAC,KAAI,IAAG,CAACC,CAAC,EAACC,CAAC,CAAC,IAAGF,CAAC,CAACI,OAAO,CAAC,CAAC,EAACC,CAAC,CAACP,CAAC,EAACQ,CAAC,CAACP,CAAC,EAACE,CAAC,CAACQ,QAAQ,CAAC,CAAC,CAAC,EAACP,CAAC,CAAC,CAAC,KAAKF,CAAC,YAAYU,IAAI,GAACZ,CAAC,CAACa,IAAI,CAAC,CAACZ,CAAC,EAACC,CAAC,CAACY,WAAW,CAAC,CAAC,CAAC,CAAC,GAAC,OAAOZ,CAAC,IAAE,SAAS,GAACF,CAAC,CAACa,IAAI,CAAC,CAACZ,CAAC,EAACC,CAAC,GAAC,GAAG,GAAC,GAAG,CAAC,CAAC,GAAC,OAAOA,CAAC,IAAE,QAAQ,GAACF,CAAC,CAACa,IAAI,CAAC,CAACZ,CAAC,EAACC,CAAC,CAAC,CAAC,GAAC,OAAOA,CAAC,IAAE,QAAQ,GAACF,CAAC,CAACa,IAAI,CAAC,CAACZ,CAAC,EAAC,GAAGC,CAAC,EAAE,CAAC,CAAC,GAACA,CAAC,IAAE,IAAI,GAACF,CAAC,CAACa,IAAI,CAAC,CAACZ,CAAC,EAAC,EAAE,CAAC,CAAC,GAACF,CAAC,CAACG,CAAC,EAACD,CAAC,EAACD,CAAC,CAAC;AAAA;AAAC,SAASe,CAACA,CAACf,CAAC,EAAC;EAAC,IAAIE,CAAC,EAACC,CAAC;EAAC,IAAIF,CAAC,GAAC,CAACC,CAAC,GAACF,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAACgB,IAAI,KAAG,IAAI,GAACd,CAAC,GAACF,CAAC,CAACiB,OAAO,CAAC,MAAM,CAAC;EAAC,IAAGhB,CAAC,EAAC;IAAC,KAAI,IAAIG,CAAC,IAAIH,CAAC,CAACiB,QAAQ,EAAC,IAAGd,CAAC,KAAGJ,CAAC,KAAGI,CAAC,CAACe,OAAO,KAAG,OAAO,IAAEf,CAAC,CAACgB,IAAI,KAAG,QAAQ,IAAEhB,CAAC,CAACe,OAAO,KAAG,QAAQ,IAAEf,CAAC,CAACgB,IAAI,KAAG,QAAQ,IAAEhB,CAAC,CAACiB,QAAQ,KAAG,OAAO,IAAEjB,CAAC,CAACgB,IAAI,KAAG,OAAO,CAAC,EAAC;MAAChB,CAAC,CAACkB,KAAK,CAAC,CAAC;MAAC;IAAM;IAAC,CAACnB,CAAC,GAACF,CAAC,CAACsB,aAAa,KAAG,IAAI,IAAEpB,CAAC,CAACqB,IAAI,CAACvB,CAAC,CAAC;EAAA;AAAC;AAAC,SAAOc,CAAC,IAAIU,aAAa,EAAC1B,CAAC,IAAI2B,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}