{"ast": null, "code": "import startOfSecond from \"../startOfSecond/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameSecond\n * @category Second Helpers\n * @summary Are the given dates in the same second (and hour and day)?\n *\n * @description\n * Are the given dates in the same second (and hour and day)?\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same second (and hour and day)\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 4 September 2014 06:30:15.000 and 4 September 2014 06:30.15.500 in the same second?\n * const result = isSameSecond(\n *   new Date(2014, 8, 4, 6, 30, 15),\n *   new Date(2014, 8, 4, 6, 30, 15, 500)\n * )\n * //=> true\n *\n * @example\n * // Are 4 September 2014 06:00:15.000 and 4 September 2014 06:01.15.000 in the same second?\n * const result = isSameSecond(\n *   new Date(2014, 8, 4, 6, 0, 15),\n *   new Date(2014, 8, 4, 6, 1, 15)\n * )\n * //=> false\n *\n * @example\n * // Are 4 September 2014 06:00:15.000 and 5 September 2014 06:00.15.000 in the same second?\n * const result = isSameSecond(\n *   new Date(2014, 8, 4, 6, 0, 15),\n *   new Date(2014, 8, 5, 6, 0, 15)\n * )\n * //=> false\n */\nexport default function isSameSecond(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfSecond = startOfSecond(dirtyDateLeft);\n  var dateRightStartOfSecond = startOfSecond(dirtyDateRight);\n  return dateLeftStartOfSecond.getTime() === dateRightStartOfSecond.getTime();\n}", "map": {"version": 3, "names": ["startOfSecond", "requiredArgs", "isSameSecond", "dirtyDateLeft", "dirtyDateRight", "arguments", "dateLeftStartOfSecond", "dateRightStartOfSecond", "getTime"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/date-fns/esm/isSameSecond/index.js"], "sourcesContent": ["import startOfSecond from \"../startOfSecond/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameSecond\n * @category Second Helpers\n * @summary Are the given dates in the same second (and hour and day)?\n *\n * @description\n * Are the given dates in the same second (and hour and day)?\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same second (and hour and day)\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 4 September 2014 06:30:15.000 and 4 September 2014 06:30.15.500 in the same second?\n * const result = isSameSecond(\n *   new Date(2014, 8, 4, 6, 30, 15),\n *   new Date(2014, 8, 4, 6, 30, 15, 500)\n * )\n * //=> true\n *\n * @example\n * // Are 4 September 2014 06:00:15.000 and 4 September 2014 06:01.15.000 in the same second?\n * const result = isSameSecond(\n *   new Date(2014, 8, 4, 6, 0, 15),\n *   new Date(2014, 8, 4, 6, 1, 15)\n * )\n * //=> false\n *\n * @example\n * // Are 4 September 2014 06:00:15.000 and 5 September 2014 06:00.15.000 in the same second?\n * const result = isSameSecond(\n *   new Date(2014, 8, 4, 6, 0, 15),\n *   new Date(2014, 8, 5, 6, 0, 15)\n * )\n * //=> false\n */\nexport default function isSameSecond(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  var dateLeftStartOfSecond = startOfSecond(dirtyDateLeft);\n  var dateRightStartOfSecond = startOfSecond(dirtyDateRight);\n  return dateLeftStartOfSecond.getTime() === dateRightStartOfSecond.getTime();\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,2BAA2B;AACrD,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAACC,aAAa,EAAEC,cAAc,EAAE;EAClEH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,qBAAqB,GAAGN,aAAa,CAACG,aAAa,CAAC;EACxD,IAAII,sBAAsB,GAAGP,aAAa,CAACI,cAAc,CAAC;EAC1D,OAAOE,qBAAqB,CAACE,OAAO,CAAC,CAAC,KAAKD,sBAAsB,CAACC,OAAO,CAAC,CAAC;AAC7E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}