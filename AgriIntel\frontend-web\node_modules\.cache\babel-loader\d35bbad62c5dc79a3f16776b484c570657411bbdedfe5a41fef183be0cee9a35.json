{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.createMemoryCodePoints = createMemoryCodePoints;\nconst sparse_bitfield_1 = __importDefault(require(\"sparse-bitfield\"));\nfunction createMemoryCodePoints(data) {\n  let offset = 0;\n  function read() {\n    const size = data.readUInt32BE(offset);\n    offset += 4;\n    const codepoints = data.slice(offset, offset + size);\n    offset += size;\n    return (0, sparse_bitfield_1.default)({\n      buffer: codepoints\n    });\n  }\n  const unassigned_code_points = read();\n  const commonly_mapped_to_nothing = read();\n  const non_ASCII_space_characters = read();\n  const prohibited_characters = read();\n  const bidirectional_r_al = read();\n  const bidirectional_l = read();\n  return {\n    unassigned_code_points,\n    commonly_mapped_to_nothing,\n    non_ASCII_space_characters,\n    prohibited_characters,\n    bidirectional_r_al,\n    bidirectional_l\n  };\n}", "map": {"version": 3, "names": ["exports", "createMemoryCodePoints", "sparse_bitfield_1", "__importDefault", "require", "data", "offset", "read", "size", "readUInt32BE", "codepoints", "slice", "default", "buffer", "unassigned_code_points", "commonly_mapped_to_nothing", "non_ASCII_space_characters", "prohibited_characters", "bidirectional_r_al", "bidirectional_l"], "sources": ["../src/memory-code-points.ts"], "sourcesContent": [null], "mappings": ";;;;;;;;;;AAEAA,OAAA,CAAAC,sBAAA,GAAAA,sBAAA;AAFA,MAAAC,iBAAA,GAAAC,eAAA,CAAAC,OAAA;AAEA,SAAgBH,sBAAsBA,CAACI,IAAY;EACjD,IAAIC,MAAM,GAAG,CAAC;EAKd,SAASC,IAAIA,CAAA;IACX,MAAMC,IAAI,GAAGH,IAAI,CAACI,YAAY,CAACH,MAAM,CAAC;IACtCA,MAAM,IAAI,CAAC;IAEX,MAAMI,UAAU,GAAGL,IAAI,CAACM,KAAK,CAACL,MAAM,EAAEA,MAAM,GAAGE,IAAI,CAAC;IACpDF,MAAM,IAAIE,IAAI;IAEd,OAAO,IAAAN,iBAAA,CAAAU,OAAQ,EAAC;MAAEC,MAAM,EAAEH;IAAU,CAAE,CAAC;EACzC;EAEA,MAAMI,sBAAsB,GAAGP,IAAI,EAAE;EACrC,MAAMQ,0BAA0B,GAAGR,IAAI,EAAE;EACzC,MAAMS,0BAA0B,GAAGT,IAAI,EAAE;EACzC,MAAMU,qBAAqB,GAAGV,IAAI,EAAE;EACpC,MAAMW,kBAAkB,GAAGX,IAAI,EAAE;EACjC,MAAMY,eAAe,GAAGZ,IAAI,EAAE;EAE9B,OAAO;IACLO,sBAAsB;IACtBC,0BAA0B;IAC1BC,0BAA0B;IAC1BC,qBAAqB;IACrBC,kBAAkB;IAClBC;GACD;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}