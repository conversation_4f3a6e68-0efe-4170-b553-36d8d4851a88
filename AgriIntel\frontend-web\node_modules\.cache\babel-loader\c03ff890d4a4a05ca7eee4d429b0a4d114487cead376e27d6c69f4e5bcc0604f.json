{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.getReadPreference = getReadPreference;\nexports.isSharded = isSharded;\nconst error_1 = require(\"../../error\");\nconst read_preference_1 = require(\"../../read_preference\");\nconst common_1 = require(\"../../sdam/common\");\nconst topology_description_1 = require(\"../../sdam/topology_description\");\nfunction getReadPreference(options) {\n  // Default to command version of the readPreference.\n  let readPreference = options?.readPreference ?? read_preference_1.ReadPreference.primary;\n  if (typeof readPreference === 'string') {\n    readPreference = read_preference_1.ReadPreference.fromString(readPreference);\n  }\n  if (!(readPreference instanceof read_preference_1.ReadPreference)) {\n    throw new error_1.MongoInvalidArgumentError('Option \"readPreference\" must be a ReadPreference instance');\n  }\n  return readPreference;\n}\nfunction isSharded(topologyOrServer) {\n  if (topologyOrServer == null) {\n    return false;\n  }\n  if (topologyOrServer.description && topologyOrServer.description.type === common_1.ServerType.Mongos) {\n    return true;\n  }\n  // NOTE: This is incredibly inefficient, and should be removed once command construction\n  // happens based on `Server` not `Topology`.\n  if (topologyOrServer.description && topologyOrServer.description instanceof topology_description_1.TopologyDescription) {\n    const servers = Array.from(topologyOrServer.description.servers.values());\n    return servers.some(server => server.type === common_1.ServerType.Mongos);\n  }\n  return false;\n}", "map": {"version": 3, "names": ["exports", "getReadPreference", "isSharded", "error_1", "require", "read_preference_1", "common_1", "topology_description_1", "options", "readPreference", "ReadPreference", "primary", "fromString", "MongoInvalidArgumentError", "topologyOrServer", "description", "type", "ServerType", "Mongos", "TopologyDescription", "servers", "Array", "from", "values", "some", "server"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\wire_protocol\\shared.ts"], "sourcesContent": ["import { MongoInvalidArgumentError } from '../../error';\nimport { ReadPreference, type ReadPreferenceLike } from '../../read_preference';\nimport { ServerType } from '../../sdam/common';\nimport type { Server } from '../../sdam/server';\nimport type { ServerDescription } from '../../sdam/server_description';\nimport type { Topology } from '../../sdam/topology';\nimport { TopologyDescription } from '../../sdam/topology_description';\nimport type { Connection } from '../connection';\n\nexport interface ReadPreferenceOption {\n  readPreference?: ReadPreferenceLike;\n}\n\nexport function getReadPreference(options?: ReadPreferenceOption): ReadPreference {\n  // Default to command version of the readPreference.\n  let readPreference = options?.readPreference ?? ReadPreference.primary;\n\n  if (typeof readPreference === 'string') {\n    readPreference = ReadPreference.fromString(readPreference);\n  }\n\n  if (!(readPreference instanceof ReadPreference)) {\n    throw new MongoInvalidArgumentError(\n      'Option \"readPreference\" must be a ReadPreference instance'\n    );\n  }\n\n  return readPreference;\n}\n\nexport function isSharded(topologyOrServer?: Topology | Server | Connection): boolean {\n  if (topologyOrServer == null) {\n    return false;\n  }\n\n  if (topologyOrServer.description && topologyOrServer.description.type === ServerType.Mongos) {\n    return true;\n  }\n\n  // NOTE: This is incredibly inefficient, and should be removed once command construction\n  // happens based on `Server` not `Topology`.\n  if (topologyOrServer.description && topologyOrServer.description instanceof TopologyDescription) {\n    const servers: ServerDescription[] = Array.from(topologyOrServer.description.servers.values());\n    return servers.some((server: ServerDescription) => server.type === ServerType.Mongos);\n  }\n\n  return false;\n}\n"], "mappings": ";;;;;AAaAA,OAAA,CAAAC,iBAAA,GAAAA,iBAAA;AAiBAD,OAAA,CAAAE,SAAA,GAAAA,SAAA;AA9BA,MAAAC,OAAA,GAAAC,OAAA;AACA,MAAAC,iBAAA,GAAAD,OAAA;AACA,MAAAE,QAAA,GAAAF,OAAA;AAIA,MAAAG,sBAAA,GAAAH,OAAA;AAOA,SAAgBH,iBAAiBA,CAACO,OAA8B;EAC9D;EACA,IAAIC,cAAc,GAAGD,OAAO,EAAEC,cAAc,IAAIJ,iBAAA,CAAAK,cAAc,CAACC,OAAO;EAEtE,IAAI,OAAOF,cAAc,KAAK,QAAQ,EAAE;IACtCA,cAAc,GAAGJ,iBAAA,CAAAK,cAAc,CAACE,UAAU,CAACH,cAAc,CAAC;EAC5D;EAEA,IAAI,EAAEA,cAAc,YAAYJ,iBAAA,CAAAK,cAAc,CAAC,EAAE;IAC/C,MAAM,IAAIP,OAAA,CAAAU,yBAAyB,CACjC,2DAA2D,CAC5D;EACH;EAEA,OAAOJ,cAAc;AACvB;AAEA,SAAgBP,SAASA,CAACY,gBAAiD;EACzE,IAAIA,gBAAgB,IAAI,IAAI,EAAE;IAC5B,OAAO,KAAK;EACd;EAEA,IAAIA,gBAAgB,CAACC,WAAW,IAAID,gBAAgB,CAACC,WAAW,CAACC,IAAI,KAAKV,QAAA,CAAAW,UAAU,CAACC,MAAM,EAAE;IAC3F,OAAO,IAAI;EACb;EAEA;EACA;EACA,IAAIJ,gBAAgB,CAACC,WAAW,IAAID,gBAAgB,CAACC,WAAW,YAAYR,sBAAA,CAAAY,mBAAmB,EAAE;IAC/F,MAAMC,OAAO,GAAwBC,KAAK,CAACC,IAAI,CAACR,gBAAgB,CAACC,WAAW,CAACK,OAAO,CAACG,MAAM,EAAE,CAAC;IAC9F,OAAOH,OAAO,CAACI,IAAI,CAAEC,MAAyB,IAAKA,MAAM,CAACT,IAAI,KAAKV,QAAA,CAAAW,UAAU,CAACC,MAAM,CAAC;EACvF;EAEA,OAAO,KAAK;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}