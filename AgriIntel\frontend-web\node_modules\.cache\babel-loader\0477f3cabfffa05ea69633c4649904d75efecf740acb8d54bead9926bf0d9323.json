{"ast": null, "code": "import _inherits<PERSON>oose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { hashQueryKeyByOptions, matchQuery, parseFilterArgs } from './utils';\nimport { Query } from './query';\nimport { notifyManager } from './notifyManager';\nimport { Subscribable } from './subscribable';\n// CLASS\nexport var QueryCache = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(QueryCache, _Subscribable);\n  function QueryCache(config) {\n    var _this;\n    _this = _Subscribable.call(this) || this;\n    _this.config = config || {};\n    _this.queries = [];\n    _this.queriesMap = {};\n    return _this;\n  }\n  var _proto = QueryCache.prototype;\n  _proto.build = function build(client, options, state) {\n    var _options$queryHash;\n    var queryKey = options.queryKey;\n    var queryHash = (_options$queryHash = options.queryHash) != null ? _options$queryHash : hashQueryKeyByOptions(queryKey, options);\n    var query = this.get(queryHash);\n    if (!query) {\n      query = new Query({\n        cache: this,\n        queryKey: queryKey,\n        queryHash: queryHash,\n        options: client.defaultQueryOptions(options),\n        state: state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n        meta: options.meta\n      });\n      this.add(query);\n    }\n    return query;\n  };\n  _proto.add = function add(query) {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query;\n      this.queries.push(query);\n      this.notify({\n        type: 'queryAdded',\n        query: query\n      });\n    }\n  };\n  _proto.remove = function remove(query) {\n    var queryInMap = this.queriesMap[query.queryHash];\n    if (queryInMap) {\n      query.destroy();\n      this.queries = this.queries.filter(function (x) {\n        return x !== query;\n      });\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash];\n      }\n      this.notify({\n        type: 'queryRemoved',\n        query: query\n      });\n    }\n  };\n  _proto.clear = function clear() {\n    var _this2 = this;\n    notifyManager.batch(function () {\n      _this2.queries.forEach(function (query) {\n        _this2.remove(query);\n      });\n    });\n  };\n  _proto.get = function get(queryHash) {\n    return this.queriesMap[queryHash];\n  };\n  _proto.getAll = function getAll() {\n    return this.queries;\n  };\n  _proto.find = function find(arg1, arg2) {\n    var _parseFilterArgs = parseFilterArgs(arg1, arg2),\n      filters = _parseFilterArgs[0];\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n    return this.queries.find(function (query) {\n      return matchQuery(filters, query);\n    });\n  };\n  _proto.findAll = function findAll(arg1, arg2) {\n    var _parseFilterArgs2 = parseFilterArgs(arg1, arg2),\n      filters = _parseFilterArgs2[0];\n    return Object.keys(filters).length > 0 ? this.queries.filter(function (query) {\n      return matchQuery(filters, query);\n    }) : this.queries;\n  };\n  _proto.notify = function notify(event) {\n    var _this3 = this;\n    notifyManager.batch(function () {\n      _this3.listeners.forEach(function (listener) {\n        listener(event);\n      });\n    });\n  };\n  _proto.onFocus = function onFocus() {\n    var _this4 = this;\n    notifyManager.batch(function () {\n      _this4.queries.forEach(function (query) {\n        query.onFocus();\n      });\n    });\n  };\n  _proto.onOnline = function onOnline() {\n    var _this5 = this;\n    notifyManager.batch(function () {\n      _this5.queries.forEach(function (query) {\n        query.onOnline();\n      });\n    });\n  };\n  return QueryCache;\n}(Subscribable);", "map": {"version": 3, "names": ["_inherits<PERSON><PERSON>e", "hashQueryKeyByOptions", "matchQuery", "parseFilter<PERSON><PERSON>s", "Query", "notify<PERSON><PERSON>ger", "Subscribable", "Query<PERSON>ache", "_Subscribable", "config", "_this", "call", "queries", "queriesMap", "_proto", "prototype", "build", "client", "options", "state", "_options$queryHash", "query<PERSON><PERSON>", "queryHash", "query", "get", "cache", "defaultQueryOptions", "defaultOptions", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "meta", "add", "push", "notify", "type", "remove", "queryInMap", "destroy", "filter", "x", "clear", "_this2", "batch", "for<PERSON>ach", "getAll", "find", "arg1", "arg2", "_parseFilterArgs", "filters", "exact", "findAll", "_parseFilterArgs2", "Object", "keys", "length", "event", "_this3", "listeners", "listener", "onFocus", "_this4", "onOnline", "_this5"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-query/es/core/queryCache.js"], "sourcesContent": ["import _inherits<PERSON>oose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { hashQueryKeyByOptions, matchQuery, parseFilterArgs } from './utils';\nimport { Query } from './query';\nimport { notifyManager } from './notifyManager';\nimport { Subscribable } from './subscribable';\n// CLASS\nexport var QueryCache = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(QueryCache, _Subscribable);\n\n  function QueryCache(config) {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n    _this.config = config || {};\n    _this.queries = [];\n    _this.queriesMap = {};\n    return _this;\n  }\n\n  var _proto = QueryCache.prototype;\n\n  _proto.build = function build(client, options, state) {\n    var _options$queryHash;\n\n    var queryKey = options.queryKey;\n    var queryHash = (_options$queryHash = options.queryHash) != null ? _options$queryHash : hashQueryKeyByOptions(queryKey, options);\n    var query = this.get(queryHash);\n\n    if (!query) {\n      query = new Query({\n        cache: this,\n        queryKey: queryKey,\n        queryHash: queryHash,\n        options: client.defaultQueryOptions(options),\n        state: state,\n        defaultOptions: client.getQueryDefaults(queryKey),\n        meta: options.meta\n      });\n      this.add(query);\n    }\n\n    return query;\n  };\n\n  _proto.add = function add(query) {\n    if (!this.queriesMap[query.queryHash]) {\n      this.queriesMap[query.queryHash] = query;\n      this.queries.push(query);\n      this.notify({\n        type: 'queryAdded',\n        query: query\n      });\n    }\n  };\n\n  _proto.remove = function remove(query) {\n    var queryInMap = this.queriesMap[query.queryHash];\n\n    if (queryInMap) {\n      query.destroy();\n      this.queries = this.queries.filter(function (x) {\n        return x !== query;\n      });\n\n      if (queryInMap === query) {\n        delete this.queriesMap[query.queryHash];\n      }\n\n      this.notify({\n        type: 'queryRemoved',\n        query: query\n      });\n    }\n  };\n\n  _proto.clear = function clear() {\n    var _this2 = this;\n\n    notifyManager.batch(function () {\n      _this2.queries.forEach(function (query) {\n        _this2.remove(query);\n      });\n    });\n  };\n\n  _proto.get = function get(queryHash) {\n    return this.queriesMap[queryHash];\n  };\n\n  _proto.getAll = function getAll() {\n    return this.queries;\n  };\n\n  _proto.find = function find(arg1, arg2) {\n    var _parseFilterArgs = parseFilterArgs(arg1, arg2),\n        filters = _parseFilterArgs[0];\n\n    if (typeof filters.exact === 'undefined') {\n      filters.exact = true;\n    }\n\n    return this.queries.find(function (query) {\n      return matchQuery(filters, query);\n    });\n  };\n\n  _proto.findAll = function findAll(arg1, arg2) {\n    var _parseFilterArgs2 = parseFilterArgs(arg1, arg2),\n        filters = _parseFilterArgs2[0];\n\n    return Object.keys(filters).length > 0 ? this.queries.filter(function (query) {\n      return matchQuery(filters, query);\n    }) : this.queries;\n  };\n\n  _proto.notify = function notify(event) {\n    var _this3 = this;\n\n    notifyManager.batch(function () {\n      _this3.listeners.forEach(function (listener) {\n        listener(event);\n      });\n    });\n  };\n\n  _proto.onFocus = function onFocus() {\n    var _this4 = this;\n\n    notifyManager.batch(function () {\n      _this4.queries.forEach(function (query) {\n        query.onFocus();\n      });\n    });\n  };\n\n  _proto.onOnline = function onOnline() {\n    var _this5 = this;\n\n    notifyManager.batch(function () {\n      _this5.queries.forEach(function (query) {\n        query.onOnline();\n      });\n    });\n  };\n\n  return QueryCache;\n}(Subscribable);"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,qBAAqB,EAAEC,UAAU,EAAEC,eAAe,QAAQ,SAAS;AAC5E,SAASC,KAAK,QAAQ,SAAS;AAC/B,SAASC,aAAa,QAAQ,iBAAiB;AAC/C,SAASC,YAAY,QAAQ,gBAAgB;AAC7C;AACA,OAAO,IAAIC,UAAU,GAAG,aAAa,UAAUC,aAAa,EAAE;EAC5DR,cAAc,CAACO,UAAU,EAAEC,aAAa,CAAC;EAEzC,SAASD,UAAUA,CAACE,MAAM,EAAE;IAC1B,IAAIC,KAAK;IAETA,KAAK,GAAGF,aAAa,CAACG,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IACxCD,KAAK,CAACD,MAAM,GAAGA,MAAM,IAAI,CAAC,CAAC;IAC3BC,KAAK,CAACE,OAAO,GAAG,EAAE;IAClBF,KAAK,CAACG,UAAU,GAAG,CAAC,CAAC;IACrB,OAAOH,KAAK;EACd;EAEA,IAAII,MAAM,GAAGP,UAAU,CAACQ,SAAS;EAEjCD,MAAM,CAACE,KAAK,GAAG,SAASA,KAAKA,CAACC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAE;IACpD,IAAIC,kBAAkB;IAEtB,IAAIC,QAAQ,GAAGH,OAAO,CAACG,QAAQ;IAC/B,IAAIC,SAAS,GAAG,CAACF,kBAAkB,GAAGF,OAAO,CAACI,SAAS,KAAK,IAAI,GAAGF,kBAAkB,GAAGnB,qBAAqB,CAACoB,QAAQ,EAAEH,OAAO,CAAC;IAChI,IAAIK,KAAK,GAAG,IAAI,CAACC,GAAG,CAACF,SAAS,CAAC;IAE/B,IAAI,CAACC,KAAK,EAAE;MACVA,KAAK,GAAG,IAAInB,KAAK,CAAC;QAChBqB,KAAK,EAAE,IAAI;QACXJ,QAAQ,EAAEA,QAAQ;QAClBC,SAAS,EAAEA,SAAS;QACpBJ,OAAO,EAAED,MAAM,CAACS,mBAAmB,CAACR,OAAO,CAAC;QAC5CC,KAAK,EAAEA,KAAK;QACZQ,cAAc,EAAEV,MAAM,CAACW,gBAAgB,CAACP,QAAQ,CAAC;QACjDQ,IAAI,EAAEX,OAAO,CAACW;MAChB,CAAC,CAAC;MACF,IAAI,CAACC,GAAG,CAACP,KAAK,CAAC;IACjB;IAEA,OAAOA,KAAK;EACd,CAAC;EAEDT,MAAM,CAACgB,GAAG,GAAG,SAASA,GAAGA,CAACP,KAAK,EAAE;IAC/B,IAAI,CAAC,IAAI,CAACV,UAAU,CAACU,KAAK,CAACD,SAAS,CAAC,EAAE;MACrC,IAAI,CAACT,UAAU,CAACU,KAAK,CAACD,SAAS,CAAC,GAAGC,KAAK;MACxC,IAAI,CAACX,OAAO,CAACmB,IAAI,CAACR,KAAK,CAAC;MACxB,IAAI,CAACS,MAAM,CAAC;QACVC,IAAI,EAAE,YAAY;QAClBV,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EAEDT,MAAM,CAACoB,MAAM,GAAG,SAASA,MAAMA,CAACX,KAAK,EAAE;IACrC,IAAIY,UAAU,GAAG,IAAI,CAACtB,UAAU,CAACU,KAAK,CAACD,SAAS,CAAC;IAEjD,IAAIa,UAAU,EAAE;MACdZ,KAAK,CAACa,OAAO,CAAC,CAAC;MACf,IAAI,CAACxB,OAAO,GAAG,IAAI,CAACA,OAAO,CAACyB,MAAM,CAAC,UAAUC,CAAC,EAAE;QAC9C,OAAOA,CAAC,KAAKf,KAAK;MACpB,CAAC,CAAC;MAEF,IAAIY,UAAU,KAAKZ,KAAK,EAAE;QACxB,OAAO,IAAI,CAACV,UAAU,CAACU,KAAK,CAACD,SAAS,CAAC;MACzC;MAEA,IAAI,CAACU,MAAM,CAAC;QACVC,IAAI,EAAE,cAAc;QACpBV,KAAK,EAAEA;MACT,CAAC,CAAC;IACJ;EACF,CAAC;EAEDT,MAAM,CAACyB,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC9B,IAAIC,MAAM,GAAG,IAAI;IAEjBnC,aAAa,CAACoC,KAAK,CAAC,YAAY;MAC9BD,MAAM,CAAC5B,OAAO,CAAC8B,OAAO,CAAC,UAAUnB,KAAK,EAAE;QACtCiB,MAAM,CAACN,MAAM,CAACX,KAAK,CAAC;MACtB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAEDT,MAAM,CAACU,GAAG,GAAG,SAASA,GAAGA,CAACF,SAAS,EAAE;IACnC,OAAO,IAAI,CAACT,UAAU,CAACS,SAAS,CAAC;EACnC,CAAC;EAEDR,MAAM,CAAC6B,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAChC,OAAO,IAAI,CAAC/B,OAAO;EACrB,CAAC;EAEDE,MAAM,CAAC8B,IAAI,GAAG,SAASA,IAAIA,CAACC,IAAI,EAAEC,IAAI,EAAE;IACtC,IAAIC,gBAAgB,GAAG5C,eAAe,CAAC0C,IAAI,EAAEC,IAAI,CAAC;MAC9CE,OAAO,GAAGD,gBAAgB,CAAC,CAAC,CAAC;IAEjC,IAAI,OAAOC,OAAO,CAACC,KAAK,KAAK,WAAW,EAAE;MACxCD,OAAO,CAACC,KAAK,GAAG,IAAI;IACtB;IAEA,OAAO,IAAI,CAACrC,OAAO,CAACgC,IAAI,CAAC,UAAUrB,KAAK,EAAE;MACxC,OAAOrB,UAAU,CAAC8C,OAAO,EAAEzB,KAAK,CAAC;IACnC,CAAC,CAAC;EACJ,CAAC;EAEDT,MAAM,CAACoC,OAAO,GAAG,SAASA,OAAOA,CAACL,IAAI,EAAEC,IAAI,EAAE;IAC5C,IAAIK,iBAAiB,GAAGhD,eAAe,CAAC0C,IAAI,EAAEC,IAAI,CAAC;MAC/CE,OAAO,GAAGG,iBAAiB,CAAC,CAAC,CAAC;IAElC,OAAOC,MAAM,CAACC,IAAI,CAACL,OAAO,CAAC,CAACM,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC1C,OAAO,CAACyB,MAAM,CAAC,UAAUd,KAAK,EAAE;MAC5E,OAAOrB,UAAU,CAAC8C,OAAO,EAAEzB,KAAK,CAAC;IACnC,CAAC,CAAC,GAAG,IAAI,CAACX,OAAO;EACnB,CAAC;EAEDE,MAAM,CAACkB,MAAM,GAAG,SAASA,MAAMA,CAACuB,KAAK,EAAE;IACrC,IAAIC,MAAM,GAAG,IAAI;IAEjBnD,aAAa,CAACoC,KAAK,CAAC,YAAY;MAC9Be,MAAM,CAACC,SAAS,CAACf,OAAO,CAAC,UAAUgB,QAAQ,EAAE;QAC3CA,QAAQ,CAACH,KAAK,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAEDzC,MAAM,CAAC6C,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAClC,IAAIC,MAAM,GAAG,IAAI;IAEjBvD,aAAa,CAACoC,KAAK,CAAC,YAAY;MAC9BmB,MAAM,CAAChD,OAAO,CAAC8B,OAAO,CAAC,UAAUnB,KAAK,EAAE;QACtCA,KAAK,CAACoC,OAAO,CAAC,CAAC;MACjB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAED7C,MAAM,CAAC+C,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IACpC,IAAIC,MAAM,GAAG,IAAI;IAEjBzD,aAAa,CAACoC,KAAK,CAAC,YAAY;MAC9BqB,MAAM,CAAClD,OAAO,CAAC8B,OAAO,CAAC,UAAUnB,KAAK,EAAE;QACtCA,KAAK,CAACsC,QAAQ,CAAC,CAAC;MAClB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC;EAED,OAAOtD,UAAU;AACnB,CAAC,CAACD,YAAY,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}