{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.executeOperation = executeOperation;\nconst error_1 = require(\"../error\");\nconst read_preference_1 = require(\"../read_preference\");\nconst server_selection_1 = require(\"../sdam/server_selection\");\nconst timeout_1 = require(\"../timeout\");\nconst utils_1 = require(\"../utils\");\nconst operation_1 = require(\"./operation\");\nconst MMAPv1_RETRY_WRITES_ERROR_CODE = error_1.MONGODB_ERROR_CODES.IllegalOperation;\nconst MMAPv1_RETRY_WRITES_ERROR_MESSAGE = 'This MongoDB deployment does not support retryable writes. Please add retryWrites=false to your connection string.';\n/**\n * Executes the given operation with provided arguments.\n * @internal\n *\n * @remarks\n * Allows for a single point of entry to provide features such as implicit sessions, which\n * are required by the Driver Sessions specification in the event that a ClientSession is\n * not provided.\n *\n * The expectation is that this function:\n * - Connects the MongoClient if it has not already been connected, see {@link autoConnect}\n * - Creates a session if none is provided and cleans up the session it creates\n * - Tries an operation and retries under certain conditions, see {@link tryOperation}\n *\n * @typeParam T - The operation's type\n * @typeParam TResult - The type of the operation's result, calculated from T\n *\n * @param client - The MongoClient to execute this operation with\n * @param operation - The operation to execute\n */\nasync function executeOperation(client, operation, timeoutContext) {\n  if (!(operation instanceof operation_1.AbstractOperation)) {\n    // TODO(NODE-3483): Extend MongoRuntimeError\n    throw new error_1.MongoRuntimeError('This method requires a valid operation instance');\n  }\n  const topology = client.topology == null ? await (0, utils_1.abortable)(autoConnect(client), operation.options) : client.topology;\n  // The driver sessions spec mandates that we implicitly create sessions for operations\n  // that are not explicitly provided with a session.\n  let session = operation.session;\n  let owner;\n  if (session == null) {\n    owner = Symbol();\n    session = client.startSession({\n      owner,\n      explicit: false\n    });\n  } else if (session.hasEnded) {\n    throw new error_1.MongoExpiredSessionError('Use of expired sessions is not permitted');\n  } else if (session.snapshotEnabled && !topology.capabilities.supportsSnapshotReads) {\n    throw new error_1.MongoCompatibilityError('Snapshot reads require MongoDB 5.0 or later');\n  } else if (session.client !== client) {\n    throw new error_1.MongoInvalidArgumentError('ClientSession must be from the same MongoClient');\n  }\n  const readPreference = operation.readPreference ?? read_preference_1.ReadPreference.primary;\n  const inTransaction = !!session?.inTransaction();\n  const hasReadAspect = operation.hasAspect(operation_1.Aspect.READ_OPERATION);\n  if (inTransaction && !readPreference.equals(read_preference_1.ReadPreference.primary) && (hasReadAspect || operation.commandName === 'runCommand')) {\n    throw new error_1.MongoTransactionError(`Read preference in a transaction must be primary, not: ${readPreference.mode}`);\n  }\n  if (session?.isPinned && session.transaction.isCommitted && !operation.bypassPinningCheck) {\n    session.unpin();\n  }\n  timeoutContext ??= timeout_1.TimeoutContext.create({\n    session,\n    serverSelectionTimeoutMS: client.s.options.serverSelectionTimeoutMS,\n    waitQueueTimeoutMS: client.s.options.waitQueueTimeoutMS,\n    timeoutMS: operation.options.timeoutMS\n  });\n  try {\n    return await tryOperation(operation, {\n      topology,\n      timeoutContext,\n      session,\n      readPreference\n    });\n  } finally {\n    if (session?.owner != null && session.owner === owner) {\n      await session.endSession();\n    }\n  }\n}\n/**\n * Connects a client if it has not yet been connected\n * @internal\n */\nasync function autoConnect(client) {\n  if (client.topology == null) {\n    if (client.s.hasBeenClosed) {\n      throw new error_1.MongoNotConnectedError('Client must be connected before running operations');\n    }\n    client.s.options.__skipPingOnConnect = true;\n    try {\n      await client.connect();\n      if (client.topology == null) {\n        throw new error_1.MongoRuntimeError('client.connect did not create a topology but also did not throw');\n      }\n      return client.topology;\n    } finally {\n      delete client.s.options.__skipPingOnConnect;\n    }\n  }\n  return client.topology;\n}\n/**\n * Executes an operation and retries as appropriate\n * @internal\n *\n * @remarks\n * Implements behaviour described in [Retryable Reads](https://github.com/mongodb/specifications/blob/master/source/retryable-reads/retryable-reads.md) and [Retryable\n * Writes](https://github.com/mongodb/specifications/blob/master/source/retryable-writes/retryable-writes.md) specification\n *\n * This function:\n * - performs initial server selection\n * - attempts to execute an operation\n * - retries the operation if it meets the criteria for a retryable read or a retryable write\n *\n * @typeParam T - The operation's type\n * @typeParam TResult - The type of the operation's result, calculated from T\n *\n * @param operation - The operation to execute\n * */\nasync function tryOperation(operation, {\n  topology,\n  timeoutContext,\n  session,\n  readPreference\n}) {\n  let selector;\n  if (operation.hasAspect(operation_1.Aspect.MUST_SELECT_SAME_SERVER)) {\n    // GetMore and KillCursor operations must always select the same server, but run through\n    // server selection to potentially force monitor checks if the server is\n    // in an unknown state.\n    selector = (0, server_selection_1.sameServerSelector)(operation.server?.description);\n  } else if (operation.trySecondaryWrite) {\n    // If operation should try to write to secondary use the custom server selector\n    // otherwise provide the read preference.\n    selector = (0, server_selection_1.secondaryWritableServerSelector)(topology.commonWireVersion, readPreference);\n  } else {\n    selector = readPreference;\n  }\n  let server = await topology.selectServer(selector, {\n    session,\n    operationName: operation.commandName,\n    timeoutContext,\n    signal: operation.options.signal\n  });\n  const hasReadAspect = operation.hasAspect(operation_1.Aspect.READ_OPERATION);\n  const hasWriteAspect = operation.hasAspect(operation_1.Aspect.WRITE_OPERATION);\n  const inTransaction = session?.inTransaction() ?? false;\n  const willRetryRead = topology.s.options.retryReads && !inTransaction && operation.canRetryRead;\n  const willRetryWrite = topology.s.options.retryWrites && !inTransaction && (0, utils_1.supportsRetryableWrites)(server) && operation.canRetryWrite;\n  const willRetry = operation.hasAspect(operation_1.Aspect.RETRYABLE) && session != null && (hasReadAspect && willRetryRead || hasWriteAspect && willRetryWrite);\n  if (hasWriteAspect && willRetryWrite && session != null) {\n    operation.options.willRetryWrite = true;\n    session.incrementTransactionNumber();\n  }\n  const maxTries = willRetry ? timeoutContext.csotEnabled() ? Infinity : 2 : 1;\n  let previousOperationError;\n  let previousServer;\n  for (let tries = 0; tries < maxTries; tries++) {\n    if (previousOperationError) {\n      if (hasWriteAspect && previousOperationError.code === MMAPv1_RETRY_WRITES_ERROR_CODE) {\n        throw new error_1.MongoServerError({\n          message: MMAPv1_RETRY_WRITES_ERROR_MESSAGE,\n          errmsg: MMAPv1_RETRY_WRITES_ERROR_MESSAGE,\n          originalError: previousOperationError\n        });\n      }\n      if (operation.hasAspect(operation_1.Aspect.COMMAND_BATCHING) && !operation.canRetryWrite) {\n        throw previousOperationError;\n      }\n      if (hasWriteAspect && !(0, error_1.isRetryableWriteError)(previousOperationError)) throw previousOperationError;\n      if (hasReadAspect && !(0, error_1.isRetryableReadError)(previousOperationError)) throw previousOperationError;\n      if (previousOperationError instanceof error_1.MongoNetworkError && operation.hasAspect(operation_1.Aspect.CURSOR_CREATING) && session != null && session.isPinned && !session.inTransaction()) {\n        session.unpin({\n          force: true,\n          forceClear: true\n        });\n      }\n      server = await topology.selectServer(selector, {\n        session,\n        operationName: operation.commandName,\n        previousServer,\n        signal: operation.options.signal\n      });\n      if (hasWriteAspect && !(0, utils_1.supportsRetryableWrites)(server)) {\n        throw new error_1.MongoUnexpectedServerResponseError('Selected server does not support retryable writes');\n      }\n    }\n    try {\n      // If tries > 0 and we are command batching we need to reset the batch.\n      if (tries > 0 && operation.hasAspect(operation_1.Aspect.COMMAND_BATCHING)) {\n        operation.resetBatch();\n      }\n      return await operation.execute(server, session, timeoutContext);\n    } catch (operationError) {\n      if (!(operationError instanceof error_1.MongoError)) throw operationError;\n      if (previousOperationError != null && operationError.hasErrorLabel(error_1.MongoErrorLabel.NoWritesPerformed)) {\n        throw previousOperationError;\n      }\n      previousServer = server.description;\n      previousOperationError = operationError;\n      // Reset timeouts\n      timeoutContext.clear();\n    }\n  }\n  throw previousOperationError ?? new error_1.MongoRuntimeError('Tried to propagate retryability error, but no error was found.');\n}", "map": {"version": 3, "names": ["exports", "executeOperation", "error_1", "require", "read_preference_1", "server_selection_1", "timeout_1", "utils_1", "operation_1", "MMAPv1_RETRY_WRITES_ERROR_CODE", "MONGODB_ERROR_CODES", "IllegalOperation", "MMAPv1_RETRY_WRITES_ERROR_MESSAGE", "client", "operation", "timeoutContext", "AbstractOperation", "MongoRuntimeError", "topology", "abortable", "autoConnect", "options", "session", "owner", "Symbol", "startSession", "explicit", "hasEnded", "MongoExpiredSessionError", "snapshotEnabled", "capabilities", "supportsSnapshotReads", "MongoCompatibilityError", "MongoInvalidArgumentError", "readPreference", "ReadPreference", "primary", "inTransaction", "hasReadAspect", "hasAspect", "Aspect", "READ_OPERATION", "equals", "commandName", "MongoTransactionError", "mode", "isPinned", "transaction", "isCommitted", "bypassPinningCheck", "unpin", "TimeoutContext", "create", "serverSelectionTimeoutMS", "s", "waitQueueTimeoutMS", "timeoutMS", "tryOperation", "endSession", "hasBeenClosed", "MongoNotConnectedError", "__skipPingOnConnect", "connect", "selector", "MUST_SELECT_SAME_SERVER", "sameServerSelector", "server", "description", "trySecondaryWrite", "secondaryWritableServerSelector", "commonWireVersion", "selectServer", "operationName", "signal", "hasWriteAspect", "WRITE_OPERATION", "willRetryRead", "retryReads", "canRetryRead", "willRetryWrite", "retryWrites", "supportsRetryableWrites", "canRetryWrite", "willRetry", "RETRYABLE", "incrementTransactionNumber", "max<PERSON>ries", "csotEnabled", "Infinity", "previousOperationError", "previousServer", "tries", "code", "MongoServerError", "message", "errmsg", "originalError", "COMMAND_BATCHING", "isRetryableWriteError", "isRetryableReadError", "MongoNetworkError", "CURSOR_CREATING", "force", "forceClear", "MongoUnexpectedServerResponseError", "resetBatch", "execute", "operationError", "MongoError", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MongoErrorLabel", "NoWritesPerformed", "clear"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\execute_operation.ts"], "sourcesContent": ["import {\n  isRetryableReadError,\n  isRetryableWriteError,\n  MongoCompatibilityError,\n  MONGODB_ERROR_CODES,\n  MongoError,\n  MongoErrorLabel,\n  MongoExpiredSessionError,\n  MongoInvalidArgumentError,\n  MongoNetworkError,\n  MongoNotConnectedError,\n  MongoRuntimeError,\n  MongoServerError,\n  MongoTransactionError,\n  MongoUnexpectedServerResponseError\n} from '../error';\nimport type { MongoClient } from '../mongo_client';\nimport { ReadPreference } from '../read_preference';\nimport type { ServerDescription } from '../sdam/server_description';\nimport {\n  sameServerSelector,\n  secondaryWritableServerSelector,\n  type ServerSelector\n} from '../sdam/server_selection';\nimport type { Topology } from '../sdam/topology';\nimport type { ClientSession } from '../sessions';\nimport { TimeoutContext } from '../timeout';\nimport { abortable, supportsRetryableWrites } from '../utils';\nimport { AbstractOperation, Aspect } from './operation';\n\nconst MMAPv1_RETRY_WRITES_ERROR_CODE = MONGODB_ERROR_CODES.IllegalOperation;\nconst MMAPv1_RETRY_WRITES_ERROR_MESSAGE =\n  'This MongoDB deployment does not support retryable writes. Please add retryWrites=false to your connection string.';\n\ntype ResultTypeFromOperation<TOperation> =\n  TOperation extends AbstractOperation<infer K> ? K : never;\n\n/**\n * Executes the given operation with provided arguments.\n * @internal\n *\n * @remarks\n * Allows for a single point of entry to provide features such as implicit sessions, which\n * are required by the Driver Sessions specification in the event that a ClientSession is\n * not provided.\n *\n * The expectation is that this function:\n * - Connects the MongoClient if it has not already been connected, see {@link autoConnect}\n * - Creates a session if none is provided and cleans up the session it creates\n * - Tries an operation and retries under certain conditions, see {@link tryOperation}\n *\n * @typeParam T - The operation's type\n * @typeParam TResult - The type of the operation's result, calculated from T\n *\n * @param client - The MongoClient to execute this operation with\n * @param operation - The operation to execute\n */\nexport async function executeOperation<\n  T extends AbstractOperation<TResult>,\n  TResult = ResultTypeFromOperation<T>\n>(client: MongoClient, operation: T, timeoutContext?: TimeoutContext | null): Promise<TResult> {\n  if (!(operation instanceof AbstractOperation)) {\n    // TODO(NODE-3483): Extend MongoRuntimeError\n    throw new MongoRuntimeError('This method requires a valid operation instance');\n  }\n\n  const topology =\n    client.topology == null\n      ? await abortable(autoConnect(client), operation.options)\n      : client.topology;\n\n  // The driver sessions spec mandates that we implicitly create sessions for operations\n  // that are not explicitly provided with a session.\n  let session = operation.session;\n  let owner: symbol | undefined;\n\n  if (session == null) {\n    owner = Symbol();\n    session = client.startSession({ owner, explicit: false });\n  } else if (session.hasEnded) {\n    throw new MongoExpiredSessionError('Use of expired sessions is not permitted');\n  } else if (session.snapshotEnabled && !topology.capabilities.supportsSnapshotReads) {\n    throw new MongoCompatibilityError('Snapshot reads require MongoDB 5.0 or later');\n  } else if (session.client !== client) {\n    throw new MongoInvalidArgumentError('ClientSession must be from the same MongoClient');\n  }\n\n  const readPreference = operation.readPreference ?? ReadPreference.primary;\n  const inTransaction = !!session?.inTransaction();\n\n  const hasReadAspect = operation.hasAspect(Aspect.READ_OPERATION);\n\n  if (\n    inTransaction &&\n    !readPreference.equals(ReadPreference.primary) &&\n    (hasReadAspect || operation.commandName === 'runCommand')\n  ) {\n    throw new MongoTransactionError(\n      `Read preference in a transaction must be primary, not: ${readPreference.mode}`\n    );\n  }\n\n  if (session?.isPinned && session.transaction.isCommitted && !operation.bypassPinningCheck) {\n    session.unpin();\n  }\n\n  timeoutContext ??= TimeoutContext.create({\n    session,\n    serverSelectionTimeoutMS: client.s.options.serverSelectionTimeoutMS,\n    waitQueueTimeoutMS: client.s.options.waitQueueTimeoutMS,\n    timeoutMS: operation.options.timeoutMS\n  });\n\n  try {\n    return await tryOperation(operation, {\n      topology,\n      timeoutContext,\n      session,\n      readPreference\n    });\n  } finally {\n    if (session?.owner != null && session.owner === owner) {\n      await session.endSession();\n    }\n  }\n}\n\n/**\n * Connects a client if it has not yet been connected\n * @internal\n */\nasync function autoConnect(client: MongoClient): Promise<Topology> {\n  if (client.topology == null) {\n    if (client.s.hasBeenClosed) {\n      throw new MongoNotConnectedError('Client must be connected before running operations');\n    }\n    client.s.options.__skipPingOnConnect = true;\n    try {\n      await client.connect();\n      if (client.topology == null) {\n        throw new MongoRuntimeError(\n          'client.connect did not create a topology but also did not throw'\n        );\n      }\n      return client.topology;\n    } finally {\n      delete client.s.options.__skipPingOnConnect;\n    }\n  }\n  return client.topology;\n}\n\n/** @internal */\ntype RetryOptions = {\n  session: ClientSession | undefined;\n  readPreference: ReadPreference;\n  topology: Topology;\n  timeoutContext: TimeoutContext;\n};\n\n/**\n * Executes an operation and retries as appropriate\n * @internal\n *\n * @remarks\n * Implements behaviour described in [Retryable Reads](https://github.com/mongodb/specifications/blob/master/source/retryable-reads/retryable-reads.md) and [Retryable\n * Writes](https://github.com/mongodb/specifications/blob/master/source/retryable-writes/retryable-writes.md) specification\n *\n * This function:\n * - performs initial server selection\n * - attempts to execute an operation\n * - retries the operation if it meets the criteria for a retryable read or a retryable write\n *\n * @typeParam T - The operation's type\n * @typeParam TResult - The type of the operation's result, calculated from T\n *\n * @param operation - The operation to execute\n * */\nasync function tryOperation<\n  T extends AbstractOperation<TResult>,\n  TResult = ResultTypeFromOperation<T>\n>(\n  operation: T,\n  { topology, timeoutContext, session, readPreference }: RetryOptions\n): Promise<TResult> {\n  let selector: ReadPreference | ServerSelector;\n\n  if (operation.hasAspect(Aspect.MUST_SELECT_SAME_SERVER)) {\n    // GetMore and KillCursor operations must always select the same server, but run through\n    // server selection to potentially force monitor checks if the server is\n    // in an unknown state.\n    selector = sameServerSelector(operation.server?.description);\n  } else if (operation.trySecondaryWrite) {\n    // If operation should try to write to secondary use the custom server selector\n    // otherwise provide the read preference.\n    selector = secondaryWritableServerSelector(topology.commonWireVersion, readPreference);\n  } else {\n    selector = readPreference;\n  }\n\n  let server = await topology.selectServer(selector, {\n    session,\n    operationName: operation.commandName,\n    timeoutContext,\n    signal: operation.options.signal\n  });\n\n  const hasReadAspect = operation.hasAspect(Aspect.READ_OPERATION);\n  const hasWriteAspect = operation.hasAspect(Aspect.WRITE_OPERATION);\n  const inTransaction = session?.inTransaction() ?? false;\n\n  const willRetryRead = topology.s.options.retryReads && !inTransaction && operation.canRetryRead;\n\n  const willRetryWrite =\n    topology.s.options.retryWrites &&\n    !inTransaction &&\n    supportsRetryableWrites(server) &&\n    operation.canRetryWrite;\n\n  const willRetry =\n    operation.hasAspect(Aspect.RETRYABLE) &&\n    session != null &&\n    ((hasReadAspect && willRetryRead) || (hasWriteAspect && willRetryWrite));\n\n  if (hasWriteAspect && willRetryWrite && session != null) {\n    operation.options.willRetryWrite = true;\n    session.incrementTransactionNumber();\n  }\n\n  const maxTries = willRetry ? (timeoutContext.csotEnabled() ? Infinity : 2) : 1;\n  let previousOperationError: MongoError | undefined;\n  let previousServer: ServerDescription | undefined;\n\n  for (let tries = 0; tries < maxTries; tries++) {\n    if (previousOperationError) {\n      if (hasWriteAspect && previousOperationError.code === MMAPv1_RETRY_WRITES_ERROR_CODE) {\n        throw new MongoServerError({\n          message: MMAPv1_RETRY_WRITES_ERROR_MESSAGE,\n          errmsg: MMAPv1_RETRY_WRITES_ERROR_MESSAGE,\n          originalError: previousOperationError\n        });\n      }\n\n      if (operation.hasAspect(Aspect.COMMAND_BATCHING) && !operation.canRetryWrite) {\n        throw previousOperationError;\n      }\n\n      if (hasWriteAspect && !isRetryableWriteError(previousOperationError))\n        throw previousOperationError;\n\n      if (hasReadAspect && !isRetryableReadError(previousOperationError))\n        throw previousOperationError;\n\n      if (\n        previousOperationError instanceof MongoNetworkError &&\n        operation.hasAspect(Aspect.CURSOR_CREATING) &&\n        session != null &&\n        session.isPinned &&\n        !session.inTransaction()\n      ) {\n        session.unpin({ force: true, forceClear: true });\n      }\n\n      server = await topology.selectServer(selector, {\n        session,\n        operationName: operation.commandName,\n        previousServer,\n        signal: operation.options.signal\n      });\n\n      if (hasWriteAspect && !supportsRetryableWrites(server)) {\n        throw new MongoUnexpectedServerResponseError(\n          'Selected server does not support retryable writes'\n        );\n      }\n    }\n\n    try {\n      // If tries > 0 and we are command batching we need to reset the batch.\n      if (tries > 0 && operation.hasAspect(Aspect.COMMAND_BATCHING)) {\n        operation.resetBatch();\n      }\n      return await operation.execute(server, session, timeoutContext);\n    } catch (operationError) {\n      if (!(operationError instanceof MongoError)) throw operationError;\n      if (\n        previousOperationError != null &&\n        operationError.hasErrorLabel(MongoErrorLabel.NoWritesPerformed)\n      ) {\n        throw previousOperationError;\n      }\n      previousServer = server.description;\n      previousOperationError = operationError;\n\n      // Reset timeouts\n      timeoutContext.clear();\n    }\n  }\n\n  throw (\n    previousOperationError ??\n    new MongoRuntimeError('Tried to propagate retryability error, but no error was found.')\n  );\n}\n"], "mappings": ";;;;;AAyDAA,OAAA,CAAAC,gBAAA,GAAAA,gBAAA;AAzDA,MAAAC,OAAA,GAAAC,OAAA;AAiBA,MAAAC,iBAAA,GAAAD,OAAA;AAEA,MAAAE,kBAAA,GAAAF,OAAA;AAOA,MAAAG,SAAA,GAAAH,OAAA;AACA,MAAAI,OAAA,GAAAJ,OAAA;AACA,MAAAK,WAAA,GAAAL,OAAA;AAEA,MAAMM,8BAA8B,GAAGP,OAAA,CAAAQ,mBAAmB,CAACC,gBAAgB;AAC3E,MAAMC,iCAAiC,GACrC,oHAAoH;AAKtH;;;;;;;;;;;;;;;;;;;;AAoBO,eAAeX,gBAAgBA,CAGpCY,MAAmB,EAAEC,SAAY,EAAEC,cAAsC;EACzE,IAAI,EAAED,SAAS,YAAYN,WAAA,CAAAQ,iBAAiB,CAAC,EAAE;IAC7C;IACA,MAAM,IAAId,OAAA,CAAAe,iBAAiB,CAAC,iDAAiD,CAAC;EAChF;EAEA,MAAMC,QAAQ,GACZL,MAAM,CAACK,QAAQ,IAAI,IAAI,GACnB,MAAM,IAAAX,OAAA,CAAAY,SAAS,EAACC,WAAW,CAACP,MAAM,CAAC,EAAEC,SAAS,CAACO,OAAO,CAAC,GACvDR,MAAM,CAACK,QAAQ;EAErB;EACA;EACA,IAAII,OAAO,GAAGR,SAAS,CAACQ,OAAO;EAC/B,IAAIC,KAAyB;EAE7B,IAAID,OAAO,IAAI,IAAI,EAAE;IACnBC,KAAK,GAAGC,MAAM,EAAE;IAChBF,OAAO,GAAGT,MAAM,CAACY,YAAY,CAAC;MAAEF,KAAK;MAAEG,QAAQ,EAAE;IAAK,CAAE,CAAC;EAC3D,CAAC,MAAM,IAAIJ,OAAO,CAACK,QAAQ,EAAE;IAC3B,MAAM,IAAIzB,OAAA,CAAA0B,wBAAwB,CAAC,0CAA0C,CAAC;EAChF,CAAC,MAAM,IAAIN,OAAO,CAACO,eAAe,IAAI,CAACX,QAAQ,CAACY,YAAY,CAACC,qBAAqB,EAAE;IAClF,MAAM,IAAI7B,OAAA,CAAA8B,uBAAuB,CAAC,6CAA6C,CAAC;EAClF,CAAC,MAAM,IAAIV,OAAO,CAACT,MAAM,KAAKA,MAAM,EAAE;IACpC,MAAM,IAAIX,OAAA,CAAA+B,yBAAyB,CAAC,iDAAiD,CAAC;EACxF;EAEA,MAAMC,cAAc,GAAGpB,SAAS,CAACoB,cAAc,IAAI9B,iBAAA,CAAA+B,cAAc,CAACC,OAAO;EACzE,MAAMC,aAAa,GAAG,CAAC,CAACf,OAAO,EAAEe,aAAa,EAAE;EAEhD,MAAMC,aAAa,GAAGxB,SAAS,CAACyB,SAAS,CAAC/B,WAAA,CAAAgC,MAAM,CAACC,cAAc,CAAC;EAEhE,IACEJ,aAAa,IACb,CAACH,cAAc,CAACQ,MAAM,CAACtC,iBAAA,CAAA+B,cAAc,CAACC,OAAO,CAAC,KAC7CE,aAAa,IAAIxB,SAAS,CAAC6B,WAAW,KAAK,YAAY,CAAC,EACzD;IACA,MAAM,IAAIzC,OAAA,CAAA0C,qBAAqB,CAC7B,0DAA0DV,cAAc,CAACW,IAAI,EAAE,CAChF;EACH;EAEA,IAAIvB,OAAO,EAAEwB,QAAQ,IAAIxB,OAAO,CAACyB,WAAW,CAACC,WAAW,IAAI,CAAClC,SAAS,CAACmC,kBAAkB,EAAE;IACzF3B,OAAO,CAAC4B,KAAK,EAAE;EACjB;EAEAnC,cAAc,KAAKT,SAAA,CAAA6C,cAAc,CAACC,MAAM,CAAC;IACvC9B,OAAO;IACP+B,wBAAwB,EAAExC,MAAM,CAACyC,CAAC,CAACjC,OAAO,CAACgC,wBAAwB;IACnEE,kBAAkB,EAAE1C,MAAM,CAACyC,CAAC,CAACjC,OAAO,CAACkC,kBAAkB;IACvDC,SAAS,EAAE1C,SAAS,CAACO,OAAO,CAACmC;GAC9B,CAAC;EAEF,IAAI;IACF,OAAO,MAAMC,YAAY,CAAC3C,SAAS,EAAE;MACnCI,QAAQ;MACRH,cAAc;MACdO,OAAO;MACPY;KACD,CAAC;EACJ,CAAC,SAAS;IACR,IAAIZ,OAAO,EAAEC,KAAK,IAAI,IAAI,IAAID,OAAO,CAACC,KAAK,KAAKA,KAAK,EAAE;MACrD,MAAMD,OAAO,CAACoC,UAAU,EAAE;IAC5B;EACF;AACF;AAEA;;;;AAIA,eAAetC,WAAWA,CAACP,MAAmB;EAC5C,IAAIA,MAAM,CAACK,QAAQ,IAAI,IAAI,EAAE;IAC3B,IAAIL,MAAM,CAACyC,CAAC,CAACK,aAAa,EAAE;MAC1B,MAAM,IAAIzD,OAAA,CAAA0D,sBAAsB,CAAC,oDAAoD,CAAC;IACxF;IACA/C,MAAM,CAACyC,CAAC,CAACjC,OAAO,CAACwC,mBAAmB,GAAG,IAAI;IAC3C,IAAI;MACF,MAAMhD,MAAM,CAACiD,OAAO,EAAE;MACtB,IAAIjD,MAAM,CAACK,QAAQ,IAAI,IAAI,EAAE;QAC3B,MAAM,IAAIhB,OAAA,CAAAe,iBAAiB,CACzB,iEAAiE,CAClE;MACH;MACA,OAAOJ,MAAM,CAACK,QAAQ;IACxB,CAAC,SAAS;MACR,OAAOL,MAAM,CAACyC,CAAC,CAACjC,OAAO,CAACwC,mBAAmB;IAC7C;EACF;EACA,OAAOhD,MAAM,CAACK,QAAQ;AACxB;AAUA;;;;;;;;;;;;;;;;;;AAkBA,eAAeuC,YAAYA,CAIzB3C,SAAY,EACZ;EAAEI,QAAQ;EAAEH,cAAc;EAAEO,OAAO;EAAEY;AAAc,CAAgB;EAEnE,IAAI6B,QAAyC;EAE7C,IAAIjD,SAAS,CAACyB,SAAS,CAAC/B,WAAA,CAAAgC,MAAM,CAACwB,uBAAuB,CAAC,EAAE;IACvD;IACA;IACA;IACAD,QAAQ,GAAG,IAAA1D,kBAAA,CAAA4D,kBAAkB,EAACnD,SAAS,CAACoD,MAAM,EAAEC,WAAW,CAAC;EAC9D,CAAC,MAAM,IAAIrD,SAAS,CAACsD,iBAAiB,EAAE;IACtC;IACA;IACAL,QAAQ,GAAG,IAAA1D,kBAAA,CAAAgE,+BAA+B,EAACnD,QAAQ,CAACoD,iBAAiB,EAAEpC,cAAc,CAAC;EACxF,CAAC,MAAM;IACL6B,QAAQ,GAAG7B,cAAc;EAC3B;EAEA,IAAIgC,MAAM,GAAG,MAAMhD,QAAQ,CAACqD,YAAY,CAACR,QAAQ,EAAE;IACjDzC,OAAO;IACPkD,aAAa,EAAE1D,SAAS,CAAC6B,WAAW;IACpC5B,cAAc;IACd0D,MAAM,EAAE3D,SAAS,CAACO,OAAO,CAACoD;GAC3B,CAAC;EAEF,MAAMnC,aAAa,GAAGxB,SAAS,CAACyB,SAAS,CAAC/B,WAAA,CAAAgC,MAAM,CAACC,cAAc,CAAC;EAChE,MAAMiC,cAAc,GAAG5D,SAAS,CAACyB,SAAS,CAAC/B,WAAA,CAAAgC,MAAM,CAACmC,eAAe,CAAC;EAClE,MAAMtC,aAAa,GAAGf,OAAO,EAAEe,aAAa,EAAE,IAAI,KAAK;EAEvD,MAAMuC,aAAa,GAAG1D,QAAQ,CAACoC,CAAC,CAACjC,OAAO,CAACwD,UAAU,IAAI,CAACxC,aAAa,IAAIvB,SAAS,CAACgE,YAAY;EAE/F,MAAMC,cAAc,GAClB7D,QAAQ,CAACoC,CAAC,CAACjC,OAAO,CAAC2D,WAAW,IAC9B,CAAC3C,aAAa,IACd,IAAA9B,OAAA,CAAA0E,uBAAuB,EAACf,MAAM,CAAC,IAC/BpD,SAAS,CAACoE,aAAa;EAEzB,MAAMC,SAAS,GACbrE,SAAS,CAACyB,SAAS,CAAC/B,WAAA,CAAAgC,MAAM,CAAC4C,SAAS,CAAC,IACrC9D,OAAO,IAAI,IAAI,KACbgB,aAAa,IAAIsC,aAAa,IAAMF,cAAc,IAAIK,cAAe,CAAC;EAE1E,IAAIL,cAAc,IAAIK,cAAc,IAAIzD,OAAO,IAAI,IAAI,EAAE;IACvDR,SAAS,CAACO,OAAO,CAAC0D,cAAc,GAAG,IAAI;IACvCzD,OAAO,CAAC+D,0BAA0B,EAAE;EACtC;EAEA,MAAMC,QAAQ,GAAGH,SAAS,GAAIpE,cAAc,CAACwE,WAAW,EAAE,GAAGC,QAAQ,GAAG,CAAC,GAAI,CAAC;EAC9E,IAAIC,sBAA8C;EAClD,IAAIC,cAA6C;EAEjD,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGL,QAAQ,EAAEK,KAAK,EAAE,EAAE;IAC7C,IAAIF,sBAAsB,EAAE;MAC1B,IAAIf,cAAc,IAAIe,sBAAsB,CAACG,IAAI,KAAKnF,8BAA8B,EAAE;QACpF,MAAM,IAAIP,OAAA,CAAA2F,gBAAgB,CAAC;UACzBC,OAAO,EAAElF,iCAAiC;UAC1CmF,MAAM,EAAEnF,iCAAiC;UACzCoF,aAAa,EAAEP;SAChB,CAAC;MACJ;MAEA,IAAI3E,SAAS,CAACyB,SAAS,CAAC/B,WAAA,CAAAgC,MAAM,CAACyD,gBAAgB,CAAC,IAAI,CAACnF,SAAS,CAACoE,aAAa,EAAE;QAC5E,MAAMO,sBAAsB;MAC9B;MAEA,IAAIf,cAAc,IAAI,CAAC,IAAAxE,OAAA,CAAAgG,qBAAqB,EAACT,sBAAsB,CAAC,EAClE,MAAMA,sBAAsB;MAE9B,IAAInD,aAAa,IAAI,CAAC,IAAApC,OAAA,CAAAiG,oBAAoB,EAACV,sBAAsB,CAAC,EAChE,MAAMA,sBAAsB;MAE9B,IACEA,sBAAsB,YAAYvF,OAAA,CAAAkG,iBAAiB,IACnDtF,SAAS,CAACyB,SAAS,CAAC/B,WAAA,CAAAgC,MAAM,CAAC6D,eAAe,CAAC,IAC3C/E,OAAO,IAAI,IAAI,IACfA,OAAO,CAACwB,QAAQ,IAChB,CAACxB,OAAO,CAACe,aAAa,EAAE,EACxB;QACAf,OAAO,CAAC4B,KAAK,CAAC;UAAEoD,KAAK,EAAE,IAAI;UAAEC,UAAU,EAAE;QAAI,CAAE,CAAC;MAClD;MAEArC,MAAM,GAAG,MAAMhD,QAAQ,CAACqD,YAAY,CAACR,QAAQ,EAAE;QAC7CzC,OAAO;QACPkD,aAAa,EAAE1D,SAAS,CAAC6B,WAAW;QACpC+C,cAAc;QACdjB,MAAM,EAAE3D,SAAS,CAACO,OAAO,CAACoD;OAC3B,CAAC;MAEF,IAAIC,cAAc,IAAI,CAAC,IAAAnE,OAAA,CAAA0E,uBAAuB,EAACf,MAAM,CAAC,EAAE;QACtD,MAAM,IAAIhE,OAAA,CAAAsG,kCAAkC,CAC1C,mDAAmD,CACpD;MACH;IACF;IAEA,IAAI;MACF;MACA,IAAIb,KAAK,GAAG,CAAC,IAAI7E,SAAS,CAACyB,SAAS,CAAC/B,WAAA,CAAAgC,MAAM,CAACyD,gBAAgB,CAAC,EAAE;QAC7DnF,SAAS,CAAC2F,UAAU,EAAE;MACxB;MACA,OAAO,MAAM3F,SAAS,CAAC4F,OAAO,CAACxC,MAAM,EAAE5C,OAAO,EAAEP,cAAc,CAAC;IACjE,CAAC,CAAC,OAAO4F,cAAc,EAAE;MACvB,IAAI,EAAEA,cAAc,YAAYzG,OAAA,CAAA0G,UAAU,CAAC,EAAE,MAAMD,cAAc;MACjE,IACElB,sBAAsB,IAAI,IAAI,IAC9BkB,cAAc,CAACE,aAAa,CAAC3G,OAAA,CAAA4G,eAAe,CAACC,iBAAiB,CAAC,EAC/D;QACA,MAAMtB,sBAAsB;MAC9B;MACAC,cAAc,GAAGxB,MAAM,CAACC,WAAW;MACnCsB,sBAAsB,GAAGkB,cAAc;MAEvC;MACA5F,cAAc,CAACiG,KAAK,EAAE;IACxB;EACF;EAEA,MACEvB,sBAAsB,IACtB,IAAIvF,OAAA,CAAAe,iBAAiB,CAAC,gEAAgE,CAAC;AAE3F", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}