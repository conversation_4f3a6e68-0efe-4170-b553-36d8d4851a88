{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport * as React from 'react';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\n/**\n * Implement the Pipeline Pattern\n *\n * More information and detailed example in (TODO add link to technical doc when ready)\n *\n * Some plugins contains custom logic to enrich data provided by other plugins or components.\n * For instance, the row grouping plugin needs to add / remove the grouping columns when the grid columns are updated.\n *\n * =====================================================================================================================\n *\n * The plugin containing the custom logic must use:\n *\n * - `useGridRegisterPipeProcessor` to register their processor.\n *\n * - `apiRef.current.requestPipeProcessorsApplication` to imperatively re-apply a group.\n *   This method should be used in last resort.\n *   Most of the time, the application should be triggered by an update on the deps of the processor.\n *\n * =====================================================================================================================\n *\n * The plugin or component that needs to enrich its data must use:\n *\n * - `apiRef.current.unstable_applyPipeProcessors` to run in chain all the processors of a given group.\n *\n * - `useGridRegisterPipeApplier` to re-apply the whole pipe when requested.\n *   The applier will be called when:\n *   * a processor is registered.\n *   * `apiRef.current.requestPipeProcessorsApplication` is called for the given group.\n */\nexport const useGridPipeProcessing = apiRef => {\n  const processorsCache = React.useRef({});\n  const isRunning = React.useRef(false);\n  const runAppliers = React.useCallback(groupCache => {\n    if (isRunning.current || !groupCache) {\n      return;\n    }\n    isRunning.current = true;\n    Object.values(groupCache.appliers).forEach(callback => {\n      callback();\n    });\n    isRunning.current = false;\n  }, []);\n  const registerPipeProcessor = React.useCallback((group, id, processor) => {\n    if (!processorsCache.current[group]) {\n      processorsCache.current[group] = {\n        processors: new Map(),\n        appliers: {}\n      };\n    }\n    const groupCache = processorsCache.current[group];\n    const oldProcessor = groupCache.processors.get(id);\n    if (oldProcessor !== processor) {\n      groupCache.processors.set(id, processor);\n      runAppliers(groupCache);\n    }\n    return () => {\n      processorsCache.current[group].processors.set(id, null);\n    };\n  }, [runAppliers]);\n  const registerPipeApplier = React.useCallback((group, id, applier) => {\n    if (!processorsCache.current[group]) {\n      processorsCache.current[group] = {\n        processors: new Map(),\n        appliers: {}\n      };\n    }\n    processorsCache.current[group].appliers[id] = applier;\n    return () => {\n      const _appliers = processorsCache.current[group].appliers,\n        otherAppliers = _objectWithoutPropertiesLoose(_appliers, [id].map(_toPropertyKey));\n      processorsCache.current[group].appliers = otherAppliers;\n    };\n  }, []);\n  const requestPipeProcessorsApplication = React.useCallback(group => {\n    const groupCache = processorsCache.current[group];\n    runAppliers(groupCache);\n  }, [runAppliers]);\n  const applyPipeProcessors = React.useCallback((...args) => {\n    const [group, value, context] = args;\n    if (!processorsCache.current[group]) {\n      return value;\n    }\n    const preProcessors = Array.from(processorsCache.current[group].processors.values());\n    return preProcessors.reduce((acc, preProcessor) => {\n      if (!preProcessor) {\n        return acc;\n      }\n      return preProcessor(acc, context);\n    }, value);\n  }, []);\n  const preProcessingPrivateApi = {\n    registerPipeProcessor,\n    registerPipeApplier,\n    requestPipeProcessorsApplication\n  };\n  const preProcessingPublicApi = {\n    unstable_applyPipeProcessors: applyPipeProcessors\n  };\n  useGridApiMethod(apiRef, preProcessingPrivateApi, 'private');\n  useGridApiMethod(apiRef, preProcessingPublicApi, 'public');\n};", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "React", "useGridApiMethod", "useGridPipeProcessing", "apiRef", "processorsCache", "useRef", "isRunning", "runAppliers", "useCallback", "groupCache", "current", "Object", "values", "appliers", "for<PERSON>ach", "callback", "registerPipeProcessor", "group", "id", "processor", "processors", "Map", "oldProcessor", "get", "set", "registerPipeApplier", "applier", "_appliers", "otherAppliers", "map", "requestPipeProcessorsApplication", "applyPipeProcessors", "args", "value", "context", "preProcessors", "Array", "from", "reduce", "acc", "preProcessor", "preProcessingPrivateApi", "preProcessingPublicApi", "unstable_applyPipeProcessors"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/x-data-grid/hooks/core/pipeProcessing/useGridPipeProcessing.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport * as React from 'react';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\n/**\n * Implement the Pipeline Pattern\n *\n * More information and detailed example in (TODO add link to technical doc when ready)\n *\n * Some plugins contains custom logic to enrich data provided by other plugins or components.\n * For instance, the row grouping plugin needs to add / remove the grouping columns when the grid columns are updated.\n *\n * =====================================================================================================================\n *\n * The plugin containing the custom logic must use:\n *\n * - `useGridRegisterPipeProcessor` to register their processor.\n *\n * - `apiRef.current.requestPipeProcessorsApplication` to imperatively re-apply a group.\n *   This method should be used in last resort.\n *   Most of the time, the application should be triggered by an update on the deps of the processor.\n *\n * =====================================================================================================================\n *\n * The plugin or component that needs to enrich its data must use:\n *\n * - `apiRef.current.unstable_applyPipeProcessors` to run in chain all the processors of a given group.\n *\n * - `useGridRegisterPipeApplier` to re-apply the whole pipe when requested.\n *   The applier will be called when:\n *   * a processor is registered.\n *   * `apiRef.current.requestPipeProcessorsApplication` is called for the given group.\n */\nexport const useGridPipeProcessing = apiRef => {\n  const processorsCache = React.useRef({});\n  const isRunning = React.useRef(false);\n  const runAppliers = React.useCallback(groupCache => {\n    if (isRunning.current || !groupCache) {\n      return;\n    }\n    isRunning.current = true;\n    Object.values(groupCache.appliers).forEach(callback => {\n      callback();\n    });\n    isRunning.current = false;\n  }, []);\n  const registerPipeProcessor = React.useCallback((group, id, processor) => {\n    if (!processorsCache.current[group]) {\n      processorsCache.current[group] = {\n        processors: new Map(),\n        appliers: {}\n      };\n    }\n    const groupCache = processorsCache.current[group];\n    const oldProcessor = groupCache.processors.get(id);\n    if (oldProcessor !== processor) {\n      groupCache.processors.set(id, processor);\n      runAppliers(groupCache);\n    }\n    return () => {\n      processorsCache.current[group].processors.set(id, null);\n    };\n  }, [runAppliers]);\n  const registerPipeApplier = React.useCallback((group, id, applier) => {\n    if (!processorsCache.current[group]) {\n      processorsCache.current[group] = {\n        processors: new Map(),\n        appliers: {}\n      };\n    }\n    processorsCache.current[group].appliers[id] = applier;\n    return () => {\n      const _appliers = processorsCache.current[group].appliers,\n        otherAppliers = _objectWithoutPropertiesLoose(_appliers, [id].map(_toPropertyKey));\n      processorsCache.current[group].appliers = otherAppliers;\n    };\n  }, []);\n  const requestPipeProcessorsApplication = React.useCallback(group => {\n    const groupCache = processorsCache.current[group];\n    runAppliers(groupCache);\n  }, [runAppliers]);\n  const applyPipeProcessors = React.useCallback((...args) => {\n    const [group, value, context] = args;\n    if (!processorsCache.current[group]) {\n      return value;\n    }\n    const preProcessors = Array.from(processorsCache.current[group].processors.values());\n    return preProcessors.reduce((acc, preProcessor) => {\n      if (!preProcessor) {\n        return acc;\n      }\n      return preProcessor(acc, context);\n    }, value);\n  }, []);\n  const preProcessingPrivateApi = {\n    registerPipeProcessor,\n    registerPipeApplier,\n    requestPipeProcessorsApplication\n  };\n  const preProcessingPublicApi = {\n    unstable_applyPipeProcessors: applyPipeProcessors\n  };\n  useGridApiMethod(apiRef, preProcessingPrivateApi, 'private');\n  useGridApiMethod(apiRef, preProcessingPublicApi, 'public');\n};"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,qBAAqB,GAAGC,MAAM,IAAI;EAC7C,MAAMC,eAAe,GAAGJ,KAAK,CAACK,MAAM,CAAC,CAAC,CAAC,CAAC;EACxC,MAAMC,SAAS,GAAGN,KAAK,CAACK,MAAM,CAAC,KAAK,CAAC;EACrC,MAAME,WAAW,GAAGP,KAAK,CAACQ,WAAW,CAACC,UAAU,IAAI;IAClD,IAAIH,SAAS,CAACI,OAAO,IAAI,CAACD,UAAU,EAAE;MACpC;IACF;IACAH,SAAS,CAACI,OAAO,GAAG,IAAI;IACxBC,MAAM,CAACC,MAAM,CAACH,UAAU,CAACI,QAAQ,CAAC,CAACC,OAAO,CAACC,QAAQ,IAAI;MACrDA,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC;IACFT,SAAS,CAACI,OAAO,GAAG,KAAK;EAC3B,CAAC,EAAE,EAAE,CAAC;EACN,MAAMM,qBAAqB,GAAGhB,KAAK,CAACQ,WAAW,CAAC,CAACS,KAAK,EAAEC,EAAE,EAAEC,SAAS,KAAK;IACxE,IAAI,CAACf,eAAe,CAACM,OAAO,CAACO,KAAK,CAAC,EAAE;MACnCb,eAAe,CAACM,OAAO,CAACO,KAAK,CAAC,GAAG;QAC/BG,UAAU,EAAE,IAAIC,GAAG,CAAC,CAAC;QACrBR,QAAQ,EAAE,CAAC;MACb,CAAC;IACH;IACA,MAAMJ,UAAU,GAAGL,eAAe,CAACM,OAAO,CAACO,KAAK,CAAC;IACjD,MAAMK,YAAY,GAAGb,UAAU,CAACW,UAAU,CAACG,GAAG,CAACL,EAAE,CAAC;IAClD,IAAII,YAAY,KAAKH,SAAS,EAAE;MAC9BV,UAAU,CAACW,UAAU,CAACI,GAAG,CAACN,EAAE,EAAEC,SAAS,CAAC;MACxCZ,WAAW,CAACE,UAAU,CAAC;IACzB;IACA,OAAO,MAAM;MACXL,eAAe,CAACM,OAAO,CAACO,KAAK,CAAC,CAACG,UAAU,CAACI,GAAG,CAACN,EAAE,EAAE,IAAI,CAAC;IACzD,CAAC;EACH,CAAC,EAAE,CAACX,WAAW,CAAC,CAAC;EACjB,MAAMkB,mBAAmB,GAAGzB,KAAK,CAACQ,WAAW,CAAC,CAACS,KAAK,EAAEC,EAAE,EAAEQ,OAAO,KAAK;IACpE,IAAI,CAACtB,eAAe,CAACM,OAAO,CAACO,KAAK,CAAC,EAAE;MACnCb,eAAe,CAACM,OAAO,CAACO,KAAK,CAAC,GAAG;QAC/BG,UAAU,EAAE,IAAIC,GAAG,CAAC,CAAC;QACrBR,QAAQ,EAAE,CAAC;MACb,CAAC;IACH;IACAT,eAAe,CAACM,OAAO,CAACO,KAAK,CAAC,CAACJ,QAAQ,CAACK,EAAE,CAAC,GAAGQ,OAAO;IACrD,OAAO,MAAM;MACX,MAAMC,SAAS,GAAGvB,eAAe,CAACM,OAAO,CAACO,KAAK,CAAC,CAACJ,QAAQ;QACvDe,aAAa,GAAG9B,6BAA6B,CAAC6B,SAAS,EAAE,CAACT,EAAE,CAAC,CAACW,GAAG,CAAC9B,cAAc,CAAC,CAAC;MACpFK,eAAe,CAACM,OAAO,CAACO,KAAK,CAAC,CAACJ,QAAQ,GAAGe,aAAa;IACzD,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,MAAME,gCAAgC,GAAG9B,KAAK,CAACQ,WAAW,CAACS,KAAK,IAAI;IAClE,MAAMR,UAAU,GAAGL,eAAe,CAACM,OAAO,CAACO,KAAK,CAAC;IACjDV,WAAW,CAACE,UAAU,CAAC;EACzB,CAAC,EAAE,CAACF,WAAW,CAAC,CAAC;EACjB,MAAMwB,mBAAmB,GAAG/B,KAAK,CAACQ,WAAW,CAAC,CAAC,GAAGwB,IAAI,KAAK;IACzD,MAAM,CAACf,KAAK,EAAEgB,KAAK,EAAEC,OAAO,CAAC,GAAGF,IAAI;IACpC,IAAI,CAAC5B,eAAe,CAACM,OAAO,CAACO,KAAK,CAAC,EAAE;MACnC,OAAOgB,KAAK;IACd;IACA,MAAME,aAAa,GAAGC,KAAK,CAACC,IAAI,CAACjC,eAAe,CAACM,OAAO,CAACO,KAAK,CAAC,CAACG,UAAU,CAACR,MAAM,CAAC,CAAC,CAAC;IACpF,OAAOuB,aAAa,CAACG,MAAM,CAAC,CAACC,GAAG,EAAEC,YAAY,KAAK;MACjD,IAAI,CAACA,YAAY,EAAE;QACjB,OAAOD,GAAG;MACZ;MACA,OAAOC,YAAY,CAACD,GAAG,EAAEL,OAAO,CAAC;IACnC,CAAC,EAAED,KAAK,CAAC;EACX,CAAC,EAAE,EAAE,CAAC;EACN,MAAMQ,uBAAuB,GAAG;IAC9BzB,qBAAqB;IACrBS,mBAAmB;IACnBK;EACF,CAAC;EACD,MAAMY,sBAAsB,GAAG;IAC7BC,4BAA4B,EAAEZ;EAChC,CAAC;EACD9B,gBAAgB,CAACE,MAAM,EAAEsC,uBAAuB,EAAE,SAAS,CAAC;EAC5DxC,gBAAgB,CAACE,MAAM,EAAEuC,sBAAsB,EAAE,QAAQ,CAAC;AAC5D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}