{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.MachineWorkflow = void 0;\nconst promises_1 = require(\"timers/promises\");\nconst utils_1 = require(\"../../../utils\");\nconst command_builders_1 = require(\"./command_builders\");\n/** The time to throttle callback calls. */\nconst THROTTLE_MS = 100;\n/**\n * Common behaviour for OIDC machine workflows.\n * @internal\n */\nclass MachineWorkflow {\n  /**\n   * Instantiate the machine workflow.\n   */\n  constructor(cache) {\n    this.cache = cache;\n    this.callback = this.withLock(this.getToken.bind(this));\n    this.lastExecutionTime = Date.now() - THROTTLE_MS;\n  }\n  /**\n   * Execute the workflow. Gets the token from the subclass implementation.\n   */\n  async execute(connection, credentials) {\n    const token = await this.getTokenFromCacheOrEnv(connection, credentials);\n    const command = (0, command_builders_1.finishCommandDocument)(token);\n    await connection.command((0, utils_1.ns)(credentials.source), command, undefined);\n  }\n  /**\n   * Reauthenticate on a machine workflow just grabs the token again since the server\n   * has said the current access token is invalid or expired.\n   */\n  async reauthenticate(connection, credentials) {\n    if (this.cache.hasAccessToken) {\n      // Reauthentication implies the token has expired.\n      if (connection.accessToken === this.cache.getAccessToken()) {\n        // If connection's access token is the same as the cache's, remove\n        // the token from the cache and connection.\n        this.cache.removeAccessToken();\n        delete connection.accessToken;\n      } else {\n        // If the connection's access token is different from the cache's, set\n        // the cache's token on the connection and do not remove from the\n        // cache.\n        connection.accessToken = this.cache.getAccessToken();\n      }\n    }\n    await this.execute(connection, credentials);\n  }\n  /**\n   * Get the document to add for speculative authentication.\n   */\n  async speculativeAuth(connection, credentials) {\n    // The spec states only cached access tokens can use speculative auth.\n    if (!this.cache.hasAccessToken) {\n      return {};\n    }\n    const token = await this.getTokenFromCacheOrEnv(connection, credentials);\n    const document = (0, command_builders_1.finishCommandDocument)(token);\n    document.db = credentials.source;\n    return {\n      speculativeAuthenticate: document\n    };\n  }\n  /**\n   * Get the token from the cache or environment.\n   */\n  async getTokenFromCacheOrEnv(connection, credentials) {\n    if (this.cache.hasAccessToken) {\n      const token = this.cache.getAccessToken();\n      // New connections won't have an access token so ensure we set here.\n      if (!connection.accessToken) {\n        connection.accessToken = token;\n      }\n      return token;\n    } else {\n      const token = await this.callback(credentials);\n      this.cache.put({\n        accessToken: token.access_token,\n        expiresInSeconds: token.expires_in\n      });\n      // Put the access token on the connection as well.\n      connection.accessToken = token.access_token;\n      return token.access_token;\n    }\n  }\n  /**\n   * Ensure the callback is only executed one at a time, and throttled to\n   * only once per 100ms.\n   */\n  withLock(callback) {\n    let lock = Promise.resolve();\n    return async credentials => {\n      // We do this to ensure that we would never return the result of the\n      // previous lock, only the current callback's value would get returned.\n      await lock;\n      lock = lock.catch(() => null).then(async () => {\n        const difference = Date.now() - this.lastExecutionTime;\n        if (difference <= THROTTLE_MS) {\n          await (0, promises_1.setTimeout)(THROTTLE_MS - difference);\n        }\n        this.lastExecutionTime = Date.now();\n        return await callback(credentials);\n      });\n      return await lock;\n    };\n  }\n}\nexports.MachineWorkflow = MachineWorkflow;", "map": {"version": 3, "names": ["promises_1", "require", "utils_1", "command_builders_1", "THROTTLE_MS", "MachineWorkflow", "constructor", "cache", "callback", "withLock", "getToken", "bind", "lastExecutionTime", "Date", "now", "execute", "connection", "credentials", "token", "getTokenFromCacheOrEnv", "command", "finishCommandDocument", "ns", "source", "undefined", "reauthenticate", "hasAccessToken", "accessToken", "getAccessToken", "removeAccessToken", "speculativeAuth", "document", "db", "speculativeAuthenticate", "put", "access_token", "expiresInSeconds", "expires_in", "lock", "Promise", "resolve", "catch", "then", "difference", "setTimeout", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\auth\\mongodb_oidc\\machine_workflow.ts"], "sourcesContent": ["import { setTimeout } from 'timers/promises';\n\nimport { type Document } from '../../../bson';\nimport { ns } from '../../../utils';\nimport type { Connection } from '../../connection';\nimport type { MongoCredentials } from '../mongo_credentials';\nimport type { Workflow } from '../mongodb_oidc';\nimport { finishCommandDocument } from './command_builders';\nimport { type TokenCache } from './token_cache';\n\n/** The time to throttle callback calls. */\nconst THROTTLE_MS = 100;\n\n/**\n * The access token format.\n * @internal\n */\nexport interface AccessToken {\n  access_token: string;\n  expires_in?: number;\n}\n\n/** @internal */\nexport type OIDCTokenFunction = (credentials: MongoCredentials) => Promise<AccessToken>;\n\n/**\n * Common behaviour for OIDC machine workflows.\n * @internal\n */\nexport abstract class MachineWorkflow implements Workflow {\n  cache: TokenCache;\n  callback: OIDCTokenFunction;\n  lastExecutionTime: number;\n\n  /**\n   * Instantiate the machine workflow.\n   */\n  constructor(cache: TokenCache) {\n    this.cache = cache;\n    this.callback = this.withLock(this.getToken.bind(this));\n    this.lastExecutionTime = Date.now() - THROTTLE_MS;\n  }\n\n  /**\n   * Execute the workflow. Gets the token from the subclass implementation.\n   */\n  async execute(connection: Connection, credentials: MongoCredentials): Promise<void> {\n    const token = await this.getTokenFromCacheOrEnv(connection, credentials);\n    const command = finishCommandDocument(token);\n    await connection.command(ns(credentials.source), command, undefined);\n  }\n\n  /**\n   * Reauthenticate on a machine workflow just grabs the token again since the server\n   * has said the current access token is invalid or expired.\n   */\n  async reauthenticate(connection: Connection, credentials: MongoCredentials): Promise<void> {\n    if (this.cache.hasAccessToken) {\n      // Reauthentication implies the token has expired.\n      if (connection.accessToken === this.cache.getAccessToken()) {\n        // If connection's access token is the same as the cache's, remove\n        // the token from the cache and connection.\n        this.cache.removeAccessToken();\n        delete connection.accessToken;\n      } else {\n        // If the connection's access token is different from the cache's, set\n        // the cache's token on the connection and do not remove from the\n        // cache.\n        connection.accessToken = this.cache.getAccessToken();\n      }\n    }\n    await this.execute(connection, credentials);\n  }\n\n  /**\n   * Get the document to add for speculative authentication.\n   */\n  async speculativeAuth(connection: Connection, credentials: MongoCredentials): Promise<Document> {\n    // The spec states only cached access tokens can use speculative auth.\n    if (!this.cache.hasAccessToken) {\n      return {};\n    }\n    const token = await this.getTokenFromCacheOrEnv(connection, credentials);\n    const document = finishCommandDocument(token);\n    document.db = credentials.source;\n    return { speculativeAuthenticate: document };\n  }\n\n  /**\n   * Get the token from the cache or environment.\n   */\n  private async getTokenFromCacheOrEnv(\n    connection: Connection,\n    credentials: MongoCredentials\n  ): Promise<string> {\n    if (this.cache.hasAccessToken) {\n      const token = this.cache.getAccessToken();\n      // New connections won't have an access token so ensure we set here.\n      if (!connection.accessToken) {\n        connection.accessToken = token;\n      }\n      return token;\n    } else {\n      const token = await this.callback(credentials);\n      this.cache.put({ accessToken: token.access_token, expiresInSeconds: token.expires_in });\n      // Put the access token on the connection as well.\n      connection.accessToken = token.access_token;\n      return token.access_token;\n    }\n  }\n\n  /**\n   * Ensure the callback is only executed one at a time, and throttled to\n   * only once per 100ms.\n   */\n  private withLock(callback: OIDCTokenFunction): OIDCTokenFunction {\n    let lock: Promise<any> = Promise.resolve();\n    return async (credentials: MongoCredentials): Promise<AccessToken> => {\n      // We do this to ensure that we would never return the result of the\n      // previous lock, only the current callback's value would get returned.\n      await lock;\n      lock = lock\n\n        .catch(() => null)\n\n        .then(async () => {\n          const difference = Date.now() - this.lastExecutionTime;\n          if (difference <= THROTTLE_MS) {\n            await setTimeout(THROTTLE_MS - difference);\n          }\n          this.lastExecutionTime = Date.now();\n          return await callback(credentials);\n        });\n      return await lock;\n    };\n  }\n\n  /**\n   * Get the token from the environment or endpoint.\n   */\n  abstract getToken(credentials: MongoCredentials): Promise<AccessToken>;\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,UAAA,GAAAC,OAAA;AAGA,MAAAC,OAAA,GAAAD,OAAA;AAIA,MAAAE,kBAAA,GAAAF,OAAA;AAGA;AACA,MAAMG,WAAW,GAAG,GAAG;AAcvB;;;;AAIA,MAAsBC,eAAe;EAKnC;;;EAGAC,YAAYC,KAAiB;IAC3B,IAAI,CAACA,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,QAAQ,CAAC,IAAI,CAACC,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvD,IAAI,CAACC,iBAAiB,GAAGC,IAAI,CAACC,GAAG,EAAE,GAAGV,WAAW;EACnD;EAEA;;;EAGA,MAAMW,OAAOA,CAACC,UAAsB,EAAEC,WAA6B;IACjE,MAAMC,KAAK,GAAG,MAAM,IAAI,CAACC,sBAAsB,CAACH,UAAU,EAAEC,WAAW,CAAC;IACxE,MAAMG,OAAO,GAAG,IAAAjB,kBAAA,CAAAkB,qBAAqB,EAACH,KAAK,CAAC;IAC5C,MAAMF,UAAU,CAACI,OAAO,CAAC,IAAAlB,OAAA,CAAAoB,EAAE,EAACL,WAAW,CAACM,MAAM,CAAC,EAAEH,OAAO,EAAEI,SAAS,CAAC;EACtE;EAEA;;;;EAIA,MAAMC,cAAcA,CAACT,UAAsB,EAAEC,WAA6B;IACxE,IAAI,IAAI,CAACV,KAAK,CAACmB,cAAc,EAAE;MAC7B;MACA,IAAIV,UAAU,CAACW,WAAW,KAAK,IAAI,CAACpB,KAAK,CAACqB,cAAc,EAAE,EAAE;QAC1D;QACA;QACA,IAAI,CAACrB,KAAK,CAACsB,iBAAiB,EAAE;QAC9B,OAAOb,UAAU,CAACW,WAAW;MAC/B,CAAC,MAAM;QACL;QACA;QACA;QACAX,UAAU,CAACW,WAAW,GAAG,IAAI,CAACpB,KAAK,CAACqB,cAAc,EAAE;MACtD;IACF;IACA,MAAM,IAAI,CAACb,OAAO,CAACC,UAAU,EAAEC,WAAW,CAAC;EAC7C;EAEA;;;EAGA,MAAMa,eAAeA,CAACd,UAAsB,EAAEC,WAA6B;IACzE;IACA,IAAI,CAAC,IAAI,CAACV,KAAK,CAACmB,cAAc,EAAE;MAC9B,OAAO,EAAE;IACX;IACA,MAAMR,KAAK,GAAG,MAAM,IAAI,CAACC,sBAAsB,CAACH,UAAU,EAAEC,WAAW,CAAC;IACxE,MAAMc,QAAQ,GAAG,IAAA5B,kBAAA,CAAAkB,qBAAqB,EAACH,KAAK,CAAC;IAC7Ca,QAAQ,CAACC,EAAE,GAAGf,WAAW,CAACM,MAAM;IAChC,OAAO;MAAEU,uBAAuB,EAAEF;IAAQ,CAAE;EAC9C;EAEA;;;EAGQ,MAAMZ,sBAAsBA,CAClCH,UAAsB,EACtBC,WAA6B;IAE7B,IAAI,IAAI,CAACV,KAAK,CAACmB,cAAc,EAAE;MAC7B,MAAMR,KAAK,GAAG,IAAI,CAACX,KAAK,CAACqB,cAAc,EAAE;MACzC;MACA,IAAI,CAACZ,UAAU,CAACW,WAAW,EAAE;QAC3BX,UAAU,CAACW,WAAW,GAAGT,KAAK;MAChC;MACA,OAAOA,KAAK;IACd,CAAC,MAAM;MACL,MAAMA,KAAK,GAAG,MAAM,IAAI,CAACV,QAAQ,CAACS,WAAW,CAAC;MAC9C,IAAI,CAACV,KAAK,CAAC2B,GAAG,CAAC;QAAEP,WAAW,EAAET,KAAK,CAACiB,YAAY;QAAEC,gBAAgB,EAAElB,KAAK,CAACmB;MAAU,CAAE,CAAC;MACvF;MACArB,UAAU,CAACW,WAAW,GAAGT,KAAK,CAACiB,YAAY;MAC3C,OAAOjB,KAAK,CAACiB,YAAY;IAC3B;EACF;EAEA;;;;EAIQ1B,QAAQA,CAACD,QAA2B;IAC1C,IAAI8B,IAAI,GAAiBC,OAAO,CAACC,OAAO,EAAE;IAC1C,OAAO,MAAOvB,WAA6B,IAA0B;MACnE;MACA;MACA,MAAMqB,IAAI;MACVA,IAAI,GAAGA,IAAI,CAERG,KAAK,CAAC,MAAM,IAAI,CAAC,CAEjBC,IAAI,CAAC,YAAW;QACf,MAAMC,UAAU,GAAG9B,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAACF,iBAAiB;QACtD,IAAI+B,UAAU,IAAIvC,WAAW,EAAE;UAC7B,MAAM,IAAAJ,UAAA,CAAA4C,UAAU,EAACxC,WAAW,GAAGuC,UAAU,CAAC;QAC5C;QACA,IAAI,CAAC/B,iBAAiB,GAAGC,IAAI,CAACC,GAAG,EAAE;QACnC,OAAO,MAAMN,QAAQ,CAACS,WAAW,CAAC;MACpC,CAAC,CAAC;MACJ,OAAO,MAAMqB,IAAI;IACnB,CAAC;EACH;;AA1GFO,OAAA,CAAAxC,eAAA,GAAAA,eAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}