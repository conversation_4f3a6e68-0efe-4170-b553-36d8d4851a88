{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { GridSignature } from '../utils/useGridApiEventHandler';\nimport { useGridApiMethod } from '../utils';\nimport { isFunction } from '../../utils/utils';\nexport const useGridStateInitialization = (apiRef, props) => {\n  const controlStateMapRef = React.useRef({});\n  const [, rawForceUpdate] = React.useState();\n  const registerControlState = React.useCallback(controlStateItem => {\n    controlStateMapRef.current[controlStateItem.stateId] = controlStateItem;\n  }, []);\n  const setState = React.useCallback((state, reason) => {\n    let newState;\n    if (isFunction(state)) {\n      newState = state(apiRef.current.state);\n    } else {\n      newState = state;\n    }\n    if (apiRef.current.state === newState) {\n      return false;\n    }\n    let ignoreSetState = false;\n\n    // Apply the control state constraints\n    const updatedControlStateIds = [];\n    Object.keys(controlStateMapRef.current).forEach(stateId => {\n      const controlState = controlStateMapRef.current[stateId];\n      const oldSubState = controlState.stateSelector(apiRef.current.state, apiRef.current.instanceId);\n      const newSubState = controlState.stateSelector(newState, apiRef.current.instanceId);\n      if (newSubState === oldSubState) {\n        return;\n      }\n      updatedControlStateIds.push({\n        stateId: controlState.stateId,\n        hasPropChanged: newSubState !== controlState.propModel\n      });\n\n      // The state is controlled, the prop should always win\n      if (controlState.propModel !== undefined && newSubState !== controlState.propModel) {\n        ignoreSetState = true;\n      }\n    });\n    if (updatedControlStateIds.length > 1) {\n      // Each hook modify its own state, and it should not leak\n      // Events are here to forward to other hooks and apply changes.\n      // You are trying to update several states in a no isolated way.\n      throw new Error(`You're not allowed to update several sub-state in one transaction. You already updated ${updatedControlStateIds[0].stateId}, therefore, you're not allowed to update ${updatedControlStateIds.map(el => el.stateId).join(', ')} in the same transaction.`);\n    }\n    if (!ignoreSetState) {\n      // We always assign it as we mutate rows for perf reason.\n      apiRef.current.state = newState;\n      if (apiRef.current.publishEvent) {\n        apiRef.current.publishEvent('stateChange', newState);\n      }\n      apiRef.current.store.update(newState);\n    }\n    if (updatedControlStateIds.length === 1) {\n      const {\n        stateId,\n        hasPropChanged\n      } = updatedControlStateIds[0];\n      const controlState = controlStateMapRef.current[stateId];\n      const model = controlState.stateSelector(newState, apiRef.current.instanceId);\n      if (controlState.propOnChange && hasPropChanged) {\n        const details = props.signature === GridSignature.DataGridPro ? {\n          api: apiRef.current,\n          reason\n        } : {\n          reason\n        };\n        controlState.propOnChange(model, details);\n      }\n      if (!ignoreSetState) {\n        apiRef.current.publishEvent(controlState.changeEvent, model, {\n          reason\n        });\n      }\n    }\n    return !ignoreSetState;\n  }, [apiRef, props.signature]);\n  const updateControlState = React.useCallback((key, state, reason) => {\n    return apiRef.current.setState(previousState => {\n      return _extends({}, previousState, {\n        [key]: state(previousState[key])\n      });\n    }, reason);\n  }, [apiRef]);\n  const forceUpdate = React.useCallback(() => rawForceUpdate(() => apiRef.current.state), [apiRef]);\n  const publicStateApi = {\n    setState,\n    forceUpdate\n  };\n  const privateStateApi = {\n    updateControlState,\n    registerControlState\n  };\n  useGridApiMethod(apiRef, publicStateApi, 'public');\n  useGridApiMethod(apiRef, privateStateApi, 'private');\n};", "map": {"version": 3, "names": ["_extends", "React", "GridSignature", "useGridApiMethod", "isFunction", "useGridStateInitialization", "apiRef", "props", "controlStateMapRef", "useRef", "rawForceUpdate", "useState", "registerControlState", "useCallback", "controlStateItem", "current", "stateId", "setState", "state", "reason", "newState", "ignoreSetState", "updatedControlStateIds", "Object", "keys", "for<PERSON>ach", "controlState", "oldSubState", "stateSelector", "instanceId", "newSubState", "push", "hasPropChanged", "propModel", "undefined", "length", "Error", "map", "el", "join", "publishEvent", "store", "update", "model", "propOnChange", "details", "signature", "DataGridPro", "api", "changeEvent", "updateControlState", "key", "previousState", "forceUpdate", "publicStateApi", "privateStateApi"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/x-data-grid/hooks/core/useGridStateInitialization.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { GridSignature } from '../utils/useGridApiEventHandler';\nimport { useGridApiMethod } from '../utils';\nimport { isFunction } from '../../utils/utils';\nexport const useGridStateInitialization = (apiRef, props) => {\n  const controlStateMapRef = React.useRef({});\n  const [, rawForceUpdate] = React.useState();\n  const registerControlState = React.useCallback(controlStateItem => {\n    controlStateMapRef.current[controlStateItem.stateId] = controlStateItem;\n  }, []);\n  const setState = React.useCallback((state, reason) => {\n    let newState;\n    if (isFunction(state)) {\n      newState = state(apiRef.current.state);\n    } else {\n      newState = state;\n    }\n    if (apiRef.current.state === newState) {\n      return false;\n    }\n    let ignoreSetState = false;\n\n    // Apply the control state constraints\n    const updatedControlStateIds = [];\n    Object.keys(controlStateMapRef.current).forEach(stateId => {\n      const controlState = controlStateMapRef.current[stateId];\n      const oldSubState = controlState.stateSelector(apiRef.current.state, apiRef.current.instanceId);\n      const newSubState = controlState.stateSelector(newState, apiRef.current.instanceId);\n      if (newSubState === oldSubState) {\n        return;\n      }\n      updatedControlStateIds.push({\n        stateId: controlState.stateId,\n        hasPropChanged: newSubState !== controlState.propModel\n      });\n\n      // The state is controlled, the prop should always win\n      if (controlState.propModel !== undefined && newSubState !== controlState.propModel) {\n        ignoreSetState = true;\n      }\n    });\n    if (updatedControlStateIds.length > 1) {\n      // Each hook modify its own state, and it should not leak\n      // Events are here to forward to other hooks and apply changes.\n      // You are trying to update several states in a no isolated way.\n      throw new Error(`You're not allowed to update several sub-state in one transaction. You already updated ${updatedControlStateIds[0].stateId}, therefore, you're not allowed to update ${updatedControlStateIds.map(el => el.stateId).join(', ')} in the same transaction.`);\n    }\n    if (!ignoreSetState) {\n      // We always assign it as we mutate rows for perf reason.\n      apiRef.current.state = newState;\n      if (apiRef.current.publishEvent) {\n        apiRef.current.publishEvent('stateChange', newState);\n      }\n      apiRef.current.store.update(newState);\n    }\n    if (updatedControlStateIds.length === 1) {\n      const {\n        stateId,\n        hasPropChanged\n      } = updatedControlStateIds[0];\n      const controlState = controlStateMapRef.current[stateId];\n      const model = controlState.stateSelector(newState, apiRef.current.instanceId);\n      if (controlState.propOnChange && hasPropChanged) {\n        const details = props.signature === GridSignature.DataGridPro ? {\n          api: apiRef.current,\n          reason\n        } : {\n          reason\n        };\n        controlState.propOnChange(model, details);\n      }\n      if (!ignoreSetState) {\n        apiRef.current.publishEvent(controlState.changeEvent, model, {\n          reason\n        });\n      }\n    }\n    return !ignoreSetState;\n  }, [apiRef, props.signature]);\n  const updateControlState = React.useCallback((key, state, reason) => {\n    return apiRef.current.setState(previousState => {\n      return _extends({}, previousState, {\n        [key]: state(previousState[key])\n      });\n    }, reason);\n  }, [apiRef]);\n  const forceUpdate = React.useCallback(() => rawForceUpdate(() => apiRef.current.state), [apiRef]);\n  const publicStateApi = {\n    setState,\n    forceUpdate\n  };\n  const privateStateApi = {\n    updateControlState,\n    registerControlState\n  };\n  useGridApiMethod(apiRef, publicStateApi, 'public');\n  useGridApiMethod(apiRef, privateStateApi, 'private');\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,gBAAgB,QAAQ,UAAU;AAC3C,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAO,MAAMC,0BAA0B,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAC3D,MAAMC,kBAAkB,GAAGP,KAAK,CAACQ,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3C,MAAM,GAAGC,cAAc,CAAC,GAAGT,KAAK,CAACU,QAAQ,CAAC,CAAC;EAC3C,MAAMC,oBAAoB,GAAGX,KAAK,CAACY,WAAW,CAACC,gBAAgB,IAAI;IACjEN,kBAAkB,CAACO,OAAO,CAACD,gBAAgB,CAACE,OAAO,CAAC,GAAGF,gBAAgB;EACzE,CAAC,EAAE,EAAE,CAAC;EACN,MAAMG,QAAQ,GAAGhB,KAAK,CAACY,WAAW,CAAC,CAACK,KAAK,EAAEC,MAAM,KAAK;IACpD,IAAIC,QAAQ;IACZ,IAAIhB,UAAU,CAACc,KAAK,CAAC,EAAE;MACrBE,QAAQ,GAAGF,KAAK,CAACZ,MAAM,CAACS,OAAO,CAACG,KAAK,CAAC;IACxC,CAAC,MAAM;MACLE,QAAQ,GAAGF,KAAK;IAClB;IACA,IAAIZ,MAAM,CAACS,OAAO,CAACG,KAAK,KAAKE,QAAQ,EAAE;MACrC,OAAO,KAAK;IACd;IACA,IAAIC,cAAc,GAAG,KAAK;;IAE1B;IACA,MAAMC,sBAAsB,GAAG,EAAE;IACjCC,MAAM,CAACC,IAAI,CAAChB,kBAAkB,CAACO,OAAO,CAAC,CAACU,OAAO,CAACT,OAAO,IAAI;MACzD,MAAMU,YAAY,GAAGlB,kBAAkB,CAACO,OAAO,CAACC,OAAO,CAAC;MACxD,MAAMW,WAAW,GAAGD,YAAY,CAACE,aAAa,CAACtB,MAAM,CAACS,OAAO,CAACG,KAAK,EAAEZ,MAAM,CAACS,OAAO,CAACc,UAAU,CAAC;MAC/F,MAAMC,WAAW,GAAGJ,YAAY,CAACE,aAAa,CAACR,QAAQ,EAAEd,MAAM,CAACS,OAAO,CAACc,UAAU,CAAC;MACnF,IAAIC,WAAW,KAAKH,WAAW,EAAE;QAC/B;MACF;MACAL,sBAAsB,CAACS,IAAI,CAAC;QAC1Bf,OAAO,EAAEU,YAAY,CAACV,OAAO;QAC7BgB,cAAc,EAAEF,WAAW,KAAKJ,YAAY,CAACO;MAC/C,CAAC,CAAC;;MAEF;MACA,IAAIP,YAAY,CAACO,SAAS,KAAKC,SAAS,IAAIJ,WAAW,KAAKJ,YAAY,CAACO,SAAS,EAAE;QAClFZ,cAAc,GAAG,IAAI;MACvB;IACF,CAAC,CAAC;IACF,IAAIC,sBAAsB,CAACa,MAAM,GAAG,CAAC,EAAE;MACrC;MACA;MACA;MACA,MAAM,IAAIC,KAAK,CAAC,0FAA0Fd,sBAAsB,CAAC,CAAC,CAAC,CAACN,OAAO,6CAA6CM,sBAAsB,CAACe,GAAG,CAACC,EAAE,IAAIA,EAAE,CAACtB,OAAO,CAAC,CAACuB,IAAI,CAAC,IAAI,CAAC,2BAA2B,CAAC;IAC7Q;IACA,IAAI,CAAClB,cAAc,EAAE;MACnB;MACAf,MAAM,CAACS,OAAO,CAACG,KAAK,GAAGE,QAAQ;MAC/B,IAAId,MAAM,CAACS,OAAO,CAACyB,YAAY,EAAE;QAC/BlC,MAAM,CAACS,OAAO,CAACyB,YAAY,CAAC,aAAa,EAAEpB,QAAQ,CAAC;MACtD;MACAd,MAAM,CAACS,OAAO,CAAC0B,KAAK,CAACC,MAAM,CAACtB,QAAQ,CAAC;IACvC;IACA,IAAIE,sBAAsB,CAACa,MAAM,KAAK,CAAC,EAAE;MACvC,MAAM;QACJnB,OAAO;QACPgB;MACF,CAAC,GAAGV,sBAAsB,CAAC,CAAC,CAAC;MAC7B,MAAMI,YAAY,GAAGlB,kBAAkB,CAACO,OAAO,CAACC,OAAO,CAAC;MACxD,MAAM2B,KAAK,GAAGjB,YAAY,CAACE,aAAa,CAACR,QAAQ,EAAEd,MAAM,CAACS,OAAO,CAACc,UAAU,CAAC;MAC7E,IAAIH,YAAY,CAACkB,YAAY,IAAIZ,cAAc,EAAE;QAC/C,MAAMa,OAAO,GAAGtC,KAAK,CAACuC,SAAS,KAAK5C,aAAa,CAAC6C,WAAW,GAAG;UAC9DC,GAAG,EAAE1C,MAAM,CAACS,OAAO;UACnBI;QACF,CAAC,GAAG;UACFA;QACF,CAAC;QACDO,YAAY,CAACkB,YAAY,CAACD,KAAK,EAAEE,OAAO,CAAC;MAC3C;MACA,IAAI,CAACxB,cAAc,EAAE;QACnBf,MAAM,CAACS,OAAO,CAACyB,YAAY,CAACd,YAAY,CAACuB,WAAW,EAAEN,KAAK,EAAE;UAC3DxB;QACF,CAAC,CAAC;MACJ;IACF;IACA,OAAO,CAACE,cAAc;EACxB,CAAC,EAAE,CAACf,MAAM,EAAEC,KAAK,CAACuC,SAAS,CAAC,CAAC;EAC7B,MAAMI,kBAAkB,GAAGjD,KAAK,CAACY,WAAW,CAAC,CAACsC,GAAG,EAAEjC,KAAK,EAAEC,MAAM,KAAK;IACnE,OAAOb,MAAM,CAACS,OAAO,CAACE,QAAQ,CAACmC,aAAa,IAAI;MAC9C,OAAOpD,QAAQ,CAAC,CAAC,CAAC,EAAEoD,aAAa,EAAE;QACjC,CAACD,GAAG,GAAGjC,KAAK,CAACkC,aAAa,CAACD,GAAG,CAAC;MACjC,CAAC,CAAC;IACJ,CAAC,EAAEhC,MAAM,CAAC;EACZ,CAAC,EAAE,CAACb,MAAM,CAAC,CAAC;EACZ,MAAM+C,WAAW,GAAGpD,KAAK,CAACY,WAAW,CAAC,MAAMH,cAAc,CAAC,MAAMJ,MAAM,CAACS,OAAO,CAACG,KAAK,CAAC,EAAE,CAACZ,MAAM,CAAC,CAAC;EACjG,MAAMgD,cAAc,GAAG;IACrBrC,QAAQ;IACRoC;EACF,CAAC;EACD,MAAME,eAAe,GAAG;IACtBL,kBAAkB;IAClBtC;EACF,CAAC;EACDT,gBAAgB,CAACG,MAAM,EAAEgD,cAAc,EAAE,QAAQ,CAAC;EAClDnD,gBAAgB,CAACG,MAAM,EAAEiD,eAAe,EAAE,SAAS,CAAC;AACtD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}