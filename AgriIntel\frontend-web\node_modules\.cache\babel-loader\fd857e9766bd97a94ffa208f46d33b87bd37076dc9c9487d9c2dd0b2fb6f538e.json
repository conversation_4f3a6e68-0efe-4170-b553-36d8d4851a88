{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { CacheProvider } from '@emotion/react';\nimport createCache from '@emotion/cache';\n\n// prepend: true moves MUI styles to the top of the <head> so they're loaded first.\n// It allows developers to easily override MUI styles with other styling solutions, like CSS modules.\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet cache;\nif (typeof document === 'object') {\n  cache = createCache({\n    key: 'css',\n    prepend: true\n  });\n}\nexport default function StyledEngineProvider(props) {\n  const {\n    injectFirst,\n    children\n  } = props;\n  return injectFirst && cache ? /*#__PURE__*/_jsx(CacheProvider, {\n    value: cache,\n    children: children\n  }) : children;\n}\nprocess.env.NODE_ENV !== \"production\" ? StyledEngineProvider.propTypes = {\n  /**\n   * Your component tree.\n   */\n  children: PropTypes.node,\n  /**\n   * By default, the styles are injected last in the <head> element of the page.\n   * As a result, they gain more specificity than any other style sheet.\n   * If you want to override MUI's styles, set this prop.\n   */\n  injectFirst: PropTypes.bool\n} : void 0;", "map": {"version": 3, "names": ["React", "PropTypes", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "createCache", "jsx", "_jsx", "cache", "document", "key", "prepend", "StyledEngineProvider", "props", "injectFirst", "children", "value", "process", "env", "NODE_ENV", "propTypes", "node", "bool"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/styled-engine/StyledEngineProvider/StyledEngineProvider.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport { CacheProvider } from '@emotion/react';\nimport createCache from '@emotion/cache';\n\n// prepend: true moves MUI styles to the top of the <head> so they're loaded first.\n// It allows developers to easily override MUI styles with other styling solutions, like CSS modules.\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nlet cache;\nif (typeof document === 'object') {\n  cache = createCache({\n    key: 'css',\n    prepend: true\n  });\n}\nexport default function StyledEngineProvider(props) {\n  const {\n    injectFirst,\n    children\n  } = props;\n  return injectFirst && cache ? /*#__PURE__*/_jsx(CacheProvider, {\n    value: cache,\n    children: children\n  }) : children;\n}\nprocess.env.NODE_ENV !== \"production\" ? StyledEngineProvider.propTypes = {\n  /**\n   * Your component tree.\n   */\n  children: PropTypes.node,\n  /**\n   * By default, the styles are injected last in the <head> element of the page.\n   * As a result, they gain more specificity than any other style sheet.\n   * If you want to override MUI's styles, set this prop.\n   */\n  injectFirst: PropTypes.bool\n} : void 0;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,SAASC,aAAa,QAAQ,gBAAgB;AAC9C,OAAOC,WAAW,MAAM,gBAAgB;;AAExC;AACA;AACA,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,IAAIC,KAAK;AACT,IAAI,OAAOC,QAAQ,KAAK,QAAQ,EAAE;EAChCD,KAAK,GAAGH,WAAW,CAAC;IAClBK,GAAG,EAAE,KAAK;IACVC,OAAO,EAAE;EACX,CAAC,CAAC;AACJ;AACA,eAAe,SAASC,oBAAoBA,CAACC,KAAK,EAAE;EAClD,MAAM;IACJC,WAAW;IACXC;EACF,CAAC,GAAGF,KAAK;EACT,OAAOC,WAAW,IAAIN,KAAK,GAAG,aAAaD,IAAI,CAACH,aAAa,EAAE;IAC7DY,KAAK,EAAER,KAAK;IACZO,QAAQ,EAAEA;EACZ,CAAC,CAAC,GAAGA,QAAQ;AACf;AACAE,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGP,oBAAoB,CAACQ,SAAS,GAAG;EACvE;AACF;AACA;EACEL,QAAQ,EAAEZ,SAAS,CAACkB,IAAI;EACxB;AACF;AACA;AACA;AACA;EACEP,WAAW,EAAEX,SAAS,CAACmB;AACzB,CAAC,GAAG,KAAK,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}