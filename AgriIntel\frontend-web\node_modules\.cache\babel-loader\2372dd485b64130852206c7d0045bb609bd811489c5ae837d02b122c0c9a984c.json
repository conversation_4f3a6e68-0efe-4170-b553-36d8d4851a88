{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{useState,useEffect,useCallback}from'react';import{useMockData}from'../utils/config';import api from'../services/apiService';import{mockFeedingRecords,mockFeedInventory,mockFeedingPlans,mockFeedingStats}from'../mocks/feedingData';// MongoDB imports removed - using API calls instead\nimport{useMongoDb}from'./useMongoDb';export const useFeedingData=()=>{const{isConnected}=useMongoDb();const[feedingRecords,setFeedingRecords]=useState([]);const[feedInventory,setFeedInventory]=useState([]);const[feedingPlans,setFeedingPlans]=useState([]);const[stats,setStats]=useState({totalFeedUsedToday:0,totalFeedCostToday:0,feedInventoryValue:0,lowStockItems:0,activeFeedingPlans:0,feedingRecordsThisWeek:0,feedUsageByType:{concentrate:0,forage:0,mineral:0}});const[loading,setLoading]=useState(true);const[error,setError]=useState(null);// Calculate feed usage by type\nconst calculateFeedUsageByType=useCallback(type=>{return feedingRecords.filter(record=>{var _feedInventory$find;return((_feedInventory$find=feedInventory.find(item=>item.id===record.feedId))===null||_feedInventory$find===void 0?void 0:_feedInventory$find.type)===type;}).reduce((sum,record)=>sum+record.quantity,0);},[feedingRecords,feedInventory]);// Update statistics\nconst updateStats=useCallback(()=>{const today=new Date();const todayRecords=feedingRecords.filter(record=>{const recordDate=new Date(record.date);return recordDate.toDateString()===today.toDateString();});const newStats={totalFeedUsedToday:todayRecords.reduce((sum,record)=>sum+record.quantity,0),totalFeedCostToday:todayRecords.reduce((sum,record)=>sum+record.quantity*record.costPerUnit,0),feedInventoryValue:feedInventory.reduce((sum,item)=>sum+item.quantity*item.costPerUnit,0),lowStockItems:feedInventory.filter(item=>item.quantity<item.reorderLevel).length,activeFeedingPlans:feedingPlans.filter(plan=>plan.isActive).length,feedingRecordsThisWeek:feedingRecords.filter(record=>{const recordDate=new Date(record.date);const diffTime=Math.abs(today.getTime()-recordDate.getTime());const diffDays=Math.ceil(diffTime/(1000*60*60*24));return diffDays<=7;}).length,feedUsageByType:{concentrate:calculateFeedUsageByType('concentrate'),forage:calculateFeedUsageByType('forage'),mineral:calculateFeedUsageByType('mineral')}};setStats(newStats);},[feedingRecords,feedInventory,feedingPlans,calculateFeedUsageByType]);// Fetch all feeding data\nconst fetchFeedingData=async()=>{try{setLoading(true);setError(null);if(isConnected&&!useMockData){// Use MongoDB\ntry{// Get collections\nconst feedingRecordsCollection=await getCollection('feeding_records');const feedInventoryCollection=await getCollection('feed_inventory');const feedingPlansCollection=await getCollection('feeding_plans');// Fetch data\nconst recordsData=await feedingRecordsCollection.find({}).toArray();const inventoryData=await feedInventoryCollection.find({}).toArray();const plansData=await feedingPlansCollection.find({}).toArray();// Set state\nsetFeedingRecords(recordsData);setFeedInventory(inventoryData);setFeedingPlans(plansData);console.log('Fetched feeding data from MongoDB:',{records:recordsData.length,inventory:inventoryData.length,plans:plansData.length});}catch(mongoError){console.error('Error fetching from MongoDB, falling back to mock data:',mongoError);// Fall back to mock data\nsetFeedingRecords(mockFeedingRecords);setFeedInventory(mockFeedInventory);setFeedingPlans(mockFeedingPlans);setStats(mockFeedingStats);}}else if(useMockData){// Use mock data\nawait new Promise(resolve=>setTimeout(resolve,1000));setFeedingRecords(mockFeedingRecords);setFeedInventory(mockFeedInventory);setFeedingPlans(mockFeedingPlans);setStats(mockFeedingStats);}else{// Use API\nconst[recordsRes,inventoryRes,plansRes]=await Promise.all([api.get('/feeding/records'),api.get('/feeding/inventory'),api.get('/feeding/plans')]);setFeedingRecords(recordsRes.data);setFeedInventory(inventoryRes.data);setFeedingPlans(plansRes.data);}updateStats();}catch(err){console.error('Failed to fetch feeding data:',err);setError('Failed to fetch feeding data');}finally{setLoading(false);}};// CRUD operations for feeding records\nconst addFeedingRecord=async record=>{try{setLoading(true);setError(null);if(isConnected&&!useMockData){// Use MongoDB\ntry{// Get collections\nconst feedingRecordsCollection=await getCollection('feeding_records');const feedInventoryCollection=await getCollection('feed_inventory');// Create new record with generated ID\nconst newRecord=_objectSpread(_objectSpread({},record),{},{id:\"record-\".concat(Date.now())});// Insert record into MongoDB\nawait feedingRecordsCollection.insertOne(newRecord);// Update inventory quantity\nconst inventoryItem=await feedInventoryCollection.findOne({id:record.feedId});if(inventoryItem){const newQuantity=inventoryItem.quantity-record.quantity;let newStatus=inventoryItem.status;if(newQuantity<=0){newStatus='reorder';}else if(newQuantity<inventoryItem.minimumStock){newStatus='low';}else{newStatus='available';}// Update inventory in MongoDB\nawait feedInventoryCollection.updateOne({id:record.feedId},{$set:{quantity:newQuantity,status:newStatus}});}// Refresh data\nawait fetchFeedingData();return newRecord;}catch(mongoError){console.error('Error adding feeding record to MongoDB:',mongoError);throw mongoError;}}else if(useMockData){// Simulate API delay\nawait new Promise(resolve=>setTimeout(resolve,500));// Create new record with generated ID\nconst newRecord=_objectSpread(_objectSpread({},record),{},{id:\"record-\".concat(Date.now())});// Update state\nsetFeedingRecords(prev=>[...prev,newRecord]);// Update inventory quantity\nconst updatedInventory=feedInventory.map(item=>{if(item.id===record.feedId){const newQuantity=item.quantity-record.quantity;let newStatus=item.status;if(newQuantity<=0){newStatus='reorder';}else if(newQuantity<item.minimumStock){newStatus='low';}else{newStatus='available';}return _objectSpread(_objectSpread({},item),{},{quantity:newQuantity,status:newStatus});}return item;});setFeedInventory(updatedInventory);updateStats();return newRecord;}else{// Use real API\nconst response=await api.post('/feeding/records',record);await fetchFeedingData();// Refresh all data\nreturn response.data;}}catch(err){console.error('Error adding feeding record:',err);setError('Failed to add feeding record');throw err;}finally{setLoading(false);}};const updateFeedingRecord=async(id,record)=>{try{setLoading(true);setError(null);if(isConnected&&!useMockData){// Use MongoDB\ntry{// Get collections\nconst feedingRecordsCollection=await getCollection('feeding_records');const feedInventoryCollection=await getCollection('feed_inventory');// Find the original record\nconst originalRecord=await feedingRecordsCollection.findOne({id});if(!originalRecord)throw new Error('Record not found');// Update record in MongoDB\nawait feedingRecordsCollection.updateOne({id},{$set:record});// If feed quantity changed, update inventory\nif(record.quantity!==undefined&&record.quantity!==originalRecord.quantity){const quantityDiff=originalRecord.quantity-record.quantity;// Get inventory item\nconst inventoryItem=await feedInventoryCollection.findOne({id:originalRecord.feedId});if(inventoryItem){const newQuantity=inventoryItem.quantity+quantityDiff;let newStatus='available';if(newQuantity<=0){newStatus='reorder';}else if(newQuantity<inventoryItem.minimumStock){newStatus='low';}else{newStatus='available';}// Update inventory in MongoDB\nawait feedInventoryCollection.updateOne({id:originalRecord.feedId},{$set:{quantity:newQuantity,status:newStatus}});}}// Refresh data\nawait fetchFeedingData();// Return updated record\nconst updatedRecord=await feedingRecordsCollection.findOne({id});return updatedRecord;}catch(mongoError){console.error('Error updating feeding record in MongoDB:',mongoError);throw mongoError;}}else if(useMockData){// Simulate API delay\nawait new Promise(resolve=>setTimeout(resolve,500));// Find the original record\nconst originalRecord=feedingRecords.find(r=>r.id===id);if(!originalRecord)throw new Error('Record not found');// Update record\nconst updatedRecord=_objectSpread(_objectSpread({},originalRecord),record);// Update state\nsetFeedingRecords(prev=>prev.map(r=>r.id===id?updatedRecord:r));// If feed quantity changed, update inventory\nif(record.quantity!==undefined&&record.quantity!==originalRecord.quantity){const quantityDiff=originalRecord.quantity-record.quantity;const updatedInventory=feedInventory.map(item=>{if(item.id===originalRecord.feedId){const newQuantity=item.quantity+quantityDiff;let newStatus='available';if(newQuantity<=0){newStatus='reorder';}else if(newQuantity<item.minimumStock){newStatus='low';}else{newStatus='available';}return _objectSpread(_objectSpread({},item),{},{quantity:newQuantity,status:newStatus});}return item;});setFeedInventory(updatedInventory);}updateStats();return updatedRecord;}else{// Use real API\nconst response=await api.put(\"/feeding/records/\".concat(id),record);await fetchFeedingData();// Refresh all data\nreturn response.data;}}catch(err){console.error('Error updating feeding record:',err);setError('Failed to update feeding record');throw err;}finally{setLoading(false);}};const deleteFeedingRecord=async id=>{try{setLoading(true);setError(null);if(isConnected&&!useMockData){// Use MongoDB\ntry{// Get collections\nconst feedingRecordsCollection=await getCollection('feeding_records');const feedInventoryCollection=await getCollection('feed_inventory');// Find the record to delete\nconst recordToDelete=await feedingRecordsCollection.findOne({id});if(!recordToDelete)throw new Error('Record not found');// Delete record from MongoDB\nawait feedingRecordsCollection.deleteOne({id});// Return feed to inventory\nconst inventoryItem=await feedInventoryCollection.findOne({id:recordToDelete.feedId});if(inventoryItem){const newQuantity=inventoryItem.quantity+recordToDelete.quantity;let newStatus='available';if(newQuantity<=0){newStatus='reorder';}else if(newQuantity<inventoryItem.minimumStock){newStatus='low';}else{newStatus='available';}// Update inventory in MongoDB\nawait feedInventoryCollection.updateOne({id:recordToDelete.feedId},{$set:{quantity:newQuantity,status:newStatus}});}// Refresh data\nawait fetchFeedingData();return true;}catch(mongoError){console.error('Error deleting feeding record from MongoDB:',mongoError);throw mongoError;}}else if(useMockData){// Simulate API delay\nawait new Promise(resolve=>setTimeout(resolve,500));// Find the record to delete\nconst recordToDelete=feedingRecords.find(r=>r.id===id);if(!recordToDelete)throw new Error('Record not found');// Update state\nsetFeedingRecords(prev=>prev.filter(r=>r.id!==id));// Return feed to inventory\nconst updatedInventory=feedInventory.map(item=>{if(item.id===recordToDelete.feedId){const newQuantity=item.quantity+recordToDelete.quantity;let newStatus='available';if(newQuantity<=0){newStatus='reorder';}else if(newQuantity<item.minimumStock){newStatus='low';}else{newStatus='available';}return _objectSpread(_objectSpread({},item),{},{quantity:newQuantity,status:newStatus});}return item;});setFeedInventory(updatedInventory);updateStats();return true;}else{// Use real API\nawait api.delete(\"/feeding/records/\".concat(id));await fetchFeedingData();// Refresh all data\nreturn true;}}catch(err){console.error('Error deleting feeding record:',err);setError('Failed to delete feeding record');throw err;}finally{setLoading(false);}};// CRUD operations for feed inventory\nconst addFeedInventory=async item=>{try{setLoading(true);setError(null);if(isConnected&&!useMockData){// Use MongoDB\ntry{// Get collection\nconst feedInventoryCollection=await getCollection('feed_inventory');// Create new inventory item with generated ID\nconst newItem=_objectSpread(_objectSpread({},item),{},{id:\"feed-\".concat(Date.now())});// Insert item into MongoDB\nawait feedInventoryCollection.insertOne(newItem);// Refresh data\nawait fetchFeedingData();return newItem;}catch(mongoError){console.error('Error adding feed inventory to MongoDB:',mongoError);throw mongoError;}}else if(useMockData){// Simulate API delay\nawait new Promise(resolve=>setTimeout(resolve,500));// Create new inventory item with generated ID\nconst newItem=_objectSpread(_objectSpread({},item),{},{id:\"feed-\".concat(Date.now())});// Update state\nsetFeedInventory(prev=>[...prev,newItem]);updateStats();return newItem;}else{// Use real API\nconst response=await api.post('/feeding/inventory',item);await fetchFeedingData();// Refresh all data\nreturn response.data;}}catch(err){console.error('Error adding feed inventory:',err);setError('Failed to add feed inventory');throw err;}finally{setLoading(false);}};const updateFeedInventory=async(id,item)=>{try{setLoading(true);setError(null);if(isConnected&&!useMockData){// Use MongoDB\ntry{// Get collection\nconst feedInventoryCollection=await getCollection('feed_inventory');// Find the original item\nconst originalItem=await feedInventoryCollection.findOne({id});if(!originalItem)throw new Error('Inventory item not found');// Update item in MongoDB\nawait feedInventoryCollection.updateOne({id},{$set:item});// Refresh data\nawait fetchFeedingData();// Return updated item\nconst updatedItem=await feedInventoryCollection.findOne({id});return updatedItem;}catch(mongoError){console.error('Error updating feed inventory in MongoDB:',mongoError);throw mongoError;}}else if(useMockData){// Simulate API delay\nawait new Promise(resolve=>setTimeout(resolve,500));// Find the original item\nconst originalItem=feedInventory.find(i=>i.id===id);if(!originalItem)throw new Error('Inventory item not found');// Update item\nconst updatedItem=_objectSpread(_objectSpread({},originalItem),item);// Update state\nsetFeedInventory(prev=>prev.map(i=>i.id===id?updatedItem:i));updateStats();return updatedItem;}else{// Use real API\nconst response=await api.put(\"/feeding/inventory/\".concat(id),item);await fetchFeedingData();// Refresh all data\nreturn response.data;}}catch(err){console.error('Error updating feed inventory:',err);setError('Failed to update feed inventory');throw err;}finally{setLoading(false);}};const deleteFeedInventory=async id=>{try{setLoading(true);setError(null);if(isConnected&&!useMockData){// Use MongoDB\ntry{// Get collections\nconst feedInventoryCollection=await getCollection('feed_inventory');const feedingRecordsCollection=await getCollection('feeding_records');// Check if this feed is used in any feeding records\nconst recordsWithFeed=await feedingRecordsCollection.find({feedId:id}).toArray();if(recordsWithFeed.length>0){throw new Error('Cannot delete feed that is used in feeding records');}// Delete item from MongoDB\nawait feedInventoryCollection.deleteOne({id});// Refresh data\nawait fetchFeedingData();return true;}catch(mongoError){console.error('Error deleting feed inventory from MongoDB:',mongoError);throw mongoError;}}else if(useMockData){// Simulate API delay\nawait new Promise(resolve=>setTimeout(resolve,500));// Check if there are any feeding records using this feed\nconst recordsUsingFeed=feedingRecords.some(record=>record.feedId===id);if(recordsUsingFeed){throw new Error('Cannot delete feed that is used in feeding records');}// Update state\nsetFeedInventory(prev=>prev.filter(i=>i.id!==id));updateStats();return true;}else{// Use real API\nawait api.delete(\"/feeding/inventory/\".concat(id));await fetchFeedingData();// Refresh all data\nreturn true;}}catch(err){console.error('Error deleting feed inventory:',err);setError('Failed to delete feed inventory');throw err;}finally{setLoading(false);}};// Generate Excel report\nconst generateExcelReport=async reportType=>{try{setLoading(true);setError(null);if(isConnected&&!useMockData){try{// Get data from MongoDB\nlet data=[];if(reportType==='records'){const feedingRecordsCollection=await getCollection('feeding_records');data=await feedingRecordsCollection.find({}).toArray();}else if(reportType==='inventory'){const feedInventoryCollection=await getCollection('feed_inventory');data=await feedInventoryCollection.find({}).toArray();}else if(reportType==='plans'){const feedingPlansCollection=await getCollection('feeding_plans');data=await feedingPlansCollection.find({}).toArray();}// In a real implementation, this would generate an Excel file\n// For now, we'll just return a filename\nconsole.log(\"Generated \".concat(reportType,\" report with \").concat(data.length,\" records\"));return\"\".concat(reportType,\"_report_\").concat(new Date().toISOString().split('T')[0],\".xlsx\");}catch(mongoError){console.error(\"Error generating \".concat(reportType,\" report from MongoDB:\"),mongoError);throw mongoError;}}else if(useMockData){// Simulate report generation\nawait new Promise(resolve=>setTimeout(resolve,1500));return\"\".concat(reportType,\"_report_\").concat(new Date().toISOString().split('T')[0],\".xlsx\");}else{// Use real API\nconst response=await api.get(\"/feeding/reports/\".concat(reportType));return response.data.reportUrl;}}catch(err){console.error(\"Error generating \".concat(reportType,\" report:\"),err);setError(\"Failed to generate \".concat(reportType,\" report\"));throw err;}finally{setLoading(false);}};// Initial data fetch\nuseEffect(()=>{fetchFeedingData();},[]);return{feedingRecords,feedInventory,feedingPlans,stats,loading,error,fetchFeedingData,addFeedingRecord,updateFeedingRecord,deleteFeedingRecord,addFeedInventory,updateFeedInventory,deleteFeedInventory,generateExcelReport};};", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "useMockData", "api", "mockFeedingRecords", "mockFeedInventory", "mockFeedingPlans", "mockFeedingStats", "useMongoDb", "useFeedingData", "isConnected", "feedingRecords", "setFeedingRecords", "feedInventory", "setFeedInventory", "feedingPlans", "setFeedingPlans", "stats", "setStats", "totalFeedUsedToday", "totalFeedCostToday", "feedInventoryValue", "lowStockItems", "activeFeedingPlans", "feedingRecordsThisWeek", "feedUsageByType", "concentrate", "forage", "mineral", "loading", "setLoading", "error", "setError", "calculateFeedUsageByType", "type", "filter", "record", "_feedInventory$find", "find", "item", "id", "feedId", "reduce", "sum", "quantity", "updateStats", "today", "Date", "todayRecords", "recordDate", "date", "toDateString", "newStats", "costPerUnit", "reorderLevel", "length", "plan", "isActive", "diffTime", "Math", "abs", "getTime", "diffDays", "ceil", "fetchFeedingData", "feedingRecordsCollection", "getCollection", "feedInventoryCollection", "feedingPlansCollection", "recordsData", "toArray", "inventoryData", "plansData", "console", "log", "records", "inventory", "plans", "mongoError", "Promise", "resolve", "setTimeout", "recordsRes", "inventoryRes", "plansRes", "all", "get", "data", "err", "addFeedingRecord", "newRecord", "_objectSpread", "concat", "now", "insertOne", "inventoryItem", "findOne", "newQuantity", "newStatus", "status", "minimumStock", "updateOne", "$set", "prev", "updatedInventory", "map", "response", "post", "updateFeedingRecord", "originalRecord", "Error", "undefined", "quantityDiff", "updatedRecord", "r", "put", "deleteFeedingRecord", "recordToDelete", "deleteOne", "delete", "addFeedInventory", "newItem", "updateFeedInventory", "originalItem", "updatedItem", "i", "deleteFeedInventory", "recordsWithFeed", "recordsUsingFeed", "some", "generateExcelReport", "reportType", "toISOString", "split", "reportUrl"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/hooks/useFeedingData.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\r\nimport { useMockData } from '../utils/config';\r\nimport api from '../services/apiService';\r\nimport {\r\n  mockFeedingRecords,\r\n  mockFeedInventory,\r\n  mockFeedingPlans,\r\n  mockFeedingStats\r\n} from '../mocks/feedingData';\r\nimport { FeedingRecord, FeedInventory, FeedingPlan, FeedingStats } from '../types/feeding';\r\n// MongoDB imports removed - using API calls instead\r\nimport { useMongoDb } from './useMongoDb';\r\n\r\nexport const useFeedingData = () => {\r\n  const { isConnected } = useMongoDb();\r\n  const [feedingRecords, setFeedingRecords] = useState<FeedingRecord[]>([]);\r\n  const [feedInventory, setFeedInventory] = useState<FeedInventory[]>([]);\r\n  const [feedingPlans, setFeedingPlans] = useState<FeedingPlan[]>([]);\r\n  const [stats, setStats] = useState<FeedingStats>({\r\n    totalFeedUsedToday: 0,\r\n    totalFeedCostToday: 0,\r\n    feedInventoryValue: 0,\r\n    lowStockItems: 0,\r\n    activeFeedingPlans: 0,\r\n    feedingRecordsThisWeek: 0,\r\n    feedUsageByType: {\r\n      concentrate: 0,\r\n      forage: 0,\r\n      mineral: 0\r\n    }\r\n  });\r\n  const [loading, setLoading] = useState(true);\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // Calculate feed usage by type\r\n  const calculateFeedUsageByType = useCallback((type: string): number => {\r\n    return feedingRecords\r\n      .filter(record => feedInventory.find(item => item.id === record.feedId)?.type === type)\r\n      .reduce((sum, record) => sum + record.quantity, 0);\r\n  }, [feedingRecords, feedInventory]);\r\n\r\n  // Update statistics\r\n  const updateStats = useCallback(() => {\r\n    const today = new Date();\r\n    const todayRecords = feedingRecords.filter(record => {\r\n      const recordDate = new Date(record.date);\r\n      return recordDate.toDateString() === today.toDateString();\r\n    });\r\n\r\n    const newStats: FeedingStats = {\r\n      totalFeedUsedToday: todayRecords.reduce((sum, record) => sum + record.quantity, 0),\r\n      totalFeedCostToday: todayRecords.reduce((sum, record) => sum + (record.quantity * record.costPerUnit), 0),\r\n      feedInventoryValue: feedInventory.reduce((sum, item) => sum + (item.quantity * item.costPerUnit), 0),\r\n      lowStockItems: feedInventory.filter(item => item.quantity < item.reorderLevel).length,\r\n      activeFeedingPlans: feedingPlans.filter(plan => plan.isActive).length,\r\n      feedingRecordsThisWeek: feedingRecords.filter(record => {\r\n        const recordDate = new Date(record.date);\r\n        const diffTime = Math.abs(today.getTime() - recordDate.getTime());\r\n        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\r\n        return diffDays <= 7;\r\n      }).length,\r\n      feedUsageByType: {\r\n        concentrate: calculateFeedUsageByType('concentrate'),\r\n        forage: calculateFeedUsageByType('forage'),\r\n        mineral: calculateFeedUsageByType('mineral')\r\n      }\r\n    };\r\n\r\n    setStats(newStats);\r\n  }, [feedingRecords, feedInventory, feedingPlans, calculateFeedUsageByType]);\r\n\r\n  // Fetch all feeding data\r\n  const fetchFeedingData = async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      if (isConnected && !useMockData) {\r\n        // Use MongoDB\r\n        try {\r\n          // Get collections\r\n          const feedingRecordsCollection = await getCollection('feeding_records');\r\n          const feedInventoryCollection = await getCollection('feed_inventory');\r\n          const feedingPlansCollection = await getCollection('feeding_plans');\r\n\r\n          // Fetch data\r\n          const recordsData = await feedingRecordsCollection.find({}).toArray();\r\n          const inventoryData = await feedInventoryCollection.find({}).toArray();\r\n          const plansData = await feedingPlansCollection.find({}).toArray();\r\n\r\n          // Set state\r\n          setFeedingRecords(recordsData);\r\n          setFeedInventory(inventoryData);\r\n          setFeedingPlans(plansData);\r\n\r\n          console.log('Fetched feeding data from MongoDB:', {\r\n            records: recordsData.length,\r\n            inventory: inventoryData.length,\r\n            plans: plansData.length\r\n          });\r\n        } catch (mongoError) {\r\n          console.error('Error fetching from MongoDB, falling back to mock data:', mongoError);\r\n          // Fall back to mock data\r\n          setFeedingRecords(mockFeedingRecords);\r\n          setFeedInventory(mockFeedInventory);\r\n          setFeedingPlans(mockFeedingPlans);\r\n          setStats(mockFeedingStats);\r\n        }\r\n      } else if (useMockData) {\r\n        // Use mock data\r\n        await new Promise(resolve => setTimeout(resolve, 1000));\r\n        setFeedingRecords(mockFeedingRecords);\r\n        setFeedInventory(mockFeedInventory);\r\n        setFeedingPlans(mockFeedingPlans);\r\n        setStats(mockFeedingStats);\r\n      } else {\r\n        // Use API\r\n        const [recordsRes, inventoryRes, plansRes] = await Promise.all([\r\n          api.get('/feeding/records'),\r\n          api.get('/feeding/inventory'),\r\n          api.get('/feeding/plans')\r\n        ]);\r\n\r\n        setFeedingRecords(recordsRes.data);\r\n        setFeedInventory(inventoryRes.data);\r\n        setFeedingPlans(plansRes.data);\r\n      }\r\n\r\n      updateStats();\r\n    } catch (err) {\r\n      console.error('Failed to fetch feeding data:', err);\r\n      setError('Failed to fetch feeding data');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // CRUD operations for feeding records\r\n  const addFeedingRecord = async (record: Omit<FeedingRecord, 'id'>) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      if (isConnected && !useMockData) {\r\n        // Use MongoDB\r\n        try {\r\n          // Get collections\r\n          const feedingRecordsCollection = await getCollection('feeding_records');\r\n          const feedInventoryCollection = await getCollection('feed_inventory');\r\n\r\n          // Create new record with generated ID\r\n          const newRecord: FeedingRecord = {\r\n            ...record,\r\n            id: `record-${Date.now()}`,\r\n          };\r\n\r\n          // Insert record into MongoDB\r\n          await feedingRecordsCollection.insertOne(newRecord);\r\n\r\n          // Update inventory quantity\r\n          const inventoryItem = await feedInventoryCollection.findOne({ id: record.feedId });\r\n          if (inventoryItem) {\r\n            const newQuantity = inventoryItem.quantity - record.quantity;\r\n            let newStatus: FeedInventory['status'] = inventoryItem.status;\r\n\r\n            if (newQuantity <= 0) {\r\n              newStatus = 'reorder';\r\n            } else if (newQuantity < inventoryItem.minimumStock) {\r\n              newStatus = 'low';\r\n            } else {\r\n              newStatus = 'available';\r\n            }\r\n\r\n            // Update inventory in MongoDB\r\n            await feedInventoryCollection.updateOne(\r\n              { id: record.feedId },\r\n              { $set: { quantity: newQuantity, status: newStatus } }\r\n            );\r\n          }\r\n\r\n          // Refresh data\r\n          await fetchFeedingData();\r\n          return newRecord;\r\n        } catch (mongoError) {\r\n          console.error('Error adding feeding record to MongoDB:', mongoError);\r\n          throw mongoError;\r\n        }\r\n      } else if (useMockData) {\r\n        // Simulate API delay\r\n        await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n        // Create new record with generated ID\r\n        const newRecord: FeedingRecord = {\r\n          ...record,\r\n          id: `record-${Date.now()}`,\r\n        };\r\n\r\n        // Update state\r\n        setFeedingRecords(prev => [...prev, newRecord]);\r\n\r\n        // Update inventory quantity\r\n        const updatedInventory = feedInventory.map(item => {\r\n          if (item.id === record.feedId) {\r\n            const newQuantity = item.quantity - record.quantity;\r\n            let newStatus: FeedInventory['status'] = item.status;\r\n\r\n            if (newQuantity <= 0) {\r\n              newStatus = 'reorder';\r\n            } else if (newQuantity < item.minimumStock) {\r\n              newStatus = 'low';\r\n            } else {\r\n              newStatus = 'available';\r\n            }\r\n\r\n            return {\r\n              ...item,\r\n              quantity: newQuantity,\r\n              status: newStatus\r\n            };\r\n          }\r\n          return item;\r\n        });\r\n\r\n        setFeedInventory(updatedInventory);\r\n        updateStats();\r\n\r\n        return newRecord;\r\n      } else {\r\n        // Use real API\r\n        const response = await api.post('/feeding/records', record);\r\n        await fetchFeedingData(); // Refresh all data\r\n        return response.data;\r\n      }\r\n    } catch (err) {\r\n      console.error('Error adding feeding record:', err);\r\n      setError('Failed to add feeding record');\r\n      throw err;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const updateFeedingRecord = async (id: string, record: Partial<FeedingRecord>) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      if (isConnected && !useMockData) {\r\n        // Use MongoDB\r\n        try {\r\n          // Get collections\r\n          const feedingRecordsCollection = await getCollection('feeding_records');\r\n          const feedInventoryCollection = await getCollection('feed_inventory');\r\n\r\n          // Find the original record\r\n          const originalRecord = await feedingRecordsCollection.findOne({ id });\r\n          if (!originalRecord) throw new Error('Record not found');\r\n\r\n          // Update record in MongoDB\r\n          await feedingRecordsCollection.updateOne(\r\n            { id },\r\n            { $set: record }\r\n          );\r\n\r\n          // If feed quantity changed, update inventory\r\n          if (record.quantity !== undefined && record.quantity !== originalRecord.quantity) {\r\n            const quantityDiff = originalRecord.quantity - record.quantity;\r\n\r\n            // Get inventory item\r\n            const inventoryItem = await feedInventoryCollection.findOne({ id: originalRecord.feedId });\r\n            if (inventoryItem) {\r\n              const newQuantity = inventoryItem.quantity + quantityDiff;\r\n              let newStatus: FeedInventory['status'] = 'available';\r\n\r\n              if (newQuantity <= 0) {\r\n                newStatus = 'reorder';\r\n              } else if (newQuantity < inventoryItem.minimumStock) {\r\n                newStatus = 'low';\r\n              } else {\r\n                newStatus = 'available';\r\n              }\r\n\r\n              // Update inventory in MongoDB\r\n              await feedInventoryCollection.updateOne(\r\n                { id: originalRecord.feedId },\r\n                { $set: { quantity: newQuantity, status: newStatus } }\r\n              );\r\n            }\r\n          }\r\n\r\n          // Refresh data\r\n          await fetchFeedingData();\r\n\r\n          // Return updated record\r\n          const updatedRecord = await feedingRecordsCollection.findOne({ id });\r\n          return updatedRecord;\r\n        } catch (mongoError) {\r\n          console.error('Error updating feeding record in MongoDB:', mongoError);\r\n          throw mongoError;\r\n        }\r\n      } else if (useMockData) {\r\n        // Simulate API delay\r\n        await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n        // Find the original record\r\n        const originalRecord = feedingRecords.find(r => r.id === id);\r\n        if (!originalRecord) throw new Error('Record not found');\r\n\r\n        // Update record\r\n        const updatedRecord: FeedingRecord = { ...originalRecord, ...record };\r\n\r\n        // Update state\r\n        setFeedingRecords(prev => prev.map(r => r.id === id ? updatedRecord : r));\r\n\r\n        // If feed quantity changed, update inventory\r\n        if (record.quantity !== undefined && record.quantity !== originalRecord.quantity) {\r\n          const quantityDiff = originalRecord.quantity - record.quantity;\r\n\r\n          const updatedInventory = feedInventory.map(item => {\r\n            if (item.id === originalRecord.feedId) {\r\n              const newQuantity = item.quantity + quantityDiff;\r\n              let newStatus: FeedInventory['status'] = 'available';\r\n\r\n              if (newQuantity <= 0) {\r\n                newStatus = 'reorder';\r\n              } else if (newQuantity < item.minimumStock) {\r\n                newStatus = 'low';\r\n              } else {\r\n                newStatus = 'available';\r\n              }\r\n\r\n              return {\r\n                ...item,\r\n                quantity: newQuantity,\r\n                status: newStatus\r\n              };\r\n            }\r\n            return item;\r\n          });\r\n\r\n          setFeedInventory(updatedInventory);\r\n        }\r\n\r\n        updateStats();\r\n        return updatedRecord;\r\n      } else {\r\n        // Use real API\r\n        const response = await api.put(`/feeding/records/${id}`, record);\r\n        await fetchFeedingData(); // Refresh all data\r\n        return response.data;\r\n      }\r\n    } catch (err) {\r\n      console.error('Error updating feeding record:', err);\r\n      setError('Failed to update feeding record');\r\n      throw err;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const deleteFeedingRecord = async (id: string) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      if (isConnected && !useMockData) {\r\n        // Use MongoDB\r\n        try {\r\n          // Get collections\r\n          const feedingRecordsCollection = await getCollection('feeding_records');\r\n          const feedInventoryCollection = await getCollection('feed_inventory');\r\n\r\n          // Find the record to delete\r\n          const recordToDelete = await feedingRecordsCollection.findOne({ id });\r\n          if (!recordToDelete) throw new Error('Record not found');\r\n\r\n          // Delete record from MongoDB\r\n          await feedingRecordsCollection.deleteOne({ id });\r\n\r\n          // Return feed to inventory\r\n          const inventoryItem = await feedInventoryCollection.findOne({ id: recordToDelete.feedId });\r\n          if (inventoryItem) {\r\n            const newQuantity = inventoryItem.quantity + recordToDelete.quantity;\r\n            let newStatus: FeedInventory['status'] = 'available';\r\n\r\n            if (newQuantity <= 0) {\r\n              newStatus = 'reorder';\r\n            } else if (newQuantity < inventoryItem.minimumStock) {\r\n              newStatus = 'low';\r\n            } else {\r\n              newStatus = 'available';\r\n            }\r\n\r\n            // Update inventory in MongoDB\r\n            await feedInventoryCollection.updateOne(\r\n              { id: recordToDelete.feedId },\r\n              { $set: { quantity: newQuantity, status: newStatus } }\r\n            );\r\n          }\r\n\r\n          // Refresh data\r\n          await fetchFeedingData();\r\n          return true;\r\n        } catch (mongoError) {\r\n          console.error('Error deleting feeding record from MongoDB:', mongoError);\r\n          throw mongoError;\r\n        }\r\n      } else if (useMockData) {\r\n        // Simulate API delay\r\n        await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n        // Find the record to delete\r\n        const recordToDelete = feedingRecords.find(r => r.id === id);\r\n        if (!recordToDelete) throw new Error('Record not found');\r\n\r\n        // Update state\r\n        setFeedingRecords(prev => prev.filter(r => r.id !== id));\r\n\r\n        // Return feed to inventory\r\n        const updatedInventory = feedInventory.map(item => {\r\n          if (item.id === recordToDelete.feedId) {\r\n            const newQuantity = item.quantity + recordToDelete.quantity;\r\n            let newStatus: FeedInventory['status'] = 'available';\r\n\r\n            if (newQuantity <= 0) {\r\n              newStatus = 'reorder';\r\n            } else if (newQuantity < item.minimumStock) {\r\n              newStatus = 'low';\r\n            } else {\r\n              newStatus = 'available';\r\n            }\r\n\r\n            return {\r\n              ...item,\r\n              quantity: newQuantity,\r\n              status: newStatus\r\n            };\r\n          }\r\n          return item;\r\n        });\r\n\r\n        setFeedInventory(updatedInventory);\r\n        updateStats();\r\n\r\n        return true;\r\n      } else {\r\n        // Use real API\r\n        await api.delete(`/feeding/records/${id}`);\r\n        await fetchFeedingData(); // Refresh all data\r\n        return true;\r\n      }\r\n    } catch (err) {\r\n      console.error('Error deleting feeding record:', err);\r\n      setError('Failed to delete feeding record');\r\n      throw err;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // CRUD operations for feed inventory\r\n  const addFeedInventory = async (item: Omit<FeedInventory, 'id'>) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      if (isConnected && !useMockData) {\r\n        // Use MongoDB\r\n        try {\r\n          // Get collection\r\n          const feedInventoryCollection = await getCollection('feed_inventory');\r\n\r\n          // Create new inventory item with generated ID\r\n          const newItem: FeedInventory = {\r\n            ...item,\r\n            id: `feed-${Date.now()}`,\r\n          };\r\n\r\n          // Insert item into MongoDB\r\n          await feedInventoryCollection.insertOne(newItem);\r\n\r\n          // Refresh data\r\n          await fetchFeedingData();\r\n          return newItem;\r\n        } catch (mongoError) {\r\n          console.error('Error adding feed inventory to MongoDB:', mongoError);\r\n          throw mongoError;\r\n        }\r\n      } else if (useMockData) {\r\n        // Simulate API delay\r\n        await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n        // Create new inventory item with generated ID\r\n        const newItem: FeedInventory = {\r\n          ...item,\r\n          id: `feed-${Date.now()}`,\r\n        };\r\n\r\n        // Update state\r\n        setFeedInventory(prev => [...prev, newItem]);\r\n        updateStats();\r\n\r\n        return newItem;\r\n      } else {\r\n        // Use real API\r\n        const response = await api.post('/feeding/inventory', item);\r\n        await fetchFeedingData(); // Refresh all data\r\n        return response.data;\r\n      }\r\n    } catch (err) {\r\n      console.error('Error adding feed inventory:', err);\r\n      setError('Failed to add feed inventory');\r\n      throw err;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const updateFeedInventory = async (id: string, item: Partial<FeedInventory>) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      if (isConnected && !useMockData) {\r\n        // Use MongoDB\r\n        try {\r\n          // Get collection\r\n          const feedInventoryCollection = await getCollection('feed_inventory');\r\n\r\n          // Find the original item\r\n          const originalItem = await feedInventoryCollection.findOne({ id });\r\n          if (!originalItem) throw new Error('Inventory item not found');\r\n\r\n          // Update item in MongoDB\r\n          await feedInventoryCollection.updateOne(\r\n            { id },\r\n            { $set: item }\r\n          );\r\n\r\n          // Refresh data\r\n          await fetchFeedingData();\r\n\r\n          // Return updated item\r\n          const updatedItem = await feedInventoryCollection.findOne({ id });\r\n          return updatedItem;\r\n        } catch (mongoError) {\r\n          console.error('Error updating feed inventory in MongoDB:', mongoError);\r\n          throw mongoError;\r\n        }\r\n      } else if (useMockData) {\r\n        // Simulate API delay\r\n        await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n        // Find the original item\r\n        const originalItem = feedInventory.find(i => i.id === id);\r\n        if (!originalItem) throw new Error('Inventory item not found');\r\n\r\n        // Update item\r\n        const updatedItem: FeedInventory = { ...originalItem, ...item };\r\n\r\n        // Update state\r\n        setFeedInventory(prev => prev.map(i => i.id === id ? updatedItem : i));\r\n        updateStats();\r\n\r\n        return updatedItem;\r\n      } else {\r\n        // Use real API\r\n        const response = await api.put(`/feeding/inventory/${id}`, item);\r\n        await fetchFeedingData(); // Refresh all data\r\n        return response.data;\r\n      }\r\n    } catch (err) {\r\n      console.error('Error updating feed inventory:', err);\r\n      setError('Failed to update feed inventory');\r\n      throw err;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const deleteFeedInventory = async (id: string) => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      if (isConnected && !useMockData) {\r\n        // Use MongoDB\r\n        try {\r\n          // Get collections\r\n          const feedInventoryCollection = await getCollection('feed_inventory');\r\n          const feedingRecordsCollection = await getCollection('feeding_records');\r\n\r\n          // Check if this feed is used in any feeding records\r\n          const recordsWithFeed = await feedingRecordsCollection.find({ feedId: id }).toArray();\r\n          if (recordsWithFeed.length > 0) {\r\n            throw new Error('Cannot delete feed that is used in feeding records');\r\n          }\r\n\r\n          // Delete item from MongoDB\r\n          await feedInventoryCollection.deleteOne({ id });\r\n\r\n          // Refresh data\r\n          await fetchFeedingData();\r\n          return true;\r\n        } catch (mongoError) {\r\n          console.error('Error deleting feed inventory from MongoDB:', mongoError);\r\n          throw mongoError;\r\n        }\r\n      } else if (useMockData) {\r\n        // Simulate API delay\r\n        await new Promise(resolve => setTimeout(resolve, 500));\r\n\r\n        // Check if there are any feeding records using this feed\r\n        const recordsUsingFeed = feedingRecords.some(record => record.feedId === id);\r\n        if (recordsUsingFeed) {\r\n          throw new Error('Cannot delete feed that is used in feeding records');\r\n        }\r\n\r\n        // Update state\r\n        setFeedInventory(prev => prev.filter(i => i.id !== id));\r\n        updateStats();\r\n\r\n        return true;\r\n      } else {\r\n        // Use real API\r\n        await api.delete(`/feeding/inventory/${id}`);\r\n        await fetchFeedingData(); // Refresh all data\r\n        return true;\r\n      }\r\n    } catch (err) {\r\n      console.error('Error deleting feed inventory:', err);\r\n      setError('Failed to delete feed inventory');\r\n      throw err;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Generate Excel report\r\n  const generateExcelReport = async (reportType: 'records' | 'inventory' | 'plans') => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      if (isConnected && !useMockData) {\r\n        try {\r\n          // Get data from MongoDB\r\n          let data: any[] = [];\r\n\r\n          if (reportType === 'records') {\r\n            const feedingRecordsCollection = await getCollection('feeding_records');\r\n            data = await feedingRecordsCollection.find({}).toArray();\r\n          } else if (reportType === 'inventory') {\r\n            const feedInventoryCollection = await getCollection('feed_inventory');\r\n            data = await feedInventoryCollection.find({}).toArray();\r\n          } else if (reportType === 'plans') {\r\n            const feedingPlansCollection = await getCollection('feeding_plans');\r\n            data = await feedingPlansCollection.find({}).toArray();\r\n          }\r\n\r\n          // In a real implementation, this would generate an Excel file\r\n          // For now, we'll just return a filename\r\n          console.log(`Generated ${reportType} report with ${data.length} records`);\r\n          return `${reportType}_report_${new Date().toISOString().split('T')[0]}.xlsx`;\r\n        } catch (mongoError) {\r\n          console.error(`Error generating ${reportType} report from MongoDB:`, mongoError);\r\n          throw mongoError;\r\n        }\r\n      } else if (useMockData) {\r\n        // Simulate report generation\r\n        await new Promise(resolve => setTimeout(resolve, 1500));\r\n        return `${reportType}_report_${new Date().toISOString().split('T')[0]}.xlsx`;\r\n      } else {\r\n        // Use real API\r\n        const response = await api.get(`/feeding/reports/${reportType}`);\r\n        return response.data.reportUrl;\r\n      }\r\n    } catch (err) {\r\n      console.error(`Error generating ${reportType} report:`, err);\r\n      setError(`Failed to generate ${reportType} report`);\r\n      throw err;\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  // Initial data fetch\r\n  useEffect(() => {\r\n    fetchFeedingData();\r\n  }, []);\r\n\r\n  return {\r\n    feedingRecords,\r\n    feedInventory,\r\n    feedingPlans,\r\n    stats,\r\n    loading,\r\n    error,\r\n    fetchFeedingData,\r\n    addFeedingRecord,\r\n    updateFeedingRecord,\r\n    deleteFeedingRecord,\r\n    addFeedInventory,\r\n    updateFeedInventory,\r\n    deleteFeedInventory,\r\n    generateExcelReport\r\n  };\r\n};\r\n"], "mappings": "gJAAA,OAASA,QAAQ,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CACxD,OAASC,WAAW,KAAQ,iBAAiB,CAC7C,MAAO,CAAAC,GAAG,KAAM,wBAAwB,CACxC,OACEC,kBAAkB,CAClBC,iBAAiB,CACjBC,gBAAgB,CAChBC,gBAAgB,KACX,sBAAsB,CAE7B;AACA,OAASC,UAAU,KAAQ,cAAc,CAEzC,MAAO,MAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAClC,KAAM,CAAEC,WAAY,CAAC,CAAGF,UAAU,CAAC,CAAC,CACpC,KAAM,CAACG,cAAc,CAAEC,iBAAiB,CAAC,CAAGb,QAAQ,CAAkB,EAAE,CAAC,CACzE,KAAM,CAACc,aAAa,CAAEC,gBAAgB,CAAC,CAAGf,QAAQ,CAAkB,EAAE,CAAC,CACvE,KAAM,CAACgB,YAAY,CAAEC,eAAe,CAAC,CAAGjB,QAAQ,CAAgB,EAAE,CAAC,CACnE,KAAM,CAACkB,KAAK,CAAEC,QAAQ,CAAC,CAAGnB,QAAQ,CAAe,CAC/CoB,kBAAkB,CAAE,CAAC,CACrBC,kBAAkB,CAAE,CAAC,CACrBC,kBAAkB,CAAE,CAAC,CACrBC,aAAa,CAAE,CAAC,CAChBC,kBAAkB,CAAE,CAAC,CACrBC,sBAAsB,CAAE,CAAC,CACzBC,eAAe,CAAE,CACfC,WAAW,CAAE,CAAC,CACdC,MAAM,CAAE,CAAC,CACTC,OAAO,CAAE,CACX,CACF,CAAC,CAAC,CACF,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAG/B,QAAQ,CAAC,IAAI,CAAC,CAC5C,KAAM,CAACgC,KAAK,CAAEC,QAAQ,CAAC,CAAGjC,QAAQ,CAAgB,IAAI,CAAC,CAEvD;AACA,KAAM,CAAAkC,wBAAwB,CAAGhC,WAAW,CAAEiC,IAAY,EAAa,CACrE,MAAO,CAAAvB,cAAc,CAClBwB,MAAM,CAACC,MAAM,OAAAC,mBAAA,OAAI,EAAAA,mBAAA,CAAAxB,aAAa,CAACyB,IAAI,CAACC,IAAI,EAAIA,IAAI,CAACC,EAAE,GAAKJ,MAAM,CAACK,MAAM,CAAC,UAAAJ,mBAAA,iBAArDA,mBAAA,CAAuDH,IAAI,IAAKA,IAAI,GAAC,CACtFQ,MAAM,CAAC,CAACC,GAAG,CAAEP,MAAM,GAAKO,GAAG,CAAGP,MAAM,CAACQ,QAAQ,CAAE,CAAC,CAAC,CACtD,CAAC,CAAE,CAACjC,cAAc,CAAEE,aAAa,CAAC,CAAC,CAEnC;AACA,KAAM,CAAAgC,WAAW,CAAG5C,WAAW,CAAC,IAAM,CACpC,KAAM,CAAA6C,KAAK,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CACxB,KAAM,CAAAC,YAAY,CAAGrC,cAAc,CAACwB,MAAM,CAACC,MAAM,EAAI,CACnD,KAAM,CAAAa,UAAU,CAAG,GAAI,CAAAF,IAAI,CAACX,MAAM,CAACc,IAAI,CAAC,CACxC,MAAO,CAAAD,UAAU,CAACE,YAAY,CAAC,CAAC,GAAKL,KAAK,CAACK,YAAY,CAAC,CAAC,CAC3D,CAAC,CAAC,CAEF,KAAM,CAAAC,QAAsB,CAAG,CAC7BjC,kBAAkB,CAAE6B,YAAY,CAACN,MAAM,CAAC,CAACC,GAAG,CAAEP,MAAM,GAAKO,GAAG,CAAGP,MAAM,CAACQ,QAAQ,CAAE,CAAC,CAAC,CAClFxB,kBAAkB,CAAE4B,YAAY,CAACN,MAAM,CAAC,CAACC,GAAG,CAAEP,MAAM,GAAKO,GAAG,CAAIP,MAAM,CAACQ,QAAQ,CAAGR,MAAM,CAACiB,WAAY,CAAE,CAAC,CAAC,CACzGhC,kBAAkB,CAAER,aAAa,CAAC6B,MAAM,CAAC,CAACC,GAAG,CAAEJ,IAAI,GAAKI,GAAG,CAAIJ,IAAI,CAACK,QAAQ,CAAGL,IAAI,CAACc,WAAY,CAAE,CAAC,CAAC,CACpG/B,aAAa,CAAET,aAAa,CAACsB,MAAM,CAACI,IAAI,EAAIA,IAAI,CAACK,QAAQ,CAAGL,IAAI,CAACe,YAAY,CAAC,CAACC,MAAM,CACrFhC,kBAAkB,CAAER,YAAY,CAACoB,MAAM,CAACqB,IAAI,EAAIA,IAAI,CAACC,QAAQ,CAAC,CAACF,MAAM,CACrE/B,sBAAsB,CAAEb,cAAc,CAACwB,MAAM,CAACC,MAAM,EAAI,CACtD,KAAM,CAAAa,UAAU,CAAG,GAAI,CAAAF,IAAI,CAACX,MAAM,CAACc,IAAI,CAAC,CACxC,KAAM,CAAAQ,QAAQ,CAAGC,IAAI,CAACC,GAAG,CAACd,KAAK,CAACe,OAAO,CAAC,CAAC,CAAGZ,UAAU,CAACY,OAAO,CAAC,CAAC,CAAC,CACjE,KAAM,CAAAC,QAAQ,CAAGH,IAAI,CAACI,IAAI,CAACL,QAAQ,EAAI,IAAI,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAC,CAAC,CAC5D,MAAO,CAAAI,QAAQ,EAAI,CAAC,CACtB,CAAC,CAAC,CAACP,MAAM,CACT9B,eAAe,CAAE,CACfC,WAAW,CAAEO,wBAAwB,CAAC,aAAa,CAAC,CACpDN,MAAM,CAAEM,wBAAwB,CAAC,QAAQ,CAAC,CAC1CL,OAAO,CAAEK,wBAAwB,CAAC,SAAS,CAC7C,CACF,CAAC,CAEDf,QAAQ,CAACkC,QAAQ,CAAC,CACpB,CAAC,CAAE,CAACzC,cAAc,CAAEE,aAAa,CAAEE,YAAY,CAAEkB,wBAAwB,CAAC,CAAC,CAE3E;AACA,KAAM,CAAA+B,gBAAgB,CAAG,KAAAA,CAAA,GAAY,CACnC,GAAI,CACFlC,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAItB,WAAW,EAAI,CAACR,WAAW,CAAE,CAC/B;AACA,GAAI,CACF;AACA,KAAM,CAAA+D,wBAAwB,CAAG,KAAM,CAAAC,aAAa,CAAC,iBAAiB,CAAC,CACvE,KAAM,CAAAC,uBAAuB,CAAG,KAAM,CAAAD,aAAa,CAAC,gBAAgB,CAAC,CACrE,KAAM,CAAAE,sBAAsB,CAAG,KAAM,CAAAF,aAAa,CAAC,eAAe,CAAC,CAEnE;AACA,KAAM,CAAAG,WAAW,CAAG,KAAM,CAAAJ,wBAAwB,CAAC3B,IAAI,CAAC,CAAC,CAAC,CAAC,CAACgC,OAAO,CAAC,CAAC,CACrE,KAAM,CAAAC,aAAa,CAAG,KAAM,CAAAJ,uBAAuB,CAAC7B,IAAI,CAAC,CAAC,CAAC,CAAC,CAACgC,OAAO,CAAC,CAAC,CACtE,KAAM,CAAAE,SAAS,CAAG,KAAM,CAAAJ,sBAAsB,CAAC9B,IAAI,CAAC,CAAC,CAAC,CAAC,CAACgC,OAAO,CAAC,CAAC,CAEjE;AACA1D,iBAAiB,CAACyD,WAAW,CAAC,CAC9BvD,gBAAgB,CAACyD,aAAa,CAAC,CAC/BvD,eAAe,CAACwD,SAAS,CAAC,CAE1BC,OAAO,CAACC,GAAG,CAAC,oCAAoC,CAAE,CAChDC,OAAO,CAAEN,WAAW,CAACd,MAAM,CAC3BqB,SAAS,CAAEL,aAAa,CAAChB,MAAM,CAC/BsB,KAAK,CAAEL,SAAS,CAACjB,MACnB,CAAC,CAAC,CACJ,CAAE,MAAOuB,UAAU,CAAE,CACnBL,OAAO,CAAC1C,KAAK,CAAC,yDAAyD,CAAE+C,UAAU,CAAC,CACpF;AACAlE,iBAAiB,CAACR,kBAAkB,CAAC,CACrCU,gBAAgB,CAACT,iBAAiB,CAAC,CACnCW,eAAe,CAACV,gBAAgB,CAAC,CACjCY,QAAQ,CAACX,gBAAgB,CAAC,CAC5B,CACF,CAAC,IAAM,IAAIL,WAAW,CAAE,CACtB;AACA,KAAM,IAAI,CAAA6E,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CACvDpE,iBAAiB,CAACR,kBAAkB,CAAC,CACrCU,gBAAgB,CAACT,iBAAiB,CAAC,CACnCW,eAAe,CAACV,gBAAgB,CAAC,CACjCY,QAAQ,CAACX,gBAAgB,CAAC,CAC5B,CAAC,IAAM,CACL;AACA,KAAM,CAAC2E,UAAU,CAAEC,YAAY,CAAEC,QAAQ,CAAC,CAAG,KAAM,CAAAL,OAAO,CAACM,GAAG,CAAC,CAC7DlF,GAAG,CAACmF,GAAG,CAAC,kBAAkB,CAAC,CAC3BnF,GAAG,CAACmF,GAAG,CAAC,oBAAoB,CAAC,CAC7BnF,GAAG,CAACmF,GAAG,CAAC,gBAAgB,CAAC,CAC1B,CAAC,CAEF1E,iBAAiB,CAACsE,UAAU,CAACK,IAAI,CAAC,CAClCzE,gBAAgB,CAACqE,YAAY,CAACI,IAAI,CAAC,CACnCvE,eAAe,CAACoE,QAAQ,CAACG,IAAI,CAAC,CAChC,CAEA1C,WAAW,CAAC,CAAC,CACf,CAAE,MAAO2C,GAAG,CAAE,CACZf,OAAO,CAAC1C,KAAK,CAAC,+BAA+B,CAAEyD,GAAG,CAAC,CACnDxD,QAAQ,CAAC,8BAA8B,CAAC,CAC1C,CAAC,OAAS,CACRF,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA2D,gBAAgB,CAAG,KAAO,CAAArD,MAAiC,EAAK,CACpE,GAAI,CACFN,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAItB,WAAW,EAAI,CAACR,WAAW,CAAE,CAC/B;AACA,GAAI,CACF;AACA,KAAM,CAAA+D,wBAAwB,CAAG,KAAM,CAAAC,aAAa,CAAC,iBAAiB,CAAC,CACvE,KAAM,CAAAC,uBAAuB,CAAG,KAAM,CAAAD,aAAa,CAAC,gBAAgB,CAAC,CAErE;AACA,KAAM,CAAAwB,SAAwB,CAAAC,aAAA,CAAAA,aAAA,IACzBvD,MAAM,MACTI,EAAE,WAAAoD,MAAA,CAAY7C,IAAI,CAAC8C,GAAG,CAAC,CAAC,CAAE,EAC3B,CAED;AACA,KAAM,CAAA5B,wBAAwB,CAAC6B,SAAS,CAACJ,SAAS,CAAC,CAEnD;AACA,KAAM,CAAAK,aAAa,CAAG,KAAM,CAAA5B,uBAAuB,CAAC6B,OAAO,CAAC,CAAExD,EAAE,CAAEJ,MAAM,CAACK,MAAO,CAAC,CAAC,CAClF,GAAIsD,aAAa,CAAE,CACjB,KAAM,CAAAE,WAAW,CAAGF,aAAa,CAACnD,QAAQ,CAAGR,MAAM,CAACQ,QAAQ,CAC5D,GAAI,CAAAsD,SAAkC,CAAGH,aAAa,CAACI,MAAM,CAE7D,GAAIF,WAAW,EAAI,CAAC,CAAE,CACpBC,SAAS,CAAG,SAAS,CACvB,CAAC,IAAM,IAAID,WAAW,CAAGF,aAAa,CAACK,YAAY,CAAE,CACnDF,SAAS,CAAG,KAAK,CACnB,CAAC,IAAM,CACLA,SAAS,CAAG,WAAW,CACzB,CAEA;AACA,KAAM,CAAA/B,uBAAuB,CAACkC,SAAS,CACrC,CAAE7D,EAAE,CAAEJ,MAAM,CAACK,MAAO,CAAC,CACrB,CAAE6D,IAAI,CAAE,CAAE1D,QAAQ,CAAEqD,WAAW,CAAEE,MAAM,CAAED,SAAU,CAAE,CACvD,CAAC,CACH,CAEA;AACA,KAAM,CAAAlC,gBAAgB,CAAC,CAAC,CACxB,MAAO,CAAA0B,SAAS,CAClB,CAAE,MAAOZ,UAAU,CAAE,CACnBL,OAAO,CAAC1C,KAAK,CAAC,yCAAyC,CAAE+C,UAAU,CAAC,CACpE,KAAM,CAAAA,UAAU,CAClB,CACF,CAAC,IAAM,IAAI5E,WAAW,CAAE,CACtB;AACA,KAAM,IAAI,CAAA6E,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,GAAG,CAAC,CAAC,CAEtD;AACA,KAAM,CAAAU,SAAwB,CAAAC,aAAA,CAAAA,aAAA,IACzBvD,MAAM,MACTI,EAAE,WAAAoD,MAAA,CAAY7C,IAAI,CAAC8C,GAAG,CAAC,CAAC,CAAE,EAC3B,CAED;AACAjF,iBAAiB,CAAC2F,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEb,SAAS,CAAC,CAAC,CAE/C;AACA,KAAM,CAAAc,gBAAgB,CAAG3F,aAAa,CAAC4F,GAAG,CAAClE,IAAI,EAAI,CACjD,GAAIA,IAAI,CAACC,EAAE,GAAKJ,MAAM,CAACK,MAAM,CAAE,CAC7B,KAAM,CAAAwD,WAAW,CAAG1D,IAAI,CAACK,QAAQ,CAAGR,MAAM,CAACQ,QAAQ,CACnD,GAAI,CAAAsD,SAAkC,CAAG3D,IAAI,CAAC4D,MAAM,CAEpD,GAAIF,WAAW,EAAI,CAAC,CAAE,CACpBC,SAAS,CAAG,SAAS,CACvB,CAAC,IAAM,IAAID,WAAW,CAAG1D,IAAI,CAAC6D,YAAY,CAAE,CAC1CF,SAAS,CAAG,KAAK,CACnB,CAAC,IAAM,CACLA,SAAS,CAAG,WAAW,CACzB,CAEA,OAAAP,aAAA,CAAAA,aAAA,IACKpD,IAAI,MACPK,QAAQ,CAAEqD,WAAW,CACrBE,MAAM,CAAED,SAAS,GAErB,CACA,MAAO,CAAA3D,IAAI,CACb,CAAC,CAAC,CAEFzB,gBAAgB,CAAC0F,gBAAgB,CAAC,CAClC3D,WAAW,CAAC,CAAC,CAEb,MAAO,CAAA6C,SAAS,CAClB,CAAC,IAAM,CACL;AACA,KAAM,CAAAgB,QAAQ,CAAG,KAAM,CAAAvG,GAAG,CAACwG,IAAI,CAAC,kBAAkB,CAAEvE,MAAM,CAAC,CAC3D,KAAM,CAAA4B,gBAAgB,CAAC,CAAC,CAAE;AAC1B,MAAO,CAAA0C,QAAQ,CAACnB,IAAI,CACtB,CACF,CAAE,MAAOC,GAAG,CAAE,CACZf,OAAO,CAAC1C,KAAK,CAAC,8BAA8B,CAAEyD,GAAG,CAAC,CAClDxD,QAAQ,CAAC,8BAA8B,CAAC,CACxC,KAAM,CAAAwD,GAAG,CACX,CAAC,OAAS,CACR1D,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA8E,mBAAmB,CAAG,KAAAA,CAAOpE,EAAU,CAAEJ,MAA8B,GAAK,CAChF,GAAI,CACFN,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAItB,WAAW,EAAI,CAACR,WAAW,CAAE,CAC/B;AACA,GAAI,CACF;AACA,KAAM,CAAA+D,wBAAwB,CAAG,KAAM,CAAAC,aAAa,CAAC,iBAAiB,CAAC,CACvE,KAAM,CAAAC,uBAAuB,CAAG,KAAM,CAAAD,aAAa,CAAC,gBAAgB,CAAC,CAErE;AACA,KAAM,CAAA2C,cAAc,CAAG,KAAM,CAAA5C,wBAAwB,CAAC+B,OAAO,CAAC,CAAExD,EAAG,CAAC,CAAC,CACrE,GAAI,CAACqE,cAAc,CAAE,KAAM,IAAI,CAAAC,KAAK,CAAC,kBAAkB,CAAC,CAExD;AACA,KAAM,CAAA7C,wBAAwB,CAACoC,SAAS,CACtC,CAAE7D,EAAG,CAAC,CACN,CAAE8D,IAAI,CAAElE,MAAO,CACjB,CAAC,CAED;AACA,GAAIA,MAAM,CAACQ,QAAQ,GAAKmE,SAAS,EAAI3E,MAAM,CAACQ,QAAQ,GAAKiE,cAAc,CAACjE,QAAQ,CAAE,CAChF,KAAM,CAAAoE,YAAY,CAAGH,cAAc,CAACjE,QAAQ,CAAGR,MAAM,CAACQ,QAAQ,CAE9D;AACA,KAAM,CAAAmD,aAAa,CAAG,KAAM,CAAA5B,uBAAuB,CAAC6B,OAAO,CAAC,CAAExD,EAAE,CAAEqE,cAAc,CAACpE,MAAO,CAAC,CAAC,CAC1F,GAAIsD,aAAa,CAAE,CACjB,KAAM,CAAAE,WAAW,CAAGF,aAAa,CAACnD,QAAQ,CAAGoE,YAAY,CACzD,GAAI,CAAAd,SAAkC,CAAG,WAAW,CAEpD,GAAID,WAAW,EAAI,CAAC,CAAE,CACpBC,SAAS,CAAG,SAAS,CACvB,CAAC,IAAM,IAAID,WAAW,CAAGF,aAAa,CAACK,YAAY,CAAE,CACnDF,SAAS,CAAG,KAAK,CACnB,CAAC,IAAM,CACLA,SAAS,CAAG,WAAW,CACzB,CAEA;AACA,KAAM,CAAA/B,uBAAuB,CAACkC,SAAS,CACrC,CAAE7D,EAAE,CAAEqE,cAAc,CAACpE,MAAO,CAAC,CAC7B,CAAE6D,IAAI,CAAE,CAAE1D,QAAQ,CAAEqD,WAAW,CAAEE,MAAM,CAAED,SAAU,CAAE,CACvD,CAAC,CACH,CACF,CAEA;AACA,KAAM,CAAAlC,gBAAgB,CAAC,CAAC,CAExB;AACA,KAAM,CAAAiD,aAAa,CAAG,KAAM,CAAAhD,wBAAwB,CAAC+B,OAAO,CAAC,CAAExD,EAAG,CAAC,CAAC,CACpE,MAAO,CAAAyE,aAAa,CACtB,CAAE,MAAOnC,UAAU,CAAE,CACnBL,OAAO,CAAC1C,KAAK,CAAC,2CAA2C,CAAE+C,UAAU,CAAC,CACtE,KAAM,CAAAA,UAAU,CAClB,CACF,CAAC,IAAM,IAAI5E,WAAW,CAAE,CACtB;AACA,KAAM,IAAI,CAAA6E,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,GAAG,CAAC,CAAC,CAEtD;AACA,KAAM,CAAA6B,cAAc,CAAGlG,cAAc,CAAC2B,IAAI,CAAC4E,CAAC,EAAIA,CAAC,CAAC1E,EAAE,GAAKA,EAAE,CAAC,CAC5D,GAAI,CAACqE,cAAc,CAAE,KAAM,IAAI,CAAAC,KAAK,CAAC,kBAAkB,CAAC,CAExD;AACA,KAAM,CAAAG,aAA4B,CAAAtB,aAAA,CAAAA,aAAA,IAAQkB,cAAc,EAAKzE,MAAM,CAAE,CAErE;AACAxB,iBAAiB,CAAC2F,IAAI,EAAIA,IAAI,CAACE,GAAG,CAACS,CAAC,EAAIA,CAAC,CAAC1E,EAAE,GAAKA,EAAE,CAAGyE,aAAa,CAAGC,CAAC,CAAC,CAAC,CAEzE;AACA,GAAI9E,MAAM,CAACQ,QAAQ,GAAKmE,SAAS,EAAI3E,MAAM,CAACQ,QAAQ,GAAKiE,cAAc,CAACjE,QAAQ,CAAE,CAChF,KAAM,CAAAoE,YAAY,CAAGH,cAAc,CAACjE,QAAQ,CAAGR,MAAM,CAACQ,QAAQ,CAE9D,KAAM,CAAA4D,gBAAgB,CAAG3F,aAAa,CAAC4F,GAAG,CAAClE,IAAI,EAAI,CACjD,GAAIA,IAAI,CAACC,EAAE,GAAKqE,cAAc,CAACpE,MAAM,CAAE,CACrC,KAAM,CAAAwD,WAAW,CAAG1D,IAAI,CAACK,QAAQ,CAAGoE,YAAY,CAChD,GAAI,CAAAd,SAAkC,CAAG,WAAW,CAEpD,GAAID,WAAW,EAAI,CAAC,CAAE,CACpBC,SAAS,CAAG,SAAS,CACvB,CAAC,IAAM,IAAID,WAAW,CAAG1D,IAAI,CAAC6D,YAAY,CAAE,CAC1CF,SAAS,CAAG,KAAK,CACnB,CAAC,IAAM,CACLA,SAAS,CAAG,WAAW,CACzB,CAEA,OAAAP,aAAA,CAAAA,aAAA,IACKpD,IAAI,MACPK,QAAQ,CAAEqD,WAAW,CACrBE,MAAM,CAAED,SAAS,GAErB,CACA,MAAO,CAAA3D,IAAI,CACb,CAAC,CAAC,CAEFzB,gBAAgB,CAAC0F,gBAAgB,CAAC,CACpC,CAEA3D,WAAW,CAAC,CAAC,CACb,MAAO,CAAAoE,aAAa,CACtB,CAAC,IAAM,CACL;AACA,KAAM,CAAAP,QAAQ,CAAG,KAAM,CAAAvG,GAAG,CAACgH,GAAG,qBAAAvB,MAAA,CAAqBpD,EAAE,EAAIJ,MAAM,CAAC,CAChE,KAAM,CAAA4B,gBAAgB,CAAC,CAAC,CAAE;AAC1B,MAAO,CAAA0C,QAAQ,CAACnB,IAAI,CACtB,CACF,CAAE,MAAOC,GAAG,CAAE,CACZf,OAAO,CAAC1C,KAAK,CAAC,gCAAgC,CAAEyD,GAAG,CAAC,CACpDxD,QAAQ,CAAC,iCAAiC,CAAC,CAC3C,KAAM,CAAAwD,GAAG,CACX,CAAC,OAAS,CACR1D,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAsF,mBAAmB,CAAG,KAAO,CAAA5E,EAAU,EAAK,CAChD,GAAI,CACFV,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAItB,WAAW,EAAI,CAACR,WAAW,CAAE,CAC/B;AACA,GAAI,CACF;AACA,KAAM,CAAA+D,wBAAwB,CAAG,KAAM,CAAAC,aAAa,CAAC,iBAAiB,CAAC,CACvE,KAAM,CAAAC,uBAAuB,CAAG,KAAM,CAAAD,aAAa,CAAC,gBAAgB,CAAC,CAErE;AACA,KAAM,CAAAmD,cAAc,CAAG,KAAM,CAAApD,wBAAwB,CAAC+B,OAAO,CAAC,CAAExD,EAAG,CAAC,CAAC,CACrE,GAAI,CAAC6E,cAAc,CAAE,KAAM,IAAI,CAAAP,KAAK,CAAC,kBAAkB,CAAC,CAExD;AACA,KAAM,CAAA7C,wBAAwB,CAACqD,SAAS,CAAC,CAAE9E,EAAG,CAAC,CAAC,CAEhD;AACA,KAAM,CAAAuD,aAAa,CAAG,KAAM,CAAA5B,uBAAuB,CAAC6B,OAAO,CAAC,CAAExD,EAAE,CAAE6E,cAAc,CAAC5E,MAAO,CAAC,CAAC,CAC1F,GAAIsD,aAAa,CAAE,CACjB,KAAM,CAAAE,WAAW,CAAGF,aAAa,CAACnD,QAAQ,CAAGyE,cAAc,CAACzE,QAAQ,CACpE,GAAI,CAAAsD,SAAkC,CAAG,WAAW,CAEpD,GAAID,WAAW,EAAI,CAAC,CAAE,CACpBC,SAAS,CAAG,SAAS,CACvB,CAAC,IAAM,IAAID,WAAW,CAAGF,aAAa,CAACK,YAAY,CAAE,CACnDF,SAAS,CAAG,KAAK,CACnB,CAAC,IAAM,CACLA,SAAS,CAAG,WAAW,CACzB,CAEA;AACA,KAAM,CAAA/B,uBAAuB,CAACkC,SAAS,CACrC,CAAE7D,EAAE,CAAE6E,cAAc,CAAC5E,MAAO,CAAC,CAC7B,CAAE6D,IAAI,CAAE,CAAE1D,QAAQ,CAAEqD,WAAW,CAAEE,MAAM,CAAED,SAAU,CAAE,CACvD,CAAC,CACH,CAEA;AACA,KAAM,CAAAlC,gBAAgB,CAAC,CAAC,CACxB,MAAO,KAAI,CACb,CAAE,MAAOc,UAAU,CAAE,CACnBL,OAAO,CAAC1C,KAAK,CAAC,6CAA6C,CAAE+C,UAAU,CAAC,CACxE,KAAM,CAAAA,UAAU,CAClB,CACF,CAAC,IAAM,IAAI5E,WAAW,CAAE,CACtB;AACA,KAAM,IAAI,CAAA6E,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,GAAG,CAAC,CAAC,CAEtD;AACA,KAAM,CAAAqC,cAAc,CAAG1G,cAAc,CAAC2B,IAAI,CAAC4E,CAAC,EAAIA,CAAC,CAAC1E,EAAE,GAAKA,EAAE,CAAC,CAC5D,GAAI,CAAC6E,cAAc,CAAE,KAAM,IAAI,CAAAP,KAAK,CAAC,kBAAkB,CAAC,CAExD;AACAlG,iBAAiB,CAAC2F,IAAI,EAAIA,IAAI,CAACpE,MAAM,CAAC+E,CAAC,EAAIA,CAAC,CAAC1E,EAAE,GAAKA,EAAE,CAAC,CAAC,CAExD;AACA,KAAM,CAAAgE,gBAAgB,CAAG3F,aAAa,CAAC4F,GAAG,CAAClE,IAAI,EAAI,CACjD,GAAIA,IAAI,CAACC,EAAE,GAAK6E,cAAc,CAAC5E,MAAM,CAAE,CACrC,KAAM,CAAAwD,WAAW,CAAG1D,IAAI,CAACK,QAAQ,CAAGyE,cAAc,CAACzE,QAAQ,CAC3D,GAAI,CAAAsD,SAAkC,CAAG,WAAW,CAEpD,GAAID,WAAW,EAAI,CAAC,CAAE,CACpBC,SAAS,CAAG,SAAS,CACvB,CAAC,IAAM,IAAID,WAAW,CAAG1D,IAAI,CAAC6D,YAAY,CAAE,CAC1CF,SAAS,CAAG,KAAK,CACnB,CAAC,IAAM,CACLA,SAAS,CAAG,WAAW,CACzB,CAEA,OAAAP,aAAA,CAAAA,aAAA,IACKpD,IAAI,MACPK,QAAQ,CAAEqD,WAAW,CACrBE,MAAM,CAAED,SAAS,GAErB,CACA,MAAO,CAAA3D,IAAI,CACb,CAAC,CAAC,CAEFzB,gBAAgB,CAAC0F,gBAAgB,CAAC,CAClC3D,WAAW,CAAC,CAAC,CAEb,MAAO,KAAI,CACb,CAAC,IAAM,CACL;AACA,KAAM,CAAA1C,GAAG,CAACoH,MAAM,qBAAA3B,MAAA,CAAqBpD,EAAE,CAAE,CAAC,CAC1C,KAAM,CAAAwB,gBAAgB,CAAC,CAAC,CAAE;AAC1B,MAAO,KAAI,CACb,CACF,CAAE,MAAOwB,GAAG,CAAE,CACZf,OAAO,CAAC1C,KAAK,CAAC,gCAAgC,CAAEyD,GAAG,CAAC,CACpDxD,QAAQ,CAAC,iCAAiC,CAAC,CAC3C,KAAM,CAAAwD,GAAG,CACX,CAAC,OAAS,CACR1D,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA0F,gBAAgB,CAAG,KAAO,CAAAjF,IAA+B,EAAK,CAClE,GAAI,CACFT,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAItB,WAAW,EAAI,CAACR,WAAW,CAAE,CAC/B;AACA,GAAI,CACF;AACA,KAAM,CAAAiE,uBAAuB,CAAG,KAAM,CAAAD,aAAa,CAAC,gBAAgB,CAAC,CAErE;AACA,KAAM,CAAAuD,OAAsB,CAAA9B,aAAA,CAAAA,aAAA,IACvBpD,IAAI,MACPC,EAAE,SAAAoD,MAAA,CAAU7C,IAAI,CAAC8C,GAAG,CAAC,CAAC,CAAE,EACzB,CAED;AACA,KAAM,CAAA1B,uBAAuB,CAAC2B,SAAS,CAAC2B,OAAO,CAAC,CAEhD;AACA,KAAM,CAAAzD,gBAAgB,CAAC,CAAC,CACxB,MAAO,CAAAyD,OAAO,CAChB,CAAE,MAAO3C,UAAU,CAAE,CACnBL,OAAO,CAAC1C,KAAK,CAAC,yCAAyC,CAAE+C,UAAU,CAAC,CACpE,KAAM,CAAAA,UAAU,CAClB,CACF,CAAC,IAAM,IAAI5E,WAAW,CAAE,CACtB;AACA,KAAM,IAAI,CAAA6E,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,GAAG,CAAC,CAAC,CAEtD;AACA,KAAM,CAAAyC,OAAsB,CAAA9B,aAAA,CAAAA,aAAA,IACvBpD,IAAI,MACPC,EAAE,SAAAoD,MAAA,CAAU7C,IAAI,CAAC8C,GAAG,CAAC,CAAC,CAAE,EACzB,CAED;AACA/E,gBAAgB,CAACyF,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEkB,OAAO,CAAC,CAAC,CAC5C5E,WAAW,CAAC,CAAC,CAEb,MAAO,CAAA4E,OAAO,CAChB,CAAC,IAAM,CACL;AACA,KAAM,CAAAf,QAAQ,CAAG,KAAM,CAAAvG,GAAG,CAACwG,IAAI,CAAC,oBAAoB,CAAEpE,IAAI,CAAC,CAC3D,KAAM,CAAAyB,gBAAgB,CAAC,CAAC,CAAE;AAC1B,MAAO,CAAA0C,QAAQ,CAACnB,IAAI,CACtB,CACF,CAAE,MAAOC,GAAG,CAAE,CACZf,OAAO,CAAC1C,KAAK,CAAC,8BAA8B,CAAEyD,GAAG,CAAC,CAClDxD,QAAQ,CAAC,8BAA8B,CAAC,CACxC,KAAM,CAAAwD,GAAG,CACX,CAAC,OAAS,CACR1D,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAA4F,mBAAmB,CAAG,KAAAA,CAAOlF,EAAU,CAAED,IAA4B,GAAK,CAC9E,GAAI,CACFT,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAItB,WAAW,EAAI,CAACR,WAAW,CAAE,CAC/B;AACA,GAAI,CACF;AACA,KAAM,CAAAiE,uBAAuB,CAAG,KAAM,CAAAD,aAAa,CAAC,gBAAgB,CAAC,CAErE;AACA,KAAM,CAAAyD,YAAY,CAAG,KAAM,CAAAxD,uBAAuB,CAAC6B,OAAO,CAAC,CAAExD,EAAG,CAAC,CAAC,CAClE,GAAI,CAACmF,YAAY,CAAE,KAAM,IAAI,CAAAb,KAAK,CAAC,0BAA0B,CAAC,CAE9D;AACA,KAAM,CAAA3C,uBAAuB,CAACkC,SAAS,CACrC,CAAE7D,EAAG,CAAC,CACN,CAAE8D,IAAI,CAAE/D,IAAK,CACf,CAAC,CAED;AACA,KAAM,CAAAyB,gBAAgB,CAAC,CAAC,CAExB;AACA,KAAM,CAAA4D,WAAW,CAAG,KAAM,CAAAzD,uBAAuB,CAAC6B,OAAO,CAAC,CAAExD,EAAG,CAAC,CAAC,CACjE,MAAO,CAAAoF,WAAW,CACpB,CAAE,MAAO9C,UAAU,CAAE,CACnBL,OAAO,CAAC1C,KAAK,CAAC,2CAA2C,CAAE+C,UAAU,CAAC,CACtE,KAAM,CAAAA,UAAU,CAClB,CACF,CAAC,IAAM,IAAI5E,WAAW,CAAE,CACtB;AACA,KAAM,IAAI,CAAA6E,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,GAAG,CAAC,CAAC,CAEtD;AACA,KAAM,CAAA2C,YAAY,CAAG9G,aAAa,CAACyB,IAAI,CAACuF,CAAC,EAAIA,CAAC,CAACrF,EAAE,GAAKA,EAAE,CAAC,CACzD,GAAI,CAACmF,YAAY,CAAE,KAAM,IAAI,CAAAb,KAAK,CAAC,0BAA0B,CAAC,CAE9D;AACA,KAAM,CAAAc,WAA0B,CAAAjC,aAAA,CAAAA,aAAA,IAAQgC,YAAY,EAAKpF,IAAI,CAAE,CAE/D;AACAzB,gBAAgB,CAACyF,IAAI,EAAIA,IAAI,CAACE,GAAG,CAACoB,CAAC,EAAIA,CAAC,CAACrF,EAAE,GAAKA,EAAE,CAAGoF,WAAW,CAAGC,CAAC,CAAC,CAAC,CACtEhF,WAAW,CAAC,CAAC,CAEb,MAAO,CAAA+E,WAAW,CACpB,CAAC,IAAM,CACL;AACA,KAAM,CAAAlB,QAAQ,CAAG,KAAM,CAAAvG,GAAG,CAACgH,GAAG,uBAAAvB,MAAA,CAAuBpD,EAAE,EAAID,IAAI,CAAC,CAChE,KAAM,CAAAyB,gBAAgB,CAAC,CAAC,CAAE;AAC1B,MAAO,CAAA0C,QAAQ,CAACnB,IAAI,CACtB,CACF,CAAE,MAAOC,GAAG,CAAE,CACZf,OAAO,CAAC1C,KAAK,CAAC,gCAAgC,CAAEyD,GAAG,CAAC,CACpDxD,QAAQ,CAAC,iCAAiC,CAAC,CAC3C,KAAM,CAAAwD,GAAG,CACX,CAAC,OAAS,CACR1D,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED,KAAM,CAAAgG,mBAAmB,CAAG,KAAO,CAAAtF,EAAU,EAAK,CAChD,GAAI,CACFV,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAItB,WAAW,EAAI,CAACR,WAAW,CAAE,CAC/B;AACA,GAAI,CACF;AACA,KAAM,CAAAiE,uBAAuB,CAAG,KAAM,CAAAD,aAAa,CAAC,gBAAgB,CAAC,CACrE,KAAM,CAAAD,wBAAwB,CAAG,KAAM,CAAAC,aAAa,CAAC,iBAAiB,CAAC,CAEvE;AACA,KAAM,CAAA6D,eAAe,CAAG,KAAM,CAAA9D,wBAAwB,CAAC3B,IAAI,CAAC,CAAEG,MAAM,CAAED,EAAG,CAAC,CAAC,CAAC8B,OAAO,CAAC,CAAC,CACrF,GAAIyD,eAAe,CAACxE,MAAM,CAAG,CAAC,CAAE,CAC9B,KAAM,IAAI,CAAAuD,KAAK,CAAC,oDAAoD,CAAC,CACvE,CAEA;AACA,KAAM,CAAA3C,uBAAuB,CAACmD,SAAS,CAAC,CAAE9E,EAAG,CAAC,CAAC,CAE/C;AACA,KAAM,CAAAwB,gBAAgB,CAAC,CAAC,CACxB,MAAO,KAAI,CACb,CAAE,MAAOc,UAAU,CAAE,CACnBL,OAAO,CAAC1C,KAAK,CAAC,6CAA6C,CAAE+C,UAAU,CAAC,CACxE,KAAM,CAAAA,UAAU,CAClB,CACF,CAAC,IAAM,IAAI5E,WAAW,CAAE,CACtB;AACA,KAAM,IAAI,CAAA6E,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,GAAG,CAAC,CAAC,CAEtD;AACA,KAAM,CAAAgD,gBAAgB,CAAGrH,cAAc,CAACsH,IAAI,CAAC7F,MAAM,EAAIA,MAAM,CAACK,MAAM,GAAKD,EAAE,CAAC,CAC5E,GAAIwF,gBAAgB,CAAE,CACpB,KAAM,IAAI,CAAAlB,KAAK,CAAC,oDAAoD,CAAC,CACvE,CAEA;AACAhG,gBAAgB,CAACyF,IAAI,EAAIA,IAAI,CAACpE,MAAM,CAAC0F,CAAC,EAAIA,CAAC,CAACrF,EAAE,GAAKA,EAAE,CAAC,CAAC,CACvDK,WAAW,CAAC,CAAC,CAEb,MAAO,KAAI,CACb,CAAC,IAAM,CACL;AACA,KAAM,CAAA1C,GAAG,CAACoH,MAAM,uBAAA3B,MAAA,CAAuBpD,EAAE,CAAE,CAAC,CAC5C,KAAM,CAAAwB,gBAAgB,CAAC,CAAC,CAAE;AAC1B,MAAO,KAAI,CACb,CACF,CAAE,MAAOwB,GAAG,CAAE,CACZf,OAAO,CAAC1C,KAAK,CAAC,gCAAgC,CAAEyD,GAAG,CAAC,CACpDxD,QAAQ,CAAC,iCAAiC,CAAC,CAC3C,KAAM,CAAAwD,GAAG,CACX,CAAC,OAAS,CACR1D,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAoG,mBAAmB,CAAG,KAAO,CAAAC,UAA6C,EAAK,CACnF,GAAI,CACFrG,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CAEd,GAAItB,WAAW,EAAI,CAACR,WAAW,CAAE,CAC/B,GAAI,CACF;AACA,GAAI,CAAAqF,IAAW,CAAG,EAAE,CAEpB,GAAI4C,UAAU,GAAK,SAAS,CAAE,CAC5B,KAAM,CAAAlE,wBAAwB,CAAG,KAAM,CAAAC,aAAa,CAAC,iBAAiB,CAAC,CACvEqB,IAAI,CAAG,KAAM,CAAAtB,wBAAwB,CAAC3B,IAAI,CAAC,CAAC,CAAC,CAAC,CAACgC,OAAO,CAAC,CAAC,CAC1D,CAAC,IAAM,IAAI6D,UAAU,GAAK,WAAW,CAAE,CACrC,KAAM,CAAAhE,uBAAuB,CAAG,KAAM,CAAAD,aAAa,CAAC,gBAAgB,CAAC,CACrEqB,IAAI,CAAG,KAAM,CAAApB,uBAAuB,CAAC7B,IAAI,CAAC,CAAC,CAAC,CAAC,CAACgC,OAAO,CAAC,CAAC,CACzD,CAAC,IAAM,IAAI6D,UAAU,GAAK,OAAO,CAAE,CACjC,KAAM,CAAA/D,sBAAsB,CAAG,KAAM,CAAAF,aAAa,CAAC,eAAe,CAAC,CACnEqB,IAAI,CAAG,KAAM,CAAAnB,sBAAsB,CAAC9B,IAAI,CAAC,CAAC,CAAC,CAAC,CAACgC,OAAO,CAAC,CAAC,CACxD,CAEA;AACA;AACAG,OAAO,CAACC,GAAG,cAAAkB,MAAA,CAAcuC,UAAU,kBAAAvC,MAAA,CAAgBL,IAAI,CAAChC,MAAM,YAAU,CAAC,CACzE,SAAAqC,MAAA,CAAUuC,UAAU,aAAAvC,MAAA,CAAW,GAAI,CAAA7C,IAAI,CAAC,CAAC,CAACqF,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,UACvE,CAAE,MAAOvD,UAAU,CAAE,CACnBL,OAAO,CAAC1C,KAAK,qBAAA6D,MAAA,CAAqBuC,UAAU,0BAAyBrD,UAAU,CAAC,CAChF,KAAM,CAAAA,UAAU,CAClB,CACF,CAAC,IAAM,IAAI5E,WAAW,CAAE,CACtB;AACA,KAAM,IAAI,CAAA6E,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CACvD,SAAAY,MAAA,CAAUuC,UAAU,aAAAvC,MAAA,CAAW,GAAI,CAAA7C,IAAI,CAAC,CAAC,CAACqF,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,UACvE,CAAC,IAAM,CACL;AACA,KAAM,CAAA3B,QAAQ,CAAG,KAAM,CAAAvG,GAAG,CAACmF,GAAG,qBAAAM,MAAA,CAAqBuC,UAAU,CAAE,CAAC,CAChE,MAAO,CAAAzB,QAAQ,CAACnB,IAAI,CAAC+C,SAAS,CAChC,CACF,CAAE,MAAO9C,GAAG,CAAE,CACZf,OAAO,CAAC1C,KAAK,qBAAA6D,MAAA,CAAqBuC,UAAU,aAAY3C,GAAG,CAAC,CAC5DxD,QAAQ,uBAAA4D,MAAA,CAAuBuC,UAAU,WAAS,CAAC,CACnD,KAAM,CAAA3C,GAAG,CACX,CAAC,OAAS,CACR1D,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA9B,SAAS,CAAC,IAAM,CACdgE,gBAAgB,CAAC,CAAC,CACpB,CAAC,CAAE,EAAE,CAAC,CAEN,MAAO,CACLrD,cAAc,CACdE,aAAa,CACbE,YAAY,CACZE,KAAK,CACLY,OAAO,CACPE,KAAK,CACLiC,gBAAgB,CAChByB,gBAAgB,CAChBmB,mBAAmB,CACnBQ,mBAAmB,CACnBI,gBAAgB,CAChBE,mBAAmB,CACnBI,mBAAmB,CACnBI,mBACF,CAAC,CACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}