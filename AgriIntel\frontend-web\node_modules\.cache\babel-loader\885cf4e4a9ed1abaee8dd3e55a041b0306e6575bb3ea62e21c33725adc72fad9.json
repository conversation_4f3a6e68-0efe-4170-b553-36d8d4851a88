{"ast": null, "code": "import isSameWeek from \"../isSameWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameISOWeek\n * @category ISO Week Helpers\n * @summary Are the given dates in the same ISO week (and year)?\n *\n * @description\n * Are the given dates in the same ISO week (and year)?\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same ISO week (and year)\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 1 September 2014 and 7 September 2014 in the same ISO week?\n * const result = isSameISOWeek(new Date(2014, 8, 1), new Date(2014, 8, 7))\n * //=> true\n *\n * @example\n * // Are 1 September 2014 and 1 September 2015 in the same ISO week?\n * const result = isSameISOWeek(new Date(2014, 8, 1), new Date(2015, 8, 1))\n * //=> false\n */\nexport default function isSameISOWeek(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  return isSameWeek(dirtyDateLeft, dirtyDateRight, {\n    weekStartsOn: 1\n  });\n}", "map": {"version": 3, "names": ["isSameWeek", "requiredArgs", "isSameISOWeek", "dirtyDateLeft", "dirtyDateRight", "arguments", "weekStartsOn"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/date-fns/esm/isSameISOWeek/index.js"], "sourcesContent": ["import isSameWeek from \"../isSameWeek/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name isSameISOWeek\n * @category ISO Week Helpers\n * @summary Are the given dates in the same ISO week (and year)?\n *\n * @description\n * Are the given dates in the same ISO week (and year)?\n *\n * ISO week-numbering year: http://en.wikipedia.org/wiki/ISO_week_date\n *\n * @param {Date|Number} dateLeft - the first date to check\n * @param {Date|Number} dateRight - the second date to check\n * @returns {Boolean} the dates are in the same ISO week (and year)\n * @throws {TypeError} 2 arguments required\n *\n * @example\n * // Are 1 September 2014 and 7 September 2014 in the same ISO week?\n * const result = isSameISOWeek(new Date(2014, 8, 1), new Date(2014, 8, 7))\n * //=> true\n *\n * @example\n * // Are 1 September 2014 and 1 September 2015 in the same ISO week?\n * const result = isSameISOWeek(new Date(2014, 8, 1), new Date(2015, 8, 1))\n * //=> false\n */\nexport default function isSameISOWeek(dirtyDateLeft, dirtyDateRight) {\n  requiredArgs(2, arguments);\n  return isSameWeek(dirtyDateLeft, dirtyDateRight, {\n    weekStartsOn: 1\n  });\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,wBAAwB;AAC/C,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,aAAaA,CAACC,aAAa,EAAEC,cAAc,EAAE;EACnEH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,OAAOL,UAAU,CAACG,aAAa,EAAEC,cAAc,EAAE;IAC/CE,YAAY,EAAE;EAChB,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}