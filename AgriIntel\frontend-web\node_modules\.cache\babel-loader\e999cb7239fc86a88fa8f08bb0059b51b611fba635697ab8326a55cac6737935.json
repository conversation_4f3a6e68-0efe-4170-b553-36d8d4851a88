{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.RunCommandCursor = void 0;\nconst responses_1 = require(\"../cmap/wire_protocol/responses\");\nconst error_1 = require(\"../error\");\nconst execute_operation_1 = require(\"../operations/execute_operation\");\nconst get_more_1 = require(\"../operations/get_more\");\nconst run_command_1 = require(\"../operations/run_command\");\nconst utils_1 = require(\"../utils\");\nconst abstract_cursor_1 = require(\"./abstract_cursor\");\n/** @public */\nclass RunCommandCursor extends abstract_cursor_1.AbstractCursor {\n  /**\n   * Controls the `getMore.comment` field\n   * @param comment - any BSON value\n   */\n  setComment(comment) {\n    this.getMoreOptions.comment = comment;\n    return this;\n  }\n  /**\n   * Controls the `getMore.maxTimeMS` field. Only valid when cursor is tailable await\n   * @param maxTimeMS - the number of milliseconds to wait for new data\n   */\n  setMaxTimeMS(maxTimeMS) {\n    this.getMoreOptions.maxAwaitTimeMS = maxTimeMS;\n    return this;\n  }\n  /**\n   * Controls the `getMore.batchSize` field\n   * @param batchSize - the number documents to return in the `nextBatch`\n   */\n  setBatchSize(batchSize) {\n    this.getMoreOptions.batchSize = batchSize;\n    return this;\n  }\n  /** Unsupported for RunCommandCursor */\n  clone() {\n    throw new error_1.MongoAPIError('Clone not supported, create a new cursor with db.runCursorCommand');\n  }\n  /** Unsupported for RunCommandCursor: readConcern must be configured directly on command document */\n  withReadConcern(_) {\n    throw new error_1.MongoAPIError('RunCommandCursor does not support readConcern it must be attached to the command being run');\n  }\n  /** Unsupported for RunCommandCursor: various cursor flags must be configured directly on command document */\n  addCursorFlag(_, __) {\n    throw new error_1.MongoAPIError('RunCommandCursor does not support cursor flags, they must be attached to the command being run');\n  }\n  /**\n   * Unsupported for RunCommandCursor: maxTimeMS must be configured directly on command document\n   */\n  maxTimeMS(_) {\n    throw new error_1.MongoAPIError('maxTimeMS must be configured on the command document directly, to configure getMore.maxTimeMS use cursor.setMaxTimeMS()');\n  }\n  /** Unsupported for RunCommandCursor: batchSize must be configured directly on command document */\n  batchSize(_) {\n    throw new error_1.MongoAPIError('batchSize must be configured on the command document directly, to configure getMore.batchSize use cursor.setBatchSize()');\n  }\n  /** @internal */\n  constructor(db, command, options = {}) {\n    super(db.client, (0, utils_1.ns)(db.namespace), options);\n    this.getMoreOptions = {};\n    this.db = db;\n    this.command = Object.freeze({\n      ...command\n    });\n  }\n  /** @internal */\n  async _initialize(session) {\n    const operation = new run_command_1.RunCommandOperation(this.db, this.command, {\n      ...this.cursorOptions,\n      session: session,\n      readPreference: this.cursorOptions.readPreference,\n      responseType: responses_1.CursorResponse\n    });\n    const response = await (0, execute_operation_1.executeOperation)(this.client, operation, this.timeoutContext);\n    return {\n      server: operation.server,\n      session,\n      response\n    };\n  }\n  /** @internal */\n  async getMore(_batchSize) {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const getMoreOperation = new get_more_1.GetMoreOperation(this.namespace, this.id, this.server, {\n      ...this.cursorOptions,\n      session: this.session,\n      ...this.getMoreOptions\n    });\n    return await (0, execute_operation_1.executeOperation)(this.client, getMoreOperation, this.timeoutContext);\n  }\n}\nexports.RunCommandCursor = RunCommandCursor;", "map": {"version": 3, "names": ["responses_1", "require", "error_1", "execute_operation_1", "get_more_1", "run_command_1", "utils_1", "abstract_cursor_1", "RunCommandCursor", "AbstractCursor", "setComment", "comment", "getMoreOptions", "setMaxTimeMS", "maxTimeMS", "maxAwaitTimeMS", "setBatchSize", "batchSize", "clone", "MongoAPIError", "withReadConcern", "_", "addCursorFlag", "__", "constructor", "db", "command", "options", "client", "ns", "namespace", "Object", "freeze", "_initialize", "session", "operation", "RunCommandOperation", "cursorOptions", "readPreference", "responseType", "CursorResponse", "response", "executeOperation", "timeoutContext", "server", "getMore", "_batchSize", "getMoreOperation", "GetMoreOperation", "id", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cursor\\run_command_cursor.ts"], "sourcesContent": ["import type { BSONSerializeOptions, Document } from '../bson';\nimport { CursorResponse } from '../cmap/wire_protocol/responses';\nimport type { Db } from '../db';\nimport { MongoAPIError } from '../error';\nimport { executeOperation } from '../operations/execute_operation';\nimport { GetMoreOperation } from '../operations/get_more';\nimport { RunCommandOperation } from '../operations/run_command';\nimport type { ReadConcernLike } from '../read_concern';\nimport type { ReadPreferenceLike } from '../read_preference';\nimport type { ClientSession } from '../sessions';\nimport { ns } from '../utils';\nimport {\n  AbstractCursor,\n  type CursorTimeoutMode,\n  type InitialCursorResponse\n} from './abstract_cursor';\n\n/** @public */\nexport type RunCursorCommandOptions = {\n  readPreference?: ReadPreferenceLike;\n  session?: ClientSession;\n  /**\n   * @experimental\n   * Specifies the time an operation will run until it throws a timeout error. Note that if\n   * `maxTimeMS` is provided in the command in addition to setting `timeoutMS` in the options, then\n   * the original value of `maxTimeMS` will be overwritten.\n   */\n  timeoutMS?: number;\n  /**\n   * @public\n   * @experimental\n   * Specifies how `timeoutMS` is applied to the cursor. Can be either `'cursorLifeTime'` or `'iteration'`\n   * When set to `'iteration'`, the deadline specified by `timeoutMS` applies to each call of\n   * `cursor.next()`.\n   * When set to `'cursorLifetime'`, the deadline applies to the life of the entire cursor.\n   *\n   * Depending on the type of cursor being used, this option has different default values.\n   * For non-tailable cursors, this value defaults to `'cursorLifetime'`\n   * For tailable cursors, this value defaults to `'iteration'` since tailable cursors, by\n   * definition can have an arbitrarily long lifetime.\n   *\n   * @example\n   * ```ts\n   * const cursor = collection.find({}, {timeoutMS: 100, timeoutMode: 'iteration'});\n   * for await (const doc of cursor) {\n   *  // process doc\n   *  // This will throw a timeout error if any of the iterator's `next()` calls takes more than 100ms, but\n   *  // will continue to iterate successfully otherwise, regardless of the number of batches.\n   * }\n   * ```\n   *\n   * @example\n   * ```ts\n   * const cursor = collection.find({}, { timeoutMS: 1000, timeoutMode: 'cursorLifetime' });\n   * const docs = await cursor.toArray(); // This entire line will throw a timeout error if all batches are not fetched and returned within 1000ms.\n   * ```\n   */\n  timeoutMode?: CursorTimeoutMode;\n  tailable?: boolean;\n  awaitData?: boolean;\n} & BSONSerializeOptions;\n\n/** @public */\nexport class RunCommandCursor extends AbstractCursor {\n  public readonly command: Readonly<Record<string, any>>;\n  public readonly getMoreOptions: {\n    comment?: any;\n    maxAwaitTimeMS?: number;\n    batchSize?: number;\n  } = {};\n\n  /**\n   * Controls the `getMore.comment` field\n   * @param comment - any BSON value\n   */\n  public setComment(comment: any): this {\n    this.getMoreOptions.comment = comment;\n    return this;\n  }\n\n  /**\n   * Controls the `getMore.maxTimeMS` field. Only valid when cursor is tailable await\n   * @param maxTimeMS - the number of milliseconds to wait for new data\n   */\n  public setMaxTimeMS(maxTimeMS: number): this {\n    this.getMoreOptions.maxAwaitTimeMS = maxTimeMS;\n    return this;\n  }\n\n  /**\n   * Controls the `getMore.batchSize` field\n   * @param batchSize - the number documents to return in the `nextBatch`\n   */\n  public setBatchSize(batchSize: number): this {\n    this.getMoreOptions.batchSize = batchSize;\n    return this;\n  }\n\n  /** Unsupported for RunCommandCursor */\n  public override clone(): never {\n    throw new MongoAPIError('Clone not supported, create a new cursor with db.runCursorCommand');\n  }\n\n  /** Unsupported for RunCommandCursor: readConcern must be configured directly on command document */\n  public override withReadConcern(_: ReadConcernLike): never {\n    throw new MongoAPIError(\n      'RunCommandCursor does not support readConcern it must be attached to the command being run'\n    );\n  }\n\n  /** Unsupported for RunCommandCursor: various cursor flags must be configured directly on command document */\n  public override addCursorFlag(_: string, __: boolean): never {\n    throw new MongoAPIError(\n      'RunCommandCursor does not support cursor flags, they must be attached to the command being run'\n    );\n  }\n\n  /**\n   * Unsupported for RunCommandCursor: maxTimeMS must be configured directly on command document\n   */\n  public override maxTimeMS(_: number): never {\n    throw new MongoAPIError(\n      'maxTimeMS must be configured on the command document directly, to configure getMore.maxTimeMS use cursor.setMaxTimeMS()'\n    );\n  }\n\n  /** Unsupported for RunCommandCursor: batchSize must be configured directly on command document */\n  public override batchSize(_: number): never {\n    throw new MongoAPIError(\n      'batchSize must be configured on the command document directly, to configure getMore.batchSize use cursor.setBatchSize()'\n    );\n  }\n\n  /** @internal */\n  private db: Db;\n\n  /** @internal */\n  constructor(db: Db, command: Document, options: RunCursorCommandOptions = {}) {\n    super(db.client, ns(db.namespace), options);\n    this.db = db;\n    this.command = Object.freeze({ ...command });\n  }\n\n  /** @internal */\n  protected async _initialize(session: ClientSession): Promise<InitialCursorResponse> {\n    const operation = new RunCommandOperation<CursorResponse>(this.db, this.command, {\n      ...this.cursorOptions,\n      session: session,\n      readPreference: this.cursorOptions.readPreference,\n      responseType: CursorResponse\n    });\n\n    const response = await executeOperation(this.client, operation, this.timeoutContext);\n\n    return {\n      server: operation.server,\n      session,\n      response\n    };\n  }\n\n  /** @internal */\n  override async getMore(_batchSize: number): Promise<CursorResponse> {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const getMoreOperation = new GetMoreOperation(this.namespace, this.id!, this.server!, {\n      ...this.cursorOptions,\n      session: this.session,\n      ...this.getMoreOptions\n    });\n\n    return await executeOperation(this.client, getMoreOperation, this.timeoutContext);\n  }\n}\n"], "mappings": ";;;;;;AACA,MAAAA,WAAA,GAAAC,OAAA;AAEA,MAAAC,OAAA,GAAAD,OAAA;AACA,MAAAE,mBAAA,GAAAF,OAAA;AACA,MAAAG,UAAA,GAAAH,OAAA;AACA,MAAAI,aAAA,GAAAJ,OAAA;AAIA,MAAAK,OAAA,GAAAL,OAAA;AACA,MAAAM,iBAAA,GAAAN,OAAA;AAmDA;AACA,MAAaO,gBAAiB,SAAQD,iBAAA,CAAAE,cAAc;EAQlD;;;;EAIOC,UAAUA,CAACC,OAAY;IAC5B,IAAI,CAACC,cAAc,CAACD,OAAO,GAAGA,OAAO;IACrC,OAAO,IAAI;EACb;EAEA;;;;EAIOE,YAAYA,CAACC,SAAiB;IACnC,IAAI,CAACF,cAAc,CAACG,cAAc,GAAGD,SAAS;IAC9C,OAAO,IAAI;EACb;EAEA;;;;EAIOE,YAAYA,CAACC,SAAiB;IACnC,IAAI,CAACL,cAAc,CAACK,SAAS,GAAGA,SAAS;IACzC,OAAO,IAAI;EACb;EAEA;EACgBC,KAAKA,CAAA;IACnB,MAAM,IAAIhB,OAAA,CAAAiB,aAAa,CAAC,mEAAmE,CAAC;EAC9F;EAEA;EACgBC,eAAeA,CAACC,CAAkB;IAChD,MAAM,IAAInB,OAAA,CAAAiB,aAAa,CACrB,4FAA4F,CAC7F;EACH;EAEA;EACgBG,aAAaA,CAACD,CAAS,EAAEE,EAAW;IAClD,MAAM,IAAIrB,OAAA,CAAAiB,aAAa,CACrB,gGAAgG,CACjG;EACH;EAEA;;;EAGgBL,SAASA,CAACO,CAAS;IACjC,MAAM,IAAInB,OAAA,CAAAiB,aAAa,CACrB,yHAAyH,CAC1H;EACH;EAEA;EACgBF,SAASA,CAACI,CAAS;IACjC,MAAM,IAAInB,OAAA,CAAAiB,aAAa,CACrB,yHAAyH,CAC1H;EACH;EAKA;EACAK,YAAYC,EAAM,EAAEC,OAAiB,EAAEC,OAAA,GAAmC,EAAE;IAC1E,KAAK,CAACF,EAAE,CAACG,MAAM,EAAE,IAAAtB,OAAA,CAAAuB,EAAE,EAACJ,EAAE,CAACK,SAAS,CAAC,EAAEH,OAAO,CAAC;IAzE7B,KAAAf,cAAc,GAI1B,EAAE;IAsEJ,IAAI,CAACa,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,OAAO,GAAGK,MAAM,CAACC,MAAM,CAAC;MAAE,GAAGN;IAAO,CAAE,CAAC;EAC9C;EAEA;EACU,MAAMO,WAAWA,CAACC,OAAsB;IAChD,MAAMC,SAAS,GAAG,IAAI9B,aAAA,CAAA+B,mBAAmB,CAAiB,IAAI,CAACX,EAAE,EAAE,IAAI,CAACC,OAAO,EAAE;MAC/E,GAAG,IAAI,CAACW,aAAa;MACrBH,OAAO,EAAEA,OAAO;MAChBI,cAAc,EAAE,IAAI,CAACD,aAAa,CAACC,cAAc;MACjDC,YAAY,EAAEvC,WAAA,CAAAwC;KACf,CAAC;IAEF,MAAMC,QAAQ,GAAG,MAAM,IAAAtC,mBAAA,CAAAuC,gBAAgB,EAAC,IAAI,CAACd,MAAM,EAAEO,SAAS,EAAE,IAAI,CAACQ,cAAc,CAAC;IAEpF,OAAO;MACLC,MAAM,EAAET,SAAS,CAACS,MAAM;MACxBV,OAAO;MACPO;KACD;EACH;EAEA;EACS,MAAMI,OAAOA,CAACC,UAAkB;IACvC;IACA,MAAMC,gBAAgB,GAAG,IAAI3C,UAAA,CAAA4C,gBAAgB,CAAC,IAAI,CAAClB,SAAS,EAAE,IAAI,CAACmB,EAAG,EAAE,IAAI,CAACL,MAAO,EAAE;MACpF,GAAG,IAAI,CAACP,aAAa;MACrBH,OAAO,EAAE,IAAI,CAACA,OAAO;MACrB,GAAG,IAAI,CAACtB;KACT,CAAC;IAEF,OAAO,MAAM,IAAAT,mBAAA,CAAAuC,gBAAgB,EAAC,IAAI,CAACd,MAAM,EAAEmB,gBAAgB,EAAE,IAAI,CAACJ,cAAc,CAAC;EACnF;;AA5GFO,OAAA,CAAA1C,gBAAA,GAAAA,gBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}