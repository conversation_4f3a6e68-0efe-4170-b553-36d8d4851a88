{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.AzureMachineWorkflow = void 0;\nconst azure_1 = require(\"../../../client-side-encryption/providers/azure\");\nconst error_1 = require(\"../../../error\");\nconst utils_1 = require(\"../../../utils\");\nconst machine_workflow_1 = require(\"./machine_workflow\");\n/** Azure request headers. */\nconst AZURE_HEADERS = Object.freeze({\n  Metadata: 'true',\n  Accept: 'application/json'\n});\n/** Invalid endpoint result error. */\nconst ENDPOINT_RESULT_ERROR = 'Azure endpoint did not return a value with only access_token and expires_in properties';\n/** Error for when the token audience is missing in the environment. */\nconst TOKEN_RESOURCE_MISSING_ERROR = 'TOKEN_RESOURCE must be set in the auth mechanism properties when ENVIRONMENT is azure.';\n/**\n * Device workflow implementation for Azure.\n *\n * @internal\n */\nclass AzureMachineWorkflow extends machine_workflow_1.MachineWorkflow {\n  /**\n   * Instantiate the machine workflow.\n   */\n  constructor(cache) {\n    super(cache);\n  }\n  /**\n   * Get the token from the environment.\n   */\n  async getToken(credentials) {\n    const tokenAudience = credentials?.mechanismProperties.TOKEN_RESOURCE;\n    const username = credentials?.username;\n    if (!tokenAudience) {\n      throw new error_1.MongoAzureError(TOKEN_RESOURCE_MISSING_ERROR);\n    }\n    const response = await getAzureTokenData(tokenAudience, username);\n    if (!isEndpointResultValid(response)) {\n      throw new error_1.MongoAzureError(ENDPOINT_RESULT_ERROR);\n    }\n    return response;\n  }\n}\nexports.AzureMachineWorkflow = AzureMachineWorkflow;\n/**\n * Hit the Azure endpoint to get the token data.\n */\nasync function getAzureTokenData(tokenAudience, username) {\n  const url = new URL(azure_1.AZURE_BASE_URL);\n  (0, azure_1.addAzureParams)(url, tokenAudience, username);\n  const response = await (0, utils_1.get)(url, {\n    headers: AZURE_HEADERS\n  });\n  if (response.status !== 200) {\n    throw new error_1.MongoAzureError(`Status code ${response.status} returned from the Azure endpoint. Response body: ${response.body}`);\n  }\n  const result = JSON.parse(response.body);\n  return {\n    access_token: result.access_token,\n    expires_in: Number(result.expires_in)\n  };\n}\n/**\n * Determines if a result returned from the endpoint is valid.\n * This means the result is not nullish, contains the access_token required field\n * and the expires_in required field.\n */\nfunction isEndpointResultValid(token) {\n  if (token == null || typeof token !== 'object') return false;\n  return 'access_token' in token && typeof token.access_token === 'string' && 'expires_in' in token && typeof token.expires_in === 'number';\n}", "map": {"version": 3, "names": ["azure_1", "require", "error_1", "utils_1", "machine_workflow_1", "AZURE_HEADERS", "Object", "freeze", "<PERSON><PERSON><PERSON>", "Accept", "ENDPOINT_RESULT_ERROR", "TOKEN_RESOURCE_MISSING_ERROR", "AzureMachineWorkflow", "MachineWorkflow", "constructor", "cache", "getToken", "credentials", "tokenAudience", "mechanismProperties", "TOKEN_RESOURCE", "username", "MongoAzureError", "response", "getAzureTokenData", "isEndpointResultValid", "exports", "url", "URL", "AZURE_BASE_URL", "addAzureParams", "get", "headers", "status", "body", "result", "JSON", "parse", "access_token", "expires_in", "Number", "token"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\auth\\mongodb_oidc\\azure_machine_workflow.ts"], "sourcesContent": ["import { add<PERSON>zurePara<PERSON>, AZURE_BASE_URL } from '../../../client-side-encryption/providers/azure';\nimport { MongoAzureError } from '../../../error';\nimport { get } from '../../../utils';\nimport type { MongoCredentials } from '../mongo_credentials';\nimport { type AccessToken, MachineWorkflow } from './machine_workflow';\nimport { type TokenCache } from './token_cache';\n\n/** Azure request headers. */\nconst AZURE_HEADERS = Object.freeze({ Metadata: 'true', Accept: 'application/json' });\n\n/** Invalid endpoint result error. */\nconst ENDPOINT_RESULT_ERROR =\n  'Azure endpoint did not return a value with only access_token and expires_in properties';\n\n/** Error for when the token audience is missing in the environment. */\nconst TOKEN_RESOURCE_MISSING_ERROR =\n  'TOKEN_RESOURCE must be set in the auth mechanism properties when ENVIRONMENT is azure.';\n\n/**\n * Device workflow implementation for Azure.\n *\n * @internal\n */\nexport class AzureMachineWorkflow extends MachineWorkflow {\n  /**\n   * Instantiate the machine workflow.\n   */\n  constructor(cache: TokenCache) {\n    super(cache);\n  }\n\n  /**\n   * Get the token from the environment.\n   */\n  async getToken(credentials?: MongoCredentials): Promise<AccessToken> {\n    const tokenAudience = credentials?.mechanismProperties.TOKEN_RESOURCE;\n    const username = credentials?.username;\n    if (!tokenAudience) {\n      throw new MongoAzureError(TOKEN_RESOURCE_MISSING_ERROR);\n    }\n    const response = await getAzureTokenData(tokenAudience, username);\n    if (!isEndpointResultValid(response)) {\n      throw new MongoAzureError(ENDPOINT_RESULT_ERROR);\n    }\n    return response;\n  }\n}\n\n/**\n * Hit the Azure endpoint to get the token data.\n */\nasync function getAzureTokenData(tokenAudience: string, username?: string): Promise<AccessToken> {\n  const url = new URL(AZURE_BASE_URL);\n  addAzureParams(url, tokenAudience, username);\n  const response = await get(url, {\n    headers: AZURE_HEADERS\n  });\n  if (response.status !== 200) {\n    throw new MongoAzureError(\n      `Status code ${response.status} returned from the Azure endpoint. Response body: ${response.body}`\n    );\n  }\n  const result = JSON.parse(response.body);\n  return {\n    access_token: result.access_token,\n    expires_in: Number(result.expires_in)\n  };\n}\n\n/**\n * Determines if a result returned from the endpoint is valid.\n * This means the result is not nullish, contains the access_token required field\n * and the expires_in required field.\n */\nfunction isEndpointResultValid(\n  token: unknown\n): token is { access_token: unknown; expires_in: unknown } {\n  if (token == null || typeof token !== 'object') return false;\n  return (\n    'access_token' in token &&\n    typeof token.access_token === 'string' &&\n    'expires_in' in token &&\n    typeof token.expires_in === 'number'\n  );\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,OAAA,GAAAC,OAAA;AACA,MAAAC,OAAA,GAAAD,OAAA;AACA,MAAAE,OAAA,GAAAF,OAAA;AAEA,MAAAG,kBAAA,GAAAH,OAAA;AAGA;AACA,MAAMI,aAAa,GAAGC,MAAM,CAACC,MAAM,CAAC;EAAEC,QAAQ,EAAE,MAAM;EAAEC,MAAM,EAAE;AAAkB,CAAE,CAAC;AAErF;AACA,MAAMC,qBAAqB,GACzB,wFAAwF;AAE1F;AACA,MAAMC,4BAA4B,GAChC,wFAAwF;AAE1F;;;;;AAKA,MAAaC,oBAAqB,SAAQR,kBAAA,CAAAS,eAAe;EACvD;;;EAGAC,YAAYC,KAAiB;IAC3B,KAAK,CAACA,KAAK,CAAC;EACd;EAEA;;;EAGA,MAAMC,QAAQA,CAACC,WAA8B;IAC3C,MAAMC,aAAa,GAAGD,WAAW,EAAEE,mBAAmB,CAACC,cAAc;IACrE,MAAMC,QAAQ,GAAGJ,WAAW,EAAEI,QAAQ;IACtC,IAAI,CAACH,aAAa,EAAE;MAClB,MAAM,IAAIhB,OAAA,CAAAoB,eAAe,CAACX,4BAA4B,CAAC;IACzD;IACA,MAAMY,QAAQ,GAAG,MAAMC,iBAAiB,CAACN,aAAa,EAAEG,QAAQ,CAAC;IACjE,IAAI,CAACI,qBAAqB,CAACF,QAAQ,CAAC,EAAE;MACpC,MAAM,IAAIrB,OAAA,CAAAoB,eAAe,CAACZ,qBAAqB,CAAC;IAClD;IACA,OAAOa,QAAQ;EACjB;;AAtBFG,OAAA,CAAAd,oBAAA,GAAAA,oBAAA;AAyBA;;;AAGA,eAAeY,iBAAiBA,CAACN,aAAqB,EAAEG,QAAiB;EACvE,MAAMM,GAAG,GAAG,IAAIC,GAAG,CAAC5B,OAAA,CAAA6B,cAAc,CAAC;EACnC,IAAA7B,OAAA,CAAA8B,cAAc,EAACH,GAAG,EAAET,aAAa,EAAEG,QAAQ,CAAC;EAC5C,MAAME,QAAQ,GAAG,MAAM,IAAApB,OAAA,CAAA4B,GAAG,EAACJ,GAAG,EAAE;IAC9BK,OAAO,EAAE3B;GACV,CAAC;EACF,IAAIkB,QAAQ,CAACU,MAAM,KAAK,GAAG,EAAE;IAC3B,MAAM,IAAI/B,OAAA,CAAAoB,eAAe,CACvB,eAAeC,QAAQ,CAACU,MAAM,qDAAqDV,QAAQ,CAACW,IAAI,EAAE,CACnG;EACH;EACA,MAAMC,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACd,QAAQ,CAACW,IAAI,CAAC;EACxC,OAAO;IACLI,YAAY,EAAEH,MAAM,CAACG,YAAY;IACjCC,UAAU,EAAEC,MAAM,CAACL,MAAM,CAACI,UAAU;GACrC;AACH;AAEA;;;;;AAKA,SAASd,qBAAqBA,CAC5BgB,KAAc;EAEd,IAAIA,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAO,KAAK;EAC5D,OACE,cAAc,IAAIA,KAAK,IACvB,OAAOA,KAAK,CAACH,YAAY,KAAK,QAAQ,IACtC,YAAY,IAAIG,KAAK,IACrB,OAAOA,KAAK,CAACF,UAAU,KAAK,QAAQ;AAExC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}