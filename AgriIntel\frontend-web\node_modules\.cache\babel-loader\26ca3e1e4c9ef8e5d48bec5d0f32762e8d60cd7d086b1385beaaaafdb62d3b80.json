{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nconst initialState = {\n  sidebarOpen: true,\n  theme: 'light',\n  language: localStorage.getItem('language') || 'en',\n  notifications: [],\n  loading: {\n    global: false\n  },\n  modals: {},\n  breadcrumbs: [],\n  pageTitle: 'Dashboard'\n};\nconst uiSlice = createSlice({\n  name: 'ui',\n  initialState,\n  reducers: {\n    toggleSidebar: state => {\n      state.sidebarOpen = !state.sidebarOpen;\n    },\n    setSidebarOpen: (state, action) => {\n      state.sidebarOpen = action.payload;\n    },\n    setTheme: (state, action) => {\n      state.theme = action.payload;\n      localStorage.setItem('theme', action.payload);\n    },\n    setLanguage: (state, action) => {\n      state.language = action.payload;\n      localStorage.setItem('language', action.payload);\n    },\n    addNotification: (state, action) => {\n      const notification = {\n        ...action.payload,\n        id: Date.now().toString(),\n        timestamp: new Date().toISOString(),\n        read: false\n      };\n      state.notifications.unshift(notification);\n\n      // Keep only last 50 notifications\n      if (state.notifications.length > 50) {\n        state.notifications = state.notifications.slice(0, 50);\n      }\n    },\n    removeNotification: (state, action) => {\n      state.notifications = state.notifications.filter(n => n.id !== action.payload);\n    },\n    markNotificationAsRead: (state, action) => {\n      const notification = state.notifications.find(n => n.id === action.payload);\n      if (notification) {\n        notification.read = true;\n      }\n    },\n    markAllNotificationsAsRead: state => {\n      state.notifications.forEach(n => n.read = true);\n    },\n    clearNotifications: state => {\n      state.notifications = [];\n    },\n    setLoading: (state, action) => {\n      state.loading[action.payload.key] = action.payload.loading;\n    },\n    setGlobalLoading: (state, action) => {\n      state.loading.global = action.payload;\n    },\n    openModal: (state, action) => {\n      state.modals[action.payload] = true;\n    },\n    closeModal: (state, action) => {\n      state.modals[action.payload] = false;\n    },\n    toggleModal: (state, action) => {\n      state.modals[action.payload] = !state.modals[action.payload];\n    },\n    setBreadcrumbs: (state, action) => {\n      state.breadcrumbs = action.payload;\n    },\n    setPageTitle: (state, action) => {\n      state.pageTitle = action.payload;\n      document.title = `${action.payload} - AMPD Livestock Management`;\n    }\n  }\n});\nexport const {\n  toggleSidebar,\n  setSidebarOpen,\n  setTheme,\n  setLanguage,\n  addNotification,\n  removeNotification,\n  markNotificationAsRead,\n  markAllNotificationsAsRead,\n  clearNotifications,\n  setLoading,\n  setGlobalLoading,\n  openModal,\n  closeModal,\n  toggleModal,\n  setBreadcrumbs,\n  setPageTitle\n} = uiSlice.actions;\nexport default uiSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "sidebarOpen", "theme", "language", "localStorage", "getItem", "notifications", "loading", "global", "modals", "breadcrumbs", "pageTitle", "uiSlice", "name", "reducers", "toggleSidebar", "state", "setSidebarOpen", "action", "payload", "setTheme", "setItem", "setLanguage", "addNotification", "notification", "id", "Date", "now", "toString", "timestamp", "toISOString", "read", "unshift", "length", "slice", "removeNotification", "filter", "n", "markNotificationAsRead", "find", "markAllNotificationsAsRead", "for<PERSON>ach", "clearNotifications", "setLoading", "key", "setGlobalLoading", "openModal", "closeModal", "toggleModal", "setBreadcrumbs", "setPageTitle", "document", "title", "actions", "reducer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/store/slices/uiSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\n\nexport interface Notification {\n  id: string;\n  type: 'success' | 'error' | 'warning' | 'info';\n  title: string;\n  message: string;\n  duration?: number;\n  timestamp: string;\n  read: boolean;\n}\n\nexport interface UIState {\n  sidebarOpen: boolean;\n  theme: 'light' | 'dark';\n  language: 'en' | 'af' | 'st' | 'tn' | 'zu';\n  notifications: Notification[];\n  loading: {\n    global: boolean;\n    [key: string]: boolean;\n  };\n  modals: {\n    [key: string]: boolean;\n  };\n  breadcrumbs: Array<{\n    label: string;\n    path?: string;\n  }>;\n  pageTitle: string;\n}\n\nconst initialState: UIState = {\n  sidebarOpen: true,\n  theme: 'light',\n  language: (localStorage.getItem('language') as any) || 'en',\n  notifications: [],\n  loading: {\n    global: false,\n  },\n  modals: {},\n  breadcrumbs: [],\n  pageTitle: 'Dashboard',\n};\n\nconst uiSlice = createSlice({\n  name: 'ui',\n  initialState,\n  reducers: {\n    toggleSidebar: (state) => {\n      state.sidebarOpen = !state.sidebarOpen;\n    },\n    setSidebarOpen: (state, action: PayloadAction<boolean>) => {\n      state.sidebarOpen = action.payload;\n    },\n    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {\n      state.theme = action.payload;\n      localStorage.setItem('theme', action.payload);\n    },\n    setLanguage: (state, action: PayloadAction<'en' | 'af' | 'st' | 'tn' | 'zu'>) => {\n      state.language = action.payload;\n      localStorage.setItem('language', action.payload);\n    },\n    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'timestamp' | 'read'>>) => {\n      const notification: Notification = {\n        ...action.payload,\n        id: Date.now().toString(),\n        timestamp: new Date().toISOString(),\n        read: false,\n      };\n      state.notifications.unshift(notification);\n      \n      // Keep only last 50 notifications\n      if (state.notifications.length > 50) {\n        state.notifications = state.notifications.slice(0, 50);\n      }\n    },\n    removeNotification: (state, action: PayloadAction<string>) => {\n      state.notifications = state.notifications.filter(n => n.id !== action.payload);\n    },\n    markNotificationAsRead: (state, action: PayloadAction<string>) => {\n      const notification = state.notifications.find(n => n.id === action.payload);\n      if (notification) {\n        notification.read = true;\n      }\n    },\n    markAllNotificationsAsRead: (state) => {\n      state.notifications.forEach(n => n.read = true);\n    },\n    clearNotifications: (state) => {\n      state.notifications = [];\n    },\n    setLoading: (state, action: PayloadAction<{ key: string; loading: boolean }>) => {\n      state.loading[action.payload.key] = action.payload.loading;\n    },\n    setGlobalLoading: (state, action: PayloadAction<boolean>) => {\n      state.loading.global = action.payload;\n    },\n    openModal: (state, action: PayloadAction<string>) => {\n      state.modals[action.payload] = true;\n    },\n    closeModal: (state, action: PayloadAction<string>) => {\n      state.modals[action.payload] = false;\n    },\n    toggleModal: (state, action: PayloadAction<string>) => {\n      state.modals[action.payload] = !state.modals[action.payload];\n    },\n    setBreadcrumbs: (state, action: PayloadAction<UIState['breadcrumbs']>) => {\n      state.breadcrumbs = action.payload;\n    },\n    setPageTitle: (state, action: PayloadAction<string>) => {\n      state.pageTitle = action.payload;\n      document.title = `${action.payload} - AMPD Livestock Management`;\n    },\n  },\n});\n\nexport const {\n  toggleSidebar,\n  setSidebarOpen,\n  setTheme,\n  setLanguage,\n  addNotification,\n  removeNotification,\n  markNotificationAsRead,\n  markAllNotificationsAsRead,\n  clearNotifications,\n  setLoading,\n  setGlobalLoading,\n  openModal,\n  closeModal,\n  toggleModal,\n  setBreadcrumbs,\n  setPageTitle,\n} = uiSlice.actions;\n\nexport default uiSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AA+B7D,MAAMC,YAAqB,GAAG;EAC5BC,WAAW,EAAE,IAAI;EACjBC,KAAK,EAAE,OAAO;EACdC,QAAQ,EAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,IAAY,IAAI;EAC3DC,aAAa,EAAE,EAAE;EACjBC,OAAO,EAAE;IACPC,MAAM,EAAE;EACV,CAAC;EACDC,MAAM,EAAE,CAAC,CAAC;EACVC,WAAW,EAAE,EAAE;EACfC,SAAS,EAAE;AACb,CAAC;AAED,MAAMC,OAAO,GAAGb,WAAW,CAAC;EAC1Bc,IAAI,EAAE,IAAI;EACVb,YAAY;EACZc,QAAQ,EAAE;IACRC,aAAa,EAAGC,KAAK,IAAK;MACxBA,KAAK,CAACf,WAAW,GAAG,CAACe,KAAK,CAACf,WAAW;IACxC,CAAC;IACDgB,cAAc,EAAEA,CAACD,KAAK,EAAEE,MAA8B,KAAK;MACzDF,KAAK,CAACf,WAAW,GAAGiB,MAAM,CAACC,OAAO;IACpC,CAAC;IACDC,QAAQ,EAAEA,CAACJ,KAAK,EAAEE,MAAuC,KAAK;MAC5DF,KAAK,CAACd,KAAK,GAAGgB,MAAM,CAACC,OAAO;MAC5Bf,YAAY,CAACiB,OAAO,CAAC,OAAO,EAAEH,MAAM,CAACC,OAAO,CAAC;IAC/C,CAAC;IACDG,WAAW,EAAEA,CAACN,KAAK,EAAEE,MAAuD,KAAK;MAC/EF,KAAK,CAACb,QAAQ,GAAGe,MAAM,CAACC,OAAO;MAC/Bf,YAAY,CAACiB,OAAO,CAAC,UAAU,EAAEH,MAAM,CAACC,OAAO,CAAC;IAClD,CAAC;IACDI,eAAe,EAAEA,CAACP,KAAK,EAAEE,MAAsE,KAAK;MAClG,MAAMM,YAA0B,GAAG;QACjC,GAAGN,MAAM,CAACC,OAAO;QACjBM,EAAE,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;QACzBC,SAAS,EAAE,IAAIH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC;QACnCC,IAAI,EAAE;MACR,CAAC;MACDf,KAAK,CAACV,aAAa,CAAC0B,OAAO,CAACR,YAAY,CAAC;;MAEzC;MACA,IAAIR,KAAK,CAACV,aAAa,CAAC2B,MAAM,GAAG,EAAE,EAAE;QACnCjB,KAAK,CAACV,aAAa,GAAGU,KAAK,CAACV,aAAa,CAAC4B,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC;MACxD;IACF,CAAC;IACDC,kBAAkB,EAAEA,CAACnB,KAAK,EAAEE,MAA6B,KAAK;MAC5DF,KAAK,CAACV,aAAa,GAAGU,KAAK,CAACV,aAAa,CAAC8B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACZ,EAAE,KAAKP,MAAM,CAACC,OAAO,CAAC;IAChF,CAAC;IACDmB,sBAAsB,EAAEA,CAACtB,KAAK,EAAEE,MAA6B,KAAK;MAChE,MAAMM,YAAY,GAAGR,KAAK,CAACV,aAAa,CAACiC,IAAI,CAACF,CAAC,IAAIA,CAAC,CAACZ,EAAE,KAAKP,MAAM,CAACC,OAAO,CAAC;MAC3E,IAAIK,YAAY,EAAE;QAChBA,YAAY,CAACO,IAAI,GAAG,IAAI;MAC1B;IACF,CAAC;IACDS,0BAA0B,EAAGxB,KAAK,IAAK;MACrCA,KAAK,CAACV,aAAa,CAACmC,OAAO,CAACJ,CAAC,IAAIA,CAAC,CAACN,IAAI,GAAG,IAAI,CAAC;IACjD,CAAC;IACDW,kBAAkB,EAAG1B,KAAK,IAAK;MAC7BA,KAAK,CAACV,aAAa,GAAG,EAAE;IAC1B,CAAC;IACDqC,UAAU,EAAEA,CAAC3B,KAAK,EAAEE,MAAwD,KAAK;MAC/EF,KAAK,CAACT,OAAO,CAACW,MAAM,CAACC,OAAO,CAACyB,GAAG,CAAC,GAAG1B,MAAM,CAACC,OAAO,CAACZ,OAAO;IAC5D,CAAC;IACDsC,gBAAgB,EAAEA,CAAC7B,KAAK,EAAEE,MAA8B,KAAK;MAC3DF,KAAK,CAACT,OAAO,CAACC,MAAM,GAAGU,MAAM,CAACC,OAAO;IACvC,CAAC;IACD2B,SAAS,EAAEA,CAAC9B,KAAK,EAAEE,MAA6B,KAAK;MACnDF,KAAK,CAACP,MAAM,CAACS,MAAM,CAACC,OAAO,CAAC,GAAG,IAAI;IACrC,CAAC;IACD4B,UAAU,EAAEA,CAAC/B,KAAK,EAAEE,MAA6B,KAAK;MACpDF,KAAK,CAACP,MAAM,CAACS,MAAM,CAACC,OAAO,CAAC,GAAG,KAAK;IACtC,CAAC;IACD6B,WAAW,EAAEA,CAAChC,KAAK,EAAEE,MAA6B,KAAK;MACrDF,KAAK,CAACP,MAAM,CAACS,MAAM,CAACC,OAAO,CAAC,GAAG,CAACH,KAAK,CAACP,MAAM,CAACS,MAAM,CAACC,OAAO,CAAC;IAC9D,CAAC;IACD8B,cAAc,EAAEA,CAACjC,KAAK,EAAEE,MAA6C,KAAK;MACxEF,KAAK,CAACN,WAAW,GAAGQ,MAAM,CAACC,OAAO;IACpC,CAAC;IACD+B,YAAY,EAAEA,CAAClC,KAAK,EAAEE,MAA6B,KAAK;MACtDF,KAAK,CAACL,SAAS,GAAGO,MAAM,CAACC,OAAO;MAChCgC,QAAQ,CAACC,KAAK,GAAG,GAAGlC,MAAM,CAACC,OAAO,8BAA8B;IAClE;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACXJ,aAAa;EACbE,cAAc;EACdG,QAAQ;EACRE,WAAW;EACXC,eAAe;EACfY,kBAAkB;EAClBG,sBAAsB;EACtBE,0BAA0B;EAC1BE,kBAAkB;EAClBC,UAAU;EACVE,gBAAgB;EAChBC,SAAS;EACTC,UAAU;EACVC,WAAW;EACXC,cAAc;EACdC;AACF,CAAC,GAAGtC,OAAO,CAACyC,OAAO;AAEnB,eAAezC,OAAO,CAAC0C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}