import React, { useState, useEffect } from 'react';
import { CustomButton } from '../../components/common';
import { useParams, useNavigate } from 'react-router-dom';
import { Typography, Paper, Grid, Tabs, Tab, Box, Chip, Divider, List, ListItem, ListItemText, Card, CardContent, CircularProgress } from '@mui/material';
import {
  ArrowBack,
  Edit,
  Delete,
  Print,
  Share,
  MedicalServices,
  Restaurant,
  Pets,
  MonetizationOn,
  EventNote,
  Assessment
} from '../../utils/iconImports';
import { motion } from 'framer-motion';
import { useTheme, alpha } from '@mui/material/styles';
import { mockAnimals } from '../../mocks/animalData';
import { useMongoAnimalData } from '../../hooks/useMongoAnimalData';
import { useMongoDb } from '../../hooks/useMongoDb';
import { mockHealthRecords } from '../../mocks/healthData';
import { mockFeedingRecords } from '../../mocks/feedingData';
import { mockBreedingRecords } from '../../mocks/breedingData';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`animal-tabpanel-${index}`}
      aria-labelledby={`animal-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const AnimalDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [tabValue, setTabValue] = useState(0);
  const [animal, setAnimal] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const theme = useTheme();

  // Use MongoDB if connected, otherwise use mock data
  const { isConnected } = useMongoDb();
  const { getAnimalById } = useMongoAnimalData();

  useEffect(() => {
    const fetchAnimal = async () => {
      try {
        let foundAnimal = null;

        if (isConnected) {
          // Try to fetch from MongoDB
          foundAnimal = await getAnimalById(id || '');
        }

        // Fall back to mock data if MongoDB fetch fails or not connected
        if (!foundAnimal) {
          foundAnimal = mockAnimals.find(a => a.id.toString() === id);
        }

        setAnimal(foundAnimal || null);
      } catch (error) {
        console.error('Error fetching animal details:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAnimal();
  }, [id, isConnected, getAnimalById]);

  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const { deleteAnimal } = useMongoAnimalData();

  const handleEdit = () => {
    navigate(`/animals/edit/${id}`);
  };

  const handleDelete = async () => {
    if (window.confirm('Are you sure you want to delete this animal?')) {
      try {
        setLoading(true);
        if (isConnected) {
          await deleteAnimal(id || '');
        } else {
          // Simulate API call with mock data
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
        navigate('/animals');
      } catch (error) {
        console.error('Error deleting animal:', error);
        alert('Failed to delete animal. Please try again.');
        setLoading(false);
      }
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (!animal) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <Typography variant="h6" color="text.secondary">Animal not found</Typography>
      </Box>
    );
  }

  // Calculate age from birthDate
  const calculateAge = (birthDate?: Date | string) => {
    if (!birthDate) return 'Unknown';

    const birth = new Date(birthDate);
    const ageInMonths = Math.floor(
      (new Date().getTime() - birth.getTime()) / (1000 * 60 * 60 * 24 * 30.44)
    );
    return ageInMonths < 12 ? `${ageInMonths} months` : `${Math.floor(ageInMonths / 12)} years`;
  };

  // Filter related records
  const healthRecords = mockHealthRecords.filter(record => record.animalId === animal.id);
  // Fix for the 'includes' error - check if animals array exists and contains the animal id
  const feedingRecords = mockFeedingRecords.filter(record =>
    record.animals && Array.isArray(record.animals) && record.animals.includes(animal.id)
  );
  const breedingRecords = mockBreedingRecords.filter(record =>
    record.femaleId === animal.id || record.maleId === animal.id
  );

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.5 }}
      className="p-4"
    >
      <Box sx={{ mb: 4 }}>
        <CustomButton
          startIcon={<ArrowBack />}
          onClick={() => navigate('/animals')}
          variant="outlined"
          sx={{ mb: 2 }}
        >
          Back to Animals
        </CustomButton>

        <Paper sx={{
          p: 3,
          mb: 3,
          borderRadius: 2,
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          backgroundColor: alpha(theme.palette.primary.main, 0.03)
        }}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={8}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Typography variant="h4" component="h1" sx={{ mr: 2 }}>
                  {animal.name}
                </Typography>
                <Chip
                  label={animal.status}
                  color={
                    animal.status === 'Active' ? 'success' :
                    animal.status === 'Sold' ? 'info' :
                    animal.status === 'Deceased' ? 'error' : 'default'
                  }
                  sx={{ mr: 1 }}
                />
                <Chip label={`Tag: ${animal.tagNumber}`} variant="outlined" />
              </Box>

              <Typography variant="subtitle1" color="text.secondary" gutterBottom>
                {animal.breed} {animal.species} • {animal.gender}
              </Typography>

              <Grid container spacing={2} sx={{ mt: 2 }}>
                <Grid item xs={6} sm={3}>
                  <Typography variant="body2" color="text.secondary">Age</Typography>
                  <Typography variant="body1">
                    {animal.birthDate ? calculateAge(animal.birthDate) : 'Unknown'}
                  </Typography>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Typography variant="body2" color="text.secondary">Weight</Typography>
                  <Typography variant="body1">{animal.weight} kg</Typography>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Typography variant="body2" color="text.secondary">Location</Typography>
                  <Typography variant="body1">{animal.location}</Typography>
                </Grid>
                <Grid item xs={6} sm={3}>
                  <Typography variant="body2" color="text.secondary">Purchase Date</Typography>
                  <Typography variant="body1">{animal.purchaseDate ? new Date(animal.purchaseDate).toLocaleDateString() : 'Unknown'}</Typography>
                </Grid>
              </Grid>
            </Grid>

            <Grid item xs={12} md={4} sx={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'flex-start' }}>
              <Box>
                <CustomButton
                  variant="contained"
                  startIcon={<Edit />}
                  onClick={handleEdit}
                  sx={{ mr: 1 }}
                >
                  Edit
                </CustomButton>
                <CustomButton
                  variant="outlined"
                  startIcon={<Print />}
                  sx={{ mr: 1 }}
                >
                  Print
                </CustomButton>
                <CustomButton
                  variant="outlined"
                  startIcon={<Share />}
                  sx={{ mr: 1 }}
                >
                  Share
                </CustomButton>
                <CustomButton
                  variant="outlined"
                  color="error"
                  startIcon={<Delete />}
                  onClick={handleDelete}
                >
                  Delete
                </CustomButton>
              </Box>
            </Grid>
          </Grid>
        </Paper>

        <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
          <Tabs value={tabValue} onChange={handleTabChange} aria-label="animal detail tabs">
            <Tab label="Overview" />
            <Tab label="Health" icon={<MedicalServices fontSize="small" />} iconPosition="start" />
            <Tab label="Feeding" icon={<Restaurant fontSize="small" />} iconPosition="start" />
            <Tab label="Breeding" icon={<Pets fontSize="small" />} iconPosition="start" />
            <Tab label="Financial" icon={<MonetizationOn fontSize="small" />} iconPosition="start" />
            <Tab label="Asset Management" icon={<Assessment fontSize="small" />} iconPosition="start" />
            <Tab label="Notes" icon={<EventNote fontSize="small" />} iconPosition="start" />
          </Tabs>
        </Box>

        <TabPanel value={tabValue} index={0}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card sx={{
                mb: 2,
                borderRadius: 2,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                backgroundColor: alpha(theme.palette.success.main, 0.03),
                borderLeft: `4px solid ${theme.palette.success.main}`
              }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>Details</Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Species</Typography>
                      <Typography variant="body1">{animal.species}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Breed</Typography>
                      <Typography variant="body1">{animal.breed}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Gender</Typography>
                      <Typography variant="body1">{animal.gender}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Date of Birth</Typography>
                      <Typography variant="body1">{animal.birthDate ? new Date(animal.birthDate).toLocaleDateString() : 'Unknown'}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Weight</Typography>
                      <Typography variant="body1">{animal.weight} kg</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Status</Typography>
                      <Typography variant="body1">{animal.status}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Location</Typography>
                      <Typography variant="body1">{animal.location}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Tag Number</Typography>
                      <Typography variant="body1">{animal.tagNumber}</Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              <Card sx={{
                borderRadius: 2,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                backgroundColor: alpha(theme.palette.info.main, 0.03),
                borderLeft: `4px solid ${theme.palette.info.main}`
              }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>Purchase Information</Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Purchase Date</Typography>
                      <Typography variant="body1">{animal.purchaseDate ? new Date(animal.purchaseDate).toLocaleDateString() : 'Unknown'}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Purchase Price</Typography>
                      <Typography variant="body1">${animal.purchasePrice}</Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card sx={{
                mb: 2,
                borderRadius: 2,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                backgroundColor: alpha(theme.palette.warning.main, 0.03),
                borderLeft: `4px solid ${theme.palette.warning.main}`
              }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>Recent Activity</Typography>
                  <Divider sx={{ mb: 2 }} />
                  <List>
                    {healthRecords.slice(0, 3).map((record, index) => (
                      <ListItem key={index} divider={index < 2}>
                        <ListItemText
                          primary={record.type}
                          secondary={`${record.date} - ${record.notes}`}
                        />
                      </ListItem>
                    ))}
                    {healthRecords.length === 0 && (
                      <ListItem>
                        <ListItemText primary="No recent health records" />
                      </ListItem>
                    )}
                  </List>
                </CardContent>
              </Card>

              <Card sx={{
                borderRadius: 2,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                backgroundColor: alpha(theme.palette.error.main, 0.03),
                borderLeft: `4px solid ${theme.palette.error.main}`
              }}>
                <CardContent>
                  <Typography variant="h6" gutterBottom>Notes</Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Typography variant="body1">
                    {animal.notes || "No notes available for this animal."}
                  </Typography>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          <Typography variant="h6" gutterBottom>Health Records</Typography>
          {healthRecords.length > 0 ? (
            <List>
              {healthRecords.map((record, index) => (
                <ListItem key={index} divider={index < healthRecords.length - 1}>
                  <ListItemText
                    primary={record.type}
                    secondary={`${record.date} - ${record.notes}`}
                  />
                </ListItem>
              ))}
            </List>
          ) : (
            <Typography>No health records available</Typography>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          <Typography variant="h6" gutterBottom>Feeding Records</Typography>
          {feedingRecords.length > 0 ? (
            <List>
              {feedingRecords.map((record, index) => (
                <ListItem key={index} divider={index < feedingRecords.length - 1}>
                  <ListItemText
                    primary={`${record.feedType} - ${record.quantity} ${record.unit}`}
                    secondary={`${record.date} - ${record.notes}`}
                  />
                </ListItem>
              ))}
            </List>
          ) : (
            <Typography>No feeding records available</Typography>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={3}>
          <Typography variant="h6" gutterBottom>Breeding Records</Typography>
          {breedingRecords.length > 0 ? (
            <List>
              {breedingRecords.map((record, index) => (
                <ListItem key={index} divider={index < breedingRecords.length - 1}>
                  <ListItemText
                    primary={`Breeding ID: ${record.id} - Status: ${record.status}`}
                    secondary={`Date: ${record.date} - ${record.notes}`}
                  />
                </ListItem>
              ))}
            </List>
          ) : (
            <Typography>No breeding records available</Typography>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={4}>
          <Typography variant="h6" gutterBottom>Financial Information</Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card sx={{
                borderRadius: 2,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                backgroundColor: alpha(theme.palette.primary.main, 0.03),
                borderLeft: `4px solid ${theme.palette.primary.main}`,
                mb: 3
              }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>Purchase Information</Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Purchase Date</Typography>
                      <Typography variant="body1">{animal.purchaseDate ? new Date(animal.purchaseDate).toLocaleDateString() : 'Unknown'}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Purchase Price</Typography>
                      <Typography variant="body1">R {animal.purchasePrice || 0}</Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              <Card sx={{
                borderRadius: 2,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                backgroundColor: alpha(theme.palette.error.main, 0.03),
                borderLeft: `4px solid ${theme.palette.error.main}`
              }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>Expenses</Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Feed Costs</Typography>
                      <Typography variant="body1">R {(animal.maintenanceCosts || 0) * 0.6}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Health Costs</Typography>
                      <Typography variant="body1">R {(animal.maintenanceCosts || 0) * 0.3}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Other Costs</Typography>
                      <Typography variant="body1">R {(animal.maintenanceCosts || 0) * 0.1}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Total Maintenance</Typography>
                      <Typography variant="body1" fontWeight="bold">R {animal.maintenanceCosts || 0}</Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>

            <Grid item xs={12} md={6}>
              <Card sx={{
                borderRadius: 2,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                backgroundColor: alpha(theme.palette.success.main, 0.03),
                borderLeft: `4px solid ${theme.palette.success.main}`,
                mb: 3
              }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>Asset Valuation</Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Initial Value</Typography>
                      <Typography variant="body1">R {animal.purchasePrice || 0}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Current Value</Typography>
                      <Typography variant="body1">R {animal.currentValue || (animal.purchasePrice * 0.9).toFixed(2)}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Depreciation Rate</Typography>
                      <Typography variant="body1">{animal.depreciationRate || 10}% per year</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Last Valuation</Typography>
                      <Typography variant="body1">{new Date().toLocaleDateString()}</Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              <Card sx={{
                borderRadius: 2,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                backgroundColor: alpha(theme.palette.info.main, 0.03),
                borderLeft: `4px solid ${theme.palette.info.main}`
              }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>Revenue & Profitability</Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Revenue Generated</Typography>
                      <Typography variant="body1">R {animal.revenueGenerated || 0}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Total Costs</Typography>
                      <Typography variant="body1">R {(animal.maintenanceCosts || 0) + (animal.purchasePrice || 0)}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Net Profit/Loss</Typography>
                      <Typography
                        variant="body1"
                        color={(animal.revenueGenerated || 0) - ((animal.maintenanceCosts || 0) + (animal.purchasePrice || 0)) >= 0 ? 'success.main' : 'error.main'}
                        fontWeight="bold"
                      >
                        R {(animal.revenueGenerated || 0) - ((animal.maintenanceCosts || 0) + (animal.purchasePrice || 0))}
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">ROI</Typography>
                      <Typography
                        variant="body1"
                        color={(animal.roi || 0) >= 0 ? 'success.main' : 'error.main'}
                        fontWeight="bold"
                      >
                        {animal.roi || 0}%
                      </Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={5}>
          <Typography variant="h6" gutterBottom>Asset Management</Typography>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <Card sx={{
                borderRadius: 2,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                backgroundColor: alpha(theme.palette.primary.main, 0.03),
                borderLeft: `4px solid ${theme.palette.primary.main}`,
                mb: 3
              }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>Asset Information</Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Status</Typography>
                      <Typography variant="body1">
                        <Chip
                          label={animal.status}
                          color={
                            animal.status === 'Active' ? 'success' :
                            animal.status === 'Retired' ? 'error' :
                            'default'
                          }
                          size="small"
                        />
                      </Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Current Value</Typography>
                      <Typography variant="body1">R {animal.currentValue || (animal.purchasePrice * 0.9).toFixed(2)}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Purchase Price</Typography>
                      <Typography variant="body1">R {animal.purchasePrice || 0}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Depreciation Rate</Typography>
                      <Typography variant="body1">{animal.depreciationRate || 10}%</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Breeding Count</Typography>
                      <Typography variant="body1">{animal.breedingCount || 0}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">ROI</Typography>
                      <Typography variant="body1">{animal.roi || 0}%</Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              {animal.status === 'Retired' && (
                <Card sx={{
                  borderRadius: 2,
                  boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                  backgroundColor: alpha(theme.palette.error.main, 0.03),
                  borderLeft: `4px solid ${theme.palette.error.main}`
                }}>
                  <CardContent>
                    <Typography variant="subtitle1" gutterBottom>Retirement Information</Typography>
                    <Divider sx={{ mb: 2 }} />
                    <Grid container spacing={2}>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">Retirement Date</Typography>
                        <Typography variant="body1">{animal.retirementDate ? new Date(animal.retirementDate).toLocaleDateString() : 'N/A'}</Typography>
                      </Grid>
                      <Grid item xs={6}>
                        <Typography variant="body2" color="text.secondary">Retirement Reason</Typography>
                        <Typography variant="body1">{animal.retirementReason || 'N/A'}</Typography>
                      </Grid>
                      <Grid item xs={12}>
                        <Typography variant="body2" color="text.secondary">Retirement Notes</Typography>
                        <Typography variant="body1">{animal.retirementNotes || 'No notes available'}</Typography>
                      </Grid>
                    </Grid>
                  </CardContent>
                </Card>
              )}
            </Grid>

            <Grid item xs={12} md={6}>
              <Card sx={{
                borderRadius: 2,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                backgroundColor: alpha(theme.palette.success.main, 0.03),
                borderLeft: `4px solid ${theme.palette.success.main}`,
                mb: 3
              }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>Financial Performance</Typography>
                  <Divider sx={{ mb: 2 }} />
                  <Grid container spacing={2}>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Maintenance Costs</Typography>
                      <Typography variant="body1">R {animal.maintenanceCosts || 0}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Revenue Generated</Typography>
                      <Typography variant="body1">R {animal.revenueGenerated || 0}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">Net Profit</Typography>
                      <Typography variant="body1">R {(animal.revenueGenerated || 0) - (animal.maintenanceCosts || 0)}</Typography>
                    </Grid>
                    <Grid item xs={6}>
                      <Typography variant="body2" color="text.secondary">ROI</Typography>
                      <Typography variant="body1">{animal.roi || 0}%</Typography>
                    </Grid>
                  </Grid>
                </CardContent>
              </Card>

              <Card sx={{
                borderRadius: 2,
                boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
                backgroundColor: alpha(theme.palette.warning.main, 0.03),
                borderLeft: `4px solid ${theme.palette.warning.main}`
              }}>
                <CardContent>
                  <Typography variant="subtitle1" gutterBottom>Retirement Eligibility</Typography>
                  <Divider sx={{ mb: 2 }} />
                  {animal.status !== 'Retired' ? (
                    <>
                      <Grid container spacing={2}>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">Age</Typography>
                          <Typography variant="body1">{calculateAge(animal.birthDate)}</Typography>
                        </Grid>
                        <Grid item xs={6}>
                          <Typography variant="body2" color="text.secondary">Breeding Count</Typography>
                          <Typography variant="body1">{animal.breedingCount || 0}</Typography>
                        </Grid>
                        <Grid item xs={12}>
                          <Typography variant="body2" color="text.secondary">Retirement Status</Typography>
                          <Box sx={{ mt: 1 }}>
                            {calculateAge(animal.birthDate).includes('years') && parseInt(calculateAge(animal.birthDate)) >= 10 ? (
                              <Chip label="Eligible for Retirement (Age)" color="error" sx={{ mr: 1 }} />
                            ) : (animal.breedingCount || 0) >= 8 ? (
                              <Chip label="Eligible for Retirement (Breeding)" color="error" sx={{ mr: 1 }} />
                            ) : (
                              <Chip label="Not Eligible for Retirement" color="success" />
                            )}
                          </Box>
                        </Grid>
                      </Grid>
                      <Box sx={{ mt: 2 }}>
                        <CustomButton
                          variant="contained"
                          color="primary"
                          disabled={!(calculateAge(animal.birthDate).includes('years') && parseInt(calculateAge(animal.birthDate)) >= 10) && !((animal.breedingCount || 0) >= 8)}
                          onClick={() => navigate(`/animals/asset-management/retirement?id=${animal.id}`)}
                        >
                          Retire Animal
                        </CustomButton>
                      </Box>
                    </>
                  ) : (
                    <Typography>This animal is already retired.</Typography>
                  )}
                </CardContent>
              </Card>
            </Grid>
          </Grid>
        </TabPanel>

        <TabPanel value={tabValue} index={6}>
          <Typography variant="h6" gutterBottom>Notes</Typography>
          <Paper sx={{
            p: 3,
            borderRadius: 2,
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            backgroundColor: alpha(theme.palette.info.main, 0.03),
            borderLeft: `4px solid ${theme.palette.info.main}`
          }}>
            <Typography>{animal.notes || "No notes available for this animal."}</Typography>
          </Paper>
        </TabPanel>
      </Box>
    </motion.div>
  );
};

export default AnimalDetail;
