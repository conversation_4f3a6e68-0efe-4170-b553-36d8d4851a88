{"ast": null, "code": "import React,{useEffect}from'react';import{useDispatch,useSelector}from'react-redux';import{useTranslation}from'react-i18next';import{ChartBarIcon,HeartIcon,ExclamationTriangleIcon,CalendarIcon}from'@heroicons/react/24/outline';import{fetchAnimalStatistics}from'../../store/slices/animalSlice';import{setPageTitle,setBreadcrumbs}from'../../store/slices/uiSlice';// Dashboard Components\nimport DashboardWidget from'../../components/dashboard/DashboardWidget';import StatCard from'../../components/dashboard/StatCard';import ActivityFeed from'../../components/dashboard/ActivityFeed';import{jsxs as _jsxs,jsx as _jsx}from\"react/jsx-runtime\";const DashboardPage=()=>{var _statistics$byHealthS,_statistics$byBreedin;const dispatch=useDispatch();const{t}=useTranslation();const{user}=useSelector(state=>state.auth);const{statistics}=useSelector(state=>state.animals);useEffect(()=>{dispatch(setPageTitle(t('dashboard.title')));dispatch(setBreadcrumbs([{label:t('dashboard.title')}]));// Fetch dashboard data\ndispatch(fetchAnimalStatistics());},[dispatch,t]);const stats=[{name:t('dashboard.totalAnimals'),value:(statistics===null||statistics===void 0?void 0:statistics.total)||0,icon:ChartBarIcon,color:'bg-blue-500',change:'+12%',changeType:'positive'},{name:t('dashboard.healthyAnimals'),value:(statistics===null||statistics===void 0?void 0:(_statistics$byHealthS=statistics.byHealthStatus)===null||_statistics$byHealthS===void 0?void 0:_statistics$byHealthS.healthy)||0,icon:HeartIcon,color:'bg-green-500',change:'+2%',changeType:'positive'},{name:t('dashboard.pregnantAnimals'),value:(statistics===null||statistics===void 0?void 0:(_statistics$byBreedin=statistics.byBreedingStatus)===null||_statistics$byBreedin===void 0?void 0:_statistics$byBreedin.pregnant)||0,icon:CalendarIcon,color:'bg-purple-500',change:'+5%',changeType:'positive'},{name:t('dashboard.healthAlerts'),value:8,icon:ExclamationTriangleIcon,color:'bg-red-500',change:'-3%',changeType:'negative'}];return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"bg-white rounded-lg shadow-soft p-6\",children:[/*#__PURE__*/_jsxs(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:[t('dashboard.welcome'),\", \",user===null||user===void 0?void 0:user.firstName,\"!\"]}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-gray-600\",children:\"Here's what's happening with your livestock today.\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\",children:stats.map(stat=>/*#__PURE__*/_jsx(StatCard,{name:stat.name,value:stat.value,icon:stat.icon,color:stat.color,change:stat.change,changeType:stat.changeType},stat.name))}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 gap-5 lg:grid-cols-2\",children:[/*#__PURE__*/_jsx(DashboardWidget,{title:t('dashboard.recentActivity'),children:/*#__PURE__*/_jsx(ActivityFeed,{maxItems:6})}),/*#__PURE__*/_jsx(DashboardWidget,{title:t('dashboard.alerts'),children:/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 p-3 border-l-4 border-l-red-500 bg-red-50 rounded-r-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 bg-red-400 rounded-full flex-shrink-0\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-900 font-medium\",children:\"Vaccination overdue for 3 animals\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-red-600\",children:\"High priority \\u2022 Immediate action required\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 p-3 border-l-4 border-l-yellow-500 bg-yellow-50 rounded-r-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 bg-orange-400 rounded-full flex-shrink-0\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-900 font-medium\",children:\"Feed inventory running low\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-yellow-600\",children:\"Medium priority \\u2022 Order within 3 days\"})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3 p-3 border-l-4 border-l-blue-500 bg-blue-50 rounded-r-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 bg-blue-400 rounded-full flex-shrink-0\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-900 font-medium\",children:\"Breeding season approaching\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-blue-600\",children:\"Low priority \\u2022 Plan for next month\"})]})]})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 gap-5 lg:grid-cols-2\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:t('dashboard.animalsBySpecies')})}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:/*#__PURE__*/_jsx(\"div\",{className:\"h-64\",children:statistics!==null&&statistics!==void 0&&statistics.bySpecies?/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:Object.entries(statistics.bySpecies).map(_ref=>{let[species,count]=_ref;const percentage=(count/statistics.total*100).toFixed(1);return/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 rounded-full \".concat(species==='cattle'?'bg-blue-500':species==='sheep'?'bg-green-500':species==='goat'?'bg-yellow-500':'bg-purple-500')}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600 capitalize\",children:species})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-900\",children:count}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-xs text-gray-500 ml-2\",children:[\"(\",percentage,\"%)\"]})]})]},species);})}):/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-full text-gray-500\",children:\"Loading chart data...\"})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:t('dashboard.healthStatusOverview')})}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:/*#__PURE__*/_jsx(\"div\",{className:\"h-64\",children:statistics!==null&&statistics!==void 0&&statistics.byHealthStatus?/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:Object.entries(statistics.byHealthStatus).map(_ref2=>{let[status,count]=_ref2;const percentage=(count/statistics.total*100).toFixed(1);return/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-3\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-3 h-3 rounded-full \".concat(status==='healthy'?'bg-green-500':status==='sick'?'bg-red-500':status==='injured'?'bg-orange-500':'bg-blue-500')}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600 capitalize\",children:status})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-right\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-900\",children:count}),/*#__PURE__*/_jsxs(\"span\",{className:\"text-xs text-gray-500 ml-2\",children:[\"(\",percentage,\"%)\"]})]})]},status);})}):/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center justify-center h-full text-gray-500\",children:\"Loading chart data...\"})})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"grid grid-cols-1 gap-5 lg:grid-cols-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:t('dashboard.breedingStatus')})}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:/*#__PURE__*/_jsx(\"div\",{className:\"space-y-3\",children:(statistics===null||statistics===void 0?void 0:statistics.byBreedingStatus)&&Object.entries(statistics.byBreedingStatus).map(_ref3=>{let[status,count]=_ref3;return/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600 capitalize\",children:status}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-900\",children:count})]},status);})})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:t('dashboard.monthlyGrowth')})}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"New Births\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-green-600\",children:\"+12\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Acquisitions\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-blue-600\",children:\"+8\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Sales\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-red-600\",children:\"-5\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Net Growth\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-900\",children:\"+15\"})]})]})})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:t('dashboard.financialSummary')})}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-3\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Monthly Revenue\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-green-600\",children:\"R 45,200\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Feed Costs\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-red-600\",children:\"R 18,500\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Vet Costs\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-red-600\",children:\"R 3,200\"})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"span\",{className:\"text-sm text-gray-600\",children:\"Net Profit\"}),/*#__PURE__*/_jsx(\"span\",{className:\"text-sm font-medium text-gray-900\",children:\"R 23,500\"})]})]})})]})]})]});};export default DashboardPage;", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useSelector", "useTranslation", "ChartBarIcon", "HeartIcon", "ExclamationTriangleIcon", "CalendarIcon", "fetchAnimalStatistics", "setPageTitle", "setBreadcrumbs", "DashboardWidget", "StatCard", "ActivityFeed", "jsxs", "_jsxs", "jsx", "_jsx", "DashboardPage", "_statistics$byHealthS", "_statistics$byBreedin", "dispatch", "t", "user", "state", "auth", "statistics", "animals", "label", "stats", "name", "value", "total", "icon", "color", "change", "changeType", "byHealthStatus", "healthy", "byBreedingStatus", "pregnant", "className", "children", "firstName", "map", "stat", "title", "maxItems", "bySpecies", "Object", "entries", "_ref", "species", "count", "percentage", "toFixed", "concat", "_ref2", "status", "_ref3"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/pages/dashboard/DashboardPage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport {\n  ChartBarIcon,\n  HeartIcon,\n  ExclamationTriangleIcon,\n  CalendarIcon,\n  CurrencyDollarIcon,\n  ArrowTrendingUpIcon,\n} from '@heroicons/react/24/outline';\n\nimport { AppDispatch, RootState } from '../../store/store';\nimport { fetchAnimalStatistics } from '../../store/slices/animalSlice';\nimport { setPageTitle, setBreadcrumbs } from '../../store/slices/uiSlice';\n\n// Dashboard Components\nimport DashboardWidget from '../../components/dashboard/DashboardWidget';\nimport StatCard from '../../components/dashboard/StatCard';\nimport ActivityFeed from '../../components/dashboard/ActivityFeed';\n\nconst DashboardPage: React.FC = () => {\n  const dispatch = useDispatch<AppDispatch>();\n  const { t } = useTranslation();\n  \n  const { user } = useSelector((state: RootState) => state.auth);\n  const { statistics } = useSelector((state: RootState) => state.animals);\n\n  useEffect(() => {\n    dispatch(setPageTitle(t('dashboard.title')));\n    dispatch(setBreadcrumbs([\n      { label: t('dashboard.title') }\n    ]));\n    \n    // Fetch dashboard data\n    dispatch(fetchAnimalStatistics());\n  }, [dispatch, t]);\n\n  const stats = [\n    {\n      name: t('dashboard.totalAnimals'),\n      value: statistics?.total || 0,\n      icon: ChartBarIcon,\n      color: 'bg-blue-500',\n      change: '+12%',\n      changeType: 'positive' as const,\n    },\n    {\n      name: t('dashboard.healthyAnimals'),\n      value: statistics?.byHealthStatus?.healthy || 0,\n      icon: HeartIcon,\n      color: 'bg-green-500',\n      change: '+2%',\n      changeType: 'positive' as const,\n    },\n    {\n      name: t('dashboard.pregnantAnimals'),\n      value: statistics?.byBreedingStatus?.pregnant || 0,\n      icon: CalendarIcon,\n      color: 'bg-purple-500',\n      change: '+5%',\n      changeType: 'positive' as const,\n    },\n    {\n      name: t('dashboard.healthAlerts'),\n      value: 8,\n      icon: ExclamationTriangleIcon,\n      color: 'bg-red-500',\n      change: '-3%',\n      changeType: 'negative' as const,\n    },\n  ];\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Section */}\n      <div className=\"bg-white rounded-lg shadow-soft p-6\">\n        <h1 className=\"text-2xl font-bold text-gray-900\">\n          {t('dashboard.welcome')}, {user?.firstName}!\n        </h1>\n        <p className=\"mt-2 text-gray-600\">\n          Here's what's happening with your livestock today.\n        </p>\n      </div>\n\n      {/* Stats Grid */}\n      <div className=\"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4\">\n        {stats.map((stat) => (\n          <StatCard\n            key={stat.name}\n            name={stat.name}\n            value={stat.value}\n            icon={stat.icon}\n            color={stat.color}\n            change={stat.change}\n            changeType={stat.changeType}\n          />\n        ))}\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"grid grid-cols-1 gap-5 lg:grid-cols-2\">\n        {/* Recent Activity */}\n        <DashboardWidget title={t('dashboard.recentActivity')}>\n          <ActivityFeed maxItems={6} />\n        </DashboardWidget>\n\n        {/* Alerts */}\n        <DashboardWidget title={t('dashboard.alerts')}>\n          <div className=\"space-y-4\">\n            <div className=\"flex items-center space-x-3 p-3 border-l-4 border-l-red-500 bg-red-50 rounded-r-md\">\n              <div className=\"w-2 h-2 bg-red-400 rounded-full flex-shrink-0\"></div>\n              <div className=\"flex-1\">\n                <p className=\"text-sm text-gray-900 font-medium\">Vaccination overdue for 3 animals</p>\n                <p className=\"text-xs text-red-600\">High priority • Immediate action required</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-3 p-3 border-l-4 border-l-yellow-500 bg-yellow-50 rounded-r-md\">\n              <div className=\"w-2 h-2 bg-orange-400 rounded-full flex-shrink-0\"></div>\n              <div className=\"flex-1\">\n                <p className=\"text-sm text-gray-900 font-medium\">Feed inventory running low</p>\n                <p className=\"text-xs text-yellow-600\">Medium priority • Order within 3 days</p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-3 p-3 border-l-4 border-l-blue-500 bg-blue-50 rounded-r-md\">\n              <div className=\"w-2 h-2 bg-blue-400 rounded-full flex-shrink-0\"></div>\n              <div className=\"flex-1\">\n                <p className=\"text-sm text-gray-900 font-medium\">Breeding season approaching</p>\n                <p className=\"text-xs text-blue-600\">Low priority • Plan for next month</p>\n              </div>\n            </div>\n          </div>\n        </DashboardWidget>\n      </div>\n\n      {/* Charts Section */}\n      <div className=\"grid grid-cols-1 gap-5 lg:grid-cols-2\">\n        {/* Animals by Species Chart */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"text-lg font-medium text-gray-900\">\n              {t('dashboard.animalsBySpecies')}\n            </h3>\n          </div>\n          <div className=\"card-body\">\n            <div className=\"h-64\">\n              {statistics?.bySpecies ? (\n                <div className=\"space-y-3\">\n                  {Object.entries(statistics.bySpecies).map(([species, count]) => {\n                    const percentage = ((count / statistics.total) * 100).toFixed(1);\n                    return (\n                      <div key={species} className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center space-x-3\">\n                          <div className={`w-3 h-3 rounded-full ${\n                            species === 'cattle' ? 'bg-blue-500' :\n                            species === 'sheep' ? 'bg-green-500' :\n                            species === 'goat' ? 'bg-yellow-500' :\n                            'bg-purple-500'\n                          }`}></div>\n                          <span className=\"text-sm text-gray-600 capitalize\">{species}</span>\n                        </div>\n                        <div className=\"text-right\">\n                          <span className=\"text-sm font-medium text-gray-900\">{count}</span>\n                          <span className=\"text-xs text-gray-500 ml-2\">({percentage}%)</span>\n                        </div>\n                      </div>\n                    );\n                  })}\n                </div>\n              ) : (\n                <div className=\"flex items-center justify-center h-full text-gray-500\">\n                  Loading chart data...\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Health Status Chart */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"text-lg font-medium text-gray-900\">\n              {t('dashboard.healthStatusOverview')}\n            </h3>\n          </div>\n          <div className=\"card-body\">\n            <div className=\"h-64\">\n              {statistics?.byHealthStatus ? (\n                <div className=\"space-y-3\">\n                  {Object.entries(statistics.byHealthStatus).map(([status, count]) => {\n                    const percentage = ((count / statistics.total) * 100).toFixed(1);\n                    return (\n                      <div key={status} className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center space-x-3\">\n                          <div className={`w-3 h-3 rounded-full ${\n                            status === 'healthy' ? 'bg-green-500' :\n                            status === 'sick' ? 'bg-red-500' :\n                            status === 'injured' ? 'bg-orange-500' :\n                            'bg-blue-500'\n                          }`}></div>\n                          <span className=\"text-sm text-gray-600 capitalize\">{status}</span>\n                        </div>\n                        <div className=\"text-right\">\n                          <span className=\"text-sm font-medium text-gray-900\">{count}</span>\n                          <span className=\"text-xs text-gray-500 ml-2\">({percentage}%)</span>\n                        </div>\n                      </div>\n                    );\n                  })}\n                </div>\n              ) : (\n                <div className=\"flex items-center justify-center h-full text-gray-500\">\n                  Loading chart data...\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Additional Analytics Section */}\n      <div className=\"grid grid-cols-1 gap-5 lg:grid-cols-3\">\n        {/* Breeding Status */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"text-lg font-medium text-gray-900\">\n              {t('dashboard.breedingStatus')}\n            </h3>\n          </div>\n          <div className=\"card-body\">\n            <div className=\"space-y-3\">\n              {statistics?.byBreedingStatus && Object.entries(statistics.byBreedingStatus).map(([status, count]) => (\n                <div key={status} className=\"flex items-center justify-between\">\n                  <span className=\"text-sm text-gray-600 capitalize\">{status}</span>\n                  <span className=\"text-sm font-medium text-gray-900\">{count}</span>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Monthly Growth */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"text-lg font-medium text-gray-900\">\n              {t('dashboard.monthlyGrowth')}\n            </h3>\n          </div>\n          <div className=\"card-body\">\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">New Births</span>\n                <span className=\"text-sm font-medium text-green-600\">+12</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">Acquisitions</span>\n                <span className=\"text-sm font-medium text-blue-600\">+8</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">Sales</span>\n                <span className=\"text-sm font-medium text-red-600\">-5</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">Net Growth</span>\n                <span className=\"text-sm font-medium text-gray-900\">+15</span>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Financial Summary */}\n        <div className=\"card\">\n          <div className=\"card-header\">\n            <h3 className=\"text-lg font-medium text-gray-900\">\n              {t('dashboard.financialSummary')}\n            </h3>\n          </div>\n          <div className=\"card-body\">\n            <div className=\"space-y-3\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">Monthly Revenue</span>\n                <span className=\"text-sm font-medium text-green-600\">R 45,200</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">Feed Costs</span>\n                <span className=\"text-sm font-medium text-red-600\">R 18,500</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">Vet Costs</span>\n                <span className=\"text-sm font-medium text-red-600\">R 3,200</span>\n              </div>\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-gray-600\">Net Profit</span>\n                <span className=\"text-sm font-medium text-gray-900\">R 23,500</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default DashboardPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OACEC,YAAY,CACZC,SAAS,CACTC,uBAAuB,CACvBC,YAAY,KAGP,6BAA6B,CAGpC,OAASC,qBAAqB,KAAQ,gCAAgC,CACtE,OAASC,YAAY,CAAEC,cAAc,KAAQ,4BAA4B,CAEzE;AACA,MAAO,CAAAC,eAAe,KAAM,4CAA4C,CACxE,MAAO,CAAAC,QAAQ,KAAM,qCAAqC,CAC1D,MAAO,CAAAC,YAAY,KAAM,yCAAyC,CAAC,OAAAC,IAAA,IAAAC,KAAA,CAAAC,GAAA,IAAAC,IAAA,yBAEnE,KAAM,CAAAC,aAAuB,CAAGA,CAAA,GAAM,KAAAC,qBAAA,CAAAC,qBAAA,CACpC,KAAM,CAAAC,QAAQ,CAAGpB,WAAW,CAAc,CAAC,CAC3C,KAAM,CAAEqB,CAAE,CAAC,CAAGnB,cAAc,CAAC,CAAC,CAE9B,KAAM,CAAEoB,IAAK,CAAC,CAAGrB,WAAW,CAAEsB,KAAgB,EAAKA,KAAK,CAACC,IAAI,CAAC,CAC9D,KAAM,CAAEC,UAAW,CAAC,CAAGxB,WAAW,CAAEsB,KAAgB,EAAKA,KAAK,CAACG,OAAO,CAAC,CAEvE3B,SAAS,CAAC,IAAM,CACdqB,QAAQ,CAACZ,YAAY,CAACa,CAAC,CAAC,iBAAiB,CAAC,CAAC,CAAC,CAC5CD,QAAQ,CAACX,cAAc,CAAC,CACtB,CAAEkB,KAAK,CAAEN,CAAC,CAAC,iBAAiB,CAAE,CAAC,CAChC,CAAC,CAAC,CAEH;AACAD,QAAQ,CAACb,qBAAqB,CAAC,CAAC,CAAC,CACnC,CAAC,CAAE,CAACa,QAAQ,CAAEC,CAAC,CAAC,CAAC,CAEjB,KAAM,CAAAO,KAAK,CAAG,CACZ,CACEC,IAAI,CAAER,CAAC,CAAC,wBAAwB,CAAC,CACjCS,KAAK,CAAE,CAAAL,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEM,KAAK,GAAI,CAAC,CAC7BC,IAAI,CAAE7B,YAAY,CAClB8B,KAAK,CAAE,aAAa,CACpBC,MAAM,CAAE,MAAM,CACdC,UAAU,CAAE,UACd,CAAC,CACD,CACEN,IAAI,CAAER,CAAC,CAAC,0BAA0B,CAAC,CACnCS,KAAK,CAAE,CAAAL,UAAU,SAAVA,UAAU,kBAAAP,qBAAA,CAAVO,UAAU,CAAEW,cAAc,UAAAlB,qBAAA,iBAA1BA,qBAAA,CAA4BmB,OAAO,GAAI,CAAC,CAC/CL,IAAI,CAAE5B,SAAS,CACf6B,KAAK,CAAE,cAAc,CACrBC,MAAM,CAAE,KAAK,CACbC,UAAU,CAAE,UACd,CAAC,CACD,CACEN,IAAI,CAAER,CAAC,CAAC,2BAA2B,CAAC,CACpCS,KAAK,CAAE,CAAAL,UAAU,SAAVA,UAAU,kBAAAN,qBAAA,CAAVM,UAAU,CAAEa,gBAAgB,UAAAnB,qBAAA,iBAA5BA,qBAAA,CAA8BoB,QAAQ,GAAI,CAAC,CAClDP,IAAI,CAAE1B,YAAY,CAClB2B,KAAK,CAAE,eAAe,CACtBC,MAAM,CAAE,KAAK,CACbC,UAAU,CAAE,UACd,CAAC,CACD,CACEN,IAAI,CAAER,CAAC,CAAC,wBAAwB,CAAC,CACjCS,KAAK,CAAE,CAAC,CACRE,IAAI,CAAE3B,uBAAuB,CAC7B4B,KAAK,CAAE,YAAY,CACnBC,MAAM,CAAE,KAAK,CACbC,UAAU,CAAE,UACd,CAAC,CACF,CAED,mBACErB,KAAA,QAAK0B,SAAS,CAAC,WAAW,CAAAC,QAAA,eAExB3B,KAAA,QAAK0B,SAAS,CAAC,qCAAqC,CAAAC,QAAA,eAClD3B,KAAA,OAAI0B,SAAS,CAAC,kCAAkC,CAAAC,QAAA,EAC7CpB,CAAC,CAAC,mBAAmB,CAAC,CAAC,IAAE,CAACC,IAAI,SAAJA,IAAI,iBAAJA,IAAI,CAAEoB,SAAS,CAAC,GAC7C,EAAI,CAAC,cACL1B,IAAA,MAAGwB,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,oDAElC,CAAG,CAAC,EACD,CAAC,cAGNzB,IAAA,QAAKwB,SAAS,CAAC,sDAAsD,CAAAC,QAAA,CAClEb,KAAK,CAACe,GAAG,CAAEC,IAAI,eACd5B,IAAA,CAACL,QAAQ,EAEPkB,IAAI,CAAEe,IAAI,CAACf,IAAK,CAChBC,KAAK,CAAEc,IAAI,CAACd,KAAM,CAClBE,IAAI,CAAEY,IAAI,CAACZ,IAAK,CAChBC,KAAK,CAAEW,IAAI,CAACX,KAAM,CAClBC,MAAM,CAAEU,IAAI,CAACV,MAAO,CACpBC,UAAU,CAAES,IAAI,CAACT,UAAW,EANvBS,IAAI,CAACf,IAOX,CACF,CAAC,CACC,CAAC,cAGNf,KAAA,QAAK0B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEpDzB,IAAA,CAACN,eAAe,EAACmC,KAAK,CAAExB,CAAC,CAAC,0BAA0B,CAAE,CAAAoB,QAAA,cACpDzB,IAAA,CAACJ,YAAY,EAACkC,QAAQ,CAAE,CAAE,CAAE,CAAC,CACd,CAAC,cAGlB9B,IAAA,CAACN,eAAe,EAACmC,KAAK,CAAExB,CAAC,CAAC,kBAAkB,CAAE,CAAAoB,QAAA,cAC5C3B,KAAA,QAAK0B,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3B,KAAA,QAAK0B,SAAS,CAAC,oFAAoF,CAAAC,QAAA,eACjGzB,IAAA,QAAKwB,SAAS,CAAC,+CAA+C,CAAM,CAAC,cACrE1B,KAAA,QAAK0B,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrBzB,IAAA,MAAGwB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,mCAAiC,CAAG,CAAC,cACtFzB,IAAA,MAAGwB,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAC,gDAAyC,CAAG,CAAC,EAC9E,CAAC,EACH,CAAC,cACN3B,KAAA,QAAK0B,SAAS,CAAC,0FAA0F,CAAAC,QAAA,eACvGzB,IAAA,QAAKwB,SAAS,CAAC,kDAAkD,CAAM,CAAC,cACxE1B,KAAA,QAAK0B,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrBzB,IAAA,MAAGwB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,4BAA0B,CAAG,CAAC,cAC/EzB,IAAA,MAAGwB,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,4CAAqC,CAAG,CAAC,EAC7E,CAAC,EACH,CAAC,cACN3B,KAAA,QAAK0B,SAAS,CAAC,sFAAsF,CAAAC,QAAA,eACnGzB,IAAA,QAAKwB,SAAS,CAAC,gDAAgD,CAAM,CAAC,cACtE1B,KAAA,QAAK0B,SAAS,CAAC,QAAQ,CAAAC,QAAA,eACrBzB,IAAA,MAAGwB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,6BAA2B,CAAG,CAAC,cAChFzB,IAAA,MAAGwB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,yCAAkC,CAAG,CAAC,EACxE,CAAC,EACH,CAAC,EACH,CAAC,CACS,CAAC,EACf,CAAC,cAGN3B,KAAA,QAAK0B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEpD3B,KAAA,QAAK0B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBzB,IAAA,QAAKwB,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BzB,IAAA,OAAIwB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC9CpB,CAAC,CAAC,4BAA4B,CAAC,CAC9B,CAAC,CACF,CAAC,cACNL,IAAA,QAAKwB,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBzB,IAAA,QAAKwB,SAAS,CAAC,MAAM,CAAAC,QAAA,CAClBhB,UAAU,SAAVA,UAAU,WAAVA,UAAU,CAAEsB,SAAS,cACpB/B,IAAA,QAAKwB,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBO,MAAM,CAACC,OAAO,CAACxB,UAAU,CAACsB,SAAS,CAAC,CAACJ,GAAG,CAACO,IAAA,EAAsB,IAArB,CAACC,OAAO,CAAEC,KAAK,CAAC,CAAAF,IAAA,CACzD,KAAM,CAAAG,UAAU,CAAG,CAAED,KAAK,CAAG3B,UAAU,CAACM,KAAK,CAAI,GAAG,EAAEuB,OAAO,CAAC,CAAC,CAAC,CAChE,mBACExC,KAAA,QAAmB0B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAC9D3B,KAAA,QAAK0B,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CzB,IAAA,QAAKwB,SAAS,yBAAAe,MAAA,CACZJ,OAAO,GAAK,QAAQ,CAAG,aAAa,CACpCA,OAAO,GAAK,OAAO,CAAG,cAAc,CACpCA,OAAO,GAAK,MAAM,CAAG,eAAe,CACpC,eAAe,CACd,CAAM,CAAC,cACVnC,IAAA,SAAMwB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEU,OAAO,CAAO,CAAC,EAChE,CAAC,cACNrC,KAAA,QAAK0B,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBzB,IAAA,SAAMwB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEW,KAAK,CAAO,CAAC,cAClEtC,KAAA,SAAM0B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAC,GAAC,CAACY,UAAU,CAAC,IAAE,EAAM,CAAC,EAChE,CAAC,GAbEF,OAcL,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,cAENnC,IAAA,QAAKwB,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,uBAEvE,CAAK,CACN,CACE,CAAC,CACH,CAAC,EACH,CAAC,cAGN3B,KAAA,QAAK0B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBzB,IAAA,QAAKwB,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BzB,IAAA,OAAIwB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC9CpB,CAAC,CAAC,gCAAgC,CAAC,CAClC,CAAC,CACF,CAAC,cACNL,IAAA,QAAKwB,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBzB,IAAA,QAAKwB,SAAS,CAAC,MAAM,CAAAC,QAAA,CAClBhB,UAAU,SAAVA,UAAU,WAAVA,UAAU,CAAEW,cAAc,cACzBpB,IAAA,QAAKwB,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvBO,MAAM,CAACC,OAAO,CAACxB,UAAU,CAACW,cAAc,CAAC,CAACO,GAAG,CAACa,KAAA,EAAqB,IAApB,CAACC,MAAM,CAAEL,KAAK,CAAC,CAAAI,KAAA,CAC7D,KAAM,CAAAH,UAAU,CAAG,CAAED,KAAK,CAAG3B,UAAU,CAACM,KAAK,CAAI,GAAG,EAAEuB,OAAO,CAAC,CAAC,CAAC,CAChE,mBACExC,KAAA,QAAkB0B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAC7D3B,KAAA,QAAK0B,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CzB,IAAA,QAAKwB,SAAS,yBAAAe,MAAA,CACZE,MAAM,GAAK,SAAS,CAAG,cAAc,CACrCA,MAAM,GAAK,MAAM,CAAG,YAAY,CAChCA,MAAM,GAAK,SAAS,CAAG,eAAe,CACtC,aAAa,CACZ,CAAM,CAAC,cACVzC,IAAA,SAAMwB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEgB,MAAM,CAAO,CAAC,EAC/D,CAAC,cACN3C,KAAA,QAAK0B,SAAS,CAAC,YAAY,CAAAC,QAAA,eACzBzB,IAAA,SAAMwB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEW,KAAK,CAAO,CAAC,cAClEtC,KAAA,SAAM0B,SAAS,CAAC,4BAA4B,CAAAC,QAAA,EAAC,GAAC,CAACY,UAAU,CAAC,IAAE,EAAM,CAAC,EAChE,CAAC,GAbEI,MAcL,CAAC,CAEV,CAAC,CAAC,CACC,CAAC,cAENzC,IAAA,QAAKwB,SAAS,CAAC,uDAAuD,CAAAC,QAAA,CAAC,uBAEvE,CAAK,CACN,CACE,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,cAGN3B,KAAA,QAAK0B,SAAS,CAAC,uCAAuC,CAAAC,QAAA,eAEpD3B,KAAA,QAAK0B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBzB,IAAA,QAAKwB,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BzB,IAAA,OAAIwB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC9CpB,CAAC,CAAC,0BAA0B,CAAC,CAC5B,CAAC,CACF,CAAC,cACNL,IAAA,QAAKwB,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBzB,IAAA,QAAKwB,SAAS,CAAC,WAAW,CAAAC,QAAA,CACvB,CAAAhB,UAAU,SAAVA,UAAU,iBAAVA,UAAU,CAAEa,gBAAgB,GAAIU,MAAM,CAACC,OAAO,CAACxB,UAAU,CAACa,gBAAgB,CAAC,CAACK,GAAG,CAACe,KAAA,MAAC,CAACD,MAAM,CAAEL,KAAK,CAAC,CAAAM,KAAA,oBAC/F5C,KAAA,QAAkB0B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAC7DzB,IAAA,SAAMwB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAEgB,MAAM,CAAO,CAAC,cAClEzC,IAAA,SAAMwB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAEW,KAAK,CAAO,CAAC,GAF1DK,MAGL,CAAC,EACP,CAAC,CACC,CAAC,CACH,CAAC,EACH,CAAC,cAGN3C,KAAA,QAAK0B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBzB,IAAA,QAAKwB,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BzB,IAAA,OAAIwB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC9CpB,CAAC,CAAC,yBAAyB,CAAC,CAC3B,CAAC,CACF,CAAC,cACNL,IAAA,QAAKwB,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxB3B,KAAA,QAAK0B,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3B,KAAA,QAAK0B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDzB,IAAA,SAAMwB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,cACzDzB,IAAA,SAAMwB,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,KAAG,CAAM,CAAC,EAC5D,CAAC,cACN3B,KAAA,QAAK0B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDzB,IAAA,SAAMwB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,cAAY,CAAM,CAAC,cAC3DzB,IAAA,SAAMwB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,IAAE,CAAM,CAAC,EAC1D,CAAC,cACN3B,KAAA,QAAK0B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDzB,IAAA,SAAMwB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,OAAK,CAAM,CAAC,cACpDzB,IAAA,SAAMwB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,IAAE,CAAM,CAAC,EACzD,CAAC,cACN3B,KAAA,QAAK0B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDzB,IAAA,SAAMwB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,cACzDzB,IAAA,SAAMwB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,KAAG,CAAM,CAAC,EAC3D,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,cAGN3B,KAAA,QAAK0B,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBzB,IAAA,QAAKwB,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BzB,IAAA,OAAIwB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAC9CpB,CAAC,CAAC,4BAA4B,CAAC,CAC9B,CAAC,CACF,CAAC,cACNL,IAAA,QAAKwB,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxB3B,KAAA,QAAK0B,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB3B,KAAA,QAAK0B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDzB,IAAA,SAAMwB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,iBAAe,CAAM,CAAC,cAC9DzB,IAAA,SAAMwB,SAAS,CAAC,oCAAoC,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,EACjE,CAAC,cACN3B,KAAA,QAAK0B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDzB,IAAA,SAAMwB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,cACzDzB,IAAA,SAAMwB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,EAC/D,CAAC,cACN3B,KAAA,QAAK0B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDzB,IAAA,SAAMwB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,WAAS,CAAM,CAAC,cACxDzB,IAAA,SAAMwB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,SAAO,CAAM,CAAC,EAC9D,CAAC,cACN3B,KAAA,QAAK0B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChDzB,IAAA,SAAMwB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,YAAU,CAAM,CAAC,cACzDzB,IAAA,SAAMwB,SAAS,CAAC,mCAAmC,CAAAC,QAAA,CAAC,UAAQ,CAAM,CAAC,EAChE,CAAC,EACH,CAAC,CACH,CAAC,EACH,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAxB,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}