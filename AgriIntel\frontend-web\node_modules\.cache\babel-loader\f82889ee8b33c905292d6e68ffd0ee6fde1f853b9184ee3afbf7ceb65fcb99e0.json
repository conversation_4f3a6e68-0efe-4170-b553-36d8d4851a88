{"ast": null, "code": "export function warn() {\n  if (console && console.warn) {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (typeof args[0] === 'string') args[0] = \"react-i18next:: \".concat(args[0]);\n    console.warn(...args);\n  }\n}\nconst alreadyWarned = {};\nexport function warnOnce() {\n  for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    args[_key2] = arguments[_key2];\n  }\n  if (typeof args[0] === 'string' && alreadyWarned[args[0]]) return;\n  if (typeof args[0] === 'string') alreadyWarned[args[0]] = new Date();\n  warn(...args);\n}\nconst loadedClb = (i18n, cb) => () => {\n  if (i18n.isInitialized) {\n    cb();\n  } else {\n    const initialized = () => {\n      setTimeout(() => {\n        i18n.off('initialized', initialized);\n      }, 0);\n      cb();\n    };\n    i18n.on('initialized', initialized);\n  }\n};\nexport function loadNamespaces(i18n, ns, cb) {\n  i18n.loadNamespaces(ns, loadedClb(i18n, cb));\n}\nexport function loadLanguages(i18n, lng, ns, cb) {\n  if (typeof ns === 'string') ns = [ns];\n  ns.forEach(n => {\n    if (i18n.options.ns.indexOf(n) < 0) i18n.options.ns.push(n);\n  });\n  i18n.loadLanguages(lng, loadedClb(i18n, cb));\n}\nfunction oldI18nextHasLoadedNamespace(ns, i18n) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  const lng = i18n.languages[0];\n  const fallbackLng = i18n.options ? i18n.options.fallbackLng : false;\n  const lastLng = i18n.languages[i18n.languages.length - 1];\n  if (lng.toLowerCase() === 'cimode') return true;\n  const loadNotPending = (l, n) => {\n    const loadState = i18n.services.backendConnector.state[\"\".concat(l, \"|\").concat(n)];\n    return loadState === -1 || loadState === 2;\n  };\n  if (options.bindI18n && options.bindI18n.indexOf('languageChanging') > -1 && i18n.services.backendConnector.backend && i18n.isLanguageChangingTo && !loadNotPending(i18n.isLanguageChangingTo, ns)) return false;\n  if (i18n.hasResourceBundle(lng, ns)) return true;\n  if (!i18n.services.backendConnector.backend || i18n.options.resources && !i18n.options.partialBundledLanguages) return true;\n  if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n  return false;\n}\nexport function hasLoadedNamespace(ns, i18n) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  if (!i18n.languages || !i18n.languages.length) {\n    warnOnce('i18n.languages were undefined or empty', i18n.languages);\n    return true;\n  }\n  const isNewerI18next = i18n.options.ignoreJSONStructure !== undefined;\n  if (!isNewerI18next) {\n    return oldI18nextHasLoadedNamespace(ns, i18n, options);\n  }\n  return i18n.hasLoadedNamespace(ns, {\n    lng: options.lng,\n    precheck: (i18nInstance, loadNotPending) => {\n      if (options.bindI18n && options.bindI18n.indexOf('languageChanging') > -1 && i18nInstance.services.backendConnector.backend && i18nInstance.isLanguageChangingTo && !loadNotPending(i18nInstance.isLanguageChangingTo, ns)) return false;\n    }\n  });\n}\nexport function getDisplayName(Component) {\n  return Component.displayName || Component.name || (typeof Component === 'string' && Component.length > 0 ? Component : 'Unknown');\n}", "map": {"version": 3, "names": ["warn", "console", "_len", "arguments", "length", "args", "Array", "_key", "concat", "alreadyWarned", "warnOnce", "_len2", "_key2", "Date", "loadedClb", "i18n", "cb", "isInitialized", "initialized", "setTimeout", "off", "on", "loadNamespaces", "ns", "loadLanguages", "lng", "for<PERSON>ach", "n", "options", "indexOf", "push", "oldI18nextHasLoadedNamespace", "undefined", "languages", "fallbackLng", "lastLng", "toLowerCase", "loadNotPending", "l", "loadState", "services", "backendConnector", "state", "bindI18n", "backend", "isLanguageChangingTo", "hasResourceBundle", "resources", "partialBundledLanguages", "hasLoadedNamespace", "isNewerI18next", "ignoreJSONStructure", "precheck", "i18nInstance", "getDisplayName", "Component", "displayName", "name"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-i18next/dist/es/utils.js"], "sourcesContent": ["export function warn() {\n  if (console && console.warn) {\n    for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n      args[_key] = arguments[_key];\n    }\n    if (typeof args[0] === 'string') args[0] = `react-i18next:: ${args[0]}`;\n    console.warn(...args);\n  }\n}\nconst alreadyWarned = {};\nexport function warnOnce() {\n  for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n    args[_key2] = arguments[_key2];\n  }\n  if (typeof args[0] === 'string' && alreadyWarned[args[0]]) return;\n  if (typeof args[0] === 'string') alreadyWarned[args[0]] = new Date();\n  warn(...args);\n}\nconst loadedClb = (i18n, cb) => () => {\n  if (i18n.isInitialized) {\n    cb();\n  } else {\n    const initialized = () => {\n      setTimeout(() => {\n        i18n.off('initialized', initialized);\n      }, 0);\n      cb();\n    };\n    i18n.on('initialized', initialized);\n  }\n};\nexport function loadNamespaces(i18n, ns, cb) {\n  i18n.loadNamespaces(ns, loadedClb(i18n, cb));\n}\nexport function loadLanguages(i18n, lng, ns, cb) {\n  if (typeof ns === 'string') ns = [ns];\n  ns.forEach(n => {\n    if (i18n.options.ns.indexOf(n) < 0) i18n.options.ns.push(n);\n  });\n  i18n.loadLanguages(lng, loadedClb(i18n, cb));\n}\nfunction oldI18nextHasLoadedNamespace(ns, i18n) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  const lng = i18n.languages[0];\n  const fallbackLng = i18n.options ? i18n.options.fallbackLng : false;\n  const lastLng = i18n.languages[i18n.languages.length - 1];\n  if (lng.toLowerCase() === 'cimode') return true;\n  const loadNotPending = (l, n) => {\n    const loadState = i18n.services.backendConnector.state[`${l}|${n}`];\n    return loadState === -1 || loadState === 2;\n  };\n  if (options.bindI18n && options.bindI18n.indexOf('languageChanging') > -1 && i18n.services.backendConnector.backend && i18n.isLanguageChangingTo && !loadNotPending(i18n.isLanguageChangingTo, ns)) return false;\n  if (i18n.hasResourceBundle(lng, ns)) return true;\n  if (!i18n.services.backendConnector.backend || i18n.options.resources && !i18n.options.partialBundledLanguages) return true;\n  if (loadNotPending(lng, ns) && (!fallbackLng || loadNotPending(lastLng, ns))) return true;\n  return false;\n}\nexport function hasLoadedNamespace(ns, i18n) {\n  let options = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : {};\n  if (!i18n.languages || !i18n.languages.length) {\n    warnOnce('i18n.languages were undefined or empty', i18n.languages);\n    return true;\n  }\n  const isNewerI18next = i18n.options.ignoreJSONStructure !== undefined;\n  if (!isNewerI18next) {\n    return oldI18nextHasLoadedNamespace(ns, i18n, options);\n  }\n  return i18n.hasLoadedNamespace(ns, {\n    lng: options.lng,\n    precheck: (i18nInstance, loadNotPending) => {\n      if (options.bindI18n && options.bindI18n.indexOf('languageChanging') > -1 && i18nInstance.services.backendConnector.backend && i18nInstance.isLanguageChangingTo && !loadNotPending(i18nInstance.isLanguageChangingTo, ns)) return false;\n    }\n  });\n}\nexport function getDisplayName(Component) {\n  return Component.displayName || Component.name || (typeof Component === 'string' && Component.length > 0 ? Component : 'Unknown');\n}"], "mappings": "AAAA,OAAO,SAASA,IAAIA,CAAA,EAAG;EACrB,IAAIC,OAAO,IAAIA,OAAO,CAACD,IAAI,EAAE;IAC3B,KAAK,IAAIE,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC9B;IACA,IAAI,OAAOF,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAEA,IAAI,CAAC,CAAC,CAAC,sBAAAG,MAAA,CAAsBH,IAAI,CAAC,CAAC,CAAC,CAAE;IACvEJ,OAAO,CAACD,IAAI,CAAC,GAAGK,IAAI,CAAC;EACvB;AACF;AACA,MAAMI,aAAa,GAAG,CAAC,CAAC;AACxB,OAAO,SAASC,QAAQA,CAAA,EAAG;EACzB,KAAK,IAAIC,KAAK,GAAGR,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACK,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;IAC7FP,IAAI,CAACO,KAAK,CAAC,GAAGT,SAAS,CAACS,KAAK,CAAC;EAChC;EACA,IAAI,OAAOP,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,IAAII,aAAa,CAACJ,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;EAC3D,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAEI,aAAa,CAACJ,IAAI,CAAC,CAAC,CAAC,CAAC,GAAG,IAAIQ,IAAI,CAAC,CAAC;EACpEb,IAAI,CAAC,GAAGK,IAAI,CAAC;AACf;AACA,MAAMS,SAAS,GAAGA,CAACC,IAAI,EAAEC,EAAE,KAAK,MAAM;EACpC,IAAID,IAAI,CAACE,aAAa,EAAE;IACtBD,EAAE,CAAC,CAAC;EACN,CAAC,MAAM;IACL,MAAME,WAAW,GAAGA,CAAA,KAAM;MACxBC,UAAU,CAAC,MAAM;QACfJ,IAAI,CAACK,GAAG,CAAC,aAAa,EAAEF,WAAW,CAAC;MACtC,CAAC,EAAE,CAAC,CAAC;MACLF,EAAE,CAAC,CAAC;IACN,CAAC;IACDD,IAAI,CAACM,EAAE,CAAC,aAAa,EAAEH,WAAW,CAAC;EACrC;AACF,CAAC;AACD,OAAO,SAASI,cAAcA,CAACP,IAAI,EAAEQ,EAAE,EAAEP,EAAE,EAAE;EAC3CD,IAAI,CAACO,cAAc,CAACC,EAAE,EAAET,SAAS,CAACC,IAAI,EAAEC,EAAE,CAAC,CAAC;AAC9C;AACA,OAAO,SAASQ,aAAaA,CAACT,IAAI,EAAEU,GAAG,EAAEF,EAAE,EAAEP,EAAE,EAAE;EAC/C,IAAI,OAAOO,EAAE,KAAK,QAAQ,EAAEA,EAAE,GAAG,CAACA,EAAE,CAAC;EACrCA,EAAE,CAACG,OAAO,CAACC,CAAC,IAAI;IACd,IAAIZ,IAAI,CAACa,OAAO,CAACL,EAAE,CAACM,OAAO,CAACF,CAAC,CAAC,GAAG,CAAC,EAAEZ,IAAI,CAACa,OAAO,CAACL,EAAE,CAACO,IAAI,CAACH,CAAC,CAAC;EAC7D,CAAC,CAAC;EACFZ,IAAI,CAACS,aAAa,CAACC,GAAG,EAAEX,SAAS,CAACC,IAAI,EAAEC,EAAE,CAAC,CAAC;AAC9C;AACA,SAASe,4BAA4BA,CAACR,EAAE,EAAER,IAAI,EAAE;EAC9C,IAAIa,OAAO,GAAGzB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK6B,SAAS,GAAG7B,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpF,MAAMsB,GAAG,GAAGV,IAAI,CAACkB,SAAS,CAAC,CAAC,CAAC;EAC7B,MAAMC,WAAW,GAAGnB,IAAI,CAACa,OAAO,GAAGb,IAAI,CAACa,OAAO,CAACM,WAAW,GAAG,KAAK;EACnE,MAAMC,OAAO,GAAGpB,IAAI,CAACkB,SAAS,CAAClB,IAAI,CAACkB,SAAS,CAAC7B,MAAM,GAAG,CAAC,CAAC;EACzD,IAAIqB,GAAG,CAACW,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE,OAAO,IAAI;EAC/C,MAAMC,cAAc,GAAGA,CAACC,CAAC,EAAEX,CAAC,KAAK;IAC/B,MAAMY,SAAS,GAAGxB,IAAI,CAACyB,QAAQ,CAACC,gBAAgB,CAACC,KAAK,IAAAlC,MAAA,CAAI8B,CAAC,OAAA9B,MAAA,CAAImB,CAAC,EAAG;IACnE,OAAOY,SAAS,KAAK,CAAC,CAAC,IAAIA,SAAS,KAAK,CAAC;EAC5C,CAAC;EACD,IAAIX,OAAO,CAACe,QAAQ,IAAIf,OAAO,CAACe,QAAQ,CAACd,OAAO,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAId,IAAI,CAACyB,QAAQ,CAACC,gBAAgB,CAACG,OAAO,IAAI7B,IAAI,CAAC8B,oBAAoB,IAAI,CAACR,cAAc,CAACtB,IAAI,CAAC8B,oBAAoB,EAAEtB,EAAE,CAAC,EAAE,OAAO,KAAK;EAChN,IAAIR,IAAI,CAAC+B,iBAAiB,CAACrB,GAAG,EAAEF,EAAE,CAAC,EAAE,OAAO,IAAI;EAChD,IAAI,CAACR,IAAI,CAACyB,QAAQ,CAACC,gBAAgB,CAACG,OAAO,IAAI7B,IAAI,CAACa,OAAO,CAACmB,SAAS,IAAI,CAAChC,IAAI,CAACa,OAAO,CAACoB,uBAAuB,EAAE,OAAO,IAAI;EAC3H,IAAIX,cAAc,CAACZ,GAAG,EAAEF,EAAE,CAAC,KAAK,CAACW,WAAW,IAAIG,cAAc,CAACF,OAAO,EAAEZ,EAAE,CAAC,CAAC,EAAE,OAAO,IAAI;EACzF,OAAO,KAAK;AACd;AACA,OAAO,SAAS0B,kBAAkBA,CAAC1B,EAAE,EAAER,IAAI,EAAE;EAC3C,IAAIa,OAAO,GAAGzB,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAK6B,SAAS,GAAG7B,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpF,IAAI,CAACY,IAAI,CAACkB,SAAS,IAAI,CAAClB,IAAI,CAACkB,SAAS,CAAC7B,MAAM,EAAE;IAC7CM,QAAQ,CAAC,wCAAwC,EAAEK,IAAI,CAACkB,SAAS,CAAC;IAClE,OAAO,IAAI;EACb;EACA,MAAMiB,cAAc,GAAGnC,IAAI,CAACa,OAAO,CAACuB,mBAAmB,KAAKnB,SAAS;EACrE,IAAI,CAACkB,cAAc,EAAE;IACnB,OAAOnB,4BAA4B,CAACR,EAAE,EAAER,IAAI,EAAEa,OAAO,CAAC;EACxD;EACA,OAAOb,IAAI,CAACkC,kBAAkB,CAAC1B,EAAE,EAAE;IACjCE,GAAG,EAAEG,OAAO,CAACH,GAAG;IAChB2B,QAAQ,EAAEA,CAACC,YAAY,EAAEhB,cAAc,KAAK;MAC1C,IAAIT,OAAO,CAACe,QAAQ,IAAIf,OAAO,CAACe,QAAQ,CAACd,OAAO,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,IAAIwB,YAAY,CAACb,QAAQ,CAACC,gBAAgB,CAACG,OAAO,IAAIS,YAAY,CAACR,oBAAoB,IAAI,CAACR,cAAc,CAACgB,YAAY,CAACR,oBAAoB,EAAEtB,EAAE,CAAC,EAAE,OAAO,KAAK;IAC1O;EACF,CAAC,CAAC;AACJ;AACA,OAAO,SAAS+B,cAAcA,CAACC,SAAS,EAAE;EACxC,OAAOA,SAAS,CAACC,WAAW,IAAID,SAAS,CAACE,IAAI,KAAK,OAAOF,SAAS,KAAK,QAAQ,IAAIA,SAAS,CAACnD,MAAM,GAAG,CAAC,GAAGmD,SAAS,GAAG,SAAS,CAAC;AACnI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}