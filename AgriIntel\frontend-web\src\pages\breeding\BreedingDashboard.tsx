import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { motion } from 'framer-motion';
import { Grid, Card, CardContent, Typography, Box, Chip, IconButton, Avatar, LinearProgress, useTheme, List, ListItem, ListItemText, ListItemAvatar, ListItemIcon, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, alpha, Divider, Paper, Tab, Tabs, Menu, MenuItem, FormControl, InputLabel, Select, TextField, CircularProgress, Tooltip as MuiTooltip, TablePagination, Badge, Alert, Skeleton } from '@mui/material';
import {
  Add,
  Favorite,
  ChildCare,
  CalendarToday,
  Search,
  FilterList,
  MoreVert,
  ArrowForward,
  CheckCircle,
  Schedule,
  Pets,
  Female,
  Male,
  Science,
  Notifications,
  Warning,
  BarChart,
  PieChart as PieChartIcon,
  Timeline,
  Download,
  Print,
  Share,
  Assessment,
  TrendingUp,
  TrendingDown,
  Info,
  MonitorHeart,
  Biotech,
  Healing,
  EventNote,
  DateRange,
  Medication,
  HealthAndSafety,
  Dashboard,
  InsertChart,
  ListAlt,
  Thermostat,
  ChildFriendly,
  Analytics,
  Refresh
 } from '../../utils/iconImports';
import {
  PieChart,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  Legend,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  LineChart,
  Line,
  AreaChart,
  Area,
  ScatterChart,
  Scatter,
  ComposedChart
} from 'recharts';
import { BarChart as RechartsBarChart } from 'recharts';
import { EnhancedPieLabelRenderProps } from '../../types/recharts';
import {  ModuleHeader, AnimatedBackgroundCard, ModuleContainer, ModuleHeaderCard, BlendedBackgroundCard, ResponsiveNavTabs, withSubModuleTranslation, StandardDashboard, ModernChart , CustomButton } from '../../components/common';
import { mockBreedingRecords, mockBirthRecords, mockHeatRecords, mockBreedingPerformanceData } from '../../mocks/breedingData';
import { format, parseISO, isAfter, isBefore, addDays, differenceInDays, addMonths, subMonths, subDays, getMonth, getYear } from 'date-fns';
import { useMongoDb } from '../../hooks/useMongoDb';
import { BreedingRecord } from '../../types/breeding';

// Animation variants
const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
};

const itemVariants = {
  hidden: { y: 20, opacity: 0 },
  visible: {
    y: 0,
    opacity: 1,
    transition: {
      duration: 0.5
    }
  }
};

// Interface for tab panel props
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

// Tab Panel component
const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`breeding-tabpanel-${index}`}
      aria-labelledby={`breeding-tab-${index}`}
      {...other}
      style={{ paddingTop: '20px' }}
    >
      {value === index && (
        <Box>
          {children}
        </Box>
      )}
    </div>
  );
};

interface BreedingDashboardProps {
  translate?: (key: string, params?: Record<string, string | number>) => string;
  translateSubModule?: (field: string, fallback: string) => string;
  translateModuleField?: (field: string, fallback?: string) => string;
}

const BreedingDashboard: React.FC<BreedingDashboardProps> = ({
  translate,
  translateSubModule,
  translateModuleField
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { services, isConnected, isLoading: isDbLoading } = useMongoDb();

  // State for data loading
  const [breedingRecords, setBreedingRecords] = useState<BreedingRecord[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshTrigger, setRefreshTrigger] = useState(0);

  // State for tabs, filters, and interactions
  const [tabValue, setTabValue] = useState(0);
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'year'>('month');
  const [selectedMetric, setSelectedMetric] = useState<string | null>(null);
  const [radarTooltip, setRadarTooltip] = useState<{ show: boolean; content: string }>({ show: false, content: '' });
  const [selectedBreedingType, setSelectedBreedingType] = useState<string | null>(null);
  const [selectedMonth, setSelectedMonth] = useState<string | null>(null);
  const [chartTooltip, setChartTooltip] = useState<{ show: boolean; content: string }>({ show: false, content: '' });
  const [selectedChart, setSelectedChart] = useState<string | null>(null);

  // State for menu
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [exportFormat, setExportFormat] = useState('pdf');

  // State for pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);

  // State for date range filter
  const [dateRange, setDateRange] = useState<{start: string, end: string}>({
    start: new Date(new Date().setMonth(new Date().getMonth() - 6)).toISOString().split('T')[0],
    end: new Date().toISOString().split('T')[0]
  });

  // State for animal type filter
  const [animalTypeFilter, setAnimalTypeFilter] = useState('all');

  // Fetch breeding records from MongoDB
  useEffect(() => {
    const fetchBreedingRecords = async () => {
      setIsLoading(true);
      setError(null);
      try {
        // Try to fetch from MongoDB if connected
        if (isConnected && services.breeding) {
          const records = await services.breeding.findAll();
          setBreedingRecords(records);
        } else {
          // Fall back to mock data
          setBreedingRecords(mockBreedingRecords);
        }
      } catch (err) {
        console.error('Error fetching breeding records:', err);
        setError('Failed to load breeding records. Using mock data instead.');
        setBreedingRecords(mockBreedingRecords);
      } finally {
        setIsLoading(false);
      }
    };

    fetchBreedingRecords();
  }, [isConnected, services.breeding, refreshTrigger]);

  // Handle refresh data
  const handleRefreshData = () => {
    setRefreshTrigger(prev => prev + 1);
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Handle tab ID change for ResponsiveNavTabs
  const handleTabIdChange = (tabId: string) => {
    setTabValue(parseInt(tabId, 10));
  };

  // Handle menu open/close
  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  // Handle export
  const handleExport = () => {
    console.log(`Exporting in ${exportFormat} format`);
    handleMenuClose();
  };

  // Handle pagination
  const handleChangePage = (event: unknown, newValue: number) => {
    setPage(newValue);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Calculate breeding statistics
  const totalBreedings = breedingRecords.length;
  const confirmedBreedings = breedingRecords.filter(record => record.status.toLowerCase() === 'confirmed').length;
  const pendingBreedings = breedingRecords.filter(record => record.status.toLowerCase() === 'pending').length;
  const unsuccessfulBreedings = breedingRecords.filter(record => record.status.toLowerCase() === 'unsuccessful').length;
  const successRate = totalBreedings > 0 ? Math.round((confirmedBreedings / totalBreedings) * 100) : 0;

  // Calculate upcoming events
  const today = new Date();
  const upcomingHeatDates = mockHeatRecords
    .filter(record => isAfter(parseISO(record.date), today))
    .sort((a, b) => parseISO(a.date).getTime() - parseISO(b.date).getTime())
    .slice(0, 5);

  const upcomingBirths = breedingRecords
    .filter(record => record.expectedDueDate && isAfter(parseISO(record.expectedDueDate), today) && isBefore(parseISO(record.expectedDueDate), addDays(today, 30)))
    .sort((a, b) => parseISO(a.expectedDueDate!).getTime() - parseISO(b.expectedDueDate!).getTime())
    .slice(0, 5);

  // Calculate additional breeding metrics
  const totalBirths = mockBirthRecords.length;
  const totalOffspring = mockBirthRecords.reduce((sum, record) => sum + record.numberOfOffspring, 0);
  const twinRate = mockBirthRecords.filter(record => record.numberOfOffspring > 1).length /
    (mockBirthRecords.length || 1) * 100;
  const complicationRate = mockBirthRecords.filter(record => record.complications).length /
    (mockBirthRecords.length || 1) * 100;
  const assistanceRate = mockBirthRecords.filter(record => record.assistanceProvided).length /
    (mockBirthRecords.length || 1) * 100;

  // Calculate breeding efficiency
  const averageBreedingToConception = 1.8; // Mock data - average number of breeding attempts to conception
  const averageGestationLength = 280; // Mock data - average gestation length in days
  const averageCalvingInterval = 385; // Mock data - average calving interval in days

  // Prepare chart data
  const breedingMethodData = [
    { name: 'Natural', value: breedingRecords.filter(record => record.type === 'natural').length },
    { name: 'AI', value: breedingRecords.filter(record => record.type === 'artificial').length },
    { name: 'Embryo Transfer', value: 0 }
  ];

  const breedingStatusData = [
    { name: 'Confirmed', value: confirmedBreedings },
    { name: 'Pending', value: pendingBreedings },
    { name: 'Failed', value: unsuccessfulBreedings }
  ];

  // Generate monthly breeding data from actual records
  const generateMonthlyBreedingData = () => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const data = months.map(month => ({
      name: month,
      natural: 0,
      ai: 0,
      embryo: 0,
      success: 0,
      failure: 0
    }));

    // Populate with real data if available
    if (breedingRecords.length > 0) {
      breedingRecords.forEach(record => {
        const date = parseISO(record.date);
        const monthIndex = getMonth(date);

        // Increment breeding method count
        if (record.type === 'natural') {
          data[monthIndex].natural += 1;
        } else if (record.type === 'artificial') {
          data[monthIndex].ai += 1;
        }

        // Increment success/failure count
        if (record.status === 'confirmed') {
          data[monthIndex].success += 1;
        } else if (record.status === 'unsuccessful') {
          data[monthIndex].failure += 1;
        }
      });
    } else {
      // Use mock data if no records
      return [
        { name: 'Jan', natural: 5, ai: 8, embryo: 2, success: 12, failure: 3 },
        { name: 'Feb', natural: 7, ai: 6, embryo: 1, success: 10, failure: 4 },
        { name: 'Mar', natural: 4, ai: 9, embryo: 3, success: 13, failure: 3 },
        { name: 'Apr', natural: 6, ai: 7, embryo: 2, success: 11, failure: 4 },
        { name: 'May', natural: 8, ai: 5, embryo: 1, success: 10, failure: 4 },
        { name: 'Jun', natural: 9, ai: 4, embryo: 2, success: 12, failure: 3 },
        { name: 'Jul', natural: 7, ai: 6, embryo: 3, success: 13, failure: 3 },
        { name: 'Aug', natural: 5, ai: 8, embryo: 2, success: 11, failure: 4 },
        { name: 'Sep', natural: 6, ai: 7, embryo: 1, success: 10, failure: 4 },
        { name: 'Oct', natural: 8, ai: 5, embryo: 2, success: 12, failure: 3 },
        { name: 'Nov', natural: 9, ai: 4, embryo: 3, success: 13, failure: 3 },
        { name: 'Dec', natural: 7, ai: 6, embryo: 2, success: 11, failure: 4 }
      ];
    }

    return data;
  };

  const monthlyBreedingData = generateMonthlyBreedingData();

  // Generate success rate trend data
  const generateSuccessRateTrendData = () => {
    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
    const currentYear = new Date().getFullYear();

    // Initialize with mock data
    const data = months.map((month, index) => ({
      name: month,
      rate: 65 + Math.floor(Math.random() * 25), // Random success rate between 65-90%
      year: currentYear
    }));

    // Try to populate with real data if available
    if (breedingRecords.length > 0) {
      // Group records by month
      const recordsByMonth = new Array(12).fill(0).map(() => ({ total: 0, success: 0 }));

      breedingRecords.forEach(record => {
        const date = parseISO(record.date);
        const year = getYear(date);

        // Only include current year records
        if (year === currentYear) {
          const monthIndex = getMonth(date);
          recordsByMonth[monthIndex].total += 1;

          if (record.status === 'confirmed') {
            recordsByMonth[monthIndex].success += 1;
          }
        }
      });

      // Calculate success rates
      recordsByMonth.forEach((monthData, index) => {
        if (monthData.total > 0) {
          data[index].rate = Math.round((monthData.success / monthData.total) * 100);
        }
      });
    }

    return data;
  };

  const successRateTrendData = generateSuccessRateTrendData();

  // Breeding success by animal type
  const breedingSuccessByAnimalType = [
    { name: 'Cattle', success: 85, attempts: 100 },
    { name: 'Sheep', success: 78, attempts: 90 },
    { name: 'Goats', success: 82, attempts: 95 },
    { name: 'Pigs', success: 90, attempts: 110 }
  ];

  // Breeding efficiency data
  const breedingEfficiencyData = [
    { name: 'Cattle', efficiency: 92 },
    { name: 'Sheep', efficiency: 88 },
    { name: 'Goats', efficiency: 85 },
    { name: 'Pigs', efficiency: 95 }
  ];

  // Birth complications data
  const birthComplicationsData = [
    { name: 'None', value: 85 },
    { name: 'Minor', value: 10 },
    { name: 'Major', value: 5 }
  ];

  // Genetic improvement tracking data
  const geneticImprovementData = [
    { year: '2019', improvement: 0 },
    { year: '2020', improvement: 5 },
    { year: '2021', improvement: 12 },
    { year: '2022', improvement: 18 },
    { year: '2023', improvement: 25 },
    { year: '2024', improvement: 32 }
  ];

  // Seasonal breeding patterns
  const seasonalBreedingData = [
    { season: 'Spring', breedings: 35, success: 30 },
    { season: 'Summer', breedings: 25, success: 20 },
    { season: 'Fall', breedings: 40, success: 35 },
    { season: 'Winter', breedings: 20, success: 15 }
  ];

  // Calving distribution by month
  const calvingDistributionData = [
    { month: 'Jan', count: 12 },
    { month: 'Feb', count: 15 },
    { month: 'Mar', count: 18 },
    { month: 'Apr', count: 22 },
    { month: 'May', count: 16 },
    { month: 'Jun', count: 10 },
    { month: 'Jul', count: 8 },
    { month: 'Aug', count: 6 },
    { month: 'Sep', count: 9 },
    { month: 'Oct', count: 14 },
    { month: 'Nov', count: 20 },
    { month: 'Dec', count: 16 }
  ];

  // Gestation period distribution data
  const gestationPeriodData = [
    { period: '270-275', count: 5 },
    { period: '276-280', count: 15 },
    { period: '281-285', count: 35 },
    { period: '286-290', count: 25 },
    { period: '291-295', count: 12 },
    { period: '296-300', count: 8 }
  ];

  const COLORS = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.warning.main,
    theme.palette.error.main,
    theme.palette.info.main
  ];

  // Prepare dashboard stats
  const dashboardStats = [
    {
      label: translateModuleField ? translateModuleField('total_breedings', "Total Breedings") : "Total Breedings",
      value: totalBreedings,
      icon: <Pets />,
      color: theme.palette.primary.main,
      trend: {
        value: 5,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: translateModuleField ? translateModuleField('success_rate', "Success Rate") : "Success Rate",
      value: `${successRate}%`,
      icon: <CheckCircle />,
      color: theme.palette.success.main,
      trend: {
        value: 2,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: translateModuleField ? translateModuleField('upcoming_births', "Upcoming Births") : "Upcoming Births",
      value: upcomingBirths.length,
      icon: <Female />,
      color: theme.palette.info.main,
      trend: {
        value: 1,
        isPositive: true,
        label: "since last month"
      }
    },
    {
      label: translateModuleField ? translateModuleField('heat_cycles', "Heat Cycles") : "Heat Cycles",
      value: upcomingHeatDates.length,
      icon: <CalendarToday />,
      color: theme.palette.warning.main,
      trend: {
        value: 3,
        isPositive: false,
        label: "since last month"
      }
    }
  ];

  // Prepare dashboard actions
  const dashboardActions = [
    {
      label: translateModuleField ? translateModuleField('add_breeding_record', "Add Breeding Record") : "Add Breeding Record",
      icon: <Add />,
      onClick: () => navigate('/breeding/schedule'),
      color: 'primary'
    }
  ];

  // Prepare dashboard tabs
  const dashboardTabs = [
    {
      label: translateModuleField ? translateModuleField('overview', "Overview") : "Overview",
      icon: <Dashboard />,
      content: (
        <Box>
          {/* Overview content will go here */}
        </Box>
      )
    },
    {
      label: translateModuleField ? translateModuleField('performance_metrics', "Performance Metrics") : "Performance Metrics",
      icon: <Assessment />,
      content: (
        <Box>
          {/* Performance metrics content will go here */}
        </Box>
      )
    },
    {
      label: translateModuleField ? translateModuleField('genetic_analysis', "Genetic Analysis") : "Genetic Analysis",
      icon: <Biotech />,
      content: (
        <Box>
          {/* Genetic analysis content will go here */}
        </Box>
      )
    },
    {
      label: translateModuleField ? translateModuleField('health_complications', "Health & Complications") : "Health & Complications",
      icon: <Healing />,
      content: (
        <Box>
          {/* Health & complications content will go here */}
        </Box>
      )
    },
    {
      label: translateModuleField ? translateModuleField('seasonal_patterns', "Seasonal Patterns") : "Seasonal Patterns",
      icon: <DateRange />,
      content: (
        <Box>
          {/* Seasonal patterns content will go here */}
        </Box>
      )
    }
  ];

  return (
    <StandardDashboard
      title={translateSubModule ? translateSubModule('title', "Breeding Management") : "Breeding Management"}
      subtitle={translateSubModule ? translateSubModule('subtitle', "Monitor and manage breeding activities, heat cycles, pregnancy tracking, and birth predictions") : "Monitor and manage breeding activities, heat cycles, pregnancy tracking, and birth predictions"}
      icon={<Pets />}
      stats={dashboardStats}
      actions={dashboardActions}
      tabs={dashboardTabs}
      activeTab={tabValue}
      onTabChange={handleTabChange}
      isLoading={isLoading || isDbLoading}
      loadingMessage={translateModuleField ? translateModuleField('loading', "Loading breeding data...") : "Loading breeding data..."}
      onRefresh={handleRefreshData}
      module="breeding"
    >

      <Box sx={{ px: 3, pb: 5 }}>
        {/* Stats Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mb-4"
        >
          <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 3, mb: 2 }}>
            {translateModuleField ? translateModuleField('breeding_statistics', "Breeding Statistics") : "Breeding Statistics"}
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Total Breedings"
                  subtitle={`${totalBreedings} breeding records`}
                  module="breeding"
                  uniqueId="breeding-total"
                  icon={<Pets />}
                  height={160}
                  accentColor={theme.palette.primary.main}
                />
              </motion.div>
            </Grid>

            <Grid item xs={12} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Success Rate"
                  subtitle={`${successRate}% successful breedings`}
                  module="breeding"
                  uniqueId="breeding-success"
                  icon={<CheckCircle />}
                  height={160}
                  accentColor={theme.palette.success.main}
                  secondaryColor={theme.palette.success.dark}
                />
              </motion.div>
            </Grid>

            <Grid item xs={12} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Upcoming Births"
                  subtitle={`${upcomingBirths.length} expected in next 30 days`}
                  module="breeding"
                  uniqueId="breeding-births"
                  icon={<Female />}
                  height={160}
                  accentColor={theme.palette.info.main}
                  secondaryColor={theme.palette.info.dark}
                />
              </motion.div>
            </Grid>

            <Grid item xs={12} md={3}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Heat Cycles"
                  subtitle={`${upcomingHeatDates.length} upcoming heat cycles`}
                  module="breeding"
                  uniqueId="breeding-heat"
                  icon={<CalendarToday />}
                  height={160}
                  accentColor={theme.palette.warning.main}
                  secondaryColor={theme.palette.warning.dark}
                />
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>

        {/* Charts Section */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mb-4"
        >
          <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
            {translateModuleField ? translateModuleField('breeding_analytics', "Breeding Analytics") : "Breeding Analytics"}
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <ModernChart
                title={translateModuleField ? translateModuleField('breeding_methods', "Breeding Methods") : "Breeding Methods"}
                subtitle={translateModuleField ? translateModuleField('breeding_methods_desc', "Distribution of breeding methods used") : "Distribution of breeding methods used"}
                data={breedingMethodData}
                type="pie"
                dataKeys={['value']}
                height={350}
                accentColor={theme.palette.primary.main}
                allowChartTypeChange={true}
                module="breeding"
                tooltip={translateModuleField ? translateModuleField('breeding_methods_help', "Shows the distribution of different breeding methods used") : "Shows the distribution of different breeding methods used"}
                formatValue={(value) => `${value} records`}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <ModernChart
                title={translateModuleField ? translateModuleField('breeding_status', "Breeding Status") : "Breeding Status"}
                subtitle={translateModuleField ? translateModuleField('breeding_status_desc', "Current status of breeding records") : "Current status of breeding records"}
                data={breedingStatusData}
                type="pie"
                dataKeys={['value']}
                height={350}
                accentColor={theme.palette.secondary.main}
                allowChartTypeChange={true}
                module="breeding"
                tooltip={translateModuleField ? translateModuleField('breeding_status_help', "Shows the current status of all breeding records") : "Shows the current status of all breeding records"}
                formatValue={(value) => `${value} records`}
              />
            </Grid>
            <Grid item xs={12}>
              <ModernChart
                title={translateModuleField ? translateModuleField('monthly_breeding', "Monthly Breeding Activity") : "Monthly Breeding Activity"}
                subtitle={translateModuleField ? translateModuleField('monthly_breeding_desc', "Breeding methods used throughout the year") : "Breeding methods used throughout the year"}
                data={monthlyBreedingData}
                type="bar"
                dataKeys={['natural', 'ai', 'embryo']}
                xAxisDataKey="name"
                height={350}
                accentColor={theme.palette.primary.main}
                allowChartTypeChange={true}
                allowTimeRangeChange={true}
                module="breeding"
                tooltip={translateModuleField ? translateModuleField('monthly_breeding_help', "Shows breeding activity by month and method") : "Shows breeding activity by month and method"}
                formatValue={(value) => `${value} breedings`}
              />
            </Grid>
          </Grid>
        </motion.div>

        {/* Upcoming Events Section */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="mb-4"
        >
          <Typography variant="h5" fontWeight="bold" gutterBottom sx={{ mt: 4, mb: 2 }}>
            {translateModuleField ? translateModuleField('upcoming_events', "Upcoming Events") : "Upcoming Events"}
          </Typography>

          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Upcoming Heat Cycles"
                  subtitle="Animals expected to be in heat soon"
                  module="breeding"
                  uniqueId="breeding-upcoming-heat"
                  height={400}
                  icon={<CalendarToday />}
                  accentColor={theme.palette.warning.main}
                  action={
                    <CustomButton
                      size="small"
                      variant="outlined"
                      onClick={() => navigate('/breeding/heat-calendar')}
                      sx={{
                        color: 'white',
                        borderColor: 'rgba(255,255,255,0.5)',
                        '&:hover': { borderColor: 'white', bgcolor: 'rgba(255,255,255,0.1)' }
                      }}
                    >
                      View Calendar
                    </CustomButton>
                  }
                  content={
                    <List>
                      {upcomingHeatDates.length > 0 ? (
                        upcomingHeatDates.map((record) => (
                          <ListItem key={record.id} divider>
                            <ListItemIcon>
                              <Avatar sx={{ bgcolor: theme.palette.warning.main }}>
                                <Female />
                              </Avatar>
                            </ListItemIcon>
                            <ListItemText
                              primary={`Animal ID: ${record.animalId}`}
                              secondary={`Expected Date: ${format(parseISO(record.date), 'dd MMM yyyy')} • Intensity: ${record.intensity}`}
                            />
                            <IconButton size="small" onClick={() => navigate(`/breeding/heat-calendar`)}>
                              <ArrowForward fontSize="small" />
                            </IconButton>
                          </ListItem>
                        ))
                      ) : (
                        <ListItem>
                          <ListItemText primary="No upcoming heat cycles" />
                        </ListItem>
                      )}
                    </List>
                  }
                />
              </motion.div>
            </Grid>

            <Grid item xs={12} md={6}>
              <motion.div variants={itemVariants}>
                <AnimatedBackgroundCard
                  title="Expected Births"
                  subtitle="Animals expected to give birth in the next 30 days"
                  module="breeding"
                  uniqueId="breeding-upcoming-births"
                  height={400}
                  icon={<Notifications />}
                  accentColor={theme.palette.info.main}
                  action={
                    <CustomButton
                      size="small"
                      variant="outlined"
                      onClick={() => navigate('/breeding/predictions')}
                      sx={{
                        color: 'white',
                        borderColor: 'rgba(255,255,255,0.5)',
                        '&:hover': { borderColor: 'white', bgcolor: 'rgba(255,255,255,0.1)' }
                      }}
                    >
                      View All
                    </CustomButton>
                  }
                  content={
                    <List>
                      {upcomingBirths.length > 0 ? (
                        upcomingBirths.map((record) => (
                          <ListItem key={record.id} divider>
                            <ListItemIcon>
                              <Avatar sx={{ bgcolor: theme.palette.info.main }}>
                                <Pets />
                              </Avatar>
                            </ListItemIcon>
                            <ListItemText
                              primary={`Female ID: ${record.femaleId} • Male ID: ${record.maleId}`}
                              secondary={`Due Date: ${format(parseISO(record.expectedDueDate!), 'dd MMM yyyy')} • Method: ${record.type === 'natural' ? 'Natural' : 'Artificial'}`}
                            />
                            <IconButton size="small" onClick={() => navigate(`/breeding/predictions`)}>
                              <ArrowForward fontSize="small" />
                            </IconButton>
                          </ListItem>
                        ))
                      ) : (
                        <ListItem>
                          <ListItemText primary="No upcoming births in the next 30 days" />
                        </ListItem>
                      )}
                    </List>
                  }
                />
              </motion.div>
            </Grid>
          </Grid>
        </motion.div>

      {/* Breeding Performance Radar Chart */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
      >
        <ModernChart
          title={translateModuleField ? translateModuleField('breeding_performance', "Breeding Performance Metrics") : "Breeding Performance Metrics"}
          subtitle={translateModuleField ? translateModuleField('breeding_performance_desc', "Key performance indicators for breeding program") : "Key performance indicators for breeding program"}
          data={mockBreedingPerformanceData}
          type="radar"
          dataKeys={['A']}
          xAxisDataKey="subject"
          height={350}
          accentColor={theme.palette.primary.main}
          allowChartTypeChange={false}
          module="breeding"
          tooltip={translateModuleField ? translateModuleField('breeding_performance_help', "Shows performance metrics across different breeding aspects") : "Shows performance metrics across different breeding aspects"}
          formatValue={(value) => `${value}%`}
          onRefresh={handleRefreshData}
        />
      </motion.div>

      {/* Radar Chart Interaction Feedback */}
      {radarTooltip.show && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card sx={{
            borderRadius: '12px',
            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
            mb: 4,
            overflow: 'hidden',
            background: `linear-gradient(135deg, ${alpha('#4A6FA5', 0.85)}, ${alpha('#3A5A8C', 0.75)})`,
            color: 'white'
          }}>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Typography variant="h6" fontWeight="bold" color="white">
                  Breeding Performance Details
                </Typography>
                <IconButton size="small" onClick={() => setRadarTooltip({ show: false, content: '' })} sx={{ color: 'white' }}>
                  <MoreVert />
                </IconButton>
              </Box>
              <Typography variant="body1" mt={1} color="white">
                {radarTooltip.content}
              </Typography>
              {selectedMetric && (
                <Box mt={2}>
                  <Typography variant="body2" color="rgba(255,255,255,0.8)" mb={1}>
                    Improvement Suggestions for {selectedMetric}:
                  </Typography>
                  <Typography variant="body2" color="white">
                    {selectedMetric === 'Success Rate' && 'Optimize nutrition and timing for better breeding success.'}
                    {selectedMetric === 'Conception Rate' && 'Consider adjusting insemination techniques and timing.'}
                    {selectedMetric === 'Calving Ease' && 'Review breeding pairs for genetic compatibility to improve calving ease.'}
                    {selectedMetric === 'Gestation Length' && 'Monitor and adjust nutrition during pregnancy for optimal gestation.'}
                    {selectedMetric === 'Calf Survival' && 'Improve post-birth care protocols and monitoring.'}
                    {selectedMetric === 'Dam Recovery' && 'Enhance post-partum care and nutrition for faster recovery.'}
                  </Typography>
                </Box>
              )}
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Breeding Records Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
      >
        <Card sx={{
          borderRadius: '12px',
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          mb: 4,
          overflow: 'hidden',
          background: `linear-gradient(135deg, ${alpha('#4A6FA5', 0.85)}, ${alpha('#3A5A8C', 0.75)})`,
        }}>
          <Box sx={{ p: 2, color: 'white', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6" fontWeight="bold" color="white">
              Breeding Records
            </Typography>
            <Box>
              <IconButton color="inherit" size="small">
                <Search />
              </IconButton>
              <IconButton color="inherit" size="small">
                <FilterList />
              </IconButton>
              <IconButton color="inherit" size="small">
                <MoreVert />
              </IconButton>
            </Box>
          </Box>
          <TableContainer>
            <Table>
              <TableHead sx={{ bgcolor: 'rgba(255,255,255,0.1)' }}>
                <TableRow>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>ID</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Female</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Male</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Breeding Date</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Expected Due Date</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Method</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Status</TableCell>
                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {mockBreedingRecords.map((record) => (
                  <TableRow key={record.id} hover sx={{ '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' } }}>
                    <TableCell sx={{ color: 'white' }}>{record.id}</TableCell>
                    <TableCell sx={{ color: 'white' }}>{`Animal ${record.femaleId}`}</TableCell>
                    <TableCell sx={{ color: 'white' }}>{`Animal ${record.maleId}`}</TableCell>
                    <TableCell sx={{ color: 'white' }}>{new Date(record.date).toLocaleDateString()}</TableCell>
                    <TableCell sx={{ color: 'white' }}>{new Date(record.expectedDueDate).toLocaleDateString()}</TableCell>
                    <TableCell>
                      <Chip
                        label={record.type === 'natural' ? 'Natural' : 'Artificial'}
                        sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={record.status.charAt(0).toUpperCase() + record.status.slice(1)}
                        sx={{
                          bgcolor: record.status === 'pending' ? 'rgba(255,193,7,0.2)' :
                                  record.status === 'confirmed' ? 'rgba(76,175,80,0.2)' :
                                  'rgba(244,67,54,0.2)',
                          color: 'white'
                        }}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      <IconButton size="small" onClick={() => navigate(`/breeding/records/${record.id}`)} sx={{ color: 'white' }}>
                        <ArrowForward fontSize="small" />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
        </Card>
      </motion.div>
      </Box>
    </StandardDashboard>
  );
};

// Wrap the component with our HOC to provide translation functions
export default withSubModuleTranslation(BreedingDashboard, 'breeding', 'dashboard');
