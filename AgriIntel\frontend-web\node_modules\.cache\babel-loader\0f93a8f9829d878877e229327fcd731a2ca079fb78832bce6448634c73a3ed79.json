{"ast": null, "code": "import React from'react';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AnimalDetailsPage=()=>{const{t}=useTranslation();return/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:t('animals.animalDetails')})}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Animal details page coming soon...\"})})]})});};export default AnimalDetailsPage;", "map": {"version": 3, "names": ["React", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "AnimalDetailsPage", "t", "className", "children"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/pages/animals/AnimalDetailsPage.tsx"], "sourcesContent": ["import React from 'react';\nimport { useTranslation } from 'react-i18next';\n\nconst AnimalDetailsPage: React.FC = () => {\n  const { t } = useTranslation();\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">\n            {t('animals.animalDetails')}\n          </h1>\n        </div>\n        <div className=\"card-body\">\n          <p className=\"text-gray-600\">\n            Animal details page coming soon...\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AnimalDetailsPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,iBAA2B,CAAGA,CAAA,GAAM,CACxC,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAE9B,mBACEE,IAAA,QAAKK,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBJ,KAAA,QAAKG,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBN,IAAA,QAAKK,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BN,IAAA,OAAIK,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC7CF,CAAC,CAAC,uBAAuB,CAAC,CACzB,CAAC,CACF,CAAC,cACNJ,IAAA,QAAKK,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBN,IAAA,MAAGK,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,oCAE7B,CAAG,CAAC,CACD,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}