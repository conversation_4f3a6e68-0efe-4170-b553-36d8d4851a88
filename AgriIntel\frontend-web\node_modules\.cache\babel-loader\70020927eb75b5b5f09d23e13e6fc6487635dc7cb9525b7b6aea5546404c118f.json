{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ConnectionPoolMetrics = void 0;\n/** @internal */\nclass ConnectionPoolMetrics {\n  constructor() {\n    this.txnConnections = 0;\n    this.cursorConnections = 0;\n    this.otherConnections = 0;\n  }\n  /**\n   * Mark a connection as pinned for a specific operation.\n   */\n  markPinned(pinType) {\n    if (pinType === ConnectionPoolMetrics.TXN) {\n      this.txnConnections += 1;\n    } else if (pinType === ConnectionPoolMetrics.CURSOR) {\n      this.cursorConnections += 1;\n    } else {\n      this.otherConnections += 1;\n    }\n  }\n  /**\n   * Unmark a connection as pinned for an operation.\n   */\n  markUnpinned(pinType) {\n    if (pinType === ConnectionPoolMetrics.TXN) {\n      this.txnConnections -= 1;\n    } else if (pinType === ConnectionPoolMetrics.CURSOR) {\n      this.cursorConnections -= 1;\n    } else {\n      this.otherConnections -= 1;\n    }\n  }\n  /**\n   * Return information about the cmap metrics as a string.\n   */\n  info(maxPoolSize) {\n    return 'Timed out while checking out a connection from connection pool: ' + `maxPoolSize: ${maxPoolSize}, ` + `connections in use by cursors: ${this.cursorConnections}, ` + `connections in use by transactions: ${this.txnConnections}, ` + `connections in use by other operations: ${this.otherConnections}`;\n  }\n  /**\n   * Reset the metrics to the initial values.\n   */\n  reset() {\n    this.txnConnections = 0;\n    this.cursorConnections = 0;\n    this.otherConnections = 0;\n  }\n}\nexports.ConnectionPoolMetrics = ConnectionPoolMetrics;\nConnectionPoolMetrics.TXN = 'txn';\nConnectionPoolMetrics.CURSOR = 'cursor';\nConnectionPoolMetrics.OTHER = 'other';", "map": {"version": 3, "names": ["ConnectionPoolMetrics", "constructor", "txnConnections", "cursorConnections", "otherConnections", "<PERSON><PERSON><PERSON><PERSON>", "pinType", "TXN", "CURSOR", "markUnpinned", "info", "maxPoolSize", "reset", "exports", "OTHER"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\metrics.ts"], "sourcesContent": ["/** @internal */\nexport class ConnectionPoolMetrics {\n  static readonly TXN = 'txn' as const;\n  static readonly CURSOR = 'cursor' as const;\n  static readonly OTHER = 'other' as const;\n\n  txnConnections = 0;\n  cursorConnections = 0;\n  otherConnections = 0;\n\n  /**\n   * Mark a connection as pinned for a specific operation.\n   */\n  markPinned(pinType: string): void {\n    if (pinType === ConnectionPoolMetrics.TXN) {\n      this.txnConnections += 1;\n    } else if (pinType === ConnectionPoolMetrics.CURSOR) {\n      this.cursorConnections += 1;\n    } else {\n      this.otherConnections += 1;\n    }\n  }\n\n  /**\n   * Unmark a connection as pinned for an operation.\n   */\n  markUnpinned(pinType: string): void {\n    if (pinType === ConnectionPoolMetrics.TXN) {\n      this.txnConnections -= 1;\n    } else if (pinType === ConnectionPoolMetrics.CURSOR) {\n      this.cursorConnections -= 1;\n    } else {\n      this.otherConnections -= 1;\n    }\n  }\n\n  /**\n   * Return information about the cmap metrics as a string.\n   */\n  info(maxPoolSize: number): string {\n    return (\n      'Timed out while checking out a connection from connection pool: ' +\n      `maxPoolSize: ${maxPoolSize}, ` +\n      `connections in use by cursors: ${this.cursorConnections}, ` +\n      `connections in use by transactions: ${this.txnConnections}, ` +\n      `connections in use by other operations: ${this.otherConnections}`\n    );\n  }\n\n  /**\n   * Reset the metrics to the initial values.\n   */\n  reset(): void {\n    this.txnConnections = 0;\n    this.cursorConnections = 0;\n    this.otherConnections = 0;\n  }\n}\n"], "mappings": ";;;;;;AAAA;AACA,MAAaA,qBAAqB;EAAlCC,YAAA;IAKE,KAAAC,cAAc,GAAG,CAAC;IAClB,KAAAC,iBAAiB,GAAG,CAAC;IACrB,KAAAC,gBAAgB,GAAG,CAAC;EAiDtB;EA/CE;;;EAGAC,UAAUA,CAACC,OAAe;IACxB,IAAIA,OAAO,KAAKN,qBAAqB,CAACO,GAAG,EAAE;MACzC,IAAI,CAACL,cAAc,IAAI,CAAC;IAC1B,CAAC,MAAM,IAAII,OAAO,KAAKN,qBAAqB,CAACQ,MAAM,EAAE;MACnD,IAAI,CAACL,iBAAiB,IAAI,CAAC;IAC7B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,IAAI,CAAC;IAC5B;EACF;EAEA;;;EAGAK,YAAYA,CAACH,OAAe;IAC1B,IAAIA,OAAO,KAAKN,qBAAqB,CAACO,GAAG,EAAE;MACzC,IAAI,CAACL,cAAc,IAAI,CAAC;IAC1B,CAAC,MAAM,IAAII,OAAO,KAAKN,qBAAqB,CAACQ,MAAM,EAAE;MACnD,IAAI,CAACL,iBAAiB,IAAI,CAAC;IAC7B,CAAC,MAAM;MACL,IAAI,CAACC,gBAAgB,IAAI,CAAC;IAC5B;EACF;EAEA;;;EAGAM,IAAIA,CAACC,WAAmB;IACtB,OACE,kEAAkE,GAClE,gBAAgBA,WAAW,IAAI,GAC/B,kCAAkC,IAAI,CAACR,iBAAiB,IAAI,GAC5D,uCAAuC,IAAI,CAACD,cAAc,IAAI,GAC9D,2CAA2C,IAAI,CAACE,gBAAgB,EAAE;EAEtE;EAEA;;;EAGAQ,KAAKA,CAAA;IACH,IAAI,CAACV,cAAc,GAAG,CAAC;IACvB,IAAI,CAACC,iBAAiB,GAAG,CAAC;IAC1B,IAAI,CAACC,gBAAgB,GAAG,CAAC;EAC3B;;AAvDFS,OAAA,CAAAb,qBAAA,GAAAA,qBAAA;AACkBA,qBAAA,CAAAO,GAAG,GAAG,KAAc;AACpBP,qBAAA,CAAAQ,MAAM,GAAG,QAAiB;AAC1BR,qBAAA,CAAAc,KAAK,GAAG,OAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}