{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { gridFilteredTopLevelRowCountSelector } from '../filter';\nimport { useGridLogger, useGridSelector, useGridApiMethod } from '../../utils';\nimport { useGridRegisterPipeProcessor } from '../../core/pipeProcessing';\nimport { gridPaginationRowCountSelector } from './gridPaginationSelector';\nimport { noRowCountInServerMode } from './gridPaginationUtils';\n\n/**\n * @requires useGridFilter (state)\n * @requires useGridDimensions (event) - can be after\n */\nexport const useGridRowCount = (apiRef, props) => {\n  var _props$initialState2;\n  const logger = useGridLogger(apiRef, 'useGridRowCount');\n  const visibleTopLevelRowCount = useGridSelector(apiRef, gridFilteredTopLevelRowCountSelector);\n  const rowCount = useGridSelector(apiRef, gridPaginationRowCountSelector);\n  apiRef.current.registerControlState({\n    stateId: 'paginationRowCount',\n    propModel: props.rowCount,\n    propOnChange: props.onRowCountChange,\n    stateSelector: gridPaginationRowCountSelector,\n    changeEvent: 'rowCountChange'\n  });\n\n  /**\n   * API METHODS\n   */\n  const setRowCount = React.useCallback(newRowCount => {\n    if (rowCount === newRowCount) {\n      return;\n    }\n    logger.debug(\"Setting 'rowCount' to\", newRowCount);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        rowCount: newRowCount\n      })\n    }));\n  }, [apiRef, logger, rowCount]);\n  const paginationRowCountApi = {\n    setRowCount\n  };\n  useGridApiMethod(apiRef, paginationRowCountApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    var _props$initialState;\n    const exportedRowCount = gridPaginationRowCountSelector(apiRef);\n    const shouldExportRowCount =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the `rowCount` is controlled\n    props.rowCount != null ||\n    // Always export if the `rowCount` has been initialized\n    ((_props$initialState = props.initialState) == null || (_props$initialState = _props$initialState.pagination) == null ? void 0 : _props$initialState.rowCount) != null;\n    if (!shouldExportRowCount) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      pagination: _extends({}, prevState.pagination, {\n        rowCount: exportedRowCount\n      })\n    });\n  }, [apiRef, props.rowCount, (_props$initialState2 = props.initialState) == null || (_props$initialState2 = _props$initialState2.pagination) == null ? void 0 : _props$initialState2.rowCount]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    var _context$stateToResto;\n    const restoredRowCount = (_context$stateToResto = context.stateToRestore.pagination) != null && _context$stateToResto.rowCount ? context.stateToRestore.pagination.rowCount : gridPaginationRowCountSelector(apiRef);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        rowCount: restoredRowCount\n      })\n    }));\n    return params;\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n\n  /**\n   * EFFECTS\n   */\n  React.useEffect(() => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (props.paginationMode === 'server' && props.rowCount == null) {\n        noRowCountInServerMode();\n      }\n    }\n  }, [props.rowCount, props.paginationMode]);\n  React.useEffect(() => {\n    if (props.paginationMode === 'client') {\n      apiRef.current.setRowCount(visibleTopLevelRowCount);\n    } else if (props.rowCount != null) {\n      apiRef.current.setRowCount(props.rowCount);\n    }\n  }, [apiRef, visibleTopLevelRowCount, props.paginationMode, props.rowCount]);\n};", "map": {"version": 3, "names": ["_extends", "React", "gridFilteredTopLevelRowCountSelector", "useGridLogger", "useGridSelector", "useGridApiMethod", "useGridRegisterPipeProcessor", "gridPaginationRowCountSelector", "noRowCountInServerMode", "useGridRowCount", "apiRef", "props", "_props$initialState2", "logger", "visibleTopLevelRowCount", "rowCount", "current", "registerControlState", "stateId", "propModel", "propOnChange", "onRowCountChange", "stateSelector", "changeEvent", "setRowCount", "useCallback", "newRowCount", "debug", "setState", "state", "pagination", "paginationRowCountApi", "stateExportPreProcessing", "prevState", "context", "_props$initialState", "exportedRowCount", "shouldExportRowCount", "exportOnlyDirtyModels", "initialState", "stateRestorePreProcessing", "params", "_context$stateToResto", "restoredRowCount", "stateToRestore", "useEffect", "process", "env", "NODE_ENV", "paginationMode"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/x-data-grid/hooks/features/pagination/useGridRowCount.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport * as React from 'react';\nimport { gridFilteredTopLevelRowCountSelector } from '../filter';\nimport { useGridLogger, useGridSelector, useGridApiMethod } from '../../utils';\nimport { useGridRegisterPipeProcessor } from '../../core/pipeProcessing';\nimport { gridPaginationRowCountSelector } from './gridPaginationSelector';\nimport { noRowCountInServerMode } from './gridPaginationUtils';\n\n/**\n * @requires useGridFilter (state)\n * @requires useGridDimensions (event) - can be after\n */\nexport const useGridRowCount = (apiRef, props) => {\n  var _props$initialState2;\n  const logger = useGridLogger(apiRef, 'useGridRowCount');\n  const visibleTopLevelRowCount = useGridSelector(apiRef, gridFilteredTopLevelRowCountSelector);\n  const rowCount = useGridSelector(apiRef, gridPaginationRowCountSelector);\n  apiRef.current.registerControlState({\n    stateId: 'paginationRowCount',\n    propModel: props.rowCount,\n    propOnChange: props.onRowCountChange,\n    stateSelector: gridPaginationRowCountSelector,\n    changeEvent: 'rowCountChange'\n  });\n\n  /**\n   * API METHODS\n   */\n  const setRowCount = React.useCallback(newRowCount => {\n    if (rowCount === newRowCount) {\n      return;\n    }\n    logger.debug(\"Setting 'rowCount' to\", newRowCount);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        rowCount: newRowCount\n      })\n    }));\n  }, [apiRef, logger, rowCount]);\n  const paginationRowCountApi = {\n    setRowCount\n  };\n  useGridApiMethod(apiRef, paginationRowCountApi, 'public');\n\n  /**\n   * PRE-PROCESSING\n   */\n  const stateExportPreProcessing = React.useCallback((prevState, context) => {\n    var _props$initialState;\n    const exportedRowCount = gridPaginationRowCountSelector(apiRef);\n    const shouldExportRowCount =\n    // Always export if the `exportOnlyDirtyModels` property is not activated\n    !context.exportOnlyDirtyModels ||\n    // Always export if the `rowCount` is controlled\n    props.rowCount != null ||\n    // Always export if the `rowCount` has been initialized\n    ((_props$initialState = props.initialState) == null || (_props$initialState = _props$initialState.pagination) == null ? void 0 : _props$initialState.rowCount) != null;\n    if (!shouldExportRowCount) {\n      return prevState;\n    }\n    return _extends({}, prevState, {\n      pagination: _extends({}, prevState.pagination, {\n        rowCount: exportedRowCount\n      })\n    });\n  }, [apiRef, props.rowCount, (_props$initialState2 = props.initialState) == null || (_props$initialState2 = _props$initialState2.pagination) == null ? void 0 : _props$initialState2.rowCount]);\n  const stateRestorePreProcessing = React.useCallback((params, context) => {\n    var _context$stateToResto;\n    const restoredRowCount = (_context$stateToResto = context.stateToRestore.pagination) != null && _context$stateToResto.rowCount ? context.stateToRestore.pagination.rowCount : gridPaginationRowCountSelector(apiRef);\n    apiRef.current.setState(state => _extends({}, state, {\n      pagination: _extends({}, state.pagination, {\n        rowCount: restoredRowCount\n      })\n    }));\n    return params;\n  }, [apiRef]);\n  useGridRegisterPipeProcessor(apiRef, 'exportState', stateExportPreProcessing);\n  useGridRegisterPipeProcessor(apiRef, 'restoreState', stateRestorePreProcessing);\n\n  /**\n   * EFFECTS\n   */\n  React.useEffect(() => {\n    if (process.env.NODE_ENV !== 'production') {\n      if (props.paginationMode === 'server' && props.rowCount == null) {\n        noRowCountInServerMode();\n      }\n    }\n  }, [props.rowCount, props.paginationMode]);\n  React.useEffect(() => {\n    if (props.paginationMode === 'client') {\n      apiRef.current.setRowCount(visibleTopLevelRowCount);\n    } else if (props.rowCount != null) {\n      apiRef.current.setRowCount(props.rowCount);\n    }\n  }, [apiRef, visibleTopLevelRowCount, props.paginationMode, props.rowCount]);\n};"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,oCAAoC,QAAQ,WAAW;AAChE,SAASC,aAAa,EAAEC,eAAe,EAAEC,gBAAgB,QAAQ,aAAa;AAC9E,SAASC,4BAA4B,QAAQ,2BAA2B;AACxE,SAASC,8BAA8B,QAAQ,0BAA0B;AACzE,SAASC,sBAAsB,QAAQ,uBAAuB;;AAE9D;AACA;AACA;AACA;AACA,OAAO,MAAMC,eAAe,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAChD,IAAIC,oBAAoB;EACxB,MAAMC,MAAM,GAAGV,aAAa,CAACO,MAAM,EAAE,iBAAiB,CAAC;EACvD,MAAMI,uBAAuB,GAAGV,eAAe,CAACM,MAAM,EAAER,oCAAoC,CAAC;EAC7F,MAAMa,QAAQ,GAAGX,eAAe,CAACM,MAAM,EAAEH,8BAA8B,CAAC;EACxEG,MAAM,CAACM,OAAO,CAACC,oBAAoB,CAAC;IAClCC,OAAO,EAAE,oBAAoB;IAC7BC,SAAS,EAAER,KAAK,CAACI,QAAQ;IACzBK,YAAY,EAAET,KAAK,CAACU,gBAAgB;IACpCC,aAAa,EAAEf,8BAA8B;IAC7CgB,WAAW,EAAE;EACf,CAAC,CAAC;;EAEF;AACF;AACA;EACE,MAAMC,WAAW,GAAGvB,KAAK,CAACwB,WAAW,CAACC,WAAW,IAAI;IACnD,IAAIX,QAAQ,KAAKW,WAAW,EAAE;MAC5B;IACF;IACAb,MAAM,CAACc,KAAK,CAAC,uBAAuB,EAAED,WAAW,CAAC;IAClDhB,MAAM,CAACM,OAAO,CAACY,QAAQ,CAACC,KAAK,IAAI7B,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,EAAE;MACnDC,UAAU,EAAE9B,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,CAACC,UAAU,EAAE;QACzCf,QAAQ,EAAEW;MACZ,CAAC;IACH,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,CAAChB,MAAM,EAAEG,MAAM,EAAEE,QAAQ,CAAC,CAAC;EAC9B,MAAMgB,qBAAqB,GAAG;IAC5BP;EACF,CAAC;EACDnB,gBAAgB,CAACK,MAAM,EAAEqB,qBAAqB,EAAE,QAAQ,CAAC;;EAEzD;AACF;AACA;EACE,MAAMC,wBAAwB,GAAG/B,KAAK,CAACwB,WAAW,CAAC,CAACQ,SAAS,EAAEC,OAAO,KAAK;IACzE,IAAIC,mBAAmB;IACvB,MAAMC,gBAAgB,GAAG7B,8BAA8B,CAACG,MAAM,CAAC;IAC/D,MAAM2B,oBAAoB;IAC1B;IACA,CAACH,OAAO,CAACI,qBAAqB;IAC9B;IACA3B,KAAK,CAACI,QAAQ,IAAI,IAAI;IACtB;IACA,CAAC,CAACoB,mBAAmB,GAAGxB,KAAK,CAAC4B,YAAY,KAAK,IAAI,IAAI,CAACJ,mBAAmB,GAAGA,mBAAmB,CAACL,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGK,mBAAmB,CAACpB,QAAQ,KAAK,IAAI;IACtK,IAAI,CAACsB,oBAAoB,EAAE;MACzB,OAAOJ,SAAS;IAClB;IACA,OAAOjC,QAAQ,CAAC,CAAC,CAAC,EAAEiC,SAAS,EAAE;MAC7BH,UAAU,EAAE9B,QAAQ,CAAC,CAAC,CAAC,EAAEiC,SAAS,CAACH,UAAU,EAAE;QAC7Cf,QAAQ,EAAEqB;MACZ,CAAC;IACH,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC1B,MAAM,EAAEC,KAAK,CAACI,QAAQ,EAAE,CAACH,oBAAoB,GAAGD,KAAK,CAAC4B,YAAY,KAAK,IAAI,IAAI,CAAC3B,oBAAoB,GAAGA,oBAAoB,CAACkB,UAAU,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGlB,oBAAoB,CAACG,QAAQ,CAAC,CAAC;EAC9L,MAAMyB,yBAAyB,GAAGvC,KAAK,CAACwB,WAAW,CAAC,CAACgB,MAAM,EAAEP,OAAO,KAAK;IACvE,IAAIQ,qBAAqB;IACzB,MAAMC,gBAAgB,GAAG,CAACD,qBAAqB,GAAGR,OAAO,CAACU,cAAc,CAACd,UAAU,KAAK,IAAI,IAAIY,qBAAqB,CAAC3B,QAAQ,GAAGmB,OAAO,CAACU,cAAc,CAACd,UAAU,CAACf,QAAQ,GAAGR,8BAA8B,CAACG,MAAM,CAAC;IACpNA,MAAM,CAACM,OAAO,CAACY,QAAQ,CAACC,KAAK,IAAI7B,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,EAAE;MACnDC,UAAU,EAAE9B,QAAQ,CAAC,CAAC,CAAC,EAAE6B,KAAK,CAACC,UAAU,EAAE;QACzCf,QAAQ,EAAE4B;MACZ,CAAC;IACH,CAAC,CAAC,CAAC;IACH,OAAOF,MAAM;EACf,CAAC,EAAE,CAAC/B,MAAM,CAAC,CAAC;EACZJ,4BAA4B,CAACI,MAAM,EAAE,aAAa,EAAEsB,wBAAwB,CAAC;EAC7E1B,4BAA4B,CAACI,MAAM,EAAE,cAAc,EAAE8B,yBAAyB,CAAC;;EAE/E;AACF;AACA;EACEvC,KAAK,CAAC4C,SAAS,CAAC,MAAM;IACpB,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,IAAIrC,KAAK,CAACsC,cAAc,KAAK,QAAQ,IAAItC,KAAK,CAACI,QAAQ,IAAI,IAAI,EAAE;QAC/DP,sBAAsB,CAAC,CAAC;MAC1B;IACF;EACF,CAAC,EAAE,CAACG,KAAK,CAACI,QAAQ,EAAEJ,KAAK,CAACsC,cAAc,CAAC,CAAC;EAC1ChD,KAAK,CAAC4C,SAAS,CAAC,MAAM;IACpB,IAAIlC,KAAK,CAACsC,cAAc,KAAK,QAAQ,EAAE;MACrCvC,MAAM,CAACM,OAAO,CAACQ,WAAW,CAACV,uBAAuB,CAAC;IACrD,CAAC,MAAM,IAAIH,KAAK,CAACI,QAAQ,IAAI,IAAI,EAAE;MACjCL,MAAM,CAACM,OAAO,CAACQ,WAAW,CAACb,KAAK,CAACI,QAAQ,CAAC;IAC5C;EACF,CAAC,EAAE,CAACL,MAAM,EAAEI,uBAAuB,EAAEH,KAAK,CAACsC,cAAc,EAAEtC,KAAK,CAACI,QAAQ,CAAC,CAAC;AAC7E,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}