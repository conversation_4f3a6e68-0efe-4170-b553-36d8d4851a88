{"ast": null, "code": "import { scheduleMicrotask } from './utils'; // TYPES\n\n// CLASS\nexport var NotifyManager = /*#__PURE__*/function () {\n  function NotifyManager() {\n    this.queue = [];\n    this.transactions = 0;\n    this.notifyFn = function (callback) {\n      callback();\n    };\n    this.batchNotifyFn = function (callback) {\n      callback();\n    };\n  }\n  var _proto = NotifyManager.prototype;\n  _proto.batch = function batch(callback) {\n    var result;\n    this.transactions++;\n    try {\n      result = callback();\n    } finally {\n      this.transactions--;\n      if (!this.transactions) {\n        this.flush();\n      }\n    }\n    return result;\n  };\n  _proto.schedule = function schedule(callback) {\n    var _this = this;\n    if (this.transactions) {\n      this.queue.push(callback);\n    } else {\n      scheduleMicrotask(function () {\n        _this.notifyFn(callback);\n      });\n    }\n  }\n  /**\n   * All calls to the wrapped function will be batched.\n   */;\n  _proto.batchCalls = function batchCalls(callback) {\n    var _this2 = this;\n    return function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n      _this2.schedule(function () {\n        callback.apply(void 0, args);\n      });\n    };\n  };\n  _proto.flush = function flush() {\n    var _this3 = this;\n    var queue = this.queue;\n    this.queue = [];\n    if (queue.length) {\n      scheduleMicrotask(function () {\n        _this3.batchNotifyFn(function () {\n          queue.forEach(function (callback) {\n            _this3.notifyFn(callback);\n          });\n        });\n      });\n    }\n  }\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */;\n  _proto.setNotifyFunction = function setNotifyFunction(fn) {\n    this.notifyFn = fn;\n  }\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */;\n  _proto.setBatchNotifyFunction = function setBatchNotifyFunction(fn) {\n    this.batchNotifyFn = fn;\n  };\n  return NotifyManager;\n}(); // SINGLETON\n\nexport var notifyManager = new NotifyManager();", "map": {"version": 3, "names": ["scheduleMicrotask", "NotifyManager", "queue", "transactions", "notifyFn", "callback", "batchNotifyFn", "_proto", "prototype", "batch", "result", "flush", "schedule", "_this", "push", "batchCalls", "_this2", "_len", "arguments", "length", "args", "Array", "_key", "apply", "_this3", "for<PERSON>ach", "setNotifyFunction", "fn", "setBatchNotifyFunction", "notify<PERSON><PERSON>ger"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-query/es/core/notifyManager.js"], "sourcesContent": ["import { scheduleMicrotask } from './utils'; // TYPES\n\n// CLASS\nexport var NotifyManager = /*#__PURE__*/function () {\n  function NotifyManager() {\n    this.queue = [];\n    this.transactions = 0;\n\n    this.notifyFn = function (callback) {\n      callback();\n    };\n\n    this.batchNotifyFn = function (callback) {\n      callback();\n    };\n  }\n\n  var _proto = NotifyManager.prototype;\n\n  _proto.batch = function batch(callback) {\n    var result;\n    this.transactions++;\n\n    try {\n      result = callback();\n    } finally {\n      this.transactions--;\n\n      if (!this.transactions) {\n        this.flush();\n      }\n    }\n\n    return result;\n  };\n\n  _proto.schedule = function schedule(callback) {\n    var _this = this;\n\n    if (this.transactions) {\n      this.queue.push(callback);\n    } else {\n      scheduleMicrotask(function () {\n        _this.notifyFn(callback);\n      });\n    }\n  }\n  /**\n   * All calls to the wrapped function will be batched.\n   */\n  ;\n\n  _proto.batchCalls = function batchCalls(callback) {\n    var _this2 = this;\n\n    return function () {\n      for (var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++) {\n        args[_key] = arguments[_key];\n      }\n\n      _this2.schedule(function () {\n        callback.apply(void 0, args);\n      });\n    };\n  };\n\n  _proto.flush = function flush() {\n    var _this3 = this;\n\n    var queue = this.queue;\n    this.queue = [];\n\n    if (queue.length) {\n      scheduleMicrotask(function () {\n        _this3.batchNotifyFn(function () {\n          queue.forEach(function (callback) {\n            _this3.notifyFn(callback);\n          });\n        });\n      });\n    }\n  }\n  /**\n   * Use this method to set a custom notify function.\n   * This can be used to for example wrap notifications with `React.act` while running tests.\n   */\n  ;\n\n  _proto.setNotifyFunction = function setNotifyFunction(fn) {\n    this.notifyFn = fn;\n  }\n  /**\n   * Use this method to set a custom function to batch notifications together into a single tick.\n   * By default React Query will use the batch function provided by ReactDOM or React Native.\n   */\n  ;\n\n  _proto.setBatchNotifyFunction = function setBatchNotifyFunction(fn) {\n    this.batchNotifyFn = fn;\n  };\n\n  return NotifyManager;\n}(); // SINGLETON\n\nexport var notifyManager = new NotifyManager();"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,SAAS,CAAC,CAAC;;AAE7C;AACA,OAAO,IAAIC,aAAa,GAAG,aAAa,YAAY;EAClD,SAASA,aAAaA,CAAA,EAAG;IACvB,IAAI,CAACC,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,YAAY,GAAG,CAAC;IAErB,IAAI,CAACC,QAAQ,GAAG,UAAUC,QAAQ,EAAE;MAClCA,QAAQ,CAAC,CAAC;IACZ,CAAC;IAED,IAAI,CAACC,aAAa,GAAG,UAAUD,QAAQ,EAAE;MACvCA,QAAQ,CAAC,CAAC;IACZ,CAAC;EACH;EAEA,IAAIE,MAAM,GAAGN,aAAa,CAACO,SAAS;EAEpCD,MAAM,CAACE,KAAK,GAAG,SAASA,KAAKA,CAACJ,QAAQ,EAAE;IACtC,IAAIK,MAAM;IACV,IAAI,CAACP,YAAY,EAAE;IAEnB,IAAI;MACFO,MAAM,GAAGL,QAAQ,CAAC,CAAC;IACrB,CAAC,SAAS;MACR,IAAI,CAACF,YAAY,EAAE;MAEnB,IAAI,CAAC,IAAI,CAACA,YAAY,EAAE;QACtB,IAAI,CAACQ,KAAK,CAAC,CAAC;MACd;IACF;IAEA,OAAOD,MAAM;EACf,CAAC;EAEDH,MAAM,CAACK,QAAQ,GAAG,SAASA,QAAQA,CAACP,QAAQ,EAAE;IAC5C,IAAIQ,KAAK,GAAG,IAAI;IAEhB,IAAI,IAAI,CAACV,YAAY,EAAE;MACrB,IAAI,CAACD,KAAK,CAACY,IAAI,CAACT,QAAQ,CAAC;IAC3B,CAAC,MAAM;MACLL,iBAAiB,CAAC,YAAY;QAC5Ba,KAAK,CAACT,QAAQ,CAACC,QAAQ,CAAC;MAC1B,CAAC,CAAC;IACJ;EACF;EACA;AACF;AACA,KAFE;EAKAE,MAAM,CAACQ,UAAU,GAAG,SAASA,UAAUA,CAACV,QAAQ,EAAE;IAChD,IAAIW,MAAM,GAAG,IAAI;IAEjB,OAAO,YAAY;MACjB,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,IAAI,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;QACvFF,IAAI,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;MAC9B;MAEAN,MAAM,CAACJ,QAAQ,CAAC,YAAY;QAC1BP,QAAQ,CAACkB,KAAK,CAAC,KAAK,CAAC,EAAEH,IAAI,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC;EACH,CAAC;EAEDb,MAAM,CAACI,KAAK,GAAG,SAASA,KAAKA,CAAA,EAAG;IAC9B,IAAIa,MAAM,GAAG,IAAI;IAEjB,IAAItB,KAAK,GAAG,IAAI,CAACA,KAAK;IACtB,IAAI,CAACA,KAAK,GAAG,EAAE;IAEf,IAAIA,KAAK,CAACiB,MAAM,EAAE;MAChBnB,iBAAiB,CAAC,YAAY;QAC5BwB,MAAM,CAAClB,aAAa,CAAC,YAAY;UAC/BJ,KAAK,CAACuB,OAAO,CAAC,UAAUpB,QAAQ,EAAE;YAChCmB,MAAM,CAACpB,QAAQ,CAACC,QAAQ,CAAC;UAC3B,CAAC,CAAC;QACJ,CAAC,CAAC;MACJ,CAAC,CAAC;IACJ;EACF;EACA;AACF;AACA;AACA,KAHE;EAMAE,MAAM,CAACmB,iBAAiB,GAAG,SAASA,iBAAiBA,CAACC,EAAE,EAAE;IACxD,IAAI,CAACvB,QAAQ,GAAGuB,EAAE;EACpB;EACA;AACF;AACA;AACA,KAHE;EAMApB,MAAM,CAACqB,sBAAsB,GAAG,SAASA,sBAAsBA,CAACD,EAAE,EAAE;IAClE,IAAI,CAACrB,aAAa,GAAGqB,EAAE;EACzB,CAAC;EAED,OAAO1B,aAAa;AACtB,CAAC,CAAC,CAAC,CAAC,CAAC;;AAEL,OAAO,IAAI4B,aAAa,GAAG,IAAI5B,aAAa,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}