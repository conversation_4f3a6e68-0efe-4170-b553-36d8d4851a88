{"ast": null, "code": "import { useEffect as s, useState as o } from \"react\";\nimport { disposables as t } from '../utils/disposables.js';\nfunction p() {\n  let [e] = o(t);\n  return s(() => () => e.dispose(), [e]), e;\n}\nexport { p as useDisposables };", "map": {"version": 3, "names": ["useEffect", "s", "useState", "o", "disposables", "t", "p", "e", "dispose", "useDisposables"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/hooks/use-disposables.js"], "sourcesContent": ["import{useEffect as s,useState as o}from\"react\";import{disposables as t}from'../utils/disposables.js';function p(){let[e]=o(t);return s(()=>()=>e.dispose(),[e]),e}export{p as useDisposables};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,IAAG,CAACC,CAAC,CAAC,GAACJ,CAAC,CAACE,CAAC,CAAC;EAAC,OAAOJ,CAAC,CAAC,MAAI,MAAIM,CAAC,CAACC,OAAO,CAAC,CAAC,EAAC,CAACD,CAAC,CAAC,CAAC,EAACA,CAAC;AAAA;AAAC,SAAOD,CAAC,IAAIG,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}