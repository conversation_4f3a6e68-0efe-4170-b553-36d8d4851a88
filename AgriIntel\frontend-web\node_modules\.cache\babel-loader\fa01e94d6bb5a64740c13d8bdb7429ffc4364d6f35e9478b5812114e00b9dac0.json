{"ast": null, "code": "\"use strict\";\n\nconst getCodePoint = character => character.codePointAt(0);\nconst first = x => x[0];\nconst last = x => x[x.length - 1];\nfunction toCodePoints(input) {\n  const codepoints = [];\n  const size = input.length;\n  for (let i = 0; i < size; i += 1) {\n    const before = input.charCodeAt(i);\n    if (before >= 0xd800 && before <= 0xdbff && size > i + 1) {\n      const next = input.charCodeAt(i + 1);\n      if (next >= 0xdc00 && next <= 0xdfff) {\n        codepoints.push((before - 0xd800) * 0x400 + next - 0xdc00 + 0x10000);\n        i += 1;\n        continue;\n      }\n    }\n    codepoints.push(before);\n  }\n  return codepoints;\n}\nfunction saslprep({\n  unassigned_code_points,\n  commonly_mapped_to_nothing,\n  non_ASCII_space_characters,\n  prohibited_characters,\n  bidirectional_r_al,\n  bidirectional_l\n}, input, opts = {}) {\n  const mapping2space = non_ASCII_space_characters;\n  const mapping2nothing = commonly_mapped_to_nothing;\n  if (typeof input !== 'string') {\n    throw new TypeError('Expected string.');\n  }\n  if (input.length === 0) {\n    return '';\n  }\n  const mapped_input = toCodePoints(input).map(character => mapping2space.get(character) ? 0x20 : character).filter(character => !mapping2nothing.get(character));\n  const normalized_input = String.fromCodePoint.apply(null, mapped_input).normalize('NFKC');\n  const normalized_map = toCodePoints(normalized_input);\n  const hasProhibited = normalized_map.some(character => prohibited_characters.get(character));\n  if (hasProhibited) {\n    throw new Error('Prohibited character, see https://tools.ietf.org/html/rfc4013#section-2.3');\n  }\n  if (opts.allowUnassigned !== true) {\n    const hasUnassigned = normalized_map.some(character => unassigned_code_points.get(character));\n    if (hasUnassigned) {\n      throw new Error('Unassigned code point, see https://tools.ietf.org/html/rfc4013#section-2.5');\n    }\n  }\n  const hasBidiRAL = normalized_map.some(character => bidirectional_r_al.get(character));\n  const hasBidiL = normalized_map.some(character => bidirectional_l.get(character));\n  if (hasBidiRAL && hasBidiL) {\n    throw new Error('String must not contain RandALCat and LCat at the same time,' + ' see https://tools.ietf.org/html/rfc3454#section-6');\n  }\n  const isFirstBidiRAL = bidirectional_r_al.get(getCodePoint(first(normalized_input)));\n  const isLastBidiRAL = bidirectional_r_al.get(getCodePoint(last(normalized_input)));\n  if (hasBidiRAL && !(isFirstBidiRAL && isLastBidiRAL)) {\n    throw new Error('Bidirectional RandALCat character must be the first and the last' + ' character of the string, see https://tools.ietf.org/html/rfc3454#section-6');\n  }\n  return normalized_input;\n}\nsaslprep.saslprep = saslprep;\nsaslprep.default = saslprep;\nmodule.exports = saslprep;", "map": {"version": 3, "names": ["getCodePoint", "character", "codePointAt", "first", "x", "last", "length", "toCodePoints", "input", "codepoints", "size", "i", "before", "charCodeAt", "next", "push", "saslprep", "unassigned_code_points", "commonly_mapped_to_nothing", "non_ASCII_space_characters", "prohibited_characters", "bidirectional_r_al", "bidirectional_l", "opts", "mapping2space", "mapping2nothing", "TypeError", "mapped_input", "map", "get", "filter", "normalized_input", "String", "fromCodePoint", "apply", "normalize", "normalized_map", "hasProhibited", "some", "Error", "allowUnassigned", "hasUnassigned", "hasBidiRAL", "hasBidiL", "isFirstBidiRAL", "isLastBidiRAL", "default", "module", "exports"], "sources": ["../src/index.ts"], "sourcesContent": [null], "mappings": ";;AAGA,MAAMA,YAAY,GAAIC,SAAiB,IAAKA,SAAS,CAACC,WAAW,CAAC,CAAC,CAAC;AACpE,MAAMC,KAAK,GAA8BC,CAAI,IAAgBA,CAAC,CAAC,CAAC,CAAC;AACjE,MAAMC,IAAI,GAA8BD,CAAI,IAAgBA,CAAC,CAACA,CAAC,CAACE,MAAM,GAAG,CAAC,CAAC;AAO3E,SAASC,YAAYA,CAACC,KAAa;EACjC,MAAMC,UAAU,GAAG,EAAE;EACrB,MAAMC,IAAI,GAAGF,KAAK,CAACF,MAAM;EAEzB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,IAAI,EAAEC,CAAC,IAAI,CAAC,EAAE;IAChC,MAAMC,MAAM,GAAGJ,KAAK,CAACK,UAAU,CAACF,CAAC,CAAC;IAElC,IAAIC,MAAM,IAAI,MAAM,IAAIA,MAAM,IAAI,MAAM,IAAIF,IAAI,GAAGC,CAAC,GAAG,CAAC,EAAE;MACxD,MAAMG,IAAI,GAAGN,KAAK,CAACK,UAAU,CAACF,CAAC,GAAG,CAAC,CAAC;MAEpC,IAAIG,IAAI,IAAI,MAAM,IAAIA,IAAI,IAAI,MAAM,EAAE;QACpCL,UAAU,CAACM,IAAI,CAAC,CAACH,MAAM,GAAG,MAAM,IAAI,KAAK,GAAGE,IAAI,GAAG,MAAM,GAAG,OAAO,CAAC;QACpEH,CAAC,IAAI,CAAC;QACN;MACF;IACF;IAEAF,UAAU,CAACM,IAAI,CAACH,MAAM,CAAC;EACzB;EAEA,OAAOH,UAAU;AACnB;AAKA,SAASO,QAAQA,CACf;EACEC,sBAAsB;EACtBC,0BAA0B;EAC1BC,0BAA0B;EAC1BC,qBAAqB;EACrBC,kBAAkB;EAClBC;AAAe,CAC2B,EAC5Cd,KAAa,EACbe,IAAA,GAAsC,EAAE;EAQxC,MAAMC,aAAa,GAAGL,0BAA0B;EAMhD,MAAMM,eAAe,GAAGP,0BAA0B;EAElD,IAAI,OAAOV,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAM,IAAIkB,SAAS,CAAC,kBAAkB,CAAC;EACzC;EAEA,IAAIlB,KAAK,CAACF,MAAM,KAAK,CAAC,EAAE;IACtB,OAAO,EAAE;EACX;EAGA,MAAMqB,YAAY,GAAGpB,YAAY,CAACC,KAAK,CAAC,CAErCoB,GAAG,CAAE3B,SAAS,IAAMuB,aAAa,CAACK,GAAG,CAAC5B,SAAS,CAAC,GAAG,IAAI,GAAGA,SAAU,CAAC,CAErE6B,MAAM,CAAE7B,SAAS,IAAK,CAACwB,eAAe,CAACI,GAAG,CAAC5B,SAAS,CAAC,CAAC;EAGzD,MAAM8B,gBAAgB,GAAGC,MAAM,CAACC,aAAa,CAC1CC,KAAK,CAAC,IAAI,EAAEP,YAAY,CAAC,CACzBQ,SAAS,CAAC,MAAM,CAAC;EAEpB,MAAMC,cAAc,GAAG7B,YAAY,CAACwB,gBAAgB,CAAC;EAGrD,MAAMM,aAAa,GAAGD,cAAc,CAACE,IAAI,CAAErC,SAAS,IAClDmB,qBAAqB,CAACS,GAAG,CAAC5B,SAAS,CAAC,CACrC;EAED,IAAIoC,aAAa,EAAE;IACjB,MAAM,IAAIE,KAAK,CACb,2EAA2E,CAC5E;EACH;EAGA,IAAIhB,IAAI,CAACiB,eAAe,KAAK,IAAI,EAAE;IACjC,MAAMC,aAAa,GAAGL,cAAc,CAACE,IAAI,CAAErC,SAAS,IAClDgB,sBAAsB,CAACY,GAAG,CAAC5B,SAAS,CAAC,CACtC;IAED,IAAIwC,aAAa,EAAE;MACjB,MAAM,IAAIF,KAAK,CACb,4EAA4E,CAC7E;IACH;EACF;EAIA,MAAMG,UAAU,GAAGN,cAAc,CAACE,IAAI,CAAErC,SAAS,IAC/CoB,kBAAkB,CAACQ,GAAG,CAAC5B,SAAS,CAAC,CAClC;EAED,MAAM0C,QAAQ,GAAGP,cAAc,CAACE,IAAI,CAAErC,SAAS,IAC7CqB,eAAe,CAACO,GAAG,CAAC5B,SAAS,CAAC,CAC/B;EAID,IAAIyC,UAAU,IAAIC,QAAQ,EAAE;IAC1B,MAAM,IAAIJ,KAAK,CACb,8DAA8D,GAC5D,oDAAoD,CACvD;EACH;EAQA,MAAMK,cAAc,GAAGvB,kBAAkB,CAACQ,GAAG,CAC3C7B,YAAY,CAACG,KAAK,CAAC4B,gBAAgB,CAAC,CAAE,CACvC;EACD,MAAMc,aAAa,GAAGxB,kBAAkB,CAACQ,GAAG,CAC1C7B,YAAY,CAACK,IAAI,CAAC0B,gBAAgB,CAAC,CAAE,CACtC;EAED,IAAIW,UAAU,IAAI,EAAEE,cAAc,IAAIC,aAAa,CAAC,EAAE;IACpD,MAAM,IAAIN,KAAK,CACb,kEAAkE,GAChE,6EAA6E,CAChF;EACH;EAEA,OAAOR,gBAAgB;AACzB;AAEAf,QAAQ,CAACA,QAAQ,GAAGA,QAAQ;AAC5BA,QAAQ,CAAC8B,OAAO,GAAG9B,QAAQ;AAC3B+B,MAAA,CAAAC,OAAA,GAAShC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}