{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.loadAWSCredentials = loadAWSCredentials;\nconst aws_temporary_credentials_1 = require(\"../../cmap/auth/aws_temporary_credentials\");\n/**\n * @internal\n */\nasync function loadAWSCredentials(kmsProviders, provider) {\n  const credentialProvider = new aws_temporary_credentials_1.AWSSDKCredentialProvider(provider);\n  // We shouldn't ever receive a response from the AWS SDK that doesn't have a `SecretAccessKey`\n  // or `AccessKeyId`.  However, TS says these fields are optional.  We provide empty strings\n  // and let libmongocrypt error if we're unable to fetch the required keys.\n  const {\n    SecretAccessKey = '',\n    AccessKeyId = '',\n    Token\n  } = await credentialProvider.getCredentials();\n  const aws = {\n    secretAccessKey: SecretAccessKey,\n    accessKeyId: AccessKeyId\n  };\n  // the AWS session token is only required for temporary credentials so only attach it to the\n  // result if it's present in the response from the aws sdk\n  Token != null && (aws.sessionToken = Token);\n  return {\n    ...kmsProviders,\n    aws\n  };\n}", "map": {"version": 3, "names": ["exports", "loadAWSCredentials", "aws_temporary_credentials_1", "require", "kmsProviders", "provider", "credentialProvider", "AWSSDKCredentialProvider", "SecretAccess<PERSON>ey", "AccessKeyId", "Token", "getCredentials", "aws", "secretAccessKey", "accessKeyId", "sessionToken"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\client-side-encryption\\providers\\aws.ts"], "sourcesContent": ["import {\n  type AWSCredentialProvider,\n  AWSSDKCredentialProvider\n} from '../../cmap/auth/aws_temporary_credentials';\nimport { type KMSProviders } from '.';\n\n/**\n * @internal\n */\nexport async function loadAWSCredentials(\n  kmsProviders: KMSProviders,\n  provider?: AWSCredentialProvider\n): Promise<KMSProviders> {\n  const credentialProvider = new AWSSDKCredentialProvider(provider);\n\n  // We shouldn't ever receive a response from the AWS SDK that doesn't have a `SecretAccessKey`\n  // or `AccessKeyId`.  However, TS says these fields are optional.  We provide empty strings\n  // and let libmongocrypt error if we're unable to fetch the required keys.\n  const {\n    SecretAccessKey = '',\n    AccessKeyId = '',\n    Token\n  } = await credentialProvider.getCredentials();\n  const aws: NonNullable<KMSProviders['aws']> = {\n    secretAccessKey: SecretAccessKey,\n    accessKeyId: AccessKeyId\n  };\n  // the AWS session token is only required for temporary credentials so only attach it to the\n  // result if it's present in the response from the aws sdk\n  Token != null && (aws.sessionToken = Token);\n\n  return { ...kmsProviders, aws };\n}\n"], "mappings": ";;;;;AASAA,OAAA,CAAAC,kBAAA,GAAAA,kBAAA;AATA,MAAAC,2BAAA,GAAAC,OAAA;AAMA;;;AAGO,eAAeF,kBAAkBA,CACtCG,YAA0B,EAC1BC,QAAgC;EAEhC,MAAMC,kBAAkB,GAAG,IAAIJ,2BAAA,CAAAK,wBAAwB,CAACF,QAAQ,CAAC;EAEjE;EACA;EACA;EACA,MAAM;IACJG,eAAe,GAAG,EAAE;IACpBC,WAAW,GAAG,EAAE;IAChBC;EAAK,CACN,GAAG,MAAMJ,kBAAkB,CAACK,cAAc,EAAE;EAC7C,MAAMC,GAAG,GAAqC;IAC5CC,eAAe,EAAEL,eAAe;IAChCM,WAAW,EAAEL;GACd;EACD;EACA;EACAC,KAAK,IAAI,IAAI,KAAKE,GAAG,CAACG,YAAY,GAAGL,KAAK,CAAC;EAE3C,OAAO;IAAE,GAAGN,YAAY;IAAEQ;EAAG,CAAE;AACjC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}