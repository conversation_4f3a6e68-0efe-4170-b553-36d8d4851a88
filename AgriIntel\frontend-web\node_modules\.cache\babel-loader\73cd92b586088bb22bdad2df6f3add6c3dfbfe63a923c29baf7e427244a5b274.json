{"ast": null, "code": "\"use strict\";\n\nconst {\n  utf8Encode,\n  utf8DecodeWithoutBOM\n} = require(\"./encoding\");\nconst {\n  percentDecodeBytes,\n  utf8PercentEncodeString,\n  isURLEncodedPercentEncode\n} = require(\"./percent-encoding\");\nfunction p(char) {\n  return char.codePointAt(0);\n}\n\n// https://url.spec.whatwg.org/#concept-urlencoded-parser\nfunction parseUrlencoded(input) {\n  const sequences = strictlySplitByteSequence(input, p(\"&\"));\n  const output = [];\n  for (const bytes of sequences) {\n    if (bytes.length === 0) {\n      continue;\n    }\n    let name, value;\n    const indexOfEqual = bytes.indexOf(p(\"=\"));\n    if (indexOfEqual >= 0) {\n      name = bytes.slice(0, indexOfEqual);\n      value = bytes.slice(indexOfEqual + 1);\n    } else {\n      name = bytes;\n      value = new Uint8Array(0);\n    }\n    name = replaceByteInByteSequence(name, 0x2B, 0x20);\n    value = replaceByteInByteSequence(value, 0x2B, 0x20);\n    const nameString = utf8DecodeWithoutBOM(percentDecodeBytes(name));\n    const valueString = utf8DecodeWithoutBOM(percentDecodeBytes(value));\n    output.push([nameString, valueString]);\n  }\n  return output;\n}\n\n// https://url.spec.whatwg.org/#concept-urlencoded-string-parser\nfunction parseUrlencodedString(input) {\n  return parseUrlencoded(utf8Encode(input));\n}\n\n// https://url.spec.whatwg.org/#concept-urlencoded-serializer\nfunction serializeUrlencoded(tuples) {\n  // TODO: accept and use encoding argument\n\n  let output = \"\";\n  for (const [i, tuple] of tuples.entries()) {\n    const name = utf8PercentEncodeString(tuple[0], isURLEncodedPercentEncode, true);\n    const value = utf8PercentEncodeString(tuple[1], isURLEncodedPercentEncode, true);\n    if (i !== 0) {\n      output += \"&\";\n    }\n    output += `${name}=${value}`;\n  }\n  return output;\n}\nfunction strictlySplitByteSequence(buf, cp) {\n  const list = [];\n  let last = 0;\n  let i = buf.indexOf(cp);\n  while (i >= 0) {\n    list.push(buf.slice(last, i));\n    last = i + 1;\n    i = buf.indexOf(cp, last);\n  }\n  if (last !== buf.length) {\n    list.push(buf.slice(last));\n  }\n  return list;\n}\nfunction replaceByteInByteSequence(buf, from, to) {\n  let i = buf.indexOf(from);\n  while (i >= 0) {\n    buf[i] = to;\n    i = buf.indexOf(from, i + 1);\n  }\n  return buf;\n}\nmodule.exports = {\n  parseUrlencodedString,\n  serializeUrlencoded\n};", "map": {"version": 3, "names": ["utf8Encode", "utf8DecodeWithoutBOM", "require", "percentDecodeBytes", "utf8PercentEncodeString", "isURLEncodedPercentEncode", "p", "char", "codePointAt", "parseUrlencoded", "input", "sequences", "strictlySplitByteSequence", "output", "bytes", "length", "name", "value", "indexOfEqual", "indexOf", "slice", "Uint8Array", "replaceByteInByteSequence", "nameString", "valueString", "push", "parseUrlencodedString", "serializeUrlencoded", "tuples", "i", "tuple", "entries", "buf", "cp", "list", "last", "from", "to", "module", "exports"], "sources": ["C:/Users/<USER>/node_modules/mongodb-connection-string-url/node_modules/whatwg-url/lib/urlencoded.js"], "sourcesContent": ["\"use strict\";\nconst { utf8Encode, utf8DecodeWithoutBOM } = require(\"./encoding\");\nconst { percentDecodeBytes, utf8PercentEncodeString, isURLEncodedPercentEncode } = require(\"./percent-encoding\");\n\nfunction p(char) {\n  return char.codePointAt(0);\n}\n\n// https://url.spec.whatwg.org/#concept-urlencoded-parser\nfunction parseUrlencoded(input) {\n  const sequences = strictlySplitByteSequence(input, p(\"&\"));\n  const output = [];\n  for (const bytes of sequences) {\n    if (bytes.length === 0) {\n      continue;\n    }\n\n    let name, value;\n    const indexOfEqual = bytes.indexOf(p(\"=\"));\n\n    if (indexOfEqual >= 0) {\n      name = bytes.slice(0, indexOfEqual);\n      value = bytes.slice(indexOfEqual + 1);\n    } else {\n      name = bytes;\n      value = new Uint8Array(0);\n    }\n\n    name = replaceByteInByteSequence(name, 0x2B, 0x20);\n    value = replaceByteInByteSequence(value, 0x2B, 0x20);\n\n    const nameString = utf8DecodeWithoutBOM(percentDecodeBytes(name));\n    const valueString = utf8DecodeWithoutBOM(percentDecodeBytes(value));\n\n    output.push([nameString, valueString]);\n  }\n  return output;\n}\n\n// https://url.spec.whatwg.org/#concept-urlencoded-string-parser\nfunction parseUrlencodedString(input) {\n  return parseUrlencoded(utf8Encode(input));\n}\n\n// https://url.spec.whatwg.org/#concept-urlencoded-serializer\nfunction serializeUrlencoded(tuples) {\n  // TODO: accept and use encoding argument\n\n  let output = \"\";\n  for (const [i, tuple] of tuples.entries()) {\n    const name = utf8PercentEncodeString(tuple[0], isURLEncodedPercentEncode, true);\n    const value = utf8PercentEncodeString(tuple[1], isURLEncodedPercentEncode, true);\n\n    if (i !== 0) {\n      output += \"&\";\n    }\n    output += `${name}=${value}`;\n  }\n  return output;\n}\n\nfunction strictlySplitByteSequence(buf, cp) {\n  const list = [];\n  let last = 0;\n  let i = buf.indexOf(cp);\n  while (i >= 0) {\n    list.push(buf.slice(last, i));\n    last = i + 1;\n    i = buf.indexOf(cp, last);\n  }\n  if (last !== buf.length) {\n    list.push(buf.slice(last));\n  }\n  return list;\n}\n\nfunction replaceByteInByteSequence(buf, from, to) {\n  let i = buf.indexOf(from);\n  while (i >= 0) {\n    buf[i] = to;\n    i = buf.indexOf(from, i + 1);\n  }\n  return buf;\n}\n\nmodule.exports = {\n  parseUrlencodedString,\n  serializeUrlencoded\n};\n"], "mappings": "AAAA,YAAY;;AACZ,MAAM;EAAEA,UAAU;EAAEC;AAAqB,CAAC,GAAGC,OAAO,CAAC,YAAY,CAAC;AAClE,MAAM;EAAEC,kBAAkB;EAAEC,uBAAuB;EAAEC;AAA0B,CAAC,GAAGH,OAAO,CAAC,oBAAoB,CAAC;AAEhH,SAASI,CAACA,CAACC,IAAI,EAAE;EACf,OAAOA,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;AAC5B;;AAEA;AACA,SAASC,eAAeA,CAACC,KAAK,EAAE;EAC9B,MAAMC,SAAS,GAAGC,yBAAyB,CAACF,KAAK,EAAEJ,CAAC,CAAC,GAAG,CAAC,CAAC;EAC1D,MAAMO,MAAM,GAAG,EAAE;EACjB,KAAK,MAAMC,KAAK,IAAIH,SAAS,EAAE;IAC7B,IAAIG,KAAK,CAACC,MAAM,KAAK,CAAC,EAAE;MACtB;IACF;IAEA,IAAIC,IAAI,EAAEC,KAAK;IACf,MAAMC,YAAY,GAAGJ,KAAK,CAACK,OAAO,CAACb,CAAC,CAAC,GAAG,CAAC,CAAC;IAE1C,IAAIY,YAAY,IAAI,CAAC,EAAE;MACrBF,IAAI,GAAGF,KAAK,CAACM,KAAK,CAAC,CAAC,EAAEF,YAAY,CAAC;MACnCD,KAAK,GAAGH,KAAK,CAACM,KAAK,CAACF,YAAY,GAAG,CAAC,CAAC;IACvC,CAAC,MAAM;MACLF,IAAI,GAAGF,KAAK;MACZG,KAAK,GAAG,IAAII,UAAU,CAAC,CAAC,CAAC;IAC3B;IAEAL,IAAI,GAAGM,yBAAyB,CAACN,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;IAClDC,KAAK,GAAGK,yBAAyB,CAACL,KAAK,EAAE,IAAI,EAAE,IAAI,CAAC;IAEpD,MAAMM,UAAU,GAAGtB,oBAAoB,CAACE,kBAAkB,CAACa,IAAI,CAAC,CAAC;IACjE,MAAMQ,WAAW,GAAGvB,oBAAoB,CAACE,kBAAkB,CAACc,KAAK,CAAC,CAAC;IAEnEJ,MAAM,CAACY,IAAI,CAAC,CAACF,UAAU,EAAEC,WAAW,CAAC,CAAC;EACxC;EACA,OAAOX,MAAM;AACf;;AAEA;AACA,SAASa,qBAAqBA,CAAChB,KAAK,EAAE;EACpC,OAAOD,eAAe,CAACT,UAAU,CAACU,KAAK,CAAC,CAAC;AAC3C;;AAEA;AACA,SAASiB,mBAAmBA,CAACC,MAAM,EAAE;EACnC;;EAEA,IAAIf,MAAM,GAAG,EAAE;EACf,KAAK,MAAM,CAACgB,CAAC,EAAEC,KAAK,CAAC,IAAIF,MAAM,CAACG,OAAO,CAAC,CAAC,EAAE;IACzC,MAAMf,IAAI,GAAGZ,uBAAuB,CAAC0B,KAAK,CAAC,CAAC,CAAC,EAAEzB,yBAAyB,EAAE,IAAI,CAAC;IAC/E,MAAMY,KAAK,GAAGb,uBAAuB,CAAC0B,KAAK,CAAC,CAAC,CAAC,EAAEzB,yBAAyB,EAAE,IAAI,CAAC;IAEhF,IAAIwB,CAAC,KAAK,CAAC,EAAE;MACXhB,MAAM,IAAI,GAAG;IACf;IACAA,MAAM,IAAI,GAAGG,IAAI,IAAIC,KAAK,EAAE;EAC9B;EACA,OAAOJ,MAAM;AACf;AAEA,SAASD,yBAAyBA,CAACoB,GAAG,EAAEC,EAAE,EAAE;EAC1C,MAAMC,IAAI,GAAG,EAAE;EACf,IAAIC,IAAI,GAAG,CAAC;EACZ,IAAIN,CAAC,GAAGG,GAAG,CAACb,OAAO,CAACc,EAAE,CAAC;EACvB,OAAOJ,CAAC,IAAI,CAAC,EAAE;IACbK,IAAI,CAACT,IAAI,CAACO,GAAG,CAACZ,KAAK,CAACe,IAAI,EAAEN,CAAC,CAAC,CAAC;IAC7BM,IAAI,GAAGN,CAAC,GAAG,CAAC;IACZA,CAAC,GAAGG,GAAG,CAACb,OAAO,CAACc,EAAE,EAAEE,IAAI,CAAC;EAC3B;EACA,IAAIA,IAAI,KAAKH,GAAG,CAACjB,MAAM,EAAE;IACvBmB,IAAI,CAACT,IAAI,CAACO,GAAG,CAACZ,KAAK,CAACe,IAAI,CAAC,CAAC;EAC5B;EACA,OAAOD,IAAI;AACb;AAEA,SAASZ,yBAAyBA,CAACU,GAAG,EAAEI,IAAI,EAAEC,EAAE,EAAE;EAChD,IAAIR,CAAC,GAAGG,GAAG,CAACb,OAAO,CAACiB,IAAI,CAAC;EACzB,OAAOP,CAAC,IAAI,CAAC,EAAE;IACbG,GAAG,CAACH,CAAC,CAAC,GAAGQ,EAAE;IACXR,CAAC,GAAGG,GAAG,CAACb,OAAO,CAACiB,IAAI,EAAEP,CAAC,GAAG,CAAC,CAAC;EAC9B;EACA,OAAOG,GAAG;AACZ;AAEAM,MAAM,CAACC,OAAO,GAAG;EACfb,qBAAqB;EACrBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}