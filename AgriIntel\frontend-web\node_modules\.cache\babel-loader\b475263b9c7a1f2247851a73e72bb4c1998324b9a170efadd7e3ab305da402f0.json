{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DistinctOperation = void 0;\nconst utils_1 = require(\"../utils\");\nconst command_1 = require(\"./command\");\nconst operation_1 = require(\"./operation\");\n/**\n * Return a list of distinct values for the given key across a collection.\n * @internal\n */\nclass DistinctOperation extends command_1.CommandOperation {\n  /**\n   * Construct a Distinct operation.\n   *\n   * @param collection - Collection instance.\n   * @param key - Field of the document to find distinct values for.\n   * @param query - The query for filtering the set of documents to which we apply the distinct filter.\n   * @param options - Optional settings. See Collection.prototype.distinct for a list of options.\n   */\n  constructor(collection, key, query, options) {\n    super(collection, options);\n    this.options = options ?? {};\n    this.collection = collection;\n    this.key = key;\n    this.query = query;\n  }\n  get commandName() {\n    return 'distinct';\n  }\n  async execute(server, session, timeoutContext) {\n    const coll = this.collection;\n    const key = this.key;\n    const query = this.query;\n    const options = this.options;\n    // Distinct command\n    const cmd = {\n      distinct: coll.collectionName,\n      key: key,\n      query: query\n    };\n    // Add maxTimeMS if defined\n    if (typeof options.maxTimeMS === 'number') {\n      cmd.maxTimeMS = options.maxTimeMS;\n    }\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (typeof options.comment !== 'undefined') {\n      cmd.comment = options.comment;\n    }\n    if (options.hint != null) {\n      cmd.hint = options.hint;\n    }\n    // Do we have a readConcern specified\n    (0, utils_1.decorateWithReadConcern)(cmd, coll, options);\n    // Have we specified collation\n    (0, utils_1.decorateWithCollation)(cmd, coll, options);\n    const result = await super.executeCommand(server, session, cmd, timeoutContext);\n    return this.explain ? result : result.values;\n  }\n}\nexports.DistinctOperation = DistinctOperation;\n(0, operation_1.defineAspects)(DistinctOperation, [operation_1.Aspect.READ_OPERATION, operation_1.Aspect.RETRYABLE, operation_1.Aspect.EXPLAINABLE]);", "map": {"version": 3, "names": ["utils_1", "require", "command_1", "operation_1", "DistinctOperation", "CommandOperation", "constructor", "collection", "key", "query", "options", "commandName", "execute", "server", "session", "timeoutContext", "coll", "cmd", "distinct", "collectionName", "maxTimeMS", "comment", "hint", "decorateWithReadConcern", "decorateWithCollation", "result", "executeCommand", "explain", "values", "exports", "defineAspects", "Aspect", "READ_OPERATION", "RETRYABLE", "EXPLAINABLE"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\distinct.ts"], "sourcesContent": ["import type { Document } from '../bson';\nimport type { Collection } from '../collection';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { decorateWithCollation, decorateWithReadConcern } from '../utils';\nimport { CommandOperation, type CommandOperationOptions } from './command';\nimport { Aspect, defineAspects } from './operation';\n\n/** @public */\nexport type DistinctOptions = CommandOperationOptions & {\n  /**\n   * @sinceServerVersion 7.1\n   *\n   * The index to use. Specify either the index name as a string or the index key pattern.\n   * If specified, then the query system will only consider plans using the hinted index.\n   *\n   * If provided as a string, `hint` must be index name for an index on the collection.\n   * If provided as an object, `hint` must be an index description for an index defined on the collection.\n   *\n   * See https://www.mongodb.com/docs/manual/reference/command/distinct/#command-fields.\n   */\n  hint?: Document | string;\n};\n\n/**\n * Return a list of distinct values for the given key across a collection.\n * @internal\n */\nexport class DistinctOperation extends CommandOperation<any[]> {\n  override options: DistinctOptions;\n  collection: Collection;\n  /** Field of the document to find distinct values for. */\n  key: string;\n  /** The query for filtering the set of documents to which we apply the distinct filter. */\n  query: Document;\n\n  /**\n   * Construct a Distinct operation.\n   *\n   * @param collection - Collection instance.\n   * @param key - Field of the document to find distinct values for.\n   * @param query - The query for filtering the set of documents to which we apply the distinct filter.\n   * @param options - Optional settings. See Collection.prototype.distinct for a list of options.\n   */\n  constructor(collection: Collection, key: string, query: Document, options?: DistinctOptions) {\n    super(collection, options);\n\n    this.options = options ?? {};\n    this.collection = collection;\n    this.key = key;\n    this.query = query;\n  }\n\n  override get commandName() {\n    return 'distinct' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<any[]> {\n    const coll = this.collection;\n    const key = this.key;\n    const query = this.query;\n    const options = this.options;\n\n    // Distinct command\n    const cmd: Document = {\n      distinct: coll.collectionName,\n      key: key,\n      query: query\n    };\n\n    // Add maxTimeMS if defined\n    if (typeof options.maxTimeMS === 'number') {\n      cmd.maxTimeMS = options.maxTimeMS;\n    }\n\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (typeof options.comment !== 'undefined') {\n      cmd.comment = options.comment;\n    }\n\n    if (options.hint != null) {\n      cmd.hint = options.hint;\n    }\n\n    // Do we have a readConcern specified\n    decorateWithReadConcern(cmd, coll, options);\n\n    // Have we specified collation\n    decorateWithCollation(cmd, coll, options);\n\n    const result = await super.executeCommand(server, session, cmd, timeoutContext);\n\n    return this.explain ? result : result.values;\n  }\n}\n\ndefineAspects(DistinctOperation, [Aspect.READ_OPERATION, Aspect.RETRYABLE, Aspect.EXPLAINABLE]);\n"], "mappings": ";;;;;;AAKA,MAAAA,OAAA,GAAAC,OAAA;AACA,MAAAC,SAAA,GAAAD,OAAA;AACA,MAAAE,WAAA,GAAAF,OAAA;AAkBA;;;;AAIA,MAAaG,iBAAkB,SAAQF,SAAA,CAAAG,gBAAuB;EAQ5D;;;;;;;;EAQAC,YAAYC,UAAsB,EAAEC,GAAW,EAAEC,KAAe,EAAEC,OAAyB;IACzF,KAAK,CAACH,UAAU,EAAEG,OAAO,CAAC;IAE1B,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAI,EAAE;IAC5B,IAAI,CAACH,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,KAAK,GAAGA,KAAK;EACpB;EAEA,IAAaE,WAAWA,CAAA;IACtB,OAAO,UAAmB;EAC5B;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMC,IAAI,GAAG,IAAI,CAACT,UAAU;IAC5B,MAAMC,GAAG,GAAG,IAAI,CAACA,GAAG;IACpB,MAAMC,KAAK,GAAG,IAAI,CAACA,KAAK;IACxB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;IAE5B;IACA,MAAMO,GAAG,GAAa;MACpBC,QAAQ,EAAEF,IAAI,CAACG,cAAc;MAC7BX,GAAG,EAAEA,GAAG;MACRC,KAAK,EAAEA;KACR;IAED;IACA,IAAI,OAAOC,OAAO,CAACU,SAAS,KAAK,QAAQ,EAAE;MACzCH,GAAG,CAACG,SAAS,GAAGV,OAAO,CAACU,SAAS;IACnC;IAEA;IACA;IACA,IAAI,OAAOV,OAAO,CAACW,OAAO,KAAK,WAAW,EAAE;MAC1CJ,GAAG,CAACI,OAAO,GAAGX,OAAO,CAACW,OAAO;IAC/B;IAEA,IAAIX,OAAO,CAACY,IAAI,IAAI,IAAI,EAAE;MACxBL,GAAG,CAACK,IAAI,GAAGZ,OAAO,CAACY,IAAI;IACzB;IAEA;IACA,IAAAtB,OAAA,CAAAuB,uBAAuB,EAACN,GAAG,EAAED,IAAI,EAAEN,OAAO,CAAC;IAE3C;IACA,IAAAV,OAAA,CAAAwB,qBAAqB,EAACP,GAAG,EAAED,IAAI,EAAEN,OAAO,CAAC;IAEzC,MAAMe,MAAM,GAAG,MAAM,KAAK,CAACC,cAAc,CAACb,MAAM,EAAEC,OAAO,EAAEG,GAAG,EAAEF,cAAc,CAAC;IAE/E,OAAO,IAAI,CAACY,OAAO,GAAGF,MAAM,GAAGA,MAAM,CAACG,MAAM;EAC9C;;AAtEFC,OAAA,CAAAzB,iBAAA,GAAAA,iBAAA;AAyEA,IAAAD,WAAA,CAAA2B,aAAa,EAAC1B,iBAAiB,EAAE,CAACD,WAAA,CAAA4B,MAAM,CAACC,cAAc,EAAE7B,WAAA,CAAA4B,MAAM,CAACE,SAAS,EAAE9B,WAAA,CAAA4B,MAAM,CAACG,WAAW,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}