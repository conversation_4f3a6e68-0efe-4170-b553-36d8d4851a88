{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.onData = onData;\nconst utils_1 = require(\"../../utils\");\n/**\n * onData is adapted from Node.js' events.on helper\n * https://nodejs.org/api/events.html#eventsonemitter-eventname-options\n *\n * Returns an AsyncIterator that iterates each 'data' event emitted from emitter.\n * It will reject upon an error event.\n */\nfunction onData(emitter, {\n  timeoutContext,\n  signal\n}) {\n  signal?.throwIfAborted();\n  // Setup pending events and pending promise lists\n  /**\n   * When the caller has not yet called .next(), we store the\n   * value from the event in this list. Next time they call .next()\n   * we pull the first value out of this list and resolve a promise with it.\n   */\n  const unconsumedEvents = new utils_1.List();\n  /**\n   * When there has not yet been an event, a new promise will be created\n   * and implicitly stored in this list. When an event occurs we take the first\n   * promise in this list and resolve it.\n   */\n  const unconsumedPromises = new utils_1.List();\n  /**\n   * Stored an error created by an error event.\n   * This error will turn into a rejection for the subsequent .next() call\n   */\n  let error = null;\n  /** Set to true only after event listeners have been removed. */\n  let finished = false;\n  const iterator = {\n    next() {\n      // First, we consume all unread events\n      const value = unconsumedEvents.shift();\n      if (value != null) {\n        return Promise.resolve({\n          value,\n          done: false\n        });\n      }\n      // Then we error, if an error happened\n      // This happens one time if at all, because after 'error'\n      // we stop listening\n      if (error != null) {\n        const p = Promise.reject(error);\n        // Only the first element errors\n        error = null;\n        return p;\n      }\n      // If the iterator is finished, resolve to done\n      if (finished) return closeHandler();\n      // Wait until an event happens\n      const {\n        promise,\n        resolve,\n        reject\n      } = (0, utils_1.promiseWithResolvers)();\n      unconsumedPromises.push({\n        resolve,\n        reject\n      });\n      return promise;\n    },\n    return() {\n      return closeHandler();\n    },\n    throw(err) {\n      errorHandler(err);\n      return Promise.resolve({\n        value: undefined,\n        done: true\n      });\n    },\n    [Symbol.asyncIterator]() {\n      return this;\n    }\n  };\n  // Adding event handlers\n  emitter.on('data', eventHandler);\n  emitter.on('error', errorHandler);\n  const abortListener = (0, utils_1.addAbortListener)(signal, function () {\n    errorHandler(this.reason);\n  });\n  const timeoutForSocketRead = timeoutContext?.timeoutForSocketRead;\n  timeoutForSocketRead?.throwIfExpired();\n  timeoutForSocketRead?.then(undefined, errorHandler);\n  return iterator;\n  function eventHandler(value) {\n    const promise = unconsumedPromises.shift();\n    if (promise != null) promise.resolve({\n      value,\n      done: false\n    });else unconsumedEvents.push(value);\n  }\n  function errorHandler(err) {\n    const promise = unconsumedPromises.shift();\n    if (promise != null) promise.reject(err);else error = err;\n    void closeHandler();\n  }\n  function closeHandler() {\n    // Adding event handlers\n    emitter.off('data', eventHandler);\n    emitter.off('error', errorHandler);\n    abortListener?.[utils_1.kDispose]();\n    finished = true;\n    timeoutForSocketRead?.clear();\n    const doneResult = {\n      value: undefined,\n      done: finished\n    };\n    for (const promise of unconsumedPromises) {\n      promise.resolve(doneResult);\n    }\n    return Promise.resolve(doneResult);\n  }\n}", "map": {"version": 3, "names": ["exports", "onData", "utils_1", "require", "emitter", "timeoutContext", "signal", "throwIfAborted", "unconsumedEvents", "List", "unconsumedPromises", "error", "finished", "iterator", "next", "value", "shift", "Promise", "resolve", "done", "p", "reject", "<PERSON><PERSON><PERSON><PERSON>", "promise", "promiseWithResolvers", "push", "return", "throw", "err", "<PERSON><PERSON><PERSON><PERSON>", "undefined", "Symbol", "asyncIterator", "on", "<PERSON><PERSON><PERSON><PERSON>", "abortListener", "addAbortListener", "reason", "timeoutForSocketRead", "throwIfExpired", "then", "off", "kDispose", "clear", "doneResult"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\wire_protocol\\on_data.ts"], "sourcesContent": ["import { type EventEmitter } from 'events';\n\nimport { type Abortable } from '../../mongo_types';\nimport { type TimeoutContext } from '../../timeout';\nimport { addAbortListener, kDispose, List, promiseWithResolvers } from '../../utils';\n\n/**\n * @internal\n * An object holding references to a promise's resolve and reject functions.\n */\ntype PendingPromises = Omit<\n  ReturnType<typeof promiseWithResolvers<IteratorResult<Buffer>>>,\n  'promise'\n>;\n\n/**\n * onData is adapted from Node.js' events.on helper\n * https://nodejs.org/api/events.html#eventsonemitter-eventname-options\n *\n * Returns an AsyncIterator that iterates each 'data' event emitted from emitter.\n * It will reject upon an error event.\n */\nexport function onData(\n  emitter: EventEmitter,\n  { timeoutContext, signal }: { timeoutContext?: TimeoutContext } & Abortable\n) {\n  signal?.throwIfAborted();\n\n  // Setup pending events and pending promise lists\n  /**\n   * When the caller has not yet called .next(), we store the\n   * value from the event in this list. Next time they call .next()\n   * we pull the first value out of this list and resolve a promise with it.\n   */\n  const unconsumedEvents = new List<Buffer>();\n  /**\n   * When there has not yet been an event, a new promise will be created\n   * and implicitly stored in this list. When an event occurs we take the first\n   * promise in this list and resolve it.\n   */\n  const unconsumedPromises = new List<PendingPromises>();\n\n  /**\n   * Stored an error created by an error event.\n   * This error will turn into a rejection for the subsequent .next() call\n   */\n  let error: Error | null = null;\n\n  /** Set to true only after event listeners have been removed. */\n  let finished = false;\n\n  const iterator: AsyncGenerator<Buffer> = {\n    next() {\n      // First, we consume all unread events\n      const value = unconsumedEvents.shift();\n      if (value != null) {\n        return Promise.resolve({ value, done: false });\n      }\n\n      // Then we error, if an error happened\n      // This happens one time if at all, because after 'error'\n      // we stop listening\n      if (error != null) {\n        const p = Promise.reject(error);\n        // Only the first element errors\n        error = null;\n        return p;\n      }\n\n      // If the iterator is finished, resolve to done\n      if (finished) return closeHandler();\n\n      // Wait until an event happens\n      const { promise, resolve, reject } = promiseWithResolvers<IteratorResult<Buffer>>();\n      unconsumedPromises.push({ resolve, reject });\n      return promise;\n    },\n\n    return() {\n      return closeHandler();\n    },\n\n    throw(err: Error) {\n      errorHandler(err);\n      return Promise.resolve({ value: undefined, done: true });\n    },\n\n    [Symbol.asyncIterator]() {\n      return this;\n    }\n  };\n\n  // Adding event handlers\n  emitter.on('data', eventHandler);\n  emitter.on('error', errorHandler);\n  const abortListener = addAbortListener(signal, function () {\n    errorHandler(this.reason);\n  });\n\n  const timeoutForSocketRead = timeoutContext?.timeoutForSocketRead;\n  timeoutForSocketRead?.throwIfExpired();\n  timeoutForSocketRead?.then(undefined, errorHandler);\n\n  return iterator;\n\n  function eventHandler(value: Buffer) {\n    const promise = unconsumedPromises.shift();\n    if (promise != null) promise.resolve({ value, done: false });\n    else unconsumedEvents.push(value);\n  }\n\n  function errorHandler(err: Error) {\n    const promise = unconsumedPromises.shift();\n\n    if (promise != null) promise.reject(err);\n    else error = err;\n    void closeHandler();\n  }\n\n  function closeHandler() {\n    // Adding event handlers\n    emitter.off('data', eventHandler);\n    emitter.off('error', errorHandler);\n    abortListener?.[kDispose]();\n    finished = true;\n    timeoutForSocketRead?.clear();\n    const doneResult = { value: undefined, done: finished } as const;\n\n    for (const promise of unconsumedPromises) {\n      promise.resolve(doneResult);\n    }\n\n    return Promise.resolve(doneResult);\n  }\n}\n"], "mappings": ";;;;;AAsBAA,OAAA,CAAAC,MAAA,GAAAA,MAAA;AAlBA,MAAAC,OAAA,GAAAC,OAAA;AAWA;;;;;;;AAOA,SAAgBF,MAAMA,CACpBG,OAAqB,EACrB;EAAEC,cAAc;EAAEC;AAAM,CAAmD;EAE3EA,MAAM,EAAEC,cAAc,EAAE;EAExB;EACA;;;;;EAKA,MAAMC,gBAAgB,GAAG,IAAIN,OAAA,CAAAO,IAAI,EAAU;EAC3C;;;;;EAKA,MAAMC,kBAAkB,GAAG,IAAIR,OAAA,CAAAO,IAAI,EAAmB;EAEtD;;;;EAIA,IAAIE,KAAK,GAAiB,IAAI;EAE9B;EACA,IAAIC,QAAQ,GAAG,KAAK;EAEpB,MAAMC,QAAQ,GAA2B;IACvCC,IAAIA,CAAA;MACF;MACA,MAAMC,KAAK,GAAGP,gBAAgB,CAACQ,KAAK,EAAE;MACtC,IAAID,KAAK,IAAI,IAAI,EAAE;QACjB,OAAOE,OAAO,CAACC,OAAO,CAAC;UAAEH,KAAK;UAAEI,IAAI,EAAE;QAAK,CAAE,CAAC;MAChD;MAEA;MACA;MACA;MACA,IAAIR,KAAK,IAAI,IAAI,EAAE;QACjB,MAAMS,CAAC,GAAGH,OAAO,CAACI,MAAM,CAACV,KAAK,CAAC;QAC/B;QACAA,KAAK,GAAG,IAAI;QACZ,OAAOS,CAAC;MACV;MAEA;MACA,IAAIR,QAAQ,EAAE,OAAOU,YAAY,EAAE;MAEnC;MACA,MAAM;QAAEC,OAAO;QAAEL,OAAO;QAAEG;MAAM,CAAE,GAAG,IAAAnB,OAAA,CAAAsB,oBAAoB,GAA0B;MACnFd,kBAAkB,CAACe,IAAI,CAAC;QAAEP,OAAO;QAAEG;MAAM,CAAE,CAAC;MAC5C,OAAOE,OAAO;IAChB,CAAC;IAEDG,MAAMA,CAAA;MACJ,OAAOJ,YAAY,EAAE;IACvB,CAAC;IAEDK,KAAKA,CAACC,GAAU;MACdC,YAAY,CAACD,GAAG,CAAC;MACjB,OAAOX,OAAO,CAACC,OAAO,CAAC;QAAEH,KAAK,EAAEe,SAAS;QAAEX,IAAI,EAAE;MAAI,CAAE,CAAC;IAC1D,CAAC;IAED,CAACY,MAAM,CAACC,aAAa,IAAC;MACpB,OAAO,IAAI;IACb;GACD;EAED;EACA5B,OAAO,CAAC6B,EAAE,CAAC,MAAM,EAAEC,YAAY,CAAC;EAChC9B,OAAO,CAAC6B,EAAE,CAAC,OAAO,EAAEJ,YAAY,CAAC;EACjC,MAAMM,aAAa,GAAG,IAAAjC,OAAA,CAAAkC,gBAAgB,EAAC9B,MAAM,EAAE;IAC7CuB,YAAY,CAAC,IAAI,CAACQ,MAAM,CAAC;EAC3B,CAAC,CAAC;EAEF,MAAMC,oBAAoB,GAAGjC,cAAc,EAAEiC,oBAAoB;EACjEA,oBAAoB,EAAEC,cAAc,EAAE;EACtCD,oBAAoB,EAAEE,IAAI,CAACV,SAAS,EAAED,YAAY,CAAC;EAEnD,OAAOhB,QAAQ;EAEf,SAASqB,YAAYA,CAACnB,KAAa;IACjC,MAAMQ,OAAO,GAAGb,kBAAkB,CAACM,KAAK,EAAE;IAC1C,IAAIO,OAAO,IAAI,IAAI,EAAEA,OAAO,CAACL,OAAO,CAAC;MAAEH,KAAK;MAAEI,IAAI,EAAE;IAAK,CAAE,CAAC,CAAC,KACxDX,gBAAgB,CAACiB,IAAI,CAACV,KAAK,CAAC;EACnC;EAEA,SAASc,YAAYA,CAACD,GAAU;IAC9B,MAAML,OAAO,GAAGb,kBAAkB,CAACM,KAAK,EAAE;IAE1C,IAAIO,OAAO,IAAI,IAAI,EAAEA,OAAO,CAACF,MAAM,CAACO,GAAG,CAAC,CAAC,KACpCjB,KAAK,GAAGiB,GAAG;IAChB,KAAKN,YAAY,EAAE;EACrB;EAEA,SAASA,YAAYA,CAAA;IACnB;IACAlB,OAAO,CAACqC,GAAG,CAAC,MAAM,EAAEP,YAAY,CAAC;IACjC9B,OAAO,CAACqC,GAAG,CAAC,OAAO,EAAEZ,YAAY,CAAC;IAClCM,aAAa,GAAGjC,OAAA,CAAAwC,QAAQ,CAAC,EAAE;IAC3B9B,QAAQ,GAAG,IAAI;IACf0B,oBAAoB,EAAEK,KAAK,EAAE;IAC7B,MAAMC,UAAU,GAAG;MAAE7B,KAAK,EAAEe,SAAS;MAAEX,IAAI,EAAEP;IAAQ,CAAW;IAEhE,KAAK,MAAMW,OAAO,IAAIb,kBAAkB,EAAE;MACxCa,OAAO,CAACL,OAAO,CAAC0B,UAAU,CAAC;IAC7B;IAEA,OAAO3B,OAAO,CAACC,OAAO,CAAC0B,UAAU,CAAC;EACpC;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}