{"ast": null, "code": "module.exports = function (size) {\n  return new LruCache(size);\n};\nfunction LruCache(size) {\n  this.capacity = size | 0;\n  this.map = Object.create(null);\n  this.list = new DoublyLinkedList();\n}\nLruCache.prototype.get = function (key) {\n  var node = this.map[key];\n  if (node == null) return undefined;\n  this.used(node);\n  return node.val;\n};\nLruCache.prototype.set = function (key, val) {\n  var node = this.map[key];\n  if (node != null) {\n    node.val = val;\n  } else {\n    if (!this.capacity) this.prune();\n    if (!this.capacity) return false;\n    node = new DoublyLinkedNode(key, val);\n    this.map[key] = node;\n    this.capacity--;\n  }\n  this.used(node);\n  return true;\n};\nLruCache.prototype.used = function (node) {\n  this.list.moveToFront(node);\n};\nLruCache.prototype.prune = function () {\n  var node = this.list.pop();\n  if (node != null) {\n    delete this.map[node.key];\n    this.capacity++;\n  }\n};\nfunction DoublyLinkedList() {\n  this.firstNode = null;\n  this.lastNode = null;\n}\nDoublyLinkedList.prototype.moveToFront = function (node) {\n  if (this.firstNode == node) return;\n  this.remove(node);\n  if (this.firstNode == null) {\n    this.firstNode = node;\n    this.lastNode = node;\n    node.prev = null;\n    node.next = null;\n  } else {\n    node.prev = null;\n    node.next = this.firstNode;\n    node.next.prev = node;\n    this.firstNode = node;\n  }\n};\nDoublyLinkedList.prototype.pop = function () {\n  var lastNode = this.lastNode;\n  if (lastNode != null) {\n    this.remove(lastNode);\n  }\n  return lastNode;\n};\nDoublyLinkedList.prototype.remove = function (node) {\n  if (this.firstNode == node) {\n    this.firstNode = node.next;\n  } else if (node.prev != null) {\n    node.prev.next = node.next;\n  }\n  if (this.lastNode == node) {\n    this.lastNode = node.prev;\n  } else if (node.next != null) {\n    node.next.prev = node.prev;\n  }\n};\nfunction DoublyLinkedNode(key, val) {\n  this.key = key;\n  this.val = val;\n  this.prev = null;\n  this.next = null;\n}", "map": {"version": 3, "names": ["module", "exports", "size", "<PERSON><PERSON><PERSON><PERSON>", "capacity", "map", "Object", "create", "list", "DoublyLinkedList", "prototype", "get", "key", "node", "undefined", "used", "val", "set", "prune", "DoublyLinkedNode", "moveToFront", "pop", "firstNode", "lastNode", "remove", "prev", "next"], "sources": ["C:/Users/<USER>/node_modules/aws4/lru.js"], "sourcesContent": ["module.exports = function(size) {\n  return new LruCache(size)\n}\n\nfunction LruCache(size) {\n  this.capacity = size | 0\n  this.map = Object.create(null)\n  this.list = new DoublyLinkedList()\n}\n\nLruCache.prototype.get = function(key) {\n  var node = this.map[key]\n  if (node == null) return undefined\n  this.used(node)\n  return node.val\n}\n\nLruCache.prototype.set = function(key, val) {\n  var node = this.map[key]\n  if (node != null) {\n    node.val = val\n  } else {\n    if (!this.capacity) this.prune()\n    if (!this.capacity) return false\n    node = new DoublyLinkedNode(key, val)\n    this.map[key] = node\n    this.capacity--\n  }\n  this.used(node)\n  return true\n}\n\nLruCache.prototype.used = function(node) {\n  this.list.moveToFront(node)\n}\n\nLruCache.prototype.prune = function() {\n  var node = this.list.pop()\n  if (node != null) {\n    delete this.map[node.key]\n    this.capacity++\n  }\n}\n\n\nfunction DoublyLinkedList() {\n  this.firstNode = null\n  this.lastNode = null\n}\n\nDoublyLinkedList.prototype.moveToFront = function(node) {\n  if (this.firstNode == node) return\n\n  this.remove(node)\n\n  if (this.firstNode == null) {\n    this.firstNode = node\n    this.lastNode = node\n    node.prev = null\n    node.next = null\n  } else {\n    node.prev = null\n    node.next = this.firstNode\n    node.next.prev = node\n    this.firstNode = node\n  }\n}\n\nDoublyLinkedList.prototype.pop = function() {\n  var lastNode = this.lastNode\n  if (lastNode != null) {\n    this.remove(lastNode)\n  }\n  return lastNode\n}\n\nDoublyLinkedList.prototype.remove = function(node) {\n  if (this.firstNode == node) {\n    this.firstNode = node.next\n  } else if (node.prev != null) {\n    node.prev.next = node.next\n  }\n  if (this.lastNode == node) {\n    this.lastNode = node.prev\n  } else if (node.next != null) {\n    node.next.prev = node.prev\n  }\n}\n\n\nfunction DoublyLinkedNode(key, val) {\n  this.key = key\n  this.val = val\n  this.prev = null\n  this.next = null\n}\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG,UAASC,IAAI,EAAE;EAC9B,OAAO,IAAIC,QAAQ,CAACD,IAAI,CAAC;AAC3B,CAAC;AAED,SAASC,QAAQA,CAACD,IAAI,EAAE;EACtB,IAAI,CAACE,QAAQ,GAAGF,IAAI,GAAG,CAAC;EACxB,IAAI,CAACG,GAAG,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAC9B,IAAI,CAACC,IAAI,GAAG,IAAIC,gBAAgB,CAAC,CAAC;AACpC;AAEAN,QAAQ,CAACO,SAAS,CAACC,GAAG,GAAG,UAASC,GAAG,EAAE;EACrC,IAAIC,IAAI,GAAG,IAAI,CAACR,GAAG,CAACO,GAAG,CAAC;EACxB,IAAIC,IAAI,IAAI,IAAI,EAAE,OAAOC,SAAS;EAClC,IAAI,CAACC,IAAI,CAACF,IAAI,CAAC;EACf,OAAOA,IAAI,CAACG,GAAG;AACjB,CAAC;AAEDb,QAAQ,CAACO,SAAS,CAACO,GAAG,GAAG,UAASL,GAAG,EAAEI,GAAG,EAAE;EAC1C,IAAIH,IAAI,GAAG,IAAI,CAACR,GAAG,CAACO,GAAG,CAAC;EACxB,IAAIC,IAAI,IAAI,IAAI,EAAE;IAChBA,IAAI,CAACG,GAAG,GAAGA,GAAG;EAChB,CAAC,MAAM;IACL,IAAI,CAAC,IAAI,CAACZ,QAAQ,EAAE,IAAI,CAACc,KAAK,CAAC,CAAC;IAChC,IAAI,CAAC,IAAI,CAACd,QAAQ,EAAE,OAAO,KAAK;IAChCS,IAAI,GAAG,IAAIM,gBAAgB,CAACP,GAAG,EAAEI,GAAG,CAAC;IACrC,IAAI,CAACX,GAAG,CAACO,GAAG,CAAC,GAAGC,IAAI;IACpB,IAAI,CAACT,QAAQ,EAAE;EACjB;EACA,IAAI,CAACW,IAAI,CAACF,IAAI,CAAC;EACf,OAAO,IAAI;AACb,CAAC;AAEDV,QAAQ,CAACO,SAAS,CAACK,IAAI,GAAG,UAASF,IAAI,EAAE;EACvC,IAAI,CAACL,IAAI,CAACY,WAAW,CAACP,IAAI,CAAC;AAC7B,CAAC;AAEDV,QAAQ,CAACO,SAAS,CAACQ,KAAK,GAAG,YAAW;EACpC,IAAIL,IAAI,GAAG,IAAI,CAACL,IAAI,CAACa,GAAG,CAAC,CAAC;EAC1B,IAAIR,IAAI,IAAI,IAAI,EAAE;IAChB,OAAO,IAAI,CAACR,GAAG,CAACQ,IAAI,CAACD,GAAG,CAAC;IACzB,IAAI,CAACR,QAAQ,EAAE;EACjB;AACF,CAAC;AAGD,SAASK,gBAAgBA,CAAA,EAAG;EAC1B,IAAI,CAACa,SAAS,GAAG,IAAI;EACrB,IAAI,CAACC,QAAQ,GAAG,IAAI;AACtB;AAEAd,gBAAgB,CAACC,SAAS,CAACU,WAAW,GAAG,UAASP,IAAI,EAAE;EACtD,IAAI,IAAI,CAACS,SAAS,IAAIT,IAAI,EAAE;EAE5B,IAAI,CAACW,MAAM,CAACX,IAAI,CAAC;EAEjB,IAAI,IAAI,CAACS,SAAS,IAAI,IAAI,EAAE;IAC1B,IAAI,CAACA,SAAS,GAAGT,IAAI;IACrB,IAAI,CAACU,QAAQ,GAAGV,IAAI;IACpBA,IAAI,CAACY,IAAI,GAAG,IAAI;IAChBZ,IAAI,CAACa,IAAI,GAAG,IAAI;EAClB,CAAC,MAAM;IACLb,IAAI,CAACY,IAAI,GAAG,IAAI;IAChBZ,IAAI,CAACa,IAAI,GAAG,IAAI,CAACJ,SAAS;IAC1BT,IAAI,CAACa,IAAI,CAACD,IAAI,GAAGZ,IAAI;IACrB,IAAI,CAACS,SAAS,GAAGT,IAAI;EACvB;AACF,CAAC;AAEDJ,gBAAgB,CAACC,SAAS,CAACW,GAAG,GAAG,YAAW;EAC1C,IAAIE,QAAQ,GAAG,IAAI,CAACA,QAAQ;EAC5B,IAAIA,QAAQ,IAAI,IAAI,EAAE;IACpB,IAAI,CAACC,MAAM,CAACD,QAAQ,CAAC;EACvB;EACA,OAAOA,QAAQ;AACjB,CAAC;AAEDd,gBAAgB,CAACC,SAAS,CAACc,MAAM,GAAG,UAASX,IAAI,EAAE;EACjD,IAAI,IAAI,CAACS,SAAS,IAAIT,IAAI,EAAE;IAC1B,IAAI,CAACS,SAAS,GAAGT,IAAI,CAACa,IAAI;EAC5B,CAAC,MAAM,IAAIb,IAAI,CAACY,IAAI,IAAI,IAAI,EAAE;IAC5BZ,IAAI,CAACY,IAAI,CAACC,IAAI,GAAGb,IAAI,CAACa,IAAI;EAC5B;EACA,IAAI,IAAI,CAACH,QAAQ,IAAIV,IAAI,EAAE;IACzB,IAAI,CAACU,QAAQ,GAAGV,IAAI,CAACY,IAAI;EAC3B,CAAC,MAAM,IAAIZ,IAAI,CAACa,IAAI,IAAI,IAAI,EAAE;IAC5Bb,IAAI,CAACa,IAAI,CAACD,IAAI,GAAGZ,IAAI,CAACY,IAAI;EAC5B;AACF,CAAC;AAGD,SAASN,gBAAgBA,CAACP,GAAG,EAAEI,GAAG,EAAE;EAClC,IAAI,CAACJ,GAAG,GAAGA,GAAG;EACd,IAAI,CAACI,GAAG,GAAGA,GAAG;EACd,IAAI,CAACS,IAAI,GAAG,IAAI;EAChB,IAAI,CAACC,IAAI,GAAG,IAAI;AAClB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}