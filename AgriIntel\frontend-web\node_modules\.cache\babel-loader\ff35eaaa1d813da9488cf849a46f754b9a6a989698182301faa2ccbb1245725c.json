{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"ns\", \"children\"];\nimport { useTranslation } from './useTranslation.js';\nexport function Translation(props) {\n  const {\n      ns,\n      children\n    } = props,\n    options = _objectWithoutProperties(props, _excluded);\n  const [t, i18n, ready] = useTranslation(ns, options);\n  return children(t, {\n    i18n,\n    lng: i18n.language\n  }, ready);\n}", "map": {"version": 3, "names": ["useTranslation", "Translation", "props", "ns", "children", "options", "_objectWithoutProperties", "_excluded", "t", "i18n", "ready", "lng", "language"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-i18next/dist/es/Translation.js"], "sourcesContent": ["import { useTranslation } from './useTranslation.js';\nexport function Translation(props) {\n  const {\n    ns,\n    children,\n    ...options\n  } = props;\n  const [t, i18n, ready] = useTranslation(ns, options);\n  return children(t, {\n    i18n,\n    lng: i18n.language\n  }, ready);\n}"], "mappings": ";;AAAA,SAASA,cAAc,QAAQ,qBAAqB;AACpD,OAAO,SAASC,WAAWA,CAACC,KAAK,EAAE;EACjC,MAAM;MACJC,EAAE;MACFC;IAEF,CAAC,GAAGF,KAAK;IADJG,OAAO,GAAAC,wBAAA,CACRJ,KAAK,EAAAK,SAAA;EACT,MAAM,CAACC,CAAC,EAAEC,IAAI,EAAEC,KAAK,CAAC,GAAGV,cAAc,CAACG,EAAE,EAAEE,OAAO,CAAC;EACpD,OAAOD,QAAQ,CAACI,CAAC,EAAE;IACjBC,IAAI;IACJE,GAAG,EAAEF,IAAI,CAACG;EACZ,CAAC,EAAEF,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}