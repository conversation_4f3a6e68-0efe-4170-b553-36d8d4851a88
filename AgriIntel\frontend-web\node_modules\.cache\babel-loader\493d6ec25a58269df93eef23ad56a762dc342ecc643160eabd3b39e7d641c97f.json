{"ast": null, "code": "/**\n * A specialized version of `_.some` for arrays without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if any element passes the predicate check,\n *  else `false`.\n */\nfunction arraySome(array, predicate) {\n  var index = -1,\n    length = array == null ? 0 : array.length;\n  while (++index < length) {\n    if (predicate(array[index], index, array)) {\n      return true;\n    }\n  }\n  return false;\n}\nmodule.exports = arraySome;", "map": {"version": 3, "names": ["arraySome", "array", "predicate", "index", "length", "module", "exports"], "sources": ["C:/Users/<USER>/node_modules/lodash/_arraySome.js"], "sourcesContent": ["/**\n * A specialized version of `_.some` for arrays without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} [array] The array to iterate over.\n * @param {Function} predicate The function invoked per iteration.\n * @returns {boolean} Returns `true` if any element passes the predicate check,\n *  else `false`.\n */\nfunction arraySome(array, predicate) {\n  var index = -1,\n      length = array == null ? 0 : array.length;\n\n  while (++index < length) {\n    if (predicate(array[index], index, array)) {\n      return true;\n    }\n  }\n  return false;\n}\n\nmodule.exports = arraySome;\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASA,SAASA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACnC,IAAIC,KAAK,GAAG,CAAC,CAAC;IACVC,MAAM,GAAGH,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGA,KAAK,CAACG,MAAM;EAE7C,OAAO,EAAED,KAAK,GAAGC,MAAM,EAAE;IACvB,IAAIF,SAAS,CAACD,KAAK,CAACE,KAAK,CAAC,EAAEA,KAAK,EAAEF,KAAK,CAAC,EAAE;MACzC,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;AAEAI,MAAM,CAACC,OAAO,GAAGN,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}