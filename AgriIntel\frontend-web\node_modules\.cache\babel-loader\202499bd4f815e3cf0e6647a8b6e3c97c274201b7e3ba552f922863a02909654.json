{"ast": null, "code": "/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\nfunction emptyFunction() {}\nfunction emptyFunctionWithReset() {}\nemptyFunctionWithReset.resetWarningCache = emptyFunction;\nmodule.exports = function () {\n  function shim(props, propName, componentName, location, propFullName, secret) {\n    if (secret === ReactPropTypesSecret) {\n      // It is still safe when called from React.\n      return;\n    }\n    var err = new Error('Calling PropTypes validators directly is not supported by the `prop-types` package. ' + 'Use PropTypes.checkPropTypes() to call them. ' + 'Read more at http://fb.me/use-check-prop-types');\n    err.name = 'Invariant Violation';\n    throw err;\n  }\n  ;\n  shim.isRequired = shim;\n  function getShim() {\n    return shim;\n  }\n  ;\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n  var ReactPropTypes = {\n    array: shim,\n    bigint: shim,\n    bool: shim,\n    func: shim,\n    number: shim,\n    object: shim,\n    string: shim,\n    symbol: shim,\n    any: shim,\n    arrayOf: getShim,\n    element: shim,\n    elementType: shim,\n    instanceOf: getShim,\n    node: shim,\n    objectOf: getShim,\n    oneOf: getShim,\n    oneOfType: getShim,\n    shape: getShim,\n    exact: getShim,\n    checkPropTypes: emptyFunctionWithReset,\n    resetWarningCache: emptyFunction\n  };\n  ReactPropTypes.PropTypes = ReactPropTypes;\n  return ReactPropTypes;\n};", "map": {"version": 3, "names": ["ReactPropTypesSecret", "require", "emptyFunction", "emptyFunctionWithReset", "resetWarningCache", "module", "exports", "shim", "props", "propName", "componentName", "location", "prop<PERSON><PERSON><PERSON><PERSON>", "secret", "err", "Error", "name", "isRequired", "getShim", "ReactPropTypes", "array", "bigint", "bool", "func", "number", "object", "string", "symbol", "any", "arrayOf", "element", "elementType", "instanceOf", "node", "objectOf", "oneOf", "oneOfType", "shape", "exact", "checkPropTypes", "PropTypes"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/prop-types/factoryWithThrowingShims.js"], "sourcesContent": ["/**\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n'use strict';\n\nvar ReactPropTypesSecret = require('./lib/ReactPropTypesSecret');\n\nfunction emptyFunction() {}\nfunction emptyFunctionWithReset() {}\nemptyFunctionWithReset.resetWarningCache = emptyFunction;\n\nmodule.exports = function() {\n  function shim(props, propName, componentName, location, propFullName, secret) {\n    if (secret === ReactPropTypesSecret) {\n      // It is still safe when called from React.\n      return;\n    }\n    var err = new Error(\n      'Calling PropTypes validators directly is not supported by the `prop-types` package. ' +\n      'Use PropTypes.checkPropTypes() to call them. ' +\n      'Read more at http://fb.me/use-check-prop-types'\n    );\n    err.name = 'Invariant Violation';\n    throw err;\n  };\n  shim.isRequired = shim;\n  function getShim() {\n    return shim;\n  };\n  // Important!\n  // Keep this list in sync with production version in `./factoryWithTypeCheckers.js`.\n  var ReactPropTypes = {\n    array: shim,\n    bigint: shim,\n    bool: shim,\n    func: shim,\n    number: shim,\n    object: shim,\n    string: shim,\n    symbol: shim,\n\n    any: shim,\n    arrayOf: getShim,\n    element: shim,\n    elementType: shim,\n    instanceOf: getShim,\n    node: shim,\n    objectOf: getShim,\n    oneOf: getShim,\n    oneOfType: getShim,\n    shape: getShim,\n    exact: getShim,\n\n    checkPropTypes: emptyFunctionWithReset,\n    resetWarningCache: emptyFunction\n  };\n\n  ReactPropTypes.PropTypes = ReactPropTypes;\n\n  return ReactPropTypes;\n};\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;;AAEA,YAAY;;AAEZ,IAAIA,oBAAoB,GAAGC,OAAO,CAAC,4BAA4B,CAAC;AAEhE,SAASC,aAAaA,CAAA,EAAG,CAAC;AAC1B,SAASC,sBAAsBA,CAAA,EAAG,CAAC;AACnCA,sBAAsB,CAACC,iBAAiB,GAAGF,aAAa;AAExDG,MAAM,CAACC,OAAO,GAAG,YAAW;EAC1B,SAASC,IAAIA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,MAAM,EAAE;IAC5E,IAAIA,MAAM,KAAKb,oBAAoB,EAAE;MACnC;MACA;IACF;IACA,IAAIc,GAAG,GAAG,IAAIC,KAAK,CACjB,sFAAsF,GACtF,+CAA+C,GAC/C,gDACF,CAAC;IACDD,GAAG,CAACE,IAAI,GAAG,qBAAqB;IAChC,MAAMF,GAAG;EACX;EAAC;EACDP,IAAI,CAACU,UAAU,GAAGV,IAAI;EACtB,SAASW,OAAOA,CAAA,EAAG;IACjB,OAAOX,IAAI;EACb;EAAC;EACD;EACA;EACA,IAAIY,cAAc,GAAG;IACnBC,KAAK,EAAEb,IAAI;IACXc,MAAM,EAAEd,IAAI;IACZe,IAAI,EAAEf,IAAI;IACVgB,IAAI,EAAEhB,IAAI;IACViB,MAAM,EAAEjB,IAAI;IACZkB,MAAM,EAAElB,IAAI;IACZmB,MAAM,EAAEnB,IAAI;IACZoB,MAAM,EAAEpB,IAAI;IAEZqB,GAAG,EAAErB,IAAI;IACTsB,OAAO,EAAEX,OAAO;IAChBY,OAAO,EAAEvB,IAAI;IACbwB,WAAW,EAAExB,IAAI;IACjByB,UAAU,EAAEd,OAAO;IACnBe,IAAI,EAAE1B,IAAI;IACV2B,QAAQ,EAAEhB,OAAO;IACjBiB,KAAK,EAAEjB,OAAO;IACdkB,SAAS,EAAElB,OAAO;IAClBmB,KAAK,EAAEnB,OAAO;IACdoB,KAAK,EAAEpB,OAAO;IAEdqB,cAAc,EAAEpC,sBAAsB;IACtCC,iBAAiB,EAAEF;EACrB,CAAC;EAEDiB,cAAc,CAACqB,SAAS,GAAGrB,cAAc;EAEzC,OAAOA,cAAc;AACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}