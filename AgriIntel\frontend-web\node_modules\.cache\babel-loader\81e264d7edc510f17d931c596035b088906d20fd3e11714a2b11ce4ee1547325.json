{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.kDispose = exports.randomBytes = exports.COSMOS_DB_MSG = exports.DOCUMENT_DB_MSG = exports.COSMOS_DB_CHECK = exports.DOCUMENT_DB_CHECK = exports.MONGODB_WARNING_CODE = exports.DEFAULT_PK_FACTORY = exports.HostAddress = exports.BufferPool = exports.List = exports.MongoDBCollectionNamespace = exports.MongoDBNamespace = exports.ByteUtils = void 0;\nexports.isUint8Array = isUint8Array;\nexports.hostMatchesWildcards = hostMatchesWildcards;\nexports.normalizeHintField = normalizeHintField;\nexports.isObject = isObject;\nexports.mergeOptions = mergeOptions;\nexports.filterOptions = filterOptions;\nexports.applyRetryableWrites = applyRetryableWrites;\nexports.isPromiseLike = isPromiseLike;\nexports.decorateWithCollation = decorateWithCollation;\nexports.decorateWithReadConcern = decorateWithReadConcern;\nexports.getTopology = getTopology;\nexports.ns = ns;\nexports.makeCounter = makeCounter;\nexports.uuidV4 = uuidV4;\nexports.maxWireVersion = maxWireVersion;\nexports.arrayStrictEqual = arrayStrictEqual;\nexports.errorStrictEqual = errorStrictEqual;\nexports.makeStateMachine = makeStateMachine;\nexports.now = now;\nexports.calculateDurationInMs = calculateDurationInMs;\nexports.hasAtomicOperators = hasAtomicOperators;\nexports.resolveTimeoutOptions = resolveTimeoutOptions;\nexports.resolveOptions = resolveOptions;\nexports.isSuperset = isSuperset;\nexports.isHello = isHello;\nexports.setDifference = setDifference;\nexports.isRecord = isRecord;\nexports.emitWarning = emitWarning;\nexports.emitWarningOnce = emitWarningOnce;\nexports.enumToString = enumToString;\nexports.supportsRetryableWrites = supportsRetryableWrites;\nexports.shuffle = shuffle;\nexports.commandSupportsReadConcern = commandSupportsReadConcern;\nexports.compareObjectId = compareObjectId;\nexports.parseInteger = parseInteger;\nexports.parseUnsignedInteger = parseUnsignedInteger;\nexports.checkParentDomainMatch = checkParentDomainMatch;\nexports.get = get;\nexports.request = request;\nexports.isHostMatch = isHostMatch;\nexports.promiseWithResolvers = promiseWithResolvers;\nexports.squashError = squashError;\nexports.once = once;\nexports.maybeAddIdToDocuments = maybeAddIdToDocuments;\nexports.fileIsAccessible = fileIsAccessible;\nexports.csotMin = csotMin;\nexports.noop = noop;\nexports.decorateDecryptionResult = decorateDecryptionResult;\nexports.addAbortListener = addAbortListener;\nexports.abortable = abortable;\nconst crypto = require(\"crypto\");\nconst fs_1 = require(\"fs\");\nconst http = require(\"http\");\nconst timers_1 = require(\"timers\");\nconst url = require(\"url\");\nconst url_1 = require(\"url\");\nconst util_1 = require(\"util\");\nconst bson_1 = require(\"./bson\");\nconst constants_1 = require(\"./cmap/wire_protocol/constants\");\nconst constants_2 = require(\"./constants\");\nconst error_1 = require(\"./error\");\nconst read_concern_1 = require(\"./read_concern\");\nconst read_preference_1 = require(\"./read_preference\");\nconst common_1 = require(\"./sdam/common\");\nconst write_concern_1 = require(\"./write_concern\");\nexports.ByteUtils = {\n  toLocalBufferType(buffer) {\n    return Buffer.isBuffer(buffer) ? buffer : Buffer.from(buffer.buffer, buffer.byteOffset, buffer.byteLength);\n  },\n  equals(seqA, seqB) {\n    return exports.ByteUtils.toLocalBufferType(seqA).equals(seqB);\n  },\n  compare(seqA, seqB) {\n    return exports.ByteUtils.toLocalBufferType(seqA).compare(seqB);\n  },\n  toBase64(uint8array) {\n    return exports.ByteUtils.toLocalBufferType(uint8array).toString('base64');\n  }\n};\n/**\n * Returns true if value is a Uint8Array or a Buffer\n * @param value - any value that may be a Uint8Array\n */\nfunction isUint8Array(value) {\n  return value != null && typeof value === 'object' && Symbol.toStringTag in value && value[Symbol.toStringTag] === 'Uint8Array';\n}\n/**\n * Determines if a connection's address matches a user provided list\n * of domain wildcards.\n */\nfunction hostMatchesWildcards(host, wildcards) {\n  for (const wildcard of wildcards) {\n    if (host === wildcard || wildcard.startsWith('*.') && host?.endsWith(wildcard.substring(2, wildcard.length)) || wildcard.startsWith('*/') && host?.endsWith(wildcard.substring(2, wildcard.length))) {\n      return true;\n    }\n  }\n  return false;\n}\n/**\n * Ensure Hint field is in a shape we expect:\n * - object of index names mapping to 1 or -1\n * - just an index name\n * @internal\n */\nfunction normalizeHintField(hint) {\n  let finalHint = undefined;\n  if (typeof hint === 'string') {\n    finalHint = hint;\n  } else if (Array.isArray(hint)) {\n    finalHint = {};\n    hint.forEach(param => {\n      finalHint[param] = 1;\n    });\n  } else if (hint != null && typeof hint === 'object') {\n    finalHint = {};\n    for (const name in hint) {\n      finalHint[name] = hint[name];\n    }\n  }\n  return finalHint;\n}\nconst TO_STRING = object => Object.prototype.toString.call(object);\n/**\n * Checks if arg is an Object:\n * - **NOTE**: the check is based on the `[Symbol.toStringTag]() === 'Object'`\n * @internal\n */\nfunction isObject(arg) {\n  return '[object Object]' === TO_STRING(arg);\n}\n/** @internal */\nfunction mergeOptions(target, source) {\n  return {\n    ...target,\n    ...source\n  };\n}\n/** @internal */\nfunction filterOptions(options, names) {\n  const filterOptions = {};\n  for (const name in options) {\n    if (names.includes(name)) {\n      filterOptions[name] = options[name];\n    }\n  }\n  // Filtered options\n  return filterOptions;\n}\n/**\n * Applies retryWrites: true to a command if retryWrites is set on the command's database.\n * @internal\n *\n * @param target - The target command to which we will apply retryWrites.\n * @param db - The database from which we can inherit a retryWrites value.\n */\nfunction applyRetryableWrites(target, db) {\n  if (db && db.s.options?.retryWrites) {\n    target.retryWrites = true;\n  }\n  return target;\n}\n/**\n * Applies a write concern to a command based on well defined inheritance rules, optionally\n * detecting support for the write concern in the first place.\n * @internal\n *\n * @param target - the target command we will be applying the write concern to\n * @param sources - sources where we can inherit default write concerns from\n * @param options - optional settings passed into a command for write concern overrides\n */\n/**\n * Checks if a given value is a Promise\n *\n * @typeParam T - The resolution type of the possible promise\n * @param value - An object that could be a promise\n * @returns true if the provided value is a Promise\n */\nfunction isPromiseLike(value) {\n  return value != null && typeof value === 'object' && 'then' in value && typeof value.then === 'function';\n}\n/**\n * Applies collation to a given command.\n * @internal\n *\n * @param command - the command on which to apply collation\n * @param target - target of command\n * @param options - options containing collation settings\n */\nfunction decorateWithCollation(command, target, options) {\n  const capabilities = getTopology(target).capabilities;\n  if (options.collation && typeof options.collation === 'object') {\n    if (capabilities && capabilities.commandsTakeCollation) {\n      command.collation = options.collation;\n    } else {\n      throw new error_1.MongoCompatibilityError(`Current topology does not support collation`);\n    }\n  }\n}\n/**\n * Applies a read concern to a given command.\n * @internal\n *\n * @param command - the command on which to apply the read concern\n * @param coll - the parent collection of the operation calling this method\n */\nfunction decorateWithReadConcern(command, coll, options) {\n  if (options && options.session && options.session.inTransaction()) {\n    return;\n  }\n  const readConcern = Object.assign({}, command.readConcern || {});\n  if (coll.s.readConcern) {\n    Object.assign(readConcern, coll.s.readConcern);\n  }\n  if (Object.keys(readConcern).length > 0) {\n    Object.assign(command, {\n      readConcern: readConcern\n    });\n  }\n}\n/**\n * A helper function to get the topology from a given provider. Throws\n * if the topology cannot be found.\n * @throws MongoNotConnectedError\n * @internal\n */\nfunction getTopology(provider) {\n  // MongoClient or ClientSession or AbstractCursor\n  if ('topology' in provider && provider.topology) {\n    return provider.topology;\n  } else if ('client' in provider && provider.client.topology) {\n    return provider.client.topology;\n  }\n  throw new error_1.MongoNotConnectedError('MongoClient must be connected to perform this operation');\n}\n/** @internal */\nfunction ns(ns) {\n  return MongoDBNamespace.fromString(ns);\n}\n/** @public */\nclass MongoDBNamespace {\n  /**\n   * Create a namespace object\n   *\n   * @param db - database name\n   * @param collection - collection name\n   */\n  constructor(db, collection) {\n    this.db = db;\n    this.collection = collection;\n    this.collection = collection === '' ? undefined : collection;\n  }\n  toString() {\n    return this.collection ? `${this.db}.${this.collection}` : this.db;\n  }\n  withCollection(collection) {\n    return new MongoDBCollectionNamespace(this.db, collection);\n  }\n  static fromString(namespace) {\n    if (typeof namespace !== 'string' || namespace === '') {\n      // TODO(NODE-3483): Replace with MongoNamespaceError\n      throw new error_1.MongoRuntimeError(`Cannot parse namespace from \"${namespace}\"`);\n    }\n    const [db, ...collectionParts] = namespace.split('.');\n    const collection = collectionParts.join('.');\n    return new MongoDBNamespace(db, collection === '' ? undefined : collection);\n  }\n}\nexports.MongoDBNamespace = MongoDBNamespace;\n/**\n * @public\n *\n * A class representing a collection's namespace.  This class enforces (through Typescript) that\n * the `collection` portion of the namespace is defined and should only be\n * used in scenarios where this can be guaranteed.\n */\nclass MongoDBCollectionNamespace extends MongoDBNamespace {\n  constructor(db, collection) {\n    super(db, collection);\n    this.collection = collection;\n  }\n  static fromString(namespace) {\n    return super.fromString(namespace);\n  }\n}\nexports.MongoDBCollectionNamespace = MongoDBCollectionNamespace;\n/** @internal */\nfunction* makeCounter(seed = 0) {\n  let count = seed;\n  while (true) {\n    const newCount = count;\n    count += 1;\n    yield newCount;\n  }\n}\n/**\n * Synchronously Generate a UUIDv4\n * @internal\n */\nfunction uuidV4() {\n  const result = crypto.randomBytes(16);\n  result[6] = result[6] & 0x0f | 0x40;\n  result[8] = result[8] & 0x3f | 0x80;\n  return result;\n}\n/**\n * A helper function for determining `maxWireVersion` between legacy and new topology instances\n * @internal\n */\nfunction maxWireVersion(topologyOrServer) {\n  if (topologyOrServer) {\n    if (topologyOrServer.loadBalanced || topologyOrServer.serverApi?.version) {\n      // Since we do not have a monitor in the load balanced mode,\n      // we assume the load-balanced server is always pointed at the latest mongodb version.\n      // There is a risk that for on-prem deployments\n      // that don't upgrade immediately that this could alert to the\n      // application that a feature is available that is actually not.\n      // We also return the max supported wire version for serverAPI.\n      return constants_1.MAX_SUPPORTED_WIRE_VERSION;\n    }\n    if (topologyOrServer.hello) {\n      return topologyOrServer.hello.maxWireVersion;\n    }\n    if ('lastHello' in topologyOrServer && typeof topologyOrServer.lastHello === 'function') {\n      const lastHello = topologyOrServer.lastHello();\n      if (lastHello) {\n        return lastHello.maxWireVersion;\n      }\n    }\n    if (topologyOrServer.description && 'maxWireVersion' in topologyOrServer.description && topologyOrServer.description.maxWireVersion != null) {\n      return topologyOrServer.description.maxWireVersion;\n    }\n  }\n  return 0;\n}\n/** @internal */\nfunction arrayStrictEqual(arr, arr2) {\n  if (!Array.isArray(arr) || !Array.isArray(arr2)) {\n    return false;\n  }\n  return arr.length === arr2.length && arr.every((elt, idx) => elt === arr2[idx]);\n}\n/** @internal */\nfunction errorStrictEqual(lhs, rhs) {\n  if (lhs === rhs) {\n    return true;\n  }\n  if (!lhs || !rhs) {\n    return lhs === rhs;\n  }\n  if (lhs == null && rhs != null || lhs != null && rhs == null) {\n    return false;\n  }\n  if (lhs.constructor.name !== rhs.constructor.name) {\n    return false;\n  }\n  if (lhs.message !== rhs.message) {\n    return false;\n  }\n  return true;\n}\n/** @internal */\nfunction makeStateMachine(stateTable) {\n  return function stateTransition(target, newState) {\n    const legalStates = stateTable[target.s.state];\n    if (legalStates && legalStates.indexOf(newState) < 0) {\n      throw new error_1.MongoRuntimeError(`illegal state transition from [${target.s.state}] => [${newState}], allowed: [${legalStates}]`);\n    }\n    target.emit('stateChanged', target.s.state, newState);\n    target.s.state = newState;\n  };\n}\n/** @internal */\nfunction now() {\n  const hrtime = process.hrtime();\n  return Math.floor(hrtime[0] * 1000 + hrtime[1] / 1000000);\n}\n/** @internal */\nfunction calculateDurationInMs(started) {\n  if (typeof started !== 'number') {\n    return -1;\n  }\n  const elapsed = now() - started;\n  return elapsed < 0 ? 0 : elapsed;\n}\n/** @internal */\nfunction hasAtomicOperators(doc) {\n  if (Array.isArray(doc)) {\n    for (const document of doc) {\n      if (hasAtomicOperators(document)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  const keys = Object.keys(doc);\n  return keys.length > 0 && keys[0][0] === '$';\n}\nfunction resolveTimeoutOptions(client, options) {\n  const {\n    socketTimeoutMS,\n    serverSelectionTimeoutMS,\n    waitQueueTimeoutMS,\n    timeoutMS\n  } = client.s.options;\n  return {\n    socketTimeoutMS,\n    serverSelectionTimeoutMS,\n    waitQueueTimeoutMS,\n    timeoutMS,\n    ...options\n  };\n}\n/**\n * Merge inherited properties from parent into options, prioritizing values from options,\n * then values from parent.\n *\n * @param parent - An optional owning class of the operation being run. ex. Db/Collection/MongoClient.\n * @param options - The options passed to the operation method.\n *\n * @internal\n */\nfunction resolveOptions(parent, options) {\n  const result = Object.assign({}, options, (0, bson_1.resolveBSONOptions)(options, parent));\n  const timeoutMS = options?.timeoutMS ?? parent?.timeoutMS;\n  // Users cannot pass a readConcern/writeConcern to operations in a transaction\n  const session = options?.session;\n  if (!session?.inTransaction()) {\n    const readConcern = read_concern_1.ReadConcern.fromOptions(options) ?? parent?.readConcern;\n    if (readConcern) {\n      result.readConcern = readConcern;\n    }\n    let writeConcern = write_concern_1.WriteConcern.fromOptions(options) ?? parent?.writeConcern;\n    if (writeConcern) {\n      if (timeoutMS != null) {\n        writeConcern = write_concern_1.WriteConcern.fromOptions({\n          writeConcern: {\n            ...writeConcern,\n            wtimeout: undefined,\n            wtimeoutMS: undefined\n          }\n        });\n      }\n      result.writeConcern = writeConcern;\n    }\n  }\n  result.timeoutMS = timeoutMS;\n  const readPreference = read_preference_1.ReadPreference.fromOptions(options) ?? parent?.readPreference;\n  if (readPreference) {\n    result.readPreference = readPreference;\n  }\n  const isConvenientTransaction = session?.explicit && session?.timeoutContext != null;\n  if (isConvenientTransaction && options?.timeoutMS != null) {\n    throw new error_1.MongoInvalidArgumentError('An operation cannot be given a timeoutMS setting when inside a withTransaction call that has a timeoutMS setting');\n  }\n  return result;\n}\nfunction isSuperset(set, subset) {\n  set = Array.isArray(set) ? new Set(set) : set;\n  subset = Array.isArray(subset) ? new Set(subset) : subset;\n  for (const elem of subset) {\n    if (!set.has(elem)) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Checks if the document is a Hello request\n * @internal\n */\nfunction isHello(doc) {\n  return doc[constants_2.LEGACY_HELLO_COMMAND] || doc.hello ? true : false;\n}\n/** Returns the items that are uniquely in setA */\nfunction setDifference(setA, setB) {\n  const difference = new Set(setA);\n  for (const elem of setB) {\n    difference.delete(elem);\n  }\n  return difference;\n}\nconst HAS_OWN = (object, prop) => Object.prototype.hasOwnProperty.call(object, prop);\nfunction isRecord(value, requiredKeys = undefined) {\n  if (!isObject(value)) {\n    return false;\n  }\n  const ctor = value.constructor;\n  if (ctor && ctor.prototype) {\n    if (!isObject(ctor.prototype)) {\n      return false;\n    }\n    // Check to see if some method exists from the Object exists\n    if (!HAS_OWN(ctor.prototype, 'isPrototypeOf')) {\n      return false;\n    }\n  }\n  if (requiredKeys) {\n    const keys = Object.keys(value);\n    return isSuperset(keys, requiredKeys);\n  }\n  return true;\n}\n/**\n * A sequential list of items in a circularly linked list\n * @remarks\n * The head node is special, it is always defined and has a value of null.\n * It is never \"included\" in the list, in that, it is not returned by pop/shift or yielded by the iterator.\n * The circular linkage and always defined head node are to reduce checks for null next/prev references to zero.\n * New nodes are declared as object literals with keys always in the same order: next, prev, value.\n * @internal\n */\nclass List {\n  get length() {\n    return this.count;\n  }\n  get [Symbol.toStringTag]() {\n    return 'List';\n  }\n  constructor() {\n    this.count = 0;\n    // this is carefully crafted:\n    // declaring a complete and consistently key ordered\n    // object is beneficial to the runtime optimizations\n    this.head = {\n      next: null,\n      prev: null,\n      value: null\n    };\n    this.head.next = this.head;\n    this.head.prev = this.head;\n  }\n  toArray() {\n    return Array.from(this);\n  }\n  toString() {\n    return `head <=> ${this.toArray().join(' <=> ')} <=> head`;\n  }\n  *[Symbol.iterator]() {\n    for (const node of this.nodes()) {\n      yield node.value;\n    }\n  }\n  *nodes() {\n    let ptr = this.head.next;\n    while (ptr !== this.head) {\n      // Save next before yielding so that we make removing within iteration safe\n      const {\n        next\n      } = ptr;\n      yield ptr;\n      ptr = next;\n    }\n  }\n  /** Insert at end of list */\n  push(value) {\n    this.count += 1;\n    const newNode = {\n      next: this.head,\n      prev: this.head.prev,\n      value\n    };\n    this.head.prev.next = newNode;\n    this.head.prev = newNode;\n  }\n  /** Inserts every item inside an iterable instead of the iterable itself */\n  pushMany(iterable) {\n    for (const value of iterable) {\n      this.push(value);\n    }\n  }\n  /** Insert at front of list */\n  unshift(value) {\n    this.count += 1;\n    const newNode = {\n      next: this.head.next,\n      prev: this.head,\n      value\n    };\n    this.head.next.prev = newNode;\n    this.head.next = newNode;\n  }\n  remove(node) {\n    if (node === this.head || this.length === 0) {\n      return null;\n    }\n    this.count -= 1;\n    const prevNode = node.prev;\n    const nextNode = node.next;\n    prevNode.next = nextNode;\n    nextNode.prev = prevNode;\n    return node.value;\n  }\n  /** Removes the first node at the front of the list */\n  shift() {\n    return this.remove(this.head.next);\n  }\n  /** Removes the last node at the end of the list */\n  pop() {\n    return this.remove(this.head.prev);\n  }\n  /** Iterates through the list and removes nodes where filter returns true */\n  prune(filter) {\n    for (const node of this.nodes()) {\n      if (filter(node.value)) {\n        this.remove(node);\n      }\n    }\n  }\n  clear() {\n    this.count = 0;\n    this.head.next = this.head;\n    this.head.prev = this.head;\n  }\n  /** Returns the first item in the list, does not remove */\n  first() {\n    // If the list is empty, value will be the head's null\n    return this.head.next.value;\n  }\n  /** Returns the last item in the list, does not remove */\n  last() {\n    // If the list is empty, value will be the head's null\n    return this.head.prev.value;\n  }\n}\nexports.List = List;\n/**\n * A pool of Buffers which allow you to read them as if they were one\n * @internal\n */\nclass BufferPool {\n  constructor() {\n    this.buffers = new List();\n    this.totalByteLength = 0;\n  }\n  get length() {\n    return this.totalByteLength;\n  }\n  /** Adds a buffer to the internal buffer pool list */\n  append(buffer) {\n    this.buffers.push(buffer);\n    this.totalByteLength += buffer.length;\n  }\n  /**\n   * If BufferPool contains 4 bytes or more construct an int32 from the leading bytes,\n   * otherwise return null. Size can be negative, caller should error check.\n   */\n  getInt32() {\n    if (this.totalByteLength < 4) {\n      return null;\n    }\n    const firstBuffer = this.buffers.first();\n    if (firstBuffer != null && firstBuffer.byteLength >= 4) {\n      return firstBuffer.readInt32LE(0);\n    }\n    // Unlikely case: an int32 is split across buffers.\n    // Use read and put the returned buffer back on top\n    const top4Bytes = this.read(4);\n    const value = top4Bytes.readInt32LE(0);\n    // Put it back.\n    this.totalByteLength += 4;\n    this.buffers.unshift(top4Bytes);\n    return value;\n  }\n  /** Reads the requested number of bytes, optionally consuming them */\n  read(size) {\n    if (typeof size !== 'number' || size < 0) {\n      throw new error_1.MongoInvalidArgumentError('Argument \"size\" must be a non-negative number');\n    }\n    // oversized request returns empty buffer\n    if (size > this.totalByteLength) {\n      return Buffer.alloc(0);\n    }\n    // We know we have enough, we just don't know how it is spread across chunks\n    // TODO(NODE-4732): alloc API should change based on raw option\n    const result = Buffer.allocUnsafe(size);\n    for (let bytesRead = 0; bytesRead < size;) {\n      const buffer = this.buffers.shift();\n      if (buffer == null) {\n        break;\n      }\n      const bytesRemaining = size - bytesRead;\n      const bytesReadable = Math.min(bytesRemaining, buffer.byteLength);\n      const bytes = buffer.subarray(0, bytesReadable);\n      result.set(bytes, bytesRead);\n      bytesRead += bytesReadable;\n      this.totalByteLength -= bytesReadable;\n      if (bytesReadable < buffer.byteLength) {\n        this.buffers.unshift(buffer.subarray(bytesReadable));\n      }\n    }\n    return result;\n  }\n}\nexports.BufferPool = BufferPool;\n/** @public */\nclass HostAddress {\n  constructor(hostString) {\n    this.host = undefined;\n    this.port = undefined;\n    this.socketPath = undefined;\n    this.isIPv6 = false;\n    const escapedHost = hostString.split(' ').join('%20'); // escape spaces, for socket path hosts\n    if (escapedHost.endsWith('.sock')) {\n      // heuristically determine if we're working with a domain socket\n      this.socketPath = decodeURIComponent(escapedHost);\n      return;\n    }\n    const urlString = `iLoveJS://${escapedHost}`;\n    let url;\n    try {\n      url = new url_1.URL(urlString);\n    } catch (urlError) {\n      const runtimeError = new error_1.MongoRuntimeError(`Unable to parse ${escapedHost} with URL`);\n      runtimeError.cause = urlError;\n      throw runtimeError;\n    }\n    const hostname = url.hostname;\n    const port = url.port;\n    let normalized = decodeURIComponent(hostname).toLowerCase();\n    if (normalized.startsWith('[') && normalized.endsWith(']')) {\n      this.isIPv6 = true;\n      normalized = normalized.substring(1, hostname.length - 1);\n    }\n    this.host = normalized.toLowerCase();\n    if (typeof port === 'number') {\n      this.port = port;\n    } else if (typeof port === 'string' && port !== '') {\n      this.port = Number.parseInt(port, 10);\n    } else {\n      this.port = 27017;\n    }\n    if (this.port === 0) {\n      throw new error_1.MongoParseError('Invalid port (zero) with hostname');\n    }\n    Object.freeze(this);\n  }\n  [Symbol.for('nodejs.util.inspect.custom')]() {\n    return this.inspect();\n  }\n  inspect() {\n    return `new HostAddress('${this.toString()}')`;\n  }\n  toString() {\n    if (typeof this.host === 'string') {\n      if (this.isIPv6) {\n        return `[${this.host}]:${this.port}`;\n      }\n      return `${this.host}:${this.port}`;\n    }\n    return `${this.socketPath}`;\n  }\n  static fromString(s) {\n    return new HostAddress(s);\n  }\n  static fromHostPort(host, port) {\n    if (host.includes(':')) {\n      host = `[${host}]`; // IPv6 address\n    }\n    return HostAddress.fromString(`${host}:${port}`);\n  }\n  static fromSrvRecord({\n    name,\n    port\n  }) {\n    return HostAddress.fromHostPort(name, port);\n  }\n  toHostPort() {\n    if (this.socketPath) {\n      return {\n        host: this.socketPath,\n        port: 0\n      };\n    }\n    const host = this.host ?? '';\n    const port = this.port ?? 0;\n    return {\n      host,\n      port\n    };\n  }\n}\nexports.HostAddress = HostAddress;\nexports.DEFAULT_PK_FACTORY = {\n  // We prefer not to rely on ObjectId having a createPk method\n  createPk() {\n    return new bson_1.ObjectId();\n  }\n};\n/**\n * When the driver used emitWarning the code will be equal to this.\n * @public\n *\n * @example\n * ```ts\n * process.on('warning', (warning) => {\n *  if (warning.code === MONGODB_WARNING_CODE) console.error('Ah an important warning! :)')\n * })\n * ```\n */\nexports.MONGODB_WARNING_CODE = 'MONGODB DRIVER';\n/** @internal */\nfunction emitWarning(message) {\n  return process.emitWarning(message, {\n    code: exports.MONGODB_WARNING_CODE\n  });\n}\nconst emittedWarnings = new Set();\n/**\n * Will emit a warning once for the duration of the application.\n * Uses the message to identify if it has already been emitted\n * so using string interpolation can cause multiple emits\n * @internal\n */\nfunction emitWarningOnce(message) {\n  if (!emittedWarnings.has(message)) {\n    emittedWarnings.add(message);\n    return emitWarning(message);\n  }\n}\n/**\n * Takes a JS object and joins the values into a string separated by ', '\n */\nfunction enumToString(en) {\n  return Object.values(en).join(', ');\n}\n/**\n * Determine if a server supports retryable writes.\n *\n * @internal\n */\nfunction supportsRetryableWrites(server) {\n  if (!server) {\n    return false;\n  }\n  if (server.loadBalanced) {\n    // Loadbalanced topologies will always support retry writes\n    return true;\n  }\n  if (server.description.logicalSessionTimeoutMinutes != null) {\n    // that supports sessions\n    if (server.description.type !== common_1.ServerType.Standalone) {\n      // and that is not a standalone\n      return true;\n    }\n  }\n  return false;\n}\n/**\n * Fisher–Yates Shuffle\n *\n * Reference: https://bost.ocks.org/mike/shuffle/\n * @param sequence - items to be shuffled\n * @param limit - Defaults to `0`. If nonzero shuffle will slice the randomized array e.g, `.slice(0, limit)` otherwise will return the entire randomized array.\n */\nfunction shuffle(sequence, limit = 0) {\n  const items = Array.from(sequence); // shallow copy in order to never shuffle the input\n  if (limit > items.length) {\n    throw new error_1.MongoRuntimeError('Limit must be less than the number of items');\n  }\n  let remainingItemsToShuffle = items.length;\n  const lowerBound = limit % items.length === 0 ? 1 : items.length - limit;\n  while (remainingItemsToShuffle > lowerBound) {\n    // Pick a remaining element\n    const randomIndex = Math.floor(Math.random() * remainingItemsToShuffle);\n    remainingItemsToShuffle -= 1;\n    // And swap it with the current element\n    const swapHold = items[remainingItemsToShuffle];\n    items[remainingItemsToShuffle] = items[randomIndex];\n    items[randomIndex] = swapHold;\n  }\n  return limit % items.length === 0 ? items : items.slice(lowerBound);\n}\n/**\n * TODO(NODE-4936): read concern eligibility for commands should be codified in command construction\n * @internal\n * @see https://github.com/mongodb/specifications/blob/master/source/read-write-concern/read-write-concern.md#read-concern\n */\nfunction commandSupportsReadConcern(command) {\n  if (command.aggregate || command.count || command.distinct || command.find || command.geoNear) {\n    return true;\n  }\n  return false;\n}\n/**\n * Compare objectIds. `null` is always less\n * - `+1 = oid1 is greater than oid2`\n * - `-1 = oid1 is less than oid2`\n * - `+0 = oid1 is equal oid2`\n */\nfunction compareObjectId(oid1, oid2) {\n  if (oid1 == null && oid2 == null) {\n    return 0;\n  }\n  if (oid1 == null) {\n    return -1;\n  }\n  if (oid2 == null) {\n    return 1;\n  }\n  return exports.ByteUtils.compare(oid1.id, oid2.id);\n}\nfunction parseInteger(value) {\n  if (typeof value === 'number') return Math.trunc(value);\n  const parsedValue = Number.parseInt(String(value), 10);\n  return Number.isNaN(parsedValue) ? null : parsedValue;\n}\nfunction parseUnsignedInteger(value) {\n  const parsedInt = parseInteger(value);\n  return parsedInt != null && parsedInt >= 0 ? parsedInt : null;\n}\n/**\n * This function throws a MongoAPIError in the event that either of the following is true:\n * * If the provided address domain does not match the provided parent domain\n * * If the parent domain contains less than three `.` separated parts and the provided address does not contain at least one more domain level than its parent\n *\n * If a DNS server were to become compromised SRV records would still need to\n * advertise addresses that are under the same domain as the srvHost.\n *\n * @param address - The address to check against a domain\n * @param srvHost - The domain to check the provided address against\n * @returns void\n */\nfunction checkParentDomainMatch(address, srvHost) {\n  // Remove trailing dot if exists on either the resolved address or the srv hostname\n  const normalizedAddress = address.endsWith('.') ? address.slice(0, address.length - 1) : address;\n  const normalizedSrvHost = srvHost.endsWith('.') ? srvHost.slice(0, srvHost.length - 1) : srvHost;\n  const allCharacterBeforeFirstDot = /^.*?\\./;\n  const srvIsLessThanThreeParts = normalizedSrvHost.split('.').length < 3;\n  // Remove all characters before first dot\n  // Add leading dot back to string so\n  //   an srvHostDomain = '.trusted.site'\n  //   will not satisfy an addressDomain that endsWith '.fake-trusted.site'\n  const addressDomain = `.${normalizedAddress.replace(allCharacterBeforeFirstDot, '')}`;\n  let srvHostDomain = srvIsLessThanThreeParts ? normalizedSrvHost : `.${normalizedSrvHost.replace(allCharacterBeforeFirstDot, '')}`;\n  if (!srvHostDomain.startsWith('.')) {\n    srvHostDomain = '.' + srvHostDomain;\n  }\n  if (srvIsLessThanThreeParts && normalizedAddress.split('.').length <= normalizedSrvHost.split('.').length) {\n    throw new error_1.MongoAPIError('Server record does not have at least one more domain level than parent URI');\n  }\n  if (!addressDomain.endsWith(srvHostDomain)) {\n    throw new error_1.MongoAPIError('Server record does not share hostname with parent URI');\n  }\n}\n/**\n * Perform a get request that returns status and body.\n * @internal\n */\nfunction get(url, options = {}) {\n  return new Promise((resolve, reject) => {\n    /* eslint-disable prefer-const */\n    let timeoutId;\n    const request = http.get(url, options, response => {\n      response.setEncoding('utf8');\n      let body = '';\n      response.on('data', chunk => body += chunk);\n      response.on('end', () => {\n        (0, timers_1.clearTimeout)(timeoutId);\n        resolve({\n          status: response.statusCode,\n          body\n        });\n      });\n    }).on('error', error => {\n      (0, timers_1.clearTimeout)(timeoutId);\n      reject(error);\n    }).end();\n    timeoutId = (0, timers_1.setTimeout)(() => {\n      request.destroy(new error_1.MongoNetworkTimeoutError(`request timed out after 10 seconds`));\n    }, 10000);\n  });\n}\nasync function request(uri, options = {}) {\n  return await new Promise((resolve, reject) => {\n    const requestOptions = {\n      method: 'GET',\n      timeout: 10000,\n      json: true,\n      ...url.parse(uri),\n      ...options\n    };\n    const req = http.request(requestOptions, res => {\n      res.setEncoding('utf8');\n      let data = '';\n      res.on('data', d => {\n        data += d;\n      });\n      res.once('end', () => {\n        if (options.json === false) {\n          resolve(data);\n          return;\n        }\n        try {\n          const parsed = JSON.parse(data);\n          resolve(parsed);\n        } catch {\n          // TODO(NODE-3483)\n          reject(new error_1.MongoRuntimeError(`Invalid JSON response: \"${data}\"`));\n        }\n      });\n    });\n    req.once('timeout', () => req.destroy(new error_1.MongoNetworkTimeoutError(`Network request to ${uri} timed out after ${options.timeout} ms`)));\n    req.once('error', error => reject(error));\n    req.end();\n  });\n}\n/** @internal */\nexports.DOCUMENT_DB_CHECK = /(\\.docdb\\.amazonaws\\.com$)|(\\.docdb-elastic\\.amazonaws\\.com$)/;\n/** @internal */\nexports.COSMOS_DB_CHECK = /\\.cosmos\\.azure\\.com$/;\n/** @internal */\nexports.DOCUMENT_DB_MSG = 'You appear to be connected to a DocumentDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/documentdb';\n/** @internal */\nexports.COSMOS_DB_MSG = 'You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb';\n/** @internal */\nfunction isHostMatch(match, host) {\n  return host && match.test(host.toLowerCase()) ? true : false;\n}\nfunction promiseWithResolvers() {\n  let resolve;\n  let reject;\n  const promise = new Promise(function withResolversExecutor(promiseResolve, promiseReject) {\n    resolve = promiseResolve;\n    reject = promiseReject;\n  });\n  return {\n    promise,\n    resolve,\n    reject\n  };\n}\n/**\n * A noop function intended for use in preventing unhandled rejections.\n *\n * @example\n * ```js\n * const promise = myAsyncTask();\n * // eslint-disable-next-line github/no-then\n * promise.then(undefined, squashError);\n * ```\n */\nfunction squashError(_error) {\n  return;\n}\nexports.randomBytes = (0, util_1.promisify)(crypto.randomBytes);\n/**\n * Replicates the events.once helper.\n *\n * Removes unused signal logic and It **only** supports 0 or 1 argument events.\n *\n * @param ee - An event emitter that may emit `ev`\n * @param name - An event name to wait for\n */\nasync function once(ee, name, options) {\n  options?.signal?.throwIfAborted();\n  const {\n    promise,\n    resolve,\n    reject\n  } = promiseWithResolvers();\n  const onEvent = data => resolve(data);\n  const onError = error => reject(error);\n  const abortListener = addAbortListener(options?.signal, function () {\n    reject(this.reason);\n  });\n  ee.once(name, onEvent).once('error', onError);\n  try {\n    return await promise;\n  } finally {\n    ee.off(name, onEvent);\n    ee.off('error', onError);\n    abortListener?.[exports.kDispose]();\n  }\n}\nfunction maybeAddIdToDocuments(coll, docOrDocs, options) {\n  const forceServerObjectId = typeof options.forceServerObjectId === 'boolean' ? options.forceServerObjectId : coll.s.db.options?.forceServerObjectId;\n  // no need to modify the docs if server sets the ObjectId\n  if (forceServerObjectId === true) {\n    return docOrDocs;\n  }\n  const transform = doc => {\n    if (doc._id == null) {\n      doc._id = coll.s.pkFactory.createPk();\n    }\n    return doc;\n  };\n  return Array.isArray(docOrDocs) ? docOrDocs.map(transform) : transform(docOrDocs);\n}\nasync function fileIsAccessible(fileName, mode) {\n  try {\n    await fs_1.promises.access(fileName, mode);\n    return true;\n  } catch {\n    return false;\n  }\n}\nfunction csotMin(duration1, duration2) {\n  if (duration1 === 0) return duration2;\n  if (duration2 === 0) return duration1;\n  return Math.min(duration1, duration2);\n}\nfunction noop() {\n  return;\n}\n/**\n * Recurse through the (identically-shaped) `decrypted` and `original`\n * objects and attach a `decryptedKeys` property on each sub-object that\n * contained encrypted fields. Because we only call this on BSON responses,\n * we do not need to worry about circular references.\n *\n * @internal\n */\nfunction decorateDecryptionResult(decrypted, original, isTopLevelDecorateCall = true) {\n  if (isTopLevelDecorateCall) {\n    // The original value could have been either a JS object or a BSON buffer\n    if (Buffer.isBuffer(original)) {\n      original = (0, bson_1.deserialize)(original);\n    }\n    if (Buffer.isBuffer(decrypted)) {\n      throw new error_1.MongoRuntimeError('Expected result of decryption to be deserialized BSON object');\n    }\n  }\n  if (!decrypted || typeof decrypted !== 'object') return;\n  for (const k of Object.keys(decrypted)) {\n    const originalValue = original[k];\n    // An object was decrypted by libmongocrypt if and only if it was\n    // a BSON Binary object with subtype 6.\n    if (originalValue && originalValue._bsontype === 'Binary' && originalValue.sub_type === 6) {\n      if (!decrypted[constants_2.kDecoratedKeys]) {\n        Object.defineProperty(decrypted, constants_2.kDecoratedKeys, {\n          value: [],\n          configurable: true,\n          enumerable: false,\n          writable: false\n        });\n      }\n      // this is defined in the preceding if-statement\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      decrypted[constants_2.kDecoratedKeys].push(k);\n      // Do not recurse into this decrypted value. It could be a sub-document/array,\n      // in which case there is no original value associated with its subfields.\n      continue;\n    }\n    decorateDecryptionResult(decrypted[k], originalValue, false);\n  }\n}\n/** @internal */\nexports.kDispose = Symbol.dispose ?? Symbol('dispose');\n/**\n * A utility that helps with writing listener code idiomatically\n *\n * @example\n * ```js\n * using listener = addAbortListener(signal, function () {\n *   console.log('aborted', this.reason);\n * });\n * ```\n *\n * @param signal - if exists adds an abort listener\n * @param listener - the listener to be added to signal\n * @returns A disposable that will remove the abort listener\n */\nfunction addAbortListener(signal, listener) {\n  if (signal == null) return;\n  signal.addEventListener('abort', listener, {\n    once: true\n  });\n  return {\n    [exports.kDispose]: () => signal.removeEventListener('abort', listener)\n  };\n}\n/**\n * Takes a promise and races it with a promise wrapping the abort event of the optionally provided signal.\n * The given promise is _always_ ordered before the signal's abort promise.\n * When given an already rejected promise and an already aborted signal, the promise's rejection takes precedence.\n *\n * Any asynchronous processing in `promise` will continue even after the abort signal has fired,\n * but control will be returned to the caller\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/race\n *\n * @param promise - A promise to discard if the signal aborts\n * @param options - An options object carrying an optional signal\n */\nasync function abortable(promise, {\n  signal\n}) {\n  if (signal == null) {\n    return await promise;\n  }\n  const {\n    promise: aborted,\n    reject\n  } = promiseWithResolvers();\n  const abortListener = signal.aborted ? reject(signal.reason) : addAbortListener(signal, function () {\n    reject(this.reason);\n  });\n  try {\n    return await Promise.race([promise, aborted]);\n  } finally {\n    abortListener?.[exports.kDispose]();\n  }\n}", "map": {"version": 3, "names": ["exports", "isUint8Array", "hostMatchesWildcards", "normalizeHintField", "isObject", "mergeOptions", "filterOptions", "applyRetryableWrites", "isPromiseLike", "decorateWithCollation", "decorateWithReadConcern", "getTopology", "ns", "makeCounter", "uuidV4", "maxWireVersion", "arrayStrictEqual", "errorStrictEqual", "makeStateMachine", "now", "calculateDurationInMs", "hasAtomicOperators", "resolveTimeoutOptions", "resolveOptions", "isSuperset", "<PERSON><PERSON><PERSON>", "setDifference", "isRecord", "emitWarning", "emitWarningOnce", "enumToString", "supportsRetryableWrites", "shuffle", "commandSupportsReadConcern", "compareObjectId", "parseInteger", "parseUnsignedInteger", "checkParentDomainMatch", "get", "request", "isHostMatch", "promiseWithResolvers", "squashError", "once", "maybeAddIdToDocuments", "fileIsAccessible", "csotMin", "noop", "decorateDecryptionResult", "addAbortListener", "abortable", "crypto", "require", "fs_1", "http", "timers_1", "url", "url_1", "util_1", "bson_1", "constants_1", "constants_2", "error_1", "read_concern_1", "read_preference_1", "common_1", "write_concern_1", "ByteUtils", "toLocalBufferType", "buffer", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "from", "byteOffset", "byteLength", "equals", "seqA", "seqB", "compare", "toBase64", "uint8array", "toString", "value", "Symbol", "toStringTag", "host", "wildcards", "wildcard", "startsWith", "endsWith", "substring", "length", "hint", "finalHint", "undefined", "Array", "isArray", "for<PERSON>ach", "param", "name", "TO_STRING", "object", "Object", "prototype", "call", "arg", "target", "source", "options", "names", "includes", "db", "s", "retryWrites", "then", "command", "capabilities", "collation", "commandsTakeCollation", "MongoCompatibilityError", "coll", "session", "inTransaction", "readConcern", "assign", "keys", "provider", "topology", "client", "MongoNotConnectedError", "MongoDBNamespace", "fromString", "constructor", "collection", "withCollection", "MongoDBCollectionNamespace", "namespace", "MongoRuntimeError", "collectionParts", "split", "join", "seed", "count", "newCount", "result", "randomBytes", "topologyOrServer", "loadBalanced", "serverApi", "version", "MAX_SUPPORTED_WIRE_VERSION", "hello", "<PERSON><PERSON><PERSON>", "description", "arr", "arr2", "every", "elt", "idx", "lhs", "rhs", "message", "stateTable", "stateTransition", "newState", "legalStates", "state", "indexOf", "emit", "hrtime", "process", "Math", "floor", "started", "elapsed", "doc", "document", "socketTimeoutMS", "serverSelectionTimeoutMS", "waitQueueTimeoutMS", "timeoutMS", "parent", "resolveBSONOptions", "ReadConcern", "fromOptions", "writeConcern", "WriteConcern", "wtimeout", "wtimeoutMS", "readPreference", "ReadPreference", "isConvenientTransaction", "explicit", "timeoutContext", "MongoInvalidArgumentError", "set", "subset", "Set", "elem", "has", "LEGACY_HELLO_COMMAND", "setA", "setB", "difference", "delete", "HAS_OWN", "prop", "hasOwnProperty", "requiredKeys", "ctor", "List", "head", "next", "prev", "toArray", "iterator", "node", "nodes", "ptr", "push", "newNode", "pushMany", "iterable", "unshift", "remove", "prevNode", "nextNode", "shift", "pop", "prune", "filter", "clear", "first", "last", "BufferPool", "buffers", "totalByteLength", "append", "getInt32", "firstBuffer", "readInt32LE", "top4Bytes", "read", "size", "alloc", "allocUnsafe", "bytesRead", "bytesRemaining", "bytesReadable", "min", "bytes", "subarray", "HostAddress", "hostString", "port", "socketPath", "isIPv6", "escapedHost", "decodeURIComponent", "urlString", "URL", "url<PERSON><PERSON>r", "runtimeError", "cause", "hostname", "normalized", "toLowerCase", "Number", "parseInt", "MongoParseError", "freeze", "for", "inspect", "fromHostPort", "fromSrvRecord", "toHostPort", "DEFAULT_PK_FACTORY", "createPk", "ObjectId", "MONGODB_WARNING_CODE", "code", "emittedWarnings", "add", "en", "values", "server", "logicalSessionTimeoutMinutes", "type", "ServerType", "Standalone", "sequence", "limit", "items", "remainingItemsToShuffle", "lowerBound", "randomIndex", "random", "swapHold", "slice", "aggregate", "distinct", "find", "geoNear", "oid1", "oid2", "id", "trunc", "parsedValue", "String", "isNaN", "parsedInt", "address", "srvHost", "normalizedAddress", "normalizedSrvHost", "allCharacterBeforeFirstDot", "srvIsLessThanThreeParts", "addressDomain", "replace", "srvHostDomain", "MongoAPIError", "Promise", "resolve", "reject", "timeoutId", "response", "setEncoding", "body", "on", "chunk", "clearTimeout", "status", "statusCode", "error", "end", "setTimeout", "destroy", "MongoNetworkTimeoutError", "uri", "requestOptions", "method", "timeout", "json", "parse", "req", "res", "data", "d", "parsed", "JSON", "DOCUMENT_DB_CHECK", "COSMOS_DB_CHECK", "DOCUMENT_DB_MSG", "COSMOS_DB_MSG", "match", "test", "promise", "withResolversExecutor", "promiseResolve", "promiseReject", "_error", "promisify", "ee", "signal", "throwIfAborted", "onEvent", "onError", "abortListener", "reason", "off", "kDispose", "docOrDocs", "forceServerObjectId", "transform", "_id", "pkFactory", "map", "fileName", "mode", "promises", "access", "duration1", "duration2", "decrypted", "original", "isTopLevelDecorateCall", "deserialize", "k", "originalValue", "_bsontype", "sub_type", "kDecoratedKeys", "defineProperty", "configurable", "enumerable", "writable", "dispose", "listener", "addEventListener", "removeEventListener", "aborted", "race"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\utils.ts"], "sourcesContent": ["import * as crypto from 'crypto';\nimport type { SrvRecord } from 'dns';\nimport { type EventEmitter } from 'events';\nimport { promises as fs } from 'fs';\nimport * as http from 'http';\nimport { clearTimeout, setTimeout } from 'timers';\nimport * as url from 'url';\nimport { URL } from 'url';\nimport { promisify } from 'util';\n\nimport { deserialize, type Document, ObjectId, resolveBSONOptions } from './bson';\nimport type { Connection } from './cmap/connection';\nimport { MAX_SUPPORTED_WIRE_VERSION } from './cmap/wire_protocol/constants';\nimport type { Collection } from './collection';\nimport { kDecoratedKeys, LEGACY_HELLO_COMMAND } from './constants';\nimport type { AbstractCursor } from './cursor/abstract_cursor';\nimport type { FindCursor } from './cursor/find_cursor';\nimport type { Db } from './db';\nimport {\n  type AnyError,\n  MongoAPIError,\n  MongoCompatibilityError,\n  MongoInvalidArgumentError,\n  MongoNetworkTimeoutError,\n  MongoNotConnectedError,\n  MongoParseError,\n  MongoRuntimeError\n} from './error';\nimport type { MongoClient } from './mongo_client';\nimport { type Abortable } from './mongo_types';\nimport type { CommandOperationOptions, OperationParent } from './operations/command';\nimport type { Hint, OperationOptions } from './operations/operation';\nimport { ReadConcern } from './read_concern';\nimport { ReadPreference } from './read_preference';\nimport { ServerType } from './sdam/common';\nimport type { Server } from './sdam/server';\nimport type { Topology } from './sdam/topology';\nimport type { ClientSession } from './sessions';\nimport { type TimeoutContextOptions } from './timeout';\nimport { WriteConcern } from './write_concern';\n\n/**\n * MongoDB Driver style callback\n * @public\n */\nexport type Callback<T = any> = (error?: AnyError, result?: T) => void;\n\nexport type AnyOptions = Document;\n\nexport const ByteUtils = {\n  toLocalBufferType(this: void, buffer: Buffer | Uint8Array): Buffer {\n    return Buffer.isBuffer(buffer)\n      ? buffer\n      : Buffer.from(buffer.buffer, buffer.byteOffset, buffer.byteLength);\n  },\n\n  equals(this: void, seqA: Uint8Array, seqB: Uint8Array) {\n    return ByteUtils.toLocalBufferType(seqA).equals(seqB);\n  },\n\n  compare(this: void, seqA: Uint8Array, seqB: Uint8Array) {\n    return ByteUtils.toLocalBufferType(seqA).compare(seqB);\n  },\n\n  toBase64(this: void, uint8array: Uint8Array) {\n    return ByteUtils.toLocalBufferType(uint8array).toString('base64');\n  }\n};\n\n/**\n * Returns true if value is a Uint8Array or a Buffer\n * @param value - any value that may be a Uint8Array\n */\nexport function isUint8Array(value: unknown): value is Uint8Array {\n  return (\n    value != null &&\n    typeof value === 'object' &&\n    Symbol.toStringTag in value &&\n    value[Symbol.toStringTag] === 'Uint8Array'\n  );\n}\n\n/**\n * Determines if a connection's address matches a user provided list\n * of domain wildcards.\n */\nexport function hostMatchesWildcards(host: string, wildcards: string[]): boolean {\n  for (const wildcard of wildcards) {\n    if (\n      host === wildcard ||\n      (wildcard.startsWith('*.') && host?.endsWith(wildcard.substring(2, wildcard.length))) ||\n      (wildcard.startsWith('*/') && host?.endsWith(wildcard.substring(2, wildcard.length)))\n    ) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Ensure Hint field is in a shape we expect:\n * - object of index names mapping to 1 or -1\n * - just an index name\n * @internal\n */\nexport function normalizeHintField(hint?: Hint): Hint | undefined {\n  let finalHint = undefined;\n\n  if (typeof hint === 'string') {\n    finalHint = hint;\n  } else if (Array.isArray(hint)) {\n    finalHint = {};\n\n    hint.forEach(param => {\n      finalHint[param] = 1;\n    });\n  } else if (hint != null && typeof hint === 'object') {\n    finalHint = {} as Document;\n    for (const name in hint) {\n      finalHint[name] = hint[name];\n    }\n  }\n\n  return finalHint;\n}\n\nconst TO_STRING = (object: unknown) => Object.prototype.toString.call(object);\n/**\n * Checks if arg is an Object:\n * - **NOTE**: the check is based on the `[Symbol.toStringTag]() === 'Object'`\n * @internal\n */\n\nexport function isObject(arg: unknown): arg is object {\n  return '[object Object]' === TO_STRING(arg);\n}\n\n/** @internal */\nexport function mergeOptions<T, S>(target: T, source: S): T & S {\n  return { ...target, ...source };\n}\n\n/** @internal */\nexport function filterOptions(options: AnyOptions, names: ReadonlyArray<string>): AnyOptions {\n  const filterOptions: AnyOptions = {};\n\n  for (const name in options) {\n    if (names.includes(name)) {\n      filterOptions[name] = options[name];\n    }\n  }\n\n  // Filtered options\n  return filterOptions;\n}\n\ninterface HasRetryableWrites {\n  retryWrites?: boolean;\n}\n/**\n * Applies retryWrites: true to a command if retryWrites is set on the command's database.\n * @internal\n *\n * @param target - The target command to which we will apply retryWrites.\n * @param db - The database from which we can inherit a retryWrites value.\n */\nexport function applyRetryableWrites<T extends HasRetryableWrites>(target: T, db?: Db): T {\n  if (db && db.s.options?.retryWrites) {\n    target.retryWrites = true;\n  }\n\n  return target;\n}\n\n/**\n * Applies a write concern to a command based on well defined inheritance rules, optionally\n * detecting support for the write concern in the first place.\n * @internal\n *\n * @param target - the target command we will be applying the write concern to\n * @param sources - sources where we can inherit default write concerns from\n * @param options - optional settings passed into a command for write concern overrides\n */\n\n/**\n * Checks if a given value is a Promise\n *\n * @typeParam T - The resolution type of the possible promise\n * @param value - An object that could be a promise\n * @returns true if the provided value is a Promise\n */\nexport function isPromiseLike<T = unknown>(value?: unknown): value is PromiseLike<T> {\n  return (\n    value != null &&\n    typeof value === 'object' &&\n    'then' in value &&\n    typeof value.then === 'function'\n  );\n}\n\n/**\n * Applies collation to a given command.\n * @internal\n *\n * @param command - the command on which to apply collation\n * @param target - target of command\n * @param options - options containing collation settings\n */\nexport function decorateWithCollation(\n  command: Document,\n  target: MongoClient | Db | Collection,\n  options: AnyOptions\n): void {\n  const capabilities = getTopology(target).capabilities;\n  if (options.collation && typeof options.collation === 'object') {\n    if (capabilities && capabilities.commandsTakeCollation) {\n      command.collation = options.collation;\n    } else {\n      throw new MongoCompatibilityError(`Current topology does not support collation`);\n    }\n  }\n}\n\n/**\n * Applies a read concern to a given command.\n * @internal\n *\n * @param command - the command on which to apply the read concern\n * @param coll - the parent collection of the operation calling this method\n */\nexport function decorateWithReadConcern(\n  command: Document,\n  coll: { s: { readConcern?: ReadConcern } },\n  options?: OperationOptions\n): void {\n  if (options && options.session && options.session.inTransaction()) {\n    return;\n  }\n  const readConcern = Object.assign({}, command.readConcern || {});\n  if (coll.s.readConcern) {\n    Object.assign(readConcern, coll.s.readConcern);\n  }\n\n  if (Object.keys(readConcern).length > 0) {\n    Object.assign(command, { readConcern: readConcern });\n  }\n}\n\n/**\n * @internal\n */\nexport type TopologyProvider =\n  | MongoClient\n  | ClientSession\n  | FindCursor\n  | AbstractCursor\n  | Collection<any>\n  | Db;\n\n/**\n * A helper function to get the topology from a given provider. Throws\n * if the topology cannot be found.\n * @throws MongoNotConnectedError\n * @internal\n */\nexport function getTopology(provider: TopologyProvider): Topology {\n  // MongoClient or ClientSession or AbstractCursor\n  if ('topology' in provider && provider.topology) {\n    return provider.topology;\n  } else if ('client' in provider && provider.client.topology) {\n    return provider.client.topology;\n  }\n\n  throw new MongoNotConnectedError('MongoClient must be connected to perform this operation');\n}\n\n/** @internal */\nexport function ns(ns: string): MongoDBNamespace {\n  return MongoDBNamespace.fromString(ns);\n}\n\n/** @public */\nexport class MongoDBNamespace {\n  /**\n   * Create a namespace object\n   *\n   * @param db - database name\n   * @param collection - collection name\n   */\n  constructor(\n    public db: string,\n    public collection?: string\n  ) {\n    this.collection = collection === '' ? undefined : collection;\n  }\n\n  toString(): string {\n    return this.collection ? `${this.db}.${this.collection}` : this.db;\n  }\n\n  withCollection(collection: string): MongoDBCollectionNamespace {\n    return new MongoDBCollectionNamespace(this.db, collection);\n  }\n\n  static fromString(namespace?: string): MongoDBNamespace {\n    if (typeof namespace !== 'string' || namespace === '') {\n      // TODO(NODE-3483): Replace with MongoNamespaceError\n      throw new MongoRuntimeError(`Cannot parse namespace from \"${namespace}\"`);\n    }\n\n    const [db, ...collectionParts] = namespace.split('.');\n    const collection = collectionParts.join('.');\n    return new MongoDBNamespace(db, collection === '' ? undefined : collection);\n  }\n}\n\n/**\n * @public\n *\n * A class representing a collection's namespace.  This class enforces (through Typescript) that\n * the `collection` portion of the namespace is defined and should only be\n * used in scenarios where this can be guaranteed.\n */\nexport class MongoDBCollectionNamespace extends MongoDBNamespace {\n  constructor(\n    db: string,\n    override collection: string\n  ) {\n    super(db, collection);\n  }\n\n  static override fromString(namespace?: string): MongoDBCollectionNamespace {\n    return super.fromString(namespace) as MongoDBCollectionNamespace;\n  }\n}\n\n/** @internal */\nexport function* makeCounter(seed = 0): Generator<number> {\n  let count = seed;\n  while (true) {\n    const newCount = count;\n    count += 1;\n    yield newCount;\n  }\n}\n\n/**\n * Synchronously Generate a UUIDv4\n * @internal\n */\nexport function uuidV4(): Buffer {\n  const result = crypto.randomBytes(16);\n  result[6] = (result[6] & 0x0f) | 0x40;\n  result[8] = (result[8] & 0x3f) | 0x80;\n  return result;\n}\n\n/**\n * A helper function for determining `maxWireVersion` between legacy and new topology instances\n * @internal\n */\nexport function maxWireVersion(topologyOrServer?: Connection | Topology | Server): number {\n  if (topologyOrServer) {\n    if (topologyOrServer.loadBalanced || topologyOrServer.serverApi?.version) {\n      // Since we do not have a monitor in the load balanced mode,\n      // we assume the load-balanced server is always pointed at the latest mongodb version.\n      // There is a risk that for on-prem deployments\n      // that don't upgrade immediately that this could alert to the\n      // application that a feature is available that is actually not.\n      // We also return the max supported wire version for serverAPI.\n      return MAX_SUPPORTED_WIRE_VERSION;\n    }\n    if (topologyOrServer.hello) {\n      return topologyOrServer.hello.maxWireVersion;\n    }\n\n    if ('lastHello' in topologyOrServer && typeof topologyOrServer.lastHello === 'function') {\n      const lastHello = topologyOrServer.lastHello();\n      if (lastHello) {\n        return lastHello.maxWireVersion;\n      }\n    }\n\n    if (\n      topologyOrServer.description &&\n      'maxWireVersion' in topologyOrServer.description &&\n      topologyOrServer.description.maxWireVersion != null\n    ) {\n      return topologyOrServer.description.maxWireVersion;\n    }\n  }\n\n  return 0;\n}\n\n/** @internal */\nexport function arrayStrictEqual(arr: unknown[], arr2: unknown[]): boolean {\n  if (!Array.isArray(arr) || !Array.isArray(arr2)) {\n    return false;\n  }\n\n  return arr.length === arr2.length && arr.every((elt, idx) => elt === arr2[idx]);\n}\n\n/** @internal */\nexport function errorStrictEqual(lhs?: AnyError | null, rhs?: AnyError | null): boolean {\n  if (lhs === rhs) {\n    return true;\n  }\n\n  if (!lhs || !rhs) {\n    return lhs === rhs;\n  }\n\n  if ((lhs == null && rhs != null) || (lhs != null && rhs == null)) {\n    return false;\n  }\n\n  if (lhs.constructor.name !== rhs.constructor.name) {\n    return false;\n  }\n\n  if (lhs.message !== rhs.message) {\n    return false;\n  }\n\n  return true;\n}\n\ninterface StateTable {\n  [key: string]: string[];\n}\ninterface ObjectWithState {\n  s: { state: string };\n  emit(event: 'stateChanged', state: string, newState: string): void;\n}\ninterface StateTransitionFunction {\n  (target: ObjectWithState, newState: string): void;\n}\n\n/** @public */\nexport type EventEmitterWithState = {\n  /** @internal */\n  stateChanged(previous: string, current: string): void;\n};\n\n/** @internal */\nexport function makeStateMachine(stateTable: StateTable): StateTransitionFunction {\n  return function stateTransition(target, newState) {\n    const legalStates = stateTable[target.s.state];\n    if (legalStates && legalStates.indexOf(newState) < 0) {\n      throw new MongoRuntimeError(\n        `illegal state transition from [${target.s.state}] => [${newState}], allowed: [${legalStates}]`\n      );\n    }\n\n    target.emit('stateChanged', target.s.state, newState);\n    target.s.state = newState;\n  };\n}\n\n/** @internal */\nexport function now(): number {\n  const hrtime = process.hrtime();\n  return Math.floor(hrtime[0] * 1000 + hrtime[1] / 1000000);\n}\n\n/** @internal */\nexport function calculateDurationInMs(started: number | undefined): number {\n  if (typeof started !== 'number') {\n    return -1;\n  }\n\n  const elapsed = now() - started;\n  return elapsed < 0 ? 0 : elapsed;\n}\n\n/** @internal */\nexport function hasAtomicOperators(doc: Document | Document[]): boolean {\n  if (Array.isArray(doc)) {\n    for (const document of doc) {\n      if (hasAtomicOperators(document)) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  const keys = Object.keys(doc);\n  return keys.length > 0 && keys[0][0] === '$';\n}\n\nexport function resolveTimeoutOptions<T extends Partial<TimeoutContextOptions>>(\n  client: MongoClient,\n  options: T\n): T &\n  Pick<\n    MongoClient['s']['options'],\n    'timeoutMS' | 'serverSelectionTimeoutMS' | 'waitQueueTimeoutMS' | 'socketTimeoutMS'\n  > {\n  const { socketTimeoutMS, serverSelectionTimeoutMS, waitQueueTimeoutMS, timeoutMS } =\n    client.s.options;\n  return { socketTimeoutMS, serverSelectionTimeoutMS, waitQueueTimeoutMS, timeoutMS, ...options };\n}\n/**\n * Merge inherited properties from parent into options, prioritizing values from options,\n * then values from parent.\n *\n * @param parent - An optional owning class of the operation being run. ex. Db/Collection/MongoClient.\n * @param options - The options passed to the operation method.\n *\n * @internal\n */\nexport function resolveOptions<T extends CommandOperationOptions>(\n  parent: OperationParent | undefined,\n  options?: T\n): T {\n  const result: T = Object.assign({}, options, resolveBSONOptions(options, parent));\n\n  const timeoutMS = options?.timeoutMS ?? parent?.timeoutMS;\n  // Users cannot pass a readConcern/writeConcern to operations in a transaction\n  const session = options?.session;\n\n  if (!session?.inTransaction()) {\n    const readConcern = ReadConcern.fromOptions(options) ?? parent?.readConcern;\n    if (readConcern) {\n      result.readConcern = readConcern;\n    }\n\n    let writeConcern = WriteConcern.fromOptions(options) ?? parent?.writeConcern;\n    if (writeConcern) {\n      if (timeoutMS != null) {\n        writeConcern = WriteConcern.fromOptions({\n          writeConcern: {\n            ...writeConcern,\n            wtimeout: undefined,\n            wtimeoutMS: undefined\n          }\n        });\n      }\n      result.writeConcern = writeConcern;\n    }\n  }\n\n  result.timeoutMS = timeoutMS;\n\n  const readPreference = ReadPreference.fromOptions(options) ?? parent?.readPreference;\n  if (readPreference) {\n    result.readPreference = readPreference;\n  }\n\n  const isConvenientTransaction = session?.explicit && session?.timeoutContext != null;\n  if (isConvenientTransaction && options?.timeoutMS != null) {\n    throw new MongoInvalidArgumentError(\n      'An operation cannot be given a timeoutMS setting when inside a withTransaction call that has a timeoutMS setting'\n    );\n  }\n\n  return result;\n}\n\nexport function isSuperset(set: Set<any> | any[], subset: Set<any> | any[]): boolean {\n  set = Array.isArray(set) ? new Set(set) : set;\n  subset = Array.isArray(subset) ? new Set(subset) : subset;\n  for (const elem of subset) {\n    if (!set.has(elem)) {\n      return false;\n    }\n  }\n  return true;\n}\n\n/**\n * Checks if the document is a Hello request\n * @internal\n */\nexport function isHello(doc: Document): boolean {\n  return doc[LEGACY_HELLO_COMMAND] || doc.hello ? true : false;\n}\n\n/** Returns the items that are uniquely in setA */\nexport function setDifference<T>(setA: Iterable<T>, setB: Iterable<T>): Set<T> {\n  const difference = new Set<T>(setA);\n  for (const elem of setB) {\n    difference.delete(elem);\n  }\n  return difference;\n}\n\nconst HAS_OWN = (object: unknown, prop: string) =>\n  Object.prototype.hasOwnProperty.call(object, prop);\n\nexport function isRecord<T extends readonly string[]>(\n  value: unknown,\n  requiredKeys: T\n): value is Record<T[number], any>;\nexport function isRecord(value: unknown): value is Record<string, any>;\nexport function isRecord(\n  value: unknown,\n  requiredKeys: string[] | undefined = undefined\n): value is Record<string, any> {\n  if (!isObject(value)) {\n    return false;\n  }\n\n  const ctor = (value as any).constructor;\n  if (ctor && ctor.prototype) {\n    if (!isObject(ctor.prototype)) {\n      return false;\n    }\n\n    // Check to see if some method exists from the Object exists\n    if (!HAS_OWN(ctor.prototype, 'isPrototypeOf')) {\n      return false;\n    }\n  }\n\n  if (requiredKeys) {\n    const keys = Object.keys(value as Record<string, any>);\n    return isSuperset(keys, requiredKeys);\n  }\n\n  return true;\n}\n\ntype ListNode<T> = {\n  value: T;\n  next: ListNode<T> | HeadNode<T>;\n  prev: ListNode<T> | HeadNode<T>;\n};\n\ntype HeadNode<T> = {\n  value: null;\n  next: ListNode<T>;\n  prev: ListNode<T>;\n};\n\n/**\n * When a list is empty the head is a reference with pointers to itself\n * So this type represents that self referential state\n */\ntype EmptyNode = {\n  value: null;\n  next: EmptyNode;\n  prev: EmptyNode;\n};\n\n/**\n * A sequential list of items in a circularly linked list\n * @remarks\n * The head node is special, it is always defined and has a value of null.\n * It is never \"included\" in the list, in that, it is not returned by pop/shift or yielded by the iterator.\n * The circular linkage and always defined head node are to reduce checks for null next/prev references to zero.\n * New nodes are declared as object literals with keys always in the same order: next, prev, value.\n * @internal\n */\nexport class List<T = unknown> {\n  private readonly head: HeadNode<T> | EmptyNode;\n  private count: number;\n\n  get length() {\n    return this.count;\n  }\n\n  get [Symbol.toStringTag]() {\n    return 'List' as const;\n  }\n\n  constructor() {\n    this.count = 0;\n\n    // this is carefully crafted:\n    // declaring a complete and consistently key ordered\n    // object is beneficial to the runtime optimizations\n    this.head = {\n      next: null,\n      prev: null,\n      value: null\n    } as unknown as EmptyNode;\n    this.head.next = this.head;\n    this.head.prev = this.head;\n  }\n\n  toArray() {\n    return Array.from(this);\n  }\n\n  toString() {\n    return `head <=> ${this.toArray().join(' <=> ')} <=> head`;\n  }\n\n  *[Symbol.iterator](): Generator<T, void, void> {\n    for (const node of this.nodes()) {\n      yield node.value;\n    }\n  }\n\n  private *nodes(): Generator<ListNode<T>, void, void> {\n    let ptr: HeadNode<T> | ListNode<T> | EmptyNode = this.head.next;\n    while (ptr !== this.head) {\n      // Save next before yielding so that we make removing within iteration safe\n      const { next } = ptr as ListNode<T>;\n      yield ptr as ListNode<T>;\n      ptr = next;\n    }\n  }\n\n  /** Insert at end of list */\n  push(value: T) {\n    this.count += 1;\n    const newNode: ListNode<T> = {\n      next: this.head as HeadNode<T>,\n      prev: this.head.prev as ListNode<T>,\n      value\n    };\n    this.head.prev.next = newNode;\n    this.head.prev = newNode;\n  }\n\n  /** Inserts every item inside an iterable instead of the iterable itself */\n  pushMany(iterable: Iterable<T>) {\n    for (const value of iterable) {\n      this.push(value);\n    }\n  }\n\n  /** Insert at front of list */\n  unshift(value: T) {\n    this.count += 1;\n    const newNode: ListNode<T> = {\n      next: this.head.next as ListNode<T>,\n      prev: this.head as HeadNode<T>,\n      value\n    };\n    this.head.next.prev = newNode;\n    this.head.next = newNode;\n  }\n\n  private remove(node: ListNode<T> | EmptyNode): T | null {\n    if (node === this.head || this.length === 0) {\n      return null;\n    }\n\n    this.count -= 1;\n\n    const prevNode = node.prev;\n    const nextNode = node.next;\n    prevNode.next = nextNode;\n    nextNode.prev = prevNode;\n\n    return node.value;\n  }\n\n  /** Removes the first node at the front of the list */\n  shift(): T | null {\n    return this.remove(this.head.next);\n  }\n\n  /** Removes the last node at the end of the list */\n  pop(): T | null {\n    return this.remove(this.head.prev);\n  }\n\n  /** Iterates through the list and removes nodes where filter returns true */\n  prune(filter: (value: T) => boolean) {\n    for (const node of this.nodes()) {\n      if (filter(node.value)) {\n        this.remove(node);\n      }\n    }\n  }\n\n  clear() {\n    this.count = 0;\n    this.head.next = this.head as EmptyNode;\n    this.head.prev = this.head as EmptyNode;\n  }\n\n  /** Returns the first item in the list, does not remove */\n  first(): T | null {\n    // If the list is empty, value will be the head's null\n    return this.head.next.value;\n  }\n\n  /** Returns the last item in the list, does not remove */\n  last(): T | null {\n    // If the list is empty, value will be the head's null\n    return this.head.prev.value;\n  }\n}\n\n/**\n * A pool of Buffers which allow you to read them as if they were one\n * @internal\n */\nexport class BufferPool {\n  private buffers: List<Buffer>;\n  private totalByteLength: number;\n\n  constructor() {\n    this.buffers = new List();\n    this.totalByteLength = 0;\n  }\n\n  get length(): number {\n    return this.totalByteLength;\n  }\n\n  /** Adds a buffer to the internal buffer pool list */\n  append(buffer: Buffer): void {\n    this.buffers.push(buffer);\n    this.totalByteLength += buffer.length;\n  }\n\n  /**\n   * If BufferPool contains 4 bytes or more construct an int32 from the leading bytes,\n   * otherwise return null. Size can be negative, caller should error check.\n   */\n  getInt32(): number | null {\n    if (this.totalByteLength < 4) {\n      return null;\n    }\n    const firstBuffer = this.buffers.first();\n    if (firstBuffer != null && firstBuffer.byteLength >= 4) {\n      return firstBuffer.readInt32LE(0);\n    }\n\n    // Unlikely case: an int32 is split across buffers.\n    // Use read and put the returned buffer back on top\n    const top4Bytes = this.read(4);\n    const value = top4Bytes.readInt32LE(0);\n\n    // Put it back.\n    this.totalByteLength += 4;\n    this.buffers.unshift(top4Bytes);\n\n    return value;\n  }\n\n  /** Reads the requested number of bytes, optionally consuming them */\n  read(size: number): Buffer {\n    if (typeof size !== 'number' || size < 0) {\n      throw new MongoInvalidArgumentError('Argument \"size\" must be a non-negative number');\n    }\n\n    // oversized request returns empty buffer\n    if (size > this.totalByteLength) {\n      return Buffer.alloc(0);\n    }\n\n    // We know we have enough, we just don't know how it is spread across chunks\n    // TODO(NODE-4732): alloc API should change based on raw option\n    const result = Buffer.allocUnsafe(size);\n\n    for (let bytesRead = 0; bytesRead < size; ) {\n      const buffer = this.buffers.shift();\n      if (buffer == null) {\n        break;\n      }\n      const bytesRemaining = size - bytesRead;\n      const bytesReadable = Math.min(bytesRemaining, buffer.byteLength);\n      const bytes = buffer.subarray(0, bytesReadable);\n\n      result.set(bytes, bytesRead);\n\n      bytesRead += bytesReadable;\n      this.totalByteLength -= bytesReadable;\n      if (bytesReadable < buffer.byteLength) {\n        this.buffers.unshift(buffer.subarray(bytesReadable));\n      }\n    }\n\n    return result;\n  }\n}\n\n/** @public */\nexport class HostAddress {\n  host: string | undefined = undefined;\n  port: number | undefined = undefined;\n  socketPath: string | undefined = undefined;\n  isIPv6 = false;\n\n  constructor(hostString: string) {\n    const escapedHost = hostString.split(' ').join('%20'); // escape spaces, for socket path hosts\n\n    if (escapedHost.endsWith('.sock')) {\n      // heuristically determine if we're working with a domain socket\n      this.socketPath = decodeURIComponent(escapedHost);\n      return;\n    }\n\n    const urlString = `iLoveJS://${escapedHost}`;\n    let url;\n    try {\n      url = new URL(urlString);\n    } catch (urlError) {\n      const runtimeError = new MongoRuntimeError(`Unable to parse ${escapedHost} with URL`);\n      runtimeError.cause = urlError;\n      throw runtimeError;\n    }\n\n    const hostname = url.hostname;\n    const port = url.port;\n\n    let normalized = decodeURIComponent(hostname).toLowerCase();\n    if (normalized.startsWith('[') && normalized.endsWith(']')) {\n      this.isIPv6 = true;\n      normalized = normalized.substring(1, hostname.length - 1);\n    }\n\n    this.host = normalized.toLowerCase();\n\n    if (typeof port === 'number') {\n      this.port = port;\n    } else if (typeof port === 'string' && port !== '') {\n      this.port = Number.parseInt(port, 10);\n    } else {\n      this.port = 27017;\n    }\n\n    if (this.port === 0) {\n      throw new MongoParseError('Invalid port (zero) with hostname');\n    }\n    Object.freeze(this);\n  }\n\n  [Symbol.for('nodejs.util.inspect.custom')](): string {\n    return this.inspect();\n  }\n\n  inspect(): string {\n    return `new HostAddress('${this.toString()}')`;\n  }\n\n  toString(): string {\n    if (typeof this.host === 'string') {\n      if (this.isIPv6) {\n        return `[${this.host}]:${this.port}`;\n      }\n      return `${this.host}:${this.port}`;\n    }\n    return `${this.socketPath}`;\n  }\n\n  static fromString(this: void, s: string): HostAddress {\n    return new HostAddress(s);\n  }\n\n  static fromHostPort(host: string, port: number): HostAddress {\n    if (host.includes(':')) {\n      host = `[${host}]`; // IPv6 address\n    }\n    return HostAddress.fromString(`${host}:${port}`);\n  }\n\n  static fromSrvRecord({ name, port }: SrvRecord): HostAddress {\n    return HostAddress.fromHostPort(name, port);\n  }\n\n  toHostPort(): { host: string; port: number } {\n    if (this.socketPath) {\n      return { host: this.socketPath, port: 0 };\n    }\n\n    const host = this.host ?? '';\n    const port = this.port ?? 0;\n    return { host, port };\n  }\n}\n\nexport const DEFAULT_PK_FACTORY = {\n  // We prefer not to rely on ObjectId having a createPk method\n  createPk(): ObjectId {\n    return new ObjectId();\n  }\n};\n\n/**\n * When the driver used emitWarning the code will be equal to this.\n * @public\n *\n * @example\n * ```ts\n * process.on('warning', (warning) => {\n *  if (warning.code === MONGODB_WARNING_CODE) console.error('Ah an important warning! :)')\n * })\n * ```\n */\nexport const MONGODB_WARNING_CODE = 'MONGODB DRIVER';\n\n/** @internal */\nexport function emitWarning(message: string): void {\n  return process.emitWarning(message, { code: MONGODB_WARNING_CODE } as any);\n}\n\nconst emittedWarnings = new Set();\n/**\n * Will emit a warning once for the duration of the application.\n * Uses the message to identify if it has already been emitted\n * so using string interpolation can cause multiple emits\n * @internal\n */\nexport function emitWarningOnce(message: string): void {\n  if (!emittedWarnings.has(message)) {\n    emittedWarnings.add(message);\n    return emitWarning(message);\n  }\n}\n\n/**\n * Takes a JS object and joins the values into a string separated by ', '\n */\nexport function enumToString(en: Record<string, unknown>): string {\n  return Object.values(en).join(', ');\n}\n\n/**\n * Determine if a server supports retryable writes.\n *\n * @internal\n */\nexport function supportsRetryableWrites(server?: Server): boolean {\n  if (!server) {\n    return false;\n  }\n\n  if (server.loadBalanced) {\n    // Loadbalanced topologies will always support retry writes\n    return true;\n  }\n\n  if (server.description.logicalSessionTimeoutMinutes != null) {\n    // that supports sessions\n    if (server.description.type !== ServerType.Standalone) {\n      // and that is not a standalone\n      return true;\n    }\n  }\n\n  return false;\n}\n\n/**\n * Fisher–Yates Shuffle\n *\n * Reference: https://bost.ocks.org/mike/shuffle/\n * @param sequence - items to be shuffled\n * @param limit - Defaults to `0`. If nonzero shuffle will slice the randomized array e.g, `.slice(0, limit)` otherwise will return the entire randomized array.\n */\nexport function shuffle<T>(sequence: Iterable<T>, limit = 0): Array<T> {\n  const items = Array.from(sequence); // shallow copy in order to never shuffle the input\n\n  if (limit > items.length) {\n    throw new MongoRuntimeError('Limit must be less than the number of items');\n  }\n\n  let remainingItemsToShuffle = items.length;\n  const lowerBound = limit % items.length === 0 ? 1 : items.length - limit;\n  while (remainingItemsToShuffle > lowerBound) {\n    // Pick a remaining element\n    const randomIndex = Math.floor(Math.random() * remainingItemsToShuffle);\n    remainingItemsToShuffle -= 1;\n\n    // And swap it with the current element\n    const swapHold = items[remainingItemsToShuffle];\n    items[remainingItemsToShuffle] = items[randomIndex];\n    items[randomIndex] = swapHold;\n  }\n\n  return limit % items.length === 0 ? items : items.slice(lowerBound);\n}\n\n/**\n * TODO(NODE-4936): read concern eligibility for commands should be codified in command construction\n * @internal\n * @see https://github.com/mongodb/specifications/blob/master/source/read-write-concern/read-write-concern.md#read-concern\n */\nexport function commandSupportsReadConcern(command: Document): boolean {\n  if (command.aggregate || command.count || command.distinct || command.find || command.geoNear) {\n    return true;\n  }\n\n  return false;\n}\n\n/**\n * Compare objectIds. `null` is always less\n * - `+1 = oid1 is greater than oid2`\n * - `-1 = oid1 is less than oid2`\n * - `+0 = oid1 is equal oid2`\n */\nexport function compareObjectId(oid1?: ObjectId | null, oid2?: ObjectId | null): 0 | 1 | -1 {\n  if (oid1 == null && oid2 == null) {\n    return 0;\n  }\n\n  if (oid1 == null) {\n    return -1;\n  }\n\n  if (oid2 == null) {\n    return 1;\n  }\n\n  return ByteUtils.compare(oid1.id, oid2.id);\n}\n\nexport function parseInteger(value: unknown): number | null {\n  if (typeof value === 'number') return Math.trunc(value);\n  const parsedValue = Number.parseInt(String(value), 10);\n\n  return Number.isNaN(parsedValue) ? null : parsedValue;\n}\n\nexport function parseUnsignedInteger(value: unknown): number | null {\n  const parsedInt = parseInteger(value);\n\n  return parsedInt != null && parsedInt >= 0 ? parsedInt : null;\n}\n\n/**\n * This function throws a MongoAPIError in the event that either of the following is true:\n * * If the provided address domain does not match the provided parent domain\n * * If the parent domain contains less than three `.` separated parts and the provided address does not contain at least one more domain level than its parent\n *\n * If a DNS server were to become compromised SRV records would still need to\n * advertise addresses that are under the same domain as the srvHost.\n *\n * @param address - The address to check against a domain\n * @param srvHost - The domain to check the provided address against\n * @returns void\n */\nexport function checkParentDomainMatch(address: string, srvHost: string): void {\n  // Remove trailing dot if exists on either the resolved address or the srv hostname\n  const normalizedAddress = address.endsWith('.') ? address.slice(0, address.length - 1) : address;\n  const normalizedSrvHost = srvHost.endsWith('.') ? srvHost.slice(0, srvHost.length - 1) : srvHost;\n\n  const allCharacterBeforeFirstDot = /^.*?\\./;\n  const srvIsLessThanThreeParts = normalizedSrvHost.split('.').length < 3;\n  // Remove all characters before first dot\n  // Add leading dot back to string so\n  //   an srvHostDomain = '.trusted.site'\n  //   will not satisfy an addressDomain that endsWith '.fake-trusted.site'\n  const addressDomain = `.${normalizedAddress.replace(allCharacterBeforeFirstDot, '')}`;\n  let srvHostDomain = srvIsLessThanThreeParts\n    ? normalizedSrvHost\n    : `.${normalizedSrvHost.replace(allCharacterBeforeFirstDot, '')}`;\n\n  if (!srvHostDomain.startsWith('.')) {\n    srvHostDomain = '.' + srvHostDomain;\n  }\n  if (\n    srvIsLessThanThreeParts &&\n    normalizedAddress.split('.').length <= normalizedSrvHost.split('.').length\n  ) {\n    throw new MongoAPIError(\n      'Server record does not have at least one more domain level than parent URI'\n    );\n  }\n  if (!addressDomain.endsWith(srvHostDomain)) {\n    throw new MongoAPIError('Server record does not share hostname with parent URI');\n  }\n}\n\ninterface RequestOptions {\n  json?: boolean;\n  method?: string;\n  timeout?: number;\n  headers?: http.OutgoingHttpHeaders;\n}\n\n/**\n * Perform a get request that returns status and body.\n * @internal\n */\nexport function get(\n  url: URL | string,\n  options: http.RequestOptions = {}\n): Promise<{ body: string; status: number | undefined }> {\n  return new Promise((resolve, reject) => {\n    /* eslint-disable prefer-const */\n    let timeoutId: NodeJS.Timeout;\n    const request = http\n      .get(url, options, response => {\n        response.setEncoding('utf8');\n        let body = '';\n        response.on('data', chunk => (body += chunk));\n        response.on('end', () => {\n          clearTimeout(timeoutId);\n          resolve({ status: response.statusCode, body });\n        });\n      })\n      .on('error', error => {\n        clearTimeout(timeoutId);\n        reject(error);\n      })\n      .end();\n    timeoutId = setTimeout(() => {\n      request.destroy(new MongoNetworkTimeoutError(`request timed out after 10 seconds`));\n    }, 10000);\n  });\n}\n\nexport async function request(uri: string): Promise<Record<string, any>>;\nexport async function request(\n  uri: string,\n  options?: { json?: true } & RequestOptions\n): Promise<Record<string, any>>;\nexport async function request(\n  uri: string,\n  options?: { json: false } & RequestOptions\n): Promise<string>;\nexport async function request(\n  uri: string,\n  options: RequestOptions = {}\n): Promise<string | Record<string, any>> {\n  return await new Promise<string | Record<string, any>>((resolve, reject) => {\n    const requestOptions = {\n      method: 'GET',\n      timeout: 10000,\n      json: true,\n      ...url.parse(uri),\n      ...options\n    };\n\n    const req = http.request(requestOptions, res => {\n      res.setEncoding('utf8');\n\n      let data = '';\n      res.on('data', d => {\n        data += d;\n      });\n\n      res.once('end', () => {\n        if (options.json === false) {\n          resolve(data);\n          return;\n        }\n\n        try {\n          const parsed = JSON.parse(data);\n          resolve(parsed);\n        } catch {\n          // TODO(NODE-3483)\n          reject(new MongoRuntimeError(`Invalid JSON response: \"${data}\"`));\n        }\n      });\n    });\n\n    req.once('timeout', () =>\n      req.destroy(\n        new MongoNetworkTimeoutError(\n          `Network request to ${uri} timed out after ${options.timeout} ms`\n        )\n      )\n    );\n    req.once('error', error => reject(error));\n    req.end();\n  });\n}\n\n/** @internal */\nexport const DOCUMENT_DB_CHECK = /(\\.docdb\\.amazonaws\\.com$)|(\\.docdb-elastic\\.amazonaws\\.com$)/;\n/** @internal */\nexport const COSMOS_DB_CHECK = /\\.cosmos\\.azure\\.com$/;\n\n/** @internal */\nexport const DOCUMENT_DB_MSG =\n  'You appear to be connected to a DocumentDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/documentdb';\n/** @internal */\nexport const COSMOS_DB_MSG =\n  'You appear to be connected to a CosmosDB cluster. For more information regarding feature compatibility and support please visit https://www.mongodb.com/supportability/cosmosdb';\n\n/** @internal */\nexport function isHostMatch(match: RegExp, host?: string): boolean {\n  return host && match.test(host.toLowerCase()) ? true : false;\n}\n\nexport function promiseWithResolvers<T>(): {\n  promise: Promise<T>;\n  resolve: (value: T) => void;\n  reject: (error: Error) => void;\n} {\n  let resolve!: (value: T) => void;\n  let reject!: (error: Error) => void;\n  const promise = new Promise<T>(function withResolversExecutor(promiseResolve, promiseReject) {\n    resolve = promiseResolve;\n    reject = promiseReject;\n  });\n  return { promise, resolve, reject } as const;\n}\n\n/**\n * A noop function intended for use in preventing unhandled rejections.\n *\n * @example\n * ```js\n * const promise = myAsyncTask();\n * // eslint-disable-next-line github/no-then\n * promise.then(undefined, squashError);\n * ```\n */\nexport function squashError(_error: unknown) {\n  return;\n}\n\nexport const randomBytes = promisify(crypto.randomBytes);\n\n/**\n * Replicates the events.once helper.\n *\n * Removes unused signal logic and It **only** supports 0 or 1 argument events.\n *\n * @param ee - An event emitter that may emit `ev`\n * @param name - An event name to wait for\n */\nexport async function once<T>(ee: EventEmitter, name: string, options?: Abortable): Promise<T> {\n  options?.signal?.throwIfAborted();\n\n  const { promise, resolve, reject } = promiseWithResolvers<T>();\n  const onEvent = (data: T) => resolve(data);\n  const onError = (error: Error) => reject(error);\n  const abortListener = addAbortListener(options?.signal, function () {\n    reject(this.reason);\n  });\n\n  ee.once(name, onEvent).once('error', onError);\n\n  try {\n    return await promise;\n  } finally {\n    ee.off(name, onEvent);\n    ee.off('error', onError);\n    abortListener?.[kDispose]();\n  }\n}\n\nexport function maybeAddIdToDocuments(\n  coll: Collection,\n  docs: Document[],\n  options: { forceServerObjectId?: boolean }\n): Document[];\nexport function maybeAddIdToDocuments(\n  coll: Collection,\n  docs: Document,\n  options: { forceServerObjectId?: boolean }\n): Document;\nexport function maybeAddIdToDocuments(\n  coll: Collection,\n  docOrDocs: Document[] | Document,\n  options: { forceServerObjectId?: boolean }\n): Document[] | Document {\n  const forceServerObjectId =\n    typeof options.forceServerObjectId === 'boolean'\n      ? options.forceServerObjectId\n      : coll.s.db.options?.forceServerObjectId;\n\n  // no need to modify the docs if server sets the ObjectId\n  if (forceServerObjectId === true) {\n    return docOrDocs;\n  }\n\n  const transform = (doc: Document): Document => {\n    if (doc._id == null) {\n      doc._id = coll.s.pkFactory.createPk();\n    }\n\n    return doc;\n  };\n  return Array.isArray(docOrDocs) ? docOrDocs.map(transform) : transform(docOrDocs);\n}\n\nexport async function fileIsAccessible(fileName: string, mode?: number) {\n  try {\n    await fs.access(fileName, mode);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nexport function csotMin(duration1: number, duration2: number): number {\n  if (duration1 === 0) return duration2;\n  if (duration2 === 0) return duration1;\n  return Math.min(duration1, duration2);\n}\n\nexport function noop() {\n  return;\n}\n\n/**\n * Recurse through the (identically-shaped) `decrypted` and `original`\n * objects and attach a `decryptedKeys` property on each sub-object that\n * contained encrypted fields. Because we only call this on BSON responses,\n * we do not need to worry about circular references.\n *\n * @internal\n */\nexport function decorateDecryptionResult(\n  decrypted: Document & { [kDecoratedKeys]?: Array<string> },\n  original: Document,\n  isTopLevelDecorateCall = true\n): void {\n  if (isTopLevelDecorateCall) {\n    // The original value could have been either a JS object or a BSON buffer\n    if (Buffer.isBuffer(original)) {\n      original = deserialize(original);\n    }\n    if (Buffer.isBuffer(decrypted)) {\n      throw new MongoRuntimeError('Expected result of decryption to be deserialized BSON object');\n    }\n  }\n\n  if (!decrypted || typeof decrypted !== 'object') return;\n  for (const k of Object.keys(decrypted)) {\n    const originalValue = original[k];\n\n    // An object was decrypted by libmongocrypt if and only if it was\n    // a BSON Binary object with subtype 6.\n    if (originalValue && originalValue._bsontype === 'Binary' && originalValue.sub_type === 6) {\n      if (!decrypted[kDecoratedKeys]) {\n        Object.defineProperty(decrypted, kDecoratedKeys, {\n          value: [],\n          configurable: true,\n          enumerable: false,\n          writable: false\n        });\n      }\n      // this is defined in the preceding if-statement\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      decrypted[kDecoratedKeys]!.push(k);\n      // Do not recurse into this decrypted value. It could be a sub-document/array,\n      // in which case there is no original value associated with its subfields.\n      continue;\n    }\n\n    decorateDecryptionResult(decrypted[k], originalValue, false);\n  }\n}\n\n/** @internal */\nexport const kDispose: unique symbol = (Symbol.dispose as any) ?? Symbol('dispose');\n\n/** @internal */\nexport interface Disposable {\n  [kDispose](): void;\n}\n\n/**\n * A utility that helps with writing listener code idiomatically\n *\n * @example\n * ```js\n * using listener = addAbortListener(signal, function () {\n *   console.log('aborted', this.reason);\n * });\n * ```\n *\n * @param signal - if exists adds an abort listener\n * @param listener - the listener to be added to signal\n * @returns A disposable that will remove the abort listener\n */\nexport function addAbortListener(\n  signal: AbortSignal | undefined | null,\n  listener: (this: AbortSignal, event: Event) => void\n): Disposable | undefined {\n  if (signal == null) return;\n  signal.addEventListener('abort', listener, { once: true });\n  return { [kDispose]: () => signal.removeEventListener('abort', listener) };\n}\n\n/**\n * Takes a promise and races it with a promise wrapping the abort event of the optionally provided signal.\n * The given promise is _always_ ordered before the signal's abort promise.\n * When given an already rejected promise and an already aborted signal, the promise's rejection takes precedence.\n *\n * Any asynchronous processing in `promise` will continue even after the abort signal has fired,\n * but control will be returned to the caller\n *\n * @see https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Promise/race\n *\n * @param promise - A promise to discard if the signal aborts\n * @param options - An options object carrying an optional signal\n */\nexport async function abortable<T>(\n  promise: Promise<T>,\n  { signal }: { signal?: AbortSignal }\n): Promise<T> {\n  if (signal == null) {\n    return await promise;\n  }\n\n  const { promise: aborted, reject } = promiseWithResolvers<never>();\n\n  const abortListener = signal.aborted\n    ? reject(signal.reason)\n    : addAbortListener(signal, function () {\n        reject(this.reason);\n      });\n\n  try {\n    return await Promise.race([promise, aborted]);\n  } finally {\n    abortListener?.[kDispose]();\n  }\n}\n"], "mappings": ";;;;;;AAyEAA,OAAA,CAAAC,YAAA,GAAAA,YAAA;AAaAD,OAAA,CAAAE,oBAAA,GAAAA,oBAAA;AAmBAF,OAAA,CAAAG,kBAAA,GAAAA,kBAAA;AA4BAH,OAAA,CAAAI,QAAA,GAAAA,QAAA;AAKAJ,OAAA,CAAAK,YAAA,GAAAA,YAAA;AAKAL,OAAA,CAAAM,aAAA,GAAAA,aAAA;AAuBAN,OAAA,CAAAO,oBAAA,GAAAA,oBAAA;AAyBAP,OAAA,CAAAQ,aAAA,GAAAA,aAAA;AAiBAR,OAAA,CAAAS,qBAAA,GAAAA,qBAAA;AAsBAT,OAAA,CAAAU,uBAAA,GAAAA,uBAAA;AAmCAV,OAAA,CAAAW,WAAA,GAAAA,WAAA;AAYAX,OAAA,CAAAY,EAAA,GAAAA,EAAA;AA4DAZ,OAAA,CAAAa,WAAA,GAAAA,WAAA;AAaAb,OAAA,CAAAc,MAAA,GAAAA,MAAA;AAWAd,OAAA,CAAAe,cAAA,GAAAA,cAAA;AAmCAf,OAAA,CAAAgB,gBAAA,GAAAA,gBAAA;AASAhB,OAAA,CAAAiB,gBAAA,GAAAA,gBAAA;AA0CAjB,OAAA,CAAAkB,gBAAA,GAAAA,gBAAA;AAeAlB,OAAA,CAAAmB,GAAA,GAAAA,GAAA;AAMAnB,OAAA,CAAAoB,qBAAA,GAAAA,qBAAA;AAUApB,OAAA,CAAAqB,kBAAA,GAAAA,kBAAA;AAcArB,OAAA,CAAAsB,qBAAA,GAAAA,qBAAA;AAqBAtB,OAAA,CAAAuB,cAAA,GAAAA,cAAA;AAgDAvB,OAAA,CAAAwB,UAAA,GAAAA,UAAA;AAeAxB,OAAA,CAAAyB,OAAA,GAAAA,OAAA;AAKAzB,OAAA,CAAA0B,aAAA,GAAAA,aAAA;AAgBA1B,OAAA,CAAA2B,QAAA,GAAAA,QAAA;AA2YA3B,OAAA,CAAA4B,WAAA,GAAAA,WAAA;AAWA5B,OAAA,CAAA6B,eAAA,GAAAA,eAAA;AAUA7B,OAAA,CAAA8B,YAAA,GAAAA,YAAA;AASA9B,OAAA,CAAA+B,uBAAA,GAAAA,uBAAA;AA4BA/B,OAAA,CAAAgC,OAAA,GAAAA,OAAA;AA4BAhC,OAAA,CAAAiC,0BAAA,GAAAA,0BAAA;AAcAjC,OAAA,CAAAkC,eAAA,GAAAA,eAAA;AAgBAlC,OAAA,CAAAmC,YAAA,GAAAA,YAAA;AAOAnC,OAAA,CAAAoC,oBAAA,GAAAA,oBAAA;AAkBApC,OAAA,CAAAqC,sBAAA,GAAAA,sBAAA;AA2CArC,OAAA,CAAAsC,GAAA,GAAAA,GAAA;AAqCAtC,OAAA,CAAAuC,OAAA,GAAAA,OAAA;AA8DAvC,OAAA,CAAAwC,WAAA,GAAAA,WAAA;AAIAxC,OAAA,CAAAyC,oBAAA,GAAAA,oBAAA;AAwBAzC,OAAA,CAAA0C,WAAA,GAAAA,WAAA;AAcA1C,OAAA,CAAA2C,IAAA,GAAAA,IAAA;AA+BA3C,OAAA,CAAA4C,qBAAA,GAAAA,qBAAA;AAyBA5C,OAAA,CAAA6C,gBAAA,GAAAA,gBAAA;AASA7C,OAAA,CAAA8C,OAAA,GAAAA,OAAA;AAMA9C,OAAA,CAAA+C,IAAA,GAAAA,IAAA;AAYA/C,OAAA,CAAAgD,wBAAA,GAAAA,wBAAA;AAgEAhD,OAAA,CAAAiD,gBAAA,GAAAA,gBAAA;AAsBAjD,OAAA,CAAAkD,SAAA,GAAAA,SAAA;AA98CA,MAAAC,MAAA,GAAAC,OAAA;AAGA,MAAAC,IAAA,GAAAD,OAAA;AACA,MAAAE,IAAA,GAAAF,OAAA;AACA,MAAAG,QAAA,GAAAH,OAAA;AACA,MAAAI,GAAA,GAAAJ,OAAA;AACA,MAAAK,KAAA,GAAAL,OAAA;AACA,MAAAM,MAAA,GAAAN,OAAA;AAEA,MAAAO,MAAA,GAAAP,OAAA;AAEA,MAAAQ,WAAA,GAAAR,OAAA;AAEA,MAAAS,WAAA,GAAAT,OAAA;AAIA,MAAAU,OAAA,GAAAV,OAAA;AAcA,MAAAW,cAAA,GAAAX,OAAA;AACA,MAAAY,iBAAA,GAAAZ,OAAA;AACA,MAAAa,QAAA,GAAAb,OAAA;AAKA,MAAAc,eAAA,GAAAd,OAAA;AAUapD,OAAA,CAAAmE,SAAS,GAAG;EACvBC,iBAAiBA,CAAaC,MAA2B;IACvD,OAAOC,MAAM,CAACC,QAAQ,CAACF,MAAM,CAAC,GAC1BA,MAAM,GACNC,MAAM,CAACE,IAAI,CAACH,MAAM,CAACA,MAAM,EAAEA,MAAM,CAACI,UAAU,EAAEJ,MAAM,CAACK,UAAU,CAAC;EACtE,CAAC;EAEDC,MAAMA,CAAaC,IAAgB,EAAEC,IAAgB;IACnD,OAAO7E,OAAA,CAAAmE,SAAS,CAACC,iBAAiB,CAACQ,IAAI,CAAC,CAACD,MAAM,CAACE,IAAI,CAAC;EACvD,CAAC;EAEDC,OAAOA,CAAaF,IAAgB,EAAEC,IAAgB;IACpD,OAAO7E,OAAA,CAAAmE,SAAS,CAACC,iBAAiB,CAACQ,IAAI,CAAC,CAACE,OAAO,CAACD,IAAI,CAAC;EACxD,CAAC;EAEDE,QAAQA,CAAaC,UAAsB;IACzC,OAAOhF,OAAA,CAAAmE,SAAS,CAACC,iBAAiB,CAACY,UAAU,CAAC,CAACC,QAAQ,CAAC,QAAQ,CAAC;EACnE;CACD;AAED;;;;AAIA,SAAgBhF,YAAYA,CAACiF,KAAc;EACzC,OACEA,KAAK,IAAI,IAAI,IACb,OAAOA,KAAK,KAAK,QAAQ,IACzBC,MAAM,CAACC,WAAW,IAAIF,KAAK,IAC3BA,KAAK,CAACC,MAAM,CAACC,WAAW,CAAC,KAAK,YAAY;AAE9C;AAEA;;;;AAIA,SAAgBlF,oBAAoBA,CAACmF,IAAY,EAAEC,SAAmB;EACpE,KAAK,MAAMC,QAAQ,IAAID,SAAS,EAAE;IAChC,IACED,IAAI,KAAKE,QAAQ,IAChBA,QAAQ,CAACC,UAAU,CAAC,IAAI,CAAC,IAAIH,IAAI,EAAEI,QAAQ,CAACF,QAAQ,CAACG,SAAS,CAAC,CAAC,EAAEH,QAAQ,CAACI,MAAM,CAAC,CAAE,IACpFJ,QAAQ,CAACC,UAAU,CAAC,IAAI,CAAC,IAAIH,IAAI,EAAEI,QAAQ,CAACF,QAAQ,CAACG,SAAS,CAAC,CAAC,EAAEH,QAAQ,CAACI,MAAM,CAAC,CAAE,EACrF;MACA,OAAO,IAAI;IACb;EACF;EACA,OAAO,KAAK;AACd;AAEA;;;;;;AAMA,SAAgBxF,kBAAkBA,CAACyF,IAAW;EAC5C,IAAIC,SAAS,GAAGC,SAAS;EAEzB,IAAI,OAAOF,IAAI,KAAK,QAAQ,EAAE;IAC5BC,SAAS,GAAGD,IAAI;EAClB,CAAC,MAAM,IAAIG,KAAK,CAACC,OAAO,CAACJ,IAAI,CAAC,EAAE;IAC9BC,SAAS,GAAG,EAAE;IAEdD,IAAI,CAACK,OAAO,CAACC,KAAK,IAAG;MACnBL,SAAS,CAACK,KAAK,CAAC,GAAG,CAAC;IACtB,CAAC,CAAC;EACJ,CAAC,MAAM,IAAIN,IAAI,IAAI,IAAI,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IACnDC,SAAS,GAAG,EAAc;IAC1B,KAAK,MAAMM,IAAI,IAAIP,IAAI,EAAE;MACvBC,SAAS,CAACM,IAAI,CAAC,GAAGP,IAAI,CAACO,IAAI,CAAC;IAC9B;EACF;EAEA,OAAON,SAAS;AAClB;AAEA,MAAMO,SAAS,GAAIC,MAAe,IAAKC,MAAM,CAACC,SAAS,CAACtB,QAAQ,CAACuB,IAAI,CAACH,MAAM,CAAC;AAC7E;;;;;AAMA,SAAgBjG,QAAQA,CAACqG,GAAY;EACnC,OAAO,iBAAiB,KAAKL,SAAS,CAACK,GAAG,CAAC;AAC7C;AAEA;AACA,SAAgBpG,YAAYA,CAAOqG,MAAS,EAAEC,MAAS;EACrD,OAAO;IAAE,GAAGD,MAAM;IAAE,GAAGC;EAAM,CAAE;AACjC;AAEA;AACA,SAAgBrG,aAAaA,CAACsG,OAAmB,EAAEC,KAA4B;EAC7E,MAAMvG,aAAa,GAAe,EAAE;EAEpC,KAAK,MAAM6F,IAAI,IAAIS,OAAO,EAAE;IAC1B,IAAIC,KAAK,CAACC,QAAQ,CAACX,IAAI,CAAC,EAAE;MACxB7F,aAAa,CAAC6F,IAAI,CAAC,GAAGS,OAAO,CAACT,IAAI,CAAC;IACrC;EACF;EAEA;EACA,OAAO7F,aAAa;AACtB;AAKA;;;;;;;AAOA,SAAgBC,oBAAoBA,CAA+BmG,MAAS,EAAEK,EAAO;EACnF,IAAIA,EAAE,IAAIA,EAAE,CAACC,CAAC,CAACJ,OAAO,EAAEK,WAAW,EAAE;IACnCP,MAAM,CAACO,WAAW,GAAG,IAAI;EAC3B;EAEA,OAAOP,MAAM;AACf;AAEA;;;;;;;;;AAUA;;;;;;;AAOA,SAAgBlG,aAAaA,CAAc0E,KAAe;EACxD,OACEA,KAAK,IAAI,IAAI,IACb,OAAOA,KAAK,KAAK,QAAQ,IACzB,MAAM,IAAIA,KAAK,IACf,OAAOA,KAAK,CAACgC,IAAI,KAAK,UAAU;AAEpC;AAEA;;;;;;;;AAQA,SAAgBzG,qBAAqBA,CACnC0G,OAAiB,EACjBT,MAAqC,EACrCE,OAAmB;EAEnB,MAAMQ,YAAY,GAAGzG,WAAW,CAAC+F,MAAM,CAAC,CAACU,YAAY;EACrD,IAAIR,OAAO,CAACS,SAAS,IAAI,OAAOT,OAAO,CAACS,SAAS,KAAK,QAAQ,EAAE;IAC9D,IAAID,YAAY,IAAIA,YAAY,CAACE,qBAAqB,EAAE;MACtDH,OAAO,CAACE,SAAS,GAAGT,OAAO,CAACS,SAAS;IACvC,CAAC,MAAM;MACL,MAAM,IAAIvD,OAAA,CAAAyD,uBAAuB,CAAC,6CAA6C,CAAC;IAClF;EACF;AACF;AAEA;;;;;;;AAOA,SAAgB7G,uBAAuBA,CACrCyG,OAAiB,EACjBK,IAA0C,EAC1CZ,OAA0B;EAE1B,IAAIA,OAAO,IAAIA,OAAO,CAACa,OAAO,IAAIb,OAAO,CAACa,OAAO,CAACC,aAAa,EAAE,EAAE;IACjE;EACF;EACA,MAAMC,WAAW,GAAGrB,MAAM,CAACsB,MAAM,CAAC,EAAE,EAAET,OAAO,CAACQ,WAAW,IAAI,EAAE,CAAC;EAChE,IAAIH,IAAI,CAACR,CAAC,CAACW,WAAW,EAAE;IACtBrB,MAAM,CAACsB,MAAM,CAACD,WAAW,EAAEH,IAAI,CAACR,CAAC,CAACW,WAAW,CAAC;EAChD;EAEA,IAAIrB,MAAM,CAACuB,IAAI,CAACF,WAAW,CAAC,CAAChC,MAAM,GAAG,CAAC,EAAE;IACvCW,MAAM,CAACsB,MAAM,CAACT,OAAO,EAAE;MAAEQ,WAAW,EAAEA;IAAW,CAAE,CAAC;EACtD;AACF;AAaA;;;;;;AAMA,SAAgBhH,WAAWA,CAACmH,QAA0B;EACpD;EACA,IAAI,UAAU,IAAIA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ,EAAE;IAC/C,OAAOD,QAAQ,CAACC,QAAQ;EAC1B,CAAC,MAAM,IAAI,QAAQ,IAAID,QAAQ,IAAIA,QAAQ,CAACE,MAAM,CAACD,QAAQ,EAAE;IAC3D,OAAOD,QAAQ,CAACE,MAAM,CAACD,QAAQ;EACjC;EAEA,MAAM,IAAIjE,OAAA,CAAAmE,sBAAsB,CAAC,yDAAyD,CAAC;AAC7F;AAEA;AACA,SAAgBrH,EAAEA,CAACA,EAAU;EAC3B,OAAOsH,gBAAgB,CAACC,UAAU,CAACvH,EAAE,CAAC;AACxC;AAEA;AACA,MAAasH,gBAAgB;EAC3B;;;;;;EAMAE,YACSrB,EAAU,EACVsB,UAAmB;IADnB,KAAAtB,EAAE,GAAFA,EAAE;IACF,KAAAsB,UAAU,GAAVA,UAAU;IAEjB,IAAI,CAACA,UAAU,GAAGA,UAAU,KAAK,EAAE,GAAGvC,SAAS,GAAGuC,UAAU;EAC9D;EAEApD,QAAQA,CAAA;IACN,OAAO,IAAI,CAACoD,UAAU,GAAG,GAAG,IAAI,CAACtB,EAAE,IAAI,IAAI,CAACsB,UAAU,EAAE,GAAG,IAAI,CAACtB,EAAE;EACpE;EAEAuB,cAAcA,CAACD,UAAkB;IAC/B,OAAO,IAAIE,0BAA0B,CAAC,IAAI,CAACxB,EAAE,EAAEsB,UAAU,CAAC;EAC5D;EAEA,OAAOF,UAAUA,CAACK,SAAkB;IAClC,IAAI,OAAOA,SAAS,KAAK,QAAQ,IAAIA,SAAS,KAAK,EAAE,EAAE;MACrD;MACA,MAAM,IAAI1E,OAAA,CAAA2E,iBAAiB,CAAC,gCAAgCD,SAAS,GAAG,CAAC;IAC3E;IAEA,MAAM,CAACzB,EAAE,EAAE,GAAG2B,eAAe,CAAC,GAAGF,SAAS,CAACG,KAAK,CAAC,GAAG,CAAC;IACrD,MAAMN,UAAU,GAAGK,eAAe,CAACE,IAAI,CAAC,GAAG,CAAC;IAC5C,OAAO,IAAIV,gBAAgB,CAACnB,EAAE,EAAEsB,UAAU,KAAK,EAAE,GAAGvC,SAAS,GAAGuC,UAAU,CAAC;EAC7E;;AA/BFrI,OAAA,CAAAkI,gBAAA,GAAAA,gBAAA;AAkCA;;;;;;;AAOA,MAAaK,0BAA2B,SAAQL,gBAAgB;EAC9DE,YACErB,EAAU,EACDsB,UAAkB;IAE3B,KAAK,CAACtB,EAAE,EAAEsB,UAAU,CAAC;IAFZ,KAAAA,UAAU,GAAVA,UAAU;EAGrB;EAEA,OAAgBF,UAAUA,CAACK,SAAkB;IAC3C,OAAO,KAAK,CAACL,UAAU,CAACK,SAAS,CAA+B;EAClE;;AAVFxI,OAAA,CAAAuI,0BAAA,GAAAA,0BAAA;AAaA;AACA,UAAiB1H,WAAWA,CAACgI,IAAI,GAAG,CAAC;EACnC,IAAIC,KAAK,GAAGD,IAAI;EAChB,OAAO,IAAI,EAAE;IACX,MAAME,QAAQ,GAAGD,KAAK;IACtBA,KAAK,IAAI,CAAC;IACV,MAAMC,QAAQ;EAChB;AACF;AAEA;;;;AAIA,SAAgBjI,MAAMA,CAAA;EACpB,MAAMkI,MAAM,GAAG7F,MAAM,CAAC8F,WAAW,CAAC,EAAE,CAAC;EACrCD,MAAM,CAAC,CAAC,CAAC,GAAIA,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,IAAI;EACrCA,MAAM,CAAC,CAAC,CAAC,GAAIA,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,GAAI,IAAI;EACrC,OAAOA,MAAM;AACf;AAEA;;;;AAIA,SAAgBjI,cAAcA,CAACmI,gBAAiD;EAC9E,IAAIA,gBAAgB,EAAE;IACpB,IAAIA,gBAAgB,CAACC,YAAY,IAAID,gBAAgB,CAACE,SAAS,EAAEC,OAAO,EAAE;MACxE;MACA;MACA;MACA;MACA;MACA;MACA,OAAOzF,WAAA,CAAA0F,0BAA0B;IACnC;IACA,IAAIJ,gBAAgB,CAACK,KAAK,EAAE;MAC1B,OAAOL,gBAAgB,CAACK,KAAK,CAACxI,cAAc;IAC9C;IAEA,IAAI,WAAW,IAAImI,gBAAgB,IAAI,OAAOA,gBAAgB,CAACM,SAAS,KAAK,UAAU,EAAE;MACvF,MAAMA,SAAS,GAAGN,gBAAgB,CAACM,SAAS,EAAE;MAC9C,IAAIA,SAAS,EAAE;QACb,OAAOA,SAAS,CAACzI,cAAc;MACjC;IACF;IAEA,IACEmI,gBAAgB,CAACO,WAAW,IAC5B,gBAAgB,IAAIP,gBAAgB,CAACO,WAAW,IAChDP,gBAAgB,CAACO,WAAW,CAAC1I,cAAc,IAAI,IAAI,EACnD;MACA,OAAOmI,gBAAgB,CAACO,WAAW,CAAC1I,cAAc;IACpD;EACF;EAEA,OAAO,CAAC;AACV;AAEA;AACA,SAAgBC,gBAAgBA,CAAC0I,GAAc,EAAEC,IAAe;EAC9D,IAAI,CAAC5D,KAAK,CAACC,OAAO,CAAC0D,GAAG,CAAC,IAAI,CAAC3D,KAAK,CAACC,OAAO,CAAC2D,IAAI,CAAC,EAAE;IAC/C,OAAO,KAAK;EACd;EAEA,OAAOD,GAAG,CAAC/D,MAAM,KAAKgE,IAAI,CAAChE,MAAM,IAAI+D,GAAG,CAACE,KAAK,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAKD,GAAG,KAAKF,IAAI,CAACG,GAAG,CAAC,CAAC;AACjF;AAEA;AACA,SAAgB7I,gBAAgBA,CAAC8I,GAAqB,EAAEC,GAAqB;EAC3E,IAAID,GAAG,KAAKC,GAAG,EAAE;IACf,OAAO,IAAI;EACb;EAEA,IAAI,CAACD,GAAG,IAAI,CAACC,GAAG,EAAE;IAChB,OAAOD,GAAG,KAAKC,GAAG;EACpB;EAEA,IAAKD,GAAG,IAAI,IAAI,IAAIC,GAAG,IAAI,IAAI,IAAMD,GAAG,IAAI,IAAI,IAAIC,GAAG,IAAI,IAAK,EAAE;IAChE,OAAO,KAAK;EACd;EAEA,IAAID,GAAG,CAAC3B,WAAW,CAACjC,IAAI,KAAK6D,GAAG,CAAC5B,WAAW,CAACjC,IAAI,EAAE;IACjD,OAAO,KAAK;EACd;EAEA,IAAI4D,GAAG,CAACE,OAAO,KAAKD,GAAG,CAACC,OAAO,EAAE;IAC/B,OAAO,KAAK;EACd;EAEA,OAAO,IAAI;AACb;AAmBA;AACA,SAAgB/I,gBAAgBA,CAACgJ,UAAsB;EACrD,OAAO,SAASC,eAAeA,CAACzD,MAAM,EAAE0D,QAAQ;IAC9C,MAAMC,WAAW,GAAGH,UAAU,CAACxD,MAAM,CAACM,CAAC,CAACsD,KAAK,CAAC;IAC9C,IAAID,WAAW,IAAIA,WAAW,CAACE,OAAO,CAACH,QAAQ,CAAC,GAAG,CAAC,EAAE;MACpD,MAAM,IAAItG,OAAA,CAAA2E,iBAAiB,CACzB,kCAAkC/B,MAAM,CAACM,CAAC,CAACsD,KAAK,SAASF,QAAQ,gBAAgBC,WAAW,GAAG,CAChG;IACH;IAEA3D,MAAM,CAAC8D,IAAI,CAAC,cAAc,EAAE9D,MAAM,CAACM,CAAC,CAACsD,KAAK,EAAEF,QAAQ,CAAC;IACrD1D,MAAM,CAACM,CAAC,CAACsD,KAAK,GAAGF,QAAQ;EAC3B,CAAC;AACH;AAEA;AACA,SAAgBjJ,GAAGA,CAAA;EACjB,MAAMsJ,MAAM,GAAGC,OAAO,CAACD,MAAM,EAAE;EAC/B,OAAOE,IAAI,CAACC,KAAK,CAACH,MAAM,CAAC,CAAC,CAAC,GAAG,IAAI,GAAGA,MAAM,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC;AAC3D;AAEA;AACA,SAAgBrJ,qBAAqBA,CAACyJ,OAA2B;EAC/D,IAAI,OAAOA,OAAO,KAAK,QAAQ,EAAE;IAC/B,OAAO,CAAC,CAAC;EACX;EAEA,MAAMC,OAAO,GAAG3J,GAAG,EAAE,GAAG0J,OAAO;EAC/B,OAAOC,OAAO,GAAG,CAAC,GAAG,CAAC,GAAGA,OAAO;AAClC;AAEA;AACA,SAAgBzJ,kBAAkBA,CAAC0J,GAA0B;EAC3D,IAAIhF,KAAK,CAACC,OAAO,CAAC+E,GAAG,CAAC,EAAE;IACtB,KAAK,MAAMC,QAAQ,IAAID,GAAG,EAAE;MAC1B,IAAI1J,kBAAkB,CAAC2J,QAAQ,CAAC,EAAE;QAChC,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd;EAEA,MAAMnD,IAAI,GAAGvB,MAAM,CAACuB,IAAI,CAACkD,GAAG,CAAC;EAC7B,OAAOlD,IAAI,CAAClC,MAAM,GAAG,CAAC,IAAIkC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG;AAC9C;AAEA,SAAgBvG,qBAAqBA,CACnC0G,MAAmB,EACnBpB,OAAU;EAMV,MAAM;IAAEqE,eAAe;IAAEC,wBAAwB;IAAEC,kBAAkB;IAAEC;EAAS,CAAE,GAChFpD,MAAM,CAAChB,CAAC,CAACJ,OAAO;EAClB,OAAO;IAAEqE,eAAe;IAAEC,wBAAwB;IAAEC,kBAAkB;IAAEC,SAAS;IAAE,GAAGxE;EAAO,CAAE;AACjG;AACA;;;;;;;;;AASA,SAAgBrF,cAAcA,CAC5B8J,MAAmC,EACnCzE,OAAW;EAEX,MAAMoC,MAAM,GAAM1C,MAAM,CAACsB,MAAM,CAAC,EAAE,EAAEhB,OAAO,EAAE,IAAAjD,MAAA,CAAA2H,kBAAkB,EAAC1E,OAAO,EAAEyE,MAAM,CAAC,CAAC;EAEjF,MAAMD,SAAS,GAAGxE,OAAO,EAAEwE,SAAS,IAAIC,MAAM,EAAED,SAAS;EACzD;EACA,MAAM3D,OAAO,GAAGb,OAAO,EAAEa,OAAO;EAEhC,IAAI,CAACA,OAAO,EAAEC,aAAa,EAAE,EAAE;IAC7B,MAAMC,WAAW,GAAG5D,cAAA,CAAAwH,WAAW,CAACC,WAAW,CAAC5E,OAAO,CAAC,IAAIyE,MAAM,EAAE1D,WAAW;IAC3E,IAAIA,WAAW,EAAE;MACfqB,MAAM,CAACrB,WAAW,GAAGA,WAAW;IAClC;IAEA,IAAI8D,YAAY,GAAGvH,eAAA,CAAAwH,YAAY,CAACF,WAAW,CAAC5E,OAAO,CAAC,IAAIyE,MAAM,EAAEI,YAAY;IAC5E,IAAIA,YAAY,EAAE;MAChB,IAAIL,SAAS,IAAI,IAAI,EAAE;QACrBK,YAAY,GAAGvH,eAAA,CAAAwH,YAAY,CAACF,WAAW,CAAC;UACtCC,YAAY,EAAE;YACZ,GAAGA,YAAY;YACfE,QAAQ,EAAE7F,SAAS;YACnB8F,UAAU,EAAE9F;;SAEf,CAAC;MACJ;MACAkD,MAAM,CAACyC,YAAY,GAAGA,YAAY;IACpC;EACF;EAEAzC,MAAM,CAACoC,SAAS,GAAGA,SAAS;EAE5B,MAAMS,cAAc,GAAG7H,iBAAA,CAAA8H,cAAc,CAACN,WAAW,CAAC5E,OAAO,CAAC,IAAIyE,MAAM,EAAEQ,cAAc;EACpF,IAAIA,cAAc,EAAE;IAClB7C,MAAM,CAAC6C,cAAc,GAAGA,cAAc;EACxC;EAEA,MAAME,uBAAuB,GAAGtE,OAAO,EAAEuE,QAAQ,IAAIvE,OAAO,EAAEwE,cAAc,IAAI,IAAI;EACpF,IAAIF,uBAAuB,IAAInF,OAAO,EAAEwE,SAAS,IAAI,IAAI,EAAE;IACzD,MAAM,IAAItH,OAAA,CAAAoI,yBAAyB,CACjC,kHAAkH,CACnH;EACH;EAEA,OAAOlD,MAAM;AACf;AAEA,SAAgBxH,UAAUA,CAAC2K,GAAqB,EAAEC,MAAwB;EACxED,GAAG,GAAGpG,KAAK,CAACC,OAAO,CAACmG,GAAG,CAAC,GAAG,IAAIE,GAAG,CAACF,GAAG,CAAC,GAAGA,GAAG;EAC7CC,MAAM,GAAGrG,KAAK,CAACC,OAAO,CAACoG,MAAM,CAAC,GAAG,IAAIC,GAAG,CAACD,MAAM,CAAC,GAAGA,MAAM;EACzD,KAAK,MAAME,IAAI,IAAIF,MAAM,EAAE;IACzB,IAAI,CAACD,GAAG,CAACI,GAAG,CAACD,IAAI,CAAC,EAAE;MAClB,OAAO,KAAK;IACd;EACF;EACA,OAAO,IAAI;AACb;AAEA;;;;AAIA,SAAgB7K,OAAOA,CAACsJ,GAAa;EACnC,OAAOA,GAAG,CAAClH,WAAA,CAAA2I,oBAAoB,CAAC,IAAIzB,GAAG,CAACxB,KAAK,GAAG,IAAI,GAAG,KAAK;AAC9D;AAEA;AACA,SAAgB7H,aAAaA,CAAI+K,IAAiB,EAAEC,IAAiB;EACnE,MAAMC,UAAU,GAAG,IAAIN,GAAG,CAAII,IAAI,CAAC;EACnC,KAAK,MAAMH,IAAI,IAAII,IAAI,EAAE;IACvBC,UAAU,CAACC,MAAM,CAACN,IAAI,CAAC;EACzB;EACA,OAAOK,UAAU;AACnB;AAEA,MAAME,OAAO,GAAGA,CAACxG,MAAe,EAAEyG,IAAY,KAC5CxG,MAAM,CAACC,SAAS,CAACwG,cAAc,CAACvG,IAAI,CAACH,MAAM,EAAEyG,IAAI,CAAC;AAOpD,SAAgBnL,QAAQA,CACtBuD,KAAc,EACd8H,YAAA,GAAqClH,SAAS;EAE9C,IAAI,CAAC1F,QAAQ,CAAC8E,KAAK,CAAC,EAAE;IACpB,OAAO,KAAK;EACd;EAEA,MAAM+H,IAAI,GAAI/H,KAAa,CAACkD,WAAW;EACvC,IAAI6E,IAAI,IAAIA,IAAI,CAAC1G,SAAS,EAAE;IAC1B,IAAI,CAACnG,QAAQ,CAAC6M,IAAI,CAAC1G,SAAS,CAAC,EAAE;MAC7B,OAAO,KAAK;IACd;IAEA;IACA,IAAI,CAACsG,OAAO,CAACI,IAAI,CAAC1G,SAAS,EAAE,eAAe,CAAC,EAAE;MAC7C,OAAO,KAAK;IACd;EACF;EAEA,IAAIyG,YAAY,EAAE;IAChB,MAAMnF,IAAI,GAAGvB,MAAM,CAACuB,IAAI,CAAC3C,KAA4B,CAAC;IACtD,OAAO1D,UAAU,CAACqG,IAAI,EAAEmF,YAAY,CAAC;EACvC;EAEA,OAAO,IAAI;AACb;AAwBA;;;;;;;;;AASA,MAAaE,IAAI;EAIf,IAAIvH,MAAMA,CAAA;IACR,OAAO,IAAI,CAACmD,KAAK;EACnB;EAEA,KAAK3D,MAAM,CAACC,WAAW,IAAC;IACtB,OAAO,MAAe;EACxB;EAEAgD,YAAA;IACE,IAAI,CAACU,KAAK,GAAG,CAAC;IAEd;IACA;IACA;IACA,IAAI,CAACqE,IAAI,GAAG;MACVC,IAAI,EAAE,IAAI;MACVC,IAAI,EAAE,IAAI;MACVnI,KAAK,EAAE;KACgB;IACzB,IAAI,CAACiI,IAAI,CAACC,IAAI,GAAG,IAAI,CAACD,IAAI;IAC1B,IAAI,CAACA,IAAI,CAACE,IAAI,GAAG,IAAI,CAACF,IAAI;EAC5B;EAEAG,OAAOA,CAAA;IACL,OAAOvH,KAAK,CAACvB,IAAI,CAAC,IAAI,CAAC;EACzB;EAEAS,QAAQA,CAAA;IACN,OAAO,YAAY,IAAI,CAACqI,OAAO,EAAE,CAAC1E,IAAI,CAAC,OAAO,CAAC,WAAW;EAC5D;EAEA,EAAEzD,MAAM,CAACoI,QAAQ,IAAC;IAChB,KAAK,MAAMC,IAAI,IAAI,IAAI,CAACC,KAAK,EAAE,EAAE;MAC/B,MAAMD,IAAI,CAACtI,KAAK;IAClB;EACF;EAEQ,CAACuI,KAAKA,CAAA;IACZ,IAAIC,GAAG,GAA0C,IAAI,CAACP,IAAI,CAACC,IAAI;IAC/D,OAAOM,GAAG,KAAK,IAAI,CAACP,IAAI,EAAE;MACxB;MACA,MAAM;QAAEC;MAAI,CAAE,GAAGM,GAAkB;MACnC,MAAMA,GAAkB;MACxBA,GAAG,GAAGN,IAAI;IACZ;EACF;EAEA;EACAO,IAAIA,CAACzI,KAAQ;IACX,IAAI,CAAC4D,KAAK,IAAI,CAAC;IACf,MAAM8E,OAAO,GAAgB;MAC3BR,IAAI,EAAE,IAAI,CAACD,IAAmB;MAC9BE,IAAI,EAAE,IAAI,CAACF,IAAI,CAACE,IAAmB;MACnCnI;KACD;IACD,IAAI,CAACiI,IAAI,CAACE,IAAI,CAACD,IAAI,GAAGQ,OAAO;IAC7B,IAAI,CAACT,IAAI,CAACE,IAAI,GAAGO,OAAO;EAC1B;EAEA;EACAC,QAAQA,CAACC,QAAqB;IAC5B,KAAK,MAAM5I,KAAK,IAAI4I,QAAQ,EAAE;MAC5B,IAAI,CAACH,IAAI,CAACzI,KAAK,CAAC;IAClB;EACF;EAEA;EACA6I,OAAOA,CAAC7I,KAAQ;IACd,IAAI,CAAC4D,KAAK,IAAI,CAAC;IACf,MAAM8E,OAAO,GAAgB;MAC3BR,IAAI,EAAE,IAAI,CAACD,IAAI,CAACC,IAAmB;MACnCC,IAAI,EAAE,IAAI,CAACF,IAAmB;MAC9BjI;KACD;IACD,IAAI,CAACiI,IAAI,CAACC,IAAI,CAACC,IAAI,GAAGO,OAAO;IAC7B,IAAI,CAACT,IAAI,CAACC,IAAI,GAAGQ,OAAO;EAC1B;EAEQI,MAAMA,CAACR,IAA6B;IAC1C,IAAIA,IAAI,KAAK,IAAI,CAACL,IAAI,IAAI,IAAI,CAACxH,MAAM,KAAK,CAAC,EAAE;MAC3C,OAAO,IAAI;IACb;IAEA,IAAI,CAACmD,KAAK,IAAI,CAAC;IAEf,MAAMmF,QAAQ,GAAGT,IAAI,CAACH,IAAI;IAC1B,MAAMa,QAAQ,GAAGV,IAAI,CAACJ,IAAI;IAC1Ba,QAAQ,CAACb,IAAI,GAAGc,QAAQ;IACxBA,QAAQ,CAACb,IAAI,GAAGY,QAAQ;IAExB,OAAOT,IAAI,CAACtI,KAAK;EACnB;EAEA;EACAiJ,KAAKA,CAAA;IACH,OAAO,IAAI,CAACH,MAAM,CAAC,IAAI,CAACb,IAAI,CAACC,IAAI,CAAC;EACpC;EAEA;EACAgB,GAAGA,CAAA;IACD,OAAO,IAAI,CAACJ,MAAM,CAAC,IAAI,CAACb,IAAI,CAACE,IAAI,CAAC;EACpC;EAEA;EACAgB,KAAKA,CAACC,MAA6B;IACjC,KAAK,MAAMd,IAAI,IAAI,IAAI,CAACC,KAAK,EAAE,EAAE;MAC/B,IAAIa,MAAM,CAACd,IAAI,CAACtI,KAAK,CAAC,EAAE;QACtB,IAAI,CAAC8I,MAAM,CAACR,IAAI,CAAC;MACnB;IACF;EACF;EAEAe,KAAKA,CAAA;IACH,IAAI,CAACzF,KAAK,GAAG,CAAC;IACd,IAAI,CAACqE,IAAI,CAACC,IAAI,GAAG,IAAI,CAACD,IAAiB;IACvC,IAAI,CAACA,IAAI,CAACE,IAAI,GAAG,IAAI,CAACF,IAAiB;EACzC;EAEA;EACAqB,KAAKA,CAAA;IACH;IACA,OAAO,IAAI,CAACrB,IAAI,CAACC,IAAI,CAAClI,KAAK;EAC7B;EAEA;EACAuJ,IAAIA,CAAA;IACF;IACA,OAAO,IAAI,CAACtB,IAAI,CAACE,IAAI,CAACnI,KAAK;EAC7B;;AApIFlF,OAAA,CAAAkN,IAAA,GAAAA,IAAA;AAuIA;;;;AAIA,MAAawB,UAAU;EAIrBtG,YAAA;IACE,IAAI,CAACuG,OAAO,GAAG,IAAIzB,IAAI,EAAE;IACzB,IAAI,CAAC0B,eAAe,GAAG,CAAC;EAC1B;EAEA,IAAIjJ,MAAMA,CAAA;IACR,OAAO,IAAI,CAACiJ,eAAe;EAC7B;EAEA;EACAC,MAAMA,CAACxK,MAAc;IACnB,IAAI,CAACsK,OAAO,CAAChB,IAAI,CAACtJ,MAAM,CAAC;IACzB,IAAI,CAACuK,eAAe,IAAIvK,MAAM,CAACsB,MAAM;EACvC;EAEA;;;;EAIAmJ,QAAQA,CAAA;IACN,IAAI,IAAI,CAACF,eAAe,GAAG,CAAC,EAAE;MAC5B,OAAO,IAAI;IACb;IACA,MAAMG,WAAW,GAAG,IAAI,CAACJ,OAAO,CAACH,KAAK,EAAE;IACxC,IAAIO,WAAW,IAAI,IAAI,IAAIA,WAAW,CAACrK,UAAU,IAAI,CAAC,EAAE;MACtD,OAAOqK,WAAW,CAACC,WAAW,CAAC,CAAC,CAAC;IACnC;IAEA;IACA;IACA,MAAMC,SAAS,GAAG,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;IAC9B,MAAMhK,KAAK,GAAG+J,SAAS,CAACD,WAAW,CAAC,CAAC,CAAC;IAEtC;IACA,IAAI,CAACJ,eAAe,IAAI,CAAC;IACzB,IAAI,CAACD,OAAO,CAACZ,OAAO,CAACkB,SAAS,CAAC;IAE/B,OAAO/J,KAAK;EACd;EAEA;EACAgK,IAAIA,CAACC,IAAY;IACf,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,GAAG,CAAC,EAAE;MACxC,MAAM,IAAIrL,OAAA,CAAAoI,yBAAyB,CAAC,+CAA+C,CAAC;IACtF;IAEA;IACA,IAAIiD,IAAI,GAAG,IAAI,CAACP,eAAe,EAAE;MAC/B,OAAOtK,MAAM,CAAC8K,KAAK,CAAC,CAAC,CAAC;IACxB;IAEA;IACA;IACA,MAAMpG,MAAM,GAAG1E,MAAM,CAAC+K,WAAW,CAACF,IAAI,CAAC;IAEvC,KAAK,IAAIG,SAAS,GAAG,CAAC,EAAEA,SAAS,GAAGH,IAAI,GAAI;MAC1C,MAAM9K,MAAM,GAAG,IAAI,CAACsK,OAAO,CAACR,KAAK,EAAE;MACnC,IAAI9J,MAAM,IAAI,IAAI,EAAE;QAClB;MACF;MACA,MAAMkL,cAAc,GAAGJ,IAAI,GAAGG,SAAS;MACvC,MAAME,aAAa,GAAG7E,IAAI,CAAC8E,GAAG,CAACF,cAAc,EAAElL,MAAM,CAACK,UAAU,CAAC;MACjE,MAAMgL,KAAK,GAAGrL,MAAM,CAACsL,QAAQ,CAAC,CAAC,EAAEH,aAAa,CAAC;MAE/CxG,MAAM,CAACmD,GAAG,CAACuD,KAAK,EAAEJ,SAAS,CAAC;MAE5BA,SAAS,IAAIE,aAAa;MAC1B,IAAI,CAACZ,eAAe,IAAIY,aAAa;MACrC,IAAIA,aAAa,GAAGnL,MAAM,CAACK,UAAU,EAAE;QACrC,IAAI,CAACiK,OAAO,CAACZ,OAAO,CAAC1J,MAAM,CAACsL,QAAQ,CAACH,aAAa,CAAC,CAAC;MACtD;IACF;IAEA,OAAOxG,MAAM;EACf;;AA9EFhJ,OAAA,CAAA0O,UAAA,GAAAA,UAAA;AAiFA;AACA,MAAakB,WAAW;EAMtBxH,YAAYyH,UAAkB;IAL9B,KAAAxK,IAAI,GAAuBS,SAAS;IACpC,KAAAgK,IAAI,GAAuBhK,SAAS;IACpC,KAAAiK,UAAU,GAAuBjK,SAAS;IAC1C,KAAAkK,MAAM,GAAG,KAAK;IAGZ,MAAMC,WAAW,GAAGJ,UAAU,CAAClH,KAAK,CAAC,GAAG,CAAC,CAACC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;IAEvD,IAAIqH,WAAW,CAACxK,QAAQ,CAAC,OAAO,CAAC,EAAE;MACjC;MACA,IAAI,CAACsK,UAAU,GAAGG,kBAAkB,CAACD,WAAW,CAAC;MACjD;IACF;IAEA,MAAME,SAAS,GAAG,aAAaF,WAAW,EAAE;IAC5C,IAAIzM,GAAG;IACP,IAAI;MACFA,GAAG,GAAG,IAAIC,KAAA,CAAA2M,GAAG,CAACD,SAAS,CAAC;IAC1B,CAAC,CAAC,OAAOE,QAAQ,EAAE;MACjB,MAAMC,YAAY,GAAG,IAAIxM,OAAA,CAAA2E,iBAAiB,CAAC,mBAAmBwH,WAAW,WAAW,CAAC;MACrFK,YAAY,CAACC,KAAK,GAAGF,QAAQ;MAC7B,MAAMC,YAAY;IACpB;IAEA,MAAME,QAAQ,GAAGhN,GAAG,CAACgN,QAAQ;IAC7B,MAAMV,IAAI,GAAGtM,GAAG,CAACsM,IAAI;IAErB,IAAIW,UAAU,GAAGP,kBAAkB,CAACM,QAAQ,CAAC,CAACE,WAAW,EAAE;IAC3D,IAAID,UAAU,CAACjL,UAAU,CAAC,GAAG,CAAC,IAAIiL,UAAU,CAAChL,QAAQ,CAAC,GAAG,CAAC,EAAE;MAC1D,IAAI,CAACuK,MAAM,GAAG,IAAI;MAClBS,UAAU,GAAGA,UAAU,CAAC/K,SAAS,CAAC,CAAC,EAAE8K,QAAQ,CAAC7K,MAAM,GAAG,CAAC,CAAC;IAC3D;IAEA,IAAI,CAACN,IAAI,GAAGoL,UAAU,CAACC,WAAW,EAAE;IAEpC,IAAI,OAAOZ,IAAI,KAAK,QAAQ,EAAE;MAC5B,IAAI,CAACA,IAAI,GAAGA,IAAI;IAClB,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,KAAK,EAAE,EAAE;MAClD,IAAI,CAACA,IAAI,GAAGa,MAAM,CAACC,QAAQ,CAACd,IAAI,EAAE,EAAE,CAAC;IACvC,CAAC,MAAM;MACL,IAAI,CAACA,IAAI,GAAG,KAAK;IACnB;IAEA,IAAI,IAAI,CAACA,IAAI,KAAK,CAAC,EAAE;MACnB,MAAM,IAAIhM,OAAA,CAAA+M,eAAe,CAAC,mCAAmC,CAAC;IAChE;IACAvK,MAAM,CAACwK,MAAM,CAAC,IAAI,CAAC;EACrB;EAEA,CAAC3L,MAAM,CAAC4L,GAAG,CAAC,4BAA4B,CAAC,IAAC;IACxC,OAAO,IAAI,CAACC,OAAO,EAAE;EACvB;EAEAA,OAAOA,CAAA;IACL,OAAO,oBAAoB,IAAI,CAAC/L,QAAQ,EAAE,IAAI;EAChD;EAEAA,QAAQA,CAAA;IACN,IAAI,OAAO,IAAI,CAACI,IAAI,KAAK,QAAQ,EAAE;MACjC,IAAI,IAAI,CAAC2K,MAAM,EAAE;QACf,OAAO,IAAI,IAAI,CAAC3K,IAAI,KAAK,IAAI,CAACyK,IAAI,EAAE;MACtC;MACA,OAAO,GAAG,IAAI,CAACzK,IAAI,IAAI,IAAI,CAACyK,IAAI,EAAE;IACpC;IACA,OAAO,GAAG,IAAI,CAACC,UAAU,EAAE;EAC7B;EAEA,OAAO5H,UAAUA,CAAanB,CAAS;IACrC,OAAO,IAAI4I,WAAW,CAAC5I,CAAC,CAAC;EAC3B;EAEA,OAAOiK,YAAYA,CAAC5L,IAAY,EAAEyK,IAAY;IAC5C,IAAIzK,IAAI,CAACyB,QAAQ,CAAC,GAAG,CAAC,EAAE;MACtBzB,IAAI,GAAG,IAAIA,IAAI,GAAG,CAAC,CAAC;IACtB;IACA,OAAOuK,WAAW,CAACzH,UAAU,CAAC,GAAG9C,IAAI,IAAIyK,IAAI,EAAE,CAAC;EAClD;EAEA,OAAOoB,aAAaA,CAAC;IAAE/K,IAAI;IAAE2J;EAAI,CAAa;IAC5C,OAAOF,WAAW,CAACqB,YAAY,CAAC9K,IAAI,EAAE2J,IAAI,CAAC;EAC7C;EAEAqB,UAAUA,CAAA;IACR,IAAI,IAAI,CAACpB,UAAU,EAAE;MACnB,OAAO;QAAE1K,IAAI,EAAE,IAAI,CAAC0K,UAAU;QAAED,IAAI,EAAE;MAAC,CAAE;IAC3C;IAEA,MAAMzK,IAAI,GAAG,IAAI,CAACA,IAAI,IAAI,EAAE;IAC5B,MAAMyK,IAAI,GAAG,IAAI,CAACA,IAAI,IAAI,CAAC;IAC3B,OAAO;MAAEzK,IAAI;MAAEyK;IAAI,CAAE;EACvB;;AA3FF9P,OAAA,CAAA4P,WAAA,GAAAA,WAAA;AA8Fa5P,OAAA,CAAAoR,kBAAkB,GAAG;EAChC;EACAC,QAAQA,CAAA;IACN,OAAO,IAAI1N,MAAA,CAAA2N,QAAQ,EAAE;EACvB;CACD;AAED;;;;;;;;;;;AAWatR,OAAA,CAAAuR,oBAAoB,GAAG,gBAAgB;AAEpD;AACA,SAAgB3P,WAAWA,CAACqI,OAAe;EACzC,OAAOS,OAAO,CAAC9I,WAAW,CAACqI,OAAO,EAAE;IAAEuH,IAAI,EAAExR,OAAA,CAAAuR;EAAoB,CAAS,CAAC;AAC5E;AAEA,MAAME,eAAe,GAAG,IAAIpF,GAAG,EAAE;AACjC;;;;;;AAMA,SAAgBxK,eAAeA,CAACoI,OAAe;EAC7C,IAAI,CAACwH,eAAe,CAAClF,GAAG,CAACtC,OAAO,CAAC,EAAE;IACjCwH,eAAe,CAACC,GAAG,CAACzH,OAAO,CAAC;IAC5B,OAAOrI,WAAW,CAACqI,OAAO,CAAC;EAC7B;AACF;AAEA;;;AAGA,SAAgBnI,YAAYA,CAAC6P,EAA2B;EACtD,OAAOrL,MAAM,CAACsL,MAAM,CAACD,EAAE,CAAC,CAAC/I,IAAI,CAAC,IAAI,CAAC;AACrC;AAEA;;;;;AAKA,SAAgB7G,uBAAuBA,CAAC8P,MAAe;EACrD,IAAI,CAACA,MAAM,EAAE;IACX,OAAO,KAAK;EACd;EAEA,IAAIA,MAAM,CAAC1I,YAAY,EAAE;IACvB;IACA,OAAO,IAAI;EACb;EAEA,IAAI0I,MAAM,CAACpI,WAAW,CAACqI,4BAA4B,IAAI,IAAI,EAAE;IAC3D;IACA,IAAID,MAAM,CAACpI,WAAW,CAACsI,IAAI,KAAK9N,QAAA,CAAA+N,UAAU,CAACC,UAAU,EAAE;MACrD;MACA,OAAO,IAAI;IACb;EACF;EAEA,OAAO,KAAK;AACd;AAEA;;;;;;;AAOA,SAAgBjQ,OAAOA,CAAIkQ,QAAqB,EAAEC,KAAK,GAAG,CAAC;EACzD,MAAMC,KAAK,GAAGrM,KAAK,CAACvB,IAAI,CAAC0N,QAAQ,CAAC,CAAC,CAAC;EAEpC,IAAIC,KAAK,GAAGC,KAAK,CAACzM,MAAM,EAAE;IACxB,MAAM,IAAI7B,OAAA,CAAA2E,iBAAiB,CAAC,6CAA6C,CAAC;EAC5E;EAEA,IAAI4J,uBAAuB,GAAGD,KAAK,CAACzM,MAAM;EAC1C,MAAM2M,UAAU,GAAGH,KAAK,GAAGC,KAAK,CAACzM,MAAM,KAAK,CAAC,GAAG,CAAC,GAAGyM,KAAK,CAACzM,MAAM,GAAGwM,KAAK;EACxE,OAAOE,uBAAuB,GAAGC,UAAU,EAAE;IAC3C;IACA,MAAMC,WAAW,GAAG5H,IAAI,CAACC,KAAK,CAACD,IAAI,CAAC6H,MAAM,EAAE,GAAGH,uBAAuB,CAAC;IACvEA,uBAAuB,IAAI,CAAC;IAE5B;IACA,MAAMI,QAAQ,GAAGL,KAAK,CAACC,uBAAuB,CAAC;IAC/CD,KAAK,CAACC,uBAAuB,CAAC,GAAGD,KAAK,CAACG,WAAW,CAAC;IACnDH,KAAK,CAACG,WAAW,CAAC,GAAGE,QAAQ;EAC/B;EAEA,OAAON,KAAK,GAAGC,KAAK,CAACzM,MAAM,KAAK,CAAC,GAAGyM,KAAK,GAAGA,KAAK,CAACM,KAAK,CAACJ,UAAU,CAAC;AACrE;AAEA;;;;;AAKA,SAAgBrQ,0BAA0BA,CAACkF,OAAiB;EAC1D,IAAIA,OAAO,CAACwL,SAAS,IAAIxL,OAAO,CAAC2B,KAAK,IAAI3B,OAAO,CAACyL,QAAQ,IAAIzL,OAAO,CAAC0L,IAAI,IAAI1L,OAAO,CAAC2L,OAAO,EAAE;IAC7F,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd;AAEA;;;;;;AAMA,SAAgB5Q,eAAeA,CAAC6Q,IAAsB,EAAEC,IAAsB;EAC5E,IAAID,IAAI,IAAI,IAAI,IAAIC,IAAI,IAAI,IAAI,EAAE;IAChC,OAAO,CAAC;EACV;EAEA,IAAID,IAAI,IAAI,IAAI,EAAE;IAChB,OAAO,CAAC,CAAC;EACX;EAEA,IAAIC,IAAI,IAAI,IAAI,EAAE;IAChB,OAAO,CAAC;EACV;EAEA,OAAOhT,OAAA,CAAAmE,SAAS,CAACW,OAAO,CAACiO,IAAI,CAACE,EAAE,EAAED,IAAI,CAACC,EAAE,CAAC;AAC5C;AAEA,SAAgB9Q,YAAYA,CAAC+C,KAAc;EACzC,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE,OAAOyF,IAAI,CAACuI,KAAK,CAAChO,KAAK,CAAC;EACvD,MAAMiO,WAAW,GAAGxC,MAAM,CAACC,QAAQ,CAACwC,MAAM,CAAClO,KAAK,CAAC,EAAE,EAAE,CAAC;EAEtD,OAAOyL,MAAM,CAAC0C,KAAK,CAACF,WAAW,CAAC,GAAG,IAAI,GAAGA,WAAW;AACvD;AAEA,SAAgB/Q,oBAAoBA,CAAC8C,KAAc;EACjD,MAAMoO,SAAS,GAAGnR,YAAY,CAAC+C,KAAK,CAAC;EAErC,OAAOoO,SAAS,IAAI,IAAI,IAAIA,SAAS,IAAI,CAAC,GAAGA,SAAS,GAAG,IAAI;AAC/D;AAEA;;;;;;;;;;;;AAYA,SAAgBjR,sBAAsBA,CAACkR,OAAe,EAAEC,OAAe;EACrE;EACA,MAAMC,iBAAiB,GAAGF,OAAO,CAAC9N,QAAQ,CAAC,GAAG,CAAC,GAAG8N,OAAO,CAACb,KAAK,CAAC,CAAC,EAAEa,OAAO,CAAC5N,MAAM,GAAG,CAAC,CAAC,GAAG4N,OAAO;EAChG,MAAMG,iBAAiB,GAAGF,OAAO,CAAC/N,QAAQ,CAAC,GAAG,CAAC,GAAG+N,OAAO,CAACd,KAAK,CAAC,CAAC,EAAEc,OAAO,CAAC7N,MAAM,GAAG,CAAC,CAAC,GAAG6N,OAAO;EAEhG,MAAMG,0BAA0B,GAAG,QAAQ;EAC3C,MAAMC,uBAAuB,GAAGF,iBAAiB,CAAC/K,KAAK,CAAC,GAAG,CAAC,CAAChD,MAAM,GAAG,CAAC;EACvE;EACA;EACA;EACA;EACA,MAAMkO,aAAa,GAAG,IAAIJ,iBAAiB,CAACK,OAAO,CAACH,0BAA0B,EAAE,EAAE,CAAC,EAAE;EACrF,IAAII,aAAa,GAAGH,uBAAuB,GACvCF,iBAAiB,GACjB,IAAIA,iBAAiB,CAACI,OAAO,CAACH,0BAA0B,EAAE,EAAE,CAAC,EAAE;EAEnE,IAAI,CAACI,aAAa,CAACvO,UAAU,CAAC,GAAG,CAAC,EAAE;IAClCuO,aAAa,GAAG,GAAG,GAAGA,aAAa;EACrC;EACA,IACEH,uBAAuB,IACvBH,iBAAiB,CAAC9K,KAAK,CAAC,GAAG,CAAC,CAAChD,MAAM,IAAI+N,iBAAiB,CAAC/K,KAAK,CAAC,GAAG,CAAC,CAAChD,MAAM,EAC1E;IACA,MAAM,IAAI7B,OAAA,CAAAkQ,aAAa,CACrB,4EAA4E,CAC7E;EACH;EACA,IAAI,CAACH,aAAa,CAACpO,QAAQ,CAACsO,aAAa,CAAC,EAAE;IAC1C,MAAM,IAAIjQ,OAAA,CAAAkQ,aAAa,CAAC,uDAAuD,CAAC;EAClF;AACF;AASA;;;;AAIA,SAAgB1R,GAAGA,CACjBkB,GAAiB,EACjBoD,OAAA,GAA+B,EAAE;EAEjC,OAAO,IAAIqN,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;IACrC;IACA,IAAIC,SAAyB;IAC7B,MAAM7R,OAAO,GAAGe,IAAI,CACjBhB,GAAG,CAACkB,GAAG,EAAEoD,OAAO,EAAEyN,QAAQ,IAAG;MAC5BA,QAAQ,CAACC,WAAW,CAAC,MAAM,CAAC;MAC5B,IAAIC,IAAI,GAAG,EAAE;MACbF,QAAQ,CAACG,EAAE,CAAC,MAAM,EAAEC,KAAK,IAAKF,IAAI,IAAIE,KAAM,CAAC;MAC7CJ,QAAQ,CAACG,EAAE,CAAC,KAAK,EAAE,MAAK;QACtB,IAAAjR,QAAA,CAAAmR,YAAY,EAACN,SAAS,CAAC;QACvBF,OAAO,CAAC;UAAES,MAAM,EAAEN,QAAQ,CAACO,UAAU;UAAEL;QAAI,CAAE,CAAC;MAChD,CAAC,CAAC;IACJ,CAAC,CAAC,CACDC,EAAE,CAAC,OAAO,EAAEK,KAAK,IAAG;MACnB,IAAAtR,QAAA,CAAAmR,YAAY,EAACN,SAAS,CAAC;MACvBD,MAAM,CAACU,KAAK,CAAC;IACf,CAAC,CAAC,CACDC,GAAG,EAAE;IACRV,SAAS,GAAG,IAAA7Q,QAAA,CAAAwR,UAAU,EAAC,MAAK;MAC1BxS,OAAO,CAACyS,OAAO,CAAC,IAAIlR,OAAA,CAAAmR,wBAAwB,CAAC,oCAAoC,CAAC,CAAC;IACrF,CAAC,EAAE,KAAK,CAAC;EACX,CAAC,CAAC;AACJ;AAWO,eAAe1S,OAAOA,CAC3B2S,GAAW,EACXtO,OAAA,GAA0B,EAAE;EAE5B,OAAO,MAAM,IAAIqN,OAAO,CAA+B,CAACC,OAAO,EAAEC,MAAM,KAAI;IACzE,MAAMgB,cAAc,GAAG;MACrBC,MAAM,EAAE,KAAK;MACbC,OAAO,EAAE,KAAK;MACdC,IAAI,EAAE,IAAI;MACV,GAAG9R,GAAG,CAAC+R,KAAK,CAACL,GAAG,CAAC;MACjB,GAAGtO;KACJ;IAED,MAAM4O,GAAG,GAAGlS,IAAI,CAACf,OAAO,CAAC4S,cAAc,EAAEM,GAAG,IAAG;MAC7CA,GAAG,CAACnB,WAAW,CAAC,MAAM,CAAC;MAEvB,IAAIoB,IAAI,GAAG,EAAE;MACbD,GAAG,CAACjB,EAAE,CAAC,MAAM,EAAEmB,CAAC,IAAG;QACjBD,IAAI,IAAIC,CAAC;MACX,CAAC,CAAC;MAEFF,GAAG,CAAC9S,IAAI,CAAC,KAAK,EAAE,MAAK;QACnB,IAAIiE,OAAO,CAAC0O,IAAI,KAAK,KAAK,EAAE;UAC1BpB,OAAO,CAACwB,IAAI,CAAC;UACb;QACF;QAEA,IAAI;UACF,MAAME,MAAM,GAAGC,IAAI,CAACN,KAAK,CAACG,IAAI,CAAC;UAC/BxB,OAAO,CAAC0B,MAAM,CAAC;QACjB,CAAC,CAAC,MAAM;UACN;UACAzB,MAAM,CAAC,IAAIrQ,OAAA,CAAA2E,iBAAiB,CAAC,2BAA2BiN,IAAI,GAAG,CAAC,CAAC;QACnE;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;IAEFF,GAAG,CAAC7S,IAAI,CAAC,SAAS,EAAE,MAClB6S,GAAG,CAACR,OAAO,CACT,IAAIlR,OAAA,CAAAmR,wBAAwB,CAC1B,sBAAsBC,GAAG,oBAAoBtO,OAAO,CAACyO,OAAO,KAAK,CAClE,CACF,CACF;IACDG,GAAG,CAAC7S,IAAI,CAAC,OAAO,EAAEkS,KAAK,IAAIV,MAAM,CAACU,KAAK,CAAC,CAAC;IACzCW,GAAG,CAACV,GAAG,EAAE;EACX,CAAC,CAAC;AACJ;AAEA;AACa9U,OAAA,CAAA8V,iBAAiB,GAAG,+DAA+D;AAChG;AACa9V,OAAA,CAAA+V,eAAe,GAAG,uBAAuB;AAEtD;AACa/V,OAAA,CAAAgW,eAAe,GAC1B,qLAAqL;AACvL;AACahW,OAAA,CAAAiW,aAAa,GACxB,iLAAiL;AAEnL;AACA,SAAgBzT,WAAWA,CAAC0T,KAAa,EAAE7Q,IAAa;EACtD,OAAOA,IAAI,IAAI6Q,KAAK,CAACC,IAAI,CAAC9Q,IAAI,CAACqL,WAAW,EAAE,CAAC,GAAG,IAAI,GAAG,KAAK;AAC9D;AAEA,SAAgBjO,oBAAoBA,CAAA;EAKlC,IAAIyR,OAA4B;EAChC,IAAIC,MAA+B;EACnC,MAAMiC,OAAO,GAAG,IAAInC,OAAO,CAAI,SAASoC,qBAAqBA,CAACC,cAAc,EAAEC,aAAa;IACzFrC,OAAO,GAAGoC,cAAc;IACxBnC,MAAM,GAAGoC,aAAa;EACxB,CAAC,CAAC;EACF,OAAO;IAAEH,OAAO;IAAElC,OAAO;IAAEC;EAAM,CAAW;AAC9C;AAEA;;;;;;;;;;AAUA,SAAgBzR,WAAWA,CAAC8T,MAAe;EACzC;AACF;AAEaxW,OAAA,CAAAiJ,WAAW,GAAG,IAAAvF,MAAA,CAAA+S,SAAS,EAACtT,MAAM,CAAC8F,WAAW,CAAC;AAExD;;;;;;;;AAQO,eAAetG,IAAIA,CAAI+T,EAAgB,EAAEvQ,IAAY,EAAES,OAAmB;EAC/EA,OAAO,EAAE+P,MAAM,EAAEC,cAAc,EAAE;EAEjC,MAAM;IAAER,OAAO;IAAElC,OAAO;IAAEC;EAAM,CAAE,GAAG1R,oBAAoB,EAAK;EAC9D,MAAMoU,OAAO,GAAInB,IAAO,IAAKxB,OAAO,CAACwB,IAAI,CAAC;EAC1C,MAAMoB,OAAO,GAAIjC,KAAY,IAAKV,MAAM,CAACU,KAAK,CAAC;EAC/C,MAAMkC,aAAa,GAAG9T,gBAAgB,CAAC2D,OAAO,EAAE+P,MAAM,EAAE;IACtDxC,MAAM,CAAC,IAAI,CAAC6C,MAAM,CAAC;EACrB,CAAC,CAAC;EAEFN,EAAE,CAAC/T,IAAI,CAACwD,IAAI,EAAE0Q,OAAO,CAAC,CAAClU,IAAI,CAAC,OAAO,EAAEmU,OAAO,CAAC;EAE7C,IAAI;IACF,OAAO,MAAMV,OAAO;EACtB,CAAC,SAAS;IACRM,EAAE,CAACO,GAAG,CAAC9Q,IAAI,EAAE0Q,OAAO,CAAC;IACrBH,EAAE,CAACO,GAAG,CAAC,OAAO,EAAEH,OAAO,CAAC;IACxBC,aAAa,GAAG/W,OAAA,CAAAkX,QAAQ,CAAC,EAAE;EAC7B;AACF;AAYA,SAAgBtU,qBAAqBA,CACnC4E,IAAgB,EAChB2P,SAAgC,EAChCvQ,OAA0C;EAE1C,MAAMwQ,mBAAmB,GACvB,OAAOxQ,OAAO,CAACwQ,mBAAmB,KAAK,SAAS,GAC5CxQ,OAAO,CAACwQ,mBAAmB,GAC3B5P,IAAI,CAACR,CAAC,CAACD,EAAE,CAACH,OAAO,EAAEwQ,mBAAmB;EAE5C;EACA,IAAIA,mBAAmB,KAAK,IAAI,EAAE;IAChC,OAAOD,SAAS;EAClB;EAEA,MAAME,SAAS,GAAItM,GAAa,IAAc;IAC5C,IAAIA,GAAG,CAACuM,GAAG,IAAI,IAAI,EAAE;MACnBvM,GAAG,CAACuM,GAAG,GAAG9P,IAAI,CAACR,CAAC,CAACuQ,SAAS,CAAClG,QAAQ,EAAE;IACvC;IAEA,OAAOtG,GAAG;EACZ,CAAC;EACD,OAAOhF,KAAK,CAACC,OAAO,CAACmR,SAAS,CAAC,GAAGA,SAAS,CAACK,GAAG,CAACH,SAAS,CAAC,GAAGA,SAAS,CAACF,SAAS,CAAC;AACnF;AAEO,eAAetU,gBAAgBA,CAAC4U,QAAgB,EAAEC,IAAa;EACpE,IAAI;IACF,MAAMrU,IAAA,CAAAsU,QAAE,CAACC,MAAM,CAACH,QAAQ,EAAEC,IAAI,CAAC;IAC/B,OAAO,IAAI;EACb,CAAC,CAAC,MAAM;IACN,OAAO,KAAK;EACd;AACF;AAEA,SAAgB5U,OAAOA,CAAC+U,SAAiB,EAAEC,SAAiB;EAC1D,IAAID,SAAS,KAAK,CAAC,EAAE,OAAOC,SAAS;EACrC,IAAIA,SAAS,KAAK,CAAC,EAAE,OAAOD,SAAS;EACrC,OAAOlN,IAAI,CAAC8E,GAAG,CAACoI,SAAS,EAAEC,SAAS,CAAC;AACvC;AAEA,SAAgB/U,IAAIA,CAAA;EAClB;AACF;AAEA;;;;;;;;AAQA,SAAgBC,wBAAwBA,CACtC+U,SAA0D,EAC1DC,QAAkB,EAClBC,sBAAsB,GAAG,IAAI;EAE7B,IAAIA,sBAAsB,EAAE;IAC1B;IACA,IAAI3T,MAAM,CAACC,QAAQ,CAACyT,QAAQ,CAAC,EAAE;MAC7BA,QAAQ,GAAG,IAAArU,MAAA,CAAAuU,WAAW,EAACF,QAAQ,CAAC;IAClC;IACA,IAAI1T,MAAM,CAACC,QAAQ,CAACwT,SAAS,CAAC,EAAE;MAC9B,MAAM,IAAIjU,OAAA,CAAA2E,iBAAiB,CAAC,8DAA8D,CAAC;IAC7F;EACF;EAEA,IAAI,CAACsP,SAAS,IAAI,OAAOA,SAAS,KAAK,QAAQ,EAAE;EACjD,KAAK,MAAMI,CAAC,IAAI7R,MAAM,CAACuB,IAAI,CAACkQ,SAAS,CAAC,EAAE;IACtC,MAAMK,aAAa,GAAGJ,QAAQ,CAACG,CAAC,CAAC;IAEjC;IACA;IACA,IAAIC,aAAa,IAAIA,aAAa,CAACC,SAAS,KAAK,QAAQ,IAAID,aAAa,CAACE,QAAQ,KAAK,CAAC,EAAE;MACzF,IAAI,CAACP,SAAS,CAAClU,WAAA,CAAA0U,cAAc,CAAC,EAAE;QAC9BjS,MAAM,CAACkS,cAAc,CAACT,SAAS,EAAElU,WAAA,CAAA0U,cAAc,EAAE;UAC/CrT,KAAK,EAAE,EAAE;UACTuT,YAAY,EAAE,IAAI;UAClBC,UAAU,EAAE,KAAK;UACjBC,QAAQ,EAAE;SACX,CAAC;MACJ;MACA;MACA;MACAZ,SAAS,CAAClU,WAAA,CAAA0U,cAAc,CAAE,CAAC5K,IAAI,CAACwK,CAAC,CAAC;MAClC;MACA;MACA;IACF;IAEAnV,wBAAwB,CAAC+U,SAAS,CAACI,CAAC,CAAC,EAAEC,aAAa,EAAE,KAAK,CAAC;EAC9D;AACF;AAEA;AACapY,OAAA,CAAAkX,QAAQ,GAAmB/R,MAAM,CAACyT,OAAe,IAAIzT,MAAM,CAAC,SAAS,CAAC;AAOnF;;;;;;;;;;;;;;AAcA,SAAgBlC,gBAAgBA,CAC9B0T,MAAsC,EACtCkC,QAAmD;EAEnD,IAAIlC,MAAM,IAAI,IAAI,EAAE;EACpBA,MAAM,CAACmC,gBAAgB,CAAC,OAAO,EAAED,QAAQ,EAAE;IAAElW,IAAI,EAAE;EAAI,CAAE,CAAC;EAC1D,OAAO;IAAE,CAAC3C,OAAA,CAAAkX,QAAQ,GAAG,MAAMP,MAAM,CAACoC,mBAAmB,CAAC,OAAO,EAAEF,QAAQ;EAAC,CAAE;AAC5E;AAEA;;;;;;;;;;;;;AAaO,eAAe3V,SAASA,CAC7BkT,OAAmB,EACnB;EAAEO;AAAM,CAA4B;EAEpC,IAAIA,MAAM,IAAI,IAAI,EAAE;IAClB,OAAO,MAAMP,OAAO;EACtB;EAEA,MAAM;IAAEA,OAAO,EAAE4C,OAAO;IAAE7E;EAAM,CAAE,GAAG1R,oBAAoB,EAAS;EAElE,MAAMsU,aAAa,GAAGJ,MAAM,CAACqC,OAAO,GAChC7E,MAAM,CAACwC,MAAM,CAACK,MAAM,CAAC,GACrB/T,gBAAgB,CAAC0T,MAAM,EAAE;IACvBxC,MAAM,CAAC,IAAI,CAAC6C,MAAM,CAAC;EACrB,CAAC,CAAC;EAEN,IAAI;IACF,OAAO,MAAM/C,OAAO,CAACgF,IAAI,CAAC,CAAC7C,OAAO,EAAE4C,OAAO,CAAC,CAAC;EAC/C,CAAC,SAAS;IACRjC,aAAa,GAAG/W,OAAA,CAAAkX,QAAQ,CAAC,EAAE;EAC7B;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}