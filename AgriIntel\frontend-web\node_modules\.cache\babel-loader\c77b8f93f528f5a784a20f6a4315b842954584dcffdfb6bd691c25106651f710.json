{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.FindOperation = void 0;\nconst responses_1 = require(\"../cmap/wire_protocol/responses\");\nconst error_1 = require(\"../error\");\nconst explain_1 = require(\"../explain\");\nconst read_concern_1 = require(\"../read_concern\");\nconst sort_1 = require(\"../sort\");\nconst utils_1 = require(\"../utils\");\nconst command_1 = require(\"./command\");\nconst operation_1 = require(\"./operation\");\n/** @internal */\nclass FindOperation extends command_1.CommandOperation {\n  constructor(ns, filter = {}, options = {}) {\n    super(undefined, options);\n    this.options = {\n      ...options\n    };\n    delete this.options.writeConcern;\n    this.ns = ns;\n    if (typeof filter !== 'object' || Array.isArray(filter)) {\n      throw new error_1.MongoInvalidArgumentError('Query filter must be a plain object or ObjectId');\n    }\n    // special case passing in an ObjectId as a filter\n    this.filter = filter != null && filter._bsontype === 'ObjectId' ? {\n      _id: filter\n    } : filter;\n  }\n  get commandName() {\n    return 'find';\n  }\n  async execute(server, session, timeoutContext) {\n    this.server = server;\n    const options = this.options;\n    let findCommand = makeFindCommand(this.ns, this.filter, options);\n    if (this.explain) {\n      (0, explain_1.validateExplainTimeoutOptions)(this.options, this.explain);\n      findCommand = (0, explain_1.decorateWithExplain)(findCommand, this.explain);\n    }\n    return await server.command(this.ns, findCommand, {\n      ...this.options,\n      ...this.bsonOptions,\n      documentsReturnedIn: 'firstBatch',\n      session,\n      timeoutContext\n    }, this.explain ? responses_1.ExplainedCursorResponse : responses_1.CursorResponse);\n  }\n}\nexports.FindOperation = FindOperation;\nfunction makeFindCommand(ns, filter, options) {\n  const findCommand = {\n    find: ns.collection,\n    filter\n  };\n  if (options.sort) {\n    findCommand.sort = (0, sort_1.formatSort)(options.sort);\n  }\n  if (options.projection) {\n    let projection = options.projection;\n    if (projection && Array.isArray(projection)) {\n      projection = projection.length ? projection.reduce((result, field) => {\n        result[field] = 1;\n        return result;\n      }, {}) : {\n        _id: 1\n      };\n    }\n    findCommand.projection = projection;\n  }\n  if (options.hint) {\n    findCommand.hint = (0, utils_1.normalizeHintField)(options.hint);\n  }\n  if (typeof options.skip === 'number') {\n    findCommand.skip = options.skip;\n  }\n  if (typeof options.limit === 'number') {\n    if (options.limit < 0) {\n      findCommand.limit = -options.limit;\n      findCommand.singleBatch = true;\n    } else {\n      findCommand.limit = options.limit;\n    }\n  }\n  if (typeof options.batchSize === 'number') {\n    if (options.batchSize < 0) {\n      if (options.limit && options.limit !== 0 && Math.abs(options.batchSize) < Math.abs(options.limit)) {\n        findCommand.limit = -options.batchSize;\n      }\n      findCommand.singleBatch = true;\n    } else {\n      findCommand.batchSize = options.batchSize;\n    }\n  }\n  if (typeof options.singleBatch === 'boolean') {\n    findCommand.singleBatch = options.singleBatch;\n  }\n  // we check for undefined specifically here to allow falsy values\n  // eslint-disable-next-line no-restricted-syntax\n  if (options.comment !== undefined) {\n    findCommand.comment = options.comment;\n  }\n  if (typeof options.maxTimeMS === 'number') {\n    findCommand.maxTimeMS = options.maxTimeMS;\n  }\n  const readConcern = read_concern_1.ReadConcern.fromOptions(options);\n  if (readConcern) {\n    findCommand.readConcern = readConcern.toJSON();\n  }\n  if (options.max) {\n    findCommand.max = options.max;\n  }\n  if (options.min) {\n    findCommand.min = options.min;\n  }\n  if (typeof options.returnKey === 'boolean') {\n    findCommand.returnKey = options.returnKey;\n  }\n  if (typeof options.showRecordId === 'boolean') {\n    findCommand.showRecordId = options.showRecordId;\n  }\n  if (typeof options.tailable === 'boolean') {\n    findCommand.tailable = options.tailable;\n  }\n  if (typeof options.oplogReplay === 'boolean') {\n    findCommand.oplogReplay = options.oplogReplay;\n  }\n  if (typeof options.timeout === 'boolean') {\n    findCommand.noCursorTimeout = !options.timeout;\n  } else if (typeof options.noCursorTimeout === 'boolean') {\n    findCommand.noCursorTimeout = options.noCursorTimeout;\n  }\n  if (typeof options.awaitData === 'boolean') {\n    findCommand.awaitData = options.awaitData;\n  }\n  if (typeof options.allowPartialResults === 'boolean') {\n    findCommand.allowPartialResults = options.allowPartialResults;\n  }\n  if (options.collation) {\n    findCommand.collation = options.collation;\n  }\n  if (typeof options.allowDiskUse === 'boolean') {\n    findCommand.allowDiskUse = options.allowDiskUse;\n  }\n  if (options.let) {\n    findCommand.let = options.let;\n  }\n  return findCommand;\n}\n(0, operation_1.defineAspects)(FindOperation, [operation_1.Aspect.READ_OPERATION, operation_1.Aspect.RETRYABLE, operation_1.Aspect.EXPLAINABLE, operation_1.Aspect.CURSOR_CREATING]);", "map": {"version": 3, "names": ["responses_1", "require", "error_1", "explain_1", "read_concern_1", "sort_1", "utils_1", "command_1", "operation_1", "FindOperation", "CommandOperation", "constructor", "ns", "filter", "options", "undefined", "writeConcern", "Array", "isArray", "MongoInvalidArgumentError", "_bsontype", "_id", "commandName", "execute", "server", "session", "timeoutContext", "findCommand", "makeFindCommand", "explain", "validateExplainTimeoutOptions", "decorateWithExplain", "command", "bsonOptions", "documentsReturnedIn", "ExplainedCursorResponse", "CursorResponse", "exports", "find", "collection", "sort", "formatSort", "projection", "length", "reduce", "result", "field", "hint", "normalizeHintField", "skip", "limit", "singleBatch", "batchSize", "Math", "abs", "comment", "maxTimeMS", "readConcern", "ReadConcern", "fromOptions", "toJSON", "max", "min", "<PERSON><PERSON><PERSON>", "showRecordId", "tailable", "oplogReplay", "timeout", "noCursorTimeout", "await<PERSON><PERSON>", "allowPartialResults", "collation", "allowDiskUse", "let", "defineAspects", "Aspect", "READ_OPERATION", "RETRYABLE", "EXPLAINABLE", "CURSOR_CREATING"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\find.ts"], "sourcesContent": ["import type { Document } from '../bson';\nimport { CursorResponse, ExplainedCursorResponse } from '../cmap/wire_protocol/responses';\nimport { type AbstractCursorOptions, type CursorTimeoutMode } from '../cursor/abstract_cursor';\nimport { MongoInvalidArgumentError } from '../error';\nimport {\n  decorateWithExplain,\n  type ExplainOptions,\n  validateExplainTimeoutOptions\n} from '../explain';\nimport { ReadConcern } from '../read_concern';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { formatSort, type Sort } from '../sort';\nimport { type TimeoutContext } from '../timeout';\nimport { type MongoDBNamespace, normalizeHintField } from '../utils';\nimport { type CollationOptions, CommandOperation, type CommandOperationOptions } from './command';\nimport { Aspect, defineAspects, type Hint } from './operation';\n\n/**\n * @public\n * @typeParam TSchema - Unused schema definition, deprecated usage, only specify `FindOptions` with no generic\n */\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nexport interface FindOptions<TSchema extends Document = Document>\n  extends Omit<CommandOperationOptions, 'writeConcern' | 'explain'>,\n    AbstractCursorOptions {\n  /** Sets the limit of documents returned in the query. */\n  limit?: number;\n  /** Set to sort the documents coming back from the query. Array of indexes, `[['a', 1]]` etc. */\n  sort?: Sort;\n  /** The fields to return in the query. Object of fields to either include or exclude (one of, not both), `{'a':1, 'b': 1}` **or** `{'a': 0, 'b': 0}` */\n  projection?: Document;\n  /** Set to skip N documents ahead in your query (useful for pagination). */\n  skip?: number;\n  /** Tell the query to use specific indexes in the query. Object of indexes to use, `{'_id':1}` */\n  hint?: Hint;\n  /** Specify if the cursor can timeout. */\n  timeout?: boolean;\n  /** Specify if the cursor is tailable. */\n  tailable?: boolean;\n  /** Specify if the cursor is a tailable-await cursor. Requires `tailable` to be true */\n  awaitData?: boolean;\n  /** Set the batchSize for the getMoreCommand when iterating over the query results. */\n  batchSize?: number;\n  /** If true, returns only the index keys in the resulting documents. */\n  returnKey?: boolean;\n  /** The inclusive lower bound for a specific index */\n  min?: Document;\n  /** The exclusive upper bound for a specific index */\n  max?: Document;\n  /** Number of milliseconds to wait before aborting the query. */\n  maxTimeMS?: number;\n  /** The maximum amount of time for the server to wait on new documents to satisfy a tailable cursor query. Requires `tailable` and `awaitData` to be true */\n  maxAwaitTimeMS?: number;\n  /** The server normally times out idle cursors after an inactivity period (10 minutes) to prevent excess memory use. Set this option to prevent that. */\n  noCursorTimeout?: boolean;\n  /** Specify collation (MongoDB 3.4 or higher) settings for update operation (see 3.4 documentation for available fields). */\n  collation?: CollationOptions;\n  /** Allows disk use for blocking sort operations exceeding 100MB memory. (MongoDB 3.2 or higher) */\n  allowDiskUse?: boolean;\n  /** Determines whether to close the cursor after the first batch. Defaults to false. */\n  singleBatch?: boolean;\n  /** For queries against a sharded collection, allows the command (or subsequent getMore commands) to return partial results, rather than an error, if one or more queried shards are unavailable. */\n  allowPartialResults?: boolean;\n  /** Determines whether to return the record identifier for each document. If true, adds a field $recordId to the returned documents. */\n  showRecordId?: boolean;\n  /** Map of parameter names and values that can be accessed using $$var (requires MongoDB 5.0). */\n  let?: Document;\n  /**\n   * Option to enable an optimized code path for queries looking for a particular range of `ts` values in the oplog. Requires `tailable` to be true.\n   * @deprecated Starting from MongoDB 4.4 this flag is not needed and will be ignored.\n   */\n  oplogReplay?: boolean;\n\n  /**\n   * Specifies the verbosity mode for the explain output.\n   * @deprecated This API is deprecated in favor of `collection.find().explain()`.\n   */\n  explain?: ExplainOptions['explain'];\n  /** @internal*/\n  timeoutMode?: CursorTimeoutMode;\n}\n\n/** @internal */\nexport class FindOperation extends CommandOperation<CursorResponse> {\n  /**\n   * @remarks WriteConcern can still be present on the options because\n   * we inherit options from the client/db/collection.  The\n   * key must be present on the options in order to delete it.\n   * This allows typescript to delete the key but will\n   * not allow a writeConcern to be assigned as a property on options.\n   */\n  override options: FindOptions & { writeConcern?: never };\n  filter: Document;\n\n  constructor(ns: MongoDBNamespace, filter: Document = {}, options: FindOptions = {}) {\n    super(undefined, options);\n\n    this.options = { ...options };\n    delete this.options.writeConcern;\n    this.ns = ns;\n\n    if (typeof filter !== 'object' || Array.isArray(filter)) {\n      throw new MongoInvalidArgumentError('Query filter must be a plain object or ObjectId');\n    }\n\n    // special case passing in an ObjectId as a filter\n    this.filter = filter != null && filter._bsontype === 'ObjectId' ? { _id: filter } : filter;\n  }\n\n  override get commandName() {\n    return 'find' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<CursorResponse> {\n    this.server = server;\n\n    const options = this.options;\n\n    let findCommand = makeFindCommand(this.ns, this.filter, options);\n    if (this.explain) {\n      validateExplainTimeoutOptions(this.options, this.explain);\n      findCommand = decorateWithExplain(findCommand, this.explain);\n    }\n\n    return await server.command(\n      this.ns,\n      findCommand,\n      {\n        ...this.options,\n        ...this.bsonOptions,\n        documentsReturnedIn: 'firstBatch',\n        session,\n        timeoutContext\n      },\n      this.explain ? ExplainedCursorResponse : CursorResponse\n    );\n  }\n}\n\nfunction makeFindCommand(ns: MongoDBNamespace, filter: Document, options: FindOptions): Document {\n  const findCommand: Document = {\n    find: ns.collection,\n    filter\n  };\n\n  if (options.sort) {\n    findCommand.sort = formatSort(options.sort);\n  }\n\n  if (options.projection) {\n    let projection = options.projection;\n    if (projection && Array.isArray(projection)) {\n      projection = projection.length\n        ? projection.reduce((result, field) => {\n            result[field] = 1;\n            return result;\n          }, {})\n        : { _id: 1 };\n    }\n\n    findCommand.projection = projection;\n  }\n\n  if (options.hint) {\n    findCommand.hint = normalizeHintField(options.hint);\n  }\n\n  if (typeof options.skip === 'number') {\n    findCommand.skip = options.skip;\n  }\n\n  if (typeof options.limit === 'number') {\n    if (options.limit < 0) {\n      findCommand.limit = -options.limit;\n      findCommand.singleBatch = true;\n    } else {\n      findCommand.limit = options.limit;\n    }\n  }\n\n  if (typeof options.batchSize === 'number') {\n    if (options.batchSize < 0) {\n      if (\n        options.limit &&\n        options.limit !== 0 &&\n        Math.abs(options.batchSize) < Math.abs(options.limit)\n      ) {\n        findCommand.limit = -options.batchSize;\n      }\n\n      findCommand.singleBatch = true;\n    } else {\n      findCommand.batchSize = options.batchSize;\n    }\n  }\n\n  if (typeof options.singleBatch === 'boolean') {\n    findCommand.singleBatch = options.singleBatch;\n  }\n\n  // we check for undefined specifically here to allow falsy values\n  // eslint-disable-next-line no-restricted-syntax\n  if (options.comment !== undefined) {\n    findCommand.comment = options.comment;\n  }\n\n  if (typeof options.maxTimeMS === 'number') {\n    findCommand.maxTimeMS = options.maxTimeMS;\n  }\n\n  const readConcern = ReadConcern.fromOptions(options);\n  if (readConcern) {\n    findCommand.readConcern = readConcern.toJSON();\n  }\n\n  if (options.max) {\n    findCommand.max = options.max;\n  }\n\n  if (options.min) {\n    findCommand.min = options.min;\n  }\n\n  if (typeof options.returnKey === 'boolean') {\n    findCommand.returnKey = options.returnKey;\n  }\n\n  if (typeof options.showRecordId === 'boolean') {\n    findCommand.showRecordId = options.showRecordId;\n  }\n\n  if (typeof options.tailable === 'boolean') {\n    findCommand.tailable = options.tailable;\n  }\n\n  if (typeof options.oplogReplay === 'boolean') {\n    findCommand.oplogReplay = options.oplogReplay;\n  }\n\n  if (typeof options.timeout === 'boolean') {\n    findCommand.noCursorTimeout = !options.timeout;\n  } else if (typeof options.noCursorTimeout === 'boolean') {\n    findCommand.noCursorTimeout = options.noCursorTimeout;\n  }\n\n  if (typeof options.awaitData === 'boolean') {\n    findCommand.awaitData = options.awaitData;\n  }\n\n  if (typeof options.allowPartialResults === 'boolean') {\n    findCommand.allowPartialResults = options.allowPartialResults;\n  }\n\n  if (options.collation) {\n    findCommand.collation = options.collation;\n  }\n\n  if (typeof options.allowDiskUse === 'boolean') {\n    findCommand.allowDiskUse = options.allowDiskUse;\n  }\n\n  if (options.let) {\n    findCommand.let = options.let;\n  }\n\n  return findCommand;\n}\n\ndefineAspects(FindOperation, [\n  Aspect.READ_OPERATION,\n  Aspect.RETRYABLE,\n  Aspect.EXPLAINABLE,\n  Aspect.CURSOR_CREATING\n]);\n"], "mappings": ";;;;;;AACA,MAAAA,WAAA,GAAAC,OAAA;AAEA,MAAAC,OAAA,GAAAD,OAAA;AACA,MAAAE,SAAA,GAAAF,OAAA;AAKA,MAAAG,cAAA,GAAAH,OAAA;AAGA,MAAAI,MAAA,GAAAJ,OAAA;AAEA,MAAAK,OAAA,GAAAL,OAAA;AACA,MAAAM,SAAA,GAAAN,OAAA;AACA,MAAAO,WAAA,GAAAP,OAAA;AAmEA;AACA,MAAaQ,aAAc,SAAQF,SAAA,CAAAG,gBAAgC;EAWjEC,YAAYC,EAAoB,EAAEC,MAAA,GAAmB,EAAE,EAAEC,OAAA,GAAuB,EAAE;IAChF,KAAK,CAACC,SAAS,EAAED,OAAO,CAAC;IAEzB,IAAI,CAACA,OAAO,GAAG;MAAE,GAAGA;IAAO,CAAE;IAC7B,OAAO,IAAI,CAACA,OAAO,CAACE,YAAY;IAChC,IAAI,CAACJ,EAAE,GAAGA,EAAE;IAEZ,IAAI,OAAOC,MAAM,KAAK,QAAQ,IAAII,KAAK,CAACC,OAAO,CAACL,MAAM,CAAC,EAAE;MACvD,MAAM,IAAIX,OAAA,CAAAiB,yBAAyB,CAAC,iDAAiD,CAAC;IACxF;IAEA;IACA,IAAI,CAACN,MAAM,GAAGA,MAAM,IAAI,IAAI,IAAIA,MAAM,CAACO,SAAS,KAAK,UAAU,GAAG;MAAEC,GAAG,EAAER;IAAM,CAAE,GAAGA,MAAM;EAC5F;EAEA,IAAaS,WAAWA,CAAA;IACtB,OAAO,MAAe;EACxB;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,IAAI,CAACF,MAAM,GAAGA,MAAM;IAEpB,MAAMV,OAAO,GAAG,IAAI,CAACA,OAAO;IAE5B,IAAIa,WAAW,GAAGC,eAAe,CAAC,IAAI,CAAChB,EAAE,EAAE,IAAI,CAACC,MAAM,EAAEC,OAAO,CAAC;IAChE,IAAI,IAAI,CAACe,OAAO,EAAE;MAChB,IAAA1B,SAAA,CAAA2B,6BAA6B,EAAC,IAAI,CAAChB,OAAO,EAAE,IAAI,CAACe,OAAO,CAAC;MACzDF,WAAW,GAAG,IAAAxB,SAAA,CAAA4B,mBAAmB,EAACJ,WAAW,EAAE,IAAI,CAACE,OAAO,CAAC;IAC9D;IAEA,OAAO,MAAML,MAAM,CAACQ,OAAO,CACzB,IAAI,CAACpB,EAAE,EACPe,WAAW,EACX;MACE,GAAG,IAAI,CAACb,OAAO;MACf,GAAG,IAAI,CAACmB,WAAW;MACnBC,mBAAmB,EAAE,YAAY;MACjCT,OAAO;MACPC;KACD,EACD,IAAI,CAACG,OAAO,GAAG7B,WAAA,CAAAmC,uBAAuB,GAAGnC,WAAA,CAAAoC,cAAc,CACxD;EACH;;AAzDFC,OAAA,CAAA5B,aAAA,GAAAA,aAAA;AA4DA,SAASmB,eAAeA,CAAChB,EAAoB,EAAEC,MAAgB,EAAEC,OAAoB;EACnF,MAAMa,WAAW,GAAa;IAC5BW,IAAI,EAAE1B,EAAE,CAAC2B,UAAU;IACnB1B;GACD;EAED,IAAIC,OAAO,CAAC0B,IAAI,EAAE;IAChBb,WAAW,CAACa,IAAI,GAAG,IAAAnC,MAAA,CAAAoC,UAAU,EAAC3B,OAAO,CAAC0B,IAAI,CAAC;EAC7C;EAEA,IAAI1B,OAAO,CAAC4B,UAAU,EAAE;IACtB,IAAIA,UAAU,GAAG5B,OAAO,CAAC4B,UAAU;IACnC,IAAIA,UAAU,IAAIzB,KAAK,CAACC,OAAO,CAACwB,UAAU,CAAC,EAAE;MAC3CA,UAAU,GAAGA,UAAU,CAACC,MAAM,GAC1BD,UAAU,CAACE,MAAM,CAAC,CAACC,MAAM,EAAEC,KAAK,KAAI;QAClCD,MAAM,CAACC,KAAK,CAAC,GAAG,CAAC;QACjB,OAAOD,MAAM;MACf,CAAC,EAAE,EAAE,CAAC,GACN;QAAExB,GAAG,EAAE;MAAC,CAAE;IAChB;IAEAM,WAAW,CAACe,UAAU,GAAGA,UAAU;EACrC;EAEA,IAAI5B,OAAO,CAACiC,IAAI,EAAE;IAChBpB,WAAW,CAACoB,IAAI,GAAG,IAAAzC,OAAA,CAAA0C,kBAAkB,EAAClC,OAAO,CAACiC,IAAI,CAAC;EACrD;EAEA,IAAI,OAAOjC,OAAO,CAACmC,IAAI,KAAK,QAAQ,EAAE;IACpCtB,WAAW,CAACsB,IAAI,GAAGnC,OAAO,CAACmC,IAAI;EACjC;EAEA,IAAI,OAAOnC,OAAO,CAACoC,KAAK,KAAK,QAAQ,EAAE;IACrC,IAAIpC,OAAO,CAACoC,KAAK,GAAG,CAAC,EAAE;MACrBvB,WAAW,CAACuB,KAAK,GAAG,CAACpC,OAAO,CAACoC,KAAK;MAClCvB,WAAW,CAACwB,WAAW,GAAG,IAAI;IAChC,CAAC,MAAM;MACLxB,WAAW,CAACuB,KAAK,GAAGpC,OAAO,CAACoC,KAAK;IACnC;EACF;EAEA,IAAI,OAAOpC,OAAO,CAACsC,SAAS,KAAK,QAAQ,EAAE;IACzC,IAAItC,OAAO,CAACsC,SAAS,GAAG,CAAC,EAAE;MACzB,IACEtC,OAAO,CAACoC,KAAK,IACbpC,OAAO,CAACoC,KAAK,KAAK,CAAC,IACnBG,IAAI,CAACC,GAAG,CAACxC,OAAO,CAACsC,SAAS,CAAC,GAAGC,IAAI,CAACC,GAAG,CAACxC,OAAO,CAACoC,KAAK,CAAC,EACrD;QACAvB,WAAW,CAACuB,KAAK,GAAG,CAACpC,OAAO,CAACsC,SAAS;MACxC;MAEAzB,WAAW,CAACwB,WAAW,GAAG,IAAI;IAChC,CAAC,MAAM;MACLxB,WAAW,CAACyB,SAAS,GAAGtC,OAAO,CAACsC,SAAS;IAC3C;EACF;EAEA,IAAI,OAAOtC,OAAO,CAACqC,WAAW,KAAK,SAAS,EAAE;IAC5CxB,WAAW,CAACwB,WAAW,GAAGrC,OAAO,CAACqC,WAAW;EAC/C;EAEA;EACA;EACA,IAAIrC,OAAO,CAACyC,OAAO,KAAKxC,SAAS,EAAE;IACjCY,WAAW,CAAC4B,OAAO,GAAGzC,OAAO,CAACyC,OAAO;EACvC;EAEA,IAAI,OAAOzC,OAAO,CAAC0C,SAAS,KAAK,QAAQ,EAAE;IACzC7B,WAAW,CAAC6B,SAAS,GAAG1C,OAAO,CAAC0C,SAAS;EAC3C;EAEA,MAAMC,WAAW,GAAGrD,cAAA,CAAAsD,WAAW,CAACC,WAAW,CAAC7C,OAAO,CAAC;EACpD,IAAI2C,WAAW,EAAE;IACf9B,WAAW,CAAC8B,WAAW,GAAGA,WAAW,CAACG,MAAM,EAAE;EAChD;EAEA,IAAI9C,OAAO,CAAC+C,GAAG,EAAE;IACflC,WAAW,CAACkC,GAAG,GAAG/C,OAAO,CAAC+C,GAAG;EAC/B;EAEA,IAAI/C,OAAO,CAACgD,GAAG,EAAE;IACfnC,WAAW,CAACmC,GAAG,GAAGhD,OAAO,CAACgD,GAAG;EAC/B;EAEA,IAAI,OAAOhD,OAAO,CAACiD,SAAS,KAAK,SAAS,EAAE;IAC1CpC,WAAW,CAACoC,SAAS,GAAGjD,OAAO,CAACiD,SAAS;EAC3C;EAEA,IAAI,OAAOjD,OAAO,CAACkD,YAAY,KAAK,SAAS,EAAE;IAC7CrC,WAAW,CAACqC,YAAY,GAAGlD,OAAO,CAACkD,YAAY;EACjD;EAEA,IAAI,OAAOlD,OAAO,CAACmD,QAAQ,KAAK,SAAS,EAAE;IACzCtC,WAAW,CAACsC,QAAQ,GAAGnD,OAAO,CAACmD,QAAQ;EACzC;EAEA,IAAI,OAAOnD,OAAO,CAACoD,WAAW,KAAK,SAAS,EAAE;IAC5CvC,WAAW,CAACuC,WAAW,GAAGpD,OAAO,CAACoD,WAAW;EAC/C;EAEA,IAAI,OAAOpD,OAAO,CAACqD,OAAO,KAAK,SAAS,EAAE;IACxCxC,WAAW,CAACyC,eAAe,GAAG,CAACtD,OAAO,CAACqD,OAAO;EAChD,CAAC,MAAM,IAAI,OAAOrD,OAAO,CAACsD,eAAe,KAAK,SAAS,EAAE;IACvDzC,WAAW,CAACyC,eAAe,GAAGtD,OAAO,CAACsD,eAAe;EACvD;EAEA,IAAI,OAAOtD,OAAO,CAACuD,SAAS,KAAK,SAAS,EAAE;IAC1C1C,WAAW,CAAC0C,SAAS,GAAGvD,OAAO,CAACuD,SAAS;EAC3C;EAEA,IAAI,OAAOvD,OAAO,CAACwD,mBAAmB,KAAK,SAAS,EAAE;IACpD3C,WAAW,CAAC2C,mBAAmB,GAAGxD,OAAO,CAACwD,mBAAmB;EAC/D;EAEA,IAAIxD,OAAO,CAACyD,SAAS,EAAE;IACrB5C,WAAW,CAAC4C,SAAS,GAAGzD,OAAO,CAACyD,SAAS;EAC3C;EAEA,IAAI,OAAOzD,OAAO,CAAC0D,YAAY,KAAK,SAAS,EAAE;IAC7C7C,WAAW,CAAC6C,YAAY,GAAG1D,OAAO,CAAC0D,YAAY;EACjD;EAEA,IAAI1D,OAAO,CAAC2D,GAAG,EAAE;IACf9C,WAAW,CAAC8C,GAAG,GAAG3D,OAAO,CAAC2D,GAAG;EAC/B;EAEA,OAAO9C,WAAW;AACpB;AAEA,IAAAnB,WAAA,CAAAkE,aAAa,EAACjE,aAAa,EAAE,CAC3BD,WAAA,CAAAmE,MAAM,CAACC,cAAc,EACrBpE,WAAA,CAAAmE,MAAM,CAACE,SAAS,EAChBrE,WAAA,CAAAmE,MAAM,CAACG,WAAW,EAClBtE,WAAA,CAAAmE,MAAM,CAACI,eAAe,CACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}