{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Encrypter = void 0;\nconst util_1 = require(\"util\");\nconst auto_encrypter_1 = require(\"./client-side-encryption/auto_encrypter\");\nconst constants_1 = require(\"./constants\");\nconst deps_1 = require(\"./deps\");\nconst error_1 = require(\"./error\");\nconst mongo_client_1 = require(\"./mongo_client\");\n/** @internal */\nclass Encrypter {\n  constructor(client, uri, options) {\n    if (typeof options.autoEncryption !== 'object') {\n      throw new error_1.MongoInvalidArgumentError('Option \"autoEncryption\" must be specified');\n    }\n    // initialize to null, if we call getInternalClient, we may set this it is important to not overwrite those function calls.\n    this.internalClient = null;\n    this.bypassAutoEncryption = !!options.autoEncryption.bypassAutoEncryption;\n    this.needsConnecting = false;\n    if (options.maxPoolSize === 0 && options.autoEncryption.keyVaultClient == null) {\n      options.autoEncryption.keyVaultClient = client;\n    } else if (options.autoEncryption.keyVaultClient == null) {\n      options.autoEncryption.keyVaultClient = this.getInternalClient(client, uri, options);\n    }\n    if (this.bypassAutoEncryption) {\n      options.autoEncryption.metadataClient = undefined;\n    } else if (options.maxPoolSize === 0) {\n      options.autoEncryption.metadataClient = client;\n    } else {\n      options.autoEncryption.metadataClient = this.getInternalClient(client, uri, options);\n    }\n    if (options.proxyHost) {\n      options.autoEncryption.proxyOptions = {\n        proxyHost: options.proxyHost,\n        proxyPort: options.proxyPort,\n        proxyUsername: options.proxyUsername,\n        proxyPassword: options.proxyPassword\n      };\n    }\n    this.autoEncrypter = new auto_encrypter_1.AutoEncrypter(client, options.autoEncryption);\n  }\n  getInternalClient(client, uri, options) {\n    let internalClient = this.internalClient;\n    if (internalClient == null) {\n      const clonedOptions = {};\n      for (const key of [...Object.getOwnPropertyNames(options), ...Object.getOwnPropertySymbols(options)]) {\n        if (['autoEncryption', 'minPoolSize', 'servers', 'caseTranslate', 'dbName'].includes(key)) continue;\n        Reflect.set(clonedOptions, key, Reflect.get(options, key));\n      }\n      clonedOptions.minPoolSize = 0;\n      internalClient = new mongo_client_1.MongoClient(uri, clonedOptions);\n      this.internalClient = internalClient;\n      for (const eventName of constants_1.MONGO_CLIENT_EVENTS) {\n        for (const listener of client.listeners(eventName)) {\n          internalClient.on(eventName, listener);\n        }\n      }\n      client.on('newListener', (eventName, listener) => {\n        internalClient?.on(eventName, listener);\n      });\n      this.needsConnecting = true;\n    }\n    return internalClient;\n  }\n  async connectInternalClient() {\n    const internalClient = this.internalClient;\n    if (this.needsConnecting && internalClient != null) {\n      this.needsConnecting = false;\n      await internalClient.connect();\n    }\n  }\n  closeCallback(client, force, callback) {\n    (0, util_1.callbackify)(this.close.bind(this))(client, force, callback);\n  }\n  async close(client, force) {\n    let error;\n    try {\n      await this.autoEncrypter.teardown(force);\n    } catch (autoEncrypterError) {\n      error = autoEncrypterError;\n    }\n    const internalClient = this.internalClient;\n    if (internalClient != null && client !== internalClient) {\n      return await internalClient.close(force);\n    }\n    if (error != null) {\n      throw error;\n    }\n  }\n  static checkForMongoCrypt() {\n    const mongodbClientEncryption = (0, deps_1.getMongoDBClientEncryption)();\n    if ('kModuleError' in mongodbClientEncryption) {\n      throw new error_1.MongoMissingDependencyError('Auto-encryption requested, but the module is not installed. ' + 'Please add `mongodb-client-encryption` as a dependency of your project', {\n        cause: mongodbClientEncryption['kModuleError'],\n        dependencyName: 'mongodb-client-encryption'\n      });\n    }\n  }\n}\nexports.Encrypter = Encrypter;", "map": {"version": 3, "names": ["util_1", "require", "auto_encrypter_1", "constants_1", "deps_1", "error_1", "mongo_client_1", "Encrypter", "constructor", "client", "uri", "options", "autoEncryption", "MongoInvalidArgumentError", "internalClient", "bypassAutoEncryption", "needsConnecting", "maxPoolSize", "keyVaultClient", "getInternalClient", "metadataClient", "undefined", "proxyHost", "proxyOptions", "proxyPort", "proxyUsername", "proxyPassword", "autoEncrypter", "AutoEncrypter", "clonedOptions", "key", "Object", "getOwnPropertyNames", "getOwnPropertySymbols", "includes", "Reflect", "set", "get", "minPoolSize", "MongoClient", "eventName", "MONGO_CLIENT_EVENTS", "listener", "listeners", "on", "connectInternalClient", "connect", "closeCallback", "force", "callback", "callbackify", "close", "bind", "error", "teardown", "autoEncrypterError", "checkForMongoCrypt", "mongodbClientEncryption", "getMongoDBClientEncryption", "MongoMissingDependencyError", "cause", "dependencyName", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\encrypter.ts"], "sourcesContent": ["import { callbackify } from 'util';\n\nimport { AutoEncrypter, type AutoEncryptionOptions } from './client-side-encryption/auto_encrypter';\nimport { MONGO_CLIENT_EVENTS } from './constants';\nimport { getMongoDBClientEncryption } from './deps';\nimport { MongoInvalidArgumentError, MongoMissingDependencyError } from './error';\nimport { MongoClient, type MongoClientOptions } from './mongo_client';\nimport { type Callback } from './utils';\n\n/** @internal */\nexport interface EncrypterOptions {\n  autoEncryption: AutoEncryptionOptions;\n  maxPoolSize?: number;\n}\n\n/** @internal */\nexport class Encrypter {\n  private internalClient: MongoClient | null;\n  bypassAutoEncryption: boolean;\n  needsConnecting: boolean;\n  autoEncrypter: AutoEncrypter;\n\n  constructor(client: MongoClient, uri: string, options: MongoClientOptions) {\n    if (typeof options.autoEncryption !== 'object') {\n      throw new MongoInvalidArgumentError('Option \"autoEncryption\" must be specified');\n    }\n    // initialize to null, if we call getInternalClient, we may set this it is important to not overwrite those function calls.\n    this.internalClient = null;\n\n    this.bypassAutoEncryption = !!options.autoEncryption.bypassAutoEncryption;\n    this.needsConnecting = false;\n\n    if (options.maxPoolSize === 0 && options.autoEncryption.keyVaultClient == null) {\n      options.autoEncryption.keyVaultClient = client;\n    } else if (options.autoEncryption.keyVaultClient == null) {\n      options.autoEncryption.keyVaultClient = this.getInternalClient(client, uri, options);\n    }\n\n    if (this.bypassAutoEncryption) {\n      options.autoEncryption.metadataClient = undefined;\n    } else if (options.maxPoolSize === 0) {\n      options.autoEncryption.metadataClient = client;\n    } else {\n      options.autoEncryption.metadataClient = this.getInternalClient(client, uri, options);\n    }\n\n    if (options.proxyHost) {\n      options.autoEncryption.proxyOptions = {\n        proxyHost: options.proxyHost,\n        proxyPort: options.proxyPort,\n        proxyUsername: options.proxyUsername,\n        proxyPassword: options.proxyPassword\n      };\n    }\n\n    this.autoEncrypter = new AutoEncrypter(client, options.autoEncryption);\n  }\n\n  getInternalClient(client: MongoClient, uri: string, options: MongoClientOptions): MongoClient {\n    let internalClient = this.internalClient;\n    if (internalClient == null) {\n      const clonedOptions: MongoClientOptions = {};\n\n      for (const key of [\n        ...Object.getOwnPropertyNames(options),\n        ...Object.getOwnPropertySymbols(options)\n      ] as string[]) {\n        if (['autoEncryption', 'minPoolSize', 'servers', 'caseTranslate', 'dbName'].includes(key))\n          continue;\n        Reflect.set(clonedOptions, key, Reflect.get(options, key));\n      }\n\n      clonedOptions.minPoolSize = 0;\n\n      internalClient = new MongoClient(uri, clonedOptions);\n      this.internalClient = internalClient;\n\n      for (const eventName of MONGO_CLIENT_EVENTS) {\n        for (const listener of client.listeners(eventName)) {\n          internalClient.on(eventName, listener);\n        }\n      }\n\n      client.on('newListener', (eventName, listener) => {\n        internalClient?.on(eventName, listener);\n      });\n\n      this.needsConnecting = true;\n    }\n    return internalClient;\n  }\n\n  async connectInternalClient(): Promise<void> {\n    const internalClient = this.internalClient;\n    if (this.needsConnecting && internalClient != null) {\n      this.needsConnecting = false;\n      await internalClient.connect();\n    }\n  }\n\n  closeCallback(client: MongoClient, force: boolean, callback: Callback<void>) {\n    callbackify(this.close.bind(this))(client, force, callback);\n  }\n\n  async close(client: MongoClient, force: boolean): Promise<void> {\n    let error;\n    try {\n      await this.autoEncrypter.teardown(force);\n    } catch (autoEncrypterError) {\n      error = autoEncrypterError;\n    }\n    const internalClient = this.internalClient;\n    if (internalClient != null && client !== internalClient) {\n      return await internalClient.close(force);\n    }\n    if (error != null) {\n      throw error;\n    }\n  }\n\n  static checkForMongoCrypt(): void {\n    const mongodbClientEncryption = getMongoDBClientEncryption();\n    if ('kModuleError' in mongodbClientEncryption) {\n      throw new MongoMissingDependencyError(\n        'Auto-encryption requested, but the module is not installed. ' +\n          'Please add `mongodb-client-encryption` as a dependency of your project',\n        {\n          cause: mongodbClientEncryption['kModuleError'],\n          dependencyName: 'mongodb-client-encryption'\n        }\n      );\n    }\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,MAAA,GAAAC,OAAA;AAEA,MAAAC,gBAAA,GAAAD,OAAA;AACA,MAAAE,WAAA,GAAAF,OAAA;AACA,MAAAG,MAAA,GAAAH,OAAA;AACA,MAAAI,OAAA,GAAAJ,OAAA;AACA,MAAAK,cAAA,GAAAL,OAAA;AASA;AACA,MAAaM,SAAS;EAMpBC,YAAYC,MAAmB,EAAEC,GAAW,EAAEC,OAA2B;IACvE,IAAI,OAAOA,OAAO,CAACC,cAAc,KAAK,QAAQ,EAAE;MAC9C,MAAM,IAAIP,OAAA,CAAAQ,yBAAyB,CAAC,2CAA2C,CAAC;IAClF;IACA;IACA,IAAI,CAACC,cAAc,GAAG,IAAI;IAE1B,IAAI,CAACC,oBAAoB,GAAG,CAAC,CAACJ,OAAO,CAACC,cAAc,CAACG,oBAAoB;IACzE,IAAI,CAACC,eAAe,GAAG,KAAK;IAE5B,IAAIL,OAAO,CAACM,WAAW,KAAK,CAAC,IAAIN,OAAO,CAACC,cAAc,CAACM,cAAc,IAAI,IAAI,EAAE;MAC9EP,OAAO,CAACC,cAAc,CAACM,cAAc,GAAGT,MAAM;IAChD,CAAC,MAAM,IAAIE,OAAO,CAACC,cAAc,CAACM,cAAc,IAAI,IAAI,EAAE;MACxDP,OAAO,CAACC,cAAc,CAACM,cAAc,GAAG,IAAI,CAACC,iBAAiB,CAACV,MAAM,EAAEC,GAAG,EAAEC,OAAO,CAAC;IACtF;IAEA,IAAI,IAAI,CAACI,oBAAoB,EAAE;MAC7BJ,OAAO,CAACC,cAAc,CAACQ,cAAc,GAAGC,SAAS;IACnD,CAAC,MAAM,IAAIV,OAAO,CAACM,WAAW,KAAK,CAAC,EAAE;MACpCN,OAAO,CAACC,cAAc,CAACQ,cAAc,GAAGX,MAAM;IAChD,CAAC,MAAM;MACLE,OAAO,CAACC,cAAc,CAACQ,cAAc,GAAG,IAAI,CAACD,iBAAiB,CAACV,MAAM,EAAEC,GAAG,EAAEC,OAAO,CAAC;IACtF;IAEA,IAAIA,OAAO,CAACW,SAAS,EAAE;MACrBX,OAAO,CAACC,cAAc,CAACW,YAAY,GAAG;QACpCD,SAAS,EAAEX,OAAO,CAACW,SAAS;QAC5BE,SAAS,EAAEb,OAAO,CAACa,SAAS;QAC5BC,aAAa,EAAEd,OAAO,CAACc,aAAa;QACpCC,aAAa,EAAEf,OAAO,CAACe;OACxB;IACH;IAEA,IAAI,CAACC,aAAa,GAAG,IAAIzB,gBAAA,CAAA0B,aAAa,CAACnB,MAAM,EAAEE,OAAO,CAACC,cAAc,CAAC;EACxE;EAEAO,iBAAiBA,CAACV,MAAmB,EAAEC,GAAW,EAAEC,OAA2B;IAC7E,IAAIG,cAAc,GAAG,IAAI,CAACA,cAAc;IACxC,IAAIA,cAAc,IAAI,IAAI,EAAE;MAC1B,MAAMe,aAAa,GAAuB,EAAE;MAE5C,KAAK,MAAMC,GAAG,IAAI,CAChB,GAAGC,MAAM,CAACC,mBAAmB,CAACrB,OAAO,CAAC,EACtC,GAAGoB,MAAM,CAACE,qBAAqB,CAACtB,OAAO,CAAC,CAC7B,EAAE;QACb,IAAI,CAAC,gBAAgB,EAAE,aAAa,EAAE,SAAS,EAAE,eAAe,EAAE,QAAQ,CAAC,CAACuB,QAAQ,CAACJ,GAAG,CAAC,EACvF;QACFK,OAAO,CAACC,GAAG,CAACP,aAAa,EAAEC,GAAG,EAAEK,OAAO,CAACE,GAAG,CAAC1B,OAAO,EAAEmB,GAAG,CAAC,CAAC;MAC5D;MAEAD,aAAa,CAACS,WAAW,GAAG,CAAC;MAE7BxB,cAAc,GAAG,IAAIR,cAAA,CAAAiC,WAAW,CAAC7B,GAAG,EAAEmB,aAAa,CAAC;MACpD,IAAI,CAACf,cAAc,GAAGA,cAAc;MAEpC,KAAK,MAAM0B,SAAS,IAAIrC,WAAA,CAAAsC,mBAAmB,EAAE;QAC3C,KAAK,MAAMC,QAAQ,IAAIjC,MAAM,CAACkC,SAAS,CAACH,SAAS,CAAC,EAAE;UAClD1B,cAAc,CAAC8B,EAAE,CAACJ,SAAS,EAAEE,QAAQ,CAAC;QACxC;MACF;MAEAjC,MAAM,CAACmC,EAAE,CAAC,aAAa,EAAE,CAACJ,SAAS,EAAEE,QAAQ,KAAI;QAC/C5B,cAAc,EAAE8B,EAAE,CAACJ,SAAS,EAAEE,QAAQ,CAAC;MACzC,CAAC,CAAC;MAEF,IAAI,CAAC1B,eAAe,GAAG,IAAI;IAC7B;IACA,OAAOF,cAAc;EACvB;EAEA,MAAM+B,qBAAqBA,CAAA;IACzB,MAAM/B,cAAc,GAAG,IAAI,CAACA,cAAc;IAC1C,IAAI,IAAI,CAACE,eAAe,IAAIF,cAAc,IAAI,IAAI,EAAE;MAClD,IAAI,CAACE,eAAe,GAAG,KAAK;MAC5B,MAAMF,cAAc,CAACgC,OAAO,EAAE;IAChC;EACF;EAEAC,aAAaA,CAACtC,MAAmB,EAAEuC,KAAc,EAAEC,QAAwB;IACzE,IAAAjD,MAAA,CAAAkD,WAAW,EAAC,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC3C,MAAM,EAAEuC,KAAK,EAAEC,QAAQ,CAAC;EAC7D;EAEA,MAAME,KAAKA,CAAC1C,MAAmB,EAAEuC,KAAc;IAC7C,IAAIK,KAAK;IACT,IAAI;MACF,MAAM,IAAI,CAAC1B,aAAa,CAAC2B,QAAQ,CAACN,KAAK,CAAC;IAC1C,CAAC,CAAC,OAAOO,kBAAkB,EAAE;MAC3BF,KAAK,GAAGE,kBAAkB;IAC5B;IACA,MAAMzC,cAAc,GAAG,IAAI,CAACA,cAAc;IAC1C,IAAIA,cAAc,IAAI,IAAI,IAAIL,MAAM,KAAKK,cAAc,EAAE;MACvD,OAAO,MAAMA,cAAc,CAACqC,KAAK,CAACH,KAAK,CAAC;IAC1C;IACA,IAAIK,KAAK,IAAI,IAAI,EAAE;MACjB,MAAMA,KAAK;IACb;EACF;EAEA,OAAOG,kBAAkBA,CAAA;IACvB,MAAMC,uBAAuB,GAAG,IAAArD,MAAA,CAAAsD,0BAA0B,GAAE;IAC5D,IAAI,cAAc,IAAID,uBAAuB,EAAE;MAC7C,MAAM,IAAIpD,OAAA,CAAAsD,2BAA2B,CACnC,8DAA8D,GAC5D,wEAAwE,EAC1E;QACEC,KAAK,EAAEH,uBAAuB,CAAC,cAAc,CAAC;QAC9CI,cAAc,EAAE;OACjB,CACF;IACH;EACF;;AApHFC,OAAA,CAAAvD,SAAA,GAAAA,SAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}