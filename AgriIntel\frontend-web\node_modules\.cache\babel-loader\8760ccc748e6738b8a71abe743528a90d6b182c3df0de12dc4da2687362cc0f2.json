{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.OpCompressedRequest = exports.OpMsgResponse = exports.OpMsgRequest = exports.DocumentSequence = exports.OpReply = exports.OpQueryRequest = void 0;\nconst BSON = require(\"../bson\");\nconst error_1 = require(\"../error\");\nconst compression_1 = require(\"./wire_protocol/compression\");\nconst constants_1 = require(\"./wire_protocol/constants\");\n// Incrementing request id\nlet _requestId = 0;\n// Query flags\nconst OPTS_TAILABLE_CURSOR = 2;\nconst OPTS_SECONDARY = 4;\nconst OPTS_OPLOG_REPLAY = 8;\nconst OPTS_NO_CURSOR_TIMEOUT = 16;\nconst OPTS_AWAIT_DATA = 32;\nconst OPTS_EXHAUST = 64;\nconst OPTS_PARTIAL = 128;\n// Response flags\nconst CURSOR_NOT_FOUND = 1;\nconst QUERY_FAILURE = 2;\nconst SHARD_CONFIG_STALE = 4;\nconst AWAIT_CAPABLE = 8;\nconst encodeUTF8Into = BSON.BSON.onDemand.ByteUtils.encodeUTF8Into;\n/** @internal */\nclass OpQueryRequest {\n  constructor(databaseName, query, options) {\n    this.databaseName = databaseName;\n    this.query = query;\n    /** moreToCome is an OP_MSG only concept */\n    this.moreToCome = false;\n    // Basic options needed to be passed in\n    // TODO(NODE-3483): Replace with MongoCommandError\n    const ns = `${databaseName}.$cmd`;\n    if (typeof databaseName !== 'string') {\n      throw new error_1.MongoRuntimeError('Database name must be a string for a query');\n    }\n    // TODO(NODE-3483): Replace with MongoCommandError\n    if (query == null) throw new error_1.MongoRuntimeError('A query document must be specified for query');\n    // Validate that we are not passing 0x00 in the collection name\n    if (ns.indexOf('\\x00') !== -1) {\n      // TODO(NODE-3483): Use MongoNamespace static method\n      throw new error_1.MongoRuntimeError('Namespace cannot contain a null character');\n    }\n    // Basic options\n    this.ns = ns;\n    // Additional options\n    this.numberToSkip = options.numberToSkip || 0;\n    this.numberToReturn = options.numberToReturn || 0;\n    this.returnFieldSelector = options.returnFieldSelector || undefined;\n    this.requestId = options.requestId ?? OpQueryRequest.getRequestId();\n    // special case for pre-3.2 find commands, delete ASAP\n    this.pre32Limit = options.pre32Limit;\n    // Serialization option\n    this.serializeFunctions = typeof options.serializeFunctions === 'boolean' ? options.serializeFunctions : false;\n    this.ignoreUndefined = typeof options.ignoreUndefined === 'boolean' ? options.ignoreUndefined : false;\n    this.maxBsonSize = options.maxBsonSize || 1024 * 1024 * 16;\n    this.checkKeys = typeof options.checkKeys === 'boolean' ? options.checkKeys : false;\n    this.batchSize = this.numberToReturn;\n    // Flags\n    this.tailable = false;\n    this.secondaryOk = typeof options.secondaryOk === 'boolean' ? options.secondaryOk : false;\n    this.oplogReplay = false;\n    this.noCursorTimeout = false;\n    this.awaitData = false;\n    this.exhaust = false;\n    this.partial = false;\n  }\n  /** Assign next request Id. */\n  incRequestId() {\n    this.requestId = _requestId++;\n  }\n  /** Peek next request Id. */\n  nextRequestId() {\n    return _requestId + 1;\n  }\n  /** Increment then return next request Id. */\n  static getRequestId() {\n    return ++_requestId;\n  }\n  // Uses a single allocated buffer for the process, avoiding multiple memory allocations\n  toBin() {\n    const buffers = [];\n    let projection = null;\n    // Set up the flags\n    let flags = 0;\n    if (this.tailable) {\n      flags |= OPTS_TAILABLE_CURSOR;\n    }\n    if (this.secondaryOk) {\n      flags |= OPTS_SECONDARY;\n    }\n    if (this.oplogReplay) {\n      flags |= OPTS_OPLOG_REPLAY;\n    }\n    if (this.noCursorTimeout) {\n      flags |= OPTS_NO_CURSOR_TIMEOUT;\n    }\n    if (this.awaitData) {\n      flags |= OPTS_AWAIT_DATA;\n    }\n    if (this.exhaust) {\n      flags |= OPTS_EXHAUST;\n    }\n    if (this.partial) {\n      flags |= OPTS_PARTIAL;\n    }\n    // If batchSize is different to this.numberToReturn\n    if (this.batchSize !== this.numberToReturn) this.numberToReturn = this.batchSize;\n    // Allocate write protocol header buffer\n    const header = Buffer.alloc(4 * 4 +\n    // Header\n    4 +\n    // Flags\n    Buffer.byteLength(this.ns) + 1 +\n    // namespace\n    4 +\n    // numberToSkip\n    4 // numberToReturn\n    );\n    // Add header to buffers\n    buffers.push(header);\n    // Serialize the query\n    const query = BSON.serialize(this.query, {\n      checkKeys: this.checkKeys,\n      serializeFunctions: this.serializeFunctions,\n      ignoreUndefined: this.ignoreUndefined\n    });\n    // Add query document\n    buffers.push(query);\n    if (this.returnFieldSelector && Object.keys(this.returnFieldSelector).length > 0) {\n      // Serialize the projection document\n      projection = BSON.serialize(this.returnFieldSelector, {\n        checkKeys: this.checkKeys,\n        serializeFunctions: this.serializeFunctions,\n        ignoreUndefined: this.ignoreUndefined\n      });\n      // Add projection document\n      buffers.push(projection);\n    }\n    // Total message size\n    const totalLength = header.length + query.length + (projection ? projection.length : 0);\n    // Set up the index\n    let index = 4;\n    // Write total document length\n    header[3] = totalLength >> 24 & 0xff;\n    header[2] = totalLength >> 16 & 0xff;\n    header[1] = totalLength >> 8 & 0xff;\n    header[0] = totalLength & 0xff;\n    // Write header information requestId\n    header[index + 3] = this.requestId >> 24 & 0xff;\n    header[index + 2] = this.requestId >> 16 & 0xff;\n    header[index + 1] = this.requestId >> 8 & 0xff;\n    header[index] = this.requestId & 0xff;\n    index = index + 4;\n    // Write header information responseTo\n    header[index + 3] = 0 >> 24 & 0xff;\n    header[index + 2] = 0 >> 16 & 0xff;\n    header[index + 1] = 0 >> 8 & 0xff;\n    header[index] = 0 & 0xff;\n    index = index + 4;\n    // Write header information OP_QUERY\n    header[index + 3] = constants_1.OP_QUERY >> 24 & 0xff;\n    header[index + 2] = constants_1.OP_QUERY >> 16 & 0xff;\n    header[index + 1] = constants_1.OP_QUERY >> 8 & 0xff;\n    header[index] = constants_1.OP_QUERY & 0xff;\n    index = index + 4;\n    // Write header information flags\n    header[index + 3] = flags >> 24 & 0xff;\n    header[index + 2] = flags >> 16 & 0xff;\n    header[index + 1] = flags >> 8 & 0xff;\n    header[index] = flags & 0xff;\n    index = index + 4;\n    // Write collection name\n    index = index + header.write(this.ns, index, 'utf8') + 1;\n    header[index - 1] = 0;\n    // Write header information flags numberToSkip\n    header[index + 3] = this.numberToSkip >> 24 & 0xff;\n    header[index + 2] = this.numberToSkip >> 16 & 0xff;\n    header[index + 1] = this.numberToSkip >> 8 & 0xff;\n    header[index] = this.numberToSkip & 0xff;\n    index = index + 4;\n    // Write header information flags numberToReturn\n    header[index + 3] = this.numberToReturn >> 24 & 0xff;\n    header[index + 2] = this.numberToReturn >> 16 & 0xff;\n    header[index + 1] = this.numberToReturn >> 8 & 0xff;\n    header[index] = this.numberToReturn & 0xff;\n    index = index + 4;\n    // Return the buffers\n    return buffers;\n  }\n}\nexports.OpQueryRequest = OpQueryRequest;\n/** @internal */\nclass OpReply {\n  constructor(message, msgHeader, msgBody, opts) {\n    this.index = 0;\n    this.sections = [];\n    /** moreToCome is an OP_MSG only concept */\n    this.moreToCome = false;\n    this.parsed = false;\n    this.raw = message;\n    this.data = msgBody;\n    this.opts = opts ?? {\n      useBigInt64: false,\n      promoteLongs: true,\n      promoteValues: true,\n      promoteBuffers: false,\n      bsonRegExp: false\n    };\n    // Read the message header\n    this.length = msgHeader.length;\n    this.requestId = msgHeader.requestId;\n    this.responseTo = msgHeader.responseTo;\n    this.opCode = msgHeader.opCode;\n    this.fromCompressed = msgHeader.fromCompressed;\n    // Flag values\n    this.useBigInt64 = typeof this.opts.useBigInt64 === 'boolean' ? this.opts.useBigInt64 : false;\n    this.promoteLongs = typeof this.opts.promoteLongs === 'boolean' ? this.opts.promoteLongs : true;\n    this.promoteValues = typeof this.opts.promoteValues === 'boolean' ? this.opts.promoteValues : true;\n    this.promoteBuffers = typeof this.opts.promoteBuffers === 'boolean' ? this.opts.promoteBuffers : false;\n    this.bsonRegExp = typeof this.opts.bsonRegExp === 'boolean' ? this.opts.bsonRegExp : false;\n  }\n  isParsed() {\n    return this.parsed;\n  }\n  parse() {\n    // Don't parse again if not needed\n    if (this.parsed) return this.sections[0];\n    // Position within OP_REPLY at which documents start\n    // (See https://www.mongodb.com/docs/manual/reference/mongodb-wire-protocol/#wire-op-reply)\n    this.index = 20;\n    // Read the message body\n    this.responseFlags = this.data.readInt32LE(0);\n    this.cursorId = new BSON.Long(this.data.readInt32LE(4), this.data.readInt32LE(8));\n    this.startingFrom = this.data.readInt32LE(12);\n    this.numberReturned = this.data.readInt32LE(16);\n    if (this.numberReturned < 0 || this.numberReturned > 2 ** 32 - 1) {\n      throw new RangeError(`OP_REPLY numberReturned is an invalid array length ${this.numberReturned}`);\n    }\n    this.cursorNotFound = (this.responseFlags & CURSOR_NOT_FOUND) !== 0;\n    this.queryFailure = (this.responseFlags & QUERY_FAILURE) !== 0;\n    this.shardConfigStale = (this.responseFlags & SHARD_CONFIG_STALE) !== 0;\n    this.awaitCapable = (this.responseFlags & AWAIT_CAPABLE) !== 0;\n    // Parse Body\n    for (let i = 0; i < this.numberReturned; i++) {\n      const bsonSize = this.data[this.index] | this.data[this.index + 1] << 8 | this.data[this.index + 2] << 16 | this.data[this.index + 3] << 24;\n      const section = this.data.subarray(this.index, this.index + bsonSize);\n      this.sections.push(section);\n      // Adjust the index\n      this.index = this.index + bsonSize;\n    }\n    // Set parsed\n    this.parsed = true;\n    return this.sections[0];\n  }\n}\nexports.OpReply = OpReply;\n// Msg Flags\nconst OPTS_CHECKSUM_PRESENT = 1;\nconst OPTS_MORE_TO_COME = 2;\nconst OPTS_EXHAUST_ALLOWED = 1 << 16;\n/** @internal */\nclass DocumentSequence {\n  /**\n   * Create a new document sequence for the provided field.\n   * @param field - The field it will replace.\n   */\n  constructor(field, documents) {\n    this.field = field;\n    this.documents = [];\n    this.chunks = [];\n    this.serializedDocumentsLength = 0;\n    // Document sequences starts with type 1 at the first byte.\n    // Field strings must always be UTF-8.\n    const buffer = Buffer.allocUnsafe(1 + 4 + this.field.length + 1);\n    buffer[0] = 1;\n    // Third part is the field name at offset 5 with trailing null byte.\n    encodeUTF8Into(buffer, `${this.field}\\0`, 5);\n    this.chunks.push(buffer);\n    this.header = buffer;\n    if (documents) {\n      for (const doc of documents) {\n        this.push(doc, BSON.serialize(doc));\n      }\n    }\n  }\n  /**\n   * Push a document to the document sequence. Will serialize the document\n   * as well and return the current serialized length of all documents.\n   * @param document - The document to add.\n   * @param buffer - The serialized document in raw BSON.\n   * @returns The new total document sequence length.\n   */\n  push(document, buffer) {\n    this.serializedDocumentsLength += buffer.length;\n    // Push the document.\n    this.documents.push(document);\n    // Push the document raw bson.\n    this.chunks.push(buffer);\n    // Write the new length.\n    this.header?.writeInt32LE(4 + this.field.length + 1 + this.serializedDocumentsLength, 1);\n    return this.serializedDocumentsLength + this.header.length;\n  }\n  /**\n   * Get the fully serialized bytes for the document sequence section.\n   * @returns The section bytes.\n   */\n  toBin() {\n    return Buffer.concat(this.chunks);\n  }\n}\nexports.DocumentSequence = DocumentSequence;\n/** @internal */\nclass OpMsgRequest {\n  constructor(databaseName, command, options) {\n    this.databaseName = databaseName;\n    this.command = command;\n    this.options = options;\n    // Basic options needed to be passed in\n    if (command == null) throw new error_1.MongoInvalidArgumentError('Query document must be specified for query');\n    // Basic options\n    this.command.$db = databaseName;\n    // Ensure empty options\n    this.options = options ?? {};\n    // Additional options\n    this.requestId = options.requestId ? options.requestId : OpMsgRequest.getRequestId();\n    // Serialization option\n    this.serializeFunctions = typeof options.serializeFunctions === 'boolean' ? options.serializeFunctions : false;\n    this.ignoreUndefined = typeof options.ignoreUndefined === 'boolean' ? options.ignoreUndefined : false;\n    this.checkKeys = typeof options.checkKeys === 'boolean' ? options.checkKeys : false;\n    this.maxBsonSize = options.maxBsonSize || 1024 * 1024 * 16;\n    // flags\n    this.checksumPresent = false;\n    this.moreToCome = options.moreToCome ?? command.writeConcern?.w === 0;\n    this.exhaustAllowed = typeof options.exhaustAllowed === 'boolean' ? options.exhaustAllowed : false;\n  }\n  toBin() {\n    const buffers = [];\n    let flags = 0;\n    if (this.checksumPresent) {\n      flags |= OPTS_CHECKSUM_PRESENT;\n    }\n    if (this.moreToCome) {\n      flags |= OPTS_MORE_TO_COME;\n    }\n    if (this.exhaustAllowed) {\n      flags |= OPTS_EXHAUST_ALLOWED;\n    }\n    const header = Buffer.alloc(4 * 4 +\n    // Header\n    4 // Flags\n    );\n    buffers.push(header);\n    let totalLength = header.length;\n    const command = this.command;\n    totalLength += this.makeSections(buffers, command);\n    header.writeInt32LE(totalLength, 0); // messageLength\n    header.writeInt32LE(this.requestId, 4); // requestID\n    header.writeInt32LE(0, 8); // responseTo\n    header.writeInt32LE(constants_1.OP_MSG, 12); // opCode\n    header.writeUInt32LE(flags, 16); // flags\n    return buffers;\n  }\n  /**\n   * Add the sections to the OP_MSG request's buffers and returns the length.\n   */\n  makeSections(buffers, document) {\n    const sequencesBuffer = this.extractDocumentSequences(document);\n    const payloadTypeBuffer = Buffer.allocUnsafe(1);\n    payloadTypeBuffer[0] = 0;\n    const documentBuffer = this.serializeBson(document);\n    // First section, type 0\n    buffers.push(payloadTypeBuffer);\n    buffers.push(documentBuffer);\n    // Subsequent sections, type 1\n    buffers.push(sequencesBuffer);\n    return payloadTypeBuffer.length + documentBuffer.length + sequencesBuffer.length;\n  }\n  /**\n   * Extracts the document sequences from the command document and returns\n   * a buffer to be added as multiple sections after the initial type 0\n   * section in the message.\n   */\n  extractDocumentSequences(document) {\n    // Pull out any field in the command document that's value is a document sequence.\n    const chunks = [];\n    for (const [key, value] of Object.entries(document)) {\n      if (value instanceof DocumentSequence) {\n        chunks.push(value.toBin());\n        // Why are we removing the field from the command? This is because it needs to be\n        // removed in the OP_MSG request first section, and DocumentSequence is not a\n        // BSON type and is specific to the MongoDB wire protocol so there's nothing\n        // our BSON serializer can do about this. Since DocumentSequence is not exposed\n        // in the public API and only used internally, we are never mutating an original\n        // command provided by the user, just our own, and it's cheaper to delete from\n        // our own command than copying it.\n        delete document[key];\n      }\n    }\n    if (chunks.length > 0) {\n      return Buffer.concat(chunks);\n    }\n    // If we have no document sequences we return an empty buffer for nothing to add\n    // to the payload.\n    return Buffer.alloc(0);\n  }\n  serializeBson(document) {\n    return BSON.serialize(document, {\n      checkKeys: this.checkKeys,\n      serializeFunctions: this.serializeFunctions,\n      ignoreUndefined: this.ignoreUndefined\n    });\n  }\n  static getRequestId() {\n    _requestId = _requestId + 1 & 0x7fffffff;\n    return _requestId;\n  }\n}\nexports.OpMsgRequest = OpMsgRequest;\n/** @internal */\nclass OpMsgResponse {\n  constructor(message, msgHeader, msgBody, opts) {\n    this.index = 0;\n    this.sections = [];\n    this.parsed = false;\n    this.raw = message;\n    this.data = msgBody;\n    this.opts = opts ?? {\n      useBigInt64: false,\n      promoteLongs: true,\n      promoteValues: true,\n      promoteBuffers: false,\n      bsonRegExp: false\n    };\n    // Read the message header\n    this.length = msgHeader.length;\n    this.requestId = msgHeader.requestId;\n    this.responseTo = msgHeader.responseTo;\n    this.opCode = msgHeader.opCode;\n    this.fromCompressed = msgHeader.fromCompressed;\n    // Read response flags\n    this.responseFlags = msgBody.readInt32LE(0);\n    this.checksumPresent = (this.responseFlags & OPTS_CHECKSUM_PRESENT) !== 0;\n    this.moreToCome = (this.responseFlags & OPTS_MORE_TO_COME) !== 0;\n    this.exhaustAllowed = (this.responseFlags & OPTS_EXHAUST_ALLOWED) !== 0;\n    this.useBigInt64 = typeof this.opts.useBigInt64 === 'boolean' ? this.opts.useBigInt64 : false;\n    this.promoteLongs = typeof this.opts.promoteLongs === 'boolean' ? this.opts.promoteLongs : true;\n    this.promoteValues = typeof this.opts.promoteValues === 'boolean' ? this.opts.promoteValues : true;\n    this.promoteBuffers = typeof this.opts.promoteBuffers === 'boolean' ? this.opts.promoteBuffers : false;\n    this.bsonRegExp = typeof this.opts.bsonRegExp === 'boolean' ? this.opts.bsonRegExp : false;\n  }\n  isParsed() {\n    return this.parsed;\n  }\n  parse() {\n    // Don't parse again if not needed\n    if (this.parsed) return this.sections[0];\n    this.index = 4;\n    while (this.index < this.data.length) {\n      const payloadType = this.data.readUInt8(this.index++);\n      if (payloadType === 0) {\n        const bsonSize = this.data.readUInt32LE(this.index);\n        const bin = this.data.subarray(this.index, this.index + bsonSize);\n        this.sections.push(bin);\n        this.index += bsonSize;\n      } else if (payloadType === 1) {\n        // It was decided that no driver makes use of payload type 1\n        // TODO(NODE-3483): Replace with MongoDeprecationError\n        throw new error_1.MongoRuntimeError('OP_MSG Payload Type 1 detected unsupported protocol');\n      }\n    }\n    this.parsed = true;\n    return this.sections[0];\n  }\n}\nexports.OpMsgResponse = OpMsgResponse;\nconst MESSAGE_HEADER_SIZE = 16;\nconst COMPRESSION_DETAILS_SIZE = 9; // originalOpcode + uncompressedSize, compressorID\n/**\n * @internal\n *\n * An OP_COMPRESSED request wraps either an OP_QUERY or OP_MSG message.\n */\nclass OpCompressedRequest {\n  constructor(command, options) {\n    this.command = command;\n    this.options = options;\n  }\n  // Return whether a command contains an uncompressible command term\n  // Will return true if command contains no uncompressible command terms\n  static canCompress(command) {\n    const commandDoc = command instanceof OpMsgRequest ? command.command : command.query;\n    const commandName = Object.keys(commandDoc)[0];\n    return !compression_1.uncompressibleCommands.has(commandName);\n  }\n  async toBin() {\n    const concatenatedOriginalCommandBuffer = Buffer.concat(this.command.toBin());\n    // otherwise, compress the message\n    const messageToBeCompressed = concatenatedOriginalCommandBuffer.slice(MESSAGE_HEADER_SIZE);\n    // Extract information needed for OP_COMPRESSED from the uncompressed message\n    const originalCommandOpCode = concatenatedOriginalCommandBuffer.readInt32LE(12);\n    // Compress the message body\n    const compressedMessage = await (0, compression_1.compress)(this.options, messageToBeCompressed);\n    // Create the msgHeader of OP_COMPRESSED\n    const msgHeader = Buffer.alloc(MESSAGE_HEADER_SIZE);\n    msgHeader.writeInt32LE(MESSAGE_HEADER_SIZE + COMPRESSION_DETAILS_SIZE + compressedMessage.length, 0); // messageLength\n    msgHeader.writeInt32LE(this.command.requestId, 4); // requestID\n    msgHeader.writeInt32LE(0, 8); // responseTo (zero)\n    msgHeader.writeInt32LE(constants_1.OP_COMPRESSED, 12); // opCode\n    // Create the compression details of OP_COMPRESSED\n    const compressionDetails = Buffer.alloc(COMPRESSION_DETAILS_SIZE);\n    compressionDetails.writeInt32LE(originalCommandOpCode, 0); // originalOpcode\n    compressionDetails.writeInt32LE(messageToBeCompressed.length, 4); // Size of the uncompressed compressedMessage, excluding the MsgHeader\n    compressionDetails.writeUInt8(compression_1.Compressor[this.options.agreedCompressor], 8); // compressorID\n    return [msgHeader, compressionDetails, compressedMessage];\n  }\n}\nexports.OpCompressedRequest = OpCompressedRequest;", "map": {"version": 3, "names": ["BSON", "require", "error_1", "compression_1", "constants_1", "_requestId", "OPTS_TAILABLE_CURSOR", "OPTS_SECONDARY", "OPTS_OPLOG_REPLAY", "OPTS_NO_CURSOR_TIMEOUT", "OPTS_AWAIT_DATA", "OPTS_EXHAUST", "OPTS_PARTIAL", "CURSOR_NOT_FOUND", "QUERY_FAILURE", "SHARD_CONFIG_STALE", "AWAIT_CAPABLE", "encodeUTF8Into", "onDemand", "ByteUtils", "OpQueryRequest", "constructor", "databaseName", "query", "options", "moreToCome", "ns", "MongoRuntimeError", "indexOf", "numberToSkip", "numberToReturn", "returnFieldSelector", "undefined", "requestId", "getRequestId", "pre32Limit", "serializeFunctions", "ignoreUndefined", "maxBsonSize", "checkKeys", "batchSize", "tailable", "secondaryOk", "oplogReplay", "noCursorTimeout", "await<PERSON><PERSON>", "exhaust", "partial", "incRequestId", "nextRequestId", "to<PERSON>in", "buffers", "projection", "flags", "header", "<PERSON><PERSON><PERSON>", "alloc", "byteLength", "push", "serialize", "Object", "keys", "length", "totalLength", "index", "OP_QUERY", "write", "exports", "OpReply", "message", "msgH<PERSON>er", "msgBody", "opts", "sections", "parsed", "raw", "data", "useBigInt64", "promoteLongs", "promoteValues", "promoteBuffers", "bsonRegExp", "responseTo", "opCode", "fromCompressed", "isParsed", "parse", "responseFlags", "readInt32LE", "cursorId", "<PERSON>", "startingFrom", "numberReturned", "RangeError", "cursorNotFound", "queryFailure", "shardConfigStale", "await<PERSON><PERSON><PERSON>", "i", "bsonSize", "section", "subarray", "OPTS_CHECKSUM_PRESENT", "OPTS_MORE_TO_COME", "OPTS_EXHAUST_ALLOWED", "DocumentSequence", "field", "documents", "chunks", "serializedDocumentsLength", "buffer", "allocUnsafe", "doc", "document", "writeInt32LE", "concat", "OpMsgRequest", "command", "MongoInvalidArgumentError", "$db", "checksumPresent", "writeConcern", "w", "exhaustAllowed", "makeSections", "OP_MSG", "writeUInt32LE", "<PERSON><PERSON><PERSON><PERSON>", "extractDocumentSequences", "payloadTypeBuffer", "documentBuffer", "serializeBson", "key", "value", "entries", "OpMsgResponse", "payloadType", "readUInt8", "readUInt32LE", "bin", "MESSAGE_HEADER_SIZE", "COMPRESSION_DETAILS_SIZE", "OpCompressedRequest", "canCompress", "commandDoc", "commandName", "uncompressibleCommands", "has", "concatenatedOriginalCommandBuffer", "messageToBeCompressed", "slice", "originalCommandOpCode", "compressedMessage", "compress", "OP_COMPRESSED", "compressionDetails", "writeUInt8", "Compressor", "agreedCompressor"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\commands.ts"], "sourcesContent": ["import type { BSONSerializeOptions, Document, Long } from '../bson';\nimport * as BSON from '../bson';\nimport { MongoInvalidArgumentError, MongoRuntimeError } from '../error';\nimport { type ReadPreference } from '../read_preference';\nimport type { ClientSession } from '../sessions';\nimport type { CommandOptions } from './connection';\nimport {\n  compress,\n  Compressor,\n  type CompressorName,\n  uncompressibleCommands\n} from './wire_protocol/compression';\nimport { OP_COMPRESSED, OP_MSG, OP_QUERY } from './wire_protocol/constants';\n\n// Incrementing request id\nlet _requestId = 0;\n\n// Query flags\nconst OPTS_TAILABLE_CURSOR = 2;\nconst OPTS_SECONDARY = 4;\nconst OPTS_OPLOG_REPLAY = 8;\nconst OPTS_NO_CURSOR_TIMEOUT = 16;\nconst OPTS_AWAIT_DATA = 32;\nconst OPTS_EXHAUST = 64;\nconst OPTS_PARTIAL = 128;\n\n// Response flags\nconst CURSOR_NOT_FOUND = 1;\nconst QUERY_FAILURE = 2;\nconst SHARD_CONFIG_STALE = 4;\nconst AWAIT_CAPABLE = 8;\n\nconst encodeUTF8Into = BSON.BSON.onDemand.ByteUtils.encodeUTF8Into;\n\n/** @internal */\nexport type WriteProtocolMessageType = OpQueryRequest | OpMsgRequest;\n\n/** @internal */\nexport interface OpQueryOptions extends CommandOptions {\n  socketTimeoutMS?: number;\n  session?: ClientSession;\n  numberToSkip?: number;\n  numberToReturn?: number;\n  returnFieldSelector?: Document;\n  pre32Limit?: number;\n  serializeFunctions?: boolean;\n  ignoreUndefined?: boolean;\n  maxBsonSize?: number;\n  checkKeys?: boolean;\n  secondaryOk?: boolean;\n\n  requestId?: number;\n  moreToCome?: boolean;\n  exhaustAllowed?: boolean;\n}\n\n/** @internal */\nexport class OpQueryRequest {\n  ns: string;\n  numberToSkip: number;\n  numberToReturn: number;\n  returnFieldSelector?: Document;\n  requestId: number;\n  pre32Limit?: number;\n  serializeFunctions: boolean;\n  ignoreUndefined: boolean;\n  maxBsonSize: number;\n  checkKeys: boolean;\n  batchSize: number;\n  tailable: boolean;\n  secondaryOk: boolean;\n  oplogReplay: boolean;\n  noCursorTimeout: boolean;\n  awaitData: boolean;\n  exhaust: boolean;\n  partial: boolean;\n  /** moreToCome is an OP_MSG only concept */\n  moreToCome = false;\n\n  constructor(\n    public databaseName: string,\n    public query: Document,\n    options: OpQueryOptions\n  ) {\n    // Basic options needed to be passed in\n    // TODO(NODE-3483): Replace with MongoCommandError\n    const ns = `${databaseName}.$cmd`;\n    if (typeof databaseName !== 'string') {\n      throw new MongoRuntimeError('Database name must be a string for a query');\n    }\n    // TODO(NODE-3483): Replace with MongoCommandError\n    if (query == null) throw new MongoRuntimeError('A query document must be specified for query');\n\n    // Validate that we are not passing 0x00 in the collection name\n    if (ns.indexOf('\\x00') !== -1) {\n      // TODO(NODE-3483): Use MongoNamespace static method\n      throw new MongoRuntimeError('Namespace cannot contain a null character');\n    }\n\n    // Basic options\n    this.ns = ns;\n\n    // Additional options\n    this.numberToSkip = options.numberToSkip || 0;\n    this.numberToReturn = options.numberToReturn || 0;\n    this.returnFieldSelector = options.returnFieldSelector || undefined;\n    this.requestId = options.requestId ?? OpQueryRequest.getRequestId();\n\n    // special case for pre-3.2 find commands, delete ASAP\n    this.pre32Limit = options.pre32Limit;\n\n    // Serialization option\n    this.serializeFunctions =\n      typeof options.serializeFunctions === 'boolean' ? options.serializeFunctions : false;\n    this.ignoreUndefined =\n      typeof options.ignoreUndefined === 'boolean' ? options.ignoreUndefined : false;\n    this.maxBsonSize = options.maxBsonSize || 1024 * 1024 * 16;\n    this.checkKeys = typeof options.checkKeys === 'boolean' ? options.checkKeys : false;\n    this.batchSize = this.numberToReturn;\n\n    // Flags\n    this.tailable = false;\n    this.secondaryOk = typeof options.secondaryOk === 'boolean' ? options.secondaryOk : false;\n    this.oplogReplay = false;\n    this.noCursorTimeout = false;\n    this.awaitData = false;\n    this.exhaust = false;\n    this.partial = false;\n  }\n\n  /** Assign next request Id. */\n  incRequestId(): void {\n    this.requestId = _requestId++;\n  }\n\n  /** Peek next request Id. */\n  nextRequestId(): number {\n    return _requestId + 1;\n  }\n\n  /** Increment then return next request Id. */\n  static getRequestId(): number {\n    return ++_requestId;\n  }\n\n  // Uses a single allocated buffer for the process, avoiding multiple memory allocations\n  toBin(): Uint8Array[] {\n    const buffers = [];\n    let projection = null;\n\n    // Set up the flags\n    let flags = 0;\n    if (this.tailable) {\n      flags |= OPTS_TAILABLE_CURSOR;\n    }\n\n    if (this.secondaryOk) {\n      flags |= OPTS_SECONDARY;\n    }\n\n    if (this.oplogReplay) {\n      flags |= OPTS_OPLOG_REPLAY;\n    }\n\n    if (this.noCursorTimeout) {\n      flags |= OPTS_NO_CURSOR_TIMEOUT;\n    }\n\n    if (this.awaitData) {\n      flags |= OPTS_AWAIT_DATA;\n    }\n\n    if (this.exhaust) {\n      flags |= OPTS_EXHAUST;\n    }\n\n    if (this.partial) {\n      flags |= OPTS_PARTIAL;\n    }\n\n    // If batchSize is different to this.numberToReturn\n    if (this.batchSize !== this.numberToReturn) this.numberToReturn = this.batchSize;\n\n    // Allocate write protocol header buffer\n    const header = Buffer.alloc(\n      4 * 4 + // Header\n        4 + // Flags\n        Buffer.byteLength(this.ns) +\n        1 + // namespace\n        4 + // numberToSkip\n        4 // numberToReturn\n    );\n\n    // Add header to buffers\n    buffers.push(header);\n\n    // Serialize the query\n    const query = BSON.serialize(this.query, {\n      checkKeys: this.checkKeys,\n      serializeFunctions: this.serializeFunctions,\n      ignoreUndefined: this.ignoreUndefined\n    });\n\n    // Add query document\n    buffers.push(query);\n\n    if (this.returnFieldSelector && Object.keys(this.returnFieldSelector).length > 0) {\n      // Serialize the projection document\n      projection = BSON.serialize(this.returnFieldSelector, {\n        checkKeys: this.checkKeys,\n        serializeFunctions: this.serializeFunctions,\n        ignoreUndefined: this.ignoreUndefined\n      });\n      // Add projection document\n      buffers.push(projection);\n    }\n\n    // Total message size\n    const totalLength = header.length + query.length + (projection ? projection.length : 0);\n\n    // Set up the index\n    let index = 4;\n\n    // Write total document length\n    header[3] = (totalLength >> 24) & 0xff;\n    header[2] = (totalLength >> 16) & 0xff;\n    header[1] = (totalLength >> 8) & 0xff;\n    header[0] = totalLength & 0xff;\n\n    // Write header information requestId\n    header[index + 3] = (this.requestId >> 24) & 0xff;\n    header[index + 2] = (this.requestId >> 16) & 0xff;\n    header[index + 1] = (this.requestId >> 8) & 0xff;\n    header[index] = this.requestId & 0xff;\n    index = index + 4;\n\n    // Write header information responseTo\n    header[index + 3] = (0 >> 24) & 0xff;\n    header[index + 2] = (0 >> 16) & 0xff;\n    header[index + 1] = (0 >> 8) & 0xff;\n    header[index] = 0 & 0xff;\n    index = index + 4;\n\n    // Write header information OP_QUERY\n    header[index + 3] = (OP_QUERY >> 24) & 0xff;\n    header[index + 2] = (OP_QUERY >> 16) & 0xff;\n    header[index + 1] = (OP_QUERY >> 8) & 0xff;\n    header[index] = OP_QUERY & 0xff;\n    index = index + 4;\n\n    // Write header information flags\n    header[index + 3] = (flags >> 24) & 0xff;\n    header[index + 2] = (flags >> 16) & 0xff;\n    header[index + 1] = (flags >> 8) & 0xff;\n    header[index] = flags & 0xff;\n    index = index + 4;\n\n    // Write collection name\n    index = index + header.write(this.ns, index, 'utf8') + 1;\n    header[index - 1] = 0;\n\n    // Write header information flags numberToSkip\n    header[index + 3] = (this.numberToSkip >> 24) & 0xff;\n    header[index + 2] = (this.numberToSkip >> 16) & 0xff;\n    header[index + 1] = (this.numberToSkip >> 8) & 0xff;\n    header[index] = this.numberToSkip & 0xff;\n    index = index + 4;\n\n    // Write header information flags numberToReturn\n    header[index + 3] = (this.numberToReturn >> 24) & 0xff;\n    header[index + 2] = (this.numberToReturn >> 16) & 0xff;\n    header[index + 1] = (this.numberToReturn >> 8) & 0xff;\n    header[index] = this.numberToReturn & 0xff;\n    index = index + 4;\n\n    // Return the buffers\n    return buffers;\n  }\n}\n\n/** @internal */\nexport interface MessageHeader {\n  length: number;\n  requestId: number;\n  responseTo: number;\n  opCode: number;\n  fromCompressed?: boolean;\n}\n\n/** @internal */\nexport class OpReply {\n  parsed: boolean;\n  raw: Buffer;\n  data: Buffer;\n  opts: BSONSerializeOptions;\n  length: number;\n  requestId: number;\n  responseTo: number;\n  opCode: number;\n  fromCompressed?: boolean;\n  responseFlags?: number;\n  cursorId?: Long;\n  startingFrom?: number;\n  numberReturned?: number;\n  cursorNotFound?: boolean;\n  queryFailure?: boolean;\n  shardConfigStale?: boolean;\n  awaitCapable?: boolean;\n  useBigInt64: boolean;\n  promoteLongs: boolean;\n  promoteValues: boolean;\n  promoteBuffers: boolean;\n  bsonRegExp?: boolean;\n  index = 0;\n  sections: Uint8Array[] = [];\n\n  /** moreToCome is an OP_MSG only concept */\n  moreToCome = false;\n\n  constructor(\n    message: Buffer,\n    msgHeader: MessageHeader,\n    msgBody: Buffer,\n    opts?: BSONSerializeOptions\n  ) {\n    this.parsed = false;\n    this.raw = message;\n    this.data = msgBody;\n    this.opts = opts ?? {\n      useBigInt64: false,\n      promoteLongs: true,\n      promoteValues: true,\n      promoteBuffers: false,\n      bsonRegExp: false\n    };\n\n    // Read the message header\n    this.length = msgHeader.length;\n    this.requestId = msgHeader.requestId;\n    this.responseTo = msgHeader.responseTo;\n    this.opCode = msgHeader.opCode;\n    this.fromCompressed = msgHeader.fromCompressed;\n\n    // Flag values\n    this.useBigInt64 = typeof this.opts.useBigInt64 === 'boolean' ? this.opts.useBigInt64 : false;\n    this.promoteLongs = typeof this.opts.promoteLongs === 'boolean' ? this.opts.promoteLongs : true;\n    this.promoteValues =\n      typeof this.opts.promoteValues === 'boolean' ? this.opts.promoteValues : true;\n    this.promoteBuffers =\n      typeof this.opts.promoteBuffers === 'boolean' ? this.opts.promoteBuffers : false;\n    this.bsonRegExp = typeof this.opts.bsonRegExp === 'boolean' ? this.opts.bsonRegExp : false;\n  }\n\n  isParsed(): boolean {\n    return this.parsed;\n  }\n\n  parse(): Uint8Array {\n    // Don't parse again if not needed\n    if (this.parsed) return this.sections[0];\n\n    // Position within OP_REPLY at which documents start\n    // (See https://www.mongodb.com/docs/manual/reference/mongodb-wire-protocol/#wire-op-reply)\n    this.index = 20;\n\n    // Read the message body\n    this.responseFlags = this.data.readInt32LE(0);\n    this.cursorId = new BSON.Long(this.data.readInt32LE(4), this.data.readInt32LE(8));\n    this.startingFrom = this.data.readInt32LE(12);\n    this.numberReturned = this.data.readInt32LE(16);\n\n    if (this.numberReturned < 0 || this.numberReturned > 2 ** 32 - 1) {\n      throw new RangeError(\n        `OP_REPLY numberReturned is an invalid array length ${this.numberReturned}`\n      );\n    }\n\n    this.cursorNotFound = (this.responseFlags & CURSOR_NOT_FOUND) !== 0;\n    this.queryFailure = (this.responseFlags & QUERY_FAILURE) !== 0;\n    this.shardConfigStale = (this.responseFlags & SHARD_CONFIG_STALE) !== 0;\n    this.awaitCapable = (this.responseFlags & AWAIT_CAPABLE) !== 0;\n\n    // Parse Body\n    for (let i = 0; i < this.numberReturned; i++) {\n      const bsonSize =\n        this.data[this.index] |\n        (this.data[this.index + 1] << 8) |\n        (this.data[this.index + 2] << 16) |\n        (this.data[this.index + 3] << 24);\n\n      const section = this.data.subarray(this.index, this.index + bsonSize);\n      this.sections.push(section);\n\n      // Adjust the index\n      this.index = this.index + bsonSize;\n    }\n\n    // Set parsed\n    this.parsed = true;\n\n    return this.sections[0];\n  }\n}\n\n// Msg Flags\nconst OPTS_CHECKSUM_PRESENT = 1;\nconst OPTS_MORE_TO_COME = 2;\nconst OPTS_EXHAUST_ALLOWED = 1 << 16;\n\n/** @internal */\nexport interface OpMsgOptions {\n  socketTimeoutMS?: number;\n  session?: ClientSession;\n  numberToSkip?: number;\n  numberToReturn?: number;\n  returnFieldSelector?: Document;\n  pre32Limit?: number;\n  serializeFunctions?: boolean;\n  ignoreUndefined?: boolean;\n  maxBsonSize?: number;\n  checkKeys?: boolean;\n  secondaryOk?: boolean;\n\n  requestId?: number;\n  moreToCome?: boolean;\n  exhaustAllowed?: boolean;\n  readPreference: ReadPreference;\n}\n\n/** @internal */\nexport class DocumentSequence {\n  field: string;\n  documents: Document[];\n  serializedDocumentsLength: number;\n  private chunks: Uint8Array[];\n  private header: Buffer;\n\n  /**\n   * Create a new document sequence for the provided field.\n   * @param field - The field it will replace.\n   */\n  constructor(field: string, documents?: Document[]) {\n    this.field = field;\n    this.documents = [];\n    this.chunks = [];\n    this.serializedDocumentsLength = 0;\n    // Document sequences starts with type 1 at the first byte.\n    // Field strings must always be UTF-8.\n    const buffer = Buffer.allocUnsafe(1 + 4 + this.field.length + 1);\n    buffer[0] = 1;\n    // Third part is the field name at offset 5 with trailing null byte.\n    encodeUTF8Into(buffer, `${this.field}\\0`, 5);\n    this.chunks.push(buffer);\n    this.header = buffer;\n    if (documents) {\n      for (const doc of documents) {\n        this.push(doc, BSON.serialize(doc));\n      }\n    }\n  }\n\n  /**\n   * Push a document to the document sequence. Will serialize the document\n   * as well and return the current serialized length of all documents.\n   * @param document - The document to add.\n   * @param buffer - The serialized document in raw BSON.\n   * @returns The new total document sequence length.\n   */\n  push(document: Document, buffer: Uint8Array): number {\n    this.serializedDocumentsLength += buffer.length;\n    // Push the document.\n    this.documents.push(document);\n    // Push the document raw bson.\n    this.chunks.push(buffer);\n    // Write the new length.\n    this.header?.writeInt32LE(4 + this.field.length + 1 + this.serializedDocumentsLength, 1);\n    return this.serializedDocumentsLength + this.header.length;\n  }\n\n  /**\n   * Get the fully serialized bytes for the document sequence section.\n   * @returns The section bytes.\n   */\n  toBin(): Uint8Array {\n    return Buffer.concat(this.chunks);\n  }\n}\n\n/** @internal */\nexport class OpMsgRequest {\n  requestId: number;\n  serializeFunctions: boolean;\n  ignoreUndefined: boolean;\n  checkKeys: boolean;\n  maxBsonSize: number;\n  checksumPresent: boolean;\n  moreToCome: boolean;\n  exhaustAllowed: boolean;\n\n  constructor(\n    public databaseName: string,\n    public command: Document,\n    public options: OpQueryOptions\n  ) {\n    // Basic options needed to be passed in\n    if (command == null)\n      throw new MongoInvalidArgumentError('Query document must be specified for query');\n\n    // Basic options\n    this.command.$db = databaseName;\n\n    // Ensure empty options\n    this.options = options ?? {};\n\n    // Additional options\n    this.requestId = options.requestId ? options.requestId : OpMsgRequest.getRequestId();\n\n    // Serialization option\n    this.serializeFunctions =\n      typeof options.serializeFunctions === 'boolean' ? options.serializeFunctions : false;\n    this.ignoreUndefined =\n      typeof options.ignoreUndefined === 'boolean' ? options.ignoreUndefined : false;\n    this.checkKeys = typeof options.checkKeys === 'boolean' ? options.checkKeys : false;\n    this.maxBsonSize = options.maxBsonSize || 1024 * 1024 * 16;\n\n    // flags\n    this.checksumPresent = false;\n    this.moreToCome = options.moreToCome ?? command.writeConcern?.w === 0;\n    this.exhaustAllowed =\n      typeof options.exhaustAllowed === 'boolean' ? options.exhaustAllowed : false;\n  }\n\n  toBin(): Buffer[] {\n    const buffers: Buffer[] = [];\n    let flags = 0;\n\n    if (this.checksumPresent) {\n      flags |= OPTS_CHECKSUM_PRESENT;\n    }\n\n    if (this.moreToCome) {\n      flags |= OPTS_MORE_TO_COME;\n    }\n\n    if (this.exhaustAllowed) {\n      flags |= OPTS_EXHAUST_ALLOWED;\n    }\n\n    const header = Buffer.alloc(\n      4 * 4 + // Header\n        4 // Flags\n    );\n\n    buffers.push(header);\n\n    let totalLength = header.length;\n    const command = this.command;\n    totalLength += this.makeSections(buffers, command);\n\n    header.writeInt32LE(totalLength, 0); // messageLength\n    header.writeInt32LE(this.requestId, 4); // requestID\n    header.writeInt32LE(0, 8); // responseTo\n    header.writeInt32LE(OP_MSG, 12); // opCode\n    header.writeUInt32LE(flags, 16); // flags\n    return buffers;\n  }\n\n  /**\n   * Add the sections to the OP_MSG request's buffers and returns the length.\n   */\n  makeSections(buffers: Uint8Array[], document: Document): number {\n    const sequencesBuffer = this.extractDocumentSequences(document);\n    const payloadTypeBuffer = Buffer.allocUnsafe(1);\n    payloadTypeBuffer[0] = 0;\n\n    const documentBuffer = this.serializeBson(document);\n    // First section, type 0\n    buffers.push(payloadTypeBuffer);\n    buffers.push(documentBuffer);\n    // Subsequent sections, type 1\n    buffers.push(sequencesBuffer);\n\n    return payloadTypeBuffer.length + documentBuffer.length + sequencesBuffer.length;\n  }\n\n  /**\n   * Extracts the document sequences from the command document and returns\n   * a buffer to be added as multiple sections after the initial type 0\n   * section in the message.\n   */\n  extractDocumentSequences(document: Document): Uint8Array {\n    // Pull out any field in the command document that's value is a document sequence.\n    const chunks = [];\n    for (const [key, value] of Object.entries(document)) {\n      if (value instanceof DocumentSequence) {\n        chunks.push(value.toBin());\n        // Why are we removing the field from the command? This is because it needs to be\n        // removed in the OP_MSG request first section, and DocumentSequence is not a\n        // BSON type and is specific to the MongoDB wire protocol so there's nothing\n        // our BSON serializer can do about this. Since DocumentSequence is not exposed\n        // in the public API and only used internally, we are never mutating an original\n        // command provided by the user, just our own, and it's cheaper to delete from\n        // our own command than copying it.\n        delete document[key];\n      }\n    }\n    if (chunks.length > 0) {\n      return Buffer.concat(chunks);\n    }\n    // If we have no document sequences we return an empty buffer for nothing to add\n    // to the payload.\n    return Buffer.alloc(0);\n  }\n\n  serializeBson(document: Document): Uint8Array {\n    return BSON.serialize(document, {\n      checkKeys: this.checkKeys,\n      serializeFunctions: this.serializeFunctions,\n      ignoreUndefined: this.ignoreUndefined\n    });\n  }\n\n  static getRequestId(): number {\n    _requestId = (_requestId + 1) & 0x7fffffff;\n    return _requestId;\n  }\n}\n\n/** @internal */\nexport class OpMsgResponse {\n  parsed: boolean;\n  raw: Buffer;\n  data: Buffer;\n  opts: BSONSerializeOptions;\n  length: number;\n  requestId: number;\n  responseTo: number;\n  opCode: number;\n  fromCompressed?: boolean;\n  responseFlags: number;\n  checksumPresent: boolean;\n  /** Indicates the server will be sending more responses on this connection */\n  moreToCome: boolean;\n  exhaustAllowed: boolean;\n  useBigInt64: boolean;\n  promoteLongs: boolean;\n  promoteValues: boolean;\n  promoteBuffers: boolean;\n  bsonRegExp: boolean;\n  index = 0;\n  sections: Uint8Array[] = [];\n\n  constructor(\n    message: Buffer,\n    msgHeader: MessageHeader,\n    msgBody: Buffer,\n    opts?: BSONSerializeOptions\n  ) {\n    this.parsed = false;\n    this.raw = message;\n    this.data = msgBody;\n    this.opts = opts ?? {\n      useBigInt64: false,\n      promoteLongs: true,\n      promoteValues: true,\n      promoteBuffers: false,\n      bsonRegExp: false\n    };\n\n    // Read the message header\n    this.length = msgHeader.length;\n    this.requestId = msgHeader.requestId;\n    this.responseTo = msgHeader.responseTo;\n    this.opCode = msgHeader.opCode;\n    this.fromCompressed = msgHeader.fromCompressed;\n\n    // Read response flags\n    this.responseFlags = msgBody.readInt32LE(0);\n    this.checksumPresent = (this.responseFlags & OPTS_CHECKSUM_PRESENT) !== 0;\n    this.moreToCome = (this.responseFlags & OPTS_MORE_TO_COME) !== 0;\n    this.exhaustAllowed = (this.responseFlags & OPTS_EXHAUST_ALLOWED) !== 0;\n    this.useBigInt64 = typeof this.opts.useBigInt64 === 'boolean' ? this.opts.useBigInt64 : false;\n    this.promoteLongs = typeof this.opts.promoteLongs === 'boolean' ? this.opts.promoteLongs : true;\n    this.promoteValues =\n      typeof this.opts.promoteValues === 'boolean' ? this.opts.promoteValues : true;\n    this.promoteBuffers =\n      typeof this.opts.promoteBuffers === 'boolean' ? this.opts.promoteBuffers : false;\n    this.bsonRegExp = typeof this.opts.bsonRegExp === 'boolean' ? this.opts.bsonRegExp : false;\n  }\n\n  isParsed(): boolean {\n    return this.parsed;\n  }\n\n  parse(): Uint8Array {\n    // Don't parse again if not needed\n    if (this.parsed) return this.sections[0];\n\n    this.index = 4;\n\n    while (this.index < this.data.length) {\n      const payloadType = this.data.readUInt8(this.index++);\n      if (payloadType === 0) {\n        const bsonSize = this.data.readUInt32LE(this.index);\n        const bin = this.data.subarray(this.index, this.index + bsonSize);\n\n        this.sections.push(bin);\n\n        this.index += bsonSize;\n      } else if (payloadType === 1) {\n        // It was decided that no driver makes use of payload type 1\n\n        // TODO(NODE-3483): Replace with MongoDeprecationError\n        throw new MongoRuntimeError('OP_MSG Payload Type 1 detected unsupported protocol');\n      }\n    }\n\n    this.parsed = true;\n\n    return this.sections[0];\n  }\n}\n\nconst MESSAGE_HEADER_SIZE = 16;\nconst COMPRESSION_DETAILS_SIZE = 9; // originalOpcode + uncompressedSize, compressorID\n\n/**\n * @internal\n *\n * An OP_COMPRESSED request wraps either an OP_QUERY or OP_MSG message.\n */\nexport class OpCompressedRequest {\n  constructor(\n    private command: WriteProtocolMessageType,\n    private options: { zlibCompressionLevel: number; agreedCompressor: CompressorName }\n  ) {}\n\n  // Return whether a command contains an uncompressible command term\n  // Will return true if command contains no uncompressible command terms\n  static canCompress(command: WriteProtocolMessageType) {\n    const commandDoc = command instanceof OpMsgRequest ? command.command : command.query;\n    const commandName = Object.keys(commandDoc)[0];\n    return !uncompressibleCommands.has(commandName);\n  }\n\n  async toBin(): Promise<Buffer[]> {\n    const concatenatedOriginalCommandBuffer = Buffer.concat(this.command.toBin());\n    // otherwise, compress the message\n    const messageToBeCompressed = concatenatedOriginalCommandBuffer.slice(MESSAGE_HEADER_SIZE);\n\n    // Extract information needed for OP_COMPRESSED from the uncompressed message\n    const originalCommandOpCode = concatenatedOriginalCommandBuffer.readInt32LE(12);\n\n    // Compress the message body\n    const compressedMessage = await compress(this.options, messageToBeCompressed);\n    // Create the msgHeader of OP_COMPRESSED\n    const msgHeader = Buffer.alloc(MESSAGE_HEADER_SIZE);\n    msgHeader.writeInt32LE(\n      MESSAGE_HEADER_SIZE + COMPRESSION_DETAILS_SIZE + compressedMessage.length,\n      0\n    ); // messageLength\n    msgHeader.writeInt32LE(this.command.requestId, 4); // requestID\n    msgHeader.writeInt32LE(0, 8); // responseTo (zero)\n    msgHeader.writeInt32LE(OP_COMPRESSED, 12); // opCode\n\n    // Create the compression details of OP_COMPRESSED\n    const compressionDetails = Buffer.alloc(COMPRESSION_DETAILS_SIZE);\n    compressionDetails.writeInt32LE(originalCommandOpCode, 0); // originalOpcode\n    compressionDetails.writeInt32LE(messageToBeCompressed.length, 4); // Size of the uncompressed compressedMessage, excluding the MsgHeader\n    compressionDetails.writeUInt8(Compressor[this.options.agreedCompressor], 8); // compressorID\n    return [msgHeader, compressionDetails, compressedMessage];\n  }\n}\n"], "mappings": ";;;;;;AACA,MAAAA,IAAA,GAAAC,OAAA;AACA,MAAAC,OAAA,GAAAD,OAAA;AAIA,MAAAE,aAAA,GAAAF,OAAA;AAMA,MAAAG,WAAA,GAAAH,OAAA;AAEA;AACA,IAAII,UAAU,GAAG,CAAC;AAElB;AACA,MAAMC,oBAAoB,GAAG,CAAC;AAC9B,MAAMC,cAAc,GAAG,CAAC;AACxB,MAAMC,iBAAiB,GAAG,CAAC;AAC3B,MAAMC,sBAAsB,GAAG,EAAE;AACjC,MAAMC,eAAe,GAAG,EAAE;AAC1B,MAAMC,YAAY,GAAG,EAAE;AACvB,MAAMC,YAAY,GAAG,GAAG;AAExB;AACA,MAAMC,gBAAgB,GAAG,CAAC;AAC1B,MAAMC,aAAa,GAAG,CAAC;AACvB,MAAMC,kBAAkB,GAAG,CAAC;AAC5B,MAAMC,aAAa,GAAG,CAAC;AAEvB,MAAMC,cAAc,GAAGjB,IAAI,CAACA,IAAI,CAACkB,QAAQ,CAACC,SAAS,CAACF,cAAc;AAwBlE;AACA,MAAaG,cAAc;EAsBzBC,YACSC,YAAoB,EACpBC,KAAe,EACtBC,OAAuB;IAFhB,KAAAF,YAAY,GAAZA,YAAY;IACZ,KAAAC,KAAK,GAALA,KAAK;IALd;IACA,KAAAE,UAAU,GAAG,KAAK;IAOhB;IACA;IACA,MAAMC,EAAE,GAAG,GAAGJ,YAAY,OAAO;IACjC,IAAI,OAAOA,YAAY,KAAK,QAAQ,EAAE;MACpC,MAAM,IAAIpB,OAAA,CAAAyB,iBAAiB,CAAC,4CAA4C,CAAC;IAC3E;IACA;IACA,IAAIJ,KAAK,IAAI,IAAI,EAAE,MAAM,IAAIrB,OAAA,CAAAyB,iBAAiB,CAAC,8CAA8C,CAAC;IAE9F;IACA,IAAID,EAAE,CAACE,OAAO,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC,EAAE;MAC7B;MACA,MAAM,IAAI1B,OAAA,CAAAyB,iBAAiB,CAAC,2CAA2C,CAAC;IAC1E;IAEA;IACA,IAAI,CAACD,EAAE,GAAGA,EAAE;IAEZ;IACA,IAAI,CAACG,YAAY,GAAGL,OAAO,CAACK,YAAY,IAAI,CAAC;IAC7C,IAAI,CAACC,cAAc,GAAGN,OAAO,CAACM,cAAc,IAAI,CAAC;IACjD,IAAI,CAACC,mBAAmB,GAAGP,OAAO,CAACO,mBAAmB,IAAIC,SAAS;IACnE,IAAI,CAACC,SAAS,GAAGT,OAAO,CAACS,SAAS,IAAIb,cAAc,CAACc,YAAY,EAAE;IAEnE;IACA,IAAI,CAACC,UAAU,GAAGX,OAAO,CAACW,UAAU;IAEpC;IACA,IAAI,CAACC,kBAAkB,GACrB,OAAOZ,OAAO,CAACY,kBAAkB,KAAK,SAAS,GAAGZ,OAAO,CAACY,kBAAkB,GAAG,KAAK;IACtF,IAAI,CAACC,eAAe,GAClB,OAAOb,OAAO,CAACa,eAAe,KAAK,SAAS,GAAGb,OAAO,CAACa,eAAe,GAAG,KAAK;IAChF,IAAI,CAACC,WAAW,GAAGd,OAAO,CAACc,WAAW,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE;IAC1D,IAAI,CAACC,SAAS,GAAG,OAAOf,OAAO,CAACe,SAAS,KAAK,SAAS,GAAGf,OAAO,CAACe,SAAS,GAAG,KAAK;IACnF,IAAI,CAACC,SAAS,GAAG,IAAI,CAACV,cAAc;IAEpC;IACA,IAAI,CAACW,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,WAAW,GAAG,OAAOlB,OAAO,CAACkB,WAAW,KAAK,SAAS,GAAGlB,OAAO,CAACkB,WAAW,GAAG,KAAK;IACzF,IAAI,CAACC,WAAW,GAAG,KAAK;IACxB,IAAI,CAACC,eAAe,GAAG,KAAK;IAC5B,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,OAAO,GAAG,KAAK;IACpB,IAAI,CAACC,OAAO,GAAG,KAAK;EACtB;EAEA;EACAC,YAAYA,CAAA;IACV,IAAI,CAACf,SAAS,GAAG5B,UAAU,EAAE;EAC/B;EAEA;EACA4C,aAAaA,CAAA;IACX,OAAO5C,UAAU,GAAG,CAAC;EACvB;EAEA;EACA,OAAO6B,YAAYA,CAAA;IACjB,OAAO,EAAE7B,UAAU;EACrB;EAEA;EACA6C,KAAKA,CAAA;IACH,MAAMC,OAAO,GAAG,EAAE;IAClB,IAAIC,UAAU,GAAG,IAAI;IAErB;IACA,IAAIC,KAAK,GAAG,CAAC;IACb,IAAI,IAAI,CAACZ,QAAQ,EAAE;MACjBY,KAAK,IAAI/C,oBAAoB;IAC/B;IAEA,IAAI,IAAI,CAACoC,WAAW,EAAE;MACpBW,KAAK,IAAI9C,cAAc;IACzB;IAEA,IAAI,IAAI,CAACoC,WAAW,EAAE;MACpBU,KAAK,IAAI7C,iBAAiB;IAC5B;IAEA,IAAI,IAAI,CAACoC,eAAe,EAAE;MACxBS,KAAK,IAAI5C,sBAAsB;IACjC;IAEA,IAAI,IAAI,CAACoC,SAAS,EAAE;MAClBQ,KAAK,IAAI3C,eAAe;IAC1B;IAEA,IAAI,IAAI,CAACoC,OAAO,EAAE;MAChBO,KAAK,IAAI1C,YAAY;IACvB;IAEA,IAAI,IAAI,CAACoC,OAAO,EAAE;MAChBM,KAAK,IAAIzC,YAAY;IACvB;IAEA;IACA,IAAI,IAAI,CAAC4B,SAAS,KAAK,IAAI,CAACV,cAAc,EAAE,IAAI,CAACA,cAAc,GAAG,IAAI,CAACU,SAAS;IAEhF;IACA,MAAMc,MAAM,GAAGC,MAAM,CAACC,KAAK,CACzB,CAAC,GAAG,CAAC;IAAG;IACN,CAAC;IAAG;IACJD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC/B,EAAE,CAAC,GAC1B,CAAC;IAAG;IACJ,CAAC;IAAG;IACJ,CAAC,CAAC;KACL;IAED;IACAyB,OAAO,CAACO,IAAI,CAACJ,MAAM,CAAC;IAEpB;IACA,MAAM/B,KAAK,GAAGvB,IAAI,CAAC2D,SAAS,CAAC,IAAI,CAACpC,KAAK,EAAE;MACvCgB,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBH,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;MAC3CC,eAAe,EAAE,IAAI,CAACA;KACvB,CAAC;IAEF;IACAc,OAAO,CAACO,IAAI,CAACnC,KAAK,CAAC;IAEnB,IAAI,IAAI,CAACQ,mBAAmB,IAAI6B,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC9B,mBAAmB,CAAC,CAAC+B,MAAM,GAAG,CAAC,EAAE;MAChF;MACAV,UAAU,GAAGpD,IAAI,CAAC2D,SAAS,CAAC,IAAI,CAAC5B,mBAAmB,EAAE;QACpDQ,SAAS,EAAE,IAAI,CAACA,SAAS;QACzBH,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;QAC3CC,eAAe,EAAE,IAAI,CAACA;OACvB,CAAC;MACF;MACAc,OAAO,CAACO,IAAI,CAACN,UAAU,CAAC;IAC1B;IAEA;IACA,MAAMW,WAAW,GAAGT,MAAM,CAACQ,MAAM,GAAGvC,KAAK,CAACuC,MAAM,IAAIV,UAAU,GAAGA,UAAU,CAACU,MAAM,GAAG,CAAC,CAAC;IAEvF;IACA,IAAIE,KAAK,GAAG,CAAC;IAEb;IACAV,MAAM,CAAC,CAAC,CAAC,GAAIS,WAAW,IAAI,EAAE,GAAI,IAAI;IACtCT,MAAM,CAAC,CAAC,CAAC,GAAIS,WAAW,IAAI,EAAE,GAAI,IAAI;IACtCT,MAAM,CAAC,CAAC,CAAC,GAAIS,WAAW,IAAI,CAAC,GAAI,IAAI;IACrCT,MAAM,CAAC,CAAC,CAAC,GAAGS,WAAW,GAAG,IAAI;IAE9B;IACAT,MAAM,CAACU,KAAK,GAAG,CAAC,CAAC,GAAI,IAAI,CAAC/B,SAAS,IAAI,EAAE,GAAI,IAAI;IACjDqB,MAAM,CAACU,KAAK,GAAG,CAAC,CAAC,GAAI,IAAI,CAAC/B,SAAS,IAAI,EAAE,GAAI,IAAI;IACjDqB,MAAM,CAACU,KAAK,GAAG,CAAC,CAAC,GAAI,IAAI,CAAC/B,SAAS,IAAI,CAAC,GAAI,IAAI;IAChDqB,MAAM,CAACU,KAAK,CAAC,GAAG,IAAI,CAAC/B,SAAS,GAAG,IAAI;IACrC+B,KAAK,GAAGA,KAAK,GAAG,CAAC;IAEjB;IACAV,MAAM,CAACU,KAAK,GAAG,CAAC,CAAC,GAAI,CAAC,IAAI,EAAE,GAAI,IAAI;IACpCV,MAAM,CAACU,KAAK,GAAG,CAAC,CAAC,GAAI,CAAC,IAAI,EAAE,GAAI,IAAI;IACpCV,MAAM,CAACU,KAAK,GAAG,CAAC,CAAC,GAAI,CAAC,IAAI,CAAC,GAAI,IAAI;IACnCV,MAAM,CAACU,KAAK,CAAC,GAAG,CAAC,GAAG,IAAI;IACxBA,KAAK,GAAGA,KAAK,GAAG,CAAC;IAEjB;IACAV,MAAM,CAACU,KAAK,GAAG,CAAC,CAAC,GAAI5D,WAAA,CAAA6D,QAAQ,IAAI,EAAE,GAAI,IAAI;IAC3CX,MAAM,CAACU,KAAK,GAAG,CAAC,CAAC,GAAI5D,WAAA,CAAA6D,QAAQ,IAAI,EAAE,GAAI,IAAI;IAC3CX,MAAM,CAACU,KAAK,GAAG,CAAC,CAAC,GAAI5D,WAAA,CAAA6D,QAAQ,IAAI,CAAC,GAAI,IAAI;IAC1CX,MAAM,CAACU,KAAK,CAAC,GAAG5D,WAAA,CAAA6D,QAAQ,GAAG,IAAI;IAC/BD,KAAK,GAAGA,KAAK,GAAG,CAAC;IAEjB;IACAV,MAAM,CAACU,KAAK,GAAG,CAAC,CAAC,GAAIX,KAAK,IAAI,EAAE,GAAI,IAAI;IACxCC,MAAM,CAACU,KAAK,GAAG,CAAC,CAAC,GAAIX,KAAK,IAAI,EAAE,GAAI,IAAI;IACxCC,MAAM,CAACU,KAAK,GAAG,CAAC,CAAC,GAAIX,KAAK,IAAI,CAAC,GAAI,IAAI;IACvCC,MAAM,CAACU,KAAK,CAAC,GAAGX,KAAK,GAAG,IAAI;IAC5BW,KAAK,GAAGA,KAAK,GAAG,CAAC;IAEjB;IACAA,KAAK,GAAGA,KAAK,GAAGV,MAAM,CAACY,KAAK,CAAC,IAAI,CAACxC,EAAE,EAAEsC,KAAK,EAAE,MAAM,CAAC,GAAG,CAAC;IACxDV,MAAM,CAACU,KAAK,GAAG,CAAC,CAAC,GAAG,CAAC;IAErB;IACAV,MAAM,CAACU,KAAK,GAAG,CAAC,CAAC,GAAI,IAAI,CAACnC,YAAY,IAAI,EAAE,GAAI,IAAI;IACpDyB,MAAM,CAACU,KAAK,GAAG,CAAC,CAAC,GAAI,IAAI,CAACnC,YAAY,IAAI,EAAE,GAAI,IAAI;IACpDyB,MAAM,CAACU,KAAK,GAAG,CAAC,CAAC,GAAI,IAAI,CAACnC,YAAY,IAAI,CAAC,GAAI,IAAI;IACnDyB,MAAM,CAACU,KAAK,CAAC,GAAG,IAAI,CAACnC,YAAY,GAAG,IAAI;IACxCmC,KAAK,GAAGA,KAAK,GAAG,CAAC;IAEjB;IACAV,MAAM,CAACU,KAAK,GAAG,CAAC,CAAC,GAAI,IAAI,CAAClC,cAAc,IAAI,EAAE,GAAI,IAAI;IACtDwB,MAAM,CAACU,KAAK,GAAG,CAAC,CAAC,GAAI,IAAI,CAAClC,cAAc,IAAI,EAAE,GAAI,IAAI;IACtDwB,MAAM,CAACU,KAAK,GAAG,CAAC,CAAC,GAAI,IAAI,CAAClC,cAAc,IAAI,CAAC,GAAI,IAAI;IACrDwB,MAAM,CAACU,KAAK,CAAC,GAAG,IAAI,CAAClC,cAAc,GAAG,IAAI;IAC1CkC,KAAK,GAAGA,KAAK,GAAG,CAAC;IAEjB;IACA,OAAOb,OAAO;EAChB;;AA5NFgB,OAAA,CAAA/C,cAAA,GAAAA,cAAA;AAwOA;AACA,MAAagD,OAAO;EA6BlB/C,YACEgD,OAAe,EACfC,SAAwB,EACxBC,OAAe,EACfC,IAA2B;IAV7B,KAAAR,KAAK,GAAG,CAAC;IACT,KAAAS,QAAQ,GAAiB,EAAE;IAE3B;IACA,KAAAhD,UAAU,GAAG,KAAK;IAQhB,IAAI,CAACiD,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,GAAG,GAAGN,OAAO;IAClB,IAAI,CAACO,IAAI,GAAGL,OAAO;IACnB,IAAI,CAACC,IAAI,GAAGA,IAAI,IAAI;MAClBK,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,aAAa,EAAE,IAAI;MACnBC,cAAc,EAAE,KAAK;MACrBC,UAAU,EAAE;KACb;IAED;IACA,IAAI,CAACnB,MAAM,GAAGQ,SAAS,CAACR,MAAM;IAC9B,IAAI,CAAC7B,SAAS,GAAGqC,SAAS,CAACrC,SAAS;IACpC,IAAI,CAACiD,UAAU,GAAGZ,SAAS,CAACY,UAAU;IACtC,IAAI,CAACC,MAAM,GAAGb,SAAS,CAACa,MAAM;IAC9B,IAAI,CAACC,cAAc,GAAGd,SAAS,CAACc,cAAc;IAE9C;IACA,IAAI,CAACP,WAAW,GAAG,OAAO,IAAI,CAACL,IAAI,CAACK,WAAW,KAAK,SAAS,GAAG,IAAI,CAACL,IAAI,CAACK,WAAW,GAAG,KAAK;IAC7F,IAAI,CAACC,YAAY,GAAG,OAAO,IAAI,CAACN,IAAI,CAACM,YAAY,KAAK,SAAS,GAAG,IAAI,CAACN,IAAI,CAACM,YAAY,GAAG,IAAI;IAC/F,IAAI,CAACC,aAAa,GAChB,OAAO,IAAI,CAACP,IAAI,CAACO,aAAa,KAAK,SAAS,GAAG,IAAI,CAACP,IAAI,CAACO,aAAa,GAAG,IAAI;IAC/E,IAAI,CAACC,cAAc,GACjB,OAAO,IAAI,CAACR,IAAI,CAACQ,cAAc,KAAK,SAAS,GAAG,IAAI,CAACR,IAAI,CAACQ,cAAc,GAAG,KAAK;IAClF,IAAI,CAACC,UAAU,GAAG,OAAO,IAAI,CAACT,IAAI,CAACS,UAAU,KAAK,SAAS,GAAG,IAAI,CAACT,IAAI,CAACS,UAAU,GAAG,KAAK;EAC5F;EAEAI,QAAQA,CAAA;IACN,OAAO,IAAI,CAACX,MAAM;EACpB;EAEAY,KAAKA,CAAA;IACH;IACA,IAAI,IAAI,CAACZ,MAAM,EAAE,OAAO,IAAI,CAACD,QAAQ,CAAC,CAAC,CAAC;IAExC;IACA;IACA,IAAI,CAACT,KAAK,GAAG,EAAE;IAEf;IACA,IAAI,CAACuB,aAAa,GAAG,IAAI,CAACX,IAAI,CAACY,WAAW,CAAC,CAAC,CAAC;IAC7C,IAAI,CAACC,QAAQ,GAAG,IAAIzF,IAAI,CAAC0F,IAAI,CAAC,IAAI,CAACd,IAAI,CAACY,WAAW,CAAC,CAAC,CAAC,EAAE,IAAI,CAACZ,IAAI,CAACY,WAAW,CAAC,CAAC,CAAC,CAAC;IACjF,IAAI,CAACG,YAAY,GAAG,IAAI,CAACf,IAAI,CAACY,WAAW,CAAC,EAAE,CAAC;IAC7C,IAAI,CAACI,cAAc,GAAG,IAAI,CAAChB,IAAI,CAACY,WAAW,CAAC,EAAE,CAAC;IAE/C,IAAI,IAAI,CAACI,cAAc,GAAG,CAAC,IAAI,IAAI,CAACA,cAAc,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;MAChE,MAAM,IAAIC,UAAU,CAClB,sDAAsD,IAAI,CAACD,cAAc,EAAE,CAC5E;IACH;IAEA,IAAI,CAACE,cAAc,GAAG,CAAC,IAAI,CAACP,aAAa,GAAG1E,gBAAgB,MAAM,CAAC;IACnE,IAAI,CAACkF,YAAY,GAAG,CAAC,IAAI,CAACR,aAAa,GAAGzE,aAAa,MAAM,CAAC;IAC9D,IAAI,CAACkF,gBAAgB,GAAG,CAAC,IAAI,CAACT,aAAa,GAAGxE,kBAAkB,MAAM,CAAC;IACvE,IAAI,CAACkF,YAAY,GAAG,CAAC,IAAI,CAACV,aAAa,GAAGvE,aAAa,MAAM,CAAC;IAE9D;IACA,KAAK,IAAIkF,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACN,cAAc,EAAEM,CAAC,EAAE,EAAE;MAC5C,MAAMC,QAAQ,GACZ,IAAI,CAACvB,IAAI,CAAC,IAAI,CAACZ,KAAK,CAAC,GACpB,IAAI,CAACY,IAAI,CAAC,IAAI,CAACZ,KAAK,GAAG,CAAC,CAAC,IAAI,CAAE,GAC/B,IAAI,CAACY,IAAI,CAAC,IAAI,CAACZ,KAAK,GAAG,CAAC,CAAC,IAAI,EAAG,GAChC,IAAI,CAACY,IAAI,CAAC,IAAI,CAACZ,KAAK,GAAG,CAAC,CAAC,IAAI,EAAG;MAEnC,MAAMoC,OAAO,GAAG,IAAI,CAACxB,IAAI,CAACyB,QAAQ,CAAC,IAAI,CAACrC,KAAK,EAAE,IAAI,CAACA,KAAK,GAAGmC,QAAQ,CAAC;MACrE,IAAI,CAAC1B,QAAQ,CAACf,IAAI,CAAC0C,OAAO,CAAC;MAE3B;MACA,IAAI,CAACpC,KAAK,GAAG,IAAI,CAACA,KAAK,GAAGmC,QAAQ;IACpC;IAEA;IACA,IAAI,CAACzB,MAAM,GAAG,IAAI;IAElB,OAAO,IAAI,CAACD,QAAQ,CAAC,CAAC,CAAC;EACzB;;AA/GFN,OAAA,CAAAC,OAAA,GAAAA,OAAA;AAkHA;AACA,MAAMkC,qBAAqB,GAAG,CAAC;AAC/B,MAAMC,iBAAiB,GAAG,CAAC;AAC3B,MAAMC,oBAAoB,GAAG,CAAC,IAAI,EAAE;AAsBpC;AACA,MAAaC,gBAAgB;EAO3B;;;;EAIApF,YAAYqF,KAAa,EAAEC,SAAsB;IAC/C,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB,IAAI,CAACC,yBAAyB,GAAG,CAAC;IAClC;IACA;IACA,MAAMC,MAAM,GAAGvD,MAAM,CAACwD,WAAW,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAACL,KAAK,CAAC5C,MAAM,GAAG,CAAC,CAAC;IAChEgD,MAAM,CAAC,CAAC,CAAC,GAAG,CAAC;IACb;IACA7F,cAAc,CAAC6F,MAAM,EAAE,GAAG,IAAI,CAACJ,KAAK,IAAI,EAAE,CAAC,CAAC;IAC5C,IAAI,CAACE,MAAM,CAAClD,IAAI,CAACoD,MAAM,CAAC;IACxB,IAAI,CAACxD,MAAM,GAAGwD,MAAM;IACpB,IAAIH,SAAS,EAAE;MACb,KAAK,MAAMK,GAAG,IAAIL,SAAS,EAAE;QAC3B,IAAI,CAACjD,IAAI,CAACsD,GAAG,EAAEhH,IAAI,CAAC2D,SAAS,CAACqD,GAAG,CAAC,CAAC;MACrC;IACF;EACF;EAEA;;;;;;;EAOAtD,IAAIA,CAACuD,QAAkB,EAAEH,MAAkB;IACzC,IAAI,CAACD,yBAAyB,IAAIC,MAAM,CAAChD,MAAM;IAC/C;IACA,IAAI,CAAC6C,SAAS,CAACjD,IAAI,CAACuD,QAAQ,CAAC;IAC7B;IACA,IAAI,CAACL,MAAM,CAAClD,IAAI,CAACoD,MAAM,CAAC;IACxB;IACA,IAAI,CAACxD,MAAM,EAAE4D,YAAY,CAAC,CAAC,GAAG,IAAI,CAACR,KAAK,CAAC5C,MAAM,GAAG,CAAC,GAAG,IAAI,CAAC+C,yBAAyB,EAAE,CAAC,CAAC;IACxF,OAAO,IAAI,CAACA,yBAAyB,GAAG,IAAI,CAACvD,MAAM,CAACQ,MAAM;EAC5D;EAEA;;;;EAIAZ,KAAKA,CAAA;IACH,OAAOK,MAAM,CAAC4D,MAAM,CAAC,IAAI,CAACP,MAAM,CAAC;EACnC;;AAvDFzC,OAAA,CAAAsC,gBAAA,GAAAA,gBAAA;AA0DA;AACA,MAAaW,YAAY;EAUvB/F,YACSC,YAAoB,EACpB+F,OAAiB,EACjB7F,OAAuB;IAFvB,KAAAF,YAAY,GAAZA,YAAY;IACZ,KAAA+F,OAAO,GAAPA,OAAO;IACP,KAAA7F,OAAO,GAAPA,OAAO;IAEd;IACA,IAAI6F,OAAO,IAAI,IAAI,EACjB,MAAM,IAAInH,OAAA,CAAAoH,yBAAyB,CAAC,4CAA4C,CAAC;IAEnF;IACA,IAAI,CAACD,OAAO,CAACE,GAAG,GAAGjG,YAAY;IAE/B;IACA,IAAI,CAACE,OAAO,GAAGA,OAAO,IAAI,EAAE;IAE5B;IACA,IAAI,CAACS,SAAS,GAAGT,OAAO,CAACS,SAAS,GAAGT,OAAO,CAACS,SAAS,GAAGmF,YAAY,CAAClF,YAAY,EAAE;IAEpF;IACA,IAAI,CAACE,kBAAkB,GACrB,OAAOZ,OAAO,CAACY,kBAAkB,KAAK,SAAS,GAAGZ,OAAO,CAACY,kBAAkB,GAAG,KAAK;IACtF,IAAI,CAACC,eAAe,GAClB,OAAOb,OAAO,CAACa,eAAe,KAAK,SAAS,GAAGb,OAAO,CAACa,eAAe,GAAG,KAAK;IAChF,IAAI,CAACE,SAAS,GAAG,OAAOf,OAAO,CAACe,SAAS,KAAK,SAAS,GAAGf,OAAO,CAACe,SAAS,GAAG,KAAK;IACnF,IAAI,CAACD,WAAW,GAAGd,OAAO,CAACc,WAAW,IAAI,IAAI,GAAG,IAAI,GAAG,EAAE;IAE1D;IACA,IAAI,CAACkF,eAAe,GAAG,KAAK;IAC5B,IAAI,CAAC/F,UAAU,GAAGD,OAAO,CAACC,UAAU,IAAI4F,OAAO,CAACI,YAAY,EAAEC,CAAC,KAAK,CAAC;IACrE,IAAI,CAACC,cAAc,GACjB,OAAOnG,OAAO,CAACmG,cAAc,KAAK,SAAS,GAAGnG,OAAO,CAACmG,cAAc,GAAG,KAAK;EAChF;EAEAzE,KAAKA,CAAA;IACH,MAAMC,OAAO,GAAa,EAAE;IAC5B,IAAIE,KAAK,GAAG,CAAC;IAEb,IAAI,IAAI,CAACmE,eAAe,EAAE;MACxBnE,KAAK,IAAIiD,qBAAqB;IAChC;IAEA,IAAI,IAAI,CAAC7E,UAAU,EAAE;MACnB4B,KAAK,IAAIkD,iBAAiB;IAC5B;IAEA,IAAI,IAAI,CAACoB,cAAc,EAAE;MACvBtE,KAAK,IAAImD,oBAAoB;IAC/B;IAEA,MAAMlD,MAAM,GAAGC,MAAM,CAACC,KAAK,CACzB,CAAC,GAAG,CAAC;IAAG;IACN,CAAC,CAAC;KACL;IAEDL,OAAO,CAACO,IAAI,CAACJ,MAAM,CAAC;IAEpB,IAAIS,WAAW,GAAGT,MAAM,CAACQ,MAAM;IAC/B,MAAMuD,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5BtD,WAAW,IAAI,IAAI,CAAC6D,YAAY,CAACzE,OAAO,EAAEkE,OAAO,CAAC;IAElD/D,MAAM,CAAC4D,YAAY,CAACnD,WAAW,EAAE,CAAC,CAAC,CAAC,CAAC;IACrCT,MAAM,CAAC4D,YAAY,CAAC,IAAI,CAACjF,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;IACxCqB,MAAM,CAAC4D,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC3B5D,MAAM,CAAC4D,YAAY,CAAC9G,WAAA,CAAAyH,MAAM,EAAE,EAAE,CAAC,CAAC,CAAC;IACjCvE,MAAM,CAACwE,aAAa,CAACzE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC;IACjC,OAAOF,OAAO;EAChB;EAEA;;;EAGAyE,YAAYA,CAACzE,OAAqB,EAAE8D,QAAkB;IACpD,MAAMc,eAAe,GAAG,IAAI,CAACC,wBAAwB,CAACf,QAAQ,CAAC;IAC/D,MAAMgB,iBAAiB,GAAG1E,MAAM,CAACwD,WAAW,CAAC,CAAC,CAAC;IAC/CkB,iBAAiB,CAAC,CAAC,CAAC,GAAG,CAAC;IAExB,MAAMC,cAAc,GAAG,IAAI,CAACC,aAAa,CAAClB,QAAQ,CAAC;IACnD;IACA9D,OAAO,CAACO,IAAI,CAACuE,iBAAiB,CAAC;IAC/B9E,OAAO,CAACO,IAAI,CAACwE,cAAc,CAAC;IAC5B;IACA/E,OAAO,CAACO,IAAI,CAACqE,eAAe,CAAC;IAE7B,OAAOE,iBAAiB,CAACnE,MAAM,GAAGoE,cAAc,CAACpE,MAAM,GAAGiE,eAAe,CAACjE,MAAM;EAClF;EAEA;;;;;EAKAkE,wBAAwBA,CAACf,QAAkB;IACzC;IACA,MAAML,MAAM,GAAG,EAAE;IACjB,KAAK,MAAM,CAACwB,GAAG,EAAEC,KAAK,CAAC,IAAIzE,MAAM,CAAC0E,OAAO,CAACrB,QAAQ,CAAC,EAAE;MACnD,IAAIoB,KAAK,YAAY5B,gBAAgB,EAAE;QACrCG,MAAM,CAAClD,IAAI,CAAC2E,KAAK,CAACnF,KAAK,EAAE,CAAC;QAC1B;QACA;QACA;QACA;QACA;QACA;QACA;QACA,OAAO+D,QAAQ,CAACmB,GAAG,CAAC;MACtB;IACF;IACA,IAAIxB,MAAM,CAAC9C,MAAM,GAAG,CAAC,EAAE;MACrB,OAAOP,MAAM,CAAC4D,MAAM,CAACP,MAAM,CAAC;IAC9B;IACA;IACA;IACA,OAAOrD,MAAM,CAACC,KAAK,CAAC,CAAC,CAAC;EACxB;EAEA2E,aAAaA,CAAClB,QAAkB;IAC9B,OAAOjH,IAAI,CAAC2D,SAAS,CAACsD,QAAQ,EAAE;MAC9B1E,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBH,kBAAkB,EAAE,IAAI,CAACA,kBAAkB;MAC3CC,eAAe,EAAE,IAAI,CAACA;KACvB,CAAC;EACJ;EAEA,OAAOH,YAAYA,CAAA;IACjB7B,UAAU,GAAIA,UAAU,GAAG,CAAC,GAAI,UAAU;IAC1C,OAAOA,UAAU;EACnB;;AAxIF8D,OAAA,CAAAiD,YAAA,GAAAA,YAAA;AA2IA;AACA,MAAamB,aAAa;EAuBxBlH,YACEgD,OAAe,EACfC,SAAwB,EACxBC,OAAe,EACfC,IAA2B;IAP7B,KAAAR,KAAK,GAAG,CAAC;IACT,KAAAS,QAAQ,GAAiB,EAAE;IAQzB,IAAI,CAACC,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,GAAG,GAAGN,OAAO;IAClB,IAAI,CAACO,IAAI,GAAGL,OAAO;IACnB,IAAI,CAACC,IAAI,GAAGA,IAAI,IAAI;MAClBK,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,aAAa,EAAE,IAAI;MACnBC,cAAc,EAAE,KAAK;MACrBC,UAAU,EAAE;KACb;IAED;IACA,IAAI,CAACnB,MAAM,GAAGQ,SAAS,CAACR,MAAM;IAC9B,IAAI,CAAC7B,SAAS,GAAGqC,SAAS,CAACrC,SAAS;IACpC,IAAI,CAACiD,UAAU,GAAGZ,SAAS,CAACY,UAAU;IACtC,IAAI,CAACC,MAAM,GAAGb,SAAS,CAACa,MAAM;IAC9B,IAAI,CAACC,cAAc,GAAGd,SAAS,CAACc,cAAc;IAE9C;IACA,IAAI,CAACG,aAAa,GAAGhB,OAAO,CAACiB,WAAW,CAAC,CAAC,CAAC;IAC3C,IAAI,CAACgC,eAAe,GAAG,CAAC,IAAI,CAACjC,aAAa,GAAGe,qBAAqB,MAAM,CAAC;IACzE,IAAI,CAAC7E,UAAU,GAAG,CAAC,IAAI,CAAC8D,aAAa,GAAGgB,iBAAiB,MAAM,CAAC;IAChE,IAAI,CAACoB,cAAc,GAAG,CAAC,IAAI,CAACpC,aAAa,GAAGiB,oBAAoB,MAAM,CAAC;IACvE,IAAI,CAAC3B,WAAW,GAAG,OAAO,IAAI,CAACL,IAAI,CAACK,WAAW,KAAK,SAAS,GAAG,IAAI,CAACL,IAAI,CAACK,WAAW,GAAG,KAAK;IAC7F,IAAI,CAACC,YAAY,GAAG,OAAO,IAAI,CAACN,IAAI,CAACM,YAAY,KAAK,SAAS,GAAG,IAAI,CAACN,IAAI,CAACM,YAAY,GAAG,IAAI;IAC/F,IAAI,CAACC,aAAa,GAChB,OAAO,IAAI,CAACP,IAAI,CAACO,aAAa,KAAK,SAAS,GAAG,IAAI,CAACP,IAAI,CAACO,aAAa,GAAG,IAAI;IAC/E,IAAI,CAACC,cAAc,GACjB,OAAO,IAAI,CAACR,IAAI,CAACQ,cAAc,KAAK,SAAS,GAAG,IAAI,CAACR,IAAI,CAACQ,cAAc,GAAG,KAAK;IAClF,IAAI,CAACC,UAAU,GAAG,OAAO,IAAI,CAACT,IAAI,CAACS,UAAU,KAAK,SAAS,GAAG,IAAI,CAACT,IAAI,CAACS,UAAU,GAAG,KAAK;EAC5F;EAEAI,QAAQA,CAAA;IACN,OAAO,IAAI,CAACX,MAAM;EACpB;EAEAY,KAAKA,CAAA;IACH;IACA,IAAI,IAAI,CAACZ,MAAM,EAAE,OAAO,IAAI,CAACD,QAAQ,CAAC,CAAC,CAAC;IAExC,IAAI,CAACT,KAAK,GAAG,CAAC;IAEd,OAAO,IAAI,CAACA,KAAK,GAAG,IAAI,CAACY,IAAI,CAACd,MAAM,EAAE;MACpC,MAAM0E,WAAW,GAAG,IAAI,CAAC5D,IAAI,CAAC6D,SAAS,CAAC,IAAI,CAACzE,KAAK,EAAE,CAAC;MACrD,IAAIwE,WAAW,KAAK,CAAC,EAAE;QACrB,MAAMrC,QAAQ,GAAG,IAAI,CAACvB,IAAI,CAAC8D,YAAY,CAAC,IAAI,CAAC1E,KAAK,CAAC;QACnD,MAAM2E,GAAG,GAAG,IAAI,CAAC/D,IAAI,CAACyB,QAAQ,CAAC,IAAI,CAACrC,KAAK,EAAE,IAAI,CAACA,KAAK,GAAGmC,QAAQ,CAAC;QAEjE,IAAI,CAAC1B,QAAQ,CAACf,IAAI,CAACiF,GAAG,CAAC;QAEvB,IAAI,CAAC3E,KAAK,IAAImC,QAAQ;MACxB,CAAC,MAAM,IAAIqC,WAAW,KAAK,CAAC,EAAE;QAC5B;QAEA;QACA,MAAM,IAAItI,OAAA,CAAAyB,iBAAiB,CAAC,qDAAqD,CAAC;MACpF;IACF;IAEA,IAAI,CAAC+C,MAAM,GAAG,IAAI;IAElB,OAAO,IAAI,CAACD,QAAQ,CAAC,CAAC,CAAC;EACzB;;AA3FFN,OAAA,CAAAoE,aAAA,GAAAA,aAAA;AA8FA,MAAMK,mBAAmB,GAAG,EAAE;AAC9B,MAAMC,wBAAwB,GAAG,CAAC,CAAC,CAAC;AAEpC;;;;;AAKA,MAAaC,mBAAmB;EAC9BzH,YACUgG,OAAiC,EACjC7F,OAA2E;IAD3E,KAAA6F,OAAO,GAAPA,OAAO;IACP,KAAA7F,OAAO,GAAPA,OAAO;EACd;EAEH;EACA;EACA,OAAOuH,WAAWA,CAAC1B,OAAiC;IAClD,MAAM2B,UAAU,GAAG3B,OAAO,YAAYD,YAAY,GAAGC,OAAO,CAACA,OAAO,GAAGA,OAAO,CAAC9F,KAAK;IACpF,MAAM0H,WAAW,GAAGrF,MAAM,CAACC,IAAI,CAACmF,UAAU,CAAC,CAAC,CAAC,CAAC;IAC9C,OAAO,CAAC7I,aAAA,CAAA+I,sBAAsB,CAACC,GAAG,CAACF,WAAW,CAAC;EACjD;EAEA,MAAM/F,KAAKA,CAAA;IACT,MAAMkG,iCAAiC,GAAG7F,MAAM,CAAC4D,MAAM,CAAC,IAAI,CAACE,OAAO,CAACnE,KAAK,EAAE,CAAC;IAC7E;IACA,MAAMmG,qBAAqB,GAAGD,iCAAiC,CAACE,KAAK,CAACV,mBAAmB,CAAC;IAE1F;IACA,MAAMW,qBAAqB,GAAGH,iCAAiC,CAAC5D,WAAW,CAAC,EAAE,CAAC;IAE/E;IACA,MAAMgE,iBAAiB,GAAG,MAAM,IAAArJ,aAAA,CAAAsJ,QAAQ,EAAC,IAAI,CAACjI,OAAO,EAAE6H,qBAAqB,CAAC;IAC7E;IACA,MAAM/E,SAAS,GAAGf,MAAM,CAACC,KAAK,CAACoF,mBAAmB,CAAC;IACnDtE,SAAS,CAAC4C,YAAY,CACpB0B,mBAAmB,GAAGC,wBAAwB,GAAGW,iBAAiB,CAAC1F,MAAM,EACzE,CAAC,CACF,CAAC,CAAC;IACHQ,SAAS,CAAC4C,YAAY,CAAC,IAAI,CAACG,OAAO,CAACpF,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC;IACnDqC,SAAS,CAAC4C,YAAY,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC9B5C,SAAS,CAAC4C,YAAY,CAAC9G,WAAA,CAAAsJ,aAAa,EAAE,EAAE,CAAC,CAAC,CAAC;IAE3C;IACA,MAAMC,kBAAkB,GAAGpG,MAAM,CAACC,KAAK,CAACqF,wBAAwB,CAAC;IACjEc,kBAAkB,CAACzC,YAAY,CAACqC,qBAAqB,EAAE,CAAC,CAAC,CAAC,CAAC;IAC3DI,kBAAkB,CAACzC,YAAY,CAACmC,qBAAqB,CAACvF,MAAM,EAAE,CAAC,CAAC,CAAC,CAAC;IAClE6F,kBAAkB,CAACC,UAAU,CAACzJ,aAAA,CAAA0J,UAAU,CAAC,IAAI,CAACrI,OAAO,CAACsI,gBAAgB,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;IAC7E,OAAO,CAACxF,SAAS,EAAEqF,kBAAkB,EAAEH,iBAAiB,CAAC;EAC3D;;AAxCFrF,OAAA,CAAA2E,mBAAA,GAAAA,mBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}