{"ast": null, "code": "import { useEffect as d } from \"react\";\nimport { useLatestValue as s } from './use-latest-value.js';\nfunction E(n, e, a, t) {\n  let i = s(a);\n  d(() => {\n    n = n != null ? n : window;\n    function r(o) {\n      i.current(o);\n    }\n    return n.addEventListener(e, r, t), () => n.removeEventListener(e, r, t);\n  }, [n, e, t]);\n}\nexport { E as useEventListener };", "map": {"version": 3, "names": ["useEffect", "d", "useLatestValue", "s", "E", "n", "e", "a", "t", "i", "window", "r", "o", "current", "addEventListener", "removeEventListener", "useEventListener"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/hooks/use-event-listener.js"], "sourcesContent": ["import{useEffect as d}from\"react\";import{useLatestValue as s}from'./use-latest-value.js';function E(n,e,a,t){let i=s(a);d(()=>{n=n!=null?n:window;function r(o){i.current(o)}return n.addEventListener(e,r,t),()=>n.removeEventListener(e,r,t)},[n,e,t])}export{E as useEventListener};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACN,CAAC,CAACI,CAAC,CAAC;EAACN,CAAC,CAAC,MAAI;IAACI,CAAC,GAACA,CAAC,IAAE,IAAI,GAACA,CAAC,GAACK,MAAM;IAAC,SAASC,CAACA,CAACC,CAAC,EAAC;MAACH,CAAC,CAACI,OAAO,CAACD,CAAC,CAAC;IAAA;IAAC,OAAOP,CAAC,CAACS,gBAAgB,CAACR,CAAC,EAACK,CAAC,EAACH,CAAC,CAAC,EAAC,MAAIH,CAAC,CAACU,mBAAmB,CAACT,CAAC,EAACK,CAAC,EAACH,CAAC,CAAC;EAAA,CAAC,EAAC,<PERSON><PERSON>H,CAAC,EAACC,CAAC,EAACE,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOJ,CAAC,IAAIY,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}