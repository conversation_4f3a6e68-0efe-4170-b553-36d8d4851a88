{"ast": null, "code": "\"use strict\";\n\n// Note that we take code points as JS numbers, not JS strings.\nfunction isASCIIDigit(c) {\n  return c >= 0x30 && c <= 0x39;\n}\nfunction isASCIIAlpha(c) {\n  return c >= 0x41 && c <= 0x5A || c >= 0x61 && c <= 0x7A;\n}\nfunction isASCIIAlphanumeric(c) {\n  return isASCIIAlpha(c) || isASCIIDigit(c);\n}\nfunction isASCIIHex(c) {\n  return isASCIIDigit(c) || c >= 0x41 && c <= 0x46 || c >= 0x61 && c <= 0x66;\n}\nmodule.exports = {\n  isASCIIDigit,\n  isASCIIAlpha,\n  isASCIIAlphanumeric,\n  isASCIIHex\n};", "map": {"version": 3, "names": ["isASCIIDigit", "c", "isASCIIAlpha", "isASCIIAlphanumeric", "isASCIIHex", "module", "exports"], "sources": ["C:/Users/<USER>/node_modules/mongodb-connection-string-url/node_modules/whatwg-url/lib/infra.js"], "sourcesContent": ["\"use strict\";\n\n// Note that we take code points as JS numbers, not JS strings.\n\nfunction isASCIIDigit(c) {\n  return c >= 0x30 && c <= 0x39;\n}\n\nfunction isASCIIAlpha(c) {\n  return (c >= 0x41 && c <= 0x5A) || (c >= 0x61 && c <= 0x7A);\n}\n\nfunction isASCIIAlphanumeric(c) {\n  return isASCIIAlpha(c) || isASCIIDigit(c);\n}\n\nfunction isASCIIHex(c) {\n  return isASCIIDigit(c) || (c >= 0x41 && c <= 0x46) || (c >= 0x61 && c <= 0x66);\n}\n\nmodule.exports = {\n  isASCIIDigit,\n  isASCIIAlpha,\n  isASCIIAlphanumeric,\n  isASCIIHex\n};\n"], "mappings": "AAAA,YAAY;;AAEZ;AAEA,SAASA,YAAYA,CAACC,CAAC,EAAE;EACvB,OAAOA,CAAC,IAAI,IAAI,IAAIA,CAAC,IAAI,IAAI;AAC/B;AAEA,SAASC,YAAYA,CAACD,CAAC,EAAE;EACvB,OAAQA,CAAC,IAAI,IAAI,IAAIA,CAAC,IAAI,IAAI,IAAMA,CAAC,IAAI,IAAI,IAAIA,CAAC,IAAI,IAAK;AAC7D;AAEA,SAASE,mBAAmBA,CAACF,CAAC,EAAE;EAC9B,OAAOC,YAAY,CAACD,CAAC,CAAC,IAAID,YAAY,CAACC,CAAC,CAAC;AAC3C;AAEA,SAASG,UAAUA,CAACH,CAAC,EAAE;EACrB,OAAOD,YAAY,CAACC,CAAC,CAAC,IAAKA,CAAC,IAAI,IAAI,IAAIA,CAAC,IAAI,IAAK,IAAKA,CAAC,IAAI,IAAI,IAAIA,CAAC,IAAI,IAAK;AAChF;AAEAI,MAAM,CAACC,OAAO,GAAG;EACfN,YAAY;EACZE,YAAY;EACZC,mBAAmB;EACnBC;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}