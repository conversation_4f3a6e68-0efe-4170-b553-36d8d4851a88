{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"native\"];\nimport * as React from 'react';\nimport MUIMenuItem from '@mui/material/MenuItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function MUISelectOption(_ref) {\n  let {\n      native\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  if (native) {\n    return /*#__PURE__*/_jsx(\"option\", _extends({}, props));\n  }\n  return /*#__PURE__*/_jsx(MUIMenuItem, _extends({}, props));\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "React", "MUIMenuItem", "jsx", "_jsx", "MUISelectOption", "_ref", "native", "props"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/x-data-grid/material/components/MUISelectOption.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"native\"];\nimport * as React from 'react';\nimport MUIMenuItem from '@mui/material/MenuItem';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nexport default function MUISelectOption(_ref) {\n  let {\n      native\n    } = _ref,\n    props = _objectWithoutPropertiesLoose(_ref, _excluded);\n  if (native) {\n    return /*#__PURE__*/_jsx(\"option\", _extends({}, props));\n  }\n  return /*#__PURE__*/_jsx(MUIMenuItem, _extends({}, props));\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,QAAQ,CAAC;AAC5B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,WAAW,MAAM,wBAAwB;AAChD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,eAAe,SAASC,eAAeA,CAACC,IAAI,EAAE;EAC5C,IAAI;MACAC;IACF,CAAC,GAAGD,IAAI;IACRE,KAAK,GAAGT,6BAA6B,CAACO,IAAI,EAAEN,SAAS,CAAC;EACxD,IAAIO,MAAM,EAAE;IACV,OAAO,aAAaH,IAAI,CAAC,QAAQ,EAAEN,QAAQ,CAAC,CAAC,CAAC,EAAEU,KAAK,CAAC,CAAC;EACzD;EACA,OAAO,aAAaJ,IAAI,CAACF,WAAW,EAAEJ,QAAQ,CAAC,CAAC,CAAC,EAAEU,KAAK,CAAC,CAAC;AAC5D", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}