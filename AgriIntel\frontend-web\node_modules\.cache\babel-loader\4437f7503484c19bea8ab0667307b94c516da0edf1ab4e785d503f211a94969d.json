{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"id\", \"field\"],\n  _excluded2 = [\"id\", \"field\"];\nimport * as React from 'react';\nimport { unstable_useEventCallback as useEventCallback, unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { useGridApiEventHandler, useGridApiOptionHandler } from '../../utils/useGridApiEventHandler';\nimport { GridEditModes, GridCellModes } from '../../../models/gridEditRowModel';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nimport { gridEditRowsStateSelector } from './gridEditingSelectors';\nimport { isPrintableKey } from '../../../utils/keyboardUtils';\nimport { buildWarning } from '../../../utils/warning';\nimport { gridRowsDataRowIdToIdLookupSelector } from '../rows/gridRowsSelector';\nimport { deepClone } from '../../../utils/utils';\nimport { GridCellEditStartReasons, GridCellEditStopReasons } from '../../../models/params/gridEditCellParams';\nconst missingOnProcessRowUpdateErrorWarning = buildWarning(['MUI: A call to `processRowUpdate` threw an error which was not handled because `onProcessRowUpdateError` is missing.', 'To handle the error pass a callback to the `onProcessRowUpdateError` prop, e.g. `<DataGrid onProcessRowUpdateError={(error) => ...} />`.', 'For more detail, see http://mui.com/components/data-grid/editing/#server-side-persistence.'], 'error');\nexport const useGridCellEditing = (apiRef, props) => {\n  const [cellModesModel, setCellModesModel] = React.useState({});\n  const cellModesModelRef = React.useRef(cellModesModel);\n  const prevCellModesModel = React.useRef({});\n  const {\n    processRowUpdate,\n    onProcessRowUpdateError,\n    cellModesModel: cellModesModelProp,\n    onCellModesModelChange\n  } = props;\n  const runIfEditModeIsCell = callback => (...args) => {\n    if (props.editMode === GridEditModes.Cell) {\n      callback(...args);\n    }\n  };\n  const throwIfNotEditable = React.useCallback((id, field) => {\n    const params = apiRef.current.getCellParams(id, field);\n    if (!apiRef.current.isCellEditable(params)) {\n      throw new Error(`MUI: The cell with id=${id} and field=${field} is not editable.`);\n    }\n  }, [apiRef]);\n  const throwIfNotInMode = React.useCallback((id, field, mode) => {\n    if (apiRef.current.getCellMode(id, field) !== mode) {\n      throw new Error(`MUI: The cell with id=${id} and field=${field} is not in ${mode} mode.`);\n    }\n  }, [apiRef]);\n  const handleCellDoubleClick = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (params.cellMode === GridCellModes.Edit) {\n      return;\n    }\n    const newParams = _extends({}, params, {\n      reason: GridCellEditStartReasons.cellDoubleClick\n    });\n    apiRef.current.publishEvent('cellEditStart', newParams, event);\n  }, [apiRef]);\n  const handleCellFocusOut = React.useCallback((params, event) => {\n    if (params.cellMode === GridCellModes.View) {\n      return;\n    }\n    if (apiRef.current.getCellMode(params.id, params.field) === GridCellModes.View) {\n      return;\n    }\n    const newParams = _extends({}, params, {\n      reason: GridCellEditStopReasons.cellFocusOut\n    });\n    apiRef.current.publishEvent('cellEditStop', newParams, event);\n  }, [apiRef]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    if (params.cellMode === GridCellModes.Edit) {\n      // Wait until IME is settled for Asian languages like Japanese and Chinese\n      // TODO: `event.which` is deprecated but this is a temporary workaround\n      if (event.which === 229) {\n        return;\n      }\n      let reason;\n      if (event.key === 'Escape') {\n        reason = GridCellEditStopReasons.escapeKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridCellEditStopReasons.enterKeyDown;\n      } else if (event.key === 'Tab') {\n        reason = event.shiftKey ? GridCellEditStopReasons.shiftTabKeyDown : GridCellEditStopReasons.tabKeyDown;\n        event.preventDefault(); // Prevent going to the next element in the tab sequence\n      }\n      if (reason) {\n        const newParams = _extends({}, params, {\n          reason\n        });\n        apiRef.current.publishEvent('cellEditStop', newParams, event);\n      }\n    } else if (params.isEditable) {\n      let reason;\n      const canStartEditing = apiRef.current.unstable_applyPipeProcessors('canStartEditing', true, {\n        event,\n        cellParams: params,\n        editMode: 'cell'\n      });\n      if (!canStartEditing) {\n        return;\n      }\n      if (isPrintableKey(event)) {\n        reason = GridCellEditStartReasons.printableKeyDown;\n      } else if ((event.ctrlKey || event.metaKey) && event.key === 'v') {\n        reason = GridCellEditStartReasons.pasteKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridCellEditStartReasons.enterKeyDown;\n      } else if (event.key === 'Delete' || event.key === 'Backspace') {\n        // Delete on Windows, Backspace on macOS\n        reason = GridCellEditStartReasons.deleteKeyDown;\n      }\n      if (reason) {\n        const newParams = _extends({}, params, {\n          reason,\n          key: event.key\n        });\n        apiRef.current.publishEvent('cellEditStart', newParams, event);\n      }\n    }\n  }, [apiRef]);\n  const handleCellEditStart = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    const startCellEditModeParams = {\n      id,\n      field\n    };\n    if (reason === GridCellEditStartReasons.printableKeyDown || reason === GridCellEditStartReasons.deleteKeyDown || reason === GridCellEditStartReasons.pasteKeyDown) {\n      startCellEditModeParams.deleteValue = true;\n    }\n    apiRef.current.startCellEditMode(startCellEditModeParams);\n  }, [apiRef]);\n  const handleCellEditStop = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id, field);\n    let cellToFocusAfter;\n    if (reason === GridCellEditStopReasons.enterKeyDown) {\n      cellToFocusAfter = 'below';\n    } else if (reason === GridCellEditStopReasons.tabKeyDown) {\n      cellToFocusAfter = 'right';\n    } else if (reason === GridCellEditStopReasons.shiftTabKeyDown) {\n      cellToFocusAfter = 'left';\n    }\n    const ignoreModifications = reason === 'escapeKeyDown';\n    apiRef.current.stopCellEditMode({\n      id,\n      field,\n      ignoreModifications,\n      cellToFocusAfter\n    });\n  }, [apiRef]);\n  useGridApiEventHandler(apiRef, 'cellDoubleClick', runIfEditModeIsCell(handleCellDoubleClick));\n  useGridApiEventHandler(apiRef, 'cellFocusOut', runIfEditModeIsCell(handleCellFocusOut));\n  useGridApiEventHandler(apiRef, 'cellKeyDown', runIfEditModeIsCell(handleCellKeyDown));\n  useGridApiEventHandler(apiRef, 'cellEditStart', runIfEditModeIsCell(handleCellEditStart));\n  useGridApiEventHandler(apiRef, 'cellEditStop', runIfEditModeIsCell(handleCellEditStop));\n  useGridApiOptionHandler(apiRef, 'cellEditStart', props.onCellEditStart);\n  useGridApiOptionHandler(apiRef, 'cellEditStop', props.onCellEditStop);\n  const getCellMode = React.useCallback((id, field) => {\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const isEditing = editingState[id] && editingState[id][field];\n    return isEditing ? GridCellModes.Edit : GridCellModes.View;\n  }, [apiRef]);\n  const updateCellModesModel = useEventCallback(newModel => {\n    const isNewModelDifferentFromProp = newModel !== props.cellModesModel;\n    if (onCellModesModelChange && isNewModelDifferentFromProp) {\n      onCellModesModelChange(newModel, {});\n    }\n    if (props.cellModesModel && isNewModelDifferentFromProp) {\n      return; // The prop always win\n    }\n    setCellModesModel(newModel);\n    cellModesModelRef.current = newModel;\n    apiRef.current.publishEvent('cellModesModelChange', newModel);\n  });\n  const updateFieldInCellModesModel = React.useCallback((id, field, newProps) => {\n    // We use the ref because it always contain the up-to-date value, different from the state\n    // that needs a rerender to reflect the new value\n    const newModel = _extends({}, cellModesModelRef.current);\n    if (newProps !== null) {\n      newModel[id] = _extends({}, newModel[id], {\n        [field]: _extends({}, newProps)\n      });\n    } else {\n      const _newModel$id = newModel[id],\n        otherFields = _objectWithoutPropertiesLoose(_newModel$id, [field].map(_toPropertyKey)); // Ensure that we have a new object, not a reference\n      newModel[id] = otherFields;\n      if (Object.keys(newModel[id]).length === 0) {\n        delete newModel[id];\n      }\n    }\n    updateCellModesModel(newModel);\n  }, [updateCellModesModel]);\n  const updateOrDeleteFieldState = React.useCallback((id, field, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = _extends({}, newEditingState[id], {\n          [field]: _extends({}, newProps)\n        });\n      } else {\n        delete newEditingState[id][field];\n        if (Object.keys(newEditingState[id]).length === 0) {\n          delete newEditingState[id];\n        }\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const startCellEditMode = React.useCallback(params => {\n    const {\n        id,\n        field\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded);\n    throwIfNotEditable(id, field);\n    throwIfNotInMode(id, field, GridCellModes.View);\n    updateFieldInCellModesModel(id, field, _extends({\n      mode: GridCellModes.Edit\n    }, other));\n  }, [throwIfNotEditable, throwIfNotInMode, updateFieldInCellModesModel]);\n  const updateStateToStartCellEditMode = useEventCallback(params => {\n    const {\n      id,\n      field,\n      deleteValue,\n      initialValue\n    } = params;\n    let newValue = apiRef.current.getCellValue(id, field);\n    if (deleteValue || initialValue) {\n      newValue = deleteValue ? '' : initialValue;\n    }\n    const newProps = {\n      value: newValue,\n      error: false,\n      isProcessingProps: false\n    };\n    updateOrDeleteFieldState(id, field, newProps);\n    apiRef.current.setCellFocus(id, field);\n  });\n  const stopCellEditMode = React.useCallback(params => {\n    const {\n        id,\n        field\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded2);\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    updateFieldInCellModesModel(id, field, _extends({\n      mode: GridCellModes.View\n    }, other));\n  }, [throwIfNotInMode, updateFieldInCellModesModel]);\n  const updateStateToStopCellEditMode = useEventCallback(async params => {\n    const {\n      id,\n      field,\n      ignoreModifications,\n      cellToFocusAfter = 'none'\n    } = params;\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    apiRef.current.runPendingEditCellValueMutation(id, field);\n    const finishCellEditMode = () => {\n      updateOrDeleteFieldState(id, field, null);\n      updateFieldInCellModesModel(id, field, null);\n      if (cellToFocusAfter !== 'none') {\n        apiRef.current.moveFocusToRelativeCell(id, field, cellToFocusAfter);\n      }\n    };\n    if (ignoreModifications) {\n      finishCellEditMode();\n      return;\n    }\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const {\n      error,\n      isProcessingProps\n    } = editingState[id][field];\n    if (error || isProcessingProps) {\n      // Attempt to change cell mode to \"view\" was not successful\n      // Update previous mode to allow another attempt\n      prevCellModesModel.current[id][field].mode = GridCellModes.Edit;\n      // Revert the mode in the cellModesModel prop back to \"edit\"\n      updateFieldInCellModesModel(id, field, {\n        mode: GridCellModes.Edit\n      });\n      return;\n    }\n    const rowUpdate = apiRef.current.getRowWithUpdatedValuesFromCellEditing(id, field);\n    if (processRowUpdate) {\n      const handleError = errorThrown => {\n        prevCellModesModel.current[id][field].mode = GridCellModes.Edit;\n        // Revert the mode in the cellModesModel prop back to \"edit\"\n        updateFieldInCellModesModel(id, field, {\n          mode: GridCellModes.Edit\n        });\n        if (onProcessRowUpdateError) {\n          onProcessRowUpdateError(errorThrown);\n        } else {\n          missingOnProcessRowUpdateErrorWarning();\n        }\n      };\n      try {\n        const row = apiRef.current.getRow(id);\n        Promise.resolve(processRowUpdate(rowUpdate, row)).then(finalRowUpdate => {\n          apiRef.current.updateRows([finalRowUpdate]);\n          finishCellEditMode();\n        }).catch(handleError);\n      } catch (errorThrown) {\n        handleError(errorThrown);\n      }\n    } else {\n      apiRef.current.updateRows([rowUpdate]);\n      finishCellEditMode();\n    }\n  });\n  const setCellEditingEditCellValue = React.useCallback(async params => {\n    var _editingState$id;\n    const {\n      id,\n      field,\n      value,\n      debounceMs,\n      unstable_skipValueParser: skipValueParser\n    } = params;\n    throwIfNotEditable(id, field);\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    const column = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    let parsedValue = value;\n    if (column.valueParser && !skipValueParser) {\n      parsedValue = column.valueParser(value, apiRef.current.getCellParams(id, field));\n    }\n    let editingState = gridEditRowsStateSelector(apiRef.current.state);\n    let newProps = _extends({}, editingState[id][field], {\n      value: parsedValue,\n      changeReason: debounceMs ? 'debouncedSetEditCellValue' : 'setEditCellValue'\n    });\n    if (column.preProcessEditCellProps) {\n      const hasChanged = value !== editingState[id][field].value;\n      newProps = _extends({}, newProps, {\n        isProcessingProps: true\n      });\n      updateOrDeleteFieldState(id, field, newProps);\n      newProps = await Promise.resolve(column.preProcessEditCellProps({\n        id,\n        row,\n        props: newProps,\n        hasChanged\n      }));\n    }\n\n    // Check again if the cell is in edit mode because the user may have\n    // discarded the changes while the props were being processed.\n    if (apiRef.current.getCellMode(id, field) === GridCellModes.View) {\n      return false;\n    }\n    editingState = gridEditRowsStateSelector(apiRef.current.state);\n    newProps = _extends({}, newProps, {\n      isProcessingProps: false\n    });\n    // We don't update the value with the one coming from the props pre-processing\n    // because when the promise resolves it may be already outdated. The only\n    // exception to this rule is when there's no pre-processing.\n    newProps.value = column.preProcessEditCellProps ? editingState[id][field].value : parsedValue;\n    updateOrDeleteFieldState(id, field, newProps);\n    editingState = gridEditRowsStateSelector(apiRef.current.state);\n    return !((_editingState$id = editingState[id]) != null && (_editingState$id = _editingState$id[field]) != null && _editingState$id.error);\n  }, [apiRef, throwIfNotEditable, throwIfNotInMode, updateOrDeleteFieldState]);\n  const getRowWithUpdatedValuesFromCellEditing = React.useCallback((id, field) => {\n    const column = apiRef.current.getColumn(field);\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const row = apiRef.current.getRow(id);\n    if (!editingState[id] || !editingState[id][field]) {\n      return apiRef.current.getRow(id);\n    }\n    const {\n      value\n    } = editingState[id][field];\n    return column.valueSetter ? column.valueSetter({\n      value,\n      row\n    }) : _extends({}, row, {\n      [field]: value\n    });\n  }, [apiRef]);\n  const editingApi = {\n    getCellMode,\n    startCellEditMode,\n    stopCellEditMode\n  };\n  const editingPrivateApi = {\n    setCellEditingEditCellValue,\n    getRowWithUpdatedValuesFromCellEditing\n  };\n  useGridApiMethod(apiRef, editingApi, 'public');\n  useGridApiMethod(apiRef, editingPrivateApi, 'private');\n  React.useEffect(() => {\n    if (cellModesModelProp) {\n      updateCellModesModel(cellModesModelProp);\n    }\n  }, [cellModesModelProp, updateCellModesModel]);\n\n  // Run this effect synchronously so that the keyboard event can impact the yet-to-be-rendered input.\n  useEnhancedEffect(() => {\n    const idToIdLookup = gridRowsDataRowIdToIdLookupSelector(apiRef);\n\n    // Update the ref here because updateStateToStopCellEditMode may change it later\n    const copyOfPrevCellModes = prevCellModesModel.current;\n    prevCellModesModel.current = deepClone(cellModesModel); // Do a deep-clone because the attributes might be changed later\n\n    Object.entries(cellModesModel).forEach(([id, fields]) => {\n      Object.entries(fields).forEach(([field, params]) => {\n        var _copyOfPrevCellModes$, _idToIdLookup$id;\n        const prevMode = ((_copyOfPrevCellModes$ = copyOfPrevCellModes[id]) == null || (_copyOfPrevCellModes$ = _copyOfPrevCellModes$[field]) == null ? void 0 : _copyOfPrevCellModes$.mode) || GridCellModes.View;\n        const originalId = (_idToIdLookup$id = idToIdLookup[id]) != null ? _idToIdLookup$id : id;\n        if (params.mode === GridCellModes.Edit && prevMode === GridCellModes.View) {\n          updateStateToStartCellEditMode(_extends({\n            id: originalId,\n            field\n          }, params));\n        } else if (params.mode === GridCellModes.View && prevMode === GridCellModes.Edit) {\n          updateStateToStopCellEditMode(_extends({\n            id: originalId,\n            field\n          }, params));\n        }\n      });\n    });\n  }, [apiRef, cellModesModel, updateStateToStartCellEditMode, updateStateToStopCellEditMode]);\n};", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_extends", "_excluded", "_excluded2", "React", "unstable_useEventCallback", "useEventCallback", "unstable_useEnhancedEffect", "useEnhancedEffect", "useGridApiEventHandler", "useGridApiOptionHandler", "GridEditModes", "GridCellModes", "useGridApiMethod", "gridEditRowsStateSelector", "isPrintableKey", "buildWarning", "gridRowsDataRowIdToIdLookupSelector", "deepClone", "GridCellEditStartReasons", "GridCellEditStopReasons", "missingOnProcessRowUpdateErrorWarning", "useGridCellEditing", "apiRef", "props", "cellModesModel", "setCellModesModel", "useState", "cellModesModelRef", "useRef", "prevCellModesModel", "processRowUpdate", "onProcessRowUpdateError", "cellModesModelProp", "onCellModesModelChange", "runIfEditModeIsCell", "callback", "args", "editMode", "Cell", "throwIfNotEditable", "useCallback", "id", "field", "params", "current", "getCellParams", "isCellEditable", "Error", "throwIfNotInMode", "mode", "getCellMode", "handleCellDoubleClick", "event", "isEditable", "cellMode", "Edit", "newParams", "reason", "cellDoubleClick", "publishEvent", "handleCellFocusOut", "View", "cellFocusOut", "handleCellKeyDown", "which", "key", "escapeKeyDown", "enterKeyDown", "shift<PERSON>ey", "shiftTabKeyDown", "tabKeyDown", "preventDefault", "canStartEditing", "unstable_applyPipeProcessors", "cellParams", "printableKeyDown", "ctrl<PERSON>ey", "metaKey", "pasteKeyDown", "deleteKeyDown", "handleCellEditStart", "startCellEditModeParams", "deleteValue", "startCellEditMode", "handleCellEditStop", "runPendingEditCellValueMutation", "cellToFocusAfter", "ignoreModifications", "stopCellEditMode", "onCellEditStart", "onCellEditStop", "editingState", "state", "isEditing", "updateCellModesModel", "newModel", "isNewModelDifferentFromProp", "updateFieldInCellModesModel", "newProps", "_newModel$id", "otherFields", "map", "Object", "keys", "length", "updateOrDeleteFieldState", "setState", "newEditingState", "editRows", "forceUpdate", "other", "updateStateToStartCellEditMode", "initialValue", "newValue", "getCellValue", "value", "error", "isProcessingProps", "setCellFocus", "updateStateToStopCellEditMode", "finishCellEditMode", "moveFocusToRelativeCell", "rowUpdate", "getRowWithUpdatedValuesFromCellEditing", "handleError", "errorThrown", "row", "getRow", "Promise", "resolve", "then", "finalRowUpdate", "updateRows", "catch", "setCellEditingEditCellValue", "_editingState$id", "debounceMs", "unstable_skip<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "column", "getColumn", "parsedValue", "valueParser", "changeReason", "preProcessEditCellProps", "has<PERSON><PERSON>ed", "valueSetter", "editingApi", "editingPrivateApi", "useEffect", "idToIdLookup", "copyOfPrevCellModes", "entries", "for<PERSON>ach", "fields", "_copyOfPrevCellModes$", "_idToIdLookup$id", "prevMode", "originalId"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/x-data-grid/hooks/features/editing/useGridCellEditing.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"id\", \"field\"],\n  _excluded2 = [\"id\", \"field\"];\nimport * as React from 'react';\nimport { unstable_useEventCallback as useEventCallback, unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { useGridApiEventHandler, useGridApiOptionHandler } from '../../utils/useGridApiEventHandler';\nimport { GridEditModes, GridCellModes } from '../../../models/gridEditRowModel';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nimport { gridEditRowsStateSelector } from './gridEditingSelectors';\nimport { isPrintableKey } from '../../../utils/keyboardUtils';\nimport { buildWarning } from '../../../utils/warning';\nimport { gridRowsDataRowIdToIdLookupSelector } from '../rows/gridRowsSelector';\nimport { deepClone } from '../../../utils/utils';\nimport { GridCellEditStartReasons, GridCellEditStopReasons } from '../../../models/params/gridEditCellParams';\nconst missingOnProcessRowUpdateErrorWarning = buildWarning(['MUI: A call to `processRowUpdate` threw an error which was not handled because `onProcessRowUpdateError` is missing.', 'To handle the error pass a callback to the `onProcessRowUpdateError` prop, e.g. `<DataGrid onProcessRowUpdateError={(error) => ...} />`.', 'For more detail, see http://mui.com/components/data-grid/editing/#server-side-persistence.'], 'error');\nexport const useGridCellEditing = (apiRef, props) => {\n  const [cellModesModel, setCellModesModel] = React.useState({});\n  const cellModesModelRef = React.useRef(cellModesModel);\n  const prevCellModesModel = React.useRef({});\n  const {\n    processRowUpdate,\n    onProcessRowUpdateError,\n    cellModesModel: cellModesModelProp,\n    onCellModesModelChange\n  } = props;\n  const runIfEditModeIsCell = callback => (...args) => {\n    if (props.editMode === GridEditModes.Cell) {\n      callback(...args);\n    }\n  };\n  const throwIfNotEditable = React.useCallback((id, field) => {\n    const params = apiRef.current.getCellParams(id, field);\n    if (!apiRef.current.isCellEditable(params)) {\n      throw new Error(`MUI: The cell with id=${id} and field=${field} is not editable.`);\n    }\n  }, [apiRef]);\n  const throwIfNotInMode = React.useCallback((id, field, mode) => {\n    if (apiRef.current.getCellMode(id, field) !== mode) {\n      throw new Error(`MUI: The cell with id=${id} and field=${field} is not in ${mode} mode.`);\n    }\n  }, [apiRef]);\n  const handleCellDoubleClick = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (params.cellMode === GridCellModes.Edit) {\n      return;\n    }\n    const newParams = _extends({}, params, {\n      reason: GridCellEditStartReasons.cellDoubleClick\n    });\n    apiRef.current.publishEvent('cellEditStart', newParams, event);\n  }, [apiRef]);\n  const handleCellFocusOut = React.useCallback((params, event) => {\n    if (params.cellMode === GridCellModes.View) {\n      return;\n    }\n    if (apiRef.current.getCellMode(params.id, params.field) === GridCellModes.View) {\n      return;\n    }\n    const newParams = _extends({}, params, {\n      reason: GridCellEditStopReasons.cellFocusOut\n    });\n    apiRef.current.publishEvent('cellEditStop', newParams, event);\n  }, [apiRef]);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    if (params.cellMode === GridCellModes.Edit) {\n      // Wait until IME is settled for Asian languages like Japanese and Chinese\n      // TODO: `event.which` is deprecated but this is a temporary workaround\n      if (event.which === 229) {\n        return;\n      }\n      let reason;\n      if (event.key === 'Escape') {\n        reason = GridCellEditStopReasons.escapeKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridCellEditStopReasons.enterKeyDown;\n      } else if (event.key === 'Tab') {\n        reason = event.shiftKey ? GridCellEditStopReasons.shiftTabKeyDown : GridCellEditStopReasons.tabKeyDown;\n        event.preventDefault(); // Prevent going to the next element in the tab sequence\n      }\n      if (reason) {\n        const newParams = _extends({}, params, {\n          reason\n        });\n        apiRef.current.publishEvent('cellEditStop', newParams, event);\n      }\n    } else if (params.isEditable) {\n      let reason;\n      const canStartEditing = apiRef.current.unstable_applyPipeProcessors('canStartEditing', true, {\n        event,\n        cellParams: params,\n        editMode: 'cell'\n      });\n      if (!canStartEditing) {\n        return;\n      }\n      if (isPrintableKey(event)) {\n        reason = GridCellEditStartReasons.printableKeyDown;\n      } else if ((event.ctrlKey || event.metaKey) && event.key === 'v') {\n        reason = GridCellEditStartReasons.pasteKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridCellEditStartReasons.enterKeyDown;\n      } else if (event.key === 'Delete' || event.key === 'Backspace') {\n        // Delete on Windows, Backspace on macOS\n        reason = GridCellEditStartReasons.deleteKeyDown;\n      }\n      if (reason) {\n        const newParams = _extends({}, params, {\n          reason,\n          key: event.key\n        });\n        apiRef.current.publishEvent('cellEditStart', newParams, event);\n      }\n    }\n  }, [apiRef]);\n  const handleCellEditStart = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    const startCellEditModeParams = {\n      id,\n      field\n    };\n    if (reason === GridCellEditStartReasons.printableKeyDown || reason === GridCellEditStartReasons.deleteKeyDown || reason === GridCellEditStartReasons.pasteKeyDown) {\n      startCellEditModeParams.deleteValue = true;\n    }\n    apiRef.current.startCellEditMode(startCellEditModeParams);\n  }, [apiRef]);\n  const handleCellEditStop = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id, field);\n    let cellToFocusAfter;\n    if (reason === GridCellEditStopReasons.enterKeyDown) {\n      cellToFocusAfter = 'below';\n    } else if (reason === GridCellEditStopReasons.tabKeyDown) {\n      cellToFocusAfter = 'right';\n    } else if (reason === GridCellEditStopReasons.shiftTabKeyDown) {\n      cellToFocusAfter = 'left';\n    }\n    const ignoreModifications = reason === 'escapeKeyDown';\n    apiRef.current.stopCellEditMode({\n      id,\n      field,\n      ignoreModifications,\n      cellToFocusAfter\n    });\n  }, [apiRef]);\n  useGridApiEventHandler(apiRef, 'cellDoubleClick', runIfEditModeIsCell(handleCellDoubleClick));\n  useGridApiEventHandler(apiRef, 'cellFocusOut', runIfEditModeIsCell(handleCellFocusOut));\n  useGridApiEventHandler(apiRef, 'cellKeyDown', runIfEditModeIsCell(handleCellKeyDown));\n  useGridApiEventHandler(apiRef, 'cellEditStart', runIfEditModeIsCell(handleCellEditStart));\n  useGridApiEventHandler(apiRef, 'cellEditStop', runIfEditModeIsCell(handleCellEditStop));\n  useGridApiOptionHandler(apiRef, 'cellEditStart', props.onCellEditStart);\n  useGridApiOptionHandler(apiRef, 'cellEditStop', props.onCellEditStop);\n  const getCellMode = React.useCallback((id, field) => {\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const isEditing = editingState[id] && editingState[id][field];\n    return isEditing ? GridCellModes.Edit : GridCellModes.View;\n  }, [apiRef]);\n  const updateCellModesModel = useEventCallback(newModel => {\n    const isNewModelDifferentFromProp = newModel !== props.cellModesModel;\n    if (onCellModesModelChange && isNewModelDifferentFromProp) {\n      onCellModesModelChange(newModel, {});\n    }\n    if (props.cellModesModel && isNewModelDifferentFromProp) {\n      return; // The prop always win\n    }\n    setCellModesModel(newModel);\n    cellModesModelRef.current = newModel;\n    apiRef.current.publishEvent('cellModesModelChange', newModel);\n  });\n  const updateFieldInCellModesModel = React.useCallback((id, field, newProps) => {\n    // We use the ref because it always contain the up-to-date value, different from the state\n    // that needs a rerender to reflect the new value\n    const newModel = _extends({}, cellModesModelRef.current);\n    if (newProps !== null) {\n      newModel[id] = _extends({}, newModel[id], {\n        [field]: _extends({}, newProps)\n      });\n    } else {\n      const _newModel$id = newModel[id],\n        otherFields = _objectWithoutPropertiesLoose(_newModel$id, [field].map(_toPropertyKey)); // Ensure that we have a new object, not a reference\n      newModel[id] = otherFields;\n      if (Object.keys(newModel[id]).length === 0) {\n        delete newModel[id];\n      }\n    }\n    updateCellModesModel(newModel);\n  }, [updateCellModesModel]);\n  const updateOrDeleteFieldState = React.useCallback((id, field, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = _extends({}, newEditingState[id], {\n          [field]: _extends({}, newProps)\n        });\n      } else {\n        delete newEditingState[id][field];\n        if (Object.keys(newEditingState[id]).length === 0) {\n          delete newEditingState[id];\n        }\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const startCellEditMode = React.useCallback(params => {\n    const {\n        id,\n        field\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded);\n    throwIfNotEditable(id, field);\n    throwIfNotInMode(id, field, GridCellModes.View);\n    updateFieldInCellModesModel(id, field, _extends({\n      mode: GridCellModes.Edit\n    }, other));\n  }, [throwIfNotEditable, throwIfNotInMode, updateFieldInCellModesModel]);\n  const updateStateToStartCellEditMode = useEventCallback(params => {\n    const {\n      id,\n      field,\n      deleteValue,\n      initialValue\n    } = params;\n    let newValue = apiRef.current.getCellValue(id, field);\n    if (deleteValue || initialValue) {\n      newValue = deleteValue ? '' : initialValue;\n    }\n    const newProps = {\n      value: newValue,\n      error: false,\n      isProcessingProps: false\n    };\n    updateOrDeleteFieldState(id, field, newProps);\n    apiRef.current.setCellFocus(id, field);\n  });\n  const stopCellEditMode = React.useCallback(params => {\n    const {\n        id,\n        field\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded2);\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    updateFieldInCellModesModel(id, field, _extends({\n      mode: GridCellModes.View\n    }, other));\n  }, [throwIfNotInMode, updateFieldInCellModesModel]);\n  const updateStateToStopCellEditMode = useEventCallback(async params => {\n    const {\n      id,\n      field,\n      ignoreModifications,\n      cellToFocusAfter = 'none'\n    } = params;\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    apiRef.current.runPendingEditCellValueMutation(id, field);\n    const finishCellEditMode = () => {\n      updateOrDeleteFieldState(id, field, null);\n      updateFieldInCellModesModel(id, field, null);\n      if (cellToFocusAfter !== 'none') {\n        apiRef.current.moveFocusToRelativeCell(id, field, cellToFocusAfter);\n      }\n    };\n    if (ignoreModifications) {\n      finishCellEditMode();\n      return;\n    }\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const {\n      error,\n      isProcessingProps\n    } = editingState[id][field];\n    if (error || isProcessingProps) {\n      // Attempt to change cell mode to \"view\" was not successful\n      // Update previous mode to allow another attempt\n      prevCellModesModel.current[id][field].mode = GridCellModes.Edit;\n      // Revert the mode in the cellModesModel prop back to \"edit\"\n      updateFieldInCellModesModel(id, field, {\n        mode: GridCellModes.Edit\n      });\n      return;\n    }\n    const rowUpdate = apiRef.current.getRowWithUpdatedValuesFromCellEditing(id, field);\n    if (processRowUpdate) {\n      const handleError = errorThrown => {\n        prevCellModesModel.current[id][field].mode = GridCellModes.Edit;\n        // Revert the mode in the cellModesModel prop back to \"edit\"\n        updateFieldInCellModesModel(id, field, {\n          mode: GridCellModes.Edit\n        });\n        if (onProcessRowUpdateError) {\n          onProcessRowUpdateError(errorThrown);\n        } else {\n          missingOnProcessRowUpdateErrorWarning();\n        }\n      };\n      try {\n        const row = apiRef.current.getRow(id);\n        Promise.resolve(processRowUpdate(rowUpdate, row)).then(finalRowUpdate => {\n          apiRef.current.updateRows([finalRowUpdate]);\n          finishCellEditMode();\n        }).catch(handleError);\n      } catch (errorThrown) {\n        handleError(errorThrown);\n      }\n    } else {\n      apiRef.current.updateRows([rowUpdate]);\n      finishCellEditMode();\n    }\n  });\n  const setCellEditingEditCellValue = React.useCallback(async params => {\n    var _editingState$id;\n    const {\n      id,\n      field,\n      value,\n      debounceMs,\n      unstable_skipValueParser: skipValueParser\n    } = params;\n    throwIfNotEditable(id, field);\n    throwIfNotInMode(id, field, GridCellModes.Edit);\n    const column = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    let parsedValue = value;\n    if (column.valueParser && !skipValueParser) {\n      parsedValue = column.valueParser(value, apiRef.current.getCellParams(id, field));\n    }\n    let editingState = gridEditRowsStateSelector(apiRef.current.state);\n    let newProps = _extends({}, editingState[id][field], {\n      value: parsedValue,\n      changeReason: debounceMs ? 'debouncedSetEditCellValue' : 'setEditCellValue'\n    });\n    if (column.preProcessEditCellProps) {\n      const hasChanged = value !== editingState[id][field].value;\n      newProps = _extends({}, newProps, {\n        isProcessingProps: true\n      });\n      updateOrDeleteFieldState(id, field, newProps);\n      newProps = await Promise.resolve(column.preProcessEditCellProps({\n        id,\n        row,\n        props: newProps,\n        hasChanged\n      }));\n    }\n\n    // Check again if the cell is in edit mode because the user may have\n    // discarded the changes while the props were being processed.\n    if (apiRef.current.getCellMode(id, field) === GridCellModes.View) {\n      return false;\n    }\n    editingState = gridEditRowsStateSelector(apiRef.current.state);\n    newProps = _extends({}, newProps, {\n      isProcessingProps: false\n    });\n    // We don't update the value with the one coming from the props pre-processing\n    // because when the promise resolves it may be already outdated. The only\n    // exception to this rule is when there's no pre-processing.\n    newProps.value = column.preProcessEditCellProps ? editingState[id][field].value : parsedValue;\n    updateOrDeleteFieldState(id, field, newProps);\n    editingState = gridEditRowsStateSelector(apiRef.current.state);\n    return !((_editingState$id = editingState[id]) != null && (_editingState$id = _editingState$id[field]) != null && _editingState$id.error);\n  }, [apiRef, throwIfNotEditable, throwIfNotInMode, updateOrDeleteFieldState]);\n  const getRowWithUpdatedValuesFromCellEditing = React.useCallback((id, field) => {\n    const column = apiRef.current.getColumn(field);\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const row = apiRef.current.getRow(id);\n    if (!editingState[id] || !editingState[id][field]) {\n      return apiRef.current.getRow(id);\n    }\n    const {\n      value\n    } = editingState[id][field];\n    return column.valueSetter ? column.valueSetter({\n      value,\n      row\n    }) : _extends({}, row, {\n      [field]: value\n    });\n  }, [apiRef]);\n  const editingApi = {\n    getCellMode,\n    startCellEditMode,\n    stopCellEditMode\n  };\n  const editingPrivateApi = {\n    setCellEditingEditCellValue,\n    getRowWithUpdatedValuesFromCellEditing\n  };\n  useGridApiMethod(apiRef, editingApi, 'public');\n  useGridApiMethod(apiRef, editingPrivateApi, 'private');\n  React.useEffect(() => {\n    if (cellModesModelProp) {\n      updateCellModesModel(cellModesModelProp);\n    }\n  }, [cellModesModelProp, updateCellModesModel]);\n\n  // Run this effect synchronously so that the keyboard event can impact the yet-to-be-rendered input.\n  useEnhancedEffect(() => {\n    const idToIdLookup = gridRowsDataRowIdToIdLookupSelector(apiRef);\n\n    // Update the ref here because updateStateToStopCellEditMode may change it later\n    const copyOfPrevCellModes = prevCellModesModel.current;\n    prevCellModesModel.current = deepClone(cellModesModel); // Do a deep-clone because the attributes might be changed later\n\n    Object.entries(cellModesModel).forEach(([id, fields]) => {\n      Object.entries(fields).forEach(([field, params]) => {\n        var _copyOfPrevCellModes$, _idToIdLookup$id;\n        const prevMode = ((_copyOfPrevCellModes$ = copyOfPrevCellModes[id]) == null || (_copyOfPrevCellModes$ = _copyOfPrevCellModes$[field]) == null ? void 0 : _copyOfPrevCellModes$.mode) || GridCellModes.View;\n        const originalId = (_idToIdLookup$id = idToIdLookup[id]) != null ? _idToIdLookup$id : id;\n        if (params.mode === GridCellModes.Edit && prevMode === GridCellModes.View) {\n          updateStateToStartCellEditMode(_extends({\n            id: originalId,\n            field\n          }, params));\n        } else if (params.mode === GridCellModes.View && prevMode === GridCellModes.Edit) {\n          updateStateToStopCellEditMode(_extends({\n            id: originalId,\n            field\n          }, params));\n        }\n      });\n    });\n  }, [apiRef, cellModesModel, updateStateToStartCellEditMode, updateStateToStopCellEditMode]);\n};"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC;EAC/BC,UAAU,GAAG,CAAC,IAAI,EAAE,OAAO,CAAC;AAC9B,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,yBAAyB,IAAIC,gBAAgB,EAAEC,0BAA0B,IAAIC,iBAAiB,QAAQ,YAAY;AAC3H,SAASC,sBAAsB,EAAEC,uBAAuB,QAAQ,oCAAoC;AACpG,SAASC,aAAa,EAAEC,aAAa,QAAQ,kCAAkC;AAC/E,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,yBAAyB,QAAQ,wBAAwB;AAClE,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,mCAAmC,QAAQ,0BAA0B;AAC9E,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,wBAAwB,EAAEC,uBAAuB,QAAQ,2CAA2C;AAC7G,MAAMC,qCAAqC,GAAGL,YAAY,CAAC,CAAC,sHAAsH,EAAE,0IAA0I,EAAE,4FAA4F,CAAC,EAAE,OAAO,CAAC;AACva,OAAO,MAAMM,kBAAkB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EACnD,MAAM,CAACC,cAAc,EAAEC,iBAAiB,CAAC,GAAGtB,KAAK,CAACuB,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC9D,MAAMC,iBAAiB,GAAGxB,KAAK,CAACyB,MAAM,CAACJ,cAAc,CAAC;EACtD,MAAMK,kBAAkB,GAAG1B,KAAK,CAACyB,MAAM,CAAC,CAAC,CAAC,CAAC;EAC3C,MAAM;IACJE,gBAAgB;IAChBC,uBAAuB;IACvBP,cAAc,EAAEQ,kBAAkB;IAClCC;EACF,CAAC,GAAGV,KAAK;EACT,MAAMW,mBAAmB,GAAGC,QAAQ,IAAI,CAAC,GAAGC,IAAI,KAAK;IACnD,IAAIb,KAAK,CAACc,QAAQ,KAAK3B,aAAa,CAAC4B,IAAI,EAAE;MACzCH,QAAQ,CAAC,GAAGC,IAAI,CAAC;IACnB;EACF,CAAC;EACD,MAAMG,kBAAkB,GAAGpC,KAAK,CAACqC,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,KAAK;IAC1D,MAAMC,MAAM,GAAGrB,MAAM,CAACsB,OAAO,CAACC,aAAa,CAACJ,EAAE,EAAEC,KAAK,CAAC;IACtD,IAAI,CAACpB,MAAM,CAACsB,OAAO,CAACE,cAAc,CAACH,MAAM,CAAC,EAAE;MAC1C,MAAM,IAAII,KAAK,CAAC,yBAAyBN,EAAE,cAAcC,KAAK,mBAAmB,CAAC;IACpF;EACF,CAAC,EAAE,CAACpB,MAAM,CAAC,CAAC;EACZ,MAAM0B,gBAAgB,GAAG7C,KAAK,CAACqC,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,EAAEO,IAAI,KAAK;IAC9D,IAAI3B,MAAM,CAACsB,OAAO,CAACM,WAAW,CAACT,EAAE,EAAEC,KAAK,CAAC,KAAKO,IAAI,EAAE;MAClD,MAAM,IAAIF,KAAK,CAAC,yBAAyBN,EAAE,cAAcC,KAAK,cAAcO,IAAI,QAAQ,CAAC;IAC3F;EACF,CAAC,EAAE,CAAC3B,MAAM,CAAC,CAAC;EACZ,MAAM6B,qBAAqB,GAAGhD,KAAK,CAACqC,WAAW,CAAC,CAACG,MAAM,EAAES,KAAK,KAAK;IACjE,IAAI,CAACT,MAAM,CAACU,UAAU,EAAE;MACtB;IACF;IACA,IAAIV,MAAM,CAACW,QAAQ,KAAK3C,aAAa,CAAC4C,IAAI,EAAE;MAC1C;IACF;IACA,MAAMC,SAAS,GAAGxD,QAAQ,CAAC,CAAC,CAAC,EAAE2C,MAAM,EAAE;MACrCc,MAAM,EAAEvC,wBAAwB,CAACwC;IACnC,CAAC,CAAC;IACFpC,MAAM,CAACsB,OAAO,CAACe,YAAY,CAAC,eAAe,EAAEH,SAAS,EAAEJ,KAAK,CAAC;EAChE,CAAC,EAAE,CAAC9B,MAAM,CAAC,CAAC;EACZ,MAAMsC,kBAAkB,GAAGzD,KAAK,CAACqC,WAAW,CAAC,CAACG,MAAM,EAAES,KAAK,KAAK;IAC9D,IAAIT,MAAM,CAACW,QAAQ,KAAK3C,aAAa,CAACkD,IAAI,EAAE;MAC1C;IACF;IACA,IAAIvC,MAAM,CAACsB,OAAO,CAACM,WAAW,CAACP,MAAM,CAACF,EAAE,EAAEE,MAAM,CAACD,KAAK,CAAC,KAAK/B,aAAa,CAACkD,IAAI,EAAE;MAC9E;IACF;IACA,MAAML,SAAS,GAAGxD,QAAQ,CAAC,CAAC,CAAC,EAAE2C,MAAM,EAAE;MACrCc,MAAM,EAAEtC,uBAAuB,CAAC2C;IAClC,CAAC,CAAC;IACFxC,MAAM,CAACsB,OAAO,CAACe,YAAY,CAAC,cAAc,EAAEH,SAAS,EAAEJ,KAAK,CAAC;EAC/D,CAAC,EAAE,CAAC9B,MAAM,CAAC,CAAC;EACZ,MAAMyC,iBAAiB,GAAG5D,KAAK,CAACqC,WAAW,CAAC,CAACG,MAAM,EAAES,KAAK,KAAK;IAC7D,IAAIT,MAAM,CAACW,QAAQ,KAAK3C,aAAa,CAAC4C,IAAI,EAAE;MAC1C;MACA;MACA,IAAIH,KAAK,CAACY,KAAK,KAAK,GAAG,EAAE;QACvB;MACF;MACA,IAAIP,MAAM;MACV,IAAIL,KAAK,CAACa,GAAG,KAAK,QAAQ,EAAE;QAC1BR,MAAM,GAAGtC,uBAAuB,CAAC+C,aAAa;MAChD,CAAC,MAAM,IAAId,KAAK,CAACa,GAAG,KAAK,OAAO,EAAE;QAChCR,MAAM,GAAGtC,uBAAuB,CAACgD,YAAY;MAC/C,CAAC,MAAM,IAAIf,KAAK,CAACa,GAAG,KAAK,KAAK,EAAE;QAC9BR,MAAM,GAAGL,KAAK,CAACgB,QAAQ,GAAGjD,uBAAuB,CAACkD,eAAe,GAAGlD,uBAAuB,CAACmD,UAAU;QACtGlB,KAAK,CAACmB,cAAc,CAAC,CAAC,CAAC,CAAC;MAC1B;MACA,IAAId,MAAM,EAAE;QACV,MAAMD,SAAS,GAAGxD,QAAQ,CAAC,CAAC,CAAC,EAAE2C,MAAM,EAAE;UACrCc;QACF,CAAC,CAAC;QACFnC,MAAM,CAACsB,OAAO,CAACe,YAAY,CAAC,cAAc,EAAEH,SAAS,EAAEJ,KAAK,CAAC;MAC/D;IACF,CAAC,MAAM,IAAIT,MAAM,CAACU,UAAU,EAAE;MAC5B,IAAII,MAAM;MACV,MAAMe,eAAe,GAAGlD,MAAM,CAACsB,OAAO,CAAC6B,4BAA4B,CAAC,iBAAiB,EAAE,IAAI,EAAE;QAC3FrB,KAAK;QACLsB,UAAU,EAAE/B,MAAM;QAClBN,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF,IAAI,CAACmC,eAAe,EAAE;QACpB;MACF;MACA,IAAI1D,cAAc,CAACsC,KAAK,CAAC,EAAE;QACzBK,MAAM,GAAGvC,wBAAwB,CAACyD,gBAAgB;MACpD,CAAC,MAAM,IAAI,CAACvB,KAAK,CAACwB,OAAO,IAAIxB,KAAK,CAACyB,OAAO,KAAKzB,KAAK,CAACa,GAAG,KAAK,GAAG,EAAE;QAChER,MAAM,GAAGvC,wBAAwB,CAAC4D,YAAY;MAChD,CAAC,MAAM,IAAI1B,KAAK,CAACa,GAAG,KAAK,OAAO,EAAE;QAChCR,MAAM,GAAGvC,wBAAwB,CAACiD,YAAY;MAChD,CAAC,MAAM,IAAIf,KAAK,CAACa,GAAG,KAAK,QAAQ,IAAIb,KAAK,CAACa,GAAG,KAAK,WAAW,EAAE;QAC9D;QACAR,MAAM,GAAGvC,wBAAwB,CAAC6D,aAAa;MACjD;MACA,IAAItB,MAAM,EAAE;QACV,MAAMD,SAAS,GAAGxD,QAAQ,CAAC,CAAC,CAAC,EAAE2C,MAAM,EAAE;UACrCc,MAAM;UACNQ,GAAG,EAAEb,KAAK,CAACa;QACb,CAAC,CAAC;QACF3C,MAAM,CAACsB,OAAO,CAACe,YAAY,CAAC,eAAe,EAAEH,SAAS,EAAEJ,KAAK,CAAC;MAChE;IACF;EACF,CAAC,EAAE,CAAC9B,MAAM,CAAC,CAAC;EACZ,MAAM0D,mBAAmB,GAAG7E,KAAK,CAACqC,WAAW,CAACG,MAAM,IAAI;IACtD,MAAM;MACJF,EAAE;MACFC,KAAK;MACLe;IACF,CAAC,GAAGd,MAAM;IACV,MAAMsC,uBAAuB,GAAG;MAC9BxC,EAAE;MACFC;IACF,CAAC;IACD,IAAIe,MAAM,KAAKvC,wBAAwB,CAACyD,gBAAgB,IAAIlB,MAAM,KAAKvC,wBAAwB,CAAC6D,aAAa,IAAItB,MAAM,KAAKvC,wBAAwB,CAAC4D,YAAY,EAAE;MACjKG,uBAAuB,CAACC,WAAW,GAAG,IAAI;IAC5C;IACA5D,MAAM,CAACsB,OAAO,CAACuC,iBAAiB,CAACF,uBAAuB,CAAC;EAC3D,CAAC,EAAE,CAAC3D,MAAM,CAAC,CAAC;EACZ,MAAM8D,kBAAkB,GAAGjF,KAAK,CAACqC,WAAW,CAACG,MAAM,IAAI;IACrD,MAAM;MACJF,EAAE;MACFC,KAAK;MACLe;IACF,CAAC,GAAGd,MAAM;IACVrB,MAAM,CAACsB,OAAO,CAACyC,+BAA+B,CAAC5C,EAAE,EAAEC,KAAK,CAAC;IACzD,IAAI4C,gBAAgB;IACpB,IAAI7B,MAAM,KAAKtC,uBAAuB,CAACgD,YAAY,EAAE;MACnDmB,gBAAgB,GAAG,OAAO;IAC5B,CAAC,MAAM,IAAI7B,MAAM,KAAKtC,uBAAuB,CAACmD,UAAU,EAAE;MACxDgB,gBAAgB,GAAG,OAAO;IAC5B,CAAC,MAAM,IAAI7B,MAAM,KAAKtC,uBAAuB,CAACkD,eAAe,EAAE;MAC7DiB,gBAAgB,GAAG,MAAM;IAC3B;IACA,MAAMC,mBAAmB,GAAG9B,MAAM,KAAK,eAAe;IACtDnC,MAAM,CAACsB,OAAO,CAAC4C,gBAAgB,CAAC;MAC9B/C,EAAE;MACFC,KAAK;MACL6C,mBAAmB;MACnBD;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAChE,MAAM,CAAC,CAAC;EACZd,sBAAsB,CAACc,MAAM,EAAE,iBAAiB,EAAEY,mBAAmB,CAACiB,qBAAqB,CAAC,CAAC;EAC7F3C,sBAAsB,CAACc,MAAM,EAAE,cAAc,EAAEY,mBAAmB,CAAC0B,kBAAkB,CAAC,CAAC;EACvFpD,sBAAsB,CAACc,MAAM,EAAE,aAAa,EAAEY,mBAAmB,CAAC6B,iBAAiB,CAAC,CAAC;EACrFvD,sBAAsB,CAACc,MAAM,EAAE,eAAe,EAAEY,mBAAmB,CAAC8C,mBAAmB,CAAC,CAAC;EACzFxE,sBAAsB,CAACc,MAAM,EAAE,cAAc,EAAEY,mBAAmB,CAACkD,kBAAkB,CAAC,CAAC;EACvF3E,uBAAuB,CAACa,MAAM,EAAE,eAAe,EAAEC,KAAK,CAACkE,eAAe,CAAC;EACvEhF,uBAAuB,CAACa,MAAM,EAAE,cAAc,EAAEC,KAAK,CAACmE,cAAc,CAAC;EACrE,MAAMxC,WAAW,GAAG/C,KAAK,CAACqC,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,KAAK;IACnD,MAAMiD,YAAY,GAAG9E,yBAAyB,CAACS,MAAM,CAACsB,OAAO,CAACgD,KAAK,CAAC;IACpE,MAAMC,SAAS,GAAGF,YAAY,CAAClD,EAAE,CAAC,IAAIkD,YAAY,CAAClD,EAAE,CAAC,CAACC,KAAK,CAAC;IAC7D,OAAOmD,SAAS,GAAGlF,aAAa,CAAC4C,IAAI,GAAG5C,aAAa,CAACkD,IAAI;EAC5D,CAAC,EAAE,CAACvC,MAAM,CAAC,CAAC;EACZ,MAAMwE,oBAAoB,GAAGzF,gBAAgB,CAAC0F,QAAQ,IAAI;IACxD,MAAMC,2BAA2B,GAAGD,QAAQ,KAAKxE,KAAK,CAACC,cAAc;IACrE,IAAIS,sBAAsB,IAAI+D,2BAA2B,EAAE;MACzD/D,sBAAsB,CAAC8D,QAAQ,EAAE,CAAC,CAAC,CAAC;IACtC;IACA,IAAIxE,KAAK,CAACC,cAAc,IAAIwE,2BAA2B,EAAE;MACvD,OAAO,CAAC;IACV;IACAvE,iBAAiB,CAACsE,QAAQ,CAAC;IAC3BpE,iBAAiB,CAACiB,OAAO,GAAGmD,QAAQ;IACpCzE,MAAM,CAACsB,OAAO,CAACe,YAAY,CAAC,sBAAsB,EAAEoC,QAAQ,CAAC;EAC/D,CAAC,CAAC;EACF,MAAME,2BAA2B,GAAG9F,KAAK,CAACqC,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,EAAEwD,QAAQ,KAAK;IAC7E;IACA;IACA,MAAMH,QAAQ,GAAG/F,QAAQ,CAAC,CAAC,CAAC,EAAE2B,iBAAiB,CAACiB,OAAO,CAAC;IACxD,IAAIsD,QAAQ,KAAK,IAAI,EAAE;MACrBH,QAAQ,CAACtD,EAAE,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC,EAAE+F,QAAQ,CAACtD,EAAE,CAAC,EAAE;QACxC,CAACC,KAAK,GAAG1C,QAAQ,CAAC,CAAC,CAAC,EAAEkG,QAAQ;MAChC,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,MAAMC,YAAY,GAAGJ,QAAQ,CAACtD,EAAE,CAAC;QAC/B2D,WAAW,GAAGtG,6BAA6B,CAACqG,YAAY,EAAE,CAACzD,KAAK,CAAC,CAAC2D,GAAG,CAACtG,cAAc,CAAC,CAAC,CAAC,CAAC;MAC1FgG,QAAQ,CAACtD,EAAE,CAAC,GAAG2D,WAAW;MAC1B,IAAIE,MAAM,CAACC,IAAI,CAACR,QAAQ,CAACtD,EAAE,CAAC,CAAC,CAAC+D,MAAM,KAAK,CAAC,EAAE;QAC1C,OAAOT,QAAQ,CAACtD,EAAE,CAAC;MACrB;IACF;IACAqD,oBAAoB,CAACC,QAAQ,CAAC;EAChC,CAAC,EAAE,CAACD,oBAAoB,CAAC,CAAC;EAC1B,MAAMW,wBAAwB,GAAGtG,KAAK,CAACqC,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,EAAEwD,QAAQ,KAAK;IAC1E5E,MAAM,CAACsB,OAAO,CAAC8D,QAAQ,CAACd,KAAK,IAAI;MAC/B,MAAMe,eAAe,GAAG3G,QAAQ,CAAC,CAAC,CAAC,EAAE4F,KAAK,CAACgB,QAAQ,CAAC;MACpD,IAAIV,QAAQ,KAAK,IAAI,EAAE;QACrBS,eAAe,CAAClE,EAAE,CAAC,GAAGzC,QAAQ,CAAC,CAAC,CAAC,EAAE2G,eAAe,CAAClE,EAAE,CAAC,EAAE;UACtD,CAACC,KAAK,GAAG1C,QAAQ,CAAC,CAAC,CAAC,EAAEkG,QAAQ;QAChC,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,OAAOS,eAAe,CAAClE,EAAE,CAAC,CAACC,KAAK,CAAC;QACjC,IAAI4D,MAAM,CAACC,IAAI,CAACI,eAAe,CAAClE,EAAE,CAAC,CAAC,CAAC+D,MAAM,KAAK,CAAC,EAAE;UACjD,OAAOG,eAAe,CAAClE,EAAE,CAAC;QAC5B;MACF;MACA,OAAOzC,QAAQ,CAAC,CAAC,CAAC,EAAE4F,KAAK,EAAE;QACzBgB,QAAQ,EAAED;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IACFrF,MAAM,CAACsB,OAAO,CAACiE,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACvF,MAAM,CAAC,CAAC;EACZ,MAAM6D,iBAAiB,GAAGhF,KAAK,CAACqC,WAAW,CAACG,MAAM,IAAI;IACpD,MAAM;QACFF,EAAE;QACFC;MACF,CAAC,GAAGC,MAAM;MACVmE,KAAK,GAAGhH,6BAA6B,CAAC6C,MAAM,EAAE1C,SAAS,CAAC;IAC1DsC,kBAAkB,CAACE,EAAE,EAAEC,KAAK,CAAC;IAC7BM,gBAAgB,CAACP,EAAE,EAAEC,KAAK,EAAE/B,aAAa,CAACkD,IAAI,CAAC;IAC/CoC,2BAA2B,CAACxD,EAAE,EAAEC,KAAK,EAAE1C,QAAQ,CAAC;MAC9CiD,IAAI,EAAEtC,aAAa,CAAC4C;IACtB,CAAC,EAAEuD,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAACvE,kBAAkB,EAAES,gBAAgB,EAAEiD,2BAA2B,CAAC,CAAC;EACvE,MAAMc,8BAA8B,GAAG1G,gBAAgB,CAACsC,MAAM,IAAI;IAChE,MAAM;MACJF,EAAE;MACFC,KAAK;MACLwC,WAAW;MACX8B;IACF,CAAC,GAAGrE,MAAM;IACV,IAAIsE,QAAQ,GAAG3F,MAAM,CAACsB,OAAO,CAACsE,YAAY,CAACzE,EAAE,EAAEC,KAAK,CAAC;IACrD,IAAIwC,WAAW,IAAI8B,YAAY,EAAE;MAC/BC,QAAQ,GAAG/B,WAAW,GAAG,EAAE,GAAG8B,YAAY;IAC5C;IACA,MAAMd,QAAQ,GAAG;MACfiB,KAAK,EAAEF,QAAQ;MACfG,KAAK,EAAE,KAAK;MACZC,iBAAiB,EAAE;IACrB,CAAC;IACDZ,wBAAwB,CAAChE,EAAE,EAAEC,KAAK,EAAEwD,QAAQ,CAAC;IAC7C5E,MAAM,CAACsB,OAAO,CAAC0E,YAAY,CAAC7E,EAAE,EAAEC,KAAK,CAAC;EACxC,CAAC,CAAC;EACF,MAAM8C,gBAAgB,GAAGrF,KAAK,CAACqC,WAAW,CAACG,MAAM,IAAI;IACnD,MAAM;QACFF,EAAE;QACFC;MACF,CAAC,GAAGC,MAAM;MACVmE,KAAK,GAAGhH,6BAA6B,CAAC6C,MAAM,EAAEzC,UAAU,CAAC;IAC3D8C,gBAAgB,CAACP,EAAE,EAAEC,KAAK,EAAE/B,aAAa,CAAC4C,IAAI,CAAC;IAC/C0C,2BAA2B,CAACxD,EAAE,EAAEC,KAAK,EAAE1C,QAAQ,CAAC;MAC9CiD,IAAI,EAAEtC,aAAa,CAACkD;IACtB,CAAC,EAAEiD,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC9D,gBAAgB,EAAEiD,2BAA2B,CAAC,CAAC;EACnD,MAAMsB,6BAA6B,GAAGlH,gBAAgB,CAAC,MAAMsC,MAAM,IAAI;IACrE,MAAM;MACJF,EAAE;MACFC,KAAK;MACL6C,mBAAmB;MACnBD,gBAAgB,GAAG;IACrB,CAAC,GAAG3C,MAAM;IACVK,gBAAgB,CAACP,EAAE,EAAEC,KAAK,EAAE/B,aAAa,CAAC4C,IAAI,CAAC;IAC/CjC,MAAM,CAACsB,OAAO,CAACyC,+BAA+B,CAAC5C,EAAE,EAAEC,KAAK,CAAC;IACzD,MAAM8E,kBAAkB,GAAGA,CAAA,KAAM;MAC/Bf,wBAAwB,CAAChE,EAAE,EAAEC,KAAK,EAAE,IAAI,CAAC;MACzCuD,2BAA2B,CAACxD,EAAE,EAAEC,KAAK,EAAE,IAAI,CAAC;MAC5C,IAAI4C,gBAAgB,KAAK,MAAM,EAAE;QAC/BhE,MAAM,CAACsB,OAAO,CAAC6E,uBAAuB,CAAChF,EAAE,EAAEC,KAAK,EAAE4C,gBAAgB,CAAC;MACrE;IACF,CAAC;IACD,IAAIC,mBAAmB,EAAE;MACvBiC,kBAAkB,CAAC,CAAC;MACpB;IACF;IACA,MAAM7B,YAAY,GAAG9E,yBAAyB,CAACS,MAAM,CAACsB,OAAO,CAACgD,KAAK,CAAC;IACpE,MAAM;MACJwB,KAAK;MACLC;IACF,CAAC,GAAG1B,YAAY,CAAClD,EAAE,CAAC,CAACC,KAAK,CAAC;IAC3B,IAAI0E,KAAK,IAAIC,iBAAiB,EAAE;MAC9B;MACA;MACAxF,kBAAkB,CAACe,OAAO,CAACH,EAAE,CAAC,CAACC,KAAK,CAAC,CAACO,IAAI,GAAGtC,aAAa,CAAC4C,IAAI;MAC/D;MACA0C,2BAA2B,CAACxD,EAAE,EAAEC,KAAK,EAAE;QACrCO,IAAI,EAAEtC,aAAa,CAAC4C;MACtB,CAAC,CAAC;MACF;IACF;IACA,MAAMmE,SAAS,GAAGpG,MAAM,CAACsB,OAAO,CAAC+E,sCAAsC,CAAClF,EAAE,EAAEC,KAAK,CAAC;IAClF,IAAIZ,gBAAgB,EAAE;MACpB,MAAM8F,WAAW,GAAGC,WAAW,IAAI;QACjChG,kBAAkB,CAACe,OAAO,CAACH,EAAE,CAAC,CAACC,KAAK,CAAC,CAACO,IAAI,GAAGtC,aAAa,CAAC4C,IAAI;QAC/D;QACA0C,2BAA2B,CAACxD,EAAE,EAAEC,KAAK,EAAE;UACrCO,IAAI,EAAEtC,aAAa,CAAC4C;QACtB,CAAC,CAAC;QACF,IAAIxB,uBAAuB,EAAE;UAC3BA,uBAAuB,CAAC8F,WAAW,CAAC;QACtC,CAAC,MAAM;UACLzG,qCAAqC,CAAC,CAAC;QACzC;MACF,CAAC;MACD,IAAI;QACF,MAAM0G,GAAG,GAAGxG,MAAM,CAACsB,OAAO,CAACmF,MAAM,CAACtF,EAAE,CAAC;QACrCuF,OAAO,CAACC,OAAO,CAACnG,gBAAgB,CAAC4F,SAAS,EAAEI,GAAG,CAAC,CAAC,CAACI,IAAI,CAACC,cAAc,IAAI;UACvE7G,MAAM,CAACsB,OAAO,CAACwF,UAAU,CAAC,CAACD,cAAc,CAAC,CAAC;UAC3CX,kBAAkB,CAAC,CAAC;QACtB,CAAC,CAAC,CAACa,KAAK,CAACT,WAAW,CAAC;MACvB,CAAC,CAAC,OAAOC,WAAW,EAAE;QACpBD,WAAW,CAACC,WAAW,CAAC;MAC1B;IACF,CAAC,MAAM;MACLvG,MAAM,CAACsB,OAAO,CAACwF,UAAU,CAAC,CAACV,SAAS,CAAC,CAAC;MACtCF,kBAAkB,CAAC,CAAC;IACtB;EACF,CAAC,CAAC;EACF,MAAMc,2BAA2B,GAAGnI,KAAK,CAACqC,WAAW,CAAC,MAAMG,MAAM,IAAI;IACpE,IAAI4F,gBAAgB;IACpB,MAAM;MACJ9F,EAAE;MACFC,KAAK;MACLyE,KAAK;MACLqB,UAAU;MACVC,wBAAwB,EAAEC;IAC5B,CAAC,GAAG/F,MAAM;IACVJ,kBAAkB,CAACE,EAAE,EAAEC,KAAK,CAAC;IAC7BM,gBAAgB,CAACP,EAAE,EAAEC,KAAK,EAAE/B,aAAa,CAAC4C,IAAI,CAAC;IAC/C,MAAMoF,MAAM,GAAGrH,MAAM,CAACsB,OAAO,CAACgG,SAAS,CAAClG,KAAK,CAAC;IAC9C,MAAMoF,GAAG,GAAGxG,MAAM,CAACsB,OAAO,CAACmF,MAAM,CAACtF,EAAE,CAAC;IACrC,IAAIoG,WAAW,GAAG1B,KAAK;IACvB,IAAIwB,MAAM,CAACG,WAAW,IAAI,CAACJ,eAAe,EAAE;MAC1CG,WAAW,GAAGF,MAAM,CAACG,WAAW,CAAC3B,KAAK,EAAE7F,MAAM,CAACsB,OAAO,CAACC,aAAa,CAACJ,EAAE,EAAEC,KAAK,CAAC,CAAC;IAClF;IACA,IAAIiD,YAAY,GAAG9E,yBAAyB,CAACS,MAAM,CAACsB,OAAO,CAACgD,KAAK,CAAC;IAClE,IAAIM,QAAQ,GAAGlG,QAAQ,CAAC,CAAC,CAAC,EAAE2F,YAAY,CAAClD,EAAE,CAAC,CAACC,KAAK,CAAC,EAAE;MACnDyE,KAAK,EAAE0B,WAAW;MAClBE,YAAY,EAAEP,UAAU,GAAG,2BAA2B,GAAG;IAC3D,CAAC,CAAC;IACF,IAAIG,MAAM,CAACK,uBAAuB,EAAE;MAClC,MAAMC,UAAU,GAAG9B,KAAK,KAAKxB,YAAY,CAAClD,EAAE,CAAC,CAACC,KAAK,CAAC,CAACyE,KAAK;MAC1DjB,QAAQ,GAAGlG,QAAQ,CAAC,CAAC,CAAC,EAAEkG,QAAQ,EAAE;QAChCmB,iBAAiB,EAAE;MACrB,CAAC,CAAC;MACFZ,wBAAwB,CAAChE,EAAE,EAAEC,KAAK,EAAEwD,QAAQ,CAAC;MAC7CA,QAAQ,GAAG,MAAM8B,OAAO,CAACC,OAAO,CAACU,MAAM,CAACK,uBAAuB,CAAC;QAC9DvG,EAAE;QACFqF,GAAG;QACHvG,KAAK,EAAE2E,QAAQ;QACf+C;MACF,CAAC,CAAC,CAAC;IACL;;IAEA;IACA;IACA,IAAI3H,MAAM,CAACsB,OAAO,CAACM,WAAW,CAACT,EAAE,EAAEC,KAAK,CAAC,KAAK/B,aAAa,CAACkD,IAAI,EAAE;MAChE,OAAO,KAAK;IACd;IACA8B,YAAY,GAAG9E,yBAAyB,CAACS,MAAM,CAACsB,OAAO,CAACgD,KAAK,CAAC;IAC9DM,QAAQ,GAAGlG,QAAQ,CAAC,CAAC,CAAC,EAAEkG,QAAQ,EAAE;MAChCmB,iBAAiB,EAAE;IACrB,CAAC,CAAC;IACF;IACA;IACA;IACAnB,QAAQ,CAACiB,KAAK,GAAGwB,MAAM,CAACK,uBAAuB,GAAGrD,YAAY,CAAClD,EAAE,CAAC,CAACC,KAAK,CAAC,CAACyE,KAAK,GAAG0B,WAAW;IAC7FpC,wBAAwB,CAAChE,EAAE,EAAEC,KAAK,EAAEwD,QAAQ,CAAC;IAC7CP,YAAY,GAAG9E,yBAAyB,CAACS,MAAM,CAACsB,OAAO,CAACgD,KAAK,CAAC;IAC9D,OAAO,EAAE,CAAC2C,gBAAgB,GAAG5C,YAAY,CAAClD,EAAE,CAAC,KAAK,IAAI,IAAI,CAAC8F,gBAAgB,GAAGA,gBAAgB,CAAC7F,KAAK,CAAC,KAAK,IAAI,IAAI6F,gBAAgB,CAACnB,KAAK,CAAC;EAC3I,CAAC,EAAE,CAAC9F,MAAM,EAAEiB,kBAAkB,EAAES,gBAAgB,EAAEyD,wBAAwB,CAAC,CAAC;EAC5E,MAAMkB,sCAAsC,GAAGxH,KAAK,CAACqC,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,KAAK;IAC9E,MAAMiG,MAAM,GAAGrH,MAAM,CAACsB,OAAO,CAACgG,SAAS,CAAClG,KAAK,CAAC;IAC9C,MAAMiD,YAAY,GAAG9E,yBAAyB,CAACS,MAAM,CAACsB,OAAO,CAACgD,KAAK,CAAC;IACpE,MAAMkC,GAAG,GAAGxG,MAAM,CAACsB,OAAO,CAACmF,MAAM,CAACtF,EAAE,CAAC;IACrC,IAAI,CAACkD,YAAY,CAAClD,EAAE,CAAC,IAAI,CAACkD,YAAY,CAAClD,EAAE,CAAC,CAACC,KAAK,CAAC,EAAE;MACjD,OAAOpB,MAAM,CAACsB,OAAO,CAACmF,MAAM,CAACtF,EAAE,CAAC;IAClC;IACA,MAAM;MACJ0E;IACF,CAAC,GAAGxB,YAAY,CAAClD,EAAE,CAAC,CAACC,KAAK,CAAC;IAC3B,OAAOiG,MAAM,CAACO,WAAW,GAAGP,MAAM,CAACO,WAAW,CAAC;MAC7C/B,KAAK;MACLW;IACF,CAAC,CAAC,GAAG9H,QAAQ,CAAC,CAAC,CAAC,EAAE8H,GAAG,EAAE;MACrB,CAACpF,KAAK,GAAGyE;IACX,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC7F,MAAM,CAAC,CAAC;EACZ,MAAM6H,UAAU,GAAG;IACjBjG,WAAW;IACXiC,iBAAiB;IACjBK;EACF,CAAC;EACD,MAAM4D,iBAAiB,GAAG;IACxBd,2BAA2B;IAC3BX;EACF,CAAC;EACD/G,gBAAgB,CAACU,MAAM,EAAE6H,UAAU,EAAE,QAAQ,CAAC;EAC9CvI,gBAAgB,CAACU,MAAM,EAAE8H,iBAAiB,EAAE,SAAS,CAAC;EACtDjJ,KAAK,CAACkJ,SAAS,CAAC,MAAM;IACpB,IAAIrH,kBAAkB,EAAE;MACtB8D,oBAAoB,CAAC9D,kBAAkB,CAAC;IAC1C;EACF,CAAC,EAAE,CAACA,kBAAkB,EAAE8D,oBAAoB,CAAC,CAAC;;EAE9C;EACAvF,iBAAiB,CAAC,MAAM;IACtB,MAAM+I,YAAY,GAAGtI,mCAAmC,CAACM,MAAM,CAAC;;IAEhE;IACA,MAAMiI,mBAAmB,GAAG1H,kBAAkB,CAACe,OAAO;IACtDf,kBAAkB,CAACe,OAAO,GAAG3B,SAAS,CAACO,cAAc,CAAC,CAAC,CAAC;;IAExD8E,MAAM,CAACkD,OAAO,CAAChI,cAAc,CAAC,CAACiI,OAAO,CAAC,CAAC,CAAChH,EAAE,EAAEiH,MAAM,CAAC,KAAK;MACvDpD,MAAM,CAACkD,OAAO,CAACE,MAAM,CAAC,CAACD,OAAO,CAAC,CAAC,CAAC/G,KAAK,EAAEC,MAAM,CAAC,KAAK;QAClD,IAAIgH,qBAAqB,EAAEC,gBAAgB;QAC3C,MAAMC,QAAQ,GAAG,CAAC,CAACF,qBAAqB,GAAGJ,mBAAmB,CAAC9G,EAAE,CAAC,KAAK,IAAI,IAAI,CAACkH,qBAAqB,GAAGA,qBAAqB,CAACjH,KAAK,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGiH,qBAAqB,CAAC1G,IAAI,KAAKtC,aAAa,CAACkD,IAAI;QAC1M,MAAMiG,UAAU,GAAG,CAACF,gBAAgB,GAAGN,YAAY,CAAC7G,EAAE,CAAC,KAAK,IAAI,GAAGmH,gBAAgB,GAAGnH,EAAE;QACxF,IAAIE,MAAM,CAACM,IAAI,KAAKtC,aAAa,CAAC4C,IAAI,IAAIsG,QAAQ,KAAKlJ,aAAa,CAACkD,IAAI,EAAE;UACzEkD,8BAA8B,CAAC/G,QAAQ,CAAC;YACtCyC,EAAE,EAAEqH,UAAU;YACdpH;UACF,CAAC,EAAEC,MAAM,CAAC,CAAC;QACb,CAAC,MAAM,IAAIA,MAAM,CAACM,IAAI,KAAKtC,aAAa,CAACkD,IAAI,IAAIgG,QAAQ,KAAKlJ,aAAa,CAAC4C,IAAI,EAAE;UAChFgE,6BAA6B,CAACvH,QAAQ,CAAC;YACrCyC,EAAE,EAAEqH,UAAU;YACdpH;UACF,CAAC,EAAEC,MAAM,CAAC,CAAC;QACb;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACrB,MAAM,EAAEE,cAAc,EAAEuF,8BAA8B,EAAEQ,6BAA6B,CAAC,CAAC;AAC7F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}