{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Server = void 0;\nconst connection_1 = require(\"../cmap/connection\");\nconst connection_pool_1 = require(\"../cmap/connection_pool\");\nconst errors_1 = require(\"../cmap/errors\");\nconst constants_1 = require(\"../constants\");\nconst error_1 = require(\"../error\");\nconst mongo_types_1 = require(\"../mongo_types\");\nconst transactions_1 = require(\"../transactions\");\nconst utils_1 = require(\"../utils\");\nconst write_concern_1 = require(\"../write_concern\");\nconst common_1 = require(\"./common\");\nconst monitor_1 = require(\"./monitor\");\nconst server_description_1 = require(\"./server_description\");\nconst stateTransition = (0, utils_1.makeStateMachine)({\n  [common_1.STATE_CLOSED]: [common_1.STATE_CLOSED, common_1.STATE_CONNECTING],\n  [common_1.STATE_CONNECTING]: [common_1.STATE_CONNECTING, common_1.STATE_CLOSING, common_1.STATE_CONNECTED, common_1.STATE_CLOSED],\n  [common_1.STATE_CONNECTED]: [common_1.STATE_CONNECTED, common_1.STATE_CLOSING, common_1.STATE_CLOSED],\n  [common_1.STATE_CLOSING]: [common_1.STATE_CLOSING, common_1.STATE_CLOSED]\n});\n/** @internal */\nclass Server extends mongo_types_1.TypedEventEmitter {\n  /**\n   * Create a server\n   */\n  constructor(topology, description, options) {\n    super();\n    this.on('error', utils_1.noop);\n    this.serverApi = options.serverApi;\n    const poolOptions = {\n      hostAddress: description.hostAddress,\n      ...options\n    };\n    this.topology = topology;\n    this.pool = new connection_pool_1.ConnectionPool(this, poolOptions);\n    this.s = {\n      description,\n      options,\n      state: common_1.STATE_CLOSED,\n      operationCount: 0\n    };\n    for (const event of [...constants_1.CMAP_EVENTS, ...constants_1.APM_EVENTS]) {\n      this.pool.on(event, e => this.emit(event, e));\n    }\n    this.pool.on(connection_1.Connection.CLUSTER_TIME_RECEIVED, clusterTime => {\n      this.clusterTime = clusterTime;\n    });\n    if (this.loadBalanced) {\n      this.monitor = null;\n      // monitoring is disabled in load balancing mode\n      return;\n    }\n    // create the monitor\n    this.monitor = new monitor_1.Monitor(this, this.s.options);\n    for (const event of constants_1.HEARTBEAT_EVENTS) {\n      this.monitor.on(event, e => this.emit(event, e));\n    }\n    this.monitor.on('resetServer', error => markServerUnknown(this, error));\n    this.monitor.on(Server.SERVER_HEARTBEAT_SUCCEEDED, event => {\n      this.emit(Server.DESCRIPTION_RECEIVED, new server_description_1.ServerDescription(this.description.hostAddress, event.reply, {\n        roundTripTime: this.monitor?.roundTripTime,\n        minRoundTripTime: this.monitor?.minRoundTripTime\n      }));\n      if (this.s.state === common_1.STATE_CONNECTING) {\n        stateTransition(this, common_1.STATE_CONNECTED);\n        this.emit(Server.CONNECT, this);\n      }\n    });\n  }\n  get clusterTime() {\n    return this.topology.clusterTime;\n  }\n  set clusterTime(clusterTime) {\n    this.topology.clusterTime = clusterTime;\n  }\n  get description() {\n    return this.s.description;\n  }\n  get name() {\n    return this.s.description.address;\n  }\n  get autoEncrypter() {\n    if (this.s.options && this.s.options.autoEncrypter) {\n      return this.s.options.autoEncrypter;\n    }\n    return;\n  }\n  get loadBalanced() {\n    return this.topology.description.type === common_1.TopologyType.LoadBalanced;\n  }\n  /**\n   * Initiate server connect\n   */\n  connect() {\n    if (this.s.state !== common_1.STATE_CLOSED) {\n      return;\n    }\n    stateTransition(this, common_1.STATE_CONNECTING);\n    // If in load balancer mode we automatically set the server to\n    // a load balancer. It never transitions out of this state and\n    // has no monitor.\n    if (!this.loadBalanced) {\n      this.monitor?.connect();\n    } else {\n      stateTransition(this, common_1.STATE_CONNECTED);\n      this.emit(Server.CONNECT, this);\n    }\n  }\n  /** Destroy the server connection */\n  destroy() {\n    if (this.s.state === common_1.STATE_CLOSED) {\n      return;\n    }\n    stateTransition(this, common_1.STATE_CLOSING);\n    if (!this.loadBalanced) {\n      this.monitor?.close();\n    }\n    this.pool.close();\n    stateTransition(this, common_1.STATE_CLOSED);\n    this.emit('closed');\n  }\n  /**\n   * Immediately schedule monitoring of this server. If there already an attempt being made\n   * this will be a no-op.\n   */\n  requestCheck() {\n    if (!this.loadBalanced) {\n      this.monitor?.requestCheck();\n    }\n  }\n  async command(ns, cmd, {\n    ...options\n  }, responseType) {\n    if (ns.db == null || typeof ns === 'string') {\n      throw new error_1.MongoInvalidArgumentError('Namespace must not be a string');\n    }\n    if (this.s.state === common_1.STATE_CLOSING || this.s.state === common_1.STATE_CLOSED) {\n      throw new error_1.MongoServerClosedError();\n    }\n    options.directConnection = this.topology.s.options.directConnection;\n    // There are cases where we need to flag the read preference not to get sent in\n    // the command, such as pre-5.0 servers attempting to perform an aggregate write\n    // with a non-primary read preference. In this case the effective read preference\n    // (primary) is not the same as the provided and must be removed completely.\n    if (options.omitReadPreference) {\n      delete options.readPreference;\n    }\n    if (this.description.iscryptd) {\n      options.omitMaxTimeMS = true;\n    }\n    const session = options.session;\n    let conn = session?.pinnedConnection;\n    this.incrementOperationCount();\n    if (conn == null) {\n      try {\n        conn = await this.pool.checkOut(options);\n        if (this.loadBalanced && isPinnableCommand(cmd, session)) {\n          session?.pin(conn);\n        }\n      } catch (checkoutError) {\n        this.decrementOperationCount();\n        if (!(checkoutError instanceof errors_1.PoolClearedError)) this.handleError(checkoutError);\n        throw checkoutError;\n      }\n    }\n    let reauthPromise = null;\n    try {\n      try {\n        const res = await conn.command(ns, cmd, options, responseType);\n        (0, write_concern_1.throwIfWriteConcernError)(res);\n        return res;\n      } catch (commandError) {\n        throw this.decorateCommandError(conn, cmd, options, commandError);\n      }\n    } catch (operationError) {\n      if (operationError instanceof error_1.MongoError && operationError.code === error_1.MONGODB_ERROR_CODES.Reauthenticate) {\n        reauthPromise = this.pool.reauthenticate(conn);\n        reauthPromise.then(undefined, error => {\n          reauthPromise = null;\n          (0, utils_1.squashError)(error);\n        });\n        await (0, utils_1.abortable)(reauthPromise, options);\n        reauthPromise = null; // only reachable if reauth succeeds\n        try {\n          const res = await conn.command(ns, cmd, options, responseType);\n          (0, write_concern_1.throwIfWriteConcernError)(res);\n          return res;\n        } catch (commandError) {\n          throw this.decorateCommandError(conn, cmd, options, commandError);\n        }\n      } else {\n        throw operationError;\n      }\n    } finally {\n      this.decrementOperationCount();\n      if (session?.pinnedConnection !== conn) {\n        if (reauthPromise != null) {\n          // The reauth promise only exists if it hasn't thrown.\n          const checkBackIn = () => {\n            this.pool.checkIn(conn);\n          };\n          void reauthPromise.then(checkBackIn, checkBackIn);\n        } else {\n          this.pool.checkIn(conn);\n        }\n      }\n    }\n  }\n  /**\n   * Handle SDAM error\n   * @internal\n   */\n  handleError(error, connection) {\n    if (!(error instanceof error_1.MongoError)) {\n      return;\n    }\n    const isStaleError = error.connectionGeneration && error.connectionGeneration < this.pool.generation;\n    if (isStaleError) {\n      return;\n    }\n    const isNetworkNonTimeoutError = error instanceof error_1.MongoNetworkError && !(error instanceof error_1.MongoNetworkTimeoutError);\n    const isNetworkTimeoutBeforeHandshakeError = error instanceof error_1.MongoNetworkError && error.beforeHandshake;\n    const isAuthHandshakeError = error.hasErrorLabel(error_1.MongoErrorLabel.HandshakeError);\n    if (isNetworkNonTimeoutError || isNetworkTimeoutBeforeHandshakeError || isAuthHandshakeError) {\n      // In load balanced mode we never mark the server as unknown and always\n      // clear for the specific service id.\n      if (!this.loadBalanced) {\n        error.addErrorLabel(error_1.MongoErrorLabel.ResetPool);\n        markServerUnknown(this, error);\n      } else if (connection) {\n        this.pool.clear({\n          serviceId: connection.serviceId\n        });\n      }\n    } else {\n      if ((0, error_1.isSDAMUnrecoverableError)(error)) {\n        if (shouldHandleStateChangeError(this, error)) {\n          const shouldClearPool = (0, utils_1.maxWireVersion)(this) <= 7 || (0, error_1.isNodeShuttingDownError)(error);\n          if (this.loadBalanced && connection && shouldClearPool) {\n            this.pool.clear({\n              serviceId: connection.serviceId\n            });\n          }\n          if (!this.loadBalanced) {\n            if (shouldClearPool) {\n              error.addErrorLabel(error_1.MongoErrorLabel.ResetPool);\n            }\n            markServerUnknown(this, error);\n            process.nextTick(() => this.requestCheck());\n          }\n        }\n      }\n    }\n  }\n  /**\n   * Ensure that error is properly decorated and internal state is updated before throwing\n   * @internal\n   */\n  decorateCommandError(connection, cmd, options, error) {\n    if (typeof error !== 'object' || error == null || !('name' in error)) {\n      throw new error_1.MongoRuntimeError('An unexpected error type: ' + typeof error);\n    }\n    if (error.name === 'AbortError' && 'cause' in error && error.cause instanceof error_1.MongoError) {\n      error = error.cause;\n    }\n    if (!(error instanceof error_1.MongoError)) {\n      // Node.js or some other error we have not special handling for\n      return error;\n    }\n    if (connectionIsStale(this.pool, connection)) {\n      return error;\n    }\n    const session = options?.session;\n    if (error instanceof error_1.MongoNetworkError) {\n      if (session && !session.hasEnded && session.serverSession) {\n        session.serverSession.isDirty = true;\n      }\n      // inActiveTransaction check handles commit and abort.\n      if (inActiveTransaction(session, cmd) && !error.hasErrorLabel(error_1.MongoErrorLabel.TransientTransactionError)) {\n        error.addErrorLabel(error_1.MongoErrorLabel.TransientTransactionError);\n      }\n      if ((isRetryableWritesEnabled(this.topology) || (0, transactions_1.isTransactionCommand)(cmd)) && (0, utils_1.supportsRetryableWrites)(this) && !inActiveTransaction(session, cmd)) {\n        error.addErrorLabel(error_1.MongoErrorLabel.RetryableWriteError);\n      }\n    } else {\n      if ((isRetryableWritesEnabled(this.topology) || (0, transactions_1.isTransactionCommand)(cmd)) && (0, error_1.needsRetryableWriteLabel)(error, (0, utils_1.maxWireVersion)(this), this.description.type) && !inActiveTransaction(session, cmd)) {\n        error.addErrorLabel(error_1.MongoErrorLabel.RetryableWriteError);\n      }\n    }\n    if (session && session.isPinned && error.hasErrorLabel(error_1.MongoErrorLabel.TransientTransactionError)) {\n      session.unpin({\n        force: true\n      });\n    }\n    this.handleError(error, connection);\n    return error;\n  }\n  /**\n   * Decrement the operation count, returning the new count.\n   */\n  decrementOperationCount() {\n    return this.s.operationCount -= 1;\n  }\n  /**\n   * Increment the operation count, returning the new count.\n   */\n  incrementOperationCount() {\n    return this.s.operationCount += 1;\n  }\n}\nexports.Server = Server;\n/** @event */\nServer.SERVER_HEARTBEAT_STARTED = constants_1.SERVER_HEARTBEAT_STARTED;\n/** @event */\nServer.SERVER_HEARTBEAT_SUCCEEDED = constants_1.SERVER_HEARTBEAT_SUCCEEDED;\n/** @event */\nServer.SERVER_HEARTBEAT_FAILED = constants_1.SERVER_HEARTBEAT_FAILED;\n/** @event */\nServer.CONNECT = constants_1.CONNECT;\n/** @event */\nServer.DESCRIPTION_RECEIVED = constants_1.DESCRIPTION_RECEIVED;\n/** @event */\nServer.CLOSED = constants_1.CLOSED;\n/** @event */\nServer.ENDED = constants_1.ENDED;\nfunction markServerUnknown(server, error) {\n  // Load balancer servers can never be marked unknown.\n  if (server.loadBalanced) {\n    return;\n  }\n  if (error instanceof error_1.MongoNetworkError && !(error instanceof error_1.MongoNetworkTimeoutError)) {\n    server.monitor?.reset();\n  }\n  server.emit(Server.DESCRIPTION_RECEIVED, new server_description_1.ServerDescription(server.description.hostAddress, undefined, {\n    error\n  }));\n}\nfunction isPinnableCommand(cmd, session) {\n  if (session) {\n    return session.inTransaction() || session.transaction.isCommitted && 'commitTransaction' in cmd || 'aggregate' in cmd || 'find' in cmd || 'getMore' in cmd || 'listCollections' in cmd || 'listIndexes' in cmd || 'bulkWrite' in cmd;\n  }\n  return false;\n}\nfunction connectionIsStale(pool, connection) {\n  if (connection.serviceId) {\n    return connection.generation !== pool.serviceGenerations.get(connection.serviceId.toHexString());\n  }\n  return connection.generation !== pool.generation;\n}\nfunction shouldHandleStateChangeError(server, err) {\n  const etv = err.topologyVersion;\n  const stv = server.description.topologyVersion;\n  return (0, server_description_1.compareTopologyVersion)(stv, etv) < 0;\n}\nfunction inActiveTransaction(session, cmd) {\n  return session && session.inTransaction() && !(0, transactions_1.isTransactionCommand)(cmd);\n}\n/** this checks the retryWrites option passed down from the client options, it\n * does not check if the server supports retryable writes */\nfunction isRetryableWritesEnabled(topology) {\n  return topology.s.options.retryWrites !== false;\n}", "map": {"version": 3, "names": ["connection_1", "require", "connection_pool_1", "errors_1", "constants_1", "error_1", "mongo_types_1", "transactions_1", "utils_1", "write_concern_1", "common_1", "monitor_1", "server_description_1", "stateTransition", "makeStateMachine", "STATE_CLOSED", "STATE_CONNECTING", "STATE_CLOSING", "STATE_CONNECTED", "Server", "TypedEventEmitter", "constructor", "topology", "description", "options", "on", "noop", "serverApi", "poolOptions", "host<PERSON><PERSON><PERSON>", "pool", "ConnectionPool", "s", "state", "operationCount", "event", "CMAP_EVENTS", "APM_EVENTS", "e", "emit", "Connection", "CLUSTER_TIME_RECEIVED", "clusterTime", "loadBalanced", "monitor", "Monitor", "HEARTBEAT_EVENTS", "error", "markServerUnknown", "SERVER_HEARTBEAT_SUCCEEDED", "DESCRIPTION_RECEIVED", "ServerDescription", "reply", "roundTripTime", "minRoundTripTime", "CONNECT", "name", "address", "autoEncrypter", "type", "TopologyType", "LoadBalanced", "connect", "destroy", "close", "requestCheck", "command", "ns", "cmd", "responseType", "db", "MongoInvalidArgumentError", "MongoServerClosedError", "directConnection", "omitReadPreference", "readPreference", "iscryptd", "omitMaxTimeMS", "session", "conn", "pinnedConnection", "incrementOperationCount", "checkOut", "isPinnableCommand", "pin", "checkoutError", "decrementOperationCount", "PoolClearedError", "handleError", "reauth<PERSON>romise", "res", "throwIfWriteConcernError", "commandError", "decorateCommandError", "operationError", "MongoError", "code", "MONGODB_ERROR_CODES", "Reauthenticate", "reauthenticate", "then", "undefined", "squashError", "abortable", "checkBackIn", "checkIn", "connection", "isStaleError", "connectionGeneration", "generation", "isNetworkNonTimeoutError", "MongoNetworkError", "MongoNetworkTimeoutError", "isNetworkTimeoutBeforeHandshakeError", "beforeHandshake", "isAuthHandshakeError", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MongoErrorLabel", "Handshake<PERSON><PERSON><PERSON>", "addErrorLabel", "ResetPool", "clear", "serviceId", "isSDAMUnrecoverableError", "shouldHandleStateChangeError", "shouldClearPool", "maxWireVersion", "isNodeShuttingDownError", "process", "nextTick", "MongoRuntimeError", "cause", "connectionIsStale", "hasEnded", "serverSession", "isDirty", "inActiveTransaction", "TransientTransactionError", "isRetryableWritesEnabled", "isTransactionCommand", "supportsRetryableWrites", "RetryableWriteError", "needsRetryableWriteLabel", "isPinned", "unpin", "force", "exports", "SERVER_HEARTBEAT_STARTED", "SERVER_HEARTBEAT_FAILED", "CLOSED", "ENDED", "server", "reset", "inTransaction", "transaction", "isCommitted", "serviceGenerations", "get", "toHexString", "err", "etv", "topologyVersion", "stv", "compareTopologyVersion", "retryWrites"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\sdam\\server.ts"], "sourcesContent": ["import type { Document } from '../bson';\nimport { type AutoEncrypter } from '../client-side-encryption/auto_encrypter';\nimport { type CommandOptions, Connection } from '../cmap/connection';\nimport {\n  ConnectionPool,\n  type ConnectionPoolEvents,\n  type ConnectionPoolOptions\n} from '../cmap/connection_pool';\nimport { PoolClearedError } from '../cmap/errors';\nimport { type MongoDBResponseConstructor } from '../cmap/wire_protocol/responses';\nimport {\n  APM_EVENTS,\n  CLOSED,\n  CMAP_EVENTS,\n  CONNECT,\n  DESCRIPTION_RECEIVED,\n  ENDED,\n  HEARTBEAT_EVENTS,\n  SERVER_HEARTBEAT_FAILED,\n  SERVER_HEARTBEAT_STARTED,\n  SERVER_HEARTBEAT_SUCCEEDED\n} from '../constants';\nimport {\n  type AnyError,\n  isNodeShuttingDownError,\n  isSDAMUnrecoverableError,\n  MONGODB_ERROR_CODES,\n  MongoError,\n  MongoErrorLabel,\n  MongoInvalidArgumentError,\n  MongoNetworkError,\n  MongoNetworkTimeoutError,\n  MongoRuntimeError,\n  MongoServerClosedError,\n  type MongoServerError,\n  needsRetryableWriteLabel\n} from '../error';\nimport type { ServerApi } from '../mongo_client';\nimport { type Abortable, TypedEventEmitter } from '../mongo_types';\nimport type { GetMoreOptions } from '../operations/get_more';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { isTransactionCommand } from '../transactions';\nimport {\n  abortable,\n  type EventEmitterWithState,\n  makeStateMachine,\n  maxWireVersion,\n  type MongoDBNamespace,\n  noop,\n  squashError,\n  supportsRetryableWrites\n} from '../utils';\nimport { throwIfWriteConcernError } from '../write_concern';\nimport {\n  type ClusterTime,\n  STATE_CLOSED,\n  STATE_CLOSING,\n  STATE_CONNECTED,\n  STATE_CONNECTING,\n  TopologyType\n} from './common';\nimport type {\n  ServerHeartbeatFailedEvent,\n  ServerHeartbeatStartedEvent,\n  ServerHeartbeatSucceededEvent\n} from './events';\nimport { Monitor, type MonitorOptions } from './monitor';\nimport { compareTopologyVersion, ServerDescription } from './server_description';\nimport type { Topology } from './topology';\n\nconst stateTransition = makeStateMachine({\n  [STATE_CLOSED]: [STATE_CLOSED, STATE_CONNECTING],\n  [STATE_CONNECTING]: [STATE_CONNECTING, STATE_CLOSING, STATE_CONNECTED, STATE_CLOSED],\n  [STATE_CONNECTED]: [STATE_CONNECTED, STATE_CLOSING, STATE_CLOSED],\n  [STATE_CLOSING]: [STATE_CLOSING, STATE_CLOSED]\n});\n\n/** @internal */\nexport type ServerOptions = Omit<ConnectionPoolOptions, 'id' | 'generation' | 'hostAddress'> &\n  MonitorOptions;\n\n/** @internal */\nexport interface ServerPrivate {\n  /** The server description for this server */\n  description: ServerDescription;\n  /** A copy of the options used to construct this instance */\n  options: ServerOptions;\n  /** The current state of the Server */\n  state: string;\n  /** MongoDB server API version */\n  serverApi?: ServerApi;\n  /** A count of the operations currently running against the server. */\n  operationCount: number;\n}\n\n/** @public */\nexport type ServerEvents = {\n  serverHeartbeatStarted(event: ServerHeartbeatStartedEvent): void;\n  serverHeartbeatSucceeded(event: ServerHeartbeatSucceededEvent): void;\n  serverHeartbeatFailed(event: ServerHeartbeatFailedEvent): void;\n  /** Top level MongoClient doesn't emit this so it is marked: @internal */\n  connect(server: Server): void;\n  descriptionReceived(description: ServerDescription): void;\n  closed(): void;\n  ended(): void;\n} & ConnectionPoolEvents &\n  EventEmitterWithState;\n\n/** @internal */\nexport type ServerCommandOptions = Omit<CommandOptions, 'timeoutContext' | 'socketTimeoutMS'> & {\n  timeoutContext: TimeoutContext;\n} & Abortable;\n\n/** @internal */\nexport class Server extends TypedEventEmitter<ServerEvents> {\n  /** @internal */\n  s: ServerPrivate;\n  /** @internal */\n  topology: Topology;\n  /** @internal */\n  pool: ConnectionPool;\n  serverApi?: ServerApi;\n  hello?: Document;\n  monitor: Monitor | null;\n\n  /** @event */\n  static readonly SERVER_HEARTBEAT_STARTED = SERVER_HEARTBEAT_STARTED;\n  /** @event */\n  static readonly SERVER_HEARTBEAT_SUCCEEDED = SERVER_HEARTBEAT_SUCCEEDED;\n  /** @event */\n  static readonly SERVER_HEARTBEAT_FAILED = SERVER_HEARTBEAT_FAILED;\n  /** @event */\n  static readonly CONNECT = CONNECT;\n  /** @event */\n  static readonly DESCRIPTION_RECEIVED = DESCRIPTION_RECEIVED;\n  /** @event */\n  static readonly CLOSED = CLOSED;\n  /** @event */\n  static readonly ENDED = ENDED;\n\n  /**\n   * Create a server\n   */\n  constructor(topology: Topology, description: ServerDescription, options: ServerOptions) {\n    super();\n    this.on('error', noop);\n\n    this.serverApi = options.serverApi;\n\n    const poolOptions = { hostAddress: description.hostAddress, ...options };\n\n    this.topology = topology;\n    this.pool = new ConnectionPool(this, poolOptions);\n\n    this.s = {\n      description,\n      options,\n      state: STATE_CLOSED,\n      operationCount: 0\n    };\n\n    for (const event of [...CMAP_EVENTS, ...APM_EVENTS]) {\n      this.pool.on(event, (e: any) => this.emit(event, e));\n    }\n\n    this.pool.on(Connection.CLUSTER_TIME_RECEIVED, (clusterTime: ClusterTime) => {\n      this.clusterTime = clusterTime;\n    });\n\n    if (this.loadBalanced) {\n      this.monitor = null;\n      // monitoring is disabled in load balancing mode\n      return;\n    }\n\n    // create the monitor\n    this.monitor = new Monitor(this, this.s.options);\n\n    for (const event of HEARTBEAT_EVENTS) {\n      this.monitor.on(event, (e: any) => this.emit(event, e));\n    }\n\n    this.monitor.on('resetServer', (error: MongoServerError) => markServerUnknown(this, error));\n    this.monitor.on(Server.SERVER_HEARTBEAT_SUCCEEDED, (event: ServerHeartbeatSucceededEvent) => {\n      this.emit(\n        Server.DESCRIPTION_RECEIVED,\n        new ServerDescription(this.description.hostAddress, event.reply, {\n          roundTripTime: this.monitor?.roundTripTime,\n          minRoundTripTime: this.monitor?.minRoundTripTime\n        })\n      );\n\n      if (this.s.state === STATE_CONNECTING) {\n        stateTransition(this, STATE_CONNECTED);\n        this.emit(Server.CONNECT, this);\n      }\n    });\n  }\n\n  get clusterTime(): ClusterTime | undefined {\n    return this.topology.clusterTime;\n  }\n\n  set clusterTime(clusterTime: ClusterTime | undefined) {\n    this.topology.clusterTime = clusterTime;\n  }\n\n  get description(): ServerDescription {\n    return this.s.description;\n  }\n\n  get name(): string {\n    return this.s.description.address;\n  }\n\n  get autoEncrypter(): AutoEncrypter | undefined {\n    if (this.s.options && this.s.options.autoEncrypter) {\n      return this.s.options.autoEncrypter;\n    }\n    return;\n  }\n\n  get loadBalanced(): boolean {\n    return this.topology.description.type === TopologyType.LoadBalanced;\n  }\n\n  /**\n   * Initiate server connect\n   */\n  connect(): void {\n    if (this.s.state !== STATE_CLOSED) {\n      return;\n    }\n\n    stateTransition(this, STATE_CONNECTING);\n\n    // If in load balancer mode we automatically set the server to\n    // a load balancer. It never transitions out of this state and\n    // has no monitor.\n    if (!this.loadBalanced) {\n      this.monitor?.connect();\n    } else {\n      stateTransition(this, STATE_CONNECTED);\n      this.emit(Server.CONNECT, this);\n    }\n  }\n\n  /** Destroy the server connection */\n  destroy(): void {\n    if (this.s.state === STATE_CLOSED) {\n      return;\n    }\n\n    stateTransition(this, STATE_CLOSING);\n\n    if (!this.loadBalanced) {\n      this.monitor?.close();\n    }\n\n    this.pool.close();\n    stateTransition(this, STATE_CLOSED);\n    this.emit('closed');\n  }\n\n  /**\n   * Immediately schedule monitoring of this server. If there already an attempt being made\n   * this will be a no-op.\n   */\n  requestCheck(): void {\n    if (!this.loadBalanced) {\n      this.monitor?.requestCheck();\n    }\n  }\n\n  public async command<T extends MongoDBResponseConstructor>(\n    ns: MongoDBNamespace,\n    command: Document,\n    options: ServerCommandOptions,\n    responseType: T | undefined\n  ): Promise<typeof responseType extends undefined ? Document : InstanceType<T>>;\n\n  public async command(\n    ns: MongoDBNamespace,\n    command: Document,\n    options: ServerCommandOptions\n  ): Promise<Document>;\n\n  public async command(\n    ns: MongoDBNamespace,\n    cmd: Document,\n    { ...options }: ServerCommandOptions,\n    responseType?: MongoDBResponseConstructor\n  ): Promise<Document> {\n    if (ns.db == null || typeof ns === 'string') {\n      throw new MongoInvalidArgumentError('Namespace must not be a string');\n    }\n\n    if (this.s.state === STATE_CLOSING || this.s.state === STATE_CLOSED) {\n      throw new MongoServerClosedError();\n    }\n\n    options.directConnection = this.topology.s.options.directConnection;\n\n    // There are cases where we need to flag the read preference not to get sent in\n    // the command, such as pre-5.0 servers attempting to perform an aggregate write\n    // with a non-primary read preference. In this case the effective read preference\n    // (primary) is not the same as the provided and must be removed completely.\n    if (options.omitReadPreference) {\n      delete options.readPreference;\n    }\n\n    if (this.description.iscryptd) {\n      options.omitMaxTimeMS = true;\n    }\n\n    const session = options.session;\n    let conn = session?.pinnedConnection;\n\n    this.incrementOperationCount();\n    if (conn == null) {\n      try {\n        conn = await this.pool.checkOut(options);\n        if (this.loadBalanced && isPinnableCommand(cmd, session)) {\n          session?.pin(conn);\n        }\n      } catch (checkoutError) {\n        this.decrementOperationCount();\n        if (!(checkoutError instanceof PoolClearedError)) this.handleError(checkoutError);\n        throw checkoutError;\n      }\n    }\n\n    let reauthPromise: Promise<void> | null = null;\n\n    try {\n      try {\n        const res = await conn.command(ns, cmd, options, responseType);\n        throwIfWriteConcernError(res);\n        return res;\n      } catch (commandError) {\n        throw this.decorateCommandError(conn, cmd, options, commandError);\n      }\n    } catch (operationError) {\n      if (\n        operationError instanceof MongoError &&\n        operationError.code === MONGODB_ERROR_CODES.Reauthenticate\n      ) {\n        reauthPromise = this.pool.reauthenticate(conn);\n        reauthPromise.then(undefined, error => {\n          reauthPromise = null;\n          squashError(error);\n        });\n\n        await abortable(reauthPromise, options);\n        reauthPromise = null; // only reachable if reauth succeeds\n\n        try {\n          const res = await conn.command(ns, cmd, options, responseType);\n          throwIfWriteConcernError(res);\n          return res;\n        } catch (commandError) {\n          throw this.decorateCommandError(conn, cmd, options, commandError);\n        }\n      } else {\n        throw operationError;\n      }\n    } finally {\n      this.decrementOperationCount();\n      if (session?.pinnedConnection !== conn) {\n        if (reauthPromise != null) {\n          // The reauth promise only exists if it hasn't thrown.\n          const checkBackIn = () => {\n            this.pool.checkIn(conn);\n          };\n          void reauthPromise.then(checkBackIn, checkBackIn);\n        } else {\n          this.pool.checkIn(conn);\n        }\n      }\n    }\n  }\n\n  /**\n   * Handle SDAM error\n   * @internal\n   */\n  handleError(error: AnyError, connection?: Connection) {\n    if (!(error instanceof MongoError)) {\n      return;\n    }\n\n    const isStaleError =\n      error.connectionGeneration && error.connectionGeneration < this.pool.generation;\n    if (isStaleError) {\n      return;\n    }\n\n    const isNetworkNonTimeoutError =\n      error instanceof MongoNetworkError && !(error instanceof MongoNetworkTimeoutError);\n    const isNetworkTimeoutBeforeHandshakeError =\n      error instanceof MongoNetworkError && error.beforeHandshake;\n    const isAuthHandshakeError = error.hasErrorLabel(MongoErrorLabel.HandshakeError);\n    if (isNetworkNonTimeoutError || isNetworkTimeoutBeforeHandshakeError || isAuthHandshakeError) {\n      // In load balanced mode we never mark the server as unknown and always\n      // clear for the specific service id.\n      if (!this.loadBalanced) {\n        error.addErrorLabel(MongoErrorLabel.ResetPool);\n        markServerUnknown(this, error);\n      } else if (connection) {\n        this.pool.clear({ serviceId: connection.serviceId });\n      }\n    } else {\n      if (isSDAMUnrecoverableError(error)) {\n        if (shouldHandleStateChangeError(this, error)) {\n          const shouldClearPool = maxWireVersion(this) <= 7 || isNodeShuttingDownError(error);\n          if (this.loadBalanced && connection && shouldClearPool) {\n            this.pool.clear({ serviceId: connection.serviceId });\n          }\n\n          if (!this.loadBalanced) {\n            if (shouldClearPool) {\n              error.addErrorLabel(MongoErrorLabel.ResetPool);\n            }\n            markServerUnknown(this, error);\n            process.nextTick(() => this.requestCheck());\n          }\n        }\n      }\n    }\n  }\n\n  /**\n   * Ensure that error is properly decorated and internal state is updated before throwing\n   * @internal\n   */\n  private decorateCommandError(\n    connection: Connection,\n    cmd: Document,\n    options: CommandOptions | GetMoreOptions | undefined,\n    error: unknown\n  ): Error {\n    if (typeof error !== 'object' || error == null || !('name' in error)) {\n      throw new MongoRuntimeError('An unexpected error type: ' + typeof error);\n    }\n\n    if (error.name === 'AbortError' && 'cause' in error && error.cause instanceof MongoError) {\n      error = error.cause;\n    }\n\n    if (!(error instanceof MongoError)) {\n      // Node.js or some other error we have not special handling for\n      return error as Error;\n    }\n\n    if (connectionIsStale(this.pool, connection)) {\n      return error;\n    }\n\n    const session = options?.session;\n    if (error instanceof MongoNetworkError) {\n      if (session && !session.hasEnded && session.serverSession) {\n        session.serverSession.isDirty = true;\n      }\n\n      // inActiveTransaction check handles commit and abort.\n      if (\n        inActiveTransaction(session, cmd) &&\n        !error.hasErrorLabel(MongoErrorLabel.TransientTransactionError)\n      ) {\n        error.addErrorLabel(MongoErrorLabel.TransientTransactionError);\n      }\n\n      if (\n        (isRetryableWritesEnabled(this.topology) || isTransactionCommand(cmd)) &&\n        supportsRetryableWrites(this) &&\n        !inActiveTransaction(session, cmd)\n      ) {\n        error.addErrorLabel(MongoErrorLabel.RetryableWriteError);\n      }\n    } else {\n      if (\n        (isRetryableWritesEnabled(this.topology) || isTransactionCommand(cmd)) &&\n        needsRetryableWriteLabel(error, maxWireVersion(this), this.description.type) &&\n        !inActiveTransaction(session, cmd)\n      ) {\n        error.addErrorLabel(MongoErrorLabel.RetryableWriteError);\n      }\n    }\n\n    if (\n      session &&\n      session.isPinned &&\n      error.hasErrorLabel(MongoErrorLabel.TransientTransactionError)\n    ) {\n      session.unpin({ force: true });\n    }\n\n    this.handleError(error, connection);\n\n    return error;\n  }\n\n  /**\n   * Decrement the operation count, returning the new count.\n   */\n  private decrementOperationCount(): number {\n    return (this.s.operationCount -= 1);\n  }\n\n  /**\n   * Increment the operation count, returning the new count.\n   */\n  private incrementOperationCount(): number {\n    return (this.s.operationCount += 1);\n  }\n}\n\nfunction markServerUnknown(server: Server, error?: MongoError) {\n  // Load balancer servers can never be marked unknown.\n  if (server.loadBalanced) {\n    return;\n  }\n\n  if (error instanceof MongoNetworkError && !(error instanceof MongoNetworkTimeoutError)) {\n    server.monitor?.reset();\n  }\n\n  server.emit(\n    Server.DESCRIPTION_RECEIVED,\n    new ServerDescription(server.description.hostAddress, undefined, { error })\n  );\n}\n\nfunction isPinnableCommand(cmd: Document, session?: ClientSession): boolean {\n  if (session) {\n    return (\n      session.inTransaction() ||\n      (session.transaction.isCommitted && 'commitTransaction' in cmd) ||\n      'aggregate' in cmd ||\n      'find' in cmd ||\n      'getMore' in cmd ||\n      'listCollections' in cmd ||\n      'listIndexes' in cmd ||\n      'bulkWrite' in cmd\n    );\n  }\n\n  return false;\n}\n\nfunction connectionIsStale(pool: ConnectionPool, connection: Connection) {\n  if (connection.serviceId) {\n    return (\n      connection.generation !== pool.serviceGenerations.get(connection.serviceId.toHexString())\n    );\n  }\n\n  return connection.generation !== pool.generation;\n}\n\nfunction shouldHandleStateChangeError(server: Server, err: MongoError) {\n  const etv = err.topologyVersion;\n  const stv = server.description.topologyVersion;\n  return compareTopologyVersion(stv, etv) < 0;\n}\n\nfunction inActiveTransaction(session: ClientSession | undefined, cmd: Document) {\n  return session && session.inTransaction() && !isTransactionCommand(cmd);\n}\n\n/** this checks the retryWrites option passed down from the client options, it\n * does not check if the server supports retryable writes */\nfunction isRetryableWritesEnabled(topology: Topology) {\n  return topology.s.options.retryWrites !== false;\n}\n"], "mappings": ";;;;;;AAEA,MAAAA,YAAA,GAAAC,OAAA;AACA,MAAAC,iBAAA,GAAAD,OAAA;AAKA,MAAAE,QAAA,GAAAF,OAAA;AAEA,MAAAG,WAAA,GAAAH,OAAA;AAYA,MAAAI,OAAA,GAAAJ,OAAA;AAgBA,MAAAK,aAAA,GAAAL,OAAA;AAIA,MAAAM,cAAA,GAAAN,OAAA;AACA,MAAAO,OAAA,GAAAP,OAAA;AAUA,MAAAQ,eAAA,GAAAR,OAAA;AACA,MAAAS,QAAA,GAAAT,OAAA;AAaA,MAAAU,SAAA,GAAAV,OAAA;AACA,MAAAW,oBAAA,GAAAX,OAAA;AAGA,MAAMY,eAAe,GAAG,IAAAL,OAAA,CAAAM,gBAAgB,EAAC;EACvC,CAACJ,QAAA,CAAAK,YAAY,GAAG,CAACL,QAAA,CAAAK,YAAY,EAAEL,QAAA,CAAAM,gBAAgB,CAAC;EAChD,CAACN,QAAA,CAAAM,gBAAgB,GAAG,CAACN,QAAA,CAAAM,gBAAgB,EAAEN,QAAA,CAAAO,aAAa,EAAEP,QAAA,CAAAQ,eAAe,EAAER,QAAA,CAAAK,YAAY,CAAC;EACpF,CAACL,QAAA,CAAAQ,eAAe,GAAG,CAACR,QAAA,CAAAQ,eAAe,EAAER,QAAA,CAAAO,aAAa,EAAEP,QAAA,CAAAK,YAAY,CAAC;EACjE,CAACL,QAAA,CAAAO,aAAa,GAAG,CAACP,QAAA,CAAAO,aAAa,EAAEP,QAAA,CAAAK,YAAY;CAC9C,CAAC;AAsCF;AACA,MAAaI,MAAO,SAAQb,aAAA,CAAAc,iBAA+B;EA0BzD;;;EAGAC,YAAYC,QAAkB,EAAEC,WAA8B,EAAEC,OAAsB;IACpF,KAAK,EAAE;IACP,IAAI,CAACC,EAAE,CAAC,OAAO,EAAEjB,OAAA,CAAAkB,IAAI,CAAC;IAEtB,IAAI,CAACC,SAAS,GAAGH,OAAO,CAACG,SAAS;IAElC,MAAMC,WAAW,GAAG;MAAEC,WAAW,EAAEN,WAAW,CAACM,WAAW;MAAE,GAAGL;IAAO,CAAE;IAExE,IAAI,CAACF,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACQ,IAAI,GAAG,IAAI5B,iBAAA,CAAA6B,cAAc,CAAC,IAAI,EAAEH,WAAW,CAAC;IAEjD,IAAI,CAACI,CAAC,GAAG;MACPT,WAAW;MACXC,OAAO;MACPS,KAAK,EAAEvB,QAAA,CAAAK,YAAY;MACnBmB,cAAc,EAAE;KACjB;IAED,KAAK,MAAMC,KAAK,IAAI,CAAC,GAAG/B,WAAA,CAAAgC,WAAW,EAAE,GAAGhC,WAAA,CAAAiC,UAAU,CAAC,EAAE;MACnD,IAAI,CAACP,IAAI,CAACL,EAAE,CAACU,KAAK,EAAGG,CAAM,IAAK,IAAI,CAACC,IAAI,CAACJ,KAAK,EAAEG,CAAC,CAAC,CAAC;IACtD;IAEA,IAAI,CAACR,IAAI,CAACL,EAAE,CAACzB,YAAA,CAAAwC,UAAU,CAACC,qBAAqB,EAAGC,WAAwB,IAAI;MAC1E,IAAI,CAACA,WAAW,GAAGA,WAAW;IAChC,CAAC,CAAC;IAEF,IAAI,IAAI,CAACC,YAAY,EAAE;MACrB,IAAI,CAACC,OAAO,GAAG,IAAI;MACnB;MACA;IACF;IAEA;IACA,IAAI,CAACA,OAAO,GAAG,IAAIjC,SAAA,CAAAkC,OAAO,CAAC,IAAI,EAAE,IAAI,CAACb,CAAC,CAACR,OAAO,CAAC;IAEhD,KAAK,MAAMW,KAAK,IAAI/B,WAAA,CAAA0C,gBAAgB,EAAE;MACpC,IAAI,CAACF,OAAO,CAACnB,EAAE,CAACU,KAAK,EAAGG,CAAM,IAAK,IAAI,CAACC,IAAI,CAACJ,KAAK,EAAEG,CAAC,CAAC,CAAC;IACzD;IAEA,IAAI,CAACM,OAAO,CAACnB,EAAE,CAAC,aAAa,EAAGsB,KAAuB,IAAKC,iBAAiB,CAAC,IAAI,EAAED,KAAK,CAAC,CAAC;IAC3F,IAAI,CAACH,OAAO,CAACnB,EAAE,CAACN,MAAM,CAAC8B,0BAA0B,EAAGd,KAAoC,IAAI;MAC1F,IAAI,CAACI,IAAI,CACPpB,MAAM,CAAC+B,oBAAoB,EAC3B,IAAItC,oBAAA,CAAAuC,iBAAiB,CAAC,IAAI,CAAC5B,WAAW,CAACM,WAAW,EAAEM,KAAK,CAACiB,KAAK,EAAE;QAC/DC,aAAa,EAAE,IAAI,CAACT,OAAO,EAAES,aAAa;QAC1CC,gBAAgB,EAAE,IAAI,CAACV,OAAO,EAAEU;OACjC,CAAC,CACH;MAED,IAAI,IAAI,CAACtB,CAAC,CAACC,KAAK,KAAKvB,QAAA,CAAAM,gBAAgB,EAAE;QACrCH,eAAe,CAAC,IAAI,EAAEH,QAAA,CAAAQ,eAAe,CAAC;QACtC,IAAI,CAACqB,IAAI,CAACpB,MAAM,CAACoC,OAAO,EAAE,IAAI,CAAC;MACjC;IACF,CAAC,CAAC;EACJ;EAEA,IAAIb,WAAWA,CAAA;IACb,OAAO,IAAI,CAACpB,QAAQ,CAACoB,WAAW;EAClC;EAEA,IAAIA,WAAWA,CAACA,WAAoC;IAClD,IAAI,CAACpB,QAAQ,CAACoB,WAAW,GAAGA,WAAW;EACzC;EAEA,IAAInB,WAAWA,CAAA;IACb,OAAO,IAAI,CAACS,CAAC,CAACT,WAAW;EAC3B;EAEA,IAAIiC,IAAIA,CAAA;IACN,OAAO,IAAI,CAACxB,CAAC,CAACT,WAAW,CAACkC,OAAO;EACnC;EAEA,IAAIC,aAAaA,CAAA;IACf,IAAI,IAAI,CAAC1B,CAAC,CAACR,OAAO,IAAI,IAAI,CAACQ,CAAC,CAACR,OAAO,CAACkC,aAAa,EAAE;MAClD,OAAO,IAAI,CAAC1B,CAAC,CAACR,OAAO,CAACkC,aAAa;IACrC;IACA;EACF;EAEA,IAAIf,YAAYA,CAAA;IACd,OAAO,IAAI,CAACrB,QAAQ,CAACC,WAAW,CAACoC,IAAI,KAAKjD,QAAA,CAAAkD,YAAY,CAACC,YAAY;EACrE;EAEA;;;EAGAC,OAAOA,CAAA;IACL,IAAI,IAAI,CAAC9B,CAAC,CAACC,KAAK,KAAKvB,QAAA,CAAAK,YAAY,EAAE;MACjC;IACF;IAEAF,eAAe,CAAC,IAAI,EAAEH,QAAA,CAAAM,gBAAgB,CAAC;IAEvC;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC2B,YAAY,EAAE;MACtB,IAAI,CAACC,OAAO,EAAEkB,OAAO,EAAE;IACzB,CAAC,MAAM;MACLjD,eAAe,CAAC,IAAI,EAAEH,QAAA,CAAAQ,eAAe,CAAC;MACtC,IAAI,CAACqB,IAAI,CAACpB,MAAM,CAACoC,OAAO,EAAE,IAAI,CAAC;IACjC;EACF;EAEA;EACAQ,OAAOA,CAAA;IACL,IAAI,IAAI,CAAC/B,CAAC,CAACC,KAAK,KAAKvB,QAAA,CAAAK,YAAY,EAAE;MACjC;IACF;IAEAF,eAAe,CAAC,IAAI,EAAEH,QAAA,CAAAO,aAAa,CAAC;IAEpC,IAAI,CAAC,IAAI,CAAC0B,YAAY,EAAE;MACtB,IAAI,CAACC,OAAO,EAAEoB,KAAK,EAAE;IACvB;IAEA,IAAI,CAAClC,IAAI,CAACkC,KAAK,EAAE;IACjBnD,eAAe,CAAC,IAAI,EAAEH,QAAA,CAAAK,YAAY,CAAC;IACnC,IAAI,CAACwB,IAAI,CAAC,QAAQ,CAAC;EACrB;EAEA;;;;EAIA0B,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACtB,YAAY,EAAE;MACtB,IAAI,CAACC,OAAO,EAAEqB,YAAY,EAAE;IAC9B;EACF;EAeO,MAAMC,OAAOA,CAClBC,EAAoB,EACpBC,GAAa,EACb;IAAE,GAAG5C;EAAO,CAAwB,EACpC6C,YAAyC;IAEzC,IAAIF,EAAE,CAACG,EAAE,IAAI,IAAI,IAAI,OAAOH,EAAE,KAAK,QAAQ,EAAE;MAC3C,MAAM,IAAI9D,OAAA,CAAAkE,yBAAyB,CAAC,gCAAgC,CAAC;IACvE;IAEA,IAAI,IAAI,CAACvC,CAAC,CAACC,KAAK,KAAKvB,QAAA,CAAAO,aAAa,IAAI,IAAI,CAACe,CAAC,CAACC,KAAK,KAAKvB,QAAA,CAAAK,YAAY,EAAE;MACnE,MAAM,IAAIV,OAAA,CAAAmE,sBAAsB,EAAE;IACpC;IAEAhD,OAAO,CAACiD,gBAAgB,GAAG,IAAI,CAACnD,QAAQ,CAACU,CAAC,CAACR,OAAO,CAACiD,gBAAgB;IAEnE;IACA;IACA;IACA;IACA,IAAIjD,OAAO,CAACkD,kBAAkB,EAAE;MAC9B,OAAOlD,OAAO,CAACmD,cAAc;IAC/B;IAEA,IAAI,IAAI,CAACpD,WAAW,CAACqD,QAAQ,EAAE;MAC7BpD,OAAO,CAACqD,aAAa,GAAG,IAAI;IAC9B;IAEA,MAAMC,OAAO,GAAGtD,OAAO,CAACsD,OAAO;IAC/B,IAAIC,IAAI,GAAGD,OAAO,EAAEE,gBAAgB;IAEpC,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAIF,IAAI,IAAI,IAAI,EAAE;MAChB,IAAI;QACFA,IAAI,GAAG,MAAM,IAAI,CAACjD,IAAI,CAACoD,QAAQ,CAAC1D,OAAO,CAAC;QACxC,IAAI,IAAI,CAACmB,YAAY,IAAIwC,iBAAiB,CAACf,GAAG,EAAEU,OAAO,CAAC,EAAE;UACxDA,OAAO,EAAEM,GAAG,CAACL,IAAI,CAAC;QACpB;MACF,CAAC,CAAC,OAAOM,aAAa,EAAE;QACtB,IAAI,CAACC,uBAAuB,EAAE;QAC9B,IAAI,EAAED,aAAa,YAAYlF,QAAA,CAAAoF,gBAAgB,CAAC,EAAE,IAAI,CAACC,WAAW,CAACH,aAAa,CAAC;QACjF,MAAMA,aAAa;MACrB;IACF;IAEA,IAAII,aAAa,GAAyB,IAAI;IAE9C,IAAI;MACF,IAAI;QACF,MAAMC,GAAG,GAAG,MAAMX,IAAI,CAACb,OAAO,CAACC,EAAE,EAAEC,GAAG,EAAE5C,OAAO,EAAE6C,YAAY,CAAC;QAC9D,IAAA5D,eAAA,CAAAkF,wBAAwB,EAACD,GAAG,CAAC;QAC7B,OAAOA,GAAG;MACZ,CAAC,CAAC,OAAOE,YAAY,EAAE;QACrB,MAAM,IAAI,CAACC,oBAAoB,CAACd,IAAI,EAAEX,GAAG,EAAE5C,OAAO,EAAEoE,YAAY,CAAC;MACnE;IACF,CAAC,CAAC,OAAOE,cAAc,EAAE;MACvB,IACEA,cAAc,YAAYzF,OAAA,CAAA0F,UAAU,IACpCD,cAAc,CAACE,IAAI,KAAK3F,OAAA,CAAA4F,mBAAmB,CAACC,cAAc,EAC1D;QACAT,aAAa,GAAG,IAAI,CAAC3D,IAAI,CAACqE,cAAc,CAACpB,IAAI,CAAC;QAC9CU,aAAa,CAACW,IAAI,CAACC,SAAS,EAAEtD,KAAK,IAAG;UACpC0C,aAAa,GAAG,IAAI;UACpB,IAAAjF,OAAA,CAAA8F,WAAW,EAACvD,KAAK,CAAC;QACpB,CAAC,CAAC;QAEF,MAAM,IAAAvC,OAAA,CAAA+F,SAAS,EAACd,aAAa,EAAEjE,OAAO,CAAC;QACvCiE,aAAa,GAAG,IAAI,CAAC,CAAC;QAEtB,IAAI;UACF,MAAMC,GAAG,GAAG,MAAMX,IAAI,CAACb,OAAO,CAACC,EAAE,EAAEC,GAAG,EAAE5C,OAAO,EAAE6C,YAAY,CAAC;UAC9D,IAAA5D,eAAA,CAAAkF,wBAAwB,EAACD,GAAG,CAAC;UAC7B,OAAOA,GAAG;QACZ,CAAC,CAAC,OAAOE,YAAY,EAAE;UACrB,MAAM,IAAI,CAACC,oBAAoB,CAACd,IAAI,EAAEX,GAAG,EAAE5C,OAAO,EAAEoE,YAAY,CAAC;QACnE;MACF,CAAC,MAAM;QACL,MAAME,cAAc;MACtB;IACF,CAAC,SAAS;MACR,IAAI,CAACR,uBAAuB,EAAE;MAC9B,IAAIR,OAAO,EAAEE,gBAAgB,KAAKD,IAAI,EAAE;QACtC,IAAIU,aAAa,IAAI,IAAI,EAAE;UACzB;UACA,MAAMe,WAAW,GAAGA,CAAA,KAAK;YACvB,IAAI,CAAC1E,IAAI,CAAC2E,OAAO,CAAC1B,IAAI,CAAC;UACzB,CAAC;UACD,KAAKU,aAAa,CAACW,IAAI,CAACI,WAAW,EAAEA,WAAW,CAAC;QACnD,CAAC,MAAM;UACL,IAAI,CAAC1E,IAAI,CAAC2E,OAAO,CAAC1B,IAAI,CAAC;QACzB;MACF;IACF;EACF;EAEA;;;;EAIAS,WAAWA,CAACzC,KAAe,EAAE2D,UAAuB;IAClD,IAAI,EAAE3D,KAAK,YAAY1C,OAAA,CAAA0F,UAAU,CAAC,EAAE;MAClC;IACF;IAEA,MAAMY,YAAY,GAChB5D,KAAK,CAAC6D,oBAAoB,IAAI7D,KAAK,CAAC6D,oBAAoB,GAAG,IAAI,CAAC9E,IAAI,CAAC+E,UAAU;IACjF,IAAIF,YAAY,EAAE;MAChB;IACF;IAEA,MAAMG,wBAAwB,GAC5B/D,KAAK,YAAY1C,OAAA,CAAA0G,iBAAiB,IAAI,EAAEhE,KAAK,YAAY1C,OAAA,CAAA2G,wBAAwB,CAAC;IACpF,MAAMC,oCAAoC,GACxClE,KAAK,YAAY1C,OAAA,CAAA0G,iBAAiB,IAAIhE,KAAK,CAACmE,eAAe;IAC7D,MAAMC,oBAAoB,GAAGpE,KAAK,CAACqE,aAAa,CAAC/G,OAAA,CAAAgH,eAAe,CAACC,cAAc,CAAC;IAChF,IAAIR,wBAAwB,IAAIG,oCAAoC,IAAIE,oBAAoB,EAAE;MAC5F;MACA;MACA,IAAI,CAAC,IAAI,CAACxE,YAAY,EAAE;QACtBI,KAAK,CAACwE,aAAa,CAAClH,OAAA,CAAAgH,eAAe,CAACG,SAAS,CAAC;QAC9CxE,iBAAiB,CAAC,IAAI,EAAED,KAAK,CAAC;MAChC,CAAC,MAAM,IAAI2D,UAAU,EAAE;QACrB,IAAI,CAAC5E,IAAI,CAAC2F,KAAK,CAAC;UAAEC,SAAS,EAAEhB,UAAU,CAACgB;QAAS,CAAE,CAAC;MACtD;IACF,CAAC,MAAM;MACL,IAAI,IAAArH,OAAA,CAAAsH,wBAAwB,EAAC5E,KAAK,CAAC,EAAE;QACnC,IAAI6E,4BAA4B,CAAC,IAAI,EAAE7E,KAAK,CAAC,EAAE;UAC7C,MAAM8E,eAAe,GAAG,IAAArH,OAAA,CAAAsH,cAAc,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAAzH,OAAA,CAAA0H,uBAAuB,EAAChF,KAAK,CAAC;UACnF,IAAI,IAAI,CAACJ,YAAY,IAAI+D,UAAU,IAAImB,eAAe,EAAE;YACtD,IAAI,CAAC/F,IAAI,CAAC2F,KAAK,CAAC;cAAEC,SAAS,EAAEhB,UAAU,CAACgB;YAAS,CAAE,CAAC;UACtD;UAEA,IAAI,CAAC,IAAI,CAAC/E,YAAY,EAAE;YACtB,IAAIkF,eAAe,EAAE;cACnB9E,KAAK,CAACwE,aAAa,CAAClH,OAAA,CAAAgH,eAAe,CAACG,SAAS,CAAC;YAChD;YACAxE,iBAAiB,CAAC,IAAI,EAAED,KAAK,CAAC;YAC9BiF,OAAO,CAACC,QAAQ,CAAC,MAAM,IAAI,CAAChE,YAAY,EAAE,CAAC;UAC7C;QACF;MACF;IACF;EACF;EAEA;;;;EAIQ4B,oBAAoBA,CAC1Ba,UAAsB,EACtBtC,GAAa,EACb5C,OAAoD,EACpDuB,KAAc;IAEd,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAIA,KAAK,IAAI,IAAI,IAAI,EAAE,MAAM,IAAIA,KAAK,CAAC,EAAE;MACpE,MAAM,IAAI1C,OAAA,CAAA6H,iBAAiB,CAAC,4BAA4B,GAAG,OAAOnF,KAAK,CAAC;IAC1E;IAEA,IAAIA,KAAK,CAACS,IAAI,KAAK,YAAY,IAAI,OAAO,IAAIT,KAAK,IAAIA,KAAK,CAACoF,KAAK,YAAY9H,OAAA,CAAA0F,UAAU,EAAE;MACxFhD,KAAK,GAAGA,KAAK,CAACoF,KAAK;IACrB;IAEA,IAAI,EAAEpF,KAAK,YAAY1C,OAAA,CAAA0F,UAAU,CAAC,EAAE;MAClC;MACA,OAAOhD,KAAc;IACvB;IAEA,IAAIqF,iBAAiB,CAAC,IAAI,CAACtG,IAAI,EAAE4E,UAAU,CAAC,EAAE;MAC5C,OAAO3D,KAAK;IACd;IAEA,MAAM+B,OAAO,GAAGtD,OAAO,EAAEsD,OAAO;IAChC,IAAI/B,KAAK,YAAY1C,OAAA,CAAA0G,iBAAiB,EAAE;MACtC,IAAIjC,OAAO,IAAI,CAACA,OAAO,CAACuD,QAAQ,IAAIvD,OAAO,CAACwD,aAAa,EAAE;QACzDxD,OAAO,CAACwD,aAAa,CAACC,OAAO,GAAG,IAAI;MACtC;MAEA;MACA,IACEC,mBAAmB,CAAC1D,OAAO,EAAEV,GAAG,CAAC,IACjC,CAACrB,KAAK,CAACqE,aAAa,CAAC/G,OAAA,CAAAgH,eAAe,CAACoB,yBAAyB,CAAC,EAC/D;QACA1F,KAAK,CAACwE,aAAa,CAAClH,OAAA,CAAAgH,eAAe,CAACoB,yBAAyB,CAAC;MAChE;MAEA,IACE,CAACC,wBAAwB,CAAC,IAAI,CAACpH,QAAQ,CAAC,IAAI,IAAAf,cAAA,CAAAoI,oBAAoB,EAACvE,GAAG,CAAC,KACrE,IAAA5D,OAAA,CAAAoI,uBAAuB,EAAC,IAAI,CAAC,IAC7B,CAACJ,mBAAmB,CAAC1D,OAAO,EAAEV,GAAG,CAAC,EAClC;QACArB,KAAK,CAACwE,aAAa,CAAClH,OAAA,CAAAgH,eAAe,CAACwB,mBAAmB,CAAC;MAC1D;IACF,CAAC,MAAM;MACL,IACE,CAACH,wBAAwB,CAAC,IAAI,CAACpH,QAAQ,CAAC,IAAI,IAAAf,cAAA,CAAAoI,oBAAoB,EAACvE,GAAG,CAAC,KACrE,IAAA/D,OAAA,CAAAyI,wBAAwB,EAAC/F,KAAK,EAAE,IAAAvC,OAAA,CAAAsH,cAAc,EAAC,IAAI,CAAC,EAAE,IAAI,CAACvG,WAAW,CAACoC,IAAI,CAAC,IAC5E,CAAC6E,mBAAmB,CAAC1D,OAAO,EAAEV,GAAG,CAAC,EAClC;QACArB,KAAK,CAACwE,aAAa,CAAClH,OAAA,CAAAgH,eAAe,CAACwB,mBAAmB,CAAC;MAC1D;IACF;IAEA,IACE/D,OAAO,IACPA,OAAO,CAACiE,QAAQ,IAChBhG,KAAK,CAACqE,aAAa,CAAC/G,OAAA,CAAAgH,eAAe,CAACoB,yBAAyB,CAAC,EAC9D;MACA3D,OAAO,CAACkE,KAAK,CAAC;QAAEC,KAAK,EAAE;MAAI,CAAE,CAAC;IAChC;IAEA,IAAI,CAACzD,WAAW,CAACzC,KAAK,EAAE2D,UAAU,CAAC;IAEnC,OAAO3D,KAAK;EACd;EAEA;;;EAGQuC,uBAAuBA,CAAA;IAC7B,OAAQ,IAAI,CAACtD,CAAC,CAACE,cAAc,IAAI,CAAC;EACpC;EAEA;;;EAGQ+C,uBAAuBA,CAAA;IAC7B,OAAQ,IAAI,CAACjD,CAAC,CAACE,cAAc,IAAI,CAAC;EACpC;;AAhZFgH,OAAA,CAAA/H,MAAA,GAAAA,MAAA;AAWE;AACgBA,MAAA,CAAAgI,wBAAwB,GAAG/I,WAAA,CAAA+I,wBAAwB;AACnE;AACgBhI,MAAA,CAAA8B,0BAA0B,GAAG7C,WAAA,CAAA6C,0BAA0B;AACvE;AACgB9B,MAAA,CAAAiI,uBAAuB,GAAGhJ,WAAA,CAAAgJ,uBAAuB;AACjE;AACgBjI,MAAA,CAAAoC,OAAO,GAAGnD,WAAA,CAAAmD,OAAO;AACjC;AACgBpC,MAAA,CAAA+B,oBAAoB,GAAG9C,WAAA,CAAA8C,oBAAoB;AAC3D;AACgB/B,MAAA,CAAAkI,MAAM,GAAGjJ,WAAA,CAAAiJ,MAAM;AAC/B;AACgBlI,MAAA,CAAAmI,KAAK,GAAGlJ,WAAA,CAAAkJ,KAAK;AA2X/B,SAAStG,iBAAiBA,CAACuG,MAAc,EAAExG,KAAkB;EAC3D;EACA,IAAIwG,MAAM,CAAC5G,YAAY,EAAE;IACvB;EACF;EAEA,IAAII,KAAK,YAAY1C,OAAA,CAAA0G,iBAAiB,IAAI,EAAEhE,KAAK,YAAY1C,OAAA,CAAA2G,wBAAwB,CAAC,EAAE;IACtFuC,MAAM,CAAC3G,OAAO,EAAE4G,KAAK,EAAE;EACzB;EAEAD,MAAM,CAAChH,IAAI,CACTpB,MAAM,CAAC+B,oBAAoB,EAC3B,IAAItC,oBAAA,CAAAuC,iBAAiB,CAACoG,MAAM,CAAChI,WAAW,CAACM,WAAW,EAAEwE,SAAS,EAAE;IAAEtD;EAAK,CAAE,CAAC,CAC5E;AACH;AAEA,SAASoC,iBAAiBA,CAACf,GAAa,EAAEU,OAAuB;EAC/D,IAAIA,OAAO,EAAE;IACX,OACEA,OAAO,CAAC2E,aAAa,EAAE,IACtB3E,OAAO,CAAC4E,WAAW,CAACC,WAAW,IAAI,mBAAmB,IAAIvF,GAAI,IAC/D,WAAW,IAAIA,GAAG,IAClB,MAAM,IAAIA,GAAG,IACb,SAAS,IAAIA,GAAG,IAChB,iBAAiB,IAAIA,GAAG,IACxB,aAAa,IAAIA,GAAG,IACpB,WAAW,IAAIA,GAAG;EAEtB;EAEA,OAAO,KAAK;AACd;AAEA,SAASgE,iBAAiBA,CAACtG,IAAoB,EAAE4E,UAAsB;EACrE,IAAIA,UAAU,CAACgB,SAAS,EAAE;IACxB,OACEhB,UAAU,CAACG,UAAU,KAAK/E,IAAI,CAAC8H,kBAAkB,CAACC,GAAG,CAACnD,UAAU,CAACgB,SAAS,CAACoC,WAAW,EAAE,CAAC;EAE7F;EAEA,OAAOpD,UAAU,CAACG,UAAU,KAAK/E,IAAI,CAAC+E,UAAU;AAClD;AAEA,SAASe,4BAA4BA,CAAC2B,MAAc,EAAEQ,GAAe;EACnE,MAAMC,GAAG,GAAGD,GAAG,CAACE,eAAe;EAC/B,MAAMC,GAAG,GAAGX,MAAM,CAAChI,WAAW,CAAC0I,eAAe;EAC9C,OAAO,IAAArJ,oBAAA,CAAAuJ,sBAAsB,EAACD,GAAG,EAAEF,GAAG,CAAC,GAAG,CAAC;AAC7C;AAEA,SAASxB,mBAAmBA,CAAC1D,OAAkC,EAAEV,GAAa;EAC5E,OAAOU,OAAO,IAAIA,OAAO,CAAC2E,aAAa,EAAE,IAAI,CAAC,IAAAlJ,cAAA,CAAAoI,oBAAoB,EAACvE,GAAG,CAAC;AACzE;AAEA;;AAEA,SAASsE,wBAAwBA,CAACpH,QAAkB;EAClD,OAAOA,QAAQ,CAACU,CAAC,CAACR,OAAO,CAAC4I,WAAW,KAAK,KAAK;AACjD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}