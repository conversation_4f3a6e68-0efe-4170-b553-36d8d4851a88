{"ast": null, "code": "import PropTypes from 'prop-types';\nimport chainPropTypes from \"../chainPropTypes/index.js\";\nfunction isClassComponent(elementType) {\n  // elementType.prototype?.isReactComponent\n  const {\n    prototype = {}\n  } = elementType;\n  return Boolean(prototype.isReactComponent);\n}\nfunction acceptingRef(props, propName, componentName, location, propFullName) {\n  const element = props[propName];\n  const safePropName = propFullName || propName;\n  if (element == null ||\n  // When server-side rendering React doesn't warn either.\n  // This is not an accurate check for SSR.\n  // This is only in place for Emotion compat.\n  // TODO: Revisit once https://github.com/facebook/react/issues/20047 is resolved.\n  typeof window === 'undefined') {\n    return null;\n  }\n  let warningHint;\n  const elementType = element.type;\n  /**\n   * Blacklisting instead of whitelisting\n   *\n   * Blacklisting will miss some components, such as React.Fragment. Those will at least\n   * trigger a warning in React.\n   * We can't whitelist because there is no safe way to detect React.forwardRef\n   * or class components. \"Safe\" means there's no public API.\n   *\n   */\n  if (typeof elementType === 'function' && !isClassComponent(elementType)) {\n    warningHint = 'Did you accidentally use a plain function component for an element instead?';\n  }\n  if (warningHint !== undefined) {\n    return new Error(`Invalid ${location} \\`${safePropName}\\` supplied to \\`${componentName}\\`. ` + `Expected an element that can hold a ref. ${warningHint} ` + 'For more information see https://mui.com/r/caveat-with-refs-guide');\n  }\n  return null;\n}\nconst elementAcceptingRef = chainPropTypes(PropTypes.element, acceptingRef);\nelementAcceptingRef.isRequired = chainPropTypes(PropTypes.element.isRequired, acceptingRef);\nexport default elementAcceptingRef;", "map": {"version": 3, "names": ["PropTypes", "chainPropTypes", "isClassComponent", "elementType", "prototype", "Boolean", "isReactComponent", "acceptingRef", "props", "propName", "componentName", "location", "prop<PERSON><PERSON><PERSON><PERSON>", "element", "safePropName", "window", "warningHint", "type", "undefined", "Error", "elementAcceptingRef", "isRequired"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/base/node_modules/@mui/utils/esm/elementAcceptingRef/elementAcceptingRef.js"], "sourcesContent": ["import PropTypes from 'prop-types';\nimport chainPropTypes from \"../chainPropTypes/index.js\";\nfunction isClassComponent(elementType) {\n  // elementType.prototype?.isReactComponent\n  const {\n    prototype = {}\n  } = elementType;\n  return Boolean(prototype.isReactComponent);\n}\nfunction acceptingRef(props, propName, componentName, location, propFullName) {\n  const element = props[propName];\n  const safePropName = propFullName || propName;\n  if (element == null ||\n  // When server-side rendering React doesn't warn either.\n  // This is not an accurate check for SSR.\n  // This is only in place for Emotion compat.\n  // TODO: Revisit once https://github.com/facebook/react/issues/20047 is resolved.\n  typeof window === 'undefined') {\n    return null;\n  }\n  let warningHint;\n  const elementType = element.type;\n  /**\n   * Blacklisting instead of whitelisting\n   *\n   * Blacklisting will miss some components, such as React.Fragment. Those will at least\n   * trigger a warning in React.\n   * We can't whitelist because there is no safe way to detect React.forwardRef\n   * or class components. \"Safe\" means there's no public API.\n   *\n   */\n  if (typeof elementType === 'function' && !isClassComponent(elementType)) {\n    warningHint = 'Did you accidentally use a plain function component for an element instead?';\n  }\n  if (warningHint !== undefined) {\n    return new Error(`Invalid ${location} \\`${safePropName}\\` supplied to \\`${componentName}\\`. ` + `Expected an element that can hold a ref. ${warningHint} ` + 'For more information see https://mui.com/r/caveat-with-refs-guide');\n  }\n  return null;\n}\nconst elementAcceptingRef = chainPropTypes(PropTypes.element, acceptingRef);\nelementAcceptingRef.isRequired = chainPropTypes(PropTypes.element.isRequired, acceptingRef);\nexport default elementAcceptingRef;"], "mappings": "AAAA,OAAOA,SAAS,MAAM,YAAY;AAClC,OAAOC,cAAc,MAAM,4BAA4B;AACvD,SAASC,gBAAgBA,CAACC,WAAW,EAAE;EACrC;EACA,MAAM;IACJC,SAAS,GAAG,CAAC;EACf,CAAC,GAAGD,WAAW;EACf,OAAOE,OAAO,CAACD,SAAS,CAACE,gBAAgB,CAAC;AAC5C;AACA,SAASC,YAAYA,CAACC,KAAK,EAAEC,QAAQ,EAAEC,aAAa,EAAEC,QAAQ,EAAEC,YAAY,EAAE;EAC5E,MAAMC,OAAO,GAAGL,KAAK,CAACC,QAAQ,CAAC;EAC/B,MAAMK,YAAY,GAAGF,YAAY,IAAIH,QAAQ;EAC7C,IAAII,OAAO,IAAI,IAAI;EACnB;EACA;EACA;EACA;EACA,OAAOE,MAAM,KAAK,WAAW,EAAE;IAC7B,OAAO,IAAI;EACb;EACA,IAAIC,WAAW;EACf,MAAMb,WAAW,GAAGU,OAAO,CAACI,IAAI;EAChC;AACF;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACE,IAAI,OAAOd,WAAW,KAAK,UAAU,IAAI,CAACD,gBAAgB,CAACC,WAAW,CAAC,EAAE;IACvEa,WAAW,GAAG,6EAA6E;EAC7F;EACA,IAAIA,WAAW,KAAKE,SAAS,EAAE;IAC7B,OAAO,IAAIC,KAAK,CAAC,WAAWR,QAAQ,MAAMG,YAAY,oBAAoBJ,aAAa,MAAM,GAAG,4CAA4CM,WAAW,GAAG,GAAG,mEAAmE,CAAC;EACnO;EACA,OAAO,IAAI;AACb;AACA,MAAMI,mBAAmB,GAAGnB,cAAc,CAACD,SAAS,CAACa,OAAO,EAAEN,YAAY,CAAC;AAC3Ea,mBAAmB,CAACC,UAAU,GAAGpB,cAAc,CAACD,SAAS,CAACa,OAAO,CAACQ,UAAU,EAAEd,YAAY,CAAC;AAC3F,eAAea,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}