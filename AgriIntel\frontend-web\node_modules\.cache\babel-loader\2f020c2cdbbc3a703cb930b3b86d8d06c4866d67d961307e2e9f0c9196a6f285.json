{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.MIN_SECONDARY_WRITE_WIRE_VERSION = void 0;\nexports.writableServerSelector = writableServerSelector;\nexports.sameServerSelector = sameServerSelector;\nexports.secondaryWritableServerSelector = secondaryWritableServerSelector;\nexports.readPreferenceServerSelector = readPreferenceServerSelector;\nconst error_1 = require(\"../error\");\nconst read_preference_1 = require(\"../read_preference\");\nconst common_1 = require(\"./common\");\n// max staleness constants\nconst IDLE_WRITE_PERIOD = 10000;\nconst SMALLEST_MAX_STALENESS_SECONDS = 90;\n//  Minimum version to try writes on secondaries.\nexports.MIN_SECONDARY_WRITE_WIRE_VERSION = 13;\n/**\n * Returns a server selector that selects for writable servers\n */\nfunction writableServerSelector() {\n  return function writableServer(topologyDescription, servers) {\n    return latencyWindowReducer(topologyDescription, servers.filter(s => s.isWritable));\n  };\n}\n/**\n * The purpose of this selector is to select the same server, only\n * if it is in a state that it can have commands sent to it.\n */\nfunction sameServerSelector(description) {\n  return function sameServerSelector(topologyDescription, servers) {\n    if (!description) return [];\n    // Filter the servers to match the provided description only if\n    // the type is not unknown.\n    return servers.filter(sd => {\n      return sd.address === description.address && sd.type !== common_1.ServerType.Unknown;\n    });\n  };\n}\n/**\n * Returns a server selector that uses a read preference to select a\n * server potentially for a write on a secondary.\n */\nfunction secondaryWritableServerSelector(wireVersion, readPreference) {\n  // If server version < 5.0, read preference always primary.\n  // If server version >= 5.0...\n  // - If read preference is supplied, use that.\n  // - If no read preference is supplied, use primary.\n  if (!readPreference || !wireVersion || wireVersion && wireVersion < exports.MIN_SECONDARY_WRITE_WIRE_VERSION) {\n    return readPreferenceServerSelector(read_preference_1.ReadPreference.primary);\n  }\n  return readPreferenceServerSelector(readPreference);\n}\n/**\n * Reduces the passed in array of servers by the rules of the \"Max Staleness\" specification\n * found here:\n *\n * @see https://github.com/mongodb/specifications/blob/master/source/max-staleness/max-staleness.md\n *\n * @param readPreference - The read preference providing max staleness guidance\n * @param topologyDescription - The topology description\n * @param servers - The list of server descriptions to be reduced\n * @returns The list of servers that satisfy the requirements of max staleness\n */\nfunction maxStalenessReducer(readPreference, topologyDescription, servers) {\n  if (readPreference.maxStalenessSeconds == null || readPreference.maxStalenessSeconds < 0) {\n    return servers;\n  }\n  const maxStaleness = readPreference.maxStalenessSeconds;\n  const maxStalenessVariance = (topologyDescription.heartbeatFrequencyMS + IDLE_WRITE_PERIOD) / 1000;\n  if (maxStaleness < maxStalenessVariance) {\n    throw new error_1.MongoInvalidArgumentError(`Option \"maxStalenessSeconds\" must be at least ${maxStalenessVariance} seconds`);\n  }\n  if (maxStaleness < SMALLEST_MAX_STALENESS_SECONDS) {\n    throw new error_1.MongoInvalidArgumentError(`Option \"maxStalenessSeconds\" must be at least ${SMALLEST_MAX_STALENESS_SECONDS} seconds`);\n  }\n  if (topologyDescription.type === common_1.TopologyType.ReplicaSetWithPrimary) {\n    const primary = Array.from(topologyDescription.servers.values()).filter(primaryFilter)[0];\n    return servers.reduce((result, server) => {\n      const stalenessMS = server.lastUpdateTime - server.lastWriteDate - (primary.lastUpdateTime - primary.lastWriteDate) + topologyDescription.heartbeatFrequencyMS;\n      const staleness = stalenessMS / 1000;\n      const maxStalenessSeconds = readPreference.maxStalenessSeconds ?? 0;\n      if (staleness <= maxStalenessSeconds) {\n        result.push(server);\n      }\n      return result;\n    }, []);\n  }\n  if (topologyDescription.type === common_1.TopologyType.ReplicaSetNoPrimary) {\n    if (servers.length === 0) {\n      return servers;\n    }\n    const sMax = servers.reduce((max, s) => s.lastWriteDate > max.lastWriteDate ? s : max);\n    return servers.reduce((result, server) => {\n      const stalenessMS = sMax.lastWriteDate - server.lastWriteDate + topologyDescription.heartbeatFrequencyMS;\n      const staleness = stalenessMS / 1000;\n      const maxStalenessSeconds = readPreference.maxStalenessSeconds ?? 0;\n      if (staleness <= maxStalenessSeconds) {\n        result.push(server);\n      }\n      return result;\n    }, []);\n  }\n  return servers;\n}\n/**\n * Determines whether a server's tags match a given set of tags\n *\n * @param tagSet - The requested tag set to match\n * @param serverTags - The server's tags\n */\nfunction tagSetMatch(tagSet, serverTags) {\n  const keys = Object.keys(tagSet);\n  const serverTagKeys = Object.keys(serverTags);\n  for (let i = 0; i < keys.length; ++i) {\n    const key = keys[i];\n    if (serverTagKeys.indexOf(key) === -1 || serverTags[key] !== tagSet[key]) {\n      return false;\n    }\n  }\n  return true;\n}\n/**\n * Reduces a set of server descriptions based on tags requested by the read preference\n *\n * @param readPreference - The read preference providing the requested tags\n * @param servers - The list of server descriptions to reduce\n * @returns The list of servers matching the requested tags\n */\nfunction tagSetReducer(readPreference, servers) {\n  if (readPreference.tags == null || Array.isArray(readPreference.tags) && readPreference.tags.length === 0) {\n    return servers;\n  }\n  for (let i = 0; i < readPreference.tags.length; ++i) {\n    const tagSet = readPreference.tags[i];\n    const serversMatchingTagset = servers.reduce((matched, server) => {\n      if (tagSetMatch(tagSet, server.tags)) matched.push(server);\n      return matched;\n    }, []);\n    if (serversMatchingTagset.length) {\n      return serversMatchingTagset;\n    }\n  }\n  return [];\n}\n/**\n * Reduces a list of servers to ensure they fall within an acceptable latency window. This is\n * further specified in the \"Server Selection\" specification, found here:\n *\n * @see https://github.com/mongodb/specifications/blob/master/source/server-selection/server-selection.md\n *\n * @param topologyDescription - The topology description\n * @param servers - The list of servers to reduce\n * @returns The servers which fall within an acceptable latency window\n */\nfunction latencyWindowReducer(topologyDescription, servers) {\n  const low = servers.reduce((min, server) => Math.min(server.roundTripTime, min), Infinity);\n  const high = low + topologyDescription.localThresholdMS;\n  return servers.reduce((result, server) => {\n    if (server.roundTripTime <= high && server.roundTripTime >= low) result.push(server);\n    return result;\n  }, []);\n}\n// filters\nfunction primaryFilter(server) {\n  return server.type === common_1.ServerType.RSPrimary;\n}\nfunction secondaryFilter(server) {\n  return server.type === common_1.ServerType.RSSecondary;\n}\nfunction nearestFilter(server) {\n  return server.type === common_1.ServerType.RSSecondary || server.type === common_1.ServerType.RSPrimary;\n}\nfunction knownFilter(server) {\n  return server.type !== common_1.ServerType.Unknown;\n}\nfunction loadBalancerFilter(server) {\n  return server.type === common_1.ServerType.LoadBalancer;\n}\n/**\n * Returns a function which selects servers based on a provided read preference\n *\n * @param readPreference - The read preference to select with\n */\nfunction readPreferenceServerSelector(readPreference) {\n  if (!readPreference.isValid()) {\n    throw new error_1.MongoInvalidArgumentError('Invalid read preference specified');\n  }\n  return function readPreferenceServers(topologyDescription, servers, deprioritized = []) {\n    const commonWireVersion = topologyDescription.commonWireVersion;\n    if (commonWireVersion && readPreference.minWireVersion && readPreference.minWireVersion > commonWireVersion) {\n      throw new error_1.MongoCompatibilityError(`Minimum wire version '${readPreference.minWireVersion}' required, but found '${commonWireVersion}'`);\n    }\n    if (topologyDescription.type === common_1.TopologyType.LoadBalanced) {\n      return servers.filter(loadBalancerFilter);\n    }\n    if (topologyDescription.type === common_1.TopologyType.Unknown) {\n      return [];\n    }\n    if (topologyDescription.type === common_1.TopologyType.Single) {\n      return latencyWindowReducer(topologyDescription, servers.filter(knownFilter));\n    }\n    if (topologyDescription.type === common_1.TopologyType.Sharded) {\n      const filtered = servers.filter(server => {\n        return !deprioritized.includes(server);\n      });\n      const selectable = filtered.length > 0 ? filtered : deprioritized;\n      return latencyWindowReducer(topologyDescription, selectable.filter(knownFilter));\n    }\n    const mode = readPreference.mode;\n    if (mode === read_preference_1.ReadPreference.PRIMARY) {\n      return servers.filter(primaryFilter);\n    }\n    if (mode === read_preference_1.ReadPreference.PRIMARY_PREFERRED) {\n      const result = servers.filter(primaryFilter);\n      if (result.length) {\n        return result;\n      }\n    }\n    const filter = mode === read_preference_1.ReadPreference.NEAREST ? nearestFilter : secondaryFilter;\n    const selectedServers = latencyWindowReducer(topologyDescription, tagSetReducer(readPreference, maxStalenessReducer(readPreference, topologyDescription, servers.filter(filter))));\n    if (mode === read_preference_1.ReadPreference.SECONDARY_PREFERRED && selectedServers.length === 0) {\n      return servers.filter(primaryFilter);\n    }\n    return selectedServers;\n  };\n}", "map": {"version": 3, "names": ["exports", "writableServerSelector", "sameServerSelector", "secondaryWritableServerSelector", "readPreferenceServerSelector", "error_1", "require", "read_preference_1", "common_1", "IDLE_WRITE_PERIOD", "SMALLEST_MAX_STALENESS_SECONDS", "MIN_SECONDARY_WRITE_WIRE_VERSION", "writableServer", "topologyDescription", "servers", "latencyWindowReducer", "filter", "s", "isWritable", "description", "sd", "address", "type", "ServerType", "Unknown", "wireVersion", "readPreference", "ReadPreference", "primary", "maxStalenessReducer", "maxStalenessSeconds", "maxStaleness", "maxStalenessVariance", "heartbeatFrequencyMS", "MongoInvalidArgumentError", "TopologyType", "ReplicaSetWithPrimary", "Array", "from", "values", "primaryFilter", "reduce", "result", "server", "stalenessMS", "lastUpdateTime", "lastWriteDate", "staleness", "push", "ReplicaSetNoPrimary", "length", "sMax", "max", "tagSetMatch", "tagSet", "serverTags", "keys", "Object", "serverTagKeys", "i", "key", "indexOf", "tagSetReducer", "tags", "isArray", "serversMatchingTagset", "matched", "low", "min", "Math", "roundTripTime", "Infinity", "high", "localThresholdMS", "RSPrimary", "secondaryFilter", "RSSecondary", "nearestFilter", "knownFilter", "loadBalancerFilter", "LoadBalancer", "<PERSON><PERSON><PERSON><PERSON>", "readPreferenceServers", "deprioritized", "commonWireVersion", "minWireVersion", "MongoCompatibilityError", "LoadBalanced", "Single", "Sharded", "filtered", "includes", "selectable", "mode", "PRIMARY", "PRIMARY_PREFERRED", "NEAREST", "selectedServers", "SECONDARY_PREFERRED"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\sdam\\server_selection.ts"], "sourcesContent": ["import { MongoCompatibilityError, MongoInvalidArgumentError } from '../error';\nimport { ReadPreference } from '../read_preference';\nimport { ServerType, TopologyType } from './common';\nimport type { ServerDescription, TagSet } from './server_description';\nimport type { TopologyDescription } from './topology_description';\n\n// max staleness constants\nconst IDLE_WRITE_PERIOD = 10000;\nconst SMALLEST_MAX_STALENESS_SECONDS = 90;\n\n//  Minimum version to try writes on secondaries.\nexport const MIN_SECONDARY_WRITE_WIRE_VERSION = 13;\n\n/** @internal */\nexport type ServerSelector = (\n  topologyDescription: TopologyDescription,\n  servers: ServerDescription[],\n  deprioritized?: ServerDescription[]\n) => ServerDescription[];\n\n/**\n * Returns a server selector that selects for writable servers\n */\nexport function writableServerSelector(): ServerSelector {\n  return function writableServer(\n    topologyDescription: TopologyDescription,\n    servers: ServerDescription[]\n  ): ServerDescription[] {\n    return latencyWindowReducer(\n      topologyDescription,\n      servers.filter((s: ServerDescription) => s.isWritable)\n    );\n  };\n}\n\n/**\n * The purpose of this selector is to select the same server, only\n * if it is in a state that it can have commands sent to it.\n */\nexport function sameServerSelector(description?: ServerDescription): ServerSelector {\n  return function sameServerSelector(\n    topologyDescription: TopologyDescription,\n    servers: ServerDescription[]\n  ): ServerDescription[] {\n    if (!description) return [];\n    // Filter the servers to match the provided description only if\n    // the type is not unknown.\n    return servers.filter(sd => {\n      return sd.address === description.address && sd.type !== ServerType.Unknown;\n    });\n  };\n}\n\n/**\n * Returns a server selector that uses a read preference to select a\n * server potentially for a write on a secondary.\n */\nexport function secondaryWritableServerSelector(\n  wireVersion?: number,\n  readPreference?: ReadPreference\n): ServerSelector {\n  // If server version < 5.0, read preference always primary.\n  // If server version >= 5.0...\n  // - If read preference is supplied, use that.\n  // - If no read preference is supplied, use primary.\n  if (\n    !readPreference ||\n    !wireVersion ||\n    (wireVersion && wireVersion < MIN_SECONDARY_WRITE_WIRE_VERSION)\n  ) {\n    return readPreferenceServerSelector(ReadPreference.primary);\n  }\n  return readPreferenceServerSelector(readPreference);\n}\n\n/**\n * Reduces the passed in array of servers by the rules of the \"Max Staleness\" specification\n * found here:\n *\n * @see https://github.com/mongodb/specifications/blob/master/source/max-staleness/max-staleness.md\n *\n * @param readPreference - The read preference providing max staleness guidance\n * @param topologyDescription - The topology description\n * @param servers - The list of server descriptions to be reduced\n * @returns The list of servers that satisfy the requirements of max staleness\n */\nfunction maxStalenessReducer(\n  readPreference: ReadPreference,\n  topologyDescription: TopologyDescription,\n  servers: ServerDescription[]\n): ServerDescription[] {\n  if (readPreference.maxStalenessSeconds == null || readPreference.maxStalenessSeconds < 0) {\n    return servers;\n  }\n\n  const maxStaleness = readPreference.maxStalenessSeconds;\n  const maxStalenessVariance =\n    (topologyDescription.heartbeatFrequencyMS + IDLE_WRITE_PERIOD) / 1000;\n  if (maxStaleness < maxStalenessVariance) {\n    throw new MongoInvalidArgumentError(\n      `Option \"maxStalenessSeconds\" must be at least ${maxStalenessVariance} seconds`\n    );\n  }\n\n  if (maxStaleness < SMALLEST_MAX_STALENESS_SECONDS) {\n    throw new MongoInvalidArgumentError(\n      `Option \"maxStalenessSeconds\" must be at least ${SMALLEST_MAX_STALENESS_SECONDS} seconds`\n    );\n  }\n\n  if (topologyDescription.type === TopologyType.ReplicaSetWithPrimary) {\n    const primary: ServerDescription = Array.from(topologyDescription.servers.values()).filter(\n      primaryFilter\n    )[0];\n\n    return servers.reduce((result: ServerDescription[], server: ServerDescription) => {\n      const stalenessMS =\n        server.lastUpdateTime -\n        server.lastWriteDate -\n        (primary.lastUpdateTime - primary.lastWriteDate) +\n        topologyDescription.heartbeatFrequencyMS;\n\n      const staleness = stalenessMS / 1000;\n      const maxStalenessSeconds = readPreference.maxStalenessSeconds ?? 0;\n      if (staleness <= maxStalenessSeconds) {\n        result.push(server);\n      }\n\n      return result;\n    }, []);\n  }\n\n  if (topologyDescription.type === TopologyType.ReplicaSetNoPrimary) {\n    if (servers.length === 0) {\n      return servers;\n    }\n\n    const sMax = servers.reduce((max: ServerDescription, s: ServerDescription) =>\n      s.lastWriteDate > max.lastWriteDate ? s : max\n    );\n\n    return servers.reduce((result: ServerDescription[], server: ServerDescription) => {\n      const stalenessMS =\n        sMax.lastWriteDate - server.lastWriteDate + topologyDescription.heartbeatFrequencyMS;\n\n      const staleness = stalenessMS / 1000;\n      const maxStalenessSeconds = readPreference.maxStalenessSeconds ?? 0;\n      if (staleness <= maxStalenessSeconds) {\n        result.push(server);\n      }\n\n      return result;\n    }, []);\n  }\n\n  return servers;\n}\n\n/**\n * Determines whether a server's tags match a given set of tags\n *\n * @param tagSet - The requested tag set to match\n * @param serverTags - The server's tags\n */\nfunction tagSetMatch(tagSet: TagSet, serverTags: TagSet) {\n  const keys = Object.keys(tagSet);\n  const serverTagKeys = Object.keys(serverTags);\n  for (let i = 0; i < keys.length; ++i) {\n    const key = keys[i];\n    if (serverTagKeys.indexOf(key) === -1 || serverTags[key] !== tagSet[key]) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\n/**\n * Reduces a set of server descriptions based on tags requested by the read preference\n *\n * @param readPreference - The read preference providing the requested tags\n * @param servers - The list of server descriptions to reduce\n * @returns The list of servers matching the requested tags\n */\nfunction tagSetReducer(\n  readPreference: ReadPreference,\n  servers: ServerDescription[]\n): ServerDescription[] {\n  if (\n    readPreference.tags == null ||\n    (Array.isArray(readPreference.tags) && readPreference.tags.length === 0)\n  ) {\n    return servers;\n  }\n\n  for (let i = 0; i < readPreference.tags.length; ++i) {\n    const tagSet = readPreference.tags[i];\n    const serversMatchingTagset = servers.reduce(\n      (matched: ServerDescription[], server: ServerDescription) => {\n        if (tagSetMatch(tagSet, server.tags)) matched.push(server);\n        return matched;\n      },\n      []\n    );\n\n    if (serversMatchingTagset.length) {\n      return serversMatchingTagset;\n    }\n  }\n\n  return [];\n}\n\n/**\n * Reduces a list of servers to ensure they fall within an acceptable latency window. This is\n * further specified in the \"Server Selection\" specification, found here:\n *\n * @see https://github.com/mongodb/specifications/blob/master/source/server-selection/server-selection.md\n *\n * @param topologyDescription - The topology description\n * @param servers - The list of servers to reduce\n * @returns The servers which fall within an acceptable latency window\n */\nfunction latencyWindowReducer(\n  topologyDescription: TopologyDescription,\n  servers: ServerDescription[]\n): ServerDescription[] {\n  const low = servers.reduce(\n    (min: number, server: ServerDescription) => Math.min(server.roundTripTime, min),\n    Infinity\n  );\n\n  const high = low + topologyDescription.localThresholdMS;\n  return servers.reduce((result: ServerDescription[], server: ServerDescription) => {\n    if (server.roundTripTime <= high && server.roundTripTime >= low) result.push(server);\n    return result;\n  }, []);\n}\n\n// filters\nfunction primaryFilter(server: ServerDescription): boolean {\n  return server.type === ServerType.RSPrimary;\n}\n\nfunction secondaryFilter(server: ServerDescription): boolean {\n  return server.type === ServerType.RSSecondary;\n}\n\nfunction nearestFilter(server: ServerDescription): boolean {\n  return server.type === ServerType.RSSecondary || server.type === ServerType.RSPrimary;\n}\n\nfunction knownFilter(server: ServerDescription): boolean {\n  return server.type !== ServerType.Unknown;\n}\n\nfunction loadBalancerFilter(server: ServerDescription): boolean {\n  return server.type === ServerType.LoadBalancer;\n}\n\n/**\n * Returns a function which selects servers based on a provided read preference\n *\n * @param readPreference - The read preference to select with\n */\nexport function readPreferenceServerSelector(readPreference: ReadPreference): ServerSelector {\n  if (!readPreference.isValid()) {\n    throw new MongoInvalidArgumentError('Invalid read preference specified');\n  }\n\n  return function readPreferenceServers(\n    topologyDescription: TopologyDescription,\n    servers: ServerDescription[],\n    deprioritized: ServerDescription[] = []\n  ): ServerDescription[] {\n    const commonWireVersion = topologyDescription.commonWireVersion;\n    if (\n      commonWireVersion &&\n      readPreference.minWireVersion &&\n      readPreference.minWireVersion > commonWireVersion\n    ) {\n      throw new MongoCompatibilityError(\n        `Minimum wire version '${readPreference.minWireVersion}' required, but found '${commonWireVersion}'`\n      );\n    }\n\n    if (topologyDescription.type === TopologyType.LoadBalanced) {\n      return servers.filter(loadBalancerFilter);\n    }\n\n    if (topologyDescription.type === TopologyType.Unknown) {\n      return [];\n    }\n\n    if (topologyDescription.type === TopologyType.Single) {\n      return latencyWindowReducer(topologyDescription, servers.filter(knownFilter));\n    }\n\n    if (topologyDescription.type === TopologyType.Sharded) {\n      const filtered = servers.filter(server => {\n        return !deprioritized.includes(server);\n      });\n      const selectable = filtered.length > 0 ? filtered : deprioritized;\n      return latencyWindowReducer(topologyDescription, selectable.filter(knownFilter));\n    }\n\n    const mode = readPreference.mode;\n    if (mode === ReadPreference.PRIMARY) {\n      return servers.filter(primaryFilter);\n    }\n\n    if (mode === ReadPreference.PRIMARY_PREFERRED) {\n      const result = servers.filter(primaryFilter);\n      if (result.length) {\n        return result;\n      }\n    }\n\n    const filter = mode === ReadPreference.NEAREST ? nearestFilter : secondaryFilter;\n    const selectedServers = latencyWindowReducer(\n      topologyDescription,\n      tagSetReducer(\n        readPreference,\n        maxStalenessReducer(readPreference, topologyDescription, servers.filter(filter))\n      )\n    );\n\n    if (mode === ReadPreference.SECONDARY_PREFERRED && selectedServers.length === 0) {\n      return servers.filter(primaryFilter);\n    }\n\n    return selectedServers;\n  };\n}\n"], "mappings": ";;;;;;AAuBAA,OAAA,CAAAC,sBAAA,GAAAA,sBAAA;AAgBAD,OAAA,CAAAE,kBAAA,GAAAA,kBAAA;AAkBAF,OAAA,CAAAG,+BAAA,GAAAA,+BAAA;AAgNAH,OAAA,CAAAI,4BAAA,GAAAA,4BAAA;AAzQA,MAAAC,OAAA,GAAAC,OAAA;AACA,MAAAC,iBAAA,GAAAD,OAAA;AACA,MAAAE,QAAA,GAAAF,OAAA;AAIA;AACA,MAAMG,iBAAiB,GAAG,KAAK;AAC/B,MAAMC,8BAA8B,GAAG,EAAE;AAEzC;AACaV,OAAA,CAAAW,gCAAgC,GAAG,EAAE;AASlD;;;AAGA,SAAgBV,sBAAsBA,CAAA;EACpC,OAAO,SAASW,cAAcA,CAC5BC,mBAAwC,EACxCC,OAA4B;IAE5B,OAAOC,oBAAoB,CACzBF,mBAAmB,EACnBC,OAAO,CAACE,MAAM,CAAEC,CAAoB,IAAKA,CAAC,CAACC,UAAU,CAAC,CACvD;EACH,CAAC;AACH;AAEA;;;;AAIA,SAAgBhB,kBAAkBA,CAACiB,WAA+B;EAChE,OAAO,SAASjB,kBAAkBA,CAChCW,mBAAwC,EACxCC,OAA4B;IAE5B,IAAI,CAACK,WAAW,EAAE,OAAO,EAAE;IAC3B;IACA;IACA,OAAOL,OAAO,CAACE,MAAM,CAACI,EAAE,IAAG;MACzB,OAAOA,EAAE,CAACC,OAAO,KAAKF,WAAW,CAACE,OAAO,IAAID,EAAE,CAACE,IAAI,KAAKd,QAAA,CAAAe,UAAU,CAACC,OAAO;IAC7E,CAAC,CAAC;EACJ,CAAC;AACH;AAEA;;;;AAIA,SAAgBrB,+BAA+BA,CAC7CsB,WAAoB,EACpBC,cAA+B;EAE/B;EACA;EACA;EACA;EACA,IACE,CAACA,cAAc,IACf,CAACD,WAAW,IACXA,WAAW,IAAIA,WAAW,GAAGzB,OAAA,CAAAW,gCAAiC,EAC/D;IACA,OAAOP,4BAA4B,CAACG,iBAAA,CAAAoB,cAAc,CAACC,OAAO,CAAC;EAC7D;EACA,OAAOxB,4BAA4B,CAACsB,cAAc,CAAC;AACrD;AAEA;;;;;;;;;;;AAWA,SAASG,mBAAmBA,CAC1BH,cAA8B,EAC9Bb,mBAAwC,EACxCC,OAA4B;EAE5B,IAAIY,cAAc,CAACI,mBAAmB,IAAI,IAAI,IAAIJ,cAAc,CAACI,mBAAmB,GAAG,CAAC,EAAE;IACxF,OAAOhB,OAAO;EAChB;EAEA,MAAMiB,YAAY,GAAGL,cAAc,CAACI,mBAAmB;EACvD,MAAME,oBAAoB,GACxB,CAACnB,mBAAmB,CAACoB,oBAAoB,GAAGxB,iBAAiB,IAAI,IAAI;EACvE,IAAIsB,YAAY,GAAGC,oBAAoB,EAAE;IACvC,MAAM,IAAI3B,OAAA,CAAA6B,yBAAyB,CACjC,iDAAiDF,oBAAoB,UAAU,CAChF;EACH;EAEA,IAAID,YAAY,GAAGrB,8BAA8B,EAAE;IACjD,MAAM,IAAIL,OAAA,CAAA6B,yBAAyB,CACjC,iDAAiDxB,8BAA8B,UAAU,CAC1F;EACH;EAEA,IAAIG,mBAAmB,CAACS,IAAI,KAAKd,QAAA,CAAA2B,YAAY,CAACC,qBAAqB,EAAE;IACnE,MAAMR,OAAO,GAAsBS,KAAK,CAACC,IAAI,CAACzB,mBAAmB,CAACC,OAAO,CAACyB,MAAM,EAAE,CAAC,CAACvB,MAAM,CACxFwB,aAAa,CACd,CAAC,CAAC,CAAC;IAEJ,OAAO1B,OAAO,CAAC2B,MAAM,CAAC,CAACC,MAA2B,EAAEC,MAAyB,KAAI;MAC/E,MAAMC,WAAW,GACfD,MAAM,CAACE,cAAc,GACrBF,MAAM,CAACG,aAAa,IACnBlB,OAAO,CAACiB,cAAc,GAAGjB,OAAO,CAACkB,aAAa,CAAC,GAChDjC,mBAAmB,CAACoB,oBAAoB;MAE1C,MAAMc,SAAS,GAAGH,WAAW,GAAG,IAAI;MACpC,MAAMd,mBAAmB,GAAGJ,cAAc,CAACI,mBAAmB,IAAI,CAAC;MACnE,IAAIiB,SAAS,IAAIjB,mBAAmB,EAAE;QACpCY,MAAM,CAACM,IAAI,CAACL,MAAM,CAAC;MACrB;MAEA,OAAOD,MAAM;IACf,CAAC,EAAE,EAAE,CAAC;EACR;EAEA,IAAI7B,mBAAmB,CAACS,IAAI,KAAKd,QAAA,CAAA2B,YAAY,CAACc,mBAAmB,EAAE;IACjE,IAAInC,OAAO,CAACoC,MAAM,KAAK,CAAC,EAAE;MACxB,OAAOpC,OAAO;IAChB;IAEA,MAAMqC,IAAI,GAAGrC,OAAO,CAAC2B,MAAM,CAAC,CAACW,GAAsB,EAAEnC,CAAoB,KACvEA,CAAC,CAAC6B,aAAa,GAAGM,GAAG,CAACN,aAAa,GAAG7B,CAAC,GAAGmC,GAAG,CAC9C;IAED,OAAOtC,OAAO,CAAC2B,MAAM,CAAC,CAACC,MAA2B,EAAEC,MAAyB,KAAI;MAC/E,MAAMC,WAAW,GACfO,IAAI,CAACL,aAAa,GAAGH,MAAM,CAACG,aAAa,GAAGjC,mBAAmB,CAACoB,oBAAoB;MAEtF,MAAMc,SAAS,GAAGH,WAAW,GAAG,IAAI;MACpC,MAAMd,mBAAmB,GAAGJ,cAAc,CAACI,mBAAmB,IAAI,CAAC;MACnE,IAAIiB,SAAS,IAAIjB,mBAAmB,EAAE;QACpCY,MAAM,CAACM,IAAI,CAACL,MAAM,CAAC;MACrB;MAEA,OAAOD,MAAM;IACf,CAAC,EAAE,EAAE,CAAC;EACR;EAEA,OAAO5B,OAAO;AAChB;AAEA;;;;;;AAMA,SAASuC,WAAWA,CAACC,MAAc,EAAEC,UAAkB;EACrD,MAAMC,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACF,MAAM,CAAC;EAChC,MAAMI,aAAa,GAAGD,MAAM,CAACD,IAAI,CAACD,UAAU,CAAC;EAC7C,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,IAAI,CAACN,MAAM,EAAE,EAAES,CAAC,EAAE;IACpC,MAAMC,GAAG,GAAGJ,IAAI,CAACG,CAAC,CAAC;IACnB,IAAID,aAAa,CAACG,OAAO,CAACD,GAAG,CAAC,KAAK,CAAC,CAAC,IAAIL,UAAU,CAACK,GAAG,CAAC,KAAKN,MAAM,CAACM,GAAG,CAAC,EAAE;MACxE,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb;AAEA;;;;;;;AAOA,SAASE,aAAaA,CACpBpC,cAA8B,EAC9BZ,OAA4B;EAE5B,IACEY,cAAc,CAACqC,IAAI,IAAI,IAAI,IAC1B1B,KAAK,CAAC2B,OAAO,CAACtC,cAAc,CAACqC,IAAI,CAAC,IAAIrC,cAAc,CAACqC,IAAI,CAACb,MAAM,KAAK,CAAE,EACxE;IACA,OAAOpC,OAAO;EAChB;EAEA,KAAK,IAAI6C,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjC,cAAc,CAACqC,IAAI,CAACb,MAAM,EAAE,EAAES,CAAC,EAAE;IACnD,MAAML,MAAM,GAAG5B,cAAc,CAACqC,IAAI,CAACJ,CAAC,CAAC;IACrC,MAAMM,qBAAqB,GAAGnD,OAAO,CAAC2B,MAAM,CAC1C,CAACyB,OAA4B,EAAEvB,MAAyB,KAAI;MAC1D,IAAIU,WAAW,CAACC,MAAM,EAAEX,MAAM,CAACoB,IAAI,CAAC,EAAEG,OAAO,CAAClB,IAAI,CAACL,MAAM,CAAC;MAC1D,OAAOuB,OAAO;IAChB,CAAC,EACD,EAAE,CACH;IAED,IAAID,qBAAqB,CAACf,MAAM,EAAE;MAChC,OAAOe,qBAAqB;IAC9B;EACF;EAEA,OAAO,EAAE;AACX;AAEA;;;;;;;;;;AAUA,SAASlD,oBAAoBA,CAC3BF,mBAAwC,EACxCC,OAA4B;EAE5B,MAAMqD,GAAG,GAAGrD,OAAO,CAAC2B,MAAM,CACxB,CAAC2B,GAAW,EAAEzB,MAAyB,KAAK0B,IAAI,CAACD,GAAG,CAACzB,MAAM,CAAC2B,aAAa,EAAEF,GAAG,CAAC,EAC/EG,QAAQ,CACT;EAED,MAAMC,IAAI,GAAGL,GAAG,GAAGtD,mBAAmB,CAAC4D,gBAAgB;EACvD,OAAO3D,OAAO,CAAC2B,MAAM,CAAC,CAACC,MAA2B,EAAEC,MAAyB,KAAI;IAC/E,IAAIA,MAAM,CAAC2B,aAAa,IAAIE,IAAI,IAAI7B,MAAM,CAAC2B,aAAa,IAAIH,GAAG,EAAEzB,MAAM,CAACM,IAAI,CAACL,MAAM,CAAC;IACpF,OAAOD,MAAM;EACf,CAAC,EAAE,EAAE,CAAC;AACR;AAEA;AACA,SAASF,aAAaA,CAACG,MAAyB;EAC9C,OAAOA,MAAM,CAACrB,IAAI,KAAKd,QAAA,CAAAe,UAAU,CAACmD,SAAS;AAC7C;AAEA,SAASC,eAAeA,CAAChC,MAAyB;EAChD,OAAOA,MAAM,CAACrB,IAAI,KAAKd,QAAA,CAAAe,UAAU,CAACqD,WAAW;AAC/C;AAEA,SAASC,aAAaA,CAAClC,MAAyB;EAC9C,OAAOA,MAAM,CAACrB,IAAI,KAAKd,QAAA,CAAAe,UAAU,CAACqD,WAAW,IAAIjC,MAAM,CAACrB,IAAI,KAAKd,QAAA,CAAAe,UAAU,CAACmD,SAAS;AACvF;AAEA,SAASI,WAAWA,CAACnC,MAAyB;EAC5C,OAAOA,MAAM,CAACrB,IAAI,KAAKd,QAAA,CAAAe,UAAU,CAACC,OAAO;AAC3C;AAEA,SAASuD,kBAAkBA,CAACpC,MAAyB;EACnD,OAAOA,MAAM,CAACrB,IAAI,KAAKd,QAAA,CAAAe,UAAU,CAACyD,YAAY;AAChD;AAEA;;;;;AAKA,SAAgB5E,4BAA4BA,CAACsB,cAA8B;EACzE,IAAI,CAACA,cAAc,CAACuD,OAAO,EAAE,EAAE;IAC7B,MAAM,IAAI5E,OAAA,CAAA6B,yBAAyB,CAAC,mCAAmC,CAAC;EAC1E;EAEA,OAAO,SAASgD,qBAAqBA,CACnCrE,mBAAwC,EACxCC,OAA4B,EAC5BqE,aAAA,GAAqC,EAAE;IAEvC,MAAMC,iBAAiB,GAAGvE,mBAAmB,CAACuE,iBAAiB;IAC/D,IACEA,iBAAiB,IACjB1D,cAAc,CAAC2D,cAAc,IAC7B3D,cAAc,CAAC2D,cAAc,GAAGD,iBAAiB,EACjD;MACA,MAAM,IAAI/E,OAAA,CAAAiF,uBAAuB,CAC/B,yBAAyB5D,cAAc,CAAC2D,cAAc,0BAA0BD,iBAAiB,GAAG,CACrG;IACH;IAEA,IAAIvE,mBAAmB,CAACS,IAAI,KAAKd,QAAA,CAAA2B,YAAY,CAACoD,YAAY,EAAE;MAC1D,OAAOzE,OAAO,CAACE,MAAM,CAAC+D,kBAAkB,CAAC;IAC3C;IAEA,IAAIlE,mBAAmB,CAACS,IAAI,KAAKd,QAAA,CAAA2B,YAAY,CAACX,OAAO,EAAE;MACrD,OAAO,EAAE;IACX;IAEA,IAAIX,mBAAmB,CAACS,IAAI,KAAKd,QAAA,CAAA2B,YAAY,CAACqD,MAAM,EAAE;MACpD,OAAOzE,oBAAoB,CAACF,mBAAmB,EAAEC,OAAO,CAACE,MAAM,CAAC8D,WAAW,CAAC,CAAC;IAC/E;IAEA,IAAIjE,mBAAmB,CAACS,IAAI,KAAKd,QAAA,CAAA2B,YAAY,CAACsD,OAAO,EAAE;MACrD,MAAMC,QAAQ,GAAG5E,OAAO,CAACE,MAAM,CAAC2B,MAAM,IAAG;QACvC,OAAO,CAACwC,aAAa,CAACQ,QAAQ,CAAChD,MAAM,CAAC;MACxC,CAAC,CAAC;MACF,MAAMiD,UAAU,GAAGF,QAAQ,CAACxC,MAAM,GAAG,CAAC,GAAGwC,QAAQ,GAAGP,aAAa;MACjE,OAAOpE,oBAAoB,CAACF,mBAAmB,EAAE+E,UAAU,CAAC5E,MAAM,CAAC8D,WAAW,CAAC,CAAC;IAClF;IAEA,MAAMe,IAAI,GAAGnE,cAAc,CAACmE,IAAI;IAChC,IAAIA,IAAI,KAAKtF,iBAAA,CAAAoB,cAAc,CAACmE,OAAO,EAAE;MACnC,OAAOhF,OAAO,CAACE,MAAM,CAACwB,aAAa,CAAC;IACtC;IAEA,IAAIqD,IAAI,KAAKtF,iBAAA,CAAAoB,cAAc,CAACoE,iBAAiB,EAAE;MAC7C,MAAMrD,MAAM,GAAG5B,OAAO,CAACE,MAAM,CAACwB,aAAa,CAAC;MAC5C,IAAIE,MAAM,CAACQ,MAAM,EAAE;QACjB,OAAOR,MAAM;MACf;IACF;IAEA,MAAM1B,MAAM,GAAG6E,IAAI,KAAKtF,iBAAA,CAAAoB,cAAc,CAACqE,OAAO,GAAGnB,aAAa,GAAGF,eAAe;IAChF,MAAMsB,eAAe,GAAGlF,oBAAoB,CAC1CF,mBAAmB,EACnBiD,aAAa,CACXpC,cAAc,EACdG,mBAAmB,CAACH,cAAc,EAAEb,mBAAmB,EAAEC,OAAO,CAACE,MAAM,CAACA,MAAM,CAAC,CAAC,CACjF,CACF;IAED,IAAI6E,IAAI,KAAKtF,iBAAA,CAAAoB,cAAc,CAACuE,mBAAmB,IAAID,eAAe,CAAC/C,MAAM,KAAK,CAAC,EAAE;MAC/E,OAAOpC,OAAO,CAACE,MAAM,CAACwB,aAAa,CAAC;IACtC;IAEA,OAAOyD,eAAe;EACxB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}