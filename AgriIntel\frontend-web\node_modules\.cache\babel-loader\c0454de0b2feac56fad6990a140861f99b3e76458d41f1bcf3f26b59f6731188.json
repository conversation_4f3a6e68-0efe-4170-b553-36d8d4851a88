{"ast": null, "code": "'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\nvar callBindBasic = require('call-bind-apply-helpers');\n\n/** @type {(thisArg: string, searchString: string, position?: number) => number} */\nvar $indexOf = callBindBasic([GetIntrinsic('%String.prototype.indexOf%')]);\n\n/** @type {import('.')} */\nmodule.exports = function callBoundIntrinsic(name, allowMissing) {\n  // eslint-disable-next-line no-extra-parens\n  var intrinsic = /** @type {Parameters<typeof callBindBasic>[0][0]} */GetIntrinsic(name, !!allowMissing);\n  if (typeof intrinsic === 'function' && $indexOf(name, '.prototype.') > -1) {\n    return callBindBasic([intrinsic]);\n  }\n  return intrinsic;\n};", "map": {"version": 3, "names": ["GetIntrinsic", "require", "callBindBasic", "$indexOf", "module", "exports", "callBoundIntrinsic", "name", "allowMissing", "intrinsic"], "sources": ["C:/Users/<USER>/node_modules/call-bound/index.js"], "sourcesContent": ["'use strict';\n\nvar GetIntrinsic = require('get-intrinsic');\n\nvar callBindBasic = require('call-bind-apply-helpers');\n\n/** @type {(thisArg: string, searchString: string, position?: number) => number} */\nvar $indexOf = callBindBasic([GetIntrinsic('%String.prototype.indexOf%')]);\n\n/** @type {import('.')} */\nmodule.exports = function callBoundIntrinsic(name, allowMissing) {\n\t// eslint-disable-next-line no-extra-parens\n\tvar intrinsic = /** @type {Parameters<typeof callBindBasic>[0][0]} */ (GetIntrinsic(name, !!allowMissing));\n\tif (typeof intrinsic === 'function' && $indexOf(name, '.prototype.') > -1) {\n\t\treturn callBindBasic([intrinsic]);\n\t}\n\treturn intrinsic;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,IAAIA,YAAY,GAAGC,OAAO,CAAC,eAAe,CAAC;AAE3C,IAAIC,aAAa,GAAGD,OAAO,CAAC,yBAAyB,CAAC;;AAEtD;AACA,IAAIE,QAAQ,GAAGD,aAAa,CAAC,CAACF,YAAY,CAAC,4BAA4B,CAAC,CAAC,CAAC;;AAE1E;AACAI,MAAM,CAACC,OAAO,GAAG,SAASC,kBAAkBA,CAACC,IAAI,EAAEC,YAAY,EAAE;EAChE;EACA,IAAIC,SAAS,GAAG,qDAAuDT,YAAY,CAACO,IAAI,EAAE,CAAC,CAACC,YAAY,CAAE;EAC1G,IAAI,OAAOC,SAAS,KAAK,UAAU,IAAIN,QAAQ,CAACI,IAAI,EAAE,aAAa,CAAC,GAAG,CAAC,CAAC,EAAE;IAC1E,OAAOL,aAAa,CAAC,CAACO,SAAS,CAAC,CAAC;EAClC;EACA,OAAOA,SAAS;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}