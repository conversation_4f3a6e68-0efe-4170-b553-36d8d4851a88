{"ast": null, "code": "import getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n  var _options = options,\n    _options$placement = _options.placement,\n    placement = _options$placement === void 0 ? state.placement : _options$placement,\n    _options$strategy = _options.strategy,\n    strategy = _options$strategy === void 0 ? state.strategy : _options$strategy,\n    _options$boundary = _options.boundary,\n    boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n    _options$rootBoundary = _options.rootBoundary,\n    rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n    _options$elementConte = _options.elementContext,\n    elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n    _options$altBoundary = _options.altBoundary,\n    altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n    _options$padding = _options.padding,\n    padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);\n  var referenceClientRect = getBoundingClientRect(state.elements.reference);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n  return overflowOffsets;\n}", "map": {"version": 3, "names": ["getClippingRect", "getDocumentElement", "getBoundingClientRect", "computeOffsets", "rectToClientRect", "clippingParents", "reference", "popper", "bottom", "top", "right", "basePlacements", "viewport", "isElement", "mergePaddingObject", "expandToHashMap", "detectOverflow", "state", "options", "_options", "_options$placement", "placement", "_options$strategy", "strategy", "_options$boundary", "boundary", "_options$rootBoundary", "rootBoundary", "_options$elementConte", "elementContext", "_options$altBoundary", "altBoundary", "_options$padding", "padding", "paddingObject", "altContext", "popperRect", "rects", "element", "elements", "clippingClientRect", "contextElement", "referenceClientRect", "popperOffsets", "popperClientRect", "Object", "assign", "elementClientRect", "overflowOffsets", "left", "offsetData", "modifiersData", "offset", "keys", "for<PERSON>ach", "key", "multiply", "indexOf", "axis"], "sources": ["C:/Users/<USER>/node_modules/@popperjs/core/lib/utils/detectOverflow.js"], "sourcesContent": ["import getClippingRect from \"../dom-utils/getClippingRect.js\";\nimport getDocumentElement from \"../dom-utils/getDocumentElement.js\";\nimport getBoundingClientRect from \"../dom-utils/getBoundingClientRect.js\";\nimport computeOffsets from \"./computeOffsets.js\";\nimport rectToClientRect from \"./rectToClientRect.js\";\nimport { clippingParents, reference, popper, bottom, top, right, basePlacements, viewport } from \"../enums.js\";\nimport { isElement } from \"../dom-utils/instanceOf.js\";\nimport mergePaddingObject from \"./mergePaddingObject.js\";\nimport expandToHashMap from \"./expandToHashMap.js\"; // eslint-disable-next-line import/no-unused-modules\n\nexport default function detectOverflow(state, options) {\n  if (options === void 0) {\n    options = {};\n  }\n\n  var _options = options,\n      _options$placement = _options.placement,\n      placement = _options$placement === void 0 ? state.placement : _options$placement,\n      _options$strategy = _options.strategy,\n      strategy = _options$strategy === void 0 ? state.strategy : _options$strategy,\n      _options$boundary = _options.boundary,\n      boundary = _options$boundary === void 0 ? clippingParents : _options$boundary,\n      _options$rootBoundary = _options.rootBoundary,\n      rootBoundary = _options$rootBoundary === void 0 ? viewport : _options$rootBoundary,\n      _options$elementConte = _options.elementContext,\n      elementContext = _options$elementConte === void 0 ? popper : _options$elementConte,\n      _options$altBoundary = _options.altBoundary,\n      altBoundary = _options$altBoundary === void 0 ? false : _options$altBoundary,\n      _options$padding = _options.padding,\n      padding = _options$padding === void 0 ? 0 : _options$padding;\n  var paddingObject = mergePaddingObject(typeof padding !== 'number' ? padding : expandToHashMap(padding, basePlacements));\n  var altContext = elementContext === popper ? reference : popper;\n  var popperRect = state.rects.popper;\n  var element = state.elements[altBoundary ? altContext : elementContext];\n  var clippingClientRect = getClippingRect(isElement(element) ? element : element.contextElement || getDocumentElement(state.elements.popper), boundary, rootBoundary, strategy);\n  var referenceClientRect = getBoundingClientRect(state.elements.reference);\n  var popperOffsets = computeOffsets({\n    reference: referenceClientRect,\n    element: popperRect,\n    strategy: 'absolute',\n    placement: placement\n  });\n  var popperClientRect = rectToClientRect(Object.assign({}, popperRect, popperOffsets));\n  var elementClientRect = elementContext === popper ? popperClientRect : referenceClientRect; // positive = overflowing the clipping rect\n  // 0 or negative = within the clipping rect\n\n  var overflowOffsets = {\n    top: clippingClientRect.top - elementClientRect.top + paddingObject.top,\n    bottom: elementClientRect.bottom - clippingClientRect.bottom + paddingObject.bottom,\n    left: clippingClientRect.left - elementClientRect.left + paddingObject.left,\n    right: elementClientRect.right - clippingClientRect.right + paddingObject.right\n  };\n  var offsetData = state.modifiersData.offset; // Offsets can be applied only to the popper element\n\n  if (elementContext === popper && offsetData) {\n    var offset = offsetData[placement];\n    Object.keys(overflowOffsets).forEach(function (key) {\n      var multiply = [right, bottom].indexOf(key) >= 0 ? 1 : -1;\n      var axis = [top, bottom].indexOf(key) >= 0 ? 'y' : 'x';\n      overflowOffsets[key] += offset[axis] * multiply;\n    });\n  }\n\n  return overflowOffsets;\n}"], "mappings": "AAAA,OAAOA,eAAe,MAAM,iCAAiC;AAC7D,OAAOC,kBAAkB,MAAM,oCAAoC;AACnE,OAAOC,qBAAqB,MAAM,uCAAuC;AACzE,OAAOC,cAAc,MAAM,qBAAqB;AAChD,OAAOC,gBAAgB,MAAM,uBAAuB;AACpD,SAASC,eAAe,EAAEC,SAAS,EAAEC,MAAM,EAAEC,MAAM,EAAEC,GAAG,EAAEC,KAAK,EAAEC,cAAc,EAAEC,QAAQ,QAAQ,aAAa;AAC9G,SAASC,SAAS,QAAQ,4BAA4B;AACtD,OAAOC,kBAAkB,MAAM,yBAAyB;AACxD,OAAOC,eAAe,MAAM,sBAAsB,CAAC,CAAC;;AAEpD,eAAe,SAASC,cAAcA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACrD,IAAIA,OAAO,KAAK,KAAK,CAAC,EAAE;IACtBA,OAAO,GAAG,CAAC,CAAC;EACd;EAEA,IAAIC,QAAQ,GAAGD,OAAO;IAClBE,kBAAkB,GAAGD,QAAQ,CAACE,SAAS;IACvCA,SAAS,GAAGD,kBAAkB,KAAK,KAAK,CAAC,GAAGH,KAAK,CAACI,SAAS,GAAGD,kBAAkB;IAChFE,iBAAiB,GAAGH,QAAQ,CAACI,QAAQ;IACrCA,QAAQ,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAGL,KAAK,CAACM,QAAQ,GAAGD,iBAAiB;IAC5EE,iBAAiB,GAAGL,QAAQ,CAACM,QAAQ;IACrCA,QAAQ,GAAGD,iBAAiB,KAAK,KAAK,CAAC,GAAGnB,eAAe,GAAGmB,iBAAiB;IAC7EE,qBAAqB,GAAGP,QAAQ,CAACQ,YAAY;IAC7CA,YAAY,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGd,QAAQ,GAAGc,qBAAqB;IAClFE,qBAAqB,GAAGT,QAAQ,CAACU,cAAc;IAC/CA,cAAc,GAAGD,qBAAqB,KAAK,KAAK,CAAC,GAAGrB,MAAM,GAAGqB,qBAAqB;IAClFE,oBAAoB,GAAGX,QAAQ,CAACY,WAAW;IAC3CA,WAAW,GAAGD,oBAAoB,KAAK,KAAK,CAAC,GAAG,KAAK,GAAGA,oBAAoB;IAC5EE,gBAAgB,GAAGb,QAAQ,CAACc,OAAO;IACnCA,OAAO,GAAGD,gBAAgB,KAAK,KAAK,CAAC,GAAG,CAAC,GAAGA,gBAAgB;EAChE,IAAIE,aAAa,GAAGpB,kBAAkB,CAAC,OAAOmB,OAAO,KAAK,QAAQ,GAAGA,OAAO,GAAGlB,eAAe,CAACkB,OAAO,EAAEtB,cAAc,CAAC,CAAC;EACxH,IAAIwB,UAAU,GAAGN,cAAc,KAAKtB,MAAM,GAAGD,SAAS,GAAGC,MAAM;EAC/D,IAAI6B,UAAU,GAAGnB,KAAK,CAACoB,KAAK,CAAC9B,MAAM;EACnC,IAAI+B,OAAO,GAAGrB,KAAK,CAACsB,QAAQ,CAACR,WAAW,GAAGI,UAAU,GAAGN,cAAc,CAAC;EACvE,IAAIW,kBAAkB,GAAGxC,eAAe,CAACa,SAAS,CAACyB,OAAO,CAAC,GAAGA,OAAO,GAAGA,OAAO,CAACG,cAAc,IAAIxC,kBAAkB,CAACgB,KAAK,CAACsB,QAAQ,CAAChC,MAAM,CAAC,EAAEkB,QAAQ,EAAEE,YAAY,EAAEJ,QAAQ,CAAC;EAC9K,IAAImB,mBAAmB,GAAGxC,qBAAqB,CAACe,KAAK,CAACsB,QAAQ,CAACjC,SAAS,CAAC;EACzE,IAAIqC,aAAa,GAAGxC,cAAc,CAAC;IACjCG,SAAS,EAAEoC,mBAAmB;IAC9BJ,OAAO,EAAEF,UAAU;IACnBb,QAAQ,EAAE,UAAU;IACpBF,SAAS,EAAEA;EACb,CAAC,CAAC;EACF,IAAIuB,gBAAgB,GAAGxC,gBAAgB,CAACyC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEV,UAAU,EAAEO,aAAa,CAAC,CAAC;EACrF,IAAII,iBAAiB,GAAGlB,cAAc,KAAKtB,MAAM,GAAGqC,gBAAgB,GAAGF,mBAAmB,CAAC,CAAC;EAC5F;;EAEA,IAAIM,eAAe,GAAG;IACpBvC,GAAG,EAAE+B,kBAAkB,CAAC/B,GAAG,GAAGsC,iBAAiB,CAACtC,GAAG,GAAGyB,aAAa,CAACzB,GAAG;IACvED,MAAM,EAAEuC,iBAAiB,CAACvC,MAAM,GAAGgC,kBAAkB,CAAChC,MAAM,GAAG0B,aAAa,CAAC1B,MAAM;IACnFyC,IAAI,EAAET,kBAAkB,CAACS,IAAI,GAAGF,iBAAiB,CAACE,IAAI,GAAGf,aAAa,CAACe,IAAI;IAC3EvC,KAAK,EAAEqC,iBAAiB,CAACrC,KAAK,GAAG8B,kBAAkB,CAAC9B,KAAK,GAAGwB,aAAa,CAACxB;EAC5E,CAAC;EACD,IAAIwC,UAAU,GAAGjC,KAAK,CAACkC,aAAa,CAACC,MAAM,CAAC,CAAC;;EAE7C,IAAIvB,cAAc,KAAKtB,MAAM,IAAI2C,UAAU,EAAE;IAC3C,IAAIE,MAAM,GAAGF,UAAU,CAAC7B,SAAS,CAAC;IAClCwB,MAAM,CAACQ,IAAI,CAACL,eAAe,CAAC,CAACM,OAAO,CAAC,UAAUC,GAAG,EAAE;MAClD,IAAIC,QAAQ,GAAG,CAAC9C,KAAK,EAAEF,MAAM,CAAC,CAACiD,OAAO,CAACF,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;MACzD,IAAIG,IAAI,GAAG,CAACjD,GAAG,EAAED,MAAM,CAAC,CAACiD,OAAO,CAACF,GAAG,CAAC,IAAI,CAAC,GAAG,GAAG,GAAG,GAAG;MACtDP,eAAe,CAACO,GAAG,CAAC,IAAIH,MAAM,CAACM,IAAI,CAAC,GAAGF,QAAQ;IACjD,CAAC,CAAC;EACJ;EAEA,OAAOR,eAAe;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}