{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.UpdateSearchIndexOperation = void 0;\nconst operation_1 = require(\"../operation\");\n/** @internal */\nclass UpdateSearchIndexOperation extends operation_1.AbstractOperation {\n  constructor(collection, name, definition) {\n    super();\n    this.collection = collection;\n    this.name = name;\n    this.definition = definition;\n  }\n  get commandName() {\n    return 'updateSearchIndex';\n  }\n  async execute(server, session, timeoutContext) {\n    const namespace = this.collection.fullNamespace;\n    const command = {\n      updateSearchIndex: namespace.collection,\n      name: this.name,\n      definition: this.definition\n    };\n    await server.command(namespace, command, {\n      session,\n      timeoutContext\n    });\n    return;\n  }\n}\nexports.UpdateSearchIndexOperation = UpdateSearchIndexOperation;", "map": {"version": 3, "names": ["operation_1", "require", "UpdateSearchIndexOperation", "AbstractOperation", "constructor", "collection", "name", "definition", "commandName", "execute", "server", "session", "timeoutContext", "namespace", "fullNamespace", "command", "updateSearchIndex", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\search_indexes\\update.ts"], "sourcesContent": ["import type { Document } from '../../bson';\nimport type { Collection } from '../../collection';\nimport type { Server } from '../../sdam/server';\nimport type { ClientSession } from '../../sessions';\nimport { type TimeoutContext } from '../../timeout';\nimport { AbstractOperation } from '../operation';\n\n/** @internal */\nexport class UpdateSearchIndexOperation extends AbstractOperation<void> {\n  constructor(\n    private readonly collection: Collection,\n    private readonly name: string,\n    private readonly definition: Document\n  ) {\n    super();\n  }\n\n  override get commandName() {\n    return 'updateSearchIndex' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<void> {\n    const namespace = this.collection.fullNamespace;\n    const command = {\n      updateSearchIndex: namespace.collection,\n      name: this.name,\n      definition: this.definition\n    };\n\n    await server.command(namespace, command, { session, timeoutContext });\n    return;\n  }\n}\n"], "mappings": ";;;;;;AAKA,MAAAA,WAAA,GAAAC,OAAA;AAEA;AACA,MAAaC,0BAA2B,SAAQF,WAAA,CAAAG,iBAAuB;EACrEC,YACmBC,UAAsB,EACtBC,IAAY,EACZC,UAAoB;IAErC,KAAK,EAAE;IAJU,KAAAF,UAAU,GAAVA,UAAU;IACV,KAAAC,IAAI,GAAJA,IAAI;IACJ,KAAAC,UAAU,GAAVA,UAAU;EAG7B;EAEA,IAAaC,WAAWA,CAAA;IACtB,OAAO,mBAA4B;EACrC;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMC,SAAS,GAAG,IAAI,CAACR,UAAU,CAACS,aAAa;IAC/C,MAAMC,OAAO,GAAG;MACdC,iBAAiB,EAAEH,SAAS,CAACR,UAAU;MACvCC,IAAI,EAAE,IAAI,CAACA,IAAI;MACfC,UAAU,EAAE,IAAI,CAACA;KAClB;IAED,MAAMG,MAAM,CAACK,OAAO,CAACF,SAAS,EAAEE,OAAO,EAAE;MAAEJ,OAAO;MAAEC;IAAc,CAAE,CAAC;IACrE;EACF;;AA3BFK,OAAA,CAAAf,0BAAA,GAAAA,0BAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}