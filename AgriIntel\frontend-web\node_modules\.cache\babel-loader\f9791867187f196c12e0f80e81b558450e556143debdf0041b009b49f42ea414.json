{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.isEmptyCredentials = isEmptyCredentials;\nexports.refreshKMSCredentials = refreshKMSCredentials;\nconst aws_1 = require(\"./aws\");\nconst azure_1 = require(\"./azure\");\nconst gcp_1 = require(\"./gcp\");\n/**\n * Auto credential fetching should only occur when the provider is defined on the kmsProviders map\n * and the settings are an empty object.\n *\n * This is distinct from a nullish provider key.\n *\n * @internal - exposed for testing purposes only\n */\nfunction isEmptyCredentials(providerName, kmsProviders) {\n  const provider = kmsProviders[providerName];\n  if (provider == null) {\n    return false;\n  }\n  return typeof provider === 'object' && Object.keys(provider).length === 0;\n}\n/**\n * Load cloud provider credentials for the user provided KMS providers.\n * Credentials will only attempt to get loaded if they do not exist\n * and no existing credentials will get overwritten.\n *\n * @internal\n */\nasync function refreshKMSCredentials(kmsProviders, credentialProviders) {\n  let finalKMSProviders = kmsProviders;\n  if (isEmptyCredentials('aws', kmsProviders)) {\n    finalKMSProviders = await (0, aws_1.loadAWSCredentials)(finalKMSProviders, credentialProviders?.aws);\n  }\n  if (isEmptyCredentials('gcp', kmsProviders)) {\n    finalKMSProviders = await (0, gcp_1.loadGCPCredentials)(finalKMSProviders);\n  }\n  if (isEmptyCredentials('azure', kmsProviders)) {\n    finalKMSProviders = await (0, azure_1.loadAzureCredentials)(finalKMSProviders);\n  }\n  return finalKMSProviders;\n}", "map": {"version": 3, "names": ["exports", "isEmptyCredentials", "refreshKMSCredentials", "aws_1", "require", "azure_1", "gcp_1", "providerName", "kmsProviders", "provider", "Object", "keys", "length", "credentialProviders", "finalKMSProviders", "loadAWSCredentials", "aws", "loadGCPCredentials", "loadAzureCredentials"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\client-side-encryption\\providers\\index.ts"], "sourcesContent": ["import type { Binary } from '../../bson';\nimport { type AWSCredentialProvider } from '../../cmap/auth/aws_temporary_credentials';\nimport { loadAWSCredentials } from './aws';\nimport { loadAzureCredentials } from './azure';\nimport { loadGCPCredentials } from './gcp';\n\n/**\n * @public\n *\n * A data key provider.  Allowed values:\n *\n * - aws, gcp, local, kmip or azure\n * - (`mongodb-client-encryption>=6.0.1` only) a named key, in the form of:\n *    `aws:<name>`, `gcp:<name>`, `local:<name>`, `kmip:<name>`, `azure:<name>`\n *  where `name` is an alphanumeric string, underscores allowed.\n */\nexport type ClientEncryptionDataKeyProvider = keyof KMSProviders;\n\n/** @public */\nexport interface AWSKMSProviderConfiguration {\n  /**\n   * The access key used for the AWS KMS provider\n   */\n  accessKeyId: string;\n\n  /**\n   * The secret access key used for the AWS KMS provider\n   */\n  secretAccessKey: string;\n\n  /**\n   * An optional AWS session token that will be used as the\n   * X-Amz-Security-Token header for AWS requests.\n   */\n  sessionToken?: string;\n}\n\n/** @public */\nexport interface LocalKMSProviderConfiguration {\n  /**\n   * The master key used to encrypt/decrypt data keys.\n   * A 96-byte long Buffer or base64 encoded string.\n   */\n  key: Binary | Uint8Array | string;\n}\n\n/** @public */\nexport interface KMIPKMSProviderConfiguration {\n  /**\n   * The output endpoint string.\n   * The endpoint consists of a hostname and port separated by a colon.\n   * E.g. \"example.com:123\". A port is always present.\n   */\n  endpoint?: string;\n}\n\n/** @public */\nexport type AzureKMSProviderConfiguration =\n  | {\n      /**\n       * The tenant ID identifies the organization for the account\n       */\n      tenantId: string;\n\n      /**\n       * The client ID to authenticate a registered application\n       */\n      clientId: string;\n\n      /**\n       * The client secret to authenticate a registered application\n       */\n      clientSecret: string;\n\n      /**\n       * If present, a host with optional port. E.g. \"example.com\" or \"example.com:443\".\n       * This is optional, and only needed if customer is using a non-commercial Azure instance\n       * (e.g. a government or China account, which use different URLs).\n       * Defaults to \"login.microsoftonline.com\"\n       */\n      identityPlatformEndpoint?: string | undefined;\n    }\n  | {\n      /**\n       * If present, an access token to authenticate with Azure.\n       */\n      accessToken: string;\n    };\n\n/** @public */\nexport type GCPKMSProviderConfiguration =\n  | {\n      /**\n       * The service account email to authenticate\n       */\n      email: string;\n\n      /**\n       * A PKCS#8 encrypted key. This can either be a base64 string or a binary representation\n       */\n      privateKey: string | Buffer;\n\n      /**\n       * If present, a host with optional port. E.g. \"example.com\" or \"example.com:443\".\n       * Defaults to \"oauth2.googleapis.com\"\n       */\n      endpoint?: string | undefined;\n    }\n  | {\n      /**\n       * If present, an access token to authenticate with GCP.\n       */\n      accessToken: string;\n    };\n\n/**\n * @public\n * Configuration options for custom credential providers for KMS requests.\n */\nexport interface CredentialProviders {\n  /* A custom AWS credential provider */\n  aws?: AWSCredentialProvider;\n}\n\n/**\n * @public\n * Configuration options that are used by specific KMS providers during key generation, encryption, and decryption.\n *\n * Named KMS providers _are not supported_ for automatic KMS credential fetching.\n */\nexport interface KMSProviders {\n  /**\n   * Configuration options for using 'aws' as your KMS provider\n   */\n  aws?: AWSKMSProviderConfiguration | Record<string, never>;\n  [key: `aws:${string}`]: AWSKMSProviderConfiguration;\n\n  /**\n   * Configuration options for using 'local' as your KMS provider\n   */\n  local?: LocalKMSProviderConfiguration;\n  [key: `local:${string}`]: LocalKMSProviderConfiguration;\n\n  /**\n   * Configuration options for using 'kmip' as your KMS provider\n   */\n  kmip?: KMIPKMSProviderConfiguration;\n  [key: `kmip:${string}`]: KMIPKMSProviderConfiguration;\n\n  /**\n   * Configuration options for using 'azure' as your KMS provider\n   */\n  azure?: AzureKMSProviderConfiguration | Record<string, never>;\n  [key: `azure:${string}`]: AzureKMSProviderConfiguration;\n\n  /**\n   * Configuration options for using 'gcp' as your KMS provider\n   */\n  gcp?: GCPKMSProviderConfiguration | Record<string, never>;\n  [key: `gcp:${string}`]: GCPKMSProviderConfiguration;\n}\n\n/**\n * Auto credential fetching should only occur when the provider is defined on the kmsProviders map\n * and the settings are an empty object.\n *\n * This is distinct from a nullish provider key.\n *\n * @internal - exposed for testing purposes only\n */\nexport function isEmptyCredentials(\n  providerName: ClientEncryptionDataKeyProvider,\n  kmsProviders: KMSProviders\n): boolean {\n  const provider = kmsProviders[providerName];\n  if (provider == null) {\n    return false;\n  }\n  return typeof provider === 'object' && Object.keys(provider).length === 0;\n}\n\n/**\n * Load cloud provider credentials for the user provided KMS providers.\n * Credentials will only attempt to get loaded if they do not exist\n * and no existing credentials will get overwritten.\n *\n * @internal\n */\nexport async function refreshKMSCredentials(\n  kmsProviders: KMSProviders,\n  credentialProviders?: CredentialProviders\n): Promise<KMSProviders> {\n  let finalKMSProviders = kmsProviders;\n\n  if (isEmptyCredentials('aws', kmsProviders)) {\n    finalKMSProviders = await loadAWSCredentials(finalKMSProviders, credentialProviders?.aws);\n  }\n\n  if (isEmptyCredentials('gcp', kmsProviders)) {\n    finalKMSProviders = await loadGCPCredentials(finalKMSProviders);\n  }\n\n  if (isEmptyCredentials('azure', kmsProviders)) {\n    finalKMSProviders = await loadAzureCredentials(finalKMSProviders);\n  }\n  return finalKMSProviders;\n}\n"], "mappings": ";;;;;AA0KAA,OAAA,CAAAC,kBAAA,GAAAA,kBAAA;AAkBAD,OAAA,CAAAE,qBAAA,GAAAA,qBAAA;AA1LA,MAAAC,KAAA,GAAAC,OAAA;AACA,MAAAC,OAAA,GAAAD,OAAA;AACA,MAAAE,KAAA,GAAAF,OAAA;AA8JA;;;;;;;;AAQA,SAAgBH,kBAAkBA,CAChCM,YAA6C,EAC7CC,YAA0B;EAE1B,MAAMC,QAAQ,GAAGD,YAAY,CAACD,YAAY,CAAC;EAC3C,IAAIE,QAAQ,IAAI,IAAI,EAAE;IACpB,OAAO,KAAK;EACd;EACA,OAAO,OAAOA,QAAQ,KAAK,QAAQ,IAAIC,MAAM,CAACC,IAAI,CAACF,QAAQ,CAAC,CAACG,MAAM,KAAK,CAAC;AAC3E;AAEA;;;;;;;AAOO,eAAeV,qBAAqBA,CACzCM,YAA0B,EAC1BK,mBAAyC;EAEzC,IAAIC,iBAAiB,GAAGN,YAAY;EAEpC,IAAIP,kBAAkB,CAAC,KAAK,EAAEO,YAAY,CAAC,EAAE;IAC3CM,iBAAiB,GAAG,MAAM,IAAAX,KAAA,CAAAY,kBAAkB,EAACD,iBAAiB,EAAED,mBAAmB,EAAEG,GAAG,CAAC;EAC3F;EAEA,IAAIf,kBAAkB,CAAC,KAAK,EAAEO,YAAY,CAAC,EAAE;IAC3CM,iBAAiB,GAAG,MAAM,IAAAR,KAAA,CAAAW,kBAAkB,EAACH,iBAAiB,CAAC;EACjE;EAEA,IAAIb,kBAAkB,CAAC,OAAO,EAAEO,YAAY,CAAC,EAAE;IAC7CM,iBAAiB,GAAG,MAAM,IAAAT,OAAA,CAAAa,oBAAoB,EAACJ,iBAAiB,CAAC;EACnE;EACA,OAAOA,iBAAiB;AAC1B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}