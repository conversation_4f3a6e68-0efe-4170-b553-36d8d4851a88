import axios from 'axios';
import { API_BASE_URL } from '../config/api';
import { Animal } from '../types/animal';
import { FeedingRecord, FeedInventory } from '../types/feeding';
import { HealthRecord } from '../types/health';
import { BreedingRecord } from '../types/breeding';
import { FinancialRecord } from '../types/financial';
import { formatDate } from '../utils/dateUtils';
// MongoDB collection import removed - using API calls instead
import { showSnackbar } from '../utils/snackbar';

// Define report types
export type ReportType = 'analysis' | 'performance' | 'health' | 'market' | 'custom' | 'financial' | 'breeding' | 'feed';
export type ReportFormat = 'pdf' | 'excel' | 'csv' | 'html';
export type TimePeriod = 'week' | 'month' | 'quarter' | 'year' | 'custom';

export interface ReportParams {
  type: ReportType;
  format: ReportFormat;
  timePeriod: TimePeriod;
  startDate?: string;
  endDate?: string;
  filters?: Record<string, any>;
  customFields?: string[];
}

/**
 * Generate and download a report
 * @param params Report parameters
 * @returns Promise with the download URL
 */
export const generateReport = async (params: ReportParams): Promise<string> => {
  try {
    // If HTML format is requested, generate it directly in the browser
    if (params.format === 'html') {
      const htmlContent = await generateHtmlReport(params);
      const filename = `${params.type}_report.html`;

      // Download the HTML file
      downloadFile('', filename, htmlContent);

      return 'Generated HTML report';
    }

    // For other formats, in a real implementation, this would call the backend API
    // For now, we'll simulate a successful response

    // Simulate API call delay
    await new Promise(resolve => setTimeout(resolve, 1500));

    // Mock response with download URL
    const mockResponse = {
      success: true,
      downloadUrl: `${API_BASE_URL}/reports/download/${params.type}_${Date.now()}.${params.format === 'excel' ? 'xlsx' : params.format}`
    };

    // Trigger file download
    downloadFile(mockResponse.downloadUrl, `${params.type}_report.${params.format === 'excel' ? 'xlsx' : params.format}`);

    return mockResponse.downloadUrl;
  } catch (error) {
    console.error('Error generating report:', error);
    throw error;
  }
};

/**
 * Helper function to trigger file download
 * @param url URL of the file to download
 * @param filename Name to save the file as
 */
export const downloadFile = (url: string, filename: string, content?: string): void => {
  // If content is provided, use it directly
  if (content) {
    const blob = new Blob([content], { type: getContentType(filename) });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
    return;
  }

  // Otherwise, in a real implementation, this would download the file from the URL
  // For now, we'll create a mock file with some content

  // Create a blob with mock content
  const mockContent = `This is a mock ${filename} file generated at ${new Date().toLocaleString()}`;
  const blob = new Blob([mockContent], { type: getContentType(filename) });

  // Create a download link and trigger the download
  const link = document.createElement('a');
  link.href = URL.createObjectURL(blob);
  link.download = filename;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(link.href);
};

/**
 * Get the content type based on file extension
 * @param filename Filename with extension
 * @returns Content type string
 */
const getContentType = (filename: string): string => {
  if (filename.endsWith('.pdf')) {
    return 'application/pdf';
  } else if (filename.endsWith('.xlsx')) {
    return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
  } else if (filename.endsWith('.csv')) {
    return 'text/csv';
  } else if (filename.endsWith('.html')) {
    return 'text/html';
  }
  return 'text/plain';
};

/**
 * Get saved reports for a specific type
 * @param type Report type
 * @returns Promise with the list of saved reports
 */
/**
 * Generate an HTML report based on the provided parameters
 * @param params Report parameters
 * @returns Promise with the HTML content
 */
export const generateHtmlReport = async (params: ReportParams): Promise<string> => {
  try {
    // Get the current date and time for the report
    const reportDate = new Date().toLocaleDateString('en-ZA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });

    // Get the report title based on type
    const getReportTitle = () => {
      switch (params.type) {
        case 'analysis': return 'Analysis Report';
        case 'performance': return 'Performance Report';
        case 'health': return 'Health Report';
        case 'market': return 'Market Report';
        case 'financial': return 'Financial Report';
        case 'custom': return 'Custom Report';
        default: return 'MayCaiphus Livestock Report';
      }
    };

    // Get date range based on time period
    const { startDate, endDate } = getDateRange(params.timePeriod, params.startDate, params.endDate);

    // Fetch data from MongoDB collections
    let animals: any[] = [];
    let feedingRecords: any[] = [];
    let feedInventory: any[] = [];
    let healthRecords: any[] = [];
    let breedingRecords: any[] = [];
    let financialRecords: any[] = [];

    try {
      // Try to fetch data from MongoDB collections
      const animalsCollection = await getCollection('animals');
      const feedingRecordsCollection = await getCollection('feeding_records');
      const feedInventoryCollection = await getCollection('feed_inventory');
      const healthRecordsCollection = await getCollection('health_records');
      const breedingRecordsCollection = await getCollection('breeding_records');
      const financialRecordsCollection = await getCollection('financial_records');

      // Safely fetch data with error handling
      try {
        const animalsCursor = animalsCollection.find({});
        animals = animalsCursor && typeof animalsCursor.toArray === 'function'
          ? await animalsCursor.toArray()
          : [];
      } catch (error) {
        console.error('Error fetching animals:', error);
        animals = [];
      }

      try {
        const feedingRecordsCursor = feedingRecordsCollection.find({
          date: { $gte: new Date(startDate), $lte: new Date(endDate) }
        });
        feedingRecords = feedingRecordsCursor && typeof feedingRecordsCursor.toArray === 'function'
          ? await feedingRecordsCursor.toArray()
          : [];
      } catch (error) {
        console.error('Error fetching feeding records:', error);
        feedingRecords = [];
      }

      try {
        const feedInventoryCursor = feedInventoryCollection.find({});
        feedInventory = feedInventoryCursor && typeof feedInventoryCursor.toArray === 'function'
          ? await feedInventoryCursor.toArray()
          : [];
      } catch (error) {
        console.error('Error fetching feed inventory:', error);
        feedInventory = [];
      }

      try {
        const healthRecordsCursor = healthRecordsCollection.find({
          date: { $gte: new Date(startDate), $lte: new Date(endDate) }
        });
        healthRecords = healthRecordsCursor && typeof healthRecordsCursor.toArray === 'function'
          ? await healthRecordsCursor.toArray()
          : [];
      } catch (error) {
        console.error('Error fetching health records:', error);
        healthRecords = [];
      }

      try {
        const breedingRecordsCursor = breedingRecordsCollection.find({
          date: { $gte: new Date(startDate), $lte: new Date(endDate) }
        });
        breedingRecords = breedingRecordsCursor && typeof breedingRecordsCursor.toArray === 'function'
          ? await breedingRecordsCursor.toArray()
          : [];
      } catch (error) {
        console.error('Error fetching breeding records:', error);
        breedingRecords = [];
      }

      try {
        const financialRecordsCursor = financialRecordsCollection.find({
          date: { $gte: new Date(startDate), $lte: new Date(endDate) }
        });
        financialRecords = financialRecordsCursor && typeof financialRecordsCursor.toArray === 'function'
          ? await financialRecordsCursor.toArray()
          : [];
      } catch (error) {
        console.error('Error fetching financial records:', error);
        financialRecords = [];
      }
    } catch (error) {
      console.error('Error setting up MongoDB collections:', error);
      // If there's an error with MongoDB, use mock data
      console.log('Using mock data for report generation');

      // Import mock data
      const { mockAnimals } = await import('../mocks/animalData');
      const { mockFeedingRecords } = await import('../mocks/feedingData');
      const { mockHealthRecords } = await import('../mocks/healthData');
      const { mockBreedingRecords } = await import('../mocks/breedingData');
      const { mockFinancialRecords } = await import('../mocks/financialData');

      animals = mockAnimals || [];
      feedingRecords = mockFeedingRecords || [];
      feedInventory = [];
      healthRecords = mockHealthRecords || [];
      breedingRecords = mockBreedingRecords || [];
      financialRecords = mockFinancialRecords || [];
    }

    // Generate summary statistics
    const totalAnimals = animals.length;
    const speciesCounts: Record<string, number> = {};
    const statusCounts: Record<string, number> = {};
    const healthCounts: Record<string, number> = {};

    animals.forEach(animal => {
      // Count by species
      if (animal.species) {
        speciesCounts[animal.species] = (speciesCounts[animal.species] || 0) + 1;
      }

      // Count by status
      statusCounts[animal.status] = (statusCounts[animal.status] || 0) + 1;

      // Count by health status
      healthCounts[animal.healthStatus] = (healthCounts[animal.healthStatus] || 0) + 1;
    });

    // Calculate feed usage by type
    const feedUsageByType: Record<string, number> = {};
    feedingRecords.forEach(record => {
      const feedType = record.feedType;
      if (!feedUsageByType[feedType]) {
        feedUsageByType[feedType] = 0;
      }
      feedUsageByType[feedType] += record.quantity;
    });

    // Calculate total feed cost
    const totalFeedCost = feedingRecords.reduce((sum, record) => sum + (record.totalCost || 0), 0);

    // Calculate cost by animal group
    const costByAnimalGroup: Record<string, number> = {};
    feedingRecords.forEach(record => {
      const animalGroup = record.animalGroupId;
      if (!costByAnimalGroup[animalGroup]) {
        costByAnimalGroup[animalGroup] = 0;
      }
      costByAnimalGroup[animalGroup] += record.totalCost || 0;
    });

    // Calculate financial summary
    const totalRevenue = financialRecords
      .filter(record => record.type === 'income')
      .reduce((sum, record) => sum + record.amount, 0);

    const totalExpenses = financialRecords
      .filter(record => record.type === 'expense')
      .reduce((sum, record) => sum + record.amount, 0);

    const netProfit = totalRevenue - totalExpenses;
    const roi = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;

  // Start building HTML
  let html = `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>${getReportTitle()}</title>
      <style>
        /* Enhanced Report Styles */
        body {
          font-family: 'Roboto', Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          margin: 0;
          padding: 0;
          background-color: #f9f9f9;
        }

        .report-container {
          max-width: 1200px;
          margin: 30px auto;
          padding: 30px;
          background-color: #fff;
          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          border-radius: 8px;
        }

        .report-header {
          text-align: center;
          margin-bottom: 40px;
          padding-bottom: 30px;
          border-bottom: 2px solid #f0f0f0;
        }

        .report-title {
          font-size: 28px;
          font-weight: 700;
          margin: 0;
          color: #2c3e50;
          letter-spacing: -0.5px;
        }

        .report-subtitle {
          font-size: 20px;
          color: #3498db;
          margin: 10px 0;
          font-weight: 500;
        }

        .report-date {
          font-size: 14px;
          color: #7f8c8d;
          font-style: italic;
        }

        .report-section {
          margin-bottom: 40px;
        }

        .section-title {
          font-size: 22px;
          color: #2980b9;
          border-bottom: 3px solid #3498db;
          padding-bottom: 10px;
          margin-bottom: 20px;
          font-weight: 600;
        }

        .summary-stats {
          display: flex;
          flex-wrap: wrap;
          gap: 20px;
          margin-bottom: 30px;
        }

        .stat-card {
          background-color: #f8f9fa;
          border-radius: 8px;
          padding: 20px;
          min-width: 220px;
          box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);
          transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .stat-card:hover {
          transform: translateY(-5px);
          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .stat-title {
          font-size: 16px;
          color: #7f8c8d;
          margin-bottom: 8px;
          font-weight: 500;
        }

        .stat-value {
          font-size: 28px;
          font-weight: 700;
          color: #2c3e50;
        }

        table {
          width: 100%;
          border-collapse: collapse;
          margin-bottom: 30px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
          border-radius: 8px;
          overflow: hidden;
        }

        th, td {
          padding: 15px 18px;
          text-align: left;
          border-bottom: 1px solid #e0e0e0;
        }

        th {
          background-color: #f2f8fd;
          font-weight: 600;
          color: #2c3e50;
          font-size: 14px;
          text-transform: uppercase;
          letter-spacing: 0.5px;
        }

        tr:last-child td {
          border-bottom: none;
        }

        tr:hover {
          background-color: #f5f9ff;
        }

        .chart-container {
          margin-bottom: 40px;
          text-align: center;
          background-color: #f8f9fa;
          border-radius: 12px;
          padding: 25px;
          box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);
        }

        .chart-legend {
          display: flex;
          flex-wrap: wrap;
          justify-content: center;
          gap: 20px;
          margin-top: 25px;
          padding: 15px;
          background-color: rgba(255, 255, 255, 0.7);
          border-radius: 8px;
        }

        .legend-item {
          display: flex;
          align-items: center;
          font-size: 14px;
          padding: 5px 10px;
          background-color: #fff;
          border-radius: 20px;
          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
        }

        .legend-color {
          display: inline-block;
          width: 18px;
          height: 18px;
          margin-right: 8px;
          border-radius: 4px;
        }

        .legend-label {
          color: #333;
          font-weight: 500;
        }

        .chart-placeholder {
          background-color: #f8f9fa;
          border: 1px dashed #ccc;
          border-radius: 8px;
          padding: 30px;
          text-align: center;
          color: #7f8c8d;
        }

        .footer {
          margin-top: 60px;
          text-align: center;
          font-size: 13px;
          color: #95a5a6;
          padding-top: 30px;
          border-top: 1px solid #eee;
        }

        /* SVG Chart Styles */
        svg {
          max-width: 100%;
          height: auto;
          background-color: white;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        }

        svg text {
          font-family: 'Roboto', Arial, sans-serif;
        }

        svg .chart-title {
          font-size: 18px;
          font-weight: 600;
        }

        svg .axis-label {
          font-size: 13px;
        }

        svg .grid-line {
          stroke: #e0e0e0;
          stroke-dasharray: 3,3;
        }

        svg .data-point {
          stroke-width: 2;
        }

        svg .data-label {
          font-size: 12px;
          text-anchor: middle;
          font-weight: 500;
        }

        /* Financial Report Specific Styles */
        .financial-highlight {
          color: #2980b9;
          font-weight: 600;
        }

        .financial-positive {
          color: #27ae60;
          font-weight: 600;
        }

        .financial-negative {
          color: #e74c3c;
          font-weight: 600;
        }

        .financial-summary-box {
          background-color: #f8f9fa;
          border-left: 4px solid #3498db;
          padding: 20px;
          margin-bottom: 30px;
          border-radius: 0 8px 8px 0;
        }

        .financial-summary-title {
          font-size: 18px;
          font-weight: 600;
          color: #2c3e50;
          margin-bottom: 15px;
        }

        .financial-summary-content {
          display: flex;
          flex-wrap: wrap;
          gap: 20px;
        }

        .financial-summary-item {
          flex: 1;
          min-width: 200px;
        }

        .financial-summary-label {
          font-size: 14px;
          color: #7f8c8d;
          margin-bottom: 5px;
        }

        .financial-summary-value {
          font-size: 22px;
          font-weight: 700;
          color: #2c3e50;
        }

        .financial-trend-indicator {
          display: inline-flex;
          align-items: center;
          font-size: 14px;
          margin-left: 10px;
        }

        .financial-trend-up {
          color: #27ae60;
        }

        .financial-trend-down {
          color: #e74c3c;
        }

        /* Print styles */
        @media print {
          body {
            padding: 0;
            font-size: 12pt;
            background-color: white;
          }

          .report-container {
            box-shadow: none;
            padding: 0;
            max-width: 100%;
            margin: 0;
          }

          .chart-container {
            break-inside: avoid;
            page-break-inside: avoid;
            box-shadow: none;
            border: 1px solid #eee;
          }

          table {
            break-inside: auto;
            page-break-inside: auto;
            box-shadow: none;
            border: 1px solid #eee;
          }

          tr {
            break-inside: avoid;
            page-break-inside: avoid;
          }

          .no-print {
            display: none;
          }

          .stat-card {
            box-shadow: none;
            border: 1px solid #eee;
          }

          svg {
            box-shadow: none;
          }
        }
      </style>
    </head>
    <body>
      <div class="report-container">
        <div class="report-header">
          <h1 class="report-title">MayCaiphus Livestock Management System</h1>
          <h2 class="report-subtitle">${getReportTitle()}</h2>
          <p class="report-date">Generated on: ${reportDate}</p>
        </div>
  `;

  // Add summary section
  html += `
    <div class="report-section">
      <h3 class="section-title">Summary</h3>
      <div class="summary-stats">
        <div class="stat-card">
          <div class="stat-title">Total Animals</div>
          <div class="stat-value">${totalAnimals}</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">Active Animals</div>
          <div class="stat-value">${statusCounts['Active'] || 0}</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">Healthy Animals</div>
          <div class="stat-value">${healthCounts['healthy'] || 0}</div>
        </div>
      </div>
    </div>
  `;

  // Add financial summary section if financial report
  if (params.type === 'financial') {
    html += `
      <div class="report-section">
        <h3 class="section-title">Financial Summary</h3>
        <div class="summary-stats">
          <div class="stat-card">
            <div class="stat-title">Total Revenue</div>
            <div class="stat-value">R ${totalRevenue.toLocaleString()}</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">Total Expenses</div>
            <div class="stat-value">R ${totalExpenses.toLocaleString()}</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">Net Profit</div>
            <div class="stat-value">R ${netProfit.toLocaleString()}</div>
          </div>
          <div class="stat-card">
            <div class="stat-title">ROI</div>
            <div class="stat-value">${roi.toFixed(2)}%</div>
          </div>
        </div>
      </div>
    `;
  }

  // Add feed usage summary section if feed report
  if (params.type === 'analysis' || params.type === 'performance') {
    html += `
      <div class="report-section">
        <h3 class="section-title">Feed Usage Summary</h3>
        <div class="summary-stats">
          <div class="stat-card">
            <div class="stat-title">Total Feed Cost</div>
            <div class="stat-value">R ${totalFeedCost.toLocaleString()}</div>
          </div>
          ${Object.entries(feedUsageByType).slice(0, 3).map(([feedType, quantity]) => `
            <div class="stat-card">
              <div class="stat-title">${feedType}</div>
              <div class="stat-value">${quantity.toLocaleString()} kg</div>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }

  // Add charts section
  html += `
    <div class="report-section">
      <h3 class="section-title">Charts</h3>
      <div class="chart-container">
        <svg width="100%" height="300" viewBox="0 0 500 300" xmlns="http://www.w3.org/2000/svg">
          <!-- Generate pie chart for species distribution -->
          ${generateSVGPieChart(
            Object.entries(speciesCounts).map(([species, count], index) => ({
              name: species,
              value: count,
              color: `hsl(${index * 40}, 70%, 50%)`
            })),
            'Species Distribution'
          )}
        </svg>
        <div class="chart-legend">
          ${Object.entries(speciesCounts).map(([species, count], index) => `
            <div class="legend-item">
              <span class="legend-color" style="background-color: hsl(${index * 40}, 70%, 50%);"></span>
              <span class="legend-label">${species}: ${count} (${(count / totalAnimals * 100).toFixed(1)}%)</span>
            </div>
          `).join('')}
        </div>
      </div>
      <div class="chart-container">
        <svg width="100%" height="300" viewBox="0 0 500 300" xmlns="http://www.w3.org/2000/svg">
          <!-- Generate pie chart for health status distribution -->
          ${generateSVGPieChart(
            Object.entries(healthCounts).map(([status, count]) => ({
              name: status,
              value: count,
              color: getHealthStatusColor(status)
            })),
            'Health Status Distribution'
          )}
        </svg>
        <div class="chart-legend">
          ${Object.entries(healthCounts).map(([status, count]) => `
            <div class="legend-item">
              <span class="legend-color" style="background-color: ${getHealthStatusColor(status)};"></span>
              <span class="legend-label">${status}: ${count} (${(count / totalAnimals * 100).toFixed(1)}%)</span>
            </div>
          `).join('')}
        </div>
      </div>
    </div>
  `;

  // Add feed usage table if feed report
  if (params.type === 'analysis' || params.type === 'performance') {
    html += `
      <div class="report-section">
        <h3 class="section-title">Feed Usage Details</h3>
        <table>
          <thead>
            <tr>
              <th>Feed Type</th>
              <th>Quantity Used</th>
              <th>Cost</th>
              <th>Animal Group</th>
              <th>Date</th>
            </tr>
          </thead>
          <tbody>
    `;

    // Add rows for each feeding record
    feedingRecords.slice(0, 10).forEach(record => {
      html += `
        <tr>
          <td>${record.feedType}</td>
          <td>${record.quantity} ${record.unit || 'kg'}</td>
          <td>R ${(record.totalCost || 0).toLocaleString()}</td>
          <td>${record.animalGroupId || 'All'}</td>
          <td>${new Date(record.date).toLocaleDateString('en-ZA')}</td>
        </tr>
      `;
    });

    // Close the table
    html += `
          </tbody>
        </table>
      </div>
    `;
  }

  // Add data table based on report type
  switch (params.type) {
    case 'health':
      html += generateDetailedHealthReport(animals, healthRecords);
      break;
    case 'financial':
      html += generateDetailedFinancialReport(animals, financialRecords);
      break;
    case 'market':
      html += generateDetailedMarketReport(animals);
      break;
    case 'performance':
      html += generateDetailedPerformanceReport(animals);
      break;
    case 'analysis':
      html += generateDetailedAnalysisReport(animals);
      break;
    default:
      html += generateAnimalTable(animals);
  }

  // Add footer
  html += `
        <div class="footer">
          <p>MayCaiphus Livestock Management System</p>
          <p>This report was generated automatically. For questions or support, please contact your system administrator.</p>
        </div>
      </div>
    </body>
  </html>
  `;

  return html;
  } catch (error) {
    console.error('Error generating HTML report:', error);
    showSnackbar('Failed to generate report', 'error');

    // Get detailed error message
    let errorMessage = 'Unknown error';
    if (error instanceof Error) {
      errorMessage = error.message;
    } else if (typeof error === 'string') {
      errorMessage = error;
    } else if (error && typeof error === 'object') {
      errorMessage = JSON.stringify(error);
    }

    // Return a simple error report with more detailed information
    return `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Error Generating Report</title>
        <style>
          body { font-family: Arial, sans-serif; padding: 20px; }
          .error { color: #d32f2f; }
          .error-details { background-color: #f8f8f8; padding: 15px; border-left: 4px solid #d32f2f; margin-top: 20px; }
          .error-message { font-family: monospace; white-space: pre-wrap; }
          .suggestions { margin-top: 20px; background-color: #e8f5e9; padding: 15px; border-left: 4px solid #4caf50; }
        </style>
      </head>
      <body>
        <h1 class="error">Error Generating Report</h1>
        <p>There was an error generating the requested report. Please try again later or contact support.</p>

        <div class="error-details">
          <h2>Error Details</h2>
          <p class="error-message">${errorMessage}</p>
        </div>

        <div class="suggestions">
          <h2>Suggestions</h2>
          <ul>
            <li>Check your internet connection</li>
            <li>Verify that the MongoDB connection is properly configured</li>
            <li>Try refreshing the page and generating the report again</li>
            <li>If the error persists, contact your system administrator</li>
          </ul>
        </div>
      </body>
      </html>
    `;
  }
};

/**
 * Generate an HTML table for animal data
 */
const generateAnimalTable = (animals: Animal[]): string => {
  let html = `
    <div class="report-section">
      <h3 class="section-title">Animal Data</h3>
      <table>
        <thead>
          <tr>
            <th>Tag</th>
            <th>Name</th>
            <th>Species</th>
            <th>Breed</th>
            <th>Gender</th>
            <th>Birth Date</th>
            <th>Status</th>
            <th>Health</th>
            <th>Location</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Add rows for each animal
  animals.forEach(animal => {
    html += `
      <tr>
        <td>${animal.tagNumber}</td>
        <td>${animal.name}</td>
        <td>${animal.species || animal.type}</td>
        <td>${animal.breed}</td>
        <td>${animal.gender}</td>
        <td>${animal.birthDate || 'Unknown'}</td>
        <td>${animal.status}</td>
        <td>${animal.healthStatus}</td>
        <td>${animal.location}</td>
      </tr>
    `;
  });

  // Close the table
  html += `
        </tbody>
      </table>
    </div>
  `;

  return html;
};

/**
 * Generate an HTML table for health data
 */
const generateHealthTable = (animals: Animal[]): string => {
  let html = `
    <div class="report-section">
      <h3 class="section-title">Animal Health Data</h3>
      <table>
        <thead>
          <tr>
            <th>Tag</th>
            <th>Name</th>
            <th>Species</th>
            <th>Health Status</th>
            <th>Last Check-up</th>
            <th>Next Check-up</th>
            <th>Notes</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Add rows for each animal with mock health data
  animals.forEach(animal => {
    const lastCheckup = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
    const nextCheckup = new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000);

    html += `
      <tr>
        <td>${animal.tagNumber}</td>
        <td>${animal.name}</td>
        <td>${animal.species || animal.type}</td>
        <td>${animal.healthStatus}</td>
        <td>${lastCheckup.toLocaleDateString('en-ZA')}</td>
        <td>${nextCheckup.toLocaleDateString('en-ZA')}</td>
        <td>${animal.notes || '-'}</td>
      </tr>
    `;
  });

  // Close the table
  html += `
        </tbody>
      </table>
    </div>
  `;

  return html;
};

/**
 * Generate a detailed health report with comprehensive health data and visualizations
 */
const generateDetailedHealthReport = (animals: any[], healthRecords: any[]): string => {
  // Calculate health statistics
  const totalAnimals = animals.length;
  const healthStatusCounts: Record<string, number> = {};
  const treatmentTypeCounts: Record<string, number> = {};
  const monthlyHealthIssues: Record<string, number> = {};
  const vaccinationStatus: Record<string, number> = { 'Up to date': 0, 'Due soon': 0, 'Overdue': 0 };

  // Process health records
  healthRecords.forEach(record => {
    // Count by treatment type
    if (record.type) {
      treatmentTypeCounts[record.type] = (treatmentTypeCounts[record.type] || 0) + 1;
    }

    // Count monthly health issues
    if (record.date) {
      const month = new Date(record.date).toLocaleString('en-ZA', { month: 'short', year: 'numeric' });
      monthlyHealthIssues[month] = (monthlyHealthIssues[month] || 0) + 1;
    }
  });

  // Process animals for health status
  animals.forEach(animal => {
    if (animal.healthStatus) {
      healthStatusCounts[animal.healthStatus] = (healthStatusCounts[animal.healthStatus] || 0) + 1;
    }

    // Determine vaccination status (mock data for demonstration)
    const randomStatus = Math.random();
    if (randomStatus > 0.7) {
      vaccinationStatus['Up to date']++;
    } else if (randomStatus > 0.4) {
      vaccinationStatus['Due soon']++;
    } else {
      vaccinationStatus['Overdue']++;
    }
  });

  // Start building the detailed health report
  let html = `
    <div class="report-section">
      <h3 class="section-title">Health Overview</h3>
      <div class="summary-stats">
        <div class="stat-card">
          <div class="stat-title">Total Animals</div>
          <div class="stat-value">${totalAnimals}</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">Healthy Animals</div>
          <div class="stat-value">${healthStatusCounts['healthy'] || 0}</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">Sick/Injured</div>
          <div class="stat-value">${(healthStatusCounts['sick'] || 0) + (healthStatusCounts['injured'] || 0)}</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">Vaccination Status</div>
          <div class="stat-value">${vaccinationStatus['Up to date']} up to date</div>
        </div>
      </div>
    </div>

    <div class="report-section">
      <h3 class="section-title">Health Status Distribution</h3>
      <div class="chart-container">
        <svg width="100%" height="300" viewBox="0 0 500 300" xmlns="http://www.w3.org/2000/svg">
          <!-- Generate pie chart for health status -->
          ${generateSVGPieChart(
            Object.entries(healthStatusCounts).map(([status, count]) => ({
              name: status,
              value: count,
              color: getHealthStatusColor(status)
            })),
            'Health Status Distribution'
          )}
        </svg>
        <div class="chart-legend">
          ${Object.entries(healthStatusCounts).map(([status, count]) => `
            <div class="legend-item">
              <span class="legend-color" style="background-color: ${getHealthStatusColor(status)};"></span>
              <span class="legend-label">${status}: ${count} (${((count / totalAnimals) * 100).toFixed(1)}%)</span>
            </div>
          `).join('')}
        </div>
      </div>
    </div>

    <div class="report-section">
      <h3 class="section-title">Treatment Types</h3>
      <div class="chart-container">
        <svg width="100%" height="300" viewBox="0 0 500 300" xmlns="http://www.w3.org/2000/svg">
          <!-- Generate bar chart for treatment types -->
          ${generateSVGBarChart(
            Object.entries(treatmentTypeCounts).map(([type, count]) => ({
              name: type,
              value: count,
              color: getHealthStatusColor(type)
            })),
            'Treatment Types Distribution',
            (value) => value.toString()
          )}
        </svg>
        <div class="chart-legend">
          ${Object.entries(treatmentTypeCounts).map(([type, count]) => `
            <div class="legend-item">
              <span class="legend-color" style="background-color: ${getHealthStatusColor(type)};"></span>
              <span class="legend-label">${type}: ${count} (${((count / Object.values(treatmentTypeCounts).reduce((a, b) => a + b, 0)) * 100).toFixed(1)}%)</span>
            </div>
          `).join('')}
        </div>
      </div>
    </div>

    <div class="report-section">
      <h3 class="section-title">Monthly Health Trends</h3>
      <div class="chart-container">
        <svg width="100%" height="300" viewBox="0 0 500 300" xmlns="http://www.w3.org/2000/svg">
          <!-- Generate line chart for monthly health trends -->
          ${generateSVGLineChart(
            [{
              name: 'Health Issues',
              values: Object.entries(monthlyHealthIssues).map(([month, count]) => ({
                key: month,
                value: count,
                color: '#e91e63'
              }))
            }],
            'Monthly Health Issues',
            (value) => value.toString()
          )}
        </svg>
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-color" style="background-color: #e91e63;"></span>
            <span class="legend-label">Health Issues</span>
          </div>
        </div>
      </div>
    </div>

    <div class="report-section">
      <h3 class="section-title">Vaccination Status</h3>
      <div class="chart-container">
        <svg width="100%" height="300" viewBox="0 0 500 300" xmlns="http://www.w3.org/2000/svg">
          <!-- Generate pie chart for vaccination status -->
          ${generateSVGPieChart(
            Object.entries(vaccinationStatus).map(([status, count]) => {
              let color = '#4caf50'; // Default green for "Up to date"
              if (status === 'Due soon') color = '#ff9800'; // Orange for due soon
              if (status === 'Overdue') color = '#f44336'; // Red for overdue

              return {
                name: status,
                value: count,
                color: color
              };
            }),
            'Vaccination Status'
          )}
        </svg>
        <div class="chart-legend">
          ${Object.entries(vaccinationStatus).map(([status, count]) => {
            let color = '#4caf50'; // Default green for "Up to date"
            if (status === 'Due soon') color = '#ff9800'; // Orange for due soon
            if (status === 'Overdue') color = '#f44336'; // Red for overdue

            return `
              <div class="legend-item">
                <span class="legend-color" style="background-color: ${color};"></span>
                <span class="legend-label">${status}: ${count} (${((count / totalAnimals) * 100).toFixed(1)}%)</span>
              </div>
            `;
          }).join('')}
        </div>
      </div>
    </div>

    <div class="report-section">
      <h3 class="section-title">Recent Health Records</h3>
      <table>
        <thead>
          <tr>
            <th>Animal ID</th>
            <th>Date</th>
            <th>Type</th>
            <th>Diagnosis</th>
            <th>Treatment</th>
            <th>Performed By</th>
            <th>Follow-up Date</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Add rows for each health record (limit to most recent 15)
  const sortedRecords = [...healthRecords].sort((a, b) =>
    new Date(b.date).getTime() - new Date(a.date).getTime()
  ).slice(0, 15);

  sortedRecords.forEach(record => {
    const animalInfo = animals.find(a => a.id === record.animalId) || {};
    html += `
      <tr>
        <td>${record.animalId} (${animalInfo.name || 'Unknown'})</td>
        <td>${new Date(record.date).toLocaleDateString('en-ZA')}</td>
        <td>${record.type || 'Check-up'}</td>
        <td>${record.diagnosis || '-'}</td>
        <td>${record.treatment || '-'}</td>
        <td>${record.performedBy || 'Staff'}</td>
        <td>${record.followUpDate ? new Date(record.followUpDate).toLocaleDateString('en-ZA') : '-'}</td>
      </tr>
    `;
  });

  // Close the table
  html += `
        </tbody>
      </table>
    </div>

    <div class="report-section">
      <h3 class="section-title">Animals Requiring Attention</h3>
      <table>
        <thead>
          <tr>
            <th>Tag</th>
            <th>Name</th>
            <th>Species</th>
            <th>Health Status</th>
            <th>Issue</th>
            <th>Last Check-up</th>
            <th>Next Check-up</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Add rows for animals requiring attention
  const animalsNeedingAttention = animals.filter(animal =>
    animal.healthStatus === 'sick' ||
    animal.healthStatus === 'injured' ||
    animal.healthStatus === 'critical'
  );

  animalsNeedingAttention.forEach(animal => {
    const lastRecord = healthRecords
      .filter(r => r.animalId === animal.id)
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0] || {};

    const lastCheckup = lastRecord.date ? new Date(lastRecord.date) : new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);
    const nextCheckup = lastRecord.followUpDate ? new Date(lastRecord.followUpDate) : new Date(Date.now() + Math.random() * 10 * 24 * 60 * 60 * 1000);

    html += `
      <tr>
        <td>${animal.tagNumber}</td>
        <td>${animal.name}</td>
        <td>${animal.species || animal.type}</td>
        <td>${animal.healthStatus}</td>
        <td>${lastRecord.diagnosis || 'Requires check-up'}</td>
        <td>${lastCheckup.toLocaleDateString('en-ZA')}</td>
        <td>${nextCheckup.toLocaleDateString('en-ZA')}</td>
      </tr>
    `;
  });

  // Close the table
  html += `
        </tbody>
      </table>
    </div>
  `;

  return html;
};

/**
 * Generate an SVG pie chart
 */
const generateSVGPieChart = (
  data: { name: string; value: number; color: string }[],
  title: string
): string => {
  const total = data.reduce((sum, item) => sum + item.value, 0);
  if (total === 0) return '<text x="250" y="150" text-anchor="middle">No data available</text>';

  const centerX = 250;
  const centerY = 150;
  const radius = 100;

  let startAngle = 0;
  let paths = '';
  let labels = '';

  data.forEach((item, index) => {
    const percentage = item.value / total;
    const endAngle = startAngle + percentage * 2 * Math.PI;

    // Calculate path
    const x1 = centerX + radius * Math.cos(startAngle);
    const y1 = centerY + radius * Math.sin(startAngle);
    const x2 = centerX + radius * Math.cos(endAngle);
    const y2 = centerY + radius * Math.sin(endAngle);

    const largeArcFlag = percentage > 0.5 ? 1 : 0;

    paths += `<path d="M ${centerX} ${centerY} L ${x1} ${y1} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2} Z"
              fill="${item.color}" stroke="white" stroke-width="1" />`;

    // Add label if segment is large enough
    if (percentage > 0.05) {
      const labelAngle = startAngle + (endAngle - startAngle) / 2;
      const labelRadius = radius * 0.7;
      const labelX = centerX + labelRadius * Math.cos(labelAngle);
      const labelY = centerY + labelRadius * Math.sin(labelAngle);

      labels += `<text x="${labelX}" y="${labelY}" text-anchor="middle" fill="white" font-weight="bold" font-size="12">
                  ${(percentage * 100).toFixed(0)}%
                </text>`;
    }

    startAngle = endAngle;
  });

  return `
    <g>
      <text x="${centerX}" y="30" text-anchor="middle" font-size="16" font-weight="bold">${title}</text>
      ${paths}
      ${labels}
    </g>
  `;
};

/**
 * Generate an SVG bar chart
 */
const generateSVGBarChart = (
  data: { name: string; value: number; color: string }[],
  title: string,
  formatValue?: (value: number) => string
): string => {
  if (data.length === 0) return '<text x="250" y="150" text-anchor="middle">No data available</text>';

  const width = 500;
  const height = 300;
  const margin = { top: 40, right: 20, bottom: 60, left: 60 };
  const chartWidth = width - margin.left - margin.right;
  const chartHeight = height - margin.top - margin.bottom;

  const maxValue = Math.max(...data.map(d => d.value));
  const barWidth = chartWidth / data.length * 0.8;
  const barSpacing = chartWidth / data.length * 0.2;

  let bars = '';
  let xLabels = '';
  let yLabels = '';

  // Generate y-axis labels
  for (let i = 0; i <= 5; i++) {
    const value = maxValue * i / 5;
    const y = margin.top + chartHeight - (chartHeight * i / 5);
    yLabels += `
      <line x1="${margin.left - 5}" y1="${y}" x2="${margin.left}" y2="${y}" stroke="#666" />
      <text x="${margin.left - 10}" y="${y + 5}" text-anchor="end" font-size="12">${formatValue ? formatValue(value) : value}</text>
    `;
  }

  // Generate bars and x-axis labels
  data.forEach((item, index) => {
    const x = margin.left + (chartWidth / data.length) * index + barSpacing / 2;
    const barHeight = (item.value / maxValue) * chartHeight;
    const y = margin.top + chartHeight - barHeight;

    bars += `<rect x="${x}" y="${y}" width="${barWidth}" height="${barHeight}" fill="${item.color}" />`;

    xLabels += `<text x="${x + barWidth/2}" y="${margin.top + chartHeight + 20}" text-anchor="middle" font-size="12">${item.name}</text>`;
  });

  return `
    <g>
      <text x="${width/2}" y="20" text-anchor="middle" font-size="16" font-weight="bold">${title}</text>

      <!-- Y-axis -->
      <line x1="${margin.left}" y1="${margin.top}" x2="${margin.left}" y2="${margin.top + chartHeight}" stroke="#666" />
      ${yLabels}

      <!-- X-axis -->
      <line x1="${margin.left}" y1="${margin.top + chartHeight}" x2="${margin.left + chartWidth}" y2="${margin.top + chartHeight}" stroke="#666" />
      ${xLabels}

      <!-- Bars -->
      ${bars}
    </g>
  `;
};

/**
 * Generate an SVG line chart
 */
const generateSVGLineChart = (
  data: { name: string; values: { key: string; value: number; color: string }[] }[],
  title: string,
  formatValue?: (value: number) => string
): string => {
  if (data.length === 0) return '<text x="250" y="150" text-anchor="middle">No data available</text>';

  const width = 500;
  const height = 300;
  const margin = { top: 40, right: 20, bottom: 60, left: 60 };
  const chartWidth = width - margin.left - margin.right;
  const chartHeight = height - margin.top - margin.bottom;

  // Find max value across all data series
  const allValues = data.flatMap(series => series.values.map(v => v.value));
  const maxValue = Math.max(...allValues);

  let lines = '';
  let points = '';
  let xLabels = '';
  let yLabels = '';
  let legend = '';

  // Generate y-axis labels
  for (let i = 0; i <= 5; i++) {
    const value = maxValue * i / 5;
    const y = margin.top + chartHeight - (chartHeight * i / 5);
    yLabels += `
      <line x1="${margin.left - 5}" y1="${y}" x2="${margin.left}" y2="${y}" stroke="#666" />
      <text x="${margin.left - 10}" y="${y + 5}" text-anchor="end" font-size="12">${formatValue ? formatValue(value) : value}</text>
    `;
  }

  // Generate x-axis labels
  data[0].values.forEach((item, index) => {
    const x = margin.left + (chartWidth / (data[0].values.length - 1)) * index;
    xLabels += `<text x="${x}" y="${margin.top + chartHeight + 20}" text-anchor="middle" font-size="12">${item.key}</text>`;
  });

  // Generate lines and points for each data series
  data.forEach((series, seriesIndex) => {
    let pathData = '';

    series.values.forEach((point, index) => {
      const x = margin.left + (chartWidth / (series.values.length - 1)) * index;
      const y = margin.top + chartHeight - (point.value / maxValue) * chartHeight;

      if (index === 0) {
        pathData += `M ${x} ${y}`;
      } else {
        pathData += ` L ${x} ${y}`;
      }

      points += `<circle cx="${x}" cy="${y}" r="4" fill="${point.color}" />`;
    });

    lines += `<path d="${pathData}" stroke="${series.values[0].color}" stroke-width="2" fill="none" />`;

    // Add to legend
    legend += `
      <g transform="translate(${margin.left + seriesIndex * 100}, ${height - 20})">
        <line x1="0" y1="0" x2="20" y2="0" stroke="${series.values[0].color}" stroke-width="2" />
        <text x="25" y="5" font-size="12">${series.name}</text>
      </g>
    `;
  });

  return `
    <g>
      <text x="${width/2}" y="20" text-anchor="middle" font-size="16" font-weight="bold">${title}</text>

      <!-- Y-axis -->
      <line x1="${margin.left}" y1="${margin.top}" x2="${margin.left}" y2="${margin.top + chartHeight}" stroke="#666" />
      ${yLabels}

      <!-- X-axis -->
      <line x1="${margin.left}" y1="${margin.top + chartHeight}" x2="${margin.left + chartWidth}" y2="${margin.top + chartHeight}" stroke="#666" />
      ${xLabels}

      <!-- Grid lines -->
      ${Array(6).fill(0).map((_, i) => {
        const y = margin.top + chartHeight - (chartHeight * i / 5);
        return `<line x1="${margin.left}" y1="${y}" x2="${margin.left + chartWidth}" y2="${y}" stroke="#ddd" stroke-dasharray="5,5" />`;
      }).join('')}

      <!-- Lines and points -->
      ${lines}
      ${points}

      <!-- Legend -->
      ${legend}
    </g>
  `;
};

/**
 * Get color for health status
 */
const getHealthStatusColor = (status: string): string => {
  const statusLower = status.toLowerCase();
  if (statusLower.includes('healthy') || statusLower === 'good') return '#4caf50';
  if (statusLower.includes('sick') || statusLower.includes('ill')) return '#f44336';
  if (statusLower.includes('injured')) return '#ff9800';
  if (statusLower.includes('critical')) return '#d32f2f';
  if (statusLower.includes('recovering')) return '#2196f3';
  if (statusLower.includes('quarantine')) return '#9c27b0';
  if (statusLower.includes('pregnant')) return '#e91e63';
  if (statusLower.includes('treatment')) return '#ff5722';
  return '#607d8b'; // Default color
};

/**
 * Get color for financial data
 */
const getFinancialColor = (type: string): string => {
  const typeLower = type.toLowerCase();
  if (typeLower.includes('revenue') || typeLower.includes('income') || typeLower.includes('profit')) return '#4caf50';
  if (typeLower.includes('expense') || typeLower.includes('cost')) return '#f44336';
  if (typeLower.includes('sales')) return '#2196f3';
  if (typeLower.includes('feed')) return '#ff9800';
  if (typeLower.includes('labor')) return '#9c27b0';
  if (typeLower.includes('veterinary')) return '#e91e63';
  if (typeLower.includes('equipment')) return '#00bcd4';
  if (typeLower.includes('utilities')) return '#607d8b';
  return '#3f51b5'; // Default color
};

/**
 * Get date range based on time period
 */
const getDateRange = (
  timePeriod: TimePeriod,
  startDateStr?: string,
  endDateStr?: string
): { startDate: string; endDate: string } => {
  const now = new Date();
  const endDate = endDateStr ? new Date(endDateStr) : now;
  let startDate: Date;

  if (timePeriod === 'custom' && startDateStr) {
    startDate = new Date(startDateStr);
  } else {
    switch (timePeriod) {
      case 'week':
        startDate = new Date(now);
        startDate.setDate(now.getDate() - 7);
        break;
      case 'month':
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 1);
        break;
      case 'quarter':
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 3);
        break;
      case 'year':
        startDate = new Date(now);
        startDate.setFullYear(now.getFullYear() - 1);
        break;
      default:
        startDate = new Date(now);
        startDate.setMonth(now.getMonth() - 1);
    }
  }

  return {
    startDate: startDate.toISOString().split('T')[0],
    endDate: endDate.toISOString().split('T')[0]
  };
};

/**
 * Generate an HTML table for financial data
 */
const generateFinancialTable = (animals: Animal[]): string => {
  let html = `
    <div class="report-section">
      <h3 class="section-title">Financial Data</h3>
      <table>
        <thead>
          <tr>
            <th>Tag</th>
            <th>Name</th>
            <th>Species</th>
            <th>Purchase Date</th>
            <th>Purchase Price (R)</th>
            <th>Current Value (R)</th>
            <th>ROI (%)</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Add rows for each animal with real financial data
  animals.forEach(animal => {
    const purchasePrice = animal.purchasePrice || 0;
    // Calculate current value based on age, weight, and market factors
    // This is a simplified calculation - in a real app, this would be more complex
    const ageInMonths = animal.birthDate ?
      Math.floor((new Date().getTime() - new Date(animal.birthDate).getTime()) / (30 * 24 * 60 * 60 * 1000)) : 24;

    // Base value calculation
    let currentValue = purchasePrice;

    // Add value based on age (younger animals may be worth more)
    if (ageInMonths < 12) {
      currentValue *= 1.2; // 20% premium for young animals
    } else if (ageInMonths > 60) {
      currentValue *= 0.8; // 20% discount for older animals
    }

    // Add value based on health status
    if (animal.healthStatus === 'healthy') {
      currentValue *= 1.1; // 10% premium for healthy animals
    } else if (animal.healthStatus === 'sick' || animal.healthStatus === 'injured') {
      currentValue *= 0.7; // 30% discount for sick/injured animals
    }

    // Calculate ROI
    const roi = purchasePrice > 0 ? ((currentValue - purchasePrice) / purchasePrice * 100) : 0;

    html += `
      <tr>
        <td>${animal.tagNumber}</td>
        <td>${animal.name}</td>
        <td>${animal.species || animal.type}</td>
        <td>${animal.purchaseDate || '-'}</td>
        <td>R ${purchasePrice.toLocaleString()}</td>
        <td>R ${currentValue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>
        <td>${roi.toFixed(2)}%</td>
      </tr>
    `;
  });

  // Close the table
  html += `
        </tbody>
      </table>
    </div>
  `;

  return html;
};

/**
 * Generate a detailed financial report with comprehensive financial data and visualizations
 */
const generateDetailedFinancialReport = (animals: any[], financialRecords: any[]): string => {
  // Calculate financial statistics
  const totalAnimals = animals.length;

  // Process financial records
  const totalRevenue = financialRecords
    .filter(record => record.type === 'income')
    .reduce((sum, record) => sum + (record.amount || 0), 0);

  const totalExpenses = financialRecords
    .filter(record => record.type === 'expense')
    .reduce((sum, record) => sum + (record.amount || 0), 0);

  const netProfit = totalRevenue - totalExpenses;
  const profitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;

  // Calculate expense categories
  const expensesByCategory: Record<string, number> = {};
  financialRecords
    .filter(record => record.type === 'expense')
    .forEach(record => {
      if (record.category) {
        expensesByCategory[record.category] = (expensesByCategory[record.category] || 0) + (record.amount || 0);
      }
    });

  // Calculate revenue sources
  const revenueBySource: Record<string, number> = {};
  financialRecords
    .filter(record => record.type === 'income')
    .forEach(record => {
      if (record.category) {
        revenueBySource[record.category] = (revenueBySource[record.category] || 0) + (record.amount || 0);
      }
    });

  // Calculate monthly financial data
  const monthlyFinancialData: Record<string, { revenue: number, expenses: number, profit: number }> = {};

  financialRecords.forEach(record => {
    if (record.date) {
      const month = new Date(record.date).toLocaleString('en-ZA', { month: 'short', year: 'numeric' });

      if (!monthlyFinancialData[month]) {
        monthlyFinancialData[month] = { revenue: 0, expenses: 0, profit: 0 };
      }

      if (record.type === 'income') {
        monthlyFinancialData[month].revenue += (record.amount || 0);
      } else if (record.type === 'expense') {
        monthlyFinancialData[month].expenses += (record.amount || 0);
      }

      monthlyFinancialData[month].profit = monthlyFinancialData[month].revenue - monthlyFinancialData[month].expenses;
    }
  });

  // Calculate animal asset values
  const totalAssetValue = animals.reduce((sum, animal) => {
    // Simple calculation based on purchase price and health status
    let value = animal.purchasePrice || 0;

    if (animal.healthStatus === 'healthy') {
      value *= 1.1; // 10% premium for healthy animals
    } else if (animal.healthStatus === 'sick' || animal.healthStatus === 'injured') {
      value *= 0.7; // 30% discount for sick/injured animals
    }

    return sum + value;
  }, 0);

  // Start building the detailed financial report
  let html = `
    <div class="report-section">
      <h3 class="section-title">Financial Overview</h3>
      <div class="summary-stats">
        <div class="stat-card">
          <div class="stat-title">Total Revenue</div>
          <div class="stat-value">R ${totalRevenue.toLocaleString()}</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">Total Expenses</div>
          <div class="stat-value">R ${totalExpenses.toLocaleString()}</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">Net Profit</div>
          <div class="stat-value">R ${netProfit.toLocaleString()}</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">Profit Margin</div>
          <div class="stat-value">${profitMargin.toFixed(2)}%</div>
        </div>
      </div>
    </div>

    <div class="report-section">
      <h3 class="section-title">Monthly Financial Performance</h3>
      <div class="chart-container">
        <svg width="100%" height="350" viewBox="0 0 500 350" xmlns="http://www.w3.org/2000/svg">
          <!-- Generate line chart for monthly financial performance -->
          ${generateSVGLineChart(
            [
              {
                name: 'Revenue',
                values: Object.entries(monthlyFinancialData).map(([month, data]) => ({
                  key: month,
                  value: data.revenue,
                  color: '#4caf50'  // Green for revenue
                }))
              },
              {
                name: 'Expenses',
                values: Object.entries(monthlyFinancialData).map(([month, data]) => ({
                  key: month,
                  value: data.expenses,
                  color: '#f44336'  // Red for expenses
                }))
              },
              {
                name: 'Profit',
                values: Object.entries(monthlyFinancialData).map(([month, data]) => ({
                  key: month,
                  value: data.profit,
                  color: '#2196f3'  // Blue for profit
                }))
              }
            ],
            'Monthly Financial Performance',
            (value) => `R${value.toLocaleString()}`
          )}
        </svg>
        <div class="chart-legend">
          <div class="legend-item">
            <span class="legend-color" style="background-color: #4caf50;"></span>
            <span class="legend-label">Revenue</span>
          </div>
          <div class="legend-item">
            <span class="legend-color" style="background-color: #f44336;"></span>
            <span class="legend-label">Expenses</span>
          </div>
          <div class="legend-item">
            <span class="legend-color" style="background-color: #2196f3;"></span>
            <span class="legend-label">Profit</span>
          </div>
        </div>
      </div>
    </div>

    <div class="report-section">
      <h3 class="section-title">Expense Distribution</h3>
      <div class="chart-container">
        <svg width="100%" height="300" viewBox="0 0 500 300" xmlns="http://www.w3.org/2000/svg">
          <!-- Generate pie chart for expense categories -->
          ${generateSVGPieChart(
            Object.entries(expensesByCategory).map(([category, amount]) => ({
              name: category,
              value: amount,
              color: getFinancialColor(category)
            })),
            'Expense Categories'
          )}
        </svg>
        <div class="chart-legend">
          ${Object.entries(expensesByCategory).map(([category, amount]) => `
            <div class="legend-item">
              <span class="legend-color" style="background-color: ${getFinancialColor(category)};"></span>
              <span class="legend-label">${category}: R${amount.toLocaleString()} (${((amount / totalExpenses) * 100).toFixed(1)}%)</span>
            </div>
          `).join('')}
        </div>
      </div>
    </div>

    <div class="report-section">
      <h3 class="section-title">Revenue Sources</h3>
      <div class="chart-container">
        <svg width="100%" height="300" viewBox="0 0 500 300" xmlns="http://www.w3.org/2000/svg">
          <!-- Generate pie chart for revenue sources -->
          ${generateSVGPieChart(
            Object.entries(revenueBySource).map(([source, amount]) => ({
              name: source,
              value: amount,
              color: getFinancialColor(source)
            })),
            'Revenue Sources'
          )}
        </svg>
        <div class="chart-legend">
          ${Object.entries(revenueBySource).map(([source, amount]) => `
            <div class="legend-item">
              <span class="legend-color" style="background-color: ${getFinancialColor(source)};"></span>
              <span class="legend-label">${source}: R${amount.toLocaleString()} (${((amount / totalRevenue) * 100).toFixed(1)}%)</span>
            </div>
          `).join('')}
        </div>
      </div>
    </div>

    <div class="report-section">
      <h3 class="section-title">Asset Valuation</h3>
      <div class="summary-stats">
        <div class="stat-card">
          <div class="stat-title">Total Asset Value</div>
          <div class="stat-value">R ${totalAssetValue.toLocaleString()}</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">Average Asset Value</div>
          <div class="stat-value">R ${(totalAssetValue / (totalAnimals || 1)).toLocaleString(undefined, { maximumFractionDigits: 2 })}</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">Asset to Revenue Ratio</div>
          <div class="stat-value">${(totalAssetValue / (totalRevenue || 1)).toFixed(2)}</div>
        </div>
      </div>
    </div>

    <div class="report-section">
      <h3 class="section-title">Recent Financial Transactions</h3>
      <table>
        <thead>
          <tr>
            <th>Date</th>
            <th>Type</th>
            <th>Category</th>
            <th>Description</th>
            <th>Amount (R)</th>
            <th>Payment Method</th>
            <th>Reference</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Add rows for each financial record (limit to most recent 15)
  const sortedRecords = [...financialRecords].sort((a, b) =>
    new Date(b.date).getTime() - new Date(a.date).getTime()
  ).slice(0, 15);

  sortedRecords.forEach(record => {
    html += `
      <tr>
        <td>${new Date(record.date).toLocaleDateString('en-ZA')}</td>
        <td>${record.type === 'income' ? 'Income' : 'Expense'}</td>
        <td>${record.category || '-'}</td>
        <td>${record.description || '-'}</td>
        <td>R ${(record.amount || 0).toLocaleString()}</td>
        <td>${record.paymentMethod || '-'}</td>
        <td>${record.reference || '-'}</td>
      </tr>
    `;
  });

  // Close the table
  html += `
        </tbody>
      </table>
    </div>

    <div class="report-section">
      <h3 class="section-title">Budget Analysis</h3>
      <table>
        <thead>
          <tr>
            <th>Category</th>
            <th>Budgeted Amount (R)</th>
            <th>Actual Amount (R)</th>
            <th>Variance (R)</th>
            <th>Variance (%)</th>
            <th>Status</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Generate mock budget data for demonstration
  const budgetCategories = [
    'Feed', 'Veterinary', 'Labor', 'Equipment', 'Maintenance', 'Utilities', 'Marketing'
  ];

  budgetCategories.forEach(category => {
    const budgeted = Math.round(Math.random() * 50000) + 10000;
    const actual = expensesByCategory[category] || Math.round(Math.random() * budgeted * 1.2);
    const variance = budgeted - actual;
    const variancePercent = (variance / budgeted) * 100;
    const status = variancePercent > 10 ? 'Under Budget' : variancePercent < -10 ? 'Over Budget' : 'On Target';

    html += `
      <tr>
        <td>${category}</td>
        <td>R ${budgeted.toLocaleString()}</td>
        <td>R ${actual.toLocaleString()}</td>
        <td>R ${variance.toLocaleString()}</td>
        <td>${variancePercent.toFixed(2)}%</td>
        <td>${status}</td>
      </tr>
    `;
  });

  // Close the table
  html += `
        </tbody>
      </table>
    </div>
  `;

  return html;
};

/**
 * Generate an HTML table for market data
 */
const generateMarketTable = (animals: Animal[]): string => {
  // Simplified implementation for market data
  return generateFinancialTable(animals);
};

/**
 * Generate a detailed market report with comprehensive market data and visualizations
 */
const generateDetailedMarketReport = (animals: any[]): string => {
  // Calculate market statistics
  const totalAnimals = animals.length;
  const speciesCounts: Record<string, number> = {};
  const marketValueBySpecies: Record<string, number> = {};
  const marketTrends: Record<string, { current: number, previous: number, change: number }> = {};

  // Process animals for market data
  animals.forEach(animal => {
    if (animal.species) {
      speciesCounts[animal.species] = (speciesCounts[animal.species] || 0) + 1;

      // Calculate market value (simplified)
      const marketValue = animal.purchasePrice ? animal.purchasePrice * 1.2 : 5000; // Default value if no purchase price
      marketValueBySpecies[animal.species] = (marketValueBySpecies[animal.species] || 0) + marketValue;
    }
  });

  // Generate mock market trend data
  Object.keys(speciesCounts).forEach(species => {
    const currentValue = marketValueBySpecies[species] / speciesCounts[species];
    const previousValue = currentValue * (0.8 + Math.random() * 0.4); // Random previous value between 80% and 120% of current
    const change = ((currentValue - previousValue) / previousValue) * 100;

    marketTrends[species] = {
      current: currentValue,
      previous: previousValue,
      change: change
    };
  });

  // Start building the detailed market report
  let html = `
    <div class="report-section">
      <h3 class="section-title">Market Overview</h3>
      <div class="summary-stats">
        <div class="stat-card">
          <div class="stat-title">Total Marketable Animals</div>
          <div class="stat-value">${totalAnimals}</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">Total Market Value</div>
          <div class="stat-value">R ${Object.values(marketValueBySpecies).reduce((sum, value) => sum + value, 0).toLocaleString()}</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">Average Value Per Animal</div>
          <div class="stat-value">R ${(Object.values(marketValueBySpecies).reduce((sum, value) => sum + value, 0) / totalAnimals).toLocaleString(undefined, { maximumFractionDigits: 2 })}</div>
        </div>
      </div>
    </div>

    <div class="report-section">
      <h3 class="section-title">Market Value by Species</h3>
      <div class="chart-container">
        <svg width="100%" height="300" viewBox="0 0 500 300" xmlns="http://www.w3.org/2000/svg">
          <!-- Generate bar chart for market value by species -->
          ${generateSVGBarChart(
            Object.entries(marketValueBySpecies).map(([species, value], index) => ({
              name: species,
              value: value,
              color: `hsl(${index * 40}, 70%, 50%)`
            })),
            'Market Value by Species',
            (value) => `R${Math.round(value).toLocaleString()}`
          )}
        </svg>
        <div class="chart-legend">
          ${Object.entries(marketValueBySpecies).map(([species, value], index) => `
            <div class="legend-item">
              <span class="legend-color" style="background-color: hsl(${index * 40}, 70%, 50%);"></span>
              <span class="legend-label">${species}: R${value.toLocaleString()}</span>
            </div>
          `).join('')}
        </div>
      </div>
    </div>

    <div class="report-section">
      <h3 class="section-title">Market Price Trends</h3>
      <div class="chart-container">
        <svg width="100%" height="300" viewBox="0 0 500 300" xmlns="http://www.w3.org/2000/svg">
          <!-- Generate bar chart for market price trends -->
          ${generateSVGBarChart(
            Object.entries(marketTrends).map(([species, data], index) => ({
              name: species,
              value: data.change,
              color: data.change >= 0 ? '#4caf50' : '#f44336'  // Green for positive, red for negative
            })),
            'Market Price Change (%)',
            (value) => `${value > 0 ? '+' : ''}${value.toFixed(1)}%`
          )}
        </svg>
        <div class="chart-legend">
          ${Object.entries(marketTrends).map(([species, data]) => `
            <div class="legend-item">
              <span class="legend-color" style="background-color: ${data.change >= 0 ? '#4caf50' : '#f44336'};"></span>
              <span class="legend-label">${species}: Current R${data.current.toLocaleString()} (${data.change > 0 ? '+' : ''}${data.change.toFixed(2)}%)</span>
            </div>
          `).join('')}
        </div>
      </div>
    </div>

    <div class="report-section">
      <h3 class="section-title">Market Price Comparison</h3>
      <table>
        <thead>
          <tr>
            <th>Species</th>
            <th>Current Price (R)</th>
            <th>Previous Price (R)</th>
            <th>Change (%)</th>
            <th>Trend</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Add rows for each species
  Object.entries(marketTrends).forEach(([species, data]) => {
    html += `
      <tr>
        <td>${species}</td>
        <td>R ${data.current.toLocaleString(undefined, { maximumFractionDigits: 2 })}</td>
        <td>R ${data.previous.toLocaleString(undefined, { maximumFractionDigits: 2 })}</td>
        <td>${data.change > 0 ? '+' : ''}${data.change.toFixed(2)}%</td>
        <td>${data.change > 5 ? '↑ Strong Increase' : data.change > 0 ? '↗ Slight Increase' : data.change > -5 ? '↘ Slight Decrease' : '↓ Strong Decrease'}</td>
      </tr>
    `;
  });

  // Close the table
  html += `
        </tbody>
      </table>
    </div>

    <div class="report-section">
      <h3 class="section-title">Market Ready Animals</h3>
      <table>
        <thead>
          <tr>
            <th>Tag</th>
            <th>Name</th>
            <th>Species</th>
            <th>Age</th>
            <th>Weight</th>
            <th>Health Status</th>
            <th>Estimated Value (R)</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Add rows for market-ready animals (simplified criteria)
  const marketReadyAnimals = animals.filter(animal =>
    animal.status === 'Active' &&
    animal.healthStatus === 'healthy'
  ).slice(0, 15); // Limit to 15 animals for brevity

  marketReadyAnimals.forEach(animal => {
    const ageInMonths = animal.birthDate ?
      Math.floor((new Date().getTime() - new Date(animal.birthDate).getTime()) / (30 * 24 * 60 * 60 * 1000)) : 24;

    const estimatedValue = animal.purchasePrice ? animal.purchasePrice * 1.2 : 5000;

    html += `
      <tr>
        <td>${animal.tagNumber}</td>
        <td>${animal.name}</td>
        <td>${animal.species || animal.type}</td>
        <td>${ageInMonths} months</td>
        <td>${animal.weight || '---'} kg</td>
        <td>${animal.healthStatus}</td>
        <td>R ${estimatedValue.toLocaleString(undefined, { maximumFractionDigits: 2 })}</td>
      </tr>
    `;
  });

  // Close the table
  html += `
        </tbody>
      </table>
    </div>
  `;

  return html;
};

/**
 * Generate an HTML table for performance data
 */
const generatePerformanceTable = (animals: Animal[]): string => {
  // Simplified implementation for performance data
  return generateHealthTable(animals);
};

/**
 * Generate a detailed performance report with comprehensive performance data and visualizations
 */
const generateDetailedPerformanceReport = (animals: any[]): string => {
  // Calculate performance statistics
  const totalAnimals = animals.length;
  const speciesPerformance: Record<string, { count: number, avgGrowth: number, avgProduction: number }> = {};
  const performanceTrends: Record<string, number[]> = {
    'Growth Rate': [3.2, 3.5, 3.8, 4.0, 4.2, 4.1],
    'Feed Conversion': [2.1, 2.0, 1.9, 1.8, 1.7, 1.7],
    'Production Yield': [85, 87, 89, 90, 92, 93]
  };

  // Process animals for performance data
  animals.forEach(animal => {
    if (animal.species) {
      if (!speciesPerformance[animal.species]) {
        speciesPerformance[animal.species] = { count: 0, avgGrowth: 0, avgProduction: 0 };
      }

      speciesPerformance[animal.species].count++;

      // Generate random performance metrics for demonstration
      const growthRate = 2 + Math.random() * 3; // Between 2 and 5
      const productionRate = 70 + Math.random() * 30; // Between 70 and 100

      // Update averages
      speciesPerformance[animal.species].avgGrowth =
        (speciesPerformance[animal.species].avgGrowth * (speciesPerformance[animal.species].count - 1) + growthRate) /
        speciesPerformance[animal.species].count;

      speciesPerformance[animal.species].avgProduction =
        (speciesPerformance[animal.species].avgProduction * (speciesPerformance[animal.species].count - 1) + productionRate) /
        speciesPerformance[animal.species].count;
    }
  });

  // Start building the detailed performance report
  let html = `
    <div class="report-section">
      <h3 class="section-title">Performance Overview</h3>
      <div class="summary-stats">
        <div class="stat-card">
          <div class="stat-title">Total Animals</div>
          <div class="stat-value">${totalAnimals}</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">Average Growth Rate</div>
          <div class="stat-value">${Object.values(speciesPerformance).reduce((sum, data) => sum + data.avgGrowth * data.count, 0) / totalAnimals || 0}%</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">Average Production</div>
          <div class="stat-value">${Object.values(speciesPerformance).reduce((sum, data) => sum + data.avgProduction * data.count, 0) / totalAnimals || 0}%</div>
        </div>
      </div>
    </div>

    <div class="report-section">
      <h3 class="section-title">Performance Trends</h3>
      <div class="chart-container">
        <svg width="100%" height="350" viewBox="0 0 500 350" xmlns="http://www.w3.org/2000/svg">
          <!-- Generate line chart for performance trends -->
          ${generateSVGLineChart(
            Object.entries(performanceTrends).map(([metric, values]) => ({
              name: metric,
              values: values.map((value, index) => {
                // Generate month labels (last 6 months)
                const date = new Date();
                date.setMonth(date.getMonth() - (5 - index));
                const month = date.toLocaleString('en-ZA', { month: 'short' });

                let color = '#4caf50'; // Default green
                if (metric === 'Feed Conversion') color = '#f44336'; // Red
                if (metric === 'Production Yield') color = '#2196f3'; // Blue

                return {
                  key: month,
                  value: value,
                  color: color
                };
              })
            })),
            'Performance Trends Over Time',
            (value) => value.toString()
          )}
        </svg>
        <div class="chart-legend">
          ${Object.entries(performanceTrends).map(([metric, values]) => {
            let color = '#4caf50'; // Default green
            if (metric === 'Feed Conversion') color = '#f44336'; // Red
            if (metric === 'Production Yield') color = '#2196f3'; // Blue

            return `
              <div class="legend-item">
                <span class="legend-color" style="background-color: ${color};"></span>
                <span class="legend-label">${metric}: Current ${values[values.length - 1]}</span>
              </div>
            `;
          }).join('')}
        </div>
      </div>
    </div>

    <div class="report-section">
      <h3 class="section-title">Performance by Species</h3>
      <table>
        <thead>
          <tr>
            <th>Species</th>
            <th>Count</th>
            <th>Avg Growth Rate (%)</th>
            <th>Avg Production (%)</th>
            <th>Feed Conversion Ratio</th>
            <th>Performance Rating</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Add rows for each species
  Object.entries(speciesPerformance).forEach(([species, data]) => {
    // Generate a mock feed conversion ratio
    const fcr = 1.5 + Math.random() * 1.0; // Between 1.5 and 2.5

    // Calculate a performance rating (1-10)
    const performanceRating = Math.round((10 - fcr) + (data.avgGrowth / 5) + (data.avgProduction / 10));

    html += `
      <tr>
        <td>${species}</td>
        <td>${data.count}</td>
        <td>${data.avgGrowth.toFixed(2)}%</td>
        <td>${data.avgProduction.toFixed(2)}%</td>
        <td>${fcr.toFixed(2)}</td>
        <td>${performanceRating}/10</td>
      </tr>
    `;
  });

  // Close the table
  html += `
        </tbody>
      </table>
    </div>

    <div class="report-section">
      <h3 class="section-title">Top Performing Animals</h3>
      <table>
        <thead>
          <tr>
            <th>Tag</th>
            <th>Name</th>
            <th>Species</th>
            <th>Age</th>
            <th>Growth Rate (%)</th>
            <th>Production (%)</th>
            <th>Performance Rating</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Generate mock performance data for individual animals
  const animalPerformance = animals.map(animal => {
    const growthRate = 2 + Math.random() * 3; // Between 2 and 5
    const productionRate = 70 + Math.random() * 30; // Between 70 and 100
    const performanceRating = Math.round(growthRate * 1.5 + productionRate / 10);

    return {
      ...animal,
      growthRate,
      productionRate,
      performanceRating
    };
  });

  // Sort by performance rating and take top 10
  const topPerformers = [...animalPerformance]
    .sort((a, b) => b.performanceRating - a.performanceRating)
    .slice(0, 10);

  topPerformers.forEach(animal => {
    const ageInMonths = animal.birthDate ?
      Math.floor((new Date().getTime() - new Date(animal.birthDate).getTime()) / (30 * 24 * 60 * 60 * 1000)) : 24;

    html += `
      <tr>
        <td>${animal.tagNumber}</td>
        <td>${animal.name}</td>
        <td>${animal.species || animal.type}</td>
        <td>${ageInMonths} months</td>
        <td>${animal.growthRate.toFixed(2)}%</td>
        <td>${animal.productionRate.toFixed(2)}%</td>
        <td>${animal.performanceRating}/10</td>
      </tr>
    `;
  });

  // Close the table
  html += `
        </tbody>
      </table>
    </div>
  `;

  return html;
};

/**
 * Generate an HTML table for analysis data
 */
const generateAnalysisTable = (animals: Animal[]): string => {
  // Simplified implementation for analysis data
  return generateAnimalTable(animals);
};

/**
 * Generate a detailed analysis report with comprehensive data analysis and visualizations
 */
const generateDetailedAnalysisReport = (animals: any[]): string => {
  // Calculate analysis statistics
  const totalAnimals = animals.length;
  const speciesCounts: Record<string, number> = {};
  const statusCounts: Record<string, number> = {};
  const healthCounts: Record<string, number> = {};
  const locationCounts: Record<string, number> = {};

  // Process animals for analysis data
  animals.forEach(animal => {
    // Count by species
    if (animal.species) {
      speciesCounts[animal.species] = (speciesCounts[animal.species] || 0) + 1;
    }

    // Count by status
    if (animal.status) {
      statusCounts[animal.status] = (statusCounts[animal.status] || 0) + 1;
    }

    // Count by health status
    if (animal.healthStatus) {
      healthCounts[animal.healthStatus] = (healthCounts[animal.healthStatus] || 0) + 1;
    }

    // Count by location
    if (animal.location) {
      locationCounts[animal.location] = (locationCounts[animal.location] || 0) + 1;
    }
  });

  // Calculate age distribution
  const ageGroups: Record<string, number> = {
    'Under 1 year': 0,
    '1-2 years': 0,
    '2-3 years': 0,
    '3-5 years': 0,
    'Over 5 years': 0
  };

  animals.forEach(animal => {
    if (animal.birthDate) {
      const ageInMonths = Math.floor((new Date().getTime() - new Date(animal.birthDate).getTime()) / (30 * 24 * 60 * 60 * 1000));

      if (ageInMonths < 12) {
        ageGroups['Under 1 year']++;
      } else if (ageInMonths < 24) {
        ageGroups['1-2 years']++;
      } else if (ageInMonths < 36) {
        ageGroups['2-3 years']++;
      } else if (ageInMonths < 60) {
        ageGroups['3-5 years']++;
      } else {
        ageGroups['Over 5 years']++;
      }
    }
  });

  // Start building the detailed analysis report
  let html = `
    <div class="report-section">
      <h3 class="section-title">Livestock Analysis Overview</h3>
      <div class="summary-stats">
        <div class="stat-card">
          <div class="stat-title">Total Animals</div>
          <div class="stat-value">${totalAnimals}</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">Species Count</div>
          <div class="stat-value">${Object.keys(speciesCounts).length}</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">Healthy Animals</div>
          <div class="stat-value">${healthCounts['healthy'] || 0} (${((healthCounts['healthy'] || 0) / totalAnimals * 100).toFixed(1)}%)</div>
        </div>
        <div class="stat-card">
          <div class="stat-title">Active Animals</div>
          <div class="stat-value">${statusCounts['Active'] || 0} (${((statusCounts['Active'] || 0) / totalAnimals * 100).toFixed(1)}%)</div>
        </div>
      </div>
    </div>

    <div class="report-section">
      <h3 class="section-title">Species Distribution</h3>
      <div class="chart-container">
        <svg width="100%" height="300" viewBox="0 0 500 300" xmlns="http://www.w3.org/2000/svg">
          <!-- Generate pie chart for species distribution -->
          ${generateSVGPieChart(
            Object.entries(speciesCounts).map(([species, count], index) => ({
              name: species,
              value: count,
              color: `hsl(${index * 40}, 70%, 50%)`
            })),
            'Species Distribution'
          )}
        </svg>
        <div class="chart-legend">
          ${Object.entries(speciesCounts).map(([species, count], index) => `
            <div class="legend-item">
              <span class="legend-color" style="background-color: hsl(${index * 40}, 70%, 50%);"></span>
              <span class="legend-label">${species}: ${count} (${(count / totalAnimals * 100).toFixed(1)}%)</span>
            </div>
          `).join('')}
        </div>
      </div>
    </div>

    <div class="report-section">
      <h3 class="section-title">Health Status Distribution</h3>
      <div class="chart-container">
        <svg width="100%" height="300" viewBox="0 0 500 300" xmlns="http://www.w3.org/2000/svg">
          <!-- Generate pie chart for health status distribution -->
          ${generateSVGPieChart(
            Object.entries(healthCounts).map(([status, count]) => ({
              name: status,
              value: count,
              color: getHealthStatusColor(status)
            })),
            'Health Status Distribution'
          )}
        </svg>
        <div class="chart-legend">
          ${Object.entries(healthCounts).map(([status, count]) => `
            <div class="legend-item">
              <span class="legend-color" style="background-color: ${getHealthStatusColor(status)};"></span>
              <span class="legend-label">${status}: ${count} (${(count / totalAnimals * 100).toFixed(1)}%)</span>
            </div>
          `).join('')}
        </div>
      </div>
    </div>

    <div class="report-section">
      <h3 class="section-title">Age Distribution</h3>
      <div class="chart-container">
        <svg width="100%" height="300" viewBox="0 0 500 300" xmlns="http://www.w3.org/2000/svg">
          <!-- Generate bar chart for age distribution -->
          ${generateSVGBarChart(
            Object.entries(ageGroups).map(([group, count], index) => ({
              name: group,
              value: count,
              color: `hsl(${200 + index * 30}, 70%, 50%)`
            })),
            'Age Distribution',
            (value) => value.toString()
          )}
        </svg>
        <div class="chart-legend">
          ${Object.entries(ageGroups).map(([group, count], index) => `
            <div class="legend-item">
              <span class="legend-color" style="background-color: hsl(${200 + index * 30}, 70%, 50%);"></span>
              <span class="legend-label">${group}: ${count} (${(count / totalAnimals * 100).toFixed(1)}%)</span>
            </div>
          `).join('')}
        </div>
      </div>
    </div>

    <div class="report-section">
      <h3 class="section-title">Location Distribution</h3>
      <table>
        <thead>
          <tr>
            <th>Location</th>
            <th>Animal Count</th>
            <th>Percentage</th>
            <th>Species Breakdown</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Add rows for each location
  Object.entries(locationCounts).forEach(([location, count]) => {
    // Calculate species breakdown for this location
    const speciesBreakdown: Record<string, number> = {};

    animals.filter(animal => animal.location === location).forEach(animal => {
      if (animal.species) {
        speciesBreakdown[animal.species] = (speciesBreakdown[animal.species] || 0) + 1;
      }
    });

    const speciesBreakdownStr = Object.entries(speciesBreakdown)
      .map(([species, speciesCount]) => `${species}: ${speciesCount}`)
      .join(', ');

    html += `
      <tr>
        <td>${location}</td>
        <td>${count}</td>
        <td>${(count / totalAnimals * 100).toFixed(1)}%</td>
        <td>${speciesBreakdownStr}</td>
      </tr>
    `;
  });

  // Close the table
  html += `
        </tbody>
      </table>
    </div>

    <div class="report-section">
      <h3 class="section-title">Comprehensive Animal Analysis</h3>
      <table>
        <thead>
          <tr>
            <th>Species</th>
            <th>Count</th>
            <th>Avg Age (months)</th>
            <th>Health Rate</th>
            <th>Active Rate</th>
            <th>Primary Location</th>
          </tr>
        </thead>
        <tbody>
  `;

  // Add rows for each species with detailed analysis
  Object.entries(speciesCounts).forEach(([species, count]) => {
    const speciesAnimals = animals.filter(animal => animal.species === species);

    // Calculate average age
    const totalAgeInMonths = speciesAnimals.reduce((sum, animal) => {
      if (animal.birthDate) {
        return sum + Math.floor((new Date().getTime() - new Date(animal.birthDate).getTime()) / (30 * 24 * 60 * 60 * 1000));
      }
      return sum + 24; // Default to 24 months if no birth date
    }, 0);

    const avgAge = totalAgeInMonths / count;

    // Calculate health rate
    const healthyCount = speciesAnimals.filter(animal => animal.healthStatus === 'healthy').length;
    const healthRate = (healthyCount / count) * 100;

    // Calculate active rate
    const activeCount = speciesAnimals.filter(animal => animal.status === 'Active').length;
    const activeRate = (activeCount / count) * 100;

    // Find primary location
    const locationCounts: Record<string, number> = {};
    speciesAnimals.forEach(animal => {
      if (animal.location) {
        locationCounts[animal.location] = (locationCounts[animal.location] || 0) + 1;
      }
    });

    const primaryLocation = Object.entries(locationCounts).sort((a, b) => b[1] - a[1])[0]?.[0] || 'Unknown';

    html += `
      <tr>
        <td>${species}</td>
        <td>${count}</td>
        <td>${avgAge.toFixed(1)}</td>
        <td>${healthRate.toFixed(1)}%</td>
        <td>${activeRate.toFixed(1)}%</td>
        <td>${primaryLocation}</td>
      </tr>
    `;
  });

  // Close the table
  html += `
        </tbody>
      </table>
    </div>
  `;

  return html;
};

// Helper function getHealthStatusColor is already defined above

// The generateSVGPieChart, generateSVGBarChart, and generateSVGLineChart functions are already defined earlier in the file

export const getSavedReports = async (type: ReportType): Promise<any[]> => {
  try {
    // In a real implementation, this would call the backend API
    // For now, we'll return mock data
    await new Promise(resolve => setTimeout(resolve, 800));

    // Mock saved reports data
    const mockReports = [
      {
        id: '1',
        name: `${type.charAt(0).toUpperCase() + type.slice(1)} Report - Monthly`,
        createdAt: new Date().toISOString(),
        format: 'pdf',
        size: '1.2 MB'
      },
      {
        id: '2',
        name: `${type.charAt(0).toUpperCase() + type.slice(1)} Report - Quarterly`,
        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        format: 'excel',
        size: '3.5 MB'
      },
      {
        id: '3',
        name: `${type.charAt(0).toUpperCase() + type.slice(1)} Report - Annual`,
        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),
        format: 'pdf',
        size: '5.8 MB'
      },
      {
        id: '4',
        name: `Custom ${type.charAt(0).toUpperCase() + type.slice(1)} Analysis`,
        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),
        format: 'csv',
        size: '0.9 MB'
      }
    ];

    return mockReports;
  } catch (error) {
    console.error(`Error fetching saved ${type} reports:`, error);
    throw error;
  }
};
