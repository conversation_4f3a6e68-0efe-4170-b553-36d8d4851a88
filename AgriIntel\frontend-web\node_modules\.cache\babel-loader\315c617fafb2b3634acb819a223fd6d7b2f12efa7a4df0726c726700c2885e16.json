{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ClientBulkWriteCursor = void 0;\nconst client_bulk_write_1 = require(\"../operations/client_bulk_write/client_bulk_write\");\nconst execute_operation_1 = require(\"../operations/execute_operation\");\nconst utils_1 = require(\"../utils\");\nconst abstract_cursor_1 = require(\"./abstract_cursor\");\n/**\n * This is the cursor that handles client bulk write operations. Note this is never\n * exposed directly to the user and is always immediately exhausted.\n * @internal\n */\nclass ClientBulkWriteCursor extends abstract_cursor_1.AbstractCursor {\n  /** @internal */\n  constructor(client, commandBuilder, options = {}) {\n    super(client, new utils_1.MongoDBNamespace('admin', '$cmd'), options);\n    this.commandBuilder = commandBuilder;\n    this.clientBulkWriteOptions = options;\n  }\n  /**\n   * We need a way to get the top level cursor response fields for\n   * generating the bulk write result, so we expose this here.\n   */\n  get response() {\n    if (this.cursorResponse) return this.cursorResponse;\n    return null;\n  }\n  get operations() {\n    return this.commandBuilder.lastOperations;\n  }\n  clone() {\n    const clonedOptions = (0, utils_1.mergeOptions)({}, this.clientBulkWriteOptions);\n    delete clonedOptions.session;\n    return new ClientBulkWriteCursor(this.client, this.commandBuilder, {\n      ...clonedOptions\n    });\n  }\n  /** @internal */\n  async _initialize(session) {\n    const clientBulkWriteOperation = new client_bulk_write_1.ClientBulkWriteOperation(this.commandBuilder, {\n      ...this.clientBulkWriteOptions,\n      ...this.cursorOptions,\n      session\n    });\n    const response = await (0, execute_operation_1.executeOperation)(this.client, clientBulkWriteOperation, this.timeoutContext);\n    this.cursorResponse = response;\n    return {\n      server: clientBulkWriteOperation.server,\n      session,\n      response\n    };\n  }\n}\nexports.ClientBulkWriteCursor = ClientBulkWriteCursor;", "map": {"version": 3, "names": ["client_bulk_write_1", "require", "execute_operation_1", "utils_1", "abstract_cursor_1", "ClientBulkWriteCursor", "AbstractCursor", "constructor", "client", "commandBuilder", "options", "MongoDBNamespace", "clientBulkWriteOptions", "response", "cursorResponse", "operations", "lastOperations", "clone", "clonedOptions", "mergeOptions", "session", "_initialize", "clientBulkWriteOperation", "ClientBulkWriteOperation", "cursorOptions", "executeOperation", "timeoutContext", "server", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cursor\\client_bulk_write_cursor.ts"], "sourcesContent": ["import { type Document } from '../bson';\nimport { type ClientBulkWriteCursorResponse } from '../cmap/wire_protocol/responses';\nimport type { MongoClient } from '../mongo_client';\nimport { ClientBulkWriteOperation } from '../operations/client_bulk_write/client_bulk_write';\nimport { type ClientBulkWriteCommandBuilder } from '../operations/client_bulk_write/command_builder';\nimport { type ClientBulkWriteOptions } from '../operations/client_bulk_write/common';\nimport { executeOperation } from '../operations/execute_operation';\nimport type { ClientSession } from '../sessions';\nimport { mergeOptions, MongoDBNamespace } from '../utils';\nimport {\n  AbstractCursor,\n  type AbstractCursorOptions,\n  type InitialCursorResponse\n} from './abstract_cursor';\n\n/** @public */\nexport interface ClientBulkWriteCursorOptions\n  extends Omit<AbstractCursorOptions, 'maxAwaitTimeMS' | 'tailable' | 'awaitData'>,\n    ClientBulkWriteOptions {}\n\n/**\n * This is the cursor that handles client bulk write operations. Note this is never\n * exposed directly to the user and is always immediately exhausted.\n * @internal\n */\nexport class ClientBulkWriteCursor extends AbstractCursor {\n  commandBuilder: ClientBulkWriteCommandBuilder;\n  /** @internal */\n  private cursorResponse?: ClientBulkWriteCursorResponse;\n  /** @internal */\n  private clientBulkWriteOptions: ClientBulkWriteOptions;\n\n  /** @internal */\n  constructor(\n    client: MongoClient,\n    commandBuilder: ClientBulkWriteCommandBuilder,\n    options: ClientBulkWriteCursorOptions = {}\n  ) {\n    super(client, new MongoDBNamespace('admin', '$cmd'), options);\n\n    this.commandBuilder = commandBuilder;\n    this.clientBulkWriteOptions = options;\n  }\n\n  /**\n   * We need a way to get the top level cursor response fields for\n   * generating the bulk write result, so we expose this here.\n   */\n  get response(): ClientBulkWriteCursorResponse | null {\n    if (this.cursorResponse) return this.cursorResponse;\n    return null;\n  }\n\n  get operations(): Document[] {\n    return this.commandBuilder.lastOperations;\n  }\n\n  clone(): ClientBulkWriteCursor {\n    const clonedOptions = mergeOptions({}, this.clientBulkWriteOptions);\n    delete clonedOptions.session;\n    return new ClientBulkWriteCursor(this.client, this.commandBuilder, {\n      ...clonedOptions\n    });\n  }\n\n  /** @internal */\n  async _initialize(session: ClientSession): Promise<InitialCursorResponse> {\n    const clientBulkWriteOperation = new ClientBulkWriteOperation(this.commandBuilder, {\n      ...this.clientBulkWriteOptions,\n      ...this.cursorOptions,\n      session\n    });\n\n    const response = await executeOperation(\n      this.client,\n      clientBulkWriteOperation,\n      this.timeoutContext\n    );\n    this.cursorResponse = response;\n\n    return { server: clientBulkWriteOperation.server, session, response };\n  }\n}\n"], "mappings": ";;;;;;AAGA,MAAAA,mBAAA,GAAAC,OAAA;AAGA,MAAAC,mBAAA,GAAAD,OAAA;AAEA,MAAAE,OAAA,GAAAF,OAAA;AACA,MAAAG,iBAAA,GAAAH,OAAA;AAWA;;;;;AAKA,MAAaI,qBAAsB,SAAQD,iBAAA,CAAAE,cAAc;EAOvD;EACAC,YACEC,MAAmB,EACnBC,cAA6C,EAC7CC,OAAA,GAAwC,EAAE;IAE1C,KAAK,CAACF,MAAM,EAAE,IAAIL,OAAA,CAAAQ,gBAAgB,CAAC,OAAO,EAAE,MAAM,CAAC,EAAED,OAAO,CAAC;IAE7D,IAAI,CAACD,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACG,sBAAsB,GAAGF,OAAO;EACvC;EAEA;;;;EAIA,IAAIG,QAAQA,CAAA;IACV,IAAI,IAAI,CAACC,cAAc,EAAE,OAAO,IAAI,CAACA,cAAc;IACnD,OAAO,IAAI;EACb;EAEA,IAAIC,UAAUA,CAAA;IACZ,OAAO,IAAI,CAACN,cAAc,CAACO,cAAc;EAC3C;EAEAC,KAAKA,CAAA;IACH,MAAMC,aAAa,GAAG,IAAAf,OAAA,CAAAgB,YAAY,EAAC,EAAE,EAAE,IAAI,CAACP,sBAAsB,CAAC;IACnE,OAAOM,aAAa,CAACE,OAAO;IAC5B,OAAO,IAAIf,qBAAqB,CAAC,IAAI,CAACG,MAAM,EAAE,IAAI,CAACC,cAAc,EAAE;MACjE,GAAGS;KACJ,CAAC;EACJ;EAEA;EACA,MAAMG,WAAWA,CAACD,OAAsB;IACtC,MAAME,wBAAwB,GAAG,IAAItB,mBAAA,CAAAuB,wBAAwB,CAAC,IAAI,CAACd,cAAc,EAAE;MACjF,GAAG,IAAI,CAACG,sBAAsB;MAC9B,GAAG,IAAI,CAACY,aAAa;MACrBJ;KACD,CAAC;IAEF,MAAMP,QAAQ,GAAG,MAAM,IAAAX,mBAAA,CAAAuB,gBAAgB,EACrC,IAAI,CAACjB,MAAM,EACXc,wBAAwB,EACxB,IAAI,CAACI,cAAc,CACpB;IACD,IAAI,CAACZ,cAAc,GAAGD,QAAQ;IAE9B,OAAO;MAAEc,MAAM,EAAEL,wBAAwB,CAACK,MAAM;MAAEP,OAAO;MAAEP;IAAQ,CAAE;EACvE;;AAxDFe,OAAA,CAAAvB,qBAAA,GAAAA,qBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}