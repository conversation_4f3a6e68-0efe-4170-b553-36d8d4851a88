import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';

export interface Animal {
  _id: string;
  tagNumber: string;
  name?: string;
  species: 'cattle' | 'sheep' | 'goat' | 'pig' | 'chicken' | 'horse' | 'other';
  breed: string;
  gender: 'male' | 'female';
  dateOfBirth: string;
  color?: string;
  markings?: string;
  currentWeight?: number;
  birthWeight?: number;
  rfidTag?: string;
  earTagNumber?: string;
  microchipId?: string;
  sire?: string;
  dam?: string;
  status: 'active' | 'sold' | 'deceased' | 'transferred' | 'quarantine';
  location?: {
    paddock?: string;
    barn?: string;
    coordinates?: {
      latitude: number;
      longitude: number;
    };
  };
  healthStatus: 'healthy' | 'sick' | 'injured' | 'recovering' | 'quarantine';
  lastHealthCheck?: string;
  nextHealthCheck?: string;
  breedingStatus: 'available' | 'pregnant' | 'lactating' | 'breeding' | 'retired';
  lastBreedingDate?: string;
  expectedDueDate?: string;
  productionType?: 'meat' | 'milk' | 'eggs' | 'wool' | 'breeding' | 'work' | 'show';
  productionRecords?: Array<{
    date: string;
    type: 'milk' | 'eggs' | 'wool' | 'weight';
    quantity: number;
    unit: string;
    notes?: string;
  }>;
  weightHistory?: Array<{
    date: string;
    weight: number;
    measuredBy: string;
    notes?: string;
  }>;
  purchasePrice?: number;
  purchaseDate?: string;
  currentValue?: number;
  images?: Array<{
    url: string;
    caption?: string;
    uploadDate: string;
  }>;
  documents?: Array<{
    name: string;
    url: string;
    type: string;
    uploadDate: string;
  }>;
  notes?: string;
  isActive: boolean;
  createdBy: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt: string;
  age?: number;
  ageInDays?: number;
  latestWeight?: number;
}

export interface AnimalFilters {
  species?: string;
  breed?: string;
  gender?: string;
  status?: string;
  healthStatus?: string;
  breedingStatus?: string;
  location?: string;
  ageRange?: {
    min?: number;
    max?: number;
  };
  weightRange?: {
    min?: number;
    max?: number;
  };
  search?: string;
}

export interface AnimalState {
  animals: Animal[];
  selectedAnimal: Animal | null;
  filters: AnimalFilters;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
  isLoading: boolean;
  error: string | null;
  statistics: {
    total: number;
    bySpecies: Record<string, number>;
    byStatus: Record<string, number>;
    byHealthStatus: Record<string, number>;
    byBreedingStatus: Record<string, number>;
  } | null;
}

const initialState: AnimalState = {
  animals: [],
  selectedAnimal: null,
  filters: {},
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  },
  isLoading: false,
  error: null,
  statistics: null,
};

// Mock async thunks (replace with actual API calls)
export const fetchAnimals = createAsyncThunk(
  'animals/fetchAnimals',
  async (params: { page?: number; limit?: number; filters?: AnimalFilters }, { rejectWithValue }) => {
    try {
      // Mock API call - replace with actual API
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock data
      const mockAnimals: Animal[] = [
        {
          _id: '1',
          tagNumber: 'C001',
          name: 'Bessie',
          species: 'cattle',
          breed: 'Holstein',
          gender: 'female',
          dateOfBirth: '2022-03-15',
          color: 'Black and White',
          currentWeight: 450,
          birthWeight: 35,
          status: 'active',
          healthStatus: 'healthy',
          breedingStatus: 'available',
          isActive: true,
          createdBy: 'user1',
          createdAt: '2023-01-01',
          updatedAt: '2023-01-01',
          age: 1,
          ageInDays: 365,
          latestWeight: 450,
        },
        {
          _id: '2',
          tagNumber: 'S001',
          name: 'Woolly',
          species: 'sheep',
          breed: 'Merino',
          gender: 'male',
          dateOfBirth: '2023-01-10',
          color: 'White',
          currentWeight: 75,
          birthWeight: 4,
          status: 'active',
          healthStatus: 'healthy',
          breedingStatus: 'available',
          isActive: true,
          createdBy: 'user1',
          createdAt: '2023-01-10',
          updatedAt: '2023-01-10',
          age: 0,
          ageInDays: 180,
          latestWeight: 75,
        },
      ];
      
      return {
        animals: mockAnimals,
        pagination: {
          page: params.page || 1,
          limit: params.limit || 20,
          total: mockAnimals.length,
          totalPages: Math.ceil(mockAnimals.length / (params.limit || 20)),
        },
      };
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch animals');
    }
  }
);

export const fetchAnimalById = createAsyncThunk(
  'animals/fetchAnimalById',
  async (id: string, { rejectWithValue }) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // Mock data
      const mockAnimal: Animal = {
        _id: id,
        tagNumber: 'C001',
        name: 'Bessie',
        species: 'cattle',
        breed: 'Holstein',
        gender: 'female',
        dateOfBirth: '2022-03-15',
        color: 'Black and White',
        currentWeight: 450,
        birthWeight: 35,
        status: 'active',
        healthStatus: 'healthy',
        breedingStatus: 'available',
        isActive: true,
        createdBy: 'user1',
        createdAt: '2023-01-01',
        updatedAt: '2023-01-01',
        age: 1,
        ageInDays: 365,
        latestWeight: 450,
      };
      
      return mockAnimal;
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch animal');
    }
  }
);

export const fetchAnimalStatistics = createAsyncThunk(
  'animals/fetchStatistics',
  async (_, { rejectWithValue }) => {
    try {
      // Mock API call
      await new Promise(resolve => setTimeout(resolve, 500));
      
      return {
        total: 150,
        bySpecies: {
          cattle: 75,
          sheep: 45,
          goat: 20,
          pig: 10,
        },
        byStatus: {
          active: 140,
          sold: 5,
          deceased: 3,
          transferred: 2,
        },
        byHealthStatus: {
          healthy: 135,
          sick: 8,
          injured: 4,
          recovering: 3,
        },
        byBreedingStatus: {
          available: 80,
          pregnant: 25,
          lactating: 30,
          breeding: 10,
          retired: 5,
        },
      };
    } catch (error: any) {
      return rejectWithValue(error.message || 'Failed to fetch statistics');
    }
  }
);

const animalSlice = createSlice({
  name: 'animals',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<AnimalFilters>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {};
    },
    setSelectedAnimal: (state, action: PayloadAction<Animal | null>) => {
      state.selectedAnimal = action.payload;
    },
    setPagination: (state, action: PayloadAction<Partial<typeof initialState.pagination>>) => {
      state.pagination = { ...state.pagination, ...action.payload };
    },
    clearError: (state) => {
      state.error = null;
    },
  },
  extraReducers: (builder) => {
    builder
      // Fetch animals
      .addCase(fetchAnimals.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAnimals.fulfilled, (state, action) => {
        state.isLoading = false;
        state.animals = action.payload.animals;
        state.pagination = action.payload.pagination;
        state.error = null;
      })
      .addCase(fetchAnimals.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Fetch animal by ID
      .addCase(fetchAnimalById.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchAnimalById.fulfilled, (state, action) => {
        state.isLoading = false;
        state.selectedAnimal = action.payload;
        state.error = null;
      })
      .addCase(fetchAnimalById.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string;
      })
      
      // Fetch statistics
      .addCase(fetchAnimalStatistics.fulfilled, (state, action) => {
        state.statistics = action.payload;
      });
  },
});

export const { setFilters, clearFilters, setSelectedAnimal, setPagination, clearError } = animalSlice.actions;
export default animalSlice.reducer;
