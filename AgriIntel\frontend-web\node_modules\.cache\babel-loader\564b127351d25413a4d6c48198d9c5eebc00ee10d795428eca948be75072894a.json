{"ast": null, "code": "function l(r) {\n  let e = {\n    called: !1\n  };\n  return function () {\n    if (!e.called) return e.called = !0, r(...arguments);\n  };\n}\nexport { l as once };", "map": {"version": 3, "names": ["l", "r", "e", "called", "arguments", "once"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/utils/once.js"], "sourcesContent": ["function l(r){let e={called:!1};return(...t)=>{if(!e.called)return e.called=!0,r(...t)}}export{l as once};\n"], "mappings": "AAAA,SAASA,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAAC;IAACC,MAAM,EAAC,CAAC;EAAC,CAAC;EAAC,OAAM,YAAQ;IAAC,IAAG,CAACD,CAAC,CAACC,MAAM,EAAC,OAAOD,CAAC,CAACC,MAAM,GAAC,CAAC,CAAC,EAACF,CAAC,CAAC,GAAAG,SAAI,CAAC;EAAA,CAAC;AAAA;AAAC,SAAOJ,CAAC,IAAIK,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}