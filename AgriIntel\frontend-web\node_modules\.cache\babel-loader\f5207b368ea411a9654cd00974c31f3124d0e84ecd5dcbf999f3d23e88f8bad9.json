{"ast": null, "code": "import React,{useEffect}from'react';import{useDispatch}from'react-redux';import{useTranslation}from'react-i18next';import{setPageTitle,setBreadcrumbs}from'../../store/slices/uiSlice';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AnimalsPage=()=>{const dispatch=useDispatch();const{t}=useTranslation();useEffect(()=>{dispatch(setPageTitle(t('animals.title')));dispatch(setBreadcrumbs([{label:t('navigation.dashboard'),path:'/dashboard'},{label:t('animals.title')}]));},[dispatch,t]);return/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:t('animals.title')})}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Animal management features coming soon...\"})})]})});};export default AnimalsPage;", "map": {"version": 3, "names": ["React", "useEffect", "useDispatch", "useTranslation", "setPageTitle", "setBreadcrumbs", "jsx", "_jsx", "jsxs", "_jsxs", "AnimalsPage", "dispatch", "t", "label", "path", "className", "children"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/pages/animals/AnimalsPage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport { AppDispatch } from '../../store/store';\nimport { setPageTitle, setBreadcrumbs } from '../../store/slices/uiSlice';\n\nconst AnimalsPage: React.FC = () => {\n  const dispatch = useDispatch<AppDispatch>();\n  const { t } = useTranslation();\n\n  useEffect(() => {\n    dispatch(setPageTitle(t('animals.title')));\n    dispatch(setBreadcrumbs([\n      { label: t('navigation.dashboard'), path: '/dashboard' },\n      { label: t('animals.title') }\n    ]));\n  }, [dispatch, t]);\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">\n            {t('animals.title')}\n          </h1>\n        </div>\n        <div className=\"card-body\">\n          <p className=\"text-gray-600\">\n            Animal management features coming soon...\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default AnimalsPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,WAAW,KAAQ,aAAa,CACzC,OAASC,cAAc,KAAQ,eAAe,CAE9C,OAASC,YAAY,CAAEC,cAAc,KAAQ,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE1E,KAAM,CAAAC,WAAqB,CAAGA,CAAA,GAAM,CAClC,KAAM,CAAAC,QAAQ,CAAGT,WAAW,CAAc,CAAC,CAC3C,KAAM,CAAEU,CAAE,CAAC,CAAGT,cAAc,CAAC,CAAC,CAE9BF,SAAS,CAAC,IAAM,CACdU,QAAQ,CAACP,YAAY,CAACQ,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAC1CD,QAAQ,CAACN,cAAc,CAAC,CACtB,CAAEQ,KAAK,CAAED,CAAC,CAAC,sBAAsB,CAAC,CAAEE,IAAI,CAAE,YAAa,CAAC,CACxD,CAAED,KAAK,CAAED,CAAC,CAAC,eAAe,CAAE,CAAC,CAC9B,CAAC,CAAC,CACL,CAAC,CAAE,CAACD,QAAQ,CAAEC,CAAC,CAAC,CAAC,CAEjB,mBACEL,IAAA,QAAKQ,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBP,KAAA,QAAKM,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBT,IAAA,QAAKQ,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BT,IAAA,OAAIQ,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC7CJ,CAAC,CAAC,eAAe,CAAC,CACjB,CAAC,CACF,CAAC,cACNL,IAAA,QAAKQ,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBT,IAAA,MAAGQ,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,2CAE7B,CAAG,CAAC,CACD,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAN,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}