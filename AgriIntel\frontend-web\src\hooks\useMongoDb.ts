// Simple replacement for MongoDB context hook
// This provides a mock implementation to prevent build errors

export const useMongoDb = () => {
  return {
    isConnected: true,
    isLoading: false,
    error: null,
    mongoStats: null,
    syncMockData: async () => true,
    refreshStats: async () => {},
    getStats: async () => {},
    services: {
      breeding: {},
      health: {},
      animals: {}
    }
  };
};
