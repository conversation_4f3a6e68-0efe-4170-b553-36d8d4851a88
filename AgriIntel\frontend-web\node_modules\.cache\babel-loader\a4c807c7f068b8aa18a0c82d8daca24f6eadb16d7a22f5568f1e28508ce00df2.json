{"ast": null, "code": "import { useState as o } from \"react\";\nimport { useIsoMorphicEffect as r } from './use-iso-morphic-effect.js';\nfunction i(t) {\n  var n;\n  if (t.type) return t.type;\n  let e = (n = t.as) != null ? n : \"button\";\n  if (typeof e == \"string\" && e.toLowerCase() === \"button\") return \"button\";\n}\nfunction T(t, e) {\n  let [n, u] = o(() => i(t));\n  return r(() => {\n    u(i(t));\n  }, [t.type, t.as]), r(() => {\n    n || e.current && e.current instanceof HTMLButtonElement && !e.current.hasAttribute(\"type\") && u(\"button\");\n  }, [n, e]), n;\n}\nexport { T as useResolveButtonType };", "map": {"version": 3, "names": ["useState", "o", "useIsoMorphicEffect", "r", "i", "t", "n", "type", "e", "as", "toLowerCase", "T", "u", "current", "HTMLButtonElement", "hasAttribute", "useResolveButtonType"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/hooks/use-resolve-button-type.js"], "sourcesContent": ["import{useState as o}from\"react\";import{useIsoMorphicEffect as r}from'./use-iso-morphic-effect.js';function i(t){var n;if(t.type)return t.type;let e=(n=t.as)!=null?n:\"button\";if(typeof e==\"string\"&&e.toLowerCase()===\"button\")return\"button\"}function T(t,e){let[n,u]=o(()=>i(t));return r(()=>{u(i(t))},[t.type,t.as]),r(()=>{n||e.current&&e.current instanceof HTMLButtonElement&&!e.current.hasAttribute(\"type\")&&u(\"button\")},[n,e]),n}export{T as useResolveButtonType};\n"], "mappings": "AAAA,SAAOA,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAASC,CAACA,CAACC,CAAC,EAAC;EAAC,IAAIC,CAAC;EAAC,IAAGD,CAAC,CAACE,IAAI,EAAC,OAAOF,CAAC,CAACE,IAAI;EAAC,IAAIC,CAAC,GAAC,CAACF,CAAC,GAACD,CAAC,CAACI,EAAE,KAAG,IAAI,GAACH,CAAC,GAAC,QAAQ;EAAC,IAAG,OAAOE,CAAC,IAAE,QAAQ,IAAEA,CAAC,CAACE,WAAW,CAAC,CAAC,KAAG,QAAQ,EAAC,OAAM,QAAQ;AAAA;AAAC,SAASC,CAACA,CAACN,CAAC,EAACG,CAAC,EAAC;EAAC,IAAG,CAACF,CAAC,EAACM,CAAC,CAAC,GAACX,CAAC,CAAC,MAAIG,CAAC,CAACC,CAAC,CAAC,CAAC;EAAC,OAAOF,CAAC,CAAC,MAAI;IAACS,CAAC,CAACR,CAAC,CAACC,CAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAACA,CAAC,CAACE,IAAI,EAACF,CAAC,CAACI,EAAE,CAAC,CAAC,EAACN,CAAC,CAAC,MAAI;IAACG,CAAC,IAAEE,CAAC,CAACK,OAAO,IAAEL,CAAC,CAACK,OAAO,YAAYC,iBAAiB,IAAE,CAACN,CAAC,CAACK,OAAO,CAACE,YAAY,CAAC,MAAM,CAAC,IAAEH,CAAC,CAAC,QAAQ,CAAC;EAAA,CAAC,EAAC,CAACN,CAAC,EAACE,CAAC,CAAC,CAAC,EAACF,CAAC;AAAA;AAAC,SAAOK,CAAC,IAAIK,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}