{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.TokenMachineWorkflow = void 0;\nconst fs = require(\"fs\");\nconst error_1 = require(\"../../../error\");\nconst machine_workflow_1 = require(\"./machine_workflow\");\n/** Error for when the token is missing in the environment. */\nconst TOKEN_MISSING_ERROR = 'OIDC_TOKEN_FILE must be set in the environment.';\n/**\n * Device workflow implementation for AWS.\n *\n * @internal\n */\nclass TokenMachineWorkflow extends machine_workflow_1.MachineWorkflow {\n  /**\n   * Instantiate the machine workflow.\n   */\n  constructor(cache) {\n    super(cache);\n  }\n  /**\n   * Get the token from the environment.\n   */\n  async getToken() {\n    const tokenFile = process.env.OIDC_TOKEN_FILE;\n    if (!tokenFile) {\n      throw new error_1.MongoAWSError(TOKEN_MISSING_ERROR);\n    }\n    const token = await fs.promises.readFile(tokenFile, 'utf8');\n    return {\n      access_token: token\n    };\n  }\n}\nexports.TokenMachineWorkflow = TokenMachineWorkflow;", "map": {"version": 3, "names": ["fs", "require", "error_1", "machine_workflow_1", "TOKEN_MISSING_ERROR", "TokenMachineWorkflow", "MachineWorkflow", "constructor", "cache", "getToken", "tokenFile", "process", "env", "OIDC_TOKEN_FILE", "MongoAWSError", "token", "promises", "readFile", "access_token", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\auth\\mongodb_oidc\\token_machine_workflow.ts"], "sourcesContent": ["import * as fs from 'fs';\n\nimport { MongoAWSError } from '../../../error';\nimport { type AccessToken, MachineWorkflow } from './machine_workflow';\nimport { type TokenCache } from './token_cache';\n\n/** Error for when the token is missing in the environment. */\nconst TOKEN_MISSING_ERROR = 'OIDC_TOKEN_FILE must be set in the environment.';\n\n/**\n * Device workflow implementation for AWS.\n *\n * @internal\n */\nexport class TokenMachineWorkflow extends MachineWorkflow {\n  /**\n   * Instantiate the machine workflow.\n   */\n  constructor(cache: TokenCache) {\n    super(cache);\n  }\n\n  /**\n   * Get the token from the environment.\n   */\n  async getToken(): Promise<AccessToken> {\n    const tokenFile = process.env.OIDC_TOKEN_FILE;\n    if (!tokenFile) {\n      throw new MongoAWSError(TOKEN_MISSING_ERROR);\n    }\n    const token = await fs.promises.readFile(tokenFile, 'utf8');\n    return { access_token: token };\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,EAAA,GAAAC,OAAA;AAEA,MAAAC,OAAA,GAAAD,OAAA;AACA,MAAAE,kBAAA,GAAAF,OAAA;AAGA;AACA,MAAMG,mBAAmB,GAAG,iDAAiD;AAE7E;;;;;AAKA,MAAaC,oBAAqB,SAAQF,kBAAA,CAAAG,eAAe;EACvD;;;EAGAC,YAAYC,KAAiB;IAC3B,KAAK,CAACA,KAAK,CAAC;EACd;EAEA;;;EAGA,MAAMC,QAAQA,CAAA;IACZ,MAAMC,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,eAAe;IAC7C,IAAI,CAACH,SAAS,EAAE;MACd,MAAM,IAAIR,OAAA,CAAAY,aAAa,CAACV,mBAAmB,CAAC;IAC9C;IACA,MAAMW,KAAK,GAAG,MAAMf,EAAE,CAACgB,QAAQ,CAACC,QAAQ,CAACP,SAAS,EAAE,MAAM,CAAC;IAC3D,OAAO;MAAEQ,YAAY,EAAEH;IAAK,CAAE;EAChC;;AAlBFI,OAAA,CAAAd,oBAAA,GAAAA,oBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}