{"ast": null, "code": "export { default } from './useMediaQuery';\nexport * from './useMediaQuery';", "map": {"version": 3, "names": ["default"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/system/esm/useMediaQuery/index.js"], "sourcesContent": ["export { default } from './useMediaQuery';\nexport * from './useMediaQuery';"], "mappings": "AAAA,SAASA,OAAO,QAAQ,iBAAiB;AACzC,cAAc,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}