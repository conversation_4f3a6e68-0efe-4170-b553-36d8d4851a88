{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.CountOperation = void 0;\nconst command_1 = require(\"./command\");\nconst operation_1 = require(\"./operation\");\n/** @internal */\nclass CountOperation extends command_1.CommandOperation {\n  constructor(namespace, filter, options) {\n    super({\n      s: {\n        namespace: namespace\n      }\n    }, options);\n    this.options = options;\n    this.collectionName = namespace.collection;\n    this.query = filter;\n  }\n  get commandName() {\n    return 'count';\n  }\n  async execute(server, session, timeoutContext) {\n    const options = this.options;\n    const cmd = {\n      count: this.collectionName,\n      query: this.query\n    };\n    if (typeof options.limit === 'number') {\n      cmd.limit = options.limit;\n    }\n    if (typeof options.skip === 'number') {\n      cmd.skip = options.skip;\n    }\n    if (options.hint != null) {\n      cmd.hint = options.hint;\n    }\n    if (typeof options.maxTimeMS === 'number') {\n      cmd.maxTimeMS = options.maxTimeMS;\n    }\n    const result = await super.executeCommand(server, session, cmd, timeoutContext);\n    return result ? result.n : 0;\n  }\n}\nexports.CountOperation = CountOperation;\n(0, operation_1.defineAspects)(CountOperation, [operation_1.Aspect.READ_OPERATION, operation_1.Aspect.RETRYABLE]);", "map": {"version": 3, "names": ["command_1", "require", "operation_1", "CountOperation", "CommandOperation", "constructor", "namespace", "filter", "options", "s", "collectionName", "collection", "query", "commandName", "execute", "server", "session", "timeoutContext", "cmd", "count", "limit", "skip", "hint", "maxTimeMS", "result", "executeCommand", "n", "exports", "defineAspects", "Aspect", "READ_OPERATION", "RETRYABLE"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\count.ts"], "sourcesContent": ["import type { Document } from '../bson';\nimport type { Collection } from '../collection';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport type { MongoDBNamespace } from '../utils';\nimport { CommandOperation, type CommandOperationOptions } from './command';\nimport { Aspect, defineAspects } from './operation';\n\n/** @public */\nexport interface CountOptions extends CommandOperationOptions {\n  /** The number of documents to skip. */\n  skip?: number;\n  /** The maximum amounts to count before aborting. */\n  limit?: number;\n  /**\n   * Number of milliseconds to wait before aborting the query.\n   */\n  maxTimeMS?: number;\n  /** An index name hint for the query. */\n  hint?: string | Document;\n}\n\n/** @internal */\nexport class CountOperation extends CommandOperation<number> {\n  override options: CountOptions;\n  collectionName?: string;\n  query: Document;\n\n  constructor(namespace: MongoDBNamespace, filter: Document, options: CountOptions) {\n    super({ s: { namespace: namespace } } as unknown as Collection, options);\n\n    this.options = options;\n    this.collectionName = namespace.collection;\n    this.query = filter;\n  }\n\n  override get commandName() {\n    return 'count' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<number> {\n    const options = this.options;\n    const cmd: Document = {\n      count: this.collectionName,\n      query: this.query\n    };\n\n    if (typeof options.limit === 'number') {\n      cmd.limit = options.limit;\n    }\n\n    if (typeof options.skip === 'number') {\n      cmd.skip = options.skip;\n    }\n\n    if (options.hint != null) {\n      cmd.hint = options.hint;\n    }\n\n    if (typeof options.maxTimeMS === 'number') {\n      cmd.maxTimeMS = options.maxTimeMS;\n    }\n\n    const result = await super.executeCommand(server, session, cmd, timeoutContext);\n    return result ? result.n : 0;\n  }\n}\n\ndefineAspects(CountOperation, [Aspect.READ_OPERATION, Aspect.RETRYABLE]);\n"], "mappings": ";;;;;;AAMA,MAAAA,SAAA,GAAAC,OAAA;AACA,MAAAC,WAAA,GAAAD,OAAA;AAgBA;AACA,MAAaE,cAAe,SAAQH,SAAA,CAAAI,gBAAwB;EAK1DC,YAAYC,SAA2B,EAAEC,MAAgB,EAAEC,OAAqB;IAC9E,KAAK,CAAC;MAAEC,CAAC,EAAE;QAAEH,SAAS,EAAEA;MAAS;IAAE,CAA2B,EAAEE,OAAO,CAAC;IAExE,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,cAAc,GAAGJ,SAAS,CAACK,UAAU;IAC1C,IAAI,CAACC,KAAK,GAAGL,MAAM;EACrB;EAEA,IAAaM,WAAWA,CAAA;IACtB,OAAO,OAAgB;EACzB;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMT,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAMU,GAAG,GAAa;MACpBC,KAAK,EAAE,IAAI,CAACT,cAAc;MAC1BE,KAAK,EAAE,IAAI,CAACA;KACb;IAED,IAAI,OAAOJ,OAAO,CAACY,KAAK,KAAK,QAAQ,EAAE;MACrCF,GAAG,CAACE,KAAK,GAAGZ,OAAO,CAACY,KAAK;IAC3B;IAEA,IAAI,OAAOZ,OAAO,CAACa,IAAI,KAAK,QAAQ,EAAE;MACpCH,GAAG,CAACG,IAAI,GAAGb,OAAO,CAACa,IAAI;IACzB;IAEA,IAAIb,OAAO,CAACc,IAAI,IAAI,IAAI,EAAE;MACxBJ,GAAG,CAACI,IAAI,GAAGd,OAAO,CAACc,IAAI;IACzB;IAEA,IAAI,OAAOd,OAAO,CAACe,SAAS,KAAK,QAAQ,EAAE;MACzCL,GAAG,CAACK,SAAS,GAAGf,OAAO,CAACe,SAAS;IACnC;IAEA,MAAMC,MAAM,GAAG,MAAM,KAAK,CAACC,cAAc,CAACV,MAAM,EAAEC,OAAO,EAAEE,GAAG,EAAED,cAAc,CAAC;IAC/E,OAAOO,MAAM,GAAGA,MAAM,CAACE,CAAC,GAAG,CAAC;EAC9B;;AA9CFC,OAAA,CAAAxB,cAAA,GAAAA,cAAA;AAiDA,IAAAD,WAAA,CAAA0B,aAAa,EAACzB,cAAc,EAAE,CAACD,WAAA,CAAA2B,MAAM,CAACC,cAAc,EAAE5B,WAAA,CAAA2B,MAAM,CAACE,SAAS,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}