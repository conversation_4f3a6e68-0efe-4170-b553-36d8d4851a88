{"ast": null, "code": "import { isLeaf } from '../../../models/gridColumnGrouping';\nimport { isDeepEqual } from '../../../utils/utils';\n// This is the recurrence function that help writing `unwrapGroupingColumnModel()`\nconst recurrentUnwrapGroupingColumnModel = (columnGroupNode, parents, unwrappedGroupingModelToComplete) => {\n  if (isLeaf(columnGroupNode)) {\n    if (unwrappedGroupingModelToComplete[columnGroupNode.field] !== undefined) {\n      throw new Error([`MUI: columnGroupingModel contains duplicated field`, `column field ${columnGroupNode.field} occurs two times in the grouping model:`, `- ${unwrappedGroupingModelToComplete[columnGroupNode.field].join(' > ')}`, `- ${parents.join(' > ')}`].join('\\n'));\n    }\n    unwrappedGroupingModelToComplete[columnGroupNode.field] = parents;\n    return;\n  }\n  const {\n    groupId,\n    children\n  } = columnGroupNode;\n  children.forEach(child => {\n    recurrentUnwrapGroupingColumnModel(child, [...parents, groupId], unwrappedGroupingModelToComplete);\n  });\n};\n\n/**\n * This is a function that provide for each column the array of its parents.\n * Parents are ordered from the root to the leaf.\n * @param columnGroupingModel The model such as provided in DataGrid props\n * @returns An object `{[field]: groupIds}` where `groupIds` is the parents of the column `field`\n */\nexport const unwrapGroupingColumnModel = columnGroupingModel => {\n  if (!columnGroupingModel) {\n    return {};\n  }\n  const unwrappedSubTree = {};\n  columnGroupingModel.forEach(columnGroupNode => {\n    recurrentUnwrapGroupingColumnModel(columnGroupNode, [], unwrappedSubTree);\n  });\n  return unwrappedSubTree;\n};\nexport const getColumnGroupsHeaderStructure = (orderedColumns, unwrappedGroupingModel, pinnedFields) => {\n  const getParents = field => {\n    var _unwrappedGroupingMod;\n    return (_unwrappedGroupingMod = unwrappedGroupingModel[field]) != null ? _unwrappedGroupingMod : [];\n  };\n  const groupingHeaderStructure = [];\n  const maxDepth = Math.max(...orderedColumns.map(field => getParents(field).length));\n  const haveSameParents = (field1, field2, depth) => isDeepEqual(getParents(field1).slice(0, depth + 1), getParents(field2).slice(0, depth + 1));\n  const haveDifferentContainers = (field1, field2) => {\n    if (pinnedFields != null && pinnedFields.left && pinnedFields.left.includes(field1) && !pinnedFields.left.includes(field2)) {\n      return true;\n    }\n    if (pinnedFields != null && pinnedFields.right && !pinnedFields.right.includes(field1) && pinnedFields.right.includes(field2)) {\n      return true;\n    }\n    return false;\n  };\n  for (let depth = 0; depth < maxDepth; depth += 1) {\n    const depthStructure = orderedColumns.reduce((structure, newField) => {\n      var _getParents$depth;\n      const groupId = (_getParents$depth = getParents(newField)[depth]) != null ? _getParents$depth : null;\n      if (structure.length === 0) {\n        return [{\n          columnFields: [newField],\n          groupId\n        }];\n      }\n      const lastGroup = structure[structure.length - 1];\n      const prevField = lastGroup.columnFields[lastGroup.columnFields.length - 1];\n      const prevGroupId = lastGroup.groupId;\n      if (prevGroupId !== groupId || !haveSameParents(prevField, newField, depth) ||\n      // Fix for https://github.com/mui/mui-x/issues/7041\n      haveDifferentContainers(prevField, newField)) {\n        // It's a new group\n        return [...structure, {\n          columnFields: [newField],\n          groupId\n        }];\n      }\n\n      // It extends the previous group\n      return [...structure.slice(0, structure.length - 1), {\n        columnFields: [...lastGroup.columnFields, newField],\n        groupId\n      }];\n    }, []);\n    groupingHeaderStructure.push(depthStructure);\n  }\n  return groupingHeaderStructure;\n};", "map": {"version": 3, "names": ["<PERSON><PERSON><PERSON><PERSON>", "isDeepEqual", "recurrentUnwrapGroupingColumnModel", "columnGroupNode", "parents", "unwrappedGroupingModelToComplete", "field", "undefined", "Error", "join", "groupId", "children", "for<PERSON>ach", "child", "unwrapGroupingColumnModel", "columnGroupingModel", "unwrappedSubTree", "getColumnGroupsHeaderStructure", "orderedColumns", "unwrappedGroupingModel", "pinnedFields", "getParents", "_unwrappedGroupingMod", "groupingHeaderStructure", "max<PERSON><PERSON><PERSON>", "Math", "max", "map", "length", "haveSameParents", "field1", "field2", "depth", "slice", "haveDifferentContainers", "left", "includes", "right", "depthStructure", "reduce", "structure", "newField", "_getParents$depth", "columnFields", "lastGroup", "prevField", "prevGroupId", "push"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/x-data-grid/hooks/features/columnGrouping/gridColumnGroupsUtils.js"], "sourcesContent": ["import { isLeaf } from '../../../models/gridColumnGrouping';\nimport { isDeepEqual } from '../../../utils/utils';\n// This is the recurrence function that help writing `unwrapGroupingColumnModel()`\nconst recurrentUnwrapGroupingColumnModel = (columnGroupNode, parents, unwrappedGroupingModelToComplete) => {\n  if (isLeaf(columnGroupNode)) {\n    if (unwrappedGroupingModelToComplete[columnGroupNode.field] !== undefined) {\n      throw new Error([`MUI: columnGroupingModel contains duplicated field`, `column field ${columnGroupNode.field} occurs two times in the grouping model:`, `- ${unwrappedGroupingModelToComplete[columnGroupNode.field].join(' > ')}`, `- ${parents.join(' > ')}`].join('\\n'));\n    }\n    unwrappedGroupingModelToComplete[columnGroupNode.field] = parents;\n    return;\n  }\n  const {\n    groupId,\n    children\n  } = columnGroupNode;\n  children.forEach(child => {\n    recurrentUnwrapGroupingColumnModel(child, [...parents, groupId], unwrappedGroupingModelToComplete);\n  });\n};\n\n/**\n * This is a function that provide for each column the array of its parents.\n * Parents are ordered from the root to the leaf.\n * @param columnGroupingModel The model such as provided in DataGrid props\n * @returns An object `{[field]: groupIds}` where `groupIds` is the parents of the column `field`\n */\nexport const unwrapGroupingColumnModel = columnGroupingModel => {\n  if (!columnGroupingModel) {\n    return {};\n  }\n  const unwrappedSubTree = {};\n  columnGroupingModel.forEach(columnGroupNode => {\n    recurrentUnwrapGroupingColumnModel(columnGroupNode, [], unwrappedSubTree);\n  });\n  return unwrappedSubTree;\n};\nexport const getColumnGroupsHeaderStructure = (orderedColumns, unwrappedGroupingModel, pinnedFields) => {\n  const getParents = field => {\n    var _unwrappedGroupingMod;\n    return (_unwrappedGroupingMod = unwrappedGroupingModel[field]) != null ? _unwrappedGroupingMod : [];\n  };\n  const groupingHeaderStructure = [];\n  const maxDepth = Math.max(...orderedColumns.map(field => getParents(field).length));\n  const haveSameParents = (field1, field2, depth) => isDeepEqual(getParents(field1).slice(0, depth + 1), getParents(field2).slice(0, depth + 1));\n  const haveDifferentContainers = (field1, field2) => {\n    if (pinnedFields != null && pinnedFields.left && pinnedFields.left.includes(field1) && !pinnedFields.left.includes(field2)) {\n      return true;\n    }\n    if (pinnedFields != null && pinnedFields.right && !pinnedFields.right.includes(field1) && pinnedFields.right.includes(field2)) {\n      return true;\n    }\n    return false;\n  };\n  for (let depth = 0; depth < maxDepth; depth += 1) {\n    const depthStructure = orderedColumns.reduce((structure, newField) => {\n      var _getParents$depth;\n      const groupId = (_getParents$depth = getParents(newField)[depth]) != null ? _getParents$depth : null;\n      if (structure.length === 0) {\n        return [{\n          columnFields: [newField],\n          groupId\n        }];\n      }\n      const lastGroup = structure[structure.length - 1];\n      const prevField = lastGroup.columnFields[lastGroup.columnFields.length - 1];\n      const prevGroupId = lastGroup.groupId;\n      if (prevGroupId !== groupId || !haveSameParents(prevField, newField, depth) ||\n      // Fix for https://github.com/mui/mui-x/issues/7041\n      haveDifferentContainers(prevField, newField)) {\n        // It's a new group\n        return [...structure, {\n          columnFields: [newField],\n          groupId\n        }];\n      }\n\n      // It extends the previous group\n      return [...structure.slice(0, structure.length - 1), {\n        columnFields: [...lastGroup.columnFields, newField],\n        groupId\n      }];\n    }, []);\n    groupingHeaderStructure.push(depthStructure);\n  }\n  return groupingHeaderStructure;\n};"], "mappings": "AAAA,SAASA,MAAM,QAAQ,oCAAoC;AAC3D,SAASC,WAAW,QAAQ,sBAAsB;AAClD;AACA,MAAMC,kCAAkC,GAAGA,CAACC,eAAe,EAAEC,OAAO,EAAEC,gCAAgC,KAAK;EACzG,IAAIL,MAAM,CAACG,eAAe,CAAC,EAAE;IAC3B,IAAIE,gCAAgC,CAACF,eAAe,CAACG,KAAK,CAAC,KAAKC,SAAS,EAAE;MACzE,MAAM,IAAIC,KAAK,CAAC,CAAC,oDAAoD,EAAE,gBAAgBL,eAAe,CAACG,KAAK,0CAA0C,EAAE,KAAKD,gCAAgC,CAACF,eAAe,CAACG,KAAK,CAAC,CAACG,IAAI,CAAC,KAAK,CAAC,EAAE,EAAE,KAAKL,OAAO,CAACK,IAAI,CAAC,KAAK,CAAC,EAAE,CAAC,CAACA,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7Q;IACAJ,gCAAgC,CAACF,eAAe,CAACG,KAAK,CAAC,GAAGF,OAAO;IACjE;EACF;EACA,MAAM;IACJM,OAAO;IACPC;EACF,CAAC,GAAGR,eAAe;EACnBQ,QAAQ,CAACC,OAAO,CAACC,KAAK,IAAI;IACxBX,kCAAkC,CAACW,KAAK,EAAE,CAAC,GAAGT,OAAO,EAAEM,OAAO,CAAC,EAAEL,gCAAgC,CAAC;EACpG,CAAC,CAAC;AACJ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMS,yBAAyB,GAAGC,mBAAmB,IAAI;EAC9D,IAAI,CAACA,mBAAmB,EAAE;IACxB,OAAO,CAAC,CAAC;EACX;EACA,MAAMC,gBAAgB,GAAG,CAAC,CAAC;EAC3BD,mBAAmB,CAACH,OAAO,CAACT,eAAe,IAAI;IAC7CD,kCAAkC,CAACC,eAAe,EAAE,EAAE,EAAEa,gBAAgB,CAAC;EAC3E,CAAC,CAAC;EACF,OAAOA,gBAAgB;AACzB,CAAC;AACD,OAAO,MAAMC,8BAA8B,GAAGA,CAACC,cAAc,EAAEC,sBAAsB,EAAEC,YAAY,KAAK;EACtG,MAAMC,UAAU,GAAGf,KAAK,IAAI;IAC1B,IAAIgB,qBAAqB;IACzB,OAAO,CAACA,qBAAqB,GAAGH,sBAAsB,CAACb,KAAK,CAAC,KAAK,IAAI,GAAGgB,qBAAqB,GAAG,EAAE;EACrG,CAAC;EACD,MAAMC,uBAAuB,GAAG,EAAE;EAClC,MAAMC,QAAQ,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAGR,cAAc,CAACS,GAAG,CAACrB,KAAK,IAAIe,UAAU,CAACf,KAAK,CAAC,CAACsB,MAAM,CAAC,CAAC;EACnF,MAAMC,eAAe,GAAGA,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,KAAK/B,WAAW,CAACoB,UAAU,CAACS,MAAM,CAAC,CAACG,KAAK,CAAC,CAAC,EAAED,KAAK,GAAG,CAAC,CAAC,EAAEX,UAAU,CAACU,MAAM,CAAC,CAACE,KAAK,CAAC,CAAC,EAAED,KAAK,GAAG,CAAC,CAAC,CAAC;EAC9I,MAAME,uBAAuB,GAAGA,CAACJ,MAAM,EAAEC,MAAM,KAAK;IAClD,IAAIX,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACe,IAAI,IAAIf,YAAY,CAACe,IAAI,CAACC,QAAQ,CAACN,MAAM,CAAC,IAAI,CAACV,YAAY,CAACe,IAAI,CAACC,QAAQ,CAACL,MAAM,CAAC,EAAE;MAC1H,OAAO,IAAI;IACb;IACA,IAAIX,YAAY,IAAI,IAAI,IAAIA,YAAY,CAACiB,KAAK,IAAI,CAACjB,YAAY,CAACiB,KAAK,CAACD,QAAQ,CAACN,MAAM,CAAC,IAAIV,YAAY,CAACiB,KAAK,CAACD,QAAQ,CAACL,MAAM,CAAC,EAAE;MAC7H,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd,CAAC;EACD,KAAK,IAAIC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGR,QAAQ,EAAEQ,KAAK,IAAI,CAAC,EAAE;IAChD,MAAMM,cAAc,GAAGpB,cAAc,CAACqB,MAAM,CAAC,CAACC,SAAS,EAAEC,QAAQ,KAAK;MACpE,IAAIC,iBAAiB;MACrB,MAAMhC,OAAO,GAAG,CAACgC,iBAAiB,GAAGrB,UAAU,CAACoB,QAAQ,CAAC,CAACT,KAAK,CAAC,KAAK,IAAI,GAAGU,iBAAiB,GAAG,IAAI;MACpG,IAAIF,SAAS,CAACZ,MAAM,KAAK,CAAC,EAAE;QAC1B,OAAO,CAAC;UACNe,YAAY,EAAE,CAACF,QAAQ,CAAC;UACxB/B;QACF,CAAC,CAAC;MACJ;MACA,MAAMkC,SAAS,GAAGJ,SAAS,CAACA,SAAS,CAACZ,MAAM,GAAG,CAAC,CAAC;MACjD,MAAMiB,SAAS,GAAGD,SAAS,CAACD,YAAY,CAACC,SAAS,CAACD,YAAY,CAACf,MAAM,GAAG,CAAC,CAAC;MAC3E,MAAMkB,WAAW,GAAGF,SAAS,CAAClC,OAAO;MACrC,IAAIoC,WAAW,KAAKpC,OAAO,IAAI,CAACmB,eAAe,CAACgB,SAAS,EAAEJ,QAAQ,EAAET,KAAK,CAAC;MAC3E;MACAE,uBAAuB,CAACW,SAAS,EAAEJ,QAAQ,CAAC,EAAE;QAC5C;QACA,OAAO,CAAC,GAAGD,SAAS,EAAE;UACpBG,YAAY,EAAE,CAACF,QAAQ,CAAC;UACxB/B;QACF,CAAC,CAAC;MACJ;;MAEA;MACA,OAAO,CAAC,GAAG8B,SAAS,CAACP,KAAK,CAAC,CAAC,EAAEO,SAAS,CAACZ,MAAM,GAAG,CAAC,CAAC,EAAE;QACnDe,YAAY,EAAE,CAAC,GAAGC,SAAS,CAACD,YAAY,EAAEF,QAAQ,CAAC;QACnD/B;MACF,CAAC,CAAC;IACJ,CAAC,EAAE,EAAE,CAAC;IACNa,uBAAuB,CAACwB,IAAI,CAACT,cAAc,CAAC;EAC9C;EACA,OAAOf,uBAAuB;AAChC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}