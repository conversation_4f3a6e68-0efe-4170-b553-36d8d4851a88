{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.AutomatedCallbackWorkflow = void 0;\nconst error_1 = require(\"../../../error\");\nconst timeout_1 = require(\"../../../timeout\");\nconst mongodb_oidc_1 = require(\"../mongodb_oidc\");\nconst callback_workflow_1 = require(\"./callback_workflow\");\n/**\n * Class implementing behaviour for the non human callback workflow.\n * @internal\n */\nclass AutomatedCallbackWorkflow extends callback_workflow_1.CallbackWorkflow {\n  /**\n   * Instantiate the human callback workflow.\n   */\n  constructor(cache, callback) {\n    super(cache, callback);\n  }\n  /**\n   * Execute the OIDC callback workflow.\n   */\n  async execute(connection, credentials) {\n    // If there is a cached access token, try to authenticate with it. If\n    // authentication fails with an Authentication error (18),\n    // invalidate the access token, fetch a new access token, and try\n    // to authenticate again.\n    // If the server fails for any other reason, do not clear the cache.\n    if (this.cache.hasAccessToken) {\n      const token = this.cache.getAccessToken();\n      try {\n        return await this.finishAuthentication(connection, credentials, token);\n      } catch (error) {\n        if (error instanceof error_1.MongoError && error.code === error_1.MONGODB_ERROR_CODES.AuthenticationFailed) {\n          this.cache.removeAccessToken();\n          return await this.execute(connection, credentials);\n        } else {\n          throw error;\n        }\n      }\n    }\n    const response = await this.fetchAccessToken(credentials);\n    this.cache.put(response);\n    connection.accessToken = response.accessToken;\n    await this.finishAuthentication(connection, credentials, response.accessToken);\n  }\n  /**\n   * Fetches the access token using the callback.\n   */\n  async fetchAccessToken(credentials) {\n    const controller = new AbortController();\n    const params = {\n      timeoutContext: controller.signal,\n      version: mongodb_oidc_1.OIDC_VERSION\n    };\n    if (credentials.username) {\n      params.username = credentials.username;\n    }\n    const timeout = timeout_1.Timeout.expires(callback_workflow_1.AUTOMATED_TIMEOUT_MS);\n    try {\n      return await Promise.race([this.executeAndValidateCallback(params), timeout]);\n    } catch (error) {\n      if (timeout_1.TimeoutError.is(error)) {\n        controller.abort();\n        throw new error_1.MongoOIDCError(`OIDC callback timed out after ${callback_workflow_1.AUTOMATED_TIMEOUT_MS}ms.`);\n      }\n      throw error;\n    } finally {\n      timeout.clear();\n    }\n  }\n}\nexports.AutomatedCallbackWorkflow = AutomatedCallbackWorkflow;", "map": {"version": 3, "names": ["error_1", "require", "timeout_1", "mongodb_oidc_1", "callback_workflow_1", "AutomatedCallbackWorkflow", "CallbackWorkflow", "constructor", "cache", "callback", "execute", "connection", "credentials", "hasAccessToken", "token", "getAccessToken", "finishAuthentication", "error", "MongoError", "code", "MONGODB_ERROR_CODES", "AuthenticationFailed", "removeAccessToken", "response", "fetchAccessToken", "put", "accessToken", "controller", "AbortController", "params", "timeoutContext", "signal", "version", "OIDC_VERSION", "username", "timeout", "Timeout", "expires", "AUTOMATED_TIMEOUT_MS", "Promise", "race", "executeAndValidateCallback", "TimeoutError", "is", "abort", "MongoOIDCError", "clear", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\auth\\mongodb_oidc\\automated_callback_workflow.ts"], "sourcesContent": ["import { MONGODB_ERROR_CODES, MongoError, MongoOIDCError } from '../../../error';\nimport { Timeout, TimeoutError } from '../../../timeout';\nimport { type Connection } from '../../connection';\nimport { type MongoCredentials } from '../mongo_credentials';\nimport {\n  OIDC_VERSION,\n  type OIDCCallbackFunction,\n  type OIDCCallbackParams,\n  type OIDCResponse\n} from '../mongodb_oidc';\nimport { AUTOMATED_TIMEOUT_MS, CallbackWorkflow } from './callback_workflow';\nimport { type TokenCache } from './token_cache';\n\n/**\n * Class implementing behaviour for the non human callback workflow.\n * @internal\n */\nexport class AutomatedCallbackWorkflow extends CallbackWorkflow {\n  /**\n   * Instantiate the human callback workflow.\n   */\n  constructor(cache: TokenCache, callback: OIDCCallbackFunction) {\n    super(cache, callback);\n  }\n\n  /**\n   * Execute the OIDC callback workflow.\n   */\n  async execute(connection: Connection, credentials: MongoCredentials): Promise<void> {\n    // If there is a cached access token, try to authenticate with it. If\n    // authentication fails with an Authentication error (18),\n    // invalidate the access token, fetch a new access token, and try\n    // to authenticate again.\n    // If the server fails for any other reason, do not clear the cache.\n    if (this.cache.hasAccessToken) {\n      const token = this.cache.getAccessToken();\n      try {\n        return await this.finishAuthentication(connection, credentials, token);\n      } catch (error) {\n        if (\n          error instanceof MongoError &&\n          error.code === MONGODB_ERROR_CODES.AuthenticationFailed\n        ) {\n          this.cache.removeAccessToken();\n          return await this.execute(connection, credentials);\n        } else {\n          throw error;\n        }\n      }\n    }\n    const response = await this.fetchAccessToken(credentials);\n    this.cache.put(response);\n    connection.accessToken = response.accessToken;\n    await this.finishAuthentication(connection, credentials, response.accessToken);\n  }\n\n  /**\n   * Fetches the access token using the callback.\n   */\n  protected async fetchAccessToken(credentials: MongoCredentials): Promise<OIDCResponse> {\n    const controller = new AbortController();\n    const params: OIDCCallbackParams = {\n      timeoutContext: controller.signal,\n      version: OIDC_VERSION\n    };\n    if (credentials.username) {\n      params.username = credentials.username;\n    }\n    const timeout = Timeout.expires(AUTOMATED_TIMEOUT_MS);\n    try {\n      return await Promise.race([this.executeAndValidateCallback(params), timeout]);\n    } catch (error) {\n      if (TimeoutError.is(error)) {\n        controller.abort();\n        throw new MongoOIDCError(`OIDC callback timed out after ${AUTOMATED_TIMEOUT_MS}ms.`);\n      }\n      throw error;\n    } finally {\n      timeout.clear();\n    }\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,OAAA,GAAAC,OAAA;AACA,MAAAC,SAAA,GAAAD,OAAA;AAGA,MAAAE,cAAA,GAAAF,OAAA;AAMA,MAAAG,mBAAA,GAAAH,OAAA;AAGA;;;;AAIA,MAAaI,yBAA0B,SAAQD,mBAAA,CAAAE,gBAAgB;EAC7D;;;EAGAC,YAAYC,KAAiB,EAAEC,QAA8B;IAC3D,KAAK,CAACD,KAAK,EAAEC,QAAQ,CAAC;EACxB;EAEA;;;EAGA,MAAMC,OAAOA,CAACC,UAAsB,EAAEC,WAA6B;IACjE;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACJ,KAAK,CAACK,cAAc,EAAE;MAC7B,MAAMC,KAAK,GAAG,IAAI,CAACN,KAAK,CAACO,cAAc,EAAE;MACzC,IAAI;QACF,OAAO,MAAM,IAAI,CAACC,oBAAoB,CAACL,UAAU,EAAEC,WAAW,EAAEE,KAAK,CAAC;MACxE,CAAC,CAAC,OAAOG,KAAK,EAAE;QACd,IACEA,KAAK,YAAYjB,OAAA,CAAAkB,UAAU,IAC3BD,KAAK,CAACE,IAAI,KAAKnB,OAAA,CAAAoB,mBAAmB,CAACC,oBAAoB,EACvD;UACA,IAAI,CAACb,KAAK,CAACc,iBAAiB,EAAE;UAC9B,OAAO,MAAM,IAAI,CAACZ,OAAO,CAACC,UAAU,EAAEC,WAAW,CAAC;QACpD,CAAC,MAAM;UACL,MAAMK,KAAK;QACb;MACF;IACF;IACA,MAAMM,QAAQ,GAAG,MAAM,IAAI,CAACC,gBAAgB,CAACZ,WAAW,CAAC;IACzD,IAAI,CAACJ,KAAK,CAACiB,GAAG,CAACF,QAAQ,CAAC;IACxBZ,UAAU,CAACe,WAAW,GAAGH,QAAQ,CAACG,WAAW;IAC7C,MAAM,IAAI,CAACV,oBAAoB,CAACL,UAAU,EAAEC,WAAW,EAAEW,QAAQ,CAACG,WAAW,CAAC;EAChF;EAEA;;;EAGU,MAAMF,gBAAgBA,CAACZ,WAA6B;IAC5D,MAAMe,UAAU,GAAG,IAAIC,eAAe,EAAE;IACxC,MAAMC,MAAM,GAAuB;MACjCC,cAAc,EAAEH,UAAU,CAACI,MAAM;MACjCC,OAAO,EAAE7B,cAAA,CAAA8B;KACV;IACD,IAAIrB,WAAW,CAACsB,QAAQ,EAAE;MACxBL,MAAM,CAACK,QAAQ,GAAGtB,WAAW,CAACsB,QAAQ;IACxC;IACA,MAAMC,OAAO,GAAGjC,SAAA,CAAAkC,OAAO,CAACC,OAAO,CAACjC,mBAAA,CAAAkC,oBAAoB,CAAC;IACrD,IAAI;MACF,OAAO,MAAMC,OAAO,CAACC,IAAI,CAAC,CAAC,IAAI,CAACC,0BAA0B,CAACZ,MAAM,CAAC,EAAEM,OAAO,CAAC,CAAC;IAC/E,CAAC,CAAC,OAAOlB,KAAK,EAAE;MACd,IAAIf,SAAA,CAAAwC,YAAY,CAACC,EAAE,CAAC1B,KAAK,CAAC,EAAE;QAC1BU,UAAU,CAACiB,KAAK,EAAE;QAClB,MAAM,IAAI5C,OAAA,CAAA6C,cAAc,CAAC,iCAAiCzC,mBAAA,CAAAkC,oBAAoB,KAAK,CAAC;MACtF;MACA,MAAMrB,KAAK;IACb,CAAC,SAAS;MACRkB,OAAO,CAACW,KAAK,EAAE;IACjB;EACF;;AA/DFC,OAAA,CAAA1C,yBAAA,GAAAA,yBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}