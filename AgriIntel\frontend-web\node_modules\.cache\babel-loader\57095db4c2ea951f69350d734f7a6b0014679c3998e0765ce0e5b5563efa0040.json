{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { useState, useEffect, useContext, useRef } from 'react';\nimport { getI18n, getDefaults, ReportNamespaces, I18nContext } from './context.js';\nimport { warnOnce, loadNamespaces, loadLanguages, hasLoadedNamespace } from './utils.js';\nconst usePrevious = (value, ignore) => {\n  const ref = useRef();\n  useEffect(() => {\n    ref.current = ignore ? ref.current : value;\n  }, [value, ignore]);\n  return ref.current;\n};\nexport function useTranslation(ns) {\n  let props = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  if (i18n && !i18n.reportNamespaces) i18n.reportNamespaces = new ReportNamespaces();\n  if (!i18n) {\n    warnOnce('You will need to pass in an i18next instance by using initReactI18next');\n    const notReadyT = (k, optsOrDefaultValue) => {\n      if (typeof optsOrDefaultValue === 'string') return optsOrDefaultValue;\n      if (optsOrDefaultValue && typeof optsOrDefaultValue === 'object' && typeof optsOrDefaultValue.defaultValue === 'string') return optsOrDefaultValue.defaultValue;\n      return Array.isArray(k) ? k[k.length - 1] : k;\n    };\n    const retNotReady = [notReadyT, {}, false];\n    retNotReady.t = notReadyT;\n    retNotReady.i18n = {};\n    retNotReady.ready = false;\n    return retNotReady;\n  }\n  if (i18n.options.react && i18n.options.react.wait !== undefined) warnOnce('It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.');\n  const i18nOptions = _objectSpread(_objectSpread(_objectSpread({}, getDefaults()), i18n.options.react), props);\n  const {\n    useSuspense,\n    keyPrefix\n  } = i18nOptions;\n  let namespaces = ns || defaultNSFromContext || i18n.options && i18n.options.defaultNS;\n  namespaces = typeof namespaces === 'string' ? [namespaces] : namespaces || ['translation'];\n  if (i18n.reportNamespaces.addUsedNamespaces) i18n.reportNamespaces.addUsedNamespaces(namespaces);\n  const ready = (i18n.isInitialized || i18n.initializedStoreOnce) && namespaces.every(n => hasLoadedNamespace(n, i18n, i18nOptions));\n  function getT() {\n    return i18n.getFixedT(props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  }\n  const [t, setT] = useState(getT);\n  let joinedNS = namespaces.join();\n  if (props.lng) joinedNS = \"\".concat(props.lng).concat(joinedNS);\n  const previousJoinedNS = usePrevious(joinedNS);\n  const isMounted = useRef(true);\n  useEffect(() => {\n    const {\n      bindI18n,\n      bindI18nStore\n    } = i18nOptions;\n    isMounted.current = true;\n    if (!ready && !useSuspense) {\n      if (props.lng) {\n        loadLanguages(i18n, props.lng, namespaces, () => {\n          if (isMounted.current) setT(getT);\n        });\n      } else {\n        loadNamespaces(i18n, namespaces, () => {\n          if (isMounted.current) setT(getT);\n        });\n      }\n    }\n    if (ready && previousJoinedNS && previousJoinedNS !== joinedNS && isMounted.current) {\n      setT(getT);\n    }\n    function boundReset() {\n      if (isMounted.current) setT(getT);\n    }\n    if (bindI18n && i18n) i18n.on(bindI18n, boundReset);\n    if (bindI18nStore && i18n) i18n.store.on(bindI18nStore, boundReset);\n    return () => {\n      isMounted.current = false;\n      if (bindI18n && i18n) bindI18n.split(' ').forEach(e => i18n.off(e, boundReset));\n      if (bindI18nStore && i18n) bindI18nStore.split(' ').forEach(e => i18n.store.off(e, boundReset));\n    };\n  }, [i18n, joinedNS]);\n  const isInitial = useRef(true);\n  useEffect(() => {\n    if (isMounted.current && !isInitial.current) {\n      setT(getT);\n    }\n    isInitial.current = false;\n  }, [i18n, keyPrefix]);\n  const ret = [t, i18n, ready];\n  ret.t = t;\n  ret.i18n = i18n;\n  ret.ready = ready;\n  if (ready) return ret;\n  if (!ready && !useSuspense) return ret;\n  throw new Promise(resolve => {\n    if (props.lng) {\n      loadLanguages(i18n, props.lng, namespaces, () => resolve());\n    } else {\n      loadNamespaces(i18n, namespaces, () => resolve());\n    }\n  });\n}", "map": {"version": 3, "names": ["useState", "useEffect", "useContext", "useRef", "getI18n", "getDefaults", "ReportNamespaces", "I18nContext", "warnOnce", "loadNamespaces", "loadLanguages", "hasLoadedNamespace", "usePrevious", "value", "ignore", "ref", "current", "useTranslation", "ns", "props", "arguments", "length", "undefined", "i18n", "i18nFromProps", "i18nFromContext", "defaultNS", "defaultNSFromContext", "reportNamespaces", "notReadyT", "k", "optsOrDefaultValue", "defaultValue", "Array", "isArray", "retNotReady", "t", "ready", "options", "react", "wait", "i18nOptions", "_objectSpread", "useSuspense", "keyPrefix", "namespaces", "addUsedNamespaces", "isInitialized", "initializedStoreOnce", "every", "n", "getT", "getFixedT", "lng", "nsMode", "setT", "joinedNS", "join", "concat", "previousJoinedNS", "isMounted", "bindI18n", "bindI18nStore", "boundReset", "on", "store", "split", "for<PERSON>ach", "e", "off", "isInitial", "ret", "Promise", "resolve"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-i18next/dist/es/useTranslation.js"], "sourcesContent": ["import { useState, useEffect, useContext, useRef } from 'react';\nimport { getI18n, getDefaults, ReportNamespaces, I18nContext } from './context.js';\nimport { warnOnce, loadNamespaces, loadLanguages, hasLoadedNamespace } from './utils.js';\nconst usePrevious = (value, ignore) => {\n  const ref = useRef();\n  useEffect(() => {\n    ref.current = ignore ? ref.current : value;\n  }, [value, ignore]);\n  return ref.current;\n};\nexport function useTranslation(ns) {\n  let props = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  const {\n    i18n: i18nFromProps\n  } = props;\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  if (i18n && !i18n.reportNamespaces) i18n.reportNamespaces = new ReportNamespaces();\n  if (!i18n) {\n    warnOnce('You will need to pass in an i18next instance by using initReactI18next');\n    const notReadyT = (k, optsOrDefaultValue) => {\n      if (typeof optsOrDefaultValue === 'string') return optsOrDefaultValue;\n      if (optsOrDefaultValue && typeof optsOrDefaultValue === 'object' && typeof optsOrDefaultValue.defaultValue === 'string') return optsOrDefaultValue.defaultValue;\n      return Array.isArray(k) ? k[k.length - 1] : k;\n    };\n    const retNotReady = [notReadyT, {}, false];\n    retNotReady.t = notReadyT;\n    retNotReady.i18n = {};\n    retNotReady.ready = false;\n    return retNotReady;\n  }\n  if (i18n.options.react && i18n.options.react.wait !== undefined) warnOnce('It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.');\n  const i18nOptions = {\n    ...getDefaults(),\n    ...i18n.options.react,\n    ...props\n  };\n  const {\n    useSuspense,\n    keyPrefix\n  } = i18nOptions;\n  let namespaces = ns || defaultNSFromContext || i18n.options && i18n.options.defaultNS;\n  namespaces = typeof namespaces === 'string' ? [namespaces] : namespaces || ['translation'];\n  if (i18n.reportNamespaces.addUsedNamespaces) i18n.reportNamespaces.addUsedNamespaces(namespaces);\n  const ready = (i18n.isInitialized || i18n.initializedStoreOnce) && namespaces.every(n => hasLoadedNamespace(n, i18n, i18nOptions));\n  function getT() {\n    return i18n.getFixedT(props.lng || null, i18nOptions.nsMode === 'fallback' ? namespaces : namespaces[0], keyPrefix);\n  }\n  const [t, setT] = useState(getT);\n  let joinedNS = namespaces.join();\n  if (props.lng) joinedNS = `${props.lng}${joinedNS}`;\n  const previousJoinedNS = usePrevious(joinedNS);\n  const isMounted = useRef(true);\n  useEffect(() => {\n    const {\n      bindI18n,\n      bindI18nStore\n    } = i18nOptions;\n    isMounted.current = true;\n    if (!ready && !useSuspense) {\n      if (props.lng) {\n        loadLanguages(i18n, props.lng, namespaces, () => {\n          if (isMounted.current) setT(getT);\n        });\n      } else {\n        loadNamespaces(i18n, namespaces, () => {\n          if (isMounted.current) setT(getT);\n        });\n      }\n    }\n    if (ready && previousJoinedNS && previousJoinedNS !== joinedNS && isMounted.current) {\n      setT(getT);\n    }\n    function boundReset() {\n      if (isMounted.current) setT(getT);\n    }\n    if (bindI18n && i18n) i18n.on(bindI18n, boundReset);\n    if (bindI18nStore && i18n) i18n.store.on(bindI18nStore, boundReset);\n    return () => {\n      isMounted.current = false;\n      if (bindI18n && i18n) bindI18n.split(' ').forEach(e => i18n.off(e, boundReset));\n      if (bindI18nStore && i18n) bindI18nStore.split(' ').forEach(e => i18n.store.off(e, boundReset));\n    };\n  }, [i18n, joinedNS]);\n  const isInitial = useRef(true);\n  useEffect(() => {\n    if (isMounted.current && !isInitial.current) {\n      setT(getT);\n    }\n    isInitial.current = false;\n  }, [i18n, keyPrefix]);\n  const ret = [t, i18n, ready];\n  ret.t = t;\n  ret.i18n = i18n;\n  ret.ready = ready;\n  if (ready) return ret;\n  if (!ready && !useSuspense) return ret;\n  throw new Promise(resolve => {\n    if (props.lng) {\n      loadLanguages(i18n, props.lng, namespaces, () => resolve());\n    } else {\n      loadNamespaces(i18n, namespaces, () => resolve());\n    }\n  });\n}"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,SAAS,EAAEC,UAAU,EAAEC,MAAM,QAAQ,OAAO;AAC/D,SAASC,OAAO,EAAEC,WAAW,EAAEC,gBAAgB,EAAEC,WAAW,QAAQ,cAAc;AAClF,SAASC,QAAQ,EAAEC,cAAc,EAAEC,aAAa,EAAEC,kBAAkB,QAAQ,YAAY;AACxF,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,MAAM,KAAK;EACrC,MAAMC,GAAG,GAAGZ,MAAM,CAAC,CAAC;EACpBF,SAAS,CAAC,MAAM;IACdc,GAAG,CAACC,OAAO,GAAGF,MAAM,GAAGC,GAAG,CAACC,OAAO,GAAGH,KAAK;EAC5C,CAAC,EAAE,CAACA,KAAK,EAAEC,MAAM,CAAC,CAAC;EACnB,OAAOC,GAAG,CAACC,OAAO;AACpB,CAAC;AACD,OAAO,SAASC,cAAcA,CAACC,EAAE,EAAE;EACjC,IAAIC,KAAK,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EAClF,MAAM;IACJG,IAAI,EAAEC;EACR,CAAC,GAAGL,KAAK;EACT,MAAM;IACJI,IAAI,EAAEE,eAAe;IACrBC,SAAS,EAAEC;EACb,CAAC,GAAGzB,UAAU,CAACK,WAAW,CAAC,IAAI,CAAC,CAAC;EACjC,MAAMgB,IAAI,GAAGC,aAAa,IAAIC,eAAe,IAAIrB,OAAO,CAAC,CAAC;EAC1D,IAAImB,IAAI,IAAI,CAACA,IAAI,CAACK,gBAAgB,EAAEL,IAAI,CAACK,gBAAgB,GAAG,IAAItB,gBAAgB,CAAC,CAAC;EAClF,IAAI,CAACiB,IAAI,EAAE;IACTf,QAAQ,CAAC,wEAAwE,CAAC;IAClF,MAAMqB,SAAS,GAAGA,CAACC,CAAC,EAAEC,kBAAkB,KAAK;MAC3C,IAAI,OAAOA,kBAAkB,KAAK,QAAQ,EAAE,OAAOA,kBAAkB;MACrE,IAAIA,kBAAkB,IAAI,OAAOA,kBAAkB,KAAK,QAAQ,IAAI,OAAOA,kBAAkB,CAACC,YAAY,KAAK,QAAQ,EAAE,OAAOD,kBAAkB,CAACC,YAAY;MAC/J,OAAOC,KAAK,CAACC,OAAO,CAACJ,CAAC,CAAC,GAAGA,CAAC,CAACA,CAAC,CAACT,MAAM,GAAG,CAAC,CAAC,GAAGS,CAAC;IAC/C,CAAC;IACD,MAAMK,WAAW,GAAG,CAACN,SAAS,EAAE,CAAC,CAAC,EAAE,KAAK,CAAC;IAC1CM,WAAW,CAACC,CAAC,GAAGP,SAAS;IACzBM,WAAW,CAACZ,IAAI,GAAG,CAAC,CAAC;IACrBY,WAAW,CAACE,KAAK,GAAG,KAAK;IACzB,OAAOF,WAAW;EACpB;EACA,IAAIZ,IAAI,CAACe,OAAO,CAACC,KAAK,IAAIhB,IAAI,CAACe,OAAO,CAACC,KAAK,CAACC,IAAI,KAAKlB,SAAS,EAAEd,QAAQ,CAAC,qGAAqG,CAAC;EAChL,MAAMiC,WAAW,GAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACZrC,WAAW,CAAC,CAAC,GACbkB,IAAI,CAACe,OAAO,CAACC,KAAK,GAClBpB,KAAK,CACT;EACD,MAAM;IACJwB,WAAW;IACXC;EACF,CAAC,GAAGH,WAAW;EACf,IAAII,UAAU,GAAG3B,EAAE,IAAIS,oBAAoB,IAAIJ,IAAI,CAACe,OAAO,IAAIf,IAAI,CAACe,OAAO,CAACZ,SAAS;EACrFmB,UAAU,GAAG,OAAOA,UAAU,KAAK,QAAQ,GAAG,CAACA,UAAU,CAAC,GAAGA,UAAU,IAAI,CAAC,aAAa,CAAC;EAC1F,IAAItB,IAAI,CAACK,gBAAgB,CAACkB,iBAAiB,EAAEvB,IAAI,CAACK,gBAAgB,CAACkB,iBAAiB,CAACD,UAAU,CAAC;EAChG,MAAMR,KAAK,GAAG,CAACd,IAAI,CAACwB,aAAa,IAAIxB,IAAI,CAACyB,oBAAoB,KAAKH,UAAU,CAACI,KAAK,CAACC,CAAC,IAAIvC,kBAAkB,CAACuC,CAAC,EAAE3B,IAAI,EAAEkB,WAAW,CAAC,CAAC;EAClI,SAASU,IAAIA,CAAA,EAAG;IACd,OAAO5B,IAAI,CAAC6B,SAAS,CAACjC,KAAK,CAACkC,GAAG,IAAI,IAAI,EAAEZ,WAAW,CAACa,MAAM,KAAK,UAAU,GAAGT,UAAU,GAAGA,UAAU,CAAC,CAAC,CAAC,EAAED,SAAS,CAAC;EACrH;EACA,MAAM,CAACR,CAAC,EAAEmB,IAAI,CAAC,GAAGvD,QAAQ,CAACmD,IAAI,CAAC;EAChC,IAAIK,QAAQ,GAAGX,UAAU,CAACY,IAAI,CAAC,CAAC;EAChC,IAAItC,KAAK,CAACkC,GAAG,EAAEG,QAAQ,MAAAE,MAAA,CAAMvC,KAAK,CAACkC,GAAG,EAAAK,MAAA,CAAGF,QAAQ,CAAE;EACnD,MAAMG,gBAAgB,GAAG/C,WAAW,CAAC4C,QAAQ,CAAC;EAC9C,MAAMI,SAAS,GAAGzD,MAAM,CAAC,IAAI,CAAC;EAC9BF,SAAS,CAAC,MAAM;IACd,MAAM;MACJ4D,QAAQ;MACRC;IACF,CAAC,GAAGrB,WAAW;IACfmB,SAAS,CAAC5C,OAAO,GAAG,IAAI;IACxB,IAAI,CAACqB,KAAK,IAAI,CAACM,WAAW,EAAE;MAC1B,IAAIxB,KAAK,CAACkC,GAAG,EAAE;QACb3C,aAAa,CAACa,IAAI,EAAEJ,KAAK,CAACkC,GAAG,EAAER,UAAU,EAAE,MAAM;UAC/C,IAAIe,SAAS,CAAC5C,OAAO,EAAEuC,IAAI,CAACJ,IAAI,CAAC;QACnC,CAAC,CAAC;MACJ,CAAC,MAAM;QACL1C,cAAc,CAACc,IAAI,EAAEsB,UAAU,EAAE,MAAM;UACrC,IAAIe,SAAS,CAAC5C,OAAO,EAAEuC,IAAI,CAACJ,IAAI,CAAC;QACnC,CAAC,CAAC;MACJ;IACF;IACA,IAAId,KAAK,IAAIsB,gBAAgB,IAAIA,gBAAgB,KAAKH,QAAQ,IAAII,SAAS,CAAC5C,OAAO,EAAE;MACnFuC,IAAI,CAACJ,IAAI,CAAC;IACZ;IACA,SAASY,UAAUA,CAAA,EAAG;MACpB,IAAIH,SAAS,CAAC5C,OAAO,EAAEuC,IAAI,CAACJ,IAAI,CAAC;IACnC;IACA,IAAIU,QAAQ,IAAItC,IAAI,EAAEA,IAAI,CAACyC,EAAE,CAACH,QAAQ,EAAEE,UAAU,CAAC;IACnD,IAAID,aAAa,IAAIvC,IAAI,EAAEA,IAAI,CAAC0C,KAAK,CAACD,EAAE,CAACF,aAAa,EAAEC,UAAU,CAAC;IACnE,OAAO,MAAM;MACXH,SAAS,CAAC5C,OAAO,GAAG,KAAK;MACzB,IAAI6C,QAAQ,IAAItC,IAAI,EAAEsC,QAAQ,CAACK,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAACC,CAAC,IAAI7C,IAAI,CAAC8C,GAAG,CAACD,CAAC,EAAEL,UAAU,CAAC,CAAC;MAC/E,IAAID,aAAa,IAAIvC,IAAI,EAAEuC,aAAa,CAACI,KAAK,CAAC,GAAG,CAAC,CAACC,OAAO,CAACC,CAAC,IAAI7C,IAAI,CAAC0C,KAAK,CAACI,GAAG,CAACD,CAAC,EAAEL,UAAU,CAAC,CAAC;IACjG,CAAC;EACH,CAAC,EAAE,CAACxC,IAAI,EAAEiC,QAAQ,CAAC,CAAC;EACpB,MAAMc,SAAS,GAAGnE,MAAM,CAAC,IAAI,CAAC;EAC9BF,SAAS,CAAC,MAAM;IACd,IAAI2D,SAAS,CAAC5C,OAAO,IAAI,CAACsD,SAAS,CAACtD,OAAO,EAAE;MAC3CuC,IAAI,CAACJ,IAAI,CAAC;IACZ;IACAmB,SAAS,CAACtD,OAAO,GAAG,KAAK;EAC3B,CAAC,EAAE,CAACO,IAAI,EAAEqB,SAAS,CAAC,CAAC;EACrB,MAAM2B,GAAG,GAAG,CAACnC,CAAC,EAAEb,IAAI,EAAEc,KAAK,CAAC;EAC5BkC,GAAG,CAACnC,CAAC,GAAGA,CAAC;EACTmC,GAAG,CAAChD,IAAI,GAAGA,IAAI;EACfgD,GAAG,CAAClC,KAAK,GAAGA,KAAK;EACjB,IAAIA,KAAK,EAAE,OAAOkC,GAAG;EACrB,IAAI,CAAClC,KAAK,IAAI,CAACM,WAAW,EAAE,OAAO4B,GAAG;EACtC,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAI;IAC3B,IAAItD,KAAK,CAACkC,GAAG,EAAE;MACb3C,aAAa,CAACa,IAAI,EAAEJ,KAAK,CAACkC,GAAG,EAAER,UAAU,EAAE,MAAM4B,OAAO,CAAC,CAAC,CAAC;IAC7D,CAAC,MAAM;MACLhE,cAAc,CAACc,IAAI,EAAEsB,UAAU,EAAE,MAAM4B,OAAO,CAAC,CAAC,CAAC;IACnD;EACF,CAAC,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}