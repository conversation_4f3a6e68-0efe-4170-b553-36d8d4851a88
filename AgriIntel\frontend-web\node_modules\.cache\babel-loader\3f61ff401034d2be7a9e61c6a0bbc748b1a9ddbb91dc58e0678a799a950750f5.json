{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.MongoCredentials = exports.DEFAULT_ALLOWED_HOSTS = void 0;\nconst error_1 = require(\"../../error\");\nconst gssapi_1 = require(\"./gssapi\");\nconst providers_1 = require(\"./providers\");\n/**\n * @see https://github.com/mongodb/specifications/blob/master/source/auth/auth.md\n */\nfunction getDefaultAuthMechanism(hello) {\n  if (hello) {\n    // If hello contains saslSupportedMechs, use scram-sha-256\n    // if it is available, else scram-sha-1\n    if (Array.isArray(hello.saslSupportedMechs)) {\n      return hello.saslSupportedMechs.includes(providers_1.AuthMechanism.MONGODB_SCRAM_SHA256) ? providers_1.AuthMechanism.MONGODB_SCRAM_SHA256 : providers_1.AuthMechanism.MONGODB_SCRAM_SHA1;\n    }\n  }\n  // Default auth mechanism for 4.0 and higher.\n  return providers_1.AuthMechanism.MONGODB_SCRAM_SHA256;\n}\nconst ALLOWED_ENVIRONMENT_NAMES = ['test', 'azure', 'gcp', 'k8s'];\nconst ALLOWED_HOSTS_ERROR = 'Auth mechanism property ALLOWED_HOSTS must be an array of strings.';\n/** @internal */\nexports.DEFAULT_ALLOWED_HOSTS = ['*.mongodb.net', '*.mongodb-qa.net', '*.mongodb-dev.net', '*.mongodbgov.net', 'localhost', '127.0.0.1', '::1'];\n/** Error for when the token audience is missing in the environment. */\nconst TOKEN_RESOURCE_MISSING_ERROR = 'TOKEN_RESOURCE must be set in the auth mechanism properties when ENVIRONMENT is azure or gcp.';\n/**\n * A representation of the credentials used by MongoDB\n * @public\n */\nclass MongoCredentials {\n  constructor(options) {\n    this.username = options.username ?? '';\n    this.password = options.password;\n    this.source = options.source;\n    if (!this.source && options.db) {\n      this.source = options.db;\n    }\n    this.mechanism = options.mechanism || providers_1.AuthMechanism.MONGODB_DEFAULT;\n    this.mechanismProperties = options.mechanismProperties || {};\n    if (this.mechanism.match(/MONGODB-AWS/i)) {\n      if (!this.username && process.env.AWS_ACCESS_KEY_ID) {\n        this.username = process.env.AWS_ACCESS_KEY_ID;\n      }\n      if (!this.password && process.env.AWS_SECRET_ACCESS_KEY) {\n        this.password = process.env.AWS_SECRET_ACCESS_KEY;\n      }\n      if (this.mechanismProperties.AWS_SESSION_TOKEN == null && process.env.AWS_SESSION_TOKEN != null) {\n        this.mechanismProperties = {\n          ...this.mechanismProperties,\n          AWS_SESSION_TOKEN: process.env.AWS_SESSION_TOKEN\n        };\n      }\n    }\n    if (this.mechanism === providers_1.AuthMechanism.MONGODB_OIDC && !this.mechanismProperties.ALLOWED_HOSTS) {\n      this.mechanismProperties = {\n        ...this.mechanismProperties,\n        ALLOWED_HOSTS: exports.DEFAULT_ALLOWED_HOSTS\n      };\n    }\n    Object.freeze(this.mechanismProperties);\n    Object.freeze(this);\n  }\n  /** Determines if two MongoCredentials objects are equivalent */\n  equals(other) {\n    return this.mechanism === other.mechanism && this.username === other.username && this.password === other.password && this.source === other.source;\n  }\n  /**\n   * If the authentication mechanism is set to \"default\", resolves the authMechanism\n   * based on the server version and server supported sasl mechanisms.\n   *\n   * @param hello - A hello response from the server\n   */\n  resolveAuthMechanism(hello) {\n    // If the mechanism is not \"default\", then it does not need to be resolved\n    if (this.mechanism.match(/DEFAULT/i)) {\n      return new MongoCredentials({\n        username: this.username,\n        password: this.password,\n        source: this.source,\n        mechanism: getDefaultAuthMechanism(hello),\n        mechanismProperties: this.mechanismProperties\n      });\n    }\n    return this;\n  }\n  validate() {\n    if ((this.mechanism === providers_1.AuthMechanism.MONGODB_GSSAPI || this.mechanism === providers_1.AuthMechanism.MONGODB_PLAIN || this.mechanism === providers_1.AuthMechanism.MONGODB_SCRAM_SHA1 || this.mechanism === providers_1.AuthMechanism.MONGODB_SCRAM_SHA256) && !this.username) {\n      throw new error_1.MongoMissingCredentialsError(`Username required for mechanism '${this.mechanism}'`);\n    }\n    if (this.mechanism === providers_1.AuthMechanism.MONGODB_OIDC) {\n      if (this.username && this.mechanismProperties.ENVIRONMENT && this.mechanismProperties.ENVIRONMENT !== 'azure') {\n        throw new error_1.MongoInvalidArgumentError(`username and ENVIRONMENT '${this.mechanismProperties.ENVIRONMENT}' may not be used together for mechanism '${this.mechanism}'.`);\n      }\n      if (this.username && this.password) {\n        throw new error_1.MongoInvalidArgumentError(`No password is allowed in ENVIRONMENT '${this.mechanismProperties.ENVIRONMENT}' for '${this.mechanism}'.`);\n      }\n      if ((this.mechanismProperties.ENVIRONMENT === 'azure' || this.mechanismProperties.ENVIRONMENT === 'gcp') && !this.mechanismProperties.TOKEN_RESOURCE) {\n        throw new error_1.MongoInvalidArgumentError(TOKEN_RESOURCE_MISSING_ERROR);\n      }\n      if (this.mechanismProperties.ENVIRONMENT && !ALLOWED_ENVIRONMENT_NAMES.includes(this.mechanismProperties.ENVIRONMENT)) {\n        throw new error_1.MongoInvalidArgumentError(`Currently only a ENVIRONMENT in ${ALLOWED_ENVIRONMENT_NAMES.join(',')} is supported for mechanism '${this.mechanism}'.`);\n      }\n      if (!this.mechanismProperties.ENVIRONMENT && !this.mechanismProperties.OIDC_CALLBACK && !this.mechanismProperties.OIDC_HUMAN_CALLBACK) {\n        throw new error_1.MongoInvalidArgumentError(`Either a ENVIRONMENT, OIDC_CALLBACK, or OIDC_HUMAN_CALLBACK must be specified for mechanism '${this.mechanism}'.`);\n      }\n      if (this.mechanismProperties.ALLOWED_HOSTS) {\n        const hosts = this.mechanismProperties.ALLOWED_HOSTS;\n        if (!Array.isArray(hosts)) {\n          throw new error_1.MongoInvalidArgumentError(ALLOWED_HOSTS_ERROR);\n        }\n        for (const host of hosts) {\n          if (typeof host !== 'string') {\n            throw new error_1.MongoInvalidArgumentError(ALLOWED_HOSTS_ERROR);\n          }\n        }\n      }\n    }\n    if (providers_1.AUTH_MECHS_AUTH_SRC_EXTERNAL.has(this.mechanism)) {\n      if (this.source != null && this.source !== '$external') {\n        // TODO(NODE-3485): Replace this with a MongoAuthValidationError\n        throw new error_1.MongoAPIError(`Invalid source '${this.source}' for mechanism '${this.mechanism}' specified.`);\n      }\n    }\n    if (this.mechanism === providers_1.AuthMechanism.MONGODB_PLAIN && this.source == null) {\n      // TODO(NODE-3485): Replace this with a MongoAuthValidationError\n      throw new error_1.MongoAPIError('PLAIN Authentication Mechanism needs an auth source');\n    }\n    if (this.mechanism === providers_1.AuthMechanism.MONGODB_X509 && this.password != null) {\n      if (this.password === '') {\n        Reflect.set(this, 'password', undefined);\n        return;\n      }\n      // TODO(NODE-3485): Replace this with a MongoAuthValidationError\n      throw new error_1.MongoAPIError(`Password not allowed for mechanism MONGODB-X509`);\n    }\n    const canonicalization = this.mechanismProperties.CANONICALIZE_HOST_NAME ?? false;\n    if (!Object.values(gssapi_1.GSSAPICanonicalizationValue).includes(canonicalization)) {\n      throw new error_1.MongoAPIError(`Invalid CANONICALIZE_HOST_NAME value: ${canonicalization}`);\n    }\n  }\n  static merge(creds, options) {\n    return new MongoCredentials({\n      username: options.username ?? creds?.username ?? '',\n      password: options.password ?? creds?.password ?? '',\n      mechanism: options.mechanism ?? creds?.mechanism ?? providers_1.AuthMechanism.MONGODB_DEFAULT,\n      mechanismProperties: options.mechanismProperties ?? creds?.mechanismProperties ?? {},\n      source: options.source ?? options.db ?? creds?.source ?? 'admin'\n    });\n  }\n}\nexports.MongoCredentials = MongoCredentials;", "map": {"version": 3, "names": ["error_1", "require", "gssapi_1", "providers_1", "getDefaultAuthMechanism", "hello", "Array", "isArray", "saslSupportedMechs", "includes", "AuthMechanism", "MONGODB_SCRAM_SHA256", "MONGODB_SCRAM_SHA1", "ALLOWED_ENVIRONMENT_NAMES", "ALLOWED_HOSTS_ERROR", "exports", "DEFAULT_ALLOWED_HOSTS", "TOKEN_RESOURCE_MISSING_ERROR", "MongoCredentials", "constructor", "options", "username", "password", "source", "db", "mechanism", "MONGODB_DEFAULT", "mechanismProperties", "match", "process", "env", "AWS_ACCESS_KEY_ID", "AWS_SECRET_ACCESS_KEY", "AWS_SESSION_TOKEN", "MONGODB_OIDC", "ALLOWED_HOSTS", "Object", "freeze", "equals", "other", "resolveAuthMechanism", "validate", "MONGODB_GSSAPI", "MONGODB_PLAIN", "MongoMissingCredentialsError", "ENVIRONMENT", "MongoInvalidArgumentError", "TOKEN_RESOURCE", "join", "OIDC_CALLBACK", "OIDC_HUMAN_CALLBACK", "hosts", "host", "AUTH_MECHS_AUTH_SRC_EXTERNAL", "has", "MongoAPIError", "MONGODB_X509", "Reflect", "set", "undefined", "canonicalization", "CANONICALIZE_HOST_NAME", "values", "GSSAPICanonicalizationValue", "merge", "creds"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\auth\\mongo_credentials.ts"], "sourcesContent": ["// Resolves the default auth mechanism according to\n// Resolves the default auth mechanism according to\nimport type { Document } from '../../bson';\nimport {\n  MongoAPIError,\n  MongoInvalidArgumentError,\n  MongoMissingCredentialsError\n} from '../../error';\nimport type { AWSCredentialProvider } from './aws_temporary_credentials';\nimport { GSSAPICanonicalizationValue } from './gssapi';\nimport type { OIDCCallbackFunction } from './mongodb_oidc';\nimport { AUTH_MECHS_AUTH_SRC_EXTERNAL, AuthMechanism } from './providers';\n\n/**\n * @see https://github.com/mongodb/specifications/blob/master/source/auth/auth.md\n */\nfunction getDefaultAuthMechanism(hello: Document | null): AuthMechanism {\n  if (hello) {\n    // If hello contains saslSupportedMechs, use scram-sha-256\n    // if it is available, else scram-sha-1\n    if (Array.isArray(hello.saslSupportedMechs)) {\n      return hello.saslSupportedMechs.includes(AuthMechanism.MONGODB_SCRAM_SHA256)\n        ? AuthMechanism.MONGODB_SCRAM_SHA256\n        : AuthMechanism.MONGODB_SCRAM_SHA1;\n    }\n  }\n\n  // Default auth mechanism for 4.0 and higher.\n  return AuthMechanism.MONGODB_SCRAM_SHA256;\n}\n\nconst ALLOWED_ENVIRONMENT_NAMES: AuthMechanismProperties['ENVIRONMENT'][] = [\n  'test',\n  'azure',\n  'gcp',\n  'k8s'\n];\nconst ALLOWED_HOSTS_ERROR = 'Auth mechanism property ALLOWED_HOSTS must be an array of strings.';\n\n/** @internal */\nexport const DEFAULT_ALLOWED_HOSTS = [\n  '*.mongodb.net',\n  '*.mongodb-qa.net',\n  '*.mongodb-dev.net',\n  '*.mongodbgov.net',\n  'localhost',\n  '127.0.0.1',\n  '::1'\n];\n\n/** Error for when the token audience is missing in the environment. */\nconst TOKEN_RESOURCE_MISSING_ERROR =\n  'TOKEN_RESOURCE must be set in the auth mechanism properties when ENVIRONMENT is azure or gcp.';\n\n/** @public */\nexport interface AuthMechanismProperties extends Document {\n  SERVICE_HOST?: string;\n  SERVICE_NAME?: string;\n  SERVICE_REALM?: string;\n  CANONICALIZE_HOST_NAME?: GSSAPICanonicalizationValue;\n  AWS_SESSION_TOKEN?: string;\n  /** A user provided OIDC machine callback function. */\n  OIDC_CALLBACK?: OIDCCallbackFunction;\n  /** A user provided OIDC human interacted callback function. */\n  OIDC_HUMAN_CALLBACK?: OIDCCallbackFunction;\n  /** The OIDC environment. Note that 'test' is for internal use only. */\n  ENVIRONMENT?: 'test' | 'azure' | 'gcp' | 'k8s';\n  /** Allowed hosts that OIDC auth can connect to. */\n  ALLOWED_HOSTS?: string[];\n  /** The resource token for OIDC auth in Azure and GCP. */\n  TOKEN_RESOURCE?: string;\n  /**\n   * A custom AWS credential provider to use. An example using the AWS SDK default provider chain:\n   *\n   * ```ts\n   * const client = new MongoClient(process.env.MONGODB_URI, {\n   *   authMechanismProperties: {\n   *     AWS_CREDENTIAL_PROVIDER: fromNodeProviderChain()\n   *   }\n   * });\n   * ```\n   *\n   * Using a custom function that returns AWS credentials:\n   *\n   * ```ts\n   * const client = new MongoClient(process.env.MONGODB_URI, {\n   *   authMechanismProperties: {\n   *     AWS_CREDENTIAL_PROVIDER: async () => {\n   *       return {\n   *         accessKeyId: process.env.ACCESS_KEY_ID,\n   *         secretAccessKey: process.env.SECRET_ACCESS_KEY\n   *       }\n   *     }\n   *   }\n   * });\n   * ```\n   */\n  AWS_CREDENTIAL_PROVIDER?: AWSCredentialProvider;\n}\n\n/** @public */\nexport interface MongoCredentialsOptions {\n  username?: string;\n  password: string;\n  source: string;\n  db?: string;\n  mechanism?: AuthMechanism;\n  mechanismProperties: AuthMechanismProperties;\n}\n\n/**\n * A representation of the credentials used by MongoDB\n * @public\n */\nexport class MongoCredentials {\n  /** The username used for authentication */\n  readonly username: string;\n  /** The password used for authentication */\n  readonly password: string;\n  /** The database that the user should authenticate against */\n  readonly source: string;\n  /** The method used to authenticate */\n  readonly mechanism: AuthMechanism;\n  /** Special properties used by some types of auth mechanisms */\n  readonly mechanismProperties: AuthMechanismProperties;\n\n  constructor(options: MongoCredentialsOptions) {\n    this.username = options.username ?? '';\n    this.password = options.password;\n    this.source = options.source;\n    if (!this.source && options.db) {\n      this.source = options.db;\n    }\n    this.mechanism = options.mechanism || AuthMechanism.MONGODB_DEFAULT;\n    this.mechanismProperties = options.mechanismProperties || {};\n\n    if (this.mechanism.match(/MONGODB-AWS/i)) {\n      if (!this.username && process.env.AWS_ACCESS_KEY_ID) {\n        this.username = process.env.AWS_ACCESS_KEY_ID;\n      }\n\n      if (!this.password && process.env.AWS_SECRET_ACCESS_KEY) {\n        this.password = process.env.AWS_SECRET_ACCESS_KEY;\n      }\n\n      if (\n        this.mechanismProperties.AWS_SESSION_TOKEN == null &&\n        process.env.AWS_SESSION_TOKEN != null\n      ) {\n        this.mechanismProperties = {\n          ...this.mechanismProperties,\n          AWS_SESSION_TOKEN: process.env.AWS_SESSION_TOKEN\n        };\n      }\n    }\n\n    if (this.mechanism === AuthMechanism.MONGODB_OIDC && !this.mechanismProperties.ALLOWED_HOSTS) {\n      this.mechanismProperties = {\n        ...this.mechanismProperties,\n        ALLOWED_HOSTS: DEFAULT_ALLOWED_HOSTS\n      };\n    }\n\n    Object.freeze(this.mechanismProperties);\n    Object.freeze(this);\n  }\n\n  /** Determines if two MongoCredentials objects are equivalent */\n  equals(other: MongoCredentials): boolean {\n    return (\n      this.mechanism === other.mechanism &&\n      this.username === other.username &&\n      this.password === other.password &&\n      this.source === other.source\n    );\n  }\n\n  /**\n   * If the authentication mechanism is set to \"default\", resolves the authMechanism\n   * based on the server version and server supported sasl mechanisms.\n   *\n   * @param hello - A hello response from the server\n   */\n  resolveAuthMechanism(hello: Document | null): MongoCredentials {\n    // If the mechanism is not \"default\", then it does not need to be resolved\n    if (this.mechanism.match(/DEFAULT/i)) {\n      return new MongoCredentials({\n        username: this.username,\n        password: this.password,\n        source: this.source,\n        mechanism: getDefaultAuthMechanism(hello),\n        mechanismProperties: this.mechanismProperties\n      });\n    }\n\n    return this;\n  }\n\n  validate(): void {\n    if (\n      (this.mechanism === AuthMechanism.MONGODB_GSSAPI ||\n        this.mechanism === AuthMechanism.MONGODB_PLAIN ||\n        this.mechanism === AuthMechanism.MONGODB_SCRAM_SHA1 ||\n        this.mechanism === AuthMechanism.MONGODB_SCRAM_SHA256) &&\n      !this.username\n    ) {\n      throw new MongoMissingCredentialsError(`Username required for mechanism '${this.mechanism}'`);\n    }\n\n    if (this.mechanism === AuthMechanism.MONGODB_OIDC) {\n      if (\n        this.username &&\n        this.mechanismProperties.ENVIRONMENT &&\n        this.mechanismProperties.ENVIRONMENT !== 'azure'\n      ) {\n        throw new MongoInvalidArgumentError(\n          `username and ENVIRONMENT '${this.mechanismProperties.ENVIRONMENT}' may not be used together for mechanism '${this.mechanism}'.`\n        );\n      }\n\n      if (this.username && this.password) {\n        throw new MongoInvalidArgumentError(\n          `No password is allowed in ENVIRONMENT '${this.mechanismProperties.ENVIRONMENT}' for '${this.mechanism}'.`\n        );\n      }\n\n      if (\n        (this.mechanismProperties.ENVIRONMENT === 'azure' ||\n          this.mechanismProperties.ENVIRONMENT === 'gcp') &&\n        !this.mechanismProperties.TOKEN_RESOURCE\n      ) {\n        throw new MongoInvalidArgumentError(TOKEN_RESOURCE_MISSING_ERROR);\n      }\n\n      if (\n        this.mechanismProperties.ENVIRONMENT &&\n        !ALLOWED_ENVIRONMENT_NAMES.includes(this.mechanismProperties.ENVIRONMENT)\n      ) {\n        throw new MongoInvalidArgumentError(\n          `Currently only a ENVIRONMENT in ${ALLOWED_ENVIRONMENT_NAMES.join(\n            ','\n          )} is supported for mechanism '${this.mechanism}'.`\n        );\n      }\n\n      if (\n        !this.mechanismProperties.ENVIRONMENT &&\n        !this.mechanismProperties.OIDC_CALLBACK &&\n        !this.mechanismProperties.OIDC_HUMAN_CALLBACK\n      ) {\n        throw new MongoInvalidArgumentError(\n          `Either a ENVIRONMENT, OIDC_CALLBACK, or OIDC_HUMAN_CALLBACK must be specified for mechanism '${this.mechanism}'.`\n        );\n      }\n\n      if (this.mechanismProperties.ALLOWED_HOSTS) {\n        const hosts = this.mechanismProperties.ALLOWED_HOSTS;\n        if (!Array.isArray(hosts)) {\n          throw new MongoInvalidArgumentError(ALLOWED_HOSTS_ERROR);\n        }\n        for (const host of hosts) {\n          if (typeof host !== 'string') {\n            throw new MongoInvalidArgumentError(ALLOWED_HOSTS_ERROR);\n          }\n        }\n      }\n    }\n\n    if (AUTH_MECHS_AUTH_SRC_EXTERNAL.has(this.mechanism)) {\n      if (this.source != null && this.source !== '$external') {\n        // TODO(NODE-3485): Replace this with a MongoAuthValidationError\n        throw new MongoAPIError(\n          `Invalid source '${this.source}' for mechanism '${this.mechanism}' specified.`\n        );\n      }\n    }\n\n    if (this.mechanism === AuthMechanism.MONGODB_PLAIN && this.source == null) {\n      // TODO(NODE-3485): Replace this with a MongoAuthValidationError\n      throw new MongoAPIError('PLAIN Authentication Mechanism needs an auth source');\n    }\n\n    if (this.mechanism === AuthMechanism.MONGODB_X509 && this.password != null) {\n      if (this.password === '') {\n        Reflect.set(this, 'password', undefined);\n        return;\n      }\n      // TODO(NODE-3485): Replace this with a MongoAuthValidationError\n      throw new MongoAPIError(`Password not allowed for mechanism MONGODB-X509`);\n    }\n\n    const canonicalization = this.mechanismProperties.CANONICALIZE_HOST_NAME ?? false;\n    if (!Object.values(GSSAPICanonicalizationValue).includes(canonicalization)) {\n      throw new MongoAPIError(`Invalid CANONICALIZE_HOST_NAME value: ${canonicalization}`);\n    }\n  }\n\n  static merge(\n    creds: MongoCredentials | undefined,\n    options: Partial<MongoCredentialsOptions>\n  ): MongoCredentials {\n    return new MongoCredentials({\n      username: options.username ?? creds?.username ?? '',\n      password: options.password ?? creds?.password ?? '',\n      mechanism: options.mechanism ?? creds?.mechanism ?? AuthMechanism.MONGODB_DEFAULT,\n      mechanismProperties: options.mechanismProperties ?? creds?.mechanismProperties ?? {},\n      source: options.source ?? options.db ?? creds?.source ?? 'admin'\n    });\n  }\n}\n"], "mappings": ";;;;;;AAGA,MAAAA,OAAA,GAAAC,OAAA;AAMA,MAAAC,QAAA,GAAAD,OAAA;AAEA,MAAAE,WAAA,GAAAF,OAAA;AAEA;;;AAGA,SAASG,uBAAuBA,CAACC,KAAsB;EACrD,IAAIA,KAAK,EAAE;IACT;IACA;IACA,IAAIC,KAAK,CAACC,OAAO,CAACF,KAAK,CAACG,kBAAkB,CAAC,EAAE;MAC3C,OAAOH,KAAK,CAACG,kBAAkB,CAACC,QAAQ,CAACN,WAAA,CAAAO,aAAa,CAACC,oBAAoB,CAAC,GACxER,WAAA,CAAAO,aAAa,CAACC,oBAAoB,GAClCR,WAAA,CAAAO,aAAa,CAACE,kBAAkB;IACtC;EACF;EAEA;EACA,OAAOT,WAAA,CAAAO,aAAa,CAACC,oBAAoB;AAC3C;AAEA,MAAME,yBAAyB,GAA6C,CAC1E,MAAM,EACN,OAAO,EACP,KAAK,EACL,KAAK,CACN;AACD,MAAMC,mBAAmB,GAAG,oEAAoE;AAEhG;AACaC,OAAA,CAAAC,qBAAqB,GAAG,CACnC,eAAe,EACf,kBAAkB,EAClB,mBAAmB,EACnB,kBAAkB,EAClB,WAAW,EACX,WAAW,EACX,KAAK,CACN;AAED;AACA,MAAMC,4BAA4B,GAChC,+FAA+F;AA0DjG;;;;AAIA,MAAaC,gBAAgB;EAY3BC,YAAYC,OAAgC;IAC1C,IAAI,CAACC,QAAQ,GAAGD,OAAO,CAACC,QAAQ,IAAI,EAAE;IACtC,IAAI,CAACC,QAAQ,GAAGF,OAAO,CAACE,QAAQ;IAChC,IAAI,CAACC,MAAM,GAAGH,OAAO,CAACG,MAAM;IAC5B,IAAI,CAAC,IAAI,CAACA,MAAM,IAAIH,OAAO,CAACI,EAAE,EAAE;MAC9B,IAAI,CAACD,MAAM,GAAGH,OAAO,CAACI,EAAE;IAC1B;IACA,IAAI,CAACC,SAAS,GAAGL,OAAO,CAACK,SAAS,IAAItB,WAAA,CAAAO,aAAa,CAACgB,eAAe;IACnE,IAAI,CAACC,mBAAmB,GAAGP,OAAO,CAACO,mBAAmB,IAAI,EAAE;IAE5D,IAAI,IAAI,CAACF,SAAS,CAACG,KAAK,CAAC,cAAc,CAAC,EAAE;MACxC,IAAI,CAAC,IAAI,CAACP,QAAQ,IAAIQ,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAE;QACnD,IAAI,CAACV,QAAQ,GAAGQ,OAAO,CAACC,GAAG,CAACC,iBAAiB;MAC/C;MAEA,IAAI,CAAC,IAAI,CAACT,QAAQ,IAAIO,OAAO,CAACC,GAAG,CAACE,qBAAqB,EAAE;QACvD,IAAI,CAACV,QAAQ,GAAGO,OAAO,CAACC,GAAG,CAACE,qBAAqB;MACnD;MAEA,IACE,IAAI,CAACL,mBAAmB,CAACM,iBAAiB,IAAI,IAAI,IAClDJ,OAAO,CAACC,GAAG,CAACG,iBAAiB,IAAI,IAAI,EACrC;QACA,IAAI,CAACN,mBAAmB,GAAG;UACzB,GAAG,IAAI,CAACA,mBAAmB;UAC3BM,iBAAiB,EAAEJ,OAAO,CAACC,GAAG,CAACG;SAChC;MACH;IACF;IAEA,IAAI,IAAI,CAACR,SAAS,KAAKtB,WAAA,CAAAO,aAAa,CAACwB,YAAY,IAAI,CAAC,IAAI,CAACP,mBAAmB,CAACQ,aAAa,EAAE;MAC5F,IAAI,CAACR,mBAAmB,GAAG;QACzB,GAAG,IAAI,CAACA,mBAAmB;QAC3BQ,aAAa,EAAEpB,OAAA,CAAAC;OAChB;IACH;IAEAoB,MAAM,CAACC,MAAM,CAAC,IAAI,CAACV,mBAAmB,CAAC;IACvCS,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EACrB;EAEA;EACAC,MAAMA,CAACC,KAAuB;IAC5B,OACE,IAAI,CAACd,SAAS,KAAKc,KAAK,CAACd,SAAS,IAClC,IAAI,CAACJ,QAAQ,KAAKkB,KAAK,CAAClB,QAAQ,IAChC,IAAI,CAACC,QAAQ,KAAKiB,KAAK,CAACjB,QAAQ,IAChC,IAAI,CAACC,MAAM,KAAKgB,KAAK,CAAChB,MAAM;EAEhC;EAEA;;;;;;EAMAiB,oBAAoBA,CAACnC,KAAsB;IACzC;IACA,IAAI,IAAI,CAACoB,SAAS,CAACG,KAAK,CAAC,UAAU,CAAC,EAAE;MACpC,OAAO,IAAIV,gBAAgB,CAAC;QAC1BG,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBC,QAAQ,EAAE,IAAI,CAACA,QAAQ;QACvBC,MAAM,EAAE,IAAI,CAACA,MAAM;QACnBE,SAAS,EAAErB,uBAAuB,CAACC,KAAK,CAAC;QACzCsB,mBAAmB,EAAE,IAAI,CAACA;OAC3B,CAAC;IACJ;IAEA,OAAO,IAAI;EACb;EAEAc,QAAQA,CAAA;IACN,IACE,CAAC,IAAI,CAAChB,SAAS,KAAKtB,WAAA,CAAAO,aAAa,CAACgC,cAAc,IAC9C,IAAI,CAACjB,SAAS,KAAKtB,WAAA,CAAAO,aAAa,CAACiC,aAAa,IAC9C,IAAI,CAAClB,SAAS,KAAKtB,WAAA,CAAAO,aAAa,CAACE,kBAAkB,IACnD,IAAI,CAACa,SAAS,KAAKtB,WAAA,CAAAO,aAAa,CAACC,oBAAoB,KACvD,CAAC,IAAI,CAACU,QAAQ,EACd;MACA,MAAM,IAAIrB,OAAA,CAAA4C,4BAA4B,CAAC,oCAAoC,IAAI,CAACnB,SAAS,GAAG,CAAC;IAC/F;IAEA,IAAI,IAAI,CAACA,SAAS,KAAKtB,WAAA,CAAAO,aAAa,CAACwB,YAAY,EAAE;MACjD,IACE,IAAI,CAACb,QAAQ,IACb,IAAI,CAACM,mBAAmB,CAACkB,WAAW,IACpC,IAAI,CAAClB,mBAAmB,CAACkB,WAAW,KAAK,OAAO,EAChD;QACA,MAAM,IAAI7C,OAAA,CAAA8C,yBAAyB,CACjC,6BAA6B,IAAI,CAACnB,mBAAmB,CAACkB,WAAW,6CAA6C,IAAI,CAACpB,SAAS,IAAI,CACjI;MACH;MAEA,IAAI,IAAI,CAACJ,QAAQ,IAAI,IAAI,CAACC,QAAQ,EAAE;QAClC,MAAM,IAAItB,OAAA,CAAA8C,yBAAyB,CACjC,0CAA0C,IAAI,CAACnB,mBAAmB,CAACkB,WAAW,UAAU,IAAI,CAACpB,SAAS,IAAI,CAC3G;MACH;MAEA,IACE,CAAC,IAAI,CAACE,mBAAmB,CAACkB,WAAW,KAAK,OAAO,IAC/C,IAAI,CAAClB,mBAAmB,CAACkB,WAAW,KAAK,KAAK,KAChD,CAAC,IAAI,CAAClB,mBAAmB,CAACoB,cAAc,EACxC;QACA,MAAM,IAAI/C,OAAA,CAAA8C,yBAAyB,CAAC7B,4BAA4B,CAAC;MACnE;MAEA,IACE,IAAI,CAACU,mBAAmB,CAACkB,WAAW,IACpC,CAAChC,yBAAyB,CAACJ,QAAQ,CAAC,IAAI,CAACkB,mBAAmB,CAACkB,WAAW,CAAC,EACzE;QACA,MAAM,IAAI7C,OAAA,CAAA8C,yBAAyB,CACjC,mCAAmCjC,yBAAyB,CAACmC,IAAI,CAC/D,GAAG,CACJ,gCAAgC,IAAI,CAACvB,SAAS,IAAI,CACpD;MACH;MAEA,IACE,CAAC,IAAI,CAACE,mBAAmB,CAACkB,WAAW,IACrC,CAAC,IAAI,CAAClB,mBAAmB,CAACsB,aAAa,IACvC,CAAC,IAAI,CAACtB,mBAAmB,CAACuB,mBAAmB,EAC7C;QACA,MAAM,IAAIlD,OAAA,CAAA8C,yBAAyB,CACjC,gGAAgG,IAAI,CAACrB,SAAS,IAAI,CACnH;MACH;MAEA,IAAI,IAAI,CAACE,mBAAmB,CAACQ,aAAa,EAAE;QAC1C,MAAMgB,KAAK,GAAG,IAAI,CAACxB,mBAAmB,CAACQ,aAAa;QACpD,IAAI,CAAC7B,KAAK,CAACC,OAAO,CAAC4C,KAAK,CAAC,EAAE;UACzB,MAAM,IAAInD,OAAA,CAAA8C,yBAAyB,CAAChC,mBAAmB,CAAC;QAC1D;QACA,KAAK,MAAMsC,IAAI,IAAID,KAAK,EAAE;UACxB,IAAI,OAAOC,IAAI,KAAK,QAAQ,EAAE;YAC5B,MAAM,IAAIpD,OAAA,CAAA8C,yBAAyB,CAAChC,mBAAmB,CAAC;UAC1D;QACF;MACF;IACF;IAEA,IAAIX,WAAA,CAAAkD,4BAA4B,CAACC,GAAG,CAAC,IAAI,CAAC7B,SAAS,CAAC,EAAE;MACpD,IAAI,IAAI,CAACF,MAAM,IAAI,IAAI,IAAI,IAAI,CAACA,MAAM,KAAK,WAAW,EAAE;QACtD;QACA,MAAM,IAAIvB,OAAA,CAAAuD,aAAa,CACrB,mBAAmB,IAAI,CAAChC,MAAM,oBAAoB,IAAI,CAACE,SAAS,cAAc,CAC/E;MACH;IACF;IAEA,IAAI,IAAI,CAACA,SAAS,KAAKtB,WAAA,CAAAO,aAAa,CAACiC,aAAa,IAAI,IAAI,CAACpB,MAAM,IAAI,IAAI,EAAE;MACzE;MACA,MAAM,IAAIvB,OAAA,CAAAuD,aAAa,CAAC,qDAAqD,CAAC;IAChF;IAEA,IAAI,IAAI,CAAC9B,SAAS,KAAKtB,WAAA,CAAAO,aAAa,CAAC8C,YAAY,IAAI,IAAI,CAAClC,QAAQ,IAAI,IAAI,EAAE;MAC1E,IAAI,IAAI,CAACA,QAAQ,KAAK,EAAE,EAAE;QACxBmC,OAAO,CAACC,GAAG,CAAC,IAAI,EAAE,UAAU,EAAEC,SAAS,CAAC;QACxC;MACF;MACA;MACA,MAAM,IAAI3D,OAAA,CAAAuD,aAAa,CAAC,iDAAiD,CAAC;IAC5E;IAEA,MAAMK,gBAAgB,GAAG,IAAI,CAACjC,mBAAmB,CAACkC,sBAAsB,IAAI,KAAK;IACjF,IAAI,CAACzB,MAAM,CAAC0B,MAAM,CAAC5D,QAAA,CAAA6D,2BAA2B,CAAC,CAACtD,QAAQ,CAACmD,gBAAgB,CAAC,EAAE;MAC1E,MAAM,IAAI5D,OAAA,CAAAuD,aAAa,CAAC,yCAAyCK,gBAAgB,EAAE,CAAC;IACtF;EACF;EAEA,OAAOI,KAAKA,CACVC,KAAmC,EACnC7C,OAAyC;IAEzC,OAAO,IAAIF,gBAAgB,CAAC;MAC1BG,QAAQ,EAAED,OAAO,CAACC,QAAQ,IAAI4C,KAAK,EAAE5C,QAAQ,IAAI,EAAE;MACnDC,QAAQ,EAAEF,OAAO,CAACE,QAAQ,IAAI2C,KAAK,EAAE3C,QAAQ,IAAI,EAAE;MACnDG,SAAS,EAAEL,OAAO,CAACK,SAAS,IAAIwC,KAAK,EAAExC,SAAS,IAAItB,WAAA,CAAAO,aAAa,CAACgB,eAAe;MACjFC,mBAAmB,EAAEP,OAAO,CAACO,mBAAmB,IAAIsC,KAAK,EAAEtC,mBAAmB,IAAI,EAAE;MACpFJ,MAAM,EAAEH,OAAO,CAACG,MAAM,IAAIH,OAAO,CAACI,EAAE,IAAIyC,KAAK,EAAE1C,MAAM,IAAI;KAC1D,CAAC;EACJ;;AAlMFR,OAAA,CAAAG,gBAAA,GAAAA,gBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}