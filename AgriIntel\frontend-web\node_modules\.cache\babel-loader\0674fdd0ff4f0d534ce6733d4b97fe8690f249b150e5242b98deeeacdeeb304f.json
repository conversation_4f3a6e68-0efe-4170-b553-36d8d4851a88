{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.InsertManyOperation = exports.InsertOneOperation = exports.InsertOperation = void 0;\nconst error_1 = require(\"../error\");\nconst utils_1 = require(\"../utils\");\nconst write_concern_1 = require(\"../write_concern\");\nconst bulk_write_1 = require(\"./bulk_write\");\nconst command_1 = require(\"./command\");\nconst operation_1 = require(\"./operation\");\n/** @internal */\nclass InsertOperation extends command_1.CommandOperation {\n  constructor(ns, documents, options) {\n    super(undefined, options);\n    this.options = {\n      ...options,\n      checkKeys: options.checkKeys ?? false\n    };\n    this.ns = ns;\n    this.documents = documents;\n  }\n  get commandName() {\n    return 'insert';\n  }\n  async execute(server, session, timeoutContext) {\n    const options = this.options ?? {};\n    const ordered = typeof options.ordered === 'boolean' ? options.ordered : true;\n    const command = {\n      insert: this.ns.collection,\n      documents: this.documents,\n      ordered\n    };\n    if (typeof options.bypassDocumentValidation === 'boolean') {\n      command.bypassDocumentValidation = options.bypassDocumentValidation;\n    }\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (options.comment !== undefined) {\n      command.comment = options.comment;\n    }\n    return await super.executeCommand(server, session, command, timeoutContext);\n  }\n}\nexports.InsertOperation = InsertOperation;\nclass InsertOneOperation extends InsertOperation {\n  constructor(collection, doc, options) {\n    super(collection.s.namespace, (0, utils_1.maybeAddIdToDocuments)(collection, [doc], options), options);\n  }\n  async execute(server, session, timeoutContext) {\n    const res = await super.execute(server, session, timeoutContext);\n    if (res.code) throw new error_1.MongoServerError(res);\n    if (res.writeErrors) {\n      // This should be a WriteError but we can't change it now because of error hierarchy\n      throw new error_1.MongoServerError(res.writeErrors[0]);\n    }\n    return {\n      acknowledged: this.writeConcern?.w !== 0,\n      insertedId: this.documents[0]._id\n    };\n  }\n}\nexports.InsertOneOperation = InsertOneOperation;\n/** @internal */\nclass InsertManyOperation extends operation_1.AbstractOperation {\n  constructor(collection, docs, options) {\n    super(options);\n    if (!Array.isArray(docs)) {\n      throw new error_1.MongoInvalidArgumentError('Argument \"docs\" must be an array of documents');\n    }\n    this.options = options;\n    this.collection = collection;\n    this.docs = docs;\n  }\n  get commandName() {\n    return 'insert';\n  }\n  async execute(server, session, timeoutContext) {\n    const coll = this.collection;\n    const options = {\n      ...this.options,\n      ...this.bsonOptions,\n      readPreference: this.readPreference\n    };\n    const writeConcern = write_concern_1.WriteConcern.fromOptions(options);\n    const bulkWriteOperation = new bulk_write_1.BulkWriteOperation(coll, this.docs.map(document => ({\n      insertOne: {\n        document\n      }\n    })), options);\n    try {\n      const res = await bulkWriteOperation.execute(server, session, timeoutContext);\n      return {\n        acknowledged: writeConcern?.w !== 0,\n        insertedCount: res.insertedCount,\n        insertedIds: res.insertedIds\n      };\n    } catch (err) {\n      if (err && err.message === 'Operation must be an object with an operation key') {\n        throw new error_1.MongoInvalidArgumentError('Collection.insertMany() cannot be called with an array that has null/undefined values');\n      }\n      throw err;\n    }\n  }\n}\nexports.InsertManyOperation = InsertManyOperation;\n(0, operation_1.defineAspects)(InsertOperation, [operation_1.Aspect.RETRYABLE, operation_1.Aspect.WRITE_OPERATION]);\n(0, operation_1.defineAspects)(InsertOneOperation, [operation_1.Aspect.RETRYABLE, operation_1.Aspect.WRITE_OPERATION]);\n(0, operation_1.defineAspects)(InsertManyOperation, [operation_1.Aspect.WRITE_OPERATION]);", "map": {"version": 3, "names": ["error_1", "require", "utils_1", "write_concern_1", "bulk_write_1", "command_1", "operation_1", "InsertOperation", "CommandOperation", "constructor", "ns", "documents", "options", "undefined", "checkKeys", "commandName", "execute", "server", "session", "timeoutContext", "ordered", "command", "insert", "collection", "bypassDocumentValidation", "comment", "executeCommand", "exports", "InsertOneOperation", "doc", "s", "namespace", "maybeAddIdToDocuments", "res", "code", "MongoServerError", "writeErrors", "acknowledged", "writeConcern", "w", "insertedId", "_id", "InsertManyOperation", "AbstractOperation", "docs", "Array", "isArray", "MongoInvalidArgumentError", "coll", "bsonOptions", "readPreference", "WriteConcern", "fromOptions", "bulkWriteOperation", "BulkWriteOperation", "map", "document", "insertOne", "insertedCount", "insertedIds", "err", "message", "defineAspects", "Aspect", "RETRYABLE", "WRITE_OPERATION"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\insert.ts"], "sourcesContent": ["import type { Document } from '../bson';\nimport type { BulkWriteOptions } from '../bulk/common';\nimport type { Collection } from '../collection';\nimport { MongoInvalidArgumentError, MongoServerError } from '../error';\nimport type { InferIdType } from '../mongo_types';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { maybeAddIdToDocuments, type MongoDBNamespace } from '../utils';\nimport { WriteConcern } from '../write_concern';\nimport { BulkWriteOperation } from './bulk_write';\nimport { CommandOperation, type CommandOperationOptions } from './command';\nimport { AbstractOperation, Aspect, defineAspects } from './operation';\n\n/** @internal */\nexport class InsertOperation extends CommandOperation<Document> {\n  override options: BulkWriteOptions;\n  documents: Document[];\n\n  constructor(ns: MongoDBNamespace, documents: Document[], options: BulkWriteOptions) {\n    super(undefined, options);\n    this.options = { ...options, checkKeys: options.checkKeys ?? false };\n    this.ns = ns;\n    this.documents = documents;\n  }\n\n  override get commandName() {\n    return 'insert' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<Document> {\n    const options = this.options ?? {};\n    const ordered = typeof options.ordered === 'boolean' ? options.ordered : true;\n    const command: Document = {\n      insert: this.ns.collection,\n      documents: this.documents,\n      ordered\n    };\n\n    if (typeof options.bypassDocumentValidation === 'boolean') {\n      command.bypassDocumentValidation = options.bypassDocumentValidation;\n    }\n\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (options.comment !== undefined) {\n      command.comment = options.comment;\n    }\n\n    return await super.executeCommand(server, session, command, timeoutContext);\n  }\n}\n\n/** @public */\nexport interface InsertOneOptions extends CommandOperationOptions {\n  /** Allow driver to bypass schema validation. */\n  bypassDocumentValidation?: boolean;\n  /** Force server to assign _id values instead of driver. */\n  forceServerObjectId?: boolean;\n}\n\n/** @public */\nexport interface InsertOneResult<TSchema = Document> {\n  /** Indicates whether this write result was acknowledged. If not, then all other members of this result will be undefined */\n  acknowledged: boolean;\n  /** The identifier that was inserted. If the server generated the identifier, this value will be null as the driver does not have access to that data */\n  insertedId: InferIdType<TSchema>;\n}\n\nexport class InsertOneOperation extends InsertOperation {\n  constructor(collection: Collection, doc: Document, options: InsertOneOptions) {\n    super(collection.s.namespace, maybeAddIdToDocuments(collection, [doc], options), options);\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<InsertOneResult> {\n    const res = await super.execute(server, session, timeoutContext);\n    if (res.code) throw new MongoServerError(res);\n    if (res.writeErrors) {\n      // This should be a WriteError but we can't change it now because of error hierarchy\n      throw new MongoServerError(res.writeErrors[0]);\n    }\n\n    return {\n      acknowledged: this.writeConcern?.w !== 0,\n      insertedId: this.documents[0]._id\n    };\n  }\n}\n\n/** @public */\nexport interface InsertManyResult<TSchema = Document> {\n  /** Indicates whether this write result was acknowledged. If not, then all other members of this result will be undefined */\n  acknowledged: boolean;\n  /** The number of inserted documents for this operations */\n  insertedCount: number;\n  /** Map of the index of the inserted document to the id of the inserted document */\n  insertedIds: { [key: number]: InferIdType<TSchema> };\n}\n\n/** @internal */\nexport class InsertManyOperation extends AbstractOperation<InsertManyResult> {\n  override options: BulkWriteOptions;\n  collection: Collection;\n  docs: ReadonlyArray<Document>;\n\n  constructor(collection: Collection, docs: ReadonlyArray<Document>, options: BulkWriteOptions) {\n    super(options);\n\n    if (!Array.isArray(docs)) {\n      throw new MongoInvalidArgumentError('Argument \"docs\" must be an array of documents');\n    }\n\n    this.options = options;\n    this.collection = collection;\n    this.docs = docs;\n  }\n\n  override get commandName() {\n    return 'insert' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<InsertManyResult> {\n    const coll = this.collection;\n    const options = { ...this.options, ...this.bsonOptions, readPreference: this.readPreference };\n    const writeConcern = WriteConcern.fromOptions(options);\n    const bulkWriteOperation = new BulkWriteOperation(\n      coll,\n      this.docs.map(document => ({\n        insertOne: { document }\n      })),\n      options\n    );\n\n    try {\n      const res = await bulkWriteOperation.execute(server, session, timeoutContext);\n      return {\n        acknowledged: writeConcern?.w !== 0,\n        insertedCount: res.insertedCount,\n        insertedIds: res.insertedIds\n      };\n    } catch (err) {\n      if (err && err.message === 'Operation must be an object with an operation key') {\n        throw new MongoInvalidArgumentError(\n          'Collection.insertMany() cannot be called with an array that has null/undefined values'\n        );\n      }\n      throw err;\n    }\n  }\n}\n\ndefineAspects(InsertOperation, [Aspect.RETRYABLE, Aspect.WRITE_OPERATION]);\ndefineAspects(InsertOneOperation, [Aspect.RETRYABLE, Aspect.WRITE_OPERATION]);\ndefineAspects(InsertManyOperation, [Aspect.WRITE_OPERATION]);\n"], "mappings": ";;;;;;AAGA,MAAAA,OAAA,GAAAC,OAAA;AAKA,MAAAC,OAAA,GAAAD,OAAA;AACA,MAAAE,eAAA,GAAAF,OAAA;AACA,MAAAG,YAAA,GAAAH,OAAA;AACA,MAAAI,SAAA,GAAAJ,OAAA;AACA,MAAAK,WAAA,GAAAL,OAAA;AAEA;AACA,MAAaM,eAAgB,SAAQF,SAAA,CAAAG,gBAA0B;EAI7DC,YAAYC,EAAoB,EAAEC,SAAqB,EAAEC,OAAyB;IAChF,KAAK,CAACC,SAAS,EAAED,OAAO,CAAC;IACzB,IAAI,CAACA,OAAO,GAAG;MAAE,GAAGA,OAAO;MAAEE,SAAS,EAAEF,OAAO,CAACE,SAAS,IAAI;IAAK,CAAE;IACpE,IAAI,CAACJ,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC5B;EAEA,IAAaI,WAAWA,CAAA;IACtB,OAAO,QAAiB;EAC1B;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMP,OAAO,GAAG,IAAI,CAACA,OAAO,IAAI,EAAE;IAClC,MAAMQ,OAAO,GAAG,OAAOR,OAAO,CAACQ,OAAO,KAAK,SAAS,GAAGR,OAAO,CAACQ,OAAO,GAAG,IAAI;IAC7E,MAAMC,OAAO,GAAa;MACxBC,MAAM,EAAE,IAAI,CAACZ,EAAE,CAACa,UAAU;MAC1BZ,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBS;KACD;IAED,IAAI,OAAOR,OAAO,CAACY,wBAAwB,KAAK,SAAS,EAAE;MACzDH,OAAO,CAACG,wBAAwB,GAAGZ,OAAO,CAACY,wBAAwB;IACrE;IAEA;IACA;IACA,IAAIZ,OAAO,CAACa,OAAO,KAAKZ,SAAS,EAAE;MACjCQ,OAAO,CAACI,OAAO,GAAGb,OAAO,CAACa,OAAO;IACnC;IAEA,OAAO,MAAM,KAAK,CAACC,cAAc,CAACT,MAAM,EAAEC,OAAO,EAAEG,OAAO,EAAEF,cAAc,CAAC;EAC7E;;AAvCFQ,OAAA,CAAApB,eAAA,GAAAA,eAAA;AA0DA,MAAaqB,kBAAmB,SAAQrB,eAAe;EACrDE,YAAYc,UAAsB,EAAEM,GAAa,EAAEjB,OAAyB;IAC1E,KAAK,CAACW,UAAU,CAACO,CAAC,CAACC,SAAS,EAAE,IAAA7B,OAAA,CAAA8B,qBAAqB,EAACT,UAAU,EAAE,CAACM,GAAG,CAAC,EAAEjB,OAAO,CAAC,EAAEA,OAAO,CAAC;EAC3F;EAES,MAAMI,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMc,GAAG,GAAG,MAAM,KAAK,CAACjB,OAAO,CAACC,MAAM,EAAEC,OAAO,EAAEC,cAAc,CAAC;IAChE,IAAIc,GAAG,CAACC,IAAI,EAAE,MAAM,IAAIlC,OAAA,CAAAmC,gBAAgB,CAACF,GAAG,CAAC;IAC7C,IAAIA,GAAG,CAACG,WAAW,EAAE;MACnB;MACA,MAAM,IAAIpC,OAAA,CAAAmC,gBAAgB,CAACF,GAAG,CAACG,WAAW,CAAC,CAAC,CAAC,CAAC;IAChD;IAEA,OAAO;MACLC,YAAY,EAAE,IAAI,CAACC,YAAY,EAAEC,CAAC,KAAK,CAAC;MACxCC,UAAU,EAAE,IAAI,CAAC7B,SAAS,CAAC,CAAC,CAAC,CAAC8B;KAC/B;EACH;;AArBFd,OAAA,CAAAC,kBAAA,GAAAA,kBAAA;AAkCA;AACA,MAAac,mBAAoB,SAAQpC,WAAA,CAAAqC,iBAAmC;EAK1ElC,YAAYc,UAAsB,EAAEqB,IAA6B,EAAEhC,OAAyB;IAC1F,KAAK,CAACA,OAAO,CAAC;IAEd,IAAI,CAACiC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,EAAE;MACxB,MAAM,IAAI5C,OAAA,CAAA+C,yBAAyB,CAAC,+CAA+C,CAAC;IACtF;IAEA,IAAI,CAACnC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACW,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACqB,IAAI,GAAGA,IAAI;EAClB;EAEA,IAAa7B,WAAWA,CAAA;IACtB,OAAO,QAAiB;EAC1B;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAM6B,IAAI,GAAG,IAAI,CAACzB,UAAU;IAC5B,MAAMX,OAAO,GAAG;MAAE,GAAG,IAAI,CAACA,OAAO;MAAE,GAAG,IAAI,CAACqC,WAAW;MAAEC,cAAc,EAAE,IAAI,CAACA;IAAc,CAAE;IAC7F,MAAMZ,YAAY,GAAGnC,eAAA,CAAAgD,YAAY,CAACC,WAAW,CAACxC,OAAO,CAAC;IACtD,MAAMyC,kBAAkB,GAAG,IAAIjD,YAAA,CAAAkD,kBAAkB,CAC/CN,IAAI,EACJ,IAAI,CAACJ,IAAI,CAACW,GAAG,CAACC,QAAQ,KAAK;MACzBC,SAAS,EAAE;QAAED;MAAQ;KACtB,CAAC,CAAC,EACH5C,OAAO,CACR;IAED,IAAI;MACF,MAAMqB,GAAG,GAAG,MAAMoB,kBAAkB,CAACrC,OAAO,CAACC,MAAM,EAAEC,OAAO,EAAEC,cAAc,CAAC;MAC7E,OAAO;QACLkB,YAAY,EAAEC,YAAY,EAAEC,CAAC,KAAK,CAAC;QACnCmB,aAAa,EAAEzB,GAAG,CAACyB,aAAa;QAChCC,WAAW,EAAE1B,GAAG,CAAC0B;OAClB;IACH,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZ,IAAIA,GAAG,IAAIA,GAAG,CAACC,OAAO,KAAK,mDAAmD,EAAE;QAC9E,MAAM,IAAI7D,OAAA,CAAA+C,yBAAyB,CACjC,uFAAuF,CACxF;MACH;MACA,MAAMa,GAAG;IACX;EACF;;AApDFjC,OAAA,CAAAe,mBAAA,GAAAA,mBAAA;AAuDA,IAAApC,WAAA,CAAAwD,aAAa,EAACvD,eAAe,EAAE,CAACD,WAAA,CAAAyD,MAAM,CAACC,SAAS,EAAE1D,WAAA,CAAAyD,MAAM,CAACE,eAAe,CAAC,CAAC;AAC1E,IAAA3D,WAAA,CAAAwD,aAAa,EAAClC,kBAAkB,EAAE,CAACtB,WAAA,CAAAyD,MAAM,CAACC,SAAS,EAAE1D,WAAA,CAAAyD,MAAM,CAACE,eAAe,CAAC,CAAC;AAC7E,IAAA3D,WAAA,CAAAwD,aAAa,EAACpB,mBAAmB,EAAE,CAACpC,WAAA,CAAAyD,MAAM,CAACE,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}