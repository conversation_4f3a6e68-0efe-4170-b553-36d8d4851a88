{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ListSearchIndexesCursor = void 0;\nconst aggregation_cursor_1 = require(\"./aggregation_cursor\");\n/** @public */\nclass ListSearchIndexesCursor extends aggregation_cursor_1.AggregationCursor {\n  /** @internal */\n  constructor({\n    fullNamespace: ns,\n    client\n  }, name, options = {}) {\n    const pipeline = name == null ? [{\n      $listSearchIndexes: {}\n    }] : [{\n      $listSearchIndexes: {\n        name\n      }\n    }];\n    super(client, ns, pipeline, options);\n  }\n}\nexports.ListSearchIndexesCursor = ListSearchIndexesCursor;", "map": {"version": 3, "names": ["aggregation_cursor_1", "require", "ListSearchIndexesCursor", "AggregationCursor", "constructor", "fullNamespace", "ns", "client", "name", "options", "pipeline", "$listSearchIndexes", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cursor\\list_search_indexes_cursor.ts"], "sourcesContent": ["import type { Collection } from '../collection';\nimport type { AggregateOptions } from '../operations/aggregate';\nimport { AggregationCursor } from './aggregation_cursor';\n\n/** @public */\nexport type ListSearchIndexesOptions = Omit<AggregateOptions, 'readConcern' | 'writeConcern'>;\n\n/** @public */\nexport class ListSearchIndexesCursor extends AggregationCursor<{ name: string }> {\n  /** @internal */\n  constructor(\n    { fullNamespace: ns, client }: Collection,\n    name: string | null,\n    options: ListSearchIndexesOptions = {}\n  ) {\n    const pipeline =\n      name == null ? [{ $listSearchIndexes: {} }] : [{ $listSearchIndexes: { name } }];\n    super(client, ns, pipeline, options);\n  }\n}\n"], "mappings": ";;;;;;AAEA,MAAAA,oBAAA,GAAAC,OAAA;AAKA;AACA,MAAaC,uBAAwB,SAAQF,oBAAA,CAAAG,iBAAmC;EAC9E;EACAC,YACE;IAAEC,aAAa,EAAEC,EAAE;IAAEC;EAAM,CAAc,EACzCC,IAAmB,EACnBC,OAAA,GAAoC,EAAE;IAEtC,MAAMC,QAAQ,GACZF,IAAI,IAAI,IAAI,GAAG,CAAC;MAAEG,kBAAkB,EAAE;IAAE,CAAE,CAAC,GAAG,CAAC;MAAEA,kBAAkB,EAAE;QAAEH;MAAI;IAAE,CAAE,CAAC;IAClF,KAAK,CAACD,MAAM,EAAED,EAAE,EAAEI,QAAQ,EAAED,OAAO,CAAC;EACtC;;AAVFG,OAAA,CAAAV,uBAAA,GAAAA,uBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}