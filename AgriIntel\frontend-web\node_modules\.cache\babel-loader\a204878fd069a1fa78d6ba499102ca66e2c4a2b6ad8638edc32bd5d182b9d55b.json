{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.MongocryptdManager = void 0;\nconst error_1 = require(\"../error\");\n/**\n * @internal\n * An internal class that handles spawning a mongocryptd.\n */\nclass MongocryptdManager {\n  constructor(extraOptions = {}) {\n    this.spawnPath = '';\n    this.spawnArgs = [];\n    this.uri = typeof extraOptions.mongocryptdURI === 'string' && extraOptions.mongocryptdURI.length > 0 ? extraOptions.mongocryptdURI : MongocryptdManager.DEFAULT_MONGOCRYPTD_URI;\n    this.bypassSpawn = !!extraOptions.mongocryptdBypassSpawn;\n    if (Object.hasOwn(extraOptions, 'mongocryptdSpawnPath') && extraOptions.mongocryptdSpawnPath) {\n      this.spawnPath = extraOptions.mongocryptdSpawnPath;\n    }\n    if (Object.hasOwn(extraOptions, 'mongocryptdSpawnArgs') && Array.isArray(extraOptions.mongocryptdSpawnArgs)) {\n      this.spawnArgs = this.spawnArgs.concat(extraOptions.mongocryptdSpawnArgs);\n    }\n    if (this.spawnArgs.filter(arg => typeof arg === 'string').every(arg => arg.indexOf('--idleShutdownTimeoutSecs') < 0)) {\n      this.spawnArgs.push('--idleShutdownTimeoutSecs', '60');\n    }\n  }\n  /**\n   * Will check to see if a mongocryptd is up. If it is not up, it will attempt\n   * to spawn a mongocryptd in a detached process, and then wait for it to be up.\n   */\n  async spawn() {\n    const cmdName = this.spawnPath || 'mongocryptd';\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    const {\n      spawn\n    } = require('child_process');\n    // Spawned with stdio: ignore and detached: true\n    // to ensure child can outlive parent.\n    this._child = spawn(cmdName, this.spawnArgs, {\n      stdio: 'ignore',\n      detached: true\n    });\n    this._child.on('error', () => {\n      // From the FLE spec:\n      // \"The stdout and stderr of the spawned process MUST not be exposed in the driver\n      // (e.g. redirect to /dev/null). Users can pass the argument --logpath to\n      // extraOptions.mongocryptdSpawnArgs if they need to inspect mongocryptd logs.\n      // If spawning is necessary, the driver MUST spawn mongocryptd whenever server\n      // selection on the MongoClient to mongocryptd fails. If the MongoClient fails to\n      // connect after spawning, the server selection error is propagated to the user.\"\n      // The AutoEncrypter and MongoCryptdManager should work together to spawn\n      // mongocryptd whenever necessary.  Additionally, the `mongocryptd` intentionally\n      // shuts down after 60s and gets respawned when necessary.  We rely on server\n      // selection timeouts when connecting to the `mongocryptd` to inform users that something\n      // has been configured incorrectly.  For those reasons, we suppress stderr from\n      // the `mongocryptd` process and immediately unref the process.\n    });\n    // unref child to remove handle from event loop\n    this._child.unref();\n  }\n  /**\n   * @returns the result of `fn` or rejects with an error.\n   */\n  async withRespawn(fn) {\n    try {\n      const result = await fn();\n      return result;\n    } catch (err) {\n      // If we are not bypassing spawning, then we should retry once on a MongoTimeoutError (server selection error)\n      const shouldSpawn = err instanceof error_1.MongoNetworkTimeoutError && !this.bypassSpawn;\n      if (!shouldSpawn) {\n        throw err;\n      }\n    }\n    await this.spawn();\n    const result = await fn();\n    return result;\n  }\n}\nexports.MongocryptdManager = MongocryptdManager;\nMongocryptdManager.DEFAULT_MONGOCRYPTD_URI = 'mongodb://localhost:27020';", "map": {"version": 3, "names": ["error_1", "require", "MongocryptdManager", "constructor", "extraOptions", "spawnPath", "spawnArgs", "uri", "mongocryptdURI", "length", "DEFAULT_MONGOCRYPTD_URI", "bypassSpawn", "mongocryptdBypassSpawn", "Object", "hasOwn", "mongocryptdSpawnPath", "Array", "isArray", "mongocryptdSpawnArgs", "concat", "filter", "arg", "every", "indexOf", "push", "spawn", "cmdName", "_child", "stdio", "detached", "on", "unref", "withRespawn", "fn", "result", "err", "shouldSpawn", "MongoNetworkTimeoutError", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\client-side-encryption\\mongocryptd_manager.ts"], "sourcesContent": ["import type { ChildProcess } from 'child_process';\n\nimport { MongoNetworkTimeoutError } from '../error';\nimport { type AutoEncryptionExtraOptions } from './auto_encrypter';\n\n/**\n * @internal\n * An internal class that handles spawning a mongocryptd.\n */\nexport class MongocryptdManager {\n  static DEFAULT_MONGOCRYPTD_URI = 'mongodb://localhost:27020';\n\n  uri: string;\n  bypassSpawn: boolean;\n  spawnPath = '';\n  spawnArgs: Array<string> = [];\n  _child?: ChildProcess;\n\n  constructor(extraOptions: AutoEncryptionExtraOptions = {}) {\n    this.uri =\n      typeof extraOptions.mongocryptdURI === 'string' && extraOptions.mongocryptdURI.length > 0\n        ? extraOptions.mongocryptdURI\n        : MongocryptdManager.DEFAULT_MONGOCRYPTD_URI;\n\n    this.bypassSpawn = !!extraOptions.mongocryptdBypassSpawn;\n\n    if (Object.hasOwn(extraOptions, 'mongocryptdSpawnPath') && extraOptions.mongocryptdSpawnPath) {\n      this.spawnPath = extraOptions.mongocryptdSpawnPath;\n    }\n    if (\n      Object.hasOwn(extraOptions, 'mongocryptdSpawnArgs') &&\n      Array.isArray(extraOptions.mongocryptdSpawnArgs)\n    ) {\n      this.spawnArgs = this.spawnArgs.concat(extraOptions.mongocryptdSpawnArgs);\n    }\n    if (\n      this.spawnArgs\n        .filter(arg => typeof arg === 'string')\n        .every(arg => arg.indexOf('--idleShutdownTimeoutSecs') < 0)\n    ) {\n      this.spawnArgs.push('--idleShutdownTimeoutSecs', '60');\n    }\n  }\n\n  /**\n   * Will check to see if a mongocryptd is up. If it is not up, it will attempt\n   * to spawn a mongocryptd in a detached process, and then wait for it to be up.\n   */\n  async spawn(): Promise<void> {\n    const cmdName = this.spawnPath || 'mongocryptd';\n\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    const { spawn } = require('child_process') as typeof import('child_process');\n\n    // Spawned with stdio: ignore and detached: true\n    // to ensure child can outlive parent.\n    this._child = spawn(cmdName, this.spawnArgs, {\n      stdio: 'ignore',\n      detached: true\n    });\n\n    this._child.on('error', () => {\n      // From the FLE spec:\n      // \"The stdout and stderr of the spawned process MUST not be exposed in the driver\n      // (e.g. redirect to /dev/null). Users can pass the argument --logpath to\n      // extraOptions.mongocryptdSpawnArgs if they need to inspect mongocryptd logs.\n      // If spawning is necessary, the driver MUST spawn mongocryptd whenever server\n      // selection on the MongoClient to mongocryptd fails. If the MongoClient fails to\n      // connect after spawning, the server selection error is propagated to the user.\"\n      // The AutoEncrypter and MongoCryptdManager should work together to spawn\n      // mongocryptd whenever necessary.  Additionally, the `mongocryptd` intentionally\n      // shuts down after 60s and gets respawned when necessary.  We rely on server\n      // selection timeouts when connecting to the `mongocryptd` to inform users that something\n      // has been configured incorrectly.  For those reasons, we suppress stderr from\n      // the `mongocryptd` process and immediately unref the process.\n    });\n\n    // unref child to remove handle from event loop\n    this._child.unref();\n  }\n\n  /**\n   * @returns the result of `fn` or rejects with an error.\n   */\n  async withRespawn<T>(fn: () => Promise<T>): ReturnType<typeof fn> {\n    try {\n      const result = await fn();\n      return result;\n    } catch (err) {\n      // If we are not bypassing spawning, then we should retry once on a MongoTimeoutError (server selection error)\n      const shouldSpawn = err instanceof MongoNetworkTimeoutError && !this.bypassSpawn;\n      if (!shouldSpawn) {\n        throw err;\n      }\n    }\n    await this.spawn();\n    const result = await fn();\n    return result;\n  }\n}\n"], "mappings": ";;;;;;AAEA,MAAAA,OAAA,GAAAC,OAAA;AAGA;;;;AAIA,MAAaC,kBAAkB;EAS7BC,YAAYC,YAAA,GAA2C,EAAE;IAJzD,KAAAC,SAAS,GAAG,EAAE;IACd,KAAAC,SAAS,GAAkB,EAAE;IAI3B,IAAI,CAACC,GAAG,GACN,OAAOH,YAAY,CAACI,cAAc,KAAK,QAAQ,IAAIJ,YAAY,CAACI,cAAc,CAACC,MAAM,GAAG,CAAC,GACrFL,YAAY,CAACI,cAAc,GAC3BN,kBAAkB,CAACQ,uBAAuB;IAEhD,IAAI,CAACC,WAAW,GAAG,CAAC,CAACP,YAAY,CAACQ,sBAAsB;IAExD,IAAIC,MAAM,CAACC,MAAM,CAACV,YAAY,EAAE,sBAAsB,CAAC,IAAIA,YAAY,CAACW,oBAAoB,EAAE;MAC5F,IAAI,CAACV,SAAS,GAAGD,YAAY,CAACW,oBAAoB;IACpD;IACA,IACEF,MAAM,CAACC,MAAM,CAACV,YAAY,EAAE,sBAAsB,CAAC,IACnDY,KAAK,CAACC,OAAO,CAACb,YAAY,CAACc,oBAAoB,CAAC,EAChD;MACA,IAAI,CAACZ,SAAS,GAAG,IAAI,CAACA,SAAS,CAACa,MAAM,CAACf,YAAY,CAACc,oBAAoB,CAAC;IAC3E;IACA,IACE,IAAI,CAACZ,SAAS,CACXc,MAAM,CAACC,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,CAAC,CACtCC,KAAK,CAACD,GAAG,IAAIA,GAAG,CAACE,OAAO,CAAC,2BAA2B,CAAC,GAAG,CAAC,CAAC,EAC7D;MACA,IAAI,CAACjB,SAAS,CAACkB,IAAI,CAAC,2BAA2B,EAAE,IAAI,CAAC;IACxD;EACF;EAEA;;;;EAIA,MAAMC,KAAKA,CAAA;IACT,MAAMC,OAAO,GAAG,IAAI,CAACrB,SAAS,IAAI,aAAa;IAE/C;IACA,MAAM;MAAEoB;IAAK,CAAE,GAAGxB,OAAO,CAAC,eAAe,CAAmC;IAE5E;IACA;IACA,IAAI,CAAC0B,MAAM,GAAGF,KAAK,CAACC,OAAO,EAAE,IAAI,CAACpB,SAAS,EAAE;MAC3CsB,KAAK,EAAE,QAAQ;MACfC,QAAQ,EAAE;KACX,CAAC;IAEF,IAAI,CAACF,MAAM,CAACG,EAAE,CAAC,OAAO,EAAE,MAAK;MAC3B;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAAA,CACD,CAAC;IAEF;IACA,IAAI,CAACH,MAAM,CAACI,KAAK,EAAE;EACrB;EAEA;;;EAGA,MAAMC,WAAWA,CAAIC,EAAoB;IACvC,IAAI;MACF,MAAMC,MAAM,GAAG,MAAMD,EAAE,EAAE;MACzB,OAAOC,MAAM;IACf,CAAC,CAAC,OAAOC,GAAG,EAAE;MACZ;MACA,MAAMC,WAAW,GAAGD,GAAG,YAAYnC,OAAA,CAAAqC,wBAAwB,IAAI,CAAC,IAAI,CAAC1B,WAAW;MAChF,IAAI,CAACyB,WAAW,EAAE;QAChB,MAAMD,GAAG;MACX;IACF;IACA,MAAM,IAAI,CAACV,KAAK,EAAE;IAClB,MAAMS,MAAM,GAAG,MAAMD,EAAE,EAAE;IACzB,OAAOC,MAAM;EACf;;AAzFFI,OAAA,CAAApC,kBAAA,GAAAA,kBAAA;AACSA,kBAAA,CAAAQ,uBAAuB,GAAG,2BAA2B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}