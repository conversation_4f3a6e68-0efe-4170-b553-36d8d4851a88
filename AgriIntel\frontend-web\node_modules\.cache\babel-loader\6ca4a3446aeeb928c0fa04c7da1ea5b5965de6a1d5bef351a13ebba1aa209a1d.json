{"ast": null, "code": "import axios from 'axios';\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';\n\n// Create axios instance\nconst apiClient = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json'\n  }\n});\n\n// Request interceptor to add auth token\napiClient.interceptors.request.use(config => {\n  const token = localStorage.getItem('token');\n  if (token) {\n    config.headers.Authorization = `Bearer ${token}`;\n  }\n  return config;\n}, error => {\n  return Promise.reject(error);\n});\n\n// Response interceptor to handle token refresh\napiClient.interceptors.response.use(response => response, async error => {\n  var _error$response;\n  const originalRequest = error.config;\n  if (((_error$response = error.response) === null || _error$response === void 0 ? void 0 : _error$response.status) === 401 && !originalRequest._retry) {\n    originalRequest._retry = true;\n    try {\n      const refreshToken = localStorage.getItem('refreshToken');\n      if (refreshToken) {\n        const response = await axios.post(`${API_BASE_URL}/auth/refresh-token`, {\n          refreshToken\n        });\n        const {\n          token,\n          refreshToken: newRefreshToken\n        } = response.data.data;\n        localStorage.setItem('token', token);\n        localStorage.setItem('refreshToken', newRefreshToken);\n\n        // Retry original request with new token\n        originalRequest.headers.Authorization = `Bearer ${token}`;\n        return apiClient(originalRequest);\n      }\n    } catch (refreshError) {\n      // Refresh failed, redirect to login\n      localStorage.removeItem('token');\n      localStorage.removeItem('refreshToken');\n      window.location.href = '/login';\n      return Promise.reject(refreshError);\n    }\n  }\n  return Promise.reject(error);\n});\nexport const authAPI = {\n  // Login user\n  login: async credentials => {\n    const response = await apiClient.post('/auth/login', credentials);\n    return response.data;\n  },\n  // Register user\n  register: async userData => {\n    const response = await apiClient.post('/auth/register', userData);\n    return response.data;\n  },\n  // Get current user\n  getCurrentUser: async () => {\n    const response = await apiClient.get('/auth/me');\n    return response.data;\n  },\n  // Refresh token\n  refreshToken: async refreshToken => {\n    const response = await apiClient.post('/auth/refresh-token', {\n      refreshToken\n    });\n    return response.data;\n  },\n  // Logout user\n  logout: async refreshToken => {\n    const response = await apiClient.post('/auth/logout', {\n      refreshToken\n    });\n    return response.data;\n  },\n  // Change password\n  changePassword: async passwordData => {\n    const response = await apiClient.post('/auth/change-password', passwordData);\n    return response.data;\n  },\n  // Forgot password\n  forgotPassword: async email => {\n    const response = await apiClient.post('/auth/forgot-password', {\n      email\n    });\n    return response.data;\n  },\n  // Reset password\n  resetPassword: async (token, newPassword) => {\n    const response = await apiClient.post('/auth/reset-password', {\n      token,\n      newPassword\n    });\n    return response.data;\n  },\n  // Verify email\n  verifyEmail: async token => {\n    const response = await apiClient.post('/auth/verify-email', {\n      token\n    });\n    return response.data;\n  },\n  // Resend verification email\n  resendVerificationEmail: async () => {\n    const response = await apiClient.post('/auth/resend-verification');\n    return response.data;\n  }\n};\nexport default apiClient;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "apiClient", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "error", "Promise", "reject", "response", "_error$response", "originalRequest", "status", "_retry", "refreshToken", "post", "newRefreshToken", "data", "setItem", "refreshError", "removeItem", "window", "location", "href", "authAPI", "login", "credentials", "register", "userData", "getCurrentUser", "get", "logout", "changePassword", "passwordData", "forgotPassword", "email", "resetPassword", "newPassword", "verifyEmail", "resendVerificationEmail"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/services/api/authAPI.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';\n\n// Create axios instance\nconst apiClient = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napiClient.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle token refresh\napiClient.interceptors.response.use(\n  (response) => response,\n  async (error) => {\n    const originalRequest = error.config;\n\n    if (error.response?.status === 401 && !originalRequest._retry) {\n      originalRequest._retry = true;\n\n      try {\n        const refreshToken = localStorage.getItem('refreshToken');\n        if (refreshToken) {\n          const response = await axios.post(`${API_BASE_URL}/auth/refresh-token`, {\n            refreshToken,\n          });\n\n          const { token, refreshToken: newRefreshToken } = response.data.data;\n          localStorage.setItem('token', token);\n          localStorage.setItem('refreshToken', newRefreshToken);\n\n          // Retry original request with new token\n          originalRequest.headers.Authorization = `Bearer ${token}`;\n          return apiClient(originalRequest);\n        }\n      } catch (refreshError) {\n        // Refresh failed, redirect to login\n        localStorage.removeItem('token');\n        localStorage.removeItem('refreshToken');\n        window.location.href = '/login';\n        return Promise.reject(refreshError);\n      }\n    }\n\n    return Promise.reject(error);\n  }\n);\n\nexport interface LoginCredentials {\n  username: string;\n  password: string;\n}\n\nexport interface RegisterData {\n  username: string;\n  email: string;\n  password: string;\n  firstName: string;\n  lastName: string;\n  role?: string;\n  phoneNumber?: string;\n  department?: string;\n}\n\nexport interface ChangePasswordData {\n  currentPassword: string;\n  newPassword: string;\n}\n\nexport interface AuthResponse {\n  success: boolean;\n  message: string;\n  data: {\n    user: any;\n    token: string;\n    refreshToken: string;\n  };\n}\n\nexport interface UserResponse {\n  success: boolean;\n  data: {\n    user: any;\n  };\n}\n\nexport interface MessageResponse {\n  success: boolean;\n  message: string;\n}\n\nexport interface TokenResponse {\n  success: boolean;\n  data: {\n    token: string;\n    refreshToken: string;\n  };\n}\n\nexport const authAPI = {\n  // Login user\n  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {\n    const response = await apiClient.post('/auth/login', credentials);\n    return response.data;\n  },\n\n  // Register user\n  register: async (userData: RegisterData): Promise<AuthResponse> => {\n    const response = await apiClient.post('/auth/register', userData);\n    return response.data;\n  },\n\n  // Get current user\n  getCurrentUser: async (): Promise<UserResponse> => {\n    const response = await apiClient.get('/auth/me');\n    return response.data;\n  },\n\n  // Refresh token\n  refreshToken: async (refreshToken: string): Promise<TokenResponse> => {\n    const response = await apiClient.post('/auth/refresh-token', { refreshToken });\n    return response.data;\n  },\n\n  // Logout user\n  logout: async (refreshToken?: string): Promise<MessageResponse> => {\n    const response = await apiClient.post('/auth/logout', { refreshToken });\n    return response.data;\n  },\n\n  // Change password\n  changePassword: async (passwordData: ChangePasswordData): Promise<MessageResponse> => {\n    const response = await apiClient.post('/auth/change-password', passwordData);\n    return response.data;\n  },\n\n  // Forgot password\n  forgotPassword: async (email: string): Promise<MessageResponse> => {\n    const response = await apiClient.post('/auth/forgot-password', { email });\n    return response.data;\n  },\n\n  // Reset password\n  resetPassword: async (token: string, newPassword: string): Promise<MessageResponse> => {\n    const response = await apiClient.post('/auth/reset-password', { token, newPassword });\n    return response.data;\n  },\n\n  // Verify email\n  verifyEmail: async (token: string): Promise<MessageResponse> => {\n    const response = await apiClient.post('/auth/verify-email', { token });\n    return response.data;\n  },\n\n  // Resend verification email\n  resendVerificationEmail: async (): Promise<MessageResponse> => {\n    const response = await apiClient.post('/auth/resend-verification');\n    return response.data;\n  },\n};\n\nexport default apiClient;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,MAAMC,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,IAAI,2BAA2B;;AAEjF;AACA,MAAMC,SAAS,GAAGL,KAAK,CAACM,MAAM,CAAC;EAC7BC,OAAO,EAAEN,YAAY;EACrBO,OAAO,EAAE;IACP,cAAc,EAAE;EAClB;AACF,CAAC,CAAC;;AAEF;AACAH,SAAS,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAC/BC,MAAM,IAAK;EACV,MAAMC,KAAK,GAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;EAC3C,IAAIF,KAAK,EAAE;IACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;EAClD;EACA,OAAOD,MAAM;AACf,CAAC,EACAK,KAAK,IAAK;EACT,OAAOC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;;AAED;AACAZ,SAAS,CAACI,YAAY,CAACW,QAAQ,CAACT,GAAG,CAChCS,QAAQ,IAAKA,QAAQ,EACtB,MAAOH,KAAK,IAAK;EAAA,IAAAI,eAAA;EACf,MAAMC,eAAe,GAAGL,KAAK,CAACL,MAAM;EAEpC,IAAI,EAAAS,eAAA,GAAAJ,KAAK,CAACG,QAAQ,cAAAC,eAAA,uBAAdA,eAAA,CAAgBE,MAAM,MAAK,GAAG,IAAI,CAACD,eAAe,CAACE,MAAM,EAAE;IAC7DF,eAAe,CAACE,MAAM,GAAG,IAAI;IAE7B,IAAI;MACF,MAAMC,YAAY,GAAGX,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC;MACzD,IAAIU,YAAY,EAAE;QAChB,MAAML,QAAQ,GAAG,MAAMpB,KAAK,CAAC0B,IAAI,CAAC,GAAGzB,YAAY,qBAAqB,EAAE;UACtEwB;QACF,CAAC,CAAC;QAEF,MAAM;UAAEZ,KAAK;UAAEY,YAAY,EAAEE;QAAgB,CAAC,GAAGP,QAAQ,CAACQ,IAAI,CAACA,IAAI;QACnEd,YAAY,CAACe,OAAO,CAAC,OAAO,EAAEhB,KAAK,CAAC;QACpCC,YAAY,CAACe,OAAO,CAAC,cAAc,EAAEF,eAAe,CAAC;;QAErD;QACAL,eAAe,CAACd,OAAO,CAACQ,aAAa,GAAG,UAAUH,KAAK,EAAE;QACzD,OAAOR,SAAS,CAACiB,eAAe,CAAC;MACnC;IACF,CAAC,CAAC,OAAOQ,YAAY,EAAE;MACrB;MACAhB,YAAY,CAACiB,UAAU,CAAC,OAAO,CAAC;MAChCjB,YAAY,CAACiB,UAAU,CAAC,cAAc,CAAC;MACvCC,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,QAAQ;MAC/B,OAAOhB,OAAO,CAACC,MAAM,CAACW,YAAY,CAAC;IACrC;EACF;EAEA,OAAOZ,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC;AAC9B,CACF,CAAC;AAqDD,OAAO,MAAMkB,OAAO,GAAG;EACrB;EACAC,KAAK,EAAE,MAAOC,WAA6B,IAA4B;IACrE,MAAMjB,QAAQ,GAAG,MAAMf,SAAS,CAACqB,IAAI,CAAC,aAAa,EAAEW,WAAW,CAAC;IACjE,OAAOjB,QAAQ,CAACQ,IAAI;EACtB,CAAC;EAED;EACAU,QAAQ,EAAE,MAAOC,QAAsB,IAA4B;IACjE,MAAMnB,QAAQ,GAAG,MAAMf,SAAS,CAACqB,IAAI,CAAC,gBAAgB,EAAEa,QAAQ,CAAC;IACjE,OAAOnB,QAAQ,CAACQ,IAAI;EACtB,CAAC;EAED;EACAY,cAAc,EAAE,MAAAA,CAAA,KAAmC;IACjD,MAAMpB,QAAQ,GAAG,MAAMf,SAAS,CAACoC,GAAG,CAAC,UAAU,CAAC;IAChD,OAAOrB,QAAQ,CAACQ,IAAI;EACtB,CAAC;EAED;EACAH,YAAY,EAAE,MAAOA,YAAoB,IAA6B;IACpE,MAAML,QAAQ,GAAG,MAAMf,SAAS,CAACqB,IAAI,CAAC,qBAAqB,EAAE;MAAED;IAAa,CAAC,CAAC;IAC9E,OAAOL,QAAQ,CAACQ,IAAI;EACtB,CAAC;EAED;EACAc,MAAM,EAAE,MAAOjB,YAAqB,IAA+B;IACjE,MAAML,QAAQ,GAAG,MAAMf,SAAS,CAACqB,IAAI,CAAC,cAAc,EAAE;MAAED;IAAa,CAAC,CAAC;IACvE,OAAOL,QAAQ,CAACQ,IAAI;EACtB,CAAC;EAED;EACAe,cAAc,EAAE,MAAOC,YAAgC,IAA+B;IACpF,MAAMxB,QAAQ,GAAG,MAAMf,SAAS,CAACqB,IAAI,CAAC,uBAAuB,EAAEkB,YAAY,CAAC;IAC5E,OAAOxB,QAAQ,CAACQ,IAAI;EACtB,CAAC;EAED;EACAiB,cAAc,EAAE,MAAOC,KAAa,IAA+B;IACjE,MAAM1B,QAAQ,GAAG,MAAMf,SAAS,CAACqB,IAAI,CAAC,uBAAuB,EAAE;MAAEoB;IAAM,CAAC,CAAC;IACzE,OAAO1B,QAAQ,CAACQ,IAAI;EACtB,CAAC;EAED;EACAmB,aAAa,EAAE,MAAAA,CAAOlC,KAAa,EAAEmC,WAAmB,KAA+B;IACrF,MAAM5B,QAAQ,GAAG,MAAMf,SAAS,CAACqB,IAAI,CAAC,sBAAsB,EAAE;MAAEb,KAAK;MAAEmC;IAAY,CAAC,CAAC;IACrF,OAAO5B,QAAQ,CAACQ,IAAI;EACtB,CAAC;EAED;EACAqB,WAAW,EAAE,MAAOpC,KAAa,IAA+B;IAC9D,MAAMO,QAAQ,GAAG,MAAMf,SAAS,CAACqB,IAAI,CAAC,oBAAoB,EAAE;MAAEb;IAAM,CAAC,CAAC;IACtE,OAAOO,QAAQ,CAACQ,IAAI;EACtB,CAAC;EAED;EACAsB,uBAAuB,EAAE,MAAAA,CAAA,KAAsC;IAC7D,MAAM9B,QAAQ,GAAG,MAAMf,SAAS,CAACqB,IAAI,CAAC,2BAA2B,CAAC;IAClE,OAAON,QAAQ,CAACQ,IAAI;EACtB;AACF,CAAC;AAED,eAAevB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}