{"ast": null, "code": "function c() {\n  let o;\n  return {\n    before(_ref) {\n      let {\n        doc: e\n      } = _ref;\n      var l;\n      let n = e.documentElement;\n      o = ((l = e.defaultView) != null ? l : window).innerWidth - n.clientWidth;\n    },\n    after(_ref2) {\n      let {\n        doc: e,\n        d: n\n      } = _ref2;\n      let t = e.documentElement,\n        l = t.clientWidth - t.offsetWidth,\n        r = o - l;\n      n.style(t, \"paddingRight\", \"\".concat(r, \"px\"));\n    }\n  };\n}\nexport { c as adjustScrollbarPadding };", "map": {"version": 3, "names": ["c", "o", "before", "_ref", "doc", "e", "l", "n", "documentElement", "defaultView", "window", "innerWidth", "clientWidth", "after", "_ref2", "d", "t", "offsetWidth", "r", "style", "concat", "adjustScrollbarPadding"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/hooks/document-overflow/adjust-scrollbar-padding.js"], "sourcesContent": ["function c(){let o;return{before({doc:e}){var l;let n=e.documentElement;o=((l=e.defaultView)!=null?l:window).innerWidth-n.clientWidth},after({doc:e,d:n}){let t=e.documentElement,l=t.clientWidth-t.offsetWidth,r=o-l;n.style(t,\"paddingRight\",`${r}px`)}}}export{c as adjustScrollbarPadding};\n"], "mappings": "AAAA,SAASA,CAACA,CAAA,EAAE;EAAC,IAAIC,CAAC;EAAC,OAAM;IAACC,MAAMA,CAAAC,IAAA,EAAS;MAAA,IAAR;QAACC,GAAG,EAACC;MAAC,CAAC,GAAAF,IAAA;MAAE,IAAIG,CAAC;MAAC,IAAIC,CAAC,GAACF,CAAC,CAACG,eAAe;MAACP,CAAC,GAAC,CAAC,CAACK,CAAC,GAACD,CAAC,CAACI,WAAW,KAAG,IAAI,GAACH,CAAC,GAACI,MAAM,EAAEC,UAAU,GAACJ,CAAC,CAACK,WAAW;IAAA,CAAC;IAACC,KAAKA,CAAAC,KAAA,EAAa;MAAA,IAAZ;QAACV,GAAG,EAACC,CAAC;QAACU,CAAC,EAACR;MAAC,CAAC,GAAAO,KAAA;MAAE,IAAIE,CAAC,GAACX,CAAC,CAACG,eAAe;QAACF,CAAC,GAACU,CAAC,CAACJ,WAAW,GAACI,CAAC,CAACC,WAAW;QAACC,CAAC,GAACjB,CAAC,GAACK,CAAC;MAACC,CAAC,CAACY,KAAK,CAACH,CAAC,EAAC,cAAc,KAAAI,MAAA,CAAIF,CAAC,OAAI,CAAC;IAAA;EAAC,CAAC;AAAA;AAAC,SAAOlB,CAAC,IAAIqB,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}