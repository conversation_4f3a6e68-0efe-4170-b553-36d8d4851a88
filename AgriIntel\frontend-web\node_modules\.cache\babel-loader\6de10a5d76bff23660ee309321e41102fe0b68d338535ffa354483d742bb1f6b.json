{"ast": null, "code": "\"use strict\";\n\nconst conversions = require(\"webidl-conversions\");\nconst utils = require(\"./utils.js\");\nexports.convert = (globalObject, value, {\n  context = \"The provided value\"\n} = {}) => {\n  if (typeof value !== \"function\") {\n    throw new globalObject.TypeError(context + \" is not a function\");\n  }\n  function invokeTheCallbackFunction(...args) {\n    const thisArg = utils.tryWrapperForImpl(this);\n    let callResult;\n    for (let i = 0; i < args.length; i++) {\n      args[i] = utils.tryWrapperForImpl(args[i]);\n    }\n    callResult = Reflect.apply(value, thisArg, args);\n    callResult = conversions[\"any\"](callResult, {\n      context: context,\n      globals: globalObject\n    });\n    return callResult;\n  }\n  invokeTheCallbackFunction.construct = (...args) => {\n    for (let i = 0; i < args.length; i++) {\n      args[i] = utils.tryWrapperForImpl(args[i]);\n    }\n    let callResult = Reflect.construct(value, args);\n    callResult = conversions[\"any\"](callResult, {\n      context: context,\n      globals: globalObject\n    });\n    return callResult;\n  };\n  invokeTheCallbackFunction[utils.wrapperSymbol] = value;\n  invokeTheCallbackFunction.objectReference = value;\n  return invokeTheCallbackFunction;\n};", "map": {"version": 3, "names": ["conversions", "require", "utils", "exports", "convert", "globalObject", "value", "context", "TypeError", "invokeTheCallbackFunction", "args", "thisArg", "tryWrapperForImpl", "callResult", "i", "length", "Reflect", "apply", "globals", "construct", "wrapperSymbol", "objectReference"], "sources": ["C:/Users/<USER>/node_modules/mongodb-connection-string-url/node_modules/whatwg-url/lib/Function.js"], "sourcesContent": ["\"use strict\";\n\nconst conversions = require(\"webidl-conversions\");\nconst utils = require(\"./utils.js\");\n\nexports.convert = (globalObject, value, { context = \"The provided value\" } = {}) => {\n  if (typeof value !== \"function\") {\n    throw new globalObject.TypeError(context + \" is not a function\");\n  }\n\n  function invokeTheCallbackFunction(...args) {\n    const thisArg = utils.tryWrapperForImpl(this);\n    let callResult;\n\n    for (let i = 0; i < args.length; i++) {\n      args[i] = utils.tryWrapperForImpl(args[i]);\n    }\n\n    callResult = Reflect.apply(value, thisArg, args);\n\n    callResult = conversions[\"any\"](callResult, { context: context, globals: globalObject });\n\n    return callResult;\n  }\n\n  invokeTheCallbackFunction.construct = (...args) => {\n    for (let i = 0; i < args.length; i++) {\n      args[i] = utils.tryWrapperForImpl(args[i]);\n    }\n\n    let callResult = Reflect.construct(value, args);\n\n    callResult = conversions[\"any\"](callResult, { context: context, globals: globalObject });\n\n    return callResult;\n  };\n\n  invokeTheCallbackFunction[utils.wrapperSymbol] = value;\n  invokeTheCallbackFunction.objectReference = value;\n\n  return invokeTheCallbackFunction;\n};\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,WAAW,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AACjD,MAAMC,KAAK,GAAGD,OAAO,CAAC,YAAY,CAAC;AAEnCE,OAAO,CAACC,OAAO,GAAG,CAACC,YAAY,EAAEC,KAAK,EAAE;EAAEC,OAAO,GAAG;AAAqB,CAAC,GAAG,CAAC,CAAC,KAAK;EAClF,IAAI,OAAOD,KAAK,KAAK,UAAU,EAAE;IAC/B,MAAM,IAAID,YAAY,CAACG,SAAS,CAACD,OAAO,GAAG,oBAAoB,CAAC;EAClE;EAEA,SAASE,yBAAyBA,CAAC,GAAGC,IAAI,EAAE;IAC1C,MAAMC,OAAO,GAAGT,KAAK,CAACU,iBAAiB,CAAC,IAAI,CAAC;IAC7C,IAAIC,UAAU;IAEd,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;MACpCJ,IAAI,CAACI,CAAC,CAAC,GAAGZ,KAAK,CAACU,iBAAiB,CAACF,IAAI,CAACI,CAAC,CAAC,CAAC;IAC5C;IAEAD,UAAU,GAAGG,OAAO,CAACC,KAAK,CAACX,KAAK,EAAEK,OAAO,EAAED,IAAI,CAAC;IAEhDG,UAAU,GAAGb,WAAW,CAAC,KAAK,CAAC,CAACa,UAAU,EAAE;MAAEN,OAAO,EAAEA,OAAO;MAAEW,OAAO,EAAEb;IAAa,CAAC,CAAC;IAExF,OAAOQ,UAAU;EACnB;EAEAJ,yBAAyB,CAACU,SAAS,GAAG,CAAC,GAAGT,IAAI,KAAK;IACjD,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,IAAI,CAACK,MAAM,EAAED,CAAC,EAAE,EAAE;MACpCJ,IAAI,CAACI,CAAC,CAAC,GAAGZ,KAAK,CAACU,iBAAiB,CAACF,IAAI,CAACI,CAAC,CAAC,CAAC;IAC5C;IAEA,IAAID,UAAU,GAAGG,OAAO,CAACG,SAAS,CAACb,KAAK,EAAEI,IAAI,CAAC;IAE/CG,UAAU,GAAGb,WAAW,CAAC,KAAK,CAAC,CAACa,UAAU,EAAE;MAAEN,OAAO,EAAEA,OAAO;MAAEW,OAAO,EAAEb;IAAa,CAAC,CAAC;IAExF,OAAOQ,UAAU;EACnB,CAAC;EAEDJ,yBAAyB,CAACP,KAAK,CAACkB,aAAa,CAAC,GAAGd,KAAK;EACtDG,yBAAyB,CAACY,eAAe,GAAGf,KAAK;EAEjD,OAAOG,yBAAyB;AAClC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}