{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _objectWithoutProperties from\"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";const _excluded=[\"children\",\"value\",\"index\"];import React,{useState,useEffect}from'react';import{useNavigate}from'react-router-dom';import{motion}from'framer-motion';import{Grid,Card,CardContent,Typography,Box,Chip,IconButton,Avatar,useTheme,List,ListItem,ListItemText,ListItemIcon,Table,TableBody,TableCell,TableContainer,TableHead,TableRow,alpha}from'@mui/material';import{Add,CalendarToday,Search,FilterList,<PERSON><PERSON><PERSON>,<PERSON><PERSON><PERSON><PERSON>,<PERSON><PERSON>ircle,<PERSON>s,Female,Notifications,Assessment,Biotech,Healing,DateRange,Dashboard}from'../../utils/iconImports';import{AnimatedBackgroundCard,withSubModuleTranslation,StandardDashboard,ModernChart,CustomButton}from'../../components/common';import{mockBreedingRecords,mockBirthRecords,mockHeatRecords,mockBreedingPerformanceData}from'../../mocks/breedingData';import{format,parseISO,isAfter,isBefore,addDays,getMonth,getYear}from'date-fns';import{useMongoDb}from'../../hooks/useMongoDb';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";// Animation variants\nconst containerVariants={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:0.1}}};const itemVariants={hidden:{y:20,opacity:0},visible:{y:0,opacity:1,transition:{duration:0.5}}};// Interface for tab panel props\n// Tab Panel component\nconst TabPanel=props=>{const{children,value,index}=props,other=_objectWithoutProperties(props,_excluded);return/*#__PURE__*/_jsx(\"div\",_objectSpread(_objectSpread({role:\"tabpanel\",hidden:value!==index,id:\"breeding-tabpanel-\".concat(index),\"aria-labelledby\":\"breeding-tab-\".concat(index)},other),{},{style:{paddingTop:'20px'},children:value===index&&/*#__PURE__*/_jsx(Box,{children:children})}));};const BreedingDashboard=_ref=>{let{translate,translateSubModule,translateModuleField}=_ref;const theme=useTheme();const navigate=useNavigate();const{services,isConnected,isLoading:isDbLoading}=useMongoDb();// State for data loading\nconst[breedingRecords,setBreedingRecords]=useState([]);const[isLoading,setIsLoading]=useState(true);const[error,setError]=useState(null);const[refreshTrigger,setRefreshTrigger]=useState(0);// State for tabs, filters, and interactions\nconst[tabValue,setTabValue]=useState(0);const[timeRange,setTimeRange]=useState('month');const[selectedMetric,setSelectedMetric]=useState(null);const[radarTooltip,setRadarTooltip]=useState({show:false,content:''});const[selectedBreedingType,setSelectedBreedingType]=useState(null);const[selectedMonth,setSelectedMonth]=useState(null);const[chartTooltip,setChartTooltip]=useState({show:false,content:''});const[selectedChart,setSelectedChart]=useState(null);// State for menu\nconst[anchorEl,setAnchorEl]=useState(null);const[exportFormat,setExportFormat]=useState('pdf');// State for pagination\nconst[page,setPage]=useState(0);const[rowsPerPage,setRowsPerPage]=useState(5);// State for date range filter\nconst[dateRange,setDateRange]=useState({start:new Date(new Date().setMonth(new Date().getMonth()-6)).toISOString().split('T')[0],end:new Date().toISOString().split('T')[0]});// State for animal type filter\nconst[animalTypeFilter,setAnimalTypeFilter]=useState('all');// Fetch breeding records from MongoDB\nuseEffect(()=>{const fetchBreedingRecords=async()=>{setIsLoading(true);setError(null);try{// Try to fetch from MongoDB if connected\nif(isConnected&&services.breeding){const records=await services.breeding.findAll();setBreedingRecords(records);}else{// Fall back to mock data\nsetBreedingRecords(mockBreedingRecords);}}catch(err){console.error('Error fetching breeding records:',err);setError('Failed to load breeding records. Using mock data instead.');setBreedingRecords(mockBreedingRecords);}finally{setIsLoading(false);}};fetchBreedingRecords();},[isConnected,services.breeding,refreshTrigger]);// Handle refresh data\nconst handleRefreshData=()=>{setRefreshTrigger(prev=>prev+1);};// Handle tab change\nconst handleTabChange=(event,newValue)=>{setTabValue(newValue);};// Handle tab ID change for ResponsiveNavTabs\nconst handleTabIdChange=tabId=>{setTabValue(parseInt(tabId,10));};// Handle menu open/close\nconst handleMenuClick=event=>{setAnchorEl(event.currentTarget);};const handleMenuClose=()=>{setAnchorEl(null);};// Handle export\nconst handleExport=()=>{console.log(\"Exporting in \".concat(exportFormat,\" format\"));handleMenuClose();};// Handle pagination\nconst handleChangePage=(event,newValue)=>{setPage(newValue);};const handleChangeRowsPerPage=event=>{setRowsPerPage(parseInt(event.target.value,10));setPage(0);};// Calculate breeding statistics\nconst totalBreedings=breedingRecords.length;const confirmedBreedings=breedingRecords.filter(record=>record.status.toLowerCase()==='confirmed').length;const pendingBreedings=breedingRecords.filter(record=>record.status.toLowerCase()==='pending').length;const unsuccessfulBreedings=breedingRecords.filter(record=>record.status.toLowerCase()==='unsuccessful').length;const successRate=totalBreedings>0?Math.round(confirmedBreedings/totalBreedings*100):0;// Calculate upcoming events\nconst today=new Date();const upcomingHeatDates=mockHeatRecords.filter(record=>isAfter(parseISO(record.date),today)).sort((a,b)=>parseISO(a.date).getTime()-parseISO(b.date).getTime()).slice(0,5);const upcomingBirths=breedingRecords.filter(record=>record.expectedDueDate&&isAfter(parseISO(record.expectedDueDate),today)&&isBefore(parseISO(record.expectedDueDate),addDays(today,30))).sort((a,b)=>parseISO(a.expectedDueDate).getTime()-parseISO(b.expectedDueDate).getTime()).slice(0,5);// Calculate additional breeding metrics\nconst totalBirths=mockBirthRecords.length;const totalOffspring=mockBirthRecords.reduce((sum,record)=>sum+record.numberOfOffspring,0);const twinRate=mockBirthRecords.filter(record=>record.numberOfOffspring>1).length/(mockBirthRecords.length||1)*100;const complicationRate=mockBirthRecords.filter(record=>record.complications).length/(mockBirthRecords.length||1)*100;const assistanceRate=mockBirthRecords.filter(record=>record.assistanceProvided).length/(mockBirthRecords.length||1)*100;// Calculate breeding efficiency\nconst averageBreedingToConception=1.8;// Mock data - average number of breeding attempts to conception\nconst averageGestationLength=280;// Mock data - average gestation length in days\nconst averageCalvingInterval=385;// Mock data - average calving interval in days\n// Prepare chart data\nconst breedingMethodData=[{name:'Natural',value:breedingRecords.filter(record=>record.type==='natural').length},{name:'AI',value:breedingRecords.filter(record=>record.type==='artificial').length},{name:'Embryo Transfer',value:0}];const breedingStatusData=[{name:'Confirmed',value:confirmedBreedings},{name:'Pending',value:pendingBreedings},{name:'Failed',value:unsuccessfulBreedings}];// Generate monthly breeding data from actual records\nconst generateMonthlyBreedingData=()=>{const months=['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'];const data=months.map(month=>({name:month,natural:0,ai:0,embryo:0,success:0,failure:0}));// Populate with real data if available\nif(breedingRecords.length>0){breedingRecords.forEach(record=>{const date=parseISO(record.date);const monthIndex=getMonth(date);// Increment breeding method count\nif(record.type==='natural'){data[monthIndex].natural+=1;}else if(record.type==='artificial'){data[monthIndex].ai+=1;}// Increment success/failure count\nif(record.status==='confirmed'){data[monthIndex].success+=1;}else if(record.status==='unsuccessful'){data[monthIndex].failure+=1;}});}else{// Use mock data if no records\nreturn[{name:'Jan',natural:5,ai:8,embryo:2,success:12,failure:3},{name:'Feb',natural:7,ai:6,embryo:1,success:10,failure:4},{name:'Mar',natural:4,ai:9,embryo:3,success:13,failure:3},{name:'Apr',natural:6,ai:7,embryo:2,success:11,failure:4},{name:'May',natural:8,ai:5,embryo:1,success:10,failure:4},{name:'Jun',natural:9,ai:4,embryo:2,success:12,failure:3},{name:'Jul',natural:7,ai:6,embryo:3,success:13,failure:3},{name:'Aug',natural:5,ai:8,embryo:2,success:11,failure:4},{name:'Sep',natural:6,ai:7,embryo:1,success:10,failure:4},{name:'Oct',natural:8,ai:5,embryo:2,success:12,failure:3},{name:'Nov',natural:9,ai:4,embryo:3,success:13,failure:3},{name:'Dec',natural:7,ai:6,embryo:2,success:11,failure:4}];}return data;};const monthlyBreedingData=generateMonthlyBreedingData();// Generate success rate trend data\nconst generateSuccessRateTrendData=()=>{const months=['Jan','Feb','Mar','Apr','May','Jun','Jul','Aug','Sep','Oct','Nov','Dec'];const currentYear=new Date().getFullYear();// Initialize with mock data\nconst data=months.map((month,index)=>({name:month,rate:65+Math.floor(Math.random()*25),// Random success rate between 65-90%\nyear:currentYear}));// Try to populate with real data if available\nif(breedingRecords.length>0){// Group records by month\nconst recordsByMonth=new Array(12).fill(0).map(()=>({total:0,success:0}));breedingRecords.forEach(record=>{const date=parseISO(record.date);const year=getYear(date);// Only include current year records\nif(year===currentYear){const monthIndex=getMonth(date);recordsByMonth[monthIndex].total+=1;if(record.status==='confirmed'){recordsByMonth[monthIndex].success+=1;}}});// Calculate success rates\nrecordsByMonth.forEach((monthData,index)=>{if(monthData.total>0){data[index].rate=Math.round(monthData.success/monthData.total*100);}});}return data;};const successRateTrendData=generateSuccessRateTrendData();// Breeding success by animal type\nconst breedingSuccessByAnimalType=[{name:'Cattle',success:85,attempts:100},{name:'Sheep',success:78,attempts:90},{name:'Goats',success:82,attempts:95},{name:'Pigs',success:90,attempts:110}];// Breeding efficiency data\nconst breedingEfficiencyData=[{name:'Cattle',efficiency:92},{name:'Sheep',efficiency:88},{name:'Goats',efficiency:85},{name:'Pigs',efficiency:95}];// Birth complications data\nconst birthComplicationsData=[{name:'None',value:85},{name:'Minor',value:10},{name:'Major',value:5}];// Genetic improvement tracking data\nconst geneticImprovementData=[{year:'2019',improvement:0},{year:'2020',improvement:5},{year:'2021',improvement:12},{year:'2022',improvement:18},{year:'2023',improvement:25},{year:'2024',improvement:32}];// Seasonal breeding patterns\nconst seasonalBreedingData=[{season:'Spring',breedings:35,success:30},{season:'Summer',breedings:25,success:20},{season:'Fall',breedings:40,success:35},{season:'Winter',breedings:20,success:15}];// Calving distribution by month\nconst calvingDistributionData=[{month:'Jan',count:12},{month:'Feb',count:15},{month:'Mar',count:18},{month:'Apr',count:22},{month:'May',count:16},{month:'Jun',count:10},{month:'Jul',count:8},{month:'Aug',count:6},{month:'Sep',count:9},{month:'Oct',count:14},{month:'Nov',count:20},{month:'Dec',count:16}];// Gestation period distribution data\nconst gestationPeriodData=[{period:'270-275',count:5},{period:'276-280',count:15},{period:'281-285',count:35},{period:'286-290',count:25},{period:'291-295',count:12},{period:'296-300',count:8}];const COLORS=[theme.palette.primary.main,theme.palette.secondary.main,theme.palette.success.main,theme.palette.warning.main,theme.palette.error.main,theme.palette.info.main];// Prepare dashboard stats\nconst dashboardStats=[{label:translateModuleField?translateModuleField('total_breedings',\"Total Breedings\"):\"Total Breedings\",value:totalBreedings,icon:/*#__PURE__*/_jsx(Pets,{}),color:theme.palette.primary.main,trend:{value:5,isPositive:true,label:\"since last month\"}},{label:translateModuleField?translateModuleField('success_rate',\"Success Rate\"):\"Success Rate\",value:\"\".concat(successRate,\"%\"),icon:/*#__PURE__*/_jsx(CheckCircle,{}),color:theme.palette.success.main,trend:{value:2,isPositive:true,label:\"since last month\"}},{label:translateModuleField?translateModuleField('upcoming_births',\"Upcoming Births\"):\"Upcoming Births\",value:upcomingBirths.length,icon:/*#__PURE__*/_jsx(Female,{}),color:theme.palette.info.main,trend:{value:1,isPositive:true,label:\"since last month\"}},{label:translateModuleField?translateModuleField('heat_cycles',\"Heat Cycles\"):\"Heat Cycles\",value:upcomingHeatDates.length,icon:/*#__PURE__*/_jsx(CalendarToday,{}),color:theme.palette.warning.main,trend:{value:3,isPositive:false,label:\"since last month\"}}];// Prepare dashboard actions\nconst dashboardActions=[{label:translateModuleField?translateModuleField('add_breeding_record',\"Add Breeding Record\"):\"Add Breeding Record\",icon:/*#__PURE__*/_jsx(Add,{}),onClick:()=>navigate('/breeding/schedule'),color:'primary'}];// Prepare dashboard tabs\nconst dashboardTabs=[{label:translateModuleField?translateModuleField('overview',\"Overview\"):\"Overview\",icon:/*#__PURE__*/_jsx(Dashboard,{}),content:/*#__PURE__*/_jsx(Box,{})},{label:translateModuleField?translateModuleField('performance_metrics',\"Performance Metrics\"):\"Performance Metrics\",icon:/*#__PURE__*/_jsx(Assessment,{}),content:/*#__PURE__*/_jsx(Box,{})},{label:translateModuleField?translateModuleField('genetic_analysis',\"Genetic Analysis\"):\"Genetic Analysis\",icon:/*#__PURE__*/_jsx(Biotech,{}),content:/*#__PURE__*/_jsx(Box,{})},{label:translateModuleField?translateModuleField('health_complications',\"Health & Complications\"):\"Health & Complications\",icon:/*#__PURE__*/_jsx(Healing,{}),content:/*#__PURE__*/_jsx(Box,{})},{label:translateModuleField?translateModuleField('seasonal_patterns',\"Seasonal Patterns\"):\"Seasonal Patterns\",icon:/*#__PURE__*/_jsx(DateRange,{}),content:/*#__PURE__*/_jsx(Box,{})}];return/*#__PURE__*/_jsx(StandardDashboard,{title:translateSubModule?translateSubModule('title',\"Breeding Management\"):\"Breeding Management\",subtitle:translateSubModule?translateSubModule('subtitle',\"Monitor and manage breeding activities, heat cycles, pregnancy tracking, and birth predictions\"):\"Monitor and manage breeding activities, heat cycles, pregnancy tracking, and birth predictions\",icon:/*#__PURE__*/_jsx(Pets,{}),stats:dashboardStats,actions:dashboardActions,tabs:dashboardTabs,activeTab:tabValue,onTabChange:handleTabChange,isLoading:isLoading||isDbLoading,loadingMessage:translateModuleField?translateModuleField('loading',\"Loading breeding data...\"):\"Loading breeding data...\",onRefresh:handleRefreshData,module:\"breeding\",children:/*#__PURE__*/_jsxs(Box,{sx:{px:3,pb:5},children:[/*#__PURE__*/_jsxs(motion.div,{variants:containerVariants,initial:\"hidden\",animate:\"visible\",className:\"mb-4\",children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:\"bold\",gutterBottom:true,sx:{mt:3,mb:2},children:translateModuleField?translateModuleField('breeding_statistics',\"Breeding Statistics\"):\"Breeding Statistics\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:3,children:/*#__PURE__*/_jsx(motion.div,{variants:itemVariants,children:/*#__PURE__*/_jsx(AnimatedBackgroundCard,{title:\"Total Breedings\",subtitle:\"\".concat(totalBreedings,\" breeding records\"),module:\"breeding\",uniqueId:\"breeding-total\",icon:/*#__PURE__*/_jsx(Pets,{}),height:160,accentColor:theme.palette.primary.main})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:3,children:/*#__PURE__*/_jsx(motion.div,{variants:itemVariants,children:/*#__PURE__*/_jsx(AnimatedBackgroundCard,{title:\"Success Rate\",subtitle:\"\".concat(successRate,\"% successful breedings\"),module:\"breeding\",uniqueId:\"breeding-success\",icon:/*#__PURE__*/_jsx(CheckCircle,{}),height:160,accentColor:theme.palette.success.main,secondaryColor:theme.palette.success.dark})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:3,children:/*#__PURE__*/_jsx(motion.div,{variants:itemVariants,children:/*#__PURE__*/_jsx(AnimatedBackgroundCard,{title:\"Upcoming Births\",subtitle:\"\".concat(upcomingBirths.length,\" expected in next 30 days\"),module:\"breeding\",uniqueId:\"breeding-births\",icon:/*#__PURE__*/_jsx(Female,{}),height:160,accentColor:theme.palette.info.main,secondaryColor:theme.palette.info.dark})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:3,children:/*#__PURE__*/_jsx(motion.div,{variants:itemVariants,children:/*#__PURE__*/_jsx(AnimatedBackgroundCard,{title:\"Heat Cycles\",subtitle:\"\".concat(upcomingHeatDates.length,\" upcoming heat cycles\"),module:\"breeding\",uniqueId:\"breeding-heat\",icon:/*#__PURE__*/_jsx(CalendarToday,{}),height:160,accentColor:theme.palette.warning.main,secondaryColor:theme.palette.warning.dark})})})]})]}),/*#__PURE__*/_jsxs(motion.div,{variants:containerVariants,initial:\"hidden\",animate:\"visible\",className:\"mb-4\",children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:\"bold\",gutterBottom:true,sx:{mt:4,mb:2},children:translateModuleField?translateModuleField('breeding_analytics',\"Breeding Analytics\"):\"Breeding Analytics\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(ModernChart,{title:translateModuleField?translateModuleField('breeding_methods',\"Breeding Methods\"):\"Breeding Methods\",subtitle:translateModuleField?translateModuleField('breeding_methods_desc',\"Distribution of breeding methods used\"):\"Distribution of breeding methods used\",data:breedingMethodData,type:\"pie\",dataKeys:['value'],height:350,accentColor:theme.palette.primary.main,allowChartTypeChange:true,module:\"breeding\",tooltip:translateModuleField?translateModuleField('breeding_methods_help',\"Shows the distribution of different breeding methods used\"):\"Shows the distribution of different breeding methods used\",formatValue:value=>\"\".concat(value,\" records\")})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(ModernChart,{title:translateModuleField?translateModuleField('breeding_status',\"Breeding Status\"):\"Breeding Status\",subtitle:translateModuleField?translateModuleField('breeding_status_desc',\"Current status of breeding records\"):\"Current status of breeding records\",data:breedingStatusData,type:\"pie\",dataKeys:['value'],height:350,accentColor:theme.palette.secondary.main,allowChartTypeChange:true,module:\"breeding\",tooltip:translateModuleField?translateModuleField('breeding_status_help',\"Shows the current status of all breeding records\"):\"Shows the current status of all breeding records\",formatValue:value=>\"\".concat(value,\" records\")})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(ModernChart,{title:translateModuleField?translateModuleField('monthly_breeding',\"Monthly Breeding Activity\"):\"Monthly Breeding Activity\",subtitle:translateModuleField?translateModuleField('monthly_breeding_desc',\"Breeding methods used throughout the year\"):\"Breeding methods used throughout the year\",data:monthlyBreedingData,type:\"bar\",dataKeys:['natural','ai','embryo'],xAxisDataKey:\"name\",height:350,accentColor:theme.palette.primary.main,allowChartTypeChange:true,allowTimeRangeChange:true,module:\"breeding\",tooltip:translateModuleField?translateModuleField('monthly_breeding_help',\"Shows breeding activity by month and method\"):\"Shows breeding activity by month and method\",formatValue:value=>\"\".concat(value,\" breedings\")})})]})]}),/*#__PURE__*/_jsxs(motion.div,{variants:containerVariants,initial:\"hidden\",animate:\"visible\",className:\"mb-4\",children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",fontWeight:\"bold\",gutterBottom:true,sx:{mt:4,mb:2},children:translateModuleField?translateModuleField('upcoming_events',\"Upcoming Events\"):\"Upcoming Events\"}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(motion.div,{variants:itemVariants,children:/*#__PURE__*/_jsx(AnimatedBackgroundCard,{title:\"Upcoming Heat Cycles\",subtitle:\"Animals expected to be in heat soon\",module:\"breeding\",uniqueId:\"breeding-upcoming-heat\",height:400,icon:/*#__PURE__*/_jsx(CalendarToday,{}),accentColor:theme.palette.warning.main,action:/*#__PURE__*/_jsx(CustomButton,{size:\"small\",variant:\"outlined\",onClick:()=>navigate('/breeding/heat-calendar'),sx:{color:'white',borderColor:'rgba(255,255,255,0.5)','&:hover':{borderColor:'white',bgcolor:'rgba(255,255,255,0.1)'}},children:\"View Calendar\"}),content:/*#__PURE__*/_jsx(List,{children:upcomingHeatDates.length>0?upcomingHeatDates.map(record=>/*#__PURE__*/_jsxs(ListItem,{divider:true,children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(Avatar,{sx:{bgcolor:theme.palette.warning.main},children:/*#__PURE__*/_jsx(Female,{})})}),/*#__PURE__*/_jsx(ListItemText,{primary:\"Animal ID: \".concat(record.animalId),secondary:\"Expected Date: \".concat(format(parseISO(record.date),'dd MMM yyyy'),\" \\u2022 Intensity: \").concat(record.intensity)}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>navigate(\"/breeding/heat-calendar\"),children:/*#__PURE__*/_jsx(ArrowForward,{fontSize:\"small\"})})]},record.id)):/*#__PURE__*/_jsx(ListItem,{children:/*#__PURE__*/_jsx(ListItemText,{primary:\"No upcoming heat cycles\"})})})})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(motion.div,{variants:itemVariants,children:/*#__PURE__*/_jsx(AnimatedBackgroundCard,{title:\"Expected Births\",subtitle:\"Animals expected to give birth in the next 30 days\",module:\"breeding\",uniqueId:\"breeding-upcoming-births\",height:400,icon:/*#__PURE__*/_jsx(Notifications,{}),accentColor:theme.palette.info.main,action:/*#__PURE__*/_jsx(CustomButton,{size:\"small\",variant:\"outlined\",onClick:()=>navigate('/breeding/predictions'),sx:{color:'white',borderColor:'rgba(255,255,255,0.5)','&:hover':{borderColor:'white',bgcolor:'rgba(255,255,255,0.1)'}},children:\"View All\"}),content:/*#__PURE__*/_jsx(List,{children:upcomingBirths.length>0?upcomingBirths.map(record=>/*#__PURE__*/_jsxs(ListItem,{divider:true,children:[/*#__PURE__*/_jsx(ListItemIcon,{children:/*#__PURE__*/_jsx(Avatar,{sx:{bgcolor:theme.palette.info.main},children:/*#__PURE__*/_jsx(Pets,{})})}),/*#__PURE__*/_jsx(ListItemText,{primary:\"Female ID: \".concat(record.femaleId,\" \\u2022 Male ID: \").concat(record.maleId),secondary:\"Due Date: \".concat(format(parseISO(record.expectedDueDate),'dd MMM yyyy'),\" \\u2022 Method: \").concat(record.type==='natural'?'Natural':'Artificial')}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>navigate(\"/breeding/predictions\"),children:/*#__PURE__*/_jsx(ArrowForward,{fontSize:\"small\"})})]},record.id)):/*#__PURE__*/_jsx(ListItem,{children:/*#__PURE__*/_jsx(ListItemText,{primary:\"No upcoming births in the next 30 days\"})})})})})})]})]}),/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.7},children:/*#__PURE__*/_jsx(ModernChart,{title:translateModuleField?translateModuleField('breeding_performance',\"Breeding Performance Metrics\"):\"Breeding Performance Metrics\",subtitle:translateModuleField?translateModuleField('breeding_performance_desc',\"Key performance indicators for breeding program\"):\"Key performance indicators for breeding program\",data:mockBreedingPerformanceData,type:\"radar\",dataKeys:['A'],xAxisDataKey:\"subject\",height:350,accentColor:theme.palette.primary.main,allowChartTypeChange:false,module:\"breeding\",tooltip:translateModuleField?translateModuleField('breeding_performance_help',\"Shows performance metrics across different breeding aspects\"):\"Shows performance metrics across different breeding aspects\",formatValue:value=>\"\".concat(value,\"%\"),onRefresh:handleRefreshData})}),radarTooltip.show&&/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:0.3},children:/*#__PURE__*/_jsx(Card,{sx:{borderRadius:'12px',boxShadow:'0 4px 20px rgba(0,0,0,0.08)',mb:4,overflow:'hidden',background:\"linear-gradient(135deg, \".concat(alpha('#4A6FA5',0.85),\", \").concat(alpha('#3A5A8C',0.75),\")\"),color:'white'},children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:\"bold\",color:\"white\",children:\"Breeding Performance Details\"}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>setRadarTooltip({show:false,content:''}),sx:{color:'white'},children:/*#__PURE__*/_jsx(MoreVert,{})})]}),/*#__PURE__*/_jsx(Typography,{variant:\"body1\",mt:1,color:\"white\",children:radarTooltip.content}),selectedMetric&&/*#__PURE__*/_jsxs(Box,{mt:2,children:[/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"rgba(255,255,255,0.8)\",mb:1,children:[\"Improvement Suggestions for \",selectedMetric,\":\"]}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",color:\"white\",children:[selectedMetric==='Success Rate'&&'Optimize nutrition and timing for better breeding success.',selectedMetric==='Conception Rate'&&'Consider adjusting insemination techniques and timing.',selectedMetric==='Calving Ease'&&'Review breeding pairs for genetic compatibility to improve calving ease.',selectedMetric==='Gestation Length'&&'Monitor and adjust nutrition during pregnancy for optimal gestation.',selectedMetric==='Calf Survival'&&'Improve post-birth care protocols and monitoring.',selectedMetric==='Dam Recovery'&&'Enhance post-partum care and nutrition for faster recovery.']})]})]})})}),/*#__PURE__*/_jsx(motion.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0.8},children:/*#__PURE__*/_jsxs(Card,{sx:{borderRadius:'12px',boxShadow:'0 4px 20px rgba(0,0,0,0.08)',mb:4,overflow:'hidden',background:\"linear-gradient(135deg, \".concat(alpha('#4A6FA5',0.85),\", \").concat(alpha('#3A5A8C',0.75),\")\")},children:[/*#__PURE__*/_jsxs(Box,{sx:{p:2,color:'white',display:'flex',justifyContent:'space-between',alignItems:'center'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:\"bold\",color:\"white\",children:\"Breeding Records\"}),/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(IconButton,{color:\"inherit\",size:\"small\",children:/*#__PURE__*/_jsx(Search,{})}),/*#__PURE__*/_jsx(IconButton,{color:\"inherit\",size:\"small\",children:/*#__PURE__*/_jsx(FilterList,{})}),/*#__PURE__*/_jsx(IconButton,{color:\"inherit\",size:\"small\",children:/*#__PURE__*/_jsx(MoreVert,{})})]})]}),/*#__PURE__*/_jsx(TableContainer,{children:/*#__PURE__*/_jsxs(Table,{children:[/*#__PURE__*/_jsx(TableHead,{sx:{bgcolor:'rgba(255,255,255,0.1)'},children:/*#__PURE__*/_jsxs(TableRow,{children:[/*#__PURE__*/_jsx(TableCell,{sx:{color:'white',fontWeight:'bold'},children:\"ID\"}),/*#__PURE__*/_jsx(TableCell,{sx:{color:'white',fontWeight:'bold'},children:\"Female\"}),/*#__PURE__*/_jsx(TableCell,{sx:{color:'white',fontWeight:'bold'},children:\"Male\"}),/*#__PURE__*/_jsx(TableCell,{sx:{color:'white',fontWeight:'bold'},children:\"Breeding Date\"}),/*#__PURE__*/_jsx(TableCell,{sx:{color:'white',fontWeight:'bold'},children:\"Expected Due Date\"}),/*#__PURE__*/_jsx(TableCell,{sx:{color:'white',fontWeight:'bold'},children:\"Method\"}),/*#__PURE__*/_jsx(TableCell,{sx:{color:'white',fontWeight:'bold'},children:\"Status\"}),/*#__PURE__*/_jsx(TableCell,{sx:{color:'white',fontWeight:'bold'},children:\"Actions\"})]})}),/*#__PURE__*/_jsx(TableBody,{children:mockBreedingRecords.map(record=>/*#__PURE__*/_jsxs(TableRow,{hover:true,sx:{'&:hover':{bgcolor:'rgba(255,255,255,0.1)'}},children:[/*#__PURE__*/_jsx(TableCell,{sx:{color:'white'},children:record.id}),/*#__PURE__*/_jsx(TableCell,{sx:{color:'white'},children:\"Animal \".concat(record.femaleId)}),/*#__PURE__*/_jsx(TableCell,{sx:{color:'white'},children:\"Animal \".concat(record.maleId)}),/*#__PURE__*/_jsx(TableCell,{sx:{color:'white'},children:new Date(record.date).toLocaleDateString()}),/*#__PURE__*/_jsx(TableCell,{sx:{color:'white'},children:new Date(record.expectedDueDate).toLocaleDateString()}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Chip,{label:record.type==='natural'?'Natural':'Artificial',sx:{bgcolor:'rgba(255,255,255,0.2)',color:'white'},size:\"small\"})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(Chip,{label:record.status.charAt(0).toUpperCase()+record.status.slice(1),sx:{bgcolor:record.status==='pending'?'rgba(255,193,7,0.2)':record.status==='confirmed'?'rgba(76,175,80,0.2)':'rgba(244,67,54,0.2)',color:'white'},size:\"small\"})}),/*#__PURE__*/_jsx(TableCell,{children:/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>navigate(\"/breeding/records/\".concat(record.id)),sx:{color:'white'},children:/*#__PURE__*/_jsx(ArrowForward,{fontSize:\"small\"})})})]},record.id))})]})})]})})]})});};// Wrap the component with our HOC to provide translation functions\nexport default withSubModuleTranslation(BreedingDashboard,'breeding','dashboard');", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useNavigate", "motion", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Box", "Chip", "IconButton", "Avatar", "useTheme", "List", "ListItem", "ListItemText", "ListItemIcon", "Table", "TableBody", "TableCell", "TableContainer", "TableHead", "TableRow", "alpha", "Add", "CalendarToday", "Search", "FilterList", "<PERSON><PERSON><PERSON>", "ArrowForward", "CheckCircle", "Pets", "Female", "Notifications", "Assessment", "Biotech", "Healing", "DateRange", "Dashboard", "AnimatedBackgroundCard", "withSubModuleTranslation", "StandardDashboard", "<PERSON><PERSON><PERSON>", "CustomButton", "mockBreedingRecords", "mockBirthRecords", "mockHeatRecords", "mockBreedingPerformanceData", "format", "parseISO", "isAfter", "isBefore", "addDays", "getMonth", "getYear", "useMongoDb", "jsx", "_jsx", "jsxs", "_jsxs", "containerVariants", "hidden", "opacity", "visible", "transition", "stagger<PERSON><PERSON><PERSON><PERSON>", "itemVariants", "y", "duration", "TabPanel", "props", "children", "value", "index", "other", "_objectWithoutProperties", "_excluded", "_objectSpread", "role", "id", "concat", "style", "paddingTop", "BreedingDashboard", "_ref", "translate", "translateSubModule", "translateModuleField", "theme", "navigate", "services", "isConnected", "isLoading", "isDbLoading", "breedingRecords", "setBreedingRecords", "setIsLoading", "error", "setError", "refreshTrigger", "setRefreshTrigger", "tabValue", "setTabValue", "timeRange", "setTimeRange", "selectedMetric", "setSelectedMetric", "radarTooltip", "setRadarTooltip", "show", "content", "selectedBreedingType", "setSelectedBreedingType", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedMonth", "chartTooltip", "setChartTooltip", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>hart", "anchorEl", "setAnchorEl", "exportFormat", "setExportFormat", "page", "setPage", "rowsPerPage", "setRowsPerPage", "date<PERSON><PERSON><PERSON>", "setDateRange", "start", "Date", "setMonth", "toISOString", "split", "end", "animalTypeFilter", "setAnimalTypeFilter", "fetchBreedingRecords", "breeding", "records", "findAll", "err", "console", "handleRefreshData", "prev", "handleTabChange", "event", "newValue", "handleTabIdChange", "tabId", "parseInt", "handleMenuClick", "currentTarget", "handleMenuClose", "handleExport", "log", "handleChangePage", "handleChangeRowsPerPage", "target", "totalBreedings", "length", "confirmedBreedings", "filter", "record", "status", "toLowerCase", "pendingBreedings", "unsuccessfulBreedings", "successRate", "Math", "round", "today", "upcomingHeatDates", "date", "sort", "a", "b", "getTime", "slice", "upcomingBirths", "expectedDueDate", "totalBirths", "totalOffspring", "reduce", "sum", "numberOfOffspring", "twinRate", "complicationRate", "complications", "assistanceRate", "assistance<PERSON>rovided", "averageBreedingToConception", "averageGestationLength", "averageCalvingInterval", "breedingMethodData", "name", "type", "breedingStatusData", "generateMonthlyBreedingData", "months", "data", "map", "month", "natural", "ai", "embryo", "success", "failure", "for<PERSON>ach", "monthIndex", "monthlyBreedingData", "generateSuccessRateTrendData", "currentYear", "getFullYear", "rate", "floor", "random", "year", "recordsByMonth", "Array", "fill", "total", "monthData", "successRateTrendData", "breedingSuccessByAnimalType", "attempts", "breedingEfficiencyData", "efficiency", "birthComplicationsData", "geneticImprovementData", "improvement", "seasonalBreedingData", "season", "breedings", "calvingDistributionData", "count", "gestationPeriodData", "period", "COLORS", "palette", "primary", "main", "secondary", "warning", "info", "dashboardStats", "label", "icon", "color", "trend", "isPositive", "dashboardActions", "onClick", "dashboardTabs", "title", "subtitle", "stats", "actions", "tabs", "activeTab", "onTabChange", "loadingMessage", "onRefresh", "module", "sx", "px", "pb", "div", "variants", "initial", "animate", "className", "variant", "fontWeight", "gutterBottom", "mt", "mb", "container", "spacing", "item", "xs", "md", "uniqueId", "height", "accentColor", "secondaryColor", "dark", "dataKeys", "allowChartTypeChange", "tooltip", "formatValue", "xAxisDataKey", "allowTimeRangeChange", "action", "size", "borderColor", "bgcolor", "divider", "animalId", "intensity", "fontSize", "femaleId", "maleId", "delay", "borderRadius", "boxShadow", "overflow", "background", "display", "justifyContent", "alignItems", "p", "hover", "toLocaleDateString", "char<PERSON>t", "toUpperCase"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/pages/breeding/BreedingDashboard.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { motion } from 'framer-motion';\nimport { Grid, Card, CardContent, Typography, Box, Chip, IconButton, Avatar, LinearProgress, useTheme, List, ListItem, ListItemText, ListItemAvatar, ListItemIcon, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, alpha, Divider, Paper, Tab, Tabs, Menu, MenuItem, FormControl, InputLabel, Select, TextField, CircularProgress, Tooltip as MuiTooltip, TablePagination, Badge, Alert, Skeleton } from '@mui/material';\nimport {\n  Add,\n  Favorite,\n  ChildCare,\n  CalendarToday,\n  Search,\n  FilterList,\n  MoreVert,\n  ArrowForward,\n  CheckCircle,\n  Schedule,\n  Pets,\n  Female,\n  Male,\n  Science,\n  Notifications,\n  Warning,\n  BarChart,\n  PieChart as PieChartIcon,\n  Timeline,\n  Download,\n  Print,\n  Share,\n  Assessment,\n  TrendingUp,\n  TrendingDown,\n  Info,\n  MonitorHeart,\n  Biotech,\n  Healing,\n  EventNote,\n  DateRange,\n  Medication,\n  HealthAndSafety,\n  Dashboard,\n  InsertChart,\n  ListAlt,\n  Thermostat,\n  ChildFriendly,\n  Analytics,\n  Refresh\n } from '../../utils/iconImports';\nimport {\n  PieChart,\n  Pie,\n  Cell,\n  ResponsiveContainer,\n  Tooltip,\n  RadarChart,\n  PolarGrid,\n  PolarAngleAxis,\n  PolarRadiusAxis,\n  Radar,\n  Legend,\n  Bar,\n  XAxis,\n  YAxis,\n  CartesianGrid,\n  LineChart,\n  Line,\n  AreaChart,\n  Area,\n  ScatterChart,\n  Scatter,\n  ComposedChart\n} from 'recharts';\nimport { BarChart as RechartsBarChart } from 'recharts';\nimport { EnhancedPieLabelRenderProps } from '../../types/recharts';\nimport {  ModuleHeader, AnimatedBackgroundCard, ModuleContainer, ModuleHeaderCard, BlendedBackgroundCard, ResponsiveNavTabs, withSubModuleTranslation, StandardDashboard, ModernChart , CustomButton } from '../../components/common';\nimport { mockBreedingRecords, mockBirthRecords, mockHeatRecords, mockBreedingPerformanceData } from '../../mocks/breedingData';\nimport { format, parseISO, isAfter, isBefore, addDays, differenceInDays, addMonths, subMonths, subDays, getMonth, getYear } from 'date-fns';\nimport { useMongoDb } from '../../hooks/useMongoDb';\nimport { BreedingRecord } from '../../types/breeding';\n\n// Animation variants\nconst containerVariants = {\n  hidden: { opacity: 0 },\n  visible: {\n    opacity: 1,\n    transition: {\n      staggerChildren: 0.1\n    }\n  }\n};\n\nconst itemVariants = {\n  hidden: { y: 20, opacity: 0 },\n  visible: {\n    y: 0,\n    opacity: 1,\n    transition: {\n      duration: 0.5\n    }\n  }\n};\n\n// Interface for tab panel props\ninterface TabPanelProps {\n  children?: React.ReactNode;\n  index: number;\n  value: number;\n}\n\n// Tab Panel component\nconst TabPanel = (props: TabPanelProps) => {\n  const { children, value, index, ...other } = props;\n\n  return (\n    <div\n      role=\"tabpanel\"\n      hidden={value !== index}\n      id={`breeding-tabpanel-${index}`}\n      aria-labelledby={`breeding-tab-${index}`}\n      {...other}\n      style={{ paddingTop: '20px' }}\n    >\n      {value === index && (\n        <Box>\n          {children}\n        </Box>\n      )}\n    </div>\n  );\n};\n\ninterface BreedingDashboardProps {\n  translate?: (key: string, params?: Record<string, string | number>) => string;\n  translateSubModule?: (field: string, fallback: string) => string;\n  translateModuleField?: (field: string, fallback?: string) => string;\n}\n\nconst BreedingDashboard: React.FC<BreedingDashboardProps> = ({\n  translate,\n  translateSubModule,\n  translateModuleField\n}) => {\n  const theme = useTheme();\n  const navigate = useNavigate();\n  const { services, isConnected, isLoading: isDbLoading } = useMongoDb();\n\n  // State for data loading\n  const [breedingRecords, setBreedingRecords] = useState<BreedingRecord[]>([]);\n  const [isLoading, setIsLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [refreshTrigger, setRefreshTrigger] = useState(0);\n\n  // State for tabs, filters, and interactions\n  const [tabValue, setTabValue] = useState(0);\n  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'year'>('month');\n  const [selectedMetric, setSelectedMetric] = useState<string | null>(null);\n  const [radarTooltip, setRadarTooltip] = useState<{ show: boolean; content: string }>({ show: false, content: '' });\n  const [selectedBreedingType, setSelectedBreedingType] = useState<string | null>(null);\n  const [selectedMonth, setSelectedMonth] = useState<string | null>(null);\n  const [chartTooltip, setChartTooltip] = useState<{ show: boolean; content: string }>({ show: false, content: '' });\n  const [selectedChart, setSelectedChart] = useState<string | null>(null);\n\n  // State for menu\n  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);\n  const [exportFormat, setExportFormat] = useState('pdf');\n\n  // State for pagination\n  const [page, setPage] = useState(0);\n  const [rowsPerPage, setRowsPerPage] = useState(5);\n\n  // State for date range filter\n  const [dateRange, setDateRange] = useState<{start: string, end: string}>({\n    start: new Date(new Date().setMonth(new Date().getMonth() - 6)).toISOString().split('T')[0],\n    end: new Date().toISOString().split('T')[0]\n  });\n\n  // State for animal type filter\n  const [animalTypeFilter, setAnimalTypeFilter] = useState('all');\n\n  // Fetch breeding records from MongoDB\n  useEffect(() => {\n    const fetchBreedingRecords = async () => {\n      setIsLoading(true);\n      setError(null);\n      try {\n        // Try to fetch from MongoDB if connected\n        if (isConnected && services.breeding) {\n          const records = await services.breeding.findAll();\n          setBreedingRecords(records);\n        } else {\n          // Fall back to mock data\n          setBreedingRecords(mockBreedingRecords);\n        }\n      } catch (err) {\n        console.error('Error fetching breeding records:', err);\n        setError('Failed to load breeding records. Using mock data instead.');\n        setBreedingRecords(mockBreedingRecords);\n      } finally {\n        setIsLoading(false);\n      }\n    };\n\n    fetchBreedingRecords();\n  }, [isConnected, services.breeding, refreshTrigger]);\n\n  // Handle refresh data\n  const handleRefreshData = () => {\n    setRefreshTrigger(prev => prev + 1);\n  };\n\n  // Handle tab change\n  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {\n    setTabValue(newValue);\n  };\n\n  // Handle tab ID change for ResponsiveNavTabs\n  const handleTabIdChange = (tabId: string) => {\n    setTabValue(parseInt(tabId, 10));\n  };\n\n  // Handle menu open/close\n  const handleMenuClick = (event: React.MouseEvent<HTMLElement>) => {\n    setAnchorEl(event.currentTarget);\n  };\n\n  const handleMenuClose = () => {\n    setAnchorEl(null);\n  };\n\n  // Handle export\n  const handleExport = () => {\n    console.log(`Exporting in ${exportFormat} format`);\n    handleMenuClose();\n  };\n\n  // Handle pagination\n  const handleChangePage = (event: unknown, newValue: number) => {\n    setPage(newValue);\n  };\n\n  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {\n    setRowsPerPage(parseInt(event.target.value, 10));\n    setPage(0);\n  };\n\n  // Calculate breeding statistics\n  const totalBreedings = breedingRecords.length;\n  const confirmedBreedings = breedingRecords.filter(record => record.status.toLowerCase() === 'confirmed').length;\n  const pendingBreedings = breedingRecords.filter(record => record.status.toLowerCase() === 'pending').length;\n  const unsuccessfulBreedings = breedingRecords.filter(record => record.status.toLowerCase() === 'unsuccessful').length;\n  const successRate = totalBreedings > 0 ? Math.round((confirmedBreedings / totalBreedings) * 100) : 0;\n\n  // Calculate upcoming events\n  const today = new Date();\n  const upcomingHeatDates = mockHeatRecords\n    .filter(record => isAfter(parseISO(record.date), today))\n    .sort((a, b) => parseISO(a.date).getTime() - parseISO(b.date).getTime())\n    .slice(0, 5);\n\n  const upcomingBirths = breedingRecords\n    .filter(record => record.expectedDueDate && isAfter(parseISO(record.expectedDueDate), today) && isBefore(parseISO(record.expectedDueDate), addDays(today, 30)))\n    .sort((a, b) => parseISO(a.expectedDueDate!).getTime() - parseISO(b.expectedDueDate!).getTime())\n    .slice(0, 5);\n\n  // Calculate additional breeding metrics\n  const totalBirths = mockBirthRecords.length;\n  const totalOffspring = mockBirthRecords.reduce((sum, record) => sum + record.numberOfOffspring, 0);\n  const twinRate = mockBirthRecords.filter(record => record.numberOfOffspring > 1).length /\n    (mockBirthRecords.length || 1) * 100;\n  const complicationRate = mockBirthRecords.filter(record => record.complications).length /\n    (mockBirthRecords.length || 1) * 100;\n  const assistanceRate = mockBirthRecords.filter(record => record.assistanceProvided).length /\n    (mockBirthRecords.length || 1) * 100;\n\n  // Calculate breeding efficiency\n  const averageBreedingToConception = 1.8; // Mock data - average number of breeding attempts to conception\n  const averageGestationLength = 280; // Mock data - average gestation length in days\n  const averageCalvingInterval = 385; // Mock data - average calving interval in days\n\n  // Prepare chart data\n  const breedingMethodData = [\n    { name: 'Natural', value: breedingRecords.filter(record => record.type === 'natural').length },\n    { name: 'AI', value: breedingRecords.filter(record => record.type === 'artificial').length },\n    { name: 'Embryo Transfer', value: 0 }\n  ];\n\n  const breedingStatusData = [\n    { name: 'Confirmed', value: confirmedBreedings },\n    { name: 'Pending', value: pendingBreedings },\n    { name: 'Failed', value: unsuccessfulBreedings }\n  ];\n\n  // Generate monthly breeding data from actual records\n  const generateMonthlyBreedingData = () => {\n    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    const data = months.map(month => ({\n      name: month,\n      natural: 0,\n      ai: 0,\n      embryo: 0,\n      success: 0,\n      failure: 0\n    }));\n\n    // Populate with real data if available\n    if (breedingRecords.length > 0) {\n      breedingRecords.forEach(record => {\n        const date = parseISO(record.date);\n        const monthIndex = getMonth(date);\n\n        // Increment breeding method count\n        if (record.type === 'natural') {\n          data[monthIndex].natural += 1;\n        } else if (record.type === 'artificial') {\n          data[monthIndex].ai += 1;\n        }\n\n        // Increment success/failure count\n        if (record.status === 'confirmed') {\n          data[monthIndex].success += 1;\n        } else if (record.status === 'unsuccessful') {\n          data[monthIndex].failure += 1;\n        }\n      });\n    } else {\n      // Use mock data if no records\n      return [\n        { name: 'Jan', natural: 5, ai: 8, embryo: 2, success: 12, failure: 3 },\n        { name: 'Feb', natural: 7, ai: 6, embryo: 1, success: 10, failure: 4 },\n        { name: 'Mar', natural: 4, ai: 9, embryo: 3, success: 13, failure: 3 },\n        { name: 'Apr', natural: 6, ai: 7, embryo: 2, success: 11, failure: 4 },\n        { name: 'May', natural: 8, ai: 5, embryo: 1, success: 10, failure: 4 },\n        { name: 'Jun', natural: 9, ai: 4, embryo: 2, success: 12, failure: 3 },\n        { name: 'Jul', natural: 7, ai: 6, embryo: 3, success: 13, failure: 3 },\n        { name: 'Aug', natural: 5, ai: 8, embryo: 2, success: 11, failure: 4 },\n        { name: 'Sep', natural: 6, ai: 7, embryo: 1, success: 10, failure: 4 },\n        { name: 'Oct', natural: 8, ai: 5, embryo: 2, success: 12, failure: 3 },\n        { name: 'Nov', natural: 9, ai: 4, embryo: 3, success: 13, failure: 3 },\n        { name: 'Dec', natural: 7, ai: 6, embryo: 2, success: 11, failure: 4 }\n      ];\n    }\n\n    return data;\n  };\n\n  const monthlyBreedingData = generateMonthlyBreedingData();\n\n  // Generate success rate trend data\n  const generateSuccessRateTrendData = () => {\n    const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];\n    const currentYear = new Date().getFullYear();\n\n    // Initialize with mock data\n    const data = months.map((month, index) => ({\n      name: month,\n      rate: 65 + Math.floor(Math.random() * 25), // Random success rate between 65-90%\n      year: currentYear\n    }));\n\n    // Try to populate with real data if available\n    if (breedingRecords.length > 0) {\n      // Group records by month\n      const recordsByMonth = new Array(12).fill(0).map(() => ({ total: 0, success: 0 }));\n\n      breedingRecords.forEach(record => {\n        const date = parseISO(record.date);\n        const year = getYear(date);\n\n        // Only include current year records\n        if (year === currentYear) {\n          const monthIndex = getMonth(date);\n          recordsByMonth[monthIndex].total += 1;\n\n          if (record.status === 'confirmed') {\n            recordsByMonth[monthIndex].success += 1;\n          }\n        }\n      });\n\n      // Calculate success rates\n      recordsByMonth.forEach((monthData, index) => {\n        if (monthData.total > 0) {\n          data[index].rate = Math.round((monthData.success / monthData.total) * 100);\n        }\n      });\n    }\n\n    return data;\n  };\n\n  const successRateTrendData = generateSuccessRateTrendData();\n\n  // Breeding success by animal type\n  const breedingSuccessByAnimalType = [\n    { name: 'Cattle', success: 85, attempts: 100 },\n    { name: 'Sheep', success: 78, attempts: 90 },\n    { name: 'Goats', success: 82, attempts: 95 },\n    { name: 'Pigs', success: 90, attempts: 110 }\n  ];\n\n  // Breeding efficiency data\n  const breedingEfficiencyData = [\n    { name: 'Cattle', efficiency: 92 },\n    { name: 'Sheep', efficiency: 88 },\n    { name: 'Goats', efficiency: 85 },\n    { name: 'Pigs', efficiency: 95 }\n  ];\n\n  // Birth complications data\n  const birthComplicationsData = [\n    { name: 'None', value: 85 },\n    { name: 'Minor', value: 10 },\n    { name: 'Major', value: 5 }\n  ];\n\n  // Genetic improvement tracking data\n  const geneticImprovementData = [\n    { year: '2019', improvement: 0 },\n    { year: '2020', improvement: 5 },\n    { year: '2021', improvement: 12 },\n    { year: '2022', improvement: 18 },\n    { year: '2023', improvement: 25 },\n    { year: '2024', improvement: 32 }\n  ];\n\n  // Seasonal breeding patterns\n  const seasonalBreedingData = [\n    { season: 'Spring', breedings: 35, success: 30 },\n    { season: 'Summer', breedings: 25, success: 20 },\n    { season: 'Fall', breedings: 40, success: 35 },\n    { season: 'Winter', breedings: 20, success: 15 }\n  ];\n\n  // Calving distribution by month\n  const calvingDistributionData = [\n    { month: 'Jan', count: 12 },\n    { month: 'Feb', count: 15 },\n    { month: 'Mar', count: 18 },\n    { month: 'Apr', count: 22 },\n    { month: 'May', count: 16 },\n    { month: 'Jun', count: 10 },\n    { month: 'Jul', count: 8 },\n    { month: 'Aug', count: 6 },\n    { month: 'Sep', count: 9 },\n    { month: 'Oct', count: 14 },\n    { month: 'Nov', count: 20 },\n    { month: 'Dec', count: 16 }\n  ];\n\n  // Gestation period distribution data\n  const gestationPeriodData = [\n    { period: '270-275', count: 5 },\n    { period: '276-280', count: 15 },\n    { period: '281-285', count: 35 },\n    { period: '286-290', count: 25 },\n    { period: '291-295', count: 12 },\n    { period: '296-300', count: 8 }\n  ];\n\n  const COLORS = [\n    theme.palette.primary.main,\n    theme.palette.secondary.main,\n    theme.palette.success.main,\n    theme.palette.warning.main,\n    theme.palette.error.main,\n    theme.palette.info.main\n  ];\n\n  // Prepare dashboard stats\n  const dashboardStats = [\n    {\n      label: translateModuleField ? translateModuleField('total_breedings', \"Total Breedings\") : \"Total Breedings\",\n      value: totalBreedings,\n      icon: <Pets />,\n      color: theme.palette.primary.main,\n      trend: {\n        value: 5,\n        isPositive: true,\n        label: \"since last month\"\n      }\n    },\n    {\n      label: translateModuleField ? translateModuleField('success_rate', \"Success Rate\") : \"Success Rate\",\n      value: `${successRate}%`,\n      icon: <CheckCircle />,\n      color: theme.palette.success.main,\n      trend: {\n        value: 2,\n        isPositive: true,\n        label: \"since last month\"\n      }\n    },\n    {\n      label: translateModuleField ? translateModuleField('upcoming_births', \"Upcoming Births\") : \"Upcoming Births\",\n      value: upcomingBirths.length,\n      icon: <Female />,\n      color: theme.palette.info.main,\n      trend: {\n        value: 1,\n        isPositive: true,\n        label: \"since last month\"\n      }\n    },\n    {\n      label: translateModuleField ? translateModuleField('heat_cycles', \"Heat Cycles\") : \"Heat Cycles\",\n      value: upcomingHeatDates.length,\n      icon: <CalendarToday />,\n      color: theme.palette.warning.main,\n      trend: {\n        value: 3,\n        isPositive: false,\n        label: \"since last month\"\n      }\n    }\n  ];\n\n  // Prepare dashboard actions\n  const dashboardActions = [\n    {\n      label: translateModuleField ? translateModuleField('add_breeding_record', \"Add Breeding Record\") : \"Add Breeding Record\",\n      icon: <Add />,\n      onClick: () => navigate('/breeding/schedule'),\n      color: 'primary'\n    }\n  ];\n\n  // Prepare dashboard tabs\n  const dashboardTabs = [\n    {\n      label: translateModuleField ? translateModuleField('overview', \"Overview\") : \"Overview\",\n      icon: <Dashboard />,\n      content: (\n        <Box>\n          {/* Overview content will go here */}\n        </Box>\n      )\n    },\n    {\n      label: translateModuleField ? translateModuleField('performance_metrics', \"Performance Metrics\") : \"Performance Metrics\",\n      icon: <Assessment />,\n      content: (\n        <Box>\n          {/* Performance metrics content will go here */}\n        </Box>\n      )\n    },\n    {\n      label: translateModuleField ? translateModuleField('genetic_analysis', \"Genetic Analysis\") : \"Genetic Analysis\",\n      icon: <Biotech />,\n      content: (\n        <Box>\n          {/* Genetic analysis content will go here */}\n        </Box>\n      )\n    },\n    {\n      label: translateModuleField ? translateModuleField('health_complications', \"Health & Complications\") : \"Health & Complications\",\n      icon: <Healing />,\n      content: (\n        <Box>\n          {/* Health & complications content will go here */}\n        </Box>\n      )\n    },\n    {\n      label: translateModuleField ? translateModuleField('seasonal_patterns', \"Seasonal Patterns\") : \"Seasonal Patterns\",\n      icon: <DateRange />,\n      content: (\n        <Box>\n          {/* Seasonal patterns content will go here */}\n        </Box>\n      )\n    }\n  ];\n\n  return (\n    <StandardDashboard\n      title={translateSubModule ? translateSubModule('title', \"Breeding Management\") : \"Breeding Management\"}\n      subtitle={translateSubModule ? translateSubModule('subtitle', \"Monitor and manage breeding activities, heat cycles, pregnancy tracking, and birth predictions\") : \"Monitor and manage breeding activities, heat cycles, pregnancy tracking, and birth predictions\"}\n      icon={<Pets />}\n      stats={dashboardStats}\n      actions={dashboardActions}\n      tabs={dashboardTabs}\n      activeTab={tabValue}\n      onTabChange={handleTabChange}\n      isLoading={isLoading || isDbLoading}\n      loadingMessage={translateModuleField ? translateModuleField('loading', \"Loading breeding data...\") : \"Loading breeding data...\"}\n      onRefresh={handleRefreshData}\n      module=\"breeding\"\n    >\n\n      <Box sx={{ px: 3, pb: 5 }}>\n        {/* Stats Cards */}\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          className=\"mb-4\"\n        >\n          <Typography variant=\"h5\" fontWeight=\"bold\" gutterBottom sx={{ mt: 3, mb: 2 }}>\n            {translateModuleField ? translateModuleField('breeding_statistics', \"Breeding Statistics\") : \"Breeding Statistics\"}\n          </Typography>\n\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={3}>\n              <motion.div variants={itemVariants}>\n                <AnimatedBackgroundCard\n                  title=\"Total Breedings\"\n                  subtitle={`${totalBreedings} breeding records`}\n                  module=\"breeding\"\n                  uniqueId=\"breeding-total\"\n                  icon={<Pets />}\n                  height={160}\n                  accentColor={theme.palette.primary.main}\n                />\n              </motion.div>\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <motion.div variants={itemVariants}>\n                <AnimatedBackgroundCard\n                  title=\"Success Rate\"\n                  subtitle={`${successRate}% successful breedings`}\n                  module=\"breeding\"\n                  uniqueId=\"breeding-success\"\n                  icon={<CheckCircle />}\n                  height={160}\n                  accentColor={theme.palette.success.main}\n                  secondaryColor={theme.palette.success.dark}\n                />\n              </motion.div>\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <motion.div variants={itemVariants}>\n                <AnimatedBackgroundCard\n                  title=\"Upcoming Births\"\n                  subtitle={`${upcomingBirths.length} expected in next 30 days`}\n                  module=\"breeding\"\n                  uniqueId=\"breeding-births\"\n                  icon={<Female />}\n                  height={160}\n                  accentColor={theme.palette.info.main}\n                  secondaryColor={theme.palette.info.dark}\n                />\n              </motion.div>\n            </Grid>\n\n            <Grid item xs={12} md={3}>\n              <motion.div variants={itemVariants}>\n                <AnimatedBackgroundCard\n                  title=\"Heat Cycles\"\n                  subtitle={`${upcomingHeatDates.length} upcoming heat cycles`}\n                  module=\"breeding\"\n                  uniqueId=\"breeding-heat\"\n                  icon={<CalendarToday />}\n                  height={160}\n                  accentColor={theme.palette.warning.main}\n                  secondaryColor={theme.palette.warning.dark}\n                />\n              </motion.div>\n            </Grid>\n          </Grid>\n        </motion.div>\n\n        {/* Charts Section */}\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          className=\"mb-4\"\n        >\n          <Typography variant=\"h5\" fontWeight=\"bold\" gutterBottom sx={{ mt: 4, mb: 2 }}>\n            {translateModuleField ? translateModuleField('breeding_analytics', \"Breeding Analytics\") : \"Breeding Analytics\"}\n          </Typography>\n\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={6}>\n              <ModernChart\n                title={translateModuleField ? translateModuleField('breeding_methods', \"Breeding Methods\") : \"Breeding Methods\"}\n                subtitle={translateModuleField ? translateModuleField('breeding_methods_desc', \"Distribution of breeding methods used\") : \"Distribution of breeding methods used\"}\n                data={breedingMethodData}\n                type=\"pie\"\n                dataKeys={['value']}\n                height={350}\n                accentColor={theme.palette.primary.main}\n                allowChartTypeChange={true}\n                module=\"breeding\"\n                tooltip={translateModuleField ? translateModuleField('breeding_methods_help', \"Shows the distribution of different breeding methods used\") : \"Shows the distribution of different breeding methods used\"}\n                formatValue={(value) => `${value} records`}\n              />\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <ModernChart\n                title={translateModuleField ? translateModuleField('breeding_status', \"Breeding Status\") : \"Breeding Status\"}\n                subtitle={translateModuleField ? translateModuleField('breeding_status_desc', \"Current status of breeding records\") : \"Current status of breeding records\"}\n                data={breedingStatusData}\n                type=\"pie\"\n                dataKeys={['value']}\n                height={350}\n                accentColor={theme.palette.secondary.main}\n                allowChartTypeChange={true}\n                module=\"breeding\"\n                tooltip={translateModuleField ? translateModuleField('breeding_status_help', \"Shows the current status of all breeding records\") : \"Shows the current status of all breeding records\"}\n                formatValue={(value) => `${value} records`}\n              />\n            </Grid>\n            <Grid item xs={12}>\n              <ModernChart\n                title={translateModuleField ? translateModuleField('monthly_breeding', \"Monthly Breeding Activity\") : \"Monthly Breeding Activity\"}\n                subtitle={translateModuleField ? translateModuleField('monthly_breeding_desc', \"Breeding methods used throughout the year\") : \"Breeding methods used throughout the year\"}\n                data={monthlyBreedingData}\n                type=\"bar\"\n                dataKeys={['natural', 'ai', 'embryo']}\n                xAxisDataKey=\"name\"\n                height={350}\n                accentColor={theme.palette.primary.main}\n                allowChartTypeChange={true}\n                allowTimeRangeChange={true}\n                module=\"breeding\"\n                tooltip={translateModuleField ? translateModuleField('monthly_breeding_help', \"Shows breeding activity by month and method\") : \"Shows breeding activity by month and method\"}\n                formatValue={(value) => `${value} breedings`}\n              />\n            </Grid>\n          </Grid>\n        </motion.div>\n\n        {/* Upcoming Events Section */}\n        <motion.div\n          variants={containerVariants}\n          initial=\"hidden\"\n          animate=\"visible\"\n          className=\"mb-4\"\n        >\n          <Typography variant=\"h5\" fontWeight=\"bold\" gutterBottom sx={{ mt: 4, mb: 2 }}>\n            {translateModuleField ? translateModuleField('upcoming_events', \"Upcoming Events\") : \"Upcoming Events\"}\n          </Typography>\n\n          <Grid container spacing={3}>\n            <Grid item xs={12} md={6}>\n              <motion.div variants={itemVariants}>\n                <AnimatedBackgroundCard\n                  title=\"Upcoming Heat Cycles\"\n                  subtitle=\"Animals expected to be in heat soon\"\n                  module=\"breeding\"\n                  uniqueId=\"breeding-upcoming-heat\"\n                  height={400}\n                  icon={<CalendarToday />}\n                  accentColor={theme.palette.warning.main}\n                  action={\n                    <CustomButton\n                      size=\"small\"\n                      variant=\"outlined\"\n                      onClick={() => navigate('/breeding/heat-calendar')}\n                      sx={{\n                        color: 'white',\n                        borderColor: 'rgba(255,255,255,0.5)',\n                        '&:hover': { borderColor: 'white', bgcolor: 'rgba(255,255,255,0.1)' }\n                      }}\n                    >\n                      View Calendar\n                    </CustomButton>\n                  }\n                  content={\n                    <List>\n                      {upcomingHeatDates.length > 0 ? (\n                        upcomingHeatDates.map((record) => (\n                          <ListItem key={record.id} divider>\n                            <ListItemIcon>\n                              <Avatar sx={{ bgcolor: theme.palette.warning.main }}>\n                                <Female />\n                              </Avatar>\n                            </ListItemIcon>\n                            <ListItemText\n                              primary={`Animal ID: ${record.animalId}`}\n                              secondary={`Expected Date: ${format(parseISO(record.date), 'dd MMM yyyy')} • Intensity: ${record.intensity}`}\n                            />\n                            <IconButton size=\"small\" onClick={() => navigate(`/breeding/heat-calendar`)}>\n                              <ArrowForward fontSize=\"small\" />\n                            </IconButton>\n                          </ListItem>\n                        ))\n                      ) : (\n                        <ListItem>\n                          <ListItemText primary=\"No upcoming heat cycles\" />\n                        </ListItem>\n                      )}\n                    </List>\n                  }\n                />\n              </motion.div>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <motion.div variants={itemVariants}>\n                <AnimatedBackgroundCard\n                  title=\"Expected Births\"\n                  subtitle=\"Animals expected to give birth in the next 30 days\"\n                  module=\"breeding\"\n                  uniqueId=\"breeding-upcoming-births\"\n                  height={400}\n                  icon={<Notifications />}\n                  accentColor={theme.palette.info.main}\n                  action={\n                    <CustomButton\n                      size=\"small\"\n                      variant=\"outlined\"\n                      onClick={() => navigate('/breeding/predictions')}\n                      sx={{\n                        color: 'white',\n                        borderColor: 'rgba(255,255,255,0.5)',\n                        '&:hover': { borderColor: 'white', bgcolor: 'rgba(255,255,255,0.1)' }\n                      }}\n                    >\n                      View All\n                    </CustomButton>\n                  }\n                  content={\n                    <List>\n                      {upcomingBirths.length > 0 ? (\n                        upcomingBirths.map((record) => (\n                          <ListItem key={record.id} divider>\n                            <ListItemIcon>\n                              <Avatar sx={{ bgcolor: theme.palette.info.main }}>\n                                <Pets />\n                              </Avatar>\n                            </ListItemIcon>\n                            <ListItemText\n                              primary={`Female ID: ${record.femaleId} • Male ID: ${record.maleId}`}\n                              secondary={`Due Date: ${format(parseISO(record.expectedDueDate!), 'dd MMM yyyy')} • Method: ${record.type === 'natural' ? 'Natural' : 'Artificial'}`}\n                            />\n                            <IconButton size=\"small\" onClick={() => navigate(`/breeding/predictions`)}>\n                              <ArrowForward fontSize=\"small\" />\n                            </IconButton>\n                          </ListItem>\n                        ))\n                      ) : (\n                        <ListItem>\n                          <ListItemText primary=\"No upcoming births in the next 30 days\" />\n                        </ListItem>\n                      )}\n                    </List>\n                  }\n                />\n              </motion.div>\n            </Grid>\n          </Grid>\n        </motion.div>\n\n      {/* Breeding Performance Radar Chart */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.7 }}\n      >\n        <ModernChart\n          title={translateModuleField ? translateModuleField('breeding_performance', \"Breeding Performance Metrics\") : \"Breeding Performance Metrics\"}\n          subtitle={translateModuleField ? translateModuleField('breeding_performance_desc', \"Key performance indicators for breeding program\") : \"Key performance indicators for breeding program\"}\n          data={mockBreedingPerformanceData}\n          type=\"radar\"\n          dataKeys={['A']}\n          xAxisDataKey=\"subject\"\n          height={350}\n          accentColor={theme.palette.primary.main}\n          allowChartTypeChange={false}\n          module=\"breeding\"\n          tooltip={translateModuleField ? translateModuleField('breeding_performance_help', \"Shows performance metrics across different breeding aspects\") : \"Shows performance metrics across different breeding aspects\"}\n          formatValue={(value) => `${value}%`}\n          onRefresh={handleRefreshData}\n        />\n      </motion.div>\n\n      {/* Radar Chart Interaction Feedback */}\n      {radarTooltip.show && (\n        <motion.div\n          initial={{ opacity: 0, y: -10 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.3 }}\n        >\n          <Card sx={{\n            borderRadius: '12px',\n            boxShadow: '0 4px 20px rgba(0,0,0,0.08)',\n            mb: 4,\n            overflow: 'hidden',\n            background: `linear-gradient(135deg, ${alpha('#4A6FA5', 0.85)}, ${alpha('#3A5A8C', 0.75)})`,\n            color: 'white'\n          }}>\n            <CardContent>\n              <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\n                <Typography variant=\"h6\" fontWeight=\"bold\" color=\"white\">\n                  Breeding Performance Details\n                </Typography>\n                <IconButton size=\"small\" onClick={() => setRadarTooltip({ show: false, content: '' })} sx={{ color: 'white' }}>\n                  <MoreVert />\n                </IconButton>\n              </Box>\n              <Typography variant=\"body1\" mt={1} color=\"white\">\n                {radarTooltip.content}\n              </Typography>\n              {selectedMetric && (\n                <Box mt={2}>\n                  <Typography variant=\"body2\" color=\"rgba(255,255,255,0.8)\" mb={1}>\n                    Improvement Suggestions for {selectedMetric}:\n                  </Typography>\n                  <Typography variant=\"body2\" color=\"white\">\n                    {selectedMetric === 'Success Rate' && 'Optimize nutrition and timing for better breeding success.'}\n                    {selectedMetric === 'Conception Rate' && 'Consider adjusting insemination techniques and timing.'}\n                    {selectedMetric === 'Calving Ease' && 'Review breeding pairs for genetic compatibility to improve calving ease.'}\n                    {selectedMetric === 'Gestation Length' && 'Monitor and adjust nutrition during pregnancy for optimal gestation.'}\n                    {selectedMetric === 'Calf Survival' && 'Improve post-birth care protocols and monitoring.'}\n                    {selectedMetric === 'Dam Recovery' && 'Enhance post-partum care and nutrition for faster recovery.'}\n                  </Typography>\n                </Box>\n              )}\n            </CardContent>\n          </Card>\n        </motion.div>\n      )}\n\n      {/* Breeding Records Table */}\n      <motion.div\n        initial={{ opacity: 0, y: 20 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.8 }}\n      >\n        <Card sx={{\n          borderRadius: '12px',\n          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',\n          mb: 4,\n          overflow: 'hidden',\n          background: `linear-gradient(135deg, ${alpha('#4A6FA5', 0.85)}, ${alpha('#3A5A8C', 0.75)})`,\n        }}>\n          <Box sx={{ p: 2, color: 'white', display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\n            <Typography variant=\"h6\" fontWeight=\"bold\" color=\"white\">\n              Breeding Records\n            </Typography>\n            <Box>\n              <IconButton color=\"inherit\" size=\"small\">\n                <Search />\n              </IconButton>\n              <IconButton color=\"inherit\" size=\"small\">\n                <FilterList />\n              </IconButton>\n              <IconButton color=\"inherit\" size=\"small\">\n                <MoreVert />\n              </IconButton>\n            </Box>\n          </Box>\n          <TableContainer>\n            <Table>\n              <TableHead sx={{ bgcolor: 'rgba(255,255,255,0.1)' }}>\n                <TableRow>\n                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>ID</TableCell>\n                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Female</TableCell>\n                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Male</TableCell>\n                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Breeding Date</TableCell>\n                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Expected Due Date</TableCell>\n                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Method</TableCell>\n                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Status</TableCell>\n                  <TableCell sx={{ color: 'white', fontWeight: 'bold' }}>Actions</TableCell>\n                </TableRow>\n              </TableHead>\n              <TableBody>\n                {mockBreedingRecords.map((record) => (\n                  <TableRow key={record.id} hover sx={{ '&:hover': { bgcolor: 'rgba(255,255,255,0.1)' } }}>\n                    <TableCell sx={{ color: 'white' }}>{record.id}</TableCell>\n                    <TableCell sx={{ color: 'white' }}>{`Animal ${record.femaleId}`}</TableCell>\n                    <TableCell sx={{ color: 'white' }}>{`Animal ${record.maleId}`}</TableCell>\n                    <TableCell sx={{ color: 'white' }}>{new Date(record.date).toLocaleDateString()}</TableCell>\n                    <TableCell sx={{ color: 'white' }}>{new Date(record.expectedDueDate).toLocaleDateString()}</TableCell>\n                    <TableCell>\n                      <Chip\n                        label={record.type === 'natural' ? 'Natural' : 'Artificial'}\n                        sx={{ bgcolor: 'rgba(255,255,255,0.2)', color: 'white' }}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <Chip\n                        label={record.status.charAt(0).toUpperCase() + record.status.slice(1)}\n                        sx={{\n                          bgcolor: record.status === 'pending' ? 'rgba(255,193,7,0.2)' :\n                                  record.status === 'confirmed' ? 'rgba(76,175,80,0.2)' :\n                                  'rgba(244,67,54,0.2)',\n                          color: 'white'\n                        }}\n                        size=\"small\"\n                      />\n                    </TableCell>\n                    <TableCell>\n                      <IconButton size=\"small\" onClick={() => navigate(`/breeding/records/${record.id}`)} sx={{ color: 'white' }}>\n                        <ArrowForward fontSize=\"small\" />\n                      </IconButton>\n                    </TableCell>\n                  </TableRow>\n                ))}\n              </TableBody>\n            </Table>\n          </TableContainer>\n        </Card>\n      </motion.div>\n      </Box>\n    </StandardDashboard>\n  );\n};\n\n// Wrap the component with our HOC to provide translation functions\nexport default withSubModuleTranslation(BreedingDashboard, 'breeding', 'dashboard');\n"], "mappings": "kWAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,MAAM,KAAQ,eAAe,CACtC,OAASC,IAAI,CAAEC,IAAI,CAAEC,WAAW,CAAEC,UAAU,CAAEC,GAAG,CAAEC,IAAI,CAAEC,UAAU,CAAEC,MAAM,CAAkBC,QAAQ,CAAEC,IAAI,CAAEC,QAAQ,CAAEC,YAAY,CAAkBC,YAAY,CAAEC,KAAK,CAAEC,SAAS,CAAEC,SAAS,CAAEC,cAAc,CAAEC,SAAS,CAAEC,QAAQ,CAAEC,KAAK,KAAiL,eAAe,CAC1a,OACEC,GAAG,CAGHC,aAAa,CACbC,MAAM,CACNC,UAAU,CACVC,QAAQ,CACRC,YAAY,CACZC,WAAW,CAEXC,IAAI,CACJC,MAAM,CAGNC,aAAa,CAQbC,UAAU,CAKVC,OAAO,CACPC,OAAO,CAEPC,SAAS,CAGTC,SAAS,KAOH,yBAAyB,CA2BjC,OAAwBC,sBAAsB,CAA+EC,wBAAwB,CAAEC,iBAAiB,CAAEC,WAAW,CAAGC,YAAY,KAAQ,yBAAyB,CACrO,OAASC,mBAAmB,CAAEC,gBAAgB,CAAEC,eAAe,CAAEC,2BAA2B,KAAQ,0BAA0B,CAC9H,OAASC,MAAM,CAAEC,QAAQ,CAAEC,OAAO,CAAEC,QAAQ,CAAEC,OAAO,CAAmDC,QAAQ,CAAEC,OAAO,KAAQ,UAAU,CAC3I,OAASC,UAAU,KAAQ,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAGpD;AACA,KAAM,CAAAC,iBAAiB,CAAG,CACxBC,MAAM,CAAE,CAAEC,OAAO,CAAE,CAAE,CAAC,CACtBC,OAAO,CAAE,CACPD,OAAO,CAAE,CAAC,CACVE,UAAU,CAAE,CACVC,eAAe,CAAE,GACnB,CACF,CACF,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,CACnBL,MAAM,CAAE,CAAEM,CAAC,CAAE,EAAE,CAAEL,OAAO,CAAE,CAAE,CAAC,CAC7BC,OAAO,CAAE,CACPI,CAAC,CAAE,CAAC,CACJL,OAAO,CAAE,CAAC,CACVE,UAAU,CAAE,CACVI,QAAQ,CAAE,GACZ,CACF,CACF,CAAC,CAED;AAOA;AACA,KAAM,CAAAC,QAAQ,CAAIC,KAAoB,EAAK,CACzC,KAAM,CAAEC,QAAQ,CAAEC,KAAK,CAAEC,KAAgB,CAAC,CAAGH,KAAK,CAAfI,KAAK,CAAAC,wBAAA,CAAKL,KAAK,CAAAM,SAAA,EAElD,mBACEnB,IAAA,OAAAoB,aAAA,CAAAA,aAAA,EACEC,IAAI,CAAC,UAAU,CACfjB,MAAM,CAAEW,KAAK,GAAKC,KAAM,CACxBM,EAAE,sBAAAC,MAAA,CAAuBP,KAAK,CAAG,CACjC,kCAAAO,MAAA,CAAiCP,KAAK,CAAG,EACrCC,KAAK,MACTO,KAAK,CAAE,CAAEC,UAAU,CAAE,MAAO,CAAE,CAAAX,QAAA,CAE7BC,KAAK,GAAKC,KAAK,eACdhB,IAAA,CAACjD,GAAG,EAAA+D,QAAA,CACDA,QAAQ,CACN,CACN,EACE,CAAC,CAEV,CAAC,CAQD,KAAM,CAAAY,iBAAmD,CAAGC,IAAA,EAItD,IAJuD,CAC3DC,SAAS,CACTC,kBAAkB,CAClBC,oBACF,CAAC,CAAAH,IAAA,CACC,KAAM,CAAAI,KAAK,CAAG5E,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAA6E,QAAQ,CAAGvF,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEwF,QAAQ,CAAEC,WAAW,CAAEC,SAAS,CAAEC,WAAY,CAAC,CAAGtC,UAAU,CAAC,CAAC,CAEtE;AACA,KAAM,CAACuC,eAAe,CAAEC,kBAAkB,CAAC,CAAG/F,QAAQ,CAAmB,EAAE,CAAC,CAC5E,KAAM,CAAC4F,SAAS,CAAEI,YAAY,CAAC,CAAGhG,QAAQ,CAAC,IAAI,CAAC,CAChD,KAAM,CAACiG,KAAK,CAAEC,QAAQ,CAAC,CAAGlG,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAACmG,cAAc,CAAEC,iBAAiB,CAAC,CAAGpG,QAAQ,CAAC,CAAC,CAAC,CAEvD;AACA,KAAM,CAACqG,QAAQ,CAAEC,WAAW,CAAC,CAAGtG,QAAQ,CAAC,CAAC,CAAC,CAC3C,KAAM,CAACuG,SAAS,CAAEC,YAAY,CAAC,CAAGxG,QAAQ,CAA4B,OAAO,CAAC,CAC9E,KAAM,CAACyG,cAAc,CAAEC,iBAAiB,CAAC,CAAG1G,QAAQ,CAAgB,IAAI,CAAC,CACzE,KAAM,CAAC2G,YAAY,CAAEC,eAAe,CAAC,CAAG5G,QAAQ,CAAqC,CAAE6G,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,EAAG,CAAC,CAAC,CAClH,KAAM,CAACC,oBAAoB,CAAEC,uBAAuB,CAAC,CAAGhH,QAAQ,CAAgB,IAAI,CAAC,CACrF,KAAM,CAACiH,aAAa,CAAEC,gBAAgB,CAAC,CAAGlH,QAAQ,CAAgB,IAAI,CAAC,CACvE,KAAM,CAACmH,YAAY,CAAEC,eAAe,CAAC,CAAGpH,QAAQ,CAAqC,CAAE6G,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,EAAG,CAAC,CAAC,CAClH,KAAM,CAACO,aAAa,CAAEC,gBAAgB,CAAC,CAAGtH,QAAQ,CAAgB,IAAI,CAAC,CAEvE;AACA,KAAM,CAACuH,QAAQ,CAAEC,WAAW,CAAC,CAAGxH,QAAQ,CAAqB,IAAI,CAAC,CAClE,KAAM,CAACyH,YAAY,CAAEC,eAAe,CAAC,CAAG1H,QAAQ,CAAC,KAAK,CAAC,CAEvD;AACA,KAAM,CAAC2H,IAAI,CAAEC,OAAO,CAAC,CAAG5H,QAAQ,CAAC,CAAC,CAAC,CACnC,KAAM,CAAC6H,WAAW,CAAEC,cAAc,CAAC,CAAG9H,QAAQ,CAAC,CAAC,CAAC,CAEjD;AACA,KAAM,CAAC+H,SAAS,CAAEC,YAAY,CAAC,CAAGhI,QAAQ,CAA+B,CACvEiI,KAAK,CAAE,GAAI,CAAAC,IAAI,CAAC,GAAI,CAAAA,IAAI,CAAC,CAAC,CAACC,QAAQ,CAAC,GAAI,CAAAD,IAAI,CAAC,CAAC,CAAC7E,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAAC,CAAC+E,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAC3FC,GAAG,CAAE,GAAI,CAAAJ,IAAI,CAAC,CAAC,CAACE,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAC5C,CAAC,CAAC,CAEF;AACA,KAAM,CAACE,gBAAgB,CAAEC,mBAAmB,CAAC,CAAGxI,QAAQ,CAAC,KAAK,CAAC,CAE/D;AACAC,SAAS,CAAC,IAAM,CACd,KAAM,CAAAwI,oBAAoB,CAAG,KAAAA,CAAA,GAAY,CACvCzC,YAAY,CAAC,IAAI,CAAC,CAClBE,QAAQ,CAAC,IAAI,CAAC,CACd,GAAI,CACF;AACA,GAAIP,WAAW,EAAID,QAAQ,CAACgD,QAAQ,CAAE,CACpC,KAAM,CAAAC,OAAO,CAAG,KAAM,CAAAjD,QAAQ,CAACgD,QAAQ,CAACE,OAAO,CAAC,CAAC,CACjD7C,kBAAkB,CAAC4C,OAAO,CAAC,CAC7B,CAAC,IAAM,CACL;AACA5C,kBAAkB,CAACnD,mBAAmB,CAAC,CACzC,CACF,CAAE,MAAOiG,GAAG,CAAE,CACZC,OAAO,CAAC7C,KAAK,CAAC,kCAAkC,CAAE4C,GAAG,CAAC,CACtD3C,QAAQ,CAAC,2DAA2D,CAAC,CACrEH,kBAAkB,CAACnD,mBAAmB,CAAC,CACzC,CAAC,OAAS,CACRoD,YAAY,CAAC,KAAK,CAAC,CACrB,CACF,CAAC,CAEDyC,oBAAoB,CAAC,CAAC,CACxB,CAAC,CAAE,CAAC9C,WAAW,CAAED,QAAQ,CAACgD,QAAQ,CAAEvC,cAAc,CAAC,CAAC,CAEpD;AACA,KAAM,CAAA4C,iBAAiB,CAAGA,CAAA,GAAM,CAC9B3C,iBAAiB,CAAC4C,IAAI,EAAIA,IAAI,CAAG,CAAC,CAAC,CACrC,CAAC,CAED;AACA,KAAM,CAAAC,eAAe,CAAGA,CAACC,KAA2B,CAAEC,QAAgB,GAAK,CACzE7C,WAAW,CAAC6C,QAAQ,CAAC,CACvB,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAIC,KAAa,EAAK,CAC3C/C,WAAW,CAACgD,QAAQ,CAACD,KAAK,CAAE,EAAE,CAAC,CAAC,CAClC,CAAC,CAED;AACA,KAAM,CAAAE,eAAe,CAAIL,KAAoC,EAAK,CAChE1B,WAAW,CAAC0B,KAAK,CAACM,aAAa,CAAC,CAClC,CAAC,CAED,KAAM,CAAAC,eAAe,CAAGA,CAAA,GAAM,CAC5BjC,WAAW,CAAC,IAAI,CAAC,CACnB,CAAC,CAED;AACA,KAAM,CAAAkC,YAAY,CAAGA,CAAA,GAAM,CACzBZ,OAAO,CAACa,GAAG,iBAAA3E,MAAA,CAAiByC,YAAY,WAAS,CAAC,CAClDgC,eAAe,CAAC,CAAC,CACnB,CAAC,CAED;AACA,KAAM,CAAAG,gBAAgB,CAAGA,CAACV,KAAc,CAAEC,QAAgB,GAAK,CAC7DvB,OAAO,CAACuB,QAAQ,CAAC,CACnB,CAAC,CAED,KAAM,CAAAU,uBAAuB,CAAIX,KAA0C,EAAK,CAC9EpB,cAAc,CAACwB,QAAQ,CAACJ,KAAK,CAACY,MAAM,CAACtF,KAAK,CAAE,EAAE,CAAC,CAAC,CAChDoD,OAAO,CAAC,CAAC,CAAC,CACZ,CAAC,CAED;AACA,KAAM,CAAAmC,cAAc,CAAGjE,eAAe,CAACkE,MAAM,CAC7C,KAAM,CAAAC,kBAAkB,CAAGnE,eAAe,CAACoE,MAAM,CAACC,MAAM,EAAIA,MAAM,CAACC,MAAM,CAACC,WAAW,CAAC,CAAC,GAAK,WAAW,CAAC,CAACL,MAAM,CAC/G,KAAM,CAAAM,gBAAgB,CAAGxE,eAAe,CAACoE,MAAM,CAACC,MAAM,EAAIA,MAAM,CAACC,MAAM,CAACC,WAAW,CAAC,CAAC,GAAK,SAAS,CAAC,CAACL,MAAM,CAC3G,KAAM,CAAAO,qBAAqB,CAAGzE,eAAe,CAACoE,MAAM,CAACC,MAAM,EAAIA,MAAM,CAACC,MAAM,CAACC,WAAW,CAAC,CAAC,GAAK,cAAc,CAAC,CAACL,MAAM,CACrH,KAAM,CAAAQ,WAAW,CAAGT,cAAc,CAAG,CAAC,CAAGU,IAAI,CAACC,KAAK,CAAET,kBAAkB,CAAGF,cAAc,CAAI,GAAG,CAAC,CAAG,CAAC,CAEpG;AACA,KAAM,CAAAY,KAAK,CAAG,GAAI,CAAAzC,IAAI,CAAC,CAAC,CACxB,KAAM,CAAA0C,iBAAiB,CAAG9H,eAAe,CACtCoH,MAAM,CAACC,MAAM,EAAIjH,OAAO,CAACD,QAAQ,CAACkH,MAAM,CAACU,IAAI,CAAC,CAAEF,KAAK,CAAC,CAAC,CACvDG,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK/H,QAAQ,CAAC8H,CAAC,CAACF,IAAI,CAAC,CAACI,OAAO,CAAC,CAAC,CAAGhI,QAAQ,CAAC+H,CAAC,CAACH,IAAI,CAAC,CAACI,OAAO,CAAC,CAAC,CAAC,CACvEC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAEd,KAAM,CAAAC,cAAc,CAAGrF,eAAe,CACnCoE,MAAM,CAACC,MAAM,EAAIA,MAAM,CAACiB,eAAe,EAAIlI,OAAO,CAACD,QAAQ,CAACkH,MAAM,CAACiB,eAAe,CAAC,CAAET,KAAK,CAAC,EAAIxH,QAAQ,CAACF,QAAQ,CAACkH,MAAM,CAACiB,eAAe,CAAC,CAAEhI,OAAO,CAACuH,KAAK,CAAE,EAAE,CAAC,CAAC,CAAC,CAC9JG,IAAI,CAAC,CAACC,CAAC,CAAEC,CAAC,GAAK/H,QAAQ,CAAC8H,CAAC,CAACK,eAAgB,CAAC,CAACH,OAAO,CAAC,CAAC,CAAGhI,QAAQ,CAAC+H,CAAC,CAACI,eAAgB,CAAC,CAACH,OAAO,CAAC,CAAC,CAAC,CAC/FC,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAEd;AACA,KAAM,CAAAG,WAAW,CAAGxI,gBAAgB,CAACmH,MAAM,CAC3C,KAAM,CAAAsB,cAAc,CAAGzI,gBAAgB,CAAC0I,MAAM,CAAC,CAACC,GAAG,CAAErB,MAAM,GAAKqB,GAAG,CAAGrB,MAAM,CAACsB,iBAAiB,CAAE,CAAC,CAAC,CAClG,KAAM,CAAAC,QAAQ,CAAG7I,gBAAgB,CAACqH,MAAM,CAACC,MAAM,EAAIA,MAAM,CAACsB,iBAAiB,CAAG,CAAC,CAAC,CAACzB,MAAM,EACpFnH,gBAAgB,CAACmH,MAAM,EAAI,CAAC,CAAC,CAAG,GAAG,CACtC,KAAM,CAAA2B,gBAAgB,CAAG9I,gBAAgB,CAACqH,MAAM,CAACC,MAAM,EAAIA,MAAM,CAACyB,aAAa,CAAC,CAAC5B,MAAM,EACpFnH,gBAAgB,CAACmH,MAAM,EAAI,CAAC,CAAC,CAAG,GAAG,CACtC,KAAM,CAAA6B,cAAc,CAAGhJ,gBAAgB,CAACqH,MAAM,CAACC,MAAM,EAAIA,MAAM,CAAC2B,kBAAkB,CAAC,CAAC9B,MAAM,EACvFnH,gBAAgB,CAACmH,MAAM,EAAI,CAAC,CAAC,CAAG,GAAG,CAEtC;AACA,KAAM,CAAA+B,2BAA2B,CAAG,GAAG,CAAE;AACzC,KAAM,CAAAC,sBAAsB,CAAG,GAAG,CAAE;AACpC,KAAM,CAAAC,sBAAsB,CAAG,GAAG,CAAE;AAEpC;AACA,KAAM,CAAAC,kBAAkB,CAAG,CACzB,CAAEC,IAAI,CAAE,SAAS,CAAE3H,KAAK,CAAEsB,eAAe,CAACoE,MAAM,CAACC,MAAM,EAAIA,MAAM,CAACiC,IAAI,GAAK,SAAS,CAAC,CAACpC,MAAO,CAAC,CAC9F,CAAEmC,IAAI,CAAE,IAAI,CAAE3H,KAAK,CAAEsB,eAAe,CAACoE,MAAM,CAACC,MAAM,EAAIA,MAAM,CAACiC,IAAI,GAAK,YAAY,CAAC,CAACpC,MAAO,CAAC,CAC5F,CAAEmC,IAAI,CAAE,iBAAiB,CAAE3H,KAAK,CAAE,CAAE,CAAC,CACtC,CAED,KAAM,CAAA6H,kBAAkB,CAAG,CACzB,CAAEF,IAAI,CAAE,WAAW,CAAE3H,KAAK,CAAEyF,kBAAmB,CAAC,CAChD,CAAEkC,IAAI,CAAE,SAAS,CAAE3H,KAAK,CAAE8F,gBAAiB,CAAC,CAC5C,CAAE6B,IAAI,CAAE,QAAQ,CAAE3H,KAAK,CAAE+F,qBAAsB,CAAC,CACjD,CAED;AACA,KAAM,CAAA+B,2BAA2B,CAAGA,CAAA,GAAM,CACxC,KAAM,CAAAC,MAAM,CAAG,CAAC,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAC,CACnG,KAAM,CAAAC,IAAI,CAAGD,MAAM,CAACE,GAAG,CAACC,KAAK,GAAK,CAChCP,IAAI,CAAEO,KAAK,CACXC,OAAO,CAAE,CAAC,CACVC,EAAE,CAAE,CAAC,CACLC,MAAM,CAAE,CAAC,CACTC,OAAO,CAAE,CAAC,CACVC,OAAO,CAAE,CACX,CAAC,CAAC,CAAC,CAEH;AACA,GAAIjH,eAAe,CAACkE,MAAM,CAAG,CAAC,CAAE,CAC9BlE,eAAe,CAACkH,OAAO,CAAC7C,MAAM,EAAI,CAChC,KAAM,CAAAU,IAAI,CAAG5H,QAAQ,CAACkH,MAAM,CAACU,IAAI,CAAC,CAClC,KAAM,CAAAoC,UAAU,CAAG5J,QAAQ,CAACwH,IAAI,CAAC,CAEjC;AACA,GAAIV,MAAM,CAACiC,IAAI,GAAK,SAAS,CAAE,CAC7BI,IAAI,CAACS,UAAU,CAAC,CAACN,OAAO,EAAI,CAAC,CAC/B,CAAC,IAAM,IAAIxC,MAAM,CAACiC,IAAI,GAAK,YAAY,CAAE,CACvCI,IAAI,CAACS,UAAU,CAAC,CAACL,EAAE,EAAI,CAAC,CAC1B,CAEA;AACA,GAAIzC,MAAM,CAACC,MAAM,GAAK,WAAW,CAAE,CACjCoC,IAAI,CAACS,UAAU,CAAC,CAACH,OAAO,EAAI,CAAC,CAC/B,CAAC,IAAM,IAAI3C,MAAM,CAACC,MAAM,GAAK,cAAc,CAAE,CAC3CoC,IAAI,CAACS,UAAU,CAAC,CAACF,OAAO,EAAI,CAAC,CAC/B,CACF,CAAC,CAAC,CACJ,CAAC,IAAM,CACL;AACA,MAAO,CACL,CAAEZ,IAAI,CAAE,KAAK,CAAEQ,OAAO,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEC,OAAO,CAAE,EAAE,CAAEC,OAAO,CAAE,CAAE,CAAC,CACtE,CAAEZ,IAAI,CAAE,KAAK,CAAEQ,OAAO,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEC,OAAO,CAAE,EAAE,CAAEC,OAAO,CAAE,CAAE,CAAC,CACtE,CAAEZ,IAAI,CAAE,KAAK,CAAEQ,OAAO,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEC,OAAO,CAAE,EAAE,CAAEC,OAAO,CAAE,CAAE,CAAC,CACtE,CAAEZ,IAAI,CAAE,KAAK,CAAEQ,OAAO,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEC,OAAO,CAAE,EAAE,CAAEC,OAAO,CAAE,CAAE,CAAC,CACtE,CAAEZ,IAAI,CAAE,KAAK,CAAEQ,OAAO,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEC,OAAO,CAAE,EAAE,CAAEC,OAAO,CAAE,CAAE,CAAC,CACtE,CAAEZ,IAAI,CAAE,KAAK,CAAEQ,OAAO,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEC,OAAO,CAAE,EAAE,CAAEC,OAAO,CAAE,CAAE,CAAC,CACtE,CAAEZ,IAAI,CAAE,KAAK,CAAEQ,OAAO,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEC,OAAO,CAAE,EAAE,CAAEC,OAAO,CAAE,CAAE,CAAC,CACtE,CAAEZ,IAAI,CAAE,KAAK,CAAEQ,OAAO,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEC,OAAO,CAAE,EAAE,CAAEC,OAAO,CAAE,CAAE,CAAC,CACtE,CAAEZ,IAAI,CAAE,KAAK,CAAEQ,OAAO,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEC,OAAO,CAAE,EAAE,CAAEC,OAAO,CAAE,CAAE,CAAC,CACtE,CAAEZ,IAAI,CAAE,KAAK,CAAEQ,OAAO,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEC,OAAO,CAAE,EAAE,CAAEC,OAAO,CAAE,CAAE,CAAC,CACtE,CAAEZ,IAAI,CAAE,KAAK,CAAEQ,OAAO,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEC,OAAO,CAAE,EAAE,CAAEC,OAAO,CAAE,CAAE,CAAC,CACtE,CAAEZ,IAAI,CAAE,KAAK,CAAEQ,OAAO,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAC,CAAEC,OAAO,CAAE,EAAE,CAAEC,OAAO,CAAE,CAAE,CAAC,CACvE,CACH,CAEA,MAAO,CAAAP,IAAI,CACb,CAAC,CAED,KAAM,CAAAU,mBAAmB,CAAGZ,2BAA2B,CAAC,CAAC,CAEzD;AACA,KAAM,CAAAa,4BAA4B,CAAGA,CAAA,GAAM,CACzC,KAAM,CAAAZ,MAAM,CAAG,CAAC,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAE,KAAK,CAAC,CACnG,KAAM,CAAAa,WAAW,CAAG,GAAI,CAAAlF,IAAI,CAAC,CAAC,CAACmF,WAAW,CAAC,CAAC,CAE5C;AACA,KAAM,CAAAb,IAAI,CAAGD,MAAM,CAACE,GAAG,CAAC,CAACC,KAAK,CAAEjI,KAAK,IAAM,CACzC0H,IAAI,CAAEO,KAAK,CACXY,IAAI,CAAE,EAAE,CAAG7C,IAAI,CAAC8C,KAAK,CAAC9C,IAAI,CAAC+C,MAAM,CAAC,CAAC,CAAG,EAAE,CAAC,CAAE;AAC3CC,IAAI,CAAEL,WACR,CAAC,CAAC,CAAC,CAEH;AACA,GAAItH,eAAe,CAACkE,MAAM,CAAG,CAAC,CAAE,CAC9B;AACA,KAAM,CAAA0D,cAAc,CAAG,GAAI,CAAAC,KAAK,CAAC,EAAE,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAACnB,GAAG,CAAC,KAAO,CAAEoB,KAAK,CAAE,CAAC,CAAEf,OAAO,CAAE,CAAE,CAAC,CAAC,CAAC,CAElFhH,eAAe,CAACkH,OAAO,CAAC7C,MAAM,EAAI,CAChC,KAAM,CAAAU,IAAI,CAAG5H,QAAQ,CAACkH,MAAM,CAACU,IAAI,CAAC,CAClC,KAAM,CAAA4C,IAAI,CAAGnK,OAAO,CAACuH,IAAI,CAAC,CAE1B;AACA,GAAI4C,IAAI,GAAKL,WAAW,CAAE,CACxB,KAAM,CAAAH,UAAU,CAAG5J,QAAQ,CAACwH,IAAI,CAAC,CACjC6C,cAAc,CAACT,UAAU,CAAC,CAACY,KAAK,EAAI,CAAC,CAErC,GAAI1D,MAAM,CAACC,MAAM,GAAK,WAAW,CAAE,CACjCsD,cAAc,CAACT,UAAU,CAAC,CAACH,OAAO,EAAI,CAAC,CACzC,CACF,CACF,CAAC,CAAC,CAEF;AACAY,cAAc,CAACV,OAAO,CAAC,CAACc,SAAS,CAAErJ,KAAK,GAAK,CAC3C,GAAIqJ,SAAS,CAACD,KAAK,CAAG,CAAC,CAAE,CACvBrB,IAAI,CAAC/H,KAAK,CAAC,CAAC6I,IAAI,CAAG7C,IAAI,CAACC,KAAK,CAAEoD,SAAS,CAAChB,OAAO,CAAGgB,SAAS,CAACD,KAAK,CAAI,GAAG,CAAC,CAC5E,CACF,CAAC,CAAC,CACJ,CAEA,MAAO,CAAArB,IAAI,CACb,CAAC,CAED,KAAM,CAAAuB,oBAAoB,CAAGZ,4BAA4B,CAAC,CAAC,CAE3D;AACA,KAAM,CAAAa,2BAA2B,CAAG,CAClC,CAAE7B,IAAI,CAAE,QAAQ,CAAEW,OAAO,CAAE,EAAE,CAAEmB,QAAQ,CAAE,GAAI,CAAC,CAC9C,CAAE9B,IAAI,CAAE,OAAO,CAAEW,OAAO,CAAE,EAAE,CAAEmB,QAAQ,CAAE,EAAG,CAAC,CAC5C,CAAE9B,IAAI,CAAE,OAAO,CAAEW,OAAO,CAAE,EAAE,CAAEmB,QAAQ,CAAE,EAAG,CAAC,CAC5C,CAAE9B,IAAI,CAAE,MAAM,CAAEW,OAAO,CAAE,EAAE,CAAEmB,QAAQ,CAAE,GAAI,CAAC,CAC7C,CAED;AACA,KAAM,CAAAC,sBAAsB,CAAG,CAC7B,CAAE/B,IAAI,CAAE,QAAQ,CAAEgC,UAAU,CAAE,EAAG,CAAC,CAClC,CAAEhC,IAAI,CAAE,OAAO,CAAEgC,UAAU,CAAE,EAAG,CAAC,CACjC,CAAEhC,IAAI,CAAE,OAAO,CAAEgC,UAAU,CAAE,EAAG,CAAC,CACjC,CAAEhC,IAAI,CAAE,MAAM,CAAEgC,UAAU,CAAE,EAAG,CAAC,CACjC,CAED;AACA,KAAM,CAAAC,sBAAsB,CAAG,CAC7B,CAAEjC,IAAI,CAAE,MAAM,CAAE3H,KAAK,CAAE,EAAG,CAAC,CAC3B,CAAE2H,IAAI,CAAE,OAAO,CAAE3H,KAAK,CAAE,EAAG,CAAC,CAC5B,CAAE2H,IAAI,CAAE,OAAO,CAAE3H,KAAK,CAAE,CAAE,CAAC,CAC5B,CAED;AACA,KAAM,CAAA6J,sBAAsB,CAAG,CAC7B,CAAEZ,IAAI,CAAE,MAAM,CAAEa,WAAW,CAAE,CAAE,CAAC,CAChC,CAAEb,IAAI,CAAE,MAAM,CAAEa,WAAW,CAAE,CAAE,CAAC,CAChC,CAAEb,IAAI,CAAE,MAAM,CAAEa,WAAW,CAAE,EAAG,CAAC,CACjC,CAAEb,IAAI,CAAE,MAAM,CAAEa,WAAW,CAAE,EAAG,CAAC,CACjC,CAAEb,IAAI,CAAE,MAAM,CAAEa,WAAW,CAAE,EAAG,CAAC,CACjC,CAAEb,IAAI,CAAE,MAAM,CAAEa,WAAW,CAAE,EAAG,CAAC,CAClC,CAED;AACA,KAAM,CAAAC,oBAAoB,CAAG,CAC3B,CAAEC,MAAM,CAAE,QAAQ,CAAEC,SAAS,CAAE,EAAE,CAAE3B,OAAO,CAAE,EAAG,CAAC,CAChD,CAAE0B,MAAM,CAAE,QAAQ,CAAEC,SAAS,CAAE,EAAE,CAAE3B,OAAO,CAAE,EAAG,CAAC,CAChD,CAAE0B,MAAM,CAAE,MAAM,CAAEC,SAAS,CAAE,EAAE,CAAE3B,OAAO,CAAE,EAAG,CAAC,CAC9C,CAAE0B,MAAM,CAAE,QAAQ,CAAEC,SAAS,CAAE,EAAE,CAAE3B,OAAO,CAAE,EAAG,CAAC,CACjD,CAED;AACA,KAAM,CAAA4B,uBAAuB,CAAG,CAC9B,CAAEhC,KAAK,CAAE,KAAK,CAAEiC,KAAK,CAAE,EAAG,CAAC,CAC3B,CAAEjC,KAAK,CAAE,KAAK,CAAEiC,KAAK,CAAE,EAAG,CAAC,CAC3B,CAAEjC,KAAK,CAAE,KAAK,CAAEiC,KAAK,CAAE,EAAG,CAAC,CAC3B,CAAEjC,KAAK,CAAE,KAAK,CAAEiC,KAAK,CAAE,EAAG,CAAC,CAC3B,CAAEjC,KAAK,CAAE,KAAK,CAAEiC,KAAK,CAAE,EAAG,CAAC,CAC3B,CAAEjC,KAAK,CAAE,KAAK,CAAEiC,KAAK,CAAE,EAAG,CAAC,CAC3B,CAAEjC,KAAK,CAAE,KAAK,CAAEiC,KAAK,CAAE,CAAE,CAAC,CAC1B,CAAEjC,KAAK,CAAE,KAAK,CAAEiC,KAAK,CAAE,CAAE,CAAC,CAC1B,CAAEjC,KAAK,CAAE,KAAK,CAAEiC,KAAK,CAAE,CAAE,CAAC,CAC1B,CAAEjC,KAAK,CAAE,KAAK,CAAEiC,KAAK,CAAE,EAAG,CAAC,CAC3B,CAAEjC,KAAK,CAAE,KAAK,CAAEiC,KAAK,CAAE,EAAG,CAAC,CAC3B,CAAEjC,KAAK,CAAE,KAAK,CAAEiC,KAAK,CAAE,EAAG,CAAC,CAC5B,CAED;AACA,KAAM,CAAAC,mBAAmB,CAAG,CAC1B,CAAEC,MAAM,CAAE,SAAS,CAAEF,KAAK,CAAE,CAAE,CAAC,CAC/B,CAAEE,MAAM,CAAE,SAAS,CAAEF,KAAK,CAAE,EAAG,CAAC,CAChC,CAAEE,MAAM,CAAE,SAAS,CAAEF,KAAK,CAAE,EAAG,CAAC,CAChC,CAAEE,MAAM,CAAE,SAAS,CAAEF,KAAK,CAAE,EAAG,CAAC,CAChC,CAAEE,MAAM,CAAE,SAAS,CAAEF,KAAK,CAAE,EAAG,CAAC,CAChC,CAAEE,MAAM,CAAE,SAAS,CAAEF,KAAK,CAAE,CAAE,CAAC,CAChC,CAED,KAAM,CAAAG,MAAM,CAAG,CACbtJ,KAAK,CAACuJ,OAAO,CAACC,OAAO,CAACC,IAAI,CAC1BzJ,KAAK,CAACuJ,OAAO,CAACG,SAAS,CAACD,IAAI,CAC5BzJ,KAAK,CAACuJ,OAAO,CAACjC,OAAO,CAACmC,IAAI,CAC1BzJ,KAAK,CAACuJ,OAAO,CAACI,OAAO,CAACF,IAAI,CAC1BzJ,KAAK,CAACuJ,OAAO,CAAC9I,KAAK,CAACgJ,IAAI,CACxBzJ,KAAK,CAACuJ,OAAO,CAACK,IAAI,CAACH,IAAI,CACxB,CAED;AACA,KAAM,CAAAI,cAAc,CAAG,CACrB,CACEC,KAAK,CAAE/J,oBAAoB,CAAGA,oBAAoB,CAAC,iBAAiB,CAAE,iBAAiB,CAAC,CAAG,iBAAiB,CAC5Gf,KAAK,CAAEuF,cAAc,CACrBwF,IAAI,cAAE9L,IAAA,CAAC1B,IAAI,GAAE,CAAC,CACdyN,KAAK,CAAEhK,KAAK,CAACuJ,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCQ,KAAK,CAAE,CACLjL,KAAK,CAAE,CAAC,CACRkL,UAAU,CAAE,IAAI,CAChBJ,KAAK,CAAE,kBACT,CACF,CAAC,CACD,CACEA,KAAK,CAAE/J,oBAAoB,CAAGA,oBAAoB,CAAC,cAAc,CAAE,cAAc,CAAC,CAAG,cAAc,CACnGf,KAAK,IAAAQ,MAAA,CAAKwF,WAAW,KAAG,CACxB+E,IAAI,cAAE9L,IAAA,CAAC3B,WAAW,GAAE,CAAC,CACrB0N,KAAK,CAAEhK,KAAK,CAACuJ,OAAO,CAACjC,OAAO,CAACmC,IAAI,CACjCQ,KAAK,CAAE,CACLjL,KAAK,CAAE,CAAC,CACRkL,UAAU,CAAE,IAAI,CAChBJ,KAAK,CAAE,kBACT,CACF,CAAC,CACD,CACEA,KAAK,CAAE/J,oBAAoB,CAAGA,oBAAoB,CAAC,iBAAiB,CAAE,iBAAiB,CAAC,CAAG,iBAAiB,CAC5Gf,KAAK,CAAE2G,cAAc,CAACnB,MAAM,CAC5BuF,IAAI,cAAE9L,IAAA,CAACzB,MAAM,GAAE,CAAC,CAChBwN,KAAK,CAAEhK,KAAK,CAACuJ,OAAO,CAACK,IAAI,CAACH,IAAI,CAC9BQ,KAAK,CAAE,CACLjL,KAAK,CAAE,CAAC,CACRkL,UAAU,CAAE,IAAI,CAChBJ,KAAK,CAAE,kBACT,CACF,CAAC,CACD,CACEA,KAAK,CAAE/J,oBAAoB,CAAGA,oBAAoB,CAAC,aAAa,CAAE,aAAa,CAAC,CAAG,aAAa,CAChGf,KAAK,CAAEoG,iBAAiB,CAACZ,MAAM,CAC/BuF,IAAI,cAAE9L,IAAA,CAAChC,aAAa,GAAE,CAAC,CACvB+N,KAAK,CAAEhK,KAAK,CAACuJ,OAAO,CAACI,OAAO,CAACF,IAAI,CACjCQ,KAAK,CAAE,CACLjL,KAAK,CAAE,CAAC,CACRkL,UAAU,CAAE,KAAK,CACjBJ,KAAK,CAAE,kBACT,CACF,CAAC,CACF,CAED;AACA,KAAM,CAAAK,gBAAgB,CAAG,CACvB,CACEL,KAAK,CAAE/J,oBAAoB,CAAGA,oBAAoB,CAAC,qBAAqB,CAAE,qBAAqB,CAAC,CAAG,qBAAqB,CACxHgK,IAAI,cAAE9L,IAAA,CAACjC,GAAG,GAAE,CAAC,CACboO,OAAO,CAAEA,CAAA,GAAMnK,QAAQ,CAAC,oBAAoB,CAAC,CAC7C+J,KAAK,CAAE,SACT,CAAC,CACF,CAED;AACA,KAAM,CAAAK,aAAa,CAAG,CACpB,CACEP,KAAK,CAAE/J,oBAAoB,CAAGA,oBAAoB,CAAC,UAAU,CAAE,UAAU,CAAC,CAAG,UAAU,CACvFgK,IAAI,cAAE9L,IAAA,CAACnB,SAAS,GAAE,CAAC,CACnBwE,OAAO,cACLrD,IAAA,CAACjD,GAAG,GAEC,CAET,CAAC,CACD,CACE8O,KAAK,CAAE/J,oBAAoB,CAAGA,oBAAoB,CAAC,qBAAqB,CAAE,qBAAqB,CAAC,CAAG,qBAAqB,CACxHgK,IAAI,cAAE9L,IAAA,CAACvB,UAAU,GAAE,CAAC,CACpB4E,OAAO,cACLrD,IAAA,CAACjD,GAAG,GAEC,CAET,CAAC,CACD,CACE8O,KAAK,CAAE/J,oBAAoB,CAAGA,oBAAoB,CAAC,kBAAkB,CAAE,kBAAkB,CAAC,CAAG,kBAAkB,CAC/GgK,IAAI,cAAE9L,IAAA,CAACtB,OAAO,GAAE,CAAC,CACjB2E,OAAO,cACLrD,IAAA,CAACjD,GAAG,GAEC,CAET,CAAC,CACD,CACE8O,KAAK,CAAE/J,oBAAoB,CAAGA,oBAAoB,CAAC,sBAAsB,CAAE,wBAAwB,CAAC,CAAG,wBAAwB,CAC/HgK,IAAI,cAAE9L,IAAA,CAACrB,OAAO,GAAE,CAAC,CACjB0E,OAAO,cACLrD,IAAA,CAACjD,GAAG,GAEC,CAET,CAAC,CACD,CACE8O,KAAK,CAAE/J,oBAAoB,CAAGA,oBAAoB,CAAC,mBAAmB,CAAE,mBAAmB,CAAC,CAAG,mBAAmB,CAClHgK,IAAI,cAAE9L,IAAA,CAACpB,SAAS,GAAE,CAAC,CACnByE,OAAO,cACLrD,IAAA,CAACjD,GAAG,GAEC,CAET,CAAC,CACF,CAED,mBACEiD,IAAA,CAAChB,iBAAiB,EAChBqN,KAAK,CAAExK,kBAAkB,CAAGA,kBAAkB,CAAC,OAAO,CAAE,qBAAqB,CAAC,CAAG,qBAAsB,CACvGyK,QAAQ,CAAEzK,kBAAkB,CAAGA,kBAAkB,CAAC,UAAU,CAAE,gGAAgG,CAAC,CAAG,gGAAiG,CACnQiK,IAAI,cAAE9L,IAAA,CAAC1B,IAAI,GAAE,CAAE,CACfiO,KAAK,CAAEX,cAAe,CACtBY,OAAO,CAAEN,gBAAiB,CAC1BO,IAAI,CAAEL,aAAc,CACpBM,SAAS,CAAE9J,QAAS,CACpB+J,WAAW,CAAEnH,eAAgB,CAC7BrD,SAAS,CAAEA,SAAS,EAAIC,WAAY,CACpCwK,cAAc,CAAE9K,oBAAoB,CAAGA,oBAAoB,CAAC,SAAS,CAAE,0BAA0B,CAAC,CAAG,0BAA2B,CAChI+K,SAAS,CAAEvH,iBAAkB,CAC7BwH,MAAM,CAAC,UAAU,CAAAhM,QAAA,cAGjBZ,KAAA,CAACnD,GAAG,EAACgQ,EAAE,CAAE,CAAEC,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAAnM,QAAA,eAExBZ,KAAA,CAACxD,MAAM,CAACwQ,GAAG,EACTC,QAAQ,CAAEhN,iBAAkB,CAC5BiN,OAAO,CAAC,QAAQ,CAChBC,OAAO,CAAC,SAAS,CACjBC,SAAS,CAAC,MAAM,CAAAxM,QAAA,eAEhBd,IAAA,CAAClD,UAAU,EAACyQ,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,MAAM,CAACC,YAAY,MAACV,EAAE,CAAE,CAAEW,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAA7M,QAAA,CAC1EgB,oBAAoB,CAAGA,oBAAoB,CAAC,qBAAqB,CAAE,qBAAqB,CAAC,CAAG,qBAAqB,CACxG,CAAC,cAEb5B,KAAA,CAACvD,IAAI,EAACiR,SAAS,MAACC,OAAO,CAAE,CAAE,CAAA/M,QAAA,eACzBd,IAAA,CAACrD,IAAI,EAACmR,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlN,QAAA,cACvBd,IAAA,CAACtD,MAAM,CAACwQ,GAAG,EAACC,QAAQ,CAAE1M,YAAa,CAAAK,QAAA,cACjCd,IAAA,CAAClB,sBAAsB,EACrBuN,KAAK,CAAC,iBAAiB,CACvBC,QAAQ,IAAA/K,MAAA,CAAK+E,cAAc,qBAAoB,CAC/CwG,MAAM,CAAC,UAAU,CACjBmB,QAAQ,CAAC,gBAAgB,CACzBnC,IAAI,cAAE9L,IAAA,CAAC1B,IAAI,GAAE,CAAE,CACf4P,MAAM,CAAE,GAAI,CACZC,WAAW,CAAEpM,KAAK,CAACuJ,OAAO,CAACC,OAAO,CAACC,IAAK,CACzC,CAAC,CACQ,CAAC,CACT,CAAC,cAEPxL,IAAA,CAACrD,IAAI,EAACmR,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlN,QAAA,cACvBd,IAAA,CAACtD,MAAM,CAACwQ,GAAG,EAACC,QAAQ,CAAE1M,YAAa,CAAAK,QAAA,cACjCd,IAAA,CAAClB,sBAAsB,EACrBuN,KAAK,CAAC,cAAc,CACpBC,QAAQ,IAAA/K,MAAA,CAAKwF,WAAW,0BAAyB,CACjD+F,MAAM,CAAC,UAAU,CACjBmB,QAAQ,CAAC,kBAAkB,CAC3BnC,IAAI,cAAE9L,IAAA,CAAC3B,WAAW,GAAE,CAAE,CACtB6P,MAAM,CAAE,GAAI,CACZC,WAAW,CAAEpM,KAAK,CAACuJ,OAAO,CAACjC,OAAO,CAACmC,IAAK,CACxC4C,cAAc,CAAErM,KAAK,CAACuJ,OAAO,CAACjC,OAAO,CAACgF,IAAK,CAC5C,CAAC,CACQ,CAAC,CACT,CAAC,cAEPrO,IAAA,CAACrD,IAAI,EAACmR,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlN,QAAA,cACvBd,IAAA,CAACtD,MAAM,CAACwQ,GAAG,EAACC,QAAQ,CAAE1M,YAAa,CAAAK,QAAA,cACjCd,IAAA,CAAClB,sBAAsB,EACrBuN,KAAK,CAAC,iBAAiB,CACvBC,QAAQ,IAAA/K,MAAA,CAAKmG,cAAc,CAACnB,MAAM,6BAA4B,CAC9DuG,MAAM,CAAC,UAAU,CACjBmB,QAAQ,CAAC,iBAAiB,CAC1BnC,IAAI,cAAE9L,IAAA,CAACzB,MAAM,GAAE,CAAE,CACjB2P,MAAM,CAAE,GAAI,CACZC,WAAW,CAAEpM,KAAK,CAACuJ,OAAO,CAACK,IAAI,CAACH,IAAK,CACrC4C,cAAc,CAAErM,KAAK,CAACuJ,OAAO,CAACK,IAAI,CAAC0C,IAAK,CACzC,CAAC,CACQ,CAAC,CACT,CAAC,cAEPrO,IAAA,CAACrD,IAAI,EAACmR,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlN,QAAA,cACvBd,IAAA,CAACtD,MAAM,CAACwQ,GAAG,EAACC,QAAQ,CAAE1M,YAAa,CAAAK,QAAA,cACjCd,IAAA,CAAClB,sBAAsB,EACrBuN,KAAK,CAAC,aAAa,CACnBC,QAAQ,IAAA/K,MAAA,CAAK4F,iBAAiB,CAACZ,MAAM,yBAAwB,CAC7DuG,MAAM,CAAC,UAAU,CACjBmB,QAAQ,CAAC,eAAe,CACxBnC,IAAI,cAAE9L,IAAA,CAAChC,aAAa,GAAE,CAAE,CACxBkQ,MAAM,CAAE,GAAI,CACZC,WAAW,CAAEpM,KAAK,CAACuJ,OAAO,CAACI,OAAO,CAACF,IAAK,CACxC4C,cAAc,CAAErM,KAAK,CAACuJ,OAAO,CAACI,OAAO,CAAC2C,IAAK,CAC5C,CAAC,CACQ,CAAC,CACT,CAAC,EACH,CAAC,EACG,CAAC,cAGbnO,KAAA,CAACxD,MAAM,CAACwQ,GAAG,EACTC,QAAQ,CAAEhN,iBAAkB,CAC5BiN,OAAO,CAAC,QAAQ,CAChBC,OAAO,CAAC,SAAS,CACjBC,SAAS,CAAC,MAAM,CAAAxM,QAAA,eAEhBd,IAAA,CAAClD,UAAU,EAACyQ,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,MAAM,CAACC,YAAY,MAACV,EAAE,CAAE,CAAEW,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAA7M,QAAA,CAC1EgB,oBAAoB,CAAGA,oBAAoB,CAAC,oBAAoB,CAAE,oBAAoB,CAAC,CAAG,oBAAoB,CACrG,CAAC,cAEb5B,KAAA,CAACvD,IAAI,EAACiR,SAAS,MAACC,OAAO,CAAE,CAAE,CAAA/M,QAAA,eACzBd,IAAA,CAACrD,IAAI,EAACmR,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlN,QAAA,cACvBd,IAAA,CAACf,WAAW,EACVoN,KAAK,CAAEvK,oBAAoB,CAAGA,oBAAoB,CAAC,kBAAkB,CAAE,kBAAkB,CAAC,CAAG,kBAAmB,CAChHwK,QAAQ,CAAExK,oBAAoB,CAAGA,oBAAoB,CAAC,uBAAuB,CAAE,uCAAuC,CAAC,CAAG,uCAAwC,CAClKiH,IAAI,CAAEN,kBAAmB,CACzBE,IAAI,CAAC,KAAK,CACV2F,QAAQ,CAAE,CAAC,OAAO,CAAE,CACpBJ,MAAM,CAAE,GAAI,CACZC,WAAW,CAAEpM,KAAK,CAACuJ,OAAO,CAACC,OAAO,CAACC,IAAK,CACxC+C,oBAAoB,CAAE,IAAK,CAC3BzB,MAAM,CAAC,UAAU,CACjB0B,OAAO,CAAE1M,oBAAoB,CAAGA,oBAAoB,CAAC,uBAAuB,CAAE,2DAA2D,CAAC,CAAG,2DAA4D,CACzM2M,WAAW,CAAG1N,KAAK,KAAAQ,MAAA,CAAQR,KAAK,YAAW,CAC5C,CAAC,CACE,CAAC,cAEPf,IAAA,CAACrD,IAAI,EAACmR,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlN,QAAA,cACvBd,IAAA,CAACf,WAAW,EACVoN,KAAK,CAAEvK,oBAAoB,CAAGA,oBAAoB,CAAC,iBAAiB,CAAE,iBAAiB,CAAC,CAAG,iBAAkB,CAC7GwK,QAAQ,CAAExK,oBAAoB,CAAGA,oBAAoB,CAAC,sBAAsB,CAAE,oCAAoC,CAAC,CAAG,oCAAqC,CAC3JiH,IAAI,CAAEH,kBAAmB,CACzBD,IAAI,CAAC,KAAK,CACV2F,QAAQ,CAAE,CAAC,OAAO,CAAE,CACpBJ,MAAM,CAAE,GAAI,CACZC,WAAW,CAAEpM,KAAK,CAACuJ,OAAO,CAACG,SAAS,CAACD,IAAK,CAC1C+C,oBAAoB,CAAE,IAAK,CAC3BzB,MAAM,CAAC,UAAU,CACjB0B,OAAO,CAAE1M,oBAAoB,CAAGA,oBAAoB,CAAC,sBAAsB,CAAE,kDAAkD,CAAC,CAAG,kDAAmD,CACtL2M,WAAW,CAAG1N,KAAK,KAAAQ,MAAA,CAAQR,KAAK,YAAW,CAC5C,CAAC,CACE,CAAC,cACPf,IAAA,CAACrD,IAAI,EAACmR,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAjN,QAAA,cAChBd,IAAA,CAACf,WAAW,EACVoN,KAAK,CAAEvK,oBAAoB,CAAGA,oBAAoB,CAAC,kBAAkB,CAAE,2BAA2B,CAAC,CAAG,2BAA4B,CAClIwK,QAAQ,CAAExK,oBAAoB,CAAGA,oBAAoB,CAAC,uBAAuB,CAAE,2CAA2C,CAAC,CAAG,2CAA4C,CAC1KiH,IAAI,CAAEU,mBAAoB,CAC1Bd,IAAI,CAAC,KAAK,CACV2F,QAAQ,CAAE,CAAC,SAAS,CAAE,IAAI,CAAE,QAAQ,CAAE,CACtCI,YAAY,CAAC,MAAM,CACnBR,MAAM,CAAE,GAAI,CACZC,WAAW,CAAEpM,KAAK,CAACuJ,OAAO,CAACC,OAAO,CAACC,IAAK,CACxC+C,oBAAoB,CAAE,IAAK,CAC3BI,oBAAoB,CAAE,IAAK,CAC3B7B,MAAM,CAAC,UAAU,CACjB0B,OAAO,CAAE1M,oBAAoB,CAAGA,oBAAoB,CAAC,uBAAuB,CAAE,6CAA6C,CAAC,CAAG,6CAA8C,CAC7K2M,WAAW,CAAG1N,KAAK,KAAAQ,MAAA,CAAQR,KAAK,cAAa,CAC9C,CAAC,CACE,CAAC,EACH,CAAC,EACG,CAAC,cAGbb,KAAA,CAACxD,MAAM,CAACwQ,GAAG,EACTC,QAAQ,CAAEhN,iBAAkB,CAC5BiN,OAAO,CAAC,QAAQ,CAChBC,OAAO,CAAC,SAAS,CACjBC,SAAS,CAAC,MAAM,CAAAxM,QAAA,eAEhBd,IAAA,CAAClD,UAAU,EAACyQ,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,MAAM,CAACC,YAAY,MAACV,EAAE,CAAE,CAAEW,EAAE,CAAE,CAAC,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAA7M,QAAA,CAC1EgB,oBAAoB,CAAGA,oBAAoB,CAAC,iBAAiB,CAAE,iBAAiB,CAAC,CAAG,iBAAiB,CAC5F,CAAC,cAEb5B,KAAA,CAACvD,IAAI,EAACiR,SAAS,MAACC,OAAO,CAAE,CAAE,CAAA/M,QAAA,eACzBd,IAAA,CAACrD,IAAI,EAACmR,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlN,QAAA,cACvBd,IAAA,CAACtD,MAAM,CAACwQ,GAAG,EAACC,QAAQ,CAAE1M,YAAa,CAAAK,QAAA,cACjCd,IAAA,CAAClB,sBAAsB,EACrBuN,KAAK,CAAC,sBAAsB,CAC5BC,QAAQ,CAAC,qCAAqC,CAC9CQ,MAAM,CAAC,UAAU,CACjBmB,QAAQ,CAAC,wBAAwB,CACjCC,MAAM,CAAE,GAAI,CACZpC,IAAI,cAAE9L,IAAA,CAAChC,aAAa,GAAE,CAAE,CACxBmQ,WAAW,CAAEpM,KAAK,CAACuJ,OAAO,CAACI,OAAO,CAACF,IAAK,CACxCoD,MAAM,cACJ5O,IAAA,CAACd,YAAY,EACX2P,IAAI,CAAC,OAAO,CACZtB,OAAO,CAAC,UAAU,CAClBpB,OAAO,CAAEA,CAAA,GAAMnK,QAAQ,CAAC,yBAAyB,CAAE,CACnD+K,EAAE,CAAE,CACFhB,KAAK,CAAE,OAAO,CACd+C,WAAW,CAAE,uBAAuB,CACpC,SAAS,CAAE,CAAEA,WAAW,CAAE,OAAO,CAAEC,OAAO,CAAE,uBAAwB,CACtE,CAAE,CAAAjO,QAAA,CACH,eAED,CAAc,CACf,CACDuC,OAAO,cACLrD,IAAA,CAAC5C,IAAI,EAAA0D,QAAA,CACFqG,iBAAiB,CAACZ,MAAM,CAAG,CAAC,CAC3BY,iBAAiB,CAAC6B,GAAG,CAAEtC,MAAM,eAC3BxG,KAAA,CAAC7C,QAAQ,EAAiB2R,OAAO,MAAAlO,QAAA,eAC/Bd,IAAA,CAACzC,YAAY,EAAAuD,QAAA,cACXd,IAAA,CAAC9C,MAAM,EAAC6P,EAAE,CAAE,CAAEgC,OAAO,CAAEhN,KAAK,CAACuJ,OAAO,CAACI,OAAO,CAACF,IAAK,CAAE,CAAA1K,QAAA,cAClDd,IAAA,CAACzB,MAAM,GAAE,CAAC,CACJ,CAAC,CACG,CAAC,cACfyB,IAAA,CAAC1C,YAAY,EACXiO,OAAO,eAAAhK,MAAA,CAAgBmF,MAAM,CAACuI,QAAQ,CAAG,CACzCxD,SAAS,mBAAAlK,MAAA,CAAoBhC,MAAM,CAACC,QAAQ,CAACkH,MAAM,CAACU,IAAI,CAAC,CAAE,aAAa,CAAC,wBAAA7F,MAAA,CAAiBmF,MAAM,CAACwI,SAAS,CAAG,CAC9G,CAAC,cACFlP,IAAA,CAAC/C,UAAU,EAAC4R,IAAI,CAAC,OAAO,CAAC1C,OAAO,CAAEA,CAAA,GAAMnK,QAAQ,0BAA0B,CAAE,CAAAlB,QAAA,cAC1Ed,IAAA,CAAC5B,YAAY,EAAC+Q,QAAQ,CAAC,OAAO,CAAE,CAAC,CACvB,CAAC,GAZAzI,MAAM,CAACpF,EAaZ,CACX,CAAC,cAEFtB,IAAA,CAAC3C,QAAQ,EAAAyD,QAAA,cACPd,IAAA,CAAC1C,YAAY,EAACiO,OAAO,CAAC,yBAAyB,CAAE,CAAC,CAC1C,CACX,CACG,CACP,CACF,CAAC,CACQ,CAAC,CACT,CAAC,cAEPvL,IAAA,CAACrD,IAAI,EAACmR,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAlN,QAAA,cACvBd,IAAA,CAACtD,MAAM,CAACwQ,GAAG,EAACC,QAAQ,CAAE1M,YAAa,CAAAK,QAAA,cACjCd,IAAA,CAAClB,sBAAsB,EACrBuN,KAAK,CAAC,iBAAiB,CACvBC,QAAQ,CAAC,oDAAoD,CAC7DQ,MAAM,CAAC,UAAU,CACjBmB,QAAQ,CAAC,0BAA0B,CACnCC,MAAM,CAAE,GAAI,CACZpC,IAAI,cAAE9L,IAAA,CAACxB,aAAa,GAAE,CAAE,CACxB2P,WAAW,CAAEpM,KAAK,CAACuJ,OAAO,CAACK,IAAI,CAACH,IAAK,CACrCoD,MAAM,cACJ5O,IAAA,CAACd,YAAY,EACX2P,IAAI,CAAC,OAAO,CACZtB,OAAO,CAAC,UAAU,CAClBpB,OAAO,CAAEA,CAAA,GAAMnK,QAAQ,CAAC,uBAAuB,CAAE,CACjD+K,EAAE,CAAE,CACFhB,KAAK,CAAE,OAAO,CACd+C,WAAW,CAAE,uBAAuB,CACpC,SAAS,CAAE,CAAEA,WAAW,CAAE,OAAO,CAAEC,OAAO,CAAE,uBAAwB,CACtE,CAAE,CAAAjO,QAAA,CACH,UAED,CAAc,CACf,CACDuC,OAAO,cACLrD,IAAA,CAAC5C,IAAI,EAAA0D,QAAA,CACF4G,cAAc,CAACnB,MAAM,CAAG,CAAC,CACxBmB,cAAc,CAACsB,GAAG,CAAEtC,MAAM,eACxBxG,KAAA,CAAC7C,QAAQ,EAAiB2R,OAAO,MAAAlO,QAAA,eAC/Bd,IAAA,CAACzC,YAAY,EAAAuD,QAAA,cACXd,IAAA,CAAC9C,MAAM,EAAC6P,EAAE,CAAE,CAAEgC,OAAO,CAAEhN,KAAK,CAACuJ,OAAO,CAACK,IAAI,CAACH,IAAK,CAAE,CAAA1K,QAAA,cAC/Cd,IAAA,CAAC1B,IAAI,GAAE,CAAC,CACF,CAAC,CACG,CAAC,cACf0B,IAAA,CAAC1C,YAAY,EACXiO,OAAO,eAAAhK,MAAA,CAAgBmF,MAAM,CAAC0I,QAAQ,sBAAA7N,MAAA,CAAemF,MAAM,CAAC2I,MAAM,CAAG,CACrE5D,SAAS,cAAAlK,MAAA,CAAehC,MAAM,CAACC,QAAQ,CAACkH,MAAM,CAACiB,eAAgB,CAAC,CAAE,aAAa,CAAC,qBAAApG,MAAA,CAAcmF,MAAM,CAACiC,IAAI,GAAK,SAAS,CAAG,SAAS,CAAG,YAAY,CAAG,CACtJ,CAAC,cACF3I,IAAA,CAAC/C,UAAU,EAAC4R,IAAI,CAAC,OAAO,CAAC1C,OAAO,CAAEA,CAAA,GAAMnK,QAAQ,wBAAwB,CAAE,CAAAlB,QAAA,cACxEd,IAAA,CAAC5B,YAAY,EAAC+Q,QAAQ,CAAC,OAAO,CAAE,CAAC,CACvB,CAAC,GAZAzI,MAAM,CAACpF,EAaZ,CACX,CAAC,cAEFtB,IAAA,CAAC3C,QAAQ,EAAAyD,QAAA,cACPd,IAAA,CAAC1C,YAAY,EAACiO,OAAO,CAAC,wCAAwC,CAAE,CAAC,CACzD,CACX,CACG,CACP,CACF,CAAC,CACQ,CAAC,CACT,CAAC,EACH,CAAC,EACG,CAAC,cAGfvL,IAAA,CAACtD,MAAM,CAACwQ,GAAG,EACTE,OAAO,CAAE,CAAE/M,OAAO,CAAE,CAAC,CAAEK,CAAC,CAAE,EAAG,CAAE,CAC/B2M,OAAO,CAAE,CAAEhN,OAAO,CAAE,CAAC,CAAEK,CAAC,CAAE,CAAE,CAAE,CAC9BH,UAAU,CAAE,CAAE+O,KAAK,CAAE,GAAI,CAAE,CAAAxO,QAAA,cAE3Bd,IAAA,CAACf,WAAW,EACVoN,KAAK,CAAEvK,oBAAoB,CAAGA,oBAAoB,CAAC,sBAAsB,CAAE,8BAA8B,CAAC,CAAG,8BAA+B,CAC5IwK,QAAQ,CAAExK,oBAAoB,CAAGA,oBAAoB,CAAC,2BAA2B,CAAE,iDAAiD,CAAC,CAAG,iDAAkD,CAC1LiH,IAAI,CAAEzJ,2BAA4B,CAClCqJ,IAAI,CAAC,OAAO,CACZ2F,QAAQ,CAAE,CAAC,GAAG,CAAE,CAChBI,YAAY,CAAC,SAAS,CACtBR,MAAM,CAAE,GAAI,CACZC,WAAW,CAAEpM,KAAK,CAACuJ,OAAO,CAACC,OAAO,CAACC,IAAK,CACxC+C,oBAAoB,CAAE,KAAM,CAC5BzB,MAAM,CAAC,UAAU,CACjB0B,OAAO,CAAE1M,oBAAoB,CAAGA,oBAAoB,CAAC,2BAA2B,CAAE,6DAA6D,CAAC,CAAG,6DAA8D,CACjN2M,WAAW,CAAG1N,KAAK,KAAAQ,MAAA,CAAQR,KAAK,KAAI,CACpC8L,SAAS,CAAEvH,iBAAkB,CAC9B,CAAC,CACQ,CAAC,CAGZpC,YAAY,CAACE,IAAI,eAChBpD,IAAA,CAACtD,MAAM,CAACwQ,GAAG,EACTE,OAAO,CAAE,CAAE/M,OAAO,CAAE,CAAC,CAAEK,CAAC,CAAE,CAAC,EAAG,CAAE,CAChC2M,OAAO,CAAE,CAAEhN,OAAO,CAAE,CAAC,CAAEK,CAAC,CAAE,CAAE,CAAE,CAC9BH,UAAU,CAAE,CAAEI,QAAQ,CAAE,GAAI,CAAE,CAAAG,QAAA,cAE9Bd,IAAA,CAACpD,IAAI,EAACmQ,EAAE,CAAE,CACRwC,YAAY,CAAE,MAAM,CACpBC,SAAS,CAAE,6BAA6B,CACxC7B,EAAE,CAAE,CAAC,CACL8B,QAAQ,CAAE,QAAQ,CAClBC,UAAU,4BAAAnO,MAAA,CAA6BzD,KAAK,CAAC,SAAS,CAAE,IAAI,CAAC,OAAAyD,MAAA,CAAKzD,KAAK,CAAC,SAAS,CAAE,IAAI,CAAC,KAAG,CAC3FiO,KAAK,CAAE,OACT,CAAE,CAAAjL,QAAA,cACAZ,KAAA,CAACrD,WAAW,EAAAiE,QAAA,eACVZ,KAAA,CAACnD,GAAG,EAAC4S,OAAO,CAAC,MAAM,CAACC,cAAc,CAAC,eAAe,CAACC,UAAU,CAAC,QAAQ,CAAA/O,QAAA,eACpEd,IAAA,CAAClD,UAAU,EAACyQ,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,MAAM,CAACzB,KAAK,CAAC,OAAO,CAAAjL,QAAA,CAAC,8BAEzD,CAAY,CAAC,cACbd,IAAA,CAAC/C,UAAU,EAAC4R,IAAI,CAAC,OAAO,CAAC1C,OAAO,CAAEA,CAAA,GAAMhJ,eAAe,CAAC,CAAEC,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,EAAG,CAAC,CAAE,CAAC0J,EAAE,CAAE,CAAEhB,KAAK,CAAE,OAAQ,CAAE,CAAAjL,QAAA,cAC5Gd,IAAA,CAAC7B,QAAQ,GAAE,CAAC,CACF,CAAC,EACV,CAAC,cACN6B,IAAA,CAAClD,UAAU,EAACyQ,OAAO,CAAC,OAAO,CAACG,EAAE,CAAE,CAAE,CAAC3B,KAAK,CAAC,OAAO,CAAAjL,QAAA,CAC7CoC,YAAY,CAACG,OAAO,CACX,CAAC,CACZL,cAAc,eACb9C,KAAA,CAACnD,GAAG,EAAC2Q,EAAE,CAAE,CAAE,CAAA5M,QAAA,eACTZ,KAAA,CAACpD,UAAU,EAACyQ,OAAO,CAAC,OAAO,CAACxB,KAAK,CAAC,uBAAuB,CAAC4B,EAAE,CAAE,CAAE,CAAA7M,QAAA,EAAC,8BACnC,CAACkC,cAAc,CAAC,GAC9C,EAAY,CAAC,cACb9C,KAAA,CAACpD,UAAU,EAACyQ,OAAO,CAAC,OAAO,CAACxB,KAAK,CAAC,OAAO,CAAAjL,QAAA,EACtCkC,cAAc,GAAK,cAAc,EAAI,4DAA4D,CACjGA,cAAc,GAAK,iBAAiB,EAAI,wDAAwD,CAChGA,cAAc,GAAK,cAAc,EAAI,0EAA0E,CAC/GA,cAAc,GAAK,kBAAkB,EAAI,sEAAsE,CAC/GA,cAAc,GAAK,eAAe,EAAI,mDAAmD,CACzFA,cAAc,GAAK,cAAc,EAAI,6DAA6D,EACzF,CAAC,EACV,CACN,EACU,CAAC,CACV,CAAC,CACG,CACb,cAGDhD,IAAA,CAACtD,MAAM,CAACwQ,GAAG,EACTE,OAAO,CAAE,CAAE/M,OAAO,CAAE,CAAC,CAAEK,CAAC,CAAE,EAAG,CAAE,CAC/B2M,OAAO,CAAE,CAAEhN,OAAO,CAAE,CAAC,CAAEK,CAAC,CAAE,CAAE,CAAE,CAC9BH,UAAU,CAAE,CAAE+O,KAAK,CAAE,GAAI,CAAE,CAAAxO,QAAA,cAE3BZ,KAAA,CAACtD,IAAI,EAACmQ,EAAE,CAAE,CACRwC,YAAY,CAAE,MAAM,CACpBC,SAAS,CAAE,6BAA6B,CACxC7B,EAAE,CAAE,CAAC,CACL8B,QAAQ,CAAE,QAAQ,CAClBC,UAAU,4BAAAnO,MAAA,CAA6BzD,KAAK,CAAC,SAAS,CAAE,IAAI,CAAC,OAAAyD,MAAA,CAAKzD,KAAK,CAAC,SAAS,CAAE,IAAI,CAAC,KAC1F,CAAE,CAAAgD,QAAA,eACAZ,KAAA,CAACnD,GAAG,EAACgQ,EAAE,CAAE,CAAE+C,CAAC,CAAE,CAAC,CAAE/D,KAAK,CAAE,OAAO,CAAE4D,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,eAAe,CAAEC,UAAU,CAAE,QAAS,CAAE,CAAA/O,QAAA,eACxGd,IAAA,CAAClD,UAAU,EAACyQ,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,MAAM,CAACzB,KAAK,CAAC,OAAO,CAAAjL,QAAA,CAAC,kBAEzD,CAAY,CAAC,cACbZ,KAAA,CAACnD,GAAG,EAAA+D,QAAA,eACFd,IAAA,CAAC/C,UAAU,EAAC8O,KAAK,CAAC,SAAS,CAAC8C,IAAI,CAAC,OAAO,CAAA/N,QAAA,cACtCd,IAAA,CAAC/B,MAAM,GAAE,CAAC,CACA,CAAC,cACb+B,IAAA,CAAC/C,UAAU,EAAC8O,KAAK,CAAC,SAAS,CAAC8C,IAAI,CAAC,OAAO,CAAA/N,QAAA,cACtCd,IAAA,CAAC9B,UAAU,GAAE,CAAC,CACJ,CAAC,cACb8B,IAAA,CAAC/C,UAAU,EAAC8O,KAAK,CAAC,SAAS,CAAC8C,IAAI,CAAC,OAAO,CAAA/N,QAAA,cACtCd,IAAA,CAAC7B,QAAQ,GAAE,CAAC,CACF,CAAC,EACV,CAAC,EACH,CAAC,cACN6B,IAAA,CAACrC,cAAc,EAAAmD,QAAA,cACbZ,KAAA,CAAC1C,KAAK,EAAAsD,QAAA,eACJd,IAAA,CAACpC,SAAS,EAACmP,EAAE,CAAE,CAAEgC,OAAO,CAAE,uBAAwB,CAAE,CAAAjO,QAAA,cAClDZ,KAAA,CAACrC,QAAQ,EAAAiD,QAAA,eACPd,IAAA,CAACtC,SAAS,EAACqP,EAAE,CAAE,CAAEhB,KAAK,CAAE,OAAO,CAAEyB,UAAU,CAAE,MAAO,CAAE,CAAA1M,QAAA,CAAC,IAAE,CAAW,CAAC,cACrEd,IAAA,CAACtC,SAAS,EAACqP,EAAE,CAAE,CAAEhB,KAAK,CAAE,OAAO,CAAEyB,UAAU,CAAE,MAAO,CAAE,CAAA1M,QAAA,CAAC,QAAM,CAAW,CAAC,cACzEd,IAAA,CAACtC,SAAS,EAACqP,EAAE,CAAE,CAAEhB,KAAK,CAAE,OAAO,CAAEyB,UAAU,CAAE,MAAO,CAAE,CAAA1M,QAAA,CAAC,MAAI,CAAW,CAAC,cACvEd,IAAA,CAACtC,SAAS,EAACqP,EAAE,CAAE,CAAEhB,KAAK,CAAE,OAAO,CAAEyB,UAAU,CAAE,MAAO,CAAE,CAAA1M,QAAA,CAAC,eAAa,CAAW,CAAC,cAChFd,IAAA,CAACtC,SAAS,EAACqP,EAAE,CAAE,CAAEhB,KAAK,CAAE,OAAO,CAAEyB,UAAU,CAAE,MAAO,CAAE,CAAA1M,QAAA,CAAC,mBAAiB,CAAW,CAAC,cACpFd,IAAA,CAACtC,SAAS,EAACqP,EAAE,CAAE,CAAEhB,KAAK,CAAE,OAAO,CAAEyB,UAAU,CAAE,MAAO,CAAE,CAAA1M,QAAA,CAAC,QAAM,CAAW,CAAC,cACzEd,IAAA,CAACtC,SAAS,EAACqP,EAAE,CAAE,CAAEhB,KAAK,CAAE,OAAO,CAAEyB,UAAU,CAAE,MAAO,CAAE,CAAA1M,QAAA,CAAC,QAAM,CAAW,CAAC,cACzEd,IAAA,CAACtC,SAAS,EAACqP,EAAE,CAAE,CAAEhB,KAAK,CAAE,OAAO,CAAEyB,UAAU,CAAE,MAAO,CAAE,CAAA1M,QAAA,CAAC,SAAO,CAAW,CAAC,EAClE,CAAC,CACF,CAAC,cACZd,IAAA,CAACvC,SAAS,EAAAqD,QAAA,CACP3B,mBAAmB,CAAC6J,GAAG,CAAEtC,MAAM,eAC9BxG,KAAA,CAACrC,QAAQ,EAAiBkS,KAAK,MAAChD,EAAE,CAAE,CAAE,SAAS,CAAE,CAAEgC,OAAO,CAAE,uBAAwB,CAAE,CAAE,CAAAjO,QAAA,eACtFd,IAAA,CAACtC,SAAS,EAACqP,EAAE,CAAE,CAAEhB,KAAK,CAAE,OAAQ,CAAE,CAAAjL,QAAA,CAAE4F,MAAM,CAACpF,EAAE,CAAY,CAAC,cAC1DtB,IAAA,CAACtC,SAAS,EAACqP,EAAE,CAAE,CAAEhB,KAAK,CAAE,OAAQ,CAAE,CAAAjL,QAAA,WAAAS,MAAA,CAAYmF,MAAM,CAAC0I,QAAQ,EAAc,CAAC,cAC5EpP,IAAA,CAACtC,SAAS,EAACqP,EAAE,CAAE,CAAEhB,KAAK,CAAE,OAAQ,CAAE,CAAAjL,QAAA,WAAAS,MAAA,CAAYmF,MAAM,CAAC2I,MAAM,EAAc,CAAC,cAC1ErP,IAAA,CAACtC,SAAS,EAACqP,EAAE,CAAE,CAAEhB,KAAK,CAAE,OAAQ,CAAE,CAAAjL,QAAA,CAAE,GAAI,CAAA2D,IAAI,CAACiC,MAAM,CAACU,IAAI,CAAC,CAAC4I,kBAAkB,CAAC,CAAC,CAAY,CAAC,cAC3FhQ,IAAA,CAACtC,SAAS,EAACqP,EAAE,CAAE,CAAEhB,KAAK,CAAE,OAAQ,CAAE,CAAAjL,QAAA,CAAE,GAAI,CAAA2D,IAAI,CAACiC,MAAM,CAACiB,eAAe,CAAC,CAACqI,kBAAkB,CAAC,CAAC,CAAY,CAAC,cACtGhQ,IAAA,CAACtC,SAAS,EAAAoD,QAAA,cACRd,IAAA,CAAChD,IAAI,EACH6O,KAAK,CAAEnF,MAAM,CAACiC,IAAI,GAAK,SAAS,CAAG,SAAS,CAAG,YAAa,CAC5DoE,EAAE,CAAE,CAAEgC,OAAO,CAAE,uBAAuB,CAAEhD,KAAK,CAAE,OAAQ,CAAE,CACzD8C,IAAI,CAAC,OAAO,CACb,CAAC,CACO,CAAC,cACZ7O,IAAA,CAACtC,SAAS,EAAAoD,QAAA,cACRd,IAAA,CAAChD,IAAI,EACH6O,KAAK,CAAEnF,MAAM,CAACC,MAAM,CAACsJ,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGxJ,MAAM,CAACC,MAAM,CAACc,KAAK,CAAC,CAAC,CAAE,CACtEsF,EAAE,CAAE,CACFgC,OAAO,CAAErI,MAAM,CAACC,MAAM,GAAK,SAAS,CAAG,qBAAqB,CACpDD,MAAM,CAACC,MAAM,GAAK,WAAW,CAAG,qBAAqB,CACrD,qBAAqB,CAC7BoF,KAAK,CAAE,OACT,CAAE,CACF8C,IAAI,CAAC,OAAO,CACb,CAAC,CACO,CAAC,cACZ7O,IAAA,CAACtC,SAAS,EAAAoD,QAAA,cACRd,IAAA,CAAC/C,UAAU,EAAC4R,IAAI,CAAC,OAAO,CAAC1C,OAAO,CAAEA,CAAA,GAAMnK,QAAQ,sBAAAT,MAAA,CAAsBmF,MAAM,CAACpF,EAAE,CAAE,CAAE,CAACyL,EAAE,CAAE,CAAEhB,KAAK,CAAE,OAAQ,CAAE,CAAAjL,QAAA,cACzGd,IAAA,CAAC5B,YAAY,EAAC+Q,QAAQ,CAAC,OAAO,CAAE,CAAC,CACvB,CAAC,CACJ,CAAC,GA7BCzI,MAAM,CAACpF,EA8BZ,CACX,CAAC,CACO,CAAC,EACP,CAAC,CACM,CAAC,EACb,CAAC,CACG,CAAC,EACR,CAAC,CACW,CAAC,CAExB,CAAC,CAED;AACA,cAAe,CAAAvC,wBAAwB,CAAC2C,iBAAiB,CAAE,UAAU,CAAE,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}