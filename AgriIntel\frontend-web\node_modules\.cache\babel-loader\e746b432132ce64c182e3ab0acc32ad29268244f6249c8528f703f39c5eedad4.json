{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Transaction = exports.TxnState = void 0;\nexports.isTransactionCommand = isTransactionCommand;\nconst error_1 = require(\"./error\");\nconst read_concern_1 = require(\"./read_concern\");\nconst read_preference_1 = require(\"./read_preference\");\nconst write_concern_1 = require(\"./write_concern\");\n/** @internal */\nexports.TxnState = Object.freeze({\n  NO_TRANSACTION: 'NO_TRANSACTION',\n  STARTING_TRANSACTION: 'STARTING_TRANSACTION',\n  TRANSACTION_IN_PROGRESS: 'TRANSACTION_IN_PROGRESS',\n  TRANSACTION_COMMITTED: 'TRANSACTION_COMMITTED',\n  TRANSACTION_COMMITTED_EMPTY: 'TRANSACTION_COMMITTED_EMPTY',\n  TRANSACTION_ABORTED: 'TRANSACTION_ABORTED'\n});\nconst stateMachine = {\n  [exports.TxnState.NO_TRANSACTION]: [exports.TxnState.NO_TRANSACTION, exports.TxnState.STARTING_TRANSACTION],\n  [exports.TxnState.STARTING_TRANSACTION]: [exports.TxnState.TRANSACTION_IN_PROGRESS, exports.TxnState.TRANSACTION_COMMITTED, exports.TxnState.TRANSACTION_COMMITTED_EMPTY, exports.TxnState.TRANSACTION_ABORTED],\n  [exports.TxnState.TRANSACTION_IN_PROGRESS]: [exports.TxnState.TRANSACTION_IN_PROGRESS, exports.TxnState.TRANSACTION_COMMITTED, exports.TxnState.TRANSACTION_ABORTED],\n  [exports.TxnState.TRANSACTION_COMMITTED]: [exports.TxnState.TRANSACTION_COMMITTED, exports.TxnState.TRANSACTION_COMMITTED_EMPTY, exports.TxnState.STARTING_TRANSACTION, exports.TxnState.NO_TRANSACTION],\n  [exports.TxnState.TRANSACTION_ABORTED]: [exports.TxnState.STARTING_TRANSACTION, exports.TxnState.NO_TRANSACTION],\n  [exports.TxnState.TRANSACTION_COMMITTED_EMPTY]: [exports.TxnState.TRANSACTION_COMMITTED_EMPTY, exports.TxnState.NO_TRANSACTION]\n};\nconst ACTIVE_STATES = new Set([exports.TxnState.STARTING_TRANSACTION, exports.TxnState.TRANSACTION_IN_PROGRESS]);\nconst COMMITTED_STATES = new Set([exports.TxnState.TRANSACTION_COMMITTED, exports.TxnState.TRANSACTION_COMMITTED_EMPTY, exports.TxnState.TRANSACTION_ABORTED]);\n/**\n * @public\n * A class maintaining state related to a server transaction. Internal Only\n */\nclass Transaction {\n  /** Create a transaction @internal */\n  constructor(options) {\n    options = options ?? {};\n    this.state = exports.TxnState.NO_TRANSACTION;\n    this.options = {};\n    const writeConcern = write_concern_1.WriteConcern.fromOptions(options);\n    if (writeConcern) {\n      if (writeConcern.w === 0) {\n        throw new error_1.MongoTransactionError('Transactions do not support unacknowledged write concern');\n      }\n      this.options.writeConcern = writeConcern;\n    }\n    if (options.readConcern) {\n      this.options.readConcern = read_concern_1.ReadConcern.fromOptions(options);\n    }\n    if (options.readPreference) {\n      this.options.readPreference = read_preference_1.ReadPreference.fromOptions(options);\n    }\n    if (options.maxCommitTimeMS) {\n      this.options.maxTimeMS = options.maxCommitTimeMS;\n    }\n    // TODO: This isn't technically necessary\n    this._pinnedServer = undefined;\n    this._recoveryToken = undefined;\n  }\n  /** @internal */\n  get server() {\n    return this._pinnedServer;\n  }\n  get recoveryToken() {\n    return this._recoveryToken;\n  }\n  get isPinned() {\n    return !!this.server;\n  }\n  /** @returns Whether the transaction has started */\n  get isStarting() {\n    return this.state === exports.TxnState.STARTING_TRANSACTION;\n  }\n  /**\n   * @returns Whether this session is presently in a transaction\n   */\n  get isActive() {\n    return ACTIVE_STATES.has(this.state);\n  }\n  get isCommitted() {\n    return COMMITTED_STATES.has(this.state);\n  }\n  /**\n   * Transition the transaction in the state machine\n   * @internal\n   * @param nextState - The new state to transition to\n   */\n  transition(nextState) {\n    const nextStates = stateMachine[this.state];\n    if (nextStates && nextStates.includes(nextState)) {\n      this.state = nextState;\n      if (this.state === exports.TxnState.NO_TRANSACTION || this.state === exports.TxnState.STARTING_TRANSACTION || this.state === exports.TxnState.TRANSACTION_ABORTED) {\n        this.unpinServer();\n      }\n      return;\n    }\n    throw new error_1.MongoRuntimeError(`Attempted illegal state transition from [${this.state}] to [${nextState}]`);\n  }\n  /** @internal */\n  pinServer(server) {\n    if (this.isActive) {\n      this._pinnedServer = server;\n    }\n  }\n  /** @internal */\n  unpinServer() {\n    this._pinnedServer = undefined;\n  }\n}\nexports.Transaction = Transaction;\nfunction isTransactionCommand(command) {\n  return !!(command.commitTransaction || command.abortTransaction);\n}", "map": {"version": 3, "names": ["exports", "isTransactionCommand", "error_1", "require", "read_concern_1", "read_preference_1", "write_concern_1", "TxnState", "Object", "freeze", "NO_TRANSACTION", "STARTING_TRANSACTION", "TRANSACTION_IN_PROGRESS", "TRANSACTION_COMMITTED", "TRANSACTION_COMMITTED_EMPTY", "TRANSACTION_ABORTED", "stateMachine", "ACTIVE_STATES", "Set", "COMMITTED_STATES", "Transaction", "constructor", "options", "state", "writeConcern", "WriteConcern", "fromOptions", "w", "MongoTransactionError", "readConcern", "ReadConcern", "readPreference", "ReadPreference", "maxCommitTimeMS", "maxTimeMS", "_pinnedServer", "undefined", "_recoveryToken", "server", "recoveryToken", "isPinned", "isStarting", "isActive", "has", "isCommitted", "transition", "nextState", "nextStates", "includes", "unpinServer", "MongoRuntimeError", "pinServer", "command", "commitTransaction", "abortTransaction"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\transactions.ts"], "sourcesContent": ["import type { Document } from './bson';\nimport { MongoRuntimeError, MongoTransactionError } from './error';\nimport type { CommandOperationOptions } from './operations/command';\nimport { ReadConcern, type ReadConcernLike } from './read_concern';\nimport { ReadPreference, type ReadPreferenceLike } from './read_preference';\nimport type { Server } from './sdam/server';\nimport { WriteConcern } from './write_concern';\n\n/** @internal */\nexport const TxnState = Object.freeze({\n  NO_TRANSACTION: 'NO_TRANSACTION',\n  STARTING_TRANSACTION: 'STARTING_TRANSACTION',\n  TRANSACTION_IN_PROGRESS: 'TRANSACTION_IN_PROGRESS',\n  TRANSACTION_COMMITTED: 'TRANSACTION_COMMITTED',\n  TRANSACTION_COMMITTED_EMPTY: 'TRANSACTION_COMMITTED_EMPTY',\n  TRANSACTION_ABORTED: 'TRANSACTION_ABORTED'\n} as const);\n\n/** @internal */\nexport type TxnState = (typeof TxnState)[keyof typeof TxnState];\n\nconst stateMachine: { [state in TxnState]: TxnState[] } = {\n  [TxnState.NO_TRANSACTION]: [TxnState.NO_TRANSACTION, TxnState.STARTING_TRANSACTION],\n  [TxnState.STARTING_TRANSACTION]: [\n    TxnState.TRANSACTION_IN_PROGRESS,\n    TxnState.TRANSACTION_COMMITTED,\n    TxnState.TRANSACTION_COMMITTED_EMPTY,\n    TxnState.TRANSACTION_ABORTED\n  ],\n  [TxnState.TRANSACTION_IN_PROGRESS]: [\n    TxnState.TRANSACTION_IN_PROGRESS,\n    TxnState.TRANSACTION_COMMITTED,\n    TxnState.TRANSACTION_ABORTED\n  ],\n  [TxnState.TRANSACTION_COMMITTED]: [\n    TxnState.TRANSACTION_COMMITTED,\n    TxnState.TRANSACTION_COMMITTED_EMPTY,\n    TxnState.STARTING_TRANSACTION,\n    TxnState.NO_TRANSACTION\n  ],\n  [TxnState.TRANSACTION_ABORTED]: [TxnState.STARTING_TRANSACTION, TxnState.NO_TRANSACTION],\n  [TxnState.TRANSACTION_COMMITTED_EMPTY]: [\n    TxnState.TRANSACTION_COMMITTED_EMPTY,\n    TxnState.NO_TRANSACTION\n  ]\n};\n\nconst ACTIVE_STATES: Set<TxnState> = new Set([\n  TxnState.STARTING_TRANSACTION,\n  TxnState.TRANSACTION_IN_PROGRESS\n]);\n\nconst COMMITTED_STATES: Set<TxnState> = new Set([\n  TxnState.TRANSACTION_COMMITTED,\n  TxnState.TRANSACTION_COMMITTED_EMPTY,\n  TxnState.TRANSACTION_ABORTED\n]);\n\n/**\n * Configuration options for a transaction.\n * @public\n */\nexport interface TransactionOptions extends Omit<CommandOperationOptions, 'timeoutMS'> {\n  // TODO(NODE-3344): These options use the proper class forms of these settings, it should accept the basic enum values too\n  /** A default read concern for commands in this transaction */\n  readConcern?: ReadConcernLike;\n  /** A default writeConcern for commands in this transaction */\n  writeConcern?: WriteConcern;\n  /** A default read preference for commands in this transaction */\n  readPreference?: ReadPreferenceLike;\n  /** Specifies the maximum amount of time to allow a commit action on a transaction to run in milliseconds */\n  maxCommitTimeMS?: number;\n}\n\n/**\n * @public\n * A class maintaining state related to a server transaction. Internal Only\n */\nexport class Transaction {\n  /** @internal */\n  state: TxnState;\n  options: TransactionOptions;\n  /** @internal */\n  _pinnedServer?: Server;\n  /** @internal */\n  _recoveryToken?: Document;\n\n  /** Create a transaction @internal */\n  constructor(options?: TransactionOptions) {\n    options = options ?? {};\n    this.state = TxnState.NO_TRANSACTION;\n    this.options = {};\n\n    const writeConcern = WriteConcern.fromOptions(options);\n    if (writeConcern) {\n      if (writeConcern.w === 0) {\n        throw new MongoTransactionError('Transactions do not support unacknowledged write concern');\n      }\n\n      this.options.writeConcern = writeConcern;\n    }\n\n    if (options.readConcern) {\n      this.options.readConcern = ReadConcern.fromOptions(options);\n    }\n\n    if (options.readPreference) {\n      this.options.readPreference = ReadPreference.fromOptions(options);\n    }\n\n    if (options.maxCommitTimeMS) {\n      this.options.maxTimeMS = options.maxCommitTimeMS;\n    }\n\n    // TODO: This isn't technically necessary\n    this._pinnedServer = undefined;\n    this._recoveryToken = undefined;\n  }\n\n  /** @internal */\n  get server(): Server | undefined {\n    return this._pinnedServer;\n  }\n\n  get recoveryToken(): Document | undefined {\n    return this._recoveryToken;\n  }\n\n  get isPinned(): boolean {\n    return !!this.server;\n  }\n\n  /** @returns Whether the transaction has started */\n  get isStarting(): boolean {\n    return this.state === TxnState.STARTING_TRANSACTION;\n  }\n\n  /**\n   * @returns Whether this session is presently in a transaction\n   */\n  get isActive(): boolean {\n    return ACTIVE_STATES.has(this.state);\n  }\n\n  get isCommitted(): boolean {\n    return COMMITTED_STATES.has(this.state);\n  }\n  /**\n   * Transition the transaction in the state machine\n   * @internal\n   * @param nextState - The new state to transition to\n   */\n  transition(nextState: TxnState): void {\n    const nextStates = stateMachine[this.state];\n    if (nextStates && nextStates.includes(nextState)) {\n      this.state = nextState;\n      if (\n        this.state === TxnState.NO_TRANSACTION ||\n        this.state === TxnState.STARTING_TRANSACTION ||\n        this.state === TxnState.TRANSACTION_ABORTED\n      ) {\n        this.unpinServer();\n      }\n      return;\n    }\n\n    throw new MongoRuntimeError(\n      `Attempted illegal state transition from [${this.state}] to [${nextState}]`\n    );\n  }\n\n  /** @internal */\n  pinServer(server: Server): void {\n    if (this.isActive) {\n      this._pinnedServer = server;\n    }\n  }\n\n  /** @internal */\n  unpinServer(): void {\n    this._pinnedServer = undefined;\n  }\n}\n\nexport function isTransactionCommand(command: Document): boolean {\n  return !!(command.commitTransaction || command.abortTransaction);\n}\n"], "mappings": ";;;;;;AAwLAA,OAAA,CAAAC,oBAAA,GAAAA,oBAAA;AAvLA,MAAAC,OAAA,GAAAC,OAAA;AAEA,MAAAC,cAAA,GAAAD,OAAA;AACA,MAAAE,iBAAA,GAAAF,OAAA;AAEA,MAAAG,eAAA,GAAAH,OAAA;AAEA;AACaH,OAAA,CAAAO,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAAC;EACpCC,cAAc,EAAE,gBAAgB;EAChCC,oBAAoB,EAAE,sBAAsB;EAC5CC,uBAAuB,EAAE,yBAAyB;EAClDC,qBAAqB,EAAE,uBAAuB;EAC9CC,2BAA2B,EAAE,6BAA6B;EAC1DC,mBAAmB,EAAE;CACb,CAAC;AAKX,MAAMC,YAAY,GAAwC;EACxD,CAAChB,OAAA,CAAAO,QAAQ,CAACG,cAAc,GAAG,CAACV,OAAA,CAAAO,QAAQ,CAACG,cAAc,EAAEV,OAAA,CAAAO,QAAQ,CAACI,oBAAoB,CAAC;EACnF,CAACX,OAAA,CAAAO,QAAQ,CAACI,oBAAoB,GAAG,CAC/BX,OAAA,CAAAO,QAAQ,CAACK,uBAAuB,EAChCZ,OAAA,CAAAO,QAAQ,CAACM,qBAAqB,EAC9Bb,OAAA,CAAAO,QAAQ,CAACO,2BAA2B,EACpCd,OAAA,CAAAO,QAAQ,CAACQ,mBAAmB,CAC7B;EACD,CAACf,OAAA,CAAAO,QAAQ,CAACK,uBAAuB,GAAG,CAClCZ,OAAA,CAAAO,QAAQ,CAACK,uBAAuB,EAChCZ,OAAA,CAAAO,QAAQ,CAACM,qBAAqB,EAC9Bb,OAAA,CAAAO,QAAQ,CAACQ,mBAAmB,CAC7B;EACD,CAACf,OAAA,CAAAO,QAAQ,CAACM,qBAAqB,GAAG,CAChCb,OAAA,CAAAO,QAAQ,CAACM,qBAAqB,EAC9Bb,OAAA,CAAAO,QAAQ,CAACO,2BAA2B,EACpCd,OAAA,CAAAO,QAAQ,CAACI,oBAAoB,EAC7BX,OAAA,CAAAO,QAAQ,CAACG,cAAc,CACxB;EACD,CAACV,OAAA,CAAAO,QAAQ,CAACQ,mBAAmB,GAAG,CAACf,OAAA,CAAAO,QAAQ,CAACI,oBAAoB,EAAEX,OAAA,CAAAO,QAAQ,CAACG,cAAc,CAAC;EACxF,CAACV,OAAA,CAAAO,QAAQ,CAACO,2BAA2B,GAAG,CACtCd,OAAA,CAAAO,QAAQ,CAACO,2BAA2B,EACpCd,OAAA,CAAAO,QAAQ,CAACG,cAAc;CAE1B;AAED,MAAMO,aAAa,GAAkB,IAAIC,GAAG,CAAC,CAC3ClB,OAAA,CAAAO,QAAQ,CAACI,oBAAoB,EAC7BX,OAAA,CAAAO,QAAQ,CAACK,uBAAuB,CACjC,CAAC;AAEF,MAAMO,gBAAgB,GAAkB,IAAID,GAAG,CAAC,CAC9ClB,OAAA,CAAAO,QAAQ,CAACM,qBAAqB,EAC9Bb,OAAA,CAAAO,QAAQ,CAACO,2BAA2B,EACpCd,OAAA,CAAAO,QAAQ,CAACQ,mBAAmB,CAC7B,CAAC;AAkBF;;;;AAIA,MAAaK,WAAW;EAStB;EACAC,YAAYC,OAA4B;IACtCA,OAAO,GAAGA,OAAO,IAAI,EAAE;IACvB,IAAI,CAACC,KAAK,GAAGvB,OAAA,CAAAO,QAAQ,CAACG,cAAc;IACpC,IAAI,CAACY,OAAO,GAAG,EAAE;IAEjB,MAAME,YAAY,GAAGlB,eAAA,CAAAmB,YAAY,CAACC,WAAW,CAACJ,OAAO,CAAC;IACtD,IAAIE,YAAY,EAAE;MAChB,IAAIA,YAAY,CAACG,CAAC,KAAK,CAAC,EAAE;QACxB,MAAM,IAAIzB,OAAA,CAAA0B,qBAAqB,CAAC,0DAA0D,CAAC;MAC7F;MAEA,IAAI,CAACN,OAAO,CAACE,YAAY,GAAGA,YAAY;IAC1C;IAEA,IAAIF,OAAO,CAACO,WAAW,EAAE;MACvB,IAAI,CAACP,OAAO,CAACO,WAAW,GAAGzB,cAAA,CAAA0B,WAAW,CAACJ,WAAW,CAACJ,OAAO,CAAC;IAC7D;IAEA,IAAIA,OAAO,CAACS,cAAc,EAAE;MAC1B,IAAI,CAACT,OAAO,CAACS,cAAc,GAAG1B,iBAAA,CAAA2B,cAAc,CAACN,WAAW,CAACJ,OAAO,CAAC;IACnE;IAEA,IAAIA,OAAO,CAACW,eAAe,EAAE;MAC3B,IAAI,CAACX,OAAO,CAACY,SAAS,GAAGZ,OAAO,CAACW,eAAe;IAClD;IAEA;IACA,IAAI,CAACE,aAAa,GAAGC,SAAS;IAC9B,IAAI,CAACC,cAAc,GAAGD,SAAS;EACjC;EAEA;EACA,IAAIE,MAAMA,CAAA;IACR,OAAO,IAAI,CAACH,aAAa;EAC3B;EAEA,IAAII,aAAaA,CAAA;IACf,OAAO,IAAI,CAACF,cAAc;EAC5B;EAEA,IAAIG,QAAQA,CAAA;IACV,OAAO,CAAC,CAAC,IAAI,CAACF,MAAM;EACtB;EAEA;EACA,IAAIG,UAAUA,CAAA;IACZ,OAAO,IAAI,CAAClB,KAAK,KAAKvB,OAAA,CAAAO,QAAQ,CAACI,oBAAoB;EACrD;EAEA;;;EAGA,IAAI+B,QAAQA,CAAA;IACV,OAAOzB,aAAa,CAAC0B,GAAG,CAAC,IAAI,CAACpB,KAAK,CAAC;EACtC;EAEA,IAAIqB,WAAWA,CAAA;IACb,OAAOzB,gBAAgB,CAACwB,GAAG,CAAC,IAAI,CAACpB,KAAK,CAAC;EACzC;EACA;;;;;EAKAsB,UAAUA,CAACC,SAAmB;IAC5B,MAAMC,UAAU,GAAG/B,YAAY,CAAC,IAAI,CAACO,KAAK,CAAC;IAC3C,IAAIwB,UAAU,IAAIA,UAAU,CAACC,QAAQ,CAACF,SAAS,CAAC,EAAE;MAChD,IAAI,CAACvB,KAAK,GAAGuB,SAAS;MACtB,IACE,IAAI,CAACvB,KAAK,KAAKvB,OAAA,CAAAO,QAAQ,CAACG,cAAc,IACtC,IAAI,CAACa,KAAK,KAAKvB,OAAA,CAAAO,QAAQ,CAACI,oBAAoB,IAC5C,IAAI,CAACY,KAAK,KAAKvB,OAAA,CAAAO,QAAQ,CAACQ,mBAAmB,EAC3C;QACA,IAAI,CAACkC,WAAW,EAAE;MACpB;MACA;IACF;IAEA,MAAM,IAAI/C,OAAA,CAAAgD,iBAAiB,CACzB,4CAA4C,IAAI,CAAC3B,KAAK,SAASuB,SAAS,GAAG,CAC5E;EACH;EAEA;EACAK,SAASA,CAACb,MAAc;IACtB,IAAI,IAAI,CAACI,QAAQ,EAAE;MACjB,IAAI,CAACP,aAAa,GAAGG,MAAM;IAC7B;EACF;EAEA;EACAW,WAAWA,CAAA;IACT,IAAI,CAACd,aAAa,GAAGC,SAAS;EAChC;;AAvGFpC,OAAA,CAAAoB,WAAA,GAAAA,WAAA;AA0GA,SAAgBnB,oBAAoBA,CAACmD,OAAiB;EACpD,OAAO,CAAC,EAAEA,OAAO,CAACC,iBAAiB,IAAID,OAAO,CAACE,gBAAgB,CAAC;AAClE", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}