{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DEFAULT_OPTIONS = exports.OPTIONS = void 0;\nexports.resolveSRVRecord = resolveSRVRecord;\nexports.parseOptions = parseOptions;\nconst dns = require(\"dns\");\nconst mongodb_connection_string_url_1 = require(\"mongodb-connection-string-url\");\nconst url_1 = require(\"url\");\nconst mongo_credentials_1 = require(\"./cmap/auth/mongo_credentials\");\nconst providers_1 = require(\"./cmap/auth/providers\");\nconst client_metadata_1 = require(\"./cmap/handshake/client_metadata\");\nconst compression_1 = require(\"./cmap/wire_protocol/compression\");\nconst encrypter_1 = require(\"./encrypter\");\nconst error_1 = require(\"./error\");\nconst mongo_client_1 = require(\"./mongo_client\");\nconst mongo_logger_1 = require(\"./mongo_logger\");\nconst read_concern_1 = require(\"./read_concern\");\nconst read_preference_1 = require(\"./read_preference\");\nconst monitor_1 = require(\"./sdam/monitor\");\nconst utils_1 = require(\"./utils\");\nconst write_concern_1 = require(\"./write_concern\");\nconst VALID_TXT_RECORDS = ['authSource', 'replicaSet', 'loadBalanced'];\nconst LB_SINGLE_HOST_ERROR = 'loadBalanced option only supported with a single host in the URI';\nconst LB_REPLICA_SET_ERROR = 'loadBalanced option not supported with a replicaSet option';\nconst LB_DIRECT_CONNECTION_ERROR = 'loadBalanced option not supported when directConnection is provided';\nfunction retryDNSTimeoutFor(api) {\n  return async function dnsReqRetryTimeout(lookupAddress) {\n    try {\n      return await dns.promises[api](lookupAddress);\n    } catch (firstDNSError) {\n      if (firstDNSError.code === dns.TIMEOUT) {\n        return await dns.promises[api](lookupAddress);\n      } else {\n        throw firstDNSError;\n      }\n    }\n  };\n}\nconst resolveSrv = retryDNSTimeoutFor('resolveSrv');\nconst resolveTxt = retryDNSTimeoutFor('resolveTxt');\n/**\n * Lookup a `mongodb+srv` connection string, combine the parts and reparse it as a normal\n * connection string.\n *\n * @param uri - The connection string to parse\n * @param options - Optional user provided connection string options\n */\nasync function resolveSRVRecord(options) {\n  if (typeof options.srvHost !== 'string') {\n    throw new error_1.MongoAPIError('Option \"srvHost\" must not be empty');\n  }\n  // Asynchronously start TXT resolution so that we do not have to wait until\n  // the SRV record is resolved before starting a second DNS query.\n  const lookupAddress = options.srvHost;\n  const txtResolutionPromise = resolveTxt(lookupAddress);\n  txtResolutionPromise.then(undefined, utils_1.squashError); // rejections will be handled later\n  const hostname = `_${options.srvServiceName}._tcp.${lookupAddress}`;\n  // Resolve the SRV record and use the result as the list of hosts to connect to.\n  const addresses = await resolveSrv(hostname);\n  if (addresses.length === 0) {\n    throw new error_1.MongoAPIError('No addresses found at host');\n  }\n  for (const {\n    name\n  } of addresses) {\n    (0, utils_1.checkParentDomainMatch)(name, lookupAddress);\n  }\n  const hostAddresses = addresses.map(r => utils_1.HostAddress.fromString(`${r.name}:${r.port ?? 27017}`));\n  validateLoadBalancedOptions(hostAddresses, options, true);\n  // Use the result of resolving the TXT record and add options from there if they exist.\n  let record;\n  try {\n    record = await txtResolutionPromise;\n  } catch (error) {\n    if (error.code !== 'ENODATA' && error.code !== 'ENOTFOUND') {\n      throw error;\n    }\n    return hostAddresses;\n  }\n  if (record.length > 1) {\n    throw new error_1.MongoParseError('Multiple text records not allowed');\n  }\n  const txtRecordOptions = new url_1.URLSearchParams(record[0].join(''));\n  const txtRecordOptionKeys = [...txtRecordOptions.keys()];\n  if (txtRecordOptionKeys.some(key => !VALID_TXT_RECORDS.includes(key))) {\n    throw new error_1.MongoParseError(`Text record may only set any of: ${VALID_TXT_RECORDS.join(', ')}`);\n  }\n  if (VALID_TXT_RECORDS.some(option => txtRecordOptions.get(option) === '')) {\n    throw new error_1.MongoParseError('Cannot have empty URI params in DNS TXT Record');\n  }\n  const source = txtRecordOptions.get('authSource') ?? undefined;\n  const replicaSet = txtRecordOptions.get('replicaSet') ?? undefined;\n  const loadBalanced = txtRecordOptions.get('loadBalanced') ?? undefined;\n  if (!options.userSpecifiedAuthSource && source && options.credentials && !providers_1.AUTH_MECHS_AUTH_SRC_EXTERNAL.has(options.credentials.mechanism)) {\n    options.credentials = mongo_credentials_1.MongoCredentials.merge(options.credentials, {\n      source\n    });\n  }\n  if (!options.userSpecifiedReplicaSet && replicaSet) {\n    options.replicaSet = replicaSet;\n  }\n  if (loadBalanced === 'true') {\n    options.loadBalanced = true;\n  }\n  if (options.replicaSet && options.srvMaxHosts > 0) {\n    throw new error_1.MongoParseError('Cannot combine replicaSet option with srvMaxHosts');\n  }\n  validateLoadBalancedOptions(hostAddresses, options, true);\n  return hostAddresses;\n}\n/**\n * Checks if TLS options are valid\n *\n * @param allOptions - All options provided by user or included in default options map\n * @throws MongoAPIError if TLS options are invalid\n */\nfunction checkTLSOptions(allOptions) {\n  if (!allOptions) return;\n  const check = (a, b) => {\n    if (allOptions.has(a) && allOptions.has(b)) {\n      throw new error_1.MongoAPIError(`The '${a}' option cannot be used with the '${b}' option`);\n    }\n  };\n  check('tlsInsecure', 'tlsAllowInvalidCertificates');\n  check('tlsInsecure', 'tlsAllowInvalidHostnames');\n  check('tlsInsecure', 'tlsDisableCertificateRevocationCheck');\n  check('tlsInsecure', 'tlsDisableOCSPEndpointCheck');\n  check('tlsAllowInvalidCertificates', 'tlsDisableCertificateRevocationCheck');\n  check('tlsAllowInvalidCertificates', 'tlsDisableOCSPEndpointCheck');\n  check('tlsDisableCertificateRevocationCheck', 'tlsDisableOCSPEndpointCheck');\n}\nfunction getBoolean(name, value) {\n  if (typeof value === 'boolean') return value;\n  switch (value) {\n    case 'true':\n      return true;\n    case 'false':\n      return false;\n    default:\n      throw new error_1.MongoParseError(`${name} must be either \"true\" or \"false\"`);\n  }\n}\nfunction getIntFromOptions(name, value) {\n  const parsedInt = (0, utils_1.parseInteger)(value);\n  if (parsedInt != null) {\n    return parsedInt;\n  }\n  throw new error_1.MongoParseError(`Expected ${name} to be stringified int value, got: ${value}`);\n}\nfunction getUIntFromOptions(name, value) {\n  const parsedValue = getIntFromOptions(name, value);\n  if (parsedValue < 0) {\n    throw new error_1.MongoParseError(`${name} can only be a positive int value, got: ${value}`);\n  }\n  return parsedValue;\n}\nfunction* entriesFromString(value) {\n  if (value === '') {\n    return;\n  }\n  const keyValuePairs = value.split(',');\n  for (const keyValue of keyValuePairs) {\n    const [key, value] = keyValue.split(/:(.*)/);\n    if (value == null) {\n      throw new error_1.MongoParseError('Cannot have undefined values in key value pairs');\n    }\n    yield [key, value];\n  }\n}\nclass CaseInsensitiveMap extends Map {\n  constructor(entries = []) {\n    super(entries.map(([k, v]) => [k.toLowerCase(), v]));\n  }\n  has(k) {\n    return super.has(k.toLowerCase());\n  }\n  get(k) {\n    return super.get(k.toLowerCase());\n  }\n  set(k, v) {\n    return super.set(k.toLowerCase(), v);\n  }\n  delete(k) {\n    return super.delete(k.toLowerCase());\n  }\n}\nfunction parseOptions(uri, mongoClient = undefined, options = {}) {\n  if (mongoClient != null && !(mongoClient instanceof mongo_client_1.MongoClient)) {\n    options = mongoClient;\n    mongoClient = undefined;\n  }\n  // validate BSONOptions\n  if (options.useBigInt64 && typeof options.promoteLongs === 'boolean' && !options.promoteLongs) {\n    throw new error_1.MongoAPIError('Must request either bigint or Long for int64 deserialization');\n  }\n  if (options.useBigInt64 && typeof options.promoteValues === 'boolean' && !options.promoteValues) {\n    throw new error_1.MongoAPIError('Must request either bigint or Long for int64 deserialization');\n  }\n  const url = new mongodb_connection_string_url_1.default(uri);\n  const {\n    hosts,\n    isSRV\n  } = url;\n  const mongoOptions = Object.create(null);\n  mongoOptions.hosts = isSRV ? [] : hosts.map(utils_1.HostAddress.fromString);\n  const urlOptions = new CaseInsensitiveMap();\n  if (url.pathname !== '/' && url.pathname !== '') {\n    const dbName = decodeURIComponent(url.pathname[0] === '/' ? url.pathname.slice(1) : url.pathname);\n    if (dbName) {\n      urlOptions.set('dbName', [dbName]);\n    }\n  }\n  if (url.username !== '') {\n    const auth = {\n      username: decodeURIComponent(url.username)\n    };\n    if (typeof url.password === 'string') {\n      auth.password = decodeURIComponent(url.password);\n    }\n    urlOptions.set('auth', [auth]);\n  }\n  for (const key of url.searchParams.keys()) {\n    const values = url.searchParams.getAll(key);\n    const isReadPreferenceTags = /readPreferenceTags/i.test(key);\n    if (!isReadPreferenceTags && values.length > 1) {\n      throw new error_1.MongoInvalidArgumentError(`URI option \"${key}\" cannot appear more than once in the connection string`);\n    }\n    if (!isReadPreferenceTags && values.includes('')) {\n      throw new error_1.MongoAPIError(`URI option \"${key}\" cannot be specified with no value`);\n    }\n    if (!urlOptions.has(key)) {\n      urlOptions.set(key, values);\n    }\n  }\n  const objectOptions = new CaseInsensitiveMap(Object.entries(options).filter(([, v]) => v != null));\n  // Validate options that can only be provided by one of uri or object\n  if (urlOptions.has('serverApi')) {\n    throw new error_1.MongoParseError('URI cannot contain `serverApi`, it can only be passed to the client');\n  }\n  const uriMechanismProperties = urlOptions.get('authMechanismProperties');\n  if (uriMechanismProperties) {\n    for (const property of uriMechanismProperties) {\n      if (/(^|,)ALLOWED_HOSTS:/.test(property)) {\n        throw new error_1.MongoParseError('Auth mechanism property ALLOWED_HOSTS is not allowed in the connection string.');\n      }\n    }\n  }\n  if (objectOptions.has('loadBalanced')) {\n    throw new error_1.MongoParseError('loadBalanced is only a valid option in the URI');\n  }\n  // All option collection\n  const allProvidedOptions = new CaseInsensitiveMap();\n  const allProvidedKeys = new Set([...urlOptions.keys(), ...objectOptions.keys()]);\n  for (const key of allProvidedKeys) {\n    const values = [];\n    const objectOptionValue = objectOptions.get(key);\n    if (objectOptionValue != null) {\n      values.push(objectOptionValue);\n    }\n    const urlValues = urlOptions.get(key) ?? [];\n    values.push(...urlValues);\n    allProvidedOptions.set(key, values);\n  }\n  if (allProvidedOptions.has('tls') || allProvidedOptions.has('ssl')) {\n    const tlsAndSslOpts = (allProvidedOptions.get('tls') || []).concat(allProvidedOptions.get('ssl') || []).map(getBoolean.bind(null, 'tls/ssl'));\n    if (new Set(tlsAndSslOpts).size !== 1) {\n      throw new error_1.MongoParseError('All values of tls/ssl must be the same.');\n    }\n  }\n  checkTLSOptions(allProvidedOptions);\n  const unsupportedOptions = (0, utils_1.setDifference)(allProvidedKeys, Array.from(Object.keys(exports.OPTIONS)).map(s => s.toLowerCase()));\n  if (unsupportedOptions.size !== 0) {\n    const optionWord = unsupportedOptions.size > 1 ? 'options' : 'option';\n    const isOrAre = unsupportedOptions.size > 1 ? 'are' : 'is';\n    throw new error_1.MongoParseError(`${optionWord} ${Array.from(unsupportedOptions).join(', ')} ${isOrAre} not supported`);\n  }\n  // Option parsing and setting\n  for (const [key, descriptor] of Object.entries(exports.OPTIONS)) {\n    const values = allProvidedOptions.get(key);\n    if (!values || values.length === 0) {\n      if (exports.DEFAULT_OPTIONS.has(key)) {\n        setOption(mongoOptions, key, descriptor, [exports.DEFAULT_OPTIONS.get(key)]);\n      }\n    } else {\n      const {\n        deprecated\n      } = descriptor;\n      if (deprecated) {\n        const deprecatedMsg = typeof deprecated === 'string' ? `: ${deprecated}` : '';\n        (0, utils_1.emitWarning)(`${key} is a deprecated option${deprecatedMsg}`);\n      }\n      setOption(mongoOptions, key, descriptor, values);\n    }\n  }\n  if (mongoOptions.credentials) {\n    const isGssapi = mongoOptions.credentials.mechanism === providers_1.AuthMechanism.MONGODB_GSSAPI;\n    const isX509 = mongoOptions.credentials.mechanism === providers_1.AuthMechanism.MONGODB_X509;\n    const isAws = mongoOptions.credentials.mechanism === providers_1.AuthMechanism.MONGODB_AWS;\n    const isOidc = mongoOptions.credentials.mechanism === providers_1.AuthMechanism.MONGODB_OIDC;\n    if ((isGssapi || isX509) && allProvidedOptions.has('authSource') && mongoOptions.credentials.source !== '$external') {\n      // If authSource was explicitly given and its incorrect, we error\n      throw new error_1.MongoParseError(`authMechanism ${mongoOptions.credentials.mechanism} requires an authSource of '$external'`);\n    }\n    if (!(isGssapi || isX509 || isAws || isOidc) && mongoOptions.dbName && !allProvidedOptions.has('authSource')) {\n      // inherit the dbName unless GSSAPI or X509, then silently ignore dbName\n      // and there was no specific authSource given\n      mongoOptions.credentials = mongo_credentials_1.MongoCredentials.merge(mongoOptions.credentials, {\n        source: mongoOptions.dbName\n      });\n    }\n    if (isAws && mongoOptions.credentials.username && !mongoOptions.credentials.password) {\n      throw new error_1.MongoMissingCredentialsError(`When using ${mongoOptions.credentials.mechanism} password must be set when a username is specified`);\n    }\n    mongoOptions.credentials.validate();\n    // Check if the only auth related option provided was authSource, if so we can remove credentials\n    if (mongoOptions.credentials.password === '' && mongoOptions.credentials.username === '' && mongoOptions.credentials.mechanism === providers_1.AuthMechanism.MONGODB_DEFAULT && Object.keys(mongoOptions.credentials.mechanismProperties).length === 0) {\n      delete mongoOptions.credentials;\n    }\n  }\n  if (!mongoOptions.dbName) {\n    // dbName default is applied here because of the credential validation above\n    mongoOptions.dbName = 'test';\n  }\n  validateLoadBalancedOptions(hosts, mongoOptions, isSRV);\n  if (mongoClient && mongoOptions.autoEncryption) {\n    encrypter_1.Encrypter.checkForMongoCrypt();\n    mongoOptions.encrypter = new encrypter_1.Encrypter(mongoClient, uri, options);\n    mongoOptions.autoEncrypter = mongoOptions.encrypter.autoEncrypter;\n  }\n  // Potential SRV Overrides and SRV connection string validations\n  mongoOptions.userSpecifiedAuthSource = objectOptions.has('authSource') || urlOptions.has('authSource');\n  mongoOptions.userSpecifiedReplicaSet = objectOptions.has('replicaSet') || urlOptions.has('replicaSet');\n  if (isSRV) {\n    // SRV Record is resolved upon connecting\n    mongoOptions.srvHost = hosts[0];\n    if (mongoOptions.directConnection) {\n      throw new error_1.MongoAPIError('SRV URI does not support directConnection');\n    }\n    if (mongoOptions.srvMaxHosts > 0 && typeof mongoOptions.replicaSet === 'string') {\n      throw new error_1.MongoParseError('Cannot use srvMaxHosts option with replicaSet');\n    }\n    // SRV turns on TLS by default, but users can override and turn it off\n    const noUserSpecifiedTLS = !objectOptions.has('tls') && !urlOptions.has('tls');\n    const noUserSpecifiedSSL = !objectOptions.has('ssl') && !urlOptions.has('ssl');\n    if (noUserSpecifiedTLS && noUserSpecifiedSSL) {\n      mongoOptions.tls = true;\n    }\n  } else {\n    const userSpecifiedSrvOptions = urlOptions.has('srvMaxHosts') || objectOptions.has('srvMaxHosts') || urlOptions.has('srvServiceName') || objectOptions.has('srvServiceName');\n    if (userSpecifiedSrvOptions) {\n      throw new error_1.MongoParseError('Cannot use srvMaxHosts or srvServiceName with a non-srv connection string');\n    }\n  }\n  if (mongoOptions.directConnection && mongoOptions.hosts.length !== 1) {\n    throw new error_1.MongoParseError('directConnection option requires exactly one host');\n  }\n  if (!mongoOptions.proxyHost && (mongoOptions.proxyPort || mongoOptions.proxyUsername || mongoOptions.proxyPassword)) {\n    throw new error_1.MongoParseError('Must specify proxyHost if other proxy options are passed');\n  }\n  if (mongoOptions.proxyUsername && !mongoOptions.proxyPassword || !mongoOptions.proxyUsername && mongoOptions.proxyPassword) {\n    throw new error_1.MongoParseError('Can only specify both of proxy username/password or neither');\n  }\n  const proxyOptions = ['proxyHost', 'proxyPort', 'proxyUsername', 'proxyPassword'].map(key => urlOptions.get(key) ?? []);\n  if (proxyOptions.some(options => options.length > 1)) {\n    throw new error_1.MongoParseError('Proxy options cannot be specified multiple times in the connection string');\n  }\n  mongoOptions.mongoLoggerOptions = mongo_logger_1.MongoLogger.resolveOptions({\n    MONGODB_LOG_COMMAND: process.env.MONGODB_LOG_COMMAND,\n    MONGODB_LOG_TOPOLOGY: process.env.MONGODB_LOG_TOPOLOGY,\n    MONGODB_LOG_SERVER_SELECTION: process.env.MONGODB_LOG_SERVER_SELECTION,\n    MONGODB_LOG_CONNECTION: process.env.MONGODB_LOG_CONNECTION,\n    MONGODB_LOG_CLIENT: process.env.MONGODB_LOG_CLIENT,\n    MONGODB_LOG_ALL: process.env.MONGODB_LOG_ALL,\n    MONGODB_LOG_MAX_DOCUMENT_LENGTH: process.env.MONGODB_LOG_MAX_DOCUMENT_LENGTH,\n    MONGODB_LOG_PATH: process.env.MONGODB_LOG_PATH\n  }, {\n    mongodbLogPath: mongoOptions.mongodbLogPath,\n    mongodbLogComponentSeverities: mongoOptions.mongodbLogComponentSeverities,\n    mongodbLogMaxDocumentLength: mongoOptions.mongodbLogMaxDocumentLength\n  });\n  mongoOptions.metadata = (0, client_metadata_1.makeClientMetadata)(mongoOptions);\n  mongoOptions.extendedMetadata = (0, client_metadata_1.addContainerMetadata)(mongoOptions.metadata).then(undefined, utils_1.squashError); // rejections will be handled later\n  return mongoOptions;\n}\n/**\n * #### Throws if LB mode is true:\n * - hosts contains more than one host\n * - there is a replicaSet name set\n * - directConnection is set\n * - if srvMaxHosts is used when an srv connection string is passed in\n *\n * @throws MongoParseError\n */\nfunction validateLoadBalancedOptions(hosts, mongoOptions, isSrv) {\n  if (mongoOptions.loadBalanced) {\n    if (hosts.length > 1) {\n      throw new error_1.MongoParseError(LB_SINGLE_HOST_ERROR);\n    }\n    if (mongoOptions.replicaSet) {\n      throw new error_1.MongoParseError(LB_REPLICA_SET_ERROR);\n    }\n    if (mongoOptions.directConnection) {\n      throw new error_1.MongoParseError(LB_DIRECT_CONNECTION_ERROR);\n    }\n    if (isSrv && mongoOptions.srvMaxHosts > 0) {\n      throw new error_1.MongoParseError('Cannot limit srv hosts with loadBalanced enabled');\n    }\n  }\n  return;\n}\nfunction setOption(mongoOptions, key, descriptor, values) {\n  const {\n    target,\n    type,\n    transform\n  } = descriptor;\n  const name = target ?? key;\n  switch (type) {\n    case 'boolean':\n      mongoOptions[name] = getBoolean(name, values[0]);\n      break;\n    case 'int':\n      mongoOptions[name] = getIntFromOptions(name, values[0]);\n      break;\n    case 'uint':\n      mongoOptions[name] = getUIntFromOptions(name, values[0]);\n      break;\n    case 'string':\n      if (values[0] == null) {\n        break;\n      }\n      mongoOptions[name] = String(values[0]);\n      break;\n    case 'record':\n      if (!(0, utils_1.isRecord)(values[0])) {\n        throw new error_1.MongoParseError(`${name} must be an object`);\n      }\n      mongoOptions[name] = values[0];\n      break;\n    case 'any':\n      mongoOptions[name] = values[0];\n      break;\n    default:\n      {\n        if (!transform) {\n          throw new error_1.MongoParseError('Descriptors missing a type must define a transform');\n        }\n        const transformValue = transform({\n          name,\n          options: mongoOptions,\n          values\n        });\n        mongoOptions[name] = transformValue;\n        break;\n      }\n  }\n}\nexports.OPTIONS = {\n  appName: {\n    type: 'string'\n  },\n  auth: {\n    target: 'credentials',\n    transform({\n      name,\n      options,\n      values: [value]\n    }) {\n      if (!(0, utils_1.isRecord)(value, ['username', 'password'])) {\n        throw new error_1.MongoParseError(`${name} must be an object with 'username' and 'password' properties`);\n      }\n      return mongo_credentials_1.MongoCredentials.merge(options.credentials, {\n        username: value.username,\n        password: value.password\n      });\n    }\n  },\n  authMechanism: {\n    target: 'credentials',\n    transform({\n      options,\n      values: [value]\n    }) {\n      const mechanisms = Object.values(providers_1.AuthMechanism);\n      const [mechanism] = mechanisms.filter(m => m.match(RegExp(String.raw`\\b${value}\\b`, 'i')));\n      if (!mechanism) {\n        throw new error_1.MongoParseError(`authMechanism one of ${mechanisms}, got ${value}`);\n      }\n      let source = options.credentials?.source;\n      if (mechanism === providers_1.AuthMechanism.MONGODB_PLAIN || providers_1.AUTH_MECHS_AUTH_SRC_EXTERNAL.has(mechanism)) {\n        // some mechanisms have '$external' as the Auth Source\n        source = '$external';\n      }\n      let password = options.credentials?.password;\n      if (mechanism === providers_1.AuthMechanism.MONGODB_X509 && password === '') {\n        password = undefined;\n      }\n      return mongo_credentials_1.MongoCredentials.merge(options.credentials, {\n        mechanism,\n        source,\n        password\n      });\n    }\n  },\n  // Note that if the authMechanismProperties contain a TOKEN_RESOURCE that has a\n  // comma in it, it MUST be supplied as a MongoClient option instead of in the\n  // connection string.\n  authMechanismProperties: {\n    target: 'credentials',\n    transform({\n      options,\n      values\n    }) {\n      // We can have a combination of options passed in the URI and options passed\n      // as an object to the MongoClient. So we must transform the string options\n      // as well as merge them together with a potentially provided object.\n      let mechanismProperties = Object.create(null);\n      for (const optionValue of values) {\n        if (typeof optionValue === 'string') {\n          for (const [key, value] of entriesFromString(optionValue)) {\n            try {\n              mechanismProperties[key] = getBoolean(key, value);\n            } catch {\n              mechanismProperties[key] = value;\n            }\n          }\n        } else {\n          if (!(0, utils_1.isRecord)(optionValue)) {\n            throw new error_1.MongoParseError('AuthMechanismProperties must be an object');\n          }\n          mechanismProperties = {\n            ...optionValue\n          };\n        }\n      }\n      return mongo_credentials_1.MongoCredentials.merge(options.credentials, {\n        mechanismProperties\n      });\n    }\n  },\n  authSource: {\n    target: 'credentials',\n    transform({\n      options,\n      values: [value]\n    }) {\n      const source = String(value);\n      return mongo_credentials_1.MongoCredentials.merge(options.credentials, {\n        source\n      });\n    }\n  },\n  autoEncryption: {\n    type: 'record'\n  },\n  autoSelectFamily: {\n    type: 'boolean',\n    default: true\n  },\n  autoSelectFamilyAttemptTimeout: {\n    type: 'uint'\n  },\n  bsonRegExp: {\n    type: 'boolean'\n  },\n  serverApi: {\n    target: 'serverApi',\n    transform({\n      values: [version]\n    }) {\n      const serverApiToValidate = typeof version === 'string' ? {\n        version\n      } : version;\n      const versionToValidate = serverApiToValidate && serverApiToValidate.version;\n      if (!versionToValidate) {\n        throw new error_1.MongoParseError(`Invalid \\`serverApi\\` property; must specify a version from the following enum: [\"${Object.values(mongo_client_1.ServerApiVersion).join('\", \"')}\"]`);\n      }\n      if (!Object.values(mongo_client_1.ServerApiVersion).some(v => v === versionToValidate)) {\n        throw new error_1.MongoParseError(`Invalid server API version=${versionToValidate}; must be in the following enum: [\"${Object.values(mongo_client_1.ServerApiVersion).join('\", \"')}\"]`);\n      }\n      return serverApiToValidate;\n    }\n  },\n  checkKeys: {\n    type: 'boolean'\n  },\n  compressors: {\n    default: 'none',\n    target: 'compressors',\n    transform({\n      values\n    }) {\n      const compressionList = new Set();\n      for (const compVal of values) {\n        const compValArray = typeof compVal === 'string' ? compVal.split(',') : compVal;\n        if (!Array.isArray(compValArray)) {\n          throw new error_1.MongoInvalidArgumentError('compressors must be an array or a comma-delimited list of strings');\n        }\n        for (const c of compValArray) {\n          if (Object.keys(compression_1.Compressor).includes(String(c))) {\n            compressionList.add(String(c));\n          } else {\n            throw new error_1.MongoInvalidArgumentError(`${c} is not a valid compression mechanism. Must be one of: ${Object.keys(compression_1.Compressor)}.`);\n          }\n        }\n      }\n      return [...compressionList];\n    }\n  },\n  connectTimeoutMS: {\n    default: 30000,\n    type: 'uint'\n  },\n  dbName: {\n    type: 'string'\n  },\n  directConnection: {\n    default: false,\n    type: 'boolean'\n  },\n  driverInfo: {\n    default: {},\n    type: 'record'\n  },\n  enableUtf8Validation: {\n    type: 'boolean',\n    default: true\n  },\n  family: {\n    transform({\n      name,\n      values: [value]\n    }) {\n      const transformValue = getIntFromOptions(name, value);\n      if (transformValue === 4 || transformValue === 6) {\n        return transformValue;\n      }\n      throw new error_1.MongoParseError(`Option 'family' must be 4 or 6 got ${transformValue}.`);\n    }\n  },\n  fieldsAsRaw: {\n    type: 'record'\n  },\n  forceServerObjectId: {\n    default: false,\n    type: 'boolean'\n  },\n  fsync: {\n    deprecated: 'Please use journal instead',\n    target: 'writeConcern',\n    transform({\n      name,\n      options,\n      values: [value]\n    }) {\n      const wc = write_concern_1.WriteConcern.fromOptions({\n        writeConcern: {\n          ...options.writeConcern,\n          fsync: getBoolean(name, value)\n        }\n      });\n      if (!wc) throw new error_1.MongoParseError(`Unable to make a writeConcern from fsync=${value}`);\n      return wc;\n    }\n  },\n  heartbeatFrequencyMS: {\n    default: 10000,\n    type: 'uint'\n  },\n  ignoreUndefined: {\n    type: 'boolean'\n  },\n  j: {\n    deprecated: 'Please use journal instead',\n    target: 'writeConcern',\n    transform({\n      name,\n      options,\n      values: [value]\n    }) {\n      const wc = write_concern_1.WriteConcern.fromOptions({\n        writeConcern: {\n          ...options.writeConcern,\n          journal: getBoolean(name, value)\n        }\n      });\n      if (!wc) throw new error_1.MongoParseError(`Unable to make a writeConcern from journal=${value}`);\n      return wc;\n    }\n  },\n  journal: {\n    target: 'writeConcern',\n    transform({\n      name,\n      options,\n      values: [value]\n    }) {\n      const wc = write_concern_1.WriteConcern.fromOptions({\n        writeConcern: {\n          ...options.writeConcern,\n          journal: getBoolean(name, value)\n        }\n      });\n      if (!wc) throw new error_1.MongoParseError(`Unable to make a writeConcern from journal=${value}`);\n      return wc;\n    }\n  },\n  loadBalanced: {\n    default: false,\n    type: 'boolean'\n  },\n  localThresholdMS: {\n    default: 15,\n    type: 'uint'\n  },\n  maxConnecting: {\n    default: 2,\n    transform({\n      name,\n      values: [value]\n    }) {\n      const maxConnecting = getUIntFromOptions(name, value);\n      if (maxConnecting === 0) {\n        throw new error_1.MongoInvalidArgumentError('maxConnecting must be > 0 if specified');\n      }\n      return maxConnecting;\n    }\n  },\n  maxIdleTimeMS: {\n    default: 0,\n    type: 'uint'\n  },\n  maxPoolSize: {\n    default: 100,\n    type: 'uint'\n  },\n  maxStalenessSeconds: {\n    target: 'readPreference',\n    transform({\n      name,\n      options,\n      values: [value]\n    }) {\n      const maxStalenessSeconds = getUIntFromOptions(name, value);\n      if (options.readPreference) {\n        return read_preference_1.ReadPreference.fromOptions({\n          readPreference: {\n            ...options.readPreference,\n            maxStalenessSeconds\n          }\n        });\n      } else {\n        return new read_preference_1.ReadPreference('secondary', undefined, {\n          maxStalenessSeconds\n        });\n      }\n    }\n  },\n  minInternalBufferSize: {\n    type: 'uint'\n  },\n  minPoolSize: {\n    default: 0,\n    type: 'uint'\n  },\n  minHeartbeatFrequencyMS: {\n    default: 500,\n    type: 'uint'\n  },\n  monitorCommands: {\n    default: false,\n    type: 'boolean'\n  },\n  name: {\n    target: 'driverInfo',\n    transform({\n      values: [value],\n      options\n    }) {\n      return {\n        ...options.driverInfo,\n        name: String(value)\n      };\n    }\n  },\n  noDelay: {\n    default: true,\n    type: 'boolean'\n  },\n  pkFactory: {\n    default: utils_1.DEFAULT_PK_FACTORY,\n    transform({\n      values: [value]\n    }) {\n      if ((0, utils_1.isRecord)(value, ['createPk']) && typeof value.createPk === 'function') {\n        return value;\n      }\n      throw new error_1.MongoParseError(`Option pkFactory must be an object with a createPk function, got ${value}`);\n    }\n  },\n  promoteBuffers: {\n    type: 'boolean'\n  },\n  promoteLongs: {\n    type: 'boolean'\n  },\n  promoteValues: {\n    type: 'boolean'\n  },\n  useBigInt64: {\n    type: 'boolean'\n  },\n  proxyHost: {\n    type: 'string'\n  },\n  proxyPassword: {\n    type: 'string'\n  },\n  proxyPort: {\n    type: 'uint'\n  },\n  proxyUsername: {\n    type: 'string'\n  },\n  raw: {\n    default: false,\n    type: 'boolean'\n  },\n  readConcern: {\n    transform({\n      values: [value],\n      options\n    }) {\n      if (value instanceof read_concern_1.ReadConcern || (0, utils_1.isRecord)(value, ['level'])) {\n        return read_concern_1.ReadConcern.fromOptions({\n          ...options.readConcern,\n          ...value\n        });\n      }\n      throw new error_1.MongoParseError(`ReadConcern must be an object, got ${JSON.stringify(value)}`);\n    }\n  },\n  readConcernLevel: {\n    target: 'readConcern',\n    transform({\n      values: [level],\n      options\n    }) {\n      return read_concern_1.ReadConcern.fromOptions({\n        ...options.readConcern,\n        level: level\n      });\n    }\n  },\n  readPreference: {\n    default: read_preference_1.ReadPreference.primary,\n    transform({\n      values: [value],\n      options\n    }) {\n      if (value instanceof read_preference_1.ReadPreference) {\n        return read_preference_1.ReadPreference.fromOptions({\n          readPreference: {\n            ...options.readPreference,\n            ...value\n          },\n          ...value\n        });\n      }\n      if ((0, utils_1.isRecord)(value, ['mode'])) {\n        const rp = read_preference_1.ReadPreference.fromOptions({\n          readPreference: {\n            ...options.readPreference,\n            ...value\n          },\n          ...value\n        });\n        if (rp) return rp;else throw new error_1.MongoParseError(`Cannot make read preference from ${JSON.stringify(value)}`);\n      }\n      if (typeof value === 'string') {\n        const rpOpts = {\n          hedge: options.readPreference?.hedge,\n          maxStalenessSeconds: options.readPreference?.maxStalenessSeconds\n        };\n        return new read_preference_1.ReadPreference(value, options.readPreference?.tags, rpOpts);\n      }\n      throw new error_1.MongoParseError(`Unknown ReadPreference value: ${value}`);\n    }\n  },\n  readPreferenceTags: {\n    target: 'readPreference',\n    transform({\n      values,\n      options\n    }) {\n      const tags = Array.isArray(values[0]) ? values[0] : values;\n      const readPreferenceTags = [];\n      for (const tag of tags) {\n        const readPreferenceTag = Object.create(null);\n        if (typeof tag === 'string') {\n          for (const [k, v] of entriesFromString(tag)) {\n            readPreferenceTag[k] = v;\n          }\n        }\n        if ((0, utils_1.isRecord)(tag)) {\n          for (const [k, v] of Object.entries(tag)) {\n            readPreferenceTag[k] = v;\n          }\n        }\n        readPreferenceTags.push(readPreferenceTag);\n      }\n      return read_preference_1.ReadPreference.fromOptions({\n        readPreference: options.readPreference,\n        readPreferenceTags\n      });\n    }\n  },\n  replicaSet: {\n    type: 'string'\n  },\n  retryReads: {\n    default: true,\n    type: 'boolean'\n  },\n  retryWrites: {\n    default: true,\n    type: 'boolean'\n  },\n  serializeFunctions: {\n    type: 'boolean'\n  },\n  serverMonitoringMode: {\n    default: 'auto',\n    transform({\n      values: [value]\n    }) {\n      if (!Object.values(monitor_1.ServerMonitoringMode).includes(value)) {\n        throw new error_1.MongoParseError('serverMonitoringMode must be one of `auto`, `poll`, or `stream`');\n      }\n      return value;\n    }\n  },\n  serverSelectionTimeoutMS: {\n    default: 30000,\n    type: 'uint'\n  },\n  servername: {\n    type: 'string'\n  },\n  socketTimeoutMS: {\n    // TODO(NODE-6491): deprecated: 'Please use timeoutMS instead',\n    default: 0,\n    type: 'uint'\n  },\n  srvMaxHosts: {\n    type: 'uint',\n    default: 0\n  },\n  srvServiceName: {\n    type: 'string',\n    default: 'mongodb'\n  },\n  ssl: {\n    target: 'tls',\n    type: 'boolean'\n  },\n  timeoutMS: {\n    type: 'uint'\n  },\n  tls: {\n    type: 'boolean'\n  },\n  tlsAllowInvalidCertificates: {\n    target: 'rejectUnauthorized',\n    transform({\n      name,\n      values: [value]\n    }) {\n      // allowInvalidCertificates is the inverse of rejectUnauthorized\n      return !getBoolean(name, value);\n    }\n  },\n  tlsAllowInvalidHostnames: {\n    target: 'checkServerIdentity',\n    transform({\n      name,\n      values: [value]\n    }) {\n      // tlsAllowInvalidHostnames means setting the checkServerIdentity function to a noop\n      return getBoolean(name, value) ? () => undefined : undefined;\n    }\n  },\n  tlsCAFile: {\n    type: 'string'\n  },\n  tlsCRLFile: {\n    type: 'string'\n  },\n  tlsCertificateKeyFile: {\n    type: 'string'\n  },\n  tlsCertificateKeyFilePassword: {\n    target: 'passphrase',\n    type: 'any'\n  },\n  tlsInsecure: {\n    transform({\n      name,\n      options,\n      values: [value]\n    }) {\n      const tlsInsecure = getBoolean(name, value);\n      if (tlsInsecure) {\n        options.checkServerIdentity = () => undefined;\n        options.rejectUnauthorized = false;\n      } else {\n        options.checkServerIdentity = options.tlsAllowInvalidHostnames ? () => undefined : undefined;\n        options.rejectUnauthorized = options.tlsAllowInvalidCertificates ? false : true;\n      }\n      return tlsInsecure;\n    }\n  },\n  w: {\n    target: 'writeConcern',\n    transform({\n      values: [value],\n      options\n    }) {\n      return write_concern_1.WriteConcern.fromOptions({\n        writeConcern: {\n          ...options.writeConcern,\n          w: value\n        }\n      });\n    }\n  },\n  waitQueueTimeoutMS: {\n    // TODO(NODE-6491): deprecated: 'Please use timeoutMS instead',\n    default: 0,\n    type: 'uint'\n  },\n  writeConcern: {\n    target: 'writeConcern',\n    transform({\n      values: [value],\n      options\n    }) {\n      if ((0, utils_1.isRecord)(value) || value instanceof write_concern_1.WriteConcern) {\n        return write_concern_1.WriteConcern.fromOptions({\n          writeConcern: {\n            ...options.writeConcern,\n            ...value\n          }\n        });\n      } else if (value === 'majority' || typeof value === 'number') {\n        return write_concern_1.WriteConcern.fromOptions({\n          writeConcern: {\n            ...options.writeConcern,\n            w: value\n          }\n        });\n      }\n      throw new error_1.MongoParseError(`Invalid WriteConcern cannot parse: ${JSON.stringify(value)}`);\n    }\n  },\n  wtimeout: {\n    deprecated: 'Please use wtimeoutMS instead',\n    target: 'writeConcern',\n    transform({\n      values: [value],\n      options\n    }) {\n      const wc = write_concern_1.WriteConcern.fromOptions({\n        writeConcern: {\n          ...options.writeConcern,\n          wtimeout: getUIntFromOptions('wtimeout', value)\n        }\n      });\n      if (wc) return wc;\n      throw new error_1.MongoParseError(`Cannot make WriteConcern from wtimeout`);\n    }\n  },\n  wtimeoutMS: {\n    target: 'writeConcern',\n    transform({\n      values: [value],\n      options\n    }) {\n      const wc = write_concern_1.WriteConcern.fromOptions({\n        writeConcern: {\n          ...options.writeConcern,\n          wtimeoutMS: getUIntFromOptions('wtimeoutMS', value)\n        }\n      });\n      if (wc) return wc;\n      throw new error_1.MongoParseError(`Cannot make WriteConcern from wtimeout`);\n    }\n  },\n  zlibCompressionLevel: {\n    default: 0,\n    type: 'int'\n  },\n  mongodbLogPath: {\n    transform({\n      values: [value]\n    }) {\n      if (!(typeof value === 'string' && ['stderr', 'stdout'].includes(value) || value && typeof value === 'object' && 'write' in value && typeof value.write === 'function')) {\n        throw new error_1.MongoAPIError(`Option 'mongodbLogPath' must be of type 'stderr' | 'stdout' | MongoDBLogWritable`);\n      }\n      return value;\n    }\n  },\n  mongodbLogComponentSeverities: {\n    transform({\n      values: [value]\n    }) {\n      if (typeof value !== 'object' || !value) {\n        throw new error_1.MongoAPIError(`Option 'mongodbLogComponentSeverities' must be a non-null object`);\n      }\n      for (const [k, v] of Object.entries(value)) {\n        if (typeof v !== 'string' || typeof k !== 'string') {\n          throw new error_1.MongoAPIError(`User input for option 'mongodbLogComponentSeverities' object cannot include a non-string key or value`);\n        }\n        if (!Object.values(mongo_logger_1.MongoLoggableComponent).some(val => val === k) && k !== 'default') {\n          throw new error_1.MongoAPIError(`User input for option 'mongodbLogComponentSeverities' contains invalid key: ${k}`);\n        }\n        if (!Object.values(mongo_logger_1.SeverityLevel).some(val => val === v)) {\n          throw new error_1.MongoAPIError(`Option 'mongodbLogComponentSeverities' does not support ${v} as a value for ${k}`);\n        }\n      }\n      return value;\n    }\n  },\n  mongodbLogMaxDocumentLength: {\n    type: 'uint'\n  },\n  // Custom types for modifying core behavior\n  connectionType: {\n    type: 'any'\n  },\n  srvPoller: {\n    type: 'any'\n  },\n  // Accepted Node.js Options\n  allowPartialTrustChain: {\n    type: 'any'\n  },\n  minDHSize: {\n    type: 'any'\n  },\n  pskCallback: {\n    type: 'any'\n  },\n  secureContext: {\n    type: 'any'\n  },\n  enableTrace: {\n    type: 'any'\n  },\n  requestCert: {\n    type: 'any'\n  },\n  rejectUnauthorized: {\n    type: 'any'\n  },\n  checkServerIdentity: {\n    type: 'any'\n  },\n  ALPNProtocols: {\n    type: 'any'\n  },\n  SNICallback: {\n    type: 'any'\n  },\n  session: {\n    type: 'any'\n  },\n  requestOCSP: {\n    type: 'any'\n  },\n  localAddress: {\n    type: 'any'\n  },\n  localPort: {\n    type: 'any'\n  },\n  hints: {\n    type: 'any'\n  },\n  lookup: {\n    type: 'any'\n  },\n  ca: {\n    type: 'any'\n  },\n  cert: {\n    type: 'any'\n  },\n  ciphers: {\n    type: 'any'\n  },\n  crl: {\n    type: 'any'\n  },\n  ecdhCurve: {\n    type: 'any'\n  },\n  key: {\n    type: 'any'\n  },\n  passphrase: {\n    type: 'any'\n  },\n  pfx: {\n    type: 'any'\n  },\n  secureProtocol: {\n    type: 'any'\n  },\n  index: {\n    type: 'any'\n  },\n  // Legacy options from v3 era\n  useNewUrlParser: {\n    type: 'boolean',\n    deprecated: 'useNewUrlParser has no effect since Node.js Driver version 4.0.0 and will be removed in the next major version'\n  },\n  useUnifiedTopology: {\n    type: 'boolean',\n    deprecated: 'useUnifiedTopology has no effect since Node.js Driver version 4.0.0 and will be removed in the next major version'\n  },\n  __skipPingOnConnect: {\n    type: 'boolean'\n  }\n};\nexports.DEFAULT_OPTIONS = new CaseInsensitiveMap(Object.entries(exports.OPTIONS).filter(([, descriptor]) => descriptor.default != null).map(([k, d]) => [k, d.default]));", "map": {"version": 3, "names": ["exports", "resolveSRVRecord", "parseOptions", "dns", "require", "mongodb_connection_string_url_1", "url_1", "mongo_credentials_1", "providers_1", "client_metadata_1", "compression_1", "encrypter_1", "error_1", "mongo_client_1", "mongo_logger_1", "read_concern_1", "read_preference_1", "monitor_1", "utils_1", "write_concern_1", "VALID_TXT_RECORDS", "LB_SINGLE_HOST_ERROR", "LB_REPLICA_SET_ERROR", "LB_DIRECT_CONNECTION_ERROR", "retryDNSTimeoutFor", "api", "dnsReqRetryTimeout", "lookup<PERSON><PERSON><PERSON>", "promises", "firstDNSError", "code", "TIMEOUT", "resolveSrv", "resolveTxt", "options", "srvHost", "MongoAPIError", "txtResolutionPromise", "then", "undefined", "squashError", "hostname", "srvServiceName", "addresses", "length", "name", "checkParentDomainMatch", "hostAddresses", "map", "r", "HostAddress", "fromString", "port", "validateLoadBalancedOptions", "record", "error", "MongoParseError", "txtRecordOptions", "URLSearchParams", "join", "txtRecordOptionKeys", "keys", "some", "key", "includes", "option", "get", "source", "replicaSet", "loadBalanced", "userSpecifiedAuthSource", "credentials", "AUTH_MECHS_AUTH_SRC_EXTERNAL", "has", "mechanism", "MongoCredentials", "merge", "userSpecifiedReplicaSet", "srvMaxHosts", "checkTLSOptions", "allOptions", "check", "a", "b", "getBoolean", "value", "getIntFromOptions", "parsedInt", "parseInteger", "getUIntFromOptions", "parsedValue", "entriesFromString", "keyValuePairs", "split", "keyValue", "CaseInsensitiveMap", "Map", "constructor", "entries", "k", "v", "toLowerCase", "set", "delete", "uri", "mongoClient", "MongoClient", "useBigInt64", "promoteLongs", "promoteValues", "url", "default", "hosts", "isSRV", "mongoOptions", "Object", "create", "urlOptions", "pathname", "dbN<PERSON>", "decodeURIComponent", "slice", "username", "auth", "password", "searchParams", "values", "getAll", "isReadPreferenceTags", "test", "MongoInvalidArgumentError", "objectOptions", "filter", "uriMechanismProperties", "property", "allProvidedOptions", "allProvidedKeys", "Set", "objectOptionValue", "push", "url<PERSON><PERSON><PERSON>", "tlsAndSslOpts", "concat", "bind", "size", "unsupportedOptions", "setDifference", "Array", "from", "OPTIONS", "s", "optionWord", "isOrAre", "descriptor", "DEFAULT_OPTIONS", "setOption", "deprecated", "deprecatedMsg", "emitWarning", "isGssapi", "AuthMechanism", "MONGODB_GSSAPI", "isX509", "MONGODB_X509", "isAws", "MONGODB_AWS", "isOidc", "MONGODB_OIDC", "MongoMissingCredentialsError", "validate", "MONGODB_DEFAULT", "mechanismProperties", "autoEncryption", "Encrypter", "checkForMongoCrypt", "encrypter", "autoEncrypter", "directConnection", "noUserSpecifiedTLS", "noUserSpecifiedSSL", "tls", "userSpecifiedSrvOptions", "proxyHost", "proxyPort", "proxyUsername", "proxyPassword", "proxyOptions", "mongoLoggerOptions", "<PERSON>go<PERSON><PERSON><PERSON>", "resolveOptions", "MONGODB_LOG_COMMAND", "process", "env", "MONGODB_LOG_TOPOLOGY", "MONGODB_LOG_SERVER_SELECTION", "MONGODB_LOG_CONNECTION", "MONGODB_LOG_CLIENT", "MONGODB_LOG_ALL", "MONGODB_LOG_MAX_DOCUMENT_LENGTH", "MONGODB_LOG_PATH", "mongodbLogPath", "mongodbLogComponentSeverities", "mongodbLogMaxDocumentLength", "metadata", "makeClientMetadata", "extendedMetadata", "addContainerMetadata", "isSrv", "target", "type", "transform", "String", "isRecord", "transformValue", "appName", "authMechanism", "mechanisms", "m", "match", "RegExp", "raw", "MONGODB_PLAIN", "authMechanismProperties", "optionValue", "authSource", "autoSelectFamily", "autoSelectFamilyAttemptTimeout", "bsonRegExp", "serverApi", "version", "serverApiToValidate", "versionToValidate", "ServerApiVersion", "checkKeys", "compressors", "compressionList", "compVal", "comp<PERSON><PERSON><PERSON><PERSON><PERSON>", "isArray", "c", "Compressor", "add", "connectTimeoutMS", "driverInfo", "enableUtf8Validation", "family", "fieldsAsRaw", "forceServerObjectId", "fsync", "wc", "WriteConcern", "fromOptions", "writeConcern", "heartbeatFrequencyMS", "ignoreUndefined", "j", "journal", "localThresholdMS", "maxConnecting", "maxIdleTimeMS", "maxPoolSize", "maxStalenessSeconds", "readPreference", "ReadPreference", "minInternalBufferSize", "minPoolSize", "minHeartbeatFrequencyMS", "monitorCommands", "no<PERSON>elay", "pkFactory", "DEFAULT_PK_FACTORY", "createPk", "promoteBuffers", "readConcern", "ReadConcern", "JSON", "stringify", "readConcernLevel", "level", "primary", "rp", "rpOpts", "hedge", "tags", "readPreferenceTags", "tag", "readPreferenceTag", "retryReads", "retryWrites", "serializeFunctions", "serverMonitoringMode", "ServerMonitoringMode", "serverSelectionTimeoutMS", "servername", "socketTimeoutMS", "ssl", "timeoutMS", "tlsAllowInvalidCertificates", "tlsAllowInvalidHostnames", "tlsCAFile", "tlsCRLFile", "tlsCertificateKeyFile", "tlsCertificateKeyFilePassword", "tlsInsecure", "checkServerIdentity", "rejectUnauthorized", "w", "waitQueueTimeoutMS", "wtimeout", "wtimeoutMS", "zlibCompressionLevel", "write", "MongoLoggableComponent", "val", "SeverityLevel", "connectionType", "srv<PERSON><PERSON><PERSON>", "allowPartial<PERSON><PERSON><PERSON><PERSON><PERSON>", "minDHSize", "pskCallback", "secureContext", "enableTrace", "requestCert", "ALPNProtocols", "SNICallback", "session", "requestOCSP", "localAddress", "localPort", "hints", "lookup", "ca", "cert", "ciphers", "crl", "ecdhCurve", "passphrase", "pfx", "secureProtocol", "index", "useNewUrlParser", "useUnifiedTopology", "__skipPingOnConnect", "d"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\connection_string.ts"], "sourcesContent": ["import * as dns from 'dns';\nimport ConnectionString from 'mongodb-connection-string-url';\nimport { URLSearchParams } from 'url';\n\nimport type { Document } from './bson';\nimport { MongoCredentials } from './cmap/auth/mongo_credentials';\nimport { AUTH_MECHS_AUTH_SRC_EXTERNAL, AuthMechanism } from './cmap/auth/providers';\nimport { addContainerMetadata, makeClientMetadata } from './cmap/handshake/client_metadata';\nimport { Compressor, type CompressorName } from './cmap/wire_protocol/compression';\nimport { Encrypter } from './encrypter';\nimport {\n  MongoAPIError,\n  MongoInvalidArgumentError,\n  MongoMissingCredentialsError,\n  MongoParseError\n} from './error';\nimport {\n  MongoClient,\n  type MongoClientOptions,\n  type MongoOptions,\n  type PkFactory,\n  type ServerApi,\n  ServerApiVersion\n} from './mongo_client';\nimport { MongoLoggableComponent, MongoLogger, SeverityLevel } from './mongo_logger';\nimport { ReadConcern, type ReadConcernLevel } from './read_concern';\nimport { ReadPreference, type ReadPreferenceMode } from './read_preference';\nimport { ServerMonitoringMode } from './sdam/monitor';\nimport type { TagSet } from './sdam/server_description';\nimport {\n  checkParentDomainMatch,\n  DEFAULT_PK_FACTORY,\n  emitWarning,\n  HostAddress,\n  isRecord,\n  parseInteger,\n  setDifference,\n  squashError\n} from './utils';\nimport { type W, WriteConcern } from './write_concern';\n\nconst VALID_TXT_RECORDS = ['authSource', 'replicaSet', 'loadBalanced'];\n\nconst LB_SINGLE_HOST_ERROR = 'loadBalanced option only supported with a single host in the URI';\nconst LB_REPLICA_SET_ERROR = 'loadBalanced option not supported with a replicaSet option';\nconst LB_DIRECT_CONNECTION_ERROR =\n  'loadBalanced option not supported when directConnection is provided';\n\nfunction retryDNSTimeoutFor(api: 'resolveSrv'): (a: string) => Promise<dns.SrvRecord[]>;\nfunction retryDNSTimeoutFor(api: 'resolveTxt'): (a: string) => Promise<string[][]>;\nfunction retryDNSTimeoutFor(\n  api: 'resolveSrv' | 'resolveTxt'\n): (a: string) => Promise<dns.SrvRecord[] | string[][]> {\n  return async function dnsReqRetryTimeout(lookupAddress: string) {\n    try {\n      return await dns.promises[api](lookupAddress);\n    } catch (firstDNSError) {\n      if (firstDNSError.code === dns.TIMEOUT) {\n        return await dns.promises[api](lookupAddress);\n      } else {\n        throw firstDNSError;\n      }\n    }\n  };\n}\n\nconst resolveSrv = retryDNSTimeoutFor('resolveSrv');\nconst resolveTxt = retryDNSTimeoutFor('resolveTxt');\n\n/**\n * Lookup a `mongodb+srv` connection string, combine the parts and reparse it as a normal\n * connection string.\n *\n * @param uri - The connection string to parse\n * @param options - Optional user provided connection string options\n */\nexport async function resolveSRVRecord(options: MongoOptions): Promise<HostAddress[]> {\n  if (typeof options.srvHost !== 'string') {\n    throw new MongoAPIError('Option \"srvHost\" must not be empty');\n  }\n\n  // Asynchronously start TXT resolution so that we do not have to wait until\n  // the SRV record is resolved before starting a second DNS query.\n  const lookupAddress = options.srvHost;\n  const txtResolutionPromise = resolveTxt(lookupAddress);\n\n  txtResolutionPromise.then(undefined, squashError); // rejections will be handled later\n\n  const hostname = `_${options.srvServiceName}._tcp.${lookupAddress}`;\n  // Resolve the SRV record and use the result as the list of hosts to connect to.\n  const addresses = await resolveSrv(hostname);\n\n  if (addresses.length === 0) {\n    throw new MongoAPIError('No addresses found at host');\n  }\n\n  for (const { name } of addresses) {\n    checkParentDomainMatch(name, lookupAddress);\n  }\n\n  const hostAddresses = addresses.map(r => HostAddress.fromString(`${r.name}:${r.port ?? 27017}`));\n\n  validateLoadBalancedOptions(hostAddresses, options, true);\n\n  // Use the result of resolving the TXT record and add options from there if they exist.\n  let record;\n  try {\n    record = await txtResolutionPromise;\n  } catch (error) {\n    if (error.code !== 'ENODATA' && error.code !== 'ENOTFOUND') {\n      throw error;\n    }\n    return hostAddresses;\n  }\n\n  if (record.length > 1) {\n    throw new MongoParseError('Multiple text records not allowed');\n  }\n\n  const txtRecordOptions = new URLSearchParams(record[0].join(''));\n  const txtRecordOptionKeys = [...txtRecordOptions.keys()];\n  if (txtRecordOptionKeys.some(key => !VALID_TXT_RECORDS.includes(key))) {\n    throw new MongoParseError(`Text record may only set any of: ${VALID_TXT_RECORDS.join(', ')}`);\n  }\n\n  if (VALID_TXT_RECORDS.some(option => txtRecordOptions.get(option) === '')) {\n    throw new MongoParseError('Cannot have empty URI params in DNS TXT Record');\n  }\n\n  const source = txtRecordOptions.get('authSource') ?? undefined;\n  const replicaSet = txtRecordOptions.get('replicaSet') ?? undefined;\n  const loadBalanced = txtRecordOptions.get('loadBalanced') ?? undefined;\n\n  if (\n    !options.userSpecifiedAuthSource &&\n    source &&\n    options.credentials &&\n    !AUTH_MECHS_AUTH_SRC_EXTERNAL.has(options.credentials.mechanism)\n  ) {\n    options.credentials = MongoCredentials.merge(options.credentials, { source });\n  }\n\n  if (!options.userSpecifiedReplicaSet && replicaSet) {\n    options.replicaSet = replicaSet;\n  }\n\n  if (loadBalanced === 'true') {\n    options.loadBalanced = true;\n  }\n\n  if (options.replicaSet && options.srvMaxHosts > 0) {\n    throw new MongoParseError('Cannot combine replicaSet option with srvMaxHosts');\n  }\n\n  validateLoadBalancedOptions(hostAddresses, options, true);\n\n  return hostAddresses;\n}\n\n/**\n * Checks if TLS options are valid\n *\n * @param allOptions - All options provided by user or included in default options map\n * @throws MongoAPIError if TLS options are invalid\n */\nfunction checkTLSOptions(allOptions: CaseInsensitiveMap): void {\n  if (!allOptions) return;\n  const check = (a: string, b: string) => {\n    if (allOptions.has(a) && allOptions.has(b)) {\n      throw new MongoAPIError(`The '${a}' option cannot be used with the '${b}' option`);\n    }\n  };\n  check('tlsInsecure', 'tlsAllowInvalidCertificates');\n  check('tlsInsecure', 'tlsAllowInvalidHostnames');\n  check('tlsInsecure', 'tlsDisableCertificateRevocationCheck');\n  check('tlsInsecure', 'tlsDisableOCSPEndpointCheck');\n  check('tlsAllowInvalidCertificates', 'tlsDisableCertificateRevocationCheck');\n  check('tlsAllowInvalidCertificates', 'tlsDisableOCSPEndpointCheck');\n  check('tlsDisableCertificateRevocationCheck', 'tlsDisableOCSPEndpointCheck');\n}\nfunction getBoolean(name: string, value: unknown): boolean {\n  if (typeof value === 'boolean') return value;\n  switch (value) {\n    case 'true':\n      return true;\n    case 'false':\n      return false;\n    default:\n      throw new MongoParseError(`${name} must be either \"true\" or \"false\"`);\n  }\n}\n\nfunction getIntFromOptions(name: string, value: unknown): number {\n  const parsedInt = parseInteger(value);\n  if (parsedInt != null) {\n    return parsedInt;\n  }\n  throw new MongoParseError(`Expected ${name} to be stringified int value, got: ${value}`);\n}\n\nfunction getUIntFromOptions(name: string, value: unknown): number {\n  const parsedValue = getIntFromOptions(name, value);\n  if (parsedValue < 0) {\n    throw new MongoParseError(`${name} can only be a positive int value, got: ${value}`);\n  }\n  return parsedValue;\n}\n\nfunction* entriesFromString(value: string): Generator<[string, string]> {\n  if (value === '') {\n    return;\n  }\n  const keyValuePairs = value.split(',');\n  for (const keyValue of keyValuePairs) {\n    const [key, value] = keyValue.split(/:(.*)/);\n    if (value == null) {\n      throw new MongoParseError('Cannot have undefined values in key value pairs');\n    }\n\n    yield [key, value];\n  }\n}\n\nclass CaseInsensitiveMap<Value = any> extends Map<string, Value> {\n  constructor(entries: Array<[string, any]> = []) {\n    super(entries.map(([k, v]) => [k.toLowerCase(), v]));\n  }\n  override has(k: string) {\n    return super.has(k.toLowerCase());\n  }\n  override get(k: string) {\n    return super.get(k.toLowerCase());\n  }\n  override set(k: string, v: any) {\n    return super.set(k.toLowerCase(), v);\n  }\n  override delete(k: string): boolean {\n    return super.delete(k.toLowerCase());\n  }\n}\n\nexport function parseOptions(\n  uri: string,\n  mongoClient: MongoClient | MongoClientOptions | undefined = undefined,\n  options: MongoClientOptions = {}\n): MongoOptions {\n  if (mongoClient != null && !(mongoClient instanceof MongoClient)) {\n    options = mongoClient;\n    mongoClient = undefined;\n  }\n\n  // validate BSONOptions\n  if (options.useBigInt64 && typeof options.promoteLongs === 'boolean' && !options.promoteLongs) {\n    throw new MongoAPIError('Must request either bigint or Long for int64 deserialization');\n  }\n\n  if (options.useBigInt64 && typeof options.promoteValues === 'boolean' && !options.promoteValues) {\n    throw new MongoAPIError('Must request either bigint or Long for int64 deserialization');\n  }\n\n  const url = new ConnectionString(uri);\n  const { hosts, isSRV } = url;\n\n  const mongoOptions = Object.create(null);\n\n  mongoOptions.hosts = isSRV ? [] : hosts.map(HostAddress.fromString);\n\n  const urlOptions = new CaseInsensitiveMap<unknown[]>();\n\n  if (url.pathname !== '/' && url.pathname !== '') {\n    const dbName = decodeURIComponent(\n      url.pathname[0] === '/' ? url.pathname.slice(1) : url.pathname\n    );\n    if (dbName) {\n      urlOptions.set('dbName', [dbName]);\n    }\n  }\n\n  if (url.username !== '') {\n    const auth: Document = {\n      username: decodeURIComponent(url.username)\n    };\n\n    if (typeof url.password === 'string') {\n      auth.password = decodeURIComponent(url.password);\n    }\n\n    urlOptions.set('auth', [auth]);\n  }\n\n  for (const key of url.searchParams.keys()) {\n    const values = url.searchParams.getAll(key);\n\n    const isReadPreferenceTags = /readPreferenceTags/i.test(key);\n\n    if (!isReadPreferenceTags && values.length > 1) {\n      throw new MongoInvalidArgumentError(\n        `URI option \"${key}\" cannot appear more than once in the connection string`\n      );\n    }\n\n    if (!isReadPreferenceTags && values.includes('')) {\n      throw new MongoAPIError(`URI option \"${key}\" cannot be specified with no value`);\n    }\n\n    if (!urlOptions.has(key)) {\n      urlOptions.set(key, values);\n    }\n  }\n\n  const objectOptions = new CaseInsensitiveMap<unknown>(\n    Object.entries(options).filter(([, v]) => v != null)\n  );\n\n  // Validate options that can only be provided by one of uri or object\n\n  if (urlOptions.has('serverApi')) {\n    throw new MongoParseError(\n      'URI cannot contain `serverApi`, it can only be passed to the client'\n    );\n  }\n\n  const uriMechanismProperties = urlOptions.get('authMechanismProperties');\n  if (uriMechanismProperties) {\n    for (const property of uriMechanismProperties) {\n      if (/(^|,)ALLOWED_HOSTS:/.test(property as string)) {\n        throw new MongoParseError(\n          'Auth mechanism property ALLOWED_HOSTS is not allowed in the connection string.'\n        );\n      }\n    }\n  }\n\n  if (objectOptions.has('loadBalanced')) {\n    throw new MongoParseError('loadBalanced is only a valid option in the URI');\n  }\n\n  // All option collection\n\n  const allProvidedOptions = new CaseInsensitiveMap<unknown[]>();\n\n  const allProvidedKeys = new Set<string>([...urlOptions.keys(), ...objectOptions.keys()]);\n\n  for (const key of allProvidedKeys) {\n    const values = [];\n    const objectOptionValue = objectOptions.get(key);\n    if (objectOptionValue != null) {\n      values.push(objectOptionValue);\n    }\n\n    const urlValues = urlOptions.get(key) ?? [];\n    values.push(...urlValues);\n    allProvidedOptions.set(key, values);\n  }\n\n  if (allProvidedOptions.has('tls') || allProvidedOptions.has('ssl')) {\n    const tlsAndSslOpts = (allProvidedOptions.get('tls') || [])\n      .concat(allProvidedOptions.get('ssl') || [])\n      .map(getBoolean.bind(null, 'tls/ssl'));\n    if (new Set(tlsAndSslOpts).size !== 1) {\n      throw new MongoParseError('All values of tls/ssl must be the same.');\n    }\n  }\n\n  checkTLSOptions(allProvidedOptions);\n\n  const unsupportedOptions = setDifference(\n    allProvidedKeys,\n    Array.from(Object.keys(OPTIONS)).map(s => s.toLowerCase())\n  );\n  if (unsupportedOptions.size !== 0) {\n    const optionWord = unsupportedOptions.size > 1 ? 'options' : 'option';\n    const isOrAre = unsupportedOptions.size > 1 ? 'are' : 'is';\n    throw new MongoParseError(\n      `${optionWord} ${Array.from(unsupportedOptions).join(', ')} ${isOrAre} not supported`\n    );\n  }\n\n  // Option parsing and setting\n\n  for (const [key, descriptor] of Object.entries(OPTIONS)) {\n    const values = allProvidedOptions.get(key);\n    if (!values || values.length === 0) {\n      if (DEFAULT_OPTIONS.has(key)) {\n        setOption(mongoOptions, key, descriptor, [DEFAULT_OPTIONS.get(key)]);\n      }\n    } else {\n      const { deprecated } = descriptor;\n      if (deprecated) {\n        const deprecatedMsg = typeof deprecated === 'string' ? `: ${deprecated}` : '';\n        emitWarning(`${key} is a deprecated option${deprecatedMsg}`);\n      }\n\n      setOption(mongoOptions, key, descriptor, values);\n    }\n  }\n\n  if (mongoOptions.credentials) {\n    const isGssapi = mongoOptions.credentials.mechanism === AuthMechanism.MONGODB_GSSAPI;\n    const isX509 = mongoOptions.credentials.mechanism === AuthMechanism.MONGODB_X509;\n    const isAws = mongoOptions.credentials.mechanism === AuthMechanism.MONGODB_AWS;\n    const isOidc = mongoOptions.credentials.mechanism === AuthMechanism.MONGODB_OIDC;\n    if (\n      (isGssapi || isX509) &&\n      allProvidedOptions.has('authSource') &&\n      mongoOptions.credentials.source !== '$external'\n    ) {\n      // If authSource was explicitly given and its incorrect, we error\n      throw new MongoParseError(\n        `authMechanism ${mongoOptions.credentials.mechanism} requires an authSource of '$external'`\n      );\n    }\n\n    if (\n      !(isGssapi || isX509 || isAws || isOidc) &&\n      mongoOptions.dbName &&\n      !allProvidedOptions.has('authSource')\n    ) {\n      // inherit the dbName unless GSSAPI or X509, then silently ignore dbName\n      // and there was no specific authSource given\n      mongoOptions.credentials = MongoCredentials.merge(mongoOptions.credentials, {\n        source: mongoOptions.dbName\n      });\n    }\n\n    if (isAws && mongoOptions.credentials.username && !mongoOptions.credentials.password) {\n      throw new MongoMissingCredentialsError(\n        `When using ${mongoOptions.credentials.mechanism} password must be set when a username is specified`\n      );\n    }\n\n    mongoOptions.credentials.validate();\n\n    // Check if the only auth related option provided was authSource, if so we can remove credentials\n    if (\n      mongoOptions.credentials.password === '' &&\n      mongoOptions.credentials.username === '' &&\n      mongoOptions.credentials.mechanism === AuthMechanism.MONGODB_DEFAULT &&\n      Object.keys(mongoOptions.credentials.mechanismProperties).length === 0\n    ) {\n      delete mongoOptions.credentials;\n    }\n  }\n\n  if (!mongoOptions.dbName) {\n    // dbName default is applied here because of the credential validation above\n    mongoOptions.dbName = 'test';\n  }\n\n  validateLoadBalancedOptions(hosts, mongoOptions, isSRV);\n\n  if (mongoClient && mongoOptions.autoEncryption) {\n    Encrypter.checkForMongoCrypt();\n    mongoOptions.encrypter = new Encrypter(mongoClient, uri, options);\n    mongoOptions.autoEncrypter = mongoOptions.encrypter.autoEncrypter;\n  }\n\n  // Potential SRV Overrides and SRV connection string validations\n\n  mongoOptions.userSpecifiedAuthSource =\n    objectOptions.has('authSource') || urlOptions.has('authSource');\n  mongoOptions.userSpecifiedReplicaSet =\n    objectOptions.has('replicaSet') || urlOptions.has('replicaSet');\n\n  if (isSRV) {\n    // SRV Record is resolved upon connecting\n    mongoOptions.srvHost = hosts[0];\n\n    if (mongoOptions.directConnection) {\n      throw new MongoAPIError('SRV URI does not support directConnection');\n    }\n\n    if (mongoOptions.srvMaxHosts > 0 && typeof mongoOptions.replicaSet === 'string') {\n      throw new MongoParseError('Cannot use srvMaxHosts option with replicaSet');\n    }\n\n    // SRV turns on TLS by default, but users can override and turn it off\n    const noUserSpecifiedTLS = !objectOptions.has('tls') && !urlOptions.has('tls');\n    const noUserSpecifiedSSL = !objectOptions.has('ssl') && !urlOptions.has('ssl');\n    if (noUserSpecifiedTLS && noUserSpecifiedSSL) {\n      mongoOptions.tls = true;\n    }\n  } else {\n    const userSpecifiedSrvOptions =\n      urlOptions.has('srvMaxHosts') ||\n      objectOptions.has('srvMaxHosts') ||\n      urlOptions.has('srvServiceName') ||\n      objectOptions.has('srvServiceName');\n\n    if (userSpecifiedSrvOptions) {\n      throw new MongoParseError(\n        'Cannot use srvMaxHosts or srvServiceName with a non-srv connection string'\n      );\n    }\n  }\n\n  if (mongoOptions.directConnection && mongoOptions.hosts.length !== 1) {\n    throw new MongoParseError('directConnection option requires exactly one host');\n  }\n\n  if (\n    !mongoOptions.proxyHost &&\n    (mongoOptions.proxyPort || mongoOptions.proxyUsername || mongoOptions.proxyPassword)\n  ) {\n    throw new MongoParseError('Must specify proxyHost if other proxy options are passed');\n  }\n\n  if (\n    (mongoOptions.proxyUsername && !mongoOptions.proxyPassword) ||\n    (!mongoOptions.proxyUsername && mongoOptions.proxyPassword)\n  ) {\n    throw new MongoParseError('Can only specify both of proxy username/password or neither');\n  }\n\n  const proxyOptions = ['proxyHost', 'proxyPort', 'proxyUsername', 'proxyPassword'].map(\n    key => urlOptions.get(key) ?? []\n  );\n\n  if (proxyOptions.some(options => options.length > 1)) {\n    throw new MongoParseError(\n      'Proxy options cannot be specified multiple times in the connection string'\n    );\n  }\n\n  mongoOptions.mongoLoggerOptions = MongoLogger.resolveOptions(\n    {\n      MONGODB_LOG_COMMAND: process.env.MONGODB_LOG_COMMAND,\n      MONGODB_LOG_TOPOLOGY: process.env.MONGODB_LOG_TOPOLOGY,\n      MONGODB_LOG_SERVER_SELECTION: process.env.MONGODB_LOG_SERVER_SELECTION,\n      MONGODB_LOG_CONNECTION: process.env.MONGODB_LOG_CONNECTION,\n      MONGODB_LOG_CLIENT: process.env.MONGODB_LOG_CLIENT,\n      MONGODB_LOG_ALL: process.env.MONGODB_LOG_ALL,\n      MONGODB_LOG_MAX_DOCUMENT_LENGTH: process.env.MONGODB_LOG_MAX_DOCUMENT_LENGTH,\n      MONGODB_LOG_PATH: process.env.MONGODB_LOG_PATH\n    },\n    {\n      mongodbLogPath: mongoOptions.mongodbLogPath,\n      mongodbLogComponentSeverities: mongoOptions.mongodbLogComponentSeverities,\n      mongodbLogMaxDocumentLength: mongoOptions.mongodbLogMaxDocumentLength\n    }\n  );\n\n  mongoOptions.metadata = makeClientMetadata(mongoOptions);\n\n  mongoOptions.extendedMetadata = addContainerMetadata(mongoOptions.metadata).then(\n    undefined,\n    squashError\n  ); // rejections will be handled later\n\n  return mongoOptions;\n}\n\n/**\n * #### Throws if LB mode is true:\n * - hosts contains more than one host\n * - there is a replicaSet name set\n * - directConnection is set\n * - if srvMaxHosts is used when an srv connection string is passed in\n *\n * @throws MongoParseError\n */\nfunction validateLoadBalancedOptions(\n  hosts: HostAddress[] | string[],\n  mongoOptions: MongoOptions,\n  isSrv: boolean\n): void {\n  if (mongoOptions.loadBalanced) {\n    if (hosts.length > 1) {\n      throw new MongoParseError(LB_SINGLE_HOST_ERROR);\n    }\n    if (mongoOptions.replicaSet) {\n      throw new MongoParseError(LB_REPLICA_SET_ERROR);\n    }\n    if (mongoOptions.directConnection) {\n      throw new MongoParseError(LB_DIRECT_CONNECTION_ERROR);\n    }\n\n    if (isSrv && mongoOptions.srvMaxHosts > 0) {\n      throw new MongoParseError('Cannot limit srv hosts with loadBalanced enabled');\n    }\n  }\n  return;\n}\n\nfunction setOption(\n  mongoOptions: any,\n  key: string,\n  descriptor: OptionDescriptor,\n  values: unknown[]\n) {\n  const { target, type, transform } = descriptor;\n  const name = target ?? key;\n\n  switch (type) {\n    case 'boolean':\n      mongoOptions[name] = getBoolean(name, values[0]);\n      break;\n    case 'int':\n      mongoOptions[name] = getIntFromOptions(name, values[0]);\n      break;\n    case 'uint':\n      mongoOptions[name] = getUIntFromOptions(name, values[0]);\n      break;\n    case 'string':\n      if (values[0] == null) {\n        break;\n      }\n      mongoOptions[name] = String(values[0]);\n      break;\n    case 'record':\n      if (!isRecord(values[0])) {\n        throw new MongoParseError(`${name} must be an object`);\n      }\n      mongoOptions[name] = values[0];\n      break;\n    case 'any':\n      mongoOptions[name] = values[0];\n      break;\n    default: {\n      if (!transform) {\n        throw new MongoParseError('Descriptors missing a type must define a transform');\n      }\n      const transformValue = transform({ name, options: mongoOptions, values });\n      mongoOptions[name] = transformValue;\n      break;\n    }\n  }\n}\n\ninterface OptionDescriptor {\n  target?: string;\n  type?: 'boolean' | 'int' | 'uint' | 'record' | 'string' | 'any';\n  default?: any;\n\n  deprecated?: boolean | string;\n  /**\n   * @param name - the original option name\n   * @param options - the options so far for resolution\n   * @param values - the possible values in precedence order\n   */\n  transform?: (args: { name: string; options: MongoOptions; values: unknown[] }) => unknown;\n}\n\nexport const OPTIONS = {\n  appName: {\n    type: 'string'\n  },\n  auth: {\n    target: 'credentials',\n    transform({ name, options, values: [value] }): MongoCredentials {\n      if (!isRecord(value, ['username', 'password'] as const)) {\n        throw new MongoParseError(\n          `${name} must be an object with 'username' and 'password' properties`\n        );\n      }\n      return MongoCredentials.merge(options.credentials, {\n        username: value.username,\n        password: value.password\n      });\n    }\n  },\n  authMechanism: {\n    target: 'credentials',\n    transform({ options, values: [value] }): MongoCredentials {\n      const mechanisms = Object.values(AuthMechanism);\n      const [mechanism] = mechanisms.filter(m => m.match(RegExp(String.raw`\\b${value}\\b`, 'i')));\n      if (!mechanism) {\n        throw new MongoParseError(`authMechanism one of ${mechanisms}, got ${value}`);\n      }\n      let source = options.credentials?.source;\n      if (\n        mechanism === AuthMechanism.MONGODB_PLAIN ||\n        AUTH_MECHS_AUTH_SRC_EXTERNAL.has(mechanism)\n      ) {\n        // some mechanisms have '$external' as the Auth Source\n        source = '$external';\n      }\n\n      let password = options.credentials?.password;\n      if (mechanism === AuthMechanism.MONGODB_X509 && password === '') {\n        password = undefined;\n      }\n      return MongoCredentials.merge(options.credentials, {\n        mechanism,\n        source,\n        password\n      });\n    }\n  },\n  // Note that if the authMechanismProperties contain a TOKEN_RESOURCE that has a\n  // comma in it, it MUST be supplied as a MongoClient option instead of in the\n  // connection string.\n  authMechanismProperties: {\n    target: 'credentials',\n    transform({ options, values }): MongoCredentials {\n      // We can have a combination of options passed in the URI and options passed\n      // as an object to the MongoClient. So we must transform the string options\n      // as well as merge them together with a potentially provided object.\n      let mechanismProperties = Object.create(null);\n\n      for (const optionValue of values) {\n        if (typeof optionValue === 'string') {\n          for (const [key, value] of entriesFromString(optionValue)) {\n            try {\n              mechanismProperties[key] = getBoolean(key, value);\n            } catch {\n              mechanismProperties[key] = value;\n            }\n          }\n        } else {\n          if (!isRecord(optionValue)) {\n            throw new MongoParseError('AuthMechanismProperties must be an object');\n          }\n          mechanismProperties = { ...optionValue };\n        }\n      }\n      return MongoCredentials.merge(options.credentials, {\n        mechanismProperties\n      });\n    }\n  },\n  authSource: {\n    target: 'credentials',\n    transform({ options, values: [value] }): MongoCredentials {\n      const source = String(value);\n      return MongoCredentials.merge(options.credentials, { source });\n    }\n  },\n  autoEncryption: {\n    type: 'record'\n  },\n  autoSelectFamily: {\n    type: 'boolean',\n    default: true\n  },\n  autoSelectFamilyAttemptTimeout: {\n    type: 'uint'\n  },\n  bsonRegExp: {\n    type: 'boolean'\n  },\n  serverApi: {\n    target: 'serverApi',\n    transform({ values: [version] }): ServerApi {\n      const serverApiToValidate =\n        typeof version === 'string' ? ({ version } as ServerApi) : (version as ServerApi);\n      const versionToValidate = serverApiToValidate && serverApiToValidate.version;\n      if (!versionToValidate) {\n        throw new MongoParseError(\n          `Invalid \\`serverApi\\` property; must specify a version from the following enum: [\"${Object.values(\n            ServerApiVersion\n          ).join('\", \"')}\"]`\n        );\n      }\n      if (!Object.values(ServerApiVersion).some(v => v === versionToValidate)) {\n        throw new MongoParseError(\n          `Invalid server API version=${versionToValidate}; must be in the following enum: [\"${Object.values(\n            ServerApiVersion\n          ).join('\", \"')}\"]`\n        );\n      }\n      return serverApiToValidate;\n    }\n  },\n  checkKeys: {\n    type: 'boolean'\n  },\n  compressors: {\n    default: 'none',\n    target: 'compressors',\n    transform({ values }) {\n      const compressionList = new Set();\n      for (const compVal of values as (CompressorName[] | string)[]) {\n        const compValArray = typeof compVal === 'string' ? compVal.split(',') : compVal;\n        if (!Array.isArray(compValArray)) {\n          throw new MongoInvalidArgumentError(\n            'compressors must be an array or a comma-delimited list of strings'\n          );\n        }\n        for (const c of compValArray) {\n          if (Object.keys(Compressor).includes(String(c))) {\n            compressionList.add(String(c));\n          } else {\n            throw new MongoInvalidArgumentError(\n              `${c} is not a valid compression mechanism. Must be one of: ${Object.keys(\n                Compressor\n              )}.`\n            );\n          }\n        }\n      }\n      return [...compressionList];\n    }\n  },\n  connectTimeoutMS: {\n    default: 30000,\n    type: 'uint'\n  },\n  dbName: {\n    type: 'string'\n  },\n  directConnection: {\n    default: false,\n    type: 'boolean'\n  },\n  driverInfo: {\n    default: {},\n    type: 'record'\n  },\n  enableUtf8Validation: { type: 'boolean', default: true },\n  family: {\n    transform({ name, values: [value] }): 4 | 6 {\n      const transformValue = getIntFromOptions(name, value);\n      if (transformValue === 4 || transformValue === 6) {\n        return transformValue;\n      }\n      throw new MongoParseError(`Option 'family' must be 4 or 6 got ${transformValue}.`);\n    }\n  },\n  fieldsAsRaw: {\n    type: 'record'\n  },\n  forceServerObjectId: {\n    default: false,\n    type: 'boolean'\n  },\n  fsync: {\n    deprecated: 'Please use journal instead',\n    target: 'writeConcern',\n    transform({ name, options, values: [value] }): WriteConcern {\n      const wc = WriteConcern.fromOptions({\n        writeConcern: {\n          ...options.writeConcern,\n          fsync: getBoolean(name, value)\n        }\n      });\n      if (!wc) throw new MongoParseError(`Unable to make a writeConcern from fsync=${value}`);\n      return wc;\n    }\n  } as OptionDescriptor,\n  heartbeatFrequencyMS: {\n    default: 10000,\n    type: 'uint'\n  },\n  ignoreUndefined: {\n    type: 'boolean'\n  },\n  j: {\n    deprecated: 'Please use journal instead',\n    target: 'writeConcern',\n    transform({ name, options, values: [value] }): WriteConcern {\n      const wc = WriteConcern.fromOptions({\n        writeConcern: {\n          ...options.writeConcern,\n          journal: getBoolean(name, value)\n        }\n      });\n      if (!wc) throw new MongoParseError(`Unable to make a writeConcern from journal=${value}`);\n      return wc;\n    }\n  } as OptionDescriptor,\n  journal: {\n    target: 'writeConcern',\n    transform({ name, options, values: [value] }): WriteConcern {\n      const wc = WriteConcern.fromOptions({\n        writeConcern: {\n          ...options.writeConcern,\n          journal: getBoolean(name, value)\n        }\n      });\n      if (!wc) throw new MongoParseError(`Unable to make a writeConcern from journal=${value}`);\n      return wc;\n    }\n  },\n  loadBalanced: {\n    default: false,\n    type: 'boolean'\n  },\n  localThresholdMS: {\n    default: 15,\n    type: 'uint'\n  },\n  maxConnecting: {\n    default: 2,\n    transform({ name, values: [value] }): number {\n      const maxConnecting = getUIntFromOptions(name, value);\n      if (maxConnecting === 0) {\n        throw new MongoInvalidArgumentError('maxConnecting must be > 0 if specified');\n      }\n      return maxConnecting;\n    }\n  },\n  maxIdleTimeMS: {\n    default: 0,\n    type: 'uint'\n  },\n  maxPoolSize: {\n    default: 100,\n    type: 'uint'\n  },\n  maxStalenessSeconds: {\n    target: 'readPreference',\n    transform({ name, options, values: [value] }) {\n      const maxStalenessSeconds = getUIntFromOptions(name, value);\n      if (options.readPreference) {\n        return ReadPreference.fromOptions({\n          readPreference: { ...options.readPreference, maxStalenessSeconds }\n        });\n      } else {\n        return new ReadPreference('secondary', undefined, { maxStalenessSeconds });\n      }\n    }\n  },\n  minInternalBufferSize: {\n    type: 'uint'\n  },\n  minPoolSize: {\n    default: 0,\n    type: 'uint'\n  },\n  minHeartbeatFrequencyMS: {\n    default: 500,\n    type: 'uint'\n  },\n  monitorCommands: {\n    default: false,\n    type: 'boolean'\n  },\n  name: {\n    target: 'driverInfo',\n    transform({ values: [value], options }) {\n      return { ...options.driverInfo, name: String(value) };\n    }\n  } as OptionDescriptor,\n  noDelay: {\n    default: true,\n    type: 'boolean'\n  },\n  pkFactory: {\n    default: DEFAULT_PK_FACTORY,\n    transform({ values: [value] }): PkFactory {\n      if (isRecord(value, ['createPk'] as const) && typeof value.createPk === 'function') {\n        return value as PkFactory;\n      }\n      throw new MongoParseError(\n        `Option pkFactory must be an object with a createPk function, got ${value}`\n      );\n    }\n  },\n  promoteBuffers: {\n    type: 'boolean'\n  },\n  promoteLongs: {\n    type: 'boolean'\n  },\n  promoteValues: {\n    type: 'boolean'\n  },\n  useBigInt64: {\n    type: 'boolean'\n  },\n  proxyHost: {\n    type: 'string'\n  },\n  proxyPassword: {\n    type: 'string'\n  },\n  proxyPort: {\n    type: 'uint'\n  },\n  proxyUsername: {\n    type: 'string'\n  },\n  raw: {\n    default: false,\n    type: 'boolean'\n  },\n  readConcern: {\n    transform({ values: [value], options }) {\n      if (value instanceof ReadConcern || isRecord(value, ['level'] as const)) {\n        return ReadConcern.fromOptions({ ...options.readConcern, ...value } as any);\n      }\n      throw new MongoParseError(`ReadConcern must be an object, got ${JSON.stringify(value)}`);\n    }\n  },\n  readConcernLevel: {\n    target: 'readConcern',\n    transform({ values: [level], options }) {\n      return ReadConcern.fromOptions({\n        ...options.readConcern,\n        level: level as ReadConcernLevel\n      });\n    }\n  },\n  readPreference: {\n    default: ReadPreference.primary,\n    transform({ values: [value], options }) {\n      if (value instanceof ReadPreference) {\n        return ReadPreference.fromOptions({\n          readPreference: { ...options.readPreference, ...value },\n          ...value\n        } as any);\n      }\n      if (isRecord(value, ['mode'] as const)) {\n        const rp = ReadPreference.fromOptions({\n          readPreference: { ...options.readPreference, ...value },\n          ...value\n        } as any);\n        if (rp) return rp;\n        else throw new MongoParseError(`Cannot make read preference from ${JSON.stringify(value)}`);\n      }\n      if (typeof value === 'string') {\n        const rpOpts = {\n          hedge: options.readPreference?.hedge,\n          maxStalenessSeconds: options.readPreference?.maxStalenessSeconds\n        };\n        return new ReadPreference(\n          value as ReadPreferenceMode,\n          options.readPreference?.tags,\n          rpOpts\n        );\n      }\n      throw new MongoParseError(`Unknown ReadPreference value: ${value}`);\n    }\n  },\n  readPreferenceTags: {\n    target: 'readPreference',\n    transform({\n      values,\n      options\n    }: {\n      values: Array<string | Record<string, string>[]>;\n      options: MongoClientOptions;\n    }) {\n      const tags: Array<string | Record<string, string>> = Array.isArray(values[0])\n        ? values[0]\n        : (values as Array<string>);\n      const readPreferenceTags = [];\n      for (const tag of tags) {\n        const readPreferenceTag: TagSet = Object.create(null);\n        if (typeof tag === 'string') {\n          for (const [k, v] of entriesFromString(tag)) {\n            readPreferenceTag[k] = v;\n          }\n        }\n        if (isRecord(tag)) {\n          for (const [k, v] of Object.entries(tag)) {\n            readPreferenceTag[k] = v;\n          }\n        }\n        readPreferenceTags.push(readPreferenceTag);\n      }\n      return ReadPreference.fromOptions({\n        readPreference: options.readPreference,\n        readPreferenceTags\n      });\n    }\n  },\n  replicaSet: {\n    type: 'string'\n  },\n  retryReads: {\n    default: true,\n    type: 'boolean'\n  },\n  retryWrites: {\n    default: true,\n    type: 'boolean'\n  },\n  serializeFunctions: {\n    type: 'boolean'\n  },\n  serverMonitoringMode: {\n    default: 'auto',\n    transform({ values: [value] }) {\n      if (!Object.values(ServerMonitoringMode).includes(value as any)) {\n        throw new MongoParseError(\n          'serverMonitoringMode must be one of `auto`, `poll`, or `stream`'\n        );\n      }\n      return value;\n    }\n  },\n  serverSelectionTimeoutMS: {\n    default: 30000,\n    type: 'uint'\n  },\n  servername: {\n    type: 'string'\n  },\n  socketTimeoutMS: {\n    // TODO(NODE-6491): deprecated: 'Please use timeoutMS instead',\n    default: 0,\n    type: 'uint'\n  },\n  srvMaxHosts: {\n    type: 'uint',\n    default: 0\n  },\n  srvServiceName: {\n    type: 'string',\n    default: 'mongodb'\n  },\n  ssl: {\n    target: 'tls',\n    type: 'boolean'\n  },\n  timeoutMS: {\n    type: 'uint'\n  },\n  tls: {\n    type: 'boolean'\n  },\n  tlsAllowInvalidCertificates: {\n    target: 'rejectUnauthorized',\n    transform({ name, values: [value] }) {\n      // allowInvalidCertificates is the inverse of rejectUnauthorized\n      return !getBoolean(name, value);\n    }\n  },\n  tlsAllowInvalidHostnames: {\n    target: 'checkServerIdentity',\n    transform({ name, values: [value] }) {\n      // tlsAllowInvalidHostnames means setting the checkServerIdentity function to a noop\n      return getBoolean(name, value) ? () => undefined : undefined;\n    }\n  },\n  tlsCAFile: {\n    type: 'string'\n  },\n  tlsCRLFile: {\n    type: 'string'\n  },\n  tlsCertificateKeyFile: {\n    type: 'string'\n  },\n  tlsCertificateKeyFilePassword: {\n    target: 'passphrase',\n    type: 'any'\n  },\n  tlsInsecure: {\n    transform({ name, options, values: [value] }) {\n      const tlsInsecure = getBoolean(name, value);\n      if (tlsInsecure) {\n        options.checkServerIdentity = () => undefined;\n        options.rejectUnauthorized = false;\n      } else {\n        options.checkServerIdentity = options.tlsAllowInvalidHostnames\n          ? () => undefined\n          : undefined;\n        options.rejectUnauthorized = options.tlsAllowInvalidCertificates ? false : true;\n      }\n      return tlsInsecure;\n    }\n  },\n  w: {\n    target: 'writeConcern',\n    transform({ values: [value], options }) {\n      return WriteConcern.fromOptions({ writeConcern: { ...options.writeConcern, w: value as W } });\n    }\n  },\n  waitQueueTimeoutMS: {\n    // TODO(NODE-6491): deprecated: 'Please use timeoutMS instead',\n    default: 0,\n    type: 'uint'\n  },\n  writeConcern: {\n    target: 'writeConcern',\n    transform({ values: [value], options }) {\n      if (isRecord(value) || value instanceof WriteConcern) {\n        return WriteConcern.fromOptions({\n          writeConcern: {\n            ...options.writeConcern,\n            ...value\n          }\n        });\n      } else if (value === 'majority' || typeof value === 'number') {\n        return WriteConcern.fromOptions({\n          writeConcern: {\n            ...options.writeConcern,\n            w: value\n          }\n        });\n      }\n\n      throw new MongoParseError(`Invalid WriteConcern cannot parse: ${JSON.stringify(value)}`);\n    }\n  },\n  wtimeout: {\n    deprecated: 'Please use wtimeoutMS instead',\n    target: 'writeConcern',\n    transform({ values: [value], options }) {\n      const wc = WriteConcern.fromOptions({\n        writeConcern: {\n          ...options.writeConcern,\n          wtimeout: getUIntFromOptions('wtimeout', value)\n        }\n      });\n      if (wc) return wc;\n      throw new MongoParseError(`Cannot make WriteConcern from wtimeout`);\n    }\n  } as OptionDescriptor,\n  wtimeoutMS: {\n    target: 'writeConcern',\n    transform({ values: [value], options }) {\n      const wc = WriteConcern.fromOptions({\n        writeConcern: {\n          ...options.writeConcern,\n          wtimeoutMS: getUIntFromOptions('wtimeoutMS', value)\n        }\n      });\n      if (wc) return wc;\n      throw new MongoParseError(`Cannot make WriteConcern from wtimeout`);\n    }\n  },\n  zlibCompressionLevel: {\n    default: 0,\n    type: 'int'\n  },\n  mongodbLogPath: {\n    transform({ values: [value] }) {\n      if (\n        !(\n          (typeof value === 'string' && ['stderr', 'stdout'].includes(value)) ||\n          (value &&\n            typeof value === 'object' &&\n            'write' in value &&\n            typeof value.write === 'function')\n        )\n      ) {\n        throw new MongoAPIError(\n          `Option 'mongodbLogPath' must be of type 'stderr' | 'stdout' | MongoDBLogWritable`\n        );\n      }\n      return value;\n    }\n  },\n  mongodbLogComponentSeverities: {\n    transform({ values: [value] }) {\n      if (typeof value !== 'object' || !value) {\n        throw new MongoAPIError(`Option 'mongodbLogComponentSeverities' must be a non-null object`);\n      }\n      for (const [k, v] of Object.entries(value)) {\n        if (typeof v !== 'string' || typeof k !== 'string') {\n          throw new MongoAPIError(\n            `User input for option 'mongodbLogComponentSeverities' object cannot include a non-string key or value`\n          );\n        }\n        if (!Object.values(MongoLoggableComponent).some(val => val === k) && k !== 'default') {\n          throw new MongoAPIError(\n            `User input for option 'mongodbLogComponentSeverities' contains invalid key: ${k}`\n          );\n        }\n        if (!Object.values(SeverityLevel).some(val => val === v)) {\n          throw new MongoAPIError(\n            `Option 'mongodbLogComponentSeverities' does not support ${v} as a value for ${k}`\n          );\n        }\n      }\n      return value;\n    }\n  },\n  mongodbLogMaxDocumentLength: { type: 'uint' },\n  // Custom types for modifying core behavior\n  connectionType: { type: 'any' },\n  srvPoller: { type: 'any' },\n  // Accepted Node.js Options\n  allowPartialTrustChain: { type: 'any' },\n  minDHSize: { type: 'any' },\n  pskCallback: { type: 'any' },\n  secureContext: { type: 'any' },\n  enableTrace: { type: 'any' },\n  requestCert: { type: 'any' },\n  rejectUnauthorized: { type: 'any' },\n  checkServerIdentity: { type: 'any' },\n  ALPNProtocols: { type: 'any' },\n  SNICallback: { type: 'any' },\n  session: { type: 'any' },\n  requestOCSP: { type: 'any' },\n  localAddress: { type: 'any' },\n  localPort: { type: 'any' },\n  hints: { type: 'any' },\n  lookup: { type: 'any' },\n  ca: { type: 'any' },\n  cert: { type: 'any' },\n  ciphers: { type: 'any' },\n  crl: { type: 'any' },\n  ecdhCurve: { type: 'any' },\n  key: { type: 'any' },\n  passphrase: { type: 'any' },\n  pfx: { type: 'any' },\n  secureProtocol: { type: 'any' },\n  index: { type: 'any' },\n  // Legacy options from v3 era\n  useNewUrlParser: {\n    type: 'boolean',\n    deprecated:\n      'useNewUrlParser has no effect since Node.js Driver version 4.0.0 and will be removed in the next major version'\n  } as OptionDescriptor,\n  useUnifiedTopology: {\n    type: 'boolean',\n    deprecated:\n      'useUnifiedTopology has no effect since Node.js Driver version 4.0.0 and will be removed in the next major version'\n  } as OptionDescriptor,\n  __skipPingOnConnect: { type: 'boolean' }\n} as Record<keyof MongoClientOptions, OptionDescriptor>;\n\nexport const DEFAULT_OPTIONS = new CaseInsensitiveMap(\n  Object.entries(OPTIONS)\n    .filter(([, descriptor]) => descriptor.default != null)\n    .map(([k, d]) => [k, d.default])\n);\n"], "mappings": ";;;;;;AA4EAA,OAAA,CAAAC,gBAAA,GAAAA,gBAAA;AAqKAD,OAAA,CAAAE,YAAA,GAAAA,YAAA;AAjPA,MAAAC,GAAA,GAAAC,OAAA;AACA,MAAAC,+BAAA,GAAAD,OAAA;AACA,MAAAE,KAAA,GAAAF,OAAA;AAGA,MAAAG,mBAAA,GAAAH,OAAA;AACA,MAAAI,WAAA,GAAAJ,OAAA;AACA,MAAAK,iBAAA,GAAAL,OAAA;AACA,MAAAM,aAAA,GAAAN,OAAA;AACA,MAAAO,WAAA,GAAAP,OAAA;AACA,MAAAQ,OAAA,GAAAR,OAAA;AAMA,MAAAS,cAAA,GAAAT,OAAA;AAQA,MAAAU,cAAA,GAAAV,OAAA;AACA,MAAAW,cAAA,GAAAX,OAAA;AACA,MAAAY,iBAAA,GAAAZ,OAAA;AACA,MAAAa,SAAA,GAAAb,OAAA;AAEA,MAAAc,OAAA,GAAAd,OAAA;AAUA,MAAAe,eAAA,GAAAf,OAAA;AAEA,MAAMgB,iBAAiB,GAAG,CAAC,YAAY,EAAE,YAAY,EAAE,cAAc,CAAC;AAEtE,MAAMC,oBAAoB,GAAG,kEAAkE;AAC/F,MAAMC,oBAAoB,GAAG,4DAA4D;AACzF,MAAMC,0BAA0B,GAC9B,qEAAqE;AAIvE,SAASC,kBAAkBA,CACzBC,GAAgC;EAEhC,OAAO,eAAeC,kBAAkBA,CAACC,aAAqB;IAC5D,IAAI;MACF,OAAO,MAAMxB,GAAG,CAACyB,QAAQ,CAACH,GAAG,CAAC,CAACE,aAAa,CAAC;IAC/C,CAAC,CAAC,OAAOE,aAAa,EAAE;MACtB,IAAIA,aAAa,CAACC,IAAI,KAAK3B,GAAG,CAAC4B,OAAO,EAAE;QACtC,OAAO,MAAM5B,GAAG,CAACyB,QAAQ,CAACH,GAAG,CAAC,CAACE,aAAa,CAAC;MAC/C,CAAC,MAAM;QACL,MAAME,aAAa;MACrB;IACF;EACF,CAAC;AACH;AAEA,MAAMG,UAAU,GAAGR,kBAAkB,CAAC,YAAY,CAAC;AACnD,MAAMS,UAAU,GAAGT,kBAAkB,CAAC,YAAY,CAAC;AAEnD;;;;;;;AAOO,eAAevB,gBAAgBA,CAACiC,OAAqB;EAC1D,IAAI,OAAOA,OAAO,CAACC,OAAO,KAAK,QAAQ,EAAE;IACvC,MAAM,IAAIvB,OAAA,CAAAwB,aAAa,CAAC,oCAAoC,CAAC;EAC/D;EAEA;EACA;EACA,MAAMT,aAAa,GAAGO,OAAO,CAACC,OAAO;EACrC,MAAME,oBAAoB,GAAGJ,UAAU,CAACN,aAAa,CAAC;EAEtDU,oBAAoB,CAACC,IAAI,CAACC,SAAS,EAAErB,OAAA,CAAAsB,WAAW,CAAC,CAAC,CAAC;EAEnD,MAAMC,QAAQ,GAAG,IAAIP,OAAO,CAACQ,cAAc,SAASf,aAAa,EAAE;EACnE;EACA,MAAMgB,SAAS,GAAG,MAAMX,UAAU,CAACS,QAAQ,CAAC;EAE5C,IAAIE,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;IAC1B,MAAM,IAAIhC,OAAA,CAAAwB,aAAa,CAAC,4BAA4B,CAAC;EACvD;EAEA,KAAK,MAAM;IAAES;EAAI,CAAE,IAAIF,SAAS,EAAE;IAChC,IAAAzB,OAAA,CAAA4B,sBAAsB,EAACD,IAAI,EAAElB,aAAa,CAAC;EAC7C;EAEA,MAAMoB,aAAa,GAAGJ,SAAS,CAACK,GAAG,CAACC,CAAC,IAAI/B,OAAA,CAAAgC,WAAW,CAACC,UAAU,CAAC,GAAGF,CAAC,CAACJ,IAAI,IAAII,CAAC,CAACG,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC;EAEhGC,2BAA2B,CAACN,aAAa,EAAEb,OAAO,EAAE,IAAI,CAAC;EAEzD;EACA,IAAIoB,MAAM;EACV,IAAI;IACFA,MAAM,GAAG,MAAMjB,oBAAoB;EACrC,CAAC,CAAC,OAAOkB,KAAK,EAAE;IACd,IAAIA,KAAK,CAACzB,IAAI,KAAK,SAAS,IAAIyB,KAAK,CAACzB,IAAI,KAAK,WAAW,EAAE;MAC1D,MAAMyB,KAAK;IACb;IACA,OAAOR,aAAa;EACtB;EAEA,IAAIO,MAAM,CAACV,MAAM,GAAG,CAAC,EAAE;IACrB,MAAM,IAAIhC,OAAA,CAAA4C,eAAe,CAAC,mCAAmC,CAAC;EAChE;EAEA,MAAMC,gBAAgB,GAAG,IAAInD,KAAA,CAAAoD,eAAe,CAACJ,MAAM,CAAC,CAAC,CAAC,CAACK,IAAI,CAAC,EAAE,CAAC,CAAC;EAChE,MAAMC,mBAAmB,GAAG,CAAC,GAAGH,gBAAgB,CAACI,IAAI,EAAE,CAAC;EACxD,IAAID,mBAAmB,CAACE,IAAI,CAACC,GAAG,IAAI,CAAC3C,iBAAiB,CAAC4C,QAAQ,CAACD,GAAG,CAAC,CAAC,EAAE;IACrE,MAAM,IAAInD,OAAA,CAAA4C,eAAe,CAAC,oCAAoCpC,iBAAiB,CAACuC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC;EAC/F;EAEA,IAAIvC,iBAAiB,CAAC0C,IAAI,CAACG,MAAM,IAAIR,gBAAgB,CAACS,GAAG,CAACD,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;IACzE,MAAM,IAAIrD,OAAA,CAAA4C,eAAe,CAAC,gDAAgD,CAAC;EAC7E;EAEA,MAAMW,MAAM,GAAGV,gBAAgB,CAACS,GAAG,CAAC,YAAY,CAAC,IAAI3B,SAAS;EAC9D,MAAM6B,UAAU,GAAGX,gBAAgB,CAACS,GAAG,CAAC,YAAY,CAAC,IAAI3B,SAAS;EAClE,MAAM8B,YAAY,GAAGZ,gBAAgB,CAACS,GAAG,CAAC,cAAc,CAAC,IAAI3B,SAAS;EAEtE,IACE,CAACL,OAAO,CAACoC,uBAAuB,IAChCH,MAAM,IACNjC,OAAO,CAACqC,WAAW,IACnB,CAAC/D,WAAA,CAAAgE,4BAA4B,CAACC,GAAG,CAACvC,OAAO,CAACqC,WAAW,CAACG,SAAS,CAAC,EAChE;IACAxC,OAAO,CAACqC,WAAW,GAAGhE,mBAAA,CAAAoE,gBAAgB,CAACC,KAAK,CAAC1C,OAAO,CAACqC,WAAW,EAAE;MAAEJ;IAAM,CAAE,CAAC;EAC/E;EAEA,IAAI,CAACjC,OAAO,CAAC2C,uBAAuB,IAAIT,UAAU,EAAE;IAClDlC,OAAO,CAACkC,UAAU,GAAGA,UAAU;EACjC;EAEA,IAAIC,YAAY,KAAK,MAAM,EAAE;IAC3BnC,OAAO,CAACmC,YAAY,GAAG,IAAI;EAC7B;EAEA,IAAInC,OAAO,CAACkC,UAAU,IAAIlC,OAAO,CAAC4C,WAAW,GAAG,CAAC,EAAE;IACjD,MAAM,IAAIlE,OAAA,CAAA4C,eAAe,CAAC,mDAAmD,CAAC;EAChF;EAEAH,2BAA2B,CAACN,aAAa,EAAEb,OAAO,EAAE,IAAI,CAAC;EAEzD,OAAOa,aAAa;AACtB;AAEA;;;;;;AAMA,SAASgC,eAAeA,CAACC,UAA8B;EACrD,IAAI,CAACA,UAAU,EAAE;EACjB,MAAMC,KAAK,GAAGA,CAACC,CAAS,EAAEC,CAAS,KAAI;IACrC,IAAIH,UAAU,CAACP,GAAG,CAACS,CAAC,CAAC,IAAIF,UAAU,CAACP,GAAG,CAACU,CAAC,CAAC,EAAE;MAC1C,MAAM,IAAIvE,OAAA,CAAAwB,aAAa,CAAC,QAAQ8C,CAAC,qCAAqCC,CAAC,UAAU,CAAC;IACpF;EACF,CAAC;EACDF,KAAK,CAAC,aAAa,EAAE,6BAA6B,CAAC;EACnDA,KAAK,CAAC,aAAa,EAAE,0BAA0B,CAAC;EAChDA,KAAK,CAAC,aAAa,EAAE,sCAAsC,CAAC;EAC5DA,KAAK,CAAC,aAAa,EAAE,6BAA6B,CAAC;EACnDA,KAAK,CAAC,6BAA6B,EAAE,sCAAsC,CAAC;EAC5EA,KAAK,CAAC,6BAA6B,EAAE,6BAA6B,CAAC;EACnEA,KAAK,CAAC,sCAAsC,EAAE,6BAA6B,CAAC;AAC9E;AACA,SAASG,UAAUA,CAACvC,IAAY,EAAEwC,KAAc;EAC9C,IAAI,OAAOA,KAAK,KAAK,SAAS,EAAE,OAAOA,KAAK;EAC5C,QAAQA,KAAK;IACX,KAAK,MAAM;MACT,OAAO,IAAI;IACb,KAAK,OAAO;MACV,OAAO,KAAK;IACd;MACE,MAAM,IAAIzE,OAAA,CAAA4C,eAAe,CAAC,GAAGX,IAAI,mCAAmC,CAAC;EACzE;AACF;AAEA,SAASyC,iBAAiBA,CAACzC,IAAY,EAAEwC,KAAc;EACrD,MAAME,SAAS,GAAG,IAAArE,OAAA,CAAAsE,YAAY,EAACH,KAAK,CAAC;EACrC,IAAIE,SAAS,IAAI,IAAI,EAAE;IACrB,OAAOA,SAAS;EAClB;EACA,MAAM,IAAI3E,OAAA,CAAA4C,eAAe,CAAC,YAAYX,IAAI,sCAAsCwC,KAAK,EAAE,CAAC;AAC1F;AAEA,SAASI,kBAAkBA,CAAC5C,IAAY,EAAEwC,KAAc;EACtD,MAAMK,WAAW,GAAGJ,iBAAiB,CAACzC,IAAI,EAAEwC,KAAK,CAAC;EAClD,IAAIK,WAAW,GAAG,CAAC,EAAE;IACnB,MAAM,IAAI9E,OAAA,CAAA4C,eAAe,CAAC,GAAGX,IAAI,2CAA2CwC,KAAK,EAAE,CAAC;EACtF;EACA,OAAOK,WAAW;AACpB;AAEA,UAAUC,iBAAiBA,CAACN,KAAa;EACvC,IAAIA,KAAK,KAAK,EAAE,EAAE;IAChB;EACF;EACA,MAAMO,aAAa,GAAGP,KAAK,CAACQ,KAAK,CAAC,GAAG,CAAC;EACtC,KAAK,MAAMC,QAAQ,IAAIF,aAAa,EAAE;IACpC,MAAM,CAAC7B,GAAG,EAAEsB,KAAK,CAAC,GAAGS,QAAQ,CAACD,KAAK,CAAC,OAAO,CAAC;IAC5C,IAAIR,KAAK,IAAI,IAAI,EAAE;MACjB,MAAM,IAAIzE,OAAA,CAAA4C,eAAe,CAAC,iDAAiD,CAAC;IAC9E;IAEA,MAAM,CAACO,GAAG,EAAEsB,KAAK,CAAC;EACpB;AACF;AAEA,MAAMU,kBAAgC,SAAQC,GAAkB;EAC9DC,YAAYC,OAAA,GAAgC,EAAE;IAC5C,KAAK,CAACA,OAAO,CAAClD,GAAG,CAAC,CAAC,CAACmD,CAAC,EAAEC,CAAC,CAAC,KAAK,CAACD,CAAC,CAACE,WAAW,EAAE,EAAED,CAAC,CAAC,CAAC,CAAC;EACtD;EACS3B,GAAGA,CAAC0B,CAAS;IACpB,OAAO,KAAK,CAAC1B,GAAG,CAAC0B,CAAC,CAACE,WAAW,EAAE,CAAC;EACnC;EACSnC,GAAGA,CAACiC,CAAS;IACpB,OAAO,KAAK,CAACjC,GAAG,CAACiC,CAAC,CAACE,WAAW,EAAE,CAAC;EACnC;EACSC,GAAGA,CAACH,CAAS,EAAEC,CAAM;IAC5B,OAAO,KAAK,CAACE,GAAG,CAACH,CAAC,CAACE,WAAW,EAAE,EAAED,CAAC,CAAC;EACtC;EACSG,MAAMA,CAACJ,CAAS;IACvB,OAAO,KAAK,CAACI,MAAM,CAACJ,CAAC,CAACE,WAAW,EAAE,CAAC;EACtC;;AAGF,SAAgBnG,YAAYA,CAC1BsG,GAAW,EACXC,WAAA,GAA4DlE,SAAS,EACrEL,OAAA,GAA8B,EAAE;EAEhC,IAAIuE,WAAW,IAAI,IAAI,IAAI,EAAEA,WAAW,YAAY5F,cAAA,CAAA6F,WAAW,CAAC,EAAE;IAChExE,OAAO,GAAGuE,WAAW;IACrBA,WAAW,GAAGlE,SAAS;EACzB;EAEA;EACA,IAAIL,OAAO,CAACyE,WAAW,IAAI,OAAOzE,OAAO,CAAC0E,YAAY,KAAK,SAAS,IAAI,CAAC1E,OAAO,CAAC0E,YAAY,EAAE;IAC7F,MAAM,IAAIhG,OAAA,CAAAwB,aAAa,CAAC,8DAA8D,CAAC;EACzF;EAEA,IAAIF,OAAO,CAACyE,WAAW,IAAI,OAAOzE,OAAO,CAAC2E,aAAa,KAAK,SAAS,IAAI,CAAC3E,OAAO,CAAC2E,aAAa,EAAE;IAC/F,MAAM,IAAIjG,OAAA,CAAAwB,aAAa,CAAC,8DAA8D,CAAC;EACzF;EAEA,MAAM0E,GAAG,GAAG,IAAIzG,+BAAA,CAAA0G,OAAgB,CAACP,GAAG,CAAC;EACrC,MAAM;IAAEQ,KAAK;IAAEC;EAAK,CAAE,GAAGH,GAAG;EAE5B,MAAMI,YAAY,GAAGC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;EAExCF,YAAY,CAACF,KAAK,GAAGC,KAAK,GAAG,EAAE,GAAGD,KAAK,CAAChE,GAAG,CAAC9B,OAAA,CAAAgC,WAAW,CAACC,UAAU,CAAC;EAEnE,MAAMkE,UAAU,GAAG,IAAItB,kBAAkB,EAAa;EAEtD,IAAIe,GAAG,CAACQ,QAAQ,KAAK,GAAG,IAAIR,GAAG,CAACQ,QAAQ,KAAK,EAAE,EAAE;IAC/C,MAAMC,MAAM,GAAGC,kBAAkB,CAC/BV,GAAG,CAACQ,QAAQ,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGR,GAAG,CAACQ,QAAQ,CAACG,KAAK,CAAC,CAAC,CAAC,GAAGX,GAAG,CAACQ,QAAQ,CAC/D;IACD,IAAIC,MAAM,EAAE;MACVF,UAAU,CAACf,GAAG,CAAC,QAAQ,EAAE,CAACiB,MAAM,CAAC,CAAC;IACpC;EACF;EAEA,IAAIT,GAAG,CAACY,QAAQ,KAAK,EAAE,EAAE;IACvB,MAAMC,IAAI,GAAa;MACrBD,QAAQ,EAAEF,kBAAkB,CAACV,GAAG,CAACY,QAAQ;KAC1C;IAED,IAAI,OAAOZ,GAAG,CAACc,QAAQ,KAAK,QAAQ,EAAE;MACpCD,IAAI,CAACC,QAAQ,GAAGJ,kBAAkB,CAACV,GAAG,CAACc,QAAQ,CAAC;IAClD;IAEAP,UAAU,CAACf,GAAG,CAAC,MAAM,EAAE,CAACqB,IAAI,CAAC,CAAC;EAChC;EAEA,KAAK,MAAM5D,GAAG,IAAI+C,GAAG,CAACe,YAAY,CAAChE,IAAI,EAAE,EAAE;IACzC,MAAMiE,MAAM,GAAGhB,GAAG,CAACe,YAAY,CAACE,MAAM,CAAChE,GAAG,CAAC;IAE3C,MAAMiE,oBAAoB,GAAG,qBAAqB,CAACC,IAAI,CAAClE,GAAG,CAAC;IAE5D,IAAI,CAACiE,oBAAoB,IAAIF,MAAM,CAAClF,MAAM,GAAG,CAAC,EAAE;MAC9C,MAAM,IAAIhC,OAAA,CAAAsH,yBAAyB,CACjC,eAAenE,GAAG,yDAAyD,CAC5E;IACH;IAEA,IAAI,CAACiE,oBAAoB,IAAIF,MAAM,CAAC9D,QAAQ,CAAC,EAAE,CAAC,EAAE;MAChD,MAAM,IAAIpD,OAAA,CAAAwB,aAAa,CAAC,eAAe2B,GAAG,qCAAqC,CAAC;IAClF;IAEA,IAAI,CAACsD,UAAU,CAAC5C,GAAG,CAACV,GAAG,CAAC,EAAE;MACxBsD,UAAU,CAACf,GAAG,CAACvC,GAAG,EAAE+D,MAAM,CAAC;IAC7B;EACF;EAEA,MAAMK,aAAa,GAAG,IAAIpC,kBAAkB,CAC1CoB,MAAM,CAACjB,OAAO,CAAChE,OAAO,CAAC,CAACkG,MAAM,CAAC,CAAC,GAAGhC,CAAC,CAAC,KAAKA,CAAC,IAAI,IAAI,CAAC,CACrD;EAED;EAEA,IAAIiB,UAAU,CAAC5C,GAAG,CAAC,WAAW,CAAC,EAAE;IAC/B,MAAM,IAAI7D,OAAA,CAAA4C,eAAe,CACvB,qEAAqE,CACtE;EACH;EAEA,MAAM6E,sBAAsB,GAAGhB,UAAU,CAACnD,GAAG,CAAC,yBAAyB,CAAC;EACxE,IAAImE,sBAAsB,EAAE;IAC1B,KAAK,MAAMC,QAAQ,IAAID,sBAAsB,EAAE;MAC7C,IAAI,qBAAqB,CAACJ,IAAI,CAACK,QAAkB,CAAC,EAAE;QAClD,MAAM,IAAI1H,OAAA,CAAA4C,eAAe,CACvB,gFAAgF,CACjF;MACH;IACF;EACF;EAEA,IAAI2E,aAAa,CAAC1D,GAAG,CAAC,cAAc,CAAC,EAAE;IACrC,MAAM,IAAI7D,OAAA,CAAA4C,eAAe,CAAC,gDAAgD,CAAC;EAC7E;EAEA;EAEA,MAAM+E,kBAAkB,GAAG,IAAIxC,kBAAkB,EAAa;EAE9D,MAAMyC,eAAe,GAAG,IAAIC,GAAG,CAAS,CAAC,GAAGpB,UAAU,CAACxD,IAAI,EAAE,EAAE,GAAGsE,aAAa,CAACtE,IAAI,EAAE,CAAC,CAAC;EAExF,KAAK,MAAME,GAAG,IAAIyE,eAAe,EAAE;IACjC,MAAMV,MAAM,GAAG,EAAE;IACjB,MAAMY,iBAAiB,GAAGP,aAAa,CAACjE,GAAG,CAACH,GAAG,CAAC;IAChD,IAAI2E,iBAAiB,IAAI,IAAI,EAAE;MAC7BZ,MAAM,CAACa,IAAI,CAACD,iBAAiB,CAAC;IAChC;IAEA,MAAME,SAAS,GAAGvB,UAAU,CAACnD,GAAG,CAACH,GAAG,CAAC,IAAI,EAAE;IAC3C+D,MAAM,CAACa,IAAI,CAAC,GAAGC,SAAS,CAAC;IACzBL,kBAAkB,CAACjC,GAAG,CAACvC,GAAG,EAAE+D,MAAM,CAAC;EACrC;EAEA,IAAIS,kBAAkB,CAAC9D,GAAG,CAAC,KAAK,CAAC,IAAI8D,kBAAkB,CAAC9D,GAAG,CAAC,KAAK,CAAC,EAAE;IAClE,MAAMoE,aAAa,GAAG,CAACN,kBAAkB,CAACrE,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,EACvD4E,MAAM,CAACP,kBAAkB,CAACrE,GAAG,CAAC,KAAK,CAAC,IAAI,EAAE,CAAC,CAC3ClB,GAAG,CAACoC,UAAU,CAAC2D,IAAI,CAAC,IAAI,EAAE,SAAS,CAAC,CAAC;IACxC,IAAI,IAAIN,GAAG,CAACI,aAAa,CAAC,CAACG,IAAI,KAAK,CAAC,EAAE;MACrC,MAAM,IAAIpI,OAAA,CAAA4C,eAAe,CAAC,yCAAyC,CAAC;IACtE;EACF;EAEAuB,eAAe,CAACwD,kBAAkB,CAAC;EAEnC,MAAMU,kBAAkB,GAAG,IAAA/H,OAAA,CAAAgI,aAAa,EACtCV,eAAe,EACfW,KAAK,CAACC,IAAI,CAACjC,MAAM,CAACtD,IAAI,CAAC7D,OAAA,CAAAqJ,OAAO,CAAC,CAAC,CAACrG,GAAG,CAACsG,CAAC,IAAIA,CAAC,CAACjD,WAAW,EAAE,CAAC,CAC3D;EACD,IAAI4C,kBAAkB,CAACD,IAAI,KAAK,CAAC,EAAE;IACjC,MAAMO,UAAU,GAAGN,kBAAkB,CAACD,IAAI,GAAG,CAAC,GAAG,SAAS,GAAG,QAAQ;IACrE,MAAMQ,OAAO,GAAGP,kBAAkB,CAACD,IAAI,GAAG,CAAC,GAAG,KAAK,GAAG,IAAI;IAC1D,MAAM,IAAIpI,OAAA,CAAA4C,eAAe,CACvB,GAAG+F,UAAU,IAAIJ,KAAK,CAACC,IAAI,CAACH,kBAAkB,CAAC,CAACtF,IAAI,CAAC,IAAI,CAAC,IAAI6F,OAAO,gBAAgB,CACtF;EACH;EAEA;EAEA,KAAK,MAAM,CAACzF,GAAG,EAAE0F,UAAU,CAAC,IAAItC,MAAM,CAACjB,OAAO,CAAClG,OAAA,CAAAqJ,OAAO,CAAC,EAAE;IACvD,MAAMvB,MAAM,GAAGS,kBAAkB,CAACrE,GAAG,CAACH,GAAG,CAAC;IAC1C,IAAI,CAAC+D,MAAM,IAAIA,MAAM,CAAClF,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI5C,OAAA,CAAA0J,eAAe,CAACjF,GAAG,CAACV,GAAG,CAAC,EAAE;QAC5B4F,SAAS,CAACzC,YAAY,EAAEnD,GAAG,EAAE0F,UAAU,EAAE,CAACzJ,OAAA,CAAA0J,eAAe,CAACxF,GAAG,CAACH,GAAG,CAAC,CAAC,CAAC;MACtE;IACF,CAAC,MAAM;MACL,MAAM;QAAE6F;MAAU,CAAE,GAAGH,UAAU;MACjC,IAAIG,UAAU,EAAE;QACd,MAAMC,aAAa,GAAG,OAAOD,UAAU,KAAK,QAAQ,GAAG,KAAKA,UAAU,EAAE,GAAG,EAAE;QAC7E,IAAA1I,OAAA,CAAA4I,WAAW,EAAC,GAAG/F,GAAG,0BAA0B8F,aAAa,EAAE,CAAC;MAC9D;MAEAF,SAAS,CAACzC,YAAY,EAAEnD,GAAG,EAAE0F,UAAU,EAAE3B,MAAM,CAAC;IAClD;EACF;EAEA,IAAIZ,YAAY,CAAC3C,WAAW,EAAE;IAC5B,MAAMwF,QAAQ,GAAG7C,YAAY,CAAC3C,WAAW,CAACG,SAAS,KAAKlE,WAAA,CAAAwJ,aAAa,CAACC,cAAc;IACpF,MAAMC,MAAM,GAAGhD,YAAY,CAAC3C,WAAW,CAACG,SAAS,KAAKlE,WAAA,CAAAwJ,aAAa,CAACG,YAAY;IAChF,MAAMC,KAAK,GAAGlD,YAAY,CAAC3C,WAAW,CAACG,SAAS,KAAKlE,WAAA,CAAAwJ,aAAa,CAACK,WAAW;IAC9E,MAAMC,MAAM,GAAGpD,YAAY,CAAC3C,WAAW,CAACG,SAAS,KAAKlE,WAAA,CAAAwJ,aAAa,CAACO,YAAY;IAChF,IACE,CAACR,QAAQ,IAAIG,MAAM,KACnB3B,kBAAkB,CAAC9D,GAAG,CAAC,YAAY,CAAC,IACpCyC,YAAY,CAAC3C,WAAW,CAACJ,MAAM,KAAK,WAAW,EAC/C;MACA;MACA,MAAM,IAAIvD,OAAA,CAAA4C,eAAe,CACvB,iBAAiB0D,YAAY,CAAC3C,WAAW,CAACG,SAAS,wCAAwC,CAC5F;IACH;IAEA,IACE,EAAEqF,QAAQ,IAAIG,MAAM,IAAIE,KAAK,IAAIE,MAAM,CAAC,IACxCpD,YAAY,CAACK,MAAM,IACnB,CAACgB,kBAAkB,CAAC9D,GAAG,CAAC,YAAY,CAAC,EACrC;MACA;MACA;MACAyC,YAAY,CAAC3C,WAAW,GAAGhE,mBAAA,CAAAoE,gBAAgB,CAACC,KAAK,CAACsC,YAAY,CAAC3C,WAAW,EAAE;QAC1EJ,MAAM,EAAE+C,YAAY,CAACK;OACtB,CAAC;IACJ;IAEA,IAAI6C,KAAK,IAAIlD,YAAY,CAAC3C,WAAW,CAACmD,QAAQ,IAAI,CAACR,YAAY,CAAC3C,WAAW,CAACqD,QAAQ,EAAE;MACpF,MAAM,IAAIhH,OAAA,CAAA4J,4BAA4B,CACpC,cAActD,YAAY,CAAC3C,WAAW,CAACG,SAAS,oDAAoD,CACrG;IACH;IAEAwC,YAAY,CAAC3C,WAAW,CAACkG,QAAQ,EAAE;IAEnC;IACA,IACEvD,YAAY,CAAC3C,WAAW,CAACqD,QAAQ,KAAK,EAAE,IACxCV,YAAY,CAAC3C,WAAW,CAACmD,QAAQ,KAAK,EAAE,IACxCR,YAAY,CAAC3C,WAAW,CAACG,SAAS,KAAKlE,WAAA,CAAAwJ,aAAa,CAACU,eAAe,IACpEvD,MAAM,CAACtD,IAAI,CAACqD,YAAY,CAAC3C,WAAW,CAACoG,mBAAmB,CAAC,CAAC/H,MAAM,KAAK,CAAC,EACtE;MACA,OAAOsE,YAAY,CAAC3C,WAAW;IACjC;EACF;EAEA,IAAI,CAAC2C,YAAY,CAACK,MAAM,EAAE;IACxB;IACAL,YAAY,CAACK,MAAM,GAAG,MAAM;EAC9B;EAEAlE,2BAA2B,CAAC2D,KAAK,EAAEE,YAAY,EAAED,KAAK,CAAC;EAEvD,IAAIR,WAAW,IAAIS,YAAY,CAAC0D,cAAc,EAAE;IAC9CjK,WAAA,CAAAkK,SAAS,CAACC,kBAAkB,EAAE;IAC9B5D,YAAY,CAAC6D,SAAS,GAAG,IAAIpK,WAAA,CAAAkK,SAAS,CAACpE,WAAW,EAAED,GAAG,EAAEtE,OAAO,CAAC;IACjEgF,YAAY,CAAC8D,aAAa,GAAG9D,YAAY,CAAC6D,SAAS,CAACC,aAAa;EACnE;EAEA;EAEA9D,YAAY,CAAC5C,uBAAuB,GAClC6D,aAAa,CAAC1D,GAAG,CAAC,YAAY,CAAC,IAAI4C,UAAU,CAAC5C,GAAG,CAAC,YAAY,CAAC;EACjEyC,YAAY,CAACrC,uBAAuB,GAClCsD,aAAa,CAAC1D,GAAG,CAAC,YAAY,CAAC,IAAI4C,UAAU,CAAC5C,GAAG,CAAC,YAAY,CAAC;EAEjE,IAAIwC,KAAK,EAAE;IACT;IACAC,YAAY,CAAC/E,OAAO,GAAG6E,KAAK,CAAC,CAAC,CAAC;IAE/B,IAAIE,YAAY,CAAC+D,gBAAgB,EAAE;MACjC,MAAM,IAAIrK,OAAA,CAAAwB,aAAa,CAAC,2CAA2C,CAAC;IACtE;IAEA,IAAI8E,YAAY,CAACpC,WAAW,GAAG,CAAC,IAAI,OAAOoC,YAAY,CAAC9C,UAAU,KAAK,QAAQ,EAAE;MAC/E,MAAM,IAAIxD,OAAA,CAAA4C,eAAe,CAAC,+CAA+C,CAAC;IAC5E;IAEA;IACA,MAAM0H,kBAAkB,GAAG,CAAC/C,aAAa,CAAC1D,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC4C,UAAU,CAAC5C,GAAG,CAAC,KAAK,CAAC;IAC9E,MAAM0G,kBAAkB,GAAG,CAAChD,aAAa,CAAC1D,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC4C,UAAU,CAAC5C,GAAG,CAAC,KAAK,CAAC;IAC9E,IAAIyG,kBAAkB,IAAIC,kBAAkB,EAAE;MAC5CjE,YAAY,CAACkE,GAAG,GAAG,IAAI;IACzB;EACF,CAAC,MAAM;IACL,MAAMC,uBAAuB,GAC3BhE,UAAU,CAAC5C,GAAG,CAAC,aAAa,CAAC,IAC7B0D,aAAa,CAAC1D,GAAG,CAAC,aAAa,CAAC,IAChC4C,UAAU,CAAC5C,GAAG,CAAC,gBAAgB,CAAC,IAChC0D,aAAa,CAAC1D,GAAG,CAAC,gBAAgB,CAAC;IAErC,IAAI4G,uBAAuB,EAAE;MAC3B,MAAM,IAAIzK,OAAA,CAAA4C,eAAe,CACvB,2EAA2E,CAC5E;IACH;EACF;EAEA,IAAI0D,YAAY,CAAC+D,gBAAgB,IAAI/D,YAAY,CAACF,KAAK,CAACpE,MAAM,KAAK,CAAC,EAAE;IACpE,MAAM,IAAIhC,OAAA,CAAA4C,eAAe,CAAC,mDAAmD,CAAC;EAChF;EAEA,IACE,CAAC0D,YAAY,CAACoE,SAAS,KACtBpE,YAAY,CAACqE,SAAS,IAAIrE,YAAY,CAACsE,aAAa,IAAItE,YAAY,CAACuE,aAAa,CAAC,EACpF;IACA,MAAM,IAAI7K,OAAA,CAAA4C,eAAe,CAAC,0DAA0D,CAAC;EACvF;EAEA,IACG0D,YAAY,CAACsE,aAAa,IAAI,CAACtE,YAAY,CAACuE,aAAa,IACzD,CAACvE,YAAY,CAACsE,aAAa,IAAItE,YAAY,CAACuE,aAAc,EAC3D;IACA,MAAM,IAAI7K,OAAA,CAAA4C,eAAe,CAAC,6DAA6D,CAAC;EAC1F;EAEA,MAAMkI,YAAY,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,eAAe,EAAE,eAAe,CAAC,CAAC1I,GAAG,CACnFe,GAAG,IAAIsD,UAAU,CAACnD,GAAG,CAACH,GAAG,CAAC,IAAI,EAAE,CACjC;EAED,IAAI2H,YAAY,CAAC5H,IAAI,CAAC5B,OAAO,IAAIA,OAAO,CAACU,MAAM,GAAG,CAAC,CAAC,EAAE;IACpD,MAAM,IAAIhC,OAAA,CAAA4C,eAAe,CACvB,2EAA2E,CAC5E;EACH;EAEA0D,YAAY,CAACyE,kBAAkB,GAAG7K,cAAA,CAAA8K,WAAW,CAACC,cAAc,CAC1D;IACEC,mBAAmB,EAAEC,OAAO,CAACC,GAAG,CAACF,mBAAmB;IACpDG,oBAAoB,EAAEF,OAAO,CAACC,GAAG,CAACC,oBAAoB;IACtDC,4BAA4B,EAAEH,OAAO,CAACC,GAAG,CAACE,4BAA4B;IACtEC,sBAAsB,EAAEJ,OAAO,CAACC,GAAG,CAACG,sBAAsB;IAC1DC,kBAAkB,EAAEL,OAAO,CAACC,GAAG,CAACI,kBAAkB;IAClDC,eAAe,EAAEN,OAAO,CAACC,GAAG,CAACK,eAAe;IAC5CC,+BAA+B,EAAEP,OAAO,CAACC,GAAG,CAACM,+BAA+B;IAC5EC,gBAAgB,EAAER,OAAO,CAACC,GAAG,CAACO;GAC/B,EACD;IACEC,cAAc,EAAEtF,YAAY,CAACsF,cAAc;IAC3CC,6BAA6B,EAAEvF,YAAY,CAACuF,6BAA6B;IACzEC,2BAA2B,EAAExF,YAAY,CAACwF;GAC3C,CACF;EAEDxF,YAAY,CAACyF,QAAQ,GAAG,IAAAlM,iBAAA,CAAAmM,kBAAkB,EAAC1F,YAAY,CAAC;EAExDA,YAAY,CAAC2F,gBAAgB,GAAG,IAAApM,iBAAA,CAAAqM,oBAAoB,EAAC5F,YAAY,CAACyF,QAAQ,CAAC,CAACrK,IAAI,CAC9EC,SAAS,EACTrB,OAAA,CAAAsB,WAAW,CACZ,CAAC,CAAC;EAEH,OAAO0E,YAAY;AACrB;AAEA;;;;;;;;;AASA,SAAS7D,2BAA2BA,CAClC2D,KAA+B,EAC/BE,YAA0B,EAC1B6F,KAAc;EAEd,IAAI7F,YAAY,CAAC7C,YAAY,EAAE;IAC7B,IAAI2C,KAAK,CAACpE,MAAM,GAAG,CAAC,EAAE;MACpB,MAAM,IAAIhC,OAAA,CAAA4C,eAAe,CAACnC,oBAAoB,CAAC;IACjD;IACA,IAAI6F,YAAY,CAAC9C,UAAU,EAAE;MAC3B,MAAM,IAAIxD,OAAA,CAAA4C,eAAe,CAAClC,oBAAoB,CAAC;IACjD;IACA,IAAI4F,YAAY,CAAC+D,gBAAgB,EAAE;MACjC,MAAM,IAAIrK,OAAA,CAAA4C,eAAe,CAACjC,0BAA0B,CAAC;IACvD;IAEA,IAAIwL,KAAK,IAAI7F,YAAY,CAACpC,WAAW,GAAG,CAAC,EAAE;MACzC,MAAM,IAAIlE,OAAA,CAAA4C,eAAe,CAAC,kDAAkD,CAAC;IAC/E;EACF;EACA;AACF;AAEA,SAASmG,SAASA,CAChBzC,YAAiB,EACjBnD,GAAW,EACX0F,UAA4B,EAC5B3B,MAAiB;EAEjB,MAAM;IAAEkF,MAAM;IAAEC,IAAI;IAAEC;EAAS,CAAE,GAAGzD,UAAU;EAC9C,MAAM5G,IAAI,GAAGmK,MAAM,IAAIjJ,GAAG;EAE1B,QAAQkJ,IAAI;IACV,KAAK,SAAS;MACZ/F,YAAY,CAACrE,IAAI,CAAC,GAAGuC,UAAU,CAACvC,IAAI,EAAEiF,MAAM,CAAC,CAAC,CAAC,CAAC;MAChD;IACF,KAAK,KAAK;MACRZ,YAAY,CAACrE,IAAI,CAAC,GAAGyC,iBAAiB,CAACzC,IAAI,EAAEiF,MAAM,CAAC,CAAC,CAAC,CAAC;MACvD;IACF,KAAK,MAAM;MACTZ,YAAY,CAACrE,IAAI,CAAC,GAAG4C,kBAAkB,CAAC5C,IAAI,EAAEiF,MAAM,CAAC,CAAC,CAAC,CAAC;MACxD;IACF,KAAK,QAAQ;MACX,IAAIA,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;QACrB;MACF;MACAZ,YAAY,CAACrE,IAAI,CAAC,GAAGsK,MAAM,CAACrF,MAAM,CAAC,CAAC,CAAC,CAAC;MACtC;IACF,KAAK,QAAQ;MACX,IAAI,CAAC,IAAA5G,OAAA,CAAAkM,QAAQ,EAACtF,MAAM,CAAC,CAAC,CAAC,CAAC,EAAE;QACxB,MAAM,IAAIlH,OAAA,CAAA4C,eAAe,CAAC,GAAGX,IAAI,oBAAoB,CAAC;MACxD;MACAqE,YAAY,CAACrE,IAAI,CAAC,GAAGiF,MAAM,CAAC,CAAC,CAAC;MAC9B;IACF,KAAK,KAAK;MACRZ,YAAY,CAACrE,IAAI,CAAC,GAAGiF,MAAM,CAAC,CAAC,CAAC;MAC9B;IACF;MAAS;QACP,IAAI,CAACoF,SAAS,EAAE;UACd,MAAM,IAAItM,OAAA,CAAA4C,eAAe,CAAC,oDAAoD,CAAC;QACjF;QACA,MAAM6J,cAAc,GAAGH,SAAS,CAAC;UAAErK,IAAI;UAAEX,OAAO,EAAEgF,YAAY;UAAEY;QAAM,CAAE,CAAC;QACzEZ,YAAY,CAACrE,IAAI,CAAC,GAAGwK,cAAc;QACnC;MACF;EACF;AACF;AAgBarN,OAAA,CAAAqJ,OAAO,GAAG;EACrBiE,OAAO,EAAE;IACPL,IAAI,EAAE;GACP;EACDtF,IAAI,EAAE;IACJqF,MAAM,EAAE,aAAa;IACrBE,SAASA,CAAC;MAAErK,IAAI;MAAEX,OAAO;MAAE4F,MAAM,EAAE,CAACzC,KAAK;IAAC,CAAE;MAC1C,IAAI,CAAC,IAAAnE,OAAA,CAAAkM,QAAQ,EAAC/H,KAAK,EAAE,CAAC,UAAU,EAAE,UAAU,CAAU,CAAC,EAAE;QACvD,MAAM,IAAIzE,OAAA,CAAA4C,eAAe,CACvB,GAAGX,IAAI,8DAA8D,CACtE;MACH;MACA,OAAOtC,mBAAA,CAAAoE,gBAAgB,CAACC,KAAK,CAAC1C,OAAO,CAACqC,WAAW,EAAE;QACjDmD,QAAQ,EAAErC,KAAK,CAACqC,QAAQ;QACxBE,QAAQ,EAAEvC,KAAK,CAACuC;OACjB,CAAC;IACJ;GACD;EACD2F,aAAa,EAAE;IACbP,MAAM,EAAE,aAAa;IACrBE,SAASA,CAAC;MAAEhL,OAAO;MAAE4F,MAAM,EAAE,CAACzC,KAAK;IAAC,CAAE;MACpC,MAAMmI,UAAU,GAAGrG,MAAM,CAACW,MAAM,CAACtH,WAAA,CAAAwJ,aAAa,CAAC;MAC/C,MAAM,CAACtF,SAAS,CAAC,GAAG8I,UAAU,CAACpF,MAAM,CAACqF,CAAC,IAAIA,CAAC,CAACC,KAAK,CAACC,MAAM,CAACR,MAAM,CAACS,GAAG,KAAKvI,KAAK,IAAI,EAAE,GAAG,CAAC,CAAC,CAAC;MAC1F,IAAI,CAACX,SAAS,EAAE;QACd,MAAM,IAAI9D,OAAA,CAAA4C,eAAe,CAAC,wBAAwBgK,UAAU,SAASnI,KAAK,EAAE,CAAC;MAC/E;MACA,IAAIlB,MAAM,GAAGjC,OAAO,CAACqC,WAAW,EAAEJ,MAAM;MACxC,IACEO,SAAS,KAAKlE,WAAA,CAAAwJ,aAAa,CAAC6D,aAAa,IACzCrN,WAAA,CAAAgE,4BAA4B,CAACC,GAAG,CAACC,SAAS,CAAC,EAC3C;QACA;QACAP,MAAM,GAAG,WAAW;MACtB;MAEA,IAAIyD,QAAQ,GAAG1F,OAAO,CAACqC,WAAW,EAAEqD,QAAQ;MAC5C,IAAIlD,SAAS,KAAKlE,WAAA,CAAAwJ,aAAa,CAACG,YAAY,IAAIvC,QAAQ,KAAK,EAAE,EAAE;QAC/DA,QAAQ,GAAGrF,SAAS;MACtB;MACA,OAAOhC,mBAAA,CAAAoE,gBAAgB,CAACC,KAAK,CAAC1C,OAAO,CAACqC,WAAW,EAAE;QACjDG,SAAS;QACTP,MAAM;QACNyD;OACD,CAAC;IACJ;GACD;EACD;EACA;EACA;EACAkG,uBAAuB,EAAE;IACvBd,MAAM,EAAE,aAAa;IACrBE,SAASA,CAAC;MAAEhL,OAAO;MAAE4F;IAAM,CAAE;MAC3B;MACA;MACA;MACA,IAAI6C,mBAAmB,GAAGxD,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;MAE7C,KAAK,MAAM2G,WAAW,IAAIjG,MAAM,EAAE;QAChC,IAAI,OAAOiG,WAAW,KAAK,QAAQ,EAAE;UACnC,KAAK,MAAM,CAAChK,GAAG,EAAEsB,KAAK,CAAC,IAAIM,iBAAiB,CAACoI,WAAW,CAAC,EAAE;YACzD,IAAI;cACFpD,mBAAmB,CAAC5G,GAAG,CAAC,GAAGqB,UAAU,CAACrB,GAAG,EAAEsB,KAAK,CAAC;YACnD,CAAC,CAAC,MAAM;cACNsF,mBAAmB,CAAC5G,GAAG,CAAC,GAAGsB,KAAK;YAClC;UACF;QACF,CAAC,MAAM;UACL,IAAI,CAAC,IAAAnE,OAAA,CAAAkM,QAAQ,EAACW,WAAW,CAAC,EAAE;YAC1B,MAAM,IAAInN,OAAA,CAAA4C,eAAe,CAAC,2CAA2C,CAAC;UACxE;UACAmH,mBAAmB,GAAG;YAAE,GAAGoD;UAAW,CAAE;QAC1C;MACF;MACA,OAAOxN,mBAAA,CAAAoE,gBAAgB,CAACC,KAAK,CAAC1C,OAAO,CAACqC,WAAW,EAAE;QACjDoG;OACD,CAAC;IACJ;GACD;EACDqD,UAAU,EAAE;IACVhB,MAAM,EAAE,aAAa;IACrBE,SAASA,CAAC;MAAEhL,OAAO;MAAE4F,MAAM,EAAE,CAACzC,KAAK;IAAC,CAAE;MACpC,MAAMlB,MAAM,GAAGgJ,MAAM,CAAC9H,KAAK,CAAC;MAC5B,OAAO9E,mBAAA,CAAAoE,gBAAgB,CAACC,KAAK,CAAC1C,OAAO,CAACqC,WAAW,EAAE;QAAEJ;MAAM,CAAE,CAAC;IAChE;GACD;EACDyG,cAAc,EAAE;IACdqC,IAAI,EAAE;GACP;EACDgB,gBAAgB,EAAE;IAChBhB,IAAI,EAAE,SAAS;IACflG,OAAO,EAAE;GACV;EACDmH,8BAA8B,EAAE;IAC9BjB,IAAI,EAAE;GACP;EACDkB,UAAU,EAAE;IACVlB,IAAI,EAAE;GACP;EACDmB,SAAS,EAAE;IACTpB,MAAM,EAAE,WAAW;IACnBE,SAASA,CAAC;MAAEpF,MAAM,EAAE,CAACuG,OAAO;IAAC,CAAE;MAC7B,MAAMC,mBAAmB,GACvB,OAAOD,OAAO,KAAK,QAAQ,GAAI;QAAEA;MAAO,CAAgB,GAAIA,OAAqB;MACnF,MAAME,iBAAiB,GAAGD,mBAAmB,IAAIA,mBAAmB,CAACD,OAAO;MAC5E,IAAI,CAACE,iBAAiB,EAAE;QACtB,MAAM,IAAI3N,OAAA,CAAA4C,eAAe,CACvB,qFAAqF2D,MAAM,CAACW,MAAM,CAChGjH,cAAA,CAAA2N,gBAAgB,CACjB,CAAC7K,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB;MACH;MACA,IAAI,CAACwD,MAAM,CAACW,MAAM,CAACjH,cAAA,CAAA2N,gBAAgB,CAAC,CAAC1K,IAAI,CAACsC,CAAC,IAAIA,CAAC,KAAKmI,iBAAiB,CAAC,EAAE;QACvE,MAAM,IAAI3N,OAAA,CAAA4C,eAAe,CACvB,8BAA8B+K,iBAAiB,sCAAsCpH,MAAM,CAACW,MAAM,CAChGjH,cAAA,CAAA2N,gBAAgB,CACjB,CAAC7K,IAAI,CAAC,MAAM,CAAC,IAAI,CACnB;MACH;MACA,OAAO2K,mBAAmB;IAC5B;GACD;EACDG,SAAS,EAAE;IACTxB,IAAI,EAAE;GACP;EACDyB,WAAW,EAAE;IACX3H,OAAO,EAAE,MAAM;IACfiG,MAAM,EAAE,aAAa;IACrBE,SAASA,CAAC;MAAEpF;IAAM,CAAE;MAClB,MAAM6G,eAAe,GAAG,IAAIlG,GAAG,EAAE;MACjC,KAAK,MAAMmG,OAAO,IAAI9G,MAAuC,EAAE;QAC7D,MAAM+G,YAAY,GAAG,OAAOD,OAAO,KAAK,QAAQ,GAAGA,OAAO,CAAC/I,KAAK,CAAC,GAAG,CAAC,GAAG+I,OAAO;QAC/E,IAAI,CAACzF,KAAK,CAAC2F,OAAO,CAACD,YAAY,CAAC,EAAE;UAChC,MAAM,IAAIjO,OAAA,CAAAsH,yBAAyB,CACjC,mEAAmE,CACpE;QACH;QACA,KAAK,MAAM6G,CAAC,IAAIF,YAAY,EAAE;UAC5B,IAAI1H,MAAM,CAACtD,IAAI,CAACnD,aAAA,CAAAsO,UAAU,CAAC,CAAChL,QAAQ,CAACmJ,MAAM,CAAC4B,CAAC,CAAC,CAAC,EAAE;YAC/CJ,eAAe,CAACM,GAAG,CAAC9B,MAAM,CAAC4B,CAAC,CAAC,CAAC;UAChC,CAAC,MAAM;YACL,MAAM,IAAInO,OAAA,CAAAsH,yBAAyB,CACjC,GAAG6G,CAAC,0DAA0D5H,MAAM,CAACtD,IAAI,CACvEnD,aAAA,CAAAsO,UAAU,CACX,GAAG,CACL;UACH;QACF;MACF;MACA,OAAO,CAAC,GAAGL,eAAe,CAAC;IAC7B;GACD;EACDO,gBAAgB,EAAE;IAChBnI,OAAO,EAAE,KAAK;IACdkG,IAAI,EAAE;GACP;EACD1F,MAAM,EAAE;IACN0F,IAAI,EAAE;GACP;EACDhC,gBAAgB,EAAE;IAChBlE,OAAO,EAAE,KAAK;IACdkG,IAAI,EAAE;GACP;EACDkC,UAAU,EAAE;IACVpI,OAAO,EAAE,EAAE;IACXkG,IAAI,EAAE;GACP;EACDmC,oBAAoB,EAAE;IAAEnC,IAAI,EAAE,SAAS;IAAElG,OAAO,EAAE;EAAI,CAAE;EACxDsI,MAAM,EAAE;IACNnC,SAASA,CAAC;MAAErK,IAAI;MAAEiF,MAAM,EAAE,CAACzC,KAAK;IAAC,CAAE;MACjC,MAAMgI,cAAc,GAAG/H,iBAAiB,CAACzC,IAAI,EAAEwC,KAAK,CAAC;MACrD,IAAIgI,cAAc,KAAK,CAAC,IAAIA,cAAc,KAAK,CAAC,EAAE;QAChD,OAAOA,cAAc;MACvB;MACA,MAAM,IAAIzM,OAAA,CAAA4C,eAAe,CAAC,sCAAsC6J,cAAc,GAAG,CAAC;IACpF;GACD;EACDiC,WAAW,EAAE;IACXrC,IAAI,EAAE;GACP;EACDsC,mBAAmB,EAAE;IACnBxI,OAAO,EAAE,KAAK;IACdkG,IAAI,EAAE;GACP;EACDuC,KAAK,EAAE;IACL5F,UAAU,EAAE,4BAA4B;IACxCoD,MAAM,EAAE,cAAc;IACtBE,SAASA,CAAC;MAAErK,IAAI;MAAEX,OAAO;MAAE4F,MAAM,EAAE,CAACzC,KAAK;IAAC,CAAE;MAC1C,MAAMoK,EAAE,GAAGtO,eAAA,CAAAuO,YAAY,CAACC,WAAW,CAAC;QAClCC,YAAY,EAAE;UACZ,GAAG1N,OAAO,CAAC0N,YAAY;UACvBJ,KAAK,EAAEpK,UAAU,CAACvC,IAAI,EAAEwC,KAAK;;OAEhC,CAAC;MACF,IAAI,CAACoK,EAAE,EAAE,MAAM,IAAI7O,OAAA,CAAA4C,eAAe,CAAC,4CAA4C6B,KAAK,EAAE,CAAC;MACvF,OAAOoK,EAAE;IACX;GACmB;EACrBI,oBAAoB,EAAE;IACpB9I,OAAO,EAAE,KAAK;IACdkG,IAAI,EAAE;GACP;EACD6C,eAAe,EAAE;IACf7C,IAAI,EAAE;GACP;EACD8C,CAAC,EAAE;IACDnG,UAAU,EAAE,4BAA4B;IACxCoD,MAAM,EAAE,cAAc;IACtBE,SAASA,CAAC;MAAErK,IAAI;MAAEX,OAAO;MAAE4F,MAAM,EAAE,CAACzC,KAAK;IAAC,CAAE;MAC1C,MAAMoK,EAAE,GAAGtO,eAAA,CAAAuO,YAAY,CAACC,WAAW,CAAC;QAClCC,YAAY,EAAE;UACZ,GAAG1N,OAAO,CAAC0N,YAAY;UACvBI,OAAO,EAAE5K,UAAU,CAACvC,IAAI,EAAEwC,KAAK;;OAElC,CAAC;MACF,IAAI,CAACoK,EAAE,EAAE,MAAM,IAAI7O,OAAA,CAAA4C,eAAe,CAAC,8CAA8C6B,KAAK,EAAE,CAAC;MACzF,OAAOoK,EAAE;IACX;GACmB;EACrBO,OAAO,EAAE;IACPhD,MAAM,EAAE,cAAc;IACtBE,SAASA,CAAC;MAAErK,IAAI;MAAEX,OAAO;MAAE4F,MAAM,EAAE,CAACzC,KAAK;IAAC,CAAE;MAC1C,MAAMoK,EAAE,GAAGtO,eAAA,CAAAuO,YAAY,CAACC,WAAW,CAAC;QAClCC,YAAY,EAAE;UACZ,GAAG1N,OAAO,CAAC0N,YAAY;UACvBI,OAAO,EAAE5K,UAAU,CAACvC,IAAI,EAAEwC,KAAK;;OAElC,CAAC;MACF,IAAI,CAACoK,EAAE,EAAE,MAAM,IAAI7O,OAAA,CAAA4C,eAAe,CAAC,8CAA8C6B,KAAK,EAAE,CAAC;MACzF,OAAOoK,EAAE;IACX;GACD;EACDpL,YAAY,EAAE;IACZ0C,OAAO,EAAE,KAAK;IACdkG,IAAI,EAAE;GACP;EACDgD,gBAAgB,EAAE;IAChBlJ,OAAO,EAAE,EAAE;IACXkG,IAAI,EAAE;GACP;EACDiD,aAAa,EAAE;IACbnJ,OAAO,EAAE,CAAC;IACVmG,SAASA,CAAC;MAAErK,IAAI;MAAEiF,MAAM,EAAE,CAACzC,KAAK;IAAC,CAAE;MACjC,MAAM6K,aAAa,GAAGzK,kBAAkB,CAAC5C,IAAI,EAAEwC,KAAK,CAAC;MACrD,IAAI6K,aAAa,KAAK,CAAC,EAAE;QACvB,MAAM,IAAItP,OAAA,CAAAsH,yBAAyB,CAAC,wCAAwC,CAAC;MAC/E;MACA,OAAOgI,aAAa;IACtB;GACD;EACDC,aAAa,EAAE;IACbpJ,OAAO,EAAE,CAAC;IACVkG,IAAI,EAAE;GACP;EACDmD,WAAW,EAAE;IACXrJ,OAAO,EAAE,GAAG;IACZkG,IAAI,EAAE;GACP;EACDoD,mBAAmB,EAAE;IACnBrD,MAAM,EAAE,gBAAgB;IACxBE,SAASA,CAAC;MAAErK,IAAI;MAAEX,OAAO;MAAE4F,MAAM,EAAE,CAACzC,KAAK;IAAC,CAAE;MAC1C,MAAMgL,mBAAmB,GAAG5K,kBAAkB,CAAC5C,IAAI,EAAEwC,KAAK,CAAC;MAC3D,IAAInD,OAAO,CAACoO,cAAc,EAAE;QAC1B,OAAOtP,iBAAA,CAAAuP,cAAc,CAACZ,WAAW,CAAC;UAChCW,cAAc,EAAE;YAAE,GAAGpO,OAAO,CAACoO,cAAc;YAAED;UAAmB;SACjE,CAAC;MACJ,CAAC,MAAM;QACL,OAAO,IAAIrP,iBAAA,CAAAuP,cAAc,CAAC,WAAW,EAAEhO,SAAS,EAAE;UAAE8N;QAAmB,CAAE,CAAC;MAC5E;IACF;GACD;EACDG,qBAAqB,EAAE;IACrBvD,IAAI,EAAE;GACP;EACDwD,WAAW,EAAE;IACX1J,OAAO,EAAE,CAAC;IACVkG,IAAI,EAAE;GACP;EACDyD,uBAAuB,EAAE;IACvB3J,OAAO,EAAE,GAAG;IACZkG,IAAI,EAAE;GACP;EACD0D,eAAe,EAAE;IACf5J,OAAO,EAAE,KAAK;IACdkG,IAAI,EAAE;GACP;EACDpK,IAAI,EAAE;IACJmK,MAAM,EAAE,YAAY;IACpBE,SAASA,CAAC;MAAEpF,MAAM,EAAE,CAACzC,KAAK,CAAC;MAAEnD;IAAO,CAAE;MACpC,OAAO;QAAE,GAAGA,OAAO,CAACiN,UAAU;QAAEtM,IAAI,EAAEsK,MAAM,CAAC9H,KAAK;MAAC,CAAE;IACvD;GACmB;EACrBuL,OAAO,EAAE;IACP7J,OAAO,EAAE,IAAI;IACbkG,IAAI,EAAE;GACP;EACD4D,SAAS,EAAE;IACT9J,OAAO,EAAE7F,OAAA,CAAA4P,kBAAkB;IAC3B5D,SAASA,CAAC;MAAEpF,MAAM,EAAE,CAACzC,KAAK;IAAC,CAAE;MAC3B,IAAI,IAAAnE,OAAA,CAAAkM,QAAQ,EAAC/H,KAAK,EAAE,CAAC,UAAU,CAAU,CAAC,IAAI,OAAOA,KAAK,CAAC0L,QAAQ,KAAK,UAAU,EAAE;QAClF,OAAO1L,KAAkB;MAC3B;MACA,MAAM,IAAIzE,OAAA,CAAA4C,eAAe,CACvB,oEAAoE6B,KAAK,EAAE,CAC5E;IACH;GACD;EACD2L,cAAc,EAAE;IACd/D,IAAI,EAAE;GACP;EACDrG,YAAY,EAAE;IACZqG,IAAI,EAAE;GACP;EACDpG,aAAa,EAAE;IACboG,IAAI,EAAE;GACP;EACDtG,WAAW,EAAE;IACXsG,IAAI,EAAE;GACP;EACD3B,SAAS,EAAE;IACT2B,IAAI,EAAE;GACP;EACDxB,aAAa,EAAE;IACbwB,IAAI,EAAE;GACP;EACD1B,SAAS,EAAE;IACT0B,IAAI,EAAE;GACP;EACDzB,aAAa,EAAE;IACbyB,IAAI,EAAE;GACP;EACDW,GAAG,EAAE;IACH7G,OAAO,EAAE,KAAK;IACdkG,IAAI,EAAE;GACP;EACDgE,WAAW,EAAE;IACX/D,SAASA,CAAC;MAAEpF,MAAM,EAAE,CAACzC,KAAK,CAAC;MAAEnD;IAAO,CAAE;MACpC,IAAImD,KAAK,YAAYtE,cAAA,CAAAmQ,WAAW,IAAI,IAAAhQ,OAAA,CAAAkM,QAAQ,EAAC/H,KAAK,EAAE,CAAC,OAAO,CAAU,CAAC,EAAE;QACvE,OAAOtE,cAAA,CAAAmQ,WAAW,CAACvB,WAAW,CAAC;UAAE,GAAGzN,OAAO,CAAC+O,WAAW;UAAE,GAAG5L;QAAK,CAAS,CAAC;MAC7E;MACA,MAAM,IAAIzE,OAAA,CAAA4C,eAAe,CAAC,sCAAsC2N,IAAI,CAACC,SAAS,CAAC/L,KAAK,CAAC,EAAE,CAAC;IAC1F;GACD;EACDgM,gBAAgB,EAAE;IAChBrE,MAAM,EAAE,aAAa;IACrBE,SAASA,CAAC;MAAEpF,MAAM,EAAE,CAACwJ,KAAK,CAAC;MAAEpP;IAAO,CAAE;MACpC,OAAOnB,cAAA,CAAAmQ,WAAW,CAACvB,WAAW,CAAC;QAC7B,GAAGzN,OAAO,CAAC+O,WAAW;QACtBK,KAAK,EAAEA;OACR,CAAC;IACJ;GACD;EACDhB,cAAc,EAAE;IACdvJ,OAAO,EAAE/F,iBAAA,CAAAuP,cAAc,CAACgB,OAAO;IAC/BrE,SAASA,CAAC;MAAEpF,MAAM,EAAE,CAACzC,KAAK,CAAC;MAAEnD;IAAO,CAAE;MACpC,IAAImD,KAAK,YAAYrE,iBAAA,CAAAuP,cAAc,EAAE;QACnC,OAAOvP,iBAAA,CAAAuP,cAAc,CAACZ,WAAW,CAAC;UAChCW,cAAc,EAAE;YAAE,GAAGpO,OAAO,CAACoO,cAAc;YAAE,GAAGjL;UAAK,CAAE;UACvD,GAAGA;SACG,CAAC;MACX;MACA,IAAI,IAAAnE,OAAA,CAAAkM,QAAQ,EAAC/H,KAAK,EAAE,CAAC,MAAM,CAAU,CAAC,EAAE;QACtC,MAAMmM,EAAE,GAAGxQ,iBAAA,CAAAuP,cAAc,CAACZ,WAAW,CAAC;UACpCW,cAAc,EAAE;YAAE,GAAGpO,OAAO,CAACoO,cAAc;YAAE,GAAGjL;UAAK,CAAE;UACvD,GAAGA;SACG,CAAC;QACT,IAAImM,EAAE,EAAE,OAAOA,EAAE,CAAC,KACb,MAAM,IAAI5Q,OAAA,CAAA4C,eAAe,CAAC,oCAAoC2N,IAAI,CAACC,SAAS,CAAC/L,KAAK,CAAC,EAAE,CAAC;MAC7F;MACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAMoM,MAAM,GAAG;UACbC,KAAK,EAAExP,OAAO,CAACoO,cAAc,EAAEoB,KAAK;UACpCrB,mBAAmB,EAAEnO,OAAO,CAACoO,cAAc,EAAED;SAC9C;QACD,OAAO,IAAIrP,iBAAA,CAAAuP,cAAc,CACvBlL,KAA2B,EAC3BnD,OAAO,CAACoO,cAAc,EAAEqB,IAAI,EAC5BF,MAAM,CACP;MACH;MACA,MAAM,IAAI7Q,OAAA,CAAA4C,eAAe,CAAC,iCAAiC6B,KAAK,EAAE,CAAC;IACrE;GACD;EACDuM,kBAAkB,EAAE;IAClB5E,MAAM,EAAE,gBAAgB;IACxBE,SAASA,CAAC;MACRpF,MAAM;MACN5F;IAAO,CAIR;MACC,MAAMyP,IAAI,GAA2CxI,KAAK,CAAC2F,OAAO,CAAChH,MAAM,CAAC,CAAC,CAAC,CAAC,GACzEA,MAAM,CAAC,CAAC,CAAC,GACRA,MAAwB;MAC7B,MAAM8J,kBAAkB,GAAG,EAAE;MAC7B,KAAK,MAAMC,GAAG,IAAIF,IAAI,EAAE;QACtB,MAAMG,iBAAiB,GAAW3K,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;QACrD,IAAI,OAAOyK,GAAG,KAAK,QAAQ,EAAE;UAC3B,KAAK,MAAM,CAAC1L,CAAC,EAAEC,CAAC,CAAC,IAAIT,iBAAiB,CAACkM,GAAG,CAAC,EAAE;YAC3CC,iBAAiB,CAAC3L,CAAC,CAAC,GAAGC,CAAC;UAC1B;QACF;QACA,IAAI,IAAAlF,OAAA,CAAAkM,QAAQ,EAACyE,GAAG,CAAC,EAAE;UACjB,KAAK,MAAM,CAAC1L,CAAC,EAAEC,CAAC,CAAC,IAAIe,MAAM,CAACjB,OAAO,CAAC2L,GAAG,CAAC,EAAE;YACxCC,iBAAiB,CAAC3L,CAAC,CAAC,GAAGC,CAAC;UAC1B;QACF;QACAwL,kBAAkB,CAACjJ,IAAI,CAACmJ,iBAAiB,CAAC;MAC5C;MACA,OAAO9Q,iBAAA,CAAAuP,cAAc,CAACZ,WAAW,CAAC;QAChCW,cAAc,EAAEpO,OAAO,CAACoO,cAAc;QACtCsB;OACD,CAAC;IACJ;GACD;EACDxN,UAAU,EAAE;IACV6I,IAAI,EAAE;GACP;EACD8E,UAAU,EAAE;IACVhL,OAAO,EAAE,IAAI;IACbkG,IAAI,EAAE;GACP;EACD+E,WAAW,EAAE;IACXjL,OAAO,EAAE,IAAI;IACbkG,IAAI,EAAE;GACP;EACDgF,kBAAkB,EAAE;IAClBhF,IAAI,EAAE;GACP;EACDiF,oBAAoB,EAAE;IACpBnL,OAAO,EAAE,MAAM;IACfmG,SAASA,CAAC;MAAEpF,MAAM,EAAE,CAACzC,KAAK;IAAC,CAAE;MAC3B,IAAI,CAAC8B,MAAM,CAACW,MAAM,CAAC7G,SAAA,CAAAkR,oBAAoB,CAAC,CAACnO,QAAQ,CAACqB,KAAY,CAAC,EAAE;QAC/D,MAAM,IAAIzE,OAAA,CAAA4C,eAAe,CACvB,iEAAiE,CAClE;MACH;MACA,OAAO6B,KAAK;IACd;GACD;EACD+M,wBAAwB,EAAE;IACxBrL,OAAO,EAAE,KAAK;IACdkG,IAAI,EAAE;GACP;EACDoF,UAAU,EAAE;IACVpF,IAAI,EAAE;GACP;EACDqF,eAAe,EAAE;IACf;IACAvL,OAAO,EAAE,CAAC;IACVkG,IAAI,EAAE;GACP;EACDnI,WAAW,EAAE;IACXmI,IAAI,EAAE,MAAM;IACZlG,OAAO,EAAE;GACV;EACDrE,cAAc,EAAE;IACduK,IAAI,EAAE,QAAQ;IACdlG,OAAO,EAAE;GACV;EACDwL,GAAG,EAAE;IACHvF,MAAM,EAAE,KAAK;IACbC,IAAI,EAAE;GACP;EACDuF,SAAS,EAAE;IACTvF,IAAI,EAAE;GACP;EACD7B,GAAG,EAAE;IACH6B,IAAI,EAAE;GACP;EACDwF,2BAA2B,EAAE;IAC3BzF,MAAM,EAAE,oBAAoB;IAC5BE,SAASA,CAAC;MAAErK,IAAI;MAAEiF,MAAM,EAAE,CAACzC,KAAK;IAAC,CAAE;MACjC;MACA,OAAO,CAACD,UAAU,CAACvC,IAAI,EAAEwC,KAAK,CAAC;IACjC;GACD;EACDqN,wBAAwB,EAAE;IACxB1F,MAAM,EAAE,qBAAqB;IAC7BE,SAASA,CAAC;MAAErK,IAAI;MAAEiF,MAAM,EAAE,CAACzC,KAAK;IAAC,CAAE;MACjC;MACA,OAAOD,UAAU,CAACvC,IAAI,EAAEwC,KAAK,CAAC,GAAG,MAAM9C,SAAS,GAAGA,SAAS;IAC9D;GACD;EACDoQ,SAAS,EAAE;IACT1F,IAAI,EAAE;GACP;EACD2F,UAAU,EAAE;IACV3F,IAAI,EAAE;GACP;EACD4F,qBAAqB,EAAE;IACrB5F,IAAI,EAAE;GACP;EACD6F,6BAA6B,EAAE;IAC7B9F,MAAM,EAAE,YAAY;IACpBC,IAAI,EAAE;GACP;EACD8F,WAAW,EAAE;IACX7F,SAASA,CAAC;MAAErK,IAAI;MAAEX,OAAO;MAAE4F,MAAM,EAAE,CAACzC,KAAK;IAAC,CAAE;MAC1C,MAAM0N,WAAW,GAAG3N,UAAU,CAACvC,IAAI,EAAEwC,KAAK,CAAC;MAC3C,IAAI0N,WAAW,EAAE;QACf7Q,OAAO,CAAC8Q,mBAAmB,GAAG,MAAMzQ,SAAS;QAC7CL,OAAO,CAAC+Q,kBAAkB,GAAG,KAAK;MACpC,CAAC,MAAM;QACL/Q,OAAO,CAAC8Q,mBAAmB,GAAG9Q,OAAO,CAACwQ,wBAAwB,GAC1D,MAAMnQ,SAAS,GACfA,SAAS;QACbL,OAAO,CAAC+Q,kBAAkB,GAAG/Q,OAAO,CAACuQ,2BAA2B,GAAG,KAAK,GAAG,IAAI;MACjF;MACA,OAAOM,WAAW;IACpB;GACD;EACDG,CAAC,EAAE;IACDlG,MAAM,EAAE,cAAc;IACtBE,SAASA,CAAC;MAAEpF,MAAM,EAAE,CAACzC,KAAK,CAAC;MAAEnD;IAAO,CAAE;MACpC,OAAOf,eAAA,CAAAuO,YAAY,CAACC,WAAW,CAAC;QAAEC,YAAY,EAAE;UAAE,GAAG1N,OAAO,CAAC0N,YAAY;UAAEsD,CAAC,EAAE7N;QAAU;MAAE,CAAE,CAAC;IAC/F;GACD;EACD8N,kBAAkB,EAAE;IAClB;IACApM,OAAO,EAAE,CAAC;IACVkG,IAAI,EAAE;GACP;EACD2C,YAAY,EAAE;IACZ5C,MAAM,EAAE,cAAc;IACtBE,SAASA,CAAC;MAAEpF,MAAM,EAAE,CAACzC,KAAK,CAAC;MAAEnD;IAAO,CAAE;MACpC,IAAI,IAAAhB,OAAA,CAAAkM,QAAQ,EAAC/H,KAAK,CAAC,IAAIA,KAAK,YAAYlE,eAAA,CAAAuO,YAAY,EAAE;QACpD,OAAOvO,eAAA,CAAAuO,YAAY,CAACC,WAAW,CAAC;UAC9BC,YAAY,EAAE;YACZ,GAAG1N,OAAO,CAAC0N,YAAY;YACvB,GAAGvK;;SAEN,CAAC;MACJ,CAAC,MAAM,IAAIA,KAAK,KAAK,UAAU,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC5D,OAAOlE,eAAA,CAAAuO,YAAY,CAACC,WAAW,CAAC;UAC9BC,YAAY,EAAE;YACZ,GAAG1N,OAAO,CAAC0N,YAAY;YACvBsD,CAAC,EAAE7N;;SAEN,CAAC;MACJ;MAEA,MAAM,IAAIzE,OAAA,CAAA4C,eAAe,CAAC,sCAAsC2N,IAAI,CAACC,SAAS,CAAC/L,KAAK,CAAC,EAAE,CAAC;IAC1F;GACD;EACD+N,QAAQ,EAAE;IACRxJ,UAAU,EAAE,+BAA+B;IAC3CoD,MAAM,EAAE,cAAc;IACtBE,SAASA,CAAC;MAAEpF,MAAM,EAAE,CAACzC,KAAK,CAAC;MAAEnD;IAAO,CAAE;MACpC,MAAMuN,EAAE,GAAGtO,eAAA,CAAAuO,YAAY,CAACC,WAAW,CAAC;QAClCC,YAAY,EAAE;UACZ,GAAG1N,OAAO,CAAC0N,YAAY;UACvBwD,QAAQ,EAAE3N,kBAAkB,CAAC,UAAU,EAAEJ,KAAK;;OAEjD,CAAC;MACF,IAAIoK,EAAE,EAAE,OAAOA,EAAE;MACjB,MAAM,IAAI7O,OAAA,CAAA4C,eAAe,CAAC,wCAAwC,CAAC;IACrE;GACmB;EACrB6P,UAAU,EAAE;IACVrG,MAAM,EAAE,cAAc;IACtBE,SAASA,CAAC;MAAEpF,MAAM,EAAE,CAACzC,KAAK,CAAC;MAAEnD;IAAO,CAAE;MACpC,MAAMuN,EAAE,GAAGtO,eAAA,CAAAuO,YAAY,CAACC,WAAW,CAAC;QAClCC,YAAY,EAAE;UACZ,GAAG1N,OAAO,CAAC0N,YAAY;UACvByD,UAAU,EAAE5N,kBAAkB,CAAC,YAAY,EAAEJ,KAAK;;OAErD,CAAC;MACF,IAAIoK,EAAE,EAAE,OAAOA,EAAE;MACjB,MAAM,IAAI7O,OAAA,CAAA4C,eAAe,CAAC,wCAAwC,CAAC;IACrE;GACD;EACD8P,oBAAoB,EAAE;IACpBvM,OAAO,EAAE,CAAC;IACVkG,IAAI,EAAE;GACP;EACDT,cAAc,EAAE;IACdU,SAASA,CAAC;MAAEpF,MAAM,EAAE,CAACzC,KAAK;IAAC,CAAE;MAC3B,IACE,EACG,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAACrB,QAAQ,CAACqB,KAAK,CAAC,IACjEA,KAAK,IACJ,OAAOA,KAAK,KAAK,QAAQ,IACzB,OAAO,IAAIA,KAAK,IAChB,OAAOA,KAAK,CAACkO,KAAK,KAAK,UAAW,CACrC,EACD;QACA,MAAM,IAAI3S,OAAA,CAAAwB,aAAa,CACrB,kFAAkF,CACnF;MACH;MACA,OAAOiD,KAAK;IACd;GACD;EACDoH,6BAA6B,EAAE;IAC7BS,SAASA,CAAC;MAAEpF,MAAM,EAAE,CAACzC,KAAK;IAAC,CAAE;MAC3B,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACA,KAAK,EAAE;QACvC,MAAM,IAAIzE,OAAA,CAAAwB,aAAa,CAAC,kEAAkE,CAAC;MAC7F;MACA,KAAK,MAAM,CAAC+D,CAAC,EAAEC,CAAC,CAAC,IAAIe,MAAM,CAACjB,OAAO,CAACb,KAAK,CAAC,EAAE;QAC1C,IAAI,OAAOe,CAAC,KAAK,QAAQ,IAAI,OAAOD,CAAC,KAAK,QAAQ,EAAE;UAClD,MAAM,IAAIvF,OAAA,CAAAwB,aAAa,CACrB,uGAAuG,CACxG;QACH;QACA,IAAI,CAAC+E,MAAM,CAACW,MAAM,CAAChH,cAAA,CAAA0S,sBAAsB,CAAC,CAAC1P,IAAI,CAAC2P,GAAG,IAAIA,GAAG,KAAKtN,CAAC,CAAC,IAAIA,CAAC,KAAK,SAAS,EAAE;UACpF,MAAM,IAAIvF,OAAA,CAAAwB,aAAa,CACrB,+EAA+E+D,CAAC,EAAE,CACnF;QACH;QACA,IAAI,CAACgB,MAAM,CAACW,MAAM,CAAChH,cAAA,CAAA4S,aAAa,CAAC,CAAC5P,IAAI,CAAC2P,GAAG,IAAIA,GAAG,KAAKrN,CAAC,CAAC,EAAE;UACxD,MAAM,IAAIxF,OAAA,CAAAwB,aAAa,CACrB,2DAA2DgE,CAAC,mBAAmBD,CAAC,EAAE,CACnF;QACH;MACF;MACA,OAAOd,KAAK;IACd;GACD;EACDqH,2BAA2B,EAAE;IAAEO,IAAI,EAAE;EAAM,CAAE;EAC7C;EACA0G,cAAc,EAAE;IAAE1G,IAAI,EAAE;EAAK,CAAE;EAC/B2G,SAAS,EAAE;IAAE3G,IAAI,EAAE;EAAK,CAAE;EAC1B;EACA4G,sBAAsB,EAAE;IAAE5G,IAAI,EAAE;EAAK,CAAE;EACvC6G,SAAS,EAAE;IAAE7G,IAAI,EAAE;EAAK,CAAE;EAC1B8G,WAAW,EAAE;IAAE9G,IAAI,EAAE;EAAK,CAAE;EAC5B+G,aAAa,EAAE;IAAE/G,IAAI,EAAE;EAAK,CAAE;EAC9BgH,WAAW,EAAE;IAAEhH,IAAI,EAAE;EAAK,CAAE;EAC5BiH,WAAW,EAAE;IAAEjH,IAAI,EAAE;EAAK,CAAE;EAC5BgG,kBAAkB,EAAE;IAAEhG,IAAI,EAAE;EAAK,CAAE;EACnC+F,mBAAmB,EAAE;IAAE/F,IAAI,EAAE;EAAK,CAAE;EACpCkH,aAAa,EAAE;IAAElH,IAAI,EAAE;EAAK,CAAE;EAC9BmH,WAAW,EAAE;IAAEnH,IAAI,EAAE;EAAK,CAAE;EAC5BoH,OAAO,EAAE;IAAEpH,IAAI,EAAE;EAAK,CAAE;EACxBqH,WAAW,EAAE;IAAErH,IAAI,EAAE;EAAK,CAAE;EAC5BsH,YAAY,EAAE;IAAEtH,IAAI,EAAE;EAAK,CAAE;EAC7BuH,SAAS,EAAE;IAAEvH,IAAI,EAAE;EAAK,CAAE;EAC1BwH,KAAK,EAAE;IAAExH,IAAI,EAAE;EAAK,CAAE;EACtByH,MAAM,EAAE;IAAEzH,IAAI,EAAE;EAAK,CAAE;EACvB0H,EAAE,EAAE;IAAE1H,IAAI,EAAE;EAAK,CAAE;EACnB2H,IAAI,EAAE;IAAE3H,IAAI,EAAE;EAAK,CAAE;EACrB4H,OAAO,EAAE;IAAE5H,IAAI,EAAE;EAAK,CAAE;EACxB6H,GAAG,EAAE;IAAE7H,IAAI,EAAE;EAAK,CAAE;EACpB8H,SAAS,EAAE;IAAE9H,IAAI,EAAE;EAAK,CAAE;EAC1BlJ,GAAG,EAAE;IAAEkJ,IAAI,EAAE;EAAK,CAAE;EACpB+H,UAAU,EAAE;IAAE/H,IAAI,EAAE;EAAK,CAAE;EAC3BgI,GAAG,EAAE;IAAEhI,IAAI,EAAE;EAAK,CAAE;EACpBiI,cAAc,EAAE;IAAEjI,IAAI,EAAE;EAAK,CAAE;EAC/BkI,KAAK,EAAE;IAAElI,IAAI,EAAE;EAAK,CAAE;EACtB;EACAmI,eAAe,EAAE;IACfnI,IAAI,EAAE,SAAS;IACfrD,UAAU,EACR;GACiB;EACrByL,kBAAkB,EAAE;IAClBpI,IAAI,EAAE,SAAS;IACfrD,UAAU,EACR;GACiB;EACrB0L,mBAAmB,EAAE;IAAErI,IAAI,EAAE;EAAS;CACe;AAE1CjN,OAAA,CAAA0J,eAAe,GAAG,IAAI3D,kBAAkB,CACnDoB,MAAM,CAACjB,OAAO,CAAClG,OAAA,CAAAqJ,OAAO,CAAC,CACpBjB,MAAM,CAAC,CAAC,GAAGqB,UAAU,CAAC,KAAKA,UAAU,CAAC1C,OAAO,IAAI,IAAI,CAAC,CACtD/D,GAAG,CAAC,CAAC,CAACmD,CAAC,EAAEoP,CAAC,CAAC,KAAK,CAACpP,CAAC,EAAEoP,CAAC,CAACxO,OAAO,CAAC,CAAC,CACnC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}