{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.WaitQueueTimeoutError = exports.PoolClearedOnNetworkError = exports.PoolClearedError = exports.PoolClosedError = void 0;\nconst error_1 = require(\"../error\");\n/**\n * An error indicating a connection pool is closed\n * @category Error\n */\nclass PoolClosedError extends error_1.MongoDriverError {\n  /**\n   * **Do not use this constructor!**\n   *\n   * Meant for internal use only.\n   *\n   * @remarks\n   * This class is only meant to be constructed within the driver. This constructor is\n   * not subject to semantic versioning compatibility guarantees and may change at any time.\n   *\n   * @public\n   **/\n  constructor(pool) {\n    super('Attempted to check out a connection from closed connection pool');\n    this.address = pool.address;\n  }\n  get name() {\n    return 'MongoPoolClosedError';\n  }\n}\nexports.PoolClosedError = PoolClosedError;\n/**\n * An error indicating a connection pool is currently paused\n * @category Error\n */\nclass PoolClearedError extends error_1.MongoNetworkError {\n  /**\n   * **Do not use this constructor!**\n   *\n   * Meant for internal use only.\n   *\n   * @remarks\n   * This class is only meant to be constructed within the driver. This constructor is\n   * not subject to semantic versioning compatibility guarantees and may change at any time.\n   *\n   * @public\n   **/\n  constructor(pool, message) {\n    const errorMessage = message ? message : `Connection pool for ${pool.address} was cleared because another operation failed with: \"${pool.serverError?.message}\"`;\n    super(errorMessage, pool.serverError ? {\n      cause: pool.serverError\n    } : undefined);\n    this.address = pool.address;\n    this.addErrorLabel(error_1.MongoErrorLabel.PoolRequstedRetry);\n  }\n  get name() {\n    return 'MongoPoolClearedError';\n  }\n}\nexports.PoolClearedError = PoolClearedError;\n/**\n * An error indicating that a connection pool has been cleared after the monitor for that server timed out.\n * @category Error\n */\nclass PoolClearedOnNetworkError extends PoolClearedError {\n  /**\n   * **Do not use this constructor!**\n   *\n   * Meant for internal use only.\n   *\n   * @remarks\n   * This class is only meant to be constructed within the driver. This constructor is\n   * not subject to semantic versioning compatibility guarantees and may change at any time.\n   *\n   * @public\n   **/\n  constructor(pool) {\n    super(pool, `Connection to ${pool.address} interrupted due to server monitor timeout`);\n  }\n  get name() {\n    return 'PoolClearedOnNetworkError';\n  }\n}\nexports.PoolClearedOnNetworkError = PoolClearedOnNetworkError;\n/**\n * An error thrown when a request to check out a connection times out\n * @category Error\n */\nclass WaitQueueTimeoutError extends error_1.MongoDriverError {\n  /**\n   * **Do not use this constructor!**\n   *\n   * Meant for internal use only.\n   *\n   * @remarks\n   * This class is only meant to be constructed within the driver. This constructor is\n   * not subject to semantic versioning compatibility guarantees and may change at any time.\n   *\n   * @public\n   **/\n  constructor(message, address) {\n    super(message);\n    this.address = address;\n  }\n  get name() {\n    return 'MongoWaitQueueTimeoutError';\n  }\n}\nexports.WaitQueueTimeoutError = WaitQueueTimeoutError;", "map": {"version": 3, "names": ["error_1", "require", "PoolClosedError", "MongoDriverError", "constructor", "pool", "address", "name", "exports", "PoolClearedError", "MongoNetworkError", "message", "errorMessage", "serverError", "cause", "undefined", "addErrorLabel", "MongoErrorLabel", "PoolRequstedRetry", "PoolClearedOnNetworkError", "WaitQueueTimeoutError"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\errors.ts"], "sourcesContent": ["import { MongoDriverError, Mongo<PERSON>rror<PERSON><PERSON><PERSON>, MongoNetworkError } from '../error';\nimport type { ConnectionPool } from './connection_pool';\n\n/**\n * An error indicating a connection pool is closed\n * @category Error\n */\nexport class PoolClosedError extends MongoDriverError {\n  /** The address of the connection pool */\n  address: string;\n\n  /**\n   * **Do not use this constructor!**\n   *\n   * Meant for internal use only.\n   *\n   * @remarks\n   * This class is only meant to be constructed within the driver. This constructor is\n   * not subject to semantic versioning compatibility guarantees and may change at any time.\n   *\n   * @public\n   **/\n  constructor(pool: ConnectionPool) {\n    super('Attempted to check out a connection from closed connection pool');\n    this.address = pool.address;\n  }\n\n  override get name(): string {\n    return 'MongoPoolClosedError';\n  }\n}\n\n/**\n * An error indicating a connection pool is currently paused\n * @category Error\n */\nexport class PoolClearedError extends MongoNetworkError {\n  /** The address of the connection pool */\n  address: string;\n\n  /**\n   * **Do not use this constructor!**\n   *\n   * Meant for internal use only.\n   *\n   * @remarks\n   * This class is only meant to be constructed within the driver. This constructor is\n   * not subject to semantic versioning compatibility guarantees and may change at any time.\n   *\n   * @public\n   **/\n  constructor(pool: ConnectionPool, message?: string) {\n    const errorMessage = message\n      ? message\n      : `Connection pool for ${pool.address} was cleared because another operation failed with: \"${pool.serverError?.message}\"`;\n    super(errorMessage, pool.serverError ? { cause: pool.serverError } : undefined);\n    this.address = pool.address;\n\n    this.addErrorLabel(MongoErrorLabel.PoolRequstedRetry);\n  }\n\n  override get name(): string {\n    return 'MongoPoolClearedError';\n  }\n}\n\n/**\n * An error indicating that a connection pool has been cleared after the monitor for that server timed out.\n * @category Error\n */\nexport class PoolClearedOnNetworkError extends PoolClearedError {\n  /**\n   * **Do not use this constructor!**\n   *\n   * Meant for internal use only.\n   *\n   * @remarks\n   * This class is only meant to be constructed within the driver. This constructor is\n   * not subject to semantic versioning compatibility guarantees and may change at any time.\n   *\n   * @public\n   **/\n  constructor(pool: ConnectionPool) {\n    super(pool, `Connection to ${pool.address} interrupted due to server monitor timeout`);\n  }\n\n  override get name(): string {\n    return 'PoolClearedOnNetworkError';\n  }\n}\n\n/**\n * An error thrown when a request to check out a connection times out\n * @category Error\n */\nexport class WaitQueueTimeoutError extends MongoDriverError {\n  /** The address of the connection pool */\n  address: string;\n\n  /**\n   * **Do not use this constructor!**\n   *\n   * Meant for internal use only.\n   *\n   * @remarks\n   * This class is only meant to be constructed within the driver. This constructor is\n   * not subject to semantic versioning compatibility guarantees and may change at any time.\n   *\n   * @public\n   **/\n  constructor(message: string, address: string) {\n    super(message);\n    this.address = address;\n  }\n\n  override get name(): string {\n    return 'MongoWaitQueueTimeoutError';\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,OAAA,GAAAC,OAAA;AAGA;;;;AAIA,MAAaC,eAAgB,SAAQF,OAAA,CAAAG,gBAAgB;EAInD;;;;;;;;;;;EAWAC,YAAYC,IAAoB;IAC9B,KAAK,CAAC,iEAAiE,CAAC;IACxE,IAAI,CAACC,OAAO,GAAGD,IAAI,CAACC,OAAO;EAC7B;EAEA,IAAaC,IAAIA,CAAA;IACf,OAAO,sBAAsB;EAC/B;;AAtBFC,OAAA,CAAAN,eAAA,GAAAA,eAAA;AAyBA;;;;AAIA,MAAaO,gBAAiB,SAAQT,OAAA,CAAAU,iBAAiB;EAIrD;;;;;;;;;;;EAWAN,YAAYC,IAAoB,EAAEM,OAAgB;IAChD,MAAMC,YAAY,GAAGD,OAAO,GACxBA,OAAO,GACP,uBAAuBN,IAAI,CAACC,OAAO,wDAAwDD,IAAI,CAACQ,WAAW,EAAEF,OAAO,GAAG;IAC3H,KAAK,CAACC,YAAY,EAAEP,IAAI,CAACQ,WAAW,GAAG;MAAEC,KAAK,EAAET,IAAI,CAACQ;IAAW,CAAE,GAAGE,SAAS,CAAC;IAC/E,IAAI,CAACT,OAAO,GAAGD,IAAI,CAACC,OAAO;IAE3B,IAAI,CAACU,aAAa,CAAChB,OAAA,CAAAiB,eAAe,CAACC,iBAAiB,CAAC;EACvD;EAEA,IAAaX,IAAIA,CAAA;IACf,OAAO,uBAAuB;EAChC;;AA3BFC,OAAA,CAAAC,gBAAA,GAAAA,gBAAA;AA8BA;;;;AAIA,MAAaU,yBAA0B,SAAQV,gBAAgB;EAC7D;;;;;;;;;;;EAWAL,YAAYC,IAAoB;IAC9B,KAAK,CAACA,IAAI,EAAE,iBAAiBA,IAAI,CAACC,OAAO,4CAA4C,CAAC;EACxF;EAEA,IAAaC,IAAIA,CAAA;IACf,OAAO,2BAA2B;EACpC;;AAlBFC,OAAA,CAAAW,yBAAA,GAAAA,yBAAA;AAqBA;;;;AAIA,MAAaC,qBAAsB,SAAQpB,OAAA,CAAAG,gBAAgB;EAIzD;;;;;;;;;;;EAWAC,YAAYO,OAAe,EAAEL,OAAe;IAC1C,KAAK,CAACK,OAAO,CAAC;IACd,IAAI,CAACL,OAAO,GAAGA,OAAO;EACxB;EAEA,IAAaC,IAAIA,CAAA;IACf,OAAO,4BAA4B;EACrC;;AAtBFC,OAAA,CAAAY,qBAAA,GAAAA,qBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}