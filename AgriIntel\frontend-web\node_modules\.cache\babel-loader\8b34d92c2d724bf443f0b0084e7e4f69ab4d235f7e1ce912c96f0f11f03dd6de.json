{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ListDatabasesOperation = void 0;\nconst utils_1 = require(\"../utils\");\nconst command_1 = require(\"./command\");\nconst operation_1 = require(\"./operation\");\n/** @internal */\nclass ListDatabasesOperation extends command_1.CommandOperation {\n  constructor(db, options) {\n    super(db, options);\n    this.options = options ?? {};\n    this.ns = new utils_1.MongoDBNamespace('admin', '$cmd');\n  }\n  get commandName() {\n    return 'listDatabases';\n  }\n  async execute(server, session, timeoutContext) {\n    const cmd = {\n      listDatabases: 1\n    };\n    if (typeof this.options.nameOnly === 'boolean') {\n      cmd.nameOnly = this.options.nameOnly;\n    }\n    if (this.options.filter) {\n      cmd.filter = this.options.filter;\n    }\n    if (typeof this.options.authorizedDatabases === 'boolean') {\n      cmd.authorizedDatabases = this.options.authorizedDatabases;\n    }\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if ((0, utils_1.maxWireVersion)(server) >= 9 && this.options.comment !== undefined) {\n      cmd.comment = this.options.comment;\n    }\n    return await super.executeCommand(server, session, cmd, timeoutContext);\n  }\n}\nexports.ListDatabasesOperation = ListDatabasesOperation;\n(0, operation_1.defineAspects)(ListDatabasesOperation, [operation_1.Aspect.READ_OPERATION, operation_1.Aspect.RETRYABLE]);", "map": {"version": 3, "names": ["utils_1", "require", "command_1", "operation_1", "ListDatabasesOperation", "CommandOperation", "constructor", "db", "options", "ns", "MongoDBNamespace", "commandName", "execute", "server", "session", "timeoutContext", "cmd", "listDatabases", "nameOnly", "filter", "authorizedDatabases", "maxWireVersion", "comment", "undefined", "executeCommand", "exports", "defineAspects", "Aspect", "READ_OPERATION", "RETRYABLE"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\list_databases.ts"], "sourcesContent": ["import type { Document } from '../bson';\nimport type { Db } from '../db';\nimport { type TODO_NODE_3286 } from '../mongo_types';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { maxWireVersion, MongoDBNamespace } from '../utils';\nimport { CommandOperation, type CommandOperationOptions } from './command';\nimport { Aspect, defineAspects } from './operation';\n\n/** @public */\nexport interface ListDatabasesResult {\n  databases: ({ name: string; sizeOnDisk?: number; empty?: boolean } & Document)[];\n  totalSize?: number;\n  totalSizeMb?: number;\n  ok: 1 | 0;\n}\n\n/** @public */\nexport interface ListDatabasesOptions extends CommandOperationOptions {\n  /** A query predicate that determines which databases are listed */\n  filter?: Document;\n  /** A flag to indicate whether the command should return just the database names, or return both database names and size information */\n  nameOnly?: boolean;\n  /** A flag that determines which databases are returned based on the user privileges when access control is enabled */\n  authorizedDatabases?: boolean;\n}\n\n/** @internal */\nexport class ListDatabasesOperation extends CommandOperation<ListDatabasesResult> {\n  override options: ListDatabasesOptions;\n\n  constructor(db: Db, options?: ListDatabasesOptions) {\n    super(db, options);\n    this.options = options ?? {};\n    this.ns = new MongoDBNamespace('admin', '$cmd');\n  }\n\n  override get commandName() {\n    return 'listDatabases' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<ListDatabasesResult> {\n    const cmd: Document = { listDatabases: 1 };\n\n    if (typeof this.options.nameOnly === 'boolean') {\n      cmd.nameOnly = this.options.nameOnly;\n    }\n\n    if (this.options.filter) {\n      cmd.filter = this.options.filter;\n    }\n\n    if (typeof this.options.authorizedDatabases === 'boolean') {\n      cmd.authorizedDatabases = this.options.authorizedDatabases;\n    }\n\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (maxWireVersion(server) >= 9 && this.options.comment !== undefined) {\n      cmd.comment = this.options.comment;\n    }\n\n    return await (super.executeCommand(\n      server,\n      session,\n      cmd,\n      timeoutContext\n    ) as Promise<TODO_NODE_3286>);\n  }\n}\n\ndefineAspects(ListDatabasesOperation, [Aspect.READ_OPERATION, Aspect.RETRYABLE]);\n"], "mappings": ";;;;;;AAMA,MAAAA,OAAA,GAAAC,OAAA;AACA,MAAAC,SAAA,GAAAD,OAAA;AACA,MAAAE,WAAA,GAAAF,OAAA;AAoBA;AACA,MAAaG,sBAAuB,SAAQF,SAAA,CAAAG,gBAAqC;EAG/EC,YAAYC,EAAM,EAAEC,OAA8B;IAChD,KAAK,CAACD,EAAE,EAAEC,OAAO,CAAC;IAClB,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAI,EAAE;IAC5B,IAAI,CAACC,EAAE,GAAG,IAAIT,OAAA,CAAAU,gBAAgB,CAAC,OAAO,EAAE,MAAM,CAAC;EACjD;EAEA,IAAaC,WAAWA,CAAA;IACtB,OAAO,eAAwB;EACjC;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMC,GAAG,GAAa;MAAEC,aAAa,EAAE;IAAC,CAAE;IAE1C,IAAI,OAAO,IAAI,CAACT,OAAO,CAACU,QAAQ,KAAK,SAAS,EAAE;MAC9CF,GAAG,CAACE,QAAQ,GAAG,IAAI,CAACV,OAAO,CAACU,QAAQ;IACtC;IAEA,IAAI,IAAI,CAACV,OAAO,CAACW,MAAM,EAAE;MACvBH,GAAG,CAACG,MAAM,GAAG,IAAI,CAACX,OAAO,CAACW,MAAM;IAClC;IAEA,IAAI,OAAO,IAAI,CAACX,OAAO,CAACY,mBAAmB,KAAK,SAAS,EAAE;MACzDJ,GAAG,CAACI,mBAAmB,GAAG,IAAI,CAACZ,OAAO,CAACY,mBAAmB;IAC5D;IAEA;IACA;IACA,IAAI,IAAApB,OAAA,CAAAqB,cAAc,EAACR,MAAM,CAAC,IAAI,CAAC,IAAI,IAAI,CAACL,OAAO,CAACc,OAAO,KAAKC,SAAS,EAAE;MACrEP,GAAG,CAACM,OAAO,GAAG,IAAI,CAACd,OAAO,CAACc,OAAO;IACpC;IAEA,OAAO,MAAO,KAAK,CAACE,cAAc,CAChCX,MAAM,EACNC,OAAO,EACPE,GAAG,EACHD,cAAc,CACa;EAC/B;;AA5CFU,OAAA,CAAArB,sBAAA,GAAAA,sBAAA;AA+CA,IAAAD,WAAA,CAAAuB,aAAa,EAACtB,sBAAsB,EAAE,CAACD,WAAA,CAAAwB,MAAM,CAACC,cAAc,EAAEzB,WAAA,CAAAwB,MAAM,CAACE,SAAS,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}