{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.hmacSha256Hook = exports.hmacSha512Hook = exports.aes256CtrDecryptHook = exports.aes256CtrEncryptHook = exports.aes256CbcDecryptHook = exports.aes256CbcEncryptHook = void 0;\nexports.makeAES256Hook = makeAES256Hook;\nexports.randomHook = randomHook;\nexports.sha256Hook = sha256Hook;\nexports.makeHmacHook = makeHmacHook;\nexports.signRsaSha256Hook = signRsaSha256Hook;\nconst crypto = require(\"crypto\");\nfunction makeAES256Hook(method, mode) {\n  return function (key, iv, input, output) {\n    let result;\n    try {\n      const cipher = crypto[method](mode, key, iv);\n      cipher.setAutoPadding(false);\n      result = cipher.update(input);\n      const final = cipher.final();\n      if (final.length > 0) {\n        result = Buffer.concat([result, final]);\n      }\n    } catch (e) {\n      return e;\n    }\n    result.copy(output);\n    return result.length;\n  };\n}\nfunction randomHook(buffer, count) {\n  try {\n    crypto.randomFillSync(buffer, 0, count);\n  } catch (e) {\n    return e;\n  }\n  return count;\n}\nfunction sha256Hook(input, output) {\n  let result;\n  try {\n    result = crypto.createHash('sha256').update(input).digest();\n  } catch (e) {\n    return e;\n  }\n  result.copy(output);\n  return result.length;\n}\nfunction makeHmacHook(algorithm) {\n  return (key, input, output) => {\n    let result;\n    try {\n      result = crypto.createHmac(algorithm, key).update(input).digest();\n    } catch (e) {\n      return e;\n    }\n    result.copy(output);\n    return result.length;\n  };\n}\nfunction signRsaSha256Hook(key, input, output) {\n  let result;\n  try {\n    const signer = crypto.createSign('sha256WithRSAEncryption');\n    const privateKey = Buffer.from(`-----BEGIN PRIVATE KEY-----\\n${key.toString('base64')}\\n-----END PRIVATE KEY-----\\n`);\n    result = signer.update(input).end().sign(privateKey);\n  } catch (e) {\n    return e;\n  }\n  result.copy(output);\n  return result.length;\n}\nexports.aes256CbcEncryptHook = makeAES256Hook('createCipheriv', 'aes-256-cbc');\nexports.aes256CbcDecryptHook = makeAES256Hook('createDecipheriv', 'aes-256-cbc');\nexports.aes256CtrEncryptHook = makeAES256Hook('createCipheriv', 'aes-256-ctr');\nexports.aes256CtrDecryptHook = makeAES256Hook('createDecipheriv', 'aes-256-ctr');\nexports.hmacSha512Hook = makeHmacHook('sha512');\nexports.hmacSha256Hook = makeHmacHook('sha256');", "map": {"version": 3, "names": ["exports", "makeAES256Hook", "randomHook", "sha256Hook", "makeHmacHook", "signRsaSha256Hook", "crypto", "require", "method", "mode", "key", "iv", "input", "output", "result", "cipher", "setAutoPadding", "update", "final", "length", "<PERSON><PERSON><PERSON>", "concat", "e", "copy", "buffer", "count", "randomFillSync", "createHash", "digest", "algorithm", "createHmac", "signer", "createSign", "privateKey", "from", "toString", "end", "sign", "aes256CbcEncryptHook", "aes256CbcDecryptHook", "aes256CtrEncryptHook", "aes256CtrDecryptHook", "hmacSha512Hook", "hmacSha256Hook"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\client-side-encryption\\crypto_callbacks.ts"], "sourcesContent": ["import * as crypto from 'crypto';\n\ntype AES256Callback = (key: <PERSON><PERSON><PERSON>, iv: <PERSON><PERSON><PERSON>, input: <PERSON><PERSON><PERSON>, output: <PERSON>uffer) => number | Error;\n\nexport function makeAES256Hook(\n  method: 'createCipheriv' | 'createDecipheriv',\n  mode: 'aes-256-cbc' | 'aes-256-ctr'\n): AES256Callback {\n  return function (key: <PERSON><PERSON><PERSON>, iv: <PERSON><PERSON><PERSON>, input: <PERSON><PERSON><PERSON>, output: <PERSON>uffer): number | Error {\n    let result;\n\n    try {\n      const cipher = crypto[method](mode, key, iv);\n      cipher.setAutoPadding(false);\n      result = cipher.update(input);\n      const final = cipher.final();\n      if (final.length > 0) {\n        result = Buffer.concat([result, final]);\n      }\n    } catch (e) {\n      return e;\n    }\n\n    result.copy(output);\n    return result.length;\n  };\n}\n\nexport function randomHook(buffer: Buffer, count: number): number | Error {\n  try {\n    crypto.randomFillSync(buffer, 0, count);\n  } catch (e) {\n    return e;\n  }\n  return count;\n}\n\nexport function sha256Hook(input: <PERSON><PERSON><PERSON>, output: Buffer): number | Error {\n  let result;\n  try {\n    result = crypto.createHash('sha256').update(input).digest();\n  } catch (e) {\n    return e;\n  }\n\n  result.copy(output);\n  return result.length;\n}\n\ntype HMACHook = (key: Buffer, input: Buffer, output: Buffer) => number | Error;\nexport function makeHmacHook(algorithm: 'sha512' | 'sha256'): HMACHook {\n  return (key: Buffer, input: Buffer, output: Buffer): number | Error => {\n    let result;\n    try {\n      result = crypto.createHmac(algorithm, key).update(input).digest();\n    } catch (e) {\n      return e;\n    }\n\n    result.copy(output);\n    return result.length;\n  };\n}\n\nexport function signRsaSha256Hook(key: Buffer, input: Buffer, output: Buffer): number | Error {\n  let result;\n  try {\n    const signer = crypto.createSign('sha256WithRSAEncryption');\n    const privateKey = Buffer.from(\n      `-----BEGIN PRIVATE KEY-----\\n${key.toString('base64')}\\n-----END PRIVATE KEY-----\\n`\n    );\n\n    result = signer.update(input).end().sign(privateKey);\n  } catch (e) {\n    return e;\n  }\n\n  result.copy(output);\n  return result.length;\n}\n\nexport const aes256CbcEncryptHook = makeAES256Hook('createCipheriv', 'aes-256-cbc');\nexport const aes256CbcDecryptHook = makeAES256Hook('createDecipheriv', 'aes-256-cbc');\nexport const aes256CtrEncryptHook = makeAES256Hook('createCipheriv', 'aes-256-ctr');\nexport const aes256CtrDecryptHook = makeAES256Hook('createDecipheriv', 'aes-256-ctr');\nexport const hmacSha512Hook = makeHmacHook('sha512');\nexport const hmacSha256Hook = makeHmacHook('sha256');\n"], "mappings": ";;;;;;AAIAA,OAAA,CAAAC,cAAA,GAAAA,cAAA;AAwBAD,OAAA,CAAAE,UAAA,GAAAA,UAAA;AASAF,OAAA,CAAAG,UAAA,GAAAA,UAAA;AAaAH,OAAA,CAAAI,YAAA,GAAAA,YAAA;AAcAJ,OAAA,CAAAK,iBAAA,GAAAA,iBAAA;AAhEA,MAAAC,MAAA,GAAAC,OAAA;AAIA,SAAgBN,cAAcA,CAC5BO,MAA6C,EAC7CC,IAAmC;EAEnC,OAAO,UAAUC,GAAW,EAAEC,EAAU,EAAEC,KAAa,EAAEC,MAAc;IACrE,IAAIC,MAAM;IAEV,IAAI;MACF,MAAMC,MAAM,GAAGT,MAAM,CAACE,MAAM,CAAC,CAACC,IAAI,EAAEC,GAAG,EAAEC,EAAE,CAAC;MAC5CI,MAAM,CAACC,cAAc,CAAC,KAAK,CAAC;MAC5BF,MAAM,GAAGC,MAAM,CAACE,MAAM,CAACL,KAAK,CAAC;MAC7B,MAAMM,KAAK,GAAGH,MAAM,CAACG,KAAK,EAAE;MAC5B,IAAIA,KAAK,CAACC,MAAM,GAAG,CAAC,EAAE;QACpBL,MAAM,GAAGM,MAAM,CAACC,MAAM,CAAC,CAACP,MAAM,EAAEI,KAAK,CAAC,CAAC;MACzC;IACF,CAAC,CAAC,OAAOI,CAAC,EAAE;MACV,OAAOA,CAAC;IACV;IAEAR,MAAM,CAACS,IAAI,CAACV,MAAM,CAAC;IACnB,OAAOC,MAAM,CAACK,MAAM;EACtB,CAAC;AACH;AAEA,SAAgBjB,UAAUA,CAACsB,MAAc,EAAEC,KAAa;EACtD,IAAI;IACFnB,MAAM,CAACoB,cAAc,CAACF,MAAM,EAAE,CAAC,EAAEC,KAAK,CAAC;EACzC,CAAC,CAAC,OAAOH,CAAC,EAAE;IACV,OAAOA,CAAC;EACV;EACA,OAAOG,KAAK;AACd;AAEA,SAAgBtB,UAAUA,CAACS,KAAa,EAAEC,MAAc;EACtD,IAAIC,MAAM;EACV,IAAI;IACFA,MAAM,GAAGR,MAAM,CAACqB,UAAU,CAAC,QAAQ,CAAC,CAACV,MAAM,CAACL,KAAK,CAAC,CAACgB,MAAM,EAAE;EAC7D,CAAC,CAAC,OAAON,CAAC,EAAE;IACV,OAAOA,CAAC;EACV;EAEAR,MAAM,CAACS,IAAI,CAACV,MAAM,CAAC;EACnB,OAAOC,MAAM,CAACK,MAAM;AACtB;AAGA,SAAgBf,YAAYA,CAACyB,SAA8B;EACzD,OAAO,CAACnB,GAAW,EAAEE,KAAa,EAAEC,MAAc,KAAoB;IACpE,IAAIC,MAAM;IACV,IAAI;MACFA,MAAM,GAAGR,MAAM,CAACwB,UAAU,CAACD,SAAS,EAAEnB,GAAG,CAAC,CAACO,MAAM,CAACL,KAAK,CAAC,CAACgB,MAAM,EAAE;IACnE,CAAC,CAAC,OAAON,CAAC,EAAE;MACV,OAAOA,CAAC;IACV;IAEAR,MAAM,CAACS,IAAI,CAACV,MAAM,CAAC;IACnB,OAAOC,MAAM,CAACK,MAAM;EACtB,CAAC;AACH;AAEA,SAAgBd,iBAAiBA,CAACK,GAAW,EAAEE,KAAa,EAAEC,MAAc;EAC1E,IAAIC,MAAM;EACV,IAAI;IACF,MAAMiB,MAAM,GAAGzB,MAAM,CAAC0B,UAAU,CAAC,yBAAyB,CAAC;IAC3D,MAAMC,UAAU,GAAGb,MAAM,CAACc,IAAI,CAC5B,gCAAgCxB,GAAG,CAACyB,QAAQ,CAAC,QAAQ,CAAC,+BAA+B,CACtF;IAEDrB,MAAM,GAAGiB,MAAM,CAACd,MAAM,CAACL,KAAK,CAAC,CAACwB,GAAG,EAAE,CAACC,IAAI,CAACJ,UAAU,CAAC;EACtD,CAAC,CAAC,OAAOX,CAAC,EAAE;IACV,OAAOA,CAAC;EACV;EAEAR,MAAM,CAACS,IAAI,CAACV,MAAM,CAAC;EACnB,OAAOC,MAAM,CAACK,MAAM;AACtB;AAEanB,OAAA,CAAAsC,oBAAoB,GAAGrC,cAAc,CAAC,gBAAgB,EAAE,aAAa,CAAC;AACtED,OAAA,CAAAuC,oBAAoB,GAAGtC,cAAc,CAAC,kBAAkB,EAAE,aAAa,CAAC;AACxED,OAAA,CAAAwC,oBAAoB,GAAGvC,cAAc,CAAC,gBAAgB,EAAE,aAAa,CAAC;AACtED,OAAA,CAAAyC,oBAAoB,GAAGxC,cAAc,CAAC,kBAAkB,EAAE,aAAa,CAAC;AACxED,OAAA,CAAA0C,cAAc,GAAGtC,YAAY,CAAC,QAAQ,CAAC;AACvCJ,OAAA,CAAA2C,cAAc,GAAGvC,YAAY,CAAC,QAAQ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}