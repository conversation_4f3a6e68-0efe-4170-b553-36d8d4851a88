{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.loadGCPCredentials = loadGCPCredentials;\nconst deps_1 = require(\"../../deps\");\n/** @internal */\nasync function loadGCPCredentials(kmsProviders) {\n  const gcpMetadata = (0, deps_1.getGcpMetadata)();\n  if ('kModuleError' in gcpMetadata) {\n    return kmsProviders;\n  }\n  const {\n    access_token: accessToken\n  } = await gcpMetadata.instance({\n    property: 'service-accounts/default/token'\n  });\n  return {\n    ...kmsProviders,\n    gcp: {\n      accessToken\n    }\n  };\n}", "map": {"version": 3, "names": ["exports", "loadGCPCredentials", "deps_1", "require", "kmsProviders", "gcpMetadata", "getGcpMetadata", "access_token", "accessToken", "instance", "property", "gcp"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\client-side-encryption\\providers\\gcp.ts"], "sourcesContent": ["import { getGcpMetadata } from '../../deps';\nimport { type KMSProviders } from '.';\n\n/** @internal */\nexport async function loadGCPCredentials(kmsProviders: KMSProviders): Promise<KMSProviders> {\n  const gcpMetadata = getGcpMetadata();\n\n  if ('kModuleError' in gcpMetadata) {\n    return kmsProviders;\n  }\n\n  const { access_token: accessToken } = await gcpMetadata.instance<{ access_token: string }>({\n    property: 'service-accounts/default/token'\n  });\n  return { ...kmsProviders, gcp: { accessToken } };\n}\n"], "mappings": ";;;;;AAIAA,OAAA,CAAAC,kBAAA,GAAAA,kBAAA;AAJA,MAAAC,MAAA,GAAAC,OAAA;AAGA;AACO,eAAeF,kBAAkBA,CAACG,YAA0B;EACjE,MAAMC,WAAW,GAAG,IAAAH,MAAA,CAAAI,cAAc,GAAE;EAEpC,IAAI,cAAc,IAAID,WAAW,EAAE;IACjC,OAAOD,YAAY;EACrB;EAEA,MAAM;IAAEG,YAAY,EAAEC;EAAW,CAAE,GAAG,MAAMH,WAAW,CAACI,QAAQ,CAA2B;IACzFC,QAAQ,EAAE;GACX,CAAC;EACF,OAAO;IAAE,GAAGN,YAAY;IAAEO,GAAG,EAAE;MAAEH;IAAW;EAAE,CAAE;AAClD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}