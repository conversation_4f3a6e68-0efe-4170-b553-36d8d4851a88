import React from 'react';
import { useTranslation } from 'react-i18next';

interface DashboardWidgetProps {
  title: string;
  children: React.ReactNode;
  className?: string;
  headerAction?: React.ReactNode;
}

const DashboardWidget: React.FC<DashboardWidgetProps> = ({
  title,
  children,
  className = '',
  headerAction
}) => {
  return (
    <div className={`card ${className}`}>
      <div className="card-header flex items-center justify-between">
        <h3 className="text-lg font-medium text-gray-900">{title}</h3>
        {headerAction && <div>{headerAction}</div>}
      </div>
      <div className="card-body">
        {children}
      </div>
    </div>
  );
};

export default DashboardWidget;
