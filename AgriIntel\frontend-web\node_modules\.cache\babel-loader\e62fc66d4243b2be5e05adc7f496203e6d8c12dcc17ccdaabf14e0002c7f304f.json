{"ast": null, "code": "import React from'react';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AuthLayout=_ref=>{let{children}=_ref;const{t}=useTranslation();return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"sm:mx-auto sm:w-full sm:max-w-md\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex justify-center\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-6 h-6 text-white\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"})})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-primary-900\",children:\"AMPD\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-primary-600\",children:\"Livestock Management\"})]})]})}),/*#__PURE__*/_jsx(\"h2\",{className:\"mt-6 text-center text-3xl font-extrabold text-gray-900\",children:t('auth.welcomeBack')}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-center text-sm text-gray-600\",children:t('auth.signInToContinue')})]}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\",children:/*#__PURE__*/_jsx(\"div\",{className:\"bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10\",children:children})}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-8 text-center\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-500\",children:\"\\xA9 2024 AMPD Livestock Management System. All rights reserved.\"})})]});};export default AuthLayout;", "map": {"version": 3, "names": ["React", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "AuthLayout", "_ref", "children", "t", "className", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/components/layout/AuthLayout.tsx"], "sourcesContent": ["import React from 'react';\nimport { useTranslation } from 'react-i18next';\n\ninterface AuthLayoutProps {\n  children: React.ReactNode;\n}\n\nconst AuthLayout: React.FC<AuthLayoutProps> = ({ children }) => {\n  const { t } = useTranslation();\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"flex justify-center\">\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center\">\n              <svg\n                className=\"w-6 h-6 text-white\"\n                fill=\"none\"\n                stroke=\"currentColor\"\n                viewBox=\"0 0 24 24\"\n              >\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n                />\n              </svg>\n            </div>\n            <div>\n              <h1 className=\"text-2xl font-bold text-primary-900\">AMPD</h1>\n              <p className=\"text-sm text-primary-600\">Livestock Management</p>\n            </div>\n          </div>\n        </div>\n        <h2 className=\"mt-6 text-center text-3xl font-extrabold text-gray-900\">\n          {t('auth.welcomeBack')}\n        </h2>\n        <p className=\"mt-2 text-center text-sm text-gray-600\">\n          {t('auth.signInToContinue')}\n        </p>\n      </div>\n\n      <div className=\"mt-8 sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10\">\n          {children}\n        </div>\n      </div>\n\n      <div className=\"mt-8 text-center\">\n        <p className=\"text-sm text-gray-500\">\n          © 2024 AMPD Livestock Management System. All rights reserved.\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default AuthLayout;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAM/C,KAAM,CAAAC,UAAqC,CAAGC,IAAA,EAAkB,IAAjB,CAAEC,QAAS,CAAC,CAAAD,IAAA,CACzD,KAAM,CAAEE,CAAE,CAAC,CAAGR,cAAc,CAAC,CAAC,CAE9B,mBACEI,KAAA,QAAKK,SAAS,CAAC,kHAAkH,CAAAF,QAAA,eAC/HH,KAAA,QAAKK,SAAS,CAAC,kCAAkC,CAAAF,QAAA,eAC/CL,IAAA,QAAKO,SAAS,CAAC,qBAAqB,CAAAF,QAAA,cAClCH,KAAA,QAAKK,SAAS,CAAC,6BAA6B,CAAAF,QAAA,eAC1CL,IAAA,QAAKO,SAAS,CAAC,sEAAsE,CAAAF,QAAA,cACnFL,IAAA,QACEO,SAAS,CAAC,oBAAoB,CAC9BC,IAAI,CAAC,MAAM,CACXC,MAAM,CAAC,cAAc,CACrBC,OAAO,CAAC,WAAW,CAAAL,QAAA,cAEnBL,IAAA,SACEW,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,WAAW,CAAE,CAAE,CACfC,CAAC,CAAC,2IAA2I,CAC9I,CAAC,CACC,CAAC,CACH,CAAC,cACNZ,KAAA,QAAAG,QAAA,eACEL,IAAA,OAAIO,SAAS,CAAC,qCAAqC,CAAAF,QAAA,CAAC,MAAI,CAAI,CAAC,cAC7DL,IAAA,MAAGO,SAAS,CAAC,0BAA0B,CAAAF,QAAA,CAAC,sBAAoB,CAAG,CAAC,EAC7D,CAAC,EACH,CAAC,CACH,CAAC,cACNL,IAAA,OAAIO,SAAS,CAAC,wDAAwD,CAAAF,QAAA,CACnEC,CAAC,CAAC,kBAAkB,CAAC,CACpB,CAAC,cACLN,IAAA,MAAGO,SAAS,CAAC,wCAAwC,CAAAF,QAAA,CAClDC,CAAC,CAAC,uBAAuB,CAAC,CAC1B,CAAC,EACD,CAAC,cAENN,IAAA,QAAKO,SAAS,CAAC,uCAAuC,CAAAF,QAAA,cACpDL,IAAA,QAAKO,SAAS,CAAC,qDAAqD,CAAAF,QAAA,CACjEA,QAAQ,CACN,CAAC,CACH,CAAC,cAENL,IAAA,QAAKO,SAAS,CAAC,kBAAkB,CAAAF,QAAA,cAC/BL,IAAA,MAAGO,SAAS,CAAC,uBAAuB,CAAAF,QAAA,CAAC,kEAErC,CAAG,CAAC,CACD,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAF,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}