import React from 'react';
import { NavLink } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import {
  HomeIcon,
  ChartBarIcon,
  HeartIcon,
  BeakerIcon,
  CurrencyDollarIcon,
  DocumentChartBarIcon,
  Cog6ToothIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';

import { RootState } from '../../store/store';

const Sidebar: React.FC = () => {
  const { t } = useTranslation();
  const { sidebarOpen } = useSelector((state: RootState) => state.ui);

  const navigation = [
    { name: t('navigation.dashboard'), href: '/dashboard', icon: HomeIcon },
    { name: t('navigation.animals'), href: '/animals', icon: ChartBarIcon },
    { name: t('navigation.health'), href: '/health', icon: HeartIcon },
    { name: t('navigation.breeding'), href: '/breeding', icon: BeakerIcon },
    { name: t('navigation.feeding'), href: '/feeding', icon: UserGroupIcon },
    { name: t('navigation.financial'), href: '/financial', icon: CurrencyDollarIcon },
    { name: t('navigation.reports'), href: '/reports', icon: DocumentChartBarIcon },
    { name: t('navigation.settings'), href: '/settings', icon: Cog6ToothIcon },
  ];

  return (
    <div
      className={`fixed inset-y-0 left-0 z-50 bg-white shadow-lg transition-all duration-300 ${
        sidebarOpen ? 'w-64' : 'w-16'
      } lg:translate-x-0`}
    >
      {/* Logo */}
      <div className="flex items-center h-16 px-4 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
            <svg
              className="w-5 h-5 text-white"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
              />
            </svg>
          </div>
          {sidebarOpen && (
            <div>
              <h1 className="text-lg font-bold text-primary-900">AMPD</h1>
              <p className="text-xs text-primary-600">Livestock</p>
            </div>
          )}
        </div>
      </div>

      {/* Navigation */}
      <nav className="mt-8 px-4">
        <ul className="space-y-2">
          {navigation.map((item) => (
            <li key={item.name}>
              <NavLink
                to={item.href}
                className={({ isActive }) =>
                  `flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${
                    isActive
                      ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600'
                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'
                  }`
                }
              >
                <item.icon className="h-5 w-5 flex-shrink-0" />
                {sidebarOpen && <span className="ml-3">{item.name}</span>}
              </NavLink>
            </li>
          ))}
        </ul>
      </nav>
    </div>
  );
};

export default Sidebar;
