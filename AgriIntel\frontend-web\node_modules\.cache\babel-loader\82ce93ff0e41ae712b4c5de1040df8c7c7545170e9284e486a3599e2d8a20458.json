{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.LegacyTimeoutContext = exports.CSOTTimeoutContext = exports.TimeoutContext = exports.Timeout = exports.TimeoutError = void 0;\nconst timers_1 = require(\"timers\");\nconst error_1 = require(\"./error\");\nconst utils_1 = require(\"./utils\");\n/** @internal */\nclass TimeoutError extends Error {\n  get name() {\n    return 'TimeoutError';\n  }\n  constructor(message, options) {\n    super(message, options);\n    this.duration = options.duration;\n  }\n  static is(error) {\n    return error != null && typeof error === 'object' && 'name' in error && error.name === 'TimeoutError';\n  }\n}\nexports.TimeoutError = TimeoutError;\n/**\n * @internal\n * This class is an abstraction over timeouts\n * The Timeout class can only be in the pending or rejected states. It is guaranteed not to resolve\n * if interacted with exclusively through its public API\n * */\nclass Timeout extends Promise {\n  get remainingTime() {\n    if (this.timedOut) return 0;\n    if (this.duration === 0) return Infinity;\n    return this.start + this.duration - Math.trunc(performance.now());\n  }\n  get timeElapsed() {\n    return Math.trunc(performance.now()) - this.start;\n  }\n  /** Create a new timeout that expires in `duration` ms */\n  constructor(executor = () => null, options) {\n    const duration = options?.duration ?? 0;\n    const unref = !!options?.unref;\n    const rejection = options?.rejection;\n    if (duration < 0) {\n      throw new error_1.MongoInvalidArgumentError('Cannot create a Timeout with a negative duration');\n    }\n    let reject;\n    super((_, promiseReject) => {\n      reject = promiseReject;\n      executor(utils_1.noop, promiseReject);\n    });\n    this.ended = null;\n    this.timedOut = false;\n    this.cleared = false;\n    this.duration = duration;\n    this.start = Math.trunc(performance.now());\n    if (rejection == null && this.duration > 0) {\n      this.id = (0, timers_1.setTimeout)(() => {\n        this.ended = Math.trunc(performance.now());\n        this.timedOut = true;\n        reject(new TimeoutError(`Expired after ${duration}ms`, {\n          duration\n        }));\n      }, this.duration);\n      if (typeof this.id.unref === 'function' && unref) {\n        // Ensure we do not keep the Node.js event loop running\n        this.id.unref();\n      }\n    } else if (rejection != null) {\n      this.ended = Math.trunc(performance.now());\n      this.timedOut = true;\n      reject(rejection);\n    }\n  }\n  /**\n   * Clears the underlying timeout. This method is idempotent\n   */\n  clear() {\n    (0, timers_1.clearTimeout)(this.id);\n    this.id = undefined;\n    this.timedOut = false;\n    this.cleared = true;\n  }\n  throwIfExpired() {\n    if (this.timedOut) {\n      // This method is invoked when someone wants to throw immediately instead of await the result of this promise\n      // Since they won't be handling the rejection from the promise (because we're about to throw here)\n      // attach handling to prevent this from bubbling up to Node.js\n      this.then(undefined, utils_1.squashError);\n      throw new TimeoutError('Timed out', {\n        duration: this.duration\n      });\n    }\n  }\n  static expires(duration, unref) {\n    return new Timeout(undefined, {\n      duration,\n      unref\n    });\n  }\n  static reject(rejection) {\n    return new Timeout(undefined, {\n      duration: 0,\n      unref: true,\n      rejection\n    });\n  }\n}\nexports.Timeout = Timeout;\nfunction isLegacyTimeoutContextOptions(v) {\n  return v != null && typeof v === 'object' && 'serverSelectionTimeoutMS' in v && typeof v.serverSelectionTimeoutMS === 'number' && 'waitQueueTimeoutMS' in v && typeof v.waitQueueTimeoutMS === 'number';\n}\nfunction isCSOTTimeoutContextOptions(v) {\n  return v != null && typeof v === 'object' && 'serverSelectionTimeoutMS' in v && typeof v.serverSelectionTimeoutMS === 'number' && 'timeoutMS' in v && typeof v.timeoutMS === 'number';\n}\n/** @internal */\nclass TimeoutContext {\n  static create(options) {\n    if (options.session?.timeoutContext != null) return options.session?.timeoutContext;\n    if (isCSOTTimeoutContextOptions(options)) return new CSOTTimeoutContext(options);else if (isLegacyTimeoutContextOptions(options)) return new LegacyTimeoutContext(options);else throw new error_1.MongoRuntimeError('Unrecognized options');\n  }\n}\nexports.TimeoutContext = TimeoutContext;\n/** @internal */\nclass CSOTTimeoutContext extends TimeoutContext {\n  constructor(options) {\n    super();\n    this.minRoundTripTime = 0;\n    this.start = Math.trunc(performance.now());\n    this.timeoutMS = options.timeoutMS;\n    this.serverSelectionTimeoutMS = options.serverSelectionTimeoutMS;\n    this.socketTimeoutMS = options.socketTimeoutMS;\n    this.clearServerSelectionTimeout = false;\n  }\n  get maxTimeMS() {\n    return this.remainingTimeMS - this.minRoundTripTime;\n  }\n  get remainingTimeMS() {\n    const timePassed = Math.trunc(performance.now()) - this.start;\n    return this.timeoutMS <= 0 ? Infinity : this.timeoutMS - timePassed;\n  }\n  csotEnabled() {\n    return true;\n  }\n  get serverSelectionTimeout() {\n    // check for undefined\n    if (typeof this._serverSelectionTimeout !== 'object' || this._serverSelectionTimeout?.cleared) {\n      const {\n        remainingTimeMS,\n        serverSelectionTimeoutMS\n      } = this;\n      if (remainingTimeMS <= 0) return Timeout.reject(new error_1.MongoOperationTimeoutError(`Timed out in server selection after ${this.timeoutMS}ms`));\n      const usingServerSelectionTimeoutMS = serverSelectionTimeoutMS !== 0 && (0, utils_1.csotMin)(remainingTimeMS, serverSelectionTimeoutMS) === serverSelectionTimeoutMS;\n      if (usingServerSelectionTimeoutMS) {\n        this._serverSelectionTimeout = Timeout.expires(serverSelectionTimeoutMS);\n      } else {\n        if (remainingTimeMS > 0 && Number.isFinite(remainingTimeMS)) {\n          this._serverSelectionTimeout = Timeout.expires(remainingTimeMS);\n        } else {\n          this._serverSelectionTimeout = null;\n        }\n      }\n    }\n    return this._serverSelectionTimeout;\n  }\n  get connectionCheckoutTimeout() {\n    if (typeof this._connectionCheckoutTimeout !== 'object' || this._connectionCheckoutTimeout?.cleared) {\n      if (typeof this._serverSelectionTimeout === 'object') {\n        // null or Timeout\n        this._connectionCheckoutTimeout = this._serverSelectionTimeout;\n      } else {\n        throw new error_1.MongoRuntimeError('Unreachable. If you are seeing this error, please file a ticket on the NODE driver project on Jira');\n      }\n    }\n    return this._connectionCheckoutTimeout;\n  }\n  get timeoutForSocketWrite() {\n    const {\n      remainingTimeMS\n    } = this;\n    if (!Number.isFinite(remainingTimeMS)) return null;\n    if (remainingTimeMS > 0) return Timeout.expires(remainingTimeMS);\n    return Timeout.reject(new error_1.MongoOperationTimeoutError('Timed out before socket write'));\n  }\n  get timeoutForSocketRead() {\n    const {\n      remainingTimeMS\n    } = this;\n    if (!Number.isFinite(remainingTimeMS)) return null;\n    if (remainingTimeMS > 0) return Timeout.expires(remainingTimeMS);\n    return Timeout.reject(new error_1.MongoOperationTimeoutError('Timed out before socket read'));\n  }\n  refresh() {\n    this.start = Math.trunc(performance.now());\n    this.minRoundTripTime = 0;\n    this._serverSelectionTimeout?.clear();\n    this._connectionCheckoutTimeout?.clear();\n  }\n  clear() {\n    this._serverSelectionTimeout?.clear();\n    this._connectionCheckoutTimeout?.clear();\n  }\n  /**\n   * @internal\n   * Throws a MongoOperationTimeoutError if the context has expired.\n   * If the context has not expired, returns the `remainingTimeMS`\n   **/\n  getRemainingTimeMSOrThrow(message) {\n    const {\n      remainingTimeMS\n    } = this;\n    if (remainingTimeMS <= 0) throw new error_1.MongoOperationTimeoutError(message ?? `Expired after ${this.timeoutMS}ms`);\n    return remainingTimeMS;\n  }\n  /**\n   * @internal\n   * This method is intended to be used in situations where concurrent operation are on the same deadline, but cannot share a single `TimeoutContext` instance.\n   * Returns a new instance of `CSOTTimeoutContext` constructed with identical options, but setting the `start` property to `this.start`.\n   */\n  clone() {\n    const timeoutContext = new CSOTTimeoutContext({\n      timeoutMS: this.timeoutMS,\n      serverSelectionTimeoutMS: this.serverSelectionTimeoutMS\n    });\n    timeoutContext.start = this.start;\n    return timeoutContext;\n  }\n  refreshed() {\n    return new CSOTTimeoutContext(this);\n  }\n  addMaxTimeMSToCommand(command, options) {\n    if (options.omitMaxTimeMS) return;\n    const maxTimeMS = this.remainingTimeMS - this.minRoundTripTime;\n    if (maxTimeMS > 0 && Number.isFinite(maxTimeMS)) command.maxTimeMS = maxTimeMS;\n  }\n  getSocketTimeoutMS() {\n    return 0;\n  }\n}\nexports.CSOTTimeoutContext = CSOTTimeoutContext;\n/** @internal */\nclass LegacyTimeoutContext extends TimeoutContext {\n  constructor(options) {\n    super();\n    this.options = options;\n    this.clearServerSelectionTimeout = true;\n  }\n  csotEnabled() {\n    return false;\n  }\n  get serverSelectionTimeout() {\n    if (this.options.serverSelectionTimeoutMS != null && this.options.serverSelectionTimeoutMS > 0) return Timeout.expires(this.options.serverSelectionTimeoutMS);\n    return null;\n  }\n  get connectionCheckoutTimeout() {\n    if (this.options.waitQueueTimeoutMS != null && this.options.waitQueueTimeoutMS > 0) return Timeout.expires(this.options.waitQueueTimeoutMS);\n    return null;\n  }\n  get timeoutForSocketWrite() {\n    return null;\n  }\n  get timeoutForSocketRead() {\n    return null;\n  }\n  refresh() {\n    return;\n  }\n  clear() {\n    return;\n  }\n  get maxTimeMS() {\n    return null;\n  }\n  refreshed() {\n    return new LegacyTimeoutContext(this.options);\n  }\n  addMaxTimeMSToCommand(_command, _options) {\n    // No max timeMS is added to commands in legacy timeout mode.\n  }\n  getSocketTimeoutMS() {\n    return this.options.socketTimeoutMS;\n  }\n}\nexports.LegacyTimeoutContext = LegacyTimeoutContext;", "map": {"version": 3, "names": ["timers_1", "require", "error_1", "utils_1", "TimeoutError", "Error", "name", "constructor", "message", "options", "duration", "is", "error", "exports", "Timeout", "Promise", "remainingTime", "timedOut", "Infinity", "start", "Math", "trunc", "performance", "now", "timeElapsed", "executor", "unref", "rejection", "MongoInvalidArgumentError", "reject", "_", "promiseReject", "noop", "ended", "cleared", "id", "setTimeout", "clear", "clearTimeout", "undefined", "throwIfExpired", "then", "squashError", "expires", "isLegacyTimeoutContextOptions", "v", "serverSelectionTimeoutMS", "waitQueueTimeoutMS", "isCSOTTimeoutContextOptions", "timeoutMS", "TimeoutContext", "create", "session", "timeoutContext", "CSOTTimeoutContext", "LegacyTimeoutContext", "MongoRuntimeError", "minRoundTripTime", "socketTimeoutMS", "clearServerSelectionTimeout", "maxTimeMS", "remainingTimeMS", "timePassed", "csotEnabled", "serverSelectionTimeout", "_serverSelectionTimeout", "MongoOperationTimeoutError", "usingServerSelectionTimeoutMS", "csotMin", "Number", "isFinite", "connectionCheckoutTimeout", "_connectionCheckoutTimeout", "timeoutForSocketWrite", "timeoutForSocketRead", "refresh", "getRemainingTimeMSOrThrow", "clone", "refreshed", "addMaxTimeMSToCommand", "command", "omitMaxTimeMS", "getSocketTimeoutMS", "_command", "_options"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\timeout.ts"], "sourcesContent": ["import { clearTimeout, setTimeout } from 'timers';\n\nimport { type Document } from './bson';\nimport { MongoInvalidArgumentError, MongoOperationTimeoutError, MongoRuntimeError } from './error';\nimport { type ClientSession } from './sessions';\nimport { csotMin, noop, squashError } from './utils';\n\n/** @internal */\nexport class TimeoutError extends Error {\n  duration: number;\n  override get name(): 'TimeoutError' {\n    return 'TimeoutError';\n  }\n\n  constructor(message: string, options: { cause?: Error; duration: number }) {\n    super(message, options);\n    this.duration = options.duration;\n  }\n\n  static is(error: unknown): error is TimeoutError {\n    return (\n      error != null && typeof error === 'object' && 'name' in error && error.name === 'TimeoutError'\n    );\n  }\n}\n\ntype Executor = ConstructorParameters<typeof Promise<never>>[0];\ntype Reject = Parameters<ConstructorParameters<typeof Promise<never>>[0]>[1];\n/**\n * @internal\n * This class is an abstraction over timeouts\n * The Timeout class can only be in the pending or rejected states. It is guaranteed not to resolve\n * if interacted with exclusively through its public API\n * */\nexport class Timeout extends Promise<never> {\n  private id?: NodeJS.Timeout;\n\n  public readonly start: number;\n  public ended: number | null = null;\n  public duration: number;\n  private timedOut = false;\n  public cleared = false;\n\n  get remainingTime(): number {\n    if (this.timedOut) return 0;\n    if (this.duration === 0) return Infinity;\n    return this.start + this.duration - Math.trunc(performance.now());\n  }\n\n  get timeElapsed(): number {\n    return Math.trunc(performance.now()) - this.start;\n  }\n\n  /** Create a new timeout that expires in `duration` ms */\n  private constructor(\n    executor: Executor = () => null,\n    options?: { duration: number; unref?: true; rejection?: Error }\n  ) {\n    const duration = options?.duration ?? 0;\n    const unref = !!options?.unref;\n    const rejection = options?.rejection;\n\n    if (duration < 0) {\n      throw new MongoInvalidArgumentError('Cannot create a Timeout with a negative duration');\n    }\n\n    let reject!: Reject;\n    super((_, promiseReject) => {\n      reject = promiseReject;\n\n      executor(noop, promiseReject);\n    });\n\n    this.duration = duration;\n    this.start = Math.trunc(performance.now());\n\n    if (rejection == null && this.duration > 0) {\n      this.id = setTimeout(() => {\n        this.ended = Math.trunc(performance.now());\n        this.timedOut = true;\n        reject(new TimeoutError(`Expired after ${duration}ms`, { duration }));\n      }, this.duration);\n      if (typeof this.id.unref === 'function' && unref) {\n        // Ensure we do not keep the Node.js event loop running\n        this.id.unref();\n      }\n    } else if (rejection != null) {\n      this.ended = Math.trunc(performance.now());\n      this.timedOut = true;\n      reject(rejection);\n    }\n  }\n\n  /**\n   * Clears the underlying timeout. This method is idempotent\n   */\n  clear(): void {\n    clearTimeout(this.id);\n    this.id = undefined;\n    this.timedOut = false;\n    this.cleared = true;\n  }\n\n  throwIfExpired(): void {\n    if (this.timedOut) {\n      // This method is invoked when someone wants to throw immediately instead of await the result of this promise\n      // Since they won't be handling the rejection from the promise (because we're about to throw here)\n      // attach handling to prevent this from bubbling up to Node.js\n      this.then(undefined, squashError);\n      throw new TimeoutError('Timed out', { duration: this.duration });\n    }\n  }\n\n  public static expires(duration: number, unref?: true): Timeout {\n    return new Timeout(undefined, { duration, unref });\n  }\n\n  static override reject(rejection?: Error): Timeout {\n    return new Timeout(undefined, { duration: 0, unref: true, rejection });\n  }\n}\n\n/** @internal */\nexport type TimeoutContextOptions = (LegacyTimeoutContextOptions | CSOTTimeoutContextOptions) & {\n  session?: ClientSession;\n};\n\n/** @internal */\nexport type LegacyTimeoutContextOptions = {\n  serverSelectionTimeoutMS: number;\n  waitQueueTimeoutMS: number;\n  socketTimeoutMS?: number;\n};\n\n/** @internal */\nexport type CSOTTimeoutContextOptions = {\n  timeoutMS: number;\n  serverSelectionTimeoutMS: number;\n  socketTimeoutMS?: number;\n};\n\nfunction isLegacyTimeoutContextOptions(v: unknown): v is LegacyTimeoutContextOptions {\n  return (\n    v != null &&\n    typeof v === 'object' &&\n    'serverSelectionTimeoutMS' in v &&\n    typeof v.serverSelectionTimeoutMS === 'number' &&\n    'waitQueueTimeoutMS' in v &&\n    typeof v.waitQueueTimeoutMS === 'number'\n  );\n}\n\nfunction isCSOTTimeoutContextOptions(v: unknown): v is CSOTTimeoutContextOptions {\n  return (\n    v != null &&\n    typeof v === 'object' &&\n    'serverSelectionTimeoutMS' in v &&\n    typeof v.serverSelectionTimeoutMS === 'number' &&\n    'timeoutMS' in v &&\n    typeof v.timeoutMS === 'number'\n  );\n}\n\n/** @internal */\nexport abstract class TimeoutContext {\n  static create(options: TimeoutContextOptions): TimeoutContext {\n    if (options.session?.timeoutContext != null) return options.session?.timeoutContext;\n    if (isCSOTTimeoutContextOptions(options)) return new CSOTTimeoutContext(options);\n    else if (isLegacyTimeoutContextOptions(options)) return new LegacyTimeoutContext(options);\n    else throw new MongoRuntimeError('Unrecognized options');\n  }\n\n  abstract get maxTimeMS(): number | null;\n\n  abstract get serverSelectionTimeout(): Timeout | null;\n\n  abstract get connectionCheckoutTimeout(): Timeout | null;\n\n  abstract get clearServerSelectionTimeout(): boolean;\n\n  abstract get timeoutForSocketWrite(): Timeout | null;\n\n  abstract get timeoutForSocketRead(): Timeout | null;\n\n  abstract csotEnabled(): this is CSOTTimeoutContext;\n\n  abstract refresh(): void;\n\n  abstract clear(): void;\n\n  /** Returns a new instance of the TimeoutContext, with all timeouts refreshed and restarted. */\n  abstract refreshed(): TimeoutContext;\n\n  abstract addMaxTimeMSToCommand(command: Document, options: { omitMaxTimeMS?: boolean }): void;\n\n  abstract getSocketTimeoutMS(): number | undefined;\n}\n\n/** @internal */\nexport class CSOTTimeoutContext extends TimeoutContext {\n  timeoutMS: number;\n  serverSelectionTimeoutMS: number;\n  socketTimeoutMS?: number;\n\n  clearServerSelectionTimeout: boolean;\n\n  private _serverSelectionTimeout?: Timeout | null;\n  private _connectionCheckoutTimeout?: Timeout | null;\n  public minRoundTripTime = 0;\n  public start: number;\n\n  constructor(options: CSOTTimeoutContextOptions) {\n    super();\n    this.start = Math.trunc(performance.now());\n\n    this.timeoutMS = options.timeoutMS;\n\n    this.serverSelectionTimeoutMS = options.serverSelectionTimeoutMS;\n\n    this.socketTimeoutMS = options.socketTimeoutMS;\n\n    this.clearServerSelectionTimeout = false;\n  }\n\n  get maxTimeMS(): number {\n    return this.remainingTimeMS - this.minRoundTripTime;\n  }\n\n  get remainingTimeMS() {\n    const timePassed = Math.trunc(performance.now()) - this.start;\n    return this.timeoutMS <= 0 ? Infinity : this.timeoutMS - timePassed;\n  }\n\n  csotEnabled(): this is CSOTTimeoutContext {\n    return true;\n  }\n\n  get serverSelectionTimeout(): Timeout | null {\n    // check for undefined\n    if (typeof this._serverSelectionTimeout !== 'object' || this._serverSelectionTimeout?.cleared) {\n      const { remainingTimeMS, serverSelectionTimeoutMS } = this;\n      if (remainingTimeMS <= 0)\n        return Timeout.reject(\n          new MongoOperationTimeoutError(`Timed out in server selection after ${this.timeoutMS}ms`)\n        );\n      const usingServerSelectionTimeoutMS =\n        serverSelectionTimeoutMS !== 0 &&\n        csotMin(remainingTimeMS, serverSelectionTimeoutMS) === serverSelectionTimeoutMS;\n      if (usingServerSelectionTimeoutMS) {\n        this._serverSelectionTimeout = Timeout.expires(serverSelectionTimeoutMS);\n      } else {\n        if (remainingTimeMS > 0 && Number.isFinite(remainingTimeMS)) {\n          this._serverSelectionTimeout = Timeout.expires(remainingTimeMS);\n        } else {\n          this._serverSelectionTimeout = null;\n        }\n      }\n    }\n\n    return this._serverSelectionTimeout;\n  }\n\n  get connectionCheckoutTimeout(): Timeout | null {\n    if (\n      typeof this._connectionCheckoutTimeout !== 'object' ||\n      this._connectionCheckoutTimeout?.cleared\n    ) {\n      if (typeof this._serverSelectionTimeout === 'object') {\n        // null or Timeout\n        this._connectionCheckoutTimeout = this._serverSelectionTimeout;\n      } else {\n        throw new MongoRuntimeError(\n          'Unreachable. If you are seeing this error, please file a ticket on the NODE driver project on Jira'\n        );\n      }\n    }\n    return this._connectionCheckoutTimeout;\n  }\n\n  get timeoutForSocketWrite(): Timeout | null {\n    const { remainingTimeMS } = this;\n    if (!Number.isFinite(remainingTimeMS)) return null;\n    if (remainingTimeMS > 0) return Timeout.expires(remainingTimeMS);\n    return Timeout.reject(new MongoOperationTimeoutError('Timed out before socket write'));\n  }\n\n  get timeoutForSocketRead(): Timeout | null {\n    const { remainingTimeMS } = this;\n    if (!Number.isFinite(remainingTimeMS)) return null;\n    if (remainingTimeMS > 0) return Timeout.expires(remainingTimeMS);\n    return Timeout.reject(new MongoOperationTimeoutError('Timed out before socket read'));\n  }\n\n  refresh(): void {\n    this.start = Math.trunc(performance.now());\n    this.minRoundTripTime = 0;\n    this._serverSelectionTimeout?.clear();\n    this._connectionCheckoutTimeout?.clear();\n  }\n\n  clear(): void {\n    this._serverSelectionTimeout?.clear();\n    this._connectionCheckoutTimeout?.clear();\n  }\n\n  /**\n   * @internal\n   * Throws a MongoOperationTimeoutError if the context has expired.\n   * If the context has not expired, returns the `remainingTimeMS`\n   **/\n  getRemainingTimeMSOrThrow(message?: string): number {\n    const { remainingTimeMS } = this;\n    if (remainingTimeMS <= 0)\n      throw new MongoOperationTimeoutError(message ?? `Expired after ${this.timeoutMS}ms`);\n    return remainingTimeMS;\n  }\n\n  /**\n   * @internal\n   * This method is intended to be used in situations where concurrent operation are on the same deadline, but cannot share a single `TimeoutContext` instance.\n   * Returns a new instance of `CSOTTimeoutContext` constructed with identical options, but setting the `start` property to `this.start`.\n   */\n  clone(): CSOTTimeoutContext {\n    const timeoutContext = new CSOTTimeoutContext({\n      timeoutMS: this.timeoutMS,\n      serverSelectionTimeoutMS: this.serverSelectionTimeoutMS\n    });\n    timeoutContext.start = this.start;\n    return timeoutContext;\n  }\n\n  override refreshed(): CSOTTimeoutContext {\n    return new CSOTTimeoutContext(this);\n  }\n\n  override addMaxTimeMSToCommand(command: Document, options: { omitMaxTimeMS?: boolean }): void {\n    if (options.omitMaxTimeMS) return;\n    const maxTimeMS = this.remainingTimeMS - this.minRoundTripTime;\n    if (maxTimeMS > 0 && Number.isFinite(maxTimeMS)) command.maxTimeMS = maxTimeMS;\n  }\n\n  override getSocketTimeoutMS(): number | undefined {\n    return 0;\n  }\n}\n\n/** @internal */\nexport class LegacyTimeoutContext extends TimeoutContext {\n  options: LegacyTimeoutContextOptions;\n  clearServerSelectionTimeout: boolean;\n\n  constructor(options: LegacyTimeoutContextOptions) {\n    super();\n    this.options = options;\n    this.clearServerSelectionTimeout = true;\n  }\n\n  csotEnabled(): this is CSOTTimeoutContext {\n    return false;\n  }\n\n  get serverSelectionTimeout(): Timeout | null {\n    if (this.options.serverSelectionTimeoutMS != null && this.options.serverSelectionTimeoutMS > 0)\n      return Timeout.expires(this.options.serverSelectionTimeoutMS);\n    return null;\n  }\n\n  get connectionCheckoutTimeout(): Timeout | null {\n    if (this.options.waitQueueTimeoutMS != null && this.options.waitQueueTimeoutMS > 0)\n      return Timeout.expires(this.options.waitQueueTimeoutMS);\n    return null;\n  }\n\n  get timeoutForSocketWrite(): Timeout | null {\n    return null;\n  }\n\n  get timeoutForSocketRead(): Timeout | null {\n    return null;\n  }\n\n  refresh(): void {\n    return;\n  }\n\n  clear(): void {\n    return;\n  }\n\n  get maxTimeMS() {\n    return null;\n  }\n\n  override refreshed(): LegacyTimeoutContext {\n    return new LegacyTimeoutContext(this.options);\n  }\n\n  override addMaxTimeMSToCommand(_command: Document, _options: { omitMaxTimeMS?: boolean }): void {\n    // No max timeMS is added to commands in legacy timeout mode.\n  }\n\n  override getSocketTimeoutMS(): number | undefined {\n    return this.options.socketTimeoutMS;\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,QAAA,GAAAC,OAAA;AAGA,MAAAC,OAAA,GAAAD,OAAA;AAEA,MAAAE,OAAA,GAAAF,OAAA;AAEA;AACA,MAAaG,YAAa,SAAQC,KAAK;EAErC,IAAaC,IAAIA,CAAA;IACf,OAAO,cAAc;EACvB;EAEAC,YAAYC,OAAe,EAAEC,OAA4C;IACvE,KAAK,CAACD,OAAO,EAAEC,OAAO,CAAC;IACvB,IAAI,CAACC,QAAQ,GAAGD,OAAO,CAACC,QAAQ;EAClC;EAEA,OAAOC,EAAEA,CAACC,KAAc;IACtB,OACEA,KAAK,IAAI,IAAI,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,MAAM,IAAIA,KAAK,IAAIA,KAAK,CAACN,IAAI,KAAK,cAAc;EAElG;;AAfFO,OAAA,CAAAT,YAAA,GAAAA,YAAA;AAoBA;;;;;;AAMA,MAAaU,OAAQ,SAAQC,OAAc;EASzC,IAAIC,aAAaA,CAAA;IACf,IAAI,IAAI,CAACC,QAAQ,EAAE,OAAO,CAAC;IAC3B,IAAI,IAAI,CAACP,QAAQ,KAAK,CAAC,EAAE,OAAOQ,QAAQ;IACxC,OAAO,IAAI,CAACC,KAAK,GAAG,IAAI,CAACT,QAAQ,GAAGU,IAAI,CAACC,KAAK,CAACC,WAAW,CAACC,GAAG,EAAE,CAAC;EACnE;EAEA,IAAIC,WAAWA,CAAA;IACb,OAAOJ,IAAI,CAACC,KAAK,CAACC,WAAW,CAACC,GAAG,EAAE,CAAC,GAAG,IAAI,CAACJ,KAAK;EACnD;EAEA;EACAZ,YACEkB,QAAA,GAAqBA,CAAA,KAAM,IAAI,EAC/BhB,OAA+D;IAE/D,MAAMC,QAAQ,GAAGD,OAAO,EAAEC,QAAQ,IAAI,CAAC;IACvC,MAAMgB,KAAK,GAAG,CAAC,CAACjB,OAAO,EAAEiB,KAAK;IAC9B,MAAMC,SAAS,GAAGlB,OAAO,EAAEkB,SAAS;IAEpC,IAAIjB,QAAQ,GAAG,CAAC,EAAE;MAChB,MAAM,IAAIR,OAAA,CAAA0B,yBAAyB,CAAC,kDAAkD,CAAC;IACzF;IAEA,IAAIC,MAAe;IACnB,KAAK,CAAC,CAACC,CAAC,EAAEC,aAAa,KAAI;MACzBF,MAAM,GAAGE,aAAa;MAEtBN,QAAQ,CAACtB,OAAA,CAAA6B,IAAI,EAAED,aAAa,CAAC;IAC/B,CAAC,CAAC;IAjCG,KAAAE,KAAK,GAAkB,IAAI;IAE1B,KAAAhB,QAAQ,GAAG,KAAK;IACjB,KAAAiB,OAAO,GAAG,KAAK;IAgCpB,IAAI,CAACxB,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACS,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACC,WAAW,CAACC,GAAG,EAAE,CAAC;IAE1C,IAAII,SAAS,IAAI,IAAI,IAAI,IAAI,CAACjB,QAAQ,GAAG,CAAC,EAAE;MAC1C,IAAI,CAACyB,EAAE,GAAG,IAAAnC,QAAA,CAAAoC,UAAU,EAAC,MAAK;QACxB,IAAI,CAACH,KAAK,GAAGb,IAAI,CAACC,KAAK,CAACC,WAAW,CAACC,GAAG,EAAE,CAAC;QAC1C,IAAI,CAACN,QAAQ,GAAG,IAAI;QACpBY,MAAM,CAAC,IAAIzB,YAAY,CAAC,iBAAiBM,QAAQ,IAAI,EAAE;UAAEA;QAAQ,CAAE,CAAC,CAAC;MACvE,CAAC,EAAE,IAAI,CAACA,QAAQ,CAAC;MACjB,IAAI,OAAO,IAAI,CAACyB,EAAE,CAACT,KAAK,KAAK,UAAU,IAAIA,KAAK,EAAE;QAChD;QACA,IAAI,CAACS,EAAE,CAACT,KAAK,EAAE;MACjB;IACF,CAAC,MAAM,IAAIC,SAAS,IAAI,IAAI,EAAE;MAC5B,IAAI,CAACM,KAAK,GAAGb,IAAI,CAACC,KAAK,CAACC,WAAW,CAACC,GAAG,EAAE,CAAC;MAC1C,IAAI,CAACN,QAAQ,GAAG,IAAI;MACpBY,MAAM,CAACF,SAAS,CAAC;IACnB;EACF;EAEA;;;EAGAU,KAAKA,CAAA;IACH,IAAArC,QAAA,CAAAsC,YAAY,EAAC,IAAI,CAACH,EAAE,CAAC;IACrB,IAAI,CAACA,EAAE,GAAGI,SAAS;IACnB,IAAI,CAACtB,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACiB,OAAO,GAAG,IAAI;EACrB;EAEAM,cAAcA,CAAA;IACZ,IAAI,IAAI,CAACvB,QAAQ,EAAE;MACjB;MACA;MACA;MACA,IAAI,CAACwB,IAAI,CAACF,SAAS,EAAEpC,OAAA,CAAAuC,WAAW,CAAC;MACjC,MAAM,IAAItC,YAAY,CAAC,WAAW,EAAE;QAAEM,QAAQ,EAAE,IAAI,CAACA;MAAQ,CAAE,CAAC;IAClE;EACF;EAEO,OAAOiC,OAAOA,CAACjC,QAAgB,EAAEgB,KAAY;IAClD,OAAO,IAAIZ,OAAO,CAACyB,SAAS,EAAE;MAAE7B,QAAQ;MAAEgB;IAAK,CAAE,CAAC;EACpD;EAEA,OAAgBG,MAAMA,CAACF,SAAiB;IACtC,OAAO,IAAIb,OAAO,CAACyB,SAAS,EAAE;MAAE7B,QAAQ,EAAE,CAAC;MAAEgB,KAAK,EAAE,IAAI;MAAEC;IAAS,CAAE,CAAC;EACxE;;AArFFd,OAAA,CAAAC,OAAA,GAAAA,OAAA;AA2GA,SAAS8B,6BAA6BA,CAACC,CAAU;EAC/C,OACEA,CAAC,IAAI,IAAI,IACT,OAAOA,CAAC,KAAK,QAAQ,IACrB,0BAA0B,IAAIA,CAAC,IAC/B,OAAOA,CAAC,CAACC,wBAAwB,KAAK,QAAQ,IAC9C,oBAAoB,IAAID,CAAC,IACzB,OAAOA,CAAC,CAACE,kBAAkB,KAAK,QAAQ;AAE5C;AAEA,SAASC,2BAA2BA,CAACH,CAAU;EAC7C,OACEA,CAAC,IAAI,IAAI,IACT,OAAOA,CAAC,KAAK,QAAQ,IACrB,0BAA0B,IAAIA,CAAC,IAC/B,OAAOA,CAAC,CAACC,wBAAwB,KAAK,QAAQ,IAC9C,WAAW,IAAID,CAAC,IAChB,OAAOA,CAAC,CAACI,SAAS,KAAK,QAAQ;AAEnC;AAEA;AACA,MAAsBC,cAAc;EAClC,OAAOC,MAAMA,CAAC1C,OAA8B;IAC1C,IAAIA,OAAO,CAAC2C,OAAO,EAAEC,cAAc,IAAI,IAAI,EAAE,OAAO5C,OAAO,CAAC2C,OAAO,EAAEC,cAAc;IACnF,IAAIL,2BAA2B,CAACvC,OAAO,CAAC,EAAE,OAAO,IAAI6C,kBAAkB,CAAC7C,OAAO,CAAC,CAAC,KAC5E,IAAImC,6BAA6B,CAACnC,OAAO,CAAC,EAAE,OAAO,IAAI8C,oBAAoB,CAAC9C,OAAO,CAAC,CAAC,KACrF,MAAM,IAAIP,OAAA,CAAAsD,iBAAiB,CAAC,sBAAsB,CAAC;EAC1D;;AANF3C,OAAA,CAAAqC,cAAA,GAAAA,cAAA;AAkCA;AACA,MAAaI,kBAAmB,SAAQJ,cAAc;EAYpD3C,YAAYE,OAAkC;IAC5C,KAAK,EAAE;IAJF,KAAAgD,gBAAgB,GAAG,CAAC;IAKzB,IAAI,CAACtC,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACC,WAAW,CAACC,GAAG,EAAE,CAAC;IAE1C,IAAI,CAAC0B,SAAS,GAAGxC,OAAO,CAACwC,SAAS;IAElC,IAAI,CAACH,wBAAwB,GAAGrC,OAAO,CAACqC,wBAAwB;IAEhE,IAAI,CAACY,eAAe,GAAGjD,OAAO,CAACiD,eAAe;IAE9C,IAAI,CAACC,2BAA2B,GAAG,KAAK;EAC1C;EAEA,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI,CAACC,eAAe,GAAG,IAAI,CAACJ,gBAAgB;EACrD;EAEA,IAAII,eAAeA,CAAA;IACjB,MAAMC,UAAU,GAAG1C,IAAI,CAACC,KAAK,CAACC,WAAW,CAACC,GAAG,EAAE,CAAC,GAAG,IAAI,CAACJ,KAAK;IAC7D,OAAO,IAAI,CAAC8B,SAAS,IAAI,CAAC,GAAG/B,QAAQ,GAAG,IAAI,CAAC+B,SAAS,GAAGa,UAAU;EACrE;EAEAC,WAAWA,CAAA;IACT,OAAO,IAAI;EACb;EAEA,IAAIC,sBAAsBA,CAAA;IACxB;IACA,IAAI,OAAO,IAAI,CAACC,uBAAuB,KAAK,QAAQ,IAAI,IAAI,CAACA,uBAAuB,EAAE/B,OAAO,EAAE;MAC7F,MAAM;QAAE2B,eAAe;QAAEf;MAAwB,CAAE,GAAG,IAAI;MAC1D,IAAIe,eAAe,IAAI,CAAC,EACtB,OAAO/C,OAAO,CAACe,MAAM,CACnB,IAAI3B,OAAA,CAAAgE,0BAA0B,CAAC,uCAAuC,IAAI,CAACjB,SAAS,IAAI,CAAC,CAC1F;MACH,MAAMkB,6BAA6B,GACjCrB,wBAAwB,KAAK,CAAC,IAC9B,IAAA3C,OAAA,CAAAiE,OAAO,EAACP,eAAe,EAAEf,wBAAwB,CAAC,KAAKA,wBAAwB;MACjF,IAAIqB,6BAA6B,EAAE;QACjC,IAAI,CAACF,uBAAuB,GAAGnD,OAAO,CAAC6B,OAAO,CAACG,wBAAwB,CAAC;MAC1E,CAAC,MAAM;QACL,IAAIe,eAAe,GAAG,CAAC,IAAIQ,MAAM,CAACC,QAAQ,CAACT,eAAe,CAAC,EAAE;UAC3D,IAAI,CAACI,uBAAuB,GAAGnD,OAAO,CAAC6B,OAAO,CAACkB,eAAe,CAAC;QACjE,CAAC,MAAM;UACL,IAAI,CAACI,uBAAuB,GAAG,IAAI;QACrC;MACF;IACF;IAEA,OAAO,IAAI,CAACA,uBAAuB;EACrC;EAEA,IAAIM,yBAAyBA,CAAA;IAC3B,IACE,OAAO,IAAI,CAACC,0BAA0B,KAAK,QAAQ,IACnD,IAAI,CAACA,0BAA0B,EAAEtC,OAAO,EACxC;MACA,IAAI,OAAO,IAAI,CAAC+B,uBAAuB,KAAK,QAAQ,EAAE;QACpD;QACA,IAAI,CAACO,0BAA0B,GAAG,IAAI,CAACP,uBAAuB;MAChE,CAAC,MAAM;QACL,MAAM,IAAI/D,OAAA,CAAAsD,iBAAiB,CACzB,oGAAoG,CACrG;MACH;IACF;IACA,OAAO,IAAI,CAACgB,0BAA0B;EACxC;EAEA,IAAIC,qBAAqBA,CAAA;IACvB,MAAM;MAAEZ;IAAe,CAAE,GAAG,IAAI;IAChC,IAAI,CAACQ,MAAM,CAACC,QAAQ,CAACT,eAAe,CAAC,EAAE,OAAO,IAAI;IAClD,IAAIA,eAAe,GAAG,CAAC,EAAE,OAAO/C,OAAO,CAAC6B,OAAO,CAACkB,eAAe,CAAC;IAChE,OAAO/C,OAAO,CAACe,MAAM,CAAC,IAAI3B,OAAA,CAAAgE,0BAA0B,CAAC,+BAA+B,CAAC,CAAC;EACxF;EAEA,IAAIQ,oBAAoBA,CAAA;IACtB,MAAM;MAAEb;IAAe,CAAE,GAAG,IAAI;IAChC,IAAI,CAACQ,MAAM,CAACC,QAAQ,CAACT,eAAe,CAAC,EAAE,OAAO,IAAI;IAClD,IAAIA,eAAe,GAAG,CAAC,EAAE,OAAO/C,OAAO,CAAC6B,OAAO,CAACkB,eAAe,CAAC;IAChE,OAAO/C,OAAO,CAACe,MAAM,CAAC,IAAI3B,OAAA,CAAAgE,0BAA0B,CAAC,8BAA8B,CAAC,CAAC;EACvF;EAEAS,OAAOA,CAAA;IACL,IAAI,CAACxD,KAAK,GAAGC,IAAI,CAACC,KAAK,CAACC,WAAW,CAACC,GAAG,EAAE,CAAC;IAC1C,IAAI,CAACkC,gBAAgB,GAAG,CAAC;IACzB,IAAI,CAACQ,uBAAuB,EAAE5B,KAAK,EAAE;IACrC,IAAI,CAACmC,0BAA0B,EAAEnC,KAAK,EAAE;EAC1C;EAEAA,KAAKA,CAAA;IACH,IAAI,CAAC4B,uBAAuB,EAAE5B,KAAK,EAAE;IACrC,IAAI,CAACmC,0BAA0B,EAAEnC,KAAK,EAAE;EAC1C;EAEA;;;;;EAKAuC,yBAAyBA,CAACpE,OAAgB;IACxC,MAAM;MAAEqD;IAAe,CAAE,GAAG,IAAI;IAChC,IAAIA,eAAe,IAAI,CAAC,EACtB,MAAM,IAAI3D,OAAA,CAAAgE,0BAA0B,CAAC1D,OAAO,IAAI,iBAAiB,IAAI,CAACyC,SAAS,IAAI,CAAC;IACtF,OAAOY,eAAe;EACxB;EAEA;;;;;EAKAgB,KAAKA,CAAA;IACH,MAAMxB,cAAc,GAAG,IAAIC,kBAAkB,CAAC;MAC5CL,SAAS,EAAE,IAAI,CAACA,SAAS;MACzBH,wBAAwB,EAAE,IAAI,CAACA;KAChC,CAAC;IACFO,cAAc,CAAClC,KAAK,GAAG,IAAI,CAACA,KAAK;IACjC,OAAOkC,cAAc;EACvB;EAESyB,SAASA,CAAA;IAChB,OAAO,IAAIxB,kBAAkB,CAAC,IAAI,CAAC;EACrC;EAESyB,qBAAqBA,CAACC,OAAiB,EAAEvE,OAAoC;IACpF,IAAIA,OAAO,CAACwE,aAAa,EAAE;IAC3B,MAAMrB,SAAS,GAAG,IAAI,CAACC,eAAe,GAAG,IAAI,CAACJ,gBAAgB;IAC9D,IAAIG,SAAS,GAAG,CAAC,IAAIS,MAAM,CAACC,QAAQ,CAACV,SAAS,CAAC,EAAEoB,OAAO,CAACpB,SAAS,GAAGA,SAAS;EAChF;EAESsB,kBAAkBA,CAAA;IACzB,OAAO,CAAC;EACV;;AAhJFrE,OAAA,CAAAyC,kBAAA,GAAAA,kBAAA;AAmJA;AACA,MAAaC,oBAAqB,SAAQL,cAAc;EAItD3C,YAAYE,OAAoC;IAC9C,KAAK,EAAE;IACP,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACkD,2BAA2B,GAAG,IAAI;EACzC;EAEAI,WAAWA,CAAA;IACT,OAAO,KAAK;EACd;EAEA,IAAIC,sBAAsBA,CAAA;IACxB,IAAI,IAAI,CAACvD,OAAO,CAACqC,wBAAwB,IAAI,IAAI,IAAI,IAAI,CAACrC,OAAO,CAACqC,wBAAwB,GAAG,CAAC,EAC5F,OAAOhC,OAAO,CAAC6B,OAAO,CAAC,IAAI,CAAClC,OAAO,CAACqC,wBAAwB,CAAC;IAC/D,OAAO,IAAI;EACb;EAEA,IAAIyB,yBAAyBA,CAAA;IAC3B,IAAI,IAAI,CAAC9D,OAAO,CAACsC,kBAAkB,IAAI,IAAI,IAAI,IAAI,CAACtC,OAAO,CAACsC,kBAAkB,GAAG,CAAC,EAChF,OAAOjC,OAAO,CAAC6B,OAAO,CAAC,IAAI,CAAClC,OAAO,CAACsC,kBAAkB,CAAC;IACzD,OAAO,IAAI;EACb;EAEA,IAAI0B,qBAAqBA,CAAA;IACvB,OAAO,IAAI;EACb;EAEA,IAAIC,oBAAoBA,CAAA;IACtB,OAAO,IAAI;EACb;EAEAC,OAAOA,CAAA;IACL;EACF;EAEAtC,KAAKA,CAAA;IACH;EACF;EAEA,IAAIuB,SAASA,CAAA;IACX,OAAO,IAAI;EACb;EAESkB,SAASA,CAAA;IAChB,OAAO,IAAIvB,oBAAoB,CAAC,IAAI,CAAC9C,OAAO,CAAC;EAC/C;EAESsE,qBAAqBA,CAACI,QAAkB,EAAEC,QAAqC;IACtF;EAAA;EAGOF,kBAAkBA,CAAA;IACzB,OAAO,IAAI,CAACzE,OAAO,CAACiD,eAAe;EACrC;;AAxDF7C,OAAA,CAAA0C,oBAAA,GAAAA,oBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}