{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { uncapitalizeObjectKeys } from './slotsMigration';\n\n// TODO v7: Remove `components` and usages of `UncapitalizeObjectKeys` type\n// after converting keys in Grid(Pro|Premium)SlotsComponent to camelCase.\n// https://github.com/mui/mui-x/issues/7940\nexport function computeSlots(_ref) {\n  let {\n    defaultSlots,\n    slots,\n    components\n  } = _ref;\n  const overrides = slots != null ? slots : components ? uncapitalizeObjectKeys(components) : null;\n  if (!overrides || Object.keys(overrides).length === 0) {\n    return defaultSlots;\n  }\n  const result = _extends({}, defaultSlots);\n  Object.keys(overrides).forEach(key => {\n    const k = key;\n    if (overrides[k] !== undefined) {\n      result[k] = overrides[k];\n    }\n  });\n  return result;\n}", "map": {"version": 3, "names": ["_extends", "uncapitalizeObjectKeys", "computeSlots", "_ref", "defaultSlots", "slots", "components", "overrides", "Object", "keys", "length", "result", "for<PERSON>ach", "key", "k", "undefined"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/x-data-grid/internals/utils/computeSlots.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport { uncapitalizeObjectKeys } from './slotsMigration';\n\n// TODO v7: Remove `components` and usages of `UncapitalizeObjectKeys` type\n// after converting keys in Grid(Pro|Premium)SlotsComponent to camelCase.\n// https://github.com/mui/mui-x/issues/7940\nexport function computeSlots({\n  defaultSlots,\n  slots,\n  components\n}) {\n  const overrides = slots != null ? slots : components ? uncapitalizeObjectKeys(components) : null;\n  if (!overrides || Object.keys(overrides).length === 0) {\n    return defaultSlots;\n  }\n  const result = _extends({}, defaultSlots);\n  Object.keys(overrides).forEach(key => {\n    const k = key;\n    if (overrides[k] !== undefined) {\n      result[k] = overrides[k];\n    }\n  });\n  return result;\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,SAASC,sBAAsB,QAAQ,kBAAkB;;AAEzD;AACA;AACA;AACA,OAAO,SAASC,YAAYA,CAAAC,IAAA,EAIzB;EAAA,IAJ0B;IAC3BC,YAAY;IACZC,KAAK;IACLC;EACF,CAAC,GAAAH,IAAA;EACC,MAAMI,SAAS,GAAGF,KAAK,IAAI,IAAI,GAAGA,KAAK,GAAGC,UAAU,GAAGL,sBAAsB,CAACK,UAAU,CAAC,GAAG,IAAI;EAChG,IAAI,CAACC,SAAS,IAAIC,MAAM,CAACC,IAAI,CAACF,SAAS,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;IACrD,OAAON,YAAY;EACrB;EACA,MAAMO,MAAM,GAAGX,QAAQ,CAAC,CAAC,CAAC,EAAEI,YAAY,CAAC;EACzCI,MAAM,CAACC,IAAI,CAACF,SAAS,CAAC,CAACK,OAAO,CAACC,GAAG,IAAI;IACpC,MAAMC,CAAC,GAAGD,GAAG;IACb,IAAIN,SAAS,CAACO,CAAC,CAAC,KAAKC,SAAS,EAAE;MAC9BJ,MAAM,CAACG,CAAC,CAAC,GAAGP,SAAS,CAACO,CAAC,CAAC;IAC1B;EACF,CAAC,CAAC;EACF,OAAOH,MAAM;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}