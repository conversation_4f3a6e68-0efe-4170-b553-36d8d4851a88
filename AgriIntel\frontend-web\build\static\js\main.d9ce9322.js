/*! For license information please see main.d9ce9322.js.LICENSE.txt */
(()=>{var e={43:(e,t,n)=>{"use strict";e.exports=n(202)},153:(e,t,n)=>{"use strict";var r=n(43),a=Symbol.for("react.element"),o=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,o={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)i.call(t,r)&&!l.hasOwnProperty(r)&&(o[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===o[r]&&(o[r]=t[r]);return{$$typeof:a,type:e,key:u,ref:c,props:o,_owner:s.current}}t.Fragment=o,t.jsx=u,t.jsxs=u},202:(e,t)=>{"use strict";var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),o=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},m=Object.assign,g={};function y(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||h}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var w=b.prototype=new v;w.constructor=b,m(w,y.prototype),w.isPureReactComponent=!0;var x=Array.isArray,k=Object.prototype.hasOwnProperty,S={current:null},E={key:!0,ref:!0,__self:!0,__source:!0};function N(e,t,r){var a,o={},i=null,s=null;if(null!=t)for(a in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(i=""+t.key),t)k.call(t,a)&&!E.hasOwnProperty(a)&&(o[a]=t[a]);var l=arguments.length-2;if(1===l)o.children=r;else if(1<l){for(var u=Array(l),c=0;c<l;c++)u[c]=arguments[c+2];o.children=u}if(e&&e.defaultProps)for(a in l=e.defaultProps)void 0===o[a]&&(o[a]=l[a]);return{$$typeof:n,type:e,key:i,ref:s,props:o,_owner:S.current}}function C(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var j=/\/+/g;function O(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function P(e,t,a,o,i){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l=!1;if(null===e)l=!0;else switch(s){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case n:case r:l=!0}}if(l)return i=i(l=e),e=""===o?"."+O(l,0):o,x(i)?(a="",null!=e&&(a=e.replace(j,"$&/")+"/"),P(i,t,a,"",(function(e){return e}))):null!=i&&(C(i)&&(i=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(i,a+(!i.key||l&&l.key===i.key?"":(""+i.key).replace(j,"$&/")+"/")+e)),t.push(i)),1;if(l=0,o=""===o?".":o+":",x(e))for(var u=0;u<e.length;u++){var c=o+O(s=e[u],u);l+=P(s,t,a,c,i)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),u=0;!(s=e.next()).done;)l+=P(s=s.value,t,a,c=o+O(s,u++),i);else if("object"===s)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function R(e,t,n){if(null==e)return e;var r=[],a=0;return P(e,r,"","",(function(e){return t.call(n,e,a++)})),r}function T(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var A={current:null},L={transition:null},I={ReactCurrentDispatcher:A,ReactCurrentBatchConfig:L,ReactCurrentOwner:S};function _(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:R,forEach:function(e,t,n){R(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return R(e,(function(){t++})),t},toArray:function(e){return R(e,(function(e){return e}))||[]},only:function(e){if(!C(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=a,t.Profiler=i,t.PureComponent=b,t.StrictMode=o,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=I,t.act=_,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var a=m({},e.props),o=e.key,i=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(i=t.ref,s=S.current),void 0!==t.key&&(o=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in t)k.call(t,u)&&!E.hasOwnProperty(u)&&(a[u]=void 0===t[u]&&void 0!==l?l[u]:t[u])}var u=arguments.length-2;if(1===u)a.children=r;else if(1<u){l=Array(u);for(var c=0;c<u;c++)l[c]=arguments[c+2];a.children=l}return{$$typeof:n,type:e.type,key:o,ref:i,props:a,_owner:s}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:s,_context:e},e.Consumer=e},t.createElement=N,t.createFactory=function(e){var t=N.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=C,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:T}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=L.transition;L.transition={};try{e()}finally{L.transition=t}},t.unstable_act=_,t.useCallback=function(e,t){return A.current.useCallback(e,t)},t.useContext=function(e){return A.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return A.current.useDeferredValue(e)},t.useEffect=function(e,t){return A.current.useEffect(e,t)},t.useId=function(){return A.current.useId()},t.useImperativeHandle=function(e,t,n){return A.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return A.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return A.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return A.current.useMemo(e,t)},t.useReducer=function(e,t,n){return A.current.useReducer(e,t,n)},t.useRef=function(e){return A.current.useRef(e)},t.useState=function(e){return A.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return A.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return A.current.useTransition()},t.version="18.3.1"},214:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});const r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MIN_SAFE_INTEGER,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.MAX_SAFE_INTEGER;return Math.max(t,Math.min(e,n))}},219:(e,t,n)=>{"use strict";var r=n(763),a={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},o={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},i={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},s={};function l(e){return r.isMemo(e)?i:s[e.$$typeof]||a}s[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},s[r.Memo]=i;var u=Object.defineProperty,c=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,f=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,h=Object.prototype;e.exports=function e(t,n,r){if("string"!==typeof n){if(h){var a=p(n);a&&a!==h&&e(t,a,r)}var i=c(n);d&&(i=i.concat(d(n)));for(var s=l(t),m=l(n),g=0;g<i.length;++g){var y=i[g];if(!o[y]&&(!r||!r[y])&&(!m||!m[y])&&(!s||!s[y])){var v=f(n,y);try{u(t,y,v)}catch(b){}}}}return t}},234:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,a=e[r];if(!(0<o(a,t)))break e;e[r]=t,e[n]=a,n=r}}function r(e){return 0===e.length?null:e[0]}function a(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,a=e.length,i=a>>>1;r<i;){var s=2*(r+1)-1,l=e[s],u=s+1,c=e[u];if(0>o(l,n))u<a&&0>o(c,l)?(e[r]=c,e[u]=n,r=u):(e[r]=l,e[s]=n,r=s);else{if(!(u<a&&0>o(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function o(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var s=Date,l=s.now();t.unstable_now=function(){return s.now()-l}}var u=[],c=[],d=1,f=null,p=3,h=!1,m=!1,g=!1,y="function"===typeof setTimeout?setTimeout:null,v="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function w(e){for(var t=r(c);null!==t;){if(null===t.callback)a(c);else{if(!(t.startTime<=e))break;a(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function x(e){if(g=!1,w(e),!m)if(null!==r(u))m=!0,L(k);else{var t=r(c);null!==t&&I(x,t.startTime-e)}}function k(e,n){m=!1,g&&(g=!1,v(C),C=-1),h=!0;var o=p;try{for(w(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!P());){var i=f.callback;if("function"===typeof i){f.callback=null,p=f.priorityLevel;var s=i(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof s?f.callback=s:f===r(u)&&a(u),w(n)}else a(u);f=r(u)}if(null!==f)var l=!0;else{var d=r(c);null!==d&&I(x,d.startTime-n),l=!1}return l}finally{f=null,p=o,h=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var S,E=!1,N=null,C=-1,j=5,O=-1;function P(){return!(t.unstable_now()-O<j)}function R(){if(null!==N){var e=t.unstable_now();O=e;var n=!0;try{n=N(!0,e)}finally{n?S():(E=!1,N=null)}}else E=!1}if("function"===typeof b)S=function(){b(R)};else if("undefined"!==typeof MessageChannel){var T=new MessageChannel,A=T.port2;T.port1.onmessage=R,S=function(){A.postMessage(null)}}else S=function(){y(R,0)};function L(e){N=e,E||(E=!0,S())}function I(e,n){C=y((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){m||h||(m=!0,L(k))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):j=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,a,o){var i=t.unstable_now();switch("object"===typeof o&&null!==o?o="number"===typeof(o=o.delay)&&0<o?i+o:i:o=i,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:d++,callback:a,priorityLevel:e,startTime:o,expirationTime:s=o+s,sortIndex:-1},o>i?(e.sortIndex=o,n(c,e),null===r(u)&&e===r(c)&&(g?(v(C),C=-1):g=!0,I(x,o-i))):(e.sortIndex=s,n(u,e),m||h||(m=!0,L(k))),e},t.unstable_shouldYield=P,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},266:(e,t,n)=>{"use strict";var r=n(994);t.e$=h,t.eM=function(e,t){const n=f(e),r=f(t);return(Math.max(n,r)+.05)/(Math.min(n,r)+.05)},t.a=m;var a=r(n(457)),o=r(n(214));function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;return(0,o.default)(e,t,n)}function s(e){e=e.slice(1);const t=new RegExp(".{1,".concat(e.length>=6?2:1,"}"),"g");let n=e.match(t);return n&&1===n[0].length&&(n=n.map((e=>e+e))),n?"rgb".concat(4===n.length?"a":"","(").concat(n.map(((e,t)=>t<3?parseInt(e,16):Math.round(parseInt(e,16)/255*1e3)/1e3)).join(", "),")"):""}function l(e){if(e.type)return e;if("#"===e.charAt(0))return l(s(e));const t=e.indexOf("("),n=e.substring(0,t);if(-1===["rgb","rgba","hsl","hsla","color"].indexOf(n))throw new Error((0,a.default)(9,e));let r,o=e.substring(t+1,e.length-1);if("color"===n){if(o=o.split(" "),r=o.shift(),4===o.length&&"/"===o[3].charAt(0)&&(o[3]=o[3].slice(1)),-1===["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].indexOf(r))throw new Error((0,a.default)(10,r))}else o=o.split(",");return o=o.map((e=>parseFloat(e))),{type:n,values:o,colorSpace:r}}const u=e=>{const t=l(e);return t.values.slice(0,3).map(((e,n)=>-1!==t.type.indexOf("hsl")&&0!==n?"".concat(e,"%"):e)).join(" ")};function c(e){const{type:t,colorSpace:n}=e;let{values:r}=e;return-1!==t.indexOf("rgb")?r=r.map(((e,t)=>t<3?parseInt(e,10):e)):-1!==t.indexOf("hsl")&&(r[1]="".concat(r[1],"%"),r[2]="".concat(r[2],"%")),r=-1!==t.indexOf("color")?"".concat(n," ").concat(r.join(" ")):"".concat(r.join(", ")),"".concat(t,"(").concat(r,")")}function d(e){e=l(e);const{values:t}=e,n=t[0],r=t[1]/100,a=t[2]/100,o=r*Math.min(a,1-a),i=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(e+n/30)%12;return a-o*Math.max(Math.min(t-3,9-t,1),-1)};let s="rgb";const u=[Math.round(255*i(0)),Math.round(255*i(8)),Math.round(255*i(4))];return"hsla"===e.type&&(s+="a",u.push(t[3])),c({type:s,values:u})}function f(e){let t="hsl"===(e=l(e)).type||"hsla"===e.type?l(d(e)).values:e.values;return t=t.map((t=>("color"!==e.type&&(t/=255),t<=.03928?t/12.92:((t+.055)/1.055)**2.4))),Number((.2126*t[0]+.7152*t[1]+.0722*t[2]).toFixed(3))}function p(e,t){return e=l(e),t=i(t),"rgb"!==e.type&&"hsl"!==e.type||(e.type+="a"),"color"===e.type?e.values[3]="/".concat(t):e.values[3]=t,c(e)}function h(e,t){if(e=l(e),t=i(t),-1!==e.type.indexOf("hsl"))e.values[2]*=1-t;else if(-1!==e.type.indexOf("rgb")||-1!==e.type.indexOf("color"))for(let n=0;n<3;n+=1)e.values[n]*=1-t;return c(e)}function m(e,t){if(e=l(e),t=i(t),-1!==e.type.indexOf("hsl"))e.values[2]+=(100-e.values[2])*t;else if(-1!==e.type.indexOf("rgb"))for(let n=0;n<3;n+=1)e.values[n]+=(255-e.values[n])*t;else if(-1!==e.type.indexOf("color"))for(let n=0;n<3;n+=1)e.values[n]+=(1-e.values[n])*t;return c(e)}function g(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.15;return f(e)>.5?h(e,t):m(e,t)}},330:(e,t,n)=>{"use strict";var r=n(43);var a="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},o=r.useState,i=r.useEffect,s=r.useLayoutEffect,l=r.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!a(e,n)}catch(r){return!0}}var c="undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=o({inst:{value:n,getSnapshot:t}}),a=r[0].inst,c=r[1];return s((function(){a.value=n,a.getSnapshot=t,u(a)&&c({inst:a})}),[e,n,t]),i((function(){return u(a)&&c({inst:a}),e((function(){u(a)&&c({inst:a})}))}),[e]),l(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},358:(e,t)=>{"use strict";var n,r=Symbol.for("react.element"),a=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),u=Symbol.for("react.context"),c=Symbol.for("react.server_context"),d=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),m=Symbol.for("react.lazy"),g=Symbol.for("react.offscreen");function y(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case o:case s:case i:case f:case p:return e;default:switch(e=e&&e.$$typeof){case c:case u:case d:case m:case h:case l:return e;default:return t}}case a:return t}}}n=Symbol.for("react.module.reference")},391:(e,t,n)=>{"use strict";var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},443:(e,t,n)=>{"use strict";e.exports=n(717)},457:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r.A});var r=n(868)},461:(e,t,n)=>{"use strict";e.exports=n(330)},579:(e,t,n)=>{"use strict";e.exports=n(153)},706:(e,t,n)=>{"use strict";n(358)},717:(e,t,n)=>{"use strict";var r=n(43),a=n(461);var o="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},i=a.useSyncExternalStore,s=r.useRef,l=r.useEffect,u=r.useMemo,c=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,a){var d=s(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;d=u((function(){function e(e){if(!l){if(l=!0,i=e,e=r(e),void 0!==a&&f.hasValue){var t=f.value;if(a(t,e))return s=t}return s=e}if(t=s,o(i,e))return t;var n=r(e);return void 0!==a&&a(t,n)?(i=e,t):(i=e,s=n)}var i,s,l=!1,u=void 0===n?null:n;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]}),[t,n,r,a]);var p=i(e,d[0],d[1]);return l((function(){f.hasValue=!0,f.value=p}),[p]),c(p),p}},730:(e,t,n)=>{"use strict";var r=n(43),a=n(853);function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=new Set,s={};function l(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(s[e]=t,e=0;e<t.length;e++)i.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},h={};function m(e,t,n,r,a,o,i){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=a,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=o,this.removeEmptyString=i}var g={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){g[e]=new m(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];g[t]=new m(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){g[e]=new m(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){g[e]=new m(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){g[e]=new m(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){g[e]=new m(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){g[e]=new m(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){g[e]=new m(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){g[e]=new m(e,5,!1,e.toLowerCase(),null,!1,!1)}));var y=/[\-:]([a-z])/g;function v(e){return e[1].toUpperCase()}function b(e,t,n,r){var a=g.hasOwnProperty(t)?g[t]:null;(null!==a?0!==a.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,a,r)&&(n=null),r||null===a?function(e){return!!d.call(h,e)||!d.call(p,e)&&(f.test(e)?h[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):a.mustUseProperty?e[a.propertyName]=null===n?3!==a.type&&"":n:(t=a.attributeName,r=a.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(a=a.type)||4===a&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(y,v);g[t]=new m(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!1,!1)})),g.xlinkHref=new m("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){g[e]=new m(e,1,!1,e.toLowerCase(),null,!0,!0)}));var w=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,x=Symbol.for("react.element"),k=Symbol.for("react.portal"),S=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),N=Symbol.for("react.profiler"),C=Symbol.for("react.provider"),j=Symbol.for("react.context"),O=Symbol.for("react.forward_ref"),P=Symbol.for("react.suspense"),R=Symbol.for("react.suspense_list"),T=Symbol.for("react.memo"),A=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var L=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var I=Symbol.iterator;function _(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=I&&e[I]||e["@@iterator"])?e:null}var D,F=Object.assign;function M(e){if(void 0===D)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);D=t&&t[1]||""}return"\n"+D+e}var z=!1;function B(e,t){if(!e||z)return"";z=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"===typeof u.stack){for(var a=u.stack.split("\n"),o=r.stack.split("\n"),i=a.length-1,s=o.length-1;1<=i&&0<=s&&a[i]!==o[s];)s--;for(;1<=i&&0<=s;i--,s--)if(a[i]!==o[s]){if(1!==i||1!==s)do{if(i--,0>--s||a[i]!==o[s]){var l="\n"+a[i].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}}while(1<=i&&0<=s);break}}}finally{z=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?M(e):""}function U(e){switch(e.tag){case 5:return M(e.type);case 16:return M("Lazy");case 13:return M("Suspense");case 19:return M("SuspenseList");case 0:case 2:case 15:return e=B(e.type,!1);case 11:return e=B(e.type.render,!1);case 1:return e=B(e.type,!0);default:return""}}function H(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case S:return"Fragment";case k:return"Portal";case N:return"Profiler";case E:return"StrictMode";case P:return"Suspense";case R:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case j:return(e.displayName||"Context")+".Consumer";case C:return(e._context.displayName||"Context")+".Provider";case O:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case T:return null!==(t=e.displayName||null)?t:H(e.type)||"Memo";case A:t=e._payload,e=e._init;try{return H(e(t))}catch(n){}}return null}function V(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return H(t);case 8:return t===E?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function W(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function $(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function K(e){e._valueTracker||(e._valueTracker=function(e){var t=$(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var a=n.get,o=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return a.call(this)},set:function(e){r=""+e,o.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=$(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function Q(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function G(e,t){var n=t.checked;return F({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function J(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=W(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function X(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function Y(e,t){X(e,t);var n=W(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,W(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&Q(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var a=0;a<n.length;a++)t["$"+n[a]]=!0;for(n=0;n<e.length;n++)a=t.hasOwnProperty("$"+e[n].value),e[n].selected!==a&&(e[n].selected=a),a&&r&&(e[n].defaultSelected=!0)}else{for(n=""+W(n),t=null,a=0;a<e.length;a++){if(e[a].value===n)return e[a].selected=!0,void(r&&(e[a].defaultSelected=!0));null!==t||e[a].disabled||(t=e[a])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(o(91));return F({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function ae(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(o(92));if(te(n)){if(1<n.length)throw Error(o(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:W(n)}}function oe(e,t){var n=W(t.value),r=W(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ie(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function se(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function le(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?se(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ce(e,t)}))}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},he=["Webkit","ms","Moz","O"];function me(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function ge(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),a=me(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,a):e[n]=a}}Object.keys(pe).forEach((function(e){he.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]}))}));var ye=F({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ve(e,t){if(t){if(ye[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(o(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(o(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(o(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var we=null;function xe(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var ke=null,Se=null,Ee=null;function Ne(e){if(e=ba(e)){if("function"!==typeof ke)throw Error(o(280));var t=e.stateNode;t&&(t=xa(t),ke(e.stateNode,e.type,t))}}function Ce(e){Se?Ee?Ee.push(e):Ee=[e]:Se=e}function je(){if(Se){var e=Se,t=Ee;if(Ee=Se=null,Ne(e),t)for(e=0;e<t.length;e++)Ne(t[e])}}function Oe(e,t){return e(t)}function Pe(){}var Re=!1;function Te(e,t,n){if(Re)return e(t,n);Re=!0;try{return Oe(e,t,n)}finally{Re=!1,(null!==Se||null!==Ee)&&(Pe(),je())}}function Ae(e,t){var n=e.stateNode;if(null===n)return null;var r=xa(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(o(231,t,typeof n));return n}var Le=!1;if(c)try{var Ie={};Object.defineProperty(Ie,"passive",{get:function(){Le=!0}}),window.addEventListener("test",Ie,Ie),window.removeEventListener("test",Ie,Ie)}catch(ce){Le=!1}function _e(e,t,n,r,a,o,i,s,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var De=!1,Fe=null,Me=!1,ze=null,Be={onError:function(e){De=!0,Fe=e}};function Ue(e,t,n,r,a,o,i,s,l){De=!1,Fe=null,_e.apply(Be,arguments)}function He(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function Ve(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function We(e){if(He(e)!==e)throw Error(o(188))}function $e(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=He(e)))throw Error(o(188));return t!==e?null:e}for(var n=e,r=t;;){var a=n.return;if(null===a)break;var i=a.alternate;if(null===i){if(null!==(r=a.return)){n=r;continue}break}if(a.child===i.child){for(i=a.child;i;){if(i===n)return We(a),e;if(i===r)return We(a),t;i=i.sibling}throw Error(o(188))}if(n.return!==r.return)n=a,r=i;else{for(var s=!1,l=a.child;l;){if(l===n){s=!0,n=a,r=i;break}if(l===r){s=!0,r=a,n=i;break}l=l.sibling}if(!s){for(l=i.child;l;){if(l===n){s=!0,n=i,r=a;break}if(l===r){s=!0,r=i,n=a;break}l=l.sibling}if(!s)throw Error(o(189))}}if(n.alternate!==r)throw Error(o(190))}if(3!==n.tag)throw Error(o(188));return n.stateNode.current===n?e:t}(e))?Ke(e):null}function Ke(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Ke(e);if(null!==t)return t;e=e.sibling}return null}var qe=a.unstable_scheduleCallback,Qe=a.unstable_cancelCallback,Ge=a.unstable_shouldYield,Je=a.unstable_requestPaint,Xe=a.unstable_now,Ye=a.unstable_getCurrentPriorityLevel,Ze=a.unstable_ImmediatePriority,et=a.unstable_UserBlockingPriority,tt=a.unstable_NormalPriority,nt=a.unstable_LowPriority,rt=a.unstable_IdlePriority,at=null,ot=null;var it=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(st(e)/lt|0)|0},st=Math.log,lt=Math.LN2;var ut=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,a=e.suspendedLanes,o=e.pingedLanes,i=268435455&n;if(0!==i){var s=i&~a;0!==s?r=dt(s):0!==(o&=i)&&(r=dt(o))}else 0!==(i=n&~a)?r=dt(i):0!==o&&(r=dt(o));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&a)&&((a=r&-r)>=(o=t&-t)||16===a&&0!==(4194240&o)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)a=1<<(n=31-it(t)),r|=e[n],t&=~a;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ht(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function mt(){var e=ut;return 0===(4194240&(ut<<=1))&&(ut=64),e}function gt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function yt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-it(t)]=n}function vt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-it(n),a=1<<r;a&t|e[r]&t&&(e[r]|=t),n&=~a}}var bt=0;function wt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var xt,kt,St,Et,Nt,Ct=!1,jt=[],Ot=null,Pt=null,Rt=null,Tt=new Map,At=new Map,Lt=[],It="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function _t(e,t){switch(e){case"focusin":case"focusout":Ot=null;break;case"dragenter":case"dragleave":Pt=null;break;case"mouseover":case"mouseout":Rt=null;break;case"pointerover":case"pointerout":Tt.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":At.delete(t.pointerId)}}function Dt(e,t,n,r,a,o){return null===e||e.nativeEvent!==o?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:o,targetContainers:[a]},null!==t&&(null!==(t=ba(t))&&kt(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==a&&-1===t.indexOf(a)&&t.push(a),e)}function Ft(e){var t=va(e.target);if(null!==t){var n=He(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=Ve(n)))return e.blockedOn=t,void Nt(e.priority,(function(){St(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function Mt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Gt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=ba(n))&&kt(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);we=r,n.target.dispatchEvent(r),we=null,t.shift()}return!0}function zt(e,t,n){Mt(e)&&n.delete(t)}function Bt(){Ct=!1,null!==Ot&&Mt(Ot)&&(Ot=null),null!==Pt&&Mt(Pt)&&(Pt=null),null!==Rt&&Mt(Rt)&&(Rt=null),Tt.forEach(zt),At.forEach(zt)}function Ut(e,t){e.blockedOn===t&&(e.blockedOn=null,Ct||(Ct=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,Bt)))}function Ht(e){function t(t){return Ut(t,e)}if(0<jt.length){Ut(jt[0],e);for(var n=1;n<jt.length;n++){var r=jt[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Ot&&Ut(Ot,e),null!==Pt&&Ut(Pt,e),null!==Rt&&Ut(Rt,e),Tt.forEach(t),At.forEach(t),n=0;n<Lt.length;n++)(r=Lt[n]).blockedOn===e&&(r.blockedOn=null);for(;0<Lt.length&&null===(n=Lt[0]).blockedOn;)Ft(n),null===n.blockedOn&&Lt.shift()}var Vt=w.ReactCurrentBatchConfig,Wt=!0;function $t(e,t,n,r){var a=bt,o=Vt.transition;Vt.transition=null;try{bt=1,qt(e,t,n,r)}finally{bt=a,Vt.transition=o}}function Kt(e,t,n,r){var a=bt,o=Vt.transition;Vt.transition=null;try{bt=4,qt(e,t,n,r)}finally{bt=a,Vt.transition=o}}function qt(e,t,n,r){if(Wt){var a=Gt(e,t,n,r);if(null===a)Wr(e,t,r,Qt,n),_t(e,r);else if(function(e,t,n,r,a){switch(t){case"focusin":return Ot=Dt(Ot,e,t,n,r,a),!0;case"dragenter":return Pt=Dt(Pt,e,t,n,r,a),!0;case"mouseover":return Rt=Dt(Rt,e,t,n,r,a),!0;case"pointerover":var o=a.pointerId;return Tt.set(o,Dt(Tt.get(o)||null,e,t,n,r,a)),!0;case"gotpointercapture":return o=a.pointerId,At.set(o,Dt(At.get(o)||null,e,t,n,r,a)),!0}return!1}(a,e,t,n,r))r.stopPropagation();else if(_t(e,r),4&t&&-1<It.indexOf(e)){for(;null!==a;){var o=ba(a);if(null!==o&&xt(o),null===(o=Gt(e,t,n,r))&&Wr(e,t,r,Qt,n),o===a)break;a=o}null!==a&&r.stopPropagation()}else Wr(e,t,r,null,n)}}var Qt=null;function Gt(e,t,n,r){if(Qt=null,null!==(e=va(e=xe(r))))if(null===(t=He(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=Ve(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Qt=e,null}function Jt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ye()){case Ze:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Xt=null,Yt=null,Zt=null;function en(){if(Zt)return Zt;var e,t,n=Yt,r=n.length,a="value"in Xt?Xt.value:Xt.textContent,o=a.length;for(e=0;e<r&&n[e]===a[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===a[o-t];t++);return Zt=a.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function an(e){function t(t,n,r,a,o){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=a,this.target=o,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(a):a[i]);return this.isDefaultPrevented=(null!=a.defaultPrevented?a.defaultPrevented:!1===a.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return F(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var on,sn,ln,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=an(un),dn=F({},un,{view:0,detail:0}),fn=an(dn),pn=F({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Nn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ln&&(ln&&"mousemove"===e.type?(on=e.screenX-ln.screenX,sn=e.screenY-ln.screenY):sn=on=0,ln=e),on)},movementY:function(e){return"movementY"in e?e.movementY:sn}}),hn=an(pn),mn=an(F({},pn,{dataTransfer:0})),gn=an(F({},dn,{relatedTarget:0})),yn=an(F({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),vn=F({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=an(vn),wn=an(F({},un,{data:0})),xn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},kn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Sn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function En(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=Sn[e])&&!!t[e]}function Nn(){return En}var Cn=F({},dn,{key:function(e){if(e.key){var t=xn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?kn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Nn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),jn=an(Cn),On=an(F({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Pn=an(F({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Nn})),Rn=an(F({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),Tn=F({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),An=an(Tn),Ln=[9,13,27,32],In=c&&"CompositionEvent"in window,_n=null;c&&"documentMode"in document&&(_n=document.documentMode);var Dn=c&&"TextEvent"in window&&!_n,Fn=c&&(!In||_n&&8<_n&&11>=_n),Mn=String.fromCharCode(32),zn=!1;function Bn(e,t){switch(e){case"keyup":return-1!==Ln.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Un(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Hn=!1;var Vn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Wn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Vn[e.type]:"textarea"===t}function $n(e,t,n,r){Ce(r),0<(t=Kr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Kn=null,qn=null;function Qn(e){Mr(e,0)}function Gn(e){if(q(wa(e)))return e}function Jn(e,t){if("change"===e)return t}var Xn=!1;if(c){var Yn;if(c){var Zn="oninput"in document;if(!Zn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Zn="function"===typeof er.oninput}Yn=Zn}else Yn=!1;Xn=Yn&&(!document.documentMode||9<document.documentMode)}function tr(){Kn&&(Kn.detachEvent("onpropertychange",nr),qn=Kn=null)}function nr(e){if("value"===e.propertyName&&Gn(qn)){var t=[];$n(t,qn,e,xe(e)),Te(Qn,t)}}function rr(e,t,n){"focusin"===e?(tr(),qn=n,(Kn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function ar(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Gn(qn)}function or(e,t){if("click"===e)return Gn(t)}function ir(e,t){if("input"===e||"change"===e)return Gn(t)}var sr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function lr(e,t){if(sr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var a=n[r];if(!d.call(t,a)||!sr(e[a],t[a]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=Q();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=Q((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function hr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var a=n.textContent.length,o=Math.min(r.start,a);r=void 0===r.end?o:Math.min(r.end,a),!e.extend&&o>r&&(a=r,r=o,o=a),a=cr(n,o);var i=cr(n,r);a&&i&&(1!==e.rangeCount||e.anchorNode!==a.node||e.anchorOffset!==a.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&((t=t.createRange()).setStart(a.node,a.offset),e.removeAllRanges(),o>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var mr=c&&"documentMode"in document&&11>=document.documentMode,gr=null,yr=null,vr=null,br=!1;function wr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==gr||gr!==Q(r)||("selectionStart"in(r=gr)&&pr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},vr&&lr(vr,r)||(vr=r,0<(r=Kr(yr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=gr)))}function xr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var kr={animationend:xr("Animation","AnimationEnd"),animationiteration:xr("Animation","AnimationIteration"),animationstart:xr("Animation","AnimationStart"),transitionend:xr("Transition","TransitionEnd")},Sr={},Er={};function Nr(e){if(Sr[e])return Sr[e];if(!kr[e])return e;var t,n=kr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Er)return Sr[e]=n[t];return e}c&&(Er=document.createElement("div").style,"AnimationEvent"in window||(delete kr.animationend.animation,delete kr.animationiteration.animation,delete kr.animationstart.animation),"TransitionEvent"in window||delete kr.transitionend.transition);var Cr=Nr("animationend"),jr=Nr("animationiteration"),Or=Nr("animationstart"),Pr=Nr("transitionend"),Rr=new Map,Tr="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Ar(e,t){Rr.set(e,t),l(t,[e])}for(var Lr=0;Lr<Tr.length;Lr++){var Ir=Tr[Lr];Ar(Ir.toLowerCase(),"on"+(Ir[0].toUpperCase()+Ir.slice(1)))}Ar(Cr,"onAnimationEnd"),Ar(jr,"onAnimationIteration"),Ar(Or,"onAnimationStart"),Ar("dblclick","onDoubleClick"),Ar("focusin","onFocus"),Ar("focusout","onBlur"),Ar(Pr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),l("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),l("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),l("onBeforeInput",["compositionend","keypress","textInput","paste"]),l("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var _r="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Dr=new Set("cancel close invalid load scroll toggle".split(" ").concat(_r));function Fr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,a,i,s,l,u){if(Ue.apply(this,arguments),De){if(!De)throw Error(o(198));var c=Fe;De=!1,Fe=null,Me||(Me=!0,ze=c)}}(r,t,void 0,e),e.currentTarget=null}function Mr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],a=r.event;r=r.listeners;e:{var o=void 0;if(t)for(var i=r.length-1;0<=i;i--){var s=r[i],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==o&&a.isPropagationStopped())break e;Fr(a,s,u),o=l}else for(i=0;i<r.length;i++){if(l=(s=r[i]).instance,u=s.currentTarget,s=s.listener,l!==o&&a.isPropagationStopped())break e;Fr(a,s,u),o=l}}}if(Me)throw e=ze,Me=!1,ze=null,e}function zr(e,t){var n=t[ma];void 0===n&&(n=t[ma]=new Set);var r=e+"__bubble";n.has(r)||(Vr(t,e,2,!1),n.add(r))}function Br(e,t,n){var r=0;t&&(r|=4),Vr(n,e,r,t)}var Ur="_reactListening"+Math.random().toString(36).slice(2);function Hr(e){if(!e[Ur]){e[Ur]=!0,i.forEach((function(t){"selectionchange"!==t&&(Dr.has(t)||Br(t,!1,e),Br(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Ur]||(t[Ur]=!0,Br("selectionchange",!1,t))}}function Vr(e,t,n,r){switch(Jt(t)){case 1:var a=$t;break;case 4:a=Kt;break;default:a=qt}n=a.bind(null,t,n,e),a=void 0,!Le||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(a=!0),r?void 0!==a?e.addEventListener(t,n,{capture:!0,passive:a}):e.addEventListener(t,n,!0):void 0!==a?e.addEventListener(t,n,{passive:a}):e.addEventListener(t,n,!1)}function Wr(e,t,n,r,a){var o=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var s=r.stateNode.containerInfo;if(s===a||8===s.nodeType&&s.parentNode===a)break;if(4===i)for(i=r.return;null!==i;){var l=i.tag;if((3===l||4===l)&&((l=i.stateNode.containerInfo)===a||8===l.nodeType&&l.parentNode===a))return;i=i.return}for(;null!==s;){if(null===(i=va(s)))return;if(5===(l=i.tag)||6===l){r=o=i;continue e}s=s.parentNode}}r=r.return}Te((function(){var r=o,a=xe(n),i=[];e:{var s=Rr.get(e);if(void 0!==s){var l=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":l=jn;break;case"focusin":u="focus",l=gn;break;case"focusout":u="blur",l=gn;break;case"beforeblur":case"afterblur":l=gn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":l=hn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":l=mn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":l=Pn;break;case Cr:case jr:case Or:l=yn;break;case Pr:l=Rn;break;case"scroll":l=fn;break;case"wheel":l=An;break;case"copy":case"cut":case"paste":l=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":l=On}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==s?s+"Capture":null:s;c=[];for(var p,h=r;null!==h;){var m=(p=h).stateNode;if(5===p.tag&&null!==m&&(p=m,null!==f&&(null!=(m=Ae(h,f))&&c.push($r(h,m,p)))),d)break;h=h.return}0<c.length&&(s=new l(s,u,null,n,a),i.push({event:s,listeners:c}))}}if(0===(7&t)){if(l="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===we||!(u=n.relatedTarget||n.fromElement)||!va(u)&&!u[ha])&&(l||s)&&(s=a.window===a?a:(s=a.ownerDocument)?s.defaultView||s.parentWindow:window,l?(l=r,null!==(u=(u=n.relatedTarget||n.toElement)?va(u):null)&&(u!==(d=He(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(l=null,u=r),l!==u)){if(c=hn,m="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(c=On,m="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==l?s:wa(l),p=null==u?s:wa(u),(s=new c(m,h+"leave",l,n,a)).target=d,s.relatedTarget=p,m=null,va(a)===r&&((c=new c(f,h+"enter",u,n,a)).target=p,c.relatedTarget=d,m=c),d=m,l&&u)e:{for(f=u,h=0,p=c=l;p;p=qr(p))h++;for(p=0,m=f;m;m=qr(m))p++;for(;0<h-p;)c=qr(c),h--;for(;0<p-h;)f=qr(f),p--;for(;h--;){if(c===f||null!==f&&c===f.alternate)break e;c=qr(c),f=qr(f)}c=null}else c=null;null!==l&&Qr(i,s,l,c,!1),null!==u&&null!==d&&Qr(i,d,u,c,!0)}if("select"===(l=(s=r?wa(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===l&&"file"===s.type)var g=Jn;else if(Wn(s))if(Xn)g=ir;else{g=ar;var y=rr}else(l=s.nodeName)&&"input"===l.toLowerCase()&&("checkbox"===s.type||"radio"===s.type)&&(g=or);switch(g&&(g=g(e,r))?$n(i,g,n,a):(y&&y(e,s,r),"focusout"===e&&(y=s._wrapperState)&&y.controlled&&"number"===s.type&&ee(s,"number",s.value)),y=r?wa(r):window,e){case"focusin":(Wn(y)||"true"===y.contentEditable)&&(gr=y,yr=r,vr=null);break;case"focusout":vr=yr=gr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,wr(i,n,a);break;case"selectionchange":if(mr)break;case"keydown":case"keyup":wr(i,n,a)}var v;if(In)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Hn?Bn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Fn&&"ko"!==n.locale&&(Hn||"onCompositionStart"!==b?"onCompositionEnd"===b&&Hn&&(v=en()):(Yt="value"in(Xt=a)?Xt.value:Xt.textContent,Hn=!0)),0<(y=Kr(r,b)).length&&(b=new wn(b,e,null,n,a),i.push({event:b,listeners:y}),v?b.data=v:null!==(v=Un(n))&&(b.data=v))),(v=Dn?function(e,t){switch(e){case"compositionend":return Un(t);case"keypress":return 32!==t.which?null:(zn=!0,Mn);case"textInput":return(e=t.data)===Mn&&zn?null:e;default:return null}}(e,n):function(e,t){if(Hn)return"compositionend"===e||!In&&Bn(e,t)?(e=en(),Zt=Yt=Xt=null,Hn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Fn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Kr(r,"onBeforeInput")).length&&(a=new wn("onBeforeInput","beforeinput",null,n,a),i.push({event:a,listeners:r}),a.data=v))}Mr(i,t)}))}function $r(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Kr(e,t){for(var n=t+"Capture",r=[];null!==e;){var a=e,o=a.stateNode;5===a.tag&&null!==o&&(a=o,null!=(o=Ae(e,n))&&r.unshift($r(e,o,a)),null!=(o=Ae(e,t))&&r.push($r(e,o,a))),e=e.return}return r}function qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Qr(e,t,n,r,a){for(var o=t._reactName,i=[];null!==n&&n!==r;){var s=n,l=s.alternate,u=s.stateNode;if(null!==l&&l===r)break;5===s.tag&&null!==u&&(s=u,a?null!=(l=Ae(n,o))&&i.unshift($r(n,l,s)):a||null!=(l=Ae(n,o))&&i.push($r(n,l,s))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Gr=/\r\n?/g,Jr=/\u0000|\uFFFD/g;function Xr(e){return("string"===typeof e?e:""+e).replace(Gr,"\n").replace(Jr,"")}function Yr(e,t,n){if(t=Xr(t),Xr(e)!==t&&n)throw Error(o(425))}function Zr(){}var ea=null,ta=null;function na(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ra="function"===typeof setTimeout?setTimeout:void 0,aa="function"===typeof clearTimeout?clearTimeout:void 0,oa="function"===typeof Promise?Promise:void 0,ia="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof oa?function(e){return oa.resolve(null).then(e).catch(sa)}:ra;function sa(e){setTimeout((function(){throw e}))}function la(e,t){var n=t,r=0;do{var a=n.nextSibling;if(e.removeChild(n),a&&8===a.nodeType)if("/$"===(n=a.data)){if(0===r)return e.removeChild(a),void Ht(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=a}while(n);Ht(t)}function ua(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function ca(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var da=Math.random().toString(36).slice(2),fa="__reactFiber$"+da,pa="__reactProps$"+da,ha="__reactContainer$"+da,ma="__reactEvents$"+da,ga="__reactListeners$"+da,ya="__reactHandles$"+da;function va(e){var t=e[fa];if(t)return t;for(var n=e.parentNode;n;){if(t=n[ha]||n[fa]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=ca(e);null!==e;){if(n=e[fa])return n;e=ca(e)}return t}n=(e=n).parentNode}return null}function ba(e){return!(e=e[fa]||e[ha])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function wa(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(o(33))}function xa(e){return e[pa]||null}var ka=[],Sa=-1;function Ea(e){return{current:e}}function Na(e){0>Sa||(e.current=ka[Sa],ka[Sa]=null,Sa--)}function Ca(e,t){Sa++,ka[Sa]=e.current,e.current=t}var ja={},Oa=Ea(ja),Pa=Ea(!1),Ra=ja;function Ta(e,t){var n=e.type.contextTypes;if(!n)return ja;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var a,o={};for(a in n)o[a]=t[a];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=o),o}function Aa(e){return null!==(e=e.childContextTypes)&&void 0!==e}function La(){Na(Pa),Na(Oa)}function Ia(e,t,n){if(Oa.current!==ja)throw Error(o(168));Ca(Oa,t),Ca(Pa,n)}function _a(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var a in r=r.getChildContext())if(!(a in t))throw Error(o(108,V(e)||"Unknown",a));return F({},n,r)}function Da(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||ja,Ra=Oa.current,Ca(Oa,e),Ca(Pa,Pa.current),!0}function Fa(e,t,n){var r=e.stateNode;if(!r)throw Error(o(169));n?(e=_a(e,t,Ra),r.__reactInternalMemoizedMergedChildContext=e,Na(Pa),Na(Oa),Ca(Oa,e)):Na(Pa),Ca(Pa,n)}var Ma=null,za=!1,Ba=!1;function Ua(e){null===Ma?Ma=[e]:Ma.push(e)}function Ha(){if(!Ba&&null!==Ma){Ba=!0;var e=0,t=bt;try{var n=Ma;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Ma=null,za=!1}catch(a){throw null!==Ma&&(Ma=Ma.slice(e+1)),qe(Ze,Ha),a}finally{bt=t,Ba=!1}}return null}var Va=[],Wa=0,$a=null,Ka=0,qa=[],Qa=0,Ga=null,Ja=1,Xa="";function Ya(e,t){Va[Wa++]=Ka,Va[Wa++]=$a,$a=e,Ka=t}function Za(e,t,n){qa[Qa++]=Ja,qa[Qa++]=Xa,qa[Qa++]=Ga,Ga=e;var r=Ja;e=Xa;var a=32-it(r)-1;r&=~(1<<a),n+=1;var o=32-it(t)+a;if(30<o){var i=a-a%5;o=(r&(1<<i)-1).toString(32),r>>=i,a-=i,Ja=1<<32-it(t)+a|n<<a|r,Xa=o+e}else Ja=1<<o|n<<a|r,Xa=e}function eo(e){null!==e.return&&(Ya(e,1),Za(e,1,0))}function to(e){for(;e===$a;)$a=Va[--Wa],Va[Wa]=null,Ka=Va[--Wa],Va[Wa]=null;for(;e===Ga;)Ga=qa[--Qa],qa[Qa]=null,Xa=qa[--Qa],qa[Qa]=null,Ja=qa[--Qa],qa[Qa]=null}var no=null,ro=null,ao=!1,oo=null;function io(e,t){var n=Tu(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function so(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,no=e,ro=ua(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,no=e,ro=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Ga?{id:Ja,overflow:Xa}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Tu(18,null,null,0)).stateNode=t,n.return=e,e.child=n,no=e,ro=null,!0);default:return!1}}function lo(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function uo(e){if(ao){var t=ro;if(t){var n=t;if(!so(e,t)){if(lo(e))throw Error(o(418));t=ua(n.nextSibling);var r=no;t&&so(e,t)?io(r,n):(e.flags=-4097&e.flags|2,ao=!1,no=e)}}else{if(lo(e))throw Error(o(418));e.flags=-4097&e.flags|2,ao=!1,no=e}}}function co(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;no=e}function fo(e){if(e!==no)return!1;if(!ao)return co(e),ao=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!na(e.type,e.memoizedProps)),t&&(t=ro)){if(lo(e))throw po(),Error(o(418));for(;t;)io(e,t),t=ua(t.nextSibling)}if(co(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){ro=ua(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}ro=null}}else ro=no?ua(e.stateNode.nextSibling):null;return!0}function po(){for(var e=ro;e;)e=ua(e.nextSibling)}function ho(){ro=no=null,ao=!1}function mo(e){null===oo?oo=[e]:oo.push(e)}var go=w.ReactCurrentBatchConfig;function yo(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(o(309));var r=n.stateNode}if(!r)throw Error(o(147,e));var a=r,i=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===i?t.ref:(t=function(e){var t=a.refs;null===e?delete t[i]:t[i]=e},t._stringRef=i,t)}if("string"!==typeof e)throw Error(o(284));if(!n._owner)throw Error(o(290,e))}return e}function vo(e,t){throw e=Object.prototype.toString.call(t),Error(o(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function bo(e){return(0,e._init)(e._payload)}function wo(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function a(e,t){return(e=Lu(e,t)).index=0,e.sibling=null,e}function i(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function s(t){return e&&null===t.alternate&&(t.flags|=2),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Fu(n,e.mode,r)).return=e,t):((t=a(t,n)).return=e,t)}function u(e,t,n,r){var o=n.type;return o===S?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===o||"object"===typeof o&&null!==o&&o.$$typeof===A&&bo(o)===t.type)?((r=a(t,n.props)).ref=yo(e,t,n),r.return=e,r):((r=Iu(n.type,n.key,n.props,null,e.mode,r)).ref=yo(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Mu(n,e.mode,r)).return=e,t):((t=a(t,n.children||[])).return=e,t)}function d(e,t,n,r,o){return null===t||7!==t.tag?((t=_u(n,e.mode,r,o)).return=e,t):((t=a(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Fu(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case x:return(n=Iu(t.type,t.key,t.props,null,e.mode,n)).ref=yo(e,null,t),n.return=e,n;case k:return(t=Mu(t,e.mode,n)).return=e,t;case A:return f(e,(0,t._init)(t._payload),n)}if(te(t)||_(t))return(t=_u(t,e.mode,n,null)).return=e,t;vo(e,t)}return null}function p(e,t,n,r){var a=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==a?null:l(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case x:return n.key===a?u(e,t,n,r):null;case k:return n.key===a?c(e,t,n,r):null;case A:return p(e,t,(a=n._init)(n._payload),r)}if(te(n)||_(n))return null!==a?null:d(e,t,n,r,null);vo(e,n)}return null}function h(e,t,n,r,a){if("string"===typeof r&&""!==r||"number"===typeof r)return l(t,e=e.get(n)||null,""+r,a);if("object"===typeof r&&null!==r){switch(r.$$typeof){case x:return u(t,e=e.get(null===r.key?n:r.key)||null,r,a);case k:return c(t,e=e.get(null===r.key?n:r.key)||null,r,a);case A:return h(e,t,n,(0,r._init)(r._payload),a)}if(te(r)||_(r))return d(t,e=e.get(n)||null,r,a,null);vo(t,r)}return null}function m(a,o,s,l){for(var u=null,c=null,d=o,m=o=0,g=null;null!==d&&m<s.length;m++){d.index>m?(g=d,d=null):g=d.sibling;var y=p(a,d,s[m],l);if(null===y){null===d&&(d=g);break}e&&d&&null===y.alternate&&t(a,d),o=i(y,o,m),null===c?u=y:c.sibling=y,c=y,d=g}if(m===s.length)return n(a,d),ao&&Ya(a,m),u;if(null===d){for(;m<s.length;m++)null!==(d=f(a,s[m],l))&&(o=i(d,o,m),null===c?u=d:c.sibling=d,c=d);return ao&&Ya(a,m),u}for(d=r(a,d);m<s.length;m++)null!==(g=h(d,a,m,s[m],l))&&(e&&null!==g.alternate&&d.delete(null===g.key?m:g.key),o=i(g,o,m),null===c?u=g:c.sibling=g,c=g);return e&&d.forEach((function(e){return t(a,e)})),ao&&Ya(a,m),u}function g(a,s,l,u){var c=_(l);if("function"!==typeof c)throw Error(o(150));if(null==(l=c.call(l)))throw Error(o(151));for(var d=c=null,m=s,g=s=0,y=null,v=l.next();null!==m&&!v.done;g++,v=l.next()){m.index>g?(y=m,m=null):y=m.sibling;var b=p(a,m,v.value,u);if(null===b){null===m&&(m=y);break}e&&m&&null===b.alternate&&t(a,m),s=i(b,s,g),null===d?c=b:d.sibling=b,d=b,m=y}if(v.done)return n(a,m),ao&&Ya(a,g),c;if(null===m){for(;!v.done;g++,v=l.next())null!==(v=f(a,v.value,u))&&(s=i(v,s,g),null===d?c=v:d.sibling=v,d=v);return ao&&Ya(a,g),c}for(m=r(a,m);!v.done;g++,v=l.next())null!==(v=h(m,a,g,v.value,u))&&(e&&null!==v.alternate&&m.delete(null===v.key?g:v.key),s=i(v,s,g),null===d?c=v:d.sibling=v,d=v);return e&&m.forEach((function(e){return t(a,e)})),ao&&Ya(a,g),c}return function e(r,o,i,l){if("object"===typeof i&&null!==i&&i.type===S&&null===i.key&&(i=i.props.children),"object"===typeof i&&null!==i){switch(i.$$typeof){case x:e:{for(var u=i.key,c=o;null!==c;){if(c.key===u){if((u=i.type)===S){if(7===c.tag){n(r,c.sibling),(o=a(c,i.props.children)).return=r,r=o;break e}}else if(c.elementType===u||"object"===typeof u&&null!==u&&u.$$typeof===A&&bo(u)===c.type){n(r,c.sibling),(o=a(c,i.props)).ref=yo(r,c,i),o.return=r,r=o;break e}n(r,c);break}t(r,c),c=c.sibling}i.type===S?((o=_u(i.props.children,r.mode,l,i.key)).return=r,r=o):((l=Iu(i.type,i.key,i.props,null,r.mode,l)).ref=yo(r,o,i),l.return=r,r=l)}return s(r);case k:e:{for(c=i.key;null!==o;){if(o.key===c){if(4===o.tag&&o.stateNode.containerInfo===i.containerInfo&&o.stateNode.implementation===i.implementation){n(r,o.sibling),(o=a(o,i.children||[])).return=r,r=o;break e}n(r,o);break}t(r,o),o=o.sibling}(o=Mu(i,r.mode,l)).return=r,r=o}return s(r);case A:return e(r,o,(c=i._init)(i._payload),l)}if(te(i))return m(r,o,i,l);if(_(i))return g(r,o,i,l);vo(r,i)}return"string"===typeof i&&""!==i||"number"===typeof i?(i=""+i,null!==o&&6===o.tag?(n(r,o.sibling),(o=a(o,i)).return=r,r=o):(n(r,o),(o=Fu(i,r.mode,l)).return=r,r=o),s(r)):n(r,o)}}var xo=wo(!0),ko=wo(!1),So=Ea(null),Eo=null,No=null,Co=null;function jo(){Co=No=Eo=null}function Oo(e){var t=So.current;Na(So),e._currentValue=t}function Po(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Ro(e,t){Eo=e,Co=No=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bs=!0),e.firstContext=null)}function To(e){var t=e._currentValue;if(Co!==e)if(e={context:e,memoizedValue:t,next:null},null===No){if(null===Eo)throw Error(o(308));No=e,Eo.dependencies={lanes:0,firstContext:e}}else No=No.next=e;return t}var Ao=null;function Lo(e){null===Ao?Ao=[e]:Ao.push(e)}function Io(e,t,n,r){var a=t.interleaved;return null===a?(n.next=n,Lo(t)):(n.next=a.next,a.next=n),t.interleaved=n,_o(e,r)}function _o(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Do=!1;function Fo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function Mo(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function zo(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Bo(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Ol)){var a=r.pending;return null===a?t.next=t:(t.next=a.next,a.next=t),r.pending=t,_o(e,n)}return null===(a=r.interleaved)?(t.next=t,Lo(r)):(t.next=a.next,a.next=t),r.interleaved=t,_o(e,n)}function Uo(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}function Ho(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var a=null,o=null;if(null!==(n=n.firstBaseUpdate)){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===o?a=o=i:o=o.next=i,n=n.next}while(null!==n);null===o?a=o=t:o=o.next=t}else a=o=t;return n={baseState:r.baseState,firstBaseUpdate:a,lastBaseUpdate:o,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Vo(e,t,n,r){var a=e.updateQueue;Do=!1;var o=a.firstBaseUpdate,i=a.lastBaseUpdate,s=a.shared.pending;if(null!==s){a.shared.pending=null;var l=s,u=l.next;l.next=null,null===i?o=u:i.next=u,i=l;var c=e.alternate;null!==c&&((s=(c=c.updateQueue).lastBaseUpdate)!==i&&(null===s?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=l))}if(null!==o){var d=a.baseState;for(i=0,c=u=l=null,s=o;;){var f=s.lane,p=s.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var h=e,m=s;switch(f=t,p=n,m.tag){case 1:if("function"===typeof(h=m.payload)){d=h.call(p,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(f="function"===typeof(h=m.payload)?h.call(p,d,f):h)||void 0===f)break e;d=F({},d,f);break e;case 2:Do=!0}}null!==s.callback&&0!==s.lane&&(e.flags|=64,null===(f=a.effects)?a.effects=[s]:f.push(s))}else p={eventTime:p,lane:f,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===c?(u=c=p,l=d):c=c.next=p,i|=f;if(null===(s=s.next)){if(null===(s=a.shared.pending))break;s=(f=s).next,f.next=null,a.lastBaseUpdate=f,a.shared.pending=null}}if(null===c&&(l=d),a.baseState=l,a.firstBaseUpdate=u,a.lastBaseUpdate=c,null!==(t=a.shared.interleaved)){a=t;do{i|=a.lane,a=a.next}while(a!==t)}else null===o&&(a.shared.lanes=0);Dl|=i,e.lanes=i,e.memoizedState=d}}function Wo(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],a=r.callback;if(null!==a){if(r.callback=null,r=n,"function"!==typeof a)throw Error(o(191,a));a.call(r)}}}var $o={},Ko=Ea($o),qo=Ea($o),Qo=Ea($o);function Go(e){if(e===$o)throw Error(o(174));return e}function Jo(e,t){switch(Ca(Qo,t),Ca(qo,e),Ca(Ko,$o),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:le(null,"");break;default:t=le(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Na(Ko),Ca(Ko,t)}function Xo(){Na(Ko),Na(qo),Na(Qo)}function Yo(e){Go(Qo.current);var t=Go(Ko.current),n=le(t,e.type);t!==n&&(Ca(qo,e),Ca(Ko,n))}function Zo(e){qo.current===e&&(Na(Ko),Na(qo))}var ei=Ea(0);function ti(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ni=[];function ri(){for(var e=0;e<ni.length;e++)ni[e]._workInProgressVersionPrimary=null;ni.length=0}var ai=w.ReactCurrentDispatcher,oi=w.ReactCurrentBatchConfig,ii=0,si=null,li=null,ui=null,ci=!1,di=!1,fi=0,pi=0;function hi(){throw Error(o(321))}function mi(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!sr(e[n],t[n]))return!1;return!0}function gi(e,t,n,r,a,i){if(ii=i,si=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ai.current=null===e||null===e.memoizedState?Zi:es,e=n(r,a),di){i=0;do{if(di=!1,fi=0,25<=i)throw Error(o(301));i+=1,ui=li=null,t.updateQueue=null,ai.current=ts,e=n(r,a)}while(di)}if(ai.current=Yi,t=null!==li&&null!==li.next,ii=0,ui=li=si=null,ci=!1,t)throw Error(o(300));return e}function yi(){var e=0!==fi;return fi=0,e}function vi(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ui?si.memoizedState=ui=e:ui=ui.next=e,ui}function bi(){if(null===li){var e=si.alternate;e=null!==e?e.memoizedState:null}else e=li.next;var t=null===ui?si.memoizedState:ui.next;if(null!==t)ui=t,li=e;else{if(null===e)throw Error(o(310));e={memoizedState:(li=e).memoizedState,baseState:li.baseState,baseQueue:li.baseQueue,queue:li.queue,next:null},null===ui?si.memoizedState=ui=e:ui=ui.next=e}return ui}function wi(e,t){return"function"===typeof t?t(e):t}function xi(e){var t=bi(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=li,a=r.baseQueue,i=n.pending;if(null!==i){if(null!==a){var s=a.next;a.next=i.next,i.next=s}r.baseQueue=a=i,n.pending=null}if(null!==a){i=a.next,r=r.baseState;var l=s=null,u=null,c=i;do{var d=c.lane;if((ii&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(l=u=f,s=r):u=u.next=f,si.lanes|=d,Dl|=d}c=c.next}while(null!==c&&c!==i);null===u?s=r:u.next=l,sr(r,t.memoizedState)||(bs=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){a=e;do{i=a.lane,si.lanes|=i,Dl|=i,a=a.next}while(a!==e)}else null===a&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function ki(e){var t=bi(),n=t.queue;if(null===n)throw Error(o(311));n.lastRenderedReducer=e;var r=n.dispatch,a=n.pending,i=t.memoizedState;if(null!==a){n.pending=null;var s=a=a.next;do{i=e(i,s.action),s=s.next}while(s!==a);sr(i,t.memoizedState)||(bs=!0),t.memoizedState=i,null===t.baseQueue&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function Si(){}function Ei(e,t){var n=si,r=bi(),a=t(),i=!sr(r.memoizedState,a);if(i&&(r.memoizedState=a,bs=!0),r=r.queue,Di(ji.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||null!==ui&&1&ui.memoizedState.tag){if(n.flags|=2048,Ti(9,Ci.bind(null,n,r,a,t),void 0,null),null===Pl)throw Error(o(349));0!==(30&ii)||Ni(n,t,a)}return a}function Ni(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=si.updateQueue)?(t={lastEffect:null,stores:null},si.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ci(e,t,n,r){t.value=n,t.getSnapshot=r,Oi(t)&&Pi(e)}function ji(e,t,n){return n((function(){Oi(t)&&Pi(e)}))}function Oi(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!sr(e,n)}catch(r){return!0}}function Pi(e){var t=_o(e,1);null!==t&&nu(t,e,1,-1)}function Ri(e){var t=vi();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:wi,lastRenderedState:e},t.queue=e,e=e.dispatch=Qi.bind(null,si,e),[t.memoizedState,e]}function Ti(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=si.updateQueue)?(t={lastEffect:null,stores:null},si.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ai(){return bi().memoizedState}function Li(e,t,n,r){var a=vi();si.flags|=e,a.memoizedState=Ti(1|t,n,void 0,void 0===r?null:r)}function Ii(e,t,n,r){var a=bi();r=void 0===r?null:r;var o=void 0;if(null!==li){var i=li.memoizedState;if(o=i.destroy,null!==r&&mi(r,i.deps))return void(a.memoizedState=Ti(t,n,o,r))}si.flags|=e,a.memoizedState=Ti(1|t,n,o,r)}function _i(e,t){return Li(8390656,8,e,t)}function Di(e,t){return Ii(2048,8,e,t)}function Fi(e,t){return Ii(4,2,e,t)}function Mi(e,t){return Ii(4,4,e,t)}function zi(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Bi(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Ii(4,4,zi.bind(null,t,e),n)}function Ui(){}function Hi(e,t){var n=bi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&mi(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Vi(e,t){var n=bi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&mi(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Wi(e,t,n){return 0===(21&ii)?(e.baseState&&(e.baseState=!1,bs=!0),e.memoizedState=n):(sr(n,t)||(n=mt(),si.lanes|=n,Dl|=n,e.baseState=!0),t)}function $i(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=oi.transition;oi.transition={};try{e(!1),t()}finally{bt=n,oi.transition=r}}function Ki(){return bi().memoizedState}function qi(e,t,n){var r=tu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Gi(e))Ji(t,n);else if(null!==(n=Io(e,t,n,r))){nu(n,e,r,eu()),Xi(n,t,r)}}function Qi(e,t,n){var r=tu(e),a={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Gi(e))Ji(t,a);else{var o=e.alternate;if(0===e.lanes&&(null===o||0===o.lanes)&&null!==(o=t.lastRenderedReducer))try{var i=t.lastRenderedState,s=o(i,n);if(a.hasEagerState=!0,a.eagerState=s,sr(s,i)){var l=t.interleaved;return null===l?(a.next=a,Lo(t)):(a.next=l.next,l.next=a),void(t.interleaved=a)}}catch(u){}null!==(n=Io(e,t,a,r))&&(nu(n,e,r,a=eu()),Xi(n,t,r))}}function Gi(e){var t=e.alternate;return e===si||null!==t&&t===si}function Ji(e,t){di=ci=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Xi(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}var Yi={readContext:To,useCallback:hi,useContext:hi,useEffect:hi,useImperativeHandle:hi,useInsertionEffect:hi,useLayoutEffect:hi,useMemo:hi,useReducer:hi,useRef:hi,useState:hi,useDebugValue:hi,useDeferredValue:hi,useTransition:hi,useMutableSource:hi,useSyncExternalStore:hi,useId:hi,unstable_isNewReconciler:!1},Zi={readContext:To,useCallback:function(e,t){return vi().memoizedState=[e,void 0===t?null:t],e},useContext:To,useEffect:_i,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,Li(4194308,4,zi.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Li(4194308,4,e,t)},useInsertionEffect:function(e,t){return Li(4,2,e,t)},useMemo:function(e,t){var n=vi();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=vi();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=qi.bind(null,si,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},vi().memoizedState=e},useState:Ri,useDebugValue:Ui,useDeferredValue:function(e){return vi().memoizedState=e},useTransition:function(){var e=Ri(!1),t=e[0];return e=$i.bind(null,e[1]),vi().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=si,a=vi();if(ao){if(void 0===n)throw Error(o(407));n=n()}else{if(n=t(),null===Pl)throw Error(o(349));0!==(30&ii)||Ni(r,t,n)}a.memoizedState=n;var i={value:n,getSnapshot:t};return a.queue=i,_i(ji.bind(null,r,i,e),[e]),r.flags|=2048,Ti(9,Ci.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=vi(),t=Pl.identifierPrefix;if(ao){var n=Xa;t=":"+t+"R"+(n=(Ja&~(1<<32-it(Ja)-1)).toString(32)+n),0<(n=fi++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=pi++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},es={readContext:To,useCallback:Hi,useContext:To,useEffect:Di,useImperativeHandle:Bi,useInsertionEffect:Fi,useLayoutEffect:Mi,useMemo:Vi,useReducer:xi,useRef:Ai,useState:function(){return xi(wi)},useDebugValue:Ui,useDeferredValue:function(e){return Wi(bi(),li.memoizedState,e)},useTransition:function(){return[xi(wi)[0],bi().memoizedState]},useMutableSource:Si,useSyncExternalStore:Ei,useId:Ki,unstable_isNewReconciler:!1},ts={readContext:To,useCallback:Hi,useContext:To,useEffect:Di,useImperativeHandle:Bi,useInsertionEffect:Fi,useLayoutEffect:Mi,useMemo:Vi,useReducer:ki,useRef:Ai,useState:function(){return ki(wi)},useDebugValue:Ui,useDeferredValue:function(e){var t=bi();return null===li?t.memoizedState=e:Wi(t,li.memoizedState,e)},useTransition:function(){return[ki(wi)[0],bi().memoizedState]},useMutableSource:Si,useSyncExternalStore:Ei,useId:Ki,unstable_isNewReconciler:!1};function ns(e,t){if(e&&e.defaultProps){for(var n in t=F({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function rs(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:F({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var as={isMounted:function(e){return!!(e=e._reactInternals)&&He(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),o=zo(r,a);o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Bo(e,o,a))&&(nu(t,e,a,r),Uo(t,e,a))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=eu(),a=tu(e),o=zo(r,a);o.tag=1,o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Bo(e,o,a))&&(nu(t,e,a,r),Uo(t,e,a))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=eu(),r=tu(e),a=zo(n,r);a.tag=2,void 0!==t&&null!==t&&(a.callback=t),null!==(t=Bo(e,a,r))&&(nu(t,e,r,n),Uo(t,e,r))}};function os(e,t,n,r,a,o,i){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,o,i):!t.prototype||!t.prototype.isPureReactComponent||(!lr(n,r)||!lr(a,o))}function is(e,t,n){var r=!1,a=ja,o=t.contextType;return"object"===typeof o&&null!==o?o=To(o):(a=Aa(t)?Ra:Oa.current,o=(r=null!==(r=t.contextTypes)&&void 0!==r)?Ta(e,a):ja),t=new t(n,o),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=as,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=a,e.__reactInternalMemoizedMaskedChildContext=o),t}function ss(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&as.enqueueReplaceState(t,t.state,null)}function ls(e,t,n,r){var a=e.stateNode;a.props=n,a.state=e.memoizedState,a.refs={},Fo(e);var o=t.contextType;"object"===typeof o&&null!==o?a.context=To(o):(o=Aa(t)?Ra:Oa.current,a.context=Ta(e,o)),a.state=e.memoizedState,"function"===typeof(o=t.getDerivedStateFromProps)&&(rs(e,t,o,n),a.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(t=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),t!==a.state&&as.enqueueReplaceState(a,a.state,null),Vo(e,n,a,r),a.state=e.memoizedState),"function"===typeof a.componentDidMount&&(e.flags|=4194308)}function us(e,t){try{var n="",r=t;do{n+=U(r),r=r.return}while(r);var a=n}catch(o){a="\nError generating stack: "+o.message+"\n"+o.stack}return{value:e,source:t,stack:a,digest:null}}function cs(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function ds(e,t){try{console.error(t.value)}catch(n){setTimeout((function(){throw n}))}}var fs="function"===typeof WeakMap?WeakMap:Map;function ps(e,t,n){(n=zo(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Wl||(Wl=!0,$l=r),ds(0,t)},n}function hs(e,t,n){(n=zo(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var a=t.value;n.payload=function(){return r(a)},n.callback=function(){ds(0,t)}}var o=e.stateNode;return null!==o&&"function"===typeof o.componentDidCatch&&(n.callback=function(){ds(0,t),"function"!==typeof r&&(null===Kl?Kl=new Set([this]):Kl.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function ms(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fs;var a=new Set;r.set(t,a)}else void 0===(a=r.get(t))&&(a=new Set,r.set(t,a));a.has(n)||(a.add(n),e=Nu.bind(null,e,t,n),t.then(e,e))}function gs(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function ys(e,t,n,r,a){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=zo(-1,1)).tag=2,Bo(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=a,e)}var vs=w.ReactCurrentOwner,bs=!1;function ws(e,t,n,r){t.child=null===e?ko(t,null,n,r):xo(t,e.child,n,r)}function xs(e,t,n,r,a){n=n.render;var o=t.ref;return Ro(t,a),r=gi(e,t,n,r,o,a),n=yi(),null===e||bs?(ao&&n&&eo(t),t.flags|=1,ws(e,t,r,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Ws(e,t,a))}function ks(e,t,n,r,a){if(null===e){var o=n.type;return"function"!==typeof o||Au(o)||void 0!==o.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=Iu(n.type,null,r,t,t.mode,a)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=o,Ss(e,t,o,r,a))}if(o=e.child,0===(e.lanes&a)){var i=o.memoizedProps;if((n=null!==(n=n.compare)?n:lr)(i,r)&&e.ref===t.ref)return Ws(e,t,a)}return t.flags|=1,(e=Lu(o,r)).ref=t.ref,e.return=t,t.child=e}function Ss(e,t,n,r,a){if(null!==e){var o=e.memoizedProps;if(lr(o,r)&&e.ref===t.ref){if(bs=!1,t.pendingProps=r=o,0===(e.lanes&a))return t.lanes=e.lanes,Ws(e,t,a);0!==(131072&e.flags)&&(bs=!0)}}return Cs(e,t,n,r,a)}function Es(e,t,n){var r=t.pendingProps,a=r.children,o=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Ca(Ll,Al),Al|=n;else{if(0===(1073741824&n))return e=null!==o?o.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Ca(Ll,Al),Al|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==o?o.baseLanes:n,Ca(Ll,Al),Al|=r}else null!==o?(r=o.baseLanes|n,t.memoizedState=null):r=n,Ca(Ll,Al),Al|=r;return ws(e,t,a,n),t.child}function Ns(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Cs(e,t,n,r,a){var o=Aa(n)?Ra:Oa.current;return o=Ta(t,o),Ro(t,a),n=gi(e,t,n,r,o,a),r=yi(),null===e||bs?(ao&&r&&eo(t),t.flags|=1,ws(e,t,n,a),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a,Ws(e,t,a))}function js(e,t,n,r,a){if(Aa(n)){var o=!0;Da(t)}else o=!1;if(Ro(t,a),null===t.stateNode)Vs(e,t),is(t,n,r),ls(t,n,r,a),r=!0;else if(null===e){var i=t.stateNode,s=t.memoizedProps;i.props=s;var l=i.context,u=n.contextType;"object"===typeof u&&null!==u?u=To(u):u=Ta(t,u=Aa(n)?Ra:Oa.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof i.getSnapshotBeforeUpdate;d||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(s!==r||l!==u)&&ss(t,i,r,u),Do=!1;var f=t.memoizedState;i.state=f,Vo(t,r,i,a),l=t.memoizedState,s!==r||f!==l||Pa.current||Do?("function"===typeof c&&(rs(t,n,c,r),l=t.memoizedState),(s=Do||os(t,n,s,r,f,l,u))?(d||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||("function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"===typeof i.componentDidMount&&(t.flags|=4194308)):("function"===typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),i.props=r,i.state=l,i.context=u,r=s):("function"===typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,Mo(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:ns(t.type,s),i.props=u,d=t.pendingProps,f=i.context,"object"===typeof(l=n.contextType)&&null!==l?l=To(l):l=Ta(t,l=Aa(n)?Ra:Oa.current);var p=n.getDerivedStateFromProps;(c="function"===typeof p||"function"===typeof i.getSnapshotBeforeUpdate)||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(s!==d||f!==l)&&ss(t,i,r,l),Do=!1,f=t.memoizedState,i.state=f,Vo(t,r,i,a);var h=t.memoizedState;s!==d||f!==h||Pa.current||Do?("function"===typeof p&&(rs(t,n,p,r),h=t.memoizedState),(u=Do||os(t,n,u,r,f,h,l)||!1)?(c||"function"!==typeof i.UNSAFE_componentWillUpdate&&"function"!==typeof i.componentWillUpdate||("function"===typeof i.componentWillUpdate&&i.componentWillUpdate(r,h,l),"function"===typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,h,l)),"function"===typeof i.componentDidUpdate&&(t.flags|=4),"function"===typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof i.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),i.props=r,i.state=h,i.context=l,r=u):("function"!==typeof i.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Os(e,t,n,r,o,a)}function Os(e,t,n,r,a,o){Ns(e,t);var i=0!==(128&t.flags);if(!r&&!i)return a&&Fa(t,n,!1),Ws(e,t,o);r=t.stateNode,vs.current=t;var s=i&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&i?(t.child=xo(t,e.child,null,o),t.child=xo(t,null,s,o)):ws(e,t,s,o),t.memoizedState=r.state,a&&Fa(t,n,!0),t.child}function Ps(e){var t=e.stateNode;t.pendingContext?Ia(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Ia(0,t.context,!1),Jo(e,t.containerInfo)}function Rs(e,t,n,r,a){return ho(),mo(a),t.flags|=256,ws(e,t,n,r),t.child}var Ts,As,Ls,Is,_s={dehydrated:null,treeContext:null,retryLane:0};function Ds(e){return{baseLanes:e,cachePool:null,transitions:null}}function Fs(e,t,n){var r,a=t.pendingProps,i=ei.current,s=!1,l=0!==(128&t.flags);if((r=l)||(r=(null===e||null!==e.memoizedState)&&0!==(2&i)),r?(s=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(i|=1),Ca(ei,1&i),null===e)return uo(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(l=a.children,e=a.fallback,s?(a=t.mode,s=t.child,l={mode:"hidden",children:l},0===(1&a)&&null!==s?(s.childLanes=0,s.pendingProps=l):s=Du(l,a,0,null),e=_u(e,a,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Ds(n),t.memoizedState=_s,e):Ms(t,l));if(null!==(i=e.memoizedState)&&null!==(r=i.dehydrated))return function(e,t,n,r,a,i,s){if(n)return 256&t.flags?(t.flags&=-257,zs(e,t,s,r=cs(Error(o(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(i=r.fallback,a=t.mode,r=Du({mode:"visible",children:r.children},a,0,null),(i=_u(i,a,s,null)).flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,0!==(1&t.mode)&&xo(t,e.child,null,s),t.child.memoizedState=Ds(s),t.memoizedState=_s,i);if(0===(1&t.mode))return zs(e,t,s,null);if("$!"===a.data){if(r=a.nextSibling&&a.nextSibling.dataset)var l=r.dgst;return r=l,zs(e,t,s,r=cs(i=Error(o(419)),r,void 0))}if(l=0!==(s&e.childLanes),bs||l){if(null!==(r=Pl)){switch(s&-s){case 4:a=2;break;case 16:a=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:a=32;break;case 536870912:a=268435456;break;default:a=0}0!==(a=0!==(a&(r.suspendedLanes|s))?0:a)&&a!==i.retryLane&&(i.retryLane=a,_o(e,a),nu(r,e,a,-1))}return mu(),zs(e,t,s,r=cs(Error(o(421))))}return"$?"===a.data?(t.flags|=128,t.child=e.child,t=ju.bind(null,e),a._reactRetry=t,null):(e=i.treeContext,ro=ua(a.nextSibling),no=t,ao=!0,oo=null,null!==e&&(qa[Qa++]=Ja,qa[Qa++]=Xa,qa[Qa++]=Ga,Ja=e.id,Xa=e.overflow,Ga=t),t=Ms(t,r.children),t.flags|=4096,t)}(e,t,l,a,r,i,n);if(s){s=a.fallback,l=t.mode,r=(i=e.child).sibling;var u={mode:"hidden",children:a.children};return 0===(1&l)&&t.child!==i?((a=t.child).childLanes=0,a.pendingProps=u,t.deletions=null):(a=Lu(i,u)).subtreeFlags=14680064&i.subtreeFlags,null!==r?s=Lu(r,s):(s=_u(s,l,n,null)).flags|=2,s.return=t,a.return=t,a.sibling=s,t.child=a,a=s,s=t.child,l=null===(l=e.child.memoizedState)?Ds(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},s.memoizedState=l,s.childLanes=e.childLanes&~n,t.memoizedState=_s,a}return e=(s=e.child).sibling,a=Lu(s,{mode:"visible",children:a.children}),0===(1&t.mode)&&(a.lanes=n),a.return=t,a.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=a,t.memoizedState=null,a}function Ms(e,t){return(t=Du({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function zs(e,t,n,r){return null!==r&&mo(r),xo(t,e.child,null,n),(e=Ms(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Bs(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Po(e.return,t,n)}function Us(e,t,n,r,a){var o=e.memoizedState;null===o?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:a}:(o.isBackwards=t,o.rendering=null,o.renderingStartTime=0,o.last=r,o.tail=n,o.tailMode=a)}function Hs(e,t,n){var r=t.pendingProps,a=r.revealOrder,o=r.tail;if(ws(e,t,r.children,n),0!==(2&(r=ei.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Bs(e,n,t);else if(19===e.tag)Bs(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Ca(ei,r),0===(1&t.mode))t.memoizedState=null;else switch(a){case"forwards":for(n=t.child,a=null;null!==n;)null!==(e=n.alternate)&&null===ti(e)&&(a=n),n=n.sibling;null===(n=a)?(a=t.child,t.child=null):(a=n.sibling,n.sibling=null),Us(t,!1,a,n,o);break;case"backwards":for(n=null,a=t.child,t.child=null;null!==a;){if(null!==(e=a.alternate)&&null===ti(e)){t.child=a;break}e=a.sibling,a.sibling=n,n=a,a=e}Us(t,!0,n,null,o);break;case"together":Us(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Vs(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Ws(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Dl|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(o(153));if(null!==t.child){for(n=Lu(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Lu(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function $s(e,t){if(!ao)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ks(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=14680064&a.subtreeFlags,r|=14680064&a.flags,a.return=e,a=a.sibling;else for(a=e.child;null!==a;)n|=a.lanes|a.childLanes,r|=a.subtreeFlags,r|=a.flags,a.return=e,a=a.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function qs(e,t,n){var r=t.pendingProps;switch(to(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ks(t),null;case 1:case 17:return Aa(t.type)&&La(),Ks(t),null;case 3:return r=t.stateNode,Xo(),Na(Pa),Na(Oa),ri(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fo(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==oo&&(iu(oo),oo=null))),As(e,t),Ks(t),null;case 5:Zo(t);var a=Go(Qo.current);if(n=t.type,null!==e&&null!=t.stateNode)Ls(e,t,n,r,a),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(o(166));return Ks(t),null}if(e=Go(Ko.current),fo(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[fa]=t,r[pa]=i,e=0!==(1&t.mode),n){case"dialog":zr("cancel",r),zr("close",r);break;case"iframe":case"object":case"embed":zr("load",r);break;case"video":case"audio":for(a=0;a<_r.length;a++)zr(_r[a],r);break;case"source":zr("error",r);break;case"img":case"image":case"link":zr("error",r),zr("load",r);break;case"details":zr("toggle",r);break;case"input":J(r,i),zr("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},zr("invalid",r);break;case"textarea":ae(r,i),zr("invalid",r)}for(var l in ve(n,i),a=null,i)if(i.hasOwnProperty(l)){var u=i[l];"children"===l?"string"===typeof u?r.textContent!==u&&(!0!==i.suppressHydrationWarning&&Yr(r.textContent,u,e),a=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(!0!==i.suppressHydrationWarning&&Yr(r.textContent,u,e),a=["children",""+u]):s.hasOwnProperty(l)&&null!=u&&"onScroll"===l&&zr("scroll",r)}switch(n){case"input":K(r),Z(r,i,!0);break;case"textarea":K(r),ie(r);break;case"select":case"option":break;default:"function"===typeof i.onClick&&(r.onclick=Zr)}r=a,t.updateQueue=r,null!==r&&(t.flags|=4)}else{l=9===a.nodeType?a:a.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=se(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),"select"===n&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[fa]=t,e[pa]=r,Ts(e,t,!1,!1),t.stateNode=e;e:{switch(l=be(n,r),n){case"dialog":zr("cancel",e),zr("close",e),a=r;break;case"iframe":case"object":case"embed":zr("load",e),a=r;break;case"video":case"audio":for(a=0;a<_r.length;a++)zr(_r[a],e);a=r;break;case"source":zr("error",e),a=r;break;case"img":case"image":case"link":zr("error",e),zr("load",e),a=r;break;case"details":zr("toggle",e),a=r;break;case"input":J(e,r),a=G(e,r),zr("invalid",e);break;case"option":default:a=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},a=F({},r,{value:void 0}),zr("invalid",e);break;case"textarea":ae(e,r),a=re(e,r),zr("invalid",e)}for(i in ve(n,a),u=a)if(u.hasOwnProperty(i)){var c=u[i];"style"===i?ge(e,c):"dangerouslySetInnerHTML"===i?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===i?"string"===typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"===typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==i&&"suppressHydrationWarning"!==i&&"autoFocus"!==i&&(s.hasOwnProperty(i)?null!=c&&"onScroll"===i&&zr("scroll",e):null!=c&&b(e,i,c,l))}switch(n){case"input":K(e),Z(e,r,!1);break;case"textarea":K(e),ie(e);break;case"option":null!=r.value&&e.setAttribute("value",""+W(r.value));break;case"select":e.multiple=!!r.multiple,null!=(i=r.value)?ne(e,!!r.multiple,i,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof a.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Ks(t),null;case 6:if(e&&null!=t.stateNode)Is(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(o(166));if(n=Go(Qo.current),Go(Ko.current),fo(t)){if(r=t.stateNode,n=t.memoizedProps,r[fa]=t,(i=r.nodeValue!==n)&&null!==(e=no))switch(e.tag){case 3:Yr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Yr(r.nodeValue,n,0!==(1&e.mode))}i&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[fa]=t,t.stateNode=r}return Ks(t),null;case 13:if(Na(ei),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(ao&&null!==ro&&0!==(1&t.mode)&&0===(128&t.flags))po(),ho(),t.flags|=98560,i=!1;else if(i=fo(t),null!==r&&null!==r.dehydrated){if(null===e){if(!i)throw Error(o(318));if(!(i=null!==(i=t.memoizedState)?i.dehydrated:null))throw Error(o(317));i[fa]=t}else ho(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Ks(t),i=!1}else null!==oo&&(iu(oo),oo=null),i=!0;if(!i)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&ei.current)?0===Il&&(Il=3):mu())),null!==t.updateQueue&&(t.flags|=4),Ks(t),null);case 4:return Xo(),As(e,t),null===e&&Hr(t.stateNode.containerInfo),Ks(t),null;case 10:return Oo(t.type._context),Ks(t),null;case 19:if(Na(ei),null===(i=t.memoizedState))return Ks(t),null;if(r=0!==(128&t.flags),null===(l=i.rendering))if(r)$s(i,!1);else{if(0!==Il||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(l=ti(e))){for(t.flags|=128,$s(i,!1),null!==(r=l.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(i=n).flags&=14680066,null===(l=i.alternate)?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=l.childLanes,i.lanes=l.lanes,i.child=l.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=l.memoizedProps,i.memoizedState=l.memoizedState,i.updateQueue=l.updateQueue,i.type=l.type,e=l.dependencies,i.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Ca(ei,1&ei.current|2),t.child}e=e.sibling}null!==i.tail&&Xe()>Hl&&(t.flags|=128,r=!0,$s(i,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ti(l))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),$s(i,!0),null===i.tail&&"hidden"===i.tailMode&&!l.alternate&&!ao)return Ks(t),null}else 2*Xe()-i.renderingStartTime>Hl&&1073741824!==n&&(t.flags|=128,r=!0,$s(i,!1),t.lanes=4194304);i.isBackwards?(l.sibling=t.child,t.child=l):(null!==(n=i.last)?n.sibling=l:t.child=l,i.last=l)}return null!==i.tail?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Xe(),t.sibling=null,n=ei.current,Ca(ei,r?1&n|2:1&n),t):(Ks(t),null);case 22:case 23:return du(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Al)&&(Ks(t),6&t.subtreeFlags&&(t.flags|=8192)):Ks(t),null;case 24:case 25:return null}throw Error(o(156,t.tag))}function Qs(e,t){switch(to(t),t.tag){case 1:return Aa(t.type)&&La(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Xo(),Na(Pa),Na(Oa),ri(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Zo(t),null;case 13:if(Na(ei),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(o(340));ho()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Na(ei),null;case 4:return Xo(),null;case 10:return Oo(t.type._context),null;case 22:case 23:return du(),null;default:return null}}Ts=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},As=function(){},Ls=function(e,t,n,r){var a=e.memoizedProps;if(a!==r){e=t.stateNode,Go(Ko.current);var o,i=null;switch(n){case"input":a=G(e,a),r=G(e,r),i=[];break;case"select":a=F({},a,{value:void 0}),r=F({},r,{value:void 0}),i=[];break;case"textarea":a=re(e,a),r=re(e,r),i=[];break;default:"function"!==typeof a.onClick&&"function"===typeof r.onClick&&(e.onclick=Zr)}for(c in ve(n,r),n=null,a)if(!r.hasOwnProperty(c)&&a.hasOwnProperty(c)&&null!=a[c])if("style"===c){var l=a[c];for(o in l)l.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(s.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in r){var u=r[c];if(l=null!=a?a[c]:void 0,r.hasOwnProperty(c)&&u!==l&&(null!=u||null!=l))if("style"===c)if(l){for(o in l)!l.hasOwnProperty(o)||u&&u.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in u)u.hasOwnProperty(o)&&l[o]!==u[o]&&(n||(n={}),n[o]=u[o])}else n||(i||(i=[]),i.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,l=l?l.__html:void 0,null!=u&&l!==u&&(i=i||[]).push(c,u)):"children"===c?"string"!==typeof u&&"number"!==typeof u||(i=i||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(s.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&zr("scroll",e),i||l===u||(i=[])):(i=i||[]).push(c,u))}n&&(i=i||[]).push("style",n);var c=i;(t.updateQueue=c)&&(t.flags|=4)}},Is=function(e,t,n,r){n!==r&&(t.flags|=4)};var Gs=!1,Js=!1,Xs="function"===typeof WeakSet?WeakSet:Set,Ys=null;function Zs(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Eu(e,t,r)}else n.current=null}function el(e,t,n){try{n()}catch(r){Eu(e,t,r)}}var tl=!1;function nl(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var a=r=r.next;do{if((a.tag&e)===e){var o=a.destroy;a.destroy=void 0,void 0!==o&&el(t,n,o)}a=a.next}while(a!==r)}}function rl(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function al(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function ol(e){var t=e.alternate;null!==t&&(e.alternate=null,ol(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[fa],delete t[pa],delete t[ma],delete t[ga],delete t[ya])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function il(e){return 5===e.tag||3===e.tag||4===e.tag}function sl(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||il(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ll(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Zr));else if(4!==r&&null!==(e=e.child))for(ll(e,t,n),e=e.sibling;null!==e;)ll(e,t,n),e=e.sibling}function ul(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(ul(e,t,n),e=e.sibling;null!==e;)ul(e,t,n),e=e.sibling}var cl=null,dl=!1;function fl(e,t,n){for(n=n.child;null!==n;)pl(e,t,n),n=n.sibling}function pl(e,t,n){if(ot&&"function"===typeof ot.onCommitFiberUnmount)try{ot.onCommitFiberUnmount(at,n)}catch(s){}switch(n.tag){case 5:Js||Zs(n,t);case 6:var r=cl,a=dl;cl=null,fl(e,t,n),dl=a,null!==(cl=r)&&(dl?(e=cl,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):cl.removeChild(n.stateNode));break;case 18:null!==cl&&(dl?(e=cl,n=n.stateNode,8===e.nodeType?la(e.parentNode,n):1===e.nodeType&&la(e,n),Ht(e)):la(cl,n.stateNode));break;case 4:r=cl,a=dl,cl=n.stateNode.containerInfo,dl=!0,fl(e,t,n),cl=r,dl=a;break;case 0:case 11:case 14:case 15:if(!Js&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){a=r=r.next;do{var o=a,i=o.destroy;o=o.tag,void 0!==i&&(0!==(2&o)||0!==(4&o))&&el(n,t,i),a=a.next}while(a!==r)}fl(e,t,n);break;case 1:if(!Js&&(Zs(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){Eu(n,t,s)}fl(e,t,n);break;case 21:fl(e,t,n);break;case 22:1&n.mode?(Js=(r=Js)||null!==n.memoizedState,fl(e,t,n),Js=r):fl(e,t,n);break;default:fl(e,t,n)}}function hl(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Xs),t.forEach((function(t){var r=Ou.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function ml(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var a=n[r];try{var i=e,s=t,l=s;e:for(;null!==l;){switch(l.tag){case 5:cl=l.stateNode,dl=!1;break e;case 3:case 4:cl=l.stateNode.containerInfo,dl=!0;break e}l=l.return}if(null===cl)throw Error(o(160));pl(i,s,a),cl=null,dl=!1;var u=a.alternate;null!==u&&(u.return=null),a.return=null}catch(c){Eu(a,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)gl(t,e),t=t.sibling}function gl(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(ml(t,e),yl(e),4&r){try{nl(3,e,e.return),rl(3,e)}catch(g){Eu(e,e.return,g)}try{nl(5,e,e.return)}catch(g){Eu(e,e.return,g)}}break;case 1:ml(t,e),yl(e),512&r&&null!==n&&Zs(n,n.return);break;case 5:if(ml(t,e),yl(e),512&r&&null!==n&&Zs(n,n.return),32&e.flags){var a=e.stateNode;try{fe(a,"")}catch(g){Eu(e,e.return,g)}}if(4&r&&null!=(a=e.stateNode)){var i=e.memoizedProps,s=null!==n?n.memoizedProps:i,l=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===l&&"radio"===i.type&&null!=i.name&&X(a,i),be(l,s);var c=be(l,i);for(s=0;s<u.length;s+=2){var d=u[s],f=u[s+1];"style"===d?ge(a,f):"dangerouslySetInnerHTML"===d?de(a,f):"children"===d?fe(a,f):b(a,d,f,c)}switch(l){case"input":Y(a,i);break;case"textarea":oe(a,i);break;case"select":var p=a._wrapperState.wasMultiple;a._wrapperState.wasMultiple=!!i.multiple;var h=i.value;null!=h?ne(a,!!i.multiple,h,!1):p!==!!i.multiple&&(null!=i.defaultValue?ne(a,!!i.multiple,i.defaultValue,!0):ne(a,!!i.multiple,i.multiple?[]:"",!1))}a[pa]=i}catch(g){Eu(e,e.return,g)}}break;case 6:if(ml(t,e),yl(e),4&r){if(null===e.stateNode)throw Error(o(162));a=e.stateNode,i=e.memoizedProps;try{a.nodeValue=i}catch(g){Eu(e,e.return,g)}}break;case 3:if(ml(t,e),yl(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Ht(t.containerInfo)}catch(g){Eu(e,e.return,g)}break;case 4:default:ml(t,e),yl(e);break;case 13:ml(t,e),yl(e),8192&(a=e.child).flags&&(i=null!==a.memoizedState,a.stateNode.isHidden=i,!i||null!==a.alternate&&null!==a.alternate.memoizedState||(Ul=Xe())),4&r&&hl(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Js=(c=Js)||d,ml(t,e),Js=c):ml(t,e),yl(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(Ys=e,d=e.child;null!==d;){for(f=Ys=d;null!==Ys;){switch(h=(p=Ys).child,p.tag){case 0:case 11:case 14:case 15:nl(4,p,p.return);break;case 1:Zs(p,p.return);var m=p.stateNode;if("function"===typeof m.componentWillUnmount){r=p,n=p.return;try{t=r,m.props=t.memoizedProps,m.state=t.memoizedState,m.componentWillUnmount()}catch(g){Eu(r,n,g)}}break;case 5:Zs(p,p.return);break;case 22:if(null!==p.memoizedState){xl(f);continue}}null!==h?(h.return=p,Ys=h):xl(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{a=f.stateNode,c?"function"===typeof(i=a.style).setProperty?i.setProperty("display","none","important"):i.display="none":(l=f.stateNode,s=void 0!==(u=f.memoizedProps.style)&&null!==u&&u.hasOwnProperty("display")?u.display:null,l.style.display=me("display",s))}catch(g){Eu(e,e.return,g)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(g){Eu(e,e.return,g)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:ml(t,e),yl(e),4&r&&hl(e);case 21:}}function yl(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(il(n)){var r=n;break e}n=n.return}throw Error(o(160))}switch(r.tag){case 5:var a=r.stateNode;32&r.flags&&(fe(a,""),r.flags&=-33),ul(e,sl(e),a);break;case 3:case 4:var i=r.stateNode.containerInfo;ll(e,sl(e),i);break;default:throw Error(o(161))}}catch(s){Eu(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function vl(e,t,n){Ys=e,bl(e,t,n)}function bl(e,t,n){for(var r=0!==(1&e.mode);null!==Ys;){var a=Ys,o=a.child;if(22===a.tag&&r){var i=null!==a.memoizedState||Gs;if(!i){var s=a.alternate,l=null!==s&&null!==s.memoizedState||Js;s=Gs;var u=Js;if(Gs=i,(Js=l)&&!u)for(Ys=a;null!==Ys;)l=(i=Ys).child,22===i.tag&&null!==i.memoizedState?kl(a):null!==l?(l.return=i,Ys=l):kl(a);for(;null!==o;)Ys=o,bl(o,t,n),o=o.sibling;Ys=a,Gs=s,Js=u}wl(e)}else 0!==(8772&a.subtreeFlags)&&null!==o?(o.return=a,Ys=o):wl(e)}}function wl(e){for(;null!==Ys;){var t=Ys;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Js||rl(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Js)if(null===n)r.componentDidMount();else{var a=t.elementType===t.type?n.memoizedProps:ns(t.type,n.memoizedProps);r.componentDidUpdate(a,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;null!==i&&Wo(t,i,r);break;case 3:var s=t.updateQueue;if(null!==s){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Wo(t,s,n)}break;case 5:var l=t.stateNode;if(null===n&&4&t.flags){n=l;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Ht(f)}}}break;default:throw Error(o(163))}Js||512&t.flags&&al(t)}catch(p){Eu(t,t.return,p)}}if(t===e){Ys=null;break}if(null!==(n=t.sibling)){n.return=t.return,Ys=n;break}Ys=t.return}}function xl(e){for(;null!==Ys;){var t=Ys;if(t===e){Ys=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Ys=n;break}Ys=t.return}}function kl(e){for(;null!==Ys;){var t=Ys;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rl(4,t)}catch(l){Eu(t,n,l)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var a=t.return;try{r.componentDidMount()}catch(l){Eu(t,a,l)}}var o=t.return;try{al(t)}catch(l){Eu(t,o,l)}break;case 5:var i=t.return;try{al(t)}catch(l){Eu(t,i,l)}}}catch(l){Eu(t,t.return,l)}if(t===e){Ys=null;break}var s=t.sibling;if(null!==s){s.return=t.return,Ys=s;break}Ys=t.return}}var Sl,El=Math.ceil,Nl=w.ReactCurrentDispatcher,Cl=w.ReactCurrentOwner,jl=w.ReactCurrentBatchConfig,Ol=0,Pl=null,Rl=null,Tl=0,Al=0,Ll=Ea(0),Il=0,_l=null,Dl=0,Fl=0,Ml=0,zl=null,Bl=null,Ul=0,Hl=1/0,Vl=null,Wl=!1,$l=null,Kl=null,ql=!1,Ql=null,Gl=0,Jl=0,Xl=null,Yl=-1,Zl=0;function eu(){return 0!==(6&Ol)?Xe():-1!==Yl?Yl:Yl=Xe()}function tu(e){return 0===(1&e.mode)?1:0!==(2&Ol)&&0!==Tl?Tl&-Tl:null!==go.transition?(0===Zl&&(Zl=mt()),Zl):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Jt(e.type)}function nu(e,t,n,r){if(50<Jl)throw Jl=0,Xl=null,Error(o(185));yt(e,n,r),0!==(2&Ol)&&e===Pl||(e===Pl&&(0===(2&Ol)&&(Fl|=n),4===Il&&su(e,Tl)),ru(e,r),1===n&&0===Ol&&0===(1&t.mode)&&(Hl=Xe()+500,za&&Ha()))}function ru(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,a=e.expirationTimes,o=e.pendingLanes;0<o;){var i=31-it(o),s=1<<i,l=a[i];-1===l?0!==(s&n)&&0===(s&r)||(a[i]=pt(s,t)):l<=t&&(e.expiredLanes|=s),o&=~s}}(e,t);var r=ft(e,e===Pl?Tl:0);if(0===r)null!==n&&Qe(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Qe(n),1===t)0===e.tag?function(e){za=!0,Ua(e)}(lu.bind(null,e)):Ua(lu.bind(null,e)),ia((function(){0===(6&Ol)&&Ha()})),n=null;else{switch(wt(r)){case 1:n=Ze;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Pu(n,au.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function au(e,t){if(Yl=-1,Zl=0,0!==(6&Ol))throw Error(o(327));var n=e.callbackNode;if(ku()&&e.callbackNode!==n)return null;var r=ft(e,e===Pl?Tl:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=gu(e,r);else{t=r;var a=Ol;Ol|=2;var i=hu();for(Pl===e&&Tl===t||(Vl=null,Hl=Xe()+500,fu(e,t));;)try{vu();break}catch(l){pu(e,l)}jo(),Nl.current=i,Ol=a,null!==Rl?t=0:(Pl=null,Tl=0,t=Il)}if(0!==t){if(2===t&&(0!==(a=ht(e))&&(r=a,t=ou(e,a))),1===t)throw n=_l,fu(e,0),su(e,r),ru(e,Xe()),n;if(6===t)su(e,r);else{if(a=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var a=n[r],o=a.getSnapshot;a=a.value;try{if(!sr(o(),a))return!1}catch(s){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(a)&&(2===(t=gu(e,r))&&(0!==(i=ht(e))&&(r=i,t=ou(e,i))),1===t))throw n=_l,fu(e,0),su(e,r),ru(e,Xe()),n;switch(e.finishedWork=a,e.finishedLanes=r,t){case 0:case 1:throw Error(o(345));case 2:case 5:xu(e,Bl,Vl);break;case 3:if(su(e,r),(130023424&r)===r&&10<(t=Ul+500-Xe())){if(0!==ft(e,0))break;if(((a=e.suspendedLanes)&r)!==r){eu(),e.pingedLanes|=e.suspendedLanes&a;break}e.timeoutHandle=ra(xu.bind(null,e,Bl,Vl),t);break}xu(e,Bl,Vl);break;case 4:if(su(e,r),(4194240&r)===r)break;for(t=e.eventTimes,a=-1;0<r;){var s=31-it(r);i=1<<s,(s=t[s])>a&&(a=s),r&=~i}if(r=a,10<(r=(120>(r=Xe()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*El(r/1960))-r)){e.timeoutHandle=ra(xu.bind(null,e,Bl,Vl),r);break}xu(e,Bl,Vl);break;default:throw Error(o(329))}}}return ru(e,Xe()),e.callbackNode===n?au.bind(null,e):null}function ou(e,t){var n=zl;return e.current.memoizedState.isDehydrated&&(fu(e,t).flags|=256),2!==(e=gu(e,t))&&(t=Bl,Bl=n,null!==t&&iu(t)),e}function iu(e){null===Bl?Bl=e:Bl.push.apply(Bl,e)}function su(e,t){for(t&=~Ml,t&=~Fl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-it(t),r=1<<n;e[n]=-1,t&=~r}}function lu(e){if(0!==(6&Ol))throw Error(o(327));ku();var t=ft(e,0);if(0===(1&t))return ru(e,Xe()),null;var n=gu(e,t);if(0!==e.tag&&2===n){var r=ht(e);0!==r&&(t=r,n=ou(e,r))}if(1===n)throw n=_l,fu(e,0),su(e,t),ru(e,Xe()),n;if(6===n)throw Error(o(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,xu(e,Bl,Vl),ru(e,Xe()),null}function uu(e,t){var n=Ol;Ol|=1;try{return e(t)}finally{0===(Ol=n)&&(Hl=Xe()+500,za&&Ha())}}function cu(e){null!==Ql&&0===Ql.tag&&0===(6&Ol)&&ku();var t=Ol;Ol|=1;var n=jl.transition,r=bt;try{if(jl.transition=null,bt=1,e)return e()}finally{bt=r,jl.transition=n,0===(6&(Ol=t))&&Ha()}}function du(){Al=Ll.current,Na(Ll)}function fu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,aa(n)),null!==Rl)for(n=Rl.return;null!==n;){var r=n;switch(to(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&La();break;case 3:Xo(),Na(Pa),Na(Oa),ri();break;case 5:Zo(r);break;case 4:Xo();break;case 13:case 19:Na(ei);break;case 10:Oo(r.type._context);break;case 22:case 23:du()}n=n.return}if(Pl=e,Rl=e=Lu(e.current,null),Tl=Al=t,Il=0,_l=null,Ml=Fl=Dl=0,Bl=zl=null,null!==Ao){for(t=0;t<Ao.length;t++)if(null!==(r=(n=Ao[t]).interleaved)){n.interleaved=null;var a=r.next,o=n.pending;if(null!==o){var i=o.next;o.next=a,r.next=i}n.pending=r}Ao=null}return e}function pu(e,t){for(;;){var n=Rl;try{if(jo(),ai.current=Yi,ci){for(var r=si.memoizedState;null!==r;){var a=r.queue;null!==a&&(a.pending=null),r=r.next}ci=!1}if(ii=0,ui=li=si=null,di=!1,fi=0,Cl.current=null,null===n||null===n.return){Il=1,_l=t,Rl=null;break}e:{var i=e,s=n.return,l=n,u=t;if(t=Tl,l.flags|=32768,null!==u&&"object"===typeof u&&"function"===typeof u.then){var c=u,d=l,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=gs(s);if(null!==h){h.flags&=-257,ys(h,s,l,0,t),1&h.mode&&ms(i,c,t),u=c;var m=(t=h).updateQueue;if(null===m){var g=new Set;g.add(u),t.updateQueue=g}else m.add(u);break e}if(0===(1&t)){ms(i,c,t),mu();break e}u=Error(o(426))}else if(ao&&1&l.mode){var y=gs(s);if(null!==y){0===(65536&y.flags)&&(y.flags|=256),ys(y,s,l,0,t),mo(us(u,l));break e}}i=u=us(u,l),4!==Il&&(Il=2),null===zl?zl=[i]:zl.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t,Ho(i,ps(0,u,t));break e;case 1:l=u;var v=i.type,b=i.stateNode;if(0===(128&i.flags)&&("function"===typeof v.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===Kl||!Kl.has(b)))){i.flags|=65536,t&=-t,i.lanes|=t,Ho(i,hs(i,l,t));break e}}i=i.return}while(null!==i)}wu(n)}catch(w){t=w,Rl===n&&null!==n&&(Rl=n=n.return);continue}break}}function hu(){var e=Nl.current;return Nl.current=Yi,null===e?Yi:e}function mu(){0!==Il&&3!==Il&&2!==Il||(Il=4),null===Pl||0===(268435455&Dl)&&0===(268435455&Fl)||su(Pl,Tl)}function gu(e,t){var n=Ol;Ol|=2;var r=hu();for(Pl===e&&Tl===t||(Vl=null,fu(e,t));;)try{yu();break}catch(a){pu(e,a)}if(jo(),Ol=n,Nl.current=r,null!==Rl)throw Error(o(261));return Pl=null,Tl=0,Il}function yu(){for(;null!==Rl;)bu(Rl)}function vu(){for(;null!==Rl&&!Ge();)bu(Rl)}function bu(e){var t=Sl(e.alternate,e,Al);e.memoizedProps=e.pendingProps,null===t?wu(e):Rl=t,Cl.current=null}function wu(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=qs(n,t,Al)))return void(Rl=n)}else{if(null!==(n=Qs(n,t)))return n.flags&=32767,void(Rl=n);if(null===e)return Il=6,void(Rl=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Rl=t);Rl=t=e}while(null!==t);0===Il&&(Il=5)}function xu(e,t,n){var r=bt,a=jl.transition;try{jl.transition=null,bt=1,function(e,t,n,r){do{ku()}while(null!==Ql);if(0!==(6&Ol))throw Error(o(327));n=e.finishedWork;var a=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(o(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var a=31-it(n),o=1<<a;t[a]=0,r[a]=-1,e[a]=-1,n&=~o}}(e,i),e===Pl&&(Rl=Pl=null,Tl=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||ql||(ql=!0,Pu(tt,(function(){return ku(),null}))),i=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||i){i=jl.transition,jl.transition=null;var s=bt;bt=1;var l=Ol;Ol|=4,Cl.current=null,function(e,t){if(ea=Wt,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var a=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch(x){n=null;break e}var s=0,l=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==a&&3!==f.nodeType||(l=s+a),f!==i||0!==r&&3!==f.nodeType||(u=s+r),3===f.nodeType&&(s+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++c===a&&(l=s),p===i&&++d===r&&(u=s),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===l||-1===u?null:{start:l,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(ta={focusedElem:e,selectionRange:n},Wt=!1,Ys=t;null!==Ys;)if(e=(t=Ys).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Ys=e;else for(;null!==Ys;){t=Ys;try{var m=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==m){var g=m.memoizedProps,y=m.memoizedState,v=t.stateNode,b=v.getSnapshotBeforeUpdate(t.elementType===t.type?g:ns(t.type,g),y);v.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var w=t.stateNode.containerInfo;1===w.nodeType?w.textContent="":9===w.nodeType&&w.documentElement&&w.removeChild(w.documentElement);break;default:throw Error(o(163))}}catch(x){Eu(t,t.return,x)}if(null!==(e=t.sibling)){e.return=t.return,Ys=e;break}Ys=t.return}m=tl,tl=!1}(e,n),gl(n,e),hr(ta),Wt=!!ea,ta=ea=null,e.current=n,vl(n,e,a),Je(),Ol=l,bt=s,jl.transition=i}else e.current=n;if(ql&&(ql=!1,Ql=e,Gl=a),i=e.pendingLanes,0===i&&(Kl=null),function(e){if(ot&&"function"===typeof ot.onCommitFiberRoot)try{ot.onCommitFiberRoot(at,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),ru(e,Xe()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)a=t[n],r(a.value,{componentStack:a.stack,digest:a.digest});if(Wl)throw Wl=!1,e=$l,$l=null,e;0!==(1&Gl)&&0!==e.tag&&ku(),i=e.pendingLanes,0!==(1&i)?e===Xl?Jl++:(Jl=0,Xl=e):Jl=0,Ha()}(e,t,n,r)}finally{jl.transition=a,bt=r}return null}function ku(){if(null!==Ql){var e=wt(Gl),t=jl.transition,n=bt;try{if(jl.transition=null,bt=16>e?16:e,null===Ql)var r=!1;else{if(e=Ql,Ql=null,Gl=0,0!==(6&Ol))throw Error(o(331));var a=Ol;for(Ol|=4,Ys=e.current;null!==Ys;){var i=Ys,s=i.child;if(0!==(16&Ys.flags)){var l=i.deletions;if(null!==l){for(var u=0;u<l.length;u++){var c=l[u];for(Ys=c;null!==Ys;){var d=Ys;switch(d.tag){case 0:case 11:case 15:nl(8,d,i)}var f=d.child;if(null!==f)f.return=d,Ys=f;else for(;null!==Ys;){var p=(d=Ys).sibling,h=d.return;if(ol(d),d===c){Ys=null;break}if(null!==p){p.return=h,Ys=p;break}Ys=h}}}var m=i.alternate;if(null!==m){var g=m.child;if(null!==g){m.child=null;do{var y=g.sibling;g.sibling=null,g=y}while(null!==g)}}Ys=i}}if(0!==(2064&i.subtreeFlags)&&null!==s)s.return=i,Ys=s;else e:for(;null!==Ys;){if(0!==(2048&(i=Ys).flags))switch(i.tag){case 0:case 11:case 15:nl(9,i,i.return)}var v=i.sibling;if(null!==v){v.return=i.return,Ys=v;break e}Ys=i.return}}var b=e.current;for(Ys=b;null!==Ys;){var w=(s=Ys).child;if(0!==(2064&s.subtreeFlags)&&null!==w)w.return=s,Ys=w;else e:for(s=b;null!==Ys;){if(0!==(2048&(l=Ys).flags))try{switch(l.tag){case 0:case 11:case 15:rl(9,l)}}catch(k){Eu(l,l.return,k)}if(l===s){Ys=null;break e}var x=l.sibling;if(null!==x){x.return=l.return,Ys=x;break e}Ys=l.return}}if(Ol=a,Ha(),ot&&"function"===typeof ot.onPostCommitFiberRoot)try{ot.onPostCommitFiberRoot(at,e)}catch(k){}r=!0}return r}finally{bt=n,jl.transition=t}}return!1}function Su(e,t,n){e=Bo(e,t=ps(0,t=us(n,t),1),1),t=eu(),null!==e&&(yt(e,1,t),ru(e,t))}function Eu(e,t,n){if(3===e.tag)Su(e,e,n);else for(;null!==t;){if(3===t.tag){Su(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Kl||!Kl.has(r))){t=Bo(t,e=hs(t,e=us(n,e),1),1),e=eu(),null!==t&&(yt(t,1,e),ru(t,e));break}}t=t.return}}function Nu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=eu(),e.pingedLanes|=e.suspendedLanes&n,Pl===e&&(Tl&n)===n&&(4===Il||3===Il&&(130023424&Tl)===Tl&&500>Xe()-Ul?fu(e,0):Ml|=n),ru(e,t)}function Cu(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ct,0===(130023424&(ct<<=1))&&(ct=4194304)));var n=eu();null!==(e=_o(e,t))&&(yt(e,t,n),ru(e,n))}function ju(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Cu(e,n)}function Ou(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,a=e.memoizedState;null!==a&&(n=a.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(o(314))}null!==r&&r.delete(t),Cu(e,n)}function Pu(e,t){return qe(e,t)}function Ru(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Tu(e,t,n,r){return new Ru(e,t,n,r)}function Au(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Lu(e,t){var n=e.alternate;return null===n?((n=Tu(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function Iu(e,t,n,r,a,i){var s=2;if(r=e,"function"===typeof e)Au(e)&&(s=1);else if("string"===typeof e)s=5;else e:switch(e){case S:return _u(n.children,a,i,t);case E:s=8,a|=8;break;case N:return(e=Tu(12,n,t,2|a)).elementType=N,e.lanes=i,e;case P:return(e=Tu(13,n,t,a)).elementType=P,e.lanes=i,e;case R:return(e=Tu(19,n,t,a)).elementType=R,e.lanes=i,e;case L:return Du(n,a,i,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case C:s=10;break e;case j:s=9;break e;case O:s=11;break e;case T:s=14;break e;case A:s=16,r=null;break e}throw Error(o(130,null==e?e:typeof e,""))}return(t=Tu(s,n,t,a)).elementType=e,t.type=r,t.lanes=i,t}function _u(e,t,n,r){return(e=Tu(7,e,r,t)).lanes=n,e}function Du(e,t,n,r){return(e=Tu(22,e,r,t)).elementType=L,e.lanes=n,e.stateNode={isHidden:!1},e}function Fu(e,t,n){return(e=Tu(6,e,null,t)).lanes=n,e}function Mu(e,t,n){return(t=Tu(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function zu(e,t,n,r,a){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=gt(0),this.expirationTimes=gt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=gt(0),this.identifierPrefix=r,this.onRecoverableError=a,this.mutableSourceEagerHydrationData=null}function Bu(e,t,n,r,a,o,i,s,l){return e=new zu(e,t,n,s,l),1===t?(t=1,!0===o&&(t|=8)):t=0,o=Tu(3,null,null,t),e.current=o,o.stateNode=e,o.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Fo(o),e}function Uu(e){if(!e)return ja;e:{if(He(e=e._reactInternals)!==e||1!==e.tag)throw Error(o(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Aa(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(o(171))}if(1===e.tag){var n=e.type;if(Aa(n))return _a(e,n,t)}return t}function Hu(e,t,n,r,a,o,i,s,l){return(e=Bu(n,r,!0,e,0,o,0,s,l)).context=Uu(null),n=e.current,(o=zo(r=eu(),a=tu(n))).callback=void 0!==t&&null!==t?t:null,Bo(n,o,a),e.current.lanes=a,yt(e,a,r),ru(e,r),e}function Vu(e,t,n,r){var a=t.current,o=eu(),i=tu(a);return n=Uu(n),null===t.context?t.context=n:t.pendingContext=n,(t=zo(o,i)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Bo(a,t,i))&&(nu(e,a,i,o),Uo(e,a,i)),i}function Wu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function $u(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Ku(e,t){$u(e,t),(e=e.alternate)&&$u(e,t)}Sl=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||Pa.current)bs=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return bs=!1,function(e,t,n){switch(t.tag){case 3:Ps(t),ho();break;case 5:Yo(t);break;case 1:Aa(t.type)&&Da(t);break;case 4:Jo(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,a=t.memoizedProps.value;Ca(So,r._currentValue),r._currentValue=a;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Ca(ei,1&ei.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Fs(e,t,n):(Ca(ei,1&ei.current),null!==(e=Ws(e,t,n))?e.sibling:null);Ca(ei,1&ei.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Hs(e,t,n);t.flags|=128}if(null!==(a=t.memoizedState)&&(a.rendering=null,a.tail=null,a.lastEffect=null),Ca(ei,ei.current),r)break;return null;case 22:case 23:return t.lanes=0,Es(e,t,n)}return Ws(e,t,n)}(e,t,n);bs=0!==(131072&e.flags)}else bs=!1,ao&&0!==(1048576&t.flags)&&Za(t,Ka,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Vs(e,t),e=t.pendingProps;var a=Ta(t,Oa.current);Ro(t,n),a=gi(null,t,r,e,a,n);var i=yi();return t.flags|=1,"object"===typeof a&&null!==a&&"function"===typeof a.render&&void 0===a.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Aa(r)?(i=!0,Da(t)):i=!1,t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,Fo(t),a.updater=as,t.stateNode=a,a._reactInternals=t,ls(t,r,e,n),t=Os(null,t,r,!0,i,n)):(t.tag=0,ao&&i&&eo(t),ws(null,t,a,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Vs(e,t),e=t.pendingProps,r=(a=r._init)(r._payload),t.type=r,a=t.tag=function(e){if("function"===typeof e)return Au(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===O)return 11;if(e===T)return 14}return 2}(r),e=ns(r,e),a){case 0:t=Cs(null,t,r,e,n);break e;case 1:t=js(null,t,r,e,n);break e;case 11:t=xs(null,t,r,e,n);break e;case 14:t=ks(null,t,r,ns(r.type,e),n);break e}throw Error(o(306,r,""))}return t;case 0:return r=t.type,a=t.pendingProps,Cs(e,t,r,a=t.elementType===r?a:ns(r,a),n);case 1:return r=t.type,a=t.pendingProps,js(e,t,r,a=t.elementType===r?a:ns(r,a),n);case 3:e:{if(Ps(t),null===e)throw Error(o(387));r=t.pendingProps,a=(i=t.memoizedState).element,Mo(e,t),Vo(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated){if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,256&t.flags){t=Rs(e,t,r,n,a=us(Error(o(423)),t));break e}if(r!==a){t=Rs(e,t,r,n,a=us(Error(o(424)),t));break e}for(ro=ua(t.stateNode.containerInfo.firstChild),no=t,ao=!0,oo=null,n=ko(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ho(),r===a){t=Ws(e,t,n);break e}ws(e,t,r,n)}t=t.child}return t;case 5:return Yo(t),null===e&&uo(t),r=t.type,a=t.pendingProps,i=null!==e?e.memoizedProps:null,s=a.children,na(r,a)?s=null:null!==i&&na(r,i)&&(t.flags|=32),Ns(e,t),ws(e,t,s,n),t.child;case 6:return null===e&&uo(t),null;case 13:return Fs(e,t,n);case 4:return Jo(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=xo(t,null,r,n):ws(e,t,r,n),t.child;case 11:return r=t.type,a=t.pendingProps,xs(e,t,r,a=t.elementType===r?a:ns(r,a),n);case 7:return ws(e,t,t.pendingProps,n),t.child;case 8:case 12:return ws(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,a=t.pendingProps,i=t.memoizedProps,s=a.value,Ca(So,r._currentValue),r._currentValue=s,null!==i)if(sr(i.value,s)){if(i.children===a.children&&!Pa.current){t=Ws(e,t,n);break e}}else for(null!==(i=t.child)&&(i.return=t);null!==i;){var l=i.dependencies;if(null!==l){s=i.child;for(var u=l.firstContext;null!==u;){if(u.context===r){if(1===i.tag){(u=zo(-1,n&-n)).tag=2;var c=i.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}i.lanes|=n,null!==(u=i.alternate)&&(u.lanes|=n),Po(i.return,n,t),l.lanes|=n;break}u=u.next}}else if(10===i.tag)s=i.type===t.type?null:i.child;else if(18===i.tag){if(null===(s=i.return))throw Error(o(341));s.lanes|=n,null!==(l=s.alternate)&&(l.lanes|=n),Po(s,n,t),s=i.sibling}else s=i.child;if(null!==s)s.return=i;else for(s=i;null!==s;){if(s===t){s=null;break}if(null!==(i=s.sibling)){i.return=s.return,s=i;break}s=s.return}i=s}ws(e,t,a.children,n),t=t.child}return t;case 9:return a=t.type,r=t.pendingProps.children,Ro(t,n),r=r(a=To(a)),t.flags|=1,ws(e,t,r,n),t.child;case 14:return a=ns(r=t.type,t.pendingProps),ks(e,t,r,a=ns(r.type,a),n);case 15:return Ss(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,a=t.pendingProps,a=t.elementType===r?a:ns(r,a),Vs(e,t),t.tag=1,Aa(r)?(e=!0,Da(t)):e=!1,Ro(t,n),is(t,r,a),ls(t,r,a,n),Os(null,t,r,!0,e,n);case 19:return Hs(e,t,n);case 22:return Es(e,t,n)}throw Error(o(156,t.tag))};var qu="function"===typeof reportError?reportError:function(e){console.error(e)};function Qu(e){this._internalRoot=e}function Gu(e){this._internalRoot=e}function Ju(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Xu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Yu(){}function Zu(e,t,n,r,a){var o=n._reactRootContainer;if(o){var i=o;if("function"===typeof a){var s=a;a=function(){var e=Wu(i);s.call(e)}}Vu(t,i,e,a)}else i=function(e,t,n,r,a){if(a){if("function"===typeof r){var o=r;r=function(){var e=Wu(i);o.call(e)}}var i=Hu(t,r,e,0,null,!1,0,"",Yu);return e._reactRootContainer=i,e[ha]=i.current,Hr(8===e.nodeType?e.parentNode:e),cu(),i}for(;a=e.lastChild;)e.removeChild(a);if("function"===typeof r){var s=r;r=function(){var e=Wu(l);s.call(e)}}var l=Bu(e,0,!1,null,0,!1,0,"",Yu);return e._reactRootContainer=l,e[ha]=l.current,Hr(8===e.nodeType?e.parentNode:e),cu((function(){Vu(t,l,n,r)})),l}(n,t,e,a,r);return Wu(i)}Gu.prototype.render=Qu.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(o(409));Vu(e,t,null,null)},Gu.prototype.unmount=Qu.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;cu((function(){Vu(null,e,null,null)})),t[ha]=null}},Gu.prototype.unstable_scheduleHydration=function(e){if(e){var t=Et();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Lt.length&&0!==t&&t<Lt[n].priority;n++);Lt.splice(n,0,e),0===n&&Ft(e)}},xt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(vt(t,1|n),ru(t,Xe()),0===(6&Ol)&&(Hl=Xe()+500,Ha()))}break;case 13:cu((function(){var t=_o(e,1);if(null!==t){var n=eu();nu(t,e,1,n)}})),Ku(e,1)}},kt=function(e){if(13===e.tag){var t=_o(e,134217728);if(null!==t)nu(t,e,134217728,eu());Ku(e,134217728)}},St=function(e){if(13===e.tag){var t=tu(e),n=_o(e,t);if(null!==n)nu(n,e,t,eu());Ku(e,t)}},Et=function(){return bt},Nt=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},ke=function(e,t,n){switch(t){case"input":if(Y(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var a=xa(r);if(!a)throw Error(o(90));q(r),Y(r,a)}}}break;case"textarea":oe(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Oe=uu,Pe=cu;var ec={usingClientEntryPoint:!1,Events:[ba,wa,xa,Ce,je,uu]},tc={findFiberByHostInstance:va,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nc={bundleType:tc.bundleType,version:tc.version,rendererPackageName:tc.rendererPackageName,rendererConfig:tc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:w.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=$e(e))?null:e.stateNode},findFiberByHostInstance:tc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var rc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!rc.isDisabled&&rc.supportsFiber)try{at=rc.inject(nc),ot=rc}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ec,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Ju(t))throw Error(o(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:k,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Ju(e))throw Error(o(299));var n=!1,r="",a=qu;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(a=t.onRecoverableError)),t=Bu(e,1,!1,null,0,n,0,r,a),e[ha]=t.current,Hr(8===e.nodeType?e.parentNode:e),new Qu(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(o(188));throw e=Object.keys(e).join(","),Error(o(268,e))}return e=null===(e=$e(t))?null:e.stateNode},t.flushSync=function(e){return cu(e)},t.hydrate=function(e,t,n){if(!Xu(t))throw Error(o(200));return Zu(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Ju(e))throw Error(o(405));var r=null!=n&&n.hydratedSources||null,a=!1,i="",s=qu;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(a=!0),void 0!==n.identifierPrefix&&(i=n.identifierPrefix),void 0!==n.onRecoverableError&&(s=n.onRecoverableError)),t=Hu(t,null,e,1,null!=n?n:null,a,0,i,s),e[ha]=t.current,Hr(e),r)for(e=0;e<r.length;e++)a=(a=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,a]:t.mutableSourceEagerHydrationData.push(n,a);return new Gu(t)},t.render=function(e,t,n){if(!Xu(t))throw Error(o(200));return Zu(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Xu(e))throw Error(o(40));return!!e._reactRootContainer&&(cu((function(){Zu(null,null,e,!1,(function(){e._reactRootContainer=null,e[ha]=null}))})),!0)},t.unstable_batchedUpdates=uu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Xu(n))throw Error(o(200));if(null==e||void 0===e._reactInternals)throw Error(o(38));return Zu(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},763:(e,t,n)=>{"use strict";e.exports=n(983)},844:e=>{e.exports={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}},853:(e,t,n)=>{"use strict";e.exports=n(234)},868:(e,t,n)=>{"use strict";function r(e){let t="https://mui.com/production-error/?code="+e;for(let n=1;n<arguments.length;n+=1)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified MUI error #"+e+"; visit "+t+" for the full message."}n.d(t,{A:()=>r})},950:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)},983:(e,t)=>{"use strict";var n="function"===typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,a=n?Symbol.for("react.portal"):60106,o=n?Symbol.for("react.fragment"):60107,i=n?Symbol.for("react.strict_mode"):60108,s=n?Symbol.for("react.profiler"):60114,l=n?Symbol.for("react.provider"):60109,u=n?Symbol.for("react.context"):60110,c=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,f=n?Symbol.for("react.forward_ref"):60112,p=n?Symbol.for("react.suspense"):60113,h=n?Symbol.for("react.suspense_list"):60120,m=n?Symbol.for("react.memo"):60115,g=n?Symbol.for("react.lazy"):60116,y=n?Symbol.for("react.block"):60121,v=n?Symbol.for("react.fundamental"):60117,b=n?Symbol.for("react.responder"):60118,w=n?Symbol.for("react.scope"):60119;function x(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case c:case d:case o:case s:case i:case p:return e;default:switch(e=e&&e.$$typeof){case u:case f:case g:case m:case l:return e;default:return t}}case a:return t}}}function k(e){return x(e)===d}t.AsyncMode=c,t.ConcurrentMode=d,t.ContextConsumer=u,t.ContextProvider=l,t.Element=r,t.ForwardRef=f,t.Fragment=o,t.Lazy=g,t.Memo=m,t.Portal=a,t.Profiler=s,t.StrictMode=i,t.Suspense=p,t.isAsyncMode=function(e){return k(e)||x(e)===c},t.isConcurrentMode=k,t.isContextConsumer=function(e){return x(e)===u},t.isContextProvider=function(e){return x(e)===l},t.isElement=function(e){return"object"===typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return x(e)===f},t.isFragment=function(e){return x(e)===o},t.isLazy=function(e){return x(e)===g},t.isMemo=function(e){return x(e)===m},t.isPortal=function(e){return x(e)===a},t.isProfiler=function(e){return x(e)===s},t.isStrictMode=function(e){return x(e)===i},t.isSuspense=function(e){return x(e)===p},t.isValidElementType=function(e){return"string"===typeof e||"function"===typeof e||e===o||e===d||e===s||e===i||e===p||e===h||"object"===typeof e&&null!==e&&(e.$$typeof===g||e.$$typeof===m||e.$$typeof===l||e.$$typeof===u||e.$$typeof===f||e.$$typeof===v||e.$$typeof===b||e.$$typeof===w||e.$$typeof===y)},t.typeOf=x},994:e=>{e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function n(r){var a=t[r];if(void 0!==a)return a.exports;var o=t[r]={exports:{}};return e[r](o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,a){if(1&a&&(r=this(r)),8&a)return r;if("object"===typeof r&&r){if(4&a&&r.__esModule)return r;if(16&a&&"function"===typeof r.then)return r}var o=Object.create(null);n.r(o);var i={};e=e||[null,t({}),t([]),t(t)];for(var s=2&a&&r;"object"==typeof s&&!~e.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach((e=>i[e]=()=>r[e]));return i.default=()=>r,n.d(o,i),o}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e={};n.r(e),n.d(e,{hasBrowserEnv:()=>Wc,hasStandardBrowserEnv:()=>Kc,hasStandardBrowserWebWorkerEnv:()=>qc,navigator:()=>$c,origin:()=>Qc});var t=n(43),r=n.t(t,2),a=n(391),o=n(461),i=n(443),s=n(950),l=n.t(s,2);let u=function(e){e()};const c=()=>u,d=Symbol.for("react-redux-context"),f="undefined"!==typeof globalThis?globalThis:{};function p(){var e;if(!t.createContext)return{};const n=null!=(e=f[d])?e:f[d]=new Map;let r=n.get(t.createContext);return r||(r=t.createContext(null),n.set(t.createContext,r)),r}const h=p();function m(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:h;return function(){return(0,t.useContext)(e)}}const g=m();let y=()=>{throw new Error("uSES not initialized!")};const v=(e,t)=>e===t;function b(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:h;const n=e===h?g:m(e);return function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{equalityFn:a=v,stabilityCheck:o,noopCheck:i}="function"===typeof r?{equalityFn:r}:r;const{store:s,subscription:l,getServerState:u,stabilityCheck:c,noopCheck:d}=n(),f=((0,t.useRef)(!0),(0,t.useCallback)({[e.name]:t=>e(t)}[e.name],[e,c,o])),p=y(l.addNestedSub,s.getState,u||s.getState,f,a);return(0,t.useDebugValue)(p),p}}const w=b();n(219),n(706);const x={notify(){},get:()=>[]};function k(e,t){let n,r=x,a=0,o=!1;function i(){u.onStateChange&&u.onStateChange()}function s(){a++,n||(n=t?t.addNestedSub(i):e.subscribe(i),r=function(){const e=c();let t=null,n=null;return{clear(){t=null,n=null},notify(){e((()=>{let e=t;for(;e;)e.callback(),e=e.next}))},get(){let e=[],n=t;for(;n;)e.push(n),n=n.next;return e},subscribe(e){let r=!0,a=n={callback:e,next:null,prev:n};return a.prev?a.prev.next=a:t=a,function(){r&&null!==t&&(r=!1,a.next?a.next.prev=a.prev:n=a.prev,a.prev?a.prev.next=a.next:t=a.next)}}}}())}function l(){a--,n&&0===a&&(n(),n=void 0,r.clear(),r=x)}const u={addNestedSub:function(e){s();const t=r.subscribe(e);let n=!1;return()=>{n||(n=!0,t(),l())}},notifyNestedSubs:function(){r.notify()},handleChangeWrapper:i,isSubscribed:function(){return o},trySubscribe:function(){o||(o=!0,s())},tryUnsubscribe:function(){o&&(o=!1,l())},getListeners:()=>r};return u}const S=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement)?t.useLayoutEffect:t.useEffect;let E=null;const N=function(e){let{store:n,context:r,children:a,serverState:o,stabilityCheck:i="once",noopCheck:s="once"}=e;const l=t.useMemo((()=>{const e=k(n);return{store:n,subscription:e,getServerState:o?()=>o:void 0,stabilityCheck:i,noopCheck:s}}),[n,o,i,s]),u=t.useMemo((()=>n.getState()),[n]);S((()=>{const{subscription:e}=l;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),u!==n.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}}),[l,u]);const c=r||h;return t.createElement(c.Provider,{value:l},a)};function C(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:h;const t=e===h?g:m(e);return function(){const{store:e}=t();return e}}const j=C();function O(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:h;const t=e===h?j:C(e);return function(){return t().dispatch}}const P=O();var R,T;function A(){return A=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},A.apply(this,arguments)}(e=>{y=e})(i.useSyncExternalStoreWithSelector),(e=>{E=e})(o.useSyncExternalStore),R=s.unstable_batchedUpdates,u=R,function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(T||(T={}));const L="popstate";function I(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function _(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(_i){}}}function D(e,t){return{usr:e.state,key:e.key,idx:t}}function F(e,t,n,r){return void 0===n&&(n=null),A({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?z(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function M(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function z(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function B(e,t,n,r){void 0===r&&(r={});let{window:a=document.defaultView,v5Compat:o=!1}=r,i=a.history,s=T.Pop,l=null,u=c();function c(){return(i.state||{idx:null}).idx}function d(){s=T.Pop;let e=c(),t=null==e?null:e-u;u=e,l&&l({action:s,location:p.location,delta:t})}function f(e){let t="null"!==a.location.origin?a.location.origin:a.location.href,n="string"===typeof e?e:M(e);return n=n.replace(/ $/,"%20"),I(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==u&&(u=0,i.replaceState(A({},i.state,{idx:u}),""));let p={get action(){return s},get location(){return e(a,i)},listen(e){if(l)throw new Error("A history only accepts one active listener");return a.addEventListener(L,d),l=e,()=>{a.removeEventListener(L,d),l=null}},createHref:e=>t(a,e),createURL:f,encodeLocation(e){let t=f(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){s=T.Push;let r=F(p.location,e,t);n&&n(r,e),u=c()+1;let d=D(r,u),f=p.createHref(r);try{i.pushState(d,"",f)}catch(h){if(h instanceof DOMException&&"DataCloneError"===h.name)throw h;a.location.assign(f)}o&&l&&l({action:s,location:p.location,delta:1})},replace:function(e,t){s=T.Replace;let r=F(p.location,e,t);n&&n(r,e),u=c();let a=D(r,u),d=p.createHref(r);i.replaceState(a,"",d),o&&l&&l({action:s,location:p.location,delta:0})},go:e=>i.go(e)};return p}var U;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(U||(U={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function H(e,t,n){return void 0===n&&(n="/"),V(e,t,n,!1)}function V(e,t,n,r){let a=re(("string"===typeof t?z(t):t).pathname||"/",n);if(null==a)return null;let o=W(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(o);let i=null;for(let s=0;null==i&&s<o.length;++s){let e=ne(a);i=ee(o[s],e,r)}return i}function W(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let a=(e,a,o)=>{let i={relativePath:void 0===o?e.path||"":o,caseSensitive:!0===e.caseSensitive,childrenIndex:a,route:e};i.relativePath.startsWith("/")&&(I(i.relativePath.startsWith(r),'Absolute route path "'+i.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),i.relativePath=i.relativePath.slice(r.length));let s=le([r,i.relativePath]),l=n.concat(i);e.children&&e.children.length>0&&(I(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+s+'".'),W(e.children,t,l,s)),(null!=e.path||e.index)&&t.push({path:s,score:Z(s,e.index),routesMeta:l})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of $(e.path))a(e,t,r);else a(e,t)})),t}function $(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,a=n.endsWith("?"),o=n.replace(/\?$/,"");if(0===r.length)return a?[o,""]:[o];let i=$(r.join("/")),s=[];return s.push(...i.map((e=>""===e?o:[o,e].join("/")))),a&&s.push(...i),s.map((t=>e.startsWith("/")&&""===t?"/":t))}const K=/^:[\w-]+$/,q=3,Q=2,G=1,J=10,X=-2,Y=e=>"*"===e;function Z(e,t){let n=e.split("/"),r=n.length;return n.some(Y)&&(r+=X),t&&(r+=Q),n.filter((e=>!Y(e))).reduce(((e,t)=>e+(K.test(t)?q:""===t?G:J)),r)}function ee(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,a={},o="/",i=[];for(let s=0;s<r.length;++s){let e=r[s],l=s===r.length-1,u="/"===o?t:t.slice(o.length)||"/",c=te({path:e.relativePath,caseSensitive:e.caseSensitive,end:l},u),d=e.route;if(!c&&l&&n&&!r[r.length-1].route.index&&(c=te({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(a,c.params),i.push({params:a,pathname:le([o,c.pathname]),pathnameBase:ue(le([o,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(o=le([o,c.pathnameBase]))}return i}function te(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);_("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],a="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),a+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?a+="\\/*$":""!==e&&"/"!==e&&(a+="(?:(?=\\/|$))");let o=new RegExp(a,t?void 0:"i");return[o,r]}(e.path,e.caseSensitive,e.end),a=t.match(n);if(!a)return null;let o=a[0],i=o.replace(/(.)\/+$/,"$1"),s=a.slice(1);return{params:r.reduce(((e,t,n)=>{let{paramName:r,isOptional:a}=t;if("*"===r){let e=s[n]||"";i=o.slice(0,o.length-e.length).replace(/(.)\/+$/,"$1")}const l=s[n];return e[r]=a&&!l?void 0:(l||"").replace(/%2F/g,"/"),e}),{}),pathname:o,pathnameBase:i,pattern:e}}function ne(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return _(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function re(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function ae(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function oe(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function ie(e,t){let n=oe(e);return t?n.map(((e,t)=>t===n.length-1?e.pathname:e.pathnameBase)):n.map((e=>e.pathnameBase))}function se(e,t,n,r){let a;void 0===r&&(r=!1),"string"===typeof e?a=z(e):(a=A({},e),I(!a.pathname||!a.pathname.includes("?"),ae("?","pathname","search",a)),I(!a.pathname||!a.pathname.includes("#"),ae("#","pathname","hash",a)),I(!a.search||!a.search.includes("#"),ae("#","search","hash",a)));let o,i=""===e||""===a.pathname,s=i?"/":a.pathname;if(null==s)o=n;else{let e=t.length-1;if(!r&&s.startsWith("..")){let t=s.split("/");for(;".."===t[0];)t.shift(),e-=1;a.pathname=t.join("/")}o=e>=0?t[e]:"/"}let l=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:a=""}="string"===typeof e?z(e):e,o=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:o,search:ce(r),hash:de(a)}}(a,o),u=s&&"/"!==s&&s.endsWith("/"),c=(i||"."===s)&&n.endsWith("/");return l.pathname.endsWith("/")||!u&&!c||(l.pathname+="/"),l}const le=e=>e.join("/").replace(/\/\/+/g,"/"),ue=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),ce=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",de=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;function fe(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}const pe=["post","put","patch","delete"],he=(new Set(pe),["get",...pe]);new Set(he),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function me(){return me=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},me.apply(this,arguments)}const ge=t.createContext(null);const ye=t.createContext(null);const ve=t.createContext(null);const be=t.createContext(null);const we=t.createContext({outlet:null,matches:[],isDataRoute:!1});const xe=t.createContext(null);function ke(){return null!=t.useContext(be)}function Se(){return ke()||I(!1),t.useContext(be).location}function Ee(e){t.useContext(ve).static||t.useLayoutEffect(e)}function Ne(){let{isDataRoute:e}=t.useContext(we);return e?function(){let{router:e}=De(Ie.UseNavigateStable),n=Me(_e.UseNavigateStable),r=t.useRef(!1);return Ee((()=>{r.current=!0})),t.useCallback((function(t,a){void 0===a&&(a={}),r.current&&("number"===typeof t?e.navigate(t):e.navigate(t,me({fromRouteId:n},a)))}),[e,n])}():function(){ke()||I(!1);let e=t.useContext(ge),{basename:n,future:r,navigator:a}=t.useContext(ve),{matches:o}=t.useContext(we),{pathname:i}=Se(),s=JSON.stringify(ie(o,r.v7_relativeSplatPath)),l=t.useRef(!1);Ee((()=>{l.current=!0}));let u=t.useCallback((function(t,r){if(void 0===r&&(r={}),!l.current)return;if("number"===typeof t)return void a.go(t);let o=se(t,JSON.parse(s),i,"path"===r.relative);null==e&&"/"!==n&&(o.pathname="/"===o.pathname?n:le([n,o.pathname])),(r.replace?a.replace:a.push)(o,r.state,r)}),[n,a,s,i,e]);return u}()}const Ce=t.createContext(null);function je(e,n){let{relative:r}=void 0===n?{}:n,{future:a}=t.useContext(ve),{matches:o}=t.useContext(we),{pathname:i}=Se(),s=JSON.stringify(ie(o,a.v7_relativeSplatPath));return t.useMemo((()=>se(e,JSON.parse(s),i,"path"===r)),[e,s,i,r])}function Oe(e,n,r,a){ke()||I(!1);let{navigator:o}=t.useContext(ve),{matches:i}=t.useContext(we),s=i[i.length-1],l=s?s.params:{},u=(s&&s.pathname,s?s.pathnameBase:"/");s&&s.route;let c,d=Se();if(n){var f;let e="string"===typeof n?z(n):n;"/"===u||(null==(f=e.pathname)?void 0:f.startsWith(u))||I(!1),c=e}else c=d;let p=c.pathname||"/",h=p;if("/"!==u){let e=u.replace(/^\//,"").split("/");h="/"+p.replace(/^\//,"").split("/").slice(e.length).join("/")}let m=H(e,{pathname:h});let g=Le(m&&m.map((e=>Object.assign({},e,{params:Object.assign({},l,e.params),pathname:le([u,o.encodeLocation?o.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?u:le([u,o.encodeLocation?o.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),i,r,a);return n&&g?t.createElement(be.Provider,{value:{location:me({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:T.Pop}},g):g}function Pe(){let e=function(){var e;let n=t.useContext(xe),r=Fe(_e.UseRouteError),a=Me(_e.UseRouteError);if(void 0!==n)return n;return null==(e=r.errors)?void 0:e[a]}(),n=fe(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,a="rgba(200,200,200, 0.5)",o={padding:"0.5rem",backgroundColor:a};return t.createElement(t.Fragment,null,t.createElement("h2",null,"Unexpected Application Error!"),t.createElement("h3",{style:{fontStyle:"italic"}},n),r?t.createElement("pre",{style:o},r):null,null)}const Re=t.createElement(Pe,null);class Te extends t.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?t.createElement(we.Provider,{value:this.props.routeContext},t.createElement(xe.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Ae(e){let{routeContext:n,match:r,children:a}=e,o=t.useContext(ge);return o&&o.static&&o.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(o.staticContext._deepestRenderedBoundaryId=r.route.id),t.createElement(we.Provider,{value:n},a)}function Le(e,n,r,a){var o;if(void 0===n&&(n=[]),void 0===r&&(r=null),void 0===a&&(a=null),null==e){var i;if(!r)return null;if(r.errors)e=r.matches;else{if(!(null!=(i=a)&&i.v7_partialHydration&&0===n.length&&!r.initialized&&r.matches.length>0))return null;e=r.matches}}let s=e,l=null==(o=r)?void 0:o.errors;if(null!=l){let e=s.findIndex((e=>e.route.id&&void 0!==(null==l?void 0:l[e.route.id])));e>=0||I(!1),s=s.slice(0,Math.min(s.length,e+1))}let u=!1,c=-1;if(r&&a&&a.v7_partialHydration)for(let t=0;t<s.length;t++){let e=s[t];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(c=t),e.route.id){let{loaderData:t,errors:n}=r,a=e.route.loader&&void 0===t[e.route.id]&&(!n||void 0===n[e.route.id]);if(e.route.lazy||a){u=!0,s=c>=0?s.slice(0,c+1):[s[0]];break}}}return s.reduceRight(((e,a,o)=>{let i,d=!1,f=null,p=null;var h;r&&(i=l&&a.route.id?l[a.route.id]:void 0,f=a.route.errorElement||Re,u&&(c<0&&0===o?(h="route-fallback",!1||ze[h]||(ze[h]=!0),d=!0,p=null):c===o&&(d=!0,p=a.route.hydrateFallbackElement||null)));let m=n.concat(s.slice(0,o+1)),g=()=>{let n;return n=i?f:d?p:a.route.Component?t.createElement(a.route.Component,null):a.route.element?a.route.element:e,t.createElement(Ae,{match:a,routeContext:{outlet:e,matches:m,isDataRoute:null!=r},children:n})};return r&&(a.route.ErrorBoundary||a.route.errorElement||0===o)?t.createElement(Te,{location:r.location,revalidation:r.revalidation,component:f,error:i,children:g(),routeContext:{outlet:null,matches:m,isDataRoute:!0}}):g()}),null)}var Ie=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Ie||{}),_e=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(_e||{});function De(e){let n=t.useContext(ge);return n||I(!1),n}function Fe(e){let n=t.useContext(ye);return n||I(!1),n}function Me(e){let n=function(){let e=t.useContext(we);return e||I(!1),e}(),r=n.matches[n.matches.length-1];return r.route.id||I(!1),r.route.id}const ze={};function Be(e,t){null==e||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}r.startTransition;function Ue(e){let{to:n,replace:r,state:a,relative:o}=e;ke()||I(!1);let{future:i,static:s}=t.useContext(ve),{matches:l}=t.useContext(we),{pathname:u}=Se(),c=Ne(),d=se(n,ie(l,i.v7_relativeSplatPath),u,"path"===o),f=JSON.stringify(d);return t.useEffect((()=>c(JSON.parse(f),{replace:r,state:a,relative:o})),[c,f,o,r,a]),null}function He(e){return function(e){let n=t.useContext(we).outlet;return n?t.createElement(Ce.Provider,{value:e},n):n}(e.context)}function Ve(e){I(!1)}function We(e){let{basename:n="/",children:r=null,location:a,navigationType:o=T.Pop,navigator:i,static:s=!1,future:l}=e;ke()&&I(!1);let u=n.replace(/^\/*/,"/"),c=t.useMemo((()=>({basename:u,navigator:i,static:s,future:me({v7_relativeSplatPath:!1},l)})),[u,l,i,s]);"string"===typeof a&&(a=z(a));let{pathname:d="/",search:f="",hash:p="",state:h=null,key:m="default"}=a,g=t.useMemo((()=>{let e=re(d,u);return null==e?null:{location:{pathname:e,search:f,hash:p,state:h,key:m},navigationType:o}}),[u,d,f,p,h,m,o]);return null==g?null:t.createElement(ve.Provider,{value:c},t.createElement(be.Provider,{children:r,value:g}))}function $e(e){let{children:t,location:n}=e;return Oe(Ke(t),n)}new Promise((()=>{}));t.Component;function Ke(e,n){void 0===n&&(n=[]);let r=[];return t.Children.forEach(e,((e,a)=>{if(!t.isValidElement(e))return;let o=[...n,a];if(e.type===t.Fragment)return void r.push.apply(r,Ke(e.props.children,o));e.type!==Ve&&I(!1),e.props.index&&e.props.children&&I(!1);let i={id:e.props.id||o.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(i.children=Ke(e.props.children,o)),r.push(i)})),r}function qe(){return qe=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},qe.apply(this,arguments)}function Qe(e,t){if(null==e)return{};var n,r,a={},o=Object.keys(e);for(r=0;r<o.length;r++)n=o[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);const Ge=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Je=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"];try{window.__reactRouterVersion="6"}catch(_i){}const Xe=t.createContext({isTransitioning:!1});new Map;const Ye=r.startTransition;l.flushSync,r.useId;function Ze(e){let{basename:n,children:r,future:a,window:o}=e,i=t.useRef();var s;null==i.current&&(i.current=(void 0===(s={window:o,v5Compat:!0})&&(s={}),B((function(e,t){let{pathname:n,search:r,hash:a}=e.location;return F("",{pathname:n,search:r,hash:a},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"===typeof t?t:M(t)}),null,s)));let l=i.current,[u,c]=t.useState({action:l.action,location:l.location}),{v7_startTransition:d}=a||{},f=t.useCallback((e=>{d&&Ye?Ye((()=>c(e))):c(e)}),[c,d]);return t.useLayoutEffect((()=>l.listen(f)),[l,f]),t.useEffect((()=>Be(a)),[a]),t.createElement(We,{basename:n,children:r,location:u.location,navigationType:u.action,navigator:l,future:a})}const et="undefined"!==typeof window&&"undefined"!==typeof window.document&&"undefined"!==typeof window.document.createElement,tt=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,nt=t.forwardRef((function(e,n){let r,{onClick:a,relative:o,reloadDocument:i,replace:s,state:l,target:u,to:c,preventScrollReset:d,viewTransition:f}=e,p=Qe(e,Ge),{basename:h}=t.useContext(ve),m=!1;if("string"===typeof c&&tt.test(c)&&(r=c,et))try{let e=new URL(window.location.href),t=c.startsWith("//")?new URL(e.protocol+c):new URL(c),n=re(t.pathname,h);t.origin===e.origin&&null!=n?c=n+t.search+t.hash:m=!0}catch(_i){}let g=function(e,n){let{relative:r}=void 0===n?{}:n;ke()||I(!1);let{basename:a,navigator:o}=t.useContext(ve),{hash:i,pathname:s,search:l}=je(e,{relative:r}),u=s;return"/"!==a&&(u="/"===s?a:le([a,s])),o.createHref({pathname:u,search:l,hash:i})}(c,{relative:o}),y=function(e,n){let{target:r,replace:a,state:o,preventScrollReset:i,relative:s,viewTransition:l}=void 0===n?{}:n,u=Ne(),c=Se(),d=je(e,{relative:s});return t.useCallback((t=>{if(function(e,t){return 0===e.button&&(!t||"_self"===t)&&!function(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}(e)}(t,r)){t.preventDefault();let n=void 0!==a?a:M(c)===M(d);u(e,{replace:n,state:o,preventScrollReset:i,relative:s,viewTransition:l})}}),[c,u,d,a,o,r,e,i,s,l])}(c,{replace:s,state:l,target:u,preventScrollReset:d,relative:o,viewTransition:f});return t.createElement("a",qe({},p,{href:r||g,onClick:m||i?a:function(e){a&&a(e),e.defaultPrevented||y(e)},ref:n,target:u}))}));const rt=t.forwardRef((function(e,n){let{"aria-current":r="page",caseSensitive:a=!1,className:o="",end:i=!1,style:s,to:l,viewTransition:u,children:c}=e,d=Qe(e,Je),f=je(l,{relative:d.relative}),p=Se(),h=t.useContext(ye),{navigator:m,basename:g}=t.useContext(ve),y=null!=h&&function(e,n){void 0===n&&(n={});let r=t.useContext(Xe);null==r&&I(!1);let{basename:a}=it(at.useViewTransitionState),o=je(e,{relative:n.relative});if(!r.isTransitioning)return!1;let i=re(r.currentLocation.pathname,a)||r.currentLocation.pathname,s=re(r.nextLocation.pathname,a)||r.nextLocation.pathname;return null!=te(o.pathname,s)||null!=te(o.pathname,i)}(f)&&!0===u,v=m.encodeLocation?m.encodeLocation(f).pathname:f.pathname,b=p.pathname,w=h&&h.navigation&&h.navigation.location?h.navigation.location.pathname:null;a||(b=b.toLowerCase(),w=w?w.toLowerCase():null,v=v.toLowerCase()),w&&g&&(w=re(w,g)||w);const x="/"!==v&&v.endsWith("/")?v.length-1:v.length;let k,S=b===v||!i&&b.startsWith(v)&&"/"===b.charAt(x),E=null!=w&&(w===v||!i&&w.startsWith(v)&&"/"===w.charAt(v.length)),N={isActive:S,isPending:E,isTransitioning:y},C=S?r:void 0;k="function"===typeof o?o(N):[o,S?"active":null,E?"pending":null,y?"transitioning":null].filter(Boolean).join(" ");let j="function"===typeof s?s(N):s;return t.createElement(nt,qe({},d,{"aria-current":C,className:k,ref:n,style:j,to:l,viewTransition:u}),"function"===typeof c?c(N):c)}));var at,ot;function it(e){let n=t.useContext(ge);return n||I(!1),n}(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(at||(at={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(ot||(ot={}));function st(){return st=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},st.apply(null,arguments)}function lt(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}var ut=n(868);function ct(e){if("object"!==typeof e||null===e)return!1;const t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function dt(e){if(t.isValidElement(e)||!ct(e))return e;const n={};return Object.keys(e).forEach((t=>{n[t]=dt(e[t])})),n}function ft(e,n){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{clone:!0};const a=r.clone?st({},e):e;return ct(e)&&ct(n)&&Object.keys(n).forEach((o=>{t.isValidElement(n[o])?a[o]=n[o]:ct(n[o])&&Object.prototype.hasOwnProperty.call(e,o)&&ct(e[o])?a[o]=ft(e[o],n[o],r):r.clone?a[o]=ct(n[o])?dt(n[o]):n[o]:a[o]=n[o]})),a}const pt={xs:0,sm:600,md:900,lg:1200,xl:1536},ht={keys:["xs","sm","md","lg","xl"],up:e=>"@media (min-width:".concat(pt[e],"px)")};function mt(e,t,n){const r=e.theme||{};if(Array.isArray(t)){const e=r.breakpoints||ht;return t.reduce(((r,a,o)=>(r[e.up(e.keys[o])]=n(t[o]),r)),{})}if("object"===typeof t){const e=r.breakpoints||ht;return Object.keys(t).reduce(((r,a)=>{if(-1!==Object.keys(e.values||pt).indexOf(a)){r[e.up(a)]=n(t[a],a)}else{const e=a;r[e]=t[e]}return r}),{})}return n(t)}function gt(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};var t;return(null==(t=e.keys)?void 0:t.reduce(((t,n)=>(t[e.up(n)]={},t)),{}))||{}}function yt(e,t){return e.reduce(((e,t)=>{const n=e[t];return(!n||0===Object.keys(n).length)&&delete e[t],e}),t)}function vt(e){if("string"!==typeof e)throw new Error((0,ut.A)(7));return e.charAt(0).toUpperCase()+e.slice(1)}function bt(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||"string"!==typeof t)return null;if(e&&e.vars&&n){const n="vars.".concat(t).split(".").reduce(((e,t)=>e&&e[t]?e[t]:null),e);if(null!=n)return n}return t.split(".").reduce(((e,t)=>e&&null!=e[t]?e[t]:null),e)}function wt(e,t,n){let r,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:n;return r="function"===typeof e?e(n):Array.isArray(e)?e[n]||a:bt(e,n)||a,t&&(r=t(r,a,e)),r}const xt=function(e){const{prop:t,cssProperty:n=e.prop,themeKey:r,transform:a}=e,o=e=>{if(null==e[t])return null;const o=e[t],i=bt(e.theme,r)||{};return mt(e,o,(e=>{let r=wt(i,a,e);return e===r&&"string"===typeof e&&(r=wt(i,a,"".concat(t).concat("default"===e?"":vt(e)),e)),!1===n?r:{[n]:r}}))};return o.propTypes={},o.filterProps=[t],o};const kt=function(e,t){return t?ft(e,t,{clone:!1}):e};const St={m:"margin",p:"padding"},Et={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},Nt={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},Ct=function(e){const t={};return n=>(void 0===t[n]&&(t[n]=e(n)),t[n])}((e=>{if(e.length>2){if(!Nt[e])return[e];e=Nt[e]}const[t,n]=e.split(""),r=St[t],a=Et[n]||"";return Array.isArray(a)?a.map((e=>r+e)):[r+a]})),jt=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],Ot=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"],Pt=[...jt,...Ot];function Rt(e,t,n,r){var a;const o=null!=(a=bt(e,t,!1))?a:n;return"number"===typeof o?e=>"string"===typeof e?e:o*e:Array.isArray(o)?e=>"string"===typeof e?e:o[e]:"function"===typeof o?o:()=>{}}function Tt(e){return Rt(e,"spacing",8)}function At(e,t){if("string"===typeof t||null==t)return t;const n=e(Math.abs(t));return t>=0?n:"number"===typeof n?-n:"-".concat(n)}function Lt(e,t,n,r){if(-1===t.indexOf(n))return null;const a=function(e,t){return n=>e.reduce(((e,r)=>(e[r]=At(t,n),e)),{})}(Ct(n),r);return mt(e,e[n],a)}function It(e,t){const n=Tt(e.theme);return Object.keys(e).map((r=>Lt(e,t,r,n))).reduce(kt,{})}function _t(e){return It(e,jt)}function Dt(e){return It(e,Ot)}function Ft(e){return It(e,Pt)}_t.propTypes={},_t.filterProps=jt,Dt.propTypes={},Dt.filterProps=Ot,Ft.propTypes={},Ft.filterProps=Pt;const Mt=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const r=t.reduce(((e,t)=>(t.filterProps.forEach((n=>{e[n]=t})),e)),{}),a=e=>Object.keys(e).reduce(((t,n)=>r[n]?kt(t,r[n](e)):t),{});return a.propTypes={},a.filterProps=t.reduce(((e,t)=>e.concat(t.filterProps)),[]),a};function zt(e){return"number"!==typeof e?e:"".concat(e,"px solid")}function Bt(e,t){return xt({prop:e,themeKey:"borders",transform:t})}const Ut=Bt("border",zt),Ht=Bt("borderTop",zt),Vt=Bt("borderRight",zt),Wt=Bt("borderBottom",zt),$t=Bt("borderLeft",zt),Kt=Bt("borderColor"),qt=Bt("borderTopColor"),Qt=Bt("borderRightColor"),Gt=Bt("borderBottomColor"),Jt=Bt("borderLeftColor"),Xt=Bt("outline",zt),Yt=Bt("outlineColor"),Zt=e=>{if(void 0!==e.borderRadius&&null!==e.borderRadius){const t=Rt(e.theme,"shape.borderRadius",4),n=e=>({borderRadius:At(t,e)});return mt(e,e.borderRadius,n)}return null};Zt.propTypes={},Zt.filterProps=["borderRadius"];Mt(Ut,Ht,Vt,Wt,$t,Kt,qt,Qt,Gt,Jt,Zt,Xt,Yt);const en=e=>{if(void 0!==e.gap&&null!==e.gap){const t=Rt(e.theme,"spacing",8),n=e=>({gap:At(t,e)});return mt(e,e.gap,n)}return null};en.propTypes={},en.filterProps=["gap"];const tn=e=>{if(void 0!==e.columnGap&&null!==e.columnGap){const t=Rt(e.theme,"spacing",8),n=e=>({columnGap:At(t,e)});return mt(e,e.columnGap,n)}return null};tn.propTypes={},tn.filterProps=["columnGap"];const nn=e=>{if(void 0!==e.rowGap&&null!==e.rowGap){const t=Rt(e.theme,"spacing",8),n=e=>({rowGap:At(t,e)});return mt(e,e.rowGap,n)}return null};nn.propTypes={},nn.filterProps=["rowGap"];Mt(en,tn,nn,xt({prop:"gridColumn"}),xt({prop:"gridRow"}),xt({prop:"gridAutoFlow"}),xt({prop:"gridAutoColumns"}),xt({prop:"gridAutoRows"}),xt({prop:"gridTemplateColumns"}),xt({prop:"gridTemplateRows"}),xt({prop:"gridTemplateAreas"}),xt({prop:"gridArea"}));function rn(e,t){return"grey"===t?t:e}Mt(xt({prop:"color",themeKey:"palette",transform:rn}),xt({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:rn}),xt({prop:"backgroundColor",themeKey:"palette",transform:rn}));function an(e){return e<=1&&0!==e?"".concat(100*e,"%"):e}const on=xt({prop:"width",transform:an}),sn=e=>{if(void 0!==e.maxWidth&&null!==e.maxWidth){const t=t=>{var n,r;const a=(null==(n=e.theme)||null==(n=n.breakpoints)||null==(n=n.values)?void 0:n[t])||pt[t];return a?"px"!==(null==(r=e.theme)||null==(r=r.breakpoints)?void 0:r.unit)?{maxWidth:"".concat(a).concat(e.theme.breakpoints.unit)}:{maxWidth:a}:{maxWidth:an(t)}};return mt(e,e.maxWidth,t)}return null};sn.filterProps=["maxWidth"];const ln=xt({prop:"minWidth",transform:an}),un=xt({prop:"height",transform:an}),cn=xt({prop:"maxHeight",transform:an}),dn=xt({prop:"minHeight",transform:an}),fn=(xt({prop:"size",cssProperty:"width",transform:an}),xt({prop:"size",cssProperty:"height",transform:an}),Mt(on,sn,ln,un,cn,dn,xt({prop:"boxSizing"})),{border:{themeKey:"borders",transform:zt},borderTop:{themeKey:"borders",transform:zt},borderRight:{themeKey:"borders",transform:zt},borderBottom:{themeKey:"borders",transform:zt},borderLeft:{themeKey:"borders",transform:zt},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:zt},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:Zt},color:{themeKey:"palette",transform:rn},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:rn},backgroundColor:{themeKey:"palette",transform:rn},p:{style:Dt},pt:{style:Dt},pr:{style:Dt},pb:{style:Dt},pl:{style:Dt},px:{style:Dt},py:{style:Dt},padding:{style:Dt},paddingTop:{style:Dt},paddingRight:{style:Dt},paddingBottom:{style:Dt},paddingLeft:{style:Dt},paddingX:{style:Dt},paddingY:{style:Dt},paddingInline:{style:Dt},paddingInlineStart:{style:Dt},paddingInlineEnd:{style:Dt},paddingBlock:{style:Dt},paddingBlockStart:{style:Dt},paddingBlockEnd:{style:Dt},m:{style:_t},mt:{style:_t},mr:{style:_t},mb:{style:_t},ml:{style:_t},mx:{style:_t},my:{style:_t},margin:{style:_t},marginTop:{style:_t},marginRight:{style:_t},marginBottom:{style:_t},marginLeft:{style:_t},marginX:{style:_t},marginY:{style:_t},marginInline:{style:_t},marginInlineStart:{style:_t},marginInlineEnd:{style:_t},marginBlock:{style:_t},marginBlockStart:{style:_t},marginBlockEnd:{style:_t},displayPrint:{cssProperty:!1,transform:e=>({"@media print":{display:e}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:en},rowGap:{style:nn},columnGap:{style:tn},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:an},maxWidth:{style:sn},minWidth:{transform:an},height:{transform:an},maxHeight:{transform:an},minHeight:{transform:an},boxSizing:{},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}});const pn=function(){function e(e,t,n,r){const a={[e]:t,theme:n},o=r[e];if(!o)return{[e]:t};const{cssProperty:i=e,themeKey:s,transform:l,style:u}=o;if(null==t)return null;if("typography"===s&&"inherit"===t)return{[e]:t};const c=bt(n,s)||{};if(u)return u(a);return mt(a,t,(t=>{let n=wt(c,l,t);return t===n&&"string"===typeof t&&(n=wt(c,l,"".concat(e).concat("default"===t?"":vt(t)),t)),!1===i?n:{[i]:n}}))}return function t(n){var r;const{sx:a,theme:o={}}=n||{};if(!a)return null;const i=null!=(r=o.unstable_sxConfig)?r:fn;function s(n){let r=n;if("function"===typeof n)r=n(o);else if("object"!==typeof n)return n;if(!r)return null;const a=gt(o.breakpoints),s=Object.keys(a);let l=a;return Object.keys(r).forEach((n=>{const a=(s=r[n],u=o,"function"===typeof s?s(u):s);var s,u;if(null!==a&&void 0!==a)if("object"===typeof a)if(i[n])l=kt(l,e(n,a,o,i));else{const e=mt({theme:o},a,(e=>({[n]:e})));!function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const r=t.reduce(((e,t)=>e.concat(Object.keys(t))),[]),a=new Set(r);return t.every((e=>a.size===Object.keys(e).length))}(e,a)?l=kt(l,e):l[n]=t({sx:a,theme:o})}else l=kt(l,e(n,a,o,i))})),yt(s,l)}return Array.isArray(a)?a.map(s):s(a)}}();pn.filterProps=["sx"];const hn=pn,mn=["values","unit","step"];function gn(e){const{values:t={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:n="px",step:r=5}=e,a=lt(e,mn),o=(e=>{const t=Object.keys(e).map((t=>({key:t,val:e[t]})))||[];return t.sort(((e,t)=>e.val-t.val)),t.reduce(((e,t)=>st({},e,{[t.key]:t.val})),{})})(t),i=Object.keys(o);function s(e){const r="number"===typeof t[e]?t[e]:e;return"@media (min-width:".concat(r).concat(n,")")}function l(e){const a="number"===typeof t[e]?t[e]:e;return"@media (max-width:".concat(a-r/100).concat(n,")")}function u(e,a){const o=i.indexOf(a);return"@media (min-width:".concat("number"===typeof t[e]?t[e]:e).concat(n,") and ")+"(max-width:".concat((-1!==o&&"number"===typeof t[i[o]]?t[i[o]]:a)-r/100).concat(n,")")}return st({keys:i,values:o,up:s,down:l,between:u,only:function(e){return i.indexOf(e)+1<i.length?u(e,i[i.indexOf(e)+1]):s(e)},not:function(e){const t=i.indexOf(e);return 0===t?s(i[1]):t===i.length-1?l(i[t]):u(e,i[i.indexOf(e)+1]).replace("@media","@media not all and")},unit:n},a)}const yn={borderRadius:4};function vn(e,t){const n=this;if(n.vars&&"function"===typeof n.getColorSchemeSelector){const r=n.getColorSchemeSelector(e).replace(/(\[[^\]]+\])/,"*:where($1)");return{[r]:t}}return n.palette.mode===e?t:{}}const bn=["breakpoints","palette","spacing","shape"];const wn=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{breakpoints:t={},palette:n={},spacing:r,shape:a={}}=e,o=lt(e,bn),i=gn(t),s=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:8;if(e.mui)return e;const t=Tt({spacing:e}),n=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return(0===n.length?[1]:n).map((e=>{const n=t(e);return"number"===typeof n?"".concat(n,"px"):n})).join(" ")};return n.mui=!0,n}(r);let l=ft({breakpoints:i,direction:"ltr",components:{},palette:st({mode:"light"},n),spacing:s,shape:st({},yn,a)},o);l.applyStyles=vn;for(var u=arguments.length,c=new Array(u>1?u-1:0),d=1;d<u;d++)c[d-1]=arguments[d];return l=c.reduce(((e,t)=>ft(e,t)),l),l.unstable_sxConfig=st({},fn,null==o?void 0:o.unstable_sxConfig),l.unstable_sx=function(e){return hn({sx:e,theme:this})},l};function xn(e,t){return st({toolbar:{minHeight:56,[e.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[e.up("sm")]:{minHeight:64}}},t)}var kn=n(266);const Sn={black:"#000",white:"#fff"},En={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"},Nn={50:"#f3e5f5",100:"#e1bee7",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",600:"#8e24aa",700:"#7b1fa2",800:"#6a1b9a",900:"#4a148c",A100:"#ea80fc",A200:"#e040fb",A400:"#d500f9",A700:"#aa00ff"},Cn={50:"#ffebee",100:"#ffcdd2",200:"#ef9a9a",300:"#e57373",400:"#ef5350",500:"#f44336",600:"#e53935",700:"#d32f2f",800:"#c62828",900:"#b71c1c",A100:"#ff8a80",A200:"#ff5252",A400:"#ff1744",A700:"#d50000"},jn={50:"#fff3e0",100:"#ffe0b2",200:"#ffcc80",300:"#ffb74d",400:"#ffa726",500:"#ff9800",600:"#fb8c00",700:"#f57c00",800:"#ef6c00",900:"#e65100",A100:"#ffd180",A200:"#ffab40",A400:"#ff9100",A700:"#ff6d00"},On={50:"#e3f2fd",100:"#bbdefb",200:"#90caf9",300:"#64b5f6",400:"#42a5f5",500:"#2196f3",600:"#1e88e5",700:"#1976d2",800:"#1565c0",900:"#0d47a1",A100:"#82b1ff",A200:"#448aff",A400:"#2979ff",A700:"#2962ff"},Pn={50:"#e1f5fe",100:"#b3e5fc",200:"#81d4fa",300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",600:"#039be5",700:"#0288d1",800:"#0277bd",900:"#01579b",A100:"#80d8ff",A200:"#40c4ff",A400:"#00b0ff",A700:"#0091ea"},Rn={50:"#e8f5e9",100:"#c8e6c9",200:"#a5d6a7",300:"#81c784",400:"#66bb6a",500:"#4caf50",600:"#43a047",700:"#388e3c",800:"#2e7d32",900:"#1b5e20",A100:"#b9f6ca",A200:"#69f0ae",A400:"#00e676",A700:"#00c853"},Tn=["mode","contrastThreshold","tonalOffset"],An={text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:Sn.white,default:Sn.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}},Ln={text:{primary:Sn.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:Sn.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}};function In(e,t,n,r){const a=r.light||r,o=r.dark||1.5*r;e[t]||(e.hasOwnProperty(n)?e[t]=e[n]:"light"===t?e.light=(0,kn.a)(e.main,a):"dark"===t&&(e.dark=(0,kn.e$)(e.main,o)))}function _n(e){const{mode:t="light",contrastThreshold:n=3,tonalOffset:r=.2}=e,a=lt(e,Tn),o=e.primary||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:On[200],light:On[50],dark:On[400]}:{main:On[700],light:On[400],dark:On[800]}}(t),i=e.secondary||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:Nn[200],light:Nn[50],dark:Nn[400]}:{main:Nn[500],light:Nn[300],dark:Nn[700]}}(t),s=e.error||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:Cn[500],light:Cn[300],dark:Cn[700]}:{main:Cn[700],light:Cn[400],dark:Cn[800]}}(t),l=e.info||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:Pn[400],light:Pn[300],dark:Pn[700]}:{main:Pn[700],light:Pn[500],dark:Pn[900]}}(t),u=e.success||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:Rn[400],light:Rn[300],dark:Rn[700]}:{main:Rn[800],light:Rn[500],dark:Rn[900]}}(t),c=e.warning||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:jn[400],light:jn[300],dark:jn[700]}:{main:"#ed6c02",light:jn[500],dark:jn[900]}}(t);function d(e){return(0,kn.eM)(e,Ln.text.primary)>=n?Ln.text.primary:An.text.primary}const f=e=>{let{color:t,name:n,mainShade:a=500,lightShade:o=300,darkShade:i=700}=e;if(t=st({},t),!t.main&&t[a]&&(t.main=t[a]),!t.hasOwnProperty("main"))throw new Error((0,ut.A)(11,n?" (".concat(n,")"):"",a));if("string"!==typeof t.main)throw new Error((0,ut.A)(12,n?" (".concat(n,")"):"",JSON.stringify(t.main)));return In(t,"light",o,r),In(t,"dark",i,r),t.contrastText||(t.contrastText=d(t.main)),t},p={dark:Ln,light:An};return ft(st({common:st({},Sn),mode:t,primary:f({color:o,name:"primary"}),secondary:f({color:i,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:f({color:s,name:"error"}),warning:f({color:c,name:"warning"}),info:f({color:l,name:"info"}),success:f({color:u,name:"success"}),grey:En,contrastThreshold:n,getContrastText:d,augmentColor:f,tonalOffset:r},p[t]),a)}const Dn=["fontFamily","fontSize","fontWeightLight","fontWeightRegular","fontWeightMedium","fontWeightBold","htmlFontSize","allVariants","pxToRem"];const Fn={textTransform:"uppercase"},Mn='"Roboto", "Helvetica", "Arial", sans-serif';function zn(e,t){const n="function"===typeof t?t(e):t,{fontFamily:r=Mn,fontSize:a=14,fontWeightLight:o=300,fontWeightRegular:i=400,fontWeightMedium:s=500,fontWeightBold:l=700,htmlFontSize:u=16,allVariants:c,pxToRem:d}=n,f=lt(n,Dn);const p=a/14,h=d||(e=>"".concat(e/u*p,"rem")),m=(e,t,n,a,o)=>{return st({fontFamily:r,fontWeight:e,fontSize:h(t),lineHeight:n},r===Mn?{letterSpacing:"".concat((i=a/t,Math.round(1e5*i)/1e5),"em")}:{},o,c);var i},g={h1:m(o,96,1.167,-1.5),h2:m(o,60,1.2,-.5),h3:m(i,48,1.167,0),h4:m(i,34,1.235,.25),h5:m(i,24,1.334,0),h6:m(s,20,1.6,.15),subtitle1:m(i,16,1.75,.15),subtitle2:m(s,14,1.57,.1),body1:m(i,16,1.5,.15),body2:m(i,14,1.43,.15),button:m(s,14,1.75,.4,Fn),caption:m(i,12,1.66,.4),overline:m(i,12,2.66,1,Fn),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return ft(st({htmlFontSize:u,pxToRem:h,fontFamily:r,fontSize:a,fontWeightLight:o,fontWeightRegular:i,fontWeightMedium:s,fontWeightBold:l},g),f,{clone:!1})}function Bn(){return["".concat(arguments.length<=0?void 0:arguments[0],"px ").concat(arguments.length<=1?void 0:arguments[1],"px ").concat(arguments.length<=2?void 0:arguments[2],"px ").concat(arguments.length<=3?void 0:arguments[3],"px rgba(0,0,0,").concat(.2,")"),"".concat(arguments.length<=4?void 0:arguments[4],"px ").concat(arguments.length<=5?void 0:arguments[5],"px ").concat(arguments.length<=6?void 0:arguments[6],"px ").concat(arguments.length<=7?void 0:arguments[7],"px rgba(0,0,0,").concat(.14,")"),"".concat(arguments.length<=8?void 0:arguments[8],"px ").concat(arguments.length<=9?void 0:arguments[9],"px ").concat(arguments.length<=10?void 0:arguments[10],"px ").concat(arguments.length<=11?void 0:arguments[11],"px rgba(0,0,0,").concat(.12,")")].join(",")}const Un=["none",Bn(0,2,1,-1,0,1,1,0,0,1,3,0),Bn(0,3,1,-2,0,2,2,0,0,1,5,0),Bn(0,3,3,-2,0,3,4,0,0,1,8,0),Bn(0,2,4,-1,0,4,5,0,0,1,10,0),Bn(0,3,5,-1,0,5,8,0,0,1,14,0),Bn(0,3,5,-1,0,6,10,0,0,1,18,0),Bn(0,4,5,-2,0,7,10,1,0,2,16,1),Bn(0,5,5,-3,0,8,10,1,0,3,14,2),Bn(0,5,6,-3,0,9,12,1,0,3,16,2),Bn(0,6,6,-3,0,10,14,1,0,4,18,3),Bn(0,6,7,-4,0,11,15,1,0,4,20,3),Bn(0,7,8,-4,0,12,17,2,0,5,22,4),Bn(0,7,8,-4,0,13,19,2,0,5,24,4),Bn(0,7,9,-4,0,14,21,2,0,5,26,4),Bn(0,8,9,-5,0,15,22,2,0,6,28,5),Bn(0,8,10,-5,0,16,24,2,0,6,30,5),Bn(0,8,11,-5,0,17,26,2,0,6,32,5),Bn(0,9,11,-5,0,18,28,2,0,7,34,6),Bn(0,9,12,-6,0,19,29,2,0,7,36,6),Bn(0,10,13,-6,0,20,31,3,0,8,38,7),Bn(0,10,13,-6,0,21,33,3,0,8,40,7),Bn(0,10,14,-6,0,22,35,3,0,8,42,7),Bn(0,11,14,-7,0,23,36,3,0,9,44,8),Bn(0,11,15,-7,0,24,38,3,0,9,46,8)],Hn=["duration","easing","delay"],Vn={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},Wn={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function $n(e){return"".concat(Math.round(e),"ms")}function Kn(e){if(!e)return 0;const t=e/36;return Math.round(10*(4+15*t**.25+t/5))}function qn(e){const t=st({},Vn,e.easing),n=st({},Wn,e.duration);return st({getAutoHeightDuration:Kn,create:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["all"],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{duration:a=n.standard,easing:o=t.easeInOut,delay:i=0}=r;lt(r,Hn);return(Array.isArray(e)?e:[e]).map((e=>"".concat(e," ").concat("string"===typeof a?a:$n(a)," ").concat(o," ").concat("string"===typeof i?i:$n(i)))).join(",")}},e,{easing:t,duration:n})}const Qn={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500},Gn=["breakpoints","mixins","spacing","palette","transitions","typography","shape"];function Jn(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{mixins:t={},palette:n={},transitions:r={},typography:a={}}=e,o=lt(e,Gn);if(e.vars&&void 0===e.generateCssVars)throw new Error((0,ut.A)(18));const i=_n(n),s=wn(e);let l=ft(s,{mixins:xn(s.breakpoints,t),palette:i,shadows:Un.slice(),typography:zn(i,a),transitions:qn(r),zIndex:st({},Qn)});l=ft(l,o);for(var u=arguments.length,c=new Array(u>1?u-1:0),d=1;d<u;d++)c[d-1]=arguments[d];return l=c.reduce(((e,t)=>ft(e,t)),l),l.unstable_sxConfig=st({},fn,null==o?void 0:o.unstable_sxConfig),l.unstable_sx=function(e){return hn({sx:e,theme:this})},l}const Xn=Jn;const Yn=t.createContext(null);function Zn(){return t.useContext(Yn)}const er="function"===typeof Symbol&&Symbol.for?Symbol.for("mui.nested"):"__THEME_NESTED__";var tr=n(579);const nr=function(e){const{children:n,theme:r}=e,a=Zn(),o=t.useMemo((()=>{const e=null===a?r:function(e,t){if("function"===typeof t)return t(e);return st({},e,t)}(a,r);return null!=e&&(e[er]=null!==a),e}),[r,a]);return(0,tr.jsx)(Yn.Provider,{value:o,children:n})};var rr=function(){function e(e){var t=this;this._insertTag=function(e){var n;n=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,n),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(function(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),void 0!==e.nonce&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}(this));var t=this.tags[this.tags.length-1];if(this.isSpeedy){var n=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(t);try{n.insertRule(e,n.cssRules.length)}catch(_i){}}else t.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach((function(e){var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)})),this.tags=[],this.ctr=0},e}(),ar=Math.abs,or=String.fromCharCode,ir=Object.assign;function sr(e){return e.trim()}function lr(e,t,n){return e.replace(t,n)}function ur(e,t){return e.indexOf(t)}function cr(e,t){return 0|e.charCodeAt(t)}function dr(e,t,n){return e.slice(t,n)}function fr(e){return e.length}function pr(e){return e.length}function hr(e,t){return t.push(e),e}var mr=1,gr=1,yr=0,vr=0,br=0,wr="";function xr(e,t,n,r,a,o,i){return{value:e,root:t,parent:n,type:r,props:a,children:o,line:mr,column:gr,length:i,return:""}}function kr(e,t){return ir(xr("",null,null,"",null,null,0),e,{length:-e.length},t)}function Sr(){return br=vr>0?cr(wr,--vr):0,gr--,10===br&&(gr=1,mr--),br}function Er(){return br=vr<yr?cr(wr,vr++):0,gr++,10===br&&(gr=1,mr++),br}function Nr(){return cr(wr,vr)}function Cr(){return vr}function jr(e,t){return dr(wr,e,t)}function Or(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function Pr(e){return mr=gr=1,yr=fr(wr=e),vr=0,[]}function Rr(e){return wr="",e}function Tr(e){return sr(jr(vr-1,Ir(91===e?e+2:40===e?e+1:e)))}function Ar(e){for(;(br=Nr())&&br<33;)Er();return Or(e)>2||Or(br)>3?"":" "}function Lr(e,t){for(;--t&&Er()&&!(br<48||br>102||br>57&&br<65||br>70&&br<97););return jr(e,Cr()+(t<6&&32==Nr()&&32==Er()))}function Ir(e){for(;Er();)switch(br){case e:return vr;case 34:case 39:34!==e&&39!==e&&Ir(br);break;case 40:41===e&&Ir(e);break;case 92:Er()}return vr}function _r(e,t){for(;Er()&&e+br!==57&&(e+br!==84||47!==Nr()););return"/*"+jr(t,vr-1)+"*"+or(47===e?e:Er())}function Dr(e){for(;!Or(Nr());)Er();return jr(e,vr)}var Fr="-ms-",Mr="-moz-",zr="-webkit-",Br="comm",Ur="rule",Hr="decl",Vr="@keyframes";function Wr(e,t){for(var n="",r=pr(e),a=0;a<r;a++)n+=t(e[a],a,e,t)||"";return n}function $r(e,t,n,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case Hr:return e.return=e.return||e.value;case Br:return"";case Vr:return e.return=e.value+"{"+Wr(e.children,r)+"}";case Ur:e.value=e.props.join(",")}return fr(n=Wr(e.children,r))?e.return=e.value+"{"+n+"}":""}function Kr(e){return Rr(qr("",null,null,null,[""],e=Pr(e),0,[0],e))}function qr(e,t,n,r,a,o,i,s,l){for(var u=0,c=0,d=i,f=0,p=0,h=0,m=1,g=1,y=1,v=0,b="",w=a,x=o,k=r,S=b;g;)switch(h=v,v=Er()){case 40:if(108!=h&&58==cr(S,d-1)){-1!=ur(S+=lr(Tr(v),"&","&\f"),"&\f")&&(y=-1);break}case 34:case 39:case 91:S+=Tr(v);break;case 9:case 10:case 13:case 32:S+=Ar(h);break;case 92:S+=Lr(Cr()-1,7);continue;case 47:switch(Nr()){case 42:case 47:hr(Gr(_r(Er(),Cr()),t,n),l);break;default:S+="/"}break;case 123*m:s[u++]=fr(S)*y;case 125*m:case 59:case 0:switch(v){case 0:case 125:g=0;case 59+c:-1==y&&(S=lr(S,/\f/g,"")),p>0&&fr(S)-d&&hr(p>32?Jr(S+";",r,n,d-1):Jr(lr(S," ","")+";",r,n,d-2),l);break;case 59:S+=";";default:if(hr(k=Qr(S,t,n,u,c,a,s,b,w=[],x=[],d),o),123===v)if(0===c)qr(S,t,k,k,w,o,d,s,x);else switch(99===f&&110===cr(S,3)?100:f){case 100:case 108:case 109:case 115:qr(e,k,k,r&&hr(Qr(e,k,k,0,0,a,s,b,a,w=[],d),x),a,x,d,s,r?w:x);break;default:qr(S,k,k,k,[""],x,0,s,x)}}u=c=p=0,m=y=1,b=S="",d=i;break;case 58:d=1+fr(S),p=h;default:if(m<1)if(123==v)--m;else if(125==v&&0==m++&&125==Sr())continue;switch(S+=or(v),v*m){case 38:y=c>0?1:(S+="\f",-1);break;case 44:s[u++]=(fr(S)-1)*y,y=1;break;case 64:45===Nr()&&(S+=Tr(Er())),f=Nr(),c=d=fr(b=S+=Dr(Cr())),v++;break;case 45:45===h&&2==fr(S)&&(m=0)}}return o}function Qr(e,t,n,r,a,o,i,s,l,u,c){for(var d=a-1,f=0===a?o:[""],p=pr(f),h=0,m=0,g=0;h<r;++h)for(var y=0,v=dr(e,d+1,d=ar(m=i[h])),b=e;y<p;++y)(b=sr(m>0?f[y]+" "+v:lr(v,/&\f/g,f[y])))&&(l[g++]=b);return xr(e,t,n,0===a?Ur:s,l,u,c)}function Gr(e,t,n){return xr(e,t,n,Br,or(br),dr(e,2,-2),0)}function Jr(e,t,n,r){return xr(e,t,n,Hr,dr(e,0,r),dr(e,r+1,-1),r)}var Xr=function(e,t,n){for(var r=0,a=0;r=a,a=Nr(),38===r&&12===a&&(t[n]=1),!Or(a);)Er();return jr(e,vr)},Yr=function(e,t){return Rr(function(e,t){var n=-1,r=44;do{switch(Or(r)){case 0:38===r&&12===Nr()&&(t[n]=1),e[n]+=Xr(vr-1,t,n);break;case 2:e[n]+=Tr(r);break;case 4:if(44===r){e[++n]=58===Nr()?"&\f":"",t[n]=e[n].length;break}default:e[n]+=or(r)}}while(r=Er());return e}(Pr(e),t))},Zr=new WeakMap,ea=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,n=e.parent,r=e.column===n.column&&e.line===n.line;"rule"!==n.type;)if(!(n=n.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||Zr.get(n))&&!r){Zr.set(e,!0);for(var a=[],o=Yr(t,a),i=n.props,s=0,l=0;s<o.length;s++)for(var u=0;u<i.length;u++,l++)e.props[l]=a[s]?o[s].replace(/&\f/g,i[u]):i[u]+" "+o[s]}}},ta=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}};function na(e,t){switch(function(e,t){return 45^cr(e,0)?(((t<<2^cr(e,0))<<2^cr(e,1))<<2^cr(e,2))<<2^cr(e,3):0}(e,t)){case 5103:return zr+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return zr+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return zr+e+Mr+e+Fr+e+e;case 6828:case 4268:return zr+e+Fr+e+e;case 6165:return zr+e+Fr+"flex-"+e+e;case 5187:return zr+e+lr(e,/(\w+).+(:[^]+)/,zr+"box-$1$2"+Fr+"flex-$1$2")+e;case 5443:return zr+e+Fr+"flex-item-"+lr(e,/flex-|-self/,"")+e;case 4675:return zr+e+Fr+"flex-line-pack"+lr(e,/align-content|flex-|-self/,"")+e;case 5548:return zr+e+Fr+lr(e,"shrink","negative")+e;case 5292:return zr+e+Fr+lr(e,"basis","preferred-size")+e;case 6060:return zr+"box-"+lr(e,"-grow","")+zr+e+Fr+lr(e,"grow","positive")+e;case 4554:return zr+lr(e,/([^-])(transform)/g,"$1"+zr+"$2")+e;case 6187:return lr(lr(lr(e,/(zoom-|grab)/,zr+"$1"),/(image-set)/,zr+"$1"),e,"")+e;case 5495:case 3959:return lr(e,/(image-set\([^]*)/,zr+"$1$`$1");case 4968:return lr(lr(e,/(.+:)(flex-)?(.*)/,zr+"box-pack:$3"+Fr+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+zr+e+e;case 4095:case 3583:case 4068:case 2532:return lr(e,/(.+)-inline(.+)/,zr+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(fr(e)-1-t>6)switch(cr(e,t+1)){case 109:if(45!==cr(e,t+4))break;case 102:return lr(e,/(.+:)(.+)-([^]+)/,"$1"+zr+"$2-$3$1"+Mr+(108==cr(e,t+3)?"$3":"$2-$3"))+e;case 115:return~ur(e,"stretch")?na(lr(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(115!==cr(e,t+1))break;case 6444:switch(cr(e,fr(e)-3-(~ur(e,"!important")&&10))){case 107:return lr(e,":",":"+zr)+e;case 101:return lr(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+zr+(45===cr(e,14)?"inline-":"")+"box$3$1"+zr+"$2$3$1"+Fr+"$2box$3")+e}break;case 5936:switch(cr(e,t+11)){case 114:return zr+e+Fr+lr(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return zr+e+Fr+lr(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return zr+e+Fr+lr(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return zr+e+Fr+e+e}return e}var ra=[function(e,t,n,r){if(e.length>-1&&!e.return)switch(e.type){case Hr:e.return=na(e.value,e.length);break;case Vr:return Wr([kr(e,{value:lr(e.value,"@","@"+zr)})],r);case Ur:if(e.length)return function(e,t){return e.map(t).join("")}(e.props,(function(t){switch(function(e,t){return(e=t.exec(e))?e[0]:e}(t,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return Wr([kr(e,{props:[lr(t,/:(read-\w+)/,":-moz-$1")]})],r);case"::placeholder":return Wr([kr(e,{props:[lr(t,/:(plac\w+)/,":"+zr+"input-$1")]}),kr(e,{props:[lr(t,/:(plac\w+)/,":-moz-$1")]}),kr(e,{props:[lr(t,/:(plac\w+)/,Fr+"input-$1")]})],r)}return""}))}}],aa=function(e){var t=e.key;if("css"===t){var n=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(n,(function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))}))}var r,a,o=e.stylisPlugins||ra,i={},s=[];r=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),(function(e){for(var t=e.getAttribute("data-emotion").split(" "),n=1;n<t.length;n++)i[t[n]]=!0;s.push(e)}));var l,u,c=[$r,(u=function(e){l.insert(e)},function(e){e.root||(e=e.return)&&u(e)})],d=function(e){var t=pr(e);return function(n,r,a,o){for(var i="",s=0;s<t;s++)i+=e[s](n,r,a,o)||"";return i}}([ea,ta].concat(o,c));a=function(e,t,n,r){l=n,function(e){Wr(Kr(e),d)}(e?e+"{"+t.styles+"}":t.styles),r&&(f.inserted[t.name]=!0)};var f={key:t,sheet:new rr({key:t,container:r,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:i,registered:{},insert:a};return f.sheet.hydrate(s),f};var oa=function(e,t,n){var r=e.key+"-"+t.name;!1===n&&void 0===e.registered[r]&&(e.registered[r]=t.styles)},ia=function(e,t,n){oa(e,t,n);var r=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var a=t;do{e.insert(t===a?"."+r:"",a,e.sheet,!0),a=a.next}while(void 0!==a)}};var sa={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};function la(e){var t=Object.create(null);return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}var ua=/[A-Z]|^ms/g,ca=/_EMO_([^_]+?)_([^]*?)_EMO_/g,da=function(e){return 45===e.charCodeAt(1)},fa=function(e){return null!=e&&"boolean"!==typeof e},pa=la((function(e){return da(e)?e:e.replace(ua,"-$&").toLowerCase()})),ha=function(e,t){switch(e){case"animation":case"animationName":if("string"===typeof t)return t.replace(ca,(function(e,t,n){return ga={name:t,styles:n,next:ga},t}))}return 1===sa[e]||da(e)||"number"!==typeof t||0===t?t:t+"px"};function ma(e,t,n){if(null==n)return"";var r=n;if(void 0!==r.__emotion_styles)return r;switch(typeof n){case"boolean":return"";case"object":var a=n;if(1===a.anim)return ga={name:a.name,styles:a.styles,next:ga},a.name;var o=n;if(void 0!==o.styles){var i=o.next;if(void 0!==i)for(;void 0!==i;)ga={name:i.name,styles:i.styles,next:ga},i=i.next;return o.styles+";"}return function(e,t,n){var r="";if(Array.isArray(n))for(var a=0;a<n.length;a++)r+=ma(e,t,n[a])+";";else for(var o in n){var i=n[o];if("object"!==typeof i){var s=i;null!=t&&void 0!==t[s]?r+=o+"{"+t[s]+"}":fa(s)&&(r+=pa(o)+":"+ha(o,s)+";")}else if(!Array.isArray(i)||"string"!==typeof i[0]||null!=t&&void 0!==t[i[0]]){var l=ma(e,t,i);switch(o){case"animation":case"animationName":r+=pa(o)+":"+l+";";break;default:r+=o+"{"+l+"}"}}else for(var u=0;u<i.length;u++)fa(i[u])&&(r+=pa(o)+":"+ha(o,i[u])+";")}return r}(e,t,n);case"function":if(void 0!==e){var s=ga,l=n(e);return ga=s,ma(e,t,l)}}var u=n;if(null==t)return u;var c=t[u];return void 0!==c?c:u}var ga,ya=/label:\s*([^\s;{]+)\s*(;|$)/g;function va(e,t,n){if(1===e.length&&"object"===typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var r=!0,a="";ga=void 0;var o=e[0];null==o||void 0===o.raw?(r=!1,a+=ma(n,t,o)):a+=o[0];for(var i=1;i<e.length;i++){if(a+=ma(n,t,e[i]),r)a+=o[i]}ya.lastIndex=0;for(var s,l="";null!==(s=ya.exec(a));)l+="-"+s[1];var u=function(e){for(var t,n=0,r=0,a=e.length;a>=4;++r,a-=4)t=***********(65535&(t=255&e.charCodeAt(r)|(255&e.charCodeAt(++r))<<8|(255&e.charCodeAt(++r))<<16|(255&e.charCodeAt(++r))<<24))+(59797*(t>>>16)<<16),n=***********(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^***********(65535&n)+(59797*(n>>>16)<<16);switch(a){case 3:n^=(255&e.charCodeAt(r+2))<<16;case 2:n^=(255&e.charCodeAt(r+1))<<8;case 1:n=***********(65535&(n^=255&e.charCodeAt(r)))+(59797*(n>>>16)<<16)}return(((n=***********(65535&(n^=n>>>13))+(59797*(n>>>16)<<16))^n>>>15)>>>0).toString(36)}(a)+l;return{name:u,styles:a,next:ga}}var ba=!!r.useInsertionEffect&&r.useInsertionEffect,wa=ba||function(e){return e()},xa=ba||t.useLayoutEffect,ka=t.createContext("undefined"!==typeof HTMLElement?aa({key:"css"}):null),Sa=(ka.Provider,function(e){return(0,t.forwardRef)((function(n,r){var a=(0,t.useContext)(ka);return e(n,a,r)}))}),Ea=t.createContext({});var Na={}.hasOwnProperty,Ca="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",ja=function(e){var t=e.cache,n=e.serialized,r=e.isStringTag;return oa(t,n,r),wa((function(){return ia(t,n,r)})),null},Oa=Sa((function(e,n,r){var a=e.css;"string"===typeof a&&void 0!==n.registered[a]&&(a=n.registered[a]);var o=e[Ca],i=[a],s="";"string"===typeof e.className?s=function(e,t,n){var r="";return n.split(" ").forEach((function(n){void 0!==e[n]?t.push(e[n]+";"):n&&(r+=n+" ")})),r}(n.registered,i,e.className):null!=e.className&&(s=e.className+" ");var l=va(i,void 0,t.useContext(Ea));s+=n.key+"-"+l.name;var u={};for(var c in e)Na.call(e,c)&&"css"!==c&&c!==Ca&&(u[c]=e[c]);return u.className=s,r&&(u.ref=r),t.createElement(t.Fragment,null,t.createElement(ja,{cache:n,serialized:l,isStringTag:"string"===typeof o}),t.createElement(o,u))})),Pa=Oa;const Ra=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;const n=t.useContext(Ea);return n&&(r=n,0!==Object.keys(r).length)?n:e;var r},Ta=["value"],Aa=t.createContext();const La=function(e){let{value:t}=e,n=lt(e,Ta);return(0,tr.jsx)(Aa.Provider,st({value:null==t||t},n))};function Ia(e,t){const n=st({},t);return Object.keys(e).forEach((r=>{if(r.toString().match(/^(components|slots)$/))n[r]=st({},e[r],n[r]);else if(r.toString().match(/^(componentsProps|slotProps)$/)){const a=e[r]||{},o=t[r];n[r]={},o&&Object.keys(o)?a&&Object.keys(a)?(n[r]=st({},o),Object.keys(a).forEach((e=>{n[r][e]=Ia(a[e],o[e])}))):n[r]=o:n[r]=a}else void 0===n[r]&&(n[r]=e[r])})),n}const _a=t.createContext(void 0);function Da(e){let{props:n,name:r}=e;return function(e){const{theme:t,name:n,props:r}=e;if(!t||!t.components||!t.components[n])return r;const a=t.components[n];return a.defaultProps?Ia(a.defaultProps,r):a.styleOverrides||a.variants?r:Ia(a,r)}({props:n,name:r,theme:{components:t.useContext(_a)}})}const Fa=function(e){let{value:t,children:n}=e;return(0,tr.jsx)(_a.Provider,{value:t,children:n})},Ma={};function za(e,n,r){let a=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return t.useMemo((()=>{const t=e&&n[e]||n;if("function"===typeof r){const o=r(t),i=e?st({},n,{[e]:o}):o;return a?()=>i:i}return st({},n,e?{[e]:r}:r)}),[e,n,r,a])}const Ba=function(e){const{children:t,theme:n,themeId:r}=e,a=Ra(Ma),o=Zn()||Ma,i=za(r,a,n),s=za(r,o,n,!0),l="rtl"===i.direction;return(0,tr.jsx)(nr,{theme:s,children:(0,tr.jsx)(Ea.Provider,{value:i,children:(0,tr.jsx)(La,{value:l,children:(0,tr.jsx)(Fa,{value:null==i?void 0:i.components,children:t})})})})},Ua="$$material",Ha=["theme"];function Va(e){let{theme:t}=e,n=lt(e,Ha);const r=t[Ua];let a=r||t;return"function"!==typeof t&&(r&&!r.vars?a=st({},r,{vars:null}):t&&!t.vars&&(a=st({},t,{vars:null}))),(0,tr.jsx)(Ba,st({},n,{themeId:r?Ua:void 0,theme:a}))}var Wa=function(e,n){var r=arguments;if(null==n||!Na.call(n,"css"))return t.createElement.apply(void 0,r);var a=r.length,o=new Array(a);o[0]=Pa,o[1]=function(e,t){var n={};for(var r in t)Na.call(t,r)&&(n[r]=t[r]);return n[Ca]=e,n}(e,n);for(var i=2;i<a;i++)o[i]=r[i];return t.createElement.apply(null,o)};!function(e){var t;t||(t=e.JSX||(e.JSX={}))}(Wa||(Wa={}));var $a=Sa((function(e,n){var r=va([e.styles],void 0,t.useContext(Ea)),a=t.useRef();return xa((function(){var e=n.key+"-global",t=new n.sheet.constructor({key:e,nonce:n.sheet.nonce,container:n.sheet.container,speedy:n.sheet.isSpeedy}),o=!1,i=document.querySelector('style[data-emotion="'+e+" "+r.name+'"]');return n.sheet.tags.length&&(t.before=n.sheet.tags[0]),null!==i&&(o=!0,i.setAttribute("data-emotion",e),t.hydrate([i])),a.current=[t,o],function(){t.flush()}}),[n]),xa((function(){var e=a.current,t=e[0];if(e[1])e[1]=!1;else{if(void 0!==r.next&&ia(n,r.next,!0),t.tags.length){var o=t.tags[t.tags.length-1].nextElementSibling;t.before=o,t.flush()}n.insert("",r,t,!1)}}),[n,r.name]),null}));function Ka(e){const{styles:t,defaultTheme:n={}}=e,r="function"===typeof t?e=>{return t(void 0===(r=e)||null===r||0===Object.keys(r).length?n:e);var r}:t;return(0,tr.jsx)($a,{styles:r})}const qa=wn();const Qa=function(){return Ra(arguments.length>0&&void 0!==arguments[0]?arguments[0]:qa)};const Ga=function(e){let{styles:t,themeId:n,defaultTheme:r={}}=e;const a=Qa(r),o="function"===typeof t?t(n&&a[n]||a):t;return(0,tr.jsx)(Ka,{styles:o})},Ja=Xn();const Xa=function(e){return(0,tr.jsx)(Ga,st({},e,{defaultTheme:Ja,themeId:Ua}))},Ya=(e,t)=>st({WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",boxSizing:"border-box",WebkitTextSizeAdjust:"100%"},t&&!e.vars&&{colorScheme:e.palette.mode}),Za=e=>st({color:(e.vars||e).palette.text.primary},e.typography.body1,{backgroundColor:(e.vars||e).palette.background.default,"@media print":{backgroundColor:(e.vars||e).palette.common.white}});const eo=function(e){const n=Da({props:e,name:"MuiCssBaseline"}),{children:r,enableColorScheme:a=!1}=n;return(0,tr.jsxs)(t.Fragment,{children:[(0,tr.jsx)(Xa,{styles:e=>function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];var n;const r={};t&&e.colorSchemes&&Object.entries(e.colorSchemes).forEach((t=>{let[n,a]=t;var o;r[e.getColorSchemeSelector(n).replace(/\s*&/,"")]={colorScheme:null==(o=a.palette)?void 0:o.mode}}));let a=st({html:Ya(e,t),"*, *::before, *::after":{boxSizing:"inherit"},"strong, b":{fontWeight:e.typography.fontWeightBold},body:st({margin:0},Za(e),{"&::backdrop":{backgroundColor:(e.vars||e).palette.background.default}})},r);const o=null==(n=e.components)||null==(n=n.MuiCssBaseline)?void 0:n.styleOverrides;return o&&(a=[a,o]),a}(e,a)}),r]})};function to(e){return to="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},to(e)}function no(e){var t=function(e,t){if("object"!=to(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=to(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==to(t)?t:t+""}function ro(e,t,n){return(t=no(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ao(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function oo(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?ao(Object(n),!0).forEach((function(t){ro(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):ao(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const io=e=>"string"===typeof e,so=()=>{let e,t;const n=new Promise(((n,r)=>{e=n,t=r}));return n.resolve=e,n.reject=t,n},lo=e=>null==e?"":""+e,uo=/###/g,co=e=>e&&e.indexOf("###")>-1?e.replace(uo,"."):e,fo=e=>!e||io(e),po=(e,t,n)=>{const r=io(t)?t.split("."):t;let a=0;for(;a<r.length-1;){if(fo(e))return{};const t=co(r[a]);!e[t]&&n&&(e[t]=new n),e=Object.prototype.hasOwnProperty.call(e,t)?e[t]:{},++a}return fo(e)?{}:{obj:e,k:co(r[a])}},ho=(e,t,n)=>{const{obj:r,k:a}=po(e,t,Object);if(void 0!==r||1===t.length)return void(r[a]=n);let o=t[t.length-1],i=t.slice(0,t.length-1),s=po(e,i,Object);for(;void 0===s.obj&&i.length;)o="".concat(i[i.length-1],".").concat(o),i=i.slice(0,i.length-1),s=po(e,i,Object),s&&s.obj&&"undefined"!==typeof s.obj["".concat(s.k,".").concat(o)]&&(s.obj=void 0);s.obj["".concat(s.k,".").concat(o)]=n},mo=(e,t)=>{const{obj:n,k:r}=po(e,t);if(n)return n[r]},go=(e,t,n)=>{for(const r in t)"__proto__"!==r&&"constructor"!==r&&(r in e?io(e[r])||e[r]instanceof String||io(t[r])||t[r]instanceof String?n&&(e[r]=t[r]):go(e[r],t[r],n):e[r]=t[r]);return e},yo=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var vo={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const bo=e=>io(e)?e.replace(/[&<>"'\/]/g,(e=>vo[e])):e;const wo=[" ",",","?","!",";"],xo=new class{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const t=this.regExpMap.get(e);if(void 0!==t)return t;const n=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,n),this.regExpQueue.push(e),n}}(20),ko=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".";if(!e)return;if(e[t])return e[t];const r=t.split(n);let a=e;for(let o=0;o<r.length;){if(!a||"object"!==typeof a)return;let e,t="";for(let i=o;i<r.length;++i)if(i!==o&&(t+=n),t+=r[i],e=a[t],void 0!==e){if(["string","number","boolean"].indexOf(typeof e)>-1&&i<r.length-1)continue;o+=i-o+1;break}a=e}return a},So=e=>e&&e.replace("_","-"),Eo={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console&&console[e]&&console[e].apply(console,t)}};class No{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.init(e,t)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||Eo,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,n,r){return r&&!this.debug?null:(io(e[0])&&(e[0]="".concat(n).concat(this.prefix," ").concat(e[0])),this.logger[t](e))}create(e){return new No(this.logger,oo(oo({},{prefix:"".concat(this.prefix,":").concat(e,":")}),this.options))}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new No(this.logger,e)}}var Co=new No;class jo{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach((e=>{this.observers[e]||(this.observers[e]=new Map);const n=this.observers[e].get(t)||0;this.observers[e].set(t,n+1)})),this}off(e,t){this.observers[e]&&(t?this.observers[e].delete(t):delete this.observers[e])}emit(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];if(this.observers[e]){Array.from(this.observers[e].entries()).forEach((e=>{let[t,r]=e;for(let a=0;a<r;a++)t(...n)}))}if(this.observers["*"]){Array.from(this.observers["*"].entries()).forEach((t=>{let[r,a]=t;for(let o=0;o<a;o++)r.apply(r,[e,...n])}))}}}class Oo extends jo{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const a=void 0!==r.keySeparator?r.keySeparator:this.options.keySeparator,o=void 0!==r.ignoreJSONStructure?r.ignoreJSONStructure:this.options.ignoreJSONStructure;let i;e.indexOf(".")>-1?i=e.split("."):(i=[e,t],n&&(Array.isArray(n)?i.push(...n):io(n)&&a?i.push(...n.split(a)):i.push(n)));const s=mo(this.data,i);return!s&&!t&&!n&&e.indexOf(".")>-1&&(e=i[0],t=i[1],n=i.slice(2).join(".")),!s&&o&&io(n)?ko(this.data&&this.data[e]&&this.data[e][t],n,a):s}addResource(e,t,n,r){let a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{silent:!1};const o=void 0!==a.keySeparator?a.keySeparator:this.options.keySeparator;let i=[e,t];n&&(i=i.concat(o?n.split(o):n)),e.indexOf(".")>-1&&(i=e.split("."),r=t,t=i[1]),this.addNamespaces(t),ho(this.data,i,r),a.silent||this.emit("added",e,t,n,r)}addResources(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{silent:!1};for(const a in n)(io(n[a])||Array.isArray(n[a]))&&this.addResource(e,t,a,n[a],{silent:!0});r.silent||this.emit("added",e,t,n)}addResourceBundle(e,t,n,r,a){let o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{silent:!1,skipCopy:!1},i=[e,t];e.indexOf(".")>-1&&(i=e.split("."),r=n,n=t,t=i[1]),this.addNamespaces(t);let s=mo(this.data,i)||{};o.skipCopy||(n=JSON.parse(JSON.stringify(n))),r?go(s,n,a):s=oo(oo({},s),n),ho(this.data,i,s),o.silent||this.emit("added",e,t,n)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return t||(t=this.options.defaultNS),"v1"===this.options.compatibilityAPI?oo(oo({},{}),this.getResource(e,t)):this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find((e=>t[e]&&Object.keys(t[e]).length>0))}toJSON(){return this.data}}var Po={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,n,r,a){return e.forEach((e=>{this.processors[e]&&(t=this.processors[e].process(t,n,r,a))})),t}};const Ro={};class To extends jo{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),((e,t,n)=>{e.forEach((e=>{t[e]&&(n[e]=t[e])}))})(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=Co.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};if(void 0===e||null===e)return!1;const n=this.resolve(e,t);return n&&void 0!==n.res}extractFromKey(e,t){let n=void 0!==t.nsSeparator?t.nsSeparator:this.options.nsSeparator;void 0===n&&(n=":");const r=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator;let a=t.ns||this.options.defaultNS||[];const o=n&&e.indexOf(n)>-1,i=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!((e,t,n)=>{t=t||"",n=n||"";const r=wo.filter((e=>t.indexOf(e)<0&&n.indexOf(e)<0));if(0===r.length)return!0;const a=xo.getRegExp("(".concat(r.map((e=>"?"===e?"\\?":e)).join("|"),")"));let o=!a.test(e);if(!o){const t=e.indexOf(n);t>0&&!a.test(e.substring(0,t))&&(o=!0)}return o})(e,n,r);if(o&&!i){const t=e.match(this.interpolator.nestingRegexp);if(t&&t.length>0)return{key:e,namespaces:io(a)?[a]:a};const o=e.split(n);(n!==r||n===r&&this.options.ns.indexOf(o[0])>-1)&&(a=o.shift()),e=o.join(r)}return{key:e,namespaces:io(a)?[a]:a}}translate(e,t,n){if("object"!==typeof t&&this.options.overloadTranslationOptionHandler&&(t=this.options.overloadTranslationOptionHandler(arguments)),"object"===typeof t&&(t=oo({},t)),t||(t={}),void 0===e||null===e)return"";Array.isArray(e)||(e=[String(e)]);const r=void 0!==t.returnDetails?t.returnDetails:this.options.returnDetails,a=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,{key:o,namespaces:i}=this.extractFromKey(e[e.length-1],t),s=i[i.length-1],l=t.lng||this.language,u=t.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(l&&"cimode"===l.toLowerCase()){if(u){const e=t.nsSeparator||this.options.nsSeparator;return r?{res:"".concat(s).concat(e).concat(o),usedKey:o,exactUsedKey:o,usedLng:l,usedNS:s,usedParams:this.getUsedParamsDetails(t)}:"".concat(s).concat(e).concat(o)}return r?{res:o,usedKey:o,exactUsedKey:o,usedLng:l,usedNS:s,usedParams:this.getUsedParamsDetails(t)}:o}const c=this.resolve(e,t);let d=c&&c.res;const f=c&&c.usedKey||o,p=c&&c.exactUsedKey||o,h=Object.prototype.toString.apply(d),m=void 0!==t.joinArrays?t.joinArrays:this.options.joinArrays,g=!this.i18nFormat||this.i18nFormat.handleAsObject,y=!io(d)&&"boolean"!==typeof d&&"number"!==typeof d;if(!(g&&d&&y&&["[object Number]","[object Function]","[object RegExp]"].indexOf(h)<0)||io(m)&&Array.isArray(d))if(g&&io(m)&&Array.isArray(d))d=d.join(m),d&&(d=this.extendTranslation(d,e,t,n));else{let r=!1,i=!1;const u=void 0!==t.count&&!io(t.count),f=To.hasDefaultValue(t),p=u?this.pluralResolver.getSuffix(l,t.count,t):"",h=t.ordinal&&u?this.pluralResolver.getSuffix(l,t.count,{ordinal:!1}):"",m=u&&!t.ordinal&&0===t.count&&this.pluralResolver.shouldUseIntlApi(),g=m&&t["defaultValue".concat(this.options.pluralSeparator,"zero")]||t["defaultValue".concat(p)]||t["defaultValue".concat(h)]||t.defaultValue;!this.isValidLookup(d)&&f&&(r=!0,d=g),this.isValidLookup(d)||(i=!0,d=o);const y=(t.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&i?void 0:d,v=f&&g!==d&&this.options.updateMissing;if(i||r||v){if(this.logger.log(v?"updateKey":"missingKey",l,s,o,v?g:d),a){const e=this.resolve(o,oo(oo({},t),{},{keySeparator:!1}));e&&e.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let e=[];const n=this.languageUtils.getFallbackCodes(this.options.fallbackLng,t.lng||this.language);if("fallback"===this.options.saveMissingTo&&n&&n[0])for(let t=0;t<n.length;t++)e.push(n[t]);else"all"===this.options.saveMissingTo?e=this.languageUtils.toResolveHierarchy(t.lng||this.language):e.push(t.lng||this.language);const r=(e,n,r)=>{const a=f&&r!==d?r:y;this.options.missingKeyHandler?this.options.missingKeyHandler(e,s,n,a,v,t):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(e,s,n,a,v,t),this.emit("missingKey",e,s,n,d)};this.options.saveMissing&&(this.options.saveMissingPlurals&&u?e.forEach((e=>{const n=this.pluralResolver.getSuffixes(e,t);m&&t["defaultValue".concat(this.options.pluralSeparator,"zero")]&&n.indexOf("".concat(this.options.pluralSeparator,"zero"))<0&&n.push("".concat(this.options.pluralSeparator,"zero")),n.forEach((n=>{r([e],o+n,t["defaultValue".concat(n)]||g)}))})):r(e,o,g))}d=this.extendTranslation(d,e,t,c,n),i&&d===o&&this.options.appendNamespaceToMissingKey&&(d="".concat(s,":").concat(o)),(i||r)&&this.options.parseMissingKeyHandler&&(d="v1"!==this.options.compatibilityAPI?this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?"".concat(s,":").concat(o):o,r?d:void 0):this.options.parseMissingKeyHandler(d))}else{if(!t.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const e=this.options.returnedObjectHandler?this.options.returnedObjectHandler(f,d,oo(oo({},t),{},{ns:i})):"key '".concat(o," (").concat(this.language,")' returned an object instead of string.");return r?(c.res=e,c.usedParams=this.getUsedParamsDetails(t),c):e}if(a){const e=Array.isArray(d),n=e?[]:{},r=e?p:f;for(const o in d)if(Object.prototype.hasOwnProperty.call(d,o)){const e="".concat(r).concat(a).concat(o);n[o]=this.translate(e,oo(oo({},t),{joinArrays:!1,ns:i})),n[o]===e&&(n[o]=d[o])}d=n}}return r?(c.res=d,c.usedParams=this.getUsedParamsDetails(t),c):d}extendTranslation(e,t,n,r,a){var o=this;if(this.i18nFormat&&this.i18nFormat.parse)e=this.i18nFormat.parse(e,oo(oo({},this.options.interpolation.defaultVariables),n),n.lng||this.language||r.usedLng,r.usedNS,r.usedKey,{resolved:r});else if(!n.skipInterpolation){n.interpolation&&this.interpolator.init(oo(oo({},n),{interpolation:oo(oo({},this.options.interpolation),n.interpolation)}));const i=io(e)&&(n&&n.interpolation&&void 0!==n.interpolation.skipOnVariables?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let s;if(i){const t=e.match(this.interpolator.nestingRegexp);s=t&&t.length}let l=n.replace&&!io(n.replace)?n.replace:n;if(this.options.interpolation.defaultVariables&&(l=oo(oo({},this.options.interpolation.defaultVariables),l)),e=this.interpolator.interpolate(e,l,n.lng||this.language||r.usedLng,n),i){const t=e.match(this.interpolator.nestingRegexp);s<(t&&t.length)&&(n.nest=!1)}!n.lng&&"v1"!==this.options.compatibilityAPI&&r&&r.res&&(n.lng=this.language||r.usedLng),!1!==n.nest&&(e=this.interpolator.nest(e,(function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return a&&a[0]===r[0]&&!n.context?(o.logger.warn("It seems you are nesting recursively key: ".concat(r[0]," in key: ").concat(t[0])),null):o.translate(...r,t)}),n)),n.interpolation&&this.interpolator.reset()}const i=n.postProcess||this.options.postProcess,s=io(i)?[i]:i;return void 0!==e&&null!==e&&s&&s.length&&!1!==n.applyPostProcessor&&(e=Po.handle(s,e,t,this.options&&this.options.postProcessPassResolved?oo({i18nResolved:oo(oo({},r),{},{usedParams:this.getUsedParamsDetails(n)})},n):n,this)),e}resolve(e){let t,n,r,a,o,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return io(e)&&(e=[e]),e.forEach((e=>{if(this.isValidLookup(t))return;const s=this.extractFromKey(e,i),l=s.key;n=l;let u=s.namespaces;this.options.fallbackNS&&(u=u.concat(this.options.fallbackNS));const c=void 0!==i.count&&!io(i.count),d=c&&!i.ordinal&&0===i.count&&this.pluralResolver.shouldUseIntlApi(),f=void 0!==i.context&&(io(i.context)||"number"===typeof i.context)&&""!==i.context,p=i.lngs?i.lngs:this.languageUtils.toResolveHierarchy(i.lng||this.language,i.fallbackLng);u.forEach((e=>{this.isValidLookup(t)||(o=e,!Ro["".concat(p[0],"-").concat(e)]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(o)&&(Ro["".concat(p[0],"-").concat(e)]=!0,this.logger.warn('key "'.concat(n,'" for languages "').concat(p.join(", "),'" won\'t get resolved as namespace "').concat(o,'" was not yet loaded'),"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),p.forEach((n=>{if(this.isValidLookup(t))return;a=n;const o=[l];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(o,l,n,e,i);else{let e;c&&(e=this.pluralResolver.getSuffix(n,i.count,i));const t="".concat(this.options.pluralSeparator,"zero"),r="".concat(this.options.pluralSeparator,"ordinal").concat(this.options.pluralSeparator);if(c&&(o.push(l+e),i.ordinal&&0===e.indexOf(r)&&o.push(l+e.replace(r,this.options.pluralSeparator)),d&&o.push(l+t)),f){const n="".concat(l).concat(this.options.contextSeparator).concat(i.context);o.push(n),c&&(o.push(n+e),i.ordinal&&0===e.indexOf(r)&&o.push(n+e.replace(r,this.options.pluralSeparator)),d&&o.push(n+t))}}let s;for(;s=o.pop();)this.isValidLookup(t)||(r=s,t=this.getResource(n,e,s,i))})))}))})),{res:t,usedKey:n,exactUsedKey:r,usedLng:a,usedNS:o}}isValidLookup(e){return void 0!==e&&!(!this.options.returnNull&&null===e)&&!(!this.options.returnEmptyString&&""===e)}getResource(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(e,t,n,r):this.resourceStore.getResource(e,t,n,r)}getUsedParamsDetails(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],n=e.replace&&!io(e.replace);let r=n?e.replace:e;if(n&&"undefined"!==typeof e.count&&(r.count=e.count),this.options.interpolation.defaultVariables&&(r=oo(oo({},this.options.interpolation.defaultVariables),r)),!n){r=oo({},r);for(const e of t)delete r[e]}return r}static hasDefaultValue(e){const t="defaultValue";for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t===n.substring(0,12)&&void 0!==e[n])return!0;return!1}}const Ao=e=>e.charAt(0).toUpperCase()+e.slice(1);class Lo{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=Co.create("languageUtils")}getScriptPartFromCode(e){if(!(e=So(e))||e.indexOf("-")<0)return null;const t=e.split("-");return 2===t.length?null:(t.pop(),"x"===t[t.length-1].toLowerCase()?null:this.formatLanguageCode(t.join("-")))}getLanguagePartFromCode(e){if(!(e=So(e))||e.indexOf("-")<0)return e;const t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(io(e)&&e.indexOf("-")>-1){if("undefined"!==typeof Intl&&"undefined"!==typeof Intl.getCanonicalLocales)try{let t=Intl.getCanonicalLocales(e)[0];if(t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t)return t}catch(_i){}const t=["hans","hant","latn","cyrl","cans","mong","arab"];let n=e.split("-");return this.options.lowerCaseLng?n=n.map((e=>e.toLowerCase())):2===n.length?(n[0]=n[0].toLowerCase(),n[1]=n[1].toUpperCase(),t.indexOf(n[1].toLowerCase())>-1&&(n[1]=Ao(n[1].toLowerCase()))):3===n.length&&(n[0]=n[0].toLowerCase(),2===n[1].length&&(n[1]=n[1].toUpperCase()),"sgn"!==n[0]&&2===n[2].length&&(n[2]=n[2].toUpperCase()),t.indexOf(n[1].toLowerCase())>-1&&(n[1]=Ao(n[1].toLowerCase())),t.indexOf(n[2].toLowerCase())>-1&&(n[2]=Ao(n[2].toLowerCase()))),n.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach((e=>{if(t)return;const n=this.formatLanguageCode(e);this.options.supportedLngs&&!this.isSupportedCode(n)||(t=n)})),!t&&this.options.supportedLngs&&e.forEach((e=>{if(t)return;const n=this.getLanguagePartFromCode(e);if(this.isSupportedCode(n))return t=n;t=this.options.supportedLngs.find((e=>e===n?e:e.indexOf("-")<0&&n.indexOf("-")<0?void 0:e.indexOf("-")>0&&n.indexOf("-")<0&&e.substring(0,e.indexOf("-"))===n||0===e.indexOf(n)&&n.length>1?e:void 0))})),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(e,t){if(!e)return[];if("function"===typeof e&&(e=e(t)),io(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let n=e[t];return n||(n=e[this.getScriptPartFromCode(t)]),n||(n=e[this.formatLanguageCode(t)]),n||(n=e[this.getLanguagePartFromCode(t)]),n||(n=e.default),n||[]}toResolveHierarchy(e,t){const n=this.getFallbackCodes(t||this.options.fallbackLng||[],e),r=[],a=e=>{e&&(this.isSupportedCode(e)?r.push(e):this.logger.warn("rejecting language code not found in supportedLngs: ".concat(e)))};return io(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?("languageOnly"!==this.options.load&&a(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&a(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&a(this.getLanguagePartFromCode(e))):io(e)&&a(this.formatLanguageCode(e)),n.forEach((e=>{r.indexOf(e)<0&&a(this.formatLanguageCode(e))})),r}}let Io=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],_o={1:e=>Number(e>1),2:e=>Number(1!=e),3:e=>0,4:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),5:e=>Number(0==e?0:1==e?1:2==e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5),6:e=>Number(1==e?0:e>=2&&e<=4?1:2),7:e=>Number(1==e?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),8:e=>Number(1==e?0:2==e?1:8!=e&&11!=e?2:3),9:e=>Number(e>=2),10:e=>Number(1==e?0:2==e?1:e<7?2:e<11?3:4),11:e=>Number(1==e||11==e?0:2==e||12==e?1:e>2&&e<20?2:3),12:e=>Number(e%10!=1||e%100==11),13:e=>Number(0!==e),14:e=>Number(1==e?0:2==e?1:3==e?2:3),15:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&(e%100<10||e%100>=20)?1:2),16:e=>Number(e%10==1&&e%100!=11?0:0!==e?1:2),17:e=>Number(1==e||e%10==1&&e%100!=11?0:1),18:e=>Number(0==e?0:1==e?1:2),19:e=>Number(1==e?0:0==e||e%100>1&&e%100<11?1:e%100>10&&e%100<20?2:3),20:e=>Number(1==e?0:0==e||e%100>0&&e%100<20?1:2),21:e=>Number(e%100==1?1:e%100==2?2:e%100==3||e%100==4?3:0),22:e=>Number(1==e?0:2==e?1:(e<0||e>10)&&e%10==0?2:3)};const Do=["v1","v2","v3"],Fo=["v4"],Mo={zero:0,one:1,two:2,few:3,many:4,other:5};class zo{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=Co.create("pluralResolver"),this.options.compatibilityJSON&&!Fo.includes(this.options.compatibilityJSON)||"undefined"!==typeof Intl&&Intl.PluralRules||(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=(()=>{const e={};return Io.forEach((t=>{t.lngs.forEach((n=>{e[n]={numbers:t.nr,plurals:_o[t.fc]}}))})),e})(),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.shouldUseIntlApi()){const r=So("dev"===e?"en":e),a=t.ordinal?"ordinal":"cardinal",o=JSON.stringify({cleanedCode:r,type:a});if(o in this.pluralRulesCache)return this.pluralRulesCache[o];let i;try{i=new Intl.PluralRules(r,{type:a})}catch(n){if(!e.match(/-|_/))return;const r=this.languageUtils.getLanguagePartFromCode(e);i=this.getRule(r,t)}return this.pluralRulesCache[o]=i,i}return this.rules[e]||this.rules[this.languageUtils.getLanguagePartFromCode(e)]}needsPlural(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=this.getRule(e,t);return this.shouldUseIntlApi()?n&&n.resolvedOptions().pluralCategories.length>1:n&&n.numbers.length>1}getPluralFormsOfKey(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.getSuffixes(e,n).map((e=>"".concat(t).concat(e)))}getSuffixes(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=this.getRule(e,t);return n?this.shouldUseIntlApi()?n.resolvedOptions().pluralCategories.sort(((e,t)=>Mo[e]-Mo[t])).map((e=>"".concat(this.options.prepend).concat(t.ordinal?"ordinal".concat(this.options.prepend):"").concat(e))):n.numbers.map((n=>this.getSuffix(e,n,t))):[]}getSuffix(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=this.getRule(e,n);return r?this.shouldUseIntlApi()?"".concat(this.options.prepend).concat(n.ordinal?"ordinal".concat(this.options.prepend):"").concat(r.select(t)):this.getSuffixRetroCompatible(r,t):(this.logger.warn("no plural rule found for: ".concat(e)),"")}getSuffixRetroCompatible(e,t){const n=e.noAbs?e.plurals(t):e.plurals(Math.abs(t));let r=e.numbers[n];this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]&&(2===r?r="plural":1===r&&(r=""));const a=()=>this.options.prepend&&r.toString()?this.options.prepend+r.toString():r.toString();return"v1"===this.options.compatibilityJSON?1===r?"":"number"===typeof r?"_plural_".concat(r.toString()):a():"v2"===this.options.compatibilityJSON||this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]?a():this.options.prepend&&n.toString()?this.options.prepend+n.toString():n.toString()}shouldUseIntlApi(){return!Do.includes(this.options.compatibilityJSON)}}const Bo=function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",a=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],o=((e,t,n)=>{const r=mo(e,n);return void 0!==r?r:mo(t,n)})(e,t,n);return!o&&a&&io(n)&&(o=ko(e,n,r),void 0===o&&(o=ko(t,n,r))),o},Uo=e=>e.replace(/\$/g,"$$$$");class Ho{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=Co.create("interpolator"),this.options=e,this.format=e.interpolation&&e.interpolation.format||(e=>e),this.init(e)}init(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});const{escape:t,escapeValue:n,useRawValueToEscape:r,prefix:a,prefixEscaped:o,suffix:i,suffixEscaped:s,formatSeparator:l,unescapeSuffix:u,unescapePrefix:c,nestingPrefix:d,nestingPrefixEscaped:f,nestingSuffix:p,nestingSuffixEscaped:h,nestingOptionsSeparator:m,maxReplaces:g,alwaysFormat:y}=e.interpolation;this.escape=void 0!==t?t:bo,this.escapeValue=void 0===n||n,this.useRawValueToEscape=void 0!==r&&r,this.prefix=a?yo(a):o||"{{",this.suffix=i?yo(i):s||"}}",this.formatSeparator=l||",",this.unescapePrefix=u?"":c||"-",this.unescapeSuffix=this.unescapePrefix?"":u||"",this.nestingPrefix=d?yo(d):f||yo("$t("),this.nestingSuffix=p?yo(p):h||yo(")"),this.nestingOptionsSeparator=m||",",this.maxReplaces=g||1e3,this.alwaysFormat=void 0!==y&&y,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(e,t)=>e&&e.source===t?(e.lastIndex=0,e):new RegExp(t,"g");this.regexp=e(this.regexp,"".concat(this.prefix,"(.+?)").concat(this.suffix)),this.regexpUnescape=e(this.regexpUnescape,"".concat(this.prefix).concat(this.unescapePrefix,"(.+?)").concat(this.unescapeSuffix).concat(this.suffix)),this.nestingRegexp=e(this.nestingRegexp,"".concat(this.nestingPrefix,"(.+?)").concat(this.nestingSuffix))}interpolate(e,t,n,r){let a,o,i;const s=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},l=e=>{if(e.indexOf(this.formatSeparator)<0){const a=Bo(t,s,e,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(a,void 0,n,oo(oo(oo({},r),t),{},{interpolationkey:e})):a}const a=e.split(this.formatSeparator),o=a.shift().trim(),i=a.join(this.formatSeparator).trim();return this.format(Bo(t,s,o,this.options.keySeparator,this.options.ignoreJSONStructure),i,n,oo(oo(oo({},r),t),{},{interpolationkey:o}))};this.resetRegExp();const u=r&&r.missingInterpolationHandler||this.options.missingInterpolationHandler,c=r&&r.interpolation&&void 0!==r.interpolation.skipOnVariables?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:e=>Uo(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?Uo(this.escape(e)):Uo(e)}].forEach((t=>{for(i=0;a=t.regex.exec(e);){const n=a[1].trim();if(o=l(n),void 0===o)if("function"===typeof u){const t=u(e,a,r);o=io(t)?t:""}else if(r&&Object.prototype.hasOwnProperty.call(r,n))o="";else{if(c){o=a[0];continue}this.logger.warn("missed to pass in variable ".concat(n," for interpolating ").concat(e)),o=""}else io(o)||this.useRawValueToEscape||(o=lo(o));const s=t.safeValue(o);if(e=e.replace(a[0],s),c?(t.regex.lastIndex+=o.length,t.regex.lastIndex-=a[0].length):t.regex.lastIndex=0,i++,i>=this.maxReplaces)break}})),e}nest(e,t){let n,r,a,o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=(e,t)=>{const n=this.nestingOptionsSeparator;if(e.indexOf(n)<0)return e;const r=e.split(new RegExp("".concat(n,"[ ]*{")));let o="{".concat(r[1]);e=r[0],o=this.interpolate(o,a);const i=o.match(/'/g),s=o.match(/"/g);(i&&i.length%2===0&&!s||s.length%2!==0)&&(o=o.replace(/'/g,'"'));try{a=JSON.parse(o),t&&(a=oo(oo({},t),a))}catch(_i){return this.logger.warn("failed parsing options string in nesting for key ".concat(e),_i),"".concat(e).concat(n).concat(o)}return a.defaultValue&&a.defaultValue.indexOf(this.prefix)>-1&&delete a.defaultValue,e};for(;n=this.nestingRegexp.exec(e);){let s=[];a=oo({},o),a=a.replace&&!io(a.replace)?a.replace:a,a.applyPostProcessor=!1,delete a.defaultValue;let l=!1;if(-1!==n[0].indexOf(this.formatSeparator)&&!/{.*}/.test(n[1])){const e=n[1].split(this.formatSeparator).map((e=>e.trim()));n[1]=e.shift(),s=e,l=!0}if(r=t(i.call(this,n[1].trim(),a),a),r&&n[0]===e&&!io(r))return r;io(r)||(r=lo(r)),r||(this.logger.warn("missed to resolve ".concat(n[1]," for nesting ").concat(e)),r=""),l&&(r=s.reduce(((e,t)=>this.format(e,t,o.lng,oo(oo({},o),{},{interpolationkey:n[1].trim()}))),r.trim())),e=e.replace(n[0],r),this.regexp.lastIndex=0}return e}}const Vo=e=>{const t={};return(n,r,a)=>{let o=a;a&&a.interpolationkey&&a.formatParams&&a.formatParams[a.interpolationkey]&&a[a.interpolationkey]&&(o=oo(oo({},o),{},{[a.interpolationkey]:void 0}));const i=r+JSON.stringify(o);let s=t[i];return s||(s=e(So(r),a),t[i]=s),s(n)}};class Wo{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=Co.create("formatter"),this.options=e,this.formats={number:Vo(((e,t)=>{const n=new Intl.NumberFormat(e,oo({},t));return e=>n.format(e)})),currency:Vo(((e,t)=>{const n=new Intl.NumberFormat(e,oo(oo({},t),{},{style:"currency"}));return e=>n.format(e)})),datetime:Vo(((e,t)=>{const n=new Intl.DateTimeFormat(e,oo({},t));return e=>n.format(e)})),relativetime:Vo(((e,t)=>{const n=new Intl.RelativeTimeFormat(e,oo({},t));return e=>n.format(e,t.range||"day")})),list:Vo(((e,t)=>{const n=new Intl.ListFormat(e,oo({},t));return e=>n.format(e)}))},this.init(e)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};this.formatSeparator=t.interpolation.formatSeparator||","}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=Vo(t)}format(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const a=t.split(this.formatSeparator);if(a.length>1&&a[0].indexOf("(")>1&&a[0].indexOf(")")<0&&a.find((e=>e.indexOf(")")>-1))){const e=a.findIndex((e=>e.indexOf(")")>-1));a[0]=[a[0],...a.splice(1,e)].join(this.formatSeparator)}const o=a.reduce(((e,t)=>{const{formatName:a,formatOptions:o}=(e=>{let t=e.toLowerCase().trim();const n={};if(e.indexOf("(")>-1){const r=e.split("(");t=r[0].toLowerCase().trim();const a=r[1].substring(0,r[1].length-1);"currency"===t&&a.indexOf(":")<0?n.currency||(n.currency=a.trim()):"relativetime"===t&&a.indexOf(":")<0?n.range||(n.range=a.trim()):a.split(";").forEach((e=>{if(e){const[t,...r]=e.split(":"),a=r.join(":").trim().replace(/^'+|'+$/g,""),o=t.trim();n[o]||(n[o]=a),"false"===a&&(n[o]=!1),"true"===a&&(n[o]=!0),isNaN(a)||(n[o]=parseInt(a,10))}}))}return{formatName:t,formatOptions:n}})(t);if(this.formats[a]){let t=e;try{const i=r&&r.formatParams&&r.formatParams[r.interpolationkey]||{},s=i.locale||i.lng||r.locale||r.lng||n;t=this.formats[a](e,s,oo(oo(oo({},o),r),i))}catch(i){this.logger.warn(i)}return t}return this.logger.warn("there was no format function for ".concat(a)),e}),e);return o}}class $o extends jo{constructor(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=n,this.languageUtils=n.languageUtils,this.options=r,this.logger=Co.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=r.maxParallelReads||10,this.readingCalls=0,this.maxRetries=r.maxRetries>=0?r.maxRetries:5,this.retryTimeout=r.retryTimeout>=1?r.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(n,r.backend,r)}queueLoad(e,t,n,r){const a={},o={},i={},s={};return e.forEach((e=>{let r=!0;t.forEach((t=>{const i="".concat(e,"|").concat(t);!n.reload&&this.store.hasResourceBundle(e,t)?this.state[i]=2:this.state[i]<0||(1===this.state[i]?void 0===o[i]&&(o[i]=!0):(this.state[i]=1,r=!1,void 0===o[i]&&(o[i]=!0),void 0===a[i]&&(a[i]=!0),void 0===s[t]&&(s[t]=!0)))})),r||(i[e]=!0)})),(Object.keys(a).length||Object.keys(o).length)&&this.queue.push({pending:o,pendingCount:Object.keys(o).length,loaded:{},errors:[],callback:r}),{toLoad:Object.keys(a),pending:Object.keys(o),toLoadLanguages:Object.keys(i),toLoadNamespaces:Object.keys(s)}}loaded(e,t,n){const r=e.split("|"),a=r[0],o=r[1];t&&this.emit("failedLoading",a,o,t),!t&&n&&this.store.addResourceBundle(a,o,n,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&n&&(this.state[e]=0);const i={};this.queue.forEach((n=>{((e,t,n)=>{const{obj:r,k:a}=po(e,t,Object);r[a]=r[a]||[],r[a].push(n)})(n.loaded,[a],o),((e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)})(n,e),t&&n.errors.push(t),0!==n.pendingCount||n.done||(Object.keys(n.loaded).forEach((e=>{i[e]||(i[e]={});const t=n.loaded[e];t.length&&t.forEach((t=>{void 0===i[e][t]&&(i[e][t]=!0)}))})),n.done=!0,n.errors.length?n.callback(n.errors):n.callback())})),this.emit("loaded",i),this.queue=this.queue.filter((e=>!e.done))}read(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.retryTimeout,o=arguments.length>5?arguments[5]:void 0;if(!e.length)return o(null,{});if(this.readingCalls>=this.maxParallelReads)return void this.waitingReads.push({lng:e,ns:t,fcName:n,tried:r,wait:a,callback:o});this.readingCalls++;const i=(i,s)=>{if(this.readingCalls--,this.waitingReads.length>0){const e=this.waitingReads.shift();this.read(e.lng,e.ns,e.fcName,e.tried,e.wait,e.callback)}i&&s&&r<this.maxRetries?setTimeout((()=>{this.read.call(this,e,t,n,r+1,2*a,o)}),a):o(i,s)},s=this.backend[n].bind(this.backend);if(2!==s.length)return s(e,t,i);try{const n=s(e,t);n&&"function"===typeof n.then?n.then((e=>i(null,e))).catch(i):i(null,n)}catch(l){i(l)}}prepareLoading(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),r&&r();io(e)&&(e=this.languageUtils.toResolveHierarchy(e)),io(t)&&(t=[t]);const a=this.queueLoad(e,t,n,r);if(!a.toLoad.length)return a.pending.length||r(),null;a.toLoad.forEach((e=>{this.loadOne(e)}))}load(e,t,n){this.prepareLoading(e,t,{},n)}reload(e,t,n){this.prepareLoading(e,t,{reload:!0},n)}loadOne(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";const n=e.split("|"),r=n[0],a=n[1];this.read(r,a,"read",void 0,void 0,((n,o)=>{n&&this.logger.warn("".concat(t,"loading namespace ").concat(a," for language ").concat(r," failed"),n),!n&&o&&this.logger.log("".concat(t,"loaded namespace ").concat(a," for language ").concat(r),o),this.loaded(e,n,o)}))}saveMissing(e,t,n,r,a){let o=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},i=arguments.length>6&&void 0!==arguments[6]?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(t))this.logger.warn('did not save key "'.concat(n,'" as the namespace "').concat(t,'" was not yet loaded'),"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");else if(void 0!==n&&null!==n&&""!==n){if(this.backend&&this.backend.create){const l=oo(oo({},o),{},{isUpdate:a}),u=this.backend.create.bind(this.backend);if(u.length<6)try{let a;a=5===u.length?u(e,t,n,r,l):u(e,t,n,r),a&&"function"===typeof a.then?a.then((e=>i(null,e))).catch(i):i(null,a)}catch(s){i(s)}else u(e,t,n,r,i,l)}e&&e[0]&&this.store.addResource(e[0],t,n,r)}}}const Ko=()=>({debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if("object"===typeof e[1]&&(t=e[1]),io(e[1])&&(t.defaultValue=e[1]),io(e[2])&&(t.tDescription=e[2]),"object"===typeof e[2]||"object"===typeof e[3]){const n=e[3]||e[2];Object.keys(n).forEach((e=>{t[e]=n[e]}))}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),qo=e=>(io(e.ns)&&(e.ns=[e.ns]),io(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),io(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs&&e.supportedLngs.indexOf("cimode")<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),e),Qo=()=>{};class Go extends jo{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;var n;if(super(),this.options=qo(e),this.services={},this.logger=Co,this.modules={external:[]},n=this,Object.getOwnPropertyNames(Object.getPrototypeOf(n)).forEach((e=>{"function"===typeof n[e]&&(n[e]=n[e].bind(n))})),t&&!this.isInitialized&&!e.isClone){if(!this.options.initImmediate)return this.init(e,t),this;setTimeout((()=>{this.init(e,t)}),0)}}init(){var e=this;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,"function"===typeof t&&(n=t,t={}),!t.defaultNS&&!1!==t.defaultNS&&t.ns&&(io(t.ns)?t.defaultNS=t.ns:t.ns.indexOf("translation")<0&&(t.defaultNS=t.ns[0]));const r=Ko();this.options=oo(oo(oo({},r),this.options),qo(t)),"v1"!==this.options.compatibilityAPI&&(this.options.interpolation=oo(oo({},r.interpolation),this.options.interpolation)),void 0!==t.keySeparator&&(this.options.userDefinedKeySeparator=t.keySeparator),void 0!==t.nsSeparator&&(this.options.userDefinedNsSeparator=t.nsSeparator);const a=e=>e?"function"===typeof e?new e:e:null;if(!this.options.isClone){let t;this.modules.logger?Co.init(a(this.modules.logger),this.options):Co.init(null,this.options),this.modules.formatter?t=this.modules.formatter:"undefined"!==typeof Intl&&(t=Wo);const n=new Lo(this.options);this.store=new Oo(this.options.resources,this.options);const o=this.services;o.logger=Co,o.resourceStore=this.store,o.languageUtils=n,o.pluralResolver=new zo(n,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),!t||this.options.interpolation.format&&this.options.interpolation.format!==r.interpolation.format||(o.formatter=a(t),o.formatter.init(o,this.options),this.options.interpolation.format=o.formatter.format.bind(o.formatter)),o.interpolator=new Ho(this.options),o.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},o.backendConnector=new $o(a(this.modules.backend),o.resourceStore,o,this.options),o.backendConnector.on("*",(function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];e.emit(t,...r)})),this.modules.languageDetector&&(o.languageDetector=a(this.modules.languageDetector),o.languageDetector.init&&o.languageDetector.init(o,this.options.detection,this.options)),this.modules.i18nFormat&&(o.i18nFormat=a(this.modules.i18nFormat),o.i18nFormat.init&&o.i18nFormat.init(this)),this.translator=new To(this.services,this.options),this.translator.on("*",(function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];e.emit(t,...r)})),this.modules.external.forEach((e=>{e.init&&e.init(this)}))}if(this.format=this.options.interpolation.format,n||(n=Qo),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.length>0&&"dev"!==e[0]&&(this.options.lng=e[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined");["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach((t=>{this[t]=function(){return e.store[t](...arguments)}}));["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach((t=>{this[t]=function(){return e.store[t](...arguments),e}}));const o=so(),i=()=>{const e=(e,t)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),o.resolve(t),n(e,t)};if(this.languages&&"v1"!==this.options.compatibilityAPI&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initImmediate?i():setTimeout(i,0),o}loadResources(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Qo;const n=io(e)?e:this.language;if("function"===typeof e&&(t=e),!this.options.resources||this.options.partialBundledLanguages){if(n&&"cimode"===n.toLowerCase()&&(!this.options.preload||0===this.options.preload.length))return t();const e=[],r=t=>{if(!t)return;if("cimode"===t)return;this.services.languageUtils.toResolveHierarchy(t).forEach((t=>{"cimode"!==t&&e.indexOf(t)<0&&e.push(t)}))};if(n)r(n);else{this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach((e=>r(e)))}this.options.preload&&this.options.preload.forEach((e=>r(e))),this.services.backendConnector.load(e,this.options.ns,(e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),t(e)}))}else t(null)}reloadResources(e,t,n){const r=so();return"function"===typeof e&&(n=e,e=void 0),"function"===typeof t&&(n=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),n||(n=Qo),this.services.backendConnector.reload(e,t,(e=>{r.resolve(),n(e)})),r}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&Po.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}setResolvedLanguage(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1))for(let t=0;t<this.languages.length;t++){const e=this.languages[t];if(!(["cimode","dev"].indexOf(e)>-1)&&this.store.hasLanguageSomeTranslations(e)){this.resolvedLanguage=e;break}}}changeLanguage(e,t){var n=this;this.isLanguageChangingTo=e;const r=so();this.emit("languageChanging",e);const a=e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)},o=(e,o)=>{o?(a(o),this.translator.changeLanguage(o),this.isLanguageChangingTo=void 0,this.emit("languageChanged",o),this.logger.log("languageChanged",o)):this.isLanguageChangingTo=void 0,r.resolve((function(){return n.t(...arguments)})),t&&t(e,(function(){return n.t(...arguments)}))},i=t=>{e||t||!this.services.languageDetector||(t=[]);const n=io(t)?t:this.services.languageUtils.getBestMatchFromCodes(t);n&&(this.language||a(n),this.translator.language||this.translator.changeLanguage(n),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(n)),this.loadResources(n,(e=>{o(e,n)}))};return e||!this.services.languageDetector||this.services.languageDetector.async?!e&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(i):this.services.languageDetector.detect(i):i(e):i(this.services.languageDetector.detect()),r}getFixedT(e,t,n){var r=this;const a=function(e,t){let o;if("object"!==typeof t){for(var i=arguments.length,s=new Array(i>2?i-2:0),l=2;l<i;l++)s[l-2]=arguments[l];o=r.options.overloadTranslationOptionHandler([e,t].concat(s))}else o=oo({},t);o.lng=o.lng||a.lng,o.lngs=o.lngs||a.lngs,o.ns=o.ns||a.ns,""!==o.keyPrefix&&(o.keyPrefix=o.keyPrefix||n||a.keyPrefix);const u=r.options.keySeparator||".";let c;return c=o.keyPrefix&&Array.isArray(e)?e.map((e=>"".concat(o.keyPrefix).concat(u).concat(e))):o.keyPrefix?"".concat(o.keyPrefix).concat(u).concat(e):e,r.t(c,o)};return io(e)?a.lng=e:a.lngs=e,a.ns=t,a.keyPrefix=n,a}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const n=t.lng||this.resolvedLanguage||this.languages[0],r=!!this.options&&this.options.fallbackLng,a=this.languages[this.languages.length-1];if("cimode"===n.toLowerCase())return!0;const o=(e,t)=>{const n=this.services.backendConnector.state["".concat(e,"|").concat(t)];return-1===n||0===n||2===n};if(t.precheck){const e=t.precheck(this,o);if(void 0!==e)return e}return!!this.hasResourceBundle(n,e)||(!(this.services.backendConnector.backend&&(!this.options.resources||this.options.partialBundledLanguages))||!(!o(n,e)||r&&!o(a,e)))}loadNamespaces(e,t){const n=so();return this.options.ns?(io(e)&&(e=[e]),e.forEach((e=>{this.options.ns.indexOf(e)<0&&this.options.ns.push(e)})),this.loadResources((e=>{n.resolve(),t&&t(e)})),n):(t&&t(),Promise.resolve())}loadLanguages(e,t){const n=so();io(e)&&(e=[e]);const r=this.options.preload||[],a=e.filter((e=>r.indexOf(e)<0&&this.services.languageUtils.isSupportedCode(e)));return a.length?(this.options.preload=r.concat(a),this.loadResources((e=>{n.resolve(),t&&t(e)})),n):(t&&t(),Promise.resolve())}dir(e){if(e||(e=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),!e)return"rtl";const t=this.services&&this.services.languageUtils||new Lo(Ko());return["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf(t.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){return new Go(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},arguments.length>1?arguments[1]:void 0)}cloneInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Qo;const n=e.forkResourceStore;n&&delete e.forkResourceStore;const r=oo(oo(oo({},this.options),e),{isClone:!0}),a=new Go(r);void 0===e.debug&&void 0===e.prefix||(a.logger=a.logger.clone(e));return["store","services","language"].forEach((e=>{a[e]=this[e]})),a.services=oo({},this.services),a.services.utils={hasLoadedNamespace:a.hasLoadedNamespace.bind(a)},n&&(a.store=new Oo(this.store.data,r),a.services.resourceStore=a.store),a.translator=new To(a.services,r),a.translator.on("*",(function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];a.emit(e,...n)})),a.init(r,t),a.translator.options=r,a.translator.backendConnector.services.utils={hasLoadedNamespace:a.hasLoadedNamespace.bind(a)},a}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const Jo=Go.createInstance();Jo.createInstance=Go.createInstance;Jo.createInstance,Jo.dir,Jo.init,Jo.loadResources,Jo.reloadResources,Jo.use,Jo.changeLanguage,Jo.getFixedT,Jo.t,Jo.exists,Jo.setDefaultNamespace,Jo.hasLoadedNamespace,Jo.loadNamespaces,Jo.loadLanguages;n(844);Object.create(null);const Xo={};function Yo(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];"string"===typeof t[0]&&Xo[t[0]]||("string"===typeof t[0]&&(Xo[t[0]]=new Date),function(){if(console&&console.warn){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];"string"===typeof t[0]&&(t[0]="react-i18next:: ".concat(t[0])),console.warn(...t)}}(...t))}const Zo=(e,t)=>()=>{if(e.isInitialized)t();else{const n=()=>{setTimeout((()=>{e.off("initialized",n)}),0),t()};e.on("initialized",n)}};function ei(e,t,n){e.loadNamespaces(t,Zo(e,n))}function ti(e,t,n,r){"string"===typeof n&&(n=[n]),n.forEach((t=>{e.options.ns.indexOf(t)<0&&e.options.ns.push(t)})),e.loadLanguages(t,Zo(e,r))}const ni=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,ri={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"\xa9","&#169;":"\xa9","&reg;":"\xae","&#174;":"\xae","&hellip;":"\u2026","&#8230;":"\u2026","&#x2F;":"/","&#47;":"/"},ai=e=>ri[e];let oi={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(ni,ai)};let ii;const si={type:"3rdParty",init(e){!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};oi=oo(oo({},oi),e)}(e.options.react),function(e){ii=e}(e)}},li=(0,t.createContext)();class ui{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach((e=>{this.usedNamespaces[e]||(this.usedNamespaces[e]=!0)}))}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}}function ci(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{i18n:r}=n,{i18n:a,defaultNS:o}=(0,t.useContext)(li)||{},i=r||a||ii;if(i&&!i.reportNamespaces&&(i.reportNamespaces=new ui),!i){Yo("You will need to pass in an i18next instance by using initReactI18next");const e=(e,t)=>"string"===typeof t?t:t&&"object"===typeof t&&"string"===typeof t.defaultValue?t.defaultValue:Array.isArray(e)?e[e.length-1]:e,t=[e,{},!1];return t.t=e,t.i18n={},t.ready=!1,t}i.options.react&&void 0!==i.options.react.wait&&Yo("It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");const s=oo(oo(oo({},oi),i.options.react),n),{useSuspense:l,keyPrefix:u}=s;let c=e||o||i.options&&i.options.defaultNS;c="string"===typeof c?[c]:c||["translation"],i.reportNamespaces.addUsedNamespaces&&i.reportNamespaces.addUsedNamespaces(c);const d=(i.isInitialized||i.initializedStoreOnce)&&c.every((e=>function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return t.languages&&t.languages.length?void 0!==t.options.ignoreJSONStructure?t.hasLoadedNamespace(e,{lng:n.lng,precheck:(t,r)=>{if(n.bindI18n&&n.bindI18n.indexOf("languageChanging")>-1&&t.services.backendConnector.backend&&t.isLanguageChangingTo&&!r(t.isLanguageChangingTo,e))return!1}}):function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=t.languages[0],a=!!t.options&&t.options.fallbackLng,o=t.languages[t.languages.length-1];if("cimode"===r.toLowerCase())return!0;const i=(e,n)=>{const r=t.services.backendConnector.state["".concat(e,"|").concat(n)];return-1===r||2===r};return!(n.bindI18n&&n.bindI18n.indexOf("languageChanging")>-1&&t.services.backendConnector.backend&&t.isLanguageChangingTo&&!i(t.isLanguageChangingTo,e))&&(!!t.hasResourceBundle(r,e)||!(t.services.backendConnector.backend&&(!t.options.resources||t.options.partialBundledLanguages))||!(!i(r,e)||a&&!i(o,e)))}(e,t,n):(Yo("i18n.languages were undefined or empty",t.languages),!0)}(e,i,s)));function f(){return i.getFixedT(n.lng||null,"fallback"===s.nsMode?c:c[0],u)}const[p,h]=(0,t.useState)(f);let m=c.join();n.lng&&(m="".concat(n.lng).concat(m));const g=((e,n)=>{const r=(0,t.useRef)();return(0,t.useEffect)((()=>{r.current=n?r.current:e}),[e,n]),r.current})(m),y=(0,t.useRef)(!0);(0,t.useEffect)((()=>{const{bindI18n:e,bindI18nStore:t}=s;function r(){y.current&&h(f)}return y.current=!0,d||l||(n.lng?ti(i,n.lng,c,(()=>{y.current&&h(f)})):ei(i,c,(()=>{y.current&&h(f)}))),d&&g&&g!==m&&y.current&&h(f),e&&i&&i.on(e,r),t&&i&&i.store.on(t,r),()=>{y.current=!1,e&&i&&e.split(" ").forEach((e=>i.off(e,r))),t&&i&&t.split(" ").forEach((e=>i.store.off(e,r)))}}),[i,m]);const v=(0,t.useRef)(!0);(0,t.useEffect)((()=>{y.current&&!v.current&&h(f),v.current=!1}),[i,u]);const b=[p,i,d];if(b.t=p,b.i18n=i,b.ready=d,d)return b;if(!d&&!l)return b;throw new Promise((e=>{n.lng?ti(i,n.lng,c,(()=>e())):ei(i,c,(()=>e()))}))}function di(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,no(r.key),r)}}var fi=[],pi=fi.forEach,hi=fi.slice;var mi=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,gi=function(e,t,n,r){var a=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{path:"/",sameSite:"strict"};n&&(a.expires=new Date,a.expires.setTime(a.expires.getTime()+60*n*1e3)),r&&(a.domain=r),document.cookie=function(e,t,n){var r=n||{};r.path=r.path||"/";var a=encodeURIComponent(t),o="".concat(e,"=").concat(a);if(r.maxAge>0){var i=r.maxAge-0;if(Number.isNaN(i))throw new Error("maxAge should be a Number");o+="; Max-Age=".concat(Math.floor(i))}if(r.domain){if(!mi.test(r.domain))throw new TypeError("option domain is invalid");o+="; Domain=".concat(r.domain)}if(r.path){if(!mi.test(r.path))throw new TypeError("option path is invalid");o+="; Path=".concat(r.path)}if(r.expires){if("function"!==typeof r.expires.toUTCString)throw new TypeError("option expires is invalid");o+="; Expires=".concat(r.expires.toUTCString())}if(r.httpOnly&&(o+="; HttpOnly"),r.secure&&(o+="; Secure"),r.sameSite)switch("string"===typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:o+="; SameSite=Strict";break;case"lax":o+="; SameSite=Lax";break;case"strict":o+="; SameSite=Strict";break;case"none":o+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return o}(e,encodeURIComponent(t),a)},yi=function(e){for(var t="".concat(e,"="),n=document.cookie.split(";"),r=0;r<n.length;r++){for(var a=n[r];" "===a.charAt(0);)a=a.substring(1,a.length);if(0===a.indexOf(t))return a.substring(t.length,a.length)}return null},vi={name:"cookie",lookup:function(e){var t;if(e.lookupCookie&&"undefined"!==typeof document){var n=yi(e.lookupCookie);n&&(t=n)}return t},cacheUserLanguage:function(e,t){t.lookupCookie&&"undefined"!==typeof document&&gi(t.lookupCookie,e,t.cookieMinutes,t.cookieDomain,t.cookieOptions)}},bi={name:"querystring",lookup:function(e){var t;if("undefined"!==typeof window){var n=window.location.search;!window.location.search&&window.location.hash&&window.location.hash.indexOf("?")>-1&&(n=window.location.hash.substring(window.location.hash.indexOf("?")));for(var r=n.substring(1).split("&"),a=0;a<r.length;a++){var o=r[a].indexOf("=");if(o>0)r[a].substring(0,o)===e.lookupQuerystring&&(t=r[a].substring(o+1))}}return t}},wi=null,xi=function(){if(null!==wi)return wi;try{wi="undefined"!==window&&null!==window.localStorage;var e="i18next.translate.boo";window.localStorage.setItem(e,"foo"),window.localStorage.removeItem(e)}catch(_i){wi=!1}return wi},ki={name:"localStorage",lookup:function(e){var t;if(e.lookupLocalStorage&&xi()){var n=window.localStorage.getItem(e.lookupLocalStorage);n&&(t=n)}return t},cacheUserLanguage:function(e,t){t.lookupLocalStorage&&xi()&&window.localStorage.setItem(t.lookupLocalStorage,e)}},Si=null,Ei=function(){if(null!==Si)return Si;try{Si="undefined"!==window&&null!==window.sessionStorage;var e="i18next.translate.boo";window.sessionStorage.setItem(e,"foo"),window.sessionStorage.removeItem(e)}catch(_i){Si=!1}return Si},Ni={name:"sessionStorage",lookup:function(e){var t;if(e.lookupSessionStorage&&Ei()){var n=window.sessionStorage.getItem(e.lookupSessionStorage);n&&(t=n)}return t},cacheUserLanguage:function(e,t){t.lookupSessionStorage&&Ei()&&window.sessionStorage.setItem(t.lookupSessionStorage,e)}},Ci={name:"navigator",lookup:function(e){var t=[];if("undefined"!==typeof navigator){if(navigator.languages)for(var n=0;n<navigator.languages.length;n++)t.push(navigator.languages[n]);navigator.userLanguage&&t.push(navigator.userLanguage),navigator.language&&t.push(navigator.language)}return t.length>0?t:void 0}},ji={name:"htmlTag",lookup:function(e){var t,n=e.htmlTag||("undefined"!==typeof document?document.documentElement:null);return n&&"function"===typeof n.getAttribute&&(t=n.getAttribute("lang")),t}},Oi={name:"path",lookup:function(e){var t;if("undefined"!==typeof window){var n=window.location.pathname.match(/\/([a-zA-Z-]*)/g);if(n instanceof Array)if("number"===typeof e.lookupFromPathIndex){if("string"!==typeof n[e.lookupFromPathIndex])return;t=n[e.lookupFromPathIndex].replace("/","")}else t=n[0].replace("/","")}return t}},Pi={name:"subdomain",lookup:function(e){var t="number"===typeof e.lookupFromSubdomainIndex?e.lookupFromSubdomainIndex+1:1,n="undefined"!==typeof window&&window.location&&window.location.hostname&&window.location.hostname.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i);if(n)return n[t]}},Ri=!1;try{document.cookie,Ri=!0}catch(_i){}var Ti=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];Ri||Ti.splice(1,1);var Ai=function(){return function(e,t,n){return t&&di(e.prototype,t),n&&di(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}((function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.type="languageDetector",this.detectors={},this.init(t,n)}),[{key:"init",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.services=e||{languageUtils:{}},this.options=function(e){return pi.call(hi.call(arguments,1),(function(t){if(t)for(var n in t)void 0===e[n]&&(e[n]=t[n])})),e}(t,this.options||{},{order:Ti,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:function(e){return e}}),"string"===typeof this.options.convertDetectedLanguage&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=function(e){return e.replace("-","_")}),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=n,this.addDetector(vi),this.addDetector(bi),this.addDetector(ki),this.addDetector(Ni),this.addDetector(Ci),this.addDetector(ji),this.addDetector(Oi),this.addDetector(Pi)}},{key:"addDetector",value:function(e){return this.detectors[e.name]=e,this}},{key:"detect",value:function(e){var t=this;e||(e=this.options.order);var n=[];return e.forEach((function(e){if(t.detectors[e]){var r=t.detectors[e].lookup(t.options);r&&"string"===typeof r&&(r=[r]),r&&(n=n.concat(r))}})),n=n.map((function(e){return t.options.convertDetectedLanguage(e)})),this.services.languageUtils.getBestMatchFromCodes?n:n.length>0?n[0]:null}},{key:"cacheUserLanguage",value:function(e,t){var n=this;t||(t=this.options.caches),t&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(e)>-1||t.forEach((function(t){n.detectors[t]&&n.detectors[t].cacheUserLanguage(e,n.options)})))}}])}();Ai.type="languageDetector";const Li={en:{translation:JSON.parse('{"common":{"loading":"Loading...","save":"Save","cancel":"Cancel","delete":"Delete","edit":"Edit","add":"Add","search":"Search","filter":"Filter","export":"Export","import":"Import","refresh":"Refresh","back":"Back","next":"Next","previous":"Previous","submit":"Submit","reset":"Reset","clear":"Clear","close":"Close","open":"Open","view":"View","download":"Download","upload":"Upload","print":"Print","share":"Share","copy":"Copy","cut":"Cut","paste":"Paste","select":"Select","selectAll":"Select All","none":"None","all":"All","yes":"Yes","no":"No","ok":"OK","confirm":"Confirm","warning":"Warning","error":"Error","success":"Success","info":"Information","required":"Required","optional":"Optional","name":"Name","description":"Description","date":"Date","time":"Time","status":"Status","type":"Type","category":"Category","notes":"Notes","actions":"Actions"},"auth":{"login":"Login","logout":"Logout","register":"Register","username":"Username","email":"Email","password":"Password","confirmPassword":"Confirm Password","firstName":"First Name","lastName":"Last Name","phoneNumber":"Phone Number","department":"Department","role":"Role","forgotPassword":"Forgot Password?","resetPassword":"Reset Password","changePassword":"Change Password","currentPassword":"Current Password","newPassword":"New Password","loginSuccess":"Login successful","loginError":"Login failed","logoutSuccess":"Logout successful","registerSuccess":"Registration successful","registerError":"Registration failed","passwordChangeSuccess":"Password changed successfully","passwordChangeError":"Password change failed","invalidCredentials":"Invalid username or password","accountDeactivated":"Account is deactivated","sessionExpired":"Session expired. Please login again.","welcomeBack":"Welcome back","signInToContinue":"Sign in to continue to AMPD Livestock Management","dontHaveAccount":"Don\'t have an account?","alreadyHaveAccount":"Already have an account?","createAccount":"Create Account","signIn":"Sign In"},"navigation":{"dashboard":"Dashboard","animals":"Animals","breeding":"Breeding","health":"Health","feeding":"Feeding","financial":"Financial","compliance":"Compliance","inventory":"Inventory","analytics":"Analytics","reports":"Reports","settings":"Settings","profile":"Profile","users":"Users","help":"Help","support":"Support"},"dashboard":{"title":"Dashboard","welcome":"Welcome to AMPD Livestock Management","overview":"Overview","quickStats":"Quick Statistics","recentActivity":"Recent Activity","alerts":"Alerts","upcomingTasks":"Upcoming Tasks","totalAnimals":"Total Animals","healthyAnimals":"Healthy Animals","pregnantAnimals":"Pregnant Animals","upcomingBirths":"Upcoming Births","vaccinationsDue":"Vaccinations Due","healthAlerts":"Health Alerts","feedingAlerts":"Feeding Alerts","breedingAlerts":"Breeding Alerts","animalsBySpecies":"Animals by Species","healthStatusOverview":"Health Status Overview","breedingStatus":"Breeding Status","monthlyGrowth":"Monthly Growth","financialSummary":"Financial Summary"},"animals":{"title":"Animal Management","addAnimal":"Add Animal","editAnimal":"Edit Animal","animalDetails":"Animal Details","animalList":"Animal List","tagNumber":"Tag Number","species":"Species","breed":"Breed","gender":"Gender","dateOfBirth":"Date of Birth","age":"Age","weight":"Weight","color":"Color","markings":"Markings","location":"Location","healthStatus":"Health Status","breedingStatus":"Breeding Status","productionType":"Production Type","sire":"Sire","dam":"Dam","rfidTag":"RFID Tag","earTag":"Ear Tag","microchip":"Microchip ID","purchasePrice":"Purchase Price","purchaseDate":"Purchase Date","currentValue":"Current Value","weightHistory":"Weight History","productionRecords":"Production Records","images":"Images","documents":"Documents","male":"Male","female":"Female","cattle":"Cattle","sheep":"Sheep","goat":"Goat","pig":"Pig","chicken":"Chicken","horse":"Horse","other":"Other","active":"Active","sold":"Sold","deceased":"Deceased","transferred":"Transferred","quarantine":"Quarantine","healthy":"Healthy","sick":"Sick","injured":"Injured","recovering":"Recovering","available":"Available","pregnant":"Pregnant","lactating":"Lactating","breeding":"Breeding","retired":"Retired"},"health":{"title":"Health Management","healthRecords":"Health Records","addHealthRecord":"Add Health Record","editHealthRecord":"Edit Health Record","healthAlerts":"Health Alerts","vaccinationSchedule":"Vaccination Schedule","recordType":"Record Type","veterinarian":"Veterinarian","diagnosis":"Diagnosis","treatment":"Treatment","medication":"Medication","dosage":"Dosage","frequency":"Frequency","duration":"Duration","symptoms":"Symptoms","temperature":"Temperature","heartRate":"Heart Rate","respiratoryRate":"Respiratory Rate","bodyConditionScore":"Body Condition Score","followUpRequired":"Follow-up Required","followUpDate":"Follow-up Date","withdrawalPeriod":"Withdrawal Period","cost":"Cost","vaccination":"Vaccination","checkup":"Checkup","illness":"Illness","injury":"Injury","surgery":"Surgery","test":"Test","vaccine":"Vaccine","manufacturer":"Manufacturer","batchNumber":"Batch Number","expirationDate":"Expiration Date","route":"Route","intramuscular":"Intramuscular","subcutaneous":"Subcutaneous","oral":"Oral","nasal":"Nasal","intravenous":"Intravenous"},"breeding":{"title":"Breeding Management","breedingRecords":"Breeding Records","birthRecords":"Birth Records","heatDetection":"Heat Detection","addBreedingRecord":"Add Breeding Record","editBreedingRecord":"Edit Breeding Record","addBirthRecord":"Add Birth Record","editBirthRecord":"Edit Birth Record","breedingDate":"Breeding Date","breedingMethod":"Breeding Method","expectedDueDate":"Expected Due Date","actualBirthDate":"Actual Birth Date","pregnancyConfirmed":"Pregnancy Confirmed","pregnancyConfirmationDate":"Pregnancy Confirmation Date","pregnancyConfirmationMethod":"Pregnancy Confirmation Method","gestationPeriod":"Gestation Period","birthWeight":"Birth Weight","birthType":"Birth Type","complications":"Complications","offspring":"Offspring","placentaExpelled":"Placenta Expelled","postBirthTreatment":"Post-Birth Treatment","natural":"Natural","artificialInsemination":"Artificial Insemination","embryoTransfer":"Embryo Transfer","assisted":"Assisted","cesarean":"Cesarean","ultrasound":"Ultrasound","bloodTest":"Blood Test","physicalExam":"Physical Exam","planned":"Planned","completed":"Completed","failed":"Failed","aborted":"Aborted","heatDate":"Heat Date","heatIntensity":"Heat Intensity","heatDuration":"Heat Duration","behaviorSigns":"Behavior Signs","physicalSigns":"Physical Signs","breedingRecommended":"Breeding Recommended","breedingWindow":"Breeding Window","bred":"Bred","weak":"Weak","moderate":"Moderate","strong":"Strong"},"feeding":{"title":"Feed Management","feedingRecords":"Feeding Records","feedInventory":"Feed Inventory","feedingPlans":"Feeding Plans","addFeedingRecord":"Add Feeding Record","editFeedingRecord":"Edit Feeding Record","feedType":"Feed Type","quantity":"Quantity","unit":"Unit","feedingTime":"Feeding Time","feedCost":"Feed Cost","nutritionAnalysis":"Nutrition Analysis","protein":"Protein","carbohydrates":"Carbohydrates","fat":"Fat","fiber":"Fiber","vitamins":"Vitamins","minerals":"Minerals","calories":"Calories","stockLevel":"Stock Level","reorderLevel":"Reorder Level","supplier":"Supplier","orderDate":"Order Date","deliveryDate":"Delivery Date","kg":"kg","lbs":"lbs","tons":"tons","bags":"bags","liters":"liters","gallons":"gallons"},"financial":{"title":"Financial Management","transactions":"Transactions","income":"Income","expenses":"Expenses","budget":"Budget","reports":"Reports","addTransaction":"Add Transaction","editTransaction":"Edit Transaction","transactionType":"Transaction Type","amount":"Amount","transactionDate":"Transaction Date","paymentMethod":"Payment Method","reference":"Reference","vendor":"Vendor","customer":"Customer","invoice":"Invoice","receipt":"Receipt","profit":"Profit","loss":"Loss","revenue":"Revenue","costs":"Costs","roi":"Return on Investment","cash":"Cash","check":"Check","creditCard":"Credit Card","bankTransfer":"Bank Transfer","animalSale":"Animal Sale","productSale":"Product Sale","feedPurchase":"Feed Purchase","veterinaryExpense":"Veterinary Expense","equipmentPurchase":"Equipment Purchase","laborCost":"Labor Cost","utilities":"Utilities","insurance":"Insurance","taxes":"Taxes","other":"Other"},"reports":{"title":"Reports & Analytics","generateReport":"Generate Report","reportType":"Report Type","dateRange":"Date Range","animalReport":"Animal Report","healthReport":"Health Report","breedingReport":"Breeding Report","financialReport":"Financial Report","productionReport":"Production Report","inventoryReport":"Inventory Report","customReport":"Custom Report","exportToPDF":"Export to PDF","exportToExcel":"Export to Excel","exportToCSV":"Export to CSV","printReport":"Print Report","scheduleReport":"Schedule Report","reportScheduled":"Report Scheduled","daily":"Daily","weekly":"Weekly","monthly":"Monthly","quarterly":"Quarterly","yearly":"Yearly","custom":"Custom"},"settings":{"title":"Settings","generalSettings":"General Settings","userSettings":"User Settings","systemSettings":"System Settings","language":"Language","theme":"Theme","notifications":"Notifications","privacy":"Privacy","security":"Security","backup":"Backup","restore":"Restore","lightTheme":"Light Theme","darkTheme":"Dark Theme","emailNotifications":"Email Notifications","pushNotifications":"Push Notifications","smsNotifications":"SMS Notifications","twoFactorAuth":"Two-Factor Authentication","passwordPolicy":"Password Policy","sessionTimeout":"Session Timeout","dataRetention":"Data Retention","auditLog":"Audit Log"},"languages":{"en":"English","af":"Afrikaans","st":"Sesotho","tn":"Setswana","zu":"isiZulu"},"roles":{"admin":"Administrator","manager":"Manager","staff":"Staff","veterinarian":"Veterinarian","viewer":"Viewer"},"validation":{"required":"This field is required","email":"Please enter a valid email address","minLength":"Minimum length is {{min}} characters","maxLength":"Maximum length is {{max}} characters","numeric":"Please enter a valid number","positive":"Please enter a positive number","date":"Please enter a valid date","phone":"Please enter a valid phone number","passwordMatch":"Passwords do not match","passwordStrength":"Password must contain at least one uppercase letter, one lowercase letter, and one number"},"messages":{"saveSuccess":"Data saved successfully","saveError":"Failed to save data","deleteSuccess":"Data deleted successfully","deleteError":"Failed to delete data","updateSuccess":"Data updated successfully","updateError":"Failed to update data","loadError":"Failed to load data","networkError":"Network error. Please check your connection.","serverError":"Server error. Please try again later.","confirmDelete":"Are you sure you want to delete this item?","unsavedChanges":"You have unsaved changes. Are you sure you want to leave?","noData":"No data available","noResults":"No results found","searchPlaceholder":"Search...","selectOption":"Select an option","uploadSuccess":"File uploaded successfully","uploadError":"Failed to upload file","fileSizeError":"File size exceeds the maximum limit","fileTypeError":"Invalid file type"}}')},af:{translation:JSON.parse('{"common":{"loading":"Laai...","save":"Stoor","cancel":"Kanselleer","delete":"Verwyder","edit":"Wysig","add":"Voeg by","search":"Soek","filter":"Filter","export":"Uitvoer","import":"Invoer","refresh":"Verfris","back":"Terug","next":"Volgende","previous":"Vorige","submit":"Dien in","reset":"Herstel","clear":"Maak skoon","close":"Sluit","open":"Maak oop","view":"Bekyk","download":"Laai af","upload":"Laai op","print":"Druk","share":"Deel","copy":"Kopieer","cut":"Knip","paste":"Plak","select":"Kies","selectAll":"Kies alles","none":"Geen","all":"Alles","yes":"Ja","no":"Nee","ok":"OK","confirm":"Bevestig","warning":"Waarskuwing","error":"Fout","success":"Sukses","info":"Inligting","required":"Vereiste","optional":"Opsioneel","name":"Naam","description":"Beskrywing","date":"Datum","time":"Tyd","status":"Status","type":"Tipe","category":"Kategorie","notes":"Notas","actions":"Aksies"},"auth":{"login":"Meld aan","logout":"Meld af","register":"Registreer","username":"Gebruikersnaam","email":"E-pos","password":"Wagwoord","confirmPassword":"Bevestig wagwoord","firstName":"Voornaam","lastName":"Van","phoneNumber":"Telefoonnommer","department":"Departement","role":"Rol","forgotPassword":"Wagwoord vergeet?","resetPassword":"Herstel wagwoord","changePassword":"Verander wagwoord","currentPassword":"Huidige wagwoord","newPassword":"Nuwe wagwoord","loginSuccess":"Aanmelding suksesvol","loginError":"Aanmelding gefaal","logoutSuccess":"Afmelding suksesvol","registerSuccess":"Registrasie suksesvol","registerError":"Registrasie gefaal","passwordChangeSuccess":"Wagwoord suksesvol verander","passwordChangeError":"Wagwoord verandering gefaal","invalidCredentials":"Ongeldige gebruikersnaam of wagwoord","accountDeactivated":"Rekening is gedeaktiveer","sessionExpired":"Sessie verval. Meld asseblief weer aan.","welcomeBack":"Welkom terug","signInToContinue":"Meld aan om voort te gaan na AMPD Veestok Bestuur","dontHaveAccount":"Het jy nie \'n rekening nie?","alreadyHaveAccount":"Het jy reeds \'n rekening?","createAccount":"Skep rekening","signIn":"Meld aan"},"navigation":{"dashboard":"Paneelbord","animals":"Diere","breeding":"Teling","health":"Gesondheid","feeding":"Voeding","financial":"Finansieel","compliance":"Nakoming","inventory":"Voorraad","analytics":"Analise","reports":"Verslae","settings":"Instellings","profile":"Profiel","users":"Gebruikers","help":"Hulp","support":"Ondersteuning"},"dashboard":{"title":"Paneelbord","welcome":"Welkom by AMPD Veestok Bestuur","overview":"Oorsig","quickStats":"Vinnige Statistieke","recentActivity":"Onlangse Aktiwiteit","alerts":"Waarskuwings","upcomingTasks":"Komende Take","totalAnimals":"Totale Diere","healthyAnimals":"Gesonde Diere","pregnantAnimals":"Dragtige Diere","upcomingBirths":"Komende Geboortes","vaccinationsDue":"Inentings Verskuldig","healthAlerts":"Gesondheid Waarskuwings","feedingAlerts":"Voeding Waarskuwings","breedingAlerts":"Teling Waarskuwings"},"languages":{"en":"Engels","af":"Afrikaans","st":"Sesotho","tn":"Setswana","zu":"isiZulu"},"roles":{"admin":"Administrateur","manager":"Bestuurder","staff":"Personeel","veterinarian":"Veearts","viewer":"Kyker"}}')},st:{translation:JSON.parse('{"common":{"loading":"E nka nako...","save":"Boloka","cancel":"Hlakola","delete":"Hlakola","edit":"Fetola","add":"Eketsa","search":"Batla","filter":"Kgetha","export":"Ntsha","import":"Tsenya","refresh":"Nchafatsa","back":"Moraho","next":"Latelang","previous":"Pele","submit":"Romela","reset":"Qala hape","clear":"Hlakola","close":"Koala","open":"Bula","view":"Sheba","download":"Jarolla","upload":"Tsenya","print":"Hatisa","share":"Arolelana","copy":"Kopisa","cut":"Kuta","paste":"Kgomaretsa","select":"Kgetha","selectAll":"Kgetha tsohle","none":"Letho","all":"Tsohle","yes":"Ee","no":"Tjhe","ok":"Ho lokile","confirm":"Netefatsa","warning":"Temoso","error":"Phoso","success":"Katleho","info":"Tlhahisoleseding","required":"Ya hlokahalang","optional":"Ya ikgethang","name":"Lebitso","description":"Tlhaloso","date":"Letsatsi","time":"Nako","status":"Boemo","type":"Mofuta","category":"Sehlopha","notes":"Dintlha","actions":"Liketso"},"auth":{"login":"Kena","logout":"Tswa","register":"Ngodisa","username":"Lebitso la mosebedisi","email":"Imeile","password":"Phasewete","confirmPassword":"Netefatsa phasewete","firstName":"Lebitso la pele","lastName":"Lebitso la ho qetela","phoneNumber":"Nomoro ya mohala","department":"Lefapha","role":"Karolo","forgotPassword":"O lebetse phasewete?","resetPassword":"Beha phasewete hape","changePassword":"Fetola phasewete","currentPassword":"Phasewete ya hajwale","newPassword":"Phasewete e ncha","loginSuccess":"Ho kena ho atlehile","loginError":"Ho kena ho hloleha","logoutSuccess":"Ho tswa ho atlehile","registerSuccess":"Ho ngodisa ho atlehile","registerError":"Ho ngodisa ho hloleha","passwordChangeSuccess":"Phasewete e fetohile ka katleho","passwordChangeError":"Ho fetola phasewete ho hloleha","invalidCredentials":"Lebitso la mosebedisi kapa phasewete e fosahetseng","accountDeactivated":"Akhaonto e thibetswe","sessionExpired":"Sebaka se felile. Ka kopo kena hape.","welcomeBack":"Rea u amohela hape","signInToContinue":"Kena ho tswela pele ho AMPD Livestock Management","dontHaveAccount":"Ha u na akhaonto?","alreadyHaveAccount":"U se u na le akhaonto?","createAccount":"Theha akhaonto","signIn":"Kena"},"navigation":{"dashboard":"Boto ya taolo","animals":"Diphoofolo","breeding":"Pelehi","health":"Bophelo bo botle","feeding":"Ho fepa","financial":"Ditjhelete","compliance":"Ho latela melao","inventory":"Thepa","analytics":"Manollo","reports":"Dipego","settings":"Ditokiso","profile":"Profaele","users":"Basebedisi","help":"Thuso","support":"Tshehets\u043e"},"dashboard":{"title":"Boto ya taolo","welcome":"Rea u amohela ho AMPD Livestock Management","overview":"Kakaretso","quickStats":"Dipalopalo tse potlakang","recentActivity":"Mesebetsi ya morao-rao","alerts":"Ditemoso","upcomingTasks":"Mesebetsi e tlang","totalAnimals":"Diphoofolo kaofela","healthyAnimals":"Diphoofolo tse phetseng hantle","pregnantAnimals":"Diphoofolo tse imileng","upcomingBirths":"Ditswalo tse tlang","vaccinationsDue":"Dihlahlamiso tse hlokahalang","healthAlerts":"Ditemoso tsa bophelo bo botle","feedingAlerts":"Ditemoso tsa ho fepa","breedingAlerts":"Ditemoso tsa pelehi"},"languages":{"en":"Senyesemane","af":"Seafrikanse","st":"Sesotho","tn":"Setswana","zu":"isiZulu"},"roles":{"admin":"Motsamaisi","manager":"Molaodi","staff":"Basebetsi","veterinarian":"Ngaka ya diphoofolo","viewer":"Motshebetsi"}}')},tn:{translation:JSON.parse('{"common":{"loading":"E a loda...","save":"Boloka","cancel":"Khansela","delete":"Phimola","edit":"Baakanya","add":"Tsenya","search":"Batla","filter":"Kgetho","export":"Ntsha","import":"Tsenya","refresh":"Nt\u0161hafatsa","back":"Morago","next":"Latelang","previous":"Pele","submit":"Romela","reset":"Simolola","clear":"Phimola","close":"Tswala","open":"Bula","view":"Bona","download":"Kopolola","upload":"Tsenya","print":"Gatisa","share":"Abelana","copy":"Kopolola","cut":"Ripa","paste":"Kgomaretsa","select":"Tlhopha","selectAll":"Tlhopha tsotlhe","none":"Sepe","all":"Tsotlhe","yes":"Ee","no":"Nnyaa","ok":"Go siame","confirm":"Netefatsa","warning":"Temoso","error":"Phoso","success":"Katlego","info":"Tshedimosetso","required":"E a tlhokega","optional":"Ga e a tlhokega","name":"Leina","description":"Tlhaloso","date":"Letlha","time":"Nako","status":"Maemo","type":"Mofuta","category":"Setlhopha","notes":"Dintlha","actions":"Ditiro"},"auth":{"login":"Tsena","logout":"Tswa","register":"Kwala","username":"Leina la modirisi","email":"Imeile","password":"Phasewete","confirmPassword":"Netefatsa phasewete","firstName":"Leina la ntlha","lastName":"Leina la bofelo","phoneNumber":"Nomoro ya mogala","department":"Lefapha","role":"Seabe","forgotPassword":"O lebetse phasewete?","resetPassword":"Beha phasewete gape","changePassword":"Fetola phasewete","currentPassword":"Phasewete ya gone jaanong","newPassword":"Phasewete e nt\u0161ha","loginSuccess":"Go tsena go atlehile","loginError":"Go tsena ga palelwa","logoutSuccess":"Go tswa go atlehile","registerSuccess":"Go kwala go atlehile","registerError":"Go kwala ga palelwa","passwordChangeSuccess":"Phasewete e fetogile ka katlego","passwordChangeError":"Go fetola phasewete ga palelwa","invalidCredentials":"Leina la modirisi kgotsa phasewete e sa siamang","accountDeactivated":"Akhaonto e thibetswe","sessionExpired":"Sebaka se fedile. Ka kopo tsena gape.","welcomeBack":"Re go amogela gape","signInToContinue":"Tsena go tswelela go AMPD Livestock Management","dontHaveAccount":"Ga o na akhaonto?","alreadyHaveAccount":"O sena o na le akhaonto?","createAccount":"Dira akhaonto","signIn":"Tsena"},"navigation":{"dashboard":"Boto ya taolo","animals":"Diphologolo","breeding":"Pelehi","health":"Boitekanelo","feeding":"Go fepa","financial":"Dit\u0161helete","compliance":"Go latela melao","inventory":"Thepa","analytics":"Tshekatsheko","reports":"Dipego","settings":"Ditokiso","profile":"Profaele","users":"Badirisi","help":"Thuso","support":"Tshegetso"},"dashboard":{"title":"Boto ya taolo","welcome":"Re go amogela go AMPD Livestock Management","overview":"Kakaretso","quickStats":"Dipalopalo tse di bonolo","recentActivity":"Ditiro tsa bosheng","alerts":"Ditemoso","upcomingTasks":"Ditiro tse di tlang","totalAnimals":"Diphologolo tsotlhe","healthyAnimals":"Diphologolo tse di itekanetseng","pregnantAnimals":"Diphologolo tse di imileng","upcomingBirths":"Ditswalo tse di tlang","vaccinationsDue":"Dihlahlamiso tse di tlhokegang","healthAlerts":"Ditemoso tsa boitekanelo","feedingAlerts":"Ditemoso tsa go fepa","breedingAlerts":"Ditemoso tsa pelehi"},"languages":{"en":"Sekgowa","af":"Seafrikanse","st":"Sesotho","tn":"Setswana","zu":"isiZulu"},"roles":{"admin":"Motsamaisi","manager":"Molaodi","staff":"Badiri","veterinarian":"Ngaka ya diphologolo","viewer":"Mmoni"}}')},zu:{translation:JSON.parse('{"common":{"loading":"Iyalayisha...","save":"Londoloza","cancel":"Khansela","delete":"Susa","edit":"Hlela","add":"Engeza","search":"Sesha","filter":"Hlola","export":"Khipha","import":"Ngenisa","refresh":"Vuselela","back":"Emuva","next":"Okulandelayo","previous":"Okwangaphambili","submit":"Thumela","reset":"Setha kabusha","clear":"Sula","close":"Vala","open":"Vula","view":"Buka","download":"Dawuniloda","upload":"Layisha","print":"Phrinta","share":"Yabelana","copy":"Kopisha","cut":"Sika","paste":"Namathisela","select":"Khetha","selectAll":"Khetha konke","none":"Lutho","all":"Konke","yes":"Yebo","no":"Cha","ok":"Kulungile","confirm":"Qinisekisa","warning":"Isexwayiso","error":"Iphutha","success":"Impumelelo","info":"Ulwazi","required":"Kuyadingeka","optional":"Akuphoqelekile","name":"Igama","description":"Incazelo","date":"Usuku","time":"Isikhathi","status":"Isimo","type":"Uhlobo","category":"Isigaba","notes":"Amanothi","actions":"Izenzo"},"auth":{"login":"Ngena","logout":"Phuma","register":"Bhalisa","username":"Igama lomsebenzisi","email":"I-imeyili","password":"Iphasiwedi","confirmPassword":"Qinisekisa iphasiwedi","firstName":"Igama lokuqala","lastName":"Isibongo","phoneNumber":"Inombolo yocingo","department":"Umnyango","role":"Indima","forgotPassword":"Ukhohlwe iphasiwedi?","resetPassword":"Setha iphasiwedi kabusha","changePassword":"Shintsha iphasiwedi","currentPassword":"Iphasiwedi yamanje","newPassword":"Iphasiwedi entsha","loginSuccess":"Ukungenela kuphumelele","loginError":"Ukungenela kuhlulekile","logoutSuccess":"Ukuphuma kuphumelele","registerSuccess":"Ukubhalisa kuphumelele","registerError":"Ukubhalisa kuhlulekile","passwordChangeSuccess":"Iphasiwedi ishintshwe ngempumelelo","passwordChangeError":"Ukushintsha iphasiwedi kuhlulekile","invalidCredentials":"Igama lomsebenzisi noma iphasiwedi engalungile","accountDeactivated":"I-akhawunti ivaliwe","sessionExpired":"Isikhathi siphelelile. Sicela ungene futhi.","welcomeBack":"Sawubona futhi","signInToContinue":"Ngena ukuze uqhubeke ku-AMPD Livestock Management","dontHaveAccount":"Awunayo i-akhawunti?","alreadyHaveAccount":"Usunayo i-akhawunti?","createAccount":"Dala i-akhawunti","signIn":"Ngena"},"navigation":{"dashboard":"Ibhodi lokulawula","animals":"Izilwane","breeding":"Ukuzala","health":"Impilo","feeding":"Ukudla","financial":"Ezezimali","compliance":"Ukulandela imithetho","inventory":"Impahla","analytics":"Ukuhlaziya","reports":"Imibiko","settings":"Izilungiselelo","profile":"Iphrofayili","users":"Abasebenzisi","help":"Usizo","support":"Ukusekela"},"dashboard":{"title":"Ibhodi lokulawula","welcome":"Siyakwamukela ku-AMPD Livestock Management","overview":"Ukubuka konke","quickStats":"Izibalo ezisheshayo","recentActivity":"Umsebenzi wakamuva","alerts":"Izexwayiso","upcomingTasks":"Imisebenzi ezayo","totalAnimals":"Izilwane zonke","healthyAnimals":"Izilwane eziphilile","pregnantAnimals":"Izilwane ezikhulelwe","upcomingBirths":"Ukuzalwa okuzayo","vaccinationsDue":"Ukugoma okudingekayo","healthAlerts":"Izexwayiso zempilo","feedingAlerts":"Izexwayiso zokudla","breedingAlerts":"Izexwayiso zokuzala"},"languages":{"en":"IsiNgisi","af":"IsiBhunu","st":"IsiSuthu","tn":"IsiTswana","zu":"IsiZulu"},"roles":{"admin":"Umphathi","manager":"Umphathi","staff":"Abasebenzi","veterinarian":"Udokotela wezilwane","viewer":"Umbukeli"}}')}};Jo.use(Ai).use(si).init({resources:Li,fallbackLng:"en",debug:!1,interpolation:{escapeValue:!1},detection:{order:["localStorage","navigator","htmlTag"],caches:["localStorage"]},react:{useSuspense:!1}});function Ii(e,t){return t||(t=e.slice(0)),Object.freeze(Object.defineProperties(e,{raw:{value:Object.freeze(t)}}))}let _i={data:""},Di=e=>"object"==typeof window?((e?e.querySelector("#_goober"):window._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||_i,Fi=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,Mi=/\/\*[^]*?\*\/|  +/g,zi=/\n+/g,Bi=(e,t)=>{let n="",r="",a="";for(let o in e){let i=e[o];"@"==o[0]?"i"==o[1]?n=o+" "+i+";":r+="f"==o[1]?Bi(i,o):o+"{"+Bi(i,"k"==o[1]?"":t)+"}":"object"==typeof i?r+=Bi(i,t?t.replace(/([^,])+/g,(e=>o.replace(/([^,]*:\S+\([^)]*\))|([^,])+/g,(t=>/&/.test(t)?t.replace(/&/g,e):e?e+" "+t:t)))):o):null!=i&&(o=/^--/.test(o)?o:o.replace(/[A-Z]/g,"-$&").toLowerCase(),a+=Bi.p?Bi.p(o,i):o+":"+i+";")}return n+(t&&a?t+"{"+a+"}":a)+r},Ui={},Hi=e=>{if("object"==typeof e){let t="";for(let n in e)t+=n+Hi(e[n]);return t}return e},Vi=(e,t,n,r,a)=>{let o=Hi(e),i=Ui[o]||(Ui[o]=(e=>{let t=0,n=11;for(;t<e.length;)n=101*n+e.charCodeAt(t++)>>>0;return"go"+n})(o));if(!Ui[i]){let t=o!==e?e:(e=>{let t,n,r=[{}];for(;t=Fi.exec(e.replace(Mi,""));)t[4]?r.shift():t[3]?(n=t[3].replace(zi," ").trim(),r.unshift(r[0][n]=r[0][n]||{})):r[0][t[1]]=t[2].replace(zi," ").trim();return r[0]})(e);Ui[i]=Bi(a?{["@keyframes "+i]:t}:t,n?"":"."+i)}let s=n&&Ui.g?Ui.g:null;return n&&(Ui.g=Ui[i]),((e,t,n,r)=>{r?t.data=t.data.replace(r,e):-1===t.data.indexOf(e)&&(t.data=n?e+t.data:t.data+e)})(Ui[i],t,r,s),i};function Wi(e){let t=this||{},n=e.call?e(t.p):e;return Vi(n.unshift?n.raw?((e,t,n)=>e.reduce(((e,r,a)=>{let o=t[a];if(o&&o.call){let e=o(n),t=e&&e.props&&e.props.className||/^go/.test(e)&&e;o=t?"."+t:e&&"object"==typeof e?e.props?"":Bi(e,""):!1===e?"":e}return e+r+(null==o?"":o)}),""))(n,[].slice.call(arguments,1),t.p):n.reduce(((e,n)=>Object.assign(e,n&&n.call?n(t.p):n)),{}):n,Di(t.target),t.g,t.o,t.k)}Wi.bind({g:1});let $i,Ki,qi,Qi=Wi.bind({k:1});function Gi(e,t){let n=this||{};return function(){let r=arguments;function a(o,i){let s=Object.assign({},o),l=s.className||a.className;n.p=Object.assign({theme:Ki&&Ki()},s),n.o=/ *go\d+/.test(l),s.className=Wi.apply(n,r)+(l?" "+l:""),t&&(s.ref=i);let u=e;return e[0]&&(u=s.as||e,delete s.as),qi&&u[0]&&qi(s),$i(u,s)}return t?t(a):a}}var Ji,Xi,Yi,Zi,es,ts,ns,rs,as,os,is,ss,ls,us,cs,ds,fs=(e,t)=>(e=>"function"==typeof e)(e)?e(t):e,ps=(()=>{let e=0;return()=>(++e).toString()})(),hs=(()=>{let e;return()=>{if(void 0===e&&typeof window<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),ms=(e,t)=>{switch(t.type){case 0:return oo(oo({},e),{},{toasts:[t.toast,...e.toasts].slice(0,20)});case 1:return oo(oo({},e),{},{toasts:e.toasts.map((e=>e.id===t.toast.id?oo(oo({},e),t.toast):e))});case 2:let{toast:n}=t;return ms(e,{type:e.toasts.find((e=>e.id===n.id))?1:0,toast:n});case 3:let{toastId:r}=t;return oo(oo({},e),{},{toasts:e.toasts.map((e=>e.id===r||void 0===r?oo(oo({},e),{},{dismissed:!0,visible:!1}):e))});case 4:return void 0===t.toastId?oo(oo({},e),{},{toasts:[]}):oo(oo({},e),{},{toasts:e.toasts.filter((e=>e.id!==t.toastId))});case 5:return oo(oo({},e),{},{pausedAt:t.time});case 6:let a=t.time-(e.pausedAt||0);return oo(oo({},e),{},{pausedAt:void 0,toasts:e.toasts.map((e=>oo(oo({},e),{},{pauseDuration:e.pauseDuration+a})))})}},gs=[],ys={toasts:[],pausedAt:void 0},vs=e=>{ys=ms(ys,e),gs.forEach((e=>{e(ys)}))},bs={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},ws=e=>(t,n)=>{let r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"blank",n=arguments.length>2?arguments[2]:void 0;return oo(oo({createdAt:Date.now(),visible:!0,dismissed:!1,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0},n),{},{id:(null==n?void 0:n.id)||ps()})}(t,e,n);return vs({type:2,toast:r}),r.id},xs=(e,t)=>ws("blank")(e,t);xs.error=ws("error"),xs.success=ws("success"),xs.loading=ws("loading"),xs.custom=ws("custom"),xs.dismiss=e=>{vs({type:3,toastId:e})},xs.remove=e=>vs({type:4,toastId:e}),xs.promise=(e,t,n)=>{let r=xs.loading(t.loading,oo(oo({},n),null==n?void 0:n.loading));return"function"==typeof e&&(e=e()),e.then((e=>{let a=t.success?fs(t.success,e):void 0;return a?xs.success(a,oo(oo({id:r},n),null==n?void 0:n.success)):xs.dismiss(r),e})).catch((e=>{let a=t.error?fs(t.error,e):void 0;a?xs.error(a,oo(oo({id:r},n),null==n?void 0:n.error)):xs.dismiss(r)})),e};var ks=(e,t)=>{vs({type:1,toast:{id:e,height:t}})},Ss=()=>{vs({type:5,time:Date.now()})},Es=new Map,Ns=e=>{let{toasts:n,pausedAt:r}=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},[n,r]=(0,t.useState)(ys),a=(0,t.useRef)(ys);(0,t.useEffect)((()=>(a.current!==ys&&r(ys),gs.push(r),()=>{let e=gs.indexOf(r);e>-1&&gs.splice(e,1)})),[]);let o=n.toasts.map((t=>{var n,r,a;return oo(oo(oo(oo({},e),e[t.type]),t),{},{removeDelay:t.removeDelay||(null==(n=e[t.type])?void 0:n.removeDelay)||(null==e?void 0:e.removeDelay),duration:t.duration||(null==(r=e[t.type])?void 0:r.duration)||(null==e?void 0:e.duration)||bs[t.type],style:oo(oo(oo({},e.style),null==(a=e[t.type])?void 0:a.style),t.style)})}));return oo(oo({},n),{},{toasts:o})}(e);(0,t.useEffect)((()=>{if(r)return;let e=Date.now(),t=n.map((t=>{if(t.duration===1/0)return;let n=(t.duration||0)+t.pauseDuration-(e-t.createdAt);if(!(n<0))return setTimeout((()=>xs.dismiss(t.id)),n);t.visible&&xs.dismiss(t.id)}));return()=>{t.forEach((e=>e&&clearTimeout(e)))}}),[n,r]);let a=(0,t.useCallback)((()=>{r&&vs({type:6,time:Date.now()})}),[r]),o=(0,t.useCallback)(((e,t)=>{let{reverseOrder:r=!1,gutter:a=8,defaultPosition:o}=t||{},i=n.filter((t=>(t.position||o)===(e.position||o)&&t.height)),s=i.findIndex((t=>t.id===e.id)),l=i.filter(((e,t)=>t<s&&e.visible)).length;return i.filter((e=>e.visible)).slice(...r?[l+1]:[0,l]).reduce(((e,t)=>e+(t.height||0)+a),0)}),[n]);return(0,t.useEffect)((()=>{n.forEach((e=>{if(e.dismissed)!function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1e3;if(Es.has(e))return;let n=setTimeout((()=>{Es.delete(e),vs({type:4,toastId:e})}),t);Es.set(e,n)}(e.id,e.removeDelay);else{let t=Es.get(e.id);t&&(clearTimeout(t),Es.delete(e.id))}}))}),[n]),{toasts:n,handlers:{updateHeight:ks,startPause:Ss,endPause:a,calculateOffset:o}}},Cs=Qi(Ji||(Ji=Ii(["\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n transform: scale(1) rotate(45deg);\n  opacity: 1;\n}"]))),js=Qi(Xi||(Xi=Ii(["\nfrom {\n  transform: scale(0);\n  opacity: 0;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}"]))),Os=Qi(Yi||(Yi=Ii(["\nfrom {\n  transform: scale(0) rotate(90deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(90deg);\n\topacity: 1;\n}"]))),Ps=Gi("div")(Zi||(Zi=Ii(["\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ",";\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: "," 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n\n  &:after,\n  &:before {\n    content: '';\n    animation: "," 0.15s ease-out forwards;\n    animation-delay: 150ms;\n    position: absolute;\n    border-radius: 3px;\n    opacity: 0;\n    background: ",";\n    bottom: 9px;\n    left: 4px;\n    height: 2px;\n    width: 12px;\n  }\n\n  &:before {\n    animation: "," 0.15s ease-out forwards;\n    animation-delay: 180ms;\n    transform: rotate(90deg);\n  }\n"])),(e=>e.primary||"#ff4b4b"),Cs,js,(e=>e.secondary||"#fff"),Os),Rs=Qi(es||(es=Ii(["\n  from {\n    transform: rotate(0deg);\n  }\n  to {\n    transform: rotate(360deg);\n  }\n"]))),Ts=Gi("div")(ts||(ts=Ii(["\n  width: 12px;\n  height: 12px;\n  box-sizing: border-box;\n  border: 2px solid;\n  border-radius: 100%;\n  border-color: ",";\n  border-right-color: ",";\n  animation: "," 1s linear infinite;\n"])),(e=>e.secondary||"#e0e0e0"),(e=>e.primary||"#616161"),Rs),As=Qi(ns||(ns=Ii(["\nfrom {\n  transform: scale(0) rotate(45deg);\n\topacity: 0;\n}\nto {\n  transform: scale(1) rotate(45deg);\n\topacity: 1;\n}"]))),Ls=Qi(rs||(rs=Ii(["\n0% {\n\theight: 0;\n\twidth: 0;\n\topacity: 0;\n}\n40% {\n  height: 0;\n\twidth: 6px;\n\topacity: 1;\n}\n100% {\n  opacity: 1;\n  height: 10px;\n}"]))),Is=Gi("div")(as||(as=Ii(["\n  width: 20px;\n  opacity: 0;\n  height: 20px;\n  border-radius: 10px;\n  background: ",";\n  position: relative;\n  transform: rotate(45deg);\n\n  animation: "," 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n  animation-delay: 100ms;\n  &:after {\n    content: '';\n    box-sizing: border-box;\n    animation: "," 0.2s ease-out forwards;\n    opacity: 0;\n    animation-delay: 200ms;\n    position: absolute;\n    border-right: 2px solid;\n    border-bottom: 2px solid;\n    border-color: ",";\n    bottom: 6px;\n    left: 6px;\n    height: 10px;\n    width: 6px;\n  }\n"])),(e=>e.primary||"#61d345"),As,Ls,(e=>e.secondary||"#fff")),_s=Gi("div")(os||(os=Ii(["\n  position: absolute;\n"]))),Ds=Gi("div")(is||(is=Ii(["\n  position: relative;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n  min-width: 20px;\n  min-height: 20px;\n"]))),Fs=Qi(ss||(ss=Ii(["\nfrom {\n  transform: scale(0.6);\n  opacity: 0.4;\n}\nto {\n  transform: scale(1);\n  opacity: 1;\n}"]))),Ms=Gi("div")(ls||(ls=Ii(["\n  position: relative;\n  transform: scale(0.6);\n  opacity: 0.4;\n  min-width: 20px;\n  animation: "," 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)\n    forwards;\n"])),Fs),zs=e=>{let{toast:n}=e,{icon:r,type:a,iconTheme:o}=n;return void 0!==r?"string"==typeof r?t.createElement(Ms,null,r):r:"blank"===a?null:t.createElement(Ds,null,t.createElement(Ts,oo({},o)),"loading"!==a&&t.createElement(_s,null,"error"===a?t.createElement(Ps,oo({},o)):t.createElement(Is,oo({},o))))},Bs=e=>"\n0% {transform: translate3d(0,".concat(-200*e,"%,0) scale(.6); opacity:.5;}\n100% {transform: translate3d(0,0,0) scale(1); opacity:1;}\n"),Us=e=>"\n0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}\n100% {transform: translate3d(0,".concat(-150*e,"%,-1px) scale(.6); opacity:0;}\n"),Hs=Gi("div")(us||(us=Ii(["\n  display: flex;\n  align-items: center;\n  background: #fff;\n  color: #363636;\n  line-height: 1.3;\n  will-change: transform;\n  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);\n  max-width: 350px;\n  pointer-events: auto;\n  padding: 8px 10px;\n  border-radius: 8px;\n"]))),Vs=Gi("div")(cs||(cs=Ii(["\n  display: flex;\n  justify-content: center;\n  margin: 4px 10px;\n  color: inherit;\n  flex: 1 1 auto;\n  white-space: pre-line;\n"]))),Ws=t.memo((e=>{let{toast:n,position:r,style:a,children:o}=e,i=n.height?((e,t)=>{let n=e.includes("top")?1:-1,[r,a]=hs()?["0%{opacity:0;} 100%{opacity:1;}","0%{opacity:1;} 100%{opacity:0;}"]:[Bs(n),Us(n)];return{animation:t?"".concat(Qi(r)," 0.35s cubic-bezier(.21,1.02,.73,1) forwards"):"".concat(Qi(a)," 0.4s forwards cubic-bezier(.06,.71,.55,1)")}})(n.position||r||"top-center",n.visible):{opacity:0},s=t.createElement(zs,{toast:n}),l=t.createElement(Vs,oo({},n.ariaProps),fs(n.message,n));return t.createElement(Hs,{className:n.className,style:oo(oo(oo({},i),a),n.style)},"function"==typeof o?o({icon:s,message:l}):t.createElement(t.Fragment,null,s,l))}));!function(e,t,n,r){Bi.p=t,$i=e,Ki=n,qi=r}(t.createElement);var $s=e=>{let{id:n,className:r,style:a,onHeightUpdate:o,children:i}=e,s=t.useCallback((e=>{if(e){let t=()=>{let t=e.getBoundingClientRect().height;o(n,t)};t(),new MutationObserver(t).observe(e,{subtree:!0,childList:!0,characterData:!0})}}),[n,o]);return t.createElement("div",{ref:s,className:r,style:a},i)},Ks=Wi(ds||(ds=Ii(["\n  z-index: 9999;\n  > * {\n    pointer-events: auto;\n  }\n"]))),qs=e=>{let{reverseOrder:n,position:r="top-center",toastOptions:a,gutter:o,children:i,containerStyle:s,containerClassName:l}=e,{toasts:u,handlers:c}=Ns(a);return t.createElement("div",{id:"_rht_toaster",style:oo({position:"fixed",zIndex:9999,top:16,left:16,right:16,bottom:16,pointerEvents:"none"},s),className:l,onMouseEnter:c.startPause,onMouseLeave:c.endPause},u.map((e=>{let a=e.position||r,s=((e,t)=>{let n=e.includes("top"),r=n?{top:0}:{bottom:0},a=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return oo(oo({left:0,right:0,display:"flex",position:"absolute",transition:hs()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:"translateY(".concat(t*(n?1:-1),"px)")},r),a)})(a,c.calculateOffset(e,{reverseOrder:n,gutter:o,defaultPosition:r}));return t.createElement($s,{id:e.id,key:e.id,onHeightUpdate:c.updateHeight,className:e.visible?Ks:"",style:s},"custom"===e.type?fs(e.message,e):i?i(e):t.createElement(Ws,{toast:e,position:a}))})))};function Qs(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw Error("[Immer] minified error nr: "+e+(n.length?" "+n.map((function(e){return"'"+e+"'"})).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function Gs(e){return!!e&&!!e[Fl]}function Js(e){var t;return!!e&&(function(e){if(!e||"object"!=typeof e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;var n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object||"function"==typeof n&&Function.toString.call(n)===Ml}(e)||Array.isArray(e)||!!e[Dl]||!!(null===(t=e.constructor)||void 0===t?void 0:t[Dl])||rl(e)||al(e))}function Xs(e,t,n){void 0===n&&(n=!1),0===Ys(e)?(n?Object.keys:zl)(e).forEach((function(r){n&&"symbol"==typeof r||t(r,e[r],e)})):e.forEach((function(n,r){return t(r,n,e)}))}function Ys(e){var t=e[Fl];return t?t.i>3?t.i-4:t.i:Array.isArray(e)?1:rl(e)?2:al(e)?3:0}function Zs(e,t){return 2===Ys(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function el(e,t){return 2===Ys(e)?e.get(t):e[t]}function tl(e,t,n){var r=Ys(e);2===r?e.set(t,n):3===r?e.add(n):e[t]=n}function nl(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}function rl(e){return Al&&e instanceof Map}function al(e){return Ll&&e instanceof Set}function ol(e){return e.o||e.t}function il(e){if(Array.isArray(e))return Array.prototype.slice.call(e);var t=Bl(e);delete t[Fl];for(var n=zl(t),r=0;r<n.length;r++){var a=n[r],o=t[a];!1===o.writable&&(o.writable=!0,o.configurable=!0),(o.get||o.set)&&(t[a]={configurable:!0,writable:!0,enumerable:o.enumerable,value:e[a]})}return Object.create(Object.getPrototypeOf(e),t)}function sl(e,t){return void 0===t&&(t=!1),ul(e)||Gs(e)||!Js(e)||(Ys(e)>1&&(e.set=e.add=e.clear=e.delete=ll),Object.freeze(e),t&&Xs(e,(function(e,t){return sl(t,!0)}),!0)),e}function ll(){Qs(2)}function ul(e){return null==e||"object"!=typeof e||Object.isFrozen(e)}function cl(e){var t=Ul[e];return t||Qs(18,e),t}function dl(e,t){Ul[e]||(Ul[e]=t)}function fl(){return Rl}function pl(e,t){t&&(cl("Patches"),e.u=[],e.s=[],e.v=t)}function hl(e){ml(e),e.p.forEach(yl),e.p=null}function ml(e){e===Rl&&(Rl=e.l)}function gl(e){return Rl={p:[],l:Rl,h:e,m:!0,_:0}}function yl(e){var t=e[Fl];0===t.i||1===t.i?t.j():t.g=!0}function vl(e,t){t._=t.p.length;var n=t.p[0],r=void 0!==e&&e!==n;return t.h.O||cl("ES5").S(t,e,r),r?(n[Fl].P&&(hl(t),Qs(4)),Js(e)&&(e=bl(t,e),t.l||xl(t,e)),t.u&&cl("Patches").M(n[Fl].t,e,t.u,t.s)):e=bl(t,n,[]),hl(t),t.u&&t.v(t.u,t.s),e!==_l?e:void 0}function bl(e,t,n){if(ul(t))return t;var r=t[Fl];if(!r)return Xs(t,(function(a,o){return wl(e,r,t,a,o,n)}),!0),t;if(r.A!==e)return t;if(!r.P)return xl(e,r.t,!0),r.t;if(!r.I){r.I=!0,r.A._--;var a=4===r.i||5===r.i?r.o=il(r.k):r.o,o=a,i=!1;3===r.i&&(o=new Set(a),a.clear(),i=!0),Xs(o,(function(t,o){return wl(e,r,a,t,o,n,i)})),xl(e,a,!1),n&&e.u&&cl("Patches").N(r,n,e.u,e.s)}return r.o}function wl(e,t,n,r,a,o,i){if(Gs(a)){var s=bl(e,a,o&&t&&3!==t.i&&!Zs(t.R,r)?o.concat(r):void 0);if(tl(n,r,s),!Gs(s))return;e.m=!1}else i&&n.add(a);if(Js(a)&&!ul(a)){if(!e.h.D&&e._<1)return;bl(e,a),t&&t.A.l||xl(e,a)}}function xl(e,t,n){void 0===n&&(n=!1),!e.l&&e.h.D&&e.m&&sl(t,n)}function kl(e,t){var n=e[Fl];return(n?ol(n):e)[t]}function Sl(e,t){if(t in e)for(var n=Object.getPrototypeOf(e);n;){var r=Object.getOwnPropertyDescriptor(n,t);if(r)return r;n=Object.getPrototypeOf(n)}}function El(e){e.P||(e.P=!0,e.l&&El(e.l))}function Nl(e){e.o||(e.o=il(e.t))}function Cl(e,t,n){var r=rl(t)?cl("MapSet").F(t,n):al(t)?cl("MapSet").T(t,n):e.O?function(e,t){var n=Array.isArray(e),r={i:n?1:0,A:t?t.A:fl(),P:!1,I:!1,R:{},l:t,t:e,k:null,o:null,j:null,C:!1},a=r,o=Hl;n&&(a=[r],o=Vl);var i=Proxy.revocable(a,o),s=i.revoke,l=i.proxy;return r.k=l,r.j=s,l}(t,n):cl("ES5").J(t,n);return(n?n.A:fl()).p.push(r),r}function jl(e,t){switch(t){case 2:return new Map(e);case 3:return Array.from(e)}return il(e)}function Ol(){function e(e,t){var n=a[e];return n?n.enumerable=t:a[e]=n={configurable:!0,enumerable:t,get:function(){var t=this[Fl];return Hl.get(t,e)},set:function(t){var n=this[Fl];Hl.set(n,e,t)}},n}function t(e){for(var t=e.length-1;t>=0;t--){var a=e[t][Fl];if(!a.P)switch(a.i){case 5:r(a)&&El(a);break;case 4:n(a)&&El(a)}}}function n(e){for(var t=e.t,n=e.k,r=zl(n),a=r.length-1;a>=0;a--){var o=r[a];if(o!==Fl){var i=t[o];if(void 0===i&&!Zs(t,o))return!0;var s=n[o],l=s&&s[Fl];if(l?l.t!==i:!nl(s,i))return!0}}var u=!!t[Fl];return r.length!==zl(t).length+(u?0:1)}function r(e){var t=e.k;if(t.length!==e.t.length)return!0;var n=Object.getOwnPropertyDescriptor(t,t.length-1);if(n&&!n.get)return!0;for(var r=0;r<t.length;r++)if(!t.hasOwnProperty(r))return!0;return!1}var a={};dl("ES5",{J:function(t,n){var r=Array.isArray(t),a=function(t,n){if(t){for(var r=Array(n.length),a=0;a<n.length;a++)Object.defineProperty(r,""+a,e(a,!0));return r}var o=Bl(n);delete o[Fl];for(var i=zl(o),s=0;s<i.length;s++){var l=i[s];o[l]=e(l,t||!!o[l].enumerable)}return Object.create(Object.getPrototypeOf(n),o)}(r,t),o={i:r?5:4,A:n?n.A:fl(),P:!1,I:!1,R:{},l:n,t:t,k:a,o:null,g:!1,C:!1};return Object.defineProperty(a,Fl,{value:o,writable:!0}),a},S:function(e,n,a){a?Gs(n)&&n[Fl].A===e&&t(e.p):(e.u&&function e(t){if(t&&"object"==typeof t){var n=t[Fl];if(n){var a=n.t,o=n.k,i=n.R,s=n.i;if(4===s)Xs(o,(function(t){t!==Fl&&(void 0!==a[t]||Zs(a,t)?i[t]||e(o[t]):(i[t]=!0,El(n)))})),Xs(a,(function(e){void 0!==o[e]||Zs(o,e)||(i[e]=!1,El(n))}));else if(5===s){if(r(n)&&(El(n),i.length=!0),o.length<a.length)for(var l=o.length;l<a.length;l++)i[l]=!1;else for(var u=a.length;u<o.length;u++)i[u]=!0;for(var c=Math.min(o.length,a.length),d=0;d<c;d++)o.hasOwnProperty(d)||(i[d]=!0),void 0===i[d]&&e(o[d])}}}}(e.p[0]),t(e.p))},K:function(e){return 4===e.i?n(e):r(e)}})}var Pl,Rl,Tl="undefined"!=typeof Symbol&&"symbol"==typeof Symbol("x"),Al="undefined"!=typeof Map,Ll="undefined"!=typeof Set,Il="undefined"!=typeof Proxy&&void 0!==Proxy.revocable&&"undefined"!=typeof Reflect,_l=Tl?Symbol.for("immer-nothing"):((Pl={})["immer-nothing"]=!0,Pl),Dl=Tl?Symbol.for("immer-draftable"):"__$immer_draftable",Fl=Tl?Symbol.for("immer-state"):"__$immer_state",Ml=("undefined"!=typeof Symbol&&Symbol.iterator,""+Object.prototype.constructor),zl="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:void 0!==Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames,Bl=Object.getOwnPropertyDescriptors||function(e){var t={};return zl(e).forEach((function(n){t[n]=Object.getOwnPropertyDescriptor(e,n)})),t},Ul={},Hl={get:function(e,t){if(t===Fl)return e;var n=ol(e);if(!Zs(n,t))return function(e,t,n){var r,a=Sl(t,n);return a?"value"in a?a.value:null===(r=a.get)||void 0===r?void 0:r.call(e.k):void 0}(e,n,t);var r=n[t];return e.I||!Js(r)?r:r===kl(e.t,t)?(Nl(e),e.o[t]=Cl(e.A.h,r,e)):r},has:function(e,t){return t in ol(e)},ownKeys:function(e){return Reflect.ownKeys(ol(e))},set:function(e,t,n){var r=Sl(ol(e),t);if(null==r?void 0:r.set)return r.set.call(e.k,n),!0;if(!e.P){var a=kl(ol(e),t),o=null==a?void 0:a[Fl];if(o&&o.t===n)return e.o[t]=n,e.R[t]=!1,!0;if(nl(n,a)&&(void 0!==n||Zs(e.t,t)))return!0;Nl(e),El(e)}return e.o[t]===n&&(void 0!==n||t in e.o)||Number.isNaN(n)&&Number.isNaN(e.o[t])||(e.o[t]=n,e.R[t]=!0),!0},deleteProperty:function(e,t){return void 0!==kl(e.t,t)||t in e.t?(e.R[t]=!1,Nl(e),El(e)):delete e.R[t],e.o&&delete e.o[t],!0},getOwnPropertyDescriptor:function(e,t){var n=ol(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r?{writable:!0,configurable:1!==e.i||"length"!==t,enumerable:r.enumerable,value:n[t]}:r},defineProperty:function(){Qs(11)},getPrototypeOf:function(e){return Object.getPrototypeOf(e.t)},setPrototypeOf:function(){Qs(12)}},Vl={};Xs(Hl,(function(e,t){Vl[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),Vl.deleteProperty=function(e,t){return Vl.set.call(this,e,t,void 0)},Vl.set=function(e,t,n){return Hl.set.call(this,e[0],t,n,e[0])};var Wl=function(){function e(e){var t=this;this.O=Il,this.D=!0,this.produce=function(e,n,r){if("function"==typeof e&&"function"!=typeof n){var a=n;n=e;var o=t;return function(e){var t=this;void 0===e&&(e=a);for(var r=arguments.length,i=Array(r>1?r-1:0),s=1;s<r;s++)i[s-1]=arguments[s];return o.produce(e,(function(e){var r;return(r=n).call.apply(r,[t,e].concat(i))}))}}var i;if("function"!=typeof n&&Qs(6),void 0!==r&&"function"!=typeof r&&Qs(7),Js(e)){var s=gl(t),l=Cl(t,e,void 0),u=!0;try{i=n(l),u=!1}finally{u?hl(s):ml(s)}return"undefined"!=typeof Promise&&i instanceof Promise?i.then((function(e){return pl(s,r),vl(e,s)}),(function(e){throw hl(s),e})):(pl(s,r),vl(i,s))}if(!e||"object"!=typeof e){if(void 0===(i=n(e))&&(i=e),i===_l&&(i=void 0),t.D&&sl(i,!0),r){var c=[],d=[];cl("Patches").M(e,i,c,d),r(c,d)}return i}Qs(21,e)},this.produceWithPatches=function(e,n){if("function"==typeof e)return function(n){for(var r=arguments.length,a=Array(r>1?r-1:0),o=1;o<r;o++)a[o-1]=arguments[o];return t.produceWithPatches(n,(function(t){return e.apply(void 0,[t].concat(a))}))};var r,a,o=t.produce(e,n,(function(e,t){r=e,a=t}));return"undefined"!=typeof Promise&&o instanceof Promise?o.then((function(e){return[e,r,a]})):[o,r,a]},"boolean"==typeof(null==e?void 0:e.useProxies)&&this.setUseProxies(e.useProxies),"boolean"==typeof(null==e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze)}var t=e.prototype;return t.createDraft=function(e){Js(e)||Qs(8),Gs(e)&&(e=function(e){return Gs(e)||Qs(22,e),function e(t){if(!Js(t))return t;var n,r=t[Fl],a=Ys(t);if(r){if(!r.P&&(r.i<4||!cl("ES5").K(r)))return r.t;r.I=!0,n=jl(t,a),r.I=!1}else n=jl(t,a);return Xs(n,(function(t,a){r&&el(r.t,t)===a||tl(n,t,e(a))})),3===a?new Set(n):n}(e)}(e));var t=gl(this),n=Cl(this,e,void 0);return n[Fl].C=!0,ml(t),n},t.finishDraft=function(e,t){var n=(e&&e[Fl]).A;return pl(n,t),vl(void 0,n)},t.setAutoFreeze=function(e){this.D=e},t.setUseProxies=function(e){e&&!Il&&Qs(20),this.O=e},t.applyPatches=function(e,t){var n;for(n=t.length-1;n>=0;n--){var r=t[n];if(0===r.path.length&&"replace"===r.op){e=r.value;break}}n>-1&&(t=t.slice(n+1));var a=cl("Patches").$;return Gs(e)?a(e,t):this.produce(e,(function(e){return a(e,t)}))},e}(),$l=new Wl,Kl=$l.produce;$l.produceWithPatches.bind($l),$l.setAutoFreeze.bind($l),$l.setUseProxies.bind($l),$l.applyPatches.bind($l),$l.createDraft.bind($l),$l.finishDraft.bind($l);const ql=Kl;function Ql(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var Gl="function"===typeof Symbol&&Symbol.observable||"@@observable",Jl=function(){return Math.random().toString(36).substring(7).split("").join(".")},Xl={INIT:"@@redux/INIT"+Jl(),REPLACE:"@@redux/REPLACE"+Jl(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+Jl()}};function Yl(e){if("object"!==typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function Zl(e,t,n){var r;if("function"===typeof t&&"function"===typeof n||"function"===typeof n&&"function"===typeof arguments[3])throw new Error(Ql(0));if("function"===typeof t&&"undefined"===typeof n&&(n=t,t=void 0),"undefined"!==typeof n){if("function"!==typeof n)throw new Error(Ql(1));return n(Zl)(e,t)}if("function"!==typeof e)throw new Error(Ql(2));var a=e,o=t,i=[],s=i,l=!1;function u(){s===i&&(s=i.slice())}function c(){if(l)throw new Error(Ql(3));return o}function d(e){if("function"!==typeof e)throw new Error(Ql(4));if(l)throw new Error(Ql(5));var t=!0;return u(),s.push(e),function(){if(t){if(l)throw new Error(Ql(6));t=!1,u();var n=s.indexOf(e);s.splice(n,1),i=null}}}function f(e){if(!Yl(e))throw new Error(Ql(7));if("undefined"===typeof e.type)throw new Error(Ql(8));if(l)throw new Error(Ql(9));try{l=!0,o=a(o,e)}finally{l=!1}for(var t=i=s,n=0;n<t.length;n++){(0,t[n])()}return e}return f({type:Xl.INIT}),(r={dispatch:f,subscribe:d,getState:c,replaceReducer:function(e){if("function"!==typeof e)throw new Error(Ql(10));a=e,f({type:Xl.REPLACE})}})[Gl]=function(){var e,t=d;return(e={subscribe:function(e){if("object"!==typeof e||null===e)throw new Error(Ql(11));function n(){e.next&&e.next(c())}return n(),{unsubscribe:t(n)}}})[Gl]=function(){return this},e},r}function eu(e){for(var t=Object.keys(e),n={},r=0;r<t.length;r++){var a=t[r];0,"function"===typeof e[a]&&(n[a]=e[a])}var o,i=Object.keys(n);try{!function(e){Object.keys(e).forEach((function(t){var n=e[t];if("undefined"===typeof n(void 0,{type:Xl.INIT}))throw new Error(Ql(12));if("undefined"===typeof n(void 0,{type:Xl.PROBE_UNKNOWN_ACTION()}))throw new Error(Ql(13))}))}(n)}catch(_i){o=_i}return function(e,t){if(void 0===e&&(e={}),o)throw o;for(var r=!1,a={},s=0;s<i.length;s++){var l=i[s],u=n[l],c=e[l],d=u(c,t);if("undefined"===typeof d){t&&t.type;throw new Error(Ql(14))}a[l]=d,r=r||d!==c}return(r=r||i.length!==Object.keys(e).length)?a:e}}function tu(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}function nu(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return function(){var n=e.apply(void 0,arguments),r=function(){throw new Error(Ql(15))},a={getState:n.getState,dispatch:function(){return r.apply(void 0,arguments)}},o=t.map((function(e){return e(a)}));return r=tu.apply(void 0,o)(n.dispatch),oo(oo({},n),{},{dispatch:r})}}}function ru(e){return function(t){var n=t.dispatch,r=t.getState;return function(t){return function(a){return"function"===typeof a?a(n,r,e):t(a)}}}}var au=ru();au.withExtraArgument=ru;const ou=au;var iu=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),su=function(e,t){var n,r,a,o,i={label:0,sent:function(){if(1&a[0])throw a[1];return a[1]},trys:[],ops:[]};return o={next:s(0),throw:s(1),return:s(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function s(o){return function(s){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(a=2&o[0]?r.return:o[0]?r.throw||((a=r.return)&&a.call(r),0):r.next)&&!(a=a.call(r,o[1])).done)return a;switch(r=0,a&&(o=[2&o[0],a.value]),o[0]){case 0:case 1:a=o;break;case 4:return i.label++,{value:o[1],done:!1};case 5:i.label++,r=o[1],o=[0];continue;case 7:o=i.ops.pop(),i.trys.pop();continue;default:if(!(a=(a=i.trys).length>0&&a[a.length-1])&&(6===o[0]||2===o[0])){i=0;continue}if(3===o[0]&&(!a||o[1]>a[0]&&o[1]<a[3])){i.label=o[1];break}if(6===o[0]&&i.label<a[1]){i.label=a[1],a=o;break}if(a&&i.label<a[2]){i.label=a[2],i.ops.push(o);break}a[2]&&i.ops.pop(),i.trys.pop();continue}o=t.call(e,i)}catch(_i){o=[6,_i],r=0}finally{n=a=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,s])}}},lu=function(e,t){for(var n=0,r=t.length,a=e.length;n<r;n++,a++)e[a]=t[n];return e},uu=Object.defineProperty,cu=Object.defineProperties,du=Object.getOwnPropertyDescriptors,fu=Object.getOwnPropertySymbols,pu=Object.prototype.hasOwnProperty,hu=Object.prototype.propertyIsEnumerable,mu=function(e,t,n){return t in e?uu(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},gu=function(e,t){for(var n in t||(t={}))pu.call(t,n)&&mu(e,n,t[n]);if(fu)for(var r=0,a=fu(t);r<a.length;r++){n=a[r];hu.call(t,n)&&mu(e,n,t[n])}return e},yu=function(e,t){return cu(e,du(t))},vu=function(e,t,n){return new Promise((function(r,a){var o=function(e){try{s(n.next(e))}catch(_i){a(_i)}},i=function(e){try{s(n.throw(e))}catch(_i){a(_i)}},s=function(e){return e.done?r(e.value):Promise.resolve(e.value).then(o,i)};s((n=n.apply(e,t)).next())}))},bu="undefined"!==typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"===typeof arguments[0]?tu:tu.apply(null,arguments)};"undefined"!==typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;function wu(e){if("object"!==typeof e||null===e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;for(var n=t;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return t===n}function xu(e,t){function n(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];if(t){var a=t.apply(void 0,n);if(!a)throw new Error("prepareAction did not return an object");return gu(gu({type:e,payload:a.payload},"meta"in a&&{meta:a.meta}),"error"in a&&{error:a.error})}return{type:e,payload:n[0]}}return n.toString=function(){return""+e},n.type=e,n.match=function(t){return t.type===e},n}var ku=function(e){function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var a=e.apply(this,n)||this;return Object.setPrototypeOf(a,t.prototype),a}return iu(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.prototype.concat.apply(this,t)},t.prototype.prepend=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,lu([void 0],e[0].concat(this)))):new(t.bind.apply(t,lu([void 0],e.concat(this))))},t}(Array),Su=function(e){function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var a=e.apply(this,n)||this;return Object.setPrototypeOf(a,t.prototype),a}return iu(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.prototype.concat.apply(this,t)},t.prototype.prepend=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,lu([void 0],e[0].concat(this)))):new(t.bind.apply(t,lu([void 0],e.concat(this))))},t}(Array);function Eu(e){return Js(e)?ql(e,(function(){})):e}function Nu(){return function(e){return function(e){void 0===e&&(e={});var t=e.thunk,n=void 0===t||t,r=(e.immutableCheck,e.serializableCheck,e.actionCreatorCheck,new ku);n&&(!function(e){return"boolean"===typeof e}(n)?r.push(ou.withExtraArgument(n.extraArgument)):r.push(ou));0;return r}(e)}}function Cu(e){var t,n={},r=[],a={addCase:function(e,t){var r="string"===typeof e?e:e.type;if(!r)throw new Error("`builder.addCase` cannot be called with an empty action type");if(r in n)throw new Error("`builder.addCase` cannot be called with two reducers for the same action type");return n[r]=t,a},addMatcher:function(e,t){return r.push({matcher:e,reducer:t}),a},addDefaultCase:function(e){return t=e,a}};return e(a),[n,r,t]}function ju(e){var t=e.name;if(!t)throw new Error("`name` is a required option for createSlice");var n,r="function"==typeof e.initialState?e.initialState:Eu(e.initialState),a=e.reducers||{},o=Object.keys(a),i={},s={},l={};function u(){var t="function"===typeof e.extraReducers?Cu(e.extraReducers):[e.extraReducers],n=t[0],a=void 0===n?{}:n,o=t[1],i=void 0===o?[]:o,l=t[2],u=void 0===l?void 0:l,c=gu(gu({},a),s);return function(e,t,n,r){void 0===n&&(n=[]);var a,o="function"===typeof t?Cu(t):[t,n,r],i=o[0],s=o[1],l=o[2];if(function(e){return"function"===typeof e}(e))a=function(){return Eu(e())};else{var u=Eu(e);a=function(){return u}}function c(e,t){void 0===e&&(e=a());var n=lu([i[t.type]],s.filter((function(e){return(0,e.matcher)(t)})).map((function(e){return e.reducer})));return 0===n.filter((function(e){return!!e})).length&&(n=[l]),n.reduce((function(e,n){if(n){var r;if(Gs(e))return void 0===(r=n(e,t))?e:r;if(Js(e))return ql(e,(function(e){return n(e,t)}));if(void 0===(r=n(e,t))){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return r}return e}),e)}return c.getInitialState=a,c}(r,(function(e){for(var t in c)e.addCase(t,c[t]);for(var n=0,r=i;n<r.length;n++){var a=r[n];e.addMatcher(a.matcher,a.reducer)}u&&e.addDefaultCase(u)}))}return o.forEach((function(e){var n,r,o=a[e],u=function(e,t){return e+"/"+t}(t,e);"reducer"in o?(n=o.reducer,r=o.prepare):n=o,i[e]=n,s[u]=n,l[e]=r?xu(u,r):xu(u)})),{name:t,reducer:function(e,t){return n||(n=u()),n(e,t)},actions:l,caseReducers:i,getInitialState:function(){return n||(n=u()),n.getInitialState()}}}var Ou=function(e){void 0===e&&(e=21);for(var t="",n=e;n--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},Pu=["name","message","stack","code"],Ru=function(e,t){this.payload=e,this.meta=t},Tu=function(e,t){this.payload=e,this.meta=t},Au=function(e){if("object"===typeof e&&null!==e){for(var t={},n=0,r=Pu;n<r.length;n++){var a=r[n];"string"===typeof e[a]&&(t[a]=e[a])}return t}return{message:String(e)}},Lu=function(){function e(e,t,n){var r=xu(e+"/fulfilled",(function(e,t,n,r){return{payload:e,meta:yu(gu({},r||{}),{arg:n,requestId:t,requestStatus:"fulfilled"})}})),a=xu(e+"/pending",(function(e,t,n){return{payload:void 0,meta:yu(gu({},n||{}),{arg:t,requestId:e,requestStatus:"pending"})}})),o=xu(e+"/rejected",(function(e,t,r,a,o){return{payload:a,error:(n&&n.serializeError||Au)(e||"Rejected"),meta:yu(gu({},o||{}),{arg:r,requestId:t,rejectedWithValue:!!a,requestStatus:"rejected",aborted:"AbortError"===(null==e?void 0:e.name),condition:"ConditionError"===(null==e?void 0:e.name)})}})),i="undefined"!==typeof AbortController?AbortController:function(){function e(){this.signal={aborted:!1,addEventListener:function(){},dispatchEvent:function(){return!1},onabort:function(){},removeEventListener:function(){},reason:void 0,throwIfAborted:function(){}}}return e.prototype.abort=function(){0},e}();return Object.assign((function(e){return function(s,l,u){var c,d=(null==n?void 0:n.idGenerator)?n.idGenerator(e):Ou(),f=new i;function p(e){c=e,f.abort()}var h=function(){return vu(this,null,(function(){var i,h,m,g,y,v;return su(this,(function(b){switch(b.label){case 0:return b.trys.push([0,4,,5]),g=null==(i=null==n?void 0:n.condition)?void 0:i.call(n,e,{getState:l,extra:u}),null===(w=g)||"object"!==typeof w||"function"!==typeof w.then?[3,2]:[4,g];case 1:g=b.sent(),b.label=2;case 2:if(!1===g||f.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};return y=new Promise((function(e,t){return f.signal.addEventListener("abort",(function(){return t({name:"AbortError",message:c||"Aborted"})}))})),s(a(d,e,null==(h=null==n?void 0:n.getPendingMeta)?void 0:h.call(n,{requestId:d,arg:e},{getState:l,extra:u}))),[4,Promise.race([y,Promise.resolve(t(e,{dispatch:s,getState:l,extra:u,requestId:d,signal:f.signal,abort:p,rejectWithValue:function(e,t){return new Ru(e,t)},fulfillWithValue:function(e,t){return new Tu(e,t)}})).then((function(t){if(t instanceof Ru)throw t;return t instanceof Tu?r(t.payload,d,e,t.meta):r(t,d,e)}))])];case 3:return m=b.sent(),[3,5];case 4:return v=b.sent(),m=v instanceof Ru?o(null,d,e,v.payload,v.meta):o(v,d,e),[3,5];case 5:return n&&!n.dispatchConditionRejection&&o.match(m)&&m.meta.condition||s(m),[2,m]}var w}))}))}();return Object.assign(h,{abort:p,requestId:d,arg:e,unwrap:function(){return h.then(Iu)}})}}),{pending:a,rejected:o,fulfilled:r,typePrefix:e})}return e.withTypes=function(){return e},e}();function Iu(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}Object.assign;var _u="listenerMiddleware";xu(_u+"/add"),xu(_u+"/removeAll"),xu(_u+"/remove");"function"===typeof queueMicrotask&&queueMicrotask.bind("undefined"!==typeof window?window:"undefined"!==typeof n.g?n.g:globalThis);var Du,Fu=function(e){return function(t){setTimeout(t,e)}};"undefined"!==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:Fu(10);function Mu(e,t){return function(){return e.apply(t,arguments)}}Ol();const{toString:zu}=Object.prototype,{getPrototypeOf:Bu}=Object,{iterator:Uu,toStringTag:Hu}=Symbol,Vu=(Wu=Object.create(null),e=>{const t=zu.call(e);return Wu[t]||(Wu[t]=t.slice(8,-1).toLowerCase())});var Wu;const $u=e=>(e=e.toLowerCase(),t=>Vu(t)===e),Ku=e=>t=>typeof t===e,{isArray:qu}=Array,Qu=Ku("undefined");const Gu=$u("ArrayBuffer");const Ju=Ku("string"),Xu=Ku("function"),Yu=Ku("number"),Zu=e=>null!==e&&"object"===typeof e,ec=e=>{if("object"!==Vu(e))return!1;const t=Bu(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Hu in e)&&!(Uu in e)},tc=$u("Date"),nc=$u("File"),rc=$u("Blob"),ac=$u("FileList"),oc=$u("URLSearchParams"),[ic,sc,lc,uc]=["ReadableStream","Request","Response","Headers"].map($u);function cc(e,t){let n,r,{allOwnKeys:a=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),qu(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const r=a?Object.getOwnPropertyNames(e):Object.keys(e),o=r.length;let i;for(n=0;n<o;n++)i=r[n],t.call(null,e[i],i,e)}}function dc(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,a=n.length;for(;a-- >0;)if(r=n[a],t===r.toLowerCase())return r;return null}const fc="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,pc=e=>!Qu(e)&&e!==fc;const hc=(mc="undefined"!==typeof Uint8Array&&Bu(Uint8Array),e=>mc&&e instanceof mc);var mc;const gc=$u("HTMLFormElement"),yc=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),vc=$u("RegExp"),bc=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};cc(n,((n,a)=>{let o;!1!==(o=t(n,a,e))&&(r[a]=o||n)})),Object.defineProperties(e,r)};const wc=$u("AsyncFunction"),xc=(kc="function"===typeof setImmediate,Sc=Xu(fc.postMessage),kc?setImmediate:Sc?((e,t)=>(fc.addEventListener("message",(n=>{let{source:r,data:a}=n;r===fc&&a===e&&t.length&&t.shift()()}),!1),n=>{t.push(n),fc.postMessage(e,"*")}))("axios@".concat(Math.random()),[]):e=>setTimeout(e));var kc,Sc;const Ec="undefined"!==typeof queueMicrotask?queueMicrotask.bind(fc):"undefined"!==typeof process&&process.nextTick||xc,Nc={isArray:qu,isArrayBuffer:Gu,isBuffer:function(e){return null!==e&&!Qu(e)&&null!==e.constructor&&!Qu(e.constructor)&&Xu(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||Xu(e.append)&&("formdata"===(t=Vu(e))||"object"===t&&Xu(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&Gu(e.buffer),t},isString:Ju,isNumber:Yu,isBoolean:e=>!0===e||!1===e,isObject:Zu,isPlainObject:ec,isReadableStream:ic,isRequest:sc,isResponse:lc,isHeaders:uc,isUndefined:Qu,isDate:tc,isFile:nc,isBlob:rc,isRegExp:vc,isFunction:Xu,isStream:e=>Zu(e)&&Xu(e.pipe),isURLSearchParams:oc,isTypedArray:hc,isFileList:ac,forEach:cc,merge:function e(){const{caseless:t}=pc(this)&&this||{},n={},r=(r,a)=>{const o=t&&dc(n,a)||a;ec(n[o])&&ec(r)?n[o]=e(n[o],r):ec(r)?n[o]=e({},r):qu(r)?n[o]=r.slice():n[o]=r};for(let a=0,o=arguments.length;a<o;a++)arguments[a]&&cc(arguments[a],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return cc(t,((t,r)=>{n&&Xu(t)?e[r]=Mu(t,n):e[r]=t}),{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let a,o,i;const s={};if(t=t||{},null==e)return t;do{for(a=Object.getOwnPropertyNames(e),o=a.length;o-- >0;)i=a[o],r&&!r(i,e,t)||s[i]||(t[i]=e[i],s[i]=!0);e=!1!==n&&Bu(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:Vu,kindOfTest:$u,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(qu(e))return e;let t=e.length;if(!Yu(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[Uu]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:gc,hasOwnProperty:yc,hasOwnProp:yc,reduceDescriptors:bc,freezeMethods:e=>{bc(e,((t,n)=>{if(Xu(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];Xu(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return qu(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:dc,global:fc,isContextDefined:pc,isSpecCompliantForm:function(e){return!!(e&&Xu(e.append)&&"FormData"===e[Hu]&&e[Uu])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(Zu(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const a=qu(e)?[]:{};return cc(e,((e,t)=>{const o=n(e,r+1);!Qu(o)&&(a[t]=o)})),t[r]=void 0,a}}return e};return n(e,0)},isAsyncFn:wc,isThenable:e=>e&&(Zu(e)||Xu(e))&&Xu(e.then)&&Xu(e.catch),setImmediate:xc,asap:Ec,isIterable:e=>null!=e&&Xu(e[Uu])};function Cc(e,t,n,r,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),a&&(this.response=a,this.status=a.status?a.status:null)}Nc.inherits(Cc,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Nc.toJSONObject(this.config),code:this.code,status:this.status}}});const jc=Cc.prototype,Oc={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{Oc[e]={value:e}})),Object.defineProperties(Cc,Oc),Object.defineProperty(jc,"isAxiosError",{value:!0}),Cc.from=(e,t,n,r,a,o)=>{const i=Object.create(jc);return Nc.toFlatObject(e,i,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),Cc.call(i,e.message,t,n,r,a),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const Pc=Cc;function Rc(e){return Nc.isPlainObject(e)||Nc.isArray(e)}function Tc(e){return Nc.endsWith(e,"[]")?e.slice(0,-2):e}function Ac(e,t,n){return e?e.concat(t).map((function(e,t){return e=Tc(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const Lc=Nc.toFlatObject(Nc,{},null,(function(e){return/^is[A-Z]/.test(e)}));const Ic=function(e,t,n){if(!Nc.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=Nc.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!Nc.isUndefined(t[e])}))).metaTokens,a=n.visitor||u,o=n.dots,i=n.indexes,s=(n.Blob||"undefined"!==typeof Blob&&Blob)&&Nc.isSpecCompliantForm(t);if(!Nc.isFunction(a))throw new TypeError("visitor must be a function");function l(e){if(null===e)return"";if(Nc.isDate(e))return e.toISOString();if(!s&&Nc.isBlob(e))throw new Pc("Blob is not supported. Use a Buffer instead.");return Nc.isArrayBuffer(e)||Nc.isTypedArray(e)?s&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,a){let s=e;if(e&&!a&&"object"===typeof e)if(Nc.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(Nc.isArray(e)&&function(e){return Nc.isArray(e)&&!e.some(Rc)}(e)||(Nc.isFileList(e)||Nc.endsWith(n,"[]"))&&(s=Nc.toArray(e)))return n=Tc(n),s.forEach((function(e,r){!Nc.isUndefined(e)&&null!==e&&t.append(!0===i?Ac([n],r,o):null===i?n:n+"[]",l(e))})),!1;return!!Rc(e)||(t.append(Ac(a,n,o),l(e)),!1)}const c=[],d=Object.assign(Lc,{defaultVisitor:u,convertValue:l,isVisitable:Rc});if(!Nc.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!Nc.isUndefined(n)){if(-1!==c.indexOf(n))throw Error("Circular reference detected in "+r.join("."));c.push(n),Nc.forEach(n,(function(n,o){!0===(!(Nc.isUndefined(n)||null===n)&&a.call(t,n,Nc.isString(o)?o.trim():o,r,d))&&e(n,r?r.concat(o):[o])})),c.pop()}}(e),t};function _c(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function Dc(e,t){this._pairs=[],e&&Ic(e,this,t)}const Fc=Dc.prototype;Fc.append=function(e,t){this._pairs.push([e,t])},Fc.toString=function(e){const t=e?function(t){return e.call(this,t,_c)}:_c;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};const Mc=Dc;function zc(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Bc(e,t,n){if(!t)return e;const r=n&&n.encode||zc;Nc.isFunction(n)&&(n={serialize:n});const a=n&&n.serialize;let o;if(o=a?a(t,n):Nc.isURLSearchParams(t)?t.toString():new Mc(t,n).toString(r),o){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+o}return e}const Uc=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){Nc.forEach(this.handlers,(function(t){null!==t&&e(t)}))}},Hc={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Vc={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:Mc,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},Wc="undefined"!==typeof window&&"undefined"!==typeof document,$c="object"===typeof navigator&&navigator||void 0,Kc=Wc&&(!$c||["ReactNative","NativeScript","NS"].indexOf($c.product)<0),qc="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,Qc=Wc&&window.location.href||"http://localhost",Gc=oo(oo({},e),Vc);const Jc=function(e){function t(e,n,r,a){let o=e[a++];if("__proto__"===o)return!0;const i=Number.isFinite(+o),s=a>=e.length;if(o=!o&&Nc.isArray(r)?r.length:o,s)return Nc.hasOwnProp(r,o)?r[o]=[r[o],n]:r[o]=n,!i;r[o]&&Nc.isObject(r[o])||(r[o]=[]);return t(e,n,r[o],a)&&Nc.isArray(r[o])&&(r[o]=function(e){const t={},n=Object.keys(e);let r;const a=n.length;let o;for(r=0;r<a;r++)o=n[r],t[o]=e[o];return t}(r[o])),!i}if(Nc.isFormData(e)&&Nc.isFunction(e.entries)){const n={};return Nc.forEachEntry(e,((e,r)=>{t(function(e){return Nc.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null};const Xc={transitional:Hc,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,a=Nc.isObject(e);a&&Nc.isHTMLForm(e)&&(e=new FormData(e));if(Nc.isFormData(e))return r?JSON.stringify(Jc(e)):e;if(Nc.isArrayBuffer(e)||Nc.isBuffer(e)||Nc.isStream(e)||Nc.isFile(e)||Nc.isBlob(e)||Nc.isReadableStream(e))return e;if(Nc.isArrayBufferView(e))return e.buffer;if(Nc.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let o;if(a){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return Ic(e,new Gc.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return Gc.isNode&&Nc.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((o=Nc.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return Ic(o?{"files[]":e}:e,t&&new t,this.formSerializer)}}return a||r?(t.setContentType("application/json",!1),function(e,t,n){if(Nc.isString(e))try{return(t||JSON.parse)(e),Nc.trim(e)}catch(_i){if("SyntaxError"!==_i.name)throw _i}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||Xc.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(Nc.isResponse(e)||Nc.isReadableStream(e))return e;if(e&&Nc.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(_i){if(n){if("SyntaxError"===_i.name)throw Pc.from(_i,Pc.ERR_BAD_RESPONSE,this,null,this.response);throw _i}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Gc.classes.FormData,Blob:Gc.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};Nc.forEach(["delete","get","head","post","put","patch"],(e=>{Xc.headers[e]={}}));const Yc=Xc,Zc=Nc.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),ed=Symbol("internals");function td(e){return e&&String(e).trim().toLowerCase()}function nd(e){return!1===e||null==e?e:Nc.isArray(e)?e.map(nd):String(e)}function rd(e,t,n,r,a){return Nc.isFunction(r)?r.call(this,t,n):(a&&(t=n),Nc.isString(t)?Nc.isString(r)?-1!==t.indexOf(r):Nc.isRegExp(r)?r.test(t):void 0:void 0)}class ad{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function a(e,t,n){const a=td(t);if(!a)throw new Error("header name must be a non-empty string");const o=Nc.findKey(r,a);(!o||void 0===r[o]||!0===n||void 0===n&&!1!==r[o])&&(r[o||t]=nd(e))}const o=(e,t)=>Nc.forEach(e,((e,n)=>a(e,n,t)));if(Nc.isPlainObject(e)||e instanceof this.constructor)o(e,t);else if(Nc.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))o((e=>{const t={};let n,r,a;return e&&e.split("\n").forEach((function(e){a=e.indexOf(":"),n=e.substring(0,a).trim().toLowerCase(),r=e.substring(a+1).trim(),!n||t[n]&&Zc[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t);else if(Nc.isObject(e)&&Nc.isIterable(e)){let n,r,a={};for(const t of e){if(!Nc.isArray(t))throw TypeError("Object iterator must return a key-value pair");a[r=t[0]]=(n=a[r])?Nc.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}o(a,t)}else null!=e&&a(t,e,n);return this}get(e,t){if(e=td(e)){const n=Nc.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(Nc.isFunction(t))return t.call(this,e,n);if(Nc.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=td(e)){const n=Nc.findKey(this,e);return!(!n||void 0===this[n]||t&&!rd(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function a(e){if(e=td(e)){const a=Nc.findKey(n,e);!a||t&&!rd(0,n[a],a,t)||(delete n[a],r=!0)}}return Nc.isArray(e)?e.forEach(a):a(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const a=t[n];e&&!rd(0,this[a],a,e,!0)||(delete this[a],r=!0)}return r}normalize(e){const t=this,n={};return Nc.forEach(this,((r,a)=>{const o=Nc.findKey(n,a);if(o)return t[o]=nd(r),void delete t[a];const i=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(a):String(a).trim();i!==a&&delete t[a],t[i]=nd(r),n[i]=!0})),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return Nc.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&Nc.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((e=>{let[t,n]=e;return t+": "+n})).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];return r.forEach((e=>t.set(e))),t}static accessor(e){const t=(this[ed]=this[ed]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=td(e);t[r]||(!function(e,t){const n=Nc.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,a){return this[r].call(this,t,e,n,a)},configurable:!0})}))}(n,e),t[r]=!0)}return Nc.isArray(e)?e.forEach(r):r(e),this}}ad.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),Nc.reduceDescriptors(ad.prototype,((e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}})),Nc.freezeMethods(ad);const od=ad;function id(e,t){const n=this||Yc,r=t||n,a=od.from(r.headers);let o=r.data;return Nc.forEach(e,(function(e){o=e.call(n,o,a.normalize(),t?t.status:void 0)})),a.normalize(),o}function sd(e){return!(!e||!e.__CANCEL__)}function ld(e,t,n){Pc.call(this,null==e?"canceled":e,Pc.ERR_CANCELED,t,n),this.name="CanceledError"}Nc.inherits(ld,Pc,{__CANCEL__:!0});const ud=ld;function cd(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new Pc("Request failed with status code "+n.status,[Pc.ERR_BAD_REQUEST,Pc.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const dd=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let a,o=0,i=0;return t=void 0!==t?t:1e3,function(s){const l=Date.now(),u=r[i];a||(a=l),n[o]=s,r[o]=l;let c=i,d=0;for(;c!==o;)d+=n[c++],c%=e;if(o=(o+1)%e,o===i&&(i=(i+1)%e),l-a<t)return;const f=u&&l-u;return f?Math.round(1e3*d/f):void 0}};const fd=function(e,t){let n,r,a=0,o=1e3/t;const i=function(t){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();a=o,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[function(){const e=Date.now(),t=e-a;for(var s=arguments.length,l=new Array(s),u=0;u<s;u++)l[u]=arguments[u];t>=o?i(l,e):(n=l,r||(r=setTimeout((()=>{r=null,i(n)}),o-t)))},()=>n&&i(n)]},pd=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const a=dd(50,250);return fd((n=>{const o=n.loaded,i=n.lengthComputable?n.total:void 0,s=o-r,l=a(s);r=o;e({loaded:o,total:i,progress:i?o/i:void 0,bytes:s,rate:l||void 0,estimated:l&&i&&o<=i?(i-o)/l:void 0,event:n,lengthComputable:null!=i,[t?"download":"upload"]:!0})}),n)},hd=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},md=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return Nc.asap((()=>e(...n)))},gd=Gc.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,Gc.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(Gc.origin),Gc.navigator&&/(msie|trident)/i.test(Gc.navigator.userAgent)):()=>!0,yd=Gc.hasStandardBrowserEnv?{write(e,t,n,r,a,o){const i=[e+"="+encodeURIComponent(t)];Nc.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),Nc.isString(r)&&i.push("path="+r),Nc.isString(a)&&i.push("domain="+a),!0===o&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function vd(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const bd=e=>e instanceof od?oo({},e):e;function wd(e,t){t=t||{};const n={};function r(e,t,n,r){return Nc.isPlainObject(e)&&Nc.isPlainObject(t)?Nc.merge.call({caseless:r},e,t):Nc.isPlainObject(t)?Nc.merge({},t):Nc.isArray(t)?t.slice():t}function a(e,t,n,a){return Nc.isUndefined(t)?Nc.isUndefined(e)?void 0:r(void 0,e,0,a):r(e,t,0,a)}function o(e,t){if(!Nc.isUndefined(t))return r(void 0,t)}function i(e,t){return Nc.isUndefined(t)?Nc.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function s(n,a,o){return o in t?r(n,a):o in e?r(void 0,n):void 0}const l={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:s,headers:(e,t,n)=>a(bd(e),bd(t),0,!0)};return Nc.forEach(Object.keys(Object.assign({},e,t)),(function(r){const o=l[r]||a,i=o(e[r],t[r],r);Nc.isUndefined(i)&&o!==s||(n[r]=i)})),n}const xd=e=>{const t=wd({},e);let n,{data:r,withXSRFToken:a,xsrfHeaderName:o,xsrfCookieName:i,headers:s,auth:l}=t;if(t.headers=s=od.from(s),t.url=Bc(vd(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&s.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):""))),Nc.isFormData(r))if(Gc.hasStandardBrowserEnv||Gc.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if(!1!==(n=s.getContentType())){const[e,...t]=n?n.split(";").map((e=>e.trim())).filter(Boolean):[];s.setContentType([e||"multipart/form-data",...t].join("; "))}if(Gc.hasStandardBrowserEnv&&(a&&Nc.isFunction(a)&&(a=a(t)),a||!1!==a&&gd(t.url))){const e=o&&i&&yd.read(i);e&&s.set(o,e)}return t},kd="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){const r=xd(e);let a=r.data;const o=od.from(r.headers).normalize();let i,s,l,u,c,{responseType:d,onUploadProgress:f,onDownloadProgress:p}=r;function h(){u&&u(),c&&c(),r.cancelToken&&r.cancelToken.unsubscribe(i),r.signal&&r.signal.removeEventListener("abort",i)}let m=new XMLHttpRequest;function g(){if(!m)return;const r=od.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders());cd((function(e){t(e),h()}),(function(e){n(e),h()}),{data:d&&"text"!==d&&"json"!==d?m.response:m.responseText,status:m.status,statusText:m.statusText,headers:r,config:e,request:m}),m=null}m.open(r.method.toUpperCase(),r.url,!0),m.timeout=r.timeout,"onloadend"in m?m.onloadend=g:m.onreadystatechange=function(){m&&4===m.readyState&&(0!==m.status||m.responseURL&&0===m.responseURL.indexOf("file:"))&&setTimeout(g)},m.onabort=function(){m&&(n(new Pc("Request aborted",Pc.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new Pc("Network Error",Pc.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const a=r.transitional||Hc;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new Pc(t,a.clarifyTimeoutError?Pc.ETIMEDOUT:Pc.ECONNABORTED,e,m)),m=null},void 0===a&&o.setContentType(null),"setRequestHeader"in m&&Nc.forEach(o.toJSON(),(function(e,t){m.setRequestHeader(t,e)})),Nc.isUndefined(r.withCredentials)||(m.withCredentials=!!r.withCredentials),d&&"json"!==d&&(m.responseType=r.responseType),p&&([l,c]=pd(p,!0),m.addEventListener("progress",l)),f&&m.upload&&([s,u]=pd(f),m.upload.addEventListener("progress",s),m.upload.addEventListener("loadend",u)),(r.cancelToken||r.signal)&&(i=t=>{m&&(n(!t||t.type?new ud(null,e,m):t),m.abort(),m=null)},r.cancelToken&&r.cancelToken.subscribe(i),r.signal&&(r.signal.aborted?i():r.signal.addEventListener("abort",i)));const y=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);y&&-1===Gc.protocols.indexOf(y)?n(new Pc("Unsupported protocol "+y+":",Pc.ERR_BAD_REQUEST,e)):m.send(a||null)}))},Sd=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const a=function(e){if(!n){n=!0,i();const t=e instanceof Error?e:this.reason;r.abort(t instanceof Pc?t:new ud(t instanceof Error?t.message:t))}};let o=t&&setTimeout((()=>{o=null,a(new Pc("timeout ".concat(t," of ms exceeded"),Pc.ETIMEDOUT))}),t);const i=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(a):e.removeEventListener("abort",a)})),e=null)};e.forEach((e=>e.addEventListener("abort",a)));const{signal:s}=r;return s.unsubscribe=()=>Nc.asap(i),s}};function Ed(e,t){this.v=e,this.k=t}function Nd(e){return function(){return new Cd(e.apply(this,arguments))}}function Cd(e){var t,n;function r(t,n){try{var o=e[t](n),i=o.value,s=i instanceof Ed;Promise.resolve(s?i.v:i).then((function(n){if(s){var l="return"===t?"return":"next";if(!i.k||n.done)return r(l,n);n=e[l](n).value}a(o.done?"return":"normal",n)}),(function(e){r("throw",e)}))}catch(e){a("throw",e)}}function a(e,a){switch(e){case"return":t.resolve({value:a,done:!0});break;case"throw":t.reject(a);break;default:t.resolve({value:a,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,a){return new Promise((function(o,i){var s={key:e,arg:a,resolve:o,reject:i,next:null};n?n=n.next=s:(t=n=s,r(e,a))}))},"function"!=typeof e.return&&(this.return=void 0)}function jd(e){return new Ed(e,0)}function Od(e){var t={},n=!1;function r(t,r){return n=!0,r=new Promise((function(n){n(e[t](r))})),{done:!1,value:new Ed(r,1)}}return t["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},t.next=function(e){return n?(n=!1,e):r("next",e)},"function"==typeof e.throw&&(t.throw=function(e){if(n)throw n=!1,e;return r("throw",e)}),"function"==typeof e.return&&(t.return=function(e){return n?(n=!1,e):r("return",e)}),t}function Pd(e){var t,n,r,a=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);a--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new Rd(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function Rd(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then((function(e){return{value:e,done:t}}))}return Rd=function(e){this.s=e,this.n=e.next},Rd.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new Rd(e)}Cd.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},Cd.prototype.next=function(e){return this._invoke("next",e)},Cd.prototype.throw=function(e){return this._invoke("throw",e)},Cd.prototype.return=function(e){return this._invoke("return",e)};const Td=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,a=0;for(;a<n;)r=a+t,yield e.slice(a,r),a=r},Ad=function(){var e=Nd((function*(e,t){var n,r=!1,a=!1;try{for(var o,i=Pd(Ld(e));r=!(o=yield jd(i.next())).done;r=!1){const e=o.value;yield*Od(Pd(Td(e,t)))}}catch(s){a=!0,n=s}finally{try{r&&null!=i.return&&(yield jd(i.return()))}finally{if(a)throw n}}}));return function(t,n){return e.apply(this,arguments)}}(),Ld=function(){var e=Nd((function*(e){if(e[Symbol.asyncIterator])return void(yield*Od(Pd(e)));const t=e.getReader();try{for(;;){const{done:e,value:n}=yield jd(t.read());if(e)break;yield n}}finally{yield jd(t.cancel())}}));return function(t){return e.apply(this,arguments)}}(),Id=(e,t,n,r)=>{const a=Ad(e,t);let o,i=0,s=e=>{o||(o=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await a.next();if(t)return s(),void e.close();let o=r.byteLength;if(n){let e=i+=o;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw s(t),t}},cancel:e=>(s(e),a.return())},{highWaterMark:2})},_d="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,Dd=_d&&"function"===typeof ReadableStream,Fd=_d&&("function"===typeof TextEncoder?(Md=new TextEncoder,e=>Md.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var Md;const zd=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(_i){return!1}},Bd=Dd&&zd((()=>{let e=!1;const t=new Request(Gc.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})),Ud=Dd&&zd((()=>Nc.isReadableStream(new Response("").body))),Hd={stream:Ud&&(e=>e.body)};var Vd;_d&&(Vd=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!Hd[e]&&(Hd[e]=Nc.isFunction(Vd[e])?t=>t[e]():(t,n)=>{throw new Pc("Response type '".concat(e,"' is not supported"),Pc.ERR_NOT_SUPPORT,n)})})));const Wd=async(e,t)=>{const n=Nc.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(Nc.isBlob(e))return e.size;if(Nc.isSpecCompliantForm(e)){const t=new Request(Gc.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return Nc.isArrayBufferView(e)||Nc.isArrayBuffer(e)?e.byteLength:(Nc.isURLSearchParams(e)&&(e+=""),Nc.isString(e)?(await Fd(e)).byteLength:void 0)})(t):n},$d=_d&&(async e=>{let{url:t,method:n,data:r,signal:a,cancelToken:o,timeout:i,onDownloadProgress:s,onUploadProgress:l,responseType:u,headers:c,withCredentials:d="same-origin",fetchOptions:f}=xd(e);u=u?(u+"").toLowerCase():"text";let p,h=Sd([a,o&&o.toAbortSignal()],i);const m=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let g;try{if(l&&Bd&&"get"!==n&&"head"!==n&&0!==(g=await Wd(c,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(Nc.isFormData(r)&&(e=n.headers.get("content-type"))&&c.setContentType(e),n.body){const[e,t]=hd(g,pd(md(l)));r=Id(n.body,65536,e,t)}}Nc.isString(d)||(d=d?"include":"omit");const a="credentials"in Request.prototype;p=new Request(t,oo(oo({},f),{},{signal:h,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:a?d:void 0}));let o=await fetch(p);const i=Ud&&("stream"===u||"response"===u);if(Ud&&(s||i&&m)){const e={};["status","statusText","headers"].forEach((t=>{e[t]=o[t]}));const t=Nc.toFiniteNumber(o.headers.get("content-length")),[n,r]=s&&hd(t,pd(md(s),!0))||[];o=new Response(Id(o.body,65536,n,(()=>{r&&r(),m&&m()})),e)}u=u||"text";let y=await Hd[Nc.findKey(Hd,u)||"text"](o,e);return!i&&m&&m(),await new Promise(((t,n)=>{cd(t,n,{data:y,headers:od.from(o.headers),status:o.status,statusText:o.statusText,config:e,request:p})}))}catch(y){if(m&&m(),y&&"TypeError"===y.name&&/Load failed|fetch/i.test(y.message))throw Object.assign(new Pc("Network Error",Pc.ERR_NETWORK,e,p),{cause:y.cause||y});throw Pc.from(y,y&&y.code,e,p)}}),Kd={http:null,xhr:kd,fetch:$d};Nc.forEach(Kd,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(_i){}Object.defineProperty(e,"adapterName",{value:t})}}));const qd=e=>"- ".concat(e),Qd=e=>Nc.isFunction(e)||null===e||!1===e,Gd=e=>{e=Nc.isArray(e)?e:[e];const{length:t}=e;let n,r;const a={};for(let o=0;o<t;o++){let t;if(n=e[o],r=n,!Qd(n)&&(r=Kd[(t=String(n)).toLowerCase()],void 0===r))throw new Pc("Unknown adapter '".concat(t,"'"));if(r)break;a[t||"#"+o]=r}if(!r){const e=Object.entries(a).map((e=>{let[t,n]=e;return"adapter ".concat(t," ")+(!1===n?"is not supported by the environment":"is not available in the build")}));let n=t?e.length>1?"since :\n"+e.map(qd).join("\n"):" "+qd(e[0]):"as no adapter specified";throw new Pc("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function Jd(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new ud(null,e)}function Xd(e){Jd(e),e.headers=od.from(e.headers),e.data=id.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return Gd(e.adapter||Yc.adapter)(e).then((function(t){return Jd(e),t.data=id.call(e,e.transformResponse,t),t.headers=od.from(t.headers),t}),(function(t){return sd(t)||(Jd(e),t&&t.response&&(t.response.data=id.call(e,e.transformResponse,t.response),t.response.headers=od.from(t.response.headers))),Promise.reject(t)}))}const Yd="1.9.0",Zd={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{Zd[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const ef={};Zd.transitional=function(e,t,n){function r(e,t){return"[Axios v1.9.0] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,a,o)=>{if(!1===e)throw new Pc(r(a," has been removed"+(t?" in "+t:"")),Pc.ERR_DEPRECATED);return t&&!ef[a]&&(ef[a]=!0,console.warn(r(a," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,a,o)}},Zd.spelling=function(e){return(t,n)=>(console.warn("".concat(n," is likely a misspelling of ").concat(e)),!0)};const tf={assertOptions:function(e,t,n){if("object"!==typeof e)throw new Pc("options must be an object",Pc.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let a=r.length;for(;a-- >0;){const o=r[a],i=t[o];if(i){const t=e[o],n=void 0===t||i(t,o,e);if(!0!==n)throw new Pc("option "+o+" must be "+n,Pc.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new Pc("Unknown option "+o,Pc.ERR_BAD_OPTION)}},validators:Zd},nf=tf.validators;class rf{constructor(e){this.defaults=e||{},this.interceptors={request:new Uc,response:new Uc}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(_i){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=wd(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:a}=t;void 0!==n&&tf.assertOptions(n,{silentJSONParsing:nf.transitional(nf.boolean),forcedJSONParsing:nf.transitional(nf.boolean),clarifyTimeoutError:nf.transitional(nf.boolean)},!1),null!=r&&(Nc.isFunction(r)?t.paramsSerializer={serialize:r}:tf.assertOptions(r,{encode:nf.function,serialize:nf.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),tf.assertOptions(t,{baseUrl:nf.spelling("baseURL"),withXsrfToken:nf.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let o=a&&Nc.merge(a.common,a[t.method]);a&&Nc.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete a[e]})),t.headers=od.concat(o,a);const i=[];let s=!0;this.interceptors.request.forEach((function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(s=s&&e.synchronous,i.unshift(e.fulfilled,e.rejected))}));const l=[];let u;this.interceptors.response.forEach((function(e){l.push(e.fulfilled,e.rejected)}));let c,d=0;if(!s){const e=[Xd.bind(this),void 0];for(e.unshift.apply(e,i),e.push.apply(e,l),c=e.length,u=Promise.resolve(t);d<c;)u=u.then(e[d++],e[d++]);return u}c=i.length;let f=t;for(d=0;d<c;){const e=i[d++],t=i[d++];try{f=e(f)}catch(p){t.call(this,p);break}}try{u=Xd.call(this,f)}catch(p){return Promise.reject(p)}for(d=0,c=l.length;d<c;)u=u.then(l[d++],l[d++]);return u}getUri(e){return Bc(vd((e=wd(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}Nc.forEach(["delete","get","head","options"],(function(e){rf.prototype[e]=function(t,n){return this.request(wd(n||{},{method:e,url:t,data:(n||{}).data}))}})),Nc.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,a){return this.request(wd(a||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}rf.prototype[e]=t(),rf.prototype[e+"Form"]=t(!0)}));const af=rf;class of{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,a){n.reason||(n.reason=new ud(e,r,a),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;const t=new of((function(t){e=t}));return{token:t,cancel:e}}}const sf=of;const lf={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(lf).forEach((e=>{let[t,n]=e;lf[n]=t}));const uf=lf;const cf=function e(t){const n=new af(t),r=Mu(af.prototype.request,n);return Nc.extend(r,af.prototype,n,{allOwnKeys:!0}),Nc.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(wd(t,n))},r}(Yc);cf.Axios=af,cf.CanceledError=ud,cf.CancelToken=sf,cf.isCancel=sd,cf.VERSION=Yd,cf.toFormData=Ic,cf.AxiosError=Pc,cf.Cancel=cf.CanceledError,cf.all=function(e){return Promise.all(e)},cf.spread=function(e){return function(t){return e.apply(null,t)}},cf.isAxiosError=function(e){return Nc.isObject(e)&&!0===e.isAxiosError},cf.mergeConfig=wd,cf.AxiosHeaders=od,cf.formToJSON=e=>Jc(Nc.isHTMLForm(e)?new FormData(e):e),cf.getAdapter=Gd,cf.HttpStatusCode=uf,cf.default=cf;const df=cf,ff={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_API_URL||"http://localhost:3001/api",pf=df.create({baseURL:ff,headers:{"Content-Type":"application/json"}});pf.interceptors.request.use((e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e}),(e=>Promise.reject(e))),pf.interceptors.response.use((e=>e),(async e=>{var t;const n=e.config;if(401===(null===(t=e.response)||void 0===t?void 0:t.status)&&!n._retry){n._retry=!0;try{const e=localStorage.getItem("refreshToken");if(e){const t=await df.post("".concat(ff,"/auth/refresh-token"),{refreshToken:e}),{token:r,refreshToken:a}=t.data.data;return localStorage.setItem("token",r),localStorage.setItem("refreshToken",a),n.headers.Authorization="Bearer ".concat(r),pf(n)}}catch(r){return localStorage.removeItem("token"),localStorage.removeItem("refreshToken"),window.location.href="/login",Promise.reject(r)}}return Promise.reject(e)}));const hf=async e=>(await pf.post("/auth/login",e)).data,mf=async e=>(await pf.post("/auth/register",e)).data,gf=async()=>(await pf.get("/auth/me")).data,yf=async e=>(await pf.post("/auth/refresh-token",{refreshToken:e})).data,vf=async e=>(await pf.post("/auth/logout",{refreshToken:e})).data,bf=async e=>(await pf.post("/auth/change-password",e)).data,wf={user:null,token:localStorage.getItem("token"),refreshToken:localStorage.getItem("refreshToken"),isAuthenticated:!!localStorage.getItem("token"),isLoading:!1,error:null},xf=Lu("auth/login",(async(e,t)=>{let{rejectWithValue:n}=t;try{const t=await hf(e);return localStorage.setItem("token",t.data.token),localStorage.setItem("refreshToken",t.data.refreshToken),t.data}catch(o){var r,a;return n((null===(r=o.response)||void 0===r||null===(a=r.data)||void 0===a?void 0:a.message)||"Login failed")}})),kf=Lu("auth/register",(async(e,t)=>{let{rejectWithValue:n}=t;try{const t=await mf(e);return localStorage.setItem("token",t.data.token),localStorage.setItem("refreshToken",t.data.refreshToken),t.data}catch(o){var r,a;return n((null===(r=o.response)||void 0===r||null===(a=r.data)||void 0===a?void 0:a.message)||"Registration failed")}})),Sf=Lu("auth/getCurrentUser",(async(e,t)=>{let{rejectWithValue:n}=t;try{return(await gf()).data}catch(o){var r,a;return n((null===(r=o.response)||void 0===r||null===(a=r.data)||void 0===a?void 0:a.message)||"Failed to get user")}})),Ef=Lu("auth/refreshToken",(async(e,t)=>{let{getState:n,rejectWithValue:r}=t;try{const e=n().auth.refreshToken;if(!e)throw new Error("No refresh token available");const t=await yf(e);return localStorage.setItem("token",t.data.token),localStorage.setItem("refreshToken",t.data.refreshToken),t.data}catch(i){var a,o;return r((null===(a=i.response)||void 0===a||null===(o=a.data)||void 0===o?void 0:o.message)||"Token refresh failed")}})),Nf=Lu("auth/logout",(async(e,t)=>{let{getState:n,rejectWithValue:r}=t;try{const e=n().auth.refreshToken;return e&&await vf(e),localStorage.removeItem("token"),localStorage.removeItem("refreshToken"),null}catch(a){return localStorage.removeItem("token"),localStorage.removeItem("refreshToken"),null}})),Cf=Lu("auth/changePassword",(async(e,t)=>{let{rejectWithValue:n}=t;try{return await bf(e)}catch(o){var r,a;return n((null===(r=o.response)||void 0===r||null===(a=r.data)||void 0===a?void 0:a.message)||"Password change failed")}})),jf=ju({name:"auth",initialState:wf,reducers:{clearError:e=>{e.error=null},setCredentials:(e,t)=>{e.user=t.payload.user,e.token=t.payload.token,e.refreshToken=t.payload.refreshToken,e.isAuthenticated=!0,e.error=null},updateUser:(e,t)=>{e.user&&(e.user=oo(oo({},e.user),t.payload))}},extraReducers:e=>{e.addCase(xf.pending,(e=>{e.isLoading=!0,e.error=null})).addCase(xf.fulfilled,((e,t)=>{e.isLoading=!1,e.user=t.payload.user,e.token=t.payload.token,e.refreshToken=t.payload.refreshToken,e.isAuthenticated=!0,e.error=null})).addCase(xf.rejected,((e,t)=>{e.isLoading=!1,e.error=t.payload,e.isAuthenticated=!1})).addCase(kf.pending,(e=>{e.isLoading=!0,e.error=null})).addCase(kf.fulfilled,((e,t)=>{e.isLoading=!1,e.user=t.payload.user,e.token=t.payload.token,e.refreshToken=t.payload.refreshToken,e.isAuthenticated=!0,e.error=null})).addCase(kf.rejected,((e,t)=>{e.isLoading=!1,e.error=t.payload,e.isAuthenticated=!1})).addCase(Sf.pending,(e=>{e.isLoading=!0})).addCase(Sf.fulfilled,((e,t)=>{e.isLoading=!1,e.user=t.payload.user,e.isAuthenticated=!0,e.error=null})).addCase(Sf.rejected,((e,t)=>{e.isLoading=!1,e.error=t.payload,e.isAuthenticated=!1,e.user=null,e.token=null,e.refreshToken=null,localStorage.removeItem("token"),localStorage.removeItem("refreshToken")})).addCase(Ef.fulfilled,((e,t)=>{e.token=t.payload.token,e.refreshToken=t.payload.refreshToken,e.error=null})).addCase(Ef.rejected,(e=>{e.isAuthenticated=!1,e.user=null,e.token=null,e.refreshToken=null,localStorage.removeItem("token"),localStorage.removeItem("refreshToken")})).addCase(Nf.fulfilled,(e=>{e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1,e.error=null})).addCase(Cf.pending,(e=>{e.isLoading=!0,e.error=null})).addCase(Cf.fulfilled,(e=>{e.isLoading=!1,e.error=null,e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1,localStorage.removeItem("token"),localStorage.removeItem("refreshToken")})).addCase(Cf.rejected,((e,t)=>{e.isLoading=!1,e.error=t.payload}))}}),{clearError:Of,setCredentials:Pf,updateUser:Rf}=jf.actions,Tf=jf.reducer,Af=ju({name:"ui",initialState:{sidebarOpen:!0,theme:"light",language:localStorage.getItem("language")||"en",notifications:[],loading:{global:!1},modals:{},breadcrumbs:[],pageTitle:"Dashboard"},reducers:{toggleSidebar:e=>{e.sidebarOpen=!e.sidebarOpen},setSidebarOpen:(e,t)=>{e.sidebarOpen=t.payload},setTheme:(e,t)=>{e.theme=t.payload,localStorage.setItem("theme",t.payload)},setLanguage:(e,t)=>{e.language=t.payload,localStorage.setItem("language",t.payload)},addNotification:(e,t)=>{const n=oo(oo({},t.payload),{},{id:Date.now().toString(),timestamp:(new Date).toISOString(),read:!1});e.notifications.unshift(n),e.notifications.length>50&&(e.notifications=e.notifications.slice(0,50))},removeNotification:(e,t)=>{e.notifications=e.notifications.filter((e=>e.id!==t.payload))},markNotificationAsRead:(e,t)=>{const n=e.notifications.find((e=>e.id===t.payload));n&&(n.read=!0)},markAllNotificationsAsRead:e=>{e.notifications.forEach((e=>e.read=!0))},clearNotifications:e=>{e.notifications=[]},setLoading:(e,t)=>{e.loading[t.payload.key]=t.payload.loading},setGlobalLoading:(e,t)=>{e.loading.global=t.payload},openModal:(e,t)=>{e.modals[t.payload]=!0},closeModal:(e,t)=>{e.modals[t.payload]=!1},toggleModal:(e,t)=>{e.modals[t.payload]=!e.modals[t.payload]},setBreadcrumbs:(e,t)=>{e.breadcrumbs=t.payload},setPageTitle:(e,t)=>{e.pageTitle=t.payload,document.title="".concat(t.payload," - AMPD Livestock Management")}}}),{toggleSidebar:Lf,setSidebarOpen:If,setTheme:_f,setLanguage:Df,addNotification:Ff,removeNotification:Mf,markNotificationAsRead:zf,markAllNotificationsAsRead:Bf,clearNotifications:Uf,setLoading:Hf,setGlobalLoading:Vf,openModal:Wf,closeModal:$f,toggleModal:Kf,setBreadcrumbs:qf,setPageTitle:Qf}=Af.actions,Gf=Af.reducer;function Jf(e,t){if(null==e)return{};var n,r,a=lt(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(r=0;r<o.length;r++)n=o[r],-1===t.indexOf(n)&&{}.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}function Xf(){let e=[],t={addEventListener:(e,n,r,a)=>(e.addEventListener(n,r,a),t.add((()=>e.removeEventListener(n,r,a)))),requestAnimationFrame(){let e=requestAnimationFrame(...arguments);return t.add((()=>cancelAnimationFrame(e)))},nextFrame(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return t.requestAnimationFrame((()=>t.requestAnimationFrame(...n)))},setTimeout(){let e=setTimeout(...arguments);return t.add((()=>clearTimeout(e)))},microTask(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];let a={current:!0};return function(e){"function"==typeof queueMicrotask?queueMicrotask(e):Promise.resolve().then(e).catch((e=>setTimeout((()=>{throw e}))))}((()=>{a.current&&n[0]()})),t.add((()=>{a.current=!1}))},style(e,t,n){let r=e.style.getPropertyValue(t);return Object.assign(e.style,{[t]:n}),this.add((()=>{Object.assign(e.style,{[t]:r})}))},group(e){let t=Xf();return e(t),this.add((()=>t.dispose()))},add:t=>(e.push(t),()=>{let n=e.indexOf(t);if(n>=0)for(let t of e.splice(n,1))t()}),dispose(){for(let t of e.splice(0))t()}};return t}function Yf(){let[e]=(0,t.useState)(Xf);return(0,t.useEffect)((()=>()=>e.dispose()),[e]),e}var Zf=Object.defineProperty,ep=(e,t,n)=>(((e,t,n)=>{t in e?Zf(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n})(e,"symbol"!=typeof t?t+"":t,n),n);let tp=new class{constructor(){ep(this,"current",this.detect()),ep(this,"handoffState","pending"),ep(this,"currentId",0)}set(e){this.current!==e&&(this.handoffState="pending",this.currentId=0,this.current=e)}reset(){this.set(this.detect())}nextId(){return++this.currentId}get isServer(){return"server"===this.current}get isClient(){return"client"===this.current}detect(){return"undefined"==typeof window||"undefined"==typeof document?"server":"client"}handoff(){"pending"===this.handoffState&&(this.handoffState="complete")}get isHandoffComplete(){return"complete"===this.handoffState}},np=(e,n)=>{tp.isServer?(0,t.useEffect)(e,n):(0,t.useLayoutEffect)(e,n)};function rp(e){let n=(0,t.useRef)(e);return np((()=>{n.current=e}),[e]),n}let ap=function(e){let n=rp(e);return t.useCallback((function(){return n.current(...arguments)}),[n])};function op(){let e=function(){let e="undefined"==typeof document;return(e=>e.useSyncExternalStore)(r)((()=>()=>{}),(()=>!1),(()=>!e))}(),[n,a]=t.useState(tp.isHandoffComplete);return n&&!1===tp.isHandoffComplete&&a(!1),t.useEffect((()=>{!0!==n&&a(!0)}),[n]),t.useEffect((()=>tp.handoff()),[]),!e&&n}var ip;let sp=null!=(ip=t.useId)?ip:function(){let e=op(),[n,r]=t.useState(e?()=>tp.nextId():null);return np((()=>{null===n&&r(tp.nextId())}),[n]),null!=n?""+n:void 0};function lp(e,t){if(e in t){let o=t[e];for(var n=arguments.length,r=new Array(n>2?n-2:0),a=2;a<n;a++)r[a-2]=arguments[a];return"function"==typeof o?o(...r):o}let o=new Error('Tried to handle "'.concat(e,'" but there is no handler defined. Only defined handlers are: ').concat(Object.keys(t).map((e=>'"'.concat(e,'"'))).join(", "),"."));throw Error.captureStackTrace&&Error.captureStackTrace(o,lp),o}function up(e){return tp.isServer?null:e instanceof Node?e.ownerDocument:null!=e&&e.hasOwnProperty("current")&&e.current instanceof Node?e.current.ownerDocument:document}let cp=["[contentEditable=true]","[tabindex]","a[href]","area[href]","button:not([disabled])","iframe","input:not([disabled])","select:not([disabled])","textarea:not([disabled])"].map((e=>"".concat(e,":not([tabindex='-1'])"))).join(",");var dp=(e=>(e[e.First=1]="First",e[e.Previous=2]="Previous",e[e.Next=4]="Next",e[e.Last=8]="Last",e[e.WrapAround=16]="WrapAround",e[e.NoScroll=32]="NoScroll",e))(dp||{}),fp=(e=>(e[e.Error=0]="Error",e[e.Overflow=1]="Overflow",e[e.Success=2]="Success",e[e.Underflow=3]="Underflow",e))(fp||{}),pp=(e=>(e[e.Previous=-1]="Previous",e[e.Next=1]="Next",e))(pp||{});function hp(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:document.body;return null==e?[]:Array.from(e.querySelectorAll(cp)).sort(((e,t)=>Math.sign((e.tabIndex||Number.MAX_SAFE_INTEGER)-(t.tabIndex||Number.MAX_SAFE_INTEGER))))}var mp=(e=>(e[e.Strict=0]="Strict",e[e.Loose=1]="Loose",e))(mp||{});function gp(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0;var n;return e!==(null==(n=up(e))?void 0:n.body)&&lp(t,{0:()=>e.matches(cp),1(){let t=e;for(;null!==t;){if(t.matches(cp))return!0;t=t.parentElement}return!1}})}function yp(e){let t=up(e);Xf().nextFrame((()=>{t&&!gp(t.activeElement,0)&&function(e){null==e||e.focus({preventScroll:!0})}(e)}))}var vp=(e=>(e[e.Keyboard=0]="Keyboard",e[e.Mouse=1]="Mouse",e))(vp||{});"undefined"!=typeof window&&"undefined"!=typeof document&&(document.addEventListener("keydown",(e=>{e.metaKey||e.altKey||e.ctrlKey||(document.documentElement.dataset.headlessuiFocusVisible="")}),!0),document.addEventListener("click",(e=>{1===e.detail?delete document.documentElement.dataset.headlessuiFocusVisible:0===e.detail&&(document.documentElement.dataset.headlessuiFocusVisible="")}),!0));let bp=["textarea","input"].join(",");function wp(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>e;return e.slice().sort(((e,n)=>{let r=t(e),a=t(n);if(null===r||null===a)return 0;let o=r.compareDocumentPosition(a);return o&Node.DOCUMENT_POSITION_FOLLOWING?-1:o&Node.DOCUMENT_POSITION_PRECEDING?1:0}))}function xp(e,t){return function(e,t){let{sorted:n=!0,relativeTo:r=null,skipElements:a=[]}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},o=Array.isArray(e)?e.length>0?e[0].ownerDocument:document:e.ownerDocument,i=Array.isArray(e)?n?wp(e):e:hp(e);a.length>0&&i.length>1&&(i=i.filter((e=>!a.includes(e)))),r=null!=r?r:o.activeElement;let s,l=(()=>{if(5&t)return 1;if(10&t)return-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),u=(()=>{if(1&t)return 0;if(2&t)return Math.max(0,i.indexOf(r))-1;if(4&t)return Math.max(0,i.indexOf(r))+1;if(8&t)return i.length-1;throw new Error("Missing Focus.First, Focus.Previous, Focus.Next or Focus.Last")})(),c=32&t?{preventScroll:!0}:{},d=0,f=i.length;do{if(d>=f||d+f<=0)return 0;let e=u+d;if(16&t)e=(e+f)%f;else{if(e<0)return 3;if(e>=f)return 1}s=i[e],null==s||s.focus(c),d+=l}while(s!==o.activeElement);return 6&t&&function(e){var t,n;return null!=(n=null==(t=null==e?void 0:e.matches)?void 0:t.call(e,bp))&&n}(s)&&s.select(),2}(hp(),t,{relativeTo:e})}function kp(){return/iPhone/gi.test(window.navigator.platform)||/Mac/gi.test(window.navigator.platform)&&window.navigator.maxTouchPoints>0||/Android/gi.test(window.navigator.userAgent)}function Sp(e,n,r){let a=rp(n);(0,t.useEffect)((()=>{function t(e){a.current(e)}return document.addEventListener(e,t,r),()=>document.removeEventListener(e,t,r)}),[e,r])}function Ep(e,n){let r=!(arguments.length>2&&void 0!==arguments[2])||arguments[2],a=(0,t.useRef)(!1);function o(t,r){if(!a.current||t.defaultPrevented)return;let o=r(t);if(null===o||!o.getRootNode().contains(o)||!o.isConnected)return;let i=function e(t){return"function"==typeof t?e(t()):Array.isArray(t)||t instanceof Set?t:[t]}(e);for(let e of i){if(null===e)continue;let n=e instanceof HTMLElement?e:e.current;if(null!=n&&n.contains(o)||t.composed&&t.composedPath().includes(n))return}return!gp(o,mp.Loose)&&-1!==o.tabIndex&&t.preventDefault(),n(t,o)}(0,t.useEffect)((()=>{requestAnimationFrame((()=>{a.current=r}))}),[r]);let i=(0,t.useRef)(null);Sp("pointerdown",(e=>{var t,n;a.current&&(i.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)}),!0),Sp("mousedown",(e=>{var t,n;a.current&&(i.current=(null==(n=null==(t=e.composedPath)?void 0:t.call(e))?void 0:n[0])||e.target)}),!0),Sp("click",(e=>{kp()||i.current&&(o(e,(()=>i.current)),i.current=null)}),!0),Sp("touchend",(e=>o(e,(()=>e.target instanceof HTMLElement?e.target:null))),!0),function(e,n,r){let a=rp(n);(0,t.useEffect)((()=>{function t(e){a.current(e)}return window.addEventListener(e,t,r),()=>window.removeEventListener(e,t,r)}),[e,r])}("blur",(e=>o(e,(()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null))),!0)}function Np(e){var t;if(e.type)return e.type;let n=null!=(t=e.as)?t:"button";return"string"==typeof n&&"button"===n.toLowerCase()?"button":void 0}function Cp(e,n){let[r,a]=(0,t.useState)((()=>Np(e)));return np((()=>{a(Np(e))}),[e.type,e.as]),np((()=>{r||n.current&&n.current instanceof HTMLButtonElement&&!n.current.hasAttribute("type")&&a("button")}),[r,n]),r}let jp=Symbol();function Op(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];let a=(0,t.useRef)(n);(0,t.useEffect)((()=>{a.current=n}),[n]);let o=ap((e=>{for(let t of a.current)null!=t&&("function"==typeof t?t(e):t.current=e)}));return n.every((e=>null==e||(null==e?void 0:e[jp])))?void 0:o}let Pp=/([\u2700-\u27BF]|[\uE000-\uF8FF]|\uD83C[\uDC00-\uDFFF]|\uD83D[\uDC00-\uDFFF]|[\u2011-\u26FF]|\uD83E[\uDD10-\uDDFF])/g;function Rp(e){var t,n;let r=null!=(t=e.innerText)?t:"",a=e.cloneNode(!0);if(!(a instanceof HTMLElement))return r;let o=!1;for(let s of a.querySelectorAll('[hidden],[aria-hidden],[role="img"]'))s.remove(),o=!0;let i=o?null!=(n=a.innerText)?n:"":r;return Pp.test(i)&&(i=i.replace(Pp,"")),i}function Tp(e){let n=(0,t.useRef)(""),r=(0,t.useRef)("");return ap((()=>{let t=e.current;if(!t)return"";let a=t.innerText;if(n.current===a)return r.current;let o=function(e){let t=e.getAttribute("aria-label");if("string"==typeof t)return t.trim();let n=e.getAttribute("aria-labelledby");if(n){let e=n.split(" ").map((e=>{let t=document.getElementById(e);if(t){let e=t.getAttribute("aria-label");return"string"==typeof e?e.trim():Rp(t).trim()}return null})).filter(Boolean);if(e.length>0)return e.join(", ")}return Rp(e).trim()}(t).trim().toLowerCase();return n.current=a,r.current=o,o}))}function Ap(e){return[e.screenX,e.screenY]}let Lp=(0,t.createContext)(null);Lp.displayName="OpenClosedContext";var Ip=(e=>(e[e.Open=1]="Open",e[e.Closed=2]="Closed",e[e.Closing=4]="Closing",e[e.Opening=8]="Opening",e))(Ip||{});function _p(){return(0,t.useContext)(Lp)}function Dp(e){let{value:n,children:r}=e;return t.createElement(Lp.Provider,{value:n},r)}function Fp(e){let t=e.parentElement,n=null;for(;t&&!(t instanceof HTMLFieldSetElement);)t instanceof HTMLLegendElement&&(n=t),t=t.parentElement;let r=""===(null==t?void 0:t.getAttribute("disabled"));return(!r||!function(e){if(!e)return!1;let t=e.previousElementSibling;for(;null!==t;){if(t instanceof HTMLLegendElement)return!1;t=t.previousElementSibling}return!0}(n))&&r}var Mp=(e=>(e[e.First=0]="First",e[e.Previous=1]="Previous",e[e.Next=2]="Next",e[e.Last=3]="Last",e[e.Specific=4]="Specific",e[e.Nothing=5]="Nothing",e))(Mp||{});function zp(e,t){let n=t.resolveItems();if(n.length<=0)return null;let r=t.resolveActiveIndex(),a=null!=r?r:-1;switch(e.focus){case 0:for(let e=0;e<n.length;++e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 1:for(let e=a-1;e>=0;--e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 2:for(let e=a+1;e<n.length;++e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 3:for(let e=n.length-1;e>=0;--e)if(!t.resolveDisabled(n[e],e,n))return e;return r;case 4:for(let r=0;r<n.length;++r)if(t.resolveId(n[r],r,n)===e.id)return r;return r;case 5:return null;default:!function(e){throw new Error("Unexpected object: "+e)}(e)}}function Bp(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Array.from(new Set(t.flatMap((e=>"string"==typeof e?e.split(" "):[])))).filter(Boolean).join(" ")}const Up=["static"],Hp=["unmount"],Vp=["as","children","refName"];var Wp=(e=>(e[e.None=0]="None",e[e.RenderStrategy=1]="RenderStrategy",e[e.Static=2]="Static",e))(Wp||{}),$p=(e=>(e[e.Unmount=0]="Unmount",e[e.Hidden=1]="Hidden",e))($p||{});function Kp(e){let{ourProps:t,theirProps:n,slot:r,defaultTag:a,features:o,visible:i=!0,name:s,mergeRefs:l}=e;l=null!=l?l:Qp;let u=Gp(n,t);if(i)return qp(u,r,a,s,l);let c=null!=o?o:0;if(2&c){let{static:e=!1}=u,t=Jf(u,Up);if(e)return qp(t,r,a,s,l)}if(1&c){let{unmount:e=!0}=u,t=Jf(u,Hp);return lp(e?0:1,{0:()=>null,1:()=>qp(oo(oo({},t),{},{hidden:!0,style:{display:"none"}}),r,a,s,l)})}return qp(u,r,a,s,l)}function qp(e){let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0,a=arguments.length>3?arguments[3]:void 0,o=arguments.length>4?arguments[4]:void 0,i=Yp(e,["unmount","static"]),{as:s=r,children:l,refName:u="ref"}=i,c=Jf(i,Vp),d=void 0!==e.ref?{[u]:e.ref}:{},f="function"==typeof l?l(n):l;"className"in c&&c.className&&"function"==typeof c.className&&(c.className=c.className(n));let p={};if(n){let e=!1,t=[];for(let[r,a]of Object.entries(n))"boolean"==typeof a&&(e=!0),!0===a&&t.push(r);e&&(p["data-headlessui-state"]=t.join(" "))}if(s===t.Fragment&&Object.keys(Xp(c)).length>0){if(!(0,t.isValidElement)(f)||Array.isArray(f)&&f.length>1)throw new Error(['Passing props on "Fragment"!',"","The current component <".concat(a,' /> is rendering a "Fragment".'),"However we need to passthrough the following props:",Object.keys(c).map((e=>"  - ".concat(e))).join("\n"),"","You can apply a few solutions:",['Add an `as="..."` prop, to ensure that we render an actual element instead of a "Fragment".',"Render a single element as the child so that we can forward the props onto that element."].map((e=>"  - ".concat(e))).join("\n")].join("\n"));let e=f.props,n="function"==typeof(null==e?void 0:e.className)?function(){return Bp(null==e?void 0:e.className(...arguments),c.className)}:Bp(null==e?void 0:e.className,c.className),r=n?{className:n}:{};return(0,t.cloneElement)(f,Object.assign({},Gp(f.props,Xp(Yp(c,["ref"]))),p,d,{ref:o(f.ref,d.ref)},r))}return(0,t.createElement)(s,Object.assign({},Yp(c,["ref"]),s!==t.Fragment&&d,s!==t.Fragment&&p),f)}function Qp(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.every((e=>null==e))?void 0:e=>{for(let n of t)null!=n&&("function"==typeof n?n(e):n.current=e)}}function Gp(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];if(0===t.length)return{};if(1===t.length)return t[0];let r={},a={};for(let o of t)for(let e in o)e.startsWith("on")&&"function"==typeof o[e]?(null!=a[e]||(a[e]=[]),a[e].push(o[e])):r[e]=o[e];if(r.disabled||r["aria-disabled"])return Object.assign(r,Object.fromEntries(Object.keys(a).map((e=>[e,void 0]))));for(let o in a)Object.assign(r,{[o](e){let t=a[o];for(var n=arguments.length,r=new Array(n>1?n-1:0),i=1;i<n;i++)r[i-1]=arguments[i];for(let a of t){if((e instanceof Event||(null==e?void 0:e.nativeEvent)instanceof Event)&&e.defaultPrevented)return;a(e,...r)}}});return r}function Jp(e){var n;return Object.assign((0,t.forwardRef)(e),{displayName:null!=(n=e.displayName)?n:e.name})}function Xp(e){let t=Object.assign({},e);for(let n in t)void 0===t[n]&&delete t[n];return t}function Yp(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=Object.assign({},e);for(let r of t)r in n&&delete n[r];return n}var Zp=(e=>(e.Space=" ",e.Enter="Enter",e.Escape="Escape",e.Backspace="Backspace",e.Delete="Delete",e.ArrowLeft="ArrowLeft",e.ArrowUp="ArrowUp",e.ArrowRight="ArrowRight",e.ArrowDown="ArrowDown",e.Home="Home",e.End="End",e.PageUp="PageUp",e.PageDown="PageDown",e.Tab="Tab",e))(Zp||{});const eh=["__demoMode"],th=["id"],nh=["id"],rh=["id","disabled"];var ah=(e=>(e[e.Open=0]="Open",e[e.Closed=1]="Closed",e))(ah||{}),oh=(e=>(e[e.Pointer=0]="Pointer",e[e.Other=1]="Other",e))(oh||{}),ih=(e=>(e[e.OpenMenu=0]="OpenMenu",e[e.CloseMenu=1]="CloseMenu",e[e.GoToItem=2]="GoToItem",e[e.Search=3]="Search",e[e.ClearSearch=4]="ClearSearch",e[e.RegisterItem=5]="RegisterItem",e[e.UnregisterItem=6]="UnregisterItem",e))(ih||{});function sh(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:e=>e,n=null!==e.activeItemIndex?e.items[e.activeItemIndex]:null,r=wp(t(e.items.slice()),(e=>e.dataRef.current.domRef.current)),a=n?r.indexOf(n):null;return-1===a&&(a=null),{items:r,activeItemIndex:a}}let lh={1:e=>1===e.menuState?e:oo(oo({},e),{},{activeItemIndex:null,menuState:1}),0:e=>0===e.menuState?e:oo(oo({},e),{},{__demoMode:!1,menuState:0}),2:(e,t)=>{var n;let r=sh(e),a=zp(t,{resolveItems:()=>r.items,resolveActiveIndex:()=>r.activeItemIndex,resolveId:e=>e.id,resolveDisabled:e=>e.dataRef.current.disabled});return oo(oo(oo({},e),r),{},{searchQuery:"",activeItemIndex:a,activationTrigger:null!=(n=t.trigger)?n:1})},3:(e,t)=>{let n=""!==e.searchQuery?0:1,r=e.searchQuery+t.value.toLowerCase(),a=(null!==e.activeItemIndex?e.items.slice(e.activeItemIndex+n).concat(e.items.slice(0,e.activeItemIndex+n)):e.items).find((e=>{var t;return(null==(t=e.dataRef.current.textValue)?void 0:t.startsWith(r))&&!e.dataRef.current.disabled})),o=a?e.items.indexOf(a):-1;return-1===o||o===e.activeItemIndex?oo(oo({},e),{},{searchQuery:r}):oo(oo({},e),{},{searchQuery:r,activeItemIndex:o,activationTrigger:1})},4:e=>""===e.searchQuery?e:oo(oo({},e),{},{searchQuery:"",searchActiveItemIndex:null}),5:(e,t)=>{let n=sh(e,(e=>[...e,{id:t.id,dataRef:t.dataRef}]));return oo(oo({},e),n)},6:(e,t)=>{let n=sh(e,(e=>{let n=e.findIndex((e=>e.id===t.id));return-1!==n&&e.splice(n,1),e}));return oo(oo(oo({},e),n),{},{activationTrigger:1})}},uh=(0,t.createContext)(null);function ch(e){let n=(0,t.useContext)(uh);if(null===n){let t=new Error("<".concat(e," /> is missing a parent <Menu /> component."));throw Error.captureStackTrace&&Error.captureStackTrace(t,ch),t}return n}function dh(e,t){return lp(t.type,lh,e,t)}uh.displayName="MenuContext";let fh=t.Fragment;let ph=Wp.RenderStrategy|Wp.Static;let hh=t.Fragment;let mh=Jp((function(e,n){let{__demoMode:r=!1}=e,a=Jf(e,eh),o=(0,t.useReducer)(dh,{__demoMode:r,menuState:r?0:1,buttonRef:(0,t.createRef)(),itemsRef:(0,t.createRef)(),items:[],searchQuery:"",activeItemIndex:null,activationTrigger:1}),[{menuState:i,itemsRef:s,buttonRef:l},u]=o,c=Op(n);Ep([l,s],((e,t)=>{var n;u({type:1}),gp(t,mp.Loose)||(e.preventDefault(),null==(n=l.current)||n.focus())}),0===i);let d=ap((()=>{u({type:1})})),f=(0,t.useMemo)((()=>({open:0===i,close:d})),[i,d]),p={ref:c};return t.createElement(uh.Provider,{value:o},t.createElement(Dp,{value:lp(i,{0:Ip.Open,1:Ip.Closed})},Kp({ourProps:p,theirProps:a,slot:f,defaultTag:fh,name:"Menu"})))})),gh=Jp((function(e,n){var r;let a=sp(),{id:o="headlessui-menu-button-".concat(a)}=e,i=Jf(e,th),[s,l]=ch("Menu.Button"),u=Op(s.buttonRef,n),c=Yf(),d=ap((e=>{switch(e.key){case Zp.Space:case Zp.Enter:case Zp.ArrowDown:e.preventDefault(),e.stopPropagation(),l({type:0}),c.nextFrame((()=>l({type:2,focus:Mp.First})));break;case Zp.ArrowUp:e.preventDefault(),e.stopPropagation(),l({type:0}),c.nextFrame((()=>l({type:2,focus:Mp.Last})))}})),f=ap((e=>{if(e.key===Zp.Space)e.preventDefault()})),p=ap((t=>{if(Fp(t.currentTarget))return t.preventDefault();e.disabled||(0===s.menuState?(l({type:1}),c.nextFrame((()=>{var e;return null==(e=s.buttonRef.current)?void 0:e.focus({preventScroll:!0})}))):(t.preventDefault(),l({type:0})))})),h=(0,t.useMemo)((()=>({open:0===s.menuState})),[s]);return Kp({ourProps:{ref:u,id:o,type:Cp(e,s.buttonRef),"aria-haspopup":"menu","aria-controls":null==(r=s.itemsRef.current)?void 0:r.id,"aria-expanded":0===s.menuState,onKeyDown:d,onKeyUp:f,onClick:p},theirProps:i,slot:h,defaultTag:"button",name:"Menu.Button"})})),yh=Jp((function(e,n){var r,a;let o=sp(),{id:i="headlessui-menu-items-".concat(o)}=e,s=Jf(e,nh),[l,u]=ch("Menu.Items"),c=Op(l.itemsRef,n),d=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return(0,t.useMemo)((()=>up(...n)),[...n])}(l.itemsRef),f=Yf(),p=_p(),h=null!==p?(p&Ip.Open)===Ip.Open:0===l.menuState;(0,t.useEffect)((()=>{let e=l.itemsRef.current;e&&0===l.menuState&&e!==(null==d?void 0:d.activeElement)&&e.focus({preventScroll:!0})}),[l.menuState,l.itemsRef,d]),function(e){let{container:n,accept:r,walk:a,enabled:o=!0}=e,i=(0,t.useRef)(r),s=(0,t.useRef)(a);(0,t.useEffect)((()=>{i.current=r,s.current=a}),[r,a]),np((()=>{if(!n||!o)return;let e=up(n);if(!e)return;let t=i.current,r=s.current,a=Object.assign((e=>t(e)),{acceptNode:t}),l=e.createTreeWalker(n,NodeFilter.SHOW_ELEMENT,a,!1);for(;l.nextNode();)r(l.currentNode)}),[n,o,i,s])}({container:l.itemsRef.current,enabled:0===l.menuState,accept:e=>"menuitem"===e.getAttribute("role")?NodeFilter.FILTER_REJECT:e.hasAttribute("role")?NodeFilter.FILTER_SKIP:NodeFilter.FILTER_ACCEPT,walk(e){e.setAttribute("role","none")}});let m=ap((e=>{var t,n;switch(f.dispose(),e.key){case Zp.Space:if(""!==l.searchQuery)return e.preventDefault(),e.stopPropagation(),u({type:3,value:e.key});case Zp.Enter:if(e.preventDefault(),e.stopPropagation(),u({type:1}),null!==l.activeItemIndex){let{dataRef:e}=l.items[l.activeItemIndex];null==(n=null==(t=e.current)?void 0:t.domRef.current)||n.click()}yp(l.buttonRef.current);break;case Zp.ArrowDown:return e.preventDefault(),e.stopPropagation(),u({type:2,focus:Mp.Next});case Zp.ArrowUp:return e.preventDefault(),e.stopPropagation(),u({type:2,focus:Mp.Previous});case Zp.Home:case Zp.PageUp:return e.preventDefault(),e.stopPropagation(),u({type:2,focus:Mp.First});case Zp.End:case Zp.PageDown:return e.preventDefault(),e.stopPropagation(),u({type:2,focus:Mp.Last});case Zp.Escape:e.preventDefault(),e.stopPropagation(),u({type:1}),Xf().nextFrame((()=>{var e;return null==(e=l.buttonRef.current)?void 0:e.focus({preventScroll:!0})}));break;case Zp.Tab:e.preventDefault(),e.stopPropagation(),u({type:1}),Xf().nextFrame((()=>{xp(l.buttonRef.current,e.shiftKey?dp.Previous:dp.Next)}));break;default:1===e.key.length&&(u({type:3,value:e.key}),f.setTimeout((()=>u({type:4})),350))}})),g=ap((e=>{if(e.key===Zp.Space)e.preventDefault()})),y=(0,t.useMemo)((()=>({open:0===l.menuState})),[l]);return Kp({ourProps:{"aria-activedescendant":null===l.activeItemIndex||null==(r=l.items[l.activeItemIndex])?void 0:r.id,"aria-labelledby":null==(a=l.buttonRef.current)?void 0:a.id,id:i,onKeyDown:m,onKeyUp:g,role:"menu",tabIndex:0,ref:c},theirProps:s,slot:y,defaultTag:"div",features:ph,visible:h,name:"Menu.Items"})})),vh=Jp((function(e,n){let r=sp(),{id:a="headlessui-menu-item-".concat(r),disabled:o=!1}=e,i=Jf(e,rh),[s,l]=ch("Menu.Item"),u=null!==s.activeItemIndex&&s.items[s.activeItemIndex].id===a,c=(0,t.useRef)(null),d=Op(n,c);np((()=>{if(s.__demoMode||0!==s.menuState||!u||0===s.activationTrigger)return;let e=Xf();return e.requestAnimationFrame((()=>{var e,t;null==(t=null==(e=c.current)?void 0:e.scrollIntoView)||t.call(e,{block:"nearest"})})),e.dispose}),[s.__demoMode,c,u,s.menuState,s.activationTrigger,s.activeItemIndex]);let f=Tp(c),p=(0,t.useRef)({disabled:o,domRef:c,get textValue(){return f()}});np((()=>{p.current.disabled=o}),[p,o]),np((()=>(l({type:5,id:a,dataRef:p}),()=>l({type:6,id:a}))),[p,a]);let h=ap((()=>{l({type:1})})),m=ap((e=>{if(o)return e.preventDefault();l({type:1}),yp(s.buttonRef.current)})),g=ap((()=>{if(o)return l({type:2,focus:Mp.Nothing});l({type:2,focus:Mp.Specific,id:a})})),y=function(){let e=(0,t.useRef)([-1,-1]);return{wasMoved(t){let n=Ap(t);return(e.current[0]!==n[0]||e.current[1]!==n[1])&&(e.current=n,!0)},update(t){e.current=Ap(t)}}}(),v=ap((e=>y.update(e))),b=ap((e=>{y.wasMoved(e)&&(o||u||l({type:2,focus:Mp.Specific,id:a,trigger:0}))})),w=ap((e=>{y.wasMoved(e)&&(o||u&&l({type:2,focus:Mp.Nothing}))})),x=(0,t.useMemo)((()=>({active:u,disabled:o,close:h})),[u,o,h]);return Kp({ourProps:{id:a,ref:d,role:"menuitem",tabIndex:!0===o?void 0:-1,"aria-disabled":!0===o||void 0,disabled:void 0,onClick:m,onFocus:g,onPointerEnter:v,onMouseEnter:v,onPointerMove:b,onMouseMove:b,onPointerLeave:w,onMouseLeave:w},theirProps:i,slot:x,defaultTag:hh,name:"Menu.Item"})})),bh=Object.assign(mh,{Button:gh,Items:yh,Item:vh});function wh(){let e=(0,t.useRef)(!1);return np((()=>(e.current=!0,()=>{e.current=!1})),[]),e}function xh(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];e&&n.length>0&&e.classList.add(...n)}function kh(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];e&&n.length>0&&e.classList.remove(...n)}function Sh(e,t,n,r){let a=n?"enter":"leave",o=Xf(),i=void 0!==r?function(e){let t={called:!1};return function(){if(!t.called)return t.called=!0,e(...arguments)}}(r):()=>{};"enter"===a&&(e.removeAttribute("hidden"),e.style.display="");let s=lp(a,{enter:()=>t.enter,leave:()=>t.leave}),l=lp(a,{enter:()=>t.enterTo,leave:()=>t.leaveTo}),u=lp(a,{enter:()=>t.enterFrom,leave:()=>t.leaveFrom});return kh(e,...t.base,...t.enter,...t.enterTo,...t.enterFrom,...t.leave,...t.leaveFrom,...t.leaveTo,...t.entered),xh(e,...t.base,...s,...u),o.nextFrame((()=>{kh(e,...t.base,...s,...u),xh(e,...t.base,...s,...l),function(e,t){let n=Xf();if(!e)return n.dispose;let{transitionDuration:r,transitionDelay:a}=getComputedStyle(e),[o,i]=[r,a].map((e=>{let[t=0]=e.split(",").filter(Boolean).map((e=>e.includes("ms")?parseFloat(e):1e3*parseFloat(e))).sort(((e,t)=>t-e));return t})),s=o+i;if(0!==s){n.group((n=>{n.setTimeout((()=>{t(),n.dispose()}),s),n.addEventListener(e,"transitionrun",(e=>{e.target===e.currentTarget&&n.dispose()}))}));let r=n.addEventListener(e,"transitionend",(e=>{e.target===e.currentTarget&&(t(),r())}))}else t();n.add((()=>t())),n.dispose}(e,(()=>(kh(e,...t.base,...s),xh(e,...t.base,...t.entered),i())))})),o.dispose}const Eh=["beforeEnter","afterEnter","beforeLeave","afterLeave","enter","enterFrom","enterTo","entered","leave","leaveFrom","leaveTo"],Nh=["show","appear","unmount"];function Ch(){return(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"").split(/\s+/).filter((e=>e.length>1))}let jh=(0,t.createContext)(null);jh.displayName="TransitionContext";var Oh=(e=>(e.Visible="visible",e.Hidden="hidden",e))(Oh||{});let Ph=(0,t.createContext)(null);function Rh(e){return"children"in e?Rh(e.children):e.current.filter((e=>{let{el:t}=e;return null!==t.current})).filter((e=>{let{state:t}=e;return"visible"===t})).length>0}function Th(e,n){let r=rp(e),a=(0,t.useRef)([]),o=wh(),i=Yf(),s=ap((function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:$p.Hidden,n=a.current.findIndex((t=>{let{el:n}=t;return n===e}));-1!==n&&(lp(t,{[$p.Unmount](){a.current.splice(n,1)},[$p.Hidden](){a.current[n].state="hidden"}}),i.microTask((()=>{var e;!Rh(a)&&o.current&&(null==(e=r.current)||e.call(r))})))})),l=ap((e=>{let t=a.current.find((t=>{let{el:n}=t;return n===e}));return t?"visible"!==t.state&&(t.state="visible"):a.current.push({el:e,state:"visible"}),()=>s(e,$p.Unmount)})),u=(0,t.useRef)([]),c=(0,t.useRef)(Promise.resolve()),d=(0,t.useRef)({enter:[],leave:[],idle:[]}),f=ap(((e,t,r)=>{u.current.splice(0),n&&(n.chains.current[t]=n.chains.current[t].filter((t=>{let[n]=t;return n!==e}))),null==n||n.chains.current[t].push([e,new Promise((e=>{u.current.push(e)}))]),null==n||n.chains.current[t].push([e,new Promise((e=>{Promise.all(d.current[t].map((e=>{let[t,n]=e;return n}))).then((()=>e()))}))]),"enter"===t?c.current=c.current.then((()=>null==n?void 0:n.wait.current)).then((()=>r(t))):r(t)})),p=ap(((e,t,n)=>{Promise.all(d.current[t].splice(0).map((e=>{let[t,n]=e;return n}))).then((()=>{var e;null==(e=u.current.shift())||e()})).then((()=>n(t)))}));return(0,t.useMemo)((()=>({children:a,register:l,unregister:s,onStart:f,onStop:p,wait:c,chains:d})),[l,s,a,f,p,d,c])}function Ah(){}Ph.displayName="NestingContext";let Lh=["beforeEnter","afterEnter","beforeLeave","afterLeave"];function Ih(e){var t;let n={};for(let r of Lh)n[r]=null!=(t=e[r])?t:Ah;return n}let _h=Wp.RenderStrategy;let Dh=Jp((function(e,n){let{show:r,appear:a=!1,unmount:o=!0}=e,i=Jf(e,Nh),s=(0,t.useRef)(null),l=Op(s,n);op();let u=_p();if(void 0===r&&null!==u&&(r=(u&Ip.Open)===Ip.Open),![!0,!1].includes(r))throw new Error("A <Transition /> is used but it is missing a `show={true | false}` prop.");let[c,d]=(0,t.useState)(r?"visible":"hidden"),f=Th((()=>{d("hidden")})),[p,h]=(0,t.useState)(!0),m=(0,t.useRef)([r]);np((()=>{!1!==p&&m.current[m.current.length-1]!==r&&(m.current.push(r),h(!1))}),[m,r]);let g=(0,t.useMemo)((()=>({show:r,appear:a,initial:p})),[r,a,p]);(0,t.useEffect)((()=>{if(r)d("visible");else if(Rh(f)){let e=s.current;if(!e)return;let t=e.getBoundingClientRect();0===t.x&&0===t.y&&0===t.width&&0===t.height&&d("hidden")}else d("hidden")}),[r,f]);let y={unmount:o},v=ap((()=>{var t;p&&h(!1),null==(t=e.beforeEnter)||t.call(e)})),b=ap((()=>{var t;p&&h(!1),null==(t=e.beforeLeave)||t.call(e)}));return t.createElement(Ph.Provider,{value:f},t.createElement(jh.Provider,{value:g},Kp({ourProps:oo(oo({},y),{},{as:t.Fragment,children:t.createElement(Fh,oo(oo(oo({ref:l},y),i),{},{beforeEnter:v,beforeLeave:b}))}),theirProps:{},defaultTag:t.Fragment,features:_h,visible:"visible"===c,name:"Transition"})))})),Fh=Jp((function(e,n){var r,a;let{beforeEnter:o,afterEnter:i,beforeLeave:s,afterLeave:l,enter:u,enterFrom:c,enterTo:d,entered:f,leave:p,leaveFrom:h,leaveTo:m}=e,g=Jf(e,Eh),y=(0,t.useRef)(null),v=Op(y,n),b=null==(r=g.unmount)||r?$p.Unmount:$p.Hidden,{show:w,appear:x,initial:k}=function(){let e=(0,t.useContext)(jh);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),[S,E]=(0,t.useState)(w?"visible":"hidden"),N=function(){let e=(0,t.useContext)(Ph);if(null===e)throw new Error("A <Transition.Child /> is used but it is missing a parent <Transition /> or <Transition.Root />.");return e}(),{register:C,unregister:j}=N;(0,t.useEffect)((()=>C(y)),[C,y]),(0,t.useEffect)((()=>{if(b===$p.Hidden&&y.current)return w&&"visible"!==S?void E("visible"):lp(S,{hidden:()=>j(y),visible:()=>C(y)})}),[S,y,C,j,w,b]);let O=rp({base:Ch(g.className),enter:Ch(u),enterFrom:Ch(c),enterTo:Ch(d),entered:Ch(f),leave:Ch(p),leaveFrom:Ch(h),leaveTo:Ch(m)}),P=function(e){let n=(0,t.useRef)(Ih(e));return(0,t.useEffect)((()=>{n.current=Ih(e)}),[e]),n}({beforeEnter:o,afterEnter:i,beforeLeave:s,afterLeave:l}),R=op();(0,t.useEffect)((()=>{if(R&&"visible"===S&&null===y.current)throw new Error("Did you forget to passthrough the `ref` to the actual DOM node?")}),[y,S,R]);let T=x&&w&&k,A=!R||k&&!x?"idle":w?"enter":"leave",L=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0,[n,r]=(0,t.useState)(e),a=wh(),o=(0,t.useCallback)((e=>{a.current&&r((t=>t|e))}),[n,a]),i=(0,t.useCallback)((e=>Boolean(n&e)),[n]),s=(0,t.useCallback)((e=>{a.current&&r((t=>t&~e))}),[r,a]),l=(0,t.useCallback)((e=>{a.current&&r((t=>t^e))}),[r]);return{flags:n,addFlag:o,hasFlag:i,removeFlag:s,toggleFlag:l}}(0),I=ap((e=>lp(e,{enter:()=>{L.addFlag(Ip.Opening),P.current.beforeEnter()},leave:()=>{L.addFlag(Ip.Closing),P.current.beforeLeave()},idle:()=>{}}))),_=ap((e=>lp(e,{enter:()=>{L.removeFlag(Ip.Opening),P.current.afterEnter()},leave:()=>{L.removeFlag(Ip.Closing),P.current.afterLeave()},idle:()=>{}}))),D=Th((()=>{E("hidden"),j(y)}),N),F=(0,t.useRef)(!1);!function(e){let{immediate:t,container:n,direction:r,classes:a,onStart:o,onStop:i}=e,s=wh(),l=Yf(),u=rp(r);np((()=>{t&&(u.current="enter")}),[t]),np((()=>{let e=Xf();l.add(e.dispose);let t=n.current;if(t&&"idle"!==u.current&&s.current)return e.dispose(),o.current(u.current),e.add(Sh(t,a.current,"enter"===u.current,(()=>{e.dispose(),i.current(u.current)}))),e.dispose}),[r])}({immediate:T,container:y,classes:O,direction:A,onStart:rp((e=>{F.current=!0,D.onStart(y,e,I)})),onStop:rp((e=>{F.current=!1,D.onStop(y,e,_),"leave"===e&&!Rh(D)&&(E("hidden"),j(y))}))});let M=g,z={ref:v};return T?M=oo(oo({},M),{},{className:Bp(g.className,...O.current.enter,...O.current.enterFrom)}):F.current&&(M.className=Bp(g.className,null==(a=y.current)?void 0:a.className),""===M.className&&delete M.className),t.createElement(Ph.Provider,{value:D},t.createElement(Dp,{value:lp(S,{visible:Ip.Open,hidden:Ip.Closed})|L.flags},Kp({ourProps:z,theirProps:M,defaultTag:"div",features:_h,visible:"visible"===S,name:"Transition.Child"})))})),Mh=Jp((function(e,n){let r=null!==(0,t.useContext)(jh),a=null!==_p();return t.createElement(t.Fragment,null,!r&&a?t.createElement(Dh,oo({ref:n},e)):t.createElement(Fh,oo({ref:n},e)))})),zh=Object.assign(Dh,{Child:Mh,Root:Dh});const Bh=["title","titleId"];function Uh(e,n){let{title:r,titleId:a}=e,o=Jf(e,Bh);return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":a},o),r?t.createElement("title",{id:a},r):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))}const Hh=t.forwardRef(Uh),Vh=["title","titleId"];function Wh(e,n){let{title:r,titleId:a}=e,o=Jf(e,Vh);return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":a},o),r?t.createElement("title",{id:a},r):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 21a9.004 9.004 0 0 0 8.716-6.747M12 21a9.004 9.004 0 0 1-8.716-6.747M12 21c2.485 0 4.5-4.03 4.5-9S14.485 3 12 3m0 18c-2.485 0-4.5-4.03-4.5-9S9.515 3 12 3m0 0a8.997 8.997 0 0 1 7.843 4.582M12 3a8.997 8.997 0 0 0-7.843 4.582m15.686 0A11.953 11.953 0 0 1 12 10.5c-2.998 0-5.74-1.1-7.843-2.918m15.686 0A8.959 8.959 0 0 1 21 12c0 .778-.099 1.533-.284 2.253m0 0A17.919 17.919 0 0 1 12 16.5c-3.162 0-6.133-.815-8.716-2.247m0 0A9.015 9.015 0 0 1 3 12c0-1.605.42-3.113 1.157-4.418"}))}const $h=t.forwardRef(Wh),Kh=["title","titleId"];function qh(e,n){let{title:r,titleId:a}=e,o=Jf(e,Kh);return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":a},o),r?t.createElement("title",{id:a},r):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))}const Qh=t.forwardRef(qh),Gh=["title","titleId"];function Jh(e,n){let{title:r,titleId:a}=e,o=Jf(e,Gh);return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":a},o),r?t.createElement("title",{id:a},r):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M17.982 18.725A7.488 7.488 0 0 0 12 15.75a7.488 7.488 0 0 0-5.982 2.975m11.963 0a9 9 0 1 0-11.963 0m11.963 0A8.966 8.966 0 0 1 12 21a8.966 8.966 0 0 1-5.982-2.275M15 9.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const Xh=t.forwardRef(Jh),Yh=["title","titleId"];function Zh(e,n){let{title:r,titleId:a}=e,o=Jf(e,Yh);return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":a},o),r?t.createElement("title",{id:a},r):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.594 3.94c.09-.542.56-.94 1.11-.94h2.593c.55 0 1.02.398 1.11.94l.213 1.281c.063.374.313.686.645.87.074.04.147.083.22.127.325.196.72.257 1.075.124l1.217-.456a1.125 1.125 0 0 1 1.37.49l1.296 2.247a1.125 1.125 0 0 1-.26 1.431l-1.003.827c-.293.241-.438.613-.43.992a7.723 7.723 0 0 1 0 .255c-.008.378.137.75.43.991l1.004.827c.424.35.534.955.26 1.43l-1.298 2.247a1.125 1.125 0 0 1-1.369.491l-1.217-.456c-.355-.133-.75-.072-1.076.124a6.47 6.47 0 0 1-.22.128c-.331.183-.581.495-.644.869l-.213 1.281c-.09.543-.56.94-1.11.94h-2.594c-.55 0-1.019-.398-1.11-.94l-.213-1.281c-.062-.374-.312-.686-.644-.87a6.52 6.52 0 0 1-.22-.127c-.325-.196-.72-.257-1.076-.124l-1.217.456a1.125 1.125 0 0 1-1.369-.49l-1.297-2.247a1.125 1.125 0 0 1 .26-1.431l1.004-.827c.292-.24.437-.613.43-.991a6.932 6.932 0 0 1 0-.255c.007-.38-.138-.751-.43-.992l-1.004-.827a1.125 1.125 0 0 1-.26-1.43l1.297-2.247a1.125 1.125 0 0 1 1.37-.491l1.216.456c.356.133.751.072 1.076-.124.072-.044.146-.086.22-.128.332-.183.582-.495.644-.869l.214-1.28Z"}),t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const em=t.forwardRef(Zh),tm=["title","titleId"];function nm(e,n){let{title:r,titleId:a}=e,o=Jf(e,tm);return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":a},o),r?t.createElement("title",{id:a},r):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9"}))}const rm=t.forwardRef(nm),am=()=>{const e=P(),{t:n,i18n:r}=ci(),{user:a}=w((e=>e.auth)),{notifications:o,language:i}=w((e=>e.ui)),[s,l]=(0,t.useState)(!1),u=o.filter((e=>!e.read)).length,c=[{code:"en",name:n("languages.en"),flag:"\ud83c\uddfa\ud83c\uddf8"},{code:"af",name:n("languages.af"),flag:"\ud83c\uddff\ud83c\udde6"},{code:"st",name:n("languages.st"),flag:"\ud83c\uddf1\ud83c\uddf8"},{code:"tn",name:n("languages.tn"),flag:"\ud83c\udde7\ud83c\uddfc"},{code:"zu",name:n("languages.zu"),flag:"\ud83c\uddff\ud83c\udde6"}],d=()=>{e(Nf())};return(0,tr.jsx)("header",{className:"bg-white shadow-sm border-b border-gray-200",children:(0,tr.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,tr.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,tr.jsx)("div",{className:"flex items-center",children:(0,tr.jsx)("button",{onClick:()=>e(Lf()),className:"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500","aria-label":"Toggle sidebar",title:"Toggle sidebar",children:(0,tr.jsx)(Hh,{className:"h-6 w-6"})})}),(0,tr.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,tr.jsxs)(bh,{as:"div",className:"relative",children:[(0,tr.jsx)(bh.Button,{className:"p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500",children:(0,tr.jsx)($h,{className:"h-6 w-6"})}),(0,tr.jsx)(zh,{enter:"transition ease-out duration-100",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95",children:(0,tr.jsx)(bh.Items,{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50",children:(0,tr.jsx)("div",{className:"py-1",children:c.map((t=>(0,tr.jsx)(bh.Item,{children:n=>{let{active:a}=n;return(0,tr.jsxs)("button",{type:"button",onClick:()=>{return n=t.code,r.changeLanguage(n),e(Df(n)),void l(!1);var n},className:"".concat(a?"bg-gray-100":""," ").concat(i===t.code?"text-primary-600 font-medium":"text-gray-700"," flex items-center w-full px-4 py-2 text-sm"),children:[(0,tr.jsx)("span",{className:"mr-3",children:t.flag}),t.name]})}},t.code)))})})})]}),(0,tr.jsxs)("button",{type:"button",className:"relative p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500","aria-label":"View notifications",title:"Notifications",children:[(0,tr.jsx)(Qh,{className:"h-6 w-6"}),u>0&&(0,tr.jsx)("span",{className:"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white"})]}),(0,tr.jsxs)(bh,{as:"div",className:"relative",children:[(0,tr.jsx)(bh.Button,{className:"flex items-center space-x-3 p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500",children:(0,tr.jsxs)("div",{className:"flex items-center space-x-2",children:[null!==a&&void 0!==a&&a.profileImage?(0,tr.jsx)("img",{className:"h-8 w-8 rounded-full",src:a.profileImage,alt:a.firstName}):(0,tr.jsx)(Xh,{className:"h-8 w-8 text-gray-400"}),(0,tr.jsxs)("div",{className:"hidden md:block text-left",children:[(0,tr.jsxs)("p",{className:"text-sm font-medium text-gray-700",children:[null===a||void 0===a?void 0:a.firstName," ",null===a||void 0===a?void 0:a.lastName]}),(0,tr.jsx)("p",{className:"text-xs text-gray-500 capitalize",children:null===a||void 0===a?void 0:a.role})]})]})}),(0,tr.jsx)(zh,{enter:"transition ease-out duration-100",enterFrom:"transform opacity-0 scale-95",enterTo:"transform opacity-100 scale-100",leave:"transition ease-in duration-75",leaveFrom:"transform opacity-100 scale-100",leaveTo:"transform opacity-0 scale-95",children:(0,tr.jsx)(bh.Items,{className:"absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-50",children:(0,tr.jsxs)("div",{className:"py-1",children:[(0,tr.jsx)(bh.Item,{children:e=>{let{active:t}=e;return(0,tr.jsxs)("a",{href:"/settings",className:"".concat(t?"bg-gray-100":""," flex items-center px-4 py-2 text-sm text-gray-700"),children:[(0,tr.jsx)(em,{className:"mr-3 h-5 w-5"}),n("navigation.settings")]})}}),(0,tr.jsx)(bh.Item,{children:e=>{let{active:t}=e;return(0,tr.jsxs)("button",{type:"button",onClick:d,className:"".concat(t?"bg-gray-100":""," flex items-center w-full px-4 py-2 text-sm text-gray-700"),children:[(0,tr.jsx)(rm,{className:"mr-3 h-5 w-5"}),n("auth.logout")]})}})]})})})]})]})]})})})},om=["title","titleId"];function im(e,n){let{title:r,titleId:a}=e,o=Jf(e,om);return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":a},o),r?t.createElement("title",{id:a},r):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))}const sm=t.forwardRef(im),lm=["title","titleId"];function um(e,n){let{title:r,titleId:a}=e,o=Jf(e,lm);return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":a},o),r?t.createElement("title",{id:a},r):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}const cm=t.forwardRef(um),dm=["title","titleId"];function fm(e,n){let{title:r,titleId:a}=e,o=Jf(e,dm);return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":a},o),r?t.createElement("title",{id:a},r):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12Z"}))}const pm=t.forwardRef(fm),hm=["title","titleId"];function mm(e,n){let{title:r,titleId:a}=e,o=Jf(e,hm);return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":a},o),r?t.createElement("title",{id:a},r):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.75 3.104v5.714a2.25 2.25 0 0 1-.659 1.591L5 14.5M9.75 3.104c-.251.023-.501.05-.75.082m.75-.082a24.301 24.301 0 0 1 4.5 0m0 0v5.714c0 .597.237 1.17.659 1.591L19.8 15.3M14.25 3.104c.251.023.501.05.75.082M19.8 15.3l-1.57.393A9.065 9.065 0 0 1 12 15a9.065 9.065 0 0 0-6.23-.693L5 14.5m14.8.8 1.402 1.402c1.232 1.232.65 3.318-1.067 3.611A48.309 48.309 0 0 1 12 21c-2.773 0-5.491-.235-8.135-.687-1.718-.293-2.3-2.379-1.067-3.61L5 14.5"}))}const gm=t.forwardRef(mm),ym=["title","titleId"];function vm(e,n){let{title:r,titleId:a}=e,o=Jf(e,ym);return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":a},o),r?t.createElement("title",{id:a},r):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))}const bm=t.forwardRef(vm),wm=["title","titleId"];function xm(e,n){let{title:r,titleId:a}=e,o=Jf(e,wm);return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":a},o),r?t.createElement("title",{id:a},r):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))}const km=t.forwardRef(xm),Sm=["title","titleId"];function Em(e,n){let{title:r,titleId:a}=e,o=Jf(e,Sm);return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":a},o),r?t.createElement("title",{id:a},r):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25M9 16.5v.75m3-3v3M15 12v5.25m-4.5-15H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))}const Nm=t.forwardRef(Em),Cm=()=>{const{t:e}=ci(),{sidebarOpen:t}=w((e=>e.ui)),n=[{name:e("navigation.dashboard"),href:"/dashboard",icon:sm},{name:e("navigation.animals"),href:"/animals",icon:cm},{name:e("navigation.health"),href:"/health",icon:pm},{name:e("navigation.breeding"),href:"/breeding",icon:gm},{name:e("navigation.feeding"),href:"/feeding",icon:bm},{name:e("navigation.financial"),href:"/financial",icon:km},{name:e("navigation.reports"),href:"/reports",icon:Nm},{name:e("navigation.settings"),href:"/settings",icon:em}];return(0,tr.jsxs)("div",{className:"fixed inset-y-0 left-0 z-50 bg-white shadow-lg transition-all duration-300 ".concat(t?"w-64":"w-16"," lg:translate-x-0"),children:[(0,tr.jsx)("div",{className:"flex items-center h-16 px-4 border-b border-gray-200",children:(0,tr.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,tr.jsx)("div",{className:"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,tr.jsx)("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,tr.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})})}),t&&(0,tr.jsxs)("div",{children:[(0,tr.jsx)("h1",{className:"text-lg font-bold text-primary-900",children:"AMPD"}),(0,tr.jsx)("p",{className:"text-xs text-primary-600",children:"Livestock"})]})]})}),(0,tr.jsx)("nav",{className:"mt-8 px-4",children:(0,tr.jsx)("ul",{className:"space-y-2",children:n.map((e=>(0,tr.jsx)("li",{children:(0,tr.jsxs)(rt,{to:e.href,className:e=>{let{isActive:t}=e;return"flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 ".concat(t?"bg-primary-100 text-primary-700 border-r-2 border-primary-600":"text-gray-600 hover:text-gray-900 hover:bg-gray-100")},children:[(0,tr.jsx)(e.icon,{className:"h-5 w-5 flex-shrink-0"}),t&&(0,tr.jsx)("span",{className:"ml-3",children:e.name})]})},e.name)))})})]})},jm=()=>{const{sidebarOpen:e}=w((e=>e.ui));return(0,tr.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,tr.jsx)(Cm,{}),(0,tr.jsxs)("div",{className:"transition-all duration-300 ".concat(e?"lg:ml-64":"lg:ml-16"),children:[(0,tr.jsx)(am,{}),(0,tr.jsx)("main",{className:"py-6",children:(0,tr.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,tr.jsx)(He,{})})})]}),e&&(0,tr.jsx)("div",{className:"fixed inset-0 z-40 lg:hidden",children:(0,tr.jsx)("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75"})})]})},Om=e=>{let{children:t}=e;const{t:n}=ci();return(0,tr.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-primary-50 to-primary-100 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:[(0,tr.jsxs)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:[(0,tr.jsx)("div",{className:"flex justify-center",children:(0,tr.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,tr.jsx)("div",{className:"w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center",children:(0,tr.jsx)("svg",{className:"w-6 h-6 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,tr.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})})}),(0,tr.jsxs)("div",{children:[(0,tr.jsx)("h1",{className:"text-2xl font-bold text-primary-900",children:"AMPD"}),(0,tr.jsx)("p",{className:"text-sm text-primary-600",children:"Livestock Management"})]})]})}),(0,tr.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-gray-900",children:n("auth.welcomeBack")}),(0,tr.jsx)("p",{className:"mt-2 text-center text-sm text-gray-600",children:n("auth.signInToContinue")})]}),(0,tr.jsx)("div",{className:"mt-8 sm:mx-auto sm:w-full sm:max-w-md",children:(0,tr.jsx)("div",{className:"bg-white py-8 px-4 shadow-lg sm:rounded-lg sm:px-10",children:t})}),(0,tr.jsx)("div",{className:"mt-8 text-center",children:(0,tr.jsx)("p",{className:"text-sm text-gray-500",children:"\xa9 2024 AMPD Livestock Management System. All rights reserved."})})]})},Pm=["title","titleId"];function Rm(e,n){let{title:r,titleId:a}=e,o=Jf(e,Pm);return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":a},o),r?t.createElement("title",{id:a},r):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))}const Tm=t.forwardRef(Rm),Am=["title","titleId"];function Lm(e,n){let{title:r,titleId:a}=e,o=Jf(e,Am);return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":a},o),r?t.createElement("title",{id:a},r):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))}const Im=t.forwardRef(Lm),_m=e=>{let{size:t="medium",color:n="primary",text:r,className:a=""}=e;const{t:o}=ci();return(0,tr.jsxs)("div",{className:"flex flex-col items-center justify-center ".concat(a),children:[(0,tr.jsx)("div",{className:"\n          animate-spin rounded-full border-2 border-t-transparent\n          ".concat({small:"h-4 w-4",medium:"h-8 w-8",large:"h-12 w-12"}[t],"\n          ").concat({primary:"border-primary-600",secondary:"border-secondary-600",white:"border-white"}[n],"\n        "),role:"status","aria-label":r||o("common.loading")}),r&&(0,tr.jsx)("p",{className:"mt-2 text-sm text-gray-600 dark:text-gray-400",children:r})]})},Dm=()=>{const e=P(),n=Ne(),{t:r}=ci(),{isLoading:a,error:o}=w((e=>e.auth)),[i,s]=(0,t.useState)({username:"",password:""}),[l,u]=(0,t.useState)(!1),[c,d]=(0,t.useState)(!1),f=t=>{const{name:n,value:r}=t.target;s((e=>oo(oo({},e),{},{[n]:r}))),o&&e(Of())};return(0,tr.jsxs)("div",{className:"space-y-6",children:[(0,tr.jsxs)("form",{className:"space-y-6",onSubmit:async t=>{t.preventDefault();try{const t=await e(xf(i));xf.fulfilled.match(t)&&n("/dashboard")}catch(o){}},children:[o&&(0,tr.jsx)("div",{className:"rounded-md bg-red-50 p-4",children:(0,tr.jsx)("div",{className:"text-sm text-red-700",children:o})}),(0,tr.jsxs)("div",{children:[(0,tr.jsx)("label",{htmlFor:"username",className:"form-label",children:r("auth.username")}),(0,tr.jsx)("input",{id:"username",name:"username",type:"text",autoComplete:"username",required:!0,className:"form-input",placeholder:r("auth.username"),value:i.username,onChange:f})]}),(0,tr.jsxs)("div",{children:[(0,tr.jsx)("label",{htmlFor:"password",className:"form-label",children:r("auth.password")}),(0,tr.jsxs)("div",{className:"relative",children:[(0,tr.jsx)("input",{id:"password",name:"password",type:l?"text":"password",autoComplete:"current-password",required:!0,className:"form-input pr-10",placeholder:r("auth.password"),value:i.password,onChange:f}),(0,tr.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>u(!l),children:l?(0,tr.jsx)(Tm,{className:"h-5 w-5 text-gray-400"}):(0,tr.jsx)(Im,{className:"h-5 w-5 text-gray-400"})})]})]}),(0,tr.jsxs)("div",{className:"flex items-center justify-between",children:[(0,tr.jsxs)("div",{className:"flex items-center",children:[(0,tr.jsx)("input",{id:"remember-me",name:"remember-me",type:"checkbox",className:"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded",checked:c,onChange:e=>d(e.target.checked)}),(0,tr.jsx)("label",{htmlFor:"remember-me",className:"ml-2 block text-sm text-gray-900",children:"Remember me"})]}),(0,tr.jsx)("div",{className:"text-sm",children:(0,tr.jsx)("a",{href:"#",className:"font-medium text-primary-600 hover:text-primary-500",children:r("auth.forgotPassword")})})]}),(0,tr.jsx)("div",{children:(0,tr.jsx)("button",{type:"submit",disabled:a,className:"w-full btn-primary flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed",children:a?(0,tr.jsx)(_m,{size:"small",color:"white"}):r("auth.signIn")})})]}),(0,tr.jsx)("div",{className:"text-center",children:(0,tr.jsxs)("p",{className:"text-sm text-gray-600",children:[r("auth.dontHaveAccount")," ",(0,tr.jsx)(nt,{to:"/register",className:"font-medium text-primary-600 hover:text-primary-500",children:r("auth.createAccount")})]})}),(0,tr.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded-md",children:[(0,tr.jsx)("h3",{className:"text-sm font-medium text-blue-800 mb-2",children:"Demo Credentials:"}),(0,tr.jsxs)("div",{className:"text-xs text-blue-700 space-y-1",children:[(0,tr.jsxs)("div",{children:[(0,tr.jsx)("strong",{children:"Admin:"})," admin / Admin@123"]}),(0,tr.jsxs)("div",{children:[(0,tr.jsx)("strong",{children:"Manager:"})," manager / Manager@123"]}),(0,tr.jsxs)("div",{children:[(0,tr.jsx)("strong",{children:"Staff:"})," staff / Staff@123"]}),(0,tr.jsxs)("div",{children:[(0,tr.jsx)("strong",{children:"Veterinarian:"})," vet / Vet@123"]})]})]})]})},Fm=()=>{const{t:e}=ci();return(0,tr.jsxs)("div",{className:"space-y-6",children:[(0,tr.jsxs)("div",{className:"text-center",children:[(0,tr.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:e("auth.register")}),(0,tr.jsx)("p",{className:"mt-2 text-gray-600",children:"Create your AMPD account"})]}),(0,tr.jsx)("div",{className:"bg-blue-50 p-4 rounded-md",children:(0,tr.jsx)("p",{className:"text-sm text-blue-700",children:"Registration is currently disabled. Please contact your administrator for account creation."})})]})},Mm=["title","titleId"];function zm(e,n){let{title:r,titleId:a}=e,o=Jf(e,Mm);return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":a},o),r?t.createElement("title",{id:a},r):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6.75 3v2.25M17.25 3v2.25M3 18.75V7.5a2.25 2.25 0 0 1 2.25-2.25h13.5A2.25 2.25 0 0 1 21 7.5v11.25m-18 0A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75m-18 0v-7.5A2.25 2.25 0 0 1 5.25 9h13.5A2.25 2.25 0 0 1 21 11.25v7.5"}))}const Bm=t.forwardRef(zm),Um=["title","titleId"];function Hm(e,n){let{title:r,titleId:a}=e,o=Jf(e,Um);return t.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:n,"aria-labelledby":a},o),r?t.createElement("title",{id:a},r):null,t.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126ZM12 15.75h.007v.008H12v-.008Z"}))}const Vm=t.forwardRef(Hm),Wm=Lu("animals/fetchAnimals",(async(e,t)=>{let{rejectWithValue:n}=t;try{await new Promise((e=>setTimeout(e,1e3)));const t=[{_id:"1",tagNumber:"C001",name:"Bessie",species:"cattle",breed:"Holstein",gender:"female",dateOfBirth:"2022-03-15",color:"Black and White",currentWeight:450,birthWeight:35,status:"active",healthStatus:"healthy",breedingStatus:"available",isActive:!0,createdBy:"user1",createdAt:"2023-01-01",updatedAt:"2023-01-01",age:1,ageInDays:365,latestWeight:450},{_id:"2",tagNumber:"S001",name:"Woolly",species:"sheep",breed:"Merino",gender:"male",dateOfBirth:"2023-01-10",color:"White",currentWeight:75,birthWeight:4,status:"active",healthStatus:"healthy",breedingStatus:"available",isActive:!0,createdBy:"user1",createdAt:"2023-01-10",updatedAt:"2023-01-10",age:0,ageInDays:180,latestWeight:75}];return{animals:t,pagination:{page:e.page||1,limit:e.limit||20,total:t.length,totalPages:Math.ceil(t.length/(e.limit||20))}}}catch(r){return n(r.message||"Failed to fetch animals")}})),$m=Lu("animals/fetchAnimalById",(async(e,t)=>{let{rejectWithValue:n}=t;try{await new Promise((e=>setTimeout(e,500)));return{_id:e,tagNumber:"C001",name:"Bessie",species:"cattle",breed:"Holstein",gender:"female",dateOfBirth:"2022-03-15",color:"Black and White",currentWeight:450,birthWeight:35,status:"active",healthStatus:"healthy",breedingStatus:"available",isActive:!0,createdBy:"user1",createdAt:"2023-01-01",updatedAt:"2023-01-01",age:1,ageInDays:365,latestWeight:450}}catch(r){return n(r.message||"Failed to fetch animal")}})),Km=Lu("animals/fetchStatistics",(async(e,t)=>{let{rejectWithValue:n}=t;try{return await new Promise((e=>setTimeout(e,500))),{total:150,bySpecies:{cattle:75,sheep:45,goat:20,pig:10},byStatus:{active:140,sold:5,deceased:3,transferred:2},byHealthStatus:{healthy:135,sick:8,injured:4,recovering:3},byBreedingStatus:{available:80,pregnant:25,lactating:30,breeding:10,retired:5}}}catch(r){return n(r.message||"Failed to fetch statistics")}})),qm=ju({name:"animals",initialState:{animals:[],selectedAnimal:null,filters:{},pagination:{page:1,limit:20,total:0,totalPages:0},isLoading:!1,error:null,statistics:null},reducers:{setFilters:(e,t)=>{e.filters=oo(oo({},e.filters),t.payload)},clearFilters:e=>{e.filters={}},setSelectedAnimal:(e,t)=>{e.selectedAnimal=t.payload},setPagination:(e,t)=>{e.pagination=oo(oo({},e.pagination),t.payload)},clearError:e=>{e.error=null}},extraReducers:e=>{e.addCase(Wm.pending,(e=>{e.isLoading=!0,e.error=null})).addCase(Wm.fulfilled,((e,t)=>{e.isLoading=!1,e.animals=t.payload.animals,e.pagination=t.payload.pagination,e.error=null})).addCase(Wm.rejected,((e,t)=>{e.isLoading=!1,e.error=t.payload})).addCase($m.pending,(e=>{e.isLoading=!0,e.error=null})).addCase($m.fulfilled,((e,t)=>{e.isLoading=!1,e.selectedAnimal=t.payload,e.error=null})).addCase($m.rejected,((e,t)=>{e.isLoading=!1,e.error=t.payload})).addCase(Km.fulfilled,((e,t)=>{e.statistics=t.payload}))}}),{setFilters:Qm,clearFilters:Gm,setSelectedAnimal:Jm,setPagination:Xm,clearError:Ym}=qm.actions,Zm=qm.reducer,eg=e=>{let{title:t,children:n,className:r="",headerAction:a}=e;return(0,tr.jsxs)("div",{className:"card ".concat(r),children:[(0,tr.jsxs)("div",{className:"card-header flex items-center justify-between",children:[(0,tr.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:t}),a&&(0,tr.jsx)("div",{children:a})]}),(0,tr.jsx)("div",{className:"card-body",children:n})]})},tg=e=>{let{name:t,value:n,icon:r,color:a,change:o,changeType:i="neutral",onClick:s}=e;return(0,tr.jsx)("div",{className:"card ".concat(s?"cursor-pointer hover:shadow-md transition-shadow":""),onClick:s,children:(0,tr.jsx)("div",{className:"card-body",children:(0,tr.jsxs)("div",{className:"flex items-center",children:[(0,tr.jsx)("div",{className:"flex-shrink-0",children:(0,tr.jsx)("div",{className:"w-8 h-8 rounded-md ".concat(a," flex items-center justify-center"),children:(0,tr.jsx)(r,{className:"w-5 h-5 text-white"})})}),(0,tr.jsx)("div",{className:"ml-5 w-0 flex-1",children:(0,tr.jsxs)("dl",{children:[(0,tr.jsx)("dt",{className:"text-sm font-medium text-gray-500 truncate",children:t}),(0,tr.jsxs)("dd",{className:"flex items-baseline",children:[(0,tr.jsx)("div",{className:"text-2xl font-semibold text-gray-900",children:n}),o&&(0,tr.jsx)("div",{className:"ml-2 flex items-baseline text-sm font-semibold ".concat("positive"===i?"text-green-600":"negative"===i?"text-red-600":"text-gray-600"),children:o})]})]})})]})})})},ng=e=>{let{activities:t=[],maxItems:n=5}=e;const{t:r}=ci(),a=(t.length>0?t:[{id:"1",type:"vaccination",message:"Cow C001 vaccinated against FMD",timestamp:"2 hours ago",animalId:"C001",priority:"medium"},{id:"2",type:"birth",message:"New calf born to Cow C015",timestamp:"4 hours ago",animalId:"C015",priority:"high"},{id:"3",type:"health",message:"Sheep S023 showing signs of lameness",timestamp:"6 hours ago",animalId:"S023",priority:"high"},{id:"4",type:"feeding",message:"Feed inventory low for cattle section",timestamp:"8 hours ago",priority:"medium"},{id:"5",type:"sale",message:"Goat G012 sold to local buyer",timestamp:"1 day ago",animalId:"G012",priority:"low"}]).slice(0,n),o=e=>{switch(e){case"vaccination":return"bg-green-400";case"birth":return"bg-blue-400";case"sale":return"bg-purple-400";case"health":return"bg-red-400";case"feeding":return"bg-yellow-400";case"breeding":return"bg-pink-400";default:return"bg-gray-400"}},i=e=>{switch(e){case"high":return"border-l-red-500";case"medium":return"border-l-yellow-500";case"low":return"border-l-green-500";default:return"border-l-gray-300"}};return(0,tr.jsxs)("div",{className:"space-y-4",children:[a.map((e=>(0,tr.jsxs)("div",{className:"flex items-start space-x-3 p-3 border-l-4 ".concat(i(e.priority)," bg-gray-50 rounded-r-md"),children:[(0,tr.jsx)("div",{className:"w-2 h-2 ".concat(o(e.type)," rounded-full mt-2 flex-shrink-0")}),(0,tr.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,tr.jsx)("p",{className:"text-sm text-gray-900",children:e.message}),(0,tr.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,tr.jsx)("p",{className:"text-xs text-gray-500",children:e.timestamp}),e.animalId&&(0,tr.jsx)("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800",children:e.animalId})]})]})]},e.id))),0===a.length&&(0,tr.jsx)("div",{className:"text-center py-8 text-gray-500",children:(0,tr.jsx)("p",{children:"No recent activities"})})]})},rg=()=>{var e,n;const r=P(),{t:a}=ci(),{user:o}=w((e=>e.auth)),{statistics:i}=w((e=>e.animals));(0,t.useEffect)((()=>{r(Qf(a("dashboard.title"))),r(qf([{label:a("dashboard.title")}])),r(Km())}),[r,a]);const s=[{name:a("dashboard.totalAnimals"),value:(null===i||void 0===i?void 0:i.total)||0,icon:cm,color:"bg-blue-500",change:"+12%",changeType:"positive"},{name:a("dashboard.healthyAnimals"),value:(null===i||void 0===i||null===(e=i.byHealthStatus)||void 0===e?void 0:e.healthy)||0,icon:pm,color:"bg-green-500",change:"+2%",changeType:"positive"},{name:a("dashboard.pregnantAnimals"),value:(null===i||void 0===i||null===(n=i.byBreedingStatus)||void 0===n?void 0:n.pregnant)||0,icon:Bm,color:"bg-purple-500",change:"+5%",changeType:"positive"},{name:a("dashboard.healthAlerts"),value:8,icon:Vm,color:"bg-red-500",change:"-3%",changeType:"negative"}];return(0,tr.jsxs)("div",{className:"space-y-6",children:[(0,tr.jsxs)("div",{className:"bg-white rounded-lg shadow-soft p-6",children:[(0,tr.jsxs)("h1",{className:"text-2xl font-bold text-gray-900",children:[a("dashboard.welcome"),", ",null===o||void 0===o?void 0:o.firstName,"!"]}),(0,tr.jsx)("p",{className:"mt-2 text-gray-600",children:"Here's what's happening with your livestock today."})]}),(0,tr.jsx)("div",{className:"grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4",children:s.map((e=>(0,tr.jsx)(tg,{name:e.name,value:e.value,icon:e.icon,color:e.color,change:e.change,changeType:e.changeType},e.name)))}),(0,tr.jsxs)("div",{className:"grid grid-cols-1 gap-5 lg:grid-cols-2",children:[(0,tr.jsx)(eg,{title:a("dashboard.recentActivity"),children:(0,tr.jsx)(ng,{maxItems:6})}),(0,tr.jsx)(eg,{title:a("dashboard.alerts"),children:(0,tr.jsxs)("div",{className:"space-y-4",children:[(0,tr.jsxs)("div",{className:"flex items-center space-x-3 p-3 border-l-4 border-l-red-500 bg-red-50 rounded-r-md",children:[(0,tr.jsx)("div",{className:"w-2 h-2 bg-red-400 rounded-full flex-shrink-0"}),(0,tr.jsxs)("div",{className:"flex-1",children:[(0,tr.jsx)("p",{className:"text-sm text-gray-900 font-medium",children:"Vaccination overdue for 3 animals"}),(0,tr.jsx)("p",{className:"text-xs text-red-600",children:"High priority \u2022 Immediate action required"})]})]}),(0,tr.jsxs)("div",{className:"flex items-center space-x-3 p-3 border-l-4 border-l-yellow-500 bg-yellow-50 rounded-r-md",children:[(0,tr.jsx)("div",{className:"w-2 h-2 bg-orange-400 rounded-full flex-shrink-0"}),(0,tr.jsxs)("div",{className:"flex-1",children:[(0,tr.jsx)("p",{className:"text-sm text-gray-900 font-medium",children:"Feed inventory running low"}),(0,tr.jsx)("p",{className:"text-xs text-yellow-600",children:"Medium priority \u2022 Order within 3 days"})]})]}),(0,tr.jsxs)("div",{className:"flex items-center space-x-3 p-3 border-l-4 border-l-blue-500 bg-blue-50 rounded-r-md",children:[(0,tr.jsx)("div",{className:"w-2 h-2 bg-blue-400 rounded-full flex-shrink-0"}),(0,tr.jsxs)("div",{className:"flex-1",children:[(0,tr.jsx)("p",{className:"text-sm text-gray-900 font-medium",children:"Breeding season approaching"}),(0,tr.jsx)("p",{className:"text-xs text-blue-600",children:"Low priority \u2022 Plan for next month"})]})]})]})})]}),(0,tr.jsxs)("div",{className:"grid grid-cols-1 gap-5 lg:grid-cols-2",children:[(0,tr.jsxs)("div",{className:"card",children:[(0,tr.jsx)("div",{className:"card-header",children:(0,tr.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:a("dashboard.animalsBySpecies")})}),(0,tr.jsx)("div",{className:"card-body",children:(0,tr.jsx)("div",{className:"h-64",children:null!==i&&void 0!==i&&i.bySpecies?(0,tr.jsx)("div",{className:"space-y-3",children:Object.entries(i.bySpecies).map((e=>{let[t,n]=e;const r=(n/i.total*100).toFixed(1);return(0,tr.jsxs)("div",{className:"flex items-center justify-between",children:[(0,tr.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,tr.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("cattle"===t?"bg-blue-500":"sheep"===t?"bg-green-500":"goat"===t?"bg-yellow-500":"bg-purple-500")}),(0,tr.jsx)("span",{className:"text-sm text-gray-600 capitalize",children:t})]}),(0,tr.jsxs)("div",{className:"text-right",children:[(0,tr.jsx)("span",{className:"text-sm font-medium text-gray-900",children:n}),(0,tr.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:["(",r,"%)"]})]})]},t)}))}):(0,tr.jsx)("div",{className:"flex items-center justify-center h-full text-gray-500",children:"Loading chart data..."})})})]}),(0,tr.jsxs)("div",{className:"card",children:[(0,tr.jsx)("div",{className:"card-header",children:(0,tr.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:a("dashboard.healthStatusOverview")})}),(0,tr.jsx)("div",{className:"card-body",children:(0,tr.jsx)("div",{className:"h-64",children:null!==i&&void 0!==i&&i.byHealthStatus?(0,tr.jsx)("div",{className:"space-y-3",children:Object.entries(i.byHealthStatus).map((e=>{let[t,n]=e;const r=(n/i.total*100).toFixed(1);return(0,tr.jsxs)("div",{className:"flex items-center justify-between",children:[(0,tr.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,tr.jsx)("div",{className:"w-3 h-3 rounded-full ".concat("healthy"===t?"bg-green-500":"sick"===t?"bg-red-500":"injured"===t?"bg-orange-500":"bg-blue-500")}),(0,tr.jsx)("span",{className:"text-sm text-gray-600 capitalize",children:t})]}),(0,tr.jsxs)("div",{className:"text-right",children:[(0,tr.jsx)("span",{className:"text-sm font-medium text-gray-900",children:n}),(0,tr.jsxs)("span",{className:"text-xs text-gray-500 ml-2",children:["(",r,"%)"]})]})]},t)}))}):(0,tr.jsx)("div",{className:"flex items-center justify-center h-full text-gray-500",children:"Loading chart data..."})})})]})]}),(0,tr.jsxs)("div",{className:"grid grid-cols-1 gap-5 lg:grid-cols-3",children:[(0,tr.jsxs)("div",{className:"card",children:[(0,tr.jsx)("div",{className:"card-header",children:(0,tr.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:a("dashboard.breedingStatus")})}),(0,tr.jsx)("div",{className:"card-body",children:(0,tr.jsx)("div",{className:"space-y-3",children:(null===i||void 0===i?void 0:i.byBreedingStatus)&&Object.entries(i.byBreedingStatus).map((e=>{let[t,n]=e;return(0,tr.jsxs)("div",{className:"flex items-center justify-between",children:[(0,tr.jsx)("span",{className:"text-sm text-gray-600 capitalize",children:t}),(0,tr.jsx)("span",{className:"text-sm font-medium text-gray-900",children:n})]},t)}))})})]}),(0,tr.jsxs)("div",{className:"card",children:[(0,tr.jsx)("div",{className:"card-header",children:(0,tr.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:a("dashboard.monthlyGrowth")})}),(0,tr.jsx)("div",{className:"card-body",children:(0,tr.jsxs)("div",{className:"space-y-3",children:[(0,tr.jsxs)("div",{className:"flex items-center justify-between",children:[(0,tr.jsx)("span",{className:"text-sm text-gray-600",children:"New Births"}),(0,tr.jsx)("span",{className:"text-sm font-medium text-green-600",children:"+12"})]}),(0,tr.jsxs)("div",{className:"flex items-center justify-between",children:[(0,tr.jsx)("span",{className:"text-sm text-gray-600",children:"Acquisitions"}),(0,tr.jsx)("span",{className:"text-sm font-medium text-blue-600",children:"+8"})]}),(0,tr.jsxs)("div",{className:"flex items-center justify-between",children:[(0,tr.jsx)("span",{className:"text-sm text-gray-600",children:"Sales"}),(0,tr.jsx)("span",{className:"text-sm font-medium text-red-600",children:"-5"})]}),(0,tr.jsxs)("div",{className:"flex items-center justify-between",children:[(0,tr.jsx)("span",{className:"text-sm text-gray-600",children:"Net Growth"}),(0,tr.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"+15"})]})]})})]}),(0,tr.jsxs)("div",{className:"card",children:[(0,tr.jsx)("div",{className:"card-header",children:(0,tr.jsx)("h3",{className:"text-lg font-medium text-gray-900",children:a("dashboard.financialSummary")})}),(0,tr.jsx)("div",{className:"card-body",children:(0,tr.jsxs)("div",{className:"space-y-3",children:[(0,tr.jsxs)("div",{className:"flex items-center justify-between",children:[(0,tr.jsx)("span",{className:"text-sm text-gray-600",children:"Monthly Revenue"}),(0,tr.jsx)("span",{className:"text-sm font-medium text-green-600",children:"R 45,200"})]}),(0,tr.jsxs)("div",{className:"flex items-center justify-between",children:[(0,tr.jsx)("span",{className:"text-sm text-gray-600",children:"Feed Costs"}),(0,tr.jsx)("span",{className:"text-sm font-medium text-red-600",children:"R 18,500"})]}),(0,tr.jsxs)("div",{className:"flex items-center justify-between",children:[(0,tr.jsx)("span",{className:"text-sm text-gray-600",children:"Vet Costs"}),(0,tr.jsx)("span",{className:"text-sm font-medium text-red-600",children:"R 3,200"})]}),(0,tr.jsxs)("div",{className:"flex items-center justify-between",children:[(0,tr.jsx)("span",{className:"text-sm text-gray-600",children:"Net Profit"}),(0,tr.jsx)("span",{className:"text-sm font-medium text-gray-900",children:"R 23,500"})]})]})})]})]})]})},ag=()=>{const e=P(),{t:n}=ci();return(0,t.useEffect)((()=>{e(Qf(n("animals.title"))),e(qf([{label:n("navigation.dashboard"),path:"/dashboard"},{label:n("animals.title")}]))}),[e,n]),(0,tr.jsx)("div",{className:"space-y-6",children:(0,tr.jsxs)("div",{className:"card",children:[(0,tr.jsx)("div",{className:"card-header",children:(0,tr.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:n("animals.title")})}),(0,tr.jsx)("div",{className:"card-body",children:(0,tr.jsx)("p",{className:"text-gray-600",children:"Animal management features coming soon..."})})]})})},og=()=>{const{t:e}=ci();return(0,tr.jsx)("div",{className:"space-y-6",children:(0,tr.jsxs)("div",{className:"card",children:[(0,tr.jsx)("div",{className:"card-header",children:(0,tr.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:e("animals.animalDetails")})}),(0,tr.jsx)("div",{className:"card-body",children:(0,tr.jsx)("p",{className:"text-gray-600",children:"Animal details page coming soon..."})})]})})},ig=()=>{const{t:e}=ci();return(0,tr.jsx)("div",{className:"space-y-6",children:(0,tr.jsxs)("div",{className:"card",children:[(0,tr.jsx)("div",{className:"card-header",children:(0,tr.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:e("health.title")})}),(0,tr.jsx)("div",{className:"card-body",children:(0,tr.jsx)("p",{className:"text-gray-600",children:"Health management features coming soon..."})})]})})},sg=()=>{const{t:e}=ci();return(0,tr.jsx)("div",{className:"space-y-6",children:(0,tr.jsxs)("div",{className:"card",children:[(0,tr.jsx)("div",{className:"card-header",children:(0,tr.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:e("breeding.title")})}),(0,tr.jsx)("div",{className:"card-body",children:(0,tr.jsx)("p",{className:"text-gray-600",children:"Breeding management features coming soon..."})})]})})},lg=()=>{const{t:e}=ci();return(0,tr.jsx)("div",{className:"space-y-6",children:(0,tr.jsxs)("div",{className:"card",children:[(0,tr.jsx)("div",{className:"card-header",children:(0,tr.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:e("feeding.title")})}),(0,tr.jsx)("div",{className:"card-body",children:(0,tr.jsx)("p",{className:"text-gray-600",children:"Feed management features coming soon..."})})]})})},ug=()=>{const{t:e}=ci();return(0,tr.jsx)("div",{className:"space-y-6",children:(0,tr.jsxs)("div",{className:"card",children:[(0,tr.jsx)("div",{className:"card-header",children:(0,tr.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:e("financial.title")})}),(0,tr.jsx)("div",{className:"card-body",children:(0,tr.jsx)("p",{className:"text-gray-600",children:"Financial management features coming soon..."})})]})})},cg=()=>{const{t:e}=ci();return(0,tr.jsx)("div",{className:"space-y-6",children:(0,tr.jsxs)("div",{className:"card",children:[(0,tr.jsx)("div",{className:"card-header",children:(0,tr.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:e("reports.title")})}),(0,tr.jsx)("div",{className:"card-body",children:(0,tr.jsx)("p",{className:"text-gray-600",children:"Reports and analytics features coming soon..."})})]})})},dg=()=>{const{t:e}=ci();return(0,tr.jsx)("div",{className:"space-y-6",children:(0,tr.jsxs)("div",{className:"card",children:[(0,tr.jsx)("div",{className:"card-header",children:(0,tr.jsx)("h1",{className:"text-2xl font-bold text-gray-900",children:e("settings.title")})}),(0,tr.jsx)("div",{className:"card-body",children:(0,tr.jsx)("p",{className:"text-gray-600",children:"Settings features coming soon..."})})]})})},fg=()=>{const{t:e}=ci();return(0,tr.jsx)("div",{className:"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8",children:(0,tr.jsx)("div",{className:"sm:mx-auto sm:w-full sm:max-w-md",children:(0,tr.jsxs)("div",{className:"text-center",children:[(0,tr.jsx)("h1",{className:"text-9xl font-bold text-primary-600",children:"404"}),(0,tr.jsx)("h2",{className:"mt-4 text-3xl font-bold text-gray-900",children:"Page not found"}),(0,tr.jsx)("p",{className:"mt-2 text-gray-600",children:"Sorry, we couldn't find the page you're looking for."}),(0,tr.jsx)("div",{className:"mt-6",children:(0,tr.jsx)(nt,{to:"/dashboard",className:"btn-primary",children:"Go back to dashboard"})})]})})})},pg=e=>{let{children:t,requiredRole:n,requiredPermission:r}=e;const a=Se(),{isAuthenticated:o,user:i}=w((e=>e.auth));if(!o)return(0,tr.jsx)(Ue,{to:"/login",state:{from:a},replace:!0});if(n&&i&&!n.includes(i.role))return(0,tr.jsx)(Ue,{to:"/dashboard",replace:!0});if(r&&i&&"admin"!==i.role){var s;if(!(null===(s=i.permissions)||void 0===s?void 0:s.some((e=>e.module===r.module&&e.actions.includes(r.action)))))return(0,tr.jsx)(Ue,{to:"/dashboard",replace:!0})}return(0,tr.jsx)(tr.Fragment,{children:t})};const hg=function(){const e=P(),{i18n:n}=ci(),{isAuthenticated:r,isLoading:a,token:o}=w((e=>e.auth)),{language:i}=w((e=>e.ui));return(0,t.useEffect)((()=>{i&&n.language!==i&&n.changeLanguage(i)}),[i,n]),(0,t.useEffect)((()=>{o&&!r&&e(Sf())}),[e,o,r]),(0,t.useEffect)((()=>{const t=t=>{["en","af","st","tn","zu"].includes(t)&&e(Df(t))};return n.on("languageChanged",t),()=>{n.off("languageChanged",t)}}),[e,n]),a?(0,tr.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,tr.jsx)(_m,{size:"large"})}):(0,tr.jsxs)("div",{className:"App",children:[(0,tr.jsxs)($e,{children:[(0,tr.jsx)(Ve,{path:"/login",element:r?(0,tr.jsx)(Ue,{to:"/dashboard",replace:!0}):(0,tr.jsx)(Om,{children:(0,tr.jsx)(Dm,{})})}),(0,tr.jsx)(Ve,{path:"/register",element:r?(0,tr.jsx)(Ue,{to:"/dashboard",replace:!0}):(0,tr.jsx)(Om,{children:(0,tr.jsx)(Fm,{})})}),(0,tr.jsxs)(Ve,{path:"/",element:(0,tr.jsx)(pg,{children:(0,tr.jsx)(jm,{})}),children:[(0,tr.jsx)(Ve,{index:!0,element:(0,tr.jsx)(Ue,{to:"/dashboard",replace:!0})}),(0,tr.jsx)(Ve,{path:"dashboard",element:(0,tr.jsx)(rg,{})}),(0,tr.jsx)(Ve,{path:"animals",element:(0,tr.jsx)(ag,{})}),(0,tr.jsx)(Ve,{path:"animals/:id",element:(0,tr.jsx)(og,{})}),(0,tr.jsx)(Ve,{path:"health",element:(0,tr.jsx)(ig,{})}),(0,tr.jsx)(Ve,{path:"breeding",element:(0,tr.jsx)(sg,{})}),(0,tr.jsx)(Ve,{path:"feeding",element:(0,tr.jsx)(lg,{})}),(0,tr.jsx)(Ve,{path:"financial",element:(0,tr.jsx)(ug,{})}),(0,tr.jsx)(Ve,{path:"reports",element:(0,tr.jsx)(cg,{})}),(0,tr.jsx)(Ve,{path:"settings",element:(0,tr.jsx)(dg,{})})]}),(0,tr.jsx)(Ve,{path:"*",element:(0,tr.jsx)(fg,{})})]}),(0,tr.jsx)(qs,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,iconTheme:{primary:"#059669",secondary:"#fff"}},error:{duration:5e3,iconTheme:{primary:"#dc2626",secondary:"#fff"}}}})]})},mg=ju({name:"health",initialState:{records:[],alerts:[],selectedRecord:null,isLoading:!1,error:null,statistics:null},reducers:{setRecords:(e,t)=>{e.records=t.payload},addRecord:(e,t)=>{e.records.unshift(t.payload)},updateRecord:(e,t)=>{const n=e.records.findIndex((e=>e._id===t.payload._id));-1!==n&&(e.records[n]=t.payload)},deleteRecord:(e,t)=>{e.records=e.records.filter((e=>e._id!==t.payload))},setAlerts:(e,t)=>{e.alerts=t.payload},addAlert:(e,t)=>{e.alerts.unshift(t.payload)},resolveAlert:(e,t)=>{const n=e.alerts.find((e=>e._id===t.payload.id));n&&(n.isResolved=!0,n.resolvedDate=(new Date).toISOString(),n.resolvedBy=t.payload.resolvedBy,n.resolvedNotes=t.payload.notes)},setSelectedRecord:(e,t)=>{e.selectedRecord=t.payload},setLoading:(e,t)=>{e.isLoading=t.payload},setError:(e,t)=>{e.error=t.payload},clearError:e=>{e.error=null},setStatistics:(e,t)=>{e.statistics=t.payload}}}),{setRecords:gg,addRecord:yg,updateRecord:vg,deleteRecord:bg,setAlerts:wg,addAlert:xg,resolveAlert:kg,setSelectedRecord:Sg,setLoading:Eg,setError:Ng,clearError:Cg,setStatistics:jg}=mg.actions,Og=mg.reducer,Pg=ju({name:"breeding",initialState:{breedingRecords:[],birthRecords:[],heatRecords:[],selectedBreedingRecord:null,selectedBirthRecord:null,isLoading:!1,error:null,statistics:null},reducers:{setBreedingRecords:(e,t)=>{e.breedingRecords=t.payload},addBreedingRecord:(e,t)=>{e.breedingRecords.unshift(t.payload)},updateBreedingRecord:(e,t)=>{const n=e.breedingRecords.findIndex((e=>e._id===t.payload._id));-1!==n&&(e.breedingRecords[n]=t.payload)},deleteBreedingRecord:(e,t)=>{e.breedingRecords=e.breedingRecords.filter((e=>e._id!==t.payload))},setBirthRecords:(e,t)=>{e.birthRecords=t.payload},addBirthRecord:(e,t)=>{e.birthRecords.unshift(t.payload)},updateBirthRecord:(e,t)=>{const n=e.birthRecords.findIndex((e=>e._id===t.payload._id));-1!==n&&(e.birthRecords[n]=t.payload)},deleteBirthRecord:(e,t)=>{e.birthRecords=e.birthRecords.filter((e=>e._id!==t.payload))},setHeatRecords:(e,t)=>{e.heatRecords=t.payload},addHeatRecord:(e,t)=>{e.heatRecords.unshift(t.payload)},updateHeatRecord:(e,t)=>{const n=e.heatRecords.findIndex((e=>e._id===t.payload._id));-1!==n&&(e.heatRecords[n]=t.payload)},deleteHeatRecord:(e,t)=>{e.heatRecords=e.heatRecords.filter((e=>e._id!==t.payload))},setSelectedBreedingRecord:(e,t)=>{e.selectedBreedingRecord=t.payload},setSelectedBirthRecord:(e,t)=>{e.selectedBirthRecord=t.payload},setLoading:(e,t)=>{e.isLoading=t.payload},setError:(e,t)=>{e.error=t.payload},clearError:e=>{e.error=null},setStatistics:(e,t)=>{e.statistics=t.payload}}}),{setBreedingRecords:Rg,addBreedingRecord:Tg,updateBreedingRecord:Ag,deleteBreedingRecord:Lg,setBirthRecords:Ig,addBirthRecord:_g,updateBirthRecord:Dg,deleteBirthRecord:Fg,setHeatRecords:Mg,addHeatRecord:zg,updateHeatRecord:Bg,deleteHeatRecord:Ug,setSelectedBreedingRecord:Hg,setSelectedBirthRecord:Vg,setLoading:Wg,setError:$g,clearError:Kg,setStatistics:qg}=Pg.actions,Qg=function(e){var t,n=Nu(),r=e||{},a=r.reducer,o=void 0===a?void 0:a,i=r.middleware,s=void 0===i?n():i,l=r.devTools,u=void 0===l||l,c=r.preloadedState,d=void 0===c?void 0:c,f=r.enhancers,p=void 0===f?void 0:f;if("function"===typeof o)t=o;else{if(!wu(o))throw new Error('"reducer" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');t=eu(o)}var h=s;"function"===typeof h&&(h=h(n));var m=nu.apply(void 0,h),g=tu;u&&(g=bu(gu({trace:!1},"object"===typeof u&&u)));var y=new Su(m),v=y;return Array.isArray(p)?v=lu([m],p):"function"===typeof p&&(v=p(y)),Zl(t,d,g.apply(void 0,v))}({reducer:{auth:Tf,animals:Zm,health:Og,breeding:Pg.reducer,ui:Gf},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST"]}})}),Gg=Xn({palette:{primary:{main:"#0f766e",light:"#14b8a6",dark:"#134e4a"},secondary:{main:"#374151",light:"#6b7280",dark:"#1f2937"},success:{main:"#059669",light:"#10b981",dark:"#047857"},warning:{main:"#d97706",light:"#f59e0b",dark:"#b45309"},error:{main:"#dc2626",light:"#ef4444",dark:"#b91c1c"},background:{default:"#f9fafb",paper:"#ffffff"}},typography:{fontFamily:'"Inter", "Roboto", "Helvetica", "Arial", sans-serif',h1:{fontWeight:700,fontSize:"2.5rem"},h2:{fontWeight:600,fontSize:"2rem"},h3:{fontWeight:600,fontSize:"1.75rem"},h4:{fontWeight:600,fontSize:"1.5rem"},h5:{fontWeight:600,fontSize:"1.25rem"},h6:{fontWeight:600,fontSize:"1rem"},body1:{fontSize:"1rem",lineHeight:1.5},body2:{fontSize:"0.875rem",lineHeight:1.43}},shape:{borderRadius:8},components:{MuiButton:{styleOverrides:{root:{textTransform:"none",fontWeight:500,borderRadius:8,padding:"8px 16px"}}},MuiCard:{styleOverrides:{root:{boxShadow:"0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)",borderRadius:12}}},MuiTextField:{styleOverrides:{root:{"& .MuiOutlinedInput-root":{borderRadius:8}}}}}});a.createRoot(document.getElementById("root")).render((0,tr.jsx)(t.StrictMode,{children:(0,tr.jsx)(N,{store:Qg,children:(0,tr.jsxs)(Va,{theme:Gg,children:[(0,tr.jsx)(eo,{}),(0,tr.jsx)(Ze,{children:(0,tr.jsx)(hg,{})})]})})}))})()})();
//# sourceMappingURL=main.d9ce9322.js.map