{"ast": null, "code": "import React from'react';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const RegisterPage=()=>{const{t}=useTranslation();return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-2xl font-bold text-gray-900\",children:t('auth.register')}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-gray-600\",children:\"Create your AMPD account\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"bg-blue-50 p-4 rounded-md\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-blue-700\",children:\"Registration is currently disabled. Please contact your administrator for account creation.\"})})]});};export default RegisterPage;", "map": {"version": 3, "names": ["React", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "RegisterPage", "t", "className", "children"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/pages/auth/RegisterPage.tsx"], "sourcesContent": ["import React from 'react';\nimport { useTranslation } from 'react-i18next';\n\nconst RegisterPage: React.FC = () => {\n  const { t } = useTranslation();\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"text-center\">\n        <h2 className=\"text-2xl font-bold text-gray-900\">\n          {t('auth.register')}\n        </h2>\n        <p className=\"mt-2 text-gray-600\">\n          Create your AMPD account\n        </p>\n      </div>\n      \n      <div className=\"bg-blue-50 p-4 rounded-md\">\n        <p className=\"text-sm text-blue-700\">\n          Registration is currently disabled. Please contact your administrator for account creation.\n        </p>\n      </div>\n    </div>\n  );\n};\n\nexport default RegisterPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAE9B,mBACEI,KAAA,QAAKG,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxBJ,KAAA,QAAKG,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BN,IAAA,OAAIK,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC7CF,CAAC,CAAC,eAAe,CAAC,CACjB,CAAC,cACLJ,IAAA,MAAGK,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,0BAElC,CAAG,CAAC,EACD,CAAC,cAENN,IAAA,QAAKK,SAAS,CAAC,2BAA2B,CAAAC,QAAA,cACxCN,IAAA,MAAGK,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAC,6FAErC,CAAG,CAAC,CACD,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}