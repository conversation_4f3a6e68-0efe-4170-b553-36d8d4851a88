{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.tokenCache = exports.AzureCredentialCache = exports.AZURE_BASE_URL = void 0;\nexports.addAzureParams = addAzureParams;\nexports.prepareRequest = prepareRequest;\nexports.fetchAzureKMSToken = fetchAzureKMSToken;\nexports.loadAzureCredentials = loadAzureCredentials;\nconst error_1 = require(\"../../error\");\nconst utils_1 = require(\"../../utils\");\nconst errors_1 = require(\"../errors\");\nconst MINIMUM_TOKEN_REFRESH_IN_MILLISECONDS = 6000;\n/** Base URL for getting Azure tokens. */\nexports.AZURE_BASE_URL = 'http://169.254.169.254/metadata/identity/oauth2/token?';\n/**\n * @internal\n */\nclass AzureCredentialCache {\n  constructor() {\n    this.cachedToken = null;\n  }\n  async getToken() {\n    if (this.cachedToken == null || this.needsRefresh(this.cachedToken)) {\n      this.cachedToken = await this._getToken();\n    }\n    return {\n      accessToken: this.cachedToken.accessToken\n    };\n  }\n  needsRefresh(token) {\n    const timeUntilExpirationMS = token.expiresOnTimestamp - Date.now();\n    return timeUntilExpirationMS <= MINIMUM_TOKEN_REFRESH_IN_MILLISECONDS;\n  }\n  /**\n   * exposed for testing\n   */\n  resetCache() {\n    this.cachedToken = null;\n  }\n  /**\n   * exposed for testing\n   */\n  _getToken() {\n    return fetchAzureKMSToken();\n  }\n}\nexports.AzureCredentialCache = AzureCredentialCache;\n/** @internal */\nexports.tokenCache = new AzureCredentialCache();\n/** @internal */\nasync function parseResponse(response) {\n  const {\n    status,\n    body: rawBody\n  } = response;\n  const body = (() => {\n    try {\n      return JSON.parse(rawBody);\n    } catch {\n      throw new errors_1.MongoCryptAzureKMSRequestError('Malformed JSON body in GET request.');\n    }\n  })();\n  if (status !== 200) {\n    throw new errors_1.MongoCryptAzureKMSRequestError('Unable to complete request.', body);\n  }\n  if (!body.access_token) {\n    throw new errors_1.MongoCryptAzureKMSRequestError('Malformed response body - missing field `access_token`.');\n  }\n  if (!body.expires_in) {\n    throw new errors_1.MongoCryptAzureKMSRequestError('Malformed response body - missing field `expires_in`.');\n  }\n  const expiresInMS = Number(body.expires_in) * 1000;\n  if (Number.isNaN(expiresInMS)) {\n    throw new errors_1.MongoCryptAzureKMSRequestError('Malformed response body - unable to parse int from `expires_in` field.');\n  }\n  return {\n    accessToken: body.access_token,\n    expiresOnTimestamp: Date.now() + expiresInMS\n  };\n}\n/**\n * @internal\n * Get the Azure endpoint URL.\n */\nfunction addAzureParams(url, resource, username) {\n  url.searchParams.append('api-version', '2018-02-01');\n  url.searchParams.append('resource', resource);\n  if (username) {\n    url.searchParams.append('client_id', username);\n  }\n  return url;\n}\n/**\n * @internal\n *\n * parses any options provided by prose tests to `fetchAzureKMSToken` and merges them with\n * the default values for headers and the request url.\n */\nfunction prepareRequest(options) {\n  const url = new URL(options.url?.toString() ?? exports.AZURE_BASE_URL);\n  addAzureParams(url, 'https://vault.azure.net');\n  const headers = {\n    ...options.headers,\n    'Content-Type': 'application/json',\n    Metadata: true\n  };\n  return {\n    headers,\n    url\n  };\n}\n/**\n * @internal\n *\n * `AzureKMSRequestOptions` allows prose tests to modify the http request sent to the idms\n * servers.  This is required to simulate different server conditions.  No options are expected to\n * be set outside of tests.\n *\n * exposed for CSFLE\n * [prose test 18](https://github.com/mongodb/specifications/tree/master/source/client-side-encryption/tests#azure-imds-credentials)\n */\nasync function fetchAzureKMSToken(options = {}) {\n  const {\n    headers,\n    url\n  } = prepareRequest(options);\n  try {\n    const response = await (0, utils_1.get)(url, {\n      headers\n    });\n    return await parseResponse(response);\n  } catch (error) {\n    if (error instanceof error_1.MongoNetworkTimeoutError) {\n      throw new errors_1.MongoCryptAzureKMSRequestError(`[Azure KMS] ${error.message}`);\n    }\n    throw error;\n  }\n}\n/**\n * @internal\n *\n * @throws Will reject with a `MongoCryptError` if the http request fails or the http response is malformed.\n */\nasync function loadAzureCredentials(kmsProviders) {\n  const azure = await exports.tokenCache.getToken();\n  return {\n    ...kmsProviders,\n    azure\n  };\n}", "map": {"version": 3, "names": ["exports", "addAzureParams", "prepareRequest", "fetchAzureKMSToken", "loadAzureCredentials", "error_1", "require", "utils_1", "errors_1", "MINIMUM_TOKEN_REFRESH_IN_MILLISECONDS", "AZURE_BASE_URL", "AzureCredentialCache", "constructor", "cachedToken", "getToken", "needsRefresh", "_getToken", "accessToken", "token", "timeUntilExpirationMS", "expiresOnTimestamp", "Date", "now", "resetCache", "tokenCache", "parseResponse", "response", "status", "body", "rawBody", "JSON", "parse", "MongoCryptAzureKMSRequestError", "access_token", "expires_in", "expiresInMS", "Number", "isNaN", "url", "resource", "username", "searchParams", "append", "options", "URL", "toString", "headers", "<PERSON><PERSON><PERSON>", "get", "error", "MongoNetworkTimeoutError", "message", "kmsProviders", "azure"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\client-side-encryption\\providers\\azure.ts"], "sourcesContent": ["import { type Document } from '../../bson';\nimport { MongoNetworkTimeoutError } from '../../error';\nimport { get } from '../../utils';\nimport { MongoCryptAzureKMSRequestError } from '../errors';\nimport { type KMSProviders } from './index';\n\nconst MINIMUM_TOKEN_REFRESH_IN_MILLISECONDS = 6000;\n/** Base URL for getting Azure tokens. */\nexport const AZURE_BASE_URL = 'http://169.254.169.254/metadata/identity/oauth2/token?';\n\n/**\n * The access token that libmongocrypt expects for Azure kms.\n */\ninterface AccessToken {\n  accessToken: string;\n}\n\n/**\n * The response from the azure idms endpoint, including the `expiresOnTimestamp`.\n * `expiresOnTimestamp` is needed for caching.\n */\ninterface AzureTokenCacheEntry extends AccessToken {\n  accessToken: string;\n  expiresOnTimestamp: number;\n}\n\n/**\n * @internal\n */\nexport class AzureCredentialCache {\n  cachedToken: AzureTokenCacheEntry | null = null;\n\n  async getToken(): Promise<AccessToken> {\n    if (this.cachedToken == null || this.needsRefresh(this.cachedToken)) {\n      this.cachedToken = await this._getToken();\n    }\n\n    return { accessToken: this.cachedToken.accessToken };\n  }\n\n  needsRefresh(token: AzureTokenCacheEntry): boolean {\n    const timeUntilExpirationMS = token.expiresOnTimestamp - Date.now();\n    return timeUntilExpirationMS <= MINIMUM_TOKEN_REFRESH_IN_MILLISECONDS;\n  }\n\n  /**\n   * exposed for testing\n   */\n  resetCache() {\n    this.cachedToken = null;\n  }\n\n  /**\n   * exposed for testing\n   */\n  _getToken(): Promise<AzureTokenCacheEntry> {\n    return fetchAzureKMSToken();\n  }\n}\n\n/** @internal */\nexport const tokenCache = new AzureCredentialCache();\n\n/** @internal */\nasync function parseResponse(response: {\n  body: string;\n  status?: number;\n}): Promise<AzureTokenCacheEntry> {\n  const { status, body: rawBody } = response;\n\n  const body: { expires_in?: number; access_token?: string } = (() => {\n    try {\n      return JSON.parse(rawBody);\n    } catch {\n      throw new MongoCryptAzureKMSRequestError('Malformed JSON body in GET request.');\n    }\n  })();\n\n  if (status !== 200) {\n    throw new MongoCryptAzureKMSRequestError('Unable to complete request.', body);\n  }\n\n  if (!body.access_token) {\n    throw new MongoCryptAzureKMSRequestError(\n      'Malformed response body - missing field `access_token`.'\n    );\n  }\n\n  if (!body.expires_in) {\n    throw new MongoCryptAzureKMSRequestError(\n      'Malformed response body - missing field `expires_in`.'\n    );\n  }\n\n  const expiresInMS = Number(body.expires_in) * 1000;\n  if (Number.isNaN(expiresInMS)) {\n    throw new MongoCryptAzureKMSRequestError(\n      'Malformed response body - unable to parse int from `expires_in` field.'\n    );\n  }\n\n  return {\n    accessToken: body.access_token,\n    expiresOnTimestamp: Date.now() + expiresInMS\n  };\n}\n\n/**\n * @internal\n *\n * exposed for CSFLE\n * [prose test 18](https://github.com/mongodb/specifications/tree/master/source/client-side-encryption/tests#azure-imds-credentials)\n */\nexport interface AzureKMSRequestOptions {\n  headers?: Document;\n  url?: URL | string;\n}\n\n/**\n * @internal\n * Get the Azure endpoint URL.\n */\nexport function addAzureParams(url: URL, resource: string, username?: string): URL {\n  url.searchParams.append('api-version', '2018-02-01');\n  url.searchParams.append('resource', resource);\n  if (username) {\n    url.searchParams.append('client_id', username);\n  }\n  return url;\n}\n\n/**\n * @internal\n *\n * parses any options provided by prose tests to `fetchAzureKMSToken` and merges them with\n * the default values for headers and the request url.\n */\nexport function prepareRequest(options: AzureKMSRequestOptions): {\n  headers: Document;\n  url: URL;\n} {\n  const url = new URL(options.url?.toString() ?? AZURE_BASE_URL);\n  addAzureParams(url, 'https://vault.azure.net');\n  const headers = { ...options.headers, 'Content-Type': 'application/json', Metadata: true };\n  return { headers, url };\n}\n\n/**\n * @internal\n *\n * `AzureKMSRequestOptions` allows prose tests to modify the http request sent to the idms\n * servers.  This is required to simulate different server conditions.  No options are expected to\n * be set outside of tests.\n *\n * exposed for CSFLE\n * [prose test 18](https://github.com/mongodb/specifications/tree/master/source/client-side-encryption/tests#azure-imds-credentials)\n */\nexport async function fetchAzureKMSToken(\n  options: AzureKMSRequestOptions = {}\n): Promise<AzureTokenCacheEntry> {\n  const { headers, url } = prepareRequest(options);\n  try {\n    const response = await get(url, { headers });\n    return await parseResponse(response);\n  } catch (error) {\n    if (error instanceof MongoNetworkTimeoutError) {\n      throw new MongoCryptAzureKMSRequestError(`[Azure KMS] ${error.message}`);\n    }\n    throw error;\n  }\n}\n\n/**\n * @internal\n *\n * @throws Will reject with a `MongoCryptError` if the http request fails or the http response is malformed.\n */\nexport async function loadAzureCredentials(kmsProviders: KMSProviders): Promise<KMSProviders> {\n  const azure = await tokenCache.getToken();\n  return { ...kmsProviders, azure };\n}\n"], "mappings": ";;;;;;AA0HAA,OAAA,CAAAC,cAAA,GAAAA,cAAA;AAeAD,OAAA,CAAAE,cAAA,GAAAA,cAAA;AAoBAF,OAAA,CAAAG,kBAAA,GAAAA,kBAAA;AAoBAH,OAAA,CAAAI,oBAAA,GAAAA,oBAAA;AAhLA,MAAAC,OAAA,GAAAC,OAAA;AACA,MAAAC,OAAA,GAAAD,OAAA;AACA,MAAAE,QAAA,GAAAF,OAAA;AAGA,MAAMG,qCAAqC,GAAG,IAAI;AAClD;AACaT,OAAA,CAAAU,cAAc,GAAG,wDAAwD;AAkBtF;;;AAGA,MAAaC,oBAAoB;EAAjCC,YAAA;IACE,KAAAC,WAAW,GAAgC,IAAI;EA4BjD;EA1BE,MAAMC,QAAQA,CAAA;IACZ,IAAI,IAAI,CAACD,WAAW,IAAI,IAAI,IAAI,IAAI,CAACE,YAAY,CAAC,IAAI,CAACF,WAAW,CAAC,EAAE;MACnE,IAAI,CAACA,WAAW,GAAG,MAAM,IAAI,CAACG,SAAS,EAAE;IAC3C;IAEA,OAAO;MAAEC,WAAW,EAAE,IAAI,CAACJ,WAAW,CAACI;IAAW,CAAE;EACtD;EAEAF,YAAYA,CAACG,KAA2B;IACtC,MAAMC,qBAAqB,GAAGD,KAAK,CAACE,kBAAkB,GAAGC,IAAI,CAACC,GAAG,EAAE;IACnE,OAAOH,qBAAqB,IAAIV,qCAAqC;EACvE;EAEA;;;EAGAc,UAAUA,CAAA;IACR,IAAI,CAACV,WAAW,GAAG,IAAI;EACzB;EAEA;;;EAGAG,SAASA,CAAA;IACP,OAAOb,kBAAkB,EAAE;EAC7B;;AA5BFH,OAAA,CAAAW,oBAAA,GAAAA,oBAAA;AA+BA;AACaX,OAAA,CAAAwB,UAAU,GAAG,IAAIb,oBAAoB,EAAE;AAEpD;AACA,eAAec,aAAaA,CAACC,QAG5B;EACC,MAAM;IAAEC,MAAM;IAAEC,IAAI,EAAEC;EAAO,CAAE,GAAGH,QAAQ;EAE1C,MAAME,IAAI,GAAmD,CAAC,MAAK;IACjE,IAAI;MACF,OAAOE,IAAI,CAACC,KAAK,CAACF,OAAO,CAAC;IAC5B,CAAC,CAAC,MAAM;MACN,MAAM,IAAIrB,QAAA,CAAAwB,8BAA8B,CAAC,qCAAqC,CAAC;IACjF;EACF,CAAC,EAAC,CAAE;EAEJ,IAAIL,MAAM,KAAK,GAAG,EAAE;IAClB,MAAM,IAAInB,QAAA,CAAAwB,8BAA8B,CAAC,6BAA6B,EAAEJ,IAAI,CAAC;EAC/E;EAEA,IAAI,CAACA,IAAI,CAACK,YAAY,EAAE;IACtB,MAAM,IAAIzB,QAAA,CAAAwB,8BAA8B,CACtC,yDAAyD,CAC1D;EACH;EAEA,IAAI,CAACJ,IAAI,CAACM,UAAU,EAAE;IACpB,MAAM,IAAI1B,QAAA,CAAAwB,8BAA8B,CACtC,uDAAuD,CACxD;EACH;EAEA,MAAMG,WAAW,GAAGC,MAAM,CAACR,IAAI,CAACM,UAAU,CAAC,GAAG,IAAI;EAClD,IAAIE,MAAM,CAACC,KAAK,CAACF,WAAW,CAAC,EAAE;IAC7B,MAAM,IAAI3B,QAAA,CAAAwB,8BAA8B,CACtC,wEAAwE,CACzE;EACH;EAEA,OAAO;IACLf,WAAW,EAAEW,IAAI,CAACK,YAAY;IAC9Bb,kBAAkB,EAAEC,IAAI,CAACC,GAAG,EAAE,GAAGa;GAClC;AACH;AAaA;;;;AAIA,SAAgBlC,cAAcA,CAACqC,GAAQ,EAAEC,QAAgB,EAAEC,QAAiB;EAC1EF,GAAG,CAACG,YAAY,CAACC,MAAM,CAAC,aAAa,EAAE,YAAY,CAAC;EACpDJ,GAAG,CAACG,YAAY,CAACC,MAAM,CAAC,UAAU,EAAEH,QAAQ,CAAC;EAC7C,IAAIC,QAAQ,EAAE;IACZF,GAAG,CAACG,YAAY,CAACC,MAAM,CAAC,WAAW,EAAEF,QAAQ,CAAC;EAChD;EACA,OAAOF,GAAG;AACZ;AAEA;;;;;;AAMA,SAAgBpC,cAAcA,CAACyC,OAA+B;EAI5D,MAAML,GAAG,GAAG,IAAIM,GAAG,CAACD,OAAO,CAACL,GAAG,EAAEO,QAAQ,EAAE,IAAI7C,OAAA,CAAAU,cAAc,CAAC;EAC9DT,cAAc,CAACqC,GAAG,EAAE,yBAAyB,CAAC;EAC9C,MAAMQ,OAAO,GAAG;IAAE,GAAGH,OAAO,CAACG,OAAO;IAAE,cAAc,EAAE,kBAAkB;IAAEC,QAAQ,EAAE;EAAI,CAAE;EAC1F,OAAO;IAAED,OAAO;IAAER;EAAG,CAAE;AACzB;AAEA;;;;;;;;;;AAUO,eAAenC,kBAAkBA,CACtCwC,OAAA,GAAkC,EAAE;EAEpC,MAAM;IAAEG,OAAO;IAAER;EAAG,CAAE,GAAGpC,cAAc,CAACyC,OAAO,CAAC;EAChD,IAAI;IACF,MAAMjB,QAAQ,GAAG,MAAM,IAAAnB,OAAA,CAAAyC,GAAG,EAACV,GAAG,EAAE;MAAEQ;IAAO,CAAE,CAAC;IAC5C,OAAO,MAAMrB,aAAa,CAACC,QAAQ,CAAC;EACtC,CAAC,CAAC,OAAOuB,KAAK,EAAE;IACd,IAAIA,KAAK,YAAY5C,OAAA,CAAA6C,wBAAwB,EAAE;MAC7C,MAAM,IAAI1C,QAAA,CAAAwB,8BAA8B,CAAC,eAAeiB,KAAK,CAACE,OAAO,EAAE,CAAC;IAC1E;IACA,MAAMF,KAAK;EACb;AACF;AAEA;;;;;AAKO,eAAe7C,oBAAoBA,CAACgD,YAA0B;EACnE,MAAMC,KAAK,GAAG,MAAMrD,OAAA,CAAAwB,UAAU,CAACV,QAAQ,EAAE;EACzC,OAAO;IAAE,GAAGsC,YAAY;IAAEC;EAAK,CAAE;AACnC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}