{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DropDatabaseOperation = exports.DropCollectionOperation = void 0;\nconst error_1 = require(\"../error\");\nconst command_1 = require(\"./command\");\nconst operation_1 = require(\"./operation\");\n/** @internal */\nclass DropCollectionOperation extends command_1.CommandOperation {\n  constructor(db, name, options = {}) {\n    super(db, options);\n    this.db = db;\n    this.options = options;\n    this.name = name;\n  }\n  get commandName() {\n    return 'drop';\n  }\n  async execute(server, session, timeoutContext) {\n    const db = this.db;\n    const options = this.options;\n    const name = this.name;\n    const encryptedFieldsMap = db.client.s.options.autoEncryption?.encryptedFieldsMap;\n    let encryptedFields = options.encryptedFields ?? encryptedFieldsMap?.[`${db.databaseName}.${name}`];\n    if (!encryptedFields && encryptedFieldsMap) {\n      // If the MongoClient was configured with an encryptedFieldsMap,\n      // and no encryptedFields config was available in it or explicitly\n      // passed as an argument, the spec tells us to look one up using\n      // listCollections().\n      const listCollectionsResult = await db.listCollections({\n        name\n      }, {\n        nameOnly: false\n      }).toArray();\n      encryptedFields = listCollectionsResult?.[0]?.options?.encryptedFields;\n    }\n    if (encryptedFields) {\n      const escCollection = encryptedFields.escCollection || `enxcol_.${name}.esc`;\n      const ecocCollection = encryptedFields.ecocCollection || `enxcol_.${name}.ecoc`;\n      for (const collectionName of [escCollection, ecocCollection]) {\n        // Drop auxilliary collections, ignoring potential NamespaceNotFound errors.\n        const dropOp = new DropCollectionOperation(db, collectionName);\n        try {\n          await dropOp.executeWithoutEncryptedFieldsCheck(server, session, timeoutContext);\n        } catch (err) {\n          if (!(err instanceof error_1.MongoServerError) || err.code !== error_1.MONGODB_ERROR_CODES.NamespaceNotFound) {\n            throw err;\n          }\n        }\n      }\n    }\n    return await this.executeWithoutEncryptedFieldsCheck(server, session, timeoutContext);\n  }\n  async executeWithoutEncryptedFieldsCheck(server, session, timeoutContext) {\n    await super.executeCommand(server, session, {\n      drop: this.name\n    }, timeoutContext);\n    return true;\n  }\n}\nexports.DropCollectionOperation = DropCollectionOperation;\n/** @internal */\nclass DropDatabaseOperation extends command_1.CommandOperation {\n  constructor(db, options) {\n    super(db, options);\n    this.options = options;\n  }\n  get commandName() {\n    return 'dropDatabase';\n  }\n  async execute(server, session, timeoutContext) {\n    await super.executeCommand(server, session, {\n      dropDatabase: 1\n    }, timeoutContext);\n    return true;\n  }\n}\nexports.DropDatabaseOperation = DropDatabaseOperation;\n(0, operation_1.defineAspects)(DropCollectionOperation, [operation_1.Aspect.WRITE_OPERATION]);\n(0, operation_1.defineAspects)(DropDatabaseOperation, [operation_1.Aspect.WRITE_OPERATION]);", "map": {"version": 3, "names": ["error_1", "require", "command_1", "operation_1", "DropCollectionOperation", "CommandOperation", "constructor", "db", "name", "options", "commandName", "execute", "server", "session", "timeoutContext", "encryptedFieldsMap", "client", "s", "autoEncryption", "encryptedFields", "databaseName", "listCollectionsResult", "listCollections", "nameOnly", "toArray", "escCollection", "ecocCollection", "collectionName", "dropOp", "executeWithoutEncryptedFieldsCheck", "err", "MongoServerError", "code", "MONGODB_ERROR_CODES", "NamespaceNotFound", "executeCommand", "drop", "exports", "DropDatabaseOperation", "dropDatabase", "defineAspects", "Aspect", "WRITE_OPERATION"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\drop.ts"], "sourcesContent": ["import type { Document } from '../bson';\nimport type { Db } from '../db';\nimport { MONGODB_ERROR_CODES, MongoServerError } from '../error';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { CommandOperation, type CommandOperationOptions } from './command';\nimport { Aspect, defineAspects } from './operation';\n\n/** @public */\nexport interface DropCollectionOptions extends CommandOperationOptions {\n  /** @experimental */\n  encryptedFields?: Document;\n}\n\n/** @internal */\nexport class DropCollectionOperation extends CommandOperation<boolean> {\n  override options: DropCollectionOptions;\n  db: Db;\n  name: string;\n\n  constructor(db: Db, name: string, options: DropCollectionOptions = {}) {\n    super(db, options);\n    this.db = db;\n    this.options = options;\n    this.name = name;\n  }\n\n  override get commandName() {\n    return 'drop' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<boolean> {\n    const db = this.db;\n    const options = this.options;\n    const name = this.name;\n\n    const encryptedFieldsMap = db.client.s.options.autoEncryption?.encryptedFieldsMap;\n    let encryptedFields: Document | undefined =\n      options.encryptedFields ?? encryptedFieldsMap?.[`${db.databaseName}.${name}`];\n\n    if (!encryptedFields && encryptedFieldsMap) {\n      // If the MongoClient was configured with an encryptedFieldsMap,\n      // and no encryptedFields config was available in it or explicitly\n      // passed as an argument, the spec tells us to look one up using\n      // listCollections().\n      const listCollectionsResult = await db\n        .listCollections({ name }, { nameOnly: false })\n        .toArray();\n      encryptedFields = listCollectionsResult?.[0]?.options?.encryptedFields;\n    }\n\n    if (encryptedFields) {\n      const escCollection = encryptedFields.escCollection || `enxcol_.${name}.esc`;\n      const ecocCollection = encryptedFields.ecocCollection || `enxcol_.${name}.ecoc`;\n\n      for (const collectionName of [escCollection, ecocCollection]) {\n        // Drop auxilliary collections, ignoring potential NamespaceNotFound errors.\n        const dropOp = new DropCollectionOperation(db, collectionName);\n        try {\n          await dropOp.executeWithoutEncryptedFieldsCheck(server, session, timeoutContext);\n        } catch (err) {\n          if (\n            !(err instanceof MongoServerError) ||\n            err.code !== MONGODB_ERROR_CODES.NamespaceNotFound\n          ) {\n            throw err;\n          }\n        }\n      }\n    }\n\n    return await this.executeWithoutEncryptedFieldsCheck(server, session, timeoutContext);\n  }\n\n  private async executeWithoutEncryptedFieldsCheck(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<boolean> {\n    await super.executeCommand(server, session, { drop: this.name }, timeoutContext);\n    return true;\n  }\n}\n\n/** @public */\nexport type DropDatabaseOptions = CommandOperationOptions;\n\n/** @internal */\nexport class DropDatabaseOperation extends CommandOperation<boolean> {\n  override options: DropDatabaseOptions;\n\n  constructor(db: Db, options: DropDatabaseOptions) {\n    super(db, options);\n    this.options = options;\n  }\n  override get commandName() {\n    return 'dropDatabase' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<boolean> {\n    await super.executeCommand(server, session, { dropDatabase: 1 }, timeoutContext);\n    return true;\n  }\n}\n\ndefineAspects(DropCollectionOperation, [Aspect.WRITE_OPERATION]);\ndefineAspects(DropDatabaseOperation, [Aspect.WRITE_OPERATION]);\n"], "mappings": ";;;;;;AAEA,MAAAA,OAAA,GAAAC,OAAA;AAIA,MAAAC,SAAA,GAAAD,OAAA;AACA,MAAAE,WAAA,GAAAF,OAAA;AAQA;AACA,MAAaG,uBAAwB,SAAQF,SAAA,CAAAG,gBAAyB;EAKpEC,YAAYC,EAAM,EAAEC,IAAY,EAAEC,OAAA,GAAiC,EAAE;IACnE,KAAK,CAACF,EAAE,EAAEE,OAAO,CAAC;IAClB,IAAI,CAACF,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,IAAI,GAAGA,IAAI;EAClB;EAEA,IAAaE,WAAWA,CAAA;IACtB,OAAO,MAAe;EACxB;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMP,EAAE,GAAG,IAAI,CAACA,EAAE;IAClB,MAAME,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAMD,IAAI,GAAG,IAAI,CAACA,IAAI;IAEtB,MAAMO,kBAAkB,GAAGR,EAAE,CAACS,MAAM,CAACC,CAAC,CAACR,OAAO,CAACS,cAAc,EAAEH,kBAAkB;IACjF,IAAII,eAAe,GACjBV,OAAO,CAACU,eAAe,IAAIJ,kBAAkB,GAAG,GAAGR,EAAE,CAACa,YAAY,IAAIZ,IAAI,EAAE,CAAC;IAE/E,IAAI,CAACW,eAAe,IAAIJ,kBAAkB,EAAE;MAC1C;MACA;MACA;MACA;MACA,MAAMM,qBAAqB,GAAG,MAAMd,EAAE,CACnCe,eAAe,CAAC;QAAEd;MAAI,CAAE,EAAE;QAAEe,QAAQ,EAAE;MAAK,CAAE,CAAC,CAC9CC,OAAO,EAAE;MACZL,eAAe,GAAGE,qBAAqB,GAAG,CAAC,CAAC,EAAEZ,OAAO,EAAEU,eAAe;IACxE;IAEA,IAAIA,eAAe,EAAE;MACnB,MAAMM,aAAa,GAAGN,eAAe,CAACM,aAAa,IAAI,WAAWjB,IAAI,MAAM;MAC5E,MAAMkB,cAAc,GAAGP,eAAe,CAACO,cAAc,IAAI,WAAWlB,IAAI,OAAO;MAE/E,KAAK,MAAMmB,cAAc,IAAI,CAACF,aAAa,EAAEC,cAAc,CAAC,EAAE;QAC5D;QACA,MAAME,MAAM,GAAG,IAAIxB,uBAAuB,CAACG,EAAE,EAAEoB,cAAc,CAAC;QAC9D,IAAI;UACF,MAAMC,MAAM,CAACC,kCAAkC,CAACjB,MAAM,EAAEC,OAAO,EAAEC,cAAc,CAAC;QAClF,CAAC,CAAC,OAAOgB,GAAG,EAAE;UACZ,IACE,EAAEA,GAAG,YAAY9B,OAAA,CAAA+B,gBAAgB,CAAC,IAClCD,GAAG,CAACE,IAAI,KAAKhC,OAAA,CAAAiC,mBAAmB,CAACC,iBAAiB,EAClD;YACA,MAAMJ,GAAG;UACX;QACF;MACF;IACF;IAEA,OAAO,MAAM,IAAI,CAACD,kCAAkC,CAACjB,MAAM,EAAEC,OAAO,EAAEC,cAAc,CAAC;EACvF;EAEQ,MAAMe,kCAAkCA,CAC9CjB,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAM,KAAK,CAACqB,cAAc,CAACvB,MAAM,EAAEC,OAAO,EAAE;MAAEuB,IAAI,EAAE,IAAI,CAAC5B;IAAI,CAAE,EAAEM,cAAc,CAAC;IAChF,OAAO,IAAI;EACb;;AAtEFuB,OAAA,CAAAjC,uBAAA,GAAAA,uBAAA;AA4EA;AACA,MAAakC,qBAAsB,SAAQpC,SAAA,CAAAG,gBAAyB;EAGlEC,YAAYC,EAAM,EAAEE,OAA4B;IAC9C,KAAK,CAACF,EAAE,EAAEE,OAAO,CAAC;IAClB,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;EACA,IAAaC,WAAWA,CAAA;IACtB,OAAO,cAAuB;EAChC;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAM,KAAK,CAACqB,cAAc,CAACvB,MAAM,EAAEC,OAAO,EAAE;MAAE0B,YAAY,EAAE;IAAC,CAAE,EAAEzB,cAAc,CAAC;IAChF,OAAO,IAAI;EACb;;AAlBFuB,OAAA,CAAAC,qBAAA,GAAAA,qBAAA;AAqBA,IAAAnC,WAAA,CAAAqC,aAAa,EAACpC,uBAAuB,EAAE,CAACD,WAAA,CAAAsC,MAAM,CAACC,eAAe,CAAC,CAAC;AAChE,IAAAvC,WAAA,CAAAqC,aAAa,EAACF,qBAAqB,EAAE,CAACnC,WAAA,CAAAsC,MAAM,CAACC,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}