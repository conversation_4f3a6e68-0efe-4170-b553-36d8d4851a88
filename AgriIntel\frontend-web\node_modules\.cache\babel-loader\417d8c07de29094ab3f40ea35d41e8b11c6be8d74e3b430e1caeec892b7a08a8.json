{"ast": null, "code": "var o;\nimport t from \"react\";\nimport { env as r } from '../utils/env.js';\nimport { useIsoMorphicEffect as d } from './use-iso-morphic-effect.js';\nimport { useServerHandoffComplete as f } from './use-server-handoff-complete.js';\nlet I = (o = t.useId) != null ? o : function () {\n  let n = f(),\n    [e, u] = t.useState(n ? () => r.nextId() : null);\n  return d(() => {\n    e === null && u(r.nextId());\n  }, [e]), e != null ? \"\" + e : void 0;\n};\nexport { I as useId };", "map": {"version": 3, "names": ["o", "t", "env", "r", "useIsoMorphicEffect", "d", "useServerHandoffComplete", "f", "I", "useId", "n", "e", "u", "useState", "nextId"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/hooks/use-id.js"], "sourcesContent": ["var o;import t from\"react\";import{env as r}from'../utils/env.js';import{useIsoMorphicEffect as d}from'./use-iso-morphic-effect.js';import{useServerHandoffComplete as f}from'./use-server-handoff-complete.js';let I=(o=t.useId)!=null?o:function(){let n=f(),[e,u]=t.useState(n?()=>r.nextId():null);return d(()=>{e===null&&u(r.nextId())},[e]),e!=null?\"\"+e:void 0};export{I as useId};\n"], "mappings": "AAAA,IAAIA,CAAC;AAAC,OAAOC,CAAC,MAAK,OAAO;AAAC,SAAOC,GAAG,IAAIC,CAAC,QAAK,iBAAiB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAAOC,wBAAwB,IAAIC,CAAC,QAAK,kCAAkC;AAAC,IAAIC,CAAC,GAAC,CAACR,CAAC,GAACC,CAAC,CAACQ,KAAK,KAAG,IAAI,GAACT,CAAC,GAAC,YAAU;EAAC,IAAIU,CAAC,GAACH,CAAC,CAAC,CAAC;IAAC,CAACI,CAAC,EAACC,CAAC,CAAC,GAACX,CAAC,CAACY,QAAQ,CAACH,CAAC,GAAC,MAAIP,CAAC,CAACW,MAAM,CAAC,CAAC,GAAC,IAAI,CAAC;EAAC,OAAOT,CAAC,CAAC,MAAI;IAACM,CAAC,KAAG,IAAI,IAAEC,CAAC,CAACT,CAAC,CAACW,MAAM,CAAC,CAAC,CAAC;EAAA,CAAC,EAAC,CAACH,CAAC,CAAC,CAAC,EAACA,CAAC,IAAE,IAAI,GAAC,EAAE,GAACA,CAAC,GAAC,KAAK,CAAC;AAAA,CAAC;AAAC,SAAOH,CAAC,IAAIC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}