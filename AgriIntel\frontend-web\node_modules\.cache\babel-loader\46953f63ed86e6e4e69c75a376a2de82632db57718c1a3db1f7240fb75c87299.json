{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\n__exportStar(require(\"./index\"), exports);\n/**\n * @internal\n *\n * Since we don't bundle tslib helpers, we need to polyfill this method.\n *\n * This is used in the generated JS.  Adapted from https://github.com/microsoft/TypeScript/blob/aafdfe5b3f76f5c41abeec412ce73c86da94c75f/src/compiler/factory/emitHelpers.ts#L1202.\n */\nfunction __exportStar(mod) {\n  for (const key of Object.keys(mod)) {\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return mod[key];\n      }\n    });\n  }\n}", "map": {"version": 3, "names": ["__exportStar", "require", "exports", "mod", "key", "Object", "keys", "defineProperty", "enumerable", "get"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\beta.ts"], "sourcesContent": ["import { type Document } from './bson';\n\nexport * from './index';\n\n/**\n * @internal\n *\n * Since we don't bundle tslib helpers, we need to polyfill this method.\n *\n * This is used in the generated JS.  Adapted from https://github.com/microsoft/TypeScript/blob/aafdfe5b3f76f5c41abeec412ce73c86da94c75f/src/compiler/factory/emitHelpers.ts#L1202.\n */\n\nfunction __exportStar(mod: Document) {\n  for (const key of Object.keys(mod)) {\n    Object.defineProperty(exports, key, {\n      enumerable: true,\n      get: function () {\n        return mod[key];\n      }\n    });\n  }\n}\n"], "mappings": ";;;;;AAEAA,YAAA,CAAAC,OAAA,aAAAC,OAAA;AAEA;;;;;;;AAQA,SAASF,YAAYA,CAACG,GAAa;EACjC,KAAK,MAAMC,GAAG,IAAIC,MAAM,CAACC,IAAI,CAACH,GAAG,CAAC,EAAE;IAClCE,MAAM,CAACE,cAAc,CAACL,OAAO,EAAEE,GAAG,EAAE;MAClCI,UAAU,EAAE,IAAI;MAChBC,GAAG,EAAE,SAAAA,CAAA;QACH,OAAON,GAAG,CAACC,GAAG,CAAC;MACjB;KACD,CAAC;EACJ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}