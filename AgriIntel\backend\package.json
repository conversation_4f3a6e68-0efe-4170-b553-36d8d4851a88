{"name": "ampd-livestock-api", "version": "1.0.0", "description": "API for AMPD Livestock Management System", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "migrate": "node scripts/run-migration.js", "populate-mongodb": "node scripts/populate-mongodb.js"}, "keywords": ["livestock", "management", "api", "mongodb"], "author": "AMPD Team", "license": "MIT", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "body-parser": "^2.2.0", "cors": "^2.8.5", "csv-writer": "^1.6.0", "dotenv": "^16.4.7", "exceljs": "^4.3.0", "express": "^4.21.2", "express-validator": "^7.0.1", "helmet": "^6.1.5", "joi": "^17.9.2", "jsonwebtoken": "^9.0.0", "mongoose": "^8.14.1", "multer": "^1.4.5", "node-cron": "^3.0.3", "nodemailer": "^6.9.3", "pdfkit": "^0.13.0", "socket.io": "^4.7.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^4.6.2", "winston": "^3.8.2"}, "devDependencies": {"jest": "^29.5.0", "nodemon": "^2.0.22", "supertest": "^6.3.3"}}