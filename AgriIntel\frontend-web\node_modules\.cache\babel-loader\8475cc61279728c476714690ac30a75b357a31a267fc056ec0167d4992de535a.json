{"ast": null, "code": "\"use strict\";\n\nconst URL = require(\"./lib/URL\");\nconst URLSearchParams = require(\"./lib/URLSearchParams\");\nexports.URL = URL;\nexports.URLSearchParams = URLSearchParams;", "map": {"version": 3, "names": ["URL", "require", "URLSearchParams", "exports"], "sources": ["C:/Users/<USER>/node_modules/mongodb-connection-string-url/node_modules/whatwg-url/webidl2js-wrapper.js"], "sourcesContent": ["\"use strict\";\n\nconst URL = require(\"./lib/URL\");\nconst URLSearchParams = require(\"./lib/URLSearchParams\");\n\nexports.URL = URL;\nexports.URLSearchParams = URLSearchParams;\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,GAAG,GAAGC,OAAO,CAAC,WAAW,CAAC;AAChC,MAAMC,eAAe,GAAGD,OAAO,CAAC,uBAAuB,CAAC;AAExDE,OAAO,CAACH,GAAG,GAAGA,GAAG;AACjBG,OAAO,CAACD,eAAe,GAAGA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}