{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nconst initialState = {\n  records: [],\n  alerts: [],\n  selectedRecord: null,\n  isLoading: false,\n  error: null,\n  statistics: null\n};\nconst healthSlice = createSlice({\n  name: 'health',\n  initialState,\n  reducers: {\n    setRecords: (state, action) => {\n      state.records = action.payload;\n    },\n    addRecord: (state, action) => {\n      state.records.unshift(action.payload);\n    },\n    updateRecord: (state, action) => {\n      const index = state.records.findIndex(record => record._id === action.payload._id);\n      if (index !== -1) {\n        state.records[index] = action.payload;\n      }\n    },\n    deleteRecord: (state, action) => {\n      state.records = state.records.filter(record => record._id !== action.payload);\n    },\n    setAlerts: (state, action) => {\n      state.alerts = action.payload;\n    },\n    addAlert: (state, action) => {\n      state.alerts.unshift(action.payload);\n    },\n    resolveAlert: (state, action) => {\n      const alert = state.alerts.find(a => a._id === action.payload.id);\n      if (alert) {\n        alert.isResolved = true;\n        alert.resolvedDate = new Date().toISOString();\n        alert.resolvedBy = action.payload.resolvedBy;\n        alert.resolvedNotes = action.payload.notes;\n      }\n    },\n    setSelectedRecord: (state, action) => {\n      state.selectedRecord = action.payload;\n    },\n    setLoading: (state, action) => {\n      state.isLoading = action.payload;\n    },\n    setError: (state, action) => {\n      state.error = action.payload;\n    },\n    clearError: state => {\n      state.error = null;\n    },\n    setStatistics: (state, action) => {\n      state.statistics = action.payload;\n    }\n  }\n});\nexport const {\n  setRecords,\n  addRecord,\n  updateRecord,\n  deleteRecord,\n  setAlerts,\n  addAlert,\n  resolveAlert,\n  setSelectedRecord,\n  setLoading,\n  setError,\n  clearError,\n  setStatistics\n} = healthSlice.actions;\nexport default healthSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "records", "alerts", "<PERSON><PERSON><PERSON><PERSON>", "isLoading", "error", "statistics", "healthSlice", "name", "reducers", "setRecords", "state", "action", "payload", "addRecord", "unshift", "updateRecord", "index", "findIndex", "record", "_id", "deleteRecord", "filter", "<PERSON><PERSON><PERSON><PERSON>", "add<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "alert", "find", "a", "id", "isResolved", "resolvedDate", "Date", "toISOString", "resolvedBy", "resolvedNotes", "notes", "setSelectedRecord", "setLoading", "setError", "clearError", "setStatistics", "actions", "reducer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/store/slices/healthSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\n\nexport interface HealthRecord {\n  _id: string;\n  animal: string;\n  recordType: 'vaccination' | 'treatment' | 'checkup' | 'illness' | 'injury' | 'surgery' | 'test';\n  date: string;\n  veterinarian?: string;\n  vaccine?: {\n    name: string;\n    manufacturer?: string;\n    batchNumber?: string;\n    expirationDate?: string;\n    dosage?: string;\n    route?: 'intramuscular' | 'subcutaneous' | 'oral' | 'nasal' | 'intravenous';\n  };\n  treatment?: {\n    condition: string;\n    medication: string;\n    dosage: string;\n    frequency: string;\n    duration: string;\n    withdrawalPeriod?: number;\n    cost?: number;\n  };\n  symptoms?: string[];\n  diagnosis?: string;\n  temperature?: number;\n  heartRate?: number;\n  respiratoryRate?: number;\n  bodyConditionScore?: number;\n  testResults?: Array<{\n    testName: string;\n    result: string;\n    normalRange?: string;\n    units?: string;\n    labName?: string;\n  }>;\n  followUpRequired: boolean;\n  followUpDate?: string;\n  followUpNotes?: string;\n  images?: Array<{\n    url: string;\n    caption?: string;\n  }>;\n  documents?: Array<{\n    name: string;\n    url: string;\n    type: string;\n  }>;\n  notes?: string;\n  cost?: number;\n  createdBy: string;\n  updatedBy?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface HealthAlert {\n  _id: string;\n  animal: string;\n  alertType: 'vaccination_due' | 'treatment_due' | 'checkup_due' | 'withdrawal_period' | 'health_concern' | 'medication_expiry';\n  priority: 'low' | 'medium' | 'high' | 'critical';\n  title: string;\n  description?: string;\n  dueDate?: string;\n  isResolved: boolean;\n  resolvedDate?: string;\n  resolvedBy?: string;\n  resolvedNotes?: string;\n  relatedHealthRecord?: string;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n  daysUntilDue?: number;\n}\n\nexport interface HealthState {\n  records: HealthRecord[];\n  alerts: HealthAlert[];\n  selectedRecord: HealthRecord | null;\n  isLoading: boolean;\n  error: string | null;\n  statistics: {\n    totalRecords: number;\n    recordsByType: Record<string, number>;\n    alertsByPriority: Record<string, number>;\n    upcomingVaccinations: number;\n    activeAlerts: number;\n  } | null;\n}\n\nconst initialState: HealthState = {\n  records: [],\n  alerts: [],\n  selectedRecord: null,\n  isLoading: false,\n  error: null,\n  statistics: null,\n};\n\nconst healthSlice = createSlice({\n  name: 'health',\n  initialState,\n  reducers: {\n    setRecords: (state, action: PayloadAction<HealthRecord[]>) => {\n      state.records = action.payload;\n    },\n    addRecord: (state, action: PayloadAction<HealthRecord>) => {\n      state.records.unshift(action.payload);\n    },\n    updateRecord: (state, action: PayloadAction<HealthRecord>) => {\n      const index = state.records.findIndex(record => record._id === action.payload._id);\n      if (index !== -1) {\n        state.records[index] = action.payload;\n      }\n    },\n    deleteRecord: (state, action: PayloadAction<string>) => {\n      state.records = state.records.filter(record => record._id !== action.payload);\n    },\n    setAlerts: (state, action: PayloadAction<HealthAlert[]>) => {\n      state.alerts = action.payload;\n    },\n    addAlert: (state, action: PayloadAction<HealthAlert>) => {\n      state.alerts.unshift(action.payload);\n    },\n    resolveAlert: (state, action: PayloadAction<{ id: string; resolvedBy: string; notes?: string }>) => {\n      const alert = state.alerts.find(a => a._id === action.payload.id);\n      if (alert) {\n        alert.isResolved = true;\n        alert.resolvedDate = new Date().toISOString();\n        alert.resolvedBy = action.payload.resolvedBy;\n        alert.resolvedNotes = action.payload.notes;\n      }\n    },\n    setSelectedRecord: (state, action: PayloadAction<HealthRecord | null>) => {\n      state.selectedRecord = action.payload;\n    },\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.isLoading = action.payload;\n    },\n    setError: (state, action: PayloadAction<string | null>) => {\n      state.error = action.payload;\n    },\n    clearError: (state) => {\n      state.error = null;\n    },\n    setStatistics: (state, action: PayloadAction<HealthState['statistics']>) => {\n      state.statistics = action.payload;\n    },\n  },\n});\n\nexport const {\n  setRecords,\n  addRecord,\n  updateRecord,\n  deleteRecord,\n  setAlerts,\n  addAlert,\n  resolveAlert,\n  setSelectedRecord,\n  setLoading,\n  setError,\n  clearError,\n  setStatistics,\n} = healthSlice.actions;\n\nexport default healthSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AA4F7D,MAAMC,YAAyB,GAAG;EAChCC,OAAO,EAAE,EAAE;EACXC,MAAM,EAAE,EAAE;EACVC,cAAc,EAAE,IAAI;EACpBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE;AACd,CAAC;AAED,MAAMC,WAAW,GAAGR,WAAW,CAAC;EAC9BS,IAAI,EAAE,QAAQ;EACdR,YAAY;EACZS,QAAQ,EAAE;IACRC,UAAU,EAAEA,CAACC,KAAK,EAAEC,MAAqC,KAAK;MAC5DD,KAAK,CAACV,OAAO,GAAGW,MAAM,CAACC,OAAO;IAChC,CAAC;IACDC,SAAS,EAAEA,CAACH,KAAK,EAAEC,MAAmC,KAAK;MACzDD,KAAK,CAACV,OAAO,CAACc,OAAO,CAACH,MAAM,CAACC,OAAO,CAAC;IACvC,CAAC;IACDG,YAAY,EAAEA,CAACL,KAAK,EAAEC,MAAmC,KAAK;MAC5D,MAAMK,KAAK,GAAGN,KAAK,CAACV,OAAO,CAACiB,SAAS,CAACC,MAAM,IAAIA,MAAM,CAACC,GAAG,KAAKR,MAAM,CAACC,OAAO,CAACO,GAAG,CAAC;MAClF,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBN,KAAK,CAACV,OAAO,CAACgB,KAAK,CAAC,GAAGL,MAAM,CAACC,OAAO;MACvC;IACF,CAAC;IACDQ,YAAY,EAAEA,CAACV,KAAK,EAAEC,MAA6B,KAAK;MACtDD,KAAK,CAACV,OAAO,GAAGU,KAAK,CAACV,OAAO,CAACqB,MAAM,CAACH,MAAM,IAAIA,MAAM,CAACC,GAAG,KAAKR,MAAM,CAACC,OAAO,CAAC;IAC/E,CAAC;IACDU,SAAS,EAAEA,CAACZ,KAAK,EAAEC,MAAoC,KAAK;MAC1DD,KAAK,CAACT,MAAM,GAAGU,MAAM,CAACC,OAAO;IAC/B,CAAC;IACDW,QAAQ,EAAEA,CAACb,KAAK,EAAEC,MAAkC,KAAK;MACvDD,KAAK,CAACT,MAAM,CAACa,OAAO,CAACH,MAAM,CAACC,OAAO,CAAC;IACtC,CAAC;IACDY,YAAY,EAAEA,CAACd,KAAK,EAAEC,MAAyE,KAAK;MAClG,MAAMc,KAAK,GAAGf,KAAK,CAACT,MAAM,CAACyB,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACR,GAAG,KAAKR,MAAM,CAACC,OAAO,CAACgB,EAAE,CAAC;MACjE,IAAIH,KAAK,EAAE;QACTA,KAAK,CAACI,UAAU,GAAG,IAAI;QACvBJ,KAAK,CAACK,YAAY,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC;QAC7CP,KAAK,CAACQ,UAAU,GAAGtB,MAAM,CAACC,OAAO,CAACqB,UAAU;QAC5CR,KAAK,CAACS,aAAa,GAAGvB,MAAM,CAACC,OAAO,CAACuB,KAAK;MAC5C;IACF,CAAC;IACDC,iBAAiB,EAAEA,CAAC1B,KAAK,EAAEC,MAA0C,KAAK;MACxED,KAAK,CAACR,cAAc,GAAGS,MAAM,CAACC,OAAO;IACvC,CAAC;IACDyB,UAAU,EAAEA,CAAC3B,KAAK,EAAEC,MAA8B,KAAK;MACrDD,KAAK,CAACP,SAAS,GAAGQ,MAAM,CAACC,OAAO;IAClC,CAAC;IACD0B,QAAQ,EAAEA,CAAC5B,KAAK,EAAEC,MAAoC,KAAK;MACzDD,KAAK,CAACN,KAAK,GAAGO,MAAM,CAACC,OAAO;IAC9B,CAAC;IACD2B,UAAU,EAAG7B,KAAK,IAAK;MACrBA,KAAK,CAACN,KAAK,GAAG,IAAI;IACpB,CAAC;IACDoC,aAAa,EAAEA,CAAC9B,KAAK,EAAEC,MAAgD,KAAK;MAC1ED,KAAK,CAACL,UAAU,GAAGM,MAAM,CAACC,OAAO;IACnC;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACXH,UAAU;EACVI,SAAS;EACTE,YAAY;EACZK,YAAY;EACZE,SAAS;EACTC,QAAQ;EACRC,YAAY;EACZY,iBAAiB;EACjBC,UAAU;EACVC,QAAQ;EACRC,UAAU;EACVC;AACF,CAAC,GAAGlC,WAAW,CAACmC,OAAO;AAEvB,eAAenC,WAAW,CAACoC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}