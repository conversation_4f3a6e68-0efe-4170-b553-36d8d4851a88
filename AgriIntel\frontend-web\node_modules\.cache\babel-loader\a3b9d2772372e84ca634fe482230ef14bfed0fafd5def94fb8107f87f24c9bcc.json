{"ast": null, "code": "import * as React from 'react';\nimport { ReactReduxContext } from './Context';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\nfunction Provider(_ref) {\n  let {\n    store,\n    context,\n    children,\n    serverState,\n    stabilityCheck = 'once',\n    noopCheck = 'once'\n  } = _ref;\n  const contextValue = React.useMemo(() => {\n    const subscription = createSubscription(store);\n    return {\n      store,\n      subscription,\n      getServerState: serverState ? () => serverState : undefined,\n      stabilityCheck,\n      noopCheck\n    };\n  }, [store, serverState, stabilityCheck, noopCheck]);\n  const previousState = React.useMemo(() => store.getState(), [store]);\n  useIsomorphicLayoutEffect(() => {\n    const {\n      subscription\n    } = contextValue;\n    subscription.onStateChange = subscription.notifyNestedSubs;\n    subscription.trySubscribe();\n    if (previousState !== store.getState()) {\n      subscription.notifyNestedSubs();\n    }\n    return () => {\n      subscription.tryUnsubscribe();\n      subscription.onStateChange = undefined;\n    };\n  }, [contextValue, previousState]);\n  const Context = context || ReactReduxContext; // @ts-ignore 'AnyAction' is assignable to the constraint of type 'A', but 'A' could be instantiated with a different subtype\n\n  return /*#__PURE__*/React.createElement(Context.Provider, {\n    value: contextValue\n  }, children);\n}\nexport default Provider;", "map": {"version": 3, "names": ["React", "ReactReduxContext", "createSubscription", "useIsomorphicLayoutEffect", "Provider", "_ref", "store", "context", "children", "serverState", "stabilityCheck", "<PERSON>op<PERSON><PERSON><PERSON>", "contextValue", "useMemo", "subscription", "getServerState", "undefined", "previousState", "getState", "onStateChange", "notifyNestedSubs", "trySubscribe", "tryUnsubscribe", "Context", "createElement", "value"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-redux/es/components/Provider.js"], "sourcesContent": ["import * as React from 'react';\nimport { ReactReduxContext } from './Context';\nimport { createSubscription } from '../utils/Subscription';\nimport { useIsomorphicLayoutEffect } from '../utils/useIsomorphicLayoutEffect';\n\nfunction Provider({\n  store,\n  context,\n  children,\n  serverState,\n  stabilityCheck = 'once',\n  noopCheck = 'once'\n}) {\n  const contextValue = React.useMemo(() => {\n    const subscription = createSubscription(store);\n    return {\n      store,\n      subscription,\n      getServerState: serverState ? () => serverState : undefined,\n      stabilityCheck,\n      noopCheck\n    };\n  }, [store, serverState, stabilityCheck, noopCheck]);\n  const previousState = React.useMemo(() => store.getState(), [store]);\n  useIsomorphicLayoutEffect(() => {\n    const {\n      subscription\n    } = contextValue;\n    subscription.onStateChange = subscription.notifyNestedSubs;\n    subscription.trySubscribe();\n\n    if (previousState !== store.getState()) {\n      subscription.notifyNestedSubs();\n    }\n\n    return () => {\n      subscription.tryUnsubscribe();\n      subscription.onStateChange = undefined;\n    };\n  }, [contextValue, previousState]);\n  const Context = context || ReactReduxContext; // @ts-ignore 'AnyAction' is assignable to the constraint of type 'A', but 'A' could be instantiated with a different subtype\n\n  return /*#__PURE__*/React.createElement(Context.Provider, {\n    value: contextValue\n  }, children);\n}\n\nexport default Provider;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,iBAAiB,QAAQ,WAAW;AAC7C,SAASC,kBAAkB,QAAQ,uBAAuB;AAC1D,SAASC,yBAAyB,QAAQ,oCAAoC;AAE9E,SAASC,QAAQA,CAAAC,IAAA,EAOd;EAAA,IAPe;IAChBC,KAAK;IACLC,OAAO;IACPC,QAAQ;IACRC,WAAW;IACXC,cAAc,GAAG,MAAM;IACvBC,SAAS,GAAG;EACd,CAAC,GAAAN,IAAA;EACC,MAAMO,YAAY,GAAGZ,KAAK,CAACa,OAAO,CAAC,MAAM;IACvC,MAAMC,YAAY,GAAGZ,kBAAkB,CAACI,KAAK,CAAC;IAC9C,OAAO;MACLA,KAAK;MACLQ,YAAY;MACZC,cAAc,EAAEN,WAAW,GAAG,MAAMA,WAAW,GAAGO,SAAS;MAC3DN,cAAc;MACdC;IACF,CAAC;EACH,CAAC,EAAE,CAACL,KAAK,EAAEG,WAAW,EAAEC,cAAc,EAAEC,SAAS,CAAC,CAAC;EACnD,MAAMM,aAAa,GAAGjB,KAAK,CAACa,OAAO,CAAC,MAAMP,KAAK,CAACY,QAAQ,CAAC,CAAC,EAAE,CAACZ,KAAK,CAAC,CAAC;EACpEH,yBAAyB,CAAC,MAAM;IAC9B,MAAM;MACJW;IACF,CAAC,GAAGF,YAAY;IAChBE,YAAY,CAACK,aAAa,GAAGL,YAAY,CAACM,gBAAgB;IAC1DN,YAAY,CAACO,YAAY,CAAC,CAAC;IAE3B,IAAIJ,aAAa,KAAKX,KAAK,CAACY,QAAQ,CAAC,CAAC,EAAE;MACtCJ,YAAY,CAACM,gBAAgB,CAAC,CAAC;IACjC;IAEA,OAAO,MAAM;MACXN,YAAY,CAACQ,cAAc,CAAC,CAAC;MAC7BR,YAAY,CAACK,aAAa,GAAGH,SAAS;IACxC,CAAC;EACH,CAAC,EAAE,CAACJ,YAAY,EAAEK,aAAa,CAAC,CAAC;EACjC,MAAMM,OAAO,GAAGhB,OAAO,IAAIN,iBAAiB,CAAC,CAAC;;EAE9C,OAAO,aAAaD,KAAK,CAACwB,aAAa,CAACD,OAAO,CAACnB,QAAQ,EAAE;IACxDqB,KAAK,EAAEb;EACT,CAAC,EAAEJ,QAAQ,CAAC;AACd;AAEA,eAAeJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}