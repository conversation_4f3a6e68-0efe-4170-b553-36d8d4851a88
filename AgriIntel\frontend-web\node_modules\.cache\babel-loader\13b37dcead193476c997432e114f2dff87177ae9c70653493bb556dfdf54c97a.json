{"ast": null, "code": "\"use strict\";\n\nfunction makeException(ErrorType, message, options) {\n  if (options.globals) {\n    ErrorType = options.globals[ErrorType.name];\n  }\n  return new ErrorType(`${options.context ? options.context : \"Value\"} ${message}.`);\n}\nfunction toNumber(value, options) {\n  if (typeof value === \"bigint\") {\n    throw makeException(TypeError, \"is a BigInt which cannot be converted to a number\", options);\n  }\n  if (!options.globals) {\n    return Number(value);\n  }\n  return options.globals.Number(value);\n}\n\n// Round x to the nearest integer, choosing the even integer if it lies halfway between two.\nfunction evenRound(x) {\n  // There are four cases for numbers with fractional part being .5:\n  //\n  // case |     x     | floor(x) | round(x) | expected | x <> 0 | x % 1 | x & 1 |   example\n  //   1  |  2n + 0.5 |  2n      |  2n + 1  |  2n      |   >    |  0.5  |   0   |  0.5 ->  0\n  //   2  |  2n + 1.5 |  2n + 1  |  2n + 2  |  2n + 2  |   >    |  0.5  |   1   |  1.5 ->  2\n  //   3  | -2n - 0.5 | -2n - 1  | -2n      | -2n      |   <    | -0.5  |   0   | -0.5 ->  0\n  //   4  | -2n - 1.5 | -2n - 2  | -2n - 1  | -2n - 2  |   <    | -0.5  |   1   | -1.5 -> -2\n  // (where n is a non-negative integer)\n  //\n  // Branch here for cases 1 and 4\n  if (x > 0 && x % 1 === +0.5 && (x & 1) === 0 || x < 0 && x % 1 === -0.5 && (x & 1) === 1) {\n    return censorNegativeZero(Math.floor(x));\n  }\n  return censorNegativeZero(Math.round(x));\n}\nfunction integerPart(n) {\n  return censorNegativeZero(Math.trunc(n));\n}\nfunction sign(x) {\n  return x < 0 ? -1 : 1;\n}\nfunction modulo(x, y) {\n  // https://tc39.github.io/ecma262/#eqn-modulo\n  // Note that http://stackoverflow.com/a/4467559/3191 does NOT work for large modulos\n  const signMightNotMatch = x % y;\n  if (sign(y) !== sign(signMightNotMatch)) {\n    return signMightNotMatch + y;\n  }\n  return signMightNotMatch;\n}\nfunction censorNegativeZero(x) {\n  return x === 0 ? 0 : x;\n}\nfunction createIntegerConversion(bitLength, {\n  unsigned\n}) {\n  let lowerBound, upperBound;\n  if (unsigned) {\n    lowerBound = 0;\n    upperBound = 2 ** bitLength - 1;\n  } else {\n    lowerBound = -(2 ** (bitLength - 1));\n    upperBound = 2 ** (bitLength - 1) - 1;\n  }\n  const twoToTheBitLength = 2 ** bitLength;\n  const twoToOneLessThanTheBitLength = 2 ** (bitLength - 1);\n  return (value, options = {}) => {\n    let x = toNumber(value, options);\n    x = censorNegativeZero(x);\n    if (options.enforceRange) {\n      if (!Number.isFinite(x)) {\n        throw makeException(TypeError, \"is not a finite number\", options);\n      }\n      x = integerPart(x);\n      if (x < lowerBound || x > upperBound) {\n        throw makeException(TypeError, `is outside the accepted range of ${lowerBound} to ${upperBound}, inclusive`, options);\n      }\n      return x;\n    }\n    if (!Number.isNaN(x) && options.clamp) {\n      x = Math.min(Math.max(x, lowerBound), upperBound);\n      x = evenRound(x);\n      return x;\n    }\n    if (!Number.isFinite(x) || x === 0) {\n      return 0;\n    }\n    x = integerPart(x);\n\n    // Math.pow(2, 64) is not accurately representable in JavaScript, so try to avoid these per-spec operations if\n    // possible. Hopefully it's an optimization for the non-64-bitLength cases too.\n    if (x >= lowerBound && x <= upperBound) {\n      return x;\n    }\n\n    // These will not work great for bitLength of 64, but oh well. See the README for more details.\n    x = modulo(x, twoToTheBitLength);\n    if (!unsigned && x >= twoToOneLessThanTheBitLength) {\n      return x - twoToTheBitLength;\n    }\n    return x;\n  };\n}\nfunction createLongLongConversion(bitLength, {\n  unsigned\n}) {\n  const upperBound = Number.MAX_SAFE_INTEGER;\n  const lowerBound = unsigned ? 0 : Number.MIN_SAFE_INTEGER;\n  const asBigIntN = unsigned ? BigInt.asUintN : BigInt.asIntN;\n  return (value, options = {}) => {\n    let x = toNumber(value, options);\n    x = censorNegativeZero(x);\n    if (options.enforceRange) {\n      if (!Number.isFinite(x)) {\n        throw makeException(TypeError, \"is not a finite number\", options);\n      }\n      x = integerPart(x);\n      if (x < lowerBound || x > upperBound) {\n        throw makeException(TypeError, `is outside the accepted range of ${lowerBound} to ${upperBound}, inclusive`, options);\n      }\n      return x;\n    }\n    if (!Number.isNaN(x) && options.clamp) {\n      x = Math.min(Math.max(x, lowerBound), upperBound);\n      x = evenRound(x);\n      return x;\n    }\n    if (!Number.isFinite(x) || x === 0) {\n      return 0;\n    }\n    let xBigInt = BigInt(integerPart(x));\n    xBigInt = asBigIntN(bitLength, xBigInt);\n    return Number(xBigInt);\n  };\n}\nexports.any = value => {\n  return value;\n};\nexports.undefined = () => {\n  return undefined;\n};\nexports.boolean = value => {\n  return Boolean(value);\n};\nexports.byte = createIntegerConversion(8, {\n  unsigned: false\n});\nexports.octet = createIntegerConversion(8, {\n  unsigned: true\n});\nexports.short = createIntegerConversion(16, {\n  unsigned: false\n});\nexports[\"unsigned short\"] = createIntegerConversion(16, {\n  unsigned: true\n});\nexports.long = createIntegerConversion(32, {\n  unsigned: false\n});\nexports[\"unsigned long\"] = createIntegerConversion(32, {\n  unsigned: true\n});\nexports[\"long long\"] = createLongLongConversion(64, {\n  unsigned: false\n});\nexports[\"unsigned long long\"] = createLongLongConversion(64, {\n  unsigned: true\n});\nexports.double = (value, options = {}) => {\n  const x = toNumber(value, options);\n  if (!Number.isFinite(x)) {\n    throw makeException(TypeError, \"is not a finite floating-point value\", options);\n  }\n  return x;\n};\nexports[\"unrestricted double\"] = (value, options = {}) => {\n  const x = toNumber(value, options);\n  return x;\n};\nexports.float = (value, options = {}) => {\n  const x = toNumber(value, options);\n  if (!Number.isFinite(x)) {\n    throw makeException(TypeError, \"is not a finite floating-point value\", options);\n  }\n  if (Object.is(x, -0)) {\n    return x;\n  }\n  const y = Math.fround(x);\n  if (!Number.isFinite(y)) {\n    throw makeException(TypeError, \"is outside the range of a single-precision floating-point value\", options);\n  }\n  return y;\n};\nexports[\"unrestricted float\"] = (value, options = {}) => {\n  const x = toNumber(value, options);\n  if (isNaN(x)) {\n    return x;\n  }\n  if (Object.is(x, -0)) {\n    return x;\n  }\n  return Math.fround(x);\n};\nexports.DOMString = (value, options = {}) => {\n  if (options.treatNullAsEmptyString && value === null) {\n    return \"\";\n  }\n  if (typeof value === \"symbol\") {\n    throw makeException(TypeError, \"is a symbol, which cannot be converted to a string\", options);\n  }\n  const StringCtor = options.globals ? options.globals.String : String;\n  return StringCtor(value);\n};\nexports.ByteString = (value, options = {}) => {\n  const x = exports.DOMString(value, options);\n  let c;\n  for (let i = 0; (c = x.codePointAt(i)) !== undefined; ++i) {\n    if (c > 255) {\n      throw makeException(TypeError, \"is not a valid ByteString\", options);\n    }\n  }\n  return x;\n};\nexports.USVString = (value, options = {}) => {\n  const S = exports.DOMString(value, options);\n  const n = S.length;\n  const U = [];\n  for (let i = 0; i < n; ++i) {\n    const c = S.charCodeAt(i);\n    if (c < 0xD800 || c > 0xDFFF) {\n      U.push(String.fromCodePoint(c));\n    } else if (0xDC00 <= c && c <= 0xDFFF) {\n      U.push(String.fromCodePoint(0xFFFD));\n    } else if (i === n - 1) {\n      U.push(String.fromCodePoint(0xFFFD));\n    } else {\n      const d = S.charCodeAt(i + 1);\n      if (0xDC00 <= d && d <= 0xDFFF) {\n        const a = c & 0x3FF;\n        const b = d & 0x3FF;\n        U.push(String.fromCodePoint((2 << 15) + (2 << 9) * a + b));\n        ++i;\n      } else {\n        U.push(String.fromCodePoint(0xFFFD));\n      }\n    }\n  }\n  return U.join(\"\");\n};\nexports.object = (value, options = {}) => {\n  if (value === null || typeof value !== \"object\" && typeof value !== \"function\") {\n    throw makeException(TypeError, \"is not an object\", options);\n  }\n  return value;\n};\nconst abByteLengthGetter = Object.getOwnPropertyDescriptor(ArrayBuffer.prototype, \"byteLength\").get;\nconst sabByteLengthGetter = typeof SharedArrayBuffer === \"function\" ? Object.getOwnPropertyDescriptor(SharedArrayBuffer.prototype, \"byteLength\").get : null;\nfunction isNonSharedArrayBuffer(value) {\n  try {\n    // This will throw on SharedArrayBuffers, but not detached ArrayBuffers.\n    // (The spec says it should throw, but the spec conflicts with implementations: https://github.com/tc39/ecma262/issues/678)\n    abByteLengthGetter.call(value);\n    return true;\n  } catch {\n    return false;\n  }\n}\nfunction isSharedArrayBuffer(value) {\n  try {\n    sabByteLengthGetter.call(value);\n    return true;\n  } catch {\n    return false;\n  }\n}\nfunction isArrayBufferDetached(value) {\n  try {\n    // eslint-disable-next-line no-new\n    new Uint8Array(value);\n    return false;\n  } catch {\n    return true;\n  }\n}\nexports.ArrayBuffer = (value, options = {}) => {\n  if (!isNonSharedArrayBuffer(value)) {\n    if (options.allowShared && !isSharedArrayBuffer(value)) {\n      throw makeException(TypeError, \"is not an ArrayBuffer or SharedArrayBuffer\", options);\n    }\n    throw makeException(TypeError, \"is not an ArrayBuffer\", options);\n  }\n  if (isArrayBufferDetached(value)) {\n    throw makeException(TypeError, \"is a detached ArrayBuffer\", options);\n  }\n  return value;\n};\nconst dvByteLengthGetter = Object.getOwnPropertyDescriptor(DataView.prototype, \"byteLength\").get;\nexports.DataView = (value, options = {}) => {\n  try {\n    dvByteLengthGetter.call(value);\n  } catch (e) {\n    throw makeException(TypeError, \"is not a DataView\", options);\n  }\n  if (!options.allowShared && isSharedArrayBuffer(value.buffer)) {\n    throw makeException(TypeError, \"is backed by a SharedArrayBuffer, which is not allowed\", options);\n  }\n  if (isArrayBufferDetached(value.buffer)) {\n    throw makeException(TypeError, \"is backed by a detached ArrayBuffer\", options);\n  }\n  return value;\n};\n\n// Returns the unforgeable `TypedArray` constructor name or `undefined`,\n// if the `this` value isn't a valid `TypedArray` object.\n//\n// https://tc39.es/ecma262/#sec-get-%typedarray%.prototype-@@tostringtag\nconst typedArrayNameGetter = Object.getOwnPropertyDescriptor(Object.getPrototypeOf(Uint8Array).prototype, Symbol.toStringTag).get;\n[Int8Array, Int16Array, Int32Array, Uint8Array, Uint16Array, Uint32Array, Uint8ClampedArray, Float32Array, Float64Array].forEach(func => {\n  const {\n    name\n  } = func;\n  const article = /^[AEIOU]/u.test(name) ? \"an\" : \"a\";\n  exports[name] = (value, options = {}) => {\n    if (!ArrayBuffer.isView(value) || typedArrayNameGetter.call(value) !== name) {\n      throw makeException(TypeError, `is not ${article} ${name} object`, options);\n    }\n    if (!options.allowShared && isSharedArrayBuffer(value.buffer)) {\n      throw makeException(TypeError, \"is a view on a SharedArrayBuffer, which is not allowed\", options);\n    }\n    if (isArrayBufferDetached(value.buffer)) {\n      throw makeException(TypeError, \"is a view on a detached ArrayBuffer\", options);\n    }\n    return value;\n  };\n});\n\n// Common definitions\n\nexports.ArrayBufferView = (value, options = {}) => {\n  if (!ArrayBuffer.isView(value)) {\n    throw makeException(TypeError, \"is not a view on an ArrayBuffer or SharedArrayBuffer\", options);\n  }\n  if (!options.allowShared && isSharedArrayBuffer(value.buffer)) {\n    throw makeException(TypeError, \"is a view on a SharedArrayBuffer, which is not allowed\", options);\n  }\n  if (isArrayBufferDetached(value.buffer)) {\n    throw makeException(TypeError, \"is a view on a detached ArrayBuffer\", options);\n  }\n  return value;\n};\nexports.BufferSource = (value, options = {}) => {\n  if (ArrayBuffer.isView(value)) {\n    if (!options.allowShared && isSharedArrayBuffer(value.buffer)) {\n      throw makeException(TypeError, \"is a view on a SharedArrayBuffer, which is not allowed\", options);\n    }\n    if (isArrayBufferDetached(value.buffer)) {\n      throw makeException(TypeError, \"is a view on a detached ArrayBuffer\", options);\n    }\n    return value;\n  }\n  if (!options.allowShared && !isNonSharedArrayBuffer(value)) {\n    throw makeException(TypeError, \"is not an ArrayBuffer or a view on one\", options);\n  }\n  if (options.allowShared && !isSharedArrayBuffer(value) && !isNonSharedArrayBuffer(value)) {\n    throw makeException(TypeError, \"is not an ArrayBuffer, SharedArrayBuffer, or a view on one\", options);\n  }\n  if (isArrayBufferDetached(value)) {\n    throw makeException(TypeError, \"is a detached ArrayBuffer\", options);\n  }\n  return value;\n};\nexports.DOMTimeStamp = exports[\"unsigned long long\"];", "map": {"version": 3, "names": ["makeException", "ErrorType", "message", "options", "globals", "name", "context", "toNumber", "value", "TypeError", "Number", "evenRound", "x", "censorNegativeZero", "Math", "floor", "round", "integerPart", "n", "trunc", "sign", "modulo", "y", "signMightNotMatch", "createIntegerConversion", "bitLength", "unsigned", "lowerBound", "upperBound", "twoToTheBitLength", "twoToOneLessThanTheBitLength", "enforceRange", "isFinite", "isNaN", "clamp", "min", "max", "createLongLongConversion", "MAX_SAFE_INTEGER", "MIN_SAFE_INTEGER", "asBigIntN", "BigInt", "asUintN", "asIntN", "xBigInt", "exports", "any", "undefined", "boolean", "Boolean", "byte", "octet", "short", "long", "double", "float", "Object", "is", "fround", "DOMString", "treatNullAsEmptyString", "StringCtor", "String", "ByteString", "c", "i", "codePointAt", "USVString", "S", "length", "U", "charCodeAt", "push", "fromCodePoint", "d", "a", "b", "join", "object", "abByteLengthGetter", "getOwnPropertyDescriptor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "prototype", "get", "sabByteLengthGetter", "SharedArrayBuffer", "isNonSharedArrayBuffer", "call", "isSharedArrayBuffer", "isArrayBufferDetached", "Uint8Array", "allowShared", "dvByteLengthGetter", "DataView", "e", "buffer", "typedArrayNameGetter", "getPrototypeOf", "Symbol", "toStringTag", "Int8Array", "Int16Array", "Int32Array", "Uint16Array", "Uint32Array", "Uint8ClampedArray", "Float32Array", "Float64Array", "for<PERSON>ach", "func", "article", "test", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>iew", "BufferSource", "DOMTimeStamp"], "sources": ["C:/Users/<USER>/node_modules/mongodb-connection-string-url/node_modules/webidl-conversions/lib/index.js"], "sourcesContent": ["\"use strict\";\n\nfunction makeException(ErrorType, message, options) {\n  if (options.globals) {\n    ErrorType = options.globals[ErrorType.name];\n  }\n  return new ErrorType(`${options.context ? options.context : \"Value\"} ${message}.`);\n}\n\nfunction toNumber(value, options) {\n  if (typeof value === \"bigint\") {\n    throw makeException(TypeError, \"is a BigInt which cannot be converted to a number\", options);\n  }\n  if (!options.globals) {\n    return Number(value);\n  }\n  return options.globals.Number(value);\n}\n\n// Round x to the nearest integer, choosing the even integer if it lies halfway between two.\nfunction evenRound(x) {\n  // There are four cases for numbers with fractional part being .5:\n  //\n  // case |     x     | floor(x) | round(x) | expected | x <> 0 | x % 1 | x & 1 |   example\n  //   1  |  2n + 0.5 |  2n      |  2n + 1  |  2n      |   >    |  0.5  |   0   |  0.5 ->  0\n  //   2  |  2n + 1.5 |  2n + 1  |  2n + 2  |  2n + 2  |   >    |  0.5  |   1   |  1.5 ->  2\n  //   3  | -2n - 0.5 | -2n - 1  | -2n      | -2n      |   <    | -0.5  |   0   | -0.5 ->  0\n  //   4  | -2n - 1.5 | -2n - 2  | -2n - 1  | -2n - 2  |   <    | -0.5  |   1   | -1.5 -> -2\n  // (where n is a non-negative integer)\n  //\n  // Branch here for cases 1 and 4\n  if ((x > 0 && (x % 1) === +0.5 && (x & 1) === 0) ||\n        (x < 0 && (x % 1) === -0.5 && (x & 1) === 1)) {\n    return censorNegativeZero(Math.floor(x));\n  }\n\n  return censorNegativeZero(Math.round(x));\n}\n\nfunction integerPart(n) {\n  return censorNegativeZero(Math.trunc(n));\n}\n\nfunction sign(x) {\n  return x < 0 ? -1 : 1;\n}\n\nfunction modulo(x, y) {\n  // https://tc39.github.io/ecma262/#eqn-modulo\n  // Note that http://stackoverflow.com/a/4467559/3191 does NOT work for large modulos\n  const signMightNotMatch = x % y;\n  if (sign(y) !== sign(signMightNotMatch)) {\n    return signMightNotMatch + y;\n  }\n  return signMightNotMatch;\n}\n\nfunction censorNegativeZero(x) {\n  return x === 0 ? 0 : x;\n}\n\nfunction createIntegerConversion(bitLength, { unsigned }) {\n  let lowerBound, upperBound;\n  if (unsigned) {\n    lowerBound = 0;\n    upperBound = 2 ** bitLength - 1;\n  } else {\n    lowerBound = -(2 ** (bitLength - 1));\n    upperBound = 2 ** (bitLength - 1) - 1;\n  }\n\n  const twoToTheBitLength = 2 ** bitLength;\n  const twoToOneLessThanTheBitLength = 2 ** (bitLength - 1);\n\n  return (value, options = {}) => {\n    let x = toNumber(value, options);\n    x = censorNegativeZero(x);\n\n    if (options.enforceRange) {\n      if (!Number.isFinite(x)) {\n        throw makeException(TypeError, \"is not a finite number\", options);\n      }\n\n      x = integerPart(x);\n\n      if (x < lowerBound || x > upperBound) {\n        throw makeException(\n          TypeError,\n          `is outside the accepted range of ${lowerBound} to ${upperBound}, inclusive`,\n          options\n        );\n      }\n\n      return x;\n    }\n\n    if (!Number.isNaN(x) && options.clamp) {\n      x = Math.min(Math.max(x, lowerBound), upperBound);\n      x = evenRound(x);\n      return x;\n    }\n\n    if (!Number.isFinite(x) || x === 0) {\n      return 0;\n    }\n    x = integerPart(x);\n\n    // Math.pow(2, 64) is not accurately representable in JavaScript, so try to avoid these per-spec operations if\n    // possible. Hopefully it's an optimization for the non-64-bitLength cases too.\n    if (x >= lowerBound && x <= upperBound) {\n      return x;\n    }\n\n    // These will not work great for bitLength of 64, but oh well. See the README for more details.\n    x = modulo(x, twoToTheBitLength);\n    if (!unsigned && x >= twoToOneLessThanTheBitLength) {\n      return x - twoToTheBitLength;\n    }\n    return x;\n  };\n}\n\nfunction createLongLongConversion(bitLength, { unsigned }) {\n  const upperBound = Number.MAX_SAFE_INTEGER;\n  const lowerBound = unsigned ? 0 : Number.MIN_SAFE_INTEGER;\n  const asBigIntN = unsigned ? BigInt.asUintN : BigInt.asIntN;\n\n  return (value, options = {}) => {\n    let x = toNumber(value, options);\n    x = censorNegativeZero(x);\n\n    if (options.enforceRange) {\n      if (!Number.isFinite(x)) {\n        throw makeException(TypeError, \"is not a finite number\", options);\n      }\n\n      x = integerPart(x);\n\n      if (x < lowerBound || x > upperBound) {\n        throw makeException(\n          TypeError,\n          `is outside the accepted range of ${lowerBound} to ${upperBound}, inclusive`,\n          options\n        );\n      }\n\n      return x;\n    }\n\n    if (!Number.isNaN(x) && options.clamp) {\n      x = Math.min(Math.max(x, lowerBound), upperBound);\n      x = evenRound(x);\n      return x;\n    }\n\n    if (!Number.isFinite(x) || x === 0) {\n      return 0;\n    }\n\n    let xBigInt = BigInt(integerPart(x));\n    xBigInt = asBigIntN(bitLength, xBigInt);\n    return Number(xBigInt);\n  };\n}\n\nexports.any = value => {\n  return value;\n};\n\nexports.undefined = () => {\n  return undefined;\n};\n\nexports.boolean = value => {\n  return Boolean(value);\n};\n\nexports.byte = createIntegerConversion(8, { unsigned: false });\nexports.octet = createIntegerConversion(8, { unsigned: true });\n\nexports.short = createIntegerConversion(16, { unsigned: false });\nexports[\"unsigned short\"] = createIntegerConversion(16, { unsigned: true });\n\nexports.long = createIntegerConversion(32, { unsigned: false });\nexports[\"unsigned long\"] = createIntegerConversion(32, { unsigned: true });\n\nexports[\"long long\"] = createLongLongConversion(64, { unsigned: false });\nexports[\"unsigned long long\"] = createLongLongConversion(64, { unsigned: true });\n\nexports.double = (value, options = {}) => {\n  const x = toNumber(value, options);\n\n  if (!Number.isFinite(x)) {\n    throw makeException(TypeError, \"is not a finite floating-point value\", options);\n  }\n\n  return x;\n};\n\nexports[\"unrestricted double\"] = (value, options = {}) => {\n  const x = toNumber(value, options);\n\n  return x;\n};\n\nexports.float = (value, options = {}) => {\n  const x = toNumber(value, options);\n\n  if (!Number.isFinite(x)) {\n    throw makeException(TypeError, \"is not a finite floating-point value\", options);\n  }\n\n  if (Object.is(x, -0)) {\n    return x;\n  }\n\n  const y = Math.fround(x);\n\n  if (!Number.isFinite(y)) {\n    throw makeException(TypeError, \"is outside the range of a single-precision floating-point value\", options);\n  }\n\n  return y;\n};\n\nexports[\"unrestricted float\"] = (value, options = {}) => {\n  const x = toNumber(value, options);\n\n  if (isNaN(x)) {\n    return x;\n  }\n\n  if (Object.is(x, -0)) {\n    return x;\n  }\n\n  return Math.fround(x);\n};\n\nexports.DOMString = (value, options = {}) => {\n  if (options.treatNullAsEmptyString && value === null) {\n    return \"\";\n  }\n\n  if (typeof value === \"symbol\") {\n    throw makeException(TypeError, \"is a symbol, which cannot be converted to a string\", options);\n  }\n\n  const StringCtor = options.globals ? options.globals.String : String;\n  return StringCtor(value);\n};\n\nexports.ByteString = (value, options = {}) => {\n  const x = exports.DOMString(value, options);\n  let c;\n  for (let i = 0; (c = x.codePointAt(i)) !== undefined; ++i) {\n    if (c > 255) {\n      throw makeException(TypeError, \"is not a valid ByteString\", options);\n    }\n  }\n\n  return x;\n};\n\nexports.USVString = (value, options = {}) => {\n  const S = exports.DOMString(value, options);\n  const n = S.length;\n  const U = [];\n  for (let i = 0; i < n; ++i) {\n    const c = S.charCodeAt(i);\n    if (c < 0xD800 || c > 0xDFFF) {\n      U.push(String.fromCodePoint(c));\n    } else if (0xDC00 <= c && c <= 0xDFFF) {\n      U.push(String.fromCodePoint(0xFFFD));\n    } else if (i === n - 1) {\n      U.push(String.fromCodePoint(0xFFFD));\n    } else {\n      const d = S.charCodeAt(i + 1);\n      if (0xDC00 <= d && d <= 0xDFFF) {\n        const a = c & 0x3FF;\n        const b = d & 0x3FF;\n        U.push(String.fromCodePoint((2 << 15) + ((2 << 9) * a) + b));\n        ++i;\n      } else {\n        U.push(String.fromCodePoint(0xFFFD));\n      }\n    }\n  }\n\n  return U.join(\"\");\n};\n\nexports.object = (value, options = {}) => {\n  if (value === null || (typeof value !== \"object\" && typeof value !== \"function\")) {\n    throw makeException(TypeError, \"is not an object\", options);\n  }\n\n  return value;\n};\n\nconst abByteLengthGetter =\n    Object.getOwnPropertyDescriptor(ArrayBuffer.prototype, \"byteLength\").get;\nconst sabByteLengthGetter =\n    typeof SharedArrayBuffer === \"function\" ?\n      Object.getOwnPropertyDescriptor(SharedArrayBuffer.prototype, \"byteLength\").get :\n      null;\n\nfunction isNonSharedArrayBuffer(value) {\n  try {\n    // This will throw on SharedArrayBuffers, but not detached ArrayBuffers.\n    // (The spec says it should throw, but the spec conflicts with implementations: https://github.com/tc39/ecma262/issues/678)\n    abByteLengthGetter.call(value);\n\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nfunction isSharedArrayBuffer(value) {\n  try {\n    sabByteLengthGetter.call(value);\n    return true;\n  } catch {\n    return false;\n  }\n}\n\nfunction isArrayBufferDetached(value) {\n  try {\n    // eslint-disable-next-line no-new\n    new Uint8Array(value);\n    return false;\n  } catch {\n    return true;\n  }\n}\n\nexports.ArrayBuffer = (value, options = {}) => {\n  if (!isNonSharedArrayBuffer(value)) {\n    if (options.allowShared && !isSharedArrayBuffer(value)) {\n      throw makeException(TypeError, \"is not an ArrayBuffer or SharedArrayBuffer\", options);\n    }\n    throw makeException(TypeError, \"is not an ArrayBuffer\", options);\n  }\n  if (isArrayBufferDetached(value)) {\n    throw makeException(TypeError, \"is a detached ArrayBuffer\", options);\n  }\n\n  return value;\n};\n\nconst dvByteLengthGetter =\n    Object.getOwnPropertyDescriptor(DataView.prototype, \"byteLength\").get;\nexports.DataView = (value, options = {}) => {\n  try {\n    dvByteLengthGetter.call(value);\n  } catch (e) {\n    throw makeException(TypeError, \"is not a DataView\", options);\n  }\n\n  if (!options.allowShared && isSharedArrayBuffer(value.buffer)) {\n    throw makeException(TypeError, \"is backed by a SharedArrayBuffer, which is not allowed\", options);\n  }\n  if (isArrayBufferDetached(value.buffer)) {\n    throw makeException(TypeError, \"is backed by a detached ArrayBuffer\", options);\n  }\n\n  return value;\n};\n\n// Returns the unforgeable `TypedArray` constructor name or `undefined`,\n// if the `this` value isn't a valid `TypedArray` object.\n//\n// https://tc39.es/ecma262/#sec-get-%typedarray%.prototype-@@tostringtag\nconst typedArrayNameGetter = Object.getOwnPropertyDescriptor(\n  Object.getPrototypeOf(Uint8Array).prototype,\n  Symbol.toStringTag\n).get;\n[\n  Int8Array,\n  Int16Array,\n  Int32Array,\n  Uint8Array,\n  Uint16Array,\n  Uint32Array,\n  Uint8ClampedArray,\n  Float32Array,\n  Float64Array\n].forEach(func => {\n  const { name } = func;\n  const article = /^[AEIOU]/u.test(name) ? \"an\" : \"a\";\n  exports[name] = (value, options = {}) => {\n    if (!ArrayBuffer.isView(value) || typedArrayNameGetter.call(value) !== name) {\n      throw makeException(TypeError, `is not ${article} ${name} object`, options);\n    }\n    if (!options.allowShared && isSharedArrayBuffer(value.buffer)) {\n      throw makeException(TypeError, \"is a view on a SharedArrayBuffer, which is not allowed\", options);\n    }\n    if (isArrayBufferDetached(value.buffer)) {\n      throw makeException(TypeError, \"is a view on a detached ArrayBuffer\", options);\n    }\n\n    return value;\n  };\n});\n\n// Common definitions\n\nexports.ArrayBufferView = (value, options = {}) => {\n  if (!ArrayBuffer.isView(value)) {\n    throw makeException(TypeError, \"is not a view on an ArrayBuffer or SharedArrayBuffer\", options);\n  }\n\n  if (!options.allowShared && isSharedArrayBuffer(value.buffer)) {\n    throw makeException(TypeError, \"is a view on a SharedArrayBuffer, which is not allowed\", options);\n  }\n\n  if (isArrayBufferDetached(value.buffer)) {\n    throw makeException(TypeError, \"is a view on a detached ArrayBuffer\", options);\n  }\n  return value;\n};\n\nexports.BufferSource = (value, options = {}) => {\n  if (ArrayBuffer.isView(value)) {\n    if (!options.allowShared && isSharedArrayBuffer(value.buffer)) {\n      throw makeException(TypeError, \"is a view on a SharedArrayBuffer, which is not allowed\", options);\n    }\n\n    if (isArrayBufferDetached(value.buffer)) {\n      throw makeException(TypeError, \"is a view on a detached ArrayBuffer\", options);\n    }\n    return value;\n  }\n\n  if (!options.allowShared && !isNonSharedArrayBuffer(value)) {\n    throw makeException(TypeError, \"is not an ArrayBuffer or a view on one\", options);\n  }\n  if (options.allowShared && !isSharedArrayBuffer(value) && !isNonSharedArrayBuffer(value)) {\n    throw makeException(TypeError, \"is not an ArrayBuffer, SharedArrayBuffer, or a view on one\", options);\n  }\n  if (isArrayBufferDetached(value)) {\n    throw makeException(TypeError, \"is a detached ArrayBuffer\", options);\n  }\n\n  return value;\n};\n\nexports.DOMTimeStamp = exports[\"unsigned long long\"];\n"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,aAAaA,CAACC,SAAS,EAAEC,OAAO,EAAEC,OAAO,EAAE;EAClD,IAAIA,OAAO,CAACC,OAAO,EAAE;IACnBH,SAAS,GAAGE,OAAO,CAACC,OAAO,CAACH,SAAS,CAACI,IAAI,CAAC;EAC7C;EACA,OAAO,IAAIJ,SAAS,CAAC,GAAGE,OAAO,CAACG,OAAO,GAAGH,OAAO,CAACG,OAAO,GAAG,OAAO,IAAIJ,OAAO,GAAG,CAAC;AACpF;AAEA,SAASK,QAAQA,CAACC,KAAK,EAAEL,OAAO,EAAE;EAChC,IAAI,OAAOK,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAMR,aAAa,CAACS,SAAS,EAAE,mDAAmD,EAAEN,OAAO,CAAC;EAC9F;EACA,IAAI,CAACA,OAAO,CAACC,OAAO,EAAE;IACpB,OAAOM,MAAM,CAACF,KAAK,CAAC;EACtB;EACA,OAAOL,OAAO,CAACC,OAAO,CAACM,MAAM,CAACF,KAAK,CAAC;AACtC;;AAEA;AACA,SAASG,SAASA,CAACC,CAAC,EAAE;EACpB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,IAAKA,CAAC,GAAG,CAAC,IAAKA,CAAC,GAAG,CAAC,KAAM,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,CAAC,MAAM,CAAC,IACxCA,CAAC,GAAG,CAAC,IAAKA,CAAC,GAAG,CAAC,KAAM,CAAC,GAAG,IAAI,CAACA,CAAC,GAAG,CAAC,MAAM,CAAE,EAAE;IAClD,OAAOC,kBAAkB,CAACC,IAAI,CAACC,KAAK,CAACH,CAAC,CAAC,CAAC;EAC1C;EAEA,OAAOC,kBAAkB,CAACC,IAAI,CAACE,KAAK,CAACJ,CAAC,CAAC,CAAC;AAC1C;AAEA,SAASK,WAAWA,CAACC,CAAC,EAAE;EACtB,OAAOL,kBAAkB,CAACC,IAAI,CAACK,KAAK,CAACD,CAAC,CAAC,CAAC;AAC1C;AAEA,SAASE,IAAIA,CAACR,CAAC,EAAE;EACf,OAAOA,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;AACvB;AAEA,SAASS,MAAMA,CAACT,CAAC,EAAEU,CAAC,EAAE;EACpB;EACA;EACA,MAAMC,iBAAiB,GAAGX,CAAC,GAAGU,CAAC;EAC/B,IAAIF,IAAI,CAACE,CAAC,CAAC,KAAKF,IAAI,CAACG,iBAAiB,CAAC,EAAE;IACvC,OAAOA,iBAAiB,GAAGD,CAAC;EAC9B;EACA,OAAOC,iBAAiB;AAC1B;AAEA,SAASV,kBAAkBA,CAACD,CAAC,EAAE;EAC7B,OAAOA,CAAC,KAAK,CAAC,GAAG,CAAC,GAAGA,CAAC;AACxB;AAEA,SAASY,uBAAuBA,CAACC,SAAS,EAAE;EAAEC;AAAS,CAAC,EAAE;EACxD,IAAIC,UAAU,EAAEC,UAAU;EAC1B,IAAIF,QAAQ,EAAE;IACZC,UAAU,GAAG,CAAC;IACdC,UAAU,GAAG,CAAC,IAAIH,SAAS,GAAG,CAAC;EACjC,CAAC,MAAM;IACLE,UAAU,GAAG,EAAE,CAAC,KAAKF,SAAS,GAAG,CAAC,CAAC,CAAC;IACpCG,UAAU,GAAG,CAAC,KAAKH,SAAS,GAAG,CAAC,CAAC,GAAG,CAAC;EACvC;EAEA,MAAMI,iBAAiB,GAAG,CAAC,IAAIJ,SAAS;EACxC,MAAMK,4BAA4B,GAAG,CAAC,KAAKL,SAAS,GAAG,CAAC,CAAC;EAEzD,OAAO,CAACjB,KAAK,EAAEL,OAAO,GAAG,CAAC,CAAC,KAAK;IAC9B,IAAIS,CAAC,GAAGL,QAAQ,CAACC,KAAK,EAAEL,OAAO,CAAC;IAChCS,CAAC,GAAGC,kBAAkB,CAACD,CAAC,CAAC;IAEzB,IAAIT,OAAO,CAAC4B,YAAY,EAAE;MACxB,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAACpB,CAAC,CAAC,EAAE;QACvB,MAAMZ,aAAa,CAACS,SAAS,EAAE,wBAAwB,EAAEN,OAAO,CAAC;MACnE;MAEAS,CAAC,GAAGK,WAAW,CAACL,CAAC,CAAC;MAElB,IAAIA,CAAC,GAAGe,UAAU,IAAIf,CAAC,GAAGgB,UAAU,EAAE;QACpC,MAAM5B,aAAa,CACjBS,SAAS,EACT,oCAAoCkB,UAAU,OAAOC,UAAU,aAAa,EAC5EzB,OACF,CAAC;MACH;MAEA,OAAOS,CAAC;IACV;IAEA,IAAI,CAACF,MAAM,CAACuB,KAAK,CAACrB,CAAC,CAAC,IAAIT,OAAO,CAAC+B,KAAK,EAAE;MACrCtB,CAAC,GAAGE,IAAI,CAACqB,GAAG,CAACrB,IAAI,CAACsB,GAAG,CAACxB,CAAC,EAAEe,UAAU,CAAC,EAAEC,UAAU,CAAC;MACjDhB,CAAC,GAAGD,SAAS,CAACC,CAAC,CAAC;MAChB,OAAOA,CAAC;IACV;IAEA,IAAI,CAACF,MAAM,CAACsB,QAAQ,CAACpB,CAAC,CAAC,IAAIA,CAAC,KAAK,CAAC,EAAE;MAClC,OAAO,CAAC;IACV;IACAA,CAAC,GAAGK,WAAW,CAACL,CAAC,CAAC;;IAElB;IACA;IACA,IAAIA,CAAC,IAAIe,UAAU,IAAIf,CAAC,IAAIgB,UAAU,EAAE;MACtC,OAAOhB,CAAC;IACV;;IAEA;IACAA,CAAC,GAAGS,MAAM,CAACT,CAAC,EAAEiB,iBAAiB,CAAC;IAChC,IAAI,CAACH,QAAQ,IAAId,CAAC,IAAIkB,4BAA4B,EAAE;MAClD,OAAOlB,CAAC,GAAGiB,iBAAiB;IAC9B;IACA,OAAOjB,CAAC;EACV,CAAC;AACH;AAEA,SAASyB,wBAAwBA,CAACZ,SAAS,EAAE;EAAEC;AAAS,CAAC,EAAE;EACzD,MAAME,UAAU,GAAGlB,MAAM,CAAC4B,gBAAgB;EAC1C,MAAMX,UAAU,GAAGD,QAAQ,GAAG,CAAC,GAAGhB,MAAM,CAAC6B,gBAAgB;EACzD,MAAMC,SAAS,GAAGd,QAAQ,GAAGe,MAAM,CAACC,OAAO,GAAGD,MAAM,CAACE,MAAM;EAE3D,OAAO,CAACnC,KAAK,EAAEL,OAAO,GAAG,CAAC,CAAC,KAAK;IAC9B,IAAIS,CAAC,GAAGL,QAAQ,CAACC,KAAK,EAAEL,OAAO,CAAC;IAChCS,CAAC,GAAGC,kBAAkB,CAACD,CAAC,CAAC;IAEzB,IAAIT,OAAO,CAAC4B,YAAY,EAAE;MACxB,IAAI,CAACrB,MAAM,CAACsB,QAAQ,CAACpB,CAAC,CAAC,EAAE;QACvB,MAAMZ,aAAa,CAACS,SAAS,EAAE,wBAAwB,EAAEN,OAAO,CAAC;MACnE;MAEAS,CAAC,GAAGK,WAAW,CAACL,CAAC,CAAC;MAElB,IAAIA,CAAC,GAAGe,UAAU,IAAIf,CAAC,GAAGgB,UAAU,EAAE;QACpC,MAAM5B,aAAa,CACjBS,SAAS,EACT,oCAAoCkB,UAAU,OAAOC,UAAU,aAAa,EAC5EzB,OACF,CAAC;MACH;MAEA,OAAOS,CAAC;IACV;IAEA,IAAI,CAACF,MAAM,CAACuB,KAAK,CAACrB,CAAC,CAAC,IAAIT,OAAO,CAAC+B,KAAK,EAAE;MACrCtB,CAAC,GAAGE,IAAI,CAACqB,GAAG,CAACrB,IAAI,CAACsB,GAAG,CAACxB,CAAC,EAAEe,UAAU,CAAC,EAAEC,UAAU,CAAC;MACjDhB,CAAC,GAAGD,SAAS,CAACC,CAAC,CAAC;MAChB,OAAOA,CAAC;IACV;IAEA,IAAI,CAACF,MAAM,CAACsB,QAAQ,CAACpB,CAAC,CAAC,IAAIA,CAAC,KAAK,CAAC,EAAE;MAClC,OAAO,CAAC;IACV;IAEA,IAAIgC,OAAO,GAAGH,MAAM,CAACxB,WAAW,CAACL,CAAC,CAAC,CAAC;IACpCgC,OAAO,GAAGJ,SAAS,CAACf,SAAS,EAAEmB,OAAO,CAAC;IACvC,OAAOlC,MAAM,CAACkC,OAAO,CAAC;EACxB,CAAC;AACH;AAEAC,OAAO,CAACC,GAAG,GAAGtC,KAAK,IAAI;EACrB,OAAOA,KAAK;AACd,CAAC;AAEDqC,OAAO,CAACE,SAAS,GAAG,MAAM;EACxB,OAAOA,SAAS;AAClB,CAAC;AAEDF,OAAO,CAACG,OAAO,GAAGxC,KAAK,IAAI;EACzB,OAAOyC,OAAO,CAACzC,KAAK,CAAC;AACvB,CAAC;AAEDqC,OAAO,CAACK,IAAI,GAAG1B,uBAAuB,CAAC,CAAC,EAAE;EAAEE,QAAQ,EAAE;AAAM,CAAC,CAAC;AAC9DmB,OAAO,CAACM,KAAK,GAAG3B,uBAAuB,CAAC,CAAC,EAAE;EAAEE,QAAQ,EAAE;AAAK,CAAC,CAAC;AAE9DmB,OAAO,CAACO,KAAK,GAAG5B,uBAAuB,CAAC,EAAE,EAAE;EAAEE,QAAQ,EAAE;AAAM,CAAC,CAAC;AAChEmB,OAAO,CAAC,gBAAgB,CAAC,GAAGrB,uBAAuB,CAAC,EAAE,EAAE;EAAEE,QAAQ,EAAE;AAAK,CAAC,CAAC;AAE3EmB,OAAO,CAACQ,IAAI,GAAG7B,uBAAuB,CAAC,EAAE,EAAE;EAAEE,QAAQ,EAAE;AAAM,CAAC,CAAC;AAC/DmB,OAAO,CAAC,eAAe,CAAC,GAAGrB,uBAAuB,CAAC,EAAE,EAAE;EAAEE,QAAQ,EAAE;AAAK,CAAC,CAAC;AAE1EmB,OAAO,CAAC,WAAW,CAAC,GAAGR,wBAAwB,CAAC,EAAE,EAAE;EAAEX,QAAQ,EAAE;AAAM,CAAC,CAAC;AACxEmB,OAAO,CAAC,oBAAoB,CAAC,GAAGR,wBAAwB,CAAC,EAAE,EAAE;EAAEX,QAAQ,EAAE;AAAK,CAAC,CAAC;AAEhFmB,OAAO,CAACS,MAAM,GAAG,CAAC9C,KAAK,EAAEL,OAAO,GAAG,CAAC,CAAC,KAAK;EACxC,MAAMS,CAAC,GAAGL,QAAQ,CAACC,KAAK,EAAEL,OAAO,CAAC;EAElC,IAAI,CAACO,MAAM,CAACsB,QAAQ,CAACpB,CAAC,CAAC,EAAE;IACvB,MAAMZ,aAAa,CAACS,SAAS,EAAE,sCAAsC,EAAEN,OAAO,CAAC;EACjF;EAEA,OAAOS,CAAC;AACV,CAAC;AAEDiC,OAAO,CAAC,qBAAqB,CAAC,GAAG,CAACrC,KAAK,EAAEL,OAAO,GAAG,CAAC,CAAC,KAAK;EACxD,MAAMS,CAAC,GAAGL,QAAQ,CAACC,KAAK,EAAEL,OAAO,CAAC;EAElC,OAAOS,CAAC;AACV,CAAC;AAEDiC,OAAO,CAACU,KAAK,GAAG,CAAC/C,KAAK,EAAEL,OAAO,GAAG,CAAC,CAAC,KAAK;EACvC,MAAMS,CAAC,GAAGL,QAAQ,CAACC,KAAK,EAAEL,OAAO,CAAC;EAElC,IAAI,CAACO,MAAM,CAACsB,QAAQ,CAACpB,CAAC,CAAC,EAAE;IACvB,MAAMZ,aAAa,CAACS,SAAS,EAAE,sCAAsC,EAAEN,OAAO,CAAC;EACjF;EAEA,IAAIqD,MAAM,CAACC,EAAE,CAAC7C,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IACpB,OAAOA,CAAC;EACV;EAEA,MAAMU,CAAC,GAAGR,IAAI,CAAC4C,MAAM,CAAC9C,CAAC,CAAC;EAExB,IAAI,CAACF,MAAM,CAACsB,QAAQ,CAACV,CAAC,CAAC,EAAE;IACvB,MAAMtB,aAAa,CAACS,SAAS,EAAE,iEAAiE,EAAEN,OAAO,CAAC;EAC5G;EAEA,OAAOmB,CAAC;AACV,CAAC;AAEDuB,OAAO,CAAC,oBAAoB,CAAC,GAAG,CAACrC,KAAK,EAAEL,OAAO,GAAG,CAAC,CAAC,KAAK;EACvD,MAAMS,CAAC,GAAGL,QAAQ,CAACC,KAAK,EAAEL,OAAO,CAAC;EAElC,IAAI8B,KAAK,CAACrB,CAAC,CAAC,EAAE;IACZ,OAAOA,CAAC;EACV;EAEA,IAAI4C,MAAM,CAACC,EAAE,CAAC7C,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE;IACpB,OAAOA,CAAC;EACV;EAEA,OAAOE,IAAI,CAAC4C,MAAM,CAAC9C,CAAC,CAAC;AACvB,CAAC;AAEDiC,OAAO,CAACc,SAAS,GAAG,CAACnD,KAAK,EAAEL,OAAO,GAAG,CAAC,CAAC,KAAK;EAC3C,IAAIA,OAAO,CAACyD,sBAAsB,IAAIpD,KAAK,KAAK,IAAI,EAAE;IACpD,OAAO,EAAE;EACX;EAEA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAMR,aAAa,CAACS,SAAS,EAAE,oDAAoD,EAAEN,OAAO,CAAC;EAC/F;EAEA,MAAM0D,UAAU,GAAG1D,OAAO,CAACC,OAAO,GAAGD,OAAO,CAACC,OAAO,CAAC0D,MAAM,GAAGA,MAAM;EACpE,OAAOD,UAAU,CAACrD,KAAK,CAAC;AAC1B,CAAC;AAEDqC,OAAO,CAACkB,UAAU,GAAG,CAACvD,KAAK,EAAEL,OAAO,GAAG,CAAC,CAAC,KAAK;EAC5C,MAAMS,CAAC,GAAGiC,OAAO,CAACc,SAAS,CAACnD,KAAK,EAAEL,OAAO,CAAC;EAC3C,IAAI6D,CAAC;EACL,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAE,CAACD,CAAC,GAAGpD,CAAC,CAACsD,WAAW,CAACD,CAAC,CAAC,MAAMlB,SAAS,EAAE,EAAEkB,CAAC,EAAE;IACzD,IAAID,CAAC,GAAG,GAAG,EAAE;MACX,MAAMhE,aAAa,CAACS,SAAS,EAAE,2BAA2B,EAAEN,OAAO,CAAC;IACtE;EACF;EAEA,OAAOS,CAAC;AACV,CAAC;AAEDiC,OAAO,CAACsB,SAAS,GAAG,CAAC3D,KAAK,EAAEL,OAAO,GAAG,CAAC,CAAC,KAAK;EAC3C,MAAMiE,CAAC,GAAGvB,OAAO,CAACc,SAAS,CAACnD,KAAK,EAAEL,OAAO,CAAC;EAC3C,MAAMe,CAAC,GAAGkD,CAAC,CAACC,MAAM;EAClB,MAAMC,CAAC,GAAG,EAAE;EACZ,KAAK,IAAIL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG/C,CAAC,EAAE,EAAE+C,CAAC,EAAE;IAC1B,MAAMD,CAAC,GAAGI,CAAC,CAACG,UAAU,CAACN,CAAC,CAAC;IACzB,IAAID,CAAC,GAAG,MAAM,IAAIA,CAAC,GAAG,MAAM,EAAE;MAC5BM,CAAC,CAACE,IAAI,CAACV,MAAM,CAACW,aAAa,CAACT,CAAC,CAAC,CAAC;IACjC,CAAC,MAAM,IAAI,MAAM,IAAIA,CAAC,IAAIA,CAAC,IAAI,MAAM,EAAE;MACrCM,CAAC,CAACE,IAAI,CAACV,MAAM,CAACW,aAAa,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC,MAAM,IAAIR,CAAC,KAAK/C,CAAC,GAAG,CAAC,EAAE;MACtBoD,CAAC,CAACE,IAAI,CAACV,MAAM,CAACW,aAAa,CAAC,MAAM,CAAC,CAAC;IACtC,CAAC,MAAM;MACL,MAAMC,CAAC,GAAGN,CAAC,CAACG,UAAU,CAACN,CAAC,GAAG,CAAC,CAAC;MAC7B,IAAI,MAAM,IAAIS,CAAC,IAAIA,CAAC,IAAI,MAAM,EAAE;QAC9B,MAAMC,CAAC,GAAGX,CAAC,GAAG,KAAK;QACnB,MAAMY,CAAC,GAAGF,CAAC,GAAG,KAAK;QACnBJ,CAAC,CAACE,IAAI,CAACV,MAAM,CAACW,aAAa,CAAC,CAAC,CAAC,IAAI,EAAE,IAAK,CAAC,CAAC,IAAI,CAAC,IAAIE,CAAE,GAAGC,CAAC,CAAC,CAAC;QAC5D,EAAEX,CAAC;MACL,CAAC,MAAM;QACLK,CAAC,CAACE,IAAI,CAACV,MAAM,CAACW,aAAa,CAAC,MAAM,CAAC,CAAC;MACtC;IACF;EACF;EAEA,OAAOH,CAAC,CAACO,IAAI,CAAC,EAAE,CAAC;AACnB,CAAC;AAEDhC,OAAO,CAACiC,MAAM,GAAG,CAACtE,KAAK,EAAEL,OAAO,GAAG,CAAC,CAAC,KAAK;EACxC,IAAIK,KAAK,KAAK,IAAI,IAAK,OAAOA,KAAK,KAAK,QAAQ,IAAI,OAAOA,KAAK,KAAK,UAAW,EAAE;IAChF,MAAMR,aAAa,CAACS,SAAS,EAAE,kBAAkB,EAAEN,OAAO,CAAC;EAC7D;EAEA,OAAOK,KAAK;AACd,CAAC;AAED,MAAMuE,kBAAkB,GACpBvB,MAAM,CAACwB,wBAAwB,CAACC,WAAW,CAACC,SAAS,EAAE,YAAY,CAAC,CAACC,GAAG;AAC5E,MAAMC,mBAAmB,GACrB,OAAOC,iBAAiB,KAAK,UAAU,GACrC7B,MAAM,CAACwB,wBAAwB,CAACK,iBAAiB,CAACH,SAAS,EAAE,YAAY,CAAC,CAACC,GAAG,GAC9E,IAAI;AAEV,SAASG,sBAAsBA,CAAC9E,KAAK,EAAE;EACrC,IAAI;IACF;IACA;IACAuE,kBAAkB,CAACQ,IAAI,CAAC/E,KAAK,CAAC;IAE9B,OAAO,IAAI;EACb,CAAC,CAAC,MAAM;IACN,OAAO,KAAK;EACd;AACF;AAEA,SAASgF,mBAAmBA,CAAChF,KAAK,EAAE;EAClC,IAAI;IACF4E,mBAAmB,CAACG,IAAI,CAAC/E,KAAK,CAAC;IAC/B,OAAO,IAAI;EACb,CAAC,CAAC,MAAM;IACN,OAAO,KAAK;EACd;AACF;AAEA,SAASiF,qBAAqBA,CAACjF,KAAK,EAAE;EACpC,IAAI;IACF;IACA,IAAIkF,UAAU,CAAClF,KAAK,CAAC;IACrB,OAAO,KAAK;EACd,CAAC,CAAC,MAAM;IACN,OAAO,IAAI;EACb;AACF;AAEAqC,OAAO,CAACoC,WAAW,GAAG,CAACzE,KAAK,EAAEL,OAAO,GAAG,CAAC,CAAC,KAAK;EAC7C,IAAI,CAACmF,sBAAsB,CAAC9E,KAAK,CAAC,EAAE;IAClC,IAAIL,OAAO,CAACwF,WAAW,IAAI,CAACH,mBAAmB,CAAChF,KAAK,CAAC,EAAE;MACtD,MAAMR,aAAa,CAACS,SAAS,EAAE,4CAA4C,EAAEN,OAAO,CAAC;IACvF;IACA,MAAMH,aAAa,CAACS,SAAS,EAAE,uBAAuB,EAAEN,OAAO,CAAC;EAClE;EACA,IAAIsF,qBAAqB,CAACjF,KAAK,CAAC,EAAE;IAChC,MAAMR,aAAa,CAACS,SAAS,EAAE,2BAA2B,EAAEN,OAAO,CAAC;EACtE;EAEA,OAAOK,KAAK;AACd,CAAC;AAED,MAAMoF,kBAAkB,GACpBpC,MAAM,CAACwB,wBAAwB,CAACa,QAAQ,CAACX,SAAS,EAAE,YAAY,CAAC,CAACC,GAAG;AACzEtC,OAAO,CAACgD,QAAQ,GAAG,CAACrF,KAAK,EAAEL,OAAO,GAAG,CAAC,CAAC,KAAK;EAC1C,IAAI;IACFyF,kBAAkB,CAACL,IAAI,CAAC/E,KAAK,CAAC;EAChC,CAAC,CAAC,OAAOsF,CAAC,EAAE;IACV,MAAM9F,aAAa,CAACS,SAAS,EAAE,mBAAmB,EAAEN,OAAO,CAAC;EAC9D;EAEA,IAAI,CAACA,OAAO,CAACwF,WAAW,IAAIH,mBAAmB,CAAChF,KAAK,CAACuF,MAAM,CAAC,EAAE;IAC7D,MAAM/F,aAAa,CAACS,SAAS,EAAE,wDAAwD,EAAEN,OAAO,CAAC;EACnG;EACA,IAAIsF,qBAAqB,CAACjF,KAAK,CAACuF,MAAM,CAAC,EAAE;IACvC,MAAM/F,aAAa,CAACS,SAAS,EAAE,qCAAqC,EAAEN,OAAO,CAAC;EAChF;EAEA,OAAOK,KAAK;AACd,CAAC;;AAED;AACA;AACA;AACA;AACA,MAAMwF,oBAAoB,GAAGxC,MAAM,CAACwB,wBAAwB,CAC1DxB,MAAM,CAACyC,cAAc,CAACP,UAAU,CAAC,CAACR,SAAS,EAC3CgB,MAAM,CAACC,WACT,CAAC,CAAChB,GAAG;AACL,CACEiB,SAAS,EACTC,UAAU,EACVC,UAAU,EACVZ,UAAU,EACVa,WAAW,EACXC,WAAW,EACXC,iBAAiB,EACjBC,YAAY,EACZC,YAAY,CACb,CAACC,OAAO,CAACC,IAAI,IAAI;EAChB,MAAM;IAAExG;EAAK,CAAC,GAAGwG,IAAI;EACrB,MAAMC,OAAO,GAAG,WAAW,CAACC,IAAI,CAAC1G,IAAI,CAAC,GAAG,IAAI,GAAG,GAAG;EACnDwC,OAAO,CAACxC,IAAI,CAAC,GAAG,CAACG,KAAK,EAAEL,OAAO,GAAG,CAAC,CAAC,KAAK;IACvC,IAAI,CAAC8E,WAAW,CAAC+B,MAAM,CAACxG,KAAK,CAAC,IAAIwF,oBAAoB,CAACT,IAAI,CAAC/E,KAAK,CAAC,KAAKH,IAAI,EAAE;MAC3E,MAAML,aAAa,CAACS,SAAS,EAAE,UAAUqG,OAAO,IAAIzG,IAAI,SAAS,EAAEF,OAAO,CAAC;IAC7E;IACA,IAAI,CAACA,OAAO,CAACwF,WAAW,IAAIH,mBAAmB,CAAChF,KAAK,CAACuF,MAAM,CAAC,EAAE;MAC7D,MAAM/F,aAAa,CAACS,SAAS,EAAE,wDAAwD,EAAEN,OAAO,CAAC;IACnG;IACA,IAAIsF,qBAAqB,CAACjF,KAAK,CAACuF,MAAM,CAAC,EAAE;MACvC,MAAM/F,aAAa,CAACS,SAAS,EAAE,qCAAqC,EAAEN,OAAO,CAAC;IAChF;IAEA,OAAOK,KAAK;EACd,CAAC;AACH,CAAC,CAAC;;AAEF;;AAEAqC,OAAO,CAACoE,eAAe,GAAG,CAACzG,KAAK,EAAEL,OAAO,GAAG,CAAC,CAAC,KAAK;EACjD,IAAI,CAAC8E,WAAW,CAAC+B,MAAM,CAACxG,KAAK,CAAC,EAAE;IAC9B,MAAMR,aAAa,CAACS,SAAS,EAAE,sDAAsD,EAAEN,OAAO,CAAC;EACjG;EAEA,IAAI,CAACA,OAAO,CAACwF,WAAW,IAAIH,mBAAmB,CAAChF,KAAK,CAACuF,MAAM,CAAC,EAAE;IAC7D,MAAM/F,aAAa,CAACS,SAAS,EAAE,wDAAwD,EAAEN,OAAO,CAAC;EACnG;EAEA,IAAIsF,qBAAqB,CAACjF,KAAK,CAACuF,MAAM,CAAC,EAAE;IACvC,MAAM/F,aAAa,CAACS,SAAS,EAAE,qCAAqC,EAAEN,OAAO,CAAC;EAChF;EACA,OAAOK,KAAK;AACd,CAAC;AAEDqC,OAAO,CAACqE,YAAY,GAAG,CAAC1G,KAAK,EAAEL,OAAO,GAAG,CAAC,CAAC,KAAK;EAC9C,IAAI8E,WAAW,CAAC+B,MAAM,CAACxG,KAAK,CAAC,EAAE;IAC7B,IAAI,CAACL,OAAO,CAACwF,WAAW,IAAIH,mBAAmB,CAAChF,KAAK,CAACuF,MAAM,CAAC,EAAE;MAC7D,MAAM/F,aAAa,CAACS,SAAS,EAAE,wDAAwD,EAAEN,OAAO,CAAC;IACnG;IAEA,IAAIsF,qBAAqB,CAACjF,KAAK,CAACuF,MAAM,CAAC,EAAE;MACvC,MAAM/F,aAAa,CAACS,SAAS,EAAE,qCAAqC,EAAEN,OAAO,CAAC;IAChF;IACA,OAAOK,KAAK;EACd;EAEA,IAAI,CAACL,OAAO,CAACwF,WAAW,IAAI,CAACL,sBAAsB,CAAC9E,KAAK,CAAC,EAAE;IAC1D,MAAMR,aAAa,CAACS,SAAS,EAAE,wCAAwC,EAAEN,OAAO,CAAC;EACnF;EACA,IAAIA,OAAO,CAACwF,WAAW,IAAI,CAACH,mBAAmB,CAAChF,KAAK,CAAC,IAAI,CAAC8E,sBAAsB,CAAC9E,KAAK,CAAC,EAAE;IACxF,MAAMR,aAAa,CAACS,SAAS,EAAE,4DAA4D,EAAEN,OAAO,CAAC;EACvG;EACA,IAAIsF,qBAAqB,CAACjF,KAAK,CAAC,EAAE;IAChC,MAAMR,aAAa,CAACS,SAAS,EAAE,2BAA2B,EAAEN,OAAO,CAAC;EACtE;EAEA,OAAOK,KAAK;AACd,CAAC;AAEDqC,OAAO,CAACsE,YAAY,GAAGtE,OAAO,CAAC,oBAAoB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}