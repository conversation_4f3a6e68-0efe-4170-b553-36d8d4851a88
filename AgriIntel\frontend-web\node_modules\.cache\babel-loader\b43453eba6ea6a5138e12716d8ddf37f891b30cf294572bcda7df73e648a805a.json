{"ast": null, "code": "import * as React from 'react';\nimport { Store } from '../../utils/Store';\nimport { useGridApiMethod } from '../utils/useGridApiMethod';\nimport { GridSignature } from '../utils/useGridApiEventHandler';\nimport { EventManager } from '../../utils/EventManager';\nconst SYMBOL_API_PRIVATE = Symbol('mui.api_private');\nconst isSyntheticEvent = event => {\n  return event.isPropagationStopped !== undefined;\n};\nexport function unwrapPrivateAPI(publicApi) {\n  return publicApi[SYMBOL_API_PRIVATE];\n}\nlet globalId = 0;\nfunction createPrivateAPI(publicApiRef) {\n  var _publicApiRef$current;\n  const existingPrivateApi = (_publicApiRef$current = publicApiRef.current) == null ? void 0 : _publicApiRef$current[SYMBOL_API_PRIVATE];\n  if (existingPrivateApi) {\n    return existingPrivateApi;\n  }\n  const state = {};\n  const privateApi = {\n    state,\n    store: Store.create(state),\n    instanceId: {\n      id: globalId\n    }\n  };\n  globalId += 1;\n  privateApi.getPublicApi = () => publicApiRef.current;\n  privateApi.register = (visibility, methods) => {\n    Object.keys(methods).forEach(methodName => {\n      const method = methods[methodName];\n      const currentPrivateMethod = privateApi[methodName];\n      if ((currentPrivateMethod == null ? void 0 : currentPrivateMethod.spying) === true) {\n        currentPrivateMethod.target = method;\n      } else {\n        privateApi[methodName] = method;\n      }\n      if (visibility === 'public') {\n        const publicApi = publicApiRef.current;\n        const currentPublicMethod = publicApi[methodName];\n        if ((currentPublicMethod == null ? void 0 : currentPublicMethod.spying) === true) {\n          currentPublicMethod.target = method;\n        } else {\n          publicApi[methodName] = method;\n        }\n      }\n    });\n  };\n  privateApi.register('private', {\n    caches: {},\n    eventManager: new EventManager()\n  });\n  return privateApi;\n}\nfunction createPublicAPI(privateApiRef) {\n  const publicApi = {\n    get state() {\n      return privateApiRef.current.state;\n    },\n    get store() {\n      return privateApiRef.current.store;\n    },\n    get instanceId() {\n      return privateApiRef.current.instanceId;\n    },\n    [SYMBOL_API_PRIVATE]: privateApiRef.current\n  };\n  return publicApi;\n}\nexport function useGridApiInitialization(inputApiRef, props) {\n  const publicApiRef = React.useRef();\n  const privateApiRef = React.useRef();\n  if (!privateApiRef.current) {\n    privateApiRef.current = createPrivateAPI(publicApiRef);\n  }\n  if (!publicApiRef.current) {\n    publicApiRef.current = createPublicAPI(privateApiRef);\n  }\n  const publishEvent = React.useCallback((...args) => {\n    const [name, params, event = {}] = args;\n    event.defaultMuiPrevented = false;\n    if (isSyntheticEvent(event) && event.isPropagationStopped()) {\n      return;\n    }\n    const details = props.signature === GridSignature.DataGridPro ? {\n      api: privateApiRef.current.getPublicApi()\n    } : {};\n    privateApiRef.current.eventManager.emit(name, params, event, details);\n  }, [privateApiRef, props.signature]);\n  const subscribeEvent = React.useCallback((event, handler, options) => {\n    privateApiRef.current.eventManager.on(event, handler, options);\n    const api = privateApiRef.current;\n    return () => {\n      api.eventManager.removeListener(event, handler);\n    };\n  }, [privateApiRef]);\n  useGridApiMethod(privateApiRef, {\n    subscribeEvent,\n    publishEvent\n  }, 'public');\n  React.useImperativeHandle(inputApiRef, () => publicApiRef.current, [publicApiRef]);\n  React.useEffect(() => {\n    const api = privateApiRef.current;\n    return () => {\n      api.publishEvent('unmount');\n    };\n  }, [privateApiRef]);\n  return privateApiRef;\n}", "map": {"version": 3, "names": ["React", "Store", "useGridApiMethod", "GridSignature", "EventManager", "SYMBOL_API_PRIVATE", "Symbol", "isSyntheticEvent", "event", "isPropagationStopped", "undefined", "unwrapPrivateAPI", "publicApi", "globalId", "createPrivateAPI", "publicApiRef", "_publicApiRef$current", "existingPrivateApi", "current", "state", "privateApi", "store", "create", "instanceId", "id", "getPublicApi", "register", "visibility", "methods", "Object", "keys", "for<PERSON>ach", "methodName", "method", "currentPrivateMethod", "spying", "target", "currentPublicMethod", "caches", "eventManager", "createPublicAPI", "privateApiRef", "useGridApiInitialization", "inputApiRef", "props", "useRef", "publishEvent", "useCallback", "args", "name", "params", "defaultMuiPrevented", "details", "signature", "DataGridPro", "api", "emit", "subscribeEvent", "handler", "options", "on", "removeListener", "useImperativeHandle", "useEffect"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/x-data-grid/hooks/core/useGridApiInitialization.js"], "sourcesContent": ["import * as React from 'react';\nimport { Store } from '../../utils/Store';\nimport { useGridApiMethod } from '../utils/useGridApiMethod';\nimport { GridSignature } from '../utils/useGridApiEventHandler';\nimport { EventManager } from '../../utils/EventManager';\nconst SYMBOL_API_PRIVATE = Symbol('mui.api_private');\nconst isSyntheticEvent = event => {\n  return event.isPropagationStopped !== undefined;\n};\nexport function unwrapPrivateAPI(publicApi) {\n  return publicApi[SYMBOL_API_PRIVATE];\n}\nlet globalId = 0;\nfunction createPrivateAPI(publicApiRef) {\n  var _publicApiRef$current;\n  const existingPrivateApi = (_publicApiRef$current = publicApiRef.current) == null ? void 0 : _publicApiRef$current[SYMBOL_API_PRIVATE];\n  if (existingPrivateApi) {\n    return existingPrivateApi;\n  }\n  const state = {};\n  const privateApi = {\n    state,\n    store: Store.create(state),\n    instanceId: {\n      id: globalId\n    }\n  };\n  globalId += 1;\n  privateApi.getPublicApi = () => publicApiRef.current;\n  privateApi.register = (visibility, methods) => {\n    Object.keys(methods).forEach(methodName => {\n      const method = methods[methodName];\n      const currentPrivateMethod = privateApi[methodName];\n      if ((currentPrivateMethod == null ? void 0 : currentPrivateMethod.spying) === true) {\n        currentPrivateMethod.target = method;\n      } else {\n        privateApi[methodName] = method;\n      }\n      if (visibility === 'public') {\n        const publicApi = publicApiRef.current;\n        const currentPublicMethod = publicApi[methodName];\n        if ((currentPublicMethod == null ? void 0 : currentPublicMethod.spying) === true) {\n          currentPublicMethod.target = method;\n        } else {\n          publicApi[methodName] = method;\n        }\n      }\n    });\n  };\n  privateApi.register('private', {\n    caches: {},\n    eventManager: new EventManager()\n  });\n  return privateApi;\n}\nfunction createPublicAPI(privateApiRef) {\n  const publicApi = {\n    get state() {\n      return privateApiRef.current.state;\n    },\n    get store() {\n      return privateApiRef.current.store;\n    },\n    get instanceId() {\n      return privateApiRef.current.instanceId;\n    },\n    [SYMBOL_API_PRIVATE]: privateApiRef.current\n  };\n  return publicApi;\n}\nexport function useGridApiInitialization(inputApiRef, props) {\n  const publicApiRef = React.useRef();\n  const privateApiRef = React.useRef();\n  if (!privateApiRef.current) {\n    privateApiRef.current = createPrivateAPI(publicApiRef);\n  }\n  if (!publicApiRef.current) {\n    publicApiRef.current = createPublicAPI(privateApiRef);\n  }\n  const publishEvent = React.useCallback((...args) => {\n    const [name, params, event = {}] = args;\n    event.defaultMuiPrevented = false;\n    if (isSyntheticEvent(event) && event.isPropagationStopped()) {\n      return;\n    }\n    const details = props.signature === GridSignature.DataGridPro ? {\n      api: privateApiRef.current.getPublicApi()\n    } : {};\n    privateApiRef.current.eventManager.emit(name, params, event, details);\n  }, [privateApiRef, props.signature]);\n  const subscribeEvent = React.useCallback((event, handler, options) => {\n    privateApiRef.current.eventManager.on(event, handler, options);\n    const api = privateApiRef.current;\n    return () => {\n      api.eventManager.removeListener(event, handler);\n    };\n  }, [privateApiRef]);\n  useGridApiMethod(privateApiRef, {\n    subscribeEvent,\n    publishEvent\n  }, 'public');\n  React.useImperativeHandle(inputApiRef, () => publicApiRef.current, [publicApiRef]);\n  React.useEffect(() => {\n    const api = privateApiRef.current;\n    return () => {\n      api.publishEvent('unmount');\n    };\n  }, [privateApiRef]);\n  return privateApiRef;\n}"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,KAAK,QAAQ,mBAAmB;AACzC,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,YAAY,QAAQ,0BAA0B;AACvD,MAAMC,kBAAkB,GAAGC,MAAM,CAAC,iBAAiB,CAAC;AACpD,MAAMC,gBAAgB,GAAGC,KAAK,IAAI;EAChC,OAAOA,KAAK,CAACC,oBAAoB,KAAKC,SAAS;AACjD,CAAC;AACD,OAAO,SAASC,gBAAgBA,CAACC,SAAS,EAAE;EAC1C,OAAOA,SAAS,CAACP,kBAAkB,CAAC;AACtC;AACA,IAAIQ,QAAQ,GAAG,CAAC;AAChB,SAASC,gBAAgBA,CAACC,YAAY,EAAE;EACtC,IAAIC,qBAAqB;EACzB,MAAMC,kBAAkB,GAAG,CAACD,qBAAqB,GAAGD,YAAY,CAACG,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGF,qBAAqB,CAACX,kBAAkB,CAAC;EACtI,IAAIY,kBAAkB,EAAE;IACtB,OAAOA,kBAAkB;EAC3B;EACA,MAAME,KAAK,GAAG,CAAC,CAAC;EAChB,MAAMC,UAAU,GAAG;IACjBD,KAAK;IACLE,KAAK,EAAEpB,KAAK,CAACqB,MAAM,CAACH,KAAK,CAAC;IAC1BI,UAAU,EAAE;MACVC,EAAE,EAAEX;IACN;EACF,CAAC;EACDA,QAAQ,IAAI,CAAC;EACbO,UAAU,CAACK,YAAY,GAAG,MAAMV,YAAY,CAACG,OAAO;EACpDE,UAAU,CAACM,QAAQ,GAAG,CAACC,UAAU,EAAEC,OAAO,KAAK;IAC7CC,MAAM,CAACC,IAAI,CAACF,OAAO,CAAC,CAACG,OAAO,CAACC,UAAU,IAAI;MACzC,MAAMC,MAAM,GAAGL,OAAO,CAACI,UAAU,CAAC;MAClC,MAAME,oBAAoB,GAAGd,UAAU,CAACY,UAAU,CAAC;MACnD,IAAI,CAACE,oBAAoB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,oBAAoB,CAACC,MAAM,MAAM,IAAI,EAAE;QAClFD,oBAAoB,CAACE,MAAM,GAAGH,MAAM;MACtC,CAAC,MAAM;QACLb,UAAU,CAACY,UAAU,CAAC,GAAGC,MAAM;MACjC;MACA,IAAIN,UAAU,KAAK,QAAQ,EAAE;QAC3B,MAAMf,SAAS,GAAGG,YAAY,CAACG,OAAO;QACtC,MAAMmB,mBAAmB,GAAGzB,SAAS,CAACoB,UAAU,CAAC;QACjD,IAAI,CAACK,mBAAmB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,mBAAmB,CAACF,MAAM,MAAM,IAAI,EAAE;UAChFE,mBAAmB,CAACD,MAAM,GAAGH,MAAM;QACrC,CAAC,MAAM;UACLrB,SAAS,CAACoB,UAAU,CAAC,GAAGC,MAAM;QAChC;MACF;IACF,CAAC,CAAC;EACJ,CAAC;EACDb,UAAU,CAACM,QAAQ,CAAC,SAAS,EAAE;IAC7BY,MAAM,EAAE,CAAC,CAAC;IACVC,YAAY,EAAE,IAAInC,YAAY,CAAC;EACjC,CAAC,CAAC;EACF,OAAOgB,UAAU;AACnB;AACA,SAASoB,eAAeA,CAACC,aAAa,EAAE;EACtC,MAAM7B,SAAS,GAAG;IAChB,IAAIO,KAAKA,CAAA,EAAG;MACV,OAAOsB,aAAa,CAACvB,OAAO,CAACC,KAAK;IACpC,CAAC;IACD,IAAIE,KAAKA,CAAA,EAAG;MACV,OAAOoB,aAAa,CAACvB,OAAO,CAACG,KAAK;IACpC,CAAC;IACD,IAAIE,UAAUA,CAAA,EAAG;MACf,OAAOkB,aAAa,CAACvB,OAAO,CAACK,UAAU;IACzC,CAAC;IACD,CAAClB,kBAAkB,GAAGoC,aAAa,CAACvB;EACtC,CAAC;EACD,OAAON,SAAS;AAClB;AACA,OAAO,SAAS8B,wBAAwBA,CAACC,WAAW,EAAEC,KAAK,EAAE;EAC3D,MAAM7B,YAAY,GAAGf,KAAK,CAAC6C,MAAM,CAAC,CAAC;EACnC,MAAMJ,aAAa,GAAGzC,KAAK,CAAC6C,MAAM,CAAC,CAAC;EACpC,IAAI,CAACJ,aAAa,CAACvB,OAAO,EAAE;IAC1BuB,aAAa,CAACvB,OAAO,GAAGJ,gBAAgB,CAACC,YAAY,CAAC;EACxD;EACA,IAAI,CAACA,YAAY,CAACG,OAAO,EAAE;IACzBH,YAAY,CAACG,OAAO,GAAGsB,eAAe,CAACC,aAAa,CAAC;EACvD;EACA,MAAMK,YAAY,GAAG9C,KAAK,CAAC+C,WAAW,CAAC,CAAC,GAAGC,IAAI,KAAK;IAClD,MAAM,CAACC,IAAI,EAAEC,MAAM,EAAE1C,KAAK,GAAG,CAAC,CAAC,CAAC,GAAGwC,IAAI;IACvCxC,KAAK,CAAC2C,mBAAmB,GAAG,KAAK;IACjC,IAAI5C,gBAAgB,CAACC,KAAK,CAAC,IAAIA,KAAK,CAACC,oBAAoB,CAAC,CAAC,EAAE;MAC3D;IACF;IACA,MAAM2C,OAAO,GAAGR,KAAK,CAACS,SAAS,KAAKlD,aAAa,CAACmD,WAAW,GAAG;MAC9DC,GAAG,EAAEd,aAAa,CAACvB,OAAO,CAACO,YAAY,CAAC;IAC1C,CAAC,GAAG,CAAC,CAAC;IACNgB,aAAa,CAACvB,OAAO,CAACqB,YAAY,CAACiB,IAAI,CAACP,IAAI,EAAEC,MAAM,EAAE1C,KAAK,EAAE4C,OAAO,CAAC;EACvE,CAAC,EAAE,CAACX,aAAa,EAAEG,KAAK,CAACS,SAAS,CAAC,CAAC;EACpC,MAAMI,cAAc,GAAGzD,KAAK,CAAC+C,WAAW,CAAC,CAACvC,KAAK,EAAEkD,OAAO,EAAEC,OAAO,KAAK;IACpElB,aAAa,CAACvB,OAAO,CAACqB,YAAY,CAACqB,EAAE,CAACpD,KAAK,EAAEkD,OAAO,EAAEC,OAAO,CAAC;IAC9D,MAAMJ,GAAG,GAAGd,aAAa,CAACvB,OAAO;IACjC,OAAO,MAAM;MACXqC,GAAG,CAAChB,YAAY,CAACsB,cAAc,CAACrD,KAAK,EAAEkD,OAAO,CAAC;IACjD,CAAC;EACH,CAAC,EAAE,CAACjB,aAAa,CAAC,CAAC;EACnBvC,gBAAgB,CAACuC,aAAa,EAAE;IAC9BgB,cAAc;IACdX;EACF,CAAC,EAAE,QAAQ,CAAC;EACZ9C,KAAK,CAAC8D,mBAAmB,CAACnB,WAAW,EAAE,MAAM5B,YAAY,CAACG,OAAO,EAAE,CAACH,YAAY,CAAC,CAAC;EAClFf,KAAK,CAAC+D,SAAS,CAAC,MAAM;IACpB,MAAMR,GAAG,GAAGd,aAAa,CAACvB,OAAO;IACjC,OAAO,MAAM;MACXqC,GAAG,CAACT,YAAY,CAAC,SAAS,CAAC;IAC7B,CAAC;EACH,CAAC,EAAE,CAACL,aAAa,CAAC,CAAC;EACnB,OAAOA,aAAa;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}