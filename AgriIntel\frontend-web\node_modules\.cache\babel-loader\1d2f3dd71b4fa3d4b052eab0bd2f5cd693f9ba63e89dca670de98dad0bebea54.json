{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.BulkWriteOperation = void 0;\nconst operation_1 = require(\"./operation\");\n/** @internal */\nclass BulkWriteOperation extends operation_1.AbstractOperation {\n  constructor(collection, operations, options) {\n    super(options);\n    this.options = options;\n    this.collection = collection;\n    this.operations = operations;\n  }\n  get commandName() {\n    return 'bulkWrite';\n  }\n  async execute(server, session, timeoutContext) {\n    const coll = this.collection;\n    const operations = this.operations;\n    const options = {\n      ...this.options,\n      ...this.bsonOptions,\n      readPreference: this.readPreference,\n      timeoutContext\n    };\n    // Create the bulk operation\n    const bulk = options.ordered === false ? coll.initializeUnorderedBulkOp(options) : coll.initializeOrderedBulkOp(options);\n    // for each op go through and add to the bulk\n    for (let i = 0; i < operations.length; i++) {\n      bulk.raw(operations[i]);\n    }\n    // Execute the bulk\n    return await bulk.execute({\n      ...options,\n      session\n    });\n  }\n}\nexports.BulkWriteOperation = BulkWriteOperation;\n(0, operation_1.defineAspects)(BulkWriteOperation, [operation_1.Aspect.WRITE_OPERATION]);", "map": {"version": 3, "names": ["operation_1", "require", "BulkWriteOperation", "AbstractOperation", "constructor", "collection", "operations", "options", "commandName", "execute", "server", "session", "timeoutContext", "coll", "bsonOptions", "readPreference", "bulk", "ordered", "initializeUnorderedBulkOp", "initializeOrderedBulkOp", "i", "length", "raw", "exports", "defineAspects", "Aspect", "WRITE_OPERATION"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\bulk_write.ts"], "sourcesContent": ["import type {\n  AnyBulkWriteOperation,\n  BulkOperationBase,\n  BulkWriteOptions,\n  BulkWriteResult\n} from '../bulk/common';\nimport type { Collection } from '../collection';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { AbstractOperation, Aspect, defineAspects } from './operation';\n\n/** @internal */\nexport class BulkWriteOperation extends AbstractOperation<BulkWriteResult> {\n  override options: BulkWriteOptions;\n  collection: Collection;\n  operations: ReadonlyArray<AnyBulkWriteOperation>;\n\n  constructor(\n    collection: Collection,\n    operations: ReadonlyArray<AnyBulkWriteOperation>,\n    options: BulkWriteOptions\n  ) {\n    super(options);\n    this.options = options;\n    this.collection = collection;\n    this.operations = operations;\n  }\n\n  override get commandName() {\n    return 'bulkWrite' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<BulkWriteResult> {\n    const coll = this.collection;\n    const operations = this.operations;\n    const options = {\n      ...this.options,\n      ...this.bsonOptions,\n      readPreference: this.readPreference,\n      timeoutContext\n    };\n\n    // Create the bulk operation\n    const bulk: BulkOperationBase =\n      options.ordered === false\n        ? coll.initializeUnorderedBulkOp(options)\n        : coll.initializeOrderedBulkOp(options);\n\n    // for each op go through and add to the bulk\n    for (let i = 0; i < operations.length; i++) {\n      bulk.raw(operations[i]);\n    }\n\n    // Execute the bulk\n    return await bulk.execute({ ...options, session });\n  }\n}\n\ndefineAspects(BulkWriteOperation, [Aspect.WRITE_OPERATION]);\n"], "mappings": ";;;;;;AAUA,MAAAA,WAAA,GAAAC,OAAA;AAEA;AACA,MAAaC,kBAAmB,SAAQF,WAAA,CAAAG,iBAAkC;EAKxEC,YACEC,UAAsB,EACtBC,UAAgD,EAChDC,OAAyB;IAEzB,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACF,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;EAEA,IAAaE,WAAWA,CAAA;IACtB,OAAO,WAAoB;EAC7B;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMC,IAAI,GAAG,IAAI,CAACR,UAAU;IAC5B,MAAMC,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,MAAMC,OAAO,GAAG;MACd,GAAG,IAAI,CAACA,OAAO;MACf,GAAG,IAAI,CAACO,WAAW;MACnBC,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCH;KACD;IAED;IACA,MAAMI,IAAI,GACRT,OAAO,CAACU,OAAO,KAAK,KAAK,GACrBJ,IAAI,CAACK,yBAAyB,CAACX,OAAO,CAAC,GACvCM,IAAI,CAACM,uBAAuB,CAACZ,OAAO,CAAC;IAE3C;IACA,KAAK,IAAIa,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,UAAU,CAACe,MAAM,EAAED,CAAC,EAAE,EAAE;MAC1CJ,IAAI,CAACM,GAAG,CAAChB,UAAU,CAACc,CAAC,CAAC,CAAC;IACzB;IAEA;IACA,OAAO,MAAMJ,IAAI,CAACP,OAAO,CAAC;MAAE,GAAGF,OAAO;MAAEI;IAAO,CAAE,CAAC;EACpD;;AA/CFY,OAAA,CAAArB,kBAAA,GAAAA,kBAAA;AAkDA,IAAAF,WAAA,CAAAwB,aAAa,EAACtB,kBAAkB,EAAE,CAACF,WAAA,CAAAyB,MAAM,CAACC,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}