# Database Configuration
MONGODB_URI=mongodb+srv://luckyrakgama:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0
MONGODB_DB_NAME=AMPD_Live_Stock

# Server Configuration
PORT=3001
NODE_ENV=development

# JWT Configuration
JWT_SECRET=ampd_livestock_jwt_secret_key_2024
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=ampd_livestock_refresh_secret_key_2024
JWT_REFRESH_EXPIRES_IN=7d

# Language Configuration
DEFAULT_LANGUAGE=en
AVAILABLE_LANGUAGES=en,af,st,tn,zu

# Mock Data Configuration
USE_MOCK_DATA=false

# Email Configuration (for notifications)
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USER=<EMAIL>
EMAIL_PASS=your-app-password

# File Upload Configuration
MAX_FILE_SIZE=10MB
UPLOAD_PATH=./uploads

# Security Configuration
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX_REQUESTS=100

# API Documentation
API_DOCS_ENABLED=true
API_DOCS_PATH=/api/docs
