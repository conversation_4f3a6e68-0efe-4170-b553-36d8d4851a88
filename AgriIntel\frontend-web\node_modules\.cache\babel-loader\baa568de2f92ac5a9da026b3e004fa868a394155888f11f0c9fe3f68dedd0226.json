{"ast": null, "code": "import style from './style';\nimport compose from './compose';\nexport const displayPrint = style({\n  prop: 'displayPrint',\n  cssProperty: false,\n  transform: value => ({\n    '@media print': {\n      display: value\n    }\n  })\n});\nexport const displayRaw = style({\n  prop: 'display'\n});\nexport const overflow = style({\n  prop: 'overflow'\n});\nexport const textOverflow = style({\n  prop: 'textOverflow'\n});\nexport const visibility = style({\n  prop: 'visibility'\n});\nexport const whiteSpace = style({\n  prop: 'whiteSpace'\n});\nexport default compose(displayPrint, displayRaw, overflow, textOverflow, visibility, whiteSpace);", "map": {"version": 3, "names": ["style", "compose", "displayPrint", "prop", "cssProperty", "transform", "value", "display", "displayRaw", "overflow", "textOverflow", "visibility", "whiteSpace"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/system/esm/display.js"], "sourcesContent": ["import style from './style';\nimport compose from './compose';\nexport const displayPrint = style({\n  prop: 'displayPrint',\n  cssProperty: false,\n  transform: value => ({\n    '@media print': {\n      display: value\n    }\n  })\n});\nexport const displayRaw = style({\n  prop: 'display'\n});\nexport const overflow = style({\n  prop: 'overflow'\n});\nexport const textOverflow = style({\n  prop: 'textOverflow'\n});\nexport const visibility = style({\n  prop: 'visibility'\n});\nexport const whiteSpace = style({\n  prop: 'whiteSpace'\n});\nexport default compose(displayPrint, displayRaw, overflow, textOverflow, visibility, whiteSpace);"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAC3B,OAAOC,OAAO,MAAM,WAAW;AAC/B,OAAO,MAAMC,YAAY,GAAGF,KAAK,CAAC;EAChCG,IAAI,EAAE,cAAc;EACpBC,WAAW,EAAE,KAAK;EAClBC,SAAS,EAAEC,KAAK,KAAK;IACnB,cAAc,EAAE;MACdC,OAAO,EAAED;IACX;EACF,CAAC;AACH,CAAC,CAAC;AACF,OAAO,MAAME,UAAU,GAAGR,KAAK,CAAC;EAC9BG,IAAI,EAAE;AACR,CAAC,CAAC;AACF,OAAO,MAAMM,QAAQ,GAAGT,KAAK,CAAC;EAC5BG,IAAI,EAAE;AACR,CAAC,CAAC;AACF,OAAO,MAAMO,YAAY,GAAGV,KAAK,CAAC;EAChCG,IAAI,EAAE;AACR,CAAC,CAAC;AACF,OAAO,MAAMQ,UAAU,GAAGX,KAAK,CAAC;EAC9BG,IAAI,EAAE;AACR,CAAC,CAAC;AACF,OAAO,MAAMS,UAAU,GAAGZ,KAAK,CAAC;EAC9BG,IAAI,EAAE;AACR,CAAC,CAAC;AACF,eAAeF,OAAO,CAACC,YAAY,EAAEM,UAAU,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,UAAU,EAAEC,UAAU,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}