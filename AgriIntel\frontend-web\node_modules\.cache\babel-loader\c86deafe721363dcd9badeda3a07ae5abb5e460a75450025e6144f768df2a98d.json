{"ast": null, "code": "var aws4 = exports,\n  url = require('url'),\n  querystring = require('querystring'),\n  crypto = require('crypto'),\n  lru = require('./lru'),\n  credentialsCache = lru(1000);\n\n// http://docs.amazonwebservices.com/general/latest/gr/signature-version-4.html\n\nfunction hmac(key, string, encoding) {\n  return crypto.createHmac('sha256', key).update(string, 'utf8').digest(encoding);\n}\nfunction hash(string, encoding) {\n  return crypto.createHash('sha256').update(string, 'utf8').digest(encoding);\n}\n\n// This function assumes the string has already been percent encoded\nfunction encodeRfc3986(urlEncodedString) {\n  return urlEncodedString.replace(/[!'()*]/g, function (c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase();\n  });\n}\nfunction encodeRfc3986Full(str) {\n  return encodeRfc3986(encodeURIComponent(str));\n}\n\n// A bit of a combination of:\n// https://github.com/aws/aws-sdk-java-v2/blob/dc695de6ab49ad03934e1b02e7263abbd2354be0/core/auth/src/main/java/software/amazon/awssdk/auth/signer/internal/AbstractAws4Signer.java#L59\n// https://github.com/aws/aws-sdk-js/blob/****************************************/lib/signers/v4.js#L191-L199\n// https://github.com/mhart/aws4fetch/blob/b3aed16b6f17384cf36ea33bcba3c1e9f3bdfefd/src/main.js#L25-L34\nvar HEADERS_TO_IGNORE = {\n  'authorization': true,\n  'connection': true,\n  'x-amzn-trace-id': true,\n  'user-agent': true,\n  'expect': true,\n  'presigned-expires': true,\n  'range': true\n};\n\n// request: { path | body, [host], [method], [headers], [service], [region] }\n// credentials: { accessKeyId, secretAccessKey, [sessionToken] }\nfunction RequestSigner(request, credentials) {\n  if (typeof request === 'string') request = url.parse(request);\n  var headers = request.headers = Object.assign({}, request.headers || {}),\n    hostParts = (!this.service || !this.region) && this.matchHost(request.hostname || request.host || headers.Host || headers.host);\n  this.request = request;\n  this.credentials = credentials || this.defaultCredentials();\n  this.service = request.service || hostParts[0] || '';\n  this.region = request.region || hostParts[1] || 'us-east-1';\n\n  // SES uses a different domain from the service name\n  if (this.service === 'email') this.service = 'ses';\n  if (!request.method && request.body) request.method = 'POST';\n  if (!headers.Host && !headers.host) {\n    headers.Host = request.hostname || request.host || this.createHost();\n\n    // If a port is specified explicitly, use it as is\n    if (request.port) headers.Host += ':' + request.port;\n  }\n  if (!request.hostname && !request.host) request.hostname = headers.Host || headers.host;\n  this.isCodeCommitGit = this.service === 'codecommit' && request.method === 'GIT';\n  this.extraHeadersToIgnore = request.extraHeadersToIgnore || Object.create(null);\n  this.extraHeadersToInclude = request.extraHeadersToInclude || Object.create(null);\n}\nRequestSigner.prototype.matchHost = function (host) {\n  var match = (host || '').match(/([^\\.]{1,63})\\.(?:([^\\.]{0,63})\\.)?amazonaws\\.com(\\.cn)?$/);\n  var hostParts = (match || []).slice(1, 3);\n\n  // ES's hostParts are sometimes the other way round, if the value that is expected\n  // to be region equals ‘es’ switch them back\n  // e.g. search-cluster-name-aaaa00aaaa0aaa0aaaaaaa0aaa.us-east-1.es.amazonaws.com\n  if (hostParts[1] === 'es' || hostParts[1] === 'aoss') hostParts = hostParts.reverse();\n  if (hostParts[1] == 's3') {\n    hostParts[0] = 's3';\n    hostParts[1] = 'us-east-1';\n  } else {\n    for (var i = 0; i < 2; i++) {\n      if (/^s3-/.test(hostParts[i])) {\n        hostParts[1] = hostParts[i].slice(3);\n        hostParts[0] = 's3';\n        break;\n      }\n    }\n  }\n  return hostParts;\n};\n\n// http://docs.aws.amazon.com/general/latest/gr/rande.html\nRequestSigner.prototype.isSingleRegion = function () {\n  // Special case for S3 and SimpleDB in us-east-1\n  if (['s3', 'sdb'].indexOf(this.service) >= 0 && this.region === 'us-east-1') return true;\n  return ['cloudfront', 'ls', 'route53', 'iam', 'importexport', 'sts'].indexOf(this.service) >= 0;\n};\nRequestSigner.prototype.createHost = function () {\n  var region = this.isSingleRegion() ? '' : '.' + this.region,\n    subdomain = this.service === 'ses' ? 'email' : this.service;\n  return subdomain + region + '.amazonaws.com';\n};\nRequestSigner.prototype.prepareRequest = function () {\n  this.parsePath();\n  var request = this.request,\n    headers = request.headers,\n    query;\n  if (request.signQuery) {\n    this.parsedPath.query = query = this.parsedPath.query || {};\n    if (this.credentials.sessionToken) query['X-Amz-Security-Token'] = this.credentials.sessionToken;\n    if (this.service === 's3' && !query['X-Amz-Expires']) query['X-Amz-Expires'] = 86400;\n    if (query['X-Amz-Date']) this.datetime = query['X-Amz-Date'];else query['X-Amz-Date'] = this.getDateTime();\n    query['X-Amz-Algorithm'] = 'AWS4-HMAC-SHA256';\n    query['X-Amz-Credential'] = this.credentials.accessKeyId + '/' + this.credentialString();\n    query['X-Amz-SignedHeaders'] = this.signedHeaders();\n  } else {\n    if (!request.doNotModifyHeaders && !this.isCodeCommitGit) {\n      if (request.body && !headers['Content-Type'] && !headers['content-type']) headers['Content-Type'] = 'application/x-www-form-urlencoded; charset=utf-8';\n      if (request.body && !headers['Content-Length'] && !headers['content-length']) headers['Content-Length'] = Buffer.byteLength(request.body);\n      if (this.credentials.sessionToken && !headers['X-Amz-Security-Token'] && !headers['x-amz-security-token']) headers['X-Amz-Security-Token'] = this.credentials.sessionToken;\n      if (this.service === 's3' && !headers['X-Amz-Content-Sha256'] && !headers['x-amz-content-sha256']) headers['X-Amz-Content-Sha256'] = hash(this.request.body || '', 'hex');\n      if (headers['X-Amz-Date'] || headers['x-amz-date']) this.datetime = headers['X-Amz-Date'] || headers['x-amz-date'];else headers['X-Amz-Date'] = this.getDateTime();\n    }\n    delete headers.Authorization;\n    delete headers.authorization;\n  }\n};\nRequestSigner.prototype.sign = function () {\n  if (!this.parsedPath) this.prepareRequest();\n  if (this.request.signQuery) {\n    this.parsedPath.query['X-Amz-Signature'] = this.signature();\n  } else {\n    this.request.headers.Authorization = this.authHeader();\n  }\n  this.request.path = this.formatPath();\n  return this.request;\n};\nRequestSigner.prototype.getDateTime = function () {\n  if (!this.datetime) {\n    var headers = this.request.headers,\n      date = new Date(headers.Date || headers.date || new Date());\n    this.datetime = date.toISOString().replace(/[:\\-]|\\.\\d{3}/g, '');\n\n    // Remove the trailing 'Z' on the timestamp string for CodeCommit git access\n    if (this.isCodeCommitGit) this.datetime = this.datetime.slice(0, -1);\n  }\n  return this.datetime;\n};\nRequestSigner.prototype.getDate = function () {\n  return this.getDateTime().substr(0, 8);\n};\nRequestSigner.prototype.authHeader = function () {\n  return ['AWS4-HMAC-SHA256 Credential=' + this.credentials.accessKeyId + '/' + this.credentialString(), 'SignedHeaders=' + this.signedHeaders(), 'Signature=' + this.signature()].join(', ');\n};\nRequestSigner.prototype.signature = function () {\n  var date = this.getDate(),\n    cacheKey = [this.credentials.secretAccessKey, date, this.region, this.service].join(),\n    kDate,\n    kRegion,\n    kService,\n    kCredentials = credentialsCache.get(cacheKey);\n  if (!kCredentials) {\n    kDate = hmac('AWS4' + this.credentials.secretAccessKey, date);\n    kRegion = hmac(kDate, this.region);\n    kService = hmac(kRegion, this.service);\n    kCredentials = hmac(kService, 'aws4_request');\n    credentialsCache.set(cacheKey, kCredentials);\n  }\n  return hmac(kCredentials, this.stringToSign(), 'hex');\n};\nRequestSigner.prototype.stringToSign = function () {\n  return ['AWS4-HMAC-SHA256', this.getDateTime(), this.credentialString(), hash(this.canonicalString(), 'hex')].join('\\n');\n};\nRequestSigner.prototype.canonicalString = function () {\n  if (!this.parsedPath) this.prepareRequest();\n  var pathStr = this.parsedPath.path,\n    query = this.parsedPath.query,\n    headers = this.request.headers,\n    queryStr = '',\n    normalizePath = this.service !== 's3',\n    decodePath = this.service === 's3' || this.request.doNotEncodePath,\n    decodeSlashesInPath = this.service === 's3',\n    firstValOnly = this.service === 's3',\n    bodyHash;\n  if (this.service === 's3' && this.request.signQuery) {\n    bodyHash = 'UNSIGNED-PAYLOAD';\n  } else if (this.isCodeCommitGit) {\n    bodyHash = '';\n  } else {\n    bodyHash = headers['X-Amz-Content-Sha256'] || headers['x-amz-content-sha256'] || hash(this.request.body || '', 'hex');\n  }\n  if (query) {\n    var reducedQuery = Object.keys(query).reduce(function (obj, key) {\n      if (!key) return obj;\n      obj[encodeRfc3986Full(key)] = !Array.isArray(query[key]) ? query[key] : firstValOnly ? query[key][0] : query[key];\n      return obj;\n    }, {});\n    var encodedQueryPieces = [];\n    Object.keys(reducedQuery).sort().forEach(function (key) {\n      if (!Array.isArray(reducedQuery[key])) {\n        encodedQueryPieces.push(key + '=' + encodeRfc3986Full(reducedQuery[key]));\n      } else {\n        reducedQuery[key].map(encodeRfc3986Full).sort().forEach(function (val) {\n          encodedQueryPieces.push(key + '=' + val);\n        });\n      }\n    });\n    queryStr = encodedQueryPieces.join('&');\n  }\n  if (pathStr !== '/') {\n    if (normalizePath) pathStr = pathStr.replace(/\\/{2,}/g, '/');\n    pathStr = pathStr.split('/').reduce(function (path, piece) {\n      if (normalizePath && piece === '..') {\n        path.pop();\n      } else if (!normalizePath || piece !== '.') {\n        if (decodePath) piece = decodeURIComponent(piece.replace(/\\+/g, ' '));\n        path.push(encodeRfc3986Full(piece));\n      }\n      return path;\n    }, []).join('/');\n    if (pathStr[0] !== '/') pathStr = '/' + pathStr;\n    if (decodeSlashesInPath) pathStr = pathStr.replace(/%2F/g, '/');\n  }\n  return [this.request.method || 'GET', pathStr, queryStr, this.canonicalHeaders() + '\\n', this.signedHeaders(), bodyHash].join('\\n');\n};\nRequestSigner.prototype.filterHeaders = function () {\n  var headers = this.request.headers,\n    extraHeadersToInclude = this.extraHeadersToInclude,\n    extraHeadersToIgnore = this.extraHeadersToIgnore;\n  this.filteredHeaders = Object.keys(headers).map(function (key) {\n    return [key.toLowerCase(), headers[key]];\n  }).filter(function (entry) {\n    return extraHeadersToInclude[entry[0]] || HEADERS_TO_IGNORE[entry[0]] == null && !extraHeadersToIgnore[entry[0]];\n  }).sort(function (a, b) {\n    return a[0] < b[0] ? -1 : 1;\n  });\n};\nRequestSigner.prototype.canonicalHeaders = function () {\n  if (!this.filteredHeaders) this.filterHeaders();\n  return this.filteredHeaders.map(function (entry) {\n    return entry[0] + ':' + entry[1].toString().trim().replace(/\\s+/g, ' ');\n  }).join('\\n');\n};\nRequestSigner.prototype.signedHeaders = function () {\n  if (!this.filteredHeaders) this.filterHeaders();\n  return this.filteredHeaders.map(function (entry) {\n    return entry[0];\n  }).join(';');\n};\nRequestSigner.prototype.credentialString = function () {\n  return [this.getDate(), this.region, this.service, 'aws4_request'].join('/');\n};\nRequestSigner.prototype.defaultCredentials = function () {\n  var env = process.env;\n  return {\n    accessKeyId: env.AWS_ACCESS_KEY_ID || env.AWS_ACCESS_KEY,\n    secretAccessKey: env.AWS_SECRET_ACCESS_KEY || env.AWS_SECRET_KEY,\n    sessionToken: env.AWS_SESSION_TOKEN\n  };\n};\nRequestSigner.prototype.parsePath = function () {\n  var path = this.request.path || '/';\n\n  // S3 doesn't always encode characters > 127 correctly and\n  // all services don't encode characters > 255 correctly\n  // So if there are non-reserved chars (and it's not already all % encoded), just encode them all\n  if (/[^0-9A-Za-z;,/?:@&=+$\\-_.!~*'()#%]/.test(path)) {\n    path = encodeURI(decodeURI(path));\n  }\n  var queryIx = path.indexOf('?'),\n    query = null;\n  if (queryIx >= 0) {\n    query = querystring.parse(path.slice(queryIx + 1));\n    path = path.slice(0, queryIx);\n  }\n  this.parsedPath = {\n    path: path,\n    query: query\n  };\n};\nRequestSigner.prototype.formatPath = function () {\n  var path = this.parsedPath.path,\n    query = this.parsedPath.query;\n  if (!query) return path;\n\n  // Services don't support empty query string keys\n  if (query[''] != null) delete query[''];\n  return path + '?' + encodeRfc3986(querystring.stringify(query));\n};\naws4.RequestSigner = RequestSigner;\naws4.sign = function (request, credentials) {\n  return new RequestSigner(request, credentials).sign();\n};", "map": {"version": 3, "names": ["aws4", "exports", "url", "require", "querystring", "crypto", "lru", "credentialsCache", "hmac", "key", "string", "encoding", "createHmac", "update", "digest", "hash", "createHash", "encodeRfc3986", "urlEncodedString", "replace", "c", "charCodeAt", "toString", "toUpperCase", "encodeRfc3986Full", "str", "encodeURIComponent", "HEADERS_TO_IGNORE", "RequestSigner", "request", "credentials", "parse", "headers", "Object", "assign", "hostParts", "service", "region", "matchHost", "hostname", "host", "Host", "defaultCredentials", "method", "body", "createHost", "port", "isCodeCommitGit", "extraHeadersToIgnore", "create", "extraHeadersToInclude", "prototype", "match", "slice", "reverse", "i", "test", "isSingleRegion", "indexOf", "subdomain", "prepareRequest", "parsePath", "query", "signQuery", "parsed<PERSON><PERSON>", "sessionToken", "datetime", "getDateTime", "accessKeyId", "credentialString", "signedHeaders", "doNotModifyHeaders", "<PERSON><PERSON><PERSON>", "byteLength", "Authorization", "authorization", "sign", "signature", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "path", "formatPath", "date", "Date", "toISOString", "getDate", "substr", "join", "cache<PERSON>ey", "secretAccessKey", "kDate", "kRegion", "kService", "kCredentials", "get", "set", "stringToSign", "canonicalString", "pathStr", "queryStr", "normalizePath", "decodePath", "doNotEncodePath", "decodeSlashesInPath", "firstValOnly", "bodyHash", "reducedQuery", "keys", "reduce", "obj", "Array", "isArray", "encodedQueryP<PERSON>ces", "sort", "for<PERSON>ach", "push", "map", "val", "split", "piece", "pop", "decodeURIComponent", "canonicalHeaders", "filterHeaders", "filteredHeaders", "toLowerCase", "filter", "entry", "a", "b", "trim", "env", "process", "AWS_ACCESS_KEY_ID", "AWS_ACCESS_KEY", "AWS_SECRET_ACCESS_KEY", "AWS_SECRET_KEY", "AWS_SESSION_TOKEN", "encodeURI", "decodeURI", "queryIx", "stringify"], "sources": ["C:/Users/<USER>/node_modules/aws4/aws4.js"], "sourcesContent": ["var aws4 = exports,\n    url = require('url'),\n    querystring = require('querystring'),\n    crypto = require('crypto'),\n    lru = require('./lru'),\n    credentialsCache = lru(1000)\n\n// http://docs.amazonwebservices.com/general/latest/gr/signature-version-4.html\n\nfunction hmac(key, string, encoding) {\n  return crypto.createHmac('sha256', key).update(string, 'utf8').digest(encoding)\n}\n\nfunction hash(string, encoding) {\n  return crypto.createHash('sha256').update(string, 'utf8').digest(encoding)\n}\n\n// This function assumes the string has already been percent encoded\nfunction encodeRfc3986(urlEncodedString) {\n  return urlEncodedString.replace(/[!'()*]/g, function(c) {\n    return '%' + c.charCodeAt(0).toString(16).toUpperCase()\n  })\n}\n\nfunction encodeRfc3986Full(str) {\n  return encodeRfc3986(encodeURIComponent(str))\n}\n\n// A bit of a combination of:\n// https://github.com/aws/aws-sdk-java-v2/blob/dc695de6ab49ad03934e1b02e7263abbd2354be0/core/auth/src/main/java/software/amazon/awssdk/auth/signer/internal/AbstractAws4Signer.java#L59\n// https://github.com/aws/aws-sdk-js/blob/****************************************/lib/signers/v4.js#L191-L199\n// https://github.com/mhart/aws4fetch/blob/b3aed16b6f17384cf36ea33bcba3c1e9f3bdfefd/src/main.js#L25-L34\nvar HEADERS_TO_IGNORE = {\n  'authorization': true,\n  'connection': true,\n  'x-amzn-trace-id': true,\n  'user-agent': true,\n  'expect': true,\n  'presigned-expires': true,\n  'range': true,\n}\n\n// request: { path | body, [host], [method], [headers], [service], [region] }\n// credentials: { accessKeyId, secretAccessKey, [sessionToken] }\nfunction RequestSigner(request, credentials) {\n\n  if (typeof request === 'string') request = url.parse(request)\n\n  var headers = request.headers = Object.assign({}, (request.headers || {})),\n      hostParts = (!this.service || !this.region) && this.matchHost(request.hostname || request.host || headers.Host || headers.host)\n\n  this.request = request\n  this.credentials = credentials || this.defaultCredentials()\n\n  this.service = request.service || hostParts[0] || ''\n  this.region = request.region || hostParts[1] || 'us-east-1'\n\n  // SES uses a different domain from the service name\n  if (this.service === 'email') this.service = 'ses'\n\n  if (!request.method && request.body)\n    request.method = 'POST'\n\n  if (!headers.Host && !headers.host) {\n    headers.Host = request.hostname || request.host || this.createHost()\n\n    // If a port is specified explicitly, use it as is\n    if (request.port)\n      headers.Host += ':' + request.port\n  }\n  if (!request.hostname && !request.host)\n    request.hostname = headers.Host || headers.host\n\n  this.isCodeCommitGit = this.service === 'codecommit' && request.method === 'GIT'\n\n  this.extraHeadersToIgnore = request.extraHeadersToIgnore || Object.create(null)\n  this.extraHeadersToInclude = request.extraHeadersToInclude || Object.create(null)\n}\n\nRequestSigner.prototype.matchHost = function(host) {\n  var match = (host || '').match(/([^\\.]{1,63})\\.(?:([^\\.]{0,63})\\.)?amazonaws\\.com(\\.cn)?$/)\n  var hostParts = (match || []).slice(1, 3)\n\n  // ES's hostParts are sometimes the other way round, if the value that is expected\n  // to be region equals ‘es’ switch them back\n  // e.g. search-cluster-name-aaaa00aaaa0aaa0aaaaaaa0aaa.us-east-1.es.amazonaws.com\n  if (hostParts[1] === 'es' || hostParts[1] === 'aoss')\n    hostParts = hostParts.reverse()\n\n  if (hostParts[1] == 's3') {\n    hostParts[0] = 's3'\n    hostParts[1] = 'us-east-1'\n  } else {\n    for (var i = 0; i < 2; i++) {\n      if (/^s3-/.test(hostParts[i])) {\n        hostParts[1] = hostParts[i].slice(3)\n        hostParts[0] = 's3'\n        break\n      }\n    }\n  }\n\n  return hostParts\n}\n\n// http://docs.aws.amazon.com/general/latest/gr/rande.html\nRequestSigner.prototype.isSingleRegion = function() {\n  // Special case for S3 and SimpleDB in us-east-1\n  if (['s3', 'sdb'].indexOf(this.service) >= 0 && this.region === 'us-east-1') return true\n\n  return ['cloudfront', 'ls', 'route53', 'iam', 'importexport', 'sts']\n    .indexOf(this.service) >= 0\n}\n\nRequestSigner.prototype.createHost = function() {\n  var region = this.isSingleRegion() ? '' : '.' + this.region,\n      subdomain = this.service === 'ses' ? 'email' : this.service\n  return subdomain + region + '.amazonaws.com'\n}\n\nRequestSigner.prototype.prepareRequest = function() {\n  this.parsePath()\n\n  var request = this.request, headers = request.headers, query\n\n  if (request.signQuery) {\n\n    this.parsedPath.query = query = this.parsedPath.query || {}\n\n    if (this.credentials.sessionToken)\n      query['X-Amz-Security-Token'] = this.credentials.sessionToken\n\n    if (this.service === 's3' && !query['X-Amz-Expires'])\n      query['X-Amz-Expires'] = 86400\n\n    if (query['X-Amz-Date'])\n      this.datetime = query['X-Amz-Date']\n    else\n      query['X-Amz-Date'] = this.getDateTime()\n\n    query['X-Amz-Algorithm'] = 'AWS4-HMAC-SHA256'\n    query['X-Amz-Credential'] = this.credentials.accessKeyId + '/' + this.credentialString()\n    query['X-Amz-SignedHeaders'] = this.signedHeaders()\n\n  } else {\n\n    if (!request.doNotModifyHeaders && !this.isCodeCommitGit) {\n      if (request.body && !headers['Content-Type'] && !headers['content-type'])\n        headers['Content-Type'] = 'application/x-www-form-urlencoded; charset=utf-8'\n\n      if (request.body && !headers['Content-Length'] && !headers['content-length'])\n        headers['Content-Length'] = Buffer.byteLength(request.body)\n\n      if (this.credentials.sessionToken && !headers['X-Amz-Security-Token'] && !headers['x-amz-security-token'])\n        headers['X-Amz-Security-Token'] = this.credentials.sessionToken\n\n      if (this.service === 's3' && !headers['X-Amz-Content-Sha256'] && !headers['x-amz-content-sha256'])\n        headers['X-Amz-Content-Sha256'] = hash(this.request.body || '', 'hex')\n\n      if (headers['X-Amz-Date'] || headers['x-amz-date'])\n        this.datetime = headers['X-Amz-Date'] || headers['x-amz-date']\n      else\n        headers['X-Amz-Date'] = this.getDateTime()\n    }\n\n    delete headers.Authorization\n    delete headers.authorization\n  }\n}\n\nRequestSigner.prototype.sign = function() {\n  if (!this.parsedPath) this.prepareRequest()\n\n  if (this.request.signQuery) {\n    this.parsedPath.query['X-Amz-Signature'] = this.signature()\n  } else {\n    this.request.headers.Authorization = this.authHeader()\n  }\n\n  this.request.path = this.formatPath()\n\n  return this.request\n}\n\nRequestSigner.prototype.getDateTime = function() {\n  if (!this.datetime) {\n    var headers = this.request.headers,\n      date = new Date(headers.Date || headers.date || new Date)\n\n    this.datetime = date.toISOString().replace(/[:\\-]|\\.\\d{3}/g, '')\n\n    // Remove the trailing 'Z' on the timestamp string for CodeCommit git access\n    if (this.isCodeCommitGit) this.datetime = this.datetime.slice(0, -1)\n  }\n  return this.datetime\n}\n\nRequestSigner.prototype.getDate = function() {\n  return this.getDateTime().substr(0, 8)\n}\n\nRequestSigner.prototype.authHeader = function() {\n  return [\n    'AWS4-HMAC-SHA256 Credential=' + this.credentials.accessKeyId + '/' + this.credentialString(),\n    'SignedHeaders=' + this.signedHeaders(),\n    'Signature=' + this.signature(),\n  ].join(', ')\n}\n\nRequestSigner.prototype.signature = function() {\n  var date = this.getDate(),\n      cacheKey = [this.credentials.secretAccessKey, date, this.region, this.service].join(),\n      kDate, kRegion, kService, kCredentials = credentialsCache.get(cacheKey)\n  if (!kCredentials) {\n    kDate = hmac('AWS4' + this.credentials.secretAccessKey, date)\n    kRegion = hmac(kDate, this.region)\n    kService = hmac(kRegion, this.service)\n    kCredentials = hmac(kService, 'aws4_request')\n    credentialsCache.set(cacheKey, kCredentials)\n  }\n  return hmac(kCredentials, this.stringToSign(), 'hex')\n}\n\nRequestSigner.prototype.stringToSign = function() {\n  return [\n    'AWS4-HMAC-SHA256',\n    this.getDateTime(),\n    this.credentialString(),\n    hash(this.canonicalString(), 'hex'),\n  ].join('\\n')\n}\n\nRequestSigner.prototype.canonicalString = function() {\n  if (!this.parsedPath) this.prepareRequest()\n\n  var pathStr = this.parsedPath.path,\n      query = this.parsedPath.query,\n      headers = this.request.headers,\n      queryStr = '',\n      normalizePath = this.service !== 's3',\n      decodePath = this.service === 's3' || this.request.doNotEncodePath,\n      decodeSlashesInPath = this.service === 's3',\n      firstValOnly = this.service === 's3',\n      bodyHash\n\n  if (this.service === 's3' && this.request.signQuery) {\n    bodyHash = 'UNSIGNED-PAYLOAD'\n  } else if (this.isCodeCommitGit) {\n    bodyHash = ''\n  } else {\n    bodyHash = headers['X-Amz-Content-Sha256'] || headers['x-amz-content-sha256'] ||\n      hash(this.request.body || '', 'hex')\n  }\n\n  if (query) {\n    var reducedQuery = Object.keys(query).reduce(function(obj, key) {\n      if (!key) return obj\n      obj[encodeRfc3986Full(key)] = !Array.isArray(query[key]) ? query[key] :\n        (firstValOnly ? query[key][0] : query[key])\n      return obj\n    }, {})\n    var encodedQueryPieces = []\n    Object.keys(reducedQuery).sort().forEach(function(key) {\n      if (!Array.isArray(reducedQuery[key])) {\n        encodedQueryPieces.push(key + '=' + encodeRfc3986Full(reducedQuery[key]))\n      } else {\n        reducedQuery[key].map(encodeRfc3986Full).sort()\n          .forEach(function(val) { encodedQueryPieces.push(key + '=' + val) })\n      }\n    })\n    queryStr = encodedQueryPieces.join('&')\n  }\n  if (pathStr !== '/') {\n    if (normalizePath) pathStr = pathStr.replace(/\\/{2,}/g, '/')\n    pathStr = pathStr.split('/').reduce(function(path, piece) {\n      if (normalizePath && piece === '..') {\n        path.pop()\n      } else if (!normalizePath || piece !== '.') {\n        if (decodePath) piece = decodeURIComponent(piece.replace(/\\+/g, ' '))\n        path.push(encodeRfc3986Full(piece))\n      }\n      return path\n    }, []).join('/')\n    if (pathStr[0] !== '/') pathStr = '/' + pathStr\n    if (decodeSlashesInPath) pathStr = pathStr.replace(/%2F/g, '/')\n  }\n\n  return [\n    this.request.method || 'GET',\n    pathStr,\n    queryStr,\n    this.canonicalHeaders() + '\\n',\n    this.signedHeaders(),\n    bodyHash,\n  ].join('\\n')\n}\n\nRequestSigner.prototype.filterHeaders = function() {\n  var headers = this.request.headers,\n      extraHeadersToInclude = this.extraHeadersToInclude,\n      extraHeadersToIgnore = this.extraHeadersToIgnore\n  this.filteredHeaders = Object.keys(headers)\n    .map(function(key) { return [key.toLowerCase(), headers[key]] })\n    .filter(function(entry) {\n      return extraHeadersToInclude[entry[0]] ||\n        (HEADERS_TO_IGNORE[entry[0]] == null && !extraHeadersToIgnore[entry[0]])\n    })\n    .sort(function(a, b) { return a[0] < b[0] ? -1 : 1 })\n}\n\nRequestSigner.prototype.canonicalHeaders = function() {\n  if (!this.filteredHeaders) this.filterHeaders()\n\n  return this.filteredHeaders.map(function(entry) {\n    return entry[0] + ':' + entry[1].toString().trim().replace(/\\s+/g, ' ')\n  }).join('\\n')\n}\n\nRequestSigner.prototype.signedHeaders = function() {\n  if (!this.filteredHeaders) this.filterHeaders()\n\n  return this.filteredHeaders.map(function(entry) { return entry[0] }).join(';')\n}\n\nRequestSigner.prototype.credentialString = function() {\n  return [\n    this.getDate(),\n    this.region,\n    this.service,\n    'aws4_request',\n  ].join('/')\n}\n\nRequestSigner.prototype.defaultCredentials = function() {\n  var env = process.env\n  return {\n    accessKeyId: env.AWS_ACCESS_KEY_ID || env.AWS_ACCESS_KEY,\n    secretAccessKey: env.AWS_SECRET_ACCESS_KEY || env.AWS_SECRET_KEY,\n    sessionToken: env.AWS_SESSION_TOKEN,\n  }\n}\n\nRequestSigner.prototype.parsePath = function() {\n  var path = this.request.path || '/'\n\n  // S3 doesn't always encode characters > 127 correctly and\n  // all services don't encode characters > 255 correctly\n  // So if there are non-reserved chars (and it's not already all % encoded), just encode them all\n  if (/[^0-9A-Za-z;,/?:@&=+$\\-_.!~*'()#%]/.test(path)) {\n    path = encodeURI(decodeURI(path))\n  }\n\n  var queryIx = path.indexOf('?'),\n      query = null\n\n  if (queryIx >= 0) {\n    query = querystring.parse(path.slice(queryIx + 1))\n    path = path.slice(0, queryIx)\n  }\n\n  this.parsedPath = {\n    path: path,\n    query: query,\n  }\n}\n\nRequestSigner.prototype.formatPath = function() {\n  var path = this.parsedPath.path,\n      query = this.parsedPath.query\n\n  if (!query) return path\n\n  // Services don't support empty query string keys\n  if (query[''] != null) delete query['']\n\n  return path + '?' + encodeRfc3986(querystring.stringify(query))\n}\n\naws4.RequestSigner = RequestSigner\n\naws4.sign = function(request, credentials) {\n  return new RequestSigner(request, credentials).sign()\n}\n"], "mappings": "AAAA,IAAIA,IAAI,GAAGC,OAAO;EACdC,GAAG,GAAGC,OAAO,CAAC,KAAK,CAAC;EACpBC,WAAW,GAAGD,OAAO,CAAC,aAAa,CAAC;EACpCE,MAAM,GAAGF,OAAO,CAAC,QAAQ,CAAC;EAC1BG,GAAG,GAAGH,OAAO,CAAC,OAAO,CAAC;EACtBI,gBAAgB,GAAGD,GAAG,CAAC,IAAI,CAAC;;AAEhC;;AAEA,SAASE,IAAIA,CAACC,GAAG,EAAEC,MAAM,EAAEC,QAAQ,EAAE;EACnC,OAAON,MAAM,CAACO,UAAU,CAAC,QAAQ,EAAEH,GAAG,CAAC,CAACI,MAAM,CAACH,MAAM,EAAE,MAAM,CAAC,CAACI,MAAM,CAACH,QAAQ,CAAC;AACjF;AAEA,SAASI,IAAIA,CAACL,MAAM,EAAEC,QAAQ,EAAE;EAC9B,OAAON,MAAM,CAACW,UAAU,CAAC,QAAQ,CAAC,CAACH,MAAM,CAACH,MAAM,EAAE,MAAM,CAAC,CAACI,MAAM,CAACH,QAAQ,CAAC;AAC5E;;AAEA;AACA,SAASM,aAAaA,CAACC,gBAAgB,EAAE;EACvC,OAAOA,gBAAgB,CAACC,OAAO,CAAC,UAAU,EAAE,UAASC,CAAC,EAAE;IACtD,OAAO,GAAG,GAAGA,CAAC,CAACC,UAAU,CAAC,CAAC,CAAC,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;EACzD,CAAC,CAAC;AACJ;AAEA,SAASC,iBAAiBA,CAACC,GAAG,EAAE;EAC9B,OAAOR,aAAa,CAACS,kBAAkB,CAACD,GAAG,CAAC,CAAC;AAC/C;;AAEA;AACA;AACA;AACA;AACA,IAAIE,iBAAiB,GAAG;EACtB,eAAe,EAAE,IAAI;EACrB,YAAY,EAAE,IAAI;EAClB,iBAAiB,EAAE,IAAI;EACvB,YAAY,EAAE,IAAI;EAClB,QAAQ,EAAE,IAAI;EACd,mBAAmB,EAAE,IAAI;EACzB,OAAO,EAAE;AACX,CAAC;;AAED;AACA;AACA,SAASC,aAAaA,CAACC,OAAO,EAAEC,WAAW,EAAE;EAE3C,IAAI,OAAOD,OAAO,KAAK,QAAQ,EAAEA,OAAO,GAAG3B,GAAG,CAAC6B,KAAK,CAACF,OAAO,CAAC;EAE7D,IAAIG,OAAO,GAAGH,OAAO,CAACG,OAAO,GAAGC,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAGL,OAAO,CAACG,OAAO,IAAI,CAAC,CAAE,CAAC;IACtEG,SAAS,GAAG,CAAC,CAAC,IAAI,CAACC,OAAO,IAAI,CAAC,IAAI,CAACC,MAAM,KAAK,IAAI,CAACC,SAAS,CAACT,OAAO,CAACU,QAAQ,IAAIV,OAAO,CAACW,IAAI,IAAIR,OAAO,CAACS,IAAI,IAAIT,OAAO,CAACQ,IAAI,CAAC;EAEnI,IAAI,CAACX,OAAO,GAAGA,OAAO;EACtB,IAAI,CAACC,WAAW,GAAGA,WAAW,IAAI,IAAI,CAACY,kBAAkB,CAAC,CAAC;EAE3D,IAAI,CAACN,OAAO,GAAGP,OAAO,CAACO,OAAO,IAAID,SAAS,CAAC,CAAC,CAAC,IAAI,EAAE;EACpD,IAAI,CAACE,MAAM,GAAGR,OAAO,CAACQ,MAAM,IAAIF,SAAS,CAAC,CAAC,CAAC,IAAI,WAAW;;EAE3D;EACA,IAAI,IAAI,CAACC,OAAO,KAAK,OAAO,EAAE,IAAI,CAACA,OAAO,GAAG,KAAK;EAElD,IAAI,CAACP,OAAO,CAACc,MAAM,IAAId,OAAO,CAACe,IAAI,EACjCf,OAAO,CAACc,MAAM,GAAG,MAAM;EAEzB,IAAI,CAACX,OAAO,CAACS,IAAI,IAAI,CAACT,OAAO,CAACQ,IAAI,EAAE;IAClCR,OAAO,CAACS,IAAI,GAAGZ,OAAO,CAACU,QAAQ,IAAIV,OAAO,CAACW,IAAI,IAAI,IAAI,CAACK,UAAU,CAAC,CAAC;;IAEpE;IACA,IAAIhB,OAAO,CAACiB,IAAI,EACdd,OAAO,CAACS,IAAI,IAAI,GAAG,GAAGZ,OAAO,CAACiB,IAAI;EACtC;EACA,IAAI,CAACjB,OAAO,CAACU,QAAQ,IAAI,CAACV,OAAO,CAACW,IAAI,EACpCX,OAAO,CAACU,QAAQ,GAAGP,OAAO,CAACS,IAAI,IAAIT,OAAO,CAACQ,IAAI;EAEjD,IAAI,CAACO,eAAe,GAAG,IAAI,CAACX,OAAO,KAAK,YAAY,IAAIP,OAAO,CAACc,MAAM,KAAK,KAAK;EAEhF,IAAI,CAACK,oBAAoB,GAAGnB,OAAO,CAACmB,oBAAoB,IAAIf,MAAM,CAACgB,MAAM,CAAC,IAAI,CAAC;EAC/E,IAAI,CAACC,qBAAqB,GAAGrB,OAAO,CAACqB,qBAAqB,IAAIjB,MAAM,CAACgB,MAAM,CAAC,IAAI,CAAC;AACnF;AAEArB,aAAa,CAACuB,SAAS,CAACb,SAAS,GAAG,UAASE,IAAI,EAAE;EACjD,IAAIY,KAAK,GAAG,CAACZ,IAAI,IAAI,EAAE,EAAEY,KAAK,CAAC,2DAA2D,CAAC;EAC3F,IAAIjB,SAAS,GAAG,CAACiB,KAAK,IAAI,EAAE,EAAEC,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC;;EAEzC;EACA;EACA;EACA,IAAIlB,SAAS,CAAC,CAAC,CAAC,KAAK,IAAI,IAAIA,SAAS,CAAC,CAAC,CAAC,KAAK,MAAM,EAClDA,SAAS,GAAGA,SAAS,CAACmB,OAAO,CAAC,CAAC;EAEjC,IAAInB,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE;IACxBA,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;IACnBA,SAAS,CAAC,CAAC,CAAC,GAAG,WAAW;EAC5B,CAAC,MAAM;IACL,KAAK,IAAIoB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;MAC1B,IAAI,MAAM,CAACC,IAAI,CAACrB,SAAS,CAACoB,CAAC,CAAC,CAAC,EAAE;QAC7BpB,SAAS,CAAC,CAAC,CAAC,GAAGA,SAAS,CAACoB,CAAC,CAAC,CAACF,KAAK,CAAC,CAAC,CAAC;QACpClB,SAAS,CAAC,CAAC,CAAC,GAAG,IAAI;QACnB;MACF;IACF;EACF;EAEA,OAAOA,SAAS;AAClB,CAAC;;AAED;AACAP,aAAa,CAACuB,SAAS,CAACM,cAAc,GAAG,YAAW;EAClD;EACA,IAAI,CAAC,IAAI,EAAE,KAAK,CAAC,CAACC,OAAO,CAAC,IAAI,CAACtB,OAAO,CAAC,IAAI,CAAC,IAAI,IAAI,CAACC,MAAM,KAAK,WAAW,EAAE,OAAO,IAAI;EAExF,OAAO,CAAC,YAAY,EAAE,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,CAAC,CACjEqB,OAAO,CAAC,IAAI,CAACtB,OAAO,CAAC,IAAI,CAAC;AAC/B,CAAC;AAEDR,aAAa,CAACuB,SAAS,CAACN,UAAU,GAAG,YAAW;EAC9C,IAAIR,MAAM,GAAG,IAAI,CAACoB,cAAc,CAAC,CAAC,GAAG,EAAE,GAAG,GAAG,GAAG,IAAI,CAACpB,MAAM;IACvDsB,SAAS,GAAG,IAAI,CAACvB,OAAO,KAAK,KAAK,GAAG,OAAO,GAAG,IAAI,CAACA,OAAO;EAC/D,OAAOuB,SAAS,GAAGtB,MAAM,GAAG,gBAAgB;AAC9C,CAAC;AAEDT,aAAa,CAACuB,SAAS,CAACS,cAAc,GAAG,YAAW;EAClD,IAAI,CAACC,SAAS,CAAC,CAAC;EAEhB,IAAIhC,OAAO,GAAG,IAAI,CAACA,OAAO;IAAEG,OAAO,GAAGH,OAAO,CAACG,OAAO;IAAE8B,KAAK;EAE5D,IAAIjC,OAAO,CAACkC,SAAS,EAAE;IAErB,IAAI,CAACC,UAAU,CAACF,KAAK,GAAGA,KAAK,GAAG,IAAI,CAACE,UAAU,CAACF,KAAK,IAAI,CAAC,CAAC;IAE3D,IAAI,IAAI,CAAChC,WAAW,CAACmC,YAAY,EAC/BH,KAAK,CAAC,sBAAsB,CAAC,GAAG,IAAI,CAAChC,WAAW,CAACmC,YAAY;IAE/D,IAAI,IAAI,CAAC7B,OAAO,KAAK,IAAI,IAAI,CAAC0B,KAAK,CAAC,eAAe,CAAC,EAClDA,KAAK,CAAC,eAAe,CAAC,GAAG,KAAK;IAEhC,IAAIA,KAAK,CAAC,YAAY,CAAC,EACrB,IAAI,CAACI,QAAQ,GAAGJ,KAAK,CAAC,YAAY,CAAC,MAEnCA,KAAK,CAAC,YAAY,CAAC,GAAG,IAAI,CAACK,WAAW,CAAC,CAAC;IAE1CL,KAAK,CAAC,iBAAiB,CAAC,GAAG,kBAAkB;IAC7CA,KAAK,CAAC,kBAAkB,CAAC,GAAG,IAAI,CAAChC,WAAW,CAACsC,WAAW,GAAG,GAAG,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC;IACxFP,KAAK,CAAC,qBAAqB,CAAC,GAAG,IAAI,CAACQ,aAAa,CAAC,CAAC;EAErD,CAAC,MAAM;IAEL,IAAI,CAACzC,OAAO,CAAC0C,kBAAkB,IAAI,CAAC,IAAI,CAACxB,eAAe,EAAE;MACxD,IAAIlB,OAAO,CAACe,IAAI,IAAI,CAACZ,OAAO,CAAC,cAAc,CAAC,IAAI,CAACA,OAAO,CAAC,cAAc,CAAC,EACtEA,OAAO,CAAC,cAAc,CAAC,GAAG,kDAAkD;MAE9E,IAAIH,OAAO,CAACe,IAAI,IAAI,CAACZ,OAAO,CAAC,gBAAgB,CAAC,IAAI,CAACA,OAAO,CAAC,gBAAgB,CAAC,EAC1EA,OAAO,CAAC,gBAAgB,CAAC,GAAGwC,MAAM,CAACC,UAAU,CAAC5C,OAAO,CAACe,IAAI,CAAC;MAE7D,IAAI,IAAI,CAACd,WAAW,CAACmC,YAAY,IAAI,CAACjC,OAAO,CAAC,sBAAsB,CAAC,IAAI,CAACA,OAAO,CAAC,sBAAsB,CAAC,EACvGA,OAAO,CAAC,sBAAsB,CAAC,GAAG,IAAI,CAACF,WAAW,CAACmC,YAAY;MAEjE,IAAI,IAAI,CAAC7B,OAAO,KAAK,IAAI,IAAI,CAACJ,OAAO,CAAC,sBAAsB,CAAC,IAAI,CAACA,OAAO,CAAC,sBAAsB,CAAC,EAC/FA,OAAO,CAAC,sBAAsB,CAAC,GAAGjB,IAAI,CAAC,IAAI,CAACc,OAAO,CAACe,IAAI,IAAI,EAAE,EAAE,KAAK,CAAC;MAExE,IAAIZ,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,YAAY,CAAC,EAChD,IAAI,CAACkC,QAAQ,GAAGlC,OAAO,CAAC,YAAY,CAAC,IAAIA,OAAO,CAAC,YAAY,CAAC,MAE9DA,OAAO,CAAC,YAAY,CAAC,GAAG,IAAI,CAACmC,WAAW,CAAC,CAAC;IAC9C;IAEA,OAAOnC,OAAO,CAAC0C,aAAa;IAC5B,OAAO1C,OAAO,CAAC2C,aAAa;EAC9B;AACF,CAAC;AAED/C,aAAa,CAACuB,SAAS,CAACyB,IAAI,GAAG,YAAW;EACxC,IAAI,CAAC,IAAI,CAACZ,UAAU,EAAE,IAAI,CAACJ,cAAc,CAAC,CAAC;EAE3C,IAAI,IAAI,CAAC/B,OAAO,CAACkC,SAAS,EAAE;IAC1B,IAAI,CAACC,UAAU,CAACF,KAAK,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAACe,SAAS,CAAC,CAAC;EAC7D,CAAC,MAAM;IACL,IAAI,CAAChD,OAAO,CAACG,OAAO,CAAC0C,aAAa,GAAG,IAAI,CAACI,UAAU,CAAC,CAAC;EACxD;EAEA,IAAI,CAACjD,OAAO,CAACkD,IAAI,GAAG,IAAI,CAACC,UAAU,CAAC,CAAC;EAErC,OAAO,IAAI,CAACnD,OAAO;AACrB,CAAC;AAEDD,aAAa,CAACuB,SAAS,CAACgB,WAAW,GAAG,YAAW;EAC/C,IAAI,CAAC,IAAI,CAACD,QAAQ,EAAE;IAClB,IAAIlC,OAAO,GAAG,IAAI,CAACH,OAAO,CAACG,OAAO;MAChCiD,IAAI,GAAG,IAAIC,IAAI,CAAClD,OAAO,CAACkD,IAAI,IAAIlD,OAAO,CAACiD,IAAI,IAAI,IAAIC,IAAI,CAAD,CAAC,CAAC;IAE3D,IAAI,CAAChB,QAAQ,GAAGe,IAAI,CAACE,WAAW,CAAC,CAAC,CAAChE,OAAO,CAAC,gBAAgB,EAAE,EAAE,CAAC;;IAEhE;IACA,IAAI,IAAI,CAAC4B,eAAe,EAAE,IAAI,CAACmB,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACb,KAAK,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;EACtE;EACA,OAAO,IAAI,CAACa,QAAQ;AACtB,CAAC;AAEDtC,aAAa,CAACuB,SAAS,CAACiC,OAAO,GAAG,YAAW;EAC3C,OAAO,IAAI,CAACjB,WAAW,CAAC,CAAC,CAACkB,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;AACxC,CAAC;AAEDzD,aAAa,CAACuB,SAAS,CAAC2B,UAAU,GAAG,YAAW;EAC9C,OAAO,CACL,8BAA8B,GAAG,IAAI,CAAChD,WAAW,CAACsC,WAAW,GAAG,GAAG,GAAG,IAAI,CAACC,gBAAgB,CAAC,CAAC,EAC7F,gBAAgB,GAAG,IAAI,CAACC,aAAa,CAAC,CAAC,EACvC,YAAY,GAAG,IAAI,CAACO,SAAS,CAAC,CAAC,CAChC,CAACS,IAAI,CAAC,IAAI,CAAC;AACd,CAAC;AAED1D,aAAa,CAACuB,SAAS,CAAC0B,SAAS,GAAG,YAAW;EAC7C,IAAII,IAAI,GAAG,IAAI,CAACG,OAAO,CAAC,CAAC;IACrBG,QAAQ,GAAG,CAAC,IAAI,CAACzD,WAAW,CAAC0D,eAAe,EAAEP,IAAI,EAAE,IAAI,CAAC5C,MAAM,EAAE,IAAI,CAACD,OAAO,CAAC,CAACkD,IAAI,CAAC,CAAC;IACrFG,KAAK;IAAEC,OAAO;IAAEC,QAAQ;IAAEC,YAAY,GAAGrF,gBAAgB,CAACsF,GAAG,CAACN,QAAQ,CAAC;EAC3E,IAAI,CAACK,YAAY,EAAE;IACjBH,KAAK,GAAGjF,IAAI,CAAC,MAAM,GAAG,IAAI,CAACsB,WAAW,CAAC0D,eAAe,EAAEP,IAAI,CAAC;IAC7DS,OAAO,GAAGlF,IAAI,CAACiF,KAAK,EAAE,IAAI,CAACpD,MAAM,CAAC;IAClCsD,QAAQ,GAAGnF,IAAI,CAACkF,OAAO,EAAE,IAAI,CAACtD,OAAO,CAAC;IACtCwD,YAAY,GAAGpF,IAAI,CAACmF,QAAQ,EAAE,cAAc,CAAC;IAC7CpF,gBAAgB,CAACuF,GAAG,CAACP,QAAQ,EAAEK,YAAY,CAAC;EAC9C;EACA,OAAOpF,IAAI,CAACoF,YAAY,EAAE,IAAI,CAACG,YAAY,CAAC,CAAC,EAAE,KAAK,CAAC;AACvD,CAAC;AAEDnE,aAAa,CAACuB,SAAS,CAAC4C,YAAY,GAAG,YAAW;EAChD,OAAO,CACL,kBAAkB,EAClB,IAAI,CAAC5B,WAAW,CAAC,CAAC,EAClB,IAAI,CAACE,gBAAgB,CAAC,CAAC,EACvBtD,IAAI,CAAC,IAAI,CAACiF,eAAe,CAAC,CAAC,EAAE,KAAK,CAAC,CACpC,CAACV,IAAI,CAAC,IAAI,CAAC;AACd,CAAC;AAED1D,aAAa,CAACuB,SAAS,CAAC6C,eAAe,GAAG,YAAW;EACnD,IAAI,CAAC,IAAI,CAAChC,UAAU,EAAE,IAAI,CAACJ,cAAc,CAAC,CAAC;EAE3C,IAAIqC,OAAO,GAAG,IAAI,CAACjC,UAAU,CAACe,IAAI;IAC9BjB,KAAK,GAAG,IAAI,CAACE,UAAU,CAACF,KAAK;IAC7B9B,OAAO,GAAG,IAAI,CAACH,OAAO,CAACG,OAAO;IAC9BkE,QAAQ,GAAG,EAAE;IACbC,aAAa,GAAG,IAAI,CAAC/D,OAAO,KAAK,IAAI;IACrCgE,UAAU,GAAG,IAAI,CAAChE,OAAO,KAAK,IAAI,IAAI,IAAI,CAACP,OAAO,CAACwE,eAAe;IAClEC,mBAAmB,GAAG,IAAI,CAAClE,OAAO,KAAK,IAAI;IAC3CmE,YAAY,GAAG,IAAI,CAACnE,OAAO,KAAK,IAAI;IACpCoE,QAAQ;EAEZ,IAAI,IAAI,CAACpE,OAAO,KAAK,IAAI,IAAI,IAAI,CAACP,OAAO,CAACkC,SAAS,EAAE;IACnDyC,QAAQ,GAAG,kBAAkB;EAC/B,CAAC,MAAM,IAAI,IAAI,CAACzD,eAAe,EAAE;IAC/ByD,QAAQ,GAAG,EAAE;EACf,CAAC,MAAM;IACLA,QAAQ,GAAGxE,OAAO,CAAC,sBAAsB,CAAC,IAAIA,OAAO,CAAC,sBAAsB,CAAC,IAC3EjB,IAAI,CAAC,IAAI,CAACc,OAAO,CAACe,IAAI,IAAI,EAAE,EAAE,KAAK,CAAC;EACxC;EAEA,IAAIkB,KAAK,EAAE;IACT,IAAI2C,YAAY,GAAGxE,MAAM,CAACyE,IAAI,CAAC5C,KAAK,CAAC,CAAC6C,MAAM,CAAC,UAASC,GAAG,EAAEnG,GAAG,EAAE;MAC9D,IAAI,CAACA,GAAG,EAAE,OAAOmG,GAAG;MACpBA,GAAG,CAACpF,iBAAiB,CAACf,GAAG,CAAC,CAAC,GAAG,CAACoG,KAAK,CAACC,OAAO,CAAChD,KAAK,CAACrD,GAAG,CAAC,CAAC,GAAGqD,KAAK,CAACrD,GAAG,CAAC,GAClE8F,YAAY,GAAGzC,KAAK,CAACrD,GAAG,CAAC,CAAC,CAAC,CAAC,GAAGqD,KAAK,CAACrD,GAAG,CAAE;MAC7C,OAAOmG,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;IACN,IAAIG,kBAAkB,GAAG,EAAE;IAC3B9E,MAAM,CAACyE,IAAI,CAACD,YAAY,CAAC,CAACO,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,UAASxG,GAAG,EAAE;MACrD,IAAI,CAACoG,KAAK,CAACC,OAAO,CAACL,YAAY,CAAChG,GAAG,CAAC,CAAC,EAAE;QACrCsG,kBAAkB,CAACG,IAAI,CAACzG,GAAG,GAAG,GAAG,GAAGe,iBAAiB,CAACiF,YAAY,CAAChG,GAAG,CAAC,CAAC,CAAC;MAC3E,CAAC,MAAM;QACLgG,YAAY,CAAChG,GAAG,CAAC,CAAC0G,GAAG,CAAC3F,iBAAiB,CAAC,CAACwF,IAAI,CAAC,CAAC,CAC5CC,OAAO,CAAC,UAASG,GAAG,EAAE;UAAEL,kBAAkB,CAACG,IAAI,CAACzG,GAAG,GAAG,GAAG,GAAG2G,GAAG,CAAC;QAAC,CAAC,CAAC;MACxE;IACF,CAAC,CAAC;IACFlB,QAAQ,GAAGa,kBAAkB,CAACzB,IAAI,CAAC,GAAG,CAAC;EACzC;EACA,IAAIW,OAAO,KAAK,GAAG,EAAE;IACnB,IAAIE,aAAa,EAAEF,OAAO,GAAGA,OAAO,CAAC9E,OAAO,CAAC,SAAS,EAAE,GAAG,CAAC;IAC5D8E,OAAO,GAAGA,OAAO,CAACoB,KAAK,CAAC,GAAG,CAAC,CAACV,MAAM,CAAC,UAAS5B,IAAI,EAAEuC,KAAK,EAAE;MACxD,IAAInB,aAAa,IAAImB,KAAK,KAAK,IAAI,EAAE;QACnCvC,IAAI,CAACwC,GAAG,CAAC,CAAC;MACZ,CAAC,MAAM,IAAI,CAACpB,aAAa,IAAImB,KAAK,KAAK,GAAG,EAAE;QAC1C,IAAIlB,UAAU,EAAEkB,KAAK,GAAGE,kBAAkB,CAACF,KAAK,CAACnG,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;QACrE4D,IAAI,CAACmC,IAAI,CAAC1F,iBAAiB,CAAC8F,KAAK,CAAC,CAAC;MACrC;MACA,OAAOvC,IAAI;IACb,CAAC,EAAE,EAAE,CAAC,CAACO,IAAI,CAAC,GAAG,CAAC;IAChB,IAAIW,OAAO,CAAC,CAAC,CAAC,KAAK,GAAG,EAAEA,OAAO,GAAG,GAAG,GAAGA,OAAO;IAC/C,IAAIK,mBAAmB,EAAEL,OAAO,GAAGA,OAAO,CAAC9E,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EACjE;EAEA,OAAO,CACL,IAAI,CAACU,OAAO,CAACc,MAAM,IAAI,KAAK,EAC5BsD,OAAO,EACPC,QAAQ,EACR,IAAI,CAACuB,gBAAgB,CAAC,CAAC,GAAG,IAAI,EAC9B,IAAI,CAACnD,aAAa,CAAC,CAAC,EACpBkC,QAAQ,CACT,CAAClB,IAAI,CAAC,IAAI,CAAC;AACd,CAAC;AAED1D,aAAa,CAACuB,SAAS,CAACuE,aAAa,GAAG,YAAW;EACjD,IAAI1F,OAAO,GAAG,IAAI,CAACH,OAAO,CAACG,OAAO;IAC9BkB,qBAAqB,GAAG,IAAI,CAACA,qBAAqB;IAClDF,oBAAoB,GAAG,IAAI,CAACA,oBAAoB;EACpD,IAAI,CAAC2E,eAAe,GAAG1F,MAAM,CAACyE,IAAI,CAAC1E,OAAO,CAAC,CACxCmF,GAAG,CAAC,UAAS1G,GAAG,EAAE;IAAE,OAAO,CAACA,GAAG,CAACmH,WAAW,CAAC,CAAC,EAAE5F,OAAO,CAACvB,GAAG,CAAC,CAAC;EAAC,CAAC,CAAC,CAC/DoH,MAAM,CAAC,UAASC,KAAK,EAAE;IACtB,OAAO5E,qBAAqB,CAAC4E,KAAK,CAAC,CAAC,CAAC,CAAC,IACnCnG,iBAAiB,CAACmG,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,IAAI,IAAI,CAAC9E,oBAAoB,CAAC8E,KAAK,CAAC,CAAC,CAAC,CAAE;EAC5E,CAAC,CAAC,CACDd,IAAI,CAAC,UAASe,CAAC,EAAEC,CAAC,EAAE;IAAE,OAAOD,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC;EAAC,CAAC,CAAC;AACzD,CAAC;AAEDpG,aAAa,CAACuB,SAAS,CAACsE,gBAAgB,GAAG,YAAW;EACpD,IAAI,CAAC,IAAI,CAACE,eAAe,EAAE,IAAI,CAACD,aAAa,CAAC,CAAC;EAE/C,OAAO,IAAI,CAACC,eAAe,CAACR,GAAG,CAAC,UAASW,KAAK,EAAE;IAC9C,OAAOA,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACxG,QAAQ,CAAC,CAAC,CAAC2G,IAAI,CAAC,CAAC,CAAC9G,OAAO,CAAC,MAAM,EAAE,GAAG,CAAC;EACzE,CAAC,CAAC,CAACmE,IAAI,CAAC,IAAI,CAAC;AACf,CAAC;AAED1D,aAAa,CAACuB,SAAS,CAACmB,aAAa,GAAG,YAAW;EACjD,IAAI,CAAC,IAAI,CAACqD,eAAe,EAAE,IAAI,CAACD,aAAa,CAAC,CAAC;EAE/C,OAAO,IAAI,CAACC,eAAe,CAACR,GAAG,CAAC,UAASW,KAAK,EAAE;IAAE,OAAOA,KAAK,CAAC,CAAC,CAAC;EAAC,CAAC,CAAC,CAACxC,IAAI,CAAC,GAAG,CAAC;AAChF,CAAC;AAED1D,aAAa,CAACuB,SAAS,CAACkB,gBAAgB,GAAG,YAAW;EACpD,OAAO,CACL,IAAI,CAACe,OAAO,CAAC,CAAC,EACd,IAAI,CAAC/C,MAAM,EACX,IAAI,CAACD,OAAO,EACZ,cAAc,CACf,CAACkD,IAAI,CAAC,GAAG,CAAC;AACb,CAAC;AAED1D,aAAa,CAACuB,SAAS,CAACT,kBAAkB,GAAG,YAAW;EACtD,IAAIwF,GAAG,GAAGC,OAAO,CAACD,GAAG;EACrB,OAAO;IACL9D,WAAW,EAAE8D,GAAG,CAACE,iBAAiB,IAAIF,GAAG,CAACG,cAAc;IACxD7C,eAAe,EAAE0C,GAAG,CAACI,qBAAqB,IAAIJ,GAAG,CAACK,cAAc;IAChEtE,YAAY,EAAEiE,GAAG,CAACM;EACpB,CAAC;AACH,CAAC;AAED5G,aAAa,CAACuB,SAAS,CAACU,SAAS,GAAG,YAAW;EAC7C,IAAIkB,IAAI,GAAG,IAAI,CAAClD,OAAO,CAACkD,IAAI,IAAI,GAAG;;EAEnC;EACA;EACA;EACA,IAAI,oCAAoC,CAACvB,IAAI,CAACuB,IAAI,CAAC,EAAE;IACnDA,IAAI,GAAG0D,SAAS,CAACC,SAAS,CAAC3D,IAAI,CAAC,CAAC;EACnC;EAEA,IAAI4D,OAAO,GAAG5D,IAAI,CAACrB,OAAO,CAAC,GAAG,CAAC;IAC3BI,KAAK,GAAG,IAAI;EAEhB,IAAI6E,OAAO,IAAI,CAAC,EAAE;IAChB7E,KAAK,GAAG1D,WAAW,CAAC2B,KAAK,CAACgD,IAAI,CAAC1B,KAAK,CAACsF,OAAO,GAAG,CAAC,CAAC,CAAC;IAClD5D,IAAI,GAAGA,IAAI,CAAC1B,KAAK,CAAC,CAAC,EAAEsF,OAAO,CAAC;EAC/B;EAEA,IAAI,CAAC3E,UAAU,GAAG;IAChBe,IAAI,EAAEA,IAAI;IACVjB,KAAK,EAAEA;EACT,CAAC;AACH,CAAC;AAEDlC,aAAa,CAACuB,SAAS,CAAC6B,UAAU,GAAG,YAAW;EAC9C,IAAID,IAAI,GAAG,IAAI,CAACf,UAAU,CAACe,IAAI;IAC3BjB,KAAK,GAAG,IAAI,CAACE,UAAU,CAACF,KAAK;EAEjC,IAAI,CAACA,KAAK,EAAE,OAAOiB,IAAI;;EAEvB;EACA,IAAIjB,KAAK,CAAC,EAAE,CAAC,IAAI,IAAI,EAAE,OAAOA,KAAK,CAAC,EAAE,CAAC;EAEvC,OAAOiB,IAAI,GAAG,GAAG,GAAG9D,aAAa,CAACb,WAAW,CAACwI,SAAS,CAAC9E,KAAK,CAAC,CAAC;AACjE,CAAC;AAED9D,IAAI,CAAC4B,aAAa,GAAGA,aAAa;AAElC5B,IAAI,CAAC4E,IAAI,GAAG,UAAS/C,OAAO,EAAEC,WAAW,EAAE;EACzC,OAAO,IAAIF,aAAa,CAACC,OAAO,EAAEC,WAAW,CAAC,CAAC8C,IAAI,CAAC,CAAC;AACvD,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}