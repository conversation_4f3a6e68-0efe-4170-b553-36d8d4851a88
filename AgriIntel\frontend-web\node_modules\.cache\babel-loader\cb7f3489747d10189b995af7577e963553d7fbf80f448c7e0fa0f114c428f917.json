{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DropSearchIndexOperation = void 0;\nconst error_1 = require(\"../../error\");\nconst operation_1 = require(\"../operation\");\n/** @internal */\nclass DropSearchIndexOperation extends operation_1.AbstractOperation {\n  constructor(collection, name) {\n    super();\n    this.collection = collection;\n    this.name = name;\n  }\n  get commandName() {\n    return 'dropSearchIndex';\n  }\n  async execute(server, session, timeoutContext) {\n    const namespace = this.collection.fullNamespace;\n    const command = {\n      dropSearchIndex: namespace.collection\n    };\n    if (typeof this.name === 'string') {\n      command.name = this.name;\n    }\n    try {\n      await server.command(namespace, command, {\n        session,\n        timeoutContext\n      });\n    } catch (error) {\n      const isNamespaceNotFoundError = error instanceof error_1.MongoServerError && error.code === error_1.MONGODB_ERROR_CODES.NamespaceNotFound;\n      if (!isNamespaceNotFoundError) {\n        throw error;\n      }\n    }\n  }\n}\nexports.DropSearchIndexOperation = DropSearchIndexOperation;", "map": {"version": 3, "names": ["error_1", "require", "operation_1", "DropSearchIndexOperation", "AbstractOperation", "constructor", "collection", "name", "commandName", "execute", "server", "session", "timeoutContext", "namespace", "fullNamespace", "command", "dropSearchIndex", "error", "isNamespaceNotFoundError", "MongoServerError", "code", "MONGODB_ERROR_CODES", "NamespaceNotFound", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\search_indexes\\drop.ts"], "sourcesContent": ["import type { Document } from '../../bson';\nimport type { Collection } from '../../collection';\nimport { MONGODB_ERROR_CODES, MongoServerError } from '../../error';\nimport type { Server } from '../../sdam/server';\nimport type { ClientSession } from '../../sessions';\nimport { type TimeoutContext } from '../../timeout';\nimport { AbstractOperation } from '../operation';\n\n/** @internal */\nexport class DropSearchIndexOperation extends AbstractOperation<void> {\n  constructor(\n    private readonly collection: Collection,\n    private readonly name: string\n  ) {\n    super();\n  }\n\n  override get commandName() {\n    return 'dropSearchIndex' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<void> {\n    const namespace = this.collection.fullNamespace;\n\n    const command: Document = {\n      dropSearchIndex: namespace.collection\n    };\n\n    if (typeof this.name === 'string') {\n      command.name = this.name;\n    }\n\n    try {\n      await server.command(namespace, command, { session, timeoutContext });\n    } catch (error) {\n      const isNamespaceNotFoundError =\n        error instanceof MongoServerError && error.code === MONGODB_ERROR_CODES.NamespaceNotFound;\n      if (!isNamespaceNotFoundError) {\n        throw error;\n      }\n    }\n  }\n}\n"], "mappings": ";;;;;;AAEA,MAAAA,OAAA,GAAAC,OAAA;AAIA,MAAAC,WAAA,GAAAD,OAAA;AAEA;AACA,MAAaE,wBAAyB,SAAQD,WAAA,CAAAE,iBAAuB;EACnEC,YACmBC,UAAsB,EACtBC,IAAY;IAE7B,KAAK,EAAE;IAHU,KAAAD,UAAU,GAAVA,UAAU;IACV,KAAAC,IAAI,GAAJA,IAAI;EAGvB;EAEA,IAAaC,WAAWA,CAAA;IACtB,OAAO,iBAA0B;EACnC;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMC,SAAS,GAAG,IAAI,CAACP,UAAU,CAACQ,aAAa;IAE/C,MAAMC,OAAO,GAAa;MACxBC,eAAe,EAAEH,SAAS,CAACP;KAC5B;IAED,IAAI,OAAO,IAAI,CAACC,IAAI,KAAK,QAAQ,EAAE;MACjCQ,OAAO,CAACR,IAAI,GAAG,IAAI,CAACA,IAAI;IAC1B;IAEA,IAAI;MACF,MAAMG,MAAM,CAACK,OAAO,CAACF,SAAS,EAAEE,OAAO,EAAE;QAAEJ,OAAO;QAAEC;MAAc,CAAE,CAAC;IACvE,CAAC,CAAC,OAAOK,KAAK,EAAE;MACd,MAAMC,wBAAwB,GAC5BD,KAAK,YAAYjB,OAAA,CAAAmB,gBAAgB,IAAIF,KAAK,CAACG,IAAI,KAAKpB,OAAA,CAAAqB,mBAAmB,CAACC,iBAAiB;MAC3F,IAAI,CAACJ,wBAAwB,EAAE;QAC7B,MAAMD,KAAK;MACb;IACF;EACF;;AApCFM,OAAA,CAAApB,wBAAA,GAAAA,wBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}