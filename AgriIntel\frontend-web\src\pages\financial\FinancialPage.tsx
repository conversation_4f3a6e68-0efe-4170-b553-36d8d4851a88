import React from 'react';
import { useTranslation } from 'react-i18next';

const FinancialPage: React.FC = () => {
  const { t } = useTranslation();

  return (
    <div className="space-y-6">
      <div className="card">
        <div className="card-header">
          <h1 className="text-2xl font-bold text-gray-900">
            {t('financial.title')}
          </h1>
        </div>
        <div className="card-body">
          <p className="text-gray-600">
            Financial management features coming soon...
          </p>
        </div>
      </div>
    </div>
  );
};

export default FinancialPage;
