{"ast": null, "code": "import { onDocumentReady as d } from './document-ready.js';\nlet t = [];\nd(() => {\n  function e(n) {\n    n.target instanceof HTMLElement && n.target !== document.body && t[0] !== n.target && (t.unshift(n.target), t = t.filter(r => r != null && r.isConnected), t.splice(10));\n  }\n  window.addEventListener(\"click\", e, {\n    capture: !0\n  }), window.addEventListener(\"mousedown\", e, {\n    capture: !0\n  }), window.addEventListener(\"focus\", e, {\n    capture: !0\n  }), document.body.addEventListener(\"click\", e, {\n    capture: !0\n  }), document.body.addEventListener(\"mousedown\", e, {\n    capture: !0\n  }), document.body.addEventListener(\"focus\", e, {\n    capture: !0\n  });\n});\nexport { t as history };", "map": {"version": 3, "names": ["onDocumentReady", "d", "t", "e", "n", "target", "HTMLElement", "document", "body", "unshift", "filter", "r", "isConnected", "splice", "window", "addEventListener", "capture", "history"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/utils/active-element-history.js"], "sourcesContent": ["import{onDocumentReady as d}from'./document-ready.js';let t=[];d(()=>{function e(n){n.target instanceof HTMLElement&&n.target!==document.body&&t[0]!==n.target&&(t.unshift(n.target),t=t.filter(r=>r!=null&&r.isConnected),t.splice(10))}window.addEventListener(\"click\",e,{capture:!0}),window.addEventListener(\"mousedown\",e,{capture:!0}),window.addEventListener(\"focus\",e,{capture:!0}),document.body.addEventListener(\"click\",e,{capture:!0}),document.body.addEventListener(\"mousedown\",e,{capture:!0}),document.body.addEventListener(\"focus\",e,{capture:!0})});export{t as history};\n"], "mappings": "AAAA,SAAOA,eAAe,IAAIC,CAAC,QAAK,qBAAqB;AAAC,IAAIC,CAAC,GAAC,EAAE;AAACD,CAAC,CAAC,MAAI;EAAC,SAASE,CAACA,CAACC,CAAC,EAAC;IAACA,CAAC,CAACC,MAAM,YAAYC,WAAW,IAAEF,CAAC,CAACC,MAAM,KAAGE,QAAQ,CAACC,IAAI,IAAEN,CAAC,CAAC,CAAC,CAAC,KAAGE,CAAC,CAACC,MAAM,KAAGH,CAAC,CAACO,OAAO,CAACL,CAAC,CAACC,MAAM,CAAC,EAACH,CAAC,GAACA,CAAC,CAACQ,MAAM,CAACC,CAAC,IAAEA,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACC,WAAW,CAAC,EAACV,CAAC,CAACW,MAAM,CAAC,EAAE,CAAC,CAAC;EAAA;EAACC,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAACZ,CAAC,EAAC;IAACa,OAAO,EAAC,CAAC;EAAC,CAAC,CAAC,EAACF,MAAM,CAACC,gBAAgB,CAAC,WAAW,EAACZ,CAAC,EAAC;IAACa,OAAO,EAAC,CAAC;EAAC,CAAC,CAAC,EAACF,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAACZ,CAAC,EAAC;IAACa,OAAO,EAAC,CAAC;EAAC,CAAC,CAAC,EAACT,QAAQ,CAACC,IAAI,CAACO,gBAAgB,CAAC,OAAO,EAACZ,CAAC,EAAC;IAACa,OAAO,EAAC,CAAC;EAAC,CAAC,CAAC,EAACT,QAAQ,CAACC,IAAI,CAACO,gBAAgB,CAAC,WAAW,EAACZ,CAAC,EAAC;IAACa,OAAO,EAAC,CAAC;EAAC,CAAC,CAAC,EAACT,QAAQ,CAACC,IAAI,CAACO,gBAAgB,CAAC,OAAO,EAACZ,CAAC,EAAC;IAACa,OAAO,EAAC,CAAC;EAAC,CAAC,CAAC;AAAA,CAAC,CAAC;AAAC,SAAOd,CAAC,IAAIe,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}