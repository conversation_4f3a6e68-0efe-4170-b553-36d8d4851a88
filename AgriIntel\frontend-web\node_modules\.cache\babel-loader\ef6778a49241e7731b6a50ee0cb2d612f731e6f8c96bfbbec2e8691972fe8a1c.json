{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ServerDescription = void 0;\nexports.parseServerType = parseServerType;\nexports.compareTopologyVersion = compareTopologyVersion;\nconst bson_1 = require(\"../bson\");\nconst error_1 = require(\"../error\");\nconst utils_1 = require(\"../utils\");\nconst common_1 = require(\"./common\");\nconst WRITABLE_SERVER_TYPES = new Set([common_1.ServerType.RSPrimary, common_1.ServerType.Standalone, common_1.ServerType.Mongos, common_1.ServerType.LoadBalancer]);\nconst DATA_BEARING_SERVER_TYPES = new Set([common_1.ServerType.RSPrimary, common_1.ServerType.RSSecondary, common_1.ServerType.Mongos, common_1.ServerType.Standalone, common_1.ServerType.LoadBalancer]);\n/**\n * The client's view of a single server, based on the most recent hello outcome.\n *\n * Internal type, not meant to be directly instantiated\n * @public\n */\nclass ServerDescription {\n  /**\n   * Create a ServerDescription\n   * @internal\n   *\n   * @param address - The address of the server\n   * @param hello - An optional hello response for this server\n   */\n  constructor(address, hello, options = {}) {\n    if (address == null || address === '') {\n      throw new error_1.MongoRuntimeError('ServerDescription must be provided with a non-empty address');\n    }\n    this.address = typeof address === 'string' ? utils_1.HostAddress.fromString(address).toString() // Use HostAddress to normalize\n    : address.toString();\n    this.type = parseServerType(hello, options);\n    this.hosts = hello?.hosts?.map(host => host.toLowerCase()) ?? [];\n    this.passives = hello?.passives?.map(host => host.toLowerCase()) ?? [];\n    this.arbiters = hello?.arbiters?.map(host => host.toLowerCase()) ?? [];\n    this.tags = hello?.tags ?? {};\n    this.minWireVersion = hello?.minWireVersion ?? 0;\n    this.maxWireVersion = hello?.maxWireVersion ?? 0;\n    this.roundTripTime = options?.roundTripTime ?? -1;\n    this.minRoundTripTime = options?.minRoundTripTime ?? 0;\n    this.lastUpdateTime = (0, utils_1.now)();\n    this.lastWriteDate = hello?.lastWrite?.lastWriteDate ?? 0;\n    // NOTE: This actually builds the stack string instead of holding onto the getter and all its\n    // associated references. This is done to prevent a memory leak.\n    this.error = options.error ?? null;\n    this.error?.stack;\n    // TODO(NODE-2674): Preserve int64 sent from MongoDB\n    this.topologyVersion = this.error?.topologyVersion ?? hello?.topologyVersion ?? null;\n    this.setName = hello?.setName ?? null;\n    this.setVersion = hello?.setVersion ?? null;\n    this.electionId = hello?.electionId ?? null;\n    this.logicalSessionTimeoutMinutes = hello?.logicalSessionTimeoutMinutes ?? null;\n    this.maxMessageSizeBytes = hello?.maxMessageSizeBytes ?? null;\n    this.maxWriteBatchSize = hello?.maxWriteBatchSize ?? null;\n    this.maxBsonObjectSize = hello?.maxBsonObjectSize ?? null;\n    this.primary = hello?.primary ?? null;\n    this.me = hello?.me?.toLowerCase() ?? null;\n    this.$clusterTime = hello?.$clusterTime ?? null;\n    this.iscryptd = Boolean(hello?.iscryptd);\n  }\n  get hostAddress() {\n    return utils_1.HostAddress.fromString(this.address);\n  }\n  get allHosts() {\n    return this.hosts.concat(this.arbiters).concat(this.passives);\n  }\n  /** Is this server available for reads*/\n  get isReadable() {\n    return this.type === common_1.ServerType.RSSecondary || this.isWritable;\n  }\n  /** Is this server data bearing */\n  get isDataBearing() {\n    return DATA_BEARING_SERVER_TYPES.has(this.type);\n  }\n  /** Is this server available for writes */\n  get isWritable() {\n    return WRITABLE_SERVER_TYPES.has(this.type);\n  }\n  get host() {\n    const chopLength = `:${this.port}`.length;\n    return this.address.slice(0, -chopLength);\n  }\n  get port() {\n    const port = this.address.split(':').pop();\n    return port ? Number.parseInt(port, 10) : 27017;\n  }\n  /**\n   * Determines if another `ServerDescription` is equal to this one per the rules defined in the SDAM specification.\n   * @see https://github.com/mongodb/specifications/blob/master/source/server-discovery-and-monitoring/server-discovery-and-monitoring.md\n   */\n  equals(other) {\n    // Despite using the comparator that would determine a nullish topologyVersion as greater than\n    // for equality we should only always perform direct equality comparison\n    const topologyVersionsEqual = this.topologyVersion === other?.topologyVersion || compareTopologyVersion(this.topologyVersion, other?.topologyVersion) === 0;\n    const electionIdsEqual = this.electionId != null && other?.electionId != null ? (0, utils_1.compareObjectId)(this.electionId, other.electionId) === 0 : this.electionId === other?.electionId;\n    return other != null && other.iscryptd === this.iscryptd && (0, utils_1.errorStrictEqual)(this.error, other.error) && this.type === other.type && this.minWireVersion === other.minWireVersion && (0, utils_1.arrayStrictEqual)(this.hosts, other.hosts) && tagsStrictEqual(this.tags, other.tags) && this.setName === other.setName && this.setVersion === other.setVersion && electionIdsEqual && this.primary === other.primary && this.logicalSessionTimeoutMinutes === other.logicalSessionTimeoutMinutes && topologyVersionsEqual;\n  }\n}\nexports.ServerDescription = ServerDescription;\n// Parses a `hello` message and determines the server type\nfunction parseServerType(hello, options) {\n  if (options?.loadBalanced) {\n    return common_1.ServerType.LoadBalancer;\n  }\n  if (!hello || !hello.ok) {\n    return common_1.ServerType.Unknown;\n  }\n  if (hello.isreplicaset) {\n    return common_1.ServerType.RSGhost;\n  }\n  if (hello.msg && hello.msg === 'isdbgrid') {\n    return common_1.ServerType.Mongos;\n  }\n  if (hello.setName) {\n    if (hello.hidden) {\n      return common_1.ServerType.RSOther;\n    } else if (hello.isWritablePrimary) {\n      return common_1.ServerType.RSPrimary;\n    } else if (hello.secondary) {\n      return common_1.ServerType.RSSecondary;\n    } else if (hello.arbiterOnly) {\n      return common_1.ServerType.RSArbiter;\n    } else {\n      return common_1.ServerType.RSOther;\n    }\n  }\n  return common_1.ServerType.Standalone;\n}\nfunction tagsStrictEqual(tags, tags2) {\n  const tagsKeys = Object.keys(tags);\n  const tags2Keys = Object.keys(tags2);\n  return tagsKeys.length === tags2Keys.length && tagsKeys.every(key => tags2[key] === tags[key]);\n}\n/**\n * Compares two topology versions.\n *\n * 1. If the response topologyVersion is unset or the ServerDescription's\n *    topologyVersion is null, the client MUST assume the response is more recent.\n * 1. If the response's topologyVersion.processId is not equal to the\n *    ServerDescription's, the client MUST assume the response is more recent.\n * 1. If the response's topologyVersion.processId is equal to the\n *    ServerDescription's, the client MUST use the counter field to determine\n *    which topologyVersion is more recent.\n *\n * ```ts\n * currentTv <   newTv === -1\n * currentTv === newTv === 0\n * currentTv >   newTv === 1\n * ```\n */\nfunction compareTopologyVersion(currentTv, newTv) {\n  if (currentTv == null || newTv == null) {\n    return -1;\n  }\n  if (!currentTv.processId.equals(newTv.processId)) {\n    return -1;\n  }\n  // TODO(NODE-2674): Preserve int64 sent from MongoDB\n  const currentCounter = typeof currentTv.counter === 'bigint' ? bson_1.Long.fromBigInt(currentTv.counter) : bson_1.Long.isLong(currentTv.counter) ? currentTv.counter : bson_1.Long.fromNumber(currentTv.counter);\n  const newCounter = typeof newTv.counter === 'bigint' ? bson_1.Long.fromBigInt(newTv.counter) : bson_1.Long.isLong(newTv.counter) ? newTv.counter : bson_1.Long.fromNumber(newTv.counter);\n  return currentCounter.compare(newCounter);\n}", "map": {"version": 3, "names": ["exports", "parseServerType", "compareTopologyVersion", "bson_1", "require", "error_1", "utils_1", "common_1", "WRITABLE_SERVER_TYPES", "Set", "ServerType", "RSPrimary", "Standalone", "Mongos", "LoadBalancer", "DATA_BEARING_SERVER_TYPES", "RSSecondary", "ServerDescription", "constructor", "address", "hello", "options", "MongoRuntimeError", "HostAddress", "fromString", "toString", "type", "hosts", "map", "host", "toLowerCase", "passives", "arbiters", "tags", "minWireVersion", "maxWireVersion", "roundTripTime", "minRoundTripTime", "lastUpdateTime", "now", "lastWriteDate", "lastWrite", "error", "stack", "topologyVersion", "setName", "setVersion", "electionId", "logicalSessionTimeoutMinutes", "maxMessageSizeBytes", "maxWriteBatchSize", "maxBsonObjectSize", "primary", "me", "$clusterTime", "iscryptd", "Boolean", "host<PERSON><PERSON><PERSON>", "allHosts", "concat", "isReadable", "isWritable", "isDataBearing", "has", "chop<PERSON><PERSON><PERSON>", "port", "length", "slice", "split", "pop", "Number", "parseInt", "equals", "other", "topologyVersionsEqual", "electionIdsEqual", "compareObjectId", "errorStrictEqual", "arrayStrictEqual", "tagsStrictEqual", "loadBalanced", "ok", "Unknown", "isreplicaset", "RSGhost", "msg", "hidden", "RSOther", "isWritablePrimary", "secondary", "arbiterOnly", "RSArbiter", "tags2", "tagsKeys", "Object", "keys", "tags2Keys", "every", "key", "currentTv", "newTv", "processId", "currentCounter", "counter", "<PERSON>", "fromBigInt", "isLong", "fromNumber", "newCounter", "compare"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\sdam\\server_description.ts"], "sourcesContent": ["import { type Document, Long, type ObjectId } from '../bson';\nimport { type <PERSON>go<PERSON>rror, MongoRuntimeError } from '../error';\nimport { arrayStrictEqual, compareObjectId, errorStrictEqual, HostAddress, now } from '../utils';\nimport { type ClusterTime, ServerType } from './common';\n\nconst WRITABLE_SERVER_TYPES = new Set<ServerType>([\n  ServerType.RSPrimary,\n  ServerType.Standalone,\n  ServerType.Mongos,\n  ServerType.LoadBalancer\n]);\n\nconst DATA_BEARING_SERVER_TYPES = new Set<ServerType>([\n  ServerType.RSPrimary,\n  ServerType.RSSecondary,\n  ServerType.Mongos,\n  ServerType.Standalone,\n  ServerType.LoadBalancer\n]);\n\n/** @public */\nexport interface TopologyVersion {\n  processId: ObjectId;\n  counter: Long;\n}\n\n/** @public */\nexport type TagSet = { [key: string]: string };\n\n/** @internal */\nexport interface ServerDescriptionOptions {\n  /** An Error used for better reporting debugging */\n  error?: MongoError;\n\n  /** The average round trip time to ping this server (in ms) */\n  roundTripTime?: number;\n  /** The minimum round trip time to ping this server over the past 10 samples(in ms) */\n  minRoundTripTime?: number;\n\n  /** If the client is in load balancing mode. */\n  loadBalanced?: boolean;\n}\n\n/**\n * The client's view of a single server, based on the most recent hello outcome.\n *\n * Internal type, not meant to be directly instantiated\n * @public\n */\nexport class ServerDescription {\n  address: string;\n  type: ServerType;\n  hosts: string[];\n  passives: string[];\n  arbiters: string[];\n  tags: TagSet;\n  error: MongoError | null;\n  topologyVersion: TopologyVersion | null;\n  minWireVersion: number;\n  maxWireVersion: number;\n  roundTripTime: number;\n  /** The minimum measurement of the last 10 measurements of roundTripTime that have been collected */\n  minRoundTripTime: number;\n  lastUpdateTime: number;\n  lastWriteDate: number;\n  me: string | null;\n  primary: string | null;\n  setName: string | null;\n  setVersion: number | null;\n  electionId: ObjectId | null;\n  logicalSessionTimeoutMinutes: number | null;\n  /** The max message size in bytes for the server. */\n  maxMessageSizeBytes: number | null;\n  /** The max number of writes in a bulk write command. */\n  maxWriteBatchSize: number | null;\n  /** The max bson object size. */\n  maxBsonObjectSize: number | null;\n  /** Indicates server is a mongocryptd instance. */\n  iscryptd: boolean;\n\n  // NOTE: does this belong here? It seems we should gossip the cluster time at the CMAP level\n  $clusterTime?: ClusterTime;\n\n  /**\n   * Create a ServerDescription\n   * @internal\n   *\n   * @param address - The address of the server\n   * @param hello - An optional hello response for this server\n   */\n  constructor(\n    address: HostAddress | string,\n    hello?: Document,\n    options: ServerDescriptionOptions = {}\n  ) {\n    if (address == null || address === '') {\n      throw new MongoRuntimeError('ServerDescription must be provided with a non-empty address');\n    }\n\n    this.address =\n      typeof address === 'string'\n        ? HostAddress.fromString(address).toString() // Use HostAddress to normalize\n        : address.toString();\n    this.type = parseServerType(hello, options);\n    this.hosts = hello?.hosts?.map((host: string) => host.toLowerCase()) ?? [];\n    this.passives = hello?.passives?.map((host: string) => host.toLowerCase()) ?? [];\n    this.arbiters = hello?.arbiters?.map((host: string) => host.toLowerCase()) ?? [];\n    this.tags = hello?.tags ?? {};\n    this.minWireVersion = hello?.minWireVersion ?? 0;\n    this.maxWireVersion = hello?.maxWireVersion ?? 0;\n    this.roundTripTime = options?.roundTripTime ?? -1;\n    this.minRoundTripTime = options?.minRoundTripTime ?? 0;\n    this.lastUpdateTime = now();\n    this.lastWriteDate = hello?.lastWrite?.lastWriteDate ?? 0;\n    // NOTE: This actually builds the stack string instead of holding onto the getter and all its\n    // associated references. This is done to prevent a memory leak.\n    this.error = options.error ?? null;\n    this.error?.stack;\n    // TODO(NODE-2674): Preserve int64 sent from MongoDB\n    this.topologyVersion = this.error?.topologyVersion ?? hello?.topologyVersion ?? null;\n    this.setName = hello?.setName ?? null;\n    this.setVersion = hello?.setVersion ?? null;\n    this.electionId = hello?.electionId ?? null;\n    this.logicalSessionTimeoutMinutes = hello?.logicalSessionTimeoutMinutes ?? null;\n    this.maxMessageSizeBytes = hello?.maxMessageSizeBytes ?? null;\n    this.maxWriteBatchSize = hello?.maxWriteBatchSize ?? null;\n    this.maxBsonObjectSize = hello?.maxBsonObjectSize ?? null;\n    this.primary = hello?.primary ?? null;\n    this.me = hello?.me?.toLowerCase() ?? null;\n    this.$clusterTime = hello?.$clusterTime ?? null;\n    this.iscryptd = Boolean(hello?.iscryptd);\n  }\n\n  get hostAddress(): HostAddress {\n    return HostAddress.fromString(this.address);\n  }\n\n  get allHosts(): string[] {\n    return this.hosts.concat(this.arbiters).concat(this.passives);\n  }\n\n  /** Is this server available for reads*/\n  get isReadable(): boolean {\n    return this.type === ServerType.RSSecondary || this.isWritable;\n  }\n\n  /** Is this server data bearing */\n  get isDataBearing(): boolean {\n    return DATA_BEARING_SERVER_TYPES.has(this.type);\n  }\n\n  /** Is this server available for writes */\n  get isWritable(): boolean {\n    return WRITABLE_SERVER_TYPES.has(this.type);\n  }\n\n  get host(): string {\n    const chopLength = `:${this.port}`.length;\n    return this.address.slice(0, -chopLength);\n  }\n\n  get port(): number {\n    const port = this.address.split(':').pop();\n    return port ? Number.parseInt(port, 10) : 27017;\n  }\n\n  /**\n   * Determines if another `ServerDescription` is equal to this one per the rules defined in the SDAM specification.\n   * @see https://github.com/mongodb/specifications/blob/master/source/server-discovery-and-monitoring/server-discovery-and-monitoring.md\n   */\n  equals(other?: ServerDescription | null): boolean {\n    // Despite using the comparator that would determine a nullish topologyVersion as greater than\n    // for equality we should only always perform direct equality comparison\n    const topologyVersionsEqual =\n      this.topologyVersion === other?.topologyVersion ||\n      compareTopologyVersion(this.topologyVersion, other?.topologyVersion) === 0;\n\n    const electionIdsEqual =\n      this.electionId != null && other?.electionId != null\n        ? compareObjectId(this.electionId, other.electionId) === 0\n        : this.electionId === other?.electionId;\n\n    return (\n      other != null &&\n      other.iscryptd === this.iscryptd &&\n      errorStrictEqual(this.error, other.error) &&\n      this.type === other.type &&\n      this.minWireVersion === other.minWireVersion &&\n      arrayStrictEqual(this.hosts, other.hosts) &&\n      tagsStrictEqual(this.tags, other.tags) &&\n      this.setName === other.setName &&\n      this.setVersion === other.setVersion &&\n      electionIdsEqual &&\n      this.primary === other.primary &&\n      this.logicalSessionTimeoutMinutes === other.logicalSessionTimeoutMinutes &&\n      topologyVersionsEqual\n    );\n  }\n}\n\n// Parses a `hello` message and determines the server type\nexport function parseServerType(hello?: Document, options?: ServerDescriptionOptions): ServerType {\n  if (options?.loadBalanced) {\n    return ServerType.LoadBalancer;\n  }\n\n  if (!hello || !hello.ok) {\n    return ServerType.Unknown;\n  }\n\n  if (hello.isreplicaset) {\n    return ServerType.RSGhost;\n  }\n\n  if (hello.msg && hello.msg === 'isdbgrid') {\n    return ServerType.Mongos;\n  }\n\n  if (hello.setName) {\n    if (hello.hidden) {\n      return ServerType.RSOther;\n    } else if (hello.isWritablePrimary) {\n      return ServerType.RSPrimary;\n    } else if (hello.secondary) {\n      return ServerType.RSSecondary;\n    } else if (hello.arbiterOnly) {\n      return ServerType.RSArbiter;\n    } else {\n      return ServerType.RSOther;\n    }\n  }\n\n  return ServerType.Standalone;\n}\n\nfunction tagsStrictEqual(tags: TagSet, tags2: TagSet): boolean {\n  const tagsKeys = Object.keys(tags);\n  const tags2Keys = Object.keys(tags2);\n\n  return (\n    tagsKeys.length === tags2Keys.length &&\n    tagsKeys.every((key: string) => tags2[key] === tags[key])\n  );\n}\n\n/**\n * Compares two topology versions.\n *\n * 1. If the response topologyVersion is unset or the ServerDescription's\n *    topologyVersion is null, the client MUST assume the response is more recent.\n * 1. If the response's topologyVersion.processId is not equal to the\n *    ServerDescription's, the client MUST assume the response is more recent.\n * 1. If the response's topologyVersion.processId is equal to the\n *    ServerDescription's, the client MUST use the counter field to determine\n *    which topologyVersion is more recent.\n *\n * ```ts\n * currentTv <   newTv === -1\n * currentTv === newTv === 0\n * currentTv >   newTv === 1\n * ```\n */\nexport function compareTopologyVersion(\n  currentTv?: TopologyVersion | null,\n  newTv?: TopologyVersion | null\n): 0 | -1 | 1 {\n  if (currentTv == null || newTv == null) {\n    return -1;\n  }\n\n  if (!currentTv.processId.equals(newTv.processId)) {\n    return -1;\n  }\n\n  // TODO(NODE-2674): Preserve int64 sent from MongoDB\n  const currentCounter =\n    typeof currentTv.counter === 'bigint'\n      ? Long.fromBigInt(currentTv.counter)\n      : Long.isLong(currentTv.counter)\n        ? currentTv.counter\n        : Long.fromNumber(currentTv.counter);\n\n  const newCounter =\n    typeof newTv.counter === 'bigint'\n      ? Long.fromBigInt(newTv.counter)\n      : Long.isLong(newTv.counter)\n        ? newTv.counter\n        : Long.fromNumber(newTv.counter);\n\n  return currentCounter.compare(newCounter);\n}\n"], "mappings": ";;;;;;AAyMAA,OAAA,CAAAC,eAAA,GAAAA,eAAA;AA6DAD,OAAA,CAAAE,sBAAA,GAAAA,sBAAA;AAtQA,MAAAC,MAAA,GAAAC,OAAA;AACA,MAAAC,OAAA,GAAAD,OAAA;AACA,MAAAE,OAAA,GAAAF,OAAA;AACA,MAAAG,QAAA,GAAAH,OAAA;AAEA,MAAMI,qBAAqB,GAAG,IAAIC,GAAG,CAAa,CAChDF,QAAA,CAAAG,UAAU,CAACC,SAAS,EACpBJ,QAAA,CAAAG,UAAU,CAACE,UAAU,EACrBL,QAAA,CAAAG,UAAU,CAACG,MAAM,EACjBN,QAAA,CAAAG,UAAU,CAACI,YAAY,CACxB,CAAC;AAEF,MAAMC,yBAAyB,GAAG,IAAIN,GAAG,CAAa,CACpDF,QAAA,CAAAG,UAAU,CAACC,SAAS,EACpBJ,QAAA,CAAAG,UAAU,CAACM,WAAW,EACtBT,QAAA,CAAAG,UAAU,CAACG,MAAM,EACjBN,QAAA,CAAAG,UAAU,CAACE,UAAU,EACrBL,QAAA,CAAAG,UAAU,CAACI,YAAY,CACxB,CAAC;AAyBF;;;;;;AAMA,MAAaG,iBAAiB;EAkC5B;;;;;;;EAOAC,YACEC,OAA6B,EAC7BC,KAAgB,EAChBC,OAAA,GAAoC,EAAE;IAEtC,IAAIF,OAAO,IAAI,IAAI,IAAIA,OAAO,KAAK,EAAE,EAAE;MACrC,MAAM,IAAId,OAAA,CAAAiB,iBAAiB,CAAC,6DAA6D,CAAC;IAC5F;IAEA,IAAI,CAACH,OAAO,GACV,OAAOA,OAAO,KAAK,QAAQ,GACvBb,OAAA,CAAAiB,WAAW,CAACC,UAAU,CAACL,OAAO,CAAC,CAACM,QAAQ,EAAE,CAAC;IAAA,EAC3CN,OAAO,CAACM,QAAQ,EAAE;IACxB,IAAI,CAACC,IAAI,GAAGzB,eAAe,CAACmB,KAAK,EAAEC,OAAO,CAAC;IAC3C,IAAI,CAACM,KAAK,GAAGP,KAAK,EAAEO,KAAK,EAAEC,GAAG,CAAEC,IAAY,IAAKA,IAAI,CAACC,WAAW,EAAE,CAAC,IAAI,EAAE;IAC1E,IAAI,CAACC,QAAQ,GAAGX,KAAK,EAAEW,QAAQ,EAAEH,GAAG,CAAEC,IAAY,IAAKA,IAAI,CAACC,WAAW,EAAE,CAAC,IAAI,EAAE;IAChF,IAAI,CAACE,QAAQ,GAAGZ,KAAK,EAAEY,QAAQ,EAAEJ,GAAG,CAAEC,IAAY,IAAKA,IAAI,CAACC,WAAW,EAAE,CAAC,IAAI,EAAE;IAChF,IAAI,CAACG,IAAI,GAAGb,KAAK,EAAEa,IAAI,IAAI,EAAE;IAC7B,IAAI,CAACC,cAAc,GAAGd,KAAK,EAAEc,cAAc,IAAI,CAAC;IAChD,IAAI,CAACC,cAAc,GAAGf,KAAK,EAAEe,cAAc,IAAI,CAAC;IAChD,IAAI,CAACC,aAAa,GAAGf,OAAO,EAAEe,aAAa,IAAI,CAAC,CAAC;IACjD,IAAI,CAACC,gBAAgB,GAAGhB,OAAO,EAAEgB,gBAAgB,IAAI,CAAC;IACtD,IAAI,CAACC,cAAc,GAAG,IAAAhC,OAAA,CAAAiC,GAAG,GAAE;IAC3B,IAAI,CAACC,aAAa,GAAGpB,KAAK,EAAEqB,SAAS,EAAED,aAAa,IAAI,CAAC;IACzD;IACA;IACA,IAAI,CAACE,KAAK,GAAGrB,OAAO,CAACqB,KAAK,IAAI,IAAI;IAClC,IAAI,CAACA,KAAK,EAAEC,KAAK;IACjB;IACA,IAAI,CAACC,eAAe,GAAG,IAAI,CAACF,KAAK,EAAEE,eAAe,IAAIxB,KAAK,EAAEwB,eAAe,IAAI,IAAI;IACpF,IAAI,CAACC,OAAO,GAAGzB,KAAK,EAAEyB,OAAO,IAAI,IAAI;IACrC,IAAI,CAACC,UAAU,GAAG1B,KAAK,EAAE0B,UAAU,IAAI,IAAI;IAC3C,IAAI,CAACC,UAAU,GAAG3B,KAAK,EAAE2B,UAAU,IAAI,IAAI;IAC3C,IAAI,CAACC,4BAA4B,GAAG5B,KAAK,EAAE4B,4BAA4B,IAAI,IAAI;IAC/E,IAAI,CAACC,mBAAmB,GAAG7B,KAAK,EAAE6B,mBAAmB,IAAI,IAAI;IAC7D,IAAI,CAACC,iBAAiB,GAAG9B,KAAK,EAAE8B,iBAAiB,IAAI,IAAI;IACzD,IAAI,CAACC,iBAAiB,GAAG/B,KAAK,EAAE+B,iBAAiB,IAAI,IAAI;IACzD,IAAI,CAACC,OAAO,GAAGhC,KAAK,EAAEgC,OAAO,IAAI,IAAI;IACrC,IAAI,CAACC,EAAE,GAAGjC,KAAK,EAAEiC,EAAE,EAAEvB,WAAW,EAAE,IAAI,IAAI;IAC1C,IAAI,CAACwB,YAAY,GAAGlC,KAAK,EAAEkC,YAAY,IAAI,IAAI;IAC/C,IAAI,CAACC,QAAQ,GAAGC,OAAO,CAACpC,KAAK,EAAEmC,QAAQ,CAAC;EAC1C;EAEA,IAAIE,WAAWA,CAAA;IACb,OAAOnD,OAAA,CAAAiB,WAAW,CAACC,UAAU,CAAC,IAAI,CAACL,OAAO,CAAC;EAC7C;EAEA,IAAIuC,QAAQA,CAAA;IACV,OAAO,IAAI,CAAC/B,KAAK,CAACgC,MAAM,CAAC,IAAI,CAAC3B,QAAQ,CAAC,CAAC2B,MAAM,CAAC,IAAI,CAAC5B,QAAQ,CAAC;EAC/D;EAEA;EACA,IAAI6B,UAAUA,CAAA;IACZ,OAAO,IAAI,CAAClC,IAAI,KAAKnB,QAAA,CAAAG,UAAU,CAACM,WAAW,IAAI,IAAI,CAAC6C,UAAU;EAChE;EAEA;EACA,IAAIC,aAAaA,CAAA;IACf,OAAO/C,yBAAyB,CAACgD,GAAG,CAAC,IAAI,CAACrC,IAAI,CAAC;EACjD;EAEA;EACA,IAAImC,UAAUA,CAAA;IACZ,OAAOrD,qBAAqB,CAACuD,GAAG,CAAC,IAAI,CAACrC,IAAI,CAAC;EAC7C;EAEA,IAAIG,IAAIA,CAAA;IACN,MAAMmC,UAAU,GAAG,IAAI,IAAI,CAACC,IAAI,EAAE,CAACC,MAAM;IACzC,OAAO,IAAI,CAAC/C,OAAO,CAACgD,KAAK,CAAC,CAAC,EAAE,CAACH,UAAU,CAAC;EAC3C;EAEA,IAAIC,IAAIA,CAAA;IACN,MAAMA,IAAI,GAAG,IAAI,CAAC9C,OAAO,CAACiD,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,EAAE;IAC1C,OAAOJ,IAAI,GAAGK,MAAM,CAACC,QAAQ,CAACN,IAAI,EAAE,EAAE,CAAC,GAAG,KAAK;EACjD;EAEA;;;;EAIAO,MAAMA,CAACC,KAAgC;IACrC;IACA;IACA,MAAMC,qBAAqB,GACzB,IAAI,CAAC9B,eAAe,KAAK6B,KAAK,EAAE7B,eAAe,IAC/C1C,sBAAsB,CAAC,IAAI,CAAC0C,eAAe,EAAE6B,KAAK,EAAE7B,eAAe,CAAC,KAAK,CAAC;IAE5E,MAAM+B,gBAAgB,GACpB,IAAI,CAAC5B,UAAU,IAAI,IAAI,IAAI0B,KAAK,EAAE1B,UAAU,IAAI,IAAI,GAChD,IAAAzC,OAAA,CAAAsE,eAAe,EAAC,IAAI,CAAC7B,UAAU,EAAE0B,KAAK,CAAC1B,UAAU,CAAC,KAAK,CAAC,GACxD,IAAI,CAACA,UAAU,KAAK0B,KAAK,EAAE1B,UAAU;IAE3C,OACE0B,KAAK,IAAI,IAAI,IACbA,KAAK,CAAClB,QAAQ,KAAK,IAAI,CAACA,QAAQ,IAChC,IAAAjD,OAAA,CAAAuE,gBAAgB,EAAC,IAAI,CAACnC,KAAK,EAAE+B,KAAK,CAAC/B,KAAK,CAAC,IACzC,IAAI,CAAChB,IAAI,KAAK+C,KAAK,CAAC/C,IAAI,IACxB,IAAI,CAACQ,cAAc,KAAKuC,KAAK,CAACvC,cAAc,IAC5C,IAAA5B,OAAA,CAAAwE,gBAAgB,EAAC,IAAI,CAACnD,KAAK,EAAE8C,KAAK,CAAC9C,KAAK,CAAC,IACzCoD,eAAe,CAAC,IAAI,CAAC9C,IAAI,EAAEwC,KAAK,CAACxC,IAAI,CAAC,IACtC,IAAI,CAACY,OAAO,KAAK4B,KAAK,CAAC5B,OAAO,IAC9B,IAAI,CAACC,UAAU,KAAK2B,KAAK,CAAC3B,UAAU,IACpC6B,gBAAgB,IAChB,IAAI,CAACvB,OAAO,KAAKqB,KAAK,CAACrB,OAAO,IAC9B,IAAI,CAACJ,4BAA4B,KAAKyB,KAAK,CAACzB,4BAA4B,IACxE0B,qBAAqB;EAEzB;;AApJF1E,OAAA,CAAAiB,iBAAA,GAAAA,iBAAA;AAuJA;AACA,SAAgBhB,eAAeA,CAACmB,KAAgB,EAAEC,OAAkC;EAClF,IAAIA,OAAO,EAAE2D,YAAY,EAAE;IACzB,OAAOzE,QAAA,CAAAG,UAAU,CAACI,YAAY;EAChC;EAEA,IAAI,CAACM,KAAK,IAAI,CAACA,KAAK,CAAC6D,EAAE,EAAE;IACvB,OAAO1E,QAAA,CAAAG,UAAU,CAACwE,OAAO;EAC3B;EAEA,IAAI9D,KAAK,CAAC+D,YAAY,EAAE;IACtB,OAAO5E,QAAA,CAAAG,UAAU,CAAC0E,OAAO;EAC3B;EAEA,IAAIhE,KAAK,CAACiE,GAAG,IAAIjE,KAAK,CAACiE,GAAG,KAAK,UAAU,EAAE;IACzC,OAAO9E,QAAA,CAAAG,UAAU,CAACG,MAAM;EAC1B;EAEA,IAAIO,KAAK,CAACyB,OAAO,EAAE;IACjB,IAAIzB,KAAK,CAACkE,MAAM,EAAE;MAChB,OAAO/E,QAAA,CAAAG,UAAU,CAAC6E,OAAO;IAC3B,CAAC,MAAM,IAAInE,KAAK,CAACoE,iBAAiB,EAAE;MAClC,OAAOjF,QAAA,CAAAG,UAAU,CAACC,SAAS;IAC7B,CAAC,MAAM,IAAIS,KAAK,CAACqE,SAAS,EAAE;MAC1B,OAAOlF,QAAA,CAAAG,UAAU,CAACM,WAAW;IAC/B,CAAC,MAAM,IAAII,KAAK,CAACsE,WAAW,EAAE;MAC5B,OAAOnF,QAAA,CAAAG,UAAU,CAACiF,SAAS;IAC7B,CAAC,MAAM;MACL,OAAOpF,QAAA,CAAAG,UAAU,CAAC6E,OAAO;IAC3B;EACF;EAEA,OAAOhF,QAAA,CAAAG,UAAU,CAACE,UAAU;AAC9B;AAEA,SAASmE,eAAeA,CAAC9C,IAAY,EAAE2D,KAAa;EAClD,MAAMC,QAAQ,GAAGC,MAAM,CAACC,IAAI,CAAC9D,IAAI,CAAC;EAClC,MAAM+D,SAAS,GAAGF,MAAM,CAACC,IAAI,CAACH,KAAK,CAAC;EAEpC,OACEC,QAAQ,CAAC3B,MAAM,KAAK8B,SAAS,CAAC9B,MAAM,IACpC2B,QAAQ,CAACI,KAAK,CAAEC,GAAW,IAAKN,KAAK,CAACM,GAAG,CAAC,KAAKjE,IAAI,CAACiE,GAAG,CAAC,CAAC;AAE7D;AAEA;;;;;;;;;;;;;;;;;AAiBA,SAAgBhG,sBAAsBA,CACpCiG,SAAkC,EAClCC,KAA8B;EAE9B,IAAID,SAAS,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,EAAE;IACtC,OAAO,CAAC,CAAC;EACX;EAEA,IAAI,CAACD,SAAS,CAACE,SAAS,CAAC7B,MAAM,CAAC4B,KAAK,CAACC,SAAS,CAAC,EAAE;IAChD,OAAO,CAAC,CAAC;EACX;EAEA;EACA,MAAMC,cAAc,GAClB,OAAOH,SAAS,CAACI,OAAO,KAAK,QAAQ,GACjCpG,MAAA,CAAAqG,IAAI,CAACC,UAAU,CAACN,SAAS,CAACI,OAAO,CAAC,GAClCpG,MAAA,CAAAqG,IAAI,CAACE,MAAM,CAACP,SAAS,CAACI,OAAO,CAAC,GAC5BJ,SAAS,CAACI,OAAO,GACjBpG,MAAA,CAAAqG,IAAI,CAACG,UAAU,CAACR,SAAS,CAACI,OAAO,CAAC;EAE1C,MAAMK,UAAU,GACd,OAAOR,KAAK,CAACG,OAAO,KAAK,QAAQ,GAC7BpG,MAAA,CAAAqG,IAAI,CAACC,UAAU,CAACL,KAAK,CAACG,OAAO,CAAC,GAC9BpG,MAAA,CAAAqG,IAAI,CAACE,MAAM,CAACN,KAAK,CAACG,OAAO,CAAC,GACxBH,KAAK,CAACG,OAAO,GACbpG,MAAA,CAAAqG,IAAI,CAACG,UAAU,CAACP,KAAK,CAACG,OAAO,CAAC;EAEtC,OAAOD,cAAc,CAACO,OAAO,CAACD,UAAU,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}