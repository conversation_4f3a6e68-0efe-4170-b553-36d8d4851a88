import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface HealthRecord {
  _id: string;
  animal: string;
  recordType: 'vaccination' | 'treatment' | 'checkup' | 'illness' | 'injury' | 'surgery' | 'test';
  date: string;
  veterinarian?: string;
  vaccine?: {
    name: string;
    manufacturer?: string;
    batchNumber?: string;
    expirationDate?: string;
    dosage?: string;
    route?: 'intramuscular' | 'subcutaneous' | 'oral' | 'nasal' | 'intravenous';
  };
  treatment?: {
    condition: string;
    medication: string;
    dosage: string;
    frequency: string;
    duration: string;
    withdrawalPeriod?: number;
    cost?: number;
  };
  symptoms?: string[];
  diagnosis?: string;
  temperature?: number;
  heartRate?: number;
  respiratoryRate?: number;
  bodyConditionScore?: number;
  testResults?: Array<{
    testName: string;
    result: string;
    normalRange?: string;
    units?: string;
    labName?: string;
  }>;
  followUpRequired: boolean;
  followUpDate?: string;
  followUpNotes?: string;
  images?: Array<{
    url: string;
    caption?: string;
  }>;
  documents?: Array<{
    name: string;
    url: string;
    type: string;
  }>;
  notes?: string;
  cost?: number;
  createdBy: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt: string;
}

export interface HealthAlert {
  _id: string;
  animal: string;
  alertType: 'vaccination_due' | 'treatment_due' | 'checkup_due' | 'withdrawal_period' | 'health_concern' | 'medication_expiry';
  priority: 'low' | 'medium' | 'high' | 'critical';
  title: string;
  description?: string;
  dueDate?: string;
  isResolved: boolean;
  resolvedDate?: string;
  resolvedBy?: string;
  resolvedNotes?: string;
  relatedHealthRecord?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
  daysUntilDue?: number;
}

export interface HealthState {
  records: HealthRecord[];
  alerts: HealthAlert[];
  selectedRecord: HealthRecord | null;
  isLoading: boolean;
  error: string | null;
  statistics: {
    totalRecords: number;
    recordsByType: Record<string, number>;
    alertsByPriority: Record<string, number>;
    upcomingVaccinations: number;
    activeAlerts: number;
  } | null;
}

const initialState: HealthState = {
  records: [],
  alerts: [],
  selectedRecord: null,
  isLoading: false,
  error: null,
  statistics: null,
};

const healthSlice = createSlice({
  name: 'health',
  initialState,
  reducers: {
    setRecords: (state, action: PayloadAction<HealthRecord[]>) => {
      state.records = action.payload;
    },
    addRecord: (state, action: PayloadAction<HealthRecord>) => {
      state.records.unshift(action.payload);
    },
    updateRecord: (state, action: PayloadAction<HealthRecord>) => {
      const index = state.records.findIndex(record => record._id === action.payload._id);
      if (index !== -1) {
        state.records[index] = action.payload;
      }
    },
    deleteRecord: (state, action: PayloadAction<string>) => {
      state.records = state.records.filter(record => record._id !== action.payload);
    },
    setAlerts: (state, action: PayloadAction<HealthAlert[]>) => {
      state.alerts = action.payload;
    },
    addAlert: (state, action: PayloadAction<HealthAlert>) => {
      state.alerts.unshift(action.payload);
    },
    resolveAlert: (state, action: PayloadAction<{ id: string; resolvedBy: string; notes?: string }>) => {
      const alert = state.alerts.find(a => a._id === action.payload.id);
      if (alert) {
        alert.isResolved = true;
        alert.resolvedDate = new Date().toISOString();
        alert.resolvedBy = action.payload.resolvedBy;
        alert.resolvedNotes = action.payload.notes;
      }
    },
    setSelectedRecord: (state, action: PayloadAction<HealthRecord | null>) => {
      state.selectedRecord = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    setStatistics: (state, action: PayloadAction<HealthState['statistics']>) => {
      state.statistics = action.payload;
    },
  },
});

export const {
  setRecords,
  addRecord,
  updateRecord,
  deleteRecord,
  setAlerts,
  addAlert,
  resolveAlert,
  setSelectedRecord,
  setLoading,
  setError,
  clearError,
  setStatistics,
} = healthSlice.actions;

export default healthSlice.reducer;
