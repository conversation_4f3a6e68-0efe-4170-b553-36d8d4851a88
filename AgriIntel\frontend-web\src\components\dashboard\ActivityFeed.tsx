import React from 'react';
import { useTranslation } from 'react-i18next';

interface Activity {
  id: string;
  type: 'vaccination' | 'birth' | 'sale' | 'health' | 'feeding' | 'breeding';
  message: string;
  timestamp: string;
  animalId?: string;
  priority?: 'low' | 'medium' | 'high';
}

interface ActivityFeedProps {
  activities?: Activity[];
  maxItems?: number;
}

const ActivityFeed: React.FC<ActivityFeedProps> = ({ 
  activities = [], 
  maxItems = 5 
}) => {
  const { t } = useTranslation();

  // Mock data if no activities provided
  const mockActivities: Activity[] = [
    {
      id: '1',
      type: 'vaccination',
      message: 'Cow C001 vaccinated against FMD',
      timestamp: '2 hours ago',
      animalId: 'C001',
      priority: 'medium'
    },
    {
      id: '2',
      type: 'birth',
      message: 'New calf born to Cow C015',
      timestamp: '4 hours ago',
      animalId: 'C015',
      priority: 'high'
    },
    {
      id: '3',
      type: 'health',
      message: 'Sheep S023 showing signs of lameness',
      timestamp: '6 hours ago',
      animalId: 'S023',
      priority: 'high'
    },
    {
      id: '4',
      type: 'feeding',
      message: 'Feed inventory low for cattle section',
      timestamp: '8 hours ago',
      priority: 'medium'
    },
    {
      id: '5',
      type: 'sale',
      message: 'Goat G012 sold to local buyer',
      timestamp: '1 day ago',
      animalId: 'G012',
      priority: 'low'
    }
  ];

  const displayActivities = activities.length > 0 ? activities : mockActivities;
  const limitedActivities = displayActivities.slice(0, maxItems);

  const getActivityColor = (type: Activity['type']) => {
    switch (type) {
      case 'vaccination':
        return 'bg-green-400';
      case 'birth':
        return 'bg-blue-400';
      case 'sale':
        return 'bg-purple-400';
      case 'health':
        return 'bg-red-400';
      case 'feeding':
        return 'bg-yellow-400';
      case 'breeding':
        return 'bg-pink-400';
      default:
        return 'bg-gray-400';
    }
  };

  const getPriorityColor = (priority?: Activity['priority']) => {
    switch (priority) {
      case 'high':
        return 'border-l-red-500';
      case 'medium':
        return 'border-l-yellow-500';
      case 'low':
        return 'border-l-green-500';
      default:
        return 'border-l-gray-300';
    }
  };

  return (
    <div className="space-y-4">
      {limitedActivities.map((activity) => (
        <div 
          key={activity.id} 
          className={`flex items-start space-x-3 p-3 border-l-4 ${getPriorityColor(activity.priority)} bg-gray-50 rounded-r-md`}
        >
          <div className={`w-2 h-2 ${getActivityColor(activity.type)} rounded-full mt-2 flex-shrink-0`}></div>
          <div className="flex-1 min-w-0">
            <p className="text-sm text-gray-900">{activity.message}</p>
            <div className="flex items-center space-x-2 mt-1">
              <p className="text-xs text-gray-500">{activity.timestamp}</p>
              {activity.animalId && (
                <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                  {activity.animalId}
                </span>
              )}
            </div>
          </div>
        </div>
      ))}
      
      {limitedActivities.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <p>No recent activities</p>
        </div>
      )}
    </div>
  );
};

export default ActivityFeed;
