{"ast": null, "code": "import _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"id\"],\n  _excluded2 = [\"id\"];\nimport * as React from 'react';\nimport { unstable_useEventCallback as useEventCallback, unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { useGridApiEventHandler, useGridApiOptionHandler } from '../../utils/useGridApiEventHandler';\nimport { GridEditModes, GridRowModes } from '../../../models/gridEditRowModel';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nimport { gridEditRowsStateSelector } from './gridEditingSelectors';\nimport { isPrintableKey } from '../../../utils/keyboardUtils';\nimport { gridColumnFieldsSelector, gridVisibleColumnFieldsSelector } from '../columns/gridColumnsSelector';\nimport { buildWarning } from '../../../utils/warning';\nimport { gridRowsDataRowIdToIdLookupSelector } from '../rows/gridRowsSelector';\nimport { deepClone } from '../../../utils/utils';\nimport { GridRowEditStopReasons, GridRowEditStartReasons } from '../../../models/params/gridRowParams';\nimport { GRID_ACTIONS_COLUMN_TYPE } from '../../../colDef';\nconst missingOnProcessRowUpdateErrorWarning = buildWarning(['MUI: A call to `processRowUpdate` threw an error which was not handled because `onProcessRowUpdateError` is missing.', 'To handle the error pass a callback to the `onProcessRowUpdateError` prop, e.g. `<DataGrid onProcessRowUpdateError={(error) => ...} />`.', 'For more detail, see http://mui.com/components/data-grid/editing/#server-side-persistence.'], 'error');\nexport const useGridRowEditing = (apiRef, props) => {\n  const [rowModesModel, setRowModesModel] = React.useState({});\n  const rowModesModelRef = React.useRef(rowModesModel);\n  const prevRowModesModel = React.useRef({});\n  const focusTimeout = React.useRef(null);\n  const nextFocusedCell = React.useRef(null);\n  const {\n    processRowUpdate,\n    onProcessRowUpdateError,\n    rowModesModel: rowModesModelProp,\n    onRowModesModelChange\n  } = props;\n  const runIfEditModeIsRow = callback => (...args) => {\n    if (props.editMode === GridEditModes.Row) {\n      callback(...args);\n    }\n  };\n  const throwIfNotEditable = React.useCallback((id, field) => {\n    const params = apiRef.current.getCellParams(id, field);\n    if (!apiRef.current.isCellEditable(params)) {\n      throw new Error(`MUI: The cell with id=${id} and field=${field} is not editable.`);\n    }\n  }, [apiRef]);\n  const throwIfNotInMode = React.useCallback((id, mode) => {\n    if (apiRef.current.getRowMode(id) !== mode) {\n      throw new Error(`MUI: The row with id=${id} is not in ${mode} mode.`);\n    }\n  }, [apiRef]);\n  const handleCellDoubleClick = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (apiRef.current.getRowMode(params.id) === GridRowModes.Edit) {\n      return;\n    }\n    const rowParams = apiRef.current.getRowParams(params.id);\n    const newParams = _extends({}, rowParams, {\n      field: params.field,\n      reason: GridRowEditStartReasons.cellDoubleClick\n    });\n    apiRef.current.publishEvent('rowEditStart', newParams, event);\n  }, [apiRef]);\n  const handleCellFocusIn = React.useCallback(params => {\n    nextFocusedCell.current = params;\n  }, []);\n  const handleCellFocusOut = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (apiRef.current.getRowMode(params.id) === GridRowModes.View) {\n      return;\n    }\n    // The mechanism to detect if we can stop editing a row is different from\n    // the cell editing. Instead of triggering it when clicking outside a cell,\n    // we must check if another cell in the same row was not clicked. To achieve\n    // that, first we keep track of all cells that gained focus. When a cell loses\n    // focus we check if the next cell that received focus is from a different row.\n    nextFocusedCell.current = null;\n    focusTimeout.current = setTimeout(() => {\n      var _nextFocusedCell$curr;\n      focusTimeout.current = null;\n      if (((_nextFocusedCell$curr = nextFocusedCell.current) == null ? void 0 : _nextFocusedCell$curr.id) !== params.id) {\n        // The row might have been deleted during the click\n        if (!apiRef.current.getRow(params.id)) {\n          return;\n        }\n\n        // The row may already changed its mode\n        if (apiRef.current.getRowMode(params.id) === GridRowModes.View) {\n          return;\n        }\n        const rowParams = apiRef.current.getRowParams(params.id);\n        const newParams = _extends({}, rowParams, {\n          field: params.field,\n          reason: GridRowEditStopReasons.rowFocusOut\n        });\n        apiRef.current.publishEvent('rowEditStop', newParams, event);\n      }\n    });\n  }, [apiRef]);\n  React.useEffect(() => {\n    return () => {\n      clearTimeout(focusTimeout.current);\n    };\n  }, []);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    if (params.cellMode === GridRowModes.Edit) {\n      // Wait until IME is settled for Asian languages like Japanese and Chinese\n      // TODO: `event.which` is deprecated but this is a temporary workaround\n      if (event.which === 229) {\n        return;\n      }\n      let reason;\n      if (event.key === 'Escape') {\n        reason = GridRowEditStopReasons.escapeKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridRowEditStopReasons.enterKeyDown;\n      } else if (event.key === 'Tab') {\n        const columnFields = gridVisibleColumnFieldsSelector(apiRef).filter(field => {\n          const column = apiRef.current.getColumn(field);\n          if (column.type === GRID_ACTIONS_COLUMN_TYPE) {\n            return true;\n          }\n          return apiRef.current.isCellEditable(apiRef.current.getCellParams(params.id, field));\n        });\n        if (event.shiftKey) {\n          if (params.field === columnFields[0]) {\n            // Exit if user pressed Shift+Tab on the first field\n            reason = GridRowEditStopReasons.shiftTabKeyDown;\n          }\n        } else if (params.field === columnFields[columnFields.length - 1]) {\n          // Exit if user pressed Tab on the last field\n          reason = GridRowEditStopReasons.tabKeyDown;\n        }\n\n        // Always prevent going to the next element in the tab sequence because the focus is\n        // handled manually to support edit components rendered inside Portals\n        event.preventDefault();\n        if (!reason) {\n          const index = columnFields.findIndex(field => field === params.field);\n          const nextFieldToFocus = columnFields[event.shiftKey ? index - 1 : index + 1];\n          apiRef.current.setCellFocus(params.id, nextFieldToFocus);\n        }\n      }\n      if (reason) {\n        const newParams = _extends({}, apiRef.current.getRowParams(params.id), {\n          reason,\n          field: params.field\n        });\n        apiRef.current.publishEvent('rowEditStop', newParams, event);\n      }\n    } else if (params.isEditable) {\n      let reason;\n      const canStartEditing = apiRef.current.unstable_applyPipeProcessors('canStartEditing', true, {\n        event,\n        cellParams: params,\n        editMode: 'row'\n      });\n      if (!canStartEditing) {\n        return;\n      }\n      if (isPrintableKey(event)) {\n        reason = GridRowEditStartReasons.printableKeyDown;\n      } else if ((event.ctrlKey || event.metaKey) && event.key === 'v') {\n        reason = GridRowEditStartReasons.printableKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridRowEditStartReasons.enterKeyDown;\n      } else if (event.key === 'Delete' || event.key === 'Backspace') {\n        // Delete on Windows, Backspace on macOS\n        reason = GridRowEditStartReasons.deleteKeyDown;\n      }\n      if (reason) {\n        const rowParams = apiRef.current.getRowParams(params.id);\n        const newParams = _extends({}, rowParams, {\n          field: params.field,\n          reason\n        });\n        apiRef.current.publishEvent('rowEditStart', newParams, event);\n      }\n    }\n  }, [apiRef]);\n  const handleRowEditStart = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    const startRowEditModeParams = {\n      id,\n      fieldToFocus: field\n    };\n    if (reason === GridRowEditStartReasons.printableKeyDown || reason === GridRowEditStartReasons.deleteKeyDown) {\n      startRowEditModeParams.deleteValue = !!field;\n    }\n    apiRef.current.startRowEditMode(startRowEditModeParams);\n  }, [apiRef]);\n  const handleRowEditStop = React.useCallback(params => {\n    const {\n      id,\n      reason,\n      field\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id);\n    let cellToFocusAfter;\n    if (reason === GridRowEditStopReasons.enterKeyDown) {\n      cellToFocusAfter = 'below';\n    } else if (reason === GridRowEditStopReasons.tabKeyDown) {\n      cellToFocusAfter = 'right';\n    } else if (reason === GridRowEditStopReasons.shiftTabKeyDown) {\n      cellToFocusAfter = 'left';\n    }\n    const ignoreModifications = reason === 'escapeKeyDown';\n    apiRef.current.stopRowEditMode({\n      id,\n      ignoreModifications,\n      field,\n      cellToFocusAfter\n    });\n  }, [apiRef]);\n  useGridApiEventHandler(apiRef, 'cellDoubleClick', runIfEditModeIsRow(handleCellDoubleClick));\n  useGridApiEventHandler(apiRef, 'cellFocusIn', runIfEditModeIsRow(handleCellFocusIn));\n  useGridApiEventHandler(apiRef, 'cellFocusOut', runIfEditModeIsRow(handleCellFocusOut));\n  useGridApiEventHandler(apiRef, 'cellKeyDown', runIfEditModeIsRow(handleCellKeyDown));\n  useGridApiEventHandler(apiRef, 'rowEditStart', runIfEditModeIsRow(handleRowEditStart));\n  useGridApiEventHandler(apiRef, 'rowEditStop', runIfEditModeIsRow(handleRowEditStop));\n  useGridApiOptionHandler(apiRef, 'rowEditStart', props.onRowEditStart);\n  useGridApiOptionHandler(apiRef, 'rowEditStop', props.onRowEditStop);\n  const getRowMode = React.useCallback(id => {\n    if (props.editMode === GridEditModes.Cell) {\n      return GridRowModes.View;\n    }\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const isEditing = editingState[id] && Object.keys(editingState[id]).length > 0;\n    return isEditing ? GridRowModes.Edit : GridRowModes.View;\n  }, [apiRef, props.editMode]);\n  const updateRowModesModel = useEventCallback(newModel => {\n    const isNewModelDifferentFromProp = newModel !== props.rowModesModel;\n    if (onRowModesModelChange && isNewModelDifferentFromProp) {\n      onRowModesModelChange(newModel, {});\n    }\n    if (props.rowModesModel && isNewModelDifferentFromProp) {\n      return; // The prop always win\n    }\n    setRowModesModel(newModel);\n    rowModesModelRef.current = newModel;\n    apiRef.current.publishEvent('rowModesModelChange', newModel);\n  });\n  const updateRowInRowModesModel = React.useCallback((id, newProps) => {\n    const newModel = _extends({}, rowModesModelRef.current);\n    if (newProps !== null) {\n      newModel[id] = _extends({}, newProps);\n    } else {\n      delete newModel[id];\n    }\n    updateRowModesModel(newModel);\n  }, [updateRowModesModel]);\n  const updateOrDeleteRowState = React.useCallback((id, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = newProps;\n      } else {\n        delete newEditingState[id];\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const updateOrDeleteFieldState = React.useCallback((id, field, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = _extends({}, newEditingState[id], {\n          [field]: _extends({}, newProps)\n        });\n      } else {\n        delete newEditingState[id][field];\n        if (Object.keys(newEditingState[id]).length === 0) {\n          delete newEditingState[id];\n        }\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const startRowEditMode = React.useCallback(params => {\n    const {\n        id\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded);\n    throwIfNotInMode(id, GridRowModes.View);\n    updateRowInRowModesModel(id, _extends({\n      mode: GridRowModes.Edit\n    }, other));\n  }, [throwIfNotInMode, updateRowInRowModesModel]);\n  const updateStateToStartRowEditMode = useEventCallback(params => {\n    const {\n      id,\n      fieldToFocus,\n      deleteValue,\n      initialValue\n    } = params;\n    const columnFields = gridColumnFieldsSelector(apiRef);\n    const newProps = columnFields.reduce((acc, field) => {\n      const cellParams = apiRef.current.getCellParams(id, field);\n      if (!cellParams.isEditable) {\n        return acc;\n      }\n      let newValue = apiRef.current.getCellValue(id, field);\n      if (fieldToFocus === field && (deleteValue || initialValue)) {\n        newValue = deleteValue ? '' : initialValue;\n      }\n      acc[field] = {\n        value: newValue,\n        error: false,\n        isProcessingProps: false\n      };\n      return acc;\n    }, {});\n    updateOrDeleteRowState(id, newProps);\n    if (fieldToFocus) {\n      apiRef.current.setCellFocus(id, fieldToFocus);\n    }\n  });\n  const stopRowEditMode = React.useCallback(params => {\n    const {\n        id\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded2);\n    throwIfNotInMode(id, GridRowModes.Edit);\n    updateRowInRowModesModel(id, _extends({\n      mode: GridRowModes.View\n    }, other));\n  }, [throwIfNotInMode, updateRowInRowModesModel]);\n  const updateStateToStopRowEditMode = useEventCallback(params => {\n    const {\n      id,\n      ignoreModifications,\n      field: focusedField,\n      cellToFocusAfter = 'none'\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id);\n    const finishRowEditMode = () => {\n      if (cellToFocusAfter !== 'none' && focusedField) {\n        apiRef.current.moveFocusToRelativeCell(id, focusedField, cellToFocusAfter);\n      }\n      updateOrDeleteRowState(id, null);\n      updateRowInRowModesModel(id, null);\n    };\n    if (ignoreModifications) {\n      finishRowEditMode();\n      return;\n    }\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const row = apiRef.current.getRow(id);\n    const isSomeFieldProcessingProps = Object.values(editingState[id]).some(fieldProps => fieldProps.isProcessingProps);\n    if (isSomeFieldProcessingProps) {\n      prevRowModesModel.current[id].mode = GridRowModes.Edit;\n      return;\n    }\n    const hasSomeFieldWithError = Object.values(editingState[id]).some(fieldProps => fieldProps.error);\n    if (hasSomeFieldWithError) {\n      prevRowModesModel.current[id].mode = GridRowModes.Edit;\n      // Revert the mode in the rowModesModel prop back to \"edit\"\n      updateRowInRowModesModel(id, {\n        mode: GridRowModes.Edit\n      });\n      return;\n    }\n    const rowUpdate = apiRef.current.getRowWithUpdatedValuesFromRowEditing(id);\n    if (processRowUpdate) {\n      const handleError = errorThrown => {\n        prevRowModesModel.current[id].mode = GridRowModes.Edit;\n        // Revert the mode in the rowModesModel prop back to \"edit\"\n        updateRowInRowModesModel(id, {\n          mode: GridRowModes.Edit\n        });\n        if (onProcessRowUpdateError) {\n          onProcessRowUpdateError(errorThrown);\n        } else {\n          missingOnProcessRowUpdateErrorWarning();\n        }\n      };\n      try {\n        Promise.resolve(processRowUpdate(rowUpdate, row)).then(finalRowUpdate => {\n          apiRef.current.updateRows([finalRowUpdate]);\n          finishRowEditMode();\n        }).catch(handleError);\n      } catch (errorThrown) {\n        handleError(errorThrown);\n      }\n    } else {\n      apiRef.current.updateRows([rowUpdate]);\n      finishRowEditMode();\n    }\n  });\n  const setRowEditingEditCellValue = React.useCallback(params => {\n    const {\n      id,\n      field,\n      value,\n      debounceMs,\n      unstable_skipValueParser: skipValueParser\n    } = params;\n    throwIfNotEditable(id, field);\n    const column = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    let parsedValue = value;\n    if (column.valueParser && !skipValueParser) {\n      parsedValue = column.valueParser(value, apiRef.current.getCellParams(id, field));\n    }\n    let editingState = gridEditRowsStateSelector(apiRef.current.state);\n    let newProps = _extends({}, editingState[id][field], {\n      value: parsedValue,\n      changeReason: debounceMs ? 'debouncedSetEditCellValue' : 'setEditCellValue'\n    });\n    if (!column.preProcessEditCellProps) {\n      updateOrDeleteFieldState(id, field, newProps);\n    }\n    return new Promise(resolve => {\n      const promises = [];\n      if (column.preProcessEditCellProps) {\n        const hasChanged = newProps.value !== editingState[id][field].value;\n        newProps = _extends({}, newProps, {\n          isProcessingProps: true\n        });\n        updateOrDeleteFieldState(id, field, newProps);\n        const _editingState$id = editingState[id],\n          otherFieldsProps = _objectWithoutPropertiesLoose(_editingState$id, [field].map(_toPropertyKey));\n        const promise = Promise.resolve(column.preProcessEditCellProps({\n          id,\n          row,\n          props: newProps,\n          hasChanged,\n          otherFieldsProps\n        })).then(processedProps => {\n          // Check again if the row is in edit mode because the user may have\n          // discarded the changes while the props were being processed.\n          if (apiRef.current.getRowMode(id) === GridRowModes.View) {\n            resolve(false);\n            return;\n          }\n          editingState = gridEditRowsStateSelector(apiRef.current.state);\n          processedProps = _extends({}, processedProps, {\n            isProcessingProps: false\n          });\n          // We don't reuse the value from the props pre-processing because when the\n          // promise resolves it may be already outdated. The only exception to this rule\n          // is when there's no pre-processing.\n          processedProps.value = column.preProcessEditCellProps ? editingState[id][field].value : parsedValue;\n          updateOrDeleteFieldState(id, field, processedProps);\n        });\n        promises.push(promise);\n      }\n      Object.entries(editingState[id]).forEach(([thisField, fieldProps]) => {\n        if (thisField === field) {\n          return;\n        }\n        const fieldColumn = apiRef.current.getColumn(thisField);\n        if (!fieldColumn.preProcessEditCellProps) {\n          return;\n        }\n        fieldProps = _extends({}, fieldProps, {\n          isProcessingProps: true\n        });\n        updateOrDeleteFieldState(id, thisField, fieldProps);\n        editingState = gridEditRowsStateSelector(apiRef.current.state);\n        const _editingState$id2 = editingState[id],\n          otherFieldsProps = _objectWithoutPropertiesLoose(_editingState$id2, [thisField].map(_toPropertyKey));\n        const promise = Promise.resolve(fieldColumn.preProcessEditCellProps({\n          id,\n          row,\n          props: fieldProps,\n          hasChanged: false,\n          otherFieldsProps\n        })).then(processedProps => {\n          // Check again if the row is in edit mode because the user may have\n          // discarded the changes while the props were being processed.\n          if (apiRef.current.getRowMode(id) === GridRowModes.View) {\n            resolve(false);\n            return;\n          }\n          processedProps = _extends({}, processedProps, {\n            isProcessingProps: false\n          });\n          updateOrDeleteFieldState(id, thisField, processedProps);\n        });\n        promises.push(promise);\n      });\n      Promise.all(promises).then(() => {\n        if (apiRef.current.getRowMode(id) === GridRowModes.Edit) {\n          editingState = gridEditRowsStateSelector(apiRef.current.state);\n          resolve(!editingState[id][field].error);\n        } else {\n          resolve(false);\n        }\n      });\n    });\n  }, [apiRef, throwIfNotEditable, updateOrDeleteFieldState]);\n  const getRowWithUpdatedValuesFromRowEditing = React.useCallback(id => {\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const row = apiRef.current.getRow(id);\n    if (!editingState[id]) {\n      return apiRef.current.getRow(id);\n    }\n    let rowUpdate = _extends({}, row);\n    Object.entries(editingState[id]).forEach(([field, fieldProps]) => {\n      const column = apiRef.current.getColumn(field);\n      if (column.valueSetter) {\n        rowUpdate = column.valueSetter({\n          value: fieldProps.value,\n          row: rowUpdate\n        });\n      } else {\n        rowUpdate[field] = fieldProps.value;\n      }\n    });\n    return rowUpdate;\n  }, [apiRef]);\n  const editingApi = {\n    getRowMode,\n    startRowEditMode,\n    stopRowEditMode\n  };\n  const editingPrivateApi = {\n    setRowEditingEditCellValue,\n    getRowWithUpdatedValuesFromRowEditing\n  };\n  useGridApiMethod(apiRef, editingApi, 'public');\n  useGridApiMethod(apiRef, editingPrivateApi, 'private');\n  React.useEffect(() => {\n    if (rowModesModelProp) {\n      updateRowModesModel(rowModesModelProp);\n    }\n  }, [rowModesModelProp, updateRowModesModel]);\n\n  // Run this effect synchronously so that the keyboard event can impact the yet-to-be-rendered input.\n  useEnhancedEffect(() => {\n    const idToIdLookup = gridRowsDataRowIdToIdLookupSelector(apiRef);\n\n    // Update the ref here because updateStateToStopRowEditMode may change it later\n    const copyOfPrevRowModesModel = prevRowModesModel.current;\n    prevRowModesModel.current = deepClone(rowModesModel); // Do a deep-clone because the attributes might be changed later\n\n    Object.entries(rowModesModel).forEach(([id, params]) => {\n      var _copyOfPrevRowModesMo, _idToIdLookup$id;\n      const prevMode = ((_copyOfPrevRowModesMo = copyOfPrevRowModesModel[id]) == null ? void 0 : _copyOfPrevRowModesMo.mode) || GridRowModes.View;\n      const originalId = (_idToIdLookup$id = idToIdLookup[id]) != null ? _idToIdLookup$id : id;\n      if (params.mode === GridRowModes.Edit && prevMode === GridRowModes.View) {\n        updateStateToStartRowEditMode(_extends({\n          id: originalId\n        }, params));\n      } else if (params.mode === GridRowModes.View && prevMode === GridRowModes.Edit) {\n        updateStateToStopRowEditMode(_extends({\n          id: originalId\n        }, params));\n      }\n    });\n  }, [apiRef, rowModesModel, updateStateToStartRowEditMode, updateStateToStopRowEditMode]);\n};", "map": {"version": 3, "names": ["_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "_objectWithoutPropertiesLoose", "_extends", "_excluded", "_excluded2", "React", "unstable_useEventCallback", "useEventCallback", "unstable_useEnhancedEffect", "useEnhancedEffect", "useGridApiEventHandler", "useGridApiOptionHandler", "GridEditModes", "GridRowModes", "useGridApiMethod", "gridEditRowsStateSelector", "isPrintableKey", "gridColumnFieldsSelector", "gridVisibleColumnFieldsSelector", "buildWarning", "gridRowsDataRowIdToIdLookupSelector", "deepClone", "GridRowEditStopReasons", "GridRowEditStartReasons", "GRID_ACTIONS_COLUMN_TYPE", "missingOnProcessRowUpdateErrorWarning", "useGridRowEditing", "apiRef", "props", "rowModesModel", "setRowModesModel", "useState", "rowModesModelRef", "useRef", "prevRowModesModel", "focusTimeout", "nextFocusedCell", "processRowUpdate", "onProcessRowUpdateError", "rowModesModelProp", "onRowModesModelChange", "runIfEditModeIsRow", "callback", "args", "editMode", "Row", "throwIfNotEditable", "useCallback", "id", "field", "params", "current", "getCellParams", "isCellEditable", "Error", "throwIfNotInMode", "mode", "getRowMode", "handleCellDoubleClick", "event", "isEditable", "Edit", "rowParams", "getRowParams", "newParams", "reason", "cellDoubleClick", "publishEvent", "handleCellFocusIn", "handleCellFocusOut", "View", "setTimeout", "_nextFocusedCell$curr", "getRow", "rowFocusOut", "useEffect", "clearTimeout", "handleCellKeyDown", "cellMode", "which", "key", "escapeKeyDown", "enterKeyDown", "columnFields", "filter", "column", "getColumn", "type", "shift<PERSON>ey", "shiftTabKeyDown", "length", "tabKeyDown", "preventDefault", "index", "findIndex", "nextFieldToFocus", "setCellFocus", "canStartEditing", "unstable_applyPipeProcessors", "cellParams", "printableKeyDown", "ctrl<PERSON>ey", "metaKey", "deleteKeyDown", "handleRowEditStart", "startRowEditModeParams", "fieldToFocus", "deleteValue", "startRowEditMode", "handleRowEditStop", "runPendingEditCellValueMutation", "cellToFocusAfter", "ignoreModifications", "stopRowEditMode", "onRowEditStart", "onRowEditStop", "Cell", "editingState", "state", "isEditing", "Object", "keys", "updateRowModesModel", "newModel", "isNewModelDifferentFromProp", "updateRowInRowModesModel", "newProps", "updateOrDeleteRowState", "setState", "newEditingState", "editRows", "forceUpdate", "updateOrDeleteFieldState", "other", "updateStateToStartRowEditMode", "initialValue", "reduce", "acc", "newValue", "getCellValue", "value", "error", "isProcessingProps", "updateStateToStopRowEditMode", "focusedField", "finishRowEditMode", "moveFocusToRelativeCell", "row", "isSomeFieldProcessingProps", "values", "some", "fieldProps", "hasSomeFieldWithError", "rowUpdate", "getRowWithUpdatedValuesFromRowEditing", "handleError", "errorThrown", "Promise", "resolve", "then", "finalRowUpdate", "updateRows", "catch", "setRowEditingEditCellValue", "debounceMs", "unstable_skip<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "parsedValue", "valueParser", "changeReason", "preProcessEditCellProps", "promises", "has<PERSON><PERSON>ed", "_editingState$id", "otherFieldsProps", "map", "promise", "processedProps", "push", "entries", "for<PERSON>ach", "thisField", "fieldColumn", "_editingState$id2", "all", "valueSetter", "editingApi", "editingPrivateApi", "idToIdLookup", "copyOfPrevRowModesModel", "_copyOfPrevRowModesMo", "_idToIdLookup$id", "prevMode", "originalId"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/x-data-grid/hooks/features/editing/useGridRowEditing.js"], "sourcesContent": ["import _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"id\"],\n  _excluded2 = [\"id\"];\nimport * as React from 'react';\nimport { unstable_useEventCallback as useEventCallback, unstable_useEnhancedEffect as useEnhancedEffect } from '@mui/utils';\nimport { useGridApiEventHandler, useGridApiOptionHandler } from '../../utils/useGridApiEventHandler';\nimport { GridEditModes, GridRowModes } from '../../../models/gridEditRowModel';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nimport { gridEditRowsStateSelector } from './gridEditingSelectors';\nimport { isPrintableKey } from '../../../utils/keyboardUtils';\nimport { gridColumnFieldsSelector, gridVisibleColumnFieldsSelector } from '../columns/gridColumnsSelector';\nimport { buildWarning } from '../../../utils/warning';\nimport { gridRowsDataRowIdToIdLookupSelector } from '../rows/gridRowsSelector';\nimport { deepClone } from '../../../utils/utils';\nimport { GridRowEditStopReasons, GridRowEditStartReasons } from '../../../models/params/gridRowParams';\nimport { GRID_ACTIONS_COLUMN_TYPE } from '../../../colDef';\nconst missingOnProcessRowUpdateErrorWarning = buildWarning(['MUI: A call to `processRowUpdate` threw an error which was not handled because `onProcessRowUpdateError` is missing.', 'To handle the error pass a callback to the `onProcessRowUpdateError` prop, e.g. `<DataGrid onProcessRowUpdateError={(error) => ...} />`.', 'For more detail, see http://mui.com/components/data-grid/editing/#server-side-persistence.'], 'error');\nexport const useGridRowEditing = (apiRef, props) => {\n  const [rowModesModel, setRowModesModel] = React.useState({});\n  const rowModesModelRef = React.useRef(rowModesModel);\n  const prevRowModesModel = React.useRef({});\n  const focusTimeout = React.useRef(null);\n  const nextFocusedCell = React.useRef(null);\n  const {\n    processRowUpdate,\n    onProcessRowUpdateError,\n    rowModesModel: rowModesModelProp,\n    onRowModesModelChange\n  } = props;\n  const runIfEditModeIsRow = callback => (...args) => {\n    if (props.editMode === GridEditModes.Row) {\n      callback(...args);\n    }\n  };\n  const throwIfNotEditable = React.useCallback((id, field) => {\n    const params = apiRef.current.getCellParams(id, field);\n    if (!apiRef.current.isCellEditable(params)) {\n      throw new Error(`MUI: The cell with id=${id} and field=${field} is not editable.`);\n    }\n  }, [apiRef]);\n  const throwIfNotInMode = React.useCallback((id, mode) => {\n    if (apiRef.current.getRowMode(id) !== mode) {\n      throw new Error(`MUI: The row with id=${id} is not in ${mode} mode.`);\n    }\n  }, [apiRef]);\n  const handleCellDoubleClick = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (apiRef.current.getRowMode(params.id) === GridRowModes.Edit) {\n      return;\n    }\n    const rowParams = apiRef.current.getRowParams(params.id);\n    const newParams = _extends({}, rowParams, {\n      field: params.field,\n      reason: GridRowEditStartReasons.cellDoubleClick\n    });\n    apiRef.current.publishEvent('rowEditStart', newParams, event);\n  }, [apiRef]);\n  const handleCellFocusIn = React.useCallback(params => {\n    nextFocusedCell.current = params;\n  }, []);\n  const handleCellFocusOut = React.useCallback((params, event) => {\n    if (!params.isEditable) {\n      return;\n    }\n    if (apiRef.current.getRowMode(params.id) === GridRowModes.View) {\n      return;\n    }\n    // The mechanism to detect if we can stop editing a row is different from\n    // the cell editing. Instead of triggering it when clicking outside a cell,\n    // we must check if another cell in the same row was not clicked. To achieve\n    // that, first we keep track of all cells that gained focus. When a cell loses\n    // focus we check if the next cell that received focus is from a different row.\n    nextFocusedCell.current = null;\n    focusTimeout.current = setTimeout(() => {\n      var _nextFocusedCell$curr;\n      focusTimeout.current = null;\n      if (((_nextFocusedCell$curr = nextFocusedCell.current) == null ? void 0 : _nextFocusedCell$curr.id) !== params.id) {\n        // The row might have been deleted during the click\n        if (!apiRef.current.getRow(params.id)) {\n          return;\n        }\n\n        // The row may already changed its mode\n        if (apiRef.current.getRowMode(params.id) === GridRowModes.View) {\n          return;\n        }\n        const rowParams = apiRef.current.getRowParams(params.id);\n        const newParams = _extends({}, rowParams, {\n          field: params.field,\n          reason: GridRowEditStopReasons.rowFocusOut\n        });\n        apiRef.current.publishEvent('rowEditStop', newParams, event);\n      }\n    });\n  }, [apiRef]);\n  React.useEffect(() => {\n    return () => {\n      clearTimeout(focusTimeout.current);\n    };\n  }, []);\n  const handleCellKeyDown = React.useCallback((params, event) => {\n    if (params.cellMode === GridRowModes.Edit) {\n      // Wait until IME is settled for Asian languages like Japanese and Chinese\n      // TODO: `event.which` is deprecated but this is a temporary workaround\n      if (event.which === 229) {\n        return;\n      }\n      let reason;\n      if (event.key === 'Escape') {\n        reason = GridRowEditStopReasons.escapeKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridRowEditStopReasons.enterKeyDown;\n      } else if (event.key === 'Tab') {\n        const columnFields = gridVisibleColumnFieldsSelector(apiRef).filter(field => {\n          const column = apiRef.current.getColumn(field);\n          if (column.type === GRID_ACTIONS_COLUMN_TYPE) {\n            return true;\n          }\n          return apiRef.current.isCellEditable(apiRef.current.getCellParams(params.id, field));\n        });\n        if (event.shiftKey) {\n          if (params.field === columnFields[0]) {\n            // Exit if user pressed Shift+Tab on the first field\n            reason = GridRowEditStopReasons.shiftTabKeyDown;\n          }\n        } else if (params.field === columnFields[columnFields.length - 1]) {\n          // Exit if user pressed Tab on the last field\n          reason = GridRowEditStopReasons.tabKeyDown;\n        }\n\n        // Always prevent going to the next element in the tab sequence because the focus is\n        // handled manually to support edit components rendered inside Portals\n        event.preventDefault();\n        if (!reason) {\n          const index = columnFields.findIndex(field => field === params.field);\n          const nextFieldToFocus = columnFields[event.shiftKey ? index - 1 : index + 1];\n          apiRef.current.setCellFocus(params.id, nextFieldToFocus);\n        }\n      }\n      if (reason) {\n        const newParams = _extends({}, apiRef.current.getRowParams(params.id), {\n          reason,\n          field: params.field\n        });\n        apiRef.current.publishEvent('rowEditStop', newParams, event);\n      }\n    } else if (params.isEditable) {\n      let reason;\n      const canStartEditing = apiRef.current.unstable_applyPipeProcessors('canStartEditing', true, {\n        event,\n        cellParams: params,\n        editMode: 'row'\n      });\n      if (!canStartEditing) {\n        return;\n      }\n      if (isPrintableKey(event)) {\n        reason = GridRowEditStartReasons.printableKeyDown;\n      } else if ((event.ctrlKey || event.metaKey) && event.key === 'v') {\n        reason = GridRowEditStartReasons.printableKeyDown;\n      } else if (event.key === 'Enter') {\n        reason = GridRowEditStartReasons.enterKeyDown;\n      } else if (event.key === 'Delete' || event.key === 'Backspace') {\n        // Delete on Windows, Backspace on macOS\n        reason = GridRowEditStartReasons.deleteKeyDown;\n      }\n      if (reason) {\n        const rowParams = apiRef.current.getRowParams(params.id);\n        const newParams = _extends({}, rowParams, {\n          field: params.field,\n          reason\n        });\n        apiRef.current.publishEvent('rowEditStart', newParams, event);\n      }\n    }\n  }, [apiRef]);\n  const handleRowEditStart = React.useCallback(params => {\n    const {\n      id,\n      field,\n      reason\n    } = params;\n    const startRowEditModeParams = {\n      id,\n      fieldToFocus: field\n    };\n    if (reason === GridRowEditStartReasons.printableKeyDown || reason === GridRowEditStartReasons.deleteKeyDown) {\n      startRowEditModeParams.deleteValue = !!field;\n    }\n    apiRef.current.startRowEditMode(startRowEditModeParams);\n  }, [apiRef]);\n  const handleRowEditStop = React.useCallback(params => {\n    const {\n      id,\n      reason,\n      field\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id);\n    let cellToFocusAfter;\n    if (reason === GridRowEditStopReasons.enterKeyDown) {\n      cellToFocusAfter = 'below';\n    } else if (reason === GridRowEditStopReasons.tabKeyDown) {\n      cellToFocusAfter = 'right';\n    } else if (reason === GridRowEditStopReasons.shiftTabKeyDown) {\n      cellToFocusAfter = 'left';\n    }\n    const ignoreModifications = reason === 'escapeKeyDown';\n    apiRef.current.stopRowEditMode({\n      id,\n      ignoreModifications,\n      field,\n      cellToFocusAfter\n    });\n  }, [apiRef]);\n  useGridApiEventHandler(apiRef, 'cellDoubleClick', runIfEditModeIsRow(handleCellDoubleClick));\n  useGridApiEventHandler(apiRef, 'cellFocusIn', runIfEditModeIsRow(handleCellFocusIn));\n  useGridApiEventHandler(apiRef, 'cellFocusOut', runIfEditModeIsRow(handleCellFocusOut));\n  useGridApiEventHandler(apiRef, 'cellKeyDown', runIfEditModeIsRow(handleCellKeyDown));\n  useGridApiEventHandler(apiRef, 'rowEditStart', runIfEditModeIsRow(handleRowEditStart));\n  useGridApiEventHandler(apiRef, 'rowEditStop', runIfEditModeIsRow(handleRowEditStop));\n  useGridApiOptionHandler(apiRef, 'rowEditStart', props.onRowEditStart);\n  useGridApiOptionHandler(apiRef, 'rowEditStop', props.onRowEditStop);\n  const getRowMode = React.useCallback(id => {\n    if (props.editMode === GridEditModes.Cell) {\n      return GridRowModes.View;\n    }\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const isEditing = editingState[id] && Object.keys(editingState[id]).length > 0;\n    return isEditing ? GridRowModes.Edit : GridRowModes.View;\n  }, [apiRef, props.editMode]);\n  const updateRowModesModel = useEventCallback(newModel => {\n    const isNewModelDifferentFromProp = newModel !== props.rowModesModel;\n    if (onRowModesModelChange && isNewModelDifferentFromProp) {\n      onRowModesModelChange(newModel, {});\n    }\n    if (props.rowModesModel && isNewModelDifferentFromProp) {\n      return; // The prop always win\n    }\n    setRowModesModel(newModel);\n    rowModesModelRef.current = newModel;\n    apiRef.current.publishEvent('rowModesModelChange', newModel);\n  });\n  const updateRowInRowModesModel = React.useCallback((id, newProps) => {\n    const newModel = _extends({}, rowModesModelRef.current);\n    if (newProps !== null) {\n      newModel[id] = _extends({}, newProps);\n    } else {\n      delete newModel[id];\n    }\n    updateRowModesModel(newModel);\n  }, [updateRowModesModel]);\n  const updateOrDeleteRowState = React.useCallback((id, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = newProps;\n      } else {\n        delete newEditingState[id];\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const updateOrDeleteFieldState = React.useCallback((id, field, newProps) => {\n    apiRef.current.setState(state => {\n      const newEditingState = _extends({}, state.editRows);\n      if (newProps !== null) {\n        newEditingState[id] = _extends({}, newEditingState[id], {\n          [field]: _extends({}, newProps)\n        });\n      } else {\n        delete newEditingState[id][field];\n        if (Object.keys(newEditingState[id]).length === 0) {\n          delete newEditingState[id];\n        }\n      }\n      return _extends({}, state, {\n        editRows: newEditingState\n      });\n    });\n    apiRef.current.forceUpdate();\n  }, [apiRef]);\n  const startRowEditMode = React.useCallback(params => {\n    const {\n        id\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded);\n    throwIfNotInMode(id, GridRowModes.View);\n    updateRowInRowModesModel(id, _extends({\n      mode: GridRowModes.Edit\n    }, other));\n  }, [throwIfNotInMode, updateRowInRowModesModel]);\n  const updateStateToStartRowEditMode = useEventCallback(params => {\n    const {\n      id,\n      fieldToFocus,\n      deleteValue,\n      initialValue\n    } = params;\n    const columnFields = gridColumnFieldsSelector(apiRef);\n    const newProps = columnFields.reduce((acc, field) => {\n      const cellParams = apiRef.current.getCellParams(id, field);\n      if (!cellParams.isEditable) {\n        return acc;\n      }\n      let newValue = apiRef.current.getCellValue(id, field);\n      if (fieldToFocus === field && (deleteValue || initialValue)) {\n        newValue = deleteValue ? '' : initialValue;\n      }\n      acc[field] = {\n        value: newValue,\n        error: false,\n        isProcessingProps: false\n      };\n      return acc;\n    }, {});\n    updateOrDeleteRowState(id, newProps);\n    if (fieldToFocus) {\n      apiRef.current.setCellFocus(id, fieldToFocus);\n    }\n  });\n  const stopRowEditMode = React.useCallback(params => {\n    const {\n        id\n      } = params,\n      other = _objectWithoutPropertiesLoose(params, _excluded2);\n    throwIfNotInMode(id, GridRowModes.Edit);\n    updateRowInRowModesModel(id, _extends({\n      mode: GridRowModes.View\n    }, other));\n  }, [throwIfNotInMode, updateRowInRowModesModel]);\n  const updateStateToStopRowEditMode = useEventCallback(params => {\n    const {\n      id,\n      ignoreModifications,\n      field: focusedField,\n      cellToFocusAfter = 'none'\n    } = params;\n    apiRef.current.runPendingEditCellValueMutation(id);\n    const finishRowEditMode = () => {\n      if (cellToFocusAfter !== 'none' && focusedField) {\n        apiRef.current.moveFocusToRelativeCell(id, focusedField, cellToFocusAfter);\n      }\n      updateOrDeleteRowState(id, null);\n      updateRowInRowModesModel(id, null);\n    };\n    if (ignoreModifications) {\n      finishRowEditMode();\n      return;\n    }\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const row = apiRef.current.getRow(id);\n    const isSomeFieldProcessingProps = Object.values(editingState[id]).some(fieldProps => fieldProps.isProcessingProps);\n    if (isSomeFieldProcessingProps) {\n      prevRowModesModel.current[id].mode = GridRowModes.Edit;\n      return;\n    }\n    const hasSomeFieldWithError = Object.values(editingState[id]).some(fieldProps => fieldProps.error);\n    if (hasSomeFieldWithError) {\n      prevRowModesModel.current[id].mode = GridRowModes.Edit;\n      // Revert the mode in the rowModesModel prop back to \"edit\"\n      updateRowInRowModesModel(id, {\n        mode: GridRowModes.Edit\n      });\n      return;\n    }\n    const rowUpdate = apiRef.current.getRowWithUpdatedValuesFromRowEditing(id);\n    if (processRowUpdate) {\n      const handleError = errorThrown => {\n        prevRowModesModel.current[id].mode = GridRowModes.Edit;\n        // Revert the mode in the rowModesModel prop back to \"edit\"\n        updateRowInRowModesModel(id, {\n          mode: GridRowModes.Edit\n        });\n        if (onProcessRowUpdateError) {\n          onProcessRowUpdateError(errorThrown);\n        } else {\n          missingOnProcessRowUpdateErrorWarning();\n        }\n      };\n      try {\n        Promise.resolve(processRowUpdate(rowUpdate, row)).then(finalRowUpdate => {\n          apiRef.current.updateRows([finalRowUpdate]);\n          finishRowEditMode();\n        }).catch(handleError);\n      } catch (errorThrown) {\n        handleError(errorThrown);\n      }\n    } else {\n      apiRef.current.updateRows([rowUpdate]);\n      finishRowEditMode();\n    }\n  });\n  const setRowEditingEditCellValue = React.useCallback(params => {\n    const {\n      id,\n      field,\n      value,\n      debounceMs,\n      unstable_skipValueParser: skipValueParser\n    } = params;\n    throwIfNotEditable(id, field);\n    const column = apiRef.current.getColumn(field);\n    const row = apiRef.current.getRow(id);\n    let parsedValue = value;\n    if (column.valueParser && !skipValueParser) {\n      parsedValue = column.valueParser(value, apiRef.current.getCellParams(id, field));\n    }\n    let editingState = gridEditRowsStateSelector(apiRef.current.state);\n    let newProps = _extends({}, editingState[id][field], {\n      value: parsedValue,\n      changeReason: debounceMs ? 'debouncedSetEditCellValue' : 'setEditCellValue'\n    });\n    if (!column.preProcessEditCellProps) {\n      updateOrDeleteFieldState(id, field, newProps);\n    }\n    return new Promise(resolve => {\n      const promises = [];\n      if (column.preProcessEditCellProps) {\n        const hasChanged = newProps.value !== editingState[id][field].value;\n        newProps = _extends({}, newProps, {\n          isProcessingProps: true\n        });\n        updateOrDeleteFieldState(id, field, newProps);\n        const _editingState$id = editingState[id],\n          otherFieldsProps = _objectWithoutPropertiesLoose(_editingState$id, [field].map(_toPropertyKey));\n        const promise = Promise.resolve(column.preProcessEditCellProps({\n          id,\n          row,\n          props: newProps,\n          hasChanged,\n          otherFieldsProps\n        })).then(processedProps => {\n          // Check again if the row is in edit mode because the user may have\n          // discarded the changes while the props were being processed.\n          if (apiRef.current.getRowMode(id) === GridRowModes.View) {\n            resolve(false);\n            return;\n          }\n          editingState = gridEditRowsStateSelector(apiRef.current.state);\n          processedProps = _extends({}, processedProps, {\n            isProcessingProps: false\n          });\n          // We don't reuse the value from the props pre-processing because when the\n          // promise resolves it may be already outdated. The only exception to this rule\n          // is when there's no pre-processing.\n          processedProps.value = column.preProcessEditCellProps ? editingState[id][field].value : parsedValue;\n          updateOrDeleteFieldState(id, field, processedProps);\n        });\n        promises.push(promise);\n      }\n      Object.entries(editingState[id]).forEach(([thisField, fieldProps]) => {\n        if (thisField === field) {\n          return;\n        }\n        const fieldColumn = apiRef.current.getColumn(thisField);\n        if (!fieldColumn.preProcessEditCellProps) {\n          return;\n        }\n        fieldProps = _extends({}, fieldProps, {\n          isProcessingProps: true\n        });\n        updateOrDeleteFieldState(id, thisField, fieldProps);\n        editingState = gridEditRowsStateSelector(apiRef.current.state);\n        const _editingState$id2 = editingState[id],\n          otherFieldsProps = _objectWithoutPropertiesLoose(_editingState$id2, [thisField].map(_toPropertyKey));\n        const promise = Promise.resolve(fieldColumn.preProcessEditCellProps({\n          id,\n          row,\n          props: fieldProps,\n          hasChanged: false,\n          otherFieldsProps\n        })).then(processedProps => {\n          // Check again if the row is in edit mode because the user may have\n          // discarded the changes while the props were being processed.\n          if (apiRef.current.getRowMode(id) === GridRowModes.View) {\n            resolve(false);\n            return;\n          }\n          processedProps = _extends({}, processedProps, {\n            isProcessingProps: false\n          });\n          updateOrDeleteFieldState(id, thisField, processedProps);\n        });\n        promises.push(promise);\n      });\n      Promise.all(promises).then(() => {\n        if (apiRef.current.getRowMode(id) === GridRowModes.Edit) {\n          editingState = gridEditRowsStateSelector(apiRef.current.state);\n          resolve(!editingState[id][field].error);\n        } else {\n          resolve(false);\n        }\n      });\n    });\n  }, [apiRef, throwIfNotEditable, updateOrDeleteFieldState]);\n  const getRowWithUpdatedValuesFromRowEditing = React.useCallback(id => {\n    const editingState = gridEditRowsStateSelector(apiRef.current.state);\n    const row = apiRef.current.getRow(id);\n    if (!editingState[id]) {\n      return apiRef.current.getRow(id);\n    }\n    let rowUpdate = _extends({}, row);\n    Object.entries(editingState[id]).forEach(([field, fieldProps]) => {\n      const column = apiRef.current.getColumn(field);\n      if (column.valueSetter) {\n        rowUpdate = column.valueSetter({\n          value: fieldProps.value,\n          row: rowUpdate\n        });\n      } else {\n        rowUpdate[field] = fieldProps.value;\n      }\n    });\n    return rowUpdate;\n  }, [apiRef]);\n  const editingApi = {\n    getRowMode,\n    startRowEditMode,\n    stopRowEditMode\n  };\n  const editingPrivateApi = {\n    setRowEditingEditCellValue,\n    getRowWithUpdatedValuesFromRowEditing\n  };\n  useGridApiMethod(apiRef, editingApi, 'public');\n  useGridApiMethod(apiRef, editingPrivateApi, 'private');\n  React.useEffect(() => {\n    if (rowModesModelProp) {\n      updateRowModesModel(rowModesModelProp);\n    }\n  }, [rowModesModelProp, updateRowModesModel]);\n\n  // Run this effect synchronously so that the keyboard event can impact the yet-to-be-rendered input.\n  useEnhancedEffect(() => {\n    const idToIdLookup = gridRowsDataRowIdToIdLookupSelector(apiRef);\n\n    // Update the ref here because updateStateToStopRowEditMode may change it later\n    const copyOfPrevRowModesModel = prevRowModesModel.current;\n    prevRowModesModel.current = deepClone(rowModesModel); // Do a deep-clone because the attributes might be changed later\n\n    Object.entries(rowModesModel).forEach(([id, params]) => {\n      var _copyOfPrevRowModesMo, _idToIdLookup$id;\n      const prevMode = ((_copyOfPrevRowModesMo = copyOfPrevRowModesModel[id]) == null ? void 0 : _copyOfPrevRowModesMo.mode) || GridRowModes.View;\n      const originalId = (_idToIdLookup$id = idToIdLookup[id]) != null ? _idToIdLookup$id : id;\n      if (params.mode === GridRowModes.Edit && prevMode === GridRowModes.View) {\n        updateStateToStartRowEditMode(_extends({\n          id: originalId\n        }, params));\n      } else if (params.mode === GridRowModes.View && prevMode === GridRowModes.Edit) {\n        updateStateToStopRowEditMode(_extends({\n          id: originalId\n        }, params));\n      }\n    });\n  }, [apiRef, rowModesModel, updateStateToStartRowEditMode, updateStateToStopRowEditMode]);\n};"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,IAAI,CAAC;EACtBC,UAAU,GAAG,CAAC,IAAI,CAAC;AACrB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,yBAAyB,IAAIC,gBAAgB,EAAEC,0BAA0B,IAAIC,iBAAiB,QAAQ,YAAY;AAC3H,SAASC,sBAAsB,EAAEC,uBAAuB,QAAQ,oCAAoC;AACpG,SAASC,aAAa,EAAEC,YAAY,QAAQ,kCAAkC;AAC9E,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,SAASC,yBAAyB,QAAQ,wBAAwB;AAClE,SAASC,cAAc,QAAQ,8BAA8B;AAC7D,SAASC,wBAAwB,EAAEC,+BAA+B,QAAQ,gCAAgC;AAC1G,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,mCAAmC,QAAQ,0BAA0B;AAC9E,SAASC,SAAS,QAAQ,sBAAsB;AAChD,SAASC,sBAAsB,EAAEC,uBAAuB,QAAQ,sCAAsC;AACtG,SAASC,wBAAwB,QAAQ,iBAAiB;AAC1D,MAAMC,qCAAqC,GAAGN,YAAY,CAAC,CAAC,sHAAsH,EAAE,0IAA0I,EAAE,4FAA4F,CAAC,EAAE,OAAO,CAAC;AACva,OAAO,MAAMO,iBAAiB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EAClD,MAAM,CAACC,aAAa,EAAEC,gBAAgB,CAAC,GAAGzB,KAAK,CAAC0B,QAAQ,CAAC,CAAC,CAAC,CAAC;EAC5D,MAAMC,gBAAgB,GAAG3B,KAAK,CAAC4B,MAAM,CAACJ,aAAa,CAAC;EACpD,MAAMK,iBAAiB,GAAG7B,KAAK,CAAC4B,MAAM,CAAC,CAAC,CAAC,CAAC;EAC1C,MAAME,YAAY,GAAG9B,KAAK,CAAC4B,MAAM,CAAC,IAAI,CAAC;EACvC,MAAMG,eAAe,GAAG/B,KAAK,CAAC4B,MAAM,CAAC,IAAI,CAAC;EAC1C,MAAM;IACJI,gBAAgB;IAChBC,uBAAuB;IACvBT,aAAa,EAAEU,iBAAiB;IAChCC;EACF,CAAC,GAAGZ,KAAK;EACT,MAAMa,kBAAkB,GAAGC,QAAQ,IAAI,CAAC,GAAGC,IAAI,KAAK;IAClD,IAAIf,KAAK,CAACgB,QAAQ,KAAKhC,aAAa,CAACiC,GAAG,EAAE;MACxCH,QAAQ,CAAC,GAAGC,IAAI,CAAC;IACnB;EACF,CAAC;EACD,MAAMG,kBAAkB,GAAGzC,KAAK,CAAC0C,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,KAAK;IAC1D,MAAMC,MAAM,GAAGvB,MAAM,CAACwB,OAAO,CAACC,aAAa,CAACJ,EAAE,EAAEC,KAAK,CAAC;IACtD,IAAI,CAACtB,MAAM,CAACwB,OAAO,CAACE,cAAc,CAACH,MAAM,CAAC,EAAE;MAC1C,MAAM,IAAII,KAAK,CAAC,yBAAyBN,EAAE,cAAcC,KAAK,mBAAmB,CAAC;IACpF;EACF,CAAC,EAAE,CAACtB,MAAM,CAAC,CAAC;EACZ,MAAM4B,gBAAgB,GAAGlD,KAAK,CAAC0C,WAAW,CAAC,CAACC,EAAE,EAAEQ,IAAI,KAAK;IACvD,IAAI7B,MAAM,CAACwB,OAAO,CAACM,UAAU,CAACT,EAAE,CAAC,KAAKQ,IAAI,EAAE;MAC1C,MAAM,IAAIF,KAAK,CAAC,wBAAwBN,EAAE,cAAcQ,IAAI,QAAQ,CAAC;IACvE;EACF,CAAC,EAAE,CAAC7B,MAAM,CAAC,CAAC;EACZ,MAAM+B,qBAAqB,GAAGrD,KAAK,CAAC0C,WAAW,CAAC,CAACG,MAAM,EAAES,KAAK,KAAK;IACjE,IAAI,CAACT,MAAM,CAACU,UAAU,EAAE;MACtB;IACF;IACA,IAAIjC,MAAM,CAACwB,OAAO,CAACM,UAAU,CAACP,MAAM,CAACF,EAAE,CAAC,KAAKnC,YAAY,CAACgD,IAAI,EAAE;MAC9D;IACF;IACA,MAAMC,SAAS,GAAGnC,MAAM,CAACwB,OAAO,CAACY,YAAY,CAACb,MAAM,CAACF,EAAE,CAAC;IACxD,MAAMgB,SAAS,GAAG9D,QAAQ,CAAC,CAAC,CAAC,EAAE4D,SAAS,EAAE;MACxCb,KAAK,EAAEC,MAAM,CAACD,KAAK;MACnBgB,MAAM,EAAE1C,uBAAuB,CAAC2C;IAClC,CAAC,CAAC;IACFvC,MAAM,CAACwB,OAAO,CAACgB,YAAY,CAAC,cAAc,EAAEH,SAAS,EAAEL,KAAK,CAAC;EAC/D,CAAC,EAAE,CAAChC,MAAM,CAAC,CAAC;EACZ,MAAMyC,iBAAiB,GAAG/D,KAAK,CAAC0C,WAAW,CAACG,MAAM,IAAI;IACpDd,eAAe,CAACe,OAAO,GAAGD,MAAM;EAClC,CAAC,EAAE,EAAE,CAAC;EACN,MAAMmB,kBAAkB,GAAGhE,KAAK,CAAC0C,WAAW,CAAC,CAACG,MAAM,EAAES,KAAK,KAAK;IAC9D,IAAI,CAACT,MAAM,CAACU,UAAU,EAAE;MACtB;IACF;IACA,IAAIjC,MAAM,CAACwB,OAAO,CAACM,UAAU,CAACP,MAAM,CAACF,EAAE,CAAC,KAAKnC,YAAY,CAACyD,IAAI,EAAE;MAC9D;IACF;IACA;IACA;IACA;IACA;IACA;IACAlC,eAAe,CAACe,OAAO,GAAG,IAAI;IAC9BhB,YAAY,CAACgB,OAAO,GAAGoB,UAAU,CAAC,MAAM;MACtC,IAAIC,qBAAqB;MACzBrC,YAAY,CAACgB,OAAO,GAAG,IAAI;MAC3B,IAAI,CAAC,CAACqB,qBAAqB,GAAGpC,eAAe,CAACe,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGqB,qBAAqB,CAACxB,EAAE,MAAME,MAAM,CAACF,EAAE,EAAE;QACjH;QACA,IAAI,CAACrB,MAAM,CAACwB,OAAO,CAACsB,MAAM,CAACvB,MAAM,CAACF,EAAE,CAAC,EAAE;UACrC;QACF;;QAEA;QACA,IAAIrB,MAAM,CAACwB,OAAO,CAACM,UAAU,CAACP,MAAM,CAACF,EAAE,CAAC,KAAKnC,YAAY,CAACyD,IAAI,EAAE;UAC9D;QACF;QACA,MAAMR,SAAS,GAAGnC,MAAM,CAACwB,OAAO,CAACY,YAAY,CAACb,MAAM,CAACF,EAAE,CAAC;QACxD,MAAMgB,SAAS,GAAG9D,QAAQ,CAAC,CAAC,CAAC,EAAE4D,SAAS,EAAE;UACxCb,KAAK,EAAEC,MAAM,CAACD,KAAK;UACnBgB,MAAM,EAAE3C,sBAAsB,CAACoD;QACjC,CAAC,CAAC;QACF/C,MAAM,CAACwB,OAAO,CAACgB,YAAY,CAAC,aAAa,EAAEH,SAAS,EAAEL,KAAK,CAAC;MAC9D;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAAChC,MAAM,CAAC,CAAC;EACZtB,KAAK,CAACsE,SAAS,CAAC,MAAM;IACpB,OAAO,MAAM;MACXC,YAAY,CAACzC,YAAY,CAACgB,OAAO,CAAC;IACpC,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EACN,MAAM0B,iBAAiB,GAAGxE,KAAK,CAAC0C,WAAW,CAAC,CAACG,MAAM,EAAES,KAAK,KAAK;IAC7D,IAAIT,MAAM,CAAC4B,QAAQ,KAAKjE,YAAY,CAACgD,IAAI,EAAE;MACzC;MACA;MACA,IAAIF,KAAK,CAACoB,KAAK,KAAK,GAAG,EAAE;QACvB;MACF;MACA,IAAId,MAAM;MACV,IAAIN,KAAK,CAACqB,GAAG,KAAK,QAAQ,EAAE;QAC1Bf,MAAM,GAAG3C,sBAAsB,CAAC2D,aAAa;MAC/C,CAAC,MAAM,IAAItB,KAAK,CAACqB,GAAG,KAAK,OAAO,EAAE;QAChCf,MAAM,GAAG3C,sBAAsB,CAAC4D,YAAY;MAC9C,CAAC,MAAM,IAAIvB,KAAK,CAACqB,GAAG,KAAK,KAAK,EAAE;QAC9B,MAAMG,YAAY,GAAGjE,+BAA+B,CAACS,MAAM,CAAC,CAACyD,MAAM,CAACnC,KAAK,IAAI;UAC3E,MAAMoC,MAAM,GAAG1D,MAAM,CAACwB,OAAO,CAACmC,SAAS,CAACrC,KAAK,CAAC;UAC9C,IAAIoC,MAAM,CAACE,IAAI,KAAK/D,wBAAwB,EAAE;YAC5C,OAAO,IAAI;UACb;UACA,OAAOG,MAAM,CAACwB,OAAO,CAACE,cAAc,CAAC1B,MAAM,CAACwB,OAAO,CAACC,aAAa,CAACF,MAAM,CAACF,EAAE,EAAEC,KAAK,CAAC,CAAC;QACtF,CAAC,CAAC;QACF,IAAIU,KAAK,CAAC6B,QAAQ,EAAE;UAClB,IAAItC,MAAM,CAACD,KAAK,KAAKkC,YAAY,CAAC,CAAC,CAAC,EAAE;YACpC;YACAlB,MAAM,GAAG3C,sBAAsB,CAACmE,eAAe;UACjD;QACF,CAAC,MAAM,IAAIvC,MAAM,CAACD,KAAK,KAAKkC,YAAY,CAACA,YAAY,CAACO,MAAM,GAAG,CAAC,CAAC,EAAE;UACjE;UACAzB,MAAM,GAAG3C,sBAAsB,CAACqE,UAAU;QAC5C;;QAEA;QACA;QACAhC,KAAK,CAACiC,cAAc,CAAC,CAAC;QACtB,IAAI,CAAC3B,MAAM,EAAE;UACX,MAAM4B,KAAK,GAAGV,YAAY,CAACW,SAAS,CAAC7C,KAAK,IAAIA,KAAK,KAAKC,MAAM,CAACD,KAAK,CAAC;UACrE,MAAM8C,gBAAgB,GAAGZ,YAAY,CAACxB,KAAK,CAAC6B,QAAQ,GAAGK,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,CAAC;UAC7ElE,MAAM,CAACwB,OAAO,CAAC6C,YAAY,CAAC9C,MAAM,CAACF,EAAE,EAAE+C,gBAAgB,CAAC;QAC1D;MACF;MACA,IAAI9B,MAAM,EAAE;QACV,MAAMD,SAAS,GAAG9D,QAAQ,CAAC,CAAC,CAAC,EAAEyB,MAAM,CAACwB,OAAO,CAACY,YAAY,CAACb,MAAM,CAACF,EAAE,CAAC,EAAE;UACrEiB,MAAM;UACNhB,KAAK,EAAEC,MAAM,CAACD;QAChB,CAAC,CAAC;QACFtB,MAAM,CAACwB,OAAO,CAACgB,YAAY,CAAC,aAAa,EAAEH,SAAS,EAAEL,KAAK,CAAC;MAC9D;IACF,CAAC,MAAM,IAAIT,MAAM,CAACU,UAAU,EAAE;MAC5B,IAAIK,MAAM;MACV,MAAMgC,eAAe,GAAGtE,MAAM,CAACwB,OAAO,CAAC+C,4BAA4B,CAAC,iBAAiB,EAAE,IAAI,EAAE;QAC3FvC,KAAK;QACLwC,UAAU,EAAEjD,MAAM;QAClBN,QAAQ,EAAE;MACZ,CAAC,CAAC;MACF,IAAI,CAACqD,eAAe,EAAE;QACpB;MACF;MACA,IAAIjF,cAAc,CAAC2C,KAAK,CAAC,EAAE;QACzBM,MAAM,GAAG1C,uBAAuB,CAAC6E,gBAAgB;MACnD,CAAC,MAAM,IAAI,CAACzC,KAAK,CAAC0C,OAAO,IAAI1C,KAAK,CAAC2C,OAAO,KAAK3C,KAAK,CAACqB,GAAG,KAAK,GAAG,EAAE;QAChEf,MAAM,GAAG1C,uBAAuB,CAAC6E,gBAAgB;MACnD,CAAC,MAAM,IAAIzC,KAAK,CAACqB,GAAG,KAAK,OAAO,EAAE;QAChCf,MAAM,GAAG1C,uBAAuB,CAAC2D,YAAY;MAC/C,CAAC,MAAM,IAAIvB,KAAK,CAACqB,GAAG,KAAK,QAAQ,IAAIrB,KAAK,CAACqB,GAAG,KAAK,WAAW,EAAE;QAC9D;QACAf,MAAM,GAAG1C,uBAAuB,CAACgF,aAAa;MAChD;MACA,IAAItC,MAAM,EAAE;QACV,MAAMH,SAAS,GAAGnC,MAAM,CAACwB,OAAO,CAACY,YAAY,CAACb,MAAM,CAACF,EAAE,CAAC;QACxD,MAAMgB,SAAS,GAAG9D,QAAQ,CAAC,CAAC,CAAC,EAAE4D,SAAS,EAAE;UACxCb,KAAK,EAAEC,MAAM,CAACD,KAAK;UACnBgB;QACF,CAAC,CAAC;QACFtC,MAAM,CAACwB,OAAO,CAACgB,YAAY,CAAC,cAAc,EAAEH,SAAS,EAAEL,KAAK,CAAC;MAC/D;IACF;EACF,CAAC,EAAE,CAAChC,MAAM,CAAC,CAAC;EACZ,MAAM6E,kBAAkB,GAAGnG,KAAK,CAAC0C,WAAW,CAACG,MAAM,IAAI;IACrD,MAAM;MACJF,EAAE;MACFC,KAAK;MACLgB;IACF,CAAC,GAAGf,MAAM;IACV,MAAMuD,sBAAsB,GAAG;MAC7BzD,EAAE;MACF0D,YAAY,EAAEzD;IAChB,CAAC;IACD,IAAIgB,MAAM,KAAK1C,uBAAuB,CAAC6E,gBAAgB,IAAInC,MAAM,KAAK1C,uBAAuB,CAACgF,aAAa,EAAE;MAC3GE,sBAAsB,CAACE,WAAW,GAAG,CAAC,CAAC1D,KAAK;IAC9C;IACAtB,MAAM,CAACwB,OAAO,CAACyD,gBAAgB,CAACH,sBAAsB,CAAC;EACzD,CAAC,EAAE,CAAC9E,MAAM,CAAC,CAAC;EACZ,MAAMkF,iBAAiB,GAAGxG,KAAK,CAAC0C,WAAW,CAACG,MAAM,IAAI;IACpD,MAAM;MACJF,EAAE;MACFiB,MAAM;MACNhB;IACF,CAAC,GAAGC,MAAM;IACVvB,MAAM,CAACwB,OAAO,CAAC2D,+BAA+B,CAAC9D,EAAE,CAAC;IAClD,IAAI+D,gBAAgB;IACpB,IAAI9C,MAAM,KAAK3C,sBAAsB,CAAC4D,YAAY,EAAE;MAClD6B,gBAAgB,GAAG,OAAO;IAC5B,CAAC,MAAM,IAAI9C,MAAM,KAAK3C,sBAAsB,CAACqE,UAAU,EAAE;MACvDoB,gBAAgB,GAAG,OAAO;IAC5B,CAAC,MAAM,IAAI9C,MAAM,KAAK3C,sBAAsB,CAACmE,eAAe,EAAE;MAC5DsB,gBAAgB,GAAG,MAAM;IAC3B;IACA,MAAMC,mBAAmB,GAAG/C,MAAM,KAAK,eAAe;IACtDtC,MAAM,CAACwB,OAAO,CAAC8D,eAAe,CAAC;MAC7BjE,EAAE;MACFgE,mBAAmB;MACnB/D,KAAK;MACL8D;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACpF,MAAM,CAAC,CAAC;EACZjB,sBAAsB,CAACiB,MAAM,EAAE,iBAAiB,EAAEc,kBAAkB,CAACiB,qBAAqB,CAAC,CAAC;EAC5FhD,sBAAsB,CAACiB,MAAM,EAAE,aAAa,EAAEc,kBAAkB,CAAC2B,iBAAiB,CAAC,CAAC;EACpF1D,sBAAsB,CAACiB,MAAM,EAAE,cAAc,EAAEc,kBAAkB,CAAC4B,kBAAkB,CAAC,CAAC;EACtF3D,sBAAsB,CAACiB,MAAM,EAAE,aAAa,EAAEc,kBAAkB,CAACoC,iBAAiB,CAAC,CAAC;EACpFnE,sBAAsB,CAACiB,MAAM,EAAE,cAAc,EAAEc,kBAAkB,CAAC+D,kBAAkB,CAAC,CAAC;EACtF9F,sBAAsB,CAACiB,MAAM,EAAE,aAAa,EAAEc,kBAAkB,CAACoE,iBAAiB,CAAC,CAAC;EACpFlG,uBAAuB,CAACgB,MAAM,EAAE,cAAc,EAAEC,KAAK,CAACsF,cAAc,CAAC;EACrEvG,uBAAuB,CAACgB,MAAM,EAAE,aAAa,EAAEC,KAAK,CAACuF,aAAa,CAAC;EACnE,MAAM1D,UAAU,GAAGpD,KAAK,CAAC0C,WAAW,CAACC,EAAE,IAAI;IACzC,IAAIpB,KAAK,CAACgB,QAAQ,KAAKhC,aAAa,CAACwG,IAAI,EAAE;MACzC,OAAOvG,YAAY,CAACyD,IAAI;IAC1B;IACA,MAAM+C,YAAY,GAAGtG,yBAAyB,CAACY,MAAM,CAACwB,OAAO,CAACmE,KAAK,CAAC;IACpE,MAAMC,SAAS,GAAGF,YAAY,CAACrE,EAAE,CAAC,IAAIwE,MAAM,CAACC,IAAI,CAACJ,YAAY,CAACrE,EAAE,CAAC,CAAC,CAAC0C,MAAM,GAAG,CAAC;IAC9E,OAAO6B,SAAS,GAAG1G,YAAY,CAACgD,IAAI,GAAGhD,YAAY,CAACyD,IAAI;EAC1D,CAAC,EAAE,CAAC3C,MAAM,EAAEC,KAAK,CAACgB,QAAQ,CAAC,CAAC;EAC5B,MAAM8E,mBAAmB,GAAGnH,gBAAgB,CAACoH,QAAQ,IAAI;IACvD,MAAMC,2BAA2B,GAAGD,QAAQ,KAAK/F,KAAK,CAACC,aAAa;IACpE,IAAIW,qBAAqB,IAAIoF,2BAA2B,EAAE;MACxDpF,qBAAqB,CAACmF,QAAQ,EAAE,CAAC,CAAC,CAAC;IACrC;IACA,IAAI/F,KAAK,CAACC,aAAa,IAAI+F,2BAA2B,EAAE;MACtD,OAAO,CAAC;IACV;IACA9F,gBAAgB,CAAC6F,QAAQ,CAAC;IAC1B3F,gBAAgB,CAACmB,OAAO,GAAGwE,QAAQ;IACnChG,MAAM,CAACwB,OAAO,CAACgB,YAAY,CAAC,qBAAqB,EAAEwD,QAAQ,CAAC;EAC9D,CAAC,CAAC;EACF,MAAME,wBAAwB,GAAGxH,KAAK,CAAC0C,WAAW,CAAC,CAACC,EAAE,EAAE8E,QAAQ,KAAK;IACnE,MAAMH,QAAQ,GAAGzH,QAAQ,CAAC,CAAC,CAAC,EAAE8B,gBAAgB,CAACmB,OAAO,CAAC;IACvD,IAAI2E,QAAQ,KAAK,IAAI,EAAE;MACrBH,QAAQ,CAAC3E,EAAE,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC,EAAE4H,QAAQ,CAAC;IACvC,CAAC,MAAM;MACL,OAAOH,QAAQ,CAAC3E,EAAE,CAAC;IACrB;IACA0E,mBAAmB,CAACC,QAAQ,CAAC;EAC/B,CAAC,EAAE,CAACD,mBAAmB,CAAC,CAAC;EACzB,MAAMK,sBAAsB,GAAG1H,KAAK,CAAC0C,WAAW,CAAC,CAACC,EAAE,EAAE8E,QAAQ,KAAK;IACjEnG,MAAM,CAACwB,OAAO,CAAC6E,QAAQ,CAACV,KAAK,IAAI;MAC/B,MAAMW,eAAe,GAAG/H,QAAQ,CAAC,CAAC,CAAC,EAAEoH,KAAK,CAACY,QAAQ,CAAC;MACpD,IAAIJ,QAAQ,KAAK,IAAI,EAAE;QACrBG,eAAe,CAACjF,EAAE,CAAC,GAAG8E,QAAQ;MAChC,CAAC,MAAM;QACL,OAAOG,eAAe,CAACjF,EAAE,CAAC;MAC5B;MACA,OAAO9C,QAAQ,CAAC,CAAC,CAAC,EAAEoH,KAAK,EAAE;QACzBY,QAAQ,EAAED;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IACFtG,MAAM,CAACwB,OAAO,CAACgF,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACxG,MAAM,CAAC,CAAC;EACZ,MAAMyG,wBAAwB,GAAG/H,KAAK,CAAC0C,WAAW,CAAC,CAACC,EAAE,EAAEC,KAAK,EAAE6E,QAAQ,KAAK;IAC1EnG,MAAM,CAACwB,OAAO,CAAC6E,QAAQ,CAACV,KAAK,IAAI;MAC/B,MAAMW,eAAe,GAAG/H,QAAQ,CAAC,CAAC,CAAC,EAAEoH,KAAK,CAACY,QAAQ,CAAC;MACpD,IAAIJ,QAAQ,KAAK,IAAI,EAAE;QACrBG,eAAe,CAACjF,EAAE,CAAC,GAAG9C,QAAQ,CAAC,CAAC,CAAC,EAAE+H,eAAe,CAACjF,EAAE,CAAC,EAAE;UACtD,CAACC,KAAK,GAAG/C,QAAQ,CAAC,CAAC,CAAC,EAAE4H,QAAQ;QAChC,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,OAAOG,eAAe,CAACjF,EAAE,CAAC,CAACC,KAAK,CAAC;QACjC,IAAIuE,MAAM,CAACC,IAAI,CAACQ,eAAe,CAACjF,EAAE,CAAC,CAAC,CAAC0C,MAAM,KAAK,CAAC,EAAE;UACjD,OAAOuC,eAAe,CAACjF,EAAE,CAAC;QAC5B;MACF;MACA,OAAO9C,QAAQ,CAAC,CAAC,CAAC,EAAEoH,KAAK,EAAE;QACzBY,QAAQ,EAAED;MACZ,CAAC,CAAC;IACJ,CAAC,CAAC;IACFtG,MAAM,CAACwB,OAAO,CAACgF,WAAW,CAAC,CAAC;EAC9B,CAAC,EAAE,CAACxG,MAAM,CAAC,CAAC;EACZ,MAAMiF,gBAAgB,GAAGvG,KAAK,CAAC0C,WAAW,CAACG,MAAM,IAAI;IACnD,MAAM;QACFF;MACF,CAAC,GAAGE,MAAM;MACVmF,KAAK,GAAGpI,6BAA6B,CAACiD,MAAM,EAAE/C,SAAS,CAAC;IAC1DoD,gBAAgB,CAACP,EAAE,EAAEnC,YAAY,CAACyD,IAAI,CAAC;IACvCuD,wBAAwB,CAAC7E,EAAE,EAAE9C,QAAQ,CAAC;MACpCsD,IAAI,EAAE3C,YAAY,CAACgD;IACrB,CAAC,EAAEwE,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC9E,gBAAgB,EAAEsE,wBAAwB,CAAC,CAAC;EAChD,MAAMS,6BAA6B,GAAG/H,gBAAgB,CAAC2C,MAAM,IAAI;IAC/D,MAAM;MACJF,EAAE;MACF0D,YAAY;MACZC,WAAW;MACX4B;IACF,CAAC,GAAGrF,MAAM;IACV,MAAMiC,YAAY,GAAGlE,wBAAwB,CAACU,MAAM,CAAC;IACrD,MAAMmG,QAAQ,GAAG3C,YAAY,CAACqD,MAAM,CAAC,CAACC,GAAG,EAAExF,KAAK,KAAK;MACnD,MAAMkD,UAAU,GAAGxE,MAAM,CAACwB,OAAO,CAACC,aAAa,CAACJ,EAAE,EAAEC,KAAK,CAAC;MAC1D,IAAI,CAACkD,UAAU,CAACvC,UAAU,EAAE;QAC1B,OAAO6E,GAAG;MACZ;MACA,IAAIC,QAAQ,GAAG/G,MAAM,CAACwB,OAAO,CAACwF,YAAY,CAAC3F,EAAE,EAAEC,KAAK,CAAC;MACrD,IAAIyD,YAAY,KAAKzD,KAAK,KAAK0D,WAAW,IAAI4B,YAAY,CAAC,EAAE;QAC3DG,QAAQ,GAAG/B,WAAW,GAAG,EAAE,GAAG4B,YAAY;MAC5C;MACAE,GAAG,CAACxF,KAAK,CAAC,GAAG;QACX2F,KAAK,EAAEF,QAAQ;QACfG,KAAK,EAAE,KAAK;QACZC,iBAAiB,EAAE;MACrB,CAAC;MACD,OAAOL,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;IACNV,sBAAsB,CAAC/E,EAAE,EAAE8E,QAAQ,CAAC;IACpC,IAAIpB,YAAY,EAAE;MAChB/E,MAAM,CAACwB,OAAO,CAAC6C,YAAY,CAAChD,EAAE,EAAE0D,YAAY,CAAC;IAC/C;EACF,CAAC,CAAC;EACF,MAAMO,eAAe,GAAG5G,KAAK,CAAC0C,WAAW,CAACG,MAAM,IAAI;IAClD,MAAM;QACFF;MACF,CAAC,GAAGE,MAAM;MACVmF,KAAK,GAAGpI,6BAA6B,CAACiD,MAAM,EAAE9C,UAAU,CAAC;IAC3DmD,gBAAgB,CAACP,EAAE,EAAEnC,YAAY,CAACgD,IAAI,CAAC;IACvCgE,wBAAwB,CAAC7E,EAAE,EAAE9C,QAAQ,CAAC;MACpCsD,IAAI,EAAE3C,YAAY,CAACyD;IACrB,CAAC,EAAE+D,KAAK,CAAC,CAAC;EACZ,CAAC,EAAE,CAAC9E,gBAAgB,EAAEsE,wBAAwB,CAAC,CAAC;EAChD,MAAMkB,4BAA4B,GAAGxI,gBAAgB,CAAC2C,MAAM,IAAI;IAC9D,MAAM;MACJF,EAAE;MACFgE,mBAAmB;MACnB/D,KAAK,EAAE+F,YAAY;MACnBjC,gBAAgB,GAAG;IACrB,CAAC,GAAG7D,MAAM;IACVvB,MAAM,CAACwB,OAAO,CAAC2D,+BAA+B,CAAC9D,EAAE,CAAC;IAClD,MAAMiG,iBAAiB,GAAGA,CAAA,KAAM;MAC9B,IAAIlC,gBAAgB,KAAK,MAAM,IAAIiC,YAAY,EAAE;QAC/CrH,MAAM,CAACwB,OAAO,CAAC+F,uBAAuB,CAAClG,EAAE,EAAEgG,YAAY,EAAEjC,gBAAgB,CAAC;MAC5E;MACAgB,sBAAsB,CAAC/E,EAAE,EAAE,IAAI,CAAC;MAChC6E,wBAAwB,CAAC7E,EAAE,EAAE,IAAI,CAAC;IACpC,CAAC;IACD,IAAIgE,mBAAmB,EAAE;MACvBiC,iBAAiB,CAAC,CAAC;MACnB;IACF;IACA,MAAM5B,YAAY,GAAGtG,yBAAyB,CAACY,MAAM,CAACwB,OAAO,CAACmE,KAAK,CAAC;IACpE,MAAM6B,GAAG,GAAGxH,MAAM,CAACwB,OAAO,CAACsB,MAAM,CAACzB,EAAE,CAAC;IACrC,MAAMoG,0BAA0B,GAAG5B,MAAM,CAAC6B,MAAM,CAAChC,YAAY,CAACrE,EAAE,CAAC,CAAC,CAACsG,IAAI,CAACC,UAAU,IAAIA,UAAU,CAACT,iBAAiB,CAAC;IACnH,IAAIM,0BAA0B,EAAE;MAC9BlH,iBAAiB,CAACiB,OAAO,CAACH,EAAE,CAAC,CAACQ,IAAI,GAAG3C,YAAY,CAACgD,IAAI;MACtD;IACF;IACA,MAAM2F,qBAAqB,GAAGhC,MAAM,CAAC6B,MAAM,CAAChC,YAAY,CAACrE,EAAE,CAAC,CAAC,CAACsG,IAAI,CAACC,UAAU,IAAIA,UAAU,CAACV,KAAK,CAAC;IAClG,IAAIW,qBAAqB,EAAE;MACzBtH,iBAAiB,CAACiB,OAAO,CAACH,EAAE,CAAC,CAACQ,IAAI,GAAG3C,YAAY,CAACgD,IAAI;MACtD;MACAgE,wBAAwB,CAAC7E,EAAE,EAAE;QAC3BQ,IAAI,EAAE3C,YAAY,CAACgD;MACrB,CAAC,CAAC;MACF;IACF;IACA,MAAM4F,SAAS,GAAG9H,MAAM,CAACwB,OAAO,CAACuG,qCAAqC,CAAC1G,EAAE,CAAC;IAC1E,IAAIX,gBAAgB,EAAE;MACpB,MAAMsH,WAAW,GAAGC,WAAW,IAAI;QACjC1H,iBAAiB,CAACiB,OAAO,CAACH,EAAE,CAAC,CAACQ,IAAI,GAAG3C,YAAY,CAACgD,IAAI;QACtD;QACAgE,wBAAwB,CAAC7E,EAAE,EAAE;UAC3BQ,IAAI,EAAE3C,YAAY,CAACgD;QACrB,CAAC,CAAC;QACF,IAAIvB,uBAAuB,EAAE;UAC3BA,uBAAuB,CAACsH,WAAW,CAAC;QACtC,CAAC,MAAM;UACLnI,qCAAqC,CAAC,CAAC;QACzC;MACF,CAAC;MACD,IAAI;QACFoI,OAAO,CAACC,OAAO,CAACzH,gBAAgB,CAACoH,SAAS,EAAEN,GAAG,CAAC,CAAC,CAACY,IAAI,CAACC,cAAc,IAAI;UACvErI,MAAM,CAACwB,OAAO,CAAC8G,UAAU,CAAC,CAACD,cAAc,CAAC,CAAC;UAC3Cf,iBAAiB,CAAC,CAAC;QACrB,CAAC,CAAC,CAACiB,KAAK,CAACP,WAAW,CAAC;MACvB,CAAC,CAAC,OAAOC,WAAW,EAAE;QACpBD,WAAW,CAACC,WAAW,CAAC;MAC1B;IACF,CAAC,MAAM;MACLjI,MAAM,CAACwB,OAAO,CAAC8G,UAAU,CAAC,CAACR,SAAS,CAAC,CAAC;MACtCR,iBAAiB,CAAC,CAAC;IACrB;EACF,CAAC,CAAC;EACF,MAAMkB,0BAA0B,GAAG9J,KAAK,CAAC0C,WAAW,CAACG,MAAM,IAAI;IAC7D,MAAM;MACJF,EAAE;MACFC,KAAK;MACL2F,KAAK;MACLwB,UAAU;MACVC,wBAAwB,EAAEC;IAC5B,CAAC,GAAGpH,MAAM;IACVJ,kBAAkB,CAACE,EAAE,EAAEC,KAAK,CAAC;IAC7B,MAAMoC,MAAM,GAAG1D,MAAM,CAACwB,OAAO,CAACmC,SAAS,CAACrC,KAAK,CAAC;IAC9C,MAAMkG,GAAG,GAAGxH,MAAM,CAACwB,OAAO,CAACsB,MAAM,CAACzB,EAAE,CAAC;IACrC,IAAIuH,WAAW,GAAG3B,KAAK;IACvB,IAAIvD,MAAM,CAACmF,WAAW,IAAI,CAACF,eAAe,EAAE;MAC1CC,WAAW,GAAGlF,MAAM,CAACmF,WAAW,CAAC5B,KAAK,EAAEjH,MAAM,CAACwB,OAAO,CAACC,aAAa,CAACJ,EAAE,EAAEC,KAAK,CAAC,CAAC;IAClF;IACA,IAAIoE,YAAY,GAAGtG,yBAAyB,CAACY,MAAM,CAACwB,OAAO,CAACmE,KAAK,CAAC;IAClE,IAAIQ,QAAQ,GAAG5H,QAAQ,CAAC,CAAC,CAAC,EAAEmH,YAAY,CAACrE,EAAE,CAAC,CAACC,KAAK,CAAC,EAAE;MACnD2F,KAAK,EAAE2B,WAAW;MAClBE,YAAY,EAAEL,UAAU,GAAG,2BAA2B,GAAG;IAC3D,CAAC,CAAC;IACF,IAAI,CAAC/E,MAAM,CAACqF,uBAAuB,EAAE;MACnCtC,wBAAwB,CAACpF,EAAE,EAAEC,KAAK,EAAE6E,QAAQ,CAAC;IAC/C;IACA,OAAO,IAAI+B,OAAO,CAACC,OAAO,IAAI;MAC5B,MAAMa,QAAQ,GAAG,EAAE;MACnB,IAAItF,MAAM,CAACqF,uBAAuB,EAAE;QAClC,MAAME,UAAU,GAAG9C,QAAQ,CAACc,KAAK,KAAKvB,YAAY,CAACrE,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC2F,KAAK;QACnEd,QAAQ,GAAG5H,QAAQ,CAAC,CAAC,CAAC,EAAE4H,QAAQ,EAAE;UAChCgB,iBAAiB,EAAE;QACrB,CAAC,CAAC;QACFV,wBAAwB,CAACpF,EAAE,EAAEC,KAAK,EAAE6E,QAAQ,CAAC;QAC7C,MAAM+C,gBAAgB,GAAGxD,YAAY,CAACrE,EAAE,CAAC;UACvC8H,gBAAgB,GAAG7K,6BAA6B,CAAC4K,gBAAgB,EAAE,CAAC5H,KAAK,CAAC,CAAC8H,GAAG,CAAC/K,cAAc,CAAC,CAAC;QACjG,MAAMgL,OAAO,GAAGnB,OAAO,CAACC,OAAO,CAACzE,MAAM,CAACqF,uBAAuB,CAAC;UAC7D1H,EAAE;UACFmG,GAAG;UACHvH,KAAK,EAAEkG,QAAQ;UACf8C,UAAU;UACVE;QACF,CAAC,CAAC,CAAC,CAACf,IAAI,CAACkB,cAAc,IAAI;UACzB;UACA;UACA,IAAItJ,MAAM,CAACwB,OAAO,CAACM,UAAU,CAACT,EAAE,CAAC,KAAKnC,YAAY,CAACyD,IAAI,EAAE;YACvDwF,OAAO,CAAC,KAAK,CAAC;YACd;UACF;UACAzC,YAAY,GAAGtG,yBAAyB,CAACY,MAAM,CAACwB,OAAO,CAACmE,KAAK,CAAC;UAC9D2D,cAAc,GAAG/K,QAAQ,CAAC,CAAC,CAAC,EAAE+K,cAAc,EAAE;YAC5CnC,iBAAiB,EAAE;UACrB,CAAC,CAAC;UACF;UACA;UACA;UACAmC,cAAc,CAACrC,KAAK,GAAGvD,MAAM,CAACqF,uBAAuB,GAAGrD,YAAY,CAACrE,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC2F,KAAK,GAAG2B,WAAW;UACnGnC,wBAAwB,CAACpF,EAAE,EAAEC,KAAK,EAAEgI,cAAc,CAAC;QACrD,CAAC,CAAC;QACFN,QAAQ,CAACO,IAAI,CAACF,OAAO,CAAC;MACxB;MACAxD,MAAM,CAAC2D,OAAO,CAAC9D,YAAY,CAACrE,EAAE,CAAC,CAAC,CAACoI,OAAO,CAAC,CAAC,CAACC,SAAS,EAAE9B,UAAU,CAAC,KAAK;QACpE,IAAI8B,SAAS,KAAKpI,KAAK,EAAE;UACvB;QACF;QACA,MAAMqI,WAAW,GAAG3J,MAAM,CAACwB,OAAO,CAACmC,SAAS,CAAC+F,SAAS,CAAC;QACvD,IAAI,CAACC,WAAW,CAACZ,uBAAuB,EAAE;UACxC;QACF;QACAnB,UAAU,GAAGrJ,QAAQ,CAAC,CAAC,CAAC,EAAEqJ,UAAU,EAAE;UACpCT,iBAAiB,EAAE;QACrB,CAAC,CAAC;QACFV,wBAAwB,CAACpF,EAAE,EAAEqI,SAAS,EAAE9B,UAAU,CAAC;QACnDlC,YAAY,GAAGtG,yBAAyB,CAACY,MAAM,CAACwB,OAAO,CAACmE,KAAK,CAAC;QAC9D,MAAMiE,iBAAiB,GAAGlE,YAAY,CAACrE,EAAE,CAAC;UACxC8H,gBAAgB,GAAG7K,6BAA6B,CAACsL,iBAAiB,EAAE,CAACF,SAAS,CAAC,CAACN,GAAG,CAAC/K,cAAc,CAAC,CAAC;QACtG,MAAMgL,OAAO,GAAGnB,OAAO,CAACC,OAAO,CAACwB,WAAW,CAACZ,uBAAuB,CAAC;UAClE1H,EAAE;UACFmG,GAAG;UACHvH,KAAK,EAAE2H,UAAU;UACjBqB,UAAU,EAAE,KAAK;UACjBE;QACF,CAAC,CAAC,CAAC,CAACf,IAAI,CAACkB,cAAc,IAAI;UACzB;UACA;UACA,IAAItJ,MAAM,CAACwB,OAAO,CAACM,UAAU,CAACT,EAAE,CAAC,KAAKnC,YAAY,CAACyD,IAAI,EAAE;YACvDwF,OAAO,CAAC,KAAK,CAAC;YACd;UACF;UACAmB,cAAc,GAAG/K,QAAQ,CAAC,CAAC,CAAC,EAAE+K,cAAc,EAAE;YAC5CnC,iBAAiB,EAAE;UACrB,CAAC,CAAC;UACFV,wBAAwB,CAACpF,EAAE,EAAEqI,SAAS,EAAEJ,cAAc,CAAC;QACzD,CAAC,CAAC;QACFN,QAAQ,CAACO,IAAI,CAACF,OAAO,CAAC;MACxB,CAAC,CAAC;MACFnB,OAAO,CAAC2B,GAAG,CAACb,QAAQ,CAAC,CAACZ,IAAI,CAAC,MAAM;QAC/B,IAAIpI,MAAM,CAACwB,OAAO,CAACM,UAAU,CAACT,EAAE,CAAC,KAAKnC,YAAY,CAACgD,IAAI,EAAE;UACvDwD,YAAY,GAAGtG,yBAAyB,CAACY,MAAM,CAACwB,OAAO,CAACmE,KAAK,CAAC;UAC9DwC,OAAO,CAAC,CAACzC,YAAY,CAACrE,EAAE,CAAC,CAACC,KAAK,CAAC,CAAC4F,KAAK,CAAC;QACzC,CAAC,MAAM;UACLiB,OAAO,CAAC,KAAK,CAAC;QAChB;MACF,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ,CAAC,EAAE,CAACnI,MAAM,EAAEmB,kBAAkB,EAAEsF,wBAAwB,CAAC,CAAC;EAC1D,MAAMsB,qCAAqC,GAAGrJ,KAAK,CAAC0C,WAAW,CAACC,EAAE,IAAI;IACpE,MAAMqE,YAAY,GAAGtG,yBAAyB,CAACY,MAAM,CAACwB,OAAO,CAACmE,KAAK,CAAC;IACpE,MAAM6B,GAAG,GAAGxH,MAAM,CAACwB,OAAO,CAACsB,MAAM,CAACzB,EAAE,CAAC;IACrC,IAAI,CAACqE,YAAY,CAACrE,EAAE,CAAC,EAAE;MACrB,OAAOrB,MAAM,CAACwB,OAAO,CAACsB,MAAM,CAACzB,EAAE,CAAC;IAClC;IACA,IAAIyG,SAAS,GAAGvJ,QAAQ,CAAC,CAAC,CAAC,EAAEiJ,GAAG,CAAC;IACjC3B,MAAM,CAAC2D,OAAO,CAAC9D,YAAY,CAACrE,EAAE,CAAC,CAAC,CAACoI,OAAO,CAAC,CAAC,CAACnI,KAAK,EAAEsG,UAAU,CAAC,KAAK;MAChE,MAAMlE,MAAM,GAAG1D,MAAM,CAACwB,OAAO,CAACmC,SAAS,CAACrC,KAAK,CAAC;MAC9C,IAAIoC,MAAM,CAACoG,WAAW,EAAE;QACtBhC,SAAS,GAAGpE,MAAM,CAACoG,WAAW,CAAC;UAC7B7C,KAAK,EAAEW,UAAU,CAACX,KAAK;UACvBO,GAAG,EAAEM;QACP,CAAC,CAAC;MACJ,CAAC,MAAM;QACLA,SAAS,CAACxG,KAAK,CAAC,GAAGsG,UAAU,CAACX,KAAK;MACrC;IACF,CAAC,CAAC;IACF,OAAOa,SAAS;EAClB,CAAC,EAAE,CAAC9H,MAAM,CAAC,CAAC;EACZ,MAAM+J,UAAU,GAAG;IACjBjI,UAAU;IACVmD,gBAAgB;IAChBK;EACF,CAAC;EACD,MAAM0E,iBAAiB,GAAG;IACxBxB,0BAA0B;IAC1BT;EACF,CAAC;EACD5I,gBAAgB,CAACa,MAAM,EAAE+J,UAAU,EAAE,QAAQ,CAAC;EAC9C5K,gBAAgB,CAACa,MAAM,EAAEgK,iBAAiB,EAAE,SAAS,CAAC;EACtDtL,KAAK,CAACsE,SAAS,CAAC,MAAM;IACpB,IAAIpC,iBAAiB,EAAE;MACrBmF,mBAAmB,CAACnF,iBAAiB,CAAC;IACxC;EACF,CAAC,EAAE,CAACA,iBAAiB,EAAEmF,mBAAmB,CAAC,CAAC;;EAE5C;EACAjH,iBAAiB,CAAC,MAAM;IACtB,MAAMmL,YAAY,GAAGxK,mCAAmC,CAACO,MAAM,CAAC;;IAEhE;IACA,MAAMkK,uBAAuB,GAAG3J,iBAAiB,CAACiB,OAAO;IACzDjB,iBAAiB,CAACiB,OAAO,GAAG9B,SAAS,CAACQ,aAAa,CAAC,CAAC,CAAC;;IAEtD2F,MAAM,CAAC2D,OAAO,CAACtJ,aAAa,CAAC,CAACuJ,OAAO,CAAC,CAAC,CAACpI,EAAE,EAAEE,MAAM,CAAC,KAAK;MACtD,IAAI4I,qBAAqB,EAAEC,gBAAgB;MAC3C,MAAMC,QAAQ,GAAG,CAAC,CAACF,qBAAqB,GAAGD,uBAAuB,CAAC7I,EAAE,CAAC,KAAK,IAAI,GAAG,KAAK,CAAC,GAAG8I,qBAAqB,CAACtI,IAAI,KAAK3C,YAAY,CAACyD,IAAI;MAC3I,MAAM2H,UAAU,GAAG,CAACF,gBAAgB,GAAGH,YAAY,CAAC5I,EAAE,CAAC,KAAK,IAAI,GAAG+I,gBAAgB,GAAG/I,EAAE;MACxF,IAAIE,MAAM,CAACM,IAAI,KAAK3C,YAAY,CAACgD,IAAI,IAAImI,QAAQ,KAAKnL,YAAY,CAACyD,IAAI,EAAE;QACvEgE,6BAA6B,CAACpI,QAAQ,CAAC;UACrC8C,EAAE,EAAEiJ;QACN,CAAC,EAAE/I,MAAM,CAAC,CAAC;MACb,CAAC,MAAM,IAAIA,MAAM,CAACM,IAAI,KAAK3C,YAAY,CAACyD,IAAI,IAAI0H,QAAQ,KAAKnL,YAAY,CAACgD,IAAI,EAAE;QAC9EkF,4BAA4B,CAAC7I,QAAQ,CAAC;UACpC8C,EAAE,EAAEiJ;QACN,CAAC,EAAE/I,MAAM,CAAC,CAAC;MACb;IACF,CAAC,CAAC;EACJ,CAAC,EAAE,CAACvB,MAAM,EAAEE,aAAa,EAAEyG,6BAA6B,EAAES,4BAA4B,CAAC,CAAC;AAC1F,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}