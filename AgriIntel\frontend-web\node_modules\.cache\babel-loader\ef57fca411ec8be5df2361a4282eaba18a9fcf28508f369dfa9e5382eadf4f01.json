{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.CommaAndColonSeparatedRecord = exports.ConnectionString = exports.redactConnectionString = void 0;\nconst whatwg_url_1 = require(\"whatwg-url\");\nconst redact_1 = require(\"./redact\");\nObject.defineProperty(exports, \"redactConnectionString\", {\n  enumerable: true,\n  get: function () {\n    return redact_1.redactConnectionString;\n  }\n});\nconst DUMMY_HOSTNAME = '__this_is_a_placeholder__';\nfunction connectionStringHasValidScheme(connectionString) {\n  return connectionString.startsWith('mongodb://') || connectionString.startsWith('mongodb+srv://');\n}\nconst HOSTS_REGEX = /^(?<protocol>[^/]+):\\/\\/(?:(?<username>[^:@]*)(?::(?<password>[^@]*))?@)?(?<hosts>(?!:)[^/?@]*)(?<rest>.*)/;\nclass CaseInsensitiveMap extends Map {\n  delete(name) {\n    return super.delete(this._normalizeKey(name));\n  }\n  get(name) {\n    return super.get(this._normalizeKey(name));\n  }\n  has(name) {\n    return super.has(this._normalizeKey(name));\n  }\n  set(name, value) {\n    return super.set(this._normalizeKey(name), value);\n  }\n  _normalizeKey(name) {\n    name = `${name}`;\n    for (const key of this.keys()) {\n      if (key.toLowerCase() === name.toLowerCase()) {\n        name = key;\n        break;\n      }\n    }\n    return name;\n  }\n}\nfunction caseInsenstiveURLSearchParams(Ctor) {\n  return class CaseInsenstiveURLSearchParams extends Ctor {\n    append(name, value) {\n      return super.append(this._normalizeKey(name), value);\n    }\n    delete(name) {\n      return super.delete(this._normalizeKey(name));\n    }\n    get(name) {\n      return super.get(this._normalizeKey(name));\n    }\n    getAll(name) {\n      return super.getAll(this._normalizeKey(name));\n    }\n    has(name) {\n      return super.has(this._normalizeKey(name));\n    }\n    set(name, value) {\n      return super.set(this._normalizeKey(name), value);\n    }\n    keys() {\n      return super.keys();\n    }\n    values() {\n      return super.values();\n    }\n    entries() {\n      return super.entries();\n    }\n    [Symbol.iterator]() {\n      return super[Symbol.iterator]();\n    }\n    _normalizeKey(name) {\n      return CaseInsensitiveMap.prototype._normalizeKey.call(this, name);\n    }\n  };\n}\nclass URLWithoutHost extends whatwg_url_1.URL {}\nclass MongoParseError extends Error {\n  get name() {\n    return 'MongoParseError';\n  }\n}\nclass ConnectionString extends URLWithoutHost {\n  constructor(uri, options = {}) {\n    var _a;\n    const {\n      looseValidation\n    } = options;\n    if (!looseValidation && !connectionStringHasValidScheme(uri)) {\n      throw new MongoParseError('Invalid scheme, expected connection string to start with \"mongodb://\" or \"mongodb+srv://\"');\n    }\n    const match = uri.match(HOSTS_REGEX);\n    if (!match) {\n      throw new MongoParseError(`Invalid connection string \"${uri}\"`);\n    }\n    const {\n      protocol,\n      username,\n      password,\n      hosts,\n      rest\n    } = (_a = match.groups) !== null && _a !== void 0 ? _a : {};\n    if (!looseValidation) {\n      if (!protocol || !hosts) {\n        throw new MongoParseError(`Protocol and host list are required in \"${uri}\"`);\n      }\n      try {\n        decodeURIComponent(username !== null && username !== void 0 ? username : '');\n        decodeURIComponent(password !== null && password !== void 0 ? password : '');\n      } catch (err) {\n        throw new MongoParseError(err.message);\n      }\n      const illegalCharacters = /[:/?#[\\]@]/gi;\n      if (username === null || username === void 0 ? void 0 : username.match(illegalCharacters)) {\n        throw new MongoParseError(`Username contains unescaped characters ${username}`);\n      }\n      if (!username || !password) {\n        const uriWithoutProtocol = uri.replace(`${protocol}://`, '');\n        if (uriWithoutProtocol.startsWith('@') || uriWithoutProtocol.startsWith(':')) {\n          throw new MongoParseError('URI contained empty userinfo section');\n        }\n      }\n      if (password === null || password === void 0 ? void 0 : password.match(illegalCharacters)) {\n        throw new MongoParseError('Password contains unescaped characters');\n      }\n    }\n    let authString = '';\n    if (typeof username === 'string') authString += username;\n    if (typeof password === 'string') authString += `:${password}`;\n    if (authString) authString += '@';\n    try {\n      super(`${protocol.toLowerCase()}://${authString}${DUMMY_HOSTNAME}${rest}`);\n    } catch (err) {\n      if (looseValidation) {\n        new ConnectionString(uri, {\n          ...options,\n          looseValidation: false\n        });\n      }\n      if (typeof err.message === 'string') {\n        err.message = err.message.replace(DUMMY_HOSTNAME, hosts);\n      }\n      throw err;\n    }\n    this._hosts = hosts.split(',');\n    if (!looseValidation) {\n      if (this.isSRV && this.hosts.length !== 1) {\n        throw new MongoParseError('mongodb+srv URI cannot have multiple service names');\n      }\n      if (this.isSRV && this.hosts.some(host => host.includes(':'))) {\n        throw new MongoParseError('mongodb+srv URI cannot have port number');\n      }\n    }\n    if (!this.pathname) {\n      this.pathname = '/';\n    }\n    Object.setPrototypeOf(this.searchParams, caseInsenstiveURLSearchParams(this.searchParams.constructor).prototype);\n  }\n  get host() {\n    return DUMMY_HOSTNAME;\n  }\n  set host(_ignored) {\n    throw new Error('No single host for connection string');\n  }\n  get hostname() {\n    return DUMMY_HOSTNAME;\n  }\n  set hostname(_ignored) {\n    throw new Error('No single host for connection string');\n  }\n  get port() {\n    return '';\n  }\n  set port(_ignored) {\n    throw new Error('No single host for connection string');\n  }\n  get href() {\n    return this.toString();\n  }\n  set href(_ignored) {\n    throw new Error('Cannot set href for connection strings');\n  }\n  get isSRV() {\n    return this.protocol.includes('srv');\n  }\n  get hosts() {\n    return this._hosts;\n  }\n  set hosts(list) {\n    this._hosts = list;\n  }\n  toString() {\n    return super.toString().replace(DUMMY_HOSTNAME, this.hosts.join(','));\n  }\n  clone() {\n    return new ConnectionString(this.toString(), {\n      looseValidation: true\n    });\n  }\n  redact(options) {\n    return (0, redact_1.redactValidConnectionString)(this, options);\n  }\n  typedSearchParams() {\n    const sametype = false && new (caseInsenstiveURLSearchParams(whatwg_url_1.URLSearchParams))();\n    return this.searchParams;\n  }\n  [Symbol.for('nodejs.util.inspect.custom')]() {\n    const {\n      href,\n      origin,\n      protocol,\n      username,\n      password,\n      hosts,\n      pathname,\n      search,\n      searchParams,\n      hash\n    } = this;\n    return {\n      href,\n      origin,\n      protocol,\n      username,\n      password,\n      hosts,\n      pathname,\n      search,\n      searchParams,\n      hash\n    };\n  }\n}\nexports.ConnectionString = ConnectionString;\nclass CommaAndColonSeparatedRecord extends CaseInsensitiveMap {\n  constructor(from) {\n    super();\n    for (const entry of (from !== null && from !== void 0 ? from : '').split(',')) {\n      if (!entry) continue;\n      const colonIndex = entry.indexOf(':');\n      if (colonIndex === -1) {\n        this.set(entry, '');\n      } else {\n        this.set(entry.slice(0, colonIndex), entry.slice(colonIndex + 1));\n      }\n    }\n  }\n  toString() {\n    return [...this].map(entry => entry.join(':')).join(',');\n  }\n}\nexports.CommaAndColonSeparatedRecord = CommaAndColonSeparatedRecord;\nexports.default = ConnectionString;", "map": {"version": 3, "names": ["whatwg_url_1", "require", "redact_1", "Object", "defineProperty", "exports", "enumerable", "get", "redactConnectionString", "DUMMY_HOSTNAME", "connectionStringHasValidScheme", "connectionString", "startsWith", "HOSTS_REGEX", "CaseInsensitiveMap", "Map", "delete", "name", "_normalizeKey", "has", "set", "value", "key", "keys", "toLowerCase", "caseInsenstiveURLSearchParams", "Ctor", "CaseInsenstiveURLSearchParams", "append", "getAll", "values", "entries", "Symbol", "iterator", "prototype", "call", "URLWithoutHost", "URL", "MongoParseError", "Error", "ConnectionString", "constructor", "uri", "options", "looseValidation", "match", "protocol", "username", "password", "hosts", "rest", "_a", "groups", "decodeURIComponent", "err", "message", "illegalCharacters", "uriWithoutProtocol", "replace", "authString", "_hosts", "split", "isSRV", "length", "some", "host", "includes", "pathname", "setPrototypeOf", "searchParams", "_ignored", "hostname", "port", "href", "toString", "list", "join", "clone", "redact", "redactValidConnectionString", "typedSearchParams", "sametype", "URLSearchParams", "for", "origin", "search", "hash", "CommaAndColonSeparatedRecord", "from", "entry", "colonIndex", "indexOf", "slice", "map", "default"], "sources": ["../src/index.ts"], "sourcesContent": [null], "mappings": ";;;;;;AAAA,MAAAA,YAAA,GAAAC,OAAA;AACA,MAAAC,QAAA,GAAAD,OAAA;AAKSE,MAAA,CAAAC,cAAA,CAAAC,OAAA;EAAAC,UAAA;EAAAC,GAAA,WAAAA,CAAA;IAAA,OAHPL,QAAA,CAAAM,sBAAsB;EAAA;AAAA;AAKxB,MAAMC,cAAc,GAAG,2BAA2B;AAElD,SAASC,8BAA8BA,CAACC,gBAAwB;EAC9D,OACEA,gBAAgB,CAACC,UAAU,CAAC,YAAY,CAAC,IACzCD,gBAAgB,CAACC,UAAU,CAAC,gBAAgB,CAAC;AAEjD;AAIA,MAAMC,WAAW,GACf,4GAA4G;AAE9G,MAAMC,kBAA8C,SAAQC,GAAc;EACxEC,MAAMA,CAACC,IAAO;IACZ,OAAO,KAAK,CAACD,MAAM,CAAC,IAAI,CAACE,aAAa,CAACD,IAAI,CAAC,CAAC;EAC/C;EAEAV,GAAGA,CAACU,IAAO;IACT,OAAO,KAAK,CAACV,GAAG,CAAC,IAAI,CAACW,aAAa,CAACD,IAAI,CAAC,CAAC;EAC5C;EAEAE,GAAGA,CAACF,IAAO;IACT,OAAO,KAAK,CAACE,GAAG,CAAC,IAAI,CAACD,aAAa,CAACD,IAAI,CAAC,CAAC;EAC5C;EAEAG,GAAGA,CAACH,IAAO,EAAEI,KAAU;IACrB,OAAO,KAAK,CAACD,GAAG,CAAC,IAAI,CAACF,aAAa,CAACD,IAAI,CAAC,EAAEI,KAAK,CAAC;EACnD;EAEAH,aAAaA,CAACD,IAAS;IACrBA,IAAI,GAAG,GAAGA,IAAI,EAAE;IAChB,KAAK,MAAMK,GAAG,IAAI,IAAI,CAACC,IAAI,EAAE,EAAE;MAC7B,IAAID,GAAG,CAACE,WAAW,EAAE,KAAKP,IAAI,CAACO,WAAW,EAAE,EAAE;QAC5CP,IAAI,GAAGK,GAAG;QACV;;;IAGJ,OAAOL,IAAI;EACb;;AAGF,SAASQ,6BAA6BA,CAA4BC,IAA4B;EAC5F,OAAO,MAAMC,6BAA8B,SAAQD,IAAI;IACrDE,MAAMA,CAACX,IAAO,EAAEI,KAAU;MACxB,OAAO,KAAK,CAACO,MAAM,CAAC,IAAI,CAACV,aAAa,CAACD,IAAI,CAAC,EAAEI,KAAK,CAAC;IACtD;IAEAL,MAAMA,CAACC,IAAO;MACZ,OAAO,KAAK,CAACD,MAAM,CAAC,IAAI,CAACE,aAAa,CAACD,IAAI,CAAC,CAAC;IAC/C;IAEAV,GAAGA,CAACU,IAAO;MACT,OAAO,KAAK,CAACV,GAAG,CAAC,IAAI,CAACW,aAAa,CAACD,IAAI,CAAC,CAAC;IAC5C;IAEAY,MAAMA,CAACZ,IAAO;MACZ,OAAO,KAAK,CAACY,MAAM,CAAC,IAAI,CAACX,aAAa,CAACD,IAAI,CAAC,CAAC;IAC/C;IAEAE,GAAGA,CAACF,IAAO;MACT,OAAO,KAAK,CAACE,GAAG,CAAC,IAAI,CAACD,aAAa,CAACD,IAAI,CAAC,CAAC;IAC5C;IAEAG,GAAGA,CAACH,IAAO,EAAEI,KAAU;MACrB,OAAO,KAAK,CAACD,GAAG,CAAC,IAAI,CAACF,aAAa,CAACD,IAAI,CAAC,EAAEI,KAAK,CAAC;IACnD;IAEAE,IAAIA,CAAA;MACF,OAAO,KAAK,CAACA,IAAI,EAAyB;IAC5C;IAEAO,MAAMA,CAAA;MACJ,OAAO,KAAK,CAACA,MAAM,EAAE;IACvB;IAEAC,OAAOA,CAAA;MACL,OAAO,KAAK,CAACA,OAAO,EAAmC;IACzD;IAEA,CAACC,MAAM,CAACC,QAAQ,IAAC;MACf,OAAO,KAAK,CAACD,MAAM,CAACC,QAAQ,CAAC,EAAmC;IAClE;IAEAf,aAAaA,CAACD,IAAO;MACnB,OAAOH,kBAAkB,CAACoB,SAAS,CAAChB,aAAa,CAACiB,IAAI,CAAC,IAAI,EAAElB,IAAI,CAAC;IACpE;GACD;AACH;AAGA,MAAemB,cAAe,SAAQpC,YAAA,CAAAqC,GAAG;AAWzC,MAAMC,eAAgB,SAAQC,KAAK;EACjC,IAAItB,IAAIA,CAAA;IACN,OAAO,iBAAiB;EAC1B;;AAWF,MAAauB,gBAAiB,SAAQJ,cAAc;EAIlDK,YAAYC,GAAW,EAAEC,OAAA,GAA0C,EAAE;;IACnE,MAAM;MAAEC;IAAe,CAAE,GAAGD,OAAO;IACnC,IAAI,CAACC,eAAe,IAAI,CAAClC,8BAA8B,CAACgC,GAAG,CAAC,EAAE;MAC5D,MAAM,IAAIJ,eAAe,CAAC,2FAA2F,CAAC;;IAGxH,MAAMO,KAAK,GAAGH,GAAG,CAACG,KAAK,CAAChC,WAAW,CAAC;IACpC,IAAI,CAACgC,KAAK,EAAE;MACV,MAAM,IAAIP,eAAe,CAAC,8BAA8BI,GAAG,GAAG,CAAC;;IAGjE,MAAM;MAAEI,QAAQ;MAAEC,QAAQ;MAAEC,QAAQ;MAAEC,KAAK;MAAEC;IAAI,CAAE,GAAG,CAAAC,EAAA,GAAAN,KAAK,CAACO,MAAM,cAAAD,EAAA,cAAAA,EAAA,GAAI,EAAE;IAExE,IAAI,CAACP,eAAe,EAAE;MACpB,IAAI,CAACE,QAAQ,IAAI,CAACG,KAAK,EAAE;QACvB,MAAM,IAAIX,eAAe,CAAC,2CAA2CI,GAAG,GAAG,CAAC;;MAG9E,IAAI;QACFW,kBAAkB,CAACN,QAAQ,aAARA,QAAQ,cAARA,QAAQ,GAAI,EAAE,CAAC;QAClCM,kBAAkB,CAACL,QAAQ,aAARA,QAAQ,cAARA,QAAQ,GAAI,EAAE,CAAC;OACnC,CAAC,OAAOM,GAAG,EAAE;QACZ,MAAM,IAAIhB,eAAe,CAAEgB,GAAa,CAACC,OAAO,CAAC;;MAInD,MAAMC,iBAAiB,GAAG,cAAc;MACxC,IAAIT,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEF,KAAK,CAACW,iBAAiB,CAAC,EAAE;QACtC,MAAM,IAAIlB,eAAe,CAAC,0CAA0CS,QAAQ,EAAE,CAAC;;MAEjF,IAAI,CAACA,QAAQ,IAAI,CAACC,QAAQ,EAAE;QAC1B,MAAMS,kBAAkB,GAAGf,GAAG,CAACgB,OAAO,CAAC,GAAGZ,QAAQ,KAAK,EAAE,EAAE,CAAC;QAC5D,IAAIW,kBAAkB,CAAC7C,UAAU,CAAC,GAAG,CAAC,IAAI6C,kBAAkB,CAAC7C,UAAU,CAAC,GAAG,CAAC,EAAE;UAC5E,MAAM,IAAI0B,eAAe,CAAC,sCAAsC,CAAC;;;MAIrE,IAAIU,QAAQ,aAARA,QAAQ,uBAARA,QAAQ,CAAEH,KAAK,CAACW,iBAAiB,CAAC,EAAE;QACtC,MAAM,IAAIlB,eAAe,CAAC,wCAAwC,CAAC;;;IAIvE,IAAIqB,UAAU,GAAG,EAAE;IACnB,IAAI,OAAOZ,QAAQ,KAAK,QAAQ,EAAEY,UAAU,IAAIZ,QAAQ;IACxD,IAAI,OAAOC,QAAQ,KAAK,QAAQ,EAAEW,UAAU,IAAI,IAAIX,QAAQ,EAAE;IAC9D,IAAIW,UAAU,EAAEA,UAAU,IAAI,GAAG;IAEjC,IAAI;MACF,KAAK,CAAC,GAAGb,QAAQ,CAACtB,WAAW,EAAE,MAAMmC,UAAU,GAAGlD,cAAc,GAAGyC,IAAI,EAAE,CAAC;KAC3E,CAAC,OAAOI,GAAQ,EAAE;MACjB,IAAIV,eAAe,EAAE;QAInB,IAAIJ,gBAAgB,CAACE,GAAG,EAAE;UACxB,GAAGC,OAAO;UACVC,eAAe,EAAE;SAClB,CAAC;;MAEJ,IAAI,OAAOU,GAAG,CAACC,OAAO,KAAK,QAAQ,EAAE;QACnCD,GAAG,CAACC,OAAO,GAAGD,GAAG,CAACC,OAAO,CAACG,OAAO,CAACjD,cAAc,EAAEwC,KAAK,CAAC;;MAE1D,MAAMK,GAAG;;IAEX,IAAI,CAACM,MAAM,GAAGX,KAAK,CAACY,KAAK,CAAC,GAAG,CAAC;IAE9B,IAAI,CAACjB,eAAe,EAAE;MACpB,IAAI,IAAI,CAACkB,KAAK,IAAI,IAAI,CAACb,KAAK,CAACc,MAAM,KAAK,CAAC,EAAE;QACzC,MAAM,IAAIzB,eAAe,CAAC,oDAAoD,CAAC;;MAEjF,IAAI,IAAI,CAACwB,KAAK,IAAI,IAAI,CAACb,KAAK,CAACe,IAAI,CAACC,IAAI,IAAIA,IAAI,CAACC,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;QAC7D,MAAM,IAAI5B,eAAe,CAAC,yCAAyC,CAAC;;;IAIxE,IAAI,CAAC,IAAI,CAAC6B,QAAQ,EAAE;MAClB,IAAI,CAACA,QAAQ,GAAG,GAAG;;IAErBhE,MAAM,CAACiE,cAAc,CAAC,IAAI,CAACC,YAAY,EAAE5C,6BAA6B,CAAC,IAAI,CAAC4C,YAAY,CAAC5B,WAAkB,CAAC,CAACP,SAAS,CAAC;EACzH;EAKA,IAAI+B,IAAIA,CAAA;IAAY,OAAOxD,cAAuB;EAAE;EACpD,IAAIwD,IAAIA,CAACK,QAAe;IAAI,MAAM,IAAI/B,KAAK,CAAC,sCAAsC,CAAC;EAAE;EACrF,IAAIgC,QAAQA,CAAA;IAAY,OAAO9D,cAAuB;EAAE;EACxD,IAAI8D,QAAQA,CAACD,QAAe;IAAI,MAAM,IAAI/B,KAAK,CAAC,sCAAsC,CAAC;EAAE;EACzF,IAAIiC,IAAIA,CAAA;IAAY,OAAO,EAAW;EAAE;EACxC,IAAIA,IAAIA,CAACF,QAAe;IAAI,MAAM,IAAI/B,KAAK,CAAC,sCAAsC,CAAC;EAAE;EACrF,IAAIkC,IAAIA,CAAA;IAAa,OAAO,IAAI,CAACC,QAAQ,EAAE;EAAE;EAC7C,IAAID,IAAIA,CAACH,QAAgB;IAAI,MAAM,IAAI/B,KAAK,CAAC,wCAAwC,CAAC;EAAE;EAExF,IAAIuB,KAAKA,CAAA;IACP,OAAO,IAAI,CAAChB,QAAQ,CAACoB,QAAQ,CAAC,KAAK,CAAC;EACtC;EAEA,IAAIjB,KAAKA,CAAA;IACP,OAAO,IAAI,CAACW,MAAM;EACpB;EAEA,IAAIX,KAAKA,CAAC0B,IAAc;IACtB,IAAI,CAACf,MAAM,GAAGe,IAAI;EACpB;EAEAD,QAAQA,CAAA;IACN,OAAO,KAAK,CAACA,QAAQ,EAAE,CAAChB,OAAO,CAACjD,cAAc,EAAE,IAAI,CAACwC,KAAK,CAAC2B,IAAI,CAAC,GAAG,CAAC,CAAC;EACvE;EAEAC,KAAKA,CAAA;IACH,OAAO,IAAIrC,gBAAgB,CAAC,IAAI,CAACkC,QAAQ,EAAE,EAAE;MAC3C9B,eAAe,EAAE;KAClB,CAAC;EACJ;EAEAkC,MAAMA,CAACnC,OAA0C;IAC/C,OAAO,IAAAzC,QAAA,CAAA6E,2BAA2B,EAAC,IAAI,EAAEpC,OAAO,CAAC;EACnD;EAGAqC,iBAAiBA,CAAA;IACf,MAAMC,QAAQ,GAAI,KAAc,IAAI,KAAKxD,6BAA6B,CAAmBzB,YAAA,CAAAkF,eAAe,CAAC,EAAC,CAAE;IAC5G,OAAO,IAAI,CAACb,YAA0C;EACxD;EAEA,CAACrC,MAAM,CAACmD,GAAG,CAAC,4BAA4B,CAAC,IAAC;IACxC,MAAM;MAAEV,IAAI;MAAEW,MAAM;MAAEtC,QAAQ;MAAEC,QAAQ;MAAEC,QAAQ;MAAEC,KAAK;MAAEkB,QAAQ;MAAEkB,MAAM;MAAEhB,YAAY;MAAEiB;IAAI,CAAE,GAAG,IAAI;IACxG,OAAO;MAAEb,IAAI;MAAEW,MAAM;MAAEtC,QAAQ;MAAEC,QAAQ;MAAEC,QAAQ;MAAEC,KAAK;MAAEkB,QAAQ;MAAEkB,MAAM;MAAEhB,YAAY;MAAEiB;IAAI,CAAE;EACpG;;AApIFjF,OAAA,CAAAmC,gBAAA,GAAAA,gBAAA;AA4IA,MAAa+C,4BAAqE,SAAQzE,kBAAoC;EAC5H2B,YAAY+C,IAAoB;IAC9B,KAAK,EAAE;IACP,KAAK,MAAMC,KAAK,IAAI,CAACD,IAAI,aAAJA,IAAI,cAAJA,IAAI,GAAI,EAAE,EAAE3B,KAAK,CAAC,GAAG,CAAC,EAAE;MAC3C,IAAI,CAAC4B,KAAK,EAAE;MACZ,MAAMC,UAAU,GAAGD,KAAK,CAACE,OAAO,CAAC,GAAG,CAAC;MAErC,IAAID,UAAU,KAAK,CAAC,CAAC,EAAE;QACrB,IAAI,CAACtE,GAAG,CAACqE,KAA2B,EAAE,EAAE,CAAC;OAC1C,MAAM;QACL,IAAI,CAACrE,GAAG,CAACqE,KAAK,CAACG,KAAK,CAAC,CAAC,EAAEF,UAAU,CAAuB,EAAED,KAAK,CAACG,KAAK,CAACF,UAAU,GAAG,CAAC,CAAC,CAAC;;;EAG7F;EAEAhB,QAAQA,CAAA;IACN,OAAO,CAAC,GAAG,IAAI,CAAC,CAACmB,GAAG,CAACJ,KAAK,IAAIA,KAAK,CAACb,IAAI,CAAC,GAAG,CAAC,CAAC,CAACA,IAAI,CAAC,GAAG,CAAC;EAC1D;;AAjBFvE,OAAA,CAAAkF,4BAAA,GAAAA,4BAAA;AAoBAlF,OAAA,CAAAyF,OAAA,GAAetD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}