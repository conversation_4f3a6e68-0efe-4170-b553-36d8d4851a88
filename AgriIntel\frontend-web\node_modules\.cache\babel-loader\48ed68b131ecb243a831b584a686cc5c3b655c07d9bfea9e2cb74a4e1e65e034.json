{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ClientBulkWriteResultsMerger = void 0;\nconst __1 = require(\"../..\");\nconst error_1 = require(\"../../error\");\n/**\n * Unacknowledged bulk writes are always the same.\n */\nconst UNACKNOWLEDGED = {\n  acknowledged: false,\n  insertedCount: 0,\n  upsertedCount: 0,\n  matchedCount: 0,\n  modifiedCount: 0,\n  deletedCount: 0,\n  insertResults: undefined,\n  updateResults: undefined,\n  deleteResults: undefined\n};\n/**\n * Merges client bulk write cursor responses together into a single result.\n * @internal\n */\nclass ClientBulkWriteResultsMerger {\n  /**\n   * @returns The standard unacknowledged bulk write result.\n   */\n  static unacknowledged() {\n    return UNACKNOWLEDGED;\n  }\n  /**\n   * Instantiate the merger.\n   * @param options - The options.\n   */\n  constructor(options) {\n    this.options = options;\n    this.currentBatchOffset = 0;\n    this.writeConcernErrors = [];\n    this.writeErrors = new Map();\n    this.result = {\n      acknowledged: true,\n      insertedCount: 0,\n      upsertedCount: 0,\n      matchedCount: 0,\n      modifiedCount: 0,\n      deletedCount: 0,\n      insertResults: undefined,\n      updateResults: undefined,\n      deleteResults: undefined\n    };\n    if (options.verboseResults) {\n      this.result.insertResults = new Map();\n      this.result.updateResults = new Map();\n      this.result.deleteResults = new Map();\n    }\n  }\n  /**\n   * Get the bulk write result object.\n   */\n  get bulkWriteResult() {\n    return {\n      acknowledged: this.result.acknowledged,\n      insertedCount: this.result.insertedCount,\n      upsertedCount: this.result.upsertedCount,\n      matchedCount: this.result.matchedCount,\n      modifiedCount: this.result.modifiedCount,\n      deletedCount: this.result.deletedCount,\n      insertResults: this.result.insertResults,\n      updateResults: this.result.updateResults,\n      deleteResults: this.result.deleteResults\n    };\n  }\n  /**\n   * Merge the results in the cursor to the existing result.\n   * @param currentBatchOffset - The offset index to the original models.\n   * @param response - The cursor response.\n   * @param documents - The documents in the cursor.\n   * @returns The current result.\n   */\n  async merge(cursor) {\n    let writeConcernErrorResult;\n    try {\n      for await (const document of cursor) {\n        // Only add to maps if ok: 1\n        if (document.ok === 1) {\n          if (this.options.verboseResults) {\n            this.processDocument(cursor, document);\n          }\n        } else {\n          // If an individual write error is encountered during an ordered bulk write, drivers MUST\n          // record the error in writeErrors and immediately throw the exception. Otherwise, drivers\n          // MUST continue to iterate the results cursor and execute any further bulkWrite batches.\n          if (this.options.ordered) {\n            const error = new error_1.MongoClientBulkWriteError({\n              message: 'Mongo client ordered bulk write encountered a write error.'\n            });\n            error.writeErrors.set(document.idx + this.currentBatchOffset, {\n              code: document.code,\n              message: document.errmsg\n            });\n            error.partialResult = this.result;\n            throw error;\n          } else {\n            this.writeErrors.set(document.idx + this.currentBatchOffset, {\n              code: document.code,\n              message: document.errmsg\n            });\n          }\n        }\n      }\n    } catch (error) {\n      if (error instanceof __1.MongoWriteConcernError) {\n        const result = error.result;\n        writeConcernErrorResult = {\n          insertedCount: result.nInserted,\n          upsertedCount: result.nUpserted,\n          matchedCount: result.nMatched,\n          modifiedCount: result.nModified,\n          deletedCount: result.nDeleted,\n          writeConcernError: result.writeConcernError\n        };\n        if (this.options.verboseResults && result.cursor.firstBatch) {\n          for (const document of result.cursor.firstBatch) {\n            if (document.ok === 1) {\n              this.processDocument(cursor, document);\n            }\n          }\n        }\n      } else {\n        throw error;\n      }\n    } finally {\n      // Update the counts from the cursor response.\n      if (cursor.response) {\n        const response = cursor.response;\n        this.incrementCounts(response);\n      }\n      // Increment the batch offset.\n      this.currentBatchOffset += cursor.operations.length;\n    }\n    // If we have write concern errors ensure they are added.\n    if (writeConcernErrorResult) {\n      const writeConcernError = writeConcernErrorResult.writeConcernError;\n      this.incrementCounts(writeConcernErrorResult);\n      this.writeConcernErrors.push({\n        code: writeConcernError.code,\n        message: writeConcernError.errmsg\n      });\n    }\n    return this.result;\n  }\n  /**\n   * Process an individual document in the results.\n   * @param cursor - The cursor.\n   * @param document - The document to process.\n   */\n  processDocument(cursor, document) {\n    // Get the corresponding operation from the command.\n    const operation = cursor.operations[document.idx];\n    // Handle insert results.\n    if ('insert' in operation) {\n      this.result.insertResults?.set(document.idx + this.currentBatchOffset, {\n        insertedId: operation.document._id\n      });\n    }\n    // Handle update results.\n    if ('update' in operation) {\n      const result = {\n        matchedCount: document.n,\n        modifiedCount: document.nModified ?? 0,\n        // Check if the bulk did actually upsert.\n        didUpsert: document.upserted != null\n      };\n      if (document.upserted) {\n        result.upsertedId = document.upserted._id;\n      }\n      this.result.updateResults?.set(document.idx + this.currentBatchOffset, result);\n    }\n    // Handle delete results.\n    if ('delete' in operation) {\n      this.result.deleteResults?.set(document.idx + this.currentBatchOffset, {\n        deletedCount: document.n\n      });\n    }\n  }\n  /**\n   * Increment the result counts.\n   * @param document - The document with the results.\n   */\n  incrementCounts(document) {\n    this.result.insertedCount += document.insertedCount;\n    this.result.upsertedCount += document.upsertedCount;\n    this.result.matchedCount += document.matchedCount;\n    this.result.modifiedCount += document.modifiedCount;\n    this.result.deletedCount += document.deletedCount;\n  }\n}\nexports.ClientBulkWriteResultsMerger = ClientBulkWriteResultsMerger;", "map": {"version": 3, "names": ["__1", "require", "error_1", "UNACKNOWLEDGED", "acknowledged", "insertedCount", "upsertedCount", "matchedCount", "modifiedCount", "deletedCount", "insertResults", "undefined", "updateResults", "deleteResults", "ClientBulkWriteResultsMerger", "unacknowledged", "constructor", "options", "currentBatchOffset", "writeConcernErrors", "writeErrors", "Map", "result", "verboseResults", "bulkWriteResult", "merge", "cursor", "writeConcernErrorResult", "document", "ok", "processDocument", "ordered", "error", "MongoClientBulkWriteError", "message", "set", "idx", "code", "errmsg", "partialResult", "MongoWriteConcernError", "nInserted", "nUpserted", "nMatched", "nModified", "nDeleted", "writeConcernError", "firstBatch", "response", "incrementCounts", "operations", "length", "push", "operation", "insertedId", "_id", "n", "didU<PERSON>rt", "upserted", "upsertedId", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\client_bulk_write\\results_merger.ts"], "sourcesContent": ["import { MongoWriteConcernError } from '../..';\nimport { type Document } from '../../bson';\nimport { type ClientBulkWriteCursor } from '../../cursor/client_bulk_write_cursor';\nimport { MongoClientBulkWriteError } from '../../error';\nimport {\n  type ClientBulkWriteError,\n  type ClientBulkWriteOptions,\n  type ClientBulkWriteResult,\n  type ClientDeleteResult,\n  type ClientInsertOneResult,\n  type ClientUpdateResult\n} from './common';\n\n/**\n * Unacknowledged bulk writes are always the same.\n */\nconst UNACKNOWLEDGED = {\n  acknowledged: false,\n  insertedCount: 0,\n  upsertedCount: 0,\n  matchedCount: 0,\n  modifiedCount: 0,\n  deletedCount: 0,\n  insertResults: undefined,\n  updateResults: undefined,\n  deleteResults: undefined\n};\n\ninterface ClientBulkWriteResultAccumulation {\n  /**\n   * Whether the bulk write was acknowledged.\n   */\n  acknowledged: boolean;\n  /**\n   * The total number of documents inserted across all insert operations.\n   */\n  insertedCount: number;\n  /**\n   * The total number of documents upserted across all update operations.\n   */\n  upsertedCount: number;\n  /**\n   * The total number of documents matched across all update operations.\n   */\n  matchedCount: number;\n  /**\n   * The total number of documents modified across all update operations.\n   */\n  modifiedCount: number;\n  /**\n   * The total number of documents deleted across all delete operations.\n   */\n  deletedCount: number;\n  /**\n   * The results of each individual insert operation that was successfully performed.\n   */\n  insertResults?: Map<number, ClientInsertOneResult>;\n  /**\n   * The results of each individual update operation that was successfully performed.\n   */\n  updateResults?: Map<number, ClientUpdateResult>;\n  /**\n   * The results of each individual delete operation that was successfully performed.\n   */\n  deleteResults?: Map<number, ClientDeleteResult>;\n}\n\n/**\n * Merges client bulk write cursor responses together into a single result.\n * @internal\n */\nexport class ClientBulkWriteResultsMerger {\n  private result: ClientBulkWriteResultAccumulation;\n  private options: ClientBulkWriteOptions;\n  private currentBatchOffset: number;\n  writeConcernErrors: Document[];\n  writeErrors: Map<number, ClientBulkWriteError>;\n\n  /**\n   * @returns The standard unacknowledged bulk write result.\n   */\n  static unacknowledged(): ClientBulkWriteResult {\n    return UNACKNOWLEDGED;\n  }\n\n  /**\n   * Instantiate the merger.\n   * @param options - The options.\n   */\n  constructor(options: ClientBulkWriteOptions) {\n    this.options = options;\n    this.currentBatchOffset = 0;\n    this.writeConcernErrors = [];\n    this.writeErrors = new Map();\n    this.result = {\n      acknowledged: true,\n      insertedCount: 0,\n      upsertedCount: 0,\n      matchedCount: 0,\n      modifiedCount: 0,\n      deletedCount: 0,\n      insertResults: undefined,\n      updateResults: undefined,\n      deleteResults: undefined\n    };\n\n    if (options.verboseResults) {\n      this.result.insertResults = new Map<number, ClientInsertOneResult>();\n      this.result.updateResults = new Map<number, ClientUpdateResult>();\n      this.result.deleteResults = new Map<number, ClientDeleteResult>();\n    }\n  }\n\n  /**\n   * Get the bulk write result object.\n   */\n  get bulkWriteResult(): ClientBulkWriteResult {\n    return {\n      acknowledged: this.result.acknowledged,\n      insertedCount: this.result.insertedCount,\n      upsertedCount: this.result.upsertedCount,\n      matchedCount: this.result.matchedCount,\n      modifiedCount: this.result.modifiedCount,\n      deletedCount: this.result.deletedCount,\n      insertResults: this.result.insertResults,\n      updateResults: this.result.updateResults,\n      deleteResults: this.result.deleteResults\n    };\n  }\n\n  /**\n   * Merge the results in the cursor to the existing result.\n   * @param currentBatchOffset - The offset index to the original models.\n   * @param response - The cursor response.\n   * @param documents - The documents in the cursor.\n   * @returns The current result.\n   */\n  async merge(cursor: ClientBulkWriteCursor): Promise<ClientBulkWriteResult> {\n    let writeConcernErrorResult;\n    try {\n      for await (const document of cursor) {\n        // Only add to maps if ok: 1\n        if (document.ok === 1) {\n          if (this.options.verboseResults) {\n            this.processDocument(cursor, document);\n          }\n        } else {\n          // If an individual write error is encountered during an ordered bulk write, drivers MUST\n          // record the error in writeErrors and immediately throw the exception. Otherwise, drivers\n          // MUST continue to iterate the results cursor and execute any further bulkWrite batches.\n          if (this.options.ordered) {\n            const error = new MongoClientBulkWriteError({\n              message: 'Mongo client ordered bulk write encountered a write error.'\n            });\n            error.writeErrors.set(document.idx + this.currentBatchOffset, {\n              code: document.code,\n              message: document.errmsg\n            });\n            error.partialResult = this.result;\n            throw error;\n          } else {\n            this.writeErrors.set(document.idx + this.currentBatchOffset, {\n              code: document.code,\n              message: document.errmsg\n            });\n          }\n        }\n      }\n    } catch (error) {\n      if (error instanceof MongoWriteConcernError) {\n        const result = error.result;\n        writeConcernErrorResult = {\n          insertedCount: result.nInserted,\n          upsertedCount: result.nUpserted,\n          matchedCount: result.nMatched,\n          modifiedCount: result.nModified,\n          deletedCount: result.nDeleted,\n          writeConcernError: result.writeConcernError\n        };\n        if (this.options.verboseResults && result.cursor.firstBatch) {\n          for (const document of result.cursor.firstBatch) {\n            if (document.ok === 1) {\n              this.processDocument(cursor, document);\n            }\n          }\n        }\n      } else {\n        throw error;\n      }\n    } finally {\n      // Update the counts from the cursor response.\n      if (cursor.response) {\n        const response = cursor.response;\n        this.incrementCounts(response);\n      }\n\n      // Increment the batch offset.\n      this.currentBatchOffset += cursor.operations.length;\n    }\n\n    // If we have write concern errors ensure they are added.\n    if (writeConcernErrorResult) {\n      const writeConcernError = writeConcernErrorResult.writeConcernError as Document;\n      this.incrementCounts(writeConcernErrorResult);\n      this.writeConcernErrors.push({\n        code: writeConcernError.code,\n        message: writeConcernError.errmsg\n      });\n    }\n\n    return this.result;\n  }\n\n  /**\n   * Process an individual document in the results.\n   * @param cursor - The cursor.\n   * @param document - The document to process.\n   */\n  private processDocument(cursor: ClientBulkWriteCursor, document: Document) {\n    // Get the corresponding operation from the command.\n    const operation = cursor.operations[document.idx];\n    // Handle insert results.\n    if ('insert' in operation) {\n      this.result.insertResults?.set(document.idx + this.currentBatchOffset, {\n        insertedId: operation.document._id\n      });\n    }\n    // Handle update results.\n    if ('update' in operation) {\n      const result: ClientUpdateResult = {\n        matchedCount: document.n,\n        modifiedCount: document.nModified ?? 0,\n        // Check if the bulk did actually upsert.\n        didUpsert: document.upserted != null\n      };\n      if (document.upserted) {\n        result.upsertedId = document.upserted._id;\n      }\n      this.result.updateResults?.set(document.idx + this.currentBatchOffset, result);\n    }\n    // Handle delete results.\n    if ('delete' in operation) {\n      this.result.deleteResults?.set(document.idx + this.currentBatchOffset, {\n        deletedCount: document.n\n      });\n    }\n  }\n\n  /**\n   * Increment the result counts.\n   * @param document - The document with the results.\n   */\n  private incrementCounts(document: Document) {\n    this.result.insertedCount += document.insertedCount;\n    this.result.upsertedCount += document.upsertedCount;\n    this.result.matchedCount += document.matchedCount;\n    this.result.modifiedCount += document.modifiedCount;\n    this.result.deletedCount += document.deletedCount;\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,GAAA,GAAAC,OAAA;AAGA,MAAAC,OAAA,GAAAD,OAAA;AAUA;;;AAGA,MAAME,cAAc,GAAG;EACrBC,YAAY,EAAE,KAAK;EACnBC,aAAa,EAAE,CAAC;EAChBC,aAAa,EAAE,CAAC;EAChBC,YAAY,EAAE,CAAC;EACfC,aAAa,EAAE,CAAC;EAChBC,YAAY,EAAE,CAAC;EACfC,aAAa,EAAEC,SAAS;EACxBC,aAAa,EAAED,SAAS;EACxBE,aAAa,EAAEF;CAChB;AAyCD;;;;AAIA,MAAaG,4BAA4B;EAOvC;;;EAGA,OAAOC,cAAcA,CAAA;IACnB,OAAOZ,cAAc;EACvB;EAEA;;;;EAIAa,YAAYC,OAA+B;IACzC,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,kBAAkB,GAAG,CAAC;IAC3B,IAAI,CAACC,kBAAkB,GAAG,EAAE;IAC5B,IAAI,CAACC,WAAW,GAAG,IAAIC,GAAG,EAAE;IAC5B,IAAI,CAACC,MAAM,GAAG;MACZlB,YAAY,EAAE,IAAI;MAClBC,aAAa,EAAE,CAAC;MAChBC,aAAa,EAAE,CAAC;MAChBC,YAAY,EAAE,CAAC;MACfC,aAAa,EAAE,CAAC;MAChBC,YAAY,EAAE,CAAC;MACfC,aAAa,EAAEC,SAAS;MACxBC,aAAa,EAAED,SAAS;MACxBE,aAAa,EAAEF;KAChB;IAED,IAAIM,OAAO,CAACM,cAAc,EAAE;MAC1B,IAAI,CAACD,MAAM,CAACZ,aAAa,GAAG,IAAIW,GAAG,EAAiC;MACpE,IAAI,CAACC,MAAM,CAACV,aAAa,GAAG,IAAIS,GAAG,EAA8B;MACjE,IAAI,CAACC,MAAM,CAACT,aAAa,GAAG,IAAIQ,GAAG,EAA8B;IACnE;EACF;EAEA;;;EAGA,IAAIG,eAAeA,CAAA;IACjB,OAAO;MACLpB,YAAY,EAAE,IAAI,CAACkB,MAAM,CAAClB,YAAY;MACtCC,aAAa,EAAE,IAAI,CAACiB,MAAM,CAACjB,aAAa;MACxCC,aAAa,EAAE,IAAI,CAACgB,MAAM,CAAChB,aAAa;MACxCC,YAAY,EAAE,IAAI,CAACe,MAAM,CAACf,YAAY;MACtCC,aAAa,EAAE,IAAI,CAACc,MAAM,CAACd,aAAa;MACxCC,YAAY,EAAE,IAAI,CAACa,MAAM,CAACb,YAAY;MACtCC,aAAa,EAAE,IAAI,CAACY,MAAM,CAACZ,aAAa;MACxCE,aAAa,EAAE,IAAI,CAACU,MAAM,CAACV,aAAa;MACxCC,aAAa,EAAE,IAAI,CAACS,MAAM,CAACT;KAC5B;EACH;EAEA;;;;;;;EAOA,MAAMY,KAAKA,CAACC,MAA6B;IACvC,IAAIC,uBAAuB;IAC3B,IAAI;MACF,WAAW,MAAMC,QAAQ,IAAIF,MAAM,EAAE;QACnC;QACA,IAAIE,QAAQ,CAACC,EAAE,KAAK,CAAC,EAAE;UACrB,IAAI,IAAI,CAACZ,OAAO,CAACM,cAAc,EAAE;YAC/B,IAAI,CAACO,eAAe,CAACJ,MAAM,EAAEE,QAAQ,CAAC;UACxC;QACF,CAAC,MAAM;UACL;UACA;UACA;UACA,IAAI,IAAI,CAACX,OAAO,CAACc,OAAO,EAAE;YACxB,MAAMC,KAAK,GAAG,IAAI9B,OAAA,CAAA+B,yBAAyB,CAAC;cAC1CC,OAAO,EAAE;aACV,CAAC;YACFF,KAAK,CAACZ,WAAW,CAACe,GAAG,CAACP,QAAQ,CAACQ,GAAG,GAAG,IAAI,CAAClB,kBAAkB,EAAE;cAC5DmB,IAAI,EAAET,QAAQ,CAACS,IAAI;cACnBH,OAAO,EAAEN,QAAQ,CAACU;aACnB,CAAC;YACFN,KAAK,CAACO,aAAa,GAAG,IAAI,CAACjB,MAAM;YACjC,MAAMU,KAAK;UACb,CAAC,MAAM;YACL,IAAI,CAACZ,WAAW,CAACe,GAAG,CAACP,QAAQ,CAACQ,GAAG,GAAG,IAAI,CAAClB,kBAAkB,EAAE;cAC3DmB,IAAI,EAAET,QAAQ,CAACS,IAAI;cACnBH,OAAO,EAAEN,QAAQ,CAACU;aACnB,CAAC;UACJ;QACF;MACF;IACF,CAAC,CAAC,OAAON,KAAK,EAAE;MACd,IAAIA,KAAK,YAAYhC,GAAA,CAAAwC,sBAAsB,EAAE;QAC3C,MAAMlB,MAAM,GAAGU,KAAK,CAACV,MAAM;QAC3BK,uBAAuB,GAAG;UACxBtB,aAAa,EAAEiB,MAAM,CAACmB,SAAS;UAC/BnC,aAAa,EAAEgB,MAAM,CAACoB,SAAS;UAC/BnC,YAAY,EAAEe,MAAM,CAACqB,QAAQ;UAC7BnC,aAAa,EAAEc,MAAM,CAACsB,SAAS;UAC/BnC,YAAY,EAAEa,MAAM,CAACuB,QAAQ;UAC7BC,iBAAiB,EAAExB,MAAM,CAACwB;SAC3B;QACD,IAAI,IAAI,CAAC7B,OAAO,CAACM,cAAc,IAAID,MAAM,CAACI,MAAM,CAACqB,UAAU,EAAE;UAC3D,KAAK,MAAMnB,QAAQ,IAAIN,MAAM,CAACI,MAAM,CAACqB,UAAU,EAAE;YAC/C,IAAInB,QAAQ,CAACC,EAAE,KAAK,CAAC,EAAE;cACrB,IAAI,CAACC,eAAe,CAACJ,MAAM,EAAEE,QAAQ,CAAC;YACxC;UACF;QACF;MACF,CAAC,MAAM;QACL,MAAMI,KAAK;MACb;IACF,CAAC,SAAS;MACR;MACA,IAAIN,MAAM,CAACsB,QAAQ,EAAE;QACnB,MAAMA,QAAQ,GAAGtB,MAAM,CAACsB,QAAQ;QAChC,IAAI,CAACC,eAAe,CAACD,QAAQ,CAAC;MAChC;MAEA;MACA,IAAI,CAAC9B,kBAAkB,IAAIQ,MAAM,CAACwB,UAAU,CAACC,MAAM;IACrD;IAEA;IACA,IAAIxB,uBAAuB,EAAE;MAC3B,MAAMmB,iBAAiB,GAAGnB,uBAAuB,CAACmB,iBAA6B;MAC/E,IAAI,CAACG,eAAe,CAACtB,uBAAuB,CAAC;MAC7C,IAAI,CAACR,kBAAkB,CAACiC,IAAI,CAAC;QAC3Bf,IAAI,EAAES,iBAAiB,CAACT,IAAI;QAC5BH,OAAO,EAAEY,iBAAiB,CAACR;OAC5B,CAAC;IACJ;IAEA,OAAO,IAAI,CAAChB,MAAM;EACpB;EAEA;;;;;EAKQQ,eAAeA,CAACJ,MAA6B,EAAEE,QAAkB;IACvE;IACA,MAAMyB,SAAS,GAAG3B,MAAM,CAACwB,UAAU,CAACtB,QAAQ,CAACQ,GAAG,CAAC;IACjD;IACA,IAAI,QAAQ,IAAIiB,SAAS,EAAE;MACzB,IAAI,CAAC/B,MAAM,CAACZ,aAAa,EAAEyB,GAAG,CAACP,QAAQ,CAACQ,GAAG,GAAG,IAAI,CAAClB,kBAAkB,EAAE;QACrEoC,UAAU,EAAED,SAAS,CAACzB,QAAQ,CAAC2B;OAChC,CAAC;IACJ;IACA;IACA,IAAI,QAAQ,IAAIF,SAAS,EAAE;MACzB,MAAM/B,MAAM,GAAuB;QACjCf,YAAY,EAAEqB,QAAQ,CAAC4B,CAAC;QACxBhD,aAAa,EAAEoB,QAAQ,CAACgB,SAAS,IAAI,CAAC;QACtC;QACAa,SAAS,EAAE7B,QAAQ,CAAC8B,QAAQ,IAAI;OACjC;MACD,IAAI9B,QAAQ,CAAC8B,QAAQ,EAAE;QACrBpC,MAAM,CAACqC,UAAU,GAAG/B,QAAQ,CAAC8B,QAAQ,CAACH,GAAG;MAC3C;MACA,IAAI,CAACjC,MAAM,CAACV,aAAa,EAAEuB,GAAG,CAACP,QAAQ,CAACQ,GAAG,GAAG,IAAI,CAAClB,kBAAkB,EAAEI,MAAM,CAAC;IAChF;IACA;IACA,IAAI,QAAQ,IAAI+B,SAAS,EAAE;MACzB,IAAI,CAAC/B,MAAM,CAACT,aAAa,EAAEsB,GAAG,CAACP,QAAQ,CAACQ,GAAG,GAAG,IAAI,CAAClB,kBAAkB,EAAE;QACrET,YAAY,EAAEmB,QAAQ,CAAC4B;OACxB,CAAC;IACJ;EACF;EAEA;;;;EAIQP,eAAeA,CAACrB,QAAkB;IACxC,IAAI,CAACN,MAAM,CAACjB,aAAa,IAAIuB,QAAQ,CAACvB,aAAa;IACnD,IAAI,CAACiB,MAAM,CAAChB,aAAa,IAAIsB,QAAQ,CAACtB,aAAa;IACnD,IAAI,CAACgB,MAAM,CAACf,YAAY,IAAIqB,QAAQ,CAACrB,YAAY;IACjD,IAAI,CAACe,MAAM,CAACd,aAAa,IAAIoB,QAAQ,CAACpB,aAAa;IACnD,IAAI,CAACc,MAAM,CAACb,YAAY,IAAImB,QAAQ,CAACnB,YAAY;EACnD;;AA3LFmD,OAAA,CAAA9C,4BAAA,GAAAA,4BAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}