{"ast": null, "code": "import { createSlice } from '@reduxjs/toolkit';\nconst initialState = {\n  breedingRecords: [],\n  birthRecords: [],\n  heatRecords: [],\n  selectedBreedingRecord: null,\n  selectedBirthRecord: null,\n  isLoading: false,\n  error: null,\n  statistics: null\n};\nconst breedingSlice = createSlice({\n  name: 'breeding',\n  initialState,\n  reducers: {\n    setBreedingRecords: (state, action) => {\n      state.breedingRecords = action.payload;\n    },\n    addBreedingRecord: (state, action) => {\n      state.breedingRecords.unshift(action.payload);\n    },\n    updateBreedingRecord: (state, action) => {\n      const index = state.breedingRecords.findIndex(record => record._id === action.payload._id);\n      if (index !== -1) {\n        state.breedingRecords[index] = action.payload;\n      }\n    },\n    deleteBreedingRecord: (state, action) => {\n      state.breedingRecords = state.breedingRecords.filter(record => record._id !== action.payload);\n    },\n    setBirthRecords: (state, action) => {\n      state.birthRecords = action.payload;\n    },\n    addBirthRecord: (state, action) => {\n      state.birthRecords.unshift(action.payload);\n    },\n    updateBirthRecord: (state, action) => {\n      const index = state.birthRecords.findIndex(record => record._id === action.payload._id);\n      if (index !== -1) {\n        state.birthRecords[index] = action.payload;\n      }\n    },\n    deleteBirthRecord: (state, action) => {\n      state.birthRecords = state.birthRecords.filter(record => record._id !== action.payload);\n    },\n    setHeatRecords: (state, action) => {\n      state.heatRecords = action.payload;\n    },\n    addHeatRecord: (state, action) => {\n      state.heatRecords.unshift(action.payload);\n    },\n    updateHeatRecord: (state, action) => {\n      const index = state.heatRecords.findIndex(record => record._id === action.payload._id);\n      if (index !== -1) {\n        state.heatRecords[index] = action.payload;\n      }\n    },\n    deleteHeatRecord: (state, action) => {\n      state.heatRecords = state.heatRecords.filter(record => record._id !== action.payload);\n    },\n    setSelectedBreedingRecord: (state, action) => {\n      state.selectedBreedingRecord = action.payload;\n    },\n    setSelectedBirthRecord: (state, action) => {\n      state.selectedBirthRecord = action.payload;\n    },\n    setLoading: (state, action) => {\n      state.isLoading = action.payload;\n    },\n    setError: (state, action) => {\n      state.error = action.payload;\n    },\n    clearError: state => {\n      state.error = null;\n    },\n    setStatistics: (state, action) => {\n      state.statistics = action.payload;\n    }\n  }\n});\nexport const {\n  setBreedingRecords,\n  addBreedingRecord,\n  updateBreedingRecord,\n  deleteBreedingRecord,\n  setBirthRecords,\n  addBirthRecord,\n  updateBirthRecord,\n  deleteBirthRecord,\n  setHeatRecords,\n  addHeatRecord,\n  updateHeatRecord,\n  deleteHeatRecord,\n  setSelectedBreedingRecord,\n  setSelectedBirthRecord,\n  setLoading,\n  setError,\n  clearError,\n  setStatistics\n} = breedingSlice.actions;\nexport default breedingSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "breedingRecords", "birthRecords", "heatRecords", "selectedBreedingRecord", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>ord", "isLoading", "error", "statistics", "breedingSlice", "name", "reducers", "setBreedingRecords", "state", "action", "payload", "addBreedingRecord", "unshift", "updateBreedingRecord", "index", "findIndex", "record", "_id", "deleteBreedingRecord", "filter", "setBirthRecords", "addBirthRecord", "updateBirthRecord", "deleteBirthRecord", "setHeatRecords", "addHeatRecord", "updateHeatRecord", "deleteHeatRecord", "setSelectedBreedingRecord", "setSelectedBirthRecord", "setLoading", "setError", "clearError", "setStatistics", "actions", "reducer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/store/slices/breedingSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\n\nexport interface BreedingRecord {\n  _id: string;\n  female: string;\n  male?: string;\n  breedingDate: string;\n  breedingMethod: 'natural' | 'artificial_insemination' | 'embryo_transfer';\n  expectedDueDate?: string;\n  actualBirthDate?: string;\n  pregnancyConfirmed: boolean;\n  pregnancyConfirmationDate?: string;\n  pregnancyConfirmationMethod?: 'ultrasound' | 'blood_test' | 'physical_exam';\n  complications?: string[];\n  veterinarian?: string;\n  semenSource?: {\n    bullId?: string;\n    semenBatch?: string;\n    supplier?: string;\n    cost?: number;\n  };\n  offspring?: string[];\n  notes?: string;\n  status: 'planned' | 'completed' | 'failed' | 'aborted';\n  createdBy: string;\n  updatedBy?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface BirthRecord {\n  _id: string;\n  mother: string;\n  father?: string;\n  birthDate: string;\n  birthTime?: string;\n  gestationPeriod?: number;\n  birthWeight?: number;\n  birthType: 'natural' | 'assisted' | 'cesarean';\n  complications?: string[];\n  veterinarian?: string;\n  offspring: Array<{\n    tagNumber: string;\n    gender: 'male' | 'female';\n    weight?: number;\n    health: 'healthy' | 'weak' | 'deceased';\n    notes?: string;\n  }>;\n  placentaExpelled: boolean;\n  placentaExpelledTime?: string;\n  postBirthTreatment?: string[];\n  notes?: string;\n  createdBy: string;\n  updatedBy?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface HeatRecord {\n  _id: string;\n  animal: string;\n  heatDate: string;\n  heatIntensity: 'weak' | 'moderate' | 'strong';\n  heatDuration?: number;\n  behaviorSigns: string[];\n  physicalSigns: string[];\n  breedingRecommended: boolean;\n  breedingWindow?: {\n    start: string;\n    end: string;\n  };\n  bred: boolean;\n  breedingDate?: string;\n  notes?: string;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface BreedingState {\n  breedingRecords: BreedingRecord[];\n  birthRecords: BirthRecord[];\n  heatRecords: HeatRecord[];\n  selectedBreedingRecord: BreedingRecord | null;\n  selectedBirthRecord: BirthRecord | null;\n  isLoading: boolean;\n  error: string | null;\n  statistics: {\n    totalBreedings: number;\n    successfulBreedings: number;\n    pregnancyRate: number;\n    birthRate: number;\n    averageGestationPeriod: number;\n    upcomingBirths: number;\n    animalsInHeat: number;\n  } | null;\n}\n\nconst initialState: BreedingState = {\n  breedingRecords: [],\n  birthRecords: [],\n  heatRecords: [],\n  selectedBreedingRecord: null,\n  selectedBirthRecord: null,\n  isLoading: false,\n  error: null,\n  statistics: null,\n};\n\nconst breedingSlice = createSlice({\n  name: 'breeding',\n  initialState,\n  reducers: {\n    setBreedingRecords: (state, action: PayloadAction<BreedingRecord[]>) => {\n      state.breedingRecords = action.payload;\n    },\n    addBreedingRecord: (state, action: PayloadAction<BreedingRecord>) => {\n      state.breedingRecords.unshift(action.payload);\n    },\n    updateBreedingRecord: (state, action: PayloadAction<BreedingRecord>) => {\n      const index = state.breedingRecords.findIndex(record => record._id === action.payload._id);\n      if (index !== -1) {\n        state.breedingRecords[index] = action.payload;\n      }\n    },\n    deleteBreedingRecord: (state, action: PayloadAction<string>) => {\n      state.breedingRecords = state.breedingRecords.filter(record => record._id !== action.payload);\n    },\n    setBirthRecords: (state, action: PayloadAction<BirthRecord[]>) => {\n      state.birthRecords = action.payload;\n    },\n    addBirthRecord: (state, action: PayloadAction<BirthRecord>) => {\n      state.birthRecords.unshift(action.payload);\n    },\n    updateBirthRecord: (state, action: PayloadAction<BirthRecord>) => {\n      const index = state.birthRecords.findIndex(record => record._id === action.payload._id);\n      if (index !== -1) {\n        state.birthRecords[index] = action.payload;\n      }\n    },\n    deleteBirthRecord: (state, action: PayloadAction<string>) => {\n      state.birthRecords = state.birthRecords.filter(record => record._id !== action.payload);\n    },\n    setHeatRecords: (state, action: PayloadAction<HeatRecord[]>) => {\n      state.heatRecords = action.payload;\n    },\n    addHeatRecord: (state, action: PayloadAction<HeatRecord>) => {\n      state.heatRecords.unshift(action.payload);\n    },\n    updateHeatRecord: (state, action: PayloadAction<HeatRecord>) => {\n      const index = state.heatRecords.findIndex(record => record._id === action.payload._id);\n      if (index !== -1) {\n        state.heatRecords[index] = action.payload;\n      }\n    },\n    deleteHeatRecord: (state, action: PayloadAction<string>) => {\n      state.heatRecords = state.heatRecords.filter(record => record._id !== action.payload);\n    },\n    setSelectedBreedingRecord: (state, action: PayloadAction<BreedingRecord | null>) => {\n      state.selectedBreedingRecord = action.payload;\n    },\n    setSelectedBirthRecord: (state, action: PayloadAction<BirthRecord | null>) => {\n      state.selectedBirthRecord = action.payload;\n    },\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.isLoading = action.payload;\n    },\n    setError: (state, action: PayloadAction<string | null>) => {\n      state.error = action.payload;\n    },\n    clearError: (state) => {\n      state.error = null;\n    },\n    setStatistics: (state, action: PayloadAction<BreedingState['statistics']>) => {\n      state.statistics = action.payload;\n    },\n  },\n});\n\nexport const {\n  setBreedingRecords,\n  addBreedingRecord,\n  updateBreedingRecord,\n  deleteBreedingRecord,\n  setBirthRecords,\n  addBirthRecord,\n  updateBirthRecord,\n  deleteBirthRecord,\n  setHeatRecords,\n  addHeatRecord,\n  updateHeatRecord,\n  deleteHeatRecord,\n  setSelectedBreedingRecord,\n  setSelectedBirthRecord,\n  setLoading,\n  setError,\n  clearError,\n  setStatistics,\n} = breedingSlice.actions;\n\nexport default breedingSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,QAAuB,kBAAkB;AAkG7D,MAAMC,YAA2B,GAAG;EAClCC,eAAe,EAAE,EAAE;EACnBC,YAAY,EAAE,EAAE;EAChBC,WAAW,EAAE,EAAE;EACfC,sBAAsB,EAAE,IAAI;EAC5BC,mBAAmB,EAAE,IAAI;EACzBC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE;AACd,CAAC;AAED,MAAMC,aAAa,GAAGV,WAAW,CAAC;EAChCW,IAAI,EAAE,UAAU;EAChBV,YAAY;EACZW,QAAQ,EAAE;IACRC,kBAAkB,EAAEA,CAACC,KAAK,EAAEC,MAAuC,KAAK;MACtED,KAAK,CAACZ,eAAe,GAAGa,MAAM,CAACC,OAAO;IACxC,CAAC;IACDC,iBAAiB,EAAEA,CAACH,KAAK,EAAEC,MAAqC,KAAK;MACnED,KAAK,CAACZ,eAAe,CAACgB,OAAO,CAACH,MAAM,CAACC,OAAO,CAAC;IAC/C,CAAC;IACDG,oBAAoB,EAAEA,CAACL,KAAK,EAAEC,MAAqC,KAAK;MACtE,MAAMK,KAAK,GAAGN,KAAK,CAACZ,eAAe,CAACmB,SAAS,CAACC,MAAM,IAAIA,MAAM,CAACC,GAAG,KAAKR,MAAM,CAACC,OAAO,CAACO,GAAG,CAAC;MAC1F,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBN,KAAK,CAACZ,eAAe,CAACkB,KAAK,CAAC,GAAGL,MAAM,CAACC,OAAO;MAC/C;IACF,CAAC;IACDQ,oBAAoB,EAAEA,CAACV,KAAK,EAAEC,MAA6B,KAAK;MAC9DD,KAAK,CAACZ,eAAe,GAAGY,KAAK,CAACZ,eAAe,CAACuB,MAAM,CAACH,MAAM,IAAIA,MAAM,CAACC,GAAG,KAAKR,MAAM,CAACC,OAAO,CAAC;IAC/F,CAAC;IACDU,eAAe,EAAEA,CAACZ,KAAK,EAAEC,MAAoC,KAAK;MAChED,KAAK,CAACX,YAAY,GAAGY,MAAM,CAACC,OAAO;IACrC,CAAC;IACDW,cAAc,EAAEA,CAACb,KAAK,EAAEC,MAAkC,KAAK;MAC7DD,KAAK,CAACX,YAAY,CAACe,OAAO,CAACH,MAAM,CAACC,OAAO,CAAC;IAC5C,CAAC;IACDY,iBAAiB,EAAEA,CAACd,KAAK,EAAEC,MAAkC,KAAK;MAChE,MAAMK,KAAK,GAAGN,KAAK,CAACX,YAAY,CAACkB,SAAS,CAACC,MAAM,IAAIA,MAAM,CAACC,GAAG,KAAKR,MAAM,CAACC,OAAO,CAACO,GAAG,CAAC;MACvF,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBN,KAAK,CAACX,YAAY,CAACiB,KAAK,CAAC,GAAGL,MAAM,CAACC,OAAO;MAC5C;IACF,CAAC;IACDa,iBAAiB,EAAEA,CAACf,KAAK,EAAEC,MAA6B,KAAK;MAC3DD,KAAK,CAACX,YAAY,GAAGW,KAAK,CAACX,YAAY,CAACsB,MAAM,CAACH,MAAM,IAAIA,MAAM,CAACC,GAAG,KAAKR,MAAM,CAACC,OAAO,CAAC;IACzF,CAAC;IACDc,cAAc,EAAEA,CAAChB,KAAK,EAAEC,MAAmC,KAAK;MAC9DD,KAAK,CAACV,WAAW,GAAGW,MAAM,CAACC,OAAO;IACpC,CAAC;IACDe,aAAa,EAAEA,CAACjB,KAAK,EAAEC,MAAiC,KAAK;MAC3DD,KAAK,CAACV,WAAW,CAACc,OAAO,CAACH,MAAM,CAACC,OAAO,CAAC;IAC3C,CAAC;IACDgB,gBAAgB,EAAEA,CAAClB,KAAK,EAAEC,MAAiC,KAAK;MAC9D,MAAMK,KAAK,GAAGN,KAAK,CAACV,WAAW,CAACiB,SAAS,CAACC,MAAM,IAAIA,MAAM,CAACC,GAAG,KAAKR,MAAM,CAACC,OAAO,CAACO,GAAG,CAAC;MACtF,IAAIH,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBN,KAAK,CAACV,WAAW,CAACgB,KAAK,CAAC,GAAGL,MAAM,CAACC,OAAO;MAC3C;IACF,CAAC;IACDiB,gBAAgB,EAAEA,CAACnB,KAAK,EAAEC,MAA6B,KAAK;MAC1DD,KAAK,CAACV,WAAW,GAAGU,KAAK,CAACV,WAAW,CAACqB,MAAM,CAACH,MAAM,IAAIA,MAAM,CAACC,GAAG,KAAKR,MAAM,CAACC,OAAO,CAAC;IACvF,CAAC;IACDkB,yBAAyB,EAAEA,CAACpB,KAAK,EAAEC,MAA4C,KAAK;MAClFD,KAAK,CAACT,sBAAsB,GAAGU,MAAM,CAACC,OAAO;IAC/C,CAAC;IACDmB,sBAAsB,EAAEA,CAACrB,KAAK,EAAEC,MAAyC,KAAK;MAC5ED,KAAK,CAACR,mBAAmB,GAAGS,MAAM,CAACC,OAAO;IAC5C,CAAC;IACDoB,UAAU,EAAEA,CAACtB,KAAK,EAAEC,MAA8B,KAAK;MACrDD,KAAK,CAACP,SAAS,GAAGQ,MAAM,CAACC,OAAO;IAClC,CAAC;IACDqB,QAAQ,EAAEA,CAACvB,KAAK,EAAEC,MAAoC,KAAK;MACzDD,KAAK,CAACN,KAAK,GAAGO,MAAM,CAACC,OAAO;IAC9B,CAAC;IACDsB,UAAU,EAAGxB,KAAK,IAAK;MACrBA,KAAK,CAACN,KAAK,GAAG,IAAI;IACpB,CAAC;IACD+B,aAAa,EAAEA,CAACzB,KAAK,EAAEC,MAAkD,KAAK;MAC5ED,KAAK,CAACL,UAAU,GAAGM,MAAM,CAACC,OAAO;IACnC;EACF;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EACXH,kBAAkB;EAClBI,iBAAiB;EACjBE,oBAAoB;EACpBK,oBAAoB;EACpBE,eAAe;EACfC,cAAc;EACdC,iBAAiB;EACjBC,iBAAiB;EACjBC,cAAc;EACdC,aAAa;EACbC,gBAAgB;EAChBC,gBAAgB;EAChBC,yBAAyB;EACzBC,sBAAsB;EACtBC,UAAU;EACVC,QAAQ;EACRC,UAAU;EACVC;AACF,CAAC,GAAG7B,aAAa,CAAC8B,OAAO;AAEzB,eAAe9B,aAAa,CAAC+B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}