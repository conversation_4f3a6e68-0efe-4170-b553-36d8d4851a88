@echo off
echo Starting AMPD Livestock Management System...
echo.

echo Installing backend dependencies...
cd backend
if not exist node_modules (
    echo Installing backend packages...
    npm install
) else (
    echo Backend dependencies already installed.
)

echo.
echo Installing frontend dependencies...
cd ../frontend-web
if not exist node_modules (
    echo Installing frontend packages...
    npm install
) else (
    echo Frontend dependencies already installed.
)

echo.
echo Starting backend server...
cd ../backend
start "AMPD Backend" cmd /k "npm run dev"

echo.
echo Waiting for backend to start...
timeout /t 5 /nobreak > nul

echo Starting frontend server...
cd ../frontend-web
start "AMPD Frontend" cmd /k "npm start"

echo.
echo AMPD Livestock Management System is starting...
echo Backend: http://localhost:3001
echo Frontend: http://localhost:3000
echo.
echo Press any key to exit...
pause > nul
