{"ast": null, "code": "if (process.env.NODE_ENV !== 'development') {\n  module.exports = {\n    ReactQueryDevtools: function () {\n      return null;\n    },\n    ReactQueryDevtoolsPanel: function () {\n      return null;\n    }\n  };\n} else {\n  module.exports = require('./development');\n}", "map": {"version": 3, "names": ["process", "env", "NODE_ENV", "module", "exports", "ReactQueryDevtools", "ReactQueryDevtoolsPanel", "require"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-query/devtools/index.js"], "sourcesContent": ["if (process.env.NODE_ENV !== 'development') {\n  module.exports = {\n    ReactQueryDevtools: function () {\n      return null\n    },\n    ReactQueryDevtoolsPanel: function () {\n      return null\n    },\n  }\n} else {\n  module.exports = require('./development')\n}\n"], "mappings": "AAAA,IAAIA,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,EAAE;EAC1CC,MAAM,CAACC,OAAO,GAAG;IACfC,kBAAkB,EAAE,SAAAA,CAAA,EAAY;MAC9B,OAAO,IAAI;IACb,CAAC;IACDC,uBAAuB,EAAE,SAAAA,CAAA,EAAY;MACnC,OAAO,IAAI;IACb;EACF,CAAC;AACH,CAAC,MAAM;EACLH,MAAM,CAACC,OAAO,GAAGG,OAAO,CAAC,eAAe,CAAC;AAC3C", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}