const mongoose = require('mongoose');

const animalSchema = new mongoose.Schema({
  // Basic Information
  tagNumber: {
    type: String,
    required: true,
    unique: true,
    trim: true
  },
  name: {
    type: String,
    trim: true
  },
  species: {
    type: String,
    required: true,
    enum: ['cattle', 'sheep', 'goat', 'pig', 'chicken', 'horse', 'other']
  },
  breed: {
    type: String,
    required: true,
    trim: true
  },
  gender: {
    type: String,
    required: true,
    enum: ['male', 'female']
  },
  dateOfBirth: {
    type: Date,
    required: true
  },
  
  // Physical Characteristics
  color: {
    type: String,
    trim: true
  },
  markings: {
    type: String,
    trim: true
  },
  currentWeight: {
    type: Number,
    min: 0
  },
  birthWeight: {
    type: Number,
    min: 0
  },
  
  // Identification
  rfidTag: {
    type: String,
    unique: true,
    sparse: true,
    trim: true
  },
  earTagNumber: {
    type: String,
    trim: true
  },
  microchipId: {
    type: String,
    unique: true,
    sparse: true,
    trim: true
  },
  
  // Genealogy
  sire: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Animal'
  },
  dam: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Animal'
  },
  
  // Status
  status: {
    type: String,
    enum: ['active', 'sold', 'deceased', 'transferred', 'quarantine'],
    default: 'active'
  },
  location: {
    paddock: {
      type: String,
      trim: true
    },
    barn: {
      type: String,
      trim: true
    },
    coordinates: {
      latitude: Number,
      longitude: Number
    }
  },
  
  // Health Information
  healthStatus: {
    type: String,
    enum: ['healthy', 'sick', 'injured', 'recovering', 'quarantine'],
    default: 'healthy'
  },
  lastHealthCheck: {
    type: Date
  },
  nextHealthCheck: {
    type: Date
  },
  
  // Breeding Information
  breedingStatus: {
    type: String,
    enum: ['available', 'pregnant', 'lactating', 'breeding', 'retired'],
    default: 'available'
  },
  lastBreedingDate: {
    type: Date
  },
  expectedDueDate: {
    type: Date
  },
  
  // Production Information
  productionType: {
    type: String,
    enum: ['meat', 'milk', 'eggs', 'wool', 'breeding', 'work', 'show']
  },
  productionRecords: [{
    date: {
      type: Date,
      required: true
    },
    type: {
      type: String,
      enum: ['milk', 'eggs', 'wool', 'weight'],
      required: true
    },
    quantity: {
      type: Number,
      required: true,
      min: 0
    },
    unit: {
      type: String,
      required: true
    },
    notes: String
  }],
  
  // Weight History
  weightHistory: [{
    date: {
      type: Date,
      required: true
    },
    weight: {
      type: Number,
      required: true,
      min: 0
    },
    measuredBy: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    notes: String
  }],
  
  // Financial Information
  purchasePrice: {
    type: Number,
    min: 0
  },
  purchaseDate: {
    type: Date
  },
  currentValue: {
    type: Number,
    min: 0
  },
  
  // Images and Documents
  images: [{
    url: String,
    caption: String,
    uploadDate: {
      type: Date,
      default: Date.now
    }
  }],
  documents: [{
    name: String,
    url: String,
    type: String,
    uploadDate: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Additional Information
  notes: {
    type: String,
    trim: true
  },
  isActive: {
    type: Boolean,
    default: true
  },
  
  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: true
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  }
}, {
  timestamps: true
});

// Indexes for performance
animalSchema.index({ tagNumber: 1 });
animalSchema.index({ species: 1 });
animalSchema.index({ breed: 1 });
animalSchema.index({ status: 1 });
animalSchema.index({ healthStatus: 1 });
animalSchema.index({ breedingStatus: 1 });
animalSchema.index({ rfidTag: 1 });
animalSchema.index({ createdAt: -1 });

// Virtual for age calculation
animalSchema.virtual('age').get(function() {
  if (!this.dateOfBirth) return null;
  
  const today = new Date();
  const birthDate = new Date(this.dateOfBirth);
  let age = today.getFullYear() - birthDate.getFullYear();
  const monthDiff = today.getMonth() - birthDate.getMonth();
  
  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
    age--;
  }
  
  return age;
});

// Virtual for age in days
animalSchema.virtual('ageInDays').get(function() {
  if (!this.dateOfBirth) return null;
  
  const today = new Date();
  const birthDate = new Date(this.dateOfBirth);
  const diffTime = Math.abs(today - birthDate);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays;
});

module.exports = mongoose.model('Animal', animalSchema);
