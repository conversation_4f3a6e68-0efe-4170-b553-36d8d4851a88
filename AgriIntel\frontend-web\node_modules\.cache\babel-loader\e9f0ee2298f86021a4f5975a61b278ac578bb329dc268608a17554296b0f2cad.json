{"ast": null, "code": "import React from'react';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const FeedingPage=()=>{const{t}=useTranslation();return/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:t('feeding.title')})}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Feed management features coming soon...\"})})]})});};export default FeedingPage;", "map": {"version": 3, "names": ["React", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "FeedingPage", "t", "className", "children"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/pages/feeding/FeedingPage.tsx"], "sourcesContent": ["import React from 'react';\nimport { useTranslation } from 'react-i18next';\n\nconst FeedingPage: React.FC = () => {\n  const { t } = useTranslation();\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">\n            {t('feeding.title')}\n          </h1>\n        </div>\n        <div className=\"card-body\">\n          <p className=\"text-gray-600\">\n            Feed management features coming soon...\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default FeedingPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,WAAqB,CAAGA,CAAA,GAAM,CAClC,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAE9B,mBACEE,IAAA,QAAKK,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBJ,KAAA,QAAKG,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBN,IAAA,QAAKK,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BN,IAAA,OAAIK,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC7CF,CAAC,CAAC,eAAe,CAAC,CACjB,CAAC,CACF,CAAC,cACNJ,IAAA,QAAKK,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBN,IAAA,MAAGK,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,yCAE7B,CAAG,CAAC,CACD,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}