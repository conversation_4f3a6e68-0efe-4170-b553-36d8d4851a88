{"ast": null, "code": "var pager = require('memory-pager');\nmodule.exports = Bitfield;\nfunction Bitfield(opts) {\n  if (!(this instanceof Bitfield)) return new Bitfield(opts);\n  if (!opts) opts = {};\n  if (Buffer.isBuffer(opts)) opts = {\n    buffer: opts\n  };\n  this.pageOffset = opts.pageOffset || 0;\n  this.pageSize = opts.pageSize || 1024;\n  this.pages = opts.pages || pager(this.pageSize);\n  this.byteLength = this.pages.length * this.pageSize;\n  this.length = 8 * this.byteLength;\n  if (!powerOfTwo(this.pageSize)) throw new Error('The page size should be a power of two');\n  this._trackUpdates = !!opts.trackUpdates;\n  this._pageMask = this.pageSize - 1;\n  if (opts.buffer) {\n    for (var i = 0; i < opts.buffer.length; i += this.pageSize) {\n      this.pages.set(i / this.pageSize, opts.buffer.slice(i, i + this.pageSize));\n    }\n    this.byteLength = opts.buffer.length;\n    this.length = 8 * this.byteLength;\n  }\n}\nBitfield.prototype.get = function (i) {\n  var o = i & 7;\n  var j = (i - o) / 8;\n  return !!(this.getByte(j) & 128 >> o);\n};\nBitfield.prototype.getByte = function (i) {\n  var o = i & this._pageMask;\n  var j = (i - o) / this.pageSize;\n  var page = this.pages.get(j, true);\n  return page ? page.buffer[o + this.pageOffset] : 0;\n};\nBitfield.prototype.set = function (i, v) {\n  var o = i & 7;\n  var j = (i - o) / 8;\n  var b = this.getByte(j);\n  return this.setByte(j, v ? b | 128 >> o : b & (255 ^ 128 >> o));\n};\nBitfield.prototype.toBuffer = function () {\n  var all = alloc(this.pages.length * this.pageSize);\n  for (var i = 0; i < this.pages.length; i++) {\n    var next = this.pages.get(i, true);\n    var allOffset = i * this.pageSize;\n    if (next) next.buffer.copy(all, allOffset, this.pageOffset, this.pageOffset + this.pageSize);\n  }\n  return all;\n};\nBitfield.prototype.setByte = function (i, b) {\n  var o = i & this._pageMask;\n  var j = (i - o) / this.pageSize;\n  var page = this.pages.get(j, false);\n  o += this.pageOffset;\n  if (page.buffer[o] === b) return false;\n  page.buffer[o] = b;\n  if (i >= this.byteLength) {\n    this.byteLength = i + 1;\n    this.length = this.byteLength * 8;\n  }\n  if (this._trackUpdates) this.pages.updated(page);\n  return true;\n};\nfunction alloc(n) {\n  if (Buffer.alloc) return Buffer.alloc(n);\n  var b = new Buffer(n);\n  b.fill(0);\n  return b;\n}\nfunction powerOfTwo(x) {\n  return !(x & x - 1);\n}", "map": {"version": 3, "names": ["pager", "require", "module", "exports", "Bitfield", "opts", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "pageOffset", "pageSize", "pages", "byteLength", "length", "powerOfTwo", "Error", "_trackUpdates", "trackUpdates", "_pageMask", "i", "set", "slice", "prototype", "get", "o", "j", "getByte", "page", "v", "b", "setByte", "<PERSON><PERSON><PERSON><PERSON>", "all", "alloc", "next", "allOffset", "copy", "updated", "n", "fill", "x"], "sources": ["C:/Users/<USER>/node_modules/sparse-bitfield/index.js"], "sourcesContent": ["var pager = require('memory-pager')\n\nmodule.exports = Bitfield\n\nfunction Bitfield (opts) {\n  if (!(this instanceof Bitfield)) return new Bitfield(opts)\n  if (!opts) opts = {}\n  if (Buffer.isBuffer(opts)) opts = {buffer: opts}\n\n  this.pageOffset = opts.pageOffset || 0\n  this.pageSize = opts.pageSize || 1024\n  this.pages = opts.pages || pager(this.pageSize)\n\n  this.byteLength = this.pages.length * this.pageSize\n  this.length = 8 * this.byteLength\n\n  if (!powerOfTwo(this.pageSize)) throw new Error('The page size should be a power of two')\n\n  this._trackUpdates = !!opts.trackUpdates\n  this._pageMask = this.pageSize - 1\n\n  if (opts.buffer) {\n    for (var i = 0; i < opts.buffer.length; i += this.pageSize) {\n      this.pages.set(i / this.pageSize, opts.buffer.slice(i, i + this.pageSize))\n    }\n    this.byteLength = opts.buffer.length\n    this.length = 8 * this.byteLength\n  }\n}\n\nBitfield.prototype.get = function (i) {\n  var o = i & 7\n  var j = (i - o) / 8\n\n  return !!(this.getByte(j) & (128 >> o))\n}\n\nBitfield.prototype.getByte = function (i) {\n  var o = i & this._pageMask\n  var j = (i - o) / this.pageSize\n  var page = this.pages.get(j, true)\n\n  return page ? page.buffer[o + this.pageOffset] : 0\n}\n\nBitfield.prototype.set = function (i, v) {\n  var o = i & 7\n  var j = (i - o) / 8\n  var b = this.getByte(j)\n\n  return this.setByte(j, v ? b | (128 >> o) : b & (255 ^ (128 >> o)))\n}\n\nBitfield.prototype.toBuffer = function () {\n  var all = alloc(this.pages.length * this.pageSize)\n\n  for (var i = 0; i < this.pages.length; i++) {\n    var next = this.pages.get(i, true)\n    var allOffset = i * this.pageSize\n    if (next) next.buffer.copy(all, allOffset, this.pageOffset, this.pageOffset + this.pageSize)\n  }\n\n  return all\n}\n\nBitfield.prototype.setByte = function (i, b) {\n  var o = i & this._pageMask\n  var j = (i - o) / this.pageSize\n  var page = this.pages.get(j, false)\n\n  o += this.pageOffset\n\n  if (page.buffer[o] === b) return false\n  page.buffer[o] = b\n\n  if (i >= this.byteLength) {\n    this.byteLength = i + 1\n    this.length = this.byteLength * 8\n  }\n\n  if (this._trackUpdates) this.pages.updated(page)\n\n  return true\n}\n\nfunction alloc (n) {\n  if (Buffer.alloc) return Buffer.alloc(n)\n  var b = new Buffer(n)\n  b.fill(0)\n  return b\n}\n\nfunction powerOfTwo (x) {\n  return !(x & (x - 1))\n}\n"], "mappings": "AAAA,IAAIA,KAAK,GAAGC,OAAO,CAAC,cAAc,CAAC;AAEnCC,MAAM,CAACC,OAAO,GAAGC,QAAQ;AAEzB,SAASA,QAAQA,CAAEC,IAAI,EAAE;EACvB,IAAI,EAAE,IAAI,YAAYD,QAAQ,CAAC,EAAE,OAAO,IAAIA,QAAQ,CAACC,IAAI,CAAC;EAC1D,IAAI,CAACA,IAAI,EAAEA,IAAI,GAAG,CAAC,CAAC;EACpB,IAAIC,MAAM,CAACC,QAAQ,CAACF,IAAI,CAAC,EAAEA,IAAI,GAAG;IAACG,MAAM,EAAEH;EAAI,CAAC;EAEhD,IAAI,CAACI,UAAU,GAAGJ,IAAI,CAACI,UAAU,IAAI,CAAC;EACtC,IAAI,CAACC,QAAQ,GAAGL,IAAI,CAACK,QAAQ,IAAI,IAAI;EACrC,IAAI,CAACC,KAAK,GAAGN,IAAI,CAACM,KAAK,IAAIX,KAAK,CAAC,IAAI,CAACU,QAAQ,CAAC;EAE/C,IAAI,CAACE,UAAU,GAAG,IAAI,CAACD,KAAK,CAACE,MAAM,GAAG,IAAI,CAACH,QAAQ;EACnD,IAAI,CAACG,MAAM,GAAG,CAAC,GAAG,IAAI,CAACD,UAAU;EAEjC,IAAI,CAACE,UAAU,CAAC,IAAI,CAACJ,QAAQ,CAAC,EAAE,MAAM,IAAIK,KAAK,CAAC,wCAAwC,CAAC;EAEzF,IAAI,CAACC,aAAa,GAAG,CAAC,CAACX,IAAI,CAACY,YAAY;EACxC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACR,QAAQ,GAAG,CAAC;EAElC,IAAIL,IAAI,CAACG,MAAM,EAAE;IACf,KAAK,IAAIW,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,IAAI,CAACG,MAAM,CAACK,MAAM,EAAEM,CAAC,IAAI,IAAI,CAACT,QAAQ,EAAE;MAC1D,IAAI,CAACC,KAAK,CAACS,GAAG,CAACD,CAAC,GAAG,IAAI,CAACT,QAAQ,EAAEL,IAAI,CAACG,MAAM,CAACa,KAAK,CAACF,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACT,QAAQ,CAAC,CAAC;IAC5E;IACA,IAAI,CAACE,UAAU,GAAGP,IAAI,CAACG,MAAM,CAACK,MAAM;IACpC,IAAI,CAACA,MAAM,GAAG,CAAC,GAAG,IAAI,CAACD,UAAU;EACnC;AACF;AAEAR,QAAQ,CAACkB,SAAS,CAACC,GAAG,GAAG,UAAUJ,CAAC,EAAE;EACpC,IAAIK,CAAC,GAAGL,CAAC,GAAG,CAAC;EACb,IAAIM,CAAC,GAAG,CAACN,CAAC,GAAGK,CAAC,IAAI,CAAC;EAEnB,OAAO,CAAC,EAAE,IAAI,CAACE,OAAO,CAACD,CAAC,CAAC,GAAI,GAAG,IAAID,CAAE,CAAC;AACzC,CAAC;AAEDpB,QAAQ,CAACkB,SAAS,CAACI,OAAO,GAAG,UAAUP,CAAC,EAAE;EACxC,IAAIK,CAAC,GAAGL,CAAC,GAAG,IAAI,CAACD,SAAS;EAC1B,IAAIO,CAAC,GAAG,CAACN,CAAC,GAAGK,CAAC,IAAI,IAAI,CAACd,QAAQ;EAC/B,IAAIiB,IAAI,GAAG,IAAI,CAAChB,KAAK,CAACY,GAAG,CAACE,CAAC,EAAE,IAAI,CAAC;EAElC,OAAOE,IAAI,GAAGA,IAAI,CAACnB,MAAM,CAACgB,CAAC,GAAG,IAAI,CAACf,UAAU,CAAC,GAAG,CAAC;AACpD,CAAC;AAEDL,QAAQ,CAACkB,SAAS,CAACF,GAAG,GAAG,UAAUD,CAAC,EAAES,CAAC,EAAE;EACvC,IAAIJ,CAAC,GAAGL,CAAC,GAAG,CAAC;EACb,IAAIM,CAAC,GAAG,CAACN,CAAC,GAAGK,CAAC,IAAI,CAAC;EACnB,IAAIK,CAAC,GAAG,IAAI,CAACH,OAAO,CAACD,CAAC,CAAC;EAEvB,OAAO,IAAI,CAACK,OAAO,CAACL,CAAC,EAAEG,CAAC,GAAGC,CAAC,GAAI,GAAG,IAAIL,CAAE,GAAGK,CAAC,IAAI,GAAG,GAAI,GAAG,IAAIL,CAAE,CAAC,CAAC;AACrE,CAAC;AAEDpB,QAAQ,CAACkB,SAAS,CAACS,QAAQ,GAAG,YAAY;EACxC,IAAIC,GAAG,GAAGC,KAAK,CAAC,IAAI,CAACtB,KAAK,CAACE,MAAM,GAAG,IAAI,CAACH,QAAQ,CAAC;EAElD,KAAK,IAAIS,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACR,KAAK,CAACE,MAAM,EAAEM,CAAC,EAAE,EAAE;IAC1C,IAAIe,IAAI,GAAG,IAAI,CAACvB,KAAK,CAACY,GAAG,CAACJ,CAAC,EAAE,IAAI,CAAC;IAClC,IAAIgB,SAAS,GAAGhB,CAAC,GAAG,IAAI,CAACT,QAAQ;IACjC,IAAIwB,IAAI,EAAEA,IAAI,CAAC1B,MAAM,CAAC4B,IAAI,CAACJ,GAAG,EAAEG,SAAS,EAAE,IAAI,CAAC1B,UAAU,EAAE,IAAI,CAACA,UAAU,GAAG,IAAI,CAACC,QAAQ,CAAC;EAC9F;EAEA,OAAOsB,GAAG;AACZ,CAAC;AAED5B,QAAQ,CAACkB,SAAS,CAACQ,OAAO,GAAG,UAAUX,CAAC,EAAEU,CAAC,EAAE;EAC3C,IAAIL,CAAC,GAAGL,CAAC,GAAG,IAAI,CAACD,SAAS;EAC1B,IAAIO,CAAC,GAAG,CAACN,CAAC,GAAGK,CAAC,IAAI,IAAI,CAACd,QAAQ;EAC/B,IAAIiB,IAAI,GAAG,IAAI,CAAChB,KAAK,CAACY,GAAG,CAACE,CAAC,EAAE,KAAK,CAAC;EAEnCD,CAAC,IAAI,IAAI,CAACf,UAAU;EAEpB,IAAIkB,IAAI,CAACnB,MAAM,CAACgB,CAAC,CAAC,KAAKK,CAAC,EAAE,OAAO,KAAK;EACtCF,IAAI,CAACnB,MAAM,CAACgB,CAAC,CAAC,GAAGK,CAAC;EAElB,IAAIV,CAAC,IAAI,IAAI,CAACP,UAAU,EAAE;IACxB,IAAI,CAACA,UAAU,GAAGO,CAAC,GAAG,CAAC;IACvB,IAAI,CAACN,MAAM,GAAG,IAAI,CAACD,UAAU,GAAG,CAAC;EACnC;EAEA,IAAI,IAAI,CAACI,aAAa,EAAE,IAAI,CAACL,KAAK,CAAC0B,OAAO,CAACV,IAAI,CAAC;EAEhD,OAAO,IAAI;AACb,CAAC;AAED,SAASM,KAAKA,CAAEK,CAAC,EAAE;EACjB,IAAIhC,MAAM,CAAC2B,KAAK,EAAE,OAAO3B,MAAM,CAAC2B,KAAK,CAACK,CAAC,CAAC;EACxC,IAAIT,CAAC,GAAG,IAAIvB,MAAM,CAACgC,CAAC,CAAC;EACrBT,CAAC,CAACU,IAAI,CAAC,CAAC,CAAC;EACT,OAAOV,CAAC;AACV;AAEA,SAASf,UAAUA,CAAE0B,CAAC,EAAE;EACtB,OAAO,EAAEA,CAAC,GAAIA,CAAC,GAAG,CAAE,CAAC;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}