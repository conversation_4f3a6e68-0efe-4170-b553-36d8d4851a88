{"ast": null, "code": "import axios from'axios';const API_BASE_URL=process.env.REACT_APP_API_URL||'http://localhost:3001/api';// Create axios instance\nconst apiClient=axios.create({baseURL:API_BASE_URL,headers:{'Content-Type':'application/json'}});// Request interceptor to add auth token\napiClient.interceptors.request.use(config=>{const token=localStorage.getItem('token');if(token){config.headers.Authorization=\"Bearer \".concat(token);}return config;},error=>{return Promise.reject(error);});// Response interceptor to handle token refresh\napiClient.interceptors.response.use(response=>response,async error=>{var _error$response;const originalRequest=error.config;if(((_error$response=error.response)===null||_error$response===void 0?void 0:_error$response.status)===401&&!originalRequest._retry){originalRequest._retry=true;try{const refreshToken=localStorage.getItem('refreshToken');if(refreshToken){const response=await axios.post(\"\".concat(API_BASE_URL,\"/auth/refresh-token\"),{refreshToken});const{token,refreshToken:newRefreshToken}=response.data.data;localStorage.setItem('token',token);localStorage.setItem('refreshToken',newRefreshToken);// Retry original request with new token\noriginalRequest.headers.Authorization=\"Bearer \".concat(token);return apiClient(originalRequest);}}catch(refreshError){// Refresh failed, redirect to login\nlocalStorage.removeItem('token');localStorage.removeItem('refreshToken');window.location.href='/login';return Promise.reject(refreshError);}}return Promise.reject(error);});export const authAPI={// Login user\nlogin:async credentials=>{const response=await apiClient.post('/auth/login',credentials);return response.data;},// Register user\nregister:async userData=>{const response=await apiClient.post('/auth/register',userData);return response.data;},// Get current user\ngetCurrentUser:async()=>{const response=await apiClient.get('/auth/me');return response.data;},// Refresh token\nrefreshToken:async refreshToken=>{const response=await apiClient.post('/auth/refresh-token',{refreshToken});return response.data;},// Logout user\nlogout:async refreshToken=>{const response=await apiClient.post('/auth/logout',{refreshToken});return response.data;},// Change password\nchangePassword:async passwordData=>{const response=await apiClient.post('/auth/change-password',passwordData);return response.data;},// Forgot password\nforgotPassword:async email=>{const response=await apiClient.post('/auth/forgot-password',{email});return response.data;},// Reset password\nresetPassword:async(token,newPassword)=>{const response=await apiClient.post('/auth/reset-password',{token,newPassword});return response.data;},// Verify email\nverifyEmail:async token=>{const response=await apiClient.post('/auth/verify-email',{token});return response.data;},// Resend verification email\nresendVerificationEmail:async()=>{const response=await apiClient.post('/auth/resend-verification');return response.data;}};export default apiClient;", "map": {"version": 3, "names": ["axios", "API_BASE_URL", "process", "env", "REACT_APP_API_URL", "apiClient", "create", "baseURL", "headers", "interceptors", "request", "use", "config", "token", "localStorage", "getItem", "Authorization", "concat", "error", "Promise", "reject", "response", "_error$response", "originalRequest", "status", "_retry", "refreshToken", "post", "newRefreshToken", "data", "setItem", "refreshError", "removeItem", "window", "location", "href", "authAPI", "login", "credentials", "register", "userData", "getCurrentUser", "get", "logout", "changePassword", "passwordData", "forgotPassword", "email", "resetPassword", "newPassword", "verifyEmail", "resendVerificationEmail"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/services/api/authAPI.ts"], "sourcesContent": ["import axios from 'axios';\n\nconst API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';\n\n// Create axios instance\nconst apiClient = axios.create({\n  baseURL: API_BASE_URL,\n  headers: {\n    'Content-Type': 'application/json',\n  },\n});\n\n// Request interceptor to add auth token\napiClient.interceptors.request.use(\n  (config) => {\n    const token = localStorage.getItem('token');\n    if (token) {\n      config.headers.Authorization = `Bearer ${token}`;\n    }\n    return config;\n  },\n  (error) => {\n    return Promise.reject(error);\n  }\n);\n\n// Response interceptor to handle token refresh\napiClient.interceptors.response.use(\n  (response) => response,\n  async (error) => {\n    const originalRequest = error.config;\n\n    if (error.response?.status === 401 && !originalRequest._retry) {\n      originalRequest._retry = true;\n\n      try {\n        const refreshToken = localStorage.getItem('refreshToken');\n        if (refreshToken) {\n          const response = await axios.post(`${API_BASE_URL}/auth/refresh-token`, {\n            refreshToken,\n          });\n\n          const { token, refreshToken: newRefreshToken } = response.data.data;\n          localStorage.setItem('token', token);\n          localStorage.setItem('refreshToken', newRefreshToken);\n\n          // Retry original request with new token\n          originalRequest.headers.Authorization = `Bearer ${token}`;\n          return apiClient(originalRequest);\n        }\n      } catch (refreshError) {\n        // Refresh failed, redirect to login\n        localStorage.removeItem('token');\n        localStorage.removeItem('refreshToken');\n        window.location.href = '/login';\n        return Promise.reject(refreshError);\n      }\n    }\n\n    return Promise.reject(error);\n  }\n);\n\nexport interface LoginCredentials {\n  username: string;\n  password: string;\n}\n\nexport interface RegisterData {\n  username: string;\n  email: string;\n  password: string;\n  firstName: string;\n  lastName: string;\n  role?: string;\n  phoneNumber?: string;\n  department?: string;\n}\n\nexport interface ChangePasswordData {\n  currentPassword: string;\n  newPassword: string;\n}\n\nexport interface AuthResponse {\n  success: boolean;\n  message: string;\n  data: {\n    user: any;\n    token: string;\n    refreshToken: string;\n  };\n}\n\nexport interface UserResponse {\n  success: boolean;\n  data: {\n    user: any;\n  };\n}\n\nexport interface MessageResponse {\n  success: boolean;\n  message: string;\n}\n\nexport interface TokenResponse {\n  success: boolean;\n  data: {\n    token: string;\n    refreshToken: string;\n  };\n}\n\nexport const authAPI = {\n  // Login user\n  login: async (credentials: LoginCredentials): Promise<AuthResponse> => {\n    const response = await apiClient.post('/auth/login', credentials);\n    return response.data;\n  },\n\n  // Register user\n  register: async (userData: RegisterData): Promise<AuthResponse> => {\n    const response = await apiClient.post('/auth/register', userData);\n    return response.data;\n  },\n\n  // Get current user\n  getCurrentUser: async (): Promise<UserResponse> => {\n    const response = await apiClient.get('/auth/me');\n    return response.data;\n  },\n\n  // Refresh token\n  refreshToken: async (refreshToken: string): Promise<TokenResponse> => {\n    const response = await apiClient.post('/auth/refresh-token', { refreshToken });\n    return response.data;\n  },\n\n  // Logout user\n  logout: async (refreshToken?: string): Promise<MessageResponse> => {\n    const response = await apiClient.post('/auth/logout', { refreshToken });\n    return response.data;\n  },\n\n  // Change password\n  changePassword: async (passwordData: ChangePasswordData): Promise<MessageResponse> => {\n    const response = await apiClient.post('/auth/change-password', passwordData);\n    return response.data;\n  },\n\n  // Forgot password\n  forgotPassword: async (email: string): Promise<MessageResponse> => {\n    const response = await apiClient.post('/auth/forgot-password', { email });\n    return response.data;\n  },\n\n  // Reset password\n  resetPassword: async (token: string, newPassword: string): Promise<MessageResponse> => {\n    const response = await apiClient.post('/auth/reset-password', { token, newPassword });\n    return response.data;\n  },\n\n  // Verify email\n  verifyEmail: async (token: string): Promise<MessageResponse> => {\n    const response = await apiClient.post('/auth/verify-email', { token });\n    return response.data;\n  },\n\n  // Resend verification email\n  resendVerificationEmail: async (): Promise<MessageResponse> => {\n    const response = await apiClient.post('/auth/resend-verification');\n    return response.data;\n  },\n};\n\nexport default apiClient;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAEzB,KAAM,CAAAC,YAAY,CAAGC,OAAO,CAACC,GAAG,CAACC,iBAAiB,EAAI,2BAA2B,CAEjF;AACA,KAAM,CAAAC,SAAS,CAAGL,KAAK,CAACM,MAAM,CAAC,CAC7BC,OAAO,CAAEN,YAAY,CACrBO,OAAO,CAAE,CACP,cAAc,CAAE,kBAClB,CACF,CAAC,CAAC,CAEF;AACAH,SAAS,CAACI,YAAY,CAACC,OAAO,CAACC,GAAG,CAC/BC,MAAM,EAAK,CACV,KAAM,CAAAC,KAAK,CAAGC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,CAC3C,GAAIF,KAAK,CAAE,CACTD,MAAM,CAACJ,OAAO,CAACQ,aAAa,WAAAC,MAAA,CAAaJ,KAAK,CAAE,CAClD,CACA,MAAO,CAAAD,MAAM,CACf,CAAC,CACAM,KAAK,EAAK,CACT,MAAO,CAAAC,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAED;AACAb,SAAS,CAACI,YAAY,CAACY,QAAQ,CAACV,GAAG,CAChCU,QAAQ,EAAKA,QAAQ,CACtB,KAAO,CAAAH,KAAK,EAAK,KAAAI,eAAA,CACf,KAAM,CAAAC,eAAe,CAAGL,KAAK,CAACN,MAAM,CAEpC,GAAI,EAAAU,eAAA,CAAAJ,KAAK,CAACG,QAAQ,UAAAC,eAAA,iBAAdA,eAAA,CAAgBE,MAAM,IAAK,GAAG,EAAI,CAACD,eAAe,CAACE,MAAM,CAAE,CAC7DF,eAAe,CAACE,MAAM,CAAG,IAAI,CAE7B,GAAI,CACF,KAAM,CAAAC,YAAY,CAAGZ,YAAY,CAACC,OAAO,CAAC,cAAc,CAAC,CACzD,GAAIW,YAAY,CAAE,CAChB,KAAM,CAAAL,QAAQ,CAAG,KAAM,CAAArB,KAAK,CAAC2B,IAAI,IAAAV,MAAA,CAAIhB,YAAY,wBAAuB,CACtEyB,YACF,CAAC,CAAC,CAEF,KAAM,CAAEb,KAAK,CAAEa,YAAY,CAAEE,eAAgB,CAAC,CAAGP,QAAQ,CAACQ,IAAI,CAACA,IAAI,CACnEf,YAAY,CAACgB,OAAO,CAAC,OAAO,CAAEjB,KAAK,CAAC,CACpCC,YAAY,CAACgB,OAAO,CAAC,cAAc,CAAEF,eAAe,CAAC,CAErD;AACAL,eAAe,CAACf,OAAO,CAACQ,aAAa,WAAAC,MAAA,CAAaJ,KAAK,CAAE,CACzD,MAAO,CAAAR,SAAS,CAACkB,eAAe,CAAC,CACnC,CACF,CAAE,MAAOQ,YAAY,CAAE,CACrB;AACAjB,YAAY,CAACkB,UAAU,CAAC,OAAO,CAAC,CAChClB,YAAY,CAACkB,UAAU,CAAC,cAAc,CAAC,CACvCC,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAG,QAAQ,CAC/B,MAAO,CAAAhB,OAAO,CAACC,MAAM,CAACW,YAAY,CAAC,CACrC,CACF,CAEA,MAAO,CAAAZ,OAAO,CAACC,MAAM,CAACF,KAAK,CAAC,CAC9B,CACF,CAAC,CAqDD,MAAO,MAAM,CAAAkB,OAAO,CAAG,CACrB;AACAC,KAAK,CAAE,KAAO,CAAAC,WAA6B,EAA4B,CACrE,KAAM,CAAAjB,QAAQ,CAAG,KAAM,CAAAhB,SAAS,CAACsB,IAAI,CAAC,aAAa,CAAEW,WAAW,CAAC,CACjE,MAAO,CAAAjB,QAAQ,CAACQ,IAAI,CACtB,CAAC,CAED;AACAU,QAAQ,CAAE,KAAO,CAAAC,QAAsB,EAA4B,CACjE,KAAM,CAAAnB,QAAQ,CAAG,KAAM,CAAAhB,SAAS,CAACsB,IAAI,CAAC,gBAAgB,CAAEa,QAAQ,CAAC,CACjE,MAAO,CAAAnB,QAAQ,CAACQ,IAAI,CACtB,CAAC,CAED;AACAY,cAAc,CAAE,KAAAA,CAAA,GAAmC,CACjD,KAAM,CAAApB,QAAQ,CAAG,KAAM,CAAAhB,SAAS,CAACqC,GAAG,CAAC,UAAU,CAAC,CAChD,MAAO,CAAArB,QAAQ,CAACQ,IAAI,CACtB,CAAC,CAED;AACAH,YAAY,CAAE,KAAO,CAAAA,YAAoB,EAA6B,CACpE,KAAM,CAAAL,QAAQ,CAAG,KAAM,CAAAhB,SAAS,CAACsB,IAAI,CAAC,qBAAqB,CAAE,CAAED,YAAa,CAAC,CAAC,CAC9E,MAAO,CAAAL,QAAQ,CAACQ,IAAI,CACtB,CAAC,CAED;AACAc,MAAM,CAAE,KAAO,CAAAjB,YAAqB,EAA+B,CACjE,KAAM,CAAAL,QAAQ,CAAG,KAAM,CAAAhB,SAAS,CAACsB,IAAI,CAAC,cAAc,CAAE,CAAED,YAAa,CAAC,CAAC,CACvE,MAAO,CAAAL,QAAQ,CAACQ,IAAI,CACtB,CAAC,CAED;AACAe,cAAc,CAAE,KAAO,CAAAC,YAAgC,EAA+B,CACpF,KAAM,CAAAxB,QAAQ,CAAG,KAAM,CAAAhB,SAAS,CAACsB,IAAI,CAAC,uBAAuB,CAAEkB,YAAY,CAAC,CAC5E,MAAO,CAAAxB,QAAQ,CAACQ,IAAI,CACtB,CAAC,CAED;AACAiB,cAAc,CAAE,KAAO,CAAAC,KAAa,EAA+B,CACjE,KAAM,CAAA1B,QAAQ,CAAG,KAAM,CAAAhB,SAAS,CAACsB,IAAI,CAAC,uBAAuB,CAAE,CAAEoB,KAAM,CAAC,CAAC,CACzE,MAAO,CAAA1B,QAAQ,CAACQ,IAAI,CACtB,CAAC,CAED;AACAmB,aAAa,CAAE,KAAAA,CAAOnC,KAAa,CAAEoC,WAAmB,GAA+B,CACrF,KAAM,CAAA5B,QAAQ,CAAG,KAAM,CAAAhB,SAAS,CAACsB,IAAI,CAAC,sBAAsB,CAAE,CAAEd,KAAK,CAAEoC,WAAY,CAAC,CAAC,CACrF,MAAO,CAAA5B,QAAQ,CAACQ,IAAI,CACtB,CAAC,CAED;AACAqB,WAAW,CAAE,KAAO,CAAArC,KAAa,EAA+B,CAC9D,KAAM,CAAAQ,QAAQ,CAAG,KAAM,CAAAhB,SAAS,CAACsB,IAAI,CAAC,oBAAoB,CAAE,CAAEd,KAAM,CAAC,CAAC,CACtE,MAAO,CAAAQ,QAAQ,CAACQ,IAAI,CACtB,CAAC,CAED;AACAsB,uBAAuB,CAAE,KAAAA,CAAA,GAAsC,CAC7D,KAAM,CAAA9B,QAAQ,CAAG,KAAM,CAAAhB,SAAS,CAACsB,IAAI,CAAC,2BAA2B,CAAC,CAClE,MAAO,CAAAN,QAAQ,CAACQ,IAAI,CACtB,CACF,CAAC,CAED,cAAe,CAAAxB,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}