import React, { useEffect } from 'react';
import { useDispatch } from 'react-redux';
import { useTranslation } from 'react-i18next';
import { AppDispatch } from '../../store/store';
import { setPageTitle, setBreadcrumbs } from '../../store/slices/uiSlice';

const AnimalsPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { t } = useTranslation();

  useEffect(() => {
    dispatch(setPageTitle(t('animals.title')));
    dispatch(setBreadcrumbs([
      { label: t('navigation.dashboard'), path: '/dashboard' },
      { label: t('animals.title') }
    ]));
  }, [dispatch, t]);

  return (
    <div className="space-y-6">
      <div className="card">
        <div className="card-header">
          <h1 className="text-2xl font-bold text-gray-900">
            {t('animals.title')}
          </h1>
        </div>
        <div className="card-body">
          <p className="text-gray-600">
            Animal management features coming soon...
          </p>
        </div>
      </div>
    </div>
  );
};

export default AnimalsPage;
