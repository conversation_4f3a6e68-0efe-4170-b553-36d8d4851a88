{"ast": null, "code": "function length(array) {\n  return array.length | 0;\n}\nfunction empty(length) {\n  return !(length > 0);\n}\nfunction arrayify(values) {\n  return typeof values !== \"object\" || \"length\" in values ? values : Array.from(values);\n}\nfunction reducer(reduce) {\n  return values => reduce(...values);\n}\nexport default function cross(...values) {\n  const reduce = typeof values[values.length - 1] === \"function\" && reducer(values.pop());\n  values = values.map(arrayify);\n  const lengths = values.map(length);\n  const j = values.length - 1;\n  const index = new Array(j + 1).fill(0);\n  const product = [];\n  if (j < 0 || lengths.some(empty)) return product;\n  while (true) {\n    product.push(index.map((j, i) => values[i][j]));\n    let i = j;\n    while (++index[i] === lengths[i]) {\n      if (i === 0) return reduce ? product.map(reduce) : product;\n      index[i--] = 0;\n    }\n  }\n}", "map": {"version": 3, "names": ["length", "array", "empty", "arrayify", "values", "Array", "from", "reducer", "reduce", "cross", "pop", "map", "lengths", "j", "index", "fill", "product", "some", "push", "i"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/d3-array/src/cross.js"], "sourcesContent": ["function length(array) {\n  return array.length | 0;\n}\n\nfunction empty(length) {\n  return !(length > 0);\n}\n\nfunction arrayify(values) {\n  return typeof values !== \"object\" || \"length\" in values ? values : Array.from(values);\n}\n\nfunction reducer(reduce) {\n  return values => reduce(...values);\n}\n\nexport default function cross(...values) {\n  const reduce = typeof values[values.length - 1] === \"function\" && reducer(values.pop());\n  values = values.map(arrayify);\n  const lengths = values.map(length);\n  const j = values.length - 1;\n  const index = new Array(j + 1).fill(0);\n  const product = [];\n  if (j < 0 || lengths.some(empty)) return product;\n  while (true) {\n    product.push(index.map((j, i) => values[i][j]));\n    let i = j;\n    while (++index[i] === lengths[i]) {\n      if (i === 0) return reduce ? product.map(reduce) : product;\n      index[i--] = 0;\n    }\n  }\n}\n"], "mappings": "AAAA,SAASA,MAAMA,CAACC,KAAK,EAAE;EACrB,OAAOA,KAAK,CAACD,MAAM,GAAG,CAAC;AACzB;AAEA,SAASE,KAAKA,CAACF,MAAM,EAAE;EACrB,OAAO,EAAEA,MAAM,GAAG,CAAC,CAAC;AACtB;AAEA,SAASG,QAAQA,CAACC,MAAM,EAAE;EACxB,OAAO,OAAOA,MAAM,KAAK,QAAQ,IAAI,QAAQ,IAAIA,MAAM,GAAGA,MAAM,GAAGC,KAAK,CAACC,IAAI,CAACF,MAAM,CAAC;AACvF;AAEA,SAASG,OAAOA,CAACC,MAAM,EAAE;EACvB,OAAOJ,MAAM,IAAII,MAAM,CAAC,GAAGJ,MAAM,CAAC;AACpC;AAEA,eAAe,SAASK,KAAKA,CAAC,GAAGL,MAAM,EAAE;EACvC,MAAMI,MAAM,GAAG,OAAOJ,MAAM,CAACA,MAAM,CAACJ,MAAM,GAAG,CAAC,CAAC,KAAK,UAAU,IAAIO,OAAO,CAACH,MAAM,CAACM,GAAG,CAAC,CAAC,CAAC;EACvFN,MAAM,GAAGA,MAAM,CAACO,GAAG,CAACR,QAAQ,CAAC;EAC7B,MAAMS,OAAO,GAAGR,MAAM,CAACO,GAAG,CAACX,MAAM,CAAC;EAClC,MAAMa,CAAC,GAAGT,MAAM,CAACJ,MAAM,GAAG,CAAC;EAC3B,MAAMc,KAAK,GAAG,IAAIT,KAAK,CAACQ,CAAC,GAAG,CAAC,CAAC,CAACE,IAAI,CAAC,CAAC,CAAC;EACtC,MAAMC,OAAO,GAAG,EAAE;EAClB,IAAIH,CAAC,GAAG,CAAC,IAAID,OAAO,CAACK,IAAI,CAACf,KAAK,CAAC,EAAE,OAAOc,OAAO;EAChD,OAAO,IAAI,EAAE;IACXA,OAAO,CAACE,IAAI,CAACJ,KAAK,CAACH,GAAG,CAAC,CAACE,CAAC,EAAEM,CAAC,KAAKf,MAAM,CAACe,CAAC,CAAC,CAACN,CAAC,CAAC,CAAC,CAAC;IAC/C,IAAIM,CAAC,GAAGN,CAAC;IACT,OAAO,EAAEC,KAAK,CAACK,CAAC,CAAC,KAAKP,OAAO,CAACO,CAAC,CAAC,EAAE;MAChC,IAAIA,CAAC,KAAK,CAAC,EAAE,OAAOX,MAAM,GAAGQ,OAAO,CAACL,GAAG,CAACH,MAAM,CAAC,GAAGQ,OAAO;MAC1DF,KAAK,CAACK,CAAC,EAAE,CAAC,GAAG,CAAC;IAChB;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}