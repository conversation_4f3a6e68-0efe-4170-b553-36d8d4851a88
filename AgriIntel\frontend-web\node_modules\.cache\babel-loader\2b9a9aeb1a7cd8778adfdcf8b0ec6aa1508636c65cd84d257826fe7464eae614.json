{"ast": null, "code": "import { useStore as u } from '../../hooks/use-store.js';\nimport { useIsoMorphicEffect as s } from '../use-iso-morphic-effect.js';\nimport { overflows as t } from './overflow-store.js';\nfunction p(e, r, n) {\n  let f = u(t),\n    o = e ? f.get(e) : void 0,\n    i = o ? o.count > 0 : !1;\n  return s(() => {\n    if (!(!e || !r)) return t.dispatch(\"PUSH\", e, n), () => t.dispatch(\"POP\", e, n);\n  }, [r, e]), i;\n}\nexport { p as useDocumentOverflowLockedEffect };", "map": {"version": 3, "names": ["useStore", "u", "useIsoMorphicEffect", "s", "overflows", "t", "p", "e", "r", "n", "f", "o", "get", "i", "count", "dispatch", "useDocumentOverflowLockedEffect"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/hooks/document-overflow/use-document-overflow.js"], "sourcesContent": ["import{useStore as u}from'../../hooks/use-store.js';import{useIsoMorphicEffect as s}from'../use-iso-morphic-effect.js';import{overflows as t}from'./overflow-store.js';function p(e,r,n){let f=u(t),o=e?f.get(e):void 0,i=o?o.count>0:!1;return s(()=>{if(!(!e||!r))return t.dispatch(\"PUSH\",e,n),()=>t.dispatch(\"POP\",e,n)},[r,e]),i}export{p as useDocumentOverflowLockedEffect};\n"], "mappings": "AAAA,SAAOA,QAAQ,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,SAAS,IAAIC,CAAC,QAAK,qBAAqB;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACT,CAAC,CAACI,CAAC,CAAC;IAACM,CAAC,GAACJ,CAAC,GAACG,CAAC,CAACE,GAAG,CAACL,CAAC,CAAC,GAAC,KAAK,CAAC;IAACM,CAAC,GAACF,CAAC,GAACA,CAAC,CAACG,KAAK,GAAC,CAAC,GAAC,CAAC,CAAC;EAAC,OAAOX,CAAC,CAAC,MAAI;IAAC,IAAG,EAAE,CAACI,CAAC,IAAE,CAACC,CAAC,CAAC,EAAC,OAAOH,CAAC,CAACU,QAAQ,CAAC,MAAM,EAACR,CAAC,EAACE,CAAC,CAAC,EAAC,MAAIJ,CAAC,CAACU,QAAQ,CAAC,KAAK,EAACR,CAAC,EAACE,CAAC,CAAC;EAAA,CAAC,EAAC,CAACD,CAAC,EAACD,CAAC,CAAC,CAAC,EAACM,CAAC;AAAA;AAAC,SAAOP,CAAC,IAAIU,+BAA+B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}