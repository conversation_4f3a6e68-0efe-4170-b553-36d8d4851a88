{"ast": null, "code": "\"use strict\";\n\nconst tr46 = require(\"tr46\");\nconst infra = require(\"./infra\");\nconst {\n  utf8DecodeWithoutBOM\n} = require(\"./encoding\");\nconst {\n  percentDecodeString,\n  utf8PercentEncodeCodePoint,\n  utf8PercentEncodeString,\n  isC0ControlPercentEncode,\n  isFragmentPercentEncode,\n  isQueryPercentEncode,\n  isSpecialQueryPercentEncode,\n  isPathPercentEncode,\n  isUserinfoPercentEncode\n} = require(\"./percent-encoding\");\nfunction p(char) {\n  return char.codePointAt(0);\n}\nconst specialSchemes = {\n  ftp: 21,\n  file: null,\n  http: 80,\n  https: 443,\n  ws: 80,\n  wss: 443\n};\nconst failure = Symbol(\"failure\");\nfunction countSymbols(str) {\n  return [...str].length;\n}\nfunction at(input, idx) {\n  const c = input[idx];\n  return isNaN(c) ? undefined : String.fromCodePoint(c);\n}\nfunction isSingleDot(buffer) {\n  return buffer === \".\" || buffer.toLowerCase() === \"%2e\";\n}\nfunction isDoubleDot(buffer) {\n  buffer = buffer.toLowerCase();\n  return buffer === \"..\" || buffer === \"%2e.\" || buffer === \".%2e\" || buffer === \"%2e%2e\";\n}\nfunction isWindowsDriveLetterCodePoints(cp1, cp2) {\n  return infra.isASCIIAlpha(cp1) && (cp2 === p(\":\") || cp2 === p(\"|\"));\n}\nfunction isWindowsDriveLetterString(string) {\n  return string.length === 2 && infra.isASCIIAlpha(string.codePointAt(0)) && (string[1] === \":\" || string[1] === \"|\");\n}\nfunction isNormalizedWindowsDriveLetterString(string) {\n  return string.length === 2 && infra.isASCIIAlpha(string.codePointAt(0)) && string[1] === \":\";\n}\nfunction containsForbiddenHostCodePoint(string) {\n  return string.search(/\\u0000|\\u0009|\\u000A|\\u000D|\\u0020|#|\\/|:|<|>|\\?|@|\\[|\\\\|\\]|\\^|\\|/u) !== -1;\n}\nfunction containsForbiddenDomainCodePoint(string) {\n  return containsForbiddenHostCodePoint(string) || string.search(/[\\u0000-\\u001F]|%|\\u007F/u) !== -1;\n}\nfunction isSpecialScheme(scheme) {\n  return specialSchemes[scheme] !== undefined;\n}\nfunction isSpecial(url) {\n  return isSpecialScheme(url.scheme);\n}\nfunction isNotSpecial(url) {\n  return !isSpecialScheme(url.scheme);\n}\nfunction defaultPort(scheme) {\n  return specialSchemes[scheme];\n}\nfunction parseIPv4Number(input) {\n  if (input === \"\") {\n    return failure;\n  }\n  let R = 10;\n  if (input.length >= 2 && input.charAt(0) === \"0\" && input.charAt(1).toLowerCase() === \"x\") {\n    input = input.substring(2);\n    R = 16;\n  } else if (input.length >= 2 && input.charAt(0) === \"0\") {\n    input = input.substring(1);\n    R = 8;\n  }\n  if (input === \"\") {\n    return 0;\n  }\n  let regex = /[^0-7]/u;\n  if (R === 10) {\n    regex = /[^0-9]/u;\n  }\n  if (R === 16) {\n    regex = /[^0-9A-Fa-f]/u;\n  }\n  if (regex.test(input)) {\n    return failure;\n  }\n  return parseInt(input, R);\n}\nfunction parseIPv4(input) {\n  const parts = input.split(\".\");\n  if (parts[parts.length - 1] === \"\") {\n    if (parts.length > 1) {\n      parts.pop();\n    }\n  }\n  if (parts.length > 4) {\n    return failure;\n  }\n  const numbers = [];\n  for (const part of parts) {\n    const n = parseIPv4Number(part);\n    if (n === failure) {\n      return failure;\n    }\n    numbers.push(n);\n  }\n  for (let i = 0; i < numbers.length - 1; ++i) {\n    if (numbers[i] > 255) {\n      return failure;\n    }\n  }\n  if (numbers[numbers.length - 1] >= 256 ** (5 - numbers.length)) {\n    return failure;\n  }\n  let ipv4 = numbers.pop();\n  let counter = 0;\n  for (const n of numbers) {\n    ipv4 += n * 256 ** (3 - counter);\n    ++counter;\n  }\n  return ipv4;\n}\nfunction serializeIPv4(address) {\n  let output = \"\";\n  let n = address;\n  for (let i = 1; i <= 4; ++i) {\n    output = String(n % 256) + output;\n    if (i !== 4) {\n      output = `.${output}`;\n    }\n    n = Math.floor(n / 256);\n  }\n  return output;\n}\nfunction parseIPv6(input) {\n  const address = [0, 0, 0, 0, 0, 0, 0, 0];\n  let pieceIndex = 0;\n  let compress = null;\n  let pointer = 0;\n  input = Array.from(input, c => c.codePointAt(0));\n  if (input[pointer] === p(\":\")) {\n    if (input[pointer + 1] !== p(\":\")) {\n      return failure;\n    }\n    pointer += 2;\n    ++pieceIndex;\n    compress = pieceIndex;\n  }\n  while (pointer < input.length) {\n    if (pieceIndex === 8) {\n      return failure;\n    }\n    if (input[pointer] === p(\":\")) {\n      if (compress !== null) {\n        return failure;\n      }\n      ++pointer;\n      ++pieceIndex;\n      compress = pieceIndex;\n      continue;\n    }\n    let value = 0;\n    let length = 0;\n    while (length < 4 && infra.isASCIIHex(input[pointer])) {\n      value = value * 0x10 + parseInt(at(input, pointer), 16);\n      ++pointer;\n      ++length;\n    }\n    if (input[pointer] === p(\".\")) {\n      if (length === 0) {\n        return failure;\n      }\n      pointer -= length;\n      if (pieceIndex > 6) {\n        return failure;\n      }\n      let numbersSeen = 0;\n      while (input[pointer] !== undefined) {\n        let ipv4Piece = null;\n        if (numbersSeen > 0) {\n          if (input[pointer] === p(\".\") && numbersSeen < 4) {\n            ++pointer;\n          } else {\n            return failure;\n          }\n        }\n        if (!infra.isASCIIDigit(input[pointer])) {\n          return failure;\n        }\n        while (infra.isASCIIDigit(input[pointer])) {\n          const number = parseInt(at(input, pointer));\n          if (ipv4Piece === null) {\n            ipv4Piece = number;\n          } else if (ipv4Piece === 0) {\n            return failure;\n          } else {\n            ipv4Piece = ipv4Piece * 10 + number;\n          }\n          if (ipv4Piece > 255) {\n            return failure;\n          }\n          ++pointer;\n        }\n        address[pieceIndex] = address[pieceIndex] * 0x100 + ipv4Piece;\n        ++numbersSeen;\n        if (numbersSeen === 2 || numbersSeen === 4) {\n          ++pieceIndex;\n        }\n      }\n      if (numbersSeen !== 4) {\n        return failure;\n      }\n      break;\n    } else if (input[pointer] === p(\":\")) {\n      ++pointer;\n      if (input[pointer] === undefined) {\n        return failure;\n      }\n    } else if (input[pointer] !== undefined) {\n      return failure;\n    }\n    address[pieceIndex] = value;\n    ++pieceIndex;\n  }\n  if (compress !== null) {\n    let swaps = pieceIndex - compress;\n    pieceIndex = 7;\n    while (pieceIndex !== 0 && swaps > 0) {\n      const temp = address[compress + swaps - 1];\n      address[compress + swaps - 1] = address[pieceIndex];\n      address[pieceIndex] = temp;\n      --pieceIndex;\n      --swaps;\n    }\n  } else if (compress === null && pieceIndex !== 8) {\n    return failure;\n  }\n  return address;\n}\nfunction serializeIPv6(address) {\n  let output = \"\";\n  const compress = findTheIPv6AddressCompressedPieceIndex(address);\n  let ignore0 = false;\n  for (let pieceIndex = 0; pieceIndex <= 7; ++pieceIndex) {\n    if (ignore0 && address[pieceIndex] === 0) {\n      continue;\n    } else if (ignore0) {\n      ignore0 = false;\n    }\n    if (compress === pieceIndex) {\n      const separator = pieceIndex === 0 ? \"::\" : \":\";\n      output += separator;\n      ignore0 = true;\n      continue;\n    }\n    output += address[pieceIndex].toString(16);\n    if (pieceIndex !== 7) {\n      output += \":\";\n    }\n  }\n  return output;\n}\nfunction parseHost(input, isOpaque = false) {\n  if (input[0] === \"[\") {\n    if (input[input.length - 1] !== \"]\") {\n      return failure;\n    }\n    return parseIPv6(input.substring(1, input.length - 1));\n  }\n  if (isOpaque) {\n    return parseOpaqueHost(input);\n  }\n  const domain = utf8DecodeWithoutBOM(percentDecodeString(input));\n  const asciiDomain = domainToASCII(domain);\n  if (asciiDomain === failure) {\n    return failure;\n  }\n  if (endsInANumber(asciiDomain)) {\n    return parseIPv4(asciiDomain);\n  }\n  return asciiDomain;\n}\nfunction endsInANumber(input) {\n  const parts = input.split(\".\");\n  if (parts[parts.length - 1] === \"\") {\n    if (parts.length === 1) {\n      return false;\n    }\n    parts.pop();\n  }\n  const last = parts[parts.length - 1];\n  if (parseIPv4Number(last) !== failure) {\n    return true;\n  }\n  if (/^[0-9]+$/u.test(last)) {\n    return true;\n  }\n  return false;\n}\nfunction parseOpaqueHost(input) {\n  if (containsForbiddenHostCodePoint(input)) {\n    return failure;\n  }\n  return utf8PercentEncodeString(input, isC0ControlPercentEncode);\n}\nfunction findTheIPv6AddressCompressedPieceIndex(address) {\n  let longestIndex = null;\n  let longestSize = 1; // only find elements > 1\n  let foundIndex = null;\n  let foundSize = 0;\n  for (let pieceIndex = 0; pieceIndex < address.length; ++pieceIndex) {\n    if (address[pieceIndex] !== 0) {\n      if (foundSize > longestSize) {\n        longestIndex = foundIndex;\n        longestSize = foundSize;\n      }\n      foundIndex = null;\n      foundSize = 0;\n    } else {\n      if (foundIndex === null) {\n        foundIndex = pieceIndex;\n      }\n      ++foundSize;\n    }\n  }\n  if (foundSize > longestSize) {\n    return foundIndex;\n  }\n  return longestIndex;\n}\nfunction serializeHost(host) {\n  if (typeof host === \"number\") {\n    return serializeIPv4(host);\n  }\n\n  // IPv6 serializer\n  if (host instanceof Array) {\n    return `[${serializeIPv6(host)}]`;\n  }\n  return host;\n}\nfunction domainToASCII(domain, beStrict = false) {\n  const result = tr46.toASCII(domain, {\n    checkHyphens: beStrict,\n    checkBidi: true,\n    checkJoiners: true,\n    useSTD3ASCIIRules: beStrict,\n    transitionalProcessing: false,\n    verifyDNSLength: beStrict,\n    ignoreInvalidPunycode: false\n  });\n  if (result === null) {\n    return failure;\n  }\n  if (!beStrict) {\n    if (result === \"\") {\n      return failure;\n    }\n    if (containsForbiddenDomainCodePoint(result)) {\n      return failure;\n    }\n  }\n  return result;\n}\nfunction trimControlChars(string) {\n  // Avoid using regexp because of this V8 bug: https://issues.chromium.org/issues/42204424\n\n  let start = 0;\n  let end = string.length;\n  for (; start < end; ++start) {\n    if (string.charCodeAt(start) > 0x20) {\n      break;\n    }\n  }\n  for (; end > start; --end) {\n    if (string.charCodeAt(end - 1) > 0x20) {\n      break;\n    }\n  }\n  return string.substring(start, end);\n}\nfunction trimTabAndNewline(url) {\n  return url.replace(/\\u0009|\\u000A|\\u000D/ug, \"\");\n}\nfunction shortenPath(url) {\n  const {\n    path\n  } = url;\n  if (path.length === 0) {\n    return;\n  }\n  if (url.scheme === \"file\" && path.length === 1 && isNormalizedWindowsDriveLetter(path[0])) {\n    return;\n  }\n  path.pop();\n}\nfunction includesCredentials(url) {\n  return url.username !== \"\" || url.password !== \"\";\n}\nfunction cannotHaveAUsernamePasswordPort(url) {\n  return url.host === null || url.host === \"\" || url.scheme === \"file\";\n}\nfunction hasAnOpaquePath(url) {\n  return typeof url.path === \"string\";\n}\nfunction isNormalizedWindowsDriveLetter(string) {\n  return /^[A-Za-z]:$/u.test(string);\n}\nfunction URLStateMachine(input, base, encodingOverride, url, stateOverride) {\n  this.pointer = 0;\n  this.input = input;\n  this.base = base || null;\n  this.encodingOverride = encodingOverride || \"utf-8\";\n  this.stateOverride = stateOverride;\n  this.url = url;\n  this.failure = false;\n  this.parseError = false;\n  if (!this.url) {\n    this.url = {\n      scheme: \"\",\n      username: \"\",\n      password: \"\",\n      host: null,\n      port: null,\n      path: [],\n      query: null,\n      fragment: null\n    };\n    const res = trimControlChars(this.input);\n    if (res !== this.input) {\n      this.parseError = true;\n    }\n    this.input = res;\n  }\n  const res = trimTabAndNewline(this.input);\n  if (res !== this.input) {\n    this.parseError = true;\n  }\n  this.input = res;\n  this.state = stateOverride || \"scheme start\";\n  this.buffer = \"\";\n  this.atFlag = false;\n  this.arrFlag = false;\n  this.passwordTokenSeenFlag = false;\n  this.input = Array.from(this.input, c => c.codePointAt(0));\n  for (; this.pointer <= this.input.length; ++this.pointer) {\n    const c = this.input[this.pointer];\n    const cStr = isNaN(c) ? undefined : String.fromCodePoint(c);\n\n    // exec state machine\n    const ret = this[`parse ${this.state}`](c, cStr);\n    if (!ret) {\n      break; // terminate algorithm\n    } else if (ret === failure) {\n      this.failure = true;\n      break;\n    }\n  }\n}\nURLStateMachine.prototype[\"parse scheme start\"] = function parseSchemeStart(c, cStr) {\n  if (infra.isASCIIAlpha(c)) {\n    this.buffer += cStr.toLowerCase();\n    this.state = \"scheme\";\n  } else if (!this.stateOverride) {\n    this.state = \"no scheme\";\n    --this.pointer;\n  } else {\n    this.parseError = true;\n    return failure;\n  }\n  return true;\n};\nURLStateMachine.prototype[\"parse scheme\"] = function parseScheme(c, cStr) {\n  if (infra.isASCIIAlphanumeric(c) || c === p(\"+\") || c === p(\"-\") || c === p(\".\")) {\n    this.buffer += cStr.toLowerCase();\n  } else if (c === p(\":\")) {\n    if (this.stateOverride) {\n      if (isSpecial(this.url) && !isSpecialScheme(this.buffer)) {\n        return false;\n      }\n      if (!isSpecial(this.url) && isSpecialScheme(this.buffer)) {\n        return false;\n      }\n      if ((includesCredentials(this.url) || this.url.port !== null) && this.buffer === \"file\") {\n        return false;\n      }\n      if (this.url.scheme === \"file\" && this.url.host === \"\") {\n        return false;\n      }\n    }\n    this.url.scheme = this.buffer;\n    if (this.stateOverride) {\n      if (this.url.port === defaultPort(this.url.scheme)) {\n        this.url.port = null;\n      }\n      return false;\n    }\n    this.buffer = \"\";\n    if (this.url.scheme === \"file\") {\n      if (this.input[this.pointer + 1] !== p(\"/\") || this.input[this.pointer + 2] !== p(\"/\")) {\n        this.parseError = true;\n      }\n      this.state = \"file\";\n    } else if (isSpecial(this.url) && this.base !== null && this.base.scheme === this.url.scheme) {\n      this.state = \"special relative or authority\";\n    } else if (isSpecial(this.url)) {\n      this.state = \"special authority slashes\";\n    } else if (this.input[this.pointer + 1] === p(\"/\")) {\n      this.state = \"path or authority\";\n      ++this.pointer;\n    } else {\n      this.url.path = \"\";\n      this.state = \"opaque path\";\n    }\n  } else if (!this.stateOverride) {\n    this.buffer = \"\";\n    this.state = \"no scheme\";\n    this.pointer = -1;\n  } else {\n    this.parseError = true;\n    return failure;\n  }\n  return true;\n};\nURLStateMachine.prototype[\"parse no scheme\"] = function parseNoScheme(c) {\n  if (this.base === null || hasAnOpaquePath(this.base) && c !== p(\"#\")) {\n    return failure;\n  } else if (hasAnOpaquePath(this.base) && c === p(\"#\")) {\n    this.url.scheme = this.base.scheme;\n    this.url.path = this.base.path;\n    this.url.query = this.base.query;\n    this.url.fragment = \"\";\n    this.state = \"fragment\";\n  } else if (this.base.scheme === \"file\") {\n    this.state = \"file\";\n    --this.pointer;\n  } else {\n    this.state = \"relative\";\n    --this.pointer;\n  }\n  return true;\n};\nURLStateMachine.prototype[\"parse special relative or authority\"] = function parseSpecialRelativeOrAuthority(c) {\n  if (c === p(\"/\") && this.input[this.pointer + 1] === p(\"/\")) {\n    this.state = \"special authority ignore slashes\";\n    ++this.pointer;\n  } else {\n    this.parseError = true;\n    this.state = \"relative\";\n    --this.pointer;\n  }\n  return true;\n};\nURLStateMachine.prototype[\"parse path or authority\"] = function parsePathOrAuthority(c) {\n  if (c === p(\"/\")) {\n    this.state = \"authority\";\n  } else {\n    this.state = \"path\";\n    --this.pointer;\n  }\n  return true;\n};\nURLStateMachine.prototype[\"parse relative\"] = function parseRelative(c) {\n  this.url.scheme = this.base.scheme;\n  if (c === p(\"/\")) {\n    this.state = \"relative slash\";\n  } else if (isSpecial(this.url) && c === p(\"\\\\\")) {\n    this.parseError = true;\n    this.state = \"relative slash\";\n  } else {\n    this.url.username = this.base.username;\n    this.url.password = this.base.password;\n    this.url.host = this.base.host;\n    this.url.port = this.base.port;\n    this.url.path = this.base.path.slice();\n    this.url.query = this.base.query;\n    if (c === p(\"?\")) {\n      this.url.query = \"\";\n      this.state = \"query\";\n    } else if (c === p(\"#\")) {\n      this.url.fragment = \"\";\n      this.state = \"fragment\";\n    } else if (!isNaN(c)) {\n      this.url.query = null;\n      this.url.path.pop();\n      this.state = \"path\";\n      --this.pointer;\n    }\n  }\n  return true;\n};\nURLStateMachine.prototype[\"parse relative slash\"] = function parseRelativeSlash(c) {\n  if (isSpecial(this.url) && (c === p(\"/\") || c === p(\"\\\\\"))) {\n    if (c === p(\"\\\\\")) {\n      this.parseError = true;\n    }\n    this.state = \"special authority ignore slashes\";\n  } else if (c === p(\"/\")) {\n    this.state = \"authority\";\n  } else {\n    this.url.username = this.base.username;\n    this.url.password = this.base.password;\n    this.url.host = this.base.host;\n    this.url.port = this.base.port;\n    this.state = \"path\";\n    --this.pointer;\n  }\n  return true;\n};\nURLStateMachine.prototype[\"parse special authority slashes\"] = function parseSpecialAuthoritySlashes(c) {\n  if (c === p(\"/\") && this.input[this.pointer + 1] === p(\"/\")) {\n    this.state = \"special authority ignore slashes\";\n    ++this.pointer;\n  } else {\n    this.parseError = true;\n    this.state = \"special authority ignore slashes\";\n    --this.pointer;\n  }\n  return true;\n};\nURLStateMachine.prototype[\"parse special authority ignore slashes\"] = function parseSpecialAuthorityIgnoreSlashes(c) {\n  if (c !== p(\"/\") && c !== p(\"\\\\\")) {\n    this.state = \"authority\";\n    --this.pointer;\n  } else {\n    this.parseError = true;\n  }\n  return true;\n};\nURLStateMachine.prototype[\"parse authority\"] = function parseAuthority(c, cStr) {\n  if (c === p(\"@\")) {\n    this.parseError = true;\n    if (this.atFlag) {\n      this.buffer = `%40${this.buffer}`;\n    }\n    this.atFlag = true;\n\n    // careful, this is based on buffer and has its own pointer (this.pointer != pointer) and inner chars\n    const len = countSymbols(this.buffer);\n    for (let pointer = 0; pointer < len; ++pointer) {\n      const codePoint = this.buffer.codePointAt(pointer);\n      if (codePoint === p(\":\") && !this.passwordTokenSeenFlag) {\n        this.passwordTokenSeenFlag = true;\n        continue;\n      }\n      const encodedCodePoints = utf8PercentEncodeCodePoint(codePoint, isUserinfoPercentEncode);\n      if (this.passwordTokenSeenFlag) {\n        this.url.password += encodedCodePoints;\n      } else {\n        this.url.username += encodedCodePoints;\n      }\n    }\n    this.buffer = \"\";\n  } else if (isNaN(c) || c === p(\"/\") || c === p(\"?\") || c === p(\"#\") || isSpecial(this.url) && c === p(\"\\\\\")) {\n    if (this.atFlag && this.buffer === \"\") {\n      this.parseError = true;\n      return failure;\n    }\n    this.pointer -= countSymbols(this.buffer) + 1;\n    this.buffer = \"\";\n    this.state = \"host\";\n  } else {\n    this.buffer += cStr;\n  }\n  return true;\n};\nURLStateMachine.prototype[\"parse hostname\"] = URLStateMachine.prototype[\"parse host\"] = function parseHostName(c, cStr) {\n  if (this.stateOverride && this.url.scheme === \"file\") {\n    --this.pointer;\n    this.state = \"file host\";\n  } else if (c === p(\":\") && !this.arrFlag) {\n    if (this.buffer === \"\") {\n      this.parseError = true;\n      return failure;\n    }\n    if (this.stateOverride === \"hostname\") {\n      return false;\n    }\n    const host = parseHost(this.buffer, isNotSpecial(this.url));\n    if (host === failure) {\n      return failure;\n    }\n    this.url.host = host;\n    this.buffer = \"\";\n    this.state = \"port\";\n  } else if (isNaN(c) || c === p(\"/\") || c === p(\"?\") || c === p(\"#\") || isSpecial(this.url) && c === p(\"\\\\\")) {\n    --this.pointer;\n    if (isSpecial(this.url) && this.buffer === \"\") {\n      this.parseError = true;\n      return failure;\n    } else if (this.stateOverride && this.buffer === \"\" && (includesCredentials(this.url) || this.url.port !== null)) {\n      this.parseError = true;\n      return false;\n    }\n    const host = parseHost(this.buffer, isNotSpecial(this.url));\n    if (host === failure) {\n      return failure;\n    }\n    this.url.host = host;\n    this.buffer = \"\";\n    this.state = \"path start\";\n    if (this.stateOverride) {\n      return false;\n    }\n  } else {\n    if (c === p(\"[\")) {\n      this.arrFlag = true;\n    } else if (c === p(\"]\")) {\n      this.arrFlag = false;\n    }\n    this.buffer += cStr;\n  }\n  return true;\n};\nURLStateMachine.prototype[\"parse port\"] = function parsePort(c, cStr) {\n  if (infra.isASCIIDigit(c)) {\n    this.buffer += cStr;\n  } else if (isNaN(c) || c === p(\"/\") || c === p(\"?\") || c === p(\"#\") || isSpecial(this.url) && c === p(\"\\\\\") || this.stateOverride) {\n    if (this.buffer !== \"\") {\n      const port = parseInt(this.buffer);\n      if (port > 2 ** 16 - 1) {\n        this.parseError = true;\n        return failure;\n      }\n      this.url.port = port === defaultPort(this.url.scheme) ? null : port;\n      this.buffer = \"\";\n    }\n    if (this.stateOverride) {\n      return false;\n    }\n    this.state = \"path start\";\n    --this.pointer;\n  } else {\n    this.parseError = true;\n    return failure;\n  }\n  return true;\n};\nconst fileOtherwiseCodePoints = new Set([p(\"/\"), p(\"\\\\\"), p(\"?\"), p(\"#\")]);\nfunction startsWithWindowsDriveLetter(input, pointer) {\n  const length = input.length - pointer;\n  return length >= 2 && isWindowsDriveLetterCodePoints(input[pointer], input[pointer + 1]) && (length === 2 || fileOtherwiseCodePoints.has(input[pointer + 2]));\n}\nURLStateMachine.prototype[\"parse file\"] = function parseFile(c) {\n  this.url.scheme = \"file\";\n  this.url.host = \"\";\n  if (c === p(\"/\") || c === p(\"\\\\\")) {\n    if (c === p(\"\\\\\")) {\n      this.parseError = true;\n    }\n    this.state = \"file slash\";\n  } else if (this.base !== null && this.base.scheme === \"file\") {\n    this.url.host = this.base.host;\n    this.url.path = this.base.path.slice();\n    this.url.query = this.base.query;\n    if (c === p(\"?\")) {\n      this.url.query = \"\";\n      this.state = \"query\";\n    } else if (c === p(\"#\")) {\n      this.url.fragment = \"\";\n      this.state = \"fragment\";\n    } else if (!isNaN(c)) {\n      this.url.query = null;\n      if (!startsWithWindowsDriveLetter(this.input, this.pointer)) {\n        shortenPath(this.url);\n      } else {\n        this.parseError = true;\n        this.url.path = [];\n      }\n      this.state = \"path\";\n      --this.pointer;\n    }\n  } else {\n    this.state = \"path\";\n    --this.pointer;\n  }\n  return true;\n};\nURLStateMachine.prototype[\"parse file slash\"] = function parseFileSlash(c) {\n  if (c === p(\"/\") || c === p(\"\\\\\")) {\n    if (c === p(\"\\\\\")) {\n      this.parseError = true;\n    }\n    this.state = \"file host\";\n  } else {\n    if (this.base !== null && this.base.scheme === \"file\") {\n      if (!startsWithWindowsDriveLetter(this.input, this.pointer) && isNormalizedWindowsDriveLetterString(this.base.path[0])) {\n        this.url.path.push(this.base.path[0]);\n      }\n      this.url.host = this.base.host;\n    }\n    this.state = \"path\";\n    --this.pointer;\n  }\n  return true;\n};\nURLStateMachine.prototype[\"parse file host\"] = function parseFileHost(c, cStr) {\n  if (isNaN(c) || c === p(\"/\") || c === p(\"\\\\\") || c === p(\"?\") || c === p(\"#\")) {\n    --this.pointer;\n    if (!this.stateOverride && isWindowsDriveLetterString(this.buffer)) {\n      this.parseError = true;\n      this.state = \"path\";\n    } else if (this.buffer === \"\") {\n      this.url.host = \"\";\n      if (this.stateOverride) {\n        return false;\n      }\n      this.state = \"path start\";\n    } else {\n      let host = parseHost(this.buffer, isNotSpecial(this.url));\n      if (host === failure) {\n        return failure;\n      }\n      if (host === \"localhost\") {\n        host = \"\";\n      }\n      this.url.host = host;\n      if (this.stateOverride) {\n        return false;\n      }\n      this.buffer = \"\";\n      this.state = \"path start\";\n    }\n  } else {\n    this.buffer += cStr;\n  }\n  return true;\n};\nURLStateMachine.prototype[\"parse path start\"] = function parsePathStart(c) {\n  if (isSpecial(this.url)) {\n    if (c === p(\"\\\\\")) {\n      this.parseError = true;\n    }\n    this.state = \"path\";\n    if (c !== p(\"/\") && c !== p(\"\\\\\")) {\n      --this.pointer;\n    }\n  } else if (!this.stateOverride && c === p(\"?\")) {\n    this.url.query = \"\";\n    this.state = \"query\";\n  } else if (!this.stateOverride && c === p(\"#\")) {\n    this.url.fragment = \"\";\n    this.state = \"fragment\";\n  } else if (c !== undefined) {\n    this.state = \"path\";\n    if (c !== p(\"/\")) {\n      --this.pointer;\n    }\n  } else if (this.stateOverride && this.url.host === null) {\n    this.url.path.push(\"\");\n  }\n  return true;\n};\nURLStateMachine.prototype[\"parse path\"] = function parsePath(c) {\n  if (isNaN(c) || c === p(\"/\") || isSpecial(this.url) && c === p(\"\\\\\") || !this.stateOverride && (c === p(\"?\") || c === p(\"#\"))) {\n    if (isSpecial(this.url) && c === p(\"\\\\\")) {\n      this.parseError = true;\n    }\n    if (isDoubleDot(this.buffer)) {\n      shortenPath(this.url);\n      if (c !== p(\"/\") && !(isSpecial(this.url) && c === p(\"\\\\\"))) {\n        this.url.path.push(\"\");\n      }\n    } else if (isSingleDot(this.buffer) && c !== p(\"/\") && !(isSpecial(this.url) && c === p(\"\\\\\"))) {\n      this.url.path.push(\"\");\n    } else if (!isSingleDot(this.buffer)) {\n      if (this.url.scheme === \"file\" && this.url.path.length === 0 && isWindowsDriveLetterString(this.buffer)) {\n        this.buffer = `${this.buffer[0]}:`;\n      }\n      this.url.path.push(this.buffer);\n    }\n    this.buffer = \"\";\n    if (c === p(\"?\")) {\n      this.url.query = \"\";\n      this.state = \"query\";\n    }\n    if (c === p(\"#\")) {\n      this.url.fragment = \"\";\n      this.state = \"fragment\";\n    }\n  } else {\n    // TODO: If c is not a URL code point and not \"%\", parse error.\n\n    if (c === p(\"%\") && (!infra.isASCIIHex(this.input[this.pointer + 1]) || !infra.isASCIIHex(this.input[this.pointer + 2]))) {\n      this.parseError = true;\n    }\n    this.buffer += utf8PercentEncodeCodePoint(c, isPathPercentEncode);\n  }\n  return true;\n};\nURLStateMachine.prototype[\"parse opaque path\"] = function parseOpaquePath(c) {\n  if (c === p(\"?\")) {\n    this.url.query = \"\";\n    this.state = \"query\";\n  } else if (c === p(\"#\")) {\n    this.url.fragment = \"\";\n    this.state = \"fragment\";\n  } else if (c === p(\" \")) {\n    const remaining = this.input[this.pointer + 1];\n    if (remaining === p(\"?\") || remaining === p(\"#\")) {\n      this.url.path += \"%20\";\n    } else {\n      this.url.path += \" \";\n    }\n  } else {\n    // TODO: Add: not a URL code point\n    if (!isNaN(c) && c !== p(\"%\")) {\n      this.parseError = true;\n    }\n    if (c === p(\"%\") && (!infra.isASCIIHex(this.input[this.pointer + 1]) || !infra.isASCIIHex(this.input[this.pointer + 2]))) {\n      this.parseError = true;\n    }\n    if (!isNaN(c)) {\n      this.url.path += utf8PercentEncodeCodePoint(c, isC0ControlPercentEncode);\n    }\n  }\n  return true;\n};\nURLStateMachine.prototype[\"parse query\"] = function parseQuery(c, cStr) {\n  if (!isSpecial(this.url) || this.url.scheme === \"ws\" || this.url.scheme === \"wss\") {\n    this.encodingOverride = \"utf-8\";\n  }\n  if (!this.stateOverride && c === p(\"#\") || isNaN(c)) {\n    const queryPercentEncodePredicate = isSpecial(this.url) ? isSpecialQueryPercentEncode : isQueryPercentEncode;\n    this.url.query += utf8PercentEncodeString(this.buffer, queryPercentEncodePredicate);\n    this.buffer = \"\";\n    if (c === p(\"#\")) {\n      this.url.fragment = \"\";\n      this.state = \"fragment\";\n    }\n  } else if (!isNaN(c)) {\n    // TODO: If c is not a URL code point and not \"%\", parse error.\n\n    if (c === p(\"%\") && (!infra.isASCIIHex(this.input[this.pointer + 1]) || !infra.isASCIIHex(this.input[this.pointer + 2]))) {\n      this.parseError = true;\n    }\n    this.buffer += cStr;\n  }\n  return true;\n};\nURLStateMachine.prototype[\"parse fragment\"] = function parseFragment(c) {\n  if (!isNaN(c)) {\n    // TODO: If c is not a URL code point and not \"%\", parse error.\n    if (c === p(\"%\") && (!infra.isASCIIHex(this.input[this.pointer + 1]) || !infra.isASCIIHex(this.input[this.pointer + 2]))) {\n      this.parseError = true;\n    }\n    this.url.fragment += utf8PercentEncodeCodePoint(c, isFragmentPercentEncode);\n  }\n  return true;\n};\nfunction serializeURL(url, excludeFragment) {\n  let output = `${url.scheme}:`;\n  if (url.host !== null) {\n    output += \"//\";\n    if (url.username !== \"\" || url.password !== \"\") {\n      output += url.username;\n      if (url.password !== \"\") {\n        output += `:${url.password}`;\n      }\n      output += \"@\";\n    }\n    output += serializeHost(url.host);\n    if (url.port !== null) {\n      output += `:${url.port}`;\n    }\n  }\n  if (url.host === null && !hasAnOpaquePath(url) && url.path.length > 1 && url.path[0] === \"\") {\n    output += \"/.\";\n  }\n  output += serializePath(url);\n  if (url.query !== null) {\n    output += `?${url.query}`;\n  }\n  if (!excludeFragment && url.fragment !== null) {\n    output += `#${url.fragment}`;\n  }\n  return output;\n}\nfunction serializeOrigin(tuple) {\n  let result = `${tuple.scheme}://`;\n  result += serializeHost(tuple.host);\n  if (tuple.port !== null) {\n    result += `:${tuple.port}`;\n  }\n  return result;\n}\nfunction serializePath(url) {\n  if (hasAnOpaquePath(url)) {\n    return url.path;\n  }\n  let output = \"\";\n  for (const segment of url.path) {\n    output += `/${segment}`;\n  }\n  return output;\n}\nmodule.exports.serializeURL = serializeURL;\nmodule.exports.serializePath = serializePath;\nmodule.exports.serializeURLOrigin = function (url) {\n  // https://url.spec.whatwg.org/#concept-url-origin\n  switch (url.scheme) {\n    case \"blob\":\n      {\n        const pathURL = module.exports.parseURL(serializePath(url));\n        if (pathURL === null) {\n          return \"null\";\n        }\n        if (pathURL.scheme !== \"http\" && pathURL.scheme !== \"https\") {\n          return \"null\";\n        }\n        return module.exports.serializeURLOrigin(pathURL);\n      }\n    case \"ftp\":\n    case \"http\":\n    case \"https\":\n    case \"ws\":\n    case \"wss\":\n      return serializeOrigin({\n        scheme: url.scheme,\n        host: url.host,\n        port: url.port\n      });\n    case \"file\":\n      // The spec says:\n      // > Unfortunate as it is, this is left as an exercise to the reader. When in doubt, return a new opaque origin.\n      // Browsers tested so far:\n      // - Chrome says \"file://\", but treats file: URLs as cross-origin for most (all?) purposes; see e.g.\n      //   https://bugs.chromium.org/p/chromium/issues/detail?id=37586\n      // - Firefox says \"null\", but treats file: URLs as same-origin sometimes based on directory stuff; see\n      //   https://developer.mozilla.org/en-US/docs/Archive/Misc_top_level/Same-origin_policy_for_file:_URIs\n      return \"null\";\n    default:\n      // serializing an opaque origin returns \"null\"\n      return \"null\";\n  }\n};\nmodule.exports.basicURLParse = function (input, options) {\n  if (options === undefined) {\n    options = {};\n  }\n  const usm = new URLStateMachine(input, options.baseURL, options.encodingOverride, options.url, options.stateOverride);\n  if (usm.failure) {\n    return null;\n  }\n  return usm.url;\n};\nmodule.exports.setTheUsername = function (url, username) {\n  url.username = utf8PercentEncodeString(username, isUserinfoPercentEncode);\n};\nmodule.exports.setThePassword = function (url, password) {\n  url.password = utf8PercentEncodeString(password, isUserinfoPercentEncode);\n};\nmodule.exports.serializeHost = serializeHost;\nmodule.exports.cannotHaveAUsernamePasswordPort = cannotHaveAUsernamePasswordPort;\nmodule.exports.hasAnOpaquePath = hasAnOpaquePath;\nmodule.exports.serializeInteger = function (integer) {\n  return String(integer);\n};\nmodule.exports.parseURL = function (input, options) {\n  if (options === undefined) {\n    options = {};\n  }\n\n  // We don't handle blobs, so this just delegates:\n  return module.exports.basicURLParse(input, {\n    baseURL: options.baseURL,\n    encodingOverride: options.encodingOverride\n  });\n};", "map": {"version": 3, "names": ["tr46", "require", "infra", "utf8DecodeWithoutBOM", "percentDecodeString", "utf8PercentEncodeCodePoint", "utf8PercentEncodeString", "isC0ControlPercentEncode", "isFragmentPercentEncode", "isQueryPercentEncode", "isSpecialQueryPercentEncode", "isPathPercentEncode", "isUserinfoPercentEncode", "p", "char", "codePointAt", "specialSchemes", "ftp", "file", "http", "https", "ws", "wss", "failure", "Symbol", "countSymbols", "str", "length", "at", "input", "idx", "c", "isNaN", "undefined", "String", "fromCodePoint", "isSingleDot", "buffer", "toLowerCase", "isDoubleDot", "isWindowsDriveLetterCodePoints", "cp1", "cp2", "isASCIIAlpha", "isWindowsDriveLetterString", "string", "isNormalizedWindowsDriveLetterString", "containsForbiddenHostCodePoint", "search", "containsForbiddenDomainCodePoint", "isSpecialScheme", "scheme", "isSpecial", "url", "isNotSpecial", "defaultPort", "parseIPv4Number", "R", "char<PERSON>t", "substring", "regex", "test", "parseInt", "parseIPv4", "parts", "split", "pop", "numbers", "part", "n", "push", "i", "ipv4", "counter", "serializeIPv4", "address", "output", "Math", "floor", "parseIPv6", "pieceIndex", "compress", "pointer", "Array", "from", "value", "isASCIIHex", "numbersSeen", "ipv4Piece", "isASCIIDigit", "number", "swaps", "temp", "serializeIPv6", "findTheIPv6AddressCompressedPieceIndex", "ignore0", "separator", "toString", "parseHost", "isOpaque", "parseOpaqueHost", "domain", "asciiDomain", "domainToASCII", "endsInANumber", "last", "longestIndex", "longestSize", "foundIndex", "foundSize", "serializeHost", "host", "<PERSON><PERSON><PERSON><PERSON>", "result", "toASCII", "checkHyphens", "checkBidi", "checkJoiners", "useSTD3ASCIIRules", "transitionalProcessing", "verifyD<PERSON><PERSON><PERSON><PERSON>", "ignoreInvalidPunycode", "trimControlChars", "start", "end", "charCodeAt", "trimTabAndNewline", "replace", "shorten<PERSON>ath", "path", "isNormalizedWindowsDriveLetter", "includesCredentials", "username", "password", "cannotHaveAUsernamePasswordPort", "hasAnOpaquePath", "URLStateMachine", "base", "encodingOverride", "stateOverride", "parseError", "port", "query", "fragment", "res", "state", "atFlag", "arrFlag", "passwordTokenSeenFlag", "cStr", "ret", "prototype", "parseSchemeStart", "parseScheme", "isASCIIAlphanumeric", "parseNoScheme", "parseSpecialRelativeOrAuthority", "parsePathOrAuthority", "parseRelative", "slice", "parseRelativeSlash", "parseSpecialAuthoritySlashes", "parseSpecialAuthorityIgnoreSlashes", "parseAuthority", "len", "codePoint", "encodedCodePoints", "parseHostName", "parsePort", "fileOtherwiseCodePoints", "Set", "startsWithWindowsDriveLetter", "has", "parseFile", "parseFileSlash", "parseFileHost", "parsePathStart", "parsePath", "parseOpaquePath", "remaining", "parse<PERSON><PERSON>y", "queryPercentEncodePredicate", "parseFragment", "serializeURL", "excludeFragment", "serializePath", "serializeOrigin", "tuple", "segment", "module", "exports", "serializeURLOrigin", "pathURL", "parseURL", "basicURLParse", "options", "usm", "baseURL", "setTheUsername", "setThePassword", "serializeInteger", "integer"], "sources": ["C:/Users/<USER>/node_modules/mongodb-connection-string-url/node_modules/whatwg-url/lib/url-state-machine.js"], "sourcesContent": ["\"use strict\";\nconst tr46 = require(\"tr46\");\n\nconst infra = require(\"./infra\");\nconst { utf8DecodeWithoutBOM } = require(\"./encoding\");\nconst { percentDecodeString, utf8PercentEncodeCodePoint, utf8PercentEncodeString, isC0ControlPercentEncode,\n  isFragmentPercentEncode, isQueryPercentEncode, isSpecialQueryPercentEncode, isPathPercentEncode,\n  isUserinfoPercentEncode } = require(\"./percent-encoding\");\n\nfunction p(char) {\n  return char.codePointAt(0);\n}\n\nconst specialSchemes = {\n  ftp: 21,\n  file: null,\n  http: 80,\n  https: 443,\n  ws: 80,\n  wss: 443\n};\n\nconst failure = Symbol(\"failure\");\n\nfunction countSymbols(str) {\n  return [...str].length;\n}\n\nfunction at(input, idx) {\n  const c = input[idx];\n  return isNaN(c) ? undefined : String.fromCodePoint(c);\n}\n\nfunction isSingleDot(buffer) {\n  return buffer === \".\" || buffer.toLowerCase() === \"%2e\";\n}\n\nfunction isDoubleDot(buffer) {\n  buffer = buffer.toLowerCase();\n  return buffer === \"..\" || buffer === \"%2e.\" || buffer === \".%2e\" || buffer === \"%2e%2e\";\n}\n\nfunction isWindowsDriveLetterCodePoints(cp1, cp2) {\n  return infra.isASCIIAlpha(cp1) && (cp2 === p(\":\") || cp2 === p(\"|\"));\n}\n\nfunction isWindowsDriveLetterString(string) {\n  return string.length === 2 && infra.isASCIIAlpha(string.codePointAt(0)) && (string[1] === \":\" || string[1] === \"|\");\n}\n\nfunction isNormalizedWindowsDriveLetterString(string) {\n  return string.length === 2 && infra.isASCIIAlpha(string.codePointAt(0)) && string[1] === \":\";\n}\n\nfunction containsForbiddenHostCodePoint(string) {\n  return string.search(/\\u0000|\\u0009|\\u000A|\\u000D|\\u0020|#|\\/|:|<|>|\\?|@|\\[|\\\\|\\]|\\^|\\|/u) !== -1;\n}\n\nfunction containsForbiddenDomainCodePoint(string) {\n  return containsForbiddenHostCodePoint(string) || string.search(/[\\u0000-\\u001F]|%|\\u007F/u) !== -1;\n}\n\nfunction isSpecialScheme(scheme) {\n  return specialSchemes[scheme] !== undefined;\n}\n\nfunction isSpecial(url) {\n  return isSpecialScheme(url.scheme);\n}\n\nfunction isNotSpecial(url) {\n  return !isSpecialScheme(url.scheme);\n}\n\nfunction defaultPort(scheme) {\n  return specialSchemes[scheme];\n}\n\nfunction parseIPv4Number(input) {\n  if (input === \"\") {\n    return failure;\n  }\n\n  let R = 10;\n\n  if (input.length >= 2 && input.charAt(0) === \"0\" && input.charAt(1).toLowerCase() === \"x\") {\n    input = input.substring(2);\n    R = 16;\n  } else if (input.length >= 2 && input.charAt(0) === \"0\") {\n    input = input.substring(1);\n    R = 8;\n  }\n\n  if (input === \"\") {\n    return 0;\n  }\n\n  let regex = /[^0-7]/u;\n  if (R === 10) {\n    regex = /[^0-9]/u;\n  }\n  if (R === 16) {\n    regex = /[^0-9A-Fa-f]/u;\n  }\n\n  if (regex.test(input)) {\n    return failure;\n  }\n\n  return parseInt(input, R);\n}\n\nfunction parseIPv4(input) {\n  const parts = input.split(\".\");\n  if (parts[parts.length - 1] === \"\") {\n    if (parts.length > 1) {\n      parts.pop();\n    }\n  }\n\n  if (parts.length > 4) {\n    return failure;\n  }\n\n  const numbers = [];\n  for (const part of parts) {\n    const n = parseIPv4Number(part);\n    if (n === failure) {\n      return failure;\n    }\n\n    numbers.push(n);\n  }\n\n  for (let i = 0; i < numbers.length - 1; ++i) {\n    if (numbers[i] > 255) {\n      return failure;\n    }\n  }\n  if (numbers[numbers.length - 1] >= 256 ** (5 - numbers.length)) {\n    return failure;\n  }\n\n  let ipv4 = numbers.pop();\n  let counter = 0;\n\n  for (const n of numbers) {\n    ipv4 += n * 256 ** (3 - counter);\n    ++counter;\n  }\n\n  return ipv4;\n}\n\nfunction serializeIPv4(address) {\n  let output = \"\";\n  let n = address;\n\n  for (let i = 1; i <= 4; ++i) {\n    output = String(n % 256) + output;\n    if (i !== 4) {\n      output = `.${output}`;\n    }\n    n = Math.floor(n / 256);\n  }\n\n  return output;\n}\n\nfunction parseIPv6(input) {\n  const address = [0, 0, 0, 0, 0, 0, 0, 0];\n  let pieceIndex = 0;\n  let compress = null;\n  let pointer = 0;\n\n  input = Array.from(input, c => c.codePointAt(0));\n\n  if (input[pointer] === p(\":\")) {\n    if (input[pointer + 1] !== p(\":\")) {\n      return failure;\n    }\n\n    pointer += 2;\n    ++pieceIndex;\n    compress = pieceIndex;\n  }\n\n  while (pointer < input.length) {\n    if (pieceIndex === 8) {\n      return failure;\n    }\n\n    if (input[pointer] === p(\":\")) {\n      if (compress !== null) {\n        return failure;\n      }\n      ++pointer;\n      ++pieceIndex;\n      compress = pieceIndex;\n      continue;\n    }\n\n    let value = 0;\n    let length = 0;\n\n    while (length < 4 && infra.isASCIIHex(input[pointer])) {\n      value = value * 0x10 + parseInt(at(input, pointer), 16);\n      ++pointer;\n      ++length;\n    }\n\n    if (input[pointer] === p(\".\")) {\n      if (length === 0) {\n        return failure;\n      }\n\n      pointer -= length;\n\n      if (pieceIndex > 6) {\n        return failure;\n      }\n\n      let numbersSeen = 0;\n\n      while (input[pointer] !== undefined) {\n        let ipv4Piece = null;\n\n        if (numbersSeen > 0) {\n          if (input[pointer] === p(\".\") && numbersSeen < 4) {\n            ++pointer;\n          } else {\n            return failure;\n          }\n        }\n\n        if (!infra.isASCIIDigit(input[pointer])) {\n          return failure;\n        }\n\n        while (infra.isASCIIDigit(input[pointer])) {\n          const number = parseInt(at(input, pointer));\n          if (ipv4Piece === null) {\n            ipv4Piece = number;\n          } else if (ipv4Piece === 0) {\n            return failure;\n          } else {\n            ipv4Piece = ipv4Piece * 10 + number;\n          }\n          if (ipv4Piece > 255) {\n            return failure;\n          }\n          ++pointer;\n        }\n\n        address[pieceIndex] = address[pieceIndex] * 0x100 + ipv4Piece;\n\n        ++numbersSeen;\n\n        if (numbersSeen === 2 || numbersSeen === 4) {\n          ++pieceIndex;\n        }\n      }\n\n      if (numbersSeen !== 4) {\n        return failure;\n      }\n\n      break;\n    } else if (input[pointer] === p(\":\")) {\n      ++pointer;\n      if (input[pointer] === undefined) {\n        return failure;\n      }\n    } else if (input[pointer] !== undefined) {\n      return failure;\n    }\n\n    address[pieceIndex] = value;\n    ++pieceIndex;\n  }\n\n  if (compress !== null) {\n    let swaps = pieceIndex - compress;\n    pieceIndex = 7;\n    while (pieceIndex !== 0 && swaps > 0) {\n      const temp = address[compress + swaps - 1];\n      address[compress + swaps - 1] = address[pieceIndex];\n      address[pieceIndex] = temp;\n      --pieceIndex;\n      --swaps;\n    }\n  } else if (compress === null && pieceIndex !== 8) {\n    return failure;\n  }\n\n  return address;\n}\n\nfunction serializeIPv6(address) {\n  let output = \"\";\n  const compress = findTheIPv6AddressCompressedPieceIndex(address);\n  let ignore0 = false;\n\n  for (let pieceIndex = 0; pieceIndex <= 7; ++pieceIndex) {\n    if (ignore0 && address[pieceIndex] === 0) {\n      continue;\n    } else if (ignore0) {\n      ignore0 = false;\n    }\n\n    if (compress === pieceIndex) {\n      const separator = pieceIndex === 0 ? \"::\" : \":\";\n      output += separator;\n      ignore0 = true;\n      continue;\n    }\n\n    output += address[pieceIndex].toString(16);\n\n    if (pieceIndex !== 7) {\n      output += \":\";\n    }\n  }\n\n  return output;\n}\n\nfunction parseHost(input, isOpaque = false) {\n  if (input[0] === \"[\") {\n    if (input[input.length - 1] !== \"]\") {\n      return failure;\n    }\n\n    return parseIPv6(input.substring(1, input.length - 1));\n  }\n\n  if (isOpaque) {\n    return parseOpaqueHost(input);\n  }\n\n  const domain = utf8DecodeWithoutBOM(percentDecodeString(input));\n  const asciiDomain = domainToASCII(domain);\n  if (asciiDomain === failure) {\n    return failure;\n  }\n\n  if (endsInANumber(asciiDomain)) {\n    return parseIPv4(asciiDomain);\n  }\n\n  return asciiDomain;\n}\n\nfunction endsInANumber(input) {\n  const parts = input.split(\".\");\n  if (parts[parts.length - 1] === \"\") {\n    if (parts.length === 1) {\n      return false;\n    }\n    parts.pop();\n  }\n\n  const last = parts[parts.length - 1];\n  if (parseIPv4Number(last) !== failure) {\n    return true;\n  }\n\n  if (/^[0-9]+$/u.test(last)) {\n    return true;\n  }\n\n  return false;\n}\n\nfunction parseOpaqueHost(input) {\n  if (containsForbiddenHostCodePoint(input)) {\n    return failure;\n  }\n\n  return utf8PercentEncodeString(input, isC0ControlPercentEncode);\n}\n\nfunction findTheIPv6AddressCompressedPieceIndex(address) {\n  let longestIndex = null;\n  let longestSize = 1; // only find elements > 1\n  let foundIndex = null;\n  let foundSize = 0;\n\n  for (let pieceIndex = 0; pieceIndex < address.length; ++pieceIndex) {\n    if (address[pieceIndex] !== 0) {\n      if (foundSize > longestSize) {\n        longestIndex = foundIndex;\n        longestSize = foundSize;\n      }\n\n      foundIndex = null;\n      foundSize = 0;\n    } else {\n      if (foundIndex === null) {\n        foundIndex = pieceIndex;\n      }\n      ++foundSize;\n    }\n  }\n\n  if (foundSize > longestSize) {\n    return foundIndex;\n  }\n\n  return longestIndex;\n}\n\nfunction serializeHost(host) {\n  if (typeof host === \"number\") {\n    return serializeIPv4(host);\n  }\n\n  // IPv6 serializer\n  if (host instanceof Array) {\n    return `[${serializeIPv6(host)}]`;\n  }\n\n  return host;\n}\n\nfunction domainToASCII(domain, beStrict = false) {\n  const result = tr46.toASCII(domain, {\n    checkHyphens: beStrict,\n    checkBidi: true,\n    checkJoiners: true,\n    useSTD3ASCIIRules: beStrict,\n    transitionalProcessing: false,\n    verifyDNSLength: beStrict,\n    ignoreInvalidPunycode: false\n  });\n  if (result === null) {\n    return failure;\n  }\n\n  if (!beStrict) {\n    if (result === \"\") {\n      return failure;\n    }\n    if (containsForbiddenDomainCodePoint(result)) {\n      return failure;\n    }\n  }\n  return result;\n}\n\nfunction trimControlChars(string) {\n  // Avoid using regexp because of this V8 bug: https://issues.chromium.org/issues/42204424\n\n  let start = 0;\n  let end = string.length;\n  for (; start < end; ++start) {\n    if (string.charCodeAt(start) > 0x20) {\n      break;\n    }\n  }\n  for (; end > start; --end) {\n    if (string.charCodeAt(end - 1) > 0x20) {\n      break;\n    }\n  }\n  return string.substring(start, end);\n}\n\nfunction trimTabAndNewline(url) {\n  return url.replace(/\\u0009|\\u000A|\\u000D/ug, \"\");\n}\n\nfunction shortenPath(url) {\n  const { path } = url;\n  if (path.length === 0) {\n    return;\n  }\n  if (url.scheme === \"file\" && path.length === 1 && isNormalizedWindowsDriveLetter(path[0])) {\n    return;\n  }\n\n  path.pop();\n}\n\nfunction includesCredentials(url) {\n  return url.username !== \"\" || url.password !== \"\";\n}\n\nfunction cannotHaveAUsernamePasswordPort(url) {\n  return url.host === null || url.host === \"\" || url.scheme === \"file\";\n}\n\nfunction hasAnOpaquePath(url) {\n  return typeof url.path === \"string\";\n}\n\nfunction isNormalizedWindowsDriveLetter(string) {\n  return /^[A-Za-z]:$/u.test(string);\n}\n\nfunction URLStateMachine(input, base, encodingOverride, url, stateOverride) {\n  this.pointer = 0;\n  this.input = input;\n  this.base = base || null;\n  this.encodingOverride = encodingOverride || \"utf-8\";\n  this.stateOverride = stateOverride;\n  this.url = url;\n  this.failure = false;\n  this.parseError = false;\n\n  if (!this.url) {\n    this.url = {\n      scheme: \"\",\n      username: \"\",\n      password: \"\",\n      host: null,\n      port: null,\n      path: [],\n      query: null,\n      fragment: null\n    };\n\n    const res = trimControlChars(this.input);\n    if (res !== this.input) {\n      this.parseError = true;\n    }\n    this.input = res;\n  }\n\n  const res = trimTabAndNewline(this.input);\n  if (res !== this.input) {\n    this.parseError = true;\n  }\n  this.input = res;\n\n  this.state = stateOverride || \"scheme start\";\n\n  this.buffer = \"\";\n  this.atFlag = false;\n  this.arrFlag = false;\n  this.passwordTokenSeenFlag = false;\n\n  this.input = Array.from(this.input, c => c.codePointAt(0));\n\n  for (; this.pointer <= this.input.length; ++this.pointer) {\n    const c = this.input[this.pointer];\n    const cStr = isNaN(c) ? undefined : String.fromCodePoint(c);\n\n    // exec state machine\n    const ret = this[`parse ${this.state}`](c, cStr);\n    if (!ret) {\n      break; // terminate algorithm\n    } else if (ret === failure) {\n      this.failure = true;\n      break;\n    }\n  }\n}\n\nURLStateMachine.prototype[\"parse scheme start\"] = function parseSchemeStart(c, cStr) {\n  if (infra.isASCIIAlpha(c)) {\n    this.buffer += cStr.toLowerCase();\n    this.state = \"scheme\";\n  } else if (!this.stateOverride) {\n    this.state = \"no scheme\";\n    --this.pointer;\n  } else {\n    this.parseError = true;\n    return failure;\n  }\n\n  return true;\n};\n\nURLStateMachine.prototype[\"parse scheme\"] = function parseScheme(c, cStr) {\n  if (infra.isASCIIAlphanumeric(c) || c === p(\"+\") || c === p(\"-\") || c === p(\".\")) {\n    this.buffer += cStr.toLowerCase();\n  } else if (c === p(\":\")) {\n    if (this.stateOverride) {\n      if (isSpecial(this.url) && !isSpecialScheme(this.buffer)) {\n        return false;\n      }\n\n      if (!isSpecial(this.url) && isSpecialScheme(this.buffer)) {\n        return false;\n      }\n\n      if ((includesCredentials(this.url) || this.url.port !== null) && this.buffer === \"file\") {\n        return false;\n      }\n\n      if (this.url.scheme === \"file\" && this.url.host === \"\") {\n        return false;\n      }\n    }\n    this.url.scheme = this.buffer;\n    if (this.stateOverride) {\n      if (this.url.port === defaultPort(this.url.scheme)) {\n        this.url.port = null;\n      }\n      return false;\n    }\n    this.buffer = \"\";\n    if (this.url.scheme === \"file\") {\n      if (this.input[this.pointer + 1] !== p(\"/\") || this.input[this.pointer + 2] !== p(\"/\")) {\n        this.parseError = true;\n      }\n      this.state = \"file\";\n    } else if (isSpecial(this.url) && this.base !== null && this.base.scheme === this.url.scheme) {\n      this.state = \"special relative or authority\";\n    } else if (isSpecial(this.url)) {\n      this.state = \"special authority slashes\";\n    } else if (this.input[this.pointer + 1] === p(\"/\")) {\n      this.state = \"path or authority\";\n      ++this.pointer;\n    } else {\n      this.url.path = \"\";\n      this.state = \"opaque path\";\n    }\n  } else if (!this.stateOverride) {\n    this.buffer = \"\";\n    this.state = \"no scheme\";\n    this.pointer = -1;\n  } else {\n    this.parseError = true;\n    return failure;\n  }\n\n  return true;\n};\n\nURLStateMachine.prototype[\"parse no scheme\"] = function parseNoScheme(c) {\n  if (this.base === null || (hasAnOpaquePath(this.base) && c !== p(\"#\"))) {\n    return failure;\n  } else if (hasAnOpaquePath(this.base) && c === p(\"#\")) {\n    this.url.scheme = this.base.scheme;\n    this.url.path = this.base.path;\n    this.url.query = this.base.query;\n    this.url.fragment = \"\";\n    this.state = \"fragment\";\n  } else if (this.base.scheme === \"file\") {\n    this.state = \"file\";\n    --this.pointer;\n  } else {\n    this.state = \"relative\";\n    --this.pointer;\n  }\n\n  return true;\n};\n\nURLStateMachine.prototype[\"parse special relative or authority\"] = function parseSpecialRelativeOrAuthority(c) {\n  if (c === p(\"/\") && this.input[this.pointer + 1] === p(\"/\")) {\n    this.state = \"special authority ignore slashes\";\n    ++this.pointer;\n  } else {\n    this.parseError = true;\n    this.state = \"relative\";\n    --this.pointer;\n  }\n\n  return true;\n};\n\nURLStateMachine.prototype[\"parse path or authority\"] = function parsePathOrAuthority(c) {\n  if (c === p(\"/\")) {\n    this.state = \"authority\";\n  } else {\n    this.state = \"path\";\n    --this.pointer;\n  }\n\n  return true;\n};\n\nURLStateMachine.prototype[\"parse relative\"] = function parseRelative(c) {\n  this.url.scheme = this.base.scheme;\n  if (c === p(\"/\")) {\n    this.state = \"relative slash\";\n  } else if (isSpecial(this.url) && c === p(\"\\\\\")) {\n    this.parseError = true;\n    this.state = \"relative slash\";\n  } else {\n    this.url.username = this.base.username;\n    this.url.password = this.base.password;\n    this.url.host = this.base.host;\n    this.url.port = this.base.port;\n    this.url.path = this.base.path.slice();\n    this.url.query = this.base.query;\n    if (c === p(\"?\")) {\n      this.url.query = \"\";\n      this.state = \"query\";\n    } else if (c === p(\"#\")) {\n      this.url.fragment = \"\";\n      this.state = \"fragment\";\n    } else if (!isNaN(c)) {\n      this.url.query = null;\n      this.url.path.pop();\n      this.state = \"path\";\n      --this.pointer;\n    }\n  }\n\n  return true;\n};\n\nURLStateMachine.prototype[\"parse relative slash\"] = function parseRelativeSlash(c) {\n  if (isSpecial(this.url) && (c === p(\"/\") || c === p(\"\\\\\"))) {\n    if (c === p(\"\\\\\")) {\n      this.parseError = true;\n    }\n    this.state = \"special authority ignore slashes\";\n  } else if (c === p(\"/\")) {\n    this.state = \"authority\";\n  } else {\n    this.url.username = this.base.username;\n    this.url.password = this.base.password;\n    this.url.host = this.base.host;\n    this.url.port = this.base.port;\n    this.state = \"path\";\n    --this.pointer;\n  }\n\n  return true;\n};\n\nURLStateMachine.prototype[\"parse special authority slashes\"] = function parseSpecialAuthoritySlashes(c) {\n  if (c === p(\"/\") && this.input[this.pointer + 1] === p(\"/\")) {\n    this.state = \"special authority ignore slashes\";\n    ++this.pointer;\n  } else {\n    this.parseError = true;\n    this.state = \"special authority ignore slashes\";\n    --this.pointer;\n  }\n\n  return true;\n};\n\nURLStateMachine.prototype[\"parse special authority ignore slashes\"] = function parseSpecialAuthorityIgnoreSlashes(c) {\n  if (c !== p(\"/\") && c !== p(\"\\\\\")) {\n    this.state = \"authority\";\n    --this.pointer;\n  } else {\n    this.parseError = true;\n  }\n\n  return true;\n};\n\nURLStateMachine.prototype[\"parse authority\"] = function parseAuthority(c, cStr) {\n  if (c === p(\"@\")) {\n    this.parseError = true;\n    if (this.atFlag) {\n      this.buffer = `%40${this.buffer}`;\n    }\n    this.atFlag = true;\n\n    // careful, this is based on buffer and has its own pointer (this.pointer != pointer) and inner chars\n    const len = countSymbols(this.buffer);\n    for (let pointer = 0; pointer < len; ++pointer) {\n      const codePoint = this.buffer.codePointAt(pointer);\n\n      if (codePoint === p(\":\") && !this.passwordTokenSeenFlag) {\n        this.passwordTokenSeenFlag = true;\n        continue;\n      }\n      const encodedCodePoints = utf8PercentEncodeCodePoint(codePoint, isUserinfoPercentEncode);\n      if (this.passwordTokenSeenFlag) {\n        this.url.password += encodedCodePoints;\n      } else {\n        this.url.username += encodedCodePoints;\n      }\n    }\n    this.buffer = \"\";\n  } else if (isNaN(c) || c === p(\"/\") || c === p(\"?\") || c === p(\"#\") ||\n             (isSpecial(this.url) && c === p(\"\\\\\"))) {\n    if (this.atFlag && this.buffer === \"\") {\n      this.parseError = true;\n      return failure;\n    }\n    this.pointer -= countSymbols(this.buffer) + 1;\n    this.buffer = \"\";\n    this.state = \"host\";\n  } else {\n    this.buffer += cStr;\n  }\n\n  return true;\n};\n\nURLStateMachine.prototype[\"parse hostname\"] =\nURLStateMachine.prototype[\"parse host\"] = function parseHostName(c, cStr) {\n  if (this.stateOverride && this.url.scheme === \"file\") {\n    --this.pointer;\n    this.state = \"file host\";\n  } else if (c === p(\":\") && !this.arrFlag) {\n    if (this.buffer === \"\") {\n      this.parseError = true;\n      return failure;\n    }\n\n    if (this.stateOverride === \"hostname\") {\n      return false;\n    }\n\n    const host = parseHost(this.buffer, isNotSpecial(this.url));\n    if (host === failure) {\n      return failure;\n    }\n\n    this.url.host = host;\n    this.buffer = \"\";\n    this.state = \"port\";\n  } else if (isNaN(c) || c === p(\"/\") || c === p(\"?\") || c === p(\"#\") ||\n             (isSpecial(this.url) && c === p(\"\\\\\"))) {\n    --this.pointer;\n    if (isSpecial(this.url) && this.buffer === \"\") {\n      this.parseError = true;\n      return failure;\n    } else if (this.stateOverride && this.buffer === \"\" &&\n               (includesCredentials(this.url) || this.url.port !== null)) {\n      this.parseError = true;\n      return false;\n    }\n\n    const host = parseHost(this.buffer, isNotSpecial(this.url));\n    if (host === failure) {\n      return failure;\n    }\n\n    this.url.host = host;\n    this.buffer = \"\";\n    this.state = \"path start\";\n    if (this.stateOverride) {\n      return false;\n    }\n  } else {\n    if (c === p(\"[\")) {\n      this.arrFlag = true;\n    } else if (c === p(\"]\")) {\n      this.arrFlag = false;\n    }\n    this.buffer += cStr;\n  }\n\n  return true;\n};\n\nURLStateMachine.prototype[\"parse port\"] = function parsePort(c, cStr) {\n  if (infra.isASCIIDigit(c)) {\n    this.buffer += cStr;\n  } else if (isNaN(c) || c === p(\"/\") || c === p(\"?\") || c === p(\"#\") ||\n             (isSpecial(this.url) && c === p(\"\\\\\")) ||\n             this.stateOverride) {\n    if (this.buffer !== \"\") {\n      const port = parseInt(this.buffer);\n      if (port > 2 ** 16 - 1) {\n        this.parseError = true;\n        return failure;\n      }\n      this.url.port = port === defaultPort(this.url.scheme) ? null : port;\n      this.buffer = \"\";\n    }\n    if (this.stateOverride) {\n      return false;\n    }\n    this.state = \"path start\";\n    --this.pointer;\n  } else {\n    this.parseError = true;\n    return failure;\n  }\n\n  return true;\n};\n\nconst fileOtherwiseCodePoints = new Set([p(\"/\"), p(\"\\\\\"), p(\"?\"), p(\"#\")]);\n\nfunction startsWithWindowsDriveLetter(input, pointer) {\n  const length = input.length - pointer;\n  return length >= 2 &&\n    isWindowsDriveLetterCodePoints(input[pointer], input[pointer + 1]) &&\n    (length === 2 || fileOtherwiseCodePoints.has(input[pointer + 2]));\n}\n\nURLStateMachine.prototype[\"parse file\"] = function parseFile(c) {\n  this.url.scheme = \"file\";\n  this.url.host = \"\";\n\n  if (c === p(\"/\") || c === p(\"\\\\\")) {\n    if (c === p(\"\\\\\")) {\n      this.parseError = true;\n    }\n    this.state = \"file slash\";\n  } else if (this.base !== null && this.base.scheme === \"file\") {\n    this.url.host = this.base.host;\n    this.url.path = this.base.path.slice();\n    this.url.query = this.base.query;\n    if (c === p(\"?\")) {\n      this.url.query = \"\";\n      this.state = \"query\";\n    } else if (c === p(\"#\")) {\n      this.url.fragment = \"\";\n      this.state = \"fragment\";\n    } else if (!isNaN(c)) {\n      this.url.query = null;\n      if (!startsWithWindowsDriveLetter(this.input, this.pointer)) {\n        shortenPath(this.url);\n      } else {\n        this.parseError = true;\n        this.url.path = [];\n      }\n\n      this.state = \"path\";\n      --this.pointer;\n    }\n  } else {\n    this.state = \"path\";\n    --this.pointer;\n  }\n\n  return true;\n};\n\nURLStateMachine.prototype[\"parse file slash\"] = function parseFileSlash(c) {\n  if (c === p(\"/\") || c === p(\"\\\\\")) {\n    if (c === p(\"\\\\\")) {\n      this.parseError = true;\n    }\n    this.state = \"file host\";\n  } else {\n    if (this.base !== null && this.base.scheme === \"file\") {\n      if (!startsWithWindowsDriveLetter(this.input, this.pointer) &&\n          isNormalizedWindowsDriveLetterString(this.base.path[0])) {\n        this.url.path.push(this.base.path[0]);\n      }\n      this.url.host = this.base.host;\n    }\n    this.state = \"path\";\n    --this.pointer;\n  }\n\n  return true;\n};\n\nURLStateMachine.prototype[\"parse file host\"] = function parseFileHost(c, cStr) {\n  if (isNaN(c) || c === p(\"/\") || c === p(\"\\\\\") || c === p(\"?\") || c === p(\"#\")) {\n    --this.pointer;\n    if (!this.stateOverride && isWindowsDriveLetterString(this.buffer)) {\n      this.parseError = true;\n      this.state = \"path\";\n    } else if (this.buffer === \"\") {\n      this.url.host = \"\";\n      if (this.stateOverride) {\n        return false;\n      }\n      this.state = \"path start\";\n    } else {\n      let host = parseHost(this.buffer, isNotSpecial(this.url));\n      if (host === failure) {\n        return failure;\n      }\n      if (host === \"localhost\") {\n        host = \"\";\n      }\n      this.url.host = host;\n\n      if (this.stateOverride) {\n        return false;\n      }\n\n      this.buffer = \"\";\n      this.state = \"path start\";\n    }\n  } else {\n    this.buffer += cStr;\n  }\n\n  return true;\n};\n\nURLStateMachine.prototype[\"parse path start\"] = function parsePathStart(c) {\n  if (isSpecial(this.url)) {\n    if (c === p(\"\\\\\")) {\n      this.parseError = true;\n    }\n    this.state = \"path\";\n\n    if (c !== p(\"/\") && c !== p(\"\\\\\")) {\n      --this.pointer;\n    }\n  } else if (!this.stateOverride && c === p(\"?\")) {\n    this.url.query = \"\";\n    this.state = \"query\";\n  } else if (!this.stateOverride && c === p(\"#\")) {\n    this.url.fragment = \"\";\n    this.state = \"fragment\";\n  } else if (c !== undefined) {\n    this.state = \"path\";\n    if (c !== p(\"/\")) {\n      --this.pointer;\n    }\n  } else if (this.stateOverride && this.url.host === null) {\n    this.url.path.push(\"\");\n  }\n\n  return true;\n};\n\nURLStateMachine.prototype[\"parse path\"] = function parsePath(c) {\n  if (isNaN(c) || c === p(\"/\") || (isSpecial(this.url) && c === p(\"\\\\\")) ||\n      (!this.stateOverride && (c === p(\"?\") || c === p(\"#\")))) {\n    if (isSpecial(this.url) && c === p(\"\\\\\")) {\n      this.parseError = true;\n    }\n\n    if (isDoubleDot(this.buffer)) {\n      shortenPath(this.url);\n      if (c !== p(\"/\") && !(isSpecial(this.url) && c === p(\"\\\\\"))) {\n        this.url.path.push(\"\");\n      }\n    } else if (isSingleDot(this.buffer) && c !== p(\"/\") &&\n               !(isSpecial(this.url) && c === p(\"\\\\\"))) {\n      this.url.path.push(\"\");\n    } else if (!isSingleDot(this.buffer)) {\n      if (this.url.scheme === \"file\" && this.url.path.length === 0 && isWindowsDriveLetterString(this.buffer)) {\n        this.buffer = `${this.buffer[0]}:`;\n      }\n      this.url.path.push(this.buffer);\n    }\n    this.buffer = \"\";\n    if (c === p(\"?\")) {\n      this.url.query = \"\";\n      this.state = \"query\";\n    }\n    if (c === p(\"#\")) {\n      this.url.fragment = \"\";\n      this.state = \"fragment\";\n    }\n  } else {\n    // TODO: If c is not a URL code point and not \"%\", parse error.\n\n    if (c === p(\"%\") &&\n      (!infra.isASCIIHex(this.input[this.pointer + 1]) ||\n        !infra.isASCIIHex(this.input[this.pointer + 2]))) {\n      this.parseError = true;\n    }\n\n    this.buffer += utf8PercentEncodeCodePoint(c, isPathPercentEncode);\n  }\n\n  return true;\n};\n\nURLStateMachine.prototype[\"parse opaque path\"] = function parseOpaquePath(c) {\n  if (c === p(\"?\")) {\n    this.url.query = \"\";\n    this.state = \"query\";\n  } else if (c === p(\"#\")) {\n    this.url.fragment = \"\";\n    this.state = \"fragment\";\n  } else if (c === p(\" \")) {\n    const remaining = this.input[this.pointer + 1];\n    if (remaining === p(\"?\") || remaining === p(\"#\")) {\n      this.url.path += \"%20\";\n    } else {\n      this.url.path += \" \";\n    }\n  } else {\n    // TODO: Add: not a URL code point\n    if (!isNaN(c) && c !== p(\"%\")) {\n      this.parseError = true;\n    }\n\n    if (c === p(\"%\") &&\n        (!infra.isASCIIHex(this.input[this.pointer + 1]) ||\n         !infra.isASCIIHex(this.input[this.pointer + 2]))) {\n      this.parseError = true;\n    }\n\n    if (!isNaN(c)) {\n      this.url.path += utf8PercentEncodeCodePoint(c, isC0ControlPercentEncode);\n    }\n  }\n\n  return true;\n};\n\nURLStateMachine.prototype[\"parse query\"] = function parseQuery(c, cStr) {\n  if (!isSpecial(this.url) || this.url.scheme === \"ws\" || this.url.scheme === \"wss\") {\n    this.encodingOverride = \"utf-8\";\n  }\n\n  if ((!this.stateOverride && c === p(\"#\")) || isNaN(c)) {\n    const queryPercentEncodePredicate = isSpecial(this.url) ? isSpecialQueryPercentEncode : isQueryPercentEncode;\n    this.url.query += utf8PercentEncodeString(this.buffer, queryPercentEncodePredicate);\n\n    this.buffer = \"\";\n\n    if (c === p(\"#\")) {\n      this.url.fragment = \"\";\n      this.state = \"fragment\";\n    }\n  } else if (!isNaN(c)) {\n    // TODO: If c is not a URL code point and not \"%\", parse error.\n\n    if (c === p(\"%\") &&\n      (!infra.isASCIIHex(this.input[this.pointer + 1]) ||\n        !infra.isASCIIHex(this.input[this.pointer + 2]))) {\n      this.parseError = true;\n    }\n\n    this.buffer += cStr;\n  }\n\n  return true;\n};\n\nURLStateMachine.prototype[\"parse fragment\"] = function parseFragment(c) {\n  if (!isNaN(c)) {\n    // TODO: If c is not a URL code point and not \"%\", parse error.\n    if (c === p(\"%\") &&\n      (!infra.isASCIIHex(this.input[this.pointer + 1]) ||\n        !infra.isASCIIHex(this.input[this.pointer + 2]))) {\n      this.parseError = true;\n    }\n\n    this.url.fragment += utf8PercentEncodeCodePoint(c, isFragmentPercentEncode);\n  }\n\n  return true;\n};\n\nfunction serializeURL(url, excludeFragment) {\n  let output = `${url.scheme}:`;\n  if (url.host !== null) {\n    output += \"//\";\n\n    if (url.username !== \"\" || url.password !== \"\") {\n      output += url.username;\n      if (url.password !== \"\") {\n        output += `:${url.password}`;\n      }\n      output += \"@\";\n    }\n\n    output += serializeHost(url.host);\n\n    if (url.port !== null) {\n      output += `:${url.port}`;\n    }\n  }\n\n  if (url.host === null && !hasAnOpaquePath(url) && url.path.length > 1 && url.path[0] === \"\") {\n    output += \"/.\";\n  }\n  output += serializePath(url);\n\n  if (url.query !== null) {\n    output += `?${url.query}`;\n  }\n\n  if (!excludeFragment && url.fragment !== null) {\n    output += `#${url.fragment}`;\n  }\n\n  return output;\n}\n\nfunction serializeOrigin(tuple) {\n  let result = `${tuple.scheme}://`;\n  result += serializeHost(tuple.host);\n\n  if (tuple.port !== null) {\n    result += `:${tuple.port}`;\n  }\n\n  return result;\n}\n\nfunction serializePath(url) {\n  if (hasAnOpaquePath(url)) {\n    return url.path;\n  }\n\n  let output = \"\";\n  for (const segment of url.path) {\n    output += `/${segment}`;\n  }\n  return output;\n}\n\nmodule.exports.serializeURL = serializeURL;\n\nmodule.exports.serializePath = serializePath;\n\nmodule.exports.serializeURLOrigin = function (url) {\n  // https://url.spec.whatwg.org/#concept-url-origin\n  switch (url.scheme) {\n    case \"blob\": {\n      const pathURL = module.exports.parseURL(serializePath(url));\n      if (pathURL === null) {\n        return \"null\";\n      }\n      if (pathURL.scheme !== \"http\" && pathURL.scheme !== \"https\") {\n        return \"null\";\n      }\n      return module.exports.serializeURLOrigin(pathURL);\n    }\n    case \"ftp\":\n    case \"http\":\n    case \"https\":\n    case \"ws\":\n    case \"wss\":\n      return serializeOrigin({\n        scheme: url.scheme,\n        host: url.host,\n        port: url.port\n      });\n    case \"file\":\n      // The spec says:\n      // > Unfortunate as it is, this is left as an exercise to the reader. When in doubt, return a new opaque origin.\n      // Browsers tested so far:\n      // - Chrome says \"file://\", but treats file: URLs as cross-origin for most (all?) purposes; see e.g.\n      //   https://bugs.chromium.org/p/chromium/issues/detail?id=37586\n      // - Firefox says \"null\", but treats file: URLs as same-origin sometimes based on directory stuff; see\n      //   https://developer.mozilla.org/en-US/docs/Archive/Misc_top_level/Same-origin_policy_for_file:_URIs\n      return \"null\";\n    default:\n      // serializing an opaque origin returns \"null\"\n      return \"null\";\n  }\n};\n\nmodule.exports.basicURLParse = function (input, options) {\n  if (options === undefined) {\n    options = {};\n  }\n\n  const usm = new URLStateMachine(input, options.baseURL, options.encodingOverride, options.url, options.stateOverride);\n  if (usm.failure) {\n    return null;\n  }\n\n  return usm.url;\n};\n\nmodule.exports.setTheUsername = function (url, username) {\n  url.username = utf8PercentEncodeString(username, isUserinfoPercentEncode);\n};\n\nmodule.exports.setThePassword = function (url, password) {\n  url.password = utf8PercentEncodeString(password, isUserinfoPercentEncode);\n};\n\nmodule.exports.serializeHost = serializeHost;\n\nmodule.exports.cannotHaveAUsernamePasswordPort = cannotHaveAUsernamePasswordPort;\n\nmodule.exports.hasAnOpaquePath = hasAnOpaquePath;\n\nmodule.exports.serializeInteger = function (integer) {\n  return String(integer);\n};\n\nmodule.exports.parseURL = function (input, options) {\n  if (options === undefined) {\n    options = {};\n  }\n\n  // We don't handle blobs, so this just delegates:\n  return module.exports.basicURLParse(input, { baseURL: options.baseURL, encodingOverride: options.encodingOverride });\n};\n"], "mappings": "AAAA,YAAY;;AACZ,MAAMA,IAAI,GAAGC,OAAO,CAAC,MAAM,CAAC;AAE5B,MAAMC,KAAK,GAAGD,OAAO,CAAC,SAAS,CAAC;AAChC,MAAM;EAAEE;AAAqB,CAAC,GAAGF,OAAO,CAAC,YAAY,CAAC;AACtD,MAAM;EAAEG,mBAAmB;EAAEC,0BAA0B;EAAEC,uBAAuB;EAAEC,wBAAwB;EACxGC,uBAAuB;EAAEC,oBAAoB;EAAEC,2BAA2B;EAAEC,mBAAmB;EAC/FC;AAAwB,CAAC,GAAGX,OAAO,CAAC,oBAAoB,CAAC;AAE3D,SAASY,CAACA,CAACC,IAAI,EAAE;EACf,OAAOA,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;AAC5B;AAEA,MAAMC,cAAc,GAAG;EACrBC,GAAG,EAAE,EAAE;EACPC,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,EAAE;EACRC,KAAK,EAAE,GAAG;EACVC,EAAE,EAAE,EAAE;EACNC,GAAG,EAAE;AACP,CAAC;AAED,MAAMC,OAAO,GAAGC,MAAM,CAAC,SAAS,CAAC;AAEjC,SAASC,YAAYA,CAACC,GAAG,EAAE;EACzB,OAAO,CAAC,GAAGA,GAAG,CAAC,CAACC,MAAM;AACxB;AAEA,SAASC,EAAEA,CAACC,KAAK,EAAEC,GAAG,EAAE;EACtB,MAAMC,CAAC,GAAGF,KAAK,CAACC,GAAG,CAAC;EACpB,OAAOE,KAAK,CAACD,CAAC,CAAC,GAAGE,SAAS,GAAGC,MAAM,CAACC,aAAa,CAACJ,CAAC,CAAC;AACvD;AAEA,SAASK,WAAWA,CAACC,MAAM,EAAE;EAC3B,OAAOA,MAAM,KAAK,GAAG,IAAIA,MAAM,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK;AACzD;AAEA,SAASC,WAAWA,CAACF,MAAM,EAAE;EAC3BA,MAAM,GAAGA,MAAM,CAACC,WAAW,CAAC,CAAC;EAC7B,OAAOD,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,MAAM,IAAIA,MAAM,KAAK,QAAQ;AACzF;AAEA,SAASG,8BAA8BA,CAACC,GAAG,EAAEC,GAAG,EAAE;EAChD,OAAOxC,KAAK,CAACyC,YAAY,CAACF,GAAG,CAAC,KAAKC,GAAG,KAAK7B,CAAC,CAAC,GAAG,CAAC,IAAI6B,GAAG,KAAK7B,CAAC,CAAC,GAAG,CAAC,CAAC;AACtE;AAEA,SAAS+B,0BAA0BA,CAACC,MAAM,EAAE;EAC1C,OAAOA,MAAM,CAAClB,MAAM,KAAK,CAAC,IAAIzB,KAAK,CAACyC,YAAY,CAACE,MAAM,CAAC9B,WAAW,CAAC,CAAC,CAAC,CAAC,KAAK8B,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAIA,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,CAAC;AACrH;AAEA,SAASC,oCAAoCA,CAACD,MAAM,EAAE;EACpD,OAAOA,MAAM,CAAClB,MAAM,KAAK,CAAC,IAAIzB,KAAK,CAACyC,YAAY,CAACE,MAAM,CAAC9B,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI8B,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG;AAC9F;AAEA,SAASE,8BAA8BA,CAACF,MAAM,EAAE;EAC9C,OAAOA,MAAM,CAACG,MAAM,CAAC,oEAAoE,CAAC,KAAK,CAAC,CAAC;AACnG;AAEA,SAASC,gCAAgCA,CAACJ,MAAM,EAAE;EAChD,OAAOE,8BAA8B,CAACF,MAAM,CAAC,IAAIA,MAAM,CAACG,MAAM,CAAC,2BAA2B,CAAC,KAAK,CAAC,CAAC;AACpG;AAEA,SAASE,eAAeA,CAACC,MAAM,EAAE;EAC/B,OAAOnC,cAAc,CAACmC,MAAM,CAAC,KAAKlB,SAAS;AAC7C;AAEA,SAASmB,SAASA,CAACC,GAAG,EAAE;EACtB,OAAOH,eAAe,CAACG,GAAG,CAACF,MAAM,CAAC;AACpC;AAEA,SAASG,YAAYA,CAACD,GAAG,EAAE;EACzB,OAAO,CAACH,eAAe,CAACG,GAAG,CAACF,MAAM,CAAC;AACrC;AAEA,SAASI,WAAWA,CAACJ,MAAM,EAAE;EAC3B,OAAOnC,cAAc,CAACmC,MAAM,CAAC;AAC/B;AAEA,SAASK,eAAeA,CAAC3B,KAAK,EAAE;EAC9B,IAAIA,KAAK,KAAK,EAAE,EAAE;IAChB,OAAON,OAAO;EAChB;EAEA,IAAIkC,CAAC,GAAG,EAAE;EAEV,IAAI5B,KAAK,CAACF,MAAM,IAAI,CAAC,IAAIE,KAAK,CAAC6B,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,IAAI7B,KAAK,CAAC6B,MAAM,CAAC,CAAC,CAAC,CAACpB,WAAW,CAAC,CAAC,KAAK,GAAG,EAAE;IACzFT,KAAK,GAAGA,KAAK,CAAC8B,SAAS,CAAC,CAAC,CAAC;IAC1BF,CAAC,GAAG,EAAE;EACR,CAAC,MAAM,IAAI5B,KAAK,CAACF,MAAM,IAAI,CAAC,IAAIE,KAAK,CAAC6B,MAAM,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACvD7B,KAAK,GAAGA,KAAK,CAAC8B,SAAS,CAAC,CAAC,CAAC;IAC1BF,CAAC,GAAG,CAAC;EACP;EAEA,IAAI5B,KAAK,KAAK,EAAE,EAAE;IAChB,OAAO,CAAC;EACV;EAEA,IAAI+B,KAAK,GAAG,SAAS;EACrB,IAAIH,CAAC,KAAK,EAAE,EAAE;IACZG,KAAK,GAAG,SAAS;EACnB;EACA,IAAIH,CAAC,KAAK,EAAE,EAAE;IACZG,KAAK,GAAG,eAAe;EACzB;EAEA,IAAIA,KAAK,CAACC,IAAI,CAAChC,KAAK,CAAC,EAAE;IACrB,OAAON,OAAO;EAChB;EAEA,OAAOuC,QAAQ,CAACjC,KAAK,EAAE4B,CAAC,CAAC;AAC3B;AAEA,SAASM,SAASA,CAAClC,KAAK,EAAE;EACxB,MAAMmC,KAAK,GAAGnC,KAAK,CAACoC,KAAK,CAAC,GAAG,CAAC;EAC9B,IAAID,KAAK,CAACA,KAAK,CAACrC,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;IAClC,IAAIqC,KAAK,CAACrC,MAAM,GAAG,CAAC,EAAE;MACpBqC,KAAK,CAACE,GAAG,CAAC,CAAC;IACb;EACF;EAEA,IAAIF,KAAK,CAACrC,MAAM,GAAG,CAAC,EAAE;IACpB,OAAOJ,OAAO;EAChB;EAEA,MAAM4C,OAAO,GAAG,EAAE;EAClB,KAAK,MAAMC,IAAI,IAAIJ,KAAK,EAAE;IACxB,MAAMK,CAAC,GAAGb,eAAe,CAACY,IAAI,CAAC;IAC/B,IAAIC,CAAC,KAAK9C,OAAO,EAAE;MACjB,OAAOA,OAAO;IAChB;IAEA4C,OAAO,CAACG,IAAI,CAACD,CAAC,CAAC;EACjB;EAEA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGJ,OAAO,CAACxC,MAAM,GAAG,CAAC,EAAE,EAAE4C,CAAC,EAAE;IAC3C,IAAIJ,OAAO,CAACI,CAAC,CAAC,GAAG,GAAG,EAAE;MACpB,OAAOhD,OAAO;IAChB;EACF;EACA,IAAI4C,OAAO,CAACA,OAAO,CAACxC,MAAM,GAAG,CAAC,CAAC,IAAI,GAAG,KAAK,CAAC,GAAGwC,OAAO,CAACxC,MAAM,CAAC,EAAE;IAC9D,OAAOJ,OAAO;EAChB;EAEA,IAAIiD,IAAI,GAAGL,OAAO,CAACD,GAAG,CAAC,CAAC;EACxB,IAAIO,OAAO,GAAG,CAAC;EAEf,KAAK,MAAMJ,CAAC,IAAIF,OAAO,EAAE;IACvBK,IAAI,IAAIH,CAAC,GAAG,GAAG,KAAK,CAAC,GAAGI,OAAO,CAAC;IAChC,EAAEA,OAAO;EACX;EAEA,OAAOD,IAAI;AACb;AAEA,SAASE,aAAaA,CAACC,OAAO,EAAE;EAC9B,IAAIC,MAAM,GAAG,EAAE;EACf,IAAIP,CAAC,GAAGM,OAAO;EAEf,KAAK,IAAIJ,CAAC,GAAG,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE,EAAEA,CAAC,EAAE;IAC3BK,MAAM,GAAG1C,MAAM,CAACmC,CAAC,GAAG,GAAG,CAAC,GAAGO,MAAM;IACjC,IAAIL,CAAC,KAAK,CAAC,EAAE;MACXK,MAAM,GAAG,IAAIA,MAAM,EAAE;IACvB;IACAP,CAAC,GAAGQ,IAAI,CAACC,KAAK,CAACT,CAAC,GAAG,GAAG,CAAC;EACzB;EAEA,OAAOO,MAAM;AACf;AAEA,SAASG,SAASA,CAAClD,KAAK,EAAE;EACxB,MAAM8C,OAAO,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACxC,IAAIK,UAAU,GAAG,CAAC;EAClB,IAAIC,QAAQ,GAAG,IAAI;EACnB,IAAIC,OAAO,GAAG,CAAC;EAEfrD,KAAK,GAAGsD,KAAK,CAACC,IAAI,CAACvD,KAAK,EAAEE,CAAC,IAAIA,CAAC,CAAChB,WAAW,CAAC,CAAC,CAAC,CAAC;EAEhD,IAAIc,KAAK,CAACqD,OAAO,CAAC,KAAKrE,CAAC,CAAC,GAAG,CAAC,EAAE;IAC7B,IAAIgB,KAAK,CAACqD,OAAO,GAAG,CAAC,CAAC,KAAKrE,CAAC,CAAC,GAAG,CAAC,EAAE;MACjC,OAAOU,OAAO;IAChB;IAEA2D,OAAO,IAAI,CAAC;IACZ,EAAEF,UAAU;IACZC,QAAQ,GAAGD,UAAU;EACvB;EAEA,OAAOE,OAAO,GAAGrD,KAAK,CAACF,MAAM,EAAE;IAC7B,IAAIqD,UAAU,KAAK,CAAC,EAAE;MACpB,OAAOzD,OAAO;IAChB;IAEA,IAAIM,KAAK,CAACqD,OAAO,CAAC,KAAKrE,CAAC,CAAC,GAAG,CAAC,EAAE;MAC7B,IAAIoE,QAAQ,KAAK,IAAI,EAAE;QACrB,OAAO1D,OAAO;MAChB;MACA,EAAE2D,OAAO;MACT,EAAEF,UAAU;MACZC,QAAQ,GAAGD,UAAU;MACrB;IACF;IAEA,IAAIK,KAAK,GAAG,CAAC;IACb,IAAI1D,MAAM,GAAG,CAAC;IAEd,OAAOA,MAAM,GAAG,CAAC,IAAIzB,KAAK,CAACoF,UAAU,CAACzD,KAAK,CAACqD,OAAO,CAAC,CAAC,EAAE;MACrDG,KAAK,GAAGA,KAAK,GAAG,IAAI,GAAGvB,QAAQ,CAAClC,EAAE,CAACC,KAAK,EAAEqD,OAAO,CAAC,EAAE,EAAE,CAAC;MACvD,EAAEA,OAAO;MACT,EAAEvD,MAAM;IACV;IAEA,IAAIE,KAAK,CAACqD,OAAO,CAAC,KAAKrE,CAAC,CAAC,GAAG,CAAC,EAAE;MAC7B,IAAIc,MAAM,KAAK,CAAC,EAAE;QAChB,OAAOJ,OAAO;MAChB;MAEA2D,OAAO,IAAIvD,MAAM;MAEjB,IAAIqD,UAAU,GAAG,CAAC,EAAE;QAClB,OAAOzD,OAAO;MAChB;MAEA,IAAIgE,WAAW,GAAG,CAAC;MAEnB,OAAO1D,KAAK,CAACqD,OAAO,CAAC,KAAKjD,SAAS,EAAE;QACnC,IAAIuD,SAAS,GAAG,IAAI;QAEpB,IAAID,WAAW,GAAG,CAAC,EAAE;UACnB,IAAI1D,KAAK,CAACqD,OAAO,CAAC,KAAKrE,CAAC,CAAC,GAAG,CAAC,IAAI0E,WAAW,GAAG,CAAC,EAAE;YAChD,EAAEL,OAAO;UACX,CAAC,MAAM;YACL,OAAO3D,OAAO;UAChB;QACF;QAEA,IAAI,CAACrB,KAAK,CAACuF,YAAY,CAAC5D,KAAK,CAACqD,OAAO,CAAC,CAAC,EAAE;UACvC,OAAO3D,OAAO;QAChB;QAEA,OAAOrB,KAAK,CAACuF,YAAY,CAAC5D,KAAK,CAACqD,OAAO,CAAC,CAAC,EAAE;UACzC,MAAMQ,MAAM,GAAG5B,QAAQ,CAAClC,EAAE,CAACC,KAAK,EAAEqD,OAAO,CAAC,CAAC;UAC3C,IAAIM,SAAS,KAAK,IAAI,EAAE;YACtBA,SAAS,GAAGE,MAAM;UACpB,CAAC,MAAM,IAAIF,SAAS,KAAK,CAAC,EAAE;YAC1B,OAAOjE,OAAO;UAChB,CAAC,MAAM;YACLiE,SAAS,GAAGA,SAAS,GAAG,EAAE,GAAGE,MAAM;UACrC;UACA,IAAIF,SAAS,GAAG,GAAG,EAAE;YACnB,OAAOjE,OAAO;UAChB;UACA,EAAE2D,OAAO;QACX;QAEAP,OAAO,CAACK,UAAU,CAAC,GAAGL,OAAO,CAACK,UAAU,CAAC,GAAG,KAAK,GAAGQ,SAAS;QAE7D,EAAED,WAAW;QAEb,IAAIA,WAAW,KAAK,CAAC,IAAIA,WAAW,KAAK,CAAC,EAAE;UAC1C,EAAEP,UAAU;QACd;MACF;MAEA,IAAIO,WAAW,KAAK,CAAC,EAAE;QACrB,OAAOhE,OAAO;MAChB;MAEA;IACF,CAAC,MAAM,IAAIM,KAAK,CAACqD,OAAO,CAAC,KAAKrE,CAAC,CAAC,GAAG,CAAC,EAAE;MACpC,EAAEqE,OAAO;MACT,IAAIrD,KAAK,CAACqD,OAAO,CAAC,KAAKjD,SAAS,EAAE;QAChC,OAAOV,OAAO;MAChB;IACF,CAAC,MAAM,IAAIM,KAAK,CAACqD,OAAO,CAAC,KAAKjD,SAAS,EAAE;MACvC,OAAOV,OAAO;IAChB;IAEAoD,OAAO,CAACK,UAAU,CAAC,GAAGK,KAAK;IAC3B,EAAEL,UAAU;EACd;EAEA,IAAIC,QAAQ,KAAK,IAAI,EAAE;IACrB,IAAIU,KAAK,GAAGX,UAAU,GAAGC,QAAQ;IACjCD,UAAU,GAAG,CAAC;IACd,OAAOA,UAAU,KAAK,CAAC,IAAIW,KAAK,GAAG,CAAC,EAAE;MACpC,MAAMC,IAAI,GAAGjB,OAAO,CAACM,QAAQ,GAAGU,KAAK,GAAG,CAAC,CAAC;MAC1ChB,OAAO,CAACM,QAAQ,GAAGU,KAAK,GAAG,CAAC,CAAC,GAAGhB,OAAO,CAACK,UAAU,CAAC;MACnDL,OAAO,CAACK,UAAU,CAAC,GAAGY,IAAI;MAC1B,EAAEZ,UAAU;MACZ,EAAEW,KAAK;IACT;EACF,CAAC,MAAM,IAAIV,QAAQ,KAAK,IAAI,IAAID,UAAU,KAAK,CAAC,EAAE;IAChD,OAAOzD,OAAO;EAChB;EAEA,OAAOoD,OAAO;AAChB;AAEA,SAASkB,aAAaA,CAAClB,OAAO,EAAE;EAC9B,IAAIC,MAAM,GAAG,EAAE;EACf,MAAMK,QAAQ,GAAGa,sCAAsC,CAACnB,OAAO,CAAC;EAChE,IAAIoB,OAAO,GAAG,KAAK;EAEnB,KAAK,IAAIf,UAAU,GAAG,CAAC,EAAEA,UAAU,IAAI,CAAC,EAAE,EAAEA,UAAU,EAAE;IACtD,IAAIe,OAAO,IAAIpB,OAAO,CAACK,UAAU,CAAC,KAAK,CAAC,EAAE;MACxC;IACF,CAAC,MAAM,IAAIe,OAAO,EAAE;MAClBA,OAAO,GAAG,KAAK;IACjB;IAEA,IAAId,QAAQ,KAAKD,UAAU,EAAE;MAC3B,MAAMgB,SAAS,GAAGhB,UAAU,KAAK,CAAC,GAAG,IAAI,GAAG,GAAG;MAC/CJ,MAAM,IAAIoB,SAAS;MACnBD,OAAO,GAAG,IAAI;MACd;IACF;IAEAnB,MAAM,IAAID,OAAO,CAACK,UAAU,CAAC,CAACiB,QAAQ,CAAC,EAAE,CAAC;IAE1C,IAAIjB,UAAU,KAAK,CAAC,EAAE;MACpBJ,MAAM,IAAI,GAAG;IACf;EACF;EAEA,OAAOA,MAAM;AACf;AAEA,SAASsB,SAASA,CAACrE,KAAK,EAAEsE,QAAQ,GAAG,KAAK,EAAE;EAC1C,IAAItE,KAAK,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;IACpB,IAAIA,KAAK,CAACA,KAAK,CAACF,MAAM,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;MACnC,OAAOJ,OAAO;IAChB;IAEA,OAAOwD,SAAS,CAAClD,KAAK,CAAC8B,SAAS,CAAC,CAAC,EAAE9B,KAAK,CAACF,MAAM,GAAG,CAAC,CAAC,CAAC;EACxD;EAEA,IAAIwE,QAAQ,EAAE;IACZ,OAAOC,eAAe,CAACvE,KAAK,CAAC;EAC/B;EAEA,MAAMwE,MAAM,GAAGlG,oBAAoB,CAACC,mBAAmB,CAACyB,KAAK,CAAC,CAAC;EAC/D,MAAMyE,WAAW,GAAGC,aAAa,CAACF,MAAM,CAAC;EACzC,IAAIC,WAAW,KAAK/E,OAAO,EAAE;IAC3B,OAAOA,OAAO;EAChB;EAEA,IAAIiF,aAAa,CAACF,WAAW,CAAC,EAAE;IAC9B,OAAOvC,SAAS,CAACuC,WAAW,CAAC;EAC/B;EAEA,OAAOA,WAAW;AACpB;AAEA,SAASE,aAAaA,CAAC3E,KAAK,EAAE;EAC5B,MAAMmC,KAAK,GAAGnC,KAAK,CAACoC,KAAK,CAAC,GAAG,CAAC;EAC9B,IAAID,KAAK,CAACA,KAAK,CAACrC,MAAM,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE;IAClC,IAAIqC,KAAK,CAACrC,MAAM,KAAK,CAAC,EAAE;MACtB,OAAO,KAAK;IACd;IACAqC,KAAK,CAACE,GAAG,CAAC,CAAC;EACb;EAEA,MAAMuC,IAAI,GAAGzC,KAAK,CAACA,KAAK,CAACrC,MAAM,GAAG,CAAC,CAAC;EACpC,IAAI6B,eAAe,CAACiD,IAAI,CAAC,KAAKlF,OAAO,EAAE;IACrC,OAAO,IAAI;EACb;EAEA,IAAI,WAAW,CAACsC,IAAI,CAAC4C,IAAI,CAAC,EAAE;IAC1B,OAAO,IAAI;EACb;EAEA,OAAO,KAAK;AACd;AAEA,SAASL,eAAeA,CAACvE,KAAK,EAAE;EAC9B,IAAIkB,8BAA8B,CAAClB,KAAK,CAAC,EAAE;IACzC,OAAON,OAAO;EAChB;EAEA,OAAOjB,uBAAuB,CAACuB,KAAK,EAAEtB,wBAAwB,CAAC;AACjE;AAEA,SAASuF,sCAAsCA,CAACnB,OAAO,EAAE;EACvD,IAAI+B,YAAY,GAAG,IAAI;EACvB,IAAIC,WAAW,GAAG,CAAC,CAAC,CAAC;EACrB,IAAIC,UAAU,GAAG,IAAI;EACrB,IAAIC,SAAS,GAAG,CAAC;EAEjB,KAAK,IAAI7B,UAAU,GAAG,CAAC,EAAEA,UAAU,GAAGL,OAAO,CAAChD,MAAM,EAAE,EAAEqD,UAAU,EAAE;IAClE,IAAIL,OAAO,CAACK,UAAU,CAAC,KAAK,CAAC,EAAE;MAC7B,IAAI6B,SAAS,GAAGF,WAAW,EAAE;QAC3BD,YAAY,GAAGE,UAAU;QACzBD,WAAW,GAAGE,SAAS;MACzB;MAEAD,UAAU,GAAG,IAAI;MACjBC,SAAS,GAAG,CAAC;IACf,CAAC,MAAM;MACL,IAAID,UAAU,KAAK,IAAI,EAAE;QACvBA,UAAU,GAAG5B,UAAU;MACzB;MACA,EAAE6B,SAAS;IACb;EACF;EAEA,IAAIA,SAAS,GAAGF,WAAW,EAAE;IAC3B,OAAOC,UAAU;EACnB;EAEA,OAAOF,YAAY;AACrB;AAEA,SAASI,aAAaA,CAACC,IAAI,EAAE;EAC3B,IAAI,OAAOA,IAAI,KAAK,QAAQ,EAAE;IAC5B,OAAOrC,aAAa,CAACqC,IAAI,CAAC;EAC5B;;EAEA;EACA,IAAIA,IAAI,YAAY5B,KAAK,EAAE;IACzB,OAAO,IAAIU,aAAa,CAACkB,IAAI,CAAC,GAAG;EACnC;EAEA,OAAOA,IAAI;AACb;AAEA,SAASR,aAAaA,CAACF,MAAM,EAAEW,QAAQ,GAAG,KAAK,EAAE;EAC/C,MAAMC,MAAM,GAAGjH,IAAI,CAACkH,OAAO,CAACb,MAAM,EAAE;IAClCc,YAAY,EAAEH,QAAQ;IACtBI,SAAS,EAAE,IAAI;IACfC,YAAY,EAAE,IAAI;IAClBC,iBAAiB,EAAEN,QAAQ;IAC3BO,sBAAsB,EAAE,KAAK;IAC7BC,eAAe,EAAER,QAAQ;IACzBS,qBAAqB,EAAE;EACzB,CAAC,CAAC;EACF,IAAIR,MAAM,KAAK,IAAI,EAAE;IACnB,OAAO1F,OAAO;EAChB;EAEA,IAAI,CAACyF,QAAQ,EAAE;IACb,IAAIC,MAAM,KAAK,EAAE,EAAE;MACjB,OAAO1F,OAAO;IAChB;IACA,IAAI0B,gCAAgC,CAACgE,MAAM,CAAC,EAAE;MAC5C,OAAO1F,OAAO;IAChB;EACF;EACA,OAAO0F,MAAM;AACf;AAEA,SAASS,gBAAgBA,CAAC7E,MAAM,EAAE;EAChC;;EAEA,IAAI8E,KAAK,GAAG,CAAC;EACb,IAAIC,GAAG,GAAG/E,MAAM,CAAClB,MAAM;EACvB,OAAOgG,KAAK,GAAGC,GAAG,EAAE,EAAED,KAAK,EAAE;IAC3B,IAAI9E,MAAM,CAACgF,UAAU,CAACF,KAAK,CAAC,GAAG,IAAI,EAAE;MACnC;IACF;EACF;EACA,OAAOC,GAAG,GAAGD,KAAK,EAAE,EAAEC,GAAG,EAAE;IACzB,IAAI/E,MAAM,CAACgF,UAAU,CAACD,GAAG,GAAG,CAAC,CAAC,GAAG,IAAI,EAAE;MACrC;IACF;EACF;EACA,OAAO/E,MAAM,CAACc,SAAS,CAACgE,KAAK,EAAEC,GAAG,CAAC;AACrC;AAEA,SAASE,iBAAiBA,CAACzE,GAAG,EAAE;EAC9B,OAAOA,GAAG,CAAC0E,OAAO,CAAC,wBAAwB,EAAE,EAAE,CAAC;AAClD;AAEA,SAASC,WAAWA,CAAC3E,GAAG,EAAE;EACxB,MAAM;IAAE4E;EAAK,CAAC,GAAG5E,GAAG;EACpB,IAAI4E,IAAI,CAACtG,MAAM,KAAK,CAAC,EAAE;IACrB;EACF;EACA,IAAI0B,GAAG,CAACF,MAAM,KAAK,MAAM,IAAI8E,IAAI,CAACtG,MAAM,KAAK,CAAC,IAAIuG,8BAA8B,CAACD,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;IACzF;EACF;EAEAA,IAAI,CAAC/D,GAAG,CAAC,CAAC;AACZ;AAEA,SAASiE,mBAAmBA,CAAC9E,GAAG,EAAE;EAChC,OAAOA,GAAG,CAAC+E,QAAQ,KAAK,EAAE,IAAI/E,GAAG,CAACgF,QAAQ,KAAK,EAAE;AACnD;AAEA,SAASC,+BAA+BA,CAACjF,GAAG,EAAE;EAC5C,OAAOA,GAAG,CAAC0D,IAAI,KAAK,IAAI,IAAI1D,GAAG,CAAC0D,IAAI,KAAK,EAAE,IAAI1D,GAAG,CAACF,MAAM,KAAK,MAAM;AACtE;AAEA,SAASoF,eAAeA,CAAClF,GAAG,EAAE;EAC5B,OAAO,OAAOA,GAAG,CAAC4E,IAAI,KAAK,QAAQ;AACrC;AAEA,SAASC,8BAA8BA,CAACrF,MAAM,EAAE;EAC9C,OAAO,cAAc,CAACgB,IAAI,CAAChB,MAAM,CAAC;AACpC;AAEA,SAAS2F,eAAeA,CAAC3G,KAAK,EAAE4G,IAAI,EAAEC,gBAAgB,EAAErF,GAAG,EAAEsF,aAAa,EAAE;EAC1E,IAAI,CAACzD,OAAO,GAAG,CAAC;EAChB,IAAI,CAACrD,KAAK,GAAGA,KAAK;EAClB,IAAI,CAAC4G,IAAI,GAAGA,IAAI,IAAI,IAAI;EACxB,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB,IAAI,OAAO;EACnD,IAAI,CAACC,aAAa,GAAGA,aAAa;EAClC,IAAI,CAACtF,GAAG,GAAGA,GAAG;EACd,IAAI,CAAC9B,OAAO,GAAG,KAAK;EACpB,IAAI,CAACqH,UAAU,GAAG,KAAK;EAEvB,IAAI,CAAC,IAAI,CAACvF,GAAG,EAAE;IACb,IAAI,CAACA,GAAG,GAAG;MACTF,MAAM,EAAE,EAAE;MACViF,QAAQ,EAAE,EAAE;MACZC,QAAQ,EAAE,EAAE;MACZtB,IAAI,EAAE,IAAI;MACV8B,IAAI,EAAE,IAAI;MACVZ,IAAI,EAAE,EAAE;MACRa,KAAK,EAAE,IAAI;MACXC,QAAQ,EAAE;IACZ,CAAC;IAED,MAAMC,GAAG,GAAGtB,gBAAgB,CAAC,IAAI,CAAC7F,KAAK,CAAC;IACxC,IAAImH,GAAG,KAAK,IAAI,CAACnH,KAAK,EAAE;MACtB,IAAI,CAAC+G,UAAU,GAAG,IAAI;IACxB;IACA,IAAI,CAAC/G,KAAK,GAAGmH,GAAG;EAClB;EAEA,MAAMA,GAAG,GAAGlB,iBAAiB,CAAC,IAAI,CAACjG,KAAK,CAAC;EACzC,IAAImH,GAAG,KAAK,IAAI,CAACnH,KAAK,EAAE;IACtB,IAAI,CAAC+G,UAAU,GAAG,IAAI;EACxB;EACA,IAAI,CAAC/G,KAAK,GAAGmH,GAAG;EAEhB,IAAI,CAACC,KAAK,GAAGN,aAAa,IAAI,cAAc;EAE5C,IAAI,CAACtG,MAAM,GAAG,EAAE;EAChB,IAAI,CAAC6G,MAAM,GAAG,KAAK;EACnB,IAAI,CAACC,OAAO,GAAG,KAAK;EACpB,IAAI,CAACC,qBAAqB,GAAG,KAAK;EAElC,IAAI,CAACvH,KAAK,GAAGsD,KAAK,CAACC,IAAI,CAAC,IAAI,CAACvD,KAAK,EAAEE,CAAC,IAAIA,CAAC,CAAChB,WAAW,CAAC,CAAC,CAAC,CAAC;EAE1D,OAAO,IAAI,CAACmE,OAAO,IAAI,IAAI,CAACrD,KAAK,CAACF,MAAM,EAAE,EAAE,IAAI,CAACuD,OAAO,EAAE;IACxD,MAAMnD,CAAC,GAAG,IAAI,CAACF,KAAK,CAAC,IAAI,CAACqD,OAAO,CAAC;IAClC,MAAMmE,IAAI,GAAGrH,KAAK,CAACD,CAAC,CAAC,GAAGE,SAAS,GAAGC,MAAM,CAACC,aAAa,CAACJ,CAAC,CAAC;;IAE3D;IACA,MAAMuH,GAAG,GAAG,IAAI,CAAC,SAAS,IAAI,CAACL,KAAK,EAAE,CAAC,CAAClH,CAAC,EAAEsH,IAAI,CAAC;IAChD,IAAI,CAACC,GAAG,EAAE;MACR,MAAM,CAAC;IACT,CAAC,MAAM,IAAIA,GAAG,KAAK/H,OAAO,EAAE;MAC1B,IAAI,CAACA,OAAO,GAAG,IAAI;MACnB;IACF;EACF;AACF;AAEAiH,eAAe,CAACe,SAAS,CAAC,oBAAoB,CAAC,GAAG,SAASC,gBAAgBA,CAACzH,CAAC,EAAEsH,IAAI,EAAE;EACnF,IAAInJ,KAAK,CAACyC,YAAY,CAACZ,CAAC,CAAC,EAAE;IACzB,IAAI,CAACM,MAAM,IAAIgH,IAAI,CAAC/G,WAAW,CAAC,CAAC;IACjC,IAAI,CAAC2G,KAAK,GAAG,QAAQ;EACvB,CAAC,MAAM,IAAI,CAAC,IAAI,CAACN,aAAa,EAAE;IAC9B,IAAI,CAACM,KAAK,GAAG,WAAW;IACxB,EAAE,IAAI,CAAC/D,OAAO;EAChB,CAAC,MAAM;IACL,IAAI,CAAC0D,UAAU,GAAG,IAAI;IACtB,OAAOrH,OAAO;EAChB;EAEA,OAAO,IAAI;AACb,CAAC;AAEDiH,eAAe,CAACe,SAAS,CAAC,cAAc,CAAC,GAAG,SAASE,WAAWA,CAAC1H,CAAC,EAAEsH,IAAI,EAAE;EACxE,IAAInJ,KAAK,CAACwJ,mBAAmB,CAAC3H,CAAC,CAAC,IAAIA,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;IAChF,IAAI,CAACwB,MAAM,IAAIgH,IAAI,CAAC/G,WAAW,CAAC,CAAC;EACnC,CAAC,MAAM,IAAIP,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;IACvB,IAAI,IAAI,CAAC8H,aAAa,EAAE;MACtB,IAAIvF,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACH,eAAe,CAAC,IAAI,CAACb,MAAM,CAAC,EAAE;QACxD,OAAO,KAAK;MACd;MAEA,IAAI,CAACe,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,IAAIH,eAAe,CAAC,IAAI,CAACb,MAAM,CAAC,EAAE;QACxD,OAAO,KAAK;MACd;MAEA,IAAI,CAAC8F,mBAAmB,CAAC,IAAI,CAAC9E,GAAG,CAAC,IAAI,IAAI,CAACA,GAAG,CAACwF,IAAI,KAAK,IAAI,KAAK,IAAI,CAACxG,MAAM,KAAK,MAAM,EAAE;QACvF,OAAO,KAAK;MACd;MAEA,IAAI,IAAI,CAACgB,GAAG,CAACF,MAAM,KAAK,MAAM,IAAI,IAAI,CAACE,GAAG,CAAC0D,IAAI,KAAK,EAAE,EAAE;QACtD,OAAO,KAAK;MACd;IACF;IACA,IAAI,CAAC1D,GAAG,CAACF,MAAM,GAAG,IAAI,CAACd,MAAM;IAC7B,IAAI,IAAI,CAACsG,aAAa,EAAE;MACtB,IAAI,IAAI,CAACtF,GAAG,CAACwF,IAAI,KAAKtF,WAAW,CAAC,IAAI,CAACF,GAAG,CAACF,MAAM,CAAC,EAAE;QAClD,IAAI,CAACE,GAAG,CAACwF,IAAI,GAAG,IAAI;MACtB;MACA,OAAO,KAAK;IACd;IACA,IAAI,CAACxG,MAAM,GAAG,EAAE;IAChB,IAAI,IAAI,CAACgB,GAAG,CAACF,MAAM,KAAK,MAAM,EAAE;MAC9B,IAAI,IAAI,CAACtB,KAAK,CAAC,IAAI,CAACqD,OAAO,GAAG,CAAC,CAAC,KAAKrE,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAACgB,KAAK,CAAC,IAAI,CAACqD,OAAO,GAAG,CAAC,CAAC,KAAKrE,CAAC,CAAC,GAAG,CAAC,EAAE;QACtF,IAAI,CAAC+H,UAAU,GAAG,IAAI;MACxB;MACA,IAAI,CAACK,KAAK,GAAG,MAAM;IACrB,CAAC,MAAM,IAAI7F,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,IAAI,IAAI,CAACoF,IAAI,KAAK,IAAI,IAAI,IAAI,CAACA,IAAI,CAACtF,MAAM,KAAK,IAAI,CAACE,GAAG,CAACF,MAAM,EAAE;MAC5F,IAAI,CAAC8F,KAAK,GAAG,+BAA+B;IAC9C,CAAC,MAAM,IAAI7F,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,EAAE;MAC9B,IAAI,CAAC4F,KAAK,GAAG,2BAA2B;IAC1C,CAAC,MAAM,IAAI,IAAI,CAACpH,KAAK,CAAC,IAAI,CAACqD,OAAO,GAAG,CAAC,CAAC,KAAKrE,CAAC,CAAC,GAAG,CAAC,EAAE;MAClD,IAAI,CAACoI,KAAK,GAAG,mBAAmB;MAChC,EAAE,IAAI,CAAC/D,OAAO;IAChB,CAAC,MAAM;MACL,IAAI,CAAC7B,GAAG,CAAC4E,IAAI,GAAG,EAAE;MAClB,IAAI,CAACgB,KAAK,GAAG,aAAa;IAC5B;EACF,CAAC,MAAM,IAAI,CAAC,IAAI,CAACN,aAAa,EAAE;IAC9B,IAAI,CAACtG,MAAM,GAAG,EAAE;IAChB,IAAI,CAAC4G,KAAK,GAAG,WAAW;IACxB,IAAI,CAAC/D,OAAO,GAAG,CAAC,CAAC;EACnB,CAAC,MAAM;IACL,IAAI,CAAC0D,UAAU,GAAG,IAAI;IACtB,OAAOrH,OAAO;EAChB;EAEA,OAAO,IAAI;AACb,CAAC;AAEDiH,eAAe,CAACe,SAAS,CAAC,iBAAiB,CAAC,GAAG,SAASI,aAAaA,CAAC5H,CAAC,EAAE;EACvE,IAAI,IAAI,CAAC0G,IAAI,KAAK,IAAI,IAAKF,eAAe,CAAC,IAAI,CAACE,IAAI,CAAC,IAAI1G,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAE,EAAE;IACtE,OAAOU,OAAO;EAChB,CAAC,MAAM,IAAIgH,eAAe,CAAC,IAAI,CAACE,IAAI,CAAC,IAAI1G,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;IACrD,IAAI,CAACwC,GAAG,CAACF,MAAM,GAAG,IAAI,CAACsF,IAAI,CAACtF,MAAM;IAClC,IAAI,CAACE,GAAG,CAAC4E,IAAI,GAAG,IAAI,CAACQ,IAAI,CAACR,IAAI;IAC9B,IAAI,CAAC5E,GAAG,CAACyF,KAAK,GAAG,IAAI,CAACL,IAAI,CAACK,KAAK;IAChC,IAAI,CAACzF,GAAG,CAAC0F,QAAQ,GAAG,EAAE;IACtB,IAAI,CAACE,KAAK,GAAG,UAAU;EACzB,CAAC,MAAM,IAAI,IAAI,CAACR,IAAI,CAACtF,MAAM,KAAK,MAAM,EAAE;IACtC,IAAI,CAAC8F,KAAK,GAAG,MAAM;IACnB,EAAE,IAAI,CAAC/D,OAAO;EAChB,CAAC,MAAM;IACL,IAAI,CAAC+D,KAAK,GAAG,UAAU;IACvB,EAAE,IAAI,CAAC/D,OAAO;EAChB;EAEA,OAAO,IAAI;AACb,CAAC;AAEDsD,eAAe,CAACe,SAAS,CAAC,qCAAqC,CAAC,GAAG,SAASK,+BAA+BA,CAAC7H,CAAC,EAAE;EAC7G,IAAIA,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAACgB,KAAK,CAAC,IAAI,CAACqD,OAAO,GAAG,CAAC,CAAC,KAAKrE,CAAC,CAAC,GAAG,CAAC,EAAE;IAC3D,IAAI,CAACoI,KAAK,GAAG,kCAAkC;IAC/C,EAAE,IAAI,CAAC/D,OAAO;EAChB,CAAC,MAAM;IACL,IAAI,CAAC0D,UAAU,GAAG,IAAI;IACtB,IAAI,CAACK,KAAK,GAAG,UAAU;IACvB,EAAE,IAAI,CAAC/D,OAAO;EAChB;EAEA,OAAO,IAAI;AACb,CAAC;AAEDsD,eAAe,CAACe,SAAS,CAAC,yBAAyB,CAAC,GAAG,SAASM,oBAAoBA,CAAC9H,CAAC,EAAE;EACtF,IAAIA,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;IAChB,IAAI,CAACoI,KAAK,GAAG,WAAW;EAC1B,CAAC,MAAM;IACL,IAAI,CAACA,KAAK,GAAG,MAAM;IACnB,EAAE,IAAI,CAAC/D,OAAO;EAChB;EAEA,OAAO,IAAI;AACb,CAAC;AAEDsD,eAAe,CAACe,SAAS,CAAC,gBAAgB,CAAC,GAAG,SAASO,aAAaA,CAAC/H,CAAC,EAAE;EACtE,IAAI,CAACsB,GAAG,CAACF,MAAM,GAAG,IAAI,CAACsF,IAAI,CAACtF,MAAM;EAClC,IAAIpB,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;IAChB,IAAI,CAACoI,KAAK,GAAG,gBAAgB;EAC/B,CAAC,MAAM,IAAI7F,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,IAAItB,CAAC,KAAKlB,CAAC,CAAC,IAAI,CAAC,EAAE;IAC/C,IAAI,CAAC+H,UAAU,GAAG,IAAI;IACtB,IAAI,CAACK,KAAK,GAAG,gBAAgB;EAC/B,CAAC,MAAM;IACL,IAAI,CAAC5F,GAAG,CAAC+E,QAAQ,GAAG,IAAI,CAACK,IAAI,CAACL,QAAQ;IACtC,IAAI,CAAC/E,GAAG,CAACgF,QAAQ,GAAG,IAAI,CAACI,IAAI,CAACJ,QAAQ;IACtC,IAAI,CAAChF,GAAG,CAAC0D,IAAI,GAAG,IAAI,CAAC0B,IAAI,CAAC1B,IAAI;IAC9B,IAAI,CAAC1D,GAAG,CAACwF,IAAI,GAAG,IAAI,CAACJ,IAAI,CAACI,IAAI;IAC9B,IAAI,CAACxF,GAAG,CAAC4E,IAAI,GAAG,IAAI,CAACQ,IAAI,CAACR,IAAI,CAAC8B,KAAK,CAAC,CAAC;IACtC,IAAI,CAAC1G,GAAG,CAACyF,KAAK,GAAG,IAAI,CAACL,IAAI,CAACK,KAAK;IAChC,IAAI/G,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;MAChB,IAAI,CAACwC,GAAG,CAACyF,KAAK,GAAG,EAAE;MACnB,IAAI,CAACG,KAAK,GAAG,OAAO;IACtB,CAAC,MAAM,IAAIlH,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;MACvB,IAAI,CAACwC,GAAG,CAAC0F,QAAQ,GAAG,EAAE;MACtB,IAAI,CAACE,KAAK,GAAG,UAAU;IACzB,CAAC,MAAM,IAAI,CAACjH,KAAK,CAACD,CAAC,CAAC,EAAE;MACpB,IAAI,CAACsB,GAAG,CAACyF,KAAK,GAAG,IAAI;MACrB,IAAI,CAACzF,GAAG,CAAC4E,IAAI,CAAC/D,GAAG,CAAC,CAAC;MACnB,IAAI,CAAC+E,KAAK,GAAG,MAAM;MACnB,EAAE,IAAI,CAAC/D,OAAO;IAChB;EACF;EAEA,OAAO,IAAI;AACb,CAAC;AAEDsD,eAAe,CAACe,SAAS,CAAC,sBAAsB,CAAC,GAAG,SAASS,kBAAkBA,CAACjI,CAAC,EAAE;EACjF,IAAIqB,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,KAAKtB,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;IAC1D,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,IAAI,CAAC,EAAE;MACjB,IAAI,CAAC+H,UAAU,GAAG,IAAI;IACxB;IACA,IAAI,CAACK,KAAK,GAAG,kCAAkC;EACjD,CAAC,MAAM,IAAIlH,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;IACvB,IAAI,CAACoI,KAAK,GAAG,WAAW;EAC1B,CAAC,MAAM;IACL,IAAI,CAAC5F,GAAG,CAAC+E,QAAQ,GAAG,IAAI,CAACK,IAAI,CAACL,QAAQ;IACtC,IAAI,CAAC/E,GAAG,CAACgF,QAAQ,GAAG,IAAI,CAACI,IAAI,CAACJ,QAAQ;IACtC,IAAI,CAAChF,GAAG,CAAC0D,IAAI,GAAG,IAAI,CAAC0B,IAAI,CAAC1B,IAAI;IAC9B,IAAI,CAAC1D,GAAG,CAACwF,IAAI,GAAG,IAAI,CAACJ,IAAI,CAACI,IAAI;IAC9B,IAAI,CAACI,KAAK,GAAG,MAAM;IACnB,EAAE,IAAI,CAAC/D,OAAO;EAChB;EAEA,OAAO,IAAI;AACb,CAAC;AAEDsD,eAAe,CAACe,SAAS,CAAC,iCAAiC,CAAC,GAAG,SAASU,4BAA4BA,CAAClI,CAAC,EAAE;EACtG,IAAIA,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAI,IAAI,CAACgB,KAAK,CAAC,IAAI,CAACqD,OAAO,GAAG,CAAC,CAAC,KAAKrE,CAAC,CAAC,GAAG,CAAC,EAAE;IAC3D,IAAI,CAACoI,KAAK,GAAG,kCAAkC;IAC/C,EAAE,IAAI,CAAC/D,OAAO;EAChB,CAAC,MAAM;IACL,IAAI,CAAC0D,UAAU,GAAG,IAAI;IACtB,IAAI,CAACK,KAAK,GAAG,kCAAkC;IAC/C,EAAE,IAAI,CAAC/D,OAAO;EAChB;EAEA,OAAO,IAAI;AACb,CAAC;AAEDsD,eAAe,CAACe,SAAS,CAAC,wCAAwC,CAAC,GAAG,SAASW,kCAAkCA,CAACnI,CAAC,EAAE;EACnH,IAAIA,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,IAAI,CAAC,EAAE;IACjC,IAAI,CAACoI,KAAK,GAAG,WAAW;IACxB,EAAE,IAAI,CAAC/D,OAAO;EAChB,CAAC,MAAM;IACL,IAAI,CAAC0D,UAAU,GAAG,IAAI;EACxB;EAEA,OAAO,IAAI;AACb,CAAC;AAEDJ,eAAe,CAACe,SAAS,CAAC,iBAAiB,CAAC,GAAG,SAASY,cAAcA,CAACpI,CAAC,EAAEsH,IAAI,EAAE;EAC9E,IAAItH,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;IAChB,IAAI,CAAC+H,UAAU,GAAG,IAAI;IACtB,IAAI,IAAI,CAACM,MAAM,EAAE;MACf,IAAI,CAAC7G,MAAM,GAAG,MAAM,IAAI,CAACA,MAAM,EAAE;IACnC;IACA,IAAI,CAAC6G,MAAM,GAAG,IAAI;;IAElB;IACA,MAAMkB,GAAG,GAAG3I,YAAY,CAAC,IAAI,CAACY,MAAM,CAAC;IACrC,KAAK,IAAI6C,OAAO,GAAG,CAAC,EAAEA,OAAO,GAAGkF,GAAG,EAAE,EAAElF,OAAO,EAAE;MAC9C,MAAMmF,SAAS,GAAG,IAAI,CAAChI,MAAM,CAACtB,WAAW,CAACmE,OAAO,CAAC;MAElD,IAAImF,SAAS,KAAKxJ,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAACuI,qBAAqB,EAAE;QACvD,IAAI,CAACA,qBAAqB,GAAG,IAAI;QACjC;MACF;MACA,MAAMkB,iBAAiB,GAAGjK,0BAA0B,CAACgK,SAAS,EAAEzJ,uBAAuB,CAAC;MACxF,IAAI,IAAI,CAACwI,qBAAqB,EAAE;QAC9B,IAAI,CAAC/F,GAAG,CAACgF,QAAQ,IAAIiC,iBAAiB;MACxC,CAAC,MAAM;QACL,IAAI,CAACjH,GAAG,CAAC+E,QAAQ,IAAIkC,iBAAiB;MACxC;IACF;IACA,IAAI,CAACjI,MAAM,GAAG,EAAE;EAClB,CAAC,MAAM,IAAIL,KAAK,CAACD,CAAC,CAAC,IAAIA,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IACvDuC,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,IAAItB,CAAC,KAAKlB,CAAC,CAAC,IAAI,CAAE,EAAE;IACjD,IAAI,IAAI,CAACqI,MAAM,IAAI,IAAI,CAAC7G,MAAM,KAAK,EAAE,EAAE;MACrC,IAAI,CAACuG,UAAU,GAAG,IAAI;MACtB,OAAOrH,OAAO;IAChB;IACA,IAAI,CAAC2D,OAAO,IAAIzD,YAAY,CAAC,IAAI,CAACY,MAAM,CAAC,GAAG,CAAC;IAC7C,IAAI,CAACA,MAAM,GAAG,EAAE;IAChB,IAAI,CAAC4G,KAAK,GAAG,MAAM;EACrB,CAAC,MAAM;IACL,IAAI,CAAC5G,MAAM,IAAIgH,IAAI;EACrB;EAEA,OAAO,IAAI;AACb,CAAC;AAEDb,eAAe,CAACe,SAAS,CAAC,gBAAgB,CAAC,GAC3Cf,eAAe,CAACe,SAAS,CAAC,YAAY,CAAC,GAAG,SAASgB,aAAaA,CAACxI,CAAC,EAAEsH,IAAI,EAAE;EACxE,IAAI,IAAI,CAACV,aAAa,IAAI,IAAI,CAACtF,GAAG,CAACF,MAAM,KAAK,MAAM,EAAE;IACpD,EAAE,IAAI,CAAC+B,OAAO;IACd,IAAI,CAAC+D,KAAK,GAAG,WAAW;EAC1B,CAAC,MAAM,IAAIlH,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,IAAI,CAACsI,OAAO,EAAE;IACxC,IAAI,IAAI,CAAC9G,MAAM,KAAK,EAAE,EAAE;MACtB,IAAI,CAACuG,UAAU,GAAG,IAAI;MACtB,OAAOrH,OAAO;IAChB;IAEA,IAAI,IAAI,CAACoH,aAAa,KAAK,UAAU,EAAE;MACrC,OAAO,KAAK;IACd;IAEA,MAAM5B,IAAI,GAAGb,SAAS,CAAC,IAAI,CAAC7D,MAAM,EAAEiB,YAAY,CAAC,IAAI,CAACD,GAAG,CAAC,CAAC;IAC3D,IAAI0D,IAAI,KAAKxF,OAAO,EAAE;MACpB,OAAOA,OAAO;IAChB;IAEA,IAAI,CAAC8B,GAAG,CAAC0D,IAAI,GAAGA,IAAI;IACpB,IAAI,CAAC1E,MAAM,GAAG,EAAE;IAChB,IAAI,CAAC4G,KAAK,GAAG,MAAM;EACrB,CAAC,MAAM,IAAIjH,KAAK,CAACD,CAAC,CAAC,IAAIA,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IACvDuC,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,IAAItB,CAAC,KAAKlB,CAAC,CAAC,IAAI,CAAE,EAAE;IACjD,EAAE,IAAI,CAACqE,OAAO;IACd,IAAI9B,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,IAAI,IAAI,CAAChB,MAAM,KAAK,EAAE,EAAE;MAC7C,IAAI,CAACuG,UAAU,GAAG,IAAI;MACtB,OAAOrH,OAAO;IAChB,CAAC,MAAM,IAAI,IAAI,CAACoH,aAAa,IAAI,IAAI,CAACtG,MAAM,KAAK,EAAE,KACvC8F,mBAAmB,CAAC,IAAI,CAAC9E,GAAG,CAAC,IAAI,IAAI,CAACA,GAAG,CAACwF,IAAI,KAAK,IAAI,CAAC,EAAE;MACpE,IAAI,CAACD,UAAU,GAAG,IAAI;MACtB,OAAO,KAAK;IACd;IAEA,MAAM7B,IAAI,GAAGb,SAAS,CAAC,IAAI,CAAC7D,MAAM,EAAEiB,YAAY,CAAC,IAAI,CAACD,GAAG,CAAC,CAAC;IAC3D,IAAI0D,IAAI,KAAKxF,OAAO,EAAE;MACpB,OAAOA,OAAO;IAChB;IAEA,IAAI,CAAC8B,GAAG,CAAC0D,IAAI,GAAGA,IAAI;IACpB,IAAI,CAAC1E,MAAM,GAAG,EAAE;IAChB,IAAI,CAAC4G,KAAK,GAAG,YAAY;IACzB,IAAI,IAAI,CAACN,aAAa,EAAE;MACtB,OAAO,KAAK;IACd;EACF,CAAC,MAAM;IACL,IAAI5G,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;MAChB,IAAI,CAACsI,OAAO,GAAG,IAAI;IACrB,CAAC,MAAM,IAAIpH,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;MACvB,IAAI,CAACsI,OAAO,GAAG,KAAK;IACtB;IACA,IAAI,CAAC9G,MAAM,IAAIgH,IAAI;EACrB;EAEA,OAAO,IAAI;AACb,CAAC;AAEDb,eAAe,CAACe,SAAS,CAAC,YAAY,CAAC,GAAG,SAASiB,SAASA,CAACzI,CAAC,EAAEsH,IAAI,EAAE;EACpE,IAAInJ,KAAK,CAACuF,YAAY,CAAC1D,CAAC,CAAC,EAAE;IACzB,IAAI,CAACM,MAAM,IAAIgH,IAAI;EACrB,CAAC,MAAM,IAAIrH,KAAK,CAACD,CAAC,CAAC,IAAIA,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IACvDuC,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,IAAItB,CAAC,KAAKlB,CAAC,CAAC,IAAI,CAAE,IACtC,IAAI,CAAC8H,aAAa,EAAE;IAC7B,IAAI,IAAI,CAACtG,MAAM,KAAK,EAAE,EAAE;MACtB,MAAMwG,IAAI,GAAG/E,QAAQ,CAAC,IAAI,CAACzB,MAAM,CAAC;MAClC,IAAIwG,IAAI,GAAG,CAAC,IAAI,EAAE,GAAG,CAAC,EAAE;QACtB,IAAI,CAACD,UAAU,GAAG,IAAI;QACtB,OAAOrH,OAAO;MAChB;MACA,IAAI,CAAC8B,GAAG,CAACwF,IAAI,GAAGA,IAAI,KAAKtF,WAAW,CAAC,IAAI,CAACF,GAAG,CAACF,MAAM,CAAC,GAAG,IAAI,GAAG0F,IAAI;MACnE,IAAI,CAACxG,MAAM,GAAG,EAAE;IAClB;IACA,IAAI,IAAI,CAACsG,aAAa,EAAE;MACtB,OAAO,KAAK;IACd;IACA,IAAI,CAACM,KAAK,GAAG,YAAY;IACzB,EAAE,IAAI,CAAC/D,OAAO;EAChB,CAAC,MAAM;IACL,IAAI,CAAC0D,UAAU,GAAG,IAAI;IACtB,OAAOrH,OAAO;EAChB;EAEA,OAAO,IAAI;AACb,CAAC;AAED,MAAMkJ,uBAAuB,GAAG,IAAIC,GAAG,CAAC,CAAC7J,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,IAAI,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AAE1E,SAAS8J,4BAA4BA,CAAC9I,KAAK,EAAEqD,OAAO,EAAE;EACpD,MAAMvD,MAAM,GAAGE,KAAK,CAACF,MAAM,GAAGuD,OAAO;EACrC,OAAOvD,MAAM,IAAI,CAAC,IAChBa,8BAA8B,CAACX,KAAK,CAACqD,OAAO,CAAC,EAAErD,KAAK,CAACqD,OAAO,GAAG,CAAC,CAAC,CAAC,KACjEvD,MAAM,KAAK,CAAC,IAAI8I,uBAAuB,CAACG,GAAG,CAAC/I,KAAK,CAACqD,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC;AACrE;AAEAsD,eAAe,CAACe,SAAS,CAAC,YAAY,CAAC,GAAG,SAASsB,SAASA,CAAC9I,CAAC,EAAE;EAC9D,IAAI,CAACsB,GAAG,CAACF,MAAM,GAAG,MAAM;EACxB,IAAI,CAACE,GAAG,CAAC0D,IAAI,GAAG,EAAE;EAElB,IAAIhF,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,IAAI,CAAC,EAAE;IACjC,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,IAAI,CAAC,EAAE;MACjB,IAAI,CAAC+H,UAAU,GAAG,IAAI;IACxB;IACA,IAAI,CAACK,KAAK,GAAG,YAAY;EAC3B,CAAC,MAAM,IAAI,IAAI,CAACR,IAAI,KAAK,IAAI,IAAI,IAAI,CAACA,IAAI,CAACtF,MAAM,KAAK,MAAM,EAAE;IAC5D,IAAI,CAACE,GAAG,CAAC0D,IAAI,GAAG,IAAI,CAAC0B,IAAI,CAAC1B,IAAI;IAC9B,IAAI,CAAC1D,GAAG,CAAC4E,IAAI,GAAG,IAAI,CAACQ,IAAI,CAACR,IAAI,CAAC8B,KAAK,CAAC,CAAC;IACtC,IAAI,CAAC1G,GAAG,CAACyF,KAAK,GAAG,IAAI,CAACL,IAAI,CAACK,KAAK;IAChC,IAAI/G,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;MAChB,IAAI,CAACwC,GAAG,CAACyF,KAAK,GAAG,EAAE;MACnB,IAAI,CAACG,KAAK,GAAG,OAAO;IACtB,CAAC,MAAM,IAAIlH,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;MACvB,IAAI,CAACwC,GAAG,CAAC0F,QAAQ,GAAG,EAAE;MACtB,IAAI,CAACE,KAAK,GAAG,UAAU;IACzB,CAAC,MAAM,IAAI,CAACjH,KAAK,CAACD,CAAC,CAAC,EAAE;MACpB,IAAI,CAACsB,GAAG,CAACyF,KAAK,GAAG,IAAI;MACrB,IAAI,CAAC6B,4BAA4B,CAAC,IAAI,CAAC9I,KAAK,EAAE,IAAI,CAACqD,OAAO,CAAC,EAAE;QAC3D8C,WAAW,CAAC,IAAI,CAAC3E,GAAG,CAAC;MACvB,CAAC,MAAM;QACL,IAAI,CAACuF,UAAU,GAAG,IAAI;QACtB,IAAI,CAACvF,GAAG,CAAC4E,IAAI,GAAG,EAAE;MACpB;MAEA,IAAI,CAACgB,KAAK,GAAG,MAAM;MACnB,EAAE,IAAI,CAAC/D,OAAO;IAChB;EACF,CAAC,MAAM;IACL,IAAI,CAAC+D,KAAK,GAAG,MAAM;IACnB,EAAE,IAAI,CAAC/D,OAAO;EAChB;EAEA,OAAO,IAAI;AACb,CAAC;AAEDsD,eAAe,CAACe,SAAS,CAAC,kBAAkB,CAAC,GAAG,SAASuB,cAAcA,CAAC/I,CAAC,EAAE;EACzE,IAAIA,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,IAAI,CAAC,EAAE;IACjC,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,IAAI,CAAC,EAAE;MACjB,IAAI,CAAC+H,UAAU,GAAG,IAAI;IACxB;IACA,IAAI,CAACK,KAAK,GAAG,WAAW;EAC1B,CAAC,MAAM;IACL,IAAI,IAAI,CAACR,IAAI,KAAK,IAAI,IAAI,IAAI,CAACA,IAAI,CAACtF,MAAM,KAAK,MAAM,EAAE;MACrD,IAAI,CAACwH,4BAA4B,CAAC,IAAI,CAAC9I,KAAK,EAAE,IAAI,CAACqD,OAAO,CAAC,IACvDpC,oCAAoC,CAAC,IAAI,CAAC2F,IAAI,CAACR,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;QAC3D,IAAI,CAAC5E,GAAG,CAAC4E,IAAI,CAAC3D,IAAI,CAAC,IAAI,CAACmE,IAAI,CAACR,IAAI,CAAC,CAAC,CAAC,CAAC;MACvC;MACA,IAAI,CAAC5E,GAAG,CAAC0D,IAAI,GAAG,IAAI,CAAC0B,IAAI,CAAC1B,IAAI;IAChC;IACA,IAAI,CAACkC,KAAK,GAAG,MAAM;IACnB,EAAE,IAAI,CAAC/D,OAAO;EAChB;EAEA,OAAO,IAAI;AACb,CAAC;AAEDsD,eAAe,CAACe,SAAS,CAAC,iBAAiB,CAAC,GAAG,SAASwB,aAAaA,CAAChJ,CAAC,EAAEsH,IAAI,EAAE;EAC7E,IAAIrH,KAAK,CAACD,CAAC,CAAC,IAAIA,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,IAAI,CAAC,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;IAC7E,EAAE,IAAI,CAACqE,OAAO;IACd,IAAI,CAAC,IAAI,CAACyD,aAAa,IAAI/F,0BAA0B,CAAC,IAAI,CAACP,MAAM,CAAC,EAAE;MAClE,IAAI,CAACuG,UAAU,GAAG,IAAI;MACtB,IAAI,CAACK,KAAK,GAAG,MAAM;IACrB,CAAC,MAAM,IAAI,IAAI,CAAC5G,MAAM,KAAK,EAAE,EAAE;MAC7B,IAAI,CAACgB,GAAG,CAAC0D,IAAI,GAAG,EAAE;MAClB,IAAI,IAAI,CAAC4B,aAAa,EAAE;QACtB,OAAO,KAAK;MACd;MACA,IAAI,CAACM,KAAK,GAAG,YAAY;IAC3B,CAAC,MAAM;MACL,IAAIlC,IAAI,GAAGb,SAAS,CAAC,IAAI,CAAC7D,MAAM,EAAEiB,YAAY,CAAC,IAAI,CAACD,GAAG,CAAC,CAAC;MACzD,IAAI0D,IAAI,KAAKxF,OAAO,EAAE;QACpB,OAAOA,OAAO;MAChB;MACA,IAAIwF,IAAI,KAAK,WAAW,EAAE;QACxBA,IAAI,GAAG,EAAE;MACX;MACA,IAAI,CAAC1D,GAAG,CAAC0D,IAAI,GAAGA,IAAI;MAEpB,IAAI,IAAI,CAAC4B,aAAa,EAAE;QACtB,OAAO,KAAK;MACd;MAEA,IAAI,CAACtG,MAAM,GAAG,EAAE;MAChB,IAAI,CAAC4G,KAAK,GAAG,YAAY;IAC3B;EACF,CAAC,MAAM;IACL,IAAI,CAAC5G,MAAM,IAAIgH,IAAI;EACrB;EAEA,OAAO,IAAI;AACb,CAAC;AAEDb,eAAe,CAACe,SAAS,CAAC,kBAAkB,CAAC,GAAG,SAASyB,cAAcA,CAACjJ,CAAC,EAAE;EACzE,IAAIqB,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,EAAE;IACvB,IAAItB,CAAC,KAAKlB,CAAC,CAAC,IAAI,CAAC,EAAE;MACjB,IAAI,CAAC+H,UAAU,GAAG,IAAI;IACxB;IACA,IAAI,CAACK,KAAK,GAAG,MAAM;IAEnB,IAAIlH,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,IAAI,CAAC,EAAE;MACjC,EAAE,IAAI,CAACqE,OAAO;IAChB;EACF,CAAC,MAAM,IAAI,CAAC,IAAI,CAACyD,aAAa,IAAI5G,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;IAC9C,IAAI,CAACwC,GAAG,CAACyF,KAAK,GAAG,EAAE;IACnB,IAAI,CAACG,KAAK,GAAG,OAAO;EACtB,CAAC,MAAM,IAAI,CAAC,IAAI,CAACN,aAAa,IAAI5G,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;IAC9C,IAAI,CAACwC,GAAG,CAAC0F,QAAQ,GAAG,EAAE;IACtB,IAAI,CAACE,KAAK,GAAG,UAAU;EACzB,CAAC,MAAM,IAAIlH,CAAC,KAAKE,SAAS,EAAE;IAC1B,IAAI,CAACgH,KAAK,GAAG,MAAM;IACnB,IAAIlH,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;MAChB,EAAE,IAAI,CAACqE,OAAO;IAChB;EACF,CAAC,MAAM,IAAI,IAAI,CAACyD,aAAa,IAAI,IAAI,CAACtF,GAAG,CAAC0D,IAAI,KAAK,IAAI,EAAE;IACvD,IAAI,CAAC1D,GAAG,CAAC4E,IAAI,CAAC3D,IAAI,CAAC,EAAE,CAAC;EACxB;EAEA,OAAO,IAAI;AACb,CAAC;AAEDkE,eAAe,CAACe,SAAS,CAAC,YAAY,CAAC,GAAG,SAAS0B,SAASA,CAAClJ,CAAC,EAAE;EAC9D,IAAIC,KAAK,CAACD,CAAC,CAAC,IAAIA,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAKuC,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,IAAItB,CAAC,KAAKlB,CAAC,CAAC,IAAI,CAAE,IACjE,CAAC,IAAI,CAAC8H,aAAa,KAAK5G,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAIkB,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,CAAE,EAAE;IAC3D,IAAIuC,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,IAAItB,CAAC,KAAKlB,CAAC,CAAC,IAAI,CAAC,EAAE;MACxC,IAAI,CAAC+H,UAAU,GAAG,IAAI;IACxB;IAEA,IAAIrG,WAAW,CAAC,IAAI,CAACF,MAAM,CAAC,EAAE;MAC5B2F,WAAW,CAAC,IAAI,CAAC3E,GAAG,CAAC;MACrB,IAAItB,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAI,EAAEuC,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,IAAItB,CAAC,KAAKlB,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;QAC3D,IAAI,CAACwC,GAAG,CAAC4E,IAAI,CAAC3D,IAAI,CAAC,EAAE,CAAC;MACxB;IACF,CAAC,MAAM,IAAIlC,WAAW,CAAC,IAAI,CAACC,MAAM,CAAC,IAAIN,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IACxC,EAAEuC,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,IAAItB,CAAC,KAAKlB,CAAC,CAAC,IAAI,CAAC,CAAC,EAAE;MAClD,IAAI,CAACwC,GAAG,CAAC4E,IAAI,CAAC3D,IAAI,CAAC,EAAE,CAAC;IACxB,CAAC,MAAM,IAAI,CAAClC,WAAW,CAAC,IAAI,CAACC,MAAM,CAAC,EAAE;MACpC,IAAI,IAAI,CAACgB,GAAG,CAACF,MAAM,KAAK,MAAM,IAAI,IAAI,CAACE,GAAG,CAAC4E,IAAI,CAACtG,MAAM,KAAK,CAAC,IAAIiB,0BAA0B,CAAC,IAAI,CAACP,MAAM,CAAC,EAAE;QACvG,IAAI,CAACA,MAAM,GAAG,GAAG,IAAI,CAACA,MAAM,CAAC,CAAC,CAAC,GAAG;MACpC;MACA,IAAI,CAACgB,GAAG,CAAC4E,IAAI,CAAC3D,IAAI,CAAC,IAAI,CAACjC,MAAM,CAAC;IACjC;IACA,IAAI,CAACA,MAAM,GAAG,EAAE;IAChB,IAAIN,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;MAChB,IAAI,CAACwC,GAAG,CAACyF,KAAK,GAAG,EAAE;MACnB,IAAI,CAACG,KAAK,GAAG,OAAO;IACtB;IACA,IAAIlH,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;MAChB,IAAI,CAACwC,GAAG,CAAC0F,QAAQ,GAAG,EAAE;MACtB,IAAI,CAACE,KAAK,GAAG,UAAU;IACzB;EACF,CAAC,MAAM;IACL;;IAEA,IAAIlH,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,KACb,CAACX,KAAK,CAACoF,UAAU,CAAC,IAAI,CAACzD,KAAK,CAAC,IAAI,CAACqD,OAAO,GAAG,CAAC,CAAC,CAAC,IAC9C,CAAChF,KAAK,CAACoF,UAAU,CAAC,IAAI,CAACzD,KAAK,CAAC,IAAI,CAACqD,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MACpD,IAAI,CAAC0D,UAAU,GAAG,IAAI;IACxB;IAEA,IAAI,CAACvG,MAAM,IAAIhC,0BAA0B,CAAC0B,CAAC,EAAEpB,mBAAmB,CAAC;EACnE;EAEA,OAAO,IAAI;AACb,CAAC;AAED6H,eAAe,CAACe,SAAS,CAAC,mBAAmB,CAAC,GAAG,SAAS2B,eAAeA,CAACnJ,CAAC,EAAE;EAC3E,IAAIA,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;IAChB,IAAI,CAACwC,GAAG,CAACyF,KAAK,GAAG,EAAE;IACnB,IAAI,CAACG,KAAK,GAAG,OAAO;EACtB,CAAC,MAAM,IAAIlH,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;IACvB,IAAI,CAACwC,GAAG,CAAC0F,QAAQ,GAAG,EAAE;IACtB,IAAI,CAACE,KAAK,GAAG,UAAU;EACzB,CAAC,MAAM,IAAIlH,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;IACvB,MAAMsK,SAAS,GAAG,IAAI,CAACtJ,KAAK,CAAC,IAAI,CAACqD,OAAO,GAAG,CAAC,CAAC;IAC9C,IAAIiG,SAAS,KAAKtK,CAAC,CAAC,GAAG,CAAC,IAAIsK,SAAS,KAAKtK,CAAC,CAAC,GAAG,CAAC,EAAE;MAChD,IAAI,CAACwC,GAAG,CAAC4E,IAAI,IAAI,KAAK;IACxB,CAAC,MAAM;MACL,IAAI,CAAC5E,GAAG,CAAC4E,IAAI,IAAI,GAAG;IACtB;EACF,CAAC,MAAM;IACL;IACA,IAAI,CAACjG,KAAK,CAACD,CAAC,CAAC,IAAIA,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;MAC7B,IAAI,CAAC+H,UAAU,GAAG,IAAI;IACxB;IAEA,IAAI7G,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,KACX,CAACX,KAAK,CAACoF,UAAU,CAAC,IAAI,CAACzD,KAAK,CAAC,IAAI,CAACqD,OAAO,GAAG,CAAC,CAAC,CAAC,IAC/C,CAAChF,KAAK,CAACoF,UAAU,CAAC,IAAI,CAACzD,KAAK,CAAC,IAAI,CAACqD,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MACrD,IAAI,CAAC0D,UAAU,GAAG,IAAI;IACxB;IAEA,IAAI,CAAC5G,KAAK,CAACD,CAAC,CAAC,EAAE;MACb,IAAI,CAACsB,GAAG,CAAC4E,IAAI,IAAI5H,0BAA0B,CAAC0B,CAAC,EAAExB,wBAAwB,CAAC;IAC1E;EACF;EAEA,OAAO,IAAI;AACb,CAAC;AAEDiI,eAAe,CAACe,SAAS,CAAC,aAAa,CAAC,GAAG,SAAS6B,UAAUA,CAACrJ,CAAC,EAAEsH,IAAI,EAAE;EACtE,IAAI,CAACjG,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,IAAI,IAAI,CAACA,GAAG,CAACF,MAAM,KAAK,IAAI,IAAI,IAAI,CAACE,GAAG,CAACF,MAAM,KAAK,KAAK,EAAE;IACjF,IAAI,CAACuF,gBAAgB,GAAG,OAAO;EACjC;EAEA,IAAK,CAAC,IAAI,CAACC,aAAa,IAAI5G,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,IAAKmB,KAAK,CAACD,CAAC,CAAC,EAAE;IACrD,MAAMsJ,2BAA2B,GAAGjI,SAAS,CAAC,IAAI,CAACC,GAAG,CAAC,GAAG3C,2BAA2B,GAAGD,oBAAoB;IAC5G,IAAI,CAAC4C,GAAG,CAACyF,KAAK,IAAIxI,uBAAuB,CAAC,IAAI,CAAC+B,MAAM,EAAEgJ,2BAA2B,CAAC;IAEnF,IAAI,CAAChJ,MAAM,GAAG,EAAE;IAEhB,IAAIN,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,EAAE;MAChB,IAAI,CAACwC,GAAG,CAAC0F,QAAQ,GAAG,EAAE;MACtB,IAAI,CAACE,KAAK,GAAG,UAAU;IACzB;EACF,CAAC,MAAM,IAAI,CAACjH,KAAK,CAACD,CAAC,CAAC,EAAE;IACpB;;IAEA,IAAIA,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,KACb,CAACX,KAAK,CAACoF,UAAU,CAAC,IAAI,CAACzD,KAAK,CAAC,IAAI,CAACqD,OAAO,GAAG,CAAC,CAAC,CAAC,IAC9C,CAAChF,KAAK,CAACoF,UAAU,CAAC,IAAI,CAACzD,KAAK,CAAC,IAAI,CAACqD,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MACpD,IAAI,CAAC0D,UAAU,GAAG,IAAI;IACxB;IAEA,IAAI,CAACvG,MAAM,IAAIgH,IAAI;EACrB;EAEA,OAAO,IAAI;AACb,CAAC;AAEDb,eAAe,CAACe,SAAS,CAAC,gBAAgB,CAAC,GAAG,SAAS+B,aAAaA,CAACvJ,CAAC,EAAE;EACtE,IAAI,CAACC,KAAK,CAACD,CAAC,CAAC,EAAE;IACb;IACA,IAAIA,CAAC,KAAKlB,CAAC,CAAC,GAAG,CAAC,KACb,CAACX,KAAK,CAACoF,UAAU,CAAC,IAAI,CAACzD,KAAK,CAAC,IAAI,CAACqD,OAAO,GAAG,CAAC,CAAC,CAAC,IAC9C,CAAChF,KAAK,CAACoF,UAAU,CAAC,IAAI,CAACzD,KAAK,CAAC,IAAI,CAACqD,OAAO,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MACpD,IAAI,CAAC0D,UAAU,GAAG,IAAI;IACxB;IAEA,IAAI,CAACvF,GAAG,CAAC0F,QAAQ,IAAI1I,0BAA0B,CAAC0B,CAAC,EAAEvB,uBAAuB,CAAC;EAC7E;EAEA,OAAO,IAAI;AACb,CAAC;AAED,SAAS+K,YAAYA,CAAClI,GAAG,EAAEmI,eAAe,EAAE;EAC1C,IAAI5G,MAAM,GAAG,GAAGvB,GAAG,CAACF,MAAM,GAAG;EAC7B,IAAIE,GAAG,CAAC0D,IAAI,KAAK,IAAI,EAAE;IACrBnC,MAAM,IAAI,IAAI;IAEd,IAAIvB,GAAG,CAAC+E,QAAQ,KAAK,EAAE,IAAI/E,GAAG,CAACgF,QAAQ,KAAK,EAAE,EAAE;MAC9CzD,MAAM,IAAIvB,GAAG,CAAC+E,QAAQ;MACtB,IAAI/E,GAAG,CAACgF,QAAQ,KAAK,EAAE,EAAE;QACvBzD,MAAM,IAAI,IAAIvB,GAAG,CAACgF,QAAQ,EAAE;MAC9B;MACAzD,MAAM,IAAI,GAAG;IACf;IAEAA,MAAM,IAAIkC,aAAa,CAACzD,GAAG,CAAC0D,IAAI,CAAC;IAEjC,IAAI1D,GAAG,CAACwF,IAAI,KAAK,IAAI,EAAE;MACrBjE,MAAM,IAAI,IAAIvB,GAAG,CAACwF,IAAI,EAAE;IAC1B;EACF;EAEA,IAAIxF,GAAG,CAAC0D,IAAI,KAAK,IAAI,IAAI,CAACwB,eAAe,CAAClF,GAAG,CAAC,IAAIA,GAAG,CAAC4E,IAAI,CAACtG,MAAM,GAAG,CAAC,IAAI0B,GAAG,CAAC4E,IAAI,CAAC,CAAC,CAAC,KAAK,EAAE,EAAE;IAC3FrD,MAAM,IAAI,IAAI;EAChB;EACAA,MAAM,IAAI6G,aAAa,CAACpI,GAAG,CAAC;EAE5B,IAAIA,GAAG,CAACyF,KAAK,KAAK,IAAI,EAAE;IACtBlE,MAAM,IAAI,IAAIvB,GAAG,CAACyF,KAAK,EAAE;EAC3B;EAEA,IAAI,CAAC0C,eAAe,IAAInI,GAAG,CAAC0F,QAAQ,KAAK,IAAI,EAAE;IAC7CnE,MAAM,IAAI,IAAIvB,GAAG,CAAC0F,QAAQ,EAAE;EAC9B;EAEA,OAAOnE,MAAM;AACf;AAEA,SAAS8G,eAAeA,CAACC,KAAK,EAAE;EAC9B,IAAI1E,MAAM,GAAG,GAAG0E,KAAK,CAACxI,MAAM,KAAK;EACjC8D,MAAM,IAAIH,aAAa,CAAC6E,KAAK,CAAC5E,IAAI,CAAC;EAEnC,IAAI4E,KAAK,CAAC9C,IAAI,KAAK,IAAI,EAAE;IACvB5B,MAAM,IAAI,IAAI0E,KAAK,CAAC9C,IAAI,EAAE;EAC5B;EAEA,OAAO5B,MAAM;AACf;AAEA,SAASwE,aAAaA,CAACpI,GAAG,EAAE;EAC1B,IAAIkF,eAAe,CAAClF,GAAG,CAAC,EAAE;IACxB,OAAOA,GAAG,CAAC4E,IAAI;EACjB;EAEA,IAAIrD,MAAM,GAAG,EAAE;EACf,KAAK,MAAMgH,OAAO,IAAIvI,GAAG,CAAC4E,IAAI,EAAE;IAC9BrD,MAAM,IAAI,IAAIgH,OAAO,EAAE;EACzB;EACA,OAAOhH,MAAM;AACf;AAEAiH,MAAM,CAACC,OAAO,CAACP,YAAY,GAAGA,YAAY;AAE1CM,MAAM,CAACC,OAAO,CAACL,aAAa,GAAGA,aAAa;AAE5CI,MAAM,CAACC,OAAO,CAACC,kBAAkB,GAAG,UAAU1I,GAAG,EAAE;EACjD;EACA,QAAQA,GAAG,CAACF,MAAM;IAChB,KAAK,MAAM;MAAE;QACX,MAAM6I,OAAO,GAAGH,MAAM,CAACC,OAAO,CAACG,QAAQ,CAACR,aAAa,CAACpI,GAAG,CAAC,CAAC;QAC3D,IAAI2I,OAAO,KAAK,IAAI,EAAE;UACpB,OAAO,MAAM;QACf;QACA,IAAIA,OAAO,CAAC7I,MAAM,KAAK,MAAM,IAAI6I,OAAO,CAAC7I,MAAM,KAAK,OAAO,EAAE;UAC3D,OAAO,MAAM;QACf;QACA,OAAO0I,MAAM,CAACC,OAAO,CAACC,kBAAkB,CAACC,OAAO,CAAC;MACnD;IACA,KAAK,KAAK;IACV,KAAK,MAAM;IACX,KAAK,OAAO;IACZ,KAAK,IAAI;IACT,KAAK,KAAK;MACR,OAAON,eAAe,CAAC;QACrBvI,MAAM,EAAEE,GAAG,CAACF,MAAM;QAClB4D,IAAI,EAAE1D,GAAG,CAAC0D,IAAI;QACd8B,IAAI,EAAExF,GAAG,CAACwF;MACZ,CAAC,CAAC;IACJ,KAAK,MAAM;MACT;MACA;MACA;MACA;MACA;MACA;MACA;MACA,OAAO,MAAM;IACf;MACE;MACA,OAAO,MAAM;EACjB;AACF,CAAC;AAEDgD,MAAM,CAACC,OAAO,CAACI,aAAa,GAAG,UAAUrK,KAAK,EAAEsK,OAAO,EAAE;EACvD,IAAIA,OAAO,KAAKlK,SAAS,EAAE;IACzBkK,OAAO,GAAG,CAAC,CAAC;EACd;EAEA,MAAMC,GAAG,GAAG,IAAI5D,eAAe,CAAC3G,KAAK,EAAEsK,OAAO,CAACE,OAAO,EAAEF,OAAO,CAACzD,gBAAgB,EAAEyD,OAAO,CAAC9I,GAAG,EAAE8I,OAAO,CAACxD,aAAa,CAAC;EACrH,IAAIyD,GAAG,CAAC7K,OAAO,EAAE;IACf,OAAO,IAAI;EACb;EAEA,OAAO6K,GAAG,CAAC/I,GAAG;AAChB,CAAC;AAEDwI,MAAM,CAACC,OAAO,CAACQ,cAAc,GAAG,UAAUjJ,GAAG,EAAE+E,QAAQ,EAAE;EACvD/E,GAAG,CAAC+E,QAAQ,GAAG9H,uBAAuB,CAAC8H,QAAQ,EAAExH,uBAAuB,CAAC;AAC3E,CAAC;AAEDiL,MAAM,CAACC,OAAO,CAACS,cAAc,GAAG,UAAUlJ,GAAG,EAAEgF,QAAQ,EAAE;EACvDhF,GAAG,CAACgF,QAAQ,GAAG/H,uBAAuB,CAAC+H,QAAQ,EAAEzH,uBAAuB,CAAC;AAC3E,CAAC;AAEDiL,MAAM,CAACC,OAAO,CAAChF,aAAa,GAAGA,aAAa;AAE5C+E,MAAM,CAACC,OAAO,CAACxD,+BAA+B,GAAGA,+BAA+B;AAEhFuD,MAAM,CAACC,OAAO,CAACvD,eAAe,GAAGA,eAAe;AAEhDsD,MAAM,CAACC,OAAO,CAACU,gBAAgB,GAAG,UAAUC,OAAO,EAAE;EACnD,OAAOvK,MAAM,CAACuK,OAAO,CAAC;AACxB,CAAC;AAEDZ,MAAM,CAACC,OAAO,CAACG,QAAQ,GAAG,UAAUpK,KAAK,EAAEsK,OAAO,EAAE;EAClD,IAAIA,OAAO,KAAKlK,SAAS,EAAE;IACzBkK,OAAO,GAAG,CAAC,CAAC;EACd;;EAEA;EACA,OAAON,MAAM,CAACC,OAAO,CAACI,aAAa,CAACrK,KAAK,EAAE;IAAEwK,OAAO,EAAEF,OAAO,CAACE,OAAO;IAAE3D,gBAAgB,EAAEyD,OAAO,CAACzD;EAAiB,CAAC,CAAC;AACtH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}