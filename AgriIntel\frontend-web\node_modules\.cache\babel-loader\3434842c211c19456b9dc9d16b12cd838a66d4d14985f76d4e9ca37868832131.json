{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.CreateCollectionOperation = void 0;\nconst constants_1 = require(\"../cmap/wire_protocol/constants\");\nconst collection_1 = require(\"../collection\");\nconst error_1 = require(\"../error\");\nconst command_1 = require(\"./command\");\nconst indexes_1 = require(\"./indexes\");\nconst operation_1 = require(\"./operation\");\nconst ILLEGAL_COMMAND_FIELDS = new Set(['w', 'wtimeout', 'timeoutMS', 'j', 'fsync', 'autoIndexId', 'pkFactory', 'raw', 'readPreference', 'session', 'readConcern', 'writeConcern', 'raw', 'fieldsAsRaw', 'useBigInt64', 'promoteLongs', 'promoteValues', 'promoteBuffers', 'bsonRegExp', 'serializeFunctions', 'ignoreUndefined', 'enableUtf8Validation']);\n/* @internal */\nconst INVALID_QE_VERSION = 'Driver support of Queryable Encryption is incompatible with server. Upgrade server to use Queryable Encryption.';\n/** @internal */\nclass CreateCollectionOperation extends command_1.CommandOperation {\n  constructor(db, name, options = {}) {\n    super(db, options);\n    this.options = options;\n    this.db = db;\n    this.name = name;\n  }\n  get commandName() {\n    return 'create';\n  }\n  async execute(server, session, timeoutContext) {\n    const db = this.db;\n    const name = this.name;\n    const options = this.options;\n    const encryptedFields = options.encryptedFields ?? db.client.s.options.autoEncryption?.encryptedFieldsMap?.[`${db.databaseName}.${name}`];\n    if (encryptedFields) {\n      // Creating a QE collection required min server of 7.0.0\n      // TODO(NODE-5353): Get wire version information from connection.\n      if (!server.loadBalanced && server.description.maxWireVersion < constants_1.MIN_SUPPORTED_QE_WIRE_VERSION) {\n        throw new error_1.MongoCompatibilityError(`${INVALID_QE_VERSION} The minimum server version required is ${constants_1.MIN_SUPPORTED_QE_SERVER_VERSION}`);\n      }\n      // Create auxilliary collections for queryable encryption support.\n      const escCollection = encryptedFields.escCollection ?? `enxcol_.${name}.esc`;\n      const ecocCollection = encryptedFields.ecocCollection ?? `enxcol_.${name}.ecoc`;\n      for (const collectionName of [escCollection, ecocCollection]) {\n        const createOp = new CreateCollectionOperation(db, collectionName, {\n          clusteredIndex: {\n            key: {\n              _id: 1\n            },\n            unique: true\n          }\n        });\n        await createOp.executeWithoutEncryptedFieldsCheck(server, session, timeoutContext);\n      }\n      if (!options.encryptedFields) {\n        this.options = {\n          ...this.options,\n          encryptedFields\n        };\n      }\n    }\n    const coll = await this.executeWithoutEncryptedFieldsCheck(server, session, timeoutContext);\n    if (encryptedFields) {\n      // Create the required index for queryable encryption support.\n      const createIndexOp = indexes_1.CreateIndexesOperation.fromIndexSpecification(db, name, {\n        __safeContent__: 1\n      }, {});\n      await createIndexOp.execute(server, session, timeoutContext);\n    }\n    return coll;\n  }\n  async executeWithoutEncryptedFieldsCheck(server, session, timeoutContext) {\n    const db = this.db;\n    const name = this.name;\n    const options = this.options;\n    const cmd = {\n      create: name\n    };\n    for (const n in options) {\n      if (options[n] != null && typeof options[n] !== 'function' && !ILLEGAL_COMMAND_FIELDS.has(n)) {\n        cmd[n] = options[n];\n      }\n    }\n    // otherwise just execute the command\n    await super.executeCommand(server, session, cmd, timeoutContext);\n    return new collection_1.Collection(db, name, options);\n  }\n}\nexports.CreateCollectionOperation = CreateCollectionOperation;\n(0, operation_1.defineAspects)(CreateCollectionOperation, [operation_1.Aspect.WRITE_OPERATION]);", "map": {"version": 3, "names": ["constants_1", "require", "collection_1", "error_1", "command_1", "indexes_1", "operation_1", "ILLEGAL_COMMAND_FIELDS", "Set", "INVALID_QE_VERSION", "CreateCollectionOperation", "CommandOperation", "constructor", "db", "name", "options", "commandName", "execute", "server", "session", "timeoutContext", "encryptedFields", "client", "s", "autoEncryption", "encryptedFieldsMap", "databaseName", "loadBalanced", "description", "maxWireVersion", "MIN_SUPPORTED_QE_WIRE_VERSION", "MongoCompatibilityError", "MIN_SUPPORTED_QE_SERVER_VERSION", "escCollection", "ecocCollection", "collectionName", "createOp", "clusteredIndex", "key", "_id", "unique", "executeWithoutEncryptedFieldsCheck", "coll", "createIndexOp", "CreateIndexesOperation", "fromIndexSpecification", "__safeContent__", "cmd", "create", "n", "has", "executeCommand", "Collection", "exports", "defineAspects", "Aspect", "WRITE_OPERATION"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\create_collection.ts"], "sourcesContent": ["import type { Document } from '../bson';\nimport {\n  MIN_SUPPORTED_QE_SERVER_VERSION,\n  MIN_SUPPORTED_QE_WIRE_VERSION\n} from '../cmap/wire_protocol/constants';\nimport { Collection } from '../collection';\nimport type { Db } from '../db';\nimport { MongoCompatibilityError } from '../error';\nimport type { PkFactory } from '../mongo_client';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { CommandOperation, type CommandOperationOptions } from './command';\nimport { CreateIndexesOperation } from './indexes';\nimport { Aspect, defineAspects } from './operation';\n\nconst ILLEGAL_COMMAND_FIELDS = new Set([\n  'w',\n  'wtimeout',\n  'timeoutMS',\n  'j',\n  'fsync',\n  'autoIndexId',\n  'pkFactory',\n  'raw',\n  'readPreference',\n  'session',\n  'readConcern',\n  'writeConcern',\n  'raw',\n  'fieldsAsRaw',\n  'useBigInt64',\n  'promoteLongs',\n  'promoteValues',\n  'promoteBuffers',\n  'bsonRegExp',\n  'serializeFunctions',\n  'ignoreUndefined',\n  'enableUtf8Validation'\n]);\n\n/** @public\n * Configuration options for timeseries collections\n * @see https://www.mongodb.com/docs/manual/core/timeseries-collections/\n */\nexport interface TimeSeriesCollectionOptions extends Document {\n  timeField: string;\n  metaField?: string;\n  granularity?: 'seconds' | 'minutes' | 'hours' | string;\n  bucketMaxSpanSeconds?: number;\n  bucketRoundingSeconds?: number;\n}\n\n/** @public\n * Configuration options for clustered collections\n * @see https://www.mongodb.com/docs/manual/core/clustered-collections/\n */\nexport interface ClusteredCollectionOptions extends Document {\n  name?: string;\n  key: Document;\n  unique: boolean;\n}\n\n/** @public */\nexport interface CreateCollectionOptions extends CommandOperationOptions {\n  /** Create a capped collection */\n  capped?: boolean;\n  /** @deprecated Create an index on the _id field of the document. This option is deprecated in MongoDB 3.2+ and will be removed once no longer supported by the server. */\n  autoIndexId?: boolean;\n  /** The size of the capped collection in bytes */\n  size?: number;\n  /** The maximum number of documents in the capped collection */\n  max?: number;\n  /** Available for the MMAPv1 storage engine only to set the usePowerOf2Sizes and the noPadding flag */\n  flags?: number;\n  /** Allows users to specify configuration to the storage engine on a per-collection basis when creating a collection */\n  storageEngine?: Document;\n  /** Allows users to specify validation rules or expressions for the collection. For more information, see Document Validation */\n  validator?: Document;\n  /** Determines how strictly MongoDB applies the validation rules to existing documents during an update */\n  validationLevel?: string;\n  /** Determines whether to error on invalid documents or just warn about the violations but allow invalid documents to be inserted */\n  validationAction?: string;\n  /** Allows users to specify a default configuration for indexes when creating a collection */\n  indexOptionDefaults?: Document;\n  /** The name of the source collection or view from which to create the view. The name is not the full namespace of the collection or view (i.e., does not include the database name and implies the same database as the view to create) */\n  viewOn?: string;\n  /** An array that consists of the aggregation pipeline stage. Creates the view by applying the specified pipeline to the viewOn collection or view */\n  pipeline?: Document[];\n  /** A primary key factory function for generation of custom _id keys. */\n  pkFactory?: PkFactory;\n  /** A document specifying configuration options for timeseries collections. */\n  timeseries?: TimeSeriesCollectionOptions;\n  /** A document specifying configuration options for clustered collections. For MongoDB 5.3 and above. */\n  clusteredIndex?: ClusteredCollectionOptions;\n  /** The number of seconds after which a document in a timeseries or clustered collection expires. */\n  expireAfterSeconds?: number;\n  /** @experimental */\n  encryptedFields?: Document;\n  /**\n   * If set, enables pre-update and post-update document events to be included for any\n   * change streams that listen on this collection.\n   */\n  changeStreamPreAndPostImages?: { enabled: boolean };\n}\n\n/* @internal */\nconst INVALID_QE_VERSION =\n  'Driver support of Queryable Encryption is incompatible with server. Upgrade server to use Queryable Encryption.';\n\n/** @internal */\nexport class CreateCollectionOperation extends CommandOperation<Collection> {\n  override options: CreateCollectionOptions;\n  db: Db;\n  name: string;\n\n  constructor(db: Db, name: string, options: CreateCollectionOptions = {}) {\n    super(db, options);\n\n    this.options = options;\n    this.db = db;\n    this.name = name;\n  }\n\n  override get commandName() {\n    return 'create' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<Collection> {\n    const db = this.db;\n    const name = this.name;\n    const options = this.options;\n\n    const encryptedFields: Document | undefined =\n      options.encryptedFields ??\n      db.client.s.options.autoEncryption?.encryptedFieldsMap?.[`${db.databaseName}.${name}`];\n\n    if (encryptedFields) {\n      // Creating a QE collection required min server of 7.0.0\n      // TODO(NODE-5353): Get wire version information from connection.\n      if (\n        !server.loadBalanced &&\n        server.description.maxWireVersion < MIN_SUPPORTED_QE_WIRE_VERSION\n      ) {\n        throw new MongoCompatibilityError(\n          `${INVALID_QE_VERSION} The minimum server version required is ${MIN_SUPPORTED_QE_SERVER_VERSION}`\n        );\n      }\n      // Create auxilliary collections for queryable encryption support.\n      const escCollection = encryptedFields.escCollection ?? `enxcol_.${name}.esc`;\n      const ecocCollection = encryptedFields.ecocCollection ?? `enxcol_.${name}.ecoc`;\n\n      for (const collectionName of [escCollection, ecocCollection]) {\n        const createOp = new CreateCollectionOperation(db, collectionName, {\n          clusteredIndex: {\n            key: { _id: 1 },\n            unique: true\n          }\n        });\n        await createOp.executeWithoutEncryptedFieldsCheck(server, session, timeoutContext);\n      }\n\n      if (!options.encryptedFields) {\n        this.options = { ...this.options, encryptedFields };\n      }\n    }\n\n    const coll = await this.executeWithoutEncryptedFieldsCheck(server, session, timeoutContext);\n\n    if (encryptedFields) {\n      // Create the required index for queryable encryption support.\n      const createIndexOp = CreateIndexesOperation.fromIndexSpecification(\n        db,\n        name,\n        { __safeContent__: 1 },\n        {}\n      );\n      await createIndexOp.execute(server, session, timeoutContext);\n    }\n\n    return coll;\n  }\n\n  private async executeWithoutEncryptedFieldsCheck(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<Collection> {\n    const db = this.db;\n    const name = this.name;\n    const options = this.options;\n\n    const cmd: Document = { create: name };\n    for (const n in options) {\n      if (\n        (options as any)[n] != null &&\n        typeof (options as any)[n] !== 'function' &&\n        !ILLEGAL_COMMAND_FIELDS.has(n)\n      ) {\n        cmd[n] = (options as any)[n];\n      }\n    }\n    // otherwise just execute the command\n    await super.executeCommand(server, session, cmd, timeoutContext);\n    return new Collection(db, name, options);\n  }\n}\n\ndefineAspects(CreateCollectionOperation, [Aspect.WRITE_OPERATION]);\n"], "mappings": ";;;;;;AACA,MAAAA,WAAA,GAAAC,OAAA;AAIA,MAAAC,YAAA,GAAAD,OAAA;AAEA,MAAAE,OAAA,GAAAF,OAAA;AAKA,MAAAG,SAAA,GAAAH,OAAA;AACA,MAAAI,SAAA,GAAAJ,OAAA;AACA,MAAAK,WAAA,GAAAL,OAAA;AAEA,MAAMM,sBAAsB,GAAG,IAAIC,GAAG,CAAC,CACrC,GAAG,EACH,UAAU,EACV,WAAW,EACX,GAAG,EACH,OAAO,EACP,aAAa,EACb,WAAW,EACX,KAAK,EACL,gBAAgB,EAChB,SAAS,EACT,aAAa,EACb,cAAc,EACd,KAAK,EACL,aAAa,EACb,aAAa,EACb,cAAc,EACd,eAAe,EACf,gBAAgB,EAChB,YAAY,EACZ,oBAAoB,EACpB,iBAAiB,EACjB,sBAAsB,CACvB,CAAC;AAmEF;AACA,MAAMC,kBAAkB,GACtB,iHAAiH;AAEnH;AACA,MAAaC,yBAA0B,SAAQN,SAAA,CAAAO,gBAA4B;EAKzEC,YAAYC,EAAM,EAAEC,IAAY,EAAEC,OAAA,GAAmC,EAAE;IACrE,KAAK,CAACF,EAAE,EAAEE,OAAO,CAAC;IAElB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACF,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,IAAI,GAAGA,IAAI;EAClB;EAEA,IAAaE,WAAWA,CAAA;IACtB,OAAO,QAAiB;EAC1B;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMP,EAAE,GAAG,IAAI,CAACA,EAAE;IAClB,MAAMC,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;IAE5B,MAAMM,eAAe,GACnBN,OAAO,CAACM,eAAe,IACvBR,EAAE,CAACS,MAAM,CAACC,CAAC,CAACR,OAAO,CAACS,cAAc,EAAEC,kBAAkB,GAAG,GAAGZ,EAAE,CAACa,YAAY,IAAIZ,IAAI,EAAE,CAAC;IAExF,IAAIO,eAAe,EAAE;MACnB;MACA;MACA,IACE,CAACH,MAAM,CAACS,YAAY,IACpBT,MAAM,CAACU,WAAW,CAACC,cAAc,GAAG7B,WAAA,CAAA8B,6BAA6B,EACjE;QACA,MAAM,IAAI3B,OAAA,CAAA4B,uBAAuB,CAC/B,GAAGtB,kBAAkB,2CAA2CT,WAAA,CAAAgC,+BAA+B,EAAE,CAClG;MACH;MACA;MACA,MAAMC,aAAa,GAAGZ,eAAe,CAACY,aAAa,IAAI,WAAWnB,IAAI,MAAM;MAC5E,MAAMoB,cAAc,GAAGb,eAAe,CAACa,cAAc,IAAI,WAAWpB,IAAI,OAAO;MAE/E,KAAK,MAAMqB,cAAc,IAAI,CAACF,aAAa,EAAEC,cAAc,CAAC,EAAE;QAC5D,MAAME,QAAQ,GAAG,IAAI1B,yBAAyB,CAACG,EAAE,EAAEsB,cAAc,EAAE;UACjEE,cAAc,EAAE;YACdC,GAAG,EAAE;cAAEC,GAAG,EAAE;YAAC,CAAE;YACfC,MAAM,EAAE;;SAEX,CAAC;QACF,MAAMJ,QAAQ,CAACK,kCAAkC,CAACvB,MAAM,EAAEC,OAAO,EAAEC,cAAc,CAAC;MACpF;MAEA,IAAI,CAACL,OAAO,CAACM,eAAe,EAAE;QAC5B,IAAI,CAACN,OAAO,GAAG;UAAE,GAAG,IAAI,CAACA,OAAO;UAAEM;QAAe,CAAE;MACrD;IACF;IAEA,MAAMqB,IAAI,GAAG,MAAM,IAAI,CAACD,kCAAkC,CAACvB,MAAM,EAAEC,OAAO,EAAEC,cAAc,CAAC;IAE3F,IAAIC,eAAe,EAAE;MACnB;MACA,MAAMsB,aAAa,GAAGtC,SAAA,CAAAuC,sBAAsB,CAACC,sBAAsB,CACjEhC,EAAE,EACFC,IAAI,EACJ;QAAEgC,eAAe,EAAE;MAAC,CAAE,EACtB,EAAE,CACH;MACD,MAAMH,aAAa,CAAC1B,OAAO,CAACC,MAAM,EAAEC,OAAO,EAAEC,cAAc,CAAC;IAC9D;IAEA,OAAOsB,IAAI;EACb;EAEQ,MAAMD,kCAAkCA,CAC9CvB,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMP,EAAE,GAAG,IAAI,CAACA,EAAE;IAClB,MAAMC,IAAI,GAAG,IAAI,CAACA,IAAI;IACtB,MAAMC,OAAO,GAAG,IAAI,CAACA,OAAO;IAE5B,MAAMgC,GAAG,GAAa;MAAEC,MAAM,EAAElC;IAAI,CAAE;IACtC,KAAK,MAAMmC,CAAC,IAAIlC,OAAO,EAAE;MACvB,IACGA,OAAe,CAACkC,CAAC,CAAC,IAAI,IAAI,IAC3B,OAAQlC,OAAe,CAACkC,CAAC,CAAC,KAAK,UAAU,IACzC,CAAC1C,sBAAsB,CAAC2C,GAAG,CAACD,CAAC,CAAC,EAC9B;QACAF,GAAG,CAACE,CAAC,CAAC,GAAIlC,OAAe,CAACkC,CAAC,CAAC;MAC9B;IACF;IACA;IACA,MAAM,KAAK,CAACE,cAAc,CAACjC,MAAM,EAAEC,OAAO,EAAE4B,GAAG,EAAE3B,cAAc,CAAC;IAChE,OAAO,IAAIlB,YAAA,CAAAkD,UAAU,CAACvC,EAAE,EAAEC,IAAI,EAAEC,OAAO,CAAC;EAC1C;;AAlGFsC,OAAA,CAAA3C,yBAAA,GAAAA,yBAAA;AAqGA,IAAAJ,WAAA,CAAAgD,aAAa,EAAC5C,yBAAyB,EAAE,CAACJ,WAAA,CAAAiD,MAAM,CAACC,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}