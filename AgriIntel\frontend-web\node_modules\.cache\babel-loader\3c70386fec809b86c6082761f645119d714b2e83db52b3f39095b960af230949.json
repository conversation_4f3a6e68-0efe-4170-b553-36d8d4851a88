{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.IsCappedOperation = void 0;\nconst error_1 = require(\"../error\");\nconst operation_1 = require(\"./operation\");\n/** @internal */\nclass IsCappedOperation extends operation_1.AbstractOperation {\n  constructor(collection, options) {\n    super(options);\n    this.options = options;\n    this.collection = collection;\n  }\n  get commandName() {\n    return 'listCollections';\n  }\n  async execute(server, session) {\n    const coll = this.collection;\n    const [collection] = await coll.s.db.listCollections({\n      name: coll.collectionName\n    }, {\n      ...this.options,\n      nameOnly: false,\n      readPreference: this.readPreference,\n      session\n    }).toArray();\n    if (collection == null || collection.options == null) {\n      throw new error_1.MongoAPIError(`collection ${coll.namespace} not found`);\n    }\n    return !!collection.options?.capped;\n  }\n}\nexports.IsCappedOperation = IsCappedOperation;", "map": {"version": 3, "names": ["error_1", "require", "operation_1", "IsCappedOperation", "AbstractOperation", "constructor", "collection", "options", "commandName", "execute", "server", "session", "coll", "s", "db", "listCollections", "name", "collectionName", "nameOnly", "readPreference", "toArray", "MongoAPIError", "namespace", "capped", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\is_capped.ts"], "sourcesContent": ["import type { Collection } from '../collection';\nimport { MongoAPIError } from '../error';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { AbstractOperation, type OperationOptions } from './operation';\n\n/** @internal */\nexport class IsCappedOperation extends AbstractOperation<boolean> {\n  override options: OperationOptions;\n  collection: Collection;\n\n  constructor(collection: Collection, options: OperationOptions) {\n    super(options);\n    this.options = options;\n    this.collection = collection;\n  }\n\n  override get commandName() {\n    return 'listCollections' as const;\n  }\n\n  override async execute(server: Server, session: ClientSession | undefined): Promise<boolean> {\n    const coll = this.collection;\n    const [collection] = await coll.s.db\n      .listCollections(\n        { name: coll.collectionName },\n        { ...this.options, nameOnly: false, readPreference: this.readPreference, session }\n      )\n      .toArray();\n    if (collection == null || collection.options == null) {\n      throw new MongoAPIError(`collection ${coll.namespace} not found`);\n    }\n    return !!collection.options?.capped;\n  }\n}\n"], "mappings": ";;;;;;AACA,MAAAA,OAAA,GAAAC,OAAA;AAGA,MAAAC,WAAA,GAAAD,OAAA;AAEA;AACA,MAAaE,iBAAkB,SAAQD,WAAA,CAAAE,iBAA0B;EAI/DC,YAAYC,UAAsB,EAAEC,OAAyB;IAC3D,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,UAAU,GAAGA,UAAU;EAC9B;EAEA,IAAaE,WAAWA,CAAA;IACtB,OAAO,iBAA0B;EACnC;EAES,MAAMC,OAAOA,CAACC,MAAc,EAAEC,OAAkC;IACvE,MAAMC,IAAI,GAAG,IAAI,CAACN,UAAU;IAC5B,MAAM,CAACA,UAAU,CAAC,GAAG,MAAMM,IAAI,CAACC,CAAC,CAACC,EAAE,CACjCC,eAAe,CACd;MAAEC,IAAI,EAAEJ,IAAI,CAACK;IAAc,CAAE,EAC7B;MAAE,GAAG,IAAI,CAACV,OAAO;MAAEW,QAAQ,EAAE,KAAK;MAAEC,cAAc,EAAE,IAAI,CAACA,cAAc;MAAER;IAAO,CAAE,CACnF,CACAS,OAAO,EAAE;IACZ,IAAId,UAAU,IAAI,IAAI,IAAIA,UAAU,CAACC,OAAO,IAAI,IAAI,EAAE;MACpD,MAAM,IAAIP,OAAA,CAAAqB,aAAa,CAAC,cAAcT,IAAI,CAACU,SAAS,YAAY,CAAC;IACnE;IACA,OAAO,CAAC,CAAChB,UAAU,CAACC,OAAO,EAAEgB,MAAM;EACrC;;AA1BFC,OAAA,CAAArB,iBAAA,GAAAA,iBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}