{"ast": null, "code": "\"use strict\";\n\nvar __importDefault = this && this.__importDefault || function (mod) {\n  return mod && mod.__esModule ? mod : {\n    \"default\": mod\n  };\n};\nconst index_1 = __importDefault(require(\"./index\"));\nconst memory_code_points_1 = require(\"./memory-code-points\");\nconst code_points_data_browser_1 = __importDefault(require(\"./code-points-data-browser\"));\nconst codePoints = (0, memory_code_points_1.createMemoryCodePoints)(code_points_data_browser_1.default);\nconst saslprep = index_1.default.bind(null, codePoints);\nObject.assign(saslprep, {\n  saslprep,\n  default: saslprep\n});\nmodule.exports = saslprep;", "map": {"version": 3, "names": ["index_1", "__importDefault", "require", "memory_code_points_1", "code_points_data_browser_1", "codePoints", "createMemoryCodePoints", "default", "saslprep", "bind", "Object", "assign", "module", "exports"], "sources": ["../src/browser.ts"], "sourcesContent": [null], "mappings": ";;;;;;;AAAA,MAAAA,OAAA,GAAAC,eAAA,CAAAC,OAAA;AACA,MAAAC,oBAAA,GAAAD,OAAA;AACA,MAAAE,0BAAA,GAAAH,eAAA,CAAAC,OAAA;AAEA,MAAMG,UAAU,GAAG,IAAAF,oBAAA,CAAAG,sBAAsB,EAACF,0BAAA,CAAAG,OAAI,CAAC;AAE/C,MAAMC,QAAQ,GAAGR,OAAA,CAAAO,OAAS,CAACE,IAAI,CAAC,IAAI,EAAEJ,UAAU,CAAC;AAEjDK,MAAM,CAACC,MAAM,CAACH,QAAQ,EAAE;EAAEA,QAAQ;EAAED,OAAO,EAAEC;AAAQ,CAAE,CAAC;AAExDI,MAAA,CAAAC,OAAA,GAASL,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}