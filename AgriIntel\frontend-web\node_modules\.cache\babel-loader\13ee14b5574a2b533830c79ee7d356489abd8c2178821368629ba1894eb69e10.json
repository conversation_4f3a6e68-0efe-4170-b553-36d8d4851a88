{"ast": null, "code": "module.exports = function isBuffer(arg) {\n  return arg && typeof arg === 'object' && typeof arg.copy === 'function' && typeof arg.fill === 'function' && typeof arg.readUInt8 === 'function';\n};", "map": {"version": 3, "names": ["module", "exports", "<PERSON><PERSON><PERSON><PERSON>", "arg", "copy", "fill", "readUInt8"], "sources": ["C:/Users/<USER>/node_modules/util/support/isBufferBrowser.js"], "sourcesContent": ["module.exports = function isBuffer(arg) {\n  return arg && typeof arg === 'object'\n    && typeof arg.copy === 'function'\n    && typeof arg.fill === 'function'\n    && typeof arg.readUInt8 === 'function';\n}"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAG,SAASC,QAAQA,CAACC,GAAG,EAAE;EACtC,OAAOA,GAAG,IAAI,OAAOA,GAAG,KAAK,QAAQ,IAChC,OAAOA,GAAG,CAACC,IAAI,KAAK,UAAU,IAC9B,OAAOD,GAAG,CAACE,IAAI,KAAK,UAAU,IAC9B,OAAOF,GAAG,CAACG,SAAS,KAAK,UAAU;AAC1C,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}