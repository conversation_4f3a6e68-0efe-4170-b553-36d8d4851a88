{"ast": null, "code": "import _inherits<PERSON>oose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { Subscribable } from './subscribable';\nimport { isServer } from './utils';\nexport var OnlineManager = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(OnlineManager, _Subscribable);\n  function OnlineManager() {\n    var _this;\n    _this = _Subscribable.call(this) || this;\n    _this.setup = function (onOnline) {\n      var _window;\n      if (!isServer && ((_window = window) == null ? void 0 : _window.addEventListener)) {\n        var listener = function listener() {\n          return onOnline();\n        }; // Listen to online\n\n        window.addEventListener('online', listener, false);\n        window.addEventListener('offline', listener, false);\n        return function () {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('online', listener);\n          window.removeEventListener('offline', listener);\n        };\n      }\n    };\n    return _this;\n  }\n  var _proto = OnlineManager.prototype;\n  _proto.onSubscribe = function onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  };\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  };\n  _proto.setEventListener = function setEventListener(setup) {\n    var _this$cleanup2,\n      _this2 = this;\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(function (online) {\n      if (typeof online === 'boolean') {\n        _this2.setOnline(online);\n      } else {\n        _this2.onOnline();\n      }\n    });\n  };\n  _proto.setOnline = function setOnline(online) {\n    this.online = online;\n    if (online) {\n      this.onOnline();\n    }\n  };\n  _proto.onOnline = function onOnline() {\n    this.listeners.forEach(function (listener) {\n      listener();\n    });\n  };\n  _proto.isOnline = function isOnline() {\n    if (typeof this.online === 'boolean') {\n      return this.online;\n    }\n    if (typeof navigator === 'undefined' || typeof navigator.onLine === 'undefined') {\n      return true;\n    }\n    return navigator.onLine;\n  };\n  return OnlineManager;\n}(Subscribable);\nexport var onlineManager = new OnlineManager();", "map": {"version": 3, "names": ["_inherits<PERSON><PERSON>e", "Subscribable", "isServer", "OnlineManager", "_Subscribable", "_this", "call", "setup", "onOnline", "_window", "window", "addEventListener", "listener", "removeEventListener", "_proto", "prototype", "onSubscribe", "cleanup", "setEventListener", "onUnsubscribe", "hasListeners", "_this$cleanup", "undefined", "_this$cleanup2", "_this2", "online", "setOnline", "listeners", "for<PERSON>ach", "isOnline", "navigator", "onLine", "onlineManager"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-query/es/core/onlineManager.js"], "sourcesContent": ["import _inherits<PERSON>oose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { Subscribable } from './subscribable';\nimport { isServer } from './utils';\nexport var OnlineManager = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(OnlineManager, _Subscribable);\n\n  function OnlineManager() {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n\n    _this.setup = function (onOnline) {\n      var _window;\n\n      if (!isServer && ((_window = window) == null ? void 0 : _window.addEventListener)) {\n        var listener = function listener() {\n          return onOnline();\n        }; // Listen to online\n\n\n        window.addEventListener('online', listener, false);\n        window.addEventListener('offline', listener, false);\n        return function () {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('online', listener);\n          window.removeEventListener('offline', listener);\n        };\n      }\n    };\n\n    return _this;\n  }\n\n  var _proto = OnlineManager.prototype;\n\n  _proto.onSubscribe = function onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  };\n\n  _proto.setEventListener = function setEventListener(setup) {\n    var _this$cleanup2,\n        _this2 = this;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(function (online) {\n      if (typeof online === 'boolean') {\n        _this2.setOnline(online);\n      } else {\n        _this2.onOnline();\n      }\n    });\n  };\n\n  _proto.setOnline = function setOnline(online) {\n    this.online = online;\n\n    if (online) {\n      this.onOnline();\n    }\n  };\n\n  _proto.onOnline = function onOnline() {\n    this.listeners.forEach(function (listener) {\n      listener();\n    });\n  };\n\n  _proto.isOnline = function isOnline() {\n    if (typeof this.online === 'boolean') {\n      return this.online;\n    }\n\n    if (typeof navigator === 'undefined' || typeof navigator.onLine === 'undefined') {\n      return true;\n    }\n\n    return navigator.onLine;\n  };\n\n  return OnlineManager;\n}(Subscribable);\nexport var onlineManager = new OnlineManager();"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,QAAQ,QAAQ,SAAS;AAClC,OAAO,IAAIC,aAAa,GAAG,aAAa,UAAUC,aAAa,EAAE;EAC/DJ,cAAc,CAACG,aAAa,EAAEC,aAAa,CAAC;EAE5C,SAASD,aAAaA,CAAA,EAAG;IACvB,IAAIE,KAAK;IAETA,KAAK,GAAGD,aAAa,CAACE,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IAExCD,KAAK,CAACE,KAAK,GAAG,UAAUC,QAAQ,EAAE;MAChC,IAAIC,OAAO;MAEX,IAAI,CAACP,QAAQ,KAAK,CAACO,OAAO,GAAGC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,OAAO,CAACE,gBAAgB,CAAC,EAAE;QACjF,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;UACjC,OAAOJ,QAAQ,CAAC,CAAC;QACnB,CAAC,CAAC,CAAC;;QAGHE,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAEC,QAAQ,EAAE,KAAK,CAAC;QAClDF,MAAM,CAACC,gBAAgB,CAAC,SAAS,EAAEC,QAAQ,EAAE,KAAK,CAAC;QACnD,OAAO,YAAY;UACjB;UACAF,MAAM,CAACG,mBAAmB,CAAC,QAAQ,EAAED,QAAQ,CAAC;UAC9CF,MAAM,CAACG,mBAAmB,CAAC,SAAS,EAAED,QAAQ,CAAC;QACjD,CAAC;MACH;IACF,CAAC;IAED,OAAOP,KAAK;EACd;EAEA,IAAIS,MAAM,GAAGX,aAAa,CAACY,SAAS;EAEpCD,MAAM,CAACE,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IAC1C,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;MACjB,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACX,KAAK,CAAC;IACnC;EACF,CAAC;EAEDO,MAAM,CAACK,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC9C,IAAI,CAAC,IAAI,CAACC,YAAY,CAAC,CAAC,EAAE;MACxB,IAAIC,aAAa;MAEjB,CAACA,aAAa,GAAG,IAAI,CAACJ,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGI,aAAa,CAACf,IAAI,CAAC,IAAI,CAAC;MAC1E,IAAI,CAACW,OAAO,GAAGK,SAAS;IAC1B;EACF,CAAC;EAEDR,MAAM,CAACI,gBAAgB,GAAG,SAASA,gBAAgBA,CAACX,KAAK,EAAE;IACzD,IAAIgB,cAAc;MACdC,MAAM,GAAG,IAAI;IAEjB,IAAI,CAACjB,KAAK,GAAGA,KAAK;IAClB,CAACgB,cAAc,GAAG,IAAI,CAACN,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGM,cAAc,CAACjB,IAAI,CAAC,IAAI,CAAC;IAC5E,IAAI,CAACW,OAAO,GAAGV,KAAK,CAAC,UAAUkB,MAAM,EAAE;MACrC,IAAI,OAAOA,MAAM,KAAK,SAAS,EAAE;QAC/BD,MAAM,CAACE,SAAS,CAACD,MAAM,CAAC;MAC1B,CAAC,MAAM;QACLD,MAAM,CAAChB,QAAQ,CAAC,CAAC;MACnB;IACF,CAAC,CAAC;EACJ,CAAC;EAEDM,MAAM,CAACY,SAAS,GAAG,SAASA,SAASA,CAACD,MAAM,EAAE;IAC5C,IAAI,CAACA,MAAM,GAAGA,MAAM;IAEpB,IAAIA,MAAM,EAAE;MACV,IAAI,CAACjB,QAAQ,CAAC,CAAC;IACjB;EACF,CAAC;EAEDM,MAAM,CAACN,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IACpC,IAAI,CAACmB,SAAS,CAACC,OAAO,CAAC,UAAUhB,QAAQ,EAAE;MACzCA,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC;EACJ,CAAC;EAEDE,MAAM,CAACe,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;IACpC,IAAI,OAAO,IAAI,CAACJ,MAAM,KAAK,SAAS,EAAE;MACpC,OAAO,IAAI,CAACA,MAAM;IACpB;IAEA,IAAI,OAAOK,SAAS,KAAK,WAAW,IAAI,OAAOA,SAAS,CAACC,MAAM,KAAK,WAAW,EAAE;MAC/E,OAAO,IAAI;IACb;IAEA,OAAOD,SAAS,CAACC,MAAM;EACzB,CAAC;EAED,OAAO5B,aAAa;AACtB,CAAC,CAACF,YAAY,CAAC;AACf,OAAO,IAAI+B,aAAa,GAAG,IAAI7B,aAAa,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}