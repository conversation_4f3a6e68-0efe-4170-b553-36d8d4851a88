{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ClientBulkWriteCursorResponse = exports.ExplainedCursorResponse = exports.CursorResponse = exports.MongoDBResponse = void 0;\nexports.isErrorResponse = isErrorResponse;\nconst bson_1 = require(\"../../bson\");\nconst error_1 = require(\"../../error\");\nconst utils_1 = require(\"../../utils\");\nconst document_1 = require(\"./on_demand/document\");\n/**\n * Accepts a BSON payload and checks for na \"ok: 0\" element.\n * This utility is intended to prevent calling response class constructors\n * that expect the result to be a success and demand certain properties to exist.\n *\n * For example, a cursor response always expects a cursor embedded document.\n * In order to write the class such that the properties reflect that assertion (non-null)\n * we cannot invoke the subclass constructor if the BSON represents an error.\n *\n * @param bytes - BSON document returned from the server\n */\nfunction isErrorResponse(bson, elements) {\n  for (let eIdx = 0; eIdx < elements.length; eIdx++) {\n    const element = elements[eIdx];\n    if (element[2 /* BSONElementOffset.nameLength */] === 2) {\n      const nameOffset = element[1 /* BSONElementOffset.nameOffset */];\n      // 111 == \"o\", 107 == \"k\"\n      if (bson[nameOffset] === 111 && bson[nameOffset + 1] === 107) {\n        const valueOffset = element[3 /* BSONElementOffset.offset */];\n        const valueLength = element[4 /* BSONElementOffset.length */];\n        // If any byte in the length of the ok number (works for any type) is non zero,\n        // then it is considered \"ok: 1\"\n        for (let i = valueOffset; i < valueOffset + valueLength; i++) {\n          if (bson[i] !== 0x00) return false;\n        }\n        return true;\n      }\n    }\n  }\n  return true;\n}\n/** @internal */\nclass MongoDBResponse extends document_1.OnDemandDocument {\n  get(name, as, required) {\n    try {\n      return super.get(name, as, required);\n    } catch (cause) {\n      throw new error_1.MongoUnexpectedServerResponseError(cause.message, {\n        cause\n      });\n    }\n  }\n  static is(value) {\n    return value instanceof MongoDBResponse;\n  }\n  static make(bson) {\n    const elements = (0, bson_1.parseToElementsToArray)(bson, 0);\n    const isError = isErrorResponse(bson, elements);\n    return isError ? new MongoDBResponse(bson, 0, false, elements) : new this(bson, 0, false, elements);\n  }\n  /**\n   * Returns true iff:\n   * - ok is 0 and the top-level code === 50\n   * - ok is 1 and the writeErrors array contains a code === 50\n   * - ok is 1 and the writeConcern object contains a code === 50\n   */\n  get isMaxTimeExpiredError() {\n    // {ok: 0, code: 50 ... }\n    const isTopLevel = this.ok === 0 && this.code === error_1.MONGODB_ERROR_CODES.MaxTimeMSExpired;\n    if (isTopLevel) return true;\n    if (this.ok === 0) return false;\n    // {ok: 1, writeConcernError: {code: 50 ... }}\n    const isWriteConcern = this.get('writeConcernError', bson_1.BSONType.object)?.getNumber('code') === error_1.MONGODB_ERROR_CODES.MaxTimeMSExpired;\n    if (isWriteConcern) return true;\n    const writeErrors = this.get('writeErrors', bson_1.BSONType.array);\n    if (writeErrors?.size()) {\n      for (let i = 0; i < writeErrors.size(); i++) {\n        const isWriteError = writeErrors.get(i, bson_1.BSONType.object)?.getNumber('code') === error_1.MONGODB_ERROR_CODES.MaxTimeMSExpired;\n        // {ok: 1, writeErrors: [{code: 50 ... }]}\n        if (isWriteError) return true;\n      }\n    }\n    return false;\n  }\n  /**\n   * Drivers can safely assume that the `recoveryToken` field is always a BSON document but drivers MUST NOT modify the\n   * contents of the document.\n   */\n  get recoveryToken() {\n    return this.get('recoveryToken', bson_1.BSONType.object)?.toObject({\n      promoteValues: false,\n      promoteLongs: false,\n      promoteBuffers: false,\n      validation: {\n        utf8: true\n      }\n    }) ?? null;\n  }\n  /**\n   * The server creates a cursor in response to a snapshot find/aggregate command and reports atClusterTime within the cursor field in the response.\n   * For the distinct command the server adds a top-level atClusterTime field to the response.\n   * The atClusterTime field represents the timestamp of the read and is guaranteed to be majority committed.\n   */\n  get atClusterTime() {\n    return this.get('cursor', bson_1.BSONType.object)?.get('atClusterTime', bson_1.BSONType.timestamp) ?? this.get('atClusterTime', bson_1.BSONType.timestamp);\n  }\n  get operationTime() {\n    return this.get('operationTime', bson_1.BSONType.timestamp);\n  }\n  /** Normalizes whatever BSON value is \"ok\" to a JS number 1 or 0. */\n  get ok() {\n    return this.getNumber('ok') ? 1 : 0;\n  }\n  get $err() {\n    return this.get('$err', bson_1.BSONType.string);\n  }\n  get errmsg() {\n    return this.get('errmsg', bson_1.BSONType.string);\n  }\n  get code() {\n    return this.getNumber('code');\n  }\n  get $clusterTime() {\n    if (!('clusterTime' in this)) {\n      const clusterTimeDoc = this.get('$clusterTime', bson_1.BSONType.object);\n      if (clusterTimeDoc == null) {\n        this.clusterTime = null;\n        return null;\n      }\n      const clusterTime = clusterTimeDoc.get('clusterTime', bson_1.BSONType.timestamp, true);\n      const signature = clusterTimeDoc.get('signature', bson_1.BSONType.object)?.toObject();\n      // @ts-expect-error: `signature` is incorrectly typed. It is public API.\n      this.clusterTime = {\n        clusterTime,\n        signature\n      };\n    }\n    return this.clusterTime ?? null;\n  }\n  toObject(options) {\n    const exactBSONOptions = {\n      ...(0, bson_1.pluckBSONSerializeOptions)(options ?? {}),\n      validation: (0, bson_1.parseUtf8ValidationOption)(options)\n    };\n    return super.toObject(exactBSONOptions);\n  }\n}\nexports.MongoDBResponse = MongoDBResponse;\n// {ok:1}\nMongoDBResponse.empty = new MongoDBResponse(new Uint8Array([13, 0, 0, 0, 16, 111, 107, 0, 1, 0, 0, 0, 0]));\n/** @internal */\nclass CursorResponse extends MongoDBResponse {\n  constructor() {\n    super(...arguments);\n    this._batch = null;\n    this.iterated = 0;\n    this._encryptedBatch = null;\n  }\n  /**\n   * This supports a feature of the FindCursor.\n   * It is an optimization to avoid an extra getMore when the limit has been reached\n   */\n  static get emptyGetMore() {\n    return new CursorResponse((0, bson_1.serialize)({\n      ok: 1,\n      cursor: {\n        id: 0n,\n        nextBatch: []\n      }\n    }));\n  }\n  static is(value) {\n    return value instanceof CursorResponse || value === CursorResponse.emptyGetMore;\n  }\n  get cursor() {\n    return this.get('cursor', bson_1.BSONType.object, true);\n  }\n  get id() {\n    try {\n      return bson_1.Long.fromBigInt(this.cursor.get('id', bson_1.BSONType.long, true));\n    } catch (cause) {\n      throw new error_1.MongoUnexpectedServerResponseError(cause.message, {\n        cause\n      });\n    }\n  }\n  get ns() {\n    const namespace = this.cursor.get('ns', bson_1.BSONType.string);\n    if (namespace != null) return (0, utils_1.ns)(namespace);\n    return null;\n  }\n  get length() {\n    return Math.max(this.batchSize - this.iterated, 0);\n  }\n  get encryptedBatch() {\n    if (this.encryptedResponse == null) return null;\n    if (this._encryptedBatch != null) return this._encryptedBatch;\n    const cursor = this.encryptedResponse?.get('cursor', bson_1.BSONType.object);\n    if (cursor?.has('firstBatch')) this._encryptedBatch = cursor.get('firstBatch', bson_1.BSONType.array, true);else if (cursor?.has('nextBatch')) this._encryptedBatch = cursor.get('nextBatch', bson_1.BSONType.array, true);else throw new error_1.MongoUnexpectedServerResponseError('Cursor document did not contain a batch');\n    return this._encryptedBatch;\n  }\n  get batch() {\n    if (this._batch != null) return this._batch;\n    const cursor = this.cursor;\n    if (cursor.has('firstBatch')) this._batch = cursor.get('firstBatch', bson_1.BSONType.array, true);else if (cursor.has('nextBatch')) this._batch = cursor.get('nextBatch', bson_1.BSONType.array, true);else throw new error_1.MongoUnexpectedServerResponseError('Cursor document did not contain a batch');\n    return this._batch;\n  }\n  get batchSize() {\n    return this.batch?.size();\n  }\n  get postBatchResumeToken() {\n    return this.cursor.get('postBatchResumeToken', bson_1.BSONType.object)?.toObject({\n      promoteValues: false,\n      promoteLongs: false,\n      promoteBuffers: false,\n      validation: {\n        utf8: true\n      }\n    }) ?? null;\n  }\n  shift(options) {\n    if (this.iterated >= this.batchSize) {\n      return null;\n    }\n    const result = this.batch.get(this.iterated, bson_1.BSONType.object, true) ?? null;\n    const encryptedResult = this.encryptedBatch?.get(this.iterated, bson_1.BSONType.object, true) ?? null;\n    this.iterated += 1;\n    if (options?.raw) {\n      return result.toBytes();\n    } else {\n      const object = result.toObject(options);\n      if (encryptedResult) {\n        (0, utils_1.decorateDecryptionResult)(object, encryptedResult.toObject(options), true);\n      }\n      return object;\n    }\n  }\n  clear() {\n    this.iterated = this.batchSize;\n  }\n}\nexports.CursorResponse = CursorResponse;\n/**\n * Explain responses have nothing to do with cursor responses\n * This class serves to temporarily avoid refactoring how cursors handle\n * explain responses which is to detect that the response is not cursor-like and return the explain\n * result as the \"first and only\" document in the \"batch\" and end the \"cursor\"\n */\nclass ExplainedCursorResponse extends CursorResponse {\n  constructor() {\n    super(...arguments);\n    this.isExplain = true;\n    this._length = 1;\n  }\n  get id() {\n    return bson_1.Long.fromBigInt(0n);\n  }\n  get batchSize() {\n    return 0;\n  }\n  get ns() {\n    return null;\n  }\n  get length() {\n    return this._length;\n  }\n  shift(options) {\n    if (this._length === 0) return null;\n    this._length -= 1;\n    return this.toObject(options);\n  }\n}\nexports.ExplainedCursorResponse = ExplainedCursorResponse;\n/**\n * Client bulk writes have some extra metadata at the top level that needs to be\n * included in the result returned to the user.\n */\nclass ClientBulkWriteCursorResponse extends CursorResponse {\n  get insertedCount() {\n    return this.get('nInserted', bson_1.BSONType.int, true);\n  }\n  get upsertedCount() {\n    return this.get('nUpserted', bson_1.BSONType.int, true);\n  }\n  get matchedCount() {\n    return this.get('nMatched', bson_1.BSONType.int, true);\n  }\n  get modifiedCount() {\n    return this.get('nModified', bson_1.BSONType.int, true);\n  }\n  get deletedCount() {\n    return this.get('nDeleted', bson_1.BSONType.int, true);\n  }\n  get writeConcernError() {\n    return this.get('writeConcernError', bson_1.BSONType.object, false);\n  }\n}\nexports.ClientBulkWriteCursorResponse = ClientBulkWriteCursorResponse;", "map": {"version": 3, "names": ["exports", "isErrorResponse", "bson_1", "require", "error_1", "utils_1", "document_1", "bson", "elements", "eIdx", "length", "element", "nameOffset", "valueOffset", "valueLength", "i", "MongoDBResponse", "OnDemandDocument", "get", "name", "as", "required", "cause", "MongoUnexpectedServerResponseError", "message", "is", "value", "make", "parseToElementsToArray", "isError", "isMaxTimeExpiredError", "isTopLevel", "ok", "code", "MONGODB_ERROR_CODES", "MaxTimeMSExpired", "isWriteConcern", "BSONType", "object", "getNumber", "writeErrors", "array", "size", "isWriteError", "recoveryToken", "toObject", "promoteValues", "promoteLongs", "promoteBuffers", "validation", "utf8", "atClusterTime", "timestamp", "operationTime", "$err", "string", "errmsg", "$clusterTime", "clusterTimeDoc", "clusterTime", "signature", "options", "exactBSONOptions", "pluckBSONSerializeOptions", "parseUtf8ValidationOption", "empty", "Uint8Array", "CursorResponse", "constructor", "_batch", "iterated", "_encryptedBatch", "emptyGetMore", "serialize", "cursor", "id", "nextBatch", "<PERSON>", "fromBigInt", "long", "ns", "namespace", "Math", "max", "batchSize", "encryptedBatch", "encryptedResponse", "has", "batch", "postBatchResumeToken", "shift", "result", "encryptedResult", "raw", "toBytes", "decorateDecryptionResult", "clear", "ExplainedCursorResponse", "isExplain", "_length", "ClientBulkWriteCursorResponse", "insertedCount", "int", "upsertedCount", "matchedCount", "modifiedCount", "deletedCount", "writeConcernError"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\wire_protocol\\responses.ts"], "sourcesContent": ["import {\n  type BSONElement,\n  type BSONSerializeOptions,\n  BSONType,\n  type DeserializeOptions,\n  type Document,\n  Long,\n  parseToElementsToArray,\n  parseUtf8ValidationOption,\n  pluckBSONSerializeOptions,\n  serialize,\n  type Timestamp\n} from '../../bson';\nimport { MONGODB_ERROR_CODES, MongoUnexpectedServerResponseError } from '../../error';\nimport { type ClusterTime } from '../../sdam/common';\nimport { decorateDecryptionResult, ns } from '../../utils';\nimport {\n  type JSTypeOf,\n  OnDemandDocument,\n  type OnDemandDocumentDeserializeOptions\n} from './on_demand/document';\n\n// eslint-disable-next-line no-restricted-syntax\nconst enum BSONElementOffset {\n  type = 0,\n  nameOffset = 1,\n  nameLength = 2,\n  offset = 3,\n  length = 4\n}\n/**\n * Accepts a BSON payload and checks for na \"ok: 0\" element.\n * This utility is intended to prevent calling response class constructors\n * that expect the result to be a success and demand certain properties to exist.\n *\n * For example, a cursor response always expects a cursor embedded document.\n * In order to write the class such that the properties reflect that assertion (non-null)\n * we cannot invoke the subclass constructor if the BSON represents an error.\n *\n * @param bytes - BSON document returned from the server\n */\nexport function isErrorResponse(bson: Uint8Array, elements: BSONElement[]): boolean {\n  for (let eIdx = 0; eIdx < elements.length; eIdx++) {\n    const element = elements[eIdx];\n\n    if (element[BSONElementOffset.nameLength] === 2) {\n      const nameOffset = element[BSONElementOffset.nameOffset];\n\n      // 111 == \"o\", 107 == \"k\"\n      if (bson[nameOffset] === 111 && bson[nameOffset + 1] === 107) {\n        const valueOffset = element[BSONElementOffset.offset];\n        const valueLength = element[BSONElementOffset.length];\n\n        // If any byte in the length of the ok number (works for any type) is non zero,\n        // then it is considered \"ok: 1\"\n        for (let i = valueOffset; i < valueOffset + valueLength; i++) {\n          if (bson[i] !== 0x00) return false;\n        }\n\n        return true;\n      }\n    }\n  }\n\n  return true;\n}\n\n/** @internal */\nexport type MongoDBResponseConstructor = {\n  new (bson: Uint8Array, offset?: number, isArray?: boolean): MongoDBResponse;\n  make(bson: Uint8Array): MongoDBResponse;\n};\n\n/** @internal */\nexport class MongoDBResponse extends OnDemandDocument {\n  // Wrap error thrown from BSON\n  public override get<const T extends keyof JSTypeOf>(\n    name: string | number,\n    as: T,\n    required?: false | undefined\n  ): JSTypeOf[T] | null;\n  public override get<const T extends keyof JSTypeOf>(\n    name: string | number,\n    as: T,\n    required: true\n  ): JSTypeOf[T];\n  public override get<const T extends keyof JSTypeOf>(\n    name: string | number,\n    as: T,\n    required?: boolean | undefined\n  ): JSTypeOf[T] | null {\n    try {\n      return super.get(name, as, required);\n    } catch (cause) {\n      throw new MongoUnexpectedServerResponseError(cause.message, { cause });\n    }\n  }\n\n  static is(value: unknown): value is MongoDBResponse {\n    return value instanceof MongoDBResponse;\n  }\n\n  static make(bson: Uint8Array) {\n    const elements = parseToElementsToArray(bson, 0);\n    const isError = isErrorResponse(bson, elements);\n    return isError\n      ? new MongoDBResponse(bson, 0, false, elements)\n      : new this(bson, 0, false, elements);\n  }\n\n  // {ok:1}\n  static empty = new MongoDBResponse(new Uint8Array([13, 0, 0, 0, 16, 111, 107, 0, 1, 0, 0, 0, 0]));\n\n  /**\n   * Returns true iff:\n   * - ok is 0 and the top-level code === 50\n   * - ok is 1 and the writeErrors array contains a code === 50\n   * - ok is 1 and the writeConcern object contains a code === 50\n   */\n  get isMaxTimeExpiredError() {\n    // {ok: 0, code: 50 ... }\n    const isTopLevel = this.ok === 0 && this.code === MONGODB_ERROR_CODES.MaxTimeMSExpired;\n    if (isTopLevel) return true;\n\n    if (this.ok === 0) return false;\n\n    // {ok: 1, writeConcernError: {code: 50 ... }}\n    const isWriteConcern =\n      this.get('writeConcernError', BSONType.object)?.getNumber('code') ===\n      MONGODB_ERROR_CODES.MaxTimeMSExpired;\n    if (isWriteConcern) return true;\n\n    const writeErrors = this.get('writeErrors', BSONType.array);\n    if (writeErrors?.size()) {\n      for (let i = 0; i < writeErrors.size(); i++) {\n        const isWriteError =\n          writeErrors.get(i, BSONType.object)?.getNumber('code') ===\n          MONGODB_ERROR_CODES.MaxTimeMSExpired;\n\n        // {ok: 1, writeErrors: [{code: 50 ... }]}\n        if (isWriteError) return true;\n      }\n    }\n\n    return false;\n  }\n\n  /**\n   * Drivers can safely assume that the `recoveryToken` field is always a BSON document but drivers MUST NOT modify the\n   * contents of the document.\n   */\n  get recoveryToken(): Document | null {\n    return (\n      this.get('recoveryToken', BSONType.object)?.toObject({\n        promoteValues: false,\n        promoteLongs: false,\n        promoteBuffers: false,\n        validation: { utf8: true }\n      }) ?? null\n    );\n  }\n\n  /**\n   * The server creates a cursor in response to a snapshot find/aggregate command and reports atClusterTime within the cursor field in the response.\n   * For the distinct command the server adds a top-level atClusterTime field to the response.\n   * The atClusterTime field represents the timestamp of the read and is guaranteed to be majority committed.\n   */\n  public get atClusterTime(): Timestamp | null {\n    return (\n      this.get('cursor', BSONType.object)?.get('atClusterTime', BSONType.timestamp) ??\n      this.get('atClusterTime', BSONType.timestamp)\n    );\n  }\n\n  public get operationTime(): Timestamp | null {\n    return this.get('operationTime', BSONType.timestamp);\n  }\n\n  /** Normalizes whatever BSON value is \"ok\" to a JS number 1 or 0. */\n  public get ok(): 0 | 1 {\n    return this.getNumber('ok') ? 1 : 0;\n  }\n\n  public get $err(): string | null {\n    return this.get('$err', BSONType.string);\n  }\n\n  public get errmsg(): string | null {\n    return this.get('errmsg', BSONType.string);\n  }\n\n  public get code(): number | null {\n    return this.getNumber('code');\n  }\n\n  private clusterTime?: ClusterTime | null;\n  public get $clusterTime(): ClusterTime | null {\n    if (!('clusterTime' in this)) {\n      const clusterTimeDoc = this.get('$clusterTime', BSONType.object);\n      if (clusterTimeDoc == null) {\n        this.clusterTime = null;\n        return null;\n      }\n      const clusterTime = clusterTimeDoc.get('clusterTime', BSONType.timestamp, true);\n      const signature = clusterTimeDoc.get('signature', BSONType.object)?.toObject();\n      // @ts-expect-error: `signature` is incorrectly typed. It is public API.\n      this.clusterTime = { clusterTime, signature };\n    }\n    return this.clusterTime ?? null;\n  }\n\n  public override toObject(options?: BSONSerializeOptions): Record<string, any> {\n    const exactBSONOptions = {\n      ...pluckBSONSerializeOptions(options ?? {}),\n      validation: parseUtf8ValidationOption(options)\n    };\n    return super.toObject(exactBSONOptions);\n  }\n}\n\n/** @internal */\nexport class CursorResponse extends MongoDBResponse {\n  /**\n   * Devtools need to know which keys were encrypted before the driver automatically decrypted them.\n   * If decorating is enabled (`Symbol.for('@@mdb.decorateDecryptionResult')`), this field will be set,\n   * storing the original encrypted response from the server, so that we can build an object that has\n   * the list of BSON keys that were encrypted stored at a well known symbol: `Symbol.for('@@mdb.decryptedKeys')`.\n   */\n  encryptedResponse?: MongoDBResponse;\n  /**\n   * This supports a feature of the FindCursor.\n   * It is an optimization to avoid an extra getMore when the limit has been reached\n   */\n  static get emptyGetMore(): CursorResponse {\n    return new CursorResponse(serialize({ ok: 1, cursor: { id: 0n, nextBatch: [] } }));\n  }\n\n  static override is(value: unknown): value is CursorResponse {\n    return value instanceof CursorResponse || value === CursorResponse.emptyGetMore;\n  }\n\n  private _batch: OnDemandDocument | null = null;\n  private iterated = 0;\n\n  get cursor() {\n    return this.get('cursor', BSONType.object, true);\n  }\n\n  public get id(): Long {\n    try {\n      return Long.fromBigInt(this.cursor.get('id', BSONType.long, true));\n    } catch (cause) {\n      throw new MongoUnexpectedServerResponseError(cause.message, { cause });\n    }\n  }\n\n  public get ns() {\n    const namespace = this.cursor.get('ns', BSONType.string);\n    if (namespace != null) return ns(namespace);\n    return null;\n  }\n\n  public get length() {\n    return Math.max(this.batchSize - this.iterated, 0);\n  }\n\n  private _encryptedBatch: OnDemandDocument | null = null;\n  get encryptedBatch() {\n    if (this.encryptedResponse == null) return null;\n    if (this._encryptedBatch != null) return this._encryptedBatch;\n\n    const cursor = this.encryptedResponse?.get('cursor', BSONType.object);\n    if (cursor?.has('firstBatch'))\n      this._encryptedBatch = cursor.get('firstBatch', BSONType.array, true);\n    else if (cursor?.has('nextBatch'))\n      this._encryptedBatch = cursor.get('nextBatch', BSONType.array, true);\n    else throw new MongoUnexpectedServerResponseError('Cursor document did not contain a batch');\n\n    return this._encryptedBatch;\n  }\n\n  private get batch() {\n    if (this._batch != null) return this._batch;\n    const cursor = this.cursor;\n    if (cursor.has('firstBatch')) this._batch = cursor.get('firstBatch', BSONType.array, true);\n    else if (cursor.has('nextBatch')) this._batch = cursor.get('nextBatch', BSONType.array, true);\n    else throw new MongoUnexpectedServerResponseError('Cursor document did not contain a batch');\n    return this._batch;\n  }\n\n  public get batchSize() {\n    return this.batch?.size();\n  }\n\n  public get postBatchResumeToken() {\n    return (\n      this.cursor.get('postBatchResumeToken', BSONType.object)?.toObject({\n        promoteValues: false,\n        promoteLongs: false,\n        promoteBuffers: false,\n        validation: { utf8: true }\n      }) ?? null\n    );\n  }\n\n  public shift(options: OnDemandDocumentDeserializeOptions): any {\n    if (this.iterated >= this.batchSize) {\n      return null;\n    }\n\n    const result = this.batch.get(this.iterated, BSONType.object, true) ?? null;\n    const encryptedResult = this.encryptedBatch?.get(this.iterated, BSONType.object, true) ?? null;\n\n    this.iterated += 1;\n\n    if (options?.raw) {\n      return result.toBytes();\n    } else {\n      const object = result.toObject(options);\n      if (encryptedResult) {\n        decorateDecryptionResult(object, encryptedResult.toObject(options), true);\n      }\n      return object;\n    }\n  }\n\n  public clear() {\n    this.iterated = this.batchSize;\n  }\n}\n\n/**\n * Explain responses have nothing to do with cursor responses\n * This class serves to temporarily avoid refactoring how cursors handle\n * explain responses which is to detect that the response is not cursor-like and return the explain\n * result as the \"first and only\" document in the \"batch\" and end the \"cursor\"\n */\nexport class ExplainedCursorResponse extends CursorResponse {\n  isExplain = true;\n\n  override get id(): Long {\n    return Long.fromBigInt(0n);\n  }\n\n  override get batchSize() {\n    return 0;\n  }\n\n  override get ns() {\n    return null;\n  }\n\n  _length = 1;\n  override get length(): number {\n    return this._length;\n  }\n\n  override shift(options?: DeserializeOptions) {\n    if (this._length === 0) return null;\n    this._length -= 1;\n    return this.toObject(options);\n  }\n}\n\n/**\n * Client bulk writes have some extra metadata at the top level that needs to be\n * included in the result returned to the user.\n */\nexport class ClientBulkWriteCursorResponse extends CursorResponse {\n  get insertedCount() {\n    return this.get('nInserted', BSONType.int, true);\n  }\n\n  get upsertedCount() {\n    return this.get('nUpserted', BSONType.int, true);\n  }\n\n  get matchedCount() {\n    return this.get('nMatched', BSONType.int, true);\n  }\n\n  get modifiedCount() {\n    return this.get('nModified', BSONType.int, true);\n  }\n\n  get deletedCount() {\n    return this.get('nDeleted', BSONType.int, true);\n  }\n\n  get writeConcernError() {\n    return this.get('writeConcernError', BSONType.object, false);\n  }\n}\n"], "mappings": ";;;;;;AAyCAA,OAAA,CAAAC,eAAA,GAAAA,eAAA;AAzCA,MAAAC,MAAA,GAAAC,OAAA;AAaA,MAAAC,OAAA,GAAAD,OAAA;AAEA,MAAAE,OAAA,GAAAF,OAAA;AACA,MAAAG,UAAA,GAAAH,OAAA;AAcA;;;;;;;;;;;AAWA,SAAgBF,eAAeA,CAACM,IAAgB,EAAEC,QAAuB;EACvE,KAAK,IAAIC,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGD,QAAQ,CAACE,MAAM,EAAED,IAAI,EAAE,EAAE;IACjD,MAAME,OAAO,GAAGH,QAAQ,CAACC,IAAI,CAAC;IAE9B,IAAIE,OAAO,sCAA8B,KAAK,CAAC,EAAE;MAC/C,MAAMC,UAAU,GAAGD,OAAO,sCAA8B;MAExD;MACA,IAAIJ,IAAI,CAACK,UAAU,CAAC,KAAK,GAAG,IAAIL,IAAI,CAACK,UAAU,GAAG,CAAC,CAAC,KAAK,GAAG,EAAE;QAC5D,MAAMC,WAAW,GAAGF,OAAO,kCAA0B;QACrD,MAAMG,WAAW,GAAGH,OAAO,kCAA0B;QAErD;QACA;QACA,KAAK,IAAII,CAAC,GAAGF,WAAW,EAAEE,CAAC,GAAGF,WAAW,GAAGC,WAAW,EAAEC,CAAC,EAAE,EAAE;UAC5D,IAAIR,IAAI,CAACQ,CAAC,CAAC,KAAK,IAAI,EAAE,OAAO,KAAK;QACpC;QAEA,OAAO,IAAI;MACb;IACF;EACF;EAEA,OAAO,IAAI;AACb;AAQA;AACA,MAAaC,eAAgB,SAAQV,UAAA,CAAAW,gBAAgB;EAYnCC,GAAGA,CACjBC,IAAqB,EACrBC,EAAK,EACLC,QAA8B;IAE9B,IAAI;MACF,OAAO,KAAK,CAACH,GAAG,CAACC,IAAI,EAAEC,EAAE,EAAEC,QAAQ,CAAC;IACtC,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,MAAM,IAAIlB,OAAA,CAAAmB,kCAAkC,CAACD,KAAK,CAACE,OAAO,EAAE;QAAEF;MAAK,CAAE,CAAC;IACxE;EACF;EAEA,OAAOG,EAAEA,CAACC,KAAc;IACtB,OAAOA,KAAK,YAAYV,eAAe;EACzC;EAEA,OAAOW,IAAIA,CAACpB,IAAgB;IAC1B,MAAMC,QAAQ,GAAG,IAAAN,MAAA,CAAA0B,sBAAsB,EAACrB,IAAI,EAAE,CAAC,CAAC;IAChD,MAAMsB,OAAO,GAAG5B,eAAe,CAACM,IAAI,EAAEC,QAAQ,CAAC;IAC/C,OAAOqB,OAAO,GACV,IAAIb,eAAe,CAACT,IAAI,EAAE,CAAC,EAAE,KAAK,EAAEC,QAAQ,CAAC,GAC7C,IAAI,IAAI,CAACD,IAAI,EAAE,CAAC,EAAE,KAAK,EAAEC,QAAQ,CAAC;EACxC;EAKA;;;;;;EAMA,IAAIsB,qBAAqBA,CAAA;IACvB;IACA,MAAMC,UAAU,GAAG,IAAI,CAACC,EAAE,KAAK,CAAC,IAAI,IAAI,CAACC,IAAI,KAAK7B,OAAA,CAAA8B,mBAAmB,CAACC,gBAAgB;IACtF,IAAIJ,UAAU,EAAE,OAAO,IAAI;IAE3B,IAAI,IAAI,CAACC,EAAE,KAAK,CAAC,EAAE,OAAO,KAAK;IAE/B;IACA,MAAMI,cAAc,GAClB,IAAI,CAAClB,GAAG,CAAC,mBAAmB,EAAEhB,MAAA,CAAAmC,QAAQ,CAACC,MAAM,CAAC,EAAEC,SAAS,CAAC,MAAM,CAAC,KACjEnC,OAAA,CAAA8B,mBAAmB,CAACC,gBAAgB;IACtC,IAAIC,cAAc,EAAE,OAAO,IAAI;IAE/B,MAAMI,WAAW,GAAG,IAAI,CAACtB,GAAG,CAAC,aAAa,EAAEhB,MAAA,CAAAmC,QAAQ,CAACI,KAAK,CAAC;IAC3D,IAAID,WAAW,EAAEE,IAAI,EAAE,EAAE;MACvB,KAAK,IAAI3B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGyB,WAAW,CAACE,IAAI,EAAE,EAAE3B,CAAC,EAAE,EAAE;QAC3C,MAAM4B,YAAY,GAChBH,WAAW,CAACtB,GAAG,CAACH,CAAC,EAAEb,MAAA,CAAAmC,QAAQ,CAACC,MAAM,CAAC,EAAEC,SAAS,CAAC,MAAM,CAAC,KACtDnC,OAAA,CAAA8B,mBAAmB,CAACC,gBAAgB;QAEtC;QACA,IAAIQ,YAAY,EAAE,OAAO,IAAI;MAC/B;IACF;IAEA,OAAO,KAAK;EACd;EAEA;;;;EAIA,IAAIC,aAAaA,CAAA;IACf,OACE,IAAI,CAAC1B,GAAG,CAAC,eAAe,EAAEhB,MAAA,CAAAmC,QAAQ,CAACC,MAAM,CAAC,EAAEO,QAAQ,CAAC;MACnDC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,KAAK;MACnBC,cAAc,EAAE,KAAK;MACrBC,UAAU,EAAE;QAAEC,IAAI,EAAE;MAAI;KACzB,CAAC,IAAI,IAAI;EAEd;EAEA;;;;;EAKA,IAAWC,aAAaA,CAAA;IACtB,OACE,IAAI,CAACjC,GAAG,CAAC,QAAQ,EAAEhB,MAAA,CAAAmC,QAAQ,CAACC,MAAM,CAAC,EAAEpB,GAAG,CAAC,eAAe,EAAEhB,MAAA,CAAAmC,QAAQ,CAACe,SAAS,CAAC,IAC7E,IAAI,CAAClC,GAAG,CAAC,eAAe,EAAEhB,MAAA,CAAAmC,QAAQ,CAACe,SAAS,CAAC;EAEjD;EAEA,IAAWC,aAAaA,CAAA;IACtB,OAAO,IAAI,CAACnC,GAAG,CAAC,eAAe,EAAEhB,MAAA,CAAAmC,QAAQ,CAACe,SAAS,CAAC;EACtD;EAEA;EACA,IAAWpB,EAAEA,CAAA;IACX,OAAO,IAAI,CAACO,SAAS,CAAC,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC;EACrC;EAEA,IAAWe,IAAIA,CAAA;IACb,OAAO,IAAI,CAACpC,GAAG,CAAC,MAAM,EAAEhB,MAAA,CAAAmC,QAAQ,CAACkB,MAAM,CAAC;EAC1C;EAEA,IAAWC,MAAMA,CAAA;IACf,OAAO,IAAI,CAACtC,GAAG,CAAC,QAAQ,EAAEhB,MAAA,CAAAmC,QAAQ,CAACkB,MAAM,CAAC;EAC5C;EAEA,IAAWtB,IAAIA,CAAA;IACb,OAAO,IAAI,CAACM,SAAS,CAAC,MAAM,CAAC;EAC/B;EAGA,IAAWkB,YAAYA,CAAA;IACrB,IAAI,EAAE,aAAa,IAAI,IAAI,CAAC,EAAE;MAC5B,MAAMC,cAAc,GAAG,IAAI,CAACxC,GAAG,CAAC,cAAc,EAAEhB,MAAA,CAAAmC,QAAQ,CAACC,MAAM,CAAC;MAChE,IAAIoB,cAAc,IAAI,IAAI,EAAE;QAC1B,IAAI,CAACC,WAAW,GAAG,IAAI;QACvB,OAAO,IAAI;MACb;MACA,MAAMA,WAAW,GAAGD,cAAc,CAACxC,GAAG,CAAC,aAAa,EAAEhB,MAAA,CAAAmC,QAAQ,CAACe,SAAS,EAAE,IAAI,CAAC;MAC/E,MAAMQ,SAAS,GAAGF,cAAc,CAACxC,GAAG,CAAC,WAAW,EAAEhB,MAAA,CAAAmC,QAAQ,CAACC,MAAM,CAAC,EAAEO,QAAQ,EAAE;MAC9E;MACA,IAAI,CAACc,WAAW,GAAG;QAAEA,WAAW;QAAEC;MAAS,CAAE;IAC/C;IACA,OAAO,IAAI,CAACD,WAAW,IAAI,IAAI;EACjC;EAEgBd,QAAQA,CAACgB,OAA8B;IACrD,MAAMC,gBAAgB,GAAG;MACvB,GAAG,IAAA5D,MAAA,CAAA6D,yBAAyB,EAACF,OAAO,IAAI,EAAE,CAAC;MAC3CZ,UAAU,EAAE,IAAA/C,MAAA,CAAA8D,yBAAyB,EAACH,OAAO;KAC9C;IACD,OAAO,KAAK,CAAChB,QAAQ,CAACiB,gBAAgB,CAAC;EACzC;;AA/IF9D,OAAA,CAAAgB,eAAA,GAAAA,eAAA;AAoCE;AACOA,eAAA,CAAAiD,KAAK,GAAG,IAAIjD,eAAe,CAAC,IAAIkD,UAAU,CAAC,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;AA6GnG;AACA,MAAaC,cAAe,SAAQnD,eAAe;EAAnDoD,YAAA;;IAoBU,KAAAC,MAAM,GAA4B,IAAI;IACtC,KAAAC,QAAQ,GAAG,CAAC;IAwBZ,KAAAC,eAAe,GAA4B,IAAI;EA+DzD;EApGE;;;;EAIA,WAAWC,YAAYA,CAAA;IACrB,OAAO,IAAIL,cAAc,CAAC,IAAAjE,MAAA,CAAAuE,SAAS,EAAC;MAAEzC,EAAE,EAAE,CAAC;MAAE0C,MAAM,EAAE;QAAEC,EAAE,EAAE,EAAE;QAAEC,SAAS,EAAE;MAAE;IAAE,CAAE,CAAC,CAAC;EACpF;EAEA,OAAgBnD,EAAEA,CAACC,KAAc;IAC/B,OAAOA,KAAK,YAAYyC,cAAc,IAAIzC,KAAK,KAAKyC,cAAc,CAACK,YAAY;EACjF;EAKA,IAAIE,MAAMA,CAAA;IACR,OAAO,IAAI,CAACxD,GAAG,CAAC,QAAQ,EAAEhB,MAAA,CAAAmC,QAAQ,CAACC,MAAM,EAAE,IAAI,CAAC;EAClD;EAEA,IAAWqC,EAAEA,CAAA;IACX,IAAI;MACF,OAAOzE,MAAA,CAAA2E,IAAI,CAACC,UAAU,CAAC,IAAI,CAACJ,MAAM,CAACxD,GAAG,CAAC,IAAI,EAAEhB,MAAA,CAAAmC,QAAQ,CAAC0C,IAAI,EAAE,IAAI,CAAC,CAAC;IACpE,CAAC,CAAC,OAAOzD,KAAK,EAAE;MACd,MAAM,IAAIlB,OAAA,CAAAmB,kCAAkC,CAACD,KAAK,CAACE,OAAO,EAAE;QAAEF;MAAK,CAAE,CAAC;IACxE;EACF;EAEA,IAAW0D,EAAEA,CAAA;IACX,MAAMC,SAAS,GAAG,IAAI,CAACP,MAAM,CAACxD,GAAG,CAAC,IAAI,EAAEhB,MAAA,CAAAmC,QAAQ,CAACkB,MAAM,CAAC;IACxD,IAAI0B,SAAS,IAAI,IAAI,EAAE,OAAO,IAAA5E,OAAA,CAAA2E,EAAE,EAACC,SAAS,CAAC;IAC3C,OAAO,IAAI;EACb;EAEA,IAAWvE,MAAMA,CAAA;IACf,OAAOwE,IAAI,CAACC,GAAG,CAAC,IAAI,CAACC,SAAS,GAAG,IAAI,CAACd,QAAQ,EAAE,CAAC,CAAC;EACpD;EAGA,IAAIe,cAAcA,CAAA;IAChB,IAAI,IAAI,CAACC,iBAAiB,IAAI,IAAI,EAAE,OAAO,IAAI;IAC/C,IAAI,IAAI,CAACf,eAAe,IAAI,IAAI,EAAE,OAAO,IAAI,CAACA,eAAe;IAE7D,MAAMG,MAAM,GAAG,IAAI,CAACY,iBAAiB,EAAEpE,GAAG,CAAC,QAAQ,EAAEhB,MAAA,CAAAmC,QAAQ,CAACC,MAAM,CAAC;IACrE,IAAIoC,MAAM,EAAEa,GAAG,CAAC,YAAY,CAAC,EAC3B,IAAI,CAAChB,eAAe,GAAGG,MAAM,CAACxD,GAAG,CAAC,YAAY,EAAEhB,MAAA,CAAAmC,QAAQ,CAACI,KAAK,EAAE,IAAI,CAAC,CAAC,KACnE,IAAIiC,MAAM,EAAEa,GAAG,CAAC,WAAW,CAAC,EAC/B,IAAI,CAAChB,eAAe,GAAGG,MAAM,CAACxD,GAAG,CAAC,WAAW,EAAEhB,MAAA,CAAAmC,QAAQ,CAACI,KAAK,EAAE,IAAI,CAAC,CAAC,KAClE,MAAM,IAAIrC,OAAA,CAAAmB,kCAAkC,CAAC,yCAAyC,CAAC;IAE5F,OAAO,IAAI,CAACgD,eAAe;EAC7B;EAEA,IAAYiB,KAAKA,CAAA;IACf,IAAI,IAAI,CAACnB,MAAM,IAAI,IAAI,EAAE,OAAO,IAAI,CAACA,MAAM;IAC3C,MAAMK,MAAM,GAAG,IAAI,CAACA,MAAM;IAC1B,IAAIA,MAAM,CAACa,GAAG,CAAC,YAAY,CAAC,EAAE,IAAI,CAAClB,MAAM,GAAGK,MAAM,CAACxD,GAAG,CAAC,YAAY,EAAEhB,MAAA,CAAAmC,QAAQ,CAACI,KAAK,EAAE,IAAI,CAAC,CAAC,KACtF,IAAIiC,MAAM,CAACa,GAAG,CAAC,WAAW,CAAC,EAAE,IAAI,CAAClB,MAAM,GAAGK,MAAM,CAACxD,GAAG,CAAC,WAAW,EAAEhB,MAAA,CAAAmC,QAAQ,CAACI,KAAK,EAAE,IAAI,CAAC,CAAC,KACzF,MAAM,IAAIrC,OAAA,CAAAmB,kCAAkC,CAAC,yCAAyC,CAAC;IAC5F,OAAO,IAAI,CAAC8C,MAAM;EACpB;EAEA,IAAWe,SAASA,CAAA;IAClB,OAAO,IAAI,CAACI,KAAK,EAAE9C,IAAI,EAAE;EAC3B;EAEA,IAAW+C,oBAAoBA,CAAA;IAC7B,OACE,IAAI,CAACf,MAAM,CAACxD,GAAG,CAAC,sBAAsB,EAAEhB,MAAA,CAAAmC,QAAQ,CAACC,MAAM,CAAC,EAAEO,QAAQ,CAAC;MACjEC,aAAa,EAAE,KAAK;MACpBC,YAAY,EAAE,KAAK;MACnBC,cAAc,EAAE,KAAK;MACrBC,UAAU,EAAE;QAAEC,IAAI,EAAE;MAAI;KACzB,CAAC,IAAI,IAAI;EAEd;EAEOwC,KAAKA,CAAC7B,OAA2C;IACtD,IAAI,IAAI,CAACS,QAAQ,IAAI,IAAI,CAACc,SAAS,EAAE;MACnC,OAAO,IAAI;IACb;IAEA,MAAMO,MAAM,GAAG,IAAI,CAACH,KAAK,CAACtE,GAAG,CAAC,IAAI,CAACoD,QAAQ,EAAEpE,MAAA,CAAAmC,QAAQ,CAACC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI;IAC3E,MAAMsD,eAAe,GAAG,IAAI,CAACP,cAAc,EAAEnE,GAAG,CAAC,IAAI,CAACoD,QAAQ,EAAEpE,MAAA,CAAAmC,QAAQ,CAACC,MAAM,EAAE,IAAI,CAAC,IAAI,IAAI;IAE9F,IAAI,CAACgC,QAAQ,IAAI,CAAC;IAElB,IAAIT,OAAO,EAAEgC,GAAG,EAAE;MAChB,OAAOF,MAAM,CAACG,OAAO,EAAE;IACzB,CAAC,MAAM;MACL,MAAMxD,MAAM,GAAGqD,MAAM,CAAC9C,QAAQ,CAACgB,OAAO,CAAC;MACvC,IAAI+B,eAAe,EAAE;QACnB,IAAAvF,OAAA,CAAA0F,wBAAwB,EAACzD,MAAM,EAAEsD,eAAe,CAAC/C,QAAQ,CAACgB,OAAO,CAAC,EAAE,IAAI,CAAC;MAC3E;MACA,OAAOvB,MAAM;IACf;EACF;EAEO0D,KAAKA,CAAA;IACV,IAAI,CAAC1B,QAAQ,GAAG,IAAI,CAACc,SAAS;EAChC;;AA3GFpF,OAAA,CAAAmE,cAAA,GAAAA,cAAA;AA8GA;;;;;;AAMA,MAAa8B,uBAAwB,SAAQ9B,cAAc;EAA3DC,YAAA;;IACE,KAAA8B,SAAS,GAAG,IAAI;IAchB,KAAAC,OAAO,GAAG,CAAC;EAUb;EAtBE,IAAaxB,EAAEA,CAAA;IACb,OAAOzE,MAAA,CAAA2E,IAAI,CAACC,UAAU,CAAC,EAAE,CAAC;EAC5B;EAEA,IAAaM,SAASA,CAAA;IACpB,OAAO,CAAC;EACV;EAEA,IAAaJ,EAAEA,CAAA;IACb,OAAO,IAAI;EACb;EAGA,IAAatE,MAAMA,CAAA;IACjB,OAAO,IAAI,CAACyF,OAAO;EACrB;EAEST,KAAKA,CAAC7B,OAA4B;IACzC,IAAI,IAAI,CAACsC,OAAO,KAAK,CAAC,EAAE,OAAO,IAAI;IACnC,IAAI,CAACA,OAAO,IAAI,CAAC;IACjB,OAAO,IAAI,CAACtD,QAAQ,CAACgB,OAAO,CAAC;EAC/B;;AAxBF7D,OAAA,CAAAiG,uBAAA,GAAAA,uBAAA;AA2BA;;;;AAIA,MAAaG,6BAA8B,SAAQjC,cAAc;EAC/D,IAAIkC,aAAaA,CAAA;IACf,OAAO,IAAI,CAACnF,GAAG,CAAC,WAAW,EAAEhB,MAAA,CAAAmC,QAAQ,CAACiE,GAAG,EAAE,IAAI,CAAC;EAClD;EAEA,IAAIC,aAAaA,CAAA;IACf,OAAO,IAAI,CAACrF,GAAG,CAAC,WAAW,EAAEhB,MAAA,CAAAmC,QAAQ,CAACiE,GAAG,EAAE,IAAI,CAAC;EAClD;EAEA,IAAIE,YAAYA,CAAA;IACd,OAAO,IAAI,CAACtF,GAAG,CAAC,UAAU,EAAEhB,MAAA,CAAAmC,QAAQ,CAACiE,GAAG,EAAE,IAAI,CAAC;EACjD;EAEA,IAAIG,aAAaA,CAAA;IACf,OAAO,IAAI,CAACvF,GAAG,CAAC,WAAW,EAAEhB,MAAA,CAAAmC,QAAQ,CAACiE,GAAG,EAAE,IAAI,CAAC;EAClD;EAEA,IAAII,YAAYA,CAAA;IACd,OAAO,IAAI,CAACxF,GAAG,CAAC,UAAU,EAAEhB,MAAA,CAAAmC,QAAQ,CAACiE,GAAG,EAAE,IAAI,CAAC;EACjD;EAEA,IAAIK,iBAAiBA,CAAA;IACnB,OAAO,IAAI,CAACzF,GAAG,CAAC,mBAAmB,EAAEhB,MAAA,CAAAmC,QAAQ,CAACC,MAAM,EAAE,KAAK,CAAC;EAC9D;;AAvBFtC,OAAA,CAAAoG,6BAAA,GAAAA,6BAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}