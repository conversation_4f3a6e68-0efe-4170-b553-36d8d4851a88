{"ast": null, "code": "/*! https://mths.be/punycode v1.4.1 by @mathias */\n;\n(function (root) {\n  /** Detect free variables */\n  var freeExports = typeof exports == 'object' && exports && !exports.nodeType && exports;\n  var freeModule = typeof module == 'object' && module && !module.nodeType && module;\n  var freeGlobal = typeof global == 'object' && global;\n  if (freeGlobal.global === freeGlobal || freeGlobal.window === freeGlobal || freeGlobal.self === freeGlobal) {\n    root = freeGlobal;\n  }\n\n  /**\n   * The `punycode` object.\n   * @name punycode\n   * @type Object\n   */\n  var punycode,\n    /** Highest positive signed 32-bit float value */\n    maxInt = 2147483647,\n    // aka. 0x7FFFFFFF or 2^31-1\n\n    /** Bootstring parameters */\n    base = 36,\n    tMin = 1,\n    tMax = 26,\n    skew = 38,\n    damp = 700,\n    initialBias = 72,\n    initialN = 128,\n    // 0x80\n    delimiter = '-',\n    // '\\x2D'\n\n    /** Regular expressions */\n    regexPunycode = /^xn--/,\n    regexNonASCII = /[^\\x20-\\x7E]/,\n    // unprintable ASCII chars + non-ASCII chars\n    regexSeparators = /[\\x2E\\u3002\\uFF0E\\uFF61]/g,\n    // RFC 3490 separators\n\n    /** Error messages */\n    errors = {\n      'overflow': 'Overflow: input needs wider integers to process',\n      'not-basic': 'Illegal input >= 0x80 (not a basic code point)',\n      'invalid-input': 'Invalid input'\n    },\n    /** Convenience shortcuts */\n    baseMinusTMin = base - tMin,\n    floor = Math.floor,\n    stringFromCharCode = String.fromCharCode,\n    /** Temporary variable */\n    key;\n\n  /*--------------------------------------------------------------------------*/\n\n  /**\n   * A generic error utility function.\n   * @private\n   * @param {String} type The error type.\n   * @returns {Error} Throws a `RangeError` with the applicable error message.\n   */\n  function error(type) {\n    throw new RangeError(errors[type]);\n  }\n\n  /**\n   * A generic `Array#map` utility function.\n   * @private\n   * @param {Array} array The array to iterate over.\n   * @param {Function} callback The function that gets called for every array\n   * item.\n   * @returns {Array} A new array of values returned by the callback function.\n   */\n  function map(array, fn) {\n    var length = array.length;\n    var result = [];\n    while (length--) {\n      result[length] = fn(array[length]);\n    }\n    return result;\n  }\n\n  /**\n   * A simple `Array#map`-like wrapper to work with domain name strings or email\n   * addresses.\n   * @private\n   * @param {String} domain The domain name or email address.\n   * @param {Function} callback The function that gets called for every\n   * character.\n   * @returns {Array} A new string of characters returned by the callback\n   * function.\n   */\n  function mapDomain(string, fn) {\n    var parts = string.split('@');\n    var result = '';\n    if (parts.length > 1) {\n      // In email addresses, only the domain name should be punycoded. Leave\n      // the local part (i.e. everything up to `@`) intact.\n      result = parts[0] + '@';\n      string = parts[1];\n    }\n    // Avoid `split(regex)` for IE8 compatibility. See #17.\n    string = string.replace(regexSeparators, '\\x2E');\n    var labels = string.split('.');\n    var encoded = map(labels, fn).join('.');\n    return result + encoded;\n  }\n\n  /**\n   * Creates an array containing the numeric code points of each Unicode\n   * character in the string. While JavaScript uses UCS-2 internally,\n   * this function will convert a pair of surrogate halves (each of which\n   * UCS-2 exposes as separate characters) into a single code point,\n   * matching UTF-16.\n   * @see `punycode.ucs2.encode`\n   * @see <https://mathiasbynens.be/notes/javascript-encoding>\n   * @memberOf punycode.ucs2\n   * @name decode\n   * @param {String} string The Unicode input string (UCS-2).\n   * @returns {Array} The new array of code points.\n   */\n  function ucs2decode(string) {\n    var output = [],\n      counter = 0,\n      length = string.length,\n      value,\n      extra;\n    while (counter < length) {\n      value = string.charCodeAt(counter++);\n      if (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n        // high surrogate, and there is a next character\n        extra = string.charCodeAt(counter++);\n        if ((extra & 0xFC00) == 0xDC00) {\n          // low surrogate\n          output.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n        } else {\n          // unmatched surrogate; only append this code unit, in case the next\n          // code unit is the high surrogate of a surrogate pair\n          output.push(value);\n          counter--;\n        }\n      } else {\n        output.push(value);\n      }\n    }\n    return output;\n  }\n\n  /**\n   * Creates a string based on an array of numeric code points.\n   * @see `punycode.ucs2.decode`\n   * @memberOf punycode.ucs2\n   * @name encode\n   * @param {Array} codePoints The array of numeric code points.\n   * @returns {String} The new Unicode string (UCS-2).\n   */\n  function ucs2encode(array) {\n    return map(array, function (value) {\n      var output = '';\n      if (value > 0xFFFF) {\n        value -= 0x10000;\n        output += stringFromCharCode(value >>> 10 & 0x3FF | 0xD800);\n        value = 0xDC00 | value & 0x3FF;\n      }\n      output += stringFromCharCode(value);\n      return output;\n    }).join('');\n  }\n\n  /**\n   * Converts a basic code point into a digit/integer.\n   * @see `digitToBasic()`\n   * @private\n   * @param {Number} codePoint The basic numeric code point value.\n   * @returns {Number} The numeric value of a basic code point (for use in\n   * representing integers) in the range `0` to `base - 1`, or `base` if\n   * the code point does not represent a value.\n   */\n  function basicToDigit(codePoint) {\n    if (codePoint - 48 < 10) {\n      return codePoint - 22;\n    }\n    if (codePoint - 65 < 26) {\n      return codePoint - 65;\n    }\n    if (codePoint - 97 < 26) {\n      return codePoint - 97;\n    }\n    return base;\n  }\n\n  /**\n   * Converts a digit/integer into a basic code point.\n   * @see `basicToDigit()`\n   * @private\n   * @param {Number} digit The numeric value of a basic code point.\n   * @returns {Number} The basic code point whose value (when used for\n   * representing integers) is `digit`, which needs to be in the range\n   * `0` to `base - 1`. If `flag` is non-zero, the uppercase form is\n   * used; else, the lowercase form is used. The behavior is undefined\n   * if `flag` is non-zero and `digit` has no uppercase form.\n   */\n  function digitToBasic(digit, flag) {\n    //  0..25 map to ASCII a..z or A..Z\n    // 26..35 map to ASCII 0..9\n    return digit + 22 + 75 * (digit < 26) - ((flag != 0) << 5);\n  }\n\n  /**\n   * Bias adaptation function as per section 3.4 of RFC 3492.\n   * https://tools.ietf.org/html/rfc3492#section-3.4\n   * @private\n   */\n  function adapt(delta, numPoints, firstTime) {\n    var k = 0;\n    delta = firstTime ? floor(delta / damp) : delta >> 1;\n    delta += floor(delta / numPoints);\n    for /* no initialization */\n    (; delta > baseMinusTMin * tMax >> 1; k += base) {\n      delta = floor(delta / baseMinusTMin);\n    }\n    return floor(k + (baseMinusTMin + 1) * delta / (delta + skew));\n  }\n\n  /**\n   * Converts a Punycode string of ASCII-only symbols to a string of Unicode\n   * symbols.\n   * @memberOf punycode\n   * @param {String} input The Punycode string of ASCII-only symbols.\n   * @returns {String} The resulting string of Unicode symbols.\n   */\n  function decode(input) {\n    // Don't use UCS-2\n    var output = [],\n      inputLength = input.length,\n      out,\n      i = 0,\n      n = initialN,\n      bias = initialBias,\n      basic,\n      j,\n      index,\n      oldi,\n      w,\n      k,\n      digit,\n      t,\n      /** Cached calculation results */\n      baseMinusT;\n\n    // Handle the basic code points: let `basic` be the number of input code\n    // points before the last delimiter, or `0` if there is none, then copy\n    // the first basic code points to the output.\n\n    basic = input.lastIndexOf(delimiter);\n    if (basic < 0) {\n      basic = 0;\n    }\n    for (j = 0; j < basic; ++j) {\n      // if it's not a basic code point\n      if (input.charCodeAt(j) >= 0x80) {\n        error('not-basic');\n      }\n      output.push(input.charCodeAt(j));\n    }\n\n    // Main decoding loop: start just after the last delimiter if any basic code\n    // points were copied; start at the beginning otherwise.\n\n    for /* no final expression */\n    (index = basic > 0 ? basic + 1 : 0; index < inputLength;) {\n      // `index` is the index of the next character to be consumed.\n      // Decode a generalized variable-length integer into `delta`,\n      // which gets added to `i`. The overflow checking is easier\n      // if we increase `i` as we go, then subtract off its starting\n      // value at the end to obtain `delta`.\n      for /* no condition */\n      (oldi = i, w = 1, k = base;; k += base) {\n        if (index >= inputLength) {\n          error('invalid-input');\n        }\n        digit = basicToDigit(input.charCodeAt(index++));\n        if (digit >= base || digit > floor((maxInt - i) / w)) {\n          error('overflow');\n        }\n        i += digit * w;\n        t = k <= bias ? tMin : k >= bias + tMax ? tMax : k - bias;\n        if (digit < t) {\n          break;\n        }\n        baseMinusT = base - t;\n        if (w > floor(maxInt / baseMinusT)) {\n          error('overflow');\n        }\n        w *= baseMinusT;\n      }\n      out = output.length + 1;\n      bias = adapt(i - oldi, out, oldi == 0);\n\n      // `i` was supposed to wrap around from `out` to `0`,\n      // incrementing `n` each time, so we'll fix that now:\n      if (floor(i / out) > maxInt - n) {\n        error('overflow');\n      }\n      n += floor(i / out);\n      i %= out;\n\n      // Insert `n` at position `i` of the output\n      output.splice(i++, 0, n);\n    }\n    return ucs2encode(output);\n  }\n\n  /**\n   * Converts a string of Unicode symbols (e.g. a domain name label) to a\n   * Punycode string of ASCII-only symbols.\n   * @memberOf punycode\n   * @param {String} input The string of Unicode symbols.\n   * @returns {String} The resulting Punycode string of ASCII-only symbols.\n   */\n  function encode(input) {\n    var n,\n      delta,\n      handledCPCount,\n      basicLength,\n      bias,\n      j,\n      m,\n      q,\n      k,\n      t,\n      currentValue,\n      output = [],\n      /** `inputLength` will hold the number of code points in `input`. */\n      inputLength,\n      /** Cached calculation results */\n      handledCPCountPlusOne,\n      baseMinusT,\n      qMinusT;\n\n    // Convert the input in UCS-2 to Unicode\n    input = ucs2decode(input);\n\n    // Cache the length\n    inputLength = input.length;\n\n    // Initialize the state\n    n = initialN;\n    delta = 0;\n    bias = initialBias;\n\n    // Handle the basic code points\n    for (j = 0; j < inputLength; ++j) {\n      currentValue = input[j];\n      if (currentValue < 0x80) {\n        output.push(stringFromCharCode(currentValue));\n      }\n    }\n    handledCPCount = basicLength = output.length;\n\n    // `handledCPCount` is the number of code points that have been handled;\n    // `basicLength` is the number of basic code points.\n\n    // Finish the basic string - if it is not empty - with a delimiter\n    if (basicLength) {\n      output.push(delimiter);\n    }\n\n    // Main encoding loop:\n    while (handledCPCount < inputLength) {\n      // All non-basic code points < n have been handled already. Find the next\n      // larger one:\n      for (m = maxInt, j = 0; j < inputLength; ++j) {\n        currentValue = input[j];\n        if (currentValue >= n && currentValue < m) {\n          m = currentValue;\n        }\n      }\n\n      // Increase `delta` enough to advance the decoder's <n,i> state to <m,0>,\n      // but guard against overflow\n      handledCPCountPlusOne = handledCPCount + 1;\n      if (m - n > floor((maxInt - delta) / handledCPCountPlusOne)) {\n        error('overflow');\n      }\n      delta += (m - n) * handledCPCountPlusOne;\n      n = m;\n      for (j = 0; j < inputLength; ++j) {\n        currentValue = input[j];\n        if (currentValue < n && ++delta > maxInt) {\n          error('overflow');\n        }\n        if (currentValue == n) {\n          // Represent delta as a generalized variable-length integer\n          for /* no condition */\n          (q = delta, k = base;; k += base) {\n            t = k <= bias ? tMin : k >= bias + tMax ? tMax : k - bias;\n            if (q < t) {\n              break;\n            }\n            qMinusT = q - t;\n            baseMinusT = base - t;\n            output.push(stringFromCharCode(digitToBasic(t + qMinusT % baseMinusT, 0)));\n            q = floor(qMinusT / baseMinusT);\n          }\n          output.push(stringFromCharCode(digitToBasic(q, 0)));\n          bias = adapt(delta, handledCPCountPlusOne, handledCPCount == basicLength);\n          delta = 0;\n          ++handledCPCount;\n        }\n      }\n      ++delta;\n      ++n;\n    }\n    return output.join('');\n  }\n\n  /**\n   * Converts a Punycode string representing a domain name or an email address\n   * to Unicode. Only the Punycoded parts of the input will be converted, i.e.\n   * it doesn't matter if you call it on a string that has already been\n   * converted to Unicode.\n   * @memberOf punycode\n   * @param {String} input The Punycoded domain name or email address to\n   * convert to Unicode.\n   * @returns {String} The Unicode representation of the given Punycode\n   * string.\n   */\n  function toUnicode(input) {\n    return mapDomain(input, function (string) {\n      return regexPunycode.test(string) ? decode(string.slice(4).toLowerCase()) : string;\n    });\n  }\n\n  /**\n   * Converts a Unicode string representing a domain name or an email address to\n   * Punycode. Only the non-ASCII parts of the domain name will be converted,\n   * i.e. it doesn't matter if you call it with a domain that's already in\n   * ASCII.\n   * @memberOf punycode\n   * @param {String} input The domain name or email address to convert, as a\n   * Unicode string.\n   * @returns {String} The Punycode representation of the given domain name or\n   * email address.\n   */\n  function toASCII(input) {\n    return mapDomain(input, function (string) {\n      return regexNonASCII.test(string) ? 'xn--' + encode(string) : string;\n    });\n  }\n\n  /*--------------------------------------------------------------------------*/\n\n  /** Define the public API */\n  punycode = {\n    /**\n     * A string representing the current Punycode.js version number.\n     * @memberOf punycode\n     * @type String\n     */\n    'version': '1.4.1',\n    /**\n     * An object of methods to convert from JavaScript's internal character\n     * representation (UCS-2) to Unicode code points, and back.\n     * @see <https://mathiasbynens.be/notes/javascript-encoding>\n     * @memberOf punycode\n     * @type Object\n     */\n    'ucs2': {\n      'decode': ucs2decode,\n      'encode': ucs2encode\n    },\n    'decode': decode,\n    'encode': encode,\n    'toASCII': toASCII,\n    'toUnicode': toUnicode\n  };\n\n  /** Expose `punycode` */\n  // Some AMD build optimizers, like r.js, check for specific condition patterns\n  // like the following:\n  if (typeof define == 'function' && typeof define.amd == 'object' && define.amd) {\n    define('punycode', function () {\n      return punycode;\n    });\n  } else if (freeExports && freeModule) {\n    if (module.exports == freeExports) {\n      // in Node.js, io.js, or RingoJS v0.8.0+\n      freeModule.exports = punycode;\n    } else {\n      // in Narwhal or RingoJS v0.7.0-\n      for (key in punycode) {\n        punycode.hasOwnProperty(key) && (freeExports[key] = punycode[key]);\n      }\n    }\n  } else {\n    // in Rhino or a web browser\n    root.punycode = punycode;\n  }\n})(this);", "map": {"version": 3, "names": ["root", "freeExports", "exports", "nodeType", "freeModule", "module", "freeGlobal", "global", "window", "self", "punycode", "maxInt", "base", "tMin", "tMax", "skew", "damp", "initialBias", "initialN", "delimiter", "regexPunycode", "regexNonASCII", "regexSeparators", "errors", "baseMinusTMin", "floor", "Math", "stringFromCharCode", "String", "fromCharCode", "key", "error", "type", "RangeError", "map", "array", "fn", "length", "result", "mapDomain", "string", "parts", "split", "replace", "labels", "encoded", "join", "ucs2decode", "output", "counter", "value", "extra", "charCodeAt", "push", "ucs2encode", "basicToDigit", "codePoint", "digitToBasic", "digit", "flag", "adapt", "delta", "numPoints", "firstTime", "k", "decode", "input", "inputLength", "out", "i", "n", "bias", "basic", "j", "index", "oldi", "w", "t", "baseMinusT", "lastIndexOf", "splice", "encode", "handledCPCount", "basicLength", "m", "q", "currentValue", "handledCPCountPlusOne", "qMinusT", "toUnicode", "test", "slice", "toLowerCase", "toASCII", "define", "amd", "hasOwnProperty"], "sources": ["C:/Users/<USER>/node_modules/url/node_modules/punycode/punycode.js"], "sourcesContent": ["/*! https://mths.be/punycode v1.4.1 by @mathias */\n;(function(root) {\n\n\t/** Detect free variables */\n\tvar freeExports = typeof exports == 'object' && exports &&\n\t\t!exports.nodeType && exports;\n\tvar freeModule = typeof module == 'object' && module &&\n\t\t!module.nodeType && module;\n\tvar freeGlobal = typeof global == 'object' && global;\n\tif (\n\t\tfreeGlobal.global === freeGlobal ||\n\t\tfreeGlobal.window === freeGlobal ||\n\t\tfreeGlobal.self === freeGlobal\n\t) {\n\t\troot = freeGlobal;\n\t}\n\n\t/**\n\t * The `punycode` object.\n\t * @name punycode\n\t * @type Object\n\t */\n\tvar punycode,\n\n\t/** Highest positive signed 32-bit float value */\n\tmaxInt = 2147483647, // aka. 0x7FFFFFFF or 2^31-1\n\n\t/** Bootstring parameters */\n\tbase = 36,\n\ttMin = 1,\n\ttMax = 26,\n\tskew = 38,\n\tdamp = 700,\n\tinitialBias = 72,\n\tinitialN = 128, // 0x80\n\tdelimiter = '-', // '\\x2D'\n\n\t/** Regular expressions */\n\tregexPunycode = /^xn--/,\n\tregexNonASCII = /[^\\x20-\\x7E]/, // unprintable ASCII chars + non-ASCII chars\n\tregexSeparators = /[\\x2E\\u3002\\uFF0E\\uFF61]/g, // RFC 3490 separators\n\n\t/** Error messages */\n\terrors = {\n\t\t'overflow': 'Overflow: input needs wider integers to process',\n\t\t'not-basic': 'Illegal input >= 0x80 (not a basic code point)',\n\t\t'invalid-input': 'Invalid input'\n\t},\n\n\t/** Convenience shortcuts */\n\tbaseMinusTMin = base - tMin,\n\tfloor = Math.floor,\n\tstringFromCharCode = String.fromCharCode,\n\n\t/** Temporary variable */\n\tkey;\n\n\t/*--------------------------------------------------------------------------*/\n\n\t/**\n\t * A generic error utility function.\n\t * @private\n\t * @param {String} type The error type.\n\t * @returns {Error} Throws a `RangeError` with the applicable error message.\n\t */\n\tfunction error(type) {\n\t\tthrow new RangeError(errors[type]);\n\t}\n\n\t/**\n\t * A generic `Array#map` utility function.\n\t * @private\n\t * @param {Array} array The array to iterate over.\n\t * @param {Function} callback The function that gets called for every array\n\t * item.\n\t * @returns {Array} A new array of values returned by the callback function.\n\t */\n\tfunction map(array, fn) {\n\t\tvar length = array.length;\n\t\tvar result = [];\n\t\twhile (length--) {\n\t\t\tresult[length] = fn(array[length]);\n\t\t}\n\t\treturn result;\n\t}\n\n\t/**\n\t * A simple `Array#map`-like wrapper to work with domain name strings or email\n\t * addresses.\n\t * @private\n\t * @param {String} domain The domain name or email address.\n\t * @param {Function} callback The function that gets called for every\n\t * character.\n\t * @returns {Array} A new string of characters returned by the callback\n\t * function.\n\t */\n\tfunction mapDomain(string, fn) {\n\t\tvar parts = string.split('@');\n\t\tvar result = '';\n\t\tif (parts.length > 1) {\n\t\t\t// In email addresses, only the domain name should be punycoded. Leave\n\t\t\t// the local part (i.e. everything up to `@`) intact.\n\t\t\tresult = parts[0] + '@';\n\t\t\tstring = parts[1];\n\t\t}\n\t\t// Avoid `split(regex)` for IE8 compatibility. See #17.\n\t\tstring = string.replace(regexSeparators, '\\x2E');\n\t\tvar labels = string.split('.');\n\t\tvar encoded = map(labels, fn).join('.');\n\t\treturn result + encoded;\n\t}\n\n\t/**\n\t * Creates an array containing the numeric code points of each Unicode\n\t * character in the string. While JavaScript uses UCS-2 internally,\n\t * this function will convert a pair of surrogate halves (each of which\n\t * UCS-2 exposes as separate characters) into a single code point,\n\t * matching UTF-16.\n\t * @see `punycode.ucs2.encode`\n\t * @see <https://mathiasbynens.be/notes/javascript-encoding>\n\t * @memberOf punycode.ucs2\n\t * @name decode\n\t * @param {String} string The Unicode input string (UCS-2).\n\t * @returns {Array} The new array of code points.\n\t */\n\tfunction ucs2decode(string) {\n\t\tvar output = [],\n\t\t    counter = 0,\n\t\t    length = string.length,\n\t\t    value,\n\t\t    extra;\n\t\twhile (counter < length) {\n\t\t\tvalue = string.charCodeAt(counter++);\n\t\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t\t// high surrogate, and there is a next character\n\t\t\t\textra = string.charCodeAt(counter++);\n\t\t\t\tif ((extra & 0xFC00) == 0xDC00) { // low surrogate\n\t\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t\t} else {\n\t\t\t\t\t// unmatched surrogate; only append this code unit, in case the next\n\t\t\t\t\t// code unit is the high surrogate of a surrogate pair\n\t\t\t\t\toutput.push(value);\n\t\t\t\t\tcounter--;\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\toutput.push(value);\n\t\t\t}\n\t\t}\n\t\treturn output;\n\t}\n\n\t/**\n\t * Creates a string based on an array of numeric code points.\n\t * @see `punycode.ucs2.decode`\n\t * @memberOf punycode.ucs2\n\t * @name encode\n\t * @param {Array} codePoints The array of numeric code points.\n\t * @returns {String} The new Unicode string (UCS-2).\n\t */\n\tfunction ucs2encode(array) {\n\t\treturn map(array, function(value) {\n\t\t\tvar output = '';\n\t\t\tif (value > 0xFFFF) {\n\t\t\t\tvalue -= 0x10000;\n\t\t\t\toutput += stringFromCharCode(value >>> 10 & 0x3FF | 0xD800);\n\t\t\t\tvalue = 0xDC00 | value & 0x3FF;\n\t\t\t}\n\t\t\toutput += stringFromCharCode(value);\n\t\t\treturn output;\n\t\t}).join('');\n\t}\n\n\t/**\n\t * Converts a basic code point into a digit/integer.\n\t * @see `digitToBasic()`\n\t * @private\n\t * @param {Number} codePoint The basic numeric code point value.\n\t * @returns {Number} The numeric value of a basic code point (for use in\n\t * representing integers) in the range `0` to `base - 1`, or `base` if\n\t * the code point does not represent a value.\n\t */\n\tfunction basicToDigit(codePoint) {\n\t\tif (codePoint - 48 < 10) {\n\t\t\treturn codePoint - 22;\n\t\t}\n\t\tif (codePoint - 65 < 26) {\n\t\t\treturn codePoint - 65;\n\t\t}\n\t\tif (codePoint - 97 < 26) {\n\t\t\treturn codePoint - 97;\n\t\t}\n\t\treturn base;\n\t}\n\n\t/**\n\t * Converts a digit/integer into a basic code point.\n\t * @see `basicToDigit()`\n\t * @private\n\t * @param {Number} digit The numeric value of a basic code point.\n\t * @returns {Number} The basic code point whose value (when used for\n\t * representing integers) is `digit`, which needs to be in the range\n\t * `0` to `base - 1`. If `flag` is non-zero, the uppercase form is\n\t * used; else, the lowercase form is used. The behavior is undefined\n\t * if `flag` is non-zero and `digit` has no uppercase form.\n\t */\n\tfunction digitToBasic(digit, flag) {\n\t\t//  0..25 map to ASCII a..z or A..Z\n\t\t// 26..35 map to ASCII 0..9\n\t\treturn digit + 22 + 75 * (digit < 26) - ((flag != 0) << 5);\n\t}\n\n\t/**\n\t * Bias adaptation function as per section 3.4 of RFC 3492.\n\t * https://tools.ietf.org/html/rfc3492#section-3.4\n\t * @private\n\t */\n\tfunction adapt(delta, numPoints, firstTime) {\n\t\tvar k = 0;\n\t\tdelta = firstTime ? floor(delta / damp) : delta >> 1;\n\t\tdelta += floor(delta / numPoints);\n\t\tfor (/* no initialization */; delta > baseMinusTMin * tMax >> 1; k += base) {\n\t\t\tdelta = floor(delta / baseMinusTMin);\n\t\t}\n\t\treturn floor(k + (baseMinusTMin + 1) * delta / (delta + skew));\n\t}\n\n\t/**\n\t * Converts a Punycode string of ASCII-only symbols to a string of Unicode\n\t * symbols.\n\t * @memberOf punycode\n\t * @param {String} input The Punycode string of ASCII-only symbols.\n\t * @returns {String} The resulting string of Unicode symbols.\n\t */\n\tfunction decode(input) {\n\t\t// Don't use UCS-2\n\t\tvar output = [],\n\t\t    inputLength = input.length,\n\t\t    out,\n\t\t    i = 0,\n\t\t    n = initialN,\n\t\t    bias = initialBias,\n\t\t    basic,\n\t\t    j,\n\t\t    index,\n\t\t    oldi,\n\t\t    w,\n\t\t    k,\n\t\t    digit,\n\t\t    t,\n\t\t    /** Cached calculation results */\n\t\t    baseMinusT;\n\n\t\t// Handle the basic code points: let `basic` be the number of input code\n\t\t// points before the last delimiter, or `0` if there is none, then copy\n\t\t// the first basic code points to the output.\n\n\t\tbasic = input.lastIndexOf(delimiter);\n\t\tif (basic < 0) {\n\t\t\tbasic = 0;\n\t\t}\n\n\t\tfor (j = 0; j < basic; ++j) {\n\t\t\t// if it's not a basic code point\n\t\t\tif (input.charCodeAt(j) >= 0x80) {\n\t\t\t\terror('not-basic');\n\t\t\t}\n\t\t\toutput.push(input.charCodeAt(j));\n\t\t}\n\n\t\t// Main decoding loop: start just after the last delimiter if any basic code\n\t\t// points were copied; start at the beginning otherwise.\n\n\t\tfor (index = basic > 0 ? basic + 1 : 0; index < inputLength; /* no final expression */) {\n\n\t\t\t// `index` is the index of the next character to be consumed.\n\t\t\t// Decode a generalized variable-length integer into `delta`,\n\t\t\t// which gets added to `i`. The overflow checking is easier\n\t\t\t// if we increase `i` as we go, then subtract off its starting\n\t\t\t// value at the end to obtain `delta`.\n\t\t\tfor (oldi = i, w = 1, k = base; /* no condition */; k += base) {\n\n\t\t\t\tif (index >= inputLength) {\n\t\t\t\t\terror('invalid-input');\n\t\t\t\t}\n\n\t\t\t\tdigit = basicToDigit(input.charCodeAt(index++));\n\n\t\t\t\tif (digit >= base || digit > floor((maxInt - i) / w)) {\n\t\t\t\t\terror('overflow');\n\t\t\t\t}\n\n\t\t\t\ti += digit * w;\n\t\t\t\tt = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);\n\n\t\t\t\tif (digit < t) {\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\n\t\t\t\tbaseMinusT = base - t;\n\t\t\t\tif (w > floor(maxInt / baseMinusT)) {\n\t\t\t\t\terror('overflow');\n\t\t\t\t}\n\n\t\t\t\tw *= baseMinusT;\n\n\t\t\t}\n\n\t\t\tout = output.length + 1;\n\t\t\tbias = adapt(i - oldi, out, oldi == 0);\n\n\t\t\t// `i` was supposed to wrap around from `out` to `0`,\n\t\t\t// incrementing `n` each time, so we'll fix that now:\n\t\t\tif (floor(i / out) > maxInt - n) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\n\t\t\tn += floor(i / out);\n\t\t\ti %= out;\n\n\t\t\t// Insert `n` at position `i` of the output\n\t\t\toutput.splice(i++, 0, n);\n\n\t\t}\n\n\t\treturn ucs2encode(output);\n\t}\n\n\t/**\n\t * Converts a string of Unicode symbols (e.g. a domain name label) to a\n\t * Punycode string of ASCII-only symbols.\n\t * @memberOf punycode\n\t * @param {String} input The string of Unicode symbols.\n\t * @returns {String} The resulting Punycode string of ASCII-only symbols.\n\t */\n\tfunction encode(input) {\n\t\tvar n,\n\t\t    delta,\n\t\t    handledCPCount,\n\t\t    basicLength,\n\t\t    bias,\n\t\t    j,\n\t\t    m,\n\t\t    q,\n\t\t    k,\n\t\t    t,\n\t\t    currentValue,\n\t\t    output = [],\n\t\t    /** `inputLength` will hold the number of code points in `input`. */\n\t\t    inputLength,\n\t\t    /** Cached calculation results */\n\t\t    handledCPCountPlusOne,\n\t\t    baseMinusT,\n\t\t    qMinusT;\n\n\t\t// Convert the input in UCS-2 to Unicode\n\t\tinput = ucs2decode(input);\n\n\t\t// Cache the length\n\t\tinputLength = input.length;\n\n\t\t// Initialize the state\n\t\tn = initialN;\n\t\tdelta = 0;\n\t\tbias = initialBias;\n\n\t\t// Handle the basic code points\n\t\tfor (j = 0; j < inputLength; ++j) {\n\t\t\tcurrentValue = input[j];\n\t\t\tif (currentValue < 0x80) {\n\t\t\t\toutput.push(stringFromCharCode(currentValue));\n\t\t\t}\n\t\t}\n\n\t\thandledCPCount = basicLength = output.length;\n\n\t\t// `handledCPCount` is the number of code points that have been handled;\n\t\t// `basicLength` is the number of basic code points.\n\n\t\t// Finish the basic string - if it is not empty - with a delimiter\n\t\tif (basicLength) {\n\t\t\toutput.push(delimiter);\n\t\t}\n\n\t\t// Main encoding loop:\n\t\twhile (handledCPCount < inputLength) {\n\n\t\t\t// All non-basic code points < n have been handled already. Find the next\n\t\t\t// larger one:\n\t\t\tfor (m = maxInt, j = 0; j < inputLength; ++j) {\n\t\t\t\tcurrentValue = input[j];\n\t\t\t\tif (currentValue >= n && currentValue < m) {\n\t\t\t\t\tm = currentValue;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t// Increase `delta` enough to advance the decoder's <n,i> state to <m,0>,\n\t\t\t// but guard against overflow\n\t\t\thandledCPCountPlusOne = handledCPCount + 1;\n\t\t\tif (m - n > floor((maxInt - delta) / handledCPCountPlusOne)) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\n\t\t\tdelta += (m - n) * handledCPCountPlusOne;\n\t\t\tn = m;\n\n\t\t\tfor (j = 0; j < inputLength; ++j) {\n\t\t\t\tcurrentValue = input[j];\n\n\t\t\t\tif (currentValue < n && ++delta > maxInt) {\n\t\t\t\t\terror('overflow');\n\t\t\t\t}\n\n\t\t\t\tif (currentValue == n) {\n\t\t\t\t\t// Represent delta as a generalized variable-length integer\n\t\t\t\t\tfor (q = delta, k = base; /* no condition */; k += base) {\n\t\t\t\t\t\tt = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);\n\t\t\t\t\t\tif (q < t) {\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t\tqMinusT = q - t;\n\t\t\t\t\t\tbaseMinusT = base - t;\n\t\t\t\t\t\toutput.push(\n\t\t\t\t\t\t\tstringFromCharCode(digitToBasic(t + qMinusT % baseMinusT, 0))\n\t\t\t\t\t\t);\n\t\t\t\t\t\tq = floor(qMinusT / baseMinusT);\n\t\t\t\t\t}\n\n\t\t\t\t\toutput.push(stringFromCharCode(digitToBasic(q, 0)));\n\t\t\t\t\tbias = adapt(delta, handledCPCountPlusOne, handledCPCount == basicLength);\n\t\t\t\t\tdelta = 0;\n\t\t\t\t\t++handledCPCount;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t++delta;\n\t\t\t++n;\n\n\t\t}\n\t\treturn output.join('');\n\t}\n\n\t/**\n\t * Converts a Punycode string representing a domain name or an email address\n\t * to Unicode. Only the Punycoded parts of the input will be converted, i.e.\n\t * it doesn't matter if you call it on a string that has already been\n\t * converted to Unicode.\n\t * @memberOf punycode\n\t * @param {String} input The Punycoded domain name or email address to\n\t * convert to Unicode.\n\t * @returns {String} The Unicode representation of the given Punycode\n\t * string.\n\t */\n\tfunction toUnicode(input) {\n\t\treturn mapDomain(input, function(string) {\n\t\t\treturn regexPunycode.test(string)\n\t\t\t\t? decode(string.slice(4).toLowerCase())\n\t\t\t\t: string;\n\t\t});\n\t}\n\n\t/**\n\t * Converts a Unicode string representing a domain name or an email address to\n\t * Punycode. Only the non-ASCII parts of the domain name will be converted,\n\t * i.e. it doesn't matter if you call it with a domain that's already in\n\t * ASCII.\n\t * @memberOf punycode\n\t * @param {String} input The domain name or email address to convert, as a\n\t * Unicode string.\n\t * @returns {String} The Punycode representation of the given domain name or\n\t * email address.\n\t */\n\tfunction toASCII(input) {\n\t\treturn mapDomain(input, function(string) {\n\t\t\treturn regexNonASCII.test(string)\n\t\t\t\t? 'xn--' + encode(string)\n\t\t\t\t: string;\n\t\t});\n\t}\n\n\t/*--------------------------------------------------------------------------*/\n\n\t/** Define the public API */\n\tpunycode = {\n\t\t/**\n\t\t * A string representing the current Punycode.js version number.\n\t\t * @memberOf punycode\n\t\t * @type String\n\t\t */\n\t\t'version': '1.4.1',\n\t\t/**\n\t\t * An object of methods to convert from JavaScript's internal character\n\t\t * representation (UCS-2) to Unicode code points, and back.\n\t\t * @see <https://mathiasbynens.be/notes/javascript-encoding>\n\t\t * @memberOf punycode\n\t\t * @type Object\n\t\t */\n\t\t'ucs2': {\n\t\t\t'decode': ucs2decode,\n\t\t\t'encode': ucs2encode\n\t\t},\n\t\t'decode': decode,\n\t\t'encode': encode,\n\t\t'toASCII': toASCII,\n\t\t'toUnicode': toUnicode\n\t};\n\n\t/** Expose `punycode` */\n\t// Some AMD build optimizers, like r.js, check for specific condition patterns\n\t// like the following:\n\tif (\n\t\ttypeof define == 'function' &&\n\t\ttypeof define.amd == 'object' &&\n\t\tdefine.amd\n\t) {\n\t\tdefine('punycode', function() {\n\t\t\treturn punycode;\n\t\t});\n\t} else if (freeExports && freeModule) {\n\t\tif (module.exports == freeExports) {\n\t\t\t// in Node.js, io.js, or RingoJS v0.8.0+\n\t\t\tfreeModule.exports = punycode;\n\t\t} else {\n\t\t\t// in Narwhal or RingoJS v0.7.0-\n\t\t\tfor (key in punycode) {\n\t\t\t\tpunycode.hasOwnProperty(key) && (freeExports[key] = punycode[key]);\n\t\t\t}\n\t\t}\n\t} else {\n\t\t// in Rhino or a web browser\n\t\troot.punycode = punycode;\n\t}\n\n}(this));\n"], "mappings": "AAAA;AACA;AAAE,WAASA,IAAI,EAAE;EAEhB;EACA,IAAIC,WAAW,GAAG,OAAOC,OAAO,IAAI,QAAQ,IAAIA,OAAO,IACtD,CAACA,OAAO,CAACC,QAAQ,IAAID,OAAO;EAC7B,IAAIE,UAAU,GAAG,OAAOC,MAAM,IAAI,QAAQ,IAAIA,MAAM,IACnD,CAACA,MAAM,CAACF,QAAQ,IAAIE,MAAM;EAC3B,IAAIC,UAAU,GAAG,OAAOC,MAAM,IAAI,QAAQ,IAAIA,MAAM;EACpD,IACCD,UAAU,CAACC,MAAM,KAAKD,UAAU,IAChCA,UAAU,CAACE,MAAM,KAAKF,UAAU,IAChCA,UAAU,CAACG,IAAI,KAAKH,UAAU,EAC7B;IACDN,IAAI,GAAGM,UAAU;EAClB;;EAEA;AACD;AACA;AACA;AACA;EACC,IAAII,QAAQ;IAEZ;IACAC,MAAM,GAAG,UAAU;IAAE;;IAErB;IACAC,IAAI,GAAG,EAAE;IACTC,IAAI,GAAG,CAAC;IACRC,IAAI,GAAG,EAAE;IACTC,IAAI,GAAG,EAAE;IACTC,IAAI,GAAG,GAAG;IACVC,WAAW,GAAG,EAAE;IAChBC,QAAQ,GAAG,GAAG;IAAE;IAChBC,SAAS,GAAG,GAAG;IAAE;;IAEjB;IACAC,aAAa,GAAG,OAAO;IACvBC,aAAa,GAAG,cAAc;IAAE;IAChCC,eAAe,GAAG,2BAA2B;IAAE;;IAE/C;IACAC,MAAM,GAAG;MACR,UAAU,EAAE,iDAAiD;MAC7D,WAAW,EAAE,gDAAgD;MAC7D,eAAe,EAAE;IAClB,CAAC;IAED;IACAC,aAAa,GAAGZ,IAAI,GAAGC,IAAI;IAC3BY,KAAK,GAAGC,IAAI,CAACD,KAAK;IAClBE,kBAAkB,GAAGC,MAAM,CAACC,YAAY;IAExC;IACAC,GAAG;;EAEH;;EAEA;AACD;AACA;AACA;AACA;AACA;EACC,SAASC,KAAKA,CAACC,IAAI,EAAE;IACpB,MAAM,IAAIC,UAAU,CAACV,MAAM,CAACS,IAAI,CAAC,CAAC;EACnC;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASE,GAAGA,CAACC,KAAK,EAAEC,EAAE,EAAE;IACvB,IAAIC,MAAM,GAAGF,KAAK,CAACE,MAAM;IACzB,IAAIC,MAAM,GAAG,EAAE;IACf,OAAOD,MAAM,EAAE,EAAE;MAChBC,MAAM,CAACD,MAAM,CAAC,GAAGD,EAAE,CAACD,KAAK,CAACE,MAAM,CAAC,CAAC;IACnC;IACA,OAAOC,MAAM;EACd;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASC,SAASA,CAACC,MAAM,EAAEJ,EAAE,EAAE;IAC9B,IAAIK,KAAK,GAAGD,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC;IAC7B,IAAIJ,MAAM,GAAG,EAAE;IACf,IAAIG,KAAK,CAACJ,MAAM,GAAG,CAAC,EAAE;MACrB;MACA;MACAC,MAAM,GAAGG,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG;MACvBD,MAAM,GAAGC,KAAK,CAAC,CAAC,CAAC;IAClB;IACA;IACAD,MAAM,GAAGA,MAAM,CAACG,OAAO,CAACrB,eAAe,EAAE,MAAM,CAAC;IAChD,IAAIsB,MAAM,GAAGJ,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC;IAC9B,IAAIG,OAAO,GAAGX,GAAG,CAACU,MAAM,EAAER,EAAE,CAAC,CAACU,IAAI,CAAC,GAAG,CAAC;IACvC,OAAOR,MAAM,GAAGO,OAAO;EACxB;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASE,UAAUA,CAACP,MAAM,EAAE;IAC3B,IAAIQ,MAAM,GAAG,EAAE;MACXC,OAAO,GAAG,CAAC;MACXZ,MAAM,GAAGG,MAAM,CAACH,MAAM;MACtBa,KAAK;MACLC,KAAK;IACT,OAAOF,OAAO,GAAGZ,MAAM,EAAE;MACxBa,KAAK,GAAGV,MAAM,CAACY,UAAU,CAACH,OAAO,EAAE,CAAC;MACpC,IAAIC,KAAK,IAAI,MAAM,IAAIA,KAAK,IAAI,MAAM,IAAID,OAAO,GAAGZ,MAAM,EAAE;QAC3D;QACAc,KAAK,GAAGX,MAAM,CAACY,UAAU,CAACH,OAAO,EAAE,CAAC;QACpC,IAAI,CAACE,KAAK,GAAG,MAAM,KAAK,MAAM,EAAE;UAAE;UACjCH,MAAM,CAACK,IAAI,CAAC,CAAC,CAACH,KAAK,GAAG,KAAK,KAAK,EAAE,KAAKC,KAAK,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC;QACjE,CAAC,MAAM;UACN;UACA;UACAH,MAAM,CAACK,IAAI,CAACH,KAAK,CAAC;UAClBD,OAAO,EAAE;QACV;MACD,CAAC,MAAM;QACND,MAAM,CAACK,IAAI,CAACH,KAAK,CAAC;MACnB;IACD;IACA,OAAOF,MAAM;EACd;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASM,UAAUA,CAACnB,KAAK,EAAE;IAC1B,OAAOD,GAAG,CAACC,KAAK,EAAE,UAASe,KAAK,EAAE;MACjC,IAAIF,MAAM,GAAG,EAAE;MACf,IAAIE,KAAK,GAAG,MAAM,EAAE;QACnBA,KAAK,IAAI,OAAO;QAChBF,MAAM,IAAIrB,kBAAkB,CAACuB,KAAK,KAAK,EAAE,GAAG,KAAK,GAAG,MAAM,CAAC;QAC3DA,KAAK,GAAG,MAAM,GAAGA,KAAK,GAAG,KAAK;MAC/B;MACAF,MAAM,IAAIrB,kBAAkB,CAACuB,KAAK,CAAC;MACnC,OAAOF,MAAM;IACd,CAAC,CAAC,CAACF,IAAI,CAAC,EAAE,CAAC;EACZ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASS,YAAYA,CAACC,SAAS,EAAE;IAChC,IAAIA,SAAS,GAAG,EAAE,GAAG,EAAE,EAAE;MACxB,OAAOA,SAAS,GAAG,EAAE;IACtB;IACA,IAAIA,SAAS,GAAG,EAAE,GAAG,EAAE,EAAE;MACxB,OAAOA,SAAS,GAAG,EAAE;IACtB;IACA,IAAIA,SAAS,GAAG,EAAE,GAAG,EAAE,EAAE;MACxB,OAAOA,SAAS,GAAG,EAAE;IACtB;IACA,OAAO5C,IAAI;EACZ;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAAS6C,YAAYA,CAACC,KAAK,EAAEC,IAAI,EAAE;IAClC;IACA;IACA,OAAOD,KAAK,GAAG,EAAE,GAAG,EAAE,IAAIA,KAAK,GAAG,EAAE,CAAC,IAAI,CAACC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;EAC3D;;EAEA;AACD;AACA;AACA;AACA;EACC,SAASC,KAAKA,CAACC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE;IAC3C,IAAIC,CAAC,GAAG,CAAC;IACTH,KAAK,GAAGE,SAAS,GAAGtC,KAAK,CAACoC,KAAK,GAAG7C,IAAI,CAAC,GAAG6C,KAAK,IAAI,CAAC;IACpDA,KAAK,IAAIpC,KAAK,CAACoC,KAAK,GAAGC,SAAS,CAAC;IACjC,IAAK;IAAA,GAAyBD,KAAK,GAAGrC,aAAa,GAAGV,IAAI,IAAI,CAAC,EAAEkD,CAAC,IAAIpD,IAAI,EAAE;MAC3EiD,KAAK,GAAGpC,KAAK,CAACoC,KAAK,GAAGrC,aAAa,CAAC;IACrC;IACA,OAAOC,KAAK,CAACuC,CAAC,GAAG,CAACxC,aAAa,GAAG,CAAC,IAAIqC,KAAK,IAAIA,KAAK,GAAG9C,IAAI,CAAC,CAAC;EAC/D;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACC,SAASkD,MAAMA,CAACC,KAAK,EAAE;IACtB;IACA,IAAIlB,MAAM,GAAG,EAAE;MACXmB,WAAW,GAAGD,KAAK,CAAC7B,MAAM;MAC1B+B,GAAG;MACHC,CAAC,GAAG,CAAC;MACLC,CAAC,GAAGpD,QAAQ;MACZqD,IAAI,GAAGtD,WAAW;MAClBuD,KAAK;MACLC,CAAC;MACDC,KAAK;MACLC,IAAI;MACJC,CAAC;MACDZ,CAAC;MACDN,KAAK;MACLmB,CAAC;MACD;MACAC,UAAU;;IAEd;IACA;IACA;;IAEAN,KAAK,GAAGN,KAAK,CAACa,WAAW,CAAC5D,SAAS,CAAC;IACpC,IAAIqD,KAAK,GAAG,CAAC,EAAE;MACdA,KAAK,GAAG,CAAC;IACV;IAEA,KAAKC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,KAAK,EAAE,EAAEC,CAAC,EAAE;MAC3B;MACA,IAAIP,KAAK,CAACd,UAAU,CAACqB,CAAC,CAAC,IAAI,IAAI,EAAE;QAChC1C,KAAK,CAAC,WAAW,CAAC;MACnB;MACAiB,MAAM,CAACK,IAAI,CAACa,KAAK,CAACd,UAAU,CAACqB,CAAC,CAAC,CAAC;IACjC;;IAEA;IACA;;IAEA,IAA6D;IAAA,CAAxDC,KAAK,GAAGF,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,EAAEE,KAAK,GAAGP,WAAW,GAA6B;MAEvF;MACA;MACA;MACA;MACA;MACA,IAAgC;MAAA,CAA3BQ,IAAI,GAAGN,CAAC,EAAEO,CAAC,GAAG,CAAC,EAAEZ,CAAC,GAAGpD,IAAI,GAAsBoD,CAAC,IAAIpD,IAAI,EAAE;QAE9D,IAAI8D,KAAK,IAAIP,WAAW,EAAE;UACzBpC,KAAK,CAAC,eAAe,CAAC;QACvB;QAEA2B,KAAK,GAAGH,YAAY,CAACW,KAAK,CAACd,UAAU,CAACsB,KAAK,EAAE,CAAC,CAAC;QAE/C,IAAIhB,KAAK,IAAI9C,IAAI,IAAI8C,KAAK,GAAGjC,KAAK,CAAC,CAACd,MAAM,GAAG0D,CAAC,IAAIO,CAAC,CAAC,EAAE;UACrD7C,KAAK,CAAC,UAAU,CAAC;QAClB;QAEAsC,CAAC,IAAIX,KAAK,GAAGkB,CAAC;QACdC,CAAC,GAAGb,CAAC,IAAIO,IAAI,GAAG1D,IAAI,GAAImD,CAAC,IAAIO,IAAI,GAAGzD,IAAI,GAAGA,IAAI,GAAGkD,CAAC,GAAGO,IAAK;QAE3D,IAAIb,KAAK,GAAGmB,CAAC,EAAE;UACd;QACD;QAEAC,UAAU,GAAGlE,IAAI,GAAGiE,CAAC;QACrB,IAAID,CAAC,GAAGnD,KAAK,CAACd,MAAM,GAAGmE,UAAU,CAAC,EAAE;UACnC/C,KAAK,CAAC,UAAU,CAAC;QAClB;QAEA6C,CAAC,IAAIE,UAAU;MAEhB;MAEAV,GAAG,GAAGpB,MAAM,CAACX,MAAM,GAAG,CAAC;MACvBkC,IAAI,GAAGX,KAAK,CAACS,CAAC,GAAGM,IAAI,EAAEP,GAAG,EAAEO,IAAI,IAAI,CAAC,CAAC;;MAEtC;MACA;MACA,IAAIlD,KAAK,CAAC4C,CAAC,GAAGD,GAAG,CAAC,GAAGzD,MAAM,GAAG2D,CAAC,EAAE;QAChCvC,KAAK,CAAC,UAAU,CAAC;MAClB;MAEAuC,CAAC,IAAI7C,KAAK,CAAC4C,CAAC,GAAGD,GAAG,CAAC;MACnBC,CAAC,IAAID,GAAG;;MAER;MACApB,MAAM,CAACgC,MAAM,CAACX,CAAC,EAAE,EAAE,CAAC,EAAEC,CAAC,CAAC;IAEzB;IAEA,OAAOhB,UAAU,CAACN,MAAM,CAAC;EAC1B;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;EACC,SAASiC,MAAMA,CAACf,KAAK,EAAE;IACtB,IAAII,CAAC;MACDT,KAAK;MACLqB,cAAc;MACdC,WAAW;MACXZ,IAAI;MACJE,CAAC;MACDW,CAAC;MACDC,CAAC;MACDrB,CAAC;MACDa,CAAC;MACDS,YAAY;MACZtC,MAAM,GAAG,EAAE;MACX;MACAmB,WAAW;MACX;MACAoB,qBAAqB;MACrBT,UAAU;MACVU,OAAO;;IAEX;IACAtB,KAAK,GAAGnB,UAAU,CAACmB,KAAK,CAAC;;IAEzB;IACAC,WAAW,GAAGD,KAAK,CAAC7B,MAAM;;IAE1B;IACAiC,CAAC,GAAGpD,QAAQ;IACZ2C,KAAK,GAAG,CAAC;IACTU,IAAI,GAAGtD,WAAW;;IAElB;IACA,KAAKwD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,WAAW,EAAE,EAAEM,CAAC,EAAE;MACjCa,YAAY,GAAGpB,KAAK,CAACO,CAAC,CAAC;MACvB,IAAIa,YAAY,GAAG,IAAI,EAAE;QACxBtC,MAAM,CAACK,IAAI,CAAC1B,kBAAkB,CAAC2D,YAAY,CAAC,CAAC;MAC9C;IACD;IAEAJ,cAAc,GAAGC,WAAW,GAAGnC,MAAM,CAACX,MAAM;;IAE5C;IACA;;IAEA;IACA,IAAI8C,WAAW,EAAE;MAChBnC,MAAM,CAACK,IAAI,CAAClC,SAAS,CAAC;IACvB;;IAEA;IACA,OAAO+D,cAAc,GAAGf,WAAW,EAAE;MAEpC;MACA;MACA,KAAKiB,CAAC,GAAGzE,MAAM,EAAE8D,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,WAAW,EAAE,EAAEM,CAAC,EAAE;QAC7Ca,YAAY,GAAGpB,KAAK,CAACO,CAAC,CAAC;QACvB,IAAIa,YAAY,IAAIhB,CAAC,IAAIgB,YAAY,GAAGF,CAAC,EAAE;UAC1CA,CAAC,GAAGE,YAAY;QACjB;MACD;;MAEA;MACA;MACAC,qBAAqB,GAAGL,cAAc,GAAG,CAAC;MAC1C,IAAIE,CAAC,GAAGd,CAAC,GAAG7C,KAAK,CAAC,CAACd,MAAM,GAAGkD,KAAK,IAAI0B,qBAAqB,CAAC,EAAE;QAC5DxD,KAAK,CAAC,UAAU,CAAC;MAClB;MAEA8B,KAAK,IAAI,CAACuB,CAAC,GAAGd,CAAC,IAAIiB,qBAAqB;MACxCjB,CAAC,GAAGc,CAAC;MAEL,KAAKX,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGN,WAAW,EAAE,EAAEM,CAAC,EAAE;QACjCa,YAAY,GAAGpB,KAAK,CAACO,CAAC,CAAC;QAEvB,IAAIa,YAAY,GAAGhB,CAAC,IAAI,EAAET,KAAK,GAAGlD,MAAM,EAAE;UACzCoB,KAAK,CAAC,UAAU,CAAC;QAClB;QAEA,IAAIuD,YAAY,IAAIhB,CAAC,EAAE;UACtB;UACA,IAA0B;UAAA,CAArBe,CAAC,GAAGxB,KAAK,EAAEG,CAAC,GAAGpD,IAAI,GAAsBoD,CAAC,IAAIpD,IAAI,EAAE;YACxDiE,CAAC,GAAGb,CAAC,IAAIO,IAAI,GAAG1D,IAAI,GAAImD,CAAC,IAAIO,IAAI,GAAGzD,IAAI,GAAGA,IAAI,GAAGkD,CAAC,GAAGO,IAAK;YAC3D,IAAIc,CAAC,GAAGR,CAAC,EAAE;cACV;YACD;YACAW,OAAO,GAAGH,CAAC,GAAGR,CAAC;YACfC,UAAU,GAAGlE,IAAI,GAAGiE,CAAC;YACrB7B,MAAM,CAACK,IAAI,CACV1B,kBAAkB,CAAC8B,YAAY,CAACoB,CAAC,GAAGW,OAAO,GAAGV,UAAU,EAAE,CAAC,CAAC,CAC7D,CAAC;YACDO,CAAC,GAAG5D,KAAK,CAAC+D,OAAO,GAAGV,UAAU,CAAC;UAChC;UAEA9B,MAAM,CAACK,IAAI,CAAC1B,kBAAkB,CAAC8B,YAAY,CAAC4B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;UACnDd,IAAI,GAAGX,KAAK,CAACC,KAAK,EAAE0B,qBAAqB,EAAEL,cAAc,IAAIC,WAAW,CAAC;UACzEtB,KAAK,GAAG,CAAC;UACT,EAAEqB,cAAc;QACjB;MACD;MAEA,EAAErB,KAAK;MACP,EAAES,CAAC;IAEJ;IACA,OAAOtB,MAAM,CAACF,IAAI,CAAC,EAAE,CAAC;EACvB;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAAS2C,SAASA,CAACvB,KAAK,EAAE;IACzB,OAAO3B,SAAS,CAAC2B,KAAK,EAAE,UAAS1B,MAAM,EAAE;MACxC,OAAOpB,aAAa,CAACsE,IAAI,CAAClD,MAAM,CAAC,GAC9ByB,MAAM,CAACzB,MAAM,CAACmD,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,GACrCpD,MAAM;IACV,CAAC,CAAC;EACH;;EAEA;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACC,SAASqD,OAAOA,CAAC3B,KAAK,EAAE;IACvB,OAAO3B,SAAS,CAAC2B,KAAK,EAAE,UAAS1B,MAAM,EAAE;MACxC,OAAOnB,aAAa,CAACqE,IAAI,CAAClD,MAAM,CAAC,GAC9B,MAAM,GAAGyC,MAAM,CAACzC,MAAM,CAAC,GACvBA,MAAM;IACV,CAAC,CAAC;EACH;;EAEA;;EAEA;EACA9B,QAAQ,GAAG;IACV;AACF;AACA;AACA;AACA;IACE,SAAS,EAAE,OAAO;IAClB;AACF;AACA;AACA;AACA;AACA;AACA;IACE,MAAM,EAAE;MACP,QAAQ,EAAEqC,UAAU;MACpB,QAAQ,EAAEO;IACX,CAAC;IACD,QAAQ,EAAEW,MAAM;IAChB,QAAQ,EAAEgB,MAAM;IAChB,SAAS,EAAEY,OAAO;IAClB,WAAW,EAAEJ;EACd,CAAC;;EAED;EACA;EACA;EACA,IACC,OAAOK,MAAM,IAAI,UAAU,IAC3B,OAAOA,MAAM,CAACC,GAAG,IAAI,QAAQ,IAC7BD,MAAM,CAACC,GAAG,EACT;IACDD,MAAM,CAAC,UAAU,EAAE,YAAW;MAC7B,OAAOpF,QAAQ;IAChB,CAAC,CAAC;EACH,CAAC,MAAM,IAAIT,WAAW,IAAIG,UAAU,EAAE;IACrC,IAAIC,MAAM,CAACH,OAAO,IAAID,WAAW,EAAE;MAClC;MACAG,UAAU,CAACF,OAAO,GAAGQ,QAAQ;IAC9B,CAAC,MAAM;MACN;MACA,KAAKoB,GAAG,IAAIpB,QAAQ,EAAE;QACrBA,QAAQ,CAACsF,cAAc,CAAClE,GAAG,CAAC,KAAK7B,WAAW,CAAC6B,GAAG,CAAC,GAAGpB,QAAQ,CAACoB,GAAG,CAAC,CAAC;MACnE;IACD;EACD,CAAC,MAAM;IACN;IACA9B,IAAI,CAACU,QAAQ,GAAGA,QAAQ;EACzB;AAED,CAAC,EAAC,IAAI,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}