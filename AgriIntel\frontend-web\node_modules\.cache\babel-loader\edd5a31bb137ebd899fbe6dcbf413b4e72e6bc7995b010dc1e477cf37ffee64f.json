{"ast": null, "code": "import React from 'react';\nvar getItem = function getItem(key) {\n  try {\n    var itemValue = localStorage.getItem(key);\n    if (typeof itemValue === 'string') {\n      return JSON.parse(itemValue);\n    }\n    return undefined;\n  } catch (_unused) {\n    return undefined;\n  }\n};\nexport default function useLocalStorage(key, defaultValue) {\n  var _React$useState = React.useState(),\n    value = _React$useState[0],\n    setValue = _React$useState[1];\n  React.useEffect(function () {\n    var initialValue = getItem(key);\n    if (typeof initialValue === 'undefined' || initialValue === null) {\n      setValue(typeof defaultValue === 'function' ? defaultValue() : defaultValue);\n    } else {\n      setValue(initialValue);\n    }\n  }, [defaultValue, key]);\n  var setter = React.useCallback(function (updater) {\n    setValue(function (old) {\n      var newVal = updater;\n      if (typeof updater == 'function') {\n        newVal = updater(old);\n      }\n      try {\n        localStorage.setItem(key, JSON.stringify(newVal));\n      } catch (_unused2) {}\n      return newVal;\n    });\n  }, [key]);\n  return [value, setter];\n}", "map": {"version": 3, "names": ["React", "getItem", "key", "itemValue", "localStorage", "JSON", "parse", "undefined", "_unused", "useLocalStorage", "defaultValue", "_React$useState", "useState", "value", "setValue", "useEffect", "initialValue", "setter", "useCallback", "updater", "old", "newVal", "setItem", "stringify", "_unused2"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-query/es/devtools/useLocalStorage.js"], "sourcesContent": ["import React from 'react';\n\nvar getItem = function getItem(key) {\n  try {\n    var itemValue = localStorage.getItem(key);\n\n    if (typeof itemValue === 'string') {\n      return JSON.parse(itemValue);\n    }\n\n    return undefined;\n  } catch (_unused) {\n    return undefined;\n  }\n};\n\nexport default function useLocalStorage(key, defaultValue) {\n  var _React$useState = React.useState(),\n      value = _React$useState[0],\n      setValue = _React$useState[1];\n\n  React.useEffect(function () {\n    var initialValue = getItem(key);\n\n    if (typeof initialValue === 'undefined' || initialValue === null) {\n      setValue(typeof defaultValue === 'function' ? defaultValue() : defaultValue);\n    } else {\n      setValue(initialValue);\n    }\n  }, [defaultValue, key]);\n  var setter = React.useCallback(function (updater) {\n    setValue(function (old) {\n      var newVal = updater;\n\n      if (typeof updater == 'function') {\n        newVal = updater(old);\n      }\n\n      try {\n        localStorage.setItem(key, JSON.stringify(newVal));\n      } catch (_unused2) {}\n\n      return newVal;\n    });\n  }, [key]);\n  return [value, setter];\n}"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAEzB,IAAIC,OAAO,GAAG,SAASA,OAAOA,CAACC,GAAG,EAAE;EAClC,IAAI;IACF,IAAIC,SAAS,GAAGC,YAAY,CAACH,OAAO,CAACC,GAAG,CAAC;IAEzC,IAAI,OAAOC,SAAS,KAAK,QAAQ,EAAE;MACjC,OAAOE,IAAI,CAACC,KAAK,CAACH,SAAS,CAAC;IAC9B;IAEA,OAAOI,SAAS;EAClB,CAAC,CAAC,OAAOC,OAAO,EAAE;IAChB,OAAOD,SAAS;EAClB;AACF,CAAC;AAED,eAAe,SAASE,eAAeA,CAACP,GAAG,EAAEQ,YAAY,EAAE;EACzD,IAAIC,eAAe,GAAGX,KAAK,CAACY,QAAQ,CAAC,CAAC;IAClCC,KAAK,GAAGF,eAAe,CAAC,CAAC,CAAC;IAC1BG,QAAQ,GAAGH,eAAe,CAAC,CAAC,CAAC;EAEjCX,KAAK,CAACe,SAAS,CAAC,YAAY;IAC1B,IAAIC,YAAY,GAAGf,OAAO,CAACC,GAAG,CAAC;IAE/B,IAAI,OAAOc,YAAY,KAAK,WAAW,IAAIA,YAAY,KAAK,IAAI,EAAE;MAChEF,QAAQ,CAAC,OAAOJ,YAAY,KAAK,UAAU,GAAGA,YAAY,CAAC,CAAC,GAAGA,YAAY,CAAC;IAC9E,CAAC,MAAM;MACLI,QAAQ,CAACE,YAAY,CAAC;IACxB;EACF,CAAC,EAAE,CAACN,YAAY,EAAER,GAAG,CAAC,CAAC;EACvB,IAAIe,MAAM,GAAGjB,KAAK,CAACkB,WAAW,CAAC,UAAUC,OAAO,EAAE;IAChDL,QAAQ,CAAC,UAAUM,GAAG,EAAE;MACtB,IAAIC,MAAM,GAAGF,OAAO;MAEpB,IAAI,OAAOA,OAAO,IAAI,UAAU,EAAE;QAChCE,MAAM,GAAGF,OAAO,CAACC,GAAG,CAAC;MACvB;MAEA,IAAI;QACFhB,YAAY,CAACkB,OAAO,CAACpB,GAAG,EAAEG,IAAI,CAACkB,SAAS,CAACF,MAAM,CAAC,CAAC;MACnD,CAAC,CAAC,OAAOG,QAAQ,EAAE,CAAC;MAEpB,OAAOH,MAAM;IACf,CAAC,CAAC;EACJ,CAAC,EAAE,CAACnB,GAAG,CAAC,CAAC;EACT,OAAO,CAACW,KAAK,EAAEI,MAAM,CAAC;AACxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}