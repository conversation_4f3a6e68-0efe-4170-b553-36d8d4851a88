import { configureStore } from '@reduxjs/toolkit';
import authReducer from './slices/authSlice';
import animalReducer from './slices/animalSlice';
import healthReducer from './slices/healthSlice';
import breedingReducer from './slices/breedingSlice';
import uiReducer from './slices/uiSlice';

export const store = configureStore({
  reducer: {
    auth: authReducer,
    animals: animalReducer,
    health: healthReducer,
    breeding: breedingReducer,
    ui: uiReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
