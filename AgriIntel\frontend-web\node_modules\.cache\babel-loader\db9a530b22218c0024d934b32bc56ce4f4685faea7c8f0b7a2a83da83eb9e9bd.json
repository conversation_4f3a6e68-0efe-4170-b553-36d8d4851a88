{"ast": null, "code": "'use client';\n\nimport _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nexport { default as THEME_ID } from './identifier';\nexport { default as adaptV4Theme } from './adaptV4Theme';\nexport { hexToRgb, rgbToHex, hslToRgb, decomposeColor, recomposeColor, getContrastRatio, getLuminance, emphasize, alpha, darken, lighten, css, keyframes } from '@mui/system';\n// TODO: Remove this function in v6.\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function experimental_sx() {\n  throw new Error(process.env.NODE_ENV !== \"production\" ? \"MUI: The `experimental_sx` has been moved to `theme.unstable_sx`.For more details, see https://github.com/mui/material-ui/pull/35150.\" : _formatMuiErrorMessage(20));\n}\nexport { default as createTheme, createMuiTheme } from './createTheme';\nexport { default as unstable_createMuiStrictModeTheme } from './createMuiStrictModeTheme';\nexport { default as createStyles } from './createStyles';\nexport { getUnit as unstable_getUnit, toUnitless as unstable_toUnitless } from './cssUtils';\nexport { default as responsiveFontSizes } from './responsiveFontSizes';\nexport { duration, easing } from './createTransitions';\nexport { default as useTheme } from './useTheme';\nexport { default as useThemeProps } from './useThemeProps';\nexport { default as styled } from './styled';\nexport { default as experimentalStyled } from './styled';\nexport { default as ThemeProvider } from './ThemeProvider';\nexport { StyledEngineProvider } from '@mui/system';\n// The legacy utilities from @mui/styles\n// These are just empty functions that throws when invoked\nexport { default as makeStyles } from './makeStyles';\nexport { default as withStyles } from './withStyles';\nexport { default as withTheme } from './withTheme';\nexport * from './CssVarsProvider';\nexport { default as experimental_extendTheme } from './experimental_extendTheme';\nexport { default as getOverlayAlpha } from './getOverlayAlpha';\nexport { default as shouldSkipGeneratingVar } from './shouldSkipGeneratingVar';\n\n// Private methods for creating parts of the theme\nexport { default as private_createTypography } from './createTypography';\nexport { default as private_createMixins } from './createMixins';\nexport { default as private_excludeVariablesFromRoot } from './excludeVariablesFromRoot';", "map": {"version": 3, "names": ["_formatMuiErrorMessage", "default", "THEME_ID", "adaptV4Theme", "hexToRgb", "rgbToHex", "hslToRgb", "decomposeColor", "recomposeColor", "getContrastRatio", "getLuminance", "emphasize", "alpha", "darken", "lighten", "css", "keyframes", "experimental_sx", "Error", "process", "env", "NODE_ENV", "createTheme", "createMuiTheme", "unstable_createMuiStrictModeTheme", "createStyles", "getUnit", "unstable_getUnit", "toUni<PERSON>s", "unstable_toUnitless", "responsiveFontSizes", "duration", "easing", "useTheme", "useThemeProps", "styled", "experimentalStyled", "ThemeProvider", "StyledEngineProvider", "makeStyles", "with<PERSON><PERSON><PERSON>", "withTheme", "experimental_extendTheme", "getOverlayAlpha", "shouldSkipGeneratingVar", "private_createTypography", "private_createMixins", "private_excludeVariablesFromRoot"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/material/styles/index.js"], "sourcesContent": ["'use client';\n\nimport _formatMuiErrorMessage from \"@mui/utils/formatMuiErrorMessage\";\nexport { default as THEME_ID } from './identifier';\nexport { default as adaptV4Theme } from './adaptV4Theme';\nexport { hexToRgb, rgbToHex, hslToRgb, decomposeColor, recomposeColor, getContrastRatio, getLuminance, emphasize, alpha, darken, lighten, css, keyframes } from '@mui/system';\n// TODO: Remove this function in v6.\n// eslint-disable-next-line @typescript-eslint/naming-convention\nexport function experimental_sx() {\n  throw new Error(process.env.NODE_ENV !== \"production\" ? `MUI: The \\`experimental_sx\\` has been moved to \\`theme.unstable_sx\\`.For more details, see https://github.com/mui/material-ui/pull/35150.` : _formatMuiErrorMessage(20));\n}\nexport { default as createTheme, createMuiTheme } from './createTheme';\nexport { default as unstable_createMuiStrictModeTheme } from './createMuiStrictModeTheme';\nexport { default as createStyles } from './createStyles';\nexport { getUnit as unstable_getUnit, toUnitless as unstable_toUnitless } from './cssUtils';\nexport { default as responsiveFontSizes } from './responsiveFontSizes';\nexport { duration, easing } from './createTransitions';\nexport { default as useTheme } from './useTheme';\nexport { default as useThemeProps } from './useThemeProps';\nexport { default as styled } from './styled';\nexport { default as experimentalStyled } from './styled';\nexport { default as ThemeProvider } from './ThemeProvider';\nexport { StyledEngineProvider } from '@mui/system';\n// The legacy utilities from @mui/styles\n// These are just empty functions that throws when invoked\nexport { default as makeStyles } from './makeStyles';\nexport { default as withStyles } from './withStyles';\nexport { default as withTheme } from './withTheme';\nexport * from './CssVarsProvider';\nexport { default as experimental_extendTheme } from './experimental_extendTheme';\nexport { default as getOverlayAlpha } from './getOverlayAlpha';\nexport { default as shouldSkipGeneratingVar } from './shouldSkipGeneratingVar';\n\n// Private methods for creating parts of the theme\nexport { default as private_createTypography } from './createTypography';\nexport { default as private_createMixins } from './createMixins';\nexport { default as private_excludeVariablesFromRoot } from './excludeVariablesFromRoot';"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,sBAAsB,MAAM,kCAAkC;AACrE,SAASC,OAAO,IAAIC,QAAQ,QAAQ,cAAc;AAClD,SAASD,OAAO,IAAIE,YAAY,QAAQ,gBAAgB;AACxD,SAASC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,YAAY,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,OAAO,EAAEC,GAAG,EAAEC,SAAS,QAAQ,aAAa;AAC7K;AACA;AACA,OAAO,SAASC,eAAeA,CAAA,EAAG;EAChC,MAAM,IAAIC,KAAK,CAACC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,6IAAiJrB,sBAAsB,CAAC,EAAE,CAAC,CAAC;AACnO;AACA,SAASC,OAAO,IAAIqB,WAAW,EAAEC,cAAc,QAAQ,eAAe;AACtE,SAAStB,OAAO,IAAIuB,iCAAiC,QAAQ,4BAA4B;AACzF,SAASvB,OAAO,IAAIwB,YAAY,QAAQ,gBAAgB;AACxD,SAASC,OAAO,IAAIC,gBAAgB,EAAEC,UAAU,IAAIC,mBAAmB,QAAQ,YAAY;AAC3F,SAAS5B,OAAO,IAAI6B,mBAAmB,QAAQ,uBAAuB;AACtE,SAASC,QAAQ,EAAEC,MAAM,QAAQ,qBAAqB;AACtD,SAAS/B,OAAO,IAAIgC,QAAQ,QAAQ,YAAY;AAChD,SAAShC,OAAO,IAAIiC,aAAa,QAAQ,iBAAiB;AAC1D,SAASjC,OAAO,IAAIkC,MAAM,QAAQ,UAAU;AAC5C,SAASlC,OAAO,IAAImC,kBAAkB,QAAQ,UAAU;AACxD,SAASnC,OAAO,IAAIoC,aAAa,QAAQ,iBAAiB;AAC1D,SAASC,oBAAoB,QAAQ,aAAa;AAClD;AACA;AACA,SAASrC,OAAO,IAAIsC,UAAU,QAAQ,cAAc;AACpD,SAAStC,OAAO,IAAIuC,UAAU,QAAQ,cAAc;AACpD,SAASvC,OAAO,IAAIwC,SAAS,QAAQ,aAAa;AAClD,cAAc,mBAAmB;AACjC,SAASxC,OAAO,IAAIyC,wBAAwB,QAAQ,4BAA4B;AAChF,SAASzC,OAAO,IAAI0C,eAAe,QAAQ,mBAAmB;AAC9D,SAAS1C,OAAO,IAAI2C,uBAAuB,QAAQ,2BAA2B;;AAE9E;AACA,SAAS3C,OAAO,IAAI4C,wBAAwB,QAAQ,oBAAoB;AACxE,SAAS5C,OAAO,IAAI6C,oBAAoB,QAAQ,gBAAgB;AAChE,SAAS7C,OAAO,IAAI8C,gCAAgC,QAAQ,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}