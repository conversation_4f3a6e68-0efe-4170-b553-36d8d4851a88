{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ListIndexesOperation = exports.DropIndexOperation = exports.CreateIndexesOperation = void 0;\nconst responses_1 = require(\"../cmap/wire_protocol/responses\");\nconst error_1 = require(\"../error\");\nconst utils_1 = require(\"../utils\");\nconst command_1 = require(\"./command\");\nconst operation_1 = require(\"./operation\");\nconst VALID_INDEX_OPTIONS = new Set(['background', 'unique', 'name', 'partialFilterExpression', 'sparse', 'hidden', 'expireAfterSeconds', 'storageEngine', 'collation', 'version',\n// text indexes\n'weights', 'default_language', 'language_override', 'textIndexVersion',\n// 2d-sphere indexes\n'2dsphereIndexVersion',\n// 2d indexes\n'bits', 'min', 'max',\n// geoHaystack Indexes\n'bucketSize',\n// wildcard indexes\n'wildcardProjection']);\nfunction isIndexDirection(x) {\n  return typeof x === 'number' || x === '2d' || x === '2dsphere' || x === 'text' || x === 'geoHaystack';\n}\nfunction isSingleIndexTuple(t) {\n  return Array.isArray(t) && t.length === 2 && isIndexDirection(t[1]);\n}\n/**\n * Converts an `IndexSpecification`, which can be specified in multiple formats, into a\n * valid `key` for the createIndexes command.\n */\nfunction constructIndexDescriptionMap(indexSpec) {\n  const key = new Map();\n  const indexSpecs = !Array.isArray(indexSpec) || isSingleIndexTuple(indexSpec) ? [indexSpec] : indexSpec;\n  // Iterate through array and handle different types\n  for (const spec of indexSpecs) {\n    if (typeof spec === 'string') {\n      key.set(spec, 1);\n    } else if (Array.isArray(spec)) {\n      key.set(spec[0], spec[1] ?? 1);\n    } else if (spec instanceof Map) {\n      for (const [property, value] of spec) {\n        key.set(property, value);\n      }\n    } else if ((0, utils_1.isObject)(spec)) {\n      for (const [property, value] of Object.entries(spec)) {\n        key.set(property, value);\n      }\n    }\n  }\n  return key;\n}\n/**\n * Receives an index description and returns a modified index description which has had invalid options removed\n * from the description and has mapped the `version` option to the `v` option.\n */\nfunction resolveIndexDescription(description) {\n  const validProvidedOptions = Object.entries(description).filter(([optionName]) => VALID_INDEX_OPTIONS.has(optionName));\n  return Object.fromEntries(\n  // we support the `version` option, but the `createIndexes` command expects it to be the `v`\n  validProvidedOptions.map(([name, value]) => name === 'version' ? ['v', value] : [name, value]));\n}\n/** @internal */\nclass CreateIndexesOperation extends command_1.CommandOperation {\n  constructor(parent, collectionName, indexes, options) {\n    super(parent, options);\n    this.options = options ?? {};\n    this.collectionName = collectionName;\n    this.indexes = indexes.map(userIndex => {\n      // Ensure the key is a Map to preserve index key ordering\n      const key = userIndex.key instanceof Map ? userIndex.key : new Map(Object.entries(userIndex.key));\n      const name = userIndex.name ?? Array.from(key).flat().join('_');\n      const validIndexOptions = resolveIndexDescription(userIndex);\n      return {\n        ...validIndexOptions,\n        name,\n        key\n      };\n    });\n  }\n  static fromIndexDescriptionArray(parent, collectionName, indexes, options) {\n    return new CreateIndexesOperation(parent, collectionName, indexes, options);\n  }\n  static fromIndexSpecification(parent, collectionName, indexSpec, options = {}) {\n    const key = constructIndexDescriptionMap(indexSpec);\n    const description = {\n      ...options,\n      key\n    };\n    return new CreateIndexesOperation(parent, collectionName, [description], options);\n  }\n  get commandName() {\n    return 'createIndexes';\n  }\n  async execute(server, session, timeoutContext) {\n    const options = this.options;\n    const indexes = this.indexes;\n    const serverWireVersion = (0, utils_1.maxWireVersion)(server);\n    const cmd = {\n      createIndexes: this.collectionName,\n      indexes\n    };\n    if (options.commitQuorum != null) {\n      if (serverWireVersion < 9) {\n        throw new error_1.MongoCompatibilityError('Option `commitQuorum` for `createIndexes` not supported on servers < 4.4');\n      }\n      cmd.commitQuorum = options.commitQuorum;\n    }\n    // collation is set on each index, it should not be defined at the root\n    this.options.collation = undefined;\n    await super.executeCommand(server, session, cmd, timeoutContext);\n    const indexNames = indexes.map(index => index.name || '');\n    return indexNames;\n  }\n}\nexports.CreateIndexesOperation = CreateIndexesOperation;\n/** @internal */\nclass DropIndexOperation extends command_1.CommandOperation {\n  constructor(collection, indexName, options) {\n    super(collection, options);\n    this.options = options ?? {};\n    this.collection = collection;\n    this.indexName = indexName;\n  }\n  get commandName() {\n    return 'dropIndexes';\n  }\n  async execute(server, session, timeoutContext) {\n    const cmd = {\n      dropIndexes: this.collection.collectionName,\n      index: this.indexName\n    };\n    return await super.executeCommand(server, session, cmd, timeoutContext);\n  }\n}\nexports.DropIndexOperation = DropIndexOperation;\n/** @internal */\nclass ListIndexesOperation extends command_1.CommandOperation {\n  constructor(collection, options) {\n    super(collection, options);\n    this.options = {\n      ...options\n    };\n    delete this.options.writeConcern;\n    this.collectionNamespace = collection.s.namespace;\n  }\n  get commandName() {\n    return 'listIndexes';\n  }\n  async execute(server, session, timeoutContext) {\n    const serverWireVersion = (0, utils_1.maxWireVersion)(server);\n    const cursor = this.options.batchSize ? {\n      batchSize: this.options.batchSize\n    } : {};\n    const command = {\n      listIndexes: this.collectionNamespace.collection,\n      cursor\n    };\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (serverWireVersion >= 9 && this.options.comment !== undefined) {\n      command.comment = this.options.comment;\n    }\n    return await super.executeCommand(server, session, command, timeoutContext, responses_1.CursorResponse);\n  }\n}\nexports.ListIndexesOperation = ListIndexesOperation;\n(0, operation_1.defineAspects)(ListIndexesOperation, [operation_1.Aspect.READ_OPERATION, operation_1.Aspect.RETRYABLE, operation_1.Aspect.CURSOR_CREATING]);\n(0, operation_1.defineAspects)(CreateIndexesOperation, [operation_1.Aspect.WRITE_OPERATION]);\n(0, operation_1.defineAspects)(DropIndexOperation, [operation_1.Aspect.WRITE_OPERATION]);", "map": {"version": 3, "names": ["responses_1", "require", "error_1", "utils_1", "command_1", "operation_1", "VALID_INDEX_OPTIONS", "Set", "isIndexDirection", "x", "isSingleIndexTuple", "t", "Array", "isArray", "length", "constructIndexDescriptionMap", "indexSpec", "key", "Map", "indexSpecs", "spec", "set", "property", "value", "isObject", "Object", "entries", "resolveIndexDescription", "description", "validProvidedOptions", "filter", "optionName", "has", "fromEntries", "map", "name", "CreateIndexesOperation", "CommandOperation", "constructor", "parent", "collectionName", "indexes", "options", "userIndex", "from", "flat", "join", "validIndexOptions", "fromIndexDescriptionArray", "fromIndexSpecification", "commandName", "execute", "server", "session", "timeoutContext", "serverWireVersion", "maxWireVersion", "cmd", "createIndexes", "commitQuorum", "MongoCompatibilityError", "collation", "undefined", "executeCommand", "indexNames", "index", "exports", "DropIndexOperation", "collection", "indexName", "dropIndexes", "ListIndexesOperation", "writeConcern", "collectionNamespace", "s", "namespace", "cursor", "batchSize", "command", "listIndexes", "comment", "CursorResponse", "defineAspects", "Aspect", "READ_OPERATION", "RETRYABLE", "CURSOR_CREATING", "WRITE_OPERATION"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\indexes.ts"], "sourcesContent": ["import type { Document } from '../bson';\nimport { CursorResponse } from '../cmap/wire_protocol/responses';\nimport type { Collection } from '../collection';\nimport { type AbstractCursorOptions } from '../cursor/abstract_cursor';\nimport { MongoCompatibilityError } from '../error';\nimport { type OneOrMore } from '../mongo_types';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { isObject, maxWireVersion, type MongoDBNamespace } from '../utils';\nimport {\n  type CollationOptions,\n  CommandOperation,\n  type CommandOperationOptions,\n  type OperationParent\n} from './command';\nimport { Aspect, defineAspects } from './operation';\n\nconst VALID_INDEX_OPTIONS = new Set([\n  'background',\n  'unique',\n  'name',\n  'partialFilterExpression',\n  'sparse',\n  'hidden',\n  'expireAfterSeconds',\n  'storageEngine',\n  'collation',\n  'version',\n\n  // text indexes\n  'weights',\n  'default_language',\n  'language_override',\n  'textIndexVersion',\n\n  // 2d-sphere indexes\n  '2dsphereIndexVersion',\n\n  // 2d indexes\n  'bits',\n  'min',\n  'max',\n\n  // geoHaystack Indexes\n  'bucketSize',\n\n  // wildcard indexes\n  'wildcardProjection'\n]);\n\n/** @public */\nexport type IndexDirection =\n  | -1\n  | 1\n  | '2d'\n  | '2dsphere'\n  | 'text'\n  | 'geoHaystack'\n  | 'hashed'\n  | number;\n\nfunction isIndexDirection(x: unknown): x is IndexDirection {\n  return (\n    typeof x === 'number' || x === '2d' || x === '2dsphere' || x === 'text' || x === 'geoHaystack'\n  );\n}\n/** @public */\nexport type IndexSpecification = OneOrMore<\n  | string\n  | [string, IndexDirection]\n  | { [key: string]: IndexDirection }\n  | Map<string, IndexDirection>\n>;\n\n/** @public */\nexport interface IndexInformationOptions extends ListIndexesOptions {\n  /**\n   * When `true`, an array of index descriptions is returned.\n   * When `false`, the driver returns an object that with keys corresponding to index names with values\n   * corresponding to the entries of the indexes' key.\n   *\n   * For example, the given the following indexes:\n   * ```\n   * [ { name: 'a_1', key: { a: 1 } }, { name: 'b_1_c_1' , key: { b: 1, c: 1 } }]\n   * ```\n   *\n   * When `full` is `true`, the above array is returned.  When `full` is `false`, the following is returned:\n   * ```\n   * {\n   *   'a_1': [['a', 1]],\n   *   'b_1_c_1': [['b', 1], ['c', 1]],\n   * }\n   * ```\n   */\n  full?: boolean;\n}\n\n/** @public */\nexport interface IndexDescription\n  extends Pick<\n    CreateIndexesOptions,\n    | 'background'\n    | 'unique'\n    | 'partialFilterExpression'\n    | 'sparse'\n    | 'hidden'\n    | 'expireAfterSeconds'\n    | 'storageEngine'\n    | 'version'\n    | 'weights'\n    | 'default_language'\n    | 'language_override'\n    | 'textIndexVersion'\n    | '2dsphereIndexVersion'\n    | 'bits'\n    | 'min'\n    | 'max'\n    | 'bucketSize'\n    | 'wildcardProjection'\n  > {\n  collation?: CollationOptions;\n  name?: string;\n  key: { [key: string]: IndexDirection } | Map<string, IndexDirection>;\n}\n\n/** @public */\nexport interface CreateIndexesOptions extends Omit<CommandOperationOptions, 'writeConcern'> {\n  /** Creates the index in the background, yielding whenever possible. */\n  background?: boolean;\n  /** Creates an unique index. */\n  unique?: boolean;\n  /** Override the autogenerated index name (useful if the resulting name is larger than 128 bytes) */\n  name?: string;\n  /** Creates a partial index based on the given filter object (MongoDB 3.2 or higher) */\n  partialFilterExpression?: Document;\n  /** Creates a sparse index. */\n  sparse?: boolean;\n  /** Allows you to expire data on indexes applied to a data (MongoDB 2.2 or higher) */\n  expireAfterSeconds?: number;\n  /** Allows users to configure the storage engine on a per-index basis when creating an index. (MongoDB 3.0 or higher) */\n  storageEngine?: Document;\n  /** (MongoDB 4.4. or higher) Specifies how many data-bearing members of a replica set, including the primary, must complete the index builds successfully before the primary marks the indexes as ready. This option accepts the same values for the \"w\" field in a write concern plus \"votingMembers\", which indicates all voting data-bearing nodes. */\n  commitQuorum?: number | string;\n  /** Specifies the index version number, either 0 or 1. */\n  version?: number;\n  // text indexes\n  weights?: Document;\n  default_language?: string;\n  language_override?: string;\n  textIndexVersion?: number;\n  // 2d-sphere indexes\n  '2dsphereIndexVersion'?: number;\n  // 2d indexes\n  bits?: number;\n  /** For geospatial indexes set the lower bound for the co-ordinates. */\n  min?: number;\n  /** For geospatial indexes set the high bound for the co-ordinates. */\n  max?: number;\n  // geoHaystack Indexes\n  bucketSize?: number;\n  // wildcard indexes\n  wildcardProjection?: Document;\n  /** Specifies that the index should exist on the target collection but should not be used by the query planner when executing operations. (MongoDB 4.4 or higher) */\n  hidden?: boolean;\n}\n\nfunction isSingleIndexTuple(t: unknown): t is [string, IndexDirection] {\n  return Array.isArray(t) && t.length === 2 && isIndexDirection(t[1]);\n}\n\n/**\n * Converts an `IndexSpecification`, which can be specified in multiple formats, into a\n * valid `key` for the createIndexes command.\n */\nfunction constructIndexDescriptionMap(indexSpec: IndexSpecification): Map<string, IndexDirection> {\n  const key: Map<string, IndexDirection> = new Map();\n\n  const indexSpecs =\n    !Array.isArray(indexSpec) || isSingleIndexTuple(indexSpec) ? [indexSpec] : indexSpec;\n\n  // Iterate through array and handle different types\n  for (const spec of indexSpecs) {\n    if (typeof spec === 'string') {\n      key.set(spec, 1);\n    } else if (Array.isArray(spec)) {\n      key.set(spec[0], spec[1] ?? 1);\n    } else if (spec instanceof Map) {\n      for (const [property, value] of spec) {\n        key.set(property, value);\n      }\n    } else if (isObject(spec)) {\n      for (const [property, value] of Object.entries(spec)) {\n        key.set(property, value);\n      }\n    }\n  }\n\n  return key;\n}\n\n/**\n * Receives an index description and returns a modified index description which has had invalid options removed\n * from the description and has mapped the `version` option to the `v` option.\n */\nfunction resolveIndexDescription(\n  description: IndexDescription\n): Omit<ResolvedIndexDescription, 'key'> {\n  const validProvidedOptions = Object.entries(description).filter(([optionName]) =>\n    VALID_INDEX_OPTIONS.has(optionName)\n  );\n\n  return Object.fromEntries(\n    // we support the `version` option, but the `createIndexes` command expects it to be the `v`\n    validProvidedOptions.map(([name, value]) => (name === 'version' ? ['v', value] : [name, value]))\n  );\n}\n\n/**\n * @public\n * The index information returned by the listIndexes command. https://www.mongodb.com/docs/manual/reference/command/listIndexes/#mongodb-dbcommand-dbcmd.listIndexes\n */\nexport type IndexDescriptionInfo = Omit<IndexDescription, 'key' | 'version'> & {\n  key: { [key: string]: IndexDirection };\n  v?: IndexDescription['version'];\n} & Document;\n\n/** @public */\nexport type IndexDescriptionCompact = Record<string, [name: string, direction: IndexDirection][]>;\n\n/**\n * @internal\n *\n * Internally, the driver represents index description keys with `Map`s to preserve key ordering.\n * We don't require users to specify maps, so we transform user provided descriptions into\n * \"resolved\" by converting the `key` into a JS `Map`, if it isn't already a map.\n *\n * Additionally, we support the `version` option, but the `createIndexes` command uses the field `v`\n * to specify the index version so we map the value of `version` to `v`, if provided.\n */\ntype ResolvedIndexDescription = Omit<IndexDescription, 'key' | 'version'> & {\n  key: Map<string, IndexDirection>;\n  v?: IndexDescription['version'];\n};\n\n/** @internal */\nexport class CreateIndexesOperation extends CommandOperation<string[]> {\n  override options: CreateIndexesOptions;\n  collectionName: string;\n  indexes: ReadonlyArray<ResolvedIndexDescription>;\n\n  private constructor(\n    parent: OperationParent,\n    collectionName: string,\n    indexes: IndexDescription[],\n    options?: CreateIndexesOptions\n  ) {\n    super(parent, options);\n\n    this.options = options ?? {};\n    this.collectionName = collectionName;\n    this.indexes = indexes.map((userIndex: IndexDescription): ResolvedIndexDescription => {\n      // Ensure the key is a Map to preserve index key ordering\n      const key =\n        userIndex.key instanceof Map ? userIndex.key : new Map(Object.entries(userIndex.key));\n      const name = userIndex.name ?? Array.from(key).flat().join('_');\n      const validIndexOptions = resolveIndexDescription(userIndex);\n      return {\n        ...validIndexOptions,\n        name,\n        key\n      };\n    });\n  }\n\n  static fromIndexDescriptionArray(\n    parent: OperationParent,\n    collectionName: string,\n    indexes: IndexDescription[],\n    options?: CreateIndexesOptions\n  ): CreateIndexesOperation {\n    return new CreateIndexesOperation(parent, collectionName, indexes, options);\n  }\n\n  static fromIndexSpecification(\n    parent: OperationParent,\n    collectionName: string,\n    indexSpec: IndexSpecification,\n    options: CreateIndexesOptions = {}\n  ): CreateIndexesOperation {\n    const key = constructIndexDescriptionMap(indexSpec);\n    const description: IndexDescription = { ...options, key };\n    return new CreateIndexesOperation(parent, collectionName, [description], options);\n  }\n\n  override get commandName() {\n    return 'createIndexes';\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<string[]> {\n    const options = this.options;\n    const indexes = this.indexes;\n\n    const serverWireVersion = maxWireVersion(server);\n\n    const cmd: Document = { createIndexes: this.collectionName, indexes };\n\n    if (options.commitQuorum != null) {\n      if (serverWireVersion < 9) {\n        throw new MongoCompatibilityError(\n          'Option `commitQuorum` for `createIndexes` not supported on servers < 4.4'\n        );\n      }\n      cmd.commitQuorum = options.commitQuorum;\n    }\n\n    // collation is set on each index, it should not be defined at the root\n    this.options.collation = undefined;\n\n    await super.executeCommand(server, session, cmd, timeoutContext);\n\n    const indexNames = indexes.map(index => index.name || '');\n    return indexNames;\n  }\n}\n\n/** @public */\nexport type DropIndexesOptions = CommandOperationOptions;\n\n/** @internal */\nexport class DropIndexOperation extends CommandOperation<Document> {\n  override options: DropIndexesOptions;\n  collection: Collection;\n  indexName: string;\n\n  constructor(collection: Collection, indexName: string, options?: DropIndexesOptions) {\n    super(collection, options);\n\n    this.options = options ?? {};\n    this.collection = collection;\n    this.indexName = indexName;\n  }\n\n  override get commandName() {\n    return 'dropIndexes' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<Document> {\n    const cmd = { dropIndexes: this.collection.collectionName, index: this.indexName };\n    return await super.executeCommand(server, session, cmd, timeoutContext);\n  }\n}\n\n/** @public */\nexport type ListIndexesOptions = AbstractCursorOptions & {\n  /** @internal */\n  omitMaxTimeMS?: boolean;\n};\n\n/** @internal */\nexport class ListIndexesOperation extends CommandOperation<CursorResponse> {\n  /**\n   * @remarks WriteConcern can still be present on the options because\n   * we inherit options from the client/db/collection.  The\n   * key must be present on the options in order to delete it.\n   * This allows typescript to delete the key but will\n   * not allow a writeConcern to be assigned as a property on options.\n   */\n  override options: ListIndexesOptions & { writeConcern?: never };\n  collectionNamespace: MongoDBNamespace;\n\n  constructor(collection: Collection, options?: ListIndexesOptions) {\n    super(collection, options);\n\n    this.options = { ...options };\n    delete this.options.writeConcern;\n    this.collectionNamespace = collection.s.namespace;\n  }\n\n  override get commandName() {\n    return 'listIndexes' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<CursorResponse> {\n    const serverWireVersion = maxWireVersion(server);\n\n    const cursor = this.options.batchSize ? { batchSize: this.options.batchSize } : {};\n\n    const command: Document = { listIndexes: this.collectionNamespace.collection, cursor };\n\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (serverWireVersion >= 9 && this.options.comment !== undefined) {\n      command.comment = this.options.comment;\n    }\n\n    return await super.executeCommand(server, session, command, timeoutContext, CursorResponse);\n  }\n}\n\ndefineAspects(ListIndexesOperation, [\n  Aspect.READ_OPERATION,\n  Aspect.RETRYABLE,\n  Aspect.CURSOR_CREATING\n]);\ndefineAspects(CreateIndexesOperation, [Aspect.WRITE_OPERATION]);\ndefineAspects(DropIndexOperation, [Aspect.WRITE_OPERATION]);\n"], "mappings": ";;;;;;AACA,MAAAA,WAAA,GAAAC,OAAA;AAGA,MAAAC,OAAA,GAAAD,OAAA;AAKA,MAAAE,OAAA,GAAAF,OAAA;AACA,MAAAG,SAAA,GAAAH,OAAA;AAMA,MAAAI,WAAA,GAAAJ,OAAA;AAEA,MAAMK,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAClC,YAAY,EACZ,QAAQ,EACR,MAAM,EACN,yBAAyB,EACzB,QAAQ,EACR,QAAQ,EACR,oBAAoB,EACpB,eAAe,EACf,WAAW,EACX,SAAS;AAET;AACA,SAAS,EACT,kBAAkB,EAClB,mBAAmB,EACnB,kBAAkB;AAElB;AACA,sBAAsB;AAEtB;AACA,MAAM,EACN,KAAK,EACL,KAAK;AAEL;AACA,YAAY;AAEZ;AACA,oBAAoB,CACrB,CAAC;AAaF,SAASC,gBAAgBA,CAACC,CAAU;EAClC,OACE,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,UAAU,IAAIA,CAAC,KAAK,MAAM,IAAIA,CAAC,KAAK,aAAa;AAElG;AAqGA,SAASC,kBAAkBA,CAACC,CAAU;EACpC,OAAOC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,IAAIA,CAAC,CAACG,MAAM,KAAK,CAAC,IAAIN,gBAAgB,CAACG,CAAC,CAAC,CAAC,CAAC,CAAC;AACrE;AAEA;;;;AAIA,SAASI,4BAA4BA,CAACC,SAA6B;EACjE,MAAMC,GAAG,GAAgC,IAAIC,GAAG,EAAE;EAElD,MAAMC,UAAU,GACd,CAACP,KAAK,CAACC,OAAO,CAACG,SAAS,CAAC,IAAIN,kBAAkB,CAACM,SAAS,CAAC,GAAG,CAACA,SAAS,CAAC,GAAGA,SAAS;EAEtF;EACA,KAAK,MAAMI,IAAI,IAAID,UAAU,EAAE;IAC7B,IAAI,OAAOC,IAAI,KAAK,QAAQ,EAAE;MAC5BH,GAAG,CAACI,GAAG,CAACD,IAAI,EAAE,CAAC,CAAC;IAClB,CAAC,MAAM,IAAIR,KAAK,CAACC,OAAO,CAACO,IAAI,CAAC,EAAE;MAC9BH,GAAG,CAACI,GAAG,CAACD,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAChC,CAAC,MAAM,IAAIA,IAAI,YAAYF,GAAG,EAAE;MAC9B,KAAK,MAAM,CAACI,QAAQ,EAAEC,KAAK,CAAC,IAAIH,IAAI,EAAE;QACpCH,GAAG,CAACI,GAAG,CAACC,QAAQ,EAAEC,KAAK,CAAC;MAC1B;IACF,CAAC,MAAM,IAAI,IAAApB,OAAA,CAAAqB,QAAQ,EAACJ,IAAI,CAAC,EAAE;MACzB,KAAK,MAAM,CAACE,QAAQ,EAAEC,KAAK,CAAC,IAAIE,MAAM,CAACC,OAAO,CAACN,IAAI,CAAC,EAAE;QACpDH,GAAG,CAACI,GAAG,CAACC,QAAQ,EAAEC,KAAK,CAAC;MAC1B;IACF;EACF;EAEA,OAAON,GAAG;AACZ;AAEA;;;;AAIA,SAASU,uBAAuBA,CAC9BC,WAA6B;EAE7B,MAAMC,oBAAoB,GAAGJ,MAAM,CAACC,OAAO,CAACE,WAAW,CAAC,CAACE,MAAM,CAAC,CAAC,CAACC,UAAU,CAAC,KAC3EzB,mBAAmB,CAAC0B,GAAG,CAACD,UAAU,CAAC,CACpC;EAED,OAAON,MAAM,CAACQ,WAAW;EACvB;EACAJ,oBAAoB,CAACK,GAAG,CAAC,CAAC,CAACC,IAAI,EAAEZ,KAAK,CAAC,KAAMY,IAAI,KAAK,SAAS,GAAG,CAAC,GAAG,EAAEZ,KAAK,CAAC,GAAG,CAACY,IAAI,EAAEZ,KAAK,CAAE,CAAC,CACjG;AACH;AA6BA;AACA,MAAaa,sBAAuB,SAAQhC,SAAA,CAAAiC,gBAA0B;EAKpEC,YACEC,MAAuB,EACvBC,cAAsB,EACtBC,OAA2B,EAC3BC,OAA8B;IAE9B,KAAK,CAACH,MAAM,EAAEG,OAAO,CAAC;IAEtB,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAI,EAAE;IAC5B,IAAI,CAACF,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,OAAO,GAAGA,OAAO,CAACP,GAAG,CAAES,SAA2B,IAA8B;MACnF;MACA,MAAM1B,GAAG,GACP0B,SAAS,CAAC1B,GAAG,YAAYC,GAAG,GAAGyB,SAAS,CAAC1B,GAAG,GAAG,IAAIC,GAAG,CAACO,MAAM,CAACC,OAAO,CAACiB,SAAS,CAAC1B,GAAG,CAAC,CAAC;MACvF,MAAMkB,IAAI,GAAGQ,SAAS,CAACR,IAAI,IAAIvB,KAAK,CAACgC,IAAI,CAAC3B,GAAG,CAAC,CAAC4B,IAAI,EAAE,CAACC,IAAI,CAAC,GAAG,CAAC;MAC/D,MAAMC,iBAAiB,GAAGpB,uBAAuB,CAACgB,SAAS,CAAC;MAC5D,OAAO;QACL,GAAGI,iBAAiB;QACpBZ,IAAI;QACJlB;OACD;IACH,CAAC,CAAC;EACJ;EAEA,OAAO+B,yBAAyBA,CAC9BT,MAAuB,EACvBC,cAAsB,EACtBC,OAA2B,EAC3BC,OAA8B;IAE9B,OAAO,IAAIN,sBAAsB,CAACG,MAAM,EAAEC,cAAc,EAAEC,OAAO,EAAEC,OAAO,CAAC;EAC7E;EAEA,OAAOO,sBAAsBA,CAC3BV,MAAuB,EACvBC,cAAsB,EACtBxB,SAA6B,EAC7B0B,OAAA,GAAgC,EAAE;IAElC,MAAMzB,GAAG,GAAGF,4BAA4B,CAACC,SAAS,CAAC;IACnD,MAAMY,WAAW,GAAqB;MAAE,GAAGc,OAAO;MAAEzB;IAAG,CAAE;IACzD,OAAO,IAAImB,sBAAsB,CAACG,MAAM,EAAEC,cAAc,EAAE,CAACZ,WAAW,CAAC,EAAEc,OAAO,CAAC;EACnF;EAEA,IAAaQ,WAAWA,CAAA;IACtB,OAAO,eAAe;EACxB;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMZ,OAAO,GAAG,IAAI,CAACA,OAAO;IAC5B,MAAMD,OAAO,GAAG,IAAI,CAACA,OAAO;IAE5B,MAAMc,iBAAiB,GAAG,IAAApD,OAAA,CAAAqD,cAAc,EAACJ,MAAM,CAAC;IAEhD,MAAMK,GAAG,GAAa;MAAEC,aAAa,EAAE,IAAI,CAAClB,cAAc;MAAEC;IAAO,CAAE;IAErE,IAAIC,OAAO,CAACiB,YAAY,IAAI,IAAI,EAAE;MAChC,IAAIJ,iBAAiB,GAAG,CAAC,EAAE;QACzB,MAAM,IAAIrD,OAAA,CAAA0D,uBAAuB,CAC/B,0EAA0E,CAC3E;MACH;MACAH,GAAG,CAACE,YAAY,GAAGjB,OAAO,CAACiB,YAAY;IACzC;IAEA;IACA,IAAI,CAACjB,OAAO,CAACmB,SAAS,GAAGC,SAAS;IAElC,MAAM,KAAK,CAACC,cAAc,CAACX,MAAM,EAAEC,OAAO,EAAEI,GAAG,EAAEH,cAAc,CAAC;IAEhE,MAAMU,UAAU,GAAGvB,OAAO,CAACP,GAAG,CAAC+B,KAAK,IAAIA,KAAK,CAAC9B,IAAI,IAAI,EAAE,CAAC;IACzD,OAAO6B,UAAU;EACnB;;AAjFFE,OAAA,CAAA9B,sBAAA,GAAAA,sBAAA;AAuFA;AACA,MAAa+B,kBAAmB,SAAQ/D,SAAA,CAAAiC,gBAA0B;EAKhEC,YAAY8B,UAAsB,EAAEC,SAAiB,EAAE3B,OAA4B;IACjF,KAAK,CAAC0B,UAAU,EAAE1B,OAAO,CAAC;IAE1B,IAAI,CAACA,OAAO,GAAGA,OAAO,IAAI,EAAE;IAC5B,IAAI,CAAC0B,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,SAAS,GAAGA,SAAS;EAC5B;EAEA,IAAanB,WAAWA,CAAA;IACtB,OAAO,aAAsB;EAC/B;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMG,GAAG,GAAG;MAAEa,WAAW,EAAE,IAAI,CAACF,UAAU,CAAC5B,cAAc;MAAEyB,KAAK,EAAE,IAAI,CAACI;IAAS,CAAE;IAClF,OAAO,MAAM,KAAK,CAACN,cAAc,CAACX,MAAM,EAAEC,OAAO,EAAEI,GAAG,EAAEH,cAAc,CAAC;EACzE;;AAxBFY,OAAA,CAAAC,kBAAA,GAAAA,kBAAA;AAiCA;AACA,MAAaI,oBAAqB,SAAQnE,SAAA,CAAAiC,gBAAgC;EAWxEC,YAAY8B,UAAsB,EAAE1B,OAA4B;IAC9D,KAAK,CAAC0B,UAAU,EAAE1B,OAAO,CAAC;IAE1B,IAAI,CAACA,OAAO,GAAG;MAAE,GAAGA;IAAO,CAAE;IAC7B,OAAO,IAAI,CAACA,OAAO,CAAC8B,YAAY;IAChC,IAAI,CAACC,mBAAmB,GAAGL,UAAU,CAACM,CAAC,CAACC,SAAS;EACnD;EAEA,IAAazB,WAAWA,CAAA;IACtB,OAAO,aAAsB;EAC/B;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMC,iBAAiB,GAAG,IAAApD,OAAA,CAAAqD,cAAc,EAACJ,MAAM,CAAC;IAEhD,MAAMwB,MAAM,GAAG,IAAI,CAAClC,OAAO,CAACmC,SAAS,GAAG;MAAEA,SAAS,EAAE,IAAI,CAACnC,OAAO,CAACmC;IAAS,CAAE,GAAG,EAAE;IAElF,MAAMC,OAAO,GAAa;MAAEC,WAAW,EAAE,IAAI,CAACN,mBAAmB,CAACL,UAAU;MAAEQ;IAAM,CAAE;IAEtF;IACA;IACA,IAAIrB,iBAAiB,IAAI,CAAC,IAAI,IAAI,CAACb,OAAO,CAACsC,OAAO,KAAKlB,SAAS,EAAE;MAChEgB,OAAO,CAACE,OAAO,GAAG,IAAI,CAACtC,OAAO,CAACsC,OAAO;IACxC;IAEA,OAAO,MAAM,KAAK,CAACjB,cAAc,CAACX,MAAM,EAAEC,OAAO,EAAEyB,OAAO,EAAExB,cAAc,EAAEtD,WAAA,CAAAiF,cAAc,CAAC;EAC7F;;AAzCFf,OAAA,CAAAK,oBAAA,GAAAA,oBAAA;AA4CA,IAAAlE,WAAA,CAAA6E,aAAa,EAACX,oBAAoB,EAAE,CAClClE,WAAA,CAAA8E,MAAM,CAACC,cAAc,EACrB/E,WAAA,CAAA8E,MAAM,CAACE,SAAS,EAChBhF,WAAA,CAAA8E,MAAM,CAACG,eAAe,CACvB,CAAC;AACF,IAAAjF,WAAA,CAAA6E,aAAa,EAAC9C,sBAAsB,EAAE,CAAC/B,WAAA,CAAA8E,MAAM,CAACI,eAAe,CAAC,CAAC;AAC/D,IAAAlF,WAAA,CAAA6E,aAAa,EAACf,kBAAkB,EAAE,CAAC9D,WAAA,CAAA8E,MAAM,CAACI,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}