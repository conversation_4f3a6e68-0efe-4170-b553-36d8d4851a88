{"ast": null, "code": "module.exports = Pager;\nfunction Pager(pageSize, opts) {\n  if (!(this instanceof Pager)) return new Pager(pageSize, opts);\n  this.length = 0;\n  this.updates = [];\n  this.path = new Uint16Array(4);\n  this.pages = new Array(32768);\n  this.maxPages = this.pages.length;\n  this.level = 0;\n  this.pageSize = pageSize || 1024;\n  this.deduplicate = opts ? opts.deduplicate : null;\n  this.zeros = this.deduplicate ? alloc(this.deduplicate.length) : null;\n}\nPager.prototype.updated = function (page) {\n  while (this.deduplicate && page.buffer[page.deduplicate] === this.deduplicate[page.deduplicate]) {\n    page.deduplicate++;\n    if (page.deduplicate === this.deduplicate.length) {\n      page.deduplicate = 0;\n      if (page.buffer.equals && page.buffer.equals(this.deduplicate)) page.buffer = this.deduplicate;\n      break;\n    }\n  }\n  if (page.updated || !this.updates) return;\n  page.updated = true;\n  this.updates.push(page);\n};\nPager.prototype.lastUpdate = function () {\n  if (!this.updates || !this.updates.length) return null;\n  var page = this.updates.pop();\n  page.updated = false;\n  return page;\n};\nPager.prototype._array = function (i, noAllocate) {\n  if (i >= this.maxPages) {\n    if (noAllocate) return;\n    grow(this, i);\n  }\n  factor(i, this.path);\n  var arr = this.pages;\n  for (var j = this.level; j > 0; j--) {\n    var p = this.path[j];\n    var next = arr[p];\n    if (!next) {\n      if (noAllocate) return;\n      next = arr[p] = new Array(32768);\n    }\n    arr = next;\n  }\n  return arr;\n};\nPager.prototype.get = function (i, noAllocate) {\n  var arr = this._array(i, noAllocate);\n  var first = this.path[0];\n  var page = arr && arr[first];\n  if (!page && !noAllocate) {\n    page = arr[first] = new Page(i, alloc(this.pageSize));\n    if (i >= this.length) this.length = i + 1;\n  }\n  if (page && page.buffer === this.deduplicate && this.deduplicate && !noAllocate) {\n    page.buffer = copy(page.buffer);\n    page.deduplicate = 0;\n  }\n  return page;\n};\nPager.prototype.set = function (i, buf) {\n  var arr = this._array(i, false);\n  var first = this.path[0];\n  if (i >= this.length) this.length = i + 1;\n  if (!buf || this.zeros && buf.equals && buf.equals(this.zeros)) {\n    arr[first] = undefined;\n    return;\n  }\n  if (this.deduplicate && buf.equals && buf.equals(this.deduplicate)) {\n    buf = this.deduplicate;\n  }\n  var page = arr[first];\n  var b = truncate(buf, this.pageSize);\n  if (page) page.buffer = b;else arr[first] = new Page(i, b);\n};\nPager.prototype.toBuffer = function () {\n  var list = new Array(this.length);\n  var empty = alloc(this.pageSize);\n  var ptr = 0;\n  while (ptr < list.length) {\n    var arr = this._array(ptr, true);\n    for (var i = 0; i < 32768 && ptr < list.length; i++) {\n      list[ptr++] = arr && arr[i] ? arr[i].buffer : empty;\n    }\n  }\n  return Buffer.concat(list);\n};\nfunction grow(pager, index) {\n  while (pager.maxPages < index) {\n    var old = pager.pages;\n    pager.pages = new Array(32768);\n    pager.pages[0] = old;\n    pager.level++;\n    pager.maxPages *= 32768;\n  }\n}\nfunction truncate(buf, len) {\n  if (buf.length === len) return buf;\n  if (buf.length > len) return buf.slice(0, len);\n  var cpy = alloc(len);\n  buf.copy(cpy);\n  return cpy;\n}\nfunction alloc(size) {\n  if (Buffer.alloc) return Buffer.alloc(size);\n  var buf = new Buffer(size);\n  buf.fill(0);\n  return buf;\n}\nfunction copy(buf) {\n  var cpy = Buffer.allocUnsafe ? Buffer.allocUnsafe(buf.length) : new Buffer(buf.length);\n  buf.copy(cpy);\n  return cpy;\n}\nfunction Page(i, buf) {\n  this.offset = i * buf.length;\n  this.buffer = buf;\n  this.updated = false;\n  this.deduplicate = 0;\n}\nfunction factor(n, out) {\n  n = (n - (out[0] = n & 32767)) / 32768;\n  n = (n - (out[1] = n & 32767)) / 32768;\n  out[3] = (n - (out[2] = n & 32767)) / 32768 & 32767;\n}", "map": {"version": 3, "names": ["module", "exports", "Pager", "pageSize", "opts", "length", "updates", "path", "Uint16Array", "pages", "Array", "maxPages", "level", "deduplicate", "zeros", "alloc", "prototype", "updated", "page", "buffer", "equals", "push", "lastUpdate", "pop", "_array", "i", "noAllocate", "grow", "factor", "arr", "j", "p", "next", "get", "first", "Page", "copy", "set", "buf", "undefined", "b", "truncate", "<PERSON><PERSON><PERSON><PERSON>", "list", "empty", "ptr", "<PERSON><PERSON><PERSON>", "concat", "pager", "index", "old", "len", "slice", "cpy", "size", "fill", "allocUnsafe", "offset", "n", "out"], "sources": ["C:/Users/<USER>/node_modules/memory-pager/index.js"], "sourcesContent": ["module.exports = Pager\n\nfunction Pager (pageSize, opts) {\n  if (!(this instanceof Pager)) return new Pager(pageSize, opts)\n\n  this.length = 0\n  this.updates = []\n  this.path = new Uint16Array(4)\n  this.pages = new Array(32768)\n  this.maxPages = this.pages.length\n  this.level = 0\n  this.pageSize = pageSize || 1024\n  this.deduplicate = opts ? opts.deduplicate : null\n  this.zeros = this.deduplicate ? alloc(this.deduplicate.length) : null\n}\n\nPager.prototype.updated = function (page) {\n  while (this.deduplicate && page.buffer[page.deduplicate] === this.deduplicate[page.deduplicate]) {\n    page.deduplicate++\n    if (page.deduplicate === this.deduplicate.length) {\n      page.deduplicate = 0\n      if (page.buffer.equals && page.buffer.equals(this.deduplicate)) page.buffer = this.deduplicate\n      break\n    }\n  }\n  if (page.updated || !this.updates) return\n  page.updated = true\n  this.updates.push(page)\n}\n\nPager.prototype.lastUpdate = function () {\n  if (!this.updates || !this.updates.length) return null\n  var page = this.updates.pop()\n  page.updated = false\n  return page\n}\n\nPager.prototype._array = function (i, noAllocate) {\n  if (i >= this.maxPages) {\n    if (noAllocate) return\n    grow(this, i)\n  }\n\n  factor(i, this.path)\n\n  var arr = this.pages\n\n  for (var j = this.level; j > 0; j--) {\n    var p = this.path[j]\n    var next = arr[p]\n\n    if (!next) {\n      if (noAllocate) return\n      next = arr[p] = new Array(32768)\n    }\n\n    arr = next\n  }\n\n  return arr\n}\n\nPager.prototype.get = function (i, noAllocate) {\n  var arr = this._array(i, noAllocate)\n  var first = this.path[0]\n  var page = arr && arr[first]\n\n  if (!page && !noAllocate) {\n    page = arr[first] = new Page(i, alloc(this.pageSize))\n    if (i >= this.length) this.length = i + 1\n  }\n\n  if (page && page.buffer === this.deduplicate && this.deduplicate && !noAllocate) {\n    page.buffer = copy(page.buffer)\n    page.deduplicate = 0\n  }\n\n  return page\n}\n\nPager.prototype.set = function (i, buf) {\n  var arr = this._array(i, false)\n  var first = this.path[0]\n\n  if (i >= this.length) this.length = i + 1\n\n  if (!buf || (this.zeros && buf.equals && buf.equals(this.zeros))) {\n    arr[first] = undefined\n    return\n  }\n\n  if (this.deduplicate && buf.equals && buf.equals(this.deduplicate)) {\n    buf = this.deduplicate\n  }\n\n  var page = arr[first]\n  var b = truncate(buf, this.pageSize)\n\n  if (page) page.buffer = b\n  else arr[first] = new Page(i, b)\n}\n\nPager.prototype.toBuffer = function () {\n  var list = new Array(this.length)\n  var empty = alloc(this.pageSize)\n  var ptr = 0\n\n  while (ptr < list.length) {\n    var arr = this._array(ptr, true)\n    for (var i = 0; i < 32768 && ptr < list.length; i++) {\n      list[ptr++] = (arr && arr[i]) ? arr[i].buffer : empty\n    }\n  }\n\n  return Buffer.concat(list)\n}\n\nfunction grow (pager, index) {\n  while (pager.maxPages < index) {\n    var old = pager.pages\n    pager.pages = new Array(32768)\n    pager.pages[0] = old\n    pager.level++\n    pager.maxPages *= 32768\n  }\n}\n\nfunction truncate (buf, len) {\n  if (buf.length === len) return buf\n  if (buf.length > len) return buf.slice(0, len)\n  var cpy = alloc(len)\n  buf.copy(cpy)\n  return cpy\n}\n\nfunction alloc (size) {\n  if (Buffer.alloc) return Buffer.alloc(size)\n  var buf = new Buffer(size)\n  buf.fill(0)\n  return buf\n}\n\nfunction copy (buf) {\n  var cpy = Buffer.allocUnsafe ? Buffer.allocUnsafe(buf.length) : new Buffer(buf.length)\n  buf.copy(cpy)\n  return cpy\n}\n\nfunction Page (i, buf) {\n  this.offset = i * buf.length\n  this.buffer = buf\n  this.updated = false\n  this.deduplicate = 0\n}\n\nfunction factor (n, out) {\n  n = (n - (out[0] = (n & 32767))) / 32768\n  n = (n - (out[1] = (n & 32767))) / 32768\n  out[3] = ((n - (out[2] = (n & 32767))) / 32768) & 32767\n}\n"], "mappings": "AAAAA,MAAM,CAACC,OAAO,GAAGC,KAAK;AAEtB,SAASA,KAAKA,CAAEC,QAAQ,EAAEC,IAAI,EAAE;EAC9B,IAAI,EAAE,IAAI,YAAYF,KAAK,CAAC,EAAE,OAAO,IAAIA,KAAK,CAACC,QAAQ,EAAEC,IAAI,CAAC;EAE9D,IAAI,CAACC,MAAM,GAAG,CAAC;EACf,IAAI,CAACC,OAAO,GAAG,EAAE;EACjB,IAAI,CAACC,IAAI,GAAG,IAAIC,WAAW,CAAC,CAAC,CAAC;EAC9B,IAAI,CAACC,KAAK,GAAG,IAAIC,KAAK,CAAC,KAAK,CAAC;EAC7B,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACF,KAAK,CAACJ,MAAM;EACjC,IAAI,CAACO,KAAK,GAAG,CAAC;EACd,IAAI,CAACT,QAAQ,GAAGA,QAAQ,IAAI,IAAI;EAChC,IAAI,CAACU,WAAW,GAAGT,IAAI,GAAGA,IAAI,CAACS,WAAW,GAAG,IAAI;EACjD,IAAI,CAACC,KAAK,GAAG,IAAI,CAACD,WAAW,GAAGE,KAAK,CAAC,IAAI,CAACF,WAAW,CAACR,MAAM,CAAC,GAAG,IAAI;AACvE;AAEAH,KAAK,CAACc,SAAS,CAACC,OAAO,GAAG,UAAUC,IAAI,EAAE;EACxC,OAAO,IAAI,CAACL,WAAW,IAAIK,IAAI,CAACC,MAAM,CAACD,IAAI,CAACL,WAAW,CAAC,KAAK,IAAI,CAACA,WAAW,CAACK,IAAI,CAACL,WAAW,CAAC,EAAE;IAC/FK,IAAI,CAACL,WAAW,EAAE;IAClB,IAAIK,IAAI,CAACL,WAAW,KAAK,IAAI,CAACA,WAAW,CAACR,MAAM,EAAE;MAChDa,IAAI,CAACL,WAAW,GAAG,CAAC;MACpB,IAAIK,IAAI,CAACC,MAAM,CAACC,MAAM,IAAIF,IAAI,CAACC,MAAM,CAACC,MAAM,CAAC,IAAI,CAACP,WAAW,CAAC,EAAEK,IAAI,CAACC,MAAM,GAAG,IAAI,CAACN,WAAW;MAC9F;IACF;EACF;EACA,IAAIK,IAAI,CAACD,OAAO,IAAI,CAAC,IAAI,CAACX,OAAO,EAAE;EACnCY,IAAI,CAACD,OAAO,GAAG,IAAI;EACnB,IAAI,CAACX,OAAO,CAACe,IAAI,CAACH,IAAI,CAAC;AACzB,CAAC;AAEDhB,KAAK,CAACc,SAAS,CAACM,UAAU,GAAG,YAAY;EACvC,IAAI,CAAC,IAAI,CAAChB,OAAO,IAAI,CAAC,IAAI,CAACA,OAAO,CAACD,MAAM,EAAE,OAAO,IAAI;EACtD,IAAIa,IAAI,GAAG,IAAI,CAACZ,OAAO,CAACiB,GAAG,CAAC,CAAC;EAC7BL,IAAI,CAACD,OAAO,GAAG,KAAK;EACpB,OAAOC,IAAI;AACb,CAAC;AAEDhB,KAAK,CAACc,SAAS,CAACQ,MAAM,GAAG,UAAUC,CAAC,EAAEC,UAAU,EAAE;EAChD,IAAID,CAAC,IAAI,IAAI,CAACd,QAAQ,EAAE;IACtB,IAAIe,UAAU,EAAE;IAChBC,IAAI,CAAC,IAAI,EAAEF,CAAC,CAAC;EACf;EAEAG,MAAM,CAACH,CAAC,EAAE,IAAI,CAAClB,IAAI,CAAC;EAEpB,IAAIsB,GAAG,GAAG,IAAI,CAACpB,KAAK;EAEpB,KAAK,IAAIqB,CAAC,GAAG,IAAI,CAAClB,KAAK,EAAEkB,CAAC,GAAG,CAAC,EAAEA,CAAC,EAAE,EAAE;IACnC,IAAIC,CAAC,GAAG,IAAI,CAACxB,IAAI,CAACuB,CAAC,CAAC;IACpB,IAAIE,IAAI,GAAGH,GAAG,CAACE,CAAC,CAAC;IAEjB,IAAI,CAACC,IAAI,EAAE;MACT,IAAIN,UAAU,EAAE;MAChBM,IAAI,GAAGH,GAAG,CAACE,CAAC,CAAC,GAAG,IAAIrB,KAAK,CAAC,KAAK,CAAC;IAClC;IAEAmB,GAAG,GAAGG,IAAI;EACZ;EAEA,OAAOH,GAAG;AACZ,CAAC;AAED3B,KAAK,CAACc,SAAS,CAACiB,GAAG,GAAG,UAAUR,CAAC,EAAEC,UAAU,EAAE;EAC7C,IAAIG,GAAG,GAAG,IAAI,CAACL,MAAM,CAACC,CAAC,EAAEC,UAAU,CAAC;EACpC,IAAIQ,KAAK,GAAG,IAAI,CAAC3B,IAAI,CAAC,CAAC,CAAC;EACxB,IAAIW,IAAI,GAAGW,GAAG,IAAIA,GAAG,CAACK,KAAK,CAAC;EAE5B,IAAI,CAAChB,IAAI,IAAI,CAACQ,UAAU,EAAE;IACxBR,IAAI,GAAGW,GAAG,CAACK,KAAK,CAAC,GAAG,IAAIC,IAAI,CAACV,CAAC,EAAEV,KAAK,CAAC,IAAI,CAACZ,QAAQ,CAAC,CAAC;IACrD,IAAIsB,CAAC,IAAI,IAAI,CAACpB,MAAM,EAAE,IAAI,CAACA,MAAM,GAAGoB,CAAC,GAAG,CAAC;EAC3C;EAEA,IAAIP,IAAI,IAAIA,IAAI,CAACC,MAAM,KAAK,IAAI,CAACN,WAAW,IAAI,IAAI,CAACA,WAAW,IAAI,CAACa,UAAU,EAAE;IAC/ER,IAAI,CAACC,MAAM,GAAGiB,IAAI,CAAClB,IAAI,CAACC,MAAM,CAAC;IAC/BD,IAAI,CAACL,WAAW,GAAG,CAAC;EACtB;EAEA,OAAOK,IAAI;AACb,CAAC;AAEDhB,KAAK,CAACc,SAAS,CAACqB,GAAG,GAAG,UAAUZ,CAAC,EAAEa,GAAG,EAAE;EACtC,IAAIT,GAAG,GAAG,IAAI,CAACL,MAAM,CAACC,CAAC,EAAE,KAAK,CAAC;EAC/B,IAAIS,KAAK,GAAG,IAAI,CAAC3B,IAAI,CAAC,CAAC,CAAC;EAExB,IAAIkB,CAAC,IAAI,IAAI,CAACpB,MAAM,EAAE,IAAI,CAACA,MAAM,GAAGoB,CAAC,GAAG,CAAC;EAEzC,IAAI,CAACa,GAAG,IAAK,IAAI,CAACxB,KAAK,IAAIwB,GAAG,CAAClB,MAAM,IAAIkB,GAAG,CAAClB,MAAM,CAAC,IAAI,CAACN,KAAK,CAAE,EAAE;IAChEe,GAAG,CAACK,KAAK,CAAC,GAAGK,SAAS;IACtB;EACF;EAEA,IAAI,IAAI,CAAC1B,WAAW,IAAIyB,GAAG,CAAClB,MAAM,IAAIkB,GAAG,CAAClB,MAAM,CAAC,IAAI,CAACP,WAAW,CAAC,EAAE;IAClEyB,GAAG,GAAG,IAAI,CAACzB,WAAW;EACxB;EAEA,IAAIK,IAAI,GAAGW,GAAG,CAACK,KAAK,CAAC;EACrB,IAAIM,CAAC,GAAGC,QAAQ,CAACH,GAAG,EAAE,IAAI,CAACnC,QAAQ,CAAC;EAEpC,IAAIe,IAAI,EAAEA,IAAI,CAACC,MAAM,GAAGqB,CAAC,MACpBX,GAAG,CAACK,KAAK,CAAC,GAAG,IAAIC,IAAI,CAACV,CAAC,EAAEe,CAAC,CAAC;AAClC,CAAC;AAEDtC,KAAK,CAACc,SAAS,CAAC0B,QAAQ,GAAG,YAAY;EACrC,IAAIC,IAAI,GAAG,IAAIjC,KAAK,CAAC,IAAI,CAACL,MAAM,CAAC;EACjC,IAAIuC,KAAK,GAAG7B,KAAK,CAAC,IAAI,CAACZ,QAAQ,CAAC;EAChC,IAAI0C,GAAG,GAAG,CAAC;EAEX,OAAOA,GAAG,GAAGF,IAAI,CAACtC,MAAM,EAAE;IACxB,IAAIwB,GAAG,GAAG,IAAI,CAACL,MAAM,CAACqB,GAAG,EAAE,IAAI,CAAC;IAChC,KAAK,IAAIpB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,KAAK,IAAIoB,GAAG,GAAGF,IAAI,CAACtC,MAAM,EAAEoB,CAAC,EAAE,EAAE;MACnDkB,IAAI,CAACE,GAAG,EAAE,CAAC,GAAIhB,GAAG,IAAIA,GAAG,CAACJ,CAAC,CAAC,GAAII,GAAG,CAACJ,CAAC,CAAC,CAACN,MAAM,GAAGyB,KAAK;IACvD;EACF;EAEA,OAAOE,MAAM,CAACC,MAAM,CAACJ,IAAI,CAAC;AAC5B,CAAC;AAED,SAAShB,IAAIA,CAAEqB,KAAK,EAAEC,KAAK,EAAE;EAC3B,OAAOD,KAAK,CAACrC,QAAQ,GAAGsC,KAAK,EAAE;IAC7B,IAAIC,GAAG,GAAGF,KAAK,CAACvC,KAAK;IACrBuC,KAAK,CAACvC,KAAK,GAAG,IAAIC,KAAK,CAAC,KAAK,CAAC;IAC9BsC,KAAK,CAACvC,KAAK,CAAC,CAAC,CAAC,GAAGyC,GAAG;IACpBF,KAAK,CAACpC,KAAK,EAAE;IACboC,KAAK,CAACrC,QAAQ,IAAI,KAAK;EACzB;AACF;AAEA,SAAS8B,QAAQA,CAAEH,GAAG,EAAEa,GAAG,EAAE;EAC3B,IAAIb,GAAG,CAACjC,MAAM,KAAK8C,GAAG,EAAE,OAAOb,GAAG;EAClC,IAAIA,GAAG,CAACjC,MAAM,GAAG8C,GAAG,EAAE,OAAOb,GAAG,CAACc,KAAK,CAAC,CAAC,EAAED,GAAG,CAAC;EAC9C,IAAIE,GAAG,GAAGtC,KAAK,CAACoC,GAAG,CAAC;EACpBb,GAAG,CAACF,IAAI,CAACiB,GAAG,CAAC;EACb,OAAOA,GAAG;AACZ;AAEA,SAAStC,KAAKA,CAAEuC,IAAI,EAAE;EACpB,IAAIR,MAAM,CAAC/B,KAAK,EAAE,OAAO+B,MAAM,CAAC/B,KAAK,CAACuC,IAAI,CAAC;EAC3C,IAAIhB,GAAG,GAAG,IAAIQ,MAAM,CAACQ,IAAI,CAAC;EAC1BhB,GAAG,CAACiB,IAAI,CAAC,CAAC,CAAC;EACX,OAAOjB,GAAG;AACZ;AAEA,SAASF,IAAIA,CAAEE,GAAG,EAAE;EAClB,IAAIe,GAAG,GAAGP,MAAM,CAACU,WAAW,GAAGV,MAAM,CAACU,WAAW,CAAClB,GAAG,CAACjC,MAAM,CAAC,GAAG,IAAIyC,MAAM,CAACR,GAAG,CAACjC,MAAM,CAAC;EACtFiC,GAAG,CAACF,IAAI,CAACiB,GAAG,CAAC;EACb,OAAOA,GAAG;AACZ;AAEA,SAASlB,IAAIA,CAAEV,CAAC,EAAEa,GAAG,EAAE;EACrB,IAAI,CAACmB,MAAM,GAAGhC,CAAC,GAAGa,GAAG,CAACjC,MAAM;EAC5B,IAAI,CAACc,MAAM,GAAGmB,GAAG;EACjB,IAAI,CAACrB,OAAO,GAAG,KAAK;EACpB,IAAI,CAACJ,WAAW,GAAG,CAAC;AACtB;AAEA,SAASe,MAAMA,CAAE8B,CAAC,EAAEC,GAAG,EAAE;EACvBD,CAAC,GAAG,CAACA,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC,GAAID,CAAC,GAAG,KAAM,CAAC,IAAI,KAAK;EACxCA,CAAC,GAAG,CAACA,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC,GAAID,CAAC,GAAG,KAAM,CAAC,IAAI,KAAK;EACxCC,GAAG,CAAC,CAAC,CAAC,GAAI,CAACD,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC,GAAID,CAAC,GAAG,KAAM,CAAC,IAAI,KAAK,GAAI,KAAK;AACzD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}