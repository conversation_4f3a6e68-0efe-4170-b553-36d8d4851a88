{"ast": null, "code": "'use strict';\n\n/** @type {import('./pow')} */\nmodule.exports = Math.pow;", "map": {"version": 3, "names": ["module", "exports", "Math", "pow"], "sources": ["C:/Users/<USER>/node_modules/math-intrinsics/pow.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./pow')} */\nmodule.exports = Math.pow;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAGC,IAAI,CAACC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}