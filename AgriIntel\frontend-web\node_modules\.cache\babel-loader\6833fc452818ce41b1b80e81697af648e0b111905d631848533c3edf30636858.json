{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.StateMachine = void 0;\nconst fs = require(\"fs/promises\");\nconst net = require(\"net\");\nconst tls = require(\"tls\");\nconst bson_1 = require(\"../bson\");\nconst abstract_cursor_1 = require(\"../cursor/abstract_cursor\");\nconst deps_1 = require(\"../deps\");\nconst error_1 = require(\"../error\");\nconst timeout_1 = require(\"../timeout\");\nconst utils_1 = require(\"../utils\");\nconst client_encryption_1 = require(\"./client_encryption\");\nconst errors_1 = require(\"./errors\");\nlet socks = null;\nfunction loadSocks() {\n  if (socks == null) {\n    const socksImport = (0, deps_1.getSocks)();\n    if ('kModuleError' in socksImport) {\n      throw socksImport.kModuleError;\n    }\n    socks = socksImport;\n  }\n  return socks;\n}\n// libmongocrypt states\nconst MONGOCRYPT_CTX_ERROR = 0;\nconst MONGOCRYPT_CTX_NEED_MONGO_COLLINFO = 1;\nconst MONGOCRYPT_CTX_NEED_MONGO_MARKINGS = 2;\nconst MONGOCRYPT_CTX_NEED_MONGO_KEYS = 3;\nconst MONGOCRYPT_CTX_NEED_KMS_CREDENTIALS = 7;\nconst MONGOCRYPT_CTX_NEED_KMS = 4;\nconst MONGOCRYPT_CTX_READY = 5;\nconst MONGOCRYPT_CTX_DONE = 6;\nconst HTTPS_PORT = 443;\nconst stateToString = new Map([[MONGOCRYPT_CTX_ERROR, 'MONGOCRYPT_CTX_ERROR'], [MONGOCRYPT_CTX_NEED_MONGO_COLLINFO, 'MONGOCRYPT_CTX_NEED_MONGO_COLLINFO'], [MONGOCRYPT_CTX_NEED_MONGO_MARKINGS, 'MONGOCRYPT_CTX_NEED_MONGO_MARKINGS'], [MONGOCRYPT_CTX_NEED_MONGO_KEYS, 'MONGOCRYPT_CTX_NEED_MONGO_KEYS'], [MONGOCRYPT_CTX_NEED_KMS_CREDENTIALS, 'MONGOCRYPT_CTX_NEED_KMS_CREDENTIALS'], [MONGOCRYPT_CTX_NEED_KMS, 'MONGOCRYPT_CTX_NEED_KMS'], [MONGOCRYPT_CTX_READY, 'MONGOCRYPT_CTX_READY'], [MONGOCRYPT_CTX_DONE, 'MONGOCRYPT_CTX_DONE']]);\nconst INSECURE_TLS_OPTIONS = ['tlsInsecure', 'tlsAllowInvalidCertificates', 'tlsAllowInvalidHostnames',\n// These options are disallowed by the spec, so we explicitly filter them out if provided, even\n// though the StateMachine does not declare support for these options.\n'tlsDisableOCSPEndpointCheck', 'tlsDisableCertificateRevocationCheck'];\n/**\n * Helper function for logging. Enabled by setting the environment flag MONGODB_CRYPT_DEBUG.\n * @param msg - Anything you want to be logged.\n */\nfunction debug(msg) {\n  if (process.env.MONGODB_CRYPT_DEBUG) {\n    // eslint-disable-next-line no-console\n    console.error(msg);\n  }\n}\n/**\n * This is kind of a hack.  For `rewrapManyDataKey`, we have tests that\n * guarantee that when there are no matching keys, `rewrapManyDataKey` returns\n * nothing.  We also have tests for auto encryption that guarantee for `encrypt`\n * we return an error when there are no matching keys.  This error is generated in\n * subsequent iterations of the state machine.\n * Some apis (`encrypt`) throw if there are no filter matches and others (`rewrapManyDataKey`)\n * do not.  We set the result manually here, and let the state machine continue.  `libmongocrypt`\n * will inform us if we need to error by setting the state to `MONGOCRYPT_CTX_ERROR` but\n * otherwise we'll return `{ v: [] }`.\n */\nlet EMPTY_V;\n/**\n * @internal\n * An internal class that executes across a MongoCryptContext until either\n * a finishing state or an error is reached. Do not instantiate directly.\n */\n// TODO(DRIVERS-2671): clarify CSOT behavior for FLE APIs\nclass StateMachine {\n  constructor(options, bsonOptions = (0, bson_1.pluckBSONSerializeOptions)(options)) {\n    this.options = options;\n    this.bsonOptions = bsonOptions;\n  }\n  /**\n   * Executes the state machine according to the specification\n   */\n  async execute(executor, context, options) {\n    const keyVaultNamespace = executor._keyVaultNamespace;\n    const keyVaultClient = executor._keyVaultClient;\n    const metaDataClient = executor._metaDataClient;\n    const mongocryptdClient = executor._mongocryptdClient;\n    const mongocryptdManager = executor._mongocryptdManager;\n    let result = null;\n    // Typescript treats getters just like properties: Once you've tested it for equality\n    // it cannot change. Which is exactly the opposite of what we use state and status for.\n    // Every call to at least `addMongoOperationResponse` and `finalize` can change the state.\n    // These wrappers let us write code more naturally and not add compiler exceptions\n    // to conditions checks inside the state machine.\n    const getStatus = () => context.status;\n    const getState = () => context.state;\n    while (getState() !== MONGOCRYPT_CTX_DONE && getState() !== MONGOCRYPT_CTX_ERROR) {\n      options.signal?.throwIfAborted();\n      debug(`[context#${context.id}] ${stateToString.get(getState()) || getState()}`);\n      switch (getState()) {\n        case MONGOCRYPT_CTX_NEED_MONGO_COLLINFO:\n          {\n            const filter = (0, bson_1.deserialize)(context.nextMongoOperation());\n            if (!metaDataClient) {\n              throw new errors_1.MongoCryptError('unreachable state machine state: entered MONGOCRYPT_CTX_NEED_MONGO_COLLINFO but metadata client is undefined');\n            }\n            const collInfoCursor = this.fetchCollectionInfo(metaDataClient, context.ns, filter, options);\n            for await (const collInfo of collInfoCursor) {\n              context.addMongoOperationResponse((0, bson_1.serialize)(collInfo));\n              if (getState() === MONGOCRYPT_CTX_ERROR) break;\n            }\n            if (getState() === MONGOCRYPT_CTX_ERROR) break;\n            context.finishMongoOperation();\n            break;\n          }\n        case MONGOCRYPT_CTX_NEED_MONGO_MARKINGS:\n          {\n            const command = context.nextMongoOperation();\n            if (getState() === MONGOCRYPT_CTX_ERROR) break;\n            if (!mongocryptdClient) {\n              throw new errors_1.MongoCryptError('unreachable state machine state: entered MONGOCRYPT_CTX_NEED_MONGO_MARKINGS but mongocryptdClient is undefined');\n            }\n            // When we are using the shared library, we don't have a mongocryptd manager.\n            const markedCommand = mongocryptdManager ? await mongocryptdManager.withRespawn(this.markCommand.bind(this, mongocryptdClient, context.ns, command, options)) : await this.markCommand(mongocryptdClient, context.ns, command, options);\n            context.addMongoOperationResponse(markedCommand);\n            context.finishMongoOperation();\n            break;\n          }\n        case MONGOCRYPT_CTX_NEED_MONGO_KEYS:\n          {\n            const filter = context.nextMongoOperation();\n            const keys = await this.fetchKeys(keyVaultClient, keyVaultNamespace, filter, options);\n            if (keys.length === 0) {\n              // See docs on EMPTY_V\n              result = EMPTY_V ??= (0, bson_1.serialize)({\n                v: []\n              });\n            }\n            for await (const key of keys) {\n              context.addMongoOperationResponse((0, bson_1.serialize)(key));\n            }\n            context.finishMongoOperation();\n            break;\n          }\n        case MONGOCRYPT_CTX_NEED_KMS_CREDENTIALS:\n          {\n            const kmsProviders = await executor.askForKMSCredentials();\n            context.provideKMSProviders((0, bson_1.serialize)(kmsProviders));\n            break;\n          }\n        case MONGOCRYPT_CTX_NEED_KMS:\n          {\n            await Promise.all(this.requests(context, options));\n            context.finishKMSRequests();\n            break;\n          }\n        case MONGOCRYPT_CTX_READY:\n          {\n            const finalizedContext = context.finalize();\n            if (getState() === MONGOCRYPT_CTX_ERROR) {\n              const message = getStatus().message || 'Finalization error';\n              throw new errors_1.MongoCryptError(message);\n            }\n            result = finalizedContext;\n            break;\n          }\n        default:\n          throw new errors_1.MongoCryptError(`Unknown state: ${getState()}`);\n      }\n    }\n    if (getState() === MONGOCRYPT_CTX_ERROR || result == null) {\n      const message = getStatus().message;\n      if (!message) {\n        debug(`unidentifiable error in MongoCrypt - received an error status from \\`libmongocrypt\\` but received no error message.`);\n      }\n      throw new errors_1.MongoCryptError(message ?? 'unidentifiable error in MongoCrypt - received an error status from `libmongocrypt` but received no error message.');\n    }\n    return result;\n  }\n  /**\n   * Handles the request to the KMS service. Exposed for testing purposes. Do not directly invoke.\n   * @param kmsContext - A C++ KMS context returned from the bindings\n   * @returns A promise that resolves when the KMS reply has be fully parsed\n   */\n  async kmsRequest(request, options) {\n    const parsedUrl = request.endpoint.split(':');\n    const port = parsedUrl[1] != null ? Number.parseInt(parsedUrl[1], 10) : HTTPS_PORT;\n    const socketOptions = {\n      host: parsedUrl[0],\n      servername: parsedUrl[0],\n      port,\n      ...(0, client_encryption_1.autoSelectSocketOptions)(this.options.socketOptions || {})\n    };\n    const message = request.message;\n    const buffer = new utils_1.BufferPool();\n    let netSocket;\n    let socket;\n    function destroySockets() {\n      for (const sock of [socket, netSocket]) {\n        if (sock) {\n          sock.destroy();\n        }\n      }\n    }\n    function onerror(cause) {\n      return new errors_1.MongoCryptError('KMS request failed', {\n        cause\n      });\n    }\n    function onclose() {\n      return new errors_1.MongoCryptError('KMS request closed');\n    }\n    const tlsOptions = this.options.tlsOptions;\n    if (tlsOptions) {\n      const kmsProvider = request.kmsProvider;\n      const providerTlsOptions = tlsOptions[kmsProvider];\n      if (providerTlsOptions) {\n        const error = this.validateTlsOptions(kmsProvider, providerTlsOptions);\n        if (error) {\n          throw error;\n        }\n        try {\n          await this.setTlsOptions(providerTlsOptions, socketOptions);\n        } catch (err) {\n          throw onerror(err);\n        }\n      }\n    }\n    let abortListener;\n    try {\n      if (this.options.proxyOptions && this.options.proxyOptions.proxyHost) {\n        netSocket = new net.Socket();\n        const {\n          promise: willConnect,\n          reject: rejectOnNetSocketError,\n          resolve: resolveOnNetSocketConnect\n        } = (0, utils_1.promiseWithResolvers)();\n        netSocket.once('error', err => rejectOnNetSocketError(onerror(err))).once('close', () => rejectOnNetSocketError(onclose())).once('connect', () => resolveOnNetSocketConnect());\n        const netSocketOptions = {\n          ...socketOptions,\n          host: this.options.proxyOptions.proxyHost,\n          port: this.options.proxyOptions.proxyPort || 1080\n        };\n        netSocket.connect(netSocketOptions);\n        await willConnect;\n        try {\n          socks ??= loadSocks();\n          socketOptions.socket = (await socks.SocksClient.createConnection({\n            existing_socket: netSocket,\n            command: 'connect',\n            destination: {\n              host: socketOptions.host,\n              port: socketOptions.port\n            },\n            proxy: {\n              // host and port are ignored because we pass existing_socket\n              host: 'iLoveJavaScript',\n              port: 0,\n              type: 5,\n              userId: this.options.proxyOptions.proxyUsername,\n              password: this.options.proxyOptions.proxyPassword\n            }\n          })).socket;\n        } catch (err) {\n          throw onerror(err);\n        }\n      }\n      socket = tls.connect(socketOptions, () => {\n        socket.write(message);\n      });\n      const {\n        promise: willResolveKmsRequest,\n        reject: rejectOnTlsSocketError,\n        resolve\n      } = (0, utils_1.promiseWithResolvers)();\n      abortListener = (0, utils_1.addAbortListener)(options?.signal, function () {\n        destroySockets();\n        rejectOnTlsSocketError(this.reason);\n      });\n      socket.once('error', err => rejectOnTlsSocketError(onerror(err))).once('close', () => rejectOnTlsSocketError(onclose())).on('data', data => {\n        buffer.append(data);\n        while (request.bytesNeeded > 0 && buffer.length) {\n          const bytesNeeded = Math.min(request.bytesNeeded, buffer.length);\n          request.addResponse(buffer.read(bytesNeeded));\n        }\n        if (request.bytesNeeded <= 0) {\n          resolve();\n        }\n      });\n      await (options?.timeoutContext?.csotEnabled() ? Promise.all([willResolveKmsRequest, timeout_1.Timeout.expires(options.timeoutContext?.remainingTimeMS)]) : willResolveKmsRequest);\n    } catch (error) {\n      if (error instanceof timeout_1.TimeoutError) throw new error_1.MongoOperationTimeoutError('KMS request timed out');\n      throw error;\n    } finally {\n      // There's no need for any more activity on this socket at this point.\n      destroySockets();\n      abortListener?.[utils_1.kDispose]();\n    }\n  }\n  *requests(context, options) {\n    for (let request = context.nextKMSRequest(); request != null; request = context.nextKMSRequest()) {\n      yield this.kmsRequest(request, options);\n    }\n  }\n  /**\n   * Validates the provided TLS options are secure.\n   *\n   * @param kmsProvider - The KMS provider name.\n   * @param tlsOptions - The client TLS options for the provider.\n   *\n   * @returns An error if any option is invalid.\n   */\n  validateTlsOptions(kmsProvider, tlsOptions) {\n    const tlsOptionNames = Object.keys(tlsOptions);\n    for (const option of INSECURE_TLS_OPTIONS) {\n      if (tlsOptionNames.includes(option)) {\n        return new errors_1.MongoCryptError(`Insecure TLS options prohibited for ${kmsProvider}: ${option}`);\n      }\n    }\n  }\n  /**\n   * Sets only the valid secure TLS options.\n   *\n   * @param tlsOptions - The client TLS options for the provider.\n   * @param options - The existing connection options.\n   */\n  async setTlsOptions(tlsOptions, options) {\n    if (tlsOptions.tlsCertificateKeyFile) {\n      const cert = await fs.readFile(tlsOptions.tlsCertificateKeyFile);\n      options.cert = options.key = cert;\n    }\n    if (tlsOptions.tlsCAFile) {\n      options.ca = await fs.readFile(tlsOptions.tlsCAFile);\n    }\n    if (tlsOptions.tlsCertificateKeyFilePassword) {\n      options.passphrase = tlsOptions.tlsCertificateKeyFilePassword;\n    }\n  }\n  /**\n   * Fetches collection info for a provided namespace, when libmongocrypt\n   * enters the `MONGOCRYPT_CTX_NEED_MONGO_COLLINFO` state. The result is\n   * used to inform libmongocrypt of the schema associated with this\n   * namespace. Exposed for testing purposes. Do not directly invoke.\n   *\n   * @param client - A MongoClient connected to the topology\n   * @param ns - The namespace to list collections from\n   * @param filter - A filter for the listCollections command\n   * @param callback - Invoked with the info of the requested collection, or with an error\n   */\n  fetchCollectionInfo(client, ns, filter, options) {\n    const {\n      db\n    } = utils_1.MongoDBCollectionNamespace.fromString(ns);\n    const cursor = client.db(db).listCollections(filter, {\n      promoteLongs: false,\n      promoteValues: false,\n      timeoutContext: options?.timeoutContext && new abstract_cursor_1.CursorTimeoutContext(options?.timeoutContext, Symbol()),\n      signal: options?.signal,\n      nameOnly: false\n    });\n    return cursor;\n  }\n  /**\n   * Calls to the mongocryptd to provide markings for a command.\n   * Exposed for testing purposes. Do not directly invoke.\n   * @param client - A MongoClient connected to a mongocryptd\n   * @param ns - The namespace (database.collection) the command is being executed on\n   * @param command - The command to execute.\n   * @param callback - Invoked with the serialized and marked bson command, or with an error\n   */\n  async markCommand(client, ns, command, options) {\n    const {\n      db\n    } = utils_1.MongoDBCollectionNamespace.fromString(ns);\n    const bsonOptions = {\n      promoteLongs: false,\n      promoteValues: false\n    };\n    const rawCommand = (0, bson_1.deserialize)(command, bsonOptions);\n    const commandOptions = {\n      timeoutMS: undefined,\n      signal: undefined\n    };\n    if (options?.timeoutContext?.csotEnabled()) {\n      commandOptions.timeoutMS = options.timeoutContext.remainingTimeMS;\n    }\n    if (options?.signal) {\n      commandOptions.signal = options.signal;\n    }\n    const response = await client.db(db).command(rawCommand, {\n      ...bsonOptions,\n      ...commandOptions\n    });\n    return (0, bson_1.serialize)(response, this.bsonOptions);\n  }\n  /**\n   * Requests keys from the keyVault collection on the topology.\n   * Exposed for testing purposes. Do not directly invoke.\n   * @param client - A MongoClient connected to the topology\n   * @param keyVaultNamespace - The namespace (database.collection) of the keyVault Collection\n   * @param filter - The filter for the find query against the keyVault Collection\n   * @param callback - Invoked with the found keys, or with an error\n   */\n  fetchKeys(client, keyVaultNamespace, filter, options) {\n    const {\n      db: dbName,\n      collection: collectionName\n    } = utils_1.MongoDBCollectionNamespace.fromString(keyVaultNamespace);\n    const commandOptions = {\n      timeoutContext: undefined,\n      signal: undefined\n    };\n    if (options?.timeoutContext != null) {\n      commandOptions.timeoutContext = new abstract_cursor_1.CursorTimeoutContext(options.timeoutContext, Symbol());\n    }\n    if (options?.signal != null) {\n      commandOptions.signal = options.signal;\n    }\n    return client.db(dbName).collection(collectionName, {\n      readConcern: {\n        level: 'majority'\n      }\n    }).find((0, bson_1.deserialize)(filter), commandOptions).toArray();\n  }\n}\nexports.StateMachine = StateMachine;", "map": {"version": 3, "names": ["fs", "require", "net", "tls", "bson_1", "abstract_cursor_1", "deps_1", "error_1", "timeout_1", "utils_1", "client_encryption_1", "errors_1", "socks", "loadSocks", "socksImport", "getSocks", "kModuleError", "MONGOCRYPT_CTX_ERROR", "MONGOCRYPT_CTX_NEED_MONGO_COLLINFO", "MONGOCRYPT_CTX_NEED_MONGO_MARKINGS", "MONGOCRYPT_CTX_NEED_MONGO_KEYS", "MONGOCRYPT_CTX_NEED_KMS_CREDENTIALS", "MONGOCRYPT_CTX_NEED_KMS", "MONGOCRYPT_CTX_READY", "MONGOCRYPT_CTX_DONE", "HTTPS_PORT", "stateToString", "Map", "INSECURE_TLS_OPTIONS", "debug", "msg", "process", "env", "MONGODB_CRYPT_DEBUG", "console", "error", "EMPTY_V", "StateMachine", "constructor", "options", "bsonOptions", "pluckBSONSerializeOptions", "execute", "executor", "context", "keyVaultNamespace", "_keyVaultNamespace", "keyVaultClient", "_keyVaultClient", "metaDataClient", "_metaDataClient", "mongocryptdClient", "_mongocryptdClient", "mongocryptdManager", "_mongocryptdManager", "result", "getStatus", "status", "getState", "state", "signal", "throwIfAborted", "id", "get", "filter", "deserialize", "nextMongoOperation", "MongoCryptError", "collInfoCursor", "fetchCollectionInfo", "ns", "collInfo", "addMongoOperationResponse", "serialize", "finishMongoOperation", "command", "markedCommand", "withRespawn", "<PERSON><PERSON><PERSON><PERSON>", "bind", "keys", "fetch<PERSON>eys", "length", "v", "key", "kmsProviders", "askForKMSCredentials", "provideKMSProviders", "Promise", "all", "requests", "finishKMSRequests", "finalizedContext", "finalize", "message", "kmsRequest", "request", "parsedUrl", "endpoint", "split", "port", "Number", "parseInt", "socketOptions", "host", "servername", "autoSelectSocketOptions", "buffer", "BufferPool", "netSocket", "socket", "destroySockets", "sock", "destroy", "onerror", "cause", "onclose", "tlsOptions", "kmsProvider", "providerTlsOptions", "validateTlsOptions", "setTlsOptions", "err", "abortListener", "proxyOptions", "proxyHost", "Socket", "promise", "willConnect", "reject", "rejectOnNetSocketError", "resolve", "resolveOnNetSocketConnect", "promiseWithResolvers", "once", "netSocketOptions", "proxyPort", "connect", "SocksClient", "createConnection", "existing_socket", "destination", "proxy", "type", "userId", "proxyUsername", "password", "proxyPassword", "write", "willResolveKmsRequest", "rejectOnTlsSocketError", "addAbortListener", "reason", "on", "data", "append", "bytesNeeded", "Math", "min", "addResponse", "read", "timeoutContext", "csotEnabled", "Timeout", "expires", "remainingTimeMS", "TimeoutError", "MongoOperationTimeoutError", "kDispose", "nextKMSRequest", "tlsOptionNames", "Object", "option", "includes", "tlsCertificateKeyFile", "cert", "readFile", "tlsCAFile", "ca", "tlsCertificateKeyFilePassword", "passphrase", "client", "db", "MongoDBCollectionNamespace", "fromString", "cursor", "listCollections", "promoteLongs", "promoteValues", "CursorTimeoutContext", "Symbol", "nameOnly", "rawCommand", "commandOptions", "timeoutMS", "undefined", "response", "dbN<PERSON>", "collection", "collectionName", "readConcern", "level", "find", "toArray", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\client-side-encryption\\state_machine.ts"], "sourcesContent": ["import * as fs from 'fs/promises';\nimport { type MongoCryptContext, type MongoCryptKMSRequest } from 'mongodb-client-encryption';\nimport * as net from 'net';\nimport * as tls from 'tls';\n\nimport {\n  type BSONSerializeOptions,\n  deserialize,\n  type Document,\n  pluckBSONSerializeOptions,\n  serialize\n} from '../bson';\nimport { type ProxyOptions } from '../cmap/connection';\nimport { CursorTimeoutContext } from '../cursor/abstract_cursor';\nimport { getSocks, type SocksLib } from '../deps';\nimport { MongoOperationTimeoutError } from '../error';\nimport { type MongoClient, type MongoClientOptions } from '../mongo_client';\nimport { type Abortable } from '../mongo_types';\nimport { type CollectionInfo } from '../operations/list_collections';\nimport { Timeout, type TimeoutContext, TimeoutError } from '../timeout';\nimport {\n  addAbortListener,\n  BufferPool,\n  kDispose,\n  MongoDBCollectionNamespace,\n  promiseWithResolvers\n} from '../utils';\nimport { autoSelectSocketOptions, type DataKey } from './client_encryption';\nimport { MongoCryptError } from './errors';\nimport { type MongocryptdManager } from './mongocryptd_manager';\nimport { type KMSProviders } from './providers';\n\nlet socks: SocksLib | null = null;\nfunction loadSocks(): SocksLib {\n  if (socks == null) {\n    const socksImport = getSocks();\n    if ('kModuleError' in socksImport) {\n      throw socksImport.kModuleError;\n    }\n    socks = socksImport;\n  }\n  return socks;\n}\n\n// libmongocrypt states\nconst MONGOCRYPT_CTX_ERROR = 0;\nconst MONGOCRYPT_CTX_NEED_MONGO_COLLINFO = 1;\nconst MONGOCRYPT_CTX_NEED_MONGO_MARKINGS = 2;\nconst MONGOCRYPT_CTX_NEED_MONGO_KEYS = 3;\nconst MONGOCRYPT_CTX_NEED_KMS_CREDENTIALS = 7;\nconst MONGOCRYPT_CTX_NEED_KMS = 4;\nconst MONGOCRYPT_CTX_READY = 5;\nconst MONGOCRYPT_CTX_DONE = 6;\n\nconst HTTPS_PORT = 443;\n\nconst stateToString = new Map([\n  [MONGOCRYPT_CTX_ERROR, 'MONGOCRYPT_CTX_ERROR'],\n  [MONGOCRYPT_CTX_NEED_MONGO_COLLINFO, 'MONGOCRYPT_CTX_NEED_MONGO_COLLINFO'],\n  [MONGOCRYPT_CTX_NEED_MONGO_MARKINGS, 'MONGOCRYPT_CTX_NEED_MONGO_MARKINGS'],\n  [MONGOCRYPT_CTX_NEED_MONGO_KEYS, 'MONGOCRYPT_CTX_NEED_MONGO_KEYS'],\n  [MONGOCRYPT_CTX_NEED_KMS_CREDENTIALS, 'MONGOCRYPT_CTX_NEED_KMS_CREDENTIALS'],\n  [MONGOCRYPT_CTX_NEED_KMS, 'MONGOCRYPT_CTX_NEED_KMS'],\n  [MONGOCRYPT_CTX_READY, 'MONGOCRYPT_CTX_READY'],\n  [MONGOCRYPT_CTX_DONE, 'MONGOCRYPT_CTX_DONE']\n]);\n\nconst INSECURE_TLS_OPTIONS = [\n  'tlsInsecure',\n  'tlsAllowInvalidCertificates',\n  'tlsAllowInvalidHostnames',\n\n  // These options are disallowed by the spec, so we explicitly filter them out if provided, even\n  // though the StateMachine does not declare support for these options.\n  'tlsDisableOCSPEndpointCheck',\n  'tlsDisableCertificateRevocationCheck'\n];\n\n/**\n * Helper function for logging. Enabled by setting the environment flag MONGODB_CRYPT_DEBUG.\n * @param msg - Anything you want to be logged.\n */\nfunction debug(msg: unknown) {\n  if (process.env.MONGODB_CRYPT_DEBUG) {\n    // eslint-disable-next-line no-console\n    console.error(msg);\n  }\n}\n\ndeclare module 'mongodb-client-encryption' {\n  // the properties added to `MongoCryptContext` here are only used for the `StateMachine`'s\n  // execute method and are not part of the C++ bindings.\n  interface MongoCryptContext {\n    id: number;\n    document: Document;\n    ns: string;\n  }\n}\n\n/**\n * @public\n *\n * TLS options to use when connecting. The spec specifically calls out which insecure\n * tls options are not allowed:\n *\n *  - tlsAllowInvalidCertificates\n *  - tlsAllowInvalidHostnames\n *  - tlsInsecure\n *\n * These options are not included in the type, and are ignored if provided.\n */\nexport type ClientEncryptionTlsOptions = Pick<\n  MongoClientOptions,\n  'tlsCAFile' | 'tlsCertificateKeyFile' | 'tlsCertificateKeyFilePassword'\n>;\n\n/** @public */\nexport type CSFLEKMSTlsOptions = {\n  aws?: ClientEncryptionTlsOptions;\n  gcp?: ClientEncryptionTlsOptions;\n  kmip?: ClientEncryptionTlsOptions;\n  local?: ClientEncryptionTlsOptions;\n  azure?: ClientEncryptionTlsOptions;\n\n  [key: string]: ClientEncryptionTlsOptions | undefined;\n};\n\n/**\n * @public\n *\n * Socket options to use for KMS requests.\n */\nexport type ClientEncryptionSocketOptions = Pick<\n  MongoClientOptions,\n  'autoSelectFamily' | 'autoSelectFamilyAttemptTimeout'\n>;\n\n/**\n * This is kind of a hack.  For `rewrapManyDataKey`, we have tests that\n * guarantee that when there are no matching keys, `rewrapManyDataKey` returns\n * nothing.  We also have tests for auto encryption that guarantee for `encrypt`\n * we return an error when there are no matching keys.  This error is generated in\n * subsequent iterations of the state machine.\n * Some apis (`encrypt`) throw if there are no filter matches and others (`rewrapManyDataKey`)\n * do not.  We set the result manually here, and let the state machine continue.  `libmongocrypt`\n * will inform us if we need to error by setting the state to `MONGOCRYPT_CTX_ERROR` but\n * otherwise we'll return `{ v: [] }`.\n */\nlet EMPTY_V;\n\n/**\n * @internal\n *\n * An interface representing an object that can be passed to the `StateMachine.execute` method.\n *\n * Not all properties are required for all operations.\n */\nexport interface StateMachineExecutable {\n  _keyVaultNamespace: string;\n  _keyVaultClient: MongoClient;\n  askForKMSCredentials: () => Promise<KMSProviders>;\n\n  /** only used for auto encryption */\n  _metaDataClient?: MongoClient;\n  /** only used for auto encryption */\n  _mongocryptdClient?: MongoClient;\n  /** only used for auto encryption */\n  _mongocryptdManager?: MongocryptdManager;\n}\n\nexport type StateMachineOptions = {\n  /** socks5 proxy options, if set. */\n  proxyOptions: ProxyOptions;\n\n  /** TLS options for KMS requests, if set. */\n  tlsOptions: CSFLEKMSTlsOptions;\n\n  /** Socket specific options we support. */\n  socketOptions: ClientEncryptionSocketOptions;\n} & Pick<BSONSerializeOptions, 'promoteLongs' | 'promoteValues'>;\n\n/**\n * @internal\n * An internal class that executes across a MongoCryptContext until either\n * a finishing state or an error is reached. Do not instantiate directly.\n */\n// TODO(DRIVERS-2671): clarify CSOT behavior for FLE APIs\nexport class StateMachine {\n  constructor(\n    private options: StateMachineOptions,\n    private bsonOptions = pluckBSONSerializeOptions(options)\n  ) {}\n\n  /**\n   * Executes the state machine according to the specification\n   */\n  async execute(\n    executor: StateMachineExecutable,\n    context: MongoCryptContext,\n    options: { timeoutContext?: TimeoutContext } & Abortable\n  ): Promise<Uint8Array> {\n    const keyVaultNamespace = executor._keyVaultNamespace;\n    const keyVaultClient = executor._keyVaultClient;\n    const metaDataClient = executor._metaDataClient;\n    const mongocryptdClient = executor._mongocryptdClient;\n    const mongocryptdManager = executor._mongocryptdManager;\n    let result: Uint8Array | null = null;\n\n    // Typescript treats getters just like properties: Once you've tested it for equality\n    // it cannot change. Which is exactly the opposite of what we use state and status for.\n    // Every call to at least `addMongoOperationResponse` and `finalize` can change the state.\n    // These wrappers let us write code more naturally and not add compiler exceptions\n    // to conditions checks inside the state machine.\n    const getStatus = () => context.status;\n    const getState = () => context.state;\n\n    while (getState() !== MONGOCRYPT_CTX_DONE && getState() !== MONGOCRYPT_CTX_ERROR) {\n      options.signal?.throwIfAborted();\n      debug(`[context#${context.id}] ${stateToString.get(getState()) || getState()}`);\n\n      switch (getState()) {\n        case MONGOCRYPT_CTX_NEED_MONGO_COLLINFO: {\n          const filter = deserialize(context.nextMongoOperation());\n          if (!metaDataClient) {\n            throw new MongoCryptError(\n              'unreachable state machine state: entered MONGOCRYPT_CTX_NEED_MONGO_COLLINFO but metadata client is undefined'\n            );\n          }\n\n          const collInfoCursor = this.fetchCollectionInfo(\n            metaDataClient,\n            context.ns,\n            filter,\n            options\n          );\n\n          for await (const collInfo of collInfoCursor) {\n            context.addMongoOperationResponse(serialize(collInfo));\n            if (getState() === MONGOCRYPT_CTX_ERROR) break;\n          }\n\n          if (getState() === MONGOCRYPT_CTX_ERROR) break;\n\n          context.finishMongoOperation();\n          break;\n        }\n\n        case MONGOCRYPT_CTX_NEED_MONGO_MARKINGS: {\n          const command = context.nextMongoOperation();\n          if (getState() === MONGOCRYPT_CTX_ERROR) break;\n\n          if (!mongocryptdClient) {\n            throw new MongoCryptError(\n              'unreachable state machine state: entered MONGOCRYPT_CTX_NEED_MONGO_MARKINGS but mongocryptdClient is undefined'\n            );\n          }\n\n          // When we are using the shared library, we don't have a mongocryptd manager.\n          const markedCommand: Uint8Array = mongocryptdManager\n            ? await mongocryptdManager.withRespawn(\n                this.markCommand.bind(this, mongocryptdClient, context.ns, command, options)\n              )\n            : await this.markCommand(mongocryptdClient, context.ns, command, options);\n\n          context.addMongoOperationResponse(markedCommand);\n          context.finishMongoOperation();\n          break;\n        }\n\n        case MONGOCRYPT_CTX_NEED_MONGO_KEYS: {\n          const filter = context.nextMongoOperation();\n          const keys = await this.fetchKeys(keyVaultClient, keyVaultNamespace, filter, options);\n\n          if (keys.length === 0) {\n            // See docs on EMPTY_V\n            result = EMPTY_V ??= serialize({ v: [] });\n          }\n          for await (const key of keys) {\n            context.addMongoOperationResponse(serialize(key));\n          }\n\n          context.finishMongoOperation();\n\n          break;\n        }\n\n        case MONGOCRYPT_CTX_NEED_KMS_CREDENTIALS: {\n          const kmsProviders = await executor.askForKMSCredentials();\n          context.provideKMSProviders(serialize(kmsProviders));\n          break;\n        }\n\n        case MONGOCRYPT_CTX_NEED_KMS: {\n          await Promise.all(this.requests(context, options));\n          context.finishKMSRequests();\n          break;\n        }\n\n        case MONGOCRYPT_CTX_READY: {\n          const finalizedContext = context.finalize();\n          if (getState() === MONGOCRYPT_CTX_ERROR) {\n            const message = getStatus().message || 'Finalization error';\n            throw new MongoCryptError(message);\n          }\n          result = finalizedContext;\n          break;\n        }\n\n        default:\n          throw new MongoCryptError(`Unknown state: ${getState()}`);\n      }\n    }\n\n    if (getState() === MONGOCRYPT_CTX_ERROR || result == null) {\n      const message = getStatus().message;\n      if (!message) {\n        debug(\n          `unidentifiable error in MongoCrypt - received an error status from \\`libmongocrypt\\` but received no error message.`\n        );\n      }\n      throw new MongoCryptError(\n        message ??\n          'unidentifiable error in MongoCrypt - received an error status from `libmongocrypt` but received no error message.'\n      );\n    }\n\n    return result;\n  }\n\n  /**\n   * Handles the request to the KMS service. Exposed for testing purposes. Do not directly invoke.\n   * @param kmsContext - A C++ KMS context returned from the bindings\n   * @returns A promise that resolves when the KMS reply has be fully parsed\n   */\n  async kmsRequest(\n    request: MongoCryptKMSRequest,\n    options?: { timeoutContext?: TimeoutContext } & Abortable\n  ): Promise<void> {\n    const parsedUrl = request.endpoint.split(':');\n    const port = parsedUrl[1] != null ? Number.parseInt(parsedUrl[1], 10) : HTTPS_PORT;\n    const socketOptions: tls.ConnectionOptions & {\n      host: string;\n      port: number;\n      autoSelectFamily?: boolean;\n      autoSelectFamilyAttemptTimeout?: number;\n    } = {\n      host: parsedUrl[0],\n      servername: parsedUrl[0],\n      port,\n      ...autoSelectSocketOptions(this.options.socketOptions || {})\n    };\n    const message = request.message;\n    const buffer = new BufferPool();\n\n    let netSocket: net.Socket;\n    let socket: tls.TLSSocket;\n\n    function destroySockets() {\n      for (const sock of [socket, netSocket]) {\n        if (sock) {\n          sock.destroy();\n        }\n      }\n    }\n\n    function onerror(cause: Error) {\n      return new MongoCryptError('KMS request failed', { cause });\n    }\n\n    function onclose() {\n      return new MongoCryptError('KMS request closed');\n    }\n\n    const tlsOptions = this.options.tlsOptions;\n    if (tlsOptions) {\n      const kmsProvider = request.kmsProvider;\n      const providerTlsOptions = tlsOptions[kmsProvider];\n      if (providerTlsOptions) {\n        const error = this.validateTlsOptions(kmsProvider, providerTlsOptions);\n        if (error) {\n          throw error;\n        }\n        try {\n          await this.setTlsOptions(providerTlsOptions, socketOptions);\n        } catch (err) {\n          throw onerror(err);\n        }\n      }\n    }\n\n    let abortListener;\n\n    try {\n      if (this.options.proxyOptions && this.options.proxyOptions.proxyHost) {\n        netSocket = new net.Socket();\n\n        const {\n          promise: willConnect,\n          reject: rejectOnNetSocketError,\n          resolve: resolveOnNetSocketConnect\n        } = promiseWithResolvers<void>();\n\n        netSocket\n          .once('error', err => rejectOnNetSocketError(onerror(err)))\n          .once('close', () => rejectOnNetSocketError(onclose()))\n          .once('connect', () => resolveOnNetSocketConnect());\n\n        const netSocketOptions = {\n          ...socketOptions,\n          host: this.options.proxyOptions.proxyHost,\n          port: this.options.proxyOptions.proxyPort || 1080\n        };\n\n        netSocket.connect(netSocketOptions);\n\n        await willConnect;\n\n        try {\n          socks ??= loadSocks();\n          socketOptions.socket = (\n            await socks.SocksClient.createConnection({\n              existing_socket: netSocket,\n              command: 'connect',\n              destination: { host: socketOptions.host, port: socketOptions.port },\n              proxy: {\n                // host and port are ignored because we pass existing_socket\n                host: 'iLoveJavaScript',\n                port: 0,\n                type: 5,\n                userId: this.options.proxyOptions.proxyUsername,\n                password: this.options.proxyOptions.proxyPassword\n              }\n            })\n          ).socket;\n        } catch (err) {\n          throw onerror(err);\n        }\n      }\n\n      socket = tls.connect(socketOptions, () => {\n        socket.write(message);\n      });\n\n      const {\n        promise: willResolveKmsRequest,\n        reject: rejectOnTlsSocketError,\n        resolve\n      } = promiseWithResolvers<void>();\n\n      abortListener = addAbortListener(options?.signal, function () {\n        destroySockets();\n        rejectOnTlsSocketError(this.reason);\n      });\n\n      socket\n        .once('error', err => rejectOnTlsSocketError(onerror(err)))\n        .once('close', () => rejectOnTlsSocketError(onclose()))\n        .on('data', data => {\n          buffer.append(data);\n          while (request.bytesNeeded > 0 && buffer.length) {\n            const bytesNeeded = Math.min(request.bytesNeeded, buffer.length);\n            request.addResponse(buffer.read(bytesNeeded));\n          }\n\n          if (request.bytesNeeded <= 0) {\n            resolve();\n          }\n        });\n      await (options?.timeoutContext?.csotEnabled()\n        ? Promise.all([\n            willResolveKmsRequest,\n            Timeout.expires(options.timeoutContext?.remainingTimeMS)\n          ])\n        : willResolveKmsRequest);\n    } catch (error) {\n      if (error instanceof TimeoutError)\n        throw new MongoOperationTimeoutError('KMS request timed out');\n      throw error;\n    } finally {\n      // There's no need for any more activity on this socket at this point.\n      destroySockets();\n      abortListener?.[kDispose]();\n    }\n  }\n\n  *requests(context: MongoCryptContext, options?: { timeoutContext?: TimeoutContext } & Abortable) {\n    for (\n      let request = context.nextKMSRequest();\n      request != null;\n      request = context.nextKMSRequest()\n    ) {\n      yield this.kmsRequest(request, options);\n    }\n  }\n\n  /**\n   * Validates the provided TLS options are secure.\n   *\n   * @param kmsProvider - The KMS provider name.\n   * @param tlsOptions - The client TLS options for the provider.\n   *\n   * @returns An error if any option is invalid.\n   */\n  validateTlsOptions(\n    kmsProvider: string,\n    tlsOptions: ClientEncryptionTlsOptions\n  ): MongoCryptError | void {\n    const tlsOptionNames = Object.keys(tlsOptions);\n    for (const option of INSECURE_TLS_OPTIONS) {\n      if (tlsOptionNames.includes(option)) {\n        return new MongoCryptError(`Insecure TLS options prohibited for ${kmsProvider}: ${option}`);\n      }\n    }\n  }\n\n  /**\n   * Sets only the valid secure TLS options.\n   *\n   * @param tlsOptions - The client TLS options for the provider.\n   * @param options - The existing connection options.\n   */\n  async setTlsOptions(\n    tlsOptions: ClientEncryptionTlsOptions,\n    options: tls.ConnectionOptions\n  ): Promise<void> {\n    if (tlsOptions.tlsCertificateKeyFile) {\n      const cert = await fs.readFile(tlsOptions.tlsCertificateKeyFile);\n      options.cert = options.key = cert;\n    }\n    if (tlsOptions.tlsCAFile) {\n      options.ca = await fs.readFile(tlsOptions.tlsCAFile);\n    }\n    if (tlsOptions.tlsCertificateKeyFilePassword) {\n      options.passphrase = tlsOptions.tlsCertificateKeyFilePassword;\n    }\n  }\n\n  /**\n   * Fetches collection info for a provided namespace, when libmongocrypt\n   * enters the `MONGOCRYPT_CTX_NEED_MONGO_COLLINFO` state. The result is\n   * used to inform libmongocrypt of the schema associated with this\n   * namespace. Exposed for testing purposes. Do not directly invoke.\n   *\n   * @param client - A MongoClient connected to the topology\n   * @param ns - The namespace to list collections from\n   * @param filter - A filter for the listCollections command\n   * @param callback - Invoked with the info of the requested collection, or with an error\n   */\n  fetchCollectionInfo(\n    client: MongoClient,\n    ns: string,\n    filter: Document,\n    options?: { timeoutContext?: TimeoutContext } & Abortable\n  ): AsyncIterable<CollectionInfo> {\n    const { db } = MongoDBCollectionNamespace.fromString(ns);\n\n    const cursor = client.db(db).listCollections(filter, {\n      promoteLongs: false,\n      promoteValues: false,\n      timeoutContext:\n        options?.timeoutContext && new CursorTimeoutContext(options?.timeoutContext, Symbol()),\n      signal: options?.signal,\n      nameOnly: false\n    });\n\n    return cursor;\n  }\n\n  /**\n   * Calls to the mongocryptd to provide markings for a command.\n   * Exposed for testing purposes. Do not directly invoke.\n   * @param client - A MongoClient connected to a mongocryptd\n   * @param ns - The namespace (database.collection) the command is being executed on\n   * @param command - The command to execute.\n   * @param callback - Invoked with the serialized and marked bson command, or with an error\n   */\n  async markCommand(\n    client: MongoClient,\n    ns: string,\n    command: Uint8Array,\n    options?: { timeoutContext?: TimeoutContext } & Abortable\n  ): Promise<Uint8Array> {\n    const { db } = MongoDBCollectionNamespace.fromString(ns);\n    const bsonOptions = { promoteLongs: false, promoteValues: false };\n    const rawCommand = deserialize(command, bsonOptions);\n\n    const commandOptions: {\n      timeoutMS?: number;\n      signal?: AbortSignal;\n    } = {\n      timeoutMS: undefined,\n      signal: undefined\n    };\n\n    if (options?.timeoutContext?.csotEnabled()) {\n      commandOptions.timeoutMS = options.timeoutContext.remainingTimeMS;\n    }\n    if (options?.signal) {\n      commandOptions.signal = options.signal;\n    }\n\n    const response = await client.db(db).command(rawCommand, {\n      ...bsonOptions,\n      ...commandOptions\n    });\n\n    return serialize(response, this.bsonOptions);\n  }\n\n  /**\n   * Requests keys from the keyVault collection on the topology.\n   * Exposed for testing purposes. Do not directly invoke.\n   * @param client - A MongoClient connected to the topology\n   * @param keyVaultNamespace - The namespace (database.collection) of the keyVault Collection\n   * @param filter - The filter for the find query against the keyVault Collection\n   * @param callback - Invoked with the found keys, or with an error\n   */\n  fetchKeys(\n    client: MongoClient,\n    keyVaultNamespace: string,\n    filter: Uint8Array,\n    options?: { timeoutContext?: TimeoutContext } & Abortable\n  ): Promise<Array<DataKey>> {\n    const { db: dbName, collection: collectionName } =\n      MongoDBCollectionNamespace.fromString(keyVaultNamespace);\n\n    const commandOptions: {\n      timeoutContext?: CursorTimeoutContext;\n      signal?: AbortSignal;\n    } = {\n      timeoutContext: undefined,\n      signal: undefined\n    };\n\n    if (options?.timeoutContext != null) {\n      commandOptions.timeoutContext = new CursorTimeoutContext(options.timeoutContext, Symbol());\n    }\n    if (options?.signal != null) {\n      commandOptions.signal = options.signal;\n    }\n\n    return client\n      .db(dbName)\n      .collection<DataKey>(collectionName, { readConcern: { level: 'majority' } })\n      .find(deserialize(filter), commandOptions)\n      .toArray();\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,EAAA,GAAAC,OAAA;AAEA,MAAAC,GAAA,GAAAD,OAAA;AACA,MAAAE,GAAA,GAAAF,OAAA;AAEA,MAAAG,MAAA,GAAAH,OAAA;AAQA,MAAAI,iBAAA,GAAAJ,OAAA;AACA,MAAAK,MAAA,GAAAL,OAAA;AACA,MAAAM,OAAA,GAAAN,OAAA;AAIA,MAAAO,SAAA,GAAAP,OAAA;AACA,MAAAQ,OAAA,GAAAR,OAAA;AAOA,MAAAS,mBAAA,GAAAT,OAAA;AACA,MAAAU,QAAA,GAAAV,OAAA;AAIA,IAAIW,KAAK,GAAoB,IAAI;AACjC,SAASC,SAASA,CAAA;EAChB,IAAID,KAAK,IAAI,IAAI,EAAE;IACjB,MAAME,WAAW,GAAG,IAAAR,MAAA,CAAAS,QAAQ,GAAE;IAC9B,IAAI,cAAc,IAAID,WAAW,EAAE;MACjC,MAAMA,WAAW,CAACE,YAAY;IAChC;IACAJ,KAAK,GAAGE,WAAW;EACrB;EACA,OAAOF,KAAK;AACd;AAEA;AACA,MAAMK,oBAAoB,GAAG,CAAC;AAC9B,MAAMC,kCAAkC,GAAG,CAAC;AAC5C,MAAMC,kCAAkC,GAAG,CAAC;AAC5C,MAAMC,8BAA8B,GAAG,CAAC;AACxC,MAAMC,mCAAmC,GAAG,CAAC;AAC7C,MAAMC,uBAAuB,GAAG,CAAC;AACjC,MAAMC,oBAAoB,GAAG,CAAC;AAC9B,MAAMC,mBAAmB,GAAG,CAAC;AAE7B,MAAMC,UAAU,GAAG,GAAG;AAEtB,MAAMC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAC5B,CAACV,oBAAoB,EAAE,sBAAsB,CAAC,EAC9C,CAACC,kCAAkC,EAAE,oCAAoC,CAAC,EAC1E,CAACC,kCAAkC,EAAE,oCAAoC,CAAC,EAC1E,CAACC,8BAA8B,EAAE,gCAAgC,CAAC,EAClE,CAACC,mCAAmC,EAAE,qCAAqC,CAAC,EAC5E,CAACC,uBAAuB,EAAE,yBAAyB,CAAC,EACpD,CAACC,oBAAoB,EAAE,sBAAsB,CAAC,EAC9C,CAACC,mBAAmB,EAAE,qBAAqB,CAAC,CAC7C,CAAC;AAEF,MAAMI,oBAAoB,GAAG,CAC3B,aAAa,EACb,6BAA6B,EAC7B,0BAA0B;AAE1B;AACA;AACA,6BAA6B,EAC7B,sCAAsC,CACvC;AAED;;;;AAIA,SAASC,KAAKA,CAACC,GAAY;EACzB,IAAIC,OAAO,CAACC,GAAG,CAACC,mBAAmB,EAAE;IACnC;IACAC,OAAO,CAACC,KAAK,CAACL,GAAG,CAAC;EACpB;AACF;AAkDA;;;;;;;;;;;AAWA,IAAIM,OAAO;AAiCX;;;;;AAKA;AACA,MAAaC,YAAY;EACvBC,YACUC,OAA4B,EAC5BC,WAAA,GAAc,IAAApC,MAAA,CAAAqC,yBAAyB,EAACF,OAAO,CAAC;IADhD,KAAAA,OAAO,GAAPA,OAAO;IACP,KAAAC,WAAW,GAAXA,WAAW;EAClB;EAEH;;;EAGA,MAAME,OAAOA,CACXC,QAAgC,EAChCC,OAA0B,EAC1BL,OAAwD;IAExD,MAAMM,iBAAiB,GAAGF,QAAQ,CAACG,kBAAkB;IACrD,MAAMC,cAAc,GAAGJ,QAAQ,CAACK,eAAe;IAC/C,MAAMC,cAAc,GAAGN,QAAQ,CAACO,eAAe;IAC/C,MAAMC,iBAAiB,GAAGR,QAAQ,CAACS,kBAAkB;IACrD,MAAMC,kBAAkB,GAAGV,QAAQ,CAACW,mBAAmB;IACvD,IAAIC,MAAM,GAAsB,IAAI;IAEpC;IACA;IACA;IACA;IACA;IACA,MAAMC,SAAS,GAAGA,CAAA,KAAMZ,OAAO,CAACa,MAAM;IACtC,MAAMC,QAAQ,GAAGA,CAAA,KAAMd,OAAO,CAACe,KAAK;IAEpC,OAAOD,QAAQ,EAAE,KAAKlC,mBAAmB,IAAIkC,QAAQ,EAAE,KAAKzC,oBAAoB,EAAE;MAChFsB,OAAO,CAACqB,MAAM,EAAEC,cAAc,EAAE;MAChChC,KAAK,CAAC,YAAYe,OAAO,CAACkB,EAAE,KAAKpC,aAAa,CAACqC,GAAG,CAACL,QAAQ,EAAE,CAAC,IAAIA,QAAQ,EAAE,EAAE,CAAC;MAE/E,QAAQA,QAAQ,EAAE;QAChB,KAAKxC,kCAAkC;UAAE;YACvC,MAAM8C,MAAM,GAAG,IAAA5D,MAAA,CAAA6D,WAAW,EAACrB,OAAO,CAACsB,kBAAkB,EAAE,CAAC;YACxD,IAAI,CAACjB,cAAc,EAAE;cACnB,MAAM,IAAItC,QAAA,CAAAwD,eAAe,CACvB,8GAA8G,CAC/G;YACH;YAEA,MAAMC,cAAc,GAAG,IAAI,CAACC,mBAAmB,CAC7CpB,cAAc,EACdL,OAAO,CAAC0B,EAAE,EACVN,MAAM,EACNzB,OAAO,CACR;YAED,WAAW,MAAMgC,QAAQ,IAAIH,cAAc,EAAE;cAC3CxB,OAAO,CAAC4B,yBAAyB,CAAC,IAAApE,MAAA,CAAAqE,SAAS,EAACF,QAAQ,CAAC,CAAC;cACtD,IAAIb,QAAQ,EAAE,KAAKzC,oBAAoB,EAAE;YAC3C;YAEA,IAAIyC,QAAQ,EAAE,KAAKzC,oBAAoB,EAAE;YAEzC2B,OAAO,CAAC8B,oBAAoB,EAAE;YAC9B;UACF;QAEA,KAAKvD,kCAAkC;UAAE;YACvC,MAAMwD,OAAO,GAAG/B,OAAO,CAACsB,kBAAkB,EAAE;YAC5C,IAAIR,QAAQ,EAAE,KAAKzC,oBAAoB,EAAE;YAEzC,IAAI,CAACkC,iBAAiB,EAAE;cACtB,MAAM,IAAIxC,QAAA,CAAAwD,eAAe,CACvB,gHAAgH,CACjH;YACH;YAEA;YACA,MAAMS,aAAa,GAAevB,kBAAkB,GAChD,MAAMA,kBAAkB,CAACwB,WAAW,CAClC,IAAI,CAACC,WAAW,CAACC,IAAI,CAAC,IAAI,EAAE5B,iBAAiB,EAAEP,OAAO,CAAC0B,EAAE,EAAEK,OAAO,EAAEpC,OAAO,CAAC,CAC7E,GACD,MAAM,IAAI,CAACuC,WAAW,CAAC3B,iBAAiB,EAAEP,OAAO,CAAC0B,EAAE,EAAEK,OAAO,EAAEpC,OAAO,CAAC;YAE3EK,OAAO,CAAC4B,yBAAyB,CAACI,aAAa,CAAC;YAChDhC,OAAO,CAAC8B,oBAAoB,EAAE;YAC9B;UACF;QAEA,KAAKtD,8BAA8B;UAAE;YACnC,MAAM4C,MAAM,GAAGpB,OAAO,CAACsB,kBAAkB,EAAE;YAC3C,MAAMc,IAAI,GAAG,MAAM,IAAI,CAACC,SAAS,CAAClC,cAAc,EAAEF,iBAAiB,EAAEmB,MAAM,EAAEzB,OAAO,CAAC;YAErF,IAAIyC,IAAI,CAACE,MAAM,KAAK,CAAC,EAAE;cACrB;cACA3B,MAAM,GAAGnB,OAAO,KAAK,IAAAhC,MAAA,CAAAqE,SAAS,EAAC;gBAAEU,CAAC,EAAE;cAAE,CAAE,CAAC;YAC3C;YACA,WAAW,MAAMC,GAAG,IAAIJ,IAAI,EAAE;cAC5BpC,OAAO,CAAC4B,yBAAyB,CAAC,IAAApE,MAAA,CAAAqE,SAAS,EAACW,GAAG,CAAC,CAAC;YACnD;YAEAxC,OAAO,CAAC8B,oBAAoB,EAAE;YAE9B;UACF;QAEA,KAAKrD,mCAAmC;UAAE;YACxC,MAAMgE,YAAY,GAAG,MAAM1C,QAAQ,CAAC2C,oBAAoB,EAAE;YAC1D1C,OAAO,CAAC2C,mBAAmB,CAAC,IAAAnF,MAAA,CAAAqE,SAAS,EAACY,YAAY,CAAC,CAAC;YACpD;UACF;QAEA,KAAK/D,uBAAuB;UAAE;YAC5B,MAAMkE,OAAO,CAACC,GAAG,CAAC,IAAI,CAACC,QAAQ,CAAC9C,OAAO,EAAEL,OAAO,CAAC,CAAC;YAClDK,OAAO,CAAC+C,iBAAiB,EAAE;YAC3B;UACF;QAEA,KAAKpE,oBAAoB;UAAE;YACzB,MAAMqE,gBAAgB,GAAGhD,OAAO,CAACiD,QAAQ,EAAE;YAC3C,IAAInC,QAAQ,EAAE,KAAKzC,oBAAoB,EAAE;cACvC,MAAM6E,OAAO,GAAGtC,SAAS,EAAE,CAACsC,OAAO,IAAI,oBAAoB;cAC3D,MAAM,IAAInF,QAAA,CAAAwD,eAAe,CAAC2B,OAAO,CAAC;YACpC;YACAvC,MAAM,GAAGqC,gBAAgB;YACzB;UACF;QAEA;UACE,MAAM,IAAIjF,QAAA,CAAAwD,eAAe,CAAC,kBAAkBT,QAAQ,EAAE,EAAE,CAAC;MAC7D;IACF;IAEA,IAAIA,QAAQ,EAAE,KAAKzC,oBAAoB,IAAIsC,MAAM,IAAI,IAAI,EAAE;MACzD,MAAMuC,OAAO,GAAGtC,SAAS,EAAE,CAACsC,OAAO;MACnC,IAAI,CAACA,OAAO,EAAE;QACZjE,KAAK,CACH,qHAAqH,CACtH;MACH;MACA,MAAM,IAAIlB,QAAA,CAAAwD,eAAe,CACvB2B,OAAO,IACL,mHAAmH,CACtH;IACH;IAEA,OAAOvC,MAAM;EACf;EAEA;;;;;EAKA,MAAMwC,UAAUA,CACdC,OAA6B,EAC7BzD,OAAyD;IAEzD,MAAM0D,SAAS,GAAGD,OAAO,CAACE,QAAQ,CAACC,KAAK,CAAC,GAAG,CAAC;IAC7C,MAAMC,IAAI,GAAGH,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,GAAGI,MAAM,CAACC,QAAQ,CAACL,SAAS,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC,GAAGxE,UAAU;IAClF,MAAM8E,aAAa,GAKf;MACFC,IAAI,EAAEP,SAAS,CAAC,CAAC,CAAC;MAClBQ,UAAU,EAAER,SAAS,CAAC,CAAC,CAAC;MACxBG,IAAI;MACJ,GAAG,IAAA1F,mBAAA,CAAAgG,uBAAuB,EAAC,IAAI,CAACnE,OAAO,CAACgE,aAAa,IAAI,EAAE;KAC5D;IACD,MAAMT,OAAO,GAAGE,OAAO,CAACF,OAAO;IAC/B,MAAMa,MAAM,GAAG,IAAIlG,OAAA,CAAAmG,UAAU,EAAE;IAE/B,IAAIC,SAAqB;IACzB,IAAIC,MAAqB;IAEzB,SAASC,cAAcA,CAAA;MACrB,KAAK,MAAMC,IAAI,IAAI,CAACF,MAAM,EAAED,SAAS,CAAC,EAAE;QACtC,IAAIG,IAAI,EAAE;UACRA,IAAI,CAACC,OAAO,EAAE;QAChB;MACF;IACF;IAEA,SAASC,OAAOA,CAACC,KAAY;MAC3B,OAAO,IAAIxG,QAAA,CAAAwD,eAAe,CAAC,oBAAoB,EAAE;QAAEgD;MAAK,CAAE,CAAC;IAC7D;IAEA,SAASC,OAAOA,CAAA;MACd,OAAO,IAAIzG,QAAA,CAAAwD,eAAe,CAAC,oBAAoB,CAAC;IAClD;IAEA,MAAMkD,UAAU,GAAG,IAAI,CAAC9E,OAAO,CAAC8E,UAAU;IAC1C,IAAIA,UAAU,EAAE;MACd,MAAMC,WAAW,GAAGtB,OAAO,CAACsB,WAAW;MACvC,MAAMC,kBAAkB,GAAGF,UAAU,CAACC,WAAW,CAAC;MAClD,IAAIC,kBAAkB,EAAE;QACtB,MAAMpF,KAAK,GAAG,IAAI,CAACqF,kBAAkB,CAACF,WAAW,EAAEC,kBAAkB,CAAC;QACtE,IAAIpF,KAAK,EAAE;UACT,MAAMA,KAAK;QACb;QACA,IAAI;UACF,MAAM,IAAI,CAACsF,aAAa,CAACF,kBAAkB,EAAEhB,aAAa,CAAC;QAC7D,CAAC,CAAC,OAAOmB,GAAG,EAAE;UACZ,MAAMR,OAAO,CAACQ,GAAG,CAAC;QACpB;MACF;IACF;IAEA,IAAIC,aAAa;IAEjB,IAAI;MACF,IAAI,IAAI,CAACpF,OAAO,CAACqF,YAAY,IAAI,IAAI,CAACrF,OAAO,CAACqF,YAAY,CAACC,SAAS,EAAE;QACpEhB,SAAS,GAAG,IAAI3G,GAAG,CAAC4H,MAAM,EAAE;QAE5B,MAAM;UACJC,OAAO,EAAEC,WAAW;UACpBC,MAAM,EAAEC,sBAAsB;UAC9BC,OAAO,EAAEC;QAAyB,CACnC,GAAG,IAAA3H,OAAA,CAAA4H,oBAAoB,GAAQ;QAEhCxB,SAAS,CACNyB,IAAI,CAAC,OAAO,EAAEZ,GAAG,IAAIQ,sBAAsB,CAAChB,OAAO,CAACQ,GAAG,CAAC,CAAC,CAAC,CAC1DY,IAAI,CAAC,OAAO,EAAE,MAAMJ,sBAAsB,CAACd,OAAO,EAAE,CAAC,CAAC,CACtDkB,IAAI,CAAC,SAAS,EAAE,MAAMF,yBAAyB,EAAE,CAAC;QAErD,MAAMG,gBAAgB,GAAG;UACvB,GAAGhC,aAAa;UAChBC,IAAI,EAAE,IAAI,CAACjE,OAAO,CAACqF,YAAY,CAACC,SAAS;UACzCzB,IAAI,EAAE,IAAI,CAAC7D,OAAO,CAACqF,YAAY,CAACY,SAAS,IAAI;SAC9C;QAED3B,SAAS,CAAC4B,OAAO,CAACF,gBAAgB,CAAC;QAEnC,MAAMP,WAAW;QAEjB,IAAI;UACFpH,KAAK,KAAKC,SAAS,EAAE;UACrB0F,aAAa,CAACO,MAAM,GAAG,CACrB,MAAMlG,KAAK,CAAC8H,WAAW,CAACC,gBAAgB,CAAC;YACvCC,eAAe,EAAE/B,SAAS;YAC1BlC,OAAO,EAAE,SAAS;YAClBkE,WAAW,EAAE;cAAErC,IAAI,EAAED,aAAa,CAACC,IAAI;cAAEJ,IAAI,EAAEG,aAAa,CAACH;YAAI,CAAE;YACnE0C,KAAK,EAAE;cACL;cACAtC,IAAI,EAAE,iBAAiB;cACvBJ,IAAI,EAAE,CAAC;cACP2C,IAAI,EAAE,CAAC;cACPC,MAAM,EAAE,IAAI,CAACzG,OAAO,CAACqF,YAAY,CAACqB,aAAa;cAC/CC,QAAQ,EAAE,IAAI,CAAC3G,OAAO,CAACqF,YAAY,CAACuB;;WAEvC,CAAC,EACFrC,MAAM;QACV,CAAC,CAAC,OAAOY,GAAG,EAAE;UACZ,MAAMR,OAAO,CAACQ,GAAG,CAAC;QACpB;MACF;MAEAZ,MAAM,GAAG3G,GAAG,CAACsI,OAAO,CAAClC,aAAa,EAAE,MAAK;QACvCO,MAAM,CAACsC,KAAK,CAACtD,OAAO,CAAC;MACvB,CAAC,CAAC;MAEF,MAAM;QACJiC,OAAO,EAAEsB,qBAAqB;QAC9BpB,MAAM,EAAEqB,sBAAsB;QAC9BnB;MAAO,CACR,GAAG,IAAA1H,OAAA,CAAA4H,oBAAoB,GAAQ;MAEhCV,aAAa,GAAG,IAAAlH,OAAA,CAAA8I,gBAAgB,EAAChH,OAAO,EAAEqB,MAAM,EAAE;QAChDmD,cAAc,EAAE;QAChBuC,sBAAsB,CAAC,IAAI,CAACE,MAAM,CAAC;MACrC,CAAC,CAAC;MAEF1C,MAAM,CACHwB,IAAI,CAAC,OAAO,EAAEZ,GAAG,IAAI4B,sBAAsB,CAACpC,OAAO,CAACQ,GAAG,CAAC,CAAC,CAAC,CAC1DY,IAAI,CAAC,OAAO,EAAE,MAAMgB,sBAAsB,CAAClC,OAAO,EAAE,CAAC,CAAC,CACtDqC,EAAE,CAAC,MAAM,EAAEC,IAAI,IAAG;QACjB/C,MAAM,CAACgD,MAAM,CAACD,IAAI,CAAC;QACnB,OAAO1D,OAAO,CAAC4D,WAAW,GAAG,CAAC,IAAIjD,MAAM,CAACzB,MAAM,EAAE;UAC/C,MAAM0E,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC9D,OAAO,CAAC4D,WAAW,EAAEjD,MAAM,CAACzB,MAAM,CAAC;UAChEc,OAAO,CAAC+D,WAAW,CAACpD,MAAM,CAACqD,IAAI,CAACJ,WAAW,CAAC,CAAC;QAC/C;QAEA,IAAI5D,OAAO,CAAC4D,WAAW,IAAI,CAAC,EAAE;UAC5BzB,OAAO,EAAE;QACX;MACF,CAAC,CAAC;MACJ,OAAO5F,OAAO,EAAE0H,cAAc,EAAEC,WAAW,EAAE,GACzC1E,OAAO,CAACC,GAAG,CAAC,CACV4D,qBAAqB,EACrB7I,SAAA,CAAA2J,OAAO,CAACC,OAAO,CAAC7H,OAAO,CAAC0H,cAAc,EAAEI,eAAe,CAAC,CACzD,CAAC,GACFhB,qBAAqB,CAAC;IAC5B,CAAC,CAAC,OAAOlH,KAAK,EAAE;MACd,IAAIA,KAAK,YAAY3B,SAAA,CAAA8J,YAAY,EAC/B,MAAM,IAAI/J,OAAA,CAAAgK,0BAA0B,CAAC,uBAAuB,CAAC;MAC/D,MAAMpI,KAAK;IACb,CAAC,SAAS;MACR;MACA4E,cAAc,EAAE;MAChBY,aAAa,GAAGlH,OAAA,CAAA+J,QAAQ,CAAC,EAAE;IAC7B;EACF;EAEA,CAAC9E,QAAQA,CAAC9C,OAA0B,EAAEL,OAAyD;IAC7F,KACE,IAAIyD,OAAO,GAAGpD,OAAO,CAAC6H,cAAc,EAAE,EACtCzE,OAAO,IAAI,IAAI,EACfA,OAAO,GAAGpD,OAAO,CAAC6H,cAAc,EAAE,EAClC;MACA,MAAM,IAAI,CAAC1E,UAAU,CAACC,OAAO,EAAEzD,OAAO,CAAC;IACzC;EACF;EAEA;;;;;;;;EAQAiF,kBAAkBA,CAChBF,WAAmB,EACnBD,UAAsC;IAEtC,MAAMqD,cAAc,GAAGC,MAAM,CAAC3F,IAAI,CAACqC,UAAU,CAAC;IAC9C,KAAK,MAAMuD,MAAM,IAAIhJ,oBAAoB,EAAE;MACzC,IAAI8I,cAAc,CAACG,QAAQ,CAACD,MAAM,CAAC,EAAE;QACnC,OAAO,IAAIjK,QAAA,CAAAwD,eAAe,CAAC,uCAAuCmD,WAAW,KAAKsD,MAAM,EAAE,CAAC;MAC7F;IACF;EACF;EAEA;;;;;;EAMA,MAAMnD,aAAaA,CACjBJ,UAAsC,EACtC9E,OAA8B;IAE9B,IAAI8E,UAAU,CAACyD,qBAAqB,EAAE;MACpC,MAAMC,IAAI,GAAG,MAAM/K,EAAE,CAACgL,QAAQ,CAAC3D,UAAU,CAACyD,qBAAqB,CAAC;MAChEvI,OAAO,CAACwI,IAAI,GAAGxI,OAAO,CAAC6C,GAAG,GAAG2F,IAAI;IACnC;IACA,IAAI1D,UAAU,CAAC4D,SAAS,EAAE;MACxB1I,OAAO,CAAC2I,EAAE,GAAG,MAAMlL,EAAE,CAACgL,QAAQ,CAAC3D,UAAU,CAAC4D,SAAS,CAAC;IACtD;IACA,IAAI5D,UAAU,CAAC8D,6BAA6B,EAAE;MAC5C5I,OAAO,CAAC6I,UAAU,GAAG/D,UAAU,CAAC8D,6BAA6B;IAC/D;EACF;EAEA;;;;;;;;;;;EAWA9G,mBAAmBA,CACjBgH,MAAmB,EACnB/G,EAAU,EACVN,MAAgB,EAChBzB,OAAyD;IAEzD,MAAM;MAAE+I;IAAE,CAAE,GAAG7K,OAAA,CAAA8K,0BAA0B,CAACC,UAAU,CAAClH,EAAE,CAAC;IAExD,MAAMmH,MAAM,GAAGJ,MAAM,CAACC,EAAE,CAACA,EAAE,CAAC,CAACI,eAAe,CAAC1H,MAAM,EAAE;MACnD2H,YAAY,EAAE,KAAK;MACnBC,aAAa,EAAE,KAAK;MACpB3B,cAAc,EACZ1H,OAAO,EAAE0H,cAAc,IAAI,IAAI5J,iBAAA,CAAAwL,oBAAoB,CAACtJ,OAAO,EAAE0H,cAAc,EAAE6B,MAAM,EAAE,CAAC;MACxFlI,MAAM,EAAErB,OAAO,EAAEqB,MAAM;MACvBmI,QAAQ,EAAE;KACX,CAAC;IAEF,OAAON,MAAM;EACf;EAEA;;;;;;;;EAQA,MAAM3G,WAAWA,CACfuG,MAAmB,EACnB/G,EAAU,EACVK,OAAmB,EACnBpC,OAAyD;IAEzD,MAAM;MAAE+I;IAAE,CAAE,GAAG7K,OAAA,CAAA8K,0BAA0B,CAACC,UAAU,CAAClH,EAAE,CAAC;IACxD,MAAM9B,WAAW,GAAG;MAAEmJ,YAAY,EAAE,KAAK;MAAEC,aAAa,EAAE;IAAK,CAAE;IACjE,MAAMI,UAAU,GAAG,IAAA5L,MAAA,CAAA6D,WAAW,EAACU,OAAO,EAAEnC,WAAW,CAAC;IAEpD,MAAMyJ,cAAc,GAGhB;MACFC,SAAS,EAAEC,SAAS;MACpBvI,MAAM,EAAEuI;KACT;IAED,IAAI5J,OAAO,EAAE0H,cAAc,EAAEC,WAAW,EAAE,EAAE;MAC1C+B,cAAc,CAACC,SAAS,GAAG3J,OAAO,CAAC0H,cAAc,CAACI,eAAe;IACnE;IACA,IAAI9H,OAAO,EAAEqB,MAAM,EAAE;MACnBqI,cAAc,CAACrI,MAAM,GAAGrB,OAAO,CAACqB,MAAM;IACxC;IAEA,MAAMwI,QAAQ,GAAG,MAAMf,MAAM,CAACC,EAAE,CAACA,EAAE,CAAC,CAAC3G,OAAO,CAACqH,UAAU,EAAE;MACvD,GAAGxJ,WAAW;MACd,GAAGyJ;KACJ,CAAC;IAEF,OAAO,IAAA7L,MAAA,CAAAqE,SAAS,EAAC2H,QAAQ,EAAE,IAAI,CAAC5J,WAAW,CAAC;EAC9C;EAEA;;;;;;;;EAQAyC,SAASA,CACPoG,MAAmB,EACnBxI,iBAAyB,EACzBmB,MAAkB,EAClBzB,OAAyD;IAEzD,MAAM;MAAE+I,EAAE,EAAEe,MAAM;MAAEC,UAAU,EAAEC;IAAc,CAAE,GAC9C9L,OAAA,CAAA8K,0BAA0B,CAACC,UAAU,CAAC3I,iBAAiB,CAAC;IAE1D,MAAMoJ,cAAc,GAGhB;MACFhC,cAAc,EAAEkC,SAAS;MACzBvI,MAAM,EAAEuI;KACT;IAED,IAAI5J,OAAO,EAAE0H,cAAc,IAAI,IAAI,EAAE;MACnCgC,cAAc,CAAChC,cAAc,GAAG,IAAI5J,iBAAA,CAAAwL,oBAAoB,CAACtJ,OAAO,CAAC0H,cAAc,EAAE6B,MAAM,EAAE,CAAC;IAC5F;IACA,IAAIvJ,OAAO,EAAEqB,MAAM,IAAI,IAAI,EAAE;MAC3BqI,cAAc,CAACrI,MAAM,GAAGrB,OAAO,CAACqB,MAAM;IACxC;IAEA,OAAOyH,MAAM,CACVC,EAAE,CAACe,MAAM,CAAC,CACVC,UAAU,CAAUC,cAAc,EAAE;MAAEC,WAAW,EAAE;QAAEC,KAAK,EAAE;MAAU;IAAE,CAAE,CAAC,CAC3EC,IAAI,CAAC,IAAAtM,MAAA,CAAA6D,WAAW,EAACD,MAAM,CAAC,EAAEiI,cAAc,CAAC,CACzCU,OAAO,EAAE;EACd;;AA3cFC,OAAA,CAAAvK,YAAA,GAAAA,YAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}