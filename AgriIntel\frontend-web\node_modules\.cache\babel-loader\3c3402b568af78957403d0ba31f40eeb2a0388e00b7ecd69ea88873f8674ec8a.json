{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.GCPMachineWorkflow = void 0;\nconst error_1 = require(\"../../../error\");\nconst utils_1 = require(\"../../../utils\");\nconst machine_workflow_1 = require(\"./machine_workflow\");\n/** GCP base URL. */\nconst GCP_BASE_URL = 'http://metadata/computeMetadata/v1/instance/service-accounts/default/identity';\n/** GCP request headers. */\nconst GCP_HEADERS = Object.freeze({\n  'Metadata-Flavor': 'Google'\n});\n/** Error for when the token audience is missing in the environment. */\nconst TOKEN_RESOURCE_MISSING_ERROR = 'TOKEN_RESOURCE must be set in the auth mechanism properties when ENVIRONMENT is gcp.';\nclass GCPMachineWorkflow extends machine_workflow_1.MachineWorkflow {\n  /**\n   * Instantiate the machine workflow.\n   */\n  constructor(cache) {\n    super(cache);\n  }\n  /**\n   * Get the token from the environment.\n   */\n  async getToken(credentials) {\n    const tokenAudience = credentials?.mechanismProperties.TOKEN_RESOURCE;\n    if (!tokenAudience) {\n      throw new error_1.MongoGCPError(TOKEN_RESOURCE_MISSING_ERROR);\n    }\n    return await getGcpTokenData(tokenAudience);\n  }\n}\nexports.GCPMachineWorkflow = GCPMachineWorkflow;\n/**\n * Hit the GCP endpoint to get the token data.\n */\nasync function getGcpTokenData(tokenAudience) {\n  const url = new URL(GCP_BASE_URL);\n  url.searchParams.append('audience', tokenAudience);\n  const response = await (0, utils_1.get)(url, {\n    headers: GCP_HEADERS\n  });\n  if (response.status !== 200) {\n    throw new error_1.MongoGCPError(`Status code ${response.status} returned from the GCP endpoint. Response body: ${response.body}`);\n  }\n  return {\n    access_token: response.body\n  };\n}", "map": {"version": 3, "names": ["error_1", "require", "utils_1", "machine_workflow_1", "GCP_BASE_URL", "GCP_HEADERS", "Object", "freeze", "TOKEN_RESOURCE_MISSING_ERROR", "GCPMachineWorkflow", "MachineWorkflow", "constructor", "cache", "getToken", "credentials", "tokenAudience", "mechanismProperties", "TOKEN_RESOURCE", "MongoGCPError", "getGcpTokenData", "exports", "url", "URL", "searchParams", "append", "response", "get", "headers", "status", "body", "access_token"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\auth\\mongodb_oidc\\gcp_machine_workflow.ts"], "sourcesContent": ["import { MongoGCPError } from '../../../error';\nimport { get } from '../../../utils';\nimport { type MongoCredentials } from '../mongo_credentials';\nimport { type AccessToken, MachineWorkflow } from './machine_workflow';\nimport { type TokenCache } from './token_cache';\n\n/** GCP base URL. */\nconst GCP_BASE_URL =\n  'http://metadata/computeMetadata/v1/instance/service-accounts/default/identity';\n\n/** GCP request headers. */\nconst GCP_HEADERS = Object.freeze({ 'Metadata-Flavor': 'Google' });\n\n/** Error for when the token audience is missing in the environment. */\nconst TOKEN_RESOURCE_MISSING_ERROR =\n  'TOKEN_RESOURCE must be set in the auth mechanism properties when ENVIRONMENT is gcp.';\n\nexport class GCPMachineWorkflow extends MachineWorkflow {\n  /**\n   * Instantiate the machine workflow.\n   */\n  constructor(cache: TokenCache) {\n    super(cache);\n  }\n\n  /**\n   * Get the token from the environment.\n   */\n  async getToken(credentials?: MongoCredentials): Promise<AccessToken> {\n    const tokenAudience = credentials?.mechanismProperties.TOKEN_RESOURCE;\n    if (!tokenAudience) {\n      throw new MongoGCPError(TOKEN_RESOURCE_MISSING_ERROR);\n    }\n    return await getGcpTokenData(tokenAudience);\n  }\n}\n\n/**\n * Hit the GCP endpoint to get the token data.\n */\nasync function getGcpTokenData(tokenAudience: string): Promise<AccessToken> {\n  const url = new URL(GCP_BASE_URL);\n  url.searchParams.append('audience', tokenAudience);\n  const response = await get(url, {\n    headers: GCP_HEADERS\n  });\n  if (response.status !== 200) {\n    throw new MongoGCPError(\n      `Status code ${response.status} returned from the GCP endpoint. Response body: ${response.body}`\n    );\n  }\n  return { access_token: response.body };\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,OAAA,GAAAC,OAAA;AACA,MAAAC,OAAA,GAAAD,OAAA;AAEA,MAAAE,kBAAA,GAAAF,OAAA;AAGA;AACA,MAAMG,YAAY,GAChB,+EAA+E;AAEjF;AACA,MAAMC,WAAW,GAAGC,MAAM,CAACC,MAAM,CAAC;EAAE,iBAAiB,EAAE;AAAQ,CAAE,CAAC;AAElE;AACA,MAAMC,4BAA4B,GAChC,sFAAsF;AAExF,MAAaC,kBAAmB,SAAQN,kBAAA,CAAAO,eAAe;EACrD;;;EAGAC,YAAYC,KAAiB;IAC3B,KAAK,CAACA,KAAK,CAAC;EACd;EAEA;;;EAGA,MAAMC,QAAQA,CAACC,WAA8B;IAC3C,MAAMC,aAAa,GAAGD,WAAW,EAAEE,mBAAmB,CAACC,cAAc;IACrE,IAAI,CAACF,aAAa,EAAE;MAClB,MAAM,IAAIf,OAAA,CAAAkB,aAAa,CAACV,4BAA4B,CAAC;IACvD;IACA,OAAO,MAAMW,eAAe,CAACJ,aAAa,CAAC;EAC7C;;AAjBFK,OAAA,CAAAX,kBAAA,GAAAA,kBAAA;AAoBA;;;AAGA,eAAeU,eAAeA,CAACJ,aAAqB;EAClD,MAAMM,GAAG,GAAG,IAAIC,GAAG,CAAClB,YAAY,CAAC;EACjCiB,GAAG,CAACE,YAAY,CAACC,MAAM,CAAC,UAAU,EAAET,aAAa,CAAC;EAClD,MAAMU,QAAQ,GAAG,MAAM,IAAAvB,OAAA,CAAAwB,GAAG,EAACL,GAAG,EAAE;IAC9BM,OAAO,EAAEtB;GACV,CAAC;EACF,IAAIoB,QAAQ,CAACG,MAAM,KAAK,GAAG,EAAE;IAC3B,MAAM,IAAI5B,OAAA,CAAAkB,aAAa,CACrB,eAAeO,QAAQ,CAACG,MAAM,mDAAmDH,QAAQ,CAACI,IAAI,EAAE,CACjG;EACH;EACA,OAAO;IAAEC,YAAY,EAAEL,QAAQ,CAACI;EAAI,CAAE;AACxC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}