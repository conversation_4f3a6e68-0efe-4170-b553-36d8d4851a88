{"ast": null, "code": "import React from'react';import{Link}from'react-router-dom';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const NotFoundPage=()=>{const{t}=useTranslation();return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\",children:/*#__PURE__*/_jsx(\"div\",{className:\"sm:mx-auto sm:w-full sm:max-w-md\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"text-center\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-9xl font-bold text-primary-600\",children:\"404\"}),/*#__PURE__*/_jsx(\"h2\",{className:\"mt-4 text-3xl font-bold text-gray-900\",children:\"Page not found\"}),/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-gray-600\",children:\"Sorry, we couldn't find the page you're looking for.\"}),/*#__PURE__*/_jsx(\"div\",{className:\"mt-6\",children:/*#__PURE__*/_jsx(Link,{to:\"/dashboard\",className:\"btn-primary\",children:\"Go back to dashboard\"})})]})})});};export default NotFoundPage;", "map": {"version": 3, "names": ["React", "Link", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "NotFoundPage", "t", "className", "children", "to"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/pages/NotFoundPage.tsx"], "sourcesContent": ["import React from 'react';\nimport { Link } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\n\nconst NotFoundPage: React.FC = () => {\n  const { t } = useTranslation();\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8\">\n      <div className=\"sm:mx-auto sm:w-full sm:max-w-md\">\n        <div className=\"text-center\">\n          <h1 className=\"text-9xl font-bold text-primary-600\">404</h1>\n          <h2 className=\"mt-4 text-3xl font-bold text-gray-900\">\n            Page not found\n          </h2>\n          <p className=\"mt-2 text-gray-600\">\n            Sorry, we couldn't find the page you're looking for.\n          </p>\n          <div className=\"mt-6\">\n            <Link\n              to=\"/dashboard\"\n              className=\"btn-primary\"\n            >\n              Go back to dashboard\n            </Link>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default NotFoundPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,IAAI,KAAQ,kBAAkB,CACvC,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAE9B,mBACEE,IAAA,QAAKK,SAAS,CAAC,4EAA4E,CAAAC,QAAA,cACzFN,IAAA,QAAKK,SAAS,CAAC,kCAAkC,CAAAC,QAAA,cAC/CJ,KAAA,QAAKG,SAAS,CAAC,aAAa,CAAAC,QAAA,eAC1BN,IAAA,OAAIK,SAAS,CAAC,qCAAqC,CAAAC,QAAA,CAAC,KAAG,CAAI,CAAC,cAC5DN,IAAA,OAAIK,SAAS,CAAC,uCAAuC,CAAAC,QAAA,CAAC,gBAEtD,CAAI,CAAC,cACLN,IAAA,MAAGK,SAAS,CAAC,oBAAoB,CAAAC,QAAA,CAAC,sDAElC,CAAG,CAAC,cACJN,IAAA,QAAKK,SAAS,CAAC,MAAM,CAAAC,QAAA,cACnBN,IAAA,CAACH,IAAI,EACHU,EAAE,CAAC,YAAY,CACfF,SAAS,CAAC,aAAa,CAAAC,QAAA,CACxB,sBAED,CAAM,CAAC,CACJ,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}