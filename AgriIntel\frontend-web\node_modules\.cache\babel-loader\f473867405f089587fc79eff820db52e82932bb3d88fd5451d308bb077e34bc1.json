{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{createSlice,createAsyncThunk}from'@reduxjs/toolkit';const initialState={animals:[],selectedAnimal:null,filters:{},pagination:{page:1,limit:20,total:0,totalPages:0},isLoading:false,error:null,statistics:null};// Mock async thunks (replace with actual API calls)\nexport const fetchAnimals=createAsyncThunk('animals/fetchAnimals',async(params,_ref)=>{let{rejectWithValue}=_ref;try{// Mock API call - replace with actual API\nawait new Promise(resolve=>setTimeout(resolve,1000));// Mock data\nconst mockAnimals=[{_id:'1',tagNumber:'C001',name:'Bessie',species:'cattle',breed:'Holstein',gender:'female',dateOfBirth:'2022-03-15',color:'Black and White',currentWeight:450,birthWeight:35,status:'active',healthStatus:'healthy',breedingStatus:'available',isActive:true,createdBy:'user1',createdAt:'2023-01-01',updatedAt:'2023-01-01',age:1,ageInDays:365,latestWeight:450},{_id:'2',tagNumber:'S001',name:'Woolly',species:'sheep',breed:'Merino',gender:'male',dateOfBirth:'2023-01-10',color:'White',currentWeight:75,birthWeight:4,status:'active',healthStatus:'healthy',breedingStatus:'available',isActive:true,createdBy:'user1',createdAt:'2023-01-10',updatedAt:'2023-01-10',age:0,ageInDays:180,latestWeight:75}];return{animals:mockAnimals,pagination:{page:params.page||1,limit:params.limit||20,total:mockAnimals.length,totalPages:Math.ceil(mockAnimals.length/(params.limit||20))}};}catch(error){return rejectWithValue(error.message||'Failed to fetch animals');}});export const fetchAnimalById=createAsyncThunk('animals/fetchAnimalById',async(id,_ref2)=>{let{rejectWithValue}=_ref2;try{// Mock API call\nawait new Promise(resolve=>setTimeout(resolve,500));// Mock data\nconst mockAnimal={_id:id,tagNumber:'C001',name:'Bessie',species:'cattle',breed:'Holstein',gender:'female',dateOfBirth:'2022-03-15',color:'Black and White',currentWeight:450,birthWeight:35,status:'active',healthStatus:'healthy',breedingStatus:'available',isActive:true,createdBy:'user1',createdAt:'2023-01-01',updatedAt:'2023-01-01',age:1,ageInDays:365,latestWeight:450};return mockAnimal;}catch(error){return rejectWithValue(error.message||'Failed to fetch animal');}});export const fetchAnimalStatistics=createAsyncThunk('animals/fetchStatistics',async(_,_ref3)=>{let{rejectWithValue}=_ref3;try{// Mock API call\nawait new Promise(resolve=>setTimeout(resolve,500));return{total:150,bySpecies:{cattle:75,sheep:45,goat:20,pig:10},byStatus:{active:140,sold:5,deceased:3,transferred:2},byHealthStatus:{healthy:135,sick:8,injured:4,recovering:3},byBreedingStatus:{available:80,pregnant:25,lactating:30,breeding:10,retired:5}};}catch(error){return rejectWithValue(error.message||'Failed to fetch statistics');}});const animalSlice=createSlice({name:'animals',initialState,reducers:{setFilters:(state,action)=>{state.filters=_objectSpread(_objectSpread({},state.filters),action.payload);},clearFilters:state=>{state.filters={};},setSelectedAnimal:(state,action)=>{state.selectedAnimal=action.payload;},setPagination:(state,action)=>{state.pagination=_objectSpread(_objectSpread({},state.pagination),action.payload);},clearError:state=>{state.error=null;}},extraReducers:builder=>{builder// Fetch animals\n.addCase(fetchAnimals.pending,state=>{state.isLoading=true;state.error=null;}).addCase(fetchAnimals.fulfilled,(state,action)=>{state.isLoading=false;state.animals=action.payload.animals;state.pagination=action.payload.pagination;state.error=null;}).addCase(fetchAnimals.rejected,(state,action)=>{state.isLoading=false;state.error=action.payload;})// Fetch animal by ID\n.addCase(fetchAnimalById.pending,state=>{state.isLoading=true;state.error=null;}).addCase(fetchAnimalById.fulfilled,(state,action)=>{state.isLoading=false;state.selectedAnimal=action.payload;state.error=null;}).addCase(fetchAnimalById.rejected,(state,action)=>{state.isLoading=false;state.error=action.payload;})// Fetch statistics\n.addCase(fetchAnimalStatistics.fulfilled,(state,action)=>{state.statistics=action.payload;});}});export const{setFilters,clearFilters,setSelectedAnimal,setPagination,clearError}=animalSlice.actions;export default animalSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "initialState", "animals", "selectedAnimal", "filters", "pagination", "page", "limit", "total", "totalPages", "isLoading", "error", "statistics", "fetchAnimals", "params", "_ref", "rejectWithValue", "Promise", "resolve", "setTimeout", "mockAnimals", "_id", "tagNumber", "name", "species", "breed", "gender", "dateOfBirth", "color", "currentWeight", "birthWeight", "status", "healthStatus", "breedingStatus", "isActive", "created<PERSON>y", "createdAt", "updatedAt", "age", "ageInDays", "latestWeight", "length", "Math", "ceil", "message", "fetchAnimalById", "id", "_ref2", "mockAnimal", "fetchAnimalStatistics", "_", "_ref3", "bySpecies", "cattle", "sheep", "goat", "pig", "byStatus", "active", "sold", "deceased", "transferred", "byHealthStatus", "healthy", "sick", "injured", "recovering", "byBreedingStatus", "available", "pregnant", "lactating", "breeding", "retired", "animalSlice", "reducers", "setFilters", "state", "action", "_objectSpread", "payload", "clearFilters", "setSelectedAnimal", "setPagination", "clearError", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "actions", "reducer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/store/slices/animalSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\n\nexport interface Animal {\n  _id: string;\n  tagNumber: string;\n  name?: string;\n  species: 'cattle' | 'sheep' | 'goat' | 'pig' | 'chicken' | 'horse' | 'other';\n  breed: string;\n  gender: 'male' | 'female';\n  dateOfBirth: string;\n  color?: string;\n  markings?: string;\n  currentWeight?: number;\n  birthWeight?: number;\n  rfidTag?: string;\n  earTagNumber?: string;\n  microchipId?: string;\n  sire?: string;\n  dam?: string;\n  status: 'active' | 'sold' | 'deceased' | 'transferred' | 'quarantine';\n  location?: {\n    paddock?: string;\n    barn?: string;\n    coordinates?: {\n      latitude: number;\n      longitude: number;\n    };\n  };\n  healthStatus: 'healthy' | 'sick' | 'injured' | 'recovering' | 'quarantine';\n  lastHealthCheck?: string;\n  nextHealthCheck?: string;\n  breedingStatus: 'available' | 'pregnant' | 'lactating' | 'breeding' | 'retired';\n  lastBreedingDate?: string;\n  expectedDueDate?: string;\n  productionType?: 'meat' | 'milk' | 'eggs' | 'wool' | 'breeding' | 'work' | 'show';\n  productionRecords?: Array<{\n    date: string;\n    type: 'milk' | 'eggs' | 'wool' | 'weight';\n    quantity: number;\n    unit: string;\n    notes?: string;\n  }>;\n  weightHistory?: Array<{\n    date: string;\n    weight: number;\n    measuredBy: string;\n    notes?: string;\n  }>;\n  purchasePrice?: number;\n  purchaseDate?: string;\n  currentValue?: number;\n  images?: Array<{\n    url: string;\n    caption?: string;\n    uploadDate: string;\n  }>;\n  documents?: Array<{\n    name: string;\n    url: string;\n    type: string;\n    uploadDate: string;\n  }>;\n  notes?: string;\n  isActive: boolean;\n  createdBy: string;\n  updatedBy?: string;\n  createdAt: string;\n  updatedAt: string;\n  age?: number;\n  ageInDays?: number;\n  latestWeight?: number;\n}\n\nexport interface AnimalFilters {\n  species?: string;\n  breed?: string;\n  gender?: string;\n  status?: string;\n  healthStatus?: string;\n  breedingStatus?: string;\n  location?: string;\n  ageRange?: {\n    min?: number;\n    max?: number;\n  };\n  weightRange?: {\n    min?: number;\n    max?: number;\n  };\n  search?: string;\n}\n\nexport interface AnimalState {\n  animals: Animal[];\n  selectedAnimal: Animal | null;\n  filters: AnimalFilters;\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n  isLoading: boolean;\n  error: string | null;\n  statistics: {\n    total: number;\n    bySpecies: Record<string, number>;\n    byStatus: Record<string, number>;\n    byHealthStatus: Record<string, number>;\n    byBreedingStatus: Record<string, number>;\n  } | null;\n}\n\nconst initialState: AnimalState = {\n  animals: [],\n  selectedAnimal: null,\n  filters: {},\n  pagination: {\n    page: 1,\n    limit: 20,\n    total: 0,\n    totalPages: 0,\n  },\n  isLoading: false,\n  error: null,\n  statistics: null,\n};\n\n// Mock async thunks (replace with actual API calls)\nexport const fetchAnimals = createAsyncThunk(\n  'animals/fetchAnimals',\n  async (params: { page?: number; limit?: number; filters?: AnimalFilters }, { rejectWithValue }) => {\n    try {\n      // Mock API call - replace with actual API\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Mock data\n      const mockAnimals: Animal[] = [\n        {\n          _id: '1',\n          tagNumber: 'C001',\n          name: 'Bessie',\n          species: 'cattle',\n          breed: 'Holstein',\n          gender: 'female',\n          dateOfBirth: '2022-03-15',\n          color: 'Black and White',\n          currentWeight: 450,\n          birthWeight: 35,\n          status: 'active',\n          healthStatus: 'healthy',\n          breedingStatus: 'available',\n          isActive: true,\n          createdBy: 'user1',\n          createdAt: '2023-01-01',\n          updatedAt: '2023-01-01',\n          age: 1,\n          ageInDays: 365,\n          latestWeight: 450,\n        },\n        {\n          _id: '2',\n          tagNumber: 'S001',\n          name: 'Woolly',\n          species: 'sheep',\n          breed: 'Merino',\n          gender: 'male',\n          dateOfBirth: '2023-01-10',\n          color: 'White',\n          currentWeight: 75,\n          birthWeight: 4,\n          status: 'active',\n          healthStatus: 'healthy',\n          breedingStatus: 'available',\n          isActive: true,\n          createdBy: 'user1',\n          createdAt: '2023-01-10',\n          updatedAt: '2023-01-10',\n          age: 0,\n          ageInDays: 180,\n          latestWeight: 75,\n        },\n      ];\n      \n      return {\n        animals: mockAnimals,\n        pagination: {\n          page: params.page || 1,\n          limit: params.limit || 20,\n          total: mockAnimals.length,\n          totalPages: Math.ceil(mockAnimals.length / (params.limit || 20)),\n        },\n      };\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to fetch animals');\n    }\n  }\n);\n\nexport const fetchAnimalById = createAsyncThunk(\n  'animals/fetchAnimalById',\n  async (id: string, { rejectWithValue }) => {\n    try {\n      // Mock API call\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      // Mock data\n      const mockAnimal: Animal = {\n        _id: id,\n        tagNumber: 'C001',\n        name: 'Bessie',\n        species: 'cattle',\n        breed: 'Holstein',\n        gender: 'female',\n        dateOfBirth: '2022-03-15',\n        color: 'Black and White',\n        currentWeight: 450,\n        birthWeight: 35,\n        status: 'active',\n        healthStatus: 'healthy',\n        breedingStatus: 'available',\n        isActive: true,\n        createdBy: 'user1',\n        createdAt: '2023-01-01',\n        updatedAt: '2023-01-01',\n        age: 1,\n        ageInDays: 365,\n        latestWeight: 450,\n      };\n      \n      return mockAnimal;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to fetch animal');\n    }\n  }\n);\n\nexport const fetchAnimalStatistics = createAsyncThunk(\n  'animals/fetchStatistics',\n  async (_, { rejectWithValue }) => {\n    try {\n      // Mock API call\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      return {\n        total: 150,\n        bySpecies: {\n          cattle: 75,\n          sheep: 45,\n          goat: 20,\n          pig: 10,\n        },\n        byStatus: {\n          active: 140,\n          sold: 5,\n          deceased: 3,\n          transferred: 2,\n        },\n        byHealthStatus: {\n          healthy: 135,\n          sick: 8,\n          injured: 4,\n          recovering: 3,\n        },\n        byBreedingStatus: {\n          available: 80,\n          pregnant: 25,\n          lactating: 30,\n          breeding: 10,\n          retired: 5,\n        },\n      };\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to fetch statistics');\n    }\n  }\n);\n\nconst animalSlice = createSlice({\n  name: 'animals',\n  initialState,\n  reducers: {\n    setFilters: (state, action: PayloadAction<AnimalFilters>) => {\n      state.filters = { ...state.filters, ...action.payload };\n    },\n    clearFilters: (state) => {\n      state.filters = {};\n    },\n    setSelectedAnimal: (state, action: PayloadAction<Animal | null>) => {\n      state.selectedAnimal = action.payload;\n    },\n    setPagination: (state, action: PayloadAction<Partial<typeof initialState.pagination>>) => {\n      state.pagination = { ...state.pagination, ...action.payload };\n    },\n    clearError: (state) => {\n      state.error = null;\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // Fetch animals\n      .addCase(fetchAnimals.pending, (state) => {\n        state.isLoading = true;\n        state.error = null;\n      })\n      .addCase(fetchAnimals.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.animals = action.payload.animals;\n        state.pagination = action.payload.pagination;\n        state.error = null;\n      })\n      .addCase(fetchAnimals.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      })\n      \n      // Fetch animal by ID\n      .addCase(fetchAnimalById.pending, (state) => {\n        state.isLoading = true;\n        state.error = null;\n      })\n      .addCase(fetchAnimalById.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.selectedAnimal = action.payload;\n        state.error = null;\n      })\n      .addCase(fetchAnimalById.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      })\n      \n      // Fetch statistics\n      .addCase(fetchAnimalStatistics.fulfilled, (state, action) => {\n        state.statistics = action.payload;\n      });\n  },\n});\n\nexport const { setFilters, clearFilters, setSelectedAnimal, setPagination, clearError } = animalSlice.actions;\nexport default animalSlice.reducer;\n"], "mappings": "gJAAA,OAASA,WAAW,CAAEC,gBAAgB,KAAuB,kBAAkB,CAiH/E,KAAM,CAAAC,YAAyB,CAAG,CAChCC,OAAO,CAAE,EAAE,CACXC,cAAc,CAAE,IAAI,CACpBC,OAAO,CAAE,CAAC,CAAC,CACXC,UAAU,CAAE,CACVC,IAAI,CAAE,CAAC,CACPC,KAAK,CAAE,EAAE,CACTC,KAAK,CAAE,CAAC,CACRC,UAAU,CAAE,CACd,CAAC,CACDC,SAAS,CAAE,KAAK,CAChBC,KAAK,CAAE,IAAI,CACXC,UAAU,CAAE,IACd,CAAC,CAED;AACA,MAAO,MAAM,CAAAC,YAAY,CAAGb,gBAAgB,CAC1C,sBAAsB,CACtB,MAAOc,MAAkE,CAAAC,IAAA,GAA0B,IAAxB,CAAEC,eAAgB,CAAC,CAAAD,IAAA,CAC5F,GAAI,CACF;AACA,KAAM,IAAI,CAAAE,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CAEvD;AACA,KAAM,CAAAE,WAAqB,CAAG,CAC5B,CACEC,GAAG,CAAE,GAAG,CACRC,SAAS,CAAE,MAAM,CACjBC,IAAI,CAAE,QAAQ,CACdC,OAAO,CAAE,QAAQ,CACjBC,KAAK,CAAE,UAAU,CACjBC,MAAM,CAAE,QAAQ,CAChBC,WAAW,CAAE,YAAY,CACzBC,KAAK,CAAE,iBAAiB,CACxBC,aAAa,CAAE,GAAG,CAClBC,WAAW,CAAE,EAAE,CACfC,MAAM,CAAE,QAAQ,CAChBC,YAAY,CAAE,SAAS,CACvBC,cAAc,CAAE,WAAW,CAC3BC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,OAAO,CAClBC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,CAAC,CACNC,SAAS,CAAE,GAAG,CACdC,YAAY,CAAE,GAChB,CAAC,CACD,CACEnB,GAAG,CAAE,GAAG,CACRC,SAAS,CAAE,MAAM,CACjBC,IAAI,CAAE,QAAQ,CACdC,OAAO,CAAE,OAAO,CAChBC,KAAK,CAAE,QAAQ,CACfC,MAAM,CAAE,MAAM,CACdC,WAAW,CAAE,YAAY,CACzBC,KAAK,CAAE,OAAO,CACdC,aAAa,CAAE,EAAE,CACjBC,WAAW,CAAE,CAAC,CACdC,MAAM,CAAE,QAAQ,CAChBC,YAAY,CAAE,SAAS,CACvBC,cAAc,CAAE,WAAW,CAC3BC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,OAAO,CAClBC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,CAAC,CACNC,SAAS,CAAE,GAAG,CACdC,YAAY,CAAE,EAChB,CAAC,CACF,CAED,MAAO,CACLtC,OAAO,CAAEkB,WAAW,CACpBf,UAAU,CAAE,CACVC,IAAI,CAAEQ,MAAM,CAACR,IAAI,EAAI,CAAC,CACtBC,KAAK,CAAEO,MAAM,CAACP,KAAK,EAAI,EAAE,CACzBC,KAAK,CAAEY,WAAW,CAACqB,MAAM,CACzBhC,UAAU,CAAEiC,IAAI,CAACC,IAAI,CAACvB,WAAW,CAACqB,MAAM,EAAI3B,MAAM,CAACP,KAAK,EAAI,EAAE,CAAC,CACjE,CACF,CAAC,CACH,CAAE,MAAOI,KAAU,CAAE,CACnB,MAAO,CAAAK,eAAe,CAACL,KAAK,CAACiC,OAAO,EAAI,yBAAyB,CAAC,CACpE,CACF,CACF,CAAC,CAED,MAAO,MAAM,CAAAC,eAAe,CAAG7C,gBAAgB,CAC7C,yBAAyB,CACzB,MAAO8C,EAAU,CAAAC,KAAA,GAA0B,IAAxB,CAAE/B,eAAgB,CAAC,CAAA+B,KAAA,CACpC,GAAI,CACF;AACA,KAAM,IAAI,CAAA9B,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,GAAG,CAAC,CAAC,CAEtD;AACA,KAAM,CAAA8B,UAAkB,CAAG,CACzB3B,GAAG,CAAEyB,EAAE,CACPxB,SAAS,CAAE,MAAM,CACjBC,IAAI,CAAE,QAAQ,CACdC,OAAO,CAAE,QAAQ,CACjBC,KAAK,CAAE,UAAU,CACjBC,MAAM,CAAE,QAAQ,CAChBC,WAAW,CAAE,YAAY,CACzBC,KAAK,CAAE,iBAAiB,CACxBC,aAAa,CAAE,GAAG,CAClBC,WAAW,CAAE,EAAE,CACfC,MAAM,CAAE,QAAQ,CAChBC,YAAY,CAAE,SAAS,CACvBC,cAAc,CAAE,WAAW,CAC3BC,QAAQ,CAAE,IAAI,CACdC,SAAS,CAAE,OAAO,CAClBC,SAAS,CAAE,YAAY,CACvBC,SAAS,CAAE,YAAY,CACvBC,GAAG,CAAE,CAAC,CACNC,SAAS,CAAE,GAAG,CACdC,YAAY,CAAE,GAChB,CAAC,CAED,MAAO,CAAAQ,UAAU,CACnB,CAAE,MAAOrC,KAAU,CAAE,CACnB,MAAO,CAAAK,eAAe,CAACL,KAAK,CAACiC,OAAO,EAAI,wBAAwB,CAAC,CACnE,CACF,CACF,CAAC,CAED,MAAO,MAAM,CAAAK,qBAAqB,CAAGjD,gBAAgB,CACnD,yBAAyB,CACzB,MAAOkD,CAAC,CAAAC,KAAA,GAA0B,IAAxB,CAAEnC,eAAgB,CAAC,CAAAmC,KAAA,CAC3B,GAAI,CACF;AACA,KAAM,IAAI,CAAAlC,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,GAAG,CAAC,CAAC,CAEtD,MAAO,CACLV,KAAK,CAAE,GAAG,CACV4C,SAAS,CAAE,CACTC,MAAM,CAAE,EAAE,CACVC,KAAK,CAAE,EAAE,CACTC,IAAI,CAAE,EAAE,CACRC,GAAG,CAAE,EACP,CAAC,CACDC,QAAQ,CAAE,CACRC,MAAM,CAAE,GAAG,CACXC,IAAI,CAAE,CAAC,CACPC,QAAQ,CAAE,CAAC,CACXC,WAAW,CAAE,CACf,CAAC,CACDC,cAAc,CAAE,CACdC,OAAO,CAAE,GAAG,CACZC,IAAI,CAAE,CAAC,CACPC,OAAO,CAAE,CAAC,CACVC,UAAU,CAAE,CACd,CAAC,CACDC,gBAAgB,CAAE,CAChBC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,SAAS,CAAE,EAAE,CACbC,QAAQ,CAAE,EAAE,CACZC,OAAO,CAAE,CACX,CACF,CAAC,CACH,CAAE,MAAO7D,KAAU,CAAE,CACnB,MAAO,CAAAK,eAAe,CAACL,KAAK,CAACiC,OAAO,EAAI,4BAA4B,CAAC,CACvE,CACF,CACF,CAAC,CAED,KAAM,CAAA6B,WAAW,CAAG1E,WAAW,CAAC,CAC9BwB,IAAI,CAAE,SAAS,CACftB,YAAY,CACZyE,QAAQ,CAAE,CACRC,UAAU,CAAEA,CAACC,KAAK,CAAEC,MAAoC,GAAK,CAC3DD,KAAK,CAACxE,OAAO,CAAA0E,aAAA,CAAAA,aAAA,IAAQF,KAAK,CAACxE,OAAO,EAAKyE,MAAM,CAACE,OAAO,CAAE,CACzD,CAAC,CACDC,YAAY,CAAGJ,KAAK,EAAK,CACvBA,KAAK,CAACxE,OAAO,CAAG,CAAC,CAAC,CACpB,CAAC,CACD6E,iBAAiB,CAAEA,CAACL,KAAK,CAAEC,MAAoC,GAAK,CAClED,KAAK,CAACzE,cAAc,CAAG0E,MAAM,CAACE,OAAO,CACvC,CAAC,CACDG,aAAa,CAAEA,CAACN,KAAK,CAAEC,MAA8D,GAAK,CACxFD,KAAK,CAACvE,UAAU,CAAAyE,aAAA,CAAAA,aAAA,IAAQF,KAAK,CAACvE,UAAU,EAAKwE,MAAM,CAACE,OAAO,CAAE,CAC/D,CAAC,CACDI,UAAU,CAAGP,KAAK,EAAK,CACrBA,KAAK,CAACjE,KAAK,CAAG,IAAI,CACpB,CACF,CAAC,CACDyE,aAAa,CAAGC,OAAO,EAAK,CAC1BA,OACE;AAAA,CACCC,OAAO,CAACzE,YAAY,CAAC0E,OAAO,CAAGX,KAAK,EAAK,CACxCA,KAAK,CAAClE,SAAS,CAAG,IAAI,CACtBkE,KAAK,CAACjE,KAAK,CAAG,IAAI,CACpB,CAAC,CAAC,CACD2E,OAAO,CAACzE,YAAY,CAAC2E,SAAS,CAAE,CAACZ,KAAK,CAAEC,MAAM,GAAK,CAClDD,KAAK,CAAClE,SAAS,CAAG,KAAK,CACvBkE,KAAK,CAAC1E,OAAO,CAAG2E,MAAM,CAACE,OAAO,CAAC7E,OAAO,CACtC0E,KAAK,CAACvE,UAAU,CAAGwE,MAAM,CAACE,OAAO,CAAC1E,UAAU,CAC5CuE,KAAK,CAACjE,KAAK,CAAG,IAAI,CACpB,CAAC,CAAC,CACD2E,OAAO,CAACzE,YAAY,CAAC4E,QAAQ,CAAE,CAACb,KAAK,CAAEC,MAAM,GAAK,CACjDD,KAAK,CAAClE,SAAS,CAAG,KAAK,CACvBkE,KAAK,CAACjE,KAAK,CAAGkE,MAAM,CAACE,OAAiB,CACxC,CAAC,CAED;AAAA,CACCO,OAAO,CAACzC,eAAe,CAAC0C,OAAO,CAAGX,KAAK,EAAK,CAC3CA,KAAK,CAAClE,SAAS,CAAG,IAAI,CACtBkE,KAAK,CAACjE,KAAK,CAAG,IAAI,CACpB,CAAC,CAAC,CACD2E,OAAO,CAACzC,eAAe,CAAC2C,SAAS,CAAE,CAACZ,KAAK,CAAEC,MAAM,GAAK,CACrDD,KAAK,CAAClE,SAAS,CAAG,KAAK,CACvBkE,KAAK,CAACzE,cAAc,CAAG0E,MAAM,CAACE,OAAO,CACrCH,KAAK,CAACjE,KAAK,CAAG,IAAI,CACpB,CAAC,CAAC,CACD2E,OAAO,CAACzC,eAAe,CAAC4C,QAAQ,CAAE,CAACb,KAAK,CAAEC,MAAM,GAAK,CACpDD,KAAK,CAAClE,SAAS,CAAG,KAAK,CACvBkE,KAAK,CAACjE,KAAK,CAAGkE,MAAM,CAACE,OAAiB,CACxC,CAAC,CAED;AAAA,CACCO,OAAO,CAACrC,qBAAqB,CAACuC,SAAS,CAAE,CAACZ,KAAK,CAAEC,MAAM,GAAK,CAC3DD,KAAK,CAAChE,UAAU,CAAGiE,MAAM,CAACE,OAAO,CACnC,CAAC,CAAC,CACN,CACF,CAAC,CAAC,CAEF,MAAO,MAAM,CAAEJ,UAAU,CAAEK,YAAY,CAAEC,iBAAiB,CAAEC,aAAa,CAAEC,UAAW,CAAC,CAAGV,WAAW,CAACiB,OAAO,CAC7G,cAAe,CAAAjB,WAAW,CAACkB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}