{"ast": null, "code": "export default function ownerDocument(node) {\n  return node && node.ownerDocument || document;\n}", "map": {"version": 3, "names": ["ownerDocument", "node", "document"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/utils/esm/ownerDocument/ownerDocument.js"], "sourcesContent": ["export default function ownerDocument(node) {\n  return node && node.ownerDocument || document;\n}"], "mappings": "AAAA,eAAe,SAASA,aAAaA,CAACC,IAAI,EAAE;EAC1C,OAAOA,IAAI,IAAIA,IAAI,CAACD,aAAa,IAAIE,QAAQ;AAC/C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}