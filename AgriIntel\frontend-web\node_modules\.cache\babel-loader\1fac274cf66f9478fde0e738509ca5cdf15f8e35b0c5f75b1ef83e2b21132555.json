{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.KillCursorsOperation = void 0;\nconst error_1 = require(\"../error\");\nconst utils_1 = require(\"../utils\");\nconst operation_1 = require(\"./operation\");\nclass KillCursorsOperation extends operation_1.AbstractOperation {\n  constructor(cursorId, ns, server, options) {\n    super(options);\n    this.ns = ns;\n    this.cursorId = cursorId;\n    this.server = server;\n  }\n  get commandName() {\n    return 'killCursors';\n  }\n  async execute(server, session, timeoutContext) {\n    if (server !== this.server) {\n      throw new error_1.MongoRuntimeError('Killcursor must run on the same server operation began on');\n    }\n    const killCursors = this.ns.collection;\n    if (killCursors == null) {\n      // Cursors should have adopted the namespace returned by MongoDB\n      // which should always defined a collection name (even a pseudo one, ex. db.aggregate())\n      throw new error_1.MongoRuntimeError('A collection name must be determined before killCursors');\n    }\n    const killCursorsCommand = {\n      killCursors,\n      cursors: [this.cursorId]\n    };\n    try {\n      await server.command(this.ns, killCursorsCommand, {\n        session,\n        timeoutContext\n      });\n    } catch (error) {\n      // The driver should never emit errors from killCursors, this is spec-ed behavior\n      (0, utils_1.squashError)(error);\n    }\n  }\n}\nexports.KillCursorsOperation = KillCursorsOperation;\n(0, operation_1.defineAspects)(KillCursorsOperation, [operation_1.Aspect.MUST_SELECT_SAME_SERVER]);", "map": {"version": 3, "names": ["error_1", "require", "utils_1", "operation_1", "KillCursorsOperation", "AbstractOperation", "constructor", "cursorId", "ns", "server", "options", "commandName", "execute", "session", "timeoutContext", "MongoRuntimeError", "killCursors", "collection", "killCursorsCommand", "cursors", "command", "error", "squashError", "exports", "defineAspects", "Aspect", "MUST_SELECT_SAME_SERVER"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\kill_cursors.ts"], "sourcesContent": ["import type { Long } from '../bson';\nimport { MongoRuntimeError } from '../error';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { type MongoDBNamespace, squashError } from '../utils';\nimport { AbstractOperation, Aspect, defineAspects, type OperationOptions } from './operation';\n\n/**\n * https://www.mongodb.com/docs/manual/reference/command/killCursors/\n * @internal\n */\ninterface KillCursorsCommand {\n  killCursors: string;\n  cursors: Long[];\n  comment?: unknown;\n}\n\nexport class KillCursorsOperation extends AbstractOperation {\n  cursorId: Long;\n\n  constructor(cursorId: Long, ns: MongoDBNamespace, server: Server, options: OperationOptions) {\n    super(options);\n    this.ns = ns;\n    this.cursorId = cursorId;\n    this.server = server;\n  }\n\n  override get commandName() {\n    return 'killCursors' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<void> {\n    if (server !== this.server) {\n      throw new MongoRuntimeError('Killcursor must run on the same server operation began on');\n    }\n\n    const killCursors = this.ns.collection;\n    if (killCursors == null) {\n      // Cursors should have adopted the namespace returned by MongoDB\n      // which should always defined a collection name (even a pseudo one, ex. db.aggregate())\n      throw new MongoRuntimeError('A collection name must be determined before killCursors');\n    }\n\n    const killCursorsCommand: KillCursorsCommand = {\n      killCursors,\n      cursors: [this.cursorId]\n    };\n    try {\n      await server.command(this.ns, killCursorsCommand, {\n        session,\n        timeoutContext\n      });\n    } catch (error) {\n      // The driver should never emit errors from killCursors, this is spec-ed behavior\n      squashError(error);\n    }\n  }\n}\n\ndefineAspects(KillCursorsOperation, [Aspect.MUST_SELECT_SAME_SERVER]);\n"], "mappings": ";;;;;;AACA,MAAAA,OAAA,GAAAC,OAAA;AAIA,MAAAC,OAAA,GAAAD,OAAA;AACA,MAAAE,WAAA,GAAAF,OAAA;AAYA,MAAaG,oBAAqB,SAAQD,WAAA,CAAAE,iBAAiB;EAGzDC,YAAYC,QAAc,EAAEC,EAAoB,EAAEC,MAAc,EAAEC,OAAyB;IACzF,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACF,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,MAAM,GAAGA,MAAM;EACtB;EAEA,IAAaE,WAAWA,CAAA;IACtB,OAAO,aAAsB;EAC/B;EAES,MAAMC,OAAOA,CACpBH,MAAc,EACdI,OAAkC,EAClCC,cAA8B;IAE9B,IAAIL,MAAM,KAAK,IAAI,CAACA,MAAM,EAAE;MAC1B,MAAM,IAAIT,OAAA,CAAAe,iBAAiB,CAAC,2DAA2D,CAAC;IAC1F;IAEA,MAAMC,WAAW,GAAG,IAAI,CAACR,EAAE,CAACS,UAAU;IACtC,IAAID,WAAW,IAAI,IAAI,EAAE;MACvB;MACA;MACA,MAAM,IAAIhB,OAAA,CAAAe,iBAAiB,CAAC,yDAAyD,CAAC;IACxF;IAEA,MAAMG,kBAAkB,GAAuB;MAC7CF,WAAW;MACXG,OAAO,EAAE,CAAC,IAAI,CAACZ,QAAQ;KACxB;IACD,IAAI;MACF,MAAME,MAAM,CAACW,OAAO,CAAC,IAAI,CAACZ,EAAE,EAAEU,kBAAkB,EAAE;QAChDL,OAAO;QACPC;OACD,CAAC;IACJ,CAAC,CAAC,OAAOO,KAAK,EAAE;MACd;MACA,IAAAnB,OAAA,CAAAoB,WAAW,EAACD,KAAK,CAAC;IACpB;EACF;;AA3CFE,OAAA,CAAAnB,oBAAA,GAAAA,oBAAA;AA8CA,IAAAD,WAAA,CAAAqB,aAAa,EAACpB,oBAAoB,EAAE,CAACD,WAAA,CAAAsB,MAAM,CAACC,uBAAuB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}