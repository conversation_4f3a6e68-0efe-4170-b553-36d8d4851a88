{"ast": null, "code": "import slotShouldForwardProp from './slotShouldForwardProp';\nconst rootShouldForwardProp = prop => slotShouldForwardProp(prop) && prop !== 'classes';\nexport default rootShouldForwardProp;", "map": {"version": 3, "names": ["slotShouldForwardProp", "rootShouldForwardProp", "prop"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/material/styles/rootShouldForwardProp.js"], "sourcesContent": ["import slotShouldForwardProp from './slotShouldForwardProp';\nconst rootShouldForwardProp = prop => slotShouldForwardProp(prop) && prop !== 'classes';\nexport default rootShouldForwardProp;"], "mappings": "AAAA,OAAOA,qBAAqB,MAAM,yBAAyB;AAC3D,MAAMC,qBAAqB,GAAGC,IAAI,IAAIF,qBAAqB,CAACE,IAAI,CAAC,IAAIA,IAAI,KAAK,SAAS;AACvF,eAAeD,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}