{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.EstimatedDocumentCountOperation = void 0;\nconst command_1 = require(\"./command\");\nconst operation_1 = require(\"./operation\");\n/** @internal */\nclass EstimatedDocumentCountOperation extends command_1.CommandOperation {\n  constructor(collection, options = {}) {\n    super(collection, options);\n    this.options = options;\n    this.collectionName = collection.collectionName;\n  }\n  get commandName() {\n    return 'count';\n  }\n  async execute(server, session, timeoutContext) {\n    const cmd = {\n      count: this.collectionName\n    };\n    if (typeof this.options.maxTimeMS === 'number') {\n      cmd.maxTimeMS = this.options.maxTimeMS;\n    }\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (this.options.comment !== undefined) {\n      cmd.comment = this.options.comment;\n    }\n    const response = await super.executeCommand(server, session, cmd, timeoutContext);\n    return response?.n || 0;\n  }\n}\nexports.EstimatedDocumentCountOperation = EstimatedDocumentCountOperation;\n(0, operation_1.defineAspects)(EstimatedDocumentCountOperation, [operation_1.Aspect.READ_OPERATION, operation_1.Aspect.RETRYABLE, operation_1.Aspect.CURSOR_CREATING]);", "map": {"version": 3, "names": ["command_1", "require", "operation_1", "EstimatedDocumentCountOperation", "CommandOperation", "constructor", "collection", "options", "collectionName", "commandName", "execute", "server", "session", "timeoutContext", "cmd", "count", "maxTimeMS", "comment", "undefined", "response", "executeCommand", "n", "exports", "defineAspects", "Aspect", "READ_OPERATION", "RETRYABLE", "CURSOR_CREATING"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\estimated_document_count.ts"], "sourcesContent": ["import type { Document } from '../bson';\nimport type { Collection } from '../collection';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { CommandOperation, type CommandOperationOptions } from './command';\nimport { Aspect, defineAspects } from './operation';\n\n/** @public */\nexport interface EstimatedDocumentCountOptions extends CommandOperationOptions {\n  /**\n   * The maximum amount of time to allow the operation to run.\n   *\n   * This option is sent only if the caller explicitly provides a value. The default is to not send a value.\n   */\n  maxTimeMS?: number;\n}\n\n/** @internal */\nexport class EstimatedDocumentCountOperation extends CommandOperation<number> {\n  override options: EstimatedDocumentCountOptions;\n  collectionName: string;\n\n  constructor(collection: Collection, options: EstimatedDocumentCountOptions = {}) {\n    super(collection, options);\n    this.options = options;\n    this.collectionName = collection.collectionName;\n  }\n\n  override get commandName() {\n    return 'count' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<number> {\n    const cmd: Document = { count: this.collectionName };\n\n    if (typeof this.options.maxTimeMS === 'number') {\n      cmd.maxTimeMS = this.options.maxTimeMS;\n    }\n\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (this.options.comment !== undefined) {\n      cmd.comment = this.options.comment;\n    }\n\n    const response = await super.executeCommand(server, session, cmd, timeoutContext);\n\n    return response?.n || 0;\n  }\n}\n\ndefineAspects(EstimatedDocumentCountOperation, [\n  Aspect.READ_OPERATION,\n  Aspect.RETRYABLE,\n  Aspect.CURSOR_CREATING\n]);\n"], "mappings": ";;;;;;AAKA,MAAAA,SAAA,GAAAC,OAAA;AACA,MAAAC,WAAA,GAAAD,OAAA;AAYA;AACA,MAAaE,+BAAgC,SAAQH,SAAA,CAAAI,gBAAwB;EAI3EC,YAAYC,UAAsB,EAAEC,OAAA,GAAyC,EAAE;IAC7E,KAAK,CAACD,UAAU,EAAEC,OAAO,CAAC;IAC1B,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,cAAc,GAAGF,UAAU,CAACE,cAAc;EACjD;EAEA,IAAaC,WAAWA,CAAA;IACtB,OAAO,OAAgB;EACzB;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMC,GAAG,GAAa;MAAEC,KAAK,EAAE,IAAI,CAACP;IAAc,CAAE;IAEpD,IAAI,OAAO,IAAI,CAACD,OAAO,CAACS,SAAS,KAAK,QAAQ,EAAE;MAC9CF,GAAG,CAACE,SAAS,GAAG,IAAI,CAACT,OAAO,CAACS,SAAS;IACxC;IAEA;IACA;IACA,IAAI,IAAI,CAACT,OAAO,CAACU,OAAO,KAAKC,SAAS,EAAE;MACtCJ,GAAG,CAACG,OAAO,GAAG,IAAI,CAACV,OAAO,CAACU,OAAO;IACpC;IAEA,MAAME,QAAQ,GAAG,MAAM,KAAK,CAACC,cAAc,CAACT,MAAM,EAAEC,OAAO,EAAEE,GAAG,EAAED,cAAc,CAAC;IAEjF,OAAOM,QAAQ,EAAEE,CAAC,IAAI,CAAC;EACzB;;AAlCFC,OAAA,CAAAnB,+BAAA,GAAAA,+BAAA;AAqCA,IAAAD,WAAA,CAAAqB,aAAa,EAACpB,+BAA+B,EAAE,CAC7CD,WAAA,CAAAsB,MAAM,CAACC,cAAc,EACrBvB,WAAA,CAAAsB,MAAM,CAACE,SAAS,EAChBxB,WAAA,CAAAsB,MAAM,CAACG,eAAe,CACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}