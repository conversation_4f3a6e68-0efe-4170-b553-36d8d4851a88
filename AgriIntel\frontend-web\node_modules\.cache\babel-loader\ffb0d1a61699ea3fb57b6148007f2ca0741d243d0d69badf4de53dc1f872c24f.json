{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"title\", \"titleId\"];\nimport * as React from \"react\";\nfunction BellAlertIcon(_ref, svgRef) {\n  let {\n      title,\n      titleId\n    } = _ref,\n    props = _objectWithoutProperties(_ref, _excluded);\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0M3.124 7.5A8.969 8.969 0 0 1 5.292 3m13.416 0a8.969 8.969 0 0 1 2.168 4.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/React.forwardRef(BellAlertIcon);\nexport default ForwardRef;", "map": {"version": 3, "names": ["React", "BellAlertIcon", "_ref", "svgRef", "title", "titleId", "props", "_objectWithoutProperties", "_excluded", "createElement", "Object", "assign", "xmlns", "fill", "viewBox", "strokeWidth", "stroke", "ref", "id", "strokeLinecap", "strokeLinejoin", "d", "ForwardRef", "forwardRef"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@heroicons/react/24/outline/esm/BellAlertIcon.js"], "sourcesContent": ["import * as React from \"react\";\nfunction BellAlertIcon({\n  title,\n  titleId,\n  ...props\n}, svgRef) {\n  return /*#__PURE__*/React.createElement(\"svg\", Object.assign({\n    xmlns: \"http://www.w3.org/2000/svg\",\n    fill: \"none\",\n    viewBox: \"0 0 24 24\",\n    strokeWidth: 1.5,\n    stroke: \"currentColor\",\n    \"aria-hidden\": \"true\",\n    \"data-slot\": \"icon\",\n    ref: svgRef,\n    \"aria-labelledby\": titleId\n  }, props), title ? /*#__PURE__*/React.createElement(\"title\", {\n    id: titleId\n  }, title) : null, /*#__PURE__*/React.createElement(\"path\", {\n    strokeLinecap: \"round\",\n    strokeLinejoin: \"round\",\n    d: \"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0M3.124 7.5A8.969 8.969 0 0 1 5.292 3m13.416 0a8.969 8.969 0 0 1 2.168 4.5\"\n  }));\n}\nconst ForwardRef = /*#__PURE__*/ React.forwardRef(BellAlertIcon);\nexport default ForwardRef;"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,aAAaA,CAAAC,IAAA,EAInBC,MAAM,EAAE;EAAA,IAJY;MACrBC,KAAK;MACLC;IAEF,CAAC,GAAAH,IAAA;IADII,KAAK,GAAAC,wBAAA,CAAAL,IAAA,EAAAM,SAAA;EAER,OAAO,aAAaR,KAAK,CAACS,aAAa,CAAC,KAAK,EAAEC,MAAM,CAACC,MAAM,CAAC;IAC3DC,KAAK,EAAE,4BAA4B;IACnCC,IAAI,EAAE,MAAM;IACZC,OAAO,EAAE,WAAW;IACpBC,WAAW,EAAE,GAAG;IAChBC,MAAM,EAAE,cAAc;IACtB,aAAa,EAAE,MAAM;IACrB,WAAW,EAAE,MAAM;IACnBC,GAAG,EAAEd,MAAM;IACX,iBAAiB,EAAEE;EACrB,CAAC,EAAEC,KAAK,CAAC,EAAEF,KAAK,GAAG,aAAaJ,KAAK,CAACS,aAAa,CAAC,OAAO,EAAE;IAC3DS,EAAE,EAAEb;EACN,CAAC,EAAED,KAAK,CAAC,GAAG,IAAI,EAAE,aAAaJ,KAAK,CAACS,aAAa,CAAC,MAAM,EAAE;IACzDU,aAAa,EAAE,OAAO;IACtBC,cAAc,EAAE,OAAO;IACvBC,CAAC,EAAE;EACL,CAAC,CAAC,CAAC;AACL;AACA,MAAMC,UAAU,GAAG,aAActB,KAAK,CAACuB,UAAU,CAACtB,aAAa,CAAC;AAChE,eAAeqB,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}