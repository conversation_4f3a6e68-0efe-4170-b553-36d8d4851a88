{"ast": null, "code": "import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name lastDayOfMonth\n * @category Month Helpers\n * @summary Return the last day of a month for the given date.\n *\n * @description\n * Return the last day of a month for the given date.\n * The result will be in the local timezone.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the last day of a month\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The last day of a month for 2 September 2014 11:55:00:\n * const result = lastDayOfMonth(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 00:00:00\n */\nexport default function lastDayOfMonth(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var month = date.getMonth();\n  date.setFullYear(date.getFullYear(), month + 1, 0);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}", "map": {"version": 3, "names": ["toDate", "requiredArgs", "lastDayOfMonth", "dirtyDate", "arguments", "date", "month", "getMonth", "setFullYear", "getFullYear", "setHours"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/date-fns/esm/lastDayOfMonth/index.js"], "sourcesContent": ["import toDate from \"../toDate/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name lastDayOfMonth\n * @category Month Helpers\n * @summary Return the last day of a month for the given date.\n *\n * @description\n * Return the last day of a month for the given date.\n * The result will be in the local timezone.\n *\n * @param {Date|Number} date - the original date\n * @returns {Date} the last day of a month\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // The last day of a month for 2 September 2014 11:55:00:\n * const result = lastDayOfMonth(new Date(2014, 8, 2, 11, 55, 0))\n * //=> Tue Sep 30 2014 00:00:00\n */\nexport default function lastDayOfMonth(dirtyDate) {\n  requiredArgs(1, arguments);\n  var date = toDate(dirtyDate);\n  var month = date.getMonth();\n  date.setFullYear(date.getFullYear(), month + 1, 0);\n  date.setHours(0, 0, 0, 0);\n  return date;\n}"], "mappings": "AAAA,OAAOA,MAAM,MAAM,oBAAoB;AACvC,OAAOC,YAAY,MAAM,+BAA+B;AACxD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,cAAcA,CAACC,SAAS,EAAE;EAChDF,YAAY,CAAC,CAAC,EAAEG,SAAS,CAAC;EAC1B,IAAIC,IAAI,GAAGL,MAAM,CAACG,SAAS,CAAC;EAC5B,IAAIG,KAAK,GAAGD,IAAI,CAACE,QAAQ,CAAC,CAAC;EAC3BF,IAAI,CAACG,WAAW,CAACH,IAAI,CAACI,WAAW,CAAC,CAAC,EAAEH,KAAK,GAAG,CAAC,EAAE,CAAC,CAAC;EAClDD,IAAI,CAACK,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EACzB,OAAOL,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}