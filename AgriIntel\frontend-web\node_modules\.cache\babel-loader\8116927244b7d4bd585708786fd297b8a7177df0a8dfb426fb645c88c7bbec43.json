{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DeleteManyOperation = exports.DeleteOneOperation = exports.DeleteOperation = void 0;\nexports.makeDeleteStatement = makeDeleteStatement;\nconst error_1 = require(\"../error\");\nconst command_1 = require(\"./command\");\nconst operation_1 = require(\"./operation\");\n/** @internal */\nclass DeleteOperation extends command_1.CommandOperation {\n  constructor(ns, statements, options) {\n    super(undefined, options);\n    this.options = options;\n    this.ns = ns;\n    this.statements = statements;\n  }\n  get commandName() {\n    return 'delete';\n  }\n  get canRetryWrite() {\n    if (super.canRetryWrite === false) {\n      return false;\n    }\n    return this.statements.every(op => op.limit != null ? op.limit > 0 : true);\n  }\n  async execute(server, session, timeoutContext) {\n    const options = this.options ?? {};\n    const ordered = typeof options.ordered === 'boolean' ? options.ordered : true;\n    const command = {\n      delete: this.ns.collection,\n      deletes: this.statements,\n      ordered\n    };\n    if (options.let) {\n      command.let = options.let;\n    }\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (options.comment !== undefined) {\n      command.comment = options.comment;\n    }\n    const unacknowledgedWrite = this.writeConcern && this.writeConcern.w === 0;\n    if (unacknowledgedWrite) {\n      if (this.statements.find(o => o.hint)) {\n        // TODO(NODE-3541): fix error for hint with unacknowledged writes\n        throw new error_1.MongoCompatibilityError(`hint is not supported with unacknowledged writes`);\n      }\n    }\n    const res = await super.executeCommand(server, session, command, timeoutContext);\n    return res;\n  }\n}\nexports.DeleteOperation = DeleteOperation;\nclass DeleteOneOperation extends DeleteOperation {\n  constructor(collection, filter, options) {\n    super(collection.s.namespace, [makeDeleteStatement(filter, {\n      ...options,\n      limit: 1\n    })], options);\n  }\n  async execute(server, session, timeoutContext) {\n    const res = await super.execute(server, session, timeoutContext);\n    if (this.explain) return res;\n    if (res.code) throw new error_1.MongoServerError(res);\n    if (res.writeErrors) throw new error_1.MongoServerError(res.writeErrors[0]);\n    return {\n      acknowledged: this.writeConcern?.w !== 0,\n      deletedCount: res.n\n    };\n  }\n}\nexports.DeleteOneOperation = DeleteOneOperation;\nclass DeleteManyOperation extends DeleteOperation {\n  constructor(collection, filter, options) {\n    super(collection.s.namespace, [makeDeleteStatement(filter, options)], options);\n  }\n  async execute(server, session, timeoutContext) {\n    const res = await super.execute(server, session, timeoutContext);\n    if (this.explain) return res;\n    if (res.code) throw new error_1.MongoServerError(res);\n    if (res.writeErrors) throw new error_1.MongoServerError(res.writeErrors[0]);\n    return {\n      acknowledged: this.writeConcern?.w !== 0,\n      deletedCount: res.n\n    };\n  }\n}\nexports.DeleteManyOperation = DeleteManyOperation;\nfunction makeDeleteStatement(filter, options) {\n  const op = {\n    q: filter,\n    limit: typeof options.limit === 'number' ? options.limit : 0\n  };\n  if (options.collation) {\n    op.collation = options.collation;\n  }\n  if (options.hint) {\n    op.hint = options.hint;\n  }\n  return op;\n}\n(0, operation_1.defineAspects)(DeleteOperation, [operation_1.Aspect.RETRYABLE, operation_1.Aspect.WRITE_OPERATION]);\n(0, operation_1.defineAspects)(DeleteOneOperation, [operation_1.Aspect.RETRYABLE, operation_1.Aspect.WRITE_OPERATION, operation_1.Aspect.EXPLAINABLE, operation_1.Aspect.SKIP_COLLATION]);\n(0, operation_1.defineAspects)(DeleteManyOperation, [operation_1.Aspect.WRITE_OPERATION, operation_1.Aspect.EXPLAINABLE, operation_1.Aspect.SKIP_COLLATION]);", "map": {"version": 3, "names": ["exports", "makeDeleteStatement", "error_1", "require", "command_1", "operation_1", "DeleteOperation", "CommandOperation", "constructor", "ns", "statements", "options", "undefined", "commandName", "canRetryWrite", "every", "op", "limit", "execute", "server", "session", "timeoutContext", "ordered", "command", "delete", "collection", "deletes", "let", "comment", "unacknowledgedWrite", "writeConcern", "w", "find", "o", "hint", "MongoCompatibilityError", "res", "executeCommand", "DeleteOneOperation", "filter", "s", "namespace", "explain", "code", "MongoServerError", "writeErrors", "acknowledged", "deletedCount", "n", "DeleteManyOperation", "q", "collation", "defineAspects", "Aspect", "RETRYABLE", "WRITE_OPERATION", "EXPLAINABLE", "SKIP_COLLATION"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\delete.ts"], "sourcesContent": ["import type { Document } from '../bson';\nimport type { Collection } from '../collection';\nimport { MongoCompatibilityError, MongoServerError } from '../error';\nimport { type TODO_NODE_3286 } from '../mongo_types';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { type MongoDBNamespace } from '../utils';\nimport { type WriteConcernOptions } from '../write_concern';\nimport { type CollationOptions, CommandOperation, type CommandOperationOptions } from './command';\nimport { Aspect, defineAspects, type Hint } from './operation';\n\n/** @public */\nexport interface DeleteOptions extends CommandOperationOptions, WriteConcernOptions {\n  /** If true, when an insert fails, don't execute the remaining writes. If false, continue with remaining inserts when one fails. */\n  ordered?: boolean;\n  /** Specifies the collation to use for the operation */\n  collation?: CollationOptions;\n  /** Specify that the update query should only consider plans using the hinted index */\n  hint?: string | Document;\n  /** Map of parameter names and values that can be accessed using $$var (requires MongoDB 5.0). */\n  let?: Document;\n}\n\n/** @public */\nexport interface DeleteResult {\n  /** Indicates whether this write result was acknowledged. If not, then all other members of this result will be undefined. */\n  acknowledged: boolean;\n  /** The number of documents that were deleted */\n  deletedCount: number;\n}\n\n/** @public */\nexport interface DeleteStatement {\n  /** The query that matches documents to delete. */\n  q: Document;\n  /** The number of matching documents to delete. */\n  limit: number;\n  /** Specifies the collation to use for the operation. */\n  collation?: CollationOptions;\n  /** A document or string that specifies the index to use to support the query predicate. */\n  hint?: Hint;\n}\n\n/** @internal */\nexport class DeleteOperation extends CommandOperation<DeleteResult> {\n  override options: DeleteOptions;\n  statements: DeleteStatement[];\n\n  constructor(ns: MongoDBNamespace, statements: DeleteStatement[], options: DeleteOptions) {\n    super(undefined, options);\n    this.options = options;\n    this.ns = ns;\n    this.statements = statements;\n  }\n\n  override get commandName() {\n    return 'delete' as const;\n  }\n\n  override get canRetryWrite(): boolean {\n    if (super.canRetryWrite === false) {\n      return false;\n    }\n\n    return this.statements.every(op => (op.limit != null ? op.limit > 0 : true));\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<DeleteResult> {\n    const options = this.options ?? {};\n    const ordered = typeof options.ordered === 'boolean' ? options.ordered : true;\n    const command: Document = {\n      delete: this.ns.collection,\n      deletes: this.statements,\n      ordered\n    };\n\n    if (options.let) {\n      command.let = options.let;\n    }\n\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (options.comment !== undefined) {\n      command.comment = options.comment;\n    }\n\n    const unacknowledgedWrite = this.writeConcern && this.writeConcern.w === 0;\n    if (unacknowledgedWrite) {\n      if (this.statements.find((o: Document) => o.hint)) {\n        // TODO(NODE-3541): fix error for hint with unacknowledged writes\n        throw new MongoCompatibilityError(`hint is not supported with unacknowledged writes`);\n      }\n    }\n\n    const res: TODO_NODE_3286 = await super.executeCommand(\n      server,\n      session,\n      command,\n      timeoutContext\n    );\n    return res;\n  }\n}\n\nexport class DeleteOneOperation extends DeleteOperation {\n  constructor(collection: Collection, filter: Document, options: DeleteOptions) {\n    super(collection.s.namespace, [makeDeleteStatement(filter, { ...options, limit: 1 })], options);\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<DeleteResult> {\n    const res: TODO_NODE_3286 = await super.execute(server, session, timeoutContext);\n    if (this.explain) return res;\n    if (res.code) throw new MongoServerError(res);\n    if (res.writeErrors) throw new MongoServerError(res.writeErrors[0]);\n\n    return {\n      acknowledged: this.writeConcern?.w !== 0,\n      deletedCount: res.n\n    };\n  }\n}\nexport class DeleteManyOperation extends DeleteOperation {\n  constructor(collection: Collection, filter: Document, options: DeleteOptions) {\n    super(collection.s.namespace, [makeDeleteStatement(filter, options)], options);\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<DeleteResult> {\n    const res: TODO_NODE_3286 = await super.execute(server, session, timeoutContext);\n    if (this.explain) return res;\n    if (res.code) throw new MongoServerError(res);\n    if (res.writeErrors) throw new MongoServerError(res.writeErrors[0]);\n\n    return {\n      acknowledged: this.writeConcern?.w !== 0,\n      deletedCount: res.n\n    };\n  }\n}\n\nexport function makeDeleteStatement(\n  filter: Document,\n  options: DeleteOptions & { limit?: number }\n): DeleteStatement {\n  const op: DeleteStatement = {\n    q: filter,\n    limit: typeof options.limit === 'number' ? options.limit : 0\n  };\n\n  if (options.collation) {\n    op.collation = options.collation;\n  }\n\n  if (options.hint) {\n    op.hint = options.hint;\n  }\n\n  return op;\n}\n\ndefineAspects(DeleteOperation, [Aspect.RETRYABLE, Aspect.WRITE_OPERATION]);\ndefineAspects(DeleteOneOperation, [\n  Aspect.RETRYABLE,\n  Aspect.WRITE_OPERATION,\n  Aspect.EXPLAINABLE,\n  Aspect.SKIP_COLLATION\n]);\ndefineAspects(DeleteManyOperation, [\n  Aspect.WRITE_OPERATION,\n  Aspect.EXPLAINABLE,\n  Aspect.SKIP_COLLATION\n]);\n"], "mappings": ";;;;;;AAwJAA,OAAA,CAAAC,mBAAA,GAAAA,mBAAA;AAtJA,MAAAC,OAAA,GAAAC,OAAA;AAOA,MAAAC,SAAA,GAAAD,OAAA;AACA,MAAAE,WAAA,GAAAF,OAAA;AAkCA;AACA,MAAaG,eAAgB,SAAQF,SAAA,CAAAG,gBAA8B;EAIjEC,YAAYC,EAAoB,EAAEC,UAA6B,EAAEC,OAAsB;IACrF,KAAK,CAACC,SAAS,EAAED,OAAO,CAAC;IACzB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACF,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;EAEA,IAAaG,WAAWA,CAAA;IACtB,OAAO,QAAiB;EAC1B;EAEA,IAAaC,aAAaA,CAAA;IACxB,IAAI,KAAK,CAACA,aAAa,KAAK,KAAK,EAAE;MACjC,OAAO,KAAK;IACd;IAEA,OAAO,IAAI,CAACJ,UAAU,CAACK,KAAK,CAACC,EAAE,IAAKA,EAAE,CAACC,KAAK,IAAI,IAAI,GAAGD,EAAE,CAACC,KAAK,GAAG,CAAC,GAAG,IAAK,CAAC;EAC9E;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMV,OAAO,GAAG,IAAI,CAACA,OAAO,IAAI,EAAE;IAClC,MAAMW,OAAO,GAAG,OAAOX,OAAO,CAACW,OAAO,KAAK,SAAS,GAAGX,OAAO,CAACW,OAAO,GAAG,IAAI;IAC7E,MAAMC,OAAO,GAAa;MACxBC,MAAM,EAAE,IAAI,CAACf,EAAE,CAACgB,UAAU;MAC1BC,OAAO,EAAE,IAAI,CAAChB,UAAU;MACxBY;KACD;IAED,IAAIX,OAAO,CAACgB,GAAG,EAAE;MACfJ,OAAO,CAACI,GAAG,GAAGhB,OAAO,CAACgB,GAAG;IAC3B;IAEA;IACA;IACA,IAAIhB,OAAO,CAACiB,OAAO,KAAKhB,SAAS,EAAE;MACjCW,OAAO,CAACK,OAAO,GAAGjB,OAAO,CAACiB,OAAO;IACnC;IAEA,MAAMC,mBAAmB,GAAG,IAAI,CAACC,YAAY,IAAI,IAAI,CAACA,YAAY,CAACC,CAAC,KAAK,CAAC;IAC1E,IAAIF,mBAAmB,EAAE;MACvB,IAAI,IAAI,CAACnB,UAAU,CAACsB,IAAI,CAAEC,CAAW,IAAKA,CAAC,CAACC,IAAI,CAAC,EAAE;QACjD;QACA,MAAM,IAAIhC,OAAA,CAAAiC,uBAAuB,CAAC,kDAAkD,CAAC;MACvF;IACF;IAEA,MAAMC,GAAG,GAAmB,MAAM,KAAK,CAACC,cAAc,CACpDlB,MAAM,EACNC,OAAO,EACPG,OAAO,EACPF,cAAc,CACf;IACD,OAAOe,GAAG;EACZ;;AA7DFpC,OAAA,CAAAM,eAAA,GAAAA,eAAA;AAgEA,MAAagC,kBAAmB,SAAQhC,eAAe;EACrDE,YAAYiB,UAAsB,EAAEc,MAAgB,EAAE5B,OAAsB;IAC1E,KAAK,CAACc,UAAU,CAACe,CAAC,CAACC,SAAS,EAAE,CAACxC,mBAAmB,CAACsC,MAAM,EAAE;MAAE,GAAG5B,OAAO;MAAEM,KAAK,EAAE;IAAC,CAAE,CAAC,CAAC,EAAEN,OAAO,CAAC;EACjG;EAES,MAAMO,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMe,GAAG,GAAmB,MAAM,KAAK,CAAClB,OAAO,CAACC,MAAM,EAAEC,OAAO,EAAEC,cAAc,CAAC;IAChF,IAAI,IAAI,CAACqB,OAAO,EAAE,OAAON,GAAG;IAC5B,IAAIA,GAAG,CAACO,IAAI,EAAE,MAAM,IAAIzC,OAAA,CAAA0C,gBAAgB,CAACR,GAAG,CAAC;IAC7C,IAAIA,GAAG,CAACS,WAAW,EAAE,MAAM,IAAI3C,OAAA,CAAA0C,gBAAgB,CAACR,GAAG,CAACS,WAAW,CAAC,CAAC,CAAC,CAAC;IAEnE,OAAO;MACLC,YAAY,EAAE,IAAI,CAAChB,YAAY,EAAEC,CAAC,KAAK,CAAC;MACxCgB,YAAY,EAAEX,GAAG,CAACY;KACnB;EACH;;AAnBFhD,OAAA,CAAAsC,kBAAA,GAAAA,kBAAA;AAqBA,MAAaW,mBAAoB,SAAQ3C,eAAe;EACtDE,YAAYiB,UAAsB,EAAEc,MAAgB,EAAE5B,OAAsB;IAC1E,KAAK,CAACc,UAAU,CAACe,CAAC,CAACC,SAAS,EAAE,CAACxC,mBAAmB,CAACsC,MAAM,EAAE5B,OAAO,CAAC,CAAC,EAAEA,OAAO,CAAC;EAChF;EAES,MAAMO,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMe,GAAG,GAAmB,MAAM,KAAK,CAAClB,OAAO,CAACC,MAAM,EAAEC,OAAO,EAAEC,cAAc,CAAC;IAChF,IAAI,IAAI,CAACqB,OAAO,EAAE,OAAON,GAAG;IAC5B,IAAIA,GAAG,CAACO,IAAI,EAAE,MAAM,IAAIzC,OAAA,CAAA0C,gBAAgB,CAACR,GAAG,CAAC;IAC7C,IAAIA,GAAG,CAACS,WAAW,EAAE,MAAM,IAAI3C,OAAA,CAAA0C,gBAAgB,CAACR,GAAG,CAACS,WAAW,CAAC,CAAC,CAAC,CAAC;IAEnE,OAAO;MACLC,YAAY,EAAE,IAAI,CAAChB,YAAY,EAAEC,CAAC,KAAK,CAAC;MACxCgB,YAAY,EAAEX,GAAG,CAACY;KACnB;EACH;;AAnBFhD,OAAA,CAAAiD,mBAAA,GAAAA,mBAAA;AAsBA,SAAgBhD,mBAAmBA,CACjCsC,MAAgB,EAChB5B,OAA2C;EAE3C,MAAMK,EAAE,GAAoB;IAC1BkC,CAAC,EAAEX,MAAM;IACTtB,KAAK,EAAE,OAAON,OAAO,CAACM,KAAK,KAAK,QAAQ,GAAGN,OAAO,CAACM,KAAK,GAAG;GAC5D;EAED,IAAIN,OAAO,CAACwC,SAAS,EAAE;IACrBnC,EAAE,CAACmC,SAAS,GAAGxC,OAAO,CAACwC,SAAS;EAClC;EAEA,IAAIxC,OAAO,CAACuB,IAAI,EAAE;IAChBlB,EAAE,CAACkB,IAAI,GAAGvB,OAAO,CAACuB,IAAI;EACxB;EAEA,OAAOlB,EAAE;AACX;AAEA,IAAAX,WAAA,CAAA+C,aAAa,EAAC9C,eAAe,EAAE,CAACD,WAAA,CAAAgD,MAAM,CAACC,SAAS,EAAEjD,WAAA,CAAAgD,MAAM,CAACE,eAAe,CAAC,CAAC;AAC1E,IAAAlD,WAAA,CAAA+C,aAAa,EAACd,kBAAkB,EAAE,CAChCjC,WAAA,CAAAgD,MAAM,CAACC,SAAS,EAChBjD,WAAA,CAAAgD,MAAM,CAACE,eAAe,EACtBlD,WAAA,CAAAgD,MAAM,CAACG,WAAW,EAClBnD,WAAA,CAAAgD,MAAM,CAACI,cAAc,CACtB,CAAC;AACF,IAAApD,WAAA,CAAA+C,aAAa,EAACH,mBAAmB,EAAE,CACjC5C,WAAA,CAAAgD,MAAM,CAACE,eAAe,EACtBlD,WAAA,CAAAgD,MAAM,CAACG,WAAW,EAClBnD,WAAA,CAAAgD,MAAM,CAACI,cAAc,CACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}