{"ast": null, "code": "import{createSlice}from'@reduxjs/toolkit';const initialState={records:[],alerts:[],selectedRecord:null,isLoading:false,error:null,statistics:null};const healthSlice=createSlice({name:'health',initialState,reducers:{setRecords:(state,action)=>{state.records=action.payload;},addRecord:(state,action)=>{state.records.unshift(action.payload);},updateRecord:(state,action)=>{const index=state.records.findIndex(record=>record._id===action.payload._id);if(index!==-1){state.records[index]=action.payload;}},deleteRecord:(state,action)=>{state.records=state.records.filter(record=>record._id!==action.payload);},setAlerts:(state,action)=>{state.alerts=action.payload;},addAlert:(state,action)=>{state.alerts.unshift(action.payload);},resolveAlert:(state,action)=>{const alert=state.alerts.find(a=>a._id===action.payload.id);if(alert){alert.isResolved=true;alert.resolvedDate=new Date().toISOString();alert.resolvedBy=action.payload.resolvedBy;alert.resolvedNotes=action.payload.notes;}},setSelectedRecord:(state,action)=>{state.selectedRecord=action.payload;},setLoading:(state,action)=>{state.isLoading=action.payload;},setError:(state,action)=>{state.error=action.payload;},clearError:state=>{state.error=null;},setStatistics:(state,action)=>{state.statistics=action.payload;}}});export const{setRecords,addRecord,updateRecord,deleteRecord,setAlerts,addAlert,resolveAlert,setSelectedRecord,setLoading,setError,clearError,setStatistics}=healthSlice.actions;export default healthSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "records", "alerts", "<PERSON><PERSON><PERSON><PERSON>", "isLoading", "error", "statistics", "healthSlice", "name", "reducers", "setRecords", "state", "action", "payload", "addRecord", "unshift", "updateRecord", "index", "findIndex", "record", "_id", "deleteRecord", "filter", "<PERSON><PERSON><PERSON><PERSON>", "add<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "alert", "find", "a", "id", "isResolved", "resolvedDate", "Date", "toISOString", "resolvedBy", "resolvedNotes", "notes", "setSelectedRecord", "setLoading", "setError", "clearError", "setStatistics", "actions", "reducer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/store/slices/healthSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\n\nexport interface HealthRecord {\n  _id: string;\n  animal: string;\n  recordType: 'vaccination' | 'treatment' | 'checkup' | 'illness' | 'injury' | 'surgery' | 'test';\n  date: string;\n  veterinarian?: string;\n  vaccine?: {\n    name: string;\n    manufacturer?: string;\n    batchNumber?: string;\n    expirationDate?: string;\n    dosage?: string;\n    route?: 'intramuscular' | 'subcutaneous' | 'oral' | 'nasal' | 'intravenous';\n  };\n  treatment?: {\n    condition: string;\n    medication: string;\n    dosage: string;\n    frequency: string;\n    duration: string;\n    withdrawalPeriod?: number;\n    cost?: number;\n  };\n  symptoms?: string[];\n  diagnosis?: string;\n  temperature?: number;\n  heartRate?: number;\n  respiratoryRate?: number;\n  bodyConditionScore?: number;\n  testResults?: Array<{\n    testName: string;\n    result: string;\n    normalRange?: string;\n    units?: string;\n    labName?: string;\n  }>;\n  followUpRequired: boolean;\n  followUpDate?: string;\n  followUpNotes?: string;\n  images?: Array<{\n    url: string;\n    caption?: string;\n  }>;\n  documents?: Array<{\n    name: string;\n    url: string;\n    type: string;\n  }>;\n  notes?: string;\n  cost?: number;\n  createdBy: string;\n  updatedBy?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface HealthAlert {\n  _id: string;\n  animal: string;\n  alertType: 'vaccination_due' | 'treatment_due' | 'checkup_due' | 'withdrawal_period' | 'health_concern' | 'medication_expiry';\n  priority: 'low' | 'medium' | 'high' | 'critical';\n  title: string;\n  description?: string;\n  dueDate?: string;\n  isResolved: boolean;\n  resolvedDate?: string;\n  resolvedBy?: string;\n  resolvedNotes?: string;\n  relatedHealthRecord?: string;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n  daysUntilDue?: number;\n}\n\nexport interface HealthState {\n  records: HealthRecord[];\n  alerts: HealthAlert[];\n  selectedRecord: HealthRecord | null;\n  isLoading: boolean;\n  error: string | null;\n  statistics: {\n    totalRecords: number;\n    recordsByType: Record<string, number>;\n    alertsByPriority: Record<string, number>;\n    upcomingVaccinations: number;\n    activeAlerts: number;\n  } | null;\n}\n\nconst initialState: HealthState = {\n  records: [],\n  alerts: [],\n  selectedRecord: null,\n  isLoading: false,\n  error: null,\n  statistics: null,\n};\n\nconst healthSlice = createSlice({\n  name: 'health',\n  initialState,\n  reducers: {\n    setRecords: (state, action: PayloadAction<HealthRecord[]>) => {\n      state.records = action.payload;\n    },\n    addRecord: (state, action: PayloadAction<HealthRecord>) => {\n      state.records.unshift(action.payload);\n    },\n    updateRecord: (state, action: PayloadAction<HealthRecord>) => {\n      const index = state.records.findIndex(record => record._id === action.payload._id);\n      if (index !== -1) {\n        state.records[index] = action.payload;\n      }\n    },\n    deleteRecord: (state, action: PayloadAction<string>) => {\n      state.records = state.records.filter(record => record._id !== action.payload);\n    },\n    setAlerts: (state, action: PayloadAction<HealthAlert[]>) => {\n      state.alerts = action.payload;\n    },\n    addAlert: (state, action: PayloadAction<HealthAlert>) => {\n      state.alerts.unshift(action.payload);\n    },\n    resolveAlert: (state, action: PayloadAction<{ id: string; resolvedBy: string; notes?: string }>) => {\n      const alert = state.alerts.find(a => a._id === action.payload.id);\n      if (alert) {\n        alert.isResolved = true;\n        alert.resolvedDate = new Date().toISOString();\n        alert.resolvedBy = action.payload.resolvedBy;\n        alert.resolvedNotes = action.payload.notes;\n      }\n    },\n    setSelectedRecord: (state, action: PayloadAction<HealthRecord | null>) => {\n      state.selectedRecord = action.payload;\n    },\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.isLoading = action.payload;\n    },\n    setError: (state, action: PayloadAction<string | null>) => {\n      state.error = action.payload;\n    },\n    clearError: (state) => {\n      state.error = null;\n    },\n    setStatistics: (state, action: PayloadAction<HealthState['statistics']>) => {\n      state.statistics = action.payload;\n    },\n  },\n});\n\nexport const {\n  setRecords,\n  addRecord,\n  updateRecord,\n  deleteRecord,\n  setAlerts,\n  addAlert,\n  resolveAlert,\n  setSelectedRecord,\n  setLoading,\n  setError,\n  clearError,\n  setStatistics,\n} = healthSlice.actions;\n\nexport default healthSlice.reducer;\n"], "mappings": "AAAA,OAASA,WAAW,KAAuB,kBAAkB,CA4F7D,KAAM,CAAAC,YAAyB,CAAG,CAChCC,OAAO,CAAE,EAAE,CACXC,MAAM,CAAE,EAAE,CACVC,cAAc,CAAE,IAAI,CACpBC,SAAS,CAAE,KAAK,CAChBC,KAAK,CAAE,IAAI,CACXC,UAAU,CAAE,IACd,CAAC,CAED,KAAM,CAAAC,WAAW,CAAGR,WAAW,CAAC,CAC9BS,IAAI,CAAE,QAAQ,CACdR,YAAY,CACZS,QAAQ,CAAE,CACRC,UAAU,CAAEA,CAACC,KAAK,CAAEC,MAAqC,GAAK,CAC5DD,KAAK,CAACV,OAAO,CAAGW,MAAM,CAACC,OAAO,CAChC,CAAC,CACDC,SAAS,CAAEA,CAACH,KAAK,CAAEC,MAAmC,GAAK,CACzDD,KAAK,CAACV,OAAO,CAACc,OAAO,CAACH,MAAM,CAACC,OAAO,CAAC,CACvC,CAAC,CACDG,YAAY,CAAEA,CAACL,KAAK,CAAEC,MAAmC,GAAK,CAC5D,KAAM,CAAAK,KAAK,CAAGN,KAAK,CAACV,OAAO,CAACiB,SAAS,CAACC,MAAM,EAAIA,MAAM,CAACC,GAAG,GAAKR,MAAM,CAACC,OAAO,CAACO,GAAG,CAAC,CAClF,GAAIH,KAAK,GAAK,CAAC,CAAC,CAAE,CAChBN,KAAK,CAACV,OAAO,CAACgB,KAAK,CAAC,CAAGL,MAAM,CAACC,OAAO,CACvC,CACF,CAAC,CACDQ,YAAY,CAAEA,CAACV,KAAK,CAAEC,MAA6B,GAAK,CACtDD,KAAK,CAACV,OAAO,CAAGU,KAAK,CAACV,OAAO,CAACqB,MAAM,CAACH,MAAM,EAAIA,MAAM,CAACC,GAAG,GAAKR,MAAM,CAACC,OAAO,CAAC,CAC/E,CAAC,CACDU,SAAS,CAAEA,CAACZ,KAAK,CAAEC,MAAoC,GAAK,CAC1DD,KAAK,CAACT,MAAM,CAAGU,MAAM,CAACC,OAAO,CAC/B,CAAC,CACDW,QAAQ,CAAEA,CAACb,KAAK,CAAEC,MAAkC,GAAK,CACvDD,KAAK,CAACT,MAAM,CAACa,OAAO,CAACH,MAAM,CAACC,OAAO,CAAC,CACtC,CAAC,CACDY,YAAY,CAAEA,CAACd,KAAK,CAAEC,MAAyE,GAAK,CAClG,KAAM,CAAAc,KAAK,CAAGf,KAAK,CAACT,MAAM,CAACyB,IAAI,CAACC,CAAC,EAAIA,CAAC,CAACR,GAAG,GAAKR,MAAM,CAACC,OAAO,CAACgB,EAAE,CAAC,CACjE,GAAIH,KAAK,CAAE,CACTA,KAAK,CAACI,UAAU,CAAG,IAAI,CACvBJ,KAAK,CAACK,YAAY,CAAG,GAAI,CAAAC,IAAI,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAC7CP,KAAK,CAACQ,UAAU,CAAGtB,MAAM,CAACC,OAAO,CAACqB,UAAU,CAC5CR,KAAK,CAACS,aAAa,CAAGvB,MAAM,CAACC,OAAO,CAACuB,KAAK,CAC5C,CACF,CAAC,CACDC,iBAAiB,CAAEA,CAAC1B,KAAK,CAAEC,MAA0C,GAAK,CACxED,KAAK,CAACR,cAAc,CAAGS,MAAM,CAACC,OAAO,CACvC,CAAC,CACDyB,UAAU,CAAEA,CAAC3B,KAAK,CAAEC,MAA8B,GAAK,CACrDD,KAAK,CAACP,SAAS,CAAGQ,MAAM,CAACC,OAAO,CAClC,CAAC,CACD0B,QAAQ,CAAEA,CAAC5B,KAAK,CAAEC,MAAoC,GAAK,CACzDD,KAAK,CAACN,KAAK,CAAGO,MAAM,CAACC,OAAO,CAC9B,CAAC,CACD2B,UAAU,CAAG7B,KAAK,EAAK,CACrBA,KAAK,CAACN,KAAK,CAAG,IAAI,CACpB,CAAC,CACDoC,aAAa,CAAEA,CAAC9B,KAAK,CAAEC,MAAgD,GAAK,CAC1ED,KAAK,CAACL,UAAU,CAAGM,MAAM,CAACC,OAAO,CACnC,CACF,CACF,CAAC,CAAC,CAEF,MAAO,MAAM,CACXH,UAAU,CACVI,SAAS,CACTE,YAAY,CACZK,YAAY,CACZE,SAAS,CACTC,QAAQ,CACRC,YAAY,CACZY,iBAAiB,CACjBC,UAAU,CACVC,QAAQ,CACRC,UAAU,CACVC,aACF,CAAC,CAAGlC,WAAW,CAACmC,OAAO,CAEvB,cAAe,CAAAnC,WAAW,CAACoC,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}