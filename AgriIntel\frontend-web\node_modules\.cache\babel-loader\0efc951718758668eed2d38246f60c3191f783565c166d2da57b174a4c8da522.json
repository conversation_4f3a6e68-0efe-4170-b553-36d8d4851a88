{"ast": null, "code": "import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport React from 'react';\nexport var defaultTheme = {\n  background: '#0b1521',\n  backgroundAlt: '#132337',\n  foreground: 'white',\n  gray: '#3f4e60',\n  grayAlt: '#222e3e',\n  inputBackgroundColor: '#fff',\n  inputTextColor: '#000',\n  success: '#00ab52',\n  danger: '#ff0085',\n  active: '#006bff',\n  warning: '#ffb200'\n};\nvar ThemeContext = /*#__PURE__*/React.createContext(defaultTheme);\nexport function ThemeProvider(_ref) {\n  var theme = _ref.theme,\n    rest = _objectWithoutPropertiesLoose(_ref, [\"theme\"]);\n  return /*#__PURE__*/React.createElement(ThemeContext.Provider, _extends({\n    value: theme\n  }, rest));\n}\nexport function useTheme() {\n  return React.useContext(ThemeContext);\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "React", "defaultTheme", "background", "backgroundAlt", "foreground", "gray", "grayAlt", "inputBackgroundColor", "inputTextColor", "success", "danger", "active", "warning", "ThemeContext", "createContext", "ThemeProvider", "_ref", "theme", "rest", "createElement", "Provider", "value", "useTheme", "useContext"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-query/es/devtools/theme.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport React from 'react';\nexport var defaultTheme = {\n  background: '#0b1521',\n  backgroundAlt: '#132337',\n  foreground: 'white',\n  gray: '#3f4e60',\n  grayAlt: '#222e3e',\n  inputBackgroundColor: '#fff',\n  inputTextColor: '#000',\n  success: '#00ab52',\n  danger: '#ff0085',\n  active: '#006bff',\n  warning: '#ffb200'\n};\nvar ThemeContext = /*#__PURE__*/React.createContext(defaultTheme);\nexport function ThemeProvider(_ref) {\n  var theme = _ref.theme,\n      rest = _objectWithoutPropertiesLoose(_ref, [\"theme\"]);\n\n  return /*#__PURE__*/React.createElement(ThemeContext.Provider, _extends({\n    value: theme\n  }, rest));\n}\nexport function useTheme() {\n  return React.useContext(ThemeContext);\n}"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,IAAIC,YAAY,GAAG;EACxBC,UAAU,EAAE,SAAS;EACrBC,aAAa,EAAE,SAAS;EACxBC,UAAU,EAAE,OAAO;EACnBC,IAAI,EAAE,SAAS;EACfC,OAAO,EAAE,SAAS;EAClBC,oBAAoB,EAAE,MAAM;EAC5BC,cAAc,EAAE,MAAM;EACtBC,OAAO,EAAE,SAAS;EAClBC,MAAM,EAAE,SAAS;EACjBC,MAAM,EAAE,SAAS;EACjBC,OAAO,EAAE;AACX,CAAC;AACD,IAAIC,YAAY,GAAG,aAAab,KAAK,CAACc,aAAa,CAACb,YAAY,CAAC;AACjE,OAAO,SAASc,aAAaA,CAACC,IAAI,EAAE;EAClC,IAAIC,KAAK,GAAGD,IAAI,CAACC,KAAK;IAClBC,IAAI,GAAGnB,6BAA6B,CAACiB,IAAI,EAAE,CAAC,OAAO,CAAC,CAAC;EAEzD,OAAO,aAAahB,KAAK,CAACmB,aAAa,CAACN,YAAY,CAACO,QAAQ,EAAEtB,QAAQ,CAAC;IACtEuB,KAAK,EAAEJ;EACT,CAAC,EAAEC,IAAI,CAAC,CAAC;AACX;AACA,OAAO,SAASI,QAAQA,CAAA,EAAG;EACzB,OAAOtB,KAAK,CAACuB,UAAU,CAACV,YAAY,CAAC;AACvC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}