{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{useState,useEffect,useCallback}from'react';import{animalMongoService}from'../services/animalMongoService';import{useMongoDb}from'./useMongoDb';/**\n * Hook to fetch and manage animal data from MongoDB\n */export const useMongoAnimalData=()=>{const[animals,setAnimals]=useState([]);const[stats,setStats]=useState(null);const[loading,setLoading]=useState(true);const[error,setError]=useState(null);const{isConnected}=useMongoDb();// Calculate stats from animals\nconst calculateStats=useCallback(animalList=>{const totalAnimals=animalList.length;const activeAnimals=animalList.filter(a=>a.status==='Active').length;const inactiveAnimals=animalList.filter(a=>a.status==='Inactive').length;const healthyAnimals=animalList.filter(a=>a.healthStatus==='healthy').length;const sickAnimals=animalList.filter(a=>a.healthStatus==='sick').length;const injuredAnimals=animalList.filter(a=>a.healthStatus==='injured').length;const pregnantAnimals=animalList.filter(a=>a.healthStatus==='pregnant').length;const speciesCount={};animalList.forEach(animal=>{const species=animal.species||'Unknown';speciesCount[species]=(speciesCount[species]||0)+1;});const breedCount={};animalList.forEach(animal=>{const breed=animal.breed||'Unknown';breedCount[breed]=(breedCount[breed]||0)+1;});return{totalAnimals,activeAnimals,inactiveAnimals,healthyAnimals,sickAnimals,injuredAnimals,pregnantAnimals,healthPercentage:totalAnimals>0?Math.round(healthyAnimals/totalAnimals*100):0,bySpecies:speciesCount,byBreed:breedCount,byStatus:{active:activeAnimals,inactive:inactiveAnimals},byHealth:{healthy:healthyAnimals,sick:sickAnimals,injured:injuredAnimals,pregnant:pregnantAnimals}};},[]);// Fetch all animals\nconst fetchAnimals=useCallback(async()=>{try{setLoading(true);// Always try to get data from MongoDB\nconsole.log('Fetching animals from MongoDB...');const data=await animalMongoService.getAllAnimals();console.log(\"Retrieved \".concat(data.length,\" animals from MongoDB\"));setAnimals(data);setStats(calculateStats(data));setError(null);}catch(err){console.error('Error in fetchAnimals:',err);setError(err);// Keep previous data if there was an error\nif(animals.length===0){setAnimals([]);setStats(calculateStats([]));}}finally{setLoading(false);}},[isConnected,calculateStats,animals]);// Create a new animal\nconst createAnimal=useCallback(async animal=>{// Always consider MongoDB as connected\n// if (!isConnected) {\n//   throw new Error('MongoDB is not connected');\n// }\ntry{const newAnimal=await animalMongoService.createAnimal(animal);setAnimals(prev=>{const newList=[...prev,newAnimal];setStats(calculateStats(newList));return newList;});return newAnimal;}catch(err){console.error('Error creating animal in MongoDB:',err);throw err;}},[isConnected,calculateStats]);// Update an animal\nconst updateAnimal=useCallback(async(id,animal)=>{// Always consider MongoDB as connected\n// if (!isConnected) {\n//   throw new Error('MongoDB is not connected');\n// }\ntry{const success=await animalMongoService.updateAnimal(id,animal);if(success){setAnimals(prev=>{const newList=prev.map(a=>a.id===id?_objectSpread(_objectSpread({},a),animal):a);setStats(calculateStats(newList));return newList;});}return success;}catch(err){console.error('Error updating animal in MongoDB:',err);throw err;}},[isConnected,calculateStats]);// Delete an animal\nconst deleteAnimal=useCallback(async id=>{// Always consider MongoDB as connected\n// if (!isConnected) {\n//   throw new Error('MongoDB is not connected');\n// }\ntry{const success=await animalMongoService.deleteAnimal(id);if(success){setAnimals(prev=>{const newList=prev.filter(a=>a.id!==id);setStats(calculateStats(newList));return newList;});}return success;}catch(err){console.error('Error deleting animal in MongoDB:',err);throw err;}},[isConnected,calculateStats]);// Fetch animals by species\nconst fetchAnimalsBySpecies=useCallback(async species=>{// Always consider MongoDB as connected\n// if (!isConnected) {\n//   throw new Error('MongoDB is not connected');\n// }\ntry{return await animalMongoService.findBySpecies(species);}catch(err){console.error('Error fetching animals by species from MongoDB:',err);throw err;}},[isConnected]);// Fetch animals by breed\nconst fetchAnimalsByBreed=useCallback(async breed=>{// Always consider MongoDB as connected\n// if (!isConnected) {\n//   throw new Error('MongoDB is not connected');\n// }\ntry{return await animalMongoService.findByBreed(breed);}catch(err){console.error('Error fetching animals by breed from MongoDB:',err);throw err;}},[isConnected]);// Fetch animals by status\nconst fetchAnimalsByStatus=useCallback(async status=>{// Always consider MongoDB as connected\n// if (!isConnected) {\n//   throw new Error('MongoDB is not connected');\n// }\ntry{return await animalMongoService.findByStatus(status);}catch(err){console.error('Error fetching animals by status from MongoDB:',err);throw err;}},[isConnected]);// Get animal by ID\nconst getAnimalById=useCallback(async id=>{try{console.log(\"Fetching animal with ID \".concat(id,\" from MongoDB...\"));const animal=await animalMongoService.getAnimalById(id);if(animal){console.log(\"Found animal with ID \".concat(id,\" in MongoDB\"));return animal;}else{console.log(\"Animal with ID \".concat(id,\" not found in MongoDB\"));return null;}}catch(err){console.error(\"Error in getAnimalById for ID \".concat(id,\":\"),err);return null;}},[]);// Load animals on mount and when connection status changes\nuseEffect(()=>{// Always fetch animals, regardless of connection status\nfetchAnimals();},[fetchAnimals]);return{animals,stats,loading,error,fetchAnimals,createAnimal,updateAnimal,deleteAnimal,fetchAnimalsBySpecies,fetchAnimalsByBreed,fetchAnimalsByStatus,getAnimalById};};", "map": {"version": 3, "names": ["useState", "useEffect", "useCallback", "animalMongoService", "useMongoDb", "useMongoAnimalData", "animals", "setAnimals", "stats", "setStats", "loading", "setLoading", "error", "setError", "isConnected", "calculateStats", "animalList", "totalAnimals", "length", "activeAnimals", "filter", "a", "status", "inactiveAnimals", "healthyAnimals", "healthStatus", "sickAnimals", "injuredAnimals", "pregnantAnimals", "speciesCount", "for<PERSON>ach", "animal", "species", "breedCount", "breed", "healthPercentage", "Math", "round", "bySpecies", "byBreed", "byStatus", "active", "inactive", "byHealth", "healthy", "sick", "injured", "pregnant", "fetchAnimals", "console", "log", "data", "getAllAnimals", "concat", "err", "createAnimal", "newAnimal", "prev", "newList", "updateAnimal", "id", "success", "map", "_objectSpread", "deleteAnimal", "fetchAnimalsBySpecies", "findBySpecies", "fetchAnimalsByBreed", "findByBreed", "fetchAnimalsByStatus", "findByStatus", "getAnimalById"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/hooks/useMongoAnimalData.ts"], "sourcesContent": ["import { useState, useEffect, useCallback } from 'react';\nimport { Animal, AnimalStats } from '../types/animal';\nimport { animalMongoService } from '../services/animalMongoService';\nimport { useMongoDb } from './useMongoDb';\n\n/**\n * Hook to fetch and manage animal data from MongoDB\n */\nexport const useMongoAnimalData = () => {\n  const [animals, setAnimals] = useState<Animal[]>([]);\n  const [stats, setStats] = useState<AnimalStats | null>(null);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<Error | null>(null);\n  const { isConnected } = useMongoDb();\n\n  // Calculate stats from animals\n  const calculateStats = useCallback((animalList: Animal[]) => {\n    const totalAnimals = animalList.length;\n    const activeAnimals = animalList.filter(a => a.status === 'Active').length;\n    const inactiveAnimals = animalList.filter(a => a.status === 'Inactive').length;\n    const healthyAnimals = animalList.filter(a => a.healthStatus === 'healthy').length;\n    const sickAnimals = animalList.filter(a => a.healthStatus === 'sick').length;\n    const injuredAnimals = animalList.filter(a => a.healthStatus === 'injured').length;\n    const pregnantAnimals = animalList.filter(a => a.healthStatus === 'pregnant').length;\n\n    const speciesCount: Record<string, number> = {};\n    animalList.forEach(animal => {\n      const species = animal.species || 'Unknown';\n      speciesCount[species] = (speciesCount[species] || 0) + 1;\n    });\n\n    const breedCount: Record<string, number> = {};\n    animalList.forEach(animal => {\n      const breed = animal.breed || 'Unknown';\n      breedCount[breed] = (breedCount[breed] || 0) + 1;\n    });\n\n    return {\n      totalAnimals,\n      activeAnimals,\n      inactiveAnimals,\n      healthyAnimals,\n      sickAnimals,\n      injuredAnimals,\n      pregnantAnimals,\n      healthPercentage: totalAnimals > 0 ? Math.round((healthyAnimals / totalAnimals) * 100) : 0,\n      bySpecies: speciesCount,\n      byBreed: breedCount,\n      byStatus: {\n        active: activeAnimals,\n        inactive: inactiveAnimals\n      },\n      byHealth: {\n        healthy: healthyAnimals,\n        sick: sickAnimals,\n        injured: injuredAnimals,\n        pregnant: pregnantAnimals\n      }\n    } as AnimalStats;\n  }, []);\n\n  // Fetch all animals\n  const fetchAnimals = useCallback(async () => {\n    try {\n      setLoading(true);\n\n      // Always try to get data from MongoDB\n      console.log('Fetching animals from MongoDB...');\n      const data = await animalMongoService.getAllAnimals();\n\n      console.log(`Retrieved ${data.length} animals from MongoDB`);\n      setAnimals(data);\n      setStats(calculateStats(data));\n      setError(null);\n    } catch (err) {\n      console.error('Error in fetchAnimals:', err);\n      setError(err as Error);\n\n      // Keep previous data if there was an error\n      if (animals.length === 0) {\n        setAnimals([]);\n        setStats(calculateStats([]));\n      }\n    } finally {\n      setLoading(false);\n    }\n  }, [isConnected, calculateStats, animals]);\n\n  // Create a new animal\n  const createAnimal = useCallback(async (animal: Animal) => {\n    // Always consider MongoDB as connected\n    // if (!isConnected) {\n    //   throw new Error('MongoDB is not connected');\n    // }\n\n    try {\n      const newAnimal = await animalMongoService.createAnimal(animal);\n      setAnimals(prev => {\n        const newList = [...prev, newAnimal];\n        setStats(calculateStats(newList));\n        return newList;\n      });\n      return newAnimal;\n    } catch (err) {\n      console.error('Error creating animal in MongoDB:', err);\n      throw err;\n    }\n  }, [isConnected, calculateStats]);\n\n  // Update an animal\n  const updateAnimal = useCallback(async (id: string, animal: Partial<Animal>) => {\n    // Always consider MongoDB as connected\n    // if (!isConnected) {\n    //   throw new Error('MongoDB is not connected');\n    // }\n\n    try {\n      const success = await animalMongoService.updateAnimal(id, animal);\n      if (success) {\n        setAnimals(prev => {\n          const newList = prev.map(a => a.id === id ? { ...a, ...animal } : a);\n          setStats(calculateStats(newList));\n          return newList;\n        });\n      }\n      return success;\n    } catch (err) {\n      console.error('Error updating animal in MongoDB:', err);\n      throw err;\n    }\n  }, [isConnected, calculateStats]);\n\n  // Delete an animal\n  const deleteAnimal = useCallback(async (id: string) => {\n    // Always consider MongoDB as connected\n    // if (!isConnected) {\n    //   throw new Error('MongoDB is not connected');\n    // }\n\n    try {\n      const success = await animalMongoService.deleteAnimal(id);\n      if (success) {\n        setAnimals(prev => {\n          const newList = prev.filter(a => a.id !== id);\n          setStats(calculateStats(newList));\n          return newList;\n        });\n      }\n      return success;\n    } catch (err) {\n      console.error('Error deleting animal in MongoDB:', err);\n      throw err;\n    }\n  }, [isConnected, calculateStats]);\n\n  // Fetch animals by species\n  const fetchAnimalsBySpecies = useCallback(async (species: string) => {\n    // Always consider MongoDB as connected\n    // if (!isConnected) {\n    //   throw new Error('MongoDB is not connected');\n    // }\n\n    try {\n      return await animalMongoService.findBySpecies(species);\n    } catch (err) {\n      console.error('Error fetching animals by species from MongoDB:', err);\n      throw err;\n    }\n  }, [isConnected]);\n\n  // Fetch animals by breed\n  const fetchAnimalsByBreed = useCallback(async (breed: string) => {\n    // Always consider MongoDB as connected\n    // if (!isConnected) {\n    //   throw new Error('MongoDB is not connected');\n    // }\n\n    try {\n      return await animalMongoService.findByBreed(breed);\n    } catch (err) {\n      console.error('Error fetching animals by breed from MongoDB:', err);\n      throw err;\n    }\n  }, [isConnected]);\n\n  // Fetch animals by status\n  const fetchAnimalsByStatus = useCallback(async (status: string) => {\n    // Always consider MongoDB as connected\n    // if (!isConnected) {\n    //   throw new Error('MongoDB is not connected');\n    // }\n\n    try {\n      return await animalMongoService.findByStatus(status);\n    } catch (err) {\n      console.error('Error fetching animals by status from MongoDB:', err);\n      throw err;\n    }\n  }, [isConnected]);\n\n  // Get animal by ID\n  const getAnimalById = useCallback(async (id: string) => {\n    try {\n      console.log(`Fetching animal with ID ${id} from MongoDB...`);\n      const animal = await animalMongoService.getAnimalById(id);\n\n      if (animal) {\n        console.log(`Found animal with ID ${id} in MongoDB`);\n        return animal;\n      } else {\n        console.log(`Animal with ID ${id} not found in MongoDB`);\n        return null;\n      }\n    } catch (err) {\n      console.error(`Error in getAnimalById for ID ${id}:`, err);\n      return null;\n    }\n  }, []);\n\n  // Load animals on mount and when connection status changes\n  useEffect(() => {\n    // Always fetch animals, regardless of connection status\n    fetchAnimals();\n  }, [fetchAnimals]);\n\n  return {\n    animals,\n    stats,\n    loading,\n    error,\n    fetchAnimals,\n    createAnimal,\n    updateAnimal,\n    deleteAnimal,\n    fetchAnimalsBySpecies,\n    fetchAnimalsByBreed,\n    fetchAnimalsByStatus,\n    getAnimalById\n  };\n};\n"], "mappings": "gJAAA,OAASA,QAAQ,CAAEC,SAAS,CAAEC,WAAW,KAAQ,OAAO,CAExD,OAASC,kBAAkB,KAAQ,gCAAgC,CACnE,OAASC,UAAU,KAAQ,cAAc,CAEzC;AACA;AACA,GACA,MAAO,MAAM,CAAAC,kBAAkB,CAAGA,CAAA,GAAM,CACtC,KAAM,CAACC,OAAO,CAAEC,UAAU,CAAC,CAAGP,QAAQ,CAAW,EAAE,CAAC,CACpD,KAAM,CAACQ,KAAK,CAAEC,QAAQ,CAAC,CAAGT,QAAQ,CAAqB,IAAI,CAAC,CAC5D,KAAM,CAACU,OAAO,CAAEC,UAAU,CAAC,CAAGX,QAAQ,CAAU,IAAI,CAAC,CACrD,KAAM,CAACY,KAAK,CAAEC,QAAQ,CAAC,CAAGb,QAAQ,CAAe,IAAI,CAAC,CACtD,KAAM,CAAEc,WAAY,CAAC,CAAGV,UAAU,CAAC,CAAC,CAEpC;AACA,KAAM,CAAAW,cAAc,CAAGb,WAAW,CAAEc,UAAoB,EAAK,CAC3D,KAAM,CAAAC,YAAY,CAAGD,UAAU,CAACE,MAAM,CACtC,KAAM,CAAAC,aAAa,CAAGH,UAAU,CAACI,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACC,MAAM,GAAK,QAAQ,CAAC,CAACJ,MAAM,CAC1E,KAAM,CAAAK,eAAe,CAAGP,UAAU,CAACI,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACC,MAAM,GAAK,UAAU,CAAC,CAACJ,MAAM,CAC9E,KAAM,CAAAM,cAAc,CAAGR,UAAU,CAACI,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACI,YAAY,GAAK,SAAS,CAAC,CAACP,MAAM,CAClF,KAAM,CAAAQ,WAAW,CAAGV,UAAU,CAACI,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACI,YAAY,GAAK,MAAM,CAAC,CAACP,MAAM,CAC5E,KAAM,CAAAS,cAAc,CAAGX,UAAU,CAACI,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACI,YAAY,GAAK,SAAS,CAAC,CAACP,MAAM,CAClF,KAAM,CAAAU,eAAe,CAAGZ,UAAU,CAACI,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACI,YAAY,GAAK,UAAU,CAAC,CAACP,MAAM,CAEpF,KAAM,CAAAW,YAAoC,CAAG,CAAC,CAAC,CAC/Cb,UAAU,CAACc,OAAO,CAACC,MAAM,EAAI,CAC3B,KAAM,CAAAC,OAAO,CAAGD,MAAM,CAACC,OAAO,EAAI,SAAS,CAC3CH,YAAY,CAACG,OAAO,CAAC,CAAG,CAACH,YAAY,CAACG,OAAO,CAAC,EAAI,CAAC,EAAI,CAAC,CAC1D,CAAC,CAAC,CAEF,KAAM,CAAAC,UAAkC,CAAG,CAAC,CAAC,CAC7CjB,UAAU,CAACc,OAAO,CAACC,MAAM,EAAI,CAC3B,KAAM,CAAAG,KAAK,CAAGH,MAAM,CAACG,KAAK,EAAI,SAAS,CACvCD,UAAU,CAACC,KAAK,CAAC,CAAG,CAACD,UAAU,CAACC,KAAK,CAAC,EAAI,CAAC,EAAI,CAAC,CAClD,CAAC,CAAC,CAEF,MAAO,CACLjB,YAAY,CACZE,aAAa,CACbI,eAAe,CACfC,cAAc,CACdE,WAAW,CACXC,cAAc,CACdC,eAAe,CACfO,gBAAgB,CAAElB,YAAY,CAAG,CAAC,CAAGmB,IAAI,CAACC,KAAK,CAAEb,cAAc,CAAGP,YAAY,CAAI,GAAG,CAAC,CAAG,CAAC,CAC1FqB,SAAS,CAAET,YAAY,CACvBU,OAAO,CAAEN,UAAU,CACnBO,QAAQ,CAAE,CACRC,MAAM,CAAEtB,aAAa,CACrBuB,QAAQ,CAAEnB,eACZ,CAAC,CACDoB,QAAQ,CAAE,CACRC,OAAO,CAAEpB,cAAc,CACvBqB,IAAI,CAAEnB,WAAW,CACjBoB,OAAO,CAAEnB,cAAc,CACvBoB,QAAQ,CAAEnB,eACZ,CACF,CAAC,CACH,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAoB,YAAY,CAAG9C,WAAW,CAAC,SAAY,CAC3C,GAAI,CACFS,UAAU,CAAC,IAAI,CAAC,CAEhB;AACAsC,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC,CAC/C,KAAM,CAAAC,IAAI,CAAG,KAAM,CAAAhD,kBAAkB,CAACiD,aAAa,CAAC,CAAC,CAErDH,OAAO,CAACC,GAAG,cAAAG,MAAA,CAAcF,IAAI,CAACjC,MAAM,yBAAuB,CAAC,CAC5DX,UAAU,CAAC4C,IAAI,CAAC,CAChB1C,QAAQ,CAACM,cAAc,CAACoC,IAAI,CAAC,CAAC,CAC9BtC,QAAQ,CAAC,IAAI,CAAC,CAChB,CAAE,MAAOyC,GAAG,CAAE,CACZL,OAAO,CAACrC,KAAK,CAAC,wBAAwB,CAAE0C,GAAG,CAAC,CAC5CzC,QAAQ,CAACyC,GAAY,CAAC,CAEtB;AACA,GAAIhD,OAAO,CAACY,MAAM,GAAK,CAAC,CAAE,CACxBX,UAAU,CAAC,EAAE,CAAC,CACdE,QAAQ,CAACM,cAAc,CAAC,EAAE,CAAC,CAAC,CAC9B,CACF,CAAC,OAAS,CACRJ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAAE,CAACG,WAAW,CAAEC,cAAc,CAAET,OAAO,CAAC,CAAC,CAE1C;AACA,KAAM,CAAAiD,YAAY,CAAGrD,WAAW,CAAC,KAAO,CAAA6B,MAAc,EAAK,CACzD;AACA;AACA;AACA;AAEA,GAAI,CACF,KAAM,CAAAyB,SAAS,CAAG,KAAM,CAAArD,kBAAkB,CAACoD,YAAY,CAACxB,MAAM,CAAC,CAC/DxB,UAAU,CAACkD,IAAI,EAAI,CACjB,KAAM,CAAAC,OAAO,CAAG,CAAC,GAAGD,IAAI,CAAED,SAAS,CAAC,CACpC/C,QAAQ,CAACM,cAAc,CAAC2C,OAAO,CAAC,CAAC,CACjC,MAAO,CAAAA,OAAO,CAChB,CAAC,CAAC,CACF,MAAO,CAAAF,SAAS,CAClB,CAAE,MAAOF,GAAG,CAAE,CACZL,OAAO,CAACrC,KAAK,CAAC,mCAAmC,CAAE0C,GAAG,CAAC,CACvD,KAAM,CAAAA,GAAG,CACX,CACF,CAAC,CAAE,CAACxC,WAAW,CAAEC,cAAc,CAAC,CAAC,CAEjC;AACA,KAAM,CAAA4C,YAAY,CAAGzD,WAAW,CAAC,MAAO0D,EAAU,CAAE7B,MAAuB,GAAK,CAC9E;AACA;AACA;AACA;AAEA,GAAI,CACF,KAAM,CAAA8B,OAAO,CAAG,KAAM,CAAA1D,kBAAkB,CAACwD,YAAY,CAACC,EAAE,CAAE7B,MAAM,CAAC,CACjE,GAAI8B,OAAO,CAAE,CACXtD,UAAU,CAACkD,IAAI,EAAI,CACjB,KAAM,CAAAC,OAAO,CAAGD,IAAI,CAACK,GAAG,CAACzC,CAAC,EAAIA,CAAC,CAACuC,EAAE,GAAKA,EAAE,CAAAG,aAAA,CAAAA,aAAA,IAAQ1C,CAAC,EAAKU,MAAM,EAAKV,CAAC,CAAC,CACpEZ,QAAQ,CAACM,cAAc,CAAC2C,OAAO,CAAC,CAAC,CACjC,MAAO,CAAAA,OAAO,CAChB,CAAC,CAAC,CACJ,CACA,MAAO,CAAAG,OAAO,CAChB,CAAE,MAAOP,GAAG,CAAE,CACZL,OAAO,CAACrC,KAAK,CAAC,mCAAmC,CAAE0C,GAAG,CAAC,CACvD,KAAM,CAAAA,GAAG,CACX,CACF,CAAC,CAAE,CAACxC,WAAW,CAAEC,cAAc,CAAC,CAAC,CAEjC;AACA,KAAM,CAAAiD,YAAY,CAAG9D,WAAW,CAAC,KAAO,CAAA0D,EAAU,EAAK,CACrD;AACA;AACA;AACA;AAEA,GAAI,CACF,KAAM,CAAAC,OAAO,CAAG,KAAM,CAAA1D,kBAAkB,CAAC6D,YAAY,CAACJ,EAAE,CAAC,CACzD,GAAIC,OAAO,CAAE,CACXtD,UAAU,CAACkD,IAAI,EAAI,CACjB,KAAM,CAAAC,OAAO,CAAGD,IAAI,CAACrC,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACuC,EAAE,GAAKA,EAAE,CAAC,CAC7CnD,QAAQ,CAACM,cAAc,CAAC2C,OAAO,CAAC,CAAC,CACjC,MAAO,CAAAA,OAAO,CAChB,CAAC,CAAC,CACJ,CACA,MAAO,CAAAG,OAAO,CAChB,CAAE,MAAOP,GAAG,CAAE,CACZL,OAAO,CAACrC,KAAK,CAAC,mCAAmC,CAAE0C,GAAG,CAAC,CACvD,KAAM,CAAAA,GAAG,CACX,CACF,CAAC,CAAE,CAACxC,WAAW,CAAEC,cAAc,CAAC,CAAC,CAEjC;AACA,KAAM,CAAAkD,qBAAqB,CAAG/D,WAAW,CAAC,KAAO,CAAA8B,OAAe,EAAK,CACnE;AACA;AACA;AACA;AAEA,GAAI,CACF,MAAO,MAAM,CAAA7B,kBAAkB,CAAC+D,aAAa,CAAClC,OAAO,CAAC,CACxD,CAAE,MAAOsB,GAAG,CAAE,CACZL,OAAO,CAACrC,KAAK,CAAC,iDAAiD,CAAE0C,GAAG,CAAC,CACrE,KAAM,CAAAA,GAAG,CACX,CACF,CAAC,CAAE,CAACxC,WAAW,CAAC,CAAC,CAEjB;AACA,KAAM,CAAAqD,mBAAmB,CAAGjE,WAAW,CAAC,KAAO,CAAAgC,KAAa,EAAK,CAC/D;AACA;AACA;AACA;AAEA,GAAI,CACF,MAAO,MAAM,CAAA/B,kBAAkB,CAACiE,WAAW,CAAClC,KAAK,CAAC,CACpD,CAAE,MAAOoB,GAAG,CAAE,CACZL,OAAO,CAACrC,KAAK,CAAC,+CAA+C,CAAE0C,GAAG,CAAC,CACnE,KAAM,CAAAA,GAAG,CACX,CACF,CAAC,CAAE,CAACxC,WAAW,CAAC,CAAC,CAEjB;AACA,KAAM,CAAAuD,oBAAoB,CAAGnE,WAAW,CAAC,KAAO,CAAAoB,MAAc,EAAK,CACjE;AACA;AACA;AACA;AAEA,GAAI,CACF,MAAO,MAAM,CAAAnB,kBAAkB,CAACmE,YAAY,CAAChD,MAAM,CAAC,CACtD,CAAE,MAAOgC,GAAG,CAAE,CACZL,OAAO,CAACrC,KAAK,CAAC,gDAAgD,CAAE0C,GAAG,CAAC,CACpE,KAAM,CAAAA,GAAG,CACX,CACF,CAAC,CAAE,CAACxC,WAAW,CAAC,CAAC,CAEjB;AACA,KAAM,CAAAyD,aAAa,CAAGrE,WAAW,CAAC,KAAO,CAAA0D,EAAU,EAAK,CACtD,GAAI,CACFX,OAAO,CAACC,GAAG,4BAAAG,MAAA,CAA4BO,EAAE,oBAAkB,CAAC,CAC5D,KAAM,CAAA7B,MAAM,CAAG,KAAM,CAAA5B,kBAAkB,CAACoE,aAAa,CAACX,EAAE,CAAC,CAEzD,GAAI7B,MAAM,CAAE,CACVkB,OAAO,CAACC,GAAG,yBAAAG,MAAA,CAAyBO,EAAE,eAAa,CAAC,CACpD,MAAO,CAAA7B,MAAM,CACf,CAAC,IAAM,CACLkB,OAAO,CAACC,GAAG,mBAAAG,MAAA,CAAmBO,EAAE,yBAAuB,CAAC,CACxD,MAAO,KAAI,CACb,CACF,CAAE,MAAON,GAAG,CAAE,CACZL,OAAO,CAACrC,KAAK,kCAAAyC,MAAA,CAAkCO,EAAE,MAAKN,GAAG,CAAC,CAC1D,MAAO,KAAI,CACb,CACF,CAAC,CAAE,EAAE,CAAC,CAEN;AACArD,SAAS,CAAC,IAAM,CACd;AACA+C,YAAY,CAAC,CAAC,CAChB,CAAC,CAAE,CAACA,YAAY,CAAC,CAAC,CAElB,MAAO,CACL1C,OAAO,CACPE,KAAK,CACLE,OAAO,CACPE,KAAK,CACLoC,YAAY,CACZO,YAAY,CACZI,YAAY,CACZK,YAAY,CACZC,qBAAqB,CACrBE,mBAAmB,CACnBE,oBAAoB,CACpBE,aACF,CAAC,CACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}