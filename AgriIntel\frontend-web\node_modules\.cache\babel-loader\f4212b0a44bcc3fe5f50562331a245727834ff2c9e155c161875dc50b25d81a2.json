{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AgriIntel\\\\frontend-web\\\\src\\\\App.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\n// Simple components for now\nconst LoginPage = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n  children: /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"max-w-md w-full space-y-8\",\n    children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n      className: \"text-3xl font-bold text-center\",\n      children: \"AMPD Livestock Management\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 10,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n      className: \"text-center\",\n      children: \"Login page coming soon...\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 9,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 8,\n  columnNumber: 3\n}, this);\n_c = LoginPage;\nconst DashboardPage = () => /*#__PURE__*/_jsxDEV(\"div\", {\n  className: \"min-h-screen bg-gray-50 p-8\",\n  children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n    className: \"text-3xl font-bold mb-6\",\n    children: \"Dashboard\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 18,\n    columnNumber: 5\n  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n    children: \"Dashboard content coming soon...\"\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 19,\n    columnNumber: 5\n  }, this)]\n}, void 0, true, {\n  fileName: _jsxFileName,\n  lineNumber: 17,\n  columnNumber: 3\n}, this);\n_c2 = DashboardPage;\nfunction App() {\n  _s();\n  const {\n    isAuthenticated\n  } = useSelector(state => state.auth);\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"App\",\n    children: /*#__PURE__*/_jsxDEV(Routes, {\n      children: [/*#__PURE__*/_jsxDEV(Route, {\n        path: \"/login\",\n        element: !isAuthenticated ? /*#__PURE__*/_jsxDEV(LoginPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 39\n        }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 55\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"/dashboard\",\n        element: isAuthenticated ? /*#__PURE__*/_jsxDEV(DashboardPage, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 38\n        }, this) : /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/login\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 58\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        index: true,\n        element: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/dashboard\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 37,\n          columnNumber: 31\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Route, {\n        path: \"*\",\n        element: /*#__PURE__*/_jsxDEV(Navigate, {\n          to: \"/login\",\n          replace: true\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 34\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 27,\n    columnNumber: 5\n  }, this);\n}\n_s(App, \"TPD1adLOrTdsrr/HMWf3CuTzU0I=\", false, function () {\n  return [useSelector];\n});\n_c3 = App;\nexport default App;\nvar _c, _c2, _c3;\n$RefreshReg$(_c, \"LoginPage\");\n$RefreshReg$(_c2, \"DashboardPage\");\n$RefreshReg$(_c3, \"App\");", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "useSelector", "jsxDEV", "_jsxDEV", "LoginPage", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "DashboardPage", "_c2", "App", "_s", "isAuthenticated", "state", "auth", "path", "element", "to", "replace", "index", "_c3", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { RootState } from './store/store';\n\n// Simple components for now\nconst LoginPage = () => (\n  <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n    <div className=\"max-w-md w-full space-y-8\">\n      <h2 className=\"text-3xl font-bold text-center\">AMPD Livestock Management</h2>\n      <p className=\"text-center\">Login page coming soon...</p>\n    </div>\n  </div>\n);\n\nconst DashboardPage = () => (\n  <div className=\"min-h-screen bg-gray-50 p-8\">\n    <h1 className=\"text-3xl font-bold mb-6\">Dashboard</h1>\n    <p>Dashboard content coming soon...</p>\n  </div>\n);\n\nfunction App() {\n  const { isAuthenticated } = useSelector((state: RootState) => state.auth);\n\n  return (\n    <div className=\"App\">\n      <Routes>\n        <Route\n          path=\"/login\"\n          element={!isAuthenticated ? <LoginPage /> : <Navigate to=\"/dashboard\" replace />}\n        />\n        <Route\n          path=\"/dashboard\"\n          element={isAuthenticated ? <DashboardPage /> : <Navigate to=\"/login\" replace />}\n        />\n        <Route index element={<Navigate to=\"/dashboard\" replace />} />\n        <Route path=\"*\" element={<Navigate to=\"/login\" replace />} />\n      </Routes>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,kBAAkB;AAC1D,SAASC,WAAW,QAAQ,aAAa;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAG1C;AACA,MAAMC,SAAS,GAAGA,CAAA,kBAChBD,OAAA;EAAKE,SAAS,EAAC,0DAA0D;EAAAC,QAAA,eACvEH,OAAA;IAAKE,SAAS,EAAC,2BAA2B;IAAAC,QAAA,gBACxCH,OAAA;MAAIE,SAAS,EAAC,gCAAgC;MAAAC,QAAA,EAAC;IAAyB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAI,CAAC,eAC7EP,OAAA;MAAGE,SAAS,EAAC,aAAa;MAAAC,QAAA,EAAC;IAAyB;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAG,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACrD;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACH,CACN;AAACC,EAAA,GAPIP,SAAS;AASf,MAAMQ,aAAa,GAAGA,CAAA,kBACpBT,OAAA;EAAKE,SAAS,EAAC,6BAA6B;EAAAC,QAAA,gBAC1CH,OAAA;IAAIE,SAAS,EAAC,yBAAyB;IAAAC,QAAA,EAAC;EAAS;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAI,CAAC,eACtDP,OAAA;IAAAG,QAAA,EAAG;EAAgC;IAAAC,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAG,CAAC;AAAA;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACpC,CACN;AAACG,GAAA,GALID,aAAa;AAOnB,SAASE,GAAGA,CAAA,EAAG;EAAAC,EAAA;EACb,MAAM;IAAEC;EAAgB,CAAC,GAAGf,WAAW,CAAEgB,KAAgB,IAAKA,KAAK,CAACC,IAAI,CAAC;EAEzE,oBACEf,OAAA;IAAKE,SAAS,EAAC,KAAK;IAAAC,QAAA,eAClBH,OAAA,CAACL,MAAM;MAAAQ,QAAA,gBACLH,OAAA,CAACJ,KAAK;QACJoB,IAAI,EAAC,QAAQ;QACbC,OAAO,EAAE,CAACJ,eAAe,gBAAGb,OAAA,CAACC,SAAS;UAAAG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGP,OAAA,CAACH,QAAQ;UAACqB,EAAE,EAAC,YAAY;UAACC,OAAO;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF,CAAC,eACFP,OAAA,CAACJ,KAAK;QACJoB,IAAI,EAAC,YAAY;QACjBC,OAAO,EAAEJ,eAAe,gBAAGb,OAAA,CAACS,aAAa;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,gBAAGP,OAAA,CAACH,QAAQ;UAACqB,EAAE,EAAC,QAAQ;UAACC,OAAO;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjF,CAAC,eACFP,OAAA,CAACJ,KAAK;QAACwB,KAAK;QAACH,OAAO,eAAEjB,OAAA,CAACH,QAAQ;UAACqB,EAAE,EAAC,YAAY;UAACC,OAAO;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9DP,OAAA,CAACJ,KAAK;QAACoB,IAAI,EAAC,GAAG;QAACC,OAAO,eAAEjB,OAAA,CAACH,QAAQ;UAACqB,EAAE,EAAC,QAAQ;UAACC,OAAO;QAAA;UAAAf,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAE;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACvD;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACN,CAAC;AAEV;AAACK,EAAA,CAnBQD,GAAG;EAAA,QACkBb,WAAW;AAAA;AAAAuB,GAAA,GADhCV,GAAG;AAqBZ,eAAeA,GAAG;AAAC,IAAAH,EAAA,EAAAE,GAAA,EAAAW,GAAA;AAAAC,YAAA,CAAAd,EAAA;AAAAc,YAAA,CAAAZ,GAAA;AAAAY,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}