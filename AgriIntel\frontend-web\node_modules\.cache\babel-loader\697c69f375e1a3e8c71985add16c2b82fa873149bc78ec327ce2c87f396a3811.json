{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ClientBulkWriteExecutor = void 0;\nconst abstract_cursor_1 = require(\"../../cursor/abstract_cursor\");\nconst client_bulk_write_cursor_1 = require(\"../../cursor/client_bulk_write_cursor\");\nconst error_1 = require(\"../../error\");\nconst timeout_1 = require(\"../../timeout\");\nconst utils_1 = require(\"../../utils\");\nconst write_concern_1 = require(\"../../write_concern\");\nconst execute_operation_1 = require(\"../execute_operation\");\nconst client_bulk_write_1 = require(\"./client_bulk_write\");\nconst command_builder_1 = require(\"./command_builder\");\nconst results_merger_1 = require(\"./results_merger\");\n/**\n * Responsible for executing a client bulk write.\n * @internal\n */\nclass ClientBulkWriteExecutor {\n  /**\n   * Instantiate the executor.\n   * @param client - The mongo client.\n   * @param operations - The user supplied bulk write models.\n   * @param options - The bulk write options.\n   */\n  constructor(client, operations, options) {\n    if (operations.length === 0) {\n      throw new error_1.MongoClientBulkWriteExecutionError('No client bulk write models were provided.');\n    }\n    this.client = client;\n    this.operations = operations;\n    this.options = {\n      ordered: true,\n      bypassDocumentValidation: false,\n      verboseResults: false,\n      ...options\n    };\n    // If no write concern was provided, we inherit one from the client.\n    if (!this.options.writeConcern) {\n      this.options.writeConcern = write_concern_1.WriteConcern.fromOptions(this.client.s.options);\n    }\n    if (this.options.writeConcern?.w === 0) {\n      if (this.options.verboseResults) {\n        throw new error_1.MongoInvalidArgumentError('Cannot request unacknowledged write concern and verbose results');\n      }\n      if (this.options.ordered) {\n        throw new error_1.MongoInvalidArgumentError('Cannot request unacknowledged write concern and ordered writes');\n      }\n    }\n  }\n  /**\n   * Execute the client bulk write. Will split commands into batches and exhaust the cursors\n   * for each, then merge the results into one.\n   * @returns The result.\n   */\n  async execute() {\n    // The command builder will take the user provided models and potential split the batch\n    // into multiple commands due to size.\n    const pkFactory = this.client.s.options.pkFactory;\n    const commandBuilder = new command_builder_1.ClientBulkWriteCommandBuilder(this.operations, this.options, pkFactory);\n    // Unacknowledged writes need to execute all batches and return { ok: 1}\n    const resolvedOptions = (0, utils_1.resolveTimeoutOptions)(this.client, this.options);\n    const context = timeout_1.TimeoutContext.create(resolvedOptions);\n    if (this.options.writeConcern?.w === 0) {\n      while (commandBuilder.hasNextBatch()) {\n        const operation = new client_bulk_write_1.ClientBulkWriteOperation(commandBuilder, this.options);\n        await (0, execute_operation_1.executeOperation)(this.client, operation, context);\n      }\n      return results_merger_1.ClientBulkWriteResultsMerger.unacknowledged();\n    } else {\n      const resultsMerger = new results_merger_1.ClientBulkWriteResultsMerger(this.options);\n      // For each command will will create and exhaust a cursor for the results.\n      while (commandBuilder.hasNextBatch()) {\n        const cursorContext = new abstract_cursor_1.CursorTimeoutContext(context, Symbol());\n        const options = {\n          ...this.options,\n          timeoutContext: cursorContext,\n          ...(resolvedOptions.timeoutMS != null && {\n            timeoutMode: abstract_cursor_1.CursorTimeoutMode.LIFETIME\n          })\n        };\n        const cursor = new client_bulk_write_cursor_1.ClientBulkWriteCursor(this.client, commandBuilder, options);\n        try {\n          await resultsMerger.merge(cursor);\n        } catch (error) {\n          // Write concern errors are recorded in the writeConcernErrors field on MongoClientBulkWriteError.\n          // When a write concern error is encountered, it should not terminate execution of the bulk write\n          // for either ordered or unordered bulk writes. However, drivers MUST throw an exception at the end\n          // of execution if any write concern errors were observed.\n          if (error instanceof error_1.MongoServerError && !(error instanceof error_1.MongoClientBulkWriteError)) {\n            // Server side errors need to be wrapped inside a MongoClientBulkWriteError, where the root\n            // cause is the error property and a partial result is to be included.\n            const bulkWriteError = new error_1.MongoClientBulkWriteError({\n              message: 'Mongo client bulk write encountered an error during execution'\n            });\n            bulkWriteError.cause = error;\n            bulkWriteError.partialResult = resultsMerger.bulkWriteResult;\n            throw bulkWriteError;\n          } else {\n            // Client side errors are just thrown.\n            throw error;\n          }\n        }\n      }\n      // If we have write concern errors or unordered write errors at the end we throw.\n      if (resultsMerger.writeConcernErrors.length > 0 || resultsMerger.writeErrors.size > 0) {\n        const error = new error_1.MongoClientBulkWriteError({\n          message: 'Mongo client bulk write encountered errors during execution.'\n        });\n        error.writeConcernErrors = resultsMerger.writeConcernErrors;\n        error.writeErrors = resultsMerger.writeErrors;\n        error.partialResult = resultsMerger.bulkWriteResult;\n        throw error;\n      }\n      return resultsMerger.bulkWriteResult;\n    }\n  }\n}\nexports.ClientBulkWriteExecutor = ClientBulkWriteExecutor;", "map": {"version": 3, "names": ["abstract_cursor_1", "require", "client_bulk_write_cursor_1", "error_1", "timeout_1", "utils_1", "write_concern_1", "execute_operation_1", "client_bulk_write_1", "command_builder_1", "results_merger_1", "ClientBulkWriteExecutor", "constructor", "client", "operations", "options", "length", "MongoClientBulkWriteExecutionError", "ordered", "bypassDocumentValidation", "verboseResults", "writeConcern", "WriteConcern", "fromOptions", "s", "w", "MongoInvalidArgumentError", "execute", "pkFactory", "commandBuilder", "ClientBulkWriteCommandBuilder", "resolvedOptions", "resolveTimeoutOptions", "context", "TimeoutContext", "create", "hasNextBatch", "operation", "ClientBulkWriteOperation", "executeOperation", "ClientBulkWriteResultsMerger", "unacknowledged", "resultsMerger", "cursor<PERSON><PERSON>xt", "CursorTimeoutContext", "Symbol", "timeoutContext", "timeoutMS", "timeoutMode", "CursorTimeoutMode", "LIFETIME", "cursor", "ClientBulkWriteCursor", "merge", "error", "MongoServerError", "MongoClientBulkWriteError", "bulkWriteError", "message", "cause", "partialResult", "bulkWriteResult", "writeConcernErrors", "writeErrors", "size", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\client_bulk_write\\executor.ts"], "sourcesContent": ["import { type Document } from '../../bson';\nimport { CursorTimeoutContext, CursorTimeoutMode } from '../../cursor/abstract_cursor';\nimport { ClientBulkWriteCursor } from '../../cursor/client_bulk_write_cursor';\nimport {\n  MongoClientBulkWriteError,\n  MongoClientBulkWriteExecutionError,\n  MongoInvalidArgumentError,\n  MongoServerError\n} from '../../error';\nimport { type MongoClient } from '../../mongo_client';\nimport { TimeoutContext } from '../../timeout';\nimport { resolveTimeoutOptions } from '../../utils';\nimport { WriteConcern } from '../../write_concern';\nimport { executeOperation } from '../execute_operation';\nimport { ClientBulkWriteOperation } from './client_bulk_write';\nimport { ClientBulkWriteCommandBuilder } from './command_builder';\nimport {\n  type AnyClientBulkWriteModel,\n  type ClientBulkWriteOptions,\n  type ClientBulkWriteResult\n} from './common';\nimport { ClientBulkWriteResultsMerger } from './results_merger';\n\n/**\n * Responsible for executing a client bulk write.\n * @internal\n */\nexport class ClientBulkWriteExecutor {\n  private readonly client: MongoClient;\n  private readonly options: ClientBulkWriteOptions;\n  private readonly operations: ReadonlyArray<AnyClientBulkWriteModel<Document>>;\n\n  /**\n   * Instantiate the executor.\n   * @param client - The mongo client.\n   * @param operations - The user supplied bulk write models.\n   * @param options - The bulk write options.\n   */\n  constructor(\n    client: MongoClient,\n    operations: ReadonlyArray<AnyClientBulkWriteModel<Document>>,\n    options?: ClientBulkWriteOptions\n  ) {\n    if (operations.length === 0) {\n      throw new MongoClientBulkWriteExecutionError('No client bulk write models were provided.');\n    }\n\n    this.client = client;\n    this.operations = operations;\n    this.options = {\n      ordered: true,\n      bypassDocumentValidation: false,\n      verboseResults: false,\n      ...options\n    };\n\n    // If no write concern was provided, we inherit one from the client.\n    if (!this.options.writeConcern) {\n      this.options.writeConcern = WriteConcern.fromOptions(this.client.s.options);\n    }\n\n    if (this.options.writeConcern?.w === 0) {\n      if (this.options.verboseResults) {\n        throw new MongoInvalidArgumentError(\n          'Cannot request unacknowledged write concern and verbose results'\n        );\n      }\n\n      if (this.options.ordered) {\n        throw new MongoInvalidArgumentError(\n          'Cannot request unacknowledged write concern and ordered writes'\n        );\n      }\n    }\n  }\n\n  /**\n   * Execute the client bulk write. Will split commands into batches and exhaust the cursors\n   * for each, then merge the results into one.\n   * @returns The result.\n   */\n  async execute(): Promise<ClientBulkWriteResult> {\n    // The command builder will take the user provided models and potential split the batch\n    // into multiple commands due to size.\n    const pkFactory = this.client.s.options.pkFactory;\n    const commandBuilder = new ClientBulkWriteCommandBuilder(\n      this.operations,\n      this.options,\n      pkFactory\n    );\n    // Unacknowledged writes need to execute all batches and return { ok: 1}\n    const resolvedOptions = resolveTimeoutOptions(this.client, this.options);\n    const context = TimeoutContext.create(resolvedOptions);\n\n    if (this.options.writeConcern?.w === 0) {\n      while (commandBuilder.hasNextBatch()) {\n        const operation = new ClientBulkWriteOperation(commandBuilder, this.options);\n        await executeOperation(this.client, operation, context);\n      }\n      return ClientBulkWriteResultsMerger.unacknowledged();\n    } else {\n      const resultsMerger = new ClientBulkWriteResultsMerger(this.options);\n      // For each command will will create and exhaust a cursor for the results.\n      while (commandBuilder.hasNextBatch()) {\n        const cursorContext = new CursorTimeoutContext(context, Symbol());\n        const options = {\n          ...this.options,\n          timeoutContext: cursorContext,\n          ...(resolvedOptions.timeoutMS != null && { timeoutMode: CursorTimeoutMode.LIFETIME })\n        };\n        const cursor = new ClientBulkWriteCursor(this.client, commandBuilder, options);\n        try {\n          await resultsMerger.merge(cursor);\n        } catch (error) {\n          // Write concern errors are recorded in the writeConcernErrors field on MongoClientBulkWriteError.\n          // When a write concern error is encountered, it should not terminate execution of the bulk write\n          // for either ordered or unordered bulk writes. However, drivers MUST throw an exception at the end\n          // of execution if any write concern errors were observed.\n          if (error instanceof MongoServerError && !(error instanceof MongoClientBulkWriteError)) {\n            // Server side errors need to be wrapped inside a MongoClientBulkWriteError, where the root\n            // cause is the error property and a partial result is to be included.\n            const bulkWriteError = new MongoClientBulkWriteError({\n              message: 'Mongo client bulk write encountered an error during execution'\n            });\n            bulkWriteError.cause = error;\n            bulkWriteError.partialResult = resultsMerger.bulkWriteResult;\n            throw bulkWriteError;\n          } else {\n            // Client side errors are just thrown.\n            throw error;\n          }\n        }\n      }\n\n      // If we have write concern errors or unordered write errors at the end we throw.\n      if (resultsMerger.writeConcernErrors.length > 0 || resultsMerger.writeErrors.size > 0) {\n        const error = new MongoClientBulkWriteError({\n          message: 'Mongo client bulk write encountered errors during execution.'\n        });\n        error.writeConcernErrors = resultsMerger.writeConcernErrors;\n        error.writeErrors = resultsMerger.writeErrors;\n        error.partialResult = resultsMerger.bulkWriteResult;\n        throw error;\n      }\n\n      return resultsMerger.bulkWriteResult;\n    }\n  }\n}\n"], "mappings": ";;;;;;AACA,MAAAA,iBAAA,GAAAC,OAAA;AACA,MAAAC,0BAAA,GAAAD,OAAA;AACA,MAAAE,OAAA,GAAAF,OAAA;AAOA,MAAAG,SAAA,GAAAH,OAAA;AACA,MAAAI,OAAA,GAAAJ,OAAA;AACA,MAAAK,eAAA,GAAAL,OAAA;AACA,MAAAM,mBAAA,GAAAN,OAAA;AACA,MAAAO,mBAAA,GAAAP,OAAA;AACA,MAAAQ,iBAAA,GAAAR,OAAA;AAMA,MAAAS,gBAAA,GAAAT,OAAA;AAEA;;;;AAIA,MAAaU,uBAAuB;EAKlC;;;;;;EAMAC,YACEC,MAAmB,EACnBC,UAA4D,EAC5DC,OAAgC;IAEhC,IAAID,UAAU,CAACE,MAAM,KAAK,CAAC,EAAE;MAC3B,MAAM,IAAIb,OAAA,CAAAc,kCAAkC,CAAC,4CAA4C,CAAC;IAC5F;IAEA,IAAI,CAACJ,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,OAAO,GAAG;MACbG,OAAO,EAAE,IAAI;MACbC,wBAAwB,EAAE,KAAK;MAC/BC,cAAc,EAAE,KAAK;MACrB,GAAGL;KACJ;IAED;IACA,IAAI,CAAC,IAAI,CAACA,OAAO,CAACM,YAAY,EAAE;MAC9B,IAAI,CAACN,OAAO,CAACM,YAAY,GAAGf,eAAA,CAAAgB,YAAY,CAACC,WAAW,CAAC,IAAI,CAACV,MAAM,CAACW,CAAC,CAACT,OAAO,CAAC;IAC7E;IAEA,IAAI,IAAI,CAACA,OAAO,CAACM,YAAY,EAAEI,CAAC,KAAK,CAAC,EAAE;MACtC,IAAI,IAAI,CAACV,OAAO,CAACK,cAAc,EAAE;QAC/B,MAAM,IAAIjB,OAAA,CAAAuB,yBAAyB,CACjC,iEAAiE,CAClE;MACH;MAEA,IAAI,IAAI,CAACX,OAAO,CAACG,OAAO,EAAE;QACxB,MAAM,IAAIf,OAAA,CAAAuB,yBAAyB,CACjC,gEAAgE,CACjE;MACH;IACF;EACF;EAEA;;;;;EAKA,MAAMC,OAAOA,CAAA;IACX;IACA;IACA,MAAMC,SAAS,GAAG,IAAI,CAACf,MAAM,CAACW,CAAC,CAACT,OAAO,CAACa,SAAS;IACjD,MAAMC,cAAc,GAAG,IAAIpB,iBAAA,CAAAqB,6BAA6B,CACtD,IAAI,CAAChB,UAAU,EACf,IAAI,CAACC,OAAO,EACZa,SAAS,CACV;IACD;IACA,MAAMG,eAAe,GAAG,IAAA1B,OAAA,CAAA2B,qBAAqB,EAAC,IAAI,CAACnB,MAAM,EAAE,IAAI,CAACE,OAAO,CAAC;IACxE,MAAMkB,OAAO,GAAG7B,SAAA,CAAA8B,cAAc,CAACC,MAAM,CAACJ,eAAe,CAAC;IAEtD,IAAI,IAAI,CAAChB,OAAO,CAACM,YAAY,EAAEI,CAAC,KAAK,CAAC,EAAE;MACtC,OAAOI,cAAc,CAACO,YAAY,EAAE,EAAE;QACpC,MAAMC,SAAS,GAAG,IAAI7B,mBAAA,CAAA8B,wBAAwB,CAACT,cAAc,EAAE,IAAI,CAACd,OAAO,CAAC;QAC5E,MAAM,IAAAR,mBAAA,CAAAgC,gBAAgB,EAAC,IAAI,CAAC1B,MAAM,EAAEwB,SAAS,EAAEJ,OAAO,CAAC;MACzD;MACA,OAAOvB,gBAAA,CAAA8B,4BAA4B,CAACC,cAAc,EAAE;IACtD,CAAC,MAAM;MACL,MAAMC,aAAa,GAAG,IAAIhC,gBAAA,CAAA8B,4BAA4B,CAAC,IAAI,CAACzB,OAAO,CAAC;MACpE;MACA,OAAOc,cAAc,CAACO,YAAY,EAAE,EAAE;QACpC,MAAMO,aAAa,GAAG,IAAI3C,iBAAA,CAAA4C,oBAAoB,CAACX,OAAO,EAAEY,MAAM,EAAE,CAAC;QACjE,MAAM9B,OAAO,GAAG;UACd,GAAG,IAAI,CAACA,OAAO;UACf+B,cAAc,EAAEH,aAAa;UAC7B,IAAIZ,eAAe,CAACgB,SAAS,IAAI,IAAI,IAAI;YAAEC,WAAW,EAAEhD,iBAAA,CAAAiD,iBAAiB,CAACC;UAAQ,CAAE;SACrF;QACD,MAAMC,MAAM,GAAG,IAAIjD,0BAAA,CAAAkD,qBAAqB,CAAC,IAAI,CAACvC,MAAM,EAAEgB,cAAc,EAAEd,OAAO,CAAC;QAC9E,IAAI;UACF,MAAM2B,aAAa,CAACW,KAAK,CAACF,MAAM,CAAC;QACnC,CAAC,CAAC,OAAOG,KAAK,EAAE;UACd;UACA;UACA;UACA;UACA,IAAIA,KAAK,YAAYnD,OAAA,CAAAoD,gBAAgB,IAAI,EAAED,KAAK,YAAYnD,OAAA,CAAAqD,yBAAyB,CAAC,EAAE;YACtF;YACA;YACA,MAAMC,cAAc,GAAG,IAAItD,OAAA,CAAAqD,yBAAyB,CAAC;cACnDE,OAAO,EAAE;aACV,CAAC;YACFD,cAAc,CAACE,KAAK,GAAGL,KAAK;YAC5BG,cAAc,CAACG,aAAa,GAAGlB,aAAa,CAACmB,eAAe;YAC5D,MAAMJ,cAAc;UACtB,CAAC,MAAM;YACL;YACA,MAAMH,KAAK;UACb;QACF;MACF;MAEA;MACA,IAAIZ,aAAa,CAACoB,kBAAkB,CAAC9C,MAAM,GAAG,CAAC,IAAI0B,aAAa,CAACqB,WAAW,CAACC,IAAI,GAAG,CAAC,EAAE;QACrF,MAAMV,KAAK,GAAG,IAAInD,OAAA,CAAAqD,yBAAyB,CAAC;UAC1CE,OAAO,EAAE;SACV,CAAC;QACFJ,KAAK,CAACQ,kBAAkB,GAAGpB,aAAa,CAACoB,kBAAkB;QAC3DR,KAAK,CAACS,WAAW,GAAGrB,aAAa,CAACqB,WAAW;QAC7CT,KAAK,CAACM,aAAa,GAAGlB,aAAa,CAACmB,eAAe;QACnD,MAAMP,KAAK;MACb;MAEA,OAAOZ,aAAa,CAACmB,eAAe;IACtC;EACF;;AAxHFI,OAAA,CAAAtD,uBAAA,GAAAA,uBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}