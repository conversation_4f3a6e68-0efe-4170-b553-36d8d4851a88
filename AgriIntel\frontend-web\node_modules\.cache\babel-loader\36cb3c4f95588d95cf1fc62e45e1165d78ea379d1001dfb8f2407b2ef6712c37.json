{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { millisecondsInMinute } from \"../constants/index.js\";\n/**\n * @name minutesToMilliseconds\n * @category Conversion Helpers\n * @summary Convert minutes to milliseconds.\n *\n * @description\n * Convert a number of minutes to a full number of milliseconds.\n *\n * @param {number} minutes - number of minutes to be converted\n *\n * @returns {number} the number of minutes converted in milliseconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 minutes to milliseconds\n * const result = minutesToMilliseconds(2)\n * //=> 120000\n */\nexport default function minutesToMilliseconds(minutes) {\n  requiredArgs(1, arguments);\n  return Math.floor(minutes * millisecondsInMinute);\n}", "map": {"version": 3, "names": ["requiredArgs", "millisecondsInMinute", "minutesToMilliseconds", "minutes", "arguments", "Math", "floor"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/date-fns/esm/minutesToMilliseconds/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { millisecondsInMinute } from \"../constants/index.js\";\n/**\n * @name minutesToMilliseconds\n * @category Conversion Helpers\n * @summary Convert minutes to milliseconds.\n *\n * @description\n * Convert a number of minutes to a full number of milliseconds.\n *\n * @param {number} minutes - number of minutes to be converted\n *\n * @returns {number} the number of minutes converted in milliseconds\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 2 minutes to milliseconds\n * const result = minutesToMilliseconds(2)\n * //=> 120000\n */\nexport default function minutesToMilliseconds(minutes) {\n  requiredArgs(1, arguments);\n  return Math.floor(minutes * millisecondsInMinute);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,qBAAqBA,CAACC,OAAO,EAAE;EACrDH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,OAAOC,IAAI,CAACC,KAAK,CAACH,OAAO,GAAGF,oBAAoB,CAAC;AACnD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}