{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.RTTSampler = exports.MonitorInterval = exports.RTTPinger = exports.Monitor = exports.ServerMonitoringMode = void 0;\nconst timers_1 = require(\"timers\");\nconst bson_1 = require(\"../bson\");\nconst connect_1 = require(\"../cmap/connect\");\nconst client_metadata_1 = require(\"../cmap/handshake/client_metadata\");\nconst constants_1 = require(\"../constants\");\nconst error_1 = require(\"../error\");\nconst mongo_logger_1 = require(\"../mongo_logger\");\nconst mongo_types_1 = require(\"../mongo_types\");\nconst utils_1 = require(\"../utils\");\nconst common_1 = require(\"./common\");\nconst events_1 = require(\"./events\");\nconst server_1 = require(\"./server\");\nconst STATE_IDLE = 'idle';\nconst STATE_MONITORING = 'monitoring';\nconst stateTransition = (0, utils_1.makeStateMachine)({\n  [common_1.STATE_CLOSING]: [common_1.STATE_CLOSING, STATE_IDLE, common_1.STATE_CLOSED],\n  [common_1.STATE_CLOSED]: [common_1.STATE_CLOSED, STATE_MONITORING],\n  [STATE_IDLE]: [STATE_IDLE, STATE_MONITORING, common_1.STATE_CLOSING],\n  [STATE_MONITORING]: [STATE_MONITORING, STATE_IDLE, common_1.STATE_CLOSING]\n});\nconst INVALID_REQUEST_CHECK_STATES = new Set([common_1.STATE_CLOSING, common_1.STATE_CLOSED, STATE_MONITORING]);\nfunction isInCloseState(monitor) {\n  return monitor.s.state === common_1.STATE_CLOSED || monitor.s.state === common_1.STATE_CLOSING;\n}\n/** @public */\nexports.ServerMonitoringMode = Object.freeze({\n  auto: 'auto',\n  poll: 'poll',\n  stream: 'stream'\n});\n/** @internal */\nclass Monitor extends mongo_types_1.TypedEventEmitter {\n  constructor(server, options) {\n    super();\n    /** @internal */\n    this.component = mongo_logger_1.MongoLoggableComponent.TOPOLOGY;\n    this.on('error', utils_1.noop);\n    this.server = server;\n    this.connection = null;\n    this.cancellationToken = new mongo_types_1.CancellationToken();\n    this.cancellationToken.setMaxListeners(Infinity);\n    this.monitorId = undefined;\n    this.s = {\n      state: common_1.STATE_CLOSED\n    };\n    this.address = server.description.address;\n    this.options = Object.freeze({\n      connectTimeoutMS: options.connectTimeoutMS ?? 10000,\n      heartbeatFrequencyMS: options.heartbeatFrequencyMS ?? 10000,\n      minHeartbeatFrequencyMS: options.minHeartbeatFrequencyMS ?? 500,\n      serverMonitoringMode: options.serverMonitoringMode\n    });\n    this.isRunningInFaasEnv = (0, client_metadata_1.getFAASEnv)() != null;\n    this.mongoLogger = this.server.topology.client?.mongoLogger;\n    this.rttSampler = new RTTSampler(10);\n    const cancellationToken = this.cancellationToken;\n    // TODO: refactor this to pull it directly from the pool, requires new ConnectionPool integration\n    const connectOptions = {\n      id: '<monitor>',\n      generation: server.pool.generation,\n      cancellationToken,\n      hostAddress: server.description.hostAddress,\n      ...options,\n      // force BSON serialization options\n      raw: false,\n      useBigInt64: false,\n      promoteLongs: true,\n      promoteValues: true,\n      promoteBuffers: true\n    };\n    // ensure no authentication is used for monitoring\n    delete connectOptions.credentials;\n    if (connectOptions.autoEncrypter) {\n      delete connectOptions.autoEncrypter;\n    }\n    this.connectOptions = Object.freeze(connectOptions);\n  }\n  connect() {\n    if (this.s.state !== common_1.STATE_CLOSED) {\n      return;\n    }\n    // start\n    const heartbeatFrequencyMS = this.options.heartbeatFrequencyMS;\n    const minHeartbeatFrequencyMS = this.options.minHeartbeatFrequencyMS;\n    this.monitorId = new MonitorInterval(monitorServer(this), {\n      heartbeatFrequencyMS: heartbeatFrequencyMS,\n      minHeartbeatFrequencyMS: minHeartbeatFrequencyMS,\n      immediate: true\n    });\n  }\n  requestCheck() {\n    if (INVALID_REQUEST_CHECK_STATES.has(this.s.state)) {\n      return;\n    }\n    this.monitorId?.wake();\n  }\n  reset() {\n    const topologyVersion = this.server.description.topologyVersion;\n    if (isInCloseState(this) || topologyVersion == null) {\n      return;\n    }\n    stateTransition(this, common_1.STATE_CLOSING);\n    resetMonitorState(this);\n    // restart monitor\n    stateTransition(this, STATE_IDLE);\n    // restart monitoring\n    const heartbeatFrequencyMS = this.options.heartbeatFrequencyMS;\n    const minHeartbeatFrequencyMS = this.options.minHeartbeatFrequencyMS;\n    this.monitorId = new MonitorInterval(monitorServer(this), {\n      heartbeatFrequencyMS: heartbeatFrequencyMS,\n      minHeartbeatFrequencyMS: minHeartbeatFrequencyMS\n    });\n  }\n  close() {\n    if (isInCloseState(this)) {\n      return;\n    }\n    stateTransition(this, common_1.STATE_CLOSING);\n    resetMonitorState(this);\n    // close monitor\n    this.emit('close');\n    stateTransition(this, common_1.STATE_CLOSED);\n  }\n  get roundTripTime() {\n    return this.rttSampler.average();\n  }\n  get minRoundTripTime() {\n    return this.rttSampler.min();\n  }\n  get latestRtt() {\n    return this.rttSampler.last;\n  }\n  addRttSample(rtt) {\n    this.rttSampler.addSample(rtt);\n  }\n  clearRttSamples() {\n    this.rttSampler.clear();\n  }\n}\nexports.Monitor = Monitor;\nfunction resetMonitorState(monitor) {\n  monitor.monitorId?.stop();\n  monitor.monitorId = undefined;\n  monitor.rttPinger?.close();\n  monitor.rttPinger = undefined;\n  monitor.cancellationToken.emit('cancel');\n  monitor.connection?.destroy();\n  monitor.connection = null;\n  monitor.clearRttSamples();\n}\nfunction useStreamingProtocol(monitor, topologyVersion) {\n  // If we have no topology version we always poll no matter\n  // what the user provided, since the server does not support\n  // the streaming protocol.\n  if (topologyVersion == null) return false;\n  const serverMonitoringMode = monitor.options.serverMonitoringMode;\n  if (serverMonitoringMode === exports.ServerMonitoringMode.poll) return false;\n  if (serverMonitoringMode === exports.ServerMonitoringMode.stream) return true;\n  // If we are in auto mode, we need to figure out if we're in a FaaS\n  // environment or not and choose the appropriate mode.\n  if (monitor.isRunningInFaasEnv) return false;\n  return true;\n}\nfunction checkServer(monitor, callback) {\n  let start;\n  let awaited;\n  const topologyVersion = monitor.server.description.topologyVersion;\n  const isAwaitable = useStreamingProtocol(monitor, topologyVersion);\n  monitor.emitAndLogHeartbeat(server_1.Server.SERVER_HEARTBEAT_STARTED, monitor.server.topology.s.id, undefined, new events_1.ServerHeartbeatStartedEvent(monitor.address, isAwaitable));\n  function onHeartbeatFailed(err) {\n    monitor.connection?.destroy();\n    monitor.connection = null;\n    monitor.emitAndLogHeartbeat(server_1.Server.SERVER_HEARTBEAT_FAILED, monitor.server.topology.s.id, undefined, new events_1.ServerHeartbeatFailedEvent(monitor.address, (0, utils_1.calculateDurationInMs)(start), err, awaited));\n    const error = !(err instanceof error_1.MongoError) ? new error_1.MongoError(error_1.MongoError.buildErrorMessage(err), {\n      cause: err\n    }) : err;\n    error.addErrorLabel(error_1.MongoErrorLabel.ResetPool);\n    if (error instanceof error_1.MongoNetworkTimeoutError) {\n      error.addErrorLabel(error_1.MongoErrorLabel.InterruptInUseConnections);\n    }\n    monitor.emit('resetServer', error);\n    callback(err);\n  }\n  function onHeartbeatSucceeded(hello) {\n    if (!('isWritablePrimary' in hello)) {\n      // Provide hello-style response document.\n      hello.isWritablePrimary = hello[constants_1.LEGACY_HELLO_COMMAND];\n    }\n    // NOTE: here we use the latestRtt as this measurement corresponds with the value\n    // obtained for this successful heartbeat, if there is no latestRtt, then we calculate the\n    // duration\n    const duration = isAwaitable && monitor.rttPinger ? monitor.rttPinger.latestRtt ?? (0, utils_1.calculateDurationInMs)(start) : (0, utils_1.calculateDurationInMs)(start);\n    monitor.addRttSample(duration);\n    monitor.emitAndLogHeartbeat(server_1.Server.SERVER_HEARTBEAT_SUCCEEDED, monitor.server.topology.s.id, hello.connectionId, new events_1.ServerHeartbeatSucceededEvent(monitor.address, duration, hello, isAwaitable));\n    if (isAwaitable) {\n      // If we are using the streaming protocol then we immediately issue another 'started'\n      // event, otherwise the \"check\" is complete and return to the main monitor loop\n      monitor.emitAndLogHeartbeat(server_1.Server.SERVER_HEARTBEAT_STARTED, monitor.server.topology.s.id, undefined, new events_1.ServerHeartbeatStartedEvent(monitor.address, true));\n      // We have not actually sent an outgoing handshake, but when we get the next response we\n      // want the duration to reflect the time since we last heard from the server\n      start = (0, utils_1.now)();\n    } else {\n      monitor.rttPinger?.close();\n      monitor.rttPinger = undefined;\n      callback(undefined, hello);\n    }\n  }\n  const {\n    connection\n  } = monitor;\n  if (connection && !connection.closed) {\n    const {\n      serverApi,\n      helloOk\n    } = connection;\n    const connectTimeoutMS = monitor.options.connectTimeoutMS;\n    const maxAwaitTimeMS = monitor.options.heartbeatFrequencyMS;\n    const cmd = {\n      [serverApi?.version || helloOk ? 'hello' : constants_1.LEGACY_HELLO_COMMAND]: 1,\n      ...(isAwaitable && topologyVersion ? {\n        maxAwaitTimeMS,\n        topologyVersion: makeTopologyVersion(topologyVersion)\n      } : {})\n    };\n    const options = isAwaitable ? {\n      socketTimeoutMS: connectTimeoutMS ? connectTimeoutMS + maxAwaitTimeMS : 0,\n      exhaustAllowed: true\n    } : {\n      socketTimeoutMS: connectTimeoutMS\n    };\n    if (isAwaitable && monitor.rttPinger == null) {\n      monitor.rttPinger = new RTTPinger(monitor);\n    }\n    // Record new start time before sending handshake\n    start = (0, utils_1.now)();\n    if (isAwaitable) {\n      awaited = true;\n      return connection.exhaustCommand((0, utils_1.ns)('admin.$cmd'), cmd, options, (error, hello) => {\n        if (error) return onHeartbeatFailed(error);\n        return onHeartbeatSucceeded(hello);\n      });\n    }\n    awaited = false;\n    connection.command((0, utils_1.ns)('admin.$cmd'), cmd, options).then(onHeartbeatSucceeded, onHeartbeatFailed);\n    return;\n  }\n  // connecting does an implicit `hello`\n  (async () => {\n    const socket = await (0, connect_1.makeSocket)(monitor.connectOptions);\n    const connection = (0, connect_1.makeConnection)(monitor.connectOptions, socket);\n    // The start time is after socket creation but before the handshake\n    start = (0, utils_1.now)();\n    try {\n      await (0, connect_1.performInitialHandshake)(connection, monitor.connectOptions);\n      return connection;\n    } catch (error) {\n      connection.destroy();\n      throw error;\n    }\n  })().then(connection => {\n    if (isInCloseState(monitor)) {\n      connection.destroy();\n      return;\n    }\n    const duration = (0, utils_1.calculateDurationInMs)(start);\n    monitor.addRttSample(duration);\n    monitor.connection = connection;\n    monitor.emitAndLogHeartbeat(server_1.Server.SERVER_HEARTBEAT_SUCCEEDED, monitor.server.topology.s.id, connection.hello?.connectionId, new events_1.ServerHeartbeatSucceededEvent(monitor.address, duration, connection.hello, useStreamingProtocol(monitor, connection.hello?.topologyVersion)));\n    callback(undefined, connection.hello);\n  }, error => {\n    monitor.connection = null;\n    awaited = false;\n    onHeartbeatFailed(error);\n  });\n}\nfunction monitorServer(monitor) {\n  return callback => {\n    if (monitor.s.state === STATE_MONITORING) {\n      process.nextTick(callback);\n      return;\n    }\n    stateTransition(monitor, STATE_MONITORING);\n    function done() {\n      if (!isInCloseState(monitor)) {\n        stateTransition(monitor, STATE_IDLE);\n      }\n      callback();\n    }\n    checkServer(monitor, (err, hello) => {\n      if (err) {\n        // otherwise an error occurred on initial discovery, also bail\n        if (monitor.server.description.type === common_1.ServerType.Unknown) {\n          return done();\n        }\n      }\n      // if the check indicates streaming is supported, immediately reschedule monitoring\n      if (useStreamingProtocol(monitor, hello?.topologyVersion)) {\n        (0, timers_1.setTimeout)(() => {\n          if (!isInCloseState(monitor)) {\n            monitor.monitorId?.wake();\n          }\n        }, 0);\n      }\n      done();\n    });\n  };\n}\nfunction makeTopologyVersion(tv) {\n  return {\n    processId: tv.processId,\n    // tests mock counter as just number, but in a real situation counter should always be a Long\n    // TODO(NODE-2674): Preserve int64 sent from MongoDB\n    counter: bson_1.Long.isLong(tv.counter) ? tv.counter : bson_1.Long.fromNumber(tv.counter)\n  };\n}\n/** @internal */\nclass RTTPinger {\n  constructor(monitor) {\n    this.connection = undefined;\n    this.cancellationToken = monitor.cancellationToken;\n    this.closed = false;\n    this.monitor = monitor;\n    this.latestRtt = monitor.latestRtt ?? undefined;\n    const heartbeatFrequencyMS = monitor.options.heartbeatFrequencyMS;\n    this.monitorId = (0, timers_1.setTimeout)(() => this.measureRoundTripTime(), heartbeatFrequencyMS);\n  }\n  get roundTripTime() {\n    return this.monitor.roundTripTime;\n  }\n  get minRoundTripTime() {\n    return this.monitor.minRoundTripTime;\n  }\n  close() {\n    this.closed = true;\n    (0, timers_1.clearTimeout)(this.monitorId);\n    this.connection?.destroy();\n    this.connection = undefined;\n  }\n  measureAndReschedule(start, conn) {\n    if (this.closed) {\n      conn?.destroy();\n      return;\n    }\n    if (this.connection == null) {\n      this.connection = conn;\n    }\n    this.latestRtt = (0, utils_1.calculateDurationInMs)(start);\n    this.monitorId = (0, timers_1.setTimeout)(() => this.measureRoundTripTime(), this.monitor.options.heartbeatFrequencyMS);\n  }\n  measureRoundTripTime() {\n    const start = (0, utils_1.now)();\n    if (this.closed) {\n      return;\n    }\n    const connection = this.connection;\n    if (connection == null) {\n      (0, connect_1.connect)(this.monitor.connectOptions).then(connection => {\n        this.measureAndReschedule(start, connection);\n      }, () => {\n        this.connection = undefined;\n      });\n      return;\n    }\n    const commandName = connection.serverApi?.version || connection.helloOk ? 'hello' : constants_1.LEGACY_HELLO_COMMAND;\n    connection.command((0, utils_1.ns)('admin.$cmd'), {\n      [commandName]: 1\n    }, undefined).then(() => this.measureAndReschedule(start), () => {\n      this.connection?.destroy();\n      this.connection = undefined;\n      return;\n    });\n  }\n}\nexports.RTTPinger = RTTPinger;\n/**\n * @internal\n */\nclass MonitorInterval {\n  constructor(fn, options = {}) {\n    this.isExpeditedCallToFnScheduled = false;\n    this.stopped = false;\n    this.isExecutionInProgress = false;\n    this.hasExecutedOnce = false;\n    this._executeAndReschedule = () => {\n      if (this.stopped) return;\n      if (this.timerId) {\n        (0, timers_1.clearTimeout)(this.timerId);\n      }\n      this.isExpeditedCallToFnScheduled = false;\n      this.isExecutionInProgress = true;\n      this.fn(() => {\n        this.lastExecutionEnded = (0, utils_1.now)();\n        this.isExecutionInProgress = false;\n        this._reschedule(this.heartbeatFrequencyMS);\n      });\n    };\n    this.fn = fn;\n    this.lastExecutionEnded = -Infinity;\n    this.heartbeatFrequencyMS = options.heartbeatFrequencyMS ?? 1000;\n    this.minHeartbeatFrequencyMS = options.minHeartbeatFrequencyMS ?? 500;\n    if (options.immediate) {\n      this._executeAndReschedule();\n    } else {\n      this._reschedule(undefined);\n    }\n  }\n  wake() {\n    const currentTime = (0, utils_1.now)();\n    const timeSinceLastCall = currentTime - this.lastExecutionEnded;\n    // TODO(NODE-4674): Add error handling and logging to the monitor\n    if (timeSinceLastCall < 0) {\n      return this._executeAndReschedule();\n    }\n    if (this.isExecutionInProgress) {\n      return;\n    }\n    // debounce multiple calls to wake within the `minInterval`\n    if (this.isExpeditedCallToFnScheduled) {\n      return;\n    }\n    // reschedule a call as soon as possible, ensuring the call never happens\n    // faster than the `minInterval`\n    if (timeSinceLastCall < this.minHeartbeatFrequencyMS) {\n      this.isExpeditedCallToFnScheduled = true;\n      this._reschedule(this.minHeartbeatFrequencyMS - timeSinceLastCall);\n      return;\n    }\n    this._executeAndReschedule();\n  }\n  stop() {\n    this.stopped = true;\n    if (this.timerId) {\n      (0, timers_1.clearTimeout)(this.timerId);\n      this.timerId = undefined;\n    }\n    this.lastExecutionEnded = -Infinity;\n    this.isExpeditedCallToFnScheduled = false;\n  }\n  toString() {\n    return JSON.stringify(this);\n  }\n  toJSON() {\n    const currentTime = (0, utils_1.now)();\n    const timeSinceLastCall = currentTime - this.lastExecutionEnded;\n    return {\n      timerId: this.timerId != null ? 'set' : 'cleared',\n      lastCallTime: this.lastExecutionEnded,\n      isExpeditedCheckScheduled: this.isExpeditedCallToFnScheduled,\n      stopped: this.stopped,\n      heartbeatFrequencyMS: this.heartbeatFrequencyMS,\n      minHeartbeatFrequencyMS: this.minHeartbeatFrequencyMS,\n      currentTime,\n      timeSinceLastCall\n    };\n  }\n  _reschedule(ms) {\n    if (this.stopped) return;\n    if (this.timerId) {\n      (0, timers_1.clearTimeout)(this.timerId);\n    }\n    this.timerId = (0, timers_1.setTimeout)(this._executeAndReschedule, ms || this.heartbeatFrequencyMS);\n  }\n}\nexports.MonitorInterval = MonitorInterval;\n/** @internal\n * This class implements the RTT sampling logic specified for [CSOT](https://github.com/mongodb/specifications/blob/bbb335e60cd7ea1e0f7cd9a9443cb95fc9d3b64d/source/client-side-operations-timeout/client-side-operations-timeout.md#drivers-use-minimum-rtt-to-short-circuit-operations)\n *\n * This is implemented as a [circular buffer](https://en.wikipedia.org/wiki/Circular_buffer) keeping\n * the most recent `windowSize` samples\n * */\nclass RTTSampler {\n  constructor(windowSize = 10) {\n    this.rttSamples = new Float64Array(windowSize);\n    this.length = 0;\n    this.writeIndex = 0;\n  }\n  /**\n   * Adds an rtt sample to the end of the circular buffer\n   * When `windowSize` samples have been collected, `addSample` overwrites the least recently added\n   * sample\n   */\n  addSample(sample) {\n    this.rttSamples[this.writeIndex++] = sample;\n    if (this.length < this.rttSamples.length) {\n      this.length++;\n    }\n    this.writeIndex %= this.rttSamples.length;\n  }\n  /**\n   * When \\< 2 samples have been collected, returns 0\n   * Otherwise computes the minimum value samples contained in the buffer\n   */\n  min() {\n    if (this.length < 2) return 0;\n    let min = this.rttSamples[0];\n    for (let i = 1; i < this.length; i++) {\n      if (this.rttSamples[i] < min) min = this.rttSamples[i];\n    }\n    return min;\n  }\n  /**\n   * Returns mean of samples contained in the buffer\n   */\n  average() {\n    if (this.length === 0) return 0;\n    let sum = 0;\n    for (let i = 0; i < this.length; i++) {\n      sum += this.rttSamples[i];\n    }\n    return sum / this.length;\n  }\n  /**\n   * Returns most recently inserted element in the buffer\n   * Returns null if the buffer is empty\n   * */\n  get last() {\n    if (this.length === 0) return null;\n    return this.rttSamples[this.writeIndex === 0 ? this.length - 1 : this.writeIndex - 1];\n  }\n  /**\n   * Clear the buffer\n   * NOTE: this does not overwrite the data held in the internal array, just the pointers into\n   * this array\n   */\n  clear() {\n    this.length = 0;\n    this.writeIndex = 0;\n  }\n}\nexports.RTTSampler = RTTSampler;", "map": {"version": 3, "names": ["timers_1", "require", "bson_1", "connect_1", "client_metadata_1", "constants_1", "error_1", "mongo_logger_1", "mongo_types_1", "utils_1", "common_1", "events_1", "server_1", "STATE_IDLE", "STATE_MONITORING", "stateTransition", "makeStateMachine", "STATE_CLOSING", "STATE_CLOSED", "INVALID_REQUEST_CHECK_STATES", "Set", "isInCloseState", "monitor", "s", "state", "exports", "ServerMonitoringMode", "Object", "freeze", "auto", "poll", "stream", "Monitor", "TypedEventEmitter", "constructor", "server", "options", "component", "MongoLoggableComponent", "TOPOLOGY", "on", "noop", "connection", "cancellationToken", "CancellationToken", "setMaxListeners", "Infinity", "monitorId", "undefined", "address", "description", "connectTimeoutMS", "heartbeatFrequencyMS", "minHeartbeatFrequencyMS", "serverMonitoringMode", "isRunningInFaasEnv", "getFAASEnv", "mongoLogger", "topology", "client", "rttSampler", "RTTSampler", "connectOptions", "id", "generation", "pool", "host<PERSON><PERSON><PERSON>", "raw", "useBigInt64", "promoteLongs", "promoteValues", "promoteBuffers", "credentials", "autoEncrypter", "connect", "MonitorInterval", "monitorServer", "immediate", "requestCheck", "has", "wake", "reset", "topologyVersion", "resetMonitorState", "close", "emit", "roundTripTime", "average", "minRoundTripTime", "min", "latestRtt", "last", "addRttSample", "rtt", "addSample", "clearRttSamples", "clear", "stop", "rttPinger", "destroy", "useStreamingProtocol", "checkServer", "callback", "start", "awaited", "isAwaitable", "emitAndLogHeartbeat", "Server", "SERVER_HEARTBEAT_STARTED", "ServerHeartbeatStartedEvent", "onHeartbeatFailed", "err", "SERVER_HEARTBEAT_FAILED", "ServerHeartbeatFailedEvent", "calculateDurationInMs", "error", "MongoError", "buildErrorMessage", "cause", "addErrorLabel", "MongoErrorLabel", "ResetPool", "MongoNetworkTimeoutError", "InterruptInUseConnections", "onHeartbeatSucceeded", "hello", "isWritablePrimary", "LEGACY_HELLO_COMMAND", "duration", "SERVER_HEARTBEAT_SUCCEEDED", "connectionId", "ServerHeartbeatSucceededEvent", "now", "closed", "serverApi", "helloOk", "maxAwaitTimeMS", "cmd", "version", "makeTopologyVersion", "socketTimeoutMS", "exhaustAllowed", "<PERSON><PERSON><PERSON><PERSON>", "exhaustCommand", "ns", "command", "then", "socket", "makeSocket", "makeConnection", "performInitialHandshake", "process", "nextTick", "done", "type", "ServerType", "Unknown", "setTimeout", "tv", "processId", "counter", "<PERSON>", "isLong", "fromNumber", "measureRoundTripTime", "clearTimeout", "measureAndReschedule", "conn", "commandName", "fn", "isExpeditedCallToFnScheduled", "stopped", "isExecutionInProgress", "hasExecutedOnce", "_executeAndReschedule", "timerId", "lastExecutionEnded", "_reschedule", "currentTime", "timeSinceLastCall", "toString", "JSON", "stringify", "toJSON", "lastCallTime", "isExpeditedCheckScheduled", "ms", "windowSize", "rttSamples", "Float64Array", "length", "writeIndex", "sample", "i", "sum"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\sdam\\monitor.ts"], "sourcesContent": ["import { clearTimeout, setTimeout } from 'timers';\n\nimport { type Document, Long } from '../bson';\nimport { connect, makeConnection, makeSocket, performInitialHandshake } from '../cmap/connect';\nimport type { Connection, ConnectionOptions } from '../cmap/connection';\nimport { getFAASEnv } from '../cmap/handshake/client_metadata';\nimport { LEGACY_HELLO_COMMAND } from '../constants';\nimport { MongoError, MongoErrorLabel, MongoNetworkTimeoutError } from '../error';\nimport { MongoLoggableComponent } from '../mongo_logger';\nimport { CancellationToken, TypedEventEmitter } from '../mongo_types';\nimport {\n  calculateDurationInMs,\n  type Callback,\n  type EventEmitterWithState,\n  makeStateMachine,\n  noop,\n  now,\n  ns\n} from '../utils';\nimport { ServerType, STATE_CLOSED, STATE_CLOSING } from './common';\nimport {\n  ServerHeartbeatFailedEvent,\n  ServerHear<PERSON>beatStartedEvent,\n  ServerHeartbeatSucceededEvent\n} from './events';\nimport { Server } from './server';\nimport type { TopologyVersion } from './server_description';\n\nconst STATE_IDLE = 'idle';\nconst STATE_MONITORING = 'monitoring';\nconst stateTransition = makeStateMachine({\n  [STATE_CLOSING]: [STATE_CLOSING, STATE_IDLE, STATE_CLOSED],\n  [STATE_CLOSED]: [STATE_CLOSED, STATE_MONITORING],\n  [STATE_IDLE]: [STATE_IDLE, STATE_MONITORING, STATE_CLOSING],\n  [STATE_MONITORING]: [STATE_MONITORING, STATE_IDLE, STATE_CLOSING]\n});\n\nconst INVALID_REQUEST_CHECK_STATES = new Set([STATE_CLOSING, STATE_CLOSED, STATE_MONITORING]);\nfunction isInCloseState(monitor: Monitor) {\n  return monitor.s.state === STATE_CLOSED || monitor.s.state === STATE_CLOSING;\n}\n\n/** @public */\nexport const ServerMonitoringMode = Object.freeze({\n  auto: 'auto',\n  poll: 'poll',\n  stream: 'stream'\n} as const);\n\n/** @public */\nexport type ServerMonitoringMode = (typeof ServerMonitoringMode)[keyof typeof ServerMonitoringMode];\n\n/** @internal */\nexport interface MonitorPrivate {\n  state: string;\n}\n\n/** @public */\nexport interface MonitorOptions\n  extends Omit<ConnectionOptions, 'id' | 'generation' | 'hostAddress'> {\n  connectTimeoutMS: number;\n  heartbeatFrequencyMS: number;\n  minHeartbeatFrequencyMS: number;\n  serverMonitoringMode: ServerMonitoringMode;\n}\n\n/** @public */\nexport type MonitorEvents = {\n  serverHeartbeatStarted(event: ServerHeartbeatStartedEvent): void;\n  serverHeartbeatSucceeded(event: ServerHeartbeatSucceededEvent): void;\n  serverHeartbeatFailed(event: ServerHeartbeatFailedEvent): void;\n  resetServer(error?: MongoError): void;\n  resetConnectionPool(): void;\n  close(): void;\n} & EventEmitterWithState;\n\n/** @internal */\nexport class Monitor extends TypedEventEmitter<MonitorEvents> {\n  /** @internal */\n  s: MonitorPrivate;\n  address: string;\n  options: Readonly<\n    Pick<\n      MonitorOptions,\n      | 'connectTimeoutMS'\n      | 'heartbeatFrequencyMS'\n      | 'minHeartbeatFrequencyMS'\n      | 'serverMonitoringMode'\n    >\n  >;\n  connectOptions: ConnectionOptions;\n  isRunningInFaasEnv: boolean;\n  server: Server;\n  connection: Connection | null;\n  cancellationToken: CancellationToken;\n  /** @internal */\n  monitorId?: MonitorInterval;\n  rttPinger?: RTTPinger;\n  /** @internal */\n  override component = MongoLoggableComponent.TOPOLOGY;\n  /** @internal */\n  private rttSampler: RTTSampler;\n\n  constructor(server: Server, options: MonitorOptions) {\n    super();\n    this.on('error', noop);\n\n    this.server = server;\n    this.connection = null;\n    this.cancellationToken = new CancellationToken();\n    this.cancellationToken.setMaxListeners(Infinity);\n    this.monitorId = undefined;\n    this.s = {\n      state: STATE_CLOSED\n    };\n    this.address = server.description.address;\n    this.options = Object.freeze({\n      connectTimeoutMS: options.connectTimeoutMS ?? 10000,\n      heartbeatFrequencyMS: options.heartbeatFrequencyMS ?? 10000,\n      minHeartbeatFrequencyMS: options.minHeartbeatFrequencyMS ?? 500,\n      serverMonitoringMode: options.serverMonitoringMode\n    });\n    this.isRunningInFaasEnv = getFAASEnv() != null;\n    this.mongoLogger = this.server.topology.client?.mongoLogger;\n    this.rttSampler = new RTTSampler(10);\n\n    const cancellationToken = this.cancellationToken;\n    // TODO: refactor this to pull it directly from the pool, requires new ConnectionPool integration\n    const connectOptions = {\n      id: '<monitor>' as const,\n      generation: server.pool.generation,\n      cancellationToken,\n      hostAddress: server.description.hostAddress,\n      ...options,\n      // force BSON serialization options\n      raw: false,\n      useBigInt64: false,\n      promoteLongs: true,\n      promoteValues: true,\n      promoteBuffers: true\n    };\n\n    // ensure no authentication is used for monitoring\n    delete connectOptions.credentials;\n    if (connectOptions.autoEncrypter) {\n      delete connectOptions.autoEncrypter;\n    }\n\n    this.connectOptions = Object.freeze(connectOptions);\n  }\n\n  connect(): void {\n    if (this.s.state !== STATE_CLOSED) {\n      return;\n    }\n\n    // start\n    const heartbeatFrequencyMS = this.options.heartbeatFrequencyMS;\n    const minHeartbeatFrequencyMS = this.options.minHeartbeatFrequencyMS;\n    this.monitorId = new MonitorInterval(monitorServer(this), {\n      heartbeatFrequencyMS: heartbeatFrequencyMS,\n      minHeartbeatFrequencyMS: minHeartbeatFrequencyMS,\n      immediate: true\n    });\n  }\n\n  requestCheck(): void {\n    if (INVALID_REQUEST_CHECK_STATES.has(this.s.state)) {\n      return;\n    }\n\n    this.monitorId?.wake();\n  }\n\n  reset(): void {\n    const topologyVersion = this.server.description.topologyVersion;\n    if (isInCloseState(this) || topologyVersion == null) {\n      return;\n    }\n\n    stateTransition(this, STATE_CLOSING);\n    resetMonitorState(this);\n\n    // restart monitor\n    stateTransition(this, STATE_IDLE);\n\n    // restart monitoring\n    const heartbeatFrequencyMS = this.options.heartbeatFrequencyMS;\n    const minHeartbeatFrequencyMS = this.options.minHeartbeatFrequencyMS;\n    this.monitorId = new MonitorInterval(monitorServer(this), {\n      heartbeatFrequencyMS: heartbeatFrequencyMS,\n      minHeartbeatFrequencyMS: minHeartbeatFrequencyMS\n    });\n  }\n\n  close(): void {\n    if (isInCloseState(this)) {\n      return;\n    }\n\n    stateTransition(this, STATE_CLOSING);\n    resetMonitorState(this);\n\n    // close monitor\n    this.emit('close');\n    stateTransition(this, STATE_CLOSED);\n  }\n\n  get roundTripTime(): number {\n    return this.rttSampler.average();\n  }\n\n  get minRoundTripTime(): number {\n    return this.rttSampler.min();\n  }\n\n  get latestRtt(): number | null {\n    return this.rttSampler.last;\n  }\n\n  addRttSample(rtt: number) {\n    this.rttSampler.addSample(rtt);\n  }\n\n  clearRttSamples() {\n    this.rttSampler.clear();\n  }\n}\n\nfunction resetMonitorState(monitor: Monitor) {\n  monitor.monitorId?.stop();\n  monitor.monitorId = undefined;\n\n  monitor.rttPinger?.close();\n  monitor.rttPinger = undefined;\n\n  monitor.cancellationToken.emit('cancel');\n\n  monitor.connection?.destroy();\n  monitor.connection = null;\n\n  monitor.clearRttSamples();\n}\n\nfunction useStreamingProtocol(monitor: Monitor, topologyVersion: TopologyVersion | null): boolean {\n  // If we have no topology version we always poll no matter\n  // what the user provided, since the server does not support\n  // the streaming protocol.\n  if (topologyVersion == null) return false;\n\n  const serverMonitoringMode = monitor.options.serverMonitoringMode;\n  if (serverMonitoringMode === ServerMonitoringMode.poll) return false;\n  if (serverMonitoringMode === ServerMonitoringMode.stream) return true;\n\n  // If we are in auto mode, we need to figure out if we're in a FaaS\n  // environment or not and choose the appropriate mode.\n  if (monitor.isRunningInFaasEnv) return false;\n  return true;\n}\n\nfunction checkServer(monitor: Monitor, callback: Callback<Document | null>) {\n  let start: number;\n  let awaited: boolean;\n  const topologyVersion = monitor.server.description.topologyVersion;\n  const isAwaitable = useStreamingProtocol(monitor, topologyVersion);\n  monitor.emitAndLogHeartbeat(\n    Server.SERVER_HEARTBEAT_STARTED,\n    monitor.server.topology.s.id,\n    undefined,\n    new ServerHeartbeatStartedEvent(monitor.address, isAwaitable)\n  );\n\n  function onHeartbeatFailed(err: Error) {\n    monitor.connection?.destroy();\n    monitor.connection = null;\n    monitor.emitAndLogHeartbeat(\n      Server.SERVER_HEARTBEAT_FAILED,\n      monitor.server.topology.s.id,\n      undefined,\n      new ServerHeartbeatFailedEvent(monitor.address, calculateDurationInMs(start), err, awaited)\n    );\n\n    const error = !(err instanceof MongoError)\n      ? new MongoError(MongoError.buildErrorMessage(err), { cause: err })\n      : err;\n    error.addErrorLabel(MongoErrorLabel.ResetPool);\n    if (error instanceof MongoNetworkTimeoutError) {\n      error.addErrorLabel(MongoErrorLabel.InterruptInUseConnections);\n    }\n\n    monitor.emit('resetServer', error);\n    callback(err);\n  }\n\n  function onHeartbeatSucceeded(hello: Document) {\n    if (!('isWritablePrimary' in hello)) {\n      // Provide hello-style response document.\n      hello.isWritablePrimary = hello[LEGACY_HELLO_COMMAND];\n    }\n\n    // NOTE: here we use the latestRtt as this measurement corresponds with the value\n    // obtained for this successful heartbeat, if there is no latestRtt, then we calculate the\n    // duration\n    const duration =\n      isAwaitable && monitor.rttPinger\n        ? (monitor.rttPinger.latestRtt ?? calculateDurationInMs(start))\n        : calculateDurationInMs(start);\n\n    monitor.addRttSample(duration);\n\n    monitor.emitAndLogHeartbeat(\n      Server.SERVER_HEARTBEAT_SUCCEEDED,\n      monitor.server.topology.s.id,\n      hello.connectionId,\n      new ServerHeartbeatSucceededEvent(monitor.address, duration, hello, isAwaitable)\n    );\n\n    if (isAwaitable) {\n      // If we are using the streaming protocol then we immediately issue another 'started'\n      // event, otherwise the \"check\" is complete and return to the main monitor loop\n      monitor.emitAndLogHeartbeat(\n        Server.SERVER_HEARTBEAT_STARTED,\n        monitor.server.topology.s.id,\n        undefined,\n        new ServerHeartbeatStartedEvent(monitor.address, true)\n      );\n      // We have not actually sent an outgoing handshake, but when we get the next response we\n      // want the duration to reflect the time since we last heard from the server\n      start = now();\n    } else {\n      monitor.rttPinger?.close();\n      monitor.rttPinger = undefined;\n\n      callback(undefined, hello);\n    }\n  }\n\n  const { connection } = monitor;\n  if (connection && !connection.closed) {\n    const { serverApi, helloOk } = connection;\n    const connectTimeoutMS = monitor.options.connectTimeoutMS;\n    const maxAwaitTimeMS = monitor.options.heartbeatFrequencyMS;\n\n    const cmd = {\n      [serverApi?.version || helloOk ? 'hello' : LEGACY_HELLO_COMMAND]: 1,\n      ...(isAwaitable && topologyVersion\n        ? { maxAwaitTimeMS, topologyVersion: makeTopologyVersion(topologyVersion) }\n        : {})\n    };\n\n    const options = isAwaitable\n      ? {\n          socketTimeoutMS: connectTimeoutMS ? connectTimeoutMS + maxAwaitTimeMS : 0,\n          exhaustAllowed: true\n        }\n      : { socketTimeoutMS: connectTimeoutMS };\n\n    if (isAwaitable && monitor.rttPinger == null) {\n      monitor.rttPinger = new RTTPinger(monitor);\n    }\n\n    // Record new start time before sending handshake\n    start = now();\n\n    if (isAwaitable) {\n      awaited = true;\n      return connection.exhaustCommand(ns('admin.$cmd'), cmd, options, (error, hello) => {\n        if (error) return onHeartbeatFailed(error);\n        return onHeartbeatSucceeded(hello);\n      });\n    }\n\n    awaited = false;\n    connection\n      .command(ns('admin.$cmd'), cmd, options)\n      .then(onHeartbeatSucceeded, onHeartbeatFailed);\n\n    return;\n  }\n\n  // connecting does an implicit `hello`\n  (async () => {\n    const socket = await makeSocket(monitor.connectOptions);\n    const connection = makeConnection(monitor.connectOptions, socket);\n    // The start time is after socket creation but before the handshake\n    start = now();\n    try {\n      await performInitialHandshake(connection, monitor.connectOptions);\n      return connection;\n    } catch (error) {\n      connection.destroy();\n      throw error;\n    }\n  })().then(\n    connection => {\n      if (isInCloseState(monitor)) {\n        connection.destroy();\n        return;\n      }\n      const duration = calculateDurationInMs(start);\n      monitor.addRttSample(duration);\n\n      monitor.connection = connection;\n      monitor.emitAndLogHeartbeat(\n        Server.SERVER_HEARTBEAT_SUCCEEDED,\n        monitor.server.topology.s.id,\n        connection.hello?.connectionId,\n        new ServerHeartbeatSucceededEvent(\n          monitor.address,\n          duration,\n          connection.hello,\n          useStreamingProtocol(monitor, connection.hello?.topologyVersion)\n        )\n      );\n\n      callback(undefined, connection.hello);\n    },\n    error => {\n      monitor.connection = null;\n      awaited = false;\n      onHeartbeatFailed(error);\n    }\n  );\n}\n\nfunction monitorServer(monitor: Monitor) {\n  return (callback: Callback) => {\n    if (monitor.s.state === STATE_MONITORING) {\n      process.nextTick(callback);\n      return;\n    }\n    stateTransition(monitor, STATE_MONITORING);\n    function done() {\n      if (!isInCloseState(monitor)) {\n        stateTransition(monitor, STATE_IDLE);\n      }\n\n      callback();\n    }\n\n    checkServer(monitor, (err, hello) => {\n      if (err) {\n        // otherwise an error occurred on initial discovery, also bail\n        if (monitor.server.description.type === ServerType.Unknown) {\n          return done();\n        }\n      }\n\n      // if the check indicates streaming is supported, immediately reschedule monitoring\n      if (useStreamingProtocol(monitor, hello?.topologyVersion)) {\n        setTimeout(() => {\n          if (!isInCloseState(monitor)) {\n            monitor.monitorId?.wake();\n          }\n        }, 0);\n      }\n\n      done();\n    });\n  };\n}\n\nfunction makeTopologyVersion(tv: TopologyVersion) {\n  return {\n    processId: tv.processId,\n    // tests mock counter as just number, but in a real situation counter should always be a Long\n    // TODO(NODE-2674): Preserve int64 sent from MongoDB\n    counter: Long.isLong(tv.counter) ? tv.counter : Long.fromNumber(tv.counter)\n  };\n}\n\n/** @internal */\nexport interface RTTPingerOptions extends ConnectionOptions {\n  heartbeatFrequencyMS: number;\n}\n\n/** @internal */\nexport class RTTPinger {\n  connection?: Connection;\n  /** @internal */\n  cancellationToken: CancellationToken;\n  /** @internal */\n  monitorId: NodeJS.Timeout;\n  /** @internal */\n  monitor: Monitor;\n  closed: boolean;\n  /** @internal */\n  latestRtt?: number;\n\n  constructor(monitor: Monitor) {\n    this.connection = undefined;\n    this.cancellationToken = monitor.cancellationToken;\n    this.closed = false;\n    this.monitor = monitor;\n    this.latestRtt = monitor.latestRtt ?? undefined;\n\n    const heartbeatFrequencyMS = monitor.options.heartbeatFrequencyMS;\n    this.monitorId = setTimeout(() => this.measureRoundTripTime(), heartbeatFrequencyMS);\n  }\n\n  get roundTripTime(): number {\n    return this.monitor.roundTripTime;\n  }\n\n  get minRoundTripTime(): number {\n    return this.monitor.minRoundTripTime;\n  }\n\n  close(): void {\n    this.closed = true;\n    clearTimeout(this.monitorId);\n\n    this.connection?.destroy();\n    this.connection = undefined;\n  }\n\n  private measureAndReschedule(start: number, conn?: Connection) {\n    if (this.closed) {\n      conn?.destroy();\n      return;\n    }\n\n    if (this.connection == null) {\n      this.connection = conn;\n    }\n\n    this.latestRtt = calculateDurationInMs(start);\n    this.monitorId = setTimeout(\n      () => this.measureRoundTripTime(),\n      this.monitor.options.heartbeatFrequencyMS\n    );\n  }\n\n  private measureRoundTripTime() {\n    const start = now();\n\n    if (this.closed) {\n      return;\n    }\n\n    const connection = this.connection;\n    if (connection == null) {\n      connect(this.monitor.connectOptions).then(\n        connection => {\n          this.measureAndReschedule(start, connection);\n        },\n        () => {\n          this.connection = undefined;\n        }\n      );\n      return;\n    }\n\n    const commandName =\n      connection.serverApi?.version || connection.helloOk ? 'hello' : LEGACY_HELLO_COMMAND;\n\n    connection.command(ns('admin.$cmd'), { [commandName]: 1 }, undefined).then(\n      () => this.measureAndReschedule(start),\n      () => {\n        this.connection?.destroy();\n        this.connection = undefined;\n        return;\n      }\n    );\n  }\n}\n\n/**\n * @internal\n */\nexport interface MonitorIntervalOptions {\n  /** The interval to execute a method on */\n  heartbeatFrequencyMS: number;\n  /** A minimum interval that must elapse before the method is called */\n  minHeartbeatFrequencyMS: number;\n  /** Whether the method should be called immediately when the interval is started  */\n  immediate: boolean;\n}\n\n/**\n * @internal\n */\nexport class MonitorInterval {\n  fn: (callback: Callback) => void;\n  timerId: NodeJS.Timeout | undefined;\n  lastExecutionEnded: number;\n  isExpeditedCallToFnScheduled = false;\n  stopped = false;\n  isExecutionInProgress = false;\n  hasExecutedOnce = false;\n\n  heartbeatFrequencyMS: number;\n  minHeartbeatFrequencyMS: number;\n\n  constructor(fn: (callback: Callback) => void, options: Partial<MonitorIntervalOptions> = {}) {\n    this.fn = fn;\n    this.lastExecutionEnded = -Infinity;\n\n    this.heartbeatFrequencyMS = options.heartbeatFrequencyMS ?? 1000;\n    this.minHeartbeatFrequencyMS = options.minHeartbeatFrequencyMS ?? 500;\n\n    if (options.immediate) {\n      this._executeAndReschedule();\n    } else {\n      this._reschedule(undefined);\n    }\n  }\n\n  wake() {\n    const currentTime = now();\n    const timeSinceLastCall = currentTime - this.lastExecutionEnded;\n\n    // TODO(NODE-4674): Add error handling and logging to the monitor\n    if (timeSinceLastCall < 0) {\n      return this._executeAndReschedule();\n    }\n\n    if (this.isExecutionInProgress) {\n      return;\n    }\n\n    // debounce multiple calls to wake within the `minInterval`\n    if (this.isExpeditedCallToFnScheduled) {\n      return;\n    }\n\n    // reschedule a call as soon as possible, ensuring the call never happens\n    // faster than the `minInterval`\n    if (timeSinceLastCall < this.minHeartbeatFrequencyMS) {\n      this.isExpeditedCallToFnScheduled = true;\n      this._reschedule(this.minHeartbeatFrequencyMS - timeSinceLastCall);\n      return;\n    }\n\n    this._executeAndReschedule();\n  }\n\n  stop() {\n    this.stopped = true;\n    if (this.timerId) {\n      clearTimeout(this.timerId);\n      this.timerId = undefined;\n    }\n\n    this.lastExecutionEnded = -Infinity;\n    this.isExpeditedCallToFnScheduled = false;\n  }\n\n  toString() {\n    return JSON.stringify(this);\n  }\n\n  toJSON() {\n    const currentTime = now();\n    const timeSinceLastCall = currentTime - this.lastExecutionEnded;\n    return {\n      timerId: this.timerId != null ? 'set' : 'cleared',\n      lastCallTime: this.lastExecutionEnded,\n      isExpeditedCheckScheduled: this.isExpeditedCallToFnScheduled,\n      stopped: this.stopped,\n      heartbeatFrequencyMS: this.heartbeatFrequencyMS,\n      minHeartbeatFrequencyMS: this.minHeartbeatFrequencyMS,\n      currentTime,\n      timeSinceLastCall\n    };\n  }\n\n  private _reschedule(ms?: number) {\n    if (this.stopped) return;\n    if (this.timerId) {\n      clearTimeout(this.timerId);\n    }\n\n    this.timerId = setTimeout(this._executeAndReschedule, ms || this.heartbeatFrequencyMS);\n  }\n\n  private _executeAndReschedule = () => {\n    if (this.stopped) return;\n    if (this.timerId) {\n      clearTimeout(this.timerId);\n    }\n\n    this.isExpeditedCallToFnScheduled = false;\n    this.isExecutionInProgress = true;\n\n    this.fn(() => {\n      this.lastExecutionEnded = now();\n      this.isExecutionInProgress = false;\n      this._reschedule(this.heartbeatFrequencyMS);\n    });\n  };\n}\n\n/** @internal\n * This class implements the RTT sampling logic specified for [CSOT](https://github.com/mongodb/specifications/blob/bbb335e60cd7ea1e0f7cd9a9443cb95fc9d3b64d/source/client-side-operations-timeout/client-side-operations-timeout.md#drivers-use-minimum-rtt-to-short-circuit-operations)\n *\n * This is implemented as a [circular buffer](https://en.wikipedia.org/wiki/Circular_buffer) keeping\n * the most recent `windowSize` samples\n * */\nexport class RTTSampler {\n  /** Index of the next slot to be overwritten */\n  private writeIndex: number;\n  private length: number;\n  private rttSamples: Float64Array;\n\n  constructor(windowSize = 10) {\n    this.rttSamples = new Float64Array(windowSize);\n    this.length = 0;\n    this.writeIndex = 0;\n  }\n\n  /**\n   * Adds an rtt sample to the end of the circular buffer\n   * When `windowSize` samples have been collected, `addSample` overwrites the least recently added\n   * sample\n   */\n  addSample(sample: number) {\n    this.rttSamples[this.writeIndex++] = sample;\n    if (this.length < this.rttSamples.length) {\n      this.length++;\n    }\n\n    this.writeIndex %= this.rttSamples.length;\n  }\n\n  /**\n   * When \\< 2 samples have been collected, returns 0\n   * Otherwise computes the minimum value samples contained in the buffer\n   */\n  min(): number {\n    if (this.length < 2) return 0;\n    let min = this.rttSamples[0];\n    for (let i = 1; i < this.length; i++) {\n      if (this.rttSamples[i] < min) min = this.rttSamples[i];\n    }\n\n    return min;\n  }\n\n  /**\n   * Returns mean of samples contained in the buffer\n   */\n  average(): number {\n    if (this.length === 0) return 0;\n    let sum = 0;\n    for (let i = 0; i < this.length; i++) {\n      sum += this.rttSamples[i];\n    }\n\n    return sum / this.length;\n  }\n\n  /**\n   * Returns most recently inserted element in the buffer\n   * Returns null if the buffer is empty\n   * */\n  get last(): number | null {\n    if (this.length === 0) return null;\n    return this.rttSamples[this.writeIndex === 0 ? this.length - 1 : this.writeIndex - 1];\n  }\n\n  /**\n   * Clear the buffer\n   * NOTE: this does not overwrite the data held in the internal array, just the pointers into\n   * this array\n   */\n  clear() {\n    this.length = 0;\n    this.writeIndex = 0;\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,QAAA,GAAAC,OAAA;AAEA,MAAAC,MAAA,GAAAD,OAAA;AACA,MAAAE,SAAA,GAAAF,OAAA;AAEA,MAAAG,iBAAA,GAAAH,OAAA;AACA,MAAAI,WAAA,GAAAJ,OAAA;AACA,MAAAK,OAAA,GAAAL,OAAA;AACA,MAAAM,cAAA,GAAAN,OAAA;AACA,MAAAO,aAAA,GAAAP,OAAA;AACA,MAAAQ,OAAA,GAAAR,OAAA;AASA,MAAAS,QAAA,GAAAT,OAAA;AACA,MAAAU,QAAA,GAAAV,OAAA;AAKA,MAAAW,QAAA,GAAAX,OAAA;AAGA,MAAMY,UAAU,GAAG,MAAM;AACzB,MAAMC,gBAAgB,GAAG,YAAY;AACrC,MAAMC,eAAe,GAAG,IAAAN,OAAA,CAAAO,gBAAgB,EAAC;EACvC,CAACN,QAAA,CAAAO,aAAa,GAAG,CAACP,QAAA,CAAAO,aAAa,EAAEJ,UAAU,EAAEH,QAAA,CAAAQ,YAAY,CAAC;EAC1D,CAACR,QAAA,CAAAQ,YAAY,GAAG,CAACR,QAAA,CAAAQ,YAAY,EAAEJ,gBAAgB,CAAC;EAChD,CAACD,UAAU,GAAG,CAACA,UAAU,EAAEC,gBAAgB,EAAEJ,QAAA,CAAAO,aAAa,CAAC;EAC3D,CAACH,gBAAgB,GAAG,CAACA,gBAAgB,EAAED,UAAU,EAAEH,QAAA,CAAAO,aAAa;CACjE,CAAC;AAEF,MAAME,4BAA4B,GAAG,IAAIC,GAAG,CAAC,CAACV,QAAA,CAAAO,aAAa,EAAEP,QAAA,CAAAQ,YAAY,EAAEJ,gBAAgB,CAAC,CAAC;AAC7F,SAASO,cAAcA,CAACC,OAAgB;EACtC,OAAOA,OAAO,CAACC,CAAC,CAACC,KAAK,KAAKd,QAAA,CAAAQ,YAAY,IAAII,OAAO,CAACC,CAAC,CAACC,KAAK,KAAKd,QAAA,CAAAO,aAAa;AAC9E;AAEA;AACaQ,OAAA,CAAAC,oBAAoB,GAAGC,MAAM,CAACC,MAAM,CAAC;EAChDC,IAAI,EAAE,MAAM;EACZC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE;CACA,CAAC;AA6BX;AACA,MAAaC,OAAQ,SAAQxB,aAAA,CAAAyB,iBAAgC;EA0B3DC,YAAYC,MAAc,EAAEC,OAAuB;IACjD,KAAK,EAAE;IANT;IACS,KAAAC,SAAS,GAAG9B,cAAA,CAAA+B,sBAAsB,CAACC,QAAQ;IAMlD,IAAI,CAACC,EAAE,CAAC,OAAO,EAAE/B,OAAA,CAAAgC,IAAI,CAAC;IAEtB,IAAI,CAACN,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACO,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,iBAAiB,GAAG,IAAInC,aAAA,CAAAoC,iBAAiB,EAAE;IAChD,IAAI,CAACD,iBAAiB,CAACE,eAAe,CAACC,QAAQ,CAAC;IAChD,IAAI,CAACC,SAAS,GAAGC,SAAS;IAC1B,IAAI,CAACzB,CAAC,GAAG;MACPC,KAAK,EAAEd,QAAA,CAAAQ;KACR;IACD,IAAI,CAAC+B,OAAO,GAAGd,MAAM,CAACe,WAAW,CAACD,OAAO;IACzC,IAAI,CAACb,OAAO,GAAGT,MAAM,CAACC,MAAM,CAAC;MAC3BuB,gBAAgB,EAAEf,OAAO,CAACe,gBAAgB,IAAI,KAAK;MACnDC,oBAAoB,EAAEhB,OAAO,CAACgB,oBAAoB,IAAI,KAAK;MAC3DC,uBAAuB,EAAEjB,OAAO,CAACiB,uBAAuB,IAAI,GAAG;MAC/DC,oBAAoB,EAAElB,OAAO,CAACkB;KAC/B,CAAC;IACF,IAAI,CAACC,kBAAkB,GAAG,IAAAnD,iBAAA,CAAAoD,UAAU,GAAE,IAAI,IAAI;IAC9C,IAAI,CAACC,WAAW,GAAG,IAAI,CAACtB,MAAM,CAACuB,QAAQ,CAACC,MAAM,EAAEF,WAAW;IAC3D,IAAI,CAACG,UAAU,GAAG,IAAIC,UAAU,CAAC,EAAE,CAAC;IAEpC,MAAMlB,iBAAiB,GAAG,IAAI,CAACA,iBAAiB;IAChD;IACA,MAAMmB,cAAc,GAAG;MACrBC,EAAE,EAAE,WAAoB;MACxBC,UAAU,EAAE7B,MAAM,CAAC8B,IAAI,CAACD,UAAU;MAClCrB,iBAAiB;MACjBuB,WAAW,EAAE/B,MAAM,CAACe,WAAW,CAACgB,WAAW;MAC3C,GAAG9B,OAAO;MACV;MACA+B,GAAG,EAAE,KAAK;MACVC,WAAW,EAAE,KAAK;MAClBC,YAAY,EAAE,IAAI;MAClBC,aAAa,EAAE,IAAI;MACnBC,cAAc,EAAE;KACjB;IAED;IACA,OAAOT,cAAc,CAACU,WAAW;IACjC,IAAIV,cAAc,CAACW,aAAa,EAAE;MAChC,OAAOX,cAAc,CAACW,aAAa;IACrC;IAEA,IAAI,CAACX,cAAc,GAAGnC,MAAM,CAACC,MAAM,CAACkC,cAAc,CAAC;EACrD;EAEAY,OAAOA,CAAA;IACL,IAAI,IAAI,CAACnD,CAAC,CAACC,KAAK,KAAKd,QAAA,CAAAQ,YAAY,EAAE;MACjC;IACF;IAEA;IACA,MAAMkC,oBAAoB,GAAG,IAAI,CAAChB,OAAO,CAACgB,oBAAoB;IAC9D,MAAMC,uBAAuB,GAAG,IAAI,CAACjB,OAAO,CAACiB,uBAAuB;IACpE,IAAI,CAACN,SAAS,GAAG,IAAI4B,eAAe,CAACC,aAAa,CAAC,IAAI,CAAC,EAAE;MACxDxB,oBAAoB,EAAEA,oBAAoB;MAC1CC,uBAAuB,EAAEA,uBAAuB;MAChDwB,SAAS,EAAE;KACZ,CAAC;EACJ;EAEAC,YAAYA,CAAA;IACV,IAAI3D,4BAA4B,CAAC4D,GAAG,CAAC,IAAI,CAACxD,CAAC,CAACC,KAAK,CAAC,EAAE;MAClD;IACF;IAEA,IAAI,CAACuB,SAAS,EAAEiC,IAAI,EAAE;EACxB;EAEAC,KAAKA,CAAA;IACH,MAAMC,eAAe,GAAG,IAAI,CAAC/C,MAAM,CAACe,WAAW,CAACgC,eAAe;IAC/D,IAAI7D,cAAc,CAAC,IAAI,CAAC,IAAI6D,eAAe,IAAI,IAAI,EAAE;MACnD;IACF;IAEAnE,eAAe,CAAC,IAAI,EAAEL,QAAA,CAAAO,aAAa,CAAC;IACpCkE,iBAAiB,CAAC,IAAI,CAAC;IAEvB;IACApE,eAAe,CAAC,IAAI,EAAEF,UAAU,CAAC;IAEjC;IACA,MAAMuC,oBAAoB,GAAG,IAAI,CAAChB,OAAO,CAACgB,oBAAoB;IAC9D,MAAMC,uBAAuB,GAAG,IAAI,CAACjB,OAAO,CAACiB,uBAAuB;IACpE,IAAI,CAACN,SAAS,GAAG,IAAI4B,eAAe,CAACC,aAAa,CAAC,IAAI,CAAC,EAAE;MACxDxB,oBAAoB,EAAEA,oBAAoB;MAC1CC,uBAAuB,EAAEA;KAC1B,CAAC;EACJ;EAEA+B,KAAKA,CAAA;IACH,IAAI/D,cAAc,CAAC,IAAI,CAAC,EAAE;MACxB;IACF;IAEAN,eAAe,CAAC,IAAI,EAAEL,QAAA,CAAAO,aAAa,CAAC;IACpCkE,iBAAiB,CAAC,IAAI,CAAC;IAEvB;IACA,IAAI,CAACE,IAAI,CAAC,OAAO,CAAC;IAClBtE,eAAe,CAAC,IAAI,EAAEL,QAAA,CAAAQ,YAAY,CAAC;EACrC;EAEA,IAAIoE,aAAaA,CAAA;IACf,OAAO,IAAI,CAAC1B,UAAU,CAAC2B,OAAO,EAAE;EAClC;EAEA,IAAIC,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAAC5B,UAAU,CAAC6B,GAAG,EAAE;EAC9B;EAEA,IAAIC,SAASA,CAAA;IACX,OAAO,IAAI,CAAC9B,UAAU,CAAC+B,IAAI;EAC7B;EAEAC,YAAYA,CAACC,GAAW;IACtB,IAAI,CAACjC,UAAU,CAACkC,SAAS,CAACD,GAAG,CAAC;EAChC;EAEAE,eAAeA,CAAA;IACb,IAAI,CAACnC,UAAU,CAACoC,KAAK,EAAE;EACzB;;AArJFvE,OAAA,CAAAO,OAAA,GAAAA,OAAA;AAwJA,SAASmD,iBAAiBA,CAAC7D,OAAgB;EACzCA,OAAO,CAACyB,SAAS,EAAEkD,IAAI,EAAE;EACzB3E,OAAO,CAACyB,SAAS,GAAGC,SAAS;EAE7B1B,OAAO,CAAC4E,SAAS,EAAEd,KAAK,EAAE;EAC1B9D,OAAO,CAAC4E,SAAS,GAAGlD,SAAS;EAE7B1B,OAAO,CAACqB,iBAAiB,CAAC0C,IAAI,CAAC,QAAQ,CAAC;EAExC/D,OAAO,CAACoB,UAAU,EAAEyD,OAAO,EAAE;EAC7B7E,OAAO,CAACoB,UAAU,GAAG,IAAI;EAEzBpB,OAAO,CAACyE,eAAe,EAAE;AAC3B;AAEA,SAASK,oBAAoBA,CAAC9E,OAAgB,EAAE4D,eAAuC;EACrF;EACA;EACA;EACA,IAAIA,eAAe,IAAI,IAAI,EAAE,OAAO,KAAK;EAEzC,MAAM5B,oBAAoB,GAAGhC,OAAO,CAACc,OAAO,CAACkB,oBAAoB;EACjE,IAAIA,oBAAoB,KAAK7B,OAAA,CAAAC,oBAAoB,CAACI,IAAI,EAAE,OAAO,KAAK;EACpE,IAAIwB,oBAAoB,KAAK7B,OAAA,CAAAC,oBAAoB,CAACK,MAAM,EAAE,OAAO,IAAI;EAErE;EACA;EACA,IAAIT,OAAO,CAACiC,kBAAkB,EAAE,OAAO,KAAK;EAC5C,OAAO,IAAI;AACb;AAEA,SAAS8C,WAAWA,CAAC/E,OAAgB,EAAEgF,QAAmC;EACxE,IAAIC,KAAa;EACjB,IAAIC,OAAgB;EACpB,MAAMtB,eAAe,GAAG5D,OAAO,CAACa,MAAM,CAACe,WAAW,CAACgC,eAAe;EAClE,MAAMuB,WAAW,GAAGL,oBAAoB,CAAC9E,OAAO,EAAE4D,eAAe,CAAC;EAClE5D,OAAO,CAACoF,mBAAmB,CACzB9F,QAAA,CAAA+F,MAAM,CAACC,wBAAwB,EAC/BtF,OAAO,CAACa,MAAM,CAACuB,QAAQ,CAACnC,CAAC,CAACwC,EAAE,EAC5Bf,SAAS,EACT,IAAIrC,QAAA,CAAAkG,2BAA2B,CAACvF,OAAO,CAAC2B,OAAO,EAAEwD,WAAW,CAAC,CAC9D;EAED,SAASK,iBAAiBA,CAACC,GAAU;IACnCzF,OAAO,CAACoB,UAAU,EAAEyD,OAAO,EAAE;IAC7B7E,OAAO,CAACoB,UAAU,GAAG,IAAI;IACzBpB,OAAO,CAACoF,mBAAmB,CACzB9F,QAAA,CAAA+F,MAAM,CAACK,uBAAuB,EAC9B1F,OAAO,CAACa,MAAM,CAACuB,QAAQ,CAACnC,CAAC,CAACwC,EAAE,EAC5Bf,SAAS,EACT,IAAIrC,QAAA,CAAAsG,0BAA0B,CAAC3F,OAAO,CAAC2B,OAAO,EAAE,IAAAxC,OAAA,CAAAyG,qBAAqB,EAACX,KAAK,CAAC,EAAEQ,GAAG,EAAEP,OAAO,CAAC,CAC5F;IAED,MAAMW,KAAK,GAAG,EAAEJ,GAAG,YAAYzG,OAAA,CAAA8G,UAAU,CAAC,GACtC,IAAI9G,OAAA,CAAA8G,UAAU,CAAC9G,OAAA,CAAA8G,UAAU,CAACC,iBAAiB,CAACN,GAAG,CAAC,EAAE;MAAEO,KAAK,EAAEP;IAAG,CAAE,CAAC,GACjEA,GAAG;IACPI,KAAK,CAACI,aAAa,CAACjH,OAAA,CAAAkH,eAAe,CAACC,SAAS,CAAC;IAC9C,IAAIN,KAAK,YAAY7G,OAAA,CAAAoH,wBAAwB,EAAE;MAC7CP,KAAK,CAACI,aAAa,CAACjH,OAAA,CAAAkH,eAAe,CAACG,yBAAyB,CAAC;IAChE;IAEArG,OAAO,CAAC+D,IAAI,CAAC,aAAa,EAAE8B,KAAK,CAAC;IAClCb,QAAQ,CAACS,GAAG,CAAC;EACf;EAEA,SAASa,oBAAoBA,CAACC,KAAe;IAC3C,IAAI,EAAE,mBAAmB,IAAIA,KAAK,CAAC,EAAE;MACnC;MACAA,KAAK,CAACC,iBAAiB,GAAGD,KAAK,CAACxH,WAAA,CAAA0H,oBAAoB,CAAC;IACvD;IAEA;IACA;IACA;IACA,MAAMC,QAAQ,GACZvB,WAAW,IAAInF,OAAO,CAAC4E,SAAS,GAC3B5E,OAAO,CAAC4E,SAAS,CAACR,SAAS,IAAI,IAAAjF,OAAA,CAAAyG,qBAAqB,EAACX,KAAK,CAAC,GAC5D,IAAA9F,OAAA,CAAAyG,qBAAqB,EAACX,KAAK,CAAC;IAElCjF,OAAO,CAACsE,YAAY,CAACoC,QAAQ,CAAC;IAE9B1G,OAAO,CAACoF,mBAAmB,CACzB9F,QAAA,CAAA+F,MAAM,CAACsB,0BAA0B,EACjC3G,OAAO,CAACa,MAAM,CAACuB,QAAQ,CAACnC,CAAC,CAACwC,EAAE,EAC5B8D,KAAK,CAACK,YAAY,EAClB,IAAIvH,QAAA,CAAAwH,6BAA6B,CAAC7G,OAAO,CAAC2B,OAAO,EAAE+E,QAAQ,EAAEH,KAAK,EAAEpB,WAAW,CAAC,CACjF;IAED,IAAIA,WAAW,EAAE;MACf;MACA;MACAnF,OAAO,CAACoF,mBAAmB,CACzB9F,QAAA,CAAA+F,MAAM,CAACC,wBAAwB,EAC/BtF,OAAO,CAACa,MAAM,CAACuB,QAAQ,CAACnC,CAAC,CAACwC,EAAE,EAC5Bf,SAAS,EACT,IAAIrC,QAAA,CAAAkG,2BAA2B,CAACvF,OAAO,CAAC2B,OAAO,EAAE,IAAI,CAAC,CACvD;MACD;MACA;MACAsD,KAAK,GAAG,IAAA9F,OAAA,CAAA2H,GAAG,GAAE;IACf,CAAC,MAAM;MACL9G,OAAO,CAAC4E,SAAS,EAAEd,KAAK,EAAE;MAC1B9D,OAAO,CAAC4E,SAAS,GAAGlD,SAAS;MAE7BsD,QAAQ,CAACtD,SAAS,EAAE6E,KAAK,CAAC;IAC5B;EACF;EAEA,MAAM;IAAEnF;EAAU,CAAE,GAAGpB,OAAO;EAC9B,IAAIoB,UAAU,IAAI,CAACA,UAAU,CAAC2F,MAAM,EAAE;IACpC,MAAM;MAAEC,SAAS;MAAEC;IAAO,CAAE,GAAG7F,UAAU;IACzC,MAAMS,gBAAgB,GAAG7B,OAAO,CAACc,OAAO,CAACe,gBAAgB;IACzD,MAAMqF,cAAc,GAAGlH,OAAO,CAACc,OAAO,CAACgB,oBAAoB;IAE3D,MAAMqF,GAAG,GAAG;MACV,CAACH,SAAS,EAAEI,OAAO,IAAIH,OAAO,GAAG,OAAO,GAAGlI,WAAA,CAAA0H,oBAAoB,GAAG,CAAC;MACnE,IAAItB,WAAW,IAAIvB,eAAe,GAC9B;QAAEsD,cAAc;QAAEtD,eAAe,EAAEyD,mBAAmB,CAACzD,eAAe;MAAC,CAAE,GACzE,EAAE;KACP;IAED,MAAM9C,OAAO,GAAGqE,WAAW,GACvB;MACEmC,eAAe,EAAEzF,gBAAgB,GAAGA,gBAAgB,GAAGqF,cAAc,GAAG,CAAC;MACzEK,cAAc,EAAE;KACjB,GACD;MAAED,eAAe,EAAEzF;IAAgB,CAAE;IAEzC,IAAIsD,WAAW,IAAInF,OAAO,CAAC4E,SAAS,IAAI,IAAI,EAAE;MAC5C5E,OAAO,CAAC4E,SAAS,GAAG,IAAI4C,SAAS,CAACxH,OAAO,CAAC;IAC5C;IAEA;IACAiF,KAAK,GAAG,IAAA9F,OAAA,CAAA2H,GAAG,GAAE;IAEb,IAAI3B,WAAW,EAAE;MACfD,OAAO,GAAG,IAAI;MACd,OAAO9D,UAAU,CAACqG,cAAc,CAAC,IAAAtI,OAAA,CAAAuI,EAAE,EAAC,YAAY,CAAC,EAAEP,GAAG,EAAErG,OAAO,EAAE,CAAC+E,KAAK,EAAEU,KAAK,KAAI;QAChF,IAAIV,KAAK,EAAE,OAAOL,iBAAiB,CAACK,KAAK,CAAC;QAC1C,OAAOS,oBAAoB,CAACC,KAAK,CAAC;MACpC,CAAC,CAAC;IACJ;IAEArB,OAAO,GAAG,KAAK;IACf9D,UAAU,CACPuG,OAAO,CAAC,IAAAxI,OAAA,CAAAuI,EAAE,EAAC,YAAY,CAAC,EAAEP,GAAG,EAAErG,OAAO,CAAC,CACvC8G,IAAI,CAACtB,oBAAoB,EAAEd,iBAAiB,CAAC;IAEhD;EACF;EAEA;EACA,CAAC,YAAW;IACV,MAAMqC,MAAM,GAAG,MAAM,IAAAhJ,SAAA,CAAAiJ,UAAU,EAAC9H,OAAO,CAACwC,cAAc,CAAC;IACvD,MAAMpB,UAAU,GAAG,IAAAvC,SAAA,CAAAkJ,cAAc,EAAC/H,OAAO,CAACwC,cAAc,EAAEqF,MAAM,CAAC;IACjE;IACA5C,KAAK,GAAG,IAAA9F,OAAA,CAAA2H,GAAG,GAAE;IACb,IAAI;MACF,MAAM,IAAAjI,SAAA,CAAAmJ,uBAAuB,EAAC5G,UAAU,EAAEpB,OAAO,CAACwC,cAAc,CAAC;MACjE,OAAOpB,UAAU;IACnB,CAAC,CAAC,OAAOyE,KAAK,EAAE;MACdzE,UAAU,CAACyD,OAAO,EAAE;MACpB,MAAMgB,KAAK;IACb;EACF,CAAC,EAAC,CAAE,CAAC+B,IAAI,CACPxG,UAAU,IAAG;IACX,IAAIrB,cAAc,CAACC,OAAO,CAAC,EAAE;MAC3BoB,UAAU,CAACyD,OAAO,EAAE;MACpB;IACF;IACA,MAAM6B,QAAQ,GAAG,IAAAvH,OAAA,CAAAyG,qBAAqB,EAACX,KAAK,CAAC;IAC7CjF,OAAO,CAACsE,YAAY,CAACoC,QAAQ,CAAC;IAE9B1G,OAAO,CAACoB,UAAU,GAAGA,UAAU;IAC/BpB,OAAO,CAACoF,mBAAmB,CACzB9F,QAAA,CAAA+F,MAAM,CAACsB,0BAA0B,EACjC3G,OAAO,CAACa,MAAM,CAACuB,QAAQ,CAACnC,CAAC,CAACwC,EAAE,EAC5BrB,UAAU,CAACmF,KAAK,EAAEK,YAAY,EAC9B,IAAIvH,QAAA,CAAAwH,6BAA6B,CAC/B7G,OAAO,CAAC2B,OAAO,EACf+E,QAAQ,EACRtF,UAAU,CAACmF,KAAK,EAChBzB,oBAAoB,CAAC9E,OAAO,EAAEoB,UAAU,CAACmF,KAAK,EAAE3C,eAAe,CAAC,CACjE,CACF;IAEDoB,QAAQ,CAACtD,SAAS,EAAEN,UAAU,CAACmF,KAAK,CAAC;EACvC,CAAC,EACDV,KAAK,IAAG;IACN7F,OAAO,CAACoB,UAAU,GAAG,IAAI;IACzB8D,OAAO,GAAG,KAAK;IACfM,iBAAiB,CAACK,KAAK,CAAC;EAC1B,CAAC,CACF;AACH;AAEA,SAASvC,aAAaA,CAACtD,OAAgB;EACrC,OAAQgF,QAAkB,IAAI;IAC5B,IAAIhF,OAAO,CAACC,CAAC,CAACC,KAAK,KAAKV,gBAAgB,EAAE;MACxCyI,OAAO,CAACC,QAAQ,CAAClD,QAAQ,CAAC;MAC1B;IACF;IACAvF,eAAe,CAACO,OAAO,EAAER,gBAAgB,CAAC;IAC1C,SAAS2I,IAAIA,CAAA;MACX,IAAI,CAACpI,cAAc,CAACC,OAAO,CAAC,EAAE;QAC5BP,eAAe,CAACO,OAAO,EAAET,UAAU,CAAC;MACtC;MAEAyF,QAAQ,EAAE;IACZ;IAEAD,WAAW,CAAC/E,OAAO,EAAE,CAACyF,GAAG,EAAEc,KAAK,KAAI;MAClC,IAAId,GAAG,EAAE;QACP;QACA,IAAIzF,OAAO,CAACa,MAAM,CAACe,WAAW,CAACwG,IAAI,KAAKhJ,QAAA,CAAAiJ,UAAU,CAACC,OAAO,EAAE;UAC1D,OAAOH,IAAI,EAAE;QACf;MACF;MAEA;MACA,IAAIrD,oBAAoB,CAAC9E,OAAO,EAAEuG,KAAK,EAAE3C,eAAe,CAAC,EAAE;QACzD,IAAAlF,QAAA,CAAA6J,UAAU,EAAC,MAAK;UACd,IAAI,CAACxI,cAAc,CAACC,OAAO,CAAC,EAAE;YAC5BA,OAAO,CAACyB,SAAS,EAAEiC,IAAI,EAAE;UAC3B;QACF,CAAC,EAAE,CAAC,CAAC;MACP;MAEAyE,IAAI,EAAE;IACR,CAAC,CAAC;EACJ,CAAC;AACH;AAEA,SAASd,mBAAmBA,CAACmB,EAAmB;EAC9C,OAAO;IACLC,SAAS,EAAED,EAAE,CAACC,SAAS;IACvB;IACA;IACAC,OAAO,EAAE9J,MAAA,CAAA+J,IAAI,CAACC,MAAM,CAACJ,EAAE,CAACE,OAAO,CAAC,GAAGF,EAAE,CAACE,OAAO,GAAG9J,MAAA,CAAA+J,IAAI,CAACE,UAAU,CAACL,EAAE,CAACE,OAAO;GAC3E;AACH;AAOA;AACA,MAAalB,SAAS;EAYpB5G,YAAYZ,OAAgB;IAC1B,IAAI,CAACoB,UAAU,GAAGM,SAAS;IAC3B,IAAI,CAACL,iBAAiB,GAAGrB,OAAO,CAACqB,iBAAiB;IAClD,IAAI,CAAC0F,MAAM,GAAG,KAAK;IACnB,IAAI,CAAC/G,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACoE,SAAS,GAAGpE,OAAO,CAACoE,SAAS,IAAI1C,SAAS;IAE/C,MAAMI,oBAAoB,GAAG9B,OAAO,CAACc,OAAO,CAACgB,oBAAoB;IACjE,IAAI,CAACL,SAAS,GAAG,IAAA/C,QAAA,CAAA6J,UAAU,EAAC,MAAM,IAAI,CAACO,oBAAoB,EAAE,EAAEhH,oBAAoB,CAAC;EACtF;EAEA,IAAIkC,aAAaA,CAAA;IACf,OAAO,IAAI,CAAChE,OAAO,CAACgE,aAAa;EACnC;EAEA,IAAIE,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAAClE,OAAO,CAACkE,gBAAgB;EACtC;EAEAJ,KAAKA,CAAA;IACH,IAAI,CAACiD,MAAM,GAAG,IAAI;IAClB,IAAArI,QAAA,CAAAqK,YAAY,EAAC,IAAI,CAACtH,SAAS,CAAC;IAE5B,IAAI,CAACL,UAAU,EAAEyD,OAAO,EAAE;IAC1B,IAAI,CAACzD,UAAU,GAAGM,SAAS;EAC7B;EAEQsH,oBAAoBA,CAAC/D,KAAa,EAAEgE,IAAiB;IAC3D,IAAI,IAAI,CAAClC,MAAM,EAAE;MACfkC,IAAI,EAAEpE,OAAO,EAAE;MACf;IACF;IAEA,IAAI,IAAI,CAACzD,UAAU,IAAI,IAAI,EAAE;MAC3B,IAAI,CAACA,UAAU,GAAG6H,IAAI;IACxB;IAEA,IAAI,CAAC7E,SAAS,GAAG,IAAAjF,OAAA,CAAAyG,qBAAqB,EAACX,KAAK,CAAC;IAC7C,IAAI,CAACxD,SAAS,GAAG,IAAA/C,QAAA,CAAA6J,UAAU,EACzB,MAAM,IAAI,CAACO,oBAAoB,EAAE,EACjC,IAAI,CAAC9I,OAAO,CAACc,OAAO,CAACgB,oBAAoB,CAC1C;EACH;EAEQgH,oBAAoBA,CAAA;IAC1B,MAAM7D,KAAK,GAAG,IAAA9F,OAAA,CAAA2H,GAAG,GAAE;IAEnB,IAAI,IAAI,CAACC,MAAM,EAAE;MACf;IACF;IAEA,MAAM3F,UAAU,GAAG,IAAI,CAACA,UAAU;IAClC,IAAIA,UAAU,IAAI,IAAI,EAAE;MACtB,IAAAvC,SAAA,CAAAuE,OAAO,EAAC,IAAI,CAACpD,OAAO,CAACwC,cAAc,CAAC,CAACoF,IAAI,CACvCxG,UAAU,IAAG;QACX,IAAI,CAAC4H,oBAAoB,CAAC/D,KAAK,EAAE7D,UAAU,CAAC;MAC9C,CAAC,EACD,MAAK;QACH,IAAI,CAACA,UAAU,GAAGM,SAAS;MAC7B,CAAC,CACF;MACD;IACF;IAEA,MAAMwH,WAAW,GACf9H,UAAU,CAAC4F,SAAS,EAAEI,OAAO,IAAIhG,UAAU,CAAC6F,OAAO,GAAG,OAAO,GAAGlI,WAAA,CAAA0H,oBAAoB;IAEtFrF,UAAU,CAACuG,OAAO,CAAC,IAAAxI,OAAA,CAAAuI,EAAE,EAAC,YAAY,CAAC,EAAE;MAAE,CAACwB,WAAW,GAAG;IAAC,CAAE,EAAExH,SAAS,CAAC,CAACkG,IAAI,CACxE,MAAM,IAAI,CAACoB,oBAAoB,CAAC/D,KAAK,CAAC,EACtC,MAAK;MACH,IAAI,CAAC7D,UAAU,EAAEyD,OAAO,EAAE;MAC1B,IAAI,CAACzD,UAAU,GAAGM,SAAS;MAC3B;IACF,CAAC,CACF;EACH;;AAvFFvB,OAAA,CAAAqH,SAAA,GAAAA,SAAA;AAsGA;;;AAGA,MAAanE,eAAe;EAY1BzC,YAAYuI,EAAgC,EAAErI,OAAA,GAA2C,EAAE;IAR3F,KAAAsI,4BAA4B,GAAG,KAAK;IACpC,KAAAC,OAAO,GAAG,KAAK;IACf,KAAAC,qBAAqB,GAAG,KAAK;IAC7B,KAAAC,eAAe,GAAG,KAAK;IAuFf,KAAAC,qBAAqB,GAAG,MAAK;MACnC,IAAI,IAAI,CAACH,OAAO,EAAE;MAClB,IAAI,IAAI,CAACI,OAAO,EAAE;QAChB,IAAA/K,QAAA,CAAAqK,YAAY,EAAC,IAAI,CAACU,OAAO,CAAC;MAC5B;MAEA,IAAI,CAACL,4BAA4B,GAAG,KAAK;MACzC,IAAI,CAACE,qBAAqB,GAAG,IAAI;MAEjC,IAAI,CAACH,EAAE,CAAC,MAAK;QACX,IAAI,CAACO,kBAAkB,GAAG,IAAAvK,OAAA,CAAA2H,GAAG,GAAE;QAC/B,IAAI,CAACwC,qBAAqB,GAAG,KAAK;QAClC,IAAI,CAACK,WAAW,CAAC,IAAI,CAAC7H,oBAAoB,CAAC;MAC7C,CAAC,CAAC;IACJ,CAAC;IA/FC,IAAI,CAACqH,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACO,kBAAkB,GAAG,CAAClI,QAAQ;IAEnC,IAAI,CAACM,oBAAoB,GAAGhB,OAAO,CAACgB,oBAAoB,IAAI,IAAI;IAChE,IAAI,CAACC,uBAAuB,GAAGjB,OAAO,CAACiB,uBAAuB,IAAI,GAAG;IAErE,IAAIjB,OAAO,CAACyC,SAAS,EAAE;MACrB,IAAI,CAACiG,qBAAqB,EAAE;IAC9B,CAAC,MAAM;MACL,IAAI,CAACG,WAAW,CAACjI,SAAS,CAAC;IAC7B;EACF;EAEAgC,IAAIA,CAAA;IACF,MAAMkG,WAAW,GAAG,IAAAzK,OAAA,CAAA2H,GAAG,GAAE;IACzB,MAAM+C,iBAAiB,GAAGD,WAAW,GAAG,IAAI,CAACF,kBAAkB;IAE/D;IACA,IAAIG,iBAAiB,GAAG,CAAC,EAAE;MACzB,OAAO,IAAI,CAACL,qBAAqB,EAAE;IACrC;IAEA,IAAI,IAAI,CAACF,qBAAqB,EAAE;MAC9B;IACF;IAEA;IACA,IAAI,IAAI,CAACF,4BAA4B,EAAE;MACrC;IACF;IAEA;IACA;IACA,IAAIS,iBAAiB,GAAG,IAAI,CAAC9H,uBAAuB,EAAE;MACpD,IAAI,CAACqH,4BAA4B,GAAG,IAAI;MACxC,IAAI,CAACO,WAAW,CAAC,IAAI,CAAC5H,uBAAuB,GAAG8H,iBAAiB,CAAC;MAClE;IACF;IAEA,IAAI,CAACL,qBAAqB,EAAE;EAC9B;EAEA7E,IAAIA,CAAA;IACF,IAAI,CAAC0E,OAAO,GAAG,IAAI;IACnB,IAAI,IAAI,CAACI,OAAO,EAAE;MAChB,IAAA/K,QAAA,CAAAqK,YAAY,EAAC,IAAI,CAACU,OAAO,CAAC;MAC1B,IAAI,CAACA,OAAO,GAAG/H,SAAS;IAC1B;IAEA,IAAI,CAACgI,kBAAkB,GAAG,CAAClI,QAAQ;IACnC,IAAI,CAAC4H,4BAA4B,GAAG,KAAK;EAC3C;EAEAU,QAAQA,CAAA;IACN,OAAOC,IAAI,CAACC,SAAS,CAAC,IAAI,CAAC;EAC7B;EAEAC,MAAMA,CAAA;IACJ,MAAML,WAAW,GAAG,IAAAzK,OAAA,CAAA2H,GAAG,GAAE;IACzB,MAAM+C,iBAAiB,GAAGD,WAAW,GAAG,IAAI,CAACF,kBAAkB;IAC/D,OAAO;MACLD,OAAO,EAAE,IAAI,CAACA,OAAO,IAAI,IAAI,GAAG,KAAK,GAAG,SAAS;MACjDS,YAAY,EAAE,IAAI,CAACR,kBAAkB;MACrCS,yBAAyB,EAAE,IAAI,CAACf,4BAA4B;MAC5DC,OAAO,EAAE,IAAI,CAACA,OAAO;MACrBvH,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;MAC/CC,uBAAuB,EAAE,IAAI,CAACA,uBAAuB;MACrD6H,WAAW;MACXC;KACD;EACH;EAEQF,WAAWA,CAACS,EAAW;IAC7B,IAAI,IAAI,CAACf,OAAO,EAAE;IAClB,IAAI,IAAI,CAACI,OAAO,EAAE;MAChB,IAAA/K,QAAA,CAAAqK,YAAY,EAAC,IAAI,CAACU,OAAO,CAAC;IAC5B;IAEA,IAAI,CAACA,OAAO,GAAG,IAAA/K,QAAA,CAAA6J,UAAU,EAAC,IAAI,CAACiB,qBAAqB,EAAEY,EAAE,IAAI,IAAI,CAACtI,oBAAoB,CAAC;EACxF;;AA5FF3B,OAAA,CAAAkD,eAAA,GAAAA,eAAA;AA+GA;;;;;;AAMA,MAAad,UAAU;EAMrB3B,YAAYyJ,UAAU,GAAG,EAAE;IACzB,IAAI,CAACC,UAAU,GAAG,IAAIC,YAAY,CAACF,UAAU,CAAC;IAC9C,IAAI,CAACG,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,UAAU,GAAG,CAAC;EACrB;EAEA;;;;;EAKAjG,SAASA,CAACkG,MAAc;IACtB,IAAI,CAACJ,UAAU,CAAC,IAAI,CAACG,UAAU,EAAE,CAAC,GAAGC,MAAM;IAC3C,IAAI,IAAI,CAACF,MAAM,GAAG,IAAI,CAACF,UAAU,CAACE,MAAM,EAAE;MACxC,IAAI,CAACA,MAAM,EAAE;IACf;IAEA,IAAI,CAACC,UAAU,IAAI,IAAI,CAACH,UAAU,CAACE,MAAM;EAC3C;EAEA;;;;EAIArG,GAAGA,CAAA;IACD,IAAI,IAAI,CAACqG,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC;IAC7B,IAAIrG,GAAG,GAAG,IAAI,CAACmG,UAAU,CAAC,CAAC,CAAC;IAC5B,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACH,MAAM,EAAEG,CAAC,EAAE,EAAE;MACpC,IAAI,IAAI,CAACL,UAAU,CAACK,CAAC,CAAC,GAAGxG,GAAG,EAAEA,GAAG,GAAG,IAAI,CAACmG,UAAU,CAACK,CAAC,CAAC;IACxD;IAEA,OAAOxG,GAAG;EACZ;EAEA;;;EAGAF,OAAOA,CAAA;IACL,IAAI,IAAI,CAACuG,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;IAC/B,IAAII,GAAG,GAAG,CAAC;IACX,KAAK,IAAID,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACH,MAAM,EAAEG,CAAC,EAAE,EAAE;MACpCC,GAAG,IAAI,IAAI,CAACN,UAAU,CAACK,CAAC,CAAC;IAC3B;IAEA,OAAOC,GAAG,GAAG,IAAI,CAACJ,MAAM;EAC1B;EAEA;;;;EAIA,IAAInG,IAAIA,CAAA;IACN,IAAI,IAAI,CAACmG,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAClC,OAAO,IAAI,CAACF,UAAU,CAAC,IAAI,CAACG,UAAU,KAAK,CAAC,GAAG,IAAI,CAACD,MAAM,GAAG,CAAC,GAAG,IAAI,CAACC,UAAU,GAAG,CAAC,CAAC;EACvF;EAEA;;;;;EAKA/F,KAAKA,CAAA;IACH,IAAI,CAAC8F,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,UAAU,GAAG,CAAC;EACrB;;AAtEFtK,OAAA,CAAAoC,UAAA,GAAAA,UAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}