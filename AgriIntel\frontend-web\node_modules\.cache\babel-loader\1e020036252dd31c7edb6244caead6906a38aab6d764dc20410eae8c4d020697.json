{"ast": null, "code": "import * as React from 'react';\nimport { localStorageAvailable } from '../../utils/utils';\nimport { useGridApiMethod } from '../utils';\nconst forceDebug = localStorageAvailable() && window.localStorage.getItem('DEBUG') != null;\nconst noop = () => {};\nconst noopLogger = {\n  debug: noop,\n  info: noop,\n  warn: noop,\n  error: noop\n};\nconst LOG_LEVELS = ['debug', 'info', 'warn', 'error'];\nfunction getAppender(name, logLevel, appender = console) {\n  const minLogLevelIdx = LOG_LEVELS.indexOf(logLevel);\n  if (minLogLevelIdx === -1) {\n    throw new Error(`MUI: Log level ${logLevel} not recognized.`);\n  }\n  const logger = LOG_LEVELS.reduce((loggerObj, method, idx) => {\n    if (idx >= minLogLevelIdx) {\n      loggerObj[method] = (...args) => {\n        const [message, ...other] = args;\n        appender[method](`MUI: ${name} - ${message}`, ...other);\n      };\n    } else {\n      loggerObj[method] = noop;\n    }\n    return loggerObj;\n  }, {});\n  return logger;\n}\nexport const useGridLoggerFactory = (apiRef, props) => {\n  const getLogger = React.useCallback(name => {\n    if (forceDebug) {\n      return getAppender(name, 'debug', props.logger);\n    }\n    if (!props.logLevel) {\n      return noopLogger;\n    }\n    return getAppender(name, props.logLevel.toString(), props.logger);\n  }, [props.logLevel, props.logger]);\n  useGridApiMethod(apiRef, {\n    getLogger\n  }, 'private');\n};", "map": {"version": 3, "names": ["React", "localStorageAvailable", "useGridApiMethod", "forceDebug", "window", "localStorage", "getItem", "noop", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "debug", "info", "warn", "error", "LOG_LEVELS", "get<PERSON><PERSON><PERSON>", "name", "logLevel", "appender", "console", "minLogLevelIdx", "indexOf", "Error", "logger", "reduce", "loggerObj", "method", "idx", "args", "message", "other", "useGridLoggerFactory", "apiRef", "props", "<PERSON><PERSON><PERSON><PERSON>", "useCallback", "toString"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/x-data-grid/hooks/core/useGridLoggerFactory.js"], "sourcesContent": ["import * as React from 'react';\nimport { localStorageAvailable } from '../../utils/utils';\nimport { useGridApiMethod } from '../utils';\nconst forceDebug = localStorageAvailable() && window.localStorage.getItem('DEBUG') != null;\nconst noop = () => {};\nconst noopLogger = {\n  debug: noop,\n  info: noop,\n  warn: noop,\n  error: noop\n};\nconst LOG_LEVELS = ['debug', 'info', 'warn', 'error'];\nfunction getAppender(name, logLevel, appender = console) {\n  const minLogLevelIdx = LOG_LEVELS.indexOf(logLevel);\n  if (minLogLevelIdx === -1) {\n    throw new Error(`MUI: Log level ${logLevel} not recognized.`);\n  }\n  const logger = LOG_LEVELS.reduce((loggerObj, method, idx) => {\n    if (idx >= minLogLevelIdx) {\n      loggerObj[method] = (...args) => {\n        const [message, ...other] = args;\n        appender[method](`MUI: ${name} - ${message}`, ...other);\n      };\n    } else {\n      loggerObj[method] = noop;\n    }\n    return loggerObj;\n  }, {});\n  return logger;\n}\nexport const useGridLoggerFactory = (apiRef, props) => {\n  const getLogger = React.useCallback(name => {\n    if (forceDebug) {\n      return getAppender(name, 'debug', props.logger);\n    }\n    if (!props.logLevel) {\n      return noopLogger;\n    }\n    return getAppender(name, props.logLevel.toString(), props.logger);\n  }, [props.logLevel, props.logger]);\n  useGridApiMethod(apiRef, {\n    getLogger\n  }, 'private');\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,qBAAqB,QAAQ,mBAAmB;AACzD,SAASC,gBAAgB,QAAQ,UAAU;AAC3C,MAAMC,UAAU,GAAGF,qBAAqB,CAAC,CAAC,IAAIG,MAAM,CAACC,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC,IAAI,IAAI;AAC1F,MAAMC,IAAI,GAAGA,CAAA,KAAM,CAAC,CAAC;AACrB,MAAMC,UAAU,GAAG;EACjBC,KAAK,EAAEF,IAAI;EACXG,IAAI,EAAEH,IAAI;EACVI,IAAI,EAAEJ,IAAI;EACVK,KAAK,EAAEL;AACT,CAAC;AACD,MAAMM,UAAU,GAAG,CAAC,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,CAAC;AACrD,SAASC,WAAWA,CAACC,IAAI,EAAEC,QAAQ,EAAEC,QAAQ,GAAGC,OAAO,EAAE;EACvD,MAAMC,cAAc,GAAGN,UAAU,CAACO,OAAO,CAACJ,QAAQ,CAAC;EACnD,IAAIG,cAAc,KAAK,CAAC,CAAC,EAAE;IACzB,MAAM,IAAIE,KAAK,CAAC,kBAAkBL,QAAQ,kBAAkB,CAAC;EAC/D;EACA,MAAMM,MAAM,GAAGT,UAAU,CAACU,MAAM,CAAC,CAACC,SAAS,EAAEC,MAAM,EAAEC,GAAG,KAAK;IAC3D,IAAIA,GAAG,IAAIP,cAAc,EAAE;MACzBK,SAAS,CAACC,MAAM,CAAC,GAAG,CAAC,GAAGE,IAAI,KAAK;QAC/B,MAAM,CAACC,OAAO,EAAE,GAAGC,KAAK,CAAC,GAAGF,IAAI;QAChCV,QAAQ,CAACQ,MAAM,CAAC,CAAC,QAAQV,IAAI,MAAMa,OAAO,EAAE,EAAE,GAAGC,KAAK,CAAC;MACzD,CAAC;IACH,CAAC,MAAM;MACLL,SAAS,CAACC,MAAM,CAAC,GAAGlB,IAAI;IAC1B;IACA,OAAOiB,SAAS;EAClB,CAAC,EAAE,CAAC,CAAC,CAAC;EACN,OAAOF,MAAM;AACf;AACA,OAAO,MAAMQ,oBAAoB,GAAGA,CAACC,MAAM,EAAEC,KAAK,KAAK;EACrD,MAAMC,SAAS,GAAGjC,KAAK,CAACkC,WAAW,CAACnB,IAAI,IAAI;IAC1C,IAAIZ,UAAU,EAAE;MACd,OAAOW,WAAW,CAACC,IAAI,EAAE,OAAO,EAAEiB,KAAK,CAACV,MAAM,CAAC;IACjD;IACA,IAAI,CAACU,KAAK,CAAChB,QAAQ,EAAE;MACnB,OAAOR,UAAU;IACnB;IACA,OAAOM,WAAW,CAACC,IAAI,EAAEiB,KAAK,CAAChB,QAAQ,CAACmB,QAAQ,CAAC,CAAC,EAAEH,KAAK,CAACV,MAAM,CAAC;EACnE,CAAC,EAAE,CAACU,KAAK,CAAChB,QAAQ,EAAEgB,KAAK,CAACV,MAAM,CAAC,CAAC;EAClCpB,gBAAgB,CAAC6B,MAAM,EAAE;IACvBE;EACF,CAAC,EAAE,SAAS,CAAC;AACf,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}