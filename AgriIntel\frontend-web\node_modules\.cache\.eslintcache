[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\App.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\reportWebVitals.ts": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\store.ts": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\i18n\\i18n.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\authSlice.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\animalSlice.ts": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\healthSlice.ts": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\breedingSlice.ts": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\uiSlice.ts": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\api\\authAPI.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\NotFoundPage.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\auth\\LoginPage.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\auth\\RegisterPage.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\HealthPage.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AnimalDetailsPage.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\dashboard\\DashboardPage.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AnimalsPage.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\breeding\\BreedingPage.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\FinancialPage.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\FeedingPage.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\ReportsPage.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\settings\\SettingsPage.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\layout\\Layout.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\LoadingSpinner.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\layout\\AuthLayout.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\auth\\ProtectedRoute.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\layout\\Sidebar.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\layout\\Header.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\DashboardWidget.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\StatCard.tsx": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\ActivityFeed.tsx": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\index.js": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\lazyLoadUtils.ts": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\dateUtils.ts": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\ProtectedRoute.tsx": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\OfflineContext.tsx": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\LanguageTest.tsx": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\MongoDbContext.tsx": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\IntegratedDataContext.tsx": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\AuthContext.tsx": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\SnackbarContext.tsx": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\DataContext.tsx": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\AlertContext.tsx": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\LanguageContext.tsx": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\ThemeContext.tsx": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\layouts\\UnifiedDashboardLayout.tsx": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\providers\\GlobalThemeProvider.tsx": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\Compliance.tsx": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\Login.tsx": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\TranslationTest.tsx": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\Settings.tsx": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\Resources.tsx": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\ResourcesDashboard.tsx": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\settings\\UserManagement.tsx": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\settings\\ThemeSettings.tsx": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\settings\\BackupSettings.tsx": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\settings\\DatabaseSettings.tsx": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\MarketReport.tsx": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\FeedingReports.tsx": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\PredictiveAnalysis.tsx": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\FinancialReport.tsx": "62", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\ReportsDashboard.tsx": "63", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\PerformanceReport.tsx": "64", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\HealthReport.tsx": "65", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\AnalysisReport.tsx": "66", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\Feeding.tsx": "67", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\breeding\\Breeding.tsx": "68", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\breeding\\BreedingDashboard.tsx": "69", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\analytics\\BusinessPredictions.tsx": "70", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\Financial.tsx": "71", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\analytics\\BusinessAnalysisDashboard.tsx": "72", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\analytics\\BusinessStrategy.tsx": "73", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\Health.tsx": "74", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\HealthDashboard.tsx": "75", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\FeedingDashboard.tsx": "76", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\FinancialDashboard.tsx": "77", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AssetManagement.tsx": "78", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\Animals.tsx": "79", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AnimalsDashboard.tsx": "80", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\dashboard\\Dashboard.tsx": "81", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\commercial\\Commercial.tsx": "82", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\index.ts": "83", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\ErrorPage\\index.ts": "84", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\offlineSync.ts": "85", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\initMongoDb.ts": "86", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\mockMongoDbClient.ts": "87", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\syncMockDataToMongoDB.ts": "88", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\cacheUtils.ts": "89", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\breedingMongoService.ts": "90", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\healthMongoService.ts": "91", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\CustomButton.tsx": "92", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\dataIntegrationService.ts": "93", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\mongoService.ts": "94", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\api.ts": "95", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useAnimalData.ts": "96", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useBreedingRecords.ts": "97", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useFinancialData.ts": "98", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useHealthRecords.ts": "99", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useCommercialData.ts": "100", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useFeedingData.ts": "101", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useInventoryData.ts": "102", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useComplianceData.ts": "103", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\Sidebar.tsx": "104", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useResourcesData.ts": "105", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\AlertSystem.tsx": "106", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\QuickActions.tsx": "107", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\NotificationsPanel.tsx": "108", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\GlobalSearch.tsx": "109", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\WeatherWidget.tsx": "110", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\GlobalBackground.tsx": "111", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\GlobalTabSelectionFixer.tsx": "112", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\iconImports.ts": "113", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\compliance\\Inspections.tsx": "114", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\compliance\\ComplianceDashboard.tsx": "115", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\compliance\\Documents.tsx": "116", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\ResourceMaintenance.tsx": "117", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\ResourceDetail.tsx": "118", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\compliance\\Certifications.tsx": "119", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\ResourceForm.tsx": "120", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\ResourcesList.tsx": "121", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\ResourceUtilization.tsx": "122", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\Training.tsx": "123", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\Guidelines.tsx": "124", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\Support.tsx": "125", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\Documentation.tsx": "126", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\GovernmentResources.tsx": "127", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\userMongoService.ts": "128", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\Downloads.tsx": "129", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\applyTabSelectionFixes.ts": "130", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModuleContainer.tsx": "131", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useResponsive.ts": "132", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\errorHandling.ts": "133", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\reportService.ts": "134", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\compliance\\BrandmarksCertificates.tsx": "135", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\formatters.ts": "136", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\cardStyles.ts": "137", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\mockFeedingData.ts": "138", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\reportUtils.ts": "139", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useReportSelection.ts": "140", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\ReportPage.tsx": "141", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\mockFinancialData.ts": "142", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\applyReportSelectionFixes.ts": "143", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\FeedingRecords.tsx": "144", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\Inventory.tsx": "145", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\FeedingNutrition.tsx": "146", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\FeedingPlans.tsx": "147", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\Suppliers.tsx": "148", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\breeding\\PregnancyTracking.tsx": "149", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\FeedingSchedules.tsx": "150", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\breeding\\HeatCalendar.tsx": "151", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useBusinessPredictions.ts": "152", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\breeding\\BreedingSchedule.tsx": "153", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\breeding\\BreedingRecords.tsx": "154", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\FinancialTransactions.tsx": "155", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\FinancialReports.tsx": "156", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\breeding\\BirthPredictions.tsx": "157", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\FinancialBudgets.tsx": "158", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\ROI.tsx": "159", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useBusinessStrategy.ts": "160", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\FinancialOverview.tsx": "161", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\Forecast.tsx": "162", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\Invoices.tsx": "163", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\Diseases.tsx": "164", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\Vaccinations.tsx": "165", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\Appointments.tsx": "166", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\Treatments.tsx": "167", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\Records.tsx": "168", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\admin\\DataMigration.tsx": "169", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\settings\\EnhancedThemeSettings.tsx": "170", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\reports\\ReportCard.tsx": "171", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\reports\\ReportButton.tsx": "172", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\reports\\ReportsModuleWrapper.tsx": "173", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\reports\\HtmlReportViewer.tsx": "174", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\breedingData.ts": "175", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\analytics\\BusinessPredictionCard.tsx": "176", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\analytics\\BusinessAnalyticsButton.tsx": "177", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\analytics\\BusinessInsightCard.tsx": "178", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\analytics\\BusinessAnalyticsCard.tsx": "179", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\analytics\\BusinessStrategyCard.tsx": "180", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\HealthRecords.tsx": "181", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\RetirementTracking.tsx": "182", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useMongoAnimalData.ts": "183", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AssetManagementDashboard.tsx": "184", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AnimalsList.tsx": "185", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AnimalDetail.tsx": "186", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AnimalForm.tsx": "187", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\feeding\\FeedingRecordModal.tsx": "188", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\feeding\\FeedInventoryModal.tsx": "189", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\Profiles.tsx": "190", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\Tracking.tsx": "191", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\Records.tsx": "192", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\RFID.tsx": "193", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\Genealogy.tsx": "194", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\commercial\\Pricing.tsx": "195", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\commercial\\PriceUpdates.tsx": "196", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\commercial\\Auctions.tsx": "197", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\dashboard\\ModernDashboard.tsx": "198", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\commercial\\SupplierDetail.tsx": "199", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\animals\\AnimalMarketplaceGrid.tsx": "200", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\commercial\\Marketplace.tsx": "201", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\commercial\\Orders.tsx": "202", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\commercial\\Suppliers.tsx": "203", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\DesignSystem.tsx": "204", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\commercial\\CommercialDashboard.tsx": "205", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\EnhancedDataTable.tsx": "206", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\AnimatedBackgroundCard.tsx": "207", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\EnhancedCard.tsx": "208", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\commercial\\BkbAuctionContext.tsx": "209", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\AnimatedDashboardCard.tsx": "210", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModuleHeader.tsx": "211", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\AnimatedChart.tsx": "212", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ResponsiveDashboard.tsx": "213", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SponsorCarousel.tsx": "214", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\EnhancedSponsorCarousel.tsx": "215", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ResponsiveDataCard.tsx": "216", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\FarmerResourceLinks.tsx": "217", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ResponsiveFormLayout.tsx": "218", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ResponsiveChartContainer.tsx": "219", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ResponsiveNavTabs.tsx": "220", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ConsistentChart.tsx": "221", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\LazyLoadFallback.tsx": "222", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ErrorBoundary.tsx": "223", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\OfflineIndicator.tsx": "224", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\AnimatedBackground.tsx": "225", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\MongoDbStatus.tsx": "226", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\StatusIndicator.tsx": "227", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\VirtualizedTable.tsx": "228", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\LazyImage.tsx": "229", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\DataCard.tsx": "230", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ResponsiveLayout.tsx": "231", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\StyledTable.tsx": "232", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\BlendedBackgroundCard.tsx": "233", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\RotatingBackground.tsx": "234", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\FeaturedDealCard.tsx": "235", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ProductCard.tsx": "236", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModuleItemCard.tsx": "237", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\CommercialBackgroundCard.tsx": "238", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModuleHeaderCard.tsx": "239", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModuleCard.tsx": "240", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModuleContentCard.tsx": "241", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModuleBackgroundRotator.tsx": "242", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\LoadingOverlay.tsx": "243", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\CalOverlay.tsx": "244", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\DashboardCard.tsx": "245", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModernDashboard.tsx": "246", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModernCard.tsx": "247", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModernChart.tsx": "248", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModernDataTable.tsx": "249", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\withSubModuleTranslation.tsx": "250", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\FallbackUI.tsx": "251", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\StandardDashboard.tsx": "252", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SubModuleMetricsCard.tsx": "253", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SubModuleDashboard.tsx": "254", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SubModuleActionPanel.tsx": "255", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\EnhancedChart.tsx": "256", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SubModuleDataTable.tsx": "257", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SubModuleReport.tsx": "258", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SubModuleChart.tsx": "259", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\UnifiedModuleContainer.tsx": "260", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModulePageWrapper.tsx": "261", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ThemeSafeWrapper.tsx": "262", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SafeButton.tsx": "263", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ThemeSafeButton.tsx": "264", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ResizeObserverFix.tsx": "265", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\PlainButton.tsx": "266", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\mongoDbClient.ts": "267", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\healthData.ts": "268", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\animalData.ts": "269", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\feedingData.ts": "270", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SimpleButton.tsx": "271", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\mockMongoDb.ts": "272", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\MuiButtonFix.tsx": "273", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SafeThemeProvider.tsx": "274", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\inventoryData.ts": "275", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\GlobalSafeButton.tsx": "276", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\FixedThemeProvider.tsx": "277", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\DirectFixButton.tsx": "278", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\FinalButtonReplacer.tsx": "279", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\mongoDbService.ts": "280", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\FinalMuiButtonFix.tsx": "281", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\FinalFixButton.tsx": "282", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\financialData.ts": "283", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ButtonReplacer.tsx": "284", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\MainNavigationFix.tsx": "285", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\CrudComponent.tsx": "286", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModuleDashboard.tsx": "287", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\StandardForm.tsx": "288", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\PageBackground.tsx": "289", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\DataTable.tsx": "290", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useDataFetching.ts": "291", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\ErrorPage\\ErrorPage.tsx": "292", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModulePage.tsx": "293", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\commercialData.ts": "294", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\export.ts": "295", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\config.ts": "296", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\index.ts": "297", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\apiService.ts": "298", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\constants\\routes.ts": "299", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\design\\DesignSystem.tsx": "300", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\snackbar.ts": "301", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useAccessibility.ts": "302", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\moduleStyles.ts": "303", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\apiRoutes.ts": "304", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\reports\\ReportGenerator.tsx": "305", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\config\\api.ts": "306", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\tabSelectionUtils.ts": "307", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\businessAnalysisService.ts": "308", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useTabSelection.ts": "309", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useFeedingNutrition.ts": "310", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\smsService.ts": "311", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useSnackbar.ts": "312", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\notificationHistoryService.ts": "313", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\notificationService.ts": "314", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\feeding\\FeedingRecordsList.tsx": "315", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\breeding\\BreedingRecordModal.tsx": "316", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\breeding\\NotificationConfig.tsx": "317", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\breeding\\NotificationHistory.tsx": "318", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\breeding\\TestNotification.tsx": "319", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useBusinessAnalytics.ts": "320", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\designEnhancements.ts": "321", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\selectionUtils.ts": "322", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\dataMigration.ts": "323", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\mockDataGenerator.ts": "324", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useReportItemSelection.ts": "325", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\animalMongoService.ts": "326", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useAlert.ts": "327", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\commercialUtils.ts": "328", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\backgroundThemes.ts": "329", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\RFIDTrackingPage.tsx": "330", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\SupplierContext.tsx": "331", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\animals\\AnimalQuickView.tsx": "332", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\animals\\RealTimeTracking.tsx": "333", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\animals\\AnimalMarketplaceCard.tsx": "334", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\health\\HealthRecordModal.tsx": "335", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\maps\\GoogleMapComponent.tsx": "336", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\commercial\\AuctionModal.tsx": "337", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\commercial\\SupplierModal.tsx": "338", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\commercial\\BkbAuctionCalendar.tsx": "339", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\commercial\\OrderModal.tsx": "340", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\WeatherWidget.tsx": "341", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\SponsorRotator.tsx": "342", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\QuickActions.tsx": "343", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\TaskManager.tsx": "344", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\NotificationCenter.tsx": "345", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\commercial\\MarketForecast.tsx": "346", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\commercial\\MarketplaceSuppliers.tsx": "347", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\commercial\\MarketplaceListingDetail.tsx": "348", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\mockMongoDbService.js": "349", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\applyDesignPattern.ts": "350", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\CardStyleUtils.ts": "351", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\themeDefaults.ts": "352", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\localImages.ts": "353", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\resizeObserverFix.ts": "354", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\translationHelpers.ts": "355", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\colorBlendingUtils.ts": "356", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\dashboardStyles.ts": "357", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\muiButtonFix.ts": "358", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\patchMuiButton.ts": "359", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\monkeyPatchMuiButton.ts": "360", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\commercial\\BkbAuctionService.ts": "361", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\finalMuiButtonFix.js": "362", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\analyticsData.ts": "363", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useApiError.ts": "364", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\reportsData.ts": "365", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\rfidData.ts": "366", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\dashboardData.ts": "367", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\supplierData.ts": "368", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\pastureMgmtData.ts": "369", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useTranslation.ts": "370", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\index.ts": "371", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\businessAnalysisData.ts": "372", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\ruralNotificationService.ts": "373", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\testNotificationService.ts": "374", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\animals\\RFIDManagement.tsx": "375", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\businessAnalyticsService.ts": "376", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\animals\\RFIDTracking.tsx": "377", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\animals\\AnimalStatusBadge.tsx": "378", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\supplierService.ts": "379", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\commercial\\BkbAuctionFilters.tsx": "380", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\models\\AnimalModel.ts": "381", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\mockData.js": "382", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\core\\ServiceRegistry.ts": "383", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\core\\BaseService.ts": "384", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\usePerformance.ts": "385", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\performanceMonitor.ts": "386"}, {"size": 3263, "mtime": 1748803490361, "results": "387", "hashOfConfig": "388"}, {"size": 13930, "mtime": 1746826011885, "results": "389", "hashOfConfig": "388"}, {"size": 438, "mtime": 1741135543092, "results": "390", "hashOfConfig": "388"}, {"size": 747, "mtime": 1748799127154, "results": "391", "hashOfConfig": "388"}, {"size": 1095, "mtime": 1748799313651, "results": "392", "hashOfConfig": "388"}, {"size": 8868, "mtime": 1748803758260, "results": "393", "hashOfConfig": "388"}, {"size": 8765, "mtime": 1748799201703, "results": "394", "hashOfConfig": "388"}, {"size": 4507, "mtime": 1748799226000, "results": "395", "hashOfConfig": "388"}, {"size": 5898, "mtime": 1748799260494, "results": "396", "hashOfConfig": "388"}, {"size": 3895, "mtime": 1748799281210, "results": "397", "hashOfConfig": "388"}, {"size": 4614, "mtime": 1748799302108, "results": "398", "hashOfConfig": "388"}, {"size": 980, "mtime": 1748799658226, "results": "399", "hashOfConfig": "388"}, {"size": 5591, "mtime": 1748799521497, "results": "400", "hashOfConfig": "388"}, {"size": 716, "mtime": 1748799588928, "results": "401", "hashOfConfig": "388"}, {"size": 601, "mtime": 1748799612547, "results": "402", "hashOfConfig": "388"}, {"size": 617, "mtime": 1748799605786, "results": "403", "hashOfConfig": "388"}, {"size": 12213, "mtime": 1748805388660, "results": "404", "hashOfConfig": "388"}, {"size": 1062, "mtime": 1748799598382, "results": "405", "hashOfConfig": "388"}, {"size": 609, "mtime": 1748799620324, "results": "406", "hashOfConfig": "388"}, {"size": 613, "mtime": 1748799635093, "results": "407", "hashOfConfig": "388"}, {"size": 602, "mtime": 1748799627844, "results": "408", "hashOfConfig": "388"}, {"size": 608, "mtime": 1748799642539, "results": "409", "hashOfConfig": "388"}, {"size": 598, "mtime": 1748799649805, "results": "410", "hashOfConfig": "388"}, {"size": 1080, "mtime": 1748799441184, "results": "411", "hashOfConfig": "388"}, {"size": 1151, "mtime": 1748799406414, "results": "412", "hashOfConfig": "388"}, {"size": 2085, "mtime": 1748799431537, "results": "413", "hashOfConfig": "388"}, {"size": 1594, "mtime": 1748803703954, "results": "414", "hashOfConfig": "388"}, {"size": 3282, "mtime": 1748799578784, "results": "415", "hashOfConfig": "388"}, {"size": 7893, "mtime": 1748801125488, "results": "416", "hashOfConfig": "388"}, {"size": 712, "mtime": 1748804622525, "results": "417", "hashOfConfig": "388"}, {"size": 1708, "mtime": 1748804636588, "results": "418", "hashOfConfig": "388"}, {"size": 3545, "mtime": 1748804659558, "results": "419", "hashOfConfig": "388"}, {"size": 459, "mtime": 1746863546049, "results": "420", "hashOfConfig": "388"}, {"size": 5194, "mtime": 1746548456871, "results": "421", "hashOfConfig": "388"}, {"size": 5080, "mtime": 1744237206922, "results": "422", "hashOfConfig": "388"}, {"size": 3137, "mtime": 1746795381953, "results": "423", "hashOfConfig": "388"}, {"size": 3671, "mtime": 1744237595811, "results": "424", "hashOfConfig": "388"}, {"size": 9033, "mtime": 1746582358716, "results": "425", "hashOfConfig": "388"}, {"size": 5642, "mtime": 1747008143505, "results": "426", "hashOfConfig": "388"}, {"size": 10528, "mtime": 1744996505178, "results": "427", "hashOfConfig": "388"}, {"size": 11055, "mtime": 1748346374678, "results": "428", "hashOfConfig": "388"}, {"size": 1448, "mtime": 1744014789184, "results": "429", "hashOfConfig": "388"}, {"size": 4156, "mtime": 1744219837898, "results": "430", "hashOfConfig": "388"}, {"size": 2015, "mtime": 1744776502349, "results": "431", "hashOfConfig": "388"}, {"size": 5946, "mtime": 1745979650336, "results": "432", "hashOfConfig": "388"}, {"size": 30459, "mtime": 1746658474553, "results": "433", "hashOfConfig": "388"}, {"size": 28681, "mtime": 1746932348858, "results": "434", "hashOfConfig": "388"}, {"size": 6255, "mtime": 1746294418506, "results": "435", "hashOfConfig": "388"}, {"size": 916, "mtime": 1744241745893, "results": "436", "hashOfConfig": "388"}, {"size": 4425, "mtime": 1748561115415, "results": "437", "hashOfConfig": "388"}, {"size": 4892, "mtime": 1746586049886, "results": "438", "hashOfConfig": "388"}, {"size": 32448, "mtime": 1746582310626, "results": "439", "hashOfConfig": "388"}, {"size": 1853, "mtime": 1744870028380, "results": "440", "hashOfConfig": "388"}, {"size": 23924, "mtime": 1746578833151, "results": "441", "hashOfConfig": "388"}, {"size": 21462, "mtime": 1746658737653, "results": "442", "hashOfConfig": "388"}, {"size": 2121, "mtime": 1746358285431, "results": "443", "hashOfConfig": "388"}, {"size": 16186, "mtime": 1746585601739, "results": "444", "hashOfConfig": "388"}, {"size": 10963, "mtime": 1746578633431, "results": "445", "hashOfConfig": "388"}, {"size": 185, "mtime": 1745081681490, "results": "446", "hashOfConfig": "388"}, {"size": 17411, "mtime": 1746578832901, "results": "447", "hashOfConfig": "388"}, {"size": 27361, "mtime": 1746578832975, "results": "448", "hashOfConfig": "388"}, {"size": 23349, "mtime": 1746578832919, "results": "449", "hashOfConfig": "388"}, {"size": 28940, "mtime": 1746578833017, "results": "450", "hashOfConfig": "388"}, {"size": 200, "mtime": 1745081665004, "results": "451", "hashOfConfig": "388"}, {"size": 185, "mtime": 1745081673589, "results": "452", "hashOfConfig": "388"}, {"size": 191, "mtime": 1745081656040, "results": "453", "hashOfConfig": "388"}, {"size": 1245, "mtime": 1745368529984, "results": "454", "hashOfConfig": "388"}, {"size": 804, "mtime": 1745368594396, "results": "455", "hashOfConfig": "388"}, {"size": 38761, "mtime": 1746578832325, "results": "456", "hashOfConfig": "388"}, {"size": 10416, "mtime": 1746468122816, "results": "457", "hashOfConfig": "388"}, {"size": 1361, "mtime": 1746275160664, "results": "458", "hashOfConfig": "388"}, {"size": 36493, "mtime": 1746549958824, "results": "459", "hashOfConfig": "388"}, {"size": 24673, "mtime": 1746576833072, "results": "460", "hashOfConfig": "388"}, {"size": 1026, "mtime": 1744225569584, "results": "461", "hashOfConfig": "388"}, {"size": 21899, "mtime": 1746816071834, "results": "462", "hashOfConfig": "388"}, {"size": 34501, "mtime": 1746578832600, "results": "463", "hashOfConfig": "388"}, {"size": 42758, "mtime": 1746641780901, "results": "464", "hashOfConfig": "388"}, {"size": 655, "mtime": 1745626511086, "results": "465", "hashOfConfig": "388"}, {"size": 1847, "mtime": 1746814189850, "results": "466", "hashOfConfig": "388"}, {"size": 22306, "mtime": 1746741084382, "results": "467", "hashOfConfig": "388"}, {"size": 702, "mtime": 1746805347600, "results": "468", "hashOfConfig": "388"}, {"size": 1912, "mtime": 1746549907002, "results": "469", "hashOfConfig": "388"}, {"size": 5586, "mtime": 1746930771329, "results": "470", "hashOfConfig": "388"}, {"size": 38, "mtime": 1741170104079, "results": "471", "hashOfConfig": "388"}, {"size": 8194, "mtime": 1744237577228, "results": "472", "hashOfConfig": "388"}, {"size": 1463, "mtime": 1747009700209, "results": "473", "hashOfConfig": "388"}, {"size": 4738, "mtime": 1747009687337, "results": "474", "hashOfConfig": "388"}, {"size": 6202, "mtime": 1746586499208, "results": "475", "hashOfConfig": "388"}, {"size": 4201, "mtime": 1745634824955, "results": "476", "hashOfConfig": "388"}, {"size": 1356, "mtime": 1744370475606, "results": "477", "hashOfConfig": "388"}, {"size": 1555, "mtime": 1744370490544, "results": "478", "hashOfConfig": "388"}, {"size": 9196, "mtime": 1746830064349, "results": "479", "hashOfConfig": "388"}, {"size": 13624, "mtime": 1744998499823, "results": "480", "hashOfConfig": "388"}, {"size": 3762, "mtime": 1747008658330, "results": "481", "hashOfConfig": "388"}, {"size": 19312, "mtime": 1746930237772, "results": "482", "hashOfConfig": "388"}, {"size": 3300, "mtime": 1746669670651, "results": "483", "hashOfConfig": "388"}, {"size": 6032, "mtime": 1748559915027, "results": "484", "hashOfConfig": "388"}, {"size": 3705, "mtime": 1748559827091, "results": "485", "hashOfConfig": "388"}, {"size": 5912, "mtime": 1746660170147, "results": "486", "hashOfConfig": "388"}, {"size": 16713, "mtime": 1746092829780, "results": "487", "hashOfConfig": "388"}, {"size": 24575, "mtime": 1748559927764, "results": "488", "hashOfConfig": "388"}, {"size": 1932, "mtime": 1744010529354, "results": "489", "hashOfConfig": "388"}, {"size": 11618, "mtime": 1744136715589, "results": "490", "hashOfConfig": "388"}, {"size": 730, "mtime": 1747270689844, "results": "491", "hashOfConfig": "388"}, {"size": 11888, "mtime": 1744136765637, "results": "492", "hashOfConfig": "388"}, {"size": 4140, "mtime": 1744780881691, "results": "493", "hashOfConfig": "388"}, {"size": 7110, "mtime": 1746578832088, "results": "494", "hashOfConfig": "388"}, {"size": 13951, "mtime": 1746582371596, "results": "495", "hashOfConfig": "388"}, {"size": 11155, "mtime": 1746578831852, "results": "496", "hashOfConfig": "388"}, {"size": 21097, "mtime": 1746578832181, "results": "497", "hashOfConfig": "388"}, {"size": 10932, "mtime": 1746932285360, "results": "498", "hashOfConfig": "388"}, {"size": 1671, "mtime": 1746358012242, "results": "499", "hashOfConfig": "388"}, {"size": 11184, "mtime": 1747189687093, "results": "500", "hashOfConfig": "388"}, {"size": 22228, "mtime": 1746578832539, "results": "501", "hashOfConfig": "388"}, {"size": 24149, "mtime": 1746578832499, "results": "502", "hashOfConfig": "388"}, {"size": 17938, "mtime": 1744065779937, "results": "503", "hashOfConfig": "388"}, {"size": 13447, "mtime": 1746578833104, "results": "504", "hashOfConfig": "388"}, {"size": 515, "mtime": 1744024326355, "results": "505", "hashOfConfig": "388"}, {"size": 26099, "mtime": 1746578832467, "results": "506", "hashOfConfig": "388"}, {"size": 603, "mtime": 1744024334097, "results": "507", "hashOfConfig": "388"}, {"size": 10081, "mtime": 1746578833185, "results": "508", "hashOfConfig": "388"}, {"size": 13570, "mtime": 1744026010109, "results": "509", "hashOfConfig": "388"}, {"size": 28891, "mtime": 1746578833281, "results": "510", "hashOfConfig": "388"}, {"size": 24463, "mtime": 1746578833087, "results": "511", "hashOfConfig": "388"}, {"size": 30372, "mtime": 1746661312486, "results": "512", "hashOfConfig": "388"}, {"size": 31397, "mtime": 1746578833057, "results": "513", "hashOfConfig": "388"}, {"size": 12145, "mtime": 1746578633173, "results": "514", "hashOfConfig": "388"}, {"size": 5523, "mtime": 1746585630670, "results": "515", "hashOfConfig": "388"}, {"size": 32401, "mtime": 1746578833067, "results": "516", "hashOfConfig": "388"}, {"size": 13626, "mtime": 1746825042858, "results": "517", "hashOfConfig": "388"}, {"size": 2065, "mtime": 1745377183904, "results": "518", "hashOfConfig": "388"}, {"size": 3587, "mtime": 1745711271056, "results": "519", "hashOfConfig": "388"}, {"size": 5165, "mtime": 1745634895739, "results": "520", "hashOfConfig": "388"}, {"size": 89370, "mtime": 1746658655105, "results": "521", "hashOfConfig": "388"}, {"size": 18959, "mtime": 1741439482733, "results": "522", "hashOfConfig": "388"}, {"size": 3865, "mtime": 1744996127978, "results": "523", "hashOfConfig": "388"}, {"size": 3819, "mtime": 1745377927773, "results": "524", "hashOfConfig": "388"}, {"size": 4235, "mtime": 1745506706708, "results": "525", "hashOfConfig": "388"}, {"size": 9026, "mtime": 1745374520430, "results": "526", "hashOfConfig": "388"}, {"size": 1775, "mtime": 1746383871559, "results": "527", "hashOfConfig": "388"}, {"size": 10651, "mtime": 1746578633052, "results": "528", "hashOfConfig": "388"}, {"size": 5965, "mtime": 1745507541574, "results": "529", "hashOfConfig": "388"}, {"size": 4206, "mtime": 1746384262393, "results": "530", "hashOfConfig": "388"}, {"size": 316, "mtime": 1745110333292, "results": "531", "hashOfConfig": "388"}, {"size": 29568, "mtime": 1746578832618, "results": "532", "hashOfConfig": "388"}, {"size": 8257, "mtime": 1744037070061, "results": "533", "hashOfConfig": "388"}, {"size": 26517, "mtime": 1746578832606, "results": "534", "hashOfConfig": "388"}, {"size": 24331, "mtime": 1746578832636, "results": "535", "hashOfConfig": "388"}, {"size": 14268, "mtime": 1746578631864, "results": "536", "hashOfConfig": "388"}, {"size": 17842, "mtime": 1746577940885, "results": "537", "hashOfConfig": "388"}, {"size": 20494, "mtime": 1746578832359, "results": "538", "hashOfConfig": "388"}, {"size": 9895, "mtime": 1746485579968, "results": "539", "hashOfConfig": "388"}, {"size": 26268, "mtime": 1746578832356, "results": "540", "hashOfConfig": "388"}, {"size": 8291, "mtime": 1746578832329, "results": "541", "hashOfConfig": "388"}, {"size": 9519, "mtime": 1746578832712, "results": "542", "hashOfConfig": "388"}, {"size": 12356, "mtime": 1746578632456, "results": "543", "hashOfConfig": "388"}, {"size": 30922, "mtime": 1746578832315, "results": "544", "hashOfConfig": "388"}, {"size": 10953, "mtime": 1746578832657, "results": "545", "hashOfConfig": "388"}, {"size": 21218, "mtime": 1746578832740, "results": "546", "hashOfConfig": "388"}, {"size": 17102, "mtime": 1746485539949, "results": "547", "hashOfConfig": "388"}, {"size": 20706, "mtime": 1746578832705, "results": "548", "hashOfConfig": "388"}, {"size": 16652, "mtime": 1746578832732, "results": "549", "hashOfConfig": "388"}, {"size": 14399, "mtime": 1746578832736, "results": "550", "hashOfConfig": "388"}, {"size": 21442, "mtime": 1746578832798, "results": "551", "hashOfConfig": "388"}, {"size": 20429, "mtime": 1746578832823, "results": "552", "hashOfConfig": "388"}, {"size": 20862, "mtime": 1746578832755, "results": "553", "hashOfConfig": "388"}, {"size": 22782, "mtime": 1746578832820, "results": "554", "hashOfConfig": "388"}, {"size": 26411, "mtime": 1746578832816, "results": "555", "hashOfConfig": "388"}, {"size": 6597, "mtime": 1746581332865, "results": "556", "hashOfConfig": "388"}, {"size": 21689, "mtime": 1746578832124, "results": "557", "hashOfConfig": "388"}, {"size": 5178, "mtime": 1746433372704, "results": "558", "hashOfConfig": "388"}, {"size": 4733, "mtime": 1746433278748, "results": "559", "hashOfConfig": "388"}, {"size": 1043, "mtime": 1746388322567, "results": "560", "hashOfConfig": "388"}, {"size": 4699, "mtime": 1746581333976, "results": "561", "hashOfConfig": "388"}, {"size": 10152, "mtime": 1744917446858, "results": "562", "hashOfConfig": "388"}, {"size": 4561, "mtime": 1746467299243, "results": "563", "hashOfConfig": "388"}, {"size": 4823, "mtime": 1746467238215, "results": "564", "hashOfConfig": "388"}, {"size": 4443, "mtime": 1746467337068, "results": "565", "hashOfConfig": "388"}, {"size": 4651, "mtime": 1746641919371, "results": "566", "hashOfConfig": "388"}, {"size": 4313, "mtime": 1746467317988, "results": "567", "hashOfConfig": "388"}, {"size": 9296, "mtime": 1746578832807, "results": "568", "hashOfConfig": "388"}, {"size": 20390, "mtime": 1746578832307, "results": "569", "hashOfConfig": "388"}, {"size": 7364, "mtime": 1746091378079, "results": "570", "hashOfConfig": "388"}, {"size": 18583, "mtime": 1746578832296, "results": "571", "hashOfConfig": "388"}, {"size": 14711, "mtime": 1746578631570, "results": "572", "hashOfConfig": "388"}, {"size": 33769, "mtime": 1746578631516, "results": "573", "hashOfConfig": "388"}, {"size": 16092, "mtime": 1746576709699, "results": "574", "hashOfConfig": "388"}, {"size": 11212, "mtime": 1746581333684, "results": "575", "hashOfConfig": "388"}, {"size": 19744, "mtime": 1746581333710, "results": "576", "hashOfConfig": "388"}, {"size": 8106, "mtime": 1745783519251, "results": "577", "hashOfConfig": "388"}, {"size": 18341, "mtime": 1746578832313, "results": "578", "hashOfConfig": "388"}, {"size": 4952, "mtime": 1746578631680, "results": "579", "hashOfConfig": "388"}, {"size": 166, "mtime": 1744970228942, "results": "580", "hashOfConfig": "388"}, {"size": 4543, "mtime": 1745298526427, "results": "581", "hashOfConfig": "388"}, {"size": 25938, "mtime": 1746578832425, "results": "582", "hashOfConfig": "388"}, {"size": 15036, "mtime": 1746578632010, "results": "583", "hashOfConfig": "388"}, {"size": 36729, "mtime": 1746578832379, "results": "584", "hashOfConfig": "388"}, {"size": 63630, "mtime": 1746813626172, "results": "585", "hashOfConfig": "388"}, {"size": 28015, "mtime": 1746578832438, "results": "586", "hashOfConfig": "388"}, {"size": 6011, "mtime": 1746582569209, "results": "587", "hashOfConfig": "388"}, {"size": 10999, "mtime": 1746577291409, "results": "588", "hashOfConfig": "388"}, {"size": 29042, "mtime": 1746578832406, "results": "589", "hashOfConfig": "388"}, {"size": 25151, "mtime": 1746578832444, "results": "590", "hashOfConfig": "388"}, {"size": 11825, "mtime": 1744406912114, "results": "591", "hashOfConfig": "388"}, {"size": 29508, "mtime": 1746578832393, "results": "592", "hashOfConfig": "388"}, {"size": 20855, "mtime": 1746578831477, "results": "593", "hashOfConfig": "388"}, {"size": 20733, "mtime": 1746938066149, "results": "594", "hashOfConfig": "388"}, {"size": 3998, "mtime": 1745982274815, "results": "595", "hashOfConfig": "388"}, {"size": 4034, "mtime": 1746126006988, "results": "596", "hashOfConfig": "388"}, {"size": 6874, "mtime": 1746581333133, "results": "597", "hashOfConfig": "388"}, {"size": 15460, "mtime": 1746581333490, "results": "598", "hashOfConfig": "388"}, {"size": 10769, "mtime": 1746581333129, "results": "599", "hashOfConfig": "388"}, {"size": 3567, "mtime": 1744235800689, "results": "600", "hashOfConfig": "388"}, {"size": 8544, "mtime": 1746578831748, "results": "601", "hashOfConfig": "388"}, {"size": 14739, "mtime": 1746578831582, "results": "602", "hashOfConfig": "388"}, {"size": 6688, "mtime": 1746578831720, "results": "603", "hashOfConfig": "388"}, {"size": 6459, "mtime": 1746578831601, "results": "604", "hashOfConfig": "388"}, {"size": 6711, "mtime": 1744235745915, "results": "605", "hashOfConfig": "388"}, {"size": 7817, "mtime": 1746578831701, "results": "606", "hashOfConfig": "388"}, {"size": 7636, "mtime": 1746578831729, "results": "607", "hashOfConfig": "388"}, {"size": 8280, "mtime": 1746661127953, "results": "608", "hashOfConfig": "388"}, {"size": 2802, "mtime": 1746581333300, "results": "609", "hashOfConfig": "388"}, {"size": 5558, "mtime": 1746581333216, "results": "610", "hashOfConfig": "388"}, {"size": 9108, "mtime": 1746581333509, "results": "611", "hashOfConfig": "388"}, {"size": 4141, "mtime": 1744777419195, "results": "612", "hashOfConfig": "388"}, {"size": 1440, "mtime": 1744370585890, "results": "613", "hashOfConfig": "388"}, {"size": 5652, "mtime": 1746032831711, "results": "614", "hashOfConfig": "388"}, {"size": 16028, "mtime": 1746578831796, "results": "615", "hashOfConfig": "388"}, {"size": 4379, "mtime": 1744893280363, "results": "616", "hashOfConfig": "388"}, {"size": 10220, "mtime": 1746661266093, "results": "617", "hashOfConfig": "388"}, {"size": 2895, "mtime": 1744795336813, "results": "618", "hashOfConfig": "388"}, {"size": 4538, "mtime": 1745982156863, "results": "619", "hashOfConfig": "388"}, {"size": 4575, "mtime": 1746355514262, "results": "620", "hashOfConfig": "388"}, {"size": 1653, "mtime": 1745212417750, "results": "621", "hashOfConfig": "388"}, {"size": 2526, "mtime": 1746581333234, "results": "622", "hashOfConfig": "388"}, {"size": 4983, "mtime": 1746581333593, "results": "623", "hashOfConfig": "388"}, {"size": 5942, "mtime": 1746581333505, "results": "624", "hashOfConfig": "388"}, {"size": 3790, "mtime": 1746355584502, "results": "625", "hashOfConfig": "388"}, {"size": 8683, "mtime": 1746581333499, "results": "626", "hashOfConfig": "388"}, {"size": 4042, "mtime": 1745982046619, "results": "627", "hashOfConfig": "388"}, {"size": 6302, "mtime": 1746740959195, "results": "628", "hashOfConfig": "388"}, {"size": 2485, "mtime": 1745377841983, "results": "629", "hashOfConfig": "388"}, {"size": 2490, "mtime": 1746483378342, "results": "630", "hashOfConfig": "388"}, {"size": 1880, "mtime": 1745373739630, "results": "631", "hashOfConfig": "388"}, {"size": 6435, "mtime": 1746581333184, "results": "632", "hashOfConfig": "388"}, {"size": 17860, "mtime": 1746824835976, "results": "633", "hashOfConfig": "388"}, {"size": 8659, "mtime": 1746581333399, "results": "634", "hashOfConfig": "388"}, {"size": 24223, "mtime": 1746658622253, "results": "635", "hashOfConfig": "388"}, {"size": 25648, "mtime": 1746581333412, "results": "636", "hashOfConfig": "388"}, {"size": 1713, "mtime": 1745783382918, "results": "637", "hashOfConfig": "388"}, {"size": 4705, "mtime": 1746581333219, "results": "638", "hashOfConfig": "388"}, {"size": 4524, "mtime": 1746859355260, "results": "639", "hashOfConfig": "388"}, {"size": 3407, "mtime": 1746274704076, "results": "640", "hashOfConfig": "388"}, {"size": 8416, "mtime": 1746578630074, "results": "641", "hashOfConfig": "388"}, {"size": 4049, "mtime": 1746581333613, "results": "642", "hashOfConfig": "388"}, {"size": 23961, "mtime": 1746581333202, "results": "643", "hashOfConfig": "388"}, {"size": 14436, "mtime": 1746578831771, "results": "644", "hashOfConfig": "388"}, {"size": 21552, "mtime": 1746581333617, "results": "645", "hashOfConfig": "388"}, {"size": 8299, "mtime": 1746578831757, "results": "646", "hashOfConfig": "388"}, {"size": 1683, "mtime": 1746294185109, "results": "647", "hashOfConfig": "388"}, {"size": 2040, "mtime": 1746355752539, "results": "648", "hashOfConfig": "388"}, {"size": 1076, "mtime": 1746357975502, "results": "649", "hashOfConfig": "388"}, {"size": 4767, "mtime": 1746468771316, "results": "650", "hashOfConfig": "388"}, {"size": 942, "mtime": 1746581333672, "results": "651", "hashOfConfig": "388"}, {"size": 2212, "mtime": 1746666825405, "results": "652", "hashOfConfig": "388"}, {"size": 3964, "mtime": 1746382861554, "results": "653", "hashOfConfig": "388"}, {"size": 3647, "mtime": 1747187266390, "results": "654", "hashOfConfig": "388"}, {"size": 3642, "mtime": 1744917414427, "results": "655", "hashOfConfig": "388"}, {"size": 10836, "mtime": 1745627801961, "results": "656", "hashOfConfig": "388"}, {"size": 4720, "mtime": 1745110457083, "results": "657", "hashOfConfig": "388"}, {"size": 4587, "mtime": 1746358758954, "results": "658", "hashOfConfig": "388"}, {"size": 2705, "mtime": 1744399386635, "results": "659", "hashOfConfig": "388"}, {"size": 2479, "mtime": 1746433785493, "results": "660", "hashOfConfig": "388"}, {"size": 3761, "mtime": 1746432677102, "results": "661", "hashOfConfig": "388"}, {"size": 2836, "mtime": 1742945889396, "results": "662", "hashOfConfig": "388"}, {"size": 3762, "mtime": 1746658518998, "results": "663", "hashOfConfig": "388"}, {"size": 4062, "mtime": 1746434163257, "results": "664", "hashOfConfig": "388"}, {"size": 4188, "mtime": 1746661330794, "results": "665", "hashOfConfig": "388"}, {"size": 6740, "mtime": 1746434557784, "results": "666", "hashOfConfig": "388"}, {"size": 5062, "mtime": 1746929712716, "results": "667", "hashOfConfig": "388"}, {"size": 1176, "mtime": 1746435422581, "results": "668", "hashOfConfig": "388"}, {"size": 5050, "mtime": 1746434505966, "results": "669", "hashOfConfig": "388"}, {"size": 6174, "mtime": 1746586530288, "results": "670", "hashOfConfig": "388"}, {"size": 9775, "mtime": 1746434360381, "results": "671", "hashOfConfig": "388"}, {"size": 5445, "mtime": 1747008175582, "results": "672", "hashOfConfig": "388"}, {"size": 11109, "mtime": 1746581333179, "results": "673", "hashOfConfig": "388"}, {"size": 8619, "mtime": 1746581333479, "results": "674", "hashOfConfig": "388"}, {"size": 9963, "mtime": 1746546201356, "results": "675", "hashOfConfig": "388"}, {"size": 2726, "mtime": 1748561665973, "results": "676", "hashOfConfig": "388"}, {"size": 15229, "mtime": 1746578831468, "results": "677", "hashOfConfig": "388"}, {"size": 1211, "mtime": 1746674096151, "results": "678", "hashOfConfig": "388"}, {"size": 756, "mtime": 1741170884072, "results": "679", "hashOfConfig": "388"}, {"size": 8933, "mtime": 1746581333506, "results": "680", "hashOfConfig": "388"}, {"size": 9942, "mtime": 1744917471989, "results": "681", "hashOfConfig": "388"}, {"size": 1470, "mtime": 1745372339960, "results": "682", "hashOfConfig": "388"}, {"size": 168, "mtime": 1748559899442, "results": "683", "hashOfConfig": "388"}, {"size": 430, "mtime": 1742946159379, "results": "684", "hashOfConfig": "388"}, {"size": 4629, "mtime": 1748399132418, "results": "685", "hashOfConfig": "388"}, {"size": 1797, "mtime": 1746487350247, "results": "686", "hashOfConfig": "388"}, {"size": 11000, "mtime": 1746578832209, "results": "687", "hashOfConfig": "388"}, {"size": 824, "mtime": 1745089047329, "results": "688", "hashOfConfig": "388"}, {"size": 4183, "mtime": 1744779524529, "results": "689", "hashOfConfig": "388"}, {"size": 8504, "mtime": 1745377154691, "results": "690", "hashOfConfig": "388"}, {"size": 3467, "mtime": 1748313739624, "results": "691", "hashOfConfig": "388"}, {"size": 16563, "mtime": 1746582663837, "results": "692", "hashOfConfig": "388"}, {"size": 3080, "mtime": 1746663135372, "results": "693", "hashOfConfig": "388"}, {"size": 2163, "mtime": 1746356628821, "results": "694", "hashOfConfig": "388"}, {"size": 8244, "mtime": 1746485381727, "results": "695", "hashOfConfig": "388"}, {"size": 586, "mtime": 1746356051449, "results": "696", "hashOfConfig": "388"}, {"size": 3276, "mtime": 1744037020058, "results": "697", "hashOfConfig": "388"}, {"size": 4323, "mtime": 1744795314297, "results": "698", "hashOfConfig": "388"}, {"size": 598, "mtime": 1744795300464, "results": "699", "hashOfConfig": "388"}, {"size": 5315, "mtime": 1744811817307, "results": "700", "hashOfConfig": "388"}, {"size": 15366, "mtime": 1746513827937, "results": "701", "hashOfConfig": "388"}, {"size": 25138, "mtime": 1746582651084, "results": "702", "hashOfConfig": "388"}, {"size": 11045, "mtime": 1746816111415, "results": "703", "hashOfConfig": "388"}, {"size": 28072, "mtime": 1746661098138, "results": "704", "hashOfConfig": "388"}, {"size": 18025, "mtime": 1746581333025, "results": "705", "hashOfConfig": "388"}, {"size": 16992, "mtime": 1746581333027, "results": "706", "hashOfConfig": "388"}, {"size": 16149, "mtime": 1746548172970, "results": "707", "hashOfConfig": "388"}, {"size": 7137, "mtime": 1746290230941, "results": "708", "hashOfConfig": "388"}, {"size": 1593, "mtime": 1746586127643, "results": "709", "hashOfConfig": "388"}, {"size": 3637, "mtime": 1744370915832, "results": "710", "hashOfConfig": "388"}, {"size": 745, "mtime": 1742945554636, "results": "711", "hashOfConfig": "388"}, {"size": 1634, "mtime": 1746433314777, "results": "712", "hashOfConfig": "388"}, {"size": 4688, "mtime": 1746658767985, "results": "713", "hashOfConfig": "388"}, {"size": 401, "mtime": 1744793803057, "results": "714", "hashOfConfig": "388"}, {"size": 1081, "mtime": 1746092221883, "results": "715", "hashOfConfig": "388"}, {"size": 11126, "mtime": 1745224915856, "results": "716", "hashOfConfig": "388"}, {"size": 1798, "mtime": 1744969819355, "results": "717", "hashOfConfig": "388"}, {"size": 5171, "mtime": 1746054418945, "results": "718", "hashOfConfig": "388"}, {"size": 3728, "mtime": 1746581332911, "results": "719", "hashOfConfig": "388"}, {"size": 9300, "mtime": 1746581332917, "results": "720", "hashOfConfig": "388"}, {"size": 7342, "mtime": 1746582557545, "results": "721", "hashOfConfig": "388"}, {"size": 7715, "mtime": 1746581333749, "results": "722", "hashOfConfig": "388"}, {"size": 4391, "mtime": 1745299533655, "results": "723", "hashOfConfig": "388"}, {"size": 21025, "mtime": 1746581333045, "results": "724", "hashOfConfig": "388"}, {"size": 16051, "mtime": 1746581333056, "results": "725", "hashOfConfig": "388"}, {"size": 17295, "mtime": 1746581333047, "results": "726", "hashOfConfig": "388"}, {"size": 16393, "mtime": 1746581333054, "results": "727", "hashOfConfig": "388"}, {"size": 10358, "mtime": 1746743910499, "results": "728", "hashOfConfig": "388"}, {"size": 10157, "mtime": 1746743860115, "results": "729", "hashOfConfig": "388"}, {"size": 7182, "mtime": 1746745365774, "results": "730", "hashOfConfig": "388"}, {"size": 26077, "mtime": 1746745581006, "results": "731", "hashOfConfig": "388"}, {"size": 14786, "mtime": 1746745639267, "results": "732", "hashOfConfig": "388"}, {"size": 21479, "mtime": 1746582593378, "results": "733", "hashOfConfig": "388"}, {"size": 16561, "mtime": 1746582633126, "results": "734", "hashOfConfig": "388"}, {"size": 18316, "mtime": 1746582605857, "results": "735", "hashOfConfig": "388"}, {"size": 2639, "mtime": 1744774481578, "results": "736", "hashOfConfig": "388"}, {"size": 4608, "mtime": 1745378074656, "results": "737", "hashOfConfig": "388"}, {"size": 3953, "mtime": 1745229142932, "results": "738", "hashOfConfig": "388"}, {"size": 1828, "mtime": 1746065766787, "results": "739", "hashOfConfig": "388"}, {"size": 5236, "mtime": 1746938033527, "results": "740", "hashOfConfig": "388"}, {"size": 6233, "mtime": 1746824888333, "results": "741", "hashOfConfig": "388"}, {"size": 1959, "mtime": 1745783365560, "results": "742", "hashOfConfig": "388"}, {"size": 3157, "mtime": 1746355638706, "results": "743", "hashOfConfig": "388"}, {"size": 5898, "mtime": 1746293412213, "results": "744", "hashOfConfig": "388"}, {"size": 3563, "mtime": 1746586098349, "results": "745", "hashOfConfig": "388"}, {"size": 5784, "mtime": 1746433806516, "results": "746", "hashOfConfig": "388"}, {"size": 6954, "mtime": 1746433823021, "results": "747", "hashOfConfig": "388"}, {"size": 6726, "mtime": 1746125874301, "results": "748", "hashOfConfig": "388"}, {"size": 7316, "mtime": 1746435402231, "results": "749", "hashOfConfig": "388"}, {"size": 2219, "mtime": 1742918088171, "results": "750", "hashOfConfig": "388"}, {"size": 2844, "mtime": 1746674039998, "results": "751", "hashOfConfig": "388"}, {"size": 6230, "mtime": 1744237170465, "results": "752", "hashOfConfig": "388"}, {"size": 2570, "mtime": 1744224840434, "results": "753", "hashOfConfig": "388"}, {"size": 2640, "mtime": 1744917395609, "results": "754", "hashOfConfig": "388"}, {"size": 1264, "mtime": 1741338443763, "results": "755", "hashOfConfig": "388"}, {"size": 1220, "mtime": 1744034141499, "results": "756", "hashOfConfig": "388"}, {"size": 754, "mtime": 1746548725400, "results": "757", "hashOfConfig": "388"}, {"size": 122, "mtime": 1744237762316, "results": "758", "hashOfConfig": "388"}, {"size": 17465, "mtime": 1746586813393, "results": "759", "hashOfConfig": "388"}, {"size": 12130, "mtime": 1746240070039, "results": "760", "hashOfConfig": "388"}, {"size": 9972, "mtime": 1744812838754, "results": "761", "hashOfConfig": "388"}, {"size": 23885, "mtime": 1746931745346, "results": "762", "hashOfConfig": "388"}, {"size": 8303, "mtime": 1746658704132, "results": "763", "hashOfConfig": "388"}, {"size": 15070, "mtime": 1746581332951, "results": "764", "hashOfConfig": "388"}, {"size": 967, "mtime": 1745078944506, "results": "765", "hashOfConfig": "388"}, {"size": 7953, "mtime": 1746513813123, "results": "766", "hashOfConfig": "388"}, {"size": 7864, "mtime": 1746581333052, "results": "767", "hashOfConfig": "388"}, {"size": 5293, "mtime": 1745627373264, "results": "768", "hashOfConfig": "388"}, {"size": 2820, "mtime": 1745123018122, "results": "769", "hashOfConfig": "388"}, {"size": 2660, "mtime": 1746125802638, "results": "770", "hashOfConfig": "388"}, {"size": 2496, "mtime": 1746125825546, "results": "771", "hashOfConfig": "388"}, {"size": 2140, "mtime": 1744237747033, "results": "772", "hashOfConfig": "388"}, {"size": 9579, "mtime": 1744242833981, "results": "773", "hashOfConfig": "388"}, {"filePath": "774", "messages": "775", "suppressedMessages": "776", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "eltgwa", {"filePath": "777", "messages": "778", "suppressedMessages": "779", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "780", "messages": "781", "suppressedMessages": "782", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "783", "messages": "784", "suppressedMessages": "785", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "786", "messages": "787", "suppressedMessages": "788", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "789", "messages": "790", "suppressedMessages": "791", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "792", "messages": "793", "suppressedMessages": "794", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "795", "messages": "796", "suppressedMessages": "797", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "798", "messages": "799", "suppressedMessages": "800", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "801", "messages": "802", "suppressedMessages": "803", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "804", "messages": "805", "suppressedMessages": "806", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "807", "messages": "808", "suppressedMessages": "809", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "810", "messages": "811", "suppressedMessages": "812", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "813", "messages": "814", "suppressedMessages": "815", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "816", "messages": "817", "suppressedMessages": "818", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "819", "messages": "820", "suppressedMessages": "821", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "822", "messages": "823", "suppressedMessages": "824", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "825", "messages": "826", "suppressedMessages": "827", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "828", "messages": "829", "suppressedMessages": "830", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "831", "messages": "832", "suppressedMessages": "833", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "834", "messages": "835", "suppressedMessages": "836", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "837", "messages": "838", "suppressedMessages": "839", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "840", "messages": "841", "suppressedMessages": "842", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "843", "messages": "844", "suppressedMessages": "845", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "846", "messages": "847", "suppressedMessages": "848", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "849", "messages": "850", "suppressedMessages": "851", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "852", "messages": "853", "suppressedMessages": "854", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "855", "messages": "856", "suppressedMessages": "857", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "858", "messages": "859", "suppressedMessages": "860", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "861", "messages": "862", "suppressedMessages": "863", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "864", "messages": "865", "suppressedMessages": "866", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "867", "messages": "868", "suppressedMessages": "869", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "870", "messages": "871", "suppressedMessages": "872", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "873", "messages": "874", "suppressedMessages": "875", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "876", "messages": "877", "suppressedMessages": "878", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "879", "messages": "880", "suppressedMessages": "881", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "882", "messages": "883", "suppressedMessages": "884", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "885", "messages": "886", "suppressedMessages": "887", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "888", "messages": "889", "suppressedMessages": "890", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "891", "messages": "892", "suppressedMessages": "893", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "894", "messages": "895", "suppressedMessages": "896", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "897", "messages": "898", "suppressedMessages": "899", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "900", "messages": "901", "suppressedMessages": "902", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "903", "messages": "904", "suppressedMessages": "905", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "906", "messages": "907", "suppressedMessages": "908", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "909", "messages": "910", "suppressedMessages": "911", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "912", "messages": "913", "suppressedMessages": "914", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "915", "messages": "916", "suppressedMessages": "917", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "918", "messages": "919", "suppressedMessages": "920", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "921", "messages": "922", "suppressedMessages": "923", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "924", "messages": "925", "suppressedMessages": "926", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "927", "messages": "928", "suppressedMessages": "929", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "930", "messages": "931", "suppressedMessages": "932", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "933", "messages": "934", "suppressedMessages": "935", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "936", "messages": "937", "suppressedMessages": "938", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "939", "messages": "940", "suppressedMessages": "941", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "942", "messages": "943", "suppressedMessages": "944", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "945", "messages": "946", "suppressedMessages": "947", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "948", "messages": "949", "suppressedMessages": "950", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "951", "messages": "952", "suppressedMessages": "953", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "954", "messages": "955", "suppressedMessages": "956", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "957", "messages": "958", "suppressedMessages": "959", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "960", "messages": "961", "suppressedMessages": "962", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "963", "messages": "964", "suppressedMessages": "965", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "966", "messages": "967", "suppressedMessages": "968", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "969", "messages": "970", "suppressedMessages": "971", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "972", "messages": "973", "suppressedMessages": "974", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "975", "messages": "976", "suppressedMessages": "977", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "978", "messages": "979", "suppressedMessages": "980", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 119, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "981", "messages": "982", "suppressedMessages": "983", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "984", "messages": "985", "suppressedMessages": "986", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "987", "messages": "988", "suppressedMessages": "989", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 30, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "990", "messages": "991", "suppressedMessages": "992", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "993", "messages": "994", "suppressedMessages": "995", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "996", "messages": "997", "suppressedMessages": "998", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 17, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "999", "messages": "1000", "suppressedMessages": "1001", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1002", "messages": "1003", "suppressedMessages": "1004", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1005", "messages": "1006", "suppressedMessages": "1007", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1008", "messages": "1009", "suppressedMessages": "1010", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1011", "messages": "1012", "suppressedMessages": "1013", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1014", "messages": "1015", "suppressedMessages": "1016", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1017", "messages": "1018", "suppressedMessages": "1019", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1020", "messages": "1021", "suppressedMessages": "1022", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1023", "messages": "1024", "suppressedMessages": "1025", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1026", "messages": "1027", "suppressedMessages": "1028", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1029", "messages": "1030", "suppressedMessages": "1031", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1032", "messages": "1033", "suppressedMessages": "1034", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1035", "messages": "1036", "suppressedMessages": "1037", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1038", "messages": "1039", "suppressedMessages": "1040", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1041", "messages": "1042", "suppressedMessages": "1043", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1044", "messages": "1045", "suppressedMessages": "1046", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1047", "messages": "1048", "suppressedMessages": "1049", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1050", "messages": "1051", "suppressedMessages": "1052", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1053", "messages": "1054", "suppressedMessages": "1055", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1056", "messages": "1057", "suppressedMessages": "1058", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1059", "messages": "1060", "suppressedMessages": "1061", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1062", "messages": "1063", "suppressedMessages": "1064", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1065", "messages": "1066", "suppressedMessages": "1067", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1068", "messages": "1069", "suppressedMessages": "1070", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1071", "messages": "1072", "suppressedMessages": "1073", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1074", "messages": "1075", "suppressedMessages": "1076", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1077", "messages": "1078", "suppressedMessages": "1079", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1080", "messages": "1081", "suppressedMessages": "1082", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1083", "messages": "1084", "suppressedMessages": "1085", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1086", "messages": "1087", "suppressedMessages": "1088", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1089", "messages": "1090", "suppressedMessages": "1091", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1092", "messages": "1093", "suppressedMessages": "1094", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1095", "messages": "1096", "suppressedMessages": "1097", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1098", "messages": "1099", "suppressedMessages": "1100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1101", "messages": "1102", "suppressedMessages": "1103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1104", "messages": "1105", "suppressedMessages": "1106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1107", "messages": "1108", "suppressedMessages": "1109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1110", "messages": "1111", "suppressedMessages": "1112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1113", "messages": "1114", "suppressedMessages": "1115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1116", "messages": "1117", "suppressedMessages": "1118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1119", "messages": "1120", "suppressedMessages": "1121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1122", "messages": "1123", "suppressedMessages": "1124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1125", "messages": "1126", "suppressedMessages": "1127", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1128", "messages": "1129", "suppressedMessages": "1130", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1131", "messages": "1132", "suppressedMessages": "1133", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1134", "messages": "1135", "suppressedMessages": "1136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1137", "messages": "1138", "suppressedMessages": "1139", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1140", "messages": "1141", "suppressedMessages": "1142", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1143", "messages": "1144", "suppressedMessages": "1145", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1146", "messages": "1147", "suppressedMessages": "1148", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1149", "messages": "1150", "suppressedMessages": "1151", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 11, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1152", "messages": "1153", "suppressedMessages": "1154", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1155", "messages": "1156", "suppressedMessages": "1157", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1158", "messages": "1159", "suppressedMessages": "1160", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1161", "messages": "1162", "suppressedMessages": "1163", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1164", "messages": "1165", "suppressedMessages": "1166", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1167", "messages": "1168", "suppressedMessages": "1169", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1170", "messages": "1171", "suppressedMessages": "1172", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1173", "messages": "1174", "suppressedMessages": "1175", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1176", "messages": "1177", "suppressedMessages": "1178", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1179", "messages": "1180", "suppressedMessages": "1181", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1182", "messages": "1183", "suppressedMessages": "1184", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1185", "messages": "1186", "suppressedMessages": "1187", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1188", "messages": "1189", "suppressedMessages": "1190", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1191", "messages": "1192", "suppressedMessages": "1193", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1194", "messages": "1195", "suppressedMessages": "1196", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1197", "messages": "1198", "suppressedMessages": "1199", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1200", "messages": "1201", "suppressedMessages": "1202", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1203", "messages": "1204", "suppressedMessages": "1205", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1206", "messages": "1207", "suppressedMessages": "1208", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1209", "messages": "1210", "suppressedMessages": "1211", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1212", "messages": "1213", "suppressedMessages": "1214", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1215", "messages": "1216", "suppressedMessages": "1217", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1218", "messages": "1219", "suppressedMessages": "1220", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1221", "messages": "1222", "suppressedMessages": "1223", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1224", "messages": "1225", "suppressedMessages": "1226", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1227", "messages": "1228", "suppressedMessages": "1229", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1230", "messages": "1231", "suppressedMessages": "1232", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1233", "messages": "1234", "suppressedMessages": "1235", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1236", "messages": "1237", "suppressedMessages": "1238", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1239", "messages": "1240", "suppressedMessages": "1241", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1242", "messages": "1243", "suppressedMessages": "1244", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1245", "messages": "1246", "suppressedMessages": "1247", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1248", "messages": "1249", "suppressedMessages": "1250", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1251", "messages": "1252", "suppressedMessages": "1253", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1254", "messages": "1255", "suppressedMessages": "1256", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1257", "messages": "1258", "suppressedMessages": "1259", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1260", "messages": "1261", "suppressedMessages": "1262", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1263", "messages": "1264", "suppressedMessages": "1265", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1266", "messages": "1267", "suppressedMessages": "1268", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1269", "messages": "1270", "suppressedMessages": "1271", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1272", "messages": "1273", "suppressedMessages": "1274", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1275", "messages": "1276", "suppressedMessages": "1277", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1278", "messages": "1279", "suppressedMessages": "1280", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1281", "messages": "1282", "suppressedMessages": "1283", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 23, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1284", "messages": "1285", "suppressedMessages": "1286", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1287", "messages": "1288", "suppressedMessages": "1289", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1290", "messages": "1291", "suppressedMessages": "1292", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1293", "messages": "1294", "suppressedMessages": "1295", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1296", "messages": "1297", "suppressedMessages": "1298", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1299", "messages": "1300", "suppressedMessages": "1301", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1302", "messages": "1303", "suppressedMessages": "1304", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1305", "messages": "1306", "suppressedMessages": "1307", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1308", "messages": "1309", "suppressedMessages": "1310", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1311", "messages": "1312", "suppressedMessages": "1313", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1314", "messages": "1315", "suppressedMessages": "1316", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1317", "messages": "1318", "suppressedMessages": "1319", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1320", "messages": "1321", "suppressedMessages": "1322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1323", "messages": "1324", "suppressedMessages": "1325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1326", "messages": "1327", "suppressedMessages": "1328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1329", "messages": "1330", "suppressedMessages": "1331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1332", "messages": "1333", "suppressedMessages": "1334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1335", "messages": "1336", "suppressedMessages": "1337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1338", "messages": "1339", "suppressedMessages": "1340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1341", "messages": "1342", "suppressedMessages": "1343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1344", "messages": "1345", "suppressedMessages": "1346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1347", "messages": "1348", "suppressedMessages": "1349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1350", "messages": "1351", "suppressedMessages": "1352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1353", "messages": "1354", "suppressedMessages": "1355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1356", "messages": "1357", "suppressedMessages": "1358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1359", "messages": "1360", "suppressedMessages": "1361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1362", "messages": "1363", "suppressedMessages": "1364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1365", "messages": "1366", "suppressedMessages": "1367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1368", "messages": "1369", "suppressedMessages": "1370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1371", "messages": "1372", "suppressedMessages": "1373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1374", "messages": "1375", "suppressedMessages": "1376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1377", "messages": "1378", "suppressedMessages": "1379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1380", "messages": "1381", "suppressedMessages": "1382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 26, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1383", "messages": "1384", "suppressedMessages": "1385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1386", "messages": "1387", "suppressedMessages": "1388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 30, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1389", "messages": "1390", "suppressedMessages": "1391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1392", "messages": "1393", "suppressedMessages": "1394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1395", "messages": "1396", "suppressedMessages": "1397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1398", "messages": "1399", "suppressedMessages": "1400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1401", "messages": "1402", "suppressedMessages": "1403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1404", "messages": "1405", "suppressedMessages": "1406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1407", "messages": "1408", "suppressedMessages": "1409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1410", "messages": "1411", "suppressedMessages": "1412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1413", "messages": "1414", "suppressedMessages": "1415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1416", "messages": "1417", "suppressedMessages": "1418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1419", "messages": "1420", "suppressedMessages": "1421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1422", "messages": "1423", "suppressedMessages": "1424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1425", "messages": "1426", "suppressedMessages": "1427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1428", "messages": "1429", "suppressedMessages": "1430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1431", "messages": "1432", "suppressedMessages": "1433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1434", "messages": "1435", "suppressedMessages": "1436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1437", "messages": "1438", "suppressedMessages": "1439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1440", "messages": "1441", "suppressedMessages": "1442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1443", "messages": "1444", "suppressedMessages": "1445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1446", "messages": "1447", "suppressedMessages": "1448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1449", "messages": "1450", "suppressedMessages": "1451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1452", "messages": "1453", "suppressedMessages": "1454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1455", "messages": "1456", "suppressedMessages": "1457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1458", "messages": "1459", "suppressedMessages": "1460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1461", "messages": "1462", "suppressedMessages": "1463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1464", "messages": "1465", "suppressedMessages": "1466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1467", "messages": "1468", "suppressedMessages": "1469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1470", "messages": "1471", "suppressedMessages": "1472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1473", "messages": "1474", "suppressedMessages": "1475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1476", "messages": "1477", "suppressedMessages": "1478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1479", "messages": "1480", "suppressedMessages": "1481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1482", "messages": "1483", "suppressedMessages": "1484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1485", "messages": "1486", "suppressedMessages": "1487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1488", "messages": "1489", "suppressedMessages": "1490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1491", "messages": "1492", "suppressedMessages": "1493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1494", "messages": "1495", "suppressedMessages": "1496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1497", "messages": "1498", "suppressedMessages": "1499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1500", "messages": "1501", "suppressedMessages": "1502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1503", "messages": "1504", "suppressedMessages": "1505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1506", "messages": "1507", "suppressedMessages": "1508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1509", "messages": "1510", "suppressedMessages": "1511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1512", "messages": "1513", "suppressedMessages": "1514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1515", "messages": "1516", "suppressedMessages": "1517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1518", "messages": "1519", "suppressedMessages": "1520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1521", "messages": "1522", "suppressedMessages": "1523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1524", "messages": "1525", "suppressedMessages": "1526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1527", "messages": "1528", "suppressedMessages": "1529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1530", "messages": "1531", "suppressedMessages": "1532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1533", "messages": "1534", "suppressedMessages": "1535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1536", "messages": "1537", "suppressedMessages": "1538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1539", "messages": "1540", "suppressedMessages": "1541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1542", "messages": "1543", "suppressedMessages": "1544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1545", "messages": "1546", "suppressedMessages": "1547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1548", "messages": "1549", "suppressedMessages": "1550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1551", "messages": "1552", "suppressedMessages": "1553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1554", "messages": "1555", "suppressedMessages": "1556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1557", "messages": "1558", "suppressedMessages": "1559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1560", "messages": "1561", "suppressedMessages": "1562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1563", "messages": "1564", "suppressedMessages": "1565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1566", "messages": "1567", "suppressedMessages": "1568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1569", "messages": "1570", "suppressedMessages": "1571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1572", "messages": "1573", "suppressedMessages": "1574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1575", "messages": "1576", "suppressedMessages": "1577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1578", "messages": "1579", "suppressedMessages": "1580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 14, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1581", "messages": "1582", "suppressedMessages": "1583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1584", "messages": "1585", "suppressedMessages": "1586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1587", "messages": "1588", "suppressedMessages": "1589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1590", "messages": "1591", "suppressedMessages": "1592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1593", "messages": "1594", "suppressedMessages": "1595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1596", "messages": "1597", "suppressedMessages": "1598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1599", "messages": "1600", "suppressedMessages": "1601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1602", "messages": "1603", "suppressedMessages": "1604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1605", "messages": "1606", "suppressedMessages": "1607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1608", "messages": "1609", "suppressedMessages": "1610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1611", "messages": "1612", "suppressedMessages": "1613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1614", "messages": "1615", "suppressedMessages": "1616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1617", "messages": "1618", "suppressedMessages": "1619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1620", "messages": "1621", "suppressedMessages": "1622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1623", "messages": "1624", "suppressedMessages": "1625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1626", "messages": "1627", "suppressedMessages": "1628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1629", "messages": "1630", "suppressedMessages": "1631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1632", "messages": "1633", "suppressedMessages": "1634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1635", "messages": "1636", "suppressedMessages": "1637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1638", "messages": "1639", "suppressedMessages": "1640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1641", "messages": "1642", "suppressedMessages": "1643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1644", "messages": "1645", "suppressedMessages": "1646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1647", "messages": "1648", "suppressedMessages": "1649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1650", "messages": "1651", "suppressedMessages": "1652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1653", "messages": "1654", "suppressedMessages": "1655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1656", "messages": "1657", "suppressedMessages": "1658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1659", "messages": "1660", "suppressedMessages": "1661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1662", "messages": "1663", "suppressedMessages": "1664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1665", "messages": "1666", "suppressedMessages": "1667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1668", "messages": "1669", "suppressedMessages": "1670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1671", "messages": "1672", "suppressedMessages": "1673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1674", "messages": "1675", "suppressedMessages": "1676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1677", "messages": "1678", "suppressedMessages": "1679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1680", "messages": "1681", "suppressedMessages": "1682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1683", "messages": "1684", "suppressedMessages": "1685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1686", "messages": "1687", "suppressedMessages": "1688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1689", "messages": "1690", "suppressedMessages": "1691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1692", "messages": "1693", "suppressedMessages": "1694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1695", "messages": "1696", "suppressedMessages": "1697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1698", "messages": "1699", "suppressedMessages": "1700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1701", "messages": "1702", "suppressedMessages": "1703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1704", "messages": "1705", "suppressedMessages": "1706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1707", "messages": "1708", "suppressedMessages": "1709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1710", "messages": "1711", "suppressedMessages": "1712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1713", "messages": "1714", "suppressedMessages": "1715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1716", "messages": "1717", "suppressedMessages": "1718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1719", "messages": "1720", "suppressedMessages": "1721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1722", "messages": "1723", "suppressedMessages": "1724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1725", "messages": "1726", "suppressedMessages": "1727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1728", "messages": "1729", "suppressedMessages": "1730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1731", "messages": "1732", "suppressedMessages": "1733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1734", "messages": "1735", "suppressedMessages": "1736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1737", "messages": "1738", "suppressedMessages": "1739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1740", "messages": "1741", "suppressedMessages": "1742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1743", "messages": "1744", "suppressedMessages": "1745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1746", "messages": "1747", "suppressedMessages": "1748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1749", "messages": "1750", "suppressedMessages": "1751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1752", "messages": "1753", "suppressedMessages": "1754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1755", "messages": "1756", "suppressedMessages": "1757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1758", "messages": "1759", "suppressedMessages": "1760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1761", "messages": "1762", "suppressedMessages": "1763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1764", "messages": "1765", "suppressedMessages": "1766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1767", "messages": "1768", "suppressedMessages": "1769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1770", "messages": "1771", "suppressedMessages": "1772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1773", "messages": "1774", "suppressedMessages": "1775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1776", "messages": "1777", "suppressedMessages": "1778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1779", "messages": "1780", "suppressedMessages": "1781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1782", "messages": "1783", "suppressedMessages": "1784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1785", "messages": "1786", "suppressedMessages": "1787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1788", "messages": "1789", "suppressedMessages": "1790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1791", "messages": "1792", "suppressedMessages": "1793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1794", "messages": "1795", "suppressedMessages": "1796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1797", "messages": "1798", "suppressedMessages": "1799", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1800", "messages": "1801", "suppressedMessages": "1802", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1803", "messages": "1804", "suppressedMessages": "1805", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1806", "messages": "1807", "suppressedMessages": "1808", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1809", "messages": "1810", "suppressedMessages": "1811", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1812", "messages": "1813", "suppressedMessages": "1814", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1815", "messages": "1816", "suppressedMessages": "1817", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1818", "messages": "1819", "suppressedMessages": "1820", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1821", "messages": "1822", "suppressedMessages": "1823", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1824", "messages": "1825", "suppressedMessages": "1826", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1827", "messages": "1828", "suppressedMessages": "1829", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1830", "messages": "1831", "suppressedMessages": "1832", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1833", "messages": "1834", "suppressedMessages": "1835", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1836", "messages": "1837", "suppressedMessages": "1838", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1839", "messages": "1840", "suppressedMessages": "1841", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1842", "messages": "1843", "suppressedMessages": "1844", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1845", "messages": "1846", "suppressedMessages": "1847", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1848", "messages": "1849", "suppressedMessages": "1850", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1851", "messages": "1852", "suppressedMessages": "1853", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1854", "messages": "1855", "suppressedMessages": "1856", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1857", "messages": "1858", "suppressedMessages": "1859", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1860", "messages": "1861", "suppressedMessages": "1862", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1863", "messages": "1864", "suppressedMessages": "1865", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1866", "messages": "1867", "suppressedMessages": "1868", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1869", "messages": "1870", "suppressedMessages": "1871", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1872", "messages": "1873", "suppressedMessages": "1874", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1875", "messages": "1876", "suppressedMessages": "1877", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1878", "messages": "1879", "suppressedMessages": "1880", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1881", "messages": "1882", "suppressedMessages": "1883", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1884", "messages": "1885", "suppressedMessages": "1886", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1887", "messages": "1888", "suppressedMessages": "1889", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1890", "messages": "1891", "suppressedMessages": "1892", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1893", "messages": "1894", "suppressedMessages": "1895", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1896", "messages": "1897", "suppressedMessages": "1898", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1899", "messages": "1900", "suppressedMessages": "1901", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1902", "messages": "1903", "suppressedMessages": "1904", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1905", "messages": "1906", "suppressedMessages": "1907", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1908", "messages": "1909", "suppressedMessages": "1910", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1911", "messages": "1912", "suppressedMessages": "1913", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1914", "messages": "1915", "suppressedMessages": "1916", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1917", "messages": "1918", "suppressedMessages": "1919", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1920", "messages": "1921", "suppressedMessages": "1922", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1923", "messages": "1924", "suppressedMessages": "1925", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "1926", "messages": "1927", "suppressedMessages": "1928", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "1929", "messages": "1930", "suppressedMessages": "1931", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\App.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\store.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\i18n\\i18n.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\authSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\animalSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\healthSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\breedingSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\uiSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\api\\authAPI.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\NotFoundPage.tsx", ["1932"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\auth\\LoginPage.tsx", ["1933"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\auth\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\HealthPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AnimalDetailsPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\dashboard\\DashboardPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AnimalsPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\breeding\\BreedingPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\FinancialPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\FeedingPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\ReportsPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\settings\\SettingsPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\layout\\Layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\layout\\AuthLayout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\layout\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\layout\\Header.tsx", ["1934"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\DashboardWidget.tsx", ["1935"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\StatCard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\ActivityFeed.tsx", ["1936"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\lazyLoadUtils.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\dateUtils.ts", ["1937"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\OfflineContext.tsx", ["1938"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\LanguageTest.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\MongoDbContext.tsx", ["1939", "1940"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\IntegratedDataContext.tsx", ["1941", "1942"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\AuthContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\SnackbarContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\DataContext.tsx", ["1943", "1944"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\AlertContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\LanguageContext.tsx", ["1945"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\ThemeContext.tsx", ["1946"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\layouts\\UnifiedDashboardLayout.tsx", ["1947", "1948", "1949", "1950", "1951"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\providers\\GlobalThemeProvider.tsx", ["1952", "1953"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\Compliance.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\Login.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\TranslationTest.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\Settings.tsx", ["1954", "1955", "1956", "1957", "1958", "1959", "1960"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\Resources.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\ResourcesDashboard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\settings\\UserManagement.tsx", ["1961"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\settings\\ThemeSettings.tsx", ["1962", "1963", "1964"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\settings\\BackupSettings.tsx", ["1965", "1966"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\settings\\DatabaseSettings.tsx", ["1967"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\MarketReport.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\FeedingReports.tsx", ["1968", "1969", "1970", "1971", "1972", "1973", "1974", "1975", "1976", "1977", "1978", "1979", "1980", "1981", "1982"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\PredictiveAnalysis.tsx", ["1983", "1984", "1985", "1986", "1987", "1988", "1989", "1990", "1991", "1992", "1993", "1994", "1995", "1996", "1997", "1998", "1999", "2000", "2001", "2002", "2003", "2004", "2005"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\FinancialReport.tsx", ["2006", "2007", "2008", "2009", "2010", "2011", "2012", "2013", "2014", "2015", "2016", "2017", "2018"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\ReportsDashboard.tsx", ["2019", "2020", "2021", "2022", "2023", "2024", "2025"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\PerformanceReport.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\HealthReport.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\AnalysisReport.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\Feeding.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\breeding\\Breeding.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\breeding\\BreedingDashboard.tsx", ["2026", "2027", "2028", "2029", "2030", "2031", "2032", "2033", "2034", "2035", "2036", "2037", "2038", "2039", "2040", "2041", "2042", "2043", "2044", "2045", "2046", "2047", "2048", "2049", "2050", "2051", "2052", "2053", "2054", "2055", "2056", "2057", "2058", "2059", "2060", "2061", "2062", "2063", "2064", "2065", "2066", "2067", "2068", "2069", "2070", "2071", "2072", "2073", "2074", "2075", "2076", "2077", "2078", "2079", "2080", "2081", "2082", "2083", "2084", "2085", "2086", "2087", "2088", "2089", "2090", "2091", "2092", "2093", "2094", "2095", "2096", "2097", "2098", "2099", "2100", "2101", "2102", "2103", "2104", "2105", "2106", "2107", "2108", "2109", "2110", "2111", "2112", "2113", "2114", "2115", "2116", "2117", "2118", "2119", "2120", "2121", "2122", "2123", "2124", "2125", "2126", "2127", "2128", "2129", "2130", "2131", "2132", "2133", "2134", "2135", "2136", "2137", "2138", "2139", "2140", "2141", "2142", "2143", "2144"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\analytics\\BusinessPredictions.tsx", ["2145", "2146", "2147"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\Financial.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\analytics\\BusinessAnalysisDashboard.tsx", ["2148", "2149", "2150", "2151", "2152", "2153", "2154", "2155", "2156", "2157", "2158", "2159", "2160", "2161", "2162", "2163", "2164", "2165", "2166", "2167", "2168", "2169", "2170", "2171", "2172", "2173", "2174", "2175", "2176", "2177"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\analytics\\BusinessStrategy.tsx", ["2178", "2179", "2180", "2181", "2182", "2183", "2184", "2185", "2186", "2187", "2188", "2189"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\Health.tsx", ["2190", "2191"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\HealthDashboard.tsx", ["2192", "2193", "2194", "2195", "2196", "2197", "2198", "2199", "2200", "2201", "2202", "2203", "2204", "2205", "2206", "2207", "2208"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\FeedingDashboard.tsx", ["2209", "2210", "2211", "2212", "2213", "2214", "2215", "2216", "2217", "2218", "2219", "2220", "2221"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\FinancialDashboard.tsx", ["2222", "2223", "2224", "2225", "2226", "2227", "2228"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AssetManagement.tsx", ["2229", "2230"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\Animals.tsx", ["2231", "2232", "2233"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AnimalsDashboard.tsx", ["2234", "2235", "2236", "2237", "2238", "2239", "2240", "2241", "2242", "2243", "2244", "2245", "2246", "2247", "2248", "2249", "2250", "2251"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\dashboard\\Dashboard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\commercial\\Commercial.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\ErrorPage\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\offlineSync.ts", ["2252"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\initMongoDb.ts", ["2253", "2254", "2255"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\mockMongoDbClient.ts", ["2256", "2257"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\syncMockDataToMongoDB.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\cacheUtils.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\breedingMongoService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\healthMongoService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\CustomButton.tsx", ["2258"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\dataIntegrationService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\mongoService.ts", ["2259"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\api.ts", ["2260"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useAnimalData.ts", ["2261"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useBreedingRecords.ts", ["2262"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useFinancialData.ts", ["2263", "2264", "2265"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useHealthRecords.ts", ["2266", "2267"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useCommercialData.ts", ["2268", "2269"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useFeedingData.ts", ["2270"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useInventoryData.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useComplianceData.ts", ["2271", "2272", "2273"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useResourcesData.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\AlertSystem.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\QuickActions.tsx", ["2274", "2275", "2276"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\NotificationsPanel.tsx", ["2277", "2278"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\GlobalSearch.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\WeatherWidget.tsx", ["2279", "2280"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\GlobalBackground.tsx", ["2281", "2282"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\GlobalTabSelectionFixer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\iconImports.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\compliance\\Inspections.tsx", ["2283", "2284", "2285", "2286", "2287", "2288", "2289", "2290", "2291", "2292"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\compliance\\ComplianceDashboard.tsx", ["2293", "2294", "2295", "2296", "2297", "2298", "2299", "2300", "2301", "2302", "2303", "2304", "2305", "2306", "2307", "2308", "2309", "2310", "2311", "2312", "2313", "2314", "2315", "2316", "2317", "2318"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\compliance\\Documents.tsx", ["2319"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\ResourceMaintenance.tsx", ["2320"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\ResourceDetail.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\compliance\\Certifications.tsx", ["2321", "2322", "2323", "2324", "2325", "2326"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\ResourceForm.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\ResourcesList.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\ResourceUtilization.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\Training.tsx", ["2327", "2328", "2329", "2330", "2331", "2332", "2333", "2334", "2335", "2336", "2337", "2338", "2339"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\Guidelines.tsx", ["2340", "2341", "2342", "2343", "2344", "2345", "2346", "2347", "2348", "2349", "2350", "2351", "2352"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\Support.tsx", ["2353", "2354", "2355", "2356", "2357", "2358", "2359", "2360", "2361", "2362", "2363"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\Documentation.tsx", ["2364", "2365", "2366", "2367", "2368", "2369", "2370", "2371", "2372", "2373", "2374"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\GovernmentResources.tsx", ["2375", "2376", "2377", "2378", "2379"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\userMongoService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\resources\\Downloads.tsx", ["2380", "2381", "2382", "2383", "2384", "2385", "2386", "2387", "2388", "2389"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\applyTabSelectionFixes.ts", ["2390", "2391", "2392"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModuleContainer.tsx", ["2393"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useResponsive.ts", ["2394", "2395", "2396", "2397"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\errorHandling.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\reportService.ts", ["2398", "2399", "2400", "2401", "2402", "2403", "2404", "2405", "2406", "2407", "2408", "2409"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\compliance\\BrandmarksCertificates.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\formatters.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\cardStyles.ts", ["2410"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\mockFeedingData.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\reportUtils.ts", ["2411", "2412"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useReportSelection.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\ReportPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\mockFinancialData.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\applyReportSelectionFixes.ts", ["2413"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\FeedingRecords.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\Inventory.tsx", ["2414", "2415", "2416", "2417", "2418", "2419", "2420", "2421", "2422", "2423", "2424", "2425", "2426", "2427", "2428", "2429", "2430", "2431", "2432", "2433", "2434", "2435", "2436", "2437", "2438", "2439"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\FeedingNutrition.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\FeedingPlans.tsx", ["2440", "2441", "2442", "2443", "2444", "2445", "2446", "2447", "2448"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\Suppliers.tsx", ["2449", "2450", "2451", "2452", "2453", "2454"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\breeding\\PregnancyTracking.tsx", ["2455", "2456", "2457", "2458"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\FeedingSchedules.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\breeding\\HeatCalendar.tsx", ["2459", "2460", "2461", "2462", "2463", "2464", "2465", "2466", "2467", "2468", "2469", "2470"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useBusinessPredictions.ts", ["2471", "2472", "2473", "2474", "2475", "2476"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\breeding\\BreedingSchedule.tsx", ["2477", "2478"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\breeding\\BreedingRecords.tsx", ["2479", "2480", "2481", "2482", "2483"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\FinancialTransactions.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\FinancialReports.tsx", ["2484", "2485", "2486", "2487"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\breeding\\BirthPredictions.tsx", ["2488", "2489", "2490", "2491", "2492", "2493", "2494", "2495", "2496"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\FinancialBudgets.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\ROI.tsx", ["2497", "2498", "2499", "2500", "2501", "2502", "2503", "2504", "2505", "2506", "2507", "2508", "2509", "2510", "2511"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useBusinessStrategy.ts", ["2512"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\FinancialOverview.tsx", ["2513", "2514", "2515", "2516", "2517", "2518", "2519", "2520", "2521", "2522", "2523", "2524"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\Forecast.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\Invoices.tsx", ["2525"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\Diseases.tsx", ["2526", "2527", "2528", "2529", "2530", "2531", "2532"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\Vaccinations.tsx", ["2533", "2534", "2535", "2536", "2537", "2538", "2539", "2540"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\Appointments.tsx", ["2541", "2542", "2543", "2544", "2545", "2546", "2547", "2548", "2549", "2550"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\Treatments.tsx", ["2551", "2552", "2553", "2554", "2555", "2556"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\Records.tsx", ["2557", "2558", "2559", "2560", "2561", "2562", "2563", "2564", "2565", "2566", "2567", "2568", "2569", "2570", "2571", "2572", "2573", "2574", "2575", "2576", "2577", "2578", "2579", "2580"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\admin\\DataMigration.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\settings\\EnhancedThemeSettings.tsx", ["2581", "2582", "2583", "2584", "2585", "2586", "2587", "2588", "2589", "2590", "2591", "2592", "2593", "2594", "2595", "2596", "2597", "2598", "2599", "2600", "2601", "2602", "2603"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\reports\\ReportCard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\reports\\ReportButton.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\reports\\ReportsModuleWrapper.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\reports\\HtmlReportViewer.tsx", ["2604"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\breedingData.ts", ["2605", "2606", "2607", "2608"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\analytics\\BusinessPredictionCard.tsx", ["2609", "2610"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\analytics\\BusinessAnalyticsButton.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\analytics\\BusinessInsightCard.tsx", ["2611", "2612"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\analytics\\BusinessAnalyticsCard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\analytics\\BusinessStrategyCard.tsx", ["2613", "2614"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\HealthRecords.tsx", ["2615", "2616"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\RetirementTracking.tsx", ["2617", "2618", "2619", "2620", "2621", "2622", "2623", "2624", "2625", "2626", "2627", "2628", "2629", "2630"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useMongoAnimalData.ts", ["2631", "2632", "2633", "2634", "2635", "2636", "2637"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AssetManagementDashboard.tsx", ["2638", "2639", "2640", "2641", "2642", "2643", "2644"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AnimalsList.tsx", ["2645", "2646", "2647", "2648", "2649", "2650", "2651"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AnimalDetail.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AnimalForm.tsx", ["2652"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\feeding\\FeedingRecordModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\feeding\\FeedInventoryModal.tsx", ["2653", "2654"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\Profiles.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\Tracking.tsx", ["2655", "2656", "2657", "2658", "2659"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\Records.tsx", ["2660"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\RFID.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\Genealogy.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\commercial\\Pricing.tsx", ["2661", "2662", "2663", "2664", "2665", "2666", "2667"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\commercial\\PriceUpdates.tsx", ["2668"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\commercial\\Auctions.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\dashboard\\ModernDashboard.tsx", ["2669", "2670", "2671", "2672", "2673", "2674", "2675", "2676", "2677", "2678", "2679", "2680", "2681"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\commercial\\SupplierDetail.tsx", ["2682", "2683", "2684", "2685", "2686"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\animals\\AnimalMarketplaceGrid.tsx", ["2687", "2688"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\commercial\\Marketplace.tsx", ["2689", "2690", "2691", "2692", "2693", "2694", "2695"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\commercial\\Orders.tsx", ["2696", "2697", "2698", "2699", "2700", "2701"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\commercial\\Suppliers.tsx", ["2702", "2703", "2704", "2705", "2706", "2707", "2708", "2709", "2710", "2711", "2712", "2713", "2714", "2715", "2716", "2717", "2718", "2719", "2720", "2721", "2722", "2723", "2724", "2725", "2726", "2727"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\DesignSystem.tsx", ["2728", "2729"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\commercial\\CommercialDashboard.tsx", ["2730", "2731", "2732", "2733", "2734", "2735", "2736", "2737", "2738", "2739", "2740", "2741", "2742", "2743", "2744", "2745", "2746", "2747", "2748", "2749", "2750", "2751", "2752", "2753", "2754", "2755", "2756", "2757", "2758", "2759"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\EnhancedDataTable.tsx", ["2760", "2761"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\AnimatedBackgroundCard.tsx", ["2762", "2763", "2764"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\EnhancedCard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\commercial\\BkbAuctionContext.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\AnimatedDashboardCard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModuleHeader.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\AnimatedChart.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ResponsiveDashboard.tsx", ["2765", "2766"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SponsorCarousel.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\EnhancedSponsorCarousel.tsx", ["2767"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ResponsiveDataCard.tsx", ["2768"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\FarmerResourceLinks.tsx", ["2769", "2770", "2771", "2772", "2773"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ResponsiveFormLayout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ResponsiveChartContainer.tsx", ["2774"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ResponsiveNavTabs.tsx", ["2775", "2776"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ConsistentChart.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\LazyLoadFallback.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ErrorBoundary.tsx", ["2777"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\OfflineIndicator.tsx", ["2778"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\AnimatedBackground.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\MongoDbStatus.tsx", ["2779"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\StatusIndicator.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\VirtualizedTable.tsx", ["2780"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\LazyImage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\DataCard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ResponsiveLayout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\StyledTable.tsx", ["2781"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\BlendedBackgroundCard.tsx", ["2782"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\RotatingBackground.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\FeaturedDealCard.tsx", ["2783"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ProductCard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModuleItemCard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\CommercialBackgroundCard.tsx", ["2784"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModuleHeaderCard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModuleCard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModuleContentCard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModuleBackgroundRotator.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\LoadingOverlay.tsx", ["2785"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\CalOverlay.tsx", ["2786", "2787", "2788", "2789", "2790", "2791", "2792", "2793"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\DashboardCard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModernDashboard.tsx", ["2794", "2795", "2796", "2797", "2798", "2799", "2800", "2801"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModernCard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModernChart.tsx", ["2802", "2803", "2804"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModernDataTable.tsx", ["2805", "2806", "2807", "2808", "2809", "2810", "2811"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\withSubModuleTranslation.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\FallbackUI.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\StandardDashboard.tsx", ["2812"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SubModuleMetricsCard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SubModuleDashboard.tsx", ["2813", "2814", "2815", "2816", "2817"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SubModuleActionPanel.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\EnhancedChart.tsx", ["2818", "2819", "2820", "2821", "2822"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SubModuleDataTable.tsx", ["2823", "2824"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SubModuleReport.tsx", ["2825", "2826"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SubModuleChart.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\UnifiedModuleContainer.tsx", ["2827", "2828"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModulePageWrapper.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ThemeSafeWrapper.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SafeButton.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ThemeSafeButton.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ResizeObserverFix.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\PlainButton.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\mongoDbClient.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\healthData.ts", ["2829", "2830", "2831"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\animalData.ts", ["2832", "2833", "2834", "2835", "2836", "2837", "2838", "2839", "2840", "2841", "2842", "2843", "2844", "2845"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\feedingData.ts", ["2846"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SimpleButton.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\mockMongoDb.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\MuiButtonFix.tsx", ["2847"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\SafeThemeProvider.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\inventoryData.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\GlobalSafeButton.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\FixedThemeProvider.tsx", ["2848"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\DirectFixButton.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\FinalButtonReplacer.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\mongoDbService.ts", ["2849"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\FinalMuiButtonFix.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\FinalFixButton.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\financialData.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ButtonReplacer.tsx", ["2850"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\MainNavigationFix.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\CrudComponent.tsx", ["2851", "2852", "2853"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModuleDashboard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\StandardForm.tsx", ["2854", "2855", "2856", "2857", "2858", "2859"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\PageBackground.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\DataTable.tsx", ["2860", "2861", "2862", "2863"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useDataFetching.ts", [], ["2864", "2865"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\ErrorPage\\ErrorPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\ModulePage.tsx", ["2866", "2867", "2868"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\commercialData.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\export.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\config.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\apiService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\constants\\routes.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\design\\DesignSystem.tsx", ["2869", "2870", "2871"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\snackbar.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useAccessibility.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\moduleStyles.ts", ["2872"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\apiRoutes.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\reports\\ReportGenerator.tsx", ["2873"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\config\\api.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\tabSelectionUtils.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\businessAnalysisService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useTabSelection.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useFeedingNutrition.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\smsService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useSnackbar.ts", ["2874"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\notificationHistoryService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\notificationService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\feeding\\FeedingRecordsList.tsx", ["2875", "2876"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\breeding\\BreedingRecordModal.tsx", ["2877"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\breeding\\NotificationConfig.tsx", ["2878"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\breeding\\NotificationHistory.tsx", ["2879", "2880", "2881", "2882", "2883", "2884", "2885", "2886", "2887"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\breeding\\TestNotification.tsx", ["2888", "2889", "2890", "2891", "2892"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useBusinessAnalytics.ts", ["2893", "2894", "2895", "2896", "2897", "2898", "2899"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\designEnhancements.ts", ["2900"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\selectionUtils.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\dataMigration.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\mockDataGenerator.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useReportItemSelection.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\animalMongoService.ts", ["2901"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useAlert.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\commercialUtils.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\backgroundThemes.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\RFIDTrackingPage.tsx", ["2902"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\contexts\\SupplierContext.tsx", ["2903"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\animals\\AnimalQuickView.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\animals\\RealTimeTracking.tsx", ["2904", "2905"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\animals\\AnimalMarketplaceCard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\health\\HealthRecordModal.tsx", ["2906", "2907", "2908", "2909"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\maps\\GoogleMapComponent.tsx", ["2910"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\commercial\\AuctionModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\commercial\\SupplierModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\commercial\\BkbAuctionCalendar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\commercial\\OrderModal.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\WeatherWidget.tsx", ["2911", "2912", "2913"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\SponsorRotator.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\QuickActions.tsx", ["2914", "2915", "2916", "2917", "2918"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\TaskManager.tsx", ["2919", "2920", "2921", "2922", "2923", "2924", "2925"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\NotificationCenter.tsx", ["2926"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\commercial\\MarketForecast.tsx", ["2927"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\commercial\\MarketplaceSuppliers.tsx", ["2928", "2929", "2930", "2931", "2932", "2933", "2934"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\modules\\commercial\\MarketplaceListingDetail.tsx", ["2935", "2936"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\mockMongoDbService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\applyDesignPattern.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\CardStyleUtils.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\themeDefaults.ts", ["2937"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\localImages.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\resizeObserverFix.ts", ["2938"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\translationHelpers.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\colorBlendingUtils.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\dashboardStyles.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\muiButtonFix.ts", ["2939"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\patchMuiButton.ts", ["2940"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\monkeyPatchMuiButton.ts", ["2941"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\commercial\\BkbAuctionService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\finalMuiButtonFix.js", ["2942", "2943", "2944", "2945"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\analyticsData.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useApiError.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\reportsData.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\rfidData.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\dashboardData.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\supplierData.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\pastureMgmtData.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\useTranslation.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\businessAnalysisData.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\ruralNotificationService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\testNotificationService.ts", ["2946", "2947"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\animals\\RFIDManagement.tsx", ["2948"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\businessAnalyticsService.ts", ["2949", "2950", "2951", "2952", "2953", "2954", "2955"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\animals\\RFIDTracking.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\animals\\AnimalStatusBadge.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\supplierService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\commercial\\BkbAuctionFilters.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\models\\AnimalModel.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\mocks\\mockData.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\core\\ServiceRegistry.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\core\\BaseService.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\hooks\\usePerformance.ts", ["2956"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\utils\\performanceMonitor.ts", ["2957", "2958"], [], {"ruleId": "2959", "severity": 1, "message": "2960", "line": 6, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 6, "endColumn": 12}, {"ruleId": "2963", "severity": 1, "message": "2964", "line": 123, "column": 13, "nodeType": "2965", "endLine": 126, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "2966", "line": 24, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 24, "endColumn": 26}, {"ruleId": "2959", "severity": 1, "message": "2967", "line": 2, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 24}, {"ruleId": "2959", "severity": 1, "message": "2960", "line": 22, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 12}, {"ruleId": "2968", "severity": 1, "message": "2969", "line": 163, "column": 1, "nodeType": "2970", "endLine": 174, "endColumn": 3}, {"ruleId": "2971", "severity": 1, "message": "2972", "line": 74, "column": 6, "nodeType": "2973", "endLine": 74, "endColumn": 22, "suggestions": "2974"}, {"ruleId": "2959", "severity": 1, "message": "2975", "line": 96, "column": 15, "nodeType": "2961", "messageId": "2962", "endLine": 96, "endColumn": 26}, {"ruleId": "2971", "severity": 1, "message": "2976", "line": 146, "column": 6, "nodeType": "2973", "endLine": 146, "endColumn": 8, "suggestions": "2977"}, {"ruleId": "2959", "severity": 1, "message": "2978", "line": 2, "column": 53, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 72}, {"ruleId": "2971", "severity": 1, "message": "2979", "line": 85, "column": 6, "nodeType": "2973", "endLine": 85, "endColumn": 8, "suggestions": "2980"}, {"ruleId": "2959", "severity": 1, "message": "2981", "line": 1, "column": 44, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 52}, {"ruleId": "2959", "severity": 1, "message": "2982", "line": 1, "column": 54, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 63}, {"ruleId": "2971", "severity": 1, "message": "2983", "line": 83, "column": 6, "nodeType": "2973", "endLine": 83, "endColumn": 8, "suggestions": "2984"}, {"ruleId": "2971", "severity": 1, "message": "2985", "line": 1029, "column": 6, "nodeType": "2973", "endLine": 1029, "endColumn": 39, "suggestions": "2986"}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 15, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 15, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "2988", "line": 20, "column": 19, "nodeType": "2961", "messageId": "2962", "endLine": 20, "endColumn": 28}, {"ruleId": "2959", "severity": 1, "message": "2989", "line": 50, "column": 22, "nodeType": "2961", "messageId": "2962", "endLine": 50, "endColumn": 30}, {"ruleId": "2959", "severity": 1, "message": "2990", "line": 50, "column": 32, "nodeType": "2961", "messageId": "2962", "endLine": 50, "endColumn": 46}, {"ruleId": "2959", "severity": 1, "message": "2991", "line": 54, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 54, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "2992", "line": 30, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 30, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "2993", "line": 30, "column": 25, "nodeType": "2961", "messageId": "2962", "endLine": 30, "endColumn": 40}, {"ruleId": "2959", "severity": 1, "message": "2994", "line": 4, "column": 161, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 171}, {"ruleId": "2959", "severity": 1, "message": "2995", "line": 12, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 12, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "2996", "line": 13, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 13, "endColumn": 6}, {"ruleId": "2959", "severity": 1, "message": "2997", "line": 14, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 9}, {"ruleId": "2959", "severity": 1, "message": "2998", "line": 58, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 58, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "2999", "line": 78, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 78, "endColumn": 29}, {"ruleId": "2959", "severity": 1, "message": "3000", "line": 93, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 93, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3001", "line": 4, "column": 37, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 45}, {"ruleId": "2959", "severity": 1, "message": "3002", "line": 5, "column": 42, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 57}, {"ruleId": "2959", "severity": 1, "message": "3003", "line": 11, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3004", "line": 49, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 49, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3005", "line": 2, "column": 27, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 32}, {"ruleId": "2959", "severity": 1, "message": "3006", "line": 2, "column": 163, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 172}, {"ruleId": "3007", "severity": 1, "message": "3008", "line": 52, "column": 57, "nodeType": "3009", "messageId": "3010", "endLine": 52, "endColumn": 58}, {"ruleId": "2959", "severity": 1, "message": "3011", "line": 1, "column": 27, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 33}, {"ruleId": "2959", "severity": 1, "message": "3006", "line": 3, "column": 95, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 104}, {"ruleId": "2959", "severity": 1, "message": "3012", "line": 3, "column": 304, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 308}, {"ruleId": "2959", "severity": 1, "message": "3013", "line": 3, "column": 310, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 324}, {"ruleId": "2959", "severity": 1, "message": "3014", "line": 10, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3015", "line": 11, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 12}, {"ruleId": "2959", "severity": 1, "message": "3016", "line": 13, "column": 15, "nodeType": "2961", "messageId": "2962", "endLine": 13, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3017", "line": 14, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3018", "line": 18, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 18, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3019", "line": 19, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 19, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3020", "line": 20, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 20, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3021", "line": 21, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 21, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3022", "line": 30, "column": 29, "nodeType": "2961", "messageId": "2962", "endLine": 30, "endColumn": 47}, {"ruleId": "2959", "severity": 1, "message": "3023", "line": 33, "column": 63, "nodeType": "2961", "messageId": "2962", "endLine": 33, "endColumn": 75}, {"ruleId": "2959", "severity": 1, "message": "3003", "line": 72, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 72, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3024", "line": 2, "column": 33, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 37}, {"ruleId": "2959", "severity": 1, "message": "3025", "line": 2, "column": 39, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 50}, {"ruleId": "2959", "severity": 1, "message": "3006", "line": 2, "column": 191, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 200}, {"ruleId": "2959", "severity": 1, "message": "2994", "line": 2, "column": 202, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 212}, {"ruleId": "2959", "severity": 1, "message": "3026", "line": 2, "column": 214, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 221}, {"ruleId": "2959", "severity": 1, "message": "3027", "line": 15, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 15, "endColumn": 12}, {"ruleId": "2959", "severity": 1, "message": "3028", "line": 16, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 16, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3029", "line": 29, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 29, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3017", "line": 30, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 30, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3030", "line": 33, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 33, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3031", "line": 34, "column": 15, "nodeType": "2961", "messageId": "2962", "endLine": 34, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3016", "line": 35, "column": 15, "nodeType": "2961", "messageId": "2962", "endLine": 35, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3032", "line": 36, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 36, "endColumn": 12}, {"ruleId": "2959", "severity": 1, "message": "3033", "line": 44, "column": 49, "nodeType": "2961", "messageId": "2962", "endLine": 44, "endColumn": 70}, {"ruleId": "2959", "severity": 1, "message": "3034", "line": 47, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 47, "endColumn": 24}, {"ruleId": "2959", "severity": 1, "message": "3035", "line": 47, "column": 26, "nodeType": "2961", "messageId": "2962", "endLine": 47, "endColumn": 36}, {"ruleId": "2959", "severity": 1, "message": "3036", "line": 47, "column": 38, "nodeType": "2961", "messageId": "2962", "endLine": 47, "endColumn": 50}, {"ruleId": "2959", "severity": 1, "message": "3037", "line": 77, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 77, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3038", "line": 77, "column": 20, "nodeType": "2961", "messageId": "2962", "endLine": 77, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3039", "line": 77, "column": 36, "nodeType": "2961", "messageId": "2962", "endLine": 77, "endColumn": 50}, {"ruleId": "2959", "severity": 1, "message": "3040", "line": 78, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 78, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3041", "line": 78, "column": 32, "nodeType": "2961", "messageId": "2962", "endLine": 78, "endColumn": 46}, {"ruleId": "2959", "severity": 1, "message": "3042", "line": 78, "column": 57, "nodeType": "2961", "messageId": "2962", "endLine": 78, "endColumn": 73}, {"ruleId": "2959", "severity": 1, "message": "3043", "line": 3, "column": 141, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 146}, {"ruleId": "2959", "severity": 1, "message": "3006", "line": 3, "column": 191, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 200}, {"ruleId": "2959", "severity": 1, "message": "3026", "line": 3, "column": 214, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 221}, {"ruleId": "2959", "severity": 1, "message": "3027", "line": 16, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 16, "endColumn": 12}, {"ruleId": "2959", "severity": 1, "message": "3028", "line": 17, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 17, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3044", "line": 18, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 18, "endColumn": 12}, {"ruleId": "2959", "severity": 1, "message": "3045", "line": 19, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 19, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3014", "line": 21, "column": 20, "nodeType": "2961", "messageId": "2962", "endLine": 21, "endColumn": 30}, {"ruleId": "2959", "severity": 1, "message": "3029", "line": 21, "column": 45, "nodeType": "2961", "messageId": "2962", "endLine": 21, "endColumn": 55}, {"ruleId": "2959", "severity": 1, "message": "3016", "line": 21, "column": 69, "nodeType": "2961", "messageId": "2962", "endLine": 21, "endColumn": 81}, {"ruleId": "2959", "severity": 1, "message": "3046", "line": 21, "column": 97, "nodeType": "2961", "messageId": "2962", "endLine": 21, "endColumn": 111}, {"ruleId": "2959", "severity": 1, "message": "3023", "line": 26, "column": 63, "nodeType": "2961", "messageId": "2962", "endLine": 26, "endColumn": 75}, {"ruleId": "2959", "severity": 1, "message": "3004", "line": 39, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 39, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "3005", "line": 4, "column": 200, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 205}, {"ruleId": "2959", "severity": 1, "message": "3029", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3047", "line": 11, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3048", "line": 22, "column": 67, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 79}, {"ruleId": "2959", "severity": 1, "message": "3049", "line": 189, "column": 5, "nodeType": "2961", "messageId": "2962", "endLine": 189, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "3050", "line": 191, "column": 5, "nodeType": "2961", "messageId": "2962", "endLine": 191, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3051", "line": 193, "column": 5, "nodeType": "2961", "messageId": "2962", "endLine": 193, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3013", "line": 4, "column": 78, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 92}, {"ruleId": "2959", "severity": 1, "message": "3052", "line": 4, "column": 134, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 148}, {"ruleId": "2959", "severity": 1, "message": "3026", "line": 4, "column": 237, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 244}, {"ruleId": "2959", "severity": 1, "message": "3005", "line": 4, "column": 246, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 251}, {"ruleId": "2959", "severity": 1, "message": "3053", "line": 4, "column": 253, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 256}, {"ruleId": "2959", "severity": 1, "message": "3054", "line": 4, "column": 258, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 262}, {"ruleId": "2959", "severity": 1, "message": "3055", "line": 4, "column": 264, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 268}, {"ruleId": "2959", "severity": 1, "message": "3056", "line": 4, "column": 270, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 278}, {"ruleId": "2959", "severity": 1, "message": "3057", "line": 4, "column": 280, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 291}, {"ruleId": "2959", "severity": 1, "message": "3058", "line": 4, "column": 293, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 303}, {"ruleId": "2959", "severity": 1, "message": "3059", "line": 4, "column": 305, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 311}, {"ruleId": "2959", "severity": 1, "message": "3006", "line": 4, "column": 313, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 322}, {"ruleId": "2959", "severity": 1, "message": "3060", "line": 4, "column": 324, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 340}, {"ruleId": "2959", "severity": 1, "message": "3061", "line": 4, "column": 353, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 363}, {"ruleId": "2959", "severity": 1, "message": "3062", "line": 4, "column": 365, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 380}, {"ruleId": "2959", "severity": 1, "message": "3063", "line": 4, "column": 382, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 387}, {"ruleId": "2959", "severity": 1, "message": "3064", "line": 4, "column": 389, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 394}, {"ruleId": "2959", "severity": 1, "message": "3065", "line": 4, "column": 396, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 404}, {"ruleId": "2959", "severity": 1, "message": "3066", "line": 7, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 7, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3067", "line": 8, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 8, "endColumn": 12}, {"ruleId": "2959", "severity": 1, "message": "3019", "line": 15, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 15, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3068", "line": 18, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 18, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3020", "line": 19, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 19, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3069", "line": 21, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 21, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3070", "line": 22, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3016", "line": 23, "column": 15, "nodeType": "2961", "messageId": "2962", "endLine": 23, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3017", "line": 24, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 24, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3071", "line": 25, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 25, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3021", "line": 26, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 26, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3072", "line": 27, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 27, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3029", "line": 29, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 29, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3073", "line": 30, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 30, "endColumn": 15}, {"ruleId": "2959", "severity": 1, "message": "3074", "line": 31, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 31, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3075", "line": 32, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 32, "endColumn": 15}, {"ruleId": "2959", "severity": 1, "message": "3076", "line": 35, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 35, "endColumn": 12}, {"ruleId": "2959", "severity": 1, "message": "3077", "line": 37, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 37, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3078", "line": 38, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 38, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3079", "line": 40, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 40, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3080", "line": 41, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 41, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3081", "line": 42, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 42, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3082", "line": 43, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 43, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3083", "line": 44, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 44, "endColumn": 12}, {"ruleId": "2959", "severity": 1, "message": "3084", "line": 45, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 45, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3047", "line": 48, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 48, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3085", "line": 49, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 49, "endColumn": 6}, {"ruleId": "2959", "severity": 1, "message": "3086", "line": 50, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 50, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3087", "line": 51, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 51, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "3088", "line": 52, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 52, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3089", "line": 53, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 53, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3090", "line": 54, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 54, "endColumn": 12}, {"ruleId": "2959", "severity": 1, "message": "3091", "line": 55, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 55, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3092", "line": 56, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 56, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3093", "line": 57, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 57, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3094", "line": 58, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 58, "endColumn": 9}, {"ruleId": "2959", "severity": 1, "message": "3095", "line": 59, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 59, "endColumn": 6}, {"ruleId": "2959", "severity": 1, "message": "3096", "line": 60, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 60, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3097", "line": 61, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 61, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3098", "line": 62, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 62, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3027", "line": 63, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 63, "endColumn": 12}, {"ruleId": "2959", "severity": 1, "message": "3028", "line": 64, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 64, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3044", "line": 65, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 65, "endColumn": 12}, {"ruleId": "2959", "severity": 1, "message": "3045", "line": 66, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 66, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3099", "line": 67, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 67, "endColumn": 15}, {"ruleId": "2959", "severity": 1, "message": "3100", "line": 68, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 68, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3101", "line": 69, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 69, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3102", "line": 71, "column": 22, "nodeType": "2961", "messageId": "2962", "endLine": 71, "endColumn": 38}, {"ruleId": "2959", "severity": 1, "message": "3103", "line": 72, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 72, "endColumn": 37}, {"ruleId": "2959", "severity": 1, "message": "3104", "line": 73, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 73, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3105", "line": 73, "column": 49, "nodeType": "2961", "messageId": "2962", "endLine": 73, "endColumn": 64}, {"ruleId": "2959", "severity": 1, "message": "3106", "line": 73, "column": 66, "nodeType": "2961", "messageId": "2962", "endLine": 73, "endColumn": 82}, {"ruleId": "2959", "severity": 1, "message": "3033", "line": 73, "column": 84, "nodeType": "2961", "messageId": "2962", "endLine": 73, "endColumn": 105}, {"ruleId": "2959", "severity": 1, "message": "3107", "line": 73, "column": 107, "nodeType": "2961", "messageId": "2962", "endLine": 73, "endColumn": 124}, {"ruleId": "2959", "severity": 1, "message": "3108", "line": 75, "column": 56, "nodeType": "2961", "messageId": "2962", "endLine": 75, "endColumn": 72}, {"ruleId": "2959", "severity": 1, "message": "3109", "line": 75, "column": 74, "nodeType": "2961", "messageId": "2962", "endLine": 75, "endColumn": 83}, {"ruleId": "2959", "severity": 1, "message": "3110", "line": 75, "column": 85, "nodeType": "2961", "messageId": "2962", "endLine": 75, "endColumn": 94}, {"ruleId": "2959", "severity": 1, "message": "3111", "line": 75, "column": 96, "nodeType": "2961", "messageId": "2962", "endLine": 75, "endColumn": 103}, {"ruleId": "2959", "severity": 1, "message": "3112", "line": 109, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 109, "endColumn": 15}, {"ruleId": "2959", "severity": 1, "message": "3113", "line": 148, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 148, "endColumn": 15}, {"ruleId": "2959", "severity": 1, "message": "3114", "line": 153, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 153, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "3115", "line": 153, "column": 21, "nodeType": "2961", "messageId": "2962", "endLine": 153, "endColumn": 33}, {"ruleId": "2959", "severity": 1, "message": "3116", "line": 154, "column": 26, "nodeType": "2961", "messageId": "2962", "endLine": 154, "endColumn": 43}, {"ruleId": "2959", "severity": 1, "message": "3117", "line": 156, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 156, "endColumn": 30}, {"ruleId": "2959", "severity": 1, "message": "3118", "line": 156, "column": 32, "nodeType": "2961", "messageId": "2962", "endLine": 156, "endColumn": 55}, {"ruleId": "2959", "severity": 1, "message": "3119", "line": 157, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 157, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3120", "line": 157, "column": 25, "nodeType": "2961", "messageId": "2962", "endLine": 157, "endColumn": 41}, {"ruleId": "2959", "severity": 1, "message": "3121", "line": 158, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 158, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "3122", "line": 158, "column": 24, "nodeType": "2961", "messageId": "2962", "endLine": 158, "endColumn": 39}, {"ruleId": "2959", "severity": 1, "message": "3123", "line": 159, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 159, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3124", "line": 159, "column": 25, "nodeType": "2961", "messageId": "2962", "endLine": 159, "endColumn": 41}, {"ruleId": "2959", "severity": 1, "message": "3125", "line": 162, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 162, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3126", "line": 163, "column": 24, "nodeType": "2961", "messageId": "2962", "endLine": 163, "endColumn": 39}, {"ruleId": "2959", "severity": 1, "message": "3127", "line": 166, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 166, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3128", "line": 167, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 167, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3129", "line": 170, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 170, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "3130", "line": 170, "column": 21, "nodeType": "2961", "messageId": "2962", "endLine": 170, "endColumn": 33}, {"ruleId": "2959", "severity": 1, "message": "3131", "line": 176, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 176, "endColumn": 26}, {"ruleId": "2959", "severity": 1, "message": "3132", "line": 176, "column": 28, "nodeType": "2961", "messageId": "2962", "endLine": 176, "endColumn": 47}, {"ruleId": "2959", "severity": 1, "message": "3133", "line": 215, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 215, "endColumn": 26}, {"ruleId": "2959", "severity": 1, "message": "3134", "line": 220, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 220, "endColumn": 24}, {"ruleId": "2959", "severity": 1, "message": "3135", "line": 229, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 229, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3136", "line": 235, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 235, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3137", "line": 239, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 239, "endColumn": 32}, {"ruleId": "2959", "severity": 1, "message": "3138", "line": 264, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 264, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "3139", "line": 265, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 265, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3140", "line": 266, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 266, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3141", "line": 268, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 268, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3142", "line": 270, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 270, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3143", "line": 274, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 274, "endColumn": 36}, {"ruleId": "2959", "severity": 1, "message": "3144", "line": 275, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 275, "endColumn": 31}, {"ruleId": "2959", "severity": 1, "message": "3145", "line": 276, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 276, "endColumn": 31}, {"ruleId": "2959", "severity": 1, "message": "3146", "line": 389, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 389, "endColumn": 29}, {"ruleId": "2959", "severity": 1, "message": "3147", "line": 392, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 392, "endColumn": 36}, {"ruleId": "2959", "severity": 1, "message": "3148", "line": 400, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 400, "endColumn": 31}, {"ruleId": "2959", "severity": 1, "message": "3149", "line": 408, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 408, "endColumn": 31}, {"ruleId": "2959", "severity": 1, "message": "3150", "line": 415, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 415, "endColumn": 31}, {"ruleId": "2959", "severity": 1, "message": "3151", "line": 425, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 425, "endColumn": 29}, {"ruleId": "2959", "severity": 1, "message": "3152", "line": 433, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 433, "endColumn": 32}, {"ruleId": "2959", "severity": 1, "message": "3153", "line": 449, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 449, "endColumn": 28}, {"ruleId": "2959", "severity": 1, "message": "3154", "line": 458, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 458, "endColumn": 15}, {"ruleId": "2959", "severity": 1, "message": "3026", "line": 11, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3155", "line": 31, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 31, "endColumn": 29}, {"ruleId": "2959", "severity": 1, "message": "3156", "line": 38, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 38, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "2982", "line": 1, "column": 27, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 36}, {"ruleId": "2959", "severity": 1, "message": "3057", "line": 7, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 7, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3058", "line": 8, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 8, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3059", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 9}, {"ruleId": "2959", "severity": 1, "message": "3056", "line": 10, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3026", "line": 14, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3157", "line": 16, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 16, "endColumn": 12}, {"ruleId": "2959", "severity": 1, "message": "3014", "line": 37, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 37, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3016", "line": 39, "column": 15, "nodeType": "2961", "messageId": "2962", "endLine": 39, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3031", "line": 40, "column": 15, "nodeType": "2961", "messageId": "2962", "endLine": 40, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3017", "line": 41, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 41, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3021", "line": 42, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 42, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3072", "line": 43, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 43, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3069", "line": 50, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 50, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3046", "line": 53, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 53, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3032", "line": 55, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 55, "endColumn": 12}, {"ruleId": "2959", "severity": 1, "message": "3158", "line": 56, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 56, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3159", "line": 74, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 74, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3160", "line": 135, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 135, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3156", "line": 149, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 149, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3114", "line": 150, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 150, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "3161", "line": 151, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 151, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "3162", "line": 151, "column": 21, "nodeType": "2961", "messageId": "2962", "endLine": 151, "endColumn": 33}, {"ruleId": "2959", "severity": 1, "message": "3163", "line": 157, "column": 37, "nodeType": "2961", "messageId": "2962", "endLine": 157, "endColumn": 52}, {"ruleId": "2959", "severity": 1, "message": "3164", "line": 158, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 158, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3165", "line": 158, "column": 36, "nodeType": "2961", "messageId": "2962", "endLine": 158, "endColumn": 50}, {"ruleId": "2959", "severity": 1, "message": "3166", "line": 189, "column": 34, "nodeType": "2961", "messageId": "2962", "endLine": 189, "endColumn": 49}, {"ruleId": "2959", "severity": 1, "message": "3167", "line": 190, "column": 43, "nodeType": "2961", "messageId": "2962", "endLine": 190, "endColumn": 61}, {"ruleId": "2959", "severity": 1, "message": "3168", "line": 346, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 346, "endColumn": 26}, {"ruleId": "2959", "severity": 1, "message": "3169", "line": 370, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 370, "endColumn": 30}, {"ruleId": "2959", "severity": 1, "message": "3026", "line": 11, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3070", "line": 22, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3047", "line": 23, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 23, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3170", "line": 26, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 26, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "3171", "line": 31, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 31, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3159", "line": 36, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 36, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3172", "line": 37, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 37, "endColumn": 30}, {"ruleId": "2959", "severity": 1, "message": "3173", "line": 39, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 39, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3156", "line": 44, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 44, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3174", "line": 50, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 50, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "3175", "line": 50, "column": 24, "nodeType": "2961", "messageId": "2962", "endLine": 50, "endColumn": 32}, {"ruleId": "2959", "severity": 1, "message": "3167", "line": 50, "column": 43, "nodeType": "2961", "messageId": "2962", "endLine": 50, "endColumn": 61}, {"ruleId": "2959", "severity": 1, "message": "3176", "line": 4, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3060", "line": 4, "column": 15, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 31}, {"ruleId": "2959", "severity": 1, "message": "3047", "line": 22, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3085", "line": 23, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 23, "endColumn": 6}, {"ruleId": "2959", "severity": 1, "message": "3086", "line": 24, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 24, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3087", "line": 25, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 25, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "3088", "line": 26, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 26, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3027", "line": 27, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 27, "endColumn": 12}, {"ruleId": "2959", "severity": 1, "message": "3028", "line": 28, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 28, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3098", "line": 29, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 29, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3096", "line": 30, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 30, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3097", "line": 31, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 31, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3094", "line": 32, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 32, "endColumn": 9}, {"ruleId": "2959", "severity": 1, "message": "3103", "line": 34, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 34, "endColumn": 37}, {"ruleId": "2959", "severity": 1, "message": "3105", "line": 37, "column": 35, "nodeType": "2961", "messageId": "2962", "endLine": 37, "endColumn": 50}, {"ruleId": "2959", "severity": 1, "message": "3106", "line": 37, "column": 52, "nodeType": "2961", "messageId": "2962", "endLine": 37, "endColumn": 68}, {"ruleId": "2959", "severity": 1, "message": "3120", "line": 81, "column": 25, "nodeType": "2961", "messageId": "2962", "endLine": 81, "endColumn": 41}, {"ruleId": "2959", "severity": 1, "message": "3177", "line": 82, "column": 28, "nodeType": "2961", "messageId": "2962", "endLine": 82, "endColumn": 47}, {"ruleId": "2959", "severity": 1, "message": "3154", "line": 88, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 88, "endColumn": 15}, {"ruleId": "2959", "severity": 1, "message": "3087", "line": 20, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 20, "endColumn": 29}, {"ruleId": "2959", "severity": 1, "message": "3088", "line": 20, "column": 31, "nodeType": "2961", "messageId": "2962", "endLine": 20, "endColumn": 38}, {"ruleId": "2959", "severity": 1, "message": "3070", "line": 20, "column": 40, "nodeType": "2961", "messageId": "2962", "endLine": 20, "endColumn": 48}, {"ruleId": "2959", "severity": 1, "message": "3095", "line": 20, "column": 50, "nodeType": "2961", "messageId": "2962", "endLine": 20, "endColumn": 53}, {"ruleId": "2959", "severity": 1, "message": "3096", "line": 20, "column": 55, "nodeType": "2961", "messageId": "2962", "endLine": 20, "endColumn": 60}, {"ruleId": "2959", "severity": 1, "message": "3097", "line": 20, "column": 62, "nodeType": "2961", "messageId": "2962", "endLine": 20, "endColumn": 67}, {"ruleId": "2959", "severity": 1, "message": "3098", "line": 20, "column": 69, "nodeType": "2961", "messageId": "2962", "endLine": 20, "endColumn": 82}, {"ruleId": "2959", "severity": 1, "message": "3094", "line": 20, "column": 84, "nodeType": "2961", "messageId": "2962", "endLine": 20, "endColumn": 90}, {"ruleId": "2959", "severity": 1, "message": "3105", "line": 21, "column": 35, "nodeType": "2961", "messageId": "2962", "endLine": 21, "endColumn": 50}, {"ruleId": "2959", "severity": 1, "message": "3106", "line": 21, "column": 52, "nodeType": "2961", "messageId": "2962", "endLine": 21, "endColumn": 68}, {"ruleId": "2959", "severity": 1, "message": "3178", "line": 22, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3179", "line": 75, "column": 28, "nodeType": "2961", "messageId": "2962", "endLine": 75, "endColumn": 47}, {"ruleId": "2959", "severity": 1, "message": "3154", "line": 81, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 81, "endColumn": 15}, {"ruleId": "2959", "severity": 1, "message": "2982", "line": 8, "column": 27, "nodeType": "2961", "messageId": "2962", "endLine": 8, "endColumn": 36}, {"ruleId": "2959", "severity": 1, "message": "3180", "line": 19, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 19, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3181", "line": 49, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 49, "endColumn": 26}, {"ruleId": "2959", "severity": 1, "message": "3182", "line": 50, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 50, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3183", "line": 94, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 94, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "3168", "line": 114, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 114, "endColumn": 24}, {"ruleId": "2959", "severity": 1, "message": "3162", "line": 148, "column": 21, "nodeType": "2961", "messageId": "2962", "endLine": 148, "endColumn": 33}, {"ruleId": "2959", "severity": 1, "message": "2981", "line": 1, "column": 17, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "2982", "line": 1, "column": 27, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 36}, {"ruleId": "2959", "severity": 1, "message": "3171", "line": 2, "column": 25, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 36}, {"ruleId": "2959", "severity": 1, "message": "3184", "line": 16, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 16, "endColumn": 32}, {"ruleId": "2959", "severity": 1, "message": "3185", "line": 17, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 17, "endColumn": 26}, {"ruleId": "2959", "severity": 1, "message": "3013", "line": 3, "column": 64, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 78}, {"ruleId": "2959", "severity": 1, "message": "3060", "line": 3, "column": 90, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 106}, {"ruleId": "2959", "severity": 1, "message": "3043", "line": 3, "column": 125, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 130}, {"ruleId": "2959", "severity": 1, "message": "3186", "line": 12, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 12, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3084", "line": 14, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3047", "line": 16, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 16, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3085", "line": 16, "column": 20, "nodeType": "2961", "messageId": "2962", "endLine": 16, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3086", "line": 16, "column": 25, "nodeType": "2961", "messageId": "2962", "endLine": 16, "endColumn": 29}, {"ruleId": "2959", "severity": 1, "message": "3070", "line": 16, "column": 52, "nodeType": "2961", "messageId": "2962", "endLine": 16, "endColumn": 60}, {"ruleId": "2959", "severity": 1, "message": "3095", "line": 16, "column": 62, "nodeType": "2961", "messageId": "2962", "endLine": 16, "endColumn": 65}, {"ruleId": "2959", "severity": 1, "message": "3103", "line": 17, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 17, "endColumn": 37}, {"ruleId": "2959", "severity": 1, "message": "3104", "line": 21, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 21, "endColumn": 15}, {"ruleId": "2959", "severity": 1, "message": "3105", "line": 22, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3187", "line": 23, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 23, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3188", "line": 25, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 25, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3189", "line": 26, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 26, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3190", "line": 107, "column": 27, "nodeType": "2961", "messageId": "2962", "endLine": 107, "endColumn": 45}, {"ruleId": "2959", "severity": 1, "message": "3154", "line": 112, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 112, "endColumn": 15}, {"ruleId": "2968", "severity": 1, "message": "2969", "line": 314, "column": 1, "nodeType": "2970", "endLine": 326, "endColumn": 3}, {"ruleId": "2959", "severity": 1, "message": "3191", "line": 2, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 28}, {"ruleId": "2959", "severity": 1, "message": "3192", "line": 2, "column": 30, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 47}, {"ruleId": "2959", "severity": 1, "message": "3193", "line": 2, "column": 49, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 65}, {"ruleId": "2959", "severity": 1, "message": "3194", "line": 2, "column": 78, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 88}, {"ruleId": "2959", "severity": 1, "message": "3195", "line": 8, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 8, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3196", "line": 179, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 179, "endColumn": 43}, {"ruleId": "2959", "severity": 1, "message": "3195", "line": 5, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 18}, {"ruleId": "2968", "severity": 1, "message": "2969", "line": 711, "column": 1, "nodeType": "2970", "endLine": 721, "endColumn": 3}, {"ruleId": "2959", "severity": 1, "message": "3197", "line": 4, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 21}, {"ruleId": "2971", "severity": 1, "message": "3198", "line": 55, "column": 6, "nodeType": "2973", "endLine": 55, "endColumn": 8, "suggestions": "3199"}, {"ruleId": "2959", "severity": 1, "message": "3200", "line": 5, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 9}, {"ruleId": "2959", "severity": 1, "message": "3201", "line": 84, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 84, "endColumn": 23}, {"ruleId": "2971", "severity": 1, "message": "3202", "line": 110, "column": 6, "nodeType": "2973", "endLine": 110, "endColumn": 8, "suggestions": "3203"}, {"ruleId": "2959", "severity": 1, "message": "3204", "line": 4, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3205", "line": 4, "column": 46, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 62}, {"ruleId": "2971", "severity": 1, "message": "3206", "line": 46, "column": 9, "nodeType": "3207", "endLine": 49, "endColumn": 4, "suggestions": "3208"}, {"ruleId": "2971", "severity": 1, "message": "3209", "line": 122, "column": 6, "nodeType": "2973", "endLine": 122, "endColumn": 35, "suggestions": "3210"}, {"ruleId": "2971", "severity": 1, "message": "3211", "line": 690, "column": 6, "nodeType": "2973", "endLine": 690, "endColumn": 8, "suggestions": "3212"}, {"ruleId": "2959", "severity": 1, "message": "3213", "line": 52, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 52, "endColumn": 49}, {"ruleId": "2959", "severity": 1, "message": "3213", "line": 82, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 82, "endColumn": 51}, {"ruleId": "2959", "severity": 1, "message": "3213", "line": 135, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 135, "endColumn": 54}, {"ruleId": "2959", "severity": 1, "message": "2981", "line": 1, "column": 17, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3214", "line": 7, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 7, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3215", "line": 19, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 19, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3216", "line": 7, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 7, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3217", "line": 18, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 18, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "3081", "line": 10, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3218", "line": 14, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "2992", "line": 30, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 30, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "2993", "line": 30, "column": 25, "nodeType": "2961", "messageId": "2962", "endLine": 30, "endColumn": 40}, {"ruleId": "2959", "severity": 1, "message": "2982", "line": 1, "column": 27, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 36}, {"ruleId": "2959", "severity": 1, "message": "3043", "line": 2, "column": 62, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 67}, {"ruleId": "2959", "severity": 1, "message": "3219", "line": 2, "column": 201, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 207}, {"ruleId": "2959", "severity": 1, "message": "3052", "line": 2, "column": 225, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 239}, {"ruleId": "2959", "severity": 1, "message": "3220", "line": 2, "column": 255, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 278}, {"ruleId": "2959", "severity": 1, "message": "3221", "line": 7, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 7, "endColumn": 15}, {"ruleId": "2959", "severity": 1, "message": "3222", "line": 10, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3071", "line": 11, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3223", "line": 25, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 25, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3113", "line": 60, "column": 33, "nodeType": "2961", "messageId": "2962", "endLine": 60, "endColumn": 38}, {"ruleId": "2959", "severity": 1, "message": "3024", "line": 3, "column": 16, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "3025", "line": 3, "column": 22, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 33}, {"ruleId": "2959", "severity": 1, "message": "2994", "line": 3, "column": 109, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 119}, {"ruleId": "2959", "severity": 1, "message": "3224", "line": 3, "column": 121, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 126}, {"ruleId": "2959", "severity": 1, "message": "3225", "line": 3, "column": 128, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 137}, {"ruleId": "2959", "severity": 1, "message": "3226", "line": 3, "column": 139, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 148}, {"ruleId": "2959", "severity": 1, "message": "3227", "line": 3, "column": 150, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 164}, {"ruleId": "2959", "severity": 1, "message": "3228", "line": 3, "column": 166, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 175}, {"ruleId": "2959", "severity": 1, "message": "3229", "line": 3, "column": 177, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 185}, {"ruleId": "2959", "severity": 1, "message": "3005", "line": 3, "column": 187, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 192}, {"ruleId": "2959", "severity": 1, "message": "3230", "line": 3, "column": 200, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 204}, {"ruleId": "2959", "severity": 1, "message": "3231", "line": 3, "column": 206, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 214}, {"ruleId": "2959", "severity": 1, "message": "3232", "line": 3, "column": 216, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 228}, {"ruleId": "2959", "severity": 1, "message": "3233", "line": 3, "column": 230, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 242}, {"ruleId": "2959", "severity": 1, "message": "3088", "line": 3, "column": 321, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 328}, {"ruleId": "2959", "severity": 1, "message": "2995", "line": 6, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 6, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "2997", "line": 7, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 7, "endColumn": 9}, {"ruleId": "2959", "severity": 1, "message": "3234", "line": 8, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 8, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3069", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3076", "line": 14, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 12}, {"ruleId": "2959", "severity": 1, "message": "3104", "line": 19, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 19, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "3235", "line": 21, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 21, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3236", "line": 23, "column": 30, "nodeType": "2961", "messageId": "2962", "endLine": 23, "endColumn": 41}, {"ruleId": "2959", "severity": 1, "message": "3237", "line": 53, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 53, "endColumn": 35}, {"ruleId": "2959", "severity": 1, "message": "3238", "line": 53, "column": 37, "nodeType": "2961", "messageId": "2962", "endLine": 53, "endColumn": 54}, {"ruleId": "2959", "severity": 1, "message": "3239", "line": 53, "column": 56, "nodeType": "2961", "messageId": "2962", "endLine": 53, "endColumn": 64}, {"ruleId": "2959", "severity": 1, "message": "3240", "line": 8, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 8, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3069", "line": 8, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 8, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "2982", "line": 1, "column": 27, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 36}, {"ruleId": "2959", "severity": 1, "message": "3012", "line": 2, "column": 186, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 190}, {"ruleId": "2959", "severity": 1, "message": "3052", "line": 2, "column": 225, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 239}, {"ruleId": "2959", "severity": 1, "message": "3220", "line": 2, "column": 255, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 278}, {"ruleId": "2959", "severity": 1, "message": "3071", "line": 11, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3241", "line": 15, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 15, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3235", "line": 3, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3043", "line": 4, "column": 62, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 67}, {"ruleId": "2959", "severity": 1, "message": "3219", "line": 4, "column": 201, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 207}, {"ruleId": "2959", "severity": 1, "message": "3230", "line": 4, "column": 209, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 213}, {"ruleId": "2959", "severity": 1, "message": "3231", "line": 4, "column": 215, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 223}, {"ruleId": "2959", "severity": 1, "message": "3052", "line": 4, "column": 225, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 239}, {"ruleId": "2959", "severity": 1, "message": "3232", "line": 4, "column": 241, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 253}, {"ruleId": "2959", "severity": 1, "message": "3220", "line": 4, "column": 255, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 278}, {"ruleId": "2959", "severity": 1, "message": "3014", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3242", "line": 20, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 20, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3113", "line": 68, "column": 39, "nodeType": "2961", "messageId": "2962", "endLine": 68, "endColumn": 44}, {"ruleId": "2959", "severity": 1, "message": "3237", "line": 74, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 74, "endColumn": 35}, {"ruleId": "2959", "severity": 1, "message": "3238", "line": 74, "column": 37, "nodeType": "2961", "messageId": "2962", "endLine": 74, "endColumn": 54}, {"ruleId": "2959", "severity": 1, "message": "3235", "line": 3, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3043", "line": 4, "column": 62, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 67}, {"ruleId": "2959", "severity": 1, "message": "3219", "line": 4, "column": 201, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 207}, {"ruleId": "2959", "severity": 1, "message": "3230", "line": 4, "column": 209, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 213}, {"ruleId": "2959", "severity": 1, "message": "3231", "line": 4, "column": 215, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 223}, {"ruleId": "2959", "severity": 1, "message": "3052", "line": 4, "column": 225, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 239}, {"ruleId": "2959", "severity": 1, "message": "3232", "line": 4, "column": 241, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 253}, {"ruleId": "2959", "severity": 1, "message": "3220", "line": 4, "column": 255, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 278}, {"ruleId": "2959", "severity": 1, "message": "3014", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3222", "line": 14, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3113", "line": 61, "column": 28, "nodeType": "2961", "messageId": "2962", "endLine": 61, "endColumn": 33}, {"ruleId": "2959", "severity": 1, "message": "3237", "line": 67, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 67, "endColumn": 35}, {"ruleId": "2959", "severity": 1, "message": "3238", "line": 67, "column": 37, "nodeType": "2961", "messageId": "2962", "endLine": 67, "endColumn": 54}, {"ruleId": "2959", "severity": 1, "message": "3230", "line": 2, "column": 209, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 213}, {"ruleId": "2959", "severity": 1, "message": "3231", "line": 2, "column": 215, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 223}, {"ruleId": "2959", "severity": 1, "message": "3052", "line": 2, "column": 225, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 239}, {"ruleId": "2959", "severity": 1, "message": "3232", "line": 2, "column": 241, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 253}, {"ruleId": "2959", "severity": 1, "message": "3220", "line": 2, "column": 255, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 278}, {"ruleId": "2959", "severity": 1, "message": "3243", "line": 2, "column": 307, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 311}, {"ruleId": "2959", "severity": 1, "message": "3014", "line": 7, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 7, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3244", "line": 18, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 18, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3074", "line": 19, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 19, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3069", "line": 20, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 20, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3113", "line": 67, "column": 38, "nodeType": "2961", "messageId": "2962", "endLine": 67, "endColumn": 43}, {"ruleId": "2959", "severity": 1, "message": "3230", "line": 2, "column": 209, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 213}, {"ruleId": "2959", "severity": 1, "message": "3231", "line": 2, "column": 215, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 223}, {"ruleId": "2959", "severity": 1, "message": "3052", "line": 2, "column": 225, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 239}, {"ruleId": "2959", "severity": 1, "message": "3232", "line": 2, "column": 241, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 253}, {"ruleId": "2959", "severity": 1, "message": "3220", "line": 2, "column": 255, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 278}, {"ruleId": "2959", "severity": 1, "message": "3243", "line": 2, "column": 307, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 311}, {"ruleId": "2959", "severity": 1, "message": "3245", "line": 2, "column": 320, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 331}, {"ruleId": "2959", "severity": 1, "message": "3014", "line": 7, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 7, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3246", "line": 18, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 18, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3247", "line": 19, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 19, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3113", "line": 126, "column": 44, "nodeType": "2961", "messageId": "2962", "endLine": 126, "endColumn": 49}, {"ruleId": "2959", "severity": 1, "message": "3024", "line": 2, "column": 33, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 37}, {"ruleId": "2959", "severity": 1, "message": "3025", "line": 2, "column": 39, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 50}, {"ruleId": "2959", "severity": 1, "message": "3248", "line": 2, "column": 52, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 61}, {"ruleId": "2959", "severity": 1, "message": "3243", "line": 2, "column": 63, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 67}, {"ruleId": "2959", "severity": 1, "message": "3026", "line": 2, "column": 69, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 76}, {"ruleId": "2959", "severity": 1, "message": "3230", "line": 2, "column": 209, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 213}, {"ruleId": "2959", "severity": 1, "message": "3231", "line": 2, "column": 215, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 223}, {"ruleId": "2959", "severity": 1, "message": "3052", "line": 2, "column": 225, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 239}, {"ruleId": "2959", "severity": 1, "message": "3232", "line": 2, "column": 241, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 253}, {"ruleId": "2959", "severity": 1, "message": "3220", "line": 2, "column": 255, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 278}, {"ruleId": "2959", "severity": 1, "message": "3243", "line": 2, "column": 307, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 311}, {"ruleId": "2959", "severity": 1, "message": "3088", "line": 2, "column": 336, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 343}, {"ruleId": "2959", "severity": 1, "message": "3014", "line": 7, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 7, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "2995", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3113", "line": 70, "column": 39, "nodeType": "2961", "messageId": "2962", "endLine": 70, "endColumn": 44}, {"ruleId": "2959", "severity": 1, "message": "3249", "line": 6, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 6, "endColumn": 33}, {"ruleId": "2959", "severity": 1, "message": "3250", "line": 6, "column": 35, "nodeType": "2961", "messageId": "2962", "endLine": 6, "endColumn": 62}, {"ruleId": "2959", "severity": 1, "message": "3251", "line": 6, "column": 64, "nodeType": "2961", "messageId": "2962", "endLine": 6, "endColumn": 93}, {"ruleId": "2959", "severity": 1, "message": "3252", "line": 41, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 41, "endColumn": 23}, {"ruleId": "3253", "severity": 1, "message": "3254", "line": 23, "column": 34, "nodeType": "2961", "messageId": "3255", "endLine": 23, "endColumn": 38}, {"ruleId": "3253", "severity": 1, "message": "3256", "line": 24, "column": 32, "nodeType": "2961", "messageId": "3255", "endLine": 24, "endColumn": 34}, {"ruleId": "3253", "severity": 1, "message": "3257", "line": 25, "column": 44, "nodeType": "2961", "messageId": "3255", "endLine": 25, "endColumn": 51}, {"ruleId": "3253", "severity": 1, "message": "3258", "line": 26, "column": 34, "nodeType": "2961", "messageId": "3255", "endLine": 26, "endColumn": 38}, {"ruleId": "2959", "severity": 1, "message": "3259", "line": 1, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3260", "line": 4, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3261", "line": 4, "column": 25, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 38}, {"ruleId": "2959", "severity": 1, "message": "3262", "line": 5, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "3263", "line": 6, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 6, "endColumn": 24}, {"ruleId": "2959", "severity": 1, "message": "3264", "line": 7, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 7, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3035", "line": 8, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 8, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "3265", "line": 257, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 257, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "3266", "line": 259, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 259, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "3267", "line": 1982, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 1982, "endColumn": 26}, {"ruleId": "2959", "severity": 1, "message": "3268", "line": 2182, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 2182, "endColumn": 31}, {"ruleId": "2959", "severity": 1, "message": "3269", "line": 2398, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 2398, "endColumn": 28}, {"ruleId": "2968", "severity": 1, "message": "2969", "line": 141, "column": 1, "nodeType": "2970", "endLine": 149, "endColumn": 3}, {"ruleId": "2959", "severity": 1, "message": "3270", "line": 2, "column": 13, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3271", "line": 3, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 15}, {"ruleId": "2968", "severity": 1, "message": "2969", "line": 89, "column": 1, "nodeType": "2970", "endLine": 92, "endColumn": 3}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 3, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3224", "line": 4, "column": 52, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 57}, {"ruleId": "2959", "severity": 1, "message": "3225", "line": 4, "column": 59, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 68}, {"ruleId": "2959", "severity": 1, "message": "3226", "line": 4, "column": 70, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 79}, {"ruleId": "2959", "severity": 1, "message": "3227", "line": 4, "column": 81, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 95}, {"ruleId": "2959", "severity": 1, "message": "3228", "line": 4, "column": 97, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 106}, {"ruleId": "2959", "severity": 1, "message": "3229", "line": 4, "column": 108, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 116}, {"ruleId": "2959", "severity": 1, "message": "3005", "line": 4, "column": 118, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 123}, {"ruleId": "2959", "severity": 1, "message": "3088", "line": 4, "column": 248, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 255}, {"ruleId": "2959", "severity": 1, "message": "3219", "line": 4, "column": 273, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 279}, {"ruleId": "2959", "severity": 1, "message": "3069", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3234", "line": 10, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3074", "line": 11, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3272", "line": 13, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 13, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3186", "line": 14, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3014", "line": 16, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 16, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3273", "line": 21, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 21, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3274", "line": 29, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 29, "endColumn": 29}, {"ruleId": "2959", "severity": 1, "message": "3178", "line": 30, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 30, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3113", "line": 34, "column": 42, "nodeType": "2961", "messageId": "2962", "endLine": 34, "endColumn": 47}, {"ruleId": "2959", "severity": 1, "message": "3275", "line": 133, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 133, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3276", "line": 139, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 139, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3277", "line": 156, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 156, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3278", "line": 168, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 168, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3168", "line": 184, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 184, "endColumn": 26}, {"ruleId": "2959", "severity": 1, "message": "3004", "line": 194, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 194, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3024", "line": 3, "column": 27, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 31}, {"ruleId": "2959", "severity": 1, "message": "3025", "line": 3, "column": 33, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 44}, {"ruleId": "2959", "severity": 1, "message": "3234", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3069", "line": 10, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3074", "line": 11, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3272", "line": 12, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 12, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3279", "line": 13, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 13, "endColumn": 12}, {"ruleId": "2959", "severity": 1, "message": "3014", "line": 16, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 16, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3038", "line": 44, "column": 26, "nodeType": "2961", "messageId": "2962", "endLine": 44, "endColumn": 31}, {"ruleId": "2959", "severity": 1, "message": "3088", "line": 4, "column": 248, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 255}, {"ruleId": "2959", "severity": 1, "message": "3219", "line": 4, "column": 257, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 263}, {"ruleId": "2959", "severity": 1, "message": "3280", "line": 12, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 12, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3272", "line": 13, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 13, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3273", "line": 14, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3281", "line": 15, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 15, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 2, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "2995", "line": 7, "column": 15, "nodeType": "2961", "messageId": "2962", "endLine": 7, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "3234", "line": 7, "column": 36, "nodeType": "2961", "messageId": "2962", "endLine": 7, "endColumn": 47}, {"ruleId": "2959", "severity": 1, "message": "3282", "line": 100, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 100, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 6, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 6, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3088", "line": 10, "column": 194, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 201}, {"ruleId": "2959", "severity": 1, "message": "2995", "line": 13, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 13, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "2997", "line": 14, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 9}, {"ruleId": "2959", "severity": 1, "message": "3074", "line": 15, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 15, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3014", "line": 16, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 16, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3283", "line": 17, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 17, "endColumn": 9}, {"ruleId": "2959", "severity": 1, "message": "3284", "line": 22, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3285", "line": 30, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 30, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3286", "line": 31, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 31, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3287", "line": 34, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 34, "endColumn": 29}, {"ruleId": "2959", "severity": 1, "message": "3288", "line": 111, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 111, "endColumn": 29}, {"ruleId": "2959", "severity": 1, "message": "3037", "line": 55, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 55, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3289", "line": 55, "column": 27, "nodeType": "2961", "messageId": "2962", "endLine": 55, "endColumn": 38}, {"ruleId": "2959", "severity": 1, "message": "3040", "line": 56, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 56, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3290", "line": 57, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 57, "endColumn": 24}, {"ruleId": "2959", "severity": 1, "message": "3266", "line": 58, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 58, "endColumn": 26}, {"ruleId": "2959", "severity": 1, "message": "3164", "line": 59, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 59, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3291", "line": 46, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 46, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3292", "line": 103, "column": 21, "nodeType": "2961", "messageId": "2962", "endLine": 103, "endColumn": 33}, {"ruleId": "2959", "severity": 1, "message": "3064", "line": 4, "column": 73, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 78}, {"ruleId": "2959", "severity": 1, "message": "3293", "line": 4, "column": 80, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 88}, {"ruleId": "2959", "severity": 1, "message": "2995", "line": 10, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "2997", "line": 11, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 9}, {"ruleId": "2959", "severity": 1, "message": "3294", "line": 24, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 24, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3071", "line": 4, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "3027", "line": 5, "column": 92, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 101}, {"ruleId": "2959", "severity": 1, "message": "3028", "line": 5, "column": 103, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 107}, {"ruleId": "2959", "severity": 1, "message": "3295", "line": 27, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 27, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 2, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3108", "line": 3, "column": 18, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 34}, {"ruleId": "2959", "severity": 1, "message": "3088", "line": 4, "column": 282, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 289}, {"ruleId": "2959", "severity": 1, "message": "2994", "line": 4, "column": 291, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 301}, {"ruleId": "2959", "severity": 1, "message": "3026", "line": 4, "column": 303, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 310}, {"ruleId": "2959", "severity": 1, "message": "3296", "line": 22, "column": 49, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 65}, {"ruleId": "2959", "severity": 1, "message": "3297", "line": 26, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 26, "endColumn": 38}, {"ruleId": "2959", "severity": 1, "message": "3298", "line": 83, "column": 32, "nodeType": "2961", "messageId": "2962", "endLine": 83, "endColumn": 55}, {"ruleId": "2959", "severity": 1, "message": "3299", "line": 198, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 198, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3024", "line": 4, "column": 33, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 37}, {"ruleId": "2959", "severity": 1, "message": "3025", "line": 4, "column": 39, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 50}, {"ruleId": "2959", "severity": 1, "message": "3054", "line": 4, "column": 258, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 262}, {"ruleId": "2959", "severity": 1, "message": "3053", "line": 4, "column": 264, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 267}, {"ruleId": "2959", "severity": 1, "message": "3026", "line": 4, "column": 269, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 276}, {"ruleId": "2959", "severity": 1, "message": "3234", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3069", "line": 10, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3186", "line": 11, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3014", "line": 12, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 12, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3283", "line": 13, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 13, "endColumn": 9}, {"ruleId": "2959", "severity": 1, "message": "3300", "line": 14, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 15}, {"ruleId": "2959", "severity": 1, "message": "3301", "line": 15, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 15, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3047", "line": 19, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 19, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3017", "line": 20, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 20, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3302", "line": 135, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 135, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3303", "line": 124, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 124, "endColumn": 24}, {"ruleId": "2959", "severity": 1, "message": "2982", "line": 1, "column": 27, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 36}, {"ruleId": "2959", "severity": 1, "message": "3060", "line": 3, "column": 77, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 93}, {"ruleId": "2959", "severity": 1, "message": "3047", "line": 12, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 12, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3071", "line": 15, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 15, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3021", "line": 16, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 16, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3072", "line": 17, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 17, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3285", "line": 22, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3286", "line": 23, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 23, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3304", "line": 29, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 29, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "3305", "line": 29, "column": 24, "nodeType": "2961", "messageId": "2962", "endLine": 29, "endColumn": 38}, {"ruleId": "2959", "severity": 1, "message": "3161", "line": 52, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 52, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "3162", "line": 52, "column": 21, "nodeType": "2961", "messageId": "2962", "endLine": 52, "endColumn": 33}, {"ruleId": "2959", "severity": 1, "message": "3306", "line": 106, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 106, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 2, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3088", "line": 3, "column": 248, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 255}, {"ruleId": "2959", "severity": 1, "message": "3013", "line": 3, "column": 257, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 271}, {"ruleId": "2959", "severity": 1, "message": "3186", "line": 8, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 8, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3234", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3216", "line": 13, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 13, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3307", "line": 14, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 2, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3088", "line": 3, "column": 248, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 255}, {"ruleId": "2959", "severity": 1, "message": "3219", "line": 3, "column": 257, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 263}, {"ruleId": "2959", "severity": 1, "message": "3043", "line": 3, "column": 275, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 280}, {"ruleId": "2959", "severity": 1, "message": "3186", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3234", "line": 10, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3216", "line": 14, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3308", "line": 124, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 124, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 2, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3088", "line": 3, "column": 248, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 255}, {"ruleId": "2959", "severity": 1, "message": "3234", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3069", "line": 10, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3074", "line": 11, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3309", "line": 12, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 12, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3310", "line": 13, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 13, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3216", "line": 14, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3311", "line": 18, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 18, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3312", "line": 20, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 20, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 2, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3219", "line": 3, "column": 257, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 263}, {"ruleId": "2959", "severity": 1, "message": "3234", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3069", "line": 10, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3074", "line": 11, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3216", "line": 14, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3024", "line": 3, "column": 27, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 31}, {"ruleId": "2959", "severity": 1, "message": "3025", "line": 3, "column": 33, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 44}, {"ruleId": "2959", "severity": 1, "message": "3224", "line": 3, "column": 59, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 64}, {"ruleId": "2959", "severity": 1, "message": "3225", "line": 3, "column": 66, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 75}, {"ruleId": "2959", "severity": 1, "message": "3226", "line": 3, "column": 77, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 86}, {"ruleId": "2959", "severity": 1, "message": "3227", "line": 3, "column": 88, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 102}, {"ruleId": "2959", "severity": 1, "message": "3228", "line": 3, "column": 104, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 113}, {"ruleId": "2959", "severity": 1, "message": "3229", "line": 3, "column": 115, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 123}, {"ruleId": "2959", "severity": 1, "message": "2994", "line": 3, "column": 148, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 158}, {"ruleId": "2959", "severity": 1, "message": "2995", "line": 6, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 6, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "2997", "line": 7, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 7, "endColumn": 9}, {"ruleId": "2959", "severity": 1, "message": "3017", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3014", "line": 15, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 15, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3021", "line": 17, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 17, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3072", "line": 18, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 18, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3104", "line": 21, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 21, "endColumn": 15}, {"ruleId": "2959", "severity": 1, "message": "3313", "line": 22, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3105", "line": 23, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 23, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3285", "line": 26, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 26, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3286", "line": 27, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 27, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3314", "line": 31, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 31, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3315", "line": 226, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 226, "endColumn": 32}, {"ruleId": "2959", "severity": 1, "message": "3316", "line": 248, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 248, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3317", "line": 464, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 464, "endColumn": 26}, {"ruleId": "2959", "severity": 1, "message": "3011", "line": 1, "column": 38, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 44}, {"ruleId": "2959", "severity": 1, "message": "3318", "line": 2, "column": 54, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 70}, {"ruleId": "2959", "severity": 1, "message": "3026", "line": 2, "column": 78, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 85}, {"ruleId": "2959", "severity": 1, "message": "3088", "line": 2, "column": 104, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 111}, {"ruleId": "2959", "severity": 1, "message": "3319", "line": 2, "column": 157, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 165}, {"ruleId": "2959", "severity": 1, "message": "3320", "line": 2, "column": 167, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 171}, {"ruleId": "2959", "severity": 1, "message": "3002", "line": 4, "column": 28, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 43}, {"ruleId": "2959", "severity": 1, "message": "3321", "line": 4, "column": 45, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 55}, {"ruleId": "2959", "severity": 1, "message": "3048", "line": 4, "column": 57, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 69}, {"ruleId": "2959", "severity": 1, "message": "3322", "line": 4, "column": 71, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 83}, {"ruleId": "2959", "severity": 1, "message": "3323", "line": 5, "column": 42, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 53}, {"ruleId": "2959", "severity": 1, "message": "3216", "line": 11, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3273", "line": 12, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 12, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3030", "line": 19, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 19, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3324", "line": 20, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 20, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 27, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 27, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3325", "line": 27, "column": 18, "nodeType": "2961", "messageId": "2962", "endLine": 27, "endColumn": 33}, {"ruleId": "2959", "severity": 1, "message": "3326", "line": 84, "column": 5, "nodeType": "2961", "messageId": "2962", "endLine": 84, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3327", "line": 85, "column": 5, "nodeType": "2961", "messageId": "2962", "endLine": 85, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3328", "line": 86, "column": 5, "nodeType": "2961", "messageId": "2962", "endLine": 86, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "3329", "line": 87, "column": 5, "nodeType": "2961", "messageId": "2962", "endLine": 87, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "3330", "line": 100, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 100, "endColumn": 22}, {"ruleId": "2971", "severity": 1, "message": "3331", "line": 172, "column": 6, "nodeType": "2973", "endLine": 172, "endColumn": 8, "suggestions": "3332"}, {"ruleId": "2959", "severity": 1, "message": "3005", "line": 3, "column": 15, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "3333", "line": 48, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 48, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "3334", "line": 51, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 51, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "3335", "line": 52, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 52, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3336", "line": 53, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 53, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 3, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3337", "line": 5, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 31}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 3, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3337", "line": 6, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 6, "endColumn": 31}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 3, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3337", "line": 5, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 31}, {"ruleId": "2959", "severity": 1, "message": "3293", "line": 5, "column": 80, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 88}, {"ruleId": "2959", "severity": 1, "message": "3338", "line": 14, "column": 20, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 30}, {"ruleId": "2959", "severity": 1, "message": "3024", "line": 3, "column": 33, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 37}, {"ruleId": "2959", "severity": 1, "message": "3025", "line": 3, "column": 39, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 50}, {"ruleId": "2959", "severity": 1, "message": "2994", "line": 3, "column": 202, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 212}, {"ruleId": "2959", "severity": 1, "message": "2995", "line": 5, "column": 17, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "2997", "line": 5, "column": 23, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 29}, {"ruleId": "2959", "severity": 1, "message": "3030", "line": 5, "column": 31, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 41}, {"ruleId": "2959", "severity": 1, "message": "3029", "line": 6, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 6, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3073", "line": 6, "column": 15, "nodeType": "2961", "messageId": "2962", "endLine": 6, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3069", "line": 6, "column": 29, "nodeType": "2961", "messageId": "2962", "endLine": 6, "endColumn": 36}, {"ruleId": "2959", "severity": 1, "message": "3234", "line": 6, "column": 38, "nodeType": "2961", "messageId": "2962", "endLine": 6, "endColumn": 49}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 9, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3033", "line": 10, "column": 49, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 70}, {"ruleId": "2959", "severity": 1, "message": "3339", "line": 46, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 46, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "3340", "line": 47, "column": 20, "nodeType": "2961", "messageId": "2962", "endLine": 47, "endColumn": 27}, {"ruleId": "2971", "severity": 1, "message": "3341", "line": 87, "column": 6, "nodeType": "2973", "endLine": 87, "endColumn": 44, "suggestions": "3342"}, {"ruleId": "2971", "severity": 1, "message": "3341", "line": 108, "column": 6, "nodeType": "2973", "endLine": 108, "endColumn": 35, "suggestions": "3343"}, {"ruleId": "2971", "severity": 1, "message": "3341", "line": 131, "column": 6, "nodeType": "2973", "endLine": 131, "endColumn": 35, "suggestions": "3344"}, {"ruleId": "2971", "severity": 1, "message": "3341", "line": 154, "column": 6, "nodeType": "2973", "endLine": 154, "endColumn": 35, "suggestions": "3345"}, {"ruleId": "2971", "severity": 1, "message": "3341", "line": 169, "column": 6, "nodeType": "2973", "endLine": 169, "endColumn": 19, "suggestions": "3346"}, {"ruleId": "2971", "severity": 1, "message": "3341", "line": 184, "column": 6, "nodeType": "2973", "endLine": 184, "endColumn": 19, "suggestions": "3347"}, {"ruleId": "2971", "severity": 1, "message": "3341", "line": 199, "column": 6, "nodeType": "2973", "endLine": 199, "endColumn": 19, "suggestions": "3348"}, {"ruleId": "2959", "severity": 1, "message": "3024", "line": 3, "column": 33, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 37}, {"ruleId": "2959", "severity": 1, "message": "3026", "line": 3, "column": 160, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 167}, {"ruleId": "2959", "severity": 1, "message": "2997", "line": 5, "column": 14, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 9, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3035", "line": 14, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "3339", "line": 40, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 40, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "3340", "line": 41, "column": 20, "nodeType": "2961", "messageId": "2962", "endLine": 41, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3024", "line": 4, "column": 16, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "3025", "line": 4, "column": 22, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 33}, {"ruleId": "2959", "severity": 1, "message": "3349", "line": 4, "column": 35, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 46}, {"ruleId": "2959", "severity": 1, "message": "3043", "line": 4, "column": 124, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 129}, {"ruleId": "2959", "severity": 1, "message": "3350", "line": 17, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 17, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3351", "line": 23, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 23, "endColumn": 33}, {"ruleId": "2959", "severity": 1, "message": "3352", "line": 37, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 37, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3005", "line": 5, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3353", "line": 117, "column": 13, "nodeType": "2961", "messageId": "2962", "endLine": 117, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3354", "line": 132, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 132, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "2982", "line": 1, "column": 27, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 36}, {"ruleId": "2959", "severity": 1, "message": "3012", "line": 2, "column": 83, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 87}, {"ruleId": "2959", "severity": 1, "message": "2994", "line": 2, "column": 89, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 99}, {"ruleId": "2959", "severity": 1, "message": "3043", "line": 9, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 15}, {"ruleId": "2959", "severity": 1, "message": "3355", "line": 199, "column": 27, "nodeType": "2961", "messageId": "2962", "endLine": 199, "endColumn": 45}, {"ruleId": "2959", "severity": 1, "message": "3156", "line": 84, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 84, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "2982", "line": 1, "column": 27, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 36}, {"ruleId": "2959", "severity": 1, "message": "3026", "line": 3, "column": 324, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 331}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 5, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3046", "line": 15, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 15, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3302", "line": 53, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 53, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3340", "line": 54, "column": 25, "nodeType": "2961", "messageId": "2962", "endLine": 54, "endColumn": 32}, {"ruleId": "2959", "severity": 1, "message": "3113", "line": 54, "column": 34, "nodeType": "2961", "messageId": "2962", "endLine": 54, "endColumn": 39}, {"ruleId": "2959", "severity": 1, "message": "2996", "line": 10, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 6}, {"ruleId": "2959", "severity": 1, "message": "3024", "line": 8, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 8, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3025", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3026", "line": 10, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3012", "line": 14, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3070", "line": 23, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 23, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3216", "line": 24, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 24, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3215", "line": 27, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 27, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3186", "line": 32, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 32, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3356", "line": 130, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 130, "endColumn": 24}, {"ruleId": "2959", "severity": 1, "message": "3357", "line": 220, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 220, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3156", "line": 235, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 235, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "3358", "line": 241, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 241, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "3359", "line": 352, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 352, "endColumn": 24}, {"ruleId": "2959", "severity": 1, "message": "2994", "line": 4, "column": 129, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 139}, {"ruleId": "2959", "severity": 1, "message": "3088", "line": 4, "column": 141, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 148}, {"ruleId": "2959", "severity": 1, "message": "3281", "line": 18, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 18, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3360", "line": 24, "column": 55, "nodeType": "2961", "messageId": "2962", "endLine": 24, "endColumn": 70}, {"ruleId": "2959", "severity": 1, "message": "3351", "line": 25, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 25, "endColumn": 33}, {"ruleId": "2959", "severity": 1, "message": "3014", "line": 6, "column": 18, "nodeType": "2961", "messageId": "2962", "endLine": 6, "endColumn": 28}, {"ruleId": "2959", "severity": 1, "message": "3352", "line": 26, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 26, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3024", "line": 11, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3025", "line": 12, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 12, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3349", "line": 13, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 13, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3012", "line": 14, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3361", "line": 15, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 15, "endColumn": 9}, {"ruleId": "2959", "severity": 1, "message": "3043", "line": 17, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 17, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3030", "line": 27, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 27, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3026", "line": 3, "column": 265, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 272}, {"ruleId": "2959", "severity": 1, "message": "3362", "line": 4, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 5, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "2995", "line": 8, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 8, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "2997", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 9}, {"ruleId": "2959", "severity": 1, "message": "3014", "line": 11, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "2982", "line": 1, "column": 27, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 36}, {"ruleId": "2959", "severity": 1, "message": "3024", "line": 2, "column": 33, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 37}, {"ruleId": "2959", "severity": 1, "message": "3025", "line": 2, "column": 39, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 50}, {"ruleId": "2959", "severity": 1, "message": "3026", "line": 2, "column": 315, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 322}, {"ruleId": "2959", "severity": 1, "message": "3060", "line": 2, "column": 376, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 392}, {"ruleId": "2959", "severity": 1, "message": "3054", "line": 2, "column": 394, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 398}, {"ruleId": "2959", "severity": 1, "message": "3053", "line": 2, "column": 400, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 403}, {"ruleId": "2959", "severity": 1, "message": "3243", "line": 2, "column": 405, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 409}, {"ruleId": "2959", "severity": 1, "message": "3014", "line": 7, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 7, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3279", "line": 17, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 17, "endColumn": 12}, {"ruleId": "2959", "severity": 1, "message": "3281", "line": 18, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 18, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3363", "line": 21, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 21, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3364", "line": 22, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3186", "line": 23, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 23, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3365", "line": 36, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 36, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3366", "line": 56, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 56, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "3367", "line": 88, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 88, "endColumn": 29}, {"ruleId": "2959", "severity": 1, "message": "3340", "line": 124, "column": 5, "nodeType": "2961", "messageId": "2962", "endLine": 124, "endColumn": 12}, {"ruleId": "2959", "severity": 1, "message": "3113", "line": 125, "column": 5, "nodeType": "2961", "messageId": "2962", "endLine": 125, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3368", "line": 137, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 137, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3369", "line": 138, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 138, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3370", "line": 147, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 147, "endColumn": 24}, {"ruleId": "2959", "severity": 1, "message": "3371", "line": 190, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 190, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3372", "line": 198, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 198, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3373", "line": 205, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 205, "endColumn": 26}, {"ruleId": "2959", "severity": 1, "message": "3374", "line": 211, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 211, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3005", "line": 5, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3025", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3005", "line": 4, "column": 170, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 175}, {"ruleId": "2959", "severity": 1, "message": "3375", "line": 4, "column": 177, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 183}, {"ruleId": "2959", "severity": 1, "message": "3376", "line": 4, "column": 185, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 196}, {"ruleId": "2959", "severity": 1, "message": "3377", "line": 4, "column": 198, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 211}, {"ruleId": "2959", "severity": 1, "message": "3378", "line": 4, "column": 213, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 226}, {"ruleId": "2959", "severity": 1, "message": "3301", "line": 12, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 12, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3234", "line": 15, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 15, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3069", "line": 16, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 16, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3047", "line": 22, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3085", "line": 22, "column": 20, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3086", "line": 22, "column": 25, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 29}, {"ruleId": "2959", "severity": 1, "message": "3087", "line": 22, "column": 31, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 50}, {"ruleId": "2959", "severity": 1, "message": "3070", "line": 22, "column": 52, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 60}, {"ruleId": "2959", "severity": 1, "message": "3095", "line": 22, "column": 62, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 65}, {"ruleId": "2959", "severity": 1, "message": "3096", "line": 22, "column": 67, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 72}, {"ruleId": "2959", "severity": 1, "message": "3097", "line": 22, "column": 74, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 79}, {"ruleId": "2959", "severity": 1, "message": "3088", "line": 22, "column": 81, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 88}, {"ruleId": "2959", "severity": 1, "message": "3044", "line": 22, "column": 90, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 99}, {"ruleId": "2959", "severity": 1, "message": "3045", "line": 22, "column": 101, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 105}, {"ruleId": "2959", "severity": 1, "message": "3098", "line": 22, "column": 107, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 120}, {"ruleId": "2959", "severity": 1, "message": "3094", "line": 22, "column": 122, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 128}, {"ruleId": "2959", "severity": 1, "message": "3103", "line": 23, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 23, "endColumn": 37}, {"ruleId": "2959", "severity": 1, "message": "3104", "line": 29, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 29, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "3105", "line": 29, "column": 24, "nodeType": "2961", "messageId": "2962", "endLine": 29, "endColumn": 39}, {"ruleId": "2959", "severity": 1, "message": "3106", "line": 29, "column": 41, "nodeType": "2961", "messageId": "2962", "endLine": 29, "endColumn": 57}, {"ruleId": "2959", "severity": 1, "message": "3123", "line": 69, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 69, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3124", "line": 69, "column": 25, "nodeType": "2961", "messageId": "2962", "endLine": 69, "endColumn": 41}, {"ruleId": "2959", "severity": 1, "message": "3121", "line": 70, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 70, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "3122", "line": 70, "column": 24, "nodeType": "2961", "messageId": "2962", "endLine": 70, "endColumn": 39}, {"ruleId": "2959", "severity": 1, "message": "3154", "line": 73, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 73, "endColumn": 15}, {"ruleId": "2959", "severity": 1, "message": "3014", "line": 6, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 6, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3301", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3379", "line": 4, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3380", "line": 89, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 89, "endColumn": 33}, {"ruleId": "2959", "severity": 1, "message": "3381", "line": 184, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 184, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3043", "line": 2, "column": 43, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 48}, {"ruleId": "2959", "severity": 1, "message": "3003", "line": 43, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 43, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "3379", "line": 14, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3003", "line": 50, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 50, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "3243", "line": 2, "column": 46, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 50}, {"ruleId": "2959", "severity": 1, "message": "3382", "line": 2, "column": 52, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 56}, {"ruleId": "2959", "severity": 1, "message": "3012", "line": 2, "column": 58, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 62}, {"ruleId": "2959", "severity": 1, "message": "3383", "line": 5, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 15}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 12, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 12, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3384", "line": 5, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "3385", "line": 2, "column": 26, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 36}, {"ruleId": "2959", "severity": 1, "message": "3043", "line": 2, "column": 48, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 53}, {"ruleId": "2959", "severity": 1, "message": "3386", "line": 3, "column": 34, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 42}, {"ruleId": "2959", "severity": 1, "message": "3385", "line": 3, "column": 111, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 121}, {"ruleId": "2959", "severity": 1, "message": "3176", "line": 2, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3012", "line": 2, "column": 183, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 187}, {"ruleId": "2959", "severity": 1, "message": "3352", "line": 51, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 51, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3387", "line": 3, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 33}, {"ruleId": "2959", "severity": 1, "message": "3352", "line": 33, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 33, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3352", "line": 32, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 32, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3388", "line": 24, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 24, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "3389", "line": 2, "column": 15, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3390", "line": 3, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3391", "line": 49, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 49, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3392", "line": 55, "column": 5, "nodeType": "2961", "messageId": "2962", "endLine": 55, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3393", "line": 61, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 61, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "3394", "line": 61, "column": 21, "nodeType": "2961", "messageId": "2962", "endLine": 61, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3395", "line": 61, "column": 29, "nodeType": "2961", "messageId": "2962", "endLine": 61, "endColumn": 33}, {"ruleId": "2959", "severity": 1, "message": "3396", "line": 61, "column": 35, "nodeType": "2961", "messageId": "2962", "endLine": 61, "endColumn": 38}, {"ruleId": "2959", "severity": 1, "message": "3026", "line": 2, "column": 57, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 64}, {"ruleId": "2959", "severity": 1, "message": "3301", "line": 7, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 7, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3071", "line": 8, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 8, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3021", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3072", "line": 10, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3014", "line": 11, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3074", "line": 12, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 12, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3397", "line": 16, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 16, "endColumn": 26}, {"ruleId": "2959", "severity": 1, "message": "3026", "line": 2, "column": 88, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 95}, {"ruleId": "2959", "severity": 1, "message": "3012", "line": 2, "column": 97, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 101}, {"ruleId": "2959", "severity": 1, "message": "3058", "line": 2, "column": 124, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 134}, {"ruleId": "2959", "severity": 1, "message": "3301", "line": 7, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 7, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3021", "line": 9, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3072", "line": 10, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "2995", "line": 12, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 12, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3398", "line": 16, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 16, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3399", "line": 17, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 17, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 20, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 20, "endColumn": 16}, {"ruleId": "3400", "severity": 1, "message": "3401", "line": 120, "column": 13, "nodeType": "3402", "messageId": "3403", "endLine": 120, "endColumn": 40}, {"ruleId": "2959", "severity": 1, "message": "3012", "line": 2, "column": 57, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 61}, {"ruleId": "2959", "severity": 1, "message": "3084", "line": 4, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3404", "line": 6, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 6, "endColumn": 24}, {"ruleId": "2959", "severity": 1, "message": "3405", "line": 6, "column": 26, "nodeType": "2961", "messageId": "2962", "endLine": 6, "endColumn": 39}, {"ruleId": "2959", "severity": 1, "message": "3406", "line": 7, "column": 28, "nodeType": "2961", "messageId": "2962", "endLine": 7, "endColumn": 44}, {"ruleId": "2959", "severity": 1, "message": "3005", "line": 3, "column": 15, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "3026", "line": 3, "column": 119, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 126}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 44, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 44, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3407", "line": 100, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 100, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3408", "line": 100, "column": 23, "nodeType": "2961", "messageId": "2962", "endLine": 100, "endColumn": 37}, {"ruleId": "2959", "severity": 1, "message": "3012", "line": 2, "column": 155, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 159}, {"ruleId": "2959", "severity": 1, "message": "3397", "line": 14, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 26}, {"ruleId": "2959", "severity": 1, "message": "3186", "line": 12, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 12, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3215", "line": 14, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3005", "line": 2, "column": 15, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "3043", "line": 2, "column": 32, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 37}, {"ruleId": "2959", "severity": 1, "message": "3409", "line": 8, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 8, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3410", "line": 9, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3411", "line": 10, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3412", "line": 1, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 26}, {"ruleId": "2959", "severity": 1, "message": "3413", "line": 1, "column": 28, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 38}, {"ruleId": "2959", "severity": 1, "message": "3414", "line": 1, "column": 40, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 52}, {"ruleId": "2959", "severity": 1, "message": "3415", "line": 1, "column": 54, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 66}, {"ruleId": "2959", "severity": 1, "message": "3416", "line": 4, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3334", "line": 5, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "3335", "line": 6, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 6, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3336", "line": 7, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 7, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3417", "line": 8, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 8, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3418", "line": 9, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3419", "line": 10, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3213", "line": 11, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 15}, {"ruleId": "2959", "severity": 1, "message": "3420", "line": 12, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 12, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3421", "line": 13, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 13, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3312", "line": 3, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3422", "line": 14, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3423", "line": 2, "column": 38, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 43}, {"ruleId": "2959", "severity": 1, "message": "3195", "line": 5, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3424", "line": 2, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3293", "line": 10, "column": 122, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 130}, {"ruleId": "2959", "severity": 1, "message": "2995", "line": 11, "column": 30, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 34}, {"ruleId": "2971", "severity": 1, "message": "2979", "line": 73, "column": 6, "nodeType": "2973", "endLine": 73, "endColumn": 8, "suggestions": "3425"}, {"ruleId": "2959", "severity": 1, "message": "3005", "line": 21, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 21, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3426", "line": 154, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 154, "endColumn": 9}, {"ruleId": "2959", "severity": 1, "message": "3427", "line": 155, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 155, "endColumn": 9}, {"ruleId": "2959", "severity": 1, "message": "3428", "line": 156, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 156, "endColumn": 9}, {"ruleId": "2959", "severity": 1, "message": "3429", "line": 157, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 157, "endColumn": 9}, {"ruleId": "2959", "severity": 1, "message": "3430", "line": 158, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 158, "endColumn": 9}, {"ruleId": "2959", "severity": 1, "message": "3012", "line": 9, "column": 172, "nodeType": "2961", "messageId": "2962", "endLine": 9, "endColumn": 176}, {"ruleId": "2959", "severity": 1, "message": "3301", "line": 12, "column": 13, "nodeType": "2961", "messageId": "2962", "endLine": 12, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3431", "line": 14, "column": 8, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "2987", "line": 15, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 15, "endColumn": 16}, {"ruleId": "2971", "severity": 1, "message": "3432", "line": 40, "column": 6, "nodeType": "2961", "endLine": 40, "endColumn": 18, "suppressions": "3433"}, {"ruleId": "2971", "severity": 1, "message": "3434", "line": 40, "column": 6, "nodeType": "2961", "endLine": 40, "endColumn": 18, "suggestions": "3435", "suppressions": "3436"}, {"ruleId": "2959", "severity": 1, "message": "3060", "line": 10, "column": 34, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 50}, {"ruleId": "2959", "severity": 1, "message": "3382", "line": 10, "column": 59, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 63}, {"ruleId": "2959", "severity": 1, "message": "3084", "line": 11, "column": 15, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "2994", "line": 3, "column": 46, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 56}, {"ruleId": "2959", "severity": 1, "message": "3219", "line": 3, "column": 64, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 70}, {"ruleId": "2968", "severity": 1, "message": "2969", "line": 396, "column": 1, "nodeType": "2970", "endLine": 404, "endColumn": 3}, {"ruleId": "2959", "severity": 1, "message": "3437", "line": 3, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 24}, {"ruleId": "2959", "severity": 1, "message": "3006", "line": 3, "column": 131, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 140}, {"ruleId": "2959", "severity": 1, "message": "3438", "line": 1, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 1, "endColumn": 20}, {"ruleId": "2959", "severity": 1, "message": "3261", "line": 16, "column": 25, "nodeType": "2961", "messageId": "2962", "endLine": 16, "endColumn": 38}, {"ruleId": "2959", "severity": 1, "message": "3339", "line": 27, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 27, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "3439", "line": 5, "column": 26, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 38}, {"ruleId": "2959", "severity": 1, "message": "3440", "line": 14, "column": 15, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3224", "line": 3, "column": 73, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 78}, {"ruleId": "2959", "severity": 1, "message": "3225", "line": 3, "column": 80, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 89}, {"ruleId": "2959", "severity": 1, "message": "3226", "line": 3, "column": 91, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 100}, {"ruleId": "2959", "severity": 1, "message": "3227", "line": 3, "column": 102, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 116}, {"ruleId": "2959", "severity": 1, "message": "3228", "line": 3, "column": 118, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 127}, {"ruleId": "2959", "severity": 1, "message": "3229", "line": 3, "column": 129, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 137}, {"ruleId": "2959", "severity": 1, "message": "3005", "line": 3, "column": 139, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 144}, {"ruleId": "2959", "severity": 1, "message": "3441", "line": 14, "column": 17, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3442", "line": 41, "column": 23, "nodeType": "2961", "messageId": "2962", "endLine": 41, "endColumn": 37}, {"ruleId": "2959", "severity": 1, "message": "3443", "line": 7, "column": 27, "nodeType": "2961", "messageId": "2962", "endLine": 7, "endColumn": 35}, {"ruleId": "2959", "severity": 1, "message": "3444", "line": 16, "column": 20, "nodeType": "2961", "messageId": "2962", "endLine": 16, "endColumn": 32}, {"ruleId": "2959", "severity": 1, "message": "3445", "line": 17, "column": 17, "nodeType": "2961", "messageId": "2962", "endLine": 17, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3446", "line": 22, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 36}, {"ruleId": "2959", "severity": 1, "message": "3447", "line": 54, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 54, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3037", "line": 121, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 121, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3041", "line": 122, "column": 18, "nodeType": "2961", "messageId": "2962", "endLine": 122, "endColumn": 32}, {"ruleId": "2959", "severity": 1, "message": "3290", "line": 123, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 123, "endColumn": 24}, {"ruleId": "2959", "severity": 1, "message": "3266", "line": 124, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 124, "endColumn": 26}, {"ruleId": "2959", "severity": 1, "message": "3164", "line": 125, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 125, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3448", "line": 125, "column": 34, "nodeType": "2961", "messageId": "2962", "endLine": 125, "endColumn": 46}, {"ruleId": "2959", "severity": 1, "message": "3449", "line": 441, "column": 13, "nodeType": "2961", "messageId": "2962", "endLine": 441, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3450", "line": 215, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 215, "endColumn": 23}, {"ruleId": "2959", "severity": 1, "message": "3451", "line": 4, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 18}, {"ruleId": "2959", "severity": 1, "message": "3385", "line": 2, "column": 15, "nodeType": "2961", "messageId": "2962", "endLine": 2, "endColumn": 25}, {"ruleId": "2971", "severity": 1, "message": "3452", "line": 40, "column": 6, "nodeType": "2973", "endLine": 40, "endColumn": 14, "suggestions": "3453"}, {"ruleId": "2971", "severity": 1, "message": "3454", "line": 153, "column": 6, "nodeType": "2973", "endLine": 153, "endColumn": 8, "suggestions": "3455"}, {"ruleId": "2971", "severity": 1, "message": "3454", "line": 162, "column": 6, "nodeType": "2973", "endLine": 162, "endColumn": 23, "suggestions": "3456"}, {"ruleId": "2959", "severity": 1, "message": "3060", "line": 3, "column": 145, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 161}, {"ruleId": "2959", "severity": 1, "message": "2997", "line": 4, "column": 24, "nodeType": "2961", "messageId": "2962", "endLine": 4, "endColumn": 30}, {"ruleId": "2959", "severity": 1, "message": "3340", "line": 26, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 26, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3457", "line": 96, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 96, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3458", "line": 5, "column": 7, "nodeType": "2961", "messageId": "2962", "endLine": 5, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3081", "line": 11, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3459", "line": 13, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 13, "endColumn": 8}, {"ruleId": "2959", "severity": 1, "message": "3460", "line": 58, "column": 17, "nodeType": "2961", "messageId": "2962", "endLine": 58, "endColumn": 25}, {"ruleId": "2959", "severity": 1, "message": "3088", "line": 10, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 10, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3461", "line": 17, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 17, "endColumn": 17}, {"ruleId": "2959", "severity": 1, "message": "3076", "line": 25, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 25, "endColumn": 12}, {"ruleId": "2959", "severity": 1, "message": "3070", "line": 26, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 26, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3215", "line": 27, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 27, "endColumn": 11}, {"ruleId": "2959", "severity": 1, "message": "3026", "line": 8, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 8, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3462", "line": 40, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 40, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "3441", "line": 41, "column": 17, "nodeType": "2961", "messageId": "2962", "endLine": 41, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3463", "line": 44, "column": 12, "nodeType": "2961", "messageId": "2962", "endLine": 44, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3156", "line": 79, "column": 9, "nodeType": "2961", "messageId": "2962", "endLine": 79, "endColumn": 19}, {"ruleId": "2959", "severity": 1, "message": "3464", "line": 103, "column": 18, "nodeType": "2961", "messageId": "2962", "endLine": 103, "endColumn": 27}, {"ruleId": "2959", "severity": 1, "message": "3465", "line": 104, "column": 25, "nodeType": "2961", "messageId": "2962", "endLine": 104, "endColumn": 41}, {"ruleId": "2959", "severity": 1, "message": "3322", "line": 36, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 36, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "3014", "line": 22, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 22, "endColumn": 13}, {"ruleId": "2959", "severity": 1, "message": "3230", "line": 3, "column": 118, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 122}, {"ruleId": "2959", "severity": 1, "message": "3231", "line": 3, "column": 124, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 132}, {"ruleId": "2959", "severity": 1, "message": "3232", "line": 3, "column": 134, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 146}, {"ruleId": "2959", "severity": 1, "message": "2994", "line": 3, "column": 157, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 167}, {"ruleId": "2959", "severity": 1, "message": "3281", "line": 12, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 12, "endColumn": 7}, {"ruleId": "2959", "severity": 1, "message": "3466", "line": 14, "column": 17, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 21}, {"ruleId": "2959", "severity": 1, "message": "3351", "line": 19, "column": 10, "nodeType": "2961", "messageId": "2962", "endLine": 19, "endColumn": 33}, {"ruleId": "2959", "severity": 1, "message": "3467", "line": 3, "column": 86, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 100}, {"ruleId": "2959", "severity": 1, "message": "3088", "line": 3, "column": 185, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 192}, {"ruleId": "2968", "severity": 1, "message": "2969", "line": 52, "column": 1, "nodeType": "2970", "endLine": 58, "endColumn": 3}, {"ruleId": "2968", "severity": 1, "message": "2969", "line": 191, "column": 1, "nodeType": "2970", "endLine": 195, "endColumn": 3}, {"ruleId": "2968", "severity": 1, "message": "2969", "line": 99, "column": 1, "nodeType": "2970", "endLine": 102, "endColumn": 3}, {"ruleId": "2968", "severity": 1, "message": "2969", "line": 147, "column": 1, "nodeType": "2970", "endLine": 149, "endColumn": 3}, {"ruleId": "2968", "severity": 1, "message": "2969", "line": 205, "column": 1, "nodeType": "2970", "endLine": 207, "endColumn": 3}, {"ruleId": "3468", "severity": 1, "message": "3469", "line": 47, "column": 9, "nodeType": "3470", "messageId": "3403", "endLine": 58, "endColumn": 11}, {"ruleId": "3468", "severity": 1, "message": "3469", "line": 62, "column": 9, "nodeType": "3471", "messageId": "3403", "endLine": 70, "endColumn": 10}, {"ruleId": "3468", "severity": 1, "message": "3469", "line": 74, "column": 9, "nodeType": "3471", "messageId": "3403", "endLine": 84, "endColumn": 10}, {"ruleId": "2968", "severity": 1, "message": "2969", "line": 210, "column": 1, "nodeType": "2970", "endLine": 212, "endColumn": 3}, {"ruleId": "2959", "severity": 1, "message": "3472", "line": 3, "column": 18, "nodeType": "2961", "messageId": "2962", "endLine": 3, "endColumn": 28}, {"ruleId": "2959", "severity": 1, "message": "3473", "line": 106, "column": 11, "nodeType": "2961", "messageId": "2962", "endLine": 106, "endColumn": 24}, {"ruleId": "2959", "severity": 1, "message": "3084", "line": 11, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 10}, {"ruleId": "2959", "severity": 1, "message": "3474", "line": 11, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 11, "endColumn": 14}, {"ruleId": "2959", "severity": 1, "message": "3475", "line": 14, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 14, "endColumn": 15}, {"ruleId": "2959", "severity": 1, "message": "3476", "line": 15, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 15, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "3477", "line": 16, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 16, "endColumn": 16}, {"ruleId": "2959", "severity": 1, "message": "3478", "line": 17, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 17, "endColumn": 22}, {"ruleId": "2959", "severity": 1, "message": "3479", "line": 18, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 18, "endColumn": 15}, {"ruleId": "2959", "severity": 1, "message": "3480", "line": 19, "column": 3, "nodeType": "2961", "messageId": "2962", "endLine": 19, "endColumn": 20}, {"ruleId": "2971", "severity": 1, "message": "3481", "line": 15, "column": 3, "nodeType": "2961", "endLine": 15, "endColumn": 12, "suggestions": "3482"}, {"ruleId": "2959", "severity": 1, "message": "3483", "line": 169, "column": 14, "nodeType": "2961", "messageId": "2962", "endLine": 169, "endColumn": 18}, {"ruleId": "2968", "severity": 1, "message": "2969", "line": 337, "column": 1, "nodeType": "2970", "endLine": 345, "endColumn": 3}, "@typescript-eslint/no-unused-vars", "'t' is assigned a value but never used.", "Identifier", "unusedVar", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'showLanguageMenu' is assigned a value but never used.", "'useTranslation' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'synchronize'. Either include it or remove the dependency array.", "ArrayExpression", ["3484"], "'useMockData' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'getStats' and 'syncMockData'. Either include them or remove the dependency array.", ["3485"], "'DataChangeEventType' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadData'. Either include it or remove the dependency array.", ["3486"], "'useState' is defined but never used.", "'useEffect' is defined but never used.", "React Hook useEffect has a missing dependency: 'language'. Either include it or remove the dependency array.", ["3487"], "React Hook React.useMemo has an unnecessary dependency: 'currentColor'. Either exclude it or remove the dependency array.", ["3488"], "'motion' is defined but never used.", "'ColorLens' is defined but never used.", "'language' is assigned a value but never used.", "'changeLanguage' is assigned a value but never used.", "'isTablet' is assigned a value but never used.", "'currentColor' is assigned a value but never used.", "'availableColors' is assigned a value but never used.", "'IconButton' is defined but never used.", "'Edit' is defined but never used.", "'Add' is defined but never used.", "'Delete' is defined but never used.", "'mockUsers' is assigned a value but never used.", "'handleOpenUserDialog' is assigned a value but never used.", "'handleDeleteUser' is assigned a value but never used.", "'Security' is defined but never used.", "'ThemeSafeButton' is defined but never used.", "'isMobile' is assigned a value but never used.", "'itemVariants' is assigned a value but never used.", "'Paper' is defined but never used.", "'TextField' is defined but never used.", "no-useless-concat", "Unexpected string concatenation of literals.", "BinaryExpression", "unexpectedConcat", "'useRef' is defined but never used.", "'Chip' is defined but never used.", "'LinearProgress' is defined but never used.", "'FilterList' is defined but never used.", "'DateRange' is defined but never used.", "'PieChartIcon' is defined but never used.", "'Timeline' is defined but never used.", "'Restaurant' is defined but never used.", "'Schedule' is defined but never used.", "'Science' is defined but never used.", "'Print' is defined but never used.", "'mockNutritionPlans' is defined but never used.", "'getChipStyle' is defined but never used.", "'Card' is defined but never used.", "'CardContent' is defined but never used.", "'Divider' is defined but never used.", "'LineChart' is defined but never used.", "'Line' is defined but never used.", "'TrendingUp' is defined but never used.", "'Visibility' is defined but never used.", "'BarChartIcon' is defined but never used.", "'ShowChart' is defined but never used.", "'BlendedBackgroundCard' is defined but never used.", "'formatCurrency' is defined but never used.", "'formatDate' is defined but never used.", "'calculateAge' is defined but never used.", "'animals' is assigned a value but never used.", "'stats' is assigned a value but never used.", "'animalsLoading' is assigned a value but never used.", "'transactions' is assigned a value but never used.", "'financialStats' is assigned a value but never used.", "'financialLoading' is assigned a value but never used.", "'alpha' is defined but never used.", "'AreaChart' is defined but never used.", "'Area' is defined but never used.", "'MonetizationOn' is defined but never used.", "'PieChart' is defined but never used.", "'SimpleButton' is defined but never used.", "'selectedReports' is assigned a value but never used.", "'selectSingleReport' is assigned a value but never used.", "'clearSelectedReports' is assigned a value but never used.", "'ListItemAvatar' is defined but never used.", "'Tab' is defined but never used.", "'Tabs' is defined but never used.", "'Menu' is defined but never used.", "'MenuItem' is defined but never used.", "'FormControl' is defined but never used.", "'InputLabel' is defined but never used.", "'Select' is defined but never used.", "'CircularProgress' is defined but never used.", "'MuiTooltip' is defined but never used.", "'TablePagination' is defined but never used.", "'Badge' is defined but never used.", "'Alert' is defined but never used.", "'Skeleton' is defined but never used.", "'Favorite' is defined but never used.", "'ChildCare' is defined but never used.", "'Male' is defined but never used.", "'Warning' is defined but never used.", "'BarChart' is defined but never used.", "'Download' is defined but never used.", "'Share' is defined but never used.", "'TrendingDown' is defined but never used.", "'Info' is defined but never used.", "'MonitorHeart' is defined but never used.", "'EventNote' is defined but never used.", "'Medication' is defined but never used.", "'HealthAndSafety' is defined but never used.", "'InsertChart' is defined but never used.", "'ListAlt' is defined but never used.", "'Thermostat' is defined but never used.", "'ChildFriendly' is defined but never used.", "'Analytics' is defined but never used.", "'Refresh' is defined but never used.", "'Pie' is defined but never used.", "'Cell' is defined but never used.", "'ResponsiveContainer' is defined but never used.", "'Tooltip' is defined but never used.", "'RadarChart' is defined but never used.", "'PolarGrid' is defined but never used.", "'PolarAngleAxis' is defined but never used.", "'PolarRadiusAxis' is defined but never used.", "'Radar' is defined but never used.", "'Legend' is defined but never used.", "'Bar' is defined but never used.", "'XAxis' is defined but never used.", "'YAxis' is defined but never used.", "'CartesianGrid' is defined but never used.", "'ScatterChart' is defined but never used.", "'Scatter' is defined but never used.", "'ComposedChart' is defined but never used.", "'RechartsBarChart' is defined but never used.", "'EnhancedPieLabelRenderProps' is defined but never used.", "'ModuleHeader' is defined but never used.", "'ModuleContainer' is defined but never used.", "'ModuleHeaderCard' is defined but never used.", "'ResponsiveNavTabs' is defined but never used.", "'differenceInDays' is defined but never used.", "'addMonths' is defined but never used.", "'subMonths' is defined but never used.", "'subDays' is defined but never used.", "'TabPanel' is assigned a value but never used.", "'error' is assigned a value but never used.", "'timeRange' is assigned a value but never used.", "'setTimeRange' is assigned a value but never used.", "'setSelectedMetric' is assigned a value but never used.", "'selectedBreedingType' is assigned a value but never used.", "'setSelectedBreedingType' is assigned a value but never used.", "'selectedMonth' is assigned a value but never used.", "'setSelectedMonth' is assigned a value but never used.", "'chartTooltip' is assigned a value but never used.", "'setChartTooltip' is assigned a value but never used.", "'selectedChart' is assigned a value but never used.", "'setSelectedChart' is assigned a value but never used.", "'anchorEl' is assigned a value but never used.", "'setExportFormat' is assigned a value but never used.", "'page' is assigned a value but never used.", "'rowsPerPage' is assigned a value but never used.", "'dateRange' is assigned a value but never used.", "'setDateRange' is assigned a value but never used.", "'animalTypeFilter' is assigned a value but never used.", "'setAnimalTypeFilter' is assigned a value but never used.", "'handleTabIdChange' is assigned a value but never used.", "'handleMenuClick' is assigned a value but never used.", "'handleExport' is assigned a value but never used.", "'handleChangePage' is assigned a value but never used.", "'handleChangeRowsPerPage' is assigned a value but never used.", "'totalBirths' is assigned a value but never used.", "'totalOffspring' is assigned a value but never used.", "'twinRate' is assigned a value but never used.", "'complicationRate' is assigned a value but never used.", "'assistanceRate' is assigned a value but never used.", "'averageBreedingToConception' is assigned a value but never used.", "'averageGestationLength' is assigned a value but never used.", "'averageCalvingInterval' is assigned a value but never used.", "'successRateTrendData' is assigned a value but never used.", "'breedingSuccessByAnimalType' is assigned a value but never used.", "'breedingEfficiencyData' is assigned a value but never used.", "'birthComplicationsData' is assigned a value but never used.", "'geneticImprovementData' is assigned a value but never used.", "'seasonalBreedingData' is assigned a value but never used.", "'calvingDistributionData' is assigned a value but never used.", "'gestationPeriodData' is assigned a value but never used.", "'COLORS' is assigned a value but never used.", "'BusinessAnalyticsCard' is defined but never used.", "'themeColor' is assigned a value but never used.", "'Container' is defined but never used.", "'Insights' is defined but never used.", "'BusinessMetricsCard' is defined but never used.", "'kpiData' is assigned a value but never used.", "'isLoading' is assigned a value but never used.", "'setIsLoading' is assigned a value but never used.", "'breedingLoading' is assigned a value but never used.", "'feedingRecords' is assigned a value but never used.", "'feedingLoading' is assigned a value but never used.", "'strategyLoading' is assigned a value but never used.", "'predictionsLoading' is assigned a value but never used.", "'containerVariants' is assigned a value but never used.", "'handleTimeRangeChange' is assigned a value but never used.", "'StrategyIcon' is defined but never used.", "'useNavigate' is defined but never used.", "'BusinessPredictionCard' is defined but never used.", "'BusinessInsightCard' is defined but never used.", "'predictions' is assigned a value but never used.", "'insights' is assigned a value but never used.", "'Box' is defined but never used.", "'setSelectedDataType' is assigned a value but never used.", "'AnimatedChart' is defined but never used.", "'setSelectedFeedType' is assigned a value but never used.", "'MoneyOff' is defined but never used.", "'financialService' is defined but never used.", "'useModuleData' is defined but never used.", "'categoryData' is assigned a value but never used.", "'AssetManagementDashboard' is defined but never used.", "'RetirementTracking' is defined but never used.", "'CalendarToday' is defined but never used.", "'ModernDashboard' is defined but never used.", "'ModernDataTable' is defined but never used.", "'ModernCard' is defined but never used.", "'setSelectedSpecies' is assigned a value but never used.", "'mockFeedingRecords' is defined but never used.", "'mockFeedInventory' is defined but never used.", "'mockFeedingPlans' is defined but never used.", "'Collection' is defined but never used.", "'MONGODB_URI' is assigned a value but never used.", "'spinnerStyles' is assigned a value but never used.", "'useMockData' is defined but never used.", "React Hook useEffect has a missing dependency: 'updateStats'. Either include it or remove the dependency array.", ["3489"], "'Budget' is defined but never used.", "'currentMonth' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchFinancialData'. Either include it or remove the dependency array.", ["3490"], "'mockHealthRecords' is defined but never used.", "'mockVaccinations' is defined but never used.", "The 'showSnackbar' function makes the dependencies of useCallback Hook (at line 88) change on every render. To fix this, wrap the definition of 'showSnackbar' in its own useCallback() Hook.", "VariableDeclarator", ["3491"], "React Hook useCallback has missing dependencies: 'calculateRevenueByMonth' and 'calculateTopSellingProducts'. Either include them or remove the dependency array.", ["3492"], "React Hook useEffect has a missing dependency: 'fetchFeedingData'. Either include it or remove the dependency array.", ["3493"], "'statuses' is assigned a value but never used.", "'Pets' is defined but never used.", "'Settings' is defined but never used.", "'Notifications' is defined but never used.", "'StatusChip' is defined but never used.", "'CalendarMonth' is defined but never used.", "'Avatar' is defined but never used.", "'ListItemSecondaryAction' is defined but never used.", "'ErrorOutline' is defined but never used.", "'Description' is defined but never used.", "'Inspection' is defined but never used.", "'Table' is defined but never used.", "'TableBody' is defined but never used.", "'TableCell' is defined but never used.", "'TableContainer' is defined but never used.", "'TableHead' is defined but never used.", "'TableRow' is defined but never used.", "'List' is defined but never used.", "'ListItem' is defined but never used.", "'ListItemText' is defined but never used.", "'ListItemIcon' is defined but never used.", "'CheckCircle' is defined but never used.", "'AccessibleWrapper' is defined but never used.", "'ModernChart' is defined but never used.", "'handleKeyboardNavigation' is assigned a value but never used.", "'getAriaAttributes' is assigned a value but never used.", "'setFocus' is assigned a value but never used.", "'PencilIcon' is defined but never used.", "'FileCopy' is defined but never used.", "'OndemandVideo' is defined but never used.", "'Link' is defined but never used.", "'LiveHelp' is defined but never used.", "'Breadcrumbs' is defined but never used.", "'Book' is defined but never used.", "'MenuBook' is defined but never used.", "'CardMedia' is defined but never used.", "'getSelectableTabsStyles' is defined but never used.", "'getSelectableTabPanelStyles' is defined but never used.", "'getSelectableTabContentStyles' is defined but never used.", "'secondaryColor' is assigned a value but never used.", "@typescript-eslint/no-use-before-define", "'down' was used before it was defined.", "noUseBeforeDefine", "'up' was used before it was defined.", "'between' was used before it was defined.", "'only' was used before it was defined.", "'axios' is defined but never used.", "'FeedingRecord' is defined but never used.", "'FeedInventory' is defined but never used.", "'HealthRecord' is defined but never used.", "'BreedingRecord' is defined but never used.", "'FinancialRecord' is defined but never used.", "'feedInventory' is assigned a value but never used.", "'breedingRecords' is assigned a value but never used.", "'generateMarketTable' is assigned a value but never used.", "'generatePerformanceTable' is assigned a value but never used.", "'generateAnalysisTable' is assigned a value but never used.", "'XLSX' is defined but never used.", "'jsPDF' is defined but never used.", "'LocalShipping' is defined but never used.", "'AttachMoney' is defined but never used.", "'AnimatedDashboardCard' is defined but never used.", "'feedTypeData' is assigned a value but never used.", "'stockLevelData' is assigned a value but never used.", "'tableColumns' is assigned a value but never used.", "'getIconForFeedType' is assigned a value but never used.", "'Inventory' is defined but never used.", "'Business' is defined but never used.", "'Star' is defined but never used.", "'getStatusColor' is assigned a value but never used.", "'Search' is defined but never used.", "'Close' is defined but never used.", "'SubModuleMetricsCard' is defined but never used.", "'SubModuleActionPanel' is defined but never used.", "'CustomCalendarPopup' is defined but never used.", "'generateSampleEvents' is assigned a value but never used.", "'animalStats' is assigned a value but never used.", "'healthRecords' is assigned a value but never used.", "'Animal' is defined but never used.", "'setSchedules' is assigned a value but never used.", "'Snackbar' is defined but never used.", "'showSnackbar' is assigned a value but never used.", "'incomeSources' is assigned a value but never used.", "'ResponsiveLayout' is defined but never used.", "'notificationSchedulerService' is defined but never used.", "'setShowTestNotification' is assigned a value but never used.", "'getRiskColor' is assigned a value but never used.", "'ArrowForward' is defined but never used.", "'MoreVert' is defined but never used.", "'navigate' is assigned a value but never used.", "'analyticsData' is assigned a value but never used.", "'useFetchData' is defined but never used.", "'generateReport' is defined but never used.", "'selectedInvoice' is assigned a value but never used.", "'Event' is defined but never used.", "'getStatusChip' is assigned a value but never used.", "'LocalHospital' is defined but never used.", "'Healing' is defined but never used.", "'AccessTime' is defined but never used.", "'mockAnimals' is defined but never used.", "'AnimatedBackgroundCard' is defined but never used.", "'format' is defined but never used.", "'getTestResultStatusChip' is assigned a value but never used.", "'getRecordsByType' is assigned a value but never used.", "'healthRecordsData' is assigned a value but never used.", "'FormControlLabel' is defined but never used.", "'Collapse' is defined but never used.", "'Fade' is defined but never used.", "'SafeButton' is defined but never used.", "'CustomButton' is defined but never used.", "'themeColors' is defined but never used.", "'VisibilityOff' is defined but never used.", "'AnimatePresence' is defined but never used.", "'showRecentActivity' is assigned a value but never used.", "'toggleRecentActivity' is assigned a value but never used.", "'showAgentPrices' is assigned a value but never used.", "'toggleAgentPrices' is assigned a value but never used.", "'previewColor' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fontSize'. Either include it or remove the dependency array.", ["3494"], "'breedingMethods' is assigned a value but never used.", "'cattleBreeds' is assigned a value but never used.", "'sheepBreeds' is assigned a value but never used.", "'goatBreeds' is assigned a value but never used.", "'BusinessAnalyticsButton' is defined but never used.", "'GridColDef' is defined but never used.", "'isConnected' is assigned a value but never used.", "'loading' is assigned a value but never used.", "React Hook useCallback has an unnecessary dependency: 'isConnected'. Either exclude it or remove the dependency array.", ["3495"], ["3496"], ["3497"], ["3498"], ["3499"], ["3500"], ["3501"], "'CardActions' is defined but never used.", "'AnimalStatusBadge' is defined but never used.", "'getConsistentBackground' is defined but never used.", "'theme' is assigned a value but never used.", "'reorderLevel' is assigned a value but never used.", "'handleNumberChange' is assigned a value but never used.", "'setLocationHistory' is assigned a value but never used.", "'upcomingTasksData' is assigned a value but never used.", "'sampleTasks' is assigned a value but never used.", "'selectedTask' is assigned a value but never used.", "'handleTaskClick' is assigned a value but never used.", "'SupplierContact' is defined but never used.", "'Rating' is defined but never used.", "'DatePicker' is defined but never used.", "'Language' is defined but never used.", "'LocationOn' is defined but never used.", "'TabPanel' is defined but never used.", "'a11yProps' is defined but never used.", "'getCategoryDisplayName' is assigned a value but never used.", "'supplierDetails' is assigned a value but never used.", "'tabValue' is assigned a value but never used.", "'handleTabChange' is assigned a value but never used.", "'handleSearchChange' is assigned a value but never used.", "'getPrimaryCategory' is assigned a value but never used.", "'getPrimaryContact' is assigned a value but never used.", "'getPrimaryLocation' is assigned a value but never used.", "'Dialog' is defined but never used.", "'DialogTitle' is defined but never used.", "'DialogContent' is defined but never used.", "'DialogActions' is defined but never used.", "'LazyImage' is defined but never used.", "'getModuleBackgroundImage' is assigned a value but never used.", "'getOverlayStyles' is assigned a value but never used.", "'Grid' is defined but never used.", "'LocalLibrary' is defined but never used.", "'MoreVertIcon' is defined but never used.", "'Typography' is defined but never used.", "'useTheme' is defined but never used.", "'createBlendedBackground' is defined but never used.", "'translate' is assigned a value but never used.", "'BoxProps' is defined but never used.", "'Overlay' is defined but never used.", "'popperRef' is assigned a value but never used.", "'offset' is assigned a value but never used.", "'position' is assigned a value but never used.", "'events' is assigned a value but never used.", "'date' is assigned a value but never used.", "'end' is assigned a value but never used.", "'getSafeElevation' is defined but never used.", "'ArrowUpward' is defined but never used.", "'ArrowDownward' is defined but never used.", "no-dupe-keys", "Duplicate key '& .MuiTableContainer-root'.", "ObjectExpression", "unexpected", "'LoadingOverlay' is defined but never used.", "'ErrorBoundary' is defined but never used.", "'getSafeAnimation' is defined but never used.", "'showTooltip' is assigned a value but never used.", "'setShowTooltip' is assigned a value but never used.", "'diagnoses' is assigned a value but never used.", "'treatments' is assigned a value but never used.", "'medications' is assigned a value but never used.", "'generateMockData' is defined but never used.", "'randomDate' is defined but never used.", "'randomNumber' is defined but never used.", "'randomChoice' is defined but never used.", "'speciesList' is assigned a value but never used.", "'gameBreeds' is assigned a value but never used.", "'horseBreeds' is assigned a value but never used.", "'genders' is assigned a value but never used.", "'locations' is assigned a value but never used.", "'healthStatuses' is assigned a value but never used.", "'isFixed' is assigned a value but never used.", "'Theme' is defined but never used.", "'DirectFixButton' is defined but never used.", ["3502"], "'xs' is assigned a value but never used.", "'sm' is assigned a value but never used.", "'md' is assigned a value but never used.", "'lg' is assigned a value but never used.", "'xl' is assigned a value but never used.", "'StatusBadge' is defined but never used.", "React Hook useEffect was passed a dependency list that is not an array literal. This means we can't statically verify whether you've passed the correct dependencies.", ["3503"], "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["3504"], ["3505"], "'getModuleTheme' is defined but never used.", "'useContext' is defined but never used.", "'FollowUpDate' is defined but never used.", "'SettingsIcon' is defined but never used.", "'FilterIcon' is defined but never used.", "'setRowsPerPage' is assigned a value but never used.", "'addHours' is defined but never used.", "'CalendarIcon' is defined but never used.", "'TimeIcon' is defined but never used.", "'notificationHistoryService' is defined but never used.", "'additionalPhones' is assigned a value but never used.", "'feedingStats' is assigned a value but never used.", "'strategyData' is assigned a value but never used.", "'highlightColor' is assigned a value but never used.", "'ObjectId' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchSuppliers'. Either include it or remove the dependency array.", ["3506"], "React Hook useEffect has a missing dependency: 'fetchLocations'. Either include it or remove the dependency array.", ["3507"], ["3508"], "'handleDelete' is assigned a value but never used.", "'containerStyle' is assigned a value but never used.", "'Grain' is defined but never used.", "'setError' is assigned a value but never used.", "'AddIcon' is defined but never used.", "'SortIcon' is defined but never used.", "'CloseIcon' is defined but never used.", "'setSortBy' is assigned a value but never used.", "'setSortDirection' is assigned a value but never used.", "'Sort' is defined but never used.", "'InputAdornment' is defined but never used.", "no-extend-native", "Object prototype is read only, properties should not be added.", "CallExpression", "AssignmentExpression", "'addMinutes' is defined but never used.", "'daysRemaining' is assigned a value but never used.", "'BusinessKPI' is defined but never used.", "'BusinessRisk' is defined but never used.", "'BusinessOpportunity' is defined but never used.", "'StrategicGoal' is defined but never used.", "'StrategicInitiative' is defined but never used.", "'SWOTAnalysis' is defined but never used.", "'StrategicScenario' is defined but never used.", "React Hook useEffect contains a call to 'setRenderCount'. Without a list of dependencies, this can lead to an infinite chain of updates. To fix this, pass [] as a second argument to the useEffect Hook.", ["3509"], "'type' is assigned a value but never used.", {"desc": "3510", "fix": "3511"}, {"desc": "3512", "fix": "3513"}, {"desc": "3514", "fix": "3515"}, {"desc": "3516", "fix": "3517"}, {"desc": "3518", "fix": "3519"}, {"desc": "3520", "fix": "3521"}, {"desc": "3522", "fix": "3523"}, {"desc": "3524", "fix": "3525"}, {"desc": "3526", "fix": "3527"}, {"desc": "3528", "fix": "3529"}, {"desc": "3530", "fix": "3531"}, {"desc": "3532", "fix": "3533"}, {"desc": "3534", "fix": "3535"}, {"desc": "3534", "fix": "3536"}, {"desc": "3534", "fix": "3537"}, {"desc": "3538", "fix": "3539"}, {"desc": "3538", "fix": "3540"}, {"desc": "3538", "fix": "3541"}, {"desc": "3514", "fix": "3542"}, {"kind": "3543", "justification": "3544"}, {"desc": "3545", "fix": "3546"}, {"kind": "3543", "justification": "3544"}, {"desc": "3547", "fix": "3548"}, {"desc": "3549", "fix": "3550"}, {"desc": "3551", "fix": "3552"}, {"desc": "3553", "fix": "3554"}, "Update the dependencies array to be: [pendingChanges, synchronize]", {"range": "3555", "text": "3556"}, "Update the dependencies array to be: [getStats, syncMockData]", {"range": "3557", "text": "3558"}, "Update the dependencies array to be: [loadData]", {"range": "3559", "text": "3560"}, "Update the dependencies array to be: [language]", {"range": "3561", "text": "3562"}, "Update the dependencies array to be: [mode, colorScheme]", {"range": "3563", "text": "3564"}, "Update the dependencies array to be: [updateStats]", {"range": "3565", "text": "3566"}, "Update the dependencies array to be: [fetchFinancialData]", {"range": "3567", "text": "3568"}, "Wrap the definition of 'showSnackbar' in its own useCallback() Hook.", {"range": "3569", "text": "3570"}, "Update the dependencies array to be: [orders, suppliers, auctions, calculateTopSellingProducts, calculateRevenueByMonth]", {"range": "3571", "text": "3572"}, "Update the dependencies array to be: [fetchFeedingData]", {"range": "3573", "text": "3574"}, "Update the dependencies array to be: [fontSize]", {"range": "3575", "text": "3576"}, "Update the dependencies array to be: [calculateStats, animals]", {"range": "3577", "text": "3578"}, "Update the dependencies array to be: [calculateStats]", {"range": "3579", "text": "3580"}, {"range": "3581", "text": "3580"}, {"range": "3582", "text": "3580"}, "Update the dependencies array to be: []", {"range": "3583", "text": "3584"}, {"range": "3585", "text": "3584"}, {"range": "3586", "text": "3584"}, {"range": "3587", "text": "3560"}, "directive", "", "Update the dependencies array to be: [fetchData]", {"range": "3588", "text": "3589"}, "Update the dependencies array to be: [fetchSuppliers, filter]", {"range": "3590", "text": "3591"}, "Update the dependencies array to be: [fetchLocations]", {"range": "3592", "text": "3593"}, "Update the dependencies array to be: [fetchLocations, refreshInterval]", {"range": "3594", "text": "3595"}, "Add dependencies array: []", {"range": "3596", "text": "3597"}, [2214, 2230], "[pendingChanges, synchronize]", [4955, 4957], "[getStats, syncMockData]", [2658, 2660], "[loadData]", [2636, 2638], "[language]", [29788, 29821], "[mode, colorScheme]", [1633, 1635], "[updateStats]", [3531, 3533], "[fetchFinancialData]", [1342, 1551], "useCallback((message: string, severity: 'success' | 'error' | 'info' | 'warning') => {\n    console.log(`${severity.toUpperCase()}: ${message}`);\n    // In a real app, you would use a proper snackbar/toast notification\n  })", [4524, 4553], "[orders, suppliers, auctions, calculateTopSellingProducts, calculateRevenueByMonth]", [24249, 24251], "[fetchFeedingData]", [5214, 5216], "[fontSize]", [3012, 3050], "[calculateStats, animals]", [3643, 3672], "[calculateStats]", [4354, 4383], [5009, 5038], [5477, 5490], "[]", [5917, 5930], [6363, 6376], [2498, 2500], [1080, 1092], "[fetchData]", [1597, 1605], "[fetchSuppliers, filter]", [4157, 4159], "[fetchLocations]", [4367, 4384], "[fetchLocations, refreshInterval]", [630, 630], ", []"]