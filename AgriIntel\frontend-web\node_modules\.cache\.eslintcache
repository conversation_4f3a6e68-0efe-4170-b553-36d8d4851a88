[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\App.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\reportWebVitals.ts": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\store.ts": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\i18n\\i18n.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\authSlice.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\animalSlice.ts": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\healthSlice.ts": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\breedingSlice.ts": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\uiSlice.ts": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\api\\authAPI.ts": "11"}, {"size": 3263, "mtime": 1748803490361, "results": "12", "hashOfConfig": "13"}, {"size": 1352, "mtime": 1748802870877, "results": "14", "hashOfConfig": "13"}, {"size": 425, "mtime": 1748799119079, "results": "15", "hashOfConfig": "13"}, {"size": 747, "mtime": 1748799127154, "results": "16", "hashOfConfig": "13"}, {"size": 1095, "mtime": 1748799313651, "results": "17", "hashOfConfig": "13"}, {"size": 8868, "mtime": 1748803758260, "results": "18", "hashOfConfig": "13"}, {"size": 8765, "mtime": 1748799201703, "results": "19", "hashOfConfig": "13"}, {"size": 4507, "mtime": 1748799226000, "results": "20", "hashOfConfig": "13"}, {"size": 5898, "mtime": 1748799260494, "results": "21", "hashOfConfig": "13"}, {"size": 3895, "mtime": 1748799281210, "results": "22", "hashOfConfig": "13"}, {"size": 4614, "mtime": 1748799302108, "results": "23", "hashOfConfig": "13"}, {"filePath": "24", "messages": "25", "suppressedMessages": "26", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "eltgwa", {"filePath": "27", "messages": "28", "suppressedMessages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\App.tsx", ["57"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\store.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\i18n\\i18n.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\authSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\animalSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\healthSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\breedingSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\uiSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\api\\authAPI.ts", [], [], {"ruleId": "58", "severity": 1, "message": "59", "line": 1, "column": 1, "nodeType": "60", "messageId": "61", "fix": "62"}, "unicode-bom", "Unexpected Unicode BOM (Byte Order Mark).", "Program", "unexpected", {"range": "63", "text": "64"}, [-1, 0], ""]