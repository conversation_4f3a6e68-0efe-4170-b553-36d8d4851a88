[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\index.tsx": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\App.tsx": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\reportWebVitals.ts": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\store.ts": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\i18n\\i18n.ts": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\authSlice.ts": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\animalSlice.ts": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\healthSlice.ts": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\breedingSlice.ts": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\uiSlice.ts": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\api\\authAPI.ts": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\NotFoundPage.tsx": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\auth\\LoginPage.tsx": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\auth\\RegisterPage.tsx": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\HealthPage.tsx": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AnimalDetailsPage.tsx": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\dashboard\\DashboardPage.tsx": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AnimalsPage.tsx": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\breeding\\BreedingPage.tsx": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\FinancialPage.tsx": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\FeedingPage.tsx": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\ReportsPage.tsx": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\settings\\SettingsPage.tsx": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\layout\\Layout.tsx": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\LoadingSpinner.tsx": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\layout\\AuthLayout.tsx": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\auth\\ProtectedRoute.tsx": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\layout\\Sidebar.tsx": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\layout\\Header.tsx": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\DashboardWidget.tsx": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\StatCard.tsx": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\ActivityFeed.tsx": "32"}, {"size": 3263, "mtime": 1748803490361, "results": "33", "hashOfConfig": "34"}, {"size": 5139, "mtime": 1748804528274, "results": "35", "hashOfConfig": "34"}, {"size": 425, "mtime": 1748799119079, "results": "36", "hashOfConfig": "34"}, {"size": 747, "mtime": 1748799127154, "results": "37", "hashOfConfig": "34"}, {"size": 1095, "mtime": 1748799313651, "results": "38", "hashOfConfig": "34"}, {"size": 8868, "mtime": 1748803758260, "results": "39", "hashOfConfig": "34"}, {"size": 8765, "mtime": 1748799201703, "results": "40", "hashOfConfig": "34"}, {"size": 4507, "mtime": 1748799226000, "results": "41", "hashOfConfig": "34"}, {"size": 5898, "mtime": 1748799260494, "results": "42", "hashOfConfig": "34"}, {"size": 3895, "mtime": 1748799281210, "results": "43", "hashOfConfig": "34"}, {"size": 4614, "mtime": 1748799302108, "results": "44", "hashOfConfig": "34"}, {"size": 980, "mtime": 1748799658226, "results": "45", "hashOfConfig": "34"}, {"size": 5591, "mtime": 1748799521497, "results": "46", "hashOfConfig": "34"}, {"size": 716, "mtime": 1748799588928, "results": "47", "hashOfConfig": "34"}, {"size": 601, "mtime": 1748799612547, "results": "48", "hashOfConfig": "34"}, {"size": 617, "mtime": 1748799605786, "results": "49", "hashOfConfig": "34"}, {"size": 12258, "mtime": 1748804855258, "results": "50", "hashOfConfig": "34"}, {"size": 1062, "mtime": 1748799598382, "results": "51", "hashOfConfig": "34"}, {"size": 609, "mtime": 1748799620324, "results": "52", "hashOfConfig": "34"}, {"size": 613, "mtime": 1748799635093, "results": "53", "hashOfConfig": "34"}, {"size": 602, "mtime": 1748799627844, "results": "54", "hashOfConfig": "34"}, {"size": 608, "mtime": 1748799642539, "results": "55", "hashOfConfig": "34"}, {"size": 598, "mtime": 1748799649805, "results": "56", "hashOfConfig": "34"}, {"size": 1080, "mtime": 1748799441184, "results": "57", "hashOfConfig": "34"}, {"size": 1151, "mtime": 1748799406414, "results": "58", "hashOfConfig": "34"}, {"size": 2085, "mtime": 1748799431537, "results": "59", "hashOfConfig": "34"}, {"size": 1594, "mtime": 1748803703954, "results": "60", "hashOfConfig": "34"}, {"size": 3282, "mtime": 1748799578784, "results": "61", "hashOfConfig": "34"}, {"size": 7893, "mtime": 1748801125488, "results": "62", "hashOfConfig": "34"}, {"size": 712, "mtime": 1748804622525, "results": "63", "hashOfConfig": "34"}, {"size": 1708, "mtime": 1748804636588, "results": "64", "hashOfConfig": "34"}, {"size": 3545, "mtime": 1748804659558, "results": "65", "hashOfConfig": "34"}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "eltgwa", {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 1, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "87", "messages": "88", "suppressedMessages": "89", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "90", "messages": "91", "suppressedMessages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "93", "messages": "94", "suppressedMessages": "95", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "96", "messages": "97", "suppressedMessages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "99", "messages": "100", "suppressedMessages": "101", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "102", "messages": "103", "suppressedMessages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "105", "messages": "106", "suppressedMessages": "107", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "108", "messages": "109", "suppressedMessages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "111", "messages": "112", "suppressedMessages": "113", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "114", "messages": "115", "suppressedMessages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "117", "messages": "118", "suppressedMessages": "119", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "120", "messages": "121", "suppressedMessages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "123", "messages": "124", "suppressedMessages": "125", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "126", "messages": "127", "suppressedMessages": "128", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "129", "messages": "130", "suppressedMessages": "131", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\index.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\App.tsx", ["162", "163"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\reportWebVitals.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\store.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\i18n\\i18n.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\authSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\animalSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\healthSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\breedingSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\store\\slices\\uiSlice.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\services\\api\\authAPI.ts", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\NotFoundPage.tsx", ["164"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\auth\\LoginPage.tsx", ["165"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\auth\\RegisterPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\health\\HealthPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AnimalDetailsPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\dashboard\\DashboardPage.tsx", ["166", "167"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\animals\\AnimalsPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\breeding\\BreedingPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\financial\\FinancialPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\feeding\\FeedingPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\reports\\ReportsPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\pages\\settings\\SettingsPage.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\layout\\Layout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\common\\LoadingSpinner.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\layout\\AuthLayout.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\auth\\ProtectedRoute.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\layout\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\layout\\Header.tsx", ["168"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\DashboardWidget.tsx", ["169"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\StatCard.tsx", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\src\\components\\dashboard\\ActivityFeed.tsx", ["170"], [], {"ruleId": "171", "severity": 1, "message": "172", "line": 1, "column": 1, "nodeType": "173", "messageId": "174", "fix": "175"}, {"ruleId": "176", "severity": 1, "message": "177", "line": 5, "column": 8, "nodeType": "178", "messageId": "179", "endLine": 5, "endColumn": 13}, {"ruleId": "176", "severity": 1, "message": "180", "line": 6, "column": 11, "nodeType": "178", "messageId": "179", "endLine": 6, "endColumn": 12}, {"ruleId": "181", "severity": 1, "message": "182", "line": 123, "column": 13, "nodeType": "183", "endLine": 126, "endColumn": 14}, {"ruleId": "176", "severity": 1, "message": "184", "line": 9, "column": 3, "nodeType": "178", "messageId": "179", "endLine": 9, "endColumn": 21}, {"ruleId": "176", "severity": 1, "message": "185", "line": 10, "column": 3, "nodeType": "178", "messageId": "179", "endLine": 10, "endColumn": 22}, {"ruleId": "176", "severity": 1, "message": "186", "line": 24, "column": 10, "nodeType": "178", "messageId": "179", "endLine": 24, "endColumn": 26}, {"ruleId": "176", "severity": 1, "message": "187", "line": 2, "column": 10, "nodeType": "178", "messageId": "179", "endLine": 2, "endColumn": 24}, {"ruleId": "176", "severity": 1, "message": "180", "line": 22, "column": 11, "nodeType": "178", "messageId": "179", "endLine": 22, "endColumn": 12}, "unicode-bom", "Unexpected Unicode BOM (Byte Order Mark).", "Program", "unexpected", {"range": "188", "text": "189"}, "@typescript-eslint/no-unused-vars", "'toast' is defined but never used.", "Identifier", "unusedVar", "'t' is assigned a value but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'CurrencyDollarIcon' is defined but never used.", "'ArrowTrendingUpIcon' is defined but never used.", "'showLanguageMenu' is assigned a value but never used.", "'useTranslation' is defined but never used.", [-1, 0], ""]