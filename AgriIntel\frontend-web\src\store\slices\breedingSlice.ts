import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface BreedingRecord {
  _id: string;
  female: string;
  male?: string;
  breedingDate: string;
  breedingMethod: 'natural' | 'artificial_insemination' | 'embryo_transfer';
  expectedDueDate?: string;
  actualBirthDate?: string;
  pregnancyConfirmed: boolean;
  pregnancyConfirmationDate?: string;
  pregnancyConfirmationMethod?: 'ultrasound' | 'blood_test' | 'physical_exam';
  complications?: string[];
  veterinarian?: string;
  semenSource?: {
    bullId?: string;
    semenBatch?: string;
    supplier?: string;
    cost?: number;
  };
  offspring?: string[];
  notes?: string;
  status: 'planned' | 'completed' | 'failed' | 'aborted';
  createdBy: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt: string;
}

export interface BirthRecord {
  _id: string;
  mother: string;
  father?: string;
  birthDate: string;
  birthTime?: string;
  gestationPeriod?: number;
  birthWeight?: number;
  birthType: 'natural' | 'assisted' | 'cesarean';
  complications?: string[];
  veterinarian?: string;
  offspring: Array<{
    tagNumber: string;
    gender: 'male' | 'female';
    weight?: number;
    health: 'healthy' | 'weak' | 'deceased';
    notes?: string;
  }>;
  placentaExpelled: boolean;
  placentaExpelledTime?: string;
  postBirthTreatment?: string[];
  notes?: string;
  createdBy: string;
  updatedBy?: string;
  createdAt: string;
  updatedAt: string;
}

export interface HeatRecord {
  _id: string;
  animal: string;
  heatDate: string;
  heatIntensity: 'weak' | 'moderate' | 'strong';
  heatDuration?: number;
  behaviorSigns: string[];
  physicalSigns: string[];
  breedingRecommended: boolean;
  breedingWindow?: {
    start: string;
    end: string;
  };
  bred: boolean;
  breedingDate?: string;
  notes?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface BreedingState {
  breedingRecords: BreedingRecord[];
  birthRecords: BirthRecord[];
  heatRecords: HeatRecord[];
  selectedBreedingRecord: BreedingRecord | null;
  selectedBirthRecord: BirthRecord | null;
  isLoading: boolean;
  error: string | null;
  statistics: {
    totalBreedings: number;
    successfulBreedings: number;
    pregnancyRate: number;
    birthRate: number;
    averageGestationPeriod: number;
    upcomingBirths: number;
    animalsInHeat: number;
  } | null;
}

const initialState: BreedingState = {
  breedingRecords: [],
  birthRecords: [],
  heatRecords: [],
  selectedBreedingRecord: null,
  selectedBirthRecord: null,
  isLoading: false,
  error: null,
  statistics: null,
};

const breedingSlice = createSlice({
  name: 'breeding',
  initialState,
  reducers: {
    setBreedingRecords: (state, action: PayloadAction<BreedingRecord[]>) => {
      state.breedingRecords = action.payload;
    },
    addBreedingRecord: (state, action: PayloadAction<BreedingRecord>) => {
      state.breedingRecords.unshift(action.payload);
    },
    updateBreedingRecord: (state, action: PayloadAction<BreedingRecord>) => {
      const index = state.breedingRecords.findIndex(record => record._id === action.payload._id);
      if (index !== -1) {
        state.breedingRecords[index] = action.payload;
      }
    },
    deleteBreedingRecord: (state, action: PayloadAction<string>) => {
      state.breedingRecords = state.breedingRecords.filter(record => record._id !== action.payload);
    },
    setBirthRecords: (state, action: PayloadAction<BirthRecord[]>) => {
      state.birthRecords = action.payload;
    },
    addBirthRecord: (state, action: PayloadAction<BirthRecord>) => {
      state.birthRecords.unshift(action.payload);
    },
    updateBirthRecord: (state, action: PayloadAction<BirthRecord>) => {
      const index = state.birthRecords.findIndex(record => record._id === action.payload._id);
      if (index !== -1) {
        state.birthRecords[index] = action.payload;
      }
    },
    deleteBirthRecord: (state, action: PayloadAction<string>) => {
      state.birthRecords = state.birthRecords.filter(record => record._id !== action.payload);
    },
    setHeatRecords: (state, action: PayloadAction<HeatRecord[]>) => {
      state.heatRecords = action.payload;
    },
    addHeatRecord: (state, action: PayloadAction<HeatRecord>) => {
      state.heatRecords.unshift(action.payload);
    },
    updateHeatRecord: (state, action: PayloadAction<HeatRecord>) => {
      const index = state.heatRecords.findIndex(record => record._id === action.payload._id);
      if (index !== -1) {
        state.heatRecords[index] = action.payload;
      }
    },
    deleteHeatRecord: (state, action: PayloadAction<string>) => {
      state.heatRecords = state.heatRecords.filter(record => record._id !== action.payload);
    },
    setSelectedBreedingRecord: (state, action: PayloadAction<BreedingRecord | null>) => {
      state.selectedBreedingRecord = action.payload;
    },
    setSelectedBirthRecord: (state, action: PayloadAction<BirthRecord | null>) => {
      state.selectedBirthRecord = action.payload;
    },
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.isLoading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    setStatistics: (state, action: PayloadAction<BreedingState['statistics']>) => {
      state.statistics = action.payload;
    },
  },
});

export const {
  setBreedingRecords,
  addBreedingRecord,
  updateBreedingRecord,
  deleteBreedingRecord,
  setBirthRecords,
  addBirthRecord,
  updateBirthRecord,
  deleteBirthRecord,
  setHeatRecords,
  addHeatRecord,
  updateHeatRecord,
  deleteHeatRecord,
  setSelectedBreedingRecord,
  setSelectedBirthRecord,
  setLoading,
  setError,
  clearError,
  setStatistics,
} = breedingSlice.actions;

export default breedingSlice.reducer;
