{"ast": null, "code": "import d, { createContext as c, useContext as m } from \"react\";\nimport { useEvent as p } from '../hooks/use-event.js';\nimport { useIsoMorphicEffect as f } from '../hooks/use-iso-morphic-effect.js';\nlet a = c(() => {});\na.displayName = \"StackContext\";\nvar s = (e => (e[e.Add = 0] = \"Add\", e[e.Remove = 1] = \"Remove\", e))(s || {});\nfunction x() {\n  return m(a);\n}\nfunction b(_ref) {\n  let {\n    children: i,\n    onUpdate: r,\n    type: e,\n    element: n,\n    enabled: u\n  } = _ref;\n  let l = x(),\n    o = p(function () {\n      r == null || r(...arguments), l(...arguments);\n    });\n  return f(() => {\n    let t = u === void 0 || u === !0;\n    return t && o(0, e, n), () => {\n      t && o(1, e, n);\n    };\n  }, [o, e, n, u]), d.createElement(a.Provider, {\n    value: o\n  }, i);\n}\nexport { s as StackMessage, b as StackProvider, x as useStackContext };", "map": {"version": 3, "names": ["d", "createContext", "c", "useContext", "m", "useEvent", "p", "useIsoMorphicEffect", "f", "a", "displayName", "s", "e", "Add", "Remove", "x", "b", "_ref", "children", "i", "onUpdate", "r", "type", "element", "n", "enabled", "u", "l", "o", "arguments", "t", "createElement", "Provider", "value", "StackMessage", "Stack<PERSON><PERSON><PERSON>", "useStackContext"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/internal/stack-context.js"], "sourcesContent": ["import d,{createContext as c,useContext as m}from\"react\";import{useEvent as p}from'../hooks/use-event.js';import{useIsoMorphicEffect as f}from'../hooks/use-iso-morphic-effect.js';let a=c(()=>{});a.displayName=\"StackContext\";var s=(e=>(e[e.Add=0]=\"Add\",e[e.Remove=1]=\"Remove\",e))(s||{});function x(){return m(a)}function b({children:i,onUpdate:r,type:e,element:n,enabled:u}){let l=x(),o=p((...t)=>{r==null||r(...t),l(...t)});return f(()=>{let t=u===void 0||u===!0;return t&&o(0,e,n),()=>{t&&o(1,e,n)}},[o,e,n,u]),d.createElement(a.Provider,{value:o},i)}export{s as StackMessage,b as StackProvider,x as useStackContext};\n"], "mappings": "AAAA,OAAOA,CAAC,IAAEC,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,oCAAoC;AAAC,IAAIC,CAAC,GAACP,CAAC,CAAC,MAAI,CAAC,CAAC,CAAC;AAACO,CAAC,CAACC,WAAW,GAAC,cAAc;AAAC,IAAIC,CAAC,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACA,CAAC,CAACC,GAAG,GAAC,CAAC,CAAC,GAAC,KAAK,EAACD,CAAC,CAACA,CAAC,CAACE,MAAM,GAAC,CAAC,CAAC,GAAC,QAAQ,EAACF,CAAC,CAAC,EAAED,CAAC,IAAE,CAAC,CAAC,CAAC;AAAC,SAASI,CAACA,CAAA,EAAE;EAAC,OAAOX,CAAC,CAACK,CAAC,CAAC;AAAA;AAAC,SAASO,CAACA,CAAAC,IAAA,EAAoD;EAAA,IAAnD;IAACC,QAAQ,EAACC,CAAC;IAACC,QAAQ,EAACC,CAAC;IAACC,IAAI,EAACV,CAAC;IAACW,OAAO,EAACC,CAAC;IAACC,OAAO,EAACC;EAAC,CAAC,GAAAT,IAAA;EAAE,IAAIU,CAAC,GAACZ,CAAC,CAAC,CAAC;IAACa,CAAC,GAACtB,CAAC,CAAC,YAAQ;MAACe,CAAC,IAAE,IAAI,IAAEA,CAAC,CAAC,GAAAQ,SAAI,CAAC,EAACF,CAAC,CAAC,GAAAE,SAAI,CAAC;IAAA,CAAC,CAAC;EAAC,OAAOrB,CAAC,CAAC,MAAI;IAAC,IAAIsB,CAAC,GAACJ,CAAC,KAAG,KAAK,CAAC,IAAEA,CAAC,KAAG,CAAC,CAAC;IAAC,OAAOI,CAAC,IAAEF,CAAC,CAAC,CAAC,EAAChB,CAAC,EAACY,CAAC,CAAC,EAAC,MAAI;MAACM,CAAC,IAAEF,CAAC,CAAC,CAAC,EAAChB,CAAC,EAACY,CAAC,CAAC;IAAA,CAAC;EAAA,CAAC,EAAC,CAACI,CAAC,EAAChB,CAAC,EAACY,CAAC,EAACE,CAAC,CAAC,CAAC,EAAC1B,CAAC,CAAC+B,aAAa,CAACtB,CAAC,CAACuB,QAAQ,EAAC;IAACC,KAAK,EAACL;EAAC,CAAC,EAACT,CAAC,CAAC;AAAA;AAAC,SAAOR,CAAC,IAAIuB,YAAY,EAAClB,CAAC,IAAImB,aAAa,EAACpB,CAAC,IAAIqB,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}