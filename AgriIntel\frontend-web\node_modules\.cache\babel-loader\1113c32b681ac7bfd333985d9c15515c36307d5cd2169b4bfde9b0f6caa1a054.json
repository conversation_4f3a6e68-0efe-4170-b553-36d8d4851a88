{"ast": null, "code": "// Simple replacement for MongoDB context hook\n// This provides a mock implementation to prevent build errors\nexport const useMongoDb=()=>{return{isConnected:true,isLoading:false,error:null,mongoStats:null,syncMockData:async()=>true,refreshStats:async()=>{},getStats:async()=>{},services:{breeding:{},health:{},animals:{}}};};", "map": {"version": 3, "names": ["useMongoDb", "isConnected", "isLoading", "error", "mongoStats", "syncMockData", "refreshStats", "getStats", "services", "breeding", "health", "animals"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/hooks/useMongoDb.ts"], "sourcesContent": ["// Simple replacement for MongoDB context hook\n// This provides a mock implementation to prevent build errors\n\nexport const useMongoDb = () => {\n  return {\n    isConnected: true,\n    isLoading: false,\n    error: null,\n    mongoStats: null,\n    syncMockData: async () => true,\n    refreshStats: async () => {},\n    getStats: async () => {},\n    services: {\n      breeding: {},\n      health: {},\n      animals: {}\n    }\n  };\n};\n"], "mappings": "AAAA;AACA;AAEA,MAAO,MAAM,CAAAA,UAAU,CAAGA,CAAA,GAAM,CAC9B,MAAO,CACLC,WAAW,CAAE,IAAI,CACjBC,SAAS,CAAE,KAAK,CAChBC,KAAK,CAAE,IAAI,CACXC,UAAU,CAAE,IAAI,CAChBC,YAAY,CAAE,KAAAA,CAAA,GAAY,IAAI,CAC9BC,YAAY,CAAE,KAAAA,CAAA,GAAY,CAAC,CAAC,CAC5BC,QAAQ,CAAE,KAAAA,CAAA,GAAY,CAAC,CAAC,CACxBC,QAAQ,CAAE,CACRC,QAAQ,CAAE,CAAC,CAAC,CACZC,MAAM,CAAE,CAAC,CAAC,CACVC,OAAO,CAAE,CAAC,CACZ,CACF,CAAC,CACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}