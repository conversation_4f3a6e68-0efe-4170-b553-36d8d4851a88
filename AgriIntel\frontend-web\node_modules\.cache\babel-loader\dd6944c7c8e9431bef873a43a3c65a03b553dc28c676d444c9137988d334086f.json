{"ast": null, "code": "import React from'react';import{Routes,Route,Navigate}from'react-router-dom';import{useSelector}from'react-redux';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";// Simple components for now\nconst LoginPage=()=>/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen flex items-center justify-center bg-gray-50\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"max-w-md w-full space-y-8\",children:[/*#__PURE__*/_jsx(\"h2\",{className:\"text-3xl font-bold text-center\",children:\"AMPD Livestock Management\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-center\",children:\"Login page coming soon...\"})]})});const DashboardPage=()=>/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-50 p-8\",children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-3xl font-bold mb-6\",children:\"Dashboard\"}),/*#__PURE__*/_jsx(\"p\",{children:\"Dashboard content coming soon...\"})]});function App(){const{isAuthenticated}=useSelector(state=>state.auth);return/*#__PURE__*/_jsx(\"div\",{className:\"App\",children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:!isAuthenticated?/*#__PURE__*/_jsx(LoginPage,{}):/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"/dashboard\",element:isAuthenticated?/*#__PURE__*/_jsx(DashboardPage,{}):/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true})}),/*#__PURE__*/_jsx(Route,{index:true,element:/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true})})]})});}export default App;", "map": {"version": 3, "names": ["React", "Routes", "Route", "Navigate", "useSelector", "jsx", "_jsx", "jsxs", "_jsxs", "LoginPage", "className", "children", "DashboardPage", "App", "isAuthenticated", "state", "auth", "path", "element", "to", "replace", "index"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/App.tsx"], "sourcesContent": ["import React from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { RootState } from './store/store';\n\n// Simple components for now\nconst LoginPage = () => (\n  <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n    <div className=\"max-w-md w-full space-y-8\">\n      <h2 className=\"text-3xl font-bold text-center\">AMPD Livestock Management</h2>\n      <p className=\"text-center\">Login page coming soon...</p>\n    </div>\n  </div>\n);\n\nconst DashboardPage = () => (\n  <div className=\"min-h-screen bg-gray-50 p-8\">\n    <h1 className=\"text-3xl font-bold mb-6\">Dashboard</h1>\n    <p>Dashboard content coming soon...</p>\n  </div>\n);\n\nfunction App() {\n  const { isAuthenticated } = useSelector((state: RootState) => state.auth);\n\n  return (\n    <div className=\"App\">\n      <Routes>\n        <Route\n          path=\"/login\"\n          element={!isAuthenticated ? <LoginPage /> : <Navigate to=\"/dashboard\" replace />}\n        />\n        <Route\n          path=\"/dashboard\"\n          element={isAuthenticated ? <DashboardPage /> : <Navigate to=\"/login\" replace />}\n        />\n        <Route index element={<Navigate to=\"/dashboard\" replace />} />\n        <Route path=\"*\" element={<Navigate to=\"/login\" replace />} />\n      </Routes>\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CAC1D,OAASC,WAAW,KAAQ,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAG1C;AACA,KAAM,CAAAC,SAAS,CAAGA,CAAA,gBAChBH,IAAA,QAAKI,SAAS,CAAC,0DAA0D,CAAAC,QAAA,cACvEH,KAAA,QAAKE,SAAS,CAAC,2BAA2B,CAAAC,QAAA,eACxCL,IAAA,OAAII,SAAS,CAAC,gCAAgC,CAAAC,QAAA,CAAC,2BAAyB,CAAI,CAAC,cAC7EL,IAAA,MAAGI,SAAS,CAAC,aAAa,CAAAC,QAAA,CAAC,2BAAyB,CAAG,CAAC,EACrD,CAAC,CACH,CACN,CAED,KAAM,CAAAC,aAAa,CAAGA,CAAA,gBACpBJ,KAAA,QAAKE,SAAS,CAAC,6BAA6B,CAAAC,QAAA,eAC1CL,IAAA,OAAII,SAAS,CAAC,yBAAyB,CAAAC,QAAA,CAAC,WAAS,CAAI,CAAC,cACtDL,IAAA,MAAAK,QAAA,CAAG,kCAAgC,CAAG,CAAC,EACpC,CACN,CAED,QAAS,CAAAE,GAAGA,CAAA,CAAG,CACb,KAAM,CAAEC,eAAgB,CAAC,CAAGV,WAAW,CAAEW,KAAgB,EAAKA,KAAK,CAACC,IAAI,CAAC,CAEzE,mBACEV,IAAA,QAAKI,SAAS,CAAC,KAAK,CAAAC,QAAA,cAClBH,KAAA,CAACP,MAAM,EAAAU,QAAA,eACLL,IAAA,CAACJ,KAAK,EACJe,IAAI,CAAC,QAAQ,CACbC,OAAO,CAAE,CAACJ,eAAe,cAAGR,IAAA,CAACG,SAAS,GAAE,CAAC,cAAGH,IAAA,CAACH,QAAQ,EAACgB,EAAE,CAAC,YAAY,CAACC,OAAO,MAAE,CAAE,CAClF,CAAC,cACFd,IAAA,CAACJ,KAAK,EACJe,IAAI,CAAC,YAAY,CACjBC,OAAO,CAAEJ,eAAe,cAAGR,IAAA,CAACM,aAAa,GAAE,CAAC,cAAGN,IAAA,CAACH,QAAQ,EAACgB,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAE,CACjF,CAAC,cACFd,IAAA,CAACJ,KAAK,EAACmB,KAAK,MAACH,OAAO,cAAEZ,IAAA,CAACH,QAAQ,EAACgB,EAAE,CAAC,YAAY,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cAC9Dd,IAAA,CAACJ,KAAK,EAACe,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEZ,IAAA,CAACH,QAAQ,EAACgB,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,EACvD,CAAC,CACN,CAAC,CAEV,CAEA,cAAe,CAAAP,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}