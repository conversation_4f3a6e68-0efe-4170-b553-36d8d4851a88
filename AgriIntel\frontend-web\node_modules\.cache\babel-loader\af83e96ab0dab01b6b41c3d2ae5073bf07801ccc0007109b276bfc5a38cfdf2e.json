{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.LEGAL_TCP_SOCKET_OPTIONS = exports.LEGAL_TLS_SOCKET_OPTIONS = void 0;\nexports.connect = connect;\nexports.makeConnection = makeConnection;\nexports.performInitialHandshake = performInitialHandshake;\nexports.prepareHandshakeDocument = prepareHandshakeDocument;\nexports.makeSocket = makeSocket;\nconst net = require(\"net\");\nconst tls = require(\"tls\");\nconst constants_1 = require(\"../constants\");\nconst deps_1 = require(\"../deps\");\nconst error_1 = require(\"../error\");\nconst utils_1 = require(\"../utils\");\nconst auth_provider_1 = require(\"./auth/auth_provider\");\nconst providers_1 = require(\"./auth/providers\");\nconst connection_1 = require(\"./connection\");\nconst constants_2 = require(\"./wire_protocol/constants\");\nasync function connect(options) {\n  let connection = null;\n  try {\n    const socket = await makeSocket(options);\n    connection = makeConnection(options, socket);\n    await performInitialHandshake(connection, options);\n    return connection;\n  } catch (error) {\n    connection?.destroy();\n    throw error;\n  }\n}\nfunction makeConnection(options, socket) {\n  let ConnectionType = options.connectionType ?? connection_1.Connection;\n  if (options.autoEncrypter) {\n    ConnectionType = connection_1.CryptoConnection;\n  }\n  return new ConnectionType(socket, options);\n}\nfunction checkSupportedServer(hello, options) {\n  const maxWireVersion = Number(hello.maxWireVersion);\n  const minWireVersion = Number(hello.minWireVersion);\n  const serverVersionHighEnough = !Number.isNaN(maxWireVersion) && maxWireVersion >= constants_2.MIN_SUPPORTED_WIRE_VERSION;\n  const serverVersionLowEnough = !Number.isNaN(minWireVersion) && minWireVersion <= constants_2.MAX_SUPPORTED_WIRE_VERSION;\n  if (serverVersionHighEnough) {\n    if (serverVersionLowEnough) {\n      return null;\n    }\n    const message = `Server at ${options.hostAddress} reports minimum wire version ${JSON.stringify(hello.minWireVersion)}, but this version of the Node.js Driver requires at most ${constants_2.MAX_SUPPORTED_WIRE_VERSION} (MongoDB ${constants_2.MAX_SUPPORTED_SERVER_VERSION})`;\n    return new error_1.MongoCompatibilityError(message);\n  }\n  const message = `Server at ${options.hostAddress} reports maximum wire version ${JSON.stringify(hello.maxWireVersion) ?? 0}, but this version of the Node.js Driver requires at least ${constants_2.MIN_SUPPORTED_WIRE_VERSION} (MongoDB ${constants_2.MIN_SUPPORTED_SERVER_VERSION})`;\n  return new error_1.MongoCompatibilityError(message);\n}\nasync function performInitialHandshake(conn, options) {\n  const credentials = options.credentials;\n  if (credentials) {\n    if (!(credentials.mechanism === providers_1.AuthMechanism.MONGODB_DEFAULT) && !options.authProviders.getOrCreateProvider(credentials.mechanism, credentials.mechanismProperties)) {\n      throw new error_1.MongoInvalidArgumentError(`AuthMechanism '${credentials.mechanism}' not supported`);\n    }\n  }\n  const authContext = new auth_provider_1.AuthContext(conn, credentials, options);\n  conn.authContext = authContext;\n  const handshakeDoc = await prepareHandshakeDocument(authContext);\n  // @ts-expect-error: TODO(NODE-5141): The options need to be filtered properly, Connection options differ from Command options\n  const handshakeOptions = {\n    ...options,\n    raw: false\n  };\n  if (typeof options.connectTimeoutMS === 'number') {\n    // The handshake technically is a monitoring check, so its socket timeout should be connectTimeoutMS\n    handshakeOptions.socketTimeoutMS = options.connectTimeoutMS;\n  }\n  const start = new Date().getTime();\n  const response = await executeHandshake(handshakeDoc, handshakeOptions);\n  if (!('isWritablePrimary' in response)) {\n    // Provide hello-style response document.\n    response.isWritablePrimary = response[constants_1.LEGACY_HELLO_COMMAND];\n  }\n  if (response.helloOk) {\n    conn.helloOk = true;\n  }\n  const supportedServerErr = checkSupportedServer(response, options);\n  if (supportedServerErr) {\n    throw supportedServerErr;\n  }\n  if (options.loadBalanced) {\n    if (!response.serviceId) {\n      throw new error_1.MongoCompatibilityError('Driver attempted to initialize in load balancing mode, ' + 'but the server does not support this mode.');\n    }\n  }\n  // NOTE: This is metadata attached to the connection while porting away from\n  //       handshake being done in the `Server` class. Likely, it should be\n  //       relocated, or at very least restructured.\n  conn.hello = response;\n  conn.lastHelloMS = new Date().getTime() - start;\n  if (!response.arbiterOnly && credentials) {\n    // store the response on auth context\n    authContext.response = response;\n    const resolvedCredentials = credentials.resolveAuthMechanism(response);\n    const provider = options.authProviders.getOrCreateProvider(resolvedCredentials.mechanism, resolvedCredentials.mechanismProperties);\n    if (!provider) {\n      throw new error_1.MongoInvalidArgumentError(`No AuthProvider for ${resolvedCredentials.mechanism} defined.`);\n    }\n    try {\n      await provider.auth(authContext);\n    } catch (error) {\n      if (error instanceof error_1.MongoError) {\n        error.addErrorLabel(error_1.MongoErrorLabel.HandshakeError);\n        if ((0, error_1.needsRetryableWriteLabel)(error, response.maxWireVersion, conn.description.type)) {\n          error.addErrorLabel(error_1.MongoErrorLabel.RetryableWriteError);\n        }\n      }\n      throw error;\n    }\n  }\n  // Connection establishment is socket creation (tcp handshake, tls handshake, MongoDB handshake (saslStart, saslContinue))\n  // Once connection is established, command logging can log events (if enabled)\n  conn.established = true;\n  async function executeHandshake(handshakeDoc, handshakeOptions) {\n    try {\n      const handshakeResponse = await conn.command((0, utils_1.ns)('admin.$cmd'), handshakeDoc, handshakeOptions);\n      return handshakeResponse;\n    } catch (error) {\n      if (error instanceof error_1.MongoError) {\n        error.addErrorLabel(error_1.MongoErrorLabel.HandshakeError);\n      }\n      throw error;\n    }\n  }\n}\n/**\n * @internal\n *\n * This function is only exposed for testing purposes.\n */\nasync function prepareHandshakeDocument(authContext) {\n  const options = authContext.options;\n  const compressors = options.compressors ? options.compressors : [];\n  const {\n    serverApi\n  } = authContext.connection;\n  const clientMetadata = await options.extendedMetadata;\n  const handshakeDoc = {\n    [serverApi?.version || options.loadBalanced === true ? 'hello' : constants_1.LEGACY_HELLO_COMMAND]: 1,\n    helloOk: true,\n    client: clientMetadata,\n    compression: compressors\n  };\n  if (options.loadBalanced === true) {\n    handshakeDoc.loadBalanced = true;\n  }\n  const credentials = authContext.credentials;\n  if (credentials) {\n    if (credentials.mechanism === providers_1.AuthMechanism.MONGODB_DEFAULT && credentials.username) {\n      handshakeDoc.saslSupportedMechs = `${credentials.source}.${credentials.username}`;\n      const provider = authContext.options.authProviders.getOrCreateProvider(providers_1.AuthMechanism.MONGODB_SCRAM_SHA256, credentials.mechanismProperties);\n      if (!provider) {\n        // This auth mechanism is always present.\n        throw new error_1.MongoInvalidArgumentError(`No AuthProvider for ${providers_1.AuthMechanism.MONGODB_SCRAM_SHA256} defined.`);\n      }\n      return await provider.prepare(handshakeDoc, authContext);\n    }\n    const provider = authContext.options.authProviders.getOrCreateProvider(credentials.mechanism, credentials.mechanismProperties);\n    if (!provider) {\n      throw new error_1.MongoInvalidArgumentError(`No AuthProvider for ${credentials.mechanism} defined.`);\n    }\n    return await provider.prepare(handshakeDoc, authContext);\n  }\n  return handshakeDoc;\n}\n/** @public */\nexports.LEGAL_TLS_SOCKET_OPTIONS = ['allowPartialTrustChain', 'ALPNProtocols', 'ca', 'cert', 'checkServerIdentity', 'ciphers', 'crl', 'ecdhCurve', 'key', 'minDHSize', 'passphrase', 'pfx', 'rejectUnauthorized', 'secureContext', 'secureProtocol', 'servername', 'session'];\n/** @public */\nexports.LEGAL_TCP_SOCKET_OPTIONS = ['autoSelectFamily', 'autoSelectFamilyAttemptTimeout', 'family', 'hints', 'localAddress', 'localPort', 'lookup'];\nfunction parseConnectOptions(options) {\n  const hostAddress = options.hostAddress;\n  if (!hostAddress) throw new error_1.MongoInvalidArgumentError('Option \"hostAddress\" is required');\n  const result = {};\n  for (const name of exports.LEGAL_TCP_SOCKET_OPTIONS) {\n    if (options[name] != null) {\n      result[name] = options[name];\n    }\n  }\n  if (typeof hostAddress.socketPath === 'string') {\n    result.path = hostAddress.socketPath;\n    return result;\n  } else if (typeof hostAddress.host === 'string') {\n    result.host = hostAddress.host;\n    result.port = hostAddress.port;\n    return result;\n  } else {\n    // This should never happen since we set up HostAddresses\n    // But if we don't throw here the socket could hang until timeout\n    // TODO(NODE-3483)\n    throw new error_1.MongoRuntimeError(`Unexpected HostAddress ${JSON.stringify(hostAddress)}`);\n  }\n}\nfunction parseSslOptions(options) {\n  const result = parseConnectOptions(options);\n  // Merge in valid SSL options\n  for (const name of exports.LEGAL_TLS_SOCKET_OPTIONS) {\n    if (options[name] != null) {\n      result[name] = options[name];\n    }\n  }\n  if (options.existingSocket) {\n    result.socket = options.existingSocket;\n  }\n  // Set default sni servername to be the same as host\n  if (result.servername == null && result.host && !net.isIP(result.host)) {\n    result.servername = result.host;\n  }\n  return result;\n}\nasync function makeSocket(options) {\n  const useTLS = options.tls ?? false;\n  const noDelay = options.noDelay ?? true;\n  const connectTimeoutMS = options.connectTimeoutMS ?? 30000;\n  const existingSocket = options.existingSocket;\n  let socket;\n  if (options.proxyHost != null) {\n    // Currently, only Socks5 is supported.\n    return await makeSocks5Connection({\n      ...options,\n      connectTimeoutMS // Should always be present for Socks5\n    });\n  }\n  if (useTLS) {\n    const tlsSocket = tls.connect(parseSslOptions(options));\n    if (typeof tlsSocket.disableRenegotiation === 'function') {\n      tlsSocket.disableRenegotiation();\n    }\n    socket = tlsSocket;\n  } else if (existingSocket) {\n    // In the TLS case, parseSslOptions() sets options.socket to existingSocket,\n    // so we only need to handle the non-TLS case here (where existingSocket\n    // gives us all we need out of the box).\n    socket = existingSocket;\n  } else {\n    socket = net.createConnection(parseConnectOptions(options));\n  }\n  socket.setKeepAlive(true, 300000);\n  socket.setTimeout(connectTimeoutMS);\n  socket.setNoDelay(noDelay);\n  let cancellationHandler = null;\n  const {\n    promise: connectedSocket,\n    resolve,\n    reject\n  } = (0, utils_1.promiseWithResolvers)();\n  if (existingSocket) {\n    resolve(socket);\n  } else {\n    const start = performance.now();\n    const connectEvent = useTLS ? 'secureConnect' : 'connect';\n    socket.once(connectEvent, () => resolve(socket)).once('error', cause => reject(new error_1.MongoNetworkError(error_1.MongoError.buildErrorMessage(cause), {\n      cause\n    }))).once('timeout', () => {\n      reject(new error_1.MongoNetworkTimeoutError(`Socket '${connectEvent}' timed out after ${performance.now() - start | 0}ms (connectTimeoutMS: ${connectTimeoutMS})`));\n    }).once('close', () => reject(new error_1.MongoNetworkError(`Socket closed after ${performance.now() - start | 0} during connection establishment`)));\n    if (options.cancellationToken != null) {\n      cancellationHandler = () => reject(new error_1.MongoNetworkError(`Socket connection establishment was cancelled after ${performance.now() - start | 0}`));\n      options.cancellationToken.once('cancel', cancellationHandler);\n    }\n  }\n  try {\n    socket = await connectedSocket;\n    return socket;\n  } catch (error) {\n    socket.destroy();\n    throw error;\n  } finally {\n    socket.setTimeout(0);\n    if (cancellationHandler != null) {\n      options.cancellationToken?.removeListener('cancel', cancellationHandler);\n    }\n  }\n}\nlet socks = null;\nfunction loadSocks() {\n  if (socks == null) {\n    const socksImport = (0, deps_1.getSocks)();\n    if ('kModuleError' in socksImport) {\n      throw socksImport.kModuleError;\n    }\n    socks = socksImport;\n  }\n  return socks;\n}\nasync function makeSocks5Connection(options) {\n  const hostAddress = utils_1.HostAddress.fromHostPort(options.proxyHost ?? '',\n  // proxyHost is guaranteed to set here\n  options.proxyPort ?? 1080);\n  // First, connect to the proxy server itself:\n  const rawSocket = await makeSocket({\n    ...options,\n    hostAddress,\n    tls: false,\n    proxyHost: undefined\n  });\n  const destination = parseConnectOptions(options);\n  if (typeof destination.host !== 'string' || typeof destination.port !== 'number') {\n    throw new error_1.MongoInvalidArgumentError('Can only make Socks5 connections to TCP hosts');\n  }\n  socks ??= loadSocks();\n  let existingSocket;\n  try {\n    // Then, establish the Socks5 proxy connection:\n    const connection = await socks.SocksClient.createConnection({\n      existing_socket: rawSocket,\n      timeout: options.connectTimeoutMS,\n      command: 'connect',\n      destination: {\n        host: destination.host,\n        port: destination.port\n      },\n      proxy: {\n        // host and port are ignored because we pass existing_socket\n        host: 'iLoveJavaScript',\n        port: 0,\n        type: 5,\n        userId: options.proxyUsername || undefined,\n        password: options.proxyPassword || undefined\n      }\n    });\n    existingSocket = connection.socket;\n  } catch (cause) {\n    throw new error_1.MongoNetworkError(error_1.MongoError.buildErrorMessage(cause), {\n      cause\n    });\n  }\n  // Finally, now treat the resulting duplex stream as the\n  // socket over which we send and receive wire protocol messages:\n  return await makeSocket({\n    ...options,\n    existingSocket,\n    proxyHost: undefined\n  });\n}", "map": {"version": 3, "names": ["exports", "connect", "makeConnection", "performInitialHandshake", "prepareHandshakeDocument", "makeSocket", "net", "require", "tls", "constants_1", "deps_1", "error_1", "utils_1", "auth_provider_1", "providers_1", "connection_1", "constants_2", "options", "connection", "socket", "error", "destroy", "ConnectionType", "connectionType", "Connection", "autoEncrypter", "CryptoConnection", "checkSupportedServer", "hello", "maxWireVersion", "Number", "minWireVersion", "serverVersionHighEnough", "isNaN", "MIN_SUPPORTED_WIRE_VERSION", "serverVersionLowEnough", "MAX_SUPPORTED_WIRE_VERSION", "message", "host<PERSON><PERSON><PERSON>", "JSON", "stringify", "MAX_SUPPORTED_SERVER_VERSION", "MongoCompatibilityError", "MIN_SUPPORTED_SERVER_VERSION", "conn", "credentials", "mechanism", "AuthMechanism", "MONGODB_DEFAULT", "authProviders", "getOrCreateProvider", "mechanismProperties", "MongoInvalidArgumentError", "authContext", "AuthContext", "handshakeDoc", "handshakeOptions", "raw", "connectTimeoutMS", "socketTimeoutMS", "start", "Date", "getTime", "response", "executeHandshake", "isWritablePrimary", "LEGACY_HELLO_COMMAND", "helloOk", "supportedServerErr", "loadBalanced", "serviceId", "lastHelloMS", "arbiterOnly", "resolvedCredentials", "resolveAuthMechanism", "provider", "auth", "MongoError", "addErrorLabel", "MongoErrorLabel", "Handshake<PERSON><PERSON><PERSON>", "needsRetryableWriteLabel", "description", "type", "RetryableWriteError", "established", "handshakeResponse", "command", "ns", "compressors", "serverApi", "clientMetadata", "extendedMetadata", "version", "client", "compression", "username", "saslSupportedMechs", "source", "MONGODB_SCRAM_SHA256", "prepare", "LEGAL_TLS_SOCKET_OPTIONS", "LEGAL_TCP_SOCKET_OPTIONS", "parseConnectOptions", "result", "name", "socketPath", "path", "host", "port", "MongoRuntimeError", "parseSslOptions", "existingSocket", "servername", "isIP", "useTLS", "no<PERSON>elay", "proxyHost", "makeSocks5Connection", "tlsSocket", "disableRenegotiation", "createConnection", "setKeepAlive", "setTimeout", "set<PERSON><PERSON><PERSON>elay", "<PERSON><PERSON><PERSON><PERSON>", "promise", "connectedSocket", "resolve", "reject", "promiseWithResolvers", "performance", "now", "connectEvent", "once", "cause", "MongoNetworkError", "buildErrorMessage", "MongoNetworkTimeoutError", "cancellationToken", "removeListener", "socks", "loadSocks", "socksImport", "getSocks", "kModuleError", "HostAddress", "fromHostPort", "proxyPort", "rawSocket", "undefined", "destination", "SocksClient", "existing_socket", "timeout", "proxy", "userId", "proxyUsername", "password", "proxyPassword"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\connect.ts"], "sourcesContent": ["import type { Socket, SocketConnectOpts } from 'net';\nimport * as net from 'net';\nimport type { ConnectionOptions as TLSConnectionOpts, TLSSocket } from 'tls';\nimport * as tls from 'tls';\n\nimport type { Document } from '../bson';\nimport { LEGACY_HELLO_COMMAND } from '../constants';\nimport { getSocks, type SocksLib } from '../deps';\nimport {\n  MongoCompatibilityError,\n  MongoError,\n  MongoErrorLabel,\n  MongoInvalidArgumentError,\n  MongoNetworkError,\n  MongoNetworkTimeoutError,\n  MongoRuntimeError,\n  needsRetryableWriteLabel\n} from '../error';\nimport { HostAddress, ns, promiseWithResolvers } from '../utils';\nimport { AuthContext } from './auth/auth_provider';\nimport { AuthMechanism } from './auth/providers';\nimport {\n  type CommandOptions,\n  Connection,\n  type ConnectionOptions,\n  CryptoConnection\n} from './connection';\nimport {\n  MAX_SUPPORTED_SERVER_VERSION,\n  MAX_SUPPORTED_WIRE_VERSION,\n  MIN_SUPPORTED_SERVER_VERSION,\n  MIN_SUPPORTED_WIRE_VERSION\n} from './wire_protocol/constants';\n\n/** @public */\nexport type Stream = Socket | TLSSocket;\n\nexport async function connect(options: ConnectionOptions): Promise<Connection> {\n  let connection: Connection | null = null;\n  try {\n    const socket = await makeSocket(options);\n    connection = makeConnection(options, socket);\n    await performInitialHandshake(connection, options);\n    return connection;\n  } catch (error) {\n    connection?.destroy();\n    throw error;\n  }\n}\n\nexport function makeConnection(options: ConnectionOptions, socket: Stream): Connection {\n  let ConnectionType = options.connectionType ?? Connection;\n  if (options.autoEncrypter) {\n    ConnectionType = CryptoConnection;\n  }\n\n  return new ConnectionType(socket, options);\n}\n\nfunction checkSupportedServer(hello: Document, options: ConnectionOptions) {\n  const maxWireVersion = Number(hello.maxWireVersion);\n  const minWireVersion = Number(hello.minWireVersion);\n  const serverVersionHighEnough =\n    !Number.isNaN(maxWireVersion) && maxWireVersion >= MIN_SUPPORTED_WIRE_VERSION;\n  const serverVersionLowEnough =\n    !Number.isNaN(minWireVersion) && minWireVersion <= MAX_SUPPORTED_WIRE_VERSION;\n\n  if (serverVersionHighEnough) {\n    if (serverVersionLowEnough) {\n      return null;\n    }\n\n    const message = `Server at ${options.hostAddress} reports minimum wire version ${JSON.stringify(\n      hello.minWireVersion\n    )}, but this version of the Node.js Driver requires at most ${MAX_SUPPORTED_WIRE_VERSION} (MongoDB ${MAX_SUPPORTED_SERVER_VERSION})`;\n    return new MongoCompatibilityError(message);\n  }\n\n  const message = `Server at ${options.hostAddress} reports maximum wire version ${\n    JSON.stringify(hello.maxWireVersion) ?? 0\n  }, but this version of the Node.js Driver requires at least ${MIN_SUPPORTED_WIRE_VERSION} (MongoDB ${MIN_SUPPORTED_SERVER_VERSION})`;\n  return new MongoCompatibilityError(message);\n}\n\nexport async function performInitialHandshake(\n  conn: Connection,\n  options: ConnectionOptions\n): Promise<void> {\n  const credentials = options.credentials;\n\n  if (credentials) {\n    if (\n      !(credentials.mechanism === AuthMechanism.MONGODB_DEFAULT) &&\n      !options.authProviders.getOrCreateProvider(\n        credentials.mechanism,\n        credentials.mechanismProperties\n      )\n    ) {\n      throw new MongoInvalidArgumentError(`AuthMechanism '${credentials.mechanism}' not supported`);\n    }\n  }\n\n  const authContext = new AuthContext(conn, credentials, options);\n  conn.authContext = authContext;\n\n  const handshakeDoc = await prepareHandshakeDocument(authContext);\n\n  // @ts-expect-error: TODO(NODE-5141): The options need to be filtered properly, Connection options differ from Command options\n  const handshakeOptions: CommandOptions = { ...options, raw: false };\n  if (typeof options.connectTimeoutMS === 'number') {\n    // The handshake technically is a monitoring check, so its socket timeout should be connectTimeoutMS\n    handshakeOptions.socketTimeoutMS = options.connectTimeoutMS;\n  }\n\n  const start = new Date().getTime();\n\n  const response = await executeHandshake(handshakeDoc, handshakeOptions);\n\n  if (!('isWritablePrimary' in response)) {\n    // Provide hello-style response document.\n    response.isWritablePrimary = response[LEGACY_HELLO_COMMAND];\n  }\n\n  if (response.helloOk) {\n    conn.helloOk = true;\n  }\n\n  const supportedServerErr = checkSupportedServer(response, options);\n  if (supportedServerErr) {\n    throw supportedServerErr;\n  }\n\n  if (options.loadBalanced) {\n    if (!response.serviceId) {\n      throw new MongoCompatibilityError(\n        'Driver attempted to initialize in load balancing mode, ' +\n          'but the server does not support this mode.'\n      );\n    }\n  }\n\n  // NOTE: This is metadata attached to the connection while porting away from\n  //       handshake being done in the `Server` class. Likely, it should be\n  //       relocated, or at very least restructured.\n  conn.hello = response;\n  conn.lastHelloMS = new Date().getTime() - start;\n\n  if (!response.arbiterOnly && credentials) {\n    // store the response on auth context\n    authContext.response = response;\n\n    const resolvedCredentials = credentials.resolveAuthMechanism(response);\n    const provider = options.authProviders.getOrCreateProvider(\n      resolvedCredentials.mechanism,\n      resolvedCredentials.mechanismProperties\n    );\n    if (!provider) {\n      throw new MongoInvalidArgumentError(\n        `No AuthProvider for ${resolvedCredentials.mechanism} defined.`\n      );\n    }\n\n    try {\n      await provider.auth(authContext);\n    } catch (error) {\n      if (error instanceof MongoError) {\n        error.addErrorLabel(MongoErrorLabel.HandshakeError);\n        if (needsRetryableWriteLabel(error, response.maxWireVersion, conn.description.type)) {\n          error.addErrorLabel(MongoErrorLabel.RetryableWriteError);\n        }\n      }\n      throw error;\n    }\n  }\n\n  // Connection establishment is socket creation (tcp handshake, tls handshake, MongoDB handshake (saslStart, saslContinue))\n  // Once connection is established, command logging can log events (if enabled)\n  conn.established = true;\n\n  async function executeHandshake(handshakeDoc: Document, handshakeOptions: CommandOptions) {\n    try {\n      const handshakeResponse = await conn.command(\n        ns('admin.$cmd'),\n        handshakeDoc,\n        handshakeOptions\n      );\n      return handshakeResponse;\n    } catch (error) {\n      if (error instanceof MongoError) {\n        error.addErrorLabel(MongoErrorLabel.HandshakeError);\n      }\n      throw error;\n    }\n  }\n}\n\n/**\n * HandshakeDocument used during authentication.\n * @internal\n */\nexport interface HandshakeDocument extends Document {\n  /**\n   * @deprecated Use hello instead\n   */\n  ismaster?: boolean;\n  hello?: boolean;\n  helloOk?: boolean;\n  client: Document;\n  compression: string[];\n  saslSupportedMechs?: string;\n  loadBalanced?: boolean;\n}\n\n/**\n * @internal\n *\n * This function is only exposed for testing purposes.\n */\nexport async function prepareHandshakeDocument(\n  authContext: AuthContext\n): Promise<HandshakeDocument> {\n  const options = authContext.options;\n  const compressors = options.compressors ? options.compressors : [];\n  const { serverApi } = authContext.connection;\n  const clientMetadata: Document = await options.extendedMetadata;\n\n  const handshakeDoc: HandshakeDocument = {\n    [serverApi?.version || options.loadBalanced === true ? 'hello' : LEGACY_HELLO_COMMAND]: 1,\n    helloOk: true,\n    client: clientMetadata,\n    compression: compressors\n  };\n\n  if (options.loadBalanced === true) {\n    handshakeDoc.loadBalanced = true;\n  }\n\n  const credentials = authContext.credentials;\n  if (credentials) {\n    if (credentials.mechanism === AuthMechanism.MONGODB_DEFAULT && credentials.username) {\n      handshakeDoc.saslSupportedMechs = `${credentials.source}.${credentials.username}`;\n\n      const provider = authContext.options.authProviders.getOrCreateProvider(\n        AuthMechanism.MONGODB_SCRAM_SHA256,\n        credentials.mechanismProperties\n      );\n      if (!provider) {\n        // This auth mechanism is always present.\n        throw new MongoInvalidArgumentError(\n          `No AuthProvider for ${AuthMechanism.MONGODB_SCRAM_SHA256} defined.`\n        );\n      }\n      return await provider.prepare(handshakeDoc, authContext);\n    }\n    const provider = authContext.options.authProviders.getOrCreateProvider(\n      credentials.mechanism,\n      credentials.mechanismProperties\n    );\n    if (!provider) {\n      throw new MongoInvalidArgumentError(`No AuthProvider for ${credentials.mechanism} defined.`);\n    }\n    return await provider.prepare(handshakeDoc, authContext);\n  }\n  return handshakeDoc;\n}\n\n/** @public */\nexport const LEGAL_TLS_SOCKET_OPTIONS = [\n  'allowPartialTrustChain',\n  'ALPNProtocols',\n  'ca',\n  'cert',\n  'checkServerIdentity',\n  'ciphers',\n  'crl',\n  'ecdhCurve',\n  'key',\n  'minDHSize',\n  'passphrase',\n  'pfx',\n  'rejectUnauthorized',\n  'secureContext',\n  'secureProtocol',\n  'servername',\n  'session'\n] as const;\n\n/** @public */\nexport const LEGAL_TCP_SOCKET_OPTIONS = [\n  'autoSelectFamily',\n  'autoSelectFamilyAttemptTimeout',\n  'family',\n  'hints',\n  'localAddress',\n  'localPort',\n  'lookup'\n] as const;\n\nfunction parseConnectOptions(options: ConnectionOptions): SocketConnectOpts {\n  const hostAddress = options.hostAddress;\n  if (!hostAddress) throw new MongoInvalidArgumentError('Option \"hostAddress\" is required');\n\n  const result: Partial<net.TcpNetConnectOpts & net.IpcNetConnectOpts> = {};\n  for (const name of LEGAL_TCP_SOCKET_OPTIONS) {\n    if (options[name] != null) {\n      (result as Document)[name] = options[name];\n    }\n  }\n\n  if (typeof hostAddress.socketPath === 'string') {\n    result.path = hostAddress.socketPath;\n    return result as net.IpcNetConnectOpts;\n  } else if (typeof hostAddress.host === 'string') {\n    result.host = hostAddress.host;\n    result.port = hostAddress.port;\n    return result as net.TcpNetConnectOpts;\n  } else {\n    // This should never happen since we set up HostAddresses\n    // But if we don't throw here the socket could hang until timeout\n    // TODO(NODE-3483)\n    throw new MongoRuntimeError(`Unexpected HostAddress ${JSON.stringify(hostAddress)}`);\n  }\n}\n\ntype MakeConnectionOptions = ConnectionOptions & { existingSocket?: Stream };\n\nfunction parseSslOptions(options: MakeConnectionOptions): TLSConnectionOpts {\n  const result: TLSConnectionOpts = parseConnectOptions(options);\n  // Merge in valid SSL options\n  for (const name of LEGAL_TLS_SOCKET_OPTIONS) {\n    if (options[name] != null) {\n      (result as Document)[name] = options[name];\n    }\n  }\n\n  if (options.existingSocket) {\n    result.socket = options.existingSocket;\n  }\n\n  // Set default sni servername to be the same as host\n  if (result.servername == null && result.host && !net.isIP(result.host)) {\n    result.servername = result.host;\n  }\n\n  return result;\n}\n\nexport async function makeSocket(options: MakeConnectionOptions): Promise<Stream> {\n  const useTLS = options.tls ?? false;\n  const noDelay = options.noDelay ?? true;\n  const connectTimeoutMS = options.connectTimeoutMS ?? 30000;\n  const existingSocket = options.existingSocket;\n\n  let socket: Stream;\n\n  if (options.proxyHost != null) {\n    // Currently, only Socks5 is supported.\n    return await makeSocks5Connection({\n      ...options,\n      connectTimeoutMS // Should always be present for Socks5\n    });\n  }\n\n  if (useTLS) {\n    const tlsSocket = tls.connect(parseSslOptions(options));\n    if (typeof tlsSocket.disableRenegotiation === 'function') {\n      tlsSocket.disableRenegotiation();\n    }\n    socket = tlsSocket;\n  } else if (existingSocket) {\n    // In the TLS case, parseSslOptions() sets options.socket to existingSocket,\n    // so we only need to handle the non-TLS case here (where existingSocket\n    // gives us all we need out of the box).\n    socket = existingSocket;\n  } else {\n    socket = net.createConnection(parseConnectOptions(options));\n  }\n\n  socket.setKeepAlive(true, 300000);\n  socket.setTimeout(connectTimeoutMS);\n  socket.setNoDelay(noDelay);\n\n  let cancellationHandler: ((err: Error) => void) | null = null;\n\n  const { promise: connectedSocket, resolve, reject } = promiseWithResolvers<Stream>();\n  if (existingSocket) {\n    resolve(socket);\n  } else {\n    const start = performance.now();\n    const connectEvent = useTLS ? 'secureConnect' : 'connect';\n    socket\n      .once(connectEvent, () => resolve(socket))\n      .once('error', cause =>\n        reject(new MongoNetworkError(MongoError.buildErrorMessage(cause), { cause }))\n      )\n      .once('timeout', () => {\n        reject(\n          new MongoNetworkTimeoutError(\n            `Socket '${connectEvent}' timed out after ${(performance.now() - start) | 0}ms (connectTimeoutMS: ${connectTimeoutMS})`\n          )\n        );\n      })\n      .once('close', () =>\n        reject(\n          new MongoNetworkError(\n            `Socket closed after ${(performance.now() - start) | 0} during connection establishment`\n          )\n        )\n      );\n\n    if (options.cancellationToken != null) {\n      cancellationHandler = () =>\n        reject(\n          new MongoNetworkError(\n            `Socket connection establishment was cancelled after ${(performance.now() - start) | 0}`\n          )\n        );\n      options.cancellationToken.once('cancel', cancellationHandler);\n    }\n  }\n\n  try {\n    socket = await connectedSocket;\n    return socket;\n  } catch (error) {\n    socket.destroy();\n    throw error;\n  } finally {\n    socket.setTimeout(0);\n    if (cancellationHandler != null) {\n      options.cancellationToken?.removeListener('cancel', cancellationHandler);\n    }\n  }\n}\n\nlet socks: SocksLib | null = null;\nfunction loadSocks() {\n  if (socks == null) {\n    const socksImport = getSocks();\n    if ('kModuleError' in socksImport) {\n      throw socksImport.kModuleError;\n    }\n    socks = socksImport;\n  }\n  return socks;\n}\n\nasync function makeSocks5Connection(options: MakeConnectionOptions): Promise<Stream> {\n  const hostAddress = HostAddress.fromHostPort(\n    options.proxyHost ?? '', // proxyHost is guaranteed to set here\n    options.proxyPort ?? 1080\n  );\n\n  // First, connect to the proxy server itself:\n  const rawSocket = await makeSocket({\n    ...options,\n    hostAddress,\n    tls: false,\n    proxyHost: undefined\n  });\n\n  const destination = parseConnectOptions(options) as net.TcpNetConnectOpts;\n  if (typeof destination.host !== 'string' || typeof destination.port !== 'number') {\n    throw new MongoInvalidArgumentError('Can only make Socks5 connections to TCP hosts');\n  }\n\n  socks ??= loadSocks();\n\n  let existingSocket: Stream;\n\n  try {\n    // Then, establish the Socks5 proxy connection:\n    const connection = await socks.SocksClient.createConnection({\n      existing_socket: rawSocket,\n      timeout: options.connectTimeoutMS,\n      command: 'connect',\n      destination: {\n        host: destination.host,\n        port: destination.port\n      },\n      proxy: {\n        // host and port are ignored because we pass existing_socket\n        host: 'iLoveJavaScript',\n        port: 0,\n        type: 5,\n        userId: options.proxyUsername || undefined,\n        password: options.proxyPassword || undefined\n      }\n    });\n    existingSocket = connection.socket;\n  } catch (cause) {\n    throw new MongoNetworkError(MongoError.buildErrorMessage(cause), { cause });\n  }\n\n  // Finally, now treat the resulting duplex stream as the\n  // socket over which we send and receive wire protocol messages:\n  return await makeSocket({ ...options, existingSocket, proxyHost: undefined });\n}\n"], "mappings": ";;;;;;AAqCAA,OAAA,CAAAC,OAAA,GAAAA,OAAA;AAaAD,OAAA,CAAAE,cAAA,GAAAA,cAAA;AAkCAF,OAAA,CAAAG,uBAAA,GAAAA,uBAAA;AAsIAH,OAAA,CAAAI,wBAAA,GAAAA,wBAAA;AAiIAJ,OAAA,CAAAK,UAAA,GAAAA,UAAA;AA1VA,MAAAC,GAAA,GAAAC,OAAA;AAEA,MAAAC,GAAA,GAAAD,OAAA;AAGA,MAAAE,WAAA,GAAAF,OAAA;AACA,MAAAG,MAAA,GAAAH,OAAA;AACA,MAAAI,OAAA,GAAAJ,OAAA;AAUA,MAAAK,OAAA,GAAAL,OAAA;AACA,MAAAM,eAAA,GAAAN,OAAA;AACA,MAAAO,WAAA,GAAAP,OAAA;AACA,MAAAQ,YAAA,GAAAR,OAAA;AAMA,MAAAS,WAAA,GAAAT,OAAA;AAUO,eAAeN,OAAOA,CAACgB,OAA0B;EACtD,IAAIC,UAAU,GAAsB,IAAI;EACxC,IAAI;IACF,MAAMC,MAAM,GAAG,MAAMd,UAAU,CAACY,OAAO,CAAC;IACxCC,UAAU,GAAGhB,cAAc,CAACe,OAAO,EAAEE,MAAM,CAAC;IAC5C,MAAMhB,uBAAuB,CAACe,UAAU,EAAED,OAAO,CAAC;IAClD,OAAOC,UAAU;EACnB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdF,UAAU,EAAEG,OAAO,EAAE;IACrB,MAAMD,KAAK;EACb;AACF;AAEA,SAAgBlB,cAAcA,CAACe,OAA0B,EAAEE,MAAc;EACvE,IAAIG,cAAc,GAAGL,OAAO,CAACM,cAAc,IAAIR,YAAA,CAAAS,UAAU;EACzD,IAAIP,OAAO,CAACQ,aAAa,EAAE;IACzBH,cAAc,GAAGP,YAAA,CAAAW,gBAAgB;EACnC;EAEA,OAAO,IAAIJ,cAAc,CAACH,MAAM,EAAEF,OAAO,CAAC;AAC5C;AAEA,SAASU,oBAAoBA,CAACC,KAAe,EAAEX,OAA0B;EACvE,MAAMY,cAAc,GAAGC,MAAM,CAACF,KAAK,CAACC,cAAc,CAAC;EACnD,MAAME,cAAc,GAAGD,MAAM,CAACF,KAAK,CAACG,cAAc,CAAC;EACnD,MAAMC,uBAAuB,GAC3B,CAACF,MAAM,CAACG,KAAK,CAACJ,cAAc,CAAC,IAAIA,cAAc,IAAIb,WAAA,CAAAkB,0BAA0B;EAC/E,MAAMC,sBAAsB,GAC1B,CAACL,MAAM,CAACG,KAAK,CAACF,cAAc,CAAC,IAAIA,cAAc,IAAIf,WAAA,CAAAoB,0BAA0B;EAE/E,IAAIJ,uBAAuB,EAAE;IAC3B,IAAIG,sBAAsB,EAAE;MAC1B,OAAO,IAAI;IACb;IAEA,MAAME,OAAO,GAAG,aAAapB,OAAO,CAACqB,WAAW,iCAAiCC,IAAI,CAACC,SAAS,CAC7FZ,KAAK,CAACG,cAAc,CACrB,6DAA6Df,WAAA,CAAAoB,0BAA0B,aAAapB,WAAA,CAAAyB,4BAA4B,GAAG;IACpI,OAAO,IAAI9B,OAAA,CAAA+B,uBAAuB,CAACL,OAAO,CAAC;EAC7C;EAEA,MAAMA,OAAO,GAAG,aAAapB,OAAO,CAACqB,WAAW,iCAC9CC,IAAI,CAACC,SAAS,CAACZ,KAAK,CAACC,cAAc,CAAC,IAAI,CAC1C,8DAA8Db,WAAA,CAAAkB,0BAA0B,aAAalB,WAAA,CAAA2B,4BAA4B,GAAG;EACpI,OAAO,IAAIhC,OAAA,CAAA+B,uBAAuB,CAACL,OAAO,CAAC;AAC7C;AAEO,eAAelC,uBAAuBA,CAC3CyC,IAAgB,EAChB3B,OAA0B;EAE1B,MAAM4B,WAAW,GAAG5B,OAAO,CAAC4B,WAAW;EAEvC,IAAIA,WAAW,EAAE;IACf,IACE,EAAEA,WAAW,CAACC,SAAS,KAAKhC,WAAA,CAAAiC,aAAa,CAACC,eAAe,CAAC,IAC1D,CAAC/B,OAAO,CAACgC,aAAa,CAACC,mBAAmB,CACxCL,WAAW,CAACC,SAAS,EACrBD,WAAW,CAACM,mBAAmB,CAChC,EACD;MACA,MAAM,IAAIxC,OAAA,CAAAyC,yBAAyB,CAAC,kBAAkBP,WAAW,CAACC,SAAS,iBAAiB,CAAC;IAC/F;EACF;EAEA,MAAMO,WAAW,GAAG,IAAIxC,eAAA,CAAAyC,WAAW,CAACV,IAAI,EAAEC,WAAW,EAAE5B,OAAO,CAAC;EAC/D2B,IAAI,CAACS,WAAW,GAAGA,WAAW;EAE9B,MAAME,YAAY,GAAG,MAAMnD,wBAAwB,CAACiD,WAAW,CAAC;EAEhE;EACA,MAAMG,gBAAgB,GAAmB;IAAE,GAAGvC,OAAO;IAAEwC,GAAG,EAAE;EAAK,CAAE;EACnE,IAAI,OAAOxC,OAAO,CAACyC,gBAAgB,KAAK,QAAQ,EAAE;IAChD;IACAF,gBAAgB,CAACG,eAAe,GAAG1C,OAAO,CAACyC,gBAAgB;EAC7D;EAEA,MAAME,KAAK,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;EAElC,MAAMC,QAAQ,GAAG,MAAMC,gBAAgB,CAACT,YAAY,EAAEC,gBAAgB,CAAC;EAEvE,IAAI,EAAE,mBAAmB,IAAIO,QAAQ,CAAC,EAAE;IACtC;IACAA,QAAQ,CAACE,iBAAiB,GAAGF,QAAQ,CAACtD,WAAA,CAAAyD,oBAAoB,CAAC;EAC7D;EAEA,IAAIH,QAAQ,CAACI,OAAO,EAAE;IACpBvB,IAAI,CAACuB,OAAO,GAAG,IAAI;EACrB;EAEA,MAAMC,kBAAkB,GAAGzC,oBAAoB,CAACoC,QAAQ,EAAE9C,OAAO,CAAC;EAClE,IAAImD,kBAAkB,EAAE;IACtB,MAAMA,kBAAkB;EAC1B;EAEA,IAAInD,OAAO,CAACoD,YAAY,EAAE;IACxB,IAAI,CAACN,QAAQ,CAACO,SAAS,EAAE;MACvB,MAAM,IAAI3D,OAAA,CAAA+B,uBAAuB,CAC/B,yDAAyD,GACvD,4CAA4C,CAC/C;IACH;EACF;EAEA;EACA;EACA;EACAE,IAAI,CAAChB,KAAK,GAAGmC,QAAQ;EACrBnB,IAAI,CAAC2B,WAAW,GAAG,IAAIV,IAAI,EAAE,CAACC,OAAO,EAAE,GAAGF,KAAK;EAE/C,IAAI,CAACG,QAAQ,CAACS,WAAW,IAAI3B,WAAW,EAAE;IACxC;IACAQ,WAAW,CAACU,QAAQ,GAAGA,QAAQ;IAE/B,MAAMU,mBAAmB,GAAG5B,WAAW,CAAC6B,oBAAoB,CAACX,QAAQ,CAAC;IACtE,MAAMY,QAAQ,GAAG1D,OAAO,CAACgC,aAAa,CAACC,mBAAmB,CACxDuB,mBAAmB,CAAC3B,SAAS,EAC7B2B,mBAAmB,CAACtB,mBAAmB,CACxC;IACD,IAAI,CAACwB,QAAQ,EAAE;MACb,MAAM,IAAIhE,OAAA,CAAAyC,yBAAyB,CACjC,uBAAuBqB,mBAAmB,CAAC3B,SAAS,WAAW,CAChE;IACH;IAEA,IAAI;MACF,MAAM6B,QAAQ,CAACC,IAAI,CAACvB,WAAW,CAAC;IAClC,CAAC,CAAC,OAAOjC,KAAK,EAAE;MACd,IAAIA,KAAK,YAAYT,OAAA,CAAAkE,UAAU,EAAE;QAC/BzD,KAAK,CAAC0D,aAAa,CAACnE,OAAA,CAAAoE,eAAe,CAACC,cAAc,CAAC;QACnD,IAAI,IAAArE,OAAA,CAAAsE,wBAAwB,EAAC7D,KAAK,EAAE2C,QAAQ,CAAClC,cAAc,EAAEe,IAAI,CAACsC,WAAW,CAACC,IAAI,CAAC,EAAE;UACnF/D,KAAK,CAAC0D,aAAa,CAACnE,OAAA,CAAAoE,eAAe,CAACK,mBAAmB,CAAC;QAC1D;MACF;MACA,MAAMhE,KAAK;IACb;EACF;EAEA;EACA;EACAwB,IAAI,CAACyC,WAAW,GAAG,IAAI;EAEvB,eAAerB,gBAAgBA,CAACT,YAAsB,EAAEC,gBAAgC;IACtF,IAAI;MACF,MAAM8B,iBAAiB,GAAG,MAAM1C,IAAI,CAAC2C,OAAO,CAC1C,IAAA3E,OAAA,CAAA4E,EAAE,EAAC,YAAY,CAAC,EAChBjC,YAAY,EACZC,gBAAgB,CACjB;MACD,OAAO8B,iBAAiB;IAC1B,CAAC,CAAC,OAAOlE,KAAK,EAAE;MACd,IAAIA,KAAK,YAAYT,OAAA,CAAAkE,UAAU,EAAE;QAC/BzD,KAAK,CAAC0D,aAAa,CAACnE,OAAA,CAAAoE,eAAe,CAACC,cAAc,CAAC;MACrD;MACA,MAAM5D,KAAK;IACb;EACF;AACF;AAmBA;;;;;AAKO,eAAehB,wBAAwBA,CAC5CiD,WAAwB;EAExB,MAAMpC,OAAO,GAAGoC,WAAW,CAACpC,OAAO;EACnC,MAAMwE,WAAW,GAAGxE,OAAO,CAACwE,WAAW,GAAGxE,OAAO,CAACwE,WAAW,GAAG,EAAE;EAClE,MAAM;IAAEC;EAAS,CAAE,GAAGrC,WAAW,CAACnC,UAAU;EAC5C,MAAMyE,cAAc,GAAa,MAAM1E,OAAO,CAAC2E,gBAAgB;EAE/D,MAAMrC,YAAY,GAAsB;IACtC,CAACmC,SAAS,EAAEG,OAAO,IAAI5E,OAAO,CAACoD,YAAY,KAAK,IAAI,GAAG,OAAO,GAAG5D,WAAA,CAAAyD,oBAAoB,GAAG,CAAC;IACzFC,OAAO,EAAE,IAAI;IACb2B,MAAM,EAAEH,cAAc;IACtBI,WAAW,EAAEN;GACd;EAED,IAAIxE,OAAO,CAACoD,YAAY,KAAK,IAAI,EAAE;IACjCd,YAAY,CAACc,YAAY,GAAG,IAAI;EAClC;EAEA,MAAMxB,WAAW,GAAGQ,WAAW,CAACR,WAAW;EAC3C,IAAIA,WAAW,EAAE;IACf,IAAIA,WAAW,CAACC,SAAS,KAAKhC,WAAA,CAAAiC,aAAa,CAACC,eAAe,IAAIH,WAAW,CAACmD,QAAQ,EAAE;MACnFzC,YAAY,CAAC0C,kBAAkB,GAAG,GAAGpD,WAAW,CAACqD,MAAM,IAAIrD,WAAW,CAACmD,QAAQ,EAAE;MAEjF,MAAMrB,QAAQ,GAAGtB,WAAW,CAACpC,OAAO,CAACgC,aAAa,CAACC,mBAAmB,CACpEpC,WAAA,CAAAiC,aAAa,CAACoD,oBAAoB,EAClCtD,WAAW,CAACM,mBAAmB,CAChC;MACD,IAAI,CAACwB,QAAQ,EAAE;QACb;QACA,MAAM,IAAIhE,OAAA,CAAAyC,yBAAyB,CACjC,uBAAuBtC,WAAA,CAAAiC,aAAa,CAACoD,oBAAoB,WAAW,CACrE;MACH;MACA,OAAO,MAAMxB,QAAQ,CAACyB,OAAO,CAAC7C,YAAY,EAAEF,WAAW,CAAC;IAC1D;IACA,MAAMsB,QAAQ,GAAGtB,WAAW,CAACpC,OAAO,CAACgC,aAAa,CAACC,mBAAmB,CACpEL,WAAW,CAACC,SAAS,EACrBD,WAAW,CAACM,mBAAmB,CAChC;IACD,IAAI,CAACwB,QAAQ,EAAE;MACb,MAAM,IAAIhE,OAAA,CAAAyC,yBAAyB,CAAC,uBAAuBP,WAAW,CAACC,SAAS,WAAW,CAAC;IAC9F;IACA,OAAO,MAAM6B,QAAQ,CAACyB,OAAO,CAAC7C,YAAY,EAAEF,WAAW,CAAC;EAC1D;EACA,OAAOE,YAAY;AACrB;AAEA;AACavD,OAAA,CAAAqG,wBAAwB,GAAG,CACtC,wBAAwB,EACxB,eAAe,EACf,IAAI,EACJ,MAAM,EACN,qBAAqB,EACrB,SAAS,EACT,KAAK,EACL,WAAW,EACX,KAAK,EACL,WAAW,EACX,YAAY,EACZ,KAAK,EACL,oBAAoB,EACpB,eAAe,EACf,gBAAgB,EAChB,YAAY,EACZ,SAAS,CACD;AAEV;AACarG,OAAA,CAAAsG,wBAAwB,GAAG,CACtC,kBAAkB,EAClB,gCAAgC,EAChC,QAAQ,EACR,OAAO,EACP,cAAc,EACd,WAAW,EACX,QAAQ,CACA;AAEV,SAASC,mBAAmBA,CAACtF,OAA0B;EACrD,MAAMqB,WAAW,GAAGrB,OAAO,CAACqB,WAAW;EACvC,IAAI,CAACA,WAAW,EAAE,MAAM,IAAI3B,OAAA,CAAAyC,yBAAyB,CAAC,kCAAkC,CAAC;EAEzF,MAAMoD,MAAM,GAA2D,EAAE;EACzE,KAAK,MAAMC,IAAI,IAAIzG,OAAA,CAAAsG,wBAAwB,EAAE;IAC3C,IAAIrF,OAAO,CAACwF,IAAI,CAAC,IAAI,IAAI,EAAE;MACxBD,MAAmB,CAACC,IAAI,CAAC,GAAGxF,OAAO,CAACwF,IAAI,CAAC;IAC5C;EACF;EAEA,IAAI,OAAOnE,WAAW,CAACoE,UAAU,KAAK,QAAQ,EAAE;IAC9CF,MAAM,CAACG,IAAI,GAAGrE,WAAW,CAACoE,UAAU;IACpC,OAAOF,MAA+B;EACxC,CAAC,MAAM,IAAI,OAAOlE,WAAW,CAACsE,IAAI,KAAK,QAAQ,EAAE;IAC/CJ,MAAM,CAACI,IAAI,GAAGtE,WAAW,CAACsE,IAAI;IAC9BJ,MAAM,CAACK,IAAI,GAAGvE,WAAW,CAACuE,IAAI;IAC9B,OAAOL,MAA+B;EACxC,CAAC,MAAM;IACL;IACA;IACA;IACA,MAAM,IAAI7F,OAAA,CAAAmG,iBAAiB,CAAC,0BAA0BvE,IAAI,CAACC,SAAS,CAACF,WAAW,CAAC,EAAE,CAAC;EACtF;AACF;AAIA,SAASyE,eAAeA,CAAC9F,OAA8B;EACrD,MAAMuF,MAAM,GAAsBD,mBAAmB,CAACtF,OAAO,CAAC;EAC9D;EACA,KAAK,MAAMwF,IAAI,IAAIzG,OAAA,CAAAqG,wBAAwB,EAAE;IAC3C,IAAIpF,OAAO,CAACwF,IAAI,CAAC,IAAI,IAAI,EAAE;MACxBD,MAAmB,CAACC,IAAI,CAAC,GAAGxF,OAAO,CAACwF,IAAI,CAAC;IAC5C;EACF;EAEA,IAAIxF,OAAO,CAAC+F,cAAc,EAAE;IAC1BR,MAAM,CAACrF,MAAM,GAAGF,OAAO,CAAC+F,cAAc;EACxC;EAEA;EACA,IAAIR,MAAM,CAACS,UAAU,IAAI,IAAI,IAAIT,MAAM,CAACI,IAAI,IAAI,CAACtG,GAAG,CAAC4G,IAAI,CAACV,MAAM,CAACI,IAAI,CAAC,EAAE;IACtEJ,MAAM,CAACS,UAAU,GAAGT,MAAM,CAACI,IAAI;EACjC;EAEA,OAAOJ,MAAM;AACf;AAEO,eAAenG,UAAUA,CAACY,OAA8B;EAC7D,MAAMkG,MAAM,GAAGlG,OAAO,CAACT,GAAG,IAAI,KAAK;EACnC,MAAM4G,OAAO,GAAGnG,OAAO,CAACmG,OAAO,IAAI,IAAI;EACvC,MAAM1D,gBAAgB,GAAGzC,OAAO,CAACyC,gBAAgB,IAAI,KAAK;EAC1D,MAAMsD,cAAc,GAAG/F,OAAO,CAAC+F,cAAc;EAE7C,IAAI7F,MAAc;EAElB,IAAIF,OAAO,CAACoG,SAAS,IAAI,IAAI,EAAE;IAC7B;IACA,OAAO,MAAMC,oBAAoB,CAAC;MAChC,GAAGrG,OAAO;MACVyC,gBAAgB,CAAC;KAClB,CAAC;EACJ;EAEA,IAAIyD,MAAM,EAAE;IACV,MAAMI,SAAS,GAAG/G,GAAG,CAACP,OAAO,CAAC8G,eAAe,CAAC9F,OAAO,CAAC,CAAC;IACvD,IAAI,OAAOsG,SAAS,CAACC,oBAAoB,KAAK,UAAU,EAAE;MACxDD,SAAS,CAACC,oBAAoB,EAAE;IAClC;IACArG,MAAM,GAAGoG,SAAS;EACpB,CAAC,MAAM,IAAIP,cAAc,EAAE;IACzB;IACA;IACA;IACA7F,MAAM,GAAG6F,cAAc;EACzB,CAAC,MAAM;IACL7F,MAAM,GAAGb,GAAG,CAACmH,gBAAgB,CAAClB,mBAAmB,CAACtF,OAAO,CAAC,CAAC;EAC7D;EAEAE,MAAM,CAACuG,YAAY,CAAC,IAAI,EAAE,MAAM,CAAC;EACjCvG,MAAM,CAACwG,UAAU,CAACjE,gBAAgB,CAAC;EACnCvC,MAAM,CAACyG,UAAU,CAACR,OAAO,CAAC;EAE1B,IAAIS,mBAAmB,GAAkC,IAAI;EAE7D,MAAM;IAAEC,OAAO,EAAEC,eAAe;IAAEC,OAAO;IAAEC;EAAM,CAAE,GAAG,IAAArH,OAAA,CAAAsH,oBAAoB,GAAU;EACpF,IAAIlB,cAAc,EAAE;IAClBgB,OAAO,CAAC7G,MAAM,CAAC;EACjB,CAAC,MAAM;IACL,MAAMyC,KAAK,GAAGuE,WAAW,CAACC,GAAG,EAAE;IAC/B,MAAMC,YAAY,GAAGlB,MAAM,GAAG,eAAe,GAAG,SAAS;IACzDhG,MAAM,CACHmH,IAAI,CAACD,YAAY,EAAE,MAAML,OAAO,CAAC7G,MAAM,CAAC,CAAC,CACzCmH,IAAI,CAAC,OAAO,EAAEC,KAAK,IAClBN,MAAM,CAAC,IAAItH,OAAA,CAAA6H,iBAAiB,CAAC7H,OAAA,CAAAkE,UAAU,CAAC4D,iBAAiB,CAACF,KAAK,CAAC,EAAE;MAAEA;IAAK,CAAE,CAAC,CAAC,CAC9E,CACAD,IAAI,CAAC,SAAS,EAAE,MAAK;MACpBL,MAAM,CACJ,IAAItH,OAAA,CAAA+H,wBAAwB,CAC1B,WAAWL,YAAY,qBAAsBF,WAAW,CAACC,GAAG,EAAE,GAAGxE,KAAK,GAAI,CAAC,yBAAyBF,gBAAgB,GAAG,CACxH,CACF;IACH,CAAC,CAAC,CACD4E,IAAI,CAAC,OAAO,EAAE,MACbL,MAAM,CACJ,IAAItH,OAAA,CAAA6H,iBAAiB,CACnB,uBAAwBL,WAAW,CAACC,GAAG,EAAE,GAAGxE,KAAK,GAAI,CAAC,kCAAkC,CACzF,CACF,CACF;IAEH,IAAI3C,OAAO,CAAC0H,iBAAiB,IAAI,IAAI,EAAE;MACrCd,mBAAmB,GAAGA,CAAA,KACpBI,MAAM,CACJ,IAAItH,OAAA,CAAA6H,iBAAiB,CACnB,uDAAwDL,WAAW,CAACC,GAAG,EAAE,GAAGxE,KAAK,GAAI,CAAC,EAAE,CACzF,CACF;MACH3C,OAAO,CAAC0H,iBAAiB,CAACL,IAAI,CAAC,QAAQ,EAAET,mBAAmB,CAAC;IAC/D;EACF;EAEA,IAAI;IACF1G,MAAM,GAAG,MAAM4G,eAAe;IAC9B,OAAO5G,MAAM;EACf,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdD,MAAM,CAACE,OAAO,EAAE;IAChB,MAAMD,KAAK;EACb,CAAC,SAAS;IACRD,MAAM,CAACwG,UAAU,CAAC,CAAC,CAAC;IACpB,IAAIE,mBAAmB,IAAI,IAAI,EAAE;MAC/B5G,OAAO,CAAC0H,iBAAiB,EAAEC,cAAc,CAAC,QAAQ,EAAEf,mBAAmB,CAAC;IAC1E;EACF;AACF;AAEA,IAAIgB,KAAK,GAAoB,IAAI;AACjC,SAASC,SAASA,CAAA;EAChB,IAAID,KAAK,IAAI,IAAI,EAAE;IACjB,MAAME,WAAW,GAAG,IAAArI,MAAA,CAAAsI,QAAQ,GAAE;IAC9B,IAAI,cAAc,IAAID,WAAW,EAAE;MACjC,MAAMA,WAAW,CAACE,YAAY;IAChC;IACAJ,KAAK,GAAGE,WAAW;EACrB;EACA,OAAOF,KAAK;AACd;AAEA,eAAevB,oBAAoBA,CAACrG,OAA8B;EAChE,MAAMqB,WAAW,GAAG1B,OAAA,CAAAsI,WAAW,CAACC,YAAY,CAC1ClI,OAAO,CAACoG,SAAS,IAAI,EAAE;EAAE;EACzBpG,OAAO,CAACmI,SAAS,IAAI,IAAI,CAC1B;EAED;EACA,MAAMC,SAAS,GAAG,MAAMhJ,UAAU,CAAC;IACjC,GAAGY,OAAO;IACVqB,WAAW;IACX9B,GAAG,EAAE,KAAK;IACV6G,SAAS,EAAEiC;GACZ,CAAC;EAEF,MAAMC,WAAW,GAAGhD,mBAAmB,CAACtF,OAAO,CAA0B;EACzE,IAAI,OAAOsI,WAAW,CAAC3C,IAAI,KAAK,QAAQ,IAAI,OAAO2C,WAAW,CAAC1C,IAAI,KAAK,QAAQ,EAAE;IAChF,MAAM,IAAIlG,OAAA,CAAAyC,yBAAyB,CAAC,+CAA+C,CAAC;EACtF;EAEAyF,KAAK,KAAKC,SAAS,EAAE;EAErB,IAAI9B,cAAsB;EAE1B,IAAI;IACF;IACA,MAAM9F,UAAU,GAAG,MAAM2H,KAAK,CAACW,WAAW,CAAC/B,gBAAgB,CAAC;MAC1DgC,eAAe,EAAEJ,SAAS;MAC1BK,OAAO,EAAEzI,OAAO,CAACyC,gBAAgB;MACjC6B,OAAO,EAAE,SAAS;MAClBgE,WAAW,EAAE;QACX3C,IAAI,EAAE2C,WAAW,CAAC3C,IAAI;QACtBC,IAAI,EAAE0C,WAAW,CAAC1C;OACnB;MACD8C,KAAK,EAAE;QACL;QACA/C,IAAI,EAAE,iBAAiB;QACvBC,IAAI,EAAE,CAAC;QACP1B,IAAI,EAAE,CAAC;QACPyE,MAAM,EAAE3I,OAAO,CAAC4I,aAAa,IAAIP,SAAS;QAC1CQ,QAAQ,EAAE7I,OAAO,CAAC8I,aAAa,IAAIT;;KAEtC,CAAC;IACFtC,cAAc,GAAG9F,UAAU,CAACC,MAAM;EACpC,CAAC,CAAC,OAAOoH,KAAK,EAAE;IACd,MAAM,IAAI5H,OAAA,CAAA6H,iBAAiB,CAAC7H,OAAA,CAAAkE,UAAU,CAAC4D,iBAAiB,CAACF,KAAK,CAAC,EAAE;MAAEA;IAAK,CAAE,CAAC;EAC7E;EAEA;EACA;EACA,OAAO,MAAMlI,UAAU,CAAC;IAAE,GAAGY,OAAO;IAAE+F,cAAc;IAAEK,SAAS,EAAEiC;EAAS,CAAE,CAAC;AAC/E", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}