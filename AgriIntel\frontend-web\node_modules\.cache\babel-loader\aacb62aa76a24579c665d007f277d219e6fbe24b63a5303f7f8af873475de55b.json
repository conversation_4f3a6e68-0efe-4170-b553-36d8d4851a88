{"ast": null, "code": "import { useState as s } from \"react\";\nimport { useIsoMorphicEffect as f } from './use-iso-morphic-effect.js';\nimport { useLatestValue as m } from './use-latest-value.js';\nfunction i(e, o) {\n  let [u, t] = s(e),\n    r = m(e);\n  return f(() => t(r.current), [r, t, ...o]), u;\n}\nexport { i as useComputed };", "map": {"version": 3, "names": ["useState", "s", "useIsoMorphicEffect", "f", "useLatestValue", "m", "i", "e", "o", "u", "t", "r", "current", "useComputed"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/hooks/use-computed.js"], "sourcesContent": ["import{useState as s}from\"react\";import{useIsoMorphicEffect as f}from'./use-iso-morphic-effect.js';import{useLatestValue as m}from'./use-latest-value.js';function i(e,o){let[u,t]=s(e),r=m(e);return f(()=>t(r.current),[r,t,...o]),u}export{i as useComputed};\n"], "mappings": "AAAA,SAAOA,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAG,CAACC,CAAC,EAACC,CAAC,CAAC,GAACT,CAAC,CAACM,CAAC,CAAC;IAACI,CAAC,GAACN,CAAC,CAACE,CAAC,CAAC;EAAC,OAAOJ,CAAC,CAAC,MAAIO,CAAC,CAACC,CAAC,CAACC,OAAO,CAAC,EAAC,CAACD,CAAC,EAACD,CAAC,EAAC,GAAGF,CAAC,CAAC,CAAC,EAACC,CAAC;AAAA;AAAC,SAAOH,CAAC,IAAIO,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}