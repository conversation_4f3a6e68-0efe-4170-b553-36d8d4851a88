{"ast": null, "code": "import React,{Suspense}from'react';import{BrowserRouter as Router,Routes,Route,Navigate}from'react-router-dom';import{LazyLoadFallback,ErrorBoundary,ResizeObserverFix,MuiButtonFix,SafeThemeProvider,FixedThemeProvider,ButtonReplacer,FinalButtonReplacer,FinalMuiButtonFix,MainNavigationFix}from'./components/common';import{AuthProvider}from'./contexts/AuthContext';import{SnackbarProvider}from'./contexts/SnackbarContext';import{DataProvider}from'./contexts/DataContext';import{OfflineProvider}from'./contexts/OfflineContext';// MongoDB provider removed - using API calls instead\nimport{AlertProvider}from'./contexts/AlertContext';import{IntegratedDataProvider}from'./contexts/IntegratedDataContext';import{LanguageProvider,useLanguage}from'./contexts/LanguageContext';import ProtectedRoute from'./components/ProtectedRoute';import UnifiedDashboardLayout from'./layouts/UnifiedDashboardLayout';import{LocalizationProvider}from'@mui/x-date-pickers';import{AdapterDayjs}from'@mui/x-date-pickers/AdapterDayjs';import{ThemeProvider}from'./contexts/ThemeContext';import{GlobalThemeProvider}from'./providers/GlobalThemeProvider';import{lazyWithRetry,preloadComponents}from'./utils/lazyLoadUtils';import LanguageTest from'./components/LanguageTest';// Import dayjs locales for supported languages\nimport'dayjs/locale/en-gb';// English (UK)\nimport'dayjs/locale/af';// Afrikaans\n// Note: Some South African languages don't have specific dayjs locales\n// We'll use en-gb as fallback for those\nimport'./utils/dateUtils';// Import date utilities to register plugins\n// MUI theme provider is handled by ThemeContext\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Login=lazyWithRetry(()=>import('./pages/Login'),'login');// Lazy load page components by feature groups with enhanced error handling\nconst Dashboard=lazyWithRetry(()=>import('./pages/dashboard/Dashboard'),'dashboard');const Animals=lazyWithRetry(()=>import('./pages/animals/Animals'),'animals');const AnimalsDashboard=lazyWithRetry(()=>import('./pages/animals/AnimalsDashboard'),'animals-dashboard');const Breeding=lazyWithRetry(()=>import('./pages/breeding/Breeding'),'breeding');const BreedingDashboard=lazyWithRetry(()=>import('./pages/breeding/BreedingDashboard'),'breeding-dashboard');const Health=lazyWithRetry(()=>import('./pages/health/Health'),'health');const HealthDashboard=lazyWithRetry(()=>import('./pages/health/HealthDashboard'),'health-dashboard');const Feeding=lazyWithRetry(()=>import('./pages/feeding/Feeding'),'feeding');const FeedingDashboard=lazyWithRetry(()=>import('./pages/feeding/FeedingDashboard'),'feeding-dashboard');// const FeedManagement = lazyWithRetry(() => import('./pages/FeedManagement'), 'feed-management');\nconst Financial=lazyWithRetry(()=>import('./pages/financial/Financial'),'financial');const FinancialDashboard=lazyWithRetry(()=>import('./pages/financial/FinancialDashboard'),'financial-dashboard');const Resources=lazyWithRetry(()=>import('./pages/resources/Resources'),'resources');const ResourcesDashboard=lazyWithRetry(()=>import('./pages/resources/ResourcesDashboard'),'resources-dashboard');const Commercial=lazyWithRetry(()=>import('./pages/commercial/Commercial'),'commercial');// const Reports = lazyWithRetry(() => import('./pages/Reports'), 'reports');\nconst ReportsDashboard=lazyWithRetry(()=>import('./pages/reports/ReportsDashboard'),'reports-dashboard');const AnalysisReport=lazyWithRetry(()=>import('./pages/reports/AnalysisReport'),'analysis-report');const PerformanceReport=lazyWithRetry(()=>import('./pages/reports/PerformanceReport'),'performance-report');const HealthReport=lazyWithRetry(()=>import('./pages/reports/HealthReport'),'health-report');const MarketReport=lazyWithRetry(()=>import('./pages/reports/MarketReport'),'market-report');const FinancialReport=lazyWithRetry(()=>import('./pages/reports/FinancialReport'),'financial-report');const FeedingReports=lazyWithRetry(()=>import('./pages/reports/FeedingReports'),'feeding-reports');const PredictiveAnalysis=lazyWithRetry(()=>import('./pages/reports/PredictiveAnalysis'),'predictive-analysis');const Compliance=lazyWithRetry(()=>import('./pages/Compliance'),'compliance');const Settings=lazyWithRetry(()=>import('./pages/Settings'),'settings');const DatabaseSettings=lazyWithRetry(()=>import('./pages/settings/DatabaseSettings'),'database-settings');const ThemeSettings=lazyWithRetry(()=>import('./pages/settings/ThemeSettings'),'theme-settings');const BackupSettings=lazyWithRetry(()=>import('./pages/settings/BackupSettings'),'backup-settings');const UserManagement=lazyWithRetry(()=>import('./pages/settings/UserManagement'),'user-management');const TranslationTest=lazyWithRetry(()=>import('./pages/TranslationTest'),'translation-test');const BusinessAnalysisDashboard=lazyWithRetry(()=>import('./pages/analytics/BusinessAnalysisDashboard'),'business-analysis-dashboard');const BusinessStrategy=lazyWithRetry(()=>import('./pages/analytics/BusinessStrategy'),'business-strategy');const BusinessPredictions=lazyWithRetry(()=>import('./pages/analytics/BusinessPredictions'),'business-predictions');const AssetManagement=lazyWithRetry(()=>import('./pages/animals/AssetManagement'),'asset-management');const ErrorPage=lazyWithRetry(()=>import('./pages/ErrorPage'),'error-page');/**\r\n * Preload critical components for better user experience with priorities\r\n * This improves initial load time and responsiveness\r\n */preloadComponents([// Critical components (highest priority - load first)\n[()=>import('./pages/Login'),'login',10],[()=>import('./pages/dashboard/Dashboard'),'dashboard',10],// High priority components (essential modules)\n[()=>import('./pages/animals/AnimalsDashboard'),'animals-dashboard',8],[()=>import('./pages/health/HealthDashboard'),'health-dashboard',8],[()=>import('./pages/financial/FinancialDashboard'),'financial-dashboard',8],// Medium priority components (important modules)\n[()=>import('./pages/breeding/BreedingDashboard'),'breeding-dashboard',5],[()=>import('./pages/feeding/FeedingDashboard'),'feeding-dashboard',5],[()=>import('./pages/analytics/BusinessAnalysisDashboard'),'business-analysis-dashboard',5],// Lower priority components (secondary modules)\n[()=>import('./pages/reports/ReportsDashboard'),'reports-dashboard',3],[()=>import('./pages/resources/ResourcesDashboard'),'resources-dashboard',3],// Lowest priority components (rarely used modules)\n[()=>import('./pages/settings/ThemeSettings'),'theme-settings',1],[()=>import('./pages/settings/UserManagement'),'user-management',1]]);/**\r\n * Main App component that sets up the application structure\r\n * Includes error boundaries, fixes, and context providers\r\n */function App(){return/*#__PURE__*/_jsxs(ErrorBoundary,{children:[/*#__PURE__*/_jsx(ResizeObserverFix,{}),/*#__PURE__*/_jsx(MuiButtonFix,{}),/*#__PURE__*/_jsx(ButtonReplacer,{}),/*#__PURE__*/_jsx(FinalButtonReplacer,{}),/*#__PURE__*/_jsx(FinalMuiButtonFix,{}),/*#__PURE__*/_jsx(MainNavigationFix,{}),/*#__PURE__*/_jsx(AuthProvider,{children:/*#__PURE__*/_jsx(ThemeProvider,{children:/*#__PURE__*/_jsx(FixedThemeProvider,{children:/*#__PURE__*/_jsx(SafeThemeProvider,{children:/*#__PURE__*/_jsx(GlobalThemeProvider,{children:/*#__PURE__*/_jsx(AlertProvider,{children:/*#__PURE__*/_jsx(SnackbarProvider,{children:/*#__PURE__*/_jsx(OfflineProvider,{children:/*#__PURE__*/_jsx(DataProvider,{children:/*#__PURE__*/_jsx(IntegratedDataProvider,{children:/*#__PURE__*/_jsx(LanguageProvider,{children:/*#__PURE__*/_jsx(AppWithLocalization,{})})})})})})})})})})})})]});}/**\r\n * Component to handle localization with the selected language\r\n * Provides routing and authentication protection for the application\r\n */const AppWithLocalization=()=>{const{language,translate,isLoading}=useLanguage();// Map language codes to localization adapter locales\nconst getLocale=lang=>{const localeMap={'en':'en-gb','af':'af','zu':'en-gb',// Fallback for Zulu\n'xh':'en-gb',// Fallback for Xhosa\n'st':'en-gb',// Fallback for Sotho\n'tn':'en-gb',// Fallback for Tswana\n've':'en-gb',// Fallback for Venda\n'ts':'en-gb',// Fallback for Tsonga\n'nr':'en-gb',// Fallback for Ndebele\n'ss':'en-gb'// Fallback for Swati\n};return localeMap[lang]||'en-gb';};// Show loading indicator if translations are still loading\nif(isLoading){return/*#__PURE__*/_jsx(LazyLoadFallback,{message:translate('common.loading')});}return/*#__PURE__*/_jsx(LocalizationProvider,{dateAdapter:AdapterDayjs,adapterLocale:getLocale(language),children:/*#__PURE__*/_jsx(Router,{children:/*#__PURE__*/_jsx(Suspense,{fallback:/*#__PURE__*/_jsx(LazyLoadFallback,{message:translate('common.loading')}),children:/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/login\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"/login\",element:/*#__PURE__*/_jsx(Login,{})}),/*#__PURE__*/_jsxs(Route,{path:\"/dashboard\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(UnifiedDashboardLayout,{})}),children:[/*#__PURE__*/_jsx(Route,{index:true,element:/*#__PURE__*/_jsx(Dashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"animals\",element:/*#__PURE__*/_jsx(AnimalsDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"animals/*\",element:/*#__PURE__*/_jsx(Animals,{})}),/*#__PURE__*/_jsx(Route,{path:\"asset-management/*\",element:/*#__PURE__*/_jsx(AssetManagement,{})}),/*#__PURE__*/_jsx(Route,{path:\"breeding\",element:/*#__PURE__*/_jsx(BreedingDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"breeding/*\",element:/*#__PURE__*/_jsx(Breeding,{})}),/*#__PURE__*/_jsx(Route,{path:\"health\",element:/*#__PURE__*/_jsx(HealthDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"health/*\",element:/*#__PURE__*/_jsx(Health,{})}),/*#__PURE__*/_jsx(Route,{path:\"feeding\",element:/*#__PURE__*/_jsx(FeedingDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"feeding/*\",element:/*#__PURE__*/_jsx(Feeding,{})}),/*#__PURE__*/_jsx(Route,{path:\"financial\",element:/*#__PURE__*/_jsx(FinancialDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"financial/*\",element:/*#__PURE__*/_jsx(Financial,{})}),/*#__PURE__*/_jsx(Route,{path:\"resources\",element:/*#__PURE__*/_jsx(ResourcesDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"resources/*\",element:/*#__PURE__*/_jsx(Resources,{})}),/*#__PURE__*/_jsx(Route,{path:\"commercial\",element:/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard/commercial/dashboard\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"commercial/*\",element:/*#__PURE__*/_jsx(Commercial,{})}),/*#__PURE__*/_jsx(Route,{path:\"reports\",element:/*#__PURE__*/_jsx(ReportsDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"reports/dashboard\",element:/*#__PURE__*/_jsx(ReportsDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"reports/analysis\",element:/*#__PURE__*/_jsx(AnalysisReport,{})}),/*#__PURE__*/_jsx(Route,{path:\"reports/performance\",element:/*#__PURE__*/_jsx(PerformanceReport,{})}),/*#__PURE__*/_jsx(Route,{path:\"reports/health\",element:/*#__PURE__*/_jsx(HealthReport,{})}),/*#__PURE__*/_jsx(Route,{path:\"reports/market\",element:/*#__PURE__*/_jsx(MarketReport,{})}),/*#__PURE__*/_jsx(Route,{path:\"reports/financial\",element:/*#__PURE__*/_jsx(FinancialReport,{})}),/*#__PURE__*/_jsx(Route,{path:\"reports/feeding\",element:/*#__PURE__*/_jsx(FeedingReports,{})}),/*#__PURE__*/_jsx(Route,{path:\"reports/predictive\",element:/*#__PURE__*/_jsx(PredictiveAnalysis,{})}),/*#__PURE__*/_jsx(Route,{path:\"analytics\",element:/*#__PURE__*/_jsx(BusinessAnalysisDashboard,{})}),/*#__PURE__*/_jsx(Route,{path:\"analytics/strategy\",element:/*#__PURE__*/_jsx(BusinessStrategy,{})}),/*#__PURE__*/_jsx(Route,{path:\"analytics/predictions\",element:/*#__PURE__*/_jsx(BusinessPredictions,{})}),/*#__PURE__*/_jsx(Route,{path:\"compliance/*\",element:/*#__PURE__*/_jsx(Compliance,{})}),/*#__PURE__*/_jsx(Route,{path:\"settings/*\",element:/*#__PURE__*/_jsx(Settings,{})}),/*#__PURE__*/_jsx(Route,{path:\"settings/database\",element:/*#__PURE__*/_jsx(DatabaseSettings,{})}),/*#__PURE__*/_jsx(Route,{path:\"settings/theme\",element:/*#__PURE__*/_jsx(ThemeSettings,{})}),/*#__PURE__*/_jsx(Route,{path:\"settings/backup\",element:/*#__PURE__*/_jsx(BackupSettings,{})}),/*#__PURE__*/_jsx(Route,{path:\"settings/users\",element:/*#__PURE__*/_jsx(ProtectedRoute,{requiredPermission:\"users:read\",children:/*#__PURE__*/_jsx(UserManagement,{})})}),/*#__PURE__*/_jsx(Route,{path:\"language-test\",element:/*#__PURE__*/_jsx(LanguageTest,{})}),/*#__PURE__*/_jsx(Route,{path:\"translation-test\",element:/*#__PURE__*/_jsx(TranslationTest,{})}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(ErrorPage,{})})]})]})})})});};export default App;", "map": {"version": 3, "names": ["React", "Suspense", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Router", "Routes", "Route", "Navigate", "LazyLoadFallback", "Error<PERSON>ou<PERSON><PERSON>", "ResizeObserverFix", "MuiButtonFix", "SafeThemeProvider", "FixedThemeProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "FinalButtonReplacer", "FinalMuiButtonFix", "MainNavigationFix", "<PERSON>th<PERSON><PERSON><PERSON>", "SnackbarProvider", "DataProvider", "OfflineProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "IntegratedDataProvider", "LanguageProvider", "useLanguage", "ProtectedRoute", "UnifiedDashboardLayout", "LocalizationProvider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ThemeProvider", "GlobalThemeProvider", "lazyWithRetry", "preloadComponents", "LanguageTest", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>", "Dashboard", "Animals", "AnimalsDashboard", "Breeding", "BreedingDashboard", "Health", "HealthDashboard", "Feeding", "FeedingDashboard", "Financial", "FinancialDashboard", "Resources", "ResourcesDashboard", "Commercial", "ReportsDashboard", "AnalysisReport", "PerformanceReport", "HealthReport", "MarketReport", "FinancialReport", "FeedingReports", "PredictiveAnalysis", "Compliance", "Settings", "DatabaseSettings", "ThemeSettings", "BackupSettings", "UserManagement", "TranslationTest", "BusinessAnalysisDashboard", "BusinessStrategy", "BusinessPredictions", "AssetManagement", "ErrorPage", "App", "children", "AppWithLocalization", "language", "translate", "isLoading", "getLocale", "lang", "localeMap", "message", "dateAdapter", "adapterLocale", "fallback", "path", "element", "to", "replace", "index", "requiredPermission"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/App.tsx"], "sourcesContent": ["import React, { Suspense } from 'react';\r\nimport { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';\r\nimport {\r\n  LazyLoadFallback,\r\n  ErrorBoundary,\r\n  ResizeObserverFix,\r\n  MuiButtonFix,\r\n  SafeThemeProvider,\r\n  FixedThemeProvider,\r\n  ButtonReplacer,\r\n  FinalButtonReplacer,\r\n  FinalMuiButtonFix,\r\n  MainNavigationFix\r\n} from './components/common';\r\nimport { AuthProvider } from './contexts/AuthContext';\r\nimport { SnackbarProvider } from './contexts/SnackbarContext';\r\nimport { DataProvider } from './contexts/DataContext';\r\nimport { OfflineProvider } from './contexts/OfflineContext';\r\n// MongoDB provider removed - using API calls instead\r\nimport { AlertProvider } from './contexts/AlertContext';\r\nimport { IntegratedDataProvider } from './contexts/IntegratedDataContext';\r\nimport { LanguageProvider, useLanguage } from './contexts/LanguageContext';\r\nimport ProtectedRoute from './components/ProtectedRoute';\r\nimport UnifiedDashboardLayout from './layouts/UnifiedDashboardLayout';\r\nimport { LocalizationProvider } from '@mui/x-date-pickers';\r\nimport { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';\r\nimport { ThemeProvider } from './contexts/ThemeContext';\r\nimport { GlobalThemeProvider } from './providers/GlobalThemeProvider';\r\nimport { lazyWithRetry, preloadComponents } from './utils/lazyLoadUtils';\r\nimport LanguageTest from './components/LanguageTest';\r\n// Import dayjs locales for supported languages\r\nimport 'dayjs/locale/en-gb'; // English (UK)\r\nimport 'dayjs/locale/af'; // Afrikaans\r\n// Note: Some South African languages don't have specific dayjs locales\r\n// We'll use en-gb as fallback for those\r\nimport './utils/dateUtils'; // Import date utilities to register plugins\r\n// MUI theme provider is handled by ThemeContext\r\n\r\nconst Login = lazyWithRetry(() => import('./pages/Login'), 'login');\r\n\r\n// Lazy load page components by feature groups with enhanced error handling\r\nconst Dashboard = lazyWithRetry(() => import('./pages/dashboard/Dashboard'), 'dashboard');\r\nconst Animals = lazyWithRetry(() => import('./pages/animals/Animals'), 'animals');\r\nconst AnimalsDashboard = lazyWithRetry(() => import('./pages/animals/AnimalsDashboard'), 'animals-dashboard');\r\nconst Breeding = lazyWithRetry(() => import('./pages/breeding/Breeding'), 'breeding');\r\nconst BreedingDashboard = lazyWithRetry(() => import('./pages/breeding/BreedingDashboard'), 'breeding-dashboard');\r\nconst Health = lazyWithRetry(() => import('./pages/health/Health'), 'health');\r\nconst HealthDashboard = lazyWithRetry(() => import('./pages/health/HealthDashboard'), 'health-dashboard');\r\nconst Feeding = lazyWithRetry(() => import('./pages/feeding/Feeding'), 'feeding');\r\nconst FeedingDashboard = lazyWithRetry(() => import('./pages/feeding/FeedingDashboard'), 'feeding-dashboard');\r\n// const FeedManagement = lazyWithRetry(() => import('./pages/FeedManagement'), 'feed-management');\r\nconst Financial = lazyWithRetry(() => import('./pages/financial/Financial'), 'financial');\r\nconst FinancialDashboard = lazyWithRetry(() => import('./pages/financial/FinancialDashboard'), 'financial-dashboard');\r\nconst Resources = lazyWithRetry(() => import('./pages/resources/Resources'), 'resources');\r\nconst ResourcesDashboard = lazyWithRetry(() => import('./pages/resources/ResourcesDashboard'), 'resources-dashboard');\r\nconst Commercial = lazyWithRetry(() => import('./pages/commercial/Commercial'), 'commercial');\r\n// const Reports = lazyWithRetry(() => import('./pages/Reports'), 'reports');\r\nconst ReportsDashboard = lazyWithRetry(() => import('./pages/reports/ReportsDashboard'), 'reports-dashboard');\r\nconst AnalysisReport = lazyWithRetry(() => import('./pages/reports/AnalysisReport'), 'analysis-report');\r\nconst PerformanceReport = lazyWithRetry(() => import('./pages/reports/PerformanceReport'), 'performance-report');\r\nconst HealthReport = lazyWithRetry(() => import('./pages/reports/HealthReport'), 'health-report');\r\nconst MarketReport = lazyWithRetry(() => import('./pages/reports/MarketReport'), 'market-report');\r\nconst FinancialReport = lazyWithRetry(() => import('./pages/reports/FinancialReport'), 'financial-report');\r\nconst FeedingReports = lazyWithRetry(() => import('./pages/reports/FeedingReports'), 'feeding-reports');\r\nconst PredictiveAnalysis = lazyWithRetry(() => import('./pages/reports/PredictiveAnalysis'), 'predictive-analysis');\r\nconst Compliance = lazyWithRetry(() => import('./pages/Compliance'), 'compliance');\r\nconst Settings = lazyWithRetry(() => import('./pages/Settings'), 'settings');\r\nconst DatabaseSettings = lazyWithRetry(() => import('./pages/settings/DatabaseSettings'), 'database-settings');\r\nconst ThemeSettings = lazyWithRetry(() => import('./pages/settings/ThemeSettings'), 'theme-settings');\r\nconst BackupSettings = lazyWithRetry(() => import('./pages/settings/BackupSettings'), 'backup-settings');\r\nconst UserManagement = lazyWithRetry(() => import('./pages/settings/UserManagement'), 'user-management');\r\nconst TranslationTest = lazyWithRetry(() => import('./pages/TranslationTest'), 'translation-test');\r\n\r\nconst BusinessAnalysisDashboard = lazyWithRetry(() => import('./pages/analytics/BusinessAnalysisDashboard'), 'business-analysis-dashboard');\r\nconst BusinessStrategy = lazyWithRetry(() => import('./pages/analytics/BusinessStrategy'), 'business-strategy');\r\nconst BusinessPredictions = lazyWithRetry(() => import('./pages/analytics/BusinessPredictions'), 'business-predictions');\r\nconst AssetManagement = lazyWithRetry(() => import('./pages/animals/AssetManagement'), 'asset-management');\r\nconst ErrorPage = lazyWithRetry(() => import('./pages/ErrorPage'), 'error-page');\r\n\r\n/**\r\n * Preload critical components for better user experience with priorities\r\n * This improves initial load time and responsiveness\r\n */\r\npreloadComponents([\r\n  // Critical components (highest priority - load first)\r\n  [() => import('./pages/Login'), 'login', 10],\r\n  [() => import('./pages/dashboard/Dashboard'), 'dashboard', 10],\r\n\r\n  // High priority components (essential modules)\r\n  [() => import('./pages/animals/AnimalsDashboard'), 'animals-dashboard', 8],\r\n  [() => import('./pages/health/HealthDashboard'), 'health-dashboard', 8],\r\n  [() => import('./pages/financial/FinancialDashboard'), 'financial-dashboard', 8],\r\n\r\n  // Medium priority components (important modules)\r\n  [() => import('./pages/breeding/BreedingDashboard'), 'breeding-dashboard', 5],\r\n  [() => import('./pages/feeding/FeedingDashboard'), 'feeding-dashboard', 5],\r\n  [() => import('./pages/analytics/BusinessAnalysisDashboard'), 'business-analysis-dashboard', 5],\r\n\r\n  // Lower priority components (secondary modules)\r\n  [() => import('./pages/reports/ReportsDashboard'), 'reports-dashboard', 3],\r\n  [() => import('./pages/resources/ResourcesDashboard'), 'resources-dashboard', 3],\r\n\r\n  // Lowest priority components (rarely used modules)\r\n  [() => import('./pages/settings/ThemeSettings'), 'theme-settings', 1],\r\n  [() => import('./pages/settings/UserManagement'), 'user-management', 1]\r\n]);\r\n\r\n/**\r\n * Main App component that sets up the application structure\r\n * Includes error boundaries, fixes, and context providers\r\n */\r\nfunction App() {\r\n  return (\r\n    <ErrorBoundary>\r\n      {/* Apply critical fixes first */}\r\n      <ResizeObserverFix />\r\n      <MuiButtonFix />\r\n      <ButtonReplacer />\r\n      <FinalButtonReplacer />\r\n      <FinalMuiButtonFix />\r\n      <MainNavigationFix />\r\n\r\n      {/* Authentication provider */}\r\n      <AuthProvider>\r\n        {/* Theme providers */}\r\n        <ThemeProvider>\r\n          <FixedThemeProvider>\r\n            <SafeThemeProvider>\r\n              <GlobalThemeProvider>\r\n                {/* Application service providers */}\r\n                <AlertProvider>\r\n                  <SnackbarProvider>\r\n                    <OfflineProvider>\r\n                      {/* Data providers */}\r\n                      <DataProvider>\r\n                        <IntegratedDataProvider>\r\n                          {/* Localization provider */}\r\n                          <LanguageProvider>\r\n                            {/* Main application with localization */}\r\n                            <AppWithLocalization />\r\n                          </LanguageProvider>\r\n                        </IntegratedDataProvider>\r\n                      </DataProvider>\r\n                    </OfflineProvider>\r\n                  </SnackbarProvider>\r\n                </AlertProvider>\r\n              </GlobalThemeProvider>\r\n            </SafeThemeProvider>\r\n          </FixedThemeProvider>\r\n        </ThemeProvider>\r\n      </AuthProvider>\r\n    </ErrorBoundary>\r\n  );\r\n}\r\n\r\n/**\r\n * Component to handle localization with the selected language\r\n * Provides routing and authentication protection for the application\r\n */\r\nconst AppWithLocalization: React.FC = () => {\r\n  const { language, translate, isLoading } = useLanguage();\r\n\r\n  // Map language codes to localization adapter locales\r\n  const getLocale = (lang: string): string => {\r\n    const localeMap: Record<string, string> = {\r\n      'en': 'en-gb',\r\n      'af': 'af',\r\n      'zu': 'en-gb', // Fallback for Zulu\r\n      'xh': 'en-gb', // Fallback for Xhosa\r\n      'st': 'en-gb', // Fallback for Sotho\r\n      'tn': 'en-gb', // Fallback for Tswana\r\n      've': 'en-gb', // Fallback for Venda\r\n      'ts': 'en-gb', // Fallback for Tsonga\r\n      'nr': 'en-gb', // Fallback for Ndebele\r\n      'ss': 'en-gb'  // Fallback for Swati\r\n    };\r\n\r\n    return localeMap[lang] || 'en-gb';\r\n  };\r\n\r\n  // Show loading indicator if translations are still loading\r\n  if (isLoading) {\r\n    return <LazyLoadFallback message={translate('common.loading')} />;\r\n  }\r\n\r\n  return (\r\n    <LocalizationProvider dateAdapter={AdapterDayjs} adapterLocale={getLocale(language)}>\r\n      <Router>\r\n        <Suspense fallback={<LazyLoadFallback message={translate('common.loading')} />}>\r\n          <Routes>\r\n            {/* Redirect root to login */}\r\n            <Route path=\"/\" element={<Navigate to=\"/login\" replace />} />\r\n            <Route path=\"/login\" element={<Login />} />\r\n            <Route\r\n              path=\"/dashboard\"\r\n              element={\r\n                <ProtectedRoute>\r\n                  <UnifiedDashboardLayout />\r\n                </ProtectedRoute>\r\n              }\r\n            >\r\n            {/* Main Dashboard */}\r\n            <Route index element={<Dashboard />} />\r\n\r\n            {/* Animal Management Module */}\r\n            <Route path=\"animals\" element={<AnimalsDashboard />} />\r\n            <Route path=\"animals/*\" element={<Animals />} />\r\n            <Route path=\"asset-management/*\" element={<AssetManagement />} />\r\n\r\n            {/* Breeding Management Module */}\r\n            <Route path=\"breeding\" element={<BreedingDashboard />} />\r\n            <Route path=\"breeding/*\" element={<Breeding />} />\r\n\r\n            {/* Health Management Module */}\r\n            <Route path=\"health\" element={<HealthDashboard />} />\r\n            <Route path=\"health/*\" element={<Health />} />\r\n\r\n            {/* Feeding Management Module */}\r\n            <Route path=\"feeding\" element={<FeedingDashboard />} />\r\n            <Route path=\"feeding/*\" element={<Feeding />} />\r\n\r\n            {/* Financial Management Module */}\r\n            <Route path=\"financial\" element={<FinancialDashboard />} />\r\n            <Route path=\"financial/*\" element={<Financial />} />\r\n\r\n            {/* Resources Management Module */}\r\n            <Route path=\"resources\" element={<ResourcesDashboard />} />\r\n            <Route path=\"resources/*\" element={<Resources />} />\r\n\r\n            {/* Commercial Module */}\r\n            <Route path=\"commercial\" element={<Navigate to=\"/dashboard/commercial/dashboard\" replace />} />\r\n            <Route path=\"commercial/*\" element={<Commercial />} />\r\n\r\n            {/* Reports Module */}\r\n            <Route path=\"reports\" element={<ReportsDashboard />} />\r\n            <Route path=\"reports/dashboard\" element={<ReportsDashboard />} />\r\n            <Route path=\"reports/analysis\" element={<AnalysisReport />} />\r\n            <Route path=\"reports/performance\" element={<PerformanceReport />} />\r\n            <Route path=\"reports/health\" element={<HealthReport />} />\r\n            <Route path=\"reports/market\" element={<MarketReport />} />\r\n            <Route path=\"reports/financial\" element={<FinancialReport />} />\r\n            <Route path=\"reports/feeding\" element={<FeedingReports />} />\r\n            <Route path=\"reports/predictive\" element={<PredictiveAnalysis />} />\r\n\r\n            {/* Business Analytics Module */}\r\n            <Route path=\"analytics\" element={<BusinessAnalysisDashboard />} />\r\n            <Route path=\"analytics/strategy\" element={<BusinessStrategy />} />\r\n            <Route path=\"analytics/predictions\" element={<BusinessPredictions />} />\r\n\r\n            {/* Compliance Module */}\r\n            <Route path=\"compliance/*\" element={<Compliance />} />\r\n\r\n            {/* Settings Module */}\r\n            <Route path=\"settings/*\" element={<Settings />} />\r\n            <Route path=\"settings/database\" element={<DatabaseSettings />} />\r\n            <Route path=\"settings/theme\" element={<ThemeSettings />} />\r\n            <Route path=\"settings/backup\" element={<BackupSettings />} />\r\n            <Route path=\"settings/users\" element={\r\n              <ProtectedRoute requiredPermission=\"users:read\">\r\n                <UserManagement />\r\n              </ProtectedRoute>\r\n            } />\r\n\r\n            {/* Development & Testing */}\r\n            <Route path=\"language-test\" element={<LanguageTest />} />\r\n            <Route path=\"translation-test\" element={<TranslationTest />} />\r\n\r\n            {/* Error Page - Catch All */}\r\n            <Route path=\"*\" element={<ErrorPage />} />\r\n          </Route>\r\n        </Routes>\r\n      </Suspense>\r\n    </Router>\r\n  </LocalizationProvider>\r\n  );\r\n};\r\n\r\nexport default App;\r\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,aAAa,GAAI,CAAAC,MAAM,CAAEC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CACnF,OACEC,gBAAgB,CAChBC,aAAa,CACbC,iBAAiB,CACjBC,YAAY,CACZC,iBAAiB,CACjBC,kBAAkB,CAClBC,cAAc,CACdC,mBAAmB,CACnBC,iBAAiB,CACjBC,iBAAiB,KACZ,qBAAqB,CAC5B,OAASC,YAAY,KAAQ,wBAAwB,CACrD,OAASC,gBAAgB,KAAQ,4BAA4B,CAC7D,OAASC,YAAY,KAAQ,wBAAwB,CACrD,OAASC,eAAe,KAAQ,2BAA2B,CAC3D;AACA,OAASC,aAAa,KAAQ,yBAAyB,CACvD,OAASC,sBAAsB,KAAQ,kCAAkC,CACzE,OAASC,gBAAgB,CAAEC,WAAW,KAAQ,4BAA4B,CAC1E,MAAO,CAAAC,cAAc,KAAM,6BAA6B,CACxD,MAAO,CAAAC,sBAAsB,KAAM,kCAAkC,CACrE,OAASC,oBAAoB,KAAQ,qBAAqB,CAC1D,OAASC,YAAY,KAAQ,kCAAkC,CAC/D,OAASC,aAAa,KAAQ,yBAAyB,CACvD,OAASC,mBAAmB,KAAQ,iCAAiC,CACrE,OAASC,aAAa,CAAEC,iBAAiB,KAAQ,uBAAuB,CACxE,MAAO,CAAAC,YAAY,KAAM,2BAA2B,CACpD;AACA,MAAO,oBAAoB,CAAE;AAC7B,MAAO,iBAAiB,CAAE;AAC1B;AACA;AACA,MAAO,mBAAmB,CAAE;AAC5B;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEA,KAAM,CAAAC,KAAK,CAAGP,aAAa,CAAC,IAAM,MAAM,CAAC,eAAe,CAAC,CAAE,OAAO,CAAC,CAEnE;AACA,KAAM,CAAAQ,SAAS,CAAGR,aAAa,CAAC,IAAM,MAAM,CAAC,6BAA6B,CAAC,CAAE,WAAW,CAAC,CACzF,KAAM,CAAAS,OAAO,CAAGT,aAAa,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAE,SAAS,CAAC,CACjF,KAAM,CAAAU,gBAAgB,CAAGV,aAAa,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAE,mBAAmB,CAAC,CAC7G,KAAM,CAAAW,QAAQ,CAAGX,aAAa,CAAC,IAAM,MAAM,CAAC,2BAA2B,CAAC,CAAE,UAAU,CAAC,CACrF,KAAM,CAAAY,iBAAiB,CAAGZ,aAAa,CAAC,IAAM,MAAM,CAAC,oCAAoC,CAAC,CAAE,oBAAoB,CAAC,CACjH,KAAM,CAAAa,MAAM,CAAGb,aAAa,CAAC,IAAM,MAAM,CAAC,uBAAuB,CAAC,CAAE,QAAQ,CAAC,CAC7E,KAAM,CAAAc,eAAe,CAAGd,aAAa,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAE,kBAAkB,CAAC,CACzG,KAAM,CAAAe,OAAO,CAAGf,aAAa,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAE,SAAS,CAAC,CACjF,KAAM,CAAAgB,gBAAgB,CAAGhB,aAAa,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAE,mBAAmB,CAAC,CAC7G;AACA,KAAM,CAAAiB,SAAS,CAAGjB,aAAa,CAAC,IAAM,MAAM,CAAC,6BAA6B,CAAC,CAAE,WAAW,CAAC,CACzF,KAAM,CAAAkB,kBAAkB,CAAGlB,aAAa,CAAC,IAAM,MAAM,CAAC,sCAAsC,CAAC,CAAE,qBAAqB,CAAC,CACrH,KAAM,CAAAmB,SAAS,CAAGnB,aAAa,CAAC,IAAM,MAAM,CAAC,6BAA6B,CAAC,CAAE,WAAW,CAAC,CACzF,KAAM,CAAAoB,kBAAkB,CAAGpB,aAAa,CAAC,IAAM,MAAM,CAAC,sCAAsC,CAAC,CAAE,qBAAqB,CAAC,CACrH,KAAM,CAAAqB,UAAU,CAAGrB,aAAa,CAAC,IAAM,MAAM,CAAC,+BAA+B,CAAC,CAAE,YAAY,CAAC,CAC7F;AACA,KAAM,CAAAsB,gBAAgB,CAAGtB,aAAa,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAE,mBAAmB,CAAC,CAC7G,KAAM,CAAAuB,cAAc,CAAGvB,aAAa,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAE,iBAAiB,CAAC,CACvG,KAAM,CAAAwB,iBAAiB,CAAGxB,aAAa,CAAC,IAAM,MAAM,CAAC,mCAAmC,CAAC,CAAE,oBAAoB,CAAC,CAChH,KAAM,CAAAyB,YAAY,CAAGzB,aAAa,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAE,eAAe,CAAC,CACjG,KAAM,CAAA0B,YAAY,CAAG1B,aAAa,CAAC,IAAM,MAAM,CAAC,8BAA8B,CAAC,CAAE,eAAe,CAAC,CACjG,KAAM,CAAA2B,eAAe,CAAG3B,aAAa,CAAC,IAAM,MAAM,CAAC,iCAAiC,CAAC,CAAE,kBAAkB,CAAC,CAC1G,KAAM,CAAA4B,cAAc,CAAG5B,aAAa,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAE,iBAAiB,CAAC,CACvG,KAAM,CAAA6B,kBAAkB,CAAG7B,aAAa,CAAC,IAAM,MAAM,CAAC,oCAAoC,CAAC,CAAE,qBAAqB,CAAC,CACnH,KAAM,CAAA8B,UAAU,CAAG9B,aAAa,CAAC,IAAM,MAAM,CAAC,oBAAoB,CAAC,CAAE,YAAY,CAAC,CAClF,KAAM,CAAA+B,QAAQ,CAAG/B,aAAa,CAAC,IAAM,MAAM,CAAC,kBAAkB,CAAC,CAAE,UAAU,CAAC,CAC5E,KAAM,CAAAgC,gBAAgB,CAAGhC,aAAa,CAAC,IAAM,MAAM,CAAC,mCAAmC,CAAC,CAAE,mBAAmB,CAAC,CAC9G,KAAM,CAAAiC,aAAa,CAAGjC,aAAa,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAE,gBAAgB,CAAC,CACrG,KAAM,CAAAkC,cAAc,CAAGlC,aAAa,CAAC,IAAM,MAAM,CAAC,iCAAiC,CAAC,CAAE,iBAAiB,CAAC,CACxG,KAAM,CAAAmC,cAAc,CAAGnC,aAAa,CAAC,IAAM,MAAM,CAAC,iCAAiC,CAAC,CAAE,iBAAiB,CAAC,CACxG,KAAM,CAAAoC,eAAe,CAAGpC,aAAa,CAAC,IAAM,MAAM,CAAC,yBAAyB,CAAC,CAAE,kBAAkB,CAAC,CAElG,KAAM,CAAAqC,yBAAyB,CAAGrC,aAAa,CAAC,IAAM,MAAM,CAAC,6CAA6C,CAAC,CAAE,6BAA6B,CAAC,CAC3I,KAAM,CAAAsC,gBAAgB,CAAGtC,aAAa,CAAC,IAAM,MAAM,CAAC,oCAAoC,CAAC,CAAE,mBAAmB,CAAC,CAC/G,KAAM,CAAAuC,mBAAmB,CAAGvC,aAAa,CAAC,IAAM,MAAM,CAAC,uCAAuC,CAAC,CAAE,sBAAsB,CAAC,CACxH,KAAM,CAAAwC,eAAe,CAAGxC,aAAa,CAAC,IAAM,MAAM,CAAC,iCAAiC,CAAC,CAAE,kBAAkB,CAAC,CAC1G,KAAM,CAAAyC,SAAS,CAAGzC,aAAa,CAAC,IAAM,MAAM,CAAC,mBAAmB,CAAC,CAAE,YAAY,CAAC,CAEhF;AACA;AACA;AACA,GACAC,iBAAiB,CAAC,CAChB;AACA,CAAC,IAAM,MAAM,CAAC,eAAe,CAAC,CAAE,OAAO,CAAE,EAAE,CAAC,CAC5C,CAAC,IAAM,MAAM,CAAC,6BAA6B,CAAC,CAAE,WAAW,CAAE,EAAE,CAAC,CAE9D;AACA,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAE,mBAAmB,CAAE,CAAC,CAAC,CAC1E,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAE,kBAAkB,CAAE,CAAC,CAAC,CACvE,CAAC,IAAM,MAAM,CAAC,sCAAsC,CAAC,CAAE,qBAAqB,CAAE,CAAC,CAAC,CAEhF;AACA,CAAC,IAAM,MAAM,CAAC,oCAAoC,CAAC,CAAE,oBAAoB,CAAE,CAAC,CAAC,CAC7E,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAE,mBAAmB,CAAE,CAAC,CAAC,CAC1E,CAAC,IAAM,MAAM,CAAC,6CAA6C,CAAC,CAAE,6BAA6B,CAAE,CAAC,CAAC,CAE/F;AACA,CAAC,IAAM,MAAM,CAAC,kCAAkC,CAAC,CAAE,mBAAmB,CAAE,CAAC,CAAC,CAC1E,CAAC,IAAM,MAAM,CAAC,sCAAsC,CAAC,CAAE,qBAAqB,CAAE,CAAC,CAAC,CAEhF;AACA,CAAC,IAAM,MAAM,CAAC,gCAAgC,CAAC,CAAE,gBAAgB,CAAE,CAAC,CAAC,CACrE,CAAC,IAAM,MAAM,CAAC,iCAAiC,CAAC,CAAE,iBAAiB,CAAE,CAAC,CAAC,CACxE,CAAC,CAEF;AACA;AACA;AACA,GACA,QAAS,CAAAyC,GAAGA,CAAA,CAAG,CACb,mBACEpC,KAAA,CAAC7B,aAAa,EAAAkE,QAAA,eAEZvC,IAAA,CAAC1B,iBAAiB,GAAE,CAAC,cACrB0B,IAAA,CAACzB,YAAY,GAAE,CAAC,cAChByB,IAAA,CAACtB,cAAc,GAAE,CAAC,cAClBsB,IAAA,CAACrB,mBAAmB,GAAE,CAAC,cACvBqB,IAAA,CAACpB,iBAAiB,GAAE,CAAC,cACrBoB,IAAA,CAACnB,iBAAiB,GAAE,CAAC,cAGrBmB,IAAA,CAAClB,YAAY,EAAAyD,QAAA,cAEXvC,IAAA,CAACN,aAAa,EAAA6C,QAAA,cACZvC,IAAA,CAACvB,kBAAkB,EAAA8D,QAAA,cACjBvC,IAAA,CAACxB,iBAAiB,EAAA+D,QAAA,cAChBvC,IAAA,CAACL,mBAAmB,EAAA4C,QAAA,cAElBvC,IAAA,CAACd,aAAa,EAAAqD,QAAA,cACZvC,IAAA,CAACjB,gBAAgB,EAAAwD,QAAA,cACfvC,IAAA,CAACf,eAAe,EAAAsD,QAAA,cAEdvC,IAAA,CAAChB,YAAY,EAAAuD,QAAA,cACXvC,IAAA,CAACb,sBAAsB,EAAAoD,QAAA,cAErBvC,IAAA,CAACZ,gBAAgB,EAAAmD,QAAA,cAEfvC,IAAA,CAACwC,mBAAmB,GAAE,CAAC,CACP,CAAC,CACG,CAAC,CACb,CAAC,CACA,CAAC,CACF,CAAC,CACN,CAAC,CACG,CAAC,CACL,CAAC,CACF,CAAC,CACR,CAAC,CACJ,CAAC,EACF,CAAC,CAEpB,CAEA;AACA;AACA;AACA,GACA,KAAM,CAAAA,mBAA6B,CAAGA,CAAA,GAAM,CAC1C,KAAM,CAAEC,QAAQ,CAAEC,SAAS,CAAEC,SAAU,CAAC,CAAGtD,WAAW,CAAC,CAAC,CAExD;AACA,KAAM,CAAAuD,SAAS,CAAIC,IAAY,EAAa,CAC1C,KAAM,CAAAC,SAAiC,CAAG,CACxC,IAAI,CAAE,OAAO,CACb,IAAI,CAAE,IAAI,CACV,IAAI,CAAE,OAAO,CAAE;AACf,IAAI,CAAE,OAAO,CAAE;AACf,IAAI,CAAE,OAAO,CAAE;AACf,IAAI,CAAE,OAAO,CAAE;AACf,IAAI,CAAE,OAAO,CAAE;AACf,IAAI,CAAE,OAAO,CAAE;AACf,IAAI,CAAE,OAAO,CAAE;AACf,IAAI,CAAE,OAAS;AACjB,CAAC,CAED,MAAO,CAAAA,SAAS,CAACD,IAAI,CAAC,EAAI,OAAO,CACnC,CAAC,CAED;AACA,GAAIF,SAAS,CAAE,CACb,mBAAO3C,IAAA,CAAC5B,gBAAgB,EAAC2E,OAAO,CAAEL,SAAS,CAAC,gBAAgB,CAAE,CAAE,CAAC,CACnE,CAEA,mBACE1C,IAAA,CAACR,oBAAoB,EAACwD,WAAW,CAAEvD,YAAa,CAACwD,aAAa,CAAEL,SAAS,CAACH,QAAQ,CAAE,CAAAF,QAAA,cAClFvC,IAAA,CAAChC,MAAM,EAAAuE,QAAA,cACLvC,IAAA,CAAClC,QAAQ,EAACoF,QAAQ,cAAElD,IAAA,CAAC5B,gBAAgB,EAAC2E,OAAO,CAAEL,SAAS,CAAC,gBAAgB,CAAE,CAAE,CAAE,CAAAH,QAAA,cAC7ErC,KAAA,CAACjC,MAAM,EAAAsE,QAAA,eAELvC,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEpD,IAAA,CAAC7B,QAAQ,EAACkF,EAAE,CAAC,QAAQ,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cAC7DtD,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEpD,IAAA,CAACG,KAAK,GAAE,CAAE,CAAE,CAAC,cAC3CD,KAAA,CAAChC,KAAK,EACJiF,IAAI,CAAC,YAAY,CACjBC,OAAO,cACLpD,IAAA,CAACV,cAAc,EAAAiD,QAAA,cACbvC,IAAA,CAACT,sBAAsB,GAAE,CAAC,CACZ,CACjB,CAAAgD,QAAA,eAGHvC,IAAA,CAAC9B,KAAK,EAACqF,KAAK,MAACH,OAAO,cAAEpD,IAAA,CAACI,SAAS,GAAE,CAAE,CAAE,CAAC,cAGvCJ,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEpD,IAAA,CAACM,gBAAgB,GAAE,CAAE,CAAE,CAAC,cACvDN,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEpD,IAAA,CAACK,OAAO,GAAE,CAAE,CAAE,CAAC,cAChDL,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,oBAAoB,CAACC,OAAO,cAAEpD,IAAA,CAACoC,eAAe,GAAE,CAAE,CAAE,CAAC,cAGjEpC,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEpD,IAAA,CAACQ,iBAAiB,GAAE,CAAE,CAAE,CAAC,cACzDR,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEpD,IAAA,CAACO,QAAQ,GAAE,CAAE,CAAE,CAAC,cAGlDP,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAEpD,IAAA,CAACU,eAAe,GAAE,CAAE,CAAE,CAAC,cACrDV,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,UAAU,CAACC,OAAO,cAAEpD,IAAA,CAACS,MAAM,GAAE,CAAE,CAAE,CAAC,cAG9CT,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEpD,IAAA,CAACY,gBAAgB,GAAE,CAAE,CAAE,CAAC,cACvDZ,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEpD,IAAA,CAACW,OAAO,GAAE,CAAE,CAAE,CAAC,cAGhDX,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEpD,IAAA,CAACc,kBAAkB,GAAE,CAAE,CAAE,CAAC,cAC3Dd,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,aAAa,CAACC,OAAO,cAAEpD,IAAA,CAACa,SAAS,GAAE,CAAE,CAAE,CAAC,cAGpDb,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEpD,IAAA,CAACgB,kBAAkB,GAAE,CAAE,CAAE,CAAC,cAC3DhB,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,aAAa,CAACC,OAAO,cAAEpD,IAAA,CAACe,SAAS,GAAE,CAAE,CAAE,CAAC,cAGpDf,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEpD,IAAA,CAAC7B,QAAQ,EAACkF,EAAE,CAAC,iCAAiC,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cAC/FtD,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEpD,IAAA,CAACiB,UAAU,GAAE,CAAE,CAAE,CAAC,cAGtDjB,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,SAAS,CAACC,OAAO,cAAEpD,IAAA,CAACkB,gBAAgB,GAAE,CAAE,CAAE,CAAC,cACvDlB,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAEpD,IAAA,CAACkB,gBAAgB,GAAE,CAAE,CAAE,CAAC,cACjElB,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAEpD,IAAA,CAACmB,cAAc,GAAE,CAAE,CAAE,CAAC,cAC9DnB,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,qBAAqB,CAACC,OAAO,cAAEpD,IAAA,CAACoB,iBAAiB,GAAE,CAAE,CAAE,CAAC,cACpEpB,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAAEpD,IAAA,CAACqB,YAAY,GAAE,CAAE,CAAE,CAAC,cAC1DrB,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAAEpD,IAAA,CAACsB,YAAY,GAAE,CAAE,CAAE,CAAC,cAC1DtB,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAEpD,IAAA,CAACuB,eAAe,GAAE,CAAE,CAAE,CAAC,cAChEvB,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEpD,IAAA,CAACwB,cAAc,GAAE,CAAE,CAAE,CAAC,cAC7DxB,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,oBAAoB,CAACC,OAAO,cAAEpD,IAAA,CAACyB,kBAAkB,GAAE,CAAE,CAAE,CAAC,cAGpEzB,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,WAAW,CAACC,OAAO,cAAEpD,IAAA,CAACiC,yBAAyB,GAAE,CAAE,CAAE,CAAC,cAClEjC,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,oBAAoB,CAACC,OAAO,cAAEpD,IAAA,CAACkC,gBAAgB,GAAE,CAAE,CAAE,CAAC,cAClElC,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,uBAAuB,CAACC,OAAO,cAAEpD,IAAA,CAACmC,mBAAmB,GAAE,CAAE,CAAE,CAAC,cAGxEnC,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,cAAc,CAACC,OAAO,cAAEpD,IAAA,CAAC0B,UAAU,GAAE,CAAE,CAAE,CAAC,cAGtD1B,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,YAAY,CAACC,OAAO,cAAEpD,IAAA,CAAC2B,QAAQ,GAAE,CAAE,CAAE,CAAC,cAClD3B,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,mBAAmB,CAACC,OAAO,cAAEpD,IAAA,CAAC4B,gBAAgB,GAAE,CAAE,CAAE,CAAC,cACjE5B,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAAEpD,IAAA,CAAC6B,aAAa,GAAE,CAAE,CAAE,CAAC,cAC3D7B,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,iBAAiB,CAACC,OAAO,cAAEpD,IAAA,CAAC8B,cAAc,GAAE,CAAE,CAAE,CAAC,cAC7D9B,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,gBAAgB,CAACC,OAAO,cAClCpD,IAAA,CAACV,cAAc,EAACkE,kBAAkB,CAAC,YAAY,CAAAjB,QAAA,cAC7CvC,IAAA,CAAC+B,cAAc,GAAE,CAAC,CACJ,CACjB,CAAE,CAAC,cAGJ/B,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,eAAe,CAACC,OAAO,cAAEpD,IAAA,CAACF,YAAY,GAAE,CAAE,CAAE,CAAC,cACzDE,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,kBAAkB,CAACC,OAAO,cAAEpD,IAAA,CAACgC,eAAe,GAAE,CAAE,CAAE,CAAC,cAG/DhC,IAAA,CAAC9B,KAAK,EAACiF,IAAI,CAAC,GAAG,CAACC,OAAO,cAAEpD,IAAA,CAACqC,SAAS,GAAE,CAAE,CAAE,CAAC,EACrC,CAAC,EACF,CAAC,CACD,CAAC,CACL,CAAC,CACW,CAAC,CAEzB,CAAC,CAED,cAAe,CAAAC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}