{"ast": null, "code": "function memo(getDeps, fn, opts) {\n  let deps = opts.initialDeps ?? [];\n  let result;\n  function memoizedFunction() {\n    var _a, _b, _c, _d;\n    let depTime;\n    if (opts.key && ((_a = opts.debug) == null ? void 0 : _a.call(opts))) depTime = Date.now();\n    const newDeps = getDeps();\n    const depsChanged = newDeps.length !== deps.length || newDeps.some((dep, index) => deps[index] !== dep);\n    if (!depsChanged) {\n      return result;\n    }\n    deps = newDeps;\n    let resultTime;\n    if (opts.key && ((_b = opts.debug) == null ? void 0 : _b.call(opts))) resultTime = Date.now();\n    result = fn(...newDeps);\n    if (opts.key && ((_c = opts.debug) == null ? void 0 : _c.call(opts))) {\n      const depEndTime = Math.round((Date.now() - depTime) * 100) / 100;\n      const resultEndTime = Math.round((Date.now() - resultTime) * 100) / 100;\n      const resultFpsPercentage = resultEndTime / 16;\n      const pad = (str, num) => {\n        str = String(str);\n        while (str.length < num) {\n          str = \" \" + str;\n        }\n        return str;\n      };\n      console.info(`%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`, `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(0, Math.min(120 - 120 * resultFpsPercentage, 120))}deg 100% 31%);`, opts == null ? void 0 : opts.key);\n    }\n    (_d = opts == null ? void 0 : opts.onChange) == null ? void 0 : _d.call(opts, result);\n    return result;\n  }\n  memoizedFunction.updateDeps = newDeps => {\n    deps = newDeps;\n  };\n  return memoizedFunction;\n}\nfunction notUndefined(value, msg) {\n  if (value === void 0) {\n    throw new Error(`Unexpected undefined${msg ? `: ${msg}` : \"\"}`);\n  } else {\n    return value;\n  }\n}\nconst approxEqual = (a, b) => Math.abs(a - b) <= 1;\nconst debounce = (targetWindow, fn, ms) => {\n  let timeoutId;\n  return function (...args) {\n    targetWindow.clearTimeout(timeoutId);\n    timeoutId = targetWindow.setTimeout(() => fn.apply(this, args), ms);\n  };\n};\nexport { approxEqual, debounce, memo, notUndefined };", "map": {"version": 3, "names": ["memo", "getDeps", "fn", "opts", "deps", "initialDeps", "result", "memoizedFunction", "_a", "_b", "_c", "_d", "depTime", "key", "debug", "call", "Date", "now", "newDeps", "depsChanged", "length", "some", "dep", "index", "resultTime", "depEndTime", "Math", "round", "resultEndTime", "resultFpsPercentage", "pad", "str", "num", "String", "console", "info", "max", "min", "onChange", "updateDeps", "notUndefined", "value", "msg", "Error", "approxEqual", "a", "b", "abs", "debounce", "targetWindow", "ms", "timeoutId", "args", "clearTimeout", "setTimeout", "apply"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\frontend-web\\node_modules\\@tanstack\\virtual-core\\src\\utils.ts"], "sourcesContent": ["export type NoInfer<A extends any> = [A][A extends any ? 0 : never]\n\nexport type PartialKeys<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>\n\nexport function memo<TDeps extends ReadonlyArray<any>, TResult>(\n  getDeps: () => [...TDeps],\n  fn: (...args: NoInfer<[...TDeps]>) => TResult,\n  opts: {\n    key: false | string\n    debug?: () => boolean\n    onChange?: (result: TResult) => void\n    initialDeps?: TDeps\n  },\n) {\n  let deps = opts.initialDeps ?? []\n  let result: TResult | undefined\n\n  function memoizedFunction(): TResult {\n    let depTime: number\n    if (opts.key && opts.debug?.()) depTime = Date.now()\n\n    const newDeps = getDeps()\n\n    const depsChanged =\n      newDeps.length !== deps.length ||\n      newDeps.some((dep: any, index: number) => deps[index] !== dep)\n\n    if (!depsChanged) {\n      return result!\n    }\n\n    deps = newDeps\n\n    let resultTime: number\n    if (opts.key && opts.debug?.()) resultTime = Date.now()\n\n    result = fn(...newDeps)\n\n    if (opts.key && opts.debug?.()) {\n      const depEndTime = Math.round((Date.now() - depTime!) * 100) / 100\n      const resultEndTime = Math.round((Date.now() - resultTime!) * 100) / 100\n      const resultFpsPercentage = resultEndTime / 16\n\n      const pad = (str: number | string, num: number) => {\n        str = String(str)\n        while (str.length < num) {\n          str = ' ' + str\n        }\n        return str\n      }\n\n      console.info(\n        `%c⏱ ${pad(resultEndTime, 5)} /${pad(depEndTime, 5)} ms`,\n        `\n            font-size: .6rem;\n            font-weight: bold;\n            color: hsl(${Math.max(\n              0,\n              Math.min(120 - 120 * resultFpsPercentage, 120),\n            )}deg 100% 31%);`,\n        opts?.key,\n      )\n    }\n\n    opts?.onChange?.(result)\n\n    return result\n  }\n\n  // Attach updateDeps to the function itself\n  memoizedFunction.updateDeps = (newDeps: [...TDeps]) => {\n    deps = newDeps\n  }\n\n  return memoizedFunction\n}\n\nexport function notUndefined<T>(value: T | undefined, msg?: string): T {\n  if (value === undefined) {\n    throw new Error(`Unexpected undefined${msg ? `: ${msg}` : ''}`)\n  } else {\n    return value\n  }\n}\n\nexport const approxEqual = (a: number, b: number) => Math.abs(a - b) <= 1\n\nexport const debounce = (\n  targetWindow: Window & typeof globalThis,\n  fn: Function,\n  ms: number,\n) => {\n  let timeoutId: number\n  return function (this: any, ...args: Array<any>) {\n    targetWindow.clearTimeout(timeoutId)\n    timeoutId = targetWindow.setTimeout(() => fn.apply(this, args), ms)\n  }\n}\n"], "mappings": "AAIgB,SAAAA,KACdC,OAAA,EACAC,EAAA,EACAC,IAAA,EAMA;EACI,IAAAC,IAAA,GAAOD,IAAA,CAAKE,WAAA,IAAe,EAAC;EAC5B,IAAAC,MAAA;EAEJ,SAASC,iBAAA,EAA4B;IAbvB,IAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA,EAAAC,EAAA;IAcR,IAAAC,OAAA;IACJ,IAAIT,IAAA,CAAKU,GAAA,MAAOL,EAAA,GAAAL,IAAA,CAAKW,KAAA,KAAL,gBAAAN,EAAA,CAAAO,IAAA,CAAAZ,IAAA,IAAgBS,OAAA,GAAUI,IAAA,CAAKC,GAAA,CAAI;IAEnD,MAAMC,OAAA,GAAUjB,OAAA,CAAQ;IAExB,MAAMkB,WAAA,GACJD,OAAA,CAAQE,MAAA,KAAWhB,IAAA,CAAKgB,MAAA,IACxBF,OAAA,CAAQG,IAAA,CAAK,CAACC,GAAA,EAAUC,KAAA,KAAkBnB,IAAA,CAAKmB,KAAK,MAAMD,GAAG;IAE/D,IAAI,CAACH,WAAA,EAAa;MACT,OAAAb,MAAA;IAAA;IAGFF,IAAA,GAAAc,OAAA;IAEH,IAAAM,UAAA;IACJ,IAAIrB,IAAA,CAAKU,GAAA,MAAOJ,EAAA,GAAAN,IAAA,CAAKW,KAAA,KAAL,gBAAAL,EAAA,CAAAM,IAAA,CAAAZ,IAAA,IAAgBqB,UAAA,GAAaR,IAAA,CAAKC,GAAA,CAAI;IAE7CX,MAAA,GAAAJ,EAAA,CAAG,GAAGgB,OAAO;IAEtB,IAAIf,IAAA,CAAKU,GAAA,MAAOH,EAAA,GAAAP,IAAA,CAAKW,KAAA,KAAL,gBAAAJ,EAAA,CAAAK,IAAA,CAAAZ,IAAA,IAAgB;MACxB,MAAAsB,UAAA,GAAaC,IAAA,CAAKC,KAAA,EAAOX,IAAA,CAAKC,GAAA,KAAQL,OAAA,IAAY,GAAG,IAAI;MACzD,MAAAgB,aAAA,GAAgBF,IAAA,CAAKC,KAAA,EAAOX,IAAA,CAAKC,GAAA,KAAQO,UAAA,IAAe,GAAG,IAAI;MACrE,MAAMK,mBAAA,GAAsBD,aAAA,GAAgB;MAEtC,MAAAE,GAAA,GAAMA,CAACC,GAAA,EAAsBC,GAAA,KAAgB;QACjDD,GAAA,GAAME,MAAA,CAAOF,GAAG;QACT,OAAAA,GAAA,CAAIX,MAAA,GAASY,GAAA,EAAK;UACvBD,GAAA,GAAM,MAAMA,GAAA;QAAA;QAEP,OAAAA,GAAA;MACT;MAEQG,OAAA,CAAAC,IAAA,CACN,OAAOL,GAAA,CAAIF,aAAA,EAAe,CAAC,CAAC,KAAKE,GAAA,CAAIL,UAAA,EAAY,CAAC,CAAC,OACnD;AAAA;AAAA;AAAA,yBAGiBC,IAAA,CAAKU,GAAA,CAChB,GACAV,IAAA,CAAKW,GAAA,CAAI,MAAM,MAAMR,mBAAA,EAAqB,GAAG,CAC9C,mBACL1B,IAAA,oBAAAA,IAAA,CAAMU,GACR;IAAA;IAGF,CAAAF,EAAA,GAAAR,IAAA,oBAAAA,IAAA,CAAMmC,QAAA,KAAN,gBAAA3B,EAAA,CAAAI,IAAA,CAAAZ,IAAA,EAAiBG,MAAA;IAEV,OAAAA,MAAA;EAAA;EAIQC,gBAAA,CAAAgC,UAAA,GAAcrB,OAAA,IAAwB;IAC9Cd,IAAA,GAAAc,OAAA;EACT;EAEO,OAAAX,gBAAA;AACT;AAEgB,SAAAiC,aAAgBC,KAAA,EAAsBC,GAAA,EAAiB;EACrE,IAAID,KAAA,KAAU,QAAW;IACjB,UAAIE,KAAA,CAAM,uBAAuBD,GAAA,GAAM,KAAKA,GAAG,KAAK,EAAE,EAAE;EAAA,OACzD;IACE,OAAAD,KAAA;EAAA;AAEX;AAEa,MAAAG,WAAA,GAAcA,CAACC,CAAA,EAAWC,CAAA,KAAcpB,IAAA,CAAKqB,GAAA,CAAIF,CAAA,GAAIC,CAAC,KAAK;AAEjE,MAAME,QAAA,GAAWA,CACtBC,YAAA,EACA/C,EAAA,EACAgD,EAAA,KACG;EACC,IAAAC,SAAA;EACJ,OAAO,aAAwBC,IAAA,EAAkB;IAC/CH,YAAA,CAAaI,YAAA,CAAaF,SAAS;IACvBA,SAAA,GAAAF,YAAA,CAAaK,UAAA,CAAW,MAAMpD,EAAA,CAAGqD,KAAA,CAAM,MAAMH,IAAI,GAAGF,EAAE;EACpE;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}