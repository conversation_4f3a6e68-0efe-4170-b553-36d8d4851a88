{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const StatCard=_ref=>{let{name,value,icon:Icon,color,change,changeType='neutral',onClick}=_ref;return/*#__PURE__*/_jsx(\"div\",{className:\"card \".concat(onClick?'cursor-pointer hover:shadow-md transition-shadow':''),onClick:onClick,children:/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex-shrink-0\",children:/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 rounded-md \".concat(color,\" flex items-center justify-center\"),children:/*#__PURE__*/_jsx(Icon,{className:\"w-5 h-5 text-white\"})})}),/*#__PURE__*/_jsx(\"div\",{className:\"ml-5 w-0 flex-1\",children:/*#__PURE__*/_jsxs(\"dl\",{children:[/*#__PURE__*/_jsx(\"dt\",{className:\"text-sm font-medium text-gray-500 truncate\",children:name}),/*#__PURE__*/_jsxs(\"dd\",{className:\"flex items-baseline\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"text-2xl font-semibold text-gray-900\",children:value}),change&&/*#__PURE__*/_jsx(\"div\",{className:\"ml-2 flex items-baseline text-sm font-semibold \".concat(changeType==='positive'?'text-green-600':changeType==='negative'?'text-red-600':'text-gray-600'),children:change})]})]})})]})})});};export default StatCard;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "StatCard", "_ref", "name", "value", "icon", "Icon", "color", "change", "changeType", "onClick", "className", "concat", "children"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/components/dashboard/StatCard.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface StatCardProps {\n  name: string;\n  value: number | string;\n  icon: React.ComponentType<{ className?: string }>;\n  color: string;\n  change?: string;\n  changeType?: 'positive' | 'negative' | 'neutral';\n  onClick?: () => void;\n}\n\nconst StatCard: React.FC<StatCardProps> = ({\n  name,\n  value,\n  icon: Icon,\n  color,\n  change,\n  changeType = 'neutral',\n  onClick\n}) => {\n  return (\n    <div \n      className={`card ${onClick ? 'cursor-pointer hover:shadow-md transition-shadow' : ''}`}\n      onClick={onClick}\n    >\n      <div className=\"card-body\">\n        <div className=\"flex items-center\">\n          <div className=\"flex-shrink-0\">\n            <div className={`w-8 h-8 rounded-md ${color} flex items-center justify-center`}>\n              <Icon className=\"w-5 h-5 text-white\" />\n            </div>\n          </div>\n          <div className=\"ml-5 w-0 flex-1\">\n            <dl>\n              <dt className=\"text-sm font-medium text-gray-500 truncate\">\n                {name}\n              </dt>\n              <dd className=\"flex items-baseline\">\n                <div className=\"text-2xl font-semibold text-gray-900\">\n                  {value}\n                </div>\n                {change && (\n                  <div className={`ml-2 flex items-baseline text-sm font-semibold ${\n                    changeType === 'positive' ? 'text-green-600' : \n                    changeType === 'negative' ? 'text-red-600' : \n                    'text-gray-600'\n                  }`}>\n                    {change}\n                  </div>\n                )}\n              </dd>\n            </dl>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default StatCard;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAY1B,KAAM,CAAAC,QAAiC,CAAGC,IAAA,EAQpC,IARqC,CACzCC,IAAI,CACJC,KAAK,CACLC,IAAI,CAAEC,IAAI,CACVC,KAAK,CACLC,MAAM,CACNC,UAAU,CAAG,SAAS,CACtBC,OACF,CAAC,CAAAR,IAAA,CACC,mBACEJ,IAAA,QACEa,SAAS,SAAAC,MAAA,CAAUF,OAAO,CAAG,kDAAkD,CAAG,EAAE,CAAG,CACvFA,OAAO,CAAEA,OAAQ,CAAAG,QAAA,cAEjBf,IAAA,QAAKa,SAAS,CAAC,WAAW,CAAAE,QAAA,cACxBb,KAAA,QAAKW,SAAS,CAAC,mBAAmB,CAAAE,QAAA,eAChCf,IAAA,QAAKa,SAAS,CAAC,eAAe,CAAAE,QAAA,cAC5Bf,IAAA,QAAKa,SAAS,uBAAAC,MAAA,CAAwBL,KAAK,qCAAoC,CAAAM,QAAA,cAC7Ef,IAAA,CAACQ,IAAI,EAACK,SAAS,CAAC,oBAAoB,CAAE,CAAC,CACpC,CAAC,CACH,CAAC,cACNb,IAAA,QAAKa,SAAS,CAAC,iBAAiB,CAAAE,QAAA,cAC9Bb,KAAA,OAAAa,QAAA,eACEf,IAAA,OAAIa,SAAS,CAAC,4CAA4C,CAAAE,QAAA,CACvDV,IAAI,CACH,CAAC,cACLH,KAAA,OAAIW,SAAS,CAAC,qBAAqB,CAAAE,QAAA,eACjCf,IAAA,QAAKa,SAAS,CAAC,sCAAsC,CAAAE,QAAA,CAClDT,KAAK,CACH,CAAC,CACLI,MAAM,eACLV,IAAA,QAAKa,SAAS,mDAAAC,MAAA,CACZH,UAAU,GAAK,UAAU,CAAG,gBAAgB,CAC5CA,UAAU,GAAK,UAAU,CAAG,cAAc,CAC1C,eAAe,CACd,CAAAI,QAAA,CACAL,MAAM,CACJ,CACN,EACC,CAAC,EACH,CAAC,CACF,CAAC,EACH,CAAC,CACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAP,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}