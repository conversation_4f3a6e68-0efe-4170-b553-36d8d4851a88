require('dotenv').config();
const mongoose = require('mongoose');
const User = require('../src/models/User');
const logger = require('../src/utils/logger');

const seedUsers = async () => {
  try {
    // Connect to database
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      dbName: process.env.MONGODB_DB_NAME || 'AMPD_Live_Stock',
    });

    logger.info('Connected to MongoDB for seeding users');

    // Check if users already exist
    const existingUsers = await User.countDocuments();
    if (existingUsers > 0) {
      logger.info('Users already exist, skipping seed');
      process.exit(0);
    }

    // Create default users
    const defaultUsers = [
      {
        username: 'admin',
        email: '<EMAIL>',
        password: 'Admin@123',
        firstName: 'System',
        lastName: 'Administrator',
        role: 'admin',
        department: 'IT',
        phoneNumber: '+27123456789',
        isActive: true,
        emailVerified: true,
      },
      {
        username: 'manager',
        email: '<EMAIL>',
        password: 'Manager@123',
        firstName: 'Farm',
        lastName: 'Manager',
        role: 'manager',
        department: 'Operations',
        phoneNumber: '+27123456790',
        isActive: true,
        emailVerified: true,
      },
      {
        username: 'staff',
        email: '<EMAIL>',
        password: 'Staff@123',
        firstName: 'Farm',
        lastName: 'Worker',
        role: 'staff',
        department: 'Operations',
        phoneNumber: '+27123456791',
        isActive: true,
        emailVerified: true,
      },
      {
        username: 'vet',
        email: '<EMAIL>',
        password: 'Vet@123',
        firstName: 'Dr. John',
        lastName: 'Veterinarian',
        role: 'veterinarian',
        department: 'Health',
        phoneNumber: '+27123456792',
        isActive: true,
        emailVerified: true,
      },
    ];

    // Create users
    for (const userData of defaultUsers) {
      const user = new User(userData);
      await user.save();
      logger.info(`Created user: ${user.username} (${user.role})`);
    }

    logger.info('Default users created successfully');
    process.exit(0);
  } catch (error) {
    logger.error('Error seeding users:', error);
    process.exit(1);
  }
};

seedUsers();
