{"ast": null, "code": "import React from'react';import{Outlet}from'react-router-dom';import{useSelector}from'react-redux';import Header from'./Header';import Sidebar from'./Sidebar';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Layout=()=>{const{sidebarOpen}=useSelector(state=>state.ui);return/*#__PURE__*/_jsxs(\"div\",{className:\"min-h-screen bg-gray-50\",children:[/*#__PURE__*/_jsx(Sidebar,{}),/*#__PURE__*/_jsxs(\"div\",{className:\"transition-all duration-300 \".concat(sidebarOpen?'lg:ml-64':'lg:ml-16'),children:[/*#__PURE__*/_jsx(Header,{}),/*#__PURE__*/_jsx(\"main\",{className:\"py-6\",children:/*#__PURE__*/_jsx(\"div\",{className:\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",children:/*#__PURE__*/_jsx(Outlet,{})})})]}),sidebarOpen&&/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 z-40 lg:hidden\",children:/*#__PURE__*/_jsx(\"div\",{className:\"fixed inset-0 bg-gray-600 bg-opacity-75\"})})]});};export default Layout;", "map": {"version": 3, "names": ["React", "Outlet", "useSelector", "Header", "Sidebar", "jsx", "_jsx", "jsxs", "_jsxs", "Layout", "sidebarOpen", "state", "ui", "className", "children", "concat"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/components/layout/Layout.tsx"], "sourcesContent": ["import React from 'react';\nimport { Outlet } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { RootState } from '../../store/store';\n\nimport Header from './Header';\nimport Sidebar from './Sidebar';\n\nconst Layout: React.FC = () => {\n  const { sidebarOpen } = useSelector((state: RootState) => state.ui);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Sidebar */}\n      <Sidebar />\n\n      {/* Main content */}\n      <div\n        className={`transition-all duration-300 ${\n          sidebarOpen ? 'lg:ml-64' : 'lg:ml-16'\n        }`}\n      >\n        {/* Header */}\n        <Header />\n\n        {/* Page content */}\n        <main className=\"py-6\">\n          <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n            <Outlet />\n          </div>\n        </main>\n      </div>\n\n      {/* Mobile sidebar overlay */}\n      {sidebarOpen && (\n        <div className=\"fixed inset-0 z-40 lg:hidden\">\n          <div className=\"fixed inset-0 bg-gray-600 bg-opacity-75\" />\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default Layout;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,MAAM,KAAQ,kBAAkB,CACzC,OAASC,WAAW,KAAQ,aAAa,CAGzC,MAAO,CAAAC,MAAM,KAAM,UAAU,CAC7B,MAAO,CAAAC,OAAO,KAAM,WAAW,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEhC,KAAM,CAAAC,MAAgB,CAAGA,CAAA,GAAM,CAC7B,KAAM,CAAEC,WAAY,CAAC,CAAGR,WAAW,CAAES,KAAgB,EAAKA,KAAK,CAACC,EAAE,CAAC,CAEnE,mBACEJ,KAAA,QAAKK,SAAS,CAAC,yBAAyB,CAAAC,QAAA,eAEtCR,IAAA,CAACF,OAAO,GAAE,CAAC,cAGXI,KAAA,QACEK,SAAS,gCAAAE,MAAA,CACPL,WAAW,CAAG,UAAU,CAAG,UAAU,CACpC,CAAAI,QAAA,eAGHR,IAAA,CAACH,MAAM,GAAE,CAAC,cAGVG,IAAA,SAAMO,SAAS,CAAC,MAAM,CAAAC,QAAA,cACpBR,IAAA,QAAKO,SAAS,CAAC,wCAAwC,CAAAC,QAAA,cACrDR,IAAA,CAACL,MAAM,GAAE,CAAC,CACP,CAAC,CACF,CAAC,EACJ,CAAC,CAGLS,WAAW,eACVJ,IAAA,QAAKO,SAAS,CAAC,8BAA8B,CAAAC,QAAA,cAC3CR,IAAA,QAAKO,SAAS,CAAC,yCAAyC,CAAE,CAAC,CACxD,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAJ,MAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}