{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"id\", \"passive\"];\nimport c, { createContext as m, useContext as L, useMemo as f, useState as b } from \"react\";\nimport { useEvent as T } from '../../hooks/use-event.js';\nimport { useId as y } from '../../hooks/use-id.js';\nimport { useIsoMorphicEffect as E } from '../../hooks/use-iso-morphic-effect.js';\nimport { useSyncRefs as g } from '../../hooks/use-sync-refs.js';\nimport { forwardRefWithAs as x, render as P } from '../../utils/render.js';\nlet d = m(null);\nfunction u() {\n  let a = L(d);\n  if (a === null) {\n    let t = new Error(\"You used a <Label /> component, but it is not inside a relevant parent.\");\n    throw Error.captureStackTrace && Error.captureStackTrace(t, u), t;\n  }\n  return a;\n}\nfunction F() {\n  let [a, t] = b([]);\n  return [a.length > 0 ? a.join(\" \") : void 0, f(() => function (e) {\n    let s = T(r => (t(l => [...l, r]), () => t(l => {\n        let n = l.slice(),\n          p = n.indexOf(r);\n        return p !== -1 && n.splice(p, 1), n;\n      }))),\n      o = f(() => ({\n        register: s,\n        slot: e.slot,\n        name: e.name,\n        props: e.props\n      }), [s, e.slot, e.name, e.props]);\n    return c.createElement(d.Provider, {\n      value: o\n    }, e.children);\n  }, [t])];\n}\nlet A = \"label\";\nfunction h(a, t) {\n  let i = y(),\n    {\n      id: e = \"headlessui-label-\".concat(i),\n      passive: s = !1\n    } = a,\n    o = _objectWithoutProperties(a, _excluded),\n    r = u(),\n    l = g(t);\n  E(() => r.register(e), [e, r.register]);\n  let n = _objectSpread(_objectSpread({\n    ref: l\n  }, r.props), {}, {\n    id: e\n  });\n  return s && (\"onClick\" in n && (delete n.htmlFor, delete n.onClick), \"onClick\" in o && delete o.onClick), P({\n    ourProps: n,\n    theirProps: o,\n    slot: r.slot || {},\n    defaultTag: A,\n    name: r.name || \"Label\"\n  });\n}\nlet v = x(h),\n  B = Object.assign(v, {});\nexport { B as Label, F as useLabels };", "map": {"version": 3, "names": ["c", "createContext", "m", "useContext", "L", "useMemo", "f", "useState", "b", "useEvent", "T", "useId", "y", "useIsoMorphicEffect", "E", "useSyncRefs", "g", "forwardRefWithAs", "x", "render", "P", "d", "u", "a", "t", "Error", "captureStackTrace", "F", "length", "join", "e", "s", "r", "l", "n", "slice", "p", "indexOf", "splice", "o", "register", "slot", "name", "props", "createElement", "Provider", "value", "children", "A", "h", "i", "id", "concat", "passive", "_objectWithoutProperties", "_excluded", "_objectSpread", "ref", "htmlFor", "onClick", "ourProps", "theirProps", "defaultTag", "v", "B", "Object", "assign", "Label", "useLabels"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/components/label/label.js"], "sourcesContent": ["import c,{createContext as m,useContext as L,useMemo as f,useState as b}from\"react\";import{useEvent as T}from'../../hooks/use-event.js';import{useId as y}from'../../hooks/use-id.js';import{useIsoMorphicEffect as E}from'../../hooks/use-iso-morphic-effect.js';import{useSyncRefs as g}from'../../hooks/use-sync-refs.js';import{forwardRefWithAs as x,render as P}from'../../utils/render.js';let d=m(null);function u(){let a=L(d);if(a===null){let t=new Error(\"You used a <Label /> component, but it is not inside a relevant parent.\");throw Error.captureStackTrace&&Error.captureStackTrace(t,u),t}return a}function F(){let[a,t]=b([]);return[a.length>0?a.join(\" \"):void 0,f(()=>function(e){let s=T(r=>(t(l=>[...l,r]),()=>t(l=>{let n=l.slice(),p=n.indexOf(r);return p!==-1&&n.splice(p,1),n}))),o=f(()=>({register:s,slot:e.slot,name:e.name,props:e.props}),[s,e.slot,e.name,e.props]);return c.createElement(d.Provider,{value:o},e.children)},[t])]}let A=\"label\";function h(a,t){let i=y(),{id:e=`headlessui-label-${i}`,passive:s=!1,...o}=a,r=u(),l=g(t);E(()=>r.register(e),[e,r.register]);let n={ref:l,...r.props,id:e};return s&&(\"onClick\"in n&&(delete n.htmlFor,delete n.onClick),\"onClick\"in o&&delete o.onClick),P({ourProps:n,theirProps:o,slot:r.slot||{},defaultTag:A,name:r.name||\"Label\"})}let v=x(h),B=Object.assign(v,{});export{B as Label,F as useLabels};\n"], "mappings": ";;;AAAA,OAAOA,CAAC,IAAEC,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,OAAO,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,uCAAuC;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,QAAK,uBAAuB;AAAC,IAAIC,CAAC,GAACnB,CAAC,CAAC,IAAI,CAAC;AAAC,SAASoB,CAACA,CAAA,EAAE;EAAC,IAAIC,CAAC,GAACnB,CAAC,CAACiB,CAAC,CAAC;EAAC,IAAGE,CAAC,KAAG,IAAI,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAIC,KAAK,CAAC,yEAAyE,CAAC;IAAC,MAAMA,KAAK,CAACC,iBAAiB,IAAED,KAAK,CAACC,iBAAiB,CAACF,CAAC,EAACF,CAAC,CAAC,EAACE,CAAC;EAAA;EAAC,OAAOD,CAAC;AAAA;AAAC,SAASI,CAACA,CAAA,EAAE;EAAC,IAAG,CAACJ,CAAC,EAACC,CAAC,CAAC,GAAChB,CAAC,CAAC,EAAE,CAAC;EAAC,OAAM,CAACe,CAAC,CAACK,MAAM,GAAC,CAAC,GAACL,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC,GAAC,KAAK,CAAC,EAACvB,CAAC,CAAC,MAAI,UAASwB,CAAC,EAAC;IAAC,IAAIC,CAAC,GAACrB,CAAC,CAACsB,CAAC,KAAGR,CAAC,CAACS,CAAC,IAAE,CAAC,GAAGA,CAAC,EAACD,CAAC,CAAC,CAAC,EAAC,MAAIR,CAAC,CAACS,CAAC,IAAE;QAAC,IAAIC,CAAC,GAACD,CAAC,CAACE,KAAK,CAAC,CAAC;UAACC,CAAC,GAACF,CAAC,CAACG,OAAO,CAACL,CAAC,CAAC;QAAC,OAAOI,CAAC,KAAG,CAAC,CAAC,IAAEF,CAAC,CAACI,MAAM,CAACF,CAAC,EAAC,CAAC,CAAC,EAACF,CAAC;MAAA,CAAC,CAAC,CAAC,CAAC;MAACK,CAAC,GAACjC,CAAC,CAAC,OAAK;QAACkC,QAAQ,EAACT,CAAC;QAACU,IAAI,EAACX,CAAC,CAACW,IAAI;QAACC,IAAI,EAACZ,CAAC,CAACY,IAAI;QAACC,KAAK,EAACb,CAAC,CAACa;MAAK,CAAC,CAAC,EAAC,CAACZ,CAAC,EAACD,CAAC,CAACW,IAAI,EAACX,CAAC,CAACY,IAAI,EAACZ,CAAC,CAACa,KAAK,CAAC,CAAC;IAAC,OAAO3C,CAAC,CAAC4C,aAAa,CAACvB,CAAC,CAACwB,QAAQ,EAAC;MAACC,KAAK,EAACP;IAAC,CAAC,EAACT,CAAC,CAACiB,QAAQ,CAAC;EAAA,CAAC,EAAC,CAACvB,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIwB,CAAC,GAAC,OAAO;AAAC,SAASC,CAACA,CAAC1B,CAAC,EAACC,CAAC,EAAC;EAAC,IAAI0B,CAAC,GAACtC,CAAC,CAAC,CAAC;IAAC;MAACuC,EAAE,EAACrB,CAAC,uBAAAsB,MAAA,CAAqBF,CAAC,CAAE;MAACG,OAAO,EAACtB,CAAC,GAAC,CAAC;IAAM,CAAC,GAACR,CAAC;IAAJgB,CAAC,GAAAe,wBAAA,CAAE/B,CAAC,EAAAgC,SAAA;IAACvB,CAAC,GAACV,CAAC,CAAC,CAAC;IAACW,CAAC,GAACjB,CAAC,CAACQ,CAAC,CAAC;EAACV,CAAC,CAAC,MAAIkB,CAAC,CAACQ,QAAQ,CAACV,CAAC,CAAC,EAAC,CAACA,CAAC,EAACE,CAAC,CAACQ,QAAQ,CAAC,CAAC;EAAC,IAAIN,CAAC,GAAAsB,aAAA,CAAAA,aAAA;IAAEC,GAAG,EAACxB;EAAC,GAAID,CAAC,CAACW,KAAK;IAACQ,EAAE,EAACrB;EAAC,EAAC;EAAC,OAAOC,CAAC,KAAG,SAAS,IAAGG,CAAC,KAAG,OAAOA,CAAC,CAACwB,OAAO,EAAC,OAAOxB,CAAC,CAACyB,OAAO,CAAC,EAAC,SAAS,IAAGpB,CAAC,IAAE,OAAOA,CAAC,CAACoB,OAAO,CAAC,EAACvC,CAAC,CAAC;IAACwC,QAAQ,EAAC1B,CAAC;IAAC2B,UAAU,EAACtB,CAAC;IAACE,IAAI,EAACT,CAAC,CAACS,IAAI,IAAE,CAAC,CAAC;IAACqB,UAAU,EAACd,CAAC;IAACN,IAAI,EAACV,CAAC,CAACU,IAAI,IAAE;EAAO,CAAC,CAAC;AAAA;AAAC,IAAIqB,CAAC,GAAC7C,CAAC,CAAC+B,CAAC,CAAC;EAACe,CAAC,GAACC,MAAM,CAACC,MAAM,CAACH,CAAC,EAAC,CAAC,CAAC,CAAC;AAAC,SAAOC,CAAC,IAAIG,KAAK,EAACxC,CAAC,IAAIyC,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}