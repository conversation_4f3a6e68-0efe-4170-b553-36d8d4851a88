{"ast": null, "code": "import React from'react';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const BreedingPage=()=>{const{t}=useTranslation();return/*#__PURE__*/_jsx(\"div\",{className:\"space-y-6\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"card\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"card-header\",children:/*#__PURE__*/_jsx(\"h1\",{className:\"text-2xl font-bold text-gray-900\",children:t('breeding.title')})}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:/*#__PURE__*/_jsx(\"p\",{className:\"text-gray-600\",children:\"Breeding management features coming soon...\"})})]})});};export default BreedingPage;", "map": {"version": 3, "names": ["React", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "BreedingPage", "t", "className", "children"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/pages/breeding/BreedingPage.tsx"], "sourcesContent": ["import React from 'react';\nimport { useTranslation } from 'react-i18next';\n\nconst BreedingPage: React.FC = () => {\n  const { t } = useTranslation();\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"card\">\n        <div className=\"card-header\">\n          <h1 className=\"text-2xl font-bold text-gray-900\">\n            {t('breeding.title')}\n          </h1>\n        </div>\n        <div className=\"card-body\">\n          <p className=\"text-gray-600\">\n            Breeding management features coming soon...\n          </p>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default BreedingPage;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE/C,KAAM,CAAAC,YAAsB,CAAGA,CAAA,GAAM,CACnC,KAAM,CAAEC,CAAE,CAAC,CAAGN,cAAc,CAAC,CAAC,CAE9B,mBACEE,IAAA,QAAKK,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBJ,KAAA,QAAKG,SAAS,CAAC,MAAM,CAAAC,QAAA,eACnBN,IAAA,QAAKK,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1BN,IAAA,OAAIK,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAC7CF,CAAC,CAAC,gBAAgB,CAAC,CAClB,CAAC,CACF,CAAC,cACNJ,IAAA,QAAKK,SAAS,CAAC,WAAW,CAAAC,QAAA,cACxBN,IAAA,MAAGK,SAAS,CAAC,eAAe,CAAAC,QAAA,CAAC,6CAE7B,CAAG,CAAC,CACD,CAAC,EACH,CAAC,CACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}