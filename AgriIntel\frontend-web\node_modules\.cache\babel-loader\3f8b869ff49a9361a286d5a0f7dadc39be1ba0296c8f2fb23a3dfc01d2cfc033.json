{"ast": null, "code": "import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';\nconst initialState = {\n  animals: [],\n  selectedAnimal: null,\n  filters: {},\n  pagination: {\n    page: 1,\n    limit: 20,\n    total: 0,\n    totalPages: 0\n  },\n  isLoading: false,\n  error: null,\n  statistics: null\n};\n\n// Mock async thunks (replace with actual API calls)\nexport const fetchAnimals = createAsyncThunk('animals/fetchAnimals', async (params, {\n  rejectWithValue\n}) => {\n  try {\n    // Mock API call - replace with actual API\n    await new Promise(resolve => setTimeout(resolve, 1000));\n\n    // Mock data\n    const mockAnimals = [{\n      _id: '1',\n      tagNumber: 'C001',\n      name: 'Bessie',\n      species: 'cattle',\n      breed: 'Holstein',\n      gender: 'female',\n      dateOfBirth: '2022-03-15',\n      color: 'Black and White',\n      currentWeight: 450,\n      birthWeight: 35,\n      status: 'active',\n      healthStatus: 'healthy',\n      breedingStatus: 'available',\n      isActive: true,\n      createdBy: 'user1',\n      createdAt: '2023-01-01',\n      updatedAt: '2023-01-01',\n      age: 1,\n      ageInDays: 365,\n      latestWeight: 450\n    }, {\n      _id: '2',\n      tagNumber: 'S001',\n      name: 'Woolly',\n      species: 'sheep',\n      breed: 'Merino',\n      gender: 'male',\n      dateOfBirth: '2023-01-10',\n      color: 'White',\n      currentWeight: 75,\n      birthWeight: 4,\n      status: 'active',\n      healthStatus: 'healthy',\n      breedingStatus: 'available',\n      isActive: true,\n      createdBy: 'user1',\n      createdAt: '2023-01-10',\n      updatedAt: '2023-01-10',\n      age: 0,\n      ageInDays: 180,\n      latestWeight: 75\n    }];\n    return {\n      animals: mockAnimals,\n      pagination: {\n        page: params.page || 1,\n        limit: params.limit || 20,\n        total: mockAnimals.length,\n        totalPages: Math.ceil(mockAnimals.length / (params.limit || 20))\n      }\n    };\n  } catch (error) {\n    return rejectWithValue(error.message || 'Failed to fetch animals');\n  }\n});\nexport const fetchAnimalById = createAsyncThunk('animals/fetchAnimalById', async (id, {\n  rejectWithValue\n}) => {\n  try {\n    // Mock API call\n    await new Promise(resolve => setTimeout(resolve, 500));\n\n    // Mock data\n    const mockAnimal = {\n      _id: id,\n      tagNumber: 'C001',\n      name: 'Bessie',\n      species: 'cattle',\n      breed: 'Holstein',\n      gender: 'female',\n      dateOfBirth: '2022-03-15',\n      color: 'Black and White',\n      currentWeight: 450,\n      birthWeight: 35,\n      status: 'active',\n      healthStatus: 'healthy',\n      breedingStatus: 'available',\n      isActive: true,\n      createdBy: 'user1',\n      createdAt: '2023-01-01',\n      updatedAt: '2023-01-01',\n      age: 1,\n      ageInDays: 365,\n      latestWeight: 450\n    };\n    return mockAnimal;\n  } catch (error) {\n    return rejectWithValue(error.message || 'Failed to fetch animal');\n  }\n});\nexport const fetchAnimalStatistics = createAsyncThunk('animals/fetchStatistics', async (_, {\n  rejectWithValue\n}) => {\n  try {\n    // Mock API call\n    await new Promise(resolve => setTimeout(resolve, 500));\n    return {\n      total: 150,\n      bySpecies: {\n        cattle: 75,\n        sheep: 45,\n        goat: 20,\n        pig: 10\n      },\n      byStatus: {\n        active: 140,\n        sold: 5,\n        deceased: 3,\n        transferred: 2\n      },\n      byHealthStatus: {\n        healthy: 135,\n        sick: 8,\n        injured: 4,\n        recovering: 3\n      },\n      byBreedingStatus: {\n        available: 80,\n        pregnant: 25,\n        lactating: 30,\n        breeding: 10,\n        retired: 5\n      }\n    };\n  } catch (error) {\n    return rejectWithValue(error.message || 'Failed to fetch statistics');\n  }\n});\nconst animalSlice = createSlice({\n  name: 'animals',\n  initialState,\n  reducers: {\n    setFilters: (state, action) => {\n      state.filters = {\n        ...state.filters,\n        ...action.payload\n      };\n    },\n    clearFilters: state => {\n      state.filters = {};\n    },\n    setSelectedAnimal: (state, action) => {\n      state.selectedAnimal = action.payload;\n    },\n    setPagination: (state, action) => {\n      state.pagination = {\n        ...state.pagination,\n        ...action.payload\n      };\n    },\n    clearError: state => {\n      state.error = null;\n    }\n  },\n  extraReducers: builder => {\n    builder\n    // Fetch animals\n    .addCase(fetchAnimals.pending, state => {\n      state.isLoading = true;\n      state.error = null;\n    }).addCase(fetchAnimals.fulfilled, (state, action) => {\n      state.isLoading = false;\n      state.animals = action.payload.animals;\n      state.pagination = action.payload.pagination;\n      state.error = null;\n    }).addCase(fetchAnimals.rejected, (state, action) => {\n      state.isLoading = false;\n      state.error = action.payload;\n    })\n\n    // Fetch animal by ID\n    .addCase(fetchAnimalById.pending, state => {\n      state.isLoading = true;\n      state.error = null;\n    }).addCase(fetchAnimalById.fulfilled, (state, action) => {\n      state.isLoading = false;\n      state.selectedAnimal = action.payload;\n      state.error = null;\n    }).addCase(fetchAnimalById.rejected, (state, action) => {\n      state.isLoading = false;\n      state.error = action.payload;\n    })\n\n    // Fetch statistics\n    .addCase(fetchAnimalStatistics.fulfilled, (state, action) => {\n      state.statistics = action.payload;\n    });\n  }\n});\nexport const {\n  setFilters,\n  clearFilters,\n  setSelectedAnimal,\n  setPagination,\n  clearError\n} = animalSlice.actions;\nexport default animalSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "createAsyncThunk", "initialState", "animals", "selectedAnimal", "filters", "pagination", "page", "limit", "total", "totalPages", "isLoading", "error", "statistics", "fetchAnimals", "params", "rejectWithValue", "Promise", "resolve", "setTimeout", "mockAnimals", "_id", "tagNumber", "name", "species", "breed", "gender", "dateOfBirth", "color", "currentWeight", "birthWeight", "status", "healthStatus", "breedingStatus", "isActive", "created<PERSON>y", "createdAt", "updatedAt", "age", "ageInDays", "latestWeight", "length", "Math", "ceil", "message", "fetchAnimalById", "id", "mockAnimal", "fetchAnimalStatistics", "_", "bySpecies", "cattle", "sheep", "goat", "pig", "byStatus", "active", "sold", "deceased", "transferred", "byHealthStatus", "healthy", "sick", "injured", "recovering", "byBreedingStatus", "available", "pregnant", "lactating", "breeding", "retired", "animalSlice", "reducers", "setFilters", "state", "action", "payload", "clearFilters", "setSelectedAnimal", "setPagination", "clearError", "extraReducers", "builder", "addCase", "pending", "fulfilled", "rejected", "actions", "reducer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/store/slices/animalSlice.ts"], "sourcesContent": ["import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';\n\nexport interface Animal {\n  _id: string;\n  tagNumber: string;\n  name?: string;\n  species: 'cattle' | 'sheep' | 'goat' | 'pig' | 'chicken' | 'horse' | 'other';\n  breed: string;\n  gender: 'male' | 'female';\n  dateOfBirth: string;\n  color?: string;\n  markings?: string;\n  currentWeight?: number;\n  birthWeight?: number;\n  rfidTag?: string;\n  earTagNumber?: string;\n  microchipId?: string;\n  sire?: string;\n  dam?: string;\n  status: 'active' | 'sold' | 'deceased' | 'transferred' | 'quarantine';\n  location?: {\n    paddock?: string;\n    barn?: string;\n    coordinates?: {\n      latitude: number;\n      longitude: number;\n    };\n  };\n  healthStatus: 'healthy' | 'sick' | 'injured' | 'recovering' | 'quarantine';\n  lastHealthCheck?: string;\n  nextHealthCheck?: string;\n  breedingStatus: 'available' | 'pregnant' | 'lactating' | 'breeding' | 'retired';\n  lastBreedingDate?: string;\n  expectedDueDate?: string;\n  productionType?: 'meat' | 'milk' | 'eggs' | 'wool' | 'breeding' | 'work' | 'show';\n  productionRecords?: Array<{\n    date: string;\n    type: 'milk' | 'eggs' | 'wool' | 'weight';\n    quantity: number;\n    unit: string;\n    notes?: string;\n  }>;\n  weightHistory?: Array<{\n    date: string;\n    weight: number;\n    measuredBy: string;\n    notes?: string;\n  }>;\n  purchasePrice?: number;\n  purchaseDate?: string;\n  currentValue?: number;\n  images?: Array<{\n    url: string;\n    caption?: string;\n    uploadDate: string;\n  }>;\n  documents?: Array<{\n    name: string;\n    url: string;\n    type: string;\n    uploadDate: string;\n  }>;\n  notes?: string;\n  isActive: boolean;\n  createdBy: string;\n  updatedBy?: string;\n  createdAt: string;\n  updatedAt: string;\n  age?: number;\n  ageInDays?: number;\n  latestWeight?: number;\n}\n\nexport interface AnimalFilters {\n  species?: string;\n  breed?: string;\n  gender?: string;\n  status?: string;\n  healthStatus?: string;\n  breedingStatus?: string;\n  location?: string;\n  ageRange?: {\n    min?: number;\n    max?: number;\n  };\n  weightRange?: {\n    min?: number;\n    max?: number;\n  };\n  search?: string;\n}\n\nexport interface AnimalState {\n  animals: Animal[];\n  selectedAnimal: Animal | null;\n  filters: AnimalFilters;\n  pagination: {\n    page: number;\n    limit: number;\n    total: number;\n    totalPages: number;\n  };\n  isLoading: boolean;\n  error: string | null;\n  statistics: {\n    total: number;\n    bySpecies: Record<string, number>;\n    byStatus: Record<string, number>;\n    byHealthStatus: Record<string, number>;\n    byBreedingStatus: Record<string, number>;\n  } | null;\n}\n\nconst initialState: AnimalState = {\n  animals: [],\n  selectedAnimal: null,\n  filters: {},\n  pagination: {\n    page: 1,\n    limit: 20,\n    total: 0,\n    totalPages: 0,\n  },\n  isLoading: false,\n  error: null,\n  statistics: null,\n};\n\n// Mock async thunks (replace with actual API calls)\nexport const fetchAnimals = createAsyncThunk(\n  'animals/fetchAnimals',\n  async (params: { page?: number; limit?: number; filters?: AnimalFilters }, { rejectWithValue }) => {\n    try {\n      // Mock API call - replace with actual API\n      await new Promise(resolve => setTimeout(resolve, 1000));\n      \n      // Mock data\n      const mockAnimals: Animal[] = [\n        {\n          _id: '1',\n          tagNumber: 'C001',\n          name: 'Bessie',\n          species: 'cattle',\n          breed: 'Holstein',\n          gender: 'female',\n          dateOfBirth: '2022-03-15',\n          color: 'Black and White',\n          currentWeight: 450,\n          birthWeight: 35,\n          status: 'active',\n          healthStatus: 'healthy',\n          breedingStatus: 'available',\n          isActive: true,\n          createdBy: 'user1',\n          createdAt: '2023-01-01',\n          updatedAt: '2023-01-01',\n          age: 1,\n          ageInDays: 365,\n          latestWeight: 450,\n        },\n        {\n          _id: '2',\n          tagNumber: 'S001',\n          name: 'Woolly',\n          species: 'sheep',\n          breed: 'Merino',\n          gender: 'male',\n          dateOfBirth: '2023-01-10',\n          color: 'White',\n          currentWeight: 75,\n          birthWeight: 4,\n          status: 'active',\n          healthStatus: 'healthy',\n          breedingStatus: 'available',\n          isActive: true,\n          createdBy: 'user1',\n          createdAt: '2023-01-10',\n          updatedAt: '2023-01-10',\n          age: 0,\n          ageInDays: 180,\n          latestWeight: 75,\n        },\n      ];\n      \n      return {\n        animals: mockAnimals,\n        pagination: {\n          page: params.page || 1,\n          limit: params.limit || 20,\n          total: mockAnimals.length,\n          totalPages: Math.ceil(mockAnimals.length / (params.limit || 20)),\n        },\n      };\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to fetch animals');\n    }\n  }\n);\n\nexport const fetchAnimalById = createAsyncThunk(\n  'animals/fetchAnimalById',\n  async (id: string, { rejectWithValue }) => {\n    try {\n      // Mock API call\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      // Mock data\n      const mockAnimal: Animal = {\n        _id: id,\n        tagNumber: 'C001',\n        name: 'Bessie',\n        species: 'cattle',\n        breed: 'Holstein',\n        gender: 'female',\n        dateOfBirth: '2022-03-15',\n        color: 'Black and White',\n        currentWeight: 450,\n        birthWeight: 35,\n        status: 'active',\n        healthStatus: 'healthy',\n        breedingStatus: 'available',\n        isActive: true,\n        createdBy: 'user1',\n        createdAt: '2023-01-01',\n        updatedAt: '2023-01-01',\n        age: 1,\n        ageInDays: 365,\n        latestWeight: 450,\n      };\n      \n      return mockAnimal;\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to fetch animal');\n    }\n  }\n);\n\nexport const fetchAnimalStatistics = createAsyncThunk(\n  'animals/fetchStatistics',\n  async (_, { rejectWithValue }) => {\n    try {\n      // Mock API call\n      await new Promise(resolve => setTimeout(resolve, 500));\n      \n      return {\n        total: 150,\n        bySpecies: {\n          cattle: 75,\n          sheep: 45,\n          goat: 20,\n          pig: 10,\n        },\n        byStatus: {\n          active: 140,\n          sold: 5,\n          deceased: 3,\n          transferred: 2,\n        },\n        byHealthStatus: {\n          healthy: 135,\n          sick: 8,\n          injured: 4,\n          recovering: 3,\n        },\n        byBreedingStatus: {\n          available: 80,\n          pregnant: 25,\n          lactating: 30,\n          breeding: 10,\n          retired: 5,\n        },\n      };\n    } catch (error: any) {\n      return rejectWithValue(error.message || 'Failed to fetch statistics');\n    }\n  }\n);\n\nconst animalSlice = createSlice({\n  name: 'animals',\n  initialState,\n  reducers: {\n    setFilters: (state, action: PayloadAction<AnimalFilters>) => {\n      state.filters = { ...state.filters, ...action.payload };\n    },\n    clearFilters: (state) => {\n      state.filters = {};\n    },\n    setSelectedAnimal: (state, action: PayloadAction<Animal | null>) => {\n      state.selectedAnimal = action.payload;\n    },\n    setPagination: (state, action: PayloadAction<Partial<typeof initialState.pagination>>) => {\n      state.pagination = { ...state.pagination, ...action.payload };\n    },\n    clearError: (state) => {\n      state.error = null;\n    },\n  },\n  extraReducers: (builder) => {\n    builder\n      // Fetch animals\n      .addCase(fetchAnimals.pending, (state) => {\n        state.isLoading = true;\n        state.error = null;\n      })\n      .addCase(fetchAnimals.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.animals = action.payload.animals;\n        state.pagination = action.payload.pagination;\n        state.error = null;\n      })\n      .addCase(fetchAnimals.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      })\n      \n      // Fetch animal by ID\n      .addCase(fetchAnimalById.pending, (state) => {\n        state.isLoading = true;\n        state.error = null;\n      })\n      .addCase(fetchAnimalById.fulfilled, (state, action) => {\n        state.isLoading = false;\n        state.selectedAnimal = action.payload;\n        state.error = null;\n      })\n      .addCase(fetchAnimalById.rejected, (state, action) => {\n        state.isLoading = false;\n        state.error = action.payload as string;\n      })\n      \n      // Fetch statistics\n      .addCase(fetchAnimalStatistics.fulfilled, (state, action) => {\n        state.statistics = action.payload;\n      });\n  },\n});\n\nexport const { setFilters, clearFilters, setSelectedAnimal, setPagination, clearError } = animalSlice.actions;\nexport default animalSlice.reducer;\n"], "mappings": "AAAA,SAASA,WAAW,EAAEC,gBAAgB,QAAuB,kBAAkB;AAiH/E,MAAMC,YAAyB,GAAG;EAChCC,OAAO,EAAE,EAAE;EACXC,cAAc,EAAE,IAAI;EACpBC,OAAO,EAAE,CAAC,CAAC;EACXC,UAAU,EAAE;IACVC,IAAI,EAAE,CAAC;IACPC,KAAK,EAAE,EAAE;IACTC,KAAK,EAAE,CAAC;IACRC,UAAU,EAAE;EACd,CAAC;EACDC,SAAS,EAAE,KAAK;EAChBC,KAAK,EAAE,IAAI;EACXC,UAAU,EAAE;AACd,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAGb,gBAAgB,CAC1C,sBAAsB,EACtB,OAAOc,MAAkE,EAAE;EAAEC;AAAgB,CAAC,KAAK;EACjG,IAAI;IACF;IACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;;IAEvD;IACA,MAAME,WAAqB,GAAG,CAC5B;MACEC,GAAG,EAAE,GAAG;MACRC,SAAS,EAAE,MAAM;MACjBC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAE,UAAU;MACjBC,MAAM,EAAE,QAAQ;MAChBC,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAE,iBAAiB;MACxBC,aAAa,EAAE,GAAG;MAClBC,WAAW,EAAE,EAAE;MACfC,MAAM,EAAE,QAAQ;MAChBC,YAAY,EAAE,SAAS;MACvBC,cAAc,EAAE,WAAW;MAC3BC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,OAAO;MAClBC,SAAS,EAAE,YAAY;MACvBC,SAAS,EAAE,YAAY;MACvBC,GAAG,EAAE,CAAC;MACNC,SAAS,EAAE,GAAG;MACdC,YAAY,EAAE;IAChB,CAAC,EACD;MACEnB,GAAG,EAAE,GAAG;MACRC,SAAS,EAAE,MAAM;MACjBC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,OAAO;MAChBC,KAAK,EAAE,QAAQ;MACfC,MAAM,EAAE,MAAM;MACdC,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAE,OAAO;MACdC,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE,CAAC;MACdC,MAAM,EAAE,QAAQ;MAChBC,YAAY,EAAE,SAAS;MACvBC,cAAc,EAAE,WAAW;MAC3BC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,OAAO;MAClBC,SAAS,EAAE,YAAY;MACvBC,SAAS,EAAE,YAAY;MACvBC,GAAG,EAAE,CAAC;MACNC,SAAS,EAAE,GAAG;MACdC,YAAY,EAAE;IAChB,CAAC,CACF;IAED,OAAO;MACLrC,OAAO,EAAEiB,WAAW;MACpBd,UAAU,EAAE;QACVC,IAAI,EAAEQ,MAAM,CAACR,IAAI,IAAI,CAAC;QACtBC,KAAK,EAAEO,MAAM,CAACP,KAAK,IAAI,EAAE;QACzBC,KAAK,EAAEW,WAAW,CAACqB,MAAM;QACzB/B,UAAU,EAAEgC,IAAI,CAACC,IAAI,CAACvB,WAAW,CAACqB,MAAM,IAAI1B,MAAM,CAACP,KAAK,IAAI,EAAE,CAAC;MACjE;IACF,CAAC;EACH,CAAC,CAAC,OAAOI,KAAU,EAAE;IACnB,OAAOI,eAAe,CAACJ,KAAK,CAACgC,OAAO,IAAI,yBAAyB,CAAC;EACpE;AACF,CACF,CAAC;AAED,OAAO,MAAMC,eAAe,GAAG5C,gBAAgB,CAC7C,yBAAyB,EACzB,OAAO6C,EAAU,EAAE;EAAE9B;AAAgB,CAAC,KAAK;EACzC,IAAI;IACF;IACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;;IAEtD;IACA,MAAM6B,UAAkB,GAAG;MACzB1B,GAAG,EAAEyB,EAAE;MACPxB,SAAS,EAAE,MAAM;MACjBC,IAAI,EAAE,QAAQ;MACdC,OAAO,EAAE,QAAQ;MACjBC,KAAK,EAAE,UAAU;MACjBC,MAAM,EAAE,QAAQ;MAChBC,WAAW,EAAE,YAAY;MACzBC,KAAK,EAAE,iBAAiB;MACxBC,aAAa,EAAE,GAAG;MAClBC,WAAW,EAAE,EAAE;MACfC,MAAM,EAAE,QAAQ;MAChBC,YAAY,EAAE,SAAS;MACvBC,cAAc,EAAE,WAAW;MAC3BC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,OAAO;MAClBC,SAAS,EAAE,YAAY;MACvBC,SAAS,EAAE,YAAY;MACvBC,GAAG,EAAE,CAAC;MACNC,SAAS,EAAE,GAAG;MACdC,YAAY,EAAE;IAChB,CAAC;IAED,OAAOO,UAAU;EACnB,CAAC,CAAC,OAAOnC,KAAU,EAAE;IACnB,OAAOI,eAAe,CAACJ,KAAK,CAACgC,OAAO,IAAI,wBAAwB,CAAC;EACnE;AACF,CACF,CAAC;AAED,OAAO,MAAMI,qBAAqB,GAAG/C,gBAAgB,CACnD,yBAAyB,EACzB,OAAOgD,CAAC,EAAE;EAAEjC;AAAgB,CAAC,KAAK;EAChC,IAAI;IACF;IACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,GAAG,CAAC,CAAC;IAEtD,OAAO;MACLT,KAAK,EAAE,GAAG;MACVyC,SAAS,EAAE;QACTC,MAAM,EAAE,EAAE;QACVC,KAAK,EAAE,EAAE;QACTC,IAAI,EAAE,EAAE;QACRC,GAAG,EAAE;MACP,CAAC;MACDC,QAAQ,EAAE;QACRC,MAAM,EAAE,GAAG;QACXC,IAAI,EAAE,CAAC;QACPC,QAAQ,EAAE,CAAC;QACXC,WAAW,EAAE;MACf,CAAC;MACDC,cAAc,EAAE;QACdC,OAAO,EAAE,GAAG;QACZC,IAAI,EAAE,CAAC;QACPC,OAAO,EAAE,CAAC;QACVC,UAAU,EAAE;MACd,CAAC;MACDC,gBAAgB,EAAE;QAChBC,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZC,SAAS,EAAE,EAAE;QACbC,QAAQ,EAAE,EAAE;QACZC,OAAO,EAAE;MACX;IACF,CAAC;EACH,CAAC,CAAC,OAAO1D,KAAU,EAAE;IACnB,OAAOI,eAAe,CAACJ,KAAK,CAACgC,OAAO,IAAI,4BAA4B,CAAC;EACvE;AACF,CACF,CAAC;AAED,MAAM2B,WAAW,GAAGvE,WAAW,CAAC;EAC9BuB,IAAI,EAAE,SAAS;EACfrB,YAAY;EACZsE,QAAQ,EAAE;IACRC,UAAU,EAAEA,CAACC,KAAK,EAAEC,MAAoC,KAAK;MAC3DD,KAAK,CAACrE,OAAO,GAAG;QAAE,GAAGqE,KAAK,CAACrE,OAAO;QAAE,GAAGsE,MAAM,CAACC;MAAQ,CAAC;IACzD,CAAC;IACDC,YAAY,EAAGH,KAAK,IAAK;MACvBA,KAAK,CAACrE,OAAO,GAAG,CAAC,CAAC;IACpB,CAAC;IACDyE,iBAAiB,EAAEA,CAACJ,KAAK,EAAEC,MAAoC,KAAK;MAClED,KAAK,CAACtE,cAAc,GAAGuE,MAAM,CAACC,OAAO;IACvC,CAAC;IACDG,aAAa,EAAEA,CAACL,KAAK,EAAEC,MAA8D,KAAK;MACxFD,KAAK,CAACpE,UAAU,GAAG;QAAE,GAAGoE,KAAK,CAACpE,UAAU;QAAE,GAAGqE,MAAM,CAACC;MAAQ,CAAC;IAC/D,CAAC;IACDI,UAAU,EAAGN,KAAK,IAAK;MACrBA,KAAK,CAAC9D,KAAK,GAAG,IAAI;IACpB;EACF,CAAC;EACDqE,aAAa,EAAGC,OAAO,IAAK;IAC1BA;IACE;IAAA,CACCC,OAAO,CAACrE,YAAY,CAACsE,OAAO,EAAGV,KAAK,IAAK;MACxCA,KAAK,CAAC/D,SAAS,GAAG,IAAI;MACtB+D,KAAK,CAAC9D,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACDuE,OAAO,CAACrE,YAAY,CAACuE,SAAS,EAAE,CAACX,KAAK,EAAEC,MAAM,KAAK;MAClDD,KAAK,CAAC/D,SAAS,GAAG,KAAK;MACvB+D,KAAK,CAACvE,OAAO,GAAGwE,MAAM,CAACC,OAAO,CAACzE,OAAO;MACtCuE,KAAK,CAACpE,UAAU,GAAGqE,MAAM,CAACC,OAAO,CAACtE,UAAU;MAC5CoE,KAAK,CAAC9D,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACDuE,OAAO,CAACrE,YAAY,CAACwE,QAAQ,EAAE,CAACZ,KAAK,EAAEC,MAAM,KAAK;MACjDD,KAAK,CAAC/D,SAAS,GAAG,KAAK;MACvB+D,KAAK,CAAC9D,KAAK,GAAG+D,MAAM,CAACC,OAAiB;IACxC,CAAC;;IAED;IAAA,CACCO,OAAO,CAACtC,eAAe,CAACuC,OAAO,EAAGV,KAAK,IAAK;MAC3CA,KAAK,CAAC/D,SAAS,GAAG,IAAI;MACtB+D,KAAK,CAAC9D,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACDuE,OAAO,CAACtC,eAAe,CAACwC,SAAS,EAAE,CAACX,KAAK,EAAEC,MAAM,KAAK;MACrDD,KAAK,CAAC/D,SAAS,GAAG,KAAK;MACvB+D,KAAK,CAACtE,cAAc,GAAGuE,MAAM,CAACC,OAAO;MACrCF,KAAK,CAAC9D,KAAK,GAAG,IAAI;IACpB,CAAC,CAAC,CACDuE,OAAO,CAACtC,eAAe,CAACyC,QAAQ,EAAE,CAACZ,KAAK,EAAEC,MAAM,KAAK;MACpDD,KAAK,CAAC/D,SAAS,GAAG,KAAK;MACvB+D,KAAK,CAAC9D,KAAK,GAAG+D,MAAM,CAACC,OAAiB;IACxC,CAAC;;IAED;IAAA,CACCO,OAAO,CAACnC,qBAAqB,CAACqC,SAAS,EAAE,CAACX,KAAK,EAAEC,MAAM,KAAK;MAC3DD,KAAK,CAAC7D,UAAU,GAAG8D,MAAM,CAACC,OAAO;IACnC,CAAC,CAAC;EACN;AACF,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEH,UAAU;EAAEI,YAAY;EAAEC,iBAAiB;EAAEC,aAAa;EAAEC;AAAW,CAAC,GAAGT,WAAW,CAACgB,OAAO;AAC7G,eAAehB,WAAW,CAACiB,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}