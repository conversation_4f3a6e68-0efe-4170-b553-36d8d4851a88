{"ast": null, "code": "import React from'react';import{Navigate,useLocation}from'react-router-dom';import{useSelector}from'react-redux';import{jsx as _jsx,Fragment as _Fragment}from\"react/jsx-runtime\";const ProtectedRoute=_ref=>{let{children,requiredRole,requiredPermission}=_ref;const location=useLocation();const{isAuthenticated,user}=useSelector(state=>state.auth);// Check if user is authenticated\nif(!isAuthenticated){return/*#__PURE__*/_jsx(Navigate,{to:\"/login\",state:{from:location},replace:true});}// Check role requirements\nif(requiredRole&&user&&!requiredRole.includes(user.role)){return/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true});}// Check permission requirements\nif(requiredPermission&&user){// Admin has all permissions\nif(user.role!=='admin'){var _user$permissions;// Check if user has the required permission\nconst hasPermission=(_user$permissions=user.permissions)===null||_user$permissions===void 0?void 0:_user$permissions.some(permission=>permission.module===requiredPermission.module&&permission.actions.includes(requiredPermission.action));if(!hasPermission){return/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true});}}}return/*#__PURE__*/_jsx(_Fragment,{children:children});};export default ProtectedRoute;", "map": {"version": 3, "names": ["React", "Navigate", "useLocation", "useSelector", "jsx", "_jsx", "Fragment", "_Fragment", "ProtectedRoute", "_ref", "children", "requiredRole", "requiredPermission", "location", "isAuthenticated", "user", "state", "auth", "to", "from", "replace", "includes", "role", "_user$permissions", "hasPermission", "permissions", "some", "permission", "module", "actions", "action"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/components/auth/ProtectedRoute.tsx"], "sourcesContent": ["import React from 'react';\nimport { Navigate, useLocation } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { RootState } from '../../store/store';\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requiredRole?: string[];\n  requiredPermission?: {\n    module: 'animals' | 'breeding' | 'health' | 'feeding' | 'financial' | 'compliance' | 'inventory' | 'analytics' | 'settings' | 'users';\n    action: 'create' | 'read' | 'update' | 'delete' | 'export' | 'import';\n  };\n}\n\nconst ProtectedRoute: React.FC<ProtectedRouteProps> = ({\n  children,\n  requiredRole,\n  requiredPermission,\n}) => {\n  const location = useLocation();\n  const { isAuthenticated, user } = useSelector((state: RootState) => state.auth);\n\n  // Check if user is authenticated\n  if (!isAuthenticated) {\n    return <Navigate to=\"/login\" state={{ from: location }} replace />;\n  }\n\n  // Check role requirements\n  if (requiredRole && user && !requiredRole.includes(user.role)) {\n    return <Navigate to=\"/dashboard\" replace />;\n  }\n\n  // Check permission requirements\n  if (requiredPermission && user) {\n    // Admin has all permissions\n    if (user.role !== 'admin') {\n      // Check if user has the required permission\n      const hasPermission = user.permissions?.some(\n        (permission) =>\n          permission.module === requiredPermission.module &&\n          permission.actions.includes(requiredPermission.action)\n      );\n\n      if (!hasPermission) {\n        return <Navigate to=\"/dashboard\" replace />;\n      }\n    }\n  }\n\n  return <>{children}</>;\n};\n\nexport default ProtectedRoute;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,QAAQ,CAAEC,WAAW,KAAQ,kBAAkB,CACxD,OAASC,WAAW,KAAQ,aAAa,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,QAAA,IAAAC,SAAA,yBAY1C,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAIhD,IAJiD,CACrDC,QAAQ,CACRC,YAAY,CACZC,kBACF,CAAC,CAAAH,IAAA,CACC,KAAM,CAAAI,QAAQ,CAAGX,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEY,eAAe,CAAEC,IAAK,CAAC,CAAGZ,WAAW,CAAEa,KAAgB,EAAKA,KAAK,CAACC,IAAI,CAAC,CAE/E;AACA,GAAI,CAACH,eAAe,CAAE,CACpB,mBAAOT,IAAA,CAACJ,QAAQ,EAACiB,EAAE,CAAC,QAAQ,CAACF,KAAK,CAAE,CAAEG,IAAI,CAAEN,QAAS,CAAE,CAACO,OAAO,MAAE,CAAC,CACpE,CAEA;AACA,GAAIT,YAAY,EAAII,IAAI,EAAI,CAACJ,YAAY,CAACU,QAAQ,CAACN,IAAI,CAACO,IAAI,CAAC,CAAE,CAC7D,mBAAOjB,IAAA,CAACJ,QAAQ,EAACiB,EAAE,CAAC,YAAY,CAACE,OAAO,MAAE,CAAC,CAC7C,CAEA;AACA,GAAIR,kBAAkB,EAAIG,IAAI,CAAE,CAC9B;AACA,GAAIA,IAAI,CAACO,IAAI,GAAK,OAAO,CAAE,KAAAC,iBAAA,CACzB;AACA,KAAM,CAAAC,aAAa,EAAAD,iBAAA,CAAGR,IAAI,CAACU,WAAW,UAAAF,iBAAA,iBAAhBA,iBAAA,CAAkBG,IAAI,CACzCC,UAAU,EACTA,UAAU,CAACC,MAAM,GAAKhB,kBAAkB,CAACgB,MAAM,EAC/CD,UAAU,CAACE,OAAO,CAACR,QAAQ,CAACT,kBAAkB,CAACkB,MAAM,CACzD,CAAC,CAED,GAAI,CAACN,aAAa,CAAE,CAClB,mBAAOnB,IAAA,CAACJ,QAAQ,EAACiB,EAAE,CAAC,YAAY,CAACE,OAAO,MAAE,CAAC,CAC7C,CACF,CACF,CAEA,mBAAOf,IAAA,CAAAE,SAAA,EAAAG,QAAA,CAAGA,QAAQ,CAAG,CAAC,CACxB,CAAC,CAED,cAAe,CAAAF,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}