{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ProfilingLevelOperation = void 0;\nconst error_1 = require(\"../error\");\nconst command_1 = require(\"./command\");\n/** @internal */\nclass ProfilingLevelOperation extends command_1.CommandOperation {\n  constructor(db, options) {\n    super(db, options);\n    this.options = options;\n  }\n  get commandName() {\n    return 'profile';\n  }\n  async execute(server, session, timeoutContext) {\n    const doc = await super.executeCommand(server, session, {\n      profile: -1\n    }, timeoutContext);\n    if (doc.ok === 1) {\n      const was = doc.was;\n      if (was === 0) return 'off';\n      if (was === 1) return 'slow_only';\n      if (was === 2) return 'all';\n      throw new error_1.MongoUnexpectedServerResponseError(`Illegal profiling level value ${was}`);\n    } else {\n      throw new error_1.MongoUnexpectedServerResponseError('Error with profile command');\n    }\n  }\n}\nexports.ProfilingLevelOperation = ProfilingLevelOperation;", "map": {"version": 3, "names": ["error_1", "require", "command_1", "ProfilingLevelOperation", "CommandOperation", "constructor", "db", "options", "commandName", "execute", "server", "session", "timeoutContext", "doc", "executeCommand", "profile", "ok", "was", "MongoUnexpectedServerResponseError", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\profiling_level.ts"], "sourcesContent": ["import type { Db } from '../db';\nimport { MongoUnexpectedServerResponseError } from '../error';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { CommandOperation, type CommandOperationOptions } from './command';\n\n/** @public */\nexport type ProfilingLevelOptions = CommandOperationOptions;\n\n/** @internal */\nexport class ProfilingLevelOperation extends CommandOperation<string> {\n  override options: ProfilingLevelOptions;\n\n  constructor(db: Db, options: ProfilingLevelOptions) {\n    super(db, options);\n    this.options = options;\n  }\n\n  override get commandName() {\n    return 'profile' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<string> {\n    const doc = await super.executeCommand(server, session, { profile: -1 }, timeoutContext);\n    if (doc.ok === 1) {\n      const was = doc.was;\n      if (was === 0) return 'off';\n      if (was === 1) return 'slow_only';\n      if (was === 2) return 'all';\n      throw new MongoUnexpectedServerResponseError(`Illegal profiling level value ${was}`);\n    } else {\n      throw new MongoUnexpectedServerResponseError('Error with profile command');\n    }\n  }\n}\n"], "mappings": ";;;;;;AACA,MAAAA,OAAA,GAAAC,OAAA;AAIA,MAAAC,SAAA,GAAAD,OAAA;AAKA;AACA,MAAaE,uBAAwB,SAAQD,SAAA,CAAAE,gBAAwB;EAGnEC,YAAYC,EAAM,EAAEC,OAA8B;IAChD,KAAK,CAACD,EAAE,EAAEC,OAAO,CAAC;IAClB,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;EAEA,IAAaC,WAAWA,CAAA;IACtB,OAAO,SAAkB;EAC3B;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMC,GAAG,GAAG,MAAM,KAAK,CAACC,cAAc,CAACJ,MAAM,EAAEC,OAAO,EAAE;MAAEI,OAAO,EAAE,CAAC;IAAC,CAAE,EAAEH,cAAc,CAAC;IACxF,IAAIC,GAAG,CAACG,EAAE,KAAK,CAAC,EAAE;MAChB,MAAMC,GAAG,GAAGJ,GAAG,CAACI,GAAG;MACnB,IAAIA,GAAG,KAAK,CAAC,EAAE,OAAO,KAAK;MAC3B,IAAIA,GAAG,KAAK,CAAC,EAAE,OAAO,WAAW;MACjC,IAAIA,GAAG,KAAK,CAAC,EAAE,OAAO,KAAK;MAC3B,MAAM,IAAIjB,OAAA,CAAAkB,kCAAkC,CAAC,iCAAiCD,GAAG,EAAE,CAAC;IACtF,CAAC,MAAM;MACL,MAAM,IAAIjB,OAAA,CAAAkB,kCAAkC,CAAC,4BAA4B,CAAC;IAC5E;EACF;;AA3BFC,OAAA,CAAAhB,uBAAA,GAAAA,uBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}