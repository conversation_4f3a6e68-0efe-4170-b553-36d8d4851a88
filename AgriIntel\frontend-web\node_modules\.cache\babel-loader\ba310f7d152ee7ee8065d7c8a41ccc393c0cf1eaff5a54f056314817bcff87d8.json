{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.DbStatsOperation = void 0;\nconst command_1 = require(\"./command\");\nconst operation_1 = require(\"./operation\");\n/** @internal */\nclass DbStatsOperation extends command_1.CommandOperation {\n  constructor(db, options) {\n    super(db, options);\n    this.options = options;\n  }\n  get commandName() {\n    return 'dbStats';\n  }\n  async execute(server, session, timeoutContext) {\n    const command = {\n      dbStats: true\n    };\n    if (this.options.scale != null) {\n      command.scale = this.options.scale;\n    }\n    return await super.executeCommand(server, session, command, timeoutContext);\n  }\n}\nexports.DbStatsOperation = DbStatsOperation;\n(0, operation_1.defineAspects)(DbStatsOperation, [operation_1.Aspect.READ_OPERATION]);", "map": {"version": 3, "names": ["command_1", "require", "operation_1", "DbStatsOperation", "CommandOperation", "constructor", "db", "options", "commandName", "execute", "server", "session", "timeoutContext", "command", "dbStats", "scale", "executeCommand", "exports", "defineAspects", "Aspect", "READ_OPERATION"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\stats.ts"], "sourcesContent": ["import type { Document } from '../bson';\nimport type { Db } from '../db';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { CommandOperation, type CommandOperationOptions } from './command';\nimport { Aspect, defineAspects } from './operation';\n\n/** @public */\nexport interface DbStatsOptions extends CommandOperationOptions {\n  /** Divide the returned sizes by scale value. */\n  scale?: number;\n}\n\n/** @internal */\nexport class DbStatsOperation extends CommandOperation<Document> {\n  override options: DbStatsOptions;\n\n  constructor(db: Db, options: DbStatsOptions) {\n    super(db, options);\n    this.options = options;\n  }\n\n  override get commandName() {\n    return 'dbStats' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<Document> {\n    const command: Document = { dbStats: true };\n    if (this.options.scale != null) {\n      command.scale = this.options.scale;\n    }\n\n    return await super.executeCommand(server, session, command, timeoutContext);\n  }\n}\n\ndefineAspects(DbStatsOperation, [Aspect.READ_OPERATION]);\n"], "mappings": ";;;;;;AAKA,MAAAA,SAAA,GAAAC,OAAA;AACA,MAAAC,WAAA,GAAAD,OAAA;AAQA;AACA,MAAaE,gBAAiB,SAAQH,SAAA,CAAAI,gBAA0B;EAG9DC,YAAYC,EAAM,EAAEC,OAAuB;IACzC,KAAK,CAACD,EAAE,EAAEC,OAAO,CAAC;IAClB,IAAI,CAACA,OAAO,GAAGA,OAAO;EACxB;EAEA,IAAaC,WAAWA,CAAA;IACtB,OAAO,SAAkB;EAC3B;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMC,OAAO,GAAa;MAAEC,OAAO,EAAE;IAAI,CAAE;IAC3C,IAAI,IAAI,CAACP,OAAO,CAACQ,KAAK,IAAI,IAAI,EAAE;MAC9BF,OAAO,CAACE,KAAK,GAAG,IAAI,CAACR,OAAO,CAACQ,KAAK;IACpC;IAEA,OAAO,MAAM,KAAK,CAACC,cAAc,CAACN,MAAM,EAAEC,OAAO,EAAEE,OAAO,EAAED,cAAc,CAAC;EAC7E;;AAvBFK,OAAA,CAAAd,gBAAA,GAAAA,gBAAA;AA0BA,IAAAD,WAAA,CAAAgB,aAAa,EAACf,gBAAgB,EAAE,CAACD,WAAA,CAAAiB,MAAM,CAACC,cAAc,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}