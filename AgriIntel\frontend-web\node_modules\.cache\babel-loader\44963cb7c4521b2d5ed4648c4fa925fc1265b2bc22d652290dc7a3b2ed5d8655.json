{"ast": null, "code": "// Used https://gist.github.com/mudge/5830382 as a starting point.\n// See https://github.com/browserify/events/blob/master/events.js for\n// the Node.js (https://nodejs.org/api/events.html) polyfill used by webpack.\nexport class EventManager {\n  constructor() {\n    this.maxListeners = 20;\n    this.warnOnce = false;\n    this.events = {};\n  }\n  on(eventName, listener, options = {}) {\n    let collection = this.events[eventName];\n    if (!collection) {\n      collection = {\n        highPriority: new Map(),\n        regular: new Map()\n      };\n      this.events[eventName] = collection;\n    }\n    if (options.isFirst) {\n      collection.highPriority.set(listener, true);\n    } else {\n      collection.regular.set(listener, true);\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      const collectionSize = collection.highPriority.size + collection.regular.size;\n      if (collectionSize > this.maxListeners && !this.warnOnce) {\n        this.warnOnce = true;\n        console.warn([`Possible EventEmitter memory leak detected. ${collectionSize} ${eventName} listeners added.`].join('\\n'));\n      }\n    }\n  }\n  removeListener(eventName, listener) {\n    if (this.events[eventName]) {\n      this.events[eventName].regular.delete(listener);\n      this.events[eventName].highPriority.delete(listener);\n    }\n  }\n  removeAllListeners() {\n    this.events = {};\n  }\n  emit(eventName, ...args) {\n    const collection = this.events[eventName];\n    if (!collection) {\n      return;\n    }\n    const highPriorityListeners = Array.from(collection.highPriority.keys());\n    const regularListeners = Array.from(collection.regular.keys());\n    for (let i = highPriorityListeners.length - 1; i >= 0; i -= 1) {\n      const listener = highPriorityListeners[i];\n      if (collection.highPriority.has(listener)) {\n        listener.apply(this, args);\n      }\n    }\n    for (let i = 0; i < regularListeners.length; i += 1) {\n      const listener = regularListeners[i];\n      if (collection.regular.has(listener)) {\n        listener.apply(this, args);\n      }\n    }\n  }\n  once(eventName, listener) {\n    // eslint-disable-next-line consistent-this\n    const that = this;\n    this.on(eventName, function oneTimeListener(...args) {\n      that.removeListener(eventName, oneTimeListener);\n      listener.apply(that, args);\n    });\n  }\n}", "map": {"version": 3, "names": ["EventManager", "constructor", "maxListeners", "warnOnce", "events", "on", "eventName", "listener", "options", "collection", "highPriority", "Map", "regular", "<PERSON><PERSON><PERSON><PERSON>", "set", "process", "env", "NODE_ENV", "collectionSize", "size", "console", "warn", "join", "removeListener", "delete", "removeAllListeners", "emit", "args", "highPriorityListeners", "Array", "from", "keys", "regularListeners", "i", "length", "has", "apply", "once", "that", "oneTimeListener"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/x-data-grid/utils/EventManager.js"], "sourcesContent": ["// Used https://gist.github.com/mudge/5830382 as a starting point.\n// See https://github.com/browserify/events/blob/master/events.js for\n// the Node.js (https://nodejs.org/api/events.html) polyfill used by webpack.\nexport class EventManager {\n  constructor() {\n    this.maxListeners = 20;\n    this.warnOnce = false;\n    this.events = {};\n  }\n  on(eventName, listener, options = {}) {\n    let collection = this.events[eventName];\n    if (!collection) {\n      collection = {\n        highPriority: new Map(),\n        regular: new Map()\n      };\n      this.events[eventName] = collection;\n    }\n    if (options.isFirst) {\n      collection.highPriority.set(listener, true);\n    } else {\n      collection.regular.set(listener, true);\n    }\n    if (process.env.NODE_ENV !== 'production') {\n      const collectionSize = collection.highPriority.size + collection.regular.size;\n      if (collectionSize > this.maxListeners && !this.warnOnce) {\n        this.warnOnce = true;\n        console.warn([`Possible EventEmitter memory leak detected. ${collectionSize} ${eventName} listeners added.`].join('\\n'));\n      }\n    }\n  }\n  removeListener(eventName, listener) {\n    if (this.events[eventName]) {\n      this.events[eventName].regular.delete(listener);\n      this.events[eventName].highPriority.delete(listener);\n    }\n  }\n  removeAllListeners() {\n    this.events = {};\n  }\n  emit(eventName, ...args) {\n    const collection = this.events[eventName];\n    if (!collection) {\n      return;\n    }\n    const highPriorityListeners = Array.from(collection.highPriority.keys());\n    const regularListeners = Array.from(collection.regular.keys());\n    for (let i = highPriorityListeners.length - 1; i >= 0; i -= 1) {\n      const listener = highPriorityListeners[i];\n      if (collection.highPriority.has(listener)) {\n        listener.apply(this, args);\n      }\n    }\n    for (let i = 0; i < regularListeners.length; i += 1) {\n      const listener = regularListeners[i];\n      if (collection.regular.has(listener)) {\n        listener.apply(this, args);\n      }\n    }\n  }\n  once(eventName, listener) {\n    // eslint-disable-next-line consistent-this\n    const that = this;\n    this.on(eventName, function oneTimeListener(...args) {\n      that.removeListener(eventName, oneTimeListener);\n      listener.apply(that, args);\n    });\n  }\n}"], "mappings": "AAAA;AACA;AACA;AACA,OAAO,MAAMA,YAAY,CAAC;EACxBC,WAAWA,CAAA,EAAG;IACZ,IAAI,CAACC,YAAY,GAAG,EAAE;IACtB,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;EAClB;EACAC,EAAEA,CAACC,SAAS,EAAEC,QAAQ,EAAEC,OAAO,GAAG,CAAC,CAAC,EAAE;IACpC,IAAIC,UAAU,GAAG,IAAI,CAACL,MAAM,CAACE,SAAS,CAAC;IACvC,IAAI,CAACG,UAAU,EAAE;MACfA,UAAU,GAAG;QACXC,YAAY,EAAE,IAAIC,GAAG,CAAC,CAAC;QACvBC,OAAO,EAAE,IAAID,GAAG,CAAC;MACnB,CAAC;MACD,IAAI,CAACP,MAAM,CAACE,SAAS,CAAC,GAAGG,UAAU;IACrC;IACA,IAAID,OAAO,CAACK,OAAO,EAAE;MACnBJ,UAAU,CAACC,YAAY,CAACI,GAAG,CAACP,QAAQ,EAAE,IAAI,CAAC;IAC7C,CAAC,MAAM;MACLE,UAAU,CAACG,OAAO,CAACE,GAAG,CAACP,QAAQ,EAAE,IAAI,CAAC;IACxC;IACA,IAAIQ,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;MACzC,MAAMC,cAAc,GAAGT,UAAU,CAACC,YAAY,CAACS,IAAI,GAAGV,UAAU,CAACG,OAAO,CAACO,IAAI;MAC7E,IAAID,cAAc,GAAG,IAAI,CAAChB,YAAY,IAAI,CAAC,IAAI,CAACC,QAAQ,EAAE;QACxD,IAAI,CAACA,QAAQ,GAAG,IAAI;QACpBiB,OAAO,CAACC,IAAI,CAAC,CAAC,+CAA+CH,cAAc,IAAIZ,SAAS,mBAAmB,CAAC,CAACgB,IAAI,CAAC,IAAI,CAAC,CAAC;MAC1H;IACF;EACF;EACAC,cAAcA,CAACjB,SAAS,EAAEC,QAAQ,EAAE;IAClC,IAAI,IAAI,CAACH,MAAM,CAACE,SAAS,CAAC,EAAE;MAC1B,IAAI,CAACF,MAAM,CAACE,SAAS,CAAC,CAACM,OAAO,CAACY,MAAM,CAACjB,QAAQ,CAAC;MAC/C,IAAI,CAACH,MAAM,CAACE,SAAS,CAAC,CAACI,YAAY,CAACc,MAAM,CAACjB,QAAQ,CAAC;IACtD;EACF;EACAkB,kBAAkBA,CAAA,EAAG;IACnB,IAAI,CAACrB,MAAM,GAAG,CAAC,CAAC;EAClB;EACAsB,IAAIA,CAACpB,SAAS,EAAE,GAAGqB,IAAI,EAAE;IACvB,MAAMlB,UAAU,GAAG,IAAI,CAACL,MAAM,CAACE,SAAS,CAAC;IACzC,IAAI,CAACG,UAAU,EAAE;MACf;IACF;IACA,MAAMmB,qBAAqB,GAAGC,KAAK,CAACC,IAAI,CAACrB,UAAU,CAACC,YAAY,CAACqB,IAAI,CAAC,CAAC,CAAC;IACxE,MAAMC,gBAAgB,GAAGH,KAAK,CAACC,IAAI,CAACrB,UAAU,CAACG,OAAO,CAACmB,IAAI,CAAC,CAAC,CAAC;IAC9D,KAAK,IAAIE,CAAC,GAAGL,qBAAqB,CAACM,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,IAAI,CAAC,EAAE;MAC7D,MAAM1B,QAAQ,GAAGqB,qBAAqB,CAACK,CAAC,CAAC;MACzC,IAAIxB,UAAU,CAACC,YAAY,CAACyB,GAAG,CAAC5B,QAAQ,CAAC,EAAE;QACzCA,QAAQ,CAAC6B,KAAK,CAAC,IAAI,EAAET,IAAI,CAAC;MAC5B;IACF;IACA,KAAK,IAAIM,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,gBAAgB,CAACE,MAAM,EAAED,CAAC,IAAI,CAAC,EAAE;MACnD,MAAM1B,QAAQ,GAAGyB,gBAAgB,CAACC,CAAC,CAAC;MACpC,IAAIxB,UAAU,CAACG,OAAO,CAACuB,GAAG,CAAC5B,QAAQ,CAAC,EAAE;QACpCA,QAAQ,CAAC6B,KAAK,CAAC,IAAI,EAAET,IAAI,CAAC;MAC5B;IACF;EACF;EACAU,IAAIA,CAAC/B,SAAS,EAAEC,QAAQ,EAAE;IACxB;IACA,MAAM+B,IAAI,GAAG,IAAI;IACjB,IAAI,CAACjC,EAAE,CAACC,SAAS,EAAE,SAASiC,eAAeA,CAAC,GAAGZ,IAAI,EAAE;MACnDW,IAAI,CAACf,cAAc,CAACjB,SAAS,EAAEiC,eAAe,CAAC;MAC/ChC,QAAQ,CAAC6B,KAAK,CAACE,IAAI,EAAEX,IAAI,CAAC;IAC5B,CAAC,CAAC;EACJ;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}