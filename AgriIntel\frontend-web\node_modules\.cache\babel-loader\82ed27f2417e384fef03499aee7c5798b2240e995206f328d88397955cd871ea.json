{"ast": null, "code": "import * as React from 'react';\nexport const useOpenState = ({\n  open,\n  onOpen,\n  onClose\n}) => {\n  const isControllingOpenProp = React.useRef(typeof open === 'boolean').current;\n  const [openState, setIsOpenState] = React.useState(false);\n\n  // It is required to update inner state in useEffect in order to avoid situation when\n  // Our component is not mounted yet, but `open` state is set to `true` (e.g. initially opened)\n  React.useEffect(() => {\n    if (isControllingOpenProp) {\n      if (typeof open !== 'boolean') {\n        throw new Error('You must not mix controlling and uncontrolled mode for `open` prop');\n      }\n      setIsOpenState(open);\n    }\n  }, [isControllingOpenProp, open]);\n  const setIsOpen = React.useCallback(newIsOpen => {\n    if (!isControllingOpenProp) {\n      setIsOpenState(newIsOpen);\n    }\n    if (newIsOpen && onOpen) {\n      onOpen();\n    }\n    if (!newIsOpen && onClose) {\n      onClose();\n    }\n  }, [isControllingOpenProp, onOpen, onClose]);\n  return {\n    isOpen: openState,\n    setIsOpen\n  };\n};", "map": {"version": 3, "names": ["React", "useOpenState", "open", "onOpen", "onClose", "isControllingOpenProp", "useRef", "current", "openState", "setIsOpenState", "useState", "useEffect", "Error", "setIsOpen", "useCallback", "newIsOpen", "isOpen"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/x-date-pickers/internals/hooks/useOpenState.js"], "sourcesContent": ["import * as React from 'react';\nexport const useOpenState = ({\n  open,\n  onOpen,\n  onClose\n}) => {\n  const isControllingOpenProp = React.useRef(typeof open === 'boolean').current;\n  const [openState, setIsOpenState] = React.useState(false);\n\n  // It is required to update inner state in useEffect in order to avoid situation when\n  // Our component is not mounted yet, but `open` state is set to `true` (e.g. initially opened)\n  React.useEffect(() => {\n    if (isControllingOpenProp) {\n      if (typeof open !== 'boolean') {\n        throw new Error('You must not mix controlling and uncontrolled mode for `open` prop');\n      }\n      setIsOpenState(open);\n    }\n  }, [isControllingOpenProp, open]);\n  const setIsOpen = React.useCallback(newIsOpen => {\n    if (!isControllingOpenProp) {\n      setIsOpenState(newIsOpen);\n    }\n    if (newIsOpen && onOpen) {\n      onOpen();\n    }\n    if (!newIsOpen && onClose) {\n      onClose();\n    }\n  }, [isControllingOpenProp, onOpen, onClose]);\n  return {\n    isOpen: openState,\n    setIsOpen\n  };\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,OAAO,MAAMC,YAAY,GAAGA,CAAC;EAC3BC,IAAI;EACJC,MAAM;EACNC;AACF,CAAC,KAAK;EACJ,MAAMC,qBAAqB,GAAGL,KAAK,CAACM,MAAM,CAAC,OAAOJ,IAAI,KAAK,SAAS,CAAC,CAACK,OAAO;EAC7E,MAAM,CAACC,SAAS,EAAEC,cAAc,CAAC,GAAGT,KAAK,CAACU,QAAQ,CAAC,KAAK,CAAC;;EAEzD;EACA;EACAV,KAAK,CAACW,SAAS,CAAC,MAAM;IACpB,IAAIN,qBAAqB,EAAE;MACzB,IAAI,OAAOH,IAAI,KAAK,SAAS,EAAE;QAC7B,MAAM,IAAIU,KAAK,CAAC,oEAAoE,CAAC;MACvF;MACAH,cAAc,CAACP,IAAI,CAAC;IACtB;EACF,CAAC,EAAE,CAACG,qBAAqB,EAAEH,IAAI,CAAC,CAAC;EACjC,MAAMW,SAAS,GAAGb,KAAK,CAACc,WAAW,CAACC,SAAS,IAAI;IAC/C,IAAI,CAACV,qBAAqB,EAAE;MAC1BI,cAAc,CAACM,SAAS,CAAC;IAC3B;IACA,IAAIA,SAAS,IAAIZ,MAAM,EAAE;MACvBA,MAAM,CAAC,CAAC;IACV;IACA,IAAI,CAACY,SAAS,IAAIX,OAAO,EAAE;MACzBA,OAAO,CAAC,CAAC;IACX;EACF,CAAC,EAAE,CAACC,qBAAqB,EAAEF,MAAM,EAAEC,OAAO,CAAC,CAAC;EAC5C,OAAO;IACLY,MAAM,EAAER,SAAS;IACjBK;EACF,CAAC;AACH,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}