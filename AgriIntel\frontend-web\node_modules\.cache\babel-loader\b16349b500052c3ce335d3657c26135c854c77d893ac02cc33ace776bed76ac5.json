{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{useNavigate}from'react-router-dom';import{Grid,Card,CardContent,Typography,Box,IconButton,useTheme,Alert,Snackbar,Divider}from'@mui/material';import{Add,Pets,TrendingUp,LocalHospital,LocationOn,MoreVert,Assessment,MonetizationOn}from'../../utils/iconImports';import{ResponsiveContainer,XAxis,YAxis,Tooltip,AreaChart,Area,CartesianGrid,Legend}from'recharts';// import AnimalList from '../../components/animals/AnimalList';\nimport{useAnimalData}from'../../hooks/useAnimalData';import{ModernChart,withSubModuleTranslation,AnimatedBackgroundCard,CustomButton,StandardDashboard,ModuleContentCard}from'../../components/common';import AnimalMarketplaceGrid from'../../components/modules/animals/AnimalMarketplaceGrid';// Import MongoDB data\nimport{useMongoAnimalData}from'../../hooks/useMongoAnimalData';import{useMongoDb}from'../../hooks/useMongoDb';import{useLanguage}from'../../contexts/LanguageContext';import{tryCatch,getErrorMessage}from'../../utils/errorHandling';// Import card styling utilities\nimport{getCardStyle}from'../../utils/cardStyles';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AnimalsDashboard=_ref=>{var _stats$totalAssetValu,_stats$valueOfActiveA,_stats$valueOfRetired,_stats$retirementByRe,_stats$retirementByRe2,_stats$retirementByRe3,_stats$retirementByRe4;let{translate:propTranslate,translateSubModule,translateModuleField}=_ref;const theme=useTheme();const navigate=useNavigate();// Use MongoDB if connected, otherwise use mock data\nconst{isConnected,mongoStats:dbStats,refreshStats,isLoading:isDbLoading}=useMongoDb();const{stats:mockStats,animals:mockAnimals}=useAnimalData();const{stats:mongoStats,animals:mongoAnimals}=useMongoAnimalData();const{translate:contextTranslate}=useLanguage();// Use the translate prop if provided, otherwise use the context translate\nconst translate=propTranslate||contextTranslate;// Loading state\nconst[isLoading,setIsLoading]=useState(false);// Use MongoDB data if connected, otherwise use mock data\nconst stats=isConnected&&dbStats?dbStats:isConnected&&mongoStats?mongoStats:mockStats||{totalAnimals:0,healthPercentage:0,activeAnimals:0,inactiveAnimals:0,healthyAnimals:0,sickAnimals:0,injuredAnimals:0,pregnantAnimals:0,averageGrowthRate:0,bySpecies:{},byStatus:{},byLocation:{},byBreed:{},byHealth:{healthy:0,sick:0,injured:0,pregnant:0},// Asset Management stats\nretiredAnimals:5,nearingRetirement:3,retirementByReason:{age:2,breeding:2,health:1,other:0},valueOfActiveAssets:850000,valueOfRetiredAssets:150000,totalAssetValue:1000000,averageRoi:15};const animals=isConnected&&mongoAnimals?mongoAnimals:mockAnimals||[];// State for chart interactions\nconst[selectedSpecies,setSelectedSpecies]=useState(null);const[selectedChart,setSelectedChart]=useState(null);const[chartTooltip,setChartTooltip]=useState({show:false,content:''});// Colors for charts\nconst COLORS=[theme.palette.primary.main,theme.palette.secondary.main,theme.palette.success.main,theme.palette.warning.main,theme.palette.error.main];// Prepare data for species chart\nconst speciesData=Object.entries(stats.bySpecies||{'Cattle':25,'Sheep':18,'Goats':12,'Pigs':8}).map(_ref2=>{let[name,value]=_ref2;return{name,value};});// Prepare data for health status chart\nconst healthData=Object.entries(stats.byHealth||{healthy:45,sick:8,injured:5,pregnant:12}).map(_ref3=>{let[name,value]=_ref3;return{name:name.charAt(0).toUpperCase()+name.slice(1),value};});// Prepare data for location chart\nconst locationData=Object.entries(stats.byLocation||{'Pasture A':15,'Pasture B':12,'Barn':8,'Quarantine':3}).map(_ref4=>{let[name,value]=_ref4;return{name,value};});// Prepare data for growth chart (mock data for demonstration)\nconst growthData=[{month:'Jan',weight:120,height:60},{month:'Feb',weight:132,height:63},{month:'Mar',weight:145,height:65},{month:'Apr',weight:160,height:68},{month:'May',weight:178,height:70},{month:'Jun',weight:190,height:72}];// Alert state for notifications\nconst[alert,setAlert]=useState({open:false,message:'',severity:'info'});// Refresh data\nconst handleRefreshData=async()=>{setIsLoading(true);await tryCatch(async()=>{await refreshStats();setAlert({open:true,message:translate('common.success'),severity:'success'});},error=>{setAlert({open:true,message:getErrorMessage(error),severity:'error'});});setIsLoading(false);};// No need for separate loading indicator since we're using LoadingOverlay\n// Prepare dashboard stats\nconst dashboardStats=[{label:translate('animals.total'),value:stats.totalAnimals||63,icon:/*#__PURE__*/_jsx(Pets,{}),color:theme.palette.primary.main,trend:{value:5,isPositive:true,label:translate('dashboard.since_last_month')}},{label:translate('animals.healthy'),value:\"\".concat(stats.healthPercentage||85,\"%\"),icon:/*#__PURE__*/_jsx(LocalHospital,{}),color:theme.palette.success.main,trend:{value:2,isPositive:true,label:translate('dashboard.since_last_month')}},{label:translate('dashboard.growth_rate'),value:\"\".concat(stats.averageGrowthRate||12,\"%\"),icon:/*#__PURE__*/_jsx(TrendingUp,{}),color:theme.palette.warning.main,trend:{value:3,isPositive:true,label:translate('dashboard.since_last_month')}},{label:translate('animals.active'),value:stats.activeAnimals||58,icon:/*#__PURE__*/_jsx(LocationOn,{}),color:theme.palette.info.main,trend:{value:1,isPositive:false,label:translate('dashboard.since_last_month')}}];// Prepare dashboard actions\nconst dashboardActions=[{label:translate('animals.add'),icon:/*#__PURE__*/_jsx(Add,{}),onClick:()=>navigate('/animals/new'),color:'primary'}];// Prepare dashboard tabs\nconst dashboardTabs=[{label:translate('dashboard.overview'),icon:/*#__PURE__*/_jsx(Assessment,{}),content:/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,mb:4,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:4,children:/*#__PURE__*/_jsx(ModernChart,{title:translate('animals.species_distribution'),subtitle:translate('animals.species_distribution_desc'),data:speciesData,type:\"pie\",dataKeys:['value'],height:350,accentColor:theme.palette.primary.main,allowChartTypeChange:true,module:\"animals\",tooltip:translate('animals.species_distribution_help'),formatValue:value=>\"\".concat(value,\" \").concat(translate('animals.animals'))})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:4,children:/*#__PURE__*/_jsx(ModernChart,{title:translate('animals.health_distribution'),subtitle:translate('animals.health_distribution_desc'),data:healthData,type:\"pie\",dataKeys:['value'],height:350,accentColor:theme.palette.success.main,allowChartTypeChange:true,module:\"health\",tooltip:translate('animals.health_distribution_help'),formatValue:value=>\"\".concat(value,\" \").concat(translate('animals.animals'))})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:4,children:/*#__PURE__*/_jsx(ModernChart,{title:translate('animals.location_distribution'),subtitle:translate('animals.location_distribution_desc'),data:locationData,type:\"bar\",dataKeys:['value'],height:350,accentColor:theme.palette.info.main,allowChartTypeChange:true,module:\"animals\",tooltip:translate('animals.location_distribution_help'),formatValue:value=>\"\".concat(value,\" \").concat(translate('animals.animals'))})})]}),/*#__PURE__*/_jsx(Grid,{container:true,spacing:3,mb:4,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(ModernChart,{title:translate('animals.growth_chart'),subtitle:translate('animals.growth_chart_desc'),data:growthData,type:\"line\",dataKeys:['weight','height'],xAxisDataKey:\"month\",height:350,accentColor:theme.palette.warning.main,allowChartTypeChange:true,allowTimeRangeChange:true,module:\"animals\",tooltip:translate('animals.growth_chart_help'),formatValue:value=>\"\".concat(value,\" \").concat(value>100?'kg':'cm')})})})]})},{label:translate('animals.asset_management'),icon:/*#__PURE__*/_jsx(MonetizationOn,{}),content:/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsxs(Grid,{container:true,spacing:3,mb:4,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(ModuleContentCard,{title:translate('animals.asset_overview'),subtitle:translate('animals.asset_overview_desc'),icon:/*#__PURE__*/_jsx(MonetizationOn,{}),module:\"animals\",height:\"100%\",delay:0.1,children:/*#__PURE__*/_jsxs(Box,{sx:{p:2},children:[/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",children:translate('animals.total_asset_value')}),/*#__PURE__*/_jsxs(Typography,{variant:\"h5\",fontWeight:\"bold\",children:[\"R \",((_stats$totalAssetValu=stats.totalAssetValue)===null||_stats$totalAssetValu===void 0?void 0:_stats$totalAssetValu.toLocaleString())||'1,000,000']})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",children:translate('animals.average_roi')}),/*#__PURE__*/_jsxs(Typography,{variant:\"h5\",fontWeight:\"bold\",children:[stats.averageRoi||15,\"%\"]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",children:translate('animals.active_assets')}),/*#__PURE__*/_jsxs(Typography,{variant:\"h5\",fontWeight:\"bold\",children:[\"R \",((_stats$valueOfActiveA=stats.valueOfActiveAssets)===null||_stats$valueOfActiveA===void 0?void 0:_stats$valueOfActiveA.toLocaleString())||'850,000']})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",children:translate('animals.retired_assets')}),/*#__PURE__*/_jsxs(Typography,{variant:\"h5\",fontWeight:\"bold\",children:[\"R \",((_stats$valueOfRetired=stats.valueOfRetiredAssets)===null||_stats$valueOfRetired===void 0?void 0:_stats$valueOfRetired.toLocaleString())||'150,000']})]})})]}),/*#__PURE__*/_jsx(Divider,{sx:{my:2}}),/*#__PURE__*/_jsxs(Box,{sx:{mb:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"subtitle2\",color:\"text.secondary\",gutterBottom:true,children:translate('animals.asset_status')}),/*#__PURE__*/_jsxs(Grid,{container:true,spacing:2,children:[/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(Box,{sx:{width:12,height:12,borderRadius:'50%',bgcolor:theme.palette.primary.main,mr:1}}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[translate('animals.active'),\": \",stats.activeAnimals||58]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(Box,{sx:{width:12,height:12,borderRadius:'50%',bgcolor:theme.palette.error.main,mr:1}}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[translate('animals.retired'),\": \",stats.retiredAnimals||5]})]})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:6,children:/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:1},children:[/*#__PURE__*/_jsx(Box,{sx:{width:12,height:12,borderRadius:'50%',bgcolor:theme.palette.warning.main,mr:1}}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",children:[translate('animals.near_retirement'),\": \",stats.nearingRetirement||3]})]})})]})]})]})})}),/*#__PURE__*/_jsx(Grid,{item:true,xs:12,md:6,children:/*#__PURE__*/_jsx(ModernChart,{title:translate('animals.retirement_reasons'),subtitle:translate('animals.retirement_reasons_desc'),data:[{name:'Age',value:((_stats$retirementByRe=stats.retirementByReason)===null||_stats$retirementByRe===void 0?void 0:_stats$retirementByRe.age)||2},{name:'Breeding',value:((_stats$retirementByRe2=stats.retirementByReason)===null||_stats$retirementByRe2===void 0?void 0:_stats$retirementByRe2.breeding)||2},{name:'Health',value:((_stats$retirementByRe3=stats.retirementByReason)===null||_stats$retirementByRe3===void 0?void 0:_stats$retirementByRe3.health)||1},{name:'Other',value:((_stats$retirementByRe4=stats.retirementByReason)===null||_stats$retirementByRe4===void 0?void 0:_stats$retirementByRe4.other)||0}],type:\"pie\",dataKeys:['value'],height:350,accentColor:theme.palette.error.main,module:\"animals\",tooltip:translate('animals.retirement_reasons_help'),formatValue:value=>\"\".concat(value,\" \").concat(translate('animals.animals'))})})]})})},{label:translate('animals.registry'),icon:/*#__PURE__*/_jsx(Pets,{}),content:/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsx(Grid,{container:true,spacing:3,mb:4,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(ModuleContentCard,{title:translate('animals.registry'),subtitle:translate('animals.registry_desc'),icon:/*#__PURE__*/_jsx(Pets,{}),module:\"animals\",height:\"100%\",delay:0.1,actionLabel:translate('animals.view_all'),onAction:()=>navigate('/animals/list'),children:/*#__PURE__*/_jsx(Box,{sx:{p:2},children:/*#__PURE__*/_jsx(AnimalMarketplaceGrid,{animals:animals,loading:isLoading,title:translate('animals.featured'),maxItems:8})})})})})})},{label:translate('animals.growth_trends'),icon:/*#__PURE__*/_jsx(TrendingUp,{}),content:/*#__PURE__*/_jsx(Box,{children:/*#__PURE__*/_jsx(Grid,{container:true,spacing:3,mb:4,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(AnimatedBackgroundCard,{title:translateModuleField?translateModuleField('animal_growth_trends',\"Animal Growth Trends\"):\"Animal Growth Trends\",module:\"animals\",backgroundImage:\"/images/modules/animals/cattle-1.jpeg\",icon:/*#__PURE__*/_jsx(TrendingUp,{}),accentColor:theme.palette.warning.main,secondaryColor:theme.palette.warning.dark,children:/*#__PURE__*/_jsx(Box,{sx:{height:{xs:300,sm:350,md:400},p:2},children:/*#__PURE__*/_jsx(ResponsiveContainer,{width:\"100%\",height:\"100%\",children:/*#__PURE__*/_jsxs(AreaChart,{data:growthData,margin:{top:10,right:30,left:0,bottom:0},onClick:data=>{if(data&&data.activePayload&&data.activePayload.length>0){const payload=data.activePayload[0].payload;setSelectedChart('growth');setChartTooltip({show:true,content:\"\".concat(payload.month,\": Weight \").concat(payload.weight,\"kg, Height \").concat(payload.height,\"cm\")});}},children:[/*#__PURE__*/_jsx(CartesianGrid,{strokeDasharray:\"3 3\"}),/*#__PURE__*/_jsx(XAxis,{dataKey:\"month\"}),/*#__PURE__*/_jsx(YAxis,{}),/*#__PURE__*/_jsx(Tooltip,{}),/*#__PURE__*/_jsx(Legend,{}),/*#__PURE__*/_jsx(Area,{type:\"monotone\",dataKey:\"weight\",stroke:theme.palette.primary.main,fill:theme.palette.primary.light,fillOpacity:0.3}),/*#__PURE__*/_jsx(Area,{type:\"monotone\",dataKey:\"height\",stroke:theme.palette.secondary.main,fill:theme.palette.secondary.light,fillOpacity:0.3})]})})})})})})})}];return/*#__PURE__*/_jsxs(StandardDashboard,{title:translate('animals.dashboard'),subtitle:translate('animals.manage'),icon:/*#__PURE__*/_jsx(Pets,{}),stats:dashboardStats.map(stat=>({label:stat.label,value:stat.value,trend:{value:stat.trend.value,isPositive:stat.trend.isPositive}})),actions:dashboardActions,tabs:dashboardTabs,activeTab:0,isLoading:isLoading||isDbLoading,loadingMessage:translate('common.loading'),onRefresh:handleRefreshData,module:\"animals\",children:[chartTooltip.show&&/*#__PURE__*/_jsx(Grid,{container:true,spacing:3,mb:4,children:/*#__PURE__*/_jsx(Grid,{item:true,xs:12,children:/*#__PURE__*/_jsx(Card,{sx:_objectSpread(_objectSpread({},getCardStyle('animals',theme)),{},{mb:4,overflow:'hidden'}),children:/*#__PURE__*/_jsxs(CardContent,{children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",mb:2,children:[selectedChart==='species'?/*#__PURE__*/_jsx(Pets,{sx:{color:'white',mr:1}}):selectedChart==='growth'?/*#__PURE__*/_jsx(TrendingUp,{sx:{color:'white',mr:1}}):/*#__PURE__*/_jsx(LocationOn,{sx:{color:'white',mr:1}}),/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:\"bold\",color:\"white\",children:selectedChart==='species'?translateModuleField?translateModuleField('species_details','Species Details'):'Species Details':selectedChart==='growth'?translateModuleField?translateModuleField('growth_details','Growth Details'):'Growth Details':translateModuleField?translateModuleField('chart_details','Chart Details'):'Chart Details'})]}),/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"space-between\",alignItems:\"center\",children:[/*#__PURE__*/_jsx(Typography,{variant:\"body1\",color:\"white\",children:chartTooltip.content}),/*#__PURE__*/_jsx(IconButton,{size:\"small\",onClick:()=>setChartTooltip({show:false,content:''}),sx:{color:'white'},children:/*#__PURE__*/_jsx(MoreVert,{})})]}),selectedChart==='species'&&selectedSpecies&&/*#__PURE__*/_jsxs(CustomButton,{variant:\"contained\",color:\"secondary\",size:\"medium\",sx:{mt:2},onClick:()=>navigate(\"/animals/list?species=\".concat(selectedSpecies)),children:[\"View \",selectedSpecies,\" Animals\"]})]})})})}),/*#__PURE__*/_jsx(Snackbar,{open:alert.open,autoHideDuration:6000,onClose:()=>setAlert(_objectSpread(_objectSpread({},alert),{},{open:false})),anchorOrigin:{vertical:'bottom',horizontal:'right'},children:/*#__PURE__*/_jsx(Alert,{onClose:()=>setAlert(_objectSpread(_objectSpread({},alert),{},{open:false})),severity:alert.severity,variant:\"filled\",sx:{width:'100%'},children:alert.message})})]});};// Wrap the component with our HOC to provide translation functions\nexport default withSubModuleTranslation(AnimalsDashboard,'animals','dashboard');", "map": {"version": 3, "names": ["React", "useState", "useNavigate", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "Box", "IconButton", "useTheme", "<PERSON><PERSON>", "Snackbar", "Divider", "Add", "Pets", "TrendingUp", "LocalHospital", "LocationOn", "<PERSON><PERSON><PERSON>", "Assessment", "MonetizationOn", "ResponsiveContainer", "XAxis", "YA<PERSON>s", "<PERSON><PERSON><PERSON>", "AreaChart", "Area", "Cartesian<PERSON><PERSON>", "Legend", "useAnimalData", "<PERSON><PERSON><PERSON>", "withSubModuleTranslation", "AnimatedBackgroundCard", "CustomButton", "StandardDashboard", "ModuleContentCard", "AnimalMarketplaceGrid", "useMongoAnimalData", "useMongoDb", "useLanguage", "tryCatch", "getErrorMessage", "getCardStyle", "jsx", "_jsx", "jsxs", "_jsxs", "AnimalsDashboard", "_ref", "_stats$totalAssetValu", "_stats$valueOfActiveA", "_stats$valueOfRetired", "_stats$retirementByRe", "_stats$retirementByRe2", "_stats$retirementByRe3", "_stats$retirementByRe4", "translate", "propTranslate", "translateSubModule", "translateModuleField", "theme", "navigate", "isConnected", "mongoStats", "dbStats", "refreshStats", "isLoading", "isDbLoading", "stats", "mockStats", "animals", "mockAnimals", "mongoAnimals", "contextTranslate", "setIsLoading", "totalAnimals", "healthPercentage", "activeAnimals", "inactiveAnimals", "healthyAnimals", "sickAnimals", "injuredAnimals", "pregnantAnimals", "averageGrowthRate", "bySpecies", "byStatus", "byLocation", "byBreed", "byHealth", "healthy", "sick", "injured", "pregnant", "retired<PERSON>ni<PERSON><PERSON>", "nearingRetirement", "retirementByReason", "age", "breeding", "health", "other", "valueOfActiveAssets", "valueOfRetiredAssets", "totalAssetValue", "averageRoi", "selectedSpecies", "setSelectedSpecies", "<PERSON><PERSON><PERSON>", "setSelected<PERSON>hart", "chartTooltip", "setChartTooltip", "show", "content", "COLORS", "palette", "primary", "main", "secondary", "success", "warning", "error", "speciesData", "Object", "entries", "map", "_ref2", "name", "value", "healthData", "_ref3", "char<PERSON>t", "toUpperCase", "slice", "locationData", "_ref4", "growthData", "month", "weight", "height", "alert", "<PERSON><PERSON><PERSON><PERSON>", "open", "message", "severity", "handleRefreshData", "dashboardStats", "label", "icon", "color", "trend", "isPositive", "concat", "info", "dashboardActions", "onClick", "dashboardTabs", "children", "container", "spacing", "mb", "item", "xs", "md", "title", "subtitle", "data", "type", "dataKeys", "accentColor", "allowChartTypeChange", "module", "tooltip", "formatValue", "xAxisDataKey", "allowTimeRangeChange", "delay", "sx", "p", "variant", "fontWeight", "toLocaleString", "my", "gutterBottom", "display", "alignItems", "width", "borderRadius", "bgcolor", "mr", "actionLabel", "onAction", "loading", "maxItems", "backgroundImage", "secondaryColor", "dark", "sm", "margin", "top", "right", "left", "bottom", "activePayload", "length", "payload", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dataKey", "stroke", "fill", "light", "fillOpacity", "stat", "actions", "tabs", "activeTab", "loadingMessage", "onRefresh", "_objectSpread", "overflow", "justifyContent", "size", "mt", "autoHideDuration", "onClose", "anchor<PERSON><PERSON><PERSON>", "vertical", "horizontal"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/pages/animals/AnimalsDashboard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useNavigate } from 'react-router-dom';\nimport { Grid, Card, CardContent, Typography, Box, IconButton, LinearProgress, useTheme, CircularProgress, Alert, Snackbar, alpha, Divider,  } from '@mui/material';\nimport {\n  Add,\n  Pets,\n  TrendingUp,\n  LocalHospital,\n  LocationOn,\n  MoreVert,\n  Assessment,\n  CalendarToday,\n  MonetizationOn,\n  Refresh,\n } from '../../utils/iconImports';\nimport { PieChart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip, AreaChart, Area, CartesianGrid, Legend } from 'recharts';\nimport { EnhancedPieLabelRenderProps } from '../../types/recharts';\n// import AnimalList from '../../components/animals/AnimalList';\nimport { useAnimalData } from '../../hooks/useAnimalData';\nimport {\n  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n  ModuleContainer,\n  ModernDashboard,\n  ModernChart,\n  ModernDataTable,\n  ModernCard,\n  withSubModuleTranslation,\n  AnimatedBackgroundCard,\n  CustomButton,\n  StandardDashboard,\n  ModuleContentCard\n} from '../../components/common';\nimport AnimalMarketplaceGrid from '../../components/modules/animals/AnimalMarketplaceGrid';\n// Import MongoDB data\nimport { useMongoAnimalData } from '../../hooks/useMongoAnimalData';\nimport { useMongoDb } from '../../hooks/useMongoDb';\nimport { useLanguage } from '../../contexts/LanguageContext';\nimport { tryCatch, getErrorMessage } from '../../utils/errorHandling';\n// Import card styling utilities\nimport { getCardStyle } from '../../utils/cardStyles';\n\ninterface AnimalsDashboardProps {\n  translate?: (key: string, params?: Record<string, string | number>) => string;\n  translateSubModule?: (field: string, fallback: string) => string;\n  translateModuleField?: (field: string, fallback?: string) => string;\n}\n\nconst AnimalsDashboard: React.FC<AnimalsDashboardProps> = ({\n  translate: propTranslate,\n  translateSubModule,\n  translateModuleField\n}) => {\n  const theme = useTheme();\n  const navigate = useNavigate();\n  // Use MongoDB if connected, otherwise use mock data\n  const { isConnected, mongoStats: dbStats, refreshStats, isLoading: isDbLoading } = useMongoDb();\n  const { stats: mockStats, animals: mockAnimals } = useAnimalData();\n  const { stats: mongoStats, animals: mongoAnimals } = useMongoAnimalData();\n  const { translate: contextTranslate } = useLanguage();\n\n  // Use the translate prop if provided, otherwise use the context translate\n  const translate = propTranslate || contextTranslate;\n\n  // Loading state\n  const [isLoading, setIsLoading] = useState<boolean>(false);\n\n  // Use MongoDB data if connected, otherwise use mock data\n  const stats = isConnected && dbStats ? dbStats :\n                isConnected && mongoStats ? mongoStats :\n                mockStats || {\n    totalAnimals: 0,\n    healthPercentage: 0,\n    activeAnimals: 0,\n    inactiveAnimals: 0,\n    healthyAnimals: 0,\n    sickAnimals: 0,\n    injuredAnimals: 0,\n    pregnantAnimals: 0,\n    averageGrowthRate: 0,\n    bySpecies: {},\n    byStatus: {},\n    byLocation: {},\n    byBreed: {},\n    byHealth: {\n      healthy: 0,\n      sick: 0,\n      injured: 0,\n      pregnant: 0\n    },\n    // Asset Management stats\n    retiredAnimals: 5,\n    nearingRetirement: 3,\n    retirementByReason: {\n      age: 2,\n      breeding: 2,\n      health: 1,\n      other: 0\n    },\n    valueOfActiveAssets: 850000,\n    valueOfRetiredAssets: 150000,\n    totalAssetValue: 1000000,\n    averageRoi: 15\n  };\n  const animals = isConnected && mongoAnimals ? mongoAnimals : mockAnimals || [];\n\n  // State for chart interactions\n  const [selectedSpecies, setSelectedSpecies] = useState<string | null>(null);\n  const [selectedChart, setSelectedChart] = useState<string | null>(null);\n  const [chartTooltip, setChartTooltip] = useState<{ show: boolean, content: string }>({ show: false, content: '' });\n\n  // Colors for charts\n  const COLORS = [\n    theme.palette.primary.main,\n    theme.palette.secondary.main,\n    theme.palette.success.main,\n    theme.palette.warning.main,\n    theme.palette.error.main,\n  ];\n\n  // Prepare data for species chart\n  const speciesData = Object.entries(stats.bySpecies || {\n    'Cattle': 25,\n    'Sheep': 18,\n    'Goats': 12,\n    'Pigs': 8\n  }).map(([name, value]) => ({\n    name,\n    value,\n  }));\n\n  // Prepare data for health status chart\n  const healthData = Object.entries(stats.byHealth || {\n    healthy: 45,\n    sick: 8,\n    injured: 5,\n    pregnant: 12\n  }).map(([name, value]) => ({\n    name: name.charAt(0).toUpperCase() + name.slice(1),\n    value,\n  }));\n\n  // Prepare data for location chart\n  const locationData = Object.entries(stats.byLocation || {\n    'Pasture A': 15,\n    'Pasture B': 12,\n    'Barn': 8,\n    'Quarantine': 3\n  }).map(([name, value]) => ({\n    name,\n    value,\n  }));\n\n  // Prepare data for growth chart (mock data for demonstration)\n  const growthData = [\n    { month: 'Jan', weight: 120, height: 60 },\n    { month: 'Feb', weight: 132, height: 63 },\n    { month: 'Mar', weight: 145, height: 65 },\n    { month: 'Apr', weight: 160, height: 68 },\n    { month: 'May', weight: 178, height: 70 },\n    { month: 'Jun', weight: 190, height: 72 },\n  ];\n\n  // Alert state for notifications\n  const [alert, setAlert] = useState<{ open: boolean; message: string; severity: 'success' | 'error' | 'info' | 'warning' }>({\n    open: false,\n    message: '',\n    severity: 'info'\n  });\n\n  // Refresh data\n  const handleRefreshData = async () => {\n    setIsLoading(true);\n\n    await tryCatch(\n      async () => {\n        await refreshStats();\n        setAlert({\n          open: true,\n          message: translate('common.success'),\n          severity: 'success'\n        });\n      },\n      (error) => {\n        setAlert({\n          open: true,\n          message: getErrorMessage(error),\n          severity: 'error'\n        });\n      }\n    );\n\n    setIsLoading(false);\n  };\n\n  // No need for separate loading indicator since we're using LoadingOverlay\n\n  // Prepare dashboard stats\n  const dashboardStats = [\n    {\n      label: translate('animals.total'),\n      value: stats.totalAnimals || 63,\n      icon: <Pets />,\n      color: theme.palette.primary.main,\n      trend: {\n        value: 5,\n        isPositive: true,\n        label: translate('dashboard.since_last_month')\n      }\n    },\n    {\n      label: translate('animals.healthy'),\n      value: `${stats.healthPercentage || 85}%`,\n      icon: <LocalHospital />,\n      color: theme.palette.success.main,\n      trend: {\n        value: 2,\n        isPositive: true,\n        label: translate('dashboard.since_last_month')\n      }\n    },\n    {\n      label: translate('dashboard.growth_rate'),\n      value: `${stats.averageGrowthRate || 12}%`,\n      icon: <TrendingUp />,\n      color: theme.palette.warning.main,\n      trend: {\n        value: 3,\n        isPositive: true,\n        label: translate('dashboard.since_last_month')\n      }\n    },\n    {\n      label: translate('animals.active'),\n      value: stats.activeAnimals || 58,\n      icon: <LocationOn />,\n      color: theme.palette.info.main,\n      trend: {\n        value: 1,\n        isPositive: false,\n        label: translate('dashboard.since_last_month')\n      }\n    }\n  ];\n\n  // Prepare dashboard actions\n  const dashboardActions = [\n    {\n      label: translate('animals.add'),\n      icon: <Add />,\n      onClick: () => navigate('/animals/new'),\n      color: 'primary'\n    }\n  ];\n\n  // Prepare dashboard tabs\n  const dashboardTabs = [\n    {\n      label: translate('dashboard.overview'),\n      icon: <Assessment />,\n      content: (\n        <Box>\n          {/* Charts Section */}\n          <Grid container spacing={3} mb={4}>\n            <Grid item xs={12} md={4}>\n              <ModernChart\n                title={translate('animals.species_distribution')}\n                subtitle={translate('animals.species_distribution_desc')}\n                data={speciesData}\n                type=\"pie\"\n                dataKeys={['value']}\n                height={350}\n                accentColor={theme.palette.primary.main}\n                allowChartTypeChange={true}\n                module=\"animals\"\n                tooltip={translate('animals.species_distribution_help')}\n                formatValue={(value) => `${value} ${translate('animals.animals')}`}\n              />\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <ModernChart\n                title={translate('animals.health_distribution')}\n                subtitle={translate('animals.health_distribution_desc')}\n                data={healthData}\n                type=\"pie\"\n                dataKeys={['value']}\n                height={350}\n                accentColor={theme.palette.success.main}\n                allowChartTypeChange={true}\n                module=\"health\"\n                tooltip={translate('animals.health_distribution_help')}\n                formatValue={(value) => `${value} ${translate('animals.animals')}`}\n              />\n            </Grid>\n\n            <Grid item xs={12} md={4}>\n              <ModernChart\n                title={translate('animals.location_distribution')}\n                subtitle={translate('animals.location_distribution_desc')}\n                data={locationData}\n                type=\"bar\"\n                dataKeys={['value']}\n                height={350}\n                accentColor={theme.palette.info.main}\n                allowChartTypeChange={true}\n                module=\"animals\"\n                tooltip={translate('animals.location_distribution_help')}\n                formatValue={(value) => `${value} ${translate('animals.animals')}`}\n              />\n            </Grid>\n          </Grid>\n\n          {/* Growth Chart */}\n          <Grid container spacing={3} mb={4}>\n            <Grid item xs={12}>\n              <ModernChart\n                title={translate('animals.growth_chart')}\n                subtitle={translate('animals.growth_chart_desc')}\n                data={growthData}\n                type=\"line\"\n                dataKeys={['weight', 'height']}\n                xAxisDataKey=\"month\"\n                height={350}\n                accentColor={theme.palette.warning.main}\n                allowChartTypeChange={true}\n                allowTimeRangeChange={true}\n                module=\"animals\"\n                tooltip={translate('animals.growth_chart_help')}\n                formatValue={(value) => `${value} ${value > 100 ? 'kg' : 'cm'}`}\n              />\n            </Grid>\n          </Grid>\n        </Box>\n      )\n    },\n    {\n      label: translate('animals.asset_management'),\n      icon: <MonetizationOn />,\n      content: (\n        <Box>\n          {/* Asset Management Stats */}\n          <Grid container spacing={3} mb={4}>\n            <Grid item xs={12} md={6}>\n              <ModuleContentCard\n                title={translate('animals.asset_overview')}\n                subtitle={translate('animals.asset_overview_desc')}\n                icon={<MonetizationOn />}\n                module=\"animals\"\n                height=\"100%\"\n                delay={0.1}\n              >\n                <Box sx={{ p: 2 }}>\n                  <Grid container spacing={2}>\n                    <Grid item xs={6}>\n                      <Box sx={{ mb: 2 }}>\n                        <Typography variant=\"subtitle2\" color=\"text.secondary\">{translate('animals.total_asset_value')}</Typography>\n                        <Typography variant=\"h5\" fontWeight=\"bold\">R {stats.totalAssetValue?.toLocaleString() || '1,000,000'}</Typography>\n                      </Box>\n                    </Grid>\n                    <Grid item xs={6}>\n                      <Box sx={{ mb: 2 }}>\n                        <Typography variant=\"subtitle2\" color=\"text.secondary\">{translate('animals.average_roi')}</Typography>\n                        <Typography variant=\"h5\" fontWeight=\"bold\">{stats.averageRoi || 15}%</Typography>\n                      </Box>\n                    </Grid>\n                    <Grid item xs={6}>\n                      <Box sx={{ mb: 2 }}>\n                        <Typography variant=\"subtitle2\" color=\"text.secondary\">{translate('animals.active_assets')}</Typography>\n                        <Typography variant=\"h5\" fontWeight=\"bold\">R {stats.valueOfActiveAssets?.toLocaleString() || '850,000'}</Typography>\n                      </Box>\n                    </Grid>\n                    <Grid item xs={6}>\n                      <Box sx={{ mb: 2 }}>\n                        <Typography variant=\"subtitle2\" color=\"text.secondary\">{translate('animals.retired_assets')}</Typography>\n                        <Typography variant=\"h5\" fontWeight=\"bold\">R {stats.valueOfRetiredAssets?.toLocaleString() || '150,000'}</Typography>\n                      </Box>\n                    </Grid>\n                  </Grid>\n\n                  <Divider sx={{ my: 2 }} />\n\n                  <Box sx={{ mb: 2 }}>\n                    <Typography variant=\"subtitle2\" color=\"text.secondary\" gutterBottom>{translate('animals.asset_status')}</Typography>\n                    <Grid container spacing={2}>\n                      <Grid item xs={6}>\n                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                          <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: theme.palette.primary.main, mr: 1 }} />\n                          <Typography variant=\"body2\">{translate('animals.active')}: {stats.activeAnimals || 58}</Typography>\n                        </Box>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                          <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: theme.palette.error.main, mr: 1 }} />\n                          <Typography variant=\"body2\">{translate('animals.retired')}: {stats.retiredAnimals || 5}</Typography>\n                        </Box>\n                      </Grid>\n                      <Grid item xs={6}>\n                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>\n                          <Box sx={{ width: 12, height: 12, borderRadius: '50%', bgcolor: theme.palette.warning.main, mr: 1 }} />\n                          <Typography variant=\"body2\">{translate('animals.near_retirement')}: {stats.nearingRetirement || 3}</Typography>\n                        </Box>\n                      </Grid>\n                    </Grid>\n                  </Box>\n                </Box>\n              </ModuleContentCard>\n            </Grid>\n\n            <Grid item xs={12} md={6}>\n              <ModernChart\n                title={translate('animals.retirement_reasons')}\n                subtitle={translate('animals.retirement_reasons_desc')}\n                data={[\n                  { name: 'Age', value: stats.retirementByReason?.age || 2 },\n                  { name: 'Breeding', value: stats.retirementByReason?.breeding || 2 },\n                  { name: 'Health', value: stats.retirementByReason?.health || 1 },\n                  { name: 'Other', value: stats.retirementByReason?.other || 0 },\n                ]}\n                type=\"pie\"\n                dataKeys={['value']}\n                height={350}\n                accentColor={theme.palette.error.main}\n                module=\"animals\"\n                tooltip={translate('animals.retirement_reasons_help')}\n                formatValue={(value) => `${value} ${translate('animals.animals')}`}\n              />\n            </Grid>\n          </Grid>\n        </Box>\n      )\n    },\n    {\n      label: translate('animals.registry'),\n      icon: <Pets />,\n      content: (\n        <Box>\n          {/* Animal Registry */}\n          <Grid container spacing={3} mb={4}>\n            <Grid item xs={12}>\n              <ModuleContentCard\n                title={translate('animals.registry')}\n                subtitle={translate('animals.registry_desc')}\n                icon={<Pets />}\n                module=\"animals\"\n                height=\"100%\"\n                delay={0.1}\n                actionLabel={translate('animals.view_all')}\n                onAction={() => navigate('/animals/list')}\n              >\n                <Box sx={{ p: 2 }}>\n                  <AnimalMarketplaceGrid\n                    animals={animals}\n                    loading={isLoading}\n                    title={translate('animals.featured')}\n                    maxItems={8}\n                  />\n                </Box>\n              </ModuleContentCard>\n            </Grid>\n          </Grid>\n        </Box>\n      )\n    },\n    {\n      label: translate('animals.growth_trends'),\n      icon: <TrendingUp />,\n      content: (\n        <Box>\n          {/* Growth Chart */}\n          <Grid container spacing={3} mb={4}>\n            <Grid item xs={12}>\n              <AnimatedBackgroundCard\n                title={translateModuleField ? translateModuleField('animal_growth_trends', \"Animal Growth Trends\") : \"Animal Growth Trends\"}\n                module=\"animals\"\n                backgroundImage=\"/images/modules/animals/cattle-1.jpeg\"\n                icon={<TrendingUp />}\n                accentColor={theme.palette.warning.main}\n                secondaryColor={theme.palette.warning.dark}\n              >\n                <Box sx={{ height: { xs: 300, sm: 350, md: 400 }, p: 2 }}>\n                  <ResponsiveContainer width=\"100%\" height=\"100%\">\n                    <AreaChart\n                      data={growthData}\n                      margin={{ top: 10, right: 30, left: 0, bottom: 0 }}\n                      onClick={(data) => {\n                        if (data && data.activePayload && data.activePayload.length > 0) {\n                          const payload = data.activePayload[0].payload;\n                          setSelectedChart('growth');\n                          setChartTooltip({\n                            show: true,\n                            content: `${payload.month}: Weight ${payload.weight}kg, Height ${payload.height}cm`\n                          });\n                        }\n                      }}\n                    >\n                      <CartesianGrid strokeDasharray=\"3 3\" />\n                      <XAxis dataKey=\"month\" />\n                      <YAxis />\n                      <Tooltip />\n                      <Legend />\n                      <Area type=\"monotone\" dataKey=\"weight\" stroke={theme.palette.primary.main} fill={theme.palette.primary.light} fillOpacity={0.3} />\n                      <Area type=\"monotone\" dataKey=\"height\" stroke={theme.palette.secondary.main} fill={theme.palette.secondary.light} fillOpacity={0.3} />\n                    </AreaChart>\n                  </ResponsiveContainer>\n                </Box>\n              </AnimatedBackgroundCard>\n            </Grid>\n          </Grid>\n        </Box>\n      )\n    }\n  ];\n\n  return (\n    <StandardDashboard\n      title={translate('animals.dashboard')}\n      subtitle={translate('animals.manage')}\n      icon={<Pets />}\n      stats={dashboardStats.map(stat => ({\n        label: stat.label,\n        value: stat.value,\n        trend: {\n          value: stat.trend.value,\n          isPositive: stat.trend.isPositive\n        }\n      }))}\n      actions={dashboardActions}\n      tabs={dashboardTabs}\n      activeTab={0}\n      isLoading={isLoading || isDbLoading}\n      loadingMessage={translate('common.loading')}\n      onRefresh={handleRefreshData}\n      module=\"animals\"\n    >\n        {/* Chart Interaction Feedback */}\n        {chartTooltip.show && (\n          <Grid container spacing={3} mb={4}>\n            <Grid item xs={12}>\n              <Card sx={{\n                ...getCardStyle('animals', theme),\n                mb: 4,\n                overflow: 'hidden'\n              }}>\n                <CardContent>\n                  <Box display=\"flex\" alignItems=\"center\" mb={2}>\n                    {selectedChart === 'species' ? <Pets sx={{ color: 'white', mr: 1 }} /> :\n                     selectedChart === 'growth' ? <TrendingUp sx={{ color: 'white', mr: 1 }} /> :\n                     <LocationOn sx={{ color: 'white', mr: 1 }} />}\n                    <Typography variant=\"h6\" fontWeight=\"bold\" color=\"white\">\n                      {selectedChart === 'species' ?\n                        (translateModuleField ? translateModuleField('species_details', 'Species Details') : 'Species Details') :\n                       selectedChart === 'growth' ?\n                        (translateModuleField ? translateModuleField('growth_details', 'Growth Details') : 'Growth Details') :\n                        (translateModuleField ? translateModuleField('chart_details', 'Chart Details') : 'Chart Details')}\n                    </Typography>\n                  </Box>\n                  <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"center\">\n                    <Typography variant=\"body1\" color=\"white\">\n                      {chartTooltip.content}\n                    </Typography>\n                    <IconButton size=\"small\" onClick={() => setChartTooltip({ show: false, content: '' })} sx={{ color: 'white' }}>\n                      <MoreVert />\n                    </IconButton>\n                  </Box>\n                  {selectedChart === 'species' && selectedSpecies && (\n                    <CustomButton\n                      variant=\"contained\"\n                      color=\"secondary\"\n                      size=\"medium\"\n                      sx={{ mt: 2 }}\n                      onClick={() => navigate(`/animals/list?species=${selectedSpecies}`)}\n                    >\n                      View {selectedSpecies} Animals\n                    </CustomButton>\n                  )}\n                </CardContent>\n              </Card>\n            </Grid>\n          </Grid>\n        )}\n\n        {/* Alert Snackbar */}\n        <Snackbar\n          open={alert.open}\n          autoHideDuration={6000}\n          onClose={() => setAlert({ ...alert, open: false })}\n          anchorOrigin={{ vertical: 'bottom', horizontal: 'right' }}\n        >\n          <Alert\n            onClose={() => setAlert({ ...alert, open: false })}\n            severity={alert.severity}\n            variant=\"filled\"\n            sx={{ width: '100%' }}\n          >\n            {alert.message}\n          </Alert>\n        </Snackbar>\n    </StandardDashboard>\n  );\n};\n\n// Wrap the component with our HOC to provide translation functions\nexport default withSubModuleTranslation(AnimalsDashboard, 'animals', 'dashboard');\n"], "mappings": "gJAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,WAAW,KAAQ,kBAAkB,CAC9C,OAASC,IAAI,CAAEC,IAAI,CAAEC,WAAW,CAAEC,UAAU,CAAEC,GAAG,CAAEC,UAAU,CAAkBC,QAAQ,CAAoBC,KAAK,CAAEC,QAAQ,CAASC,OAAO,KAAU,eAAe,CACnK,OACEC,GAAG,CACHC,IAAI,CACJC,UAAU,CACVC,aAAa,CACbC,UAAU,CACVC,QAAQ,CACRC,UAAU,CAEVC,cAAc,KAER,yBAAyB,CACjC,OAA8BC,mBAAmB,CAAiBC,KAAK,CAAEC,KAAK,CAAEC,OAAO,CAAEC,SAAS,CAAEC,IAAI,CAAEC,aAAa,CAAEC,MAAM,KAAQ,UAAU,CAEjJ;AACA,OAASC,aAAa,KAAQ,2BAA2B,CACzD,OAIEC,WAAW,CAGXC,wBAAwB,CACxBC,sBAAsB,CACtBC,YAAY,CACZC,iBAAiB,CACjBC,iBAAiB,KACZ,yBAAyB,CAChC,MAAO,CAAAC,qBAAqB,KAAM,wDAAwD,CAC1F;AACA,OAASC,kBAAkB,KAAQ,gCAAgC,CACnE,OAASC,UAAU,KAAQ,wBAAwB,CACnD,OAASC,WAAW,KAAQ,gCAAgC,CAC5D,OAASC,QAAQ,CAAEC,eAAe,KAAQ,2BAA2B,CACrE;AACA,OAASC,YAAY,KAAQ,wBAAwB,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAQtD,KAAM,CAAAC,gBAAiD,CAAGC,IAAA,EAIpD,KAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,qBAAA,CAAAC,sBAAA,CAAAC,sBAAA,CAAAC,sBAAA,IAJqD,CACzDC,SAAS,CAAEC,aAAa,CACxBC,kBAAkB,CAClBC,oBACF,CAAC,CAAAX,IAAA,CACC,KAAM,CAAAY,KAAK,CAAGnD,QAAQ,CAAC,CAAC,CACxB,KAAM,CAAAoD,QAAQ,CAAG3D,WAAW,CAAC,CAAC,CAC9B;AACA,KAAM,CAAE4D,WAAW,CAAEC,UAAU,CAAEC,OAAO,CAAEC,YAAY,CAAEC,SAAS,CAAEC,WAAY,CAAC,CAAG7B,UAAU,CAAC,CAAC,CAC/F,KAAM,CAAE8B,KAAK,CAAEC,SAAS,CAAEC,OAAO,CAAEC,WAAY,CAAC,CAAG1C,aAAa,CAAC,CAAC,CAClE,KAAM,CAAEuC,KAAK,CAAEL,UAAU,CAAEO,OAAO,CAAEE,YAAa,CAAC,CAAGnC,kBAAkB,CAAC,CAAC,CACzE,KAAM,CAAEmB,SAAS,CAAEiB,gBAAiB,CAAC,CAAGlC,WAAW,CAAC,CAAC,CAErD;AACA,KAAM,CAAAiB,SAAS,CAAGC,aAAa,EAAIgB,gBAAgB,CAEnD;AACA,KAAM,CAACP,SAAS,CAAEQ,YAAY,CAAC,CAAGzE,QAAQ,CAAU,KAAK,CAAC,CAE1D;AACA,KAAM,CAAAmE,KAAK,CAAGN,WAAW,EAAIE,OAAO,CAAGA,OAAO,CAChCF,WAAW,EAAIC,UAAU,CAAGA,UAAU,CACtCM,SAAS,EAAI,CACzBM,YAAY,CAAE,CAAC,CACfC,gBAAgB,CAAE,CAAC,CACnBC,aAAa,CAAE,CAAC,CAChBC,eAAe,CAAE,CAAC,CAClBC,cAAc,CAAE,CAAC,CACjBC,WAAW,CAAE,CAAC,CACdC,cAAc,CAAE,CAAC,CACjBC,eAAe,CAAE,CAAC,CAClBC,iBAAiB,CAAE,CAAC,CACpBC,SAAS,CAAE,CAAC,CAAC,CACbC,QAAQ,CAAE,CAAC,CAAC,CACZC,UAAU,CAAE,CAAC,CAAC,CACdC,OAAO,CAAE,CAAC,CAAC,CACXC,QAAQ,CAAE,CACRC,OAAO,CAAE,CAAC,CACVC,IAAI,CAAE,CAAC,CACPC,OAAO,CAAE,CAAC,CACVC,QAAQ,CAAE,CACZ,CAAC,CACD;AACAC,cAAc,CAAE,CAAC,CACjBC,iBAAiB,CAAE,CAAC,CACpBC,kBAAkB,CAAE,CAClBC,GAAG,CAAE,CAAC,CACNC,QAAQ,CAAE,CAAC,CACXC,MAAM,CAAE,CAAC,CACTC,KAAK,CAAE,CACT,CAAC,CACDC,mBAAmB,CAAE,MAAM,CAC3BC,oBAAoB,CAAE,MAAM,CAC5BC,eAAe,CAAE,OAAO,CACxBC,UAAU,CAAE,EACd,CAAC,CACD,KAAM,CAAAjC,OAAO,CAAGR,WAAW,EAAIU,YAAY,CAAGA,YAAY,CAAGD,WAAW,EAAI,EAAE,CAE9E;AACA,KAAM,CAACiC,eAAe,CAAEC,kBAAkB,CAAC,CAAGxG,QAAQ,CAAgB,IAAI,CAAC,CAC3E,KAAM,CAACyG,aAAa,CAAEC,gBAAgB,CAAC,CAAG1G,QAAQ,CAAgB,IAAI,CAAC,CACvE,KAAM,CAAC2G,YAAY,CAAEC,eAAe,CAAC,CAAG5G,QAAQ,CAAqC,CAAE6G,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,EAAG,CAAC,CAAC,CAElH;AACA,KAAM,CAAAC,MAAM,CAAG,CACbpD,KAAK,CAACqD,OAAO,CAACC,OAAO,CAACC,IAAI,CAC1BvD,KAAK,CAACqD,OAAO,CAACG,SAAS,CAACD,IAAI,CAC5BvD,KAAK,CAACqD,OAAO,CAACI,OAAO,CAACF,IAAI,CAC1BvD,KAAK,CAACqD,OAAO,CAACK,OAAO,CAACH,IAAI,CAC1BvD,KAAK,CAACqD,OAAO,CAACM,KAAK,CAACJ,IAAI,CACzB,CAED;AACA,KAAM,CAAAK,WAAW,CAAGC,MAAM,CAACC,OAAO,CAACtD,KAAK,CAACgB,SAAS,EAAI,CACpD,QAAQ,CAAE,EAAE,CACZ,OAAO,CAAE,EAAE,CACX,OAAO,CAAE,EAAE,CACX,MAAM,CAAE,CACV,CAAC,CAAC,CAACuC,GAAG,CAACC,KAAA,MAAC,CAACC,IAAI,CAAEC,KAAK,CAAC,CAAAF,KAAA,OAAM,CACzBC,IAAI,CACJC,KACF,CAAC,EAAC,CAAC,CAEH;AACA,KAAM,CAAAC,UAAU,CAAGN,MAAM,CAACC,OAAO,CAACtD,KAAK,CAACoB,QAAQ,EAAI,CAClDC,OAAO,CAAE,EAAE,CACXC,IAAI,CAAE,CAAC,CACPC,OAAO,CAAE,CAAC,CACVC,QAAQ,CAAE,EACZ,CAAC,CAAC,CAAC+B,GAAG,CAACK,KAAA,MAAC,CAACH,IAAI,CAAEC,KAAK,CAAC,CAAAE,KAAA,OAAM,CACzBH,IAAI,CAAEA,IAAI,CAACI,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGL,IAAI,CAACM,KAAK,CAAC,CAAC,CAAC,CAClDL,KACF,CAAC,EAAC,CAAC,CAEH;AACA,KAAM,CAAAM,YAAY,CAAGX,MAAM,CAACC,OAAO,CAACtD,KAAK,CAACkB,UAAU,EAAI,CACtD,WAAW,CAAE,EAAE,CACf,WAAW,CAAE,EAAE,CACf,MAAM,CAAE,CAAC,CACT,YAAY,CAAE,CAChB,CAAC,CAAC,CAACqC,GAAG,CAACU,KAAA,MAAC,CAACR,IAAI,CAAEC,KAAK,CAAC,CAAAO,KAAA,OAAM,CACzBR,IAAI,CACJC,KACF,CAAC,EAAC,CAAC,CAEH;AACA,KAAM,CAAAQ,UAAU,CAAG,CACjB,CAAEC,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,GAAG,CAAEC,MAAM,CAAE,EAAG,CAAC,CACzC,CAAEF,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,GAAG,CAAEC,MAAM,CAAE,EAAG,CAAC,CACzC,CAAEF,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,GAAG,CAAEC,MAAM,CAAE,EAAG,CAAC,CACzC,CAAEF,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,GAAG,CAAEC,MAAM,CAAE,EAAG,CAAC,CACzC,CAAEF,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,GAAG,CAAEC,MAAM,CAAE,EAAG,CAAC,CACzC,CAAEF,KAAK,CAAE,KAAK,CAAEC,MAAM,CAAE,GAAG,CAAEC,MAAM,CAAE,EAAG,CAAC,CAC1C,CAED;AACA,KAAM,CAACC,KAAK,CAAEC,QAAQ,CAAC,CAAG1I,QAAQ,CAAyF,CACzH2I,IAAI,CAAE,KAAK,CACXC,OAAO,CAAE,EAAE,CACXC,QAAQ,CAAE,MACZ,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,iBAAiB,CAAG,KAAAA,CAAA,GAAY,CACpCrE,YAAY,CAAC,IAAI,CAAC,CAElB,KAAM,CAAAlC,QAAQ,CACZ,SAAY,CACV,KAAM,CAAAyB,YAAY,CAAC,CAAC,CACpB0E,QAAQ,CAAC,CACPC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAErF,SAAS,CAAC,gBAAgB,CAAC,CACpCsF,QAAQ,CAAE,SACZ,CAAC,CAAC,CACJ,CAAC,CACAvB,KAAK,EAAK,CACToB,QAAQ,CAAC,CACPC,IAAI,CAAE,IAAI,CACVC,OAAO,CAAEpG,eAAe,CAAC8E,KAAK,CAAC,CAC/BuB,QAAQ,CAAE,OACZ,CAAC,CAAC,CACJ,CACF,CAAC,CAEDpE,YAAY,CAAC,KAAK,CAAC,CACrB,CAAC,CAED;AAEA;AACA,KAAM,CAAAsE,cAAc,CAAG,CACrB,CACEC,KAAK,CAAEzF,SAAS,CAAC,eAAe,CAAC,CACjCsE,KAAK,CAAE1D,KAAK,CAACO,YAAY,EAAI,EAAE,CAC/BuE,IAAI,cAAEtG,IAAA,CAAC9B,IAAI,GAAE,CAAC,CACdqI,KAAK,CAAEvF,KAAK,CAACqD,OAAO,CAACC,OAAO,CAACC,IAAI,CACjCiC,KAAK,CAAE,CACLtB,KAAK,CAAE,CAAC,CACRuB,UAAU,CAAE,IAAI,CAChBJ,KAAK,CAAEzF,SAAS,CAAC,4BAA4B,CAC/C,CACF,CAAC,CACD,CACEyF,KAAK,CAAEzF,SAAS,CAAC,iBAAiB,CAAC,CACnCsE,KAAK,IAAAwB,MAAA,CAAKlF,KAAK,CAACQ,gBAAgB,EAAI,EAAE,KAAG,CACzCsE,IAAI,cAAEtG,IAAA,CAAC5B,aAAa,GAAE,CAAC,CACvBmI,KAAK,CAAEvF,KAAK,CAACqD,OAAO,CAACI,OAAO,CAACF,IAAI,CACjCiC,KAAK,CAAE,CACLtB,KAAK,CAAE,CAAC,CACRuB,UAAU,CAAE,IAAI,CAChBJ,KAAK,CAAEzF,SAAS,CAAC,4BAA4B,CAC/C,CACF,CAAC,CACD,CACEyF,KAAK,CAAEzF,SAAS,CAAC,uBAAuB,CAAC,CACzCsE,KAAK,IAAAwB,MAAA,CAAKlF,KAAK,CAACe,iBAAiB,EAAI,EAAE,KAAG,CAC1C+D,IAAI,cAAEtG,IAAA,CAAC7B,UAAU,GAAE,CAAC,CACpBoI,KAAK,CAAEvF,KAAK,CAACqD,OAAO,CAACK,OAAO,CAACH,IAAI,CACjCiC,KAAK,CAAE,CACLtB,KAAK,CAAE,CAAC,CACRuB,UAAU,CAAE,IAAI,CAChBJ,KAAK,CAAEzF,SAAS,CAAC,4BAA4B,CAC/C,CACF,CAAC,CACD,CACEyF,KAAK,CAAEzF,SAAS,CAAC,gBAAgB,CAAC,CAClCsE,KAAK,CAAE1D,KAAK,CAACS,aAAa,EAAI,EAAE,CAChCqE,IAAI,cAAEtG,IAAA,CAAC3B,UAAU,GAAE,CAAC,CACpBkI,KAAK,CAAEvF,KAAK,CAACqD,OAAO,CAACsC,IAAI,CAACpC,IAAI,CAC9BiC,KAAK,CAAE,CACLtB,KAAK,CAAE,CAAC,CACRuB,UAAU,CAAE,KAAK,CACjBJ,KAAK,CAAEzF,SAAS,CAAC,4BAA4B,CAC/C,CACF,CAAC,CACF,CAED;AACA,KAAM,CAAAgG,gBAAgB,CAAG,CACvB,CACEP,KAAK,CAAEzF,SAAS,CAAC,aAAa,CAAC,CAC/B0F,IAAI,cAAEtG,IAAA,CAAC/B,GAAG,GAAE,CAAC,CACb4I,OAAO,CAAEA,CAAA,GAAM5F,QAAQ,CAAC,cAAc,CAAC,CACvCsF,KAAK,CAAE,SACT,CAAC,CACF,CAED;AACA,KAAM,CAAAO,aAAa,CAAG,CACpB,CACET,KAAK,CAAEzF,SAAS,CAAC,oBAAoB,CAAC,CACtC0F,IAAI,cAAEtG,IAAA,CAACzB,UAAU,GAAE,CAAC,CACpB4F,OAAO,cACLjE,KAAA,CAACvC,GAAG,EAAAoJ,QAAA,eAEF7G,KAAA,CAAC3C,IAAI,EAACyJ,SAAS,MAACC,OAAO,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAH,QAAA,eAChC/G,IAAA,CAACzC,IAAI,EAAC4J,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,cACvB/G,IAAA,CAACd,WAAW,EACVoI,KAAK,CAAE1G,SAAS,CAAC,8BAA8B,CAAE,CACjD2G,QAAQ,CAAE3G,SAAS,CAAC,mCAAmC,CAAE,CACzD4G,IAAI,CAAE5C,WAAY,CAClB6C,IAAI,CAAC,KAAK,CACVC,QAAQ,CAAE,CAAC,OAAO,CAAE,CACpB7B,MAAM,CAAE,GAAI,CACZ8B,WAAW,CAAE3G,KAAK,CAACqD,OAAO,CAACC,OAAO,CAACC,IAAK,CACxCqD,oBAAoB,CAAE,IAAK,CAC3BC,MAAM,CAAC,SAAS,CAChBC,OAAO,CAAElH,SAAS,CAAC,mCAAmC,CAAE,CACxDmH,WAAW,CAAG7C,KAAK,KAAAwB,MAAA,CAAQxB,KAAK,MAAAwB,MAAA,CAAI9F,SAAS,CAAC,iBAAiB,CAAC,CAAG,CACpE,CAAC,CACE,CAAC,cAEPZ,IAAA,CAACzC,IAAI,EAAC4J,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,cACvB/G,IAAA,CAACd,WAAW,EACVoI,KAAK,CAAE1G,SAAS,CAAC,6BAA6B,CAAE,CAChD2G,QAAQ,CAAE3G,SAAS,CAAC,kCAAkC,CAAE,CACxD4G,IAAI,CAAErC,UAAW,CACjBsC,IAAI,CAAC,KAAK,CACVC,QAAQ,CAAE,CAAC,OAAO,CAAE,CACpB7B,MAAM,CAAE,GAAI,CACZ8B,WAAW,CAAE3G,KAAK,CAACqD,OAAO,CAACI,OAAO,CAACF,IAAK,CACxCqD,oBAAoB,CAAE,IAAK,CAC3BC,MAAM,CAAC,QAAQ,CACfC,OAAO,CAAElH,SAAS,CAAC,kCAAkC,CAAE,CACvDmH,WAAW,CAAG7C,KAAK,KAAAwB,MAAA,CAAQxB,KAAK,MAAAwB,MAAA,CAAI9F,SAAS,CAAC,iBAAiB,CAAC,CAAG,CACpE,CAAC,CACE,CAAC,cAEPZ,IAAA,CAACzC,IAAI,EAAC4J,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,cACvB/G,IAAA,CAACd,WAAW,EACVoI,KAAK,CAAE1G,SAAS,CAAC,+BAA+B,CAAE,CAClD2G,QAAQ,CAAE3G,SAAS,CAAC,oCAAoC,CAAE,CAC1D4G,IAAI,CAAEhC,YAAa,CACnBiC,IAAI,CAAC,KAAK,CACVC,QAAQ,CAAE,CAAC,OAAO,CAAE,CACpB7B,MAAM,CAAE,GAAI,CACZ8B,WAAW,CAAE3G,KAAK,CAACqD,OAAO,CAACsC,IAAI,CAACpC,IAAK,CACrCqD,oBAAoB,CAAE,IAAK,CAC3BC,MAAM,CAAC,SAAS,CAChBC,OAAO,CAAElH,SAAS,CAAC,oCAAoC,CAAE,CACzDmH,WAAW,CAAG7C,KAAK,KAAAwB,MAAA,CAAQxB,KAAK,MAAAwB,MAAA,CAAI9F,SAAS,CAAC,iBAAiB,CAAC,CAAG,CACpE,CAAC,CACE,CAAC,EACH,CAAC,cAGPZ,IAAA,CAACzC,IAAI,EAACyJ,SAAS,MAACC,OAAO,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAH,QAAA,cAChC/G,IAAA,CAACzC,IAAI,EAAC4J,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAL,QAAA,cAChB/G,IAAA,CAACd,WAAW,EACVoI,KAAK,CAAE1G,SAAS,CAAC,sBAAsB,CAAE,CACzC2G,QAAQ,CAAE3G,SAAS,CAAC,2BAA2B,CAAE,CACjD4G,IAAI,CAAE9B,UAAW,CACjB+B,IAAI,CAAC,MAAM,CACXC,QAAQ,CAAE,CAAC,QAAQ,CAAE,QAAQ,CAAE,CAC/BM,YAAY,CAAC,OAAO,CACpBnC,MAAM,CAAE,GAAI,CACZ8B,WAAW,CAAE3G,KAAK,CAACqD,OAAO,CAACK,OAAO,CAACH,IAAK,CACxCqD,oBAAoB,CAAE,IAAK,CAC3BK,oBAAoB,CAAE,IAAK,CAC3BJ,MAAM,CAAC,SAAS,CAChBC,OAAO,CAAElH,SAAS,CAAC,2BAA2B,CAAE,CAChDmH,WAAW,CAAG7C,KAAK,KAAAwB,MAAA,CAAQxB,KAAK,MAAAwB,MAAA,CAAIxB,KAAK,CAAG,GAAG,CAAG,IAAI,CAAG,IAAI,CAAG,CACjE,CAAC,CACE,CAAC,CACH,CAAC,EACJ,CAET,CAAC,CACD,CACEmB,KAAK,CAAEzF,SAAS,CAAC,0BAA0B,CAAC,CAC5C0F,IAAI,cAAEtG,IAAA,CAACxB,cAAc,GAAE,CAAC,CACxB2F,OAAO,cACLnE,IAAA,CAACrC,GAAG,EAAAoJ,QAAA,cAEF7G,KAAA,CAAC3C,IAAI,EAACyJ,SAAS,MAACC,OAAO,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAH,QAAA,eAChC/G,IAAA,CAACzC,IAAI,EAAC4J,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,cACvB/G,IAAA,CAACT,iBAAiB,EAChB+H,KAAK,CAAE1G,SAAS,CAAC,wBAAwB,CAAE,CAC3C2G,QAAQ,CAAE3G,SAAS,CAAC,6BAA6B,CAAE,CACnD0F,IAAI,cAAEtG,IAAA,CAACxB,cAAc,GAAE,CAAE,CACzBqJ,MAAM,CAAC,SAAS,CAChBhC,MAAM,CAAC,MAAM,CACbqC,KAAK,CAAE,GAAI,CAAAnB,QAAA,cAEX7G,KAAA,CAACvC,GAAG,EAACwK,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAArB,QAAA,eAChB7G,KAAA,CAAC3C,IAAI,EAACyJ,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAF,QAAA,eACzB/G,IAAA,CAACzC,IAAI,EAAC4J,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAL,QAAA,cACf7G,KAAA,CAACvC,GAAG,EAACwK,EAAE,CAAE,CAAEjB,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACjB/G,IAAA,CAACtC,UAAU,EAAC2K,OAAO,CAAC,WAAW,CAAC9B,KAAK,CAAC,gBAAgB,CAAAQ,QAAA,CAAEnG,SAAS,CAAC,2BAA2B,CAAC,CAAa,CAAC,cAC5GV,KAAA,CAACxC,UAAU,EAAC2K,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,MAAM,CAAAvB,QAAA,EAAC,IAAE,CAAC,EAAA1G,qBAAA,CAAAmB,KAAK,CAACkC,eAAe,UAAArD,qBAAA,iBAArBA,qBAAA,CAAuBkI,cAAc,CAAC,CAAC,GAAI,WAAW,EAAa,CAAC,EAC/G,CAAC,CACF,CAAC,cACPvI,IAAA,CAACzC,IAAI,EAAC4J,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAL,QAAA,cACf7G,KAAA,CAACvC,GAAG,EAACwK,EAAE,CAAE,CAAEjB,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACjB/G,IAAA,CAACtC,UAAU,EAAC2K,OAAO,CAAC,WAAW,CAAC9B,KAAK,CAAC,gBAAgB,CAAAQ,QAAA,CAAEnG,SAAS,CAAC,qBAAqB,CAAC,CAAa,CAAC,cACtGV,KAAA,CAACxC,UAAU,EAAC2K,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,MAAM,CAAAvB,QAAA,EAAEvF,KAAK,CAACmC,UAAU,EAAI,EAAE,CAAC,GAAC,EAAY,CAAC,EAC9E,CAAC,CACF,CAAC,cACP3D,IAAA,CAACzC,IAAI,EAAC4J,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAL,QAAA,cACf7G,KAAA,CAACvC,GAAG,EAACwK,EAAE,CAAE,CAAEjB,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACjB/G,IAAA,CAACtC,UAAU,EAAC2K,OAAO,CAAC,WAAW,CAAC9B,KAAK,CAAC,gBAAgB,CAAAQ,QAAA,CAAEnG,SAAS,CAAC,uBAAuB,CAAC,CAAa,CAAC,cACxGV,KAAA,CAACxC,UAAU,EAAC2K,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,MAAM,CAAAvB,QAAA,EAAC,IAAE,CAAC,EAAAzG,qBAAA,CAAAkB,KAAK,CAACgC,mBAAmB,UAAAlD,qBAAA,iBAAzBA,qBAAA,CAA2BiI,cAAc,CAAC,CAAC,GAAI,SAAS,EAAa,CAAC,EACjH,CAAC,CACF,CAAC,cACPvI,IAAA,CAACzC,IAAI,EAAC4J,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAL,QAAA,cACf7G,KAAA,CAACvC,GAAG,EAACwK,EAAE,CAAE,CAAEjB,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACjB/G,IAAA,CAACtC,UAAU,EAAC2K,OAAO,CAAC,WAAW,CAAC9B,KAAK,CAAC,gBAAgB,CAAAQ,QAAA,CAAEnG,SAAS,CAAC,wBAAwB,CAAC,CAAa,CAAC,cACzGV,KAAA,CAACxC,UAAU,EAAC2K,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,MAAM,CAAAvB,QAAA,EAAC,IAAE,CAAC,EAAAxG,qBAAA,CAAAiB,KAAK,CAACiC,oBAAoB,UAAAlD,qBAAA,iBAA1BA,qBAAA,CAA4BgI,cAAc,CAAC,CAAC,GAAI,SAAS,EAAa,CAAC,EAClH,CAAC,CACF,CAAC,EACH,CAAC,cAEPvI,IAAA,CAAChC,OAAO,EAACmK,EAAE,CAAE,CAAEK,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAE1BtI,KAAA,CAACvC,GAAG,EAACwK,EAAE,CAAE,CAAEjB,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACjB/G,IAAA,CAACtC,UAAU,EAAC2K,OAAO,CAAC,WAAW,CAAC9B,KAAK,CAAC,gBAAgB,CAACkC,YAAY,MAAA1B,QAAA,CAAEnG,SAAS,CAAC,sBAAsB,CAAC,CAAa,CAAC,cACpHV,KAAA,CAAC3C,IAAI,EAACyJ,SAAS,MAACC,OAAO,CAAE,CAAE,CAAAF,QAAA,eACzB/G,IAAA,CAACzC,IAAI,EAAC4J,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAL,QAAA,cACf7G,KAAA,CAACvC,GAAG,EAACwK,EAAE,CAAE,CAAEO,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEzB,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACxD/G,IAAA,CAACrC,GAAG,EAACwK,EAAE,CAAE,CAAES,KAAK,CAAE,EAAE,CAAE/C,MAAM,CAAE,EAAE,CAAEgD,YAAY,CAAE,KAAK,CAAEC,OAAO,CAAE9H,KAAK,CAACqD,OAAO,CAACC,OAAO,CAACC,IAAI,CAAEwE,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cACvG7I,KAAA,CAACxC,UAAU,EAAC2K,OAAO,CAAC,OAAO,CAAAtB,QAAA,EAAEnG,SAAS,CAAC,gBAAgB,CAAC,CAAC,IAAE,CAACY,KAAK,CAACS,aAAa,EAAI,EAAE,EAAa,CAAC,EAChG,CAAC,CACF,CAAC,cACPjC,IAAA,CAACzC,IAAI,EAAC4J,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAL,QAAA,cACf7G,KAAA,CAACvC,GAAG,EAACwK,EAAE,CAAE,CAAEO,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEzB,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACxD/G,IAAA,CAACrC,GAAG,EAACwK,EAAE,CAAE,CAAES,KAAK,CAAE,EAAE,CAAE/C,MAAM,CAAE,EAAE,CAAEgD,YAAY,CAAE,KAAK,CAAEC,OAAO,CAAE9H,KAAK,CAACqD,OAAO,CAACM,KAAK,CAACJ,IAAI,CAAEwE,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cACrG7I,KAAA,CAACxC,UAAU,EAAC2K,OAAO,CAAC,OAAO,CAAAtB,QAAA,EAAEnG,SAAS,CAAC,iBAAiB,CAAC,CAAC,IAAE,CAACY,KAAK,CAACyB,cAAc,EAAI,CAAC,EAAa,CAAC,EACjG,CAAC,CACF,CAAC,cACPjD,IAAA,CAACzC,IAAI,EAAC4J,IAAI,MAACC,EAAE,CAAE,CAAE,CAAAL,QAAA,cACf7G,KAAA,CAACvC,GAAG,EAACwK,EAAE,CAAE,CAAEO,OAAO,CAAE,MAAM,CAAEC,UAAU,CAAE,QAAQ,CAAEzB,EAAE,CAAE,CAAE,CAAE,CAAAH,QAAA,eACxD/G,IAAA,CAACrC,GAAG,EAACwK,EAAE,CAAE,CAAES,KAAK,CAAE,EAAE,CAAE/C,MAAM,CAAE,EAAE,CAAEgD,YAAY,CAAE,KAAK,CAAEC,OAAO,CAAE9H,KAAK,CAACqD,OAAO,CAACK,OAAO,CAACH,IAAI,CAAEwE,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cACvG7I,KAAA,CAACxC,UAAU,EAAC2K,OAAO,CAAC,OAAO,CAAAtB,QAAA,EAAEnG,SAAS,CAAC,yBAAyB,CAAC,CAAC,IAAE,CAACY,KAAK,CAAC0B,iBAAiB,EAAI,CAAC,EAAa,CAAC,EAC5G,CAAC,CACF,CAAC,EACH,CAAC,EACJ,CAAC,EACH,CAAC,CACW,CAAC,CAChB,CAAC,cAEPlD,IAAA,CAACzC,IAAI,EAAC4J,IAAI,MAACC,EAAE,CAAE,EAAG,CAACC,EAAE,CAAE,CAAE,CAAAN,QAAA,cACvB/G,IAAA,CAACd,WAAW,EACVoI,KAAK,CAAE1G,SAAS,CAAC,4BAA4B,CAAE,CAC/C2G,QAAQ,CAAE3G,SAAS,CAAC,iCAAiC,CAAE,CACvD4G,IAAI,CAAE,CACJ,CAAEvC,IAAI,CAAE,KAAK,CAAEC,KAAK,CAAE,EAAA1E,qBAAA,CAAAgB,KAAK,CAAC2B,kBAAkB,UAAA3C,qBAAA,iBAAxBA,qBAAA,CAA0B4C,GAAG,GAAI,CAAE,CAAC,CAC1D,CAAE6B,IAAI,CAAE,UAAU,CAAEC,KAAK,CAAE,EAAAzE,sBAAA,CAAAe,KAAK,CAAC2B,kBAAkB,UAAA1C,sBAAA,iBAAxBA,sBAAA,CAA0B4C,QAAQ,GAAI,CAAE,CAAC,CACpE,CAAE4B,IAAI,CAAE,QAAQ,CAAEC,KAAK,CAAE,EAAAxE,sBAAA,CAAAc,KAAK,CAAC2B,kBAAkB,UAAAzC,sBAAA,iBAAxBA,sBAAA,CAA0B4C,MAAM,GAAI,CAAE,CAAC,CAChE,CAAE2B,IAAI,CAAE,OAAO,CAAEC,KAAK,CAAE,EAAAvE,sBAAA,CAAAa,KAAK,CAAC2B,kBAAkB,UAAAxC,sBAAA,iBAAxBA,sBAAA,CAA0B4C,KAAK,GAAI,CAAE,CAAC,CAC9D,CACFkE,IAAI,CAAC,KAAK,CACVC,QAAQ,CAAE,CAAC,OAAO,CAAE,CACpB7B,MAAM,CAAE,GAAI,CACZ8B,WAAW,CAAE3G,KAAK,CAACqD,OAAO,CAACM,KAAK,CAACJ,IAAK,CACtCsD,MAAM,CAAC,SAAS,CAChBC,OAAO,CAAElH,SAAS,CAAC,iCAAiC,CAAE,CACtDmH,WAAW,CAAG7C,KAAK,KAAAwB,MAAA,CAAQxB,KAAK,MAAAwB,MAAA,CAAI9F,SAAS,CAAC,iBAAiB,CAAC,CAAG,CACpE,CAAC,CACE,CAAC,EACH,CAAC,CACJ,CAET,CAAC,CACD,CACEyF,KAAK,CAAEzF,SAAS,CAAC,kBAAkB,CAAC,CACpC0F,IAAI,cAAEtG,IAAA,CAAC9B,IAAI,GAAE,CAAC,CACdiG,OAAO,cACLnE,IAAA,CAACrC,GAAG,EAAAoJ,QAAA,cAEF/G,IAAA,CAACzC,IAAI,EAACyJ,SAAS,MAACC,OAAO,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAH,QAAA,cAChC/G,IAAA,CAACzC,IAAI,EAAC4J,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAL,QAAA,cAChB/G,IAAA,CAACT,iBAAiB,EAChB+H,KAAK,CAAE1G,SAAS,CAAC,kBAAkB,CAAE,CACrC2G,QAAQ,CAAE3G,SAAS,CAAC,uBAAuB,CAAE,CAC7C0F,IAAI,cAAEtG,IAAA,CAAC9B,IAAI,GAAE,CAAE,CACf2J,MAAM,CAAC,SAAS,CAChBhC,MAAM,CAAC,MAAM,CACbqC,KAAK,CAAE,GAAI,CACXc,WAAW,CAAEpI,SAAS,CAAC,kBAAkB,CAAE,CAC3CqI,QAAQ,CAAEA,CAAA,GAAMhI,QAAQ,CAAC,eAAe,CAAE,CAAA8F,QAAA,cAE1C/G,IAAA,CAACrC,GAAG,EAACwK,EAAE,CAAE,CAAEC,CAAC,CAAE,CAAE,CAAE,CAAArB,QAAA,cAChB/G,IAAA,CAACR,qBAAqB,EACpBkC,OAAO,CAAEA,OAAQ,CACjBwH,OAAO,CAAE5H,SAAU,CACnBgG,KAAK,CAAE1G,SAAS,CAAC,kBAAkB,CAAE,CACrCuI,QAAQ,CAAE,CAAE,CACb,CAAC,CACC,CAAC,CACW,CAAC,CAChB,CAAC,CACH,CAAC,CACJ,CAET,CAAC,CACD,CACE9C,KAAK,CAAEzF,SAAS,CAAC,uBAAuB,CAAC,CACzC0F,IAAI,cAAEtG,IAAA,CAAC7B,UAAU,GAAE,CAAC,CACpBgG,OAAO,cACLnE,IAAA,CAACrC,GAAG,EAAAoJ,QAAA,cAEF/G,IAAA,CAACzC,IAAI,EAACyJ,SAAS,MAACC,OAAO,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAH,QAAA,cAChC/G,IAAA,CAACzC,IAAI,EAAC4J,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAL,QAAA,cAChB/G,IAAA,CAACZ,sBAAsB,EACrBkI,KAAK,CAAEvG,oBAAoB,CAAGA,oBAAoB,CAAC,sBAAsB,CAAE,sBAAsB,CAAC,CAAG,sBAAuB,CAC5H8G,MAAM,CAAC,SAAS,CAChBuB,eAAe,CAAC,uCAAuC,CACvD9C,IAAI,cAAEtG,IAAA,CAAC7B,UAAU,GAAE,CAAE,CACrBwJ,WAAW,CAAE3G,KAAK,CAACqD,OAAO,CAACK,OAAO,CAACH,IAAK,CACxC8E,cAAc,CAAErI,KAAK,CAACqD,OAAO,CAACK,OAAO,CAAC4E,IAAK,CAAAvC,QAAA,cAE3C/G,IAAA,CAACrC,GAAG,EAACwK,EAAE,CAAE,CAAEtC,MAAM,CAAE,CAAEuB,EAAE,CAAE,GAAG,CAAEmC,EAAE,CAAE,GAAG,CAAElC,EAAE,CAAE,GAAI,CAAC,CAAEe,CAAC,CAAE,CAAE,CAAE,CAAArB,QAAA,cACvD/G,IAAA,CAACvB,mBAAmB,EAACmK,KAAK,CAAC,MAAM,CAAC/C,MAAM,CAAC,MAAM,CAAAkB,QAAA,cAC7C7G,KAAA,CAACrB,SAAS,EACR2I,IAAI,CAAE9B,UAAW,CACjB8D,MAAM,CAAE,CAAEC,GAAG,CAAE,EAAE,CAAEC,KAAK,CAAE,EAAE,CAAEC,IAAI,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAE,CAAE,CACnD/C,OAAO,CAAGW,IAAI,EAAK,CACjB,GAAIA,IAAI,EAAIA,IAAI,CAACqC,aAAa,EAAIrC,IAAI,CAACqC,aAAa,CAACC,MAAM,CAAG,CAAC,CAAE,CAC/D,KAAM,CAAAC,OAAO,CAAGvC,IAAI,CAACqC,aAAa,CAAC,CAAC,CAAC,CAACE,OAAO,CAC7ChG,gBAAgB,CAAC,QAAQ,CAAC,CAC1BE,eAAe,CAAC,CACdC,IAAI,CAAE,IAAI,CACVC,OAAO,IAAAuC,MAAA,CAAKqD,OAAO,CAACpE,KAAK,cAAAe,MAAA,CAAYqD,OAAO,CAACnE,MAAM,gBAAAc,MAAA,CAAcqD,OAAO,CAAClE,MAAM,MACjF,CAAC,CAAC,CACJ,CACF,CAAE,CAAAkB,QAAA,eAEF/G,IAAA,CAACjB,aAAa,EAACiL,eAAe,CAAC,KAAK,CAAE,CAAC,cACvChK,IAAA,CAACtB,KAAK,EAACuL,OAAO,CAAC,OAAO,CAAE,CAAC,cACzBjK,IAAA,CAACrB,KAAK,GAAE,CAAC,cACTqB,IAAA,CAACpB,OAAO,GAAE,CAAC,cACXoB,IAAA,CAAChB,MAAM,GAAE,CAAC,cACVgB,IAAA,CAAClB,IAAI,EAAC2I,IAAI,CAAC,UAAU,CAACwC,OAAO,CAAC,QAAQ,CAACC,MAAM,CAAElJ,KAAK,CAACqD,OAAO,CAACC,OAAO,CAACC,IAAK,CAAC4F,IAAI,CAAEnJ,KAAK,CAACqD,OAAO,CAACC,OAAO,CAAC8F,KAAM,CAACC,WAAW,CAAE,GAAI,CAAE,CAAC,cAClIrK,IAAA,CAAClB,IAAI,EAAC2I,IAAI,CAAC,UAAU,CAACwC,OAAO,CAAC,QAAQ,CAACC,MAAM,CAAElJ,KAAK,CAACqD,OAAO,CAACG,SAAS,CAACD,IAAK,CAAC4F,IAAI,CAAEnJ,KAAK,CAACqD,OAAO,CAACG,SAAS,CAAC4F,KAAM,CAACC,WAAW,CAAE,GAAI,CAAE,CAAC,EAC7H,CAAC,CACO,CAAC,CACnB,CAAC,CACgB,CAAC,CACrB,CAAC,CACH,CAAC,CACJ,CAET,CAAC,CACF,CAED,mBACEnK,KAAA,CAACZ,iBAAiB,EAChBgI,KAAK,CAAE1G,SAAS,CAAC,mBAAmB,CAAE,CACtC2G,QAAQ,CAAE3G,SAAS,CAAC,gBAAgB,CAAE,CACtC0F,IAAI,cAAEtG,IAAA,CAAC9B,IAAI,GAAE,CAAE,CACfsD,KAAK,CAAE4E,cAAc,CAACrB,GAAG,CAACuF,IAAI,GAAK,CACjCjE,KAAK,CAAEiE,IAAI,CAACjE,KAAK,CACjBnB,KAAK,CAAEoF,IAAI,CAACpF,KAAK,CACjBsB,KAAK,CAAE,CACLtB,KAAK,CAAEoF,IAAI,CAAC9D,KAAK,CAACtB,KAAK,CACvBuB,UAAU,CAAE6D,IAAI,CAAC9D,KAAK,CAACC,UACzB,CACF,CAAC,CAAC,CAAE,CACJ8D,OAAO,CAAE3D,gBAAiB,CAC1B4D,IAAI,CAAE1D,aAAc,CACpB2D,SAAS,CAAE,CAAE,CACbnJ,SAAS,CAAEA,SAAS,EAAIC,WAAY,CACpCmJ,cAAc,CAAE9J,SAAS,CAAC,gBAAgB,CAAE,CAC5C+J,SAAS,CAAExE,iBAAkB,CAC7B0B,MAAM,CAAC,SAAS,CAAAd,QAAA,EAGb/C,YAAY,CAACE,IAAI,eAChBlE,IAAA,CAACzC,IAAI,EAACyJ,SAAS,MAACC,OAAO,CAAE,CAAE,CAACC,EAAE,CAAE,CAAE,CAAAH,QAAA,cAChC/G,IAAA,CAACzC,IAAI,EAAC4J,IAAI,MAACC,EAAE,CAAE,EAAG,CAAAL,QAAA,cAChB/G,IAAA,CAACxC,IAAI,EAAC2K,EAAE,CAAAyC,aAAA,CAAAA,aAAA,IACH9K,YAAY,CAAC,SAAS,CAAEkB,KAAK,CAAC,MACjCkG,EAAE,CAAE,CAAC,CACL2D,QAAQ,CAAE,QAAQ,EAClB,CAAA9D,QAAA,cACA7G,KAAA,CAACzC,WAAW,EAAAsJ,QAAA,eACV7G,KAAA,CAACvC,GAAG,EAAC+K,OAAO,CAAC,MAAM,CAACC,UAAU,CAAC,QAAQ,CAACzB,EAAE,CAAE,CAAE,CAAAH,QAAA,EAC3CjD,aAAa,GAAK,SAAS,cAAG9D,IAAA,CAAC9B,IAAI,EAACiK,EAAE,CAAE,CAAE5B,KAAK,CAAE,OAAO,CAAEwC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,CACrEjF,aAAa,GAAK,QAAQ,cAAG9D,IAAA,CAAC7B,UAAU,EAACgK,EAAE,CAAE,CAAE5B,KAAK,CAAE,OAAO,CAAEwC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC1E/I,IAAA,CAAC3B,UAAU,EAAC8J,EAAE,CAAE,CAAE5B,KAAK,CAAE,OAAO,CAAEwC,EAAE,CAAE,CAAE,CAAE,CAAE,CAAC,cAC9C/I,IAAA,CAACtC,UAAU,EAAC2K,OAAO,CAAC,IAAI,CAACC,UAAU,CAAC,MAAM,CAAC/B,KAAK,CAAC,OAAO,CAAAQ,QAAA,CACrDjD,aAAa,GAAK,SAAS,CACzB/C,oBAAoB,CAAGA,oBAAoB,CAAC,iBAAiB,CAAE,iBAAiB,CAAC,CAAG,iBAAiB,CACvG+C,aAAa,GAAK,QAAQ,CACxB/C,oBAAoB,CAAGA,oBAAoB,CAAC,gBAAgB,CAAE,gBAAgB,CAAC,CAAG,gBAAgB,CAClGA,oBAAoB,CAAGA,oBAAoB,CAAC,eAAe,CAAE,eAAe,CAAC,CAAG,eAAgB,CACzF,CAAC,EACV,CAAC,cACNb,KAAA,CAACvC,GAAG,EAAC+K,OAAO,CAAC,MAAM,CAACoC,cAAc,CAAC,eAAe,CAACnC,UAAU,CAAC,QAAQ,CAAA5B,QAAA,eACpE/G,IAAA,CAACtC,UAAU,EAAC2K,OAAO,CAAC,OAAO,CAAC9B,KAAK,CAAC,OAAO,CAAAQ,QAAA,CACtC/C,YAAY,CAACG,OAAO,CACX,CAAC,cACbnE,IAAA,CAACpC,UAAU,EAACmN,IAAI,CAAC,OAAO,CAAClE,OAAO,CAAEA,CAAA,GAAM5C,eAAe,CAAC,CAAEC,IAAI,CAAE,KAAK,CAAEC,OAAO,CAAE,EAAG,CAAC,CAAE,CAACgE,EAAE,CAAE,CAAE5B,KAAK,CAAE,OAAQ,CAAE,CAAAQ,QAAA,cAC5G/G,IAAA,CAAC1B,QAAQ,GAAE,CAAC,CACF,CAAC,EACV,CAAC,CACLwF,aAAa,GAAK,SAAS,EAAIF,eAAe,eAC7C1D,KAAA,CAACb,YAAY,EACXgJ,OAAO,CAAC,WAAW,CACnB9B,KAAK,CAAC,WAAW,CACjBwE,IAAI,CAAC,QAAQ,CACb5C,EAAE,CAAE,CAAE6C,EAAE,CAAE,CAAE,CAAE,CACdnE,OAAO,CAAEA,CAAA,GAAM5F,QAAQ,0BAAAyF,MAAA,CAA0B9C,eAAe,CAAE,CAAE,CAAAmD,QAAA,EACrE,OACM,CAACnD,eAAe,CAAC,UACxB,EAAc,CACf,EACU,CAAC,CACV,CAAC,CACH,CAAC,CACH,CACP,cAGD5D,IAAA,CAACjC,QAAQ,EACPiI,IAAI,CAAEF,KAAK,CAACE,IAAK,CACjBiF,gBAAgB,CAAE,IAAK,CACvBC,OAAO,CAAEA,CAAA,GAAMnF,QAAQ,CAAA6E,aAAA,CAAAA,aAAA,IAAM9E,KAAK,MAAEE,IAAI,CAAE,KAAK,EAAE,CAAE,CACnDmF,YAAY,CAAE,CAAEC,QAAQ,CAAE,QAAQ,CAAEC,UAAU,CAAE,OAAQ,CAAE,CAAAtE,QAAA,cAE1D/G,IAAA,CAAClC,KAAK,EACJoN,OAAO,CAAEA,CAAA,GAAMnF,QAAQ,CAAA6E,aAAA,CAAAA,aAAA,IAAM9E,KAAK,MAAEE,IAAI,CAAE,KAAK,EAAE,CAAE,CACnDE,QAAQ,CAAEJ,KAAK,CAACI,QAAS,CACzBmC,OAAO,CAAC,QAAQ,CAChBF,EAAE,CAAE,CAAES,KAAK,CAAE,MAAO,CAAE,CAAA7B,QAAA,CAErBjB,KAAK,CAACG,OAAO,CACT,CAAC,CACA,CAAC,EACI,CAAC,CAExB,CAAC,CAED;AACA,cAAe,CAAA9G,wBAAwB,CAACgB,gBAAgB,CAAE,SAAS,CAAE,WAAW,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}