{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.MongoDBOIDC = exports.OIDC_WORKFLOWS = exports.OIDC_VERSION = void 0;\nconst error_1 = require(\"../../error\");\nconst auth_provider_1 = require(\"./auth_provider\");\nconst azure_machine_workflow_1 = require(\"./mongodb_oidc/azure_machine_workflow\");\nconst gcp_machine_workflow_1 = require(\"./mongodb_oidc/gcp_machine_workflow\");\nconst k8s_machine_workflow_1 = require(\"./mongodb_oidc/k8s_machine_workflow\");\nconst token_cache_1 = require(\"./mongodb_oidc/token_cache\");\nconst token_machine_workflow_1 = require(\"./mongodb_oidc/token_machine_workflow\");\n/** Error when credentials are missing. */\nconst MISSING_CREDENTIALS_ERROR = 'AuthContext must provide credentials.';\n/** The current version of OIDC implementation. */\nexports.OIDC_VERSION = 1;\n/** @internal */\nexports.OIDC_WORKFLOWS = new Map();\nexports.OIDC_WORKFLOWS.set('test', () => new token_machine_workflow_1.TokenMachineWorkflow(new token_cache_1.TokenCache()));\nexports.OIDC_WORKFLOWS.set('azure', () => new azure_machine_workflow_1.AzureMachineWorkflow(new token_cache_1.TokenCache()));\nexports.OIDC_WORKFLOWS.set('gcp', () => new gcp_machine_workflow_1.GCPMachineWorkflow(new token_cache_1.TokenCache()));\nexports.OIDC_WORKFLOWS.set('k8s', () => new k8s_machine_workflow_1.K8SMachineWorkflow(new token_cache_1.TokenCache()));\n/**\n * OIDC auth provider.\n */\nclass MongoDBOIDC extends auth_provider_1.AuthProvider {\n  /**\n   * Instantiate the auth provider.\n   */\n  constructor(workflow) {\n    super();\n    if (!workflow) {\n      throw new error_1.MongoInvalidArgumentError('No workflow provided to the OIDC auth provider.');\n    }\n    this.workflow = workflow;\n  }\n  /**\n   * Authenticate using OIDC\n   */\n  async auth(authContext) {\n    const {\n      connection,\n      reauthenticating,\n      response\n    } = authContext;\n    if (response?.speculativeAuthenticate?.done && !reauthenticating) {\n      return;\n    }\n    const credentials = getCredentials(authContext);\n    if (reauthenticating) {\n      await this.workflow.reauthenticate(connection, credentials);\n    } else {\n      await this.workflow.execute(connection, credentials, response);\n    }\n  }\n  /**\n   * Add the speculative auth for the initial handshake.\n   */\n  async prepare(handshakeDoc, authContext) {\n    const {\n      connection\n    } = authContext;\n    const credentials = getCredentials(authContext);\n    const result = await this.workflow.speculativeAuth(connection, credentials);\n    return {\n      ...handshakeDoc,\n      ...result\n    };\n  }\n}\nexports.MongoDBOIDC = MongoDBOIDC;\n/**\n * Get credentials from the auth context, throwing if they do not exist.\n */\nfunction getCredentials(authContext) {\n  const {\n    credentials\n  } = authContext;\n  if (!credentials) {\n    throw new error_1.MongoMissingCredentialsError(MISSING_CREDENTIALS_ERROR);\n  }\n  return credentials;\n}", "map": {"version": 3, "names": ["error_1", "require", "auth_provider_1", "azure_machine_workflow_1", "gcp_machine_workflow_1", "k8s_machine_workflow_1", "token_cache_1", "token_machine_workflow_1", "MISSING_CREDENTIALS_ERROR", "exports", "OIDC_VERSION", "OIDC_WORKFLOWS", "Map", "set", "TokenMachineWorkflow", "TokenCache", "AzureMachineWorkflow", "GCPMachineWorkflow", "K8SMachineWorkflow", "MongoDBOIDC", "<PERSON>th<PERSON><PERSON><PERSON>", "constructor", "workflow", "MongoInvalidArgumentError", "auth", "authContext", "connection", "reauthenticating", "response", "speculativeAuthenticate", "done", "credentials", "getCredentials", "reauthenticate", "execute", "prepare", "handshakeDoc", "result", "speculativeAuth", "MongoMissingCredentialsError"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\auth\\mongodb_oidc.ts"], "sourcesContent": ["import type { Document } from '../../bson';\nimport { MongoInvalidArgumentError, MongoMissingCredentialsError } from '../../error';\nimport type { HandshakeDocument } from '../connect';\nimport type { Connection } from '../connection';\nimport { type AuthContext, AuthProvider } from './auth_provider';\nimport type { MongoCredentials } from './mongo_credentials';\nimport { AzureMachineWorkflow } from './mongodb_oidc/azure_machine_workflow';\nimport { GCPMachineWorkflow } from './mongodb_oidc/gcp_machine_workflow';\nimport { K8SMachineWorkflow } from './mongodb_oidc/k8s_machine_workflow';\nimport { TokenCache } from './mongodb_oidc/token_cache';\nimport { TokenMachineWorkflow } from './mongodb_oidc/token_machine_workflow';\n\n/** Error when credentials are missing. */\nconst MISSING_CREDENTIALS_ERROR = 'AuthContext must provide credentials.';\n\n/**\n * The information returned by the server on the IDP server.\n * @public\n */\nexport interface IdPInfo {\n  /**\n   * A URL which describes the Authentication Server. This identifier should\n   * be the iss of provided access tokens, and be viable for RFC8414 metadata\n   * discovery and RFC9207 identification.\n   */\n  issuer: string;\n  /** A unique client ID for this OIDC client. */\n  clientId: string;\n  /** A list of additional scopes to request from IdP. */\n  requestScopes?: string[];\n}\n\n/**\n * The response from the IdP server with the access token and\n * optional expiration time and refresh token.\n * @public\n */\nexport interface IdPServerResponse {\n  /** The OIDC access token. */\n  accessToken: string;\n  /** The time when the access token expires. For future use. */\n  expiresInSeconds?: number;\n  /** The refresh token, if applicable, to be used by the callback to request a new token from the issuer. */\n  refreshToken?: string;\n}\n\n/**\n * The response required to be returned from the machine or\n * human callback workflows' callback.\n * @public\n */\nexport interface OIDCResponse {\n  /** The OIDC access token. */\n  accessToken: string;\n  /** The time when the access token expires. For future use. */\n  expiresInSeconds?: number;\n  /** The refresh token, if applicable, to be used by the callback to request a new token from the issuer. */\n  refreshToken?: string;\n}\n\n/**\n * The parameters that the driver provides to the user supplied\n * human or machine callback.\n *\n * The version number is used to communicate callback API changes that are not breaking but that\n * users may want to know about and review their implementation. Users may wish to check the version\n * number and throw an error if their expected version number and the one provided do not match.\n * @public\n */\nexport interface OIDCCallbackParams {\n  /** Optional username. */\n  username?: string;\n  /** The context in which to timeout the OIDC callback. */\n  timeoutContext: AbortSignal;\n  /** The current OIDC API version. */\n  version: 1;\n  /** The IdP information returned from the server. */\n  idpInfo?: IdPInfo;\n  /** The refresh token, if applicable, to be used by the callback to request a new token from the issuer. */\n  refreshToken?: string;\n}\n\n/**\n * The signature of the human or machine callback functions.\n * @public\n */\nexport type OIDCCallbackFunction = (params: OIDCCallbackParams) => Promise<OIDCResponse>;\n\n/** The current version of OIDC implementation. */\nexport const OIDC_VERSION = 1;\n\ntype EnvironmentName = 'test' | 'azure' | 'gcp' | 'k8s' | undefined;\n\n/** @internal */\nexport interface Workflow {\n  /**\n   * All device workflows must implement this method in order to get the access\n   * token and then call authenticate with it.\n   */\n  execute(\n    connection: Connection,\n    credentials: MongoCredentials,\n    response?: Document\n  ): Promise<void>;\n\n  /**\n   * Each workflow should specify the correct custom behaviour for reauthentication.\n   */\n  reauthenticate(connection: Connection, credentials: MongoCredentials): Promise<void>;\n\n  /**\n   * Get the document to add for speculative authentication.\n   */\n  speculativeAuth(connection: Connection, credentials: MongoCredentials): Promise<Document>;\n}\n\n/** @internal */\nexport const OIDC_WORKFLOWS: Map<EnvironmentName, () => Workflow> = new Map();\nOIDC_WORKFLOWS.set('test', () => new TokenMachineWorkflow(new TokenCache()));\nOIDC_WORKFLOWS.set('azure', () => new AzureMachineWorkflow(new TokenCache()));\nOIDC_WORKFLOWS.set('gcp', () => new GCPMachineWorkflow(new TokenCache()));\nOIDC_WORKFLOWS.set('k8s', () => new K8SMachineWorkflow(new TokenCache()));\n\n/**\n * OIDC auth provider.\n */\nexport class MongoDBOIDC extends AuthProvider {\n  workflow: Workflow;\n\n  /**\n   * Instantiate the auth provider.\n   */\n  constructor(workflow?: Workflow) {\n    super();\n    if (!workflow) {\n      throw new MongoInvalidArgumentError('No workflow provided to the OIDC auth provider.');\n    }\n    this.workflow = workflow;\n  }\n\n  /**\n   * Authenticate using OIDC\n   */\n  override async auth(authContext: AuthContext): Promise<void> {\n    const { connection, reauthenticating, response } = authContext;\n    if (response?.speculativeAuthenticate?.done && !reauthenticating) {\n      return;\n    }\n    const credentials = getCredentials(authContext);\n    if (reauthenticating) {\n      await this.workflow.reauthenticate(connection, credentials);\n    } else {\n      await this.workflow.execute(connection, credentials, response);\n    }\n  }\n\n  /**\n   * Add the speculative auth for the initial handshake.\n   */\n  override async prepare(\n    handshakeDoc: HandshakeDocument,\n    authContext: AuthContext\n  ): Promise<HandshakeDocument> {\n    const { connection } = authContext;\n    const credentials = getCredentials(authContext);\n    const result = await this.workflow.speculativeAuth(connection, credentials);\n    return { ...handshakeDoc, ...result };\n  }\n}\n\n/**\n * Get credentials from the auth context, throwing if they do not exist.\n */\nfunction getCredentials(authContext: AuthContext): MongoCredentials {\n  const { credentials } = authContext;\n  if (!credentials) {\n    throw new MongoMissingCredentialsError(MISSING_CREDENTIALS_ERROR);\n  }\n  return credentials;\n}\n"], "mappings": ";;;;;;AACA,MAAAA,OAAA,GAAAC,OAAA;AAGA,MAAAC,eAAA,GAAAD,OAAA;AAEA,MAAAE,wBAAA,GAAAF,OAAA;AACA,MAAAG,sBAAA,GAAAH,OAAA;AACA,MAAAI,sBAAA,GAAAJ,OAAA;AACA,MAAAK,aAAA,GAAAL,OAAA;AACA,MAAAM,wBAAA,GAAAN,OAAA;AAEA;AACA,MAAMO,yBAAyB,GAAG,uCAAuC;AA2EzE;AACaC,OAAA,CAAAC,YAAY,GAAG,CAAC;AA2B7B;AACaD,OAAA,CAAAE,cAAc,GAAyC,IAAIC,GAAG,EAAE;AAC7EH,OAAA,CAAAE,cAAc,CAACE,GAAG,CAAC,MAAM,EAAE,MAAM,IAAIN,wBAAA,CAAAO,oBAAoB,CAAC,IAAIR,aAAA,CAAAS,UAAU,EAAE,CAAC,CAAC;AAC5EN,OAAA,CAAAE,cAAc,CAACE,GAAG,CAAC,OAAO,EAAE,MAAM,IAAIV,wBAAA,CAAAa,oBAAoB,CAAC,IAAIV,aAAA,CAAAS,UAAU,EAAE,CAAC,CAAC;AAC7EN,OAAA,CAAAE,cAAc,CAACE,GAAG,CAAC,KAAK,EAAE,MAAM,IAAIT,sBAAA,CAAAa,kBAAkB,CAAC,IAAIX,aAAA,CAAAS,UAAU,EAAE,CAAC,CAAC;AACzEN,OAAA,CAAAE,cAAc,CAACE,GAAG,CAAC,KAAK,EAAE,MAAM,IAAIR,sBAAA,CAAAa,kBAAkB,CAAC,IAAIZ,aAAA,CAAAS,UAAU,EAAE,CAAC,CAAC;AAEzE;;;AAGA,MAAaI,WAAY,SAAQjB,eAAA,CAAAkB,YAAY;EAG3C;;;EAGAC,YAAYC,QAAmB;IAC7B,KAAK,EAAE;IACP,IAAI,CAACA,QAAQ,EAAE;MACb,MAAM,IAAItB,OAAA,CAAAuB,yBAAyB,CAAC,iDAAiD,CAAC;IACxF;IACA,IAAI,CAACD,QAAQ,GAAGA,QAAQ;EAC1B;EAEA;;;EAGS,MAAME,IAAIA,CAACC,WAAwB;IAC1C,MAAM;MAAEC,UAAU;MAAEC,gBAAgB;MAAEC;IAAQ,CAAE,GAAGH,WAAW;IAC9D,IAAIG,QAAQ,EAAEC,uBAAuB,EAAEC,IAAI,IAAI,CAACH,gBAAgB,EAAE;MAChE;IACF;IACA,MAAMI,WAAW,GAAGC,cAAc,CAACP,WAAW,CAAC;IAC/C,IAAIE,gBAAgB,EAAE;MACpB,MAAM,IAAI,CAACL,QAAQ,CAACW,cAAc,CAACP,UAAU,EAAEK,WAAW,CAAC;IAC7D,CAAC,MAAM;MACL,MAAM,IAAI,CAACT,QAAQ,CAACY,OAAO,CAACR,UAAU,EAAEK,WAAW,EAAEH,QAAQ,CAAC;IAChE;EACF;EAEA;;;EAGS,MAAMO,OAAOA,CACpBC,YAA+B,EAC/BX,WAAwB;IAExB,MAAM;MAAEC;IAAU,CAAE,GAAGD,WAAW;IAClC,MAAMM,WAAW,GAAGC,cAAc,CAACP,WAAW,CAAC;IAC/C,MAAMY,MAAM,GAAG,MAAM,IAAI,CAACf,QAAQ,CAACgB,eAAe,CAACZ,UAAU,EAAEK,WAAW,CAAC;IAC3E,OAAO;MAAE,GAAGK,YAAY;MAAE,GAAGC;IAAM,CAAE;EACvC;;AAzCF5B,OAAA,CAAAU,WAAA,GAAAA,WAAA;AA4CA;;;AAGA,SAASa,cAAcA,CAACP,WAAwB;EAC9C,MAAM;IAAEM;EAAW,CAAE,GAAGN,WAAW;EACnC,IAAI,CAACM,WAAW,EAAE;IAChB,MAAM,IAAI/B,OAAA,CAAAuC,4BAA4B,CAAC/B,yBAAyB,CAAC;EACnE;EACA,OAAOuB,WAAW;AACpB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}