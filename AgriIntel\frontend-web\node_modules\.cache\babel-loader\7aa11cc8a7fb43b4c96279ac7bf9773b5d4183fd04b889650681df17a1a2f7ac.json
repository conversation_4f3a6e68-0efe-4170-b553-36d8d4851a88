{"ast": null, "code": "export default function bindActionCreators(actionCreators, dispatch) {\n  const boundActionCreators = {};\n  for (const key in actionCreators) {\n    const actionCreator = actionCreators[key];\n    if (typeof actionCreator === 'function') {\n      boundActionCreators[key] = function () {\n        return dispatch(actionCreator(...arguments));\n      };\n    }\n  }\n  return boundActionCreators;\n}", "map": {"version": 3, "names": ["bindActionCreators", "actionCreators", "dispatch", "boundActionCreators", "key", "actionCreator", "arguments"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-redux/es/utils/bindActionCreators.js"], "sourcesContent": ["export default function bindActionCreators(actionCreators, dispatch) {\n  const boundActionCreators = {};\n\n  for (const key in actionCreators) {\n    const actionCreator = actionCreators[key];\n\n    if (typeof actionCreator === 'function') {\n      boundActionCreators[key] = (...args) => dispatch(actionCreator(...args));\n    }\n  }\n\n  return boundActionCreators;\n}"], "mappings": "AAAA,eAAe,SAASA,kBAAkBA,CAACC,cAAc,EAAEC,QAAQ,EAAE;EACnE,MAAMC,mBAAmB,GAAG,CAAC,CAAC;EAE9B,KAAK,MAAMC,GAAG,IAAIH,cAAc,EAAE;IAChC,MAAMI,aAAa,GAAGJ,cAAc,CAACG,GAAG,CAAC;IAEzC,IAAI,OAAOC,aAAa,KAAK,UAAU,EAAE;MACvCF,mBAAmB,CAACC,GAAG,CAAC,GAAG;QAAA,OAAaF,QAAQ,CAACG,aAAa,CAAC,GAAAC,SAAO,CAAC,CAAC;MAAA;IAC1E;EACF;EAEA,OAAOH,mBAAmB;AAC5B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}