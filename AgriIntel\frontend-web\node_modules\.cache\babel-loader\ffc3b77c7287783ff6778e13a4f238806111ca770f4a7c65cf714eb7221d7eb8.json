{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"children\", \"count\", \"parent\", \"i18nKey\", \"context\", \"tOptions\", \"values\", \"defaults\", \"components\", \"ns\", \"i18n\", \"t\", \"shouldUnescape\"];\nimport { useContext } from 'react';\nimport { nodesToString, Trans as TransWithoutContext } from './TransWithoutContext.js';\nimport { getI18n, I18nContext } from './context.js';\nexport { nodesToString };\nexport function Trans(_ref) {\n  let {\n      children,\n      count,\n      parent,\n      i18nKey,\n      context,\n      tOptions = {},\n      values,\n      defaults,\n      components,\n      ns,\n      i18n: i18nFromProps,\n      t: tFromProps,\n      shouldUnescape\n    } = _ref,\n    additionalProps = _objectWithoutProperties(_ref, _excluded);\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  const t = tFromProps || i18n && i18n.t.bind(i18n);\n  return TransWithoutContext(_objectSpread({\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions,\n    values,\n    defaults,\n    components,\n    ns: ns || t && t.ns || defaultNSFromContext || i18n && i18n.options && i18n.options.defaultNS,\n    i18n,\n    t: tFromProps,\n    shouldUnescape\n  }, additionalProps));\n}", "map": {"version": 3, "names": ["useContext", "nodesToString", "Trans", "TransWithoutContext", "getI18n", "I18nContext", "_ref", "children", "count", "parent", "i18nKey", "context", "tOptions", "values", "defaults", "components", "ns", "i18n", "i18nFromProps", "t", "tFromProps", "shouldUnescape", "additionalProps", "_objectWithoutProperties", "_excluded", "i18nFromContext", "defaultNS", "defaultNSFromContext", "bind", "_objectSpread", "options"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-i18next/dist/es/Trans.js"], "sourcesContent": ["import { useContext } from 'react';\nimport { nodesToString, Trans as TransWithoutContext } from './TransWithoutContext.js';\nimport { getI18n, I18nContext } from './context.js';\nexport { nodesToString };\nexport function Trans(_ref) {\n  let {\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions = {},\n    values,\n    defaults,\n    components,\n    ns,\n    i18n: i18nFromProps,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  } = _ref;\n  const {\n    i18n: i18nFromContext,\n    defaultNS: defaultNSFromContext\n  } = useContext(I18nContext) || {};\n  const i18n = i18nFromProps || i18nFromContext || getI18n();\n  const t = tFromProps || i18n && i18n.t.bind(i18n);\n  return TransWithoutContext({\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions,\n    values,\n    defaults,\n    components,\n    ns: ns || t && t.ns || defaultNSFromContext || i18n && i18n.options && i18n.options.defaultNS,\n    i18n,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  });\n}"], "mappings": ";;;AAAA,SAASA,UAAU,QAAQ,OAAO;AAClC,SAASC,aAAa,EAAEC,KAAK,IAAIC,mBAAmB,QAAQ,0BAA0B;AACtF,SAASC,OAAO,EAAEC,WAAW,QAAQ,cAAc;AACnD,SAASJ,aAAa;AACtB,OAAO,SAASC,KAAKA,CAACI,IAAI,EAAE;EAC1B,IAAI;MACFC,QAAQ;MACRC,KAAK;MACLC,MAAM;MACNC,OAAO;MACPC,OAAO;MACPC,QAAQ,GAAG,CAAC,CAAC;MACbC,MAAM;MACNC,QAAQ;MACRC,UAAU;MACVC,EAAE;MACFC,IAAI,EAAEC,aAAa;MACnBC,CAAC,EAAEC,UAAU;MACbC;IAEF,CAAC,GAAGf,IAAI;IADHgB,eAAe,GAAAC,wBAAA,CAChBjB,IAAI,EAAAkB,SAAA;EACR,MAAM;IACJP,IAAI,EAAEQ,eAAe;IACrBC,SAAS,EAAEC;EACb,CAAC,GAAG3B,UAAU,CAACK,WAAW,CAAC,IAAI,CAAC,CAAC;EACjC,MAAMY,IAAI,GAAGC,aAAa,IAAIO,eAAe,IAAIrB,OAAO,CAAC,CAAC;EAC1D,MAAMe,CAAC,GAAGC,UAAU,IAAIH,IAAI,IAAIA,IAAI,CAACE,CAAC,CAACS,IAAI,CAACX,IAAI,CAAC;EACjD,OAAOd,mBAAmB,CAAA0B,aAAA;IACxBtB,QAAQ;IACRC,KAAK;IACLC,MAAM;IACNC,OAAO;IACPC,OAAO;IACPC,QAAQ;IACRC,MAAM;IACNC,QAAQ;IACRC,UAAU;IACVC,EAAE,EAAEA,EAAE,IAAIG,CAAC,IAAIA,CAAC,CAACH,EAAE,IAAIW,oBAAoB,IAAIV,IAAI,IAAIA,IAAI,CAACa,OAAO,IAAIb,IAAI,CAACa,OAAO,CAACJ,SAAS;IAC7FT,IAAI;IACJE,CAAC,EAAEC,UAAU;IACbC;EAAc,GACXC,eAAe,CACnB,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}