@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
    font-family: 'Roboto', system-ui, sans-serif;
  }
  
  * {
    @apply border-gray-200;
  }
}

@layer components {
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }
  
  .btn-success {
    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }
  
  .btn-warning {
    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }
  
  .btn-danger {
    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }
  
  .btn-outline {
    @apply btn border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-primary-500;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-soft border border-gray-200;
  }
  
  .card-header {
    @apply px-6 py-4 border-b border-gray-200;
  }
  
  .card-body {
    @apply px-6 py-4;
  }
  
  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-lg;
  }
  
  .form-input {
    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;
  }
  
  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-1;
  }
  
  .form-error {
    @apply mt-1 text-sm text-danger-600;
  }
  
  .table {
    @apply min-w-full divide-y divide-gray-200;
  }
  
  .table-header {
    @apply bg-gray-50;
  }
  
  .table-header-cell {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }
  
  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }
  
  .table-cell {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-primary {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-success {
    @apply badge bg-success-100 text-success-800;
  }
  
  .badge-warning {
    @apply badge bg-warning-100 text-warning-800;
  }
  
  .badge-danger {
    @apply badge bg-danger-100 text-danger-800;
  }
  
  .badge-secondary {
    @apply badge bg-secondary-100 text-secondary-800;
  }
  
  .sidebar-nav-item {
    @apply flex items-center px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-200;
  }
  
  .sidebar-nav-item.active {
    @apply text-primary-600 bg-primary-50 border-r-2 border-primary-600;
  }
  
  .loading-spinner {
    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600;
  }
  
  .stat-card {
    @apply card p-6;
  }
  
  .stat-value {
    @apply text-3xl font-bold text-gray-900;
  }
  
  .stat-label {
    @apply text-sm font-medium text-gray-500;
  }
  
  .stat-change {
    @apply text-sm font-medium;
  }
  
  .stat-change.positive {
    @apply text-success-600;
  }
  
  .stat-change.negative {
    @apply text-danger-600;
  }
}

@layer utilities {
  .text-shadow {
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  .text-shadow-lg {
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
  }
  
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }
  
  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }
  
  .gradient-primary {
    background: linear-gradient(135deg, #0f766e 0%, #14b8a6 100%);
  }
  
  .gradient-secondary {
    background: linear-gradient(135deg, #374151 0%, #6b7280 100%);
  }
  
  .gradient-accent {
    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);
  }
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
  
  .print-break {
    page-break-before: always;
  }
  
  .print-avoid-break {
    page-break-inside: avoid;
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.bounce-in {
  animation: bounceIn 0.6s ease-out;
}
