import React, { useState, useEffect } from 'react';
import { CustomButton } from '../../components/common';
import { Box, Card, CardContent, Typography, Grid, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper, useTheme, alpha, Divider, CircularProgress } from '@mui/material';
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  Legend, 
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { Add, Download, FilterList } from '@mui/icons-material';
import { motion } from 'framer-motion';
// MongoDB collection import removed - using API calls instead
import { Link } from 'react-router-dom';

interface FeedingRecord {
  id: string;
  date: Date;
  animalGroupId: string;
  animalGroup: string;
  feedType: string;
  quantity: number;
  unit: string;
  unitCost: number;
  totalCost: number;
  location: string;
  notes?: string;
}

interface FeedUsage {
  name: string;
  value: number;
}

interface CostByGroup {
  name: string;
  value: number;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884d8', '#82ca9d'];

const FeedingDashboard: React.FC = () => {
  const theme = useTheme();
  const [loading, setLoading] = useState<boolean>(true);
  const [feedingRecords, setFeedingRecords] = useState<FeedingRecord[]>([]);
  const [feedUsageData, setFeedUsageData] = useState<FeedUsage[]>([]);
  const [costByGroupData, setCostByGroupData] = useState<CostByGroup[]>([]);
  const [totalFeedCost, setTotalFeedCost] = useState<number>(0);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        
        // Get feeding records from MongoDB
        const feedingCollection = await getCollection('feeding_records');
        const records = await feedingCollection.find({}).toArray();
        
        // Process the data
        const processedRecords = records.map((record: any) => ({
          ...record,
          date: new Date(record.date),
          totalCost: record.totalCost || record.quantity * record.unitCost || 0
        }));
        
        setFeedingRecords(processedRecords);
        
        // Calculate feed usage by type
        const feedUsage: Record<string, number> = {};
        processedRecords.forEach((record: FeedingRecord) => {
          const feedType = record.feedType;
          if (!feedUsage[feedType]) {
            feedUsage[feedType] = 0;
          }
          feedUsage[feedType] += record.quantity;
        });
        
        const feedUsageArray = Object.entries(feedUsage).map(([name, value]) => ({
          name,
          value
        }));
        
        setFeedUsageData(feedUsageArray);
        
        // Calculate cost by animal group
        const costByGroup: Record<string, number> = {};
        processedRecords.forEach((record: FeedingRecord) => {
          const group = record.animalGroup || 'Unknown';
          if (!costByGroup[group]) {
            costByGroup[group] = 0;
          }
          costByGroup[group] += record.totalCost;
        });
        
        const costByGroupArray = Object.entries(costByGroup).map(([name, value]) => ({
          name,
          value
        }));
        
        setCostByGroupData(costByGroupArray);
        
        // Calculate total feed cost
        const total = processedRecords.reduce((sum, record) => sum + record.totalCost, 0);
        setTotalFeedCost(total);
      } catch (error) {
        console.error('Error fetching feeding data:', error);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, []);

  // If no real data is available, use sample data
  useEffect(() => {
    if (!loading && feedingRecords.length === 0) {
      // Sample data
      const sampleRecords: FeedingRecord[] = [
        {
          id: '1',
          date: new Date('2023-03-15'),
          animalGroupId: 'beef-cattle',
          animalGroup: 'Beef Cattle',
          feedType: 'Lucerne',
          quantity: 1000,
          unit: 'kg',
          unitCost: 5.5,
          totalCost: 5500,
          location: 'Main Kraal'
        },
        {
          id: '2',
          date: new Date('2023-03-15'),
          animalGroupId: 'beef-cattle',
          animalGroup: 'Beef Cattle',
          feedType: 'Maize',
          quantity: 450,
          unit: 'kg',
          unitCost: 4.2,
          totalCost: 1890,
          location: 'Main Kraal'
        },
        {
          id: '3',
          date: new Date('2023-03-15'),
          animalGroupId: 'breeding-ewes',
          animalGroup: 'Breeding Ewes',
          feedType: 'Eragrostis',
          quantity: 150,
          unit: 'kg',
          unitCost: 8,
          totalCost: 1200,
          location: 'East Paddock'
        },
        {
          id: '4',
          date: new Date('2023-03-14'),
          animalGroupId: 'beef-cattle',
          animalGroup: 'Beef Cattle',
          feedType: 'Lucerne',
          quantity: 950,
          unit: 'kg',
          unitCost: 5.5,
          totalCost: 5225,
          location: 'Main Kraal'
        },
        {
          id: '5',
          date: new Date('2023-03-14'),
          animalGroupId: 'beef-cattle',
          animalGroup: 'Beef Cattle',
          feedType: 'Maize',
          quantity: 400,
          unit: 'kg',
          unitCost: 4.2,
          totalCost: 1680,
          location: 'Main Kraal'
        },
        {
          id: '6',
          date: new Date('2023-03-14'),
          animalGroupId: 'boer-goats',
          animalGroup: 'Boer Goats',
          feedType: 'Teff',
          quantity: 50,
          unit: 'kg',
          unitCost: 9.1,
          totalCost: 455,
          location: 'West Paddock'
        }
      ];
      
      setFeedingRecords(sampleRecords);
      
      // Calculate feed usage by type
      const feedUsage: Record<string, number> = {
        'Lucerne': 1950,
        'Maize': 850,
        'Eragrostis': 150,
        'Teff': 50
      };
      
      const feedUsageArray = Object.entries(feedUsage).map(([name, value]) => ({
        name,
        value
      }));
      
      setFeedUsageData(feedUsageArray);
      
      // Calculate cost by animal group
      const costByGroup: Record<string, number> = {
        'Beef Cattle': 14295,
        'Breeding Ewes': 1200,
        'Boer Goats': 455
      };
      
      const costByGroupArray = Object.entries(costByGroup).map(([name, value]) => ({
        name,
        value
      }));
      
      setCostByGroupData(costByGroupArray);
      
      // Calculate total feed cost
      setTotalFeedCost(15950);
    }
  }, [loading, feedingRecords.length]);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      {/* Header */}
      <Box 
        sx={{ 
          display: 'flex', 
          justifyContent: 'space-between', 
          alignItems: 'center',
          mb: 3
        }}
      >
        <Box>
          <Typography variant="h4" component="h1" gutterBottom>
            Feeding Records
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Track and manage all feeding activities and consumption
          </Typography>
        </Box>
        <CustomButton
          variant="contained"
          startIcon={<Add />}
          component={Link}
          to="/feeding/records/add"
          sx={{ height: 40 }}
        >
          Add Record
        </CustomButton>
      </Box>

      {/* Summary Cards */}
      <Card 
        sx={{ 
          mb: 4, 
          borderRadius: 2,
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          background: theme.palette.primary.main,
          color: 'white'
        }}
      >
        <CardContent>
          <Typography variant="h5" gutterBottom>
            Feeding Summary
          </Typography>
          
          <Grid container spacing={3} sx={{ mt: 1 }}>
            <Grid item xs={12} md={6}>
              <Box sx={{ display: 'flex', flexDirection: 'column', height: '100%' }}>
                <Typography variant="h6" gutterBottom>
                  Feeding Summary
                </Typography>
                <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h3" component="div" sx={{ mb: 1 }}>
                      R {totalFeedCost.toLocaleString()}
                    </Typography>
                    <Typography variant="body2">
                      Total Feed Cost
                    </Typography>
                  </Box>
                </Box>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Feed Usage by Type */}
      <Card 
        sx={{ 
          mb: 4, 
          borderRadius: 2,
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          background: alpha(theme.palette.primary.main, 0.1)
        }}
      >
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Feed Usage by Type
          </Typography>
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer component={Paper} elevation={0}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Feed Type</TableCell>
                    <TableCell align="right">Quantity</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {feedUsageData.map((row) => (
                    <TableRow key={row.name}>
                      <TableCell component="th" scope="row">
                        {row.name}
                      </TableCell>
                      <TableCell align="right">{row.value} kg</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
          
          <Box sx={{ height: 300, mt: 3 }}>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={feedUsageData}
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
              >
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="name" />
                <YAxis />
                <Tooltip formatter={(value) => [`${value} kg`, 'Quantity']} />
                <Legend />
                <Bar dataKey="value" name="Quantity (kg)" fill={theme.palette.primary.main} />
              </BarChart>
            </ResponsiveContainer>
          </Box>
        </CardContent>
      </Card>

      {/* Cost by Animal Group */}
      <Card 
        sx={{ 
          mb: 4, 
          borderRadius: 2,
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          background: alpha(theme.palette.primary.main, 0.1)
        }}
      >
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Cost by Animal Group
          </Typography>
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer component={Paper} elevation={0}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Animal Group</TableCell>
                    <TableCell align="right">Total Cost</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {costByGroupData.map((row) => (
                    <TableRow key={row.name}>
                      <TableCell component="th" scope="row">
                        {row.name}
                      </TableCell>
                      <TableCell align="right">R {row.value.toLocaleString()}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
          
          <Box sx={{ height: 300, mt: 3 }}>
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={costByGroupData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                  outerRadius={100}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {costByGroupData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`R ${value.toLocaleString()}`, 'Cost']} />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </Box>
        </CardContent>
      </Card>

      {/* Recent Feeding Records */}
      <Card 
        sx={{ 
          mb: 4, 
          borderRadius: 2,
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          background: alpha(theme.palette.primary.main, 0.1)
        }}
      >
        <CardContent>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
            <Typography variant="h6">
              Recent Feeding Records
            </Typography>
            <Box>
              <CustomButton 
                startIcon={<FilterList />} 
                sx={{ mr: 1 }}
                variant="outlined"
                size="small"
              >
                Filter
              </CustomButton>
              <CustomButton 
                startIcon={<Download />} 
                variant="outlined"
                size="small"
              >
                Export
              </CustomButton>
            </Box>
          </Box>
          
          {loading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
              <CircularProgress />
            </Box>
          ) : (
            <TableContainer component={Paper} elevation={0}>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Date</TableCell>
                    <TableCell>Animal Group</TableCell>
                    <TableCell>Feed Type</TableCell>
                    <TableCell align="right">Quantity</TableCell>
                    <TableCell align="right">Cost</TableCell>
                    <TableCell>Location</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {feedingRecords.slice(0, 5).map((record) => (
                    <TableRow key={record.id}>
                      <TableCell>
                        {record.date.toLocaleDateString('en-ZA')}
                      </TableCell>
                      <TableCell>{record.animalGroup}</TableCell>
                      <TableCell>{record.feedType}</TableCell>
                      <TableCell align="right">{record.quantity} {record.unit}</TableCell>
                      <TableCell align="right">R {record.totalCost.toLocaleString()}</TableCell>
                      <TableCell>{record.location}</TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          )}
          
          <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
            <CustomButton 
              component={Link}
              to="/feeding/records"
              variant="text"
              color="primary"
            >
              View All Records
            </CustomButton>
          </Box>
        </CardContent>
      </Card>

      {/* All Feeding Records */}
      <Card 
        sx={{ 
          borderRadius: 2,
          boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
          background: alpha(theme.palette.primary.main, 0.1)
        }}
      >
        <CardContent>
          <Typography variant="h6" gutterBottom>
            All Feeding Records
          </Typography>
          
          <Box sx={{ p: 2, backgroundColor: theme.palette.background.paper, borderRadius: 1 }}>
            <Typography variant="subtitle1" gutterBottom fontWeight="bold">
              Complete Feeding Records
            </Typography>
            
            <TableContainer>
              <Table size="small">
                <TableHead>
                  <TableRow>
                    <TableCell>Date</TableCell>
                    <TableCell>Animal Group</TableCell>
                    <TableCell>Feed Type</TableCell>
                    <TableCell align="right">Quantity</TableCell>
                    <TableCell align="right">Cost</TableCell>
                    <TableCell>Location</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {/* Placeholder rows */}
                  <TableRow>
                    <TableCell colSpan={6} align="center">
                      <CustomButton 
                        component={Link}
                        to="/feeding/records"
                        variant="text"
                        color="primary"
                      >
                        View Complete Records
                      </CustomButton>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableContainer>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default FeedingDashboard;
