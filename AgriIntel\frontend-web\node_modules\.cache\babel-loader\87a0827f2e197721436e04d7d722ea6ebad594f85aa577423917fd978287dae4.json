{"ast": null, "code": "\"use strict\";\n\nconst urlencoded = require(\"./urlencoded\");\nexports.implementation = class URLSearchParamsImpl {\n  constructor(globalObject, constructorArgs, {\n    doNotStripQMark = false\n  }) {\n    let init = constructorArgs[0];\n    this._list = [];\n    this._url = null;\n    if (!doNotStripQMark && typeof init === \"string\" && init[0] === \"?\") {\n      init = init.slice(1);\n    }\n    if (Array.isArray(init)) {\n      for (const pair of init) {\n        if (pair.length !== 2) {\n          throw new TypeError(\"Failed to construct 'URLSearchParams': parameter 1 sequence's element does not \" + \"contain exactly two elements.\");\n        }\n        this._list.push([pair[0], pair[1]]);\n      }\n    } else if (typeof init === \"object\" && Object.getPrototypeOf(init) === null) {\n      for (const name of Object.keys(init)) {\n        const value = init[name];\n        this._list.push([name, value]);\n      }\n    } else {\n      this._list = urlencoded.parseUrlencodedString(init);\n    }\n  }\n  _updateSteps() {\n    if (this._url !== null) {\n      let serializedQuery = urlencoded.serializeUrlencoded(this._list);\n      if (serializedQuery === \"\") {\n        serializedQuery = null;\n      }\n      this._url._url.query = serializedQuery;\n    }\n  }\n  get size() {\n    return this._list.length;\n  }\n  append(name, value) {\n    this._list.push([name, value]);\n    this._updateSteps();\n  }\n  delete(name, value) {\n    let i = 0;\n    while (i < this._list.length) {\n      if (this._list[i][0] === name && (value === undefined || this._list[i][1] === value)) {\n        this._list.splice(i, 1);\n      } else {\n        i++;\n      }\n    }\n    this._updateSteps();\n  }\n  get(name) {\n    for (const tuple of this._list) {\n      if (tuple[0] === name) {\n        return tuple[1];\n      }\n    }\n    return null;\n  }\n  getAll(name) {\n    const output = [];\n    for (const tuple of this._list) {\n      if (tuple[0] === name) {\n        output.push(tuple[1]);\n      }\n    }\n    return output;\n  }\n  has(name, value) {\n    for (const tuple of this._list) {\n      if (tuple[0] === name && (value === undefined || tuple[1] === value)) {\n        return true;\n      }\n    }\n    return false;\n  }\n  set(name, value) {\n    let found = false;\n    let i = 0;\n    while (i < this._list.length) {\n      if (this._list[i][0] === name) {\n        if (found) {\n          this._list.splice(i, 1);\n        } else {\n          found = true;\n          this._list[i][1] = value;\n          i++;\n        }\n      } else {\n        i++;\n      }\n    }\n    if (!found) {\n      this._list.push([name, value]);\n    }\n    this._updateSteps();\n  }\n  sort() {\n    this._list.sort((a, b) => {\n      if (a[0] < b[0]) {\n        return -1;\n      }\n      if (a[0] > b[0]) {\n        return 1;\n      }\n      return 0;\n    });\n    this._updateSteps();\n  }\n  [Symbol.iterator]() {\n    return this._list[Symbol.iterator]();\n  }\n  toString() {\n    return urlencoded.serializeUrlencoded(this._list);\n  }\n};", "map": {"version": 3, "names": ["u<PERSON><PERSON><PERSON>", "require", "exports", "implementation", "URLSearchParamsImpl", "constructor", "globalObject", "constructorArgs", "doNotStripQMark", "init", "_list", "_url", "slice", "Array", "isArray", "pair", "length", "TypeError", "push", "Object", "getPrototypeOf", "name", "keys", "value", "parseUrlencodedString", "_updateSteps", "serializedQuery", "serializeUrlencoded", "query", "size", "append", "delete", "i", "undefined", "splice", "get", "tuple", "getAll", "output", "has", "set", "found", "sort", "a", "b", "Symbol", "iterator", "toString"], "sources": ["C:/Users/<USER>/node_modules/mongodb-connection-string-url/node_modules/whatwg-url/lib/URLSearchParams-impl.js"], "sourcesContent": ["\"use strict\";\nconst urlencoded = require(\"./urlencoded\");\n\nexports.implementation = class URLSearchParamsImpl {\n  constructor(globalObject, constructorArgs, { doNotStripQMark = false }) {\n    let init = constructorArgs[0];\n    this._list = [];\n    this._url = null;\n\n    if (!doNotStripQMark && typeof init === \"string\" && init[0] === \"?\") {\n      init = init.slice(1);\n    }\n\n    if (Array.isArray(init)) {\n      for (const pair of init) {\n        if (pair.length !== 2) {\n          throw new TypeError(\"Failed to construct 'URLSearchParams': parameter 1 sequence's element does not \" +\n                              \"contain exactly two elements.\");\n        }\n        this._list.push([pair[0], pair[1]]);\n      }\n    } else if (typeof init === \"object\" && Object.getPrototypeOf(init) === null) {\n      for (const name of Object.keys(init)) {\n        const value = init[name];\n        this._list.push([name, value]);\n      }\n    } else {\n      this._list = urlencoded.parseUrlencodedString(init);\n    }\n  }\n\n  _updateSteps() {\n    if (this._url !== null) {\n      let serializedQuery = urlencoded.serializeUrlencoded(this._list);\n      if (serializedQuery === \"\") {\n        serializedQuery = null;\n      }\n\n      this._url._url.query = serializedQuery;\n    }\n  }\n\n  get size() {\n    return this._list.length;\n  }\n\n  append(name, value) {\n    this._list.push([name, value]);\n    this._updateSteps();\n  }\n\n  delete(name, value) {\n    let i = 0;\n    while (i < this._list.length) {\n      if (this._list[i][0] === name && (value === undefined || this._list[i][1] === value)) {\n        this._list.splice(i, 1);\n      } else {\n        i++;\n      }\n    }\n    this._updateSteps();\n  }\n\n  get(name) {\n    for (const tuple of this._list) {\n      if (tuple[0] === name) {\n        return tuple[1];\n      }\n    }\n    return null;\n  }\n\n  getAll(name) {\n    const output = [];\n    for (const tuple of this._list) {\n      if (tuple[0] === name) {\n        output.push(tuple[1]);\n      }\n    }\n    return output;\n  }\n\n  has(name, value) {\n    for (const tuple of this._list) {\n      if (tuple[0] === name && (value === undefined || tuple[1] === value)) {\n        return true;\n      }\n    }\n    return false;\n  }\n\n  set(name, value) {\n    let found = false;\n    let i = 0;\n    while (i < this._list.length) {\n      if (this._list[i][0] === name) {\n        if (found) {\n          this._list.splice(i, 1);\n        } else {\n          found = true;\n          this._list[i][1] = value;\n          i++;\n        }\n      } else {\n        i++;\n      }\n    }\n    if (!found) {\n      this._list.push([name, value]);\n    }\n    this._updateSteps();\n  }\n\n  sort() {\n    this._list.sort((a, b) => {\n      if (a[0] < b[0]) {\n        return -1;\n      }\n      if (a[0] > b[0]) {\n        return 1;\n      }\n      return 0;\n    });\n\n    this._updateSteps();\n  }\n\n  [Symbol.iterator]() {\n    return this._list[Symbol.iterator]();\n  }\n\n  toString() {\n    return urlencoded.serializeUrlencoded(this._list);\n  }\n};\n"], "mappings": "AAAA,YAAY;;AACZ,MAAMA,UAAU,GAAGC,OAAO,CAAC,cAAc,CAAC;AAE1CC,OAAO,CAACC,cAAc,GAAG,MAAMC,mBAAmB,CAAC;EACjDC,WAAWA,CAACC,YAAY,EAAEC,eAAe,EAAE;IAAEC,eAAe,GAAG;EAAM,CAAC,EAAE;IACtE,IAAIC,IAAI,GAAGF,eAAe,CAAC,CAAC,CAAC;IAC7B,IAAI,CAACG,KAAK,GAAG,EAAE;IACf,IAAI,CAACC,IAAI,GAAG,IAAI;IAEhB,IAAI,CAACH,eAAe,IAAI,OAAOC,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAAC,CAAC,CAAC,KAAK,GAAG,EAAE;MACnEA,IAAI,GAAGA,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC;IACtB;IAEA,IAAIC,KAAK,CAACC,OAAO,CAACL,IAAI,CAAC,EAAE;MACvB,KAAK,MAAMM,IAAI,IAAIN,IAAI,EAAE;QACvB,IAAIM,IAAI,CAACC,MAAM,KAAK,CAAC,EAAE;UACrB,MAAM,IAAIC,SAAS,CAAC,iFAAiF,GACjF,+BAA+B,CAAC;QACtD;QACA,IAAI,CAACP,KAAK,CAACQ,IAAI,CAAC,CAACH,IAAI,CAAC,CAAC,CAAC,EAAEA,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;MACrC;IACF,CAAC,MAAM,IAAI,OAAON,IAAI,KAAK,QAAQ,IAAIU,MAAM,CAACC,cAAc,CAACX,IAAI,CAAC,KAAK,IAAI,EAAE;MAC3E,KAAK,MAAMY,IAAI,IAAIF,MAAM,CAACG,IAAI,CAACb,IAAI,CAAC,EAAE;QACpC,MAAMc,KAAK,GAAGd,IAAI,CAACY,IAAI,CAAC;QACxB,IAAI,CAACX,KAAK,CAACQ,IAAI,CAAC,CAACG,IAAI,EAAEE,KAAK,CAAC,CAAC;MAChC;IACF,CAAC,MAAM;MACL,IAAI,CAACb,KAAK,GAAGV,UAAU,CAACwB,qBAAqB,CAACf,IAAI,CAAC;IACrD;EACF;EAEAgB,YAAYA,CAAA,EAAG;IACb,IAAI,IAAI,CAACd,IAAI,KAAK,IAAI,EAAE;MACtB,IAAIe,eAAe,GAAG1B,UAAU,CAAC2B,mBAAmB,CAAC,IAAI,CAACjB,KAAK,CAAC;MAChE,IAAIgB,eAAe,KAAK,EAAE,EAAE;QAC1BA,eAAe,GAAG,IAAI;MACxB;MAEA,IAAI,CAACf,IAAI,CAACA,IAAI,CAACiB,KAAK,GAAGF,eAAe;IACxC;EACF;EAEA,IAAIG,IAAIA,CAAA,EAAG;IACT,OAAO,IAAI,CAACnB,KAAK,CAACM,MAAM;EAC1B;EAEAc,MAAMA,CAACT,IAAI,EAAEE,KAAK,EAAE;IAClB,IAAI,CAACb,KAAK,CAACQ,IAAI,CAAC,CAACG,IAAI,EAAEE,KAAK,CAAC,CAAC;IAC9B,IAAI,CAACE,YAAY,CAAC,CAAC;EACrB;EAEAM,MAAMA,CAACV,IAAI,EAAEE,KAAK,EAAE;IAClB,IAAIS,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAG,IAAI,CAACtB,KAAK,CAACM,MAAM,EAAE;MAC5B,IAAI,IAAI,CAACN,KAAK,CAACsB,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKX,IAAI,KAAKE,KAAK,KAAKU,SAAS,IAAI,IAAI,CAACvB,KAAK,CAACsB,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKT,KAAK,CAAC,EAAE;QACpF,IAAI,CAACb,KAAK,CAACwB,MAAM,CAACF,CAAC,EAAE,CAAC,CAAC;MACzB,CAAC,MAAM;QACLA,CAAC,EAAE;MACL;IACF;IACA,IAAI,CAACP,YAAY,CAAC,CAAC;EACrB;EAEAU,GAAGA,CAACd,IAAI,EAAE;IACR,KAAK,MAAMe,KAAK,IAAI,IAAI,CAAC1B,KAAK,EAAE;MAC9B,IAAI0B,KAAK,CAAC,CAAC,CAAC,KAAKf,IAAI,EAAE;QACrB,OAAOe,KAAK,CAAC,CAAC,CAAC;MACjB;IACF;IACA,OAAO,IAAI;EACb;EAEAC,MAAMA,CAAChB,IAAI,EAAE;IACX,MAAMiB,MAAM,GAAG,EAAE;IACjB,KAAK,MAAMF,KAAK,IAAI,IAAI,CAAC1B,KAAK,EAAE;MAC9B,IAAI0B,KAAK,CAAC,CAAC,CAAC,KAAKf,IAAI,EAAE;QACrBiB,MAAM,CAACpB,IAAI,CAACkB,KAAK,CAAC,CAAC,CAAC,CAAC;MACvB;IACF;IACA,OAAOE,MAAM;EACf;EAEAC,GAAGA,CAAClB,IAAI,EAAEE,KAAK,EAAE;IACf,KAAK,MAAMa,KAAK,IAAI,IAAI,CAAC1B,KAAK,EAAE;MAC9B,IAAI0B,KAAK,CAAC,CAAC,CAAC,KAAKf,IAAI,KAAKE,KAAK,KAAKU,SAAS,IAAIG,KAAK,CAAC,CAAC,CAAC,KAAKb,KAAK,CAAC,EAAE;QACpE,OAAO,IAAI;MACb;IACF;IACA,OAAO,KAAK;EACd;EAEAiB,GAAGA,CAACnB,IAAI,EAAEE,KAAK,EAAE;IACf,IAAIkB,KAAK,GAAG,KAAK;IACjB,IAAIT,CAAC,GAAG,CAAC;IACT,OAAOA,CAAC,GAAG,IAAI,CAACtB,KAAK,CAACM,MAAM,EAAE;MAC5B,IAAI,IAAI,CAACN,KAAK,CAACsB,CAAC,CAAC,CAAC,CAAC,CAAC,KAAKX,IAAI,EAAE;QAC7B,IAAIoB,KAAK,EAAE;UACT,IAAI,CAAC/B,KAAK,CAACwB,MAAM,CAACF,CAAC,EAAE,CAAC,CAAC;QACzB,CAAC,MAAM;UACLS,KAAK,GAAG,IAAI;UACZ,IAAI,CAAC/B,KAAK,CAACsB,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGT,KAAK;UACxBS,CAAC,EAAE;QACL;MACF,CAAC,MAAM;QACLA,CAAC,EAAE;MACL;IACF;IACA,IAAI,CAACS,KAAK,EAAE;MACV,IAAI,CAAC/B,KAAK,CAACQ,IAAI,CAAC,CAACG,IAAI,EAAEE,KAAK,CAAC,CAAC;IAChC;IACA,IAAI,CAACE,YAAY,CAAC,CAAC;EACrB;EAEAiB,IAAIA,CAAA,EAAG;IACL,IAAI,CAAChC,KAAK,CAACgC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;MACxB,IAAID,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,OAAO,CAAC,CAAC;MACX;MACA,IAAID,CAAC,CAAC,CAAC,CAAC,GAAGC,CAAC,CAAC,CAAC,CAAC,EAAE;QACf,OAAO,CAAC;MACV;MACA,OAAO,CAAC;IACV,CAAC,CAAC;IAEF,IAAI,CAACnB,YAAY,CAAC,CAAC;EACrB;EAEA,CAACoB,MAAM,CAACC,QAAQ,IAAI;IAClB,OAAO,IAAI,CAACpC,KAAK,CAACmC,MAAM,CAACC,QAAQ,CAAC,CAAC,CAAC;EACtC;EAEAC,QAAQA,CAAA,EAAG;IACT,OAAO/C,UAAU,CAAC2B,mBAAmB,CAAC,IAAI,CAACjB,KAAK,CAAC;EACnD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}