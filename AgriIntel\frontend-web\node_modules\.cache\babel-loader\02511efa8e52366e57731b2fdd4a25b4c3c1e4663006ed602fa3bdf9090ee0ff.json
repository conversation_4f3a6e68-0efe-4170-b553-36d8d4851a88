{"ast": null, "code": "import { useRef as r } from \"react\";\nimport { useIsoMorphicEffect as t } from './use-iso-morphic-effect.js';\nfunction f() {\n  let e = r(!1);\n  return t(() => (e.current = !0, () => {\n    e.current = !1;\n  }), []), e;\n}\nexport { f as useIsMounted };", "map": {"version": 3, "names": ["useRef", "r", "useIsoMorphicEffect", "t", "f", "e", "current", "useIsMounted"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/hooks/use-is-mounted.js"], "sourcesContent": ["import{useRef as r}from\"react\";import{useIsoMorphicEffect as t}from'./use-iso-morphic-effect.js';function f(){let e=r(!1);return t(()=>(e.current=!0,()=>{e.current=!1}),[]),e}export{f as useIsMounted};\n"], "mappings": "AAAA,SAAOA,MAAM,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,IAAIC,CAAC,GAACJ,CAAC,CAAC,CAAC,CAAC,CAAC;EAAC,OAAOE,CAAC,CAAC,OAAKE,CAAC,CAACC,OAAO,GAAC,CAAC,CAAC,EAAC,MAAI;IAACD,CAAC,CAACC,OAAO,GAAC,CAAC,CAAC;EAAA,CAAC,CAAC,EAAC,EAAE,CAAC,EAACD,CAAC;AAAA;AAAC,SAAOD,CAAC,IAAIG,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}