{"ast": null, "code": "import u, { createContext as m, useContext as D, useMemo as l, useState as T } from \"react\";\nimport { useEvent as P } from '../../hooks/use-event.js';\nimport { useId as g } from '../../hooks/use-id.js';\nimport { useIsoMorphicEffect as E } from '../../hooks/use-iso-morphic-effect.js';\nimport { useSyncRefs as x } from '../../hooks/use-sync-refs.js';\nimport { forwardRefWithAs as y, render as R } from '../../utils/render.js';\nlet d = m(null);\nfunction f() {\n  let r = D(d);\n  if (r === null) {\n    let t = new Error(\"You used a <Description /> component, but it is not inside a relevant parent.\");\n    throw Error.captureStackTrace && Error.captureStackTrace(t, f), t;\n  }\n  return r;\n}\nfunction w() {\n  let [r, t] = T([]);\n  return [r.length > 0 ? r.join(\" \") : void 0, l(() => function (e) {\n    let i = P(s => (t(o => [...o, s]), () => t(o => {\n        let p = o.slice(),\n          c = p.indexOf(s);\n        return c !== -1 && p.splice(c, 1), p;\n      }))),\n      n = l(() => ({\n        register: i,\n        slot: e.slot,\n        name: e.name,\n        props: e.props\n      }), [i, e.slot, e.name, e.props]);\n    return u.createElement(d.Provider, {\n      value: n\n    }, e.children);\n  }, [t])];\n}\nlet I = \"p\";\nfunction S(r, t) {\n  let a = g(),\n    {\n      id: e = `headlessui-description-${a}`,\n      ...i\n    } = r,\n    n = f(),\n    s = x(t);\n  E(() => n.register(e), [e, n.register]);\n  let o = {\n    ref: s,\n    ...n.props,\n    id: e\n  };\n  return R({\n    ourProps: o,\n    theirProps: i,\n    slot: n.slot || {},\n    defaultTag: I,\n    name: n.name || \"Description\"\n  });\n}\nlet h = y(S),\n  G = Object.assign(h, {});\nexport { G as Description, w as useDescriptions };", "map": {"version": 3, "names": ["u", "createContext", "m", "useContext", "D", "useMemo", "l", "useState", "T", "useEvent", "P", "useId", "g", "useIsoMorphicEffect", "E", "useSyncRefs", "x", "forwardRefWithAs", "y", "render", "R", "d", "f", "r", "t", "Error", "captureStackTrace", "w", "length", "join", "e", "i", "s", "o", "p", "slice", "c", "indexOf", "splice", "n", "register", "slot", "name", "props", "createElement", "Provider", "value", "children", "I", "S", "a", "id", "ref", "ourProps", "theirProps", "defaultTag", "h", "G", "Object", "assign", "Description", "useDescriptions"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/components/description/description.js"], "sourcesContent": ["import u,{createContext as m,useContext as D,useMemo as l,useState as T}from\"react\";import{useEvent as P}from'../../hooks/use-event.js';import{useId as g}from'../../hooks/use-id.js';import{useIsoMorphicEffect as E}from'../../hooks/use-iso-morphic-effect.js';import{useSyncRefs as x}from'../../hooks/use-sync-refs.js';import{forwardRefWithAs as y,render as R}from'../../utils/render.js';let d=m(null);function f(){let r=D(d);if(r===null){let t=new Error(\"You used a <Description /> component, but it is not inside a relevant parent.\");throw Error.captureStackTrace&&Error.captureStackTrace(t,f),t}return r}function w(){let[r,t]=T([]);return[r.length>0?r.join(\" \"):void 0,l(()=>function(e){let i=P(s=>(t(o=>[...o,s]),()=>t(o=>{let p=o.slice(),c=p.indexOf(s);return c!==-1&&p.splice(c,1),p}))),n=l(()=>({register:i,slot:e.slot,name:e.name,props:e.props}),[i,e.slot,e.name,e.props]);return u.createElement(d.Provider,{value:n},e.children)},[t])]}let I=\"p\";function S(r,t){let a=g(),{id:e=`headlessui-description-${a}`,...i}=r,n=f(),s=x(t);E(()=>n.register(e),[e,n.register]);let o={ref:s,...n.props,id:e};return R({ourProps:o,theirProps:i,slot:n.slot||{},defaultTag:I,name:n.name||\"Description\"})}let h=y(S),G=Object.assign(h,{});export{G as Description,w as useDescriptions};\n"], "mappings": "AAAA,OAAOA,CAAC,IAAEC,aAAa,IAAIC,CAAC,EAACC,UAAU,IAAIC,CAAC,EAACC,OAAO,IAAIC,CAAC,EAACC,QAAQ,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,0BAA0B;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAAOC,mBAAmB,IAAIC,CAAC,QAAK,uCAAuC;AAAC,SAAOC,WAAW,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,QAAK,uBAAuB;AAAC,IAAIC,CAAC,GAACnB,CAAC,CAAC,IAAI,CAAC;AAAC,SAASoB,CAACA,CAAA,EAAE;EAAC,IAAIC,CAAC,GAACnB,CAAC,CAACiB,CAAC,CAAC;EAAC,IAAGE,CAAC,KAAG,IAAI,EAAC;IAAC,IAAIC,CAAC,GAAC,IAAIC,KAAK,CAAC,+EAA+E,CAAC;IAAC,MAAMA,KAAK,CAACC,iBAAiB,IAAED,KAAK,CAACC,iBAAiB,CAACF,CAAC,EAACF,CAAC,CAAC,EAACE,CAAC;EAAA;EAAC,OAAOD,CAAC;AAAA;AAAC,SAASI,CAACA,CAAA,EAAE;EAAC,IAAG,CAACJ,CAAC,EAACC,CAAC,CAAC,GAAChB,CAAC,CAAC,EAAE,CAAC;EAAC,OAAM,CAACe,CAAC,CAACK,MAAM,GAAC,CAAC,GAACL,CAAC,CAACM,IAAI,CAAC,GAAG,CAAC,GAAC,KAAK,CAAC,EAACvB,CAAC,CAAC,MAAI,UAASwB,CAAC,EAAC;IAAC,IAAIC,CAAC,GAACrB,CAAC,CAACsB,CAAC,KAAGR,CAAC,CAACS,CAAC,IAAE,CAAC,GAAGA,CAAC,EAACD,CAAC,CAAC,CAAC,EAAC,MAAIR,CAAC,CAACS,CAAC,IAAE;QAAC,IAAIC,CAAC,GAACD,CAAC,CAACE,KAAK,CAAC,CAAC;UAACC,CAAC,GAACF,CAAC,CAACG,OAAO,CAACL,CAAC,CAAC;QAAC,OAAOI,CAAC,KAAG,CAAC,CAAC,IAAEF,CAAC,CAACI,MAAM,CAACF,CAAC,EAAC,CAAC,CAAC,EAACF,CAAC;MAAA,CAAC,CAAC,CAAC,CAAC;MAACK,CAAC,GAACjC,CAAC,CAAC,OAAK;QAACkC,QAAQ,EAACT,CAAC;QAACU,IAAI,EAACX,CAAC,CAACW,IAAI;QAACC,IAAI,EAACZ,CAAC,CAACY,IAAI;QAACC,KAAK,EAACb,CAAC,CAACa;MAAK,CAAC,CAAC,EAAC,CAACZ,CAAC,EAACD,CAAC,CAACW,IAAI,EAACX,CAAC,CAACY,IAAI,EAACZ,CAAC,CAACa,KAAK,CAAC,CAAC;IAAC,OAAO3C,CAAC,CAAC4C,aAAa,CAACvB,CAAC,CAACwB,QAAQ,EAAC;MAACC,KAAK,EAACP;IAAC,CAAC,EAACT,CAAC,CAACiB,QAAQ,CAAC;EAAA,CAAC,EAAC,CAACvB,CAAC,CAAC,CAAC,CAAC;AAAA;AAAC,IAAIwB,CAAC,GAAC,GAAG;AAAC,SAASC,CAACA,CAAC1B,CAAC,EAACC,CAAC,EAAC;EAAC,IAAI0B,CAAC,GAACtC,CAAC,CAAC,CAAC;IAAC;MAACuC,EAAE,EAACrB,CAAC,GAAC,0BAA0BoB,CAAC,EAAE;MAAC,GAAGnB;IAAC,CAAC,GAACR,CAAC;IAACgB,CAAC,GAACjB,CAAC,CAAC,CAAC;IAACU,CAAC,GAAChB,CAAC,CAACQ,CAAC,CAAC;EAACV,CAAC,CAAC,MAAIyB,CAAC,CAACC,QAAQ,CAACV,CAAC,CAAC,EAAC,CAACA,CAAC,EAACS,CAAC,CAACC,QAAQ,CAAC,CAAC;EAAC,IAAIP,CAAC,GAAC;IAACmB,GAAG,EAACpB,CAAC;IAAC,GAAGO,CAAC,CAACI,KAAK;IAACQ,EAAE,EAACrB;EAAC,CAAC;EAAC,OAAOV,CAAC,CAAC;IAACiC,QAAQ,EAACpB,CAAC;IAACqB,UAAU,EAACvB,CAAC;IAACU,IAAI,EAACF,CAAC,CAACE,IAAI,IAAE,CAAC,CAAC;IAACc,UAAU,EAACP,CAAC;IAACN,IAAI,EAACH,CAAC,CAACG,IAAI,IAAE;EAAa,CAAC,CAAC;AAAA;AAAC,IAAIc,CAAC,GAACtC,CAAC,CAAC+B,CAAC,CAAC;EAACQ,CAAC,GAACC,MAAM,CAACC,MAAM,CAACH,CAAC,EAAC,CAAC,CAAC,CAAC;AAAC,SAAOC,CAAC,IAAIG,WAAW,EAACjC,CAAC,IAAIkC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}