{"ast": null, "code": "\"use strict\";\n\nconst {\n  isASCIIHex\n} = require(\"./infra\");\nconst {\n  utf8Encode\n} = require(\"./encoding\");\nfunction p(char) {\n  return char.codePointAt(0);\n}\n\n// https://url.spec.whatwg.org/#percent-encode\nfunction percentEncode(c) {\n  let hex = c.toString(16).toUpperCase();\n  if (hex.length === 1) {\n    hex = `0${hex}`;\n  }\n  return `%${hex}`;\n}\n\n// https://url.spec.whatwg.org/#percent-decode\nfunction percentDecodeBytes(input) {\n  const output = new Uint8Array(input.byteLength);\n  let outputIndex = 0;\n  for (let i = 0; i < input.byteLength; ++i) {\n    const byte = input[i];\n    if (byte !== 0x25) {\n      output[outputIndex++] = byte;\n    } else if (byte === 0x25 && (!isASCIIHex(input[i + 1]) || !isASCIIHex(input[i + 2]))) {\n      output[outputIndex++] = byte;\n    } else {\n      const bytePoint = parseInt(String.fromCodePoint(input[i + 1], input[i + 2]), 16);\n      output[outputIndex++] = bytePoint;\n      i += 2;\n    }\n  }\n  return output.slice(0, outputIndex);\n}\n\n// https://url.spec.whatwg.org/#string-percent-decode\nfunction percentDecodeString(input) {\n  const bytes = utf8Encode(input);\n  return percentDecodeBytes(bytes);\n}\n\n// https://url.spec.whatwg.org/#c0-control-percent-encode-set\nfunction isC0ControlPercentEncode(c) {\n  return c <= 0x1F || c > 0x7E;\n}\n\n// https://url.spec.whatwg.org/#fragment-percent-encode-set\nconst extraFragmentPercentEncodeSet = new Set([p(\" \"), p(\"\\\"\"), p(\"<\"), p(\">\"), p(\"`\")]);\nfunction isFragmentPercentEncode(c) {\n  return isC0ControlPercentEncode(c) || extraFragmentPercentEncodeSet.has(c);\n}\n\n// https://url.spec.whatwg.org/#query-percent-encode-set\nconst extraQueryPercentEncodeSet = new Set([p(\" \"), p(\"\\\"\"), p(\"#\"), p(\"<\"), p(\">\")]);\nfunction isQueryPercentEncode(c) {\n  return isC0ControlPercentEncode(c) || extraQueryPercentEncodeSet.has(c);\n}\n\n// https://url.spec.whatwg.org/#special-query-percent-encode-set\nfunction isSpecialQueryPercentEncode(c) {\n  return isQueryPercentEncode(c) || c === p(\"'\");\n}\n\n// https://url.spec.whatwg.org/#path-percent-encode-set\nconst extraPathPercentEncodeSet = new Set([p(\"?\"), p(\"`\"), p(\"{\"), p(\"}\"), p(\"^\")]);\nfunction isPathPercentEncode(c) {\n  return isQueryPercentEncode(c) || extraPathPercentEncodeSet.has(c);\n}\n\n// https://url.spec.whatwg.org/#userinfo-percent-encode-set\nconst extraUserinfoPercentEncodeSet = new Set([p(\"/\"), p(\":\"), p(\";\"), p(\"=\"), p(\"@\"), p(\"[\"), p(\"\\\\\"), p(\"]\"), p(\"|\")]);\nfunction isUserinfoPercentEncode(c) {\n  return isPathPercentEncode(c) || extraUserinfoPercentEncodeSet.has(c);\n}\n\n// https://url.spec.whatwg.org/#component-percent-encode-set\nconst extraComponentPercentEncodeSet = new Set([p(\"$\"), p(\"%\"), p(\"&\"), p(\"+\"), p(\",\")]);\nfunction isComponentPercentEncode(c) {\n  return isUserinfoPercentEncode(c) || extraComponentPercentEncodeSet.has(c);\n}\n\n// https://url.spec.whatwg.org/#application-x-www-form-urlencoded-percent-encode-set\nconst extraURLEncodedPercentEncodeSet = new Set([p(\"!\"), p(\"'\"), p(\"(\"), p(\")\"), p(\"~\")]);\nfunction isURLEncodedPercentEncode(c) {\n  return isComponentPercentEncode(c) || extraURLEncodedPercentEncodeSet.has(c);\n}\n\n// https://url.spec.whatwg.org/#code-point-percent-encode-after-encoding\n// https://url.spec.whatwg.org/#utf-8-percent-encode\n// Assuming encoding is always utf-8 allows us to trim one of the logic branches. TODO: support encoding.\n// The \"-Internal\" variant here has code points as JS strings. The external version used by other files has code points\n// as JS numbers, like the rest of the codebase.\nfunction utf8PercentEncodeCodePointInternal(codePoint, percentEncodePredicate) {\n  const bytes = utf8Encode(codePoint);\n  let output = \"\";\n  for (const byte of bytes) {\n    // Our percentEncodePredicate operates on bytes, not code points, so this is slightly different from the spec.\n    if (!percentEncodePredicate(byte)) {\n      output += String.fromCharCode(byte);\n    } else {\n      output += percentEncode(byte);\n    }\n  }\n  return output;\n}\nfunction utf8PercentEncodeCodePoint(codePoint, percentEncodePredicate) {\n  return utf8PercentEncodeCodePointInternal(String.fromCodePoint(codePoint), percentEncodePredicate);\n}\n\n// https://url.spec.whatwg.org/#string-percent-encode-after-encoding\n// https://url.spec.whatwg.org/#string-utf-8-percent-encode\nfunction utf8PercentEncodeString(input, percentEncodePredicate, spaceAsPlus = false) {\n  let output = \"\";\n  for (const codePoint of input) {\n    if (spaceAsPlus && codePoint === \" \") {\n      output += \"+\";\n    } else {\n      output += utf8PercentEncodeCodePointInternal(codePoint, percentEncodePredicate);\n    }\n  }\n  return output;\n}\nmodule.exports = {\n  isC0ControlPercentEncode,\n  isFragmentPercentEncode,\n  isQueryPercentEncode,\n  isSpecialQueryPercentEncode,\n  isPathPercentEncode,\n  isUserinfoPercentEncode,\n  isURLEncodedPercentEncode,\n  percentDecodeString,\n  percentDecodeBytes,\n  utf8PercentEncodeString,\n  utf8PercentEncodeCodePoint\n};", "map": {"version": 3, "names": ["isASCIIHex", "require", "utf8Encode", "p", "char", "codePointAt", "percentEncode", "c", "hex", "toString", "toUpperCase", "length", "percentDecodeBytes", "input", "output", "Uint8Array", "byteLength", "outputIndex", "i", "byte", "bytePoint", "parseInt", "String", "fromCodePoint", "slice", "percentDecodeString", "bytes", "isC0ControlPercentEncode", "extraFragmentPercentEncodeSet", "Set", "isFragmentPercentEncode", "has", "extraQueryPercentEncodeSet", "isQueryPercentEncode", "isSpecialQueryPercentEncode", "extraPathPercentEncodeSet", "isPathPercentEncode", "extraUserinfoPercentEncodeSet", "isUserinfoPercentEncode", "extraComponentPercentEncodeSet", "isComponentPercentEncode", "extraURLEncodedPercentEncodeSet", "isURLEncodedPercentEncode", "utf8PercentEncodeCodePointInternal", "codePoint", "percentEncodePredicate", "fromCharCode", "utf8PercentEncodeCodePoint", "utf8PercentEncodeString", "spaceAsPlus", "module", "exports"], "sources": ["C:/Users/<USER>/node_modules/mongodb-connection-string-url/node_modules/whatwg-url/lib/percent-encoding.js"], "sourcesContent": ["\"use strict\";\nconst { isASCIIHex } = require(\"./infra\");\nconst { utf8Encode } = require(\"./encoding\");\n\nfunction p(char) {\n  return char.codePointAt(0);\n}\n\n// https://url.spec.whatwg.org/#percent-encode\nfunction percentEncode(c) {\n  let hex = c.toString(16).toUpperCase();\n  if (hex.length === 1) {\n    hex = `0${hex}`;\n  }\n\n  return `%${hex}`;\n}\n\n// https://url.spec.whatwg.org/#percent-decode\nfunction percentDecodeBytes(input) {\n  const output = new Uint8Array(input.byteLength);\n  let outputIndex = 0;\n  for (let i = 0; i < input.byteLength; ++i) {\n    const byte = input[i];\n    if (byte !== 0x25) {\n      output[outputIndex++] = byte;\n    } else if (byte === 0x25 && (!isASCIIHex(input[i + 1]) || !isASCIIHex(input[i + 2]))) {\n      output[outputIndex++] = byte;\n    } else {\n      const bytePoint = parseInt(String.fromCodePoint(input[i + 1], input[i + 2]), 16);\n      output[outputIndex++] = bytePoint;\n      i += 2;\n    }\n  }\n\n  return output.slice(0, outputIndex);\n}\n\n// https://url.spec.whatwg.org/#string-percent-decode\nfunction percentDecodeString(input) {\n  const bytes = utf8Encode(input);\n  return percentDecodeBytes(bytes);\n}\n\n// https://url.spec.whatwg.org/#c0-control-percent-encode-set\nfunction isC0ControlPercentEncode(c) {\n  return c <= 0x1F || c > 0x7E;\n}\n\n// https://url.spec.whatwg.org/#fragment-percent-encode-set\nconst extraFragmentPercentEncodeSet = new Set([p(\" \"), p(\"\\\"\"), p(\"<\"), p(\">\"), p(\"`\")]);\nfunction isFragmentPercentEncode(c) {\n  return isC0ControlPercentEncode(c) || extraFragmentPercentEncodeSet.has(c);\n}\n\n// https://url.spec.whatwg.org/#query-percent-encode-set\nconst extraQueryPercentEncodeSet = new Set([p(\" \"), p(\"\\\"\"), p(\"#\"), p(\"<\"), p(\">\")]);\nfunction isQueryPercentEncode(c) {\n  return isC0ControlPercentEncode(c) || extraQueryPercentEncodeSet.has(c);\n}\n\n// https://url.spec.whatwg.org/#special-query-percent-encode-set\nfunction isSpecialQueryPercentEncode(c) {\n  return isQueryPercentEncode(c) || c === p(\"'\");\n}\n\n// https://url.spec.whatwg.org/#path-percent-encode-set\nconst extraPathPercentEncodeSet = new Set([p(\"?\"), p(\"`\"), p(\"{\"), p(\"}\"), p(\"^\")]);\nfunction isPathPercentEncode(c) {\n  return isQueryPercentEncode(c) || extraPathPercentEncodeSet.has(c);\n}\n\n// https://url.spec.whatwg.org/#userinfo-percent-encode-set\nconst extraUserinfoPercentEncodeSet =\n  new Set([p(\"/\"), p(\":\"), p(\";\"), p(\"=\"), p(\"@\"), p(\"[\"), p(\"\\\\\"), p(\"]\"), p(\"|\")]);\nfunction isUserinfoPercentEncode(c) {\n  return isPathPercentEncode(c) || extraUserinfoPercentEncodeSet.has(c);\n}\n\n// https://url.spec.whatwg.org/#component-percent-encode-set\nconst extraComponentPercentEncodeSet = new Set([p(\"$\"), p(\"%\"), p(\"&\"), p(\"+\"), p(\",\")]);\nfunction isComponentPercentEncode(c) {\n  return isUserinfoPercentEncode(c) || extraComponentPercentEncodeSet.has(c);\n}\n\n// https://url.spec.whatwg.org/#application-x-www-form-urlencoded-percent-encode-set\nconst extraURLEncodedPercentEncodeSet = new Set([p(\"!\"), p(\"'\"), p(\"(\"), p(\")\"), p(\"~\")]);\nfunction isURLEncodedPercentEncode(c) {\n  return isComponentPercentEncode(c) || extraURLEncodedPercentEncodeSet.has(c);\n}\n\n// https://url.spec.whatwg.org/#code-point-percent-encode-after-encoding\n// https://url.spec.whatwg.org/#utf-8-percent-encode\n// Assuming encoding is always utf-8 allows us to trim one of the logic branches. TODO: support encoding.\n// The \"-Internal\" variant here has code points as JS strings. The external version used by other files has code points\n// as JS numbers, like the rest of the codebase.\nfunction utf8PercentEncodeCodePointInternal(codePoint, percentEncodePredicate) {\n  const bytes = utf8Encode(codePoint);\n  let output = \"\";\n  for (const byte of bytes) {\n    // Our percentEncodePredicate operates on bytes, not code points, so this is slightly different from the spec.\n    if (!percentEncodePredicate(byte)) {\n      output += String.fromCharCode(byte);\n    } else {\n      output += percentEncode(byte);\n    }\n  }\n\n  return output;\n}\n\nfunction utf8PercentEncodeCodePoint(codePoint, percentEncodePredicate) {\n  return utf8PercentEncodeCodePointInternal(String.fromCodePoint(codePoint), percentEncodePredicate);\n}\n\n// https://url.spec.whatwg.org/#string-percent-encode-after-encoding\n// https://url.spec.whatwg.org/#string-utf-8-percent-encode\nfunction utf8PercentEncodeString(input, percentEncodePredicate, spaceAsPlus = false) {\n  let output = \"\";\n  for (const codePoint of input) {\n    if (spaceAsPlus && codePoint === \" \") {\n      output += \"+\";\n    } else {\n      output += utf8PercentEncodeCodePointInternal(codePoint, percentEncodePredicate);\n    }\n  }\n  return output;\n}\n\nmodule.exports = {\n  isC0ControlPercentEncode,\n  isFragmentPercentEncode,\n  isQueryPercentEncode,\n  isSpecialQueryPercentEncode,\n  isPathPercentEncode,\n  isUserinfoPercentEncode,\n  isURLEncodedPercentEncode,\n  percentDecodeString,\n  percentDecodeBytes,\n  utf8PercentEncodeString,\n  utf8PercentEncodeCodePoint\n};\n"], "mappings": "AAAA,YAAY;;AACZ,MAAM;EAAEA;AAAW,CAAC,GAAGC,OAAO,CAAC,SAAS,CAAC;AACzC,MAAM;EAAEC;AAAW,CAAC,GAAGD,OAAO,CAAC,YAAY,CAAC;AAE5C,SAASE,CAACA,CAACC,IAAI,EAAE;EACf,OAAOA,IAAI,CAACC,WAAW,CAAC,CAAC,CAAC;AAC5B;;AAEA;AACA,SAASC,aAAaA,CAACC,CAAC,EAAE;EACxB,IAAIC,GAAG,GAAGD,CAAC,CAACE,QAAQ,CAAC,EAAE,CAAC,CAACC,WAAW,CAAC,CAAC;EACtC,IAAIF,GAAG,CAACG,MAAM,KAAK,CAAC,EAAE;IACpBH,GAAG,GAAG,IAAIA,GAAG,EAAE;EACjB;EAEA,OAAO,IAAIA,GAAG,EAAE;AAClB;;AAEA;AACA,SAASI,kBAAkBA,CAACC,KAAK,EAAE;EACjC,MAAMC,MAAM,GAAG,IAAIC,UAAU,CAACF,KAAK,CAACG,UAAU,CAAC;EAC/C,IAAIC,WAAW,GAAG,CAAC;EACnB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,KAAK,CAACG,UAAU,EAAE,EAAEE,CAAC,EAAE;IACzC,MAAMC,IAAI,GAAGN,KAAK,CAACK,CAAC,CAAC;IACrB,IAAIC,IAAI,KAAK,IAAI,EAAE;MACjBL,MAAM,CAACG,WAAW,EAAE,CAAC,GAAGE,IAAI;IAC9B,CAAC,MAAM,IAAIA,IAAI,KAAK,IAAI,KAAK,CAACnB,UAAU,CAACa,KAAK,CAACK,CAAC,GAAG,CAAC,CAAC,CAAC,IAAI,CAAClB,UAAU,CAACa,KAAK,CAACK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE;MACpFJ,MAAM,CAACG,WAAW,EAAE,CAAC,GAAGE,IAAI;IAC9B,CAAC,MAAM;MACL,MAAMC,SAAS,GAAGC,QAAQ,CAACC,MAAM,CAACC,aAAa,CAACV,KAAK,CAACK,CAAC,GAAG,CAAC,CAAC,EAAEL,KAAK,CAACK,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,CAAC;MAChFJ,MAAM,CAACG,WAAW,EAAE,CAAC,GAAGG,SAAS;MACjCF,CAAC,IAAI,CAAC;IACR;EACF;EAEA,OAAOJ,MAAM,CAACU,KAAK,CAAC,CAAC,EAAEP,WAAW,CAAC;AACrC;;AAEA;AACA,SAASQ,mBAAmBA,CAACZ,KAAK,EAAE;EAClC,MAAMa,KAAK,GAAGxB,UAAU,CAACW,KAAK,CAAC;EAC/B,OAAOD,kBAAkB,CAACc,KAAK,CAAC;AAClC;;AAEA;AACA,SAASC,wBAAwBA,CAACpB,CAAC,EAAE;EACnC,OAAOA,CAAC,IAAI,IAAI,IAAIA,CAAC,GAAG,IAAI;AAC9B;;AAEA;AACA,MAAMqB,6BAA6B,GAAG,IAAIC,GAAG,CAAC,CAAC1B,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,IAAI,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACxF,SAAS2B,uBAAuBA,CAACvB,CAAC,EAAE;EAClC,OAAOoB,wBAAwB,CAACpB,CAAC,CAAC,IAAIqB,6BAA6B,CAACG,GAAG,CAACxB,CAAC,CAAC;AAC5E;;AAEA;AACA,MAAMyB,0BAA0B,GAAG,IAAIH,GAAG,CAAC,CAAC1B,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,IAAI,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACrF,SAAS8B,oBAAoBA,CAAC1B,CAAC,EAAE;EAC/B,OAAOoB,wBAAwB,CAACpB,CAAC,CAAC,IAAIyB,0BAA0B,CAACD,GAAG,CAACxB,CAAC,CAAC;AACzE;;AAEA;AACA,SAAS2B,2BAA2BA,CAAC3B,CAAC,EAAE;EACtC,OAAO0B,oBAAoB,CAAC1B,CAAC,CAAC,IAAIA,CAAC,KAAKJ,CAAC,CAAC,GAAG,CAAC;AAChD;;AAEA;AACA,MAAMgC,yBAAyB,GAAG,IAAIN,GAAG,CAAC,CAAC1B,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACnF,SAASiC,mBAAmBA,CAAC7B,CAAC,EAAE;EAC9B,OAAO0B,oBAAoB,CAAC1B,CAAC,CAAC,IAAI4B,yBAAyB,CAACJ,GAAG,CAACxB,CAAC,CAAC;AACpE;;AAEA;AACA,MAAM8B,6BAA6B,GACjC,IAAIR,GAAG,CAAC,CAAC1B,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,IAAI,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACpF,SAASmC,uBAAuBA,CAAC/B,CAAC,EAAE;EAClC,OAAO6B,mBAAmB,CAAC7B,CAAC,CAAC,IAAI8B,6BAA6B,CAACN,GAAG,CAACxB,CAAC,CAAC;AACvE;;AAEA;AACA,MAAMgC,8BAA8B,GAAG,IAAIV,GAAG,CAAC,CAAC1B,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACxF,SAASqC,wBAAwBA,CAACjC,CAAC,EAAE;EACnC,OAAO+B,uBAAuB,CAAC/B,CAAC,CAAC,IAAIgC,8BAA8B,CAACR,GAAG,CAACxB,CAAC,CAAC;AAC5E;;AAEA;AACA,MAAMkC,+BAA+B,GAAG,IAAIZ,GAAG,CAAC,CAAC1B,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,EAAEA,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC;AACzF,SAASuC,yBAAyBA,CAACnC,CAAC,EAAE;EACpC,OAAOiC,wBAAwB,CAACjC,CAAC,CAAC,IAAIkC,+BAA+B,CAACV,GAAG,CAACxB,CAAC,CAAC;AAC9E;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASoC,kCAAkCA,CAACC,SAAS,EAAEC,sBAAsB,EAAE;EAC7E,MAAMnB,KAAK,GAAGxB,UAAU,CAAC0C,SAAS,CAAC;EACnC,IAAI9B,MAAM,GAAG,EAAE;EACf,KAAK,MAAMK,IAAI,IAAIO,KAAK,EAAE;IACxB;IACA,IAAI,CAACmB,sBAAsB,CAAC1B,IAAI,CAAC,EAAE;MACjCL,MAAM,IAAIQ,MAAM,CAACwB,YAAY,CAAC3B,IAAI,CAAC;IACrC,CAAC,MAAM;MACLL,MAAM,IAAIR,aAAa,CAACa,IAAI,CAAC;IAC/B;EACF;EAEA,OAAOL,MAAM;AACf;AAEA,SAASiC,0BAA0BA,CAACH,SAAS,EAAEC,sBAAsB,EAAE;EACrE,OAAOF,kCAAkC,CAACrB,MAAM,CAACC,aAAa,CAACqB,SAAS,CAAC,EAAEC,sBAAsB,CAAC;AACpG;;AAEA;AACA;AACA,SAASG,uBAAuBA,CAACnC,KAAK,EAAEgC,sBAAsB,EAAEI,WAAW,GAAG,KAAK,EAAE;EACnF,IAAInC,MAAM,GAAG,EAAE;EACf,KAAK,MAAM8B,SAAS,IAAI/B,KAAK,EAAE;IAC7B,IAAIoC,WAAW,IAAIL,SAAS,KAAK,GAAG,EAAE;MACpC9B,MAAM,IAAI,GAAG;IACf,CAAC,MAAM;MACLA,MAAM,IAAI6B,kCAAkC,CAACC,SAAS,EAAEC,sBAAsB,CAAC;IACjF;EACF;EACA,OAAO/B,MAAM;AACf;AAEAoC,MAAM,CAACC,OAAO,GAAG;EACfxB,wBAAwB;EACxBG,uBAAuB;EACvBG,oBAAoB;EACpBC,2BAA2B;EAC3BE,mBAAmB;EACnBE,uBAAuB;EACvBI,yBAAyB;EACzBjB,mBAAmB;EACnBb,kBAAkB;EAClBoC,uBAAuB;EACvBD;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}