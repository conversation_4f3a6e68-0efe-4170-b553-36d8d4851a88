{"ast": null, "code": "import { disposables as m } from '../../utils/disposables.js';\nimport { isIOS as u } from '../../utils/platform.js';\nfunction d() {\n  return u() ? {\n    before(_ref) {\n      let {\n        doc: r,\n        d: l,\n        meta: c\n      } = _ref;\n      function o(a) {\n        return c.containers.flatMap(n => n()).some(n => n.contains(a));\n      }\n      l.microTask(() => {\n        var s;\n        if (window.getComputedStyle(r.documentElement).scrollBehavior !== \"auto\") {\n          let t = m();\n          t.style(r.documentElement, \"scrollBehavior\", \"auto\"), l.add(() => l.microTask(() => t.dispose()));\n        }\n        let a = (s = window.scrollY) != null ? s : window.pageYOffset,\n          n = null;\n        l.addEventListener(r, \"click\", t => {\n          if (t.target instanceof HTMLElement) try {\n            let e = t.target.closest(\"a\");\n            if (!e) return;\n            let {\n                hash: f\n              } = new URL(e.href),\n              i = r.querySelector(f);\n            i && !o(i) && (n = i);\n          } catch (_unused) {}\n        }, !0), l.addEventListener(r, \"touchstart\", t => {\n          if (t.target instanceof HTMLElement) if (o(t.target)) {\n            let e = t.target;\n            for (; e.parentElement && o(e.parentElement);) e = e.parentElement;\n            l.style(e, \"overscrollBehavior\", \"contain\");\n          } else l.style(t.target, \"touchAction\", \"none\");\n        }), l.addEventListener(r, \"touchmove\", t => {\n          if (t.target instanceof HTMLElement) if (o(t.target)) {\n            let e = t.target;\n            for (; e.parentElement && e.dataset.headlessuiPortal !== \"\" && !(e.scrollHeight > e.clientHeight || e.scrollWidth > e.clientWidth);) e = e.parentElement;\n            e.dataset.headlessuiPortal === \"\" && t.preventDefault();\n          } else t.preventDefault();\n        }, {\n          passive: !1\n        }), l.add(() => {\n          var e;\n          let t = (e = window.scrollY) != null ? e : window.pageYOffset;\n          a !== t && window.scrollTo(0, a), n && n.isConnected && (n.scrollIntoView({\n            block: \"nearest\"\n          }), n = null);\n        });\n      });\n    }\n  } : {};\n}\nexport { d as handleIOSLocking };", "map": {"version": 3, "names": ["disposables", "m", "isIOS", "u", "d", "before", "_ref", "doc", "r", "l", "meta", "c", "o", "a", "containers", "flatMap", "n", "some", "contains", "microTask", "s", "window", "getComputedStyle", "documentElement", "scroll<PERSON>eh<PERSON>or", "t", "style", "add", "dispose", "scrollY", "pageYOffset", "addEventListener", "target", "HTMLElement", "e", "closest", "hash", "f", "URL", "href", "i", "querySelector", "_unused", "parentElement", "dataset", "headless<PERSON><PERSON><PERSON><PERSON>", "scrollHeight", "clientHeight", "scrollWidth", "clientWidth", "preventDefault", "passive", "scrollTo", "isConnected", "scrollIntoView", "block", "handleIOSLocking"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/hooks/document-overflow/handle-ios-locking.js"], "sourcesContent": ["import{disposables as m}from'../../utils/disposables.js';import{isIOS as u}from'../../utils/platform.js';function d(){return u()?{before({doc:r,d:l,meta:c}){function o(a){return c.containers.flatMap(n=>n()).some(n=>n.contains(a))}l.microTask(()=>{var s;if(window.getComputedStyle(r.documentElement).scrollBehavior!==\"auto\"){let t=m();t.style(r.documentElement,\"scrollBehavior\",\"auto\"),l.add(()=>l.microTask(()=>t.dispose()))}let a=(s=window.scrollY)!=null?s:window.pageYOffset,n=null;l.addEventListener(r,\"click\",t=>{if(t.target instanceof HTMLElement)try{let e=t.target.closest(\"a\");if(!e)return;let{hash:f}=new URL(e.href),i=r.querySelector(f);i&&!o(i)&&(n=i)}catch{}},!0),l.addEventListener(r,\"touchstart\",t=>{if(t.target instanceof HTMLElement)if(o(t.target)){let e=t.target;for(;e.parentElement&&o(e.parentElement);)e=e.parentElement;l.style(e,\"overscrollBehavior\",\"contain\")}else l.style(t.target,\"touchAction\",\"none\")}),l.addEventListener(r,\"touchmove\",t=>{if(t.target instanceof HTMLElement)if(o(t.target)){let e=t.target;for(;e.parentElement&&e.dataset.headlessuiPortal!==\"\"&&!(e.scrollHeight>e.clientHeight||e.scrollWidth>e.clientWidth);)e=e.parentElement;e.dataset.headlessuiPortal===\"\"&&t.preventDefault()}else t.preventDefault()},{passive:!1}),l.add(()=>{var e;let t=(e=window.scrollY)!=null?e:window.pageYOffset;a!==t&&window.scrollTo(0,a),n&&n.isConnected&&(n.scrollIntoView({block:\"nearest\"}),n=null)})})}}:{}}export{d as handleIOSLocking};\n"], "mappings": "AAAA,SAAOA,WAAW,IAAIC,CAAC,QAAK,4BAA4B;AAAC,SAAOC,KAAK,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAASC,CAACA,CAAA,EAAE;EAAC,OAAOD,CAAC,CAAC,CAAC,GAAC;IAACE,MAAMA,CAAAC,IAAA,EAAoB;MAAA,IAAnB;QAACC,GAAG,EAACC,CAAC;QAACJ,CAAC,EAACK,CAAC;QAACC,IAAI,EAACC;MAAC,CAAC,GAAAL,IAAA;MAAE,SAASM,CAACA,CAACC,CAAC,EAAC;QAAC,OAAOF,CAAC,CAACG,UAAU,CAACC,OAAO,CAACC,CAAC,IAAEA,CAAC,CAAC,CAAC,CAAC,CAACC,IAAI,CAACD,CAAC,IAAEA,CAAC,CAACE,QAAQ,CAACL,CAAC,CAAC,CAAC;MAAA;MAACJ,CAAC,CAACU,SAAS,CAAC,MAAI;QAAC,IAAIC,CAAC;QAAC,IAAGC,MAAM,CAACC,gBAAgB,CAACd,CAAC,CAACe,eAAe,CAAC,CAACC,cAAc,KAAG,MAAM,EAAC;UAAC,IAAIC,CAAC,GAACxB,CAAC,CAAC,CAAC;UAACwB,CAAC,CAACC,KAAK,CAAClB,CAAC,CAACe,eAAe,EAAC,gBAAgB,EAAC,MAAM,CAAC,EAACd,CAAC,CAACkB,GAAG,CAAC,MAAIlB,CAAC,CAACU,SAAS,CAAC,MAAIM,CAAC,CAACG,OAAO,CAAC,CAAC,CAAC,CAAC;QAAA;QAAC,IAAIf,CAAC,GAAC,CAACO,CAAC,GAACC,MAAM,CAACQ,OAAO,KAAG,IAAI,GAACT,CAAC,GAACC,MAAM,CAACS,WAAW;UAACd,CAAC,GAAC,IAAI;QAACP,CAAC,CAACsB,gBAAgB,CAACvB,CAAC,EAAC,OAAO,EAACiB,CAAC,IAAE;UAAC,IAAGA,CAAC,CAACO,MAAM,YAAYC,WAAW,EAAC,IAAG;YAAC,IAAIC,CAAC,GAACT,CAAC,CAACO,MAAM,CAACG,OAAO,CAAC,GAAG,CAAC;YAAC,IAAG,CAACD,CAAC,EAAC;YAAO,IAAG;gBAACE,IAAI,EAACC;cAAC,CAAC,GAAC,IAAIC,GAAG,CAACJ,CAAC,CAACK,IAAI,CAAC;cAACC,CAAC,GAAChC,CAAC,CAACiC,aAAa,CAACJ,CAAC,CAAC;YAACG,CAAC,IAAE,CAAC5B,CAAC,CAAC4B,CAAC,CAAC,KAAGxB,CAAC,GAACwB,CAAC,CAAC;UAAA,CAAC,QAAAE,OAAA,EAAK,CAAC;QAAC,CAAC,EAAC,CAAC,CAAC,CAAC,EAACjC,CAAC,CAACsB,gBAAgB,CAACvB,CAAC,EAAC,YAAY,EAACiB,CAAC,IAAE;UAAC,IAAGA,CAAC,CAACO,MAAM,YAAYC,WAAW,EAAC,IAAGrB,CAAC,CAACa,CAAC,CAACO,MAAM,CAAC,EAAC;YAAC,IAAIE,CAAC,GAACT,CAAC,CAACO,MAAM;YAAC,OAAKE,CAAC,CAACS,aAAa,IAAE/B,CAAC,CAACsB,CAAC,CAACS,aAAa,CAAC,GAAET,CAAC,GAACA,CAAC,CAACS,aAAa;YAAClC,CAAC,CAACiB,KAAK,CAACQ,CAAC,EAAC,oBAAoB,EAAC,SAAS,CAAC;UAAA,CAAC,MAAKzB,CAAC,CAACiB,KAAK,CAACD,CAAC,CAACO,MAAM,EAAC,aAAa,EAAC,MAAM,CAAC;QAAA,CAAC,CAAC,EAACvB,CAAC,CAACsB,gBAAgB,CAACvB,CAAC,EAAC,WAAW,EAACiB,CAAC,IAAE;UAAC,IAAGA,CAAC,CAACO,MAAM,YAAYC,WAAW,EAAC,IAAGrB,CAAC,CAACa,CAAC,CAACO,MAAM,CAAC,EAAC;YAAC,IAAIE,CAAC,GAACT,CAAC,CAACO,MAAM;YAAC,OAAKE,CAAC,CAACS,aAAa,IAAET,CAAC,CAACU,OAAO,CAACC,gBAAgB,KAAG,EAAE,IAAE,EAAEX,CAAC,CAACY,YAAY,GAACZ,CAAC,CAACa,YAAY,IAAEb,CAAC,CAACc,WAAW,GAACd,CAAC,CAACe,WAAW,CAAC,GAAEf,CAAC,GAACA,CAAC,CAACS,aAAa;YAACT,CAAC,CAACU,OAAO,CAACC,gBAAgB,KAAG,EAAE,IAAEpB,CAAC,CAACyB,cAAc,CAAC,CAAC;UAAA,CAAC,MAAKzB,CAAC,CAACyB,cAAc,CAAC,CAAC;QAAA,CAAC,EAAC;UAACC,OAAO,EAAC,CAAC;QAAC,CAAC,CAAC,EAAC1C,CAAC,CAACkB,GAAG,CAAC,MAAI;UAAC,IAAIO,CAAC;UAAC,IAAIT,CAAC,GAAC,CAACS,CAAC,GAACb,MAAM,CAACQ,OAAO,KAAG,IAAI,GAACK,CAAC,GAACb,MAAM,CAACS,WAAW;UAACjB,CAAC,KAAGY,CAAC,IAAEJ,MAAM,CAAC+B,QAAQ,CAAC,CAAC,EAACvC,CAAC,CAAC,EAACG,CAAC,IAAEA,CAAC,CAACqC,WAAW,KAAGrC,CAAC,CAACsC,cAAc,CAAC;YAACC,KAAK,EAAC;UAAS,CAAC,CAAC,EAACvC,CAAC,GAAC,IAAI,CAAC;QAAA,CAAC,CAAC;MAAA,CAAC,CAAC;IAAA;EAAC,CAAC,GAAC,CAAC,CAAC;AAAA;AAAC,SAAOZ,CAAC,IAAIoD,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}