{"ast": null, "code": "\"use strict\";\n\nvar __createBinding = this && this.__createBinding || (Object.create ? function (o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n    desc = {\n      enumerable: true,\n      get: function () {\n        return m[k];\n      }\n    };\n  }\n  Object.defineProperty(o, k2, desc);\n} : function (o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\nvar __setModuleDefault = this && this.__setModuleDefault || (Object.create ? function (o, v) {\n  Object.defineProperty(o, \"default\", {\n    enumerable: true,\n    value: v\n  });\n} : function (o, v) {\n  o[\"default\"] = v;\n});\nvar __importStar = this && this.__importStar || function (mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k in mod) if (k !== \"default\" && Object.prototype.hasOwnProperty.call(mod, k)) __createBinding(result, mod, k);\n  __setModuleDefault(result, mod);\n  return result;\n};\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.redactConnectionString = exports.redactValidConnectionString = void 0;\nconst index_1 = __importStar(require(\"./index\"));\nfunction redactValidConnectionString(inputUrl, options) {\n  var _a, _b;\n  const url = inputUrl.clone();\n  const replacementString = (_a = options === null || options === void 0 ? void 0 : options.replacementString) !== null && _a !== void 0 ? _a : '_credentials_';\n  const redactUsernames = (_b = options === null || options === void 0 ? void 0 : options.redactUsernames) !== null && _b !== void 0 ? _b : true;\n  if ((url.username || url.password) && redactUsernames) {\n    url.username = replacementString;\n    url.password = '';\n  } else if (url.password) {\n    url.password = replacementString;\n  }\n  if (url.searchParams.has('authMechanismProperties')) {\n    const props = new index_1.CommaAndColonSeparatedRecord(url.searchParams.get('authMechanismProperties'));\n    if (props.get('AWS_SESSION_TOKEN')) {\n      props.set('AWS_SESSION_TOKEN', replacementString);\n      url.searchParams.set('authMechanismProperties', props.toString());\n    }\n  }\n  if (url.searchParams.has('tlsCertificateKeyFilePassword')) {\n    url.searchParams.set('tlsCertificateKeyFilePassword', replacementString);\n  }\n  if (url.searchParams.has('proxyUsername') && redactUsernames) {\n    url.searchParams.set('proxyUsername', replacementString);\n  }\n  if (url.searchParams.has('proxyPassword')) {\n    url.searchParams.set('proxyPassword', replacementString);\n  }\n  return url;\n}\nexports.redactValidConnectionString = redactValidConnectionString;\nfunction redactConnectionString(uri, options) {\n  var _a, _b;\n  const replacementString = (_a = options === null || options === void 0 ? void 0 : options.replacementString) !== null && _a !== void 0 ? _a : '<credentials>';\n  const redactUsernames = (_b = options === null || options === void 0 ? void 0 : options.redactUsernames) !== null && _b !== void 0 ? _b : true;\n  let parsed;\n  try {\n    parsed = new index_1.default(uri);\n  } catch (_c) {}\n  if (parsed) {\n    options = {\n      ...options,\n      replacementString: '___credentials___'\n    };\n    return parsed.redact(options).toString().replace(/___credentials___/g, replacementString);\n  }\n  const R = replacementString;\n  const replacements = [uri => uri.replace(redactUsernames ? /(\\/\\/)(.*)(@)/g : /(\\/\\/[^@]*:)(.*)(@)/g, `$1${R}$3`), uri => uri.replace(/(AWS_SESSION_TOKEN(:|%3A))([^,&]+)/gi, `$1${R}`), uri => uri.replace(/(tlsCertificateKeyFilePassword=)([^&]+)/gi, `$1${R}`), uri => redactUsernames ? uri.replace(/(proxyUsername=)([^&]+)/gi, `$1${R}`) : uri, uri => uri.replace(/(proxyPassword=)([^&]+)/gi, `$1${R}`)];\n  for (const replacer of replacements) {\n    uri = replacer(uri);\n  }\n  return uri;\n}\nexports.redactConnectionString = redactConnectionString;", "map": {"version": 3, "names": ["index_1", "__importStar", "require", "redactValidConnectionString", "inputUrl", "options", "url", "clone", "replacementString", "_a", "redactUsernames", "_b", "username", "password", "searchParams", "has", "props", "CommaAndColonSeparatedRecord", "get", "set", "toString", "exports", "redactConnectionString", "uri", "parsed", "default", "_c", "redact", "replace", "R", "replacements", "replacer"], "sources": ["../src/redact.ts"], "sourcesContent": [null], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,MAAAA,OAAA,GAAAC,YAAA,CAAAC,OAAA;AAOA,SAAgBC,2BAA2BA,CACzCC,QAAoC,EACpCC,OAA0C;;EAC1C,MAAMC,GAAG,GAAGF,QAAQ,CAACG,KAAK,EAAE;EAC5B,MAAMC,iBAAiB,GAAG,CAAAC,EAAA,GAAAJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,iBAAiB,cAAAC,EAAA,cAAAA,EAAA,GAAI,eAAe;EACvE,MAAMC,eAAe,GAAG,CAAAC,EAAA,GAAAN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEK,eAAe,cAAAC,EAAA,cAAAA,EAAA,GAAI,IAAI;EAExD,IAAI,CAACL,GAAG,CAACM,QAAQ,IAAIN,GAAG,CAACO,QAAQ,KAAKH,eAAe,EAAE;IACrDJ,GAAG,CAACM,QAAQ,GAAGJ,iBAAiB;IAChCF,GAAG,CAACO,QAAQ,GAAG,EAAE;GAClB,MAAM,IAAIP,GAAG,CAACO,QAAQ,EAAE;IACvBP,GAAG,CAACO,QAAQ,GAAGL,iBAAiB;;EAElC,IAAIF,GAAG,CAACQ,YAAY,CAACC,GAAG,CAAC,yBAAyB,CAAC,EAAE;IACnD,MAAMC,KAAK,GAAG,IAAIhB,OAAA,CAAAiB,4BAA4B,CAACX,GAAG,CAACQ,YAAY,CAACI,GAAG,CAAC,yBAAyB,CAAC,CAAC;IAC/F,IAAIF,KAAK,CAACE,GAAG,CAAC,mBAAmB,CAAC,EAAE;MAClCF,KAAK,CAACG,GAAG,CAAC,mBAAmB,EAAEX,iBAAiB,CAAC;MACjDF,GAAG,CAACQ,YAAY,CAACK,GAAG,CAAC,yBAAyB,EAAEH,KAAK,CAACI,QAAQ,EAAE,CAAC;;;EAGrE,IAAId,GAAG,CAACQ,YAAY,CAACC,GAAG,CAAC,+BAA+B,CAAC,EAAE;IACzDT,GAAG,CAACQ,YAAY,CAACK,GAAG,CAAC,+BAA+B,EAAEX,iBAAiB,CAAC;;EAE1E,IAAIF,GAAG,CAACQ,YAAY,CAACC,GAAG,CAAC,eAAe,CAAC,IAAIL,eAAe,EAAE;IAC5DJ,GAAG,CAACQ,YAAY,CAACK,GAAG,CAAC,eAAe,EAAEX,iBAAiB,CAAC;;EAE1D,IAAIF,GAAG,CAACQ,YAAY,CAACC,GAAG,CAAC,eAAe,CAAC,EAAE;IACzCT,GAAG,CAACQ,YAAY,CAACK,GAAG,CAAC,eAAe,EAAEX,iBAAiB,CAAC;;EAE1D,OAAOF,GAAG;AACZ;AA9BAe,OAAA,CAAAlB,2BAAA,GAAAA,2BAAA;AAgCA,SAAgBmB,sBAAsBA,CACpCC,GAAW,EACXlB,OAA0C;;EAC1C,MAAMG,iBAAiB,GAAG,CAAAC,EAAA,GAAAJ,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEG,iBAAiB,cAAAC,EAAA,cAAAA,EAAA,GAAI,eAAe;EACvE,MAAMC,eAAe,GAAG,CAAAC,EAAA,GAAAN,OAAO,aAAPA,OAAO,uBAAPA,OAAO,CAAEK,eAAe,cAAAC,EAAA,cAAAA,EAAA,GAAI,IAAI;EAExD,IAAIa,MAAoC;EACxC,IAAI;IACFA,MAAM,GAAG,IAAIxB,OAAA,CAAAyB,OAAgB,CAACF,GAAG,CAAC;GACnC,CAAC,OAAAG,EAAA,EAAM;EACR,IAAIF,MAAM,EAAE;IAGVnB,OAAO,GAAG;MAAE,GAAGA,OAAO;MAAEG,iBAAiB,EAAE;IAAmB,CAAE;IAChE,OAAOgB,MAAM,CAACG,MAAM,CAACtB,OAAO,CAAC,CAACe,QAAQ,EAAE,CAACQ,OAAO,CAAC,oBAAoB,EAAEpB,iBAAiB,CAAC;;EAK3F,MAAMqB,CAAC,GAAGrB,iBAAiB;EAC3B,MAAMsB,YAAY,GAAgC,CAEhDP,GAAG,IAAIA,GAAG,CAACK,OAAO,CAAClB,eAAe,GAAG,gBAAgB,GAAG,sBAAsB,EAAE,KAAKmB,CAAC,IAAI,CAAC,EAE3FN,GAAG,IAAIA,GAAG,CAACK,OAAO,CAAC,sCAAsC,EAAE,KAAKC,CAAC,EAAE,CAAC,EAEpEN,GAAG,IAAIA,GAAG,CAACK,OAAO,CAAC,2CAA2C,EAAE,KAAKC,CAAC,EAAE,CAAC,EAEzEN,GAAG,IAAIb,eAAe,GAAGa,GAAG,CAACK,OAAO,CAAC,2BAA2B,EAAE,KAAKC,CAAC,EAAE,CAAC,GAAGN,GAAG,EAEjFA,GAAG,IAAIA,GAAG,CAACK,OAAO,CAAC,2BAA2B,EAAE,KAAKC,CAAC,EAAE,CAAC,CAC1D;EACD,KAAK,MAAME,QAAQ,IAAID,YAAY,EAAE;IACnCP,GAAG,GAAGQ,QAAQ,CAACR,GAAG,CAAC;;EAErB,OAAOA,GAAG;AACZ;AApCAF,OAAA,CAAAC,sBAAA,GAAAA,sBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}