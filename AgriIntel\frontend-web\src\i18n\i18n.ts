import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import LanguageDetector from 'i18next-browser-languagedetector';

// Import translation files
import enTranslations from './locales/en.json';
import afTranslations from './locales/af.json';
import stTranslations from './locales/st.json';
import tnTranslations from './locales/tn.json';
import zuTranslations from './locales/zu.json';

const resources = {
  en: {
    translation: enTranslations,
  },
  af: {
    translation: afTranslations,
  },
  st: {
    translation: stTranslations,
  },
  tn: {
    translation: tnTranslations,
  },
  zu: {
    translation: zuTranslations,
  },
};

i18n
  .use(LanguageDetector)
  .use(initReactI18next)
  .init({
    resources,
    fallbackLng: 'en',
    debug: process.env.NODE_ENV === 'development',

    interpolation: {
      escapeValue: false, // React already escapes values
    },

    detection: {
      order: ['localStorage', 'navigator', 'htmlTag'],
      caches: ['localStorage'],
    },

    react: {
      useSuspense: false,
    },
  });

export default i18n;
