{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ValidateCollectionOperation = void 0;\nconst error_1 = require(\"../error\");\nconst command_1 = require(\"./command\");\n/** @internal */\nclass ValidateCollectionOperation extends command_1.CommandOperation {\n  constructor(admin, collectionName, options) {\n    // Decorate command with extra options\n    const command = {\n      validate: collectionName\n    };\n    const keys = Object.keys(options);\n    for (let i = 0; i < keys.length; i++) {\n      if (Object.prototype.hasOwnProperty.call(options, keys[i]) && keys[i] !== 'session') {\n        command[keys[i]] = options[keys[i]];\n      }\n    }\n    super(admin.s.db, options);\n    this.options = options;\n    this.command = command;\n    this.collectionName = collectionName;\n  }\n  get commandName() {\n    return 'validate';\n  }\n  async execute(server, session, timeoutContext) {\n    const collectionName = this.collectionName;\n    const doc = await super.executeCommand(server, session, this.command, timeoutContext);\n    if (doc.result != null && typeof doc.result !== 'string') throw new error_1.MongoUnexpectedServerResponseError('Error with validation data');\n    if (doc.result != null && doc.result.match(/exception|corrupt/) != null) throw new error_1.MongoUnexpectedServerResponseError(`Invalid collection ${collectionName}`);\n    if (doc.valid != null && !doc.valid) throw new error_1.MongoUnexpectedServerResponseError(`Invalid collection ${collectionName}`);\n    return doc;\n  }\n}\nexports.ValidateCollectionOperation = ValidateCollectionOperation;", "map": {"version": 3, "names": ["error_1", "require", "command_1", "ValidateCollectionOperation", "CommandOperation", "constructor", "admin", "collectionName", "options", "command", "validate", "keys", "Object", "i", "length", "prototype", "hasOwnProperty", "call", "s", "db", "commandName", "execute", "server", "session", "timeoutContext", "doc", "executeCommand", "result", "MongoUnexpectedServerResponseError", "match", "valid", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\validate_collection.ts"], "sourcesContent": ["import type { Admin } from '../admin';\nimport type { Document } from '../bson';\nimport { MongoUnexpectedServerResponseError } from '../error';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { CommandOperation, type CommandOperationOptions } from './command';\n\n/** @public */\nexport interface ValidateCollectionOptions extends CommandOperationOptions {\n  /** Validates a collection in the background, without interrupting read or write traffic (only in MongoDB 4.4+) */\n  background?: boolean;\n}\n\n/** @internal */\nexport class ValidateCollectionOperation extends CommandOperation<Document> {\n  override options: ValidateCollectionOptions;\n  collectionName: string;\n  command: Document;\n\n  constructor(admin: Admin, collectionName: string, options: ValidateCollectionOptions) {\n    // Decorate command with extra options\n    const command: Document = { validate: collectionName };\n    const keys = Object.keys(options);\n    for (let i = 0; i < keys.length; i++) {\n      if (Object.prototype.hasOwnProperty.call(options, keys[i]) && keys[i] !== 'session') {\n        command[keys[i]] = (options as Document)[keys[i]];\n      }\n    }\n\n    super(admin.s.db, options);\n    this.options = options;\n    this.command = command;\n    this.collectionName = collectionName;\n  }\n\n  override get commandName() {\n    return 'validate' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<Document> {\n    const collectionName = this.collectionName;\n\n    const doc = await super.executeCommand(server, session, this.command, timeoutContext);\n    if (doc.result != null && typeof doc.result !== 'string')\n      throw new MongoUnexpectedServerResponseError('Error with validation data');\n    if (doc.result != null && doc.result.match(/exception|corrupt/) != null)\n      throw new MongoUnexpectedServerResponseError(`Invalid collection ${collectionName}`);\n    if (doc.valid != null && !doc.valid)\n      throw new MongoUnexpectedServerResponseError(`Invalid collection ${collectionName}`);\n\n    return doc;\n  }\n}\n"], "mappings": ";;;;;;AAEA,MAAAA,OAAA,GAAAC,OAAA;AAIA,MAAAC,SAAA,GAAAD,OAAA;AAQA;AACA,MAAaE,2BAA4B,SAAQD,SAAA,CAAAE,gBAA0B;EAKzEC,YAAYC,KAAY,EAAEC,cAAsB,EAAEC,OAAkC;IAClF;IACA,MAAMC,OAAO,GAAa;MAAEC,QAAQ,EAAEH;IAAc,CAAE;IACtD,MAAMI,IAAI,GAAGC,MAAM,CAACD,IAAI,CAACH,OAAO,CAAC;IACjC,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,IAAI,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,IAAID,MAAM,CAACG,SAAS,CAACC,cAAc,CAACC,IAAI,CAACT,OAAO,EAAEG,IAAI,CAACE,CAAC,CAAC,CAAC,IAAIF,IAAI,CAACE,CAAC,CAAC,KAAK,SAAS,EAAE;QACnFJ,OAAO,CAACE,IAAI,CAACE,CAAC,CAAC,CAAC,GAAIL,OAAoB,CAACG,IAAI,CAACE,CAAC,CAAC,CAAC;MACnD;IACF;IAEA,KAAK,CAACP,KAAK,CAACY,CAAC,CAACC,EAAE,EAAEX,OAAO,CAAC;IAC1B,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACF,cAAc,GAAGA,cAAc;EACtC;EAEA,IAAaa,WAAWA,CAAA;IACtB,OAAO,UAAmB;EAC5B;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMjB,cAAc,GAAG,IAAI,CAACA,cAAc;IAE1C,MAAMkB,GAAG,GAAG,MAAM,KAAK,CAACC,cAAc,CAACJ,MAAM,EAAEC,OAAO,EAAE,IAAI,CAACd,OAAO,EAAEe,cAAc,CAAC;IACrF,IAAIC,GAAG,CAACE,MAAM,IAAI,IAAI,IAAI,OAAOF,GAAG,CAACE,MAAM,KAAK,QAAQ,EACtD,MAAM,IAAI3B,OAAA,CAAA4B,kCAAkC,CAAC,4BAA4B,CAAC;IAC5E,IAAIH,GAAG,CAACE,MAAM,IAAI,IAAI,IAAIF,GAAG,CAACE,MAAM,CAACE,KAAK,CAAC,mBAAmB,CAAC,IAAI,IAAI,EACrE,MAAM,IAAI7B,OAAA,CAAA4B,kCAAkC,CAAC,sBAAsBrB,cAAc,EAAE,CAAC;IACtF,IAAIkB,GAAG,CAACK,KAAK,IAAI,IAAI,IAAI,CAACL,GAAG,CAACK,KAAK,EACjC,MAAM,IAAI9B,OAAA,CAAA4B,kCAAkC,CAAC,sBAAsBrB,cAAc,EAAE,CAAC;IAEtF,OAAOkB,GAAG;EACZ;;AAzCFM,OAAA,CAAA5B,2BAAA,GAAAA,2BAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}