{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.RenameOperation = void 0;\nconst collection_1 = require(\"../collection\");\nconst utils_1 = require(\"../utils\");\nconst command_1 = require(\"./command\");\nconst operation_1 = require(\"./operation\");\n/** @internal */\nclass RenameOperation extends command_1.CommandOperation {\n  constructor(collection, newName, options) {\n    super(collection, options);\n    this.collection = collection;\n    this.newName = newName;\n    this.options = options;\n    this.ns = new utils_1.MongoDBNamespace('admin', '$cmd');\n  }\n  get commandName() {\n    return 'renameCollection';\n  }\n  async execute(server, session, timeoutContext) {\n    // Build the command\n    const renameCollection = this.collection.namespace;\n    const toCollection = this.collection.s.namespace.withCollection(this.newName).toString();\n    const dropTarget = typeof this.options.dropTarget === 'boolean' ? this.options.dropTarget : false;\n    const command = {\n      renameCollection: renameCollection,\n      to: toCollection,\n      dropTarget: dropTarget\n    };\n    await super.executeCommand(server, session, command, timeoutContext);\n    return new collection_1.Collection(this.collection.s.db, this.newName, this.collection.s.options);\n  }\n}\nexports.RenameOperation = RenameOperation;\n(0, operation_1.defineAspects)(RenameOperation, [operation_1.Aspect.WRITE_OPERATION]);", "map": {"version": 3, "names": ["collection_1", "require", "utils_1", "command_1", "operation_1", "RenameOperation", "CommandOperation", "constructor", "collection", "newName", "options", "ns", "MongoDBNamespace", "commandName", "execute", "server", "session", "timeoutContext", "renameCollection", "namespace", "toCollection", "s", "withCollection", "toString", "drop<PERSON>ar<PERSON>", "command", "to", "executeCommand", "Collection", "db", "exports", "defineAspects", "Aspect", "WRITE_OPERATION"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\rename.ts"], "sourcesContent": ["import type { Document } from '../bson';\nimport { Collection } from '../collection';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { MongoDBNamespace } from '../utils';\nimport { CommandOperation, type CommandOperationOptions } from './command';\nimport { Aspect, defineAspects } from './operation';\n\n/** @public */\nexport interface RenameOptions extends CommandOperationOptions {\n  /** Drop the target name collection if it previously exists. */\n  dropTarget?: boolean;\n  /** Unclear */\n  new_collection?: boolean;\n}\n\n/** @internal */\nexport class RenameOperation extends CommandOperation<Document> {\n  constructor(\n    public collection: Collection,\n    public newName: string,\n    public override options: RenameOptions\n  ) {\n    super(collection, options);\n    this.ns = new MongoDBNamespace('admin', '$cmd');\n  }\n\n  override get commandName(): string {\n    return 'renameCollection' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<Collection> {\n    // Build the command\n    const renameCollection = this.collection.namespace;\n    const toCollection = this.collection.s.namespace.withCollection(this.newName).toString();\n    const dropTarget =\n      typeof this.options.dropTarget === 'boolean' ? this.options.dropTarget : false;\n\n    const command = {\n      renameCollection: renameCollection,\n      to: toCollection,\n      dropTarget: dropTarget\n    };\n\n    await super.executeCommand(server, session, command, timeoutContext);\n    return new Collection(this.collection.s.db, this.newName, this.collection.s.options);\n  }\n}\n\ndefineAspects(RenameOperation, [Aspect.WRITE_OPERATION]);\n"], "mappings": ";;;;;;AACA,MAAAA,YAAA,GAAAC,OAAA;AAIA,MAAAC,OAAA,GAAAD,OAAA;AACA,MAAAE,SAAA,GAAAF,OAAA;AACA,MAAAG,WAAA,GAAAH,OAAA;AAUA;AACA,MAAaI,eAAgB,SAAQF,SAAA,CAAAG,gBAA0B;EAC7DC,YACSC,UAAsB,EACtBC,OAAe,EACNC,OAAsB;IAEtC,KAAK,CAACF,UAAU,EAAEE,OAAO,CAAC;IAJnB,KAAAF,UAAU,GAAVA,UAAU;IACV,KAAAC,OAAO,GAAPA,OAAO;IACE,KAAAC,OAAO,GAAPA,OAAO;IAGvB,IAAI,CAACC,EAAE,GAAG,IAAIT,OAAA,CAAAU,gBAAgB,CAAC,OAAO,EAAE,MAAM,CAAC;EACjD;EAEA,IAAaC,WAAWA,CAAA;IACtB,OAAO,kBAA2B;EACpC;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B;IACA,MAAMC,gBAAgB,GAAG,IAAI,CAACV,UAAU,CAACW,SAAS;IAClD,MAAMC,YAAY,GAAG,IAAI,CAACZ,UAAU,CAACa,CAAC,CAACF,SAAS,CAACG,cAAc,CAAC,IAAI,CAACb,OAAO,CAAC,CAACc,QAAQ,EAAE;IACxF,MAAMC,UAAU,GACd,OAAO,IAAI,CAACd,OAAO,CAACc,UAAU,KAAK,SAAS,GAAG,IAAI,CAACd,OAAO,CAACc,UAAU,GAAG,KAAK;IAEhF,MAAMC,OAAO,GAAG;MACdP,gBAAgB,EAAEA,gBAAgB;MAClCQ,EAAE,EAAEN,YAAY;MAChBI,UAAU,EAAEA;KACb;IAED,MAAM,KAAK,CAACG,cAAc,CAACZ,MAAM,EAAEC,OAAO,EAAES,OAAO,EAAER,cAAc,CAAC;IACpE,OAAO,IAAIjB,YAAA,CAAA4B,UAAU,CAAC,IAAI,CAACpB,UAAU,CAACa,CAAC,CAACQ,EAAE,EAAE,IAAI,CAACpB,OAAO,EAAE,IAAI,CAACD,UAAU,CAACa,CAAC,CAACX,OAAO,CAAC;EACtF;;AAjCFoB,OAAA,CAAAzB,eAAA,GAAAA,eAAA;AAoCA,IAAAD,WAAA,CAAA2B,aAAa,EAAC1B,eAAe,EAAE,CAACD,WAAA,CAAA4B,MAAM,CAACC,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}