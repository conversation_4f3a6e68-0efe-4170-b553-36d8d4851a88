{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.CreateSearchIndexesOperation = void 0;\nconst operation_1 = require(\"../operation\");\n/** @internal */\nclass CreateSearchIndexesOperation extends operation_1.AbstractOperation {\n  constructor(collection, descriptions) {\n    super();\n    this.collection = collection;\n    this.descriptions = descriptions;\n  }\n  get commandName() {\n    return 'createSearchIndexes';\n  }\n  async execute(server, session, timeoutContext) {\n    const namespace = this.collection.fullNamespace;\n    const command = {\n      createSearchIndexes: namespace.collection,\n      indexes: this.descriptions\n    };\n    const res = await server.command(namespace, command, {\n      session,\n      timeoutContext\n    });\n    const indexesCreated = res?.indexesCreated ?? [];\n    return indexesCreated.map(({\n      name\n    }) => name);\n  }\n}\nexports.CreateSearchIndexesOperation = CreateSearchIndexesOperation;", "map": {"version": 3, "names": ["operation_1", "require", "CreateSearchIndexesOperation", "AbstractOperation", "constructor", "collection", "descriptions", "commandName", "execute", "server", "session", "timeoutContext", "namespace", "fullNamespace", "command", "createSearchIndexes", "indexes", "res", "indexesCreated", "map", "name", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\search_indexes\\create.ts"], "sourcesContent": ["import type { Document } from '../../bson';\nimport type { Collection } from '../../collection';\nimport type { Server } from '../../sdam/server';\nimport type { ClientSession } from '../../sessions';\nimport { type TimeoutContext } from '../../timeout';\nimport { AbstractOperation } from '../operation';\n\n/**\n * @public\n */\nexport interface SearchIndexDescription extends Document {\n  /** The name of the index. */\n  name?: string;\n\n  /** The index definition. */\n  definition: Document;\n\n  /** The type of the index.  Currently `search` or `vectorSearch` are supported. */\n  type?: string;\n}\n\n/** @internal */\nexport class CreateSearchIndexesOperation extends AbstractOperation<string[]> {\n  constructor(\n    private readonly collection: Collection,\n    private readonly descriptions: ReadonlyArray<SearchIndexDescription>\n  ) {\n    super();\n  }\n\n  override get commandName() {\n    return 'createSearchIndexes' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<string[]> {\n    const namespace = this.collection.fullNamespace;\n    const command = {\n      createSearchIndexes: namespace.collection,\n      indexes: this.descriptions\n    };\n\n    const res = await server.command(namespace, command, {\n      session,\n      timeoutContext\n    });\n\n    const indexesCreated: Array<{ name: string }> = res?.indexesCreated ?? [];\n    return indexesCreated.map(({ name }) => name);\n  }\n}\n"], "mappings": ";;;;;;AAKA,MAAAA,WAAA,GAAAC,OAAA;AAgBA;AACA,MAAaC,4BAA6B,SAAQF,WAAA,CAAAG,iBAA2B;EAC3EC,YACmBC,UAAsB,EACtBC,YAAmD;IAEpE,KAAK,EAAE;IAHU,KAAAD,UAAU,GAAVA,UAAU;IACV,KAAAC,YAAY,GAAZA,YAAY;EAG/B;EAEA,IAAaC,WAAWA,CAAA;IACtB,OAAO,qBAA8B;EACvC;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMC,SAAS,GAAG,IAAI,CAACP,UAAU,CAACQ,aAAa;IAC/C,MAAMC,OAAO,GAAG;MACdC,mBAAmB,EAAEH,SAAS,CAACP,UAAU;MACzCW,OAAO,EAAE,IAAI,CAACV;KACf;IAED,MAAMW,GAAG,GAAG,MAAMR,MAAM,CAACK,OAAO,CAACF,SAAS,EAAEE,OAAO,EAAE;MACnDJ,OAAO;MACPC;KACD,CAAC;IAEF,MAAMO,cAAc,GAA4BD,GAAG,EAAEC,cAAc,IAAI,EAAE;IACzE,OAAOA,cAAc,CAACC,GAAG,CAAC,CAAC;MAAEC;IAAI,CAAE,KAAKA,IAAI,CAAC;EAC/C;;AA9BFC,OAAA,CAAAnB,4BAAA,GAAAA,4BAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}