import React, { useEffect } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import toast, { Toaster } from 'react-hot-toast';

import { RootState, AppDispatch } from './store/store';
import { getCurrentUser } from './store/slices/authSlice';
import { setLanguage } from './store/slices/uiSlice';

// Layout Components
import Layout from './components/layout/Layout';
import AuthLayout from './components/layout/AuthLayout';

// Page Components
import LoginPage from './pages/auth/LoginPage';
import RegisterPage from './pages/auth/RegisterPage';
import DashboardPage from './pages/dashboard/DashboardPage';
import AnimalsPage from './pages/animals/AnimalsPage';
import AnimalDetailsPage from './pages/animals/AnimalDetailsPage';
import HealthPage from './pages/health/HealthPage';
import BreedingPage from './pages/breeding/BreedingPage';
import FeedingPage from './pages/feeding/FeedingPage';
import FinancialPage from './pages/financial/FinancialPage';
import ReportsPage from './pages/reports/ReportsPage';
import SettingsPage from './pages/settings/SettingsPage';
import NotFoundPage from './pages/NotFoundPage';

// Components
import LoadingSpinner from './components/common/LoadingSpinner';
import ProtectedRoute from './components/auth/ProtectedRoute';

function App() {
  const dispatch = useDispatch<AppDispatch>();
  const { i18n } = useTranslation();
  
  const { isAuthenticated, isLoading: authLoading, token } = useSelector(
    (state: RootState) => state.auth
  );
  const { language } = useSelector((state: RootState) => state.ui);

  useEffect(() => {
    // Set language from Redux store
    if (language && i18n.language !== language) {
      i18n.changeLanguage(language);
    }
  }, [language, i18n]);

  useEffect(() => {
    // Auto-login if token exists
    if (token && !isAuthenticated) {
      dispatch(getCurrentUser());
    }
  }, [dispatch, token, isAuthenticated]);

  useEffect(() => {
    // Sync language changes with Redux store
    const handleLanguageChange = (lng: string) => {
      if (['en', 'af', 'st', 'tn', 'zu'].includes(lng)) {
        dispatch(setLanguage(lng as any));
      }
    };

    i18n.on('languageChanged', handleLanguageChange);
    return () => {
      i18n.off('languageChanged', handleLanguageChange);
    };
  }, [dispatch, i18n]);

  // Show loading spinner while checking authentication
  if (authLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <LoadingSpinner size="large" />
      </div>
    );
  }

  return (
    <div className="App">
      <Routes>
        {/* Public Routes */}
        <Route
          path="/login"
          element={
            !isAuthenticated ? (
              <AuthLayout>
                <LoginPage />
              </AuthLayout>
            ) : (
              <Navigate to="/dashboard" replace />
            )
          }
        />
        <Route
          path="/register"
          element={
            !isAuthenticated ? (
              <AuthLayout>
                <RegisterPage />
              </AuthLayout>
            ) : (
              <Navigate to="/dashboard" replace />
            )
          }
        />

        {/* Protected Routes */}
        <Route
          path="/"
          element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }
        >
          <Route index element={<Navigate to="/dashboard" replace />} />
          <Route path="dashboard" element={<DashboardPage />} />
          
          {/* Animal Management */}
          <Route path="animals" element={<AnimalsPage />} />
          <Route path="animals/:id" element={<AnimalDetailsPage />} />
          
          {/* Health Management */}
          <Route path="health" element={<HealthPage />} />
          
          {/* Breeding Management */}
          <Route path="breeding" element={<BreedingPage />} />
          
          {/* Feed Management */}
          <Route path="feeding" element={<FeedingPage />} />
          
          {/* Financial Management */}
          <Route path="financial" element={<FinancialPage />} />
          
          {/* Reports & Analytics */}
          <Route path="reports" element={<ReportsPage />} />
          
          {/* Settings */}
          <Route path="settings" element={<SettingsPage />} />
        </Route>

        {/* 404 Page */}
        <Route path="*" element={<NotFoundPage />} />
      </Routes>

      {/* Toast Notifications */}
      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: '#363636',
            color: '#fff',
          },
          success: {
            duration: 3000,
            iconTheme: {
              primary: '#059669',
              secondary: '#fff',
            },
          },
          error: {
            duration: 5000,
            iconTheme: {
              primary: '#dc2626',
              secondary: '#fff',
            },
          },
        }}
      />
    </div>
  );
}

export default App;
