{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ClientBulkWriteOperation = void 0;\nconst beta_1 = require(\"../../beta\");\nconst responses_1 = require(\"../../cmap/wire_protocol/responses\");\nconst utils_1 = require(\"../../utils\");\nconst command_1 = require(\"../command\");\nconst operation_1 = require(\"../operation\");\n/**\n * Executes a single client bulk write operation within a potential batch.\n * @internal\n */\nclass ClientBulkWriteOperation extends command_1.CommandOperation {\n  get commandName() {\n    return 'bulkWrite';\n  }\n  constructor(commandBuilder, options) {\n    super(undefined, options);\n    this.commandBuilder = commandBuilder;\n    this.options = options;\n    this.ns = new utils_1.MongoDBNamespace('admin', '$cmd');\n  }\n  resetBatch() {\n    return this.commandBuilder.resetBatch();\n  }\n  get canRetryWrite() {\n    return this.commandBuilder.isBatchRetryable;\n  }\n  /**\n   * Execute the command. Superclass will handle write concern, etc.\n   * @param server - The server.\n   * @param session - The session.\n   * @returns The response.\n   */\n  async execute(server, session, timeoutContext) {\n    let command;\n    if (server.description.type === beta_1.ServerType.LoadBalancer) {\n      if (session) {\n        let connection;\n        if (!session.pinnedConnection) {\n          // Checkout a connection to build the command.\n          connection = await server.pool.checkOut({\n            timeoutContext\n          });\n          // Pin the connection to the session so it get used to execute the command and we do not\n          // perform a double check-in/check-out.\n          session.pin(connection);\n        } else {\n          connection = session.pinnedConnection;\n        }\n        command = this.commandBuilder.buildBatch(connection.hello?.maxMessageSizeBytes, connection.hello?.maxWriteBatchSize, connection.hello?.maxBsonObjectSize);\n      } else {\n        throw new beta_1.MongoClientBulkWriteExecutionError('Session provided to the client bulk write operation must be present.');\n      }\n    } else {\n      // At this point we have a server and the auto connect code has already\n      // run in executeOperation, so the server description will be populated.\n      // We can use that to build the command.\n      if (!server.description.maxWriteBatchSize || !server.description.maxMessageSizeBytes || !server.description.maxBsonObjectSize) {\n        throw new beta_1.MongoClientBulkWriteExecutionError('In order to execute a client bulk write, both maxWriteBatchSize, maxMessageSizeBytes and maxBsonObjectSize must be provided by the servers hello response.');\n      }\n      command = this.commandBuilder.buildBatch(server.description.maxMessageSizeBytes, server.description.maxWriteBatchSize, server.description.maxBsonObjectSize);\n    }\n    // Check after the batch is built if we cannot retry it and override the option.\n    if (!this.canRetryWrite) {\n      this.options.willRetryWrite = false;\n    }\n    return await super.executeCommand(server, session, command, timeoutContext, responses_1.ClientBulkWriteCursorResponse);\n  }\n}\nexports.ClientBulkWriteOperation = ClientBulkWriteOperation;\n// Skipping the collation as it goes on the individual ops.\n(0, operation_1.defineAspects)(ClientBulkWriteOperation, [operation_1.Aspect.WRITE_OPERATION, operation_1.Aspect.SKIP_COLLATION, operation_1.Aspect.CURSOR_CREATING, operation_1.Aspect.RETRYABLE, operation_1.Aspect.COMMAND_BATCHING]);", "map": {"version": 3, "names": ["beta_1", "require", "responses_1", "utils_1", "command_1", "operation_1", "ClientBulkWriteOperation", "CommandOperation", "commandName", "constructor", "commandBuilder", "options", "undefined", "ns", "MongoDBNamespace", "resetBatch", "canRetryWrite", "isBatchRetryable", "execute", "server", "session", "timeoutContext", "command", "description", "type", "ServerType", "LoadBalancer", "connection", "pinnedConnection", "pool", "checkOut", "pin", "buildBatch", "hello", "maxMessageSizeBytes", "maxWriteBatchSize", "maxBsonObjectSize", "MongoClientBulkWriteExecutionError", "willRetryWrite", "executeCommand", "ClientBulkWriteCursorResponse", "exports", "defineAspects", "Aspect", "WRITE_OPERATION", "SKIP_COLLATION", "CURSOR_CREATING", "RETRYABLE", "COMMAND_BATCHING"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\client_bulk_write\\client_bulk_write.ts"], "sourcesContent": ["import { MongoClientBulkWriteExecutionError, ServerType } from '../../beta';\nimport { ClientBulkWriteCursorResponse } from '../../cmap/wire_protocol/responses';\nimport type { Server } from '../../sdam/server';\nimport type { ClientSession } from '../../sessions';\nimport { type TimeoutContext } from '../../timeout';\nimport { MongoDBNamespace } from '../../utils';\nimport { CommandOperation } from '../command';\nimport { Aspect, defineAspects } from '../operation';\nimport { type ClientBulkWriteCommandBuilder } from './command_builder';\nimport { type ClientBulkWriteOptions } from './common';\n\n/**\n * Executes a single client bulk write operation within a potential batch.\n * @internal\n */\nexport class ClientBulkWriteOperation extends CommandOperation<ClientBulkWriteCursorResponse> {\n  commandBuilder: ClientBulkWriteCommandBuilder;\n  override options: ClientBulkWriteOptions;\n\n  override get commandName() {\n    return 'bulkWrite' as const;\n  }\n\n  constructor(commandBuilder: ClientBulkWriteCommandBuilder, options: ClientBulkWriteOptions) {\n    super(undefined, options);\n    this.commandBuilder = commandBuilder;\n    this.options = options;\n    this.ns = new MongoDBNamespace('admin', '$cmd');\n  }\n\n  override resetBatch(): boolean {\n    return this.commandBuilder.resetBatch();\n  }\n\n  override get canRetryWrite(): boolean {\n    return this.commandBuilder.isBatchRetryable;\n  }\n\n  /**\n   * Execute the command. Superclass will handle write concern, etc.\n   * @param server - The server.\n   * @param session - The session.\n   * @returns The response.\n   */\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<ClientBulkWriteCursorResponse> {\n    let command;\n\n    if (server.description.type === ServerType.LoadBalancer) {\n      if (session) {\n        let connection;\n        if (!session.pinnedConnection) {\n          // Checkout a connection to build the command.\n          connection = await server.pool.checkOut({ timeoutContext });\n          // Pin the connection to the session so it get used to execute the command and we do not\n          // perform a double check-in/check-out.\n          session.pin(connection);\n        } else {\n          connection = session.pinnedConnection;\n        }\n        command = this.commandBuilder.buildBatch(\n          connection.hello?.maxMessageSizeBytes,\n          connection.hello?.maxWriteBatchSize,\n          connection.hello?.maxBsonObjectSize\n        );\n      } else {\n        throw new MongoClientBulkWriteExecutionError(\n          'Session provided to the client bulk write operation must be present.'\n        );\n      }\n    } else {\n      // At this point we have a server and the auto connect code has already\n      // run in executeOperation, so the server description will be populated.\n      // We can use that to build the command.\n      if (\n        !server.description.maxWriteBatchSize ||\n        !server.description.maxMessageSizeBytes ||\n        !server.description.maxBsonObjectSize\n      ) {\n        throw new MongoClientBulkWriteExecutionError(\n          'In order to execute a client bulk write, both maxWriteBatchSize, maxMessageSizeBytes and maxBsonObjectSize must be provided by the servers hello response.'\n        );\n      }\n      command = this.commandBuilder.buildBatch(\n        server.description.maxMessageSizeBytes,\n        server.description.maxWriteBatchSize,\n        server.description.maxBsonObjectSize\n      );\n    }\n\n    // Check after the batch is built if we cannot retry it and override the option.\n    if (!this.canRetryWrite) {\n      this.options.willRetryWrite = false;\n    }\n    return await super.executeCommand(\n      server,\n      session,\n      command,\n      timeoutContext,\n      ClientBulkWriteCursorResponse\n    );\n  }\n}\n\n// Skipping the collation as it goes on the individual ops.\ndefineAspects(ClientBulkWriteOperation, [\n  Aspect.WRITE_OPERATION,\n  Aspect.SKIP_COLLATION,\n  Aspect.CURSOR_CREATING,\n  Aspect.RETRYABLE,\n  Aspect.COMMAND_BATCHING\n]);\n"], "mappings": ";;;;;;AAAA,MAAAA,MAAA,GAAAC,OAAA;AACA,MAAAC,WAAA,GAAAD,OAAA;AAIA,MAAAE,OAAA,GAAAF,OAAA;AACA,MAAAG,SAAA,GAAAH,OAAA;AACA,MAAAI,WAAA,GAAAJ,OAAA;AAIA;;;;AAIA,MAAaK,wBAAyB,SAAQF,SAAA,CAAAG,gBAA+C;EAI3F,IAAaC,WAAWA,CAAA;IACtB,OAAO,WAAoB;EAC7B;EAEAC,YAAYC,cAA6C,EAAEC,OAA+B;IACxF,KAAK,CAACC,SAAS,EAAED,OAAO,CAAC;IACzB,IAAI,CAACD,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,EAAE,GAAG,IAAIV,OAAA,CAAAW,gBAAgB,CAAC,OAAO,EAAE,MAAM,CAAC;EACjD;EAESC,UAAUA,CAAA;IACjB,OAAO,IAAI,CAACL,cAAc,CAACK,UAAU,EAAE;EACzC;EAEA,IAAaC,aAAaA,CAAA;IACxB,OAAO,IAAI,CAACN,cAAc,CAACO,gBAAgB;EAC7C;EAEA;;;;;;EAMS,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,IAAIC,OAAO;IAEX,IAAIH,MAAM,CAACI,WAAW,CAACC,IAAI,KAAKxB,MAAA,CAAAyB,UAAU,CAACC,YAAY,EAAE;MACvD,IAAIN,OAAO,EAAE;QACX,IAAIO,UAAU;QACd,IAAI,CAACP,OAAO,CAACQ,gBAAgB,EAAE;UAC7B;UACAD,UAAU,GAAG,MAAMR,MAAM,CAACU,IAAI,CAACC,QAAQ,CAAC;YAAET;UAAc,CAAE,CAAC;UAC3D;UACA;UACAD,OAAO,CAACW,GAAG,CAACJ,UAAU,CAAC;QACzB,CAAC,MAAM;UACLA,UAAU,GAAGP,OAAO,CAACQ,gBAAgB;QACvC;QACAN,OAAO,GAAG,IAAI,CAACZ,cAAc,CAACsB,UAAU,CACtCL,UAAU,CAACM,KAAK,EAAEC,mBAAmB,EACrCP,UAAU,CAACM,KAAK,EAAEE,iBAAiB,EACnCR,UAAU,CAACM,KAAK,EAAEG,iBAAiB,CACpC;MACH,CAAC,MAAM;QACL,MAAM,IAAIpC,MAAA,CAAAqC,kCAAkC,CAC1C,sEAAsE,CACvE;MACH;IACF,CAAC,MAAM;MACL;MACA;MACA;MACA,IACE,CAAClB,MAAM,CAACI,WAAW,CAACY,iBAAiB,IACrC,CAAChB,MAAM,CAACI,WAAW,CAACW,mBAAmB,IACvC,CAACf,MAAM,CAACI,WAAW,CAACa,iBAAiB,EACrC;QACA,MAAM,IAAIpC,MAAA,CAAAqC,kCAAkC,CAC1C,4JAA4J,CAC7J;MACH;MACAf,OAAO,GAAG,IAAI,CAACZ,cAAc,CAACsB,UAAU,CACtCb,MAAM,CAACI,WAAW,CAACW,mBAAmB,EACtCf,MAAM,CAACI,WAAW,CAACY,iBAAiB,EACpChB,MAAM,CAACI,WAAW,CAACa,iBAAiB,CACrC;IACH;IAEA;IACA,IAAI,CAAC,IAAI,CAACpB,aAAa,EAAE;MACvB,IAAI,CAACL,OAAO,CAAC2B,cAAc,GAAG,KAAK;IACrC;IACA,OAAO,MAAM,KAAK,CAACC,cAAc,CAC/BpB,MAAM,EACNC,OAAO,EACPE,OAAO,EACPD,cAAc,EACdnB,WAAA,CAAAsC,6BAA6B,CAC9B;EACH;;AAzFFC,OAAA,CAAAnC,wBAAA,GAAAA,wBAAA;AA4FA;AACA,IAAAD,WAAA,CAAAqC,aAAa,EAACpC,wBAAwB,EAAE,CACtCD,WAAA,CAAAsC,MAAM,CAACC,eAAe,EACtBvC,WAAA,CAAAsC,MAAM,CAACE,cAAc,EACrBxC,WAAA,CAAAsC,MAAM,CAACG,eAAe,EACtBzC,WAAA,CAAAsC,MAAM,CAACI,SAAS,EAChB1C,WAAA,CAAAsC,MAAM,CAACK,gBAAgB,CACxB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}