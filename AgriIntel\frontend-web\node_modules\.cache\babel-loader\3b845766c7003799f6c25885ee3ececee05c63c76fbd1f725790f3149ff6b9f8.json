{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.AggregateOperation = exports.DB_AGGREGATE_COLLECTION = void 0;\nconst responses_1 = require(\"../cmap/wire_protocol/responses\");\nconst error_1 = require(\"../error\");\nconst utils_1 = require(\"../utils\");\nconst write_concern_1 = require(\"../write_concern\");\nconst command_1 = require(\"./command\");\nconst operation_1 = require(\"./operation\");\n/** @internal */\n// eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\nexports.DB_AGGREGATE_COLLECTION = 1;\nconst MIN_WIRE_VERSION_$OUT_READ_CONCERN_SUPPORT = 8;\n/** @internal */\nclass AggregateOperation extends command_1.CommandOperation {\n  constructor(ns, pipeline, options) {\n    super(undefined, {\n      ...options,\n      dbName: ns.db\n    });\n    this.options = {\n      ...options\n    };\n    // Covers when ns.collection is null, undefined or the empty string, use DB_AGGREGATE_COLLECTION\n    this.target = ns.collection || exports.DB_AGGREGATE_COLLECTION;\n    this.pipeline = pipeline;\n    // determine if we have a write stage, override read preference if so\n    this.hasWriteStage = false;\n    if (typeof options?.out === 'string') {\n      this.pipeline = this.pipeline.concat({\n        $out: options.out\n      });\n      this.hasWriteStage = true;\n    } else if (pipeline.length > 0) {\n      const finalStage = pipeline[pipeline.length - 1];\n      if (finalStage.$out || finalStage.$merge) {\n        this.hasWriteStage = true;\n      }\n    }\n    if (this.hasWriteStage) {\n      this.trySecondaryWrite = true;\n    } else {\n      delete this.options.writeConcern;\n    }\n    if (this.explain && this.writeConcern) {\n      throw new error_1.MongoInvalidArgumentError('Option \"explain\" cannot be used on an aggregate call with writeConcern');\n    }\n    if (options?.cursor != null && typeof options.cursor !== 'object') {\n      throw new error_1.MongoInvalidArgumentError('Cursor options must be an object');\n    }\n  }\n  get commandName() {\n    return 'aggregate';\n  }\n  get canRetryRead() {\n    return !this.hasWriteStage;\n  }\n  addToPipeline(stage) {\n    this.pipeline.push(stage);\n  }\n  async execute(server, session, timeoutContext) {\n    const options = this.options;\n    const serverWireVersion = (0, utils_1.maxWireVersion)(server);\n    const command = {\n      aggregate: this.target,\n      pipeline: this.pipeline\n    };\n    if (this.hasWriteStage && serverWireVersion < MIN_WIRE_VERSION_$OUT_READ_CONCERN_SUPPORT) {\n      this.readConcern = undefined;\n    }\n    if (this.hasWriteStage && this.writeConcern) {\n      write_concern_1.WriteConcern.apply(command, this.writeConcern);\n    }\n    if (options.bypassDocumentValidation === true) {\n      command.bypassDocumentValidation = options.bypassDocumentValidation;\n    }\n    if (typeof options.allowDiskUse === 'boolean') {\n      command.allowDiskUse = options.allowDiskUse;\n    }\n    if (options.hint) {\n      command.hint = options.hint;\n    }\n    if (options.let) {\n      command.let = options.let;\n    }\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (options.comment !== undefined) {\n      command.comment = options.comment;\n    }\n    command.cursor = options.cursor || {};\n    if (options.batchSize && !this.hasWriteStage) {\n      command.cursor.batchSize = options.batchSize;\n    }\n    return await super.executeCommand(server, session, command, timeoutContext, this.explain ? responses_1.ExplainedCursorResponse : responses_1.CursorResponse);\n  }\n}\nexports.AggregateOperation = AggregateOperation;\n(0, operation_1.defineAspects)(AggregateOperation, [operation_1.Aspect.READ_OPERATION, operation_1.Aspect.RETRYABLE, operation_1.Aspect.EXPLAINABLE, operation_1.Aspect.CURSOR_CREATING]);", "map": {"version": 3, "names": ["responses_1", "require", "error_1", "utils_1", "write_concern_1", "command_1", "operation_1", "exports", "DB_AGGREGATE_COLLECTION", "MIN_WIRE_VERSION_$OUT_READ_CONCERN_SUPPORT", "AggregateOperation", "CommandOperation", "constructor", "ns", "pipeline", "options", "undefined", "dbN<PERSON>", "db", "target", "collection", "hasWriteStage", "out", "concat", "$out", "length", "finalStage", "$merge", "trySecondaryWrite", "writeConcern", "explain", "MongoInvalidArgumentError", "cursor", "commandName", "canRetryRead", "addToPipeline", "stage", "push", "execute", "server", "session", "timeoutContext", "serverWireVersion", "maxWireVersion", "command", "aggregate", "readConcern", "WriteConcern", "apply", "bypassDocumentValidation", "allowDiskUse", "hint", "let", "comment", "batchSize", "executeCommand", "ExplainedCursorResponse", "CursorResponse", "defineAspects", "Aspect", "READ_OPERATION", "RETRYABLE", "EXPLAINABLE", "CURSOR_CREATING"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\aggregate.ts"], "sourcesContent": ["import type { Document } from '../bson';\nimport { CursorResponse, ExplainedCursorResponse } from '../cmap/wire_protocol/responses';\nimport { type CursorTimeoutMode } from '../cursor/abstract_cursor';\nimport { MongoInvalidArgumentError } from '../error';\nimport { type ExplainOptions } from '../explain';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { maxWireVersion, type MongoDBNamespace } from '../utils';\nimport { WriteConcern } from '../write_concern';\nimport { type CollationOptions, CommandOperation, type CommandOperationOptions } from './command';\nimport { Aspect, defineAspects, type Hint } from './operation';\n\n/** @internal */\n// eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\nexport const DB_AGGREGATE_COLLECTION = 1 as const;\nconst MIN_WIRE_VERSION_$OUT_READ_CONCERN_SUPPORT = 8;\n\n/** @public */\nexport interface AggregateOptions extends Omit<CommandOperationOptions, 'explain'> {\n  /** allowDiskUse lets the server know if it can use disk to store temporary results for the aggregation (requires mongodb 2.6 \\>). */\n  allowDiskUse?: boolean;\n  /** The number of documents to return per batch. See [aggregation documentation](https://www.mongodb.com/docs/manual/reference/command/aggregate). */\n  batchSize?: number;\n  /** Allow driver to bypass schema validation. */\n  bypassDocumentValidation?: boolean;\n  /** Return the query as cursor, on 2.6 \\> it returns as a real cursor on pre 2.6 it returns as an emulated cursor. */\n  cursor?: Document;\n  /**\n   * Specifies a cumulative time limit in milliseconds for processing operations on the cursor. MongoDB interrupts the operation at the earliest following interrupt point.\n   */\n  maxTimeMS?: number;\n  /** The maximum amount of time for the server to wait on new documents to satisfy a tailable cursor query. */\n  maxAwaitTimeMS?: number;\n  /** Specify collation. */\n  collation?: CollationOptions;\n  /** Add an index selection hint to an aggregation command */\n  hint?: Hint;\n  /** Map of parameter names and values that can be accessed using $$var (requires MongoDB 5.0). */\n  let?: Document;\n\n  out?: string;\n\n  /**\n   * Specifies the verbosity mode for the explain output.\n   * @deprecated This API is deprecated in favor of `collection.aggregate().explain()`\n   * or `db.aggregate().explain()`.\n   */\n  explain?: ExplainOptions['explain'];\n  /** @internal */\n  timeoutMode?: CursorTimeoutMode;\n}\n\n/** @internal */\nexport class AggregateOperation extends CommandOperation<CursorResponse> {\n  override options: AggregateOptions;\n  target: string | typeof DB_AGGREGATE_COLLECTION;\n  pipeline: Document[];\n  hasWriteStage: boolean;\n\n  constructor(ns: MongoDBNamespace, pipeline: Document[], options?: AggregateOptions) {\n    super(undefined, { ...options, dbName: ns.db });\n\n    this.options = { ...options };\n\n    // Covers when ns.collection is null, undefined or the empty string, use DB_AGGREGATE_COLLECTION\n    this.target = ns.collection || DB_AGGREGATE_COLLECTION;\n\n    this.pipeline = pipeline;\n\n    // determine if we have a write stage, override read preference if so\n    this.hasWriteStage = false;\n    if (typeof options?.out === 'string') {\n      this.pipeline = this.pipeline.concat({ $out: options.out });\n      this.hasWriteStage = true;\n    } else if (pipeline.length > 0) {\n      const finalStage = pipeline[pipeline.length - 1];\n      if (finalStage.$out || finalStage.$merge) {\n        this.hasWriteStage = true;\n      }\n    }\n\n    if (this.hasWriteStage) {\n      this.trySecondaryWrite = true;\n    } else {\n      delete this.options.writeConcern;\n    }\n\n    if (this.explain && this.writeConcern) {\n      throw new MongoInvalidArgumentError(\n        'Option \"explain\" cannot be used on an aggregate call with writeConcern'\n      );\n    }\n\n    if (options?.cursor != null && typeof options.cursor !== 'object') {\n      throw new MongoInvalidArgumentError('Cursor options must be an object');\n    }\n  }\n\n  override get commandName() {\n    return 'aggregate' as const;\n  }\n\n  override get canRetryRead(): boolean {\n    return !this.hasWriteStage;\n  }\n\n  addToPipeline(stage: Document): void {\n    this.pipeline.push(stage);\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<CursorResponse> {\n    const options: AggregateOptions = this.options;\n    const serverWireVersion = maxWireVersion(server);\n    const command: Document = { aggregate: this.target, pipeline: this.pipeline };\n\n    if (this.hasWriteStage && serverWireVersion < MIN_WIRE_VERSION_$OUT_READ_CONCERN_SUPPORT) {\n      this.readConcern = undefined;\n    }\n\n    if (this.hasWriteStage && this.writeConcern) {\n      WriteConcern.apply(command, this.writeConcern);\n    }\n\n    if (options.bypassDocumentValidation === true) {\n      command.bypassDocumentValidation = options.bypassDocumentValidation;\n    }\n\n    if (typeof options.allowDiskUse === 'boolean') {\n      command.allowDiskUse = options.allowDiskUse;\n    }\n\n    if (options.hint) {\n      command.hint = options.hint;\n    }\n\n    if (options.let) {\n      command.let = options.let;\n    }\n\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (options.comment !== undefined) {\n      command.comment = options.comment;\n    }\n\n    command.cursor = options.cursor || {};\n    if (options.batchSize && !this.hasWriteStage) {\n      command.cursor.batchSize = options.batchSize;\n    }\n\n    return await super.executeCommand(\n      server,\n      session,\n      command,\n      timeoutContext,\n      this.explain ? ExplainedCursorResponse : CursorResponse\n    );\n  }\n}\n\ndefineAspects(AggregateOperation, [\n  Aspect.READ_OPERATION,\n  Aspect.RETRYABLE,\n  Aspect.EXPLAINABLE,\n  Aspect.CURSOR_CREATING\n]);\n"], "mappings": ";;;;;;AACA,MAAAA,WAAA,GAAAC,OAAA;AAEA,MAAAC,OAAA,GAAAD,OAAA;AAKA,MAAAE,OAAA,GAAAF,OAAA;AACA,MAAAG,eAAA,GAAAH,OAAA;AACA,MAAAI,SAAA,GAAAJ,OAAA;AACA,MAAAK,WAAA,GAAAL,OAAA;AAEA;AACA;AACaM,OAAA,CAAAC,uBAAuB,GAAG,CAAU;AACjD,MAAMC,0CAA0C,GAAG,CAAC;AAqCpD;AACA,MAAaC,kBAAmB,SAAQL,SAAA,CAAAM,gBAAgC;EAMtEC,YAAYC,EAAoB,EAAEC,QAAoB,EAAEC,OAA0B;IAChF,KAAK,CAACC,SAAS,EAAE;MAAE,GAAGD,OAAO;MAAEE,MAAM,EAAEJ,EAAE,CAACK;IAAE,CAAE,CAAC;IAE/C,IAAI,CAACH,OAAO,GAAG;MAAE,GAAGA;IAAO,CAAE;IAE7B;IACA,IAAI,CAACI,MAAM,GAAGN,EAAE,CAACO,UAAU,IAAIb,OAAA,CAAAC,uBAAuB;IAEtD,IAAI,CAACM,QAAQ,GAAGA,QAAQ;IAExB;IACA,IAAI,CAACO,aAAa,GAAG,KAAK;IAC1B,IAAI,OAAON,OAAO,EAAEO,GAAG,KAAK,QAAQ,EAAE;MACpC,IAAI,CAACR,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACS,MAAM,CAAC;QAAEC,IAAI,EAAET,OAAO,CAACO;MAAG,CAAE,CAAC;MAC3D,IAAI,CAACD,aAAa,GAAG,IAAI;IAC3B,CAAC,MAAM,IAAIP,QAAQ,CAACW,MAAM,GAAG,CAAC,EAAE;MAC9B,MAAMC,UAAU,GAAGZ,QAAQ,CAACA,QAAQ,CAACW,MAAM,GAAG,CAAC,CAAC;MAChD,IAAIC,UAAU,CAACF,IAAI,IAAIE,UAAU,CAACC,MAAM,EAAE;QACxC,IAAI,CAACN,aAAa,GAAG,IAAI;MAC3B;IACF;IAEA,IAAI,IAAI,CAACA,aAAa,EAAE;MACtB,IAAI,CAACO,iBAAiB,GAAG,IAAI;IAC/B,CAAC,MAAM;MACL,OAAO,IAAI,CAACb,OAAO,CAACc,YAAY;IAClC;IAEA,IAAI,IAAI,CAACC,OAAO,IAAI,IAAI,CAACD,YAAY,EAAE;MACrC,MAAM,IAAI3B,OAAA,CAAA6B,yBAAyB,CACjC,wEAAwE,CACzE;IACH;IAEA,IAAIhB,OAAO,EAAEiB,MAAM,IAAI,IAAI,IAAI,OAAOjB,OAAO,CAACiB,MAAM,KAAK,QAAQ,EAAE;MACjE,MAAM,IAAI9B,OAAA,CAAA6B,yBAAyB,CAAC,kCAAkC,CAAC;IACzE;EACF;EAEA,IAAaE,WAAWA,CAAA;IACtB,OAAO,WAAoB;EAC7B;EAEA,IAAaC,YAAYA,CAAA;IACvB,OAAO,CAAC,IAAI,CAACb,aAAa;EAC5B;EAEAc,aAAaA,CAACC,KAAe;IAC3B,IAAI,CAACtB,QAAQ,CAACuB,IAAI,CAACD,KAAK,CAAC;EAC3B;EAES,MAAME,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAM1B,OAAO,GAAqB,IAAI,CAACA,OAAO;IAC9C,MAAM2B,iBAAiB,GAAG,IAAAvC,OAAA,CAAAwC,cAAc,EAACJ,MAAM,CAAC;IAChD,MAAMK,OAAO,GAAa;MAAEC,SAAS,EAAE,IAAI,CAAC1B,MAAM;MAAEL,QAAQ,EAAE,IAAI,CAACA;IAAQ,CAAE;IAE7E,IAAI,IAAI,CAACO,aAAa,IAAIqB,iBAAiB,GAAGjC,0CAA0C,EAAE;MACxF,IAAI,CAACqC,WAAW,GAAG9B,SAAS;IAC9B;IAEA,IAAI,IAAI,CAACK,aAAa,IAAI,IAAI,CAACQ,YAAY,EAAE;MAC3CzB,eAAA,CAAA2C,YAAY,CAACC,KAAK,CAACJ,OAAO,EAAE,IAAI,CAACf,YAAY,CAAC;IAChD;IAEA,IAAId,OAAO,CAACkC,wBAAwB,KAAK,IAAI,EAAE;MAC7CL,OAAO,CAACK,wBAAwB,GAAGlC,OAAO,CAACkC,wBAAwB;IACrE;IAEA,IAAI,OAAOlC,OAAO,CAACmC,YAAY,KAAK,SAAS,EAAE;MAC7CN,OAAO,CAACM,YAAY,GAAGnC,OAAO,CAACmC,YAAY;IAC7C;IAEA,IAAInC,OAAO,CAACoC,IAAI,EAAE;MAChBP,OAAO,CAACO,IAAI,GAAGpC,OAAO,CAACoC,IAAI;IAC7B;IAEA,IAAIpC,OAAO,CAACqC,GAAG,EAAE;MACfR,OAAO,CAACQ,GAAG,GAAGrC,OAAO,CAACqC,GAAG;IAC3B;IAEA;IACA;IACA,IAAIrC,OAAO,CAACsC,OAAO,KAAKrC,SAAS,EAAE;MACjC4B,OAAO,CAACS,OAAO,GAAGtC,OAAO,CAACsC,OAAO;IACnC;IAEAT,OAAO,CAACZ,MAAM,GAAGjB,OAAO,CAACiB,MAAM,IAAI,EAAE;IACrC,IAAIjB,OAAO,CAACuC,SAAS,IAAI,CAAC,IAAI,CAACjC,aAAa,EAAE;MAC5CuB,OAAO,CAACZ,MAAM,CAACsB,SAAS,GAAGvC,OAAO,CAACuC,SAAS;IAC9C;IAEA,OAAO,MAAM,KAAK,CAACC,cAAc,CAC/BhB,MAAM,EACNC,OAAO,EACPI,OAAO,EACPH,cAAc,EACd,IAAI,CAACX,OAAO,GAAG9B,WAAA,CAAAwD,uBAAuB,GAAGxD,WAAA,CAAAyD,cAAc,CACxD;EACH;;AA5GFlD,OAAA,CAAAG,kBAAA,GAAAA,kBAAA;AA+GA,IAAAJ,WAAA,CAAAoD,aAAa,EAAChD,kBAAkB,EAAE,CAChCJ,WAAA,CAAAqD,MAAM,CAACC,cAAc,EACrBtD,WAAA,CAAAqD,MAAM,CAACE,SAAS,EAChBvD,WAAA,CAAAqD,MAAM,CAACG,WAAW,EAClBxD,WAAA,CAAAqD,MAAM,CAACI,eAAe,CACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}