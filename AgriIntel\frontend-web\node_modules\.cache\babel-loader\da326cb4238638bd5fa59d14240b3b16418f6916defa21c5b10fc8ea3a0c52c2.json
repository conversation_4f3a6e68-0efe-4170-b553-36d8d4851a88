{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import _objectWithoutProperties from\"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";const _excluded=[\"title\",\"subtitle\",\"backgroundImage\",\"module\",\"uniqueId\",\"icon\",\"accentColor\",\"secondaryColor\",\"actionIcon\",\"actionLabel\",\"onAction\",\"action\",\"content\",\"delay\",\"height\",\"children\",\"overlay\",\"loading\",\"error\",\"errorMessage\",\"cardVariant\",\"badges\",\"tags\",\"progress\",\"showProgressBar\",\"expandable\",\"headerAction\",\"footerContent\",\"hoverEffect\",\"secondaryAction\"];import React,{useMemo}from'react';import CustomButton from'./CustomButton';import{Box,Card,CardContent,Typography,IconButton,useTheme,alpha,Chip,Skeleton,Tooltip,Badge,CircularProgress,Divider}from'@mui/material';import{motion,AnimatePresence}from'framer-motion';import{getSelectableStyles}from'../../utils/selectionUtils';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const AnimatedBackgroundCard=_ref=>{let{title,subtitle,backgroundImage:propBackgroundImage,module,uniqueId,icon,accentColor,secondaryColor,actionIcon,actionLabel,onAction,action,content,delay=0,height=200,children,overlay='gradient',loading=false,error=false,errorMessage='An error occurred',cardVariant='default',badges,tags,progress,showProgressBar=false,expandable=false,headerAction,footerContent,hoverEffect='lift',secondaryAction}=_ref,cardProps=_objectWithoutProperties(_ref,_excluded);const theme=useTheme();// This function is no longer used as we're using the useMemo approach below\n// Keeping the function signature for reference but not using it\nconst getModuleBackgroundImage=()=>{return'';};// Check if this is an excluded module (dashboard, login, commercial)\nconst isExcludedModule=module?['dashboard','login','commercial'].includes(module.toLowerCase()):false;// Use metallic blue for all modules except excluded ones\nconst defaultPrimary=isExcludedModule?theme.palette.primary.main:'#4A6FA5';const defaultSecondary=isExcludedModule?theme.palette.primary.dark:'#3A5A8C';// Use current theme colors or fallback to provided colors if explicitly specified\nconst cardColor=accentColor||defaultPrimary;const cardSecondaryColor=secondaryColor||defaultSecondary;// Toggle expanded state is handled by the expandable prop\n// Card style variants\nconst cardVariants={default:{boxShadow:'0 4px 20px rgba(0,0,0,0.08)',border:'none',backgroundColor:alpha(cardColor,0.03)},outlined:{boxShadow:'none',border:\"1px solid \".concat(alpha(theme.palette.divider,0.8)),backgroundColor:alpha(cardColor,0.05)},elevated:{boxShadow:'0 8px 32px rgba(0,0,0,0.12)',border:'none',backgroundColor:alpha(cardColor,0.02)}};// Hover effect styles\nconst getHoverStyles=()=>{switch(hoverEffect){case'lift':return{transform:'translateY(-5px)',boxShadow:'0 8px 25px rgba(0,0,0,0.15)'};case'glow':return{boxShadow:\"0 0 20px \".concat(alpha(cardColor,0.5))};case'border':return{borderColor:cardColor};case'zoom':return{transform:'scale(1.02)'};case'none':default:return{};}};// Variables are already declared above\n// Use the provided background image or get one based on the module\nconst backgroundImage=useMemo(()=>{if(propBackgroundImage){return propBackgroundImage;}// If no background image is provided but a module is, use a default image for that module\nif(module){const moduleMap={animals:'/images/modules/animals/cattle-1.jpeg',// Cows 1.jpeg\nhealth:'/images/modules/health/health-main.png',// health Managemnt.png\nbreeding:'/images/modules/breeding/breeding-main.png',// breending and pregnancy.png\nfeeding:'/images/modules/feeding/feed-main.jpeg',// Feed managemnt.jpeg\nfeed:'/images/modules/feeding/feed-main.jpeg',// Feed managemnt.jpeg\ncommercial:'/images/modules/commercial/commercial-main.webp',// commercial products.webp\nfinancial:'/images/modules/financial/financial-main.jpg',// Financial dashboard\ncompliance:'/images/modules/commercial/commercial-2.jpeg',// commercial products 2.web.jpeg\nrfid:'/images/modules/rfid/rfid-1.webp',// RFID 1.webp\nreports:'/images/modules/reports/reports-main.jpg',// Reports dashboard\nsettings:'/images/modules/animals/cattle-1.jpeg',// Use cattle image as fallback\nlogin:'/images/login/login-bg.jpeg',// log in page.jpeg\ndashboard:'/images/modules/animals/cattle-1.jpeg',// Use cattle image as fallback\nresources:'/images/modules/health/veterinary-1.jpg'// Use veterinary image as fallback\n};return moduleMap[module.toLowerCase()]||'/images/modules/animals/cattle-1.jpeg';}return propBackgroundImage||'';},[propBackgroundImage,module]);const getOverlayStyles=()=>{switch(overlay){case'light':return{background:alpha('#ffffff',0.7),backdropFilter:'blur(5px)'};case'dark':return{background:alpha('#000000',0.6),backdropFilter:'blur(5px)'};case'gradient':return{background:\"linear-gradient(135deg, \".concat(alpha(cardColor,0.7),\", \").concat(alpha(cardSecondaryColor,0.6),\")\"),backdropFilter:'blur(5px)',animation:'gradientAnimation 15s ease infinite',backgroundSize:'200% 200%'};case'none':default:return{};}};// Handle action click with stopPropagation\nconst handleActionClick=(e,callback)=>{e.stopPropagation();callback&&callback();};// Handle secondary action click\nconst handleSecondaryActionClick=e=>{e.stopPropagation();(secondaryAction===null||secondaryAction===void 0?void 0:secondaryAction.onClick)&&secondaryAction.onClick(e);};// Render loading state\nif(loading){return/*#__PURE__*/_jsx(Box,{sx:{height:'100%'},children:/*#__PURE__*/_jsx(Card,_objectSpread(_objectSpread({},cardProps),{},{sx:_objectSpread(_objectSpread({borderRadius:'12px',overflow:'hidden',height:'100%',position:'relative'},cardVariants[cardVariant]),cardProps.sx),children:/*#__PURE__*/_jsxs(CardContent,{sx:{p:3},children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"space-between\",alignItems:\"flex-start\",mb:2,children:[/*#__PURE__*/_jsxs(Box,{width:\"70%\",children:[/*#__PURE__*/_jsx(Skeleton,{variant:\"text\",width:\"80%\",height:32}),/*#__PURE__*/_jsx(Skeleton,{variant:\"text\",width:\"60%\",height:20})]}),/*#__PURE__*/_jsx(Skeleton,{variant:\"rounded\",width:48,height:48})]}),/*#__PURE__*/_jsx(Box,{sx:{mt:2},children:/*#__PURE__*/_jsx(Skeleton,{variant:\"rounded\",width:\"100%\",height:100})}),/*#__PURE__*/_jsx(Box,{sx:{mt:2,display:'flex',justifyContent:'flex-end'},children:/*#__PURE__*/_jsx(Skeleton,{variant:\"rounded\",width:100,height:36})})]})}))});}// Render error state\nif(error){return/*#__PURE__*/_jsx(Box,{sx:{height:'100%'},children:/*#__PURE__*/_jsx(Card,_objectSpread(_objectSpread({},cardProps),{},{sx:_objectSpread({borderRadius:'12px',overflow:'hidden',height:'100%',position:'relative',border:\"1px solid \".concat(theme.palette.error.main)},cardProps.sx),children:/*#__PURE__*/_jsxs(CardContent,{sx:{position:'relative',zIndex:2,height:'100%',display:'flex',flexDirection:'column',p:3,color:theme.palette.error.main},children:[/*#__PURE__*/_jsx(Box,{display:\"flex\",justifyContent:\"space-between\",alignItems:\"flex-start\",mb:2,children:/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:\"bold\",gutterBottom:true,sx:{fontSize:{xs:'1.1rem',sm:'1.2rem'},letterSpacing:'-0.3px'},children:title})}),/*#__PURE__*/_jsxs(Box,{sx:{flex:1,display:'flex',alignItems:'center',justifyContent:'center',flexDirection:'column'},children:[/*#__PURE__*/_jsx(Typography,{variant:\"body2\",color:\"error\",align:\"center\",children:errorMessage}),onAction&&/*#__PURE__*/_jsx(CustomButton,{variant:\"outlined\",color:\"error\",size:\"small\",onClick:onAction,sx:{mt:2},children:\"Retry\"})]})]})}))});}// Render normal state\nreturn/*#__PURE__*/_jsx(Box,{sx:_objectSpread({height:'100%'},getSelectableStyles()||{}),children:/*#__PURE__*/_jsxs(Card,_objectSpread(_objectSpread({},cardProps),{},{sx:_objectSpread(_objectSpread(_objectSpread({borderRadius:'12px',overflow:'hidden',height:'100%',position:'relative',transition:'all 0.3s ease',cursor:onAction?'pointer':'default','&:hover':hoverEffect!=='none'?getHoverStyles():{},'&::before':{content:'\"\"',position:'absolute',top:0,left:0,width:'100%',height:'4px',background:\"linear-gradient(to right, \".concat(cardColor,\", \").concat(cardSecondaryColor,\")\"),zIndex:3,opacity:0.9}},cardVariants[cardVariant]),cardProps.sx),getSelectableStyles()),onClick:onAction,children:[showProgressBar&&progress!==undefined&&/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,left:0,width:\"\".concat(progress,\"%\"),height:'4px',background:theme.palette.success.main,zIndex:4,transition:'width 0.5s ease-in-out'}}),/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,left:0,right:0,bottom:0,backgroundColor:alpha(cardColor,0.05),zIndex:0,'&::after':{content:'\"\"',position:'absolute',top:0,left:0,right:0,bottom:0,background:\"linear-gradient(135deg, \".concat(alpha(cardColor,0.15),\", \").concat(alpha(cardSecondaryColor,0.1),\")\"),zIndex:1}}}),backgroundImage&&/*#__PURE__*/_jsx(Box,{sx:{position:'absolute',top:0,left:0,right:0,bottom:0,backgroundImage:\"url(\".concat(backgroundImage,\")\"),backgroundSize:'cover',backgroundPosition:'center',opacity:0.05,zIndex:0}}),/*#__PURE__*/_jsxs(CardContent,{sx:_objectSpread({position:'relative',zIndex:2,height:'100%',display:'flex',flexDirection:'column',p:3,color:overlay==='none'?theme.palette.text.primary:theme.palette.getContrastText(cardColor)},getSelectableStyles()),children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",justifyContent:\"space-between\",alignItems:\"flex-start\",mb:2,children:[/*#__PURE__*/_jsxs(Box,{sx:{flex:1},children:[/*#__PURE__*/_jsxs(Box,{display:\"flex\",alignItems:\"center\",flexWrap:\"wrap\",children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",fontWeight:\"bold\",sx:{fontSize:{xs:'1.1rem',sm:'1.2rem'},letterSpacing:'-0.3px',color:overlay==='none'?cardColor:'inherit',mr:1},children:title}),badges&&badges.length>0&&/*#__PURE__*/_jsx(Box,{display:\"flex\",alignItems:\"center\",flexWrap:\"wrap\",gap:0.5,children:badges.map((badge,index)=>/*#__PURE__*/_jsx(Tooltip,{title:badge.tooltip||'',arrow:true,placement:\"top\",children:/*#__PURE__*/_jsx(Badge,{badgeContent:badge.content,color:badge.color||'primary',sx:{'& .MuiBadge-badge':{fontSize:'0.7rem',height:'18px',minWidth:'18px'}}})},index))}),headerAction&&/*#__PURE__*/_jsx(Box,{ml:\"auto\",children:headerAction})]}),subtitle&&/*#__PURE__*/_jsx(Typography,{variant:\"body2\",sx:{opacity:0.9,fontSize:{xs:'0.85rem',sm:'0.9rem'},color:overlay==='none'?theme.palette.text.secondary:'inherit',mt:0.5},children:subtitle}),tags&&tags.length>0&&/*#__PURE__*/_jsx(Box,{sx:{display:'flex',flexWrap:'wrap',gap:0.5,mt:1},children:tags.map((tag,index)=>/*#__PURE__*/_jsx(Chip,{label:tag.label,size:\"small\",color:tag.color||'primary',variant:\"outlined\",sx:{height:'20px',fontSize:'0.7rem',backgroundColor:alpha(theme.palette[tag.color||'primary'].main,0.1),color:overlay==='none'?theme.palette[tag.color||'primary'].main:'white',borderColor:alpha(theme.palette[tag.color||'primary'].main,0.3)}},index))})]}),icon&&/*#__PURE__*/_jsx(Box,{sx:{p:1.5,borderRadius:'12px',backgroundColor:overlay==='none'?alpha(cardColor,0.1):'rgba(255, 255, 255, 0.2)',color:overlay==='none'?cardColor:'white',display:'flex',alignItems:'center',justifyContent:'center',boxShadow:'0 2px 8px rgba(0,0,0,0.05)',ml:2},children:icon})]}),progress!==undefined&&!showProgressBar&&/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',alignItems:'center',mb:2},children:[/*#__PURE__*/_jsx(CircularProgress,{variant:\"determinate\",value:progress,size:24,thickness:5,sx:{color:theme.palette.success.main}}),/*#__PURE__*/_jsxs(Typography,{variant:\"body2\",sx:{ml:1,opacity:0.9},children:[progress,\"% Complete\"]})]}),/*#__PURE__*/_jsx(AnimatePresence,{children:/*#__PURE__*/_jsx(Box,{component:motion.div,initial:{opacity:0,height:0},animate:{opacity:1,height:'auto'},exit:{opacity:0,height:0},transition:{duration:0.3},sx:{flex:1},children:children&&/*#__PURE__*/_jsx(Box,{sx:{flex:1},children:children})})}),footerContent&&/*#__PURE__*/_jsxs(Box,{mt:2,children:[/*#__PURE__*/_jsx(Divider,{sx:{my:1,opacity:0.2}}),footerContent]}),(actionIcon||actionLabel||secondaryAction)&&/*#__PURE__*/_jsxs(Box,{mt:\"auto\",pt:2,display:\"flex\",justifyContent:\"flex-end\",alignItems:\"center\",children:[secondaryAction&&/*#__PURE__*/_jsx(CustomButton,{variant:\"text\",size:\"small\",startIcon:secondaryAction.icon,onClick:handleSecondaryActionClick,sx:{mr:'auto',color:overlay==='none'?theme.palette.text.secondary:'rgba(255, 255, 255, 0.7)','&:hover':{backgroundColor:overlay==='none'?'rgba(0, 0, 0, 0.05)':'rgba(255, 255, 255, 0.1)'}},children:secondaryAction.label}),actionIcon&&/*#__PURE__*/_jsx(IconButton,{onClick:e=>handleActionClick(e,onAction),sx:{color:overlay==='none'?cardColor:'white',backgroundColor:overlay==='none'?alpha(cardColor,0.1):'rgba(255, 255, 255, 0.1)','&:hover':{backgroundColor:overlay==='none'?alpha(cardColor,0.2):'rgba(255, 255, 255, 0.2)'}},children:actionIcon}),actionLabel&&/*#__PURE__*/_jsx(CustomButton,{variant:\"outlined\",size:\"small\",onClick:e=>handleActionClick(e,onAction),sx:{ml:actionIcon?1:0,color:overlay==='none'?cardColor:'white',borderColor:overlay==='none'?alpha(cardColor,0.5):'rgba(255, 255, 255, 0.5)','&:hover':{borderColor:overlay==='none'?cardColor:'white',backgroundColor:overlay==='none'?alpha(cardColor,0.1):'rgba(255, 255, 255, 0.1)'}},children:actionLabel})]})]})]}))});};export default AnimatedBackgroundCard;", "map": {"version": 3, "names": ["React", "useMemo", "CustomButton", "Box", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Typography", "IconButton", "useTheme", "alpha", "Chip", "Skeleton", "<PERSON><PERSON><PERSON>", "Badge", "CircularProgress", "Divider", "motion", "AnimatePresence", "getSelectableStyles", "jsx", "_jsx", "jsxs", "_jsxs", "AnimatedBackgroundCard", "_ref", "title", "subtitle", "backgroundImage", "propBackgroundImage", "module", "uniqueId", "icon", "accentColor", "secondaryColor", "actionIcon", "actionLabel", "onAction", "action", "content", "delay", "height", "children", "overlay", "loading", "error", "errorMessage", "cardVariant", "badges", "tags", "progress", "showProgressBar", "expandable", "headerAction", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "hoverEffect", "secondaryAction", "cardProps", "_objectWithoutProperties", "_excluded", "theme", "getModuleBackgroundImage", "isExcludedModule", "includes", "toLowerCase", "defaultPrimary", "palette", "primary", "main", "defaultSecondary", "dark", "cardColor", "cardSecondaryColor", "cardVariants", "default", "boxShadow", "border", "backgroundColor", "outlined", "concat", "divider", "elevated", "getHoverStyles", "transform", "borderColor", "moduleMap", "animals", "health", "breeding", "feeding", "feed", "commercial", "financial", "compliance", "rfid", "reports", "settings", "login", "dashboard", "resources", "getOverlayStyles", "background", "<PERSON><PERSON>ilter", "animation", "backgroundSize", "handleActionClick", "e", "callback", "stopPropagation", "handleSecondaryActionClick", "onClick", "sx", "_objectSpread", "borderRadius", "overflow", "position", "p", "display", "justifyContent", "alignItems", "mb", "width", "variant", "mt", "zIndex", "flexDirection", "color", "fontWeight", "gutterBottom", "fontSize", "xs", "sm", "letterSpacing", "flex", "align", "size", "transition", "cursor", "top", "left", "opacity", "undefined", "success", "right", "bottom", "backgroundPosition", "text", "getContrastText", "flexWrap", "mr", "length", "gap", "map", "badge", "index", "tooltip", "arrow", "placement", "badgeContent", "min<PERSON><PERSON><PERSON>", "ml", "secondary", "tag", "label", "value", "thickness", "component", "div", "initial", "animate", "exit", "duration", "my", "pt", "startIcon"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/components/common/AnimatedBackgroundCard.tsx"], "sourcesContent": ["import React, { useMemo } from 'react';\nimport CustomButton from './CustomButton';\nimport { Box, Card, CardContent, Typography, IconButton, useTheme, alpha, CardProps, Chip, Skeleton, Tooltip, Badge, CircularProgress, Divider } from '@mui/material';\nimport LazyImage from './LazyImage';\nimport { motion, AnimatePresence } from 'framer-motion';\nimport { getSelectableStyles } from '../../utils/selectionUtils';\n\nexport interface AnimatedBackgroundCardProps extends Omit<CardProps, 'children' | 'content'> {\n  title: string;\n  subtitle?: string;\n  backgroundImage?: string;\n  module?: string;\n  uniqueId?: string;\n  icon?: React.ReactNode;\n  accentColor?: string;\n  secondaryColor?: string;\n  actionIcon?: React.ReactNode;\n  actionLabel?: string;\n  onAction?: () => void;\n  action?: React.ReactNode;\n  content?: React.ReactNode;\n  delay?: number;\n  height?: string | number;\n  children?: React.ReactNode;\n  overlay?: 'light' | 'dark' | 'gradient' | 'none';\n  loading?: boolean;\n  error?: boolean;\n  errorMessage?: string;\n  cardVariant?: 'default' | 'outlined' | 'elevated';\n  badges?: Array<{\n    content: React.ReactNode | string | number;\n    color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';\n    tooltip?: string;\n  }>;\n  tags?: Array<{\n    label: string;\n    color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';\n  }>;\n  progress?: number;\n  showProgressBar?: boolean;\n  expandable?: boolean;\n  headerAction?: React.ReactNode;\n  footerContent?: React.ReactNode;\n  hoverEffect?: 'lift' | 'glow' | 'border' | 'zoom' | 'none';\n  secondaryAction?: {\n    label: string;\n    onClick: (e: React.MouseEvent) => void;\n    icon?: React.ReactNode;\n  };\n}\n\nconst AnimatedBackgroundCard: React.FC<AnimatedBackgroundCardProps> = ({\n  title,\n  subtitle,\n  backgroundImage: propBackgroundImage,\n  module,\n  uniqueId,\n  icon,\n  accentColor,\n  secondaryColor,\n  actionIcon,\n  actionLabel,\n  onAction,\n  action,\n  content,\n  delay = 0,\n  height = 200,\n  children,\n  overlay = 'gradient',\n  loading = false,\n  error = false,\n  errorMessage = 'An error occurred',\n  cardVariant = 'default',\n  badges,\n  tags,\n  progress,\n  showProgressBar = false,\n  expandable = false,\n  headerAction,\n  footerContent,\n  hoverEffect = 'lift',\n  secondaryAction,\n  ...cardProps\n}) => {\n  const theme = useTheme();\n\n  // This function is no longer used as we're using the useMemo approach below\n  // Keeping the function signature for reference but not using it\n  const getModuleBackgroundImage = () => {\n    return '';\n  };\n\n  // Check if this is an excluded module (dashboard, login, commercial)\n  const isExcludedModule = module ? ['dashboard', 'login', 'commercial'].includes(module.toLowerCase()) : false;\n\n  // Use metallic blue for all modules except excluded ones\n  const defaultPrimary = isExcludedModule ? theme.palette.primary.main : '#4A6FA5';\n  const defaultSecondary = isExcludedModule ? theme.palette.primary.dark : '#3A5A8C';\n\n  // Use current theme colors or fallback to provided colors if explicitly specified\n  const cardColor = accentColor || defaultPrimary;\n  const cardSecondaryColor = secondaryColor || defaultSecondary;\n\n  // Toggle expanded state is handled by the expandable prop\n\n  // Card style variants\n  const cardVariants = {\n    default: {\n      boxShadow: '0 4px 20px rgba(0,0,0,0.08)',\n      border: 'none',\n      backgroundColor: alpha(cardColor, 0.03),\n    },\n    outlined: {\n      boxShadow: 'none',\n      border: `1px solid ${alpha(theme.palette.divider, 0.8)}`,\n      backgroundColor: alpha(cardColor, 0.05),\n    },\n    elevated: {\n      boxShadow: '0 8px 32px rgba(0,0,0,0.12)',\n      border: 'none',\n      backgroundColor: alpha(cardColor, 0.02),\n    },\n  };\n\n  // Hover effect styles\n  const getHoverStyles = () => {\n    switch (hoverEffect) {\n      case 'lift':\n        return {\n          transform: 'translateY(-5px)',\n          boxShadow: '0 8px 25px rgba(0,0,0,0.15)'\n        };\n      case 'glow':\n        return {\n          boxShadow: `0 0 20px ${alpha(cardColor, 0.5)}`\n        };\n      case 'border':\n        return {\n          borderColor: cardColor\n        };\n      case 'zoom':\n        return {\n          transform: 'scale(1.02)'\n        };\n      case 'none':\n      default:\n        return {};\n    }\n  };\n\n  // Variables are already declared above\n\n  // Use the provided background image or get one based on the module\n  const backgroundImage = useMemo(() => {\n    if (propBackgroundImage) {\n      return propBackgroundImage;\n    }\n\n    // If no background image is provided but a module is, use a default image for that module\n    if (module) {\n      const moduleMap: Record<string, string> = {\n        animals: '/images/modules/animals/cattle-1.jpeg', // Cows 1.jpeg\n        health: '/images/modules/health/health-main.png', // health Managemnt.png\n        breeding: '/images/modules/breeding/breeding-main.png', // breending and pregnancy.png\n        feeding: '/images/modules/feeding/feed-main.jpeg', // Feed managemnt.jpeg\n        feed: '/images/modules/feeding/feed-main.jpeg', // Feed managemnt.jpeg\n        commercial: '/images/modules/commercial/commercial-main.webp', // commercial products.webp\n        financial: '/images/modules/financial/financial-main.jpg', // Financial dashboard\n        compliance: '/images/modules/commercial/commercial-2.jpeg', // commercial products 2.web.jpeg\n        rfid: '/images/modules/rfid/rfid-1.webp', // RFID 1.webp\n        reports: '/images/modules/reports/reports-main.jpg', // Reports dashboard\n        settings: '/images/modules/animals/cattle-1.jpeg', // Use cattle image as fallback\n        login: '/images/login/login-bg.jpeg', // log in page.jpeg\n        dashboard: '/images/modules/animals/cattle-1.jpeg', // Use cattle image as fallback\n        resources: '/images/modules/health/veterinary-1.jpg', // Use veterinary image as fallback\n      };\n\n      return moduleMap[module.toLowerCase()] || '/images/modules/animals/cattle-1.jpeg';\n    }\n\n    return propBackgroundImage || '';\n  }, [propBackgroundImage, module]);\n\n  const getOverlayStyles = () => {\n    switch (overlay) {\n      case 'light':\n        return {\n          background: alpha('#ffffff', 0.7),\n          backdropFilter: 'blur(5px)',\n        };\n      case 'dark':\n        return {\n          background: alpha('#000000', 0.6),\n          backdropFilter: 'blur(5px)',\n        };\n      case 'gradient':\n        return {\n          background: `linear-gradient(135deg, ${alpha(cardColor, 0.7)}, ${alpha(cardSecondaryColor, 0.6)})`,\n          backdropFilter: 'blur(5px)',\n          animation: 'gradientAnimation 15s ease infinite',\n          backgroundSize: '200% 200%',\n        };\n      case 'none':\n      default:\n        return {};\n    }\n  };\n\n  // Handle action click with stopPropagation\n  const handleActionClick = (e: React.MouseEvent, callback?: () => void) => {\n    e.stopPropagation();\n    callback && callback();\n  };\n\n  // Handle secondary action click\n  const handleSecondaryActionClick = (e: React.MouseEvent) => {\n    e.stopPropagation();\n    secondaryAction?.onClick && secondaryAction.onClick(e);\n  };\n\n  // Render loading state\n  if (loading) {\n    return (\n      <Box\n        sx={{ height: '100%' }}\n      >\n        <Card\n          {...cardProps}\n          sx={{\n            borderRadius: '12px',\n            overflow: 'hidden',\n            height: '100%',\n            position: 'relative',\n            ...cardVariants[cardVariant],\n            ...cardProps.sx\n          }}\n        >\n          <CardContent sx={{ p: 3 }}>\n            <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"flex-start\" mb={2}>\n              <Box width=\"70%\">\n                <Skeleton variant=\"text\" width=\"80%\" height={32} />\n                <Skeleton variant=\"text\" width=\"60%\" height={20} />\n              </Box>\n              <Skeleton variant=\"rounded\" width={48} height={48} />\n            </Box>\n            <Box sx={{ mt: 2 }}>\n              <Skeleton variant=\"rounded\" width=\"100%\" height={100} />\n            </Box>\n            <Box sx={{ mt: 2, display: 'flex', justifyContent: 'flex-end' }}>\n              <Skeleton variant=\"rounded\" width={100} height={36} />\n            </Box>\n          </CardContent>\n        </Card>\n      </Box>\n    );\n  }\n\n  // Render error state\n  if (error) {\n    return (\n      <Box\n        sx={{ height: '100%' }}\n      >\n        <Card\n          {...cardProps}\n          sx={{\n            borderRadius: '12px',\n            overflow: 'hidden',\n            height: '100%',\n            position: 'relative',\n            border: `1px solid ${theme.palette.error.main}`,\n            ...cardProps.sx\n          }}\n        >\n          <CardContent\n            sx={{\n              position: 'relative',\n              zIndex: 2,\n              height: '100%',\n              display: 'flex',\n              flexDirection: 'column',\n              p: 3,\n              color: theme.palette.error.main\n            }}\n          >\n            <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"flex-start\" mb={2}>\n              <Typography\n                variant=\"h6\"\n                fontWeight=\"bold\"\n                gutterBottom\n                sx={{\n                  fontSize: { xs: '1.1rem', sm: '1.2rem' },\n                  letterSpacing: '-0.3px',\n                }}\n              >\n                {title}\n              </Typography>\n            </Box>\n            <Box sx={{ flex: 1, display: 'flex', alignItems: 'center', justifyContent: 'center', flexDirection: 'column' }}>\n              <Typography variant=\"body2\" color=\"error\" align=\"center\">\n                {errorMessage}\n              </Typography>\n              {onAction && (\n                <CustomButton\n                  variant=\"outlined\"\n                  color=\"error\"\n                  size=\"small\"\n                  onClick={onAction}\n                  sx={{ mt: 2 }}\n                >\n                  Retry\n                </CustomButton>\n              )}\n            </Box>\n          </CardContent>\n        </Card>\n      </Box>\n    );\n  }\n\n  // Render normal state\n  return (\n    <Box\n      sx={{ height: '100%', ...(getSelectableStyles() || {}) }}\n    >\n      <Card\n        {...cardProps}\n        sx={{\n          borderRadius: '12px',\n          overflow: 'hidden',\n          height: '100%',\n          position: 'relative',\n          transition: 'all 0.3s ease',\n          cursor: onAction ? 'pointer' : 'default',\n          '&:hover': hoverEffect !== 'none' ? getHoverStyles() : {},\n          '&::before': {\n            content: '\"\"',\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            width: '100%',\n            height: '4px',\n            background: `linear-gradient(to right, ${cardColor}, ${cardSecondaryColor})`,\n            zIndex: 3,\n            opacity: 0.9\n          },\n          ...cardVariants[cardVariant],\n          ...cardProps.sx,\n          ...getSelectableStyles()\n        }}\n        onClick={onAction}\n      >\n        {/* Progress Bar */}\n        {showProgressBar && progress !== undefined && (\n          <Box\n            sx={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              width: `${progress}%`,\n              height: '4px',\n              background: theme.palette.success.main,\n              zIndex: 4,\n              transition: 'width 0.5s ease-in-out'\n            }}\n          />\n        )}\n\n        {/* Solid Color Background with Overlay */}\n        <Box\n          sx={{\n            position: 'absolute',\n            top: 0,\n            left: 0,\n            right: 0,\n            bottom: 0,\n            backgroundColor: alpha(cardColor, 0.05),\n            zIndex: 0,\n            '&::after': {\n              content: '\"\"',\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              background: `linear-gradient(135deg, ${alpha(cardColor, 0.15)}, ${alpha(cardSecondaryColor, 0.1)})`,\n              zIndex: 1\n            }\n          }}\n        />\n\n        {/* Background Image (low opacity) */}\n        {backgroundImage && (\n          <Box\n            sx={{\n              position: 'absolute',\n              top: 0,\n              left: 0,\n              right: 0,\n              bottom: 0,\n              backgroundImage: `url(${backgroundImage})`,\n              backgroundSize: 'cover',\n              backgroundPosition: 'center',\n              opacity: 0.05,\n              zIndex: 0\n            }}\n          />\n        )}\n\n        {/* Card Content */}\n        <CardContent\n          sx={{\n            position: 'relative',\n            zIndex: 2,\n            height: '100%',\n            display: 'flex',\n            flexDirection: 'column',\n            p: 3,\n            color: overlay === 'none' ? theme.palette.text.primary : theme.palette.getContrastText(cardColor),\n            ...getSelectableStyles()\n          }}\n        >\n          {/* Header with Title, Subtitle, Icon, and Badges */}\n          <Box display=\"flex\" justifyContent=\"space-between\" alignItems=\"flex-start\" mb={2}>\n            <Box sx={{ flex: 1 }}>\n              <Box display=\"flex\" alignItems=\"center\" flexWrap=\"wrap\">\n                <Typography\n                  variant=\"h6\"\n                  fontWeight=\"bold\"\n                  sx={{\n                    fontSize: { xs: '1.1rem', sm: '1.2rem' },\n                    letterSpacing: '-0.3px',\n                    color: overlay === 'none' ? cardColor : 'inherit',\n                    mr: 1\n                  }}\n                >\n                  {title}\n                </Typography>\n\n                {/* Badges */}\n                {badges && badges.length > 0 && (\n                  <Box display=\"flex\" alignItems=\"center\" flexWrap=\"wrap\" gap={0.5}>\n                    {badges.map((badge, index) => (\n                      <Tooltip key={index} title={badge.tooltip || ''} arrow placement=\"top\">\n                        <Badge\n                          badgeContent={badge.content}\n                          color={badge.color || 'primary'}\n                          sx={{\n                            '& .MuiBadge-badge': {\n                              fontSize: '0.7rem',\n                              height: '18px',\n                              minWidth: '18px'\n                            }\n                          }}\n                        />\n                      </Tooltip>\n                    ))}\n                  </Box>\n                )}\n\n                {/* Header Action */}\n                {headerAction && (\n                  <Box ml=\"auto\">\n                    {headerAction}\n                  </Box>\n                )}\n              </Box>\n\n              {subtitle && (\n                <Typography\n                  variant=\"body2\"\n                  sx={{\n                    opacity: 0.9,\n                    fontSize: { xs: '0.85rem', sm: '0.9rem' },\n                    color: overlay === 'none' ? theme.palette.text.secondary : 'inherit',\n                    mt: 0.5\n                  }}\n                >\n                  {subtitle}\n                </Typography>\n              )}\n\n              {/* Tags */}\n              {tags && tags.length > 0 && (\n                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 1 }}>\n                  {tags.map((tag, index) => (\n                    <Chip\n                      key={index}\n                      label={tag.label}\n                      size=\"small\"\n                      color={tag.color || 'primary'}\n                      variant=\"outlined\"\n                      sx={{\n                        height: '20px',\n                        fontSize: '0.7rem',\n                        backgroundColor: alpha(theme.palette[tag.color || 'primary'].main, 0.1),\n                        color: overlay === 'none' ? theme.palette[tag.color || 'primary'].main : 'white',\n                        borderColor: alpha(theme.palette[tag.color || 'primary'].main, 0.3),\n                      }}\n                    />\n                  ))}\n                </Box>\n              )}\n            </Box>\n\n            {/* Icon */}\n            {icon && (\n              <Box\n                sx={{\n                  p: 1.5,\n                  borderRadius: '12px',\n                  backgroundColor: overlay === 'none'\n                    ? alpha(cardColor, 0.1)\n                    : 'rgba(255, 255, 255, 0.2)',\n                  color: overlay === 'none' ? cardColor : 'white',\n                  display: 'flex',\n                  alignItems: 'center',\n                  justifyContent: 'center',\n                  boxShadow: '0 2px 8px rgba(0,0,0,0.05)',\n                  ml: 2\n                }}\n              >\n                {icon}\n              </Box>\n            )}\n          </Box>\n\n          {/* Progress Indicator */}\n          {progress !== undefined && !showProgressBar && (\n            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>\n              <CircularProgress\n                variant=\"determinate\"\n                value={progress}\n                size={24}\n                thickness={5}\n                sx={{ color: theme.palette.success.main }}\n              />\n              <Typography variant=\"body2\" sx={{ ml: 1, opacity: 0.9 }}>\n                {progress}% Complete\n              </Typography>\n            </Box>\n          )}\n\n          {/* Main Content */}\n          <AnimatePresence>\n            {(\n              <Box\n                component={motion.div}\n                initial={{ opacity: 0, height: 0 }}\n                animate={{ opacity: 1, height: 'auto' }}\n                exit={{ opacity: 0, height: 0 }}\n                transition={{ duration: 0.3 }}\n                sx={{ flex: 1 }}\n              >\n                {children && (\n                  <Box sx={{ flex: 1 }}>\n                    {children}\n                  </Box>\n                )}\n              </Box>\n            )}\n          </AnimatePresence>\n\n          {/* Footer Content */}\n          {footerContent && (\n            <Box mt={2}>\n              <Divider sx={{ my: 1, opacity: 0.2 }} />\n              {footerContent}\n            </Box>\n          )}\n\n          {/* Action Buttons */}\n          {(actionIcon || actionLabel || secondaryAction) && (\n            <Box mt=\"auto\" pt={2} display=\"flex\" justifyContent=\"flex-end\" alignItems=\"center\">\n              {/* Secondary Action Button */}\n              {secondaryAction && (\n                <CustomButton\n                  variant=\"text\"\n                  size=\"small\"\n                  startIcon={secondaryAction.icon}\n                  onClick={handleSecondaryActionClick}\n                  sx={{\n                    mr: 'auto',\n                    color: overlay === 'none' ? theme.palette.text.secondary : 'rgba(255, 255, 255, 0.7)',\n                    '&:hover': {\n                      backgroundColor: overlay === 'none' ? 'rgba(0, 0, 0, 0.05)' : 'rgba(255, 255, 255, 0.1)'\n                    }\n                  }}\n                >\n                  {secondaryAction.label}\n                </CustomButton>\n              )}\n\n              {/* Icon Button */}\n              {actionIcon && (\n                <IconButton\n                  onClick={(e) => handleActionClick(e, onAction)}\n                  sx={{\n                    color: overlay === 'none' ? cardColor : 'white',\n                    backgroundColor: overlay === 'none' ? alpha(cardColor, 0.1) : 'rgba(255, 255, 255, 0.1)',\n                    '&:hover': {\n                      backgroundColor: overlay === 'none' ? alpha(cardColor, 0.2) : 'rgba(255, 255, 255, 0.2)'\n                    }\n                  }}\n                >\n                  {actionIcon}\n                </IconButton>\n              )}\n\n              {/* Primary Action Button */}\n              {actionLabel && (\n                <CustomButton\n                  variant=\"outlined\"\n                  size=\"small\"\n                  onClick={(e) => handleActionClick(e, onAction)}\n                  sx={{\n                    ml: actionIcon ? 1 : 0,\n                    color: overlay === 'none' ? cardColor : 'white',\n                    borderColor: overlay === 'none' ? alpha(cardColor, 0.5) : 'rgba(255, 255, 255, 0.5)',\n                    '&:hover': {\n                      borderColor: overlay === 'none' ? cardColor : 'white',\n                      backgroundColor: overlay === 'none' ? alpha(cardColor, 0.1) : 'rgba(255, 255, 255, 0.1)'\n                    }\n                  }}\n                >\n                  {actionLabel}\n                </CustomButton>\n              )}\n            </Box>\n          )}\n        </CardContent>\n      </Card>\n    </Box>\n  );\n};\n\nexport default AnimatedBackgroundCard;\n"], "mappings": "6qBAAA,MAAO,CAAAA,KAAK,EAAIC,OAAO,KAAQ,OAAO,CACtC,MAAO,CAAAC,YAAY,KAAM,gBAAgB,CACzC,OAASC,GAAG,CAAEC,IAAI,CAAEC,WAAW,CAAEC,UAAU,CAAEC,UAAU,CAAEC,QAAQ,CAAEC,KAAK,CAAaC,IAAI,CAAEC,QAAQ,CAAEC,OAAO,CAAEC,KAAK,CAAEC,gBAAgB,CAAEC,OAAO,KAAQ,eAAe,CAErK,OAASC,MAAM,CAAEC,eAAe,KAAQ,eAAe,CACvD,OAASC,mBAAmB,KAAQ,4BAA4B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBA8CjE,KAAM,CAAAC,sBAA6D,CAAGC,IAAA,EAgChE,IAhCiE,CACrEC,KAAK,CACLC,QAAQ,CACRC,eAAe,CAAEC,mBAAmB,CACpCC,MAAM,CACNC,QAAQ,CACRC,IAAI,CACJC,WAAW,CACXC,cAAc,CACdC,UAAU,CACVC,WAAW,CACXC,QAAQ,CACRC,MAAM,CACNC,OAAO,CACPC,KAAK,CAAG,CAAC,CACTC,MAAM,CAAG,GAAG,CACZC,QAAQ,CACRC,OAAO,CAAG,UAAU,CACpBC,OAAO,CAAG,KAAK,CACfC,KAAK,CAAG,KAAK,CACbC,YAAY,CAAG,mBAAmB,CAClCC,WAAW,CAAG,SAAS,CACvBC,MAAM,CACNC,IAAI,CACJC,QAAQ,CACRC,eAAe,CAAG,KAAK,CACvBC,UAAU,CAAG,KAAK,CAClBC,YAAY,CACZC,aAAa,CACbC,WAAW,CAAG,MAAM,CACpBC,eAEF,CAAC,CAAA/B,IAAA,CADIgC,SAAS,CAAAC,wBAAA,CAAAjC,IAAA,CAAAkC,SAAA,EAEZ,KAAM,CAAAC,KAAK,CAAGnD,QAAQ,CAAC,CAAC,CAExB;AACA;AACA,KAAM,CAAAoD,wBAAwB,CAAGA,CAAA,GAAM,CACrC,MAAO,EAAE,CACX,CAAC,CAED;AACA,KAAM,CAAAC,gBAAgB,CAAGhC,MAAM,CAAG,CAAC,WAAW,CAAE,OAAO,CAAE,YAAY,CAAC,CAACiC,QAAQ,CAACjC,MAAM,CAACkC,WAAW,CAAC,CAAC,CAAC,CAAG,KAAK,CAE7G;AACA,KAAM,CAAAC,cAAc,CAAGH,gBAAgB,CAAGF,KAAK,CAACM,OAAO,CAACC,OAAO,CAACC,IAAI,CAAG,SAAS,CAChF,KAAM,CAAAC,gBAAgB,CAAGP,gBAAgB,CAAGF,KAAK,CAACM,OAAO,CAACC,OAAO,CAACG,IAAI,CAAG,SAAS,CAElF;AACA,KAAM,CAAAC,SAAS,CAAGtC,WAAW,EAAIgC,cAAc,CAC/C,KAAM,CAAAO,kBAAkB,CAAGtC,cAAc,EAAImC,gBAAgB,CAE7D;AAEA;AACA,KAAM,CAAAI,YAAY,CAAG,CACnBC,OAAO,CAAE,CACPC,SAAS,CAAE,6BAA6B,CACxCC,MAAM,CAAE,MAAM,CACdC,eAAe,CAAEnE,KAAK,CAAC6D,SAAS,CAAE,IAAI,CACxC,CAAC,CACDO,QAAQ,CAAE,CACRH,SAAS,CAAE,MAAM,CACjBC,MAAM,cAAAG,MAAA,CAAerE,KAAK,CAACkD,KAAK,CAACM,OAAO,CAACc,OAAO,CAAE,GAAG,CAAC,CAAE,CACxDH,eAAe,CAAEnE,KAAK,CAAC6D,SAAS,CAAE,IAAI,CACxC,CAAC,CACDU,QAAQ,CAAE,CACRN,SAAS,CAAE,6BAA6B,CACxCC,MAAM,CAAE,MAAM,CACdC,eAAe,CAAEnE,KAAK,CAAC6D,SAAS,CAAE,IAAI,CACxC,CACF,CAAC,CAED;AACA,KAAM,CAAAW,cAAc,CAAGA,CAAA,GAAM,CAC3B,OAAQ3B,WAAW,EACjB,IAAK,MAAM,CACT,MAAO,CACL4B,SAAS,CAAE,kBAAkB,CAC7BR,SAAS,CAAE,6BACb,CAAC,CACH,IAAK,MAAM,CACT,MAAO,CACLA,SAAS,aAAAI,MAAA,CAAcrE,KAAK,CAAC6D,SAAS,CAAE,GAAG,CAAC,CAC9C,CAAC,CACH,IAAK,QAAQ,CACX,MAAO,CACLa,WAAW,CAAEb,SACf,CAAC,CACH,IAAK,MAAM,CACT,MAAO,CACLY,SAAS,CAAE,aACb,CAAC,CACH,IAAK,MAAM,CACX,QACE,MAAO,CAAC,CAAC,CACb,CACF,CAAC,CAED;AAEA;AACA,KAAM,CAAAvD,eAAe,CAAG1B,OAAO,CAAC,IAAM,CACpC,GAAI2B,mBAAmB,CAAE,CACvB,MAAO,CAAAA,mBAAmB,CAC5B,CAEA;AACA,GAAIC,MAAM,CAAE,CACV,KAAM,CAAAuD,SAAiC,CAAG,CACxCC,OAAO,CAAE,uCAAuC,CAAE;AAClDC,MAAM,CAAE,wCAAwC,CAAE;AAClDC,QAAQ,CAAE,4CAA4C,CAAE;AACxDC,OAAO,CAAE,wCAAwC,CAAE;AACnDC,IAAI,CAAE,wCAAwC,CAAE;AAChDC,UAAU,CAAE,iDAAiD,CAAE;AAC/DC,SAAS,CAAE,8CAA8C,CAAE;AAC3DC,UAAU,CAAE,8CAA8C,CAAE;AAC5DC,IAAI,CAAE,kCAAkC,CAAE;AAC1CC,OAAO,CAAE,0CAA0C,CAAE;AACrDC,QAAQ,CAAE,uCAAuC,CAAE;AACnDC,KAAK,CAAE,6BAA6B,CAAE;AACtCC,SAAS,CAAE,uCAAuC,CAAE;AACpDC,SAAS,CAAE,yCAA2C;AACxD,CAAC,CAED,MAAO,CAAAd,SAAS,CAACvD,MAAM,CAACkC,WAAW,CAAC,CAAC,CAAC,EAAI,uCAAuC,CACnF,CAEA,MAAO,CAAAnC,mBAAmB,EAAI,EAAE,CAClC,CAAC,CAAE,CAACA,mBAAmB,CAAEC,MAAM,CAAC,CAAC,CAEjC,KAAM,CAAAsE,gBAAgB,CAAGA,CAAA,GAAM,CAC7B,OAAQzD,OAAO,EACb,IAAK,OAAO,CACV,MAAO,CACL0D,UAAU,CAAE3F,KAAK,CAAC,SAAS,CAAE,GAAG,CAAC,CACjC4F,cAAc,CAAE,WAClB,CAAC,CACH,IAAK,MAAM,CACT,MAAO,CACLD,UAAU,CAAE3F,KAAK,CAAC,SAAS,CAAE,GAAG,CAAC,CACjC4F,cAAc,CAAE,WAClB,CAAC,CACH,IAAK,UAAU,CACb,MAAO,CACLD,UAAU,4BAAAtB,MAAA,CAA6BrE,KAAK,CAAC6D,SAAS,CAAE,GAAG,CAAC,OAAAQ,MAAA,CAAKrE,KAAK,CAAC8D,kBAAkB,CAAE,GAAG,CAAC,KAAG,CAClG8B,cAAc,CAAE,WAAW,CAC3BC,SAAS,CAAE,qCAAqC,CAChDC,cAAc,CAAE,WAClB,CAAC,CACH,IAAK,MAAM,CACX,QACE,MAAO,CAAC,CAAC,CACb,CACF,CAAC,CAED;AACA,KAAM,CAAAC,iBAAiB,CAAGA,CAACC,CAAmB,CAAEC,QAAqB,GAAK,CACxED,CAAC,CAACE,eAAe,CAAC,CAAC,CACnBD,QAAQ,EAAIA,QAAQ,CAAC,CAAC,CACxB,CAAC,CAED;AACA,KAAM,CAAAE,0BAA0B,CAAIH,CAAmB,EAAK,CAC1DA,CAAC,CAACE,eAAe,CAAC,CAAC,CACnB,CAAApD,eAAe,SAAfA,eAAe,iBAAfA,eAAe,CAAEsD,OAAO,GAAItD,eAAe,CAACsD,OAAO,CAACJ,CAAC,CAAC,CACxD,CAAC,CAED;AACA,GAAI9D,OAAO,CAAE,CACX,mBACEvB,IAAA,CAACjB,GAAG,EACF2G,EAAE,CAAE,CAAEtE,MAAM,CAAE,MAAO,CAAE,CAAAC,QAAA,cAEvBrB,IAAA,CAAChB,IAAI,CAAA2G,aAAA,CAAAA,aAAA,IACCvD,SAAS,MACbsD,EAAE,CAAAC,aAAA,CAAAA,aAAA,EACAC,YAAY,CAAE,MAAM,CACpBC,QAAQ,CAAE,QAAQ,CAClBzE,MAAM,CAAE,MAAM,CACd0E,QAAQ,CAAE,UAAU,EACjB1C,YAAY,CAAC1B,WAAW,CAAC,EACzBU,SAAS,CAACsD,EAAE,CACf,CAAArE,QAAA,cAEFnB,KAAA,CAACjB,WAAW,EAACyG,EAAE,CAAE,CAAEK,CAAC,CAAE,CAAE,CAAE,CAAA1E,QAAA,eACxBnB,KAAA,CAACnB,GAAG,EAACiH,OAAO,CAAC,MAAM,CAACC,cAAc,CAAC,eAAe,CAACC,UAAU,CAAC,YAAY,CAACC,EAAE,CAAE,CAAE,CAAA9E,QAAA,eAC/EnB,KAAA,CAACnB,GAAG,EAACqH,KAAK,CAAC,KAAK,CAAA/E,QAAA,eACdrB,IAAA,CAACT,QAAQ,EAAC8G,OAAO,CAAC,MAAM,CAACD,KAAK,CAAC,KAAK,CAAChF,MAAM,CAAE,EAAG,CAAE,CAAC,cACnDpB,IAAA,CAACT,QAAQ,EAAC8G,OAAO,CAAC,MAAM,CAACD,KAAK,CAAC,KAAK,CAAChF,MAAM,CAAE,EAAG,CAAE,CAAC,EAChD,CAAC,cACNpB,IAAA,CAACT,QAAQ,EAAC8G,OAAO,CAAC,SAAS,CAACD,KAAK,CAAE,EAAG,CAAChF,MAAM,CAAE,EAAG,CAAE,CAAC,EAClD,CAAC,cACNpB,IAAA,CAACjB,GAAG,EAAC2G,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAE,CAAE,CAAAjF,QAAA,cACjBrB,IAAA,CAACT,QAAQ,EAAC8G,OAAO,CAAC,SAAS,CAACD,KAAK,CAAC,MAAM,CAAChF,MAAM,CAAE,GAAI,CAAE,CAAC,CACrD,CAAC,cACNpB,IAAA,CAACjB,GAAG,EAAC2G,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAC,CAAEN,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,UAAW,CAAE,CAAA5E,QAAA,cAC9DrB,IAAA,CAACT,QAAQ,EAAC8G,OAAO,CAAC,SAAS,CAACD,KAAK,CAAE,GAAI,CAAChF,MAAM,CAAE,EAAG,CAAE,CAAC,CACnD,CAAC,EACK,CAAC,EACV,CAAC,CACJ,CAAC,CAEV,CAEA;AACA,GAAII,KAAK,CAAE,CACT,mBACExB,IAAA,CAACjB,GAAG,EACF2G,EAAE,CAAE,CAAEtE,MAAM,CAAE,MAAO,CAAE,CAAAC,QAAA,cAEvBrB,IAAA,CAAChB,IAAI,CAAA2G,aAAA,CAAAA,aAAA,IACCvD,SAAS,MACbsD,EAAE,CAAAC,aAAA,EACAC,YAAY,CAAE,MAAM,CACpBC,QAAQ,CAAE,QAAQ,CAClBzE,MAAM,CAAE,MAAM,CACd0E,QAAQ,CAAE,UAAU,CACpBvC,MAAM,cAAAG,MAAA,CAAenB,KAAK,CAACM,OAAO,CAACrB,KAAK,CAACuB,IAAI,CAAE,EAC5CX,SAAS,CAACsD,EAAE,CACf,CAAArE,QAAA,cAEFnB,KAAA,CAACjB,WAAW,EACVyG,EAAE,CAAE,CACFI,QAAQ,CAAE,UAAU,CACpBS,MAAM,CAAE,CAAC,CACTnF,MAAM,CAAE,MAAM,CACd4E,OAAO,CAAE,MAAM,CACfQ,aAAa,CAAE,QAAQ,CACvBT,CAAC,CAAE,CAAC,CACJU,KAAK,CAAElE,KAAK,CAACM,OAAO,CAACrB,KAAK,CAACuB,IAC7B,CAAE,CAAA1B,QAAA,eAEFrB,IAAA,CAACjB,GAAG,EAACiH,OAAO,CAAC,MAAM,CAACC,cAAc,CAAC,eAAe,CAACC,UAAU,CAAC,YAAY,CAACC,EAAE,CAAE,CAAE,CAAA9E,QAAA,cAC/ErB,IAAA,CAACd,UAAU,EACTmH,OAAO,CAAC,IAAI,CACZK,UAAU,CAAC,MAAM,CACjBC,YAAY,MACZjB,EAAE,CAAE,CACFkB,QAAQ,CAAE,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CAAC,CACxCC,aAAa,CAAE,QACjB,CAAE,CAAA1F,QAAA,CAEDhB,KAAK,CACI,CAAC,CACV,CAAC,cACNH,KAAA,CAACnB,GAAG,EAAC2G,EAAE,CAAE,CAAEsB,IAAI,CAAE,CAAC,CAAEhB,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAED,cAAc,CAAE,QAAQ,CAAEO,aAAa,CAAE,QAAS,CAAE,CAAAnF,QAAA,eAC7GrB,IAAA,CAACd,UAAU,EAACmH,OAAO,CAAC,OAAO,CAACI,KAAK,CAAC,OAAO,CAACQ,KAAK,CAAC,QAAQ,CAAA5F,QAAA,CACrDI,YAAY,CACH,CAAC,CACZT,QAAQ,eACPhB,IAAA,CAAClB,YAAY,EACXuH,OAAO,CAAC,UAAU,CAClBI,KAAK,CAAC,OAAO,CACbS,IAAI,CAAC,OAAO,CACZzB,OAAO,CAAEzE,QAAS,CAClB0E,EAAE,CAAE,CAAEY,EAAE,CAAE,CAAE,CAAE,CAAAjF,QAAA,CACf,OAED,CAAc,CACf,EACE,CAAC,EACK,CAAC,EACV,CAAC,CACJ,CAAC,CAEV,CAEA;AACA,mBACErB,IAAA,CAACjB,GAAG,EACF2G,EAAE,CAAAC,aAAA,EAAIvE,MAAM,CAAE,MAAM,EAAMtB,mBAAmB,CAAC,CAAC,EAAI,CAAC,CAAC,CAAI,CAAAuB,QAAA,cAEzDnB,KAAA,CAAClB,IAAI,CAAA2G,aAAA,CAAAA,aAAA,IACCvD,SAAS,MACbsD,EAAE,CAAAC,aAAA,CAAAA,aAAA,CAAAA,aAAA,EACAC,YAAY,CAAE,MAAM,CACpBC,QAAQ,CAAE,QAAQ,CAClBzE,MAAM,CAAE,MAAM,CACd0E,QAAQ,CAAE,UAAU,CACpBqB,UAAU,CAAE,eAAe,CAC3BC,MAAM,CAAEpG,QAAQ,CAAG,SAAS,CAAG,SAAS,CACxC,SAAS,CAAEkB,WAAW,GAAK,MAAM,CAAG2B,cAAc,CAAC,CAAC,CAAG,CAAC,CAAC,CACzD,WAAW,CAAE,CACX3C,OAAO,CAAE,IAAI,CACb4E,QAAQ,CAAE,UAAU,CACpBuB,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPlB,KAAK,CAAE,MAAM,CACbhF,MAAM,CAAE,KAAK,CACb4D,UAAU,8BAAAtB,MAAA,CAA+BR,SAAS,OAAAQ,MAAA,CAAKP,kBAAkB,KAAG,CAC5EoD,MAAM,CAAE,CAAC,CACTgB,OAAO,CAAE,GACX,CAAC,EACEnE,YAAY,CAAC1B,WAAW,CAAC,EACzBU,SAAS,CAACsD,EAAE,EACZ5F,mBAAmB,CAAC,CAAC,CACxB,CACF2F,OAAO,CAAEzE,QAAS,CAAAK,QAAA,EAGjBS,eAAe,EAAID,QAAQ,GAAK2F,SAAS,eACxCxH,IAAA,CAACjB,GAAG,EACF2G,EAAE,CAAE,CACFI,QAAQ,CAAE,UAAU,CACpBuB,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPlB,KAAK,IAAA1C,MAAA,CAAK7B,QAAQ,KAAG,CACrBT,MAAM,CAAE,KAAK,CACb4D,UAAU,CAAEzC,KAAK,CAACM,OAAO,CAAC4E,OAAO,CAAC1E,IAAI,CACtCwD,MAAM,CAAE,CAAC,CACTY,UAAU,CAAE,wBACd,CAAE,CACH,CACF,cAGDnH,IAAA,CAACjB,GAAG,EACF2G,EAAE,CAAE,CACFI,QAAQ,CAAE,UAAU,CACpBuB,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPI,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTnE,eAAe,CAAEnE,KAAK,CAAC6D,SAAS,CAAE,IAAI,CAAC,CACvCqD,MAAM,CAAE,CAAC,CACT,UAAU,CAAE,CACVrF,OAAO,CAAE,IAAI,CACb4E,QAAQ,CAAE,UAAU,CACpBuB,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPI,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACT3C,UAAU,4BAAAtB,MAAA,CAA6BrE,KAAK,CAAC6D,SAAS,CAAE,IAAI,CAAC,OAAAQ,MAAA,CAAKrE,KAAK,CAAC8D,kBAAkB,CAAE,GAAG,CAAC,KAAG,CACnGoD,MAAM,CAAE,CACV,CACF,CAAE,CACH,CAAC,CAGDhG,eAAe,eACdP,IAAA,CAACjB,GAAG,EACF2G,EAAE,CAAE,CACFI,QAAQ,CAAE,UAAU,CACpBuB,GAAG,CAAE,CAAC,CACNC,IAAI,CAAE,CAAC,CACPI,KAAK,CAAE,CAAC,CACRC,MAAM,CAAE,CAAC,CACTpH,eAAe,QAAAmD,MAAA,CAASnD,eAAe,KAAG,CAC1C4E,cAAc,CAAE,OAAO,CACvByC,kBAAkB,CAAE,QAAQ,CAC5BL,OAAO,CAAE,IAAI,CACbhB,MAAM,CAAE,CACV,CAAE,CACH,CACF,cAGDrG,KAAA,CAACjB,WAAW,EACVyG,EAAE,CAAAC,aAAA,EACAG,QAAQ,CAAE,UAAU,CACpBS,MAAM,CAAE,CAAC,CACTnF,MAAM,CAAE,MAAM,CACd4E,OAAO,CAAE,MAAM,CACfQ,aAAa,CAAE,QAAQ,CACvBT,CAAC,CAAE,CAAC,CACJU,KAAK,CAAEnF,OAAO,GAAK,MAAM,CAAGiB,KAAK,CAACM,OAAO,CAACgF,IAAI,CAAC/E,OAAO,CAAGP,KAAK,CAACM,OAAO,CAACiF,eAAe,CAAC5E,SAAS,CAAC,EAC9FpD,mBAAmB,CAAC,CAAC,CACxB,CAAAuB,QAAA,eAGFnB,KAAA,CAACnB,GAAG,EAACiH,OAAO,CAAC,MAAM,CAACC,cAAc,CAAC,eAAe,CAACC,UAAU,CAAC,YAAY,CAACC,EAAE,CAAE,CAAE,CAAA9E,QAAA,eAC/EnB,KAAA,CAACnB,GAAG,EAAC2G,EAAE,CAAE,CAAEsB,IAAI,CAAE,CAAE,CAAE,CAAA3F,QAAA,eACnBnB,KAAA,CAACnB,GAAG,EAACiH,OAAO,CAAC,MAAM,CAACE,UAAU,CAAC,QAAQ,CAAC6B,QAAQ,CAAC,MAAM,CAAA1G,QAAA,eACrDrB,IAAA,CAACd,UAAU,EACTmH,OAAO,CAAC,IAAI,CACZK,UAAU,CAAC,MAAM,CACjBhB,EAAE,CAAE,CACFkB,QAAQ,CAAE,CAAEC,EAAE,CAAE,QAAQ,CAAEC,EAAE,CAAE,QAAS,CAAC,CACxCC,aAAa,CAAE,QAAQ,CACvBN,KAAK,CAAEnF,OAAO,GAAK,MAAM,CAAG4B,SAAS,CAAG,SAAS,CACjD8E,EAAE,CAAE,CACN,CAAE,CAAA3G,QAAA,CAEDhB,KAAK,CACI,CAAC,CAGZsB,MAAM,EAAIA,MAAM,CAACsG,MAAM,CAAG,CAAC,eAC1BjI,IAAA,CAACjB,GAAG,EAACiH,OAAO,CAAC,MAAM,CAACE,UAAU,CAAC,QAAQ,CAAC6B,QAAQ,CAAC,MAAM,CAACG,GAAG,CAAE,GAAI,CAAA7G,QAAA,CAC9DM,MAAM,CAACwG,GAAG,CAAC,CAACC,KAAK,CAAEC,KAAK,gBACvBrI,IAAA,CAACR,OAAO,EAAaa,KAAK,CAAE+H,KAAK,CAACE,OAAO,EAAI,EAAG,CAACC,KAAK,MAACC,SAAS,CAAC,KAAK,CAAAnH,QAAA,cACpErB,IAAA,CAACP,KAAK,EACJgJ,YAAY,CAAEL,KAAK,CAAClH,OAAQ,CAC5BuF,KAAK,CAAE2B,KAAK,CAAC3B,KAAK,EAAI,SAAU,CAChCf,EAAE,CAAE,CACF,mBAAmB,CAAE,CACnBkB,QAAQ,CAAE,QAAQ,CAClBxF,MAAM,CAAE,MAAM,CACdsH,QAAQ,CAAE,MACZ,CACF,CAAE,CACH,CAAC,EAXUL,KAYL,CACV,CAAC,CACC,CACN,CAGArG,YAAY,eACXhC,IAAA,CAACjB,GAAG,EAAC4J,EAAE,CAAC,MAAM,CAAAtH,QAAA,CACXW,YAAY,CACV,CACN,EACE,CAAC,CAEL1B,QAAQ,eACPN,IAAA,CAACd,UAAU,EACTmH,OAAO,CAAC,OAAO,CACfX,EAAE,CAAE,CACF6B,OAAO,CAAE,GAAG,CACZX,QAAQ,CAAE,CAAEC,EAAE,CAAE,SAAS,CAAEC,EAAE,CAAE,QAAS,CAAC,CACzCL,KAAK,CAAEnF,OAAO,GAAK,MAAM,CAAGiB,KAAK,CAACM,OAAO,CAACgF,IAAI,CAACe,SAAS,CAAG,SAAS,CACpEtC,EAAE,CAAE,GACN,CAAE,CAAAjF,QAAA,CAEDf,QAAQ,CACC,CACb,CAGAsB,IAAI,EAAIA,IAAI,CAACqG,MAAM,CAAG,CAAC,eACtBjI,IAAA,CAACjB,GAAG,EAAC2G,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAE+B,QAAQ,CAAE,MAAM,CAAEG,GAAG,CAAE,GAAG,CAAE5B,EAAE,CAAE,CAAE,CAAE,CAAAjF,QAAA,CAC7DO,IAAI,CAACuG,GAAG,CAAC,CAACU,GAAG,CAAER,KAAK,gBACnBrI,IAAA,CAACV,IAAI,EAEHwJ,KAAK,CAAED,GAAG,CAACC,KAAM,CACjB5B,IAAI,CAAC,OAAO,CACZT,KAAK,CAAEoC,GAAG,CAACpC,KAAK,EAAI,SAAU,CAC9BJ,OAAO,CAAC,UAAU,CAClBX,EAAE,CAAE,CACFtE,MAAM,CAAE,MAAM,CACdwF,QAAQ,CAAE,QAAQ,CAClBpD,eAAe,CAAEnE,KAAK,CAACkD,KAAK,CAACM,OAAO,CAACgG,GAAG,CAACpC,KAAK,EAAI,SAAS,CAAC,CAAC1D,IAAI,CAAE,GAAG,CAAC,CACvE0D,KAAK,CAAEnF,OAAO,GAAK,MAAM,CAAGiB,KAAK,CAACM,OAAO,CAACgG,GAAG,CAACpC,KAAK,EAAI,SAAS,CAAC,CAAC1D,IAAI,CAAG,OAAO,CAChFgB,WAAW,CAAE1E,KAAK,CAACkD,KAAK,CAACM,OAAO,CAACgG,GAAG,CAACpC,KAAK,EAAI,SAAS,CAAC,CAAC1D,IAAI,CAAE,GAAG,CACpE,CAAE,EAXGsF,KAYN,CACF,CAAC,CACC,CACN,EACE,CAAC,CAGL1H,IAAI,eACHX,IAAA,CAACjB,GAAG,EACF2G,EAAE,CAAE,CACFK,CAAC,CAAE,GAAG,CACNH,YAAY,CAAE,MAAM,CACpBpC,eAAe,CAAElC,OAAO,GAAK,MAAM,CAC/BjC,KAAK,CAAC6D,SAAS,CAAE,GAAG,CAAC,CACrB,0BAA0B,CAC9BuD,KAAK,CAAEnF,OAAO,GAAK,MAAM,CAAG4B,SAAS,CAAG,OAAO,CAC/C8C,OAAO,CAAE,MAAM,CACfE,UAAU,CAAE,QAAQ,CACpBD,cAAc,CAAE,QAAQ,CACxB3C,SAAS,CAAE,4BAA4B,CACvCqF,EAAE,CAAE,CACN,CAAE,CAAAtH,QAAA,CAEDV,IAAI,CACF,CACN,EACE,CAAC,CAGLkB,QAAQ,GAAK2F,SAAS,EAAI,CAAC1F,eAAe,eACzC5B,KAAA,CAACnB,GAAG,EAAC2G,EAAE,CAAE,CAAEM,OAAO,CAAE,MAAM,CAAEE,UAAU,CAAE,QAAQ,CAAEC,EAAE,CAAE,CAAE,CAAE,CAAA9E,QAAA,eACxDrB,IAAA,CAACN,gBAAgB,EACf2G,OAAO,CAAC,aAAa,CACrB0C,KAAK,CAAElH,QAAS,CAChBqF,IAAI,CAAE,EAAG,CACT8B,SAAS,CAAE,CAAE,CACbtD,EAAE,CAAE,CAAEe,KAAK,CAAElE,KAAK,CAACM,OAAO,CAAC4E,OAAO,CAAC1E,IAAK,CAAE,CAC3C,CAAC,cACF7C,KAAA,CAAChB,UAAU,EAACmH,OAAO,CAAC,OAAO,CAACX,EAAE,CAAE,CAAEiD,EAAE,CAAE,CAAC,CAAEpB,OAAO,CAAE,GAAI,CAAE,CAAAlG,QAAA,EACrDQ,QAAQ,CAAC,YACZ,EAAY,CAAC,EACV,CACN,cAGD7B,IAAA,CAACH,eAAe,EAAAwB,QAAA,cAEZrB,IAAA,CAACjB,GAAG,EACFkK,SAAS,CAAErJ,MAAM,CAACsJ,GAAI,CACtBC,OAAO,CAAE,CAAE5B,OAAO,CAAE,CAAC,CAAEnG,MAAM,CAAE,CAAE,CAAE,CACnCgI,OAAO,CAAE,CAAE7B,OAAO,CAAE,CAAC,CAAEnG,MAAM,CAAE,MAAO,CAAE,CACxCiI,IAAI,CAAE,CAAE9B,OAAO,CAAE,CAAC,CAAEnG,MAAM,CAAE,CAAE,CAAE,CAChC+F,UAAU,CAAE,CAAEmC,QAAQ,CAAE,GAAI,CAAE,CAC9B5D,EAAE,CAAE,CAAEsB,IAAI,CAAE,CAAE,CAAE,CAAA3F,QAAA,CAEfA,QAAQ,eACPrB,IAAA,CAACjB,GAAG,EAAC2G,EAAE,CAAE,CAAEsB,IAAI,CAAE,CAAE,CAAE,CAAA3F,QAAA,CAClBA,QAAQ,CACN,CACN,CACE,CAAC,CAEO,CAAC,CAGjBY,aAAa,eACZ/B,KAAA,CAACnB,GAAG,EAACuH,EAAE,CAAE,CAAE,CAAAjF,QAAA,eACTrB,IAAA,CAACL,OAAO,EAAC+F,EAAE,CAAE,CAAE6D,EAAE,CAAE,CAAC,CAAEhC,OAAO,CAAE,GAAI,CAAE,CAAE,CAAC,CACvCtF,aAAa,EACX,CACN,CAGA,CAACnB,UAAU,EAAIC,WAAW,EAAIoB,eAAe,gBAC5CjC,KAAA,CAACnB,GAAG,EAACuH,EAAE,CAAC,MAAM,CAACkD,EAAE,CAAE,CAAE,CAACxD,OAAO,CAAC,MAAM,CAACC,cAAc,CAAC,UAAU,CAACC,UAAU,CAAC,QAAQ,CAAA7E,QAAA,EAE/Ec,eAAe,eACdnC,IAAA,CAAClB,YAAY,EACXuH,OAAO,CAAC,MAAM,CACda,IAAI,CAAC,OAAO,CACZuC,SAAS,CAAEtH,eAAe,CAACxB,IAAK,CAChC8E,OAAO,CAAED,0BAA2B,CACpCE,EAAE,CAAE,CACFsC,EAAE,CAAE,MAAM,CACVvB,KAAK,CAAEnF,OAAO,GAAK,MAAM,CAAGiB,KAAK,CAACM,OAAO,CAACgF,IAAI,CAACe,SAAS,CAAG,0BAA0B,CACrF,SAAS,CAAE,CACTpF,eAAe,CAAElC,OAAO,GAAK,MAAM,CAAG,qBAAqB,CAAG,0BAChE,CACF,CAAE,CAAAD,QAAA,CAEDc,eAAe,CAAC2G,KAAK,CACV,CACf,CAGAhI,UAAU,eACTd,IAAA,CAACb,UAAU,EACTsG,OAAO,CAAGJ,CAAC,EAAKD,iBAAiB,CAACC,CAAC,CAAErE,QAAQ,CAAE,CAC/C0E,EAAE,CAAE,CACFe,KAAK,CAAEnF,OAAO,GAAK,MAAM,CAAG4B,SAAS,CAAG,OAAO,CAC/CM,eAAe,CAAElC,OAAO,GAAK,MAAM,CAAGjC,KAAK,CAAC6D,SAAS,CAAE,GAAG,CAAC,CAAG,0BAA0B,CACxF,SAAS,CAAE,CACTM,eAAe,CAAElC,OAAO,GAAK,MAAM,CAAGjC,KAAK,CAAC6D,SAAS,CAAE,GAAG,CAAC,CAAG,0BAChE,CACF,CAAE,CAAA7B,QAAA,CAEDP,UAAU,CACD,CACb,CAGAC,WAAW,eACVf,IAAA,CAAClB,YAAY,EACXuH,OAAO,CAAC,UAAU,CAClBa,IAAI,CAAC,OAAO,CACZzB,OAAO,CAAGJ,CAAC,EAAKD,iBAAiB,CAACC,CAAC,CAAErE,QAAQ,CAAE,CAC/C0E,EAAE,CAAE,CACFiD,EAAE,CAAE7H,UAAU,CAAG,CAAC,CAAG,CAAC,CACtB2F,KAAK,CAAEnF,OAAO,GAAK,MAAM,CAAG4B,SAAS,CAAG,OAAO,CAC/Ca,WAAW,CAAEzC,OAAO,GAAK,MAAM,CAAGjC,KAAK,CAAC6D,SAAS,CAAE,GAAG,CAAC,CAAG,0BAA0B,CACpF,SAAS,CAAE,CACTa,WAAW,CAAEzC,OAAO,GAAK,MAAM,CAAG4B,SAAS,CAAG,OAAO,CACrDM,eAAe,CAAElC,OAAO,GAAK,MAAM,CAAGjC,KAAK,CAAC6D,SAAS,CAAE,GAAG,CAAC,CAAG,0BAChE,CACF,CAAE,CAAA7B,QAAA,CAEDN,WAAW,CACA,CACf,EACE,CACN,EACU,CAAC,GACV,CAAC,CACJ,CAAC,CAEV,CAAC,CAED,cAAe,CAAAZ,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}