{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ConnectionPool = exports.PoolState = void 0;\nconst timers_1 = require(\"timers\");\nconst constants_1 = require(\"../constants\");\nconst error_1 = require(\"../error\");\nconst mongo_types_1 = require(\"../mongo_types\");\nconst timeout_1 = require(\"../timeout\");\nconst utils_1 = require(\"../utils\");\nconst connect_1 = require(\"./connect\");\nconst connection_1 = require(\"./connection\");\nconst connection_pool_events_1 = require(\"./connection_pool_events\");\nconst errors_1 = require(\"./errors\");\nconst metrics_1 = require(\"./metrics\");\n/** @internal */\nexports.PoolState = Object.freeze({\n  paused: 'paused',\n  ready: 'ready',\n  closed: 'closed'\n});\n/**\n * A pool of connections which dynamically resizes, and emit events related to pool activity\n * @internal\n */\nclass ConnectionPool extends mongo_types_1.TypedEventEmitter {\n  constructor(server, options) {\n    super();\n    this.on('error', utils_1.noop);\n    this.options = Object.freeze({\n      connectionType: connection_1.Connection,\n      ...options,\n      maxPoolSize: options.maxPoolSize ?? 100,\n      minPoolSize: options.minPoolSize ?? 0,\n      maxConnecting: options.maxConnecting ?? 2,\n      maxIdleTimeMS: options.maxIdleTimeMS ?? 0,\n      waitQueueTimeoutMS: options.waitQueueTimeoutMS ?? 0,\n      minPoolSizeCheckFrequencyMS: options.minPoolSizeCheckFrequencyMS ?? 100,\n      autoEncrypter: options.autoEncrypter\n    });\n    if (this.options.minPoolSize > this.options.maxPoolSize) {\n      throw new error_1.MongoInvalidArgumentError('Connection pool minimum size must not be greater than maximum pool size');\n    }\n    this.poolState = exports.PoolState.paused;\n    this.server = server;\n    this.connections = new utils_1.List();\n    this.pending = 0;\n    this.checkedOut = new Set();\n    this.minPoolSizeTimer = undefined;\n    this.generation = 0;\n    this.serviceGenerations = new Map();\n    this.connectionCounter = (0, utils_1.makeCounter)(1);\n    this.cancellationToken = new mongo_types_1.CancellationToken();\n    this.cancellationToken.setMaxListeners(Infinity);\n    this.waitQueue = new utils_1.List();\n    this.metrics = new metrics_1.ConnectionPoolMetrics();\n    this.processingWaitQueue = false;\n    this.mongoLogger = this.server.topology.client?.mongoLogger;\n    this.component = 'connection';\n    process.nextTick(() => {\n      this.emitAndLog(ConnectionPool.CONNECTION_POOL_CREATED, new connection_pool_events_1.ConnectionPoolCreatedEvent(this));\n    });\n  }\n  /** The address of the endpoint the pool is connected to */\n  get address() {\n    return this.options.hostAddress.toString();\n  }\n  /**\n   * Check if the pool has been closed\n   *\n   * TODO(NODE-3263): We can remove this property once shell no longer needs it\n   */\n  get closed() {\n    return this.poolState === exports.PoolState.closed;\n  }\n  /** An integer expressing how many total connections (available + pending + in use) the pool currently has */\n  get totalConnectionCount() {\n    return this.availableConnectionCount + this.pendingConnectionCount + this.currentCheckedOutCount;\n  }\n  /** An integer expressing how many connections are currently available in the pool. */\n  get availableConnectionCount() {\n    return this.connections.length;\n  }\n  get pendingConnectionCount() {\n    return this.pending;\n  }\n  get currentCheckedOutCount() {\n    return this.checkedOut.size;\n  }\n  get waitQueueSize() {\n    return this.waitQueue.length;\n  }\n  get loadBalanced() {\n    return this.options.loadBalanced;\n  }\n  get serverError() {\n    return this.server.description.error;\n  }\n  /**\n   * This is exposed ONLY for use in mongosh, to enable\n   * killing all connections if a user quits the shell with\n   * operations in progress.\n   *\n   * This property may be removed as a part of NODE-3263.\n   */\n  get checkedOutConnections() {\n    return this.checkedOut;\n  }\n  /**\n   * Get the metrics information for the pool when a wait queue timeout occurs.\n   */\n  waitQueueErrorMetrics() {\n    return this.metrics.info(this.options.maxPoolSize);\n  }\n  /**\n   * Set the pool state to \"ready\"\n   */\n  ready() {\n    if (this.poolState !== exports.PoolState.paused) {\n      return;\n    }\n    this.poolState = exports.PoolState.ready;\n    this.emitAndLog(ConnectionPool.CONNECTION_POOL_READY, new connection_pool_events_1.ConnectionPoolReadyEvent(this));\n    (0, timers_1.clearTimeout)(this.minPoolSizeTimer);\n    this.ensureMinPoolSize();\n  }\n  /**\n   * Check a connection out of this pool. The connection will continue to be tracked, but no reference to it\n   * will be held by the pool. This means that if a connection is checked out it MUST be checked back in or\n   * explicitly destroyed by the new owner.\n   */\n  async checkOut(options) {\n    const checkoutTime = (0, utils_1.now)();\n    this.emitAndLog(ConnectionPool.CONNECTION_CHECK_OUT_STARTED, new connection_pool_events_1.ConnectionCheckOutStartedEvent(this));\n    const {\n      promise,\n      resolve,\n      reject\n    } = (0, utils_1.promiseWithResolvers)();\n    const timeout = options.timeoutContext.connectionCheckoutTimeout;\n    const waitQueueMember = {\n      resolve,\n      reject,\n      cancelled: false,\n      checkoutTime\n    };\n    const abortListener = (0, utils_1.addAbortListener)(options.signal, function () {\n      waitQueueMember.cancelled = true;\n      reject(this.reason);\n    });\n    this.waitQueue.push(waitQueueMember);\n    process.nextTick(() => this.processWaitQueue());\n    try {\n      timeout?.throwIfExpired();\n      return await (timeout ? Promise.race([promise, timeout]) : promise);\n    } catch (error) {\n      if (timeout_1.TimeoutError.is(error)) {\n        timeout?.clear();\n        waitQueueMember.cancelled = true;\n        this.emitAndLog(ConnectionPool.CONNECTION_CHECK_OUT_FAILED, new connection_pool_events_1.ConnectionCheckOutFailedEvent(this, 'timeout', waitQueueMember.checkoutTime));\n        const timeoutError = new errors_1.WaitQueueTimeoutError(this.loadBalanced ? this.waitQueueErrorMetrics() : 'Timed out while checking out a connection from connection pool', this.address);\n        if (options.timeoutContext.csotEnabled()) {\n          throw new error_1.MongoOperationTimeoutError('Timed out during connection checkout', {\n            cause: timeoutError\n          });\n        }\n        throw timeoutError;\n      }\n      throw error;\n    } finally {\n      abortListener?.[utils_1.kDispose]();\n      timeout?.clear();\n    }\n  }\n  /**\n   * Check a connection into the pool.\n   *\n   * @param connection - The connection to check in\n   */\n  checkIn(connection) {\n    if (!this.checkedOut.has(connection)) {\n      return;\n    }\n    const poolClosed = this.closed;\n    const stale = this.connectionIsStale(connection);\n    const willDestroy = !!(poolClosed || stale || connection.closed);\n    if (!willDestroy) {\n      connection.markAvailable();\n      this.connections.unshift(connection);\n    }\n    this.checkedOut.delete(connection);\n    this.emitAndLog(ConnectionPool.CONNECTION_CHECKED_IN, new connection_pool_events_1.ConnectionCheckedInEvent(this, connection));\n    if (willDestroy) {\n      const reason = connection.closed ? 'error' : poolClosed ? 'poolClosed' : 'stale';\n      this.destroyConnection(connection, reason);\n    }\n    process.nextTick(() => this.processWaitQueue());\n  }\n  /**\n   * Clear the pool\n   *\n   * Pool reset is handled by incrementing the pool's generation count. Any existing connection of a\n   * previous generation will eventually be pruned during subsequent checkouts.\n   */\n  clear(options = {}) {\n    if (this.closed) {\n      return;\n    }\n    // handle load balanced case\n    if (this.loadBalanced) {\n      const {\n        serviceId\n      } = options;\n      if (!serviceId) {\n        throw new error_1.MongoRuntimeError('ConnectionPool.clear() called in load balanced mode with no serviceId.');\n      }\n      const sid = serviceId.toHexString();\n      const generation = this.serviceGenerations.get(sid);\n      // Only need to worry if the generation exists, since it should\n      // always be there but typescript needs the check.\n      if (generation == null) {\n        throw new error_1.MongoRuntimeError('Service generations are required in load balancer mode.');\n      } else {\n        // Increment the generation for the service id.\n        this.serviceGenerations.set(sid, generation + 1);\n      }\n      this.emitAndLog(ConnectionPool.CONNECTION_POOL_CLEARED, new connection_pool_events_1.ConnectionPoolClearedEvent(this, {\n        serviceId\n      }));\n      return;\n    }\n    // handle non load-balanced case\n    const interruptInUseConnections = options.interruptInUseConnections ?? false;\n    const oldGeneration = this.generation;\n    this.generation += 1;\n    const alreadyPaused = this.poolState === exports.PoolState.paused;\n    this.poolState = exports.PoolState.paused;\n    this.clearMinPoolSizeTimer();\n    if (!alreadyPaused) {\n      this.emitAndLog(ConnectionPool.CONNECTION_POOL_CLEARED, new connection_pool_events_1.ConnectionPoolClearedEvent(this, {\n        interruptInUseConnections\n      }));\n    }\n    if (interruptInUseConnections) {\n      process.nextTick(() => this.interruptInUseConnections(oldGeneration));\n    }\n    this.processWaitQueue();\n  }\n  /**\n   * Closes all stale in-use connections in the pool with a resumable PoolClearedOnNetworkError.\n   *\n   * Only connections where `connection.generation <= minGeneration` are killed.\n   */\n  interruptInUseConnections(minGeneration) {\n    for (const connection of this.checkedOut) {\n      if (connection.generation <= minGeneration) {\n        connection.onError(new errors_1.PoolClearedOnNetworkError(this));\n        this.checkIn(connection);\n      }\n    }\n  }\n  /** Close the pool */\n  close() {\n    if (this.closed) {\n      return;\n    }\n    // immediately cancel any in-flight connections\n    this.cancellationToken.emit('cancel');\n    // end the connection counter\n    if (typeof this.connectionCounter.return === 'function') {\n      this.connectionCounter.return(undefined);\n    }\n    this.poolState = exports.PoolState.closed;\n    this.clearMinPoolSizeTimer();\n    this.processWaitQueue();\n    for (const conn of this.connections) {\n      this.emitAndLog(ConnectionPool.CONNECTION_CLOSED, new connection_pool_events_1.ConnectionClosedEvent(this, conn, 'poolClosed'));\n      conn.destroy();\n    }\n    this.connections.clear();\n    this.emitAndLog(ConnectionPool.CONNECTION_POOL_CLOSED, new connection_pool_events_1.ConnectionPoolClosedEvent(this));\n  }\n  /**\n   * @internal\n   * Reauthenticate a connection\n   */\n  async reauthenticate(connection) {\n    const authContext = connection.authContext;\n    if (!authContext) {\n      throw new error_1.MongoRuntimeError('No auth context found on connection.');\n    }\n    const credentials = authContext.credentials;\n    if (!credentials) {\n      throw new error_1.MongoMissingCredentialsError('Connection is missing credentials when asked to reauthenticate');\n    }\n    const resolvedCredentials = credentials.resolveAuthMechanism(connection.hello);\n    const provider = this.server.topology.client.s.authProviders.getOrCreateProvider(resolvedCredentials.mechanism, resolvedCredentials.mechanismProperties);\n    if (!provider) {\n      throw new error_1.MongoMissingCredentialsError(`Reauthenticate failed due to no auth provider for ${credentials.mechanism}`);\n    }\n    await provider.reauth(authContext);\n    return;\n  }\n  /** Clear the min pool size timer */\n  clearMinPoolSizeTimer() {\n    const minPoolSizeTimer = this.minPoolSizeTimer;\n    if (minPoolSizeTimer) {\n      (0, timers_1.clearTimeout)(minPoolSizeTimer);\n    }\n  }\n  destroyConnection(connection, reason) {\n    this.emitAndLog(ConnectionPool.CONNECTION_CLOSED, new connection_pool_events_1.ConnectionClosedEvent(this, connection, reason));\n    // destroy the connection\n    connection.destroy();\n  }\n  connectionIsStale(connection) {\n    const serviceId = connection.serviceId;\n    if (this.loadBalanced && serviceId) {\n      const sid = serviceId.toHexString();\n      const generation = this.serviceGenerations.get(sid);\n      return connection.generation !== generation;\n    }\n    return connection.generation !== this.generation;\n  }\n  connectionIsIdle(connection) {\n    return !!(this.options.maxIdleTimeMS && connection.idleTime > this.options.maxIdleTimeMS);\n  }\n  /**\n   * Destroys a connection if the connection is perished.\n   *\n   * @returns `true` if the connection was destroyed, `false` otherwise.\n   */\n  destroyConnectionIfPerished(connection) {\n    const isStale = this.connectionIsStale(connection);\n    const isIdle = this.connectionIsIdle(connection);\n    if (!isStale && !isIdle && !connection.closed) {\n      return false;\n    }\n    const reason = connection.closed ? 'error' : isStale ? 'stale' : 'idle';\n    this.destroyConnection(connection, reason);\n    return true;\n  }\n  createConnection(callback) {\n    const connectOptions = {\n      ...this.options,\n      id: this.connectionCounter.next().value,\n      generation: this.generation,\n      cancellationToken: this.cancellationToken,\n      mongoLogger: this.mongoLogger,\n      authProviders: this.server.topology.client.s.authProviders\n    };\n    this.pending++;\n    // This is our version of a \"virtual\" no-I/O connection as the spec requires\n    const connectionCreatedTime = (0, utils_1.now)();\n    this.emitAndLog(ConnectionPool.CONNECTION_CREATED, new connection_pool_events_1.ConnectionCreatedEvent(this, {\n      id: connectOptions.id\n    }));\n    (0, connect_1.connect)(connectOptions).then(connection => {\n      // The pool might have closed since we started trying to create a connection\n      if (this.poolState !== exports.PoolState.ready) {\n        this.pending--;\n        connection.destroy();\n        callback(this.closed ? new errors_1.PoolClosedError(this) : new errors_1.PoolClearedError(this));\n        return;\n      }\n      // forward all events from the connection to the pool\n      for (const event of [...constants_1.APM_EVENTS, connection_1.Connection.CLUSTER_TIME_RECEIVED]) {\n        connection.on(event, e => this.emit(event, e));\n      }\n      if (this.loadBalanced) {\n        connection.on(connection_1.Connection.PINNED, pinType => this.metrics.markPinned(pinType));\n        connection.on(connection_1.Connection.UNPINNED, pinType => this.metrics.markUnpinned(pinType));\n        const serviceId = connection.serviceId;\n        if (serviceId) {\n          let generation;\n          const sid = serviceId.toHexString();\n          if (generation = this.serviceGenerations.get(sid)) {\n            connection.generation = generation;\n          } else {\n            this.serviceGenerations.set(sid, 0);\n            connection.generation = 0;\n          }\n        }\n      }\n      connection.markAvailable();\n      this.emitAndLog(ConnectionPool.CONNECTION_READY, new connection_pool_events_1.ConnectionReadyEvent(this, connection, connectionCreatedTime));\n      this.pending--;\n      callback(undefined, connection);\n    }, error => {\n      this.pending--;\n      this.server.handleError(error);\n      this.emitAndLog(ConnectionPool.CONNECTION_CLOSED, new connection_pool_events_1.ConnectionClosedEvent(this, {\n        id: connectOptions.id,\n        serviceId: undefined\n      }, 'error',\n      // TODO(NODE-5192): Remove this cast\n      error));\n      if (error instanceof error_1.MongoNetworkError || error instanceof error_1.MongoServerError) {\n        error.connectionGeneration = connectOptions.generation;\n      }\n      callback(error ?? new error_1.MongoRuntimeError('Connection creation failed without error'));\n    });\n  }\n  ensureMinPoolSize() {\n    const minPoolSize = this.options.minPoolSize;\n    if (this.poolState !== exports.PoolState.ready || minPoolSize === 0) {\n      return;\n    }\n    this.connections.prune(connection => this.destroyConnectionIfPerished(connection));\n    if (this.totalConnectionCount < minPoolSize && this.pendingConnectionCount < this.options.maxConnecting) {\n      // NOTE: ensureMinPoolSize should not try to get all the pending\n      // connection permits because that potentially delays the availability of\n      // the connection to a checkout request\n      this.createConnection((err, connection) => {\n        if (!err && connection) {\n          this.connections.push(connection);\n          process.nextTick(() => this.processWaitQueue());\n        }\n        if (this.poolState === exports.PoolState.ready) {\n          (0, timers_1.clearTimeout)(this.minPoolSizeTimer);\n          this.minPoolSizeTimer = (0, timers_1.setTimeout)(() => this.ensureMinPoolSize(), this.options.minPoolSizeCheckFrequencyMS);\n        }\n      });\n    } else {\n      (0, timers_1.clearTimeout)(this.minPoolSizeTimer);\n      this.minPoolSizeTimer = (0, timers_1.setTimeout)(() => this.ensureMinPoolSize(), this.options.minPoolSizeCheckFrequencyMS);\n    }\n  }\n  processWaitQueue() {\n    if (this.processingWaitQueue) {\n      return;\n    }\n    this.processingWaitQueue = true;\n    while (this.waitQueueSize) {\n      const waitQueueMember = this.waitQueue.first();\n      if (!waitQueueMember) {\n        this.waitQueue.shift();\n        continue;\n      }\n      if (waitQueueMember.cancelled) {\n        this.waitQueue.shift();\n        continue;\n      }\n      if (this.poolState !== exports.PoolState.ready) {\n        const reason = this.closed ? 'poolClosed' : 'connectionError';\n        const error = this.closed ? new errors_1.PoolClosedError(this) : new errors_1.PoolClearedError(this);\n        this.emitAndLog(ConnectionPool.CONNECTION_CHECK_OUT_FAILED, new connection_pool_events_1.ConnectionCheckOutFailedEvent(this, reason, waitQueueMember.checkoutTime, error));\n        this.waitQueue.shift();\n        waitQueueMember.reject(error);\n        continue;\n      }\n      if (!this.availableConnectionCount) {\n        break;\n      }\n      const connection = this.connections.shift();\n      if (!connection) {\n        break;\n      }\n      if (!this.destroyConnectionIfPerished(connection)) {\n        this.checkedOut.add(connection);\n        this.emitAndLog(ConnectionPool.CONNECTION_CHECKED_OUT, new connection_pool_events_1.ConnectionCheckedOutEvent(this, connection, waitQueueMember.checkoutTime));\n        this.waitQueue.shift();\n        waitQueueMember.resolve(connection);\n      }\n    }\n    const {\n      maxPoolSize,\n      maxConnecting\n    } = this.options;\n    while (this.waitQueueSize > 0 && this.pendingConnectionCount < maxConnecting && (maxPoolSize === 0 || this.totalConnectionCount < maxPoolSize)) {\n      const waitQueueMember = this.waitQueue.shift();\n      if (!waitQueueMember || waitQueueMember.cancelled) {\n        continue;\n      }\n      this.createConnection((err, connection) => {\n        if (waitQueueMember.cancelled) {\n          if (!err && connection) {\n            this.connections.push(connection);\n          }\n        } else {\n          if (err) {\n            this.emitAndLog(ConnectionPool.CONNECTION_CHECK_OUT_FAILED,\n            // TODO(NODE-5192): Remove this cast\n            new connection_pool_events_1.ConnectionCheckOutFailedEvent(this, 'connectionError', waitQueueMember.checkoutTime, err));\n            waitQueueMember.reject(err);\n          } else if (connection) {\n            this.checkedOut.add(connection);\n            this.emitAndLog(ConnectionPool.CONNECTION_CHECKED_OUT, new connection_pool_events_1.ConnectionCheckedOutEvent(this, connection, waitQueueMember.checkoutTime));\n            waitQueueMember.resolve(connection);\n          }\n        }\n        process.nextTick(() => this.processWaitQueue());\n      });\n    }\n    this.processingWaitQueue = false;\n  }\n}\nexports.ConnectionPool = ConnectionPool;\n/**\n * Emitted when the connection pool is created.\n * @event\n */\nConnectionPool.CONNECTION_POOL_CREATED = constants_1.CONNECTION_POOL_CREATED;\n/**\n * Emitted once when the connection pool is closed\n * @event\n */\nConnectionPool.CONNECTION_POOL_CLOSED = constants_1.CONNECTION_POOL_CLOSED;\n/**\n * Emitted each time the connection pool is cleared and it's generation incremented\n * @event\n */\nConnectionPool.CONNECTION_POOL_CLEARED = constants_1.CONNECTION_POOL_CLEARED;\n/**\n * Emitted each time the connection pool is marked ready\n * @event\n */\nConnectionPool.CONNECTION_POOL_READY = constants_1.CONNECTION_POOL_READY;\n/**\n * Emitted when a connection is created.\n * @event\n */\nConnectionPool.CONNECTION_CREATED = constants_1.CONNECTION_CREATED;\n/**\n * Emitted when a connection becomes established, and is ready to use\n * @event\n */\nConnectionPool.CONNECTION_READY = constants_1.CONNECTION_READY;\n/**\n * Emitted when a connection is closed\n * @event\n */\nConnectionPool.CONNECTION_CLOSED = constants_1.CONNECTION_CLOSED;\n/**\n * Emitted when an attempt to check out a connection begins\n * @event\n */\nConnectionPool.CONNECTION_CHECK_OUT_STARTED = constants_1.CONNECTION_CHECK_OUT_STARTED;\n/**\n * Emitted when an attempt to check out a connection fails\n * @event\n */\nConnectionPool.CONNECTION_CHECK_OUT_FAILED = constants_1.CONNECTION_CHECK_OUT_FAILED;\n/**\n * Emitted each time a connection is successfully checked out of the connection pool\n * @event\n */\nConnectionPool.CONNECTION_CHECKED_OUT = constants_1.CONNECTION_CHECKED_OUT;\n/**\n * Emitted each time a connection is successfully checked into the connection pool\n * @event\n */\nConnectionPool.CONNECTION_CHECKED_IN = constants_1.CONNECTION_CHECKED_IN;", "map": {"version": 3, "names": ["timers_1", "require", "constants_1", "error_1", "mongo_types_1", "timeout_1", "utils_1", "connect_1", "connection_1", "connection_pool_events_1", "errors_1", "metrics_1", "exports", "PoolState", "Object", "freeze", "paused", "ready", "closed", "ConnectionPool", "TypedEventEmitter", "constructor", "server", "options", "on", "noop", "connectionType", "Connection", "maxPoolSize", "minPoolSize", "maxConnecting", "maxIdleTimeMS", "waitQueueTimeoutMS", "minPoolSizeCheckFrequencyMS", "autoEncrypter", "MongoInvalidArgumentError", "poolState", "connections", "List", "pending", "checkedOut", "Set", "minPoolSizeTimer", "undefined", "generation", "serviceGenerations", "Map", "connectionCounter", "makeCounter", "cancellationToken", "CancellationToken", "setMaxListeners", "Infinity", "waitQueue", "metrics", "ConnectionPoolMetrics", "processingWaitQueue", "mongoLogger", "topology", "client", "component", "process", "nextTick", "emitAndLog", "CONNECTION_POOL_CREATED", "ConnectionPoolCreatedEvent", "address", "host<PERSON><PERSON><PERSON>", "toString", "totalConnectionCount", "availableConnectionCount", "pendingConnectionCount", "currentCheckedOutCount", "length", "size", "waitQueueSize", "loadBalanced", "serverError", "description", "error", "checkedOutConnections", "waitQueueErrorMetrics", "info", "CONNECTION_POOL_READY", "ConnectionPoolReadyEvent", "clearTimeout", "ensureMinPoolSize", "checkOut", "checkoutTime", "now", "CONNECTION_CHECK_OUT_STARTED", "ConnectionCheckOutStartedEvent", "promise", "resolve", "reject", "promiseWithResolvers", "timeout", "timeoutContext", "connectionCheckoutTimeout", "waitQueueMember", "cancelled", "abortListener", "addAbortListener", "signal", "reason", "push", "processWaitQueue", "throwIfExpired", "Promise", "race", "TimeoutError", "is", "clear", "CONNECTION_CHECK_OUT_FAILED", "ConnectionCheckOutFailedEvent", "timeoutError", "WaitQueueTimeoutError", "csotEnabled", "MongoOperationTimeoutError", "cause", "kDispose", "checkIn", "connection", "has", "poolClosed", "stale", "connectionIsStale", "<PERSON><PERSON><PERSON><PERSON>", "markAvailable", "unshift", "delete", "CONNECTION_CHECKED_IN", "ConnectionCheckedInEvent", "destroyConnection", "serviceId", "MongoRuntimeError", "sid", "toHexString", "get", "set", "CONNECTION_POOL_CLEARED", "ConnectionPoolClearedEvent", "interruptInUseConnections", "oldGeneration", "alreadyPaused", "clearMinPoolSizeTimer", "minGeneration", "onError", "PoolClearedOnNetworkError", "close", "emit", "return", "conn", "CONNECTION_CLOSED", "ConnectionClosedEvent", "destroy", "CONNECTION_POOL_CLOSED", "ConnectionPoolClosedEvent", "reauthenticate", "authContext", "credentials", "MongoMissingCredentialsError", "resolvedCredentials", "resolveAuthMechanism", "hello", "provider", "s", "authProviders", "getOrCreateProvider", "mechanism", "mechanismProperties", "reauth", "connectionIsIdle", "idleTime", "destroyConnectionIfPerished", "isStale", "isIdle", "createConnection", "callback", "connectOptions", "id", "next", "value", "connectionCreatedTime", "CONNECTION_CREATED", "ConnectionCreatedEvent", "connect", "then", "PoolClosedError", "PoolClearedError", "event", "APM_EVENTS", "CLUSTER_TIME_RECEIVED", "e", "PINNED", "pinType", "<PERSON><PERSON><PERSON><PERSON>", "UNPINNED", "markUnpinned", "CONNECTION_READY", "ConnectionReadyEvent", "handleError", "MongoNetworkError", "MongoServerError", "connectionGeneration", "prune", "err", "setTimeout", "first", "shift", "add", "CONNECTION_CHECKED_OUT", "ConnectionCheckedOutEvent"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\connection_pool.ts"], "sourcesContent": ["import { clearTimeout, setTimeout } from 'timers';\n\nimport type { ObjectId } from '../bson';\nimport {\n  APM_EVENTS,\n  CONNECTION_CHECK_OUT_FAILED,\n  CONNECTION_CHECK_OUT_STARTED,\n  CONNECTION_CHECKED_IN,\n  CONNECTION_CHECKED_OUT,\n  CONNECTION_CLOSED,\n  CONNECTION_CREATED,\n  CONNECTION_POOL_CLEARED,\n  CONNECTION_POOL_CLOSED,\n  CONNECTION_POOL_CREATED,\n  CONNECTION_POOL_READY,\n  CONNECTION_READY\n} from '../constants';\nimport {\n  type AnyError,\n  type MongoError,\n  MongoInvalidArgumentError,\n  MongoMissingCredentialsError,\n  MongoNetworkError,\n  MongoOperationTimeoutError,\n  MongoRuntimeError,\n  MongoServerError\n} from '../error';\nimport { type Abortable, CancellationToken, TypedEventEmitter } from '../mongo_types';\nimport type { Server } from '../sdam/server';\nimport { type TimeoutContext, TimeoutError } from '../timeout';\nimport {\n  addAbortListener,\n  type Callback,\n  kDispose,\n  List,\n  makeCounter,\n  noop,\n  now,\n  promiseWithResolvers\n} from '../utils';\nimport { connect } from './connect';\nimport { Connection, type ConnectionEvents, type ConnectionOptions } from './connection';\nimport {\n  ConnectionCheckedInEvent,\n  ConnectionCheckedOutEvent,\n  ConnectionCheckOutFailedEvent,\n  ConnectionCheckOutStartedEvent,\n  ConnectionClosedEvent,\n  ConnectionCreatedEvent,\n  ConnectionPoolClearedEvent,\n  ConnectionPoolClosedEvent,\n  ConnectionPoolCreatedEvent,\n  ConnectionPoolReadyEvent,\n  ConnectionReadyEvent\n} from './connection_pool_events';\nimport {\n  PoolClearedError,\n  PoolClearedOnNetworkError,\n  PoolClosedError,\n  WaitQueueTimeoutError\n} from './errors';\nimport { ConnectionPoolMetrics } from './metrics';\n\n/** @public */\nexport interface ConnectionPoolOptions extends Omit<ConnectionOptions, 'id' | 'generation'> {\n  /** The maximum number of connections that may be associated with a pool at a given time. This includes in use and available connections. */\n  maxPoolSize: number;\n  /** The minimum number of connections that MUST exist at any moment in a single connection pool. */\n  minPoolSize: number;\n  /** The maximum number of connections that may be in the process of being established concurrently by the connection pool. */\n  maxConnecting: number;\n  /** The maximum amount of time a connection should remain idle in the connection pool before being marked idle. */\n  maxIdleTimeMS: number;\n  /** The maximum amount of time operation execution should wait for a connection to become available. The default is 0 which means there is no limit. */\n  waitQueueTimeoutMS: number;\n  /** If we are in load balancer mode. */\n  loadBalanced: boolean;\n  /** @internal */\n  minPoolSizeCheckFrequencyMS?: number;\n}\n\n/** @internal */\nexport interface WaitQueueMember {\n  resolve: (conn: Connection) => void;\n  reject: (err: AnyError) => void;\n  cancelled: boolean;\n  checkoutTime: number;\n}\n\n/** @internal */\nexport const PoolState = Object.freeze({\n  paused: 'paused',\n  ready: 'ready',\n  closed: 'closed'\n} as const);\n\ntype PoolState = (typeof PoolState)[keyof typeof PoolState];\n\n/**\n * @public\n * @deprecated This interface is deprecated and will be removed in a future release as it is not used\n * in the driver\n */\nexport interface CloseOptions {\n  force?: boolean;\n}\n\n/** @public */\nexport type ConnectionPoolEvents = {\n  connectionPoolCreated(event: ConnectionPoolCreatedEvent): void;\n  connectionPoolReady(event: ConnectionPoolReadyEvent): void;\n  connectionPoolClosed(event: ConnectionPoolClosedEvent): void;\n  connectionPoolCleared(event: ConnectionPoolClearedEvent): void;\n  connectionCreated(event: ConnectionCreatedEvent): void;\n  connectionReady(event: ConnectionReadyEvent): void;\n  connectionClosed(event: ConnectionClosedEvent): void;\n  connectionCheckOutStarted(event: ConnectionCheckOutStartedEvent): void;\n  connectionCheckOutFailed(event: ConnectionCheckOutFailedEvent): void;\n  connectionCheckedOut(event: ConnectionCheckedOutEvent): void;\n  connectionCheckedIn(event: ConnectionCheckedInEvent): void;\n} & Omit<ConnectionEvents, 'close' | 'message'>;\n\n/**\n * A pool of connections which dynamically resizes, and emit events related to pool activity\n * @internal\n */\nexport class ConnectionPool extends TypedEventEmitter<ConnectionPoolEvents> {\n  public options: Readonly<ConnectionPoolOptions>;\n  /**  An integer representing the SDAM generation of the pool */\n  public generation: number;\n  /** A map of generations to service ids */\n  public serviceGenerations: Map<string, number>;\n\n  private poolState: PoolState;\n  private server: Server;\n  private connections: List<Connection>;\n  private pending: number;\n  private checkedOut: Set<Connection>;\n  private minPoolSizeTimer?: NodeJS.Timeout;\n  private connectionCounter: Generator<number>;\n  private cancellationToken: CancellationToken;\n  private waitQueue: List<WaitQueueMember>;\n  private metrics: ConnectionPoolMetrics;\n  private processingWaitQueue: boolean;\n\n  /**\n   * Emitted when the connection pool is created.\n   * @event\n   */\n  static readonly CONNECTION_POOL_CREATED = CONNECTION_POOL_CREATED;\n  /**\n   * Emitted once when the connection pool is closed\n   * @event\n   */\n  static readonly CONNECTION_POOL_CLOSED = CONNECTION_POOL_CLOSED;\n  /**\n   * Emitted each time the connection pool is cleared and it's generation incremented\n   * @event\n   */\n  static readonly CONNECTION_POOL_CLEARED = CONNECTION_POOL_CLEARED;\n  /**\n   * Emitted each time the connection pool is marked ready\n   * @event\n   */\n  static readonly CONNECTION_POOL_READY = CONNECTION_POOL_READY;\n  /**\n   * Emitted when a connection is created.\n   * @event\n   */\n  static readonly CONNECTION_CREATED = CONNECTION_CREATED;\n  /**\n   * Emitted when a connection becomes established, and is ready to use\n   * @event\n   */\n  static readonly CONNECTION_READY = CONNECTION_READY;\n  /**\n   * Emitted when a connection is closed\n   * @event\n   */\n  static readonly CONNECTION_CLOSED = CONNECTION_CLOSED;\n  /**\n   * Emitted when an attempt to check out a connection begins\n   * @event\n   */\n  static readonly CONNECTION_CHECK_OUT_STARTED = CONNECTION_CHECK_OUT_STARTED;\n  /**\n   * Emitted when an attempt to check out a connection fails\n   * @event\n   */\n  static readonly CONNECTION_CHECK_OUT_FAILED = CONNECTION_CHECK_OUT_FAILED;\n  /**\n   * Emitted each time a connection is successfully checked out of the connection pool\n   * @event\n   */\n  static readonly CONNECTION_CHECKED_OUT = CONNECTION_CHECKED_OUT;\n  /**\n   * Emitted each time a connection is successfully checked into the connection pool\n   * @event\n   */\n  static readonly CONNECTION_CHECKED_IN = CONNECTION_CHECKED_IN;\n\n  constructor(server: Server, options: ConnectionPoolOptions) {\n    super();\n    this.on('error', noop);\n\n    this.options = Object.freeze({\n      connectionType: Connection,\n      ...options,\n      maxPoolSize: options.maxPoolSize ?? 100,\n      minPoolSize: options.minPoolSize ?? 0,\n      maxConnecting: options.maxConnecting ?? 2,\n      maxIdleTimeMS: options.maxIdleTimeMS ?? 0,\n      waitQueueTimeoutMS: options.waitQueueTimeoutMS ?? 0,\n      minPoolSizeCheckFrequencyMS: options.minPoolSizeCheckFrequencyMS ?? 100,\n      autoEncrypter: options.autoEncrypter\n    });\n\n    if (this.options.minPoolSize > this.options.maxPoolSize) {\n      throw new MongoInvalidArgumentError(\n        'Connection pool minimum size must not be greater than maximum pool size'\n      );\n    }\n\n    this.poolState = PoolState.paused;\n    this.server = server;\n    this.connections = new List();\n    this.pending = 0;\n    this.checkedOut = new Set();\n    this.minPoolSizeTimer = undefined;\n    this.generation = 0;\n    this.serviceGenerations = new Map();\n    this.connectionCounter = makeCounter(1);\n    this.cancellationToken = new CancellationToken();\n    this.cancellationToken.setMaxListeners(Infinity);\n    this.waitQueue = new List();\n    this.metrics = new ConnectionPoolMetrics();\n    this.processingWaitQueue = false;\n\n    this.mongoLogger = this.server.topology.client?.mongoLogger;\n    this.component = 'connection';\n\n    process.nextTick(() => {\n      this.emitAndLog(ConnectionPool.CONNECTION_POOL_CREATED, new ConnectionPoolCreatedEvent(this));\n    });\n  }\n\n  /** The address of the endpoint the pool is connected to */\n  get address(): string {\n    return this.options.hostAddress.toString();\n  }\n\n  /**\n   * Check if the pool has been closed\n   *\n   * TODO(NODE-3263): We can remove this property once shell no longer needs it\n   */\n  get closed(): boolean {\n    return this.poolState === PoolState.closed;\n  }\n\n  /** An integer expressing how many total connections (available + pending + in use) the pool currently has */\n  get totalConnectionCount(): number {\n    return (\n      this.availableConnectionCount + this.pendingConnectionCount + this.currentCheckedOutCount\n    );\n  }\n\n  /** An integer expressing how many connections are currently available in the pool. */\n  get availableConnectionCount(): number {\n    return this.connections.length;\n  }\n\n  get pendingConnectionCount(): number {\n    return this.pending;\n  }\n\n  get currentCheckedOutCount(): number {\n    return this.checkedOut.size;\n  }\n\n  get waitQueueSize(): number {\n    return this.waitQueue.length;\n  }\n\n  get loadBalanced(): boolean {\n    return this.options.loadBalanced;\n  }\n\n  get serverError() {\n    return this.server.description.error;\n  }\n\n  /**\n   * This is exposed ONLY for use in mongosh, to enable\n   * killing all connections if a user quits the shell with\n   * operations in progress.\n   *\n   * This property may be removed as a part of NODE-3263.\n   */\n  get checkedOutConnections() {\n    return this.checkedOut;\n  }\n\n  /**\n   * Get the metrics information for the pool when a wait queue timeout occurs.\n   */\n  private waitQueueErrorMetrics(): string {\n    return this.metrics.info(this.options.maxPoolSize);\n  }\n\n  /**\n   * Set the pool state to \"ready\"\n   */\n  ready(): void {\n    if (this.poolState !== PoolState.paused) {\n      return;\n    }\n    this.poolState = PoolState.ready;\n    this.emitAndLog(ConnectionPool.CONNECTION_POOL_READY, new ConnectionPoolReadyEvent(this));\n    clearTimeout(this.minPoolSizeTimer);\n    this.ensureMinPoolSize();\n  }\n\n  /**\n   * Check a connection out of this pool. The connection will continue to be tracked, but no reference to it\n   * will be held by the pool. This means that if a connection is checked out it MUST be checked back in or\n   * explicitly destroyed by the new owner.\n   */\n  async checkOut(options: { timeoutContext: TimeoutContext } & Abortable): Promise<Connection> {\n    const checkoutTime = now();\n    this.emitAndLog(\n      ConnectionPool.CONNECTION_CHECK_OUT_STARTED,\n      new ConnectionCheckOutStartedEvent(this)\n    );\n\n    const { promise, resolve, reject } = promiseWithResolvers<Connection>();\n\n    const timeout = options.timeoutContext.connectionCheckoutTimeout;\n\n    const waitQueueMember: WaitQueueMember = {\n      resolve,\n      reject,\n      cancelled: false,\n      checkoutTime\n    };\n\n    const abortListener = addAbortListener(options.signal, function () {\n      waitQueueMember.cancelled = true;\n      reject(this.reason);\n    });\n\n    this.waitQueue.push(waitQueueMember);\n    process.nextTick(() => this.processWaitQueue());\n\n    try {\n      timeout?.throwIfExpired();\n      return await (timeout ? Promise.race([promise, timeout]) : promise);\n    } catch (error) {\n      if (TimeoutError.is(error)) {\n        timeout?.clear();\n        waitQueueMember.cancelled = true;\n\n        this.emitAndLog(\n          ConnectionPool.CONNECTION_CHECK_OUT_FAILED,\n          new ConnectionCheckOutFailedEvent(this, 'timeout', waitQueueMember.checkoutTime)\n        );\n        const timeoutError = new WaitQueueTimeoutError(\n          this.loadBalanced\n            ? this.waitQueueErrorMetrics()\n            : 'Timed out while checking out a connection from connection pool',\n          this.address\n        );\n        if (options.timeoutContext.csotEnabled()) {\n          throw new MongoOperationTimeoutError('Timed out during connection checkout', {\n            cause: timeoutError\n          });\n        }\n        throw timeoutError;\n      }\n      throw error;\n    } finally {\n      abortListener?.[kDispose]();\n      timeout?.clear();\n    }\n  }\n\n  /**\n   * Check a connection into the pool.\n   *\n   * @param connection - The connection to check in\n   */\n  checkIn(connection: Connection): void {\n    if (!this.checkedOut.has(connection)) {\n      return;\n    }\n    const poolClosed = this.closed;\n    const stale = this.connectionIsStale(connection);\n    const willDestroy = !!(poolClosed || stale || connection.closed);\n\n    if (!willDestroy) {\n      connection.markAvailable();\n      this.connections.unshift(connection);\n    }\n\n    this.checkedOut.delete(connection);\n    this.emitAndLog(\n      ConnectionPool.CONNECTION_CHECKED_IN,\n      new ConnectionCheckedInEvent(this, connection)\n    );\n\n    if (willDestroy) {\n      const reason = connection.closed ? 'error' : poolClosed ? 'poolClosed' : 'stale';\n      this.destroyConnection(connection, reason);\n    }\n\n    process.nextTick(() => this.processWaitQueue());\n  }\n\n  /**\n   * Clear the pool\n   *\n   * Pool reset is handled by incrementing the pool's generation count. Any existing connection of a\n   * previous generation will eventually be pruned during subsequent checkouts.\n   */\n  clear(options: { serviceId?: ObjectId; interruptInUseConnections?: boolean } = {}): void {\n    if (this.closed) {\n      return;\n    }\n\n    // handle load balanced case\n    if (this.loadBalanced) {\n      const { serviceId } = options;\n      if (!serviceId) {\n        throw new MongoRuntimeError(\n          'ConnectionPool.clear() called in load balanced mode with no serviceId.'\n        );\n      }\n      const sid = serviceId.toHexString();\n      const generation = this.serviceGenerations.get(sid);\n      // Only need to worry if the generation exists, since it should\n      // always be there but typescript needs the check.\n      if (generation == null) {\n        throw new MongoRuntimeError('Service generations are required in load balancer mode.');\n      } else {\n        // Increment the generation for the service id.\n        this.serviceGenerations.set(sid, generation + 1);\n      }\n      this.emitAndLog(\n        ConnectionPool.CONNECTION_POOL_CLEARED,\n        new ConnectionPoolClearedEvent(this, { serviceId })\n      );\n      return;\n    }\n    // handle non load-balanced case\n    const interruptInUseConnections = options.interruptInUseConnections ?? false;\n    const oldGeneration = this.generation;\n    this.generation += 1;\n    const alreadyPaused = this.poolState === PoolState.paused;\n    this.poolState = PoolState.paused;\n\n    this.clearMinPoolSizeTimer();\n    if (!alreadyPaused) {\n      this.emitAndLog(\n        ConnectionPool.CONNECTION_POOL_CLEARED,\n        new ConnectionPoolClearedEvent(this, {\n          interruptInUseConnections\n        })\n      );\n    }\n\n    if (interruptInUseConnections) {\n      process.nextTick(() => this.interruptInUseConnections(oldGeneration));\n    }\n\n    this.processWaitQueue();\n  }\n\n  /**\n   * Closes all stale in-use connections in the pool with a resumable PoolClearedOnNetworkError.\n   *\n   * Only connections where `connection.generation <= minGeneration` are killed.\n   */\n  private interruptInUseConnections(minGeneration: number) {\n    for (const connection of this.checkedOut) {\n      if (connection.generation <= minGeneration) {\n        connection.onError(new PoolClearedOnNetworkError(this));\n        this.checkIn(connection);\n      }\n    }\n  }\n\n  /** Close the pool */\n  close(): void {\n    if (this.closed) {\n      return;\n    }\n\n    // immediately cancel any in-flight connections\n    this.cancellationToken.emit('cancel');\n\n    // end the connection counter\n    if (typeof this.connectionCounter.return === 'function') {\n      this.connectionCounter.return(undefined);\n    }\n\n    this.poolState = PoolState.closed;\n    this.clearMinPoolSizeTimer();\n    this.processWaitQueue();\n\n    for (const conn of this.connections) {\n      this.emitAndLog(\n        ConnectionPool.CONNECTION_CLOSED,\n        new ConnectionClosedEvent(this, conn, 'poolClosed')\n      );\n      conn.destroy();\n    }\n    this.connections.clear();\n    this.emitAndLog(ConnectionPool.CONNECTION_POOL_CLOSED, new ConnectionPoolClosedEvent(this));\n  }\n\n  /**\n   * @internal\n   * Reauthenticate a connection\n   */\n  async reauthenticate(connection: Connection): Promise<void> {\n    const authContext = connection.authContext;\n    if (!authContext) {\n      throw new MongoRuntimeError('No auth context found on connection.');\n    }\n    const credentials = authContext.credentials;\n    if (!credentials) {\n      throw new MongoMissingCredentialsError(\n        'Connection is missing credentials when asked to reauthenticate'\n      );\n    }\n\n    const resolvedCredentials = credentials.resolveAuthMechanism(connection.hello);\n    const provider = this.server.topology.client.s.authProviders.getOrCreateProvider(\n      resolvedCredentials.mechanism,\n      resolvedCredentials.mechanismProperties\n    );\n\n    if (!provider) {\n      throw new MongoMissingCredentialsError(\n        `Reauthenticate failed due to no auth provider for ${credentials.mechanism}`\n      );\n    }\n\n    await provider.reauth(authContext);\n\n    return;\n  }\n\n  /** Clear the min pool size timer */\n  private clearMinPoolSizeTimer(): void {\n    const minPoolSizeTimer = this.minPoolSizeTimer;\n    if (minPoolSizeTimer) {\n      clearTimeout(minPoolSizeTimer);\n    }\n  }\n\n  private destroyConnection(\n    connection: Connection,\n    reason: 'error' | 'idle' | 'stale' | 'poolClosed'\n  ) {\n    this.emitAndLog(\n      ConnectionPool.CONNECTION_CLOSED,\n      new ConnectionClosedEvent(this, connection, reason)\n    );\n    // destroy the connection\n    connection.destroy();\n  }\n\n  private connectionIsStale(connection: Connection) {\n    const serviceId = connection.serviceId;\n    if (this.loadBalanced && serviceId) {\n      const sid = serviceId.toHexString();\n      const generation = this.serviceGenerations.get(sid);\n      return connection.generation !== generation;\n    }\n\n    return connection.generation !== this.generation;\n  }\n\n  private connectionIsIdle(connection: Connection) {\n    return !!(this.options.maxIdleTimeMS && connection.idleTime > this.options.maxIdleTimeMS);\n  }\n\n  /**\n   * Destroys a connection if the connection is perished.\n   *\n   * @returns `true` if the connection was destroyed, `false` otherwise.\n   */\n  private destroyConnectionIfPerished(connection: Connection): boolean {\n    const isStale = this.connectionIsStale(connection);\n    const isIdle = this.connectionIsIdle(connection);\n    if (!isStale && !isIdle && !connection.closed) {\n      return false;\n    }\n    const reason = connection.closed ? 'error' : isStale ? 'stale' : 'idle';\n    this.destroyConnection(connection, reason);\n    return true;\n  }\n\n  private createConnection(callback: Callback<Connection>) {\n    const connectOptions: ConnectionOptions = {\n      ...this.options,\n      id: this.connectionCounter.next().value,\n      generation: this.generation,\n      cancellationToken: this.cancellationToken,\n      mongoLogger: this.mongoLogger,\n      authProviders: this.server.topology.client.s.authProviders\n    };\n\n    this.pending++;\n    // This is our version of a \"virtual\" no-I/O connection as the spec requires\n    const connectionCreatedTime = now();\n    this.emitAndLog(\n      ConnectionPool.CONNECTION_CREATED,\n      new ConnectionCreatedEvent(this, { id: connectOptions.id })\n    );\n\n    connect(connectOptions).then(\n      connection => {\n        // The pool might have closed since we started trying to create a connection\n        if (this.poolState !== PoolState.ready) {\n          this.pending--;\n          connection.destroy();\n          callback(this.closed ? new PoolClosedError(this) : new PoolClearedError(this));\n          return;\n        }\n\n        // forward all events from the connection to the pool\n        for (const event of [...APM_EVENTS, Connection.CLUSTER_TIME_RECEIVED]) {\n          connection.on(event, (e: any) => this.emit(event, e));\n        }\n\n        if (this.loadBalanced) {\n          connection.on(Connection.PINNED, pinType => this.metrics.markPinned(pinType));\n          connection.on(Connection.UNPINNED, pinType => this.metrics.markUnpinned(pinType));\n\n          const serviceId = connection.serviceId;\n          if (serviceId) {\n            let generation;\n            const sid = serviceId.toHexString();\n            if ((generation = this.serviceGenerations.get(sid))) {\n              connection.generation = generation;\n            } else {\n              this.serviceGenerations.set(sid, 0);\n              connection.generation = 0;\n            }\n          }\n        }\n\n        connection.markAvailable();\n        this.emitAndLog(\n          ConnectionPool.CONNECTION_READY,\n          new ConnectionReadyEvent(this, connection, connectionCreatedTime)\n        );\n\n        this.pending--;\n        callback(undefined, connection);\n      },\n      error => {\n        this.pending--;\n        this.server.handleError(error);\n        this.emitAndLog(\n          ConnectionPool.CONNECTION_CLOSED,\n          new ConnectionClosedEvent(\n            this,\n            { id: connectOptions.id, serviceId: undefined },\n            'error',\n            // TODO(NODE-5192): Remove this cast\n            error as MongoError\n          )\n        );\n        if (error instanceof MongoNetworkError || error instanceof MongoServerError) {\n          error.connectionGeneration = connectOptions.generation;\n        }\n        callback(error ?? new MongoRuntimeError('Connection creation failed without error'));\n      }\n    );\n  }\n\n  private ensureMinPoolSize() {\n    const minPoolSize = this.options.minPoolSize;\n    if (this.poolState !== PoolState.ready || minPoolSize === 0) {\n      return;\n    }\n\n    this.connections.prune(connection => this.destroyConnectionIfPerished(connection));\n\n    if (\n      this.totalConnectionCount < minPoolSize &&\n      this.pendingConnectionCount < this.options.maxConnecting\n    ) {\n      // NOTE: ensureMinPoolSize should not try to get all the pending\n      // connection permits because that potentially delays the availability of\n      // the connection to a checkout request\n      this.createConnection((err, connection) => {\n        if (!err && connection) {\n          this.connections.push(connection);\n          process.nextTick(() => this.processWaitQueue());\n        }\n        if (this.poolState === PoolState.ready) {\n          clearTimeout(this.minPoolSizeTimer);\n          this.minPoolSizeTimer = setTimeout(\n            () => this.ensureMinPoolSize(),\n            this.options.minPoolSizeCheckFrequencyMS\n          );\n        }\n      });\n    } else {\n      clearTimeout(this.minPoolSizeTimer);\n      this.minPoolSizeTimer = setTimeout(\n        () => this.ensureMinPoolSize(),\n        this.options.minPoolSizeCheckFrequencyMS\n      );\n    }\n  }\n\n  private processWaitQueue() {\n    if (this.processingWaitQueue) {\n      return;\n    }\n    this.processingWaitQueue = true;\n\n    while (this.waitQueueSize) {\n      const waitQueueMember = this.waitQueue.first();\n      if (!waitQueueMember) {\n        this.waitQueue.shift();\n        continue;\n      }\n\n      if (waitQueueMember.cancelled) {\n        this.waitQueue.shift();\n        continue;\n      }\n\n      if (this.poolState !== PoolState.ready) {\n        const reason = this.closed ? 'poolClosed' : 'connectionError';\n        const error = this.closed ? new PoolClosedError(this) : new PoolClearedError(this);\n        this.emitAndLog(\n          ConnectionPool.CONNECTION_CHECK_OUT_FAILED,\n          new ConnectionCheckOutFailedEvent(this, reason, waitQueueMember.checkoutTime, error)\n        );\n        this.waitQueue.shift();\n        waitQueueMember.reject(error);\n        continue;\n      }\n\n      if (!this.availableConnectionCount) {\n        break;\n      }\n\n      const connection = this.connections.shift();\n      if (!connection) {\n        break;\n      }\n\n      if (!this.destroyConnectionIfPerished(connection)) {\n        this.checkedOut.add(connection);\n        this.emitAndLog(\n          ConnectionPool.CONNECTION_CHECKED_OUT,\n          new ConnectionCheckedOutEvent(this, connection, waitQueueMember.checkoutTime)\n        );\n\n        this.waitQueue.shift();\n        waitQueueMember.resolve(connection);\n      }\n    }\n\n    const { maxPoolSize, maxConnecting } = this.options;\n    while (\n      this.waitQueueSize > 0 &&\n      this.pendingConnectionCount < maxConnecting &&\n      (maxPoolSize === 0 || this.totalConnectionCount < maxPoolSize)\n    ) {\n      const waitQueueMember = this.waitQueue.shift();\n      if (!waitQueueMember || waitQueueMember.cancelled) {\n        continue;\n      }\n      this.createConnection((err, connection) => {\n        if (waitQueueMember.cancelled) {\n          if (!err && connection) {\n            this.connections.push(connection);\n          }\n        } else {\n          if (err) {\n            this.emitAndLog(\n              ConnectionPool.CONNECTION_CHECK_OUT_FAILED,\n              // TODO(NODE-5192): Remove this cast\n              new ConnectionCheckOutFailedEvent(\n                this,\n                'connectionError',\n                waitQueueMember.checkoutTime,\n                err as MongoError\n              )\n            );\n            waitQueueMember.reject(err);\n          } else if (connection) {\n            this.checkedOut.add(connection);\n            this.emitAndLog(\n              ConnectionPool.CONNECTION_CHECKED_OUT,\n              new ConnectionCheckedOutEvent(this, connection, waitQueueMember.checkoutTime)\n            );\n            waitQueueMember.resolve(connection);\n          }\n        }\n        process.nextTick(() => this.processWaitQueue());\n      });\n    }\n    this.processingWaitQueue = false;\n  }\n}\n\n/**\n * A callback provided to `withConnection`\n * @internal\n *\n * @param error - An error instance representing the error during the execution.\n * @param connection - The managed connection which was checked out of the pool.\n * @param callback - A function to call back after connection management is complete\n */\nexport type WithConnectionCallback = (\n  error: MongoError | undefined,\n  connection: Connection | undefined,\n  callback: Callback<Connection>\n) => void;\n"], "mappings": ";;;;;;AAAA,MAAAA,QAAA,GAAAC,OAAA;AAGA,MAAAC,WAAA,GAAAD,OAAA;AAcA,MAAAE,OAAA,GAAAF,OAAA;AAUA,MAAAG,aAAA,GAAAH,OAAA;AAEA,MAAAI,SAAA,GAAAJ,OAAA;AACA,MAAAK,OAAA,GAAAL,OAAA;AAUA,MAAAM,SAAA,GAAAN,OAAA;AACA,MAAAO,YAAA,GAAAP,OAAA;AACA,MAAAQ,wBAAA,GAAAR,OAAA;AAaA,MAAAS,QAAA,GAAAT,OAAA;AAMA,MAAAU,SAAA,GAAAV,OAAA;AA4BA;AACaW,OAAA,CAAAC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC;EACrCC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE,OAAO;EACdC,MAAM,EAAE;CACA,CAAC;AA4BX;;;;AAIA,MAAaC,cAAe,SAAQf,aAAA,CAAAgB,iBAAuC;EA2EzEC,YAAYC,MAAc,EAAEC,OAA8B;IACxD,KAAK,EAAE;IACP,IAAI,CAACC,EAAE,CAAC,OAAO,EAAElB,OAAA,CAAAmB,IAAI,CAAC;IAEtB,IAAI,CAACF,OAAO,GAAGT,MAAM,CAACC,MAAM,CAAC;MAC3BW,cAAc,EAAElB,YAAA,CAAAmB,UAAU;MAC1B,GAAGJ,OAAO;MACVK,WAAW,EAAEL,OAAO,CAACK,WAAW,IAAI,GAAG;MACvCC,WAAW,EAAEN,OAAO,CAACM,WAAW,IAAI,CAAC;MACrCC,aAAa,EAAEP,OAAO,CAACO,aAAa,IAAI,CAAC;MACzCC,aAAa,EAAER,OAAO,CAACQ,aAAa,IAAI,CAAC;MACzCC,kBAAkB,EAAET,OAAO,CAACS,kBAAkB,IAAI,CAAC;MACnDC,2BAA2B,EAAEV,OAAO,CAACU,2BAA2B,IAAI,GAAG;MACvEC,aAAa,EAAEX,OAAO,CAACW;KACxB,CAAC;IAEF,IAAI,IAAI,CAACX,OAAO,CAACM,WAAW,GAAG,IAAI,CAACN,OAAO,CAACK,WAAW,EAAE;MACvD,MAAM,IAAIzB,OAAA,CAAAgC,yBAAyB,CACjC,yEAAyE,CAC1E;IACH;IAEA,IAAI,CAACC,SAAS,GAAGxB,OAAA,CAAAC,SAAS,CAACG,MAAM;IACjC,IAAI,CAACM,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACe,WAAW,GAAG,IAAI/B,OAAA,CAAAgC,IAAI,EAAE;IAC7B,IAAI,CAACC,OAAO,GAAG,CAAC;IAChB,IAAI,CAACC,UAAU,GAAG,IAAIC,GAAG,EAAE;IAC3B,IAAI,CAACC,gBAAgB,GAAGC,SAAS;IACjC,IAAI,CAACC,UAAU,GAAG,CAAC;IACnB,IAAI,CAACC,kBAAkB,GAAG,IAAIC,GAAG,EAAE;IACnC,IAAI,CAACC,iBAAiB,GAAG,IAAAzC,OAAA,CAAA0C,WAAW,EAAC,CAAC,CAAC;IACvC,IAAI,CAACC,iBAAiB,GAAG,IAAI7C,aAAA,CAAA8C,iBAAiB,EAAE;IAChD,IAAI,CAACD,iBAAiB,CAACE,eAAe,CAACC,QAAQ,CAAC;IAChD,IAAI,CAACC,SAAS,GAAG,IAAI/C,OAAA,CAAAgC,IAAI,EAAE;IAC3B,IAAI,CAACgB,OAAO,GAAG,IAAI3C,SAAA,CAAA4C,qBAAqB,EAAE;IAC1C,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAEhC,IAAI,CAACC,WAAW,GAAG,IAAI,CAACnC,MAAM,CAACoC,QAAQ,CAACC,MAAM,EAAEF,WAAW;IAC3D,IAAI,CAACG,SAAS,GAAG,YAAY;IAE7BC,OAAO,CAACC,QAAQ,CAAC,MAAK;MACpB,IAAI,CAACC,UAAU,CAAC5C,cAAc,CAAC6C,uBAAuB,EAAE,IAAIvD,wBAAA,CAAAwD,0BAA0B,CAAC,IAAI,CAAC,CAAC;IAC/F,CAAC,CAAC;EACJ;EAEA;EACA,IAAIC,OAAOA,CAAA;IACT,OAAO,IAAI,CAAC3C,OAAO,CAAC4C,WAAW,CAACC,QAAQ,EAAE;EAC5C;EAEA;;;;;EAKA,IAAIlD,MAAMA,CAAA;IACR,OAAO,IAAI,CAACkB,SAAS,KAAKxB,OAAA,CAAAC,SAAS,CAACK,MAAM;EAC5C;EAEA;EACA,IAAImD,oBAAoBA,CAAA;IACtB,OACE,IAAI,CAACC,wBAAwB,GAAG,IAAI,CAACC,sBAAsB,GAAG,IAAI,CAACC,sBAAsB;EAE7F;EAEA;EACA,IAAIF,wBAAwBA,CAAA;IAC1B,OAAO,IAAI,CAACjC,WAAW,CAACoC,MAAM;EAChC;EAEA,IAAIF,sBAAsBA,CAAA;IACxB,OAAO,IAAI,CAAChC,OAAO;EACrB;EAEA,IAAIiC,sBAAsBA,CAAA;IACxB,OAAO,IAAI,CAAChC,UAAU,CAACkC,IAAI;EAC7B;EAEA,IAAIC,aAAaA,CAAA;IACf,OAAO,IAAI,CAACtB,SAAS,CAACoB,MAAM;EAC9B;EAEA,IAAIG,YAAYA,CAAA;IACd,OAAO,IAAI,CAACrD,OAAO,CAACqD,YAAY;EAClC;EAEA,IAAIC,WAAWA,CAAA;IACb,OAAO,IAAI,CAACvD,MAAM,CAACwD,WAAW,CAACC,KAAK;EACtC;EAEA;;;;;;;EAOA,IAAIC,qBAAqBA,CAAA;IACvB,OAAO,IAAI,CAACxC,UAAU;EACxB;EAEA;;;EAGQyC,qBAAqBA,CAAA;IAC3B,OAAO,IAAI,CAAC3B,OAAO,CAAC4B,IAAI,CAAC,IAAI,CAAC3D,OAAO,CAACK,WAAW,CAAC;EACpD;EAEA;;;EAGAX,KAAKA,CAAA;IACH,IAAI,IAAI,CAACmB,SAAS,KAAKxB,OAAA,CAAAC,SAAS,CAACG,MAAM,EAAE;MACvC;IACF;IACA,IAAI,CAACoB,SAAS,GAAGxB,OAAA,CAAAC,SAAS,CAACI,KAAK;IAChC,IAAI,CAAC8C,UAAU,CAAC5C,cAAc,CAACgE,qBAAqB,EAAE,IAAI1E,wBAAA,CAAA2E,wBAAwB,CAAC,IAAI,CAAC,CAAC;IACzF,IAAApF,QAAA,CAAAqF,YAAY,EAAC,IAAI,CAAC3C,gBAAgB,CAAC;IACnC,IAAI,CAAC4C,iBAAiB,EAAE;EAC1B;EAEA;;;;;EAKA,MAAMC,QAAQA,CAAChE,OAAuD;IACpE,MAAMiE,YAAY,GAAG,IAAAlF,OAAA,CAAAmF,GAAG,GAAE;IAC1B,IAAI,CAAC1B,UAAU,CACb5C,cAAc,CAACuE,4BAA4B,EAC3C,IAAIjF,wBAAA,CAAAkF,8BAA8B,CAAC,IAAI,CAAC,CACzC;IAED,MAAM;MAAEC,OAAO;MAAEC,OAAO;MAAEC;IAAM,CAAE,GAAG,IAAAxF,OAAA,CAAAyF,oBAAoB,GAAc;IAEvE,MAAMC,OAAO,GAAGzE,OAAO,CAAC0E,cAAc,CAACC,yBAAyB;IAEhE,MAAMC,eAAe,GAAoB;MACvCN,OAAO;MACPC,MAAM;MACNM,SAAS,EAAE,KAAK;MAChBZ;KACD;IAED,MAAMa,aAAa,GAAG,IAAA/F,OAAA,CAAAgG,gBAAgB,EAAC/E,OAAO,CAACgF,MAAM,EAAE;MACrDJ,eAAe,CAACC,SAAS,GAAG,IAAI;MAChCN,MAAM,CAAC,IAAI,CAACU,MAAM,CAAC;IACrB,CAAC,CAAC;IAEF,IAAI,CAACnD,SAAS,CAACoD,IAAI,CAACN,eAAe,CAAC;IACpCtC,OAAO,CAACC,QAAQ,CAAC,MAAM,IAAI,CAAC4C,gBAAgB,EAAE,CAAC;IAE/C,IAAI;MACFV,OAAO,EAAEW,cAAc,EAAE;MACzB,OAAO,OAAOX,OAAO,GAAGY,OAAO,CAACC,IAAI,CAAC,CAACjB,OAAO,EAAEI,OAAO,CAAC,CAAC,GAAGJ,OAAO,CAAC;IACrE,CAAC,CAAC,OAAOb,KAAK,EAAE;MACd,IAAI1E,SAAA,CAAAyG,YAAY,CAACC,EAAE,CAAChC,KAAK,CAAC,EAAE;QAC1BiB,OAAO,EAAEgB,KAAK,EAAE;QAChBb,eAAe,CAACC,SAAS,GAAG,IAAI;QAEhC,IAAI,CAACrC,UAAU,CACb5C,cAAc,CAAC8F,2BAA2B,EAC1C,IAAIxG,wBAAA,CAAAyG,6BAA6B,CAAC,IAAI,EAAE,SAAS,EAAEf,eAAe,CAACX,YAAY,CAAC,CACjF;QACD,MAAM2B,YAAY,GAAG,IAAIzG,QAAA,CAAA0G,qBAAqB,CAC5C,IAAI,CAACxC,YAAY,GACb,IAAI,CAACK,qBAAqB,EAAE,GAC5B,gEAAgE,EACpE,IAAI,CAACf,OAAO,CACb;QACD,IAAI3C,OAAO,CAAC0E,cAAc,CAACoB,WAAW,EAAE,EAAE;UACxC,MAAM,IAAIlH,OAAA,CAAAmH,0BAA0B,CAAC,sCAAsC,EAAE;YAC3EC,KAAK,EAAEJ;WACR,CAAC;QACJ;QACA,MAAMA,YAAY;MACpB;MACA,MAAMpC,KAAK;IACb,CAAC,SAAS;MACRsB,aAAa,GAAG/F,OAAA,CAAAkH,QAAQ,CAAC,EAAE;MAC3BxB,OAAO,EAAEgB,KAAK,EAAE;IAClB;EACF;EAEA;;;;;EAKAS,OAAOA,CAACC,UAAsB;IAC5B,IAAI,CAAC,IAAI,CAAClF,UAAU,CAACmF,GAAG,CAACD,UAAU,CAAC,EAAE;MACpC;IACF;IACA,MAAME,UAAU,GAAG,IAAI,CAAC1G,MAAM;IAC9B,MAAM2G,KAAK,GAAG,IAAI,CAACC,iBAAiB,CAACJ,UAAU,CAAC;IAChD,MAAMK,WAAW,GAAG,CAAC,EAAEH,UAAU,IAAIC,KAAK,IAAIH,UAAU,CAACxG,MAAM,CAAC;IAEhE,IAAI,CAAC6G,WAAW,EAAE;MAChBL,UAAU,CAACM,aAAa,EAAE;MAC1B,IAAI,CAAC3F,WAAW,CAAC4F,OAAO,CAACP,UAAU,CAAC;IACtC;IAEA,IAAI,CAAClF,UAAU,CAAC0F,MAAM,CAACR,UAAU,CAAC;IAClC,IAAI,CAAC3D,UAAU,CACb5C,cAAc,CAACgH,qBAAqB,EACpC,IAAI1H,wBAAA,CAAA2H,wBAAwB,CAAC,IAAI,EAAEV,UAAU,CAAC,CAC/C;IAED,IAAIK,WAAW,EAAE;MACf,MAAMvB,MAAM,GAAGkB,UAAU,CAACxG,MAAM,GAAG,OAAO,GAAG0G,UAAU,GAAG,YAAY,GAAG,OAAO;MAChF,IAAI,CAACS,iBAAiB,CAACX,UAAU,EAAElB,MAAM,CAAC;IAC5C;IAEA3C,OAAO,CAACC,QAAQ,CAAC,MAAM,IAAI,CAAC4C,gBAAgB,EAAE,CAAC;EACjD;EAEA;;;;;;EAMAM,KAAKA,CAACzF,OAAA,GAAyE,EAAE;IAC/E,IAAI,IAAI,CAACL,MAAM,EAAE;MACf;IACF;IAEA;IACA,IAAI,IAAI,CAAC0D,YAAY,EAAE;MACrB,MAAM;QAAE0D;MAAS,CAAE,GAAG/G,OAAO;MAC7B,IAAI,CAAC+G,SAAS,EAAE;QACd,MAAM,IAAInI,OAAA,CAAAoI,iBAAiB,CACzB,wEAAwE,CACzE;MACH;MACA,MAAMC,GAAG,GAAGF,SAAS,CAACG,WAAW,EAAE;MACnC,MAAM7F,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAAC6F,GAAG,CAACF,GAAG,CAAC;MACnD;MACA;MACA,IAAI5F,UAAU,IAAI,IAAI,EAAE;QACtB,MAAM,IAAIzC,OAAA,CAAAoI,iBAAiB,CAAC,yDAAyD,CAAC;MACxF,CAAC,MAAM;QACL;QACA,IAAI,CAAC1F,kBAAkB,CAAC8F,GAAG,CAACH,GAAG,EAAE5F,UAAU,GAAG,CAAC,CAAC;MAClD;MACA,IAAI,CAACmB,UAAU,CACb5C,cAAc,CAACyH,uBAAuB,EACtC,IAAInI,wBAAA,CAAAoI,0BAA0B,CAAC,IAAI,EAAE;QAAEP;MAAS,CAAE,CAAC,CACpD;MACD;IACF;IACA;IACA,MAAMQ,yBAAyB,GAAGvH,OAAO,CAACuH,yBAAyB,IAAI,KAAK;IAC5E,MAAMC,aAAa,GAAG,IAAI,CAACnG,UAAU;IACrC,IAAI,CAACA,UAAU,IAAI,CAAC;IACpB,MAAMoG,aAAa,GAAG,IAAI,CAAC5G,SAAS,KAAKxB,OAAA,CAAAC,SAAS,CAACG,MAAM;IACzD,IAAI,CAACoB,SAAS,GAAGxB,OAAA,CAAAC,SAAS,CAACG,MAAM;IAEjC,IAAI,CAACiI,qBAAqB,EAAE;IAC5B,IAAI,CAACD,aAAa,EAAE;MAClB,IAAI,CAACjF,UAAU,CACb5C,cAAc,CAACyH,uBAAuB,EACtC,IAAInI,wBAAA,CAAAoI,0BAA0B,CAAC,IAAI,EAAE;QACnCC;OACD,CAAC,CACH;IACH;IAEA,IAAIA,yBAAyB,EAAE;MAC7BjF,OAAO,CAACC,QAAQ,CAAC,MAAM,IAAI,CAACgF,yBAAyB,CAACC,aAAa,CAAC,CAAC;IACvE;IAEA,IAAI,CAACrC,gBAAgB,EAAE;EACzB;EAEA;;;;;EAKQoC,yBAAyBA,CAACI,aAAqB;IACrD,KAAK,MAAMxB,UAAU,IAAI,IAAI,CAAClF,UAAU,EAAE;MACxC,IAAIkF,UAAU,CAAC9E,UAAU,IAAIsG,aAAa,EAAE;QAC1CxB,UAAU,CAACyB,OAAO,CAAC,IAAIzI,QAAA,CAAA0I,yBAAyB,CAAC,IAAI,CAAC,CAAC;QACvD,IAAI,CAAC3B,OAAO,CAACC,UAAU,CAAC;MAC1B;IACF;EACF;EAEA;EACA2B,KAAKA,CAAA;IACH,IAAI,IAAI,CAACnI,MAAM,EAAE;MACf;IACF;IAEA;IACA,IAAI,CAAC+B,iBAAiB,CAACqG,IAAI,CAAC,QAAQ,CAAC;IAErC;IACA,IAAI,OAAO,IAAI,CAACvG,iBAAiB,CAACwG,MAAM,KAAK,UAAU,EAAE;MACvD,IAAI,CAACxG,iBAAiB,CAACwG,MAAM,CAAC5G,SAAS,CAAC;IAC1C;IAEA,IAAI,CAACP,SAAS,GAAGxB,OAAA,CAAAC,SAAS,CAACK,MAAM;IACjC,IAAI,CAAC+H,qBAAqB,EAAE;IAC5B,IAAI,CAACvC,gBAAgB,EAAE;IAEvB,KAAK,MAAM8C,IAAI,IAAI,IAAI,CAACnH,WAAW,EAAE;MACnC,IAAI,CAAC0B,UAAU,CACb5C,cAAc,CAACsI,iBAAiB,EAChC,IAAIhJ,wBAAA,CAAAiJ,qBAAqB,CAAC,IAAI,EAAEF,IAAI,EAAE,YAAY,CAAC,CACpD;MACDA,IAAI,CAACG,OAAO,EAAE;IAChB;IACA,IAAI,CAACtH,WAAW,CAAC2E,KAAK,EAAE;IACxB,IAAI,CAACjD,UAAU,CAAC5C,cAAc,CAACyI,sBAAsB,EAAE,IAAInJ,wBAAA,CAAAoJ,yBAAyB,CAAC,IAAI,CAAC,CAAC;EAC7F;EAEA;;;;EAIA,MAAMC,cAAcA,CAACpC,UAAsB;IACzC,MAAMqC,WAAW,GAAGrC,UAAU,CAACqC,WAAW;IAC1C,IAAI,CAACA,WAAW,EAAE;MAChB,MAAM,IAAI5J,OAAA,CAAAoI,iBAAiB,CAAC,sCAAsC,CAAC;IACrE;IACA,MAAMyB,WAAW,GAAGD,WAAW,CAACC,WAAW;IAC3C,IAAI,CAACA,WAAW,EAAE;MAChB,MAAM,IAAI7J,OAAA,CAAA8J,4BAA4B,CACpC,gEAAgE,CACjE;IACH;IAEA,MAAMC,mBAAmB,GAAGF,WAAW,CAACG,oBAAoB,CAACzC,UAAU,CAAC0C,KAAK,CAAC;IAC9E,MAAMC,QAAQ,GAAG,IAAI,CAAC/I,MAAM,CAACoC,QAAQ,CAACC,MAAM,CAAC2G,CAAC,CAACC,aAAa,CAACC,mBAAmB,CAC9EN,mBAAmB,CAACO,SAAS,EAC7BP,mBAAmB,CAACQ,mBAAmB,CACxC;IAED,IAAI,CAACL,QAAQ,EAAE;MACb,MAAM,IAAIlK,OAAA,CAAA8J,4BAA4B,CACpC,qDAAqDD,WAAW,CAACS,SAAS,EAAE,CAC7E;IACH;IAEA,MAAMJ,QAAQ,CAACM,MAAM,CAACZ,WAAW,CAAC;IAElC;EACF;EAEA;EACQd,qBAAqBA,CAAA;IAC3B,MAAMvG,gBAAgB,GAAG,IAAI,CAACA,gBAAgB;IAC9C,IAAIA,gBAAgB,EAAE;MACpB,IAAA1C,QAAA,CAAAqF,YAAY,EAAC3C,gBAAgB,CAAC;IAChC;EACF;EAEQ2F,iBAAiBA,CACvBX,UAAsB,EACtBlB,MAAiD;IAEjD,IAAI,CAACzC,UAAU,CACb5C,cAAc,CAACsI,iBAAiB,EAChC,IAAIhJ,wBAAA,CAAAiJ,qBAAqB,CAAC,IAAI,EAAEhC,UAAU,EAAElB,MAAM,CAAC,CACpD;IACD;IACAkB,UAAU,CAACiC,OAAO,EAAE;EACtB;EAEQ7B,iBAAiBA,CAACJ,UAAsB;IAC9C,MAAMY,SAAS,GAAGZ,UAAU,CAACY,SAAS;IACtC,IAAI,IAAI,CAAC1D,YAAY,IAAI0D,SAAS,EAAE;MAClC,MAAME,GAAG,GAAGF,SAAS,CAACG,WAAW,EAAE;MACnC,MAAM7F,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAAC6F,GAAG,CAACF,GAAG,CAAC;MACnD,OAAOd,UAAU,CAAC9E,UAAU,KAAKA,UAAU;IAC7C;IAEA,OAAO8E,UAAU,CAAC9E,UAAU,KAAK,IAAI,CAACA,UAAU;EAClD;EAEQgI,gBAAgBA,CAAClD,UAAsB;IAC7C,OAAO,CAAC,EAAE,IAAI,CAACnG,OAAO,CAACQ,aAAa,IAAI2F,UAAU,CAACmD,QAAQ,GAAG,IAAI,CAACtJ,OAAO,CAACQ,aAAa,CAAC;EAC3F;EAEA;;;;;EAKQ+I,2BAA2BA,CAACpD,UAAsB;IACxD,MAAMqD,OAAO,GAAG,IAAI,CAACjD,iBAAiB,CAACJ,UAAU,CAAC;IAClD,MAAMsD,MAAM,GAAG,IAAI,CAACJ,gBAAgB,CAAClD,UAAU,CAAC;IAChD,IAAI,CAACqD,OAAO,IAAI,CAACC,MAAM,IAAI,CAACtD,UAAU,CAACxG,MAAM,EAAE;MAC7C,OAAO,KAAK;IACd;IACA,MAAMsF,MAAM,GAAGkB,UAAU,CAACxG,MAAM,GAAG,OAAO,GAAG6J,OAAO,GAAG,OAAO,GAAG,MAAM;IACvE,IAAI,CAAC1C,iBAAiB,CAACX,UAAU,EAAElB,MAAM,CAAC;IAC1C,OAAO,IAAI;EACb;EAEQyE,gBAAgBA,CAACC,QAA8B;IACrD,MAAMC,cAAc,GAAsB;MACxC,GAAG,IAAI,CAAC5J,OAAO;MACf6J,EAAE,EAAE,IAAI,CAACrI,iBAAiB,CAACsI,IAAI,EAAE,CAACC,KAAK;MACvC1I,UAAU,EAAE,IAAI,CAACA,UAAU;MAC3BK,iBAAiB,EAAE,IAAI,CAACA,iBAAiB;MACzCQ,WAAW,EAAE,IAAI,CAACA,WAAW;MAC7B8G,aAAa,EAAE,IAAI,CAACjJ,MAAM,CAACoC,QAAQ,CAACC,MAAM,CAAC2G,CAAC,CAACC;KAC9C;IAED,IAAI,CAAChI,OAAO,EAAE;IACd;IACA,MAAMgJ,qBAAqB,GAAG,IAAAjL,OAAA,CAAAmF,GAAG,GAAE;IACnC,IAAI,CAAC1B,UAAU,CACb5C,cAAc,CAACqK,kBAAkB,EACjC,IAAI/K,wBAAA,CAAAgL,sBAAsB,CAAC,IAAI,EAAE;MAAEL,EAAE,EAAED,cAAc,CAACC;IAAE,CAAE,CAAC,CAC5D;IAED,IAAA7K,SAAA,CAAAmL,OAAO,EAACP,cAAc,CAAC,CAACQ,IAAI,CAC1BjE,UAAU,IAAG;MACX;MACA,IAAI,IAAI,CAACtF,SAAS,KAAKxB,OAAA,CAAAC,SAAS,CAACI,KAAK,EAAE;QACtC,IAAI,CAACsB,OAAO,EAAE;QACdmF,UAAU,CAACiC,OAAO,EAAE;QACpBuB,QAAQ,CAAC,IAAI,CAAChK,MAAM,GAAG,IAAIR,QAAA,CAAAkL,eAAe,CAAC,IAAI,CAAC,GAAG,IAAIlL,QAAA,CAAAmL,gBAAgB,CAAC,IAAI,CAAC,CAAC;QAC9E;MACF;MAEA;MACA,KAAK,MAAMC,KAAK,IAAI,CAAC,GAAG5L,WAAA,CAAA6L,UAAU,EAAEvL,YAAA,CAAAmB,UAAU,CAACqK,qBAAqB,CAAC,EAAE;QACrEtE,UAAU,CAAClG,EAAE,CAACsK,KAAK,EAAGG,CAAM,IAAK,IAAI,CAAC3C,IAAI,CAACwC,KAAK,EAAEG,CAAC,CAAC,CAAC;MACvD;MAEA,IAAI,IAAI,CAACrH,YAAY,EAAE;QACrB8C,UAAU,CAAClG,EAAE,CAAChB,YAAA,CAAAmB,UAAU,CAACuK,MAAM,EAAEC,OAAO,IAAI,IAAI,CAAC7I,OAAO,CAAC8I,UAAU,CAACD,OAAO,CAAC,CAAC;QAC7EzE,UAAU,CAAClG,EAAE,CAAChB,YAAA,CAAAmB,UAAU,CAAC0K,QAAQ,EAAEF,OAAO,IAAI,IAAI,CAAC7I,OAAO,CAACgJ,YAAY,CAACH,OAAO,CAAC,CAAC;QAEjF,MAAM7D,SAAS,GAAGZ,UAAU,CAACY,SAAS;QACtC,IAAIA,SAAS,EAAE;UACb,IAAI1F,UAAU;UACd,MAAM4F,GAAG,GAAGF,SAAS,CAACG,WAAW,EAAE;UACnC,IAAK7F,UAAU,GAAG,IAAI,CAACC,kBAAkB,CAAC6F,GAAG,CAACF,GAAG,CAAC,EAAG;YACnDd,UAAU,CAAC9E,UAAU,GAAGA,UAAU;UACpC,CAAC,MAAM;YACL,IAAI,CAACC,kBAAkB,CAAC8F,GAAG,CAACH,GAAG,EAAE,CAAC,CAAC;YACnCd,UAAU,CAAC9E,UAAU,GAAG,CAAC;UAC3B;QACF;MACF;MAEA8E,UAAU,CAACM,aAAa,EAAE;MAC1B,IAAI,CAACjE,UAAU,CACb5C,cAAc,CAACoL,gBAAgB,EAC/B,IAAI9L,wBAAA,CAAA+L,oBAAoB,CAAC,IAAI,EAAE9E,UAAU,EAAE6D,qBAAqB,CAAC,CAClE;MAED,IAAI,CAAChJ,OAAO,EAAE;MACd2I,QAAQ,CAACvI,SAAS,EAAE+E,UAAU,CAAC;IACjC,CAAC,EACD3C,KAAK,IAAG;MACN,IAAI,CAACxC,OAAO,EAAE;MACd,IAAI,CAACjB,MAAM,CAACmL,WAAW,CAAC1H,KAAK,CAAC;MAC9B,IAAI,CAAChB,UAAU,CACb5C,cAAc,CAACsI,iBAAiB,EAChC,IAAIhJ,wBAAA,CAAAiJ,qBAAqB,CACvB,IAAI,EACJ;QAAE0B,EAAE,EAAED,cAAc,CAACC,EAAE;QAAE9C,SAAS,EAAE3F;MAAS,CAAE,EAC/C,OAAO;MACP;MACAoC,KAAmB,CACpB,CACF;MACD,IAAIA,KAAK,YAAY5E,OAAA,CAAAuM,iBAAiB,IAAI3H,KAAK,YAAY5E,OAAA,CAAAwM,gBAAgB,EAAE;QAC3E5H,KAAK,CAAC6H,oBAAoB,GAAGzB,cAAc,CAACvI,UAAU;MACxD;MACAsI,QAAQ,CAACnG,KAAK,IAAI,IAAI5E,OAAA,CAAAoI,iBAAiB,CAAC,0CAA0C,CAAC,CAAC;IACtF,CAAC,CACF;EACH;EAEQjD,iBAAiBA,CAAA;IACvB,MAAMzD,WAAW,GAAG,IAAI,CAACN,OAAO,CAACM,WAAW;IAC5C,IAAI,IAAI,CAACO,SAAS,KAAKxB,OAAA,CAAAC,SAAS,CAACI,KAAK,IAAIY,WAAW,KAAK,CAAC,EAAE;MAC3D;IACF;IAEA,IAAI,CAACQ,WAAW,CAACwK,KAAK,CAACnF,UAAU,IAAI,IAAI,CAACoD,2BAA2B,CAACpD,UAAU,CAAC,CAAC;IAElF,IACE,IAAI,CAACrD,oBAAoB,GAAGxC,WAAW,IACvC,IAAI,CAAC0C,sBAAsB,GAAG,IAAI,CAAChD,OAAO,CAACO,aAAa,EACxD;MACA;MACA;MACA;MACA,IAAI,CAACmJ,gBAAgB,CAAC,CAAC6B,GAAG,EAAEpF,UAAU,KAAI;QACxC,IAAI,CAACoF,GAAG,IAAIpF,UAAU,EAAE;UACtB,IAAI,CAACrF,WAAW,CAACoE,IAAI,CAACiB,UAAU,CAAC;UACjC7D,OAAO,CAACC,QAAQ,CAAC,MAAM,IAAI,CAAC4C,gBAAgB,EAAE,CAAC;QACjD;QACA,IAAI,IAAI,CAACtE,SAAS,KAAKxB,OAAA,CAAAC,SAAS,CAACI,KAAK,EAAE;UACtC,IAAAjB,QAAA,CAAAqF,YAAY,EAAC,IAAI,CAAC3C,gBAAgB,CAAC;UACnC,IAAI,CAACA,gBAAgB,GAAG,IAAA1C,QAAA,CAAA+M,UAAU,EAChC,MAAM,IAAI,CAACzH,iBAAiB,EAAE,EAC9B,IAAI,CAAC/D,OAAO,CAACU,2BAA2B,CACzC;QACH;MACF,CAAC,CAAC;IACJ,CAAC,MAAM;MACL,IAAAjC,QAAA,CAAAqF,YAAY,EAAC,IAAI,CAAC3C,gBAAgB,CAAC;MACnC,IAAI,CAACA,gBAAgB,GAAG,IAAA1C,QAAA,CAAA+M,UAAU,EAChC,MAAM,IAAI,CAACzH,iBAAiB,EAAE,EAC9B,IAAI,CAAC/D,OAAO,CAACU,2BAA2B,CACzC;IACH;EACF;EAEQyE,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAAClD,mBAAmB,EAAE;MAC5B;IACF;IACA,IAAI,CAACA,mBAAmB,GAAG,IAAI;IAE/B,OAAO,IAAI,CAACmB,aAAa,EAAE;MACzB,MAAMwB,eAAe,GAAG,IAAI,CAAC9C,SAAS,CAAC2J,KAAK,EAAE;MAC9C,IAAI,CAAC7G,eAAe,EAAE;QACpB,IAAI,CAAC9C,SAAS,CAAC4J,KAAK,EAAE;QACtB;MACF;MAEA,IAAI9G,eAAe,CAACC,SAAS,EAAE;QAC7B,IAAI,CAAC/C,SAAS,CAAC4J,KAAK,EAAE;QACtB;MACF;MAEA,IAAI,IAAI,CAAC7K,SAAS,KAAKxB,OAAA,CAAAC,SAAS,CAACI,KAAK,EAAE;QACtC,MAAMuF,MAAM,GAAG,IAAI,CAACtF,MAAM,GAAG,YAAY,GAAG,iBAAiB;QAC7D,MAAM6D,KAAK,GAAG,IAAI,CAAC7D,MAAM,GAAG,IAAIR,QAAA,CAAAkL,eAAe,CAAC,IAAI,CAAC,GAAG,IAAIlL,QAAA,CAAAmL,gBAAgB,CAAC,IAAI,CAAC;QAClF,IAAI,CAAC9H,UAAU,CACb5C,cAAc,CAAC8F,2BAA2B,EAC1C,IAAIxG,wBAAA,CAAAyG,6BAA6B,CAAC,IAAI,EAAEV,MAAM,EAAEL,eAAe,CAACX,YAAY,EAAET,KAAK,CAAC,CACrF;QACD,IAAI,CAAC1B,SAAS,CAAC4J,KAAK,EAAE;QACtB9G,eAAe,CAACL,MAAM,CAACf,KAAK,CAAC;QAC7B;MACF;MAEA,IAAI,CAAC,IAAI,CAACT,wBAAwB,EAAE;QAClC;MACF;MAEA,MAAMoD,UAAU,GAAG,IAAI,CAACrF,WAAW,CAAC4K,KAAK,EAAE;MAC3C,IAAI,CAACvF,UAAU,EAAE;QACf;MACF;MAEA,IAAI,CAAC,IAAI,CAACoD,2BAA2B,CAACpD,UAAU,CAAC,EAAE;QACjD,IAAI,CAAClF,UAAU,CAAC0K,GAAG,CAACxF,UAAU,CAAC;QAC/B,IAAI,CAAC3D,UAAU,CACb5C,cAAc,CAACgM,sBAAsB,EACrC,IAAI1M,wBAAA,CAAA2M,yBAAyB,CAAC,IAAI,EAAE1F,UAAU,EAAEvB,eAAe,CAACX,YAAY,CAAC,CAC9E;QAED,IAAI,CAACnC,SAAS,CAAC4J,KAAK,EAAE;QACtB9G,eAAe,CAACN,OAAO,CAAC6B,UAAU,CAAC;MACrC;IACF;IAEA,MAAM;MAAE9F,WAAW;MAAEE;IAAa,CAAE,GAAG,IAAI,CAACP,OAAO;IACnD,OACE,IAAI,CAACoD,aAAa,GAAG,CAAC,IACtB,IAAI,CAACJ,sBAAsB,GAAGzC,aAAa,KAC1CF,WAAW,KAAK,CAAC,IAAI,IAAI,CAACyC,oBAAoB,GAAGzC,WAAW,CAAC,EAC9D;MACA,MAAMuE,eAAe,GAAG,IAAI,CAAC9C,SAAS,CAAC4J,KAAK,EAAE;MAC9C,IAAI,CAAC9G,eAAe,IAAIA,eAAe,CAACC,SAAS,EAAE;QACjD;MACF;MACA,IAAI,CAAC6E,gBAAgB,CAAC,CAAC6B,GAAG,EAAEpF,UAAU,KAAI;QACxC,IAAIvB,eAAe,CAACC,SAAS,EAAE;UAC7B,IAAI,CAAC0G,GAAG,IAAIpF,UAAU,EAAE;YACtB,IAAI,CAACrF,WAAW,CAACoE,IAAI,CAACiB,UAAU,CAAC;UACnC;QACF,CAAC,MAAM;UACL,IAAIoF,GAAG,EAAE;YACP,IAAI,CAAC/I,UAAU,CACb5C,cAAc,CAAC8F,2BAA2B;YAC1C;YACA,IAAIxG,wBAAA,CAAAyG,6BAA6B,CAC/B,IAAI,EACJ,iBAAiB,EACjBf,eAAe,CAACX,YAAY,EAC5BsH,GAAiB,CAClB,CACF;YACD3G,eAAe,CAACL,MAAM,CAACgH,GAAG,CAAC;UAC7B,CAAC,MAAM,IAAIpF,UAAU,EAAE;YACrB,IAAI,CAAClF,UAAU,CAAC0K,GAAG,CAACxF,UAAU,CAAC;YAC/B,IAAI,CAAC3D,UAAU,CACb5C,cAAc,CAACgM,sBAAsB,EACrC,IAAI1M,wBAAA,CAAA2M,yBAAyB,CAAC,IAAI,EAAE1F,UAAU,EAAEvB,eAAe,CAACX,YAAY,CAAC,CAC9E;YACDW,eAAe,CAACN,OAAO,CAAC6B,UAAU,CAAC;UACrC;QACF;QACA7D,OAAO,CAACC,QAAQ,CAAC,MAAM,IAAI,CAAC4C,gBAAgB,EAAE,CAAC;MACjD,CAAC,CAAC;IACJ;IACA,IAAI,CAAClD,mBAAmB,GAAG,KAAK;EAClC;;AA/qBF5C,OAAA,CAAAO,cAAA,GAAAA,cAAA;AAmBE;;;;AAIgBA,cAAA,CAAA6C,uBAAuB,GAAG9D,WAAA,CAAA8D,uBAAuB;AACjE;;;;AAIgB7C,cAAA,CAAAyI,sBAAsB,GAAG1J,WAAA,CAAA0J,sBAAsB;AAC/D;;;;AAIgBzI,cAAA,CAAAyH,uBAAuB,GAAG1I,WAAA,CAAA0I,uBAAuB;AACjE;;;;AAIgBzH,cAAA,CAAAgE,qBAAqB,GAAGjF,WAAA,CAAAiF,qBAAqB;AAC7D;;;;AAIgBhE,cAAA,CAAAqK,kBAAkB,GAAGtL,WAAA,CAAAsL,kBAAkB;AACvD;;;;AAIgBrK,cAAA,CAAAoL,gBAAgB,GAAGrM,WAAA,CAAAqM,gBAAgB;AACnD;;;;AAIgBpL,cAAA,CAAAsI,iBAAiB,GAAGvJ,WAAA,CAAAuJ,iBAAiB;AACrD;;;;AAIgBtI,cAAA,CAAAuE,4BAA4B,GAAGxF,WAAA,CAAAwF,4BAA4B;AAC3E;;;;AAIgBvE,cAAA,CAAA8F,2BAA2B,GAAG/G,WAAA,CAAA+G,2BAA2B;AACzE;;;;AAIgB9F,cAAA,CAAAgM,sBAAsB,GAAGjN,WAAA,CAAAiN,sBAAsB;AAC/D;;;;AAIgBhM,cAAA,CAAAgH,qBAAqB,GAAGjI,WAAA,CAAAiI,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}