{"ast": null, "code": "'use client';\n\n// based on https://github.com/WICG/focus-visible/blob/v4.1.5/src/focus-visible.js\nimport * as React from 'react';\nimport { Timeout } from \"../useTimeout/useTimeout.js\";\nlet hadKeyboardEvent = true;\nlet hadFocusVisibleRecently = false;\nconst hadFocusVisibleRecentlyTimeout = new Timeout();\nconst inputTypesWhitelist = {\n  text: true,\n  search: true,\n  url: true,\n  tel: true,\n  email: true,\n  password: true,\n  number: true,\n  date: true,\n  month: true,\n  week: true,\n  time: true,\n  datetime: true,\n  'datetime-local': true\n};\n\n/**\n * Computes whether the given element should automatically trigger the\n * `focus-visible` class being added, i.e. whether it should always match\n * `:focus-visible` when focused.\n * @param {Element} node\n * @returns {boolean}\n */\nfunction focusTriggersKeyboardModality(node) {\n  const {\n    type,\n    tagName\n  } = node;\n  if (tagName === 'INPUT' && inputTypesWhitelist[type] && !node.readOnly) {\n    return true;\n  }\n  if (tagName === 'TEXTAREA' && !node.readOnly) {\n    return true;\n  }\n  if (node.isContentEditable) {\n    return true;\n  }\n  return false;\n}\n\n/**\n * Keep track of our keyboard modality state with `hadKeyboardEvent`.\n * If the most recent user interaction was via the keyboard;\n * and the key press did not include a meta, alt/option, or control key;\n * then the modality is keyboard. Otherwise, the modality is not keyboard.\n * @param {KeyboardEvent} event\n */\nfunction handleKeyDown(event) {\n  if (event.metaKey || event.altKey || event.ctrlKey) {\n    return;\n  }\n  hadKeyboardEvent = true;\n}\n\n/**\n * If at any point a user clicks with a pointing device, ensure that we change\n * the modality away from keyboard.\n * This avoids the situation where a user presses a key on an already focused\n * element, and then clicks on a different element, focusing it with a\n * pointing device, while we still think we're in keyboard modality.\n */\nfunction handlePointerDown() {\n  hadKeyboardEvent = false;\n}\nfunction handleVisibilityChange() {\n  if (this.visibilityState === 'hidden') {\n    // If the tab becomes active again, the browser will handle calling focus\n    // on the element (Safari actually calls it twice).\n    // If this tab change caused a blur on an element with focus-visible,\n    // re-apply the class when the user switches back to the tab.\n    if (hadFocusVisibleRecently) {\n      hadKeyboardEvent = true;\n    }\n  }\n}\nfunction prepare(doc) {\n  doc.addEventListener('keydown', handleKeyDown, true);\n  doc.addEventListener('mousedown', handlePointerDown, true);\n  doc.addEventListener('pointerdown', handlePointerDown, true);\n  doc.addEventListener('touchstart', handlePointerDown, true);\n  doc.addEventListener('visibilitychange', handleVisibilityChange, true);\n}\nexport function teardown(doc) {\n  doc.removeEventListener('keydown', handleKeyDown, true);\n  doc.removeEventListener('mousedown', handlePointerDown, true);\n  doc.removeEventListener('pointerdown', handlePointerDown, true);\n  doc.removeEventListener('touchstart', handlePointerDown, true);\n  doc.removeEventListener('visibilitychange', handleVisibilityChange, true);\n}\nfunction isFocusVisible(event) {\n  const {\n    target\n  } = event;\n  try {\n    return target.matches(':focus-visible');\n  } catch (error) {\n    // Browsers not implementing :focus-visible will throw a SyntaxError.\n    // We use our own heuristic for those browsers.\n    // Rethrow might be better if it's not the expected error but do we really\n    // want to crash if focus-visible malfunctioned?\n  }\n\n  // No need for validFocusTarget check. The user does that by attaching it to\n  // focusable events only.\n  return hadKeyboardEvent || focusTriggersKeyboardModality(target);\n}\nexport default function useIsFocusVisible() {\n  const ref = React.useCallback(node => {\n    if (node != null) {\n      prepare(node.ownerDocument);\n    }\n  }, []);\n  const isFocusVisibleRef = React.useRef(false);\n\n  /**\n   * Should be called if a blur event is fired\n   */\n  function handleBlurVisible() {\n    // checking against potential state variable does not suffice if we focus and blur synchronously.\n    // React wouldn't have time to trigger a re-render so `focusVisible` would be stale.\n    // Ideally we would adjust `isFocusVisible(event)` to look at `relatedTarget` for blur events.\n    // This doesn't work in IE11 due to https://github.com/facebook/react/issues/3751\n    // TODO: check again if React releases their internal changes to focus event handling (https://github.com/facebook/react/pull/19186).\n    if (isFocusVisibleRef.current) {\n      // To detect a tab/window switch, we look for a blur event followed\n      // rapidly by a visibility change.\n      // If we don't see a visibility change within 100ms, it's probably a\n      // regular focus change.\n      hadFocusVisibleRecently = true;\n      hadFocusVisibleRecentlyTimeout.start(100, () => {\n        hadFocusVisibleRecently = false;\n      });\n      isFocusVisibleRef.current = false;\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * Should be called if a blur event is fired\n   */\n  function handleFocusVisible(event) {\n    if (isFocusVisible(event)) {\n      isFocusVisibleRef.current = true;\n      return true;\n    }\n    return false;\n  }\n  return {\n    isFocusVisibleRef,\n    onFocus: handleFocusVisible,\n    onBlur: handleBlurVisible,\n    ref\n  };\n}", "map": {"version": 3, "names": ["React", "Timeout", "hadKeyboardEvent", "hadFocusVisibleRecently", "hadFocusVisibleRecentlyTimeout", "inputTypesW<PERSON>elist", "text", "search", "url", "tel", "email", "password", "number", "date", "month", "week", "time", "datetime", "focusTriggersKeyboardModality", "node", "type", "tagName", "readOnly", "isContentEditable", "handleKeyDown", "event", "metaKey", "altKey", "ctrl<PERSON>ey", "handlePointerDown", "handleVisibilityChange", "visibilityState", "prepare", "doc", "addEventListener", "teardown", "removeEventListener", "isFocusVisible", "target", "matches", "error", "useIsFocusVisible", "ref", "useCallback", "ownerDocument", "isFocusVisibleRef", "useRef", "handleBlurVisible", "current", "start", "handleFocusVisible", "onFocus", "onBlur"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/base/node_modules/@mui/utils/esm/useIsFocusVisible/useIsFocusVisible.js"], "sourcesContent": ["'use client';\n\n// based on https://github.com/WICG/focus-visible/blob/v4.1.5/src/focus-visible.js\nimport * as React from 'react';\nimport { Timeout } from \"../useTimeout/useTimeout.js\";\nlet hadKeyboardEvent = true;\nlet hadFocusVisibleRecently = false;\nconst hadFocusVisibleRecentlyTimeout = new Timeout();\nconst inputTypesWhitelist = {\n  text: true,\n  search: true,\n  url: true,\n  tel: true,\n  email: true,\n  password: true,\n  number: true,\n  date: true,\n  month: true,\n  week: true,\n  time: true,\n  datetime: true,\n  'datetime-local': true\n};\n\n/**\n * Computes whether the given element should automatically trigger the\n * `focus-visible` class being added, i.e. whether it should always match\n * `:focus-visible` when focused.\n * @param {Element} node\n * @returns {boolean}\n */\nfunction focusTriggersKeyboardModality(node) {\n  const {\n    type,\n    tagName\n  } = node;\n  if (tagName === 'INPUT' && inputTypesWhitelist[type] && !node.readOnly) {\n    return true;\n  }\n  if (tagName === 'TEXTAREA' && !node.readOnly) {\n    return true;\n  }\n  if (node.isContentEditable) {\n    return true;\n  }\n  return false;\n}\n\n/**\n * Keep track of our keyboard modality state with `hadKeyboardEvent`.\n * If the most recent user interaction was via the keyboard;\n * and the key press did not include a meta, alt/option, or control key;\n * then the modality is keyboard. Otherwise, the modality is not keyboard.\n * @param {KeyboardEvent} event\n */\nfunction handleKeyDown(event) {\n  if (event.metaKey || event.altKey || event.ctrlKey) {\n    return;\n  }\n  hadKeyboardEvent = true;\n}\n\n/**\n * If at any point a user clicks with a pointing device, ensure that we change\n * the modality away from keyboard.\n * This avoids the situation where a user presses a key on an already focused\n * element, and then clicks on a different element, focusing it with a\n * pointing device, while we still think we're in keyboard modality.\n */\nfunction handlePointerDown() {\n  hadKeyboardEvent = false;\n}\nfunction handleVisibilityChange() {\n  if (this.visibilityState === 'hidden') {\n    // If the tab becomes active again, the browser will handle calling focus\n    // on the element (Safari actually calls it twice).\n    // If this tab change caused a blur on an element with focus-visible,\n    // re-apply the class when the user switches back to the tab.\n    if (hadFocusVisibleRecently) {\n      hadKeyboardEvent = true;\n    }\n  }\n}\nfunction prepare(doc) {\n  doc.addEventListener('keydown', handleKeyDown, true);\n  doc.addEventListener('mousedown', handlePointerDown, true);\n  doc.addEventListener('pointerdown', handlePointerDown, true);\n  doc.addEventListener('touchstart', handlePointerDown, true);\n  doc.addEventListener('visibilitychange', handleVisibilityChange, true);\n}\nexport function teardown(doc) {\n  doc.removeEventListener('keydown', handleKeyDown, true);\n  doc.removeEventListener('mousedown', handlePointerDown, true);\n  doc.removeEventListener('pointerdown', handlePointerDown, true);\n  doc.removeEventListener('touchstart', handlePointerDown, true);\n  doc.removeEventListener('visibilitychange', handleVisibilityChange, true);\n}\nfunction isFocusVisible(event) {\n  const {\n    target\n  } = event;\n  try {\n    return target.matches(':focus-visible');\n  } catch (error) {\n    // Browsers not implementing :focus-visible will throw a SyntaxError.\n    // We use our own heuristic for those browsers.\n    // Rethrow might be better if it's not the expected error but do we really\n    // want to crash if focus-visible malfunctioned?\n  }\n\n  // No need for validFocusTarget check. The user does that by attaching it to\n  // focusable events only.\n  return hadKeyboardEvent || focusTriggersKeyboardModality(target);\n}\nexport default function useIsFocusVisible() {\n  const ref = React.useCallback(node => {\n    if (node != null) {\n      prepare(node.ownerDocument);\n    }\n  }, []);\n  const isFocusVisibleRef = React.useRef(false);\n\n  /**\n   * Should be called if a blur event is fired\n   */\n  function handleBlurVisible() {\n    // checking against potential state variable does not suffice if we focus and blur synchronously.\n    // React wouldn't have time to trigger a re-render so `focusVisible` would be stale.\n    // Ideally we would adjust `isFocusVisible(event)` to look at `relatedTarget` for blur events.\n    // This doesn't work in IE11 due to https://github.com/facebook/react/issues/3751\n    // TODO: check again if React releases their internal changes to focus event handling (https://github.com/facebook/react/pull/19186).\n    if (isFocusVisibleRef.current) {\n      // To detect a tab/window switch, we look for a blur event followed\n      // rapidly by a visibility change.\n      // If we don't see a visibility change within 100ms, it's probably a\n      // regular focus change.\n      hadFocusVisibleRecently = true;\n      hadFocusVisibleRecentlyTimeout.start(100, () => {\n        hadFocusVisibleRecently = false;\n      });\n      isFocusVisibleRef.current = false;\n      return true;\n    }\n    return false;\n  }\n\n  /**\n   * Should be called if a blur event is fired\n   */\n  function handleFocusVisible(event) {\n    if (isFocusVisible(event)) {\n      isFocusVisibleRef.current = true;\n      return true;\n    }\n    return false;\n  }\n  return {\n    isFocusVisibleRef,\n    onFocus: handleFocusVisible,\n    onBlur: handleBlurVisible,\n    ref\n  };\n}"], "mappings": "AAAA,YAAY;;AAEZ;AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,OAAO,QAAQ,6BAA6B;AACrD,IAAIC,gBAAgB,GAAG,IAAI;AAC3B,IAAIC,uBAAuB,GAAG,KAAK;AACnC,MAAMC,8BAA8B,GAAG,IAAIH,OAAO,CAAC,CAAC;AACpD,MAAMI,mBAAmB,GAAG;EAC1BC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE,IAAI;EACZC,GAAG,EAAE,IAAI;EACTC,GAAG,EAAE,IAAI;EACTC,KAAK,EAAE,IAAI;EACXC,QAAQ,EAAE,IAAI;EACdC,MAAM,EAAE,IAAI;EACZC,IAAI,EAAE,IAAI;EACVC,KAAK,EAAE,IAAI;EACXC,IAAI,EAAE,IAAI;EACVC,IAAI,EAAE,IAAI;EACVC,QAAQ,EAAE,IAAI;EACd,gBAAgB,EAAE;AACpB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,6BAA6BA,CAACC,IAAI,EAAE;EAC3C,MAAM;IACJC,IAAI;IACJC;EACF,CAAC,GAAGF,IAAI;EACR,IAAIE,OAAO,KAAK,OAAO,IAAIhB,mBAAmB,CAACe,IAAI,CAAC,IAAI,CAACD,IAAI,CAACG,QAAQ,EAAE;IACtE,OAAO,IAAI;EACb;EACA,IAAID,OAAO,KAAK,UAAU,IAAI,CAACF,IAAI,CAACG,QAAQ,EAAE;IAC5C,OAAO,IAAI;EACb;EACA,IAAIH,IAAI,CAACI,iBAAiB,EAAE;IAC1B,OAAO,IAAI;EACb;EACA,OAAO,KAAK;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAE;EAC5B,IAAIA,KAAK,CAACC,OAAO,IAAID,KAAK,CAACE,MAAM,IAAIF,KAAK,CAACG,OAAO,EAAE;IAClD;EACF;EACA1B,gBAAgB,GAAG,IAAI;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2B,iBAAiBA,CAAA,EAAG;EAC3B3B,gBAAgB,GAAG,KAAK;AAC1B;AACA,SAAS4B,sBAAsBA,CAAA,EAAG;EAChC,IAAI,IAAI,CAACC,eAAe,KAAK,QAAQ,EAAE;IACrC;IACA;IACA;IACA;IACA,IAAI5B,uBAAuB,EAAE;MAC3BD,gBAAgB,GAAG,IAAI;IACzB;EACF;AACF;AACA,SAAS8B,OAAOA,CAACC,GAAG,EAAE;EACpBA,GAAG,CAACC,gBAAgB,CAAC,SAAS,EAAEV,aAAa,EAAE,IAAI,CAAC;EACpDS,GAAG,CAACC,gBAAgB,CAAC,WAAW,EAAEL,iBAAiB,EAAE,IAAI,CAAC;EAC1DI,GAAG,CAACC,gBAAgB,CAAC,aAAa,EAAEL,iBAAiB,EAAE,IAAI,CAAC;EAC5DI,GAAG,CAACC,gBAAgB,CAAC,YAAY,EAAEL,iBAAiB,EAAE,IAAI,CAAC;EAC3DI,GAAG,CAACC,gBAAgB,CAAC,kBAAkB,EAAEJ,sBAAsB,EAAE,IAAI,CAAC;AACxE;AACA,OAAO,SAASK,QAAQA,CAACF,GAAG,EAAE;EAC5BA,GAAG,CAACG,mBAAmB,CAAC,SAAS,EAAEZ,aAAa,EAAE,IAAI,CAAC;EACvDS,GAAG,CAACG,mBAAmB,CAAC,WAAW,EAAEP,iBAAiB,EAAE,IAAI,CAAC;EAC7DI,GAAG,CAACG,mBAAmB,CAAC,aAAa,EAAEP,iBAAiB,EAAE,IAAI,CAAC;EAC/DI,GAAG,CAACG,mBAAmB,CAAC,YAAY,EAAEP,iBAAiB,EAAE,IAAI,CAAC;EAC9DI,GAAG,CAACG,mBAAmB,CAAC,kBAAkB,EAAEN,sBAAsB,EAAE,IAAI,CAAC;AAC3E;AACA,SAASO,cAAcA,CAACZ,KAAK,EAAE;EAC7B,MAAM;IACJa;EACF,CAAC,GAAGb,KAAK;EACT,IAAI;IACF,OAAOa,MAAM,CAACC,OAAO,CAAC,gBAAgB,CAAC;EACzC,CAAC,CAAC,OAAOC,KAAK,EAAE;IACd;IACA;IACA;IACA;EAAA;;EAGF;EACA;EACA,OAAOtC,gBAAgB,IAAIgB,6BAA6B,CAACoB,MAAM,CAAC;AAClE;AACA,eAAe,SAASG,iBAAiBA,CAAA,EAAG;EAC1C,MAAMC,GAAG,GAAG1C,KAAK,CAAC2C,WAAW,CAACxB,IAAI,IAAI;IACpC,IAAIA,IAAI,IAAI,IAAI,EAAE;MAChBa,OAAO,CAACb,IAAI,CAACyB,aAAa,CAAC;IAC7B;EACF,CAAC,EAAE,EAAE,CAAC;EACN,MAAMC,iBAAiB,GAAG7C,KAAK,CAAC8C,MAAM,CAAC,KAAK,CAAC;;EAE7C;AACF;AACA;EACE,SAASC,iBAAiBA,CAAA,EAAG;IAC3B;IACA;IACA;IACA;IACA;IACA,IAAIF,iBAAiB,CAACG,OAAO,EAAE;MAC7B;MACA;MACA;MACA;MACA7C,uBAAuB,GAAG,IAAI;MAC9BC,8BAA8B,CAAC6C,KAAK,CAAC,GAAG,EAAE,MAAM;QAC9C9C,uBAAuB,GAAG,KAAK;MACjC,CAAC,CAAC;MACF0C,iBAAiB,CAACG,OAAO,GAAG,KAAK;MACjC,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;;EAEA;AACF;AACA;EACE,SAASE,kBAAkBA,CAACzB,KAAK,EAAE;IACjC,IAAIY,cAAc,CAACZ,KAAK,CAAC,EAAE;MACzBoB,iBAAiB,CAACG,OAAO,GAAG,IAAI;MAChC,OAAO,IAAI;IACb;IACA,OAAO,KAAK;EACd;EACA,OAAO;IACLH,iBAAiB;IACjBM,OAAO,EAAED,kBAAkB;IAC3BE,MAAM,EAAEL,iBAAiB;IACzBL;EACF,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}