{"ast": null, "code": "\"use strict\";\n\nconst conversions = require(\"webidl-conversions\");\nconst utils = require(\"./utils.js\");\nconst Function = require(\"./Function.js\");\nconst newObjectInRealm = utils.newObjectInRealm;\nconst implSymbol = utils.implSymbol;\nconst ctorRegistrySymbol = utils.ctorRegistrySymbol;\nconst interfaceName = \"URLSearchParams\";\nexports.is = value => {\n  return utils.isObject(value) && utils.hasOwn(value, implSymbol) && value[implSymbol] instanceof Impl.implementation;\n};\nexports.isImpl = value => {\n  return utils.isObject(value) && value instanceof Impl.implementation;\n};\nexports.convert = (globalObject, value, {\n  context = \"The provided value\"\n} = {}) => {\n  if (exports.is(value)) {\n    return utils.implForWrapper(value);\n  }\n  throw new globalObject.TypeError(`${context} is not of type 'URLSearchParams'.`);\n};\nexports.createDefaultIterator = (globalObject, target, kind) => {\n  const ctorRegistry = globalObject[ctorRegistrySymbol];\n  const iteratorPrototype = ctorRegistry[\"URLSearchParams Iterator\"];\n  const iterator = Object.create(iteratorPrototype);\n  Object.defineProperty(iterator, utils.iterInternalSymbol, {\n    value: {\n      target,\n      kind,\n      index: 0\n    },\n    configurable: true\n  });\n  return iterator;\n};\nfunction makeWrapper(globalObject, newTarget) {\n  let proto;\n  if (newTarget !== undefined) {\n    proto = newTarget.prototype;\n  }\n  if (!utils.isObject(proto)) {\n    proto = globalObject[ctorRegistrySymbol][\"URLSearchParams\"].prototype;\n  }\n  return Object.create(proto);\n}\nexports.create = (globalObject, constructorArgs, privateData) => {\n  const wrapper = makeWrapper(globalObject);\n  return exports.setup(wrapper, globalObject, constructorArgs, privateData);\n};\nexports.createImpl = (globalObject, constructorArgs, privateData) => {\n  const wrapper = exports.create(globalObject, constructorArgs, privateData);\n  return utils.implForWrapper(wrapper);\n};\nexports._internalSetup = (wrapper, globalObject) => {};\nexports.setup = (wrapper, globalObject, constructorArgs = [], privateData = {}) => {\n  privateData.wrapper = wrapper;\n  exports._internalSetup(wrapper, globalObject);\n  Object.defineProperty(wrapper, implSymbol, {\n    value: new Impl.implementation(globalObject, constructorArgs, privateData),\n    configurable: true\n  });\n  wrapper[implSymbol][utils.wrapperSymbol] = wrapper;\n  if (Impl.init) {\n    Impl.init(wrapper[implSymbol]);\n  }\n  return wrapper;\n};\nexports.new = (globalObject, newTarget) => {\n  const wrapper = makeWrapper(globalObject, newTarget);\n  exports._internalSetup(wrapper, globalObject);\n  Object.defineProperty(wrapper, implSymbol, {\n    value: Object.create(Impl.implementation.prototype),\n    configurable: true\n  });\n  wrapper[implSymbol][utils.wrapperSymbol] = wrapper;\n  if (Impl.init) {\n    Impl.init(wrapper[implSymbol]);\n  }\n  return wrapper[implSymbol];\n};\nconst exposed = new Set([\"Window\", \"Worker\"]);\nexports.install = (globalObject, globalNames) => {\n  if (!globalNames.some(globalName => exposed.has(globalName))) {\n    return;\n  }\n  const ctorRegistry = utils.initCtorRegistry(globalObject);\n  class URLSearchParams {\n    constructor() {\n      const args = [];\n      {\n        let curArg = arguments[0];\n        if (curArg !== undefined) {\n          if (utils.isObject(curArg)) {\n            if (curArg[Symbol.iterator] !== undefined) {\n              if (!utils.isObject(curArg)) {\n                throw new globalObject.TypeError(\"Failed to construct 'URLSearchParams': parameter 1\" + \" sequence\" + \" is not an iterable object.\");\n              } else {\n                const V = [];\n                const tmp = curArg;\n                for (let nextItem of tmp) {\n                  if (!utils.isObject(nextItem)) {\n                    throw new globalObject.TypeError(\"Failed to construct 'URLSearchParams': parameter 1\" + \" sequence\" + \"'s element\" + \" is not an iterable object.\");\n                  } else {\n                    const V = [];\n                    const tmp = nextItem;\n                    for (let nextItem of tmp) {\n                      nextItem = conversions[\"USVString\"](nextItem, {\n                        context: \"Failed to construct 'URLSearchParams': parameter 1\" + \" sequence\" + \"'s element\" + \"'s element\",\n                        globals: globalObject\n                      });\n                      V.push(nextItem);\n                    }\n                    nextItem = V;\n                  }\n                  V.push(nextItem);\n                }\n                curArg = V;\n              }\n            } else {\n              if (!utils.isObject(curArg)) {\n                throw new globalObject.TypeError(\"Failed to construct 'URLSearchParams': parameter 1\" + \" record\" + \" is not an object.\");\n              } else {\n                const result = Object.create(null);\n                for (const key of Reflect.ownKeys(curArg)) {\n                  const desc = Object.getOwnPropertyDescriptor(curArg, key);\n                  if (desc && desc.enumerable) {\n                    let typedKey = key;\n                    typedKey = conversions[\"USVString\"](typedKey, {\n                      context: \"Failed to construct 'URLSearchParams': parameter 1\" + \" record\" + \"'s key\",\n                      globals: globalObject\n                    });\n                    let typedValue = curArg[key];\n                    typedValue = conversions[\"USVString\"](typedValue, {\n                      context: \"Failed to construct 'URLSearchParams': parameter 1\" + \" record\" + \"'s value\",\n                      globals: globalObject\n                    });\n                    result[typedKey] = typedValue;\n                  }\n                }\n                curArg = result;\n              }\n            }\n          } else {\n            curArg = conversions[\"USVString\"](curArg, {\n              context: \"Failed to construct 'URLSearchParams': parameter 1\",\n              globals: globalObject\n            });\n          }\n        } else {\n          curArg = \"\";\n        }\n        args.push(curArg);\n      }\n      return exports.setup(Object.create(new.target.prototype), globalObject, args);\n    }\n    append(name, value) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'append' called on an object that is not a valid instance of URLSearchParams.\");\n      }\n      if (arguments.length < 2) {\n        throw new globalObject.TypeError(`Failed to execute 'append' on 'URLSearchParams': 2 arguments required, but only ${arguments.length} present.`);\n      }\n      const args = [];\n      {\n        let curArg = arguments[0];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'append' on 'URLSearchParams': parameter 1\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      {\n        let curArg = arguments[1];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'append' on 'URLSearchParams': parameter 2\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      return utils.tryWrapperForImpl(esValue[implSymbol].append(...args));\n    }\n    delete(name) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'delete' called on an object that is not a valid instance of URLSearchParams.\");\n      }\n      if (arguments.length < 1) {\n        throw new globalObject.TypeError(`Failed to execute 'delete' on 'URLSearchParams': 1 argument required, but only ${arguments.length} present.`);\n      }\n      const args = [];\n      {\n        let curArg = arguments[0];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'delete' on 'URLSearchParams': parameter 1\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      {\n        let curArg = arguments[1];\n        if (curArg !== undefined) {\n          curArg = conversions[\"USVString\"](curArg, {\n            context: \"Failed to execute 'delete' on 'URLSearchParams': parameter 2\",\n            globals: globalObject\n          });\n        }\n        args.push(curArg);\n      }\n      return utils.tryWrapperForImpl(esValue[implSymbol].delete(...args));\n    }\n    get(name) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get' called on an object that is not a valid instance of URLSearchParams.\");\n      }\n      if (arguments.length < 1) {\n        throw new globalObject.TypeError(`Failed to execute 'get' on 'URLSearchParams': 1 argument required, but only ${arguments.length} present.`);\n      }\n      const args = [];\n      {\n        let curArg = arguments[0];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'get' on 'URLSearchParams': parameter 1\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      return esValue[implSymbol].get(...args);\n    }\n    getAll(name) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'getAll' called on an object that is not a valid instance of URLSearchParams.\");\n      }\n      if (arguments.length < 1) {\n        throw new globalObject.TypeError(`Failed to execute 'getAll' on 'URLSearchParams': 1 argument required, but only ${arguments.length} present.`);\n      }\n      const args = [];\n      {\n        let curArg = arguments[0];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'getAll' on 'URLSearchParams': parameter 1\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      return utils.tryWrapperForImpl(esValue[implSymbol].getAll(...args));\n    }\n    has(name) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'has' called on an object that is not a valid instance of URLSearchParams.\");\n      }\n      if (arguments.length < 1) {\n        throw new globalObject.TypeError(`Failed to execute 'has' on 'URLSearchParams': 1 argument required, but only ${arguments.length} present.`);\n      }\n      const args = [];\n      {\n        let curArg = arguments[0];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'has' on 'URLSearchParams': parameter 1\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      {\n        let curArg = arguments[1];\n        if (curArg !== undefined) {\n          curArg = conversions[\"USVString\"](curArg, {\n            context: \"Failed to execute 'has' on 'URLSearchParams': parameter 2\",\n            globals: globalObject\n          });\n        }\n        args.push(curArg);\n      }\n      return esValue[implSymbol].has(...args);\n    }\n    set(name, value) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set' called on an object that is not a valid instance of URLSearchParams.\");\n      }\n      if (arguments.length < 2) {\n        throw new globalObject.TypeError(`Failed to execute 'set' on 'URLSearchParams': 2 arguments required, but only ${arguments.length} present.`);\n      }\n      const args = [];\n      {\n        let curArg = arguments[0];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'set' on 'URLSearchParams': parameter 1\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      {\n        let curArg = arguments[1];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'set' on 'URLSearchParams': parameter 2\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      return utils.tryWrapperForImpl(esValue[implSymbol].set(...args));\n    }\n    sort() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'sort' called on an object that is not a valid instance of URLSearchParams.\");\n      }\n      return utils.tryWrapperForImpl(esValue[implSymbol].sort());\n    }\n    toString() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'toString' called on an object that is not a valid instance of URLSearchParams.\");\n      }\n      return esValue[implSymbol].toString();\n    }\n    keys() {\n      if (!exports.is(this)) {\n        throw new globalObject.TypeError(\"'keys' called on an object that is not a valid instance of URLSearchParams.\");\n      }\n      return exports.createDefaultIterator(globalObject, this, \"key\");\n    }\n    values() {\n      if (!exports.is(this)) {\n        throw new globalObject.TypeError(\"'values' called on an object that is not a valid instance of URLSearchParams.\");\n      }\n      return exports.createDefaultIterator(globalObject, this, \"value\");\n    }\n    entries() {\n      if (!exports.is(this)) {\n        throw new globalObject.TypeError(\"'entries' called on an object that is not a valid instance of URLSearchParams.\");\n      }\n      return exports.createDefaultIterator(globalObject, this, \"key+value\");\n    }\n    forEach(callback) {\n      if (!exports.is(this)) {\n        throw new globalObject.TypeError(\"'forEach' called on an object that is not a valid instance of URLSearchParams.\");\n      }\n      if (arguments.length < 1) {\n        throw new globalObject.TypeError(\"Failed to execute 'forEach' on 'iterable': 1 argument required, but only 0 present.\");\n      }\n      callback = Function.convert(globalObject, callback, {\n        context: \"Failed to execute 'forEach' on 'iterable': The callback provided as parameter 1\"\n      });\n      const thisArg = arguments[1];\n      let pairs = Array.from(this[implSymbol]);\n      let i = 0;\n      while (i < pairs.length) {\n        const [key, value] = pairs[i].map(utils.tryWrapperForImpl);\n        callback.call(thisArg, value, key, this);\n        pairs = Array.from(this[implSymbol]);\n        i++;\n      }\n    }\n    get size() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get size' called on an object that is not a valid instance of URLSearchParams.\");\n      }\n      return esValue[implSymbol][\"size\"];\n    }\n  }\n  Object.defineProperties(URLSearchParams.prototype, {\n    append: {\n      enumerable: true\n    },\n    delete: {\n      enumerable: true\n    },\n    get: {\n      enumerable: true\n    },\n    getAll: {\n      enumerable: true\n    },\n    has: {\n      enumerable: true\n    },\n    set: {\n      enumerable: true\n    },\n    sort: {\n      enumerable: true\n    },\n    toString: {\n      enumerable: true\n    },\n    keys: {\n      enumerable: true\n    },\n    values: {\n      enumerable: true\n    },\n    entries: {\n      enumerable: true\n    },\n    forEach: {\n      enumerable: true\n    },\n    size: {\n      enumerable: true\n    },\n    [Symbol.toStringTag]: {\n      value: \"URLSearchParams\",\n      configurable: true\n    },\n    [Symbol.iterator]: {\n      value: URLSearchParams.prototype.entries,\n      configurable: true,\n      writable: true\n    }\n  });\n  ctorRegistry[interfaceName] = URLSearchParams;\n  ctorRegistry[\"URLSearchParams Iterator\"] = Object.create(ctorRegistry[\"%IteratorPrototype%\"], {\n    [Symbol.toStringTag]: {\n      configurable: true,\n      value: \"URLSearchParams Iterator\"\n    }\n  });\n  utils.define(ctorRegistry[\"URLSearchParams Iterator\"], {\n    next() {\n      const internal = this && this[utils.iterInternalSymbol];\n      if (!internal) {\n        throw new globalObject.TypeError(\"next() called on a value that is not a URLSearchParams iterator object\");\n      }\n      const {\n        target,\n        kind,\n        index\n      } = internal;\n      const values = Array.from(target[implSymbol]);\n      const len = values.length;\n      if (index >= len) {\n        return newObjectInRealm(globalObject, {\n          value: undefined,\n          done: true\n        });\n      }\n      const pair = values[index];\n      internal.index = index + 1;\n      return newObjectInRealm(globalObject, utils.iteratorResult(pair.map(utils.tryWrapperForImpl), kind));\n    }\n  });\n  Object.defineProperty(globalObject, interfaceName, {\n    configurable: true,\n    writable: true,\n    value: URLSearchParams\n  });\n};\nconst Impl = require(\"./URLSearchParams-impl.js\");", "map": {"version": 3, "names": ["conversions", "require", "utils", "Function", "newObjectInRealm", "implSymbol", "ctorRegistrySymbol", "interfaceName", "exports", "is", "value", "isObject", "hasOwn", "Impl", "implementation", "isImpl", "convert", "globalObject", "context", "implForWrapper", "TypeError", "createDefaultIterator", "target", "kind", "ctorRegistry", "iteratorPrototype", "iterator", "Object", "create", "defineProperty", "iterInternalSymbol", "index", "configurable", "makeWrapper", "newTarget", "proto", "undefined", "prototype", "constructorArgs", "privateData", "wrapper", "setup", "createImpl", "_internalSetup", "wrapperSymbol", "init", "new", "exposed", "Set", "install", "globalNames", "some", "globalName", "has", "initCtorRegistry", "URLSearchParams", "constructor", "args", "curArg", "arguments", "Symbol", "V", "tmp", "nextItem", "globals", "push", "result", "key", "Reflect", "ownKeys", "desc", "getOwnPropertyDescriptor", "enumerable", "<PERSON><PERSON><PERSON>", "typedValue", "append", "name", "esValue", "length", "tryWrapperForImpl", "delete", "get", "getAll", "set", "sort", "toString", "keys", "values", "entries", "for<PERSON>ach", "callback", "thisArg", "pairs", "Array", "from", "i", "map", "call", "size", "defineProperties", "toStringTag", "writable", "define", "next", "internal", "len", "done", "pair", "iteratorResult"], "sources": ["C:/Users/<USER>/node_modules/mongodb-connection-string-url/node_modules/whatwg-url/lib/URLSearchParams.js"], "sourcesContent": ["\"use strict\";\n\nconst conversions = require(\"webidl-conversions\");\nconst utils = require(\"./utils.js\");\n\nconst Function = require(\"./Function.js\");\nconst newObjectInRealm = utils.newObjectInRealm;\nconst implSymbol = utils.implSymbol;\nconst ctorRegistrySymbol = utils.ctorRegistrySymbol;\n\nconst interfaceName = \"URLSearchParams\";\n\nexports.is = value => {\n  return utils.isObject(value) && utils.hasOwn(value, implSymbol) && value[implSymbol] instanceof Impl.implementation;\n};\nexports.isImpl = value => {\n  return utils.isObject(value) && value instanceof Impl.implementation;\n};\nexports.convert = (globalObject, value, { context = \"The provided value\" } = {}) => {\n  if (exports.is(value)) {\n    return utils.implForWrapper(value);\n  }\n  throw new globalObject.TypeError(`${context} is not of type 'URLSearchParams'.`);\n};\n\nexports.createDefaultIterator = (globalObject, target, kind) => {\n  const ctorRegistry = globalObject[ctorRegistrySymbol];\n  const iteratorPrototype = ctorRegistry[\"URLSearchParams Iterator\"];\n  const iterator = Object.create(iteratorPrototype);\n  Object.defineProperty(iterator, utils.iterInternalSymbol, {\n    value: { target, kind, index: 0 },\n    configurable: true\n  });\n  return iterator;\n};\n\nfunction makeWrapper(globalObject, newTarget) {\n  let proto;\n  if (newTarget !== undefined) {\n    proto = newTarget.prototype;\n  }\n\n  if (!utils.isObject(proto)) {\n    proto = globalObject[ctorRegistrySymbol][\"URLSearchParams\"].prototype;\n  }\n\n  return Object.create(proto);\n}\n\nexports.create = (globalObject, constructorArgs, privateData) => {\n  const wrapper = makeWrapper(globalObject);\n  return exports.setup(wrapper, globalObject, constructorArgs, privateData);\n};\n\nexports.createImpl = (globalObject, constructorArgs, privateData) => {\n  const wrapper = exports.create(globalObject, constructorArgs, privateData);\n  return utils.implForWrapper(wrapper);\n};\n\nexports._internalSetup = (wrapper, globalObject) => {};\n\nexports.setup = (wrapper, globalObject, constructorArgs = [], privateData = {}) => {\n  privateData.wrapper = wrapper;\n\n  exports._internalSetup(wrapper, globalObject);\n  Object.defineProperty(wrapper, implSymbol, {\n    value: new Impl.implementation(globalObject, constructorArgs, privateData),\n    configurable: true\n  });\n\n  wrapper[implSymbol][utils.wrapperSymbol] = wrapper;\n  if (Impl.init) {\n    Impl.init(wrapper[implSymbol]);\n  }\n  return wrapper;\n};\n\nexports.new = (globalObject, newTarget) => {\n  const wrapper = makeWrapper(globalObject, newTarget);\n\n  exports._internalSetup(wrapper, globalObject);\n  Object.defineProperty(wrapper, implSymbol, {\n    value: Object.create(Impl.implementation.prototype),\n    configurable: true\n  });\n\n  wrapper[implSymbol][utils.wrapperSymbol] = wrapper;\n  if (Impl.init) {\n    Impl.init(wrapper[implSymbol]);\n  }\n  return wrapper[implSymbol];\n};\n\nconst exposed = new Set([\"Window\", \"Worker\"]);\n\nexports.install = (globalObject, globalNames) => {\n  if (!globalNames.some(globalName => exposed.has(globalName))) {\n    return;\n  }\n\n  const ctorRegistry = utils.initCtorRegistry(globalObject);\n  class URLSearchParams {\n    constructor() {\n      const args = [];\n      {\n        let curArg = arguments[0];\n        if (curArg !== undefined) {\n          if (utils.isObject(curArg)) {\n            if (curArg[Symbol.iterator] !== undefined) {\n              if (!utils.isObject(curArg)) {\n                throw new globalObject.TypeError(\n                  \"Failed to construct 'URLSearchParams': parameter 1\" + \" sequence\" + \" is not an iterable object.\"\n                );\n              } else {\n                const V = [];\n                const tmp = curArg;\n                for (let nextItem of tmp) {\n                  if (!utils.isObject(nextItem)) {\n                    throw new globalObject.TypeError(\n                      \"Failed to construct 'URLSearchParams': parameter 1\" +\n                        \" sequence\" +\n                        \"'s element\" +\n                        \" is not an iterable object.\"\n                    );\n                  } else {\n                    const V = [];\n                    const tmp = nextItem;\n                    for (let nextItem of tmp) {\n                      nextItem = conversions[\"USVString\"](nextItem, {\n                        context:\n                          \"Failed to construct 'URLSearchParams': parameter 1\" +\n                          \" sequence\" +\n                          \"'s element\" +\n                          \"'s element\",\n                        globals: globalObject\n                      });\n\n                      V.push(nextItem);\n                    }\n                    nextItem = V;\n                  }\n\n                  V.push(nextItem);\n                }\n                curArg = V;\n              }\n            } else {\n              if (!utils.isObject(curArg)) {\n                throw new globalObject.TypeError(\n                  \"Failed to construct 'URLSearchParams': parameter 1\" + \" record\" + \" is not an object.\"\n                );\n              } else {\n                const result = Object.create(null);\n                for (const key of Reflect.ownKeys(curArg)) {\n                  const desc = Object.getOwnPropertyDescriptor(curArg, key);\n                  if (desc && desc.enumerable) {\n                    let typedKey = key;\n\n                    typedKey = conversions[\"USVString\"](typedKey, {\n                      context: \"Failed to construct 'URLSearchParams': parameter 1\" + \" record\" + \"'s key\",\n                      globals: globalObject\n                    });\n\n                    let typedValue = curArg[key];\n\n                    typedValue = conversions[\"USVString\"](typedValue, {\n                      context: \"Failed to construct 'URLSearchParams': parameter 1\" + \" record\" + \"'s value\",\n                      globals: globalObject\n                    });\n\n                    result[typedKey] = typedValue;\n                  }\n                }\n                curArg = result;\n              }\n            }\n          } else {\n            curArg = conversions[\"USVString\"](curArg, {\n              context: \"Failed to construct 'URLSearchParams': parameter 1\",\n              globals: globalObject\n            });\n          }\n        } else {\n          curArg = \"\";\n        }\n        args.push(curArg);\n      }\n      return exports.setup(Object.create(new.target.prototype), globalObject, args);\n    }\n\n    append(name, value) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\n          \"'append' called on an object that is not a valid instance of URLSearchParams.\"\n        );\n      }\n\n      if (arguments.length < 2) {\n        throw new globalObject.TypeError(\n          `Failed to execute 'append' on 'URLSearchParams': 2 arguments required, but only ${arguments.length} present.`\n        );\n      }\n      const args = [];\n      {\n        let curArg = arguments[0];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'append' on 'URLSearchParams': parameter 1\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      {\n        let curArg = arguments[1];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'append' on 'URLSearchParams': parameter 2\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      return utils.tryWrapperForImpl(esValue[implSymbol].append(...args));\n    }\n\n    delete(name) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\n          \"'delete' called on an object that is not a valid instance of URLSearchParams.\"\n        );\n      }\n\n      if (arguments.length < 1) {\n        throw new globalObject.TypeError(\n          `Failed to execute 'delete' on 'URLSearchParams': 1 argument required, but only ${arguments.length} present.`\n        );\n      }\n      const args = [];\n      {\n        let curArg = arguments[0];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'delete' on 'URLSearchParams': parameter 1\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      {\n        let curArg = arguments[1];\n        if (curArg !== undefined) {\n          curArg = conversions[\"USVString\"](curArg, {\n            context: \"Failed to execute 'delete' on 'URLSearchParams': parameter 2\",\n            globals: globalObject\n          });\n        }\n        args.push(curArg);\n      }\n      return utils.tryWrapperForImpl(esValue[implSymbol].delete(...args));\n    }\n\n    get(name) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get' called on an object that is not a valid instance of URLSearchParams.\");\n      }\n\n      if (arguments.length < 1) {\n        throw new globalObject.TypeError(\n          `Failed to execute 'get' on 'URLSearchParams': 1 argument required, but only ${arguments.length} present.`\n        );\n      }\n      const args = [];\n      {\n        let curArg = arguments[0];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'get' on 'URLSearchParams': parameter 1\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      return esValue[implSymbol].get(...args);\n    }\n\n    getAll(name) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\n          \"'getAll' called on an object that is not a valid instance of URLSearchParams.\"\n        );\n      }\n\n      if (arguments.length < 1) {\n        throw new globalObject.TypeError(\n          `Failed to execute 'getAll' on 'URLSearchParams': 1 argument required, but only ${arguments.length} present.`\n        );\n      }\n      const args = [];\n      {\n        let curArg = arguments[0];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'getAll' on 'URLSearchParams': parameter 1\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      return utils.tryWrapperForImpl(esValue[implSymbol].getAll(...args));\n    }\n\n    has(name) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'has' called on an object that is not a valid instance of URLSearchParams.\");\n      }\n\n      if (arguments.length < 1) {\n        throw new globalObject.TypeError(\n          `Failed to execute 'has' on 'URLSearchParams': 1 argument required, but only ${arguments.length} present.`\n        );\n      }\n      const args = [];\n      {\n        let curArg = arguments[0];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'has' on 'URLSearchParams': parameter 1\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      {\n        let curArg = arguments[1];\n        if (curArg !== undefined) {\n          curArg = conversions[\"USVString\"](curArg, {\n            context: \"Failed to execute 'has' on 'URLSearchParams': parameter 2\",\n            globals: globalObject\n          });\n        }\n        args.push(curArg);\n      }\n      return esValue[implSymbol].has(...args);\n    }\n\n    set(name, value) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set' called on an object that is not a valid instance of URLSearchParams.\");\n      }\n\n      if (arguments.length < 2) {\n        throw new globalObject.TypeError(\n          `Failed to execute 'set' on 'URLSearchParams': 2 arguments required, but only ${arguments.length} present.`\n        );\n      }\n      const args = [];\n      {\n        let curArg = arguments[0];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'set' on 'URLSearchParams': parameter 1\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      {\n        let curArg = arguments[1];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'set' on 'URLSearchParams': parameter 2\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      return utils.tryWrapperForImpl(esValue[implSymbol].set(...args));\n    }\n\n    sort() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'sort' called on an object that is not a valid instance of URLSearchParams.\");\n      }\n\n      return utils.tryWrapperForImpl(esValue[implSymbol].sort());\n    }\n\n    toString() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\n          \"'toString' called on an object that is not a valid instance of URLSearchParams.\"\n        );\n      }\n\n      return esValue[implSymbol].toString();\n    }\n\n    keys() {\n      if (!exports.is(this)) {\n        throw new globalObject.TypeError(\"'keys' called on an object that is not a valid instance of URLSearchParams.\");\n      }\n      return exports.createDefaultIterator(globalObject, this, \"key\");\n    }\n\n    values() {\n      if (!exports.is(this)) {\n        throw new globalObject.TypeError(\n          \"'values' called on an object that is not a valid instance of URLSearchParams.\"\n        );\n      }\n      return exports.createDefaultIterator(globalObject, this, \"value\");\n    }\n\n    entries() {\n      if (!exports.is(this)) {\n        throw new globalObject.TypeError(\n          \"'entries' called on an object that is not a valid instance of URLSearchParams.\"\n        );\n      }\n      return exports.createDefaultIterator(globalObject, this, \"key+value\");\n    }\n\n    forEach(callback) {\n      if (!exports.is(this)) {\n        throw new globalObject.TypeError(\n          \"'forEach' called on an object that is not a valid instance of URLSearchParams.\"\n        );\n      }\n      if (arguments.length < 1) {\n        throw new globalObject.TypeError(\n          \"Failed to execute 'forEach' on 'iterable': 1 argument required, but only 0 present.\"\n        );\n      }\n      callback = Function.convert(globalObject, callback, {\n        context: \"Failed to execute 'forEach' on 'iterable': The callback provided as parameter 1\"\n      });\n      const thisArg = arguments[1];\n      let pairs = Array.from(this[implSymbol]);\n      let i = 0;\n      while (i < pairs.length) {\n        const [key, value] = pairs[i].map(utils.tryWrapperForImpl);\n        callback.call(thisArg, value, key, this);\n        pairs = Array.from(this[implSymbol]);\n        i++;\n      }\n    }\n\n    get size() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\n          \"'get size' called on an object that is not a valid instance of URLSearchParams.\"\n        );\n      }\n\n      return esValue[implSymbol][\"size\"];\n    }\n  }\n  Object.defineProperties(URLSearchParams.prototype, {\n    append: { enumerable: true },\n    delete: { enumerable: true },\n    get: { enumerable: true },\n    getAll: { enumerable: true },\n    has: { enumerable: true },\n    set: { enumerable: true },\n    sort: { enumerable: true },\n    toString: { enumerable: true },\n    keys: { enumerable: true },\n    values: { enumerable: true },\n    entries: { enumerable: true },\n    forEach: { enumerable: true },\n    size: { enumerable: true },\n    [Symbol.toStringTag]: { value: \"URLSearchParams\", configurable: true },\n    [Symbol.iterator]: { value: URLSearchParams.prototype.entries, configurable: true, writable: true }\n  });\n  ctorRegistry[interfaceName] = URLSearchParams;\n\n  ctorRegistry[\"URLSearchParams Iterator\"] = Object.create(ctorRegistry[\"%IteratorPrototype%\"], {\n    [Symbol.toStringTag]: {\n      configurable: true,\n      value: \"URLSearchParams Iterator\"\n    }\n  });\n  utils.define(ctorRegistry[\"URLSearchParams Iterator\"], {\n    next() {\n      const internal = this && this[utils.iterInternalSymbol];\n      if (!internal) {\n        throw new globalObject.TypeError(\"next() called on a value that is not a URLSearchParams iterator object\");\n      }\n\n      const { target, kind, index } = internal;\n      const values = Array.from(target[implSymbol]);\n      const len = values.length;\n      if (index >= len) {\n        return newObjectInRealm(globalObject, { value: undefined, done: true });\n      }\n\n      const pair = values[index];\n      internal.index = index + 1;\n      return newObjectInRealm(globalObject, utils.iteratorResult(pair.map(utils.tryWrapperForImpl), kind));\n    }\n  });\n\n  Object.defineProperty(globalObject, interfaceName, {\n    configurable: true,\n    writable: true,\n    value: URLSearchParams\n  });\n};\n\nconst Impl = require(\"./URLSearchParams-impl.js\");\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,WAAW,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AACjD,MAAMC,KAAK,GAAGD,OAAO,CAAC,YAAY,CAAC;AAEnC,MAAME,QAAQ,GAAGF,OAAO,CAAC,eAAe,CAAC;AACzC,MAAMG,gBAAgB,GAAGF,KAAK,CAACE,gBAAgB;AAC/C,MAAMC,UAAU,GAAGH,KAAK,CAACG,UAAU;AACnC,MAAMC,kBAAkB,GAAGJ,KAAK,CAACI,kBAAkB;AAEnD,MAAMC,aAAa,GAAG,iBAAiB;AAEvCC,OAAO,CAACC,EAAE,GAAGC,KAAK,IAAI;EACpB,OAAOR,KAAK,CAACS,QAAQ,CAACD,KAAK,CAAC,IAAIR,KAAK,CAACU,MAAM,CAACF,KAAK,EAAEL,UAAU,CAAC,IAAIK,KAAK,CAACL,UAAU,CAAC,YAAYQ,IAAI,CAACC,cAAc;AACrH,CAAC;AACDN,OAAO,CAACO,MAAM,GAAGL,KAAK,IAAI;EACxB,OAAOR,KAAK,CAACS,QAAQ,CAACD,KAAK,CAAC,IAAIA,KAAK,YAAYG,IAAI,CAACC,cAAc;AACtE,CAAC;AACDN,OAAO,CAACQ,OAAO,GAAG,CAACC,YAAY,EAAEP,KAAK,EAAE;EAAEQ,OAAO,GAAG;AAAqB,CAAC,GAAG,CAAC,CAAC,KAAK;EAClF,IAAIV,OAAO,CAACC,EAAE,CAACC,KAAK,CAAC,EAAE;IACrB,OAAOR,KAAK,CAACiB,cAAc,CAACT,KAAK,CAAC;EACpC;EACA,MAAM,IAAIO,YAAY,CAACG,SAAS,CAAC,GAAGF,OAAO,oCAAoC,CAAC;AAClF,CAAC;AAEDV,OAAO,CAACa,qBAAqB,GAAG,CAACJ,YAAY,EAAEK,MAAM,EAAEC,IAAI,KAAK;EAC9D,MAAMC,YAAY,GAAGP,YAAY,CAACX,kBAAkB,CAAC;EACrD,MAAMmB,iBAAiB,GAAGD,YAAY,CAAC,0BAA0B,CAAC;EAClE,MAAME,QAAQ,GAAGC,MAAM,CAACC,MAAM,CAACH,iBAAiB,CAAC;EACjDE,MAAM,CAACE,cAAc,CAACH,QAAQ,EAAExB,KAAK,CAAC4B,kBAAkB,EAAE;IACxDpB,KAAK,EAAE;MAAEY,MAAM;MAAEC,IAAI;MAAEQ,KAAK,EAAE;IAAE,CAAC;IACjCC,YAAY,EAAE;EAChB,CAAC,CAAC;EACF,OAAON,QAAQ;AACjB,CAAC;AAED,SAASO,WAAWA,CAAChB,YAAY,EAAEiB,SAAS,EAAE;EAC5C,IAAIC,KAAK;EACT,IAAID,SAAS,KAAKE,SAAS,EAAE;IAC3BD,KAAK,GAAGD,SAAS,CAACG,SAAS;EAC7B;EAEA,IAAI,CAACnC,KAAK,CAACS,QAAQ,CAACwB,KAAK,CAAC,EAAE;IAC1BA,KAAK,GAAGlB,YAAY,CAACX,kBAAkB,CAAC,CAAC,iBAAiB,CAAC,CAAC+B,SAAS;EACvE;EAEA,OAAOV,MAAM,CAACC,MAAM,CAACO,KAAK,CAAC;AAC7B;AAEA3B,OAAO,CAACoB,MAAM,GAAG,CAACX,YAAY,EAAEqB,eAAe,EAAEC,WAAW,KAAK;EAC/D,MAAMC,OAAO,GAAGP,WAAW,CAAChB,YAAY,CAAC;EACzC,OAAOT,OAAO,CAACiC,KAAK,CAACD,OAAO,EAAEvB,YAAY,EAAEqB,eAAe,EAAEC,WAAW,CAAC;AAC3E,CAAC;AAED/B,OAAO,CAACkC,UAAU,GAAG,CAACzB,YAAY,EAAEqB,eAAe,EAAEC,WAAW,KAAK;EACnE,MAAMC,OAAO,GAAGhC,OAAO,CAACoB,MAAM,CAACX,YAAY,EAAEqB,eAAe,EAAEC,WAAW,CAAC;EAC1E,OAAOrC,KAAK,CAACiB,cAAc,CAACqB,OAAO,CAAC;AACtC,CAAC;AAEDhC,OAAO,CAACmC,cAAc,GAAG,CAACH,OAAO,EAAEvB,YAAY,KAAK,CAAC,CAAC;AAEtDT,OAAO,CAACiC,KAAK,GAAG,CAACD,OAAO,EAAEvB,YAAY,EAAEqB,eAAe,GAAG,EAAE,EAAEC,WAAW,GAAG,CAAC,CAAC,KAAK;EACjFA,WAAW,CAACC,OAAO,GAAGA,OAAO;EAE7BhC,OAAO,CAACmC,cAAc,CAACH,OAAO,EAAEvB,YAAY,CAAC;EAC7CU,MAAM,CAACE,cAAc,CAACW,OAAO,EAAEnC,UAAU,EAAE;IACzCK,KAAK,EAAE,IAAIG,IAAI,CAACC,cAAc,CAACG,YAAY,EAAEqB,eAAe,EAAEC,WAAW,CAAC;IAC1EP,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFQ,OAAO,CAACnC,UAAU,CAAC,CAACH,KAAK,CAAC0C,aAAa,CAAC,GAAGJ,OAAO;EAClD,IAAI3B,IAAI,CAACgC,IAAI,EAAE;IACbhC,IAAI,CAACgC,IAAI,CAACL,OAAO,CAACnC,UAAU,CAAC,CAAC;EAChC;EACA,OAAOmC,OAAO;AAChB,CAAC;AAEDhC,OAAO,CAACsC,GAAG,GAAG,CAAC7B,YAAY,EAAEiB,SAAS,KAAK;EACzC,MAAMM,OAAO,GAAGP,WAAW,CAAChB,YAAY,EAAEiB,SAAS,CAAC;EAEpD1B,OAAO,CAACmC,cAAc,CAACH,OAAO,EAAEvB,YAAY,CAAC;EAC7CU,MAAM,CAACE,cAAc,CAACW,OAAO,EAAEnC,UAAU,EAAE;IACzCK,KAAK,EAAEiB,MAAM,CAACC,MAAM,CAACf,IAAI,CAACC,cAAc,CAACuB,SAAS,CAAC;IACnDL,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFQ,OAAO,CAACnC,UAAU,CAAC,CAACH,KAAK,CAAC0C,aAAa,CAAC,GAAGJ,OAAO;EAClD,IAAI3B,IAAI,CAACgC,IAAI,EAAE;IACbhC,IAAI,CAACgC,IAAI,CAACL,OAAO,CAACnC,UAAU,CAAC,CAAC;EAChC;EACA,OAAOmC,OAAO,CAACnC,UAAU,CAAC;AAC5B,CAAC;AAED,MAAM0C,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAE7CxC,OAAO,CAACyC,OAAO,GAAG,CAAChC,YAAY,EAAEiC,WAAW,KAAK;EAC/C,IAAI,CAACA,WAAW,CAACC,IAAI,CAACC,UAAU,IAAIL,OAAO,CAACM,GAAG,CAACD,UAAU,CAAC,CAAC,EAAE;IAC5D;EACF;EAEA,MAAM5B,YAAY,GAAGtB,KAAK,CAACoD,gBAAgB,CAACrC,YAAY,CAAC;EACzD,MAAMsC,eAAe,CAAC;IACpBC,WAAWA,CAAA,EAAG;MACZ,MAAMC,IAAI,GAAG,EAAE;MACf;QACE,IAAIC,MAAM,GAAGC,SAAS,CAAC,CAAC,CAAC;QACzB,IAAID,MAAM,KAAKtB,SAAS,EAAE;UACxB,IAAIlC,KAAK,CAACS,QAAQ,CAAC+C,MAAM,CAAC,EAAE;YAC1B,IAAIA,MAAM,CAACE,MAAM,CAAClC,QAAQ,CAAC,KAAKU,SAAS,EAAE;cACzC,IAAI,CAAClC,KAAK,CAACS,QAAQ,CAAC+C,MAAM,CAAC,EAAE;gBAC3B,MAAM,IAAIzC,YAAY,CAACG,SAAS,CAC9B,oDAAoD,GAAG,WAAW,GAAG,6BACvE,CAAC;cACH,CAAC,MAAM;gBACL,MAAMyC,CAAC,GAAG,EAAE;gBACZ,MAAMC,GAAG,GAAGJ,MAAM;gBAClB,KAAK,IAAIK,QAAQ,IAAID,GAAG,EAAE;kBACxB,IAAI,CAAC5D,KAAK,CAACS,QAAQ,CAACoD,QAAQ,CAAC,EAAE;oBAC7B,MAAM,IAAI9C,YAAY,CAACG,SAAS,CAC9B,oDAAoD,GAClD,WAAW,GACX,YAAY,GACZ,6BACJ,CAAC;kBACH,CAAC,MAAM;oBACL,MAAMyC,CAAC,GAAG,EAAE;oBACZ,MAAMC,GAAG,GAAGC,QAAQ;oBACpB,KAAK,IAAIA,QAAQ,IAAID,GAAG,EAAE;sBACxBC,QAAQ,GAAG/D,WAAW,CAAC,WAAW,CAAC,CAAC+D,QAAQ,EAAE;wBAC5C7C,OAAO,EACL,oDAAoD,GACpD,WAAW,GACX,YAAY,GACZ,YAAY;wBACd8C,OAAO,EAAE/C;sBACX,CAAC,CAAC;sBAEF4C,CAAC,CAACI,IAAI,CAACF,QAAQ,CAAC;oBAClB;oBACAA,QAAQ,GAAGF,CAAC;kBACd;kBAEAA,CAAC,CAACI,IAAI,CAACF,QAAQ,CAAC;gBAClB;gBACAL,MAAM,GAAGG,CAAC;cACZ;YACF,CAAC,MAAM;cACL,IAAI,CAAC3D,KAAK,CAACS,QAAQ,CAAC+C,MAAM,CAAC,EAAE;gBAC3B,MAAM,IAAIzC,YAAY,CAACG,SAAS,CAC9B,oDAAoD,GAAG,SAAS,GAAG,oBACrE,CAAC;cACH,CAAC,MAAM;gBACL,MAAM8C,MAAM,GAAGvC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;gBAClC,KAAK,MAAMuC,GAAG,IAAIC,OAAO,CAACC,OAAO,CAACX,MAAM,CAAC,EAAE;kBACzC,MAAMY,IAAI,GAAG3C,MAAM,CAAC4C,wBAAwB,CAACb,MAAM,EAAES,GAAG,CAAC;kBACzD,IAAIG,IAAI,IAAIA,IAAI,CAACE,UAAU,EAAE;oBAC3B,IAAIC,QAAQ,GAAGN,GAAG;oBAElBM,QAAQ,GAAGzE,WAAW,CAAC,WAAW,CAAC,CAACyE,QAAQ,EAAE;sBAC5CvD,OAAO,EAAE,oDAAoD,GAAG,SAAS,GAAG,QAAQ;sBACpF8C,OAAO,EAAE/C;oBACX,CAAC,CAAC;oBAEF,IAAIyD,UAAU,GAAGhB,MAAM,CAACS,GAAG,CAAC;oBAE5BO,UAAU,GAAG1E,WAAW,CAAC,WAAW,CAAC,CAAC0E,UAAU,EAAE;sBAChDxD,OAAO,EAAE,oDAAoD,GAAG,SAAS,GAAG,UAAU;sBACtF8C,OAAO,EAAE/C;oBACX,CAAC,CAAC;oBAEFiD,MAAM,CAACO,QAAQ,CAAC,GAAGC,UAAU;kBAC/B;gBACF;gBACAhB,MAAM,GAAGQ,MAAM;cACjB;YACF;UACF,CAAC,MAAM;YACLR,MAAM,GAAG1D,WAAW,CAAC,WAAW,CAAC,CAAC0D,MAAM,EAAE;cACxCxC,OAAO,EAAE,oDAAoD;cAC7D8C,OAAO,EAAE/C;YACX,CAAC,CAAC;UACJ;QACF,CAAC,MAAM;UACLyC,MAAM,GAAG,EAAE;QACb;QACAD,IAAI,CAACQ,IAAI,CAACP,MAAM,CAAC;MACnB;MACA,OAAOlD,OAAO,CAACiC,KAAK,CAACd,MAAM,CAACC,MAAM,CAACkB,GAAG,CAACxB,MAAM,CAACe,SAAS,CAAC,EAAEpB,YAAY,EAAEwC,IAAI,CAAC;IAC/E;IAEAkB,MAAMA,CAACC,IAAI,EAAElE,KAAK,EAAE;MAClB,MAAMmE,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKzC,SAAS,GAAG,IAAI,GAAGnB,YAAY;MACzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACoE,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI5D,YAAY,CAACG,SAAS,CAC9B,+EACF,CAAC;MACH;MAEA,IAAIuC,SAAS,CAACmB,MAAM,GAAG,CAAC,EAAE;QACxB,MAAM,IAAI7D,YAAY,CAACG,SAAS,CAC9B,mFAAmFuC,SAAS,CAACmB,MAAM,WACrG,CAAC;MACH;MACA,MAAMrB,IAAI,GAAG,EAAE;MACf;QACE,IAAIC,MAAM,GAAGC,SAAS,CAAC,CAAC,CAAC;QACzBD,MAAM,GAAG1D,WAAW,CAAC,WAAW,CAAC,CAAC0D,MAAM,EAAE;UACxCxC,OAAO,EAAE,8DAA8D;UACvE8C,OAAO,EAAE/C;QACX,CAAC,CAAC;QACFwC,IAAI,CAACQ,IAAI,CAACP,MAAM,CAAC;MACnB;MACA;QACE,IAAIA,MAAM,GAAGC,SAAS,CAAC,CAAC,CAAC;QACzBD,MAAM,GAAG1D,WAAW,CAAC,WAAW,CAAC,CAAC0D,MAAM,EAAE;UACxCxC,OAAO,EAAE,8DAA8D;UACvE8C,OAAO,EAAE/C;QACX,CAAC,CAAC;QACFwC,IAAI,CAACQ,IAAI,CAACP,MAAM,CAAC;MACnB;MACA,OAAOxD,KAAK,CAAC6E,iBAAiB,CAACF,OAAO,CAACxE,UAAU,CAAC,CAACsE,MAAM,CAAC,GAAGlB,IAAI,CAAC,CAAC;IACrE;IAEAuB,MAAMA,CAACJ,IAAI,EAAE;MACX,MAAMC,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKzC,SAAS,GAAG,IAAI,GAAGnB,YAAY;MACzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACoE,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI5D,YAAY,CAACG,SAAS,CAC9B,+EACF,CAAC;MACH;MAEA,IAAIuC,SAAS,CAACmB,MAAM,GAAG,CAAC,EAAE;QACxB,MAAM,IAAI7D,YAAY,CAACG,SAAS,CAC9B,kFAAkFuC,SAAS,CAACmB,MAAM,WACpG,CAAC;MACH;MACA,MAAMrB,IAAI,GAAG,EAAE;MACf;QACE,IAAIC,MAAM,GAAGC,SAAS,CAAC,CAAC,CAAC;QACzBD,MAAM,GAAG1D,WAAW,CAAC,WAAW,CAAC,CAAC0D,MAAM,EAAE;UACxCxC,OAAO,EAAE,8DAA8D;UACvE8C,OAAO,EAAE/C;QACX,CAAC,CAAC;QACFwC,IAAI,CAACQ,IAAI,CAACP,MAAM,CAAC;MACnB;MACA;QACE,IAAIA,MAAM,GAAGC,SAAS,CAAC,CAAC,CAAC;QACzB,IAAID,MAAM,KAAKtB,SAAS,EAAE;UACxBsB,MAAM,GAAG1D,WAAW,CAAC,WAAW,CAAC,CAAC0D,MAAM,EAAE;YACxCxC,OAAO,EAAE,8DAA8D;YACvE8C,OAAO,EAAE/C;UACX,CAAC,CAAC;QACJ;QACAwC,IAAI,CAACQ,IAAI,CAACP,MAAM,CAAC;MACnB;MACA,OAAOxD,KAAK,CAAC6E,iBAAiB,CAACF,OAAO,CAACxE,UAAU,CAAC,CAAC2E,MAAM,CAAC,GAAGvB,IAAI,CAAC,CAAC;IACrE;IAEAwB,GAAGA,CAACL,IAAI,EAAE;MACR,MAAMC,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKzC,SAAS,GAAG,IAAI,GAAGnB,YAAY;MACzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACoE,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI5D,YAAY,CAACG,SAAS,CAAC,4EAA4E,CAAC;MAChH;MAEA,IAAIuC,SAAS,CAACmB,MAAM,GAAG,CAAC,EAAE;QACxB,MAAM,IAAI7D,YAAY,CAACG,SAAS,CAC9B,+EAA+EuC,SAAS,CAACmB,MAAM,WACjG,CAAC;MACH;MACA,MAAMrB,IAAI,GAAG,EAAE;MACf;QACE,IAAIC,MAAM,GAAGC,SAAS,CAAC,CAAC,CAAC;QACzBD,MAAM,GAAG1D,WAAW,CAAC,WAAW,CAAC,CAAC0D,MAAM,EAAE;UACxCxC,OAAO,EAAE,2DAA2D;UACpE8C,OAAO,EAAE/C;QACX,CAAC,CAAC;QACFwC,IAAI,CAACQ,IAAI,CAACP,MAAM,CAAC;MACnB;MACA,OAAOmB,OAAO,CAACxE,UAAU,CAAC,CAAC4E,GAAG,CAAC,GAAGxB,IAAI,CAAC;IACzC;IAEAyB,MAAMA,CAACN,IAAI,EAAE;MACX,MAAMC,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKzC,SAAS,GAAG,IAAI,GAAGnB,YAAY;MACzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACoE,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI5D,YAAY,CAACG,SAAS,CAC9B,+EACF,CAAC;MACH;MAEA,IAAIuC,SAAS,CAACmB,MAAM,GAAG,CAAC,EAAE;QACxB,MAAM,IAAI7D,YAAY,CAACG,SAAS,CAC9B,kFAAkFuC,SAAS,CAACmB,MAAM,WACpG,CAAC;MACH;MACA,MAAMrB,IAAI,GAAG,EAAE;MACf;QACE,IAAIC,MAAM,GAAGC,SAAS,CAAC,CAAC,CAAC;QACzBD,MAAM,GAAG1D,WAAW,CAAC,WAAW,CAAC,CAAC0D,MAAM,EAAE;UACxCxC,OAAO,EAAE,8DAA8D;UACvE8C,OAAO,EAAE/C;QACX,CAAC,CAAC;QACFwC,IAAI,CAACQ,IAAI,CAACP,MAAM,CAAC;MACnB;MACA,OAAOxD,KAAK,CAAC6E,iBAAiB,CAACF,OAAO,CAACxE,UAAU,CAAC,CAAC6E,MAAM,CAAC,GAAGzB,IAAI,CAAC,CAAC;IACrE;IAEAJ,GAAGA,CAACuB,IAAI,EAAE;MACR,MAAMC,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKzC,SAAS,GAAG,IAAI,GAAGnB,YAAY;MACzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACoE,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI5D,YAAY,CAACG,SAAS,CAAC,4EAA4E,CAAC;MAChH;MAEA,IAAIuC,SAAS,CAACmB,MAAM,GAAG,CAAC,EAAE;QACxB,MAAM,IAAI7D,YAAY,CAACG,SAAS,CAC9B,+EAA+EuC,SAAS,CAACmB,MAAM,WACjG,CAAC;MACH;MACA,MAAMrB,IAAI,GAAG,EAAE;MACf;QACE,IAAIC,MAAM,GAAGC,SAAS,CAAC,CAAC,CAAC;QACzBD,MAAM,GAAG1D,WAAW,CAAC,WAAW,CAAC,CAAC0D,MAAM,EAAE;UACxCxC,OAAO,EAAE,2DAA2D;UACpE8C,OAAO,EAAE/C;QACX,CAAC,CAAC;QACFwC,IAAI,CAACQ,IAAI,CAACP,MAAM,CAAC;MACnB;MACA;QACE,IAAIA,MAAM,GAAGC,SAAS,CAAC,CAAC,CAAC;QACzB,IAAID,MAAM,KAAKtB,SAAS,EAAE;UACxBsB,MAAM,GAAG1D,WAAW,CAAC,WAAW,CAAC,CAAC0D,MAAM,EAAE;YACxCxC,OAAO,EAAE,2DAA2D;YACpE8C,OAAO,EAAE/C;UACX,CAAC,CAAC;QACJ;QACAwC,IAAI,CAACQ,IAAI,CAACP,MAAM,CAAC;MACnB;MACA,OAAOmB,OAAO,CAACxE,UAAU,CAAC,CAACgD,GAAG,CAAC,GAAGI,IAAI,CAAC;IACzC;IAEA0B,GAAGA,CAACP,IAAI,EAAElE,KAAK,EAAE;MACf,MAAMmE,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKzC,SAAS,GAAG,IAAI,GAAGnB,YAAY;MACzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACoE,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI5D,YAAY,CAACG,SAAS,CAAC,4EAA4E,CAAC;MAChH;MAEA,IAAIuC,SAAS,CAACmB,MAAM,GAAG,CAAC,EAAE;QACxB,MAAM,IAAI7D,YAAY,CAACG,SAAS,CAC9B,gFAAgFuC,SAAS,CAACmB,MAAM,WAClG,CAAC;MACH;MACA,MAAMrB,IAAI,GAAG,EAAE;MACf;QACE,IAAIC,MAAM,GAAGC,SAAS,CAAC,CAAC,CAAC;QACzBD,MAAM,GAAG1D,WAAW,CAAC,WAAW,CAAC,CAAC0D,MAAM,EAAE;UACxCxC,OAAO,EAAE,2DAA2D;UACpE8C,OAAO,EAAE/C;QACX,CAAC,CAAC;QACFwC,IAAI,CAACQ,IAAI,CAACP,MAAM,CAAC;MACnB;MACA;QACE,IAAIA,MAAM,GAAGC,SAAS,CAAC,CAAC,CAAC;QACzBD,MAAM,GAAG1D,WAAW,CAAC,WAAW,CAAC,CAAC0D,MAAM,EAAE;UACxCxC,OAAO,EAAE,2DAA2D;UACpE8C,OAAO,EAAE/C;QACX,CAAC,CAAC;QACFwC,IAAI,CAACQ,IAAI,CAACP,MAAM,CAAC;MACnB;MACA,OAAOxD,KAAK,CAAC6E,iBAAiB,CAACF,OAAO,CAACxE,UAAU,CAAC,CAAC8E,GAAG,CAAC,GAAG1B,IAAI,CAAC,CAAC;IAClE;IAEA2B,IAAIA,CAAA,EAAG;MACL,MAAMP,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKzC,SAAS,GAAG,IAAI,GAAGnB,YAAY;MACzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACoE,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI5D,YAAY,CAACG,SAAS,CAAC,6EAA6E,CAAC;MACjH;MAEA,OAAOlB,KAAK,CAAC6E,iBAAiB,CAACF,OAAO,CAACxE,UAAU,CAAC,CAAC+E,IAAI,CAAC,CAAC,CAAC;IAC5D;IAEAC,QAAQA,CAAA,EAAG;MACT,MAAMR,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKzC,SAAS,GAAG,IAAI,GAAGnB,YAAY;MACzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACoE,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI5D,YAAY,CAACG,SAAS,CAC9B,iFACF,CAAC;MACH;MAEA,OAAOyD,OAAO,CAACxE,UAAU,CAAC,CAACgF,QAAQ,CAAC,CAAC;IACvC;IAEAC,IAAIA,CAAA,EAAG;MACL,IAAI,CAAC9E,OAAO,CAACC,EAAE,CAAC,IAAI,CAAC,EAAE;QACrB,MAAM,IAAIQ,YAAY,CAACG,SAAS,CAAC,6EAA6E,CAAC;MACjH;MACA,OAAOZ,OAAO,CAACa,qBAAqB,CAACJ,YAAY,EAAE,IAAI,EAAE,KAAK,CAAC;IACjE;IAEAsE,MAAMA,CAAA,EAAG;MACP,IAAI,CAAC/E,OAAO,CAACC,EAAE,CAAC,IAAI,CAAC,EAAE;QACrB,MAAM,IAAIQ,YAAY,CAACG,SAAS,CAC9B,+EACF,CAAC;MACH;MACA,OAAOZ,OAAO,CAACa,qBAAqB,CAACJ,YAAY,EAAE,IAAI,EAAE,OAAO,CAAC;IACnE;IAEAuE,OAAOA,CAAA,EAAG;MACR,IAAI,CAAChF,OAAO,CAACC,EAAE,CAAC,IAAI,CAAC,EAAE;QACrB,MAAM,IAAIQ,YAAY,CAACG,SAAS,CAC9B,gFACF,CAAC;MACH;MACA,OAAOZ,OAAO,CAACa,qBAAqB,CAACJ,YAAY,EAAE,IAAI,EAAE,WAAW,CAAC;IACvE;IAEAwE,OAAOA,CAACC,QAAQ,EAAE;MAChB,IAAI,CAAClF,OAAO,CAACC,EAAE,CAAC,IAAI,CAAC,EAAE;QACrB,MAAM,IAAIQ,YAAY,CAACG,SAAS,CAC9B,gFACF,CAAC;MACH;MACA,IAAIuC,SAAS,CAACmB,MAAM,GAAG,CAAC,EAAE;QACxB,MAAM,IAAI7D,YAAY,CAACG,SAAS,CAC9B,qFACF,CAAC;MACH;MACAsE,QAAQ,GAAGvF,QAAQ,CAACa,OAAO,CAACC,YAAY,EAAEyE,QAAQ,EAAE;QAClDxE,OAAO,EAAE;MACX,CAAC,CAAC;MACF,MAAMyE,OAAO,GAAGhC,SAAS,CAAC,CAAC,CAAC;MAC5B,IAAIiC,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACzF,UAAU,CAAC,CAAC;MACxC,IAAI0F,CAAC,GAAG,CAAC;MACT,OAAOA,CAAC,GAAGH,KAAK,CAACd,MAAM,EAAE;QACvB,MAAM,CAACX,GAAG,EAAEzD,KAAK,CAAC,GAAGkF,KAAK,CAACG,CAAC,CAAC,CAACC,GAAG,CAAC9F,KAAK,CAAC6E,iBAAiB,CAAC;QAC1DW,QAAQ,CAACO,IAAI,CAACN,OAAO,EAAEjF,KAAK,EAAEyD,GAAG,EAAE,IAAI,CAAC;QACxCyB,KAAK,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACzF,UAAU,CAAC,CAAC;QACpC0F,CAAC,EAAE;MACL;IACF;IAEA,IAAIG,IAAIA,CAAA,EAAG;MACT,MAAMrB,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKzC,SAAS,GAAG,IAAI,GAAGnB,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACoE,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI5D,YAAY,CAACG,SAAS,CAC9B,iFACF,CAAC;MACH;MAEA,OAAOyD,OAAO,CAACxE,UAAU,CAAC,CAAC,MAAM,CAAC;IACpC;EACF;EACAsB,MAAM,CAACwE,gBAAgB,CAAC5C,eAAe,CAAClB,SAAS,EAAE;IACjDsC,MAAM,EAAE;MAAEH,UAAU,EAAE;IAAK,CAAC;IAC5BQ,MAAM,EAAE;MAAER,UAAU,EAAE;IAAK,CAAC;IAC5BS,GAAG,EAAE;MAAET,UAAU,EAAE;IAAK,CAAC;IACzBU,MAAM,EAAE;MAAEV,UAAU,EAAE;IAAK,CAAC;IAC5BnB,GAAG,EAAE;MAAEmB,UAAU,EAAE;IAAK,CAAC;IACzBW,GAAG,EAAE;MAAEX,UAAU,EAAE;IAAK,CAAC;IACzBY,IAAI,EAAE;MAAEZ,UAAU,EAAE;IAAK,CAAC;IAC1Ba,QAAQ,EAAE;MAAEb,UAAU,EAAE;IAAK,CAAC;IAC9Bc,IAAI,EAAE;MAAEd,UAAU,EAAE;IAAK,CAAC;IAC1Be,MAAM,EAAE;MAAEf,UAAU,EAAE;IAAK,CAAC;IAC5BgB,OAAO,EAAE;MAAEhB,UAAU,EAAE;IAAK,CAAC;IAC7BiB,OAAO,EAAE;MAAEjB,UAAU,EAAE;IAAK,CAAC;IAC7B0B,IAAI,EAAE;MAAE1B,UAAU,EAAE;IAAK,CAAC;IAC1B,CAACZ,MAAM,CAACwC,WAAW,GAAG;MAAE1F,KAAK,EAAE,iBAAiB;MAAEsB,YAAY,EAAE;IAAK,CAAC;IACtE,CAAC4B,MAAM,CAAClC,QAAQ,GAAG;MAAEhB,KAAK,EAAE6C,eAAe,CAAClB,SAAS,CAACmD,OAAO;MAAExD,YAAY,EAAE,IAAI;MAAEqE,QAAQ,EAAE;IAAK;EACpG,CAAC,CAAC;EACF7E,YAAY,CAACjB,aAAa,CAAC,GAAGgD,eAAe;EAE7C/B,YAAY,CAAC,0BAA0B,CAAC,GAAGG,MAAM,CAACC,MAAM,CAACJ,YAAY,CAAC,qBAAqB,CAAC,EAAE;IAC5F,CAACoC,MAAM,CAACwC,WAAW,GAAG;MACpBpE,YAAY,EAAE,IAAI;MAClBtB,KAAK,EAAE;IACT;EACF,CAAC,CAAC;EACFR,KAAK,CAACoG,MAAM,CAAC9E,YAAY,CAAC,0BAA0B,CAAC,EAAE;IACrD+E,IAAIA,CAAA,EAAG;MACL,MAAMC,QAAQ,GAAG,IAAI,IAAI,IAAI,CAACtG,KAAK,CAAC4B,kBAAkB,CAAC;MACvD,IAAI,CAAC0E,QAAQ,EAAE;QACb,MAAM,IAAIvF,YAAY,CAACG,SAAS,CAAC,wEAAwE,CAAC;MAC5G;MAEA,MAAM;QAAEE,MAAM;QAAEC,IAAI;QAAEQ;MAAM,CAAC,GAAGyE,QAAQ;MACxC,MAAMjB,MAAM,GAAGM,KAAK,CAACC,IAAI,CAACxE,MAAM,CAACjB,UAAU,CAAC,CAAC;MAC7C,MAAMoG,GAAG,GAAGlB,MAAM,CAACT,MAAM;MACzB,IAAI/C,KAAK,IAAI0E,GAAG,EAAE;QAChB,OAAOrG,gBAAgB,CAACa,YAAY,EAAE;UAAEP,KAAK,EAAE0B,SAAS;UAAEsE,IAAI,EAAE;QAAK,CAAC,CAAC;MACzE;MAEA,MAAMC,IAAI,GAAGpB,MAAM,CAACxD,KAAK,CAAC;MAC1ByE,QAAQ,CAACzE,KAAK,GAAGA,KAAK,GAAG,CAAC;MAC1B,OAAO3B,gBAAgB,CAACa,YAAY,EAAEf,KAAK,CAAC0G,cAAc,CAACD,IAAI,CAACX,GAAG,CAAC9F,KAAK,CAAC6E,iBAAiB,CAAC,EAAExD,IAAI,CAAC,CAAC;IACtG;EACF,CAAC,CAAC;EAEFI,MAAM,CAACE,cAAc,CAACZ,YAAY,EAAEV,aAAa,EAAE;IACjDyB,YAAY,EAAE,IAAI;IAClBqE,QAAQ,EAAE,IAAI;IACd3F,KAAK,EAAE6C;EACT,CAAC,CAAC;AACJ,CAAC;AAED,MAAM1C,IAAI,GAAGZ,OAAO,CAAC,2BAA2B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}