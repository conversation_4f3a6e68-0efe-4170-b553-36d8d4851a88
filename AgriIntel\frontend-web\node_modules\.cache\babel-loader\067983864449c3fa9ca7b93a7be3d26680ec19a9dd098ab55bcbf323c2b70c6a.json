{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.CallbackWorkflow = exports.AUTOMATED_TIMEOUT_MS = exports.HUMAN_TIMEOUT_MS = void 0;\nconst promises_1 = require(\"timers/promises\");\nconst error_1 = require(\"../../../error\");\nconst utils_1 = require(\"../../../utils\");\nconst command_builders_1 = require(\"./command_builders\");\n/** 5 minutes in milliseconds */\nexports.HUMAN_TIMEOUT_MS = 300000;\n/** 1 minute in milliseconds */\nexports.AUTOMATED_TIMEOUT_MS = 60000;\n/** Properties allowed on results of callbacks. */\nconst RESULT_PROPERTIES = ['accessToken', 'expiresInSeconds', 'refreshToken'];\n/** Error message when the callback result is invalid. */\nconst CALLBACK_RESULT_ERROR = 'User provided OIDC callbacks must return a valid object with an accessToken.';\n/** The time to throttle callback calls. */\nconst THROTTLE_MS = 100;\n/**\n * OIDC implementation of a callback based workflow.\n * @internal\n */\nclass CallbackWorkflow {\n  /**\n   * Instantiate the callback workflow.\n   */\n  constructor(cache, callback) {\n    this.cache = cache;\n    this.callback = this.withLock(callback);\n    this.lastExecutionTime = Date.now() - THROTTLE_MS;\n  }\n  /**\n   * Get the document to add for speculative authentication. This also needs\n   * to add a db field from the credentials source.\n   */\n  async speculativeAuth(connection, credentials) {\n    // Check if the Client Cache has an access token.\n    // If it does, cache the access token in the Connection Cache and send a JwtStepRequest\n    // with the cached access token in the speculative authentication SASL payload.\n    if (this.cache.hasAccessToken) {\n      const accessToken = this.cache.getAccessToken();\n      connection.accessToken = accessToken;\n      const document = (0, command_builders_1.finishCommandDocument)(accessToken);\n      document.db = credentials.source;\n      return {\n        speculativeAuthenticate: document\n      };\n    }\n    return {};\n  }\n  /**\n   * Reauthenticate the callback workflow. For this we invalidated the access token\n   * in the cache and run the authentication steps again. No initial handshake needs\n   * to be sent.\n   */\n  async reauthenticate(connection, credentials) {\n    if (this.cache.hasAccessToken) {\n      // Reauthentication implies the token has expired.\n      if (connection.accessToken === this.cache.getAccessToken()) {\n        // If connection's access token is the same as the cache's, remove\n        // the token from the cache and connection.\n        this.cache.removeAccessToken();\n        delete connection.accessToken;\n      } else {\n        // If the connection's access token is different from the cache's, set\n        // the cache's token on the connection and do not remove from the\n        // cache.\n        connection.accessToken = this.cache.getAccessToken();\n      }\n    }\n    await this.execute(connection, credentials);\n  }\n  /**\n   * Starts the callback authentication process. If there is a speculative\n   * authentication document from the initial handshake, then we will use that\n   * value to get the issuer, otherwise we will send the saslStart command.\n   */\n  async startAuthentication(connection, credentials, response) {\n    let result;\n    if (response?.speculativeAuthenticate) {\n      result = response.speculativeAuthenticate;\n    } else {\n      result = await connection.command((0, utils_1.ns)(credentials.source), (0, command_builders_1.startCommandDocument)(credentials), undefined);\n    }\n    return result;\n  }\n  /**\n   * Finishes the callback authentication process.\n   */\n  async finishAuthentication(connection, credentials, token, conversationId) {\n    await connection.command((0, utils_1.ns)(credentials.source), (0, command_builders_1.finishCommandDocument)(token, conversationId), undefined);\n  }\n  /**\n   * Executes the callback and validates the output.\n   */\n  async executeAndValidateCallback(params) {\n    const result = await this.callback(params);\n    // Validate that the result returned by the callback is acceptable. If it is not\n    // we must clear the token result from the cache.\n    if (isCallbackResultInvalid(result)) {\n      throw new error_1.MongoMissingCredentialsError(CALLBACK_RESULT_ERROR);\n    }\n    return result;\n  }\n  /**\n   * Ensure the callback is only executed one at a time and throttles the calls\n   * to every 100ms.\n   */\n  withLock(callback) {\n    let lock = Promise.resolve();\n    return async params => {\n      // We do this to ensure that we would never return the result of the\n      // previous lock, only the current callback's value would get returned.\n      await lock;\n      lock = lock.catch(() => null).then(async () => {\n        const difference = Date.now() - this.lastExecutionTime;\n        if (difference <= THROTTLE_MS) {\n          await (0, promises_1.setTimeout)(THROTTLE_MS - difference, {\n            signal: params.timeoutContext\n          });\n        }\n        this.lastExecutionTime = Date.now();\n        return await callback(params);\n      });\n      return await lock;\n    };\n  }\n}\nexports.CallbackWorkflow = CallbackWorkflow;\n/**\n * Determines if a result returned from a request or refresh callback\n * function is invalid. This means the result is nullish, doesn't contain\n * the accessToken required field, and does not contain extra fields.\n */\nfunction isCallbackResultInvalid(tokenResult) {\n  if (tokenResult == null || typeof tokenResult !== 'object') return true;\n  if (!('accessToken' in tokenResult)) return true;\n  return !Object.getOwnPropertyNames(tokenResult).every(prop => RESULT_PROPERTIES.includes(prop));\n}", "map": {"version": 3, "names": ["promises_1", "require", "error_1", "utils_1", "command_builders_1", "exports", "HUMAN_TIMEOUT_MS", "AUTOMATED_TIMEOUT_MS", "RESULT_PROPERTIES", "CALLBACK_RESULT_ERROR", "THROTTLE_MS", "CallbackWorkflow", "constructor", "cache", "callback", "withLock", "lastExecutionTime", "Date", "now", "speculativeAuth", "connection", "credentials", "hasAccessToken", "accessToken", "getAccessToken", "document", "finishCommandDocument", "db", "source", "speculativeAuthenticate", "reauthenticate", "removeAccessToken", "execute", "startAuthentication", "response", "result", "command", "ns", "startCommandDocument", "undefined", "finishAuthentication", "token", "conversationId", "executeAndValidateCallback", "params", "isCallbackResultInvalid", "MongoMissingCredentialsError", "lock", "Promise", "resolve", "catch", "then", "difference", "setTimeout", "signal", "timeoutContext", "tokenResult", "Object", "getOwnPropertyNames", "every", "prop", "includes"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\auth\\mongodb_oidc\\callback_workflow.ts"], "sourcesContent": ["import { setTimeout } from 'timers/promises';\n\nimport { type Document } from '../../../bson';\nimport { MongoMissingCredentialsError } from '../../../error';\nimport { ns } from '../../../utils';\nimport type { Connection } from '../../connection';\nimport type { MongoCredentials } from '../mongo_credentials';\nimport {\n  type OIDCCallbackFunction,\n  type OIDCCallbackParams,\n  type OIDCResponse,\n  type Workflow\n} from '../mongodb_oidc';\nimport { finishCommandDocument, startCommandDocument } from './command_builders';\nimport { type TokenCache } from './token_cache';\n\n/** 5 minutes in milliseconds */\nexport const HUMAN_TIMEOUT_MS = 300000;\n/** 1 minute in milliseconds */\nexport const AUTOMATED_TIMEOUT_MS = 60000;\n\n/** Properties allowed on results of callbacks. */\nconst RESULT_PROPERTIES = ['accessToken', 'expiresInSeconds', 'refreshToken'];\n\n/** Error message when the callback result is invalid. */\nconst CALLBACK_RESULT_ERROR =\n  'User provided OIDC callbacks must return a valid object with an accessToken.';\n\n/** The time to throttle callback calls. */\nconst THROTTLE_MS = 100;\n\n/**\n * OIDC implementation of a callback based workflow.\n * @internal\n */\nexport abstract class CallbackWorkflow implements Workflow {\n  cache: TokenCache;\n  callback: OIDCCallbackFunction;\n  lastExecutionTime: number;\n\n  /**\n   * Instantiate the callback workflow.\n   */\n  constructor(cache: TokenCache, callback: OIDCCallbackFunction) {\n    this.cache = cache;\n    this.callback = this.withLock(callback);\n    this.lastExecutionTime = Date.now() - THROTTLE_MS;\n  }\n\n  /**\n   * Get the document to add for speculative authentication. This also needs\n   * to add a db field from the credentials source.\n   */\n  async speculativeAuth(connection: Connection, credentials: MongoCredentials): Promise<Document> {\n    // Check if the Client Cache has an access token.\n    // If it does, cache the access token in the Connection Cache and send a JwtStepRequest\n    // with the cached access token in the speculative authentication SASL payload.\n    if (this.cache.hasAccessToken) {\n      const accessToken = this.cache.getAccessToken();\n      connection.accessToken = accessToken;\n      const document = finishCommandDocument(accessToken);\n      document.db = credentials.source;\n      return { speculativeAuthenticate: document };\n    }\n    return {};\n  }\n\n  /**\n   * Reauthenticate the callback workflow. For this we invalidated the access token\n   * in the cache and run the authentication steps again. No initial handshake needs\n   * to be sent.\n   */\n  async reauthenticate(connection: Connection, credentials: MongoCredentials): Promise<void> {\n    if (this.cache.hasAccessToken) {\n      // Reauthentication implies the token has expired.\n      if (connection.accessToken === this.cache.getAccessToken()) {\n        // If connection's access token is the same as the cache's, remove\n        // the token from the cache and connection.\n        this.cache.removeAccessToken();\n        delete connection.accessToken;\n      } else {\n        // If the connection's access token is different from the cache's, set\n        // the cache's token on the connection and do not remove from the\n        // cache.\n        connection.accessToken = this.cache.getAccessToken();\n      }\n    }\n    await this.execute(connection, credentials);\n  }\n\n  /**\n   * Execute the OIDC callback workflow.\n   */\n  abstract execute(\n    connection: Connection,\n    credentials: MongoCredentials,\n    response?: Document\n  ): Promise<void>;\n\n  /**\n   * Starts the callback authentication process. If there is a speculative\n   * authentication document from the initial handshake, then we will use that\n   * value to get the issuer, otherwise we will send the saslStart command.\n   */\n  protected async startAuthentication(\n    connection: Connection,\n    credentials: MongoCredentials,\n    response?: Document\n  ): Promise<Document> {\n    let result;\n    if (response?.speculativeAuthenticate) {\n      result = response.speculativeAuthenticate;\n    } else {\n      result = await connection.command(\n        ns(credentials.source),\n        startCommandDocument(credentials),\n        undefined\n      );\n    }\n    return result;\n  }\n\n  /**\n   * Finishes the callback authentication process.\n   */\n  protected async finishAuthentication(\n    connection: Connection,\n    credentials: MongoCredentials,\n    token: string,\n    conversationId?: number\n  ): Promise<void> {\n    await connection.command(\n      ns(credentials.source),\n      finishCommandDocument(token, conversationId),\n      undefined\n    );\n  }\n\n  /**\n   * Executes the callback and validates the output.\n   */\n  protected async executeAndValidateCallback(params: OIDCCallbackParams): Promise<OIDCResponse> {\n    const result = await this.callback(params);\n    // Validate that the result returned by the callback is acceptable. If it is not\n    // we must clear the token result from the cache.\n    if (isCallbackResultInvalid(result)) {\n      throw new MongoMissingCredentialsError(CALLBACK_RESULT_ERROR);\n    }\n    return result;\n  }\n\n  /**\n   * Ensure the callback is only executed one at a time and throttles the calls\n   * to every 100ms.\n   */\n  protected withLock(callback: OIDCCallbackFunction): OIDCCallbackFunction {\n    let lock: Promise<any> = Promise.resolve();\n    return async (params: OIDCCallbackParams): Promise<OIDCResponse> => {\n      // We do this to ensure that we would never return the result of the\n      // previous lock, only the current callback's value would get returned.\n      await lock;\n      lock = lock\n\n        .catch(() => null)\n\n        .then(async () => {\n          const difference = Date.now() - this.lastExecutionTime;\n          if (difference <= THROTTLE_MS) {\n            await setTimeout(THROTTLE_MS - difference, { signal: params.timeoutContext });\n          }\n          this.lastExecutionTime = Date.now();\n          return await callback(params);\n        });\n      return await lock;\n    };\n  }\n}\n\n/**\n * Determines if a result returned from a request or refresh callback\n * function is invalid. This means the result is nullish, doesn't contain\n * the accessToken required field, and does not contain extra fields.\n */\nfunction isCallbackResultInvalid(tokenResult: unknown): boolean {\n  if (tokenResult == null || typeof tokenResult !== 'object') return true;\n  if (!('accessToken' in tokenResult)) return true;\n  return !Object.getOwnPropertyNames(tokenResult).every(prop => RESULT_PROPERTIES.includes(prop));\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,UAAA,GAAAC,OAAA;AAGA,MAAAC,OAAA,GAAAD,OAAA;AACA,MAAAE,OAAA,GAAAF,OAAA;AASA,MAAAG,kBAAA,GAAAH,OAAA;AAGA;AACaI,OAAA,CAAAC,gBAAgB,GAAG,MAAM;AACtC;AACaD,OAAA,CAAAE,oBAAoB,GAAG,KAAK;AAEzC;AACA,MAAMC,iBAAiB,GAAG,CAAC,aAAa,EAAE,kBAAkB,EAAE,cAAc,CAAC;AAE7E;AACA,MAAMC,qBAAqB,GACzB,8EAA8E;AAEhF;AACA,MAAMC,WAAW,GAAG,GAAG;AAEvB;;;;AAIA,MAAsBC,gBAAgB;EAKpC;;;EAGAC,YAAYC,KAAiB,EAAEC,QAA8B;IAC3D,IAAI,CAACD,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,QAAQ,GAAG,IAAI,CAACC,QAAQ,CAACD,QAAQ,CAAC;IACvC,IAAI,CAACE,iBAAiB,GAAGC,IAAI,CAACC,GAAG,EAAE,GAAGR,WAAW;EACnD;EAEA;;;;EAIA,MAAMS,eAAeA,CAACC,UAAsB,EAAEC,WAA6B;IACzE;IACA;IACA;IACA,IAAI,IAAI,CAACR,KAAK,CAACS,cAAc,EAAE;MAC7B,MAAMC,WAAW,GAAG,IAAI,CAACV,KAAK,CAACW,cAAc,EAAE;MAC/CJ,UAAU,CAACG,WAAW,GAAGA,WAAW;MACpC,MAAME,QAAQ,GAAG,IAAArB,kBAAA,CAAAsB,qBAAqB,EAACH,WAAW,CAAC;MACnDE,QAAQ,CAACE,EAAE,GAAGN,WAAW,CAACO,MAAM;MAChC,OAAO;QAAEC,uBAAuB,EAAEJ;MAAQ,CAAE;IAC9C;IACA,OAAO,EAAE;EACX;EAEA;;;;;EAKA,MAAMK,cAAcA,CAACV,UAAsB,EAAEC,WAA6B;IACxE,IAAI,IAAI,CAACR,KAAK,CAACS,cAAc,EAAE;MAC7B;MACA,IAAIF,UAAU,CAACG,WAAW,KAAK,IAAI,CAACV,KAAK,CAACW,cAAc,EAAE,EAAE;QAC1D;QACA;QACA,IAAI,CAACX,KAAK,CAACkB,iBAAiB,EAAE;QAC9B,OAAOX,UAAU,CAACG,WAAW;MAC/B,CAAC,MAAM;QACL;QACA;QACA;QACAH,UAAU,CAACG,WAAW,GAAG,IAAI,CAACV,KAAK,CAACW,cAAc,EAAE;MACtD;IACF;IACA,MAAM,IAAI,CAACQ,OAAO,CAACZ,UAAU,EAAEC,WAAW,CAAC;EAC7C;EAWA;;;;;EAKU,MAAMY,mBAAmBA,CACjCb,UAAsB,EACtBC,WAA6B,EAC7Ba,QAAmB;IAEnB,IAAIC,MAAM;IACV,IAAID,QAAQ,EAAEL,uBAAuB,EAAE;MACrCM,MAAM,GAAGD,QAAQ,CAACL,uBAAuB;IAC3C,CAAC,MAAM;MACLM,MAAM,GAAG,MAAMf,UAAU,CAACgB,OAAO,CAC/B,IAAAjC,OAAA,CAAAkC,EAAE,EAAChB,WAAW,CAACO,MAAM,CAAC,EACtB,IAAAxB,kBAAA,CAAAkC,oBAAoB,EAACjB,WAAW,CAAC,EACjCkB,SAAS,CACV;IACH;IACA,OAAOJ,MAAM;EACf;EAEA;;;EAGU,MAAMK,oBAAoBA,CAClCpB,UAAsB,EACtBC,WAA6B,EAC7BoB,KAAa,EACbC,cAAuB;IAEvB,MAAMtB,UAAU,CAACgB,OAAO,CACtB,IAAAjC,OAAA,CAAAkC,EAAE,EAAChB,WAAW,CAACO,MAAM,CAAC,EACtB,IAAAxB,kBAAA,CAAAsB,qBAAqB,EAACe,KAAK,EAAEC,cAAc,CAAC,EAC5CH,SAAS,CACV;EACH;EAEA;;;EAGU,MAAMI,0BAA0BA,CAACC,MAA0B;IACnE,MAAMT,MAAM,GAAG,MAAM,IAAI,CAACrB,QAAQ,CAAC8B,MAAM,CAAC;IAC1C;IACA;IACA,IAAIC,uBAAuB,CAACV,MAAM,CAAC,EAAE;MACnC,MAAM,IAAIjC,OAAA,CAAA4C,4BAA4B,CAACrC,qBAAqB,CAAC;IAC/D;IACA,OAAO0B,MAAM;EACf;EAEA;;;;EAIUpB,QAAQA,CAACD,QAA8B;IAC/C,IAAIiC,IAAI,GAAiBC,OAAO,CAACC,OAAO,EAAE;IAC1C,OAAO,MAAOL,MAA0B,IAA2B;MACjE;MACA;MACA,MAAMG,IAAI;MACVA,IAAI,GAAGA,IAAI,CAERG,KAAK,CAAC,MAAM,IAAI,CAAC,CAEjBC,IAAI,CAAC,YAAW;QACf,MAAMC,UAAU,GAAGnC,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,CAACF,iBAAiB;QACtD,IAAIoC,UAAU,IAAI1C,WAAW,EAAE;UAC7B,MAAM,IAAAV,UAAA,CAAAqD,UAAU,EAAC3C,WAAW,GAAG0C,UAAU,EAAE;YAAEE,MAAM,EAAEV,MAAM,CAACW;UAAc,CAAE,CAAC;QAC/E;QACA,IAAI,CAACvC,iBAAiB,GAAGC,IAAI,CAACC,GAAG,EAAE;QACnC,OAAO,MAAMJ,QAAQ,CAAC8B,MAAM,CAAC;MAC/B,CAAC,CAAC;MACJ,OAAO,MAAMG,IAAI;IACnB,CAAC;EACH;;AA5IF1C,OAAA,CAAAM,gBAAA,GAAAA,gBAAA;AA+IA;;;;;AAKA,SAASkC,uBAAuBA,CAACW,WAAoB;EACnD,IAAIA,WAAW,IAAI,IAAI,IAAI,OAAOA,WAAW,KAAK,QAAQ,EAAE,OAAO,IAAI;EACvE,IAAI,EAAE,aAAa,IAAIA,WAAW,CAAC,EAAE,OAAO,IAAI;EAChD,OAAO,CAACC,MAAM,CAACC,mBAAmB,CAACF,WAAW,CAAC,CAACG,KAAK,CAACC,IAAI,IAAIpD,iBAAiB,CAACqD,QAAQ,CAACD,IAAI,CAAC,CAAC;AACjG", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}