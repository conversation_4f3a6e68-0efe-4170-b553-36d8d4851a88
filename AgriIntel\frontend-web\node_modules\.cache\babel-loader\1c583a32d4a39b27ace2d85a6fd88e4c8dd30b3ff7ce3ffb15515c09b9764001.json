{"ast": null, "code": "\"use strict\";\n\nconst conversions = require(\"webidl-conversions\");\nconst utils = require(\"./utils.js\");\nconst implSymbol = utils.implSymbol;\nconst ctorRegistrySymbol = utils.ctorRegistrySymbol;\nconst interfaceName = \"URL\";\nexports.is = value => {\n  return utils.isObject(value) && utils.hasOwn(value, implSymbol) && value[implSymbol] instanceof Impl.implementation;\n};\nexports.isImpl = value => {\n  return utils.isObject(value) && value instanceof Impl.implementation;\n};\nexports.convert = (globalObject, value, {\n  context = \"The provided value\"\n} = {}) => {\n  if (exports.is(value)) {\n    return utils.implForWrapper(value);\n  }\n  throw new globalObject.TypeError(`${context} is not of type 'URL'.`);\n};\nfunction makeWrapper(globalObject, newTarget) {\n  let proto;\n  if (newTarget !== undefined) {\n    proto = newTarget.prototype;\n  }\n  if (!utils.isObject(proto)) {\n    proto = globalObject[ctorRegistrySymbol][\"URL\"].prototype;\n  }\n  return Object.create(proto);\n}\nexports.create = (globalObject, constructorArgs, privateData) => {\n  const wrapper = makeWrapper(globalObject);\n  return exports.setup(wrapper, globalObject, constructorArgs, privateData);\n};\nexports.createImpl = (globalObject, constructorArgs, privateData) => {\n  const wrapper = exports.create(globalObject, constructorArgs, privateData);\n  return utils.implForWrapper(wrapper);\n};\nexports._internalSetup = (wrapper, globalObject) => {};\nexports.setup = (wrapper, globalObject, constructorArgs = [], privateData = {}) => {\n  privateData.wrapper = wrapper;\n  exports._internalSetup(wrapper, globalObject);\n  Object.defineProperty(wrapper, implSymbol, {\n    value: new Impl.implementation(globalObject, constructorArgs, privateData),\n    configurable: true\n  });\n  wrapper[implSymbol][utils.wrapperSymbol] = wrapper;\n  if (Impl.init) {\n    Impl.init(wrapper[implSymbol]);\n  }\n  return wrapper;\n};\nexports.new = (globalObject, newTarget) => {\n  const wrapper = makeWrapper(globalObject, newTarget);\n  exports._internalSetup(wrapper, globalObject);\n  Object.defineProperty(wrapper, implSymbol, {\n    value: Object.create(Impl.implementation.prototype),\n    configurable: true\n  });\n  wrapper[implSymbol][utils.wrapperSymbol] = wrapper;\n  if (Impl.init) {\n    Impl.init(wrapper[implSymbol]);\n  }\n  return wrapper[implSymbol];\n};\nconst exposed = new Set([\"Window\", \"Worker\"]);\nexports.install = (globalObject, globalNames) => {\n  if (!globalNames.some(globalName => exposed.has(globalName))) {\n    return;\n  }\n  const ctorRegistry = utils.initCtorRegistry(globalObject);\n  class URL {\n    constructor(url) {\n      if (arguments.length < 1) {\n        throw new globalObject.TypeError(`Failed to construct 'URL': 1 argument required, but only ${arguments.length} present.`);\n      }\n      const args = [];\n      {\n        let curArg = arguments[0];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to construct 'URL': parameter 1\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      {\n        let curArg = arguments[1];\n        if (curArg !== undefined) {\n          curArg = conversions[\"USVString\"](curArg, {\n            context: \"Failed to construct 'URL': parameter 2\",\n            globals: globalObject\n          });\n        }\n        args.push(curArg);\n      }\n      return exports.setup(Object.create(new.target.prototype), globalObject, args);\n    }\n    toJSON() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'toJSON' called on an object that is not a valid instance of URL.\");\n      }\n      return esValue[implSymbol].toJSON();\n    }\n    get href() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get href' called on an object that is not a valid instance of URL.\");\n      }\n      return esValue[implSymbol][\"href\"];\n    }\n    set href(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set href' called on an object that is not a valid instance of URL.\");\n      }\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'href' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n      esValue[implSymbol][\"href\"] = V;\n    }\n    toString() {\n      const esValue = this;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'toString' called on an object that is not a valid instance of URL.\");\n      }\n      return esValue[implSymbol][\"href\"];\n    }\n    get origin() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get origin' called on an object that is not a valid instance of URL.\");\n      }\n      return esValue[implSymbol][\"origin\"];\n    }\n    get protocol() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get protocol' called on an object that is not a valid instance of URL.\");\n      }\n      return esValue[implSymbol][\"protocol\"];\n    }\n    set protocol(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set protocol' called on an object that is not a valid instance of URL.\");\n      }\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'protocol' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n      esValue[implSymbol][\"protocol\"] = V;\n    }\n    get username() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get username' called on an object that is not a valid instance of URL.\");\n      }\n      return esValue[implSymbol][\"username\"];\n    }\n    set username(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set username' called on an object that is not a valid instance of URL.\");\n      }\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'username' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n      esValue[implSymbol][\"username\"] = V;\n    }\n    get password() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get password' called on an object that is not a valid instance of URL.\");\n      }\n      return esValue[implSymbol][\"password\"];\n    }\n    set password(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set password' called on an object that is not a valid instance of URL.\");\n      }\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'password' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n      esValue[implSymbol][\"password\"] = V;\n    }\n    get host() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get host' called on an object that is not a valid instance of URL.\");\n      }\n      return esValue[implSymbol][\"host\"];\n    }\n    set host(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set host' called on an object that is not a valid instance of URL.\");\n      }\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'host' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n      esValue[implSymbol][\"host\"] = V;\n    }\n    get hostname() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get hostname' called on an object that is not a valid instance of URL.\");\n      }\n      return esValue[implSymbol][\"hostname\"];\n    }\n    set hostname(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set hostname' called on an object that is not a valid instance of URL.\");\n      }\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'hostname' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n      esValue[implSymbol][\"hostname\"] = V;\n    }\n    get port() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get port' called on an object that is not a valid instance of URL.\");\n      }\n      return esValue[implSymbol][\"port\"];\n    }\n    set port(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set port' called on an object that is not a valid instance of URL.\");\n      }\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'port' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n      esValue[implSymbol][\"port\"] = V;\n    }\n    get pathname() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get pathname' called on an object that is not a valid instance of URL.\");\n      }\n      return esValue[implSymbol][\"pathname\"];\n    }\n    set pathname(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set pathname' called on an object that is not a valid instance of URL.\");\n      }\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'pathname' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n      esValue[implSymbol][\"pathname\"] = V;\n    }\n    get search() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get search' called on an object that is not a valid instance of URL.\");\n      }\n      return esValue[implSymbol][\"search\"];\n    }\n    set search(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set search' called on an object that is not a valid instance of URL.\");\n      }\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'search' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n      esValue[implSymbol][\"search\"] = V;\n    }\n    get searchParams() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get searchParams' called on an object that is not a valid instance of URL.\");\n      }\n      return utils.getSameObject(this, \"searchParams\", () => {\n        return utils.tryWrapperForImpl(esValue[implSymbol][\"searchParams\"]);\n      });\n    }\n    get hash() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get hash' called on an object that is not a valid instance of URL.\");\n      }\n      return esValue[implSymbol][\"hash\"];\n    }\n    set hash(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set hash' called on an object that is not a valid instance of URL.\");\n      }\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'hash' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n      esValue[implSymbol][\"hash\"] = V;\n    }\n    static parse(url) {\n      if (arguments.length < 1) {\n        throw new globalObject.TypeError(`Failed to execute 'parse' on 'URL': 1 argument required, but only ${arguments.length} present.`);\n      }\n      const args = [];\n      {\n        let curArg = arguments[0];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'parse' on 'URL': parameter 1\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      {\n        let curArg = arguments[1];\n        if (curArg !== undefined) {\n          curArg = conversions[\"USVString\"](curArg, {\n            context: \"Failed to execute 'parse' on 'URL': parameter 2\",\n            globals: globalObject\n          });\n        }\n        args.push(curArg);\n      }\n      return utils.tryWrapperForImpl(Impl.implementation.parse(globalObject, ...args));\n    }\n    static canParse(url) {\n      if (arguments.length < 1) {\n        throw new globalObject.TypeError(`Failed to execute 'canParse' on 'URL': 1 argument required, but only ${arguments.length} present.`);\n      }\n      const args = [];\n      {\n        let curArg = arguments[0];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'canParse' on 'URL': parameter 1\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      {\n        let curArg = arguments[1];\n        if (curArg !== undefined) {\n          curArg = conversions[\"USVString\"](curArg, {\n            context: \"Failed to execute 'canParse' on 'URL': parameter 2\",\n            globals: globalObject\n          });\n        }\n        args.push(curArg);\n      }\n      return Impl.implementation.canParse(...args);\n    }\n  }\n  Object.defineProperties(URL.prototype, {\n    toJSON: {\n      enumerable: true\n    },\n    href: {\n      enumerable: true\n    },\n    toString: {\n      enumerable: true\n    },\n    origin: {\n      enumerable: true\n    },\n    protocol: {\n      enumerable: true\n    },\n    username: {\n      enumerable: true\n    },\n    password: {\n      enumerable: true\n    },\n    host: {\n      enumerable: true\n    },\n    hostname: {\n      enumerable: true\n    },\n    port: {\n      enumerable: true\n    },\n    pathname: {\n      enumerable: true\n    },\n    search: {\n      enumerable: true\n    },\n    searchParams: {\n      enumerable: true\n    },\n    hash: {\n      enumerable: true\n    },\n    [Symbol.toStringTag]: {\n      value: \"URL\",\n      configurable: true\n    }\n  });\n  Object.defineProperties(URL, {\n    parse: {\n      enumerable: true\n    },\n    canParse: {\n      enumerable: true\n    }\n  });\n  ctorRegistry[interfaceName] = URL;\n  Object.defineProperty(globalObject, interfaceName, {\n    configurable: true,\n    writable: true,\n    value: URL\n  });\n  if (globalNames.includes(\"Window\")) {\n    Object.defineProperty(globalObject, \"webkitURL\", {\n      configurable: true,\n      writable: true,\n      value: URL\n    });\n  }\n};\nconst Impl = require(\"./URL-impl.js\");", "map": {"version": 3, "names": ["conversions", "require", "utils", "implSymbol", "ctorRegistrySymbol", "interfaceName", "exports", "is", "value", "isObject", "hasOwn", "Impl", "implementation", "isImpl", "convert", "globalObject", "context", "implForWrapper", "TypeError", "makeWrapper", "newTarget", "proto", "undefined", "prototype", "Object", "create", "constructorArgs", "privateData", "wrapper", "setup", "createImpl", "_internalSetup", "defineProperty", "configurable", "wrapperSymbol", "init", "new", "exposed", "Set", "install", "globalNames", "some", "globalName", "has", "ctorRegistry", "initCtorRegistry", "URL", "constructor", "url", "arguments", "length", "args", "curArg", "globals", "push", "target", "toJSON", "esValue", "href", "V", "toString", "origin", "protocol", "username", "password", "host", "hostname", "port", "pathname", "search", "searchParams", "getSameObject", "tryWrapperForImpl", "hash", "parse", "canParse", "defineProperties", "enumerable", "Symbol", "toStringTag", "writable", "includes"], "sources": ["C:/Users/<USER>/node_modules/mongodb-connection-string-url/node_modules/whatwg-url/lib/URL.js"], "sourcesContent": ["\"use strict\";\n\nconst conversions = require(\"webidl-conversions\");\nconst utils = require(\"./utils.js\");\n\nconst implSymbol = utils.implSymbol;\nconst ctorRegistrySymbol = utils.ctorRegistrySymbol;\n\nconst interfaceName = \"URL\";\n\nexports.is = value => {\n  return utils.isObject(value) && utils.hasOwn(value, implSymbol) && value[implSymbol] instanceof Impl.implementation;\n};\nexports.isImpl = value => {\n  return utils.isObject(value) && value instanceof Impl.implementation;\n};\nexports.convert = (globalObject, value, { context = \"The provided value\" } = {}) => {\n  if (exports.is(value)) {\n    return utils.implForWrapper(value);\n  }\n  throw new globalObject.TypeError(`${context} is not of type 'URL'.`);\n};\n\nfunction makeWrapper(globalObject, newTarget) {\n  let proto;\n  if (newTarget !== undefined) {\n    proto = newTarget.prototype;\n  }\n\n  if (!utils.isObject(proto)) {\n    proto = globalObject[ctorRegistrySymbol][\"URL\"].prototype;\n  }\n\n  return Object.create(proto);\n}\n\nexports.create = (globalObject, constructorArgs, privateData) => {\n  const wrapper = makeWrapper(globalObject);\n  return exports.setup(wrapper, globalObject, constructorArgs, privateData);\n};\n\nexports.createImpl = (globalObject, constructorArgs, privateData) => {\n  const wrapper = exports.create(globalObject, constructorArgs, privateData);\n  return utils.implForWrapper(wrapper);\n};\n\nexports._internalSetup = (wrapper, globalObject) => {};\n\nexports.setup = (wrapper, globalObject, constructorArgs = [], privateData = {}) => {\n  privateData.wrapper = wrapper;\n\n  exports._internalSetup(wrapper, globalObject);\n  Object.defineProperty(wrapper, implSymbol, {\n    value: new Impl.implementation(globalObject, constructorArgs, privateData),\n    configurable: true\n  });\n\n  wrapper[implSymbol][utils.wrapperSymbol] = wrapper;\n  if (Impl.init) {\n    Impl.init(wrapper[implSymbol]);\n  }\n  return wrapper;\n};\n\nexports.new = (globalObject, newTarget) => {\n  const wrapper = makeWrapper(globalObject, newTarget);\n\n  exports._internalSetup(wrapper, globalObject);\n  Object.defineProperty(wrapper, implSymbol, {\n    value: Object.create(Impl.implementation.prototype),\n    configurable: true\n  });\n\n  wrapper[implSymbol][utils.wrapperSymbol] = wrapper;\n  if (Impl.init) {\n    Impl.init(wrapper[implSymbol]);\n  }\n  return wrapper[implSymbol];\n};\n\nconst exposed = new Set([\"Window\", \"Worker\"]);\n\nexports.install = (globalObject, globalNames) => {\n  if (!globalNames.some(globalName => exposed.has(globalName))) {\n    return;\n  }\n\n  const ctorRegistry = utils.initCtorRegistry(globalObject);\n  class URL {\n    constructor(url) {\n      if (arguments.length < 1) {\n        throw new globalObject.TypeError(\n          `Failed to construct 'URL': 1 argument required, but only ${arguments.length} present.`\n        );\n      }\n      const args = [];\n      {\n        let curArg = arguments[0];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to construct 'URL': parameter 1\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      {\n        let curArg = arguments[1];\n        if (curArg !== undefined) {\n          curArg = conversions[\"USVString\"](curArg, {\n            context: \"Failed to construct 'URL': parameter 2\",\n            globals: globalObject\n          });\n        }\n        args.push(curArg);\n      }\n      return exports.setup(Object.create(new.target.prototype), globalObject, args);\n    }\n\n    toJSON() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'toJSON' called on an object that is not a valid instance of URL.\");\n      }\n\n      return esValue[implSymbol].toJSON();\n    }\n\n    get href() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get href' called on an object that is not a valid instance of URL.\");\n      }\n\n      return esValue[implSymbol][\"href\"];\n    }\n\n    set href(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set href' called on an object that is not a valid instance of URL.\");\n      }\n\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'href' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n\n      esValue[implSymbol][\"href\"] = V;\n    }\n\n    toString() {\n      const esValue = this;\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'toString' called on an object that is not a valid instance of URL.\");\n      }\n\n      return esValue[implSymbol][\"href\"];\n    }\n\n    get origin() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get origin' called on an object that is not a valid instance of URL.\");\n      }\n\n      return esValue[implSymbol][\"origin\"];\n    }\n\n    get protocol() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get protocol' called on an object that is not a valid instance of URL.\");\n      }\n\n      return esValue[implSymbol][\"protocol\"];\n    }\n\n    set protocol(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set protocol' called on an object that is not a valid instance of URL.\");\n      }\n\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'protocol' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n\n      esValue[implSymbol][\"protocol\"] = V;\n    }\n\n    get username() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get username' called on an object that is not a valid instance of URL.\");\n      }\n\n      return esValue[implSymbol][\"username\"];\n    }\n\n    set username(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set username' called on an object that is not a valid instance of URL.\");\n      }\n\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'username' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n\n      esValue[implSymbol][\"username\"] = V;\n    }\n\n    get password() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get password' called on an object that is not a valid instance of URL.\");\n      }\n\n      return esValue[implSymbol][\"password\"];\n    }\n\n    set password(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set password' called on an object that is not a valid instance of URL.\");\n      }\n\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'password' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n\n      esValue[implSymbol][\"password\"] = V;\n    }\n\n    get host() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get host' called on an object that is not a valid instance of URL.\");\n      }\n\n      return esValue[implSymbol][\"host\"];\n    }\n\n    set host(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set host' called on an object that is not a valid instance of URL.\");\n      }\n\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'host' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n\n      esValue[implSymbol][\"host\"] = V;\n    }\n\n    get hostname() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get hostname' called on an object that is not a valid instance of URL.\");\n      }\n\n      return esValue[implSymbol][\"hostname\"];\n    }\n\n    set hostname(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set hostname' called on an object that is not a valid instance of URL.\");\n      }\n\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'hostname' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n\n      esValue[implSymbol][\"hostname\"] = V;\n    }\n\n    get port() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get port' called on an object that is not a valid instance of URL.\");\n      }\n\n      return esValue[implSymbol][\"port\"];\n    }\n\n    set port(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set port' called on an object that is not a valid instance of URL.\");\n      }\n\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'port' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n\n      esValue[implSymbol][\"port\"] = V;\n    }\n\n    get pathname() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get pathname' called on an object that is not a valid instance of URL.\");\n      }\n\n      return esValue[implSymbol][\"pathname\"];\n    }\n\n    set pathname(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set pathname' called on an object that is not a valid instance of URL.\");\n      }\n\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'pathname' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n\n      esValue[implSymbol][\"pathname\"] = V;\n    }\n\n    get search() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get search' called on an object that is not a valid instance of URL.\");\n      }\n\n      return esValue[implSymbol][\"search\"];\n    }\n\n    set search(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set search' called on an object that is not a valid instance of URL.\");\n      }\n\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'search' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n\n      esValue[implSymbol][\"search\"] = V;\n    }\n\n    get searchParams() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get searchParams' called on an object that is not a valid instance of URL.\");\n      }\n\n      return utils.getSameObject(this, \"searchParams\", () => {\n        return utils.tryWrapperForImpl(esValue[implSymbol][\"searchParams\"]);\n      });\n    }\n\n    get hash() {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'get hash' called on an object that is not a valid instance of URL.\");\n      }\n\n      return esValue[implSymbol][\"hash\"];\n    }\n\n    set hash(V) {\n      const esValue = this !== null && this !== undefined ? this : globalObject;\n\n      if (!exports.is(esValue)) {\n        throw new globalObject.TypeError(\"'set hash' called on an object that is not a valid instance of URL.\");\n      }\n\n      V = conversions[\"USVString\"](V, {\n        context: \"Failed to set the 'hash' property on 'URL': The provided value\",\n        globals: globalObject\n      });\n\n      esValue[implSymbol][\"hash\"] = V;\n    }\n\n    static parse(url) {\n      if (arguments.length < 1) {\n        throw new globalObject.TypeError(\n          `Failed to execute 'parse' on 'URL': 1 argument required, but only ${arguments.length} present.`\n        );\n      }\n      const args = [];\n      {\n        let curArg = arguments[0];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'parse' on 'URL': parameter 1\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      {\n        let curArg = arguments[1];\n        if (curArg !== undefined) {\n          curArg = conversions[\"USVString\"](curArg, {\n            context: \"Failed to execute 'parse' on 'URL': parameter 2\",\n            globals: globalObject\n          });\n        }\n        args.push(curArg);\n      }\n      return utils.tryWrapperForImpl(Impl.implementation.parse(globalObject, ...args));\n    }\n\n    static canParse(url) {\n      if (arguments.length < 1) {\n        throw new globalObject.TypeError(\n          `Failed to execute 'canParse' on 'URL': 1 argument required, but only ${arguments.length} present.`\n        );\n      }\n      const args = [];\n      {\n        let curArg = arguments[0];\n        curArg = conversions[\"USVString\"](curArg, {\n          context: \"Failed to execute 'canParse' on 'URL': parameter 1\",\n          globals: globalObject\n        });\n        args.push(curArg);\n      }\n      {\n        let curArg = arguments[1];\n        if (curArg !== undefined) {\n          curArg = conversions[\"USVString\"](curArg, {\n            context: \"Failed to execute 'canParse' on 'URL': parameter 2\",\n            globals: globalObject\n          });\n        }\n        args.push(curArg);\n      }\n      return Impl.implementation.canParse(...args);\n    }\n  }\n  Object.defineProperties(URL.prototype, {\n    toJSON: { enumerable: true },\n    href: { enumerable: true },\n    toString: { enumerable: true },\n    origin: { enumerable: true },\n    protocol: { enumerable: true },\n    username: { enumerable: true },\n    password: { enumerable: true },\n    host: { enumerable: true },\n    hostname: { enumerable: true },\n    port: { enumerable: true },\n    pathname: { enumerable: true },\n    search: { enumerable: true },\n    searchParams: { enumerable: true },\n    hash: { enumerable: true },\n    [Symbol.toStringTag]: { value: \"URL\", configurable: true }\n  });\n  Object.defineProperties(URL, { parse: { enumerable: true }, canParse: { enumerable: true } });\n  ctorRegistry[interfaceName] = URL;\n\n  Object.defineProperty(globalObject, interfaceName, {\n    configurable: true,\n    writable: true,\n    value: URL\n  });\n\n  if (globalNames.includes(\"Window\")) {\n    Object.defineProperty(globalObject, \"webkitURL\", {\n      configurable: true,\n      writable: true,\n      value: URL\n    });\n  }\n};\n\nconst Impl = require(\"./URL-impl.js\");\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAMA,WAAW,GAAGC,OAAO,CAAC,oBAAoB,CAAC;AACjD,MAAMC,KAAK,GAAGD,OAAO,CAAC,YAAY,CAAC;AAEnC,MAAME,UAAU,GAAGD,KAAK,CAACC,UAAU;AACnC,MAAMC,kBAAkB,GAAGF,KAAK,CAACE,kBAAkB;AAEnD,MAAMC,aAAa,GAAG,KAAK;AAE3BC,OAAO,CAACC,EAAE,GAAGC,KAAK,IAAI;EACpB,OAAON,KAAK,CAACO,QAAQ,CAACD,KAAK,CAAC,IAAIN,KAAK,CAACQ,MAAM,CAACF,KAAK,EAAEL,UAAU,CAAC,IAAIK,KAAK,CAACL,UAAU,CAAC,YAAYQ,IAAI,CAACC,cAAc;AACrH,CAAC;AACDN,OAAO,CAACO,MAAM,GAAGL,KAAK,IAAI;EACxB,OAAON,KAAK,CAACO,QAAQ,CAACD,KAAK,CAAC,IAAIA,KAAK,YAAYG,IAAI,CAACC,cAAc;AACtE,CAAC;AACDN,OAAO,CAACQ,OAAO,GAAG,CAACC,YAAY,EAAEP,KAAK,EAAE;EAAEQ,OAAO,GAAG;AAAqB,CAAC,GAAG,CAAC,CAAC,KAAK;EAClF,IAAIV,OAAO,CAACC,EAAE,CAACC,KAAK,CAAC,EAAE;IACrB,OAAON,KAAK,CAACe,cAAc,CAACT,KAAK,CAAC;EACpC;EACA,MAAM,IAAIO,YAAY,CAACG,SAAS,CAAC,GAAGF,OAAO,wBAAwB,CAAC;AACtE,CAAC;AAED,SAASG,WAAWA,CAACJ,YAAY,EAAEK,SAAS,EAAE;EAC5C,IAAIC,KAAK;EACT,IAAID,SAAS,KAAKE,SAAS,EAAE;IAC3BD,KAAK,GAAGD,SAAS,CAACG,SAAS;EAC7B;EAEA,IAAI,CAACrB,KAAK,CAACO,QAAQ,CAACY,KAAK,CAAC,EAAE;IAC1BA,KAAK,GAAGN,YAAY,CAACX,kBAAkB,CAAC,CAAC,KAAK,CAAC,CAACmB,SAAS;EAC3D;EAEA,OAAOC,MAAM,CAACC,MAAM,CAACJ,KAAK,CAAC;AAC7B;AAEAf,OAAO,CAACmB,MAAM,GAAG,CAACV,YAAY,EAAEW,eAAe,EAAEC,WAAW,KAAK;EAC/D,MAAMC,OAAO,GAAGT,WAAW,CAACJ,YAAY,CAAC;EACzC,OAAOT,OAAO,CAACuB,KAAK,CAACD,OAAO,EAAEb,YAAY,EAAEW,eAAe,EAAEC,WAAW,CAAC;AAC3E,CAAC;AAEDrB,OAAO,CAACwB,UAAU,GAAG,CAACf,YAAY,EAAEW,eAAe,EAAEC,WAAW,KAAK;EACnE,MAAMC,OAAO,GAAGtB,OAAO,CAACmB,MAAM,CAACV,YAAY,EAAEW,eAAe,EAAEC,WAAW,CAAC;EAC1E,OAAOzB,KAAK,CAACe,cAAc,CAACW,OAAO,CAAC;AACtC,CAAC;AAEDtB,OAAO,CAACyB,cAAc,GAAG,CAACH,OAAO,EAAEb,YAAY,KAAK,CAAC,CAAC;AAEtDT,OAAO,CAACuB,KAAK,GAAG,CAACD,OAAO,EAAEb,YAAY,EAAEW,eAAe,GAAG,EAAE,EAAEC,WAAW,GAAG,CAAC,CAAC,KAAK;EACjFA,WAAW,CAACC,OAAO,GAAGA,OAAO;EAE7BtB,OAAO,CAACyB,cAAc,CAACH,OAAO,EAAEb,YAAY,CAAC;EAC7CS,MAAM,CAACQ,cAAc,CAACJ,OAAO,EAAEzB,UAAU,EAAE;IACzCK,KAAK,EAAE,IAAIG,IAAI,CAACC,cAAc,CAACG,YAAY,EAAEW,eAAe,EAAEC,WAAW,CAAC;IAC1EM,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFL,OAAO,CAACzB,UAAU,CAAC,CAACD,KAAK,CAACgC,aAAa,CAAC,GAAGN,OAAO;EAClD,IAAIjB,IAAI,CAACwB,IAAI,EAAE;IACbxB,IAAI,CAACwB,IAAI,CAACP,OAAO,CAACzB,UAAU,CAAC,CAAC;EAChC;EACA,OAAOyB,OAAO;AAChB,CAAC;AAEDtB,OAAO,CAAC8B,GAAG,GAAG,CAACrB,YAAY,EAAEK,SAAS,KAAK;EACzC,MAAMQ,OAAO,GAAGT,WAAW,CAACJ,YAAY,EAAEK,SAAS,CAAC;EAEpDd,OAAO,CAACyB,cAAc,CAACH,OAAO,EAAEb,YAAY,CAAC;EAC7CS,MAAM,CAACQ,cAAc,CAACJ,OAAO,EAAEzB,UAAU,EAAE;IACzCK,KAAK,EAAEgB,MAAM,CAACC,MAAM,CAACd,IAAI,CAACC,cAAc,CAACW,SAAS,CAAC;IACnDU,YAAY,EAAE;EAChB,CAAC,CAAC;EAEFL,OAAO,CAACzB,UAAU,CAAC,CAACD,KAAK,CAACgC,aAAa,CAAC,GAAGN,OAAO;EAClD,IAAIjB,IAAI,CAACwB,IAAI,EAAE;IACbxB,IAAI,CAACwB,IAAI,CAACP,OAAO,CAACzB,UAAU,CAAC,CAAC;EAChC;EACA,OAAOyB,OAAO,CAACzB,UAAU,CAAC;AAC5B,CAAC;AAED,MAAMkC,OAAO,GAAG,IAAIC,GAAG,CAAC,CAAC,QAAQ,EAAE,QAAQ,CAAC,CAAC;AAE7ChC,OAAO,CAACiC,OAAO,GAAG,CAACxB,YAAY,EAAEyB,WAAW,KAAK;EAC/C,IAAI,CAACA,WAAW,CAACC,IAAI,CAACC,UAAU,IAAIL,OAAO,CAACM,GAAG,CAACD,UAAU,CAAC,CAAC,EAAE;IAC5D;EACF;EAEA,MAAME,YAAY,GAAG1C,KAAK,CAAC2C,gBAAgB,CAAC9B,YAAY,CAAC;EACzD,MAAM+B,GAAG,CAAC;IACRC,WAAWA,CAACC,GAAG,EAAE;MACf,IAAIC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;QACxB,MAAM,IAAInC,YAAY,CAACG,SAAS,CAC9B,4DAA4D+B,SAAS,CAACC,MAAM,WAC9E,CAAC;MACH;MACA,MAAMC,IAAI,GAAG,EAAE;MACf;QACE,IAAIC,MAAM,GAAGH,SAAS,CAAC,CAAC,CAAC;QACzBG,MAAM,GAAGpD,WAAW,CAAC,WAAW,CAAC,CAACoD,MAAM,EAAE;UACxCpC,OAAO,EAAE,wCAAwC;UACjDqC,OAAO,EAAEtC;QACX,CAAC,CAAC;QACFoC,IAAI,CAACG,IAAI,CAACF,MAAM,CAAC;MACnB;MACA;QACE,IAAIA,MAAM,GAAGH,SAAS,CAAC,CAAC,CAAC;QACzB,IAAIG,MAAM,KAAK9B,SAAS,EAAE;UACxB8B,MAAM,GAAGpD,WAAW,CAAC,WAAW,CAAC,CAACoD,MAAM,EAAE;YACxCpC,OAAO,EAAE,wCAAwC;YACjDqC,OAAO,EAAEtC;UACX,CAAC,CAAC;QACJ;QACAoC,IAAI,CAACG,IAAI,CAACF,MAAM,CAAC;MACnB;MACA,OAAO9C,OAAO,CAACuB,KAAK,CAACL,MAAM,CAACC,MAAM,CAACW,GAAG,CAACmB,MAAM,CAAChC,SAAS,CAAC,EAAER,YAAY,EAAEoC,IAAI,CAAC;IAC/E;IAEAK,MAAMA,CAAA,EAAG;MACP,MAAMC,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MACzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,mEAAmE,CAAC;MACvG;MAEA,OAAOuC,OAAO,CAACtD,UAAU,CAAC,CAACqD,MAAM,CAAC,CAAC;IACrC;IAEA,IAAIE,IAAIA,CAAA,EAAG;MACT,MAAMD,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,qEAAqE,CAAC;MACzG;MAEA,OAAOuC,OAAO,CAACtD,UAAU,CAAC,CAAC,MAAM,CAAC;IACpC;IAEA,IAAIuD,IAAIA,CAACC,CAAC,EAAE;MACV,MAAMF,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,qEAAqE,CAAC;MACzG;MAEAyC,CAAC,GAAG3D,WAAW,CAAC,WAAW,CAAC,CAAC2D,CAAC,EAAE;QAC9B3C,OAAO,EAAE,gEAAgE;QACzEqC,OAAO,EAAEtC;MACX,CAAC,CAAC;MAEF0C,OAAO,CAACtD,UAAU,CAAC,CAAC,MAAM,CAAC,GAAGwD,CAAC;IACjC;IAEAC,QAAQA,CAAA,EAAG;MACT,MAAMH,OAAO,GAAG,IAAI;MACpB,IAAI,CAACnD,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,qEAAqE,CAAC;MACzG;MAEA,OAAOuC,OAAO,CAACtD,UAAU,CAAC,CAAC,MAAM,CAAC;IACpC;IAEA,IAAI0D,MAAMA,CAAA,EAAG;MACX,MAAMJ,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,uEAAuE,CAAC;MAC3G;MAEA,OAAOuC,OAAO,CAACtD,UAAU,CAAC,CAAC,QAAQ,CAAC;IACtC;IAEA,IAAI2D,QAAQA,CAAA,EAAG;MACb,MAAML,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,yEAAyE,CAAC;MAC7G;MAEA,OAAOuC,OAAO,CAACtD,UAAU,CAAC,CAAC,UAAU,CAAC;IACxC;IAEA,IAAI2D,QAAQA,CAACH,CAAC,EAAE;MACd,MAAMF,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,yEAAyE,CAAC;MAC7G;MAEAyC,CAAC,GAAG3D,WAAW,CAAC,WAAW,CAAC,CAAC2D,CAAC,EAAE;QAC9B3C,OAAO,EAAE,oEAAoE;QAC7EqC,OAAO,EAAEtC;MACX,CAAC,CAAC;MAEF0C,OAAO,CAACtD,UAAU,CAAC,CAAC,UAAU,CAAC,GAAGwD,CAAC;IACrC;IAEA,IAAII,QAAQA,CAAA,EAAG;MACb,MAAMN,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,yEAAyE,CAAC;MAC7G;MAEA,OAAOuC,OAAO,CAACtD,UAAU,CAAC,CAAC,UAAU,CAAC;IACxC;IAEA,IAAI4D,QAAQA,CAACJ,CAAC,EAAE;MACd,MAAMF,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,yEAAyE,CAAC;MAC7G;MAEAyC,CAAC,GAAG3D,WAAW,CAAC,WAAW,CAAC,CAAC2D,CAAC,EAAE;QAC9B3C,OAAO,EAAE,oEAAoE;QAC7EqC,OAAO,EAAEtC;MACX,CAAC,CAAC;MAEF0C,OAAO,CAACtD,UAAU,CAAC,CAAC,UAAU,CAAC,GAAGwD,CAAC;IACrC;IAEA,IAAIK,QAAQA,CAAA,EAAG;MACb,MAAMP,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,yEAAyE,CAAC;MAC7G;MAEA,OAAOuC,OAAO,CAACtD,UAAU,CAAC,CAAC,UAAU,CAAC;IACxC;IAEA,IAAI6D,QAAQA,CAACL,CAAC,EAAE;MACd,MAAMF,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,yEAAyE,CAAC;MAC7G;MAEAyC,CAAC,GAAG3D,WAAW,CAAC,WAAW,CAAC,CAAC2D,CAAC,EAAE;QAC9B3C,OAAO,EAAE,oEAAoE;QAC7EqC,OAAO,EAAEtC;MACX,CAAC,CAAC;MAEF0C,OAAO,CAACtD,UAAU,CAAC,CAAC,UAAU,CAAC,GAAGwD,CAAC;IACrC;IAEA,IAAIM,IAAIA,CAAA,EAAG;MACT,MAAMR,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,qEAAqE,CAAC;MACzG;MAEA,OAAOuC,OAAO,CAACtD,UAAU,CAAC,CAAC,MAAM,CAAC;IACpC;IAEA,IAAI8D,IAAIA,CAACN,CAAC,EAAE;MACV,MAAMF,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,qEAAqE,CAAC;MACzG;MAEAyC,CAAC,GAAG3D,WAAW,CAAC,WAAW,CAAC,CAAC2D,CAAC,EAAE;QAC9B3C,OAAO,EAAE,gEAAgE;QACzEqC,OAAO,EAAEtC;MACX,CAAC,CAAC;MAEF0C,OAAO,CAACtD,UAAU,CAAC,CAAC,MAAM,CAAC,GAAGwD,CAAC;IACjC;IAEA,IAAIO,QAAQA,CAAA,EAAG;MACb,MAAMT,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,yEAAyE,CAAC;MAC7G;MAEA,OAAOuC,OAAO,CAACtD,UAAU,CAAC,CAAC,UAAU,CAAC;IACxC;IAEA,IAAI+D,QAAQA,CAACP,CAAC,EAAE;MACd,MAAMF,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,yEAAyE,CAAC;MAC7G;MAEAyC,CAAC,GAAG3D,WAAW,CAAC,WAAW,CAAC,CAAC2D,CAAC,EAAE;QAC9B3C,OAAO,EAAE,oEAAoE;QAC7EqC,OAAO,EAAEtC;MACX,CAAC,CAAC;MAEF0C,OAAO,CAACtD,UAAU,CAAC,CAAC,UAAU,CAAC,GAAGwD,CAAC;IACrC;IAEA,IAAIQ,IAAIA,CAAA,EAAG;MACT,MAAMV,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,qEAAqE,CAAC;MACzG;MAEA,OAAOuC,OAAO,CAACtD,UAAU,CAAC,CAAC,MAAM,CAAC;IACpC;IAEA,IAAIgE,IAAIA,CAACR,CAAC,EAAE;MACV,MAAMF,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,qEAAqE,CAAC;MACzG;MAEAyC,CAAC,GAAG3D,WAAW,CAAC,WAAW,CAAC,CAAC2D,CAAC,EAAE;QAC9B3C,OAAO,EAAE,gEAAgE;QACzEqC,OAAO,EAAEtC;MACX,CAAC,CAAC;MAEF0C,OAAO,CAACtD,UAAU,CAAC,CAAC,MAAM,CAAC,GAAGwD,CAAC;IACjC;IAEA,IAAIS,QAAQA,CAAA,EAAG;MACb,MAAMX,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,yEAAyE,CAAC;MAC7G;MAEA,OAAOuC,OAAO,CAACtD,UAAU,CAAC,CAAC,UAAU,CAAC;IACxC;IAEA,IAAIiE,QAAQA,CAACT,CAAC,EAAE;MACd,MAAMF,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,yEAAyE,CAAC;MAC7G;MAEAyC,CAAC,GAAG3D,WAAW,CAAC,WAAW,CAAC,CAAC2D,CAAC,EAAE;QAC9B3C,OAAO,EAAE,oEAAoE;QAC7EqC,OAAO,EAAEtC;MACX,CAAC,CAAC;MAEF0C,OAAO,CAACtD,UAAU,CAAC,CAAC,UAAU,CAAC,GAAGwD,CAAC;IACrC;IAEA,IAAIU,MAAMA,CAAA,EAAG;MACX,MAAMZ,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,uEAAuE,CAAC;MAC3G;MAEA,OAAOuC,OAAO,CAACtD,UAAU,CAAC,CAAC,QAAQ,CAAC;IACtC;IAEA,IAAIkE,MAAMA,CAACV,CAAC,EAAE;MACZ,MAAMF,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,uEAAuE,CAAC;MAC3G;MAEAyC,CAAC,GAAG3D,WAAW,CAAC,WAAW,CAAC,CAAC2D,CAAC,EAAE;QAC9B3C,OAAO,EAAE,kEAAkE;QAC3EqC,OAAO,EAAEtC;MACX,CAAC,CAAC;MAEF0C,OAAO,CAACtD,UAAU,CAAC,CAAC,QAAQ,CAAC,GAAGwD,CAAC;IACnC;IAEA,IAAIW,YAAYA,CAAA,EAAG;MACjB,MAAMb,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,6EAA6E,CAAC;MACjH;MAEA,OAAOhB,KAAK,CAACqE,aAAa,CAAC,IAAI,EAAE,cAAc,EAAE,MAAM;QACrD,OAAOrE,KAAK,CAACsE,iBAAiB,CAACf,OAAO,CAACtD,UAAU,CAAC,CAAC,cAAc,CAAC,CAAC;MACrE,CAAC,CAAC;IACJ;IAEA,IAAIsE,IAAIA,CAAA,EAAG;MACT,MAAMhB,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,qEAAqE,CAAC;MACzG;MAEA,OAAOuC,OAAO,CAACtD,UAAU,CAAC,CAAC,MAAM,CAAC;IACpC;IAEA,IAAIsE,IAAIA,CAACd,CAAC,EAAE;MACV,MAAMF,OAAO,GAAG,IAAI,KAAK,IAAI,IAAI,IAAI,KAAKnC,SAAS,GAAG,IAAI,GAAGP,YAAY;MAEzE,IAAI,CAACT,OAAO,CAACC,EAAE,CAACkD,OAAO,CAAC,EAAE;QACxB,MAAM,IAAI1C,YAAY,CAACG,SAAS,CAAC,qEAAqE,CAAC;MACzG;MAEAyC,CAAC,GAAG3D,WAAW,CAAC,WAAW,CAAC,CAAC2D,CAAC,EAAE;QAC9B3C,OAAO,EAAE,gEAAgE;QACzEqC,OAAO,EAAEtC;MACX,CAAC,CAAC;MAEF0C,OAAO,CAACtD,UAAU,CAAC,CAAC,MAAM,CAAC,GAAGwD,CAAC;IACjC;IAEA,OAAOe,KAAKA,CAAC1B,GAAG,EAAE;MAChB,IAAIC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;QACxB,MAAM,IAAInC,YAAY,CAACG,SAAS,CAC9B,qEAAqE+B,SAAS,CAACC,MAAM,WACvF,CAAC;MACH;MACA,MAAMC,IAAI,GAAG,EAAE;MACf;QACE,IAAIC,MAAM,GAAGH,SAAS,CAAC,CAAC,CAAC;QACzBG,MAAM,GAAGpD,WAAW,CAAC,WAAW,CAAC,CAACoD,MAAM,EAAE;UACxCpC,OAAO,EAAE,iDAAiD;UAC1DqC,OAAO,EAAEtC;QACX,CAAC,CAAC;QACFoC,IAAI,CAACG,IAAI,CAACF,MAAM,CAAC;MACnB;MACA;QACE,IAAIA,MAAM,GAAGH,SAAS,CAAC,CAAC,CAAC;QACzB,IAAIG,MAAM,KAAK9B,SAAS,EAAE;UACxB8B,MAAM,GAAGpD,WAAW,CAAC,WAAW,CAAC,CAACoD,MAAM,EAAE;YACxCpC,OAAO,EAAE,iDAAiD;YAC1DqC,OAAO,EAAEtC;UACX,CAAC,CAAC;QACJ;QACAoC,IAAI,CAACG,IAAI,CAACF,MAAM,CAAC;MACnB;MACA,OAAOlD,KAAK,CAACsE,iBAAiB,CAAC7D,IAAI,CAACC,cAAc,CAAC8D,KAAK,CAAC3D,YAAY,EAAE,GAAGoC,IAAI,CAAC,CAAC;IAClF;IAEA,OAAOwB,QAAQA,CAAC3B,GAAG,EAAE;MACnB,IAAIC,SAAS,CAACC,MAAM,GAAG,CAAC,EAAE;QACxB,MAAM,IAAInC,YAAY,CAACG,SAAS,CAC9B,wEAAwE+B,SAAS,CAACC,MAAM,WAC1F,CAAC;MACH;MACA,MAAMC,IAAI,GAAG,EAAE;MACf;QACE,IAAIC,MAAM,GAAGH,SAAS,CAAC,CAAC,CAAC;QACzBG,MAAM,GAAGpD,WAAW,CAAC,WAAW,CAAC,CAACoD,MAAM,EAAE;UACxCpC,OAAO,EAAE,oDAAoD;UAC7DqC,OAAO,EAAEtC;QACX,CAAC,CAAC;QACFoC,IAAI,CAACG,IAAI,CAACF,MAAM,CAAC;MACnB;MACA;QACE,IAAIA,MAAM,GAAGH,SAAS,CAAC,CAAC,CAAC;QACzB,IAAIG,MAAM,KAAK9B,SAAS,EAAE;UACxB8B,MAAM,GAAGpD,WAAW,CAAC,WAAW,CAAC,CAACoD,MAAM,EAAE;YACxCpC,OAAO,EAAE,oDAAoD;YAC7DqC,OAAO,EAAEtC;UACX,CAAC,CAAC;QACJ;QACAoC,IAAI,CAACG,IAAI,CAACF,MAAM,CAAC;MACnB;MACA,OAAOzC,IAAI,CAACC,cAAc,CAAC+D,QAAQ,CAAC,GAAGxB,IAAI,CAAC;IAC9C;EACF;EACA3B,MAAM,CAACoD,gBAAgB,CAAC9B,GAAG,CAACvB,SAAS,EAAE;IACrCiC,MAAM,EAAE;MAAEqB,UAAU,EAAE;IAAK,CAAC;IAC5BnB,IAAI,EAAE;MAAEmB,UAAU,EAAE;IAAK,CAAC;IAC1BjB,QAAQ,EAAE;MAAEiB,UAAU,EAAE;IAAK,CAAC;IAC9BhB,MAAM,EAAE;MAAEgB,UAAU,EAAE;IAAK,CAAC;IAC5Bf,QAAQ,EAAE;MAAEe,UAAU,EAAE;IAAK,CAAC;IAC9Bd,QAAQ,EAAE;MAAEc,UAAU,EAAE;IAAK,CAAC;IAC9Bb,QAAQ,EAAE;MAAEa,UAAU,EAAE;IAAK,CAAC;IAC9BZ,IAAI,EAAE;MAAEY,UAAU,EAAE;IAAK,CAAC;IAC1BX,QAAQ,EAAE;MAAEW,UAAU,EAAE;IAAK,CAAC;IAC9BV,IAAI,EAAE;MAAEU,UAAU,EAAE;IAAK,CAAC;IAC1BT,QAAQ,EAAE;MAAES,UAAU,EAAE;IAAK,CAAC;IAC9BR,MAAM,EAAE;MAAEQ,UAAU,EAAE;IAAK,CAAC;IAC5BP,YAAY,EAAE;MAAEO,UAAU,EAAE;IAAK,CAAC;IAClCJ,IAAI,EAAE;MAAEI,UAAU,EAAE;IAAK,CAAC;IAC1B,CAACC,MAAM,CAACC,WAAW,GAAG;MAAEvE,KAAK,EAAE,KAAK;MAAEyB,YAAY,EAAE;IAAK;EAC3D,CAAC,CAAC;EACFT,MAAM,CAACoD,gBAAgB,CAAC9B,GAAG,EAAE;IAAE4B,KAAK,EAAE;MAAEG,UAAU,EAAE;IAAK,CAAC;IAAEF,QAAQ,EAAE;MAAEE,UAAU,EAAE;IAAK;EAAE,CAAC,CAAC;EAC7FjC,YAAY,CAACvC,aAAa,CAAC,GAAGyC,GAAG;EAEjCtB,MAAM,CAACQ,cAAc,CAACjB,YAAY,EAAEV,aAAa,EAAE;IACjD4B,YAAY,EAAE,IAAI;IAClB+C,QAAQ,EAAE,IAAI;IACdxE,KAAK,EAAEsC;EACT,CAAC,CAAC;EAEF,IAAIN,WAAW,CAACyC,QAAQ,CAAC,QAAQ,CAAC,EAAE;IAClCzD,MAAM,CAACQ,cAAc,CAACjB,YAAY,EAAE,WAAW,EAAE;MAC/CkB,YAAY,EAAE,IAAI;MAClB+C,QAAQ,EAAE,IAAI;MACdxE,KAAK,EAAEsC;IACT,CAAC,CAAC;EACJ;AACF,CAAC;AAED,MAAMnC,IAAI,GAAGV,OAAO,CAAC,eAAe,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}