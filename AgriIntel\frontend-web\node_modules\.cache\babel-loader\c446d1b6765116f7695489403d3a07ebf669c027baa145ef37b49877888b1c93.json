{"ast": null, "code": "import { createSelector } from '../../../utils/createSelector';\n/**\n * Get the columns state\n * @category Virtualization\n */\nexport const gridVirtualizationSelector = state => state.virtualization;\n\n/**\n * Get the enabled state for virtualization\n * @category Virtualization\n */\nexport const gridVirtualizationEnabledSelector = createSelector(gridVirtualizationSelector, state => state.enabled);\n\n/**\n * Get the enabled state for virtualization\n * @category Virtualization\n */\nexport const gridVirtualizationColumnEnabledSelector = createSelector(gridVirtualizationSelector, state => state.enabledForColumns);", "map": {"version": 3, "names": ["createSelector", "gridVirtualizationSelector", "state", "virtualization", "gridVirtualizationEnabledSelector", "enabled", "gridVirtualizationColumnEnabledSelector", "enabledForColumns"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/x-data-grid/hooks/features/virtualization/gridVirtualizationSelectors.js"], "sourcesContent": ["import { createSelector } from '../../../utils/createSelector';\n/**\n * Get the columns state\n * @category Virtualization\n */\nexport const gridVirtualizationSelector = state => state.virtualization;\n\n/**\n * Get the enabled state for virtualization\n * @category Virtualization\n */\nexport const gridVirtualizationEnabledSelector = createSelector(gridVirtualizationSelector, state => state.enabled);\n\n/**\n * Get the enabled state for virtualization\n * @category Virtualization\n */\nexport const gridVirtualizationColumnEnabledSelector = createSelector(gridVirtualizationSelector, state => state.enabledForColumns);"], "mappings": "AAAA,SAASA,cAAc,QAAQ,+BAA+B;AAC9D;AACA;AACA;AACA;AACA,OAAO,MAAMC,0BAA0B,GAAGC,KAAK,IAAIA,KAAK,CAACC,cAAc;;AAEvE;AACA;AACA;AACA;AACA,OAAO,MAAMC,iCAAiC,GAAGJ,cAAc,CAACC,0BAA0B,EAAEC,KAAK,IAAIA,KAAK,CAACG,OAAO,CAAC;;AAEnH;AACA;AACA;AACA;AACA,OAAO,MAAMC,uCAAuC,GAAGN,cAAc,CAACC,0BAA0B,EAAEC,KAAK,IAAIA,KAAK,CAACK,iBAAiB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}