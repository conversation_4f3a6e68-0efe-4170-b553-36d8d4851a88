{"ast": null, "code": "import { useEffect as d, useRef as f } from \"react\";\nimport { FocusableMode as p, isFocusableElement as C } from '../utils/focus-management.js';\nimport { isMobile as M } from '../utils/platform.js';\nimport { useDocumentEvent as l } from './use-document-event.js';\nimport { useWindowEvent as T } from './use-window-event.js';\nfunction y(s, m) {\n  let a = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : !0;\n  let i = f(!1);\n  d(() => {\n    requestAnimationFrame(() => {\n      i.current = a;\n    });\n  }, [a]);\n  function c(e, r) {\n    if (!i.current || e.defaultPrevented) return;\n    let t = r(e);\n    if (t === null || !t.getRootNode().contains(t) || !t.isConnected) return;\n    let E = function u(n) {\n      return typeof n == \"function\" ? u(n()) : Array.isArray(n) || n instanceof Set ? n : [n];\n    }(s);\n    for (let u of E) {\n      if (u === null) continue;\n      let n = u instanceof HTMLElement ? u : u.current;\n      if (n != null && n.contains(t) || e.composed && e.composedPath().includes(n)) return;\n    }\n    return !C(t, p.Loose) && t.tabIndex !== -1 && e.preventDefault(), m(e, t);\n  }\n  let o = f(null);\n  l(\"pointerdown\", e => {\n    var r, t;\n    i.current && (o.current = ((t = (r = e.composedPath) == null ? void 0 : r.call(e)) == null ? void 0 : t[0]) || e.target);\n  }, !0), l(\"mousedown\", e => {\n    var r, t;\n    i.current && (o.current = ((t = (r = e.composedPath) == null ? void 0 : r.call(e)) == null ? void 0 : t[0]) || e.target);\n  }, !0), l(\"click\", e => {\n    M() || o.current && (c(e, () => o.current), o.current = null);\n  }, !0), l(\"touchend\", e => c(e, () => e.target instanceof HTMLElement ? e.target : null), !0), T(\"blur\", e => c(e, () => window.document.activeElement instanceof HTMLIFrameElement ? window.document.activeElement : null), !0);\n}\nexport { y as useOutsideClick };", "map": {"version": 3, "names": ["useEffect", "d", "useRef", "f", "FocusableMode", "p", "isFocusableElement", "C", "isMobile", "M", "useDocumentEvent", "l", "useWindowEvent", "T", "y", "s", "m", "a", "arguments", "length", "undefined", "i", "requestAnimationFrame", "current", "c", "e", "r", "defaultPrevented", "t", "getRootNode", "contains", "isConnected", "E", "u", "n", "Array", "isArray", "Set", "HTMLElement", "composed", "<PERSON><PERSON><PERSON>", "includes", "Loose", "tabIndex", "preventDefault", "o", "call", "target", "window", "document", "activeElement", "HTMLIFrameElement", "useOutsideClick"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/hooks/use-outside-click.js"], "sourcesContent": ["import{useEffect as d,useRef as f}from\"react\";import{FocusableMode as p,isFocusableElement as C}from'../utils/focus-management.js';import{isMobile as M}from'../utils/platform.js';import{useDocumentEvent as l}from'./use-document-event.js';import{useWindowEvent as T}from'./use-window-event.js';function y(s,m,a=!0){let i=f(!1);d(()=>{requestAnimationFrame(()=>{i.current=a})},[a]);function c(e,r){if(!i.current||e.defaultPrevented)return;let t=r(e);if(t===null||!t.getRootNode().contains(t)||!t.isConnected)return;let E=function u(n){return typeof n==\"function\"?u(n()):Array.isArray(n)||n instanceof Set?n:[n]}(s);for(let u of E){if(u===null)continue;let n=u instanceof HTMLElement?u:u.current;if(n!=null&&n.contains(t)||e.composed&&e.composedPath().includes(n))return}return!C(t,p.Loose)&&t.tabIndex!==-1&&e.preventDefault(),m(e,t)}let o=f(null);l(\"pointerdown\",e=>{var r,t;i.current&&(o.current=((t=(r=e.composedPath)==null?void 0:r.call(e))==null?void 0:t[0])||e.target)},!0),l(\"mousedown\",e=>{var r,t;i.current&&(o.current=((t=(r=e.composedPath)==null?void 0:r.call(e))==null?void 0:t[0])||e.target)},!0),l(\"click\",e=>{M()||o.current&&(c(e,()=>o.current),o.current=null)},!0),l(\"touchend\",e=>c(e,()=>e.target instanceof HTMLElement?e.target:null),!0),T(\"blur\",e=>c(e,()=>window.document.activeElement instanceof HTMLIFrameElement?window.document.activeElement:null),!0)}export{y as useOutsideClick};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,aAAa,IAAIC,CAAC,EAACC,kBAAkB,IAAIC,CAAC,QAAK,8BAA8B;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,sBAAsB;AAAC,SAAOC,gBAAgB,IAAIC,CAAC,QAAK,yBAAyB;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAAM;EAAA,IAALC,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAC,CAAC,CAAC;EAAE,IAAIG,CAAC,GAAClB,CAAC,CAAC,CAAC,CAAC,CAAC;EAACF,CAAC,CAAC,MAAI;IAACqB,qBAAqB,CAAC,MAAI;MAACD,CAAC,CAACE,OAAO,GAACN,CAAC;IAAA,CAAC,CAAC;EAAA,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC;EAAC,SAASO,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;IAAC,IAAG,CAACL,CAAC,CAACE,OAAO,IAAEE,CAAC,CAACE,gBAAgB,EAAC;IAAO,IAAIC,CAAC,GAACF,CAAC,CAACD,CAAC,CAAC;IAAC,IAAGG,CAAC,KAAG,IAAI,IAAE,CAACA,CAAC,CAACC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACF,CAAC,CAAC,IAAE,CAACA,CAAC,CAACG,WAAW,EAAC;IAAO,IAAIC,CAAC,GAAC,SAASC,CAACA,CAACC,CAAC,EAAC;MAAC,OAAO,OAAOA,CAAC,IAAE,UAAU,GAACD,CAAC,CAACC,CAAC,CAAC,CAAC,CAAC,GAACC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,IAAEA,CAAC,YAAYG,GAAG,GAACH,CAAC,GAAC,CAACA,CAAC,CAAC;IAAA,CAAC,CAACnB,CAAC,CAAC;IAAC,KAAI,IAAIkB,CAAC,IAAID,CAAC,EAAC;MAAC,IAAGC,CAAC,KAAG,IAAI,EAAC;MAAS,IAAIC,CAAC,GAACD,CAAC,YAAYK,WAAW,GAACL,CAAC,GAACA,CAAC,CAACV,OAAO;MAAC,IAAGW,CAAC,IAAE,IAAI,IAAEA,CAAC,CAACJ,QAAQ,CAACF,CAAC,CAAC,IAAEH,CAAC,CAACc,QAAQ,IAAEd,CAAC,CAACe,YAAY,CAAC,CAAC,CAACC,QAAQ,CAACP,CAAC,CAAC,EAAC;IAAM;IAAC,OAAM,CAAC3B,CAAC,CAACqB,CAAC,EAACvB,CAAC,CAACqC,KAAK,CAAC,IAAEd,CAAC,CAACe,QAAQ,KAAG,CAAC,CAAC,IAAElB,CAAC,CAACmB,cAAc,CAAC,CAAC,EAAC5B,CAAC,CAACS,CAAC,EAACG,CAAC,CAAC;EAAA;EAAC,IAAIiB,CAAC,GAAC1C,CAAC,CAAC,IAAI,CAAC;EAACQ,CAAC,CAAC,aAAa,EAACc,CAAC,IAAE;IAAC,IAAIC,CAAC,EAACE,CAAC;IAACP,CAAC,CAACE,OAAO,KAAGsB,CAAC,CAACtB,OAAO,GAAC,CAAC,CAACK,CAAC,GAAC,CAACF,CAAC,GAACD,CAAC,CAACe,YAAY,KAAG,IAAI,GAAC,KAAK,CAAC,GAACd,CAAC,CAACoB,IAAI,CAACrB,CAAC,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAACG,CAAC,CAAC,CAAC,CAAC,KAAGH,CAAC,CAACsB,MAAM,CAAC;EAAA,CAAC,EAAC,CAAC,CAAC,CAAC,EAACpC,CAAC,CAAC,WAAW,EAACc,CAAC,IAAE;IAAC,IAAIC,CAAC,EAACE,CAAC;IAACP,CAAC,CAACE,OAAO,KAAGsB,CAAC,CAACtB,OAAO,GAAC,CAAC,CAACK,CAAC,GAAC,CAACF,CAAC,GAACD,CAAC,CAACe,YAAY,KAAG,IAAI,GAAC,KAAK,CAAC,GAACd,CAAC,CAACoB,IAAI,CAACrB,CAAC,CAAC,KAAG,IAAI,GAAC,KAAK,CAAC,GAACG,CAAC,CAAC,CAAC,CAAC,KAAGH,CAAC,CAACsB,MAAM,CAAC;EAAA,CAAC,EAAC,CAAC,CAAC,CAAC,EAACpC,CAAC,CAAC,OAAO,EAACc,CAAC,IAAE;IAAChB,CAAC,CAAC,CAAC,IAAEoC,CAAC,CAACtB,OAAO,KAAGC,CAAC,CAACC,CAAC,EAAC,MAAIoB,CAAC,CAACtB,OAAO,CAAC,EAACsB,CAAC,CAACtB,OAAO,GAAC,IAAI,CAAC;EAAA,CAAC,EAAC,CAAC,CAAC,CAAC,EAACZ,CAAC,CAAC,UAAU,EAACc,CAAC,IAAED,CAAC,CAACC,CAAC,EAAC,MAAIA,CAAC,CAACsB,MAAM,YAAYT,WAAW,GAACb,CAAC,CAACsB,MAAM,GAAC,IAAI,CAAC,EAAC,CAAC,CAAC,CAAC,EAAClC,CAAC,CAAC,MAAM,EAACY,CAAC,IAAED,CAAC,CAACC,CAAC,EAAC,MAAIuB,MAAM,CAACC,QAAQ,CAACC,aAAa,YAAYC,iBAAiB,GAACH,MAAM,CAACC,QAAQ,CAACC,aAAa,GAAC,IAAI,CAAC,EAAC,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOpC,CAAC,IAAIsC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}