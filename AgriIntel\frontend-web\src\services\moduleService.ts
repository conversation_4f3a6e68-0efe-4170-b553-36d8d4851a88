/**
 * Module Service
 * 
 * This service provides a standardized interface for CRUD operations
 * that can be extended by specific module services.
 */

import api, { fetchData, postData, updateData, deleteData, ENDPOINTS } from './apiService';
import { useMongoDb } from '../hooks/useMongoDb';

// Base interface for all entities
export interface BaseEntity {
  id: string;
  [key: string]: any;
}

// Module service options
export interface ModuleServiceOptions {
  collectionName: string;
  endpoints: {
    getAll: string;
    getById: (id: string) => string;
    create: string;
    update: (id: string) => string;
    delete: (id: string) => string;
  };
}

/**
 * Create a module service with standardized CRUD operations
 * @param options Module service options
 */
export function createModuleService<T extends BaseEntity>(options: ModuleServiceOptions) {
  const { collectionName, endpoints } = options;

  return {
    /**
     * Get all items
     * @param params Query parameters
     */
    getAll: async (params?: Record<string, any>): Promise<T[]> => {
      try {
        return await fetchData(endpoints.getAll);
      } catch (error) {
        console.error(`Error fetching ${collectionName}:`, error);
        throw error;
      }
    },

    /**
     * Get item by ID
     * @param id Item ID
     */
    getById: async (id: string): Promise<T> => {
      try {
        return await fetchData(endpoints.getById(id));
      } catch (error) {
        console.error(`Error fetching ${collectionName} with ID ${id}:`, error);
        throw error;
      }
    },

    /**
     * Create new item
     * @param data Item data
     */
    create: async (data: Omit<T, 'id'>): Promise<T> => {
      try {
        return await postData(endpoints.create, data);
      } catch (error) {
        console.error(`Error creating ${collectionName}:`, error);
        throw error;
      }
    },

    /**
     * Update existing item
     * @param id Item ID
     * @param data Updated item data
     */
    update: async (id: string, data: Partial<T>): Promise<T> => {
      try {
        return await updateData(endpoints.update(id), data);
      } catch (error) {
        console.error(`Error updating ${collectionName} with ID ${id}:`, error);
        throw error;
      }
    },

    /**
     * Delete item
     * @param id Item ID
     */
    delete: async (id: string): Promise<void> => {
      try {
        await deleteData(endpoints.delete(id));
      } catch (error) {
        console.error(`Error deleting ${collectionName} with ID ${id}:`, error);
        throw error;
      }
    },
  };
}

/**
 * Create animal service
 */
export const animalService = createModuleService<any>({
  collectionName: 'animals',
  endpoints: {
    getAll: ENDPOINTS.ANIMALS,
    getById: (id) => ENDPOINTS.ANIMAL_BY_ID(id),
    create: ENDPOINTS.ANIMALS,
    update: (id) => ENDPOINTS.ANIMAL_BY_ID(id),
    delete: (id) => ENDPOINTS.ANIMAL_BY_ID(id),
  },
});

/**
 * Create health service
 */
export const healthService = createModuleService<any>({
  collectionName: 'health_records',
  endpoints: {
    getAll: ENDPOINTS.HEALTH_RECORDS,
    getById: (id) => ENDPOINTS.HEALTH_RECORD_BY_ID(id),
    create: ENDPOINTS.HEALTH_RECORDS,
    update: (id) => ENDPOINTS.HEALTH_RECORD_BY_ID(id),
    delete: (id) => ENDPOINTS.HEALTH_RECORD_BY_ID(id),
  },
});

/**
 * Create breeding service
 */
export const breedingService = createModuleService<any>({
  collectionName: 'breeding_records',
  endpoints: {
    getAll: ENDPOINTS.BREEDING_RECORDS,
    getById: (id) => ENDPOINTS.BREEDING_RECORD_BY_ID(id),
    create: ENDPOINTS.BREEDING_RECORDS,
    update: (id) => ENDPOINTS.BREEDING_RECORD_BY_ID(id),
    delete: (id) => ENDPOINTS.BREEDING_RECORD_BY_ID(id),
  },
});

/**
 * Create feeding service
 */
export const feedingService = createModuleService<any>({
  collectionName: 'feeding_records',
  endpoints: {
    getAll: ENDPOINTS.FEEDING_RECORDS,
    getById: (id) => ENDPOINTS.FEEDING_RECORD_BY_ID(id),
    create: ENDPOINTS.FEEDING_RECORDS,
    update: (id) => ENDPOINTS.FEEDING_RECORD_BY_ID(id),
    delete: (id) => ENDPOINTS.FEEDING_RECORD_BY_ID(id),
  },
});

/**
 * Create inventory service
 */
export const inventoryService = createModuleService<any>({
  collectionName: 'inventory',
  endpoints: {
    getAll: ENDPOINTS.INVENTORY,
    getById: (id) => ENDPOINTS.INVENTORY_BY_ID(id),
    create: ENDPOINTS.INVENTORY,
    update: (id) => ENDPOINTS.INVENTORY_BY_ID(id),
    delete: (id) => ENDPOINTS.INVENTORY_BY_ID(id),
  },
});

/**
 * Create financial service
 */
export const financialService = createModuleService<any>({
  collectionName: 'financial_transactions',
  endpoints: {
    getAll: ENDPOINTS.FINANCIAL_TRANSACTIONS,
    getById: (id) => ENDPOINTS.FINANCIAL_TRANSACTION_BY_ID(id),
    create: ENDPOINTS.FINANCIAL_TRANSACTIONS,
    update: (id) => ENDPOINTS.FINANCIAL_TRANSACTION_BY_ID(id),
    delete: (id) => ENDPOINTS.FINANCIAL_TRANSACTION_BY_ID(id),
  },
});

/**
 * Create user service
 */
export const userService = createModuleService<any>({
  collectionName: 'users',
  endpoints: {
    getAll: ENDPOINTS.USERS,
    getById: (id) => ENDPOINTS.USER_BY_ID(id),
    create: ENDPOINTS.USERS,
    update: (id) => ENDPOINTS.USER_BY_ID(id),
    delete: (id) => ENDPOINTS.USER_BY_ID(id),
  },
});

export default {
  animalService,
  healthService,
  breedingService,
  feedingService,
  inventoryService,
  financialService,
  userService,
};
