{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";// MongoDB collection import removed - using API calls instead\nimport{EventEmitter}from'events';// Create a simple ObjectId class for use in the service\nclass ObjectId{constructor(id){this.id=void 0;this.id=id||'mock-id-'+Date.now();}toString(){return this.id;}toHexString(){return this.id;}}// Data change event emitter\nexport const dataChangeEmitter=new EventEmitter();// Event types\nexport let DataChangeEventType=/*#__PURE__*/function(DataChangeEventType){DataChangeEventType[\"CREATE\"]=\"create\";DataChangeEventType[\"UPDATE\"]=\"update\";DataChangeEventType[\"DELETE\"]=\"delete\";return DataChangeEventType;}({});// Event interface\n/**\n * Centralized data integration service\n * This service handles all data operations and ensures consistency across modules\n */class DataIntegrationService{/**\n   * Create a new document in a collection\n   * @param collectionName Collection name\n   * @param data Document data\n   * @returns Created document\n   */async createDocument(collectionName,data){try{// Add timestamps\nconst now=new Date();const documentToInsert=_objectSpread(_objectSpread({},data),{},{createdAt:now,updatedAt:now});// Get collection\nconst collection=getCollection(collectionName);// Insert document\nconst result=await collection.insertOne(documentToInsert);// Get inserted document\nconst insertedDocument=_objectSpread(_objectSpread({},documentToInsert),{},{_id:result.insertedId});// Emit data change event\ndataChangeEmitter.emit('dataChange',{type:DataChangeEventType.CREATE,collection:collectionName,documentId:result.insertedId.toString(),data:insertedDocument});// Update related collections\nawait this.updateRelatedCollections(collectionName,insertedDocument);return insertedDocument;}catch(error){console.error(\"Error creating document in \".concat(collectionName,\":\"),error);throw error;}}/**\n   * Update a document in a collection\n   * @param collectionName Collection name\n   * @param id Document ID\n   * @param data Document data\n   * @returns Updated document\n   */async updateDocument(collectionName,id,data){try{// Add updated timestamp\nconst now=new Date();const documentToUpdate=_objectSpread(_objectSpread({},data),{},{updatedAt:now});// Get collection\nconst collection=getCollection(collectionName);// Update document\nconst result=await collection.updateOne({_id:new ObjectId(id)},{$set:documentToUpdate});if(result.modifiedCount===0){throw new Error(\"Document with ID \".concat(id,\" not found in \").concat(collectionName));}// Get updated document\nconst updatedDocument=await collection.findOne({_id:new ObjectId(id)});// Emit data change event\ndataChangeEmitter.emit('dataChange',{type:DataChangeEventType.UPDATE,collection:collectionName,documentId:id,data:updatedDocument});// Update related collections\nawait this.updateRelatedCollections(collectionName,updatedDocument);return updatedDocument;}catch(error){console.error(\"Error updating document in \".concat(collectionName,\":\"),error);throw error;}}/**\n   * Delete a document from a collection\n   * @param collectionName Collection name\n   * @param id Document ID\n   * @returns Deletion result\n   */async deleteDocument(collectionName,id){try{// Get document before deletion for related updates\nconst collection=getCollection(collectionName);const documentToDelete=await collection.findOne({_id:new ObjectId(id)});if(!documentToDelete){throw new Error(\"Document with ID \".concat(id,\" not found in \").concat(collectionName));}// Delete document\nconst result=await collection.deleteOne({_id:new ObjectId(id)});if(result.deletedCount===0){throw new Error(\"Document with ID \".concat(id,\" not found in \").concat(collectionName));}// Emit data change event\ndataChangeEmitter.emit('dataChange',{type:DataChangeEventType.DELETE,collection:collectionName,documentId:id,data:documentToDelete});// Update related collections\nawait this.handleDeletedDocumentRelations(collectionName,id,documentToDelete);return true;}catch(error){console.error(\"Error deleting document from \".concat(collectionName,\":\"),error);throw error;}}/**\n   * Get a document from a collection\n   * @param collectionName Collection name\n   * @param id Document ID\n   * @returns Document\n   */async getDocument(collectionName,id){try{const collection=getCollection(collectionName);return await collection.findOne({_id:new ObjectId(id)});}catch(error){console.error(\"Error getting document from \".concat(collectionName,\":\"),error);throw error;}}/**\n   * Get all documents from a collection\n   * @param collectionName Collection name\n   * @param filter Filter\n   * @param limit Limit\n   * @param skip Skip\n   * @returns Documents\n   */async getDocuments(collectionName){let filter=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};let limit=arguments.length>2&&arguments[2]!==undefined?arguments[2]:0;let skip=arguments.length>3&&arguments[3]!==undefined?arguments[3]:0;try{const collection=getCollection(collectionName);return await collection.find(filter).skip(skip).limit(limit).toArray();}catch(error){console.error(\"Error getting documents from \".concat(collectionName,\":\"),error);throw error;}}/**\n   * Update related collections when a document is created or updated\n   * @param collectionName Collection name\n   * @param document Document\n   */async updateRelatedCollections(collectionName,document){try{switch(collectionName){case'animals':// Update animal count in dashboard stats\nawait this.updateDashboardStats();break;case'financial_records':// Update financial stats\nawait this.updateFinancialStats();break;case'health_records':// Update animal health status\nif(document.animalId){await this.updateAnimalHealthStatus(document.animalId);}break;case'feeding_records':// Update feed inventory\nif(document.feedTypeId){await this.updateFeedInventory(document.feedTypeId,document.quantity);}break;// Add more cases as needed\n}}catch(error){console.error('Error updating related collections:',error);// Don't throw error to prevent blocking the main operation\n}}/**\n   * Handle deleted document relations\n   * @param collectionName Collection name\n   * @param id Document ID\n   * @param document Deleted document\n   */async handleDeletedDocumentRelations(collectionName,id,document){try{switch(collectionName){case'animals':// Delete related records\nawait this.deleteRelatedRecords('health_records',{animalId:id});await this.deleteRelatedRecords('breeding_records',{$or:[{femaleId:id},{maleId:id}]});await this.deleteRelatedRecords('feeding_records',{animals:{$elemMatch:{$eq:id}}});// Update dashboard stats\nawait this.updateDashboardStats();break;case'financial_records':// Update financial stats\nawait this.updateFinancialStats();break;// Add more cases as needed\n}}catch(error){console.error('Error handling deleted document relations:',error);// Don't throw error to prevent blocking the main operation\n}}/**\n   * Delete related records\n   * @param collectionName Collection name\n   * @param filter Filter\n   */async deleteRelatedRecords(collectionName,filter){try{const collection=getCollection(collectionName);await collection.deleteMany(filter);}catch(error){console.error(\"Error deleting related records from \".concat(collectionName,\":\"),error);throw error;}}/**\n   * Update dashboard stats\n   */async updateDashboardStats(){try{// Get animals collection\nconst animalsCollection=getCollection('animals');// Count total animals\nconst totalAnimals=await animalsCollection.countDocuments();// Count active animals\nconst activeAnimals=await animalsCollection.countDocuments({status:'Active'});// Count animals by health status\nconst healthyCounts=await animalsCollection.countDocuments({healthStatus:'healthy'});const sickCounts=await animalsCollection.countDocuments({healthStatus:'sick'});const injuredCounts=await animalsCollection.countDocuments({healthStatus:'injured'});const pregnantCounts=await animalsCollection.countDocuments({healthStatus:'pregnant'});// Calculate health percentage\nconst healthPercentage=totalAnimals>0?Math.round(healthyCounts/totalAnimals*100):0;// Get dashboard stats collection\nconst statsCollection=getCollection('dashboard_stats');// Update dashboard stats\nawait statsCollection.updateOne({_id:'animal_stats'},{$set:{totalAnimals,activeAnimals,healthPercentage,byHealth:{healthy:healthyCounts,sick:sickCounts,injured:injuredCounts,pregnant:pregnantCounts},updatedAt:new Date()}},{upsert:true});}catch(error){console.error('Error updating dashboard stats:',error);throw error;}}/**\n   * Update financial stats\n   */async updateFinancialStats(){try{// Get financial records collection\nconst financialCollection=getCollection('financial_records');// Calculate total revenue\nconst revenueRecords=await financialCollection.find({type:'income'}).toArray();const totalRevenue=revenueRecords.reduce((sum,record)=>sum+record.amount,0);// Calculate total expenses\nconst expenseRecords=await financialCollection.find({type:'expense'}).toArray();const totalExpenses=expenseRecords.reduce((sum,record)=>sum+record.amount,0);// Calculate net profit\nconst netProfit=totalRevenue-totalExpenses;// Get dashboard stats collection\nconst statsCollection=getCollection('dashboard_stats');// Update financial stats\nawait statsCollection.updateOne({_id:'financial_stats'},{$set:{totalRevenue,totalExpenses,netProfit,updatedAt:new Date()}},{upsert:true});}catch(error){console.error('Error updating financial stats:',error);throw error;}}/**\n   * Update animal health status\n   * @param animalId Animal ID\n   */async updateAnimalHealthStatus(animalId){try{// Get health records collection\nconst healthCollection=getCollection('health_records');// Get latest health record for the animal\nconst latestRecord=await healthCollection.find({animalId}).sort({date:-1}).limit(1).toArray();if(latestRecord.length===0){return;}// Determine health status based on latest record\nlet healthStatus='healthy';if(latestRecord[0].condition==='Sick'){healthStatus='sick';}else if(latestRecord[0].condition==='Injured'){healthStatus='injured';}else if(latestRecord[0].condition==='Pregnant'){healthStatus='pregnant';}// Update animal health status\nconst animalsCollection=getCollection('animals');await animalsCollection.updateOne({_id:new ObjectId(animalId)},{$set:{healthStatus,updatedAt:new Date()}});}catch(error){console.error('Error updating animal health status:',error);throw error;}}/**\n   * Update feed inventory\n   * @param feedTypeId Feed type ID\n   * @param quantity Quantity used\n   */async updateFeedInventory(feedTypeId,quantity){try{// Get feed inventory collection\nconst feedInventoryCollection=getCollection('feed_inventory');// Update feed inventory\nawait feedInventoryCollection.updateOne({_id:new ObjectId(feedTypeId)},{$inc:{quantity:-quantity,updatedAt:new Date()}});}catch(error){console.error('Error updating feed inventory:',error);throw error;}}}// Export singleton instance\nexport const dataIntegrationService=new DataIntegrationService();", "map": {"version": 3, "names": ["EventEmitter", "ObjectId", "constructor", "id", "Date", "now", "toString", "toHexString", "dataChangeEmitter", "DataChangeEventType", "DataIntegrationService", "createDocument", "collectionName", "data", "documentToInsert", "_objectSpread", "createdAt", "updatedAt", "collection", "getCollection", "result", "insertOne", "insertedDocument", "_id", "insertedId", "emit", "type", "CREATE", "documentId", "updateRelatedCollections", "error", "console", "concat", "updateDocument", "documentToUpdate", "updateOne", "$set", "modifiedCount", "Error", "updatedDocument", "findOne", "UPDATE", "deleteDocument", "documentToDelete", "deleteOne", "deletedCount", "DELETE", "handleDeletedDocumentRelations", "getDocument", "getDocuments", "filter", "arguments", "length", "undefined", "limit", "skip", "find", "toArray", "document", "updateDashboardStats", "updateFinancialStats", "animalId", "updateAnimalHealthStatus", "feedTypeId", "updateFeedInventory", "quantity", "deleteRelatedRecords", "$or", "femaleId", "maleId", "animals", "$elemMatch", "$eq", "deleteMany", "animalsCollection", "totalAnimals", "countDocuments", "activeAnimals", "status", "healthyCounts", "healthStatus", "sickCounts", "injuredCounts", "pregnantCounts", "healthPercentage", "Math", "round", "statsCollection", "byHealth", "healthy", "sick", "injured", "pregnant", "upsert", "financialCollection", "revenueRecords", "totalRevenue", "reduce", "sum", "record", "amount", "expenseRecords", "totalExpenses", "netProfit", "healthCollection", "latestRecord", "sort", "date", "condition", "feedInventoryCollection", "$inc", "dataIntegrationService"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/services/dataIntegrationService.ts"], "sourcesContent": ["// MongoDB collection import removed - using API calls instead\nimport { EventEmitter } from 'events';\n\n// Create a simple ObjectId class for use in the service\nclass ObjectId {\n  id: string;\n\n  constructor(id?: string) {\n    this.id = id || 'mock-id-' + Date.now();\n  }\n\n  toString() {\n    return this.id;\n  }\n\n  toHexString() {\n    return this.id;\n  }\n}\n\n// Data change event emitter\nexport const dataChangeEmitter = new EventEmitter();\n\n// Event types\nexport enum DataChangeEventType {\n  CREATE = 'create',\n  UPDATE = 'update',\n  DELETE = 'delete'\n}\n\n// Event interface\nexport interface DataChangeEvent {\n  type: DataChangeEventType;\n  collection: string;\n  documentId: string;\n  data?: any;\n}\n\n/**\n * Centralized data integration service\n * This service handles all data operations and ensures consistency across modules\n */\nclass DataIntegrationService {\n  /**\n   * Create a new document in a collection\n   * @param collectionName Collection name\n   * @param data Document data\n   * @returns Created document\n   */\n  async createDocument(collectionName: string, data: any): Promise<any> {\n    try {\n      // Add timestamps\n      const now = new Date();\n      const documentToInsert = {\n        ...data,\n        createdAt: now,\n        updatedAt: now\n      };\n\n      // Get collection\n      const collection = getCollection(collectionName);\n\n      // Insert document\n      const result = await collection.insertOne(documentToInsert);\n\n      // Get inserted document\n      const insertedDocument = {\n        ...documentToInsert,\n        _id: result.insertedId\n      };\n\n      // Emit data change event\n      dataChangeEmitter.emit('dataChange', {\n        type: DataChangeEventType.CREATE,\n        collection: collectionName,\n        documentId: result.insertedId.toString(),\n        data: insertedDocument\n      });\n\n      // Update related collections\n      await this.updateRelatedCollections(collectionName, insertedDocument);\n\n      return insertedDocument;\n    } catch (error) {\n      console.error(`Error creating document in ${collectionName}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update a document in a collection\n   * @param collectionName Collection name\n   * @param id Document ID\n   * @param data Document data\n   * @returns Updated document\n   */\n  async updateDocument(collectionName: string, id: string, data: any): Promise<any> {\n    try {\n      // Add updated timestamp\n      const now = new Date();\n      const documentToUpdate = {\n        ...data,\n        updatedAt: now\n      };\n\n      // Get collection\n      const collection = getCollection(collectionName);\n\n      // Update document\n      const result = await collection.updateOne(\n        { _id: new ObjectId(id) },\n        { $set: documentToUpdate }\n      );\n\n      if (result.modifiedCount === 0) {\n        throw new Error(`Document with ID ${id} not found in ${collectionName}`);\n      }\n\n      // Get updated document\n      const updatedDocument = await collection.findOne({ _id: new ObjectId(id) });\n\n      // Emit data change event\n      dataChangeEmitter.emit('dataChange', {\n        type: DataChangeEventType.UPDATE,\n        collection: collectionName,\n        documentId: id,\n        data: updatedDocument\n      });\n\n      // Update related collections\n      await this.updateRelatedCollections(collectionName, updatedDocument);\n\n      return updatedDocument;\n    } catch (error) {\n      console.error(`Error updating document in ${collectionName}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Delete a document from a collection\n   * @param collectionName Collection name\n   * @param id Document ID\n   * @returns Deletion result\n   */\n  async deleteDocument(collectionName: string, id: string): Promise<boolean> {\n    try {\n      // Get document before deletion for related updates\n      const collection = getCollection(collectionName);\n      const documentToDelete = await collection.findOne({ _id: new ObjectId(id) });\n\n      if (!documentToDelete) {\n        throw new Error(`Document with ID ${id} not found in ${collectionName}`);\n      }\n\n      // Delete document\n      const result = await collection.deleteOne({ _id: new ObjectId(id) });\n\n      if (result.deletedCount === 0) {\n        throw new Error(`Document with ID ${id} not found in ${collectionName}`);\n      }\n\n      // Emit data change event\n      dataChangeEmitter.emit('dataChange', {\n        type: DataChangeEventType.DELETE,\n        collection: collectionName,\n        documentId: id,\n        data: documentToDelete\n      });\n\n      // Update related collections\n      await this.handleDeletedDocumentRelations(collectionName, id, documentToDelete);\n\n      return true;\n    } catch (error) {\n      console.error(`Error deleting document from ${collectionName}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get a document from a collection\n   * @param collectionName Collection name\n   * @param id Document ID\n   * @returns Document\n   */\n  async getDocument(collectionName: string, id: string): Promise<any> {\n    try {\n      const collection = getCollection(collectionName);\n      return await collection.findOne({ _id: new ObjectId(id) });\n    } catch (error) {\n      console.error(`Error getting document from ${collectionName}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Get all documents from a collection\n   * @param collectionName Collection name\n   * @param filter Filter\n   * @param limit Limit\n   * @param skip Skip\n   * @returns Documents\n   */\n  async getDocuments(\n    collectionName: string,\n    filter: any = {},\n    limit: number = 0,\n    skip: number = 0\n  ): Promise<any[]> {\n    try {\n      const collection = getCollection(collectionName);\n      return await collection.find(filter).skip(skip).limit(limit).toArray();\n    } catch (error) {\n      console.error(`Error getting documents from ${collectionName}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update related collections when a document is created or updated\n   * @param collectionName Collection name\n   * @param document Document\n   */\n  private async updateRelatedCollections(collectionName: string, document: any): Promise<void> {\n    try {\n      switch (collectionName) {\n        case 'animals':\n          // Update animal count in dashboard stats\n          await this.updateDashboardStats();\n          break;\n        case 'financial_records':\n          // Update financial stats\n          await this.updateFinancialStats();\n          break;\n        case 'health_records':\n          // Update animal health status\n          if (document.animalId) {\n            await this.updateAnimalHealthStatus(document.animalId);\n          }\n          break;\n        case 'feeding_records':\n          // Update feed inventory\n          if (document.feedTypeId) {\n            await this.updateFeedInventory(document.feedTypeId, document.quantity);\n          }\n          break;\n        // Add more cases as needed\n      }\n    } catch (error) {\n      console.error('Error updating related collections:', error);\n      // Don't throw error to prevent blocking the main operation\n    }\n  }\n\n  /**\n   * Handle deleted document relations\n   * @param collectionName Collection name\n   * @param id Document ID\n   * @param document Deleted document\n   */\n  private async handleDeletedDocumentRelations(\n    collectionName: string,\n    id: string,\n    document: any\n  ): Promise<void> {\n    try {\n      switch (collectionName) {\n        case 'animals':\n          // Delete related records\n          await this.deleteRelatedRecords('health_records', { animalId: id });\n          await this.deleteRelatedRecords('breeding_records', {\n            $or: [{ femaleId: id }, { maleId: id }]\n          });\n          await this.deleteRelatedRecords('feeding_records', {\n            animals: { $elemMatch: { $eq: id } }\n          });\n\n          // Update dashboard stats\n          await this.updateDashboardStats();\n          break;\n        case 'financial_records':\n          // Update financial stats\n          await this.updateFinancialStats();\n          break;\n        // Add more cases as needed\n      }\n    } catch (error) {\n      console.error('Error handling deleted document relations:', error);\n      // Don't throw error to prevent blocking the main operation\n    }\n  }\n\n  /**\n   * Delete related records\n   * @param collectionName Collection name\n   * @param filter Filter\n   */\n  private async deleteRelatedRecords(collectionName: string, filter: any): Promise<void> {\n    try {\n      const collection = getCollection(collectionName);\n      await collection.deleteMany(filter);\n    } catch (error) {\n      console.error(`Error deleting related records from ${collectionName}:`, error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update dashboard stats\n   */\n  private async updateDashboardStats(): Promise<void> {\n    try {\n      // Get animals collection\n      const animalsCollection = getCollection('animals');\n\n      // Count total animals\n      const totalAnimals = await animalsCollection.countDocuments();\n\n      // Count active animals\n      const activeAnimals = await animalsCollection.countDocuments({ status: 'Active' });\n\n      // Count animals by health status\n      const healthyCounts = await animalsCollection.countDocuments({ healthStatus: 'healthy' });\n      const sickCounts = await animalsCollection.countDocuments({ healthStatus: 'sick' });\n      const injuredCounts = await animalsCollection.countDocuments({ healthStatus: 'injured' });\n      const pregnantCounts = await animalsCollection.countDocuments({ healthStatus: 'pregnant' });\n\n      // Calculate health percentage\n      const healthPercentage = totalAnimals > 0\n        ? Math.round((healthyCounts / totalAnimals) * 100)\n        : 0;\n\n      // Get dashboard stats collection\n      const statsCollection = getCollection('dashboard_stats');\n\n      // Update dashboard stats\n      await statsCollection.updateOne(\n        { _id: 'animal_stats' },\n        {\n          $set: {\n            totalAnimals,\n            activeAnimals,\n            healthPercentage,\n            byHealth: {\n              healthy: healthyCounts,\n              sick: sickCounts,\n              injured: injuredCounts,\n              pregnant: pregnantCounts\n            },\n            updatedAt: new Date()\n          }\n        },\n        { upsert: true }\n      );\n    } catch (error) {\n      console.error('Error updating dashboard stats:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update financial stats\n   */\n  private async updateFinancialStats(): Promise<void> {\n    try {\n      // Get financial records collection\n      const financialCollection = getCollection('financial_records');\n\n      // Calculate total revenue\n      const revenueRecords = await financialCollection.find({ type: 'income' }).toArray();\n      const totalRevenue = revenueRecords.reduce((sum, record) => sum + record.amount, 0);\n\n      // Calculate total expenses\n      const expenseRecords = await financialCollection.find({ type: 'expense' }).toArray();\n      const totalExpenses = expenseRecords.reduce((sum, record) => sum + record.amount, 0);\n\n      // Calculate net profit\n      const netProfit = totalRevenue - totalExpenses;\n\n      // Get dashboard stats collection\n      const statsCollection = getCollection('dashboard_stats');\n\n      // Update financial stats\n      await statsCollection.updateOne(\n        { _id: 'financial_stats' },\n        {\n          $set: {\n            totalRevenue,\n            totalExpenses,\n            netProfit,\n            updatedAt: new Date()\n          }\n        },\n        { upsert: true }\n      );\n    } catch (error) {\n      console.error('Error updating financial stats:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update animal health status\n   * @param animalId Animal ID\n   */\n  private async updateAnimalHealthStatus(animalId: string): Promise<void> {\n    try {\n      // Get health records collection\n      const healthCollection = getCollection('health_records');\n\n      // Get latest health record for the animal\n      const latestRecord = await healthCollection.find({ animalId })\n        .sort({ date: -1 })\n        .limit(1)\n        .toArray();\n\n      if (latestRecord.length === 0) {\n        return;\n      }\n\n      // Determine health status based on latest record\n      let healthStatus = 'healthy';\n      if (latestRecord[0].condition === 'Sick') {\n        healthStatus = 'sick';\n      } else if (latestRecord[0].condition === 'Injured') {\n        healthStatus = 'injured';\n      } else if (latestRecord[0].condition === 'Pregnant') {\n        healthStatus = 'pregnant';\n      }\n\n      // Update animal health status\n      const animalsCollection = getCollection('animals');\n      await animalsCollection.updateOne(\n        { _id: new ObjectId(animalId) },\n        { $set: { healthStatus, updatedAt: new Date() } }\n      );\n    } catch (error) {\n      console.error('Error updating animal health status:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Update feed inventory\n   * @param feedTypeId Feed type ID\n   * @param quantity Quantity used\n   */\n  private async updateFeedInventory(feedTypeId: string, quantity: number): Promise<void> {\n    try {\n      // Get feed inventory collection\n      const feedInventoryCollection = getCollection('feed_inventory');\n\n      // Update feed inventory\n      await feedInventoryCollection.updateOne(\n        { _id: new ObjectId(feedTypeId) },\n        { $inc: { quantity: -quantity, updatedAt: new Date() } }\n      );\n    } catch (error) {\n      console.error('Error updating feed inventory:', error);\n      throw error;\n    }\n  }\n}\n\n// Export singleton instance\nexport const dataIntegrationService = new DataIntegrationService();\n"], "mappings": "gJAAA;AACA,OAASA,YAAY,KAAQ,QAAQ,CAErC;AACA,KAAM,CAAAC,QAAS,CAGbC,WAAWA,CAACC,EAAW,CAAE,MAFzBA,EAAE,QAGA,IAAI,CAACA,EAAE,CAAGA,EAAE,EAAI,UAAU,CAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,CACzC,CAEAC,QAAQA,CAAA,CAAG,CACT,MAAO,KAAI,CAACH,EAAE,CAChB,CAEAI,WAAWA,CAAA,CAAG,CACZ,MAAO,KAAI,CAACJ,EAAE,CAChB,CACF,CAEA;AACA,MAAO,MAAM,CAAAK,iBAAiB,CAAG,GAAI,CAAAR,YAAY,CAAC,CAAC,CAEnD;AACA,UAAY,CAAAS,mBAAmB,uBAAnBA,mBAAmB,EAAnBA,mBAAmB,oBAAnBA,mBAAmB,oBAAnBA,mBAAmB,0BAAnB,CAAAA,mBAAmB,OAM/B;AAQA;AACA;AACA;AACA,GACA,KAAM,CAAAC,sBAAuB,CAC3B;AACF;AACA;AACA;AACA;AACA,KACE,KAAM,CAAAC,cAAcA,CAACC,cAAsB,CAAEC,IAAS,CAAgB,CACpE,GAAI,CACF;AACA,KAAM,CAAAR,GAAG,CAAG,GAAI,CAAAD,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAU,gBAAgB,CAAAC,aAAA,CAAAA,aAAA,IACjBF,IAAI,MACPG,SAAS,CAAEX,GAAG,CACdY,SAAS,CAAEZ,GAAG,EACf,CAED;AACA,KAAM,CAAAa,UAAU,CAAGC,aAAa,CAACP,cAAc,CAAC,CAEhD;AACA,KAAM,CAAAQ,MAAM,CAAG,KAAM,CAAAF,UAAU,CAACG,SAAS,CAACP,gBAAgB,CAAC,CAE3D;AACA,KAAM,CAAAQ,gBAAgB,CAAAP,aAAA,CAAAA,aAAA,IACjBD,gBAAgB,MACnBS,GAAG,CAAEH,MAAM,CAACI,UAAU,EACvB,CAED;AACAhB,iBAAiB,CAACiB,IAAI,CAAC,YAAY,CAAE,CACnCC,IAAI,CAAEjB,mBAAmB,CAACkB,MAAM,CAChCT,UAAU,CAAEN,cAAc,CAC1BgB,UAAU,CAAER,MAAM,CAACI,UAAU,CAAClB,QAAQ,CAAC,CAAC,CACxCO,IAAI,CAAES,gBACR,CAAC,CAAC,CAEF;AACA,KAAM,KAAI,CAACO,wBAAwB,CAACjB,cAAc,CAAEU,gBAAgB,CAAC,CAErE,MAAO,CAAAA,gBAAgB,CACzB,CAAE,MAAOQ,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,+BAAAE,MAAA,CAA+BpB,cAAc,MAAKkB,KAAK,CAAC,CACrE,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA;AACA;AACA;AACA;AACA,KACE,KAAM,CAAAG,cAAcA,CAACrB,cAAsB,CAAET,EAAU,CAAEU,IAAS,CAAgB,CAChF,GAAI,CACF;AACA,KAAM,CAAAR,GAAG,CAAG,GAAI,CAAAD,IAAI,CAAC,CAAC,CACtB,KAAM,CAAA8B,gBAAgB,CAAAnB,aAAA,CAAAA,aAAA,IACjBF,IAAI,MACPI,SAAS,CAAEZ,GAAG,EACf,CAED;AACA,KAAM,CAAAa,UAAU,CAAGC,aAAa,CAACP,cAAc,CAAC,CAEhD;AACA,KAAM,CAAAQ,MAAM,CAAG,KAAM,CAAAF,UAAU,CAACiB,SAAS,CACvC,CAAEZ,GAAG,CAAE,GAAI,CAAAtB,QAAQ,CAACE,EAAE,CAAE,CAAC,CACzB,CAAEiC,IAAI,CAAEF,gBAAiB,CAC3B,CAAC,CAED,GAAId,MAAM,CAACiB,aAAa,GAAK,CAAC,CAAE,CAC9B,KAAM,IAAI,CAAAC,KAAK,qBAAAN,MAAA,CAAqB7B,EAAE,mBAAA6B,MAAA,CAAiBpB,cAAc,CAAE,CAAC,CAC1E,CAEA;AACA,KAAM,CAAA2B,eAAe,CAAG,KAAM,CAAArB,UAAU,CAACsB,OAAO,CAAC,CAAEjB,GAAG,CAAE,GAAI,CAAAtB,QAAQ,CAACE,EAAE,CAAE,CAAC,CAAC,CAE3E;AACAK,iBAAiB,CAACiB,IAAI,CAAC,YAAY,CAAE,CACnCC,IAAI,CAAEjB,mBAAmB,CAACgC,MAAM,CAChCvB,UAAU,CAAEN,cAAc,CAC1BgB,UAAU,CAAEzB,EAAE,CACdU,IAAI,CAAE0B,eACR,CAAC,CAAC,CAEF;AACA,KAAM,KAAI,CAACV,wBAAwB,CAACjB,cAAc,CAAE2B,eAAe,CAAC,CAEpE,MAAO,CAAAA,eAAe,CACxB,CAAE,MAAOT,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,+BAAAE,MAAA,CAA+BpB,cAAc,MAAKkB,KAAK,CAAC,CACrE,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA;AACA;AACA;AACA,KACE,KAAM,CAAAY,cAAcA,CAAC9B,cAAsB,CAAET,EAAU,CAAoB,CACzE,GAAI,CACF;AACA,KAAM,CAAAe,UAAU,CAAGC,aAAa,CAACP,cAAc,CAAC,CAChD,KAAM,CAAA+B,gBAAgB,CAAG,KAAM,CAAAzB,UAAU,CAACsB,OAAO,CAAC,CAAEjB,GAAG,CAAE,GAAI,CAAAtB,QAAQ,CAACE,EAAE,CAAE,CAAC,CAAC,CAE5E,GAAI,CAACwC,gBAAgB,CAAE,CACrB,KAAM,IAAI,CAAAL,KAAK,qBAAAN,MAAA,CAAqB7B,EAAE,mBAAA6B,MAAA,CAAiBpB,cAAc,CAAE,CAAC,CAC1E,CAEA;AACA,KAAM,CAAAQ,MAAM,CAAG,KAAM,CAAAF,UAAU,CAAC0B,SAAS,CAAC,CAAErB,GAAG,CAAE,GAAI,CAAAtB,QAAQ,CAACE,EAAE,CAAE,CAAC,CAAC,CAEpE,GAAIiB,MAAM,CAACyB,YAAY,GAAK,CAAC,CAAE,CAC7B,KAAM,IAAI,CAAAP,KAAK,qBAAAN,MAAA,CAAqB7B,EAAE,mBAAA6B,MAAA,CAAiBpB,cAAc,CAAE,CAAC,CAC1E,CAEA;AACAJ,iBAAiB,CAACiB,IAAI,CAAC,YAAY,CAAE,CACnCC,IAAI,CAAEjB,mBAAmB,CAACqC,MAAM,CAChC5B,UAAU,CAAEN,cAAc,CAC1BgB,UAAU,CAAEzB,EAAE,CACdU,IAAI,CAAE8B,gBACR,CAAC,CAAC,CAEF;AACA,KAAM,KAAI,CAACI,8BAA8B,CAACnC,cAAc,CAAET,EAAE,CAAEwC,gBAAgB,CAAC,CAE/E,MAAO,KAAI,CACb,CAAE,MAAOb,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,iCAAAE,MAAA,CAAiCpB,cAAc,MAAKkB,KAAK,CAAC,CACvE,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA;AACA;AACA;AACA,KACE,KAAM,CAAAkB,WAAWA,CAACpC,cAAsB,CAAET,EAAU,CAAgB,CAClE,GAAI,CACF,KAAM,CAAAe,UAAU,CAAGC,aAAa,CAACP,cAAc,CAAC,CAChD,MAAO,MAAM,CAAAM,UAAU,CAACsB,OAAO,CAAC,CAAEjB,GAAG,CAAE,GAAI,CAAAtB,QAAQ,CAACE,EAAE,CAAE,CAAC,CAAC,CAC5D,CAAE,MAAO2B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,gCAAAE,MAAA,CAAgCpB,cAAc,MAAKkB,KAAK,CAAC,CACtE,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA;AACA;AACA;AACA;AACA;AACA,KACE,KAAM,CAAAmB,YAAYA,CAChBrC,cAAsB,CAIN,IAHhB,CAAAsC,MAAW,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,IAChB,CAAAG,KAAa,CAAAH,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,IACjB,CAAAI,IAAY,CAAAJ,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAEhB,GAAI,CACF,KAAM,CAAAjC,UAAU,CAAGC,aAAa,CAACP,cAAc,CAAC,CAChD,MAAO,MAAM,CAAAM,UAAU,CAACsC,IAAI,CAACN,MAAM,CAAC,CAACK,IAAI,CAACA,IAAI,CAAC,CAACD,KAAK,CAACA,KAAK,CAAC,CAACG,OAAO,CAAC,CAAC,CACxE,CAAE,MAAO3B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,iCAAAE,MAAA,CAAiCpB,cAAc,MAAKkB,KAAK,CAAC,CACvE,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA;AACA;AACA,KACE,KAAc,CAAAD,wBAAwBA,CAACjB,cAAsB,CAAE8C,QAAa,CAAiB,CAC3F,GAAI,CACF,OAAQ9C,cAAc,EACpB,IAAK,SAAS,CACZ;AACA,KAAM,KAAI,CAAC+C,oBAAoB,CAAC,CAAC,CACjC,MACF,IAAK,mBAAmB,CACtB;AACA,KAAM,KAAI,CAACC,oBAAoB,CAAC,CAAC,CACjC,MACF,IAAK,gBAAgB,CACnB;AACA,GAAIF,QAAQ,CAACG,QAAQ,CAAE,CACrB,KAAM,KAAI,CAACC,wBAAwB,CAACJ,QAAQ,CAACG,QAAQ,CAAC,CACxD,CACA,MACF,IAAK,iBAAiB,CACpB;AACA,GAAIH,QAAQ,CAACK,UAAU,CAAE,CACvB,KAAM,KAAI,CAACC,mBAAmB,CAACN,QAAQ,CAACK,UAAU,CAAEL,QAAQ,CAACO,QAAQ,CAAC,CACxE,CACA,MACF;AACF,CACF,CAAE,MAAOnC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,qCAAqC,CAAEA,KAAK,CAAC,CAC3D;AACF,CACF,CAEA;AACF;AACA;AACA;AACA;AACA,KACE,KAAc,CAAAiB,8BAA8BA,CAC1CnC,cAAsB,CACtBT,EAAU,CACVuD,QAAa,CACE,CACf,GAAI,CACF,OAAQ9C,cAAc,EACpB,IAAK,SAAS,CACZ;AACA,KAAM,KAAI,CAACsD,oBAAoB,CAAC,gBAAgB,CAAE,CAAEL,QAAQ,CAAE1D,EAAG,CAAC,CAAC,CACnE,KAAM,KAAI,CAAC+D,oBAAoB,CAAC,kBAAkB,CAAE,CAClDC,GAAG,CAAE,CAAC,CAAEC,QAAQ,CAAEjE,EAAG,CAAC,CAAE,CAAEkE,MAAM,CAAElE,EAAG,CAAC,CACxC,CAAC,CAAC,CACF,KAAM,KAAI,CAAC+D,oBAAoB,CAAC,iBAAiB,CAAE,CACjDI,OAAO,CAAE,CAAEC,UAAU,CAAE,CAAEC,GAAG,CAAErE,EAAG,CAAE,CACrC,CAAC,CAAC,CAEF;AACA,KAAM,KAAI,CAACwD,oBAAoB,CAAC,CAAC,CACjC,MACF,IAAK,mBAAmB,CACtB;AACA,KAAM,KAAI,CAACC,oBAAoB,CAAC,CAAC,CACjC,MACF;AACF,CACF,CAAE,MAAO9B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,4CAA4C,CAAEA,KAAK,CAAC,CAClE;AACF,CACF,CAEA;AACF;AACA;AACA;AACA,KACE,KAAc,CAAAoC,oBAAoBA,CAACtD,cAAsB,CAAEsC,MAAW,CAAiB,CACrF,GAAI,CACF,KAAM,CAAAhC,UAAU,CAAGC,aAAa,CAACP,cAAc,CAAC,CAChD,KAAM,CAAAM,UAAU,CAACuD,UAAU,CAACvB,MAAM,CAAC,CACrC,CAAE,MAAOpB,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,wCAAAE,MAAA,CAAwCpB,cAAc,MAAKkB,KAAK,CAAC,CAC9E,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA,KACE,KAAc,CAAA6B,oBAAoBA,CAAA,CAAkB,CAClD,GAAI,CACF;AACA,KAAM,CAAAe,iBAAiB,CAAGvD,aAAa,CAAC,SAAS,CAAC,CAElD;AACA,KAAM,CAAAwD,YAAY,CAAG,KAAM,CAAAD,iBAAiB,CAACE,cAAc,CAAC,CAAC,CAE7D;AACA,KAAM,CAAAC,aAAa,CAAG,KAAM,CAAAH,iBAAiB,CAACE,cAAc,CAAC,CAAEE,MAAM,CAAE,QAAS,CAAC,CAAC,CAElF;AACA,KAAM,CAAAC,aAAa,CAAG,KAAM,CAAAL,iBAAiB,CAACE,cAAc,CAAC,CAAEI,YAAY,CAAE,SAAU,CAAC,CAAC,CACzF,KAAM,CAAAC,UAAU,CAAG,KAAM,CAAAP,iBAAiB,CAACE,cAAc,CAAC,CAAEI,YAAY,CAAE,MAAO,CAAC,CAAC,CACnF,KAAM,CAAAE,aAAa,CAAG,KAAM,CAAAR,iBAAiB,CAACE,cAAc,CAAC,CAAEI,YAAY,CAAE,SAAU,CAAC,CAAC,CACzF,KAAM,CAAAG,cAAc,CAAG,KAAM,CAAAT,iBAAiB,CAACE,cAAc,CAAC,CAAEI,YAAY,CAAE,UAAW,CAAC,CAAC,CAE3F;AACA,KAAM,CAAAI,gBAAgB,CAAGT,YAAY,CAAG,CAAC,CACrCU,IAAI,CAACC,KAAK,CAAEP,aAAa,CAAGJ,YAAY,CAAI,GAAG,CAAC,CAChD,CAAC,CAEL;AACA,KAAM,CAAAY,eAAe,CAAGpE,aAAa,CAAC,iBAAiB,CAAC,CAExD;AACA,KAAM,CAAAoE,eAAe,CAACpD,SAAS,CAC7B,CAAEZ,GAAG,CAAE,cAAe,CAAC,CACvB,CACEa,IAAI,CAAE,CACJuC,YAAY,CACZE,aAAa,CACbO,gBAAgB,CAChBI,QAAQ,CAAE,CACRC,OAAO,CAAEV,aAAa,CACtBW,IAAI,CAAET,UAAU,CAChBU,OAAO,CAAET,aAAa,CACtBU,QAAQ,CAAET,cACZ,CAAC,CACDlE,SAAS,CAAE,GAAI,CAAAb,IAAI,CAAC,CACtB,CACF,CAAC,CACD,CAAEyF,MAAM,CAAE,IAAK,CACjB,CAAC,CACH,CAAE,MAAO/D,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvD,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA,KACE,KAAc,CAAA8B,oBAAoBA,CAAA,CAAkB,CAClD,GAAI,CACF;AACA,KAAM,CAAAkC,mBAAmB,CAAG3E,aAAa,CAAC,mBAAmB,CAAC,CAE9D;AACA,KAAM,CAAA4E,cAAc,CAAG,KAAM,CAAAD,mBAAmB,CAACtC,IAAI,CAAC,CAAE9B,IAAI,CAAE,QAAS,CAAC,CAAC,CAAC+B,OAAO,CAAC,CAAC,CACnF,KAAM,CAAAuC,YAAY,CAAGD,cAAc,CAACE,MAAM,CAAC,CAACC,GAAG,CAAEC,MAAM,GAAKD,GAAG,CAAGC,MAAM,CAACC,MAAM,CAAE,CAAC,CAAC,CAEnF;AACA,KAAM,CAAAC,cAAc,CAAG,KAAM,CAAAP,mBAAmB,CAACtC,IAAI,CAAC,CAAE9B,IAAI,CAAE,SAAU,CAAC,CAAC,CAAC+B,OAAO,CAAC,CAAC,CACpF,KAAM,CAAA6C,aAAa,CAAGD,cAAc,CAACJ,MAAM,CAAC,CAACC,GAAG,CAAEC,MAAM,GAAKD,GAAG,CAAGC,MAAM,CAACC,MAAM,CAAE,CAAC,CAAC,CAEpF;AACA,KAAM,CAAAG,SAAS,CAAGP,YAAY,CAAGM,aAAa,CAE9C;AACA,KAAM,CAAAf,eAAe,CAAGpE,aAAa,CAAC,iBAAiB,CAAC,CAExD;AACA,KAAM,CAAAoE,eAAe,CAACpD,SAAS,CAC7B,CAAEZ,GAAG,CAAE,iBAAkB,CAAC,CAC1B,CACEa,IAAI,CAAE,CACJ4D,YAAY,CACZM,aAAa,CACbC,SAAS,CACTtF,SAAS,CAAE,GAAI,CAAAb,IAAI,CAAC,CACtB,CACF,CAAC,CACD,CAAEyF,MAAM,CAAE,IAAK,CACjB,CAAC,CACH,CAAE,MAAO/D,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvD,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA;AACA,KACE,KAAc,CAAAgC,wBAAwBA,CAACD,QAAgB,CAAiB,CACtE,GAAI,CACF;AACA,KAAM,CAAA2C,gBAAgB,CAAGrF,aAAa,CAAC,gBAAgB,CAAC,CAExD;AACA,KAAM,CAAAsF,YAAY,CAAG,KAAM,CAAAD,gBAAgB,CAAChD,IAAI,CAAC,CAAEK,QAAS,CAAC,CAAC,CAC3D6C,IAAI,CAAC,CAAEC,IAAI,CAAE,CAAC,CAAE,CAAC,CAAC,CAClBrD,KAAK,CAAC,CAAC,CAAC,CACRG,OAAO,CAAC,CAAC,CAEZ,GAAIgD,YAAY,CAACrD,MAAM,GAAK,CAAC,CAAE,CAC7B,OACF,CAEA;AACA,GAAI,CAAA4B,YAAY,CAAG,SAAS,CAC5B,GAAIyB,YAAY,CAAC,CAAC,CAAC,CAACG,SAAS,GAAK,MAAM,CAAE,CACxC5B,YAAY,CAAG,MAAM,CACvB,CAAC,IAAM,IAAIyB,YAAY,CAAC,CAAC,CAAC,CAACG,SAAS,GAAK,SAAS,CAAE,CAClD5B,YAAY,CAAG,SAAS,CAC1B,CAAC,IAAM,IAAIyB,YAAY,CAAC,CAAC,CAAC,CAACG,SAAS,GAAK,UAAU,CAAE,CACnD5B,YAAY,CAAG,UAAU,CAC3B,CAEA;AACA,KAAM,CAAAN,iBAAiB,CAAGvD,aAAa,CAAC,SAAS,CAAC,CAClD,KAAM,CAAAuD,iBAAiB,CAACvC,SAAS,CAC/B,CAAEZ,GAAG,CAAE,GAAI,CAAAtB,QAAQ,CAAC4D,QAAQ,CAAE,CAAC,CAC/B,CAAEzB,IAAI,CAAE,CAAE4C,YAAY,CAAE/D,SAAS,CAAE,GAAI,CAAAb,IAAI,CAAC,CAAE,CAAE,CAClD,CAAC,CACH,CAAE,MAAO0B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,sCAAsC,CAAEA,KAAK,CAAC,CAC5D,KAAM,CAAAA,KAAK,CACb,CACF,CAEA;AACF;AACA;AACA;AACA,KACE,KAAc,CAAAkC,mBAAmBA,CAACD,UAAkB,CAAEE,QAAgB,CAAiB,CACrF,GAAI,CACF;AACA,KAAM,CAAA4C,uBAAuB,CAAG1F,aAAa,CAAC,gBAAgB,CAAC,CAE/D;AACA,KAAM,CAAA0F,uBAAuB,CAAC1E,SAAS,CACrC,CAAEZ,GAAG,CAAE,GAAI,CAAAtB,QAAQ,CAAC8D,UAAU,CAAE,CAAC,CACjC,CAAE+C,IAAI,CAAE,CAAE7C,QAAQ,CAAE,CAACA,QAAQ,CAAEhD,SAAS,CAAE,GAAI,CAAAb,IAAI,CAAC,CAAE,CAAE,CACzD,CAAC,CACH,CAAE,MAAO0B,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtD,KAAM,CAAAA,KAAK,CACb,CACF,CACF,CAEA;AACA,MAAO,MAAM,CAAAiF,sBAAsB,CAAG,GAAI,CAAArG,sBAAsB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}