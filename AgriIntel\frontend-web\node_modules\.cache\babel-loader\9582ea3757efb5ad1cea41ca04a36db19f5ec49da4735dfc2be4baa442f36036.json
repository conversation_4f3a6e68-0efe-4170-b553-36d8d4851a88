{"ast": null, "code": "import React,{useEffect}from'react';import{Routes,Route,Navigate}from'react-router-dom';import{useDispatch,useSelector}from'react-redux';import{useTranslation}from'react-i18next';import{Toaster}from'react-hot-toast';import{getCurrentUser}from'./store/slices/authSlice';import{setLanguage}from'./store/slices/uiSlice';// Layout Components\nimport Layout from'./components/layout/Layout';import AuthLayout from'./components/layout/AuthLayout';// Page Components\nimport LoginPage from'./pages/auth/LoginPage';import RegisterPage from'./pages/auth/RegisterPage';import DashboardPage from'./pages/dashboard/DashboardPage';import AnimalsPage from'./pages/animals/AnimalsPage';import AnimalDetailsPage from'./pages/animals/AnimalDetailsPage';import HealthPage from'./pages/health/HealthPage';import BreedingPage from'./pages/breeding/BreedingPage';import FeedingPage from'./pages/feeding/FeedingPage';import FinancialPage from'./pages/financial/FinancialPage';import ReportsPage from'./pages/reports/ReportsPage';import SettingsPage from'./pages/settings/SettingsPage';import NotFoundPage from'./pages/NotFoundPage';// Components\nimport LoadingSpinner from'./components/common/LoadingSpinner';import ProtectedRoute from'./components/auth/ProtectedRoute';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function App(){const dispatch=useDispatch();const{i18n}=useTranslation();const{isAuthenticated,isLoading:authLoading,token}=useSelector(state=>state.auth);const{language}=useSelector(state=>state.ui);useEffect(()=>{// Set language from Redux store\nif(language&&i18n.language!==language){i18n.changeLanguage(language);}},[language,i18n]);useEffect(()=>{// Auto-login if token exists\nif(token&&!isAuthenticated){dispatch(getCurrentUser());}},[dispatch,token,isAuthenticated]);useEffect(()=>{// Sync language changes with Redux store\nconst handleLanguageChange=lng=>{if(['en','af','st','tn','zu'].includes(lng)){dispatch(setLanguage(lng));}};i18n.on('languageChanged',handleLanguageChange);return()=>{i18n.off('languageChanged',handleLanguageChange);};},[dispatch,i18n]);// Show loading spinner while checking authentication\nif(authLoading){return/*#__PURE__*/_jsx(\"div\",{className:\"min-h-screen flex items-center justify-center bg-gray-50\",children:/*#__PURE__*/_jsx(LoadingSpinner,{size:\"large\"})});}return/*#__PURE__*/_jsxs(\"div\",{className:\"App\",children:[/*#__PURE__*/_jsxs(Routes,{children:[/*#__PURE__*/_jsx(Route,{path:\"/login\",element:!isAuthenticated?/*#__PURE__*/_jsx(AuthLayout,{children:/*#__PURE__*/_jsx(LoginPage,{})}):/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"/register\",element:!isAuthenticated?/*#__PURE__*/_jsx(AuthLayout,{children:/*#__PURE__*/_jsx(RegisterPage,{})}):/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true})}),/*#__PURE__*/_jsxs(Route,{path:\"/\",element:/*#__PURE__*/_jsx(ProtectedRoute,{children:/*#__PURE__*/_jsx(Layout,{})}),children:[/*#__PURE__*/_jsx(Route,{index:true,element:/*#__PURE__*/_jsx(Navigate,{to:\"/dashboard\",replace:true})}),/*#__PURE__*/_jsx(Route,{path:\"dashboard\",element:/*#__PURE__*/_jsx(DashboardPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"animals\",element:/*#__PURE__*/_jsx(AnimalsPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"animals/:id\",element:/*#__PURE__*/_jsx(AnimalDetailsPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"health\",element:/*#__PURE__*/_jsx(HealthPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"breeding\",element:/*#__PURE__*/_jsx(BreedingPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"feeding\",element:/*#__PURE__*/_jsx(FeedingPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"financial\",element:/*#__PURE__*/_jsx(FinancialPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"reports\",element:/*#__PURE__*/_jsx(ReportsPage,{})}),/*#__PURE__*/_jsx(Route,{path:\"settings\",element:/*#__PURE__*/_jsx(SettingsPage,{})})]}),/*#__PURE__*/_jsx(Route,{path:\"*\",element:/*#__PURE__*/_jsx(NotFoundPage,{})})]}),/*#__PURE__*/_jsx(Toaster,{position:\"top-right\",toastOptions:{duration:4000,style:{background:'#363636',color:'#fff'},success:{duration:3000,iconTheme:{primary:'#059669',secondary:'#fff'}},error:{duration:5000,iconTheme:{primary:'#dc2626',secondary:'#fff'}}}})]});}export default App;", "map": {"version": 3, "names": ["React", "useEffect", "Routes", "Route", "Navigate", "useDispatch", "useSelector", "useTranslation", "Toaster", "getCurrentUser", "setLanguage", "Layout", "AuthLayout", "LoginPage", "RegisterPage", "DashboardPage", "AnimalsPage", "AnimalDetailsPage", "HealthPage", "BreedingPage", "FeedingPage", "FinancialPage", "ReportsPage", "SettingsPage", "NotFoundPage", "LoadingSpinner", "ProtectedRoute", "jsx", "_jsx", "jsxs", "_jsxs", "App", "dispatch", "i18n", "isAuthenticated", "isLoading", "authLoading", "token", "state", "auth", "language", "ui", "changeLanguage", "handleLanguageChange", "lng", "includes", "on", "off", "className", "children", "size", "path", "element", "to", "replace", "index", "position", "toastOptions", "duration", "style", "background", "color", "success", "iconTheme", "primary", "secondary", "error"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/App.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\nimport { Routes, Route, Navigate } from 'react-router-dom';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport toast, { Toaster } from 'react-hot-toast';\n\nimport { RootState, AppDispatch } from './store/store';\nimport { getCurrentUser } from './store/slices/authSlice';\nimport { setLanguage } from './store/slices/uiSlice';\n\n// Layout Components\nimport Layout from './components/layout/Layout';\nimport AuthLayout from './components/layout/AuthLayout';\n\n// Page Components\nimport LoginPage from './pages/auth/LoginPage';\nimport RegisterPage from './pages/auth/RegisterPage';\nimport DashboardPage from './pages/dashboard/DashboardPage';\nimport AnimalsPage from './pages/animals/AnimalsPage';\nimport AnimalDetailsPage from './pages/animals/AnimalDetailsPage';\nimport HealthPage from './pages/health/HealthPage';\nimport BreedingPage from './pages/breeding/BreedingPage';\nimport FeedingPage from './pages/feeding/FeedingPage';\nimport FinancialPage from './pages/financial/FinancialPage';\nimport ReportsPage from './pages/reports/ReportsPage';\nimport SettingsPage from './pages/settings/SettingsPage';\nimport NotFoundPage from './pages/NotFoundPage';\n\n// Components\nimport LoadingSpinner from './components/common/LoadingSpinner';\nimport ProtectedRoute from './components/auth/ProtectedRoute';\n\nfunction App() {\n  const dispatch = useDispatch<AppDispatch>();\n  const { i18n } = useTranslation();\n\n  const { isAuthenticated, isLoading: authLoading, token } = useSelector(\n    (state: RootState) => state.auth\n  );\n  const { language } = useSelector((state: RootState) => state.ui);\n\n  useEffect(() => {\n    // Set language from Redux store\n    if (language && i18n.language !== language) {\n      i18n.changeLanguage(language);\n    }\n  }, [language, i18n]);\n\n  useEffect(() => {\n    // Auto-login if token exists\n    if (token && !isAuthenticated) {\n      dispatch(getCurrentUser());\n    }\n  }, [dispatch, token, isAuthenticated]);\n\n  useEffect(() => {\n    // Sync language changes with Redux store\n    const handleLanguageChange = (lng: string) => {\n      if (['en', 'af', 'st', 'tn', 'zu'].includes(lng)) {\n        dispatch(setLanguage(lng as any));\n      }\n    };\n\n    i18n.on('languageChanged', handleLanguageChange);\n    return () => {\n      i18n.off('languageChanged', handleLanguageChange);\n    };\n  }, [dispatch, i18n]);\n\n  // Show loading spinner while checking authentication\n  if (authLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <LoadingSpinner size=\"large\" />\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"App\">\n      <Routes>\n        {/* Public Routes */}\n        <Route\n          path=\"/login\"\n          element={\n            !isAuthenticated ? (\n              <AuthLayout>\n                <LoginPage />\n              </AuthLayout>\n            ) : (\n              <Navigate to=\"/dashboard\" replace />\n            )\n          }\n        />\n        <Route\n          path=\"/register\"\n          element={\n            !isAuthenticated ? (\n              <AuthLayout>\n                <RegisterPage />\n              </AuthLayout>\n            ) : (\n              <Navigate to=\"/dashboard\" replace />\n            )\n          }\n        />\n\n        {/* Protected Routes */}\n        <Route\n          path=\"/\"\n          element={\n            <ProtectedRoute>\n              <Layout />\n            </ProtectedRoute>\n          }\n        >\n          <Route index element={<Navigate to=\"/dashboard\" replace />} />\n          <Route path=\"dashboard\" element={<DashboardPage />} />\n\n          {/* Animal Management */}\n          <Route path=\"animals\" element={<AnimalsPage />} />\n          <Route path=\"animals/:id\" element={<AnimalDetailsPage />} />\n\n          {/* Health Management */}\n          <Route path=\"health\" element={<HealthPage />} />\n\n          {/* Breeding Management */}\n          <Route path=\"breeding\" element={<BreedingPage />} />\n\n          {/* Feed Management */}\n          <Route path=\"feeding\" element={<FeedingPage />} />\n\n          {/* Financial Management */}\n          <Route path=\"financial\" element={<FinancialPage />} />\n\n          {/* Reports & Analytics */}\n          <Route path=\"reports\" element={<ReportsPage />} />\n\n          {/* Settings */}\n          <Route path=\"settings\" element={<SettingsPage />} />\n        </Route>\n\n        {/* 404 Page */}\n        <Route path=\"*\" element={<NotFoundPage />} />\n      </Routes>\n\n      {/* Toast Notifications */}\n      <Toaster\n        position=\"top-right\"\n        toastOptions={{\n          duration: 4000,\n          style: {\n            background: '#363636',\n            color: '#fff',\n          },\n          success: {\n            duration: 3000,\n            iconTheme: {\n              primary: '#059669',\n              secondary: '#fff',\n            },\n          },\n          error: {\n            duration: 5000,\n            iconTheme: {\n              primary: '#dc2626',\n              secondary: '#fff',\n            },\n          },\n        }}\n      />\n    </div>\n  );\n}\n\nexport default App;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,EAAIC,SAAS,KAAQ,OAAO,CACxC,OAASC,MAAM,CAAEC,KAAK,CAAEC,QAAQ,KAAQ,kBAAkB,CAC1D,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAAgBC,OAAO,KAAQ,iBAAiB,CAGhD,OAASC,cAAc,KAAQ,0BAA0B,CACzD,OAASC,WAAW,KAAQ,wBAAwB,CAEpD;AACA,MAAO,CAAAC,MAAM,KAAM,4BAA4B,CAC/C,MAAO,CAAAC,UAAU,KAAM,gCAAgC,CAEvD;AACA,MAAO,CAAAC,SAAS,KAAM,wBAAwB,CAC9C,MAAO,CAAAC,YAAY,KAAM,2BAA2B,CACpD,MAAO,CAAAC,aAAa,KAAM,iCAAiC,CAC3D,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,iBAAiB,KAAM,mCAAmC,CACjE,MAAO,CAAAC,UAAU,KAAM,2BAA2B,CAClD,MAAO,CAAAC,YAAY,KAAM,+BAA+B,CACxD,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,aAAa,KAAM,iCAAiC,CAC3D,MAAO,CAAAC,WAAW,KAAM,6BAA6B,CACrD,MAAO,CAAAC,YAAY,KAAM,+BAA+B,CACxD,MAAO,CAAAC,YAAY,KAAM,sBAAsB,CAE/C;AACA,MAAO,CAAAC,cAAc,KAAM,oCAAoC,CAC/D,MAAO,CAAAC,cAAc,KAAM,kCAAkC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAE9D,QAAS,CAAAC,GAAGA,CAAA,CAAG,CACb,KAAM,CAAAC,QAAQ,CAAG3B,WAAW,CAAc,CAAC,CAC3C,KAAM,CAAE4B,IAAK,CAAC,CAAG1B,cAAc,CAAC,CAAC,CAEjC,KAAM,CAAE2B,eAAe,CAAEC,SAAS,CAAEC,WAAW,CAAEC,KAAM,CAAC,CAAG/B,WAAW,CACnEgC,KAAgB,EAAKA,KAAK,CAACC,IAC9B,CAAC,CACD,KAAM,CAAEC,QAAS,CAAC,CAAGlC,WAAW,CAAEgC,KAAgB,EAAKA,KAAK,CAACG,EAAE,CAAC,CAEhExC,SAAS,CAAC,IAAM,CACd;AACA,GAAIuC,QAAQ,EAAIP,IAAI,CAACO,QAAQ,GAAKA,QAAQ,CAAE,CAC1CP,IAAI,CAACS,cAAc,CAACF,QAAQ,CAAC,CAC/B,CACF,CAAC,CAAE,CAACA,QAAQ,CAAEP,IAAI,CAAC,CAAC,CAEpBhC,SAAS,CAAC,IAAM,CACd;AACA,GAAIoC,KAAK,EAAI,CAACH,eAAe,CAAE,CAC7BF,QAAQ,CAACvB,cAAc,CAAC,CAAC,CAAC,CAC5B,CACF,CAAC,CAAE,CAACuB,QAAQ,CAAEK,KAAK,CAAEH,eAAe,CAAC,CAAC,CAEtCjC,SAAS,CAAC,IAAM,CACd;AACA,KAAM,CAAA0C,oBAAoB,CAAIC,GAAW,EAAK,CAC5C,GAAI,CAAC,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAE,IAAI,CAAC,CAACC,QAAQ,CAACD,GAAG,CAAC,CAAE,CAChDZ,QAAQ,CAACtB,WAAW,CAACkC,GAAU,CAAC,CAAC,CACnC,CACF,CAAC,CAEDX,IAAI,CAACa,EAAE,CAAC,iBAAiB,CAAEH,oBAAoB,CAAC,CAChD,MAAO,IAAM,CACXV,IAAI,CAACc,GAAG,CAAC,iBAAiB,CAAEJ,oBAAoB,CAAC,CACnD,CAAC,CACH,CAAC,CAAE,CAACX,QAAQ,CAAEC,IAAI,CAAC,CAAC,CAEpB;AACA,GAAIG,WAAW,CAAE,CACf,mBACER,IAAA,QAAKoB,SAAS,CAAC,0DAA0D,CAAAC,QAAA,cACvErB,IAAA,CAACH,cAAc,EAACyB,IAAI,CAAC,OAAO,CAAE,CAAC,CAC5B,CAAC,CAEV,CAEA,mBACEpB,KAAA,QAAKkB,SAAS,CAAC,KAAK,CAAAC,QAAA,eAClBnB,KAAA,CAAC5B,MAAM,EAAA+C,QAAA,eAELrB,IAAA,CAACzB,KAAK,EACJgD,IAAI,CAAC,QAAQ,CACbC,OAAO,CACL,CAAClB,eAAe,cACdN,IAAA,CAAChB,UAAU,EAAAqC,QAAA,cACTrB,IAAA,CAACf,SAAS,GAAE,CAAC,CACH,CAAC,cAEbe,IAAA,CAACxB,QAAQ,EAACiD,EAAE,CAAC,YAAY,CAACC,OAAO,MAAE,CAEtC,CACF,CAAC,cACF1B,IAAA,CAACzB,KAAK,EACJgD,IAAI,CAAC,WAAW,CAChBC,OAAO,CACL,CAAClB,eAAe,cACdN,IAAA,CAAChB,UAAU,EAAAqC,QAAA,cACTrB,IAAA,CAACd,YAAY,GAAE,CAAC,CACN,CAAC,cAEbc,IAAA,CAACxB,QAAQ,EAACiD,EAAE,CAAC,YAAY,CAACC,OAAO,MAAE,CAEtC,CACF,CAAC,cAGFxB,KAAA,CAAC3B,KAAK,EACJgD,IAAI,CAAC,GAAG,CACRC,OAAO,cACLxB,IAAA,CAACF,cAAc,EAAAuB,QAAA,cACbrB,IAAA,CAACjB,MAAM,GAAE,CAAC,CACI,CACjB,CAAAsC,QAAA,eAEDrB,IAAA,CAACzB,KAAK,EAACoD,KAAK,MAACH,OAAO,cAAExB,IAAA,CAACxB,QAAQ,EAACiD,EAAE,CAAC,YAAY,CAACC,OAAO,MAAE,CAAE,CAAE,CAAC,cAC9D1B,IAAA,CAACzB,KAAK,EAACgD,IAAI,CAAC,WAAW,CAACC,OAAO,cAAExB,IAAA,CAACb,aAAa,GAAE,CAAE,CAAE,CAAC,cAGtDa,IAAA,CAACzB,KAAK,EAACgD,IAAI,CAAC,SAAS,CAACC,OAAO,cAAExB,IAAA,CAACZ,WAAW,GAAE,CAAE,CAAE,CAAC,cAClDY,IAAA,CAACzB,KAAK,EAACgD,IAAI,CAAC,aAAa,CAACC,OAAO,cAAExB,IAAA,CAACX,iBAAiB,GAAE,CAAE,CAAE,CAAC,cAG5DW,IAAA,CAACzB,KAAK,EAACgD,IAAI,CAAC,QAAQ,CAACC,OAAO,cAAExB,IAAA,CAACV,UAAU,GAAE,CAAE,CAAE,CAAC,cAGhDU,IAAA,CAACzB,KAAK,EAACgD,IAAI,CAAC,UAAU,CAACC,OAAO,cAAExB,IAAA,CAACT,YAAY,GAAE,CAAE,CAAE,CAAC,cAGpDS,IAAA,CAACzB,KAAK,EAACgD,IAAI,CAAC,SAAS,CAACC,OAAO,cAAExB,IAAA,CAACR,WAAW,GAAE,CAAE,CAAE,CAAC,cAGlDQ,IAAA,CAACzB,KAAK,EAACgD,IAAI,CAAC,WAAW,CAACC,OAAO,cAAExB,IAAA,CAACP,aAAa,GAAE,CAAE,CAAE,CAAC,cAGtDO,IAAA,CAACzB,KAAK,EAACgD,IAAI,CAAC,SAAS,CAACC,OAAO,cAAExB,IAAA,CAACN,WAAW,GAAE,CAAE,CAAE,CAAC,cAGlDM,IAAA,CAACzB,KAAK,EAACgD,IAAI,CAAC,UAAU,CAACC,OAAO,cAAExB,IAAA,CAACL,YAAY,GAAE,CAAE,CAAE,CAAC,EAC/C,CAAC,cAGRK,IAAA,CAACzB,KAAK,EAACgD,IAAI,CAAC,GAAG,CAACC,OAAO,cAAExB,IAAA,CAACJ,YAAY,GAAE,CAAE,CAAE,CAAC,EACvC,CAAC,cAGTI,IAAA,CAACpB,OAAO,EACNgD,QAAQ,CAAC,WAAW,CACpBC,YAAY,CAAE,CACZC,QAAQ,CAAE,IAAI,CACdC,KAAK,CAAE,CACLC,UAAU,CAAE,SAAS,CACrBC,KAAK,CAAE,MACT,CAAC,CACDC,OAAO,CAAE,CACPJ,QAAQ,CAAE,IAAI,CACdK,SAAS,CAAE,CACTC,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,MACb,CACF,CAAC,CACDC,KAAK,CAAE,CACLR,QAAQ,CAAE,IAAI,CACdK,SAAS,CAAE,CACTC,OAAO,CAAE,SAAS,CAClBC,SAAS,CAAE,MACb,CACF,CACF,CAAE,CACH,CAAC,EACC,CAAC,CAEV,CAEA,cAAe,CAAAlC,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}