{"ast": null, "code": "import Provider from './components/Provider';\nimport connect from './components/connect';\nimport { ReactReduxContext } from './components/Context';\nimport { useDispatch, createDispatchHook } from './hooks/useDispatch';\nimport { useSelector, createSelectorHook } from './hooks/useSelector';\nimport { useStore, createStoreHook } from './hooks/useStore';\nimport shallowEqual from './utils/shallowEqual';\nexport * from './types';\nexport { Provider, ReactReduxContext, connect, useDispatch, createDispatchHook, useSelector, createSelectorHook, useStore, createStoreHook, shallowEqual };", "map": {"version": 3, "names": ["Provider", "connect", "ReactReduxContext", "useDispatch", "createDispatchHook", "useSelector", "createSelectorHook", "useStore", "createStoreHook", "shallowEqual"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-redux/es/exports.js"], "sourcesContent": ["import Provider from './components/Provider';\nimport connect from './components/connect';\nimport { ReactReduxContext } from './components/Context';\nimport { useDispatch, createDispatchHook } from './hooks/useDispatch';\nimport { useSelector, createSelectorHook } from './hooks/useSelector';\nimport { useStore, createStoreHook } from './hooks/useStore';\nimport shallowEqual from './utils/shallowEqual';\nexport * from './types';\nexport { Provider, ReactReduxContext, connect, useDispatch, createDispatchHook, useSelector, createSelectorHook, useStore, createStoreHook, shallowEqual };"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,uBAAuB;AAC5C,OAAOC,OAAO,MAAM,sBAAsB;AAC1C,SAASC,iBAAiB,QAAQ,sBAAsB;AACxD,SAASC,WAAW,EAAEC,kBAAkB,QAAQ,qBAAqB;AACrE,SAASC,WAAW,EAAEC,kBAAkB,QAAQ,qBAAqB;AACrE,SAASC,QAAQ,EAAEC,eAAe,QAAQ,kBAAkB;AAC5D,OAAOC,YAAY,MAAM,sBAAsB;AAC/C,cAAc,SAAS;AACvB,SAAST,QAAQ,EAAEE,iBAAiB,EAAED,OAAO,EAAEE,WAAW,EAAEC,kBAAkB,EAAEC,WAAW,EAAEC,kBAAkB,EAAEC,QAAQ,EAAEC,eAAe,EAAEC,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}