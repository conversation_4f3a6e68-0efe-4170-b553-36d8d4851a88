{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.CryptoConnection = exports.SizedMessageTransform = exports.Connection = void 0;\nexports.hasSessionSupport = hasSessionSupport;\nconst stream_1 = require(\"stream\");\nconst timers_1 = require(\"timers\");\nconst bson_1 = require(\"../bson\");\nconst constants_1 = require(\"../constants\");\nconst error_1 = require(\"../error\");\nconst mongo_logger_1 = require(\"../mongo_logger\");\nconst mongo_types_1 = require(\"../mongo_types\");\nconst read_preference_1 = require(\"../read_preference\");\nconst common_1 = require(\"../sdam/common\");\nconst sessions_1 = require(\"../sessions\");\nconst timeout_1 = require(\"../timeout\");\nconst utils_1 = require(\"../utils\");\nconst command_monitoring_events_1 = require(\"./command_monitoring_events\");\nconst commands_1 = require(\"./commands\");\nconst stream_description_1 = require(\"./stream_description\");\nconst compression_1 = require(\"./wire_protocol/compression\");\nconst on_data_1 = require(\"./wire_protocol/on_data\");\nconst responses_1 = require(\"./wire_protocol/responses\");\nconst shared_1 = require(\"./wire_protocol/shared\");\n/** @internal */\nfunction hasSessionSupport(conn) {\n  const description = conn.description;\n  return description.logicalSessionTimeoutMinutes != null;\n}\nfunction streamIdentifier(stream, options) {\n  if (options.proxyHost) {\n    // If proxy options are specified, the properties of `stream` itself\n    // will not accurately reflect what endpoint this is connected to.\n    return options.hostAddress.toString();\n  }\n  const {\n    remoteAddress,\n    remotePort\n  } = stream;\n  if (typeof remoteAddress === 'string' && typeof remotePort === 'number') {\n    return utils_1.HostAddress.fromHostPort(remoteAddress, remotePort).toString();\n  }\n  return (0, utils_1.uuidV4)().toString('hex');\n}\n/** @internal */\nclass Connection extends mongo_types_1.TypedEventEmitter {\n  constructor(stream, options) {\n    super();\n    this.lastHelloMS = -1;\n    this.helloOk = false;\n    this.delayedTimeoutId = null;\n    /** Indicates that the connection (including underlying TCP socket) has been closed. */\n    this.closed = false;\n    this.clusterTime = null;\n    this.error = null;\n    this.dataEvents = null;\n    this.on('error', utils_1.noop);\n    this.socket = stream;\n    this.id = options.id;\n    this.address = streamIdentifier(stream, options);\n    this.socketTimeoutMS = options.socketTimeoutMS ?? 0;\n    this.monitorCommands = options.monitorCommands;\n    this.serverApi = options.serverApi;\n    this.mongoLogger = options.mongoLogger;\n    this.established = false;\n    this.description = new stream_description_1.StreamDescription(this.address, options);\n    this.generation = options.generation;\n    this.lastUseTime = (0, utils_1.now)();\n    this.messageStream = this.socket.on('error', this.onError.bind(this)).pipe(new SizedMessageTransform({\n      connection: this\n    })).on('error', this.onError.bind(this));\n    this.socket.on('close', this.onClose.bind(this));\n    this.socket.on('timeout', this.onTimeout.bind(this));\n    this.messageStream.pause();\n  }\n  get hello() {\n    return this.description.hello;\n  }\n  // the `connect` method stores the result of the handshake hello on the connection\n  set hello(response) {\n    this.description.receiveResponse(response);\n    Object.freeze(this.description);\n  }\n  get serviceId() {\n    return this.hello?.serviceId;\n  }\n  get loadBalanced() {\n    return this.description.loadBalanced;\n  }\n  get idleTime() {\n    return (0, utils_1.calculateDurationInMs)(this.lastUseTime);\n  }\n  get hasSessionSupport() {\n    return this.description.logicalSessionTimeoutMinutes != null;\n  }\n  get supportsOpMsg() {\n    return this.description != null && (0, utils_1.maxWireVersion)(this) >= 6 && !this.description.__nodejs_mock_server__;\n  }\n  get shouldEmitAndLogCommand() {\n    return (this.monitorCommands || this.established && !this.authContext?.reauthenticating && this.mongoLogger?.willLog(mongo_logger_1.MongoLoggableComponent.COMMAND, mongo_logger_1.SeverityLevel.DEBUG)) ?? false;\n  }\n  markAvailable() {\n    this.lastUseTime = (0, utils_1.now)();\n  }\n  onError(error) {\n    this.cleanup(error);\n  }\n  onClose() {\n    const message = `connection ${this.id} to ${this.address} closed`;\n    this.cleanup(new error_1.MongoNetworkError(message));\n  }\n  onTimeout() {\n    this.delayedTimeoutId = (0, timers_1.setTimeout)(() => {\n      const message = `connection ${this.id} to ${this.address} timed out`;\n      const beforeHandshake = this.hello == null;\n      this.cleanup(new error_1.MongoNetworkTimeoutError(message, {\n        beforeHandshake\n      }));\n    }, 1).unref(); // No need for this timer to hold the event loop open\n  }\n  destroy() {\n    if (this.closed) {\n      return;\n    }\n    // load balanced mode requires that these listeners remain on the connection\n    // after cleanup on timeouts, errors or close so we remove them before calling\n    // cleanup.\n    this.removeAllListeners(Connection.PINNED);\n    this.removeAllListeners(Connection.UNPINNED);\n    const message = `connection ${this.id} to ${this.address} closed`;\n    this.cleanup(new error_1.MongoNetworkError(message));\n  }\n  /**\n   * A method that cleans up the connection.  When `force` is true, this method\n   * forcibly destroys the socket.\n   *\n   * If an error is provided, any in-flight operations will be closed with the error.\n   *\n   * This method does nothing if the connection is already closed.\n   */\n  cleanup(error) {\n    if (this.closed) {\n      return;\n    }\n    this.socket.destroy();\n    this.error = error;\n    this.dataEvents?.throw(error).then(undefined, utils_1.squashError);\n    this.closed = true;\n    this.emit(Connection.CLOSE);\n  }\n  prepareCommand(db, command, options) {\n    let cmd = {\n      ...command\n    };\n    const readPreference = (0, shared_1.getReadPreference)(options);\n    const session = options?.session;\n    let clusterTime = this.clusterTime;\n    if (this.serverApi) {\n      const {\n        version,\n        strict,\n        deprecationErrors\n      } = this.serverApi;\n      cmd.apiVersion = version;\n      if (strict != null) cmd.apiStrict = strict;\n      if (deprecationErrors != null) cmd.apiDeprecationErrors = deprecationErrors;\n    }\n    if (this.hasSessionSupport && session) {\n      if (session.clusterTime && clusterTime && session.clusterTime.clusterTime.greaterThan(clusterTime.clusterTime)) {\n        clusterTime = session.clusterTime;\n      }\n      const sessionError = (0, sessions_1.applySession)(session, cmd, options);\n      if (sessionError) throw sessionError;\n    } else if (session?.explicit) {\n      throw new error_1.MongoCompatibilityError('Current topology does not support sessions');\n    }\n    // if we have a known cluster time, gossip it\n    if (clusterTime) {\n      cmd.$clusterTime = clusterTime;\n    }\n    // For standalone, drivers MUST NOT set $readPreference.\n    if (this.description.type !== common_1.ServerType.Standalone) {\n      if (!(0, shared_1.isSharded)(this) && !this.description.loadBalanced && this.supportsOpMsg && options.directConnection === true && readPreference?.mode === 'primary') {\n        // For mongos and load balancers with 'primary' mode, drivers MUST NOT set $readPreference.\n        // For all other types with a direct connection, if the read preference is 'primary'\n        // (driver sets 'primary' as default if no read preference is configured),\n        // the $readPreference MUST be set to 'primaryPreferred'\n        // to ensure that any server type can handle the request.\n        cmd.$readPreference = read_preference_1.ReadPreference.primaryPreferred.toJSON();\n      } else if ((0, shared_1.isSharded)(this) && !this.supportsOpMsg && readPreference?.mode !== 'primary') {\n        // When sending a read operation via OP_QUERY and the $readPreference modifier,\n        // the query MUST be provided using the $query modifier.\n        cmd = {\n          $query: cmd,\n          $readPreference: readPreference.toJSON()\n        };\n      } else if (readPreference?.mode !== 'primary') {\n        // For mode 'primary', drivers MUST NOT set $readPreference.\n        // For all other read preference modes (i.e. 'secondary', 'primaryPreferred', ...),\n        // drivers MUST set $readPreference\n        cmd.$readPreference = readPreference.toJSON();\n      }\n    }\n    const commandOptions = {\n      numberToSkip: 0,\n      numberToReturn: -1,\n      checkKeys: false,\n      // This value is not overridable\n      secondaryOk: readPreference.secondaryOk(),\n      ...options\n    };\n    options.timeoutContext?.addMaxTimeMSToCommand(cmd, options);\n    const message = this.supportsOpMsg ? new commands_1.OpMsgRequest(db, cmd, commandOptions) : new commands_1.OpQueryRequest(db, cmd, commandOptions);\n    return message;\n  }\n  async *sendWire(message, options, responseType) {\n    this.throwIfAborted();\n    const timeout = options.socketTimeoutMS ?? options?.timeoutContext?.getSocketTimeoutMS() ?? this.socketTimeoutMS;\n    this.socket.setTimeout(timeout);\n    try {\n      await this.writeCommand(message, {\n        agreedCompressor: this.description.compressor ?? 'none',\n        zlibCompressionLevel: this.description.zlibCompressionLevel,\n        timeoutContext: options.timeoutContext,\n        signal: options.signal\n      });\n      if (options.noResponse || message.moreToCome) {\n        yield responses_1.MongoDBResponse.empty;\n        return;\n      }\n      this.throwIfAborted();\n      if (options.timeoutContext?.csotEnabled() && options.timeoutContext.minRoundTripTime != null && options.timeoutContext.remainingTimeMS < options.timeoutContext.minRoundTripTime) {\n        throw new error_1.MongoOperationTimeoutError('Server roundtrip time is greater than the time remaining');\n      }\n      for await (const response of this.readMany(options)) {\n        this.socket.setTimeout(0);\n        const bson = response.parse();\n        const document = (responseType ?? responses_1.MongoDBResponse).make(bson);\n        yield document;\n        this.throwIfAborted();\n        this.socket.setTimeout(timeout);\n      }\n    } finally {\n      this.socket.setTimeout(0);\n    }\n  }\n  async *sendCommand(ns, command, options, responseType) {\n    options?.signal?.throwIfAborted();\n    const message = this.prepareCommand(ns.db, command, options);\n    let started = 0;\n    if (this.shouldEmitAndLogCommand) {\n      started = (0, utils_1.now)();\n      this.emitAndLogCommand(this.monitorCommands, Connection.COMMAND_STARTED, message.databaseName, this.established, new command_monitoring_events_1.CommandStartedEvent(this, message, this.description.serverConnectionId));\n    }\n    // If `documentsReturnedIn` not set or raw is not enabled, use input bson options\n    // Otherwise, support raw flag. Raw only works for cursors that hardcode firstBatch/nextBatch fields\n    const bsonOptions = options.documentsReturnedIn == null || !options.raw ? options : {\n      ...options,\n      raw: false,\n      fieldsAsRaw: {\n        [options.documentsReturnedIn]: true\n      }\n    };\n    /** MongoDBResponse instance or subclass */\n    let document = undefined;\n    /** Cached result of a toObject call */\n    let object = undefined;\n    try {\n      this.throwIfAborted();\n      for await (document of this.sendWire(message, options, responseType)) {\n        object = undefined;\n        if (options.session != null) {\n          (0, sessions_1.updateSessionFromResponse)(options.session, document);\n        }\n        if (document.$clusterTime) {\n          this.clusterTime = document.$clusterTime;\n          this.emit(Connection.CLUSTER_TIME_RECEIVED, document.$clusterTime);\n        }\n        if (document.ok === 0) {\n          if (options.timeoutContext?.csotEnabled() && document.isMaxTimeExpiredError) {\n            throw new error_1.MongoOperationTimeoutError('Server reported a timeout error', {\n              cause: new error_1.MongoServerError(object ??= document.toObject(bsonOptions))\n            });\n          }\n          throw new error_1.MongoServerError(object ??= document.toObject(bsonOptions));\n        }\n        if (this.shouldEmitAndLogCommand) {\n          this.emitAndLogCommand(this.monitorCommands, Connection.COMMAND_SUCCEEDED, message.databaseName, this.established, new command_monitoring_events_1.CommandSucceededEvent(this, message, options.noResponse ? undefined : message.moreToCome ? {\n            ok: 1\n          } : object ??= document.toObject(bsonOptions), started, this.description.serverConnectionId));\n        }\n        if (responseType == null) {\n          yield object ??= document.toObject(bsonOptions);\n        } else {\n          yield document;\n        }\n        this.throwIfAborted();\n      }\n    } catch (error) {\n      if (this.shouldEmitAndLogCommand) {\n        this.emitAndLogCommand(this.monitorCommands, Connection.COMMAND_FAILED, message.databaseName, this.established, new command_monitoring_events_1.CommandFailedEvent(this, message, error, started, this.description.serverConnectionId));\n      }\n      throw error;\n    }\n  }\n  async command(ns, command, options = {}, responseType) {\n    this.throwIfAborted();\n    options.signal?.throwIfAborted();\n    for await (const document of this.sendCommand(ns, command, options, responseType)) {\n      if (options.timeoutContext?.csotEnabled()) {\n        if (responses_1.MongoDBResponse.is(document)) {\n          if (document.isMaxTimeExpiredError) {\n            throw new error_1.MongoOperationTimeoutError('Server reported a timeout error', {\n              cause: new error_1.MongoServerError(document.toObject())\n            });\n          }\n        } else {\n          if (Array.isArray(document?.writeErrors) && document.writeErrors.some(error => error?.code === error_1.MONGODB_ERROR_CODES.MaxTimeMSExpired) || document?.writeConcernError?.code === error_1.MONGODB_ERROR_CODES.MaxTimeMSExpired) {\n            throw new error_1.MongoOperationTimeoutError('Server reported a timeout error', {\n              cause: new error_1.MongoServerError(document)\n            });\n          }\n        }\n      }\n      return document;\n    }\n    throw new error_1.MongoUnexpectedServerResponseError('Unable to get response from server');\n  }\n  exhaustCommand(ns, command, options, replyListener) {\n    const exhaustLoop = async () => {\n      this.throwIfAborted();\n      for await (const reply of this.sendCommand(ns, command, options)) {\n        replyListener(undefined, reply);\n        this.throwIfAborted();\n      }\n      throw new error_1.MongoUnexpectedServerResponseError('Server ended moreToCome unexpectedly');\n    };\n    exhaustLoop().then(undefined, replyListener);\n  }\n  throwIfAborted() {\n    if (this.error) throw this.error;\n  }\n  /**\n   * @internal\n   *\n   * Writes an OP_MSG or OP_QUERY request to the socket, optionally compressing the command. This method\n   * waits until the socket's buffer has emptied (the Nodejs socket `drain` event has fired).\n   */\n  async writeCommand(command, options) {\n    const finalCommand = options.agreedCompressor === 'none' || !commands_1.OpCompressedRequest.canCompress(command) ? command : new commands_1.OpCompressedRequest(command, {\n      agreedCompressor: options.agreedCompressor ?? 'none',\n      zlibCompressionLevel: options.zlibCompressionLevel ?? 0\n    });\n    const buffer = Buffer.concat(await finalCommand.toBin());\n    if (options.timeoutContext?.csotEnabled()) {\n      if (options.timeoutContext.minRoundTripTime != null && options.timeoutContext.remainingTimeMS < options.timeoutContext.minRoundTripTime) {\n        throw new error_1.MongoOperationTimeoutError('Server roundtrip time is greater than the time remaining');\n      }\n    }\n    if (this.socket.write(buffer)) return;\n    const drainEvent = (0, utils_1.once)(this.socket, 'drain', options);\n    const timeout = options?.timeoutContext?.timeoutForSocketWrite;\n    const drained = timeout ? Promise.race([drainEvent, timeout]) : drainEvent;\n    try {\n      return await drained;\n    } catch (writeError) {\n      if (timeout_1.TimeoutError.is(writeError)) {\n        const timeoutError = new error_1.MongoOperationTimeoutError('Timed out at socket write');\n        this.onError(timeoutError);\n        throw timeoutError;\n      } else if (writeError === options.signal?.reason) {\n        this.onError(writeError);\n      }\n      throw writeError;\n    } finally {\n      timeout?.clear();\n    }\n  }\n  /**\n   * @internal\n   *\n   * Returns an async generator that yields full wire protocol messages from the underlying socket.  This function\n   * yields messages until `moreToCome` is false or not present in a response, or the caller cancels the request\n   * by calling `return` on the generator.\n   *\n   * Note that `for-await` loops call `return` automatically when the loop is exited.\n   */\n  async *readMany(options) {\n    try {\n      this.dataEvents = (0, on_data_1.onData)(this.messageStream, options);\n      this.messageStream.resume();\n      for await (const message of this.dataEvents) {\n        const response = await (0, compression_1.decompressResponse)(message);\n        yield response;\n        if (!response.moreToCome) {\n          return;\n        }\n      }\n    } catch (readError) {\n      if (timeout_1.TimeoutError.is(readError)) {\n        const timeoutError = new error_1.MongoOperationTimeoutError(`Timed out during socket read (${readError.duration}ms)`);\n        this.dataEvents = null;\n        this.onError(timeoutError);\n        throw timeoutError;\n      } else if (readError === options.signal?.reason) {\n        this.onError(readError);\n      }\n      throw readError;\n    } finally {\n      this.dataEvents = null;\n      this.messageStream.pause();\n      this.throwIfAborted();\n    }\n  }\n}\nexports.Connection = Connection;\n/** @event */\nConnection.COMMAND_STARTED = constants_1.COMMAND_STARTED;\n/** @event */\nConnection.COMMAND_SUCCEEDED = constants_1.COMMAND_SUCCEEDED;\n/** @event */\nConnection.COMMAND_FAILED = constants_1.COMMAND_FAILED;\n/** @event */\nConnection.CLUSTER_TIME_RECEIVED = constants_1.CLUSTER_TIME_RECEIVED;\n/** @event */\nConnection.CLOSE = constants_1.CLOSE;\n/** @event */\nConnection.PINNED = constants_1.PINNED;\n/** @event */\nConnection.UNPINNED = constants_1.UNPINNED;\n/** @internal */\nclass SizedMessageTransform extends stream_1.Transform {\n  constructor({\n    connection\n  }) {\n    super({\n      writableObjectMode: false,\n      readableObjectMode: true\n    });\n    this.bufferPool = new utils_1.BufferPool();\n    this.connection = connection;\n  }\n  _transform(chunk, encoding, callback) {\n    if (this.connection.delayedTimeoutId != null) {\n      (0, timers_1.clearTimeout)(this.connection.delayedTimeoutId);\n      this.connection.delayedTimeoutId = null;\n    }\n    this.bufferPool.append(chunk);\n    while (this.bufferPool.length) {\n      // While there are any bytes in the buffer\n      // Try to fetch a size from the top 4 bytes\n      const sizeOfMessage = this.bufferPool.getInt32();\n      if (sizeOfMessage == null) {\n        // Not even an int32 worth of data. Stop the loop, we need more chunks.\n        break;\n      }\n      if (sizeOfMessage < 0) {\n        // The size in the message has a negative value, this is probably corruption, throw:\n        return callback(new error_1.MongoParseError(`Message size cannot be negative: ${sizeOfMessage}`));\n      }\n      if (sizeOfMessage > this.bufferPool.length) {\n        // We do not have enough bytes to make a sizeOfMessage chunk\n        break;\n      }\n      // Add a message to the stream\n      const message = this.bufferPool.read(sizeOfMessage);\n      if (!this.push(message)) {\n        // We only subscribe to data events so we should never get backpressure\n        // if we do, we do not have the handling for it.\n        return callback(new error_1.MongoRuntimeError(`SizedMessageTransform does not support backpressure`));\n      }\n    }\n    callback();\n  }\n}\nexports.SizedMessageTransform = SizedMessageTransform;\n/** @internal */\nclass CryptoConnection extends Connection {\n  constructor(stream, options) {\n    super(stream, options);\n    this.autoEncrypter = options.autoEncrypter;\n  }\n  async command(ns, cmd, options, responseType) {\n    const {\n      autoEncrypter\n    } = this;\n    if (!autoEncrypter) {\n      // TODO(NODE-6065): throw a MongoRuntimeError in Node V7\n      // @ts-expect-error No cause provided because there is no underlying error.\n      throw new error_1.MongoMissingDependencyError('No AutoEncrypter available for encryption', {\n        dependencyName: 'n/a'\n      });\n    }\n    const serverWireVersion = (0, utils_1.maxWireVersion)(this);\n    if (serverWireVersion === 0) {\n      // This means the initial handshake hasn't happened yet\n      return await super.command(ns, cmd, options, responseType);\n    }\n    if (serverWireVersion < 8) {\n      throw new error_1.MongoCompatibilityError('Auto-encryption requires a minimum MongoDB version of 4.2');\n    }\n    // Save sort or indexKeys based on the command being run\n    // the encrypt API serializes our JS objects to BSON to pass to the native code layer\n    // and then deserializes the encrypted result, the protocol level components\n    // of the command (ex. sort) are then converted to JS objects potentially losing\n    // import key order information. These fields are never encrypted so we can save the values\n    // from before the encryption and replace them after encryption has been performed\n    const sort = cmd.find || cmd.findAndModify ? cmd.sort : null;\n    const indexKeys = cmd.createIndexes ? cmd.indexes.map(index => index.key) : null;\n    const encrypted = await autoEncrypter.encrypt(ns.toString(), cmd, options);\n    // Replace the saved values\n    if (sort != null && (cmd.find || cmd.findAndModify)) {\n      encrypted.sort = sort;\n    }\n    if (indexKeys != null && cmd.createIndexes) {\n      for (const [offset, index] of indexKeys.entries()) {\n        // @ts-expect-error `encrypted` is a generic \"command\", but we've narrowed for only `createIndexes` commands here\n        encrypted.indexes[offset].key = index;\n      }\n    }\n    const encryptedResponse = await super.command(ns, encrypted, options,\n    // Eventually we want to require `responseType` which means we would satisfy `T` as the return type.\n    // In the meantime, we want encryptedResponse to always be _at least_ a MongoDBResponse if not a more specific subclass\n    // So that we can ensure we have access to the on-demand APIs for decorate response\n    responseType ?? responses_1.MongoDBResponse);\n    const result = await autoEncrypter.decrypt(encryptedResponse.toBytes(), options);\n    const decryptedResponse = responseType?.make(result) ?? (0, bson_1.deserialize)(result, options);\n    if (autoEncrypter[constants_1.kDecorateResult]) {\n      if (responseType == null) {\n        (0, utils_1.decorateDecryptionResult)(decryptedResponse, encryptedResponse.toObject(), true);\n      } else if (decryptedResponse instanceof responses_1.CursorResponse) {\n        decryptedResponse.encryptedResponse = encryptedResponse;\n      }\n    }\n    return decryptedResponse;\n  }\n}\nexports.CryptoConnection = CryptoConnection;", "map": {"version": 3, "names": ["exports", "hasSessionSupport", "stream_1", "require", "timers_1", "bson_1", "constants_1", "error_1", "mongo_logger_1", "mongo_types_1", "read_preference_1", "common_1", "sessions_1", "timeout_1", "utils_1", "command_monitoring_events_1", "commands_1", "stream_description_1", "compression_1", "on_data_1", "responses_1", "shared_1", "conn", "description", "logicalSessionTimeoutMinutes", "streamIdentifier", "stream", "options", "proxyHost", "host<PERSON><PERSON><PERSON>", "toString", "remoteAddress", "remotePort", "HostAddress", "fromHostPort", "uuidV4", "Connection", "TypedEventEmitter", "constructor", "lastHelloMS", "helloOk", "delayedTimeoutId", "closed", "clusterTime", "error", "dataEvents", "on", "noop", "socket", "id", "address", "socketTimeoutMS", "monitorCommands", "serverApi", "mongoLogger", "established", "StreamDescription", "generation", "lastUseTime", "now", "messageStream", "onError", "bind", "pipe", "SizedMessageTransform", "connection", "onClose", "onTimeout", "pause", "hello", "response", "receiveResponse", "Object", "freeze", "serviceId", "loadBalanced", "idleTime", "calculateDurationInMs", "supportsOpMsg", "maxWireVersion", "__nodejs_mock_server__", "shouldEmitAndLogCommand", "authContext", "reauthenticating", "<PERSON><PERSON><PERSON>", "MongoLoggableComponent", "COMMAND", "SeverityLevel", "DEBUG", "markAvailable", "cleanup", "message", "MongoNetworkError", "setTimeout", "beforeHandshake", "MongoNetworkTimeoutError", "unref", "destroy", "removeAllListeners", "PINNED", "UNPINNED", "throw", "then", "undefined", "squashError", "emit", "CLOSE", "prepareCommand", "db", "command", "cmd", "readPreference", "getReadPreference", "session", "version", "strict", "deprecationErrors", "apiVersion", "apiStrict", "apiDeprecationErrors", "greaterThan", "sessionError", "applySession", "explicit", "MongoCompatibilityError", "$clusterTime", "type", "ServerType", "Standalone", "isSharded", "directConnection", "mode", "$readPreference", "ReadPreference", "primaryPreferred", "toJSON", "$query", "commandOptions", "numberToSkip", "numberToReturn", "checkKeys", "secondaryOk", "timeoutContext", "addMaxTimeMSToCommand", "OpMsgRequest", "OpQueryRequest", "sendWire", "responseType", "throwIfAborted", "timeout", "getSocketTimeoutMS", "writeCommand", "agreedCompressor", "compressor", "zlibCompressionLevel", "signal", "noResponse", "moreToCome", "MongoDBResponse", "empty", "csotEnabled", "minRoundTripTime", "remainingTimeMS", "MongoOperationTimeoutError", "readMany", "bson", "parse", "document", "make", "sendCommand", "ns", "started", "emitAndLogCommand", "COMMAND_STARTED", "databaseName", "CommandStartedEvent", "serverConnectionId", "bsonOptions", "documentsReturnedIn", "raw", "fieldsAsRaw", "object", "updateSessionFromResponse", "CLUSTER_TIME_RECEIVED", "ok", "isMaxTimeExpiredError", "cause", "MongoServerError", "toObject", "COMMAND_SUCCEEDED", "CommandSucceededEvent", "COMMAND_FAILED", "CommandFailedEvent", "is", "Array", "isArray", "writeErrors", "some", "code", "MONGODB_ERROR_CODES", "MaxTimeMSExpired", "writeConcernError", "MongoUnexpectedServerResponseError", "exhaustCommand", "replyListener", "exhaustLoop", "reply", "finalCommand", "OpCompressedRequest", "canCompress", "buffer", "<PERSON><PERSON><PERSON>", "concat", "to<PERSON>in", "write", "drainEvent", "once", "timeoutForSocketWrite", "drained", "Promise", "race", "writeError", "TimeoutError", "timeoutError", "reason", "clear", "onData", "resume", "decompressResponse", "readError", "duration", "Transform", "writableObjectMode", "readableObjectMode", "bufferPool", "BufferPool", "_transform", "chunk", "encoding", "callback", "clearTimeout", "append", "length", "sizeOfMessage", "getInt32", "MongoParseError", "read", "push", "MongoRuntimeError", "CryptoConnection", "autoEncrypter", "MongoMissingDependencyError", "dependencyName", "serverWireVersion", "sort", "find", "findAndModify", "indexKeys", "createIndexes", "indexes", "map", "index", "key", "encrypted", "encrypt", "offset", "entries", "encryptedResponse", "result", "decrypt", "toBytes", "decryptedResponse", "deserialize", "kDecorateResult", "decorateDecryptionResult", "CursorResponse"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\connection.ts"], "sourcesContent": ["import { type Readable, Transform, type TransformCallback } from 'stream';\nimport { clearTimeout, setTimeout } from 'timers';\n\nimport {\n  type BSONSerializeOptions,\n  deserialize,\n  type DeserializeOptions,\n  type Document,\n  type ObjectId\n} from '../bson';\nimport { type AutoEncrypter } from '../client-side-encryption/auto_encrypter';\nimport {\n  CLOSE,\n  CLUSTER_TIME_RECEIVED,\n  COMMAND_FAILED,\n  COMMAND_STARTED,\n  COMMAND_SUCCEEDED,\n  kDecorateResult,\n  PINNED,\n  UNPINNED\n} from '../constants';\nimport {\n  MongoCompatibilityError,\n  MONGODB_ERROR_CODES,\n  MongoMissingDependencyError,\n  MongoNetworkError,\n  MongoNetworkTimeoutError,\n  MongoOperationTimeoutError,\n  MongoParseError,\n  MongoRuntimeError,\n  MongoServerError,\n  MongoUnexpectedServerResponseError\n} from '../error';\nimport type { <PERSON><PERSON><PERSON>, SupportedNodeConnectionOptions } from '../mongo_client';\nimport { type MongoClientAuthProviders } from '../mongo_client_auth_providers';\nimport { MongoLoggableComponent, type MongoLogger, SeverityLevel } from '../mongo_logger';\nimport { type Abortable, type CancellationToken, TypedEventEmitter } from '../mongo_types';\nimport { ReadPreference, type ReadPreferenceLike } from '../read_preference';\nimport { ServerType } from '../sdam/common';\nimport { applySession, type ClientSession, updateSessionFromResponse } from '../sessions';\nimport { type TimeoutContext, TimeoutError } from '../timeout';\nimport {\n  BufferPool,\n  calculateDurationInMs,\n  type Callback,\n  decorateDecryptionResult,\n  HostAddress,\n  maxWireVersion,\n  type MongoDBNamespace,\n  noop,\n  now,\n  once,\n  squashError,\n  uuidV4\n} from '../utils';\nimport type { WriteConcern } from '../write_concern';\nimport type { AuthContext } from './auth/auth_provider';\nimport type { MongoCredentials } from './auth/mongo_credentials';\nimport {\n  CommandFailedEvent,\n  CommandStartedEvent,\n  CommandSucceededEvent\n} from './command_monitoring_events';\nimport {\n  OpCompressedRequest,\n  OpMsgRequest,\n  type OpMsgResponse,\n  OpQueryRequest,\n  type OpReply,\n  type WriteProtocolMessageType\n} from './commands';\nimport type { Stream } from './connect';\nimport type { ClientMetadata } from './handshake/client_metadata';\nimport { StreamDescription, type StreamDescriptionOptions } from './stream_description';\nimport { type CompressorName, decompressResponse } from './wire_protocol/compression';\nimport { onData } from './wire_protocol/on_data';\nimport {\n  CursorResponse,\n  MongoDBResponse,\n  type MongoDBResponseConstructor\n} from './wire_protocol/responses';\nimport { getReadPreference, isSharded } from './wire_protocol/shared';\n\n/** @internal */\nexport interface CommandOptions extends BSONSerializeOptions {\n  secondaryOk?: boolean;\n  /** Specify read preference if command supports it */\n  readPreference?: ReadPreferenceLike;\n  monitoring?: boolean;\n  socketTimeoutMS?: number;\n  /** Session to use for the operation */\n  session?: ClientSession;\n  documentsReturnedIn?: string;\n  noResponse?: boolean;\n  omitReadPreference?: boolean;\n  omitMaxTimeMS?: boolean;\n\n  // TODO(NODE-2802): Currently the CommandOptions take a property willRetryWrite which is a hint\n  // from executeOperation that the txnNum should be applied to this command.\n  // Applying a session to a command should happen as part of command construction,\n  // most likely in the CommandOperation#executeCommand method, where we have access to\n  // the details we need to determine if a txnNum should also be applied.\n  willRetryWrite?: boolean;\n\n  writeConcern?: WriteConcern;\n\n  directConnection?: boolean;\n\n  /** @internal */\n  timeoutContext?: TimeoutContext;\n}\n\n/** @public */\nexport interface ProxyOptions {\n  proxyHost?: string;\n  proxyPort?: number;\n  proxyUsername?: string;\n  proxyPassword?: string;\n}\n\n/** @public */\nexport interface ConnectionOptions\n  extends SupportedNodeConnectionOptions,\n    StreamDescriptionOptions,\n    ProxyOptions {\n  // Internal creation info\n  id: number | '<monitor>';\n  generation: number;\n  hostAddress: HostAddress;\n  /** @internal */\n  autoEncrypter?: AutoEncrypter;\n  serverApi?: ServerApi;\n  monitorCommands: boolean;\n  /** @internal */\n  connectionType?: any;\n  credentials?: MongoCredentials;\n  /** @internal */\n  authProviders: MongoClientAuthProviders;\n  connectTimeoutMS?: number;\n  tls: boolean;\n  noDelay?: boolean;\n  socketTimeoutMS?: number;\n  cancellationToken?: CancellationToken;\n  metadata: ClientMetadata;\n  /** @internal */\n  extendedMetadata: Promise<Document>;\n  /** @internal */\n  mongoLogger?: MongoLogger | undefined;\n}\n\n/** @public */\nexport type ConnectionEvents = {\n  commandStarted(event: CommandStartedEvent): void;\n  commandSucceeded(event: CommandSucceededEvent): void;\n  commandFailed(event: CommandFailedEvent): void;\n  clusterTimeReceived(clusterTime: Document): void;\n  close(): void;\n  pinned(pinType: string): void;\n  unpinned(pinType: string): void;\n};\n\n/** @internal */\nexport function hasSessionSupport(conn: Connection): boolean {\n  const description = conn.description;\n  return description.logicalSessionTimeoutMinutes != null;\n}\n\nfunction streamIdentifier(stream: Stream, options: ConnectionOptions): string {\n  if (options.proxyHost) {\n    // If proxy options are specified, the properties of `stream` itself\n    // will not accurately reflect what endpoint this is connected to.\n    return options.hostAddress.toString();\n  }\n\n  const { remoteAddress, remotePort } = stream;\n  if (typeof remoteAddress === 'string' && typeof remotePort === 'number') {\n    return HostAddress.fromHostPort(remoteAddress, remotePort).toString();\n  }\n\n  return uuidV4().toString('hex');\n}\n\n/** @internal */\nexport class Connection extends TypedEventEmitter<ConnectionEvents> {\n  public id: number | '<monitor>';\n  public address: string;\n  public lastHelloMS = -1;\n  public serverApi?: ServerApi;\n  public helloOk = false;\n  public authContext?: AuthContext;\n  public delayedTimeoutId: NodeJS.Timeout | null = null;\n  public generation: number;\n  public accessToken?: string;\n  public readonly description: Readonly<StreamDescription>;\n  /**\n   * Represents if the connection has been established:\n   *  - TCP handshake\n   *  - TLS negotiated\n   *  - mongodb handshake (saslStart, saslContinue), includes authentication\n   *\n   * Once connection is established, command logging can log events (if enabled)\n   */\n  public established: boolean;\n  /** Indicates that the connection (including underlying TCP socket) has been closed. */\n  public closed = false;\n\n  private lastUseTime: number;\n  private clusterTime: Document | null = null;\n  private error: Error | null = null;\n  private dataEvents: AsyncGenerator<Buffer, void, void> | null = null;\n\n  private readonly socketTimeoutMS: number;\n  private readonly monitorCommands: boolean;\n  private readonly socket: Stream;\n  private readonly messageStream: Readable;\n\n  /** @event */\n  static readonly COMMAND_STARTED = COMMAND_STARTED;\n  /** @event */\n  static readonly COMMAND_SUCCEEDED = COMMAND_SUCCEEDED;\n  /** @event */\n  static readonly COMMAND_FAILED = COMMAND_FAILED;\n  /** @event */\n  static readonly CLUSTER_TIME_RECEIVED = CLUSTER_TIME_RECEIVED;\n  /** @event */\n  static readonly CLOSE = CLOSE;\n  /** @event */\n  static readonly PINNED = PINNED;\n  /** @event */\n  static readonly UNPINNED = UNPINNED;\n\n  constructor(stream: Stream, options: ConnectionOptions) {\n    super();\n    this.on('error', noop);\n\n    this.socket = stream;\n    this.id = options.id;\n    this.address = streamIdentifier(stream, options);\n    this.socketTimeoutMS = options.socketTimeoutMS ?? 0;\n    this.monitorCommands = options.monitorCommands;\n    this.serverApi = options.serverApi;\n    this.mongoLogger = options.mongoLogger;\n    this.established = false;\n\n    this.description = new StreamDescription(this.address, options);\n    this.generation = options.generation;\n    this.lastUseTime = now();\n\n    this.messageStream = this.socket\n      .on('error', this.onError.bind(this))\n      .pipe(new SizedMessageTransform({ connection: this }))\n      .on('error', this.onError.bind(this));\n    this.socket.on('close', this.onClose.bind(this));\n    this.socket.on('timeout', this.onTimeout.bind(this));\n\n    this.messageStream.pause();\n  }\n\n  public get hello() {\n    return this.description.hello;\n  }\n\n  // the `connect` method stores the result of the handshake hello on the connection\n  public set hello(response: Document | null) {\n    this.description.receiveResponse(response);\n    Object.freeze(this.description);\n  }\n\n  public get serviceId(): ObjectId | undefined {\n    return this.hello?.serviceId;\n  }\n\n  public get loadBalanced(): boolean {\n    return this.description.loadBalanced;\n  }\n\n  public get idleTime(): number {\n    return calculateDurationInMs(this.lastUseTime);\n  }\n\n  private get hasSessionSupport(): boolean {\n    return this.description.logicalSessionTimeoutMinutes != null;\n  }\n\n  private get supportsOpMsg(): boolean {\n    return (\n      this.description != null &&\n      maxWireVersion(this) >= 6 &&\n      !this.description.__nodejs_mock_server__\n    );\n  }\n\n  private get shouldEmitAndLogCommand(): boolean {\n    return (\n      (this.monitorCommands ||\n        (this.established &&\n          !this.authContext?.reauthenticating &&\n          this.mongoLogger?.willLog(MongoLoggableComponent.COMMAND, SeverityLevel.DEBUG))) ??\n      false\n    );\n  }\n\n  public markAvailable(): void {\n    this.lastUseTime = now();\n  }\n\n  public onError(error: Error) {\n    this.cleanup(error);\n  }\n\n  private onClose() {\n    const message = `connection ${this.id} to ${this.address} closed`;\n    this.cleanup(new MongoNetworkError(message));\n  }\n\n  private onTimeout() {\n    this.delayedTimeoutId = setTimeout(() => {\n      const message = `connection ${this.id} to ${this.address} timed out`;\n      const beforeHandshake = this.hello == null;\n      this.cleanup(new MongoNetworkTimeoutError(message, { beforeHandshake }));\n    }, 1).unref(); // No need for this timer to hold the event loop open\n  }\n\n  public destroy(): void {\n    if (this.closed) {\n      return;\n    }\n\n    // load balanced mode requires that these listeners remain on the connection\n    // after cleanup on timeouts, errors or close so we remove them before calling\n    // cleanup.\n    this.removeAllListeners(Connection.PINNED);\n    this.removeAllListeners(Connection.UNPINNED);\n    const message = `connection ${this.id} to ${this.address} closed`;\n    this.cleanup(new MongoNetworkError(message));\n  }\n\n  /**\n   * A method that cleans up the connection.  When `force` is true, this method\n   * forcibly destroys the socket.\n   *\n   * If an error is provided, any in-flight operations will be closed with the error.\n   *\n   * This method does nothing if the connection is already closed.\n   */\n  private cleanup(error: Error): void {\n    if (this.closed) {\n      return;\n    }\n\n    this.socket.destroy();\n    this.error = error;\n\n    this.dataEvents?.throw(error).then(undefined, squashError);\n    this.closed = true;\n    this.emit(Connection.CLOSE);\n  }\n\n  private prepareCommand(db: string, command: Document, options: CommandOptions) {\n    let cmd = { ...command };\n\n    const readPreference = getReadPreference(options);\n    const session = options?.session;\n\n    let clusterTime = this.clusterTime;\n\n    if (this.serverApi) {\n      const { version, strict, deprecationErrors } = this.serverApi;\n      cmd.apiVersion = version;\n      if (strict != null) cmd.apiStrict = strict;\n      if (deprecationErrors != null) cmd.apiDeprecationErrors = deprecationErrors;\n    }\n\n    if (this.hasSessionSupport && session) {\n      if (\n        session.clusterTime &&\n        clusterTime &&\n        session.clusterTime.clusterTime.greaterThan(clusterTime.clusterTime)\n      ) {\n        clusterTime = session.clusterTime;\n      }\n\n      const sessionError = applySession(session, cmd, options);\n      if (sessionError) throw sessionError;\n    } else if (session?.explicit) {\n      throw new MongoCompatibilityError('Current topology does not support sessions');\n    }\n\n    // if we have a known cluster time, gossip it\n    if (clusterTime) {\n      cmd.$clusterTime = clusterTime;\n    }\n\n    // For standalone, drivers MUST NOT set $readPreference.\n    if (this.description.type !== ServerType.Standalone) {\n      if (\n        !isSharded(this) &&\n        !this.description.loadBalanced &&\n        this.supportsOpMsg &&\n        options.directConnection === true &&\n        readPreference?.mode === 'primary'\n      ) {\n        // For mongos and load balancers with 'primary' mode, drivers MUST NOT set $readPreference.\n        // For all other types with a direct connection, if the read preference is 'primary'\n        // (driver sets 'primary' as default if no read preference is configured),\n        // the $readPreference MUST be set to 'primaryPreferred'\n        // to ensure that any server type can handle the request.\n        cmd.$readPreference = ReadPreference.primaryPreferred.toJSON();\n      } else if (isSharded(this) && !this.supportsOpMsg && readPreference?.mode !== 'primary') {\n        // When sending a read operation via OP_QUERY and the $readPreference modifier,\n        // the query MUST be provided using the $query modifier.\n        cmd = {\n          $query: cmd,\n          $readPreference: readPreference.toJSON()\n        };\n      } else if (readPreference?.mode !== 'primary') {\n        // For mode 'primary', drivers MUST NOT set $readPreference.\n        // For all other read preference modes (i.e. 'secondary', 'primaryPreferred', ...),\n        // drivers MUST set $readPreference\n        cmd.$readPreference = readPreference.toJSON();\n      }\n    }\n\n    const commandOptions = {\n      numberToSkip: 0,\n      numberToReturn: -1,\n      checkKeys: false,\n      // This value is not overridable\n      secondaryOk: readPreference.secondaryOk(),\n      ...options\n    };\n\n    options.timeoutContext?.addMaxTimeMSToCommand(cmd, options);\n\n    const message = this.supportsOpMsg\n      ? new OpMsgRequest(db, cmd, commandOptions)\n      : new OpQueryRequest(db, cmd, commandOptions);\n\n    return message;\n  }\n\n  private async *sendWire(\n    message: WriteProtocolMessageType,\n    options: CommandOptions & Abortable,\n    responseType?: MongoDBResponseConstructor\n  ): AsyncGenerator<MongoDBResponse> {\n    this.throwIfAborted();\n\n    const timeout =\n      options.socketTimeoutMS ??\n      options?.timeoutContext?.getSocketTimeoutMS() ??\n      this.socketTimeoutMS;\n    this.socket.setTimeout(timeout);\n\n    try {\n      await this.writeCommand(message, {\n        agreedCompressor: this.description.compressor ?? 'none',\n        zlibCompressionLevel: this.description.zlibCompressionLevel,\n        timeoutContext: options.timeoutContext,\n        signal: options.signal\n      });\n\n      if (options.noResponse || message.moreToCome) {\n        yield MongoDBResponse.empty;\n        return;\n      }\n\n      this.throwIfAborted();\n\n      if (\n        options.timeoutContext?.csotEnabled() &&\n        options.timeoutContext.minRoundTripTime != null &&\n        options.timeoutContext.remainingTimeMS < options.timeoutContext.minRoundTripTime\n      ) {\n        throw new MongoOperationTimeoutError(\n          'Server roundtrip time is greater than the time remaining'\n        );\n      }\n\n      for await (const response of this.readMany(options)) {\n        this.socket.setTimeout(0);\n        const bson = response.parse();\n\n        const document = (responseType ?? MongoDBResponse).make(bson);\n\n        yield document;\n        this.throwIfAborted();\n\n        this.socket.setTimeout(timeout);\n      }\n    } finally {\n      this.socket.setTimeout(0);\n    }\n  }\n\n  private async *sendCommand(\n    ns: MongoDBNamespace,\n    command: Document,\n    options: CommandOptions & Abortable,\n    responseType?: MongoDBResponseConstructor\n  ) {\n    options?.signal?.throwIfAborted();\n\n    const message = this.prepareCommand(ns.db, command, options);\n    let started = 0;\n    if (this.shouldEmitAndLogCommand) {\n      started = now();\n      this.emitAndLogCommand(\n        this.monitorCommands,\n        Connection.COMMAND_STARTED,\n        message.databaseName,\n        this.established,\n        new CommandStartedEvent(this, message, this.description.serverConnectionId)\n      );\n    }\n\n    // If `documentsReturnedIn` not set or raw is not enabled, use input bson options\n    // Otherwise, support raw flag. Raw only works for cursors that hardcode firstBatch/nextBatch fields\n    const bsonOptions: DeserializeOptions =\n      options.documentsReturnedIn == null || !options.raw\n        ? options\n        : {\n            ...options,\n            raw: false,\n            fieldsAsRaw: { [options.documentsReturnedIn]: true }\n          };\n\n    /** MongoDBResponse instance or subclass */\n    let document: MongoDBResponse | undefined = undefined;\n    /** Cached result of a toObject call */\n    let object: Document | undefined = undefined;\n    try {\n      this.throwIfAborted();\n      for await (document of this.sendWire(message, options, responseType)) {\n        object = undefined;\n        if (options.session != null) {\n          updateSessionFromResponse(options.session, document);\n        }\n\n        if (document.$clusterTime) {\n          this.clusterTime = document.$clusterTime;\n          this.emit(Connection.CLUSTER_TIME_RECEIVED, document.$clusterTime);\n        }\n\n        if (document.ok === 0) {\n          if (options.timeoutContext?.csotEnabled() && document.isMaxTimeExpiredError) {\n            throw new MongoOperationTimeoutError('Server reported a timeout error', {\n              cause: new MongoServerError((object ??= document.toObject(bsonOptions)))\n            });\n          }\n          throw new MongoServerError((object ??= document.toObject(bsonOptions)));\n        }\n\n        if (this.shouldEmitAndLogCommand) {\n          this.emitAndLogCommand(\n            this.monitorCommands,\n            Connection.COMMAND_SUCCEEDED,\n            message.databaseName,\n            this.established,\n            new CommandSucceededEvent(\n              this,\n              message,\n              options.noResponse\n                ? undefined\n                : message.moreToCome\n                  ? { ok: 1 }\n                  : (object ??= document.toObject(bsonOptions)),\n              started,\n              this.description.serverConnectionId\n            )\n          );\n        }\n\n        if (responseType == null) {\n          yield (object ??= document.toObject(bsonOptions));\n        } else {\n          yield document;\n        }\n\n        this.throwIfAborted();\n      }\n    } catch (error) {\n      if (this.shouldEmitAndLogCommand) {\n        this.emitAndLogCommand(\n          this.monitorCommands,\n          Connection.COMMAND_FAILED,\n          message.databaseName,\n          this.established,\n          new CommandFailedEvent(this, message, error, started, this.description.serverConnectionId)\n        );\n      }\n      throw error;\n    }\n  }\n\n  public async command<T extends MongoDBResponseConstructor>(\n    ns: MongoDBNamespace,\n    command: Document,\n    options: CommandOptions | undefined,\n    responseType: T\n  ): Promise<InstanceType<T>>;\n\n  public async command<T extends MongoDBResponseConstructor>(\n    ns: MongoDBNamespace,\n    command: Document,\n    options: CommandOptions | undefined,\n    responseType: T | undefined\n  ): Promise<typeof responseType extends undefined ? Document : InstanceType<T>>;\n\n  public async command(\n    ns: MongoDBNamespace,\n    command: Document,\n    options?: CommandOptions\n  ): Promise<Document>;\n\n  public async command(\n    ns: MongoDBNamespace,\n    command: Document,\n    options: CommandOptions & Abortable = {},\n    responseType?: MongoDBResponseConstructor\n  ): Promise<Document> {\n    this.throwIfAborted();\n    options.signal?.throwIfAborted();\n\n    for await (const document of this.sendCommand(ns, command, options, responseType)) {\n      if (options.timeoutContext?.csotEnabled()) {\n        if (MongoDBResponse.is(document)) {\n          if (document.isMaxTimeExpiredError) {\n            throw new MongoOperationTimeoutError('Server reported a timeout error', {\n              cause: new MongoServerError(document.toObject())\n            });\n          }\n        } else {\n          if (\n            (Array.isArray(document?.writeErrors) &&\n              document.writeErrors.some(\n                error => error?.code === MONGODB_ERROR_CODES.MaxTimeMSExpired\n              )) ||\n            document?.writeConcernError?.code === MONGODB_ERROR_CODES.MaxTimeMSExpired\n          ) {\n            throw new MongoOperationTimeoutError('Server reported a timeout error', {\n              cause: new MongoServerError(document)\n            });\n          }\n        }\n      }\n\n      return document;\n    }\n    throw new MongoUnexpectedServerResponseError('Unable to get response from server');\n  }\n\n  public exhaustCommand(\n    ns: MongoDBNamespace,\n    command: Document,\n    options: CommandOptions,\n    replyListener: Callback\n  ) {\n    const exhaustLoop = async () => {\n      this.throwIfAborted();\n      for await (const reply of this.sendCommand(ns, command, options)) {\n        replyListener(undefined, reply);\n        this.throwIfAborted();\n      }\n      throw new MongoUnexpectedServerResponseError('Server ended moreToCome unexpectedly');\n    };\n\n    exhaustLoop().then(undefined, replyListener);\n  }\n\n  private throwIfAborted() {\n    if (this.error) throw this.error;\n  }\n\n  /**\n   * @internal\n   *\n   * Writes an OP_MSG or OP_QUERY request to the socket, optionally compressing the command. This method\n   * waits until the socket's buffer has emptied (the Nodejs socket `drain` event has fired).\n   */\n  private async writeCommand(\n    command: WriteProtocolMessageType,\n    options: {\n      agreedCompressor?: CompressorName;\n      zlibCompressionLevel?: number;\n      timeoutContext?: TimeoutContext;\n    } & Abortable\n  ): Promise<void> {\n    const finalCommand =\n      options.agreedCompressor === 'none' || !OpCompressedRequest.canCompress(command)\n        ? command\n        : new OpCompressedRequest(command, {\n            agreedCompressor: options.agreedCompressor ?? 'none',\n            zlibCompressionLevel: options.zlibCompressionLevel ?? 0\n          });\n\n    const buffer = Buffer.concat(await finalCommand.toBin());\n\n    if (options.timeoutContext?.csotEnabled()) {\n      if (\n        options.timeoutContext.minRoundTripTime != null &&\n        options.timeoutContext.remainingTimeMS < options.timeoutContext.minRoundTripTime\n      ) {\n        throw new MongoOperationTimeoutError(\n          'Server roundtrip time is greater than the time remaining'\n        );\n      }\n    }\n\n    if (this.socket.write(buffer)) return;\n\n    const drainEvent = once<void>(this.socket, 'drain', options);\n    const timeout = options?.timeoutContext?.timeoutForSocketWrite;\n    const drained = timeout ? Promise.race([drainEvent, timeout]) : drainEvent;\n    try {\n      return await drained;\n    } catch (writeError) {\n      if (TimeoutError.is(writeError)) {\n        const timeoutError = new MongoOperationTimeoutError('Timed out at socket write');\n        this.onError(timeoutError);\n        throw timeoutError;\n      } else if (writeError === options.signal?.reason) {\n        this.onError(writeError);\n      }\n      throw writeError;\n    } finally {\n      timeout?.clear();\n    }\n  }\n\n  /**\n   * @internal\n   *\n   * Returns an async generator that yields full wire protocol messages from the underlying socket.  This function\n   * yields messages until `moreToCome` is false or not present in a response, or the caller cancels the request\n   * by calling `return` on the generator.\n   *\n   * Note that `for-await` loops call `return` automatically when the loop is exited.\n   */\n  private async *readMany(\n    options: {\n      timeoutContext?: TimeoutContext;\n    } & Abortable\n  ): AsyncGenerator<OpMsgResponse | OpReply> {\n    try {\n      this.dataEvents = onData(this.messageStream, options);\n      this.messageStream.resume();\n\n      for await (const message of this.dataEvents) {\n        const response = await decompressResponse(message);\n        yield response;\n\n        if (!response.moreToCome) {\n          return;\n        }\n      }\n    } catch (readError) {\n      if (TimeoutError.is(readError)) {\n        const timeoutError = new MongoOperationTimeoutError(\n          `Timed out during socket read (${readError.duration}ms)`\n        );\n        this.dataEvents = null;\n        this.onError(timeoutError);\n        throw timeoutError;\n      } else if (readError === options.signal?.reason) {\n        this.onError(readError);\n      }\n      throw readError;\n    } finally {\n      this.dataEvents = null;\n      this.messageStream.pause();\n      this.throwIfAborted();\n    }\n  }\n}\n\n/** @internal */\nexport class SizedMessageTransform extends Transform {\n  bufferPool: BufferPool;\n  connection: Connection;\n\n  constructor({ connection }: { connection: Connection }) {\n    super({ writableObjectMode: false, readableObjectMode: true });\n    this.bufferPool = new BufferPool();\n    this.connection = connection;\n  }\n\n  override _transform(chunk: Buffer, encoding: unknown, callback: TransformCallback): void {\n    if (this.connection.delayedTimeoutId != null) {\n      clearTimeout(this.connection.delayedTimeoutId);\n      this.connection.delayedTimeoutId = null;\n    }\n\n    this.bufferPool.append(chunk);\n\n    while (this.bufferPool.length) {\n      // While there are any bytes in the buffer\n\n      // Try to fetch a size from the top 4 bytes\n      const sizeOfMessage = this.bufferPool.getInt32();\n\n      if (sizeOfMessage == null) {\n        // Not even an int32 worth of data. Stop the loop, we need more chunks.\n        break;\n      }\n\n      if (sizeOfMessage < 0) {\n        // The size in the message has a negative value, this is probably corruption, throw:\n        return callback(new MongoParseError(`Message size cannot be negative: ${sizeOfMessage}`));\n      }\n\n      if (sizeOfMessage > this.bufferPool.length) {\n        // We do not have enough bytes to make a sizeOfMessage chunk\n        break;\n      }\n\n      // Add a message to the stream\n      const message = this.bufferPool.read(sizeOfMessage);\n\n      if (!this.push(message)) {\n        // We only subscribe to data events so we should never get backpressure\n        // if we do, we do not have the handling for it.\n        return callback(\n          new MongoRuntimeError(`SizedMessageTransform does not support backpressure`)\n        );\n      }\n    }\n\n    callback();\n  }\n}\n\n/** @internal */\nexport class CryptoConnection extends Connection {\n  /** @internal */\n  autoEncrypter?: AutoEncrypter;\n\n  constructor(stream: Stream, options: ConnectionOptions) {\n    super(stream, options);\n    this.autoEncrypter = options.autoEncrypter;\n  }\n\n  public override async command<T extends MongoDBResponseConstructor>(\n    ns: MongoDBNamespace,\n    command: Document,\n    options: CommandOptions | undefined,\n    responseType: T\n  ): Promise<InstanceType<T>>;\n\n  public override async command(\n    ns: MongoDBNamespace,\n    command: Document,\n    options?: CommandOptions\n  ): Promise<Document>;\n\n  override async command<T extends MongoDBResponseConstructor>(\n    ns: MongoDBNamespace,\n    cmd: Document,\n    options?: CommandOptions,\n    responseType?: T | undefined\n  ): Promise<Document> {\n    const { autoEncrypter } = this;\n    if (!autoEncrypter) {\n      // TODO(NODE-6065): throw a MongoRuntimeError in Node V7\n      // @ts-expect-error No cause provided because there is no underlying error.\n      throw new MongoMissingDependencyError('No AutoEncrypter available for encryption', {\n        dependencyName: 'n/a'\n      });\n    }\n\n    const serverWireVersion = maxWireVersion(this);\n    if (serverWireVersion === 0) {\n      // This means the initial handshake hasn't happened yet\n      return await super.command<T>(ns, cmd, options, responseType);\n    }\n\n    if (serverWireVersion < 8) {\n      throw new MongoCompatibilityError(\n        'Auto-encryption requires a minimum MongoDB version of 4.2'\n      );\n    }\n\n    // Save sort or indexKeys based on the command being run\n    // the encrypt API serializes our JS objects to BSON to pass to the native code layer\n    // and then deserializes the encrypted result, the protocol level components\n    // of the command (ex. sort) are then converted to JS objects potentially losing\n    // import key order information. These fields are never encrypted so we can save the values\n    // from before the encryption and replace them after encryption has been performed\n    const sort: Map<string, number> | null = cmd.find || cmd.findAndModify ? cmd.sort : null;\n    const indexKeys: Map<string, number>[] | null = cmd.createIndexes\n      ? cmd.indexes.map((index: { key: Map<string, number> }) => index.key)\n      : null;\n\n    const encrypted = await autoEncrypter.encrypt(ns.toString(), cmd, options);\n\n    // Replace the saved values\n    if (sort != null && (cmd.find || cmd.findAndModify)) {\n      encrypted.sort = sort;\n    }\n\n    if (indexKeys != null && cmd.createIndexes) {\n      for (const [offset, index] of indexKeys.entries()) {\n        // @ts-expect-error `encrypted` is a generic \"command\", but we've narrowed for only `createIndexes` commands here\n        encrypted.indexes[offset].key = index;\n      }\n    }\n\n    const encryptedResponse = await super.command(\n      ns,\n      encrypted,\n      options,\n      // Eventually we want to require `responseType` which means we would satisfy `T` as the return type.\n      // In the meantime, we want encryptedResponse to always be _at least_ a MongoDBResponse if not a more specific subclass\n      // So that we can ensure we have access to the on-demand APIs for decorate response\n      responseType ?? MongoDBResponse\n    );\n\n    const result = await autoEncrypter.decrypt(encryptedResponse.toBytes(), options);\n\n    const decryptedResponse = responseType?.make(result) ?? deserialize(result, options);\n\n    if (autoEncrypter[kDecorateResult]) {\n      if (responseType == null) {\n        decorateDecryptionResult(decryptedResponse, encryptedResponse.toObject(), true);\n      } else if (decryptedResponse instanceof CursorResponse) {\n        decryptedResponse.encryptedResponse = encryptedResponse;\n      }\n    }\n\n    return decryptedResponse;\n  }\n}\n"], "mappings": ";;;;;;AAkKAA,OAAA,CAAAC,iBAAA,GAAAA,iBAAA;AAlKA,MAAAC,QAAA,GAAAC,OAAA;AACA,MAAAC,QAAA,GAAAD,OAAA;AAEA,MAAAE,MAAA,GAAAF,OAAA;AAQA,MAAAG,WAAA,GAAAH,OAAA;AAUA,MAAAI,OAAA,GAAAJ,OAAA;AAcA,MAAAK,cAAA,GAAAL,OAAA;AACA,MAAAM,aAAA,GAAAN,OAAA;AACA,MAAAO,iBAAA,GAAAP,OAAA;AACA,MAAAQ,QAAA,GAAAR,OAAA;AACA,MAAAS,UAAA,GAAAT,OAAA;AACA,MAAAU,SAAA,GAAAV,OAAA;AACA,MAAAW,OAAA,GAAAX,OAAA;AAiBA,MAAAY,2BAAA,GAAAZ,OAAA;AAKA,MAAAa,UAAA,GAAAb,OAAA;AAUA,MAAAc,oBAAA,GAAAd,OAAA;AACA,MAAAe,aAAA,GAAAf,OAAA;AACA,MAAAgB,SAAA,GAAAhB,OAAA;AACA,MAAAiB,WAAA,GAAAjB,OAAA;AAKA,MAAAkB,QAAA,GAAAlB,OAAA;AAgFA;AACA,SAAgBF,iBAAiBA,CAACqB,IAAgB;EAChD,MAAMC,WAAW,GAAGD,IAAI,CAACC,WAAW;EACpC,OAAOA,WAAW,CAACC,4BAA4B,IAAI,IAAI;AACzD;AAEA,SAASC,gBAAgBA,CAACC,MAAc,EAAEC,OAA0B;EAClE,IAAIA,OAAO,CAACC,SAAS,EAAE;IACrB;IACA;IACA,OAAOD,OAAO,CAACE,WAAW,CAACC,QAAQ,EAAE;EACvC;EAEA,MAAM;IAAEC,aAAa;IAAEC;EAAU,CAAE,GAAGN,MAAM;EAC5C,IAAI,OAAOK,aAAa,KAAK,QAAQ,IAAI,OAAOC,UAAU,KAAK,QAAQ,EAAE;IACvE,OAAOlB,OAAA,CAAAmB,WAAW,CAACC,YAAY,CAACH,aAAa,EAAEC,UAAU,CAAC,CAACF,QAAQ,EAAE;EACvE;EAEA,OAAO,IAAAhB,OAAA,CAAAqB,MAAM,GAAE,CAACL,QAAQ,CAAC,KAAK,CAAC;AACjC;AAEA;AACA,MAAaM,UAAW,SAAQ3B,aAAA,CAAA4B,iBAAmC;EAgDjEC,YAAYZ,MAAc,EAAEC,OAA0B;IACpD,KAAK,EAAE;IA9CF,KAAAY,WAAW,GAAG,CAAC,CAAC;IAEhB,KAAAC,OAAO,GAAG,KAAK;IAEf,KAAAC,gBAAgB,GAA0B,IAAI;IAarD;IACO,KAAAC,MAAM,GAAG,KAAK;IAGb,KAAAC,WAAW,GAAoB,IAAI;IACnC,KAAAC,KAAK,GAAiB,IAAI;IAC1B,KAAAC,UAAU,GAA8C,IAAI;IAwBlE,IAAI,CAACC,EAAE,CAAC,OAAO,EAAEhC,OAAA,CAAAiC,IAAI,CAAC;IAEtB,IAAI,CAACC,MAAM,GAAGtB,MAAM;IACpB,IAAI,CAACuB,EAAE,GAAGtB,OAAO,CAACsB,EAAE;IACpB,IAAI,CAACC,OAAO,GAAGzB,gBAAgB,CAACC,MAAM,EAAEC,OAAO,CAAC;IAChD,IAAI,CAACwB,eAAe,GAAGxB,OAAO,CAACwB,eAAe,IAAI,CAAC;IACnD,IAAI,CAACC,eAAe,GAAGzB,OAAO,CAACyB,eAAe;IAC9C,IAAI,CAACC,SAAS,GAAG1B,OAAO,CAAC0B,SAAS;IAClC,IAAI,CAACC,WAAW,GAAG3B,OAAO,CAAC2B,WAAW;IACtC,IAAI,CAACC,WAAW,GAAG,KAAK;IAExB,IAAI,CAAChC,WAAW,GAAG,IAAIN,oBAAA,CAAAuC,iBAAiB,CAAC,IAAI,CAACN,OAAO,EAAEvB,OAAO,CAAC;IAC/D,IAAI,CAAC8B,UAAU,GAAG9B,OAAO,CAAC8B,UAAU;IACpC,IAAI,CAACC,WAAW,GAAG,IAAA5C,OAAA,CAAA6C,GAAG,GAAE;IAExB,IAAI,CAACC,aAAa,GAAG,IAAI,CAACZ,MAAM,CAC7BF,EAAE,CAAC,OAAO,EAAE,IAAI,CAACe,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC,CACpCC,IAAI,CAAC,IAAIC,qBAAqB,CAAC;MAAEC,UAAU,EAAE;IAAI,CAAE,CAAC,CAAC,CACrDnB,EAAE,CAAC,OAAO,EAAE,IAAI,CAACe,OAAO,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvC,IAAI,CAACd,MAAM,CAACF,EAAE,CAAC,OAAO,EAAE,IAAI,CAACoB,OAAO,CAACJ,IAAI,CAAC,IAAI,CAAC,CAAC;IAChD,IAAI,CAACd,MAAM,CAACF,EAAE,CAAC,SAAS,EAAE,IAAI,CAACqB,SAAS,CAACL,IAAI,CAAC,IAAI,CAAC,CAAC;IAEpD,IAAI,CAACF,aAAa,CAACQ,KAAK,EAAE;EAC5B;EAEA,IAAWC,KAAKA,CAAA;IACd,OAAO,IAAI,CAAC9C,WAAW,CAAC8C,KAAK;EAC/B;EAEA;EACA,IAAWA,KAAKA,CAACC,QAAyB;IACxC,IAAI,CAAC/C,WAAW,CAACgD,eAAe,CAACD,QAAQ,CAAC;IAC1CE,MAAM,CAACC,MAAM,CAAC,IAAI,CAAClD,WAAW,CAAC;EACjC;EAEA,IAAWmD,SAASA,CAAA;IAClB,OAAO,IAAI,CAACL,KAAK,EAAEK,SAAS;EAC9B;EAEA,IAAWC,YAAYA,CAAA;IACrB,OAAO,IAAI,CAACpD,WAAW,CAACoD,YAAY;EACtC;EAEA,IAAWC,QAAQA,CAAA;IACjB,OAAO,IAAA9D,OAAA,CAAA+D,qBAAqB,EAAC,IAAI,CAACnB,WAAW,CAAC;EAChD;EAEA,IAAYzD,iBAAiBA,CAAA;IAC3B,OAAO,IAAI,CAACsB,WAAW,CAACC,4BAA4B,IAAI,IAAI;EAC9D;EAEA,IAAYsD,aAAaA,CAAA;IACvB,OACE,IAAI,CAACvD,WAAW,IAAI,IAAI,IACxB,IAAAT,OAAA,CAAAiE,cAAc,EAAC,IAAI,CAAC,IAAI,CAAC,IACzB,CAAC,IAAI,CAACxD,WAAW,CAACyD,sBAAsB;EAE5C;EAEA,IAAYC,uBAAuBA,CAAA;IACjC,OACE,CAAC,IAAI,CAAC7B,eAAe,IAClB,IAAI,CAACG,WAAW,IACf,CAAC,IAAI,CAAC2B,WAAW,EAAEC,gBAAgB,IACnC,IAAI,CAAC7B,WAAW,EAAE8B,OAAO,CAAC5E,cAAA,CAAA6E,sBAAsB,CAACC,OAAO,EAAE9E,cAAA,CAAA+E,aAAa,CAACC,KAAK,CAAE,KACnF,KAAK;EAET;EAEOC,aAAaA,CAAA;IAClB,IAAI,CAAC/B,WAAW,GAAG,IAAA5C,OAAA,CAAA6C,GAAG,GAAE;EAC1B;EAEOE,OAAOA,CAACjB,KAAY;IACzB,IAAI,CAAC8C,OAAO,CAAC9C,KAAK,CAAC;EACrB;EAEQsB,OAAOA,CAAA;IACb,MAAMyB,OAAO,GAAG,cAAc,IAAI,CAAC1C,EAAE,OAAO,IAAI,CAACC,OAAO,SAAS;IACjE,IAAI,CAACwC,OAAO,CAAC,IAAInF,OAAA,CAAAqF,iBAAiB,CAACD,OAAO,CAAC,CAAC;EAC9C;EAEQxB,SAASA,CAAA;IACf,IAAI,CAAC1B,gBAAgB,GAAG,IAAArC,QAAA,CAAAyF,UAAU,EAAC,MAAK;MACtC,MAAMF,OAAO,GAAG,cAAc,IAAI,CAAC1C,EAAE,OAAO,IAAI,CAACC,OAAO,YAAY;MACpE,MAAM4C,eAAe,GAAG,IAAI,CAACzB,KAAK,IAAI,IAAI;MAC1C,IAAI,CAACqB,OAAO,CAAC,IAAInF,OAAA,CAAAwF,wBAAwB,CAACJ,OAAO,EAAE;QAAEG;MAAe,CAAE,CAAC,CAAC;IAC1E,CAAC,EAAE,CAAC,CAAC,CAACE,KAAK,EAAE,CAAC,CAAC;EACjB;EAEOC,OAAOA,CAAA;IACZ,IAAI,IAAI,CAACvD,MAAM,EAAE;MACf;IACF;IAEA;IACA;IACA;IACA,IAAI,CAACwD,kBAAkB,CAAC9D,UAAU,CAAC+D,MAAM,CAAC;IAC1C,IAAI,CAACD,kBAAkB,CAAC9D,UAAU,CAACgE,QAAQ,CAAC;IAC5C,MAAMT,OAAO,GAAG,cAAc,IAAI,CAAC1C,EAAE,OAAO,IAAI,CAACC,OAAO,SAAS;IACjE,IAAI,CAACwC,OAAO,CAAC,IAAInF,OAAA,CAAAqF,iBAAiB,CAACD,OAAO,CAAC,CAAC;EAC9C;EAEA;;;;;;;;EAQQD,OAAOA,CAAC9C,KAAY;IAC1B,IAAI,IAAI,CAACF,MAAM,EAAE;MACf;IACF;IAEA,IAAI,CAACM,MAAM,CAACiD,OAAO,EAAE;IACrB,IAAI,CAACrD,KAAK,GAAGA,KAAK;IAElB,IAAI,CAACC,UAAU,EAAEwD,KAAK,CAACzD,KAAK,CAAC,CAAC0D,IAAI,CAACC,SAAS,EAAEzF,OAAA,CAAA0F,WAAW,CAAC;IAC1D,IAAI,CAAC9D,MAAM,GAAG,IAAI;IAClB,IAAI,CAAC+D,IAAI,CAACrE,UAAU,CAACsE,KAAK,CAAC;EAC7B;EAEQC,cAAcA,CAACC,EAAU,EAAEC,OAAiB,EAAElF,OAAuB;IAC3E,IAAImF,GAAG,GAAG;MAAE,GAAGD;IAAO,CAAE;IAExB,MAAME,cAAc,GAAG,IAAA1F,QAAA,CAAA2F,iBAAiB,EAACrF,OAAO,CAAC;IACjD,MAAMsF,OAAO,GAAGtF,OAAO,EAAEsF,OAAO;IAEhC,IAAItE,WAAW,GAAG,IAAI,CAACA,WAAW;IAElC,IAAI,IAAI,CAACU,SAAS,EAAE;MAClB,MAAM;QAAE6D,OAAO;QAAEC,MAAM;QAAEC;MAAiB,CAAE,GAAG,IAAI,CAAC/D,SAAS;MAC7DyD,GAAG,CAACO,UAAU,GAAGH,OAAO;MACxB,IAAIC,MAAM,IAAI,IAAI,EAAEL,GAAG,CAACQ,SAAS,GAAGH,MAAM;MAC1C,IAAIC,iBAAiB,IAAI,IAAI,EAAEN,GAAG,CAACS,oBAAoB,GAAGH,iBAAiB;IAC7E;IAEA,IAAI,IAAI,CAACnH,iBAAiB,IAAIgH,OAAO,EAAE;MACrC,IACEA,OAAO,CAACtE,WAAW,IACnBA,WAAW,IACXsE,OAAO,CAACtE,WAAW,CAACA,WAAW,CAAC6E,WAAW,CAAC7E,WAAW,CAACA,WAAW,CAAC,EACpE;QACAA,WAAW,GAAGsE,OAAO,CAACtE,WAAW;MACnC;MAEA,MAAM8E,YAAY,GAAG,IAAA7G,UAAA,CAAA8G,YAAY,EAACT,OAAO,EAAEH,GAAG,EAAEnF,OAAO,CAAC;MACxD,IAAI8F,YAAY,EAAE,MAAMA,YAAY;IACtC,CAAC,MAAM,IAAIR,OAAO,EAAEU,QAAQ,EAAE;MAC5B,MAAM,IAAIpH,OAAA,CAAAqH,uBAAuB,CAAC,4CAA4C,CAAC;IACjF;IAEA;IACA,IAAIjF,WAAW,EAAE;MACfmE,GAAG,CAACe,YAAY,GAAGlF,WAAW;IAChC;IAEA;IACA,IAAI,IAAI,CAACpB,WAAW,CAACuG,IAAI,KAAKnH,QAAA,CAAAoH,UAAU,CAACC,UAAU,EAAE;MACnD,IACE,CAAC,IAAA3G,QAAA,CAAA4G,SAAS,EAAC,IAAI,CAAC,IAChB,CAAC,IAAI,CAAC1G,WAAW,CAACoD,YAAY,IAC9B,IAAI,CAACG,aAAa,IAClBnD,OAAO,CAACuG,gBAAgB,KAAK,IAAI,IACjCnB,cAAc,EAAEoB,IAAI,KAAK,SAAS,EAClC;QACA;QACA;QACA;QACA;QACA;QACArB,GAAG,CAACsB,eAAe,GAAG1H,iBAAA,CAAA2H,cAAc,CAACC,gBAAgB,CAACC,MAAM,EAAE;MAChE,CAAC,MAAM,IAAI,IAAAlH,QAAA,CAAA4G,SAAS,EAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAACnD,aAAa,IAAIiC,cAAc,EAAEoB,IAAI,KAAK,SAAS,EAAE;QACvF;QACA;QACArB,GAAG,GAAG;UACJ0B,MAAM,EAAE1B,GAAG;UACXsB,eAAe,EAAErB,cAAc,CAACwB,MAAM;SACvC;MACH,CAAC,MAAM,IAAIxB,cAAc,EAAEoB,IAAI,KAAK,SAAS,EAAE;QAC7C;QACA;QACA;QACArB,GAAG,CAACsB,eAAe,GAAGrB,cAAc,CAACwB,MAAM,EAAE;MAC/C;IACF;IAEA,MAAME,cAAc,GAAG;MACrBC,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE,CAAC,CAAC;MAClBC,SAAS,EAAE,KAAK;MAChB;MACAC,WAAW,EAAE9B,cAAc,CAAC8B,WAAW,EAAE;MACzC,GAAGlH;KACJ;IAEDA,OAAO,CAACmH,cAAc,EAAEC,qBAAqB,CAACjC,GAAG,EAAEnF,OAAO,CAAC;IAE3D,MAAMgE,OAAO,GAAG,IAAI,CAACb,aAAa,GAC9B,IAAI9D,UAAA,CAAAgI,YAAY,CAACpC,EAAE,EAAEE,GAAG,EAAE2B,cAAc,CAAC,GACzC,IAAIzH,UAAA,CAAAiI,cAAc,CAACrC,EAAE,EAAEE,GAAG,EAAE2B,cAAc,CAAC;IAE/C,OAAO9C,OAAO;EAChB;EAEQ,OAAOuD,QAAQA,CACrBvD,OAAiC,EACjChE,OAAmC,EACnCwH,YAAyC;IAEzC,IAAI,CAACC,cAAc,EAAE;IAErB,MAAMC,OAAO,GACX1H,OAAO,CAACwB,eAAe,IACvBxB,OAAO,EAAEmH,cAAc,EAAEQ,kBAAkB,EAAE,IAC7C,IAAI,CAACnG,eAAe;IACtB,IAAI,CAACH,MAAM,CAAC6C,UAAU,CAACwD,OAAO,CAAC;IAE/B,IAAI;MACF,MAAM,IAAI,CAACE,YAAY,CAAC5D,OAAO,EAAE;QAC/B6D,gBAAgB,EAAE,IAAI,CAACjI,WAAW,CAACkI,UAAU,IAAI,MAAM;QACvDC,oBAAoB,EAAE,IAAI,CAACnI,WAAW,CAACmI,oBAAoB;QAC3DZ,cAAc,EAAEnH,OAAO,CAACmH,cAAc;QACtCa,MAAM,EAAEhI,OAAO,CAACgI;OACjB,CAAC;MAEF,IAAIhI,OAAO,CAACiI,UAAU,IAAIjE,OAAO,CAACkE,UAAU,EAAE;QAC5C,MAAMzI,WAAA,CAAA0I,eAAe,CAACC,KAAK;QAC3B;MACF;MAEA,IAAI,CAACX,cAAc,EAAE;MAErB,IACEzH,OAAO,CAACmH,cAAc,EAAEkB,WAAW,EAAE,IACrCrI,OAAO,CAACmH,cAAc,CAACmB,gBAAgB,IAAI,IAAI,IAC/CtI,OAAO,CAACmH,cAAc,CAACoB,eAAe,GAAGvI,OAAO,CAACmH,cAAc,CAACmB,gBAAgB,EAChF;QACA,MAAM,IAAI1J,OAAA,CAAA4J,0BAA0B,CAClC,0DAA0D,CAC3D;MACH;MAEA,WAAW,MAAM7F,QAAQ,IAAI,IAAI,CAAC8F,QAAQ,CAACzI,OAAO,CAAC,EAAE;QACnD,IAAI,CAACqB,MAAM,CAAC6C,UAAU,CAAC,CAAC,CAAC;QACzB,MAAMwE,IAAI,GAAG/F,QAAQ,CAACgG,KAAK,EAAE;QAE7B,MAAMC,QAAQ,GAAG,CAACpB,YAAY,IAAI/H,WAAA,CAAA0I,eAAe,EAAEU,IAAI,CAACH,IAAI,CAAC;QAE7D,MAAME,QAAQ;QACd,IAAI,CAACnB,cAAc,EAAE;QAErB,IAAI,CAACpG,MAAM,CAAC6C,UAAU,CAACwD,OAAO,CAAC;MACjC;IACF,CAAC,SAAS;MACR,IAAI,CAACrG,MAAM,CAAC6C,UAAU,CAAC,CAAC,CAAC;IAC3B;EACF;EAEQ,OAAO4E,WAAWA,CACxBC,EAAoB,EACpB7D,OAAiB,EACjBlF,OAAmC,EACnCwH,YAAyC;IAEzCxH,OAAO,EAAEgI,MAAM,EAAEP,cAAc,EAAE;IAEjC,MAAMzD,OAAO,GAAG,IAAI,CAACgB,cAAc,CAAC+D,EAAE,CAAC9D,EAAE,EAAEC,OAAO,EAAElF,OAAO,CAAC;IAC5D,IAAIgJ,OAAO,GAAG,CAAC;IACf,IAAI,IAAI,CAAC1F,uBAAuB,EAAE;MAChC0F,OAAO,GAAG,IAAA7J,OAAA,CAAA6C,GAAG,GAAE;MACf,IAAI,CAACiH,iBAAiB,CACpB,IAAI,CAACxH,eAAe,EACpBhB,UAAU,CAACyI,eAAe,EAC1BlF,OAAO,CAACmF,YAAY,EACpB,IAAI,CAACvH,WAAW,EAChB,IAAIxC,2BAAA,CAAAgK,mBAAmB,CAAC,IAAI,EAAEpF,OAAO,EAAE,IAAI,CAACpE,WAAW,CAACyJ,kBAAkB,CAAC,CAC5E;IACH;IAEA;IACA;IACA,MAAMC,WAAW,GACftJ,OAAO,CAACuJ,mBAAmB,IAAI,IAAI,IAAI,CAACvJ,OAAO,CAACwJ,GAAG,GAC/CxJ,OAAO,GACP;MACE,GAAGA,OAAO;MACVwJ,GAAG,EAAE,KAAK;MACVC,WAAW,EAAE;QAAE,CAACzJ,OAAO,CAACuJ,mBAAmB,GAAG;MAAI;KACnD;IAEP;IACA,IAAIX,QAAQ,GAAgChE,SAAS;IACrD;IACA,IAAI8E,MAAM,GAAyB9E,SAAS;IAC5C,IAAI;MACF,IAAI,CAAC6C,cAAc,EAAE;MACrB,WAAWmB,QAAQ,IAAI,IAAI,CAACrB,QAAQ,CAACvD,OAAO,EAAEhE,OAAO,EAAEwH,YAAY,CAAC,EAAE;QACpEkC,MAAM,GAAG9E,SAAS;QAClB,IAAI5E,OAAO,CAACsF,OAAO,IAAI,IAAI,EAAE;UAC3B,IAAArG,UAAA,CAAA0K,yBAAyB,EAAC3J,OAAO,CAACsF,OAAO,EAAEsD,QAAQ,CAAC;QACtD;QAEA,IAAIA,QAAQ,CAAC1C,YAAY,EAAE;UACzB,IAAI,CAAClF,WAAW,GAAG4H,QAAQ,CAAC1C,YAAY;UACxC,IAAI,CAACpB,IAAI,CAACrE,UAAU,CAACmJ,qBAAqB,EAAEhB,QAAQ,CAAC1C,YAAY,CAAC;QACpE;QAEA,IAAI0C,QAAQ,CAACiB,EAAE,KAAK,CAAC,EAAE;UACrB,IAAI7J,OAAO,CAACmH,cAAc,EAAEkB,WAAW,EAAE,IAAIO,QAAQ,CAACkB,qBAAqB,EAAE;YAC3E,MAAM,IAAIlL,OAAA,CAAA4J,0BAA0B,CAAC,iCAAiC,EAAE;cACtEuB,KAAK,EAAE,IAAInL,OAAA,CAAAoL,gBAAgB,CAAEN,MAAM,KAAKd,QAAQ,CAACqB,QAAQ,CAACX,WAAW,CAAE;aACxE,CAAC;UACJ;UACA,MAAM,IAAI1K,OAAA,CAAAoL,gBAAgB,CAAEN,MAAM,KAAKd,QAAQ,CAACqB,QAAQ,CAACX,WAAW,CAAE,CAAC;QACzE;QAEA,IAAI,IAAI,CAAChG,uBAAuB,EAAE;UAChC,IAAI,CAAC2F,iBAAiB,CACpB,IAAI,CAACxH,eAAe,EACpBhB,UAAU,CAACyJ,iBAAiB,EAC5BlG,OAAO,CAACmF,YAAY,EACpB,IAAI,CAACvH,WAAW,EAChB,IAAIxC,2BAAA,CAAA+K,qBAAqB,CACvB,IAAI,EACJnG,OAAO,EACPhE,OAAO,CAACiI,UAAU,GACdrD,SAAS,GACTZ,OAAO,CAACkE,UAAU,GAChB;YAAE2B,EAAE,EAAE;UAAC,CAAE,GACRH,MAAM,KAAKd,QAAQ,CAACqB,QAAQ,CAACX,WAAW,CAAE,EACjDN,OAAO,EACP,IAAI,CAACpJ,WAAW,CAACyJ,kBAAkB,CACpC,CACF;QACH;QAEA,IAAI7B,YAAY,IAAI,IAAI,EAAE;UACxB,MAAOkC,MAAM,KAAKd,QAAQ,CAACqB,QAAQ,CAACX,WAAW,CAAE;QACnD,CAAC,MAAM;UACL,MAAMV,QAAQ;QAChB;QAEA,IAAI,CAACnB,cAAc,EAAE;MACvB;IACF,CAAC,CAAC,OAAOxG,KAAK,EAAE;MACd,IAAI,IAAI,CAACqC,uBAAuB,EAAE;QAChC,IAAI,CAAC2F,iBAAiB,CACpB,IAAI,CAACxH,eAAe,EACpBhB,UAAU,CAAC2J,cAAc,EACzBpG,OAAO,CAACmF,YAAY,EACpB,IAAI,CAACvH,WAAW,EAChB,IAAIxC,2BAAA,CAAAiL,kBAAkB,CAAC,IAAI,EAAErG,OAAO,EAAE/C,KAAK,EAAE+H,OAAO,EAAE,IAAI,CAACpJ,WAAW,CAACyJ,kBAAkB,CAAC,CAC3F;MACH;MACA,MAAMpI,KAAK;IACb;EACF;EAsBO,MAAMiE,OAAOA,CAClB6D,EAAoB,EACpB7D,OAAiB,EACjBlF,OAAA,GAAsC,EAAE,EACxCwH,YAAyC;IAEzC,IAAI,CAACC,cAAc,EAAE;IACrBzH,OAAO,CAACgI,MAAM,EAAEP,cAAc,EAAE;IAEhC,WAAW,MAAMmB,QAAQ,IAAI,IAAI,CAACE,WAAW,CAACC,EAAE,EAAE7D,OAAO,EAAElF,OAAO,EAAEwH,YAAY,CAAC,EAAE;MACjF,IAAIxH,OAAO,CAACmH,cAAc,EAAEkB,WAAW,EAAE,EAAE;QACzC,IAAI5I,WAAA,CAAA0I,eAAe,CAACmC,EAAE,CAAC1B,QAAQ,CAAC,EAAE;UAChC,IAAIA,QAAQ,CAACkB,qBAAqB,EAAE;YAClC,MAAM,IAAIlL,OAAA,CAAA4J,0BAA0B,CAAC,iCAAiC,EAAE;cACtEuB,KAAK,EAAE,IAAInL,OAAA,CAAAoL,gBAAgB,CAACpB,QAAQ,CAACqB,QAAQ,EAAE;aAChD,CAAC;UACJ;QACF,CAAC,MAAM;UACL,IACGM,KAAK,CAACC,OAAO,CAAC5B,QAAQ,EAAE6B,WAAW,CAAC,IACnC7B,QAAQ,CAAC6B,WAAW,CAACC,IAAI,CACvBzJ,KAAK,IAAIA,KAAK,EAAE0J,IAAI,KAAK/L,OAAA,CAAAgM,mBAAmB,CAACC,gBAAgB,CAC9D,IACHjC,QAAQ,EAAEkC,iBAAiB,EAAEH,IAAI,KAAK/L,OAAA,CAAAgM,mBAAmB,CAACC,gBAAgB,EAC1E;YACA,MAAM,IAAIjM,OAAA,CAAA4J,0BAA0B,CAAC,iCAAiC,EAAE;cACtEuB,KAAK,EAAE,IAAInL,OAAA,CAAAoL,gBAAgB,CAACpB,QAAQ;aACrC,CAAC;UACJ;QACF;MACF;MAEA,OAAOA,QAAQ;IACjB;IACA,MAAM,IAAIhK,OAAA,CAAAmM,kCAAkC,CAAC,oCAAoC,CAAC;EACpF;EAEOC,cAAcA,CACnBjC,EAAoB,EACpB7D,OAAiB,EACjBlF,OAAuB,EACvBiL,aAAuB;IAEvB,MAAMC,WAAW,GAAG,MAAAA,CAAA,KAAW;MAC7B,IAAI,CAACzD,cAAc,EAAE;MACrB,WAAW,MAAM0D,KAAK,IAAI,IAAI,CAACrC,WAAW,CAACC,EAAE,EAAE7D,OAAO,EAAElF,OAAO,CAAC,EAAE;QAChEiL,aAAa,CAACrG,SAAS,EAAEuG,KAAK,CAAC;QAC/B,IAAI,CAAC1D,cAAc,EAAE;MACvB;MACA,MAAM,IAAI7I,OAAA,CAAAmM,kCAAkC,CAAC,sCAAsC,CAAC;IACtF,CAAC;IAEDG,WAAW,EAAE,CAACvG,IAAI,CAACC,SAAS,EAAEqG,aAAa,CAAC;EAC9C;EAEQxD,cAAcA,CAAA;IACpB,IAAI,IAAI,CAACxG,KAAK,EAAE,MAAM,IAAI,CAACA,KAAK;EAClC;EAEA;;;;;;EAMQ,MAAM2G,YAAYA,CACxB1C,OAAiC,EACjClF,OAIa;IAEb,MAAMoL,YAAY,GAChBpL,OAAO,CAAC6H,gBAAgB,KAAK,MAAM,IAAI,CAACxI,UAAA,CAAAgM,mBAAmB,CAACC,WAAW,CAACpG,OAAO,CAAC,GAC5EA,OAAO,GACP,IAAI7F,UAAA,CAAAgM,mBAAmB,CAACnG,OAAO,EAAE;MAC/B2C,gBAAgB,EAAE7H,OAAO,CAAC6H,gBAAgB,IAAI,MAAM;MACpDE,oBAAoB,EAAE/H,OAAO,CAAC+H,oBAAoB,IAAI;KACvD,CAAC;IAER,MAAMwD,MAAM,GAAGC,MAAM,CAACC,MAAM,CAAC,MAAML,YAAY,CAACM,KAAK,EAAE,CAAC;IAExD,IAAI1L,OAAO,CAACmH,cAAc,EAAEkB,WAAW,EAAE,EAAE;MACzC,IACErI,OAAO,CAACmH,cAAc,CAACmB,gBAAgB,IAAI,IAAI,IAC/CtI,OAAO,CAACmH,cAAc,CAACoB,eAAe,GAAGvI,OAAO,CAACmH,cAAc,CAACmB,gBAAgB,EAChF;QACA,MAAM,IAAI1J,OAAA,CAAA4J,0BAA0B,CAClC,0DAA0D,CAC3D;MACH;IACF;IAEA,IAAI,IAAI,CAACnH,MAAM,CAACsK,KAAK,CAACJ,MAAM,CAAC,EAAE;IAE/B,MAAMK,UAAU,GAAG,IAAAzM,OAAA,CAAA0M,IAAI,EAAO,IAAI,CAACxK,MAAM,EAAE,OAAO,EAAErB,OAAO,CAAC;IAC5D,MAAM0H,OAAO,GAAG1H,OAAO,EAAEmH,cAAc,EAAE2E,qBAAqB;IAC9D,MAAMC,OAAO,GAAGrE,OAAO,GAAGsE,OAAO,CAACC,IAAI,CAAC,CAACL,UAAU,EAAElE,OAAO,CAAC,CAAC,GAAGkE,UAAU;IAC1E,IAAI;MACF,OAAO,MAAMG,OAAO;IACtB,CAAC,CAAC,OAAOG,UAAU,EAAE;MACnB,IAAIhN,SAAA,CAAAiN,YAAY,CAAC7B,EAAE,CAAC4B,UAAU,CAAC,EAAE;QAC/B,MAAME,YAAY,GAAG,IAAIxN,OAAA,CAAA4J,0BAA0B,CAAC,2BAA2B,CAAC;QAChF,IAAI,CAACtG,OAAO,CAACkK,YAAY,CAAC;QAC1B,MAAMA,YAAY;MACpB,CAAC,MAAM,IAAIF,UAAU,KAAKlM,OAAO,CAACgI,MAAM,EAAEqE,MAAM,EAAE;QAChD,IAAI,CAACnK,OAAO,CAACgK,UAAU,CAAC;MAC1B;MACA,MAAMA,UAAU;IAClB,CAAC,SAAS;MACRxE,OAAO,EAAE4E,KAAK,EAAE;IAClB;EACF;EAEA;;;;;;;;;EASQ,OAAO7D,QAAQA,CACrBzI,OAEa;IAEb,IAAI;MACF,IAAI,CAACkB,UAAU,GAAG,IAAA1B,SAAA,CAAA+M,MAAM,EAAC,IAAI,CAACtK,aAAa,EAAEjC,OAAO,CAAC;MACrD,IAAI,CAACiC,aAAa,CAACuK,MAAM,EAAE;MAE3B,WAAW,MAAMxI,OAAO,IAAI,IAAI,CAAC9C,UAAU,EAAE;QAC3C,MAAMyB,QAAQ,GAAG,MAAM,IAAApD,aAAA,CAAAkN,kBAAkB,EAACzI,OAAO,CAAC;QAClD,MAAMrB,QAAQ;QAEd,IAAI,CAACA,QAAQ,CAACuF,UAAU,EAAE;UACxB;QACF;MACF;IACF,CAAC,CAAC,OAAOwE,SAAS,EAAE;MAClB,IAAIxN,SAAA,CAAAiN,YAAY,CAAC7B,EAAE,CAACoC,SAAS,CAAC,EAAE;QAC9B,MAAMN,YAAY,GAAG,IAAIxN,OAAA,CAAA4J,0BAA0B,CACjD,iCAAiCkE,SAAS,CAACC,QAAQ,KAAK,CACzD;QACD,IAAI,CAACzL,UAAU,GAAG,IAAI;QACtB,IAAI,CAACgB,OAAO,CAACkK,YAAY,CAAC;QAC1B,MAAMA,YAAY;MACpB,CAAC,MAAM,IAAIM,SAAS,KAAK1M,OAAO,CAACgI,MAAM,EAAEqE,MAAM,EAAE;QAC/C,IAAI,CAACnK,OAAO,CAACwK,SAAS,CAAC;MACzB;MACA,MAAMA,SAAS;IACjB,CAAC,SAAS;MACR,IAAI,CAACxL,UAAU,GAAG,IAAI;MACtB,IAAI,CAACe,aAAa,CAACQ,KAAK,EAAE;MAC1B,IAAI,CAACgF,cAAc,EAAE;IACvB;EACF;;AA9kBFpJ,OAAA,CAAAoC,UAAA,GAAAA,UAAA;AAiCE;AACgBA,UAAA,CAAAyI,eAAe,GAAGvK,WAAA,CAAAuK,eAAe;AACjD;AACgBzI,UAAA,CAAAyJ,iBAAiB,GAAGvL,WAAA,CAAAuL,iBAAiB;AACrD;AACgBzJ,UAAA,CAAA2J,cAAc,GAAGzL,WAAA,CAAAyL,cAAc;AAC/C;AACgB3J,UAAA,CAAAmJ,qBAAqB,GAAGjL,WAAA,CAAAiL,qBAAqB;AAC7D;AACgBnJ,UAAA,CAAAsE,KAAK,GAAGpG,WAAA,CAAAoG,KAAK;AAC7B;AACgBtE,UAAA,CAAA+D,MAAM,GAAG7F,WAAA,CAAA6F,MAAM;AAC/B;AACgB/D,UAAA,CAAAgE,QAAQ,GAAG9F,WAAA,CAAA8F,QAAQ;AAmiBrC;AACA,MAAapC,qBAAsB,SAAQ9D,QAAA,CAAAqO,SAAS;EAIlDjM,YAAY;IAAE2B;EAAU,CAA8B;IACpD,KAAK,CAAC;MAAEuK,kBAAkB,EAAE,KAAK;MAAEC,kBAAkB,EAAE;IAAI,CAAE,CAAC;IAC9D,IAAI,CAACC,UAAU,GAAG,IAAI5N,OAAA,CAAA6N,UAAU,EAAE;IAClC,IAAI,CAAC1K,UAAU,GAAGA,UAAU;EAC9B;EAES2K,UAAUA,CAACC,KAAa,EAAEC,QAAiB,EAAEC,QAA2B;IAC/E,IAAI,IAAI,CAAC9K,UAAU,CAACxB,gBAAgB,IAAI,IAAI,EAAE;MAC5C,IAAArC,QAAA,CAAA4O,YAAY,EAAC,IAAI,CAAC/K,UAAU,CAACxB,gBAAgB,CAAC;MAC9C,IAAI,CAACwB,UAAU,CAACxB,gBAAgB,GAAG,IAAI;IACzC;IAEA,IAAI,CAACiM,UAAU,CAACO,MAAM,CAACJ,KAAK,CAAC;IAE7B,OAAO,IAAI,CAACH,UAAU,CAACQ,MAAM,EAAE;MAC7B;MAEA;MACA,MAAMC,aAAa,GAAG,IAAI,CAACT,UAAU,CAACU,QAAQ,EAAE;MAEhD,IAAID,aAAa,IAAI,IAAI,EAAE;QACzB;QACA;MACF;MAEA,IAAIA,aAAa,GAAG,CAAC,EAAE;QACrB;QACA,OAAOJ,QAAQ,CAAC,IAAIxO,OAAA,CAAA8O,eAAe,CAAC,oCAAoCF,aAAa,EAAE,CAAC,CAAC;MAC3F;MAEA,IAAIA,aAAa,GAAG,IAAI,CAACT,UAAU,CAACQ,MAAM,EAAE;QAC1C;QACA;MACF;MAEA;MACA,MAAMvJ,OAAO,GAAG,IAAI,CAAC+I,UAAU,CAACY,IAAI,CAACH,aAAa,CAAC;MAEnD,IAAI,CAAC,IAAI,CAACI,IAAI,CAAC5J,OAAO,CAAC,EAAE;QACvB;QACA;QACA,OAAOoJ,QAAQ,CACb,IAAIxO,OAAA,CAAAiP,iBAAiB,CAAC,qDAAqD,CAAC,CAC7E;MACH;IACF;IAEAT,QAAQ,EAAE;EACZ;;AApDF/O,OAAA,CAAAgE,qBAAA,GAAAA,qBAAA;AAuDA;AACA,MAAayL,gBAAiB,SAAQrN,UAAU;EAI9CE,YAAYZ,MAAc,EAAEC,OAA0B;IACpD,KAAK,CAACD,MAAM,EAAEC,OAAO,CAAC;IACtB,IAAI,CAAC+N,aAAa,GAAG/N,OAAO,CAAC+N,aAAa;EAC5C;EAeS,MAAM7I,OAAOA,CACpB6D,EAAoB,EACpB5D,GAAa,EACbnF,OAAwB,EACxBwH,YAA4B;IAE5B,MAAM;MAAEuG;IAAa,CAAE,GAAG,IAAI;IAC9B,IAAI,CAACA,aAAa,EAAE;MAClB;MACA;MACA,MAAM,IAAInP,OAAA,CAAAoP,2BAA2B,CAAC,2CAA2C,EAAE;QACjFC,cAAc,EAAE;OACjB,CAAC;IACJ;IAEA,MAAMC,iBAAiB,GAAG,IAAA/O,OAAA,CAAAiE,cAAc,EAAC,IAAI,CAAC;IAC9C,IAAI8K,iBAAiB,KAAK,CAAC,EAAE;MAC3B;MACA,OAAO,MAAM,KAAK,CAAChJ,OAAO,CAAI6D,EAAE,EAAE5D,GAAG,EAAEnF,OAAO,EAAEwH,YAAY,CAAC;IAC/D;IAEA,IAAI0G,iBAAiB,GAAG,CAAC,EAAE;MACzB,MAAM,IAAItP,OAAA,CAAAqH,uBAAuB,CAC/B,2DAA2D,CAC5D;IACH;IAEA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMkI,IAAI,GAA+BhJ,GAAG,CAACiJ,IAAI,IAAIjJ,GAAG,CAACkJ,aAAa,GAAGlJ,GAAG,CAACgJ,IAAI,GAAG,IAAI;IACxF,MAAMG,SAAS,GAAiCnJ,GAAG,CAACoJ,aAAa,GAC7DpJ,GAAG,CAACqJ,OAAO,CAACC,GAAG,CAAEC,KAAmC,IAAKA,KAAK,CAACC,GAAG,CAAC,GACnE,IAAI;IAER,MAAMC,SAAS,GAAG,MAAMb,aAAa,CAACc,OAAO,CAAC9F,EAAE,CAAC5I,QAAQ,EAAE,EAAEgF,GAAG,EAAEnF,OAAO,CAAC;IAE1E;IACA,IAAImO,IAAI,IAAI,IAAI,KAAKhJ,GAAG,CAACiJ,IAAI,IAAIjJ,GAAG,CAACkJ,aAAa,CAAC,EAAE;MACnDO,SAAS,CAACT,IAAI,GAAGA,IAAI;IACvB;IAEA,IAAIG,SAAS,IAAI,IAAI,IAAInJ,GAAG,CAACoJ,aAAa,EAAE;MAC1C,KAAK,MAAM,CAACO,MAAM,EAAEJ,KAAK,CAAC,IAAIJ,SAAS,CAACS,OAAO,EAAE,EAAE;QACjD;QACAH,SAAS,CAACJ,OAAO,CAACM,MAAM,CAAC,CAACH,GAAG,GAAGD,KAAK;MACvC;IACF;IAEA,MAAMM,iBAAiB,GAAG,MAAM,KAAK,CAAC9J,OAAO,CAC3C6D,EAAE,EACF6F,SAAS,EACT5O,OAAO;IACP;IACA;IACA;IACAwH,YAAY,IAAI/H,WAAA,CAAA0I,eAAe,CAChC;IAED,MAAM8G,MAAM,GAAG,MAAMlB,aAAa,CAACmB,OAAO,CAACF,iBAAiB,CAACG,OAAO,EAAE,EAAEnP,OAAO,CAAC;IAEhF,MAAMoP,iBAAiB,GAAG5H,YAAY,EAAEqB,IAAI,CAACoG,MAAM,CAAC,IAAI,IAAAvQ,MAAA,CAAA2Q,WAAW,EAACJ,MAAM,EAAEjP,OAAO,CAAC;IAEpF,IAAI+N,aAAa,CAACpP,WAAA,CAAA2Q,eAAe,CAAC,EAAE;MAClC,IAAI9H,YAAY,IAAI,IAAI,EAAE;QACxB,IAAArI,OAAA,CAAAoQ,wBAAwB,EAACH,iBAAiB,EAAEJ,iBAAiB,CAAC/E,QAAQ,EAAE,EAAE,IAAI,CAAC;MACjF,CAAC,MAAM,IAAImF,iBAAiB,YAAY3P,WAAA,CAAA+P,cAAc,EAAE;QACtDJ,iBAAiB,CAACJ,iBAAiB,GAAGA,iBAAiB;MACzD;IACF;IAEA,OAAOI,iBAAiB;EAC1B;;AAjGF/Q,OAAA,CAAAyP,gBAAA,GAAAA,gBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}