{"ast": null, "code": "import { useEffect as d } from \"react\";\nimport { useLatestValue as a } from './use-latest-value.js';\nfunction s(e, r, n) {\n  let o = a(r);\n  d(() => {\n    function t(i) {\n      o.current(i);\n    }\n    return window.addEventListener(e, t, n), () => window.removeEventListener(e, t, n);\n  }, [e, n]);\n}\nexport { s as useWindowEvent };", "map": {"version": 3, "names": ["useEffect", "d", "useLatestValue", "a", "s", "e", "r", "n", "o", "t", "i", "current", "window", "addEventListener", "removeEventListener", "useWindowEvent"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/hooks/use-window-event.js"], "sourcesContent": ["import{useEffect as d}from\"react\";import{useLatestValue as a}from'./use-latest-value.js';function s(e,r,n){let o=a(r);d(()=>{function t(i){o.current(i)}return window.addEventListener(e,t,n),()=>window.removeEventListener(e,t,n)},[e,n])}export{s as useWindowEvent};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,cAAc,IAAIC,CAAC,QAAK,uBAAuB;AAAC,SAASC,CAACA,CAACC,CAAC,EAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACL,CAAC,CAACG,CAAC,CAAC;EAACL,CAAC,CAAC,MAAI;IAAC,SAASQ,CAACA,CAACC,CAAC,EAAC;MAACF,CAAC,CAACG,OAAO,CAACD,CAAC,CAAC;IAAA;IAAC,OAAOE,MAAM,CAACC,gBAAgB,CAACR,CAAC,EAACI,CAAC,EAACF,CAAC,CAAC,EAAC,MAAIK,MAAM,CAACE,mBAAmB,CAACT,CAAC,EAACI,CAAC,EAACF,CAAC,CAAC;EAAA,CAAC,EAAC,CAACF,CAAC,EAACE,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOH,CAAC,IAAIW,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}