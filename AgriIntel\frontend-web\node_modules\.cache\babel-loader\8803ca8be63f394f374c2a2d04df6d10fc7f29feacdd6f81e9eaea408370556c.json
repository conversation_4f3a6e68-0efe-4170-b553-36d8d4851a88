{"ast": null, "code": "import React from'react';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const ActivityFeed=_ref=>{let{activities=[],maxItems=5}=_ref;const{t}=useTranslation();// Mock data if no activities provided\nconst mockActivities=[{id:'1',type:'vaccination',message:'Cow C001 vaccinated against FMD',timestamp:'2 hours ago',animalId:'C001',priority:'medium'},{id:'2',type:'birth',message:'New calf born to Cow C015',timestamp:'4 hours ago',animalId:'C015',priority:'high'},{id:'3',type:'health',message:'Sheep S023 showing signs of lameness',timestamp:'6 hours ago',animalId:'S023',priority:'high'},{id:'4',type:'feeding',message:'Feed inventory low for cattle section',timestamp:'8 hours ago',priority:'medium'},{id:'5',type:'sale',message:'Goat G012 sold to local buyer',timestamp:'1 day ago',animalId:'G012',priority:'low'}];const displayActivities=activities.length>0?activities:mockActivities;const limitedActivities=displayActivities.slice(0,maxItems);const getActivityColor=type=>{switch(type){case'vaccination':return'bg-green-400';case'birth':return'bg-blue-400';case'sale':return'bg-purple-400';case'health':return'bg-red-400';case'feeding':return'bg-yellow-400';case'breeding':return'bg-pink-400';default:return'bg-gray-400';}};const getPriorityColor=priority=>{switch(priority){case'high':return'border-l-red-500';case'medium':return'border-l-yellow-500';case'low':return'border-l-green-500';default:return'border-l-gray-300';}};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-4\",children:[limitedActivities.map(activity=>/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-start space-x-3 p-3 border-l-4 \".concat(getPriorityColor(activity.priority),\" bg-gray-50 rounded-r-md\"),children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-2 h-2 \".concat(getActivityColor(activity.type),\" rounded-full mt-2 flex-shrink-0\")}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex-1 min-w-0\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-sm text-gray-900\",children:activity.message}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2 mt-1\",children:[/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-gray-500\",children:activity.timestamp}),activity.animalId&&/*#__PURE__*/_jsx(\"span\",{className:\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800\",children:activity.animalId})]})]})]},activity.id)),limitedActivities.length===0&&/*#__PURE__*/_jsx(\"div\",{className:\"text-center py-8 text-gray-500\",children:/*#__PURE__*/_jsx(\"p\",{children:\"No recent activities\"})})]});};export default ActivityFeed;", "map": {"version": 3, "names": ["React", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "ActivityFeed", "_ref", "activities", "maxItems", "t", "mockActivities", "id", "type", "message", "timestamp", "animalId", "priority", "displayActivities", "length", "limitedActivities", "slice", "getActivityColor", "getPriorityColor", "className", "children", "map", "activity", "concat"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/components/dashboard/ActivityFeed.tsx"], "sourcesContent": ["import React from 'react';\nimport { useTranslation } from 'react-i18next';\n\ninterface Activity {\n  id: string;\n  type: 'vaccination' | 'birth' | 'sale' | 'health' | 'feeding' | 'breeding';\n  message: string;\n  timestamp: string;\n  animalId?: string;\n  priority?: 'low' | 'medium' | 'high';\n}\n\ninterface ActivityFeedProps {\n  activities?: Activity[];\n  maxItems?: number;\n}\n\nconst ActivityFeed: React.FC<ActivityFeedProps> = ({ \n  activities = [], \n  maxItems = 5 \n}) => {\n  const { t } = useTranslation();\n\n  // Mock data if no activities provided\n  const mockActivities: Activity[] = [\n    {\n      id: '1',\n      type: 'vaccination',\n      message: 'Cow C001 vaccinated against FMD',\n      timestamp: '2 hours ago',\n      animalId: 'C001',\n      priority: 'medium'\n    },\n    {\n      id: '2',\n      type: 'birth',\n      message: 'New calf born to Cow C015',\n      timestamp: '4 hours ago',\n      animalId: 'C015',\n      priority: 'high'\n    },\n    {\n      id: '3',\n      type: 'health',\n      message: 'Sheep S023 showing signs of lameness',\n      timestamp: '6 hours ago',\n      animalId: 'S023',\n      priority: 'high'\n    },\n    {\n      id: '4',\n      type: 'feeding',\n      message: 'Feed inventory low for cattle section',\n      timestamp: '8 hours ago',\n      priority: 'medium'\n    },\n    {\n      id: '5',\n      type: 'sale',\n      message: 'Goat G012 sold to local buyer',\n      timestamp: '1 day ago',\n      animalId: 'G012',\n      priority: 'low'\n    }\n  ];\n\n  const displayActivities = activities.length > 0 ? activities : mockActivities;\n  const limitedActivities = displayActivities.slice(0, maxItems);\n\n  const getActivityColor = (type: Activity['type']) => {\n    switch (type) {\n      case 'vaccination':\n        return 'bg-green-400';\n      case 'birth':\n        return 'bg-blue-400';\n      case 'sale':\n        return 'bg-purple-400';\n      case 'health':\n        return 'bg-red-400';\n      case 'feeding':\n        return 'bg-yellow-400';\n      case 'breeding':\n        return 'bg-pink-400';\n      default:\n        return 'bg-gray-400';\n    }\n  };\n\n  const getPriorityColor = (priority?: Activity['priority']) => {\n    switch (priority) {\n      case 'high':\n        return 'border-l-red-500';\n      case 'medium':\n        return 'border-l-yellow-500';\n      case 'low':\n        return 'border-l-green-500';\n      default:\n        return 'border-l-gray-300';\n    }\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      {limitedActivities.map((activity) => (\n        <div \n          key={activity.id} \n          className={`flex items-start space-x-3 p-3 border-l-4 ${getPriorityColor(activity.priority)} bg-gray-50 rounded-r-md`}\n        >\n          <div className={`w-2 h-2 ${getActivityColor(activity.type)} rounded-full mt-2 flex-shrink-0`}></div>\n          <div className=\"flex-1 min-w-0\">\n            <p className=\"text-sm text-gray-900\">{activity.message}</p>\n            <div className=\"flex items-center space-x-2 mt-1\">\n              <p className=\"text-xs text-gray-500\">{activity.timestamp}</p>\n              {activity.animalId && (\n                <span className=\"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800\">\n                  {activity.animalId}\n                </span>\n              )}\n            </div>\n          </div>\n        </div>\n      ))}\n      \n      {limitedActivities.length === 0 && (\n        <div className=\"text-center py-8 text-gray-500\">\n          <p>No recent activities</p>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport default ActivityFeed;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAgB/C,KAAM,CAAAC,YAAyC,CAAGC,IAAA,EAG5C,IAH6C,CACjDC,UAAU,CAAG,EAAE,CACfC,QAAQ,CAAG,CACb,CAAC,CAAAF,IAAA,CACC,KAAM,CAAEG,CAAE,CAAC,CAAGT,cAAc,CAAC,CAAC,CAE9B;AACA,KAAM,CAAAU,cAA0B,CAAG,CACjC,CACEC,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,aAAa,CACnBC,OAAO,CAAE,iCAAiC,CAC1CC,SAAS,CAAE,aAAa,CACxBC,QAAQ,CAAE,MAAM,CAChBC,QAAQ,CAAE,QACZ,CAAC,CACD,CACEL,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,OAAO,CACbC,OAAO,CAAE,2BAA2B,CACpCC,SAAS,CAAE,aAAa,CACxBC,QAAQ,CAAE,MAAM,CAChBC,QAAQ,CAAE,MACZ,CAAC,CACD,CACEL,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,QAAQ,CACdC,OAAO,CAAE,sCAAsC,CAC/CC,SAAS,CAAE,aAAa,CACxBC,QAAQ,CAAE,MAAM,CAChBC,QAAQ,CAAE,MACZ,CAAC,CACD,CACEL,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,SAAS,CACfC,OAAO,CAAE,uCAAuC,CAChDC,SAAS,CAAE,aAAa,CACxBE,QAAQ,CAAE,QACZ,CAAC,CACD,CACEL,EAAE,CAAE,GAAG,CACPC,IAAI,CAAE,MAAM,CACZC,OAAO,CAAE,+BAA+B,CACxCC,SAAS,CAAE,WAAW,CACtBC,QAAQ,CAAE,MAAM,CAChBC,QAAQ,CAAE,KACZ,CAAC,CACF,CAED,KAAM,CAAAC,iBAAiB,CAAGV,UAAU,CAACW,MAAM,CAAG,CAAC,CAAGX,UAAU,CAAGG,cAAc,CAC7E,KAAM,CAAAS,iBAAiB,CAAGF,iBAAiB,CAACG,KAAK,CAAC,CAAC,CAAEZ,QAAQ,CAAC,CAE9D,KAAM,CAAAa,gBAAgB,CAAIT,IAAsB,EAAK,CACnD,OAAQA,IAAI,EACV,IAAK,aAAa,CAChB,MAAO,cAAc,CACvB,IAAK,OAAO,CACV,MAAO,aAAa,CACtB,IAAK,MAAM,CACT,MAAO,eAAe,CACxB,IAAK,QAAQ,CACX,MAAO,YAAY,CACrB,IAAK,SAAS,CACZ,MAAO,eAAe,CACxB,IAAK,UAAU,CACb,MAAO,aAAa,CACtB,QACE,MAAO,aAAa,CACxB,CACF,CAAC,CAED,KAAM,CAAAU,gBAAgB,CAAIN,QAA+B,EAAK,CAC5D,OAAQA,QAAQ,EACd,IAAK,MAAM,CACT,MAAO,kBAAkB,CAC3B,IAAK,QAAQ,CACX,MAAO,qBAAqB,CAC9B,IAAK,KAAK,CACR,MAAO,oBAAoB,CAC7B,QACE,MAAO,mBAAmB,CAC9B,CACF,CAAC,CAED,mBACEZ,KAAA,QAAKmB,SAAS,CAAC,WAAW,CAAAC,QAAA,EACvBL,iBAAiB,CAACM,GAAG,CAAEC,QAAQ,eAC9BtB,KAAA,QAEEmB,SAAS,8CAAAI,MAAA,CAA+CL,gBAAgB,CAACI,QAAQ,CAACV,QAAQ,CAAC,4BAA2B,CAAAQ,QAAA,eAEtHtB,IAAA,QAAKqB,SAAS,YAAAI,MAAA,CAAaN,gBAAgB,CAACK,QAAQ,CAACd,IAAI,CAAC,oCAAmC,CAAM,CAAC,cACpGR,KAAA,QAAKmB,SAAS,CAAC,gBAAgB,CAAAC,QAAA,eAC7BtB,IAAA,MAAGqB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEE,QAAQ,CAACb,OAAO,CAAI,CAAC,cAC3DT,KAAA,QAAKmB,SAAS,CAAC,kCAAkC,CAAAC,QAAA,eAC/CtB,IAAA,MAAGqB,SAAS,CAAC,uBAAuB,CAAAC,QAAA,CAAEE,QAAQ,CAACZ,SAAS,CAAI,CAAC,CAC5DY,QAAQ,CAACX,QAAQ,eAChBb,IAAA,SAAMqB,SAAS,CAAC,4FAA4F,CAAAC,QAAA,CACzGE,QAAQ,CAACX,QAAQ,CACd,CACP,EACE,CAAC,EACH,CAAC,GAdDW,QAAQ,CAACf,EAeX,CACN,CAAC,CAEDQ,iBAAiB,CAACD,MAAM,GAAK,CAAC,eAC7BhB,IAAA,QAAKqB,SAAS,CAAC,gCAAgC,CAAAC,QAAA,cAC7CtB,IAAA,MAAAsB,QAAA,CAAG,sBAAoB,CAAG,CAAC,CACxB,CACN,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAnB,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}