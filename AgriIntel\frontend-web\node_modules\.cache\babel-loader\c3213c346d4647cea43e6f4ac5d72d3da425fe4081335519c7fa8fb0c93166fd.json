{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import React,{useState}from'react';import{useDispatch,useSelector}from'react-redux';import{Link,useNavigate}from'react-router-dom';import{useTranslation}from'react-i18next';import{EyeIcon,EyeSlashIcon}from'@heroicons/react/24/outline';import{loginUser,clearError}from'../../store/slices/authSlice';import LoadingSpinner from'../../components/common/LoadingSpinner';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LoginPage=()=>{const dispatch=useDispatch();const navigate=useNavigate();const{t}=useTranslation();const{isLoading,error}=useSelector(state=>state.auth);const[formData,setFormData]=useState({username:'',password:''});const[showPassword,setShowPassword]=useState(false);const[rememberMe,setRememberMe]=useState(false);const handleChange=e=>{const{name,value}=e.target;setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[name]:value}));// Clear error when user starts typing\nif(error){dispatch(clearError());}};const handleSubmit=async e=>{e.preventDefault();try{const result=await dispatch(loginUser(formData));if(loginUser.fulfilled.match(result)){navigate('/dashboard');}}catch(error){// Error is handled by the slice\n}};return/*#__PURE__*/_jsxs(\"div\",{className:\"space-y-6\",children:[/*#__PURE__*/_jsxs(\"form\",{className:\"space-y-6\",onSubmit:handleSubmit,children:[error&&/*#__PURE__*/_jsx(\"div\",{className:\"rounded-md bg-red-50 p-4\",children:/*#__PURE__*/_jsx(\"div\",{className:\"text-sm text-red-700\",children:error})}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"username\",className:\"form-label\",children:t('auth.username')}),/*#__PURE__*/_jsx(\"input\",{id:\"username\",name:\"username\",type:\"text\",autoComplete:\"username\",required:true,className:\"form-input\",placeholder:t('auth.username'),value:formData.username,onChange:handleChange})]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"label\",{htmlFor:\"password\",className:\"form-label\",children:t('auth.password')}),/*#__PURE__*/_jsxs(\"div\",{className:\"relative\",children:[/*#__PURE__*/_jsx(\"input\",{id:\"password\",name:\"password\",type:showPassword?'text':'password',autoComplete:\"current-password\",required:true,className:\"form-input pr-10\",placeholder:t('auth.password'),value:formData.password,onChange:handleChange}),/*#__PURE__*/_jsx(\"button\",{type:\"button\",className:\"absolute inset-y-0 right-0 pr-3 flex items-center\",onClick:()=>setShowPassword(!showPassword),children:showPassword?/*#__PURE__*/_jsx(EyeSlashIcon,{className:\"h-5 w-5 text-gray-400\"}):/*#__PURE__*/_jsx(EyeIcon,{className:\"h-5 w-5 text-gray-400\"})})]})]}),/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center justify-between\",children:[/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center\",children:[/*#__PURE__*/_jsx(\"input\",{id:\"remember-me\",name:\"remember-me\",type:\"checkbox\",className:\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\",checked:rememberMe,onChange:e=>setRememberMe(e.target.checked)}),/*#__PURE__*/_jsx(\"label\",{htmlFor:\"remember-me\",className:\"ml-2 block text-sm text-gray-900\",children:\"Remember me\"})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-sm\",children:/*#__PURE__*/_jsx(\"a\",{href:\"#\",className:\"font-medium text-primary-600 hover:text-primary-500\",children:t('auth.forgotPassword')})})]}),/*#__PURE__*/_jsx(\"div\",{children:/*#__PURE__*/_jsx(\"button\",{type:\"submit\",disabled:isLoading,className:\"w-full btn-primary flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\",children:isLoading?/*#__PURE__*/_jsx(LoadingSpinner,{size:\"small\",color:\"white\"}):t('auth.signIn')})})]}),/*#__PURE__*/_jsx(\"div\",{className:\"text-center\",children:/*#__PURE__*/_jsxs(\"p\",{className:\"text-sm text-gray-600\",children:[t('auth.dontHaveAccount'),' ',/*#__PURE__*/_jsx(Link,{to:\"/register\",className:\"font-medium text-primary-600 hover:text-primary-500\",children:t('auth.createAccount')})]})}),/*#__PURE__*/_jsxs(\"div\",{className:\"mt-6 p-4 bg-blue-50 rounded-md\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-sm font-medium text-blue-800 mb-2\",children:\"Demo Credentials:\"}),/*#__PURE__*/_jsxs(\"div\",{className:\"text-xs text-blue-700 space-y-1\",children:[/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Admin:\"}),\" admin / Admin@123\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Manager:\"}),\" manager / Manager@123\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Staff:\"}),\" staff / Staff@123\"]}),/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"strong\",{children:\"Veterinarian:\"}),\" vet / Vet@123\"]})]})]})]});};export default LoginPage;", "map": {"version": 3, "names": ["React", "useState", "useDispatch", "useSelector", "Link", "useNavigate", "useTranslation", "EyeIcon", "EyeSlashIcon", "loginUser", "clearError", "LoadingSpinner", "jsx", "_jsx", "jsxs", "_jsxs", "LoginPage", "dispatch", "navigate", "t", "isLoading", "error", "state", "auth", "formData", "setFormData", "username", "password", "showPassword", "setShowPassword", "rememberMe", "setRememberMe", "handleChange", "e", "name", "value", "target", "prev", "_objectSpread", "handleSubmit", "preventDefault", "result", "fulfilled", "match", "className", "children", "onSubmit", "htmlFor", "id", "type", "autoComplete", "required", "placeholder", "onChange", "onClick", "checked", "href", "disabled", "size", "color", "to"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/pages/auth/LoginPage.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { Link, useNavigate } from 'react-router-dom';\nimport { useTranslation } from 'react-i18next';\nimport { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline';\n\nimport { AppDispatch, RootState } from '../../store/store';\nimport { loginUser, clearError } from '../../store/slices/authSlice';\nimport LoadingSpinner from '../../components/common/LoadingSpinner';\n\nconst LoginPage: React.FC = () => {\n  const dispatch = useDispatch<AppDispatch>();\n  const navigate = useNavigate();\n  const { t } = useTranslation();\n  \n  const { isLoading, error } = useSelector((state: RootState) => state.auth);\n  \n  const [formData, setFormData] = useState({\n    username: '',\n    password: '',\n  });\n  const [showPassword, setShowPassword] = useState(false);\n  const [rememberMe, setRememberMe] = useState(false);\n\n  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const { name, value } = e.target;\n    setFormData(prev => ({\n      ...prev,\n      [name]: value,\n    }));\n    \n    // Clear error when user starts typing\n    if (error) {\n      dispatch(clearError());\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    \n    try {\n      const result = await dispatch(loginUser(formData));\n      if (loginUser.fulfilled.match(result)) {\n        navigate('/dashboard');\n      }\n    } catch (error) {\n      // Error is handled by the slice\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <form className=\"space-y-6\" onSubmit={handleSubmit}>\n        {error && (\n          <div className=\"rounded-md bg-red-50 p-4\">\n            <div className=\"text-sm text-red-700\">{error}</div>\n          </div>\n        )}\n\n        <div>\n          <label htmlFor=\"username\" className=\"form-label\">\n            {t('auth.username')}\n          </label>\n          <input\n            id=\"username\"\n            name=\"username\"\n            type=\"text\"\n            autoComplete=\"username\"\n            required\n            className=\"form-input\"\n            placeholder={t('auth.username')}\n            value={formData.username}\n            onChange={handleChange}\n          />\n        </div>\n\n        <div>\n          <label htmlFor=\"password\" className=\"form-label\">\n            {t('auth.password')}\n          </label>\n          <div className=\"relative\">\n            <input\n              id=\"password\"\n              name=\"password\"\n              type={showPassword ? 'text' : 'password'}\n              autoComplete=\"current-password\"\n              required\n              className=\"form-input pr-10\"\n              placeholder={t('auth.password')}\n              value={formData.password}\n              onChange={handleChange}\n            />\n            <button\n              type=\"button\"\n              className=\"absolute inset-y-0 right-0 pr-3 flex items-center\"\n              onClick={() => setShowPassword(!showPassword)}\n            >\n              {showPassword ? (\n                <EyeSlashIcon className=\"h-5 w-5 text-gray-400\" />\n              ) : (\n                <EyeIcon className=\"h-5 w-5 text-gray-400\" />\n              )}\n            </button>\n          </div>\n        </div>\n\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center\">\n            <input\n              id=\"remember-me\"\n              name=\"remember-me\"\n              type=\"checkbox\"\n              className=\"h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded\"\n              checked={rememberMe}\n              onChange={(e) => setRememberMe(e.target.checked)}\n            />\n            <label htmlFor=\"remember-me\" className=\"ml-2 block text-sm text-gray-900\">\n              Remember me\n            </label>\n          </div>\n\n          <div className=\"text-sm\">\n            <a\n              href=\"#\"\n              className=\"font-medium text-primary-600 hover:text-primary-500\"\n            >\n              {t('auth.forgotPassword')}\n            </a>\n          </div>\n        </div>\n\n        <div>\n          <button\n            type=\"submit\"\n            disabled={isLoading}\n            className=\"w-full btn-primary flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 disabled:opacity-50 disabled:cursor-not-allowed\"\n          >\n            {isLoading ? (\n              <LoadingSpinner size=\"small\" color=\"white\" />\n            ) : (\n              t('auth.signIn')\n            )}\n          </button>\n        </div>\n      </form>\n\n      <div className=\"text-center\">\n        <p className=\"text-sm text-gray-600\">\n          {t('auth.dontHaveAccount')}{' '}\n          <Link\n            to=\"/register\"\n            className=\"font-medium text-primary-600 hover:text-primary-500\"\n          >\n            {t('auth.createAccount')}\n          </Link>\n        </p>\n      </div>\n\n      {/* Demo Credentials */}\n      <div className=\"mt-6 p-4 bg-blue-50 rounded-md\">\n        <h3 className=\"text-sm font-medium text-blue-800 mb-2\">Demo Credentials:</h3>\n        <div className=\"text-xs text-blue-700 space-y-1\">\n          <div><strong>Admin:</strong> admin / Admin@123</div>\n          <div><strong>Manager:</strong> manager / Manager@123</div>\n          <div><strong>Staff:</strong> staff / Staff@123</div>\n          <div><strong>Veterinarian:</strong> vet / Vet@123</div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default LoginPage;\n"], "mappings": "gJAAA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,KAAQ,OAAO,CACvC,OAASC,WAAW,CAAEC,WAAW,KAAQ,aAAa,CACtD,OAASC,IAAI,CAAEC,WAAW,KAAQ,kBAAkB,CACpD,OAASC,cAAc,KAAQ,eAAe,CAC9C,OAASC,OAAO,CAAEC,YAAY,KAAQ,6BAA6B,CAGnE,OAASC,SAAS,CAAEC,UAAU,KAAQ,8BAA8B,CACpE,MAAO,CAAAC,cAAc,KAAM,wCAAwC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAEpE,KAAM,CAAAC,SAAmB,CAAGA,CAAA,GAAM,CAChC,KAAM,CAAAC,QAAQ,CAAGf,WAAW,CAAc,CAAC,CAC3C,KAAM,CAAAgB,QAAQ,CAAGb,WAAW,CAAC,CAAC,CAC9B,KAAM,CAAEc,CAAE,CAAC,CAAGb,cAAc,CAAC,CAAC,CAE9B,KAAM,CAAEc,SAAS,CAAEC,KAAM,CAAC,CAAGlB,WAAW,CAAEmB,KAAgB,EAAKA,KAAK,CAACC,IAAI,CAAC,CAE1E,KAAM,CAACC,QAAQ,CAAEC,WAAW,CAAC,CAAGxB,QAAQ,CAAC,CACvCyB,QAAQ,CAAE,EAAE,CACZC,QAAQ,CAAE,EACZ,CAAC,CAAC,CACF,KAAM,CAACC,YAAY,CAAEC,eAAe,CAAC,CAAG5B,QAAQ,CAAC,KAAK,CAAC,CACvD,KAAM,CAAC6B,UAAU,CAAEC,aAAa,CAAC,CAAG9B,QAAQ,CAAC,KAAK,CAAC,CAEnD,KAAM,CAAA+B,YAAY,CAAIC,CAAsC,EAAK,CAC/D,KAAM,CAAEC,IAAI,CAAEC,KAAM,CAAC,CAAGF,CAAC,CAACG,MAAM,CAChCX,WAAW,CAACY,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACXD,IAAI,MACP,CAACH,IAAI,EAAGC,KAAK,EACb,CAAC,CAEH;AACA,GAAId,KAAK,CAAE,CACTJ,QAAQ,CAACP,UAAU,CAAC,CAAC,CAAC,CACxB,CACF,CAAC,CAED,KAAM,CAAA6B,YAAY,CAAG,KAAO,CAAAN,CAAkB,EAAK,CACjDA,CAAC,CAACO,cAAc,CAAC,CAAC,CAElB,GAAI,CACF,KAAM,CAAAC,MAAM,CAAG,KAAM,CAAAxB,QAAQ,CAACR,SAAS,CAACe,QAAQ,CAAC,CAAC,CAClD,GAAIf,SAAS,CAACiC,SAAS,CAACC,KAAK,CAACF,MAAM,CAAC,CAAE,CACrCvB,QAAQ,CAAC,YAAY,CAAC,CACxB,CACF,CAAE,MAAOG,KAAK,CAAE,CACd;AAAA,CAEJ,CAAC,CAED,mBACEN,KAAA,QAAK6B,SAAS,CAAC,WAAW,CAAAC,QAAA,eACxB9B,KAAA,SAAM6B,SAAS,CAAC,WAAW,CAACE,QAAQ,CAAEP,YAAa,CAAAM,QAAA,EAChDxB,KAAK,eACJR,IAAA,QAAK+B,SAAS,CAAC,0BAA0B,CAAAC,QAAA,cACvChC,IAAA,QAAK+B,SAAS,CAAC,sBAAsB,CAAAC,QAAA,CAAExB,KAAK,CAAM,CAAC,CAChD,CACN,cAEDN,KAAA,QAAA8B,QAAA,eACEhC,IAAA,UAAOkC,OAAO,CAAC,UAAU,CAACH,SAAS,CAAC,YAAY,CAAAC,QAAA,CAC7C1B,CAAC,CAAC,eAAe,CAAC,CACd,CAAC,cACRN,IAAA,UACEmC,EAAE,CAAC,UAAU,CACbd,IAAI,CAAC,UAAU,CACfe,IAAI,CAAC,MAAM,CACXC,YAAY,CAAC,UAAU,CACvBC,QAAQ,MACRP,SAAS,CAAC,YAAY,CACtBQ,WAAW,CAAEjC,CAAC,CAAC,eAAe,CAAE,CAChCgB,KAAK,CAAEX,QAAQ,CAACE,QAAS,CACzB2B,QAAQ,CAAErB,YAAa,CACxB,CAAC,EACC,CAAC,cAENjB,KAAA,QAAA8B,QAAA,eACEhC,IAAA,UAAOkC,OAAO,CAAC,UAAU,CAACH,SAAS,CAAC,YAAY,CAAAC,QAAA,CAC7C1B,CAAC,CAAC,eAAe,CAAC,CACd,CAAC,cACRJ,KAAA,QAAK6B,SAAS,CAAC,UAAU,CAAAC,QAAA,eACvBhC,IAAA,UACEmC,EAAE,CAAC,UAAU,CACbd,IAAI,CAAC,UAAU,CACfe,IAAI,CAAErB,YAAY,CAAG,MAAM,CAAG,UAAW,CACzCsB,YAAY,CAAC,kBAAkB,CAC/BC,QAAQ,MACRP,SAAS,CAAC,kBAAkB,CAC5BQ,WAAW,CAAEjC,CAAC,CAAC,eAAe,CAAE,CAChCgB,KAAK,CAAEX,QAAQ,CAACG,QAAS,CACzB0B,QAAQ,CAAErB,YAAa,CACxB,CAAC,cACFnB,IAAA,WACEoC,IAAI,CAAC,QAAQ,CACbL,SAAS,CAAC,mDAAmD,CAC7DU,OAAO,CAAEA,CAAA,GAAMzB,eAAe,CAAC,CAACD,YAAY,CAAE,CAAAiB,QAAA,CAE7CjB,YAAY,cACXf,IAAA,CAACL,YAAY,EAACoC,SAAS,CAAC,uBAAuB,CAAE,CAAC,cAElD/B,IAAA,CAACN,OAAO,EAACqC,SAAS,CAAC,uBAAuB,CAAE,CAC7C,CACK,CAAC,EACN,CAAC,EACH,CAAC,cAEN7B,KAAA,QAAK6B,SAAS,CAAC,mCAAmC,CAAAC,QAAA,eAChD9B,KAAA,QAAK6B,SAAS,CAAC,mBAAmB,CAAAC,QAAA,eAChChC,IAAA,UACEmC,EAAE,CAAC,aAAa,CAChBd,IAAI,CAAC,aAAa,CAClBe,IAAI,CAAC,UAAU,CACfL,SAAS,CAAC,yEAAyE,CACnFW,OAAO,CAAEzB,UAAW,CACpBuB,QAAQ,CAAGpB,CAAC,EAAKF,aAAa,CAACE,CAAC,CAACG,MAAM,CAACmB,OAAO,CAAE,CAClD,CAAC,cACF1C,IAAA,UAAOkC,OAAO,CAAC,aAAa,CAACH,SAAS,CAAC,kCAAkC,CAAAC,QAAA,CAAC,aAE1E,CAAO,CAAC,EACL,CAAC,cAENhC,IAAA,QAAK+B,SAAS,CAAC,SAAS,CAAAC,QAAA,cACtBhC,IAAA,MACE2C,IAAI,CAAC,GAAG,CACRZ,SAAS,CAAC,qDAAqD,CAAAC,QAAA,CAE9D1B,CAAC,CAAC,qBAAqB,CAAC,CACxB,CAAC,CACD,CAAC,EACH,CAAC,cAENN,IAAA,QAAAgC,QAAA,cACEhC,IAAA,WACEoC,IAAI,CAAC,QAAQ,CACbQ,QAAQ,CAAErC,SAAU,CACpBwB,SAAS,CAAC,+RAA+R,CAAAC,QAAA,CAExSzB,SAAS,cACRP,IAAA,CAACF,cAAc,EAAC+C,IAAI,CAAC,OAAO,CAACC,KAAK,CAAC,OAAO,CAAE,CAAC,CAE7CxC,CAAC,CAAC,aAAa,CAChB,CACK,CAAC,CACN,CAAC,EACF,CAAC,cAEPN,IAAA,QAAK+B,SAAS,CAAC,aAAa,CAAAC,QAAA,cAC1B9B,KAAA,MAAG6B,SAAS,CAAC,uBAAuB,CAAAC,QAAA,EACjC1B,CAAC,CAAC,sBAAsB,CAAC,CAAE,GAAG,cAC/BN,IAAA,CAACT,IAAI,EACHwD,EAAE,CAAC,WAAW,CACdhB,SAAS,CAAC,qDAAqD,CAAAC,QAAA,CAE9D1B,CAAC,CAAC,oBAAoB,CAAC,CACpB,CAAC,EACN,CAAC,CACD,CAAC,cAGNJ,KAAA,QAAK6B,SAAS,CAAC,gCAAgC,CAAAC,QAAA,eAC7ChC,IAAA,OAAI+B,SAAS,CAAC,wCAAwC,CAAAC,QAAA,CAAC,mBAAiB,CAAI,CAAC,cAC7E9B,KAAA,QAAK6B,SAAS,CAAC,iCAAiC,CAAAC,QAAA,eAC9C9B,KAAA,QAAA8B,QAAA,eAAKhC,IAAA,WAAAgC,QAAA,CAAQ,QAAM,CAAQ,CAAC,qBAAkB,EAAK,CAAC,cACpD9B,KAAA,QAAA8B,QAAA,eAAKhC,IAAA,WAAAgC,QAAA,CAAQ,UAAQ,CAAQ,CAAC,yBAAsB,EAAK,CAAC,cAC1D9B,KAAA,QAAA8B,QAAA,eAAKhC,IAAA,WAAAgC,QAAA,CAAQ,QAAM,CAAQ,CAAC,qBAAkB,EAAK,CAAC,cACpD9B,KAAA,QAAA8B,QAAA,eAAKhC,IAAA,WAAAgC,QAAA,CAAQ,eAAa,CAAQ,CAAC,iBAAc,EAAK,CAAC,EACpD,CAAC,EACH,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAA7B,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}