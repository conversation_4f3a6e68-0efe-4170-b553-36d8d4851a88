{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport { unescape } from './unescape.js';\nlet defaultOptions = {\n  bindI18n: 'languageChanged',\n  bindI18nStore: '',\n  transEmptyNodeValue: '',\n  transSupportBasicHtmlNodes: true,\n  transWrapTextNodes: '',\n  transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],\n  useSuspense: true,\n  unescape\n};\nexport function setDefaults() {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  defaultOptions = _objectSpread(_objectSpread({}, defaultOptions), options);\n}\nexport function getDefaults() {\n  return defaultOptions;\n}", "map": {"version": 3, "names": ["unescape", "defaultOptions", "bindI18n", "bindI18nStore", "transEmptyNodeValue", "transSupportBasicHtmlNodes", "transWrapTextNodes", "transKeepBasicHtmlNodesFor", "useSuspense", "setDefaults", "options", "arguments", "length", "undefined", "_objectSpread", "getDefaults"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-i18next/dist/es/defaults.js"], "sourcesContent": ["import { unescape } from './unescape.js';\nlet defaultOptions = {\n  bindI18n: 'languageChanged',\n  bindI18nStore: '',\n  transEmptyNodeValue: '',\n  transSupportBasicHtmlNodes: true,\n  transWrapTextNodes: '',\n  transKeepBasicHtmlNodesFor: ['br', 'strong', 'i', 'p'],\n  useSuspense: true,\n  unescape\n};\nexport function setDefaults() {\n  let options = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : {};\n  defaultOptions = {\n    ...defaultOptions,\n    ...options\n  };\n}\nexport function getDefaults() {\n  return defaultOptions;\n}"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,IAAIC,cAAc,GAAG;EACnBC,QAAQ,EAAE,iBAAiB;EAC3BC,aAAa,EAAE,EAAE;EACjBC,mBAAmB,EAAE,EAAE;EACvBC,0BAA0B,EAAE,IAAI;EAChCC,kBAAkB,EAAE,EAAE;EACtBC,0BAA0B,EAAE,CAAC,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,CAAC;EACtDC,WAAW,EAAE,IAAI;EACjBR;AACF,CAAC;AACD,OAAO,SAASS,WAAWA,CAAA,EAAG;EAC5B,IAAIC,OAAO,GAAGC,SAAS,CAACC,MAAM,GAAG,CAAC,IAAID,SAAS,CAAC,CAAC,CAAC,KAAKE,SAAS,GAAGF,SAAS,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC;EACpFV,cAAc,GAAAa,aAAA,CAAAA,aAAA,KACTb,cAAc,GACdS,OAAO,CACX;AACH;AACA,OAAO,SAASK,WAAWA,CAAA,EAAG;EAC5B,OAAOd,cAAc;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}