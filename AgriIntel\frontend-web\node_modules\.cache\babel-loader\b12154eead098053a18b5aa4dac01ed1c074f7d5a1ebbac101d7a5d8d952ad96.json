{"ast": null, "code": "import getWeekYear from \"../getWeekYear/index.js\";\nimport startOfWeek from \"../startOfWeek/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\n/**\n * @name startOfWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Return the start of a local week-numbering year for the given date.\n *\n * @description\n * Return the start of a local week-numbering year.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#Week_numbering\n *\n * @param {Date|Number} date - the original date\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @param {1|2|3|4|5|6|7} [options.firstWeekContainsDate=1] - the day of January, which is always in the first week of the year\n * @returns {Date} the start of a week-numbering year\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.firstWeekContainsDate` must be between 1 and 7\n *\n * @example\n * // The start of an a week-numbering year for 2 July 2005 with default settings:\n * const result = startOfWeekYear(new Date(2005, 6, 2))\n * //=> Sun Dec 26 2004 00:00:00\n *\n * @example\n * // The start of a week-numbering year for 2 July 2005\n * // if Monday is the first day of week\n * // and 4 January is always in the first week of the year:\n * const result = startOfWeekYear(new Date(2005, 6, 2), {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> Mon Jan 03 2005 00:00:00\n */\nexport default function startOfWeekYear(dirtyDate, options) {\n  var _ref, _ref2, _ref3, _options$firstWeekCon, _options$locale, _options$locale$optio, _defaultOptions$local, _defaultOptions$local2;\n  requiredArgs(1, arguments);\n  var defaultOptions = getDefaultOptions();\n  var firstWeekContainsDate = toInteger((_ref = (_ref2 = (_ref3 = (_options$firstWeekCon = options === null || options === void 0 ? void 0 : options.firstWeekContainsDate) !== null && _options$firstWeekCon !== void 0 ? _options$firstWeekCon : options === null || options === void 0 ? void 0 : (_options$locale = options.locale) === null || _options$locale === void 0 ? void 0 : (_options$locale$optio = _options$locale.options) === null || _options$locale$optio === void 0 ? void 0 : _options$locale$optio.firstWeekContainsDate) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions.firstWeekContainsDate) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.firstWeekContainsDate) !== null && _ref !== void 0 ? _ref : 1);\n  var year = getWeekYear(dirtyDate, options);\n  var firstWeek = new Date(0);\n  firstWeek.setFullYear(year, 0, firstWeekContainsDate);\n  firstWeek.setHours(0, 0, 0, 0);\n  var date = startOfWeek(firstWeek, options);\n  return date;\n}", "map": {"version": 3, "names": ["getWeekYear", "startOfWeek", "toInteger", "requiredArgs", "getDefaultOptions", "startOfWeekYear", "dirtyDate", "options", "_ref", "_ref2", "_ref3", "_options$firstWeekCon", "_options$locale", "_options$locale$optio", "_defaultOptions$local", "_defaultOptions$local2", "arguments", "defaultOptions", "firstWeekContainsDate", "locale", "year", "firstWeek", "Date", "setFullYear", "setHours", "date"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/date-fns/esm/startOfWeekYear/index.js"], "sourcesContent": ["import getWeekYear from \"../getWeekYear/index.js\";\nimport startOfWeek from \"../startOfWeek/index.js\";\nimport toInteger from \"../_lib/toInteger/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { getDefaultOptions } from \"../_lib/defaultOptions/index.js\";\n/**\n * @name startOfWeekYear\n * @category Week-Numbering Year Helpers\n * @summary Return the start of a local week-numbering year for the given date.\n *\n * @description\n * Return the start of a local week-numbering year.\n * The exact calculation depends on the values of\n * `options.weekStartsOn` (which is the index of the first day of the week)\n * and `options.firstWeekContainsDate` (which is the day of January, which is always in\n * the first week of the week-numbering year)\n *\n * Week numbering: https://en.wikipedia.org/wiki/Week#Week_numbering\n *\n * @param {Date|Number} date - the original date\n * @param {Object} [options] - an object with options.\n * @param {Locale} [options.locale=defaultLocale] - the locale object. See [Locale]{@link https://date-fns.org/docs/Locale}\n * @param {0|1|2|3|4|5|6} [options.weekStartsOn=0] - the index of the first day of the week (0 - Sunday)\n * @param {1|2|3|4|5|6|7} [options.firstWeekContainsDate=1] - the day of January, which is always in the first week of the year\n * @returns {Date} the start of a week-numbering year\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `options.weekStartsOn` must be between 0 and 6\n * @throws {RangeError} `options.firstWeekContainsDate` must be between 1 and 7\n *\n * @example\n * // The start of an a week-numbering year for 2 July 2005 with default settings:\n * const result = startOfWeekYear(new Date(2005, 6, 2))\n * //=> Sun Dec 26 2004 00:00:00\n *\n * @example\n * // The start of a week-numbering year for 2 July 2005\n * // if Monday is the first day of week\n * // and 4 January is always in the first week of the year:\n * const result = startOfWeekYear(new Date(2005, 6, 2), {\n *   weekStartsOn: 1,\n *   firstWeekContainsDate: 4\n * })\n * //=> Mon Jan 03 2005 00:00:00\n */\nexport default function startOfWeekYear(dirtyDate, options) {\n  var _ref, _ref2, _ref3, _options$firstWeekCon, _options$locale, _options$locale$optio, _defaultOptions$local, _defaultOptions$local2;\n  requiredArgs(1, arguments);\n  var defaultOptions = getDefaultOptions();\n  var firstWeekContainsDate = toInteger((_ref = (_ref2 = (_ref3 = (_options$firstWeekCon = options === null || options === void 0 ? void 0 : options.firstWeekContainsDate) !== null && _options$firstWeekCon !== void 0 ? _options$firstWeekCon : options === null || options === void 0 ? void 0 : (_options$locale = options.locale) === null || _options$locale === void 0 ? void 0 : (_options$locale$optio = _options$locale.options) === null || _options$locale$optio === void 0 ? void 0 : _options$locale$optio.firstWeekContainsDate) !== null && _ref3 !== void 0 ? _ref3 : defaultOptions.firstWeekContainsDate) !== null && _ref2 !== void 0 ? _ref2 : (_defaultOptions$local = defaultOptions.locale) === null || _defaultOptions$local === void 0 ? void 0 : (_defaultOptions$local2 = _defaultOptions$local.options) === null || _defaultOptions$local2 === void 0 ? void 0 : _defaultOptions$local2.firstWeekContainsDate) !== null && _ref !== void 0 ? _ref : 1);\n  var year = getWeekYear(dirtyDate, options);\n  var firstWeek = new Date(0);\n  firstWeek.setFullYear(year, 0, firstWeekContainsDate);\n  firstWeek.setHours(0, 0, 0, 0);\n  var date = startOfWeek(firstWeek, options);\n  return date;\n}"], "mappings": "AAAA,OAAOA,WAAW,MAAM,yBAAyB;AACjD,OAAOC,WAAW,MAAM,yBAAyB;AACjD,OAAOC,SAAS,MAAM,4BAA4B;AAClD,OAAOC,YAAY,MAAM,+BAA+B;AACxD,SAASC,iBAAiB,QAAQ,iCAAiC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,eAAeA,CAACC,SAAS,EAAEC,OAAO,EAAE;EAC1D,IAAIC,IAAI,EAAEC,KAAK,EAAEC,KAAK,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,qBAAqB,EAAEC,qBAAqB,EAAEC,sBAAsB;EACpIZ,YAAY,CAAC,CAAC,EAAEa,SAAS,CAAC;EAC1B,IAAIC,cAAc,GAAGb,iBAAiB,CAAC,CAAC;EACxC,IAAIc,qBAAqB,GAAGhB,SAAS,CAAC,CAACM,IAAI,GAAG,CAACC,KAAK,GAAG,CAACC,KAAK,GAAG,CAACC,qBAAqB,GAAGJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACW,qBAAqB,MAAM,IAAI,IAAIP,qBAAqB,KAAK,KAAK,CAAC,GAAGA,qBAAqB,GAAGJ,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACK,eAAe,GAAGL,OAAO,CAACY,MAAM,MAAM,IAAI,IAAIP,eAAe,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,qBAAqB,GAAGD,eAAe,CAACL,OAAO,MAAM,IAAI,IAAIM,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,qBAAqB,CAACK,qBAAqB,MAAM,IAAI,IAAIR,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAGO,cAAc,CAACC,qBAAqB,MAAM,IAAI,IAAIT,KAAK,KAAK,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACK,qBAAqB,GAAGG,cAAc,CAACE,MAAM,MAAM,IAAI,IAAIL,qBAAqB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAG,CAACC,sBAAsB,GAAGD,qBAAqB,CAACP,OAAO,MAAM,IAAI,IAAIQ,sBAAsB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAACG,qBAAqB,MAAM,IAAI,IAAIV,IAAI,KAAK,KAAK,CAAC,GAAGA,IAAI,GAAG,CAAC,CAAC;EACl7B,IAAIY,IAAI,GAAGpB,WAAW,CAACM,SAAS,EAAEC,OAAO,CAAC;EAC1C,IAAIc,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAAC;EAC3BD,SAAS,CAACE,WAAW,CAACH,IAAI,EAAE,CAAC,EAAEF,qBAAqB,CAAC;EACrDG,SAAS,CAACG,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;EAC9B,IAAIC,IAAI,GAAGxB,WAAW,CAACoB,SAAS,EAAEd,OAAO,CAAC;EAC1C,OAAOkB,IAAI;AACb", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}