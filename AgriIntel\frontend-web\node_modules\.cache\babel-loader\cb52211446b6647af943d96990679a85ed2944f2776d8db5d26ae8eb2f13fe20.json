{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.GetMoreOperation = void 0;\nconst responses_1 = require(\"../cmap/wire_protocol/responses\");\nconst error_1 = require(\"../error\");\nconst utils_1 = require(\"../utils\");\nconst operation_1 = require(\"./operation\");\n/** @internal */\nclass GetMoreOperation extends operation_1.AbstractOperation {\n  constructor(ns, cursorId, server, options) {\n    super(options);\n    this.options = options;\n    this.ns = ns;\n    this.cursorId = cursorId;\n    this.server = server;\n  }\n  get commandName() {\n    return 'getMore';\n  }\n  /**\n   * Although there is a server already associated with the get more operation, the signature\n   * for execute passes a server so we will just use that one.\n   */\n  async execute(server, _session, timeoutContext) {\n    if (server !== this.server) {\n      throw new error_1.MongoRuntimeError('Getmore must run on the same server operation began on');\n    }\n    if (this.cursorId == null || this.cursorId.isZero()) {\n      throw new error_1.MongoRuntimeError('Unable to iterate cursor with no id');\n    }\n    const collection = this.ns.collection;\n    if (collection == null) {\n      // Cursors should have adopted the namespace returned by MongoDB\n      // which should always defined a collection name (even a pseudo one, ex. db.aggregate())\n      throw new error_1.MongoRuntimeError('A collection name must be determined before getMore');\n    }\n    const getMoreCmd = {\n      getMore: this.cursorId,\n      collection\n    };\n    if (typeof this.options.batchSize === 'number') {\n      getMoreCmd.batchSize = Math.abs(this.options.batchSize);\n    }\n    if (typeof this.options.maxAwaitTimeMS === 'number') {\n      getMoreCmd.maxTimeMS = this.options.maxAwaitTimeMS;\n    }\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (this.options.comment !== undefined && (0, utils_1.maxWireVersion)(server) >= 9) {\n      getMoreCmd.comment = this.options.comment;\n    }\n    const commandOptions = {\n      returnFieldSelector: null,\n      documentsReturnedIn: 'nextBatch',\n      timeoutContext,\n      ...this.options\n    };\n    return await server.command(this.ns, getMoreCmd, commandOptions, responses_1.CursorResponse);\n  }\n}\nexports.GetMoreOperation = GetMoreOperation;\n(0, operation_1.defineAspects)(GetMoreOperation, [operation_1.Aspect.READ_OPERATION, operation_1.Aspect.MUST_SELECT_SAME_SERVER]);", "map": {"version": 3, "names": ["responses_1", "require", "error_1", "utils_1", "operation_1", "GetMoreOperation", "AbstractOperation", "constructor", "ns", "cursorId", "server", "options", "commandName", "execute", "_session", "timeoutContext", "MongoRuntimeError", "isZero", "collection", "getMoreCmd", "getMore", "batchSize", "Math", "abs", "maxAwaitTimeMS", "maxTimeMS", "comment", "undefined", "maxWireVersion", "commandOptions", "returnFieldSelector", "documentsReturnedIn", "command", "CursorResponse", "exports", "defineAspects", "Aspect", "READ_OPERATION", "MUST_SELECT_SAME_SERVER"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\get_more.ts"], "sourcesContent": ["import type { Long } from '../bson';\nimport { CursorResponse } from '../cmap/wire_protocol/responses';\nimport { MongoRuntimeError } from '../error';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { maxWireVersion, type MongoDBNamespace } from '../utils';\nimport { AbstractOperation, Aspect, defineAspects, type OperationOptions } from './operation';\n\n/** @internal */\nexport interface GetMoreOptions extends OperationOptions {\n  /** Set the batchSize for the getMoreCommand when iterating over the query results. */\n  batchSize?: number;\n  /**\n   * Comment to apply to the operation.\n   *\n   * getMore only supports 'comment' in server versions 4.4 and above.\n   */\n  comment?: unknown;\n  /** Number of milliseconds to wait before aborting the query. */\n  maxTimeMS?: number;\n  /** TODO(NODE-4413): Address bug with maxAwaitTimeMS not being passed in from the cursor correctly */\n  maxAwaitTimeMS?: number;\n}\n\n/**\n * GetMore command: https://www.mongodb.com/docs/manual/reference/command/getMore/\n * @internal\n */\nexport interface GetMoreCommand {\n  getMore: Long;\n  collection: string;\n  batchSize?: number;\n  maxTimeMS?: number;\n  /** Only supported on wire versions 10 or greater */\n  comment?: unknown;\n}\n\n/** @internal */\nexport class GetMoreOperation extends AbstractOperation {\n  cursorId: Long;\n  override options: GetMoreOptions;\n\n  constructor(ns: MongoDBNamespace, cursorId: Long, server: Server, options: GetMoreOptions) {\n    super(options);\n\n    this.options = options;\n    this.ns = ns;\n    this.cursorId = cursorId;\n    this.server = server;\n  }\n\n  override get commandName() {\n    return 'getMore' as const;\n  }\n  /**\n   * Although there is a server already associated with the get more operation, the signature\n   * for execute passes a server so we will just use that one.\n   */\n  override async execute(\n    server: Server,\n    _session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<CursorResponse> {\n    if (server !== this.server) {\n      throw new MongoRuntimeError('Getmore must run on the same server operation began on');\n    }\n\n    if (this.cursorId == null || this.cursorId.isZero()) {\n      throw new MongoRuntimeError('Unable to iterate cursor with no id');\n    }\n\n    const collection = this.ns.collection;\n    if (collection == null) {\n      // Cursors should have adopted the namespace returned by MongoDB\n      // which should always defined a collection name (even a pseudo one, ex. db.aggregate())\n      throw new MongoRuntimeError('A collection name must be determined before getMore');\n    }\n\n    const getMoreCmd: GetMoreCommand = {\n      getMore: this.cursorId,\n      collection\n    };\n\n    if (typeof this.options.batchSize === 'number') {\n      getMoreCmd.batchSize = Math.abs(this.options.batchSize);\n    }\n\n    if (typeof this.options.maxAwaitTimeMS === 'number') {\n      getMoreCmd.maxTimeMS = this.options.maxAwaitTimeMS;\n    }\n\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (this.options.comment !== undefined && maxWireVersion(server) >= 9) {\n      getMoreCmd.comment = this.options.comment;\n    }\n\n    const commandOptions = {\n      returnFieldSelector: null,\n      documentsReturnedIn: 'nextBatch',\n      timeoutContext,\n      ...this.options\n    };\n\n    return await server.command(this.ns, getMoreCmd, commandOptions, CursorResponse);\n  }\n}\n\ndefineAspects(GetMoreOperation, [Aspect.READ_OPERATION, Aspect.MUST_SELECT_SAME_SERVER]);\n"], "mappings": ";;;;;;AACA,MAAAA,WAAA,GAAAC,OAAA;AACA,MAAAC,OAAA,GAAAD,OAAA;AAIA,MAAAE,OAAA,GAAAF,OAAA;AACA,MAAAG,WAAA,GAAAH,OAAA;AA+BA;AACA,MAAaI,gBAAiB,SAAQD,WAAA,CAAAE,iBAAiB;EAIrDC,YAAYC,EAAoB,EAAEC,QAAc,EAAEC,MAAc,EAAEC,OAAuB;IACvF,KAAK,CAACA,OAAO,CAAC;IAEd,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACH,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,MAAM,GAAGA,MAAM;EACtB;EAEA,IAAaE,WAAWA,CAAA;IACtB,OAAO,SAAkB;EAC3B;EACA;;;;EAIS,MAAMC,OAAOA,CACpBH,MAAc,EACdI,QAAmC,EACnCC,cAA8B;IAE9B,IAAIL,MAAM,KAAK,IAAI,CAACA,MAAM,EAAE;MAC1B,MAAM,IAAIR,OAAA,CAAAc,iBAAiB,CAAC,wDAAwD,CAAC;IACvF;IAEA,IAAI,IAAI,CAACP,QAAQ,IAAI,IAAI,IAAI,IAAI,CAACA,QAAQ,CAACQ,MAAM,EAAE,EAAE;MACnD,MAAM,IAAIf,OAAA,CAAAc,iBAAiB,CAAC,qCAAqC,CAAC;IACpE;IAEA,MAAME,UAAU,GAAG,IAAI,CAACV,EAAE,CAACU,UAAU;IACrC,IAAIA,UAAU,IAAI,IAAI,EAAE;MACtB;MACA;MACA,MAAM,IAAIhB,OAAA,CAAAc,iBAAiB,CAAC,qDAAqD,CAAC;IACpF;IAEA,MAAMG,UAAU,GAAmB;MACjCC,OAAO,EAAE,IAAI,CAACX,QAAQ;MACtBS;KACD;IAED,IAAI,OAAO,IAAI,CAACP,OAAO,CAACU,SAAS,KAAK,QAAQ,EAAE;MAC9CF,UAAU,CAACE,SAAS,GAAGC,IAAI,CAACC,GAAG,CAAC,IAAI,CAACZ,OAAO,CAACU,SAAS,CAAC;IACzD;IAEA,IAAI,OAAO,IAAI,CAACV,OAAO,CAACa,cAAc,KAAK,QAAQ,EAAE;MACnDL,UAAU,CAACM,SAAS,GAAG,IAAI,CAACd,OAAO,CAACa,cAAc;IACpD;IAEA;IACA;IACA,IAAI,IAAI,CAACb,OAAO,CAACe,OAAO,KAAKC,SAAS,IAAI,IAAAxB,OAAA,CAAAyB,cAAc,EAAClB,MAAM,CAAC,IAAI,CAAC,EAAE;MACrES,UAAU,CAACO,OAAO,GAAG,IAAI,CAACf,OAAO,CAACe,OAAO;IAC3C;IAEA,MAAMG,cAAc,GAAG;MACrBC,mBAAmB,EAAE,IAAI;MACzBC,mBAAmB,EAAE,WAAW;MAChChB,cAAc;MACd,GAAG,IAAI,CAACJ;KACT;IAED,OAAO,MAAMD,MAAM,CAACsB,OAAO,CAAC,IAAI,CAACxB,EAAE,EAAEW,UAAU,EAAEU,cAAc,EAAE7B,WAAA,CAAAiC,cAAc,CAAC;EAClF;;AAnEFC,OAAA,CAAA7B,gBAAA,GAAAA,gBAAA;AAsEA,IAAAD,WAAA,CAAA+B,aAAa,EAAC9B,gBAAgB,EAAE,CAACD,WAAA,CAAAgC,MAAM,CAACC,cAAc,EAAEjC,WAAA,CAAAgC,MAAM,CAACE,uBAAuB,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}