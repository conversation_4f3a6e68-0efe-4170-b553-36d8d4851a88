{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.aws4 = void 0;\nexports.getKerberos = getKerberos;\nexports.getZstdLibrary = getZstdLibrary;\nexports.getAwsCredentialProvider = getAwsCredentialProvider;\nexports.getGcpMetadata = getGcpMetadata;\nexports.getSnappy = getSnappy;\nexports.getSocks = getSocks;\nexports.getMongoDBClientEncryption = getMongoDBClientEncryption;\nconst error_1 = require(\"./error\");\nfunction makeErrorModule(error) {\n  const props = error ? {\n    kModuleError: error\n  } : {};\n  return new Proxy(props, {\n    get: (_, key) => {\n      if (key === 'kModuleError') {\n        return error;\n      }\n      throw error;\n    },\n    set: () => {\n      throw error;\n    }\n  });\n}\nfunction getKerberos() {\n  let kerberos;\n  try {\n    // Ensure you always wrap an optional require in the try block NODE-3199\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    kerberos = require('kerberos');\n  } catch (error) {\n    kerberos = makeErrorModule(new error_1.MongoMissingDependencyError('Optional module `kerberos` not found. Please install it to enable kerberos authentication', {\n      cause: error,\n      dependencyName: 'kerberos'\n    }));\n  }\n  return kerberos;\n}\nfunction getZstdLibrary() {\n  let ZStandard;\n  try {\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    ZStandard = require('@mongodb-js/zstd');\n  } catch (error) {\n    ZStandard = makeErrorModule(new error_1.MongoMissingDependencyError('Optional module `@mongodb-js/zstd` not found. Please install it to enable zstd compression', {\n      cause: error,\n      dependencyName: 'zstd'\n    }));\n  }\n  return ZStandard;\n}\nfunction getAwsCredentialProvider() {\n  try {\n    // Ensure you always wrap an optional require in the try block NODE-3199\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    const credentialProvider = require('@aws-sdk/credential-providers');\n    return credentialProvider;\n  } catch (error) {\n    return makeErrorModule(new error_1.MongoMissingDependencyError('Optional module `@aws-sdk/credential-providers` not found.' + ' Please install it to enable getting aws credentials via the official sdk.', {\n      cause: error,\n      dependencyName: '@aws-sdk/credential-providers'\n    }));\n  }\n}\nfunction getGcpMetadata() {\n  try {\n    // Ensure you always wrap an optional require in the try block NODE-3199\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    const credentialProvider = require('gcp-metadata');\n    return credentialProvider;\n  } catch (error) {\n    return makeErrorModule(new error_1.MongoMissingDependencyError('Optional module `gcp-metadata` not found.' + ' Please install it to enable getting gcp credentials via the official sdk.', {\n      cause: error,\n      dependencyName: 'gcp-metadata'\n    }));\n  }\n}\nfunction getSnappy() {\n  try {\n    // Ensure you always wrap an optional require in the try block NODE-3199\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    const value = require('snappy');\n    return value;\n  } catch (error) {\n    const kModuleError = new error_1.MongoMissingDependencyError('Optional module `snappy` not found. Please install it to enable snappy compression', {\n      cause: error,\n      dependencyName: 'snappy'\n    });\n    return {\n      kModuleError\n    };\n  }\n}\nfunction getSocks() {\n  try {\n    // Ensure you always wrap an optional require in the try block NODE-3199\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    const value = require('socks');\n    return value;\n  } catch (error) {\n    const kModuleError = new error_1.MongoMissingDependencyError('Optional module `socks` not found. Please install it to connections over a SOCKS5 proxy', {\n      cause: error,\n      dependencyName: 'socks'\n    });\n    return {\n      kModuleError\n    };\n  }\n}\nexports.aws4 = loadAws4();\nfunction loadAws4() {\n  let aws4;\n  try {\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    aws4 = require('aws4');\n  } catch (error) {\n    aws4 = makeErrorModule(new error_1.MongoMissingDependencyError('Optional module `aws4` not found. Please install it to enable AWS authentication', {\n      cause: error,\n      dependencyName: 'aws4'\n    }));\n  }\n  return aws4;\n}\n/** A utility function to get the instance of mongodb-client-encryption, if it exists. */\nfunction getMongoDBClientEncryption() {\n  let mongodbClientEncryption = null;\n  try {\n    // NOTE(NODE-3199): Ensure you always wrap an optional require literally in the try block\n    // Cannot be moved to helper utility function, bundlers search and replace the actual require call\n    // in a way that makes this line throw at bundle time, not runtime, catching here will make bundling succeed\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    mongodbClientEncryption = require('mongodb-client-encryption');\n  } catch (error) {\n    const kModuleError = new error_1.MongoMissingDependencyError('Optional module `mongodb-client-encryption` not found. Please install it to use auto encryption or ClientEncryption.', {\n      cause: error,\n      dependencyName: 'mongodb-client-encryption'\n    });\n    return {\n      kModuleError\n    };\n  }\n  return mongodbClientEncryption;\n}", "map": {"version": 3, "names": ["exports", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getZstdLibrary", "getAwsCredentialProvider", "getGcpMetadata", "getSnappy", "getSocks", "getMongoDBClientEncryption", "error_1", "require", "makeErrorModule", "error", "props", "kModuleError", "Proxy", "get", "_", "key", "set", "kerber<PERSON>", "MongoMissingDependencyError", "cause", "dependencyName", "ZStandard", "credentialProvider", "value", "aws4", "loadAws4", "mongodbClientEncryption"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\deps.ts"], "sourcesContent": ["import { type Stream } from './cmap/connect';\nimport { MongoMissingDependencyError } from './error';\nimport type { Callback } from './utils';\n\nfunction makeErrorModule(error: any) {\n  const props = error ? { kModuleError: error } : {};\n  return new Proxy(props, {\n    get: (_: any, key: any) => {\n      if (key === 'kModuleError') {\n        return error;\n      }\n      throw error;\n    },\n    set: () => {\n      throw error;\n    }\n  });\n}\n\nexport type Kerberos = typeof import('kerberos') | { kModuleError: MongoMissingDependencyError };\n\nexport function getKerberos(): Kerberos {\n  let kerberos: Kerberos;\n  try {\n    // Ensure you always wrap an optional require in the try block NODE-3199\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    kerberos = require('kerberos');\n  } catch (error) {\n    kerberos = makeErrorModule(\n      new MongoMissingDependencyError(\n        'Optional module `kerberos` not found. Please install it to enable kerberos authentication',\n        { cause: error, dependencyName: 'kerberos' }\n      )\n    );\n  }\n  return kerberos;\n}\n\nexport interface KerberosClient {\n  step(challenge: string): Promise<string>;\n  step(challenge: string, callback: Callback<string>): void;\n  wrap(challenge: string, options: { user: string }): Promise<string>;\n  wrap(challenge: string, options: { user: string }, callback: Callback<string>): void;\n  unwrap(challenge: string): Promise<string>;\n  unwrap(challenge: string, callback: Callback<string>): void;\n}\n\ntype ZStandardLib = {\n  /**\n   * Compress using zstd.\n   * @param buf - Buffer to be compressed.\n   */\n  compress(buf: Buffer, level?: number): Promise<Buffer>;\n\n  /**\n   * Decompress using zstd.\n   */\n  decompress(buf: Buffer): Promise<Buffer>;\n};\n\nexport type ZStandard = ZStandardLib | { kModuleError: MongoMissingDependencyError };\n\nexport function getZstdLibrary(): ZStandardLib | { kModuleError: MongoMissingDependencyError } {\n  let ZStandard: ZStandardLib | { kModuleError: MongoMissingDependencyError };\n  try {\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    ZStandard = require('@mongodb-js/zstd');\n  } catch (error) {\n    ZStandard = makeErrorModule(\n      new MongoMissingDependencyError(\n        'Optional module `@mongodb-js/zstd` not found. Please install it to enable zstd compression',\n        { cause: error, dependencyName: 'zstd' }\n      )\n    );\n  }\n\n  return ZStandard;\n}\n\n/**\n * @public\n * Copy of the AwsCredentialIdentityProvider interface from [`smithy/types`](https://socket.dev/npm/package/\\@smithy/types/files/1.1.1/dist-types/identity/awsCredentialIdentity.d.ts),\n * the return type of the aws-sdk's `fromNodeProviderChain().provider()`.\n */\nexport interface AWSCredentials {\n  accessKeyId: string;\n  secretAccessKey: string;\n  sessionToken?: string;\n  expiration?: Date;\n}\n\ntype CredentialProvider = {\n  fromNodeProviderChain(\n    this: void,\n    options: { clientConfig: { region: string } }\n  ): () => Promise<AWSCredentials>;\n  fromNodeProviderChain(this: void): () => Promise<AWSCredentials>;\n};\n\nexport function getAwsCredentialProvider():\n  | CredentialProvider\n  | { kModuleError: MongoMissingDependencyError } {\n  try {\n    // Ensure you always wrap an optional require in the try block NODE-3199\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    const credentialProvider = require('@aws-sdk/credential-providers');\n    return credentialProvider;\n  } catch (error) {\n    return makeErrorModule(\n      new MongoMissingDependencyError(\n        'Optional module `@aws-sdk/credential-providers` not found.' +\n          ' Please install it to enable getting aws credentials via the official sdk.',\n        { cause: error, dependencyName: '@aws-sdk/credential-providers' }\n      )\n    );\n  }\n}\n\n/** @internal */\nexport type GcpMetadata =\n  | typeof import('gcp-metadata')\n  | { kModuleError: MongoMissingDependencyError };\n\nexport function getGcpMetadata(): GcpMetadata {\n  try {\n    // Ensure you always wrap an optional require in the try block NODE-3199\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    const credentialProvider = require('gcp-metadata');\n    return credentialProvider;\n  } catch (error) {\n    return makeErrorModule(\n      new MongoMissingDependencyError(\n        'Optional module `gcp-metadata` not found.' +\n          ' Please install it to enable getting gcp credentials via the official sdk.',\n        { cause: error, dependencyName: 'gcp-metadata' }\n      )\n    );\n  }\n}\n\n/** @internal */\nexport type SnappyLib = {\n  /**\n   * In order to support both we must check the return value of the function\n   * @param buf - Buffer to be compressed\n   */\n  compress(buf: Buffer): Promise<Buffer>;\n\n  /**\n   * In order to support both we must check the return value of the function\n   * @param buf - Buffer to be compressed\n   */\n  uncompress(buf: Buffer, opt: { asBuffer: true }): Promise<Buffer>;\n};\n\nexport function getSnappy(): SnappyLib | { kModuleError: MongoMissingDependencyError } {\n  try {\n    // Ensure you always wrap an optional require in the try block NODE-3199\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    const value = require('snappy');\n    return value;\n  } catch (error) {\n    const kModuleError = new MongoMissingDependencyError(\n      'Optional module `snappy` not found. Please install it to enable snappy compression',\n      { cause: error, dependencyName: 'snappy' }\n    );\n    return { kModuleError };\n  }\n}\n\nexport type SocksLib = {\n  SocksClient: {\n    createConnection(options: {\n      command: 'connect';\n      destination: { host: string; port: number };\n      proxy: {\n        /** host and port are ignored because we pass existing_socket */\n        host: 'iLoveJavaScript';\n        port: 0;\n        type: 5;\n        userId?: string;\n        password?: string;\n      };\n      timeout?: number;\n      /** We always create our own socket, and pass it to this API for proxy negotiation */\n      existing_socket: Stream;\n    }): Promise<{ socket: Stream }>;\n  };\n};\n\nexport function getSocks(): SocksLib | { kModuleError: MongoMissingDependencyError } {\n  try {\n    // Ensure you always wrap an optional require in the try block NODE-3199\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    const value = require('socks');\n    return value;\n  } catch (error) {\n    const kModuleError = new MongoMissingDependencyError(\n      'Optional module `socks` not found. Please install it to connections over a SOCKS5 proxy',\n      { cause: error, dependencyName: 'socks' }\n    );\n    return { kModuleError };\n  }\n}\n\ninterface AWS4 {\n  /**\n   * Created these inline types to better assert future usage of this API\n   * @param options - options for request\n   * @param credentials - AWS credential details, sessionToken should be omitted entirely if its false-y\n   */\n  sign(\n    this: void,\n    options: {\n      path: '/';\n      body: string;\n      host: string;\n      method: 'POST';\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded';\n        'Content-Length': number;\n        'X-MongoDB-Server-Nonce': string;\n        'X-MongoDB-GS2-CB-Flag': 'n';\n      };\n      service: string;\n      region: string;\n    },\n    credentials:\n      | {\n          accessKeyId: string;\n          secretAccessKey: string;\n          sessionToken: string;\n        }\n      | {\n          accessKeyId: string;\n          secretAccessKey: string;\n        }\n      | undefined\n  ): {\n    headers: {\n      Authorization: string;\n      'X-Amz-Date': string;\n    };\n  };\n}\n\nexport const aws4: AWS4 | { kModuleError: MongoMissingDependencyError } = loadAws4();\n\nfunction loadAws4() {\n  let aws4: AWS4 | { kModuleError: MongoMissingDependencyError };\n  try {\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    aws4 = require('aws4');\n  } catch (error) {\n    aws4 = makeErrorModule(\n      new MongoMissingDependencyError(\n        'Optional module `aws4` not found. Please install it to enable AWS authentication',\n        { cause: error, dependencyName: 'aws4' }\n      )\n    );\n  }\n\n  return aws4;\n}\n\n/** A utility function to get the instance of mongodb-client-encryption, if it exists. */\nexport function getMongoDBClientEncryption():\n  | typeof import('mongodb-client-encryption')\n  | { kModuleError: MongoMissingDependencyError } {\n  let mongodbClientEncryption = null;\n\n  try {\n    // NOTE(NODE-3199): Ensure you always wrap an optional require literally in the try block\n    // Cannot be moved to helper utility function, bundlers search and replace the actual require call\n    // in a way that makes this line throw at bundle time, not runtime, catching here will make bundling succeed\n    // eslint-disable-next-line @typescript-eslint/no-require-imports\n    mongodbClientEncryption = require('mongodb-client-encryption');\n  } catch (error) {\n    const kModuleError = new MongoMissingDependencyError(\n      'Optional module `mongodb-client-encryption` not found. Please install it to use auto encryption or ClientEncryption.',\n      { cause: error, dependencyName: 'mongodb-client-encryption' }\n    );\n    return { kModuleError };\n  }\n\n  return mongodbClientEncryption;\n}\n"], "mappings": ";;;;;;AAqBAA,OAAA,CAAAC,WAAA,GAAAA,WAAA;AAyCAD,OAAA,CAAAE,cAAA,GAAAA,cAAA;AAqCAF,OAAA,CAAAG,wBAAA,GAAAA,wBAAA;AAwBAH,OAAA,CAAAI,cAAA,GAAAA,cAAA;AAgCAJ,OAAA,CAAAK,SAAA,GAAAA,SAAA;AAmCAL,OAAA,CAAAM,QAAA,GAAAA,QAAA;AA4EAN,OAAA,CAAAO,0BAAA,GAAAA,0BAAA;AAzQA,MAAAC,OAAA,GAAAC,OAAA;AAGA,SAASC,eAAeA,CAACC,KAAU;EACjC,MAAMC,KAAK,GAAGD,KAAK,GAAG;IAAEE,YAAY,EAAEF;EAAK,CAAE,GAAG,EAAE;EAClD,OAAO,IAAIG,KAAK,CAACF,KAAK,EAAE;IACtBG,GAAG,EAAEA,CAACC,CAAM,EAAEC,GAAQ,KAAI;MACxB,IAAIA,GAAG,KAAK,cAAc,EAAE;QAC1B,OAAON,KAAK;MACd;MACA,MAAMA,KAAK;IACb,CAAC;IACDO,GAAG,EAAEA,CAAA,KAAK;MACR,MAAMP,KAAK;IACb;GACD,CAAC;AACJ;AAIA,SAAgBV,WAAWA,CAAA;EACzB,IAAIkB,QAAkB;EACtB,IAAI;IACF;IACA;IACAA,QAAQ,GAAGV,OAAO,CAAC,UAAU,CAAC;EAChC,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdQ,QAAQ,GAAGT,eAAe,CACxB,IAAIF,OAAA,CAAAY,2BAA2B,CAC7B,2FAA2F,EAC3F;MAAEC,KAAK,EAAEV,KAAK;MAAEW,cAAc,EAAE;IAAU,CAAE,CAC7C,CACF;EACH;EACA,OAAOH,QAAQ;AACjB;AA0BA,SAAgBjB,cAAcA,CAAA;EAC5B,IAAIqB,SAAuE;EAC3E,IAAI;IACF;IACAA,SAAS,GAAGd,OAAO,CAAC,kBAAkB,CAAC;EACzC,CAAC,CAAC,OAAOE,KAAK,EAAE;IACdY,SAAS,GAAGb,eAAe,CACzB,IAAIF,OAAA,CAAAY,2BAA2B,CAC7B,4FAA4F,EAC5F;MAAEC,KAAK,EAAEV,KAAK;MAAEW,cAAc,EAAE;IAAM,CAAE,CACzC,CACF;EACH;EAEA,OAAOC,SAAS;AAClB;AAsBA,SAAgBpB,wBAAwBA,CAAA;EAGtC,IAAI;IACF;IACA;IACA,MAAMqB,kBAAkB,GAAGf,OAAO,CAAC,+BAA+B,CAAC;IACnE,OAAOe,kBAAkB;EAC3B,CAAC,CAAC,OAAOb,KAAK,EAAE;IACd,OAAOD,eAAe,CACpB,IAAIF,OAAA,CAAAY,2BAA2B,CAC7B,4DAA4D,GAC1D,4EAA4E,EAC9E;MAAEC,KAAK,EAAEV,KAAK;MAAEW,cAAc,EAAE;IAA+B,CAAE,CAClE,CACF;EACH;AACF;AAOA,SAAgBlB,cAAcA,CAAA;EAC5B,IAAI;IACF;IACA;IACA,MAAMoB,kBAAkB,GAAGf,OAAO,CAAC,cAAc,CAAC;IAClD,OAAOe,kBAAkB;EAC3B,CAAC,CAAC,OAAOb,KAAK,EAAE;IACd,OAAOD,eAAe,CACpB,IAAIF,OAAA,CAAAY,2BAA2B,CAC7B,2CAA2C,GACzC,4EAA4E,EAC9E;MAAEC,KAAK,EAAEV,KAAK;MAAEW,cAAc,EAAE;IAAc,CAAE,CACjD,CACF;EACH;AACF;AAiBA,SAAgBjB,SAASA,CAAA;EACvB,IAAI;IACF;IACA;IACA,MAAMoB,KAAK,GAAGhB,OAAO,CAAC,QAAQ,CAAC;IAC/B,OAAOgB,KAAK;EACd,CAAC,CAAC,OAAOd,KAAK,EAAE;IACd,MAAME,YAAY,GAAG,IAAIL,OAAA,CAAAY,2BAA2B,CAClD,oFAAoF,EACpF;MAAEC,KAAK,EAAEV,KAAK;MAAEW,cAAc,EAAE;IAAQ,CAAE,CAC3C;IACD,OAAO;MAAET;IAAY,CAAE;EACzB;AACF;AAsBA,SAAgBP,QAAQA,CAAA;EACtB,IAAI;IACF;IACA;IACA,MAAMmB,KAAK,GAAGhB,OAAO,CAAC,OAAO,CAAC;IAC9B,OAAOgB,KAAK;EACd,CAAC,CAAC,OAAOd,KAAK,EAAE;IACd,MAAME,YAAY,GAAG,IAAIL,OAAA,CAAAY,2BAA2B,CAClD,yFAAyF,EACzF;MAAEC,KAAK,EAAEV,KAAK;MAAEW,cAAc,EAAE;IAAO,CAAE,CAC1C;IACD,OAAO;MAAET;IAAY,CAAE;EACzB;AACF;AA2Cab,OAAA,CAAA0B,IAAI,GAAyDC,QAAQ,EAAE;AAEpF,SAASA,QAAQA,CAAA;EACf,IAAID,IAA0D;EAC9D,IAAI;IACF;IACAA,IAAI,GAAGjB,OAAO,CAAC,MAAM,CAAC;EACxB,CAAC,CAAC,OAAOE,KAAK,EAAE;IACde,IAAI,GAAGhB,eAAe,CACpB,IAAIF,OAAA,CAAAY,2BAA2B,CAC7B,kFAAkF,EAClF;MAAEC,KAAK,EAAEV,KAAK;MAAEW,cAAc,EAAE;IAAM,CAAE,CACzC,CACF;EACH;EAEA,OAAOI,IAAI;AACb;AAEA;AACA,SAAgBnB,0BAA0BA,CAAA;EAGxC,IAAIqB,uBAAuB,GAAG,IAAI;EAElC,IAAI;IACF;IACA;IACA;IACA;IACAA,uBAAuB,GAAGnB,OAAO,CAAC,2BAA2B,CAAC;EAChE,CAAC,CAAC,OAAOE,KAAK,EAAE;IACd,MAAME,YAAY,GAAG,IAAIL,OAAA,CAAAY,2BAA2B,CAClD,sHAAsH,EACtH;MAAEC,KAAK,EAAEV,KAAK;MAAEW,cAAc,EAAE;IAA2B,CAAE,CAC9D;IACD,OAAO;MAAET;IAAY,CAAE;EACzB;EAEA,OAAOe,uBAAuB;AAChC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}