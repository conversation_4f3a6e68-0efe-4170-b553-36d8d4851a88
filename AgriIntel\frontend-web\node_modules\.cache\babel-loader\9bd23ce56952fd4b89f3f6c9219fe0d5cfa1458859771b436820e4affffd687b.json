{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.CollectionsOperation = void 0;\nconst collection_1 = require(\"../collection\");\nconst operation_1 = require(\"./operation\");\n/** @internal */\nclass CollectionsOperation extends operation_1.AbstractOperation {\n  constructor(db, options) {\n    super(options);\n    this.options = options;\n    this.db = db;\n  }\n  get commandName() {\n    return 'listCollections';\n  }\n  async execute(server, session) {\n    // Let's get the collection names\n    const documents = await this.db.listCollections({}, {\n      ...this.options,\n      nameOnly: true,\n      readPreference: this.readPreference,\n      session\n    }).toArray();\n    const collections = [];\n    for (const {\n      name\n    } of documents) {\n      if (!name.includes('$')) {\n        // Filter collections removing any illegal ones\n        collections.push(new collection_1.Collection(this.db, name, this.db.s.options));\n      }\n    }\n    // Return the collection objects\n    return collections;\n  }\n}\nexports.CollectionsOperation = CollectionsOperation;", "map": {"version": 3, "names": ["collection_1", "require", "operation_1", "CollectionsOperation", "AbstractOperation", "constructor", "db", "options", "commandName", "execute", "server", "session", "documents", "listCollections", "nameOnly", "readPreference", "toArray", "collections", "name", "includes", "push", "Collection", "s", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\collections.ts"], "sourcesContent": ["import { Collection } from '../collection';\nimport type { Db } from '../db';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { AbstractOperation, type OperationOptions } from './operation';\n\nexport interface CollectionsOptions extends OperationOptions {\n  nameOnly?: boolean;\n}\n\n/** @internal */\nexport class CollectionsOperation extends AbstractOperation<Collection[]> {\n  override options: CollectionsOptions;\n  db: Db;\n\n  constructor(db: Db, options: CollectionsOptions) {\n    super(options);\n    this.options = options;\n    this.db = db;\n  }\n\n  override get commandName() {\n    return 'listCollections' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined\n  ): Promise<Collection[]> {\n    // Let's get the collection names\n    const documents = await this.db\n      .listCollections(\n        {},\n        { ...this.options, nameOnly: true, readPreference: this.readPreference, session }\n      )\n      .toArray();\n    const collections: Collection[] = [];\n    for (const { name } of documents) {\n      if (!name.includes('$')) {\n        // Filter collections removing any illegal ones\n        collections.push(new Collection(this.db, name, this.db.s.options));\n      }\n    }\n    // Return the collection objects\n    return collections;\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,YAAA,GAAAC,OAAA;AAIA,MAAAC,WAAA,GAAAD,OAAA;AAMA;AACA,MAAaE,oBAAqB,SAAQD,WAAA,CAAAE,iBAA+B;EAIvEC,YAAYC,EAAM,EAAEC,OAA2B;IAC7C,KAAK,CAACA,OAAO,CAAC;IACd,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,EAAE,GAAGA,EAAE;EACd;EAEA,IAAaE,WAAWA,CAAA;IACtB,OAAO,iBAA0B;EACnC;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC;IAElC;IACA,MAAMC,SAAS,GAAG,MAAM,IAAI,CAACN,EAAE,CAC5BO,eAAe,CACd,EAAE,EACF;MAAE,GAAG,IAAI,CAACN,OAAO;MAAEO,QAAQ,EAAE,IAAI;MAAEC,cAAc,EAAE,IAAI,CAACA,cAAc;MAAEJ;IAAO,CAAE,CAClF,CACAK,OAAO,EAAE;IACZ,MAAMC,WAAW,GAAiB,EAAE;IACpC,KAAK,MAAM;MAAEC;IAAI,CAAE,IAAIN,SAAS,EAAE;MAChC,IAAI,CAACM,IAAI,CAACC,QAAQ,CAAC,GAAG,CAAC,EAAE;QACvB;QACAF,WAAW,CAACG,IAAI,CAAC,IAAIpB,YAAA,CAAAqB,UAAU,CAAC,IAAI,CAACf,EAAE,EAAEY,IAAI,EAAE,IAAI,CAACZ,EAAE,CAACgB,CAAC,CAACf,OAAO,CAAC,CAAC;MACpE;IACF;IACA;IACA,OAAOU,WAAW;EACpB;;AAlCFM,OAAA,CAAApB,oBAAA,GAAAA,oBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}