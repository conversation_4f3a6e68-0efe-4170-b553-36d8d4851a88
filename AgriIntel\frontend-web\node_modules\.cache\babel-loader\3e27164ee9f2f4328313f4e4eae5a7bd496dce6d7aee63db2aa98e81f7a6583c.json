{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.HumanCallbackWorkflow = void 0;\nconst bson_1 = require(\"../../../bson\");\nconst error_1 = require(\"../../../error\");\nconst timeout_1 = require(\"../../../timeout\");\nconst mongodb_oidc_1 = require(\"../mongodb_oidc\");\nconst callback_workflow_1 = require(\"./callback_workflow\");\n/**\n * Class implementing behaviour for the non human callback workflow.\n * @internal\n */\nclass HumanCallbackWorkflow extends callback_workflow_1.CallbackWorkflow {\n  /**\n   * Instantiate the human callback workflow.\n   */\n  constructor(cache, callback) {\n    super(cache, callback);\n  }\n  /**\n   * Execute the OIDC human callback workflow.\n   */\n  async execute(connection, credentials) {\n    // Check if the Client Cache has an access token.\n    // If it does, cache the access token in the Connection Cache and perform a One-Step SASL conversation\n    // using the access token. If the server returns an Authentication error (18),\n    // invalidate the access token token from the Client Cache, clear the Connection Cache,\n    // and restart the authentication flow. Raise any other errors to the user. On success, exit the algorithm.\n    if (this.cache.hasAccessToken) {\n      const token = this.cache.getAccessToken();\n      connection.accessToken = token;\n      try {\n        return await this.finishAuthentication(connection, credentials, token);\n      } catch (error) {\n        if (error instanceof error_1.MongoError && error.code === error_1.MONGODB_ERROR_CODES.AuthenticationFailed) {\n          this.cache.removeAccessToken();\n          delete connection.accessToken;\n          return await this.execute(connection, credentials);\n        } else {\n          throw error;\n        }\n      }\n    }\n    // Check if the Client Cache has a refresh token.\n    // If it does, call the OIDC Human Callback with the cached refresh token and IdpInfo to get a\n    // new access token. Cache the new access token in the Client Cache and Connection Cache.\n    // Perform a One-Step SASL conversation using the new access token. If the the server returns\n    // an Authentication error (18), clear the refresh token, invalidate the access token from the\n    // Client Cache, clear the Connection Cache, and restart the authentication flow. Raise any other\n    // errors to the user. On success, exit the algorithm.\n    if (this.cache.hasRefreshToken) {\n      const refreshToken = this.cache.getRefreshToken();\n      const result = await this.fetchAccessToken(this.cache.getIdpInfo(), credentials, refreshToken);\n      this.cache.put(result);\n      connection.accessToken = result.accessToken;\n      try {\n        return await this.finishAuthentication(connection, credentials, result.accessToken);\n      } catch (error) {\n        if (error instanceof error_1.MongoError && error.code === error_1.MONGODB_ERROR_CODES.AuthenticationFailed) {\n          this.cache.removeRefreshToken();\n          delete connection.accessToken;\n          return await this.execute(connection, credentials);\n        } else {\n          throw error;\n        }\n      }\n    }\n    // Start a new Two-Step SASL conversation.\n    // Run a PrincipalStepRequest to get the IdpInfo.\n    // Call the OIDC Human Callback with the new IdpInfo to get a new access token and optional refresh\n    // token. Drivers MUST NOT pass a cached refresh token to the callback when performing\n    // a new Two-Step conversation. Cache the new IdpInfo and refresh token in the Client Cache and the\n    // new access token in the Client Cache and Connection Cache.\n    // Attempt to authenticate using a JwtStepRequest with the new access token. Raise any errors to the user.\n    const startResponse = await this.startAuthentication(connection, credentials);\n    const conversationId = startResponse.conversationId;\n    const idpInfo = bson_1.BSON.deserialize(startResponse.payload.buffer);\n    const callbackResponse = await this.fetchAccessToken(idpInfo, credentials);\n    this.cache.put(callbackResponse, idpInfo);\n    connection.accessToken = callbackResponse.accessToken;\n    return await this.finishAuthentication(connection, credentials, callbackResponse.accessToken, conversationId);\n  }\n  /**\n   * Fetches an access token using the callback.\n   */\n  async fetchAccessToken(idpInfo, credentials, refreshToken) {\n    const controller = new AbortController();\n    const params = {\n      timeoutContext: controller.signal,\n      version: mongodb_oidc_1.OIDC_VERSION,\n      idpInfo: idpInfo\n    };\n    if (credentials.username) {\n      params.username = credentials.username;\n    }\n    if (refreshToken) {\n      params.refreshToken = refreshToken;\n    }\n    const timeout = timeout_1.Timeout.expires(callback_workflow_1.HUMAN_TIMEOUT_MS);\n    try {\n      return await Promise.race([this.executeAndValidateCallback(params), timeout]);\n    } catch (error) {\n      if (timeout_1.TimeoutError.is(error)) {\n        controller.abort();\n        throw new error_1.MongoOIDCError(`OIDC callback timed out after ${callback_workflow_1.HUMAN_TIMEOUT_MS}ms.`);\n      }\n      throw error;\n    } finally {\n      timeout.clear();\n    }\n  }\n}\nexports.HumanCallbackWorkflow = HumanCallbackWorkflow;", "map": {"version": 3, "names": ["bson_1", "require", "error_1", "timeout_1", "mongodb_oidc_1", "callback_workflow_1", "HumanCallbackWorkflow", "CallbackWorkflow", "constructor", "cache", "callback", "execute", "connection", "credentials", "hasAccessToken", "token", "getAccessToken", "accessToken", "finishAuthentication", "error", "MongoError", "code", "MONGODB_ERROR_CODES", "AuthenticationFailed", "removeAccessToken", "hasRefreshToken", "refreshToken", "getRefreshToken", "result", "fetchAccessToken", "getIdpInfo", "put", "removeRefreshToken", "startResponse", "startAuthentication", "conversationId", "idpInfo", "BSON", "deserialize", "payload", "buffer", "callbackResponse", "controller", "AbortController", "params", "timeoutContext", "signal", "version", "OIDC_VERSION", "username", "timeout", "Timeout", "expires", "HUMAN_TIMEOUT_MS", "Promise", "race", "executeAndValidateCallback", "TimeoutError", "is", "abort", "MongoOIDCError", "clear", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\auth\\mongodb_oidc\\human_callback_workflow.ts"], "sourcesContent": ["import { <PERSON>ON } from '../../../bson';\nimport { MONGODB_ERROR_CODES, MongoError, MongoOIDCError } from '../../../error';\nimport { Timeout, TimeoutError } from '../../../timeout';\nimport { type Connection } from '../../connection';\nimport { type MongoCredentials } from '../mongo_credentials';\nimport {\n  type IdPInfo,\n  OIDC_VERSION,\n  type OIDCCallbackFunction,\n  type OIDCCallbackParams,\n  type OIDCResponse\n} from '../mongodb_oidc';\nimport { CallbackWorkflow, HUMAN_TIMEOUT_MS } from './callback_workflow';\nimport { type TokenCache } from './token_cache';\n\n/**\n * Class implementing behaviour for the non human callback workflow.\n * @internal\n */\nexport class HumanCallbackWorkflow extends CallbackWorkflow {\n  /**\n   * Instantiate the human callback workflow.\n   */\n  constructor(cache: TokenCache, callback: OIDCCallbackFunction) {\n    super(cache, callback);\n  }\n\n  /**\n   * Execute the OIDC human callback workflow.\n   */\n  async execute(connection: Connection, credentials: MongoCredentials): Promise<void> {\n    // Check if the Client Cache has an access token.\n    // If it does, cache the access token in the Connection Cache and perform a One-Step SASL conversation\n    // using the access token. If the server returns an Authentication error (18),\n    // invalidate the access token token from the Client Cache, clear the Connection Cache,\n    // and restart the authentication flow. Raise any other errors to the user. On success, exit the algorithm.\n    if (this.cache.hasAccessToken) {\n      const token = this.cache.getAccessToken();\n      connection.accessToken = token;\n      try {\n        return await this.finishAuthentication(connection, credentials, token);\n      } catch (error) {\n        if (\n          error instanceof MongoError &&\n          error.code === MONGODB_ERROR_CODES.AuthenticationFailed\n        ) {\n          this.cache.removeAccessToken();\n          delete connection.accessToken;\n          return await this.execute(connection, credentials);\n        } else {\n          throw error;\n        }\n      }\n    }\n    // Check if the Client Cache has a refresh token.\n    // If it does, call the OIDC Human Callback with the cached refresh token and IdpInfo to get a\n    // new access token. Cache the new access token in the Client Cache and Connection Cache.\n    // Perform a One-Step SASL conversation using the new access token. If the the server returns\n    // an Authentication error (18), clear the refresh token, invalidate the access token from the\n    // Client Cache, clear the Connection Cache, and restart the authentication flow. Raise any other\n    // errors to the user. On success, exit the algorithm.\n    if (this.cache.hasRefreshToken) {\n      const refreshToken = this.cache.getRefreshToken();\n      const result = await this.fetchAccessToken(\n        this.cache.getIdpInfo(),\n        credentials,\n        refreshToken\n      );\n      this.cache.put(result);\n      connection.accessToken = result.accessToken;\n      try {\n        return await this.finishAuthentication(connection, credentials, result.accessToken);\n      } catch (error) {\n        if (\n          error instanceof MongoError &&\n          error.code === MONGODB_ERROR_CODES.AuthenticationFailed\n        ) {\n          this.cache.removeRefreshToken();\n          delete connection.accessToken;\n          return await this.execute(connection, credentials);\n        } else {\n          throw error;\n        }\n      }\n    }\n\n    // Start a new Two-Step SASL conversation.\n    // Run a PrincipalStepRequest to get the IdpInfo.\n    // Call the OIDC Human Callback with the new IdpInfo to get a new access token and optional refresh\n    // token. Drivers MUST NOT pass a cached refresh token to the callback when performing\n    // a new Two-Step conversation. Cache the new IdpInfo and refresh token in the Client Cache and the\n    // new access token in the Client Cache and Connection Cache.\n    // Attempt to authenticate using a JwtStepRequest with the new access token. Raise any errors to the user.\n    const startResponse = await this.startAuthentication(connection, credentials);\n    const conversationId = startResponse.conversationId;\n    const idpInfo = BSON.deserialize(startResponse.payload.buffer) as IdPInfo;\n    const callbackResponse = await this.fetchAccessToken(idpInfo, credentials);\n    this.cache.put(callbackResponse, idpInfo);\n    connection.accessToken = callbackResponse.accessToken;\n    return await this.finishAuthentication(\n      connection,\n      credentials,\n      callbackResponse.accessToken,\n      conversationId\n    );\n  }\n\n  /**\n   * Fetches an access token using the callback.\n   */\n  private async fetchAccessToken(\n    idpInfo: IdPInfo,\n    credentials: MongoCredentials,\n    refreshToken?: string\n  ): Promise<OIDCResponse> {\n    const controller = new AbortController();\n    const params: OIDCCallbackParams = {\n      timeoutContext: controller.signal,\n      version: OIDC_VERSION,\n      idpInfo: idpInfo\n    };\n    if (credentials.username) {\n      params.username = credentials.username;\n    }\n    if (refreshToken) {\n      params.refreshToken = refreshToken;\n    }\n    const timeout = Timeout.expires(HUMAN_TIMEOUT_MS);\n    try {\n      return await Promise.race([this.executeAndValidateCallback(params), timeout]);\n    } catch (error) {\n      if (TimeoutError.is(error)) {\n        controller.abort();\n        throw new MongoOIDCError(`OIDC callback timed out after ${HUMAN_TIMEOUT_MS}ms.`);\n      }\n      throw error;\n    } finally {\n      timeout.clear();\n    }\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,MAAA,GAAAC,OAAA;AACA,MAAAC,OAAA,GAAAD,OAAA;AACA,MAAAE,SAAA,GAAAF,OAAA;AAGA,MAAAG,cAAA,GAAAH,OAAA;AAOA,MAAAI,mBAAA,GAAAJ,OAAA;AAGA;;;;AAIA,MAAaK,qBAAsB,SAAQD,mBAAA,CAAAE,gBAAgB;EACzD;;;EAGAC,YAAYC,KAAiB,EAAEC,QAA8B;IAC3D,KAAK,CAACD,KAAK,EAAEC,QAAQ,CAAC;EACxB;EAEA;;;EAGA,MAAMC,OAAOA,CAACC,UAAsB,EAAEC,WAA6B;IACjE;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACJ,KAAK,CAACK,cAAc,EAAE;MAC7B,MAAMC,KAAK,GAAG,IAAI,CAACN,KAAK,CAACO,cAAc,EAAE;MACzCJ,UAAU,CAACK,WAAW,GAAGF,KAAK;MAC9B,IAAI;QACF,OAAO,MAAM,IAAI,CAACG,oBAAoB,CAACN,UAAU,EAAEC,WAAW,EAAEE,KAAK,CAAC;MACxE,CAAC,CAAC,OAAOI,KAAK,EAAE;QACd,IACEA,KAAK,YAAYjB,OAAA,CAAAkB,UAAU,IAC3BD,KAAK,CAACE,IAAI,KAAKnB,OAAA,CAAAoB,mBAAmB,CAACC,oBAAoB,EACvD;UACA,IAAI,CAACd,KAAK,CAACe,iBAAiB,EAAE;UAC9B,OAAOZ,UAAU,CAACK,WAAW;UAC7B,OAAO,MAAM,IAAI,CAACN,OAAO,CAACC,UAAU,EAAEC,WAAW,CAAC;QACpD,CAAC,MAAM;UACL,MAAMM,KAAK;QACb;MACF;IACF;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACV,KAAK,CAACgB,eAAe,EAAE;MAC9B,MAAMC,YAAY,GAAG,IAAI,CAACjB,KAAK,CAACkB,eAAe,EAAE;MACjD,MAAMC,MAAM,GAAG,MAAM,IAAI,CAACC,gBAAgB,CACxC,IAAI,CAACpB,KAAK,CAACqB,UAAU,EAAE,EACvBjB,WAAW,EACXa,YAAY,CACb;MACD,IAAI,CAACjB,KAAK,CAACsB,GAAG,CAACH,MAAM,CAAC;MACtBhB,UAAU,CAACK,WAAW,GAAGW,MAAM,CAACX,WAAW;MAC3C,IAAI;QACF,OAAO,MAAM,IAAI,CAACC,oBAAoB,CAACN,UAAU,EAAEC,WAAW,EAAEe,MAAM,CAACX,WAAW,CAAC;MACrF,CAAC,CAAC,OAAOE,KAAK,EAAE;QACd,IACEA,KAAK,YAAYjB,OAAA,CAAAkB,UAAU,IAC3BD,KAAK,CAACE,IAAI,KAAKnB,OAAA,CAAAoB,mBAAmB,CAACC,oBAAoB,EACvD;UACA,IAAI,CAACd,KAAK,CAACuB,kBAAkB,EAAE;UAC/B,OAAOpB,UAAU,CAACK,WAAW;UAC7B,OAAO,MAAM,IAAI,CAACN,OAAO,CAACC,UAAU,EAAEC,WAAW,CAAC;QACpD,CAAC,MAAM;UACL,MAAMM,KAAK;QACb;MACF;IACF;IAEA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMc,aAAa,GAAG,MAAM,IAAI,CAACC,mBAAmB,CAACtB,UAAU,EAAEC,WAAW,CAAC;IAC7E,MAAMsB,cAAc,GAAGF,aAAa,CAACE,cAAc;IACnD,MAAMC,OAAO,GAAGpC,MAAA,CAAAqC,IAAI,CAACC,WAAW,CAACL,aAAa,CAACM,OAAO,CAACC,MAAM,CAAY;IACzE,MAAMC,gBAAgB,GAAG,MAAM,IAAI,CAACZ,gBAAgB,CAACO,OAAO,EAAEvB,WAAW,CAAC;IAC1E,IAAI,CAACJ,KAAK,CAACsB,GAAG,CAACU,gBAAgB,EAAEL,OAAO,CAAC;IACzCxB,UAAU,CAACK,WAAW,GAAGwB,gBAAgB,CAACxB,WAAW;IACrD,OAAO,MAAM,IAAI,CAACC,oBAAoB,CACpCN,UAAU,EACVC,WAAW,EACX4B,gBAAgB,CAACxB,WAAW,EAC5BkB,cAAc,CACf;EACH;EAEA;;;EAGQ,MAAMN,gBAAgBA,CAC5BO,OAAgB,EAChBvB,WAA6B,EAC7Ba,YAAqB;IAErB,MAAMgB,UAAU,GAAG,IAAIC,eAAe,EAAE;IACxC,MAAMC,MAAM,GAAuB;MACjCC,cAAc,EAAEH,UAAU,CAACI,MAAM;MACjCC,OAAO,EAAE3C,cAAA,CAAA4C,YAAY;MACrBZ,OAAO,EAAEA;KACV;IACD,IAAIvB,WAAW,CAACoC,QAAQ,EAAE;MACxBL,MAAM,CAACK,QAAQ,GAAGpC,WAAW,CAACoC,QAAQ;IACxC;IACA,IAAIvB,YAAY,EAAE;MAChBkB,MAAM,CAAClB,YAAY,GAAGA,YAAY;IACpC;IACA,MAAMwB,OAAO,GAAG/C,SAAA,CAAAgD,OAAO,CAACC,OAAO,CAAC/C,mBAAA,CAAAgD,gBAAgB,CAAC;IACjD,IAAI;MACF,OAAO,MAAMC,OAAO,CAACC,IAAI,CAAC,CAAC,IAAI,CAACC,0BAA0B,CAACZ,MAAM,CAAC,EAAEM,OAAO,CAAC,CAAC;IAC/E,CAAC,CAAC,OAAO/B,KAAK,EAAE;MACd,IAAIhB,SAAA,CAAAsD,YAAY,CAACC,EAAE,CAACvC,KAAK,CAAC,EAAE;QAC1BuB,UAAU,CAACiB,KAAK,EAAE;QAClB,MAAM,IAAIzD,OAAA,CAAA0D,cAAc,CAAC,iCAAiCvD,mBAAA,CAAAgD,gBAAgB,KAAK,CAAC;MAClF;MACA,MAAMlC,KAAK;IACb,CAAC,SAAS;MACR+B,OAAO,CAACW,KAAK,EAAE;IACjB;EACF;;AAxHFC,OAAA,CAAAxD,qBAAA,GAAAA,qBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}