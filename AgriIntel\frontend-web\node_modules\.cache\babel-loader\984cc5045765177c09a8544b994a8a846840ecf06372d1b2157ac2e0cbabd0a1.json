{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.RunAdminCommandOperation = exports.RunCommandOperation = void 0;\nconst utils_1 = require(\"../utils\");\nconst operation_1 = require(\"./operation\");\n/** @internal */\nclass RunCommandOperation extends operation_1.AbstractOperation {\n  constructor(parent, command, options) {\n    super(options);\n    this.command = command;\n    this.options = options;\n    this.ns = parent.s.namespace.withCollection('$cmd');\n  }\n  get commandName() {\n    return 'runCommand';\n  }\n  async execute(server, session, timeoutContext) {\n    this.server = server;\n    const res = await server.command(this.ns, this.command, {\n      ...this.options,\n      readPreference: this.readPreference,\n      session,\n      timeoutContext\n    }, this.options.responseType);\n    return res;\n  }\n}\nexports.RunCommandOperation = RunCommandOperation;\nclass RunAdminCommandOperation extends operation_1.AbstractOperation {\n  constructor(command, options) {\n    super(options);\n    this.command = command;\n    this.options = options;\n    this.ns = new utils_1.MongoDBNamespace('admin', '$cmd');\n  }\n  get commandName() {\n    return 'runCommand';\n  }\n  async execute(server, session, timeoutContext) {\n    this.server = server;\n    const res = await server.command(this.ns, this.command, {\n      ...this.options,\n      readPreference: this.readPreference,\n      session,\n      timeoutContext\n    });\n    return res;\n  }\n}\nexports.RunAdminCommandOperation = RunAdminCommandOperation;", "map": {"version": 3, "names": ["utils_1", "require", "operation_1", "RunCommandOperation", "AbstractOperation", "constructor", "parent", "command", "options", "ns", "s", "namespace", "withCollection", "commandName", "execute", "server", "session", "timeoutContext", "res", "readPreference", "responseType", "exports", "RunAdminCommandOperation", "MongoDBNamespace"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\run_command.ts"], "sourcesContent": ["import type { BSONSerializeOptions, Document } from '../bson';\nimport { type MongoDBResponseConstructor } from '../cmap/wire_protocol/responses';\nimport { type Db } from '../db';\nimport { type TODO_NODE_3286 } from '../mongo_types';\nimport type { ReadPreferenceLike } from '../read_preference';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { MongoDBNamespace } from '../utils';\nimport { AbstractOperation } from './operation';\n\n/** @public */\nexport type RunCommandOptions = {\n  /** Specify ClientSession for this command */\n  session?: ClientSession;\n  /** The read preference */\n  readPreference?: ReadPreferenceLike;\n  /**\n   * @experimental\n   * Specifies the time an operation will run until it throws a timeout error\n   */\n  timeoutMS?: number;\n  /** @internal */\n  omitMaxTimeMS?: boolean;\n} & BSONSerializeOptions;\n\n/** @internal */\nexport class RunCommandOperation<T = Document> extends AbstractOperation<T> {\n  constructor(\n    parent: Db,\n    public command: Document,\n    public override options: RunCommandOptions & { responseType?: MongoDBResponseConstructor }\n  ) {\n    super(options);\n    this.ns = parent.s.namespace.withCollection('$cmd');\n  }\n\n  override get commandName() {\n    return 'runCommand' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<T> {\n    this.server = server;\n    const res: TODO_NODE_3286 = await server.command(\n      this.ns,\n      this.command,\n      {\n        ...this.options,\n        readPreference: this.readPreference,\n        session,\n        timeoutContext\n      },\n      this.options.responseType\n    );\n\n    return res;\n  }\n}\n\nexport class RunAdminCommandOperation<T = Document> extends AbstractOperation<T> {\n  constructor(\n    public command: Document,\n    public override options: RunCommandOptions & {\n      noResponse?: boolean;\n      bypassPinningCheck?: boolean;\n    }\n  ) {\n    super(options);\n    this.ns = new MongoDBNamespace('admin', '$cmd');\n  }\n\n  override get commandName() {\n    return 'runCommand' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<T> {\n    this.server = server;\n    const res: TODO_NODE_3286 = await server.command(this.ns, this.command, {\n      ...this.options,\n      readPreference: this.readPreference,\n      session,\n      timeoutContext\n    });\n    return res;\n  }\n}\n"], "mappings": ";;;;;;AAQA,MAAAA,OAAA,GAAAC,OAAA;AACA,MAAAC,WAAA,GAAAD,OAAA;AAiBA;AACA,MAAaE,mBAAkC,SAAQD,WAAA,CAAAE,iBAAoB;EACzEC,YACEC,MAAU,EACHC,OAAiB,EACRC,OAA0E;IAE1F,KAAK,CAACA,OAAO,CAAC;IAHP,KAAAD,OAAO,GAAPA,OAAO;IACE,KAAAC,OAAO,GAAPA,OAAO;IAGvB,IAAI,CAACC,EAAE,GAAGH,MAAM,CAACI,CAAC,CAACC,SAAS,CAACC,cAAc,CAAC,MAAM,CAAC;EACrD;EAEA,IAAaC,WAAWA,CAAA;IACtB,OAAO,YAAqB;EAC9B;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,MAAMG,GAAG,GAAmB,MAAMH,MAAM,CAACR,OAAO,CAC9C,IAAI,CAACE,EAAE,EACP,IAAI,CAACF,OAAO,EACZ;MACE,GAAG,IAAI,CAACC,OAAO;MACfW,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCH,OAAO;MACPC;KACD,EACD,IAAI,CAACT,OAAO,CAACY,YAAY,CAC1B;IAED,OAAOF,GAAG;EACZ;;AAjCFG,OAAA,CAAAlB,mBAAA,GAAAA,mBAAA;AAoCA,MAAamB,wBAAuC,SAAQpB,WAAA,CAAAE,iBAAoB;EAC9EC,YACSE,OAAiB,EACRC,OAGf;IAED,KAAK,CAACA,OAAO,CAAC;IANP,KAAAD,OAAO,GAAPA,OAAO;IACE,KAAAC,OAAO,GAAPA,OAAO;IAMvB,IAAI,CAACC,EAAE,GAAG,IAAIT,OAAA,CAAAuB,gBAAgB,CAAC,OAAO,EAAE,MAAM,CAAC;EACjD;EAEA,IAAaV,WAAWA,CAAA;IACtB,OAAO,YAAqB;EAC9B;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,IAAI,CAACF,MAAM,GAAGA,MAAM;IACpB,MAAMG,GAAG,GAAmB,MAAMH,MAAM,CAACR,OAAO,CAAC,IAAI,CAACE,EAAE,EAAE,IAAI,CAACF,OAAO,EAAE;MACtE,GAAG,IAAI,CAACC,OAAO;MACfW,cAAc,EAAE,IAAI,CAACA,cAAc;MACnCH,OAAO;MACPC;KACD,CAAC;IACF,OAAOC,GAAG;EACZ;;AA7BFG,OAAA,CAAAC,wBAAA,GAAAA,wBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}