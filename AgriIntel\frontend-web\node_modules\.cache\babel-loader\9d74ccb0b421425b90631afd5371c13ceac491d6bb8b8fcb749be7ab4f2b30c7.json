{"ast": null, "code": "\"use strict\";\n\nconst usm = require(\"./url-state-machine\");\nconst urlencoded = require(\"./urlencoded\");\nconst URLSearchParams = require(\"./URLSearchParams\");\nexports.implementation = class URLImpl {\n  // Unlike the spec, we duplicate some code between the constructor and canParse, because we want to give useful error\n  // messages in the constructor that distinguish between the different causes of failure.\n  constructor(globalObject, [url, base]) {\n    let parsedBase = null;\n    if (base !== undefined) {\n      parsedBase = usm.basicURLParse(base);\n      if (parsedBase === null) {\n        throw new TypeError(`Invalid base URL: ${base}`);\n      }\n    }\n    const parsedURL = usm.basicURLParse(url, {\n      baseURL: parsedBase\n    });\n    if (parsedURL === null) {\n      throw new TypeError(`Invalid URL: ${url}`);\n    }\n    const query = parsedURL.query !== null ? parsedURL.query : \"\";\n    this._url = parsedURL;\n\n    // We cannot invoke the \"new URLSearchParams object\" algorithm without going through the constructor, which strips\n    // question mark by default. Therefore the doNotStripQMark hack is used.\n    this._query = URLSearchParams.createImpl(globalObject, [query], {\n      doNotStripQMark: true\n    });\n    this._query._url = this;\n  }\n  static parse(globalObject, input, base) {\n    try {\n      return new URLImpl(globalObject, [input, base]);\n    } catch {\n      return null;\n    }\n  }\n  static canParse(url, base) {\n    let parsedBase = null;\n    if (base !== undefined) {\n      parsedBase = usm.basicURLParse(base);\n      if (parsedBase === null) {\n        return false;\n      }\n    }\n    const parsedURL = usm.basicURLParse(url, {\n      baseURL: parsedBase\n    });\n    if (parsedURL === null) {\n      return false;\n    }\n    return true;\n  }\n  get href() {\n    return usm.serializeURL(this._url);\n  }\n  set href(v) {\n    const parsedURL = usm.basicURLParse(v);\n    if (parsedURL === null) {\n      throw new TypeError(`Invalid URL: ${v}`);\n    }\n    this._url = parsedURL;\n    this._query._list.splice(0);\n    const {\n      query\n    } = parsedURL;\n    if (query !== null) {\n      this._query._list = urlencoded.parseUrlencodedString(query);\n    }\n  }\n  get origin() {\n    return usm.serializeURLOrigin(this._url);\n  }\n  get protocol() {\n    return `${this._url.scheme}:`;\n  }\n  set protocol(v) {\n    usm.basicURLParse(`${v}:`, {\n      url: this._url,\n      stateOverride: \"scheme start\"\n    });\n  }\n  get username() {\n    return this._url.username;\n  }\n  set username(v) {\n    if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n      return;\n    }\n    usm.setTheUsername(this._url, v);\n  }\n  get password() {\n    return this._url.password;\n  }\n  set password(v) {\n    if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n      return;\n    }\n    usm.setThePassword(this._url, v);\n  }\n  get host() {\n    const url = this._url;\n    if (url.host === null) {\n      return \"\";\n    }\n    if (url.port === null) {\n      return usm.serializeHost(url.host);\n    }\n    return `${usm.serializeHost(url.host)}:${usm.serializeInteger(url.port)}`;\n  }\n  set host(v) {\n    if (usm.hasAnOpaquePath(this._url)) {\n      return;\n    }\n    usm.basicURLParse(v, {\n      url: this._url,\n      stateOverride: \"host\"\n    });\n  }\n  get hostname() {\n    if (this._url.host === null) {\n      return \"\";\n    }\n    return usm.serializeHost(this._url.host);\n  }\n  set hostname(v) {\n    if (usm.hasAnOpaquePath(this._url)) {\n      return;\n    }\n    usm.basicURLParse(v, {\n      url: this._url,\n      stateOverride: \"hostname\"\n    });\n  }\n  get port() {\n    if (this._url.port === null) {\n      return \"\";\n    }\n    return usm.serializeInteger(this._url.port);\n  }\n  set port(v) {\n    if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n      return;\n    }\n    if (v === \"\") {\n      this._url.port = null;\n    } else {\n      usm.basicURLParse(v, {\n        url: this._url,\n        stateOverride: \"port\"\n      });\n    }\n  }\n  get pathname() {\n    return usm.serializePath(this._url);\n  }\n  set pathname(v) {\n    if (usm.hasAnOpaquePath(this._url)) {\n      return;\n    }\n    this._url.path = [];\n    usm.basicURLParse(v, {\n      url: this._url,\n      stateOverride: \"path start\"\n    });\n  }\n  get search() {\n    if (this._url.query === null || this._url.query === \"\") {\n      return \"\";\n    }\n    return `?${this._url.query}`;\n  }\n  set search(v) {\n    const url = this._url;\n    if (v === \"\") {\n      url.query = null;\n      this._query._list = [];\n      return;\n    }\n    const input = v[0] === \"?\" ? v.substring(1) : v;\n    url.query = \"\";\n    usm.basicURLParse(input, {\n      url,\n      stateOverride: \"query\"\n    });\n    this._query._list = urlencoded.parseUrlencodedString(input);\n  }\n  get searchParams() {\n    return this._query;\n  }\n  get hash() {\n    if (this._url.fragment === null || this._url.fragment === \"\") {\n      return \"\";\n    }\n    return `#${this._url.fragment}`;\n  }\n  set hash(v) {\n    if (v === \"\") {\n      this._url.fragment = null;\n      return;\n    }\n    const input = v[0] === \"#\" ? v.substring(1) : v;\n    this._url.fragment = \"\";\n    usm.basicURLParse(input, {\n      url: this._url,\n      stateOverride: \"fragment\"\n    });\n  }\n  toJSON() {\n    return this.href;\n  }\n};", "map": {"version": 3, "names": ["usm", "require", "u<PERSON><PERSON><PERSON>", "URLSearchParams", "exports", "implementation", "URLImpl", "constructor", "globalObject", "url", "base", "parsedBase", "undefined", "basicURLParse", "TypeError", "parsedURL", "baseURL", "query", "_url", "_query", "createImpl", "doNotStripQMark", "parse", "input", "canParse", "href", "serializeURL", "v", "_list", "splice", "parseUrlencodedString", "origin", "serializeURLOrigin", "protocol", "scheme", "stateOverride", "username", "cannotHaveAUsernamePasswordPort", "setTheUsername", "password", "setThePassword", "host", "port", "serializeHost", "serializeInteger", "hasAnOpaquePath", "hostname", "pathname", "serializePath", "path", "search", "substring", "searchParams", "hash", "fragment", "toJSON"], "sources": ["C:/Users/<USER>/node_modules/mongodb-connection-string-url/node_modules/whatwg-url/lib/URL-impl.js"], "sourcesContent": ["\"use strict\";\nconst usm = require(\"./url-state-machine\");\nconst urlencoded = require(\"./urlencoded\");\nconst URLSearchParams = require(\"./URLSearchParams\");\n\nexports.implementation = class URLImpl {\n  // Unlike the spec, we duplicate some code between the constructor and canParse, because we want to give useful error\n  // messages in the constructor that distinguish between the different causes of failure.\n  constructor(globalObject, [url, base]) {\n    let parsedBase = null;\n    if (base !== undefined) {\n      parsedBase = usm.basicURLParse(base);\n      if (parsedBase === null) {\n        throw new TypeError(`Invalid base URL: ${base}`);\n      }\n    }\n\n    const parsedURL = usm.basicURLParse(url, { baseURL: parsedBase });\n    if (parsedURL === null) {\n      throw new TypeError(`Invalid URL: ${url}`);\n    }\n\n    const query = parsedURL.query !== null ? parsedURL.query : \"\";\n\n    this._url = parsedURL;\n\n    // We cannot invoke the \"new URLSearchParams object\" algorithm without going through the constructor, which strips\n    // question mark by default. Therefore the doNotStripQMark hack is used.\n    this._query = URLSearchParams.createImpl(globalObject, [query], { doNotStripQMark: true });\n    this._query._url = this;\n  }\n\n  static parse(globalObject, input, base) {\n    try {\n      return new URLImpl(globalObject, [input, base]);\n    } catch {\n      return null;\n    }\n  }\n\n  static canParse(url, base) {\n    let parsedBase = null;\n    if (base !== undefined) {\n      parsedBase = usm.basicURLParse(base);\n      if (parsedBase === null) {\n        return false;\n      }\n    }\n\n    const parsedURL = usm.basicURLParse(url, { baseURL: parsedBase });\n    if (parsedURL === null) {\n      return false;\n    }\n\n    return true;\n  }\n\n  get href() {\n    return usm.serializeURL(this._url);\n  }\n\n  set href(v) {\n    const parsedURL = usm.basicURLParse(v);\n    if (parsedURL === null) {\n      throw new TypeError(`Invalid URL: ${v}`);\n    }\n\n    this._url = parsedURL;\n\n    this._query._list.splice(0);\n    const { query } = parsedURL;\n    if (query !== null) {\n      this._query._list = urlencoded.parseUrlencodedString(query);\n    }\n  }\n\n  get origin() {\n    return usm.serializeURLOrigin(this._url);\n  }\n\n  get protocol() {\n    return `${this._url.scheme}:`;\n  }\n\n  set protocol(v) {\n    usm.basicURLParse(`${v}:`, { url: this._url, stateOverride: \"scheme start\" });\n  }\n\n  get username() {\n    return this._url.username;\n  }\n\n  set username(v) {\n    if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n      return;\n    }\n\n    usm.setTheUsername(this._url, v);\n  }\n\n  get password() {\n    return this._url.password;\n  }\n\n  set password(v) {\n    if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n      return;\n    }\n\n    usm.setThePassword(this._url, v);\n  }\n\n  get host() {\n    const url = this._url;\n\n    if (url.host === null) {\n      return \"\";\n    }\n\n    if (url.port === null) {\n      return usm.serializeHost(url.host);\n    }\n\n    return `${usm.serializeHost(url.host)}:${usm.serializeInteger(url.port)}`;\n  }\n\n  set host(v) {\n    if (usm.hasAnOpaquePath(this._url)) {\n      return;\n    }\n\n    usm.basicURLParse(v, { url: this._url, stateOverride: \"host\" });\n  }\n\n  get hostname() {\n    if (this._url.host === null) {\n      return \"\";\n    }\n\n    return usm.serializeHost(this._url.host);\n  }\n\n  set hostname(v) {\n    if (usm.hasAnOpaquePath(this._url)) {\n      return;\n    }\n\n    usm.basicURLParse(v, { url: this._url, stateOverride: \"hostname\" });\n  }\n\n  get port() {\n    if (this._url.port === null) {\n      return \"\";\n    }\n\n    return usm.serializeInteger(this._url.port);\n  }\n\n  set port(v) {\n    if (usm.cannotHaveAUsernamePasswordPort(this._url)) {\n      return;\n    }\n\n    if (v === \"\") {\n      this._url.port = null;\n    } else {\n      usm.basicURLParse(v, { url: this._url, stateOverride: \"port\" });\n    }\n  }\n\n  get pathname() {\n    return usm.serializePath(this._url);\n  }\n\n  set pathname(v) {\n    if (usm.hasAnOpaquePath(this._url)) {\n      return;\n    }\n\n    this._url.path = [];\n    usm.basicURLParse(v, { url: this._url, stateOverride: \"path start\" });\n  }\n\n  get search() {\n    if (this._url.query === null || this._url.query === \"\") {\n      return \"\";\n    }\n\n    return `?${this._url.query}`;\n  }\n\n  set search(v) {\n    const url = this._url;\n\n    if (v === \"\") {\n      url.query = null;\n      this._query._list = [];\n      return;\n    }\n\n    const input = v[0] === \"?\" ? v.substring(1) : v;\n    url.query = \"\";\n    usm.basicURLParse(input, { url, stateOverride: \"query\" });\n    this._query._list = urlencoded.parseUrlencodedString(input);\n  }\n\n  get searchParams() {\n    return this._query;\n  }\n\n  get hash() {\n    if (this._url.fragment === null || this._url.fragment === \"\") {\n      return \"\";\n    }\n\n    return `#${this._url.fragment}`;\n  }\n\n  set hash(v) {\n    if (v === \"\") {\n      this._url.fragment = null;\n      return;\n    }\n\n    const input = v[0] === \"#\" ? v.substring(1) : v;\n    this._url.fragment = \"\";\n    usm.basicURLParse(input, { url: this._url, stateOverride: \"fragment\" });\n  }\n\n  toJSON() {\n    return this.href;\n  }\n};\n"], "mappings": "AAAA,YAAY;;AACZ,MAAMA,GAAG,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAC1C,MAAMC,UAAU,GAAGD,OAAO,CAAC,cAAc,CAAC;AAC1C,MAAME,eAAe,GAAGF,OAAO,CAAC,mBAAmB,CAAC;AAEpDG,OAAO,CAACC,cAAc,GAAG,MAAMC,OAAO,CAAC;EACrC;EACA;EACAC,WAAWA,CAACC,YAAY,EAAE,CAACC,GAAG,EAAEC,IAAI,CAAC,EAAE;IACrC,IAAIC,UAAU,GAAG,IAAI;IACrB,IAAID,IAAI,KAAKE,SAAS,EAAE;MACtBD,UAAU,GAAGX,GAAG,CAACa,aAAa,CAACH,IAAI,CAAC;MACpC,IAAIC,UAAU,KAAK,IAAI,EAAE;QACvB,MAAM,IAAIG,SAAS,CAAC,qBAAqBJ,IAAI,EAAE,CAAC;MAClD;IACF;IAEA,MAAMK,SAAS,GAAGf,GAAG,CAACa,aAAa,CAACJ,GAAG,EAAE;MAAEO,OAAO,EAAEL;IAAW,CAAC,CAAC;IACjE,IAAII,SAAS,KAAK,IAAI,EAAE;MACtB,MAAM,IAAID,SAAS,CAAC,gBAAgBL,GAAG,EAAE,CAAC;IAC5C;IAEA,MAAMQ,KAAK,GAAGF,SAAS,CAACE,KAAK,KAAK,IAAI,GAAGF,SAAS,CAACE,KAAK,GAAG,EAAE;IAE7D,IAAI,CAACC,IAAI,GAAGH,SAAS;;IAErB;IACA;IACA,IAAI,CAACI,MAAM,GAAGhB,eAAe,CAACiB,UAAU,CAACZ,YAAY,EAAE,CAACS,KAAK,CAAC,EAAE;MAAEI,eAAe,EAAE;IAAK,CAAC,CAAC;IAC1F,IAAI,CAACF,MAAM,CAACD,IAAI,GAAG,IAAI;EACzB;EAEA,OAAOI,KAAKA,CAACd,YAAY,EAAEe,KAAK,EAAEb,IAAI,EAAE;IACtC,IAAI;MACF,OAAO,IAAIJ,OAAO,CAACE,YAAY,EAAE,CAACe,KAAK,EAAEb,IAAI,CAAC,CAAC;IACjD,CAAC,CAAC,MAAM;MACN,OAAO,IAAI;IACb;EACF;EAEA,OAAOc,QAAQA,CAACf,GAAG,EAAEC,IAAI,EAAE;IACzB,IAAIC,UAAU,GAAG,IAAI;IACrB,IAAID,IAAI,KAAKE,SAAS,EAAE;MACtBD,UAAU,GAAGX,GAAG,CAACa,aAAa,CAACH,IAAI,CAAC;MACpC,IAAIC,UAAU,KAAK,IAAI,EAAE;QACvB,OAAO,KAAK;MACd;IACF;IAEA,MAAMI,SAAS,GAAGf,GAAG,CAACa,aAAa,CAACJ,GAAG,EAAE;MAAEO,OAAO,EAAEL;IAAW,CAAC,CAAC;IACjE,IAAII,SAAS,KAAK,IAAI,EAAE;MACtB,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb;EAEA,IAAIU,IAAIA,CAAA,EAAG;IACT,OAAOzB,GAAG,CAAC0B,YAAY,CAAC,IAAI,CAACR,IAAI,CAAC;EACpC;EAEA,IAAIO,IAAIA,CAACE,CAAC,EAAE;IACV,MAAMZ,SAAS,GAAGf,GAAG,CAACa,aAAa,CAACc,CAAC,CAAC;IACtC,IAAIZ,SAAS,KAAK,IAAI,EAAE;MACtB,MAAM,IAAID,SAAS,CAAC,gBAAgBa,CAAC,EAAE,CAAC;IAC1C;IAEA,IAAI,CAACT,IAAI,GAAGH,SAAS;IAErB,IAAI,CAACI,MAAM,CAACS,KAAK,CAACC,MAAM,CAAC,CAAC,CAAC;IAC3B,MAAM;MAAEZ;IAAM,CAAC,GAAGF,SAAS;IAC3B,IAAIE,KAAK,KAAK,IAAI,EAAE;MAClB,IAAI,CAACE,MAAM,CAACS,KAAK,GAAG1B,UAAU,CAAC4B,qBAAqB,CAACb,KAAK,CAAC;IAC7D;EACF;EAEA,IAAIc,MAAMA,CAAA,EAAG;IACX,OAAO/B,GAAG,CAACgC,kBAAkB,CAAC,IAAI,CAACd,IAAI,CAAC;EAC1C;EAEA,IAAIe,QAAQA,CAAA,EAAG;IACb,OAAO,GAAG,IAAI,CAACf,IAAI,CAACgB,MAAM,GAAG;EAC/B;EAEA,IAAID,QAAQA,CAACN,CAAC,EAAE;IACd3B,GAAG,CAACa,aAAa,CAAC,GAAGc,CAAC,GAAG,EAAE;MAAElB,GAAG,EAAE,IAAI,CAACS,IAAI;MAAEiB,aAAa,EAAE;IAAe,CAAC,CAAC;EAC/E;EAEA,IAAIC,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAAClB,IAAI,CAACkB,QAAQ;EAC3B;EAEA,IAAIA,QAAQA,CAACT,CAAC,EAAE;IACd,IAAI3B,GAAG,CAACqC,+BAA+B,CAAC,IAAI,CAACnB,IAAI,CAAC,EAAE;MAClD;IACF;IAEAlB,GAAG,CAACsC,cAAc,CAAC,IAAI,CAACpB,IAAI,EAAES,CAAC,CAAC;EAClC;EAEA,IAAIY,QAAQA,CAAA,EAAG;IACb,OAAO,IAAI,CAACrB,IAAI,CAACqB,QAAQ;EAC3B;EAEA,IAAIA,QAAQA,CAACZ,CAAC,EAAE;IACd,IAAI3B,GAAG,CAACqC,+BAA+B,CAAC,IAAI,CAACnB,IAAI,CAAC,EAAE;MAClD;IACF;IAEAlB,GAAG,CAACwC,cAAc,CAAC,IAAI,CAACtB,IAAI,EAAES,CAAC,CAAC;EAClC;EAEA,IAAIc,IAAIA,CAAA,EAAG;IACT,MAAMhC,GAAG,GAAG,IAAI,CAACS,IAAI;IAErB,IAAIT,GAAG,CAACgC,IAAI,KAAK,IAAI,EAAE;MACrB,OAAO,EAAE;IACX;IAEA,IAAIhC,GAAG,CAACiC,IAAI,KAAK,IAAI,EAAE;MACrB,OAAO1C,GAAG,CAAC2C,aAAa,CAAClC,GAAG,CAACgC,IAAI,CAAC;IACpC;IAEA,OAAO,GAAGzC,GAAG,CAAC2C,aAAa,CAAClC,GAAG,CAACgC,IAAI,CAAC,IAAIzC,GAAG,CAAC4C,gBAAgB,CAACnC,GAAG,CAACiC,IAAI,CAAC,EAAE;EAC3E;EAEA,IAAID,IAAIA,CAACd,CAAC,EAAE;IACV,IAAI3B,GAAG,CAAC6C,eAAe,CAAC,IAAI,CAAC3B,IAAI,CAAC,EAAE;MAClC;IACF;IAEAlB,GAAG,CAACa,aAAa,CAACc,CAAC,EAAE;MAAElB,GAAG,EAAE,IAAI,CAACS,IAAI;MAAEiB,aAAa,EAAE;IAAO,CAAC,CAAC;EACjE;EAEA,IAAIW,QAAQA,CAAA,EAAG;IACb,IAAI,IAAI,CAAC5B,IAAI,CAACuB,IAAI,KAAK,IAAI,EAAE;MAC3B,OAAO,EAAE;IACX;IAEA,OAAOzC,GAAG,CAAC2C,aAAa,CAAC,IAAI,CAACzB,IAAI,CAACuB,IAAI,CAAC;EAC1C;EAEA,IAAIK,QAAQA,CAACnB,CAAC,EAAE;IACd,IAAI3B,GAAG,CAAC6C,eAAe,CAAC,IAAI,CAAC3B,IAAI,CAAC,EAAE;MAClC;IACF;IAEAlB,GAAG,CAACa,aAAa,CAACc,CAAC,EAAE;MAAElB,GAAG,EAAE,IAAI,CAACS,IAAI;MAAEiB,aAAa,EAAE;IAAW,CAAC,CAAC;EACrE;EAEA,IAAIO,IAAIA,CAAA,EAAG;IACT,IAAI,IAAI,CAACxB,IAAI,CAACwB,IAAI,KAAK,IAAI,EAAE;MAC3B,OAAO,EAAE;IACX;IAEA,OAAO1C,GAAG,CAAC4C,gBAAgB,CAAC,IAAI,CAAC1B,IAAI,CAACwB,IAAI,CAAC;EAC7C;EAEA,IAAIA,IAAIA,CAACf,CAAC,EAAE;IACV,IAAI3B,GAAG,CAACqC,+BAA+B,CAAC,IAAI,CAACnB,IAAI,CAAC,EAAE;MAClD;IACF;IAEA,IAAIS,CAAC,KAAK,EAAE,EAAE;MACZ,IAAI,CAACT,IAAI,CAACwB,IAAI,GAAG,IAAI;IACvB,CAAC,MAAM;MACL1C,GAAG,CAACa,aAAa,CAACc,CAAC,EAAE;QAAElB,GAAG,EAAE,IAAI,CAACS,IAAI;QAAEiB,aAAa,EAAE;MAAO,CAAC,CAAC;IACjE;EACF;EAEA,IAAIY,QAAQA,CAAA,EAAG;IACb,OAAO/C,GAAG,CAACgD,aAAa,CAAC,IAAI,CAAC9B,IAAI,CAAC;EACrC;EAEA,IAAI6B,QAAQA,CAACpB,CAAC,EAAE;IACd,IAAI3B,GAAG,CAAC6C,eAAe,CAAC,IAAI,CAAC3B,IAAI,CAAC,EAAE;MAClC;IACF;IAEA,IAAI,CAACA,IAAI,CAAC+B,IAAI,GAAG,EAAE;IACnBjD,GAAG,CAACa,aAAa,CAACc,CAAC,EAAE;MAAElB,GAAG,EAAE,IAAI,CAACS,IAAI;MAAEiB,aAAa,EAAE;IAAa,CAAC,CAAC;EACvE;EAEA,IAAIe,MAAMA,CAAA,EAAG;IACX,IAAI,IAAI,CAAChC,IAAI,CAACD,KAAK,KAAK,IAAI,IAAI,IAAI,CAACC,IAAI,CAACD,KAAK,KAAK,EAAE,EAAE;MACtD,OAAO,EAAE;IACX;IAEA,OAAO,IAAI,IAAI,CAACC,IAAI,CAACD,KAAK,EAAE;EAC9B;EAEA,IAAIiC,MAAMA,CAACvB,CAAC,EAAE;IACZ,MAAMlB,GAAG,GAAG,IAAI,CAACS,IAAI;IAErB,IAAIS,CAAC,KAAK,EAAE,EAAE;MACZlB,GAAG,CAACQ,KAAK,GAAG,IAAI;MAChB,IAAI,CAACE,MAAM,CAACS,KAAK,GAAG,EAAE;MACtB;IACF;IAEA,MAAML,KAAK,GAAGI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGA,CAAC,CAACwB,SAAS,CAAC,CAAC,CAAC,GAAGxB,CAAC;IAC/ClB,GAAG,CAACQ,KAAK,GAAG,EAAE;IACdjB,GAAG,CAACa,aAAa,CAACU,KAAK,EAAE;MAAEd,GAAG;MAAE0B,aAAa,EAAE;IAAQ,CAAC,CAAC;IACzD,IAAI,CAAChB,MAAM,CAACS,KAAK,GAAG1B,UAAU,CAAC4B,qBAAqB,CAACP,KAAK,CAAC;EAC7D;EAEA,IAAI6B,YAAYA,CAAA,EAAG;IACjB,OAAO,IAAI,CAACjC,MAAM;EACpB;EAEA,IAAIkC,IAAIA,CAAA,EAAG;IACT,IAAI,IAAI,CAACnC,IAAI,CAACoC,QAAQ,KAAK,IAAI,IAAI,IAAI,CAACpC,IAAI,CAACoC,QAAQ,KAAK,EAAE,EAAE;MAC5D,OAAO,EAAE;IACX;IAEA,OAAO,IAAI,IAAI,CAACpC,IAAI,CAACoC,QAAQ,EAAE;EACjC;EAEA,IAAID,IAAIA,CAAC1B,CAAC,EAAE;IACV,IAAIA,CAAC,KAAK,EAAE,EAAE;MACZ,IAAI,CAACT,IAAI,CAACoC,QAAQ,GAAG,IAAI;MACzB;IACF;IAEA,MAAM/B,KAAK,GAAGI,CAAC,CAAC,CAAC,CAAC,KAAK,GAAG,GAAGA,CAAC,CAACwB,SAAS,CAAC,CAAC,CAAC,GAAGxB,CAAC;IAC/C,IAAI,CAACT,IAAI,CAACoC,QAAQ,GAAG,EAAE;IACvBtD,GAAG,CAACa,aAAa,CAACU,KAAK,EAAE;MAAEd,GAAG,EAAE,IAAI,CAACS,IAAI;MAAEiB,aAAa,EAAE;IAAW,CAAC,CAAC;EACzE;EAEAoB,MAAMA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC9B,IAAI;EAClB;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}