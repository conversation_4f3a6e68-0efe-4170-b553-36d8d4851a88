{"ast": null, "code": "import deepmerge from '@mui/utils/deepmerge';\nimport createTheme from './createTheme';\nexport default function createMuiStrictModeTheme(options) {\n  for (var _len = arguments.length, args = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    args[_key - 1] = arguments[_key];\n  }\n  return createTheme(deepmerge({\n    unstable_strictMode: true\n  }, options), ...args);\n}", "map": {"version": 3, "names": ["deepmerge", "createTheme", "createMuiStrictModeTheme", "options", "_len", "arguments", "length", "args", "Array", "_key", "unstable_strictMode"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/material/styles/createMuiStrictModeTheme.js"], "sourcesContent": ["import deepmerge from '@mui/utils/deepmerge';\nimport createTheme from './createTheme';\nexport default function createMuiStrictModeTheme(options, ...args) {\n  return createTheme(deepmerge({\n    unstable_strictMode: true\n  }, options), ...args);\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,WAAW,MAAM,eAAe;AACvC,eAAe,SAASC,wBAAwBA,CAACC,OAAO,EAAW;EAAA,SAAAC,IAAA,GAAAC,SAAA,CAAAC,MAAA,EAANC,IAAI,OAAAC,KAAA,CAAAJ,IAAA,OAAAA,IAAA,WAAAK,IAAA,MAAAA,IAAA,GAAAL,IAAA,EAAAK,IAAA;IAAJF,IAAI,CAAAE,IAAA,QAAAJ,SAAA,CAAAI,IAAA;EAAA;EAC/D,OAAOR,WAAW,CAACD,SAAS,CAAC;IAC3BU,mBAAmB,EAAE;EACvB,CAAC,EAAEP,OAAO,CAAC,EAAE,GAAGI,IAAI,CAAC;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}