{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.MongoDBAWS = void 0;\nconst BSON = require(\"../../bson\");\nconst deps_1 = require(\"../../deps\");\nconst error_1 = require(\"../../error\");\nconst utils_1 = require(\"../../utils\");\nconst auth_provider_1 = require(\"./auth_provider\");\nconst aws_temporary_credentials_1 = require(\"./aws_temporary_credentials\");\nconst mongo_credentials_1 = require(\"./mongo_credentials\");\nconst providers_1 = require(\"./providers\");\nconst ASCII_N = 110;\nconst bsonOptions = {\n  useBigInt64: false,\n  promoteLongs: true,\n  promoteValues: true,\n  promoteBuffers: false,\n  bsonRegExp: false\n};\nclass MongoDBAWS extends auth_provider_1.AuthProvider {\n  constructor(credentialProvider) {\n    super();\n    this.credentialProvider = credentialProvider;\n    this.credentialFetcher = aws_temporary_credentials_1.AWSTemporaryCredentialProvider.isAWSSDKInstalled ? new aws_temporary_credentials_1.AWSSDKCredentialProvider(credentialProvider) : new aws_temporary_credentials_1.LegacyAWSTemporaryCredentialProvider();\n  }\n  async auth(authContext) {\n    const {\n      connection\n    } = authContext;\n    if (!authContext.credentials) {\n      throw new error_1.MongoMissingCredentialsError('AuthContext must provide credentials.');\n    }\n    if ('kModuleError' in deps_1.aws4) {\n      throw deps_1.aws4['kModuleError'];\n    }\n    const {\n      sign\n    } = deps_1.aws4;\n    if ((0, utils_1.maxWireVersion)(connection) < 9) {\n      throw new error_1.MongoCompatibilityError('MONGODB-AWS authentication requires MongoDB version 4.4 or later');\n    }\n    if (!authContext.credentials.username) {\n      authContext.credentials = await makeTempCredentials(authContext.credentials, this.credentialFetcher);\n    }\n    const {\n      credentials\n    } = authContext;\n    const accessKeyId = credentials.username;\n    const secretAccessKey = credentials.password;\n    // Allow the user to specify an AWS session token for authentication with temporary credentials.\n    const sessionToken = credentials.mechanismProperties.AWS_SESSION_TOKEN;\n    // If all three defined, include sessionToken, else include username and pass, else no credentials\n    const awsCredentials = accessKeyId && secretAccessKey && sessionToken ? {\n      accessKeyId,\n      secretAccessKey,\n      sessionToken\n    } : accessKeyId && secretAccessKey ? {\n      accessKeyId,\n      secretAccessKey\n    } : undefined;\n    const db = credentials.source;\n    const nonce = await (0, utils_1.randomBytes)(32);\n    // All messages between MongoDB clients and servers are sent as BSON objects\n    // in the payload field of saslStart and saslContinue.\n    const saslStart = {\n      saslStart: 1,\n      mechanism: 'MONGODB-AWS',\n      payload: BSON.serialize({\n        r: nonce,\n        p: ASCII_N\n      }, bsonOptions)\n    };\n    const saslStartResponse = await connection.command((0, utils_1.ns)(`${db}.$cmd`), saslStart, undefined);\n    const serverResponse = BSON.deserialize(saslStartResponse.payload.buffer, bsonOptions);\n    const host = serverResponse.h;\n    const serverNonce = serverResponse.s.buffer;\n    if (serverNonce.length !== 64) {\n      // TODO(NODE-3483)\n      throw new error_1.MongoRuntimeError(`Invalid server nonce length ${serverNonce.length}, expected 64`);\n    }\n    if (!utils_1.ByteUtils.equals(serverNonce.subarray(0, nonce.byteLength), nonce)) {\n      // throw because the serverNonce's leading 32 bytes must equal the client nonce's 32 bytes\n      // https://github.com/mongodb/specifications/blob/master/source/auth/auth.md#conversation-5\n      // TODO(NODE-3483)\n      throw new error_1.MongoRuntimeError('Server nonce does not begin with client nonce');\n    }\n    if (host.length < 1 || host.length > 255 || host.indexOf('..') !== -1) {\n      // TODO(NODE-3483)\n      throw new error_1.MongoRuntimeError(`Server returned an invalid host: \"${host}\"`);\n    }\n    const body = 'Action=GetCallerIdentity&Version=2011-06-15';\n    const options = sign({\n      method: 'POST',\n      host,\n      region: deriveRegion(serverResponse.h),\n      service: 'sts',\n      headers: {\n        'Content-Type': 'application/x-www-form-urlencoded',\n        'Content-Length': body.length,\n        'X-MongoDB-Server-Nonce': utils_1.ByteUtils.toBase64(serverNonce),\n        'X-MongoDB-GS2-CB-Flag': 'n'\n      },\n      path: '/',\n      body\n    }, awsCredentials);\n    const payload = {\n      a: options.headers.Authorization,\n      d: options.headers['X-Amz-Date']\n    };\n    if (sessionToken) {\n      payload.t = sessionToken;\n    }\n    const saslContinue = {\n      saslContinue: 1,\n      conversationId: saslStartResponse.conversationId,\n      payload: BSON.serialize(payload, bsonOptions)\n    };\n    await connection.command((0, utils_1.ns)(`${db}.$cmd`), saslContinue, undefined);\n  }\n}\nexports.MongoDBAWS = MongoDBAWS;\nasync function makeTempCredentials(credentials, awsCredentialFetcher) {\n  function makeMongoCredentialsFromAWSTemp(creds) {\n    // The AWS session token (creds.Token) may or may not be set.\n    if (!creds.AccessKeyId || !creds.SecretAccessKey) {\n      throw new error_1.MongoMissingCredentialsError('Could not obtain temporary MONGODB-AWS credentials');\n    }\n    return new mongo_credentials_1.MongoCredentials({\n      username: creds.AccessKeyId,\n      password: creds.SecretAccessKey,\n      source: credentials.source,\n      mechanism: providers_1.AuthMechanism.MONGODB_AWS,\n      mechanismProperties: {\n        AWS_SESSION_TOKEN: creds.Token\n      }\n    });\n  }\n  const temporaryCredentials = await awsCredentialFetcher.getCredentials();\n  return makeMongoCredentialsFromAWSTemp(temporaryCredentials);\n}\nfunction deriveRegion(host) {\n  const parts = host.split('.');\n  if (parts.length === 1 || parts[1] === 'amazonaws') {\n    return 'us-east-1';\n  }\n  return parts[1];\n}", "map": {"version": 3, "names": ["BSON", "require", "deps_1", "error_1", "utils_1", "auth_provider_1", "aws_temporary_credentials_1", "mongo_credentials_1", "providers_1", "ASCII_N", "bsonOptions", "useBigInt64", "promoteLongs", "promoteValues", "promoteBuffers", "bsonRegExp", "MongoDBAWS", "<PERSON>th<PERSON><PERSON><PERSON>", "constructor", "credentialProvider", "credentialFetcher", "AWSTemporaryCredentialProvider", "isAWSSDKInstalled", "AWSSDKCredentialProvider", "LegacyAWSTemporaryCredentialProvider", "auth", "authContext", "connection", "credentials", "MongoMissingCredentialsError", "aws4", "sign", "maxWireVersion", "MongoCompatibilityError", "username", "makeTempCredentials", "accessKeyId", "secretAccessKey", "password", "sessionToken", "mechanismProperties", "AWS_SESSION_TOKEN", "awsCredentials", "undefined", "db", "source", "nonce", "randomBytes", "saslStart", "mechanism", "payload", "serialize", "r", "p", "saslStartResponse", "command", "ns", "serverResponse", "deserialize", "buffer", "host", "h", "serverNonce", "s", "length", "MongoRuntimeError", "ByteUtils", "equals", "subarray", "byteLength", "indexOf", "body", "options", "method", "region", "deriveRegion", "service", "headers", "toBase64", "path", "a", "Authorization", "d", "t", "saslContinue", "conversationId", "exports", "awsCredentialFetcher", "makeMongoCredentialsFromAWSTemp", "creds", "AccessKeyId", "SecretAccess<PERSON>ey", "MongoCredentials", "AuthMechanism", "MONGODB_AWS", "Token", "temporaryCredentials", "getCredentials", "parts", "split"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\auth\\mongodb_aws.ts"], "sourcesContent": ["import type { Binary, BSONSerializeOptions } from '../../bson';\nimport * as BSON from '../../bson';\nimport { aws4 } from '../../deps';\nimport {\n  MongoCompatibilityError,\n  MongoMissingCredentialsError,\n  MongoRuntimeError\n} from '../../error';\nimport { ByteUtils, maxWireVersion, ns, randomBytes } from '../../utils';\nimport { type AuthContext, AuthProvider } from './auth_provider';\nimport {\n  type AWSCredentialProvider,\n  AWSSDKCredentialProvider,\n  type AWSTempCredentials,\n  AWSTemporaryCredentialProvider,\n  LegacyAWSTemporaryCredentialProvider\n} from './aws_temporary_credentials';\nimport { MongoCredentials } from './mongo_credentials';\nimport { AuthMechanism } from './providers';\n\nconst ASCII_N = 110;\nconst bsonOptions: BSONSerializeOptions = {\n  useBigInt64: false,\n  promoteLongs: true,\n  promoteValues: true,\n  promoteBuffers: false,\n  bsonRegExp: false\n};\n\ninterface AWSSaslContinuePayload {\n  a: string;\n  d: string;\n  t?: string;\n}\n\nexport class MongoDBAWS extends AuthProvider {\n  private credentialFetcher: AWSTemporaryCredentialProvider;\n  private credentialProvider?: AWSCredentialProvider;\n\n  constructor(credentialProvider?: AWSCredentialProvider) {\n    super();\n\n    this.credentialProvider = credentialProvider;\n    this.credentialFetcher = AWSTemporaryCredentialProvider.isAWSSDKInstalled\n      ? new AWSSDKCredentialProvider(credentialProvider)\n      : new LegacyAWSTemporaryCredentialProvider();\n  }\n\n  override async auth(authContext: AuthContext): Promise<void> {\n    const { connection } = authContext;\n    if (!authContext.credentials) {\n      throw new MongoMissingCredentialsError('AuthContext must provide credentials.');\n    }\n\n    if ('kModuleError' in aws4) {\n      throw aws4['kModuleError'];\n    }\n    const { sign } = aws4;\n\n    if (maxWireVersion(connection) < 9) {\n      throw new MongoCompatibilityError(\n        'MONGODB-AWS authentication requires MongoDB version 4.4 or later'\n      );\n    }\n\n    if (!authContext.credentials.username) {\n      authContext.credentials = await makeTempCredentials(\n        authContext.credentials,\n        this.credentialFetcher\n      );\n    }\n\n    const { credentials } = authContext;\n\n    const accessKeyId = credentials.username;\n    const secretAccessKey = credentials.password;\n    // Allow the user to specify an AWS session token for authentication with temporary credentials.\n    const sessionToken = credentials.mechanismProperties.AWS_SESSION_TOKEN;\n\n    // If all three defined, include sessionToken, else include username and pass, else no credentials\n    const awsCredentials =\n      accessKeyId && secretAccessKey && sessionToken\n        ? { accessKeyId, secretAccessKey, sessionToken }\n        : accessKeyId && secretAccessKey\n          ? { accessKeyId, secretAccessKey }\n          : undefined;\n\n    const db = credentials.source;\n    const nonce = await randomBytes(32);\n\n    // All messages between MongoDB clients and servers are sent as BSON objects\n    // in the payload field of saslStart and saslContinue.\n    const saslStart = {\n      saslStart: 1,\n      mechanism: 'MONGODB-AWS',\n      payload: BSON.serialize({ r: nonce, p: ASCII_N }, bsonOptions)\n    };\n\n    const saslStartResponse = await connection.command(ns(`${db}.$cmd`), saslStart, undefined);\n\n    const serverResponse = BSON.deserialize(saslStartResponse.payload.buffer, bsonOptions) as {\n      s: Binary;\n      h: string;\n    };\n    const host = serverResponse.h;\n    const serverNonce = serverResponse.s.buffer;\n    if (serverNonce.length !== 64) {\n      // TODO(NODE-3483)\n      throw new MongoRuntimeError(`Invalid server nonce length ${serverNonce.length}, expected 64`);\n    }\n\n    if (!ByteUtils.equals(serverNonce.subarray(0, nonce.byteLength), nonce)) {\n      // throw because the serverNonce's leading 32 bytes must equal the client nonce's 32 bytes\n      // https://github.com/mongodb/specifications/blob/master/source/auth/auth.md#conversation-5\n\n      // TODO(NODE-3483)\n      throw new MongoRuntimeError('Server nonce does not begin with client nonce');\n    }\n\n    if (host.length < 1 || host.length > 255 || host.indexOf('..') !== -1) {\n      // TODO(NODE-3483)\n      throw new MongoRuntimeError(`Server returned an invalid host: \"${host}\"`);\n    }\n\n    const body = 'Action=GetCallerIdentity&Version=2011-06-15';\n    const options = sign(\n      {\n        method: 'POST',\n        host,\n        region: deriveRegion(serverResponse.h),\n        service: 'sts',\n        headers: {\n          'Content-Type': 'application/x-www-form-urlencoded',\n          'Content-Length': body.length,\n          'X-MongoDB-Server-Nonce': ByteUtils.toBase64(serverNonce),\n          'X-MongoDB-GS2-CB-Flag': 'n'\n        },\n        path: '/',\n        body\n      },\n      awsCredentials\n    );\n\n    const payload: AWSSaslContinuePayload = {\n      a: options.headers.Authorization,\n      d: options.headers['X-Amz-Date']\n    };\n\n    if (sessionToken) {\n      payload.t = sessionToken;\n    }\n\n    const saslContinue = {\n      saslContinue: 1,\n      conversationId: saslStartResponse.conversationId,\n      payload: BSON.serialize(payload, bsonOptions)\n    };\n\n    await connection.command(ns(`${db}.$cmd`), saslContinue, undefined);\n  }\n}\n\nasync function makeTempCredentials(\n  credentials: MongoCredentials,\n  awsCredentialFetcher: AWSTemporaryCredentialProvider\n): Promise<MongoCredentials> {\n  function makeMongoCredentialsFromAWSTemp(creds: AWSTempCredentials) {\n    // The AWS session token (creds.Token) may or may not be set.\n    if (!creds.AccessKeyId || !creds.SecretAccessKey) {\n      throw new MongoMissingCredentialsError('Could not obtain temporary MONGODB-AWS credentials');\n    }\n\n    return new MongoCredentials({\n      username: creds.AccessKeyId,\n      password: creds.SecretAccessKey,\n      source: credentials.source,\n      mechanism: AuthMechanism.MONGODB_AWS,\n      mechanismProperties: {\n        AWS_SESSION_TOKEN: creds.Token\n      }\n    });\n  }\n  const temporaryCredentials = await awsCredentialFetcher.getCredentials();\n\n  return makeMongoCredentialsFromAWSTemp(temporaryCredentials);\n}\n\nfunction deriveRegion(host: string) {\n  const parts = host.split('.');\n  if (parts.length === 1 || parts[1] === 'amazonaws') {\n    return 'us-east-1';\n  }\n\n  return parts[1];\n}\n"], "mappings": ";;;;;;AACA,MAAAA,IAAA,GAAAC,OAAA;AACA,MAAAC,MAAA,GAAAD,OAAA;AACA,MAAAE,OAAA,GAAAF,OAAA;AAKA,MAAAG,OAAA,GAAAH,OAAA;AACA,MAAAI,eAAA,GAAAJ,OAAA;AACA,MAAAK,2BAAA,GAAAL,OAAA;AAOA,MAAAM,mBAAA,GAAAN,OAAA;AACA,MAAAO,WAAA,GAAAP,OAAA;AAEA,MAAMQ,OAAO,GAAG,GAAG;AACnB,MAAMC,WAAW,GAAyB;EACxCC,WAAW,EAAE,KAAK;EAClBC,YAAY,EAAE,IAAI;EAClBC,aAAa,EAAE,IAAI;EACnBC,cAAc,EAAE,KAAK;EACrBC,UAAU,EAAE;CACb;AAQD,MAAaC,UAAW,SAAQX,eAAA,CAAAY,YAAY;EAI1CC,YAAYC,kBAA0C;IACpD,KAAK,EAAE;IAEP,IAAI,CAACA,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,iBAAiB,GAAGd,2BAAA,CAAAe,8BAA8B,CAACC,iBAAiB,GACrE,IAAIhB,2BAAA,CAAAiB,wBAAwB,CAACJ,kBAAkB,CAAC,GAChD,IAAIb,2BAAA,CAAAkB,oCAAoC,EAAE;EAChD;EAES,MAAMC,IAAIA,CAACC,WAAwB;IAC1C,MAAM;MAAEC;IAAU,CAAE,GAAGD,WAAW;IAClC,IAAI,CAACA,WAAW,CAACE,WAAW,EAAE;MAC5B,MAAM,IAAIzB,OAAA,CAAA0B,4BAA4B,CAAC,uCAAuC,CAAC;IACjF;IAEA,IAAI,cAAc,IAAI3B,MAAA,CAAA4B,IAAI,EAAE;MAC1B,MAAM5B,MAAA,CAAA4B,IAAI,CAAC,cAAc,CAAC;IAC5B;IACA,MAAM;MAAEC;IAAI,CAAE,GAAG7B,MAAA,CAAA4B,IAAI;IAErB,IAAI,IAAA1B,OAAA,CAAA4B,cAAc,EAACL,UAAU,CAAC,GAAG,CAAC,EAAE;MAClC,MAAM,IAAIxB,OAAA,CAAA8B,uBAAuB,CAC/B,kEAAkE,CACnE;IACH;IAEA,IAAI,CAACP,WAAW,CAACE,WAAW,CAACM,QAAQ,EAAE;MACrCR,WAAW,CAACE,WAAW,GAAG,MAAMO,mBAAmB,CACjDT,WAAW,CAACE,WAAW,EACvB,IAAI,CAACR,iBAAiB,CACvB;IACH;IAEA,MAAM;MAAEQ;IAAW,CAAE,GAAGF,WAAW;IAEnC,MAAMU,WAAW,GAAGR,WAAW,CAACM,QAAQ;IACxC,MAAMG,eAAe,GAAGT,WAAW,CAACU,QAAQ;IAC5C;IACA,MAAMC,YAAY,GAAGX,WAAW,CAACY,mBAAmB,CAACC,iBAAiB;IAEtE;IACA,MAAMC,cAAc,GAClBN,WAAW,IAAIC,eAAe,IAAIE,YAAY,GAC1C;MAAEH,WAAW;MAAEC,eAAe;MAAEE;IAAY,CAAE,GAC9CH,WAAW,IAAIC,eAAe,GAC5B;MAAED,WAAW;MAAEC;IAAe,CAAE,GAChCM,SAAS;IAEjB,MAAMC,EAAE,GAAGhB,WAAW,CAACiB,MAAM;IAC7B,MAAMC,KAAK,GAAG,MAAM,IAAA1C,OAAA,CAAA2C,WAAW,EAAC,EAAE,CAAC;IAEnC;IACA;IACA,MAAMC,SAAS,GAAG;MAChBA,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,aAAa;MACxBC,OAAO,EAAElD,IAAI,CAACmD,SAAS,CAAC;QAAEC,CAAC,EAAEN,KAAK;QAAEO,CAAC,EAAE5C;MAAO,CAAE,EAAEC,WAAW;KAC9D;IAED,MAAM4C,iBAAiB,GAAG,MAAM3B,UAAU,CAAC4B,OAAO,CAAC,IAAAnD,OAAA,CAAAoD,EAAE,EAAC,GAAGZ,EAAE,OAAO,CAAC,EAAEI,SAAS,EAAEL,SAAS,CAAC;IAE1F,MAAMc,cAAc,GAAGzD,IAAI,CAAC0D,WAAW,CAACJ,iBAAiB,CAACJ,OAAO,CAACS,MAAM,EAAEjD,WAAW,CAGpF;IACD,MAAMkD,IAAI,GAAGH,cAAc,CAACI,CAAC;IAC7B,MAAMC,WAAW,GAAGL,cAAc,CAACM,CAAC,CAACJ,MAAM;IAC3C,IAAIG,WAAW,CAACE,MAAM,KAAK,EAAE,EAAE;MAC7B;MACA,MAAM,IAAI7D,OAAA,CAAA8D,iBAAiB,CAAC,+BAA+BH,WAAW,CAACE,MAAM,eAAe,CAAC;IAC/F;IAEA,IAAI,CAAC5D,OAAA,CAAA8D,SAAS,CAACC,MAAM,CAACL,WAAW,CAACM,QAAQ,CAAC,CAAC,EAAEtB,KAAK,CAACuB,UAAU,CAAC,EAAEvB,KAAK,CAAC,EAAE;MACvE;MACA;MAEA;MACA,MAAM,IAAI3C,OAAA,CAAA8D,iBAAiB,CAAC,+CAA+C,CAAC;IAC9E;IAEA,IAAIL,IAAI,CAACI,MAAM,GAAG,CAAC,IAAIJ,IAAI,CAACI,MAAM,GAAG,GAAG,IAAIJ,IAAI,CAACU,OAAO,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE;MACrE;MACA,MAAM,IAAInE,OAAA,CAAA8D,iBAAiB,CAAC,qCAAqCL,IAAI,GAAG,CAAC;IAC3E;IAEA,MAAMW,IAAI,GAAG,6CAA6C;IAC1D,MAAMC,OAAO,GAAGzC,IAAI,CAClB;MACE0C,MAAM,EAAE,MAAM;MACdb,IAAI;MACJc,MAAM,EAAEC,YAAY,CAAClB,cAAc,CAACI,CAAC,CAAC;MACtCe,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE;QACP,cAAc,EAAE,mCAAmC;QACnD,gBAAgB,EAAEN,IAAI,CAACP,MAAM;QAC7B,wBAAwB,EAAE5D,OAAA,CAAA8D,SAAS,CAACY,QAAQ,CAAChB,WAAW,CAAC;QACzD,uBAAuB,EAAE;OAC1B;MACDiB,IAAI,EAAE,GAAG;MACTR;KACD,EACD7B,cAAc,CACf;IAED,MAAMQ,OAAO,GAA2B;MACtC8B,CAAC,EAAER,OAAO,CAACK,OAAO,CAACI,aAAa;MAChCC,CAAC,EAAEV,OAAO,CAACK,OAAO,CAAC,YAAY;KAChC;IAED,IAAItC,YAAY,EAAE;MAChBW,OAAO,CAACiC,CAAC,GAAG5C,YAAY;IAC1B;IAEA,MAAM6C,YAAY,GAAG;MACnBA,YAAY,EAAE,CAAC;MACfC,cAAc,EAAE/B,iBAAiB,CAAC+B,cAAc;MAChDnC,OAAO,EAAElD,IAAI,CAACmD,SAAS,CAACD,OAAO,EAAExC,WAAW;KAC7C;IAED,MAAMiB,UAAU,CAAC4B,OAAO,CAAC,IAAAnD,OAAA,CAAAoD,EAAE,EAAC,GAAGZ,EAAE,OAAO,CAAC,EAAEwC,YAAY,EAAEzC,SAAS,CAAC;EACrE;;AA5HF2C,OAAA,CAAAtE,UAAA,GAAAA,UAAA;AA+HA,eAAemB,mBAAmBA,CAChCP,WAA6B,EAC7B2D,oBAAoD;EAEpD,SAASC,+BAA+BA,CAACC,KAAyB;IAChE;IACA,IAAI,CAACA,KAAK,CAACC,WAAW,IAAI,CAACD,KAAK,CAACE,eAAe,EAAE;MAChD,MAAM,IAAIxF,OAAA,CAAA0B,4BAA4B,CAAC,oDAAoD,CAAC;IAC9F;IAEA,OAAO,IAAItB,mBAAA,CAAAqF,gBAAgB,CAAC;MAC1B1D,QAAQ,EAAEuD,KAAK,CAACC,WAAW;MAC3BpD,QAAQ,EAAEmD,KAAK,CAACE,eAAe;MAC/B9C,MAAM,EAAEjB,WAAW,CAACiB,MAAM;MAC1BI,SAAS,EAAEzC,WAAA,CAAAqF,aAAa,CAACC,WAAW;MACpCtD,mBAAmB,EAAE;QACnBC,iBAAiB,EAAEgD,KAAK,CAACM;;KAE5B,CAAC;EACJ;EACA,MAAMC,oBAAoB,GAAG,MAAMT,oBAAoB,CAACU,cAAc,EAAE;EAExE,OAAOT,+BAA+B,CAACQ,oBAAoB,CAAC;AAC9D;AAEA,SAASrB,YAAYA,CAACf,IAAY;EAChC,MAAMsC,KAAK,GAAGtC,IAAI,CAACuC,KAAK,CAAC,GAAG,CAAC;EAC7B,IAAID,KAAK,CAAClC,MAAM,KAAK,CAAC,IAAIkC,KAAK,CAAC,CAAC,CAAC,KAAK,WAAW,EAAE;IAClD,OAAO,WAAW;EACpB;EAEA,OAAOA,KAAK,CAAC,CAAC,CAAC;AACjB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}