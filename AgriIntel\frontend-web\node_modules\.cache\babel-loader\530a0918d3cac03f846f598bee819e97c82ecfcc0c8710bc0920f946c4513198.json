{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.Plain = void 0;\nconst bson_1 = require(\"../../bson\");\nconst error_1 = require(\"../../error\");\nconst utils_1 = require(\"../../utils\");\nconst auth_provider_1 = require(\"./auth_provider\");\nclass Plain extends auth_provider_1.AuthProvider {\n  async auth(authContext) {\n    const {\n      connection,\n      credentials\n    } = authContext;\n    if (!credentials) {\n      throw new error_1.MongoMissingCredentialsError('AuthContext must provide credentials.');\n    }\n    const {\n      username,\n      password\n    } = credentials;\n    const payload = new bson_1.Binary(Buffer.from(`\\x00${username}\\x00${password}`));\n    const command = {\n      saslStart: 1,\n      mechanism: 'PLAIN',\n      payload: payload,\n      autoAuthorize: 1\n    };\n    await connection.command((0, utils_1.ns)('$external.$cmd'), command, undefined);\n  }\n}\nexports.Plain = Plain;", "map": {"version": 3, "names": ["bson_1", "require", "error_1", "utils_1", "auth_provider_1", "Plain", "<PERSON>th<PERSON><PERSON><PERSON>", "auth", "authContext", "connection", "credentials", "MongoMissingCredentialsError", "username", "password", "payload", "Binary", "<PERSON><PERSON><PERSON>", "from", "command", "saslStart", "mechanism", "autoAuthorize", "ns", "undefined", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\auth\\plain.ts"], "sourcesContent": ["import { Binary } from '../../bson';\nimport { MongoMissingCredentialsError } from '../../error';\nimport { ns } from '../../utils';\nimport { type AuthContext, AuthProvider } from './auth_provider';\n\nexport class Plain extends AuthProvider {\n  override async auth(authContext: AuthContext): Promise<void> {\n    const { connection, credentials } = authContext;\n    if (!credentials) {\n      throw new MongoMissingCredentialsError('AuthContext must provide credentials.');\n    }\n\n    const { username, password } = credentials;\n\n    const payload = new Binary(Buffer.from(`\\x00${username}\\x00${password}`));\n    const command = {\n      saslStart: 1,\n      mechanism: 'PLAIN',\n      payload: payload,\n      autoAuthorize: 1\n    };\n\n    await connection.command(ns('$external.$cmd'), command, undefined);\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,MAAA,GAAAC,OAAA;AACA,MAAAC,OAAA,GAAAD,OAAA;AACA,MAAAE,OAAA,GAAAF,OAAA;AACA,MAAAG,eAAA,GAAAH,OAAA;AAEA,MAAaI,KAAM,SAAQD,eAAA,CAAAE,YAAY;EAC5B,MAAMC,IAAIA,CAACC,WAAwB;IAC1C,MAAM;MAAEC,UAAU;MAAEC;IAAW,CAAE,GAAGF,WAAW;IAC/C,IAAI,CAACE,WAAW,EAAE;MAChB,MAAM,IAAIR,OAAA,CAAAS,4BAA4B,CAAC,uCAAuC,CAAC;IACjF;IAEA,MAAM;MAAEC,QAAQ;MAAEC;IAAQ,CAAE,GAAGH,WAAW;IAE1C,MAAMI,OAAO,GAAG,IAAId,MAAA,CAAAe,MAAM,CAACC,MAAM,CAACC,IAAI,CAAC,OAAOL,QAAQ,OAAOC,QAAQ,EAAE,CAAC,CAAC;IACzE,MAAMK,OAAO,GAAG;MACdC,SAAS,EAAE,CAAC;MACZC,SAAS,EAAE,OAAO;MAClBN,OAAO,EAAEA,OAAO;MAChBO,aAAa,EAAE;KAChB;IAED,MAAMZ,UAAU,CAACS,OAAO,CAAC,IAAAf,OAAA,CAAAmB,EAAE,EAAC,gBAAgB,CAAC,EAAEJ,OAAO,EAAEK,SAAS,CAAC;EACpE;;AAlBFC,OAAA,CAAAnB,KAAA,GAAAA,KAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}