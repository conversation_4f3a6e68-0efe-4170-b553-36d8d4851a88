{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ListCollectionsOperation = void 0;\nconst responses_1 = require(\"../cmap/wire_protocol/responses\");\nconst utils_1 = require(\"../utils\");\nconst command_1 = require(\"./command\");\nconst operation_1 = require(\"./operation\");\n/** @internal */\nclass ListCollectionsOperation extends command_1.CommandOperation {\n  constructor(db, filter, options) {\n    super(db, options);\n    this.options = {\n      ...options\n    };\n    delete this.options.writeConcern;\n    this.db = db;\n    this.filter = filter;\n    this.nameOnly = !!this.options.nameOnly;\n    this.authorizedCollections = !!this.options.authorizedCollections;\n    if (typeof this.options.batchSize === 'number') {\n      this.batchSize = this.options.batchSize;\n    }\n  }\n  get commandName() {\n    return 'listCollections';\n  }\n  async execute(server, session, timeoutContext) {\n    return await super.executeCommand(server, session, this.generateCommand((0, utils_1.maxWireVersion)(server)), timeoutContext, responses_1.CursorResponse);\n  }\n  /* This is here for the purpose of unit testing the final command that gets sent. */\n  generateCommand(wireVersion) {\n    const command = {\n      listCollections: 1,\n      filter: this.filter,\n      cursor: this.batchSize ? {\n        batchSize: this.batchSize\n      } : {},\n      nameOnly: this.nameOnly,\n      authorizedCollections: this.authorizedCollections\n    };\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (wireVersion >= 9 && this.options.comment !== undefined) {\n      command.comment = this.options.comment;\n    }\n    return command;\n  }\n}\nexports.ListCollectionsOperation = ListCollectionsOperation;\n(0, operation_1.defineAspects)(ListCollectionsOperation, [operation_1.Aspect.READ_OPERATION, operation_1.Aspect.RETRYABLE, operation_1.Aspect.CURSOR_CREATING]);", "map": {"version": 3, "names": ["responses_1", "require", "utils_1", "command_1", "operation_1", "ListCollectionsOperation", "CommandOperation", "constructor", "db", "filter", "options", "writeConcern", "nameOnly", "authorizedCollections", "batchSize", "commandName", "execute", "server", "session", "timeoutContext", "executeCommand", "generateCommand", "maxWireVersion", "CursorResponse", "wireVersion", "command", "listCollections", "cursor", "comment", "undefined", "exports", "defineAspects", "Aspect", "READ_OPERATION", "RETRYABLE", "CURSOR_CREATING"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\list_collections.ts"], "sourcesContent": ["import type { Binary, Document } from '../bson';\nimport { CursorResponse } from '../cmap/wire_protocol/responses';\nimport { type CursorTimeoutContext, type CursorTimeoutMode } from '../cursor/abstract_cursor';\nimport type { Db } from '../db';\nimport { type Abortable } from '../mongo_types';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { maxWireVersion } from '../utils';\nimport { CommandOperation, type CommandOperationOptions } from './command';\nimport { Aspect, defineAspects } from './operation';\n\n/** @public */\nexport interface ListCollectionsOptions\n  extends Omit<CommandOperationOptions, 'writeConcern'>,\n    Abortable {\n  /** Since 4.0: If true, will only return the collection name in the response, and will omit additional info */\n  nameOnly?: boolean;\n  /** Since 4.0: If true and nameOnly is true, allows a user without the required privilege (i.e. listCollections action on the database) to run the command when access control is enforced. */\n  authorizedCollections?: boolean;\n  /** The batchSize for the returned command cursor or if pre 2.8 the systems batch collection */\n  batchSize?: number;\n  /** @internal */\n  timeoutMode?: CursorTimeoutMode;\n\n  /** @internal */\n  timeoutContext?: CursorTimeoutContext;\n}\n\n/** @internal */\nexport class ListCollectionsOperation extends CommandOperation<CursorResponse> {\n  /**\n   * @remarks WriteConcern can still be present on the options because\n   * we inherit options from the client/db/collection.  The\n   * key must be present on the options in order to delete it.\n   * This allows typescript to delete the key but will\n   * not allow a writeConcern to be assigned as a property on options.\n   */\n  override options: ListCollectionsOptions & { writeConcern?: never };\n  db: Db;\n  filter: Document;\n  nameOnly: boolean;\n  authorizedCollections: boolean;\n  batchSize?: number;\n\n  constructor(db: Db, filter: Document, options?: ListCollectionsOptions) {\n    super(db, options);\n\n    this.options = { ...options };\n    delete this.options.writeConcern;\n    this.db = db;\n    this.filter = filter;\n    this.nameOnly = !!this.options.nameOnly;\n    this.authorizedCollections = !!this.options.authorizedCollections;\n\n    if (typeof this.options.batchSize === 'number') {\n      this.batchSize = this.options.batchSize;\n    }\n  }\n\n  override get commandName() {\n    return 'listCollections' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<CursorResponse> {\n    return await super.executeCommand(\n      server,\n      session,\n      this.generateCommand(maxWireVersion(server)),\n      timeoutContext,\n      CursorResponse\n    );\n  }\n\n  /* This is here for the purpose of unit testing the final command that gets sent. */\n  generateCommand(wireVersion: number): Document {\n    const command: Document = {\n      listCollections: 1,\n      filter: this.filter,\n      cursor: this.batchSize ? { batchSize: this.batchSize } : {},\n      nameOnly: this.nameOnly,\n      authorizedCollections: this.authorizedCollections\n    };\n\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (wireVersion >= 9 && this.options.comment !== undefined) {\n      command.comment = this.options.comment;\n    }\n\n    return command;\n  }\n}\n\n/** @public */\nexport interface CollectionInfo extends Document {\n  name: string;\n  type?: string;\n  options?: Document;\n  info?: {\n    readOnly?: false;\n    uuid?: Binary;\n  };\n  idIndex?: Document;\n}\n\ndefineAspects(ListCollectionsOperation, [\n  Aspect.READ_OPERATION,\n  Aspect.RETRYABLE,\n  Aspect.CURSOR_CREATING\n]);\n"], "mappings": ";;;;;;AACA,MAAAA,WAAA,GAAAC,OAAA;AAOA,MAAAC,OAAA,GAAAD,OAAA;AACA,MAAAE,SAAA,GAAAF,OAAA;AACA,MAAAG,WAAA,GAAAH,OAAA;AAmBA;AACA,MAAaI,wBAAyB,SAAQF,SAAA,CAAAG,gBAAgC;EAe5EC,YAAYC,EAAM,EAAEC,MAAgB,EAAEC,OAAgC;IACpE,KAAK,CAACF,EAAE,EAAEE,OAAO,CAAC;IAElB,IAAI,CAACA,OAAO,GAAG;MAAE,GAAGA;IAAO,CAAE;IAC7B,OAAO,IAAI,CAACA,OAAO,CAACC,YAAY;IAChC,IAAI,CAACH,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACG,QAAQ,GAAG,CAAC,CAAC,IAAI,CAACF,OAAO,CAACE,QAAQ;IACvC,IAAI,CAACC,qBAAqB,GAAG,CAAC,CAAC,IAAI,CAACH,OAAO,CAACG,qBAAqB;IAEjE,IAAI,OAAO,IAAI,CAACH,OAAO,CAACI,SAAS,KAAK,QAAQ,EAAE;MAC9C,IAAI,CAACA,SAAS,GAAG,IAAI,CAACJ,OAAO,CAACI,SAAS;IACzC;EACF;EAEA,IAAaC,WAAWA,CAAA;IACtB,OAAO,iBAA0B;EACnC;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,OAAO,MAAM,KAAK,CAACC,cAAc,CAC/BH,MAAM,EACNC,OAAO,EACP,IAAI,CAACG,eAAe,CAAC,IAAAnB,OAAA,CAAAoB,cAAc,EAACL,MAAM,CAAC,CAAC,EAC5CE,cAAc,EACdnB,WAAA,CAAAuB,cAAc,CACf;EACH;EAEA;EACAF,eAAeA,CAACG,WAAmB;IACjC,MAAMC,OAAO,GAAa;MACxBC,eAAe,EAAE,CAAC;MAClBjB,MAAM,EAAE,IAAI,CAACA,MAAM;MACnBkB,MAAM,EAAE,IAAI,CAACb,SAAS,GAAG;QAAEA,SAAS,EAAE,IAAI,CAACA;MAAS,CAAE,GAAG,EAAE;MAC3DF,QAAQ,EAAE,IAAI,CAACA,QAAQ;MACvBC,qBAAqB,EAAE,IAAI,CAACA;KAC7B;IAED;IACA;IACA,IAAIW,WAAW,IAAI,CAAC,IAAI,IAAI,CAACd,OAAO,CAACkB,OAAO,KAAKC,SAAS,EAAE;MAC1DJ,OAAO,CAACG,OAAO,GAAG,IAAI,CAAClB,OAAO,CAACkB,OAAO;IACxC;IAEA,OAAOH,OAAO;EAChB;;AAjEFK,OAAA,CAAAzB,wBAAA,GAAAA,wBAAA;AAgFA,IAAAD,WAAA,CAAA2B,aAAa,EAAC1B,wBAAwB,EAAE,CACtCD,WAAA,CAAA4B,MAAM,CAACC,cAAc,EACrB7B,WAAA,CAAA4B,MAAM,CAACE,SAAS,EAChB9B,WAAA,CAAA4B,MAAM,CAACG,eAAe,CACvB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}