{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport * as React from 'react';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nexport const GRID_DEFAULT_STRATEGY = 'none';\nexport const GRID_STRATEGIES_PROCESSORS = {\n  rowTreeCreation: 'rowTree',\n  filtering: 'rowTree',\n  sorting: 'rowTree',\n  visibleRowsLookupCreation: 'rowTree'\n};\n/**\n * Implements a variant of the Strategy Pattern (see https://en.wikipedia.org/wiki/Strategy_pattern)\n *\n * More information and detailed example in (TODO add link to technical doc when ready)\n *\n * Some plugins contains custom logic that must only be applied if the right strategy is active.\n * For instance, the row grouping plugin has a custom filtering algorithm.\n * This algorithm must be applied by the filtering plugin if the row grouping is the current way of grouping rows,\n * but not if the tree data is the current way of grouping rows.\n *\n * =====================================================================================================================\n *\n * The plugin containing the custom logic must use:\n *\n * - `useGridRegisterStrategyProcessor` to register their processor.\n *   When the processor of the active strategy changes, it will fire `\"activeStrategyProcessorChange\"` to re-apply the processor.\n *\n * - `apiRef.current.setStrategyAvailability` to tell if their strategy can be used.\n *\n * =====================================================================================================================\n *\n * The plugin or component that needs to apply the custom logic of the current strategy must use:\n *\n * - `apiRef.current.applyStrategyProcessor` to run the processor of the active strategy for a given processor name.\n *\n * - the \"strategyAvailabilityChange\" event to update something when the active strategy changes.\n *    Warning: Be careful not to apply the processor several times.\n *    For instance \"rowsSet\" is fired by `useGridRows` whenever the active strategy changes.\n *    So listening to both would most likely run your logic twice.\n *\n * - The \"activeStrategyProcessorChange\" event to update something when the processor of the active strategy changes.\n *\n * =====================================================================================================================\n *\n * Each processor name is part of a strategy group which can only have one active strategy at the time.\n * For now, there is only one strategy group named `rowTree` which customize\n * - row tree creation algorithm.\n * - sorting algorithm.\n * - filtering algorithm.\n */\nexport const useGridStrategyProcessing = apiRef => {\n  const availableStrategies = React.useRef(new Map());\n  const strategiesCache = React.useRef({});\n  const registerStrategyProcessor = React.useCallback((strategyName, processorName, processor) => {\n    const cleanup = () => {\n      const _ref = strategiesCache.current[processorName],\n        otherProcessors = _objectWithoutPropertiesLoose(_ref, [strategyName].map(_toPropertyKey));\n      strategiesCache.current[processorName] = otherProcessors;\n    };\n    if (!strategiesCache.current[processorName]) {\n      strategiesCache.current[processorName] = {};\n    }\n    const groupPreProcessors = strategiesCache.current[processorName];\n    const previousProcessor = groupPreProcessors[strategyName];\n    groupPreProcessors[strategyName] = processor;\n    if (!previousProcessor || previousProcessor === processor) {\n      return cleanup;\n    }\n    if (strategyName === apiRef.current.getActiveStrategy(GRID_STRATEGIES_PROCESSORS[processorName])) {\n      apiRef.current.publishEvent('activeStrategyProcessorChange', processorName);\n    }\n    return cleanup;\n  }, [apiRef]);\n  const applyStrategyProcessor = React.useCallback((processorName, params) => {\n    const activeStrategy = apiRef.current.getActiveStrategy(GRID_STRATEGIES_PROCESSORS[processorName]);\n    if (activeStrategy == null) {\n      throw new Error(\"Can't apply a strategy processor before defining an active strategy\");\n    }\n    const groupCache = strategiesCache.current[processorName];\n    if (!groupCache || !groupCache[activeStrategy]) {\n      throw new Error(`No processor found for processor \"${processorName}\" on strategy \"${activeStrategy}\"`);\n    }\n    const processor = groupCache[activeStrategy];\n    return processor(params);\n  }, [apiRef]);\n  const getActiveStrategy = React.useCallback(strategyGroup => {\n    var _availableStrategyEnt;\n    const strategyEntries = Array.from(availableStrategies.current.entries());\n    const availableStrategyEntry = strategyEntries.find(([, strategy]) => {\n      if (strategy.group !== strategyGroup) {\n        return false;\n      }\n      return strategy.isAvailable();\n    });\n    return (_availableStrategyEnt = availableStrategyEntry == null ? void 0 : availableStrategyEntry[0]) != null ? _availableStrategyEnt : GRID_DEFAULT_STRATEGY;\n  }, []);\n  const setStrategyAvailability = React.useCallback((strategyGroup, strategyName, isAvailable) => {\n    availableStrategies.current.set(strategyName, {\n      group: strategyGroup,\n      isAvailable\n    });\n    apiRef.current.publishEvent('strategyAvailabilityChange');\n  }, [apiRef]);\n  const strategyProcessingApi = {\n    registerStrategyProcessor,\n    applyStrategyProcessor,\n    getActiveStrategy,\n    setStrategyAvailability\n  };\n  useGridApiMethod(apiRef, strategyProcessingApi, 'private');\n};", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_to<PERSON><PERSON><PERSON><PERSON><PERSON>", "React", "useGridApiMethod", "GRID_DEFAULT_STRATEGY", "GRID_STRATEGIES_PROCESSORS", "rowTreeCreation", "filtering", "sorting", "visibleRowsLookupCreation", "useGridStrategyProcessing", "apiRef", "availableStrategies", "useRef", "Map", "strategiesCache", "registerStrategyProcessor", "useCallback", "strategyName", "processorName", "processor", "cleanup", "_ref", "current", "otherProcessors", "map", "groupPreProcessors", "previousProcessor", "getActiveStrategy", "publishEvent", "applyStrategyProcessor", "params", "activeStrategy", "Error", "groupCache", "strategyGroup", "_availableStrategyEnt", "strategyEntries", "Array", "from", "entries", "availableStrategyEntry", "find", "strategy", "group", "isAvailable", "setStrategyAvailability", "set", "strategyProcessingApi"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/x-data-grid/hooks/core/strategyProcessing/useGridStrategyProcessing.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _toPropertyKey from \"@babel/runtime/helpers/esm/toPropertyKey\";\nimport * as React from 'react';\nimport { useGridApiMethod } from '../../utils/useGridApiMethod';\nexport const GRID_DEFAULT_STRATEGY = 'none';\nexport const GRID_STRATEGIES_PROCESSORS = {\n  rowTreeCreation: 'rowTree',\n  filtering: 'rowTree',\n  sorting: 'rowTree',\n  visibleRowsLookupCreation: 'rowTree'\n};\n/**\n * Implements a variant of the Strategy Pattern (see https://en.wikipedia.org/wiki/Strategy_pattern)\n *\n * More information and detailed example in (TODO add link to technical doc when ready)\n *\n * Some plugins contains custom logic that must only be applied if the right strategy is active.\n * For instance, the row grouping plugin has a custom filtering algorithm.\n * This algorithm must be applied by the filtering plugin if the row grouping is the current way of grouping rows,\n * but not if the tree data is the current way of grouping rows.\n *\n * =====================================================================================================================\n *\n * The plugin containing the custom logic must use:\n *\n * - `useGridRegisterStrategyProcessor` to register their processor.\n *   When the processor of the active strategy changes, it will fire `\"activeStrategyProcessorChange\"` to re-apply the processor.\n *\n * - `apiRef.current.setStrategyAvailability` to tell if their strategy can be used.\n *\n * =====================================================================================================================\n *\n * The plugin or component that needs to apply the custom logic of the current strategy must use:\n *\n * - `apiRef.current.applyStrategyProcessor` to run the processor of the active strategy for a given processor name.\n *\n * - the \"strategyAvailabilityChange\" event to update something when the active strategy changes.\n *    Warning: Be careful not to apply the processor several times.\n *    For instance \"rowsSet\" is fired by `useGridRows` whenever the active strategy changes.\n *    So listening to both would most likely run your logic twice.\n *\n * - The \"activeStrategyProcessorChange\" event to update something when the processor of the active strategy changes.\n *\n * =====================================================================================================================\n *\n * Each processor name is part of a strategy group which can only have one active strategy at the time.\n * For now, there is only one strategy group named `rowTree` which customize\n * - row tree creation algorithm.\n * - sorting algorithm.\n * - filtering algorithm.\n */\nexport const useGridStrategyProcessing = apiRef => {\n  const availableStrategies = React.useRef(new Map());\n  const strategiesCache = React.useRef({});\n  const registerStrategyProcessor = React.useCallback((strategyName, processorName, processor) => {\n    const cleanup = () => {\n      const _ref = strategiesCache.current[processorName],\n        otherProcessors = _objectWithoutPropertiesLoose(_ref, [strategyName].map(_toPropertyKey));\n      strategiesCache.current[processorName] = otherProcessors;\n    };\n    if (!strategiesCache.current[processorName]) {\n      strategiesCache.current[processorName] = {};\n    }\n    const groupPreProcessors = strategiesCache.current[processorName];\n    const previousProcessor = groupPreProcessors[strategyName];\n    groupPreProcessors[strategyName] = processor;\n    if (!previousProcessor || previousProcessor === processor) {\n      return cleanup;\n    }\n    if (strategyName === apiRef.current.getActiveStrategy(GRID_STRATEGIES_PROCESSORS[processorName])) {\n      apiRef.current.publishEvent('activeStrategyProcessorChange', processorName);\n    }\n    return cleanup;\n  }, [apiRef]);\n  const applyStrategyProcessor = React.useCallback((processorName, params) => {\n    const activeStrategy = apiRef.current.getActiveStrategy(GRID_STRATEGIES_PROCESSORS[processorName]);\n    if (activeStrategy == null) {\n      throw new Error(\"Can't apply a strategy processor before defining an active strategy\");\n    }\n    const groupCache = strategiesCache.current[processorName];\n    if (!groupCache || !groupCache[activeStrategy]) {\n      throw new Error(`No processor found for processor \"${processorName}\" on strategy \"${activeStrategy}\"`);\n    }\n    const processor = groupCache[activeStrategy];\n    return processor(params);\n  }, [apiRef]);\n  const getActiveStrategy = React.useCallback(strategyGroup => {\n    var _availableStrategyEnt;\n    const strategyEntries = Array.from(availableStrategies.current.entries());\n    const availableStrategyEntry = strategyEntries.find(([, strategy]) => {\n      if (strategy.group !== strategyGroup) {\n        return false;\n      }\n      return strategy.isAvailable();\n    });\n    return (_availableStrategyEnt = availableStrategyEntry == null ? void 0 : availableStrategyEntry[0]) != null ? _availableStrategyEnt : GRID_DEFAULT_STRATEGY;\n  }, []);\n  const setStrategyAvailability = React.useCallback((strategyGroup, strategyName, isAvailable) => {\n    availableStrategies.current.set(strategyName, {\n      group: strategyGroup,\n      isAvailable\n    });\n    apiRef.current.publishEvent('strategyAvailabilityChange');\n  }, [apiRef]);\n  const strategyProcessingApi = {\n    registerStrategyProcessor,\n    applyStrategyProcessor,\n    getActiveStrategy,\n    setStrategyAvailability\n  };\n  useGridApiMethod(apiRef, strategyProcessingApi, 'private');\n};"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,gBAAgB,QAAQ,8BAA8B;AAC/D,OAAO,MAAMC,qBAAqB,GAAG,MAAM;AAC3C,OAAO,MAAMC,0BAA0B,GAAG;EACxCC,eAAe,EAAE,SAAS;EAC1BC,SAAS,EAAE,SAAS;EACpBC,OAAO,EAAE,SAAS;EAClBC,yBAAyB,EAAE;AAC7B,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMC,yBAAyB,GAAGC,MAAM,IAAI;EACjD,MAAMC,mBAAmB,GAAGV,KAAK,CAACW,MAAM,CAAC,IAAIC,GAAG,CAAC,CAAC,CAAC;EACnD,MAAMC,eAAe,GAAGb,KAAK,CAACW,MAAM,CAAC,CAAC,CAAC,CAAC;EACxC,MAAMG,yBAAyB,GAAGd,KAAK,CAACe,WAAW,CAAC,CAACC,YAAY,EAAEC,aAAa,EAAEC,SAAS,KAAK;IAC9F,MAAMC,OAAO,GAAGA,CAAA,KAAM;MACpB,MAAMC,IAAI,GAAGP,eAAe,CAACQ,OAAO,CAACJ,aAAa,CAAC;QACjDK,eAAe,GAAGxB,6BAA6B,CAACsB,IAAI,EAAE,CAACJ,YAAY,CAAC,CAACO,GAAG,CAACxB,cAAc,CAAC,CAAC;MAC3Fc,eAAe,CAACQ,OAAO,CAACJ,aAAa,CAAC,GAAGK,eAAe;IAC1D,CAAC;IACD,IAAI,CAACT,eAAe,CAACQ,OAAO,CAACJ,aAAa,CAAC,EAAE;MAC3CJ,eAAe,CAACQ,OAAO,CAACJ,aAAa,CAAC,GAAG,CAAC,CAAC;IAC7C;IACA,MAAMO,kBAAkB,GAAGX,eAAe,CAACQ,OAAO,CAACJ,aAAa,CAAC;IACjE,MAAMQ,iBAAiB,GAAGD,kBAAkB,CAACR,YAAY,CAAC;IAC1DQ,kBAAkB,CAACR,YAAY,CAAC,GAAGE,SAAS;IAC5C,IAAI,CAACO,iBAAiB,IAAIA,iBAAiB,KAAKP,SAAS,EAAE;MACzD,OAAOC,OAAO;IAChB;IACA,IAAIH,YAAY,KAAKP,MAAM,CAACY,OAAO,CAACK,iBAAiB,CAACvB,0BAA0B,CAACc,aAAa,CAAC,CAAC,EAAE;MAChGR,MAAM,CAACY,OAAO,CAACM,YAAY,CAAC,+BAA+B,EAAEV,aAAa,CAAC;IAC7E;IACA,OAAOE,OAAO;EAChB,CAAC,EAAE,CAACV,MAAM,CAAC,CAAC;EACZ,MAAMmB,sBAAsB,GAAG5B,KAAK,CAACe,WAAW,CAAC,CAACE,aAAa,EAAEY,MAAM,KAAK;IAC1E,MAAMC,cAAc,GAAGrB,MAAM,CAACY,OAAO,CAACK,iBAAiB,CAACvB,0BAA0B,CAACc,aAAa,CAAC,CAAC;IAClG,IAAIa,cAAc,IAAI,IAAI,EAAE;MAC1B,MAAM,IAAIC,KAAK,CAAC,qEAAqE,CAAC;IACxF;IACA,MAAMC,UAAU,GAAGnB,eAAe,CAACQ,OAAO,CAACJ,aAAa,CAAC;IACzD,IAAI,CAACe,UAAU,IAAI,CAACA,UAAU,CAACF,cAAc,CAAC,EAAE;MAC9C,MAAM,IAAIC,KAAK,CAAC,qCAAqCd,aAAa,kBAAkBa,cAAc,GAAG,CAAC;IACxG;IACA,MAAMZ,SAAS,GAAGc,UAAU,CAACF,cAAc,CAAC;IAC5C,OAAOZ,SAAS,CAACW,MAAM,CAAC;EAC1B,CAAC,EAAE,CAACpB,MAAM,CAAC,CAAC;EACZ,MAAMiB,iBAAiB,GAAG1B,KAAK,CAACe,WAAW,CAACkB,aAAa,IAAI;IAC3D,IAAIC,qBAAqB;IACzB,MAAMC,eAAe,GAAGC,KAAK,CAACC,IAAI,CAAC3B,mBAAmB,CAACW,OAAO,CAACiB,OAAO,CAAC,CAAC,CAAC;IACzE,MAAMC,sBAAsB,GAAGJ,eAAe,CAACK,IAAI,CAAC,CAAC,GAAGC,QAAQ,CAAC,KAAK;MACpE,IAAIA,QAAQ,CAACC,KAAK,KAAKT,aAAa,EAAE;QACpC,OAAO,KAAK;MACd;MACA,OAAOQ,QAAQ,CAACE,WAAW,CAAC,CAAC;IAC/B,CAAC,CAAC;IACF,OAAO,CAACT,qBAAqB,GAAGK,sBAAsB,IAAI,IAAI,GAAG,KAAK,CAAC,GAAGA,sBAAsB,CAAC,CAAC,CAAC,KAAK,IAAI,GAAGL,qBAAqB,GAAGhC,qBAAqB;EAC9J,CAAC,EAAE,EAAE,CAAC;EACN,MAAM0C,uBAAuB,GAAG5C,KAAK,CAACe,WAAW,CAAC,CAACkB,aAAa,EAAEjB,YAAY,EAAE2B,WAAW,KAAK;IAC9FjC,mBAAmB,CAACW,OAAO,CAACwB,GAAG,CAAC7B,YAAY,EAAE;MAC5C0B,KAAK,EAAET,aAAa;MACpBU;IACF,CAAC,CAAC;IACFlC,MAAM,CAACY,OAAO,CAACM,YAAY,CAAC,4BAA4B,CAAC;EAC3D,CAAC,EAAE,CAAClB,MAAM,CAAC,CAAC;EACZ,MAAMqC,qBAAqB,GAAG;IAC5BhC,yBAAyB;IACzBc,sBAAsB;IACtBF,iBAAiB;IACjBkB;EACF,CAAC;EACD3C,gBAAgB,CAACQ,MAAM,EAAEqC,qBAAqB,EAAE,SAAS,CAAC;AAC5D,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}