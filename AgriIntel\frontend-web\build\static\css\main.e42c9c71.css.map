{"version": 3, "file": "static/css/main.e42c9c71.css", "mappings": "AAAA,wCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd,kCAAc,CAAd,uBAAc,CAAd,kBAAc,CAAd,kBAAc,CAAd,aAAc,CAAd,aAAc,CAAd,aAAc,CAAd,cAAc,CAAd,cAAc,CAAd,YAAc,CAAd,YAAc,CAAd,iBAAc,CAAd,qCAAc,CAAd,6BAAc,CAAd,4BAAc,CAAd,2BAAc,CAAd,cAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,iBAAc,CAAd,0BAAc,CAAd,2BAAc,CAAd,yBAAc,CAAd,iCAAc,CAAd,0BAAc,CAAd,qBAAc,CAAd,6BAAc,CAAd,WAAc,CAAd,iBAAc,CAAd,eAAc,CAAd,gBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,eAAc,CAAd,YAAc,CAAd,kBAAc,CAAd,oBAAc,CAAd,0BAAc,CAAd,wBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,sBAAc,CAAd,uBAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,oBAAc,CAAd,oBAAc,CAAd;;CAAc,CAAd,uCAAc,CAAd,qBAAc,CAAd,8BAAc,CAAd,wCAAc,CAAd,4BAAc,CAAd,uCAAc,CAAd,sCAAc,CAAd,8BAAc,CAAd,eAAc,CAAd,UAAc,CAAd,wBAAc,CAAd,QAAc,CAAd,uBAAc,CAAd,aAAc,CAAd,QAAc,CAAd,4DAAc,CAAd,gCAAc,CAAd,mCAAc,CAAd,mBAAc,CAAd,eAAc,CAAd,uBAAc,CAAd,2BAAc,CAAd,8CAAc,CAAd,oCAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,mBAAc,CAAd,qBAAc,CAAd,aAAc,CAAd,iBAAc,CAAd,sBAAc,CAAd,iBAAc,CAAd,aAAc,CAAd,8BAAc,CAAd,oBAAc,CAAd,aAAc,CAAd,mEAAc,CAAd,aAAc,CAAd,mBAAc,CAAd,cAAc,CAAd,+BAAc,CAAd,mBAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,QAAc,CAAd,SAAc,CAAd,iCAAc,CAAd,gHAAc,CAAd,wBAAc,CAAd,qBAAc,CAAd,4BAAc,CAAd,gCAAc,CAAd,+BAAc,CAAd,mEAAc,CAAd,0CAAc,CAAd,mBAAc,CAAd,mDAAc,CAAd,sDAAc,CAAd,YAAc,CAAd,yBAAc,CAAd,2DAAc,CAAd,iBAAc,CAAd,yBAAc,CAAd,0BAAc,CAAd,QAAc,CAAd,SAAc,CAAd,gBAAc,CAAd,wBAAc,CAAd,sDAAc,CAAd,SAAc,CAAd,mCAAc,CAAd,wBAAc,CAAd,4DAAc,CAAd,qBAAc,CAAd,qBAAc,CAAd,cAAc,CAAd,uDAAc,CAAd,2CAAc,CAAd,sBAAc,CAAd,mBAAc,CAAd,wBAAc,CAAd,wDAAc,CAAd,aAAc,CAAd,4CAAc,CAAd,uCAAc,CAAd,uBAAc,CAAd,oBAAc,CAAd,wDAAc,CAyBV,8CAAgF,CAAhF,sDAAgF,CAAhF,kBAAgF,CAAhF,kBAAgF,CAAhF,qBAAgF,CAAhF,gBAAgF,CAAhF,+CAAgF,CAAhF,kGAAgF,CAAhF,mBAAgF,CAAhF,iBAAgF,CAAhF,eAAgF,CAAhF,sBAAgF,CAAhF,mBAAgF,CAAhF,kBAAgF,CAAhF,yHAAgF,CAAhF,yFAAgF,CAAhF,uHAAgF,CAAhF,kDAAgF,CAAhF,6HAAgF,CAAhF,wGAAgF,CAAhF,kGAAgF,CAAhF,wFAAgF,CAAhF,uBAAgF,CAAhF,kBAAgF,CAAhF,8BAAgF,CAAhF,mBAAgF,CAAhF,wBAAgF,CAAhF,uDAAgF,CAAhF,UAAgF,CAAhF,+CAAgF,CAAhF,oCAAgF,CAAhF,wBAAgF,CAAhF,uDAAgF,CAAhF,sCAAgF,CAAhF,wDAAgF,CAwBhF,2BAA6D,CAA7D,iBAA6D,CAA7D,gEAA6D,CAA7D,kGAA6D,CAA7D,qBAA6D,CAA7D,wDAA6D,CAA7D,oBAA6D,CAA7D,wDAA6D,CAA7D,mBAA6D,CAA7D,gBAA6D,CAA7D,+CAA6D,CAA7D,kGAA6D,CAI7D,kCAAyC,CAAzC,uBAAyC,CAAzC,oBAAyC,CAAzC,wDAAyC,CAIzC,wBAJA,mBAIgB,CAQhB,iCAA4K,CAA5K,oBAA4K,CAA5K,wDAA4K,CAA5K,qBAA4K,CAA5K,gBAA4K,CAA5K,aAA4K,CAA5K,+BAA4K,CAA5K,mDAA4K,CAA5K,aAA4K,CAA5K,sDAA4K,CAA5K,6CAA4K,CAA5K,sDAA4K,CAA5K,+CAA4K,CAA5K,kGAA4K,CAA5K,uCAA4K,CAA5K,mBAA4K,CAA5K,6EAA4K,CAA5K,uDAA4K,CAA5K,uBAA4K,CAA5K,kBAA4K,CAA5K,sDAA4K,CAA5K,mBAA4K,EAI5K,+BAAmD,CAAnD,aAAmD,CAAnD,0DAAmD,CAAnD,iBAAmD,CAAnD,eAAmD,CAAnD,mBAAmD,CAAnD,oBAAmD,CAwDnD,8CAAmE,CAAnE,iBAAmE,CAAnE,mBAAmE,CAAnE,wBAAmE,CAAnE,wDAAmE,CAAnE,oBAAmE,CAAnE,uDAAmE,CAAnE,sBAAmE,CAAnE,aAAmE,CAAnE,8CAAmE,CAwBnE,yCAAuB,CAAvB,aAAuB,CAAvB,6CAAuB,CAIvB,yCAAsB,CAAtB,aAAsB,CAAtB,6CAAsB,CAvJ1B,qBAAmB,CAAnB,2BAAmB,CAAnB,2BAAmB,CAAnB,gBAAmB,CAAnB,yBAAmB,CAAnB,cAAmB,CAAnB,gBAAmB,CAAnB,YAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,yBAAmB,CAAnB,iBAAmB,CAAnB,yBAAmB,CAAnB,uBAAmB,CAAnB,wBAAmB,CAAnB,yBAAmB,CAAnB,yBAAmB,CAAnB,uBAAmB,CAAnB,sBAAmB,CAAnB,qBAAmB,CAAnB,uBAAmB,CAAnB,qBAAmB,CAAnB,oBAAmB,CAAnB,kBAAmB,CAAnB,gCAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,mBAAmB,CAAnB,kBAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,mBAAmB,CAAnB,8BAAmB,CAAnB,YAAmB,CAAnB,kBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,gBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,iBAAmB,CAAnB,kBAAmB,CAAnB,iBAAmB,CAAnB,iBAAmB,CAAnB,eAAmB,CAAnB,kBAAmB,CAAnB,oBAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,yBAAmB,CAAnB,cAAmB,CAAnB,kNAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,wMAAmB,CAAnB,0CAAmB,EAAnB,+CAAmB,CAAnB,8BAAmB,CAAnB,0DAAmB,CAAnB,+BAAmB,CAAnB,mCAAmB,CAAnB,gCAAmB,CAAnB,oCAAmB,CAAnB,sCAAmB,CAAnB,8CAAmB,CAAnB,kBAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,0GAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,+DAAmB,CAAnB,wGAAmB,CAAnB,+DAAmB,CAAnB,4GAAmB,CAAnB,yBAAmB,CAAnB,sBAAmB,CAAnB,kBAAmB,CAAnB,6BAAmB,CAAnB,kCAAmB,CAAnB,+BAAmB,CAAnB,iCAAmB,CAAnB,gFAAmB,CAAnB,wBAAmB,CAAnB,0BAAmB,CAAnB,iCAAmB,CAAnB,iCAAmB,CAAnB,kCAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,sCAAmB,CAAnB,oBAAmB,CAAnB,wDAAmB,CAAnB,yCAAmB,CAAnB,oBAAmB,CAAnB,uDAAmB,CAAnB,2CAAmB,CAAnB,oBAAmB,CAAnB,qDAAmB,CAAnB,sCAAmB,CAAnB,mCAAmB,CAAnB,iBAAmB,CAAnB,wDAAmB,CAAnB,wCAAmB,CAAnB,yBAAmB,CAAnB,4DAAmB,CAAnB,wCAAmB,CAAnB,yBAAmB,CAAnB,6DAAmB,CAAnB,yCAAmB,CAAnB,yBAAmB,CAAnB,2DAAmB,CAAnB,uCAAmB,CAAnB,yBAAmB,CAAnB,2DAAmB,CAAnB,0CAAmB,CAAnB,yBAAmB,CAAnB,2DAAmB,CAAnB,4CAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,qDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,8BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,iCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,iCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,4BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,6BAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,2BAAmB,CAAnB,qBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,uDAAmB,CAAnB,+BAAmB,CAAnB,wBAAmB,CAAnB,wDAAmB,CAAnB,gCAAmB,CAAnB,wBAAmB,CAAnB,sDAAmB,CAAnB,mCAAmB,CAAnB,6FAAmB,CAAnB,4EAAmB,CAAnB,yDAAmB,CAAnB,iEAAmB,CAAnB,uEAAmB,CAAnB,kBAAmB,CAAnB,mBAAmB,CAAnB,iBAAmB,CAAnB,mBAAmB,CAAnB,wBAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,oBAAmB,CAAnB,uBAAmB,CAAnB,kBAAmB,CAAnB,mDAAmB,CAAnB,8CAAmB,CAAnB,2CAAmB,CAAnB,4CAAmB,CAAnB,8CAAmB,CAAnB,0CAAmB,CAAnB,2BAAmB,CAAnB,0BAAmB,CAAnB,0BAAmB,CAAnB,8BAAmB,CAAnB,4BAAmB,CAAnB,0BAAmB,CAAnB,gBAAmB,CAAnB,4BAAmB,CAAnB,mBAAmB,CAAnB,wBAAmB,CAAnB,aAAmB,CAAnB,2BAAmB,CAAnB,mBAAmB,CAAnB,0BAAmB,CAAnB,mBAAmB,CAAnB,yBAAmB,CAAnB,gBAAmB,CAAnB,0BAAmB,CAAnB,+BAAmB,CAAnB,4BAAmB,CAAnB,8BAAmB,CAAnB,qCAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,+CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,kCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,mCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,qCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,qCAAmB,CAAnB,aAAmB,CAAnB,8CAAmB,CAAnB,qCAAmB,CAAnB,aAAmB,CAAnB,4CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,iCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,+BAAmB,CAAnB,UAAmB,CAAnB,+CAAmB,CAAnB,oCAAmB,CAAnB,aAAmB,CAAnB,6CAAmB,CAAnB,oBAAmB,CAAnB,sBAAmB,CAAnB,0EAAmB,CAAnB,iGAAmB,CAAnB,qEAAmB,CAAnB,kGAAmB,CAAnB,4CAAmB,CAAnB,sDAAmB,CAAnB,6EAAmB,CAAnB,kGAAmB,CAAnB,+CAAmB,CAAnB,kGAAmB,CAAnB,kHAAmB,CAAnB,wGAAmB,CAAnB,uFAAmB,CAAnB,wFAAmB,CAAnB,kHAAmB,CAAnB,wGAAmB,CAAnB,+BAAmB,CAAnB,mDAAmB,CAAnB,+BAAmB,CAAnB,yDAAmB,CAAnB,sCAAmB,CAAnB,wLAAmB,CAAnB,kMAAmB,CAAnB,6IAAmB,CAAnB,mMAAmB,CAAnB,kDAAmB,CAAnB,gEAAmB,CAAnB,kDAAmB,CAAnB,6IAAmB,CAAnB,yFAAmB,CAAnB,uHAAmB,CAAnB,kDAAmB,CAAnB,0EAAmB,CAAnB,kDAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,qCAAmB,CAAnB,0DAAmB,CAAnB,2DAAmB,CAkMnB,oBAEE,UAAW,CADX,SAEF,CAEA,0BACE,kBACF,CAEA,0BACE,kBAAmB,CACnB,iBACF,CAEA,gCACE,kBACF,CAGA,aACE,UACE,sBACF,CAEA,aACE,wBACF,CAEA,mBACE,uBACF,CACF,CAGA,SACE,gCACF,CAEA,UACE,8BACF,CAEA,WACE,+BACF,CAhPA,2CAiPA,CAjPA,wBAiPA,CAjPA,wDAiPA,CAjPA,8CAiPA,CAjPA,wBAiPA,CAjPA,uDAiPA,CAjPA,+CAiPA,CAjPA,aAiPA,CAjPA,+CAiPA,CAjPA,+CAiPA,CAjPA,aAiPA,CAjPA,4CAiPA,CAjPA,kDAiPA,CAjPA,aAiPA,CAjPA,8CAiPA,CAjPA,qFAiPA,CAjPA,+FAiPA,CAjPA,+CAiPA,CAjPA,kGAiPA,CAjPA,kDAiPA,CAjPA,kBAiPA,CAjPA,+HAiPA,CAjPA,wGAiPA,CAjPA,uEAiPA,CAjPA,wFAiPA,CAjPA,8CAiPA,CAjPA,kDAiPA,CAjPA,wDAiPA,CAjPA,sDAiPA,CAjPA,yDAiPA,CAjPA,yCAiPA,CAjPA,sDAiPA,CAjPA,iBAiPA,CAjPA,sBAiPA,CAjPA,6BAiPA,CAjPA,8DAiPA,CAjPA,mCAiPA,CAjPA,8BAiPA,CAjPA,oBAiPA,CAjPA,6BAiPA,CAjPA,oBAiPA,EAjPA,iDAiPA,EAjPA,qDAiPA,CAjPA,4BAiPA,CAjPA,wBAiPA,CAjPA,uCAiPA,CAjPA,6LAiPA,CAjPA,8DAiPA,CAjPA,8DAiPA,CAjPA,8DAiPA,CAjPA,2BAiPA,CAjPA,kBAiPA,EAjPA,2EAiPA,CAjPA,aAiPA,CAjPA,+CAiPA", "sources": ["index.css"], "sourcesContent": ["@tailwind base;\n@tailwind components;\n@tailwind utilities;\n\n@layer base {\n  html {\n    font-family: 'Inter', system-ui, sans-serif;\n  }\n  \n  body {\n    @apply bg-gray-50 text-gray-900;\n    font-family: 'Roboto', system-ui, sans-serif;\n  }\n  \n  * {\n    @apply border-gray-200;\n  }\n}\n\n@layer components {\n  .btn {\n    @apply inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-offset-2 transition-colors duration-200;\n  }\n  \n  .btn-primary {\n    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;\n  }\n  \n  .btn-secondary {\n    @apply btn bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;\n  }\n  \n  .btn-success {\n    @apply btn bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;\n  }\n  \n  .btn-warning {\n    @apply btn bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;\n  }\n  \n  .btn-danger {\n    @apply btn bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;\n  }\n  \n  .btn-outline {\n    @apply btn border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-primary-500;\n  }\n  \n  .card {\n    @apply bg-white rounded-lg shadow-soft border border-gray-200;\n  }\n  \n  .card-header {\n    @apply px-6 py-4 border-b border-gray-200;\n  }\n  \n  .card-body {\n    @apply px-6 py-4;\n  }\n  \n  .card-footer {\n    @apply px-6 py-4 border-t border-gray-200 bg-gray-50 rounded-b-lg;\n  }\n  \n  .form-input {\n    @apply block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-primary-500 focus:border-primary-500 sm:text-sm;\n  }\n  \n  .form-label {\n    @apply block text-sm font-medium text-gray-700 mb-1;\n  }\n  \n  .form-error {\n    @apply mt-1 text-sm text-danger-600;\n  }\n  \n  .table {\n    @apply min-w-full divide-y divide-gray-200;\n  }\n  \n  .table-header {\n    @apply bg-gray-50;\n  }\n  \n  .table-header-cell {\n    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;\n  }\n  \n  .table-body {\n    @apply bg-white divide-y divide-gray-200;\n  }\n  \n  .table-cell {\n    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;\n  }\n  \n  .badge {\n    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;\n  }\n  \n  .badge-primary {\n    @apply badge bg-primary-100 text-primary-800;\n  }\n  \n  .badge-success {\n    @apply badge bg-success-100 text-success-800;\n  }\n  \n  .badge-warning {\n    @apply badge bg-warning-100 text-warning-800;\n  }\n  \n  .badge-danger {\n    @apply badge bg-danger-100 text-danger-800;\n  }\n  \n  .badge-secondary {\n    @apply badge bg-secondary-100 text-secondary-800;\n  }\n  \n  .sidebar-nav-item {\n    @apply flex items-center px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-md transition-colors duration-200;\n  }\n  \n  .sidebar-nav-item.active {\n    @apply text-primary-600 bg-primary-50 border-r-2 border-primary-600;\n  }\n  \n  .loading-spinner {\n    @apply animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600;\n  }\n  \n  .stat-card {\n    @apply card p-6;\n  }\n  \n  .stat-value {\n    @apply text-3xl font-bold text-gray-900;\n  }\n  \n  .stat-label {\n    @apply text-sm font-medium text-gray-500;\n  }\n  \n  .stat-change {\n    @apply text-sm font-medium;\n  }\n  \n  .stat-change.positive {\n    @apply text-success-600;\n  }\n  \n  .stat-change.negative {\n    @apply text-danger-600;\n  }\n}\n\n@layer utilities {\n  .text-shadow {\n    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\n  }\n  \n  .text-shadow-lg {\n    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);\n  }\n  \n  .scrollbar-hide {\n    -ms-overflow-style: none; /* Internet Explorer 10+ */\n    scrollbar-width: none; /* Firefox */\n  }\n\n  .scrollbar-hide::-webkit-scrollbar {\n    display: none; /* Safari and Chrome */\n  }\n\n  /* Fallback for browsers that don't support scrollbar-width */\n  @supports not (scrollbar-width: none) {\n    .scrollbar-hide {\n      overflow: -moz-scrollbars-none; /* Old Firefox */\n    }\n  }\n  \n  .gradient-primary {\n    background: linear-gradient(135deg, #0f766e 0%, #14b8a6 100%);\n  }\n  \n  .gradient-secondary {\n    background: linear-gradient(135deg, #374151 0%, #6b7280 100%);\n  }\n  \n  .gradient-accent {\n    background: linear-gradient(135deg, #7c3aed 0%, #a855f7 100%);\n  }\n}\n\n/* Custom scrollbar */\n::-webkit-scrollbar {\n  width: 6px;\n  height: 6px;\n}\n\n::-webkit-scrollbar-track {\n  background: #f1f5f9;\n}\n\n::-webkit-scrollbar-thumb {\n  background: #cbd5e1;\n  border-radius: 3px;\n}\n\n::-webkit-scrollbar-thumb:hover {\n  background: #94a3b8;\n}\n\n/* Print styles */\n@media print {\n  .no-print {\n    display: none !important;\n  }\n  \n  .print-break {\n    page-break-before: always;\n  }\n  \n  .print-avoid-break {\n    page-break-inside: avoid;\n  }\n}\n\n/* Animation classes */\n.fade-in {\n  animation: fadeIn 0.5s ease-in-out;\n}\n\n.slide-in {\n  animation: slideIn 0.3s ease-out;\n}\n\n.bounce-in {\n  animation: bounceIn 0.6s ease-out;\n}\n"], "names": [], "sourceRoot": ""}