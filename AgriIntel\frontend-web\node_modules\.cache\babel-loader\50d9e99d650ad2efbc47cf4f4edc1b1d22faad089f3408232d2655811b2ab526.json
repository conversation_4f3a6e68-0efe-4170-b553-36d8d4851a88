{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.X509 = void 0;\nconst error_1 = require(\"../../error\");\nconst utils_1 = require(\"../../utils\");\nconst auth_provider_1 = require(\"./auth_provider\");\nclass X509 extends auth_provider_1.AuthProvider {\n  async prepare(handshakeDoc, authContext) {\n    const {\n      credentials\n    } = authContext;\n    if (!credentials) {\n      throw new error_1.MongoMissingCredentialsError('AuthContext must provide credentials.');\n    }\n    return {\n      ...handshakeDoc,\n      speculativeAuthenticate: x509AuthenticateCommand(credentials)\n    };\n  }\n  async auth(authContext) {\n    const connection = authContext.connection;\n    const credentials = authContext.credentials;\n    if (!credentials) {\n      throw new error_1.MongoMissingCredentialsError('AuthContext must provide credentials.');\n    }\n    const response = authContext.response;\n    if (response?.speculativeAuthenticate) {\n      return;\n    }\n    await connection.command((0, utils_1.ns)('$external.$cmd'), x509AuthenticateCommand(credentials), undefined);\n  }\n}\nexports.X509 = X509;\nfunction x509AuthenticateCommand(credentials) {\n  const command = {\n    authenticate: 1,\n    mechanism: 'MONGODB-X509'\n  };\n  if (credentials.username) {\n    command.user = credentials.username;\n  }\n  return command;\n}", "map": {"version": 3, "names": ["error_1", "require", "utils_1", "auth_provider_1", "X509", "<PERSON>th<PERSON><PERSON><PERSON>", "prepare", "handshakeDoc", "authContext", "credentials", "MongoMissingCredentialsError", "speculativeAuthenticate", "x509AuthenticateCommand", "auth", "connection", "response", "command", "ns", "undefined", "exports", "authenticate", "mechanism", "username", "user"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\auth\\x509.ts"], "sourcesContent": ["import type { Document } from '../../bson';\nimport { MongoMissingCredentialsError } from '../../error';\nimport { ns } from '../../utils';\nimport type { HandshakeDocument } from '../connect';\nimport { type AuthContext, AuthProvider } from './auth_provider';\nimport type { MongoCredentials } from './mongo_credentials';\n\nexport class X509 extends AuthProvider {\n  override async prepare(\n    handshakeDoc: HandshakeDocument,\n    authContext: AuthContext\n  ): Promise<HandshakeDocument> {\n    const { credentials } = authContext;\n    if (!credentials) {\n      throw new MongoMissingCredentialsError('AuthContext must provide credentials.');\n    }\n    return { ...handshakeDoc, speculativeAuthenticate: x509AuthenticateCommand(credentials) };\n  }\n\n  override async auth(authContext: AuthContext) {\n    const connection = authContext.connection;\n    const credentials = authContext.credentials;\n    if (!credentials) {\n      throw new MongoMissingCredentialsError('AuthContext must provide credentials.');\n    }\n    const response = authContext.response;\n\n    if (response?.speculativeAuthenticate) {\n      return;\n    }\n\n    await connection.command(ns('$external.$cmd'), x509AuthenticateCommand(credentials), undefined);\n  }\n}\n\nfunction x509AuthenticateCommand(credentials: MongoCredentials) {\n  const command: Document = { authenticate: 1, mechanism: 'MONGODB-X509' };\n  if (credentials.username) {\n    command.user = credentials.username;\n  }\n\n  return command;\n}\n"], "mappings": ";;;;;;AACA,MAAAA,OAAA,GAAAC,OAAA;AACA,MAAAC,OAAA,GAAAD,OAAA;AAEA,MAAAE,eAAA,GAAAF,OAAA;AAGA,MAAaG,IAAK,SAAQD,eAAA,CAAAE,YAAY;EAC3B,MAAMC,OAAOA,CACpBC,YAA+B,EAC/BC,WAAwB;IAExB,MAAM;MAAEC;IAAW,CAAE,GAAGD,WAAW;IACnC,IAAI,CAACC,WAAW,EAAE;MAChB,MAAM,IAAIT,OAAA,CAAAU,4BAA4B,CAAC,uCAAuC,CAAC;IACjF;IACA,OAAO;MAAE,GAAGH,YAAY;MAAEI,uBAAuB,EAAEC,uBAAuB,CAACH,WAAW;IAAC,CAAE;EAC3F;EAES,MAAMI,IAAIA,CAACL,WAAwB;IAC1C,MAAMM,UAAU,GAAGN,WAAW,CAACM,UAAU;IACzC,MAAML,WAAW,GAAGD,WAAW,CAACC,WAAW;IAC3C,IAAI,CAACA,WAAW,EAAE;MAChB,MAAM,IAAIT,OAAA,CAAAU,4BAA4B,CAAC,uCAAuC,CAAC;IACjF;IACA,MAAMK,QAAQ,GAAGP,WAAW,CAACO,QAAQ;IAErC,IAAIA,QAAQ,EAAEJ,uBAAuB,EAAE;MACrC;IACF;IAEA,MAAMG,UAAU,CAACE,OAAO,CAAC,IAAAd,OAAA,CAAAe,EAAE,EAAC,gBAAgB,CAAC,EAAEL,uBAAuB,CAACH,WAAW,CAAC,EAAES,SAAS,CAAC;EACjG;;AAzBFC,OAAA,CAAAf,IAAA,GAAAA,IAAA;AA4BA,SAASQ,uBAAuBA,CAACH,WAA6B;EAC5D,MAAMO,OAAO,GAAa;IAAEI,YAAY,EAAE,CAAC;IAAEC,SAAS,EAAE;EAAc,CAAE;EACxE,IAAIZ,WAAW,CAACa,QAAQ,EAAE;IACxBN,OAAO,CAACO,IAAI,GAAGd,WAAW,CAACa,QAAQ;EACrC;EAEA,OAAON,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}