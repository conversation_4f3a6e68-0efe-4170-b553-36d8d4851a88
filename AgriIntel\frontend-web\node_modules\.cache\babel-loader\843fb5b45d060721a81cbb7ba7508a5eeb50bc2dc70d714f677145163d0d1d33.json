{"ast": null, "code": "import * as React from 'react';\nimport { useFirstRender } from '../../utils/useFirstRender';\nexport const useGridRegisterPipeProcessor = (apiRef, group, callback) => {\n  const cleanup = React.useRef();\n  const id = React.useRef(`mui-${Math.round(Math.random() * 1e9)}`);\n  const registerPreProcessor = React.useCallback(() => {\n    cleanup.current = apiRef.current.registerPipeProcessor(group, id.current, callback);\n  }, [apiRef, callback, group]);\n  useFirstRender(() => {\n    registerPreProcessor();\n  });\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n    } else {\n      registerPreProcessor();\n    }\n    return () => {\n      if (cleanup.current) {\n        cleanup.current();\n        cleanup.current = null;\n      }\n    };\n  }, [registerPreProcessor]);\n};", "map": {"version": 3, "names": ["React", "useFirstRender", "useGridRegisterPipeProcessor", "apiRef", "group", "callback", "cleanup", "useRef", "id", "Math", "round", "random", "registerPreProcessor", "useCallback", "current", "registerPipeProcessor", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "useEffect"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/x-data-grid/hooks/core/pipeProcessing/useGridRegisterPipeProcessor.js"], "sourcesContent": ["import * as React from 'react';\nimport { useFirstRender } from '../../utils/useFirstRender';\nexport const useGridRegisterPipeProcessor = (apiRef, group, callback) => {\n  const cleanup = React.useRef();\n  const id = React.useRef(`mui-${Math.round(Math.random() * 1e9)}`);\n  const registerPreProcessor = React.useCallback(() => {\n    cleanup.current = apiRef.current.registerPipeProcessor(group, id.current, callback);\n  }, [apiRef, callback, group]);\n  useFirstRender(() => {\n    registerPreProcessor();\n  });\n  const isFirstRender = React.useRef(true);\n  React.useEffect(() => {\n    if (isFirstRender.current) {\n      isFirstRender.current = false;\n    } else {\n      registerPreProcessor();\n    }\n    return () => {\n      if (cleanup.current) {\n        cleanup.current();\n        cleanup.current = null;\n      }\n    };\n  }, [registerPreProcessor]);\n};"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,cAAc,QAAQ,4BAA4B;AAC3D,OAAO,MAAMC,4BAA4B,GAAGA,CAACC,MAAM,EAAEC,KAAK,EAAEC,QAAQ,KAAK;EACvE,MAAMC,OAAO,GAAGN,KAAK,CAACO,MAAM,CAAC,CAAC;EAC9B,MAAMC,EAAE,GAAGR,KAAK,CAACO,MAAM,CAAC,OAAOE,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,GAAG,CAAC,EAAE,CAAC;EACjE,MAAMC,oBAAoB,GAAGZ,KAAK,CAACa,WAAW,CAAC,MAAM;IACnDP,OAAO,CAACQ,OAAO,GAAGX,MAAM,CAACW,OAAO,CAACC,qBAAqB,CAACX,KAAK,EAAEI,EAAE,CAACM,OAAO,EAAET,QAAQ,CAAC;EACrF,CAAC,EAAE,CAACF,MAAM,EAAEE,QAAQ,EAAED,KAAK,CAAC,CAAC;EAC7BH,cAAc,CAAC,MAAM;IACnBW,oBAAoB,CAAC,CAAC;EACxB,CAAC,CAAC;EACF,MAAMI,aAAa,GAAGhB,KAAK,CAACO,MAAM,CAAC,IAAI,CAAC;EACxCP,KAAK,CAACiB,SAAS,CAAC,MAAM;IACpB,IAAID,aAAa,CAACF,OAAO,EAAE;MACzBE,aAAa,CAACF,OAAO,GAAG,KAAK;IAC/B,CAAC,MAAM;MACLF,oBAAoB,CAAC,CAAC;IACxB;IACA,OAAO,MAAM;MACX,IAAIN,OAAO,CAACQ,OAAO,EAAE;QACnBR,OAAO,CAACQ,OAAO,CAAC,CAAC;QACjBR,OAAO,CAACQ,OAAO,GAAG,IAAI;MACxB;IACF,CAAC;EACH,CAAC,EAAE,CAACF,oBAAoB,CAAC,CAAC;AAC5B,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}