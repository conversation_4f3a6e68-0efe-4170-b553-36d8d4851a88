{"ast": null, "code": "import{createSlice}from'@reduxjs/toolkit';const initialState={breedingRecords:[],birthRecords:[],heatRecords:[],selectedBreedingRecord:null,selectedBirthRecord:null,isLoading:false,error:null,statistics:null};const breedingSlice=createSlice({name:'breeding',initialState,reducers:{setBreedingRecords:(state,action)=>{state.breedingRecords=action.payload;},addBreedingRecord:(state,action)=>{state.breedingRecords.unshift(action.payload);},updateBreedingRecord:(state,action)=>{const index=state.breedingRecords.findIndex(record=>record._id===action.payload._id);if(index!==-1){state.breedingRecords[index]=action.payload;}},deleteBreedingRecord:(state,action)=>{state.breedingRecords=state.breedingRecords.filter(record=>record._id!==action.payload);},setBirthRecords:(state,action)=>{state.birthRecords=action.payload;},addBirthRecord:(state,action)=>{state.birthRecords.unshift(action.payload);},updateBirthRecord:(state,action)=>{const index=state.birthRecords.findIndex(record=>record._id===action.payload._id);if(index!==-1){state.birthRecords[index]=action.payload;}},deleteBirthRecord:(state,action)=>{state.birthRecords=state.birthRecords.filter(record=>record._id!==action.payload);},setHeatRecords:(state,action)=>{state.heatRecords=action.payload;},addHeatRecord:(state,action)=>{state.heatRecords.unshift(action.payload);},updateHeatRecord:(state,action)=>{const index=state.heatRecords.findIndex(record=>record._id===action.payload._id);if(index!==-1){state.heatRecords[index]=action.payload;}},deleteHeatRecord:(state,action)=>{state.heatRecords=state.heatRecords.filter(record=>record._id!==action.payload);},setSelectedBreedingRecord:(state,action)=>{state.selectedBreedingRecord=action.payload;},setSelectedBirthRecord:(state,action)=>{state.selectedBirthRecord=action.payload;},setLoading:(state,action)=>{state.isLoading=action.payload;},setError:(state,action)=>{state.error=action.payload;},clearError:state=>{state.error=null;},setStatistics:(state,action)=>{state.statistics=action.payload;}}});export const{setBreedingRecords,addBreedingRecord,updateBreedingRecord,deleteBreedingRecord,setBirthRecords,addBirthRecord,updateBirthRecord,deleteBirthRecord,setHeatRecords,addHeatRecord,updateHeatRecord,deleteHeatRecord,setSelectedBreedingRecord,setSelectedBirthRecord,setLoading,setError,clearError,setStatistics}=breedingSlice.actions;export default breedingSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "breedingRecords", "birthRecords", "heatRecords", "selectedBreedingRecord", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>ord", "isLoading", "error", "statistics", "breedingSlice", "name", "reducers", "setBreedingRecords", "state", "action", "payload", "addBreedingRecord", "unshift", "updateBreedingRecord", "index", "findIndex", "record", "_id", "deleteBreedingRecord", "filter", "setBirthRecords", "addBirthRecord", "updateBirthRecord", "deleteBirthRecord", "setHeatRecords", "addHeatRecord", "updateHeatRecord", "deleteHeatRecord", "setSelectedBreedingRecord", "setSelectedBirthRecord", "setLoading", "setError", "clearError", "setStatistics", "actions", "reducer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/store/slices/breedingSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\n\nexport interface BreedingRecord {\n  _id: string;\n  female: string;\n  male?: string;\n  breedingDate: string;\n  breedingMethod: 'natural' | 'artificial_insemination' | 'embryo_transfer';\n  expectedDueDate?: string;\n  actualBirthDate?: string;\n  pregnancyConfirmed: boolean;\n  pregnancyConfirmationDate?: string;\n  pregnancyConfirmationMethod?: 'ultrasound' | 'blood_test' | 'physical_exam';\n  complications?: string[];\n  veterinarian?: string;\n  semenSource?: {\n    bullId?: string;\n    semenBatch?: string;\n    supplier?: string;\n    cost?: number;\n  };\n  offspring?: string[];\n  notes?: string;\n  status: 'planned' | 'completed' | 'failed' | 'aborted';\n  createdBy: string;\n  updatedBy?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface BirthRecord {\n  _id: string;\n  mother: string;\n  father?: string;\n  birthDate: string;\n  birthTime?: string;\n  gestationPeriod?: number;\n  birthWeight?: number;\n  birthType: 'natural' | 'assisted' | 'cesarean';\n  complications?: string[];\n  veterinarian?: string;\n  offspring: Array<{\n    tagNumber: string;\n    gender: 'male' | 'female';\n    weight?: number;\n    health: 'healthy' | 'weak' | 'deceased';\n    notes?: string;\n  }>;\n  placentaExpelled: boolean;\n  placentaExpelledTime?: string;\n  postBirthTreatment?: string[];\n  notes?: string;\n  createdBy: string;\n  updatedBy?: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface HeatRecord {\n  _id: string;\n  animal: string;\n  heatDate: string;\n  heatIntensity: 'weak' | 'moderate' | 'strong';\n  heatDuration?: number;\n  behaviorSigns: string[];\n  physicalSigns: string[];\n  breedingRecommended: boolean;\n  breedingWindow?: {\n    start: string;\n    end: string;\n  };\n  bred: boolean;\n  breedingDate?: string;\n  notes?: string;\n  createdBy: string;\n  createdAt: string;\n  updatedAt: string;\n}\n\nexport interface BreedingState {\n  breedingRecords: BreedingRecord[];\n  birthRecords: BirthRecord[];\n  heatRecords: HeatRecord[];\n  selectedBreedingRecord: BreedingRecord | null;\n  selectedBirthRecord: BirthRecord | null;\n  isLoading: boolean;\n  error: string | null;\n  statistics: {\n    totalBreedings: number;\n    successfulBreedings: number;\n    pregnancyRate: number;\n    birthRate: number;\n    averageGestationPeriod: number;\n    upcomingBirths: number;\n    animalsInHeat: number;\n  } | null;\n}\n\nconst initialState: BreedingState = {\n  breedingRecords: [],\n  birthRecords: [],\n  heatRecords: [],\n  selectedBreedingRecord: null,\n  selectedBirthRecord: null,\n  isLoading: false,\n  error: null,\n  statistics: null,\n};\n\nconst breedingSlice = createSlice({\n  name: 'breeding',\n  initialState,\n  reducers: {\n    setBreedingRecords: (state, action: PayloadAction<BreedingRecord[]>) => {\n      state.breedingRecords = action.payload;\n    },\n    addBreedingRecord: (state, action: PayloadAction<BreedingRecord>) => {\n      state.breedingRecords.unshift(action.payload);\n    },\n    updateBreedingRecord: (state, action: PayloadAction<BreedingRecord>) => {\n      const index = state.breedingRecords.findIndex(record => record._id === action.payload._id);\n      if (index !== -1) {\n        state.breedingRecords[index] = action.payload;\n      }\n    },\n    deleteBreedingRecord: (state, action: PayloadAction<string>) => {\n      state.breedingRecords = state.breedingRecords.filter(record => record._id !== action.payload);\n    },\n    setBirthRecords: (state, action: PayloadAction<BirthRecord[]>) => {\n      state.birthRecords = action.payload;\n    },\n    addBirthRecord: (state, action: PayloadAction<BirthRecord>) => {\n      state.birthRecords.unshift(action.payload);\n    },\n    updateBirthRecord: (state, action: PayloadAction<BirthRecord>) => {\n      const index = state.birthRecords.findIndex(record => record._id === action.payload._id);\n      if (index !== -1) {\n        state.birthRecords[index] = action.payload;\n      }\n    },\n    deleteBirthRecord: (state, action: PayloadAction<string>) => {\n      state.birthRecords = state.birthRecords.filter(record => record._id !== action.payload);\n    },\n    setHeatRecords: (state, action: PayloadAction<HeatRecord[]>) => {\n      state.heatRecords = action.payload;\n    },\n    addHeatRecord: (state, action: PayloadAction<HeatRecord>) => {\n      state.heatRecords.unshift(action.payload);\n    },\n    updateHeatRecord: (state, action: PayloadAction<HeatRecord>) => {\n      const index = state.heatRecords.findIndex(record => record._id === action.payload._id);\n      if (index !== -1) {\n        state.heatRecords[index] = action.payload;\n      }\n    },\n    deleteHeatRecord: (state, action: PayloadAction<string>) => {\n      state.heatRecords = state.heatRecords.filter(record => record._id !== action.payload);\n    },\n    setSelectedBreedingRecord: (state, action: PayloadAction<BreedingRecord | null>) => {\n      state.selectedBreedingRecord = action.payload;\n    },\n    setSelectedBirthRecord: (state, action: PayloadAction<BirthRecord | null>) => {\n      state.selectedBirthRecord = action.payload;\n    },\n    setLoading: (state, action: PayloadAction<boolean>) => {\n      state.isLoading = action.payload;\n    },\n    setError: (state, action: PayloadAction<string | null>) => {\n      state.error = action.payload;\n    },\n    clearError: (state) => {\n      state.error = null;\n    },\n    setStatistics: (state, action: PayloadAction<BreedingState['statistics']>) => {\n      state.statistics = action.payload;\n    },\n  },\n});\n\nexport const {\n  setBreedingRecords,\n  addBreedingRecord,\n  updateBreedingRecord,\n  deleteBreedingRecord,\n  setBirthRecords,\n  addBirthRecord,\n  updateBirthRecord,\n  deleteBirthRecord,\n  setHeatRecords,\n  addHeatRecord,\n  updateHeatRecord,\n  deleteHeatRecord,\n  setSelectedBreedingRecord,\n  setSelectedBirthRecord,\n  setLoading,\n  setError,\n  clearError,\n  setStatistics,\n} = breedingSlice.actions;\n\nexport default breedingSlice.reducer;\n"], "mappings": "AAAA,OAASA,WAAW,KAAuB,kBAAkB,CAkG7D,KAAM,CAAAC,YAA2B,CAAG,CAClCC,eAAe,CAAE,EAAE,CACnBC,YAAY,CAAE,EAAE,CAChBC,WAAW,CAAE,EAAE,CACfC,sBAAsB,CAAE,IAAI,CAC5BC,mBAAmB,CAAE,IAAI,CACzBC,SAAS,CAAE,KAAK,CAChBC,KAAK,CAAE,IAAI,CACXC,UAAU,CAAE,IACd,CAAC,CAED,KAAM,CAAAC,aAAa,CAAGV,WAAW,CAAC,CAChCW,IAAI,CAAE,UAAU,CAChBV,YAAY,CACZW,QAAQ,CAAE,CACRC,kBAAkB,CAAEA,CAACC,KAAK,CAAEC,MAAuC,GAAK,CACtED,KAAK,CAACZ,eAAe,CAAGa,MAAM,CAACC,OAAO,CACxC,CAAC,CACDC,iBAAiB,CAAEA,CAACH,KAAK,CAAEC,MAAqC,GAAK,CACnED,KAAK,CAACZ,eAAe,CAACgB,OAAO,CAACH,MAAM,CAACC,OAAO,CAAC,CAC/C,CAAC,CACDG,oBAAoB,CAAEA,CAACL,KAAK,CAAEC,MAAqC,GAAK,CACtE,KAAM,CAAAK,KAAK,CAAGN,KAAK,CAACZ,eAAe,CAACmB,SAAS,CAACC,MAAM,EAAIA,MAAM,CAACC,GAAG,GAAKR,MAAM,CAACC,OAAO,CAACO,GAAG,CAAC,CAC1F,GAAIH,KAAK,GAAK,CAAC,CAAC,CAAE,CAChBN,KAAK,CAACZ,eAAe,CAACkB,KAAK,CAAC,CAAGL,MAAM,CAACC,OAAO,CAC/C,CACF,CAAC,CACDQ,oBAAoB,CAAEA,CAACV,KAAK,CAAEC,MAA6B,GAAK,CAC9DD,KAAK,CAACZ,eAAe,CAAGY,KAAK,CAACZ,eAAe,CAACuB,MAAM,CAACH,MAAM,EAAIA,MAAM,CAACC,GAAG,GAAKR,MAAM,CAACC,OAAO,CAAC,CAC/F,CAAC,CACDU,eAAe,CAAEA,CAACZ,KAAK,CAAEC,MAAoC,GAAK,CAChED,KAAK,CAACX,YAAY,CAAGY,MAAM,CAACC,OAAO,CACrC,CAAC,CACDW,cAAc,CAAEA,CAACb,KAAK,CAAEC,MAAkC,GAAK,CAC7DD,KAAK,CAACX,YAAY,CAACe,OAAO,CAACH,MAAM,CAACC,OAAO,CAAC,CAC5C,CAAC,CACDY,iBAAiB,CAAEA,CAACd,KAAK,CAAEC,MAAkC,GAAK,CAChE,KAAM,CAAAK,KAAK,CAAGN,KAAK,CAACX,YAAY,CAACkB,SAAS,CAACC,MAAM,EAAIA,MAAM,CAACC,GAAG,GAAKR,MAAM,CAACC,OAAO,CAACO,GAAG,CAAC,CACvF,GAAIH,KAAK,GAAK,CAAC,CAAC,CAAE,CAChBN,KAAK,CAACX,YAAY,CAACiB,KAAK,CAAC,CAAGL,MAAM,CAACC,OAAO,CAC5C,CACF,CAAC,CACDa,iBAAiB,CAAEA,CAACf,KAAK,CAAEC,MAA6B,GAAK,CAC3DD,KAAK,CAACX,YAAY,CAAGW,KAAK,CAACX,YAAY,CAACsB,MAAM,CAACH,MAAM,EAAIA,MAAM,CAACC,GAAG,GAAKR,MAAM,CAACC,OAAO,CAAC,CACzF,CAAC,CACDc,cAAc,CAAEA,CAAChB,KAAK,CAAEC,MAAmC,GAAK,CAC9DD,KAAK,CAACV,WAAW,CAAGW,MAAM,CAACC,OAAO,CACpC,CAAC,CACDe,aAAa,CAAEA,CAACjB,KAAK,CAAEC,MAAiC,GAAK,CAC3DD,KAAK,CAACV,WAAW,CAACc,OAAO,CAACH,MAAM,CAACC,OAAO,CAAC,CAC3C,CAAC,CACDgB,gBAAgB,CAAEA,CAAClB,KAAK,CAAEC,MAAiC,GAAK,CAC9D,KAAM,CAAAK,KAAK,CAAGN,KAAK,CAACV,WAAW,CAACiB,SAAS,CAACC,MAAM,EAAIA,MAAM,CAACC,GAAG,GAAKR,MAAM,CAACC,OAAO,CAACO,GAAG,CAAC,CACtF,GAAIH,KAAK,GAAK,CAAC,CAAC,CAAE,CAChBN,KAAK,CAACV,WAAW,CAACgB,KAAK,CAAC,CAAGL,MAAM,CAACC,OAAO,CAC3C,CACF,CAAC,CACDiB,gBAAgB,CAAEA,CAACnB,KAAK,CAAEC,MAA6B,GAAK,CAC1DD,KAAK,CAACV,WAAW,CAAGU,KAAK,CAACV,WAAW,CAACqB,MAAM,CAACH,MAAM,EAAIA,MAAM,CAACC,GAAG,GAAKR,MAAM,CAACC,OAAO,CAAC,CACvF,CAAC,CACDkB,yBAAyB,CAAEA,CAACpB,KAAK,CAAEC,MAA4C,GAAK,CAClFD,KAAK,CAACT,sBAAsB,CAAGU,MAAM,CAACC,OAAO,CAC/C,CAAC,CACDmB,sBAAsB,CAAEA,CAACrB,KAAK,CAAEC,MAAyC,GAAK,CAC5ED,KAAK,CAACR,mBAAmB,CAAGS,MAAM,CAACC,OAAO,CAC5C,CAAC,CACDoB,UAAU,CAAEA,CAACtB,KAAK,CAAEC,MAA8B,GAAK,CACrDD,KAAK,CAACP,SAAS,CAAGQ,MAAM,CAACC,OAAO,CAClC,CAAC,CACDqB,QAAQ,CAAEA,CAACvB,KAAK,CAAEC,MAAoC,GAAK,CACzDD,KAAK,CAACN,KAAK,CAAGO,MAAM,CAACC,OAAO,CAC9B,CAAC,CACDsB,UAAU,CAAGxB,KAAK,EAAK,CACrBA,KAAK,CAACN,KAAK,CAAG,IAAI,CACpB,CAAC,CACD+B,aAAa,CAAEA,CAACzB,KAAK,CAAEC,MAAkD,GAAK,CAC5ED,KAAK,CAACL,UAAU,CAAGM,MAAM,CAACC,OAAO,CACnC,CACF,CACF,CAAC,CAAC,CAEF,MAAO,MAAM,CACXH,kBAAkB,CAClBI,iBAAiB,CACjBE,oBAAoB,CACpBK,oBAAoB,CACpBE,eAAe,CACfC,cAAc,CACdC,iBAAiB,CACjBC,iBAAiB,CACjBC,cAAc,CACdC,aAAa,CACbC,gBAAgB,CAChBC,gBAAgB,CAChBC,yBAAyB,CACzBC,sBAAsB,CACtBC,UAAU,CACVC,QAAQ,CACRC,UAAU,CACVC,aACF,CAAC,CAAG7B,aAAa,CAAC8B,OAAO,CAEzB,cAAe,CAAA9B,aAAa,CAAC+B,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}