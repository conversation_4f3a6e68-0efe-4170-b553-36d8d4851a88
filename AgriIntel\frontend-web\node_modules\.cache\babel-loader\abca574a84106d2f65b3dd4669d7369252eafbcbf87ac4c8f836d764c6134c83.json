{"ast": null, "code": "import _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { Subscribable } from './subscribable';\nimport { isServer } from './utils';\nexport var FocusManager = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(FocusManager, _Subscribable);\n  function FocusManager() {\n    var _this;\n    _this = _Subscribable.call(this) || this;\n    _this.setup = function (onFocus) {\n      var _window;\n      if (!isServer && ((_window = window) == null ? void 0 : _window.addEventListener)) {\n        var listener = function listener() {\n          return onFocus();\n        }; // Listen to visibillitychange and focus\n\n        window.addEventListener('visibilitychange', listener, false);\n        window.addEventListener('focus', listener, false);\n        return function () {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener);\n          window.removeEventListener('focus', listener);\n        };\n      }\n    };\n    return _this;\n  }\n  var _proto = FocusManager.prototype;\n  _proto.onSubscribe = function onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  };\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  };\n  _proto.setEventListener = function setEventListener(setup) {\n    var _this$cleanup2,\n      _this2 = this;\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(function (focused) {\n      if (typeof focused === 'boolean') {\n        _this2.setFocused(focused);\n      } else {\n        _this2.onFocus();\n      }\n    });\n  };\n  _proto.setFocused = function setFocused(focused) {\n    this.focused = focused;\n    if (focused) {\n      this.onFocus();\n    }\n  };\n  _proto.onFocus = function onFocus() {\n    this.listeners.forEach(function (listener) {\n      listener();\n    });\n  };\n  _proto.isFocused = function isFocused() {\n    if (typeof this.focused === 'boolean') {\n      return this.focused;\n    } // document global can be unavailable in react native\n\n    if (typeof document === 'undefined') {\n      return true;\n    }\n    return [undefined, 'visible', 'prerender'].includes(document.visibilityState);\n  };\n  return FocusManager;\n}(Subscribable);\nexport var focusManager = new FocusManager();", "map": {"version": 3, "names": ["_inherits<PERSON><PERSON>e", "Subscribable", "isServer", "FocusManager", "_Subscribable", "_this", "call", "setup", "onFocus", "_window", "window", "addEventListener", "listener", "removeEventListener", "_proto", "prototype", "onSubscribe", "cleanup", "setEventListener", "onUnsubscribe", "hasListeners", "_this$cleanup", "undefined", "_this$cleanup2", "_this2", "focused", "setFocused", "listeners", "for<PERSON>ach", "isFocused", "document", "includes", "visibilityState", "focusManager"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-query/es/core/focusManager.js"], "sourcesContent": ["import _inheritsLoose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport { Subscribable } from './subscribable';\nimport { isServer } from './utils';\nexport var FocusManager = /*#__PURE__*/function (_Subscribable) {\n  _inheritsLoose(FocusManager, _Subscribable);\n\n  function FocusManager() {\n    var _this;\n\n    _this = _Subscribable.call(this) || this;\n\n    _this.setup = function (onFocus) {\n      var _window;\n\n      if (!isServer && ((_window = window) == null ? void 0 : _window.addEventListener)) {\n        var listener = function listener() {\n          return onFocus();\n        }; // Listen to visibillitychange and focus\n\n\n        window.addEventListener('visibilitychange', listener, false);\n        window.addEventListener('focus', listener, false);\n        return function () {\n          // Be sure to unsubscribe if a new handler is set\n          window.removeEventListener('visibilitychange', listener);\n          window.removeEventListener('focus', listener);\n        };\n      }\n    };\n\n    return _this;\n  }\n\n  var _proto = FocusManager.prototype;\n\n  _proto.onSubscribe = function onSubscribe() {\n    if (!this.cleanup) {\n      this.setEventListener(this.setup);\n    }\n  };\n\n  _proto.onUnsubscribe = function onUnsubscribe() {\n    if (!this.hasListeners()) {\n      var _this$cleanup;\n\n      (_this$cleanup = this.cleanup) == null ? void 0 : _this$cleanup.call(this);\n      this.cleanup = undefined;\n    }\n  };\n\n  _proto.setEventListener = function setEventListener(setup) {\n    var _this$cleanup2,\n        _this2 = this;\n\n    this.setup = setup;\n    (_this$cleanup2 = this.cleanup) == null ? void 0 : _this$cleanup2.call(this);\n    this.cleanup = setup(function (focused) {\n      if (typeof focused === 'boolean') {\n        _this2.setFocused(focused);\n      } else {\n        _this2.onFocus();\n      }\n    });\n  };\n\n  _proto.setFocused = function setFocused(focused) {\n    this.focused = focused;\n\n    if (focused) {\n      this.onFocus();\n    }\n  };\n\n  _proto.onFocus = function onFocus() {\n    this.listeners.forEach(function (listener) {\n      listener();\n    });\n  };\n\n  _proto.isFocused = function isFocused() {\n    if (typeof this.focused === 'boolean') {\n      return this.focused;\n    } // document global can be unavailable in react native\n\n\n    if (typeof document === 'undefined') {\n      return true;\n    }\n\n    return [undefined, 'visible', 'prerender'].includes(document.visibilityState);\n  };\n\n  return FocusManager;\n}(Subscribable);\nexport var focusManager = new FocusManager();"], "mappings": "AAAA,OAAOA,cAAc,MAAM,0CAA0C;AACrE,SAASC,YAAY,QAAQ,gBAAgB;AAC7C,SAASC,QAAQ,QAAQ,SAAS;AAClC,OAAO,IAAIC,YAAY,GAAG,aAAa,UAAUC,aAAa,EAAE;EAC9DJ,cAAc,CAACG,YAAY,EAAEC,aAAa,CAAC;EAE3C,SAASD,YAAYA,CAAA,EAAG;IACtB,IAAIE,KAAK;IAETA,KAAK,GAAGD,aAAa,CAACE,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI;IAExCD,KAAK,CAACE,KAAK,GAAG,UAAUC,OAAO,EAAE;MAC/B,IAAIC,OAAO;MAEX,IAAI,CAACP,QAAQ,KAAK,CAACO,OAAO,GAAGC,MAAM,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGD,OAAO,CAACE,gBAAgB,CAAC,EAAE;QACjF,IAAIC,QAAQ,GAAG,SAASA,QAAQA,CAAA,EAAG;UACjC,OAAOJ,OAAO,CAAC,CAAC;QAClB,CAAC,CAAC,CAAC;;QAGHE,MAAM,CAACC,gBAAgB,CAAC,kBAAkB,EAAEC,QAAQ,EAAE,KAAK,CAAC;QAC5DF,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAEC,QAAQ,EAAE,KAAK,CAAC;QACjD,OAAO,YAAY;UACjB;UACAF,MAAM,CAACG,mBAAmB,CAAC,kBAAkB,EAAED,QAAQ,CAAC;UACxDF,MAAM,CAACG,mBAAmB,CAAC,OAAO,EAAED,QAAQ,CAAC;QAC/C,CAAC;MACH;IACF,CAAC;IAED,OAAOP,KAAK;EACd;EAEA,IAAIS,MAAM,GAAGX,YAAY,CAACY,SAAS;EAEnCD,MAAM,CAACE,WAAW,GAAG,SAASA,WAAWA,CAAA,EAAG;IAC1C,IAAI,CAAC,IAAI,CAACC,OAAO,EAAE;MACjB,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACX,KAAK,CAAC;IACnC;EACF,CAAC;EAEDO,MAAM,CAACK,aAAa,GAAG,SAASA,aAAaA,CAAA,EAAG;IAC9C,IAAI,CAAC,IAAI,CAACC,YAAY,CAAC,CAAC,EAAE;MACxB,IAAIC,aAAa;MAEjB,CAACA,aAAa,GAAG,IAAI,CAACJ,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGI,aAAa,CAACf,IAAI,CAAC,IAAI,CAAC;MAC1E,IAAI,CAACW,OAAO,GAAGK,SAAS;IAC1B;EACF,CAAC;EAEDR,MAAM,CAACI,gBAAgB,GAAG,SAASA,gBAAgBA,CAACX,KAAK,EAAE;IACzD,IAAIgB,cAAc;MACdC,MAAM,GAAG,IAAI;IAEjB,IAAI,CAACjB,KAAK,GAAGA,KAAK;IAClB,CAACgB,cAAc,GAAG,IAAI,CAACN,OAAO,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGM,cAAc,CAACjB,IAAI,CAAC,IAAI,CAAC;IAC5E,IAAI,CAACW,OAAO,GAAGV,KAAK,CAAC,UAAUkB,OAAO,EAAE;MACtC,IAAI,OAAOA,OAAO,KAAK,SAAS,EAAE;QAChCD,MAAM,CAACE,UAAU,CAACD,OAAO,CAAC;MAC5B,CAAC,MAAM;QACLD,MAAM,CAAChB,OAAO,CAAC,CAAC;MAClB;IACF,CAAC,CAAC;EACJ,CAAC;EAEDM,MAAM,CAACY,UAAU,GAAG,SAASA,UAAUA,CAACD,OAAO,EAAE;IAC/C,IAAI,CAACA,OAAO,GAAGA,OAAO;IAEtB,IAAIA,OAAO,EAAE;MACX,IAAI,CAACjB,OAAO,CAAC,CAAC;IAChB;EACF,CAAC;EAEDM,MAAM,CAACN,OAAO,GAAG,SAASA,OAAOA,CAAA,EAAG;IAClC,IAAI,CAACmB,SAAS,CAACC,OAAO,CAAC,UAAUhB,QAAQ,EAAE;MACzCA,QAAQ,CAAC,CAAC;IACZ,CAAC,CAAC;EACJ,CAAC;EAEDE,MAAM,CAACe,SAAS,GAAG,SAASA,SAASA,CAAA,EAAG;IACtC,IAAI,OAAO,IAAI,CAACJ,OAAO,KAAK,SAAS,EAAE;MACrC,OAAO,IAAI,CAACA,OAAO;IACrB,CAAC,CAAC;;IAGF,IAAI,OAAOK,QAAQ,KAAK,WAAW,EAAE;MACnC,OAAO,IAAI;IACb;IAEA,OAAO,CAACR,SAAS,EAAE,SAAS,EAAE,WAAW,CAAC,CAACS,QAAQ,CAACD,QAAQ,CAACE,eAAe,CAAC;EAC/E,CAAC;EAED,OAAO7B,YAAY;AACrB,CAAC,CAACF,YAAY,CAAC;AACf,OAAO,IAAIgC,YAAY,GAAG,IAAI9B,YAAY,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}