{"name": "ampd-livestock-frontend", "version": "1.0.0", "private": true, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.2.0", "@mui/icons-material": "^5.16.14", "@mui/material": "^5.15.0", "@mui/x-data-grid": "^6.18.5", "@mui/x-date-pickers": "^6.20.2", "@reduxjs/toolkit": "^1.9.5", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@types/jest": "^29.5.0", "@types/node": "^18.15.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "axios": "^1.9.0", "chart.js": "^4.4.9", "date-fns": "^2.30.0", "framer-motion": "^10.12.0", "i18next": "^23.2.3", "i18next-browser-languagedetector": "^7.1.0", "i18next-http-backend": "^2.2.1", "react": "^18.2.0", "react-chartjs-2": "^5.3.0", "react-dom": "^18.2.0", "react-hook-form": "^7.45.1", "react-hot-toast": "^2.4.1", "react-i18next": "^13.0.1", "react-query": "^3.39.3", "react-redux": "^8.1.1", "react-router-dom": "^6.15.0", "react-scripts": "5.0.1", "recharts": "^2.15.2", "tailwindcss": "^3.3.3", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "set NODE_OPTIONS=--openssl-legacy-provider && react-scripts build", "build:win": "set NODE_OPTIONS=--openssl-legacy-provider && react-scripts build", "build:unix": "NODE_OPTIONS=--openssl-legacy-provider react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/react-router-dom": "^5.3.3", "autoprefixer": "^10.4.14", "postcss": "^8.4.31"}, "proxy": "http://localhost:3001"}