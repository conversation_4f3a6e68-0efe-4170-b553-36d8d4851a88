{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.StreamDescription = void 0;\nconst bson_1 = require(\"../bson\");\nconst common_1 = require(\"../sdam/common\");\nconst server_description_1 = require(\"../sdam/server_description\");\nconst RESPONSE_FIELDS = ['minWireVersion', 'maxWireVersion', 'maxBsonObjectSize', 'maxMessageSizeBytes', 'maxWriteBatchSize', 'logicalSessionTimeoutMinutes'];\n/** @public */\nclass StreamDescription {\n  constructor(address, options) {\n    this.hello = null;\n    this.address = address;\n    this.type = common_1.ServerType.Unknown;\n    this.minWireVersion = undefined;\n    this.maxWireVersion = undefined;\n    this.maxBsonObjectSize = 16777216;\n    this.maxMessageSizeBytes = 48000000;\n    this.maxWriteBatchSize = 100000;\n    this.logicalSessionTimeoutMinutes = options?.logicalSessionTimeoutMinutes;\n    this.loadBalanced = !!options?.loadBalanced;\n    this.compressors = options && options.compressors && Array.isArray(options.compressors) ? options.compressors : [];\n    this.serverConnectionId = null;\n  }\n  receiveResponse(response) {\n    if (response == null) {\n      return;\n    }\n    this.hello = response;\n    this.type = (0, server_description_1.parseServerType)(response);\n    if ('connectionId' in response) {\n      this.serverConnectionId = this.parseServerConnectionID(response.connectionId);\n    } else {\n      this.serverConnectionId = null;\n    }\n    for (const field of RESPONSE_FIELDS) {\n      if (response[field] != null) {\n        this[field] = response[field];\n      }\n      // testing case\n      if ('__nodejs_mock_server__' in response) {\n        this.__nodejs_mock_server__ = response['__nodejs_mock_server__'];\n      }\n    }\n    if (response.compression) {\n      this.compressor = this.compressors.filter(c => response.compression?.includes(c))[0];\n    }\n  }\n  /* @internal */\n  parseServerConnectionID(serverConnectionId) {\n    // Connection ids are always integral, so it's safe to coerce doubles as well as\n    // any integral type.\n    return bson_1.Long.isLong(serverConnectionId) ? serverConnectionId.toBigInt() :\n    // @ts-expect-error: Doubles are coercible to number\n    BigInt(serverConnectionId);\n  }\n}\nexports.StreamDescription = StreamDescription;", "map": {"version": 3, "names": ["bson_1", "require", "common_1", "server_description_1", "RESPONSE_FIELDS", "StreamDescription", "constructor", "address", "options", "hello", "type", "ServerType", "Unknown", "minWireVersion", "undefined", "maxWireVersion", "maxBsonObjectSize", "maxMessageSizeBytes", "maxWriteBatchSize", "logicalSessionTimeoutMinutes", "loadBalanced", "compressors", "Array", "isArray", "serverConnectionId", "receiveResponse", "response", "parseServerType", "parseServerConnectionID", "connectionId", "field", "__nodejs_mock_server__", "compression", "compressor", "filter", "c", "includes", "<PERSON>", "isLong", "toBigInt", "BigInt", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\stream_description.ts"], "sourcesContent": ["import { type Document, type Double, Long } from '../bson';\nimport { ServerType } from '../sdam/common';\nimport { parseServerType } from '../sdam/server_description';\nimport type { CompressorName } from './wire_protocol/compression';\n\nconst RESPONSE_FIELDS = [\n  'minWireVersion',\n  'maxWireVersion',\n  'maxBsonObjectSize',\n  'maxMessageSizeBytes',\n  'maxWriteBatchSize',\n  'logicalSessionTimeoutMinutes'\n] as const;\n\n/** @public */\nexport interface StreamDescriptionOptions {\n  compressors?: CompressorName[];\n  logicalSessionTimeoutMinutes?: number;\n  loadBalanced: boolean;\n}\n\n/** @public */\nexport class StreamDescription {\n  address: string;\n  type: ServerType;\n  minWireVersion?: number;\n  maxWireVersion?: number;\n  maxBsonObjectSize: number;\n  maxMessageSizeBytes: number;\n  maxWriteBatchSize: number;\n  compressors: CompressorName[];\n  compressor?: CompressorName;\n  logicalSessionTimeoutMinutes?: number;\n  loadBalanced: boolean;\n\n  __nodejs_mock_server__?: boolean;\n\n  zlibCompressionLevel?: number;\n  serverConnectionId: bigint | null;\n\n  public hello: Document | null = null;\n\n  constructor(address: string, options?: StreamDescriptionOptions) {\n    this.address = address;\n    this.type = ServerType.Unknown;\n    this.minWireVersion = undefined;\n    this.maxWireVersion = undefined;\n    this.maxBsonObjectSize = 16777216;\n    this.maxMessageSizeBytes = 48000000;\n    this.maxWriteBatchSize = 100000;\n    this.logicalSessionTimeoutMinutes = options?.logicalSessionTimeoutMinutes;\n    this.loadBalanced = !!options?.loadBalanced;\n    this.compressors =\n      options && options.compressors && Array.isArray(options.compressors)\n        ? options.compressors\n        : [];\n    this.serverConnectionId = null;\n  }\n\n  receiveResponse(response: Document | null): void {\n    if (response == null) {\n      return;\n    }\n    this.hello = response;\n    this.type = parseServerType(response);\n    if ('connectionId' in response) {\n      this.serverConnectionId = this.parseServerConnectionID(response.connectionId);\n    } else {\n      this.serverConnectionId = null;\n    }\n    for (const field of RESPONSE_FIELDS) {\n      if (response[field] != null) {\n        this[field] = response[field];\n      }\n\n      // testing case\n      if ('__nodejs_mock_server__' in response) {\n        this.__nodejs_mock_server__ = response['__nodejs_mock_server__'];\n      }\n    }\n\n    if (response.compression) {\n      this.compressor = this.compressors.filter(c => response.compression?.includes(c))[0];\n    }\n  }\n\n  /* @internal */\n  parseServerConnectionID(serverConnectionId: number | Double | bigint | Long): bigint {\n    // Connection ids are always integral, so it's safe to coerce doubles as well as\n    // any integral type.\n    return Long.isLong(serverConnectionId)\n      ? serverConnectionId.toBigInt()\n      : // @ts-expect-error: Doubles are coercible to number\n        BigInt(serverConnectionId);\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,MAAA,GAAAC,OAAA;AACA,MAAAC,QAAA,GAAAD,OAAA;AACA,MAAAE,oBAAA,GAAAF,OAAA;AAGA,MAAMG,eAAe,GAAG,CACtB,gBAAgB,EAChB,gBAAgB,EAChB,mBAAmB,EACnB,qBAAqB,EACrB,mBAAmB,EACnB,8BAA8B,CACtB;AASV;AACA,MAAaC,iBAAiB;EAoB5BC,YAAYC,OAAe,EAAEC,OAAkC;IAFxD,KAAAC,KAAK,GAAoB,IAAI;IAGlC,IAAI,CAACF,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACG,IAAI,GAAGR,QAAA,CAAAS,UAAU,CAACC,OAAO;IAC9B,IAAI,CAACC,cAAc,GAAGC,SAAS;IAC/B,IAAI,CAACC,cAAc,GAAGD,SAAS;IAC/B,IAAI,CAACE,iBAAiB,GAAG,QAAQ;IACjC,IAAI,CAACC,mBAAmB,GAAG,QAAQ;IACnC,IAAI,CAACC,iBAAiB,GAAG,MAAM;IAC/B,IAAI,CAACC,4BAA4B,GAAGX,OAAO,EAAEW,4BAA4B;IACzE,IAAI,CAACC,YAAY,GAAG,CAAC,CAACZ,OAAO,EAAEY,YAAY;IAC3C,IAAI,CAACC,WAAW,GACdb,OAAO,IAAIA,OAAO,CAACa,WAAW,IAAIC,KAAK,CAACC,OAAO,CAACf,OAAO,CAACa,WAAW,CAAC,GAChEb,OAAO,CAACa,WAAW,GACnB,EAAE;IACR,IAAI,CAACG,kBAAkB,GAAG,IAAI;EAChC;EAEAC,eAAeA,CAACC,QAAyB;IACvC,IAAIA,QAAQ,IAAI,IAAI,EAAE;MACpB;IACF;IACA,IAAI,CAACjB,KAAK,GAAGiB,QAAQ;IACrB,IAAI,CAAChB,IAAI,GAAG,IAAAP,oBAAA,CAAAwB,eAAe,EAACD,QAAQ,CAAC;IACrC,IAAI,cAAc,IAAIA,QAAQ,EAAE;MAC9B,IAAI,CAACF,kBAAkB,GAAG,IAAI,CAACI,uBAAuB,CAACF,QAAQ,CAACG,YAAY,CAAC;IAC/E,CAAC,MAAM;MACL,IAAI,CAACL,kBAAkB,GAAG,IAAI;IAChC;IACA,KAAK,MAAMM,KAAK,IAAI1B,eAAe,EAAE;MACnC,IAAIsB,QAAQ,CAACI,KAAK,CAAC,IAAI,IAAI,EAAE;QAC3B,IAAI,CAACA,KAAK,CAAC,GAAGJ,QAAQ,CAACI,KAAK,CAAC;MAC/B;MAEA;MACA,IAAI,wBAAwB,IAAIJ,QAAQ,EAAE;QACxC,IAAI,CAACK,sBAAsB,GAAGL,QAAQ,CAAC,wBAAwB,CAAC;MAClE;IACF;IAEA,IAAIA,QAAQ,CAACM,WAAW,EAAE;MACxB,IAAI,CAACC,UAAU,GAAG,IAAI,CAACZ,WAAW,CAACa,MAAM,CAACC,CAAC,IAAIT,QAAQ,CAACM,WAAW,EAAEI,QAAQ,CAACD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;IACtF;EACF;EAEA;EACAP,uBAAuBA,CAACJ,kBAAmD;IACzE;IACA;IACA,OAAOxB,MAAA,CAAAqC,IAAI,CAACC,MAAM,CAACd,kBAAkB,CAAC,GAClCA,kBAAkB,CAACe,QAAQ,EAAE;IAC7B;IACAC,MAAM,CAAChB,kBAAkB,CAAC;EAChC;;AAxEFiB,OAAA,CAAApC,iBAAA,GAAAA,iBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}