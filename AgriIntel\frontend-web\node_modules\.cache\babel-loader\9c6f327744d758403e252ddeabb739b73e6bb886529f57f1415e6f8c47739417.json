{"ast": null, "code": "import _objectSpread from \"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nimport _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nconst _excluded = [\"initialI18nStore\", \"initialLanguage\"];\nimport { createElement } from 'react';\nimport { useSSR } from './useSSR.js';\nimport { composeInitialProps } from './context.js';\nimport { getDisplayName } from './utils.js';\nexport function withSSR() {\n  return function Extend(WrappedComponent) {\n    function I18nextWithSSR(_ref) {\n      let {\n          initialI18nStore,\n          initialLanguage\n        } = _ref,\n        rest = _objectWithoutProperties(_ref, _excluded);\n      useSSR(initialI18nStore, initialLanguage);\n      return createElement(WrappedComponent, _objectSpread({}, rest));\n    }\n    I18nextWithSSR.getInitialProps = composeInitialProps(WrappedComponent);\n    I18nextWithSSR.displayName = \"withI18nextSSR(\".concat(getDisplayName(WrappedComponent), \")\");\n    I18nextWithSSR.WrappedComponent = WrappedComponent;\n    return I18nextWithSSR;\n  };\n}", "map": {"version": 3, "names": ["createElement", "useSSR", "composeInitialProps", "getDisplayName", "withSSR", "Extend", "WrappedComponent", "I18nextWithSSR", "_ref", "initialI18nStore", "initialLanguage", "rest", "_objectWithoutProperties", "_excluded", "_objectSpread", "getInitialProps", "displayName", "concat"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-i18next/dist/es/withSSR.js"], "sourcesContent": ["import { createElement } from 'react';\nimport { useSSR } from './useSSR.js';\nimport { composeInitialProps } from './context.js';\nimport { getDisplayName } from './utils.js';\nexport function withSSR() {\n  return function Extend(WrappedComponent) {\n    function I18nextWithSSR(_ref) {\n      let {\n        initialI18nStore,\n        initialLanguage,\n        ...rest\n      } = _ref;\n      useSSR(initialI18nStore, initialLanguage);\n      return createElement(WrappedComponent, {\n        ...rest\n      });\n    }\n    I18nextWithSSR.getInitialProps = composeInitialProps(WrappedComponent);\n    I18nextWithSSR.displayName = `withI18nextSSR(${getDisplayName(WrappedComponent)})`;\n    I18nextWithSSR.WrappedComponent = WrappedComponent;\n    return I18nextWithSSR;\n  };\n}"], "mappings": ";;;AAAA,SAASA,aAAa,QAAQ,OAAO;AACrC,SAASC,MAAM,QAAQ,aAAa;AACpC,SAASC,mBAAmB,QAAQ,cAAc;AAClD,SAASC,cAAc,QAAQ,YAAY;AAC3C,OAAO,SAASC,OAAOA,CAAA,EAAG;EACxB,OAAO,SAASC,MAAMA,CAACC,gBAAgB,EAAE;IACvC,SAASC,cAAcA,CAACC,IAAI,EAAE;MAC5B,IAAI;UACFC,gBAAgB;UAChBC;QAEF,CAAC,GAAGF,IAAI;QADHG,IAAI,GAAAC,wBAAA,CACLJ,IAAI,EAAAK,SAAA;MACRZ,MAAM,CAACQ,gBAAgB,EAAEC,eAAe,CAAC;MACzC,OAAOV,aAAa,CAACM,gBAAgB,EAAAQ,aAAA,KAChCH,IAAI,CACR,CAAC;IACJ;IACAJ,cAAc,CAACQ,eAAe,GAAGb,mBAAmB,CAACI,gBAAgB,CAAC;IACtEC,cAAc,CAACS,WAAW,qBAAAC,MAAA,CAAqBd,cAAc,CAACG,gBAAgB,CAAC,MAAG;IAClFC,cAAc,CAACD,gBAAgB,GAAGA,gBAAgB;IAClD,OAAOC,cAAc;EACvB,CAAC;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}