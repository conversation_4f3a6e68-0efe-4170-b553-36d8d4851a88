{"ast": null, "code": "import deepmerge from '@mui/utils/deepmerge';\nimport createTheme from './createTheme';\nexport default function createMuiStrictModeTheme(options, ...args) {\n  return createTheme(deepmerge({\n    unstable_strictMode: true\n  }, options), ...args);\n}", "map": {"version": 3, "names": ["deepmerge", "createTheme", "createMuiStrictModeTheme", "options", "args", "unstable_strictMode"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/material/styles/createMuiStrictModeTheme.js"], "sourcesContent": ["import deepmerge from '@mui/utils/deepmerge';\nimport createTheme from './createTheme';\nexport default function createMuiStrictModeTheme(options, ...args) {\n  return createTheme(deepmerge({\n    unstable_strictMode: true\n  }, options), ...args);\n}"], "mappings": "AAAA,OAAOA,SAAS,MAAM,sBAAsB;AAC5C,OAAOC,WAAW,MAAM,eAAe;AACvC,eAAe,SAASC,wBAAwBA,CAACC,OAAO,EAAE,GAAGC,IAAI,EAAE;EACjE,OAAOH,WAAW,CAACD,SAAS,CAAC;IAC3BK,mBAAmB,EAAE;EACvB,CAAC,EAAEF,OAAO,CAAC,EAAE,GAAGC,IAAI,CAAC;AACvB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}