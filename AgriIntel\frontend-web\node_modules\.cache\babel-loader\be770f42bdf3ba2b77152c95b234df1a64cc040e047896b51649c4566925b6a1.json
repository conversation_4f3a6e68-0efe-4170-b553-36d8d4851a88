{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.OP_MSG = exports.OP_COMPRESSED = exports.OP_DELETE = exports.OP_QUERY = exports.OP_INSERT = exports.OP_UPDATE = exports.OP_REPLY = exports.MIN_SUPPORTED_QE_SERVER_VERSION = exports.MIN_SUPPORTED_QE_WIRE_VERSION = exports.MAX_SUPPORTED_WIRE_VERSION = exports.MIN_SUPPORTED_WIRE_VERSION = exports.MAX_SUPPORTED_SERVER_VERSION = exports.MIN_SUPPORTED_SERVER_VERSION = void 0;\nexports.MIN_SUPPORTED_SERVER_VERSION = '4.0';\nexports.MAX_SUPPORTED_SERVER_VERSION = '8.0';\nexports.MIN_SUPPORTED_WIRE_VERSION = 7;\nexports.MAX_SUPPORTED_WIRE_VERSION = 25;\nexports.MIN_SUPPORTED_QE_WIRE_VERSION = 21;\nexports.MIN_SUPPORTED_QE_SERVER_VERSION = '7.0';\nexports.OP_REPLY = 1;\nexports.OP_UPDATE = 2001;\nexports.OP_INSERT = 2002;\nexports.OP_QUERY = 2004;\nexports.OP_DELETE = 2006;\nexports.OP_COMPRESSED = 2012;\nexports.OP_MSG = 2013;", "map": {"version": 3, "names": ["exports", "MIN_SUPPORTED_SERVER_VERSION", "MAX_SUPPORTED_SERVER_VERSION", "MIN_SUPPORTED_WIRE_VERSION", "MAX_SUPPORTED_WIRE_VERSION", "MIN_SUPPORTED_QE_WIRE_VERSION", "MIN_SUPPORTED_QE_SERVER_VERSION", "OP_REPLY", "OP_UPDATE", "OP_INSERT", "OP_QUERY", "OP_DELETE", "OP_COMPRESSED", "OP_MSG"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\wire_protocol\\constants.ts"], "sourcesContent": ["export const MIN_SUPPORTED_SERVER_VERSION = '4.0';\nexport const MAX_SUPPORTED_SERVER_VERSION = '8.0';\nexport const MIN_SUPPORTED_WIRE_VERSION = 7;\nexport const MAX_SUPPORTED_WIRE_VERSION = 25;\nexport const MIN_SUPPORTED_QE_WIRE_VERSION = 21;\nexport const MIN_SUPPORTED_QE_SERVER_VERSION = '7.0';\nexport const OP_REPLY = 1;\nexport const OP_UPDATE = 2001;\nexport const OP_INSERT = 2002;\nexport const OP_QUERY = 2004;\nexport const OP_DELETE = 2006;\nexport const OP_COMPRESSED = 2012;\nexport const OP_MSG = 2013;\n"], "mappings": ";;;;;;AAAaA,OAAA,CAAAC,4BAA4B,GAAG,KAAK;AACpCD,OAAA,CAAAE,4BAA4B,GAAG,KAAK;AACpCF,OAAA,CAAAG,0BAA0B,GAAG,CAAC;AAC9BH,OAAA,CAAAI,0BAA0B,GAAG,EAAE;AAC/BJ,OAAA,CAAAK,6BAA6B,GAAG,EAAE;AAClCL,OAAA,CAAAM,+BAA+B,GAAG,KAAK;AACvCN,OAAA,CAAAO,QAAQ,GAAG,CAAC;AACZP,OAAA,CAAAQ,SAAS,GAAG,IAAI;AAChBR,OAAA,CAAAS,SAAS,GAAG,IAAI;AAChBT,OAAA,CAAAU,QAAQ,GAAG,IAAI;AACfV,OAAA,CAAAW,SAAS,GAAG,IAAI;AAChBX,OAAA,CAAAY,aAAa,GAAG,IAAI;AACpBZ,OAAA,CAAAa,MAAM,GAAG,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}