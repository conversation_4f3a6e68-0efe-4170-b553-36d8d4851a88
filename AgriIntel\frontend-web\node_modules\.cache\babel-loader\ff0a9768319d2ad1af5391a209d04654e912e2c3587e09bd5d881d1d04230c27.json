{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.AbstractOperation = exports.Aspect = void 0;\nexports.defineAspects = defineAspects;\nconst bson_1 = require(\"../bson\");\nconst read_preference_1 = require(\"../read_preference\");\nexports.Aspect = {\n  READ_OPERATION: Symbol('READ_OPERATION'),\n  WRITE_OPERATION: Symbol('WRITE_OPERATION'),\n  RETRYABLE: Symbol('RETRYABLE'),\n  EXPLAINABLE: Symbol('EXPLAINABLE'),\n  SKIP_COLLATION: Symbol('SKIP_COLLATION'),\n  CURSOR_CREATING: Symbol('CURSOR_CREATING'),\n  MUST_SELECT_SAME_SERVER: Symbol('MUST_SELECT_SAME_SERVER'),\n  COMMAND_BATCHING: Symbol('COMMAND_BATCHING')\n};\n/**\n * This class acts as a parent class for any operation and is responsible for setting this.options,\n * as well as setting and getting a session.\n * Additionally, this class implements `hasAspect`, which determines whether an operation has\n * a specific aspect.\n * @internal\n */\nclass AbstractOperation {\n  constructor(options = {}) {\n    this.readPreference = this.hasAspect(exports.Aspect.WRITE_OPERATION) ? read_preference_1.ReadPreference.primary : read_preference_1.ReadPreference.fromOptions(options) ?? read_preference_1.ReadPreference.primary;\n    // Pull the BSON serialize options from the already-resolved options\n    this.bsonOptions = (0, bson_1.resolveBSONOptions)(options);\n    this._session = options.session != null ? options.session : undefined;\n    this.options = options;\n    this.bypassPinningCheck = !!options.bypassPinningCheck;\n    this.trySecondaryWrite = false;\n  }\n  hasAspect(aspect) {\n    const ctor = this.constructor;\n    if (ctor.aspects == null) {\n      return false;\n    }\n    return ctor.aspects.has(aspect);\n  }\n  // Make sure the session is not writable from outside this class.\n  get session() {\n    return this._session;\n  }\n  clearSession() {\n    this._session = undefined;\n  }\n  resetBatch() {\n    return true;\n  }\n  get canRetryRead() {\n    return this.hasAspect(exports.Aspect.RETRYABLE) && this.hasAspect(exports.Aspect.READ_OPERATION);\n  }\n  get canRetryWrite() {\n    return this.hasAspect(exports.Aspect.RETRYABLE) && this.hasAspect(exports.Aspect.WRITE_OPERATION);\n  }\n}\nexports.AbstractOperation = AbstractOperation;\nfunction defineAspects(operation, aspects) {\n  if (!Array.isArray(aspects) && !(aspects instanceof Set)) {\n    aspects = [aspects];\n  }\n  aspects = new Set(aspects);\n  Object.defineProperty(operation, 'aspects', {\n    value: aspects,\n    writable: false\n  });\n  return aspects;\n}", "map": {"version": 3, "names": ["exports", "defineAspects", "bson_1", "require", "read_preference_1", "Aspect", "READ_OPERATION", "Symbol", "WRITE_OPERATION", "RETRYABLE", "EXPLAINABLE", "SKIP_COLLATION", "CURSOR_CREATING", "MUST_SELECT_SAME_SERVER", "COMMAND_BATCHING", "AbstractOperation", "constructor", "options", "readPreference", "hasAspect", "ReadPreference", "primary", "fromOptions", "bsonOptions", "resolveBSONOptions", "_session", "session", "undefined", "bypassPinningCheck", "trySecondaryWrite", "aspect", "ctor", "aspects", "has", "clearSession", "resetBatch", "canRetryRead", "canRetryWrite", "operation", "Array", "isArray", "Set", "Object", "defineProperty", "value", "writable"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\operation.ts"], "sourcesContent": ["import { type BSONSerializeOptions, type Document, resolveBSONOptions } from '../bson';\nimport { type Abortable } from '../mongo_types';\nimport { ReadPreference, type ReadPreferenceLike } from '../read_preference';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport type { MongoDBNamespace } from '../utils';\n\nexport const Aspect = {\n  READ_OPERATION: Symbol('READ_OPERATION'),\n  WRITE_OPERATION: Symbol('WRITE_OPERATION'),\n  RETRYABLE: Symbol('RETRYABLE'),\n  EXPLAINABLE: Symbol('EXPLAINABLE'),\n  SKIP_COLLATION: Symbol('SKIP_COLLATION'),\n  CURSOR_CREATING: Symbol('CURSOR_CREATING'),\n  MUST_SELECT_SAME_SERVER: Symbol('MUST_SELECT_SAME_SERVER'),\n  COMMAND_BATCHING: Symbol('COMMAND_BATCHING')\n} as const;\n\n/** @public */\nexport type Hint = string | Document;\n\n/** @public */\nexport interface OperationOptions extends BSONSerializeOptions {\n  /** Specify ClientSession for this command */\n  session?: ClientSession;\n  willRetryWrite?: boolean;\n\n  /** The preferred read preference (ReadPreference.primary, ReadPreference.primary_preferred, ReadPreference.secondary, ReadPreference.secondary_preferred, ReadPreference.nearest). */\n  readPreference?: ReadPreferenceLike;\n\n  /** @internal Hints to `executeOperation` that this operation should not unpin on an ended transaction */\n  bypassPinningCheck?: boolean;\n  omitReadPreference?: boolean;\n\n  /** @internal Hint to `executeOperation` to omit maxTimeMS */\n  omitMaxTimeMS?: boolean;\n\n  /**\n   * @experimental\n   * Specifies the time an operation will run until it throws a timeout error\n   */\n  timeoutMS?: number;\n}\n\n/**\n * This class acts as a parent class for any operation and is responsible for setting this.options,\n * as well as setting and getting a session.\n * Additionally, this class implements `hasAspect`, which determines whether an operation has\n * a specific aspect.\n * @internal\n */\nexport abstract class AbstractOperation<TResult = any> {\n  ns!: MongoDBNamespace;\n  readPreference: ReadPreference;\n  server!: Server;\n  bypassPinningCheck: boolean;\n  trySecondaryWrite: boolean;\n\n  // BSON serialization options\n  bsonOptions?: BSONSerializeOptions;\n\n  options: OperationOptions & Abortable;\n\n  /** Specifies the time an operation will run until it throws a timeout error. */\n  timeoutMS?: number;\n\n  private _session: ClientSession | undefined;\n\n  static aspects?: Set<symbol>;\n\n  constructor(options: OperationOptions & Abortable = {}) {\n    this.readPreference = this.hasAspect(Aspect.WRITE_OPERATION)\n      ? ReadPreference.primary\n      : (ReadPreference.fromOptions(options) ?? ReadPreference.primary);\n\n    // Pull the BSON serialize options from the already-resolved options\n    this.bsonOptions = resolveBSONOptions(options);\n\n    this._session = options.session != null ? options.session : undefined;\n\n    this.options = options;\n    this.bypassPinningCheck = !!options.bypassPinningCheck;\n    this.trySecondaryWrite = false;\n  }\n\n  /** Must match the first key of the command object sent to the server.\n  Command name should be stateless (should not use 'this' keyword) */\n  abstract get commandName(): string;\n\n  abstract execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<TResult>;\n\n  hasAspect(aspect: symbol): boolean {\n    const ctor = this.constructor as { aspects?: Set<symbol> };\n    if (ctor.aspects == null) {\n      return false;\n    }\n\n    return ctor.aspects.has(aspect);\n  }\n\n  // Make sure the session is not writable from outside this class.\n  get session(): ClientSession | undefined {\n    return this._session;\n  }\n\n  clearSession() {\n    this._session = undefined;\n  }\n\n  resetBatch(): boolean {\n    return true;\n  }\n\n  get canRetryRead(): boolean {\n    return this.hasAspect(Aspect.RETRYABLE) && this.hasAspect(Aspect.READ_OPERATION);\n  }\n\n  get canRetryWrite(): boolean {\n    return this.hasAspect(Aspect.RETRYABLE) && this.hasAspect(Aspect.WRITE_OPERATION);\n  }\n}\n\nexport function defineAspects(\n  operation: { aspects?: Set<symbol> },\n  aspects: symbol | symbol[] | Set<symbol>\n): Set<symbol> {\n  if (!Array.isArray(aspects) && !(aspects instanceof Set)) {\n    aspects = [aspects];\n  }\n\n  aspects = new Set(aspects);\n  Object.defineProperty(operation, 'aspects', {\n    value: aspects,\n    writable: false\n  });\n\n  return aspects;\n}\n"], "mappings": ";;;;;;AA+HAA,OAAA,CAAAC,aAAA,GAAAA,aAAA;AA/HA,MAAAC,MAAA,GAAAC,OAAA;AAEA,MAAAC,iBAAA,GAAAD,OAAA;AAMaH,OAAA,CAAAK,MAAM,GAAG;EACpBC,cAAc,EAAEC,MAAM,CAAC,gBAAgB,CAAC;EACxCC,eAAe,EAAED,MAAM,CAAC,iBAAiB,CAAC;EAC1CE,SAAS,EAAEF,MAAM,CAAC,WAAW,CAAC;EAC9BG,WAAW,EAAEH,MAAM,CAAC,aAAa,CAAC;EAClCI,cAAc,EAAEJ,MAAM,CAAC,gBAAgB,CAAC;EACxCK,eAAe,EAAEL,MAAM,CAAC,iBAAiB,CAAC;EAC1CM,uBAAuB,EAAEN,MAAM,CAAC,yBAAyB,CAAC;EAC1DO,gBAAgB,EAAEP,MAAM,CAAC,kBAAkB;CACnC;AA4BV;;;;;;;AAOA,MAAsBQ,iBAAiB;EAmBrCC,YAAYC,OAAA,GAAwC,EAAE;IACpD,IAAI,CAACC,cAAc,GAAG,IAAI,CAACC,SAAS,CAACnB,OAAA,CAAAK,MAAM,CAACG,eAAe,CAAC,GACxDJ,iBAAA,CAAAgB,cAAc,CAACC,OAAO,GACrBjB,iBAAA,CAAAgB,cAAc,CAACE,WAAW,CAACL,OAAO,CAAC,IAAIb,iBAAA,CAAAgB,cAAc,CAACC,OAAQ;IAEnE;IACA,IAAI,CAACE,WAAW,GAAG,IAAArB,MAAA,CAAAsB,kBAAkB,EAACP,OAAO,CAAC;IAE9C,IAAI,CAACQ,QAAQ,GAAGR,OAAO,CAACS,OAAO,IAAI,IAAI,GAAGT,OAAO,CAACS,OAAO,GAAGC,SAAS;IAErE,IAAI,CAACV,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACW,kBAAkB,GAAG,CAAC,CAACX,OAAO,CAACW,kBAAkB;IACtD,IAAI,CAACC,iBAAiB,GAAG,KAAK;EAChC;EAYAV,SAASA,CAACW,MAAc;IACtB,MAAMC,IAAI,GAAG,IAAI,CAACf,WAAwC;IAC1D,IAAIe,IAAI,CAACC,OAAO,IAAI,IAAI,EAAE;MACxB,OAAO,KAAK;IACd;IAEA,OAAOD,IAAI,CAACC,OAAO,CAACC,GAAG,CAACH,MAAM,CAAC;EACjC;EAEA;EACA,IAAIJ,OAAOA,CAAA;IACT,OAAO,IAAI,CAACD,QAAQ;EACtB;EAEAS,YAAYA,CAAA;IACV,IAAI,CAACT,QAAQ,GAAGE,SAAS;EAC3B;EAEAQ,UAAUA,CAAA;IACR,OAAO,IAAI;EACb;EAEA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAI,CAACjB,SAAS,CAACnB,OAAA,CAAAK,MAAM,CAACI,SAAS,CAAC,IAAI,IAAI,CAACU,SAAS,CAACnB,OAAA,CAAAK,MAAM,CAACC,cAAc,CAAC;EAClF;EAEA,IAAI+B,aAAaA,CAAA;IACf,OAAO,IAAI,CAAClB,SAAS,CAACnB,OAAA,CAAAK,MAAM,CAACI,SAAS,CAAC,IAAI,IAAI,CAACU,SAAS,CAACnB,OAAA,CAAAK,MAAM,CAACG,eAAe,CAAC;EACnF;;AAxEFR,OAAA,CAAAe,iBAAA,GAAAA,iBAAA;AA2EA,SAAgBd,aAAaA,CAC3BqC,SAAoC,EACpCN,OAAwC;EAExC,IAAI,CAACO,KAAK,CAACC,OAAO,CAACR,OAAO,CAAC,IAAI,EAAEA,OAAO,YAAYS,GAAG,CAAC,EAAE;IACxDT,OAAO,GAAG,CAACA,OAAO,CAAC;EACrB;EAEAA,OAAO,GAAG,IAAIS,GAAG,CAACT,OAAO,CAAC;EAC1BU,MAAM,CAACC,cAAc,CAACL,SAAS,EAAE,SAAS,EAAE;IAC1CM,KAAK,EAAEZ,OAAO;IACda,QAAQ,EAAE;GACX,CAAC;EAEF,OAAOb,OAAO;AAChB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}