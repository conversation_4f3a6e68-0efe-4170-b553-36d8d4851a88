{"ast": null, "code": "function a(o, r) {\n  let t = o(),\n    n = new Set();\n  return {\n    getSnapshot() {\n      return t;\n    },\n    subscribe(e) {\n      return n.add(e), () => n.delete(e);\n    },\n    dispatch(e, ...s) {\n      let i = r[e].call(t, ...s);\n      i && (t = i, n.forEach(c => c()));\n    }\n  };\n}\nexport { a as createStore };", "map": {"version": 3, "names": ["a", "o", "r", "t", "n", "Set", "getSnapshot", "subscribe", "e", "add", "delete", "dispatch", "s", "i", "call", "for<PERSON>ach", "c", "createStore"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/utils/store.js"], "sourcesContent": ["function a(o,r){let t=o(),n=new Set;return{getSnapshot(){return t},subscribe(e){return n.add(e),()=>n.delete(e)},dispatch(e,...s){let i=r[e].call(t,...s);i&&(t=i,n.forEach(c=>c()))}}}export{a as createStore};\n"], "mappings": "AAAA,SAASA,CAACA,CAACC,CAAC,EAACC,CAAC,EAAC;EAAC,IAAIC,CAAC,GAACF,CAAC,CAAC,CAAC;IAACG,CAAC,GAAC,IAAIC,GAAG,CAAD,CAAC;EAAC,OAAM;IAACC,WAAWA,CAAA,EAAE;MAAC,OAAOH,CAAC;IAAA,CAAC;IAACI,SAASA,CAACC,CAAC,EAAC;MAAC,OAAOJ,CAAC,CAACK,GAAG,CAACD,CAAC,CAAC,EAAC,MAAIJ,CAAC,CAACM,MAAM,CAACF,CAAC,CAAC;IAAA,CAAC;IAACG,QAAQA,CAACH,CAAC,EAAC,GAAGI,CAAC,EAAC;MAAC,IAAIC,CAAC,GAACX,CAAC,CAACM,CAAC,CAAC,CAACM,IAAI,CAACX,CAAC,EAAC,GAA<PERSON>,CAAC,CAAC;MAACC,CAAC,KAAGV,CAAC,GAACU,CAAC,EAACT,CAAC,CAACW,OAAO,CAACC,CAAC,IAAEA,CAAC,CAAC,CAAC,CAAC,CAAC;IAAA;EAAC,CAAC;AAAA;AAAC,SAAOhB,CAAC,IAAIiB,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}