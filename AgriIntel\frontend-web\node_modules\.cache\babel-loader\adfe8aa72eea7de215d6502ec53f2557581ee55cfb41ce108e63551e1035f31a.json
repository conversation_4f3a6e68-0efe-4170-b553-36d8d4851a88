{"ast": null, "code": "import React from'react';import{useTranslation}from'react-i18next';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const LoadingSpinner=_ref=>{let{size='medium',color='primary',text,className=''}=_ref;const{t}=useTranslation();const sizeClasses={small:'h-4 w-4',medium:'h-8 w-8',large:'h-12 w-12'};const colorClasses={primary:'border-primary-600',secondary:'border-secondary-600',white:'border-white'};return/*#__PURE__*/_jsxs(\"div\",{className:\"flex flex-col items-center justify-center \".concat(className),children:[/*#__PURE__*/_jsx(\"div\",{className:\"\\n          animate-spin rounded-full border-2 border-t-transparent\\n          \".concat(sizeClasses[size],\"\\n          \").concat(colorClasses[color],\"\\n        \"),role:\"status\",\"aria-label\":text||t('common.loading')}),text&&/*#__PURE__*/_jsx(\"p\",{className:\"mt-2 text-sm text-gray-600 dark:text-gray-400\",children:text})]});};export default LoadingSpinner;", "map": {"version": 3, "names": ["React", "useTranslation", "jsx", "_jsx", "jsxs", "_jsxs", "LoadingSpinner", "_ref", "size", "color", "text", "className", "t", "sizeClasses", "small", "medium", "large", "colorClasses", "primary", "secondary", "white", "concat", "children", "role"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/components/common/LoadingSpinner.tsx"], "sourcesContent": ["import React from 'react';\nimport { useTranslation } from 'react-i18next';\n\ninterface LoadingSpinnerProps {\n  size?: 'small' | 'medium' | 'large';\n  color?: 'primary' | 'secondary' | 'white';\n  text?: string;\n  className?: string;\n}\n\nconst LoadingSpinner: React.FC<LoadingSpinnerProps> = ({\n  size = 'medium',\n  color = 'primary',\n  text,\n  className = '',\n}) => {\n  const { t } = useTranslation();\n\n  const sizeClasses = {\n    small: 'h-4 w-4',\n    medium: 'h-8 w-8',\n    large: 'h-12 w-12',\n  };\n\n  const colorClasses = {\n    primary: 'border-primary-600',\n    secondary: 'border-secondary-600',\n    white: 'border-white',\n  };\n\n  return (\n    <div className={`flex flex-col items-center justify-center ${className}`}>\n      <div\n        className={`\n          animate-spin rounded-full border-2 border-t-transparent\n          ${sizeClasses[size]}\n          ${colorClasses[color]}\n        `}\n        role=\"status\"\n        aria-label={text || t('common.loading')}\n      />\n      {text && (\n        <p className=\"mt-2 text-sm text-gray-600 dark:text-gray-400\">\n          {text}\n        </p>\n      )}\n    </div>\n  );\n};\n\nexport default LoadingSpinner;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,cAAc,KAAQ,eAAe,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAS/C,KAAM,CAAAC,cAA6C,CAAGC,IAAA,EAKhD,IALiD,CACrDC,IAAI,CAAG,QAAQ,CACfC,KAAK,CAAG,SAAS,CACjBC,IAAI,CACJC,SAAS,CAAG,EACd,CAAC,CAAAJ,IAAA,CACC,KAAM,CAAEK,CAAE,CAAC,CAAGX,cAAc,CAAC,CAAC,CAE9B,KAAM,CAAAY,WAAW,CAAG,CAClBC,KAAK,CAAE,SAAS,CAChBC,MAAM,CAAE,SAAS,CACjBC,KAAK,CAAE,WACT,CAAC,CAED,KAAM,CAAAC,YAAY,CAAG,CACnBC,OAAO,CAAE,oBAAoB,CAC7BC,SAAS,CAAE,sBAAsB,CACjCC,KAAK,CAAE,cACT,CAAC,CAED,mBACEf,KAAA,QAAKM,SAAS,8CAAAU,MAAA,CAA+CV,SAAS,CAAG,CAAAW,QAAA,eACvEnB,IAAA,QACEQ,SAAS,mFAAAU,MAAA,CAELR,WAAW,CAACL,IAAI,CAAC,iBAAAa,MAAA,CACjBJ,YAAY,CAACR,KAAK,CAAC,cACrB,CACFc,IAAI,CAAC,QAAQ,CACb,aAAYb,IAAI,EAAIE,CAAC,CAAC,gBAAgB,CAAE,CACzC,CAAC,CACDF,IAAI,eACHP,IAAA,MAAGQ,SAAS,CAAC,+CAA+C,CAAAW,QAAA,CACzDZ,IAAI,CACJ,CACJ,EACE,CAAC,CAEV,CAAC,CAED,cAAe,CAAAJ,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}