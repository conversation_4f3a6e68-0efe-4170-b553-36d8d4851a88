{"ast": null, "code": "var o = (r => (r.<PERSON> = \" \", r.<PERSON><PERSON> = \"Enter\", r.<PERSON> = \"Escape\", r.<PERSON>space = \"Backspace\", r.Delete = \"Delete\", r.<PERSON> = \"ArrowLeft\", r.<PERSON>p = \"ArrowUp\", r.<PERSON> = \"ArrowRight\", r.<PERSON> = \"ArrowDown\", r.Home = \"Home\", r.End = \"End\", r.PageUp = \"PageUp\", r.PageDown = \"PageDown\", r.Tab = \"Tab\", r))(o || {});\nexport { o as Keys };", "map": {"version": 3, "names": ["o", "r", "Space", "Enter", "Escape", "Backspace", "Delete", "ArrowLeft", "ArrowUp", "ArrowRight", "ArrowDown", "Home", "End", "PageUp", "PageDown", "Tab", "Keys"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/components/keyboard.js"], "sourcesContent": ["var o=(r=>(r.<PERSON>=\" \",r.<PERSON><PERSON>=\"Enter\",r.<PERSON>=\"Escape\",r.<PERSON>space=\"Backspace\",r.Delete=\"Delete\",r.<PERSON>=\"ArrowLeft\",r.<PERSON>p=\"ArrowUp\",r.<PERSON>=\"ArrowRight\",r.<PERSON>=\"ArrowDown\",r.Home=\"Home\",r.End=\"End\",r.PageUp=\"PageUp\",r.PageDown=\"PageDown\",r.Tab=\"Tab\",r))(o||{});export{o as Keys};\n"], "mappings": "AAAA,IAAIA,CAAC,GAAC,CAACC,CAAC,KAAGA,CAAC,CAACC,KAAK,GAAC,GAAG,EAACD,CAAC,CAACE,KAAK,GAAC,OAAO,EAACF,CAAC,CAACG,MAAM,GAAC,QAAQ,EAACH,CAAC,CAACI,SAAS,GAAC,WAAW,EAACJ,CAAC,CAACK,MAAM,GAAC,QAAQ,EAACL,CAAC,CAACM,SAAS,GAAC,WAAW,EAACN,CAAC,CAACO,OAAO,GAAC,SAAS,EAACP,CAAC,CAACQ,UAAU,GAAC,YAAY,EAACR,CAAC,CAACS,SAAS,GAAC,WAAW,EAACT,CAAC,CAACU,IAAI,GAAC,MAAM,EAACV,CAAC,CAACW,GAAG,GAAC,KAAK,EAACX,CAAC,CAACY,MAAM,GAAC,QAAQ,EAACZ,CAAC,CAACa,QAAQ,GAAC,UAAU,EAACb,CAAC,CAACc,GAAG,GAAC,KAAK,EAACd,CAAC,CAAC,EAAED,CAAC,IAAE,CAAC,CAAC,CAAC;AAAC,SAAOA,CAAC,IAAIgB,IAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}