import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import {
  ChartBarIcon,
  HeartIcon,
  ExclamationTriangleIcon,
  CalendarIcon,
} from '@heroicons/react/24/outline';

import { AppDispatch, RootState } from '../../store/store';
import { fetchAnimalStatistics } from '../../store/slices/animalSlice';
import { setPageTitle, setBreadcrumbs } from '../../store/slices/uiSlice';

const DashboardPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { t } = useTranslation();
  
  const { user } = useSelector((state: RootState) => state.auth);
  const { statistics } = useSelector((state: RootState) => state.animals);

  useEffect(() => {
    dispatch(setPageTitle(t('dashboard.title')));
    dispatch(setBreadcrumbs([
      { label: t('dashboard.title') }
    ]));
    
    // Fetch dashboard data
    dispatch(fetchAnimalStatistics());
  }, [dispatch, t]);

  const stats = [
    {
      name: t('dashboard.totalAnimals'),
      value: statistics?.total || 0,
      icon: ChartBarIcon,
      color: 'bg-blue-500',
      change: '+12%',
      changeType: 'positive',
    },
    {
      name: t('dashboard.healthyAnimals'),
      value: statistics?.byHealthStatus?.healthy || 0,
      icon: HeartIcon,
      color: 'bg-green-500',
      change: '+2%',
      changeType: 'positive',
    },
    {
      name: t('dashboard.pregnantAnimals'),
      value: statistics?.byBreedingStatus?.pregnant || 0,
      icon: CalendarIcon,
      color: 'bg-purple-500',
      change: '+5%',
      changeType: 'positive',
    },
    {
      name: t('dashboard.healthAlerts'),
      value: 8,
      icon: ExclamationTriangleIcon,
      color: 'bg-red-500',
      change: '-3%',
      changeType: 'negative',
    },
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-white rounded-lg shadow-soft p-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {t('dashboard.welcome')}, {user?.firstName}!
        </h1>
        <p className="mt-2 text-gray-600">
          Here's what's happening with your livestock today.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <div key={stat.name} className="card">
            <div className="card-body">
              <div className="flex items-center">
                <div className="flex-shrink-0">
                  <div className={`w-8 h-8 rounded-md ${stat.color} flex items-center justify-center`}>
                    <stat.icon className="w-5 h-5 text-white" />
                  </div>
                </div>
                <div className="ml-5 w-0 flex-1">
                  <dl>
                    <dt className="text-sm font-medium text-gray-500 truncate">
                      {stat.name}
                    </dt>
                    <dd className="flex items-baseline">
                      <div className="text-2xl font-semibold text-gray-900">
                        {stat.value}
                      </div>
                      <div className={`ml-2 flex items-baseline text-sm font-semibold ${
                        stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {stat.change}
                      </div>
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 gap-5 lg:grid-cols-2">
        {/* Recent Activity */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">
              {t('dashboard.recentActivity')}
            </h3>
          </div>
          <div className="card-body">
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">Cow C001 vaccinated</p>
                  <p className="text-xs text-gray-500">2 hours ago</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">New sheep S015 added</p>
                  <p className="text-xs text-gray-500">4 hours ago</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">Health check due for Pig P003</p>
                  <p className="text-xs text-gray-500">6 hours ago</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Alerts */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">
              {t('dashboard.alerts')}
            </h3>
          </div>
          <div className="card-body">
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-red-400 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">Vaccination overdue for 3 animals</p>
                  <p className="text-xs text-gray-500">High priority</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-orange-400 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">Feed inventory running low</p>
                  <p className="text-xs text-gray-500">Medium priority</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
                <div className="flex-1">
                  <p className="text-sm text-gray-900">Breeding season approaching</p>
                  <p className="text-xs text-gray-500">Low priority</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 gap-5 lg:grid-cols-2">
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">
              Animals by Species
            </h3>
          </div>
          <div className="card-body">
            <div className="space-y-3">
              {statistics?.bySpecies && Object.entries(statistics.bySpecies).map(([species, count]) => (
                <div key={species} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 capitalize">{species}</span>
                  <span className="text-sm font-medium text-gray-900">{count}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">
              Health Status Overview
            </h3>
          </div>
          <div className="card-body">
            <div className="space-y-3">
              {statistics?.byHealthStatus && Object.entries(statistics.byHealthStatus).map(([status, count]) => (
                <div key={status} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 capitalize">{status}</span>
                  <span className="text-sm font-medium text-gray-900">{count}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
