import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useTranslation } from 'react-i18next';
import {
  ChartBarIcon,
  HeartIcon,
  ExclamationTriangleIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ArrowTrendingUpIcon,
} from '@heroicons/react/24/outline';

import { AppDispatch, RootState } from '../../store/store';
import { fetchAnimalStatistics } from '../../store/slices/animalSlice';
import { setPageTitle, setBreadcrumbs } from '../../store/slices/uiSlice';

// Dashboard Components
import DashboardWidget from '../../components/dashboard/DashboardWidget';
import StatCard from '../../components/dashboard/StatCard';
import ActivityFeed from '../../components/dashboard/ActivityFeed';

const DashboardPage: React.FC = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { t } = useTranslation();
  
  const { user } = useSelector((state: RootState) => state.auth);
  const { statistics } = useSelector((state: RootState) => state.animals);

  useEffect(() => {
    dispatch(setPageTitle(t('dashboard.title')));
    dispatch(setBreadcrumbs([
      { label: t('dashboard.title') }
    ]));
    
    // Fetch dashboard data
    dispatch(fetchAnimalStatistics());
  }, [dispatch, t]);

  const stats = [
    {
      name: t('dashboard.totalAnimals'),
      value: statistics?.total || 0,
      icon: ChartBarIcon,
      color: 'bg-blue-500',
      change: '+12%',
      changeType: 'positive' as const,
    },
    {
      name: t('dashboard.healthyAnimals'),
      value: statistics?.byHealthStatus?.healthy || 0,
      icon: HeartIcon,
      color: 'bg-green-500',
      change: '+2%',
      changeType: 'positive' as const,
    },
    {
      name: t('dashboard.pregnantAnimals'),
      value: statistics?.byBreedingStatus?.pregnant || 0,
      icon: CalendarIcon,
      color: 'bg-purple-500',
      change: '+5%',
      changeType: 'positive' as const,
    },
    {
      name: t('dashboard.healthAlerts'),
      value: 8,
      icon: ExclamationTriangleIcon,
      color: 'bg-red-500',
      change: '-3%',
      changeType: 'negative' as const,
    },
  ];

  return (
    <div className="space-y-6">
      {/* Welcome Section */}
      <div className="bg-white rounded-lg shadow-soft p-6">
        <h1 className="text-2xl font-bold text-gray-900">
          {t('dashboard.welcome')}, {user?.firstName}!
        </h1>
        <p className="mt-2 text-gray-600">
          Here's what's happening with your livestock today.
        </p>
      </div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        {stats.map((stat) => (
          <StatCard
            key={stat.name}
            name={stat.name}
            value={stat.value}
            icon={stat.icon}
            color={stat.color}
            change={stat.change}
            changeType={stat.changeType}
          />
        ))}
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 gap-5 lg:grid-cols-2">
        {/* Recent Activity */}
        <DashboardWidget title={t('dashboard.recentActivity')}>
          <ActivityFeed maxItems={6} />
        </DashboardWidget>

        {/* Alerts */}
        <DashboardWidget title={t('dashboard.alerts')}>
          <div className="space-y-4">
            <div className="flex items-center space-x-3 p-3 border-l-4 border-l-red-500 bg-red-50 rounded-r-md">
              <div className="w-2 h-2 bg-red-400 rounded-full flex-shrink-0"></div>
              <div className="flex-1">
                <p className="text-sm text-gray-900 font-medium">Vaccination overdue for 3 animals</p>
                <p className="text-xs text-red-600">High priority • Immediate action required</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 border-l-4 border-l-yellow-500 bg-yellow-50 rounded-r-md">
              <div className="w-2 h-2 bg-orange-400 rounded-full flex-shrink-0"></div>
              <div className="flex-1">
                <p className="text-sm text-gray-900 font-medium">Feed inventory running low</p>
                <p className="text-xs text-yellow-600">Medium priority • Order within 3 days</p>
              </div>
            </div>
            <div className="flex items-center space-x-3 p-3 border-l-4 border-l-blue-500 bg-blue-50 rounded-r-md">
              <div className="w-2 h-2 bg-blue-400 rounded-full flex-shrink-0"></div>
              <div className="flex-1">
                <p className="text-sm text-gray-900 font-medium">Breeding season approaching</p>
                <p className="text-xs text-blue-600">Low priority • Plan for next month</p>
              </div>
            </div>
          </div>
        </DashboardWidget>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 gap-5 lg:grid-cols-2">
        {/* Animals by Species Chart */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">
              {t('dashboard.animalsBySpecies')}
            </h3>
          </div>
          <div className="card-body">
            <div className="h-64">
              {statistics?.bySpecies ? (
                <div className="space-y-3">
                  {Object.entries(statistics.bySpecies).map(([species, count]) => {
                    const percentage = ((count / statistics.total) * 100).toFixed(1);
                    return (
                      <div key={species} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`w-3 h-3 rounded-full ${
                            species === 'cattle' ? 'bg-blue-500' :
                            species === 'sheep' ? 'bg-green-500' :
                            species === 'goat' ? 'bg-yellow-500' :
                            'bg-purple-500'
                          }`}></div>
                          <span className="text-sm text-gray-600 capitalize">{species}</span>
                        </div>
                        <div className="text-right">
                          <span className="text-sm font-medium text-gray-900">{count}</span>
                          <span className="text-xs text-gray-500 ml-2">({percentage}%)</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  Loading chart data...
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Health Status Chart */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">
              {t('dashboard.healthStatusOverview')}
            </h3>
          </div>
          <div className="card-body">
            <div className="h-64">
              {statistics?.byHealthStatus ? (
                <div className="space-y-3">
                  {Object.entries(statistics.byHealthStatus).map(([status, count]) => {
                    const percentage = ((count / statistics.total) * 100).toFixed(1);
                    return (
                      <div key={status} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`w-3 h-3 rounded-full ${
                            status === 'healthy' ? 'bg-green-500' :
                            status === 'sick' ? 'bg-red-500' :
                            status === 'injured' ? 'bg-orange-500' :
                            'bg-blue-500'
                          }`}></div>
                          <span className="text-sm text-gray-600 capitalize">{status}</span>
                        </div>
                        <div className="text-right">
                          <span className="text-sm font-medium text-gray-900">{count}</span>
                          <span className="text-xs text-gray-500 ml-2">({percentage}%)</span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="flex items-center justify-center h-full text-gray-500">
                  Loading chart data...
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Additional Analytics Section */}
      <div className="grid grid-cols-1 gap-5 lg:grid-cols-3">
        {/* Breeding Status */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">
              {t('dashboard.breedingStatus')}
            </h3>
          </div>
          <div className="card-body">
            <div className="space-y-3">
              {statistics?.byBreedingStatus && Object.entries(statistics.byBreedingStatus).map(([status, count]) => (
                <div key={status} className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 capitalize">{status}</span>
                  <span className="text-sm font-medium text-gray-900">{count}</span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Monthly Growth */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">
              {t('dashboard.monthlyGrowth')}
            </h3>
          </div>
          <div className="card-body">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">New Births</span>
                <span className="text-sm font-medium text-green-600">+12</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Acquisitions</span>
                <span className="text-sm font-medium text-blue-600">+8</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Sales</span>
                <span className="text-sm font-medium text-red-600">-5</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Net Growth</span>
                <span className="text-sm font-medium text-gray-900">+15</span>
              </div>
            </div>
          </div>
        </div>

        {/* Financial Summary */}
        <div className="card">
          <div className="card-header">
            <h3 className="text-lg font-medium text-gray-900">
              {t('dashboard.financialSummary')}
            </h3>
          </div>
          <div className="card-body">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Monthly Revenue</span>
                <span className="text-sm font-medium text-green-600">R 45,200</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Feed Costs</span>
                <span className="text-sm font-medium text-red-600">R 18,500</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Vet Costs</span>
                <span className="text-sm font-medium text-red-600">R 3,200</span>
              </div>
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Net Profit</span>
                <span className="text-sm font-medium text-gray-900">R 23,500</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
