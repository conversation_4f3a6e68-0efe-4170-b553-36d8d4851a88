{"ast": null, "code": "let i18nInstance;\nexport function setI18n(instance) {\n  i18nInstance = instance;\n}\nexport function getI18n() {\n  return i18nInstance;\n}", "map": {"version": 3, "names": ["i18nInstance", "setI18n", "instance", "getI18n"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-i18next/dist/es/i18nInstance.js"], "sourcesContent": ["let i18nInstance;\nexport function setI18n(instance) {\n  i18nInstance = instance;\n}\nexport function getI18n() {\n  return i18nInstance;\n}"], "mappings": "AAAA,IAAIA,YAAY;AAChB,OAAO,SAASC,OAAOA,CAACC,QAAQ,EAAE;EAChCF,YAAY,GAAGE,QAAQ;AACzB;AACA,OAAO,SAASC,OAAOA,CAAA,EAAG;EACxB,OAAOH,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}