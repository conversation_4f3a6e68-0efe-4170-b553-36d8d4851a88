{"ast": null, "code": "import { getBatch } from './batch'; // encapsulates the subscription logic for connecting a component to the redux store, as\n// well as nesting subscriptions of descendant components, so that we can ensure the\n// ancestor components re-render before descendants\n\nfunction createListenerCollection() {\n  const batch = getBatch();\n  let first = null;\n  let last = null;\n  return {\n    clear() {\n      first = null;\n      last = null;\n    },\n    notify() {\n      batch(() => {\n        let listener = first;\n        while (listener) {\n          listener.callback();\n          listener = listener.next;\n        }\n      });\n    },\n    get() {\n      let listeners = [];\n      let listener = first;\n      while (listener) {\n        listeners.push(listener);\n        listener = listener.next;\n      }\n      return listeners;\n    },\n    subscribe(callback) {\n      let isSubscribed = true;\n      let listener = last = {\n        callback,\n        next: null,\n        prev: last\n      };\n      if (listener.prev) {\n        listener.prev.next = listener;\n      } else {\n        first = listener;\n      }\n      return function unsubscribe() {\n        if (!isSubscribed || first === null) return;\n        isSubscribed = false;\n        if (listener.next) {\n          listener.next.prev = listener.prev;\n        } else {\n          last = listener.prev;\n        }\n        if (listener.prev) {\n          listener.prev.next = listener.next;\n        } else {\n          first = listener.next;\n        }\n      };\n    }\n  };\n}\nconst nullListeners = {\n  notify() {},\n  get: () => []\n};\nexport function createSubscription(store, parentSub) {\n  let unsubscribe;\n  let listeners = nullListeners; // Reasons to keep the subscription active\n\n  let subscriptionsAmount = 0; // Is this specific subscription subscribed (or only nested ones?)\n\n  let selfSubscribed = false;\n  function addNestedSub(listener) {\n    trySubscribe();\n    const cleanupListener = listeners.subscribe(listener); // cleanup nested sub\n\n    let removed = false;\n    return () => {\n      if (!removed) {\n        removed = true;\n        cleanupListener();\n        tryUnsubscribe();\n      }\n    };\n  }\n  function notifyNestedSubs() {\n    listeners.notify();\n  }\n  function handleChangeWrapper() {\n    if (subscription.onStateChange) {\n      subscription.onStateChange();\n    }\n  }\n  function isSubscribed() {\n    return selfSubscribed;\n  }\n  function trySubscribe() {\n    subscriptionsAmount++;\n    if (!unsubscribe) {\n      unsubscribe = parentSub ? parentSub.addNestedSub(handleChangeWrapper) : store.subscribe(handleChangeWrapper);\n      listeners = createListenerCollection();\n    }\n  }\n  function tryUnsubscribe() {\n    subscriptionsAmount--;\n    if (unsubscribe && subscriptionsAmount === 0) {\n      unsubscribe();\n      unsubscribe = undefined;\n      listeners.clear();\n      listeners = nullListeners;\n    }\n  }\n  function trySubscribeSelf() {\n    if (!selfSubscribed) {\n      selfSubscribed = true;\n      trySubscribe();\n    }\n  }\n  function tryUnsubscribeSelf() {\n    if (selfSubscribed) {\n      selfSubscribed = false;\n      tryUnsubscribe();\n    }\n  }\n  const subscription = {\n    addNestedSub,\n    notifyNestedSubs,\n    handleChangeWrapper,\n    isSubscribed,\n    trySubscribe: trySubscribeSelf,\n    tryUnsubscribe: tryUnsubscribeSelf,\n    getListeners: () => listeners\n  };\n  return subscription;\n}", "map": {"version": 3, "names": ["getBatch", "createListenerCollection", "batch", "first", "last", "clear", "notify", "listener", "callback", "next", "get", "listeners", "push", "subscribe", "isSubscribed", "prev", "unsubscribe", "nullListeners", "createSubscription", "store", "parentSub", "subscriptionsAmount", "selfSubscribed", "addNestedSub", "trySubscribe", "cleanupListener", "removed", "tryUnsubscribe", "notifyNestedSubs", "handleChangeWrapper", "subscription", "onStateChange", "undefined", "trySubscribeSelf", "tryUnsubscribeSelf", "getListeners"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-redux/es/utils/Subscription.js"], "sourcesContent": ["import { getBatch } from './batch'; // encapsulates the subscription logic for connecting a component to the redux store, as\n// well as nesting subscriptions of descendant components, so that we can ensure the\n// ancestor components re-render before descendants\n\nfunction createListenerCollection() {\n  const batch = getBatch();\n  let first = null;\n  let last = null;\n  return {\n    clear() {\n      first = null;\n      last = null;\n    },\n\n    notify() {\n      batch(() => {\n        let listener = first;\n\n        while (listener) {\n          listener.callback();\n          listener = listener.next;\n        }\n      });\n    },\n\n    get() {\n      let listeners = [];\n      let listener = first;\n\n      while (listener) {\n        listeners.push(listener);\n        listener = listener.next;\n      }\n\n      return listeners;\n    },\n\n    subscribe(callback) {\n      let isSubscribed = true;\n      let listener = last = {\n        callback,\n        next: null,\n        prev: last\n      };\n\n      if (listener.prev) {\n        listener.prev.next = listener;\n      } else {\n        first = listener;\n      }\n\n      return function unsubscribe() {\n        if (!isSubscribed || first === null) return;\n        isSubscribed = false;\n\n        if (listener.next) {\n          listener.next.prev = listener.prev;\n        } else {\n          last = listener.prev;\n        }\n\n        if (listener.prev) {\n          listener.prev.next = listener.next;\n        } else {\n          first = listener.next;\n        }\n      };\n    }\n\n  };\n}\n\nconst nullListeners = {\n  notify() {},\n\n  get: () => []\n};\nexport function createSubscription(store, parentSub) {\n  let unsubscribe;\n  let listeners = nullListeners; // Reasons to keep the subscription active\n\n  let subscriptionsAmount = 0; // Is this specific subscription subscribed (or only nested ones?)\n\n  let selfSubscribed = false;\n\n  function addNestedSub(listener) {\n    trySubscribe();\n    const cleanupListener = listeners.subscribe(listener); // cleanup nested sub\n\n    let removed = false;\n    return () => {\n      if (!removed) {\n        removed = true;\n        cleanupListener();\n        tryUnsubscribe();\n      }\n    };\n  }\n\n  function notifyNestedSubs() {\n    listeners.notify();\n  }\n\n  function handleChangeWrapper() {\n    if (subscription.onStateChange) {\n      subscription.onStateChange();\n    }\n  }\n\n  function isSubscribed() {\n    return selfSubscribed;\n  }\n\n  function trySubscribe() {\n    subscriptionsAmount++;\n\n    if (!unsubscribe) {\n      unsubscribe = parentSub ? parentSub.addNestedSub(handleChangeWrapper) : store.subscribe(handleChangeWrapper);\n      listeners = createListenerCollection();\n    }\n  }\n\n  function tryUnsubscribe() {\n    subscriptionsAmount--;\n\n    if (unsubscribe && subscriptionsAmount === 0) {\n      unsubscribe();\n      unsubscribe = undefined;\n      listeners.clear();\n      listeners = nullListeners;\n    }\n  }\n\n  function trySubscribeSelf() {\n    if (!selfSubscribed) {\n      selfSubscribed = true;\n      trySubscribe();\n    }\n  }\n\n  function tryUnsubscribeSelf() {\n    if (selfSubscribed) {\n      selfSubscribed = false;\n      tryUnsubscribe();\n    }\n  }\n\n  const subscription = {\n    addNestedSub,\n    notifyNestedSubs,\n    handleChangeWrapper,\n    isSubscribed,\n    trySubscribe: trySubscribeSelf,\n    tryUnsubscribe: tryUnsubscribeSelf,\n    getListeners: () => listeners\n  };\n  return subscription;\n}"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,SAAS,CAAC,CAAC;AACpC;AACA;;AAEA,SAASC,wBAAwBA,CAAA,EAAG;EAClC,MAAMC,KAAK,GAAGF,QAAQ,CAAC,CAAC;EACxB,IAAIG,KAAK,GAAG,IAAI;EAChB,IAAIC,IAAI,GAAG,IAAI;EACf,OAAO;IACLC,KAAKA,CAAA,EAAG;MACNF,KAAK,GAAG,IAAI;MACZC,IAAI,GAAG,IAAI;IACb,CAAC;IAEDE,MAAMA,CAAA,EAAG;MACPJ,KAAK,CAAC,MAAM;QACV,IAAIK,QAAQ,GAAGJ,KAAK;QAEpB,OAAOI,QAAQ,EAAE;UACfA,QAAQ,CAACC,QAAQ,CAAC,CAAC;UACnBD,QAAQ,GAAGA,QAAQ,CAACE,IAAI;QAC1B;MACF,CAAC,CAAC;IACJ,CAAC;IAEDC,GAAGA,CAAA,EAAG;MACJ,IAAIC,SAAS,GAAG,EAAE;MAClB,IAAIJ,QAAQ,GAAGJ,KAAK;MAEpB,OAAOI,QAAQ,EAAE;QACfI,SAAS,CAACC,IAAI,CAACL,QAAQ,CAAC;QACxBA,QAAQ,GAAGA,QAAQ,CAACE,IAAI;MAC1B;MAEA,OAAOE,SAAS;IAClB,CAAC;IAEDE,SAASA,CAACL,QAAQ,EAAE;MAClB,IAAIM,YAAY,GAAG,IAAI;MACvB,IAAIP,QAAQ,GAAGH,IAAI,GAAG;QACpBI,QAAQ;QACRC,IAAI,EAAE,IAAI;QACVM,IAAI,EAAEX;MACR,CAAC;MAED,IAAIG,QAAQ,CAACQ,IAAI,EAAE;QACjBR,QAAQ,CAACQ,IAAI,CAACN,IAAI,GAAGF,QAAQ;MAC/B,CAAC,MAAM;QACLJ,KAAK,GAAGI,QAAQ;MAClB;MAEA,OAAO,SAASS,WAAWA,CAAA,EAAG;QAC5B,IAAI,CAACF,YAAY,IAAIX,KAAK,KAAK,IAAI,EAAE;QACrCW,YAAY,GAAG,KAAK;QAEpB,IAAIP,QAAQ,CAACE,IAAI,EAAE;UACjBF,QAAQ,CAACE,IAAI,CAACM,IAAI,GAAGR,QAAQ,CAACQ,IAAI;QACpC,CAAC,MAAM;UACLX,IAAI,GAAGG,QAAQ,CAACQ,IAAI;QACtB;QAEA,IAAIR,QAAQ,CAACQ,IAAI,EAAE;UACjBR,QAAQ,CAACQ,IAAI,CAACN,IAAI,GAAGF,QAAQ,CAACE,IAAI;QACpC,CAAC,MAAM;UACLN,KAAK,GAAGI,QAAQ,CAACE,IAAI;QACvB;MACF,CAAC;IACH;EAEF,CAAC;AACH;AAEA,MAAMQ,aAAa,GAAG;EACpBX,MAAMA,CAAA,EAAG,CAAC,CAAC;EAEXI,GAAG,EAAEA,CAAA,KAAM;AACb,CAAC;AACD,OAAO,SAASQ,kBAAkBA,CAACC,KAAK,EAAEC,SAAS,EAAE;EACnD,IAAIJ,WAAW;EACf,IAAIL,SAAS,GAAGM,aAAa,CAAC,CAAC;;EAE/B,IAAII,mBAAmB,GAAG,CAAC,CAAC,CAAC;;EAE7B,IAAIC,cAAc,GAAG,KAAK;EAE1B,SAASC,YAAYA,CAAChB,QAAQ,EAAE;IAC9BiB,YAAY,CAAC,CAAC;IACd,MAAMC,eAAe,GAAGd,SAAS,CAACE,SAAS,CAACN,QAAQ,CAAC,CAAC,CAAC;;IAEvD,IAAImB,OAAO,GAAG,KAAK;IACnB,OAAO,MAAM;MACX,IAAI,CAACA,OAAO,EAAE;QACZA,OAAO,GAAG,IAAI;QACdD,eAAe,CAAC,CAAC;QACjBE,cAAc,CAAC,CAAC;MAClB;IACF,CAAC;EACH;EAEA,SAASC,gBAAgBA,CAAA,EAAG;IAC1BjB,SAAS,CAACL,MAAM,CAAC,CAAC;EACpB;EAEA,SAASuB,mBAAmBA,CAAA,EAAG;IAC7B,IAAIC,YAAY,CAACC,aAAa,EAAE;MAC9BD,YAAY,CAACC,aAAa,CAAC,CAAC;IAC9B;EACF;EAEA,SAASjB,YAAYA,CAAA,EAAG;IACtB,OAAOQ,cAAc;EACvB;EAEA,SAASE,YAAYA,CAAA,EAAG;IACtBH,mBAAmB,EAAE;IAErB,IAAI,CAACL,WAAW,EAAE;MAChBA,WAAW,GAAGI,SAAS,GAAGA,SAAS,CAACG,YAAY,CAACM,mBAAmB,CAAC,GAAGV,KAAK,CAACN,SAAS,CAACgB,mBAAmB,CAAC;MAC5GlB,SAAS,GAAGV,wBAAwB,CAAC,CAAC;IACxC;EACF;EAEA,SAAS0B,cAAcA,CAAA,EAAG;IACxBN,mBAAmB,EAAE;IAErB,IAAIL,WAAW,IAAIK,mBAAmB,KAAK,CAAC,EAAE;MAC5CL,WAAW,CAAC,CAAC;MACbA,WAAW,GAAGgB,SAAS;MACvBrB,SAAS,CAACN,KAAK,CAAC,CAAC;MACjBM,SAAS,GAAGM,aAAa;IAC3B;EACF;EAEA,SAASgB,gBAAgBA,CAAA,EAAG;IAC1B,IAAI,CAACX,cAAc,EAAE;MACnBA,cAAc,GAAG,IAAI;MACrBE,YAAY,CAAC,CAAC;IAChB;EACF;EAEA,SAASU,kBAAkBA,CAAA,EAAG;IAC5B,IAAIZ,cAAc,EAAE;MAClBA,cAAc,GAAG,KAAK;MACtBK,cAAc,CAAC,CAAC;IAClB;EACF;EAEA,MAAMG,YAAY,GAAG;IACnBP,YAAY;IACZK,gBAAgB;IAChBC,mBAAmB;IACnBf,YAAY;IACZU,YAAY,EAAES,gBAAgB;IAC9BN,cAAc,EAAEO,kBAAkB;IAClCC,YAAY,EAAEA,CAAA,KAAMxB;EACtB,CAAC;EACD,OAAOmB,YAAY;AACrB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}