{"ast": null, "code": "'use strict';\n\n/** @type {import('./uri')} */\nmodule.exports = URIError;", "map": {"version": 3, "names": ["module", "exports", "URIError"], "sources": ["C:/Users/<USER>/node_modules/es-errors/uri.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./uri')} */\nmodule.exports = URIError;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAGC,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}