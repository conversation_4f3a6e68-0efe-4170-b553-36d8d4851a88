{"ast": null, "code": "import React from'react';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const DashboardWidget=_ref=>{let{title,children,className='',headerAction}=_ref;return/*#__PURE__*/_jsxs(\"div\",{className:\"card \".concat(className),children:[/*#__PURE__*/_jsxs(\"div\",{className:\"card-header flex items-center justify-between\",children:[/*#__PURE__*/_jsx(\"h3\",{className:\"text-lg font-medium text-gray-900\",children:title}),headerAction&&/*#__PURE__*/_jsx(\"div\",{children:headerAction})]}),/*#__PURE__*/_jsx(\"div\",{className:\"card-body\",children:children})]});};export default DashboardWidget;", "map": {"version": 3, "names": ["React", "jsx", "_jsx", "jsxs", "_jsxs", "DashboardWidget", "_ref", "title", "children", "className", "headerAction", "concat"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/components/dashboard/DashboardWidget.tsx"], "sourcesContent": ["import React from 'react';\nimport { useTranslation } from 'react-i18next';\n\ninterface DashboardWidgetProps {\n  title: string;\n  children: React.ReactNode;\n  className?: string;\n  headerAction?: React.ReactNode;\n}\n\nconst DashboardWidget: React.FC<DashboardWidgetProps> = ({\n  title,\n  children,\n  className = '',\n  headerAction\n}) => {\n  return (\n    <div className={`card ${className}`}>\n      <div className=\"card-header flex items-center justify-between\">\n        <h3 className=\"text-lg font-medium text-gray-900\">{title}</h3>\n        {headerAction && <div>{headerAction}</div>}\n      </div>\n      <div className=\"card-body\">\n        {children}\n      </div>\n    </div>\n  );\n};\n\nexport default DashboardWidget;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAU1B,KAAM,CAAAC,eAA+C,CAAGC,IAAA,EAKlD,IALmD,CACvDC,KAAK,CACLC,QAAQ,CACRC,SAAS,CAAG,EAAE,CACdC,YACF,CAAC,CAAAJ,IAAA,CACC,mBACEF,KAAA,QAAKK,SAAS,SAAAE,MAAA,CAAUF,SAAS,CAAG,CAAAD,QAAA,eAClCJ,KAAA,QAAKK,SAAS,CAAC,+CAA+C,CAAAD,QAAA,eAC5DN,IAAA,OAAIO,SAAS,CAAC,mCAAmC,CAAAD,QAAA,CAAED,KAAK,CAAK,CAAC,CAC7DG,YAAY,eAAIR,IAAA,QAAAM,QAAA,CAAME,YAAY,CAAM,CAAC,EACvC,CAAC,cACNR,IAAA,QAAKO,SAAS,CAAC,WAAW,CAAAD,QAAA,CACvBA,QAAQ,CACN,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAH,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}