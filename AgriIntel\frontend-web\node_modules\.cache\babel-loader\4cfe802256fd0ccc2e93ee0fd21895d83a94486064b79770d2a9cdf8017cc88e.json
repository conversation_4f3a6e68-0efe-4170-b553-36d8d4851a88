{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.K8SMachineWorkflow = void 0;\nconst promises_1 = require(\"fs/promises\");\nconst machine_workflow_1 = require(\"./machine_workflow\");\n/** The fallback file name */\nconst FALLBACK_FILENAME = '/var/run/secrets/kubernetes.io/serviceaccount/token';\n/** The azure environment variable for the file name. */\nconst AZURE_FILENAME = 'AZURE_FEDERATED_TOKEN_FILE';\n/** The AWS environment variable for the file name. */\nconst AWS_FILENAME = 'AWS_WEB_IDENTITY_TOKEN_FILE';\nclass K8SMachineWorkflow extends machine_workflow_1.MachineWorkflow {\n  /**\n   * Instantiate the machine workflow.\n   */\n  constructor(cache) {\n    super(cache);\n  }\n  /**\n   * Get the token from the environment.\n   */\n  async getToken() {\n    let filename;\n    if (process.env[AZURE_FILENAME]) {\n      filename = process.env[AZURE_FILENAME];\n    } else if (process.env[AWS_FILENAME]) {\n      filename = process.env[AWS_FILENAME];\n    } else {\n      filename = FALLBACK_FILENAME;\n    }\n    const token = await (0, promises_1.readFile)(filename, 'utf8');\n    return {\n      access_token: token\n    };\n  }\n}\nexports.K8SMachineWorkflow = K8SMachineWorkflow;", "map": {"version": 3, "names": ["promises_1", "require", "machine_workflow_1", "FALLBACK_FILENAME", "AZURE_FILENAME", "AWS_FILENAME", "K8SMachineWorkflow", "MachineWorkflow", "constructor", "cache", "getToken", "filename", "process", "env", "token", "readFile", "access_token", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\auth\\mongodb_oidc\\k8s_machine_workflow.ts"], "sourcesContent": ["import { readFile } from 'fs/promises';\n\nimport { type AccessToken, MachineWorkflow } from './machine_workflow';\nimport { type TokenCache } from './token_cache';\n\n/** The fallback file name */\nconst FALLBACK_FILENAME = '/var/run/secrets/kubernetes.io/serviceaccount/token';\n\n/** The azure environment variable for the file name. */\nconst AZURE_FILENAME = 'AZURE_FEDERATED_TOKEN_FILE';\n\n/** The AWS environment variable for the file name. */\nconst AWS_FILENAME = 'AWS_WEB_IDENTITY_TOKEN_FILE';\n\nexport class K8SMachineWorkflow extends MachineWorkflow {\n  /**\n   * Instantiate the machine workflow.\n   */\n  constructor(cache: TokenCache) {\n    super(cache);\n  }\n\n  /**\n   * Get the token from the environment.\n   */\n  async getToken(): Promise<AccessToken> {\n    let filename: string;\n    if (process.env[AZURE_FILENAME]) {\n      filename = process.env[AZURE_FILENAME];\n    } else if (process.env[AWS_FILENAME]) {\n      filename = process.env[AWS_FILENAME];\n    } else {\n      filename = FALLBACK_FILENAME;\n    }\n    const token = await readFile(filename, 'utf8');\n    return { access_token: token };\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,UAAA,GAAAC,OAAA;AAEA,MAAAC,kBAAA,GAAAD,OAAA;AAGA;AACA,MAAME,iBAAiB,GAAG,qDAAqD;AAE/E;AACA,MAAMC,cAAc,GAAG,4BAA4B;AAEnD;AACA,MAAMC,YAAY,GAAG,6BAA6B;AAElD,MAAaC,kBAAmB,SAAQJ,kBAAA,CAAAK,eAAe;EACrD;;;EAGAC,YAAYC,KAAiB;IAC3B,KAAK,CAACA,KAAK,CAAC;EACd;EAEA;;;EAGA,MAAMC,QAAQA,CAAA;IACZ,IAAIC,QAAgB;IACpB,IAAIC,OAAO,CAACC,GAAG,CAACT,cAAc,CAAC,EAAE;MAC/BO,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACT,cAAc,CAAC;IACxC,CAAC,MAAM,IAAIQ,OAAO,CAACC,GAAG,CAACR,YAAY,CAAC,EAAE;MACpCM,QAAQ,GAAGC,OAAO,CAACC,GAAG,CAACR,YAAY,CAAC;IACtC,CAAC,MAAM;MACLM,QAAQ,GAAGR,iBAAiB;IAC9B;IACA,MAAMW,KAAK,GAAG,MAAM,IAAAd,UAAA,CAAAe,QAAQ,EAACJ,QAAQ,EAAE,MAAM,CAAC;IAC9C,OAAO;MAAEK,YAAY,EAAEF;IAAK,CAAE;EAChC;;AAtBFG,OAAA,CAAAX,kBAAA,GAAAA,kBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}