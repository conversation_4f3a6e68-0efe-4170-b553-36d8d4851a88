// MongoDB collection import removed - using API calls instead
import { EventEmitter } from 'events';

// Create a simple ObjectId class for use in the service
class ObjectId {
  id: string;

  constructor(id?: string) {
    this.id = id || 'mock-id-' + Date.now();
  }

  toString() {
    return this.id;
  }

  toHexString() {
    return this.id;
  }
}

// Data change event emitter
export const dataChangeEmitter = new EventEmitter();

// Event types
export enum DataChangeEventType {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete'
}

// Event interface
export interface DataChangeEvent {
  type: DataChangeEventType;
  collection: string;
  documentId: string;
  data?: any;
}

/**
 * Centralized data integration service
 * This service handles all data operations and ensures consistency across modules
 */
class DataIntegrationService {
  /**
   * Create a new document in a collection
   * @param collectionName Collection name
   * @param data Document data
   * @returns Created document
   */
  async createDocument(collectionName: string, data: any): Promise<any> {
    try {
      // Add timestamps
      const now = new Date();
      const documentToInsert = {
        ...data,
        createdAt: now,
        updatedAt: now
      };

      // Get collection
      const collection = getCollection(collectionName);

      // Insert document
      const result = await collection.insertOne(documentToInsert);

      // Get inserted document
      const insertedDocument = {
        ...documentToInsert,
        _id: result.insertedId
      };

      // Emit data change event
      dataChangeEmitter.emit('dataChange', {
        type: DataChangeEventType.CREATE,
        collection: collectionName,
        documentId: result.insertedId.toString(),
        data: insertedDocument
      });

      // Update related collections
      await this.updateRelatedCollections(collectionName, insertedDocument);

      return insertedDocument;
    } catch (error) {
      console.error(`Error creating document in ${collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Update a document in a collection
   * @param collectionName Collection name
   * @param id Document ID
   * @param data Document data
   * @returns Updated document
   */
  async updateDocument(collectionName: string, id: string, data: any): Promise<any> {
    try {
      // Add updated timestamp
      const now = new Date();
      const documentToUpdate = {
        ...data,
        updatedAt: now
      };

      // Get collection
      const collection = getCollection(collectionName);

      // Update document
      const result = await collection.updateOne(
        { _id: new ObjectId(id) },
        { $set: documentToUpdate }
      );

      if (result.modifiedCount === 0) {
        throw new Error(`Document with ID ${id} not found in ${collectionName}`);
      }

      // Get updated document
      const updatedDocument = await collection.findOne({ _id: new ObjectId(id) });

      // Emit data change event
      dataChangeEmitter.emit('dataChange', {
        type: DataChangeEventType.UPDATE,
        collection: collectionName,
        documentId: id,
        data: updatedDocument
      });

      // Update related collections
      await this.updateRelatedCollections(collectionName, updatedDocument);

      return updatedDocument;
    } catch (error) {
      console.error(`Error updating document in ${collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Delete a document from a collection
   * @param collectionName Collection name
   * @param id Document ID
   * @returns Deletion result
   */
  async deleteDocument(collectionName: string, id: string): Promise<boolean> {
    try {
      // Get document before deletion for related updates
      const collection = getCollection(collectionName);
      const documentToDelete = await collection.findOne({ _id: new ObjectId(id) });

      if (!documentToDelete) {
        throw new Error(`Document with ID ${id} not found in ${collectionName}`);
      }

      // Delete document
      const result = await collection.deleteOne({ _id: new ObjectId(id) });

      if (result.deletedCount === 0) {
        throw new Error(`Document with ID ${id} not found in ${collectionName}`);
      }

      // Emit data change event
      dataChangeEmitter.emit('dataChange', {
        type: DataChangeEventType.DELETE,
        collection: collectionName,
        documentId: id,
        data: documentToDelete
      });

      // Update related collections
      await this.handleDeletedDocumentRelations(collectionName, id, documentToDelete);

      return true;
    } catch (error) {
      console.error(`Error deleting document from ${collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Get a document from a collection
   * @param collectionName Collection name
   * @param id Document ID
   * @returns Document
   */
  async getDocument(collectionName: string, id: string): Promise<any> {
    try {
      const collection = getCollection(collectionName);
      return await collection.findOne({ _id: new ObjectId(id) });
    } catch (error) {
      console.error(`Error getting document from ${collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Get all documents from a collection
   * @param collectionName Collection name
   * @param filter Filter
   * @param limit Limit
   * @param skip Skip
   * @returns Documents
   */
  async getDocuments(
    collectionName: string,
    filter: any = {},
    limit: number = 0,
    skip: number = 0
  ): Promise<any[]> {
    try {
      const collection = getCollection(collectionName);
      return await collection.find(filter).skip(skip).limit(limit).toArray();
    } catch (error) {
      console.error(`Error getting documents from ${collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Update related collections when a document is created or updated
   * @param collectionName Collection name
   * @param document Document
   */
  private async updateRelatedCollections(collectionName: string, document: any): Promise<void> {
    try {
      switch (collectionName) {
        case 'animals':
          // Update animal count in dashboard stats
          await this.updateDashboardStats();
          break;
        case 'financial_records':
          // Update financial stats
          await this.updateFinancialStats();
          break;
        case 'health_records':
          // Update animal health status
          if (document.animalId) {
            await this.updateAnimalHealthStatus(document.animalId);
          }
          break;
        case 'feeding_records':
          // Update feed inventory
          if (document.feedTypeId) {
            await this.updateFeedInventory(document.feedTypeId, document.quantity);
          }
          break;
        // Add more cases as needed
      }
    } catch (error) {
      console.error('Error updating related collections:', error);
      // Don't throw error to prevent blocking the main operation
    }
  }

  /**
   * Handle deleted document relations
   * @param collectionName Collection name
   * @param id Document ID
   * @param document Deleted document
   */
  private async handleDeletedDocumentRelations(
    collectionName: string,
    id: string,
    document: any
  ): Promise<void> {
    try {
      switch (collectionName) {
        case 'animals':
          // Delete related records
          await this.deleteRelatedRecords('health_records', { animalId: id });
          await this.deleteRelatedRecords('breeding_records', {
            $or: [{ femaleId: id }, { maleId: id }]
          });
          await this.deleteRelatedRecords('feeding_records', {
            animals: { $elemMatch: { $eq: id } }
          });

          // Update dashboard stats
          await this.updateDashboardStats();
          break;
        case 'financial_records':
          // Update financial stats
          await this.updateFinancialStats();
          break;
        // Add more cases as needed
      }
    } catch (error) {
      console.error('Error handling deleted document relations:', error);
      // Don't throw error to prevent blocking the main operation
    }
  }

  /**
   * Delete related records
   * @param collectionName Collection name
   * @param filter Filter
   */
  private async deleteRelatedRecords(collectionName: string, filter: any): Promise<void> {
    try {
      const collection = getCollection(collectionName);
      await collection.deleteMany(filter);
    } catch (error) {
      console.error(`Error deleting related records from ${collectionName}:`, error);
      throw error;
    }
  }

  /**
   * Update dashboard stats
   */
  private async updateDashboardStats(): Promise<void> {
    try {
      // Get animals collection
      const animalsCollection = getCollection('animals');

      // Count total animals
      const totalAnimals = await animalsCollection.countDocuments();

      // Count active animals
      const activeAnimals = await animalsCollection.countDocuments({ status: 'Active' });

      // Count animals by health status
      const healthyCounts = await animalsCollection.countDocuments({ healthStatus: 'healthy' });
      const sickCounts = await animalsCollection.countDocuments({ healthStatus: 'sick' });
      const injuredCounts = await animalsCollection.countDocuments({ healthStatus: 'injured' });
      const pregnantCounts = await animalsCollection.countDocuments({ healthStatus: 'pregnant' });

      // Calculate health percentage
      const healthPercentage = totalAnimals > 0
        ? Math.round((healthyCounts / totalAnimals) * 100)
        : 0;

      // Get dashboard stats collection
      const statsCollection = getCollection('dashboard_stats');

      // Update dashboard stats
      await statsCollection.updateOne(
        { _id: 'animal_stats' },
        {
          $set: {
            totalAnimals,
            activeAnimals,
            healthPercentage,
            byHealth: {
              healthy: healthyCounts,
              sick: sickCounts,
              injured: injuredCounts,
              pregnant: pregnantCounts
            },
            updatedAt: new Date()
          }
        },
        { upsert: true }
      );
    } catch (error) {
      console.error('Error updating dashboard stats:', error);
      throw error;
    }
  }

  /**
   * Update financial stats
   */
  private async updateFinancialStats(): Promise<void> {
    try {
      // Get financial records collection
      const financialCollection = getCollection('financial_records');

      // Calculate total revenue
      const revenueRecords = await financialCollection.find({ type: 'income' }).toArray();
      const totalRevenue = revenueRecords.reduce((sum, record) => sum + record.amount, 0);

      // Calculate total expenses
      const expenseRecords = await financialCollection.find({ type: 'expense' }).toArray();
      const totalExpenses = expenseRecords.reduce((sum, record) => sum + record.amount, 0);

      // Calculate net profit
      const netProfit = totalRevenue - totalExpenses;

      // Get dashboard stats collection
      const statsCollection = getCollection('dashboard_stats');

      // Update financial stats
      await statsCollection.updateOne(
        { _id: 'financial_stats' },
        {
          $set: {
            totalRevenue,
            totalExpenses,
            netProfit,
            updatedAt: new Date()
          }
        },
        { upsert: true }
      );
    } catch (error) {
      console.error('Error updating financial stats:', error);
      throw error;
    }
  }

  /**
   * Update animal health status
   * @param animalId Animal ID
   */
  private async updateAnimalHealthStatus(animalId: string): Promise<void> {
    try {
      // Get health records collection
      const healthCollection = getCollection('health_records');

      // Get latest health record for the animal
      const latestRecord = await healthCollection.find({ animalId })
        .sort({ date: -1 })
        .limit(1)
        .toArray();

      if (latestRecord.length === 0) {
        return;
      }

      // Determine health status based on latest record
      let healthStatus = 'healthy';
      if (latestRecord[0].condition === 'Sick') {
        healthStatus = 'sick';
      } else if (latestRecord[0].condition === 'Injured') {
        healthStatus = 'injured';
      } else if (latestRecord[0].condition === 'Pregnant') {
        healthStatus = 'pregnant';
      }

      // Update animal health status
      const animalsCollection = getCollection('animals');
      await animalsCollection.updateOne(
        { _id: new ObjectId(animalId) },
        { $set: { healthStatus, updatedAt: new Date() } }
      );
    } catch (error) {
      console.error('Error updating animal health status:', error);
      throw error;
    }
  }

  /**
   * Update feed inventory
   * @param feedTypeId Feed type ID
   * @param quantity Quantity used
   */
  private async updateFeedInventory(feedTypeId: string, quantity: number): Promise<void> {
    try {
      // Get feed inventory collection
      const feedInventoryCollection = getCollection('feed_inventory');

      // Update feed inventory
      await feedInventoryCollection.updateOne(
        { _id: new ObjectId(feedTypeId) },
        { $inc: { quantity: -quantity, updatedAt: new Date() } }
      );
    } catch (error) {
      console.error('Error updating feed inventory:', error);
      throw error;
    }
  }
}

// Export singleton instance
export const dataIntegrationService = new DataIntegrationService();
