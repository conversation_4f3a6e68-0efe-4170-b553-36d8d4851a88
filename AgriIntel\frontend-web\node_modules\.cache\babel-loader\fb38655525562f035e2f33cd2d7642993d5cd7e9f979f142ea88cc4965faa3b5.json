{"ast": null, "code": "import React from'react';import{NavLink}from'react-router-dom';import{useSelector}from'react-redux';import{useTranslation}from'react-i18next';import{HomeIcon,ChartBarIcon,HeartIcon,BeakerIcon,CurrencyDollarIcon,DocumentChartBarIcon,Cog6ToothIcon,UserGroupIcon}from'@heroicons/react/24/outline';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const Sidebar=()=>{const{t}=useTranslation();const{sidebarOpen}=useSelector(state=>state.ui);const navigation=[{name:t('navigation.dashboard'),href:'/dashboard',icon:HomeIcon},{name:t('navigation.animals'),href:'/animals',icon:ChartBarIcon},{name:t('navigation.health'),href:'/health',icon:HeartIcon},{name:t('navigation.breeding'),href:'/breeding',icon:BeakerIcon},{name:t('navigation.feeding'),href:'/feeding',icon:UserGroupIcon},{name:t('navigation.financial'),href:'/financial',icon:CurrencyDollarIcon},{name:t('navigation.reports'),href:'/reports',icon:DocumentChartBarIcon},{name:t('navigation.settings'),href:'/settings',icon:Cog6ToothIcon}];return/*#__PURE__*/_jsxs(\"div\",{className:\"fixed inset-y-0 left-0 z-50 bg-white shadow-lg transition-all duration-300 \".concat(sidebarOpen?'w-64':'w-16',\" lg:translate-x-0\"),children:[/*#__PURE__*/_jsx(\"div\",{className:\"flex items-center h-16 px-4 border-b border-gray-200\",children:/*#__PURE__*/_jsxs(\"div\",{className:\"flex items-center space-x-2\",children:[/*#__PURE__*/_jsx(\"div\",{className:\"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\",children:/*#__PURE__*/_jsx(\"svg\",{className:\"w-5 h-5 text-white\",fill:\"none\",stroke:\"currentColor\",viewBox:\"0 0 24 24\",children:/*#__PURE__*/_jsx(\"path\",{strokeLinecap:\"round\",strokeLinejoin:\"round\",strokeWidth:2,d:\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"})})}),sidebarOpen&&/*#__PURE__*/_jsxs(\"div\",{children:[/*#__PURE__*/_jsx(\"h1\",{className:\"text-lg font-bold text-primary-900\",children:\"AMPD\"}),/*#__PURE__*/_jsx(\"p\",{className:\"text-xs text-primary-600\",children:\"Livestock\"})]})]})}),/*#__PURE__*/_jsx(\"nav\",{className:\"mt-8 px-4\",children:/*#__PURE__*/_jsx(\"ul\",{className:\"space-y-2\",children:navigation.map(item=>/*#__PURE__*/_jsx(\"li\",{children:/*#__PURE__*/_jsxs(NavLink,{to:item.href,className:_ref=>{let{isActive}=_ref;return\"flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 \".concat(isActive?'bg-primary-100 text-primary-700 border-r-2 border-primary-600':'text-gray-600 hover:text-gray-900 hover:bg-gray-100');},children:[/*#__PURE__*/_jsx(item.icon,{className:\"h-5 w-5 flex-shrink-0\"}),sidebarOpen&&/*#__PURE__*/_jsx(\"span\",{className:\"ml-3\",children:item.name})]})},item.name))})})]});};export default Sidebar;", "map": {"version": 3, "names": ["React", "NavLink", "useSelector", "useTranslation", "HomeIcon", "ChartBarIcon", "HeartIcon", "BeakerIcon", "CurrencyDollarIcon", "DocumentChartBarIcon", "Cog6ToothIcon", "UserGroupIcon", "jsx", "_jsx", "jsxs", "_jsxs", "Sidebar", "t", "sidebarOpen", "state", "ui", "navigation", "name", "href", "icon", "className", "concat", "children", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "map", "item", "to", "_ref", "isActive"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/components/layout/Sidebar.tsx"], "sourcesContent": ["import React from 'react';\nimport { NavLink } from 'react-router-dom';\nimport { useSelector } from 'react-redux';\nimport { useTranslation } from 'react-i18next';\nimport {\n  HomeIcon,\n  ChartBarIcon,\n  HeartIcon,\n  BeakerIcon,\n  CurrencyDollarIcon,\n  DocumentChartBarIcon,\n  Cog6ToothIcon,\n  UserGroupIcon,\n} from '@heroicons/react/24/outline';\n\nimport { RootState } from '../../store/store';\n\nconst Sidebar: React.FC = () => {\n  const { t } = useTranslation();\n  const { sidebarOpen } = useSelector((state: RootState) => state.ui);\n\n  const navigation = [\n    { name: t('navigation.dashboard'), href: '/dashboard', icon: HomeIcon },\n    { name: t('navigation.animals'), href: '/animals', icon: ChartBarIcon },\n    { name: t('navigation.health'), href: '/health', icon: HeartIcon },\n    { name: t('navigation.breeding'), href: '/breeding', icon: BeakerIcon },\n    { name: t('navigation.feeding'), href: '/feeding', icon: UserGroupIcon },\n    { name: t('navigation.financial'), href: '/financial', icon: CurrencyDollarIcon },\n    { name: t('navigation.reports'), href: '/reports', icon: DocumentChartBarIcon },\n    { name: t('navigation.settings'), href: '/settings', icon: Cog6ToothIcon },\n  ];\n\n  return (\n    <div\n      className={`fixed inset-y-0 left-0 z-50 bg-white shadow-lg transition-all duration-300 ${\n        sidebarOpen ? 'w-64' : 'w-16'\n      } lg:translate-x-0`}\n    >\n      {/* Logo */}\n      <div className=\"flex items-center h-16 px-4 border-b border-gray-200\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center\">\n            <svg\n              className=\"w-5 h-5 text-white\"\n              fill=\"none\"\n              stroke=\"currentColor\"\n              viewBox=\"0 0 24 24\"\n            >\n              <path\n                strokeLinecap=\"round\"\n                strokeLinejoin=\"round\"\n                strokeWidth={2}\n                d=\"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4\"\n              />\n            </svg>\n          </div>\n          {sidebarOpen && (\n            <div>\n              <h1 className=\"text-lg font-bold text-primary-900\">AMPD</h1>\n              <p className=\"text-xs text-primary-600\">Livestock</p>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <nav className=\"mt-8 px-4\">\n        <ul className=\"space-y-2\">\n          {navigation.map((item) => (\n            <li key={item.name}>\n              <NavLink\n                to={item.href}\n                className={({ isActive }) =>\n                  `flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors duration-200 ${\n                    isActive\n                      ? 'bg-primary-100 text-primary-700 border-r-2 border-primary-600'\n                      : 'text-gray-600 hover:text-gray-900 hover:bg-gray-100'\n                  }`\n                }\n              >\n                <item.icon className=\"h-5 w-5 flex-shrink-0\" />\n                {sidebarOpen && <span className=\"ml-3\">{item.name}</span>}\n              </NavLink>\n            </li>\n          ))}\n        </ul>\n      </nav>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,OAASC,OAAO,KAAQ,kBAAkB,CAC1C,OAASC,WAAW,KAAQ,aAAa,CACzC,OAASC,cAAc,KAAQ,eAAe,CAC9C,OACEC,QAAQ,CACRC,YAAY,CACZC,SAAS,CACTC,UAAU,CACVC,kBAAkB,CAClBC,oBAAoB,CACpBC,aAAa,CACbC,aAAa,KACR,6BAA6B,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBAIrC,KAAM,CAAAC,OAAiB,CAAGA,CAAA,GAAM,CAC9B,KAAM,CAAEC,CAAE,CAAC,CAAGd,cAAc,CAAC,CAAC,CAC9B,KAAM,CAAEe,WAAY,CAAC,CAAGhB,WAAW,CAAEiB,KAAgB,EAAKA,KAAK,CAACC,EAAE,CAAC,CAEnE,KAAM,CAAAC,UAAU,CAAG,CACjB,CAAEC,IAAI,CAAEL,CAAC,CAAC,sBAAsB,CAAC,CAAEM,IAAI,CAAE,YAAY,CAAEC,IAAI,CAAEpB,QAAS,CAAC,CACvE,CAAEkB,IAAI,CAAEL,CAAC,CAAC,oBAAoB,CAAC,CAAEM,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAEnB,YAAa,CAAC,CACvE,CAAEiB,IAAI,CAAEL,CAAC,CAAC,mBAAmB,CAAC,CAAEM,IAAI,CAAE,SAAS,CAAEC,IAAI,CAAElB,SAAU,CAAC,CAClE,CAAEgB,IAAI,CAAEL,CAAC,CAAC,qBAAqB,CAAC,CAAEM,IAAI,CAAE,WAAW,CAAEC,IAAI,CAAEjB,UAAW,CAAC,CACvE,CAAEe,IAAI,CAAEL,CAAC,CAAC,oBAAoB,CAAC,CAAEM,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAEb,aAAc,CAAC,CACxE,CAAEW,IAAI,CAAEL,CAAC,CAAC,sBAAsB,CAAC,CAAEM,IAAI,CAAE,YAAY,CAAEC,IAAI,CAAEhB,kBAAmB,CAAC,CACjF,CAAEc,IAAI,CAAEL,CAAC,CAAC,oBAAoB,CAAC,CAAEM,IAAI,CAAE,UAAU,CAAEC,IAAI,CAAEf,oBAAqB,CAAC,CAC/E,CAAEa,IAAI,CAAEL,CAAC,CAAC,qBAAqB,CAAC,CAAEM,IAAI,CAAE,WAAW,CAAEC,IAAI,CAAEd,aAAc,CAAC,CAC3E,CAED,mBACEK,KAAA,QACEU,SAAS,+EAAAC,MAAA,CACPR,WAAW,CAAG,MAAM,CAAG,MAAM,qBACX,CAAAS,QAAA,eAGpBd,IAAA,QAAKY,SAAS,CAAC,sDAAsD,CAAAE,QAAA,cACnEZ,KAAA,QAAKU,SAAS,CAAC,6BAA6B,CAAAE,QAAA,eAC1Cd,IAAA,QAAKY,SAAS,CAAC,oEAAoE,CAAAE,QAAA,cACjFd,IAAA,QACEY,SAAS,CAAC,oBAAoB,CAC9BG,IAAI,CAAC,MAAM,CACXC,MAAM,CAAC,cAAc,CACrBC,OAAO,CAAC,WAAW,CAAAH,QAAA,cAEnBd,IAAA,SACEkB,aAAa,CAAC,OAAO,CACrBC,cAAc,CAAC,OAAO,CACtBC,WAAW,CAAE,CAAE,CACfC,CAAC,CAAC,2IAA2I,CAC9I,CAAC,CACC,CAAC,CACH,CAAC,CACLhB,WAAW,eACVH,KAAA,QAAAY,QAAA,eACEd,IAAA,OAAIY,SAAS,CAAC,oCAAoC,CAAAE,QAAA,CAAC,MAAI,CAAI,CAAC,cAC5Dd,IAAA,MAAGY,SAAS,CAAC,0BAA0B,CAAAE,QAAA,CAAC,WAAS,CAAG,CAAC,EAClD,CACN,EACE,CAAC,CACH,CAAC,cAGNd,IAAA,QAAKY,SAAS,CAAC,WAAW,CAAAE,QAAA,cACxBd,IAAA,OAAIY,SAAS,CAAC,WAAW,CAAAE,QAAA,CACtBN,UAAU,CAACc,GAAG,CAAEC,IAAI,eACnBvB,IAAA,OAAAc,QAAA,cACEZ,KAAA,CAACd,OAAO,EACNoC,EAAE,CAAED,IAAI,CAACb,IAAK,CACdE,SAAS,CAAEa,IAAA,MAAC,CAAEC,QAAS,CAAC,CAAAD,IAAA,oGAAAZ,MAAA,CAEpBa,QAAQ,CACJ,+DAA+D,CAC/D,qDAAqD,GAE5D,CAAAZ,QAAA,eAEDd,IAAA,CAACuB,IAAI,CAACZ,IAAI,EAACC,SAAS,CAAC,uBAAuB,CAAE,CAAC,CAC9CP,WAAW,eAAIL,IAAA,SAAMY,SAAS,CAAC,MAAM,CAAAE,QAAA,CAAES,IAAI,CAACd,IAAI,CAAO,CAAC,EAClD,CAAC,EAbHc,IAAI,CAACd,IAcV,CACL,CAAC,CACA,CAAC,CACF,CAAC,EACH,CAAC,CAEV,CAAC,CAED,cAAe,CAAAN,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}