{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.TopologyDescription = void 0;\nconst bson_1 = require(\"../bson\");\nconst WIRE_CONSTANTS = require(\"../cmap/wire_protocol/constants\");\nconst error_1 = require(\"../error\");\nconst utils_1 = require(\"../utils\");\nconst common_1 = require(\"./common\");\nconst server_description_1 = require(\"./server_description\");\n// constants related to compatibility checks\nconst MIN_SUPPORTED_SERVER_VERSION = WIRE_CONSTANTS.MIN_SUPPORTED_SERVER_VERSION;\nconst MAX_SUPPORTED_SERVER_VERSION = WIRE_CONSTANTS.MAX_SUPPORTED_SERVER_VERSION;\nconst MIN_SUPPORTED_WIRE_VERSION = WIRE_CONSTANTS.MIN_SUPPORTED_WIRE_VERSION;\nconst MAX_SUPPORTED_WIRE_VERSION = WIRE_CONSTANTS.MAX_SUPPORTED_WIRE_VERSION;\nconst MONGOS_OR_UNKNOWN = new Set([common_1.ServerType.Mongos, common_1.ServerType.Unknown]);\nconst MONGOS_OR_STANDALONE = new Set([common_1.ServerType.Mongos, common_1.ServerType.Standalone]);\nconst NON_PRIMARY_RS_MEMBERS = new Set([common_1.ServerType.RSSecondary, common_1.ServerType.RSArbiter, common_1.ServerType.RSOther]);\n/**\n * Representation of a deployment of servers\n * @public\n */\nclass TopologyDescription {\n  /**\n   * Create a TopologyDescription\n   */\n  constructor(topologyType, serverDescriptions = null, setName = null, maxSetVersion = null, maxElectionId = null, commonWireVersion = null, options = null) {\n    options = options ?? {};\n    this.type = topologyType ?? common_1.TopologyType.Unknown;\n    this.servers = serverDescriptions ?? new Map();\n    this.stale = false;\n    this.compatible = true;\n    this.heartbeatFrequencyMS = options.heartbeatFrequencyMS ?? 0;\n    this.localThresholdMS = options.localThresholdMS ?? 15;\n    this.setName = setName ?? null;\n    this.maxElectionId = maxElectionId ?? null;\n    this.maxSetVersion = maxSetVersion ?? null;\n    this.commonWireVersion = commonWireVersion ?? 0;\n    // determine server compatibility\n    for (const serverDescription of this.servers.values()) {\n      // Load balancer mode is always compatible.\n      if (serverDescription.type === common_1.ServerType.Unknown || serverDescription.type === common_1.ServerType.LoadBalancer) {\n        continue;\n      }\n      if (serverDescription.minWireVersion > MAX_SUPPORTED_WIRE_VERSION) {\n        this.compatible = false;\n        this.compatibilityError = `Server at ${serverDescription.address} requires wire version ${serverDescription.minWireVersion}, but this version of the driver only supports up to ${MAX_SUPPORTED_WIRE_VERSION} (MongoDB ${MAX_SUPPORTED_SERVER_VERSION})`;\n      }\n      if (serverDescription.maxWireVersion < MIN_SUPPORTED_WIRE_VERSION) {\n        this.compatible = false;\n        this.compatibilityError = `Server at ${serverDescription.address} reports wire version ${serverDescription.maxWireVersion}, but this version of the driver requires at least ${MIN_SUPPORTED_WIRE_VERSION} (MongoDB ${MIN_SUPPORTED_SERVER_VERSION}).`;\n        break;\n      }\n    }\n    // Whenever a client updates the TopologyDescription from a hello response, it MUST set\n    // TopologyDescription.logicalSessionTimeoutMinutes to the smallest logicalSessionTimeoutMinutes\n    // value among ServerDescriptions of all data-bearing server types. If any have a null\n    // logicalSessionTimeoutMinutes, then TopologyDescription.logicalSessionTimeoutMinutes MUST be\n    // set to null.\n    this.logicalSessionTimeoutMinutes = null;\n    for (const [, server] of this.servers) {\n      if (server.isReadable) {\n        if (server.logicalSessionTimeoutMinutes == null) {\n          // If any of the servers have a null logicalSessionsTimeout, then the whole topology does\n          this.logicalSessionTimeoutMinutes = null;\n          break;\n        }\n        if (this.logicalSessionTimeoutMinutes == null) {\n          // First server with a non null logicalSessionsTimeout\n          this.logicalSessionTimeoutMinutes = server.logicalSessionTimeoutMinutes;\n          continue;\n        }\n        // Always select the smaller of the:\n        // current server logicalSessionsTimeout and the topologies logicalSessionsTimeout\n        this.logicalSessionTimeoutMinutes = Math.min(this.logicalSessionTimeoutMinutes, server.logicalSessionTimeoutMinutes);\n      }\n    }\n  }\n  /**\n   * Returns a new TopologyDescription based on the SrvPollingEvent\n   * @internal\n   */\n  updateFromSrvPollingEvent(ev, srvMaxHosts = 0) {\n    /** The SRV addresses defines the set of addresses we should be using */\n    const incomingHostnames = ev.hostnames();\n    const currentHostnames = new Set(this.servers.keys());\n    const hostnamesToAdd = new Set(incomingHostnames);\n    const hostnamesToRemove = new Set();\n    for (const hostname of currentHostnames) {\n      // filter hostnamesToAdd (made from incomingHostnames) down to what is *not* present in currentHostnames\n      hostnamesToAdd.delete(hostname);\n      if (!incomingHostnames.has(hostname)) {\n        // If the SRV Records no longer include this hostname\n        // we have to stop using it\n        hostnamesToRemove.add(hostname);\n      }\n    }\n    if (hostnamesToAdd.size === 0 && hostnamesToRemove.size === 0) {\n      // No new hosts to add and none to remove\n      return this;\n    }\n    const serverDescriptions = new Map(this.servers);\n    for (const removedHost of hostnamesToRemove) {\n      serverDescriptions.delete(removedHost);\n    }\n    if (hostnamesToAdd.size > 0) {\n      if (srvMaxHosts === 0) {\n        // Add all!\n        for (const hostToAdd of hostnamesToAdd) {\n          serverDescriptions.set(hostToAdd, new server_description_1.ServerDescription(hostToAdd));\n        }\n      } else if (serverDescriptions.size < srvMaxHosts) {\n        // Add only the amount needed to get us back to srvMaxHosts\n        const selectedHosts = (0, utils_1.shuffle)(hostnamesToAdd, srvMaxHosts - serverDescriptions.size);\n        for (const selectedHostToAdd of selectedHosts) {\n          serverDescriptions.set(selectedHostToAdd, new server_description_1.ServerDescription(selectedHostToAdd));\n        }\n      }\n    }\n    return new TopologyDescription(this.type, serverDescriptions, this.setName, this.maxSetVersion, this.maxElectionId, this.commonWireVersion, {\n      heartbeatFrequencyMS: this.heartbeatFrequencyMS,\n      localThresholdMS: this.localThresholdMS\n    });\n  }\n  /**\n   * Returns a copy of this description updated with a given ServerDescription\n   * @internal\n   */\n  update(serverDescription) {\n    const address = serverDescription.address;\n    // potentially mutated values\n    let {\n      type: topologyType,\n      setName,\n      maxSetVersion,\n      maxElectionId,\n      commonWireVersion\n    } = this;\n    const serverType = serverDescription.type;\n    const serverDescriptions = new Map(this.servers);\n    // update common wire version\n    if (serverDescription.maxWireVersion !== 0) {\n      if (commonWireVersion == null) {\n        commonWireVersion = serverDescription.maxWireVersion;\n      } else {\n        commonWireVersion = Math.min(commonWireVersion, serverDescription.maxWireVersion);\n      }\n    }\n    if (typeof serverDescription.setName === 'string' && typeof setName === 'string' && serverDescription.setName !== setName) {\n      if (topologyType === common_1.TopologyType.Single) {\n        // \"Single\" Topology with setName mismatch is direct connection usage, mark unknown do not remove\n        serverDescription = new server_description_1.ServerDescription(address);\n      } else {\n        serverDescriptions.delete(address);\n      }\n    }\n    // update the actual server description\n    serverDescriptions.set(address, serverDescription);\n    if (topologyType === common_1.TopologyType.Single) {\n      // once we are defined as single, that never changes\n      return new TopologyDescription(common_1.TopologyType.Single, serverDescriptions, setName, maxSetVersion, maxElectionId, commonWireVersion, {\n        heartbeatFrequencyMS: this.heartbeatFrequencyMS,\n        localThresholdMS: this.localThresholdMS\n      });\n    }\n    if (topologyType === common_1.TopologyType.Unknown) {\n      if (serverType === common_1.ServerType.Standalone && this.servers.size !== 1) {\n        serverDescriptions.delete(address);\n      } else {\n        topologyType = topologyTypeForServerType(serverType);\n      }\n    }\n    if (topologyType === common_1.TopologyType.Sharded) {\n      if (!MONGOS_OR_UNKNOWN.has(serverType)) {\n        serverDescriptions.delete(address);\n      }\n    }\n    if (topologyType === common_1.TopologyType.ReplicaSetNoPrimary) {\n      if (MONGOS_OR_STANDALONE.has(serverType)) {\n        serverDescriptions.delete(address);\n      }\n      if (serverType === common_1.ServerType.RSPrimary) {\n        const result = updateRsFromPrimary(serverDescriptions, serverDescription, setName, maxSetVersion, maxElectionId);\n        topologyType = result[0];\n        setName = result[1];\n        maxSetVersion = result[2];\n        maxElectionId = result[3];\n      } else if (NON_PRIMARY_RS_MEMBERS.has(serverType)) {\n        const result = updateRsNoPrimaryFromMember(serverDescriptions, serverDescription, setName);\n        topologyType = result[0];\n        setName = result[1];\n      }\n    }\n    if (topologyType === common_1.TopologyType.ReplicaSetWithPrimary) {\n      if (MONGOS_OR_STANDALONE.has(serverType)) {\n        serverDescriptions.delete(address);\n        topologyType = checkHasPrimary(serverDescriptions);\n      } else if (serverType === common_1.ServerType.RSPrimary) {\n        const result = updateRsFromPrimary(serverDescriptions, serverDescription, setName, maxSetVersion, maxElectionId);\n        topologyType = result[0];\n        setName = result[1];\n        maxSetVersion = result[2];\n        maxElectionId = result[3];\n      } else if (NON_PRIMARY_RS_MEMBERS.has(serverType)) {\n        topologyType = updateRsWithPrimaryFromMember(serverDescriptions, serverDescription, setName);\n      } else {\n        topologyType = checkHasPrimary(serverDescriptions);\n      }\n    }\n    return new TopologyDescription(topologyType, serverDescriptions, setName, maxSetVersion, maxElectionId, commonWireVersion, {\n      heartbeatFrequencyMS: this.heartbeatFrequencyMS,\n      localThresholdMS: this.localThresholdMS\n    });\n  }\n  get error() {\n    const descriptionsWithError = Array.from(this.servers.values()).filter(sd => sd.error);\n    if (descriptionsWithError.length > 0) {\n      return descriptionsWithError[0].error;\n    }\n    return null;\n  }\n  /**\n   * Determines if the topology description has any known servers\n   */\n  get hasKnownServers() {\n    return Array.from(this.servers.values()).some(sd => sd.type !== common_1.ServerType.Unknown);\n  }\n  /**\n   * Determines if this topology description has a data-bearing server available.\n   */\n  get hasDataBearingServers() {\n    return Array.from(this.servers.values()).some(sd => sd.isDataBearing);\n  }\n  /**\n   * Determines if the topology has a definition for the provided address\n   * @internal\n   */\n  hasServer(address) {\n    return this.servers.has(address);\n  }\n  /**\n   * Returns a JSON-serializable representation of the TopologyDescription.  This is primarily\n   * intended for use with JSON.stringify().\n   *\n   * This method will not throw.\n   */\n  toJSON() {\n    return bson_1.EJSON.serialize(this);\n  }\n}\nexports.TopologyDescription = TopologyDescription;\nfunction topologyTypeForServerType(serverType) {\n  switch (serverType) {\n    case common_1.ServerType.Standalone:\n      return common_1.TopologyType.Single;\n    case common_1.ServerType.Mongos:\n      return common_1.TopologyType.Sharded;\n    case common_1.ServerType.RSPrimary:\n      return common_1.TopologyType.ReplicaSetWithPrimary;\n    case common_1.ServerType.RSOther:\n    case common_1.ServerType.RSSecondary:\n      return common_1.TopologyType.ReplicaSetNoPrimary;\n    default:\n      return common_1.TopologyType.Unknown;\n  }\n}\nfunction updateRsFromPrimary(serverDescriptions, serverDescription, setName = null, maxSetVersion = null, maxElectionId = null) {\n  const setVersionElectionIdMismatch = (serverDescription, maxSetVersion, maxElectionId) => {\n    return `primary marked stale due to electionId/setVersion mismatch:` + ` server setVersion: ${serverDescription.setVersion},` + ` server electionId: ${serverDescription.electionId},` + ` topology setVersion: ${maxSetVersion},` + ` topology electionId: ${maxElectionId}`;\n  };\n  setName = setName || serverDescription.setName;\n  if (setName !== serverDescription.setName) {\n    serverDescriptions.delete(serverDescription.address);\n    return [checkHasPrimary(serverDescriptions), setName, maxSetVersion, maxElectionId];\n  }\n  if (serverDescription.maxWireVersion >= 17) {\n    const electionIdComparison = (0, utils_1.compareObjectId)(maxElectionId, serverDescription.electionId);\n    const maxElectionIdIsEqual = electionIdComparison === 0;\n    const maxElectionIdIsLess = electionIdComparison === -1;\n    const maxSetVersionIsLessOrEqual = (maxSetVersion ?? -1) <= (serverDescription.setVersion ?? -1);\n    if (maxElectionIdIsLess || maxElectionIdIsEqual && maxSetVersionIsLessOrEqual) {\n      // The reported electionId was greater\n      // or the electionId was equal and reported setVersion was greater\n      // Always update both values, they are a tuple\n      maxElectionId = serverDescription.electionId;\n      maxSetVersion = serverDescription.setVersion;\n    } else {\n      // Stale primary\n      // replace serverDescription with a default ServerDescription of type \"Unknown\"\n      serverDescriptions.set(serverDescription.address, new server_description_1.ServerDescription(serverDescription.address, undefined, {\n        error: new error_1.MongoStalePrimaryError(setVersionElectionIdMismatch(serverDescription, maxSetVersion, maxElectionId))\n      }));\n      return [checkHasPrimary(serverDescriptions), setName, maxSetVersion, maxElectionId];\n    }\n  } else {\n    const electionId = serverDescription.electionId ? serverDescription.electionId : null;\n    if (serverDescription.setVersion && electionId) {\n      if (maxSetVersion && maxElectionId) {\n        if (maxSetVersion > serverDescription.setVersion || (0, utils_1.compareObjectId)(maxElectionId, electionId) > 0) {\n          // this primary is stale, we must remove it\n          serverDescriptions.set(serverDescription.address, new server_description_1.ServerDescription(serverDescription.address, undefined, {\n            error: new error_1.MongoStalePrimaryError(setVersionElectionIdMismatch(serverDescription, maxSetVersion, maxElectionId))\n          }));\n          return [checkHasPrimary(serverDescriptions), setName, maxSetVersion, maxElectionId];\n        }\n      }\n      maxElectionId = serverDescription.electionId;\n    }\n    if (serverDescription.setVersion != null && (maxSetVersion == null || serverDescription.setVersion > maxSetVersion)) {\n      maxSetVersion = serverDescription.setVersion;\n    }\n  }\n  // We've heard from the primary. Is it the same primary as before?\n  for (const [address, server] of serverDescriptions) {\n    if (server.type === common_1.ServerType.RSPrimary && server.address !== serverDescription.address) {\n      // Reset old primary's type to Unknown.\n      serverDescriptions.set(address, new server_description_1.ServerDescription(server.address, undefined, {\n        error: new error_1.MongoStalePrimaryError('primary marked stale due to discovery of newer primary')\n      }));\n      // There can only be one primary\n      break;\n    }\n  }\n  // Discover new hosts from this primary's response.\n  serverDescription.allHosts.forEach(address => {\n    if (!serverDescriptions.has(address)) {\n      serverDescriptions.set(address, new server_description_1.ServerDescription(address));\n    }\n  });\n  // Remove hosts not in the response.\n  const currentAddresses = Array.from(serverDescriptions.keys());\n  const responseAddresses = serverDescription.allHosts;\n  currentAddresses.filter(addr => responseAddresses.indexOf(addr) === -1).forEach(address => {\n    serverDescriptions.delete(address);\n  });\n  return [checkHasPrimary(serverDescriptions), setName, maxSetVersion, maxElectionId];\n}\nfunction updateRsWithPrimaryFromMember(serverDescriptions, serverDescription, setName = null) {\n  if (setName == null) {\n    // TODO(NODE-3483): should be an appropriate runtime error\n    throw new error_1.MongoRuntimeError('Argument \"setName\" is required if connected to a replica set');\n  }\n  if (setName !== serverDescription.setName || serverDescription.me && serverDescription.address !== serverDescription.me) {\n    serverDescriptions.delete(serverDescription.address);\n  }\n  return checkHasPrimary(serverDescriptions);\n}\nfunction updateRsNoPrimaryFromMember(serverDescriptions, serverDescription, setName = null) {\n  const topologyType = common_1.TopologyType.ReplicaSetNoPrimary;\n  setName = setName ?? serverDescription.setName;\n  if (setName !== serverDescription.setName) {\n    serverDescriptions.delete(serverDescription.address);\n    return [topologyType, setName];\n  }\n  serverDescription.allHosts.forEach(address => {\n    if (!serverDescriptions.has(address)) {\n      serverDescriptions.set(address, new server_description_1.ServerDescription(address));\n    }\n  });\n  if (serverDescription.me && serverDescription.address !== serverDescription.me) {\n    serverDescriptions.delete(serverDescription.address);\n  }\n  return [topologyType, setName];\n}\nfunction checkHasPrimary(serverDescriptions) {\n  for (const serverDescription of serverDescriptions.values()) {\n    if (serverDescription.type === common_1.ServerType.RSPrimary) {\n      return common_1.TopologyType.ReplicaSetWithPrimary;\n    }\n  }\n  return common_1.TopologyType.ReplicaSetNoPrimary;\n}", "map": {"version": 3, "names": ["bson_1", "require", "WIRE_CONSTANTS", "error_1", "utils_1", "common_1", "server_description_1", "MIN_SUPPORTED_SERVER_VERSION", "MAX_SUPPORTED_SERVER_VERSION", "MIN_SUPPORTED_WIRE_VERSION", "MAX_SUPPORTED_WIRE_VERSION", "MONGOS_OR_UNKNOWN", "Set", "ServerType", "Mongos", "Unknown", "MONGOS_OR_STANDALONE", "Standalone", "NON_PRIMARY_RS_MEMBERS", "RSSecondary", "RSArbiter", "RSOther", "TopologyDescription", "constructor", "topologyType", "serverDescriptions", "setName", "maxSetVersion", "maxElectionId", "commonWireVersion", "options", "type", "TopologyType", "servers", "Map", "stale", "compatible", "heartbeatFrequencyMS", "localThresholdMS", "serverDescription", "values", "LoadBalancer", "minWireVersion", "compatibilityError", "address", "maxWireVersion", "logicalSessionTimeoutMinutes", "server", "isReadable", "Math", "min", "updateFromSrvPollingEvent", "ev", "srvMaxHosts", "incomingHostnames", "hostnames", "currentHostnames", "keys", "hostnamesToAdd", "hostnamesToRemove", "hostname", "delete", "has", "add", "size", "removedHost", "hostToAdd", "set", "ServerDescription", "selectedHosts", "shuffle", "selectedHostToAdd", "update", "serverType", "Single", "topologyTypeForServerType", "Sharded", "ReplicaSetNoPrimary", "RSPrimary", "result", "updateRsFromPrimary", "updateRsNoPrimaryFromMember", "ReplicaSetWithPrimary", "checkHasPrimary", "updateRsWithPrimaryFromMember", "error", "descriptionsWithError", "Array", "from", "filter", "sd", "length", "hasKnownServers", "some", "hasDataBearingServers", "isDataBearing", "hasServer", "toJSON", "EJSON", "serialize", "exports", "setVersionElectionIdMismatch", "setVersion", "electionId", "electionIdComparison", "compareObjectId", "maxElectionIdIsEqual", "maxElectionIdIsLess", "maxSetVersionIsLessOrEqual", "undefined", "MongoStalePrimaryError", "allHosts", "for<PERSON>ach", "currentAddresses", "responseAddresses", "addr", "indexOf", "MongoRuntimeError", "me"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\sdam\\topology_description.ts"], "sourcesContent": ["import { EJSON, type ObjectId } from '../bson';\nimport * as WIRE_CONSTANTS from '../cmap/wire_protocol/constants';\nimport { type MongoError, MongoRuntimeError, MongoStalePrimaryError } from '../error';\nimport { compareObjectId, shuffle } from '../utils';\nimport { ServerType, TopologyType } from './common';\nimport { ServerDescription } from './server_description';\nimport type { SrvPollingEvent } from './srv_polling';\n\n// constants related to compatibility checks\nconst MIN_SUPPORTED_SERVER_VERSION = WIRE_CONSTANTS.MIN_SUPPORTED_SERVER_VERSION;\nconst MAX_SUPPORTED_SERVER_VERSION = WIRE_CONSTANTS.MAX_SUPPORTED_SERVER_VERSION;\nconst MIN_SUPPORTED_WIRE_VERSION = WIRE_CONSTANTS.MIN_SUPPORTED_WIRE_VERSION;\nconst MAX_SUPPORTED_WIRE_VERSION = WIRE_CONSTANTS.MAX_SUPPORTED_WIRE_VERSION;\n\nconst MONGOS_OR_UNKNOWN = new Set<ServerType>([ServerType.Mongos, ServerType.Unknown]);\nconst MONGOS_OR_STANDALONE = new Set<ServerType>([ServerType.Mongos, ServerType.Standalone]);\nconst NON_PRIMARY_RS_MEMBERS = new Set<ServerType>([\n  ServerType.RSSecondary,\n  ServerType.RSArbiter,\n  ServerType.RSOther\n]);\n\n/** @public */\nexport interface TopologyDescriptionOptions {\n  heartbeatFrequencyMS?: number;\n  localThresholdMS?: number;\n}\n\n/**\n * Representation of a deployment of servers\n * @public\n */\nexport class TopologyDescription {\n  type: TopologyType;\n  setName: string | null;\n  maxSetVersion: number | null;\n  maxElectionId: ObjectId | null;\n  servers: Map<string, ServerDescription>;\n  stale: boolean;\n  compatible: boolean;\n  compatibilityError?: string;\n  logicalSessionTimeoutMinutes: number | null;\n  heartbeatFrequencyMS: number;\n  localThresholdMS: number;\n  commonWireVersion: number;\n  /**\n   * Create a TopologyDescription\n   */\n  constructor(\n    topologyType: TopologyType,\n    serverDescriptions: Map<string, ServerDescription> | null = null,\n    setName: string | null = null,\n    maxSetVersion: number | null = null,\n    maxElectionId: ObjectId | null = null,\n    commonWireVersion: number | null = null,\n    options: TopologyDescriptionOptions | null = null\n  ) {\n    options = options ?? {};\n\n    this.type = topologyType ?? TopologyType.Unknown;\n    this.servers = serverDescriptions ?? new Map();\n    this.stale = false;\n    this.compatible = true;\n    this.heartbeatFrequencyMS = options.heartbeatFrequencyMS ?? 0;\n    this.localThresholdMS = options.localThresholdMS ?? 15;\n    this.setName = setName ?? null;\n    this.maxElectionId = maxElectionId ?? null;\n    this.maxSetVersion = maxSetVersion ?? null;\n    this.commonWireVersion = commonWireVersion ?? 0;\n\n    // determine server compatibility\n    for (const serverDescription of this.servers.values()) {\n      // Load balancer mode is always compatible.\n      if (\n        serverDescription.type === ServerType.Unknown ||\n        serverDescription.type === ServerType.LoadBalancer\n      ) {\n        continue;\n      }\n\n      if (serverDescription.minWireVersion > MAX_SUPPORTED_WIRE_VERSION) {\n        this.compatible = false;\n        this.compatibilityError = `Server at ${serverDescription.address} requires wire version ${serverDescription.minWireVersion}, but this version of the driver only supports up to ${MAX_SUPPORTED_WIRE_VERSION} (MongoDB ${MAX_SUPPORTED_SERVER_VERSION})`;\n      }\n\n      if (serverDescription.maxWireVersion < MIN_SUPPORTED_WIRE_VERSION) {\n        this.compatible = false;\n        this.compatibilityError = `Server at ${serverDescription.address} reports wire version ${serverDescription.maxWireVersion}, but this version of the driver requires at least ${MIN_SUPPORTED_WIRE_VERSION} (MongoDB ${MIN_SUPPORTED_SERVER_VERSION}).`;\n        break;\n      }\n    }\n\n    // Whenever a client updates the TopologyDescription from a hello response, it MUST set\n    // TopologyDescription.logicalSessionTimeoutMinutes to the smallest logicalSessionTimeoutMinutes\n    // value among ServerDescriptions of all data-bearing server types. If any have a null\n    // logicalSessionTimeoutMinutes, then TopologyDescription.logicalSessionTimeoutMinutes MUST be\n    // set to null.\n    this.logicalSessionTimeoutMinutes = null;\n    for (const [, server] of this.servers) {\n      if (server.isReadable) {\n        if (server.logicalSessionTimeoutMinutes == null) {\n          // If any of the servers have a null logicalSessionsTimeout, then the whole topology does\n          this.logicalSessionTimeoutMinutes = null;\n          break;\n        }\n\n        if (this.logicalSessionTimeoutMinutes == null) {\n          // First server with a non null logicalSessionsTimeout\n          this.logicalSessionTimeoutMinutes = server.logicalSessionTimeoutMinutes;\n          continue;\n        }\n\n        // Always select the smaller of the:\n        // current server logicalSessionsTimeout and the topologies logicalSessionsTimeout\n        this.logicalSessionTimeoutMinutes = Math.min(\n          this.logicalSessionTimeoutMinutes,\n          server.logicalSessionTimeoutMinutes\n        );\n      }\n    }\n  }\n\n  /**\n   * Returns a new TopologyDescription based on the SrvPollingEvent\n   * @internal\n   */\n  updateFromSrvPollingEvent(ev: SrvPollingEvent, srvMaxHosts = 0): TopologyDescription {\n    /** The SRV addresses defines the set of addresses we should be using */\n    const incomingHostnames = ev.hostnames();\n    const currentHostnames = new Set(this.servers.keys());\n\n    const hostnamesToAdd = new Set<string>(incomingHostnames);\n    const hostnamesToRemove = new Set<string>();\n    for (const hostname of currentHostnames) {\n      // filter hostnamesToAdd (made from incomingHostnames) down to what is *not* present in currentHostnames\n      hostnamesToAdd.delete(hostname);\n      if (!incomingHostnames.has(hostname)) {\n        // If the SRV Records no longer include this hostname\n        // we have to stop using it\n        hostnamesToRemove.add(hostname);\n      }\n    }\n\n    if (hostnamesToAdd.size === 0 && hostnamesToRemove.size === 0) {\n      // No new hosts to add and none to remove\n      return this;\n    }\n\n    const serverDescriptions = new Map(this.servers);\n    for (const removedHost of hostnamesToRemove) {\n      serverDescriptions.delete(removedHost);\n    }\n\n    if (hostnamesToAdd.size > 0) {\n      if (srvMaxHosts === 0) {\n        // Add all!\n        for (const hostToAdd of hostnamesToAdd) {\n          serverDescriptions.set(hostToAdd, new ServerDescription(hostToAdd));\n        }\n      } else if (serverDescriptions.size < srvMaxHosts) {\n        // Add only the amount needed to get us back to srvMaxHosts\n        const selectedHosts = shuffle(hostnamesToAdd, srvMaxHosts - serverDescriptions.size);\n        for (const selectedHostToAdd of selectedHosts) {\n          serverDescriptions.set(selectedHostToAdd, new ServerDescription(selectedHostToAdd));\n        }\n      }\n    }\n\n    return new TopologyDescription(\n      this.type,\n      serverDescriptions,\n      this.setName,\n      this.maxSetVersion,\n      this.maxElectionId,\n      this.commonWireVersion,\n      { heartbeatFrequencyMS: this.heartbeatFrequencyMS, localThresholdMS: this.localThresholdMS }\n    );\n  }\n\n  /**\n   * Returns a copy of this description updated with a given ServerDescription\n   * @internal\n   */\n  update(serverDescription: ServerDescription): TopologyDescription {\n    const address = serverDescription.address;\n\n    // potentially mutated values\n    let { type: topologyType, setName, maxSetVersion, maxElectionId, commonWireVersion } = this;\n\n    const serverType = serverDescription.type;\n    const serverDescriptions = new Map(this.servers);\n\n    // update common wire version\n    if (serverDescription.maxWireVersion !== 0) {\n      if (commonWireVersion == null) {\n        commonWireVersion = serverDescription.maxWireVersion;\n      } else {\n        commonWireVersion = Math.min(commonWireVersion, serverDescription.maxWireVersion);\n      }\n    }\n\n    if (\n      typeof serverDescription.setName === 'string' &&\n      typeof setName === 'string' &&\n      serverDescription.setName !== setName\n    ) {\n      if (topologyType === TopologyType.Single) {\n        // \"Single\" Topology with setName mismatch is direct connection usage, mark unknown do not remove\n        serverDescription = new ServerDescription(address);\n      } else {\n        serverDescriptions.delete(address);\n      }\n    }\n\n    // update the actual server description\n    serverDescriptions.set(address, serverDescription);\n\n    if (topologyType === TopologyType.Single) {\n      // once we are defined as single, that never changes\n      return new TopologyDescription(\n        TopologyType.Single,\n        serverDescriptions,\n        setName,\n        maxSetVersion,\n        maxElectionId,\n        commonWireVersion,\n        { heartbeatFrequencyMS: this.heartbeatFrequencyMS, localThresholdMS: this.localThresholdMS }\n      );\n    }\n\n    if (topologyType === TopologyType.Unknown) {\n      if (serverType === ServerType.Standalone && this.servers.size !== 1) {\n        serverDescriptions.delete(address);\n      } else {\n        topologyType = topologyTypeForServerType(serverType);\n      }\n    }\n\n    if (topologyType === TopologyType.Sharded) {\n      if (!MONGOS_OR_UNKNOWN.has(serverType)) {\n        serverDescriptions.delete(address);\n      }\n    }\n\n    if (topologyType === TopologyType.ReplicaSetNoPrimary) {\n      if (MONGOS_OR_STANDALONE.has(serverType)) {\n        serverDescriptions.delete(address);\n      }\n\n      if (serverType === ServerType.RSPrimary) {\n        const result = updateRsFromPrimary(\n          serverDescriptions,\n          serverDescription,\n          setName,\n          maxSetVersion,\n          maxElectionId\n        );\n\n        topologyType = result[0];\n        setName = result[1];\n        maxSetVersion = result[2];\n        maxElectionId = result[3];\n      } else if (NON_PRIMARY_RS_MEMBERS.has(serverType)) {\n        const result = updateRsNoPrimaryFromMember(serverDescriptions, serverDescription, setName);\n        topologyType = result[0];\n        setName = result[1];\n      }\n    }\n\n    if (topologyType === TopologyType.ReplicaSetWithPrimary) {\n      if (MONGOS_OR_STANDALONE.has(serverType)) {\n        serverDescriptions.delete(address);\n        topologyType = checkHasPrimary(serverDescriptions);\n      } else if (serverType === ServerType.RSPrimary) {\n        const result = updateRsFromPrimary(\n          serverDescriptions,\n          serverDescription,\n          setName,\n          maxSetVersion,\n          maxElectionId\n        );\n\n        topologyType = result[0];\n        setName = result[1];\n        maxSetVersion = result[2];\n        maxElectionId = result[3];\n      } else if (NON_PRIMARY_RS_MEMBERS.has(serverType)) {\n        topologyType = updateRsWithPrimaryFromMember(\n          serverDescriptions,\n          serverDescription,\n          setName\n        );\n      } else {\n        topologyType = checkHasPrimary(serverDescriptions);\n      }\n    }\n\n    return new TopologyDescription(\n      topologyType,\n      serverDescriptions,\n      setName,\n      maxSetVersion,\n      maxElectionId,\n      commonWireVersion,\n      { heartbeatFrequencyMS: this.heartbeatFrequencyMS, localThresholdMS: this.localThresholdMS }\n    );\n  }\n\n  get error(): MongoError | null {\n    const descriptionsWithError = Array.from(this.servers.values()).filter(\n      (sd: ServerDescription) => sd.error\n    );\n\n    if (descriptionsWithError.length > 0) {\n      return descriptionsWithError[0].error;\n    }\n\n    return null;\n  }\n\n  /**\n   * Determines if the topology description has any known servers\n   */\n  get hasKnownServers(): boolean {\n    return Array.from(this.servers.values()).some(\n      (sd: ServerDescription) => sd.type !== ServerType.Unknown\n    );\n  }\n\n  /**\n   * Determines if this topology description has a data-bearing server available.\n   */\n  get hasDataBearingServers(): boolean {\n    return Array.from(this.servers.values()).some((sd: ServerDescription) => sd.isDataBearing);\n  }\n\n  /**\n   * Determines if the topology has a definition for the provided address\n   * @internal\n   */\n  hasServer(address: string): boolean {\n    return this.servers.has(address);\n  }\n\n  /**\n   * Returns a JSON-serializable representation of the TopologyDescription.  This is primarily\n   * intended for use with JSON.stringify().\n   *\n   * This method will not throw.\n   */\n  toJSON() {\n    return EJSON.serialize(this);\n  }\n}\n\nfunction topologyTypeForServerType(serverType: ServerType): TopologyType {\n  switch (serverType) {\n    case ServerType.Standalone:\n      return TopologyType.Single;\n    case ServerType.Mongos:\n      return TopologyType.Sharded;\n    case ServerType.RSPrimary:\n      return TopologyType.ReplicaSetWithPrimary;\n    case ServerType.RSOther:\n    case ServerType.RSSecondary:\n      return TopologyType.ReplicaSetNoPrimary;\n    default:\n      return TopologyType.Unknown;\n  }\n}\n\nfunction updateRsFromPrimary(\n  serverDescriptions: Map<string, ServerDescription>,\n  serverDescription: ServerDescription,\n  setName: string | null = null,\n  maxSetVersion: number | null = null,\n  maxElectionId: ObjectId | null = null\n): [TopologyType, string | null, number | null, ObjectId | null] {\n  const setVersionElectionIdMismatch = (\n    serverDescription: ServerDescription,\n    maxSetVersion: number | null,\n    maxElectionId: ObjectId | null\n  ) => {\n    return (\n      `primary marked stale due to electionId/setVersion mismatch:` +\n      ` server setVersion: ${serverDescription.setVersion},` +\n      ` server electionId: ${serverDescription.electionId},` +\n      ` topology setVersion: ${maxSetVersion},` +\n      ` topology electionId: ${maxElectionId}`\n    );\n  };\n  setName = setName || serverDescription.setName;\n  if (setName !== serverDescription.setName) {\n    serverDescriptions.delete(serverDescription.address);\n    return [checkHasPrimary(serverDescriptions), setName, maxSetVersion, maxElectionId];\n  }\n\n  if (serverDescription.maxWireVersion >= 17) {\n    const electionIdComparison = compareObjectId(maxElectionId, serverDescription.electionId);\n    const maxElectionIdIsEqual = electionIdComparison === 0;\n    const maxElectionIdIsLess = electionIdComparison === -1;\n    const maxSetVersionIsLessOrEqual =\n      (maxSetVersion ?? -1) <= (serverDescription.setVersion ?? -1);\n\n    if (maxElectionIdIsLess || (maxElectionIdIsEqual && maxSetVersionIsLessOrEqual)) {\n      // The reported electionId was greater\n      // or the electionId was equal and reported setVersion was greater\n      // Always update both values, they are a tuple\n      maxElectionId = serverDescription.electionId;\n      maxSetVersion = serverDescription.setVersion;\n    } else {\n      // Stale primary\n      // replace serverDescription with a default ServerDescription of type \"Unknown\"\n      serverDescriptions.set(\n        serverDescription.address,\n        new ServerDescription(serverDescription.address, undefined, {\n          error: new MongoStalePrimaryError(\n            setVersionElectionIdMismatch(serverDescription, maxSetVersion, maxElectionId)\n          )\n        })\n      );\n\n      return [checkHasPrimary(serverDescriptions), setName, maxSetVersion, maxElectionId];\n    }\n  } else {\n    const electionId = serverDescription.electionId ? serverDescription.electionId : null;\n    if (serverDescription.setVersion && electionId) {\n      if (maxSetVersion && maxElectionId) {\n        if (\n          maxSetVersion > serverDescription.setVersion ||\n          compareObjectId(maxElectionId, electionId) > 0\n        ) {\n          // this primary is stale, we must remove it\n          serverDescriptions.set(\n            serverDescription.address,\n            new ServerDescription(serverDescription.address, undefined, {\n              error: new MongoStalePrimaryError(\n                setVersionElectionIdMismatch(serverDescription, maxSetVersion, maxElectionId)\n              )\n            })\n          );\n\n          return [checkHasPrimary(serverDescriptions), setName, maxSetVersion, maxElectionId];\n        }\n      }\n\n      maxElectionId = serverDescription.electionId;\n    }\n\n    if (\n      serverDescription.setVersion != null &&\n      (maxSetVersion == null || serverDescription.setVersion > maxSetVersion)\n    ) {\n      maxSetVersion = serverDescription.setVersion;\n    }\n  }\n\n  // We've heard from the primary. Is it the same primary as before?\n  for (const [address, server] of serverDescriptions) {\n    if (server.type === ServerType.RSPrimary && server.address !== serverDescription.address) {\n      // Reset old primary's type to Unknown.\n      serverDescriptions.set(\n        address,\n        new ServerDescription(server.address, undefined, {\n          error: new MongoStalePrimaryError(\n            'primary marked stale due to discovery of newer primary'\n          )\n        })\n      );\n\n      // There can only be one primary\n      break;\n    }\n  }\n\n  // Discover new hosts from this primary's response.\n  serverDescription.allHosts.forEach((address: string) => {\n    if (!serverDescriptions.has(address)) {\n      serverDescriptions.set(address, new ServerDescription(address));\n    }\n  });\n\n  // Remove hosts not in the response.\n  const currentAddresses = Array.from(serverDescriptions.keys());\n  const responseAddresses = serverDescription.allHosts;\n  currentAddresses\n    .filter((addr: string) => responseAddresses.indexOf(addr) === -1)\n    .forEach((address: string) => {\n      serverDescriptions.delete(address);\n    });\n\n  return [checkHasPrimary(serverDescriptions), setName, maxSetVersion, maxElectionId];\n}\n\nfunction updateRsWithPrimaryFromMember(\n  serverDescriptions: Map<string, ServerDescription>,\n  serverDescription: ServerDescription,\n  setName: string | null = null\n): TopologyType {\n  if (setName == null) {\n    // TODO(NODE-3483): should be an appropriate runtime error\n    throw new MongoRuntimeError('Argument \"setName\" is required if connected to a replica set');\n  }\n\n  if (\n    setName !== serverDescription.setName ||\n    (serverDescription.me && serverDescription.address !== serverDescription.me)\n  ) {\n    serverDescriptions.delete(serverDescription.address);\n  }\n\n  return checkHasPrimary(serverDescriptions);\n}\n\nfunction updateRsNoPrimaryFromMember(\n  serverDescriptions: Map<string, ServerDescription>,\n  serverDescription: ServerDescription,\n  setName: string | null = null\n): [TopologyType, string | null] {\n  const topologyType = TopologyType.ReplicaSetNoPrimary;\n  setName = setName ?? serverDescription.setName;\n  if (setName !== serverDescription.setName) {\n    serverDescriptions.delete(serverDescription.address);\n    return [topologyType, setName];\n  }\n\n  serverDescription.allHosts.forEach((address: string) => {\n    if (!serverDescriptions.has(address)) {\n      serverDescriptions.set(address, new ServerDescription(address));\n    }\n  });\n\n  if (serverDescription.me && serverDescription.address !== serverDescription.me) {\n    serverDescriptions.delete(serverDescription.address);\n  }\n\n  return [topologyType, setName];\n}\n\nfunction checkHasPrimary(serverDescriptions: Map<string, ServerDescription>): TopologyType {\n  for (const serverDescription of serverDescriptions.values()) {\n    if (serverDescription.type === ServerType.RSPrimary) {\n      return TopologyType.ReplicaSetWithPrimary;\n    }\n  }\n\n  return TopologyType.ReplicaSetNoPrimary;\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,MAAA,GAAAC,OAAA;AACA,MAAAC,cAAA,GAAAD,OAAA;AACA,MAAAE,OAAA,GAAAF,OAAA;AACA,MAAAG,OAAA,GAAAH,OAAA;AACA,MAAAI,QAAA,GAAAJ,OAAA;AACA,MAAAK,oBAAA,GAAAL,OAAA;AAGA;AACA,MAAMM,4BAA4B,GAAGL,cAAc,CAACK,4BAA4B;AAChF,MAAMC,4BAA4B,GAAGN,cAAc,CAACM,4BAA4B;AAChF,MAAMC,0BAA0B,GAAGP,cAAc,CAACO,0BAA0B;AAC5E,MAAMC,0BAA0B,GAAGR,cAAc,CAACQ,0BAA0B;AAE5E,MAAMC,iBAAiB,GAAG,IAAIC,GAAG,CAAa,CAACP,QAAA,CAAAQ,UAAU,CAACC,MAAM,EAAET,QAAA,CAAAQ,UAAU,CAACE,OAAO,CAAC,CAAC;AACtF,MAAMC,oBAAoB,GAAG,IAAIJ,GAAG,CAAa,CAACP,QAAA,CAAAQ,UAAU,CAACC,MAAM,EAAET,QAAA,CAAAQ,UAAU,CAACI,UAAU,CAAC,CAAC;AAC5F,MAAMC,sBAAsB,GAAG,IAAIN,GAAG,CAAa,CACjDP,QAAA,CAAAQ,UAAU,CAACM,WAAW,EACtBd,QAAA,CAAAQ,UAAU,CAACO,SAAS,EACpBf,QAAA,CAAAQ,UAAU,CAACQ,OAAO,CACnB,CAAC;AAQF;;;;AAIA,MAAaC,mBAAmB;EAa9B;;;EAGAC,YACEC,YAA0B,EAC1BC,kBAAA,GAA4D,IAAI,EAChEC,OAAA,GAAyB,IAAI,EAC7BC,aAAA,GAA+B,IAAI,EACnCC,aAAA,GAAiC,IAAI,EACrCC,iBAAA,GAAmC,IAAI,EACvCC,OAAA,GAA6C,IAAI;IAEjDA,OAAO,GAAGA,OAAO,IAAI,EAAE;IAEvB,IAAI,CAACC,IAAI,GAAGP,YAAY,IAAInB,QAAA,CAAA2B,YAAY,CAACjB,OAAO;IAChD,IAAI,CAACkB,OAAO,GAAGR,kBAAkB,IAAI,IAAIS,GAAG,EAAE;IAC9C,IAAI,CAACC,KAAK,GAAG,KAAK;IAClB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,oBAAoB,GAAGP,OAAO,CAACO,oBAAoB,IAAI,CAAC;IAC7D,IAAI,CAACC,gBAAgB,GAAGR,OAAO,CAACQ,gBAAgB,IAAI,EAAE;IACtD,IAAI,CAACZ,OAAO,GAAGA,OAAO,IAAI,IAAI;IAC9B,IAAI,CAACE,aAAa,GAAGA,aAAa,IAAI,IAAI;IAC1C,IAAI,CAACD,aAAa,GAAGA,aAAa,IAAI,IAAI;IAC1C,IAAI,CAACE,iBAAiB,GAAGA,iBAAiB,IAAI,CAAC;IAE/C;IACA,KAAK,MAAMU,iBAAiB,IAAI,IAAI,CAACN,OAAO,CAACO,MAAM,EAAE,EAAE;MACrD;MACA,IACED,iBAAiB,CAACR,IAAI,KAAK1B,QAAA,CAAAQ,UAAU,CAACE,OAAO,IAC7CwB,iBAAiB,CAACR,IAAI,KAAK1B,QAAA,CAAAQ,UAAU,CAAC4B,YAAY,EAClD;QACA;MACF;MAEA,IAAIF,iBAAiB,CAACG,cAAc,GAAGhC,0BAA0B,EAAE;QACjE,IAAI,CAAC0B,UAAU,GAAG,KAAK;QACvB,IAAI,CAACO,kBAAkB,GAAG,aAAaJ,iBAAiB,CAACK,OAAO,0BAA0BL,iBAAiB,CAACG,cAAc,wDAAwDhC,0BAA0B,aAAaF,4BAA4B,GAAG;MAC1P;MAEA,IAAI+B,iBAAiB,CAACM,cAAc,GAAGpC,0BAA0B,EAAE;QACjE,IAAI,CAAC2B,UAAU,GAAG,KAAK;QACvB,IAAI,CAACO,kBAAkB,GAAG,aAAaJ,iBAAiB,CAACK,OAAO,yBAAyBL,iBAAiB,CAACM,cAAc,sDAAsDpC,0BAA0B,aAAaF,4BAA4B,IAAI;QACtP;MACF;IACF;IAEA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACuC,4BAA4B,GAAG,IAAI;IACxC,KAAK,MAAM,GAAGC,MAAM,CAAC,IAAI,IAAI,CAACd,OAAO,EAAE;MACrC,IAAIc,MAAM,CAACC,UAAU,EAAE;QACrB,IAAID,MAAM,CAACD,4BAA4B,IAAI,IAAI,EAAE;UAC/C;UACA,IAAI,CAACA,4BAA4B,GAAG,IAAI;UACxC;QACF;QAEA,IAAI,IAAI,CAACA,4BAA4B,IAAI,IAAI,EAAE;UAC7C;UACA,IAAI,CAACA,4BAA4B,GAAGC,MAAM,CAACD,4BAA4B;UACvE;QACF;QAEA;QACA;QACA,IAAI,CAACA,4BAA4B,GAAGG,IAAI,CAACC,GAAG,CAC1C,IAAI,CAACJ,4BAA4B,EACjCC,MAAM,CAACD,4BAA4B,CACpC;MACH;IACF;EACF;EAEA;;;;EAIAK,yBAAyBA,CAACC,EAAmB,EAAEC,WAAW,GAAG,CAAC;IAC5D;IACA,MAAMC,iBAAiB,GAAGF,EAAE,CAACG,SAAS,EAAE;IACxC,MAAMC,gBAAgB,GAAG,IAAI5C,GAAG,CAAC,IAAI,CAACqB,OAAO,CAACwB,IAAI,EAAE,CAAC;IAErD,MAAMC,cAAc,GAAG,IAAI9C,GAAG,CAAS0C,iBAAiB,CAAC;IACzD,MAAMK,iBAAiB,GAAG,IAAI/C,GAAG,EAAU;IAC3C,KAAK,MAAMgD,QAAQ,IAAIJ,gBAAgB,EAAE;MACvC;MACAE,cAAc,CAACG,MAAM,CAACD,QAAQ,CAAC;MAC/B,IAAI,CAACN,iBAAiB,CAACQ,GAAG,CAACF,QAAQ,CAAC,EAAE;QACpC;QACA;QACAD,iBAAiB,CAACI,GAAG,CAACH,QAAQ,CAAC;MACjC;IACF;IAEA,IAAIF,cAAc,CAACM,IAAI,KAAK,CAAC,IAAIL,iBAAiB,CAACK,IAAI,KAAK,CAAC,EAAE;MAC7D;MACA,OAAO,IAAI;IACb;IAEA,MAAMvC,kBAAkB,GAAG,IAAIS,GAAG,CAAC,IAAI,CAACD,OAAO,CAAC;IAChD,KAAK,MAAMgC,WAAW,IAAIN,iBAAiB,EAAE;MAC3ClC,kBAAkB,CAACoC,MAAM,CAACI,WAAW,CAAC;IACxC;IAEA,IAAIP,cAAc,CAACM,IAAI,GAAG,CAAC,EAAE;MAC3B,IAAIX,WAAW,KAAK,CAAC,EAAE;QACrB;QACA,KAAK,MAAMa,SAAS,IAAIR,cAAc,EAAE;UACtCjC,kBAAkB,CAAC0C,GAAG,CAACD,SAAS,EAAE,IAAI5D,oBAAA,CAAA8D,iBAAiB,CAACF,SAAS,CAAC,CAAC;QACrE;MACF,CAAC,MAAM,IAAIzC,kBAAkB,CAACuC,IAAI,GAAGX,WAAW,EAAE;QAChD;QACA,MAAMgB,aAAa,GAAG,IAAAjE,OAAA,CAAAkE,OAAO,EAACZ,cAAc,EAAEL,WAAW,GAAG5B,kBAAkB,CAACuC,IAAI,CAAC;QACpF,KAAK,MAAMO,iBAAiB,IAAIF,aAAa,EAAE;UAC7C5C,kBAAkB,CAAC0C,GAAG,CAACI,iBAAiB,EAAE,IAAIjE,oBAAA,CAAA8D,iBAAiB,CAACG,iBAAiB,CAAC,CAAC;QACrF;MACF;IACF;IAEA,OAAO,IAAIjD,mBAAmB,CAC5B,IAAI,CAACS,IAAI,EACTN,kBAAkB,EAClB,IAAI,CAACC,OAAO,EACZ,IAAI,CAACC,aAAa,EAClB,IAAI,CAACC,aAAa,EAClB,IAAI,CAACC,iBAAiB,EACtB;MAAEQ,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;MAAEC,gBAAgB,EAAE,IAAI,CAACA;IAAgB,CAAE,CAC7F;EACH;EAEA;;;;EAIAkC,MAAMA,CAACjC,iBAAoC;IACzC,MAAMK,OAAO,GAAGL,iBAAiB,CAACK,OAAO;IAEzC;IACA,IAAI;MAAEb,IAAI,EAAEP,YAAY;MAAEE,OAAO;MAAEC,aAAa;MAAEC,aAAa;MAAEC;IAAiB,CAAE,GAAG,IAAI;IAE3F,MAAM4C,UAAU,GAAGlC,iBAAiB,CAACR,IAAI;IACzC,MAAMN,kBAAkB,GAAG,IAAIS,GAAG,CAAC,IAAI,CAACD,OAAO,CAAC;IAEhD;IACA,IAAIM,iBAAiB,CAACM,cAAc,KAAK,CAAC,EAAE;MAC1C,IAAIhB,iBAAiB,IAAI,IAAI,EAAE;QAC7BA,iBAAiB,GAAGU,iBAAiB,CAACM,cAAc;MACtD,CAAC,MAAM;QACLhB,iBAAiB,GAAGoB,IAAI,CAACC,GAAG,CAACrB,iBAAiB,EAAEU,iBAAiB,CAACM,cAAc,CAAC;MACnF;IACF;IAEA,IACE,OAAON,iBAAiB,CAACb,OAAO,KAAK,QAAQ,IAC7C,OAAOA,OAAO,KAAK,QAAQ,IAC3Ba,iBAAiB,CAACb,OAAO,KAAKA,OAAO,EACrC;MACA,IAAIF,YAAY,KAAKnB,QAAA,CAAA2B,YAAY,CAAC0C,MAAM,EAAE;QACxC;QACAnC,iBAAiB,GAAG,IAAIjC,oBAAA,CAAA8D,iBAAiB,CAACxB,OAAO,CAAC;MACpD,CAAC,MAAM;QACLnB,kBAAkB,CAACoC,MAAM,CAACjB,OAAO,CAAC;MACpC;IACF;IAEA;IACAnB,kBAAkB,CAAC0C,GAAG,CAACvB,OAAO,EAAEL,iBAAiB,CAAC;IAElD,IAAIf,YAAY,KAAKnB,QAAA,CAAA2B,YAAY,CAAC0C,MAAM,EAAE;MACxC;MACA,OAAO,IAAIpD,mBAAmB,CAC5BjB,QAAA,CAAA2B,YAAY,CAAC0C,MAAM,EACnBjD,kBAAkB,EAClBC,OAAO,EACPC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjB;QAAEQ,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;QAAEC,gBAAgB,EAAE,IAAI,CAACA;MAAgB,CAAE,CAC7F;IACH;IAEA,IAAId,YAAY,KAAKnB,QAAA,CAAA2B,YAAY,CAACjB,OAAO,EAAE;MACzC,IAAI0D,UAAU,KAAKpE,QAAA,CAAAQ,UAAU,CAACI,UAAU,IAAI,IAAI,CAACgB,OAAO,CAAC+B,IAAI,KAAK,CAAC,EAAE;QACnEvC,kBAAkB,CAACoC,MAAM,CAACjB,OAAO,CAAC;MACpC,CAAC,MAAM;QACLpB,YAAY,GAAGmD,yBAAyB,CAACF,UAAU,CAAC;MACtD;IACF;IAEA,IAAIjD,YAAY,KAAKnB,QAAA,CAAA2B,YAAY,CAAC4C,OAAO,EAAE;MACzC,IAAI,CAACjE,iBAAiB,CAACmD,GAAG,CAACW,UAAU,CAAC,EAAE;QACtChD,kBAAkB,CAACoC,MAAM,CAACjB,OAAO,CAAC;MACpC;IACF;IAEA,IAAIpB,YAAY,KAAKnB,QAAA,CAAA2B,YAAY,CAAC6C,mBAAmB,EAAE;MACrD,IAAI7D,oBAAoB,CAAC8C,GAAG,CAACW,UAAU,CAAC,EAAE;QACxChD,kBAAkB,CAACoC,MAAM,CAACjB,OAAO,CAAC;MACpC;MAEA,IAAI6B,UAAU,KAAKpE,QAAA,CAAAQ,UAAU,CAACiE,SAAS,EAAE;QACvC,MAAMC,MAAM,GAAGC,mBAAmB,CAChCvD,kBAAkB,EAClBc,iBAAiB,EACjBb,OAAO,EACPC,aAAa,EACbC,aAAa,CACd;QAEDJ,YAAY,GAAGuD,MAAM,CAAC,CAAC,CAAC;QACxBrD,OAAO,GAAGqD,MAAM,CAAC,CAAC,CAAC;QACnBpD,aAAa,GAAGoD,MAAM,CAAC,CAAC,CAAC;QACzBnD,aAAa,GAAGmD,MAAM,CAAC,CAAC,CAAC;MAC3B,CAAC,MAAM,IAAI7D,sBAAsB,CAAC4C,GAAG,CAACW,UAAU,CAAC,EAAE;QACjD,MAAMM,MAAM,GAAGE,2BAA2B,CAACxD,kBAAkB,EAAEc,iBAAiB,EAAEb,OAAO,CAAC;QAC1FF,YAAY,GAAGuD,MAAM,CAAC,CAAC,CAAC;QACxBrD,OAAO,GAAGqD,MAAM,CAAC,CAAC,CAAC;MACrB;IACF;IAEA,IAAIvD,YAAY,KAAKnB,QAAA,CAAA2B,YAAY,CAACkD,qBAAqB,EAAE;MACvD,IAAIlE,oBAAoB,CAAC8C,GAAG,CAACW,UAAU,CAAC,EAAE;QACxChD,kBAAkB,CAACoC,MAAM,CAACjB,OAAO,CAAC;QAClCpB,YAAY,GAAG2D,eAAe,CAAC1D,kBAAkB,CAAC;MACpD,CAAC,MAAM,IAAIgD,UAAU,KAAKpE,QAAA,CAAAQ,UAAU,CAACiE,SAAS,EAAE;QAC9C,MAAMC,MAAM,GAAGC,mBAAmB,CAChCvD,kBAAkB,EAClBc,iBAAiB,EACjBb,OAAO,EACPC,aAAa,EACbC,aAAa,CACd;QAEDJ,YAAY,GAAGuD,MAAM,CAAC,CAAC,CAAC;QACxBrD,OAAO,GAAGqD,MAAM,CAAC,CAAC,CAAC;QACnBpD,aAAa,GAAGoD,MAAM,CAAC,CAAC,CAAC;QACzBnD,aAAa,GAAGmD,MAAM,CAAC,CAAC,CAAC;MAC3B,CAAC,MAAM,IAAI7D,sBAAsB,CAAC4C,GAAG,CAACW,UAAU,CAAC,EAAE;QACjDjD,YAAY,GAAG4D,6BAA6B,CAC1C3D,kBAAkB,EAClBc,iBAAiB,EACjBb,OAAO,CACR;MACH,CAAC,MAAM;QACLF,YAAY,GAAG2D,eAAe,CAAC1D,kBAAkB,CAAC;MACpD;IACF;IAEA,OAAO,IAAIH,mBAAmB,CAC5BE,YAAY,EACZC,kBAAkB,EAClBC,OAAO,EACPC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjB;MAAEQ,oBAAoB,EAAE,IAAI,CAACA,oBAAoB;MAAEC,gBAAgB,EAAE,IAAI,CAACA;IAAgB,CAAE,CAC7F;EACH;EAEA,IAAI+C,KAAKA,CAAA;IACP,MAAMC,qBAAqB,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAI,CAACvD,OAAO,CAACO,MAAM,EAAE,CAAC,CAACiD,MAAM,CACnEC,EAAqB,IAAKA,EAAE,CAACL,KAAK,CACpC;IAED,IAAIC,qBAAqB,CAACK,MAAM,GAAG,CAAC,EAAE;MACpC,OAAOL,qBAAqB,CAAC,CAAC,CAAC,CAACD,KAAK;IACvC;IAEA,OAAO,IAAI;EACb;EAEA;;;EAGA,IAAIO,eAAeA,CAAA;IACjB,OAAOL,KAAK,CAACC,IAAI,CAAC,IAAI,CAACvD,OAAO,CAACO,MAAM,EAAE,CAAC,CAACqD,IAAI,CAC1CH,EAAqB,IAAKA,EAAE,CAAC3D,IAAI,KAAK1B,QAAA,CAAAQ,UAAU,CAACE,OAAO,CAC1D;EACH;EAEA;;;EAGA,IAAI+E,qBAAqBA,CAAA;IACvB,OAAOP,KAAK,CAACC,IAAI,CAAC,IAAI,CAACvD,OAAO,CAACO,MAAM,EAAE,CAAC,CAACqD,IAAI,CAAEH,EAAqB,IAAKA,EAAE,CAACK,aAAa,CAAC;EAC5F;EAEA;;;;EAIAC,SAASA,CAACpD,OAAe;IACvB,OAAO,IAAI,CAACX,OAAO,CAAC6B,GAAG,CAAClB,OAAO,CAAC;EAClC;EAEA;;;;;;EAMAqD,MAAMA,CAAA;IACJ,OAAOjG,MAAA,CAAAkG,KAAK,CAACC,SAAS,CAAC,IAAI,CAAC;EAC9B;;AAhUFC,OAAA,CAAA9E,mBAAA,GAAAA,mBAAA;AAmUA,SAASqD,yBAAyBA,CAACF,UAAsB;EACvD,QAAQA,UAAU;IAChB,KAAKpE,QAAA,CAAAQ,UAAU,CAACI,UAAU;MACxB,OAAOZ,QAAA,CAAA2B,YAAY,CAAC0C,MAAM;IAC5B,KAAKrE,QAAA,CAAAQ,UAAU,CAACC,MAAM;MACpB,OAAOT,QAAA,CAAA2B,YAAY,CAAC4C,OAAO;IAC7B,KAAKvE,QAAA,CAAAQ,UAAU,CAACiE,SAAS;MACvB,OAAOzE,QAAA,CAAA2B,YAAY,CAACkD,qBAAqB;IAC3C,KAAK7E,QAAA,CAAAQ,UAAU,CAACQ,OAAO;IACvB,KAAKhB,QAAA,CAAAQ,UAAU,CAACM,WAAW;MACzB,OAAOd,QAAA,CAAA2B,YAAY,CAAC6C,mBAAmB;IACzC;MACE,OAAOxE,QAAA,CAAA2B,YAAY,CAACjB,OAAO;EAC/B;AACF;AAEA,SAASiE,mBAAmBA,CAC1BvD,kBAAkD,EAClDc,iBAAoC,EACpCb,OAAA,GAAyB,IAAI,EAC7BC,aAAA,GAA+B,IAAI,EACnCC,aAAA,GAAiC,IAAI;EAErC,MAAMyE,4BAA4B,GAAGA,CACnC9D,iBAAoC,EACpCZ,aAA4B,EAC5BC,aAA8B,KAC5B;IACF,OACE,6DAA6D,GAC7D,uBAAuBW,iBAAiB,CAAC+D,UAAU,GAAG,GACtD,uBAAuB/D,iBAAiB,CAACgE,UAAU,GAAG,GACtD,yBAAyB5E,aAAa,GAAG,GACzC,yBAAyBC,aAAa,EAAE;EAE5C,CAAC;EACDF,OAAO,GAAGA,OAAO,IAAIa,iBAAiB,CAACb,OAAO;EAC9C,IAAIA,OAAO,KAAKa,iBAAiB,CAACb,OAAO,EAAE;IACzCD,kBAAkB,CAACoC,MAAM,CAACtB,iBAAiB,CAACK,OAAO,CAAC;IACpD,OAAO,CAACuC,eAAe,CAAC1D,kBAAkB,CAAC,EAAEC,OAAO,EAAEC,aAAa,EAAEC,aAAa,CAAC;EACrF;EAEA,IAAIW,iBAAiB,CAACM,cAAc,IAAI,EAAE,EAAE;IAC1C,MAAM2D,oBAAoB,GAAG,IAAApG,OAAA,CAAAqG,eAAe,EAAC7E,aAAa,EAAEW,iBAAiB,CAACgE,UAAU,CAAC;IACzF,MAAMG,oBAAoB,GAAGF,oBAAoB,KAAK,CAAC;IACvD,MAAMG,mBAAmB,GAAGH,oBAAoB,KAAK,CAAC,CAAC;IACvD,MAAMI,0BAA0B,GAC9B,CAACjF,aAAa,IAAI,CAAC,CAAC,MAAMY,iBAAiB,CAAC+D,UAAU,IAAI,CAAC,CAAC,CAAC;IAE/D,IAAIK,mBAAmB,IAAKD,oBAAoB,IAAIE,0BAA2B,EAAE;MAC/E;MACA;MACA;MACAhF,aAAa,GAAGW,iBAAiB,CAACgE,UAAU;MAC5C5E,aAAa,GAAGY,iBAAiB,CAAC+D,UAAU;IAC9C,CAAC,MAAM;MACL;MACA;MACA7E,kBAAkB,CAAC0C,GAAG,CACpB5B,iBAAiB,CAACK,OAAO,EACzB,IAAItC,oBAAA,CAAA8D,iBAAiB,CAAC7B,iBAAiB,CAACK,OAAO,EAAEiE,SAAS,EAAE;QAC1DxB,KAAK,EAAE,IAAIlF,OAAA,CAAA2G,sBAAsB,CAC/BT,4BAA4B,CAAC9D,iBAAiB,EAAEZ,aAAa,EAAEC,aAAa,CAAC;OAEhF,CAAC,CACH;MAED,OAAO,CAACuD,eAAe,CAAC1D,kBAAkB,CAAC,EAAEC,OAAO,EAAEC,aAAa,EAAEC,aAAa,CAAC;IACrF;EACF,CAAC,MAAM;IACL,MAAM2E,UAAU,GAAGhE,iBAAiB,CAACgE,UAAU,GAAGhE,iBAAiB,CAACgE,UAAU,GAAG,IAAI;IACrF,IAAIhE,iBAAiB,CAAC+D,UAAU,IAAIC,UAAU,EAAE;MAC9C,IAAI5E,aAAa,IAAIC,aAAa,EAAE;QAClC,IACED,aAAa,GAAGY,iBAAiB,CAAC+D,UAAU,IAC5C,IAAAlG,OAAA,CAAAqG,eAAe,EAAC7E,aAAa,EAAE2E,UAAU,CAAC,GAAG,CAAC,EAC9C;UACA;UACA9E,kBAAkB,CAAC0C,GAAG,CACpB5B,iBAAiB,CAACK,OAAO,EACzB,IAAItC,oBAAA,CAAA8D,iBAAiB,CAAC7B,iBAAiB,CAACK,OAAO,EAAEiE,SAAS,EAAE;YAC1DxB,KAAK,EAAE,IAAIlF,OAAA,CAAA2G,sBAAsB,CAC/BT,4BAA4B,CAAC9D,iBAAiB,EAAEZ,aAAa,EAAEC,aAAa,CAAC;WAEhF,CAAC,CACH;UAED,OAAO,CAACuD,eAAe,CAAC1D,kBAAkB,CAAC,EAAEC,OAAO,EAAEC,aAAa,EAAEC,aAAa,CAAC;QACrF;MACF;MAEAA,aAAa,GAAGW,iBAAiB,CAACgE,UAAU;IAC9C;IAEA,IACEhE,iBAAiB,CAAC+D,UAAU,IAAI,IAAI,KACnC3E,aAAa,IAAI,IAAI,IAAIY,iBAAiB,CAAC+D,UAAU,GAAG3E,aAAa,CAAC,EACvE;MACAA,aAAa,GAAGY,iBAAiB,CAAC+D,UAAU;IAC9C;EACF;EAEA;EACA,KAAK,MAAM,CAAC1D,OAAO,EAAEG,MAAM,CAAC,IAAItB,kBAAkB,EAAE;IAClD,IAAIsB,MAAM,CAAChB,IAAI,KAAK1B,QAAA,CAAAQ,UAAU,CAACiE,SAAS,IAAI/B,MAAM,CAACH,OAAO,KAAKL,iBAAiB,CAACK,OAAO,EAAE;MACxF;MACAnB,kBAAkB,CAAC0C,GAAG,CACpBvB,OAAO,EACP,IAAItC,oBAAA,CAAA8D,iBAAiB,CAACrB,MAAM,CAACH,OAAO,EAAEiE,SAAS,EAAE;QAC/CxB,KAAK,EAAE,IAAIlF,OAAA,CAAA2G,sBAAsB,CAC/B,wDAAwD;OAE3D,CAAC,CACH;MAED;MACA;IACF;EACF;EAEA;EACAvE,iBAAiB,CAACwE,QAAQ,CAACC,OAAO,CAAEpE,OAAe,IAAI;IACrD,IAAI,CAACnB,kBAAkB,CAACqC,GAAG,CAAClB,OAAO,CAAC,EAAE;MACpCnB,kBAAkB,CAAC0C,GAAG,CAACvB,OAAO,EAAE,IAAItC,oBAAA,CAAA8D,iBAAiB,CAACxB,OAAO,CAAC,CAAC;IACjE;EACF,CAAC,CAAC;EAEF;EACA,MAAMqE,gBAAgB,GAAG1B,KAAK,CAACC,IAAI,CAAC/D,kBAAkB,CAACgC,IAAI,EAAE,CAAC;EAC9D,MAAMyD,iBAAiB,GAAG3E,iBAAiB,CAACwE,QAAQ;EACpDE,gBAAgB,CACbxB,MAAM,CAAE0B,IAAY,IAAKD,iBAAiB,CAACE,OAAO,CAACD,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,CAChEH,OAAO,CAAEpE,OAAe,IAAI;IAC3BnB,kBAAkB,CAACoC,MAAM,CAACjB,OAAO,CAAC;EACpC,CAAC,CAAC;EAEJ,OAAO,CAACuC,eAAe,CAAC1D,kBAAkB,CAAC,EAAEC,OAAO,EAAEC,aAAa,EAAEC,aAAa,CAAC;AACrF;AAEA,SAASwD,6BAA6BA,CACpC3D,kBAAkD,EAClDc,iBAAoC,EACpCb,OAAA,GAAyB,IAAI;EAE7B,IAAIA,OAAO,IAAI,IAAI,EAAE;IACnB;IACA,MAAM,IAAIvB,OAAA,CAAAkH,iBAAiB,CAAC,8DAA8D,CAAC;EAC7F;EAEA,IACE3F,OAAO,KAAKa,iBAAiB,CAACb,OAAO,IACpCa,iBAAiB,CAAC+E,EAAE,IAAI/E,iBAAiB,CAACK,OAAO,KAAKL,iBAAiB,CAAC+E,EAAG,EAC5E;IACA7F,kBAAkB,CAACoC,MAAM,CAACtB,iBAAiB,CAACK,OAAO,CAAC;EACtD;EAEA,OAAOuC,eAAe,CAAC1D,kBAAkB,CAAC;AAC5C;AAEA,SAASwD,2BAA2BA,CAClCxD,kBAAkD,EAClDc,iBAAoC,EACpCb,OAAA,GAAyB,IAAI;EAE7B,MAAMF,YAAY,GAAGnB,QAAA,CAAA2B,YAAY,CAAC6C,mBAAmB;EACrDnD,OAAO,GAAGA,OAAO,IAAIa,iBAAiB,CAACb,OAAO;EAC9C,IAAIA,OAAO,KAAKa,iBAAiB,CAACb,OAAO,EAAE;IACzCD,kBAAkB,CAACoC,MAAM,CAACtB,iBAAiB,CAACK,OAAO,CAAC;IACpD,OAAO,CAACpB,YAAY,EAAEE,OAAO,CAAC;EAChC;EAEAa,iBAAiB,CAACwE,QAAQ,CAACC,OAAO,CAAEpE,OAAe,IAAI;IACrD,IAAI,CAACnB,kBAAkB,CAACqC,GAAG,CAAClB,OAAO,CAAC,EAAE;MACpCnB,kBAAkB,CAAC0C,GAAG,CAACvB,OAAO,EAAE,IAAItC,oBAAA,CAAA8D,iBAAiB,CAACxB,OAAO,CAAC,CAAC;IACjE;EACF,CAAC,CAAC;EAEF,IAAIL,iBAAiB,CAAC+E,EAAE,IAAI/E,iBAAiB,CAACK,OAAO,KAAKL,iBAAiB,CAAC+E,EAAE,EAAE;IAC9E7F,kBAAkB,CAACoC,MAAM,CAACtB,iBAAiB,CAACK,OAAO,CAAC;EACtD;EAEA,OAAO,CAACpB,YAAY,EAAEE,OAAO,CAAC;AAChC;AAEA,SAASyD,eAAeA,CAAC1D,kBAAkD;EACzE,KAAK,MAAMc,iBAAiB,IAAId,kBAAkB,CAACe,MAAM,EAAE,EAAE;IAC3D,IAAID,iBAAiB,CAACR,IAAI,KAAK1B,QAAA,CAAAQ,UAAU,CAACiE,SAAS,EAAE;MACnD,OAAOzE,QAAA,CAAA2B,YAAY,CAACkD,qBAAqB;IAC3C;EACF;EAEA,OAAO7E,QAAA,CAAA2B,YAAY,CAAC6C,mBAAmB;AACzC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}