{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ReplaceOneOperation = exports.UpdateManyOperation = exports.UpdateOneOperation = exports.UpdateOperation = void 0;\nexports.makeUpdateStatement = makeUpdateStatement;\nconst error_1 = require(\"../error\");\nconst utils_1 = require(\"../utils\");\nconst command_1 = require(\"./command\");\nconst operation_1 = require(\"./operation\");\n/**\n * @internal\n * UpdateOperation is used in bulk write, while UpdateOneOperation and UpdateManyOperation are only used in the collections API\n */\nclass UpdateOperation extends command_1.CommandOperation {\n  constructor(ns, statements, options) {\n    super(undefined, options);\n    this.options = options;\n    this.ns = ns;\n    this.statements = statements;\n  }\n  get commandName() {\n    return 'update';\n  }\n  get canRetryWrite() {\n    if (super.canRetryWrite === false) {\n      return false;\n    }\n    return this.statements.every(op => op.multi == null || op.multi === false);\n  }\n  async execute(server, session, timeoutContext) {\n    const options = this.options ?? {};\n    const ordered = typeof options.ordered === 'boolean' ? options.ordered : true;\n    const command = {\n      update: this.ns.collection,\n      updates: this.statements,\n      ordered\n    };\n    if (typeof options.bypassDocumentValidation === 'boolean') {\n      command.bypassDocumentValidation = options.bypassDocumentValidation;\n    }\n    if (options.let) {\n      command.let = options.let;\n    }\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (options.comment !== undefined) {\n      command.comment = options.comment;\n    }\n    const unacknowledgedWrite = this.writeConcern && this.writeConcern.w === 0;\n    if (unacknowledgedWrite) {\n      if (this.statements.find(o => o.hint)) {\n        // TODO(NODE-3541): fix error for hint with unacknowledged writes\n        throw new error_1.MongoCompatibilityError(`hint is not supported with unacknowledged writes`);\n      }\n    }\n    const res = await super.executeCommand(server, session, command, timeoutContext);\n    return res;\n  }\n}\nexports.UpdateOperation = UpdateOperation;\n/** @internal */\nclass UpdateOneOperation extends UpdateOperation {\n  constructor(collection, filter, update, options) {\n    super(collection.s.namespace, [makeUpdateStatement(filter, update, {\n      ...options,\n      multi: false\n    })], options);\n    if (!(0, utils_1.hasAtomicOperators)(update)) {\n      throw new error_1.MongoInvalidArgumentError('Update document requires atomic operators');\n    }\n  }\n  async execute(server, session, timeoutContext) {\n    const res = await super.execute(server, session, timeoutContext);\n    if (this.explain != null) return res;\n    if (res.code) throw new error_1.MongoServerError(res);\n    if (res.writeErrors) throw new error_1.MongoServerError(res.writeErrors[0]);\n    return {\n      acknowledged: this.writeConcern?.w !== 0,\n      modifiedCount: res.nModified ?? res.n,\n      upsertedId: Array.isArray(res.upserted) && res.upserted.length > 0 ? res.upserted[0]._id : null,\n      upsertedCount: Array.isArray(res.upserted) && res.upserted.length ? res.upserted.length : 0,\n      matchedCount: Array.isArray(res.upserted) && res.upserted.length > 0 ? 0 : res.n\n    };\n  }\n}\nexports.UpdateOneOperation = UpdateOneOperation;\n/** @internal */\nclass UpdateManyOperation extends UpdateOperation {\n  constructor(collection, filter, update, options) {\n    super(collection.s.namespace, [makeUpdateStatement(filter, update, {\n      ...options,\n      multi: true\n    })], options);\n    if (!(0, utils_1.hasAtomicOperators)(update)) {\n      throw new error_1.MongoInvalidArgumentError('Update document requires atomic operators');\n    }\n  }\n  async execute(server, session, timeoutContext) {\n    const res = await super.execute(server, session, timeoutContext);\n    if (this.explain != null) return res;\n    if (res.code) throw new error_1.MongoServerError(res);\n    if (res.writeErrors) throw new error_1.MongoServerError(res.writeErrors[0]);\n    return {\n      acknowledged: this.writeConcern?.w !== 0,\n      modifiedCount: res.nModified ?? res.n,\n      upsertedId: Array.isArray(res.upserted) && res.upserted.length > 0 ? res.upserted[0]._id : null,\n      upsertedCount: Array.isArray(res.upserted) && res.upserted.length ? res.upserted.length : 0,\n      matchedCount: Array.isArray(res.upserted) && res.upserted.length > 0 ? 0 : res.n\n    };\n  }\n}\nexports.UpdateManyOperation = UpdateManyOperation;\n/** @internal */\nclass ReplaceOneOperation extends UpdateOperation {\n  constructor(collection, filter, replacement, options) {\n    super(collection.s.namespace, [makeUpdateStatement(filter, replacement, {\n      ...options,\n      multi: false\n    })], options);\n    if ((0, utils_1.hasAtomicOperators)(replacement)) {\n      throw new error_1.MongoInvalidArgumentError('Replacement document must not contain atomic operators');\n    }\n  }\n  async execute(server, session, timeoutContext) {\n    const res = await super.execute(server, session, timeoutContext);\n    if (this.explain != null) return res;\n    if (res.code) throw new error_1.MongoServerError(res);\n    if (res.writeErrors) throw new error_1.MongoServerError(res.writeErrors[0]);\n    return {\n      acknowledged: this.writeConcern?.w !== 0,\n      modifiedCount: res.nModified ?? res.n,\n      upsertedId: Array.isArray(res.upserted) && res.upserted.length > 0 ? res.upserted[0]._id : null,\n      upsertedCount: Array.isArray(res.upserted) && res.upserted.length ? res.upserted.length : 0,\n      matchedCount: Array.isArray(res.upserted) && res.upserted.length > 0 ? 0 : res.n\n    };\n  }\n}\nexports.ReplaceOneOperation = ReplaceOneOperation;\nfunction makeUpdateStatement(filter, update, options) {\n  if (filter == null || typeof filter !== 'object') {\n    throw new error_1.MongoInvalidArgumentError('Selector must be a valid JavaScript object');\n  }\n  if (update == null || typeof update !== 'object') {\n    throw new error_1.MongoInvalidArgumentError('Document must be a valid JavaScript object');\n  }\n  const op = {\n    q: filter,\n    u: update\n  };\n  if (typeof options.upsert === 'boolean') {\n    op.upsert = options.upsert;\n  }\n  if (options.multi) {\n    op.multi = options.multi;\n  }\n  if (options.hint) {\n    op.hint = options.hint;\n  }\n  if (options.arrayFilters) {\n    op.arrayFilters = options.arrayFilters;\n  }\n  if (options.collation) {\n    op.collation = options.collation;\n  }\n  return op;\n}\n(0, operation_1.defineAspects)(UpdateOperation, [operation_1.Aspect.RETRYABLE, operation_1.Aspect.WRITE_OPERATION, operation_1.Aspect.SKIP_COLLATION]);\n(0, operation_1.defineAspects)(UpdateOneOperation, [operation_1.Aspect.RETRYABLE, operation_1.Aspect.WRITE_OPERATION, operation_1.Aspect.EXPLAINABLE, operation_1.Aspect.SKIP_COLLATION]);\n(0, operation_1.defineAspects)(UpdateManyOperation, [operation_1.Aspect.WRITE_OPERATION, operation_1.Aspect.EXPLAINABLE, operation_1.Aspect.SKIP_COLLATION]);\n(0, operation_1.defineAspects)(ReplaceOneOperation, [operation_1.Aspect.RETRYABLE, operation_1.Aspect.WRITE_OPERATION, operation_1.Aspect.SKIP_COLLATION]);", "map": {"version": 3, "names": ["exports", "makeUpdateStatement", "error_1", "require", "utils_1", "command_1", "operation_1", "UpdateOperation", "CommandOperation", "constructor", "ns", "statements", "options", "undefined", "commandName", "canRetryWrite", "every", "op", "multi", "execute", "server", "session", "timeoutContext", "ordered", "command", "update", "collection", "updates", "bypassDocumentValidation", "let", "comment", "unacknowledgedWrite", "writeConcern", "w", "find", "o", "hint", "MongoCompatibilityError", "res", "executeCommand", "UpdateOneOperation", "filter", "s", "namespace", "hasAtomicOperators", "MongoInvalidArgumentError", "explain", "code", "MongoServerError", "writeErrors", "acknowledged", "modifiedCount", "nModified", "n", "upsertedId", "Array", "isArray", "upserted", "length", "_id", "upsertedCount", "matchedCount", "UpdateManyOperation", "ReplaceOneOperation", "replacement", "q", "u", "upsert", "arrayFilters", "collation", "defineAspects", "Aspect", "RETRYABLE", "WRITE_OPERATION", "SKIP_COLLATION", "EXPLAINABLE"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\update.ts"], "sourcesContent": ["import type { Document } from '../bson';\nimport type { Collection } from '../collection';\nimport { MongoCompatibilityError, MongoInvalidArgumentError, MongoServerError } from '../error';\nimport type { InferIdType, TODO_NODE_3286 } from '../mongo_types';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { hasAtomicOperators, type MongoDBNamespace } from '../utils';\nimport { type CollationOptions, CommandOperation, type CommandOperationOptions } from './command';\nimport { Aspect, defineAspects, type Hint } from './operation';\n\n/** @public */\nexport interface UpdateOptions extends CommandOperationOptions {\n  /** A set of filters specifying to which array elements an update should apply */\n  arrayFilters?: Document[];\n  /** If true, allows the write to opt-out of document level validation */\n  bypassDocumentValidation?: boolean;\n  /** Specifies a collation */\n  collation?: CollationOptions;\n  /** Specify that the update query should only consider plans using the hinted index */\n  hint?: Hint;\n  /** When true, creates a new document if no document matches the query */\n  upsert?: boolean;\n  /** Map of parameter names and values that can be accessed using $$var (requires MongoDB 5.0). */\n  let?: Document;\n}\n\n/**\n * @public\n * `TSchema` is the schema of the collection\n */\nexport interface UpdateResult<TSchema extends Document = Document> {\n  /** Indicates whether this write result was acknowledged. If not, then all other members of this result will be undefined */\n  acknowledged: boolean;\n  /** The number of documents that matched the filter */\n  matchedCount: number;\n  /** The number of documents that were modified */\n  modifiedCount: number;\n  /** The number of documents that were upserted */\n  upsertedCount: number;\n  /** The identifier of the inserted document if an upsert took place */\n  upsertedId: InferIdType<TSchema> | null;\n}\n\n/** @public */\nexport interface UpdateStatement {\n  /** The query that matches documents to update. */\n  q: Document;\n  /** The modifications to apply. */\n  u: Document | Document[];\n  /**  If true, perform an insert if no documents match the query. */\n  upsert?: boolean;\n  /** If true, updates all documents that meet the query criteria. */\n  multi?: boolean;\n  /** Specifies the collation to use for the operation. */\n  collation?: CollationOptions;\n  /** An array of filter documents that determines which array elements to modify for an update operation on an array field. */\n  arrayFilters?: Document[];\n  /** A document or string that specifies the index to use to support the query predicate. */\n  hint?: Hint;\n}\n\n/**\n * @internal\n * UpdateOperation is used in bulk write, while UpdateOneOperation and UpdateManyOperation are only used in the collections API\n */\nexport class UpdateOperation extends CommandOperation<Document> {\n  override options: UpdateOptions & { ordered?: boolean };\n  statements: UpdateStatement[];\n\n  constructor(\n    ns: MongoDBNamespace,\n    statements: UpdateStatement[],\n    options: UpdateOptions & { ordered?: boolean }\n  ) {\n    super(undefined, options);\n    this.options = options;\n    this.ns = ns;\n\n    this.statements = statements;\n  }\n\n  override get commandName() {\n    return 'update' as const;\n  }\n\n  override get canRetryWrite(): boolean {\n    if (super.canRetryWrite === false) {\n      return false;\n    }\n\n    return this.statements.every(op => op.multi == null || op.multi === false);\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<Document> {\n    const options = this.options ?? {};\n    const ordered = typeof options.ordered === 'boolean' ? options.ordered : true;\n    const command: Document = {\n      update: this.ns.collection,\n      updates: this.statements,\n      ordered\n    };\n\n    if (typeof options.bypassDocumentValidation === 'boolean') {\n      command.bypassDocumentValidation = options.bypassDocumentValidation;\n    }\n\n    if (options.let) {\n      command.let = options.let;\n    }\n\n    // we check for undefined specifically here to allow falsy values\n    // eslint-disable-next-line no-restricted-syntax\n    if (options.comment !== undefined) {\n      command.comment = options.comment;\n    }\n\n    const unacknowledgedWrite = this.writeConcern && this.writeConcern.w === 0;\n    if (unacknowledgedWrite) {\n      if (this.statements.find((o: Document) => o.hint)) {\n        // TODO(NODE-3541): fix error for hint with unacknowledged writes\n        throw new MongoCompatibilityError(`hint is not supported with unacknowledged writes`);\n      }\n    }\n\n    const res = await super.executeCommand(server, session, command, timeoutContext);\n    return res;\n  }\n}\n\n/** @internal */\nexport class UpdateOneOperation extends UpdateOperation {\n  constructor(collection: Collection, filter: Document, update: Document, options: UpdateOptions) {\n    super(\n      collection.s.namespace,\n      [makeUpdateStatement(filter, update, { ...options, multi: false })],\n      options\n    );\n\n    if (!hasAtomicOperators(update)) {\n      throw new MongoInvalidArgumentError('Update document requires atomic operators');\n    }\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<UpdateResult> {\n    const res: TODO_NODE_3286 = await super.execute(server, session, timeoutContext);\n    if (this.explain != null) return res;\n    if (res.code) throw new MongoServerError(res);\n    if (res.writeErrors) throw new MongoServerError(res.writeErrors[0]);\n\n    return {\n      acknowledged: this.writeConcern?.w !== 0,\n      modifiedCount: res.nModified ?? res.n,\n      upsertedId:\n        Array.isArray(res.upserted) && res.upserted.length > 0 ? res.upserted[0]._id : null,\n      upsertedCount: Array.isArray(res.upserted) && res.upserted.length ? res.upserted.length : 0,\n      matchedCount: Array.isArray(res.upserted) && res.upserted.length > 0 ? 0 : res.n\n    };\n  }\n}\n\n/** @internal */\nexport class UpdateManyOperation extends UpdateOperation {\n  constructor(collection: Collection, filter: Document, update: Document, options: UpdateOptions) {\n    super(\n      collection.s.namespace,\n      [makeUpdateStatement(filter, update, { ...options, multi: true })],\n      options\n    );\n\n    if (!hasAtomicOperators(update)) {\n      throw new MongoInvalidArgumentError('Update document requires atomic operators');\n    }\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<UpdateResult> {\n    const res: TODO_NODE_3286 = await super.execute(server, session, timeoutContext);\n    if (this.explain != null) return res;\n    if (res.code) throw new MongoServerError(res);\n    if (res.writeErrors) throw new MongoServerError(res.writeErrors[0]);\n\n    return {\n      acknowledged: this.writeConcern?.w !== 0,\n      modifiedCount: res.nModified ?? res.n,\n      upsertedId:\n        Array.isArray(res.upserted) && res.upserted.length > 0 ? res.upserted[0]._id : null,\n      upsertedCount: Array.isArray(res.upserted) && res.upserted.length ? res.upserted.length : 0,\n      matchedCount: Array.isArray(res.upserted) && res.upserted.length > 0 ? 0 : res.n\n    };\n  }\n}\n\n/** @public */\nexport interface ReplaceOptions extends CommandOperationOptions {\n  /** If true, allows the write to opt-out of document level validation */\n  bypassDocumentValidation?: boolean;\n  /** Specifies a collation */\n  collation?: CollationOptions;\n  /** Specify that the update query should only consider plans using the hinted index */\n  hint?: string | Document;\n  /** When true, creates a new document if no document matches the query */\n  upsert?: boolean;\n  /** Map of parameter names and values that can be accessed using $$var (requires MongoDB 5.0). */\n  let?: Document;\n}\n\n/** @internal */\nexport class ReplaceOneOperation extends UpdateOperation {\n  constructor(\n    collection: Collection,\n    filter: Document,\n    replacement: Document,\n    options: ReplaceOptions\n  ) {\n    super(\n      collection.s.namespace,\n      [makeUpdateStatement(filter, replacement, { ...options, multi: false })],\n      options\n    );\n\n    if (hasAtomicOperators(replacement)) {\n      throw new MongoInvalidArgumentError('Replacement document must not contain atomic operators');\n    }\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<UpdateResult> {\n    const res: TODO_NODE_3286 = await super.execute(server, session, timeoutContext);\n    if (this.explain != null) return res;\n    if (res.code) throw new MongoServerError(res);\n    if (res.writeErrors) throw new MongoServerError(res.writeErrors[0]);\n\n    return {\n      acknowledged: this.writeConcern?.w !== 0,\n      modifiedCount: res.nModified ?? res.n,\n      upsertedId:\n        Array.isArray(res.upserted) && res.upserted.length > 0 ? res.upserted[0]._id : null,\n      upsertedCount: Array.isArray(res.upserted) && res.upserted.length ? res.upserted.length : 0,\n      matchedCount: Array.isArray(res.upserted) && res.upserted.length > 0 ? 0 : res.n\n    };\n  }\n}\n\nexport function makeUpdateStatement(\n  filter: Document,\n  update: Document | Document[],\n  options: UpdateOptions & { multi?: boolean }\n): UpdateStatement {\n  if (filter == null || typeof filter !== 'object') {\n    throw new MongoInvalidArgumentError('Selector must be a valid JavaScript object');\n  }\n\n  if (update == null || typeof update !== 'object') {\n    throw new MongoInvalidArgumentError('Document must be a valid JavaScript object');\n  }\n\n  const op: UpdateStatement = { q: filter, u: update };\n  if (typeof options.upsert === 'boolean') {\n    op.upsert = options.upsert;\n  }\n\n  if (options.multi) {\n    op.multi = options.multi;\n  }\n\n  if (options.hint) {\n    op.hint = options.hint;\n  }\n\n  if (options.arrayFilters) {\n    op.arrayFilters = options.arrayFilters;\n  }\n\n  if (options.collation) {\n    op.collation = options.collation;\n  }\n\n  return op;\n}\n\ndefineAspects(UpdateOperation, [Aspect.RETRYABLE, Aspect.WRITE_OPERATION, Aspect.SKIP_COLLATION]);\ndefineAspects(UpdateOneOperation, [\n  Aspect.RETRYABLE,\n  Aspect.WRITE_OPERATION,\n  Aspect.EXPLAINABLE,\n  Aspect.SKIP_COLLATION\n]);\ndefineAspects(UpdateManyOperation, [\n  Aspect.WRITE_OPERATION,\n  Aspect.EXPLAINABLE,\n  Aspect.SKIP_COLLATION\n]);\ndefineAspects(ReplaceOneOperation, [\n  Aspect.RETRYABLE,\n  Aspect.WRITE_OPERATION,\n  Aspect.SKIP_COLLATION\n]);\n"], "mappings": ";;;;;;AAkQAA,OAAA,CAAAC,mBAAA,GAAAA,mBAAA;AAhQA,MAAAC,OAAA,GAAAC,OAAA;AAKA,MAAAC,OAAA,GAAAD,OAAA;AACA,MAAAE,SAAA,GAAAF,OAAA;AACA,MAAAG,WAAA,GAAAH,OAAA;AAqDA;;;;AAIA,MAAaI,eAAgB,SAAQF,SAAA,CAAAG,gBAA0B;EAI7DC,YACEC,EAAoB,EACpBC,UAA6B,EAC7BC,OAA8C;IAE9C,KAAK,CAACC,SAAS,EAAED,OAAO,CAAC;IACzB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACF,EAAE,GAAGA,EAAE;IAEZ,IAAI,CAACC,UAAU,GAAGA,UAAU;EAC9B;EAEA,IAAaG,WAAWA,CAAA;IACtB,OAAO,QAAiB;EAC1B;EAEA,IAAaC,aAAaA,CAAA;IACxB,IAAI,KAAK,CAACA,aAAa,KAAK,KAAK,EAAE;MACjC,OAAO,KAAK;IACd;IAEA,OAAO,IAAI,CAACJ,UAAU,CAACK,KAAK,CAACC,EAAE,IAAIA,EAAE,CAACC,KAAK,IAAI,IAAI,IAAID,EAAE,CAACC,KAAK,KAAK,KAAK,CAAC;EAC5E;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMV,OAAO,GAAG,IAAI,CAACA,OAAO,IAAI,EAAE;IAClC,MAAMW,OAAO,GAAG,OAAOX,OAAO,CAACW,OAAO,KAAK,SAAS,GAAGX,OAAO,CAACW,OAAO,GAAG,IAAI;IAC7E,MAAMC,OAAO,GAAa;MACxBC,MAAM,EAAE,IAAI,CAACf,EAAE,CAACgB,UAAU;MAC1BC,OAAO,EAAE,IAAI,CAAChB,UAAU;MACxBY;KACD;IAED,IAAI,OAAOX,OAAO,CAACgB,wBAAwB,KAAK,SAAS,EAAE;MACzDJ,OAAO,CAACI,wBAAwB,GAAGhB,OAAO,CAACgB,wBAAwB;IACrE;IAEA,IAAIhB,OAAO,CAACiB,GAAG,EAAE;MACfL,OAAO,CAACK,GAAG,GAAGjB,OAAO,CAACiB,GAAG;IAC3B;IAEA;IACA;IACA,IAAIjB,OAAO,CAACkB,OAAO,KAAKjB,SAAS,EAAE;MACjCW,OAAO,CAACM,OAAO,GAAGlB,OAAO,CAACkB,OAAO;IACnC;IAEA,MAAMC,mBAAmB,GAAG,IAAI,CAACC,YAAY,IAAI,IAAI,CAACA,YAAY,CAACC,CAAC,KAAK,CAAC;IAC1E,IAAIF,mBAAmB,EAAE;MACvB,IAAI,IAAI,CAACpB,UAAU,CAACuB,IAAI,CAAEC,CAAW,IAAKA,CAAC,CAACC,IAAI,CAAC,EAAE;QACjD;QACA,MAAM,IAAIlC,OAAA,CAAAmC,uBAAuB,CAAC,kDAAkD,CAAC;MACvF;IACF;IAEA,MAAMC,GAAG,GAAG,MAAM,KAAK,CAACC,cAAc,CAACnB,MAAM,EAAEC,OAAO,EAAEG,OAAO,EAAEF,cAAc,CAAC;IAChF,OAAOgB,GAAG;EACZ;;AAjEFtC,OAAA,CAAAO,eAAA,GAAAA,eAAA;AAoEA;AACA,MAAaiC,kBAAmB,SAAQjC,eAAe;EACrDE,YAAYiB,UAAsB,EAAEe,MAAgB,EAAEhB,MAAgB,EAAEb,OAAsB;IAC5F,KAAK,CACHc,UAAU,CAACgB,CAAC,CAACC,SAAS,EACtB,CAAC1C,mBAAmB,CAACwC,MAAM,EAAEhB,MAAM,EAAE;MAAE,GAAGb,OAAO;MAAEM,KAAK,EAAE;IAAK,CAAE,CAAC,CAAC,EACnEN,OAAO,CACR;IAED,IAAI,CAAC,IAAAR,OAAA,CAAAwC,kBAAkB,EAACnB,MAAM,CAAC,EAAE;MAC/B,MAAM,IAAIvB,OAAA,CAAA2C,yBAAyB,CAAC,2CAA2C,CAAC;IAClF;EACF;EAES,MAAM1B,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMgB,GAAG,GAAmB,MAAM,KAAK,CAACnB,OAAO,CAACC,MAAM,EAAEC,OAAO,EAAEC,cAAc,CAAC;IAChF,IAAI,IAAI,CAACwB,OAAO,IAAI,IAAI,EAAE,OAAOR,GAAG;IACpC,IAAIA,GAAG,CAACS,IAAI,EAAE,MAAM,IAAI7C,OAAA,CAAA8C,gBAAgB,CAACV,GAAG,CAAC;IAC7C,IAAIA,GAAG,CAACW,WAAW,EAAE,MAAM,IAAI/C,OAAA,CAAA8C,gBAAgB,CAACV,GAAG,CAACW,WAAW,CAAC,CAAC,CAAC,CAAC;IAEnE,OAAO;MACLC,YAAY,EAAE,IAAI,CAAClB,YAAY,EAAEC,CAAC,KAAK,CAAC;MACxCkB,aAAa,EAAEb,GAAG,CAACc,SAAS,IAAId,GAAG,CAACe,CAAC;MACrCC,UAAU,EACRC,KAAK,CAACC,OAAO,CAAClB,GAAG,CAACmB,QAAQ,CAAC,IAAInB,GAAG,CAACmB,QAAQ,CAACC,MAAM,GAAG,CAAC,GAAGpB,GAAG,CAACmB,QAAQ,CAAC,CAAC,CAAC,CAACE,GAAG,GAAG,IAAI;MACrFC,aAAa,EAAEL,KAAK,CAACC,OAAO,CAAClB,GAAG,CAACmB,QAAQ,CAAC,IAAInB,GAAG,CAACmB,QAAQ,CAACC,MAAM,GAAGpB,GAAG,CAACmB,QAAQ,CAACC,MAAM,GAAG,CAAC;MAC3FG,YAAY,EAAEN,KAAK,CAACC,OAAO,CAAClB,GAAG,CAACmB,QAAQ,CAAC,IAAInB,GAAG,CAACmB,QAAQ,CAACC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAGpB,GAAG,CAACe;KAChF;EACH;;AA/BFrD,OAAA,CAAAwC,kBAAA,GAAAA,kBAAA;AAkCA;AACA,MAAasB,mBAAoB,SAAQvD,eAAe;EACtDE,YAAYiB,UAAsB,EAAEe,MAAgB,EAAEhB,MAAgB,EAAEb,OAAsB;IAC5F,KAAK,CACHc,UAAU,CAACgB,CAAC,CAACC,SAAS,EACtB,CAAC1C,mBAAmB,CAACwC,MAAM,EAAEhB,MAAM,EAAE;MAAE,GAAGb,OAAO;MAAEM,KAAK,EAAE;IAAI,CAAE,CAAC,CAAC,EAClEN,OAAO,CACR;IAED,IAAI,CAAC,IAAAR,OAAA,CAAAwC,kBAAkB,EAACnB,MAAM,CAAC,EAAE;MAC/B,MAAM,IAAIvB,OAAA,CAAA2C,yBAAyB,CAAC,2CAA2C,CAAC;IAClF;EACF;EAES,MAAM1B,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMgB,GAAG,GAAmB,MAAM,KAAK,CAACnB,OAAO,CAACC,MAAM,EAAEC,OAAO,EAAEC,cAAc,CAAC;IAChF,IAAI,IAAI,CAACwB,OAAO,IAAI,IAAI,EAAE,OAAOR,GAAG;IACpC,IAAIA,GAAG,CAACS,IAAI,EAAE,MAAM,IAAI7C,OAAA,CAAA8C,gBAAgB,CAACV,GAAG,CAAC;IAC7C,IAAIA,GAAG,CAACW,WAAW,EAAE,MAAM,IAAI/C,OAAA,CAAA8C,gBAAgB,CAACV,GAAG,CAACW,WAAW,CAAC,CAAC,CAAC,CAAC;IAEnE,OAAO;MACLC,YAAY,EAAE,IAAI,CAAClB,YAAY,EAAEC,CAAC,KAAK,CAAC;MACxCkB,aAAa,EAAEb,GAAG,CAACc,SAAS,IAAId,GAAG,CAACe,CAAC;MACrCC,UAAU,EACRC,KAAK,CAACC,OAAO,CAAClB,GAAG,CAACmB,QAAQ,CAAC,IAAInB,GAAG,CAACmB,QAAQ,CAACC,MAAM,GAAG,CAAC,GAAGpB,GAAG,CAACmB,QAAQ,CAAC,CAAC,CAAC,CAACE,GAAG,GAAG,IAAI;MACrFC,aAAa,EAAEL,KAAK,CAACC,OAAO,CAAClB,GAAG,CAACmB,QAAQ,CAAC,IAAInB,GAAG,CAACmB,QAAQ,CAACC,MAAM,GAAGpB,GAAG,CAACmB,QAAQ,CAACC,MAAM,GAAG,CAAC;MAC3FG,YAAY,EAAEN,KAAK,CAACC,OAAO,CAAClB,GAAG,CAACmB,QAAQ,CAAC,IAAInB,GAAG,CAACmB,QAAQ,CAACC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAGpB,GAAG,CAACe;KAChF;EACH;;AA/BFrD,OAAA,CAAA8D,mBAAA,GAAAA,mBAAA;AAgDA;AACA,MAAaC,mBAAoB,SAAQxD,eAAe;EACtDE,YACEiB,UAAsB,EACtBe,MAAgB,EAChBuB,WAAqB,EACrBpD,OAAuB;IAEvB,KAAK,CACHc,UAAU,CAACgB,CAAC,CAACC,SAAS,EACtB,CAAC1C,mBAAmB,CAACwC,MAAM,EAAEuB,WAAW,EAAE;MAAE,GAAGpD,OAAO;MAAEM,KAAK,EAAE;IAAK,CAAE,CAAC,CAAC,EACxEN,OAAO,CACR;IAED,IAAI,IAAAR,OAAA,CAAAwC,kBAAkB,EAACoB,WAAW,CAAC,EAAE;MACnC,MAAM,IAAI9D,OAAA,CAAA2C,yBAAyB,CAAC,wDAAwD,CAAC;IAC/F;EACF;EAES,MAAM1B,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAMgB,GAAG,GAAmB,MAAM,KAAK,CAACnB,OAAO,CAACC,MAAM,EAAEC,OAAO,EAAEC,cAAc,CAAC;IAChF,IAAI,IAAI,CAACwB,OAAO,IAAI,IAAI,EAAE,OAAOR,GAAG;IACpC,IAAIA,GAAG,CAACS,IAAI,EAAE,MAAM,IAAI7C,OAAA,CAAA8C,gBAAgB,CAACV,GAAG,CAAC;IAC7C,IAAIA,GAAG,CAACW,WAAW,EAAE,MAAM,IAAI/C,OAAA,CAAA8C,gBAAgB,CAACV,GAAG,CAACW,WAAW,CAAC,CAAC,CAAC,CAAC;IAEnE,OAAO;MACLC,YAAY,EAAE,IAAI,CAAClB,YAAY,EAAEC,CAAC,KAAK,CAAC;MACxCkB,aAAa,EAAEb,GAAG,CAACc,SAAS,IAAId,GAAG,CAACe,CAAC;MACrCC,UAAU,EACRC,KAAK,CAACC,OAAO,CAAClB,GAAG,CAACmB,QAAQ,CAAC,IAAInB,GAAG,CAACmB,QAAQ,CAACC,MAAM,GAAG,CAAC,GAAGpB,GAAG,CAACmB,QAAQ,CAAC,CAAC,CAAC,CAACE,GAAG,GAAG,IAAI;MACrFC,aAAa,EAAEL,KAAK,CAACC,OAAO,CAAClB,GAAG,CAACmB,QAAQ,CAAC,IAAInB,GAAG,CAACmB,QAAQ,CAACC,MAAM,GAAGpB,GAAG,CAACmB,QAAQ,CAACC,MAAM,GAAG,CAAC;MAC3FG,YAAY,EAAEN,KAAK,CAACC,OAAO,CAAClB,GAAG,CAACmB,QAAQ,CAAC,IAAInB,GAAG,CAACmB,QAAQ,CAACC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAGpB,GAAG,CAACe;KAChF;EACH;;AApCFrD,OAAA,CAAA+D,mBAAA,GAAAA,mBAAA;AAuCA,SAAgB9D,mBAAmBA,CACjCwC,MAAgB,EAChBhB,MAA6B,EAC7Bb,OAA4C;EAE5C,IAAI6B,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAChD,MAAM,IAAIvC,OAAA,CAAA2C,yBAAyB,CAAC,4CAA4C,CAAC;EACnF;EAEA,IAAIpB,MAAM,IAAI,IAAI,IAAI,OAAOA,MAAM,KAAK,QAAQ,EAAE;IAChD,MAAM,IAAIvB,OAAA,CAAA2C,yBAAyB,CAAC,4CAA4C,CAAC;EACnF;EAEA,MAAM5B,EAAE,GAAoB;IAAEgD,CAAC,EAAExB,MAAM;IAAEyB,CAAC,EAAEzC;EAAM,CAAE;EACpD,IAAI,OAAOb,OAAO,CAACuD,MAAM,KAAK,SAAS,EAAE;IACvClD,EAAE,CAACkD,MAAM,GAAGvD,OAAO,CAACuD,MAAM;EAC5B;EAEA,IAAIvD,OAAO,CAACM,KAAK,EAAE;IACjBD,EAAE,CAACC,KAAK,GAAGN,OAAO,CAACM,KAAK;EAC1B;EAEA,IAAIN,OAAO,CAACwB,IAAI,EAAE;IAChBnB,EAAE,CAACmB,IAAI,GAAGxB,OAAO,CAACwB,IAAI;EACxB;EAEA,IAAIxB,OAAO,CAACwD,YAAY,EAAE;IACxBnD,EAAE,CAACmD,YAAY,GAAGxD,OAAO,CAACwD,YAAY;EACxC;EAEA,IAAIxD,OAAO,CAACyD,SAAS,EAAE;IACrBpD,EAAE,CAACoD,SAAS,GAAGzD,OAAO,CAACyD,SAAS;EAClC;EAEA,OAAOpD,EAAE;AACX;AAEA,IAAAX,WAAA,CAAAgE,aAAa,EAAC/D,eAAe,EAAE,CAACD,WAAA,CAAAiE,MAAM,CAACC,SAAS,EAAElE,WAAA,CAAAiE,MAAM,CAACE,eAAe,EAAEnE,WAAA,CAAAiE,MAAM,CAACG,cAAc,CAAC,CAAC;AACjG,IAAApE,WAAA,CAAAgE,aAAa,EAAC9B,kBAAkB,EAAE,CAChClC,WAAA,CAAAiE,MAAM,CAACC,SAAS,EAChBlE,WAAA,CAAAiE,MAAM,CAACE,eAAe,EACtBnE,WAAA,CAAAiE,MAAM,CAACI,WAAW,EAClBrE,WAAA,CAAAiE,MAAM,CAACG,cAAc,CACtB,CAAC;AACF,IAAApE,WAAA,CAAAgE,aAAa,EAACR,mBAAmB,EAAE,CACjCxD,WAAA,CAAAiE,MAAM,CAACE,eAAe,EACtBnE,WAAA,CAAAiE,MAAM,CAACI,WAAW,EAClBrE,WAAA,CAAAiE,MAAM,CAACG,cAAc,CACtB,CAAC;AACF,IAAApE,WAAA,CAAAgE,aAAa,EAACP,mBAAmB,EAAE,CACjCzD,WAAA,CAAAiE,MAAM,CAACC,SAAS,EAChBlE,WAAA,CAAAiE,MAAM,CAACE,eAAe,EACtBnE,WAAA,CAAAiE,MAAM,CAACG,cAAc,CACtB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}