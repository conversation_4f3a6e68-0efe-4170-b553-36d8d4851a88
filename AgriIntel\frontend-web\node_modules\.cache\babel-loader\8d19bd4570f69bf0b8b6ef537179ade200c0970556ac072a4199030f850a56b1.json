{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.TokenCache = void 0;\nconst error_1 = require(\"../../../error\");\nclass MongoOIDCError extends error_1.MongoDriverError {}\n/** @internal */\nclass TokenCache {\n  get hasAccessToken() {\n    return !!this.accessToken;\n  }\n  get hasRefreshToken() {\n    return !!this.refreshToken;\n  }\n  get hasIdpInfo() {\n    return !!this.idpInfo;\n  }\n  getAccessToken() {\n    if (!this.accessToken) {\n      throw new MongoOIDCError('Attempted to get an access token when none exists.');\n    }\n    return this.accessToken;\n  }\n  getRefreshToken() {\n    if (!this.refreshToken) {\n      throw new MongoOIDCError('Attempted to get a refresh token when none exists.');\n    }\n    return this.refreshToken;\n  }\n  getIdpInfo() {\n    if (!this.idpInfo) {\n      throw new MongoOIDCError('Attempted to get IDP information when none exists.');\n    }\n    return this.idpInfo;\n  }\n  put(response, idpInfo) {\n    this.accessToken = response.accessToken;\n    this.refreshToken = response.refreshToken;\n    this.expiresInSeconds = response.expiresInSeconds;\n    if (idpInfo) {\n      this.idpInfo = idpInfo;\n    }\n  }\n  removeAccessToken() {\n    this.accessToken = undefined;\n  }\n  removeRefreshToken() {\n    this.refreshToken = undefined;\n  }\n}\nexports.TokenCache = TokenCache;", "map": {"version": 3, "names": ["error_1", "require", "MongoOIDCError", "MongoDriverError", "TokenCache", "hasAccessToken", "accessToken", "hasRefreshToken", "refreshToken", "hasIdpInfo", "idpInfo", "getAccessToken", "getRefreshToken", "getIdpInfo", "put", "response", "expiresInSeconds", "removeAccessToken", "undefined", "removeRefreshToken", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\auth\\mongodb_oidc\\token_cache.ts"], "sourcesContent": ["import { MongoDriverError } from '../../../error';\nimport type { IdPInfo, OIDCResponse } from '../mongodb_oidc';\n\nclass MongoOIDCError extends MongoDriverError {}\n\n/** @internal */\nexport class TokenCache {\n  private accessToken?: string;\n  private refreshToken?: string;\n  private idpInfo?: IdPInfo;\n  private expiresInSeconds?: number;\n\n  get hasAccessToken(): boolean {\n    return !!this.accessToken;\n  }\n\n  get hasRefreshToken(): boolean {\n    return !!this.refreshToken;\n  }\n\n  get hasIdpInfo(): boolean {\n    return !!this.idpInfo;\n  }\n\n  getAccessToken(): string {\n    if (!this.accessToken) {\n      throw new MongoOIDCError('Attempted to get an access token when none exists.');\n    }\n    return this.accessToken;\n  }\n\n  getRefreshToken(): string {\n    if (!this.refreshToken) {\n      throw new MongoOIDCError('Attempted to get a refresh token when none exists.');\n    }\n    return this.refreshToken;\n  }\n\n  getIdpInfo(): IdPInfo {\n    if (!this.idpInfo) {\n      throw new MongoOIDCError('Attempted to get IDP information when none exists.');\n    }\n    return this.idpInfo;\n  }\n\n  put(response: OIDCResponse, idpInfo?: IdPInfo) {\n    this.accessToken = response.accessToken;\n    this.refreshToken = response.refreshToken;\n    this.expiresInSeconds = response.expiresInSeconds;\n    if (idpInfo) {\n      this.idpInfo = idpInfo;\n    }\n  }\n\n  removeAccessToken() {\n    this.accessToken = undefined;\n  }\n\n  removeRefreshToken() {\n    this.refreshToken = undefined;\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,OAAA,GAAAC,OAAA;AAGA,MAAMC,cAAe,SAAQF,OAAA,CAAAG,gBAAgB;AAE7C;AACA,MAAaC,UAAU;EAMrB,IAAIC,cAAcA,CAAA;IAChB,OAAO,CAAC,CAAC,IAAI,CAACC,WAAW;EAC3B;EAEA,IAAIC,eAAeA,CAAA;IACjB,OAAO,CAAC,CAAC,IAAI,CAACC,YAAY;EAC5B;EAEA,IAAIC,UAAUA,CAAA;IACZ,OAAO,CAAC,CAAC,IAAI,CAACC,OAAO;EACvB;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACL,WAAW,EAAE;MACrB,MAAM,IAAIJ,cAAc,CAAC,oDAAoD,CAAC;IAChF;IACA,OAAO,IAAI,CAACI,WAAW;EACzB;EAEAM,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACJ,YAAY,EAAE;MACtB,MAAM,IAAIN,cAAc,CAAC,oDAAoD,CAAC;IAChF;IACA,OAAO,IAAI,CAACM,YAAY;EAC1B;EAEAK,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAACH,OAAO,EAAE;MACjB,MAAM,IAAIR,cAAc,CAAC,oDAAoD,CAAC;IAChF;IACA,OAAO,IAAI,CAACQ,OAAO;EACrB;EAEAI,GAAGA,CAACC,QAAsB,EAAEL,OAAiB;IAC3C,IAAI,CAACJ,WAAW,GAAGS,QAAQ,CAACT,WAAW;IACvC,IAAI,CAACE,YAAY,GAAGO,QAAQ,CAACP,YAAY;IACzC,IAAI,CAACQ,gBAAgB,GAAGD,QAAQ,CAACC,gBAAgB;IACjD,IAAIN,OAAO,EAAE;MACX,IAAI,CAACA,OAAO,GAAGA,OAAO;IACxB;EACF;EAEAO,iBAAiBA,CAAA;IACf,IAAI,CAACX,WAAW,GAAGY,SAAS;EAC9B;EAEAC,kBAAkBA,CAAA;IAChB,IAAI,CAACX,YAAY,GAAGU,SAAS;EAC/B;;AAtDFE,OAAA,CAAAhB,UAAA,GAAAA,UAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}