{"ast": null, "code": "import ascending from \"./ascending.js\";\nimport { ascendingDefined, compareDefined } from \"./sort.js\";\nexport default function rank(values, valueof = ascending) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  let V = Array.from(values);\n  const R = new Float64Array(V.length);\n  if (valueof.length !== 2) V = V.map(valueof), valueof = ascending;\n  const compareIndex = (i, j) => valueof(V[i], V[j]);\n  let k, r;\n  values = Uint32Array.from(V, (_, i) => i);\n  // Risky chaining due to Safari 14 https://github.com/d3/d3-array/issues/123\n  values.sort(valueof === ascending ? (i, j) => ascendingDefined(V[i], V[j]) : compareDefined(compareIndex));\n  values.forEach((j, i) => {\n    const c = compareIndex(j, k === undefined ? j : k);\n    if (c >= 0) {\n      if (k === undefined || c > 0) k = j, r = i;\n      R[j] = r;\n    } else {\n      R[j] = NaN;\n    }\n  });\n  return R;\n}", "map": {"version": 3, "names": ["ascending", "ascendingDefined", "compareDefined", "rank", "values", "valueof", "Symbol", "iterator", "TypeError", "V", "Array", "from", "R", "Float64Array", "length", "map", "compareIndex", "i", "j", "k", "r", "Uint32Array", "_", "sort", "for<PERSON>ach", "c", "undefined", "NaN"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/d3-array/src/rank.js"], "sourcesContent": ["import ascending from \"./ascending.js\";\nimport {ascendingDefined, compareDefined} from \"./sort.js\";\n\nexport default function rank(values, valueof = ascending) {\n  if (typeof values[Symbol.iterator] !== \"function\") throw new TypeError(\"values is not iterable\");\n  let V = Array.from(values);\n  const R = new Float64Array(V.length);\n  if (valueof.length !== 2) V = V.map(valueof), valueof = ascending;\n  const compareIndex = (i, j) => valueof(V[i], V[j]);\n  let k, r;\n  values = Uint32Array.from(V, (_, i) => i);\n  // Risky chaining due to Safari 14 https://github.com/d3/d3-array/issues/123\n  values.sort(valueof === ascending ? (i, j) => ascendingDefined(V[i], V[j]) : compareDefined(compareIndex));\n  values.forEach((j, i) => {\n      const c = compareIndex(j, k === undefined ? j : k);\n      if (c >= 0) {\n        if (k === undefined || c > 0) k = j, r = i;\n        R[j] = r;\n      } else {\n        R[j] = NaN;\n      }\n    });\n  return R;\n}\n"], "mappings": "AAAA,OAAOA,SAAS,MAAM,gBAAgB;AACtC,SAAQC,gBAAgB,EAAEC,cAAc,QAAO,WAAW;AAE1D,eAAe,SAASC,IAAIA,CAACC,MAAM,EAAEC,OAAO,GAAGL,SAAS,EAAE;EACxD,IAAI,OAAOI,MAAM,CAACE,MAAM,CAACC,QAAQ,CAAC,KAAK,UAAU,EAAE,MAAM,IAAIC,SAAS,CAAC,wBAAwB,CAAC;EAChG,IAAIC,CAAC,GAAGC,KAAK,CAACC,IAAI,CAACP,MAAM,CAAC;EAC1B,MAAMQ,CAAC,GAAG,IAAIC,YAAY,CAACJ,CAAC,CAACK,MAAM,CAAC;EACpC,IAAIT,OAAO,CAACS,MAAM,KAAK,CAAC,EAAEL,CAAC,GAAGA,CAAC,CAACM,GAAG,CAACV,OAAO,CAAC,EAAEA,OAAO,GAAGL,SAAS;EACjE,MAAMgB,YAAY,GAAGA,CAACC,CAAC,EAAEC,CAAC,KAAKb,OAAO,CAACI,CAAC,CAACQ,CAAC,CAAC,EAAER,CAAC,CAACS,CAAC,CAAC,CAAC;EAClD,IAAIC,CAAC,EAAEC,CAAC;EACRhB,MAAM,GAAGiB,WAAW,CAACV,IAAI,CAACF,CAAC,EAAE,CAACa,CAAC,EAAEL,CAAC,KAAKA,CAAC,CAAC;EACzC;EACAb,MAAM,CAACmB,IAAI,CAAClB,OAAO,KAAKL,SAAS,GAAG,CAACiB,CAAC,EAAEC,CAAC,KAAKjB,gBAAgB,CAACQ,CAAC,CAACQ,CAAC,CAAC,EAAER,CAAC,CAACS,CAAC,CAAC,CAAC,GAAGhB,cAAc,CAACc,YAAY,CAAC,CAAC;EAC1GZ,MAAM,CAACoB,OAAO,CAAC,CAACN,CAAC,EAAED,CAAC,KAAK;IACrB,MAAMQ,CAAC,GAAGT,YAAY,CAACE,CAAC,EAAEC,CAAC,KAAKO,SAAS,GAAGR,CAAC,GAAGC,CAAC,CAAC;IAClD,IAAIM,CAAC,IAAI,CAAC,EAAE;MACV,IAAIN,CAAC,KAAKO,SAAS,IAAID,CAAC,GAAG,CAAC,EAAEN,CAAC,GAAGD,CAAC,EAAEE,CAAC,GAAGH,CAAC;MAC1CL,CAAC,CAACM,CAAC,CAAC,GAAGE,CAAC;IACV,CAAC,MAAM;MACLR,CAAC,CAACM,CAAC,CAAC,GAAGS,GAAG;IACZ;EACF,CAAC,CAAC;EACJ,OAAOf,CAAC;AACV", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}