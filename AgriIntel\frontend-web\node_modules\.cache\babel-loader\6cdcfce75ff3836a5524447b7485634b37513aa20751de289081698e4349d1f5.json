{"ast": null, "code": "\"use strict\";\n\nmodule.exports.STATUS_MAPPING = {\n  mapped: 1,\n  valid: 2,\n  disallowed: 3,\n  deviation: 6,\n  ignored: 7\n};", "map": {"version": 3, "names": ["module", "exports", "STATUS_MAPPING", "mapped", "valid", "disallowed", "deviation", "ignored"], "sources": ["C:/Users/<USER>/node_modules/mongodb-connection-string-url/node_modules/tr46/lib/statusMapping.js"], "sourcesContent": ["\"use strict\";\n\nmodule.exports.STATUS_MAPPING = {\n  mapped: 1,\n  valid: 2,\n  disallowed: 3,\n  deviation: 6,\n  ignored: 7\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,CAACC,cAAc,GAAG;EAC9BC,MAAM,EAAE,CAAC;EACTC,KAAK,EAAE,CAAC;EACRC,UAAU,EAAE,CAAC;EACbC,SAAS,EAAE,CAAC;EACZC,OAAO,EAAE;AACX,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}