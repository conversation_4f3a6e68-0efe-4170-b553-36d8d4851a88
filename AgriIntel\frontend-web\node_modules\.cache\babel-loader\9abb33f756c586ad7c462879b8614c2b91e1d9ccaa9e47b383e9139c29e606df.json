{"ast": null, "code": "import { useEffect as l, useRef as i } from \"react\";\nimport { useEvent as r } from './use-event.js';\nlet u = Symbol();\nfunction T(t) {\n  let n = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : !0;\n  return Object.assign(t, {\n    [u]: n\n  });\n}\nfunction y() {\n  for (var _len = arguments.length, t = new Array(_len), _key = 0; _key < _len; _key++) {\n    t[_key] = arguments[_key];\n  }\n  let n = i(t);\n  l(() => {\n    n.current = t;\n  }, [t]);\n  let c = r(e => {\n    for (let o of n.current) o != null && (typeof o == \"function\" ? o(e) : o.current = e);\n  });\n  return t.every(e => e == null || (e == null ? void 0 : e[u])) ? void 0 : c;\n}\nexport { T as optionalRef, y as useSyncRefs };", "map": {"version": 3, "names": ["useEffect", "l", "useRef", "i", "useEvent", "r", "u", "Symbol", "T", "t", "n", "arguments", "length", "undefined", "Object", "assign", "y", "_len", "Array", "_key", "current", "c", "e", "o", "every", "optionalRef", "useSyncRefs"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/hooks/use-sync-refs.js"], "sourcesContent": ["import{useEffect as l,useRef as i}from\"react\";import{useEvent as r}from'./use-event.js';let u=Symbol();function T(t,n=!0){return Object.assign(t,{[u]:n})}function y(...t){let n=i(t);l(()=>{n.current=t},[t]);let c=r(e=>{for(let o of n.current)o!=null&&(typeof o==\"function\"?o(e):o.current=e)});return t.every(e=>e==null||(e==null?void 0:e[u]))?void 0:c}export{T as optionalRef,y as useSyncRefs};\n"], "mappings": "AAAA,SAAOA,SAAS,IAAIC,CAAC,EAACC,MAAM,IAAIC,CAAC,QAAK,OAAO;AAAC,SAAOC,QAAQ,IAAIC,CAAC,QAAK,gBAAgB;AAAC,IAAIC,CAAC,GAACC,MAAM,CAAC,CAAC;AAAC,SAASC,CAACA,CAACC,CAAC,EAAM;EAAA,IAALC,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAC,CAAC,CAAC;EAAE,OAAOG,MAAM,CAACC,MAAM,CAACN,CAAC,EAAC;IAAC,CAACH,CAAC,GAAEI;EAAC,CAAC,CAAC;AAAA;AAAC,SAASM,CAACA,CAAA,EAAM;EAAA,SAAAC,IAAA,GAAAN,SAAA,CAAAC,MAAA,EAAFH,CAAC,OAAAS,KAAA,CAAAD,IAAA,GAAAE,IAAA,MAAAA,IAAA,GAAAF,IAAA,EAAAE,IAAA;IAADV,CAAC,CAAAU,IAAA,IAAAR,SAAA,CAAAQ,IAAA;EAAA;EAAE,IAAIT,CAAC,GAACP,CAAC,CAACM,CAAC,CAAC;EAACR,CAAC,CAAC,MAAI;IAACS,CAAC,CAACU,OAAO,GAACX,CAAC;EAAA,CAAC,EAAC,CAACA,CAAC,CAAC,CAAC;EAAC,IAAIY,CAAC,GAAChB,CAAC,CAACiB,CAAC,IAAE;IAAC,KAAI,IAAIC,CAAC,IAAIb,CAAC,CAACU,OAAO,EAACG,CAAC,IAAE,IAAI,KAAG,OAAOA,CAAC,IAAE,UAAU,GAACA,CAAC,CAACD,CAAC,CAAC,GAACC,CAAC,CAACH,OAAO,GAACE,CAAC,CAAC;EAAA,CAAC,CAAC;EAAC,OAAOb,CAAC,CAACe,KAAK,CAACF,CAAC,IAAEA,CAAC,IAAE,IAAI,KAAGA,CAAC,IAAE,IAAI,GAAC,KAAK,CAAC,GAACA,CAAC,CAAChB,CAAC,CAAC,CAAC,CAAC,GAAC,KAAK,CAAC,GAACe,CAAC;AAAA;AAAC,SAAOb,CAAC,IAAIiB,WAAW,EAACT,CAAC,IAAIU,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}