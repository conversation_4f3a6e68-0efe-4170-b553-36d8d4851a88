{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{API_BASE_URL}from'../config/api';// MongoDB collection import removed - using API calls instead\nimport{showSnackbar}from'../utils/snackbar';// Define report types\n/**\n * Generate and download a report\n * @param params Report parameters\n * @returns Promise with the download URL\n */export const generateReport=async params=>{try{// If HTML format is requested, generate it directly in the browser\nif(params.format==='html'){const htmlContent=await generateHtmlReport(params);const filename=\"\".concat(params.type,\"_report.html\");// Download the HTML file\ndownloadFile('',filename,htmlContent);return'Generated HTML report';}// For other formats, in a real implementation, this would call the backend API\n// For now, we'll simulate a successful response\n// Simulate API call delay\nawait new Promise(resolve=>setTimeout(resolve,1500));// Mock response with download URL\nconst mockResponse={success:true,downloadUrl:\"\".concat(API_BASE_URL,\"/reports/download/\").concat(params.type,\"_\").concat(Date.now(),\".\").concat(params.format==='excel'?'xlsx':params.format)};// Trigger file download\ndownloadFile(mockResponse.downloadUrl,\"\".concat(params.type,\"_report.\").concat(params.format==='excel'?'xlsx':params.format));return mockResponse.downloadUrl;}catch(error){console.error('Error generating report:',error);throw error;}};/**\n * Helper function to trigger file download\n * @param url URL of the file to download\n * @param filename Name to save the file as\n */export const downloadFile=(url,filename,content)=>{// If content is provided, use it directly\nif(content){const blob=new Blob([content],{type:getContentType(filename)});const link=document.createElement('a');link.href=URL.createObjectURL(blob);link.download=filename;document.body.appendChild(link);link.click();document.body.removeChild(link);URL.revokeObjectURL(link.href);return;}// Otherwise, in a real implementation, this would download the file from the URL\n// For now, we'll create a mock file with some content\n// Create a blob with mock content\nconst mockContent=\"This is a mock \".concat(filename,\" file generated at \").concat(new Date().toLocaleString());const blob=new Blob([mockContent],{type:getContentType(filename)});// Create a download link and trigger the download\nconst link=document.createElement('a');link.href=URL.createObjectURL(blob);link.download=filename;document.body.appendChild(link);link.click();document.body.removeChild(link);URL.revokeObjectURL(link.href);};/**\n * Get the content type based on file extension\n * @param filename Filename with extension\n * @returns Content type string\n */const getContentType=filename=>{if(filename.endsWith('.pdf')){return'application/pdf';}else if(filename.endsWith('.xlsx')){return'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';}else if(filename.endsWith('.csv')){return'text/csv';}else if(filename.endsWith('.html')){return'text/html';}return'text/plain';};/**\n * Get saved reports for a specific type\n * @param type Report type\n * @returns Promise with the list of saved reports\n *//**\n * Generate an HTML report based on the provided parameters\n * @param params Report parameters\n * @returns Promise with the HTML content\n */export const generateHtmlReport=async params=>{try{// Get the current date and time for the report\nconst reportDate=new Date().toLocaleDateString('en-ZA',{year:'numeric',month:'long',day:'numeric',hour:'2-digit',minute:'2-digit'});// Get the report title based on type\nconst getReportTitle=()=>{switch(params.type){case'analysis':return'Analysis Report';case'performance':return'Performance Report';case'health':return'Health Report';case'market':return'Market Report';case'financial':return'Financial Report';case'custom':return'Custom Report';default:return'MayCaiphus Livestock Report';}};// Get date range based on time period\nconst{startDate,endDate}=getDateRange(params.timePeriod,params.startDate,params.endDate);// Fetch data from MongoDB collections\nlet animals=[];let feedingRecords=[];let feedInventory=[];let healthRecords=[];let breedingRecords=[];let financialRecords=[];try{// Try to fetch data from MongoDB collections\nconst animalsCollection=await getCollection('animals');const feedingRecordsCollection=await getCollection('feeding_records');const feedInventoryCollection=await getCollection('feed_inventory');const healthRecordsCollection=await getCollection('health_records');const breedingRecordsCollection=await getCollection('breeding_records');const financialRecordsCollection=await getCollection('financial_records');// Safely fetch data with error handling\ntry{const animalsCursor=animalsCollection.find({});animals=animalsCursor&&typeof animalsCursor.toArray==='function'?await animalsCursor.toArray():[];}catch(error){console.error('Error fetching animals:',error);animals=[];}try{const feedingRecordsCursor=feedingRecordsCollection.find({date:{$gte:new Date(startDate),$lte:new Date(endDate)}});feedingRecords=feedingRecordsCursor&&typeof feedingRecordsCursor.toArray==='function'?await feedingRecordsCursor.toArray():[];}catch(error){console.error('Error fetching feeding records:',error);feedingRecords=[];}try{const feedInventoryCursor=feedInventoryCollection.find({});feedInventory=feedInventoryCursor&&typeof feedInventoryCursor.toArray==='function'?await feedInventoryCursor.toArray():[];}catch(error){console.error('Error fetching feed inventory:',error);feedInventory=[];}try{const healthRecordsCursor=healthRecordsCollection.find({date:{$gte:new Date(startDate),$lte:new Date(endDate)}});healthRecords=healthRecordsCursor&&typeof healthRecordsCursor.toArray==='function'?await healthRecordsCursor.toArray():[];}catch(error){console.error('Error fetching health records:',error);healthRecords=[];}try{const breedingRecordsCursor=breedingRecordsCollection.find({date:{$gte:new Date(startDate),$lte:new Date(endDate)}});breedingRecords=breedingRecordsCursor&&typeof breedingRecordsCursor.toArray==='function'?await breedingRecordsCursor.toArray():[];}catch(error){console.error('Error fetching breeding records:',error);breedingRecords=[];}try{const financialRecordsCursor=financialRecordsCollection.find({date:{$gte:new Date(startDate),$lte:new Date(endDate)}});financialRecords=financialRecordsCursor&&typeof financialRecordsCursor.toArray==='function'?await financialRecordsCursor.toArray():[];}catch(error){console.error('Error fetching financial records:',error);financialRecords=[];}}catch(error){console.error('Error setting up MongoDB collections:',error);// If there's an error with MongoDB, use mock data\nconsole.log('Using mock data for report generation');// Import mock data\nconst{mockAnimals}=await import('../mocks/animalData');const{mockFeedingRecords}=await import('../mocks/feedingData');const{mockHealthRecords}=await import('../mocks/healthData');const{mockBreedingRecords}=await import('../mocks/breedingData');const{mockFinancialRecords}=await import('../mocks/financialData');animals=mockAnimals||[];feedingRecords=mockFeedingRecords||[];feedInventory=[];healthRecords=mockHealthRecords||[];breedingRecords=mockBreedingRecords||[];financialRecords=mockFinancialRecords||[];}// Generate summary statistics\nconst totalAnimals=animals.length;const speciesCounts={};const statusCounts={};const healthCounts={};animals.forEach(animal=>{// Count by species\nif(animal.species){speciesCounts[animal.species]=(speciesCounts[animal.species]||0)+1;}// Count by status\nstatusCounts[animal.status]=(statusCounts[animal.status]||0)+1;// Count by health status\nhealthCounts[animal.healthStatus]=(healthCounts[animal.healthStatus]||0)+1;});// Calculate feed usage by type\nconst feedUsageByType={};feedingRecords.forEach(record=>{const feedType=record.feedType;if(!feedUsageByType[feedType]){feedUsageByType[feedType]=0;}feedUsageByType[feedType]+=record.quantity;});// Calculate total feed cost\nconst totalFeedCost=feedingRecords.reduce((sum,record)=>sum+(record.totalCost||0),0);// Calculate cost by animal group\nconst costByAnimalGroup={};feedingRecords.forEach(record=>{const animalGroup=record.animalGroupId;if(!costByAnimalGroup[animalGroup]){costByAnimalGroup[animalGroup]=0;}costByAnimalGroup[animalGroup]+=record.totalCost||0;});// Calculate financial summary\nconst totalRevenue=financialRecords.filter(record=>record.type==='income').reduce((sum,record)=>sum+record.amount,0);const totalExpenses=financialRecords.filter(record=>record.type==='expense').reduce((sum,record)=>sum+record.amount,0);const netProfit=totalRevenue-totalExpenses;const roi=totalRevenue>0?netProfit/totalRevenue*100:0;// Start building HTML\nlet html=\"\\n    <!DOCTYPE html>\\n    <html lang=\\\"en\\\">\\n    <head>\\n      <meta charset=\\\"UTF-8\\\">\\n      <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n      <title>\".concat(getReportTitle(),\"</title>\\n      <style>\\n        /* Enhanced Report Styles */\\n        body {\\n          font-family: 'Roboto', Arial, sans-serif;\\n          line-height: 1.6;\\n          color: #333;\\n          margin: 0;\\n          padding: 0;\\n          background-color: #f9f9f9;\\n        }\\n\\n        .report-container {\\n          max-width: 1200px;\\n          margin: 30px auto;\\n          padding: 30px;\\n          background-color: #fff;\\n          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n          border-radius: 8px;\\n        }\\n\\n        .report-header {\\n          text-align: center;\\n          margin-bottom: 40px;\\n          padding-bottom: 30px;\\n          border-bottom: 2px solid #f0f0f0;\\n        }\\n\\n        .report-title {\\n          font-size: 28px;\\n          font-weight: 700;\\n          margin: 0;\\n          color: #2c3e50;\\n          letter-spacing: -0.5px;\\n        }\\n\\n        .report-subtitle {\\n          font-size: 20px;\\n          color: #3498db;\\n          margin: 10px 0;\\n          font-weight: 500;\\n        }\\n\\n        .report-date {\\n          font-size: 14px;\\n          color: #7f8c8d;\\n          font-style: italic;\\n        }\\n\\n        .report-section {\\n          margin-bottom: 40px;\\n        }\\n\\n        .section-title {\\n          font-size: 22px;\\n          color: #2980b9;\\n          border-bottom: 3px solid #3498db;\\n          padding-bottom: 10px;\\n          margin-bottom: 20px;\\n          font-weight: 600;\\n        }\\n\\n        .summary-stats {\\n          display: flex;\\n          flex-wrap: wrap;\\n          gap: 20px;\\n          margin-bottom: 30px;\\n        }\\n\\n        .stat-card {\\n          background-color: #f8f9fa;\\n          border-radius: 8px;\\n          padding: 20px;\\n          min-width: 220px;\\n          box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);\\n          transition: transform 0.2s ease, box-shadow 0.2s ease;\\n        }\\n\\n        .stat-card:hover {\\n          transform: translateY(-5px);\\n          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\\n        }\\n\\n        .stat-title {\\n          font-size: 16px;\\n          color: #7f8c8d;\\n          margin-bottom: 8px;\\n          font-weight: 500;\\n        }\\n\\n        .stat-value {\\n          font-size: 28px;\\n          font-weight: 700;\\n          color: #2c3e50;\\n        }\\n\\n        table {\\n          width: 100%;\\n          border-collapse: collapse;\\n          margin-bottom: 30px;\\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\\n          border-radius: 8px;\\n          overflow: hidden;\\n        }\\n\\n        th, td {\\n          padding: 15px 18px;\\n          text-align: left;\\n          border-bottom: 1px solid #e0e0e0;\\n        }\\n\\n        th {\\n          background-color: #f2f8fd;\\n          font-weight: 600;\\n          color: #2c3e50;\\n          font-size: 14px;\\n          text-transform: uppercase;\\n          letter-spacing: 0.5px;\\n        }\\n\\n        tr:last-child td {\\n          border-bottom: none;\\n        }\\n\\n        tr:hover {\\n          background-color: #f5f9ff;\\n        }\\n\\n        .chart-container {\\n          margin-bottom: 40px;\\n          text-align: center;\\n          background-color: #f8f9fa;\\n          border-radius: 12px;\\n          padding: 25px;\\n          box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);\\n        }\\n\\n        .chart-legend {\\n          display: flex;\\n          flex-wrap: wrap;\\n          justify-content: center;\\n          gap: 20px;\\n          margin-top: 25px;\\n          padding: 15px;\\n          background-color: rgba(255, 255, 255, 0.7);\\n          border-radius: 8px;\\n        }\\n\\n        .legend-item {\\n          display: flex;\\n          align-items: center;\\n          font-size: 14px;\\n          padding: 5px 10px;\\n          background-color: #fff;\\n          border-radius: 20px;\\n          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);\\n        }\\n\\n        .legend-color {\\n          display: inline-block;\\n          width: 18px;\\n          height: 18px;\\n          margin-right: 8px;\\n          border-radius: 4px;\\n        }\\n\\n        .legend-label {\\n          color: #333;\\n          font-weight: 500;\\n        }\\n\\n        .chart-placeholder {\\n          background-color: #f8f9fa;\\n          border: 1px dashed #ccc;\\n          border-radius: 8px;\\n          padding: 30px;\\n          text-align: center;\\n          color: #7f8c8d;\\n        }\\n\\n        .footer {\\n          margin-top: 60px;\\n          text-align: center;\\n          font-size: 13px;\\n          color: #95a5a6;\\n          padding-top: 30px;\\n          border-top: 1px solid #eee;\\n        }\\n\\n        /* SVG Chart Styles */\\n        svg {\\n          max-width: 100%;\\n          height: auto;\\n          background-color: white;\\n          border-radius: 8px;\\n          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\\n        }\\n\\n        svg text {\\n          font-family: 'Roboto', Arial, sans-serif;\\n        }\\n\\n        svg .chart-title {\\n          font-size: 18px;\\n          font-weight: 600;\\n        }\\n\\n        svg .axis-label {\\n          font-size: 13px;\\n        }\\n\\n        svg .grid-line {\\n          stroke: #e0e0e0;\\n          stroke-dasharray: 3,3;\\n        }\\n\\n        svg .data-point {\\n          stroke-width: 2;\\n        }\\n\\n        svg .data-label {\\n          font-size: 12px;\\n          text-anchor: middle;\\n          font-weight: 500;\\n        }\\n\\n        /* Financial Report Specific Styles */\\n        .financial-highlight {\\n          color: #2980b9;\\n          font-weight: 600;\\n        }\\n\\n        .financial-positive {\\n          color: #27ae60;\\n          font-weight: 600;\\n        }\\n\\n        .financial-negative {\\n          color: #e74c3c;\\n          font-weight: 600;\\n        }\\n\\n        .financial-summary-box {\\n          background-color: #f8f9fa;\\n          border-left: 4px solid #3498db;\\n          padding: 20px;\\n          margin-bottom: 30px;\\n          border-radius: 0 8px 8px 0;\\n        }\\n\\n        .financial-summary-title {\\n          font-size: 18px;\\n          font-weight: 600;\\n          color: #2c3e50;\\n          margin-bottom: 15px;\\n        }\\n\\n        .financial-summary-content {\\n          display: flex;\\n          flex-wrap: wrap;\\n          gap: 20px;\\n        }\\n\\n        .financial-summary-item {\\n          flex: 1;\\n          min-width: 200px;\\n        }\\n\\n        .financial-summary-label {\\n          font-size: 14px;\\n          color: #7f8c8d;\\n          margin-bottom: 5px;\\n        }\\n\\n        .financial-summary-value {\\n          font-size: 22px;\\n          font-weight: 700;\\n          color: #2c3e50;\\n        }\\n\\n        .financial-trend-indicator {\\n          display: inline-flex;\\n          align-items: center;\\n          font-size: 14px;\\n          margin-left: 10px;\\n        }\\n\\n        .financial-trend-up {\\n          color: #27ae60;\\n        }\\n\\n        .financial-trend-down {\\n          color: #e74c3c;\\n        }\\n\\n        /* Print styles */\\n        @media print {\\n          body {\\n            padding: 0;\\n            font-size: 12pt;\\n            background-color: white;\\n          }\\n\\n          .report-container {\\n            box-shadow: none;\\n            padding: 0;\\n            max-width: 100%;\\n            margin: 0;\\n          }\\n\\n          .chart-container {\\n            break-inside: avoid;\\n            page-break-inside: avoid;\\n            box-shadow: none;\\n            border: 1px solid #eee;\\n          }\\n\\n          table {\\n            break-inside: auto;\\n            page-break-inside: auto;\\n            box-shadow: none;\\n            border: 1px solid #eee;\\n          }\\n\\n          tr {\\n            break-inside: avoid;\\n            page-break-inside: avoid;\\n          }\\n\\n          .no-print {\\n            display: none;\\n          }\\n\\n          .stat-card {\\n            box-shadow: none;\\n            border: 1px solid #eee;\\n          }\\n\\n          svg {\\n            box-shadow: none;\\n          }\\n        }\\n      </style>\\n    </head>\\n    <body>\\n      <div class=\\\"report-container\\\">\\n        <div class=\\\"report-header\\\">\\n          <h1 class=\\\"report-title\\\">MayCaiphus Livestock Management System</h1>\\n          <h2 class=\\\"report-subtitle\\\">\").concat(getReportTitle(),\"</h2>\\n          <p class=\\\"report-date\\\">Generated on: \").concat(reportDate,\"</p>\\n        </div>\\n  \");// Add summary section\nhtml+=\"\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Summary</h3>\\n      <div class=\\\"summary-stats\\\">\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Total Animals</div>\\n          <div class=\\\"stat-value\\\">\".concat(totalAnimals,\"</div>\\n        </div>\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Active Animals</div>\\n          <div class=\\\"stat-value\\\">\").concat(statusCounts['Active']||0,\"</div>\\n        </div>\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Healthy Animals</div>\\n          <div class=\\\"stat-value\\\">\").concat(healthCounts['healthy']||0,\"</div>\\n        </div>\\n      </div>\\n    </div>\\n  \");// Add financial summary section if financial report\nif(params.type==='financial'){html+=\"\\n      <div class=\\\"report-section\\\">\\n        <h3 class=\\\"section-title\\\">Financial Summary</h3>\\n        <div class=\\\"summary-stats\\\">\\n          <div class=\\\"stat-card\\\">\\n            <div class=\\\"stat-title\\\">Total Revenue</div>\\n            <div class=\\\"stat-value\\\">R \".concat(totalRevenue.toLocaleString(),\"</div>\\n          </div>\\n          <div class=\\\"stat-card\\\">\\n            <div class=\\\"stat-title\\\">Total Expenses</div>\\n            <div class=\\\"stat-value\\\">R \").concat(totalExpenses.toLocaleString(),\"</div>\\n          </div>\\n          <div class=\\\"stat-card\\\">\\n            <div class=\\\"stat-title\\\">Net Profit</div>\\n            <div class=\\\"stat-value\\\">R \").concat(netProfit.toLocaleString(),\"</div>\\n          </div>\\n          <div class=\\\"stat-card\\\">\\n            <div class=\\\"stat-title\\\">ROI</div>\\n            <div class=\\\"stat-value\\\">\").concat(roi.toFixed(2),\"%</div>\\n          </div>\\n        </div>\\n      </div>\\n    \");}// Add feed usage summary section if feed report\nif(params.type==='analysis'||params.type==='performance'){html+=\"\\n      <div class=\\\"report-section\\\">\\n        <h3 class=\\\"section-title\\\">Feed Usage Summary</h3>\\n        <div class=\\\"summary-stats\\\">\\n          <div class=\\\"stat-card\\\">\\n            <div class=\\\"stat-title\\\">Total Feed Cost</div>\\n            <div class=\\\"stat-value\\\">R \".concat(totalFeedCost.toLocaleString(),\"</div>\\n          </div>\\n          \").concat(Object.entries(feedUsageByType).slice(0,3).map(_ref=>{let[feedType,quantity]=_ref;return\"\\n            <div class=\\\"stat-card\\\">\\n              <div class=\\\"stat-title\\\">\".concat(feedType,\"</div>\\n              <div class=\\\"stat-value\\\">\").concat(quantity.toLocaleString(),\" kg</div>\\n            </div>\\n          \");}).join(''),\"\\n        </div>\\n      </div>\\n    \");}// Add charts section\nhtml+=\"\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Charts</h3>\\n      <div class=\\\"chart-container\\\">\\n        <svg width=\\\"100%\\\" height=\\\"300\\\" viewBox=\\\"0 0 500 300\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\">\\n          <!-- Generate pie chart for species distribution -->\\n          \".concat(generateSVGPieChart(Object.entries(speciesCounts).map((_ref2,index)=>{let[species,count]=_ref2;return{name:species,value:count,color:\"hsl(\".concat(index*40,\", 70%, 50%)\")};}),'Species Distribution'),\"\\n        </svg>\\n        <div class=\\\"chart-legend\\\">\\n          \").concat(Object.entries(speciesCounts).map((_ref3,index)=>{let[species,count]=_ref3;return\"\\n            <div class=\\\"legend-item\\\">\\n              <span class=\\\"legend-color\\\" style=\\\"background-color: hsl(\".concat(index*40,\", 70%, 50%);\\\"></span>\\n              <span class=\\\"legend-label\\\">\").concat(species,\": \").concat(count,\" (\").concat((count/totalAnimals*100).toFixed(1),\"%)</span>\\n            </div>\\n          \");}).join(''),\"\\n        </div>\\n      </div>\\n      <div class=\\\"chart-container\\\">\\n        <svg width=\\\"100%\\\" height=\\\"300\\\" viewBox=\\\"0 0 500 300\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\">\\n          <!-- Generate pie chart for health status distribution -->\\n          \").concat(generateSVGPieChart(Object.entries(healthCounts).map(_ref4=>{let[status,count]=_ref4;return{name:status,value:count,color:getHealthStatusColor(status)};}),'Health Status Distribution'),\"\\n        </svg>\\n        <div class=\\\"chart-legend\\\">\\n          \").concat(Object.entries(healthCounts).map(_ref5=>{let[status,count]=_ref5;return\"\\n            <div class=\\\"legend-item\\\">\\n              <span class=\\\"legend-color\\\" style=\\\"background-color: \".concat(getHealthStatusColor(status),\";\\\"></span>\\n              <span class=\\\"legend-label\\\">\").concat(status,\": \").concat(count,\" (\").concat((count/totalAnimals*100).toFixed(1),\"%)</span>\\n            </div>\\n          \");}).join(''),\"\\n        </div>\\n      </div>\\n    </div>\\n  \");// Add feed usage table if feed report\nif(params.type==='analysis'||params.type==='performance'){html+=\"\\n      <div class=\\\"report-section\\\">\\n        <h3 class=\\\"section-title\\\">Feed Usage Details</h3>\\n        <table>\\n          <thead>\\n            <tr>\\n              <th>Feed Type</th>\\n              <th>Quantity Used</th>\\n              <th>Cost</th>\\n              <th>Animal Group</th>\\n              <th>Date</th>\\n            </tr>\\n          </thead>\\n          <tbody>\\n    \";// Add rows for each feeding record\nfeedingRecords.slice(0,10).forEach(record=>{html+=\"\\n        <tr>\\n          <td>\".concat(record.feedType,\"</td>\\n          <td>\").concat(record.quantity,\" \").concat(record.unit||'kg',\"</td>\\n          <td>R \").concat((record.totalCost||0).toLocaleString(),\"</td>\\n          <td>\").concat(record.animalGroupId||'All',\"</td>\\n          <td>\").concat(new Date(record.date).toLocaleDateString('en-ZA'),\"</td>\\n        </tr>\\n      \");});// Close the table\nhtml+=\"\\n          </tbody>\\n        </table>\\n      </div>\\n    \";}// Add data table based on report type\nswitch(params.type){case'health':html+=generateDetailedHealthReport(animals,healthRecords);break;case'financial':html+=generateDetailedFinancialReport(animals,financialRecords);break;case'market':html+=generateDetailedMarketReport(animals);break;case'performance':html+=generateDetailedPerformanceReport(animals);break;case'analysis':html+=generateDetailedAnalysisReport(animals);break;default:html+=generateAnimalTable(animals);}// Add footer\nhtml+=\"\\n        <div class=\\\"footer\\\">\\n          <p>MayCaiphus Livestock Management System</p>\\n          <p>This report was generated automatically. For questions or support, please contact your system administrator.</p>\\n        </div>\\n      </div>\\n    </body>\\n  </html>\\n  \";return html;}catch(error){console.error('Error generating HTML report:',error);showSnackbar('Failed to generate report','error');// Get detailed error message\nlet errorMessage='Unknown error';if(error instanceof Error){errorMessage=error.message;}else if(typeof error==='string'){errorMessage=error;}else if(error&&typeof error==='object'){errorMessage=JSON.stringify(error);}// Return a simple error report with more detailed information\nreturn\"\\n      <!DOCTYPE html>\\n      <html lang=\\\"en\\\">\\n      <head>\\n        <meta charset=\\\"UTF-8\\\">\\n        <meta name=\\\"viewport\\\" content=\\\"width=device-width, initial-scale=1.0\\\">\\n        <title>Error Generating Report</title>\\n        <style>\\n          body { font-family: Arial, sans-serif; padding: 20px; }\\n          .error { color: #d32f2f; }\\n          .error-details { background-color: #f8f8f8; padding: 15px; border-left: 4px solid #d32f2f; margin-top: 20px; }\\n          .error-message { font-family: monospace; white-space: pre-wrap; }\\n          .suggestions { margin-top: 20px; background-color: #e8f5e9; padding: 15px; border-left: 4px solid #4caf50; }\\n        </style>\\n      </head>\\n      <body>\\n        <h1 class=\\\"error\\\">Error Generating Report</h1>\\n        <p>There was an error generating the requested report. Please try again later or contact support.</p>\\n\\n        <div class=\\\"error-details\\\">\\n          <h2>Error Details</h2>\\n          <p class=\\\"error-message\\\">\".concat(errorMessage,\"</p>\\n        </div>\\n\\n        <div class=\\\"suggestions\\\">\\n          <h2>Suggestions</h2>\\n          <ul>\\n            <li>Check your internet connection</li>\\n            <li>Verify that the MongoDB connection is properly configured</li>\\n            <li>Try refreshing the page and generating the report again</li>\\n            <li>If the error persists, contact your system administrator</li>\\n          </ul>\\n        </div>\\n      </body>\\n      </html>\\n    \");}};/**\n * Generate an HTML table for animal data\n */const generateAnimalTable=animals=>{let html=\"\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Animal Data</h3>\\n      <table>\\n        <thead>\\n          <tr>\\n            <th>Tag</th>\\n            <th>Name</th>\\n            <th>Species</th>\\n            <th>Breed</th>\\n            <th>Gender</th>\\n            <th>Birth Date</th>\\n            <th>Status</th>\\n            <th>Health</th>\\n            <th>Location</th>\\n          </tr>\\n        </thead>\\n        <tbody>\\n  \";// Add rows for each animal\nanimals.forEach(animal=>{html+=\"\\n      <tr>\\n        <td>\".concat(animal.tagNumber,\"</td>\\n        <td>\").concat(animal.name,\"</td>\\n        <td>\").concat(animal.species||animal.type,\"</td>\\n        <td>\").concat(animal.breed,\"</td>\\n        <td>\").concat(animal.gender,\"</td>\\n        <td>\").concat(animal.birthDate||'Unknown',\"</td>\\n        <td>\").concat(animal.status,\"</td>\\n        <td>\").concat(animal.healthStatus,\"</td>\\n        <td>\").concat(animal.location,\"</td>\\n      </tr>\\n    \");});// Close the table\nhtml+=\"\\n        </tbody>\\n      </table>\\n    </div>\\n  \";return html;};/**\n * Generate an HTML table for health data\n */const generateHealthTable=animals=>{let html=\"\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Animal Health Data</h3>\\n      <table>\\n        <thead>\\n          <tr>\\n            <th>Tag</th>\\n            <th>Name</th>\\n            <th>Species</th>\\n            <th>Health Status</th>\\n            <th>Last Check-up</th>\\n            <th>Next Check-up</th>\\n            <th>Notes</th>\\n          </tr>\\n        </thead>\\n        <tbody>\\n  \";// Add rows for each animal with mock health data\nanimals.forEach(animal=>{const lastCheckup=new Date(Date.now()-Math.random()*30*24*60*60*1000);const nextCheckup=new Date(Date.now()+Math.random()*30*24*60*60*1000);html+=\"\\n      <tr>\\n        <td>\".concat(animal.tagNumber,\"</td>\\n        <td>\").concat(animal.name,\"</td>\\n        <td>\").concat(animal.species||animal.type,\"</td>\\n        <td>\").concat(animal.healthStatus,\"</td>\\n        <td>\").concat(lastCheckup.toLocaleDateString('en-ZA'),\"</td>\\n        <td>\").concat(nextCheckup.toLocaleDateString('en-ZA'),\"</td>\\n        <td>\").concat(animal.notes||'-',\"</td>\\n      </tr>\\n    \");});// Close the table\nhtml+=\"\\n        </tbody>\\n      </table>\\n    </div>\\n  \";return html;};/**\n * Generate a detailed health report with comprehensive health data and visualizations\n */const generateDetailedHealthReport=(animals,healthRecords)=>{// Calculate health statistics\nconst totalAnimals=animals.length;const healthStatusCounts={};const treatmentTypeCounts={};const monthlyHealthIssues={};const vaccinationStatus={'Up to date':0,'Due soon':0,'Overdue':0};// Process health records\nhealthRecords.forEach(record=>{// Count by treatment type\nif(record.type){treatmentTypeCounts[record.type]=(treatmentTypeCounts[record.type]||0)+1;}// Count monthly health issues\nif(record.date){const month=new Date(record.date).toLocaleString('en-ZA',{month:'short',year:'numeric'});monthlyHealthIssues[month]=(monthlyHealthIssues[month]||0)+1;}});// Process animals for health status\nanimals.forEach(animal=>{if(animal.healthStatus){healthStatusCounts[animal.healthStatus]=(healthStatusCounts[animal.healthStatus]||0)+1;}// Determine vaccination status (mock data for demonstration)\nconst randomStatus=Math.random();if(randomStatus>0.7){vaccinationStatus['Up to date']++;}else if(randomStatus>0.4){vaccinationStatus['Due soon']++;}else{vaccinationStatus['Overdue']++;}});// Start building the detailed health report\nlet html=\"\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Health Overview</h3>\\n      <div class=\\\"summary-stats\\\">\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Total Animals</div>\\n          <div class=\\\"stat-value\\\">\".concat(totalAnimals,\"</div>\\n        </div>\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Healthy Animals</div>\\n          <div class=\\\"stat-value\\\">\").concat(healthStatusCounts['healthy']||0,\"</div>\\n        </div>\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Sick/Injured</div>\\n          <div class=\\\"stat-value\\\">\").concat((healthStatusCounts['sick']||0)+(healthStatusCounts['injured']||0),\"</div>\\n        </div>\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Vaccination Status</div>\\n          <div class=\\\"stat-value\\\">\").concat(vaccinationStatus['Up to date'],\" up to date</div>\\n        </div>\\n      </div>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Health Status Distribution</h3>\\n      <div class=\\\"chart-container\\\">\\n        <svg width=\\\"100%\\\" height=\\\"300\\\" viewBox=\\\"0 0 500 300\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\">\\n          <!-- Generate pie chart for health status -->\\n          \").concat(generateSVGPieChart(Object.entries(healthStatusCounts).map(_ref6=>{let[status,count]=_ref6;return{name:status,value:count,color:getHealthStatusColor(status)};}),'Health Status Distribution'),\"\\n        </svg>\\n        <div class=\\\"chart-legend\\\">\\n          \").concat(Object.entries(healthStatusCounts).map(_ref7=>{let[status,count]=_ref7;return\"\\n            <div class=\\\"legend-item\\\">\\n              <span class=\\\"legend-color\\\" style=\\\"background-color: \".concat(getHealthStatusColor(status),\";\\\"></span>\\n              <span class=\\\"legend-label\\\">\").concat(status,\": \").concat(count,\" (\").concat((count/totalAnimals*100).toFixed(1),\"%)</span>\\n            </div>\\n          \");}).join(''),\"\\n        </div>\\n      </div>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Treatment Types</h3>\\n      <div class=\\\"chart-container\\\">\\n        <svg width=\\\"100%\\\" height=\\\"300\\\" viewBox=\\\"0 0 500 300\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\">\\n          <!-- Generate bar chart for treatment types -->\\n          \").concat(generateSVGBarChart(Object.entries(treatmentTypeCounts).map(_ref8=>{let[type,count]=_ref8;return{name:type,value:count,color:getHealthStatusColor(type)};}),'Treatment Types Distribution',value=>value.toString()),\"\\n        </svg>\\n        <div class=\\\"chart-legend\\\">\\n          \").concat(Object.entries(treatmentTypeCounts).map(_ref9=>{let[type,count]=_ref9;return\"\\n            <div class=\\\"legend-item\\\">\\n              <span class=\\\"legend-color\\\" style=\\\"background-color: \".concat(getHealthStatusColor(type),\";\\\"></span>\\n              <span class=\\\"legend-label\\\">\").concat(type,\": \").concat(count,\" (\").concat((count/Object.values(treatmentTypeCounts).reduce((a,b)=>a+b,0)*100).toFixed(1),\"%)</span>\\n            </div>\\n          \");}).join(''),\"\\n        </div>\\n      </div>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Monthly Health Trends</h3>\\n      <div class=\\\"chart-container\\\">\\n        <svg width=\\\"100%\\\" height=\\\"300\\\" viewBox=\\\"0 0 500 300\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\">\\n          <!-- Generate line chart for monthly health trends -->\\n          \").concat(generateSVGLineChart([{name:'Health Issues',values:Object.entries(monthlyHealthIssues).map(_ref0=>{let[month,count]=_ref0;return{key:month,value:count,color:'#e91e63'};})}],'Monthly Health Issues',value=>value.toString()),\"\\n        </svg>\\n        <div class=\\\"chart-legend\\\">\\n          <div class=\\\"legend-item\\\">\\n            <span class=\\\"legend-color\\\" style=\\\"background-color: #e91e63;\\\"></span>\\n            <span class=\\\"legend-label\\\">Health Issues</span>\\n          </div>\\n        </div>\\n      </div>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Vaccination Status</h3>\\n      <div class=\\\"chart-container\\\">\\n        <svg width=\\\"100%\\\" height=\\\"300\\\" viewBox=\\\"0 0 500 300\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\">\\n          <!-- Generate pie chart for vaccination status -->\\n          \").concat(generateSVGPieChart(Object.entries(vaccinationStatus).map(_ref1=>{let[status,count]=_ref1;let color='#4caf50';// Default green for \"Up to date\"\nif(status==='Due soon')color='#ff9800';// Orange for due soon\nif(status==='Overdue')color='#f44336';// Red for overdue\nreturn{name:status,value:count,color:color};}),'Vaccination Status'),\"\\n        </svg>\\n        <div class=\\\"chart-legend\\\">\\n          \").concat(Object.entries(vaccinationStatus).map(_ref10=>{let[status,count]=_ref10;let color='#4caf50';// Default green for \"Up to date\"\nif(status==='Due soon')color='#ff9800';// Orange for due soon\nif(status==='Overdue')color='#f44336';// Red for overdue\nreturn\"\\n              <div class=\\\"legend-item\\\">\\n                <span class=\\\"legend-color\\\" style=\\\"background-color: \".concat(color,\";\\\"></span>\\n                <span class=\\\"legend-label\\\">\").concat(status,\": \").concat(count,\" (\").concat((count/totalAnimals*100).toFixed(1),\"%)</span>\\n              </div>\\n            \");}).join(''),\"\\n        </div>\\n      </div>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Recent Health Records</h3>\\n      <table>\\n        <thead>\\n          <tr>\\n            <th>Animal ID</th>\\n            <th>Date</th>\\n            <th>Type</th>\\n            <th>Diagnosis</th>\\n            <th>Treatment</th>\\n            <th>Performed By</th>\\n            <th>Follow-up Date</th>\\n          </tr>\\n        </thead>\\n        <tbody>\\n  \");// Add rows for each health record (limit to most recent 15)\nconst sortedRecords=[...healthRecords].sort((a,b)=>new Date(b.date).getTime()-new Date(a.date).getTime()).slice(0,15);sortedRecords.forEach(record=>{const animalInfo=animals.find(a=>a.id===record.animalId)||{};html+=\"\\n      <tr>\\n        <td>\".concat(record.animalId,\" (\").concat(animalInfo.name||'Unknown',\")</td>\\n        <td>\").concat(new Date(record.date).toLocaleDateString('en-ZA'),\"</td>\\n        <td>\").concat(record.type||'Check-up',\"</td>\\n        <td>\").concat(record.diagnosis||'-',\"</td>\\n        <td>\").concat(record.treatment||'-',\"</td>\\n        <td>\").concat(record.performedBy||'Staff',\"</td>\\n        <td>\").concat(record.followUpDate?new Date(record.followUpDate).toLocaleDateString('en-ZA'):'-',\"</td>\\n      </tr>\\n    \");});// Close the table\nhtml+=\"\\n        </tbody>\\n      </table>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Animals Requiring Attention</h3>\\n      <table>\\n        <thead>\\n          <tr>\\n            <th>Tag</th>\\n            <th>Name</th>\\n            <th>Species</th>\\n            <th>Health Status</th>\\n            <th>Issue</th>\\n            <th>Last Check-up</th>\\n            <th>Next Check-up</th>\\n          </tr>\\n        </thead>\\n        <tbody>\\n  \";// Add rows for animals requiring attention\nconst animalsNeedingAttention=animals.filter(animal=>animal.healthStatus==='sick'||animal.healthStatus==='injured'||animal.healthStatus==='critical');animalsNeedingAttention.forEach(animal=>{const lastRecord=healthRecords.filter(r=>r.animalId===animal.id).sort((a,b)=>new Date(b.date).getTime()-new Date(a.date).getTime())[0]||{};const lastCheckup=lastRecord.date?new Date(lastRecord.date):new Date(Date.now()-Math.random()*30*24*60*60*1000);const nextCheckup=lastRecord.followUpDate?new Date(lastRecord.followUpDate):new Date(Date.now()+Math.random()*10*24*60*60*1000);html+=\"\\n      <tr>\\n        <td>\".concat(animal.tagNumber,\"</td>\\n        <td>\").concat(animal.name,\"</td>\\n        <td>\").concat(animal.species||animal.type,\"</td>\\n        <td>\").concat(animal.healthStatus,\"</td>\\n        <td>\").concat(lastRecord.diagnosis||'Requires check-up',\"</td>\\n        <td>\").concat(lastCheckup.toLocaleDateString('en-ZA'),\"</td>\\n        <td>\").concat(nextCheckup.toLocaleDateString('en-ZA'),\"</td>\\n      </tr>\\n    \");});// Close the table\nhtml+=\"\\n        </tbody>\\n      </table>\\n    </div>\\n  \";return html;};/**\n * Generate an SVG pie chart\n */const generateSVGPieChart=(data,title)=>{const total=data.reduce((sum,item)=>sum+item.value,0);if(total===0)return'<text x=\"250\" y=\"150\" text-anchor=\"middle\">No data available</text>';const centerX=250;const centerY=150;const radius=100;let startAngle=0;let paths='';let labels='';data.forEach((item,index)=>{const percentage=item.value/total;const endAngle=startAngle+percentage*2*Math.PI;// Calculate path\nconst x1=centerX+radius*Math.cos(startAngle);const y1=centerY+radius*Math.sin(startAngle);const x2=centerX+radius*Math.cos(endAngle);const y2=centerY+radius*Math.sin(endAngle);const largeArcFlag=percentage>0.5?1:0;paths+=\"<path d=\\\"M \".concat(centerX,\" \").concat(centerY,\" L \").concat(x1,\" \").concat(y1,\" A \").concat(radius,\" \").concat(radius,\" 0 \").concat(largeArcFlag,\" 1 \").concat(x2,\" \").concat(y2,\" Z\\\"\\n              fill=\\\"\").concat(item.color,\"\\\" stroke=\\\"white\\\" stroke-width=\\\"1\\\" />\");// Add label if segment is large enough\nif(percentage>0.05){const labelAngle=startAngle+(endAngle-startAngle)/2;const labelRadius=radius*0.7;const labelX=centerX+labelRadius*Math.cos(labelAngle);const labelY=centerY+labelRadius*Math.sin(labelAngle);labels+=\"<text x=\\\"\".concat(labelX,\"\\\" y=\\\"\").concat(labelY,\"\\\" text-anchor=\\\"middle\\\" fill=\\\"white\\\" font-weight=\\\"bold\\\" font-size=\\\"12\\\">\\n                  \").concat((percentage*100).toFixed(0),\"%\\n                </text>\");}startAngle=endAngle;});return\"\\n    <g>\\n      <text x=\\\"\".concat(centerX,\"\\\" y=\\\"30\\\" text-anchor=\\\"middle\\\" font-size=\\\"16\\\" font-weight=\\\"bold\\\">\").concat(title,\"</text>\\n      \").concat(paths,\"\\n      \").concat(labels,\"\\n    </g>\\n  \");};/**\n * Generate an SVG bar chart\n */const generateSVGBarChart=(data,title,formatValue)=>{if(data.length===0)return'<text x=\"250\" y=\"150\" text-anchor=\"middle\">No data available</text>';const width=500;const height=300;const margin={top:40,right:20,bottom:60,left:60};const chartWidth=width-margin.left-margin.right;const chartHeight=height-margin.top-margin.bottom;const maxValue=Math.max(...data.map(d=>d.value));const barWidth=chartWidth/data.length*0.8;const barSpacing=chartWidth/data.length*0.2;let bars='';let xLabels='';let yLabels='';// Generate y-axis labels\nfor(let i=0;i<=5;i++){const value=maxValue*i/5;const y=margin.top+chartHeight-chartHeight*i/5;yLabels+=\"\\n      <line x1=\\\"\".concat(margin.left-5,\"\\\" y1=\\\"\").concat(y,\"\\\" x2=\\\"\").concat(margin.left,\"\\\" y2=\\\"\").concat(y,\"\\\" stroke=\\\"#666\\\" />\\n      <text x=\\\"\").concat(margin.left-10,\"\\\" y=\\\"\").concat(y+5,\"\\\" text-anchor=\\\"end\\\" font-size=\\\"12\\\">\").concat(formatValue?formatValue(value):value,\"</text>\\n    \");}// Generate bars and x-axis labels\ndata.forEach((item,index)=>{const x=margin.left+chartWidth/data.length*index+barSpacing/2;const barHeight=item.value/maxValue*chartHeight;const y=margin.top+chartHeight-barHeight;bars+=\"<rect x=\\\"\".concat(x,\"\\\" y=\\\"\").concat(y,\"\\\" width=\\\"\").concat(barWidth,\"\\\" height=\\\"\").concat(barHeight,\"\\\" fill=\\\"\").concat(item.color,\"\\\" />\");xLabels+=\"<text x=\\\"\".concat(x+barWidth/2,\"\\\" y=\\\"\").concat(margin.top+chartHeight+20,\"\\\" text-anchor=\\\"middle\\\" font-size=\\\"12\\\">\").concat(item.name,\"</text>\");});return\"\\n    <g>\\n      <text x=\\\"\".concat(width/2,\"\\\" y=\\\"20\\\" text-anchor=\\\"middle\\\" font-size=\\\"16\\\" font-weight=\\\"bold\\\">\").concat(title,\"</text>\\n\\n      <!-- Y-axis -->\\n      <line x1=\\\"\").concat(margin.left,\"\\\" y1=\\\"\").concat(margin.top,\"\\\" x2=\\\"\").concat(margin.left,\"\\\" y2=\\\"\").concat(margin.top+chartHeight,\"\\\" stroke=\\\"#666\\\" />\\n      \").concat(yLabels,\"\\n\\n      <!-- X-axis -->\\n      <line x1=\\\"\").concat(margin.left,\"\\\" y1=\\\"\").concat(margin.top+chartHeight,\"\\\" x2=\\\"\").concat(margin.left+chartWidth,\"\\\" y2=\\\"\").concat(margin.top+chartHeight,\"\\\" stroke=\\\"#666\\\" />\\n      \").concat(xLabels,\"\\n\\n      <!-- Bars -->\\n      \").concat(bars,\"\\n    </g>\\n  \");};/**\n * Generate an SVG line chart\n */const generateSVGLineChart=(data,title,formatValue)=>{if(data.length===0)return'<text x=\"250\" y=\"150\" text-anchor=\"middle\">No data available</text>';const width=500;const height=300;const margin={top:40,right:20,bottom:60,left:60};const chartWidth=width-margin.left-margin.right;const chartHeight=height-margin.top-margin.bottom;// Find max value across all data series\nconst allValues=data.flatMap(series=>series.values.map(v=>v.value));const maxValue=Math.max(...allValues);let lines='';let points='';let xLabels='';let yLabels='';let legend='';// Generate y-axis labels\nfor(let i=0;i<=5;i++){const value=maxValue*i/5;const y=margin.top+chartHeight-chartHeight*i/5;yLabels+=\"\\n      <line x1=\\\"\".concat(margin.left-5,\"\\\" y1=\\\"\").concat(y,\"\\\" x2=\\\"\").concat(margin.left,\"\\\" y2=\\\"\").concat(y,\"\\\" stroke=\\\"#666\\\" />\\n      <text x=\\\"\").concat(margin.left-10,\"\\\" y=\\\"\").concat(y+5,\"\\\" text-anchor=\\\"end\\\" font-size=\\\"12\\\">\").concat(formatValue?formatValue(value):value,\"</text>\\n    \");}// Generate x-axis labels\ndata[0].values.forEach((item,index)=>{const x=margin.left+chartWidth/(data[0].values.length-1)*index;xLabels+=\"<text x=\\\"\".concat(x,\"\\\" y=\\\"\").concat(margin.top+chartHeight+20,\"\\\" text-anchor=\\\"middle\\\" font-size=\\\"12\\\">\").concat(item.key,\"</text>\");});// Generate lines and points for each data series\ndata.forEach((series,seriesIndex)=>{let pathData='';series.values.forEach((point,index)=>{const x=margin.left+chartWidth/(series.values.length-1)*index;const y=margin.top+chartHeight-point.value/maxValue*chartHeight;if(index===0){pathData+=\"M \".concat(x,\" \").concat(y);}else{pathData+=\" L \".concat(x,\" \").concat(y);}points+=\"<circle cx=\\\"\".concat(x,\"\\\" cy=\\\"\").concat(y,\"\\\" r=\\\"4\\\" fill=\\\"\").concat(point.color,\"\\\" />\");});lines+=\"<path d=\\\"\".concat(pathData,\"\\\" stroke=\\\"\").concat(series.values[0].color,\"\\\" stroke-width=\\\"2\\\" fill=\\\"none\\\" />\");// Add to legend\nlegend+=\"\\n      <g transform=\\\"translate(\".concat(margin.left+seriesIndex*100,\", \").concat(height-20,\")\\\">\\n        <line x1=\\\"0\\\" y1=\\\"0\\\" x2=\\\"20\\\" y2=\\\"0\\\" stroke=\\\"\").concat(series.values[0].color,\"\\\" stroke-width=\\\"2\\\" />\\n        <text x=\\\"25\\\" y=\\\"5\\\" font-size=\\\"12\\\">\").concat(series.name,\"</text>\\n      </g>\\n    \");});return\"\\n    <g>\\n      <text x=\\\"\".concat(width/2,\"\\\" y=\\\"20\\\" text-anchor=\\\"middle\\\" font-size=\\\"16\\\" font-weight=\\\"bold\\\">\").concat(title,\"</text>\\n\\n      <!-- Y-axis -->\\n      <line x1=\\\"\").concat(margin.left,\"\\\" y1=\\\"\").concat(margin.top,\"\\\" x2=\\\"\").concat(margin.left,\"\\\" y2=\\\"\").concat(margin.top+chartHeight,\"\\\" stroke=\\\"#666\\\" />\\n      \").concat(yLabels,\"\\n\\n      <!-- X-axis -->\\n      <line x1=\\\"\").concat(margin.left,\"\\\" y1=\\\"\").concat(margin.top+chartHeight,\"\\\" x2=\\\"\").concat(margin.left+chartWidth,\"\\\" y2=\\\"\").concat(margin.top+chartHeight,\"\\\" stroke=\\\"#666\\\" />\\n      \").concat(xLabels,\"\\n\\n      <!-- Grid lines -->\\n      \").concat(Array(6).fill(0).map((_,i)=>{const y=margin.top+chartHeight-chartHeight*i/5;return\"<line x1=\\\"\".concat(margin.left,\"\\\" y1=\\\"\").concat(y,\"\\\" x2=\\\"\").concat(margin.left+chartWidth,\"\\\" y2=\\\"\").concat(y,\"\\\" stroke=\\\"#ddd\\\" stroke-dasharray=\\\"5,5\\\" />\");}).join(''),\"\\n\\n      <!-- Lines and points -->\\n      \").concat(lines,\"\\n      \").concat(points,\"\\n\\n      <!-- Legend -->\\n      \").concat(legend,\"\\n    </g>\\n  \");};/**\n * Get color for health status\n */const getHealthStatusColor=status=>{const statusLower=status.toLowerCase();if(statusLower.includes('healthy')||statusLower==='good')return'#4caf50';if(statusLower.includes('sick')||statusLower.includes('ill'))return'#f44336';if(statusLower.includes('injured'))return'#ff9800';if(statusLower.includes('critical'))return'#d32f2f';if(statusLower.includes('recovering'))return'#2196f3';if(statusLower.includes('quarantine'))return'#9c27b0';if(statusLower.includes('pregnant'))return'#e91e63';if(statusLower.includes('treatment'))return'#ff5722';return'#607d8b';// Default color\n};/**\n * Get color for financial data\n */const getFinancialColor=type=>{const typeLower=type.toLowerCase();if(typeLower.includes('revenue')||typeLower.includes('income')||typeLower.includes('profit'))return'#4caf50';if(typeLower.includes('expense')||typeLower.includes('cost'))return'#f44336';if(typeLower.includes('sales'))return'#2196f3';if(typeLower.includes('feed'))return'#ff9800';if(typeLower.includes('labor'))return'#9c27b0';if(typeLower.includes('veterinary'))return'#e91e63';if(typeLower.includes('equipment'))return'#00bcd4';if(typeLower.includes('utilities'))return'#607d8b';return'#3f51b5';// Default color\n};/**\n * Get date range based on time period\n */const getDateRange=(timePeriod,startDateStr,endDateStr)=>{const now=new Date();const endDate=endDateStr?new Date(endDateStr):now;let startDate;if(timePeriod==='custom'&&startDateStr){startDate=new Date(startDateStr);}else{switch(timePeriod){case'week':startDate=new Date(now);startDate.setDate(now.getDate()-7);break;case'month':startDate=new Date(now);startDate.setMonth(now.getMonth()-1);break;case'quarter':startDate=new Date(now);startDate.setMonth(now.getMonth()-3);break;case'year':startDate=new Date(now);startDate.setFullYear(now.getFullYear()-1);break;default:startDate=new Date(now);startDate.setMonth(now.getMonth()-1);}}return{startDate:startDate.toISOString().split('T')[0],endDate:endDate.toISOString().split('T')[0]};};/**\n * Generate an HTML table for financial data\n */const generateFinancialTable=animals=>{let html=\"\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Financial Data</h3>\\n      <table>\\n        <thead>\\n          <tr>\\n            <th>Tag</th>\\n            <th>Name</th>\\n            <th>Species</th>\\n            <th>Purchase Date</th>\\n            <th>Purchase Price (R)</th>\\n            <th>Current Value (R)</th>\\n            <th>ROI (%)</th>\\n          </tr>\\n        </thead>\\n        <tbody>\\n  \";// Add rows for each animal with real financial data\nanimals.forEach(animal=>{const purchasePrice=animal.purchasePrice||0;// Calculate current value based on age, weight, and market factors\n// This is a simplified calculation - in a real app, this would be more complex\nconst ageInMonths=animal.birthDate?Math.floor((new Date().getTime()-new Date(animal.birthDate).getTime())/(30*24*60*60*1000)):24;// Base value calculation\nlet currentValue=purchasePrice;// Add value based on age (younger animals may be worth more)\nif(ageInMonths<12){currentValue*=1.2;// 20% premium for young animals\n}else if(ageInMonths>60){currentValue*=0.8;// 20% discount for older animals\n}// Add value based on health status\nif(animal.healthStatus==='healthy'){currentValue*=1.1;// 10% premium for healthy animals\n}else if(animal.healthStatus==='sick'||animal.healthStatus==='injured'){currentValue*=0.7;// 30% discount for sick/injured animals\n}// Calculate ROI\nconst roi=purchasePrice>0?(currentValue-purchasePrice)/purchasePrice*100:0;html+=\"\\n      <tr>\\n        <td>\".concat(animal.tagNumber,\"</td>\\n        <td>\").concat(animal.name,\"</td>\\n        <td>\").concat(animal.species||animal.type,\"</td>\\n        <td>\").concat(animal.purchaseDate||'-',\"</td>\\n        <td>R \").concat(purchasePrice.toLocaleString(),\"</td>\\n        <td>R \").concat(currentValue.toLocaleString(undefined,{minimumFractionDigits:2,maximumFractionDigits:2}),\"</td>\\n        <td>\").concat(roi.toFixed(2),\"%</td>\\n      </tr>\\n    \");});// Close the table\nhtml+=\"\\n        </tbody>\\n      </table>\\n    </div>\\n  \";return html;};/**\n * Generate a detailed financial report with comprehensive financial data and visualizations\n */const generateDetailedFinancialReport=(animals,financialRecords)=>{// Calculate financial statistics\nconst totalAnimals=animals.length;// Process financial records\nconst totalRevenue=financialRecords.filter(record=>record.type==='income').reduce((sum,record)=>sum+(record.amount||0),0);const totalExpenses=financialRecords.filter(record=>record.type==='expense').reduce((sum,record)=>sum+(record.amount||0),0);const netProfit=totalRevenue-totalExpenses;const profitMargin=totalRevenue>0?netProfit/totalRevenue*100:0;// Calculate expense categories\nconst expensesByCategory={};financialRecords.filter(record=>record.type==='expense').forEach(record=>{if(record.category){expensesByCategory[record.category]=(expensesByCategory[record.category]||0)+(record.amount||0);}});// Calculate revenue sources\nconst revenueBySource={};financialRecords.filter(record=>record.type==='income').forEach(record=>{if(record.category){revenueBySource[record.category]=(revenueBySource[record.category]||0)+(record.amount||0);}});// Calculate monthly financial data\nconst monthlyFinancialData={};financialRecords.forEach(record=>{if(record.date){const month=new Date(record.date).toLocaleString('en-ZA',{month:'short',year:'numeric'});if(!monthlyFinancialData[month]){monthlyFinancialData[month]={revenue:0,expenses:0,profit:0};}if(record.type==='income'){monthlyFinancialData[month].revenue+=record.amount||0;}else if(record.type==='expense'){monthlyFinancialData[month].expenses+=record.amount||0;}monthlyFinancialData[month].profit=monthlyFinancialData[month].revenue-monthlyFinancialData[month].expenses;}});// Calculate animal asset values\nconst totalAssetValue=animals.reduce((sum,animal)=>{// Simple calculation based on purchase price and health status\nlet value=animal.purchasePrice||0;if(animal.healthStatus==='healthy'){value*=1.1;// 10% premium for healthy animals\n}else if(animal.healthStatus==='sick'||animal.healthStatus==='injured'){value*=0.7;// 30% discount for sick/injured animals\n}return sum+value;},0);// Start building the detailed financial report\nlet html=\"\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Financial Overview</h3>\\n      <div class=\\\"summary-stats\\\">\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Total Revenue</div>\\n          <div class=\\\"stat-value\\\">R \".concat(totalRevenue.toLocaleString(),\"</div>\\n        </div>\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Total Expenses</div>\\n          <div class=\\\"stat-value\\\">R \").concat(totalExpenses.toLocaleString(),\"</div>\\n        </div>\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Net Profit</div>\\n          <div class=\\\"stat-value\\\">R \").concat(netProfit.toLocaleString(),\"</div>\\n        </div>\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Profit Margin</div>\\n          <div class=\\\"stat-value\\\">\").concat(profitMargin.toFixed(2),\"%</div>\\n        </div>\\n      </div>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Monthly Financial Performance</h3>\\n      <div class=\\\"chart-container\\\">\\n        <svg width=\\\"100%\\\" height=\\\"350\\\" viewBox=\\\"0 0 500 350\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\">\\n          <!-- Generate line chart for monthly financial performance -->\\n          \").concat(generateSVGLineChart([{name:'Revenue',values:Object.entries(monthlyFinancialData).map(_ref11=>{let[month,data]=_ref11;return{key:month,value:data.revenue,color:'#4caf50'// Green for revenue\n};})},{name:'Expenses',values:Object.entries(monthlyFinancialData).map(_ref12=>{let[month,data]=_ref12;return{key:month,value:data.expenses,color:'#f44336'// Red for expenses\n};})},{name:'Profit',values:Object.entries(monthlyFinancialData).map(_ref13=>{let[month,data]=_ref13;return{key:month,value:data.profit,color:'#2196f3'// Blue for profit\n};})}],'Monthly Financial Performance',value=>\"R\".concat(value.toLocaleString())),\"\\n        </svg>\\n        <div class=\\\"chart-legend\\\">\\n          <div class=\\\"legend-item\\\">\\n            <span class=\\\"legend-color\\\" style=\\\"background-color: #4caf50;\\\"></span>\\n            <span class=\\\"legend-label\\\">Revenue</span>\\n          </div>\\n          <div class=\\\"legend-item\\\">\\n            <span class=\\\"legend-color\\\" style=\\\"background-color: #f44336;\\\"></span>\\n            <span class=\\\"legend-label\\\">Expenses</span>\\n          </div>\\n          <div class=\\\"legend-item\\\">\\n            <span class=\\\"legend-color\\\" style=\\\"background-color: #2196f3;\\\"></span>\\n            <span class=\\\"legend-label\\\">Profit</span>\\n          </div>\\n        </div>\\n      </div>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Expense Distribution</h3>\\n      <div class=\\\"chart-container\\\">\\n        <svg width=\\\"100%\\\" height=\\\"300\\\" viewBox=\\\"0 0 500 300\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\">\\n          <!-- Generate pie chart for expense categories -->\\n          \").concat(generateSVGPieChart(Object.entries(expensesByCategory).map(_ref14=>{let[category,amount]=_ref14;return{name:category,value:amount,color:getFinancialColor(category)};}),'Expense Categories'),\"\\n        </svg>\\n        <div class=\\\"chart-legend\\\">\\n          \").concat(Object.entries(expensesByCategory).map(_ref15=>{let[category,amount]=_ref15;return\"\\n            <div class=\\\"legend-item\\\">\\n              <span class=\\\"legend-color\\\" style=\\\"background-color: \".concat(getFinancialColor(category),\";\\\"></span>\\n              <span class=\\\"legend-label\\\">\").concat(category,\": R\").concat(amount.toLocaleString(),\" (\").concat((amount/totalExpenses*100).toFixed(1),\"%)</span>\\n            </div>\\n          \");}).join(''),\"\\n        </div>\\n      </div>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Revenue Sources</h3>\\n      <div class=\\\"chart-container\\\">\\n        <svg width=\\\"100%\\\" height=\\\"300\\\" viewBox=\\\"0 0 500 300\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\">\\n          <!-- Generate pie chart for revenue sources -->\\n          \").concat(generateSVGPieChart(Object.entries(revenueBySource).map(_ref16=>{let[source,amount]=_ref16;return{name:source,value:amount,color:getFinancialColor(source)};}),'Revenue Sources'),\"\\n        </svg>\\n        <div class=\\\"chart-legend\\\">\\n          \").concat(Object.entries(revenueBySource).map(_ref17=>{let[source,amount]=_ref17;return\"\\n            <div class=\\\"legend-item\\\">\\n              <span class=\\\"legend-color\\\" style=\\\"background-color: \".concat(getFinancialColor(source),\";\\\"></span>\\n              <span class=\\\"legend-label\\\">\").concat(source,\": R\").concat(amount.toLocaleString(),\" (\").concat((amount/totalRevenue*100).toFixed(1),\"%)</span>\\n            </div>\\n          \");}).join(''),\"\\n        </div>\\n      </div>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Asset Valuation</h3>\\n      <div class=\\\"summary-stats\\\">\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Total Asset Value</div>\\n          <div class=\\\"stat-value\\\">R \").concat(totalAssetValue.toLocaleString(),\"</div>\\n        </div>\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Average Asset Value</div>\\n          <div class=\\\"stat-value\\\">R \").concat((totalAssetValue/(totalAnimals||1)).toLocaleString(undefined,{maximumFractionDigits:2}),\"</div>\\n        </div>\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Asset to Revenue Ratio</div>\\n          <div class=\\\"stat-value\\\">\").concat((totalAssetValue/(totalRevenue||1)).toFixed(2),\"</div>\\n        </div>\\n      </div>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Recent Financial Transactions</h3>\\n      <table>\\n        <thead>\\n          <tr>\\n            <th>Date</th>\\n            <th>Type</th>\\n            <th>Category</th>\\n            <th>Description</th>\\n            <th>Amount (R)</th>\\n            <th>Payment Method</th>\\n            <th>Reference</th>\\n          </tr>\\n        </thead>\\n        <tbody>\\n  \");// Add rows for each financial record (limit to most recent 15)\nconst sortedRecords=[...financialRecords].sort((a,b)=>new Date(b.date).getTime()-new Date(a.date).getTime()).slice(0,15);sortedRecords.forEach(record=>{html+=\"\\n      <tr>\\n        <td>\".concat(new Date(record.date).toLocaleDateString('en-ZA'),\"</td>\\n        <td>\").concat(record.type==='income'?'Income':'Expense',\"</td>\\n        <td>\").concat(record.category||'-',\"</td>\\n        <td>\").concat(record.description||'-',\"</td>\\n        <td>R \").concat((record.amount||0).toLocaleString(),\"</td>\\n        <td>\").concat(record.paymentMethod||'-',\"</td>\\n        <td>\").concat(record.reference||'-',\"</td>\\n      </tr>\\n    \");});// Close the table\nhtml+=\"\\n        </tbody>\\n      </table>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Budget Analysis</h3>\\n      <table>\\n        <thead>\\n          <tr>\\n            <th>Category</th>\\n            <th>Budgeted Amount (R)</th>\\n            <th>Actual Amount (R)</th>\\n            <th>Variance (R)</th>\\n            <th>Variance (%)</th>\\n            <th>Status</th>\\n          </tr>\\n        </thead>\\n        <tbody>\\n  \";// Generate mock budget data for demonstration\nconst budgetCategories=['Feed','Veterinary','Labor','Equipment','Maintenance','Utilities','Marketing'];budgetCategories.forEach(category=>{const budgeted=Math.round(Math.random()*50000)+10000;const actual=expensesByCategory[category]||Math.round(Math.random()*budgeted*1.2);const variance=budgeted-actual;const variancePercent=variance/budgeted*100;const status=variancePercent>10?'Under Budget':variancePercent<-10?'Over Budget':'On Target';html+=\"\\n      <tr>\\n        <td>\".concat(category,\"</td>\\n        <td>R \").concat(budgeted.toLocaleString(),\"</td>\\n        <td>R \").concat(actual.toLocaleString(),\"</td>\\n        <td>R \").concat(variance.toLocaleString(),\"</td>\\n        <td>\").concat(variancePercent.toFixed(2),\"%</td>\\n        <td>\").concat(status,\"</td>\\n      </tr>\\n    \");});// Close the table\nhtml+=\"\\n        </tbody>\\n      </table>\\n    </div>\\n  \";return html;};/**\n * Generate an HTML table for market data\n */const generateMarketTable=animals=>{// Simplified implementation for market data\nreturn generateFinancialTable(animals);};/**\n * Generate a detailed market report with comprehensive market data and visualizations\n */const generateDetailedMarketReport=animals=>{// Calculate market statistics\nconst totalAnimals=animals.length;const speciesCounts={};const marketValueBySpecies={};const marketTrends={};// Process animals for market data\nanimals.forEach(animal=>{if(animal.species){speciesCounts[animal.species]=(speciesCounts[animal.species]||0)+1;// Calculate market value (simplified)\nconst marketValue=animal.purchasePrice?animal.purchasePrice*1.2:5000;// Default value if no purchase price\nmarketValueBySpecies[animal.species]=(marketValueBySpecies[animal.species]||0)+marketValue;}});// Generate mock market trend data\nObject.keys(speciesCounts).forEach(species=>{const currentValue=marketValueBySpecies[species]/speciesCounts[species];const previousValue=currentValue*(0.8+Math.random()*0.4);// Random previous value between 80% and 120% of current\nconst change=(currentValue-previousValue)/previousValue*100;marketTrends[species]={current:currentValue,previous:previousValue,change:change};});// Start building the detailed market report\nlet html=\"\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Market Overview</h3>\\n      <div class=\\\"summary-stats\\\">\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Total Marketable Animals</div>\\n          <div class=\\\"stat-value\\\">\".concat(totalAnimals,\"</div>\\n        </div>\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Total Market Value</div>\\n          <div class=\\\"stat-value\\\">R \").concat(Object.values(marketValueBySpecies).reduce((sum,value)=>sum+value,0).toLocaleString(),\"</div>\\n        </div>\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Average Value Per Animal</div>\\n          <div class=\\\"stat-value\\\">R \").concat((Object.values(marketValueBySpecies).reduce((sum,value)=>sum+value,0)/totalAnimals).toLocaleString(undefined,{maximumFractionDigits:2}),\"</div>\\n        </div>\\n      </div>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Market Value by Species</h3>\\n      <div class=\\\"chart-container\\\">\\n        <svg width=\\\"100%\\\" height=\\\"300\\\" viewBox=\\\"0 0 500 300\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\">\\n          <!-- Generate bar chart for market value by species -->\\n          \").concat(generateSVGBarChart(Object.entries(marketValueBySpecies).map((_ref18,index)=>{let[species,value]=_ref18;return{name:species,value:value,color:\"hsl(\".concat(index*40,\", 70%, 50%)\")};}),'Market Value by Species',value=>\"R\".concat(Math.round(value).toLocaleString())),\"\\n        </svg>\\n        <div class=\\\"chart-legend\\\">\\n          \").concat(Object.entries(marketValueBySpecies).map((_ref19,index)=>{let[species,value]=_ref19;return\"\\n            <div class=\\\"legend-item\\\">\\n              <span class=\\\"legend-color\\\" style=\\\"background-color: hsl(\".concat(index*40,\", 70%, 50%);\\\"></span>\\n              <span class=\\\"legend-label\\\">\").concat(species,\": R\").concat(value.toLocaleString(),\"</span>\\n            </div>\\n          \");}).join(''),\"\\n        </div>\\n      </div>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Market Price Trends</h3>\\n      <div class=\\\"chart-container\\\">\\n        <svg width=\\\"100%\\\" height=\\\"300\\\" viewBox=\\\"0 0 500 300\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\">\\n          <!-- Generate bar chart for market price trends -->\\n          \").concat(generateSVGBarChart(Object.entries(marketTrends).map((_ref20,index)=>{let[species,data]=_ref20;return{name:species,value:data.change,color:data.change>=0?'#4caf50':'#f44336'// Green for positive, red for negative\n};}),'Market Price Change (%)',value=>\"\".concat(value>0?'+':'').concat(value.toFixed(1),\"%\")),\"\\n        </svg>\\n        <div class=\\\"chart-legend\\\">\\n          \").concat(Object.entries(marketTrends).map(_ref21=>{let[species,data]=_ref21;return\"\\n            <div class=\\\"legend-item\\\">\\n              <span class=\\\"legend-color\\\" style=\\\"background-color: \".concat(data.change>=0?'#4caf50':'#f44336',\";\\\"></span>\\n              <span class=\\\"legend-label\\\">\").concat(species,\": Current R\").concat(data.current.toLocaleString(),\" (\").concat(data.change>0?'+':'').concat(data.change.toFixed(2),\"%)</span>\\n            </div>\\n          \");}).join(''),\"\\n        </div>\\n      </div>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Market Price Comparison</h3>\\n      <table>\\n        <thead>\\n          <tr>\\n            <th>Species</th>\\n            <th>Current Price (R)</th>\\n            <th>Previous Price (R)</th>\\n            <th>Change (%)</th>\\n            <th>Trend</th>\\n          </tr>\\n        </thead>\\n        <tbody>\\n  \");// Add rows for each species\nObject.entries(marketTrends).forEach(_ref22=>{let[species,data]=_ref22;html+=\"\\n      <tr>\\n        <td>\".concat(species,\"</td>\\n        <td>R \").concat(data.current.toLocaleString(undefined,{maximumFractionDigits:2}),\"</td>\\n        <td>R \").concat(data.previous.toLocaleString(undefined,{maximumFractionDigits:2}),\"</td>\\n        <td>\").concat(data.change>0?'+':'').concat(data.change.toFixed(2),\"%</td>\\n        <td>\").concat(data.change>5?'↑ Strong Increase':data.change>0?'↗ Slight Increase':data.change>-5?'↘ Slight Decrease':'↓ Strong Decrease',\"</td>\\n      </tr>\\n    \");});// Close the table\nhtml+=\"\\n        </tbody>\\n      </table>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Market Ready Animals</h3>\\n      <table>\\n        <thead>\\n          <tr>\\n            <th>Tag</th>\\n            <th>Name</th>\\n            <th>Species</th>\\n            <th>Age</th>\\n            <th>Weight</th>\\n            <th>Health Status</th>\\n            <th>Estimated Value (R)</th>\\n          </tr>\\n        </thead>\\n        <tbody>\\n  \";// Add rows for market-ready animals (simplified criteria)\nconst marketReadyAnimals=animals.filter(animal=>animal.status==='Active'&&animal.healthStatus==='healthy').slice(0,15);// Limit to 15 animals for brevity\nmarketReadyAnimals.forEach(animal=>{const ageInMonths=animal.birthDate?Math.floor((new Date().getTime()-new Date(animal.birthDate).getTime())/(30*24*60*60*1000)):24;const estimatedValue=animal.purchasePrice?animal.purchasePrice*1.2:5000;html+=\"\\n      <tr>\\n        <td>\".concat(animal.tagNumber,\"</td>\\n        <td>\").concat(animal.name,\"</td>\\n        <td>\").concat(animal.species||animal.type,\"</td>\\n        <td>\").concat(ageInMonths,\" months</td>\\n        <td>\").concat(animal.weight||'---',\" kg</td>\\n        <td>\").concat(animal.healthStatus,\"</td>\\n        <td>R \").concat(estimatedValue.toLocaleString(undefined,{maximumFractionDigits:2}),\"</td>\\n      </tr>\\n    \");});// Close the table\nhtml+=\"\\n        </tbody>\\n      </table>\\n    </div>\\n  \";return html;};/**\n * Generate an HTML table for performance data\n */const generatePerformanceTable=animals=>{// Simplified implementation for performance data\nreturn generateHealthTable(animals);};/**\n * Generate a detailed performance report with comprehensive performance data and visualizations\n */const generateDetailedPerformanceReport=animals=>{// Calculate performance statistics\nconst totalAnimals=animals.length;const speciesPerformance={};const performanceTrends={'Growth Rate':[3.2,3.5,3.8,4.0,4.2,4.1],'Feed Conversion':[2.1,2.0,1.9,1.8,1.7,1.7],'Production Yield':[85,87,89,90,92,93]};// Process animals for performance data\nanimals.forEach(animal=>{if(animal.species){if(!speciesPerformance[animal.species]){speciesPerformance[animal.species]={count:0,avgGrowth:0,avgProduction:0};}speciesPerformance[animal.species].count++;// Generate random performance metrics for demonstration\nconst growthRate=2+Math.random()*3;// Between 2 and 5\nconst productionRate=70+Math.random()*30;// Between 70 and 100\n// Update averages\nspeciesPerformance[animal.species].avgGrowth=(speciesPerformance[animal.species].avgGrowth*(speciesPerformance[animal.species].count-1)+growthRate)/speciesPerformance[animal.species].count;speciesPerformance[animal.species].avgProduction=(speciesPerformance[animal.species].avgProduction*(speciesPerformance[animal.species].count-1)+productionRate)/speciesPerformance[animal.species].count;}});// Start building the detailed performance report\nlet html=\"\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Performance Overview</h3>\\n      <div class=\\\"summary-stats\\\">\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Total Animals</div>\\n          <div class=\\\"stat-value\\\">\".concat(totalAnimals,\"</div>\\n        </div>\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Average Growth Rate</div>\\n          <div class=\\\"stat-value\\\">\").concat(Object.values(speciesPerformance).reduce((sum,data)=>sum+data.avgGrowth*data.count,0)/totalAnimals||0,\"%</div>\\n        </div>\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Average Production</div>\\n          <div class=\\\"stat-value\\\">\").concat(Object.values(speciesPerformance).reduce((sum,data)=>sum+data.avgProduction*data.count,0)/totalAnimals||0,\"%</div>\\n        </div>\\n      </div>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Performance Trends</h3>\\n      <div class=\\\"chart-container\\\">\\n        <svg width=\\\"100%\\\" height=\\\"350\\\" viewBox=\\\"0 0 500 350\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\">\\n          <!-- Generate line chart for performance trends -->\\n          \").concat(generateSVGLineChart(Object.entries(performanceTrends).map(_ref23=>{let[metric,values]=_ref23;return{name:metric,values:values.map((value,index)=>{// Generate month labels (last 6 months)\nconst date=new Date();date.setMonth(date.getMonth()-(5-index));const month=date.toLocaleString('en-ZA',{month:'short'});let color='#4caf50';// Default green\nif(metric==='Feed Conversion')color='#f44336';// Red\nif(metric==='Production Yield')color='#2196f3';// Blue\nreturn{key:month,value:value,color:color};})};}),'Performance Trends Over Time',value=>value.toString()),\"\\n        </svg>\\n        <div class=\\\"chart-legend\\\">\\n          \").concat(Object.entries(performanceTrends).map(_ref24=>{let[metric,values]=_ref24;let color='#4caf50';// Default green\nif(metric==='Feed Conversion')color='#f44336';// Red\nif(metric==='Production Yield')color='#2196f3';// Blue\nreturn\"\\n              <div class=\\\"legend-item\\\">\\n                <span class=\\\"legend-color\\\" style=\\\"background-color: \".concat(color,\";\\\"></span>\\n                <span class=\\\"legend-label\\\">\").concat(metric,\": Current \").concat(values[values.length-1],\"</span>\\n              </div>\\n            \");}).join(''),\"\\n        </div>\\n      </div>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Performance by Species</h3>\\n      <table>\\n        <thead>\\n          <tr>\\n            <th>Species</th>\\n            <th>Count</th>\\n            <th>Avg Growth Rate (%)</th>\\n            <th>Avg Production (%)</th>\\n            <th>Feed Conversion Ratio</th>\\n            <th>Performance Rating</th>\\n          </tr>\\n        </thead>\\n        <tbody>\\n  \");// Add rows for each species\nObject.entries(speciesPerformance).forEach(_ref25=>{let[species,data]=_ref25;// Generate a mock feed conversion ratio\nconst fcr=1.5+Math.random()*1.0;// Between 1.5 and 2.5\n// Calculate a performance rating (1-10)\nconst performanceRating=Math.round(10-fcr+data.avgGrowth/5+data.avgProduction/10);html+=\"\\n      <tr>\\n        <td>\".concat(species,\"</td>\\n        <td>\").concat(data.count,\"</td>\\n        <td>\").concat(data.avgGrowth.toFixed(2),\"%</td>\\n        <td>\").concat(data.avgProduction.toFixed(2),\"%</td>\\n        <td>\").concat(fcr.toFixed(2),\"</td>\\n        <td>\").concat(performanceRating,\"/10</td>\\n      </tr>\\n    \");});// Close the table\nhtml+=\"\\n        </tbody>\\n      </table>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Top Performing Animals</h3>\\n      <table>\\n        <thead>\\n          <tr>\\n            <th>Tag</th>\\n            <th>Name</th>\\n            <th>Species</th>\\n            <th>Age</th>\\n            <th>Growth Rate (%)</th>\\n            <th>Production (%)</th>\\n            <th>Performance Rating</th>\\n          </tr>\\n        </thead>\\n        <tbody>\\n  \";// Generate mock performance data for individual animals\nconst animalPerformance=animals.map(animal=>{const growthRate=2+Math.random()*3;// Between 2 and 5\nconst productionRate=70+Math.random()*30;// Between 70 and 100\nconst performanceRating=Math.round(growthRate*1.5+productionRate/10);return _objectSpread(_objectSpread({},animal),{},{growthRate,productionRate,performanceRating});});// Sort by performance rating and take top 10\nconst topPerformers=[...animalPerformance].sort((a,b)=>b.performanceRating-a.performanceRating).slice(0,10);topPerformers.forEach(animal=>{const ageInMonths=animal.birthDate?Math.floor((new Date().getTime()-new Date(animal.birthDate).getTime())/(30*24*60*60*1000)):24;html+=\"\\n      <tr>\\n        <td>\".concat(animal.tagNumber,\"</td>\\n        <td>\").concat(animal.name,\"</td>\\n        <td>\").concat(animal.species||animal.type,\"</td>\\n        <td>\").concat(ageInMonths,\" months</td>\\n        <td>\").concat(animal.growthRate.toFixed(2),\"%</td>\\n        <td>\").concat(animal.productionRate.toFixed(2),\"%</td>\\n        <td>\").concat(animal.performanceRating,\"/10</td>\\n      </tr>\\n    \");});// Close the table\nhtml+=\"\\n        </tbody>\\n      </table>\\n    </div>\\n  \";return html;};/**\n * Generate an HTML table for analysis data\n */const generateAnalysisTable=animals=>{// Simplified implementation for analysis data\nreturn generateAnimalTable(animals);};/**\n * Generate a detailed analysis report with comprehensive data analysis and visualizations\n */const generateDetailedAnalysisReport=animals=>{// Calculate analysis statistics\nconst totalAnimals=animals.length;const speciesCounts={};const statusCounts={};const healthCounts={};const locationCounts={};// Process animals for analysis data\nanimals.forEach(animal=>{// Count by species\nif(animal.species){speciesCounts[animal.species]=(speciesCounts[animal.species]||0)+1;}// Count by status\nif(animal.status){statusCounts[animal.status]=(statusCounts[animal.status]||0)+1;}// Count by health status\nif(animal.healthStatus){healthCounts[animal.healthStatus]=(healthCounts[animal.healthStatus]||0)+1;}// Count by location\nif(animal.location){locationCounts[animal.location]=(locationCounts[animal.location]||0)+1;}});// Calculate age distribution\nconst ageGroups={'Under 1 year':0,'1-2 years':0,'2-3 years':0,'3-5 years':0,'Over 5 years':0};animals.forEach(animal=>{if(animal.birthDate){const ageInMonths=Math.floor((new Date().getTime()-new Date(animal.birthDate).getTime())/(30*24*60*60*1000));if(ageInMonths<12){ageGroups['Under 1 year']++;}else if(ageInMonths<24){ageGroups['1-2 years']++;}else if(ageInMonths<36){ageGroups['2-3 years']++;}else if(ageInMonths<60){ageGroups['3-5 years']++;}else{ageGroups['Over 5 years']++;}}});// Start building the detailed analysis report\nlet html=\"\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Livestock Analysis Overview</h3>\\n      <div class=\\\"summary-stats\\\">\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Total Animals</div>\\n          <div class=\\\"stat-value\\\">\".concat(totalAnimals,\"</div>\\n        </div>\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Species Count</div>\\n          <div class=\\\"stat-value\\\">\").concat(Object.keys(speciesCounts).length,\"</div>\\n        </div>\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Healthy Animals</div>\\n          <div class=\\\"stat-value\\\">\").concat(healthCounts['healthy']||0,\" (\").concat(((healthCounts['healthy']||0)/totalAnimals*100).toFixed(1),\"%)</div>\\n        </div>\\n        <div class=\\\"stat-card\\\">\\n          <div class=\\\"stat-title\\\">Active Animals</div>\\n          <div class=\\\"stat-value\\\">\").concat(statusCounts['Active']||0,\" (\").concat(((statusCounts['Active']||0)/totalAnimals*100).toFixed(1),\"%)</div>\\n        </div>\\n      </div>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Species Distribution</h3>\\n      <div class=\\\"chart-container\\\">\\n        <svg width=\\\"100%\\\" height=\\\"300\\\" viewBox=\\\"0 0 500 300\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\">\\n          <!-- Generate pie chart for species distribution -->\\n          \").concat(generateSVGPieChart(Object.entries(speciesCounts).map((_ref26,index)=>{let[species,count]=_ref26;return{name:species,value:count,color:\"hsl(\".concat(index*40,\", 70%, 50%)\")};}),'Species Distribution'),\"\\n        </svg>\\n        <div class=\\\"chart-legend\\\">\\n          \").concat(Object.entries(speciesCounts).map((_ref27,index)=>{let[species,count]=_ref27;return\"\\n            <div class=\\\"legend-item\\\">\\n              <span class=\\\"legend-color\\\" style=\\\"background-color: hsl(\".concat(index*40,\", 70%, 50%);\\\"></span>\\n              <span class=\\\"legend-label\\\">\").concat(species,\": \").concat(count,\" (\").concat((count/totalAnimals*100).toFixed(1),\"%)</span>\\n            </div>\\n          \");}).join(''),\"\\n        </div>\\n      </div>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Health Status Distribution</h3>\\n      <div class=\\\"chart-container\\\">\\n        <svg width=\\\"100%\\\" height=\\\"300\\\" viewBox=\\\"0 0 500 300\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\">\\n          <!-- Generate pie chart for health status distribution -->\\n          \").concat(generateSVGPieChart(Object.entries(healthCounts).map(_ref28=>{let[status,count]=_ref28;return{name:status,value:count,color:getHealthStatusColor(status)};}),'Health Status Distribution'),\"\\n        </svg>\\n        <div class=\\\"chart-legend\\\">\\n          \").concat(Object.entries(healthCounts).map(_ref29=>{let[status,count]=_ref29;return\"\\n            <div class=\\\"legend-item\\\">\\n              <span class=\\\"legend-color\\\" style=\\\"background-color: \".concat(getHealthStatusColor(status),\";\\\"></span>\\n              <span class=\\\"legend-label\\\">\").concat(status,\": \").concat(count,\" (\").concat((count/totalAnimals*100).toFixed(1),\"%)</span>\\n            </div>\\n          \");}).join(''),\"\\n        </div>\\n      </div>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Age Distribution</h3>\\n      <div class=\\\"chart-container\\\">\\n        <svg width=\\\"100%\\\" height=\\\"300\\\" viewBox=\\\"0 0 500 300\\\" xmlns=\\\"http://www.w3.org/2000/svg\\\">\\n          <!-- Generate bar chart for age distribution -->\\n          \").concat(generateSVGBarChart(Object.entries(ageGroups).map((_ref30,index)=>{let[group,count]=_ref30;return{name:group,value:count,color:\"hsl(\".concat(200+index*30,\", 70%, 50%)\")};}),'Age Distribution',value=>value.toString()),\"\\n        </svg>\\n        <div class=\\\"chart-legend\\\">\\n          \").concat(Object.entries(ageGroups).map((_ref31,index)=>{let[group,count]=_ref31;return\"\\n            <div class=\\\"legend-item\\\">\\n              <span class=\\\"legend-color\\\" style=\\\"background-color: hsl(\".concat(200+index*30,\", 70%, 50%);\\\"></span>\\n              <span class=\\\"legend-label\\\">\").concat(group,\": \").concat(count,\" (\").concat((count/totalAnimals*100).toFixed(1),\"%)</span>\\n            </div>\\n          \");}).join(''),\"\\n        </div>\\n      </div>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Location Distribution</h3>\\n      <table>\\n        <thead>\\n          <tr>\\n            <th>Location</th>\\n            <th>Animal Count</th>\\n            <th>Percentage</th>\\n            <th>Species Breakdown</th>\\n          </tr>\\n        </thead>\\n        <tbody>\\n  \");// Add rows for each location\nObject.entries(locationCounts).forEach(_ref32=>{let[location,count]=_ref32;// Calculate species breakdown for this location\nconst speciesBreakdown={};animals.filter(animal=>animal.location===location).forEach(animal=>{if(animal.species){speciesBreakdown[animal.species]=(speciesBreakdown[animal.species]||0)+1;}});const speciesBreakdownStr=Object.entries(speciesBreakdown).map(_ref33=>{let[species,speciesCount]=_ref33;return\"\".concat(species,\": \").concat(speciesCount);}).join(', ');html+=\"\\n      <tr>\\n        <td>\".concat(location,\"</td>\\n        <td>\").concat(count,\"</td>\\n        <td>\").concat((count/totalAnimals*100).toFixed(1),\"%</td>\\n        <td>\").concat(speciesBreakdownStr,\"</td>\\n      </tr>\\n    \");});// Close the table\nhtml+=\"\\n        </tbody>\\n      </table>\\n    </div>\\n\\n    <div class=\\\"report-section\\\">\\n      <h3 class=\\\"section-title\\\">Comprehensive Animal Analysis</h3>\\n      <table>\\n        <thead>\\n          <tr>\\n            <th>Species</th>\\n            <th>Count</th>\\n            <th>Avg Age (months)</th>\\n            <th>Health Rate</th>\\n            <th>Active Rate</th>\\n            <th>Primary Location</th>\\n          </tr>\\n        </thead>\\n        <tbody>\\n  \";// Add rows for each species with detailed analysis\nObject.entries(speciesCounts).forEach(_ref34=>{var _Object$entries$sort$;let[species,count]=_ref34;const speciesAnimals=animals.filter(animal=>animal.species===species);// Calculate average age\nconst totalAgeInMonths=speciesAnimals.reduce((sum,animal)=>{if(animal.birthDate){return sum+Math.floor((new Date().getTime()-new Date(animal.birthDate).getTime())/(30*24*60*60*1000));}return sum+24;// Default to 24 months if no birth date\n},0);const avgAge=totalAgeInMonths/count;// Calculate health rate\nconst healthyCount=speciesAnimals.filter(animal=>animal.healthStatus==='healthy').length;const healthRate=healthyCount/count*100;// Calculate active rate\nconst activeCount=speciesAnimals.filter(animal=>animal.status==='Active').length;const activeRate=activeCount/count*100;// Find primary location\nconst locationCounts={};speciesAnimals.forEach(animal=>{if(animal.location){locationCounts[animal.location]=(locationCounts[animal.location]||0)+1;}});const primaryLocation=((_Object$entries$sort$=Object.entries(locationCounts).sort((a,b)=>b[1]-a[1])[0])===null||_Object$entries$sort$===void 0?void 0:_Object$entries$sort$[0])||'Unknown';html+=\"\\n      <tr>\\n        <td>\".concat(species,\"</td>\\n        <td>\").concat(count,\"</td>\\n        <td>\").concat(avgAge.toFixed(1),\"</td>\\n        <td>\").concat(healthRate.toFixed(1),\"%</td>\\n        <td>\").concat(activeRate.toFixed(1),\"%</td>\\n        <td>\").concat(primaryLocation,\"</td>\\n      </tr>\\n    \");});// Close the table\nhtml+=\"\\n        </tbody>\\n      </table>\\n    </div>\\n  \";return html;};// Helper function getHealthStatusColor is already defined above\n// The generateSVGPieChart, generateSVGBarChart, and generateSVGLineChart functions are already defined earlier in the file\nexport const getSavedReports=async type=>{try{// In a real implementation, this would call the backend API\n// For now, we'll return mock data\nawait new Promise(resolve=>setTimeout(resolve,800));// Mock saved reports data\nconst mockReports=[{id:'1',name:\"\".concat(type.charAt(0).toUpperCase()+type.slice(1),\" Report - Monthly\"),createdAt:new Date().toISOString(),format:'pdf',size:'1.2 MB'},{id:'2',name:\"\".concat(type.charAt(0).toUpperCase()+type.slice(1),\" Report - Quarterly\"),createdAt:new Date(Date.now()-7*24*60*60*1000).toISOString(),format:'excel',size:'3.5 MB'},{id:'3',name:\"\".concat(type.charAt(0).toUpperCase()+type.slice(1),\" Report - Annual\"),createdAt:new Date(Date.now()-30*24*60*60*1000).toISOString(),format:'pdf',size:'5.8 MB'},{id:'4',name:\"Custom \".concat(type.charAt(0).toUpperCase()+type.slice(1),\" Analysis\"),createdAt:new Date(Date.now()-2*24*60*60*1000).toISOString(),format:'csv',size:'0.9 MB'}];return mockReports;}catch(error){console.error(\"Error fetching saved \".concat(type,\" reports:\"),error);throw error;}};", "map": {"version": 3, "names": ["API_BASE_URL", "showSnackbar", "generateReport", "params", "format", "htmlContent", "generateHtmlReport", "filename", "concat", "type", "downloadFile", "Promise", "resolve", "setTimeout", "mockResponse", "success", "downloadUrl", "Date", "now", "error", "console", "url", "content", "blob", "Blob", "getContentType", "link", "document", "createElement", "href", "URL", "createObjectURL", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "mockContent", "toLocaleString", "endsWith", "reportDate", "toLocaleDateString", "year", "month", "day", "hour", "minute", "getReportTitle", "startDate", "endDate", "getDateRange", "timePeriod", "animals", "feedingRecords", "feedInventory", "healthRecords", "breedingRecords", "financialRecords", "animalsCollection", "getCollection", "feedingRecordsCollection", "feedInventoryCollection", "healthRecordsCollection", "breedingRecordsCollection", "financialRecordsCollection", "animalsCursor", "find", "toArray", "feedingRecordsCursor", "date", "$gte", "$lte", "feedInventoryCursor", "healthRecordsCursor", "breedingRecordsCursor", "financialRecordsCursor", "log", "mockAnimals", "mockFeedingRecords", "mockHealthRecords", "mockBreedingRecords", "mockFinancialRecords", "totalAnimals", "length", "speciesCounts", "statusCounts", "healthCounts", "for<PERSON>ach", "animal", "species", "status", "healthStatus", "feedUsageByType", "record", "feedType", "quantity", "totalFeedCost", "reduce", "sum", "totalCost", "costByAnimalGroup", "animalGroup", "animalGroupId", "totalRevenue", "filter", "amount", "totalExpenses", "netProfit", "roi", "html", "toFixed", "Object", "entries", "slice", "map", "_ref", "join", "generateSV<PERSON>ie<PERSON>hart", "_ref2", "index", "count", "name", "value", "color", "_ref3", "_ref4", "getHealthStatusColor", "_ref5", "unit", "generateDetailedHealthReport", "generateDetailedFinancialReport", "generateDetailedMarketReport", "generateDetailedPerformanceReport", "generateDetailedAnalysisReport", "generateAnimalTable", "errorMessage", "Error", "message", "JSON", "stringify", "tagNumber", "breed", "gender", "birthDate", "location", "generateHealthTable", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Math", "random", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notes", "healthStatusCounts", "treatmentTypeCounts", "monthlyHealthIssues", "vaccinationStatus", "randomStatus", "_ref6", "_ref7", "generateSVGBarChart", "_ref8", "toString", "_ref9", "values", "a", "b", "generateSVGLineChart", "_ref0", "key", "_ref1", "_ref10", "sortedRecords", "sort", "getTime", "animalInfo", "id", "animalId", "diagnosis", "treatment", "performed<PERSON><PERSON>", "followUpDate", "animalsNeedingAttention", "lastRecord", "r", "data", "title", "total", "item", "centerX", "centerY", "radius", "startAngle", "paths", "labels", "percentage", "endAngle", "PI", "x1", "cos", "y1", "sin", "x2", "y2", "largeArcFlag", "labelAngle", "labelRadius", "labelX", "labelY", "formatValue", "width", "height", "margin", "top", "right", "bottom", "left", "chartWidth", "chartHeight", "maxValue", "max", "d", "<PERSON><PERSON><PERSON><PERSON>", "barSpacing", "bars", "xLabels", "y<PERSON><PERSON><PERSON>", "i", "y", "x", "barHeight", "allValues", "flatMap", "series", "v", "lines", "points", "legend", "seriesIndex", "pathData", "point", "Array", "fill", "_", "statusLower", "toLowerCase", "includes", "getFinancialColor", "typeLower", "startDateStr", "endDateStr", "setDate", "getDate", "setMonth", "getMonth", "setFullYear", "getFullYear", "toISOString", "split", "generateFinancialTable", "purchasePrice", "ageInMonths", "floor", "currentValue", "purchaseDate", "undefined", "minimumFractionDigits", "maximumFractionDigits", "profitMargin", "expensesByCategory", "category", "revenueBySource", "monthlyFinancialData", "revenue", "expenses", "profit", "totalAssetValue", "_ref11", "_ref12", "_ref13", "_ref14", "_ref15", "_ref16", "source", "_ref17", "description", "paymentMethod", "reference", "budgetCategories", "budgeted", "round", "actual", "variance", "variancePercent", "generateMarketTable", "marketValueBySpecies", "marketTrends", "marketValue", "keys", "previousValue", "change", "current", "previous", "_ref18", "_ref19", "_ref20", "_ref21", "_ref22", "marketReadyAnimals", "estimatedValue", "weight", "generatePerformanceTable", "speciesPerformance", "performanceTrends", "avg<PERSON>row<PERSON>", "avgProduction", "growthRate", "productionRate", "_ref23", "metric", "_ref24", "_ref25", "fcr", "performanceRating", "animalPerformance", "_objectSpread", "topPerformers", "generateAnalysisTable", "locationCounts", "ageGroups", "_ref26", "_ref27", "_ref28", "_ref29", "_ref30", "group", "_ref31", "_ref32", "speciesBreakdown", "speciesBreakdownStr", "_ref33", "speciesCount", "_ref34", "_Object$entries$sort$", "speciesAnimals", "totalAgeInMonths", "avgAge", "healthyCount", "healthRate", "activeCount", "activeRate", "primaryLocation", "getSavedReports", "mockReports", "char<PERSON>t", "toUpperCase", "createdAt", "size"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/services/reportService.ts"], "sourcesContent": ["import axios from 'axios';\nimport { API_BASE_URL } from '../config/api';\nimport { Animal } from '../types/animal';\nimport { FeedingRecord, FeedInventory } from '../types/feeding';\nimport { HealthRecord } from '../types/health';\nimport { BreedingRecord } from '../types/breeding';\nimport { FinancialRecord } from '../types/financial';\nimport { formatDate } from '../utils/dateUtils';\n// MongoDB collection import removed - using API calls instead\nimport { showSnackbar } from '../utils/snackbar';\n\n// Define report types\nexport type ReportType = 'analysis' | 'performance' | 'health' | 'market' | 'custom' | 'financial' | 'breeding' | 'feed';\nexport type ReportFormat = 'pdf' | 'excel' | 'csv' | 'html';\nexport type TimePeriod = 'week' | 'month' | 'quarter' | 'year' | 'custom';\n\nexport interface ReportParams {\n  type: ReportType;\n  format: ReportFormat;\n  timePeriod: TimePeriod;\n  startDate?: string;\n  endDate?: string;\n  filters?: Record<string, any>;\n  customFields?: string[];\n}\n\n/**\n * Generate and download a report\n * @param params Report parameters\n * @returns Promise with the download URL\n */\nexport const generateReport = async (params: ReportParams): Promise<string> => {\n  try {\n    // If HTML format is requested, generate it directly in the browser\n    if (params.format === 'html') {\n      const htmlContent = await generateHtmlReport(params);\n      const filename = `${params.type}_report.html`;\n\n      // Download the HTML file\n      downloadFile('', filename, htmlContent);\n\n      return 'Generated HTML report';\n    }\n\n    // For other formats, in a real implementation, this would call the backend API\n    // For now, we'll simulate a successful response\n\n    // Simulate API call delay\n    await new Promise(resolve => setTimeout(resolve, 1500));\n\n    // Mock response with download URL\n    const mockResponse = {\n      success: true,\n      downloadUrl: `${API_BASE_URL}/reports/download/${params.type}_${Date.now()}.${params.format === 'excel' ? 'xlsx' : params.format}`\n    };\n\n    // Trigger file download\n    downloadFile(mockResponse.downloadUrl, `${params.type}_report.${params.format === 'excel' ? 'xlsx' : params.format}`);\n\n    return mockResponse.downloadUrl;\n  } catch (error) {\n    console.error('Error generating report:', error);\n    throw error;\n  }\n};\n\n/**\n * Helper function to trigger file download\n * @param url URL of the file to download\n * @param filename Name to save the file as\n */\nexport const downloadFile = (url: string, filename: string, content?: string): void => {\n  // If content is provided, use it directly\n  if (content) {\n    const blob = new Blob([content], { type: getContentType(filename) });\n    const link = document.createElement('a');\n    link.href = URL.createObjectURL(blob);\n    link.download = filename;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(link.href);\n    return;\n  }\n\n  // Otherwise, in a real implementation, this would download the file from the URL\n  // For now, we'll create a mock file with some content\n\n  // Create a blob with mock content\n  const mockContent = `This is a mock ${filename} file generated at ${new Date().toLocaleString()}`;\n  const blob = new Blob([mockContent], { type: getContentType(filename) });\n\n  // Create a download link and trigger the download\n  const link = document.createElement('a');\n  link.href = URL.createObjectURL(blob);\n  link.download = filename;\n  document.body.appendChild(link);\n  link.click();\n  document.body.removeChild(link);\n  URL.revokeObjectURL(link.href);\n};\n\n/**\n * Get the content type based on file extension\n * @param filename Filename with extension\n * @returns Content type string\n */\nconst getContentType = (filename: string): string => {\n  if (filename.endsWith('.pdf')) {\n    return 'application/pdf';\n  } else if (filename.endsWith('.xlsx')) {\n    return 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';\n  } else if (filename.endsWith('.csv')) {\n    return 'text/csv';\n  } else if (filename.endsWith('.html')) {\n    return 'text/html';\n  }\n  return 'text/plain';\n};\n\n/**\n * Get saved reports for a specific type\n * @param type Report type\n * @returns Promise with the list of saved reports\n */\n/**\n * Generate an HTML report based on the provided parameters\n * @param params Report parameters\n * @returns Promise with the HTML content\n */\nexport const generateHtmlReport = async (params: ReportParams): Promise<string> => {\n  try {\n    // Get the current date and time for the report\n    const reportDate = new Date().toLocaleDateString('en-ZA', {\n      year: 'numeric',\n      month: 'long',\n      day: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n\n    // Get the report title based on type\n    const getReportTitle = () => {\n      switch (params.type) {\n        case 'analysis': return 'Analysis Report';\n        case 'performance': return 'Performance Report';\n        case 'health': return 'Health Report';\n        case 'market': return 'Market Report';\n        case 'financial': return 'Financial Report';\n        case 'custom': return 'Custom Report';\n        default: return 'MayCaiphus Livestock Report';\n      }\n    };\n\n    // Get date range based on time period\n    const { startDate, endDate } = getDateRange(params.timePeriod, params.startDate, params.endDate);\n\n    // Fetch data from MongoDB collections\n    let animals: any[] = [];\n    let feedingRecords: any[] = [];\n    let feedInventory: any[] = [];\n    let healthRecords: any[] = [];\n    let breedingRecords: any[] = [];\n    let financialRecords: any[] = [];\n\n    try {\n      // Try to fetch data from MongoDB collections\n      const animalsCollection = await getCollection('animals');\n      const feedingRecordsCollection = await getCollection('feeding_records');\n      const feedInventoryCollection = await getCollection('feed_inventory');\n      const healthRecordsCollection = await getCollection('health_records');\n      const breedingRecordsCollection = await getCollection('breeding_records');\n      const financialRecordsCollection = await getCollection('financial_records');\n\n      // Safely fetch data with error handling\n      try {\n        const animalsCursor = animalsCollection.find({});\n        animals = animalsCursor && typeof animalsCursor.toArray === 'function'\n          ? await animalsCursor.toArray()\n          : [];\n      } catch (error) {\n        console.error('Error fetching animals:', error);\n        animals = [];\n      }\n\n      try {\n        const feedingRecordsCursor = feedingRecordsCollection.find({\n          date: { $gte: new Date(startDate), $lte: new Date(endDate) }\n        });\n        feedingRecords = feedingRecordsCursor && typeof feedingRecordsCursor.toArray === 'function'\n          ? await feedingRecordsCursor.toArray()\n          : [];\n      } catch (error) {\n        console.error('Error fetching feeding records:', error);\n        feedingRecords = [];\n      }\n\n      try {\n        const feedInventoryCursor = feedInventoryCollection.find({});\n        feedInventory = feedInventoryCursor && typeof feedInventoryCursor.toArray === 'function'\n          ? await feedInventoryCursor.toArray()\n          : [];\n      } catch (error) {\n        console.error('Error fetching feed inventory:', error);\n        feedInventory = [];\n      }\n\n      try {\n        const healthRecordsCursor = healthRecordsCollection.find({\n          date: { $gte: new Date(startDate), $lte: new Date(endDate) }\n        });\n        healthRecords = healthRecordsCursor && typeof healthRecordsCursor.toArray === 'function'\n          ? await healthRecordsCursor.toArray()\n          : [];\n      } catch (error) {\n        console.error('Error fetching health records:', error);\n        healthRecords = [];\n      }\n\n      try {\n        const breedingRecordsCursor = breedingRecordsCollection.find({\n          date: { $gte: new Date(startDate), $lte: new Date(endDate) }\n        });\n        breedingRecords = breedingRecordsCursor && typeof breedingRecordsCursor.toArray === 'function'\n          ? await breedingRecordsCursor.toArray()\n          : [];\n      } catch (error) {\n        console.error('Error fetching breeding records:', error);\n        breedingRecords = [];\n      }\n\n      try {\n        const financialRecordsCursor = financialRecordsCollection.find({\n          date: { $gte: new Date(startDate), $lte: new Date(endDate) }\n        });\n        financialRecords = financialRecordsCursor && typeof financialRecordsCursor.toArray === 'function'\n          ? await financialRecordsCursor.toArray()\n          : [];\n      } catch (error) {\n        console.error('Error fetching financial records:', error);\n        financialRecords = [];\n      }\n    } catch (error) {\n      console.error('Error setting up MongoDB collections:', error);\n      // If there's an error with MongoDB, use mock data\n      console.log('Using mock data for report generation');\n\n      // Import mock data\n      const { mockAnimals } = await import('../mocks/animalData');\n      const { mockFeedingRecords } = await import('../mocks/feedingData');\n      const { mockHealthRecords } = await import('../mocks/healthData');\n      const { mockBreedingRecords } = await import('../mocks/breedingData');\n      const { mockFinancialRecords } = await import('../mocks/financialData');\n\n      animals = mockAnimals || [];\n      feedingRecords = mockFeedingRecords || [];\n      feedInventory = [];\n      healthRecords = mockHealthRecords || [];\n      breedingRecords = mockBreedingRecords || [];\n      financialRecords = mockFinancialRecords || [];\n    }\n\n    // Generate summary statistics\n    const totalAnimals = animals.length;\n    const speciesCounts: Record<string, number> = {};\n    const statusCounts: Record<string, number> = {};\n    const healthCounts: Record<string, number> = {};\n\n    animals.forEach(animal => {\n      // Count by species\n      if (animal.species) {\n        speciesCounts[animal.species] = (speciesCounts[animal.species] || 0) + 1;\n      }\n\n      // Count by status\n      statusCounts[animal.status] = (statusCounts[animal.status] || 0) + 1;\n\n      // Count by health status\n      healthCounts[animal.healthStatus] = (healthCounts[animal.healthStatus] || 0) + 1;\n    });\n\n    // Calculate feed usage by type\n    const feedUsageByType: Record<string, number> = {};\n    feedingRecords.forEach(record => {\n      const feedType = record.feedType;\n      if (!feedUsageByType[feedType]) {\n        feedUsageByType[feedType] = 0;\n      }\n      feedUsageByType[feedType] += record.quantity;\n    });\n\n    // Calculate total feed cost\n    const totalFeedCost = feedingRecords.reduce((sum, record) => sum + (record.totalCost || 0), 0);\n\n    // Calculate cost by animal group\n    const costByAnimalGroup: Record<string, number> = {};\n    feedingRecords.forEach(record => {\n      const animalGroup = record.animalGroupId;\n      if (!costByAnimalGroup[animalGroup]) {\n        costByAnimalGroup[animalGroup] = 0;\n      }\n      costByAnimalGroup[animalGroup] += record.totalCost || 0;\n    });\n\n    // Calculate financial summary\n    const totalRevenue = financialRecords\n      .filter(record => record.type === 'income')\n      .reduce((sum, record) => sum + record.amount, 0);\n\n    const totalExpenses = financialRecords\n      .filter(record => record.type === 'expense')\n      .reduce((sum, record) => sum + record.amount, 0);\n\n    const netProfit = totalRevenue - totalExpenses;\n    const roi = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;\n\n  // Start building HTML\n  let html = `\n    <!DOCTYPE html>\n    <html lang=\"en\">\n    <head>\n      <meta charset=\"UTF-8\">\n      <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n      <title>${getReportTitle()}</title>\n      <style>\n        /* Enhanced Report Styles */\n        body {\n          font-family: 'Roboto', Arial, sans-serif;\n          line-height: 1.6;\n          color: #333;\n          margin: 0;\n          padding: 0;\n          background-color: #f9f9f9;\n        }\n\n        .report-container {\n          max-width: 1200px;\n          margin: 30px auto;\n          padding: 30px;\n          background-color: #fff;\n          box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\n          border-radius: 8px;\n        }\n\n        .report-header {\n          text-align: center;\n          margin-bottom: 40px;\n          padding-bottom: 30px;\n          border-bottom: 2px solid #f0f0f0;\n        }\n\n        .report-title {\n          font-size: 28px;\n          font-weight: 700;\n          margin: 0;\n          color: #2c3e50;\n          letter-spacing: -0.5px;\n        }\n\n        .report-subtitle {\n          font-size: 20px;\n          color: #3498db;\n          margin: 10px 0;\n          font-weight: 500;\n        }\n\n        .report-date {\n          font-size: 14px;\n          color: #7f8c8d;\n          font-style: italic;\n        }\n\n        .report-section {\n          margin-bottom: 40px;\n        }\n\n        .section-title {\n          font-size: 22px;\n          color: #2980b9;\n          border-bottom: 3px solid #3498db;\n          padding-bottom: 10px;\n          margin-bottom: 20px;\n          font-weight: 600;\n        }\n\n        .summary-stats {\n          display: flex;\n          flex-wrap: wrap;\n          gap: 20px;\n          margin-bottom: 30px;\n        }\n\n        .stat-card {\n          background-color: #f8f9fa;\n          border-radius: 8px;\n          padding: 20px;\n          min-width: 220px;\n          box-shadow: 0 3px 10px rgba(0, 0, 0, 0.08);\n          transition: transform 0.2s ease, box-shadow 0.2s ease;\n        }\n\n        .stat-card:hover {\n          transform: translateY(-5px);\n          box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);\n        }\n\n        .stat-title {\n          font-size: 16px;\n          color: #7f8c8d;\n          margin-bottom: 8px;\n          font-weight: 500;\n        }\n\n        .stat-value {\n          font-size: 28px;\n          font-weight: 700;\n          color: #2c3e50;\n        }\n\n        table {\n          width: 100%;\n          border-collapse: collapse;\n          margin-bottom: 30px;\n          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);\n          border-radius: 8px;\n          overflow: hidden;\n        }\n\n        th, td {\n          padding: 15px 18px;\n          text-align: left;\n          border-bottom: 1px solid #e0e0e0;\n        }\n\n        th {\n          background-color: #f2f8fd;\n          font-weight: 600;\n          color: #2c3e50;\n          font-size: 14px;\n          text-transform: uppercase;\n          letter-spacing: 0.5px;\n        }\n\n        tr:last-child td {\n          border-bottom: none;\n        }\n\n        tr:hover {\n          background-color: #f5f9ff;\n        }\n\n        .chart-container {\n          margin-bottom: 40px;\n          text-align: center;\n          background-color: #f8f9fa;\n          border-radius: 12px;\n          padding: 25px;\n          box-shadow: 0 3px 15px rgba(0, 0, 0, 0.08);\n        }\n\n        .chart-legend {\n          display: flex;\n          flex-wrap: wrap;\n          justify-content: center;\n          gap: 20px;\n          margin-top: 25px;\n          padding: 15px;\n          background-color: rgba(255, 255, 255, 0.7);\n          border-radius: 8px;\n        }\n\n        .legend-item {\n          display: flex;\n          align-items: center;\n          font-size: 14px;\n          padding: 5px 10px;\n          background-color: #fff;\n          border-radius: 20px;\n          box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);\n        }\n\n        .legend-color {\n          display: inline-block;\n          width: 18px;\n          height: 18px;\n          margin-right: 8px;\n          border-radius: 4px;\n        }\n\n        .legend-label {\n          color: #333;\n          font-weight: 500;\n        }\n\n        .chart-placeholder {\n          background-color: #f8f9fa;\n          border: 1px dashed #ccc;\n          border-radius: 8px;\n          padding: 30px;\n          text-align: center;\n          color: #7f8c8d;\n        }\n\n        .footer {\n          margin-top: 60px;\n          text-align: center;\n          font-size: 13px;\n          color: #95a5a6;\n          padding-top: 30px;\n          border-top: 1px solid #eee;\n        }\n\n        /* SVG Chart Styles */\n        svg {\n          max-width: 100%;\n          height: auto;\n          background-color: white;\n          border-radius: 8px;\n          box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);\n        }\n\n        svg text {\n          font-family: 'Roboto', Arial, sans-serif;\n        }\n\n        svg .chart-title {\n          font-size: 18px;\n          font-weight: 600;\n        }\n\n        svg .axis-label {\n          font-size: 13px;\n        }\n\n        svg .grid-line {\n          stroke: #e0e0e0;\n          stroke-dasharray: 3,3;\n        }\n\n        svg .data-point {\n          stroke-width: 2;\n        }\n\n        svg .data-label {\n          font-size: 12px;\n          text-anchor: middle;\n          font-weight: 500;\n        }\n\n        /* Financial Report Specific Styles */\n        .financial-highlight {\n          color: #2980b9;\n          font-weight: 600;\n        }\n\n        .financial-positive {\n          color: #27ae60;\n          font-weight: 600;\n        }\n\n        .financial-negative {\n          color: #e74c3c;\n          font-weight: 600;\n        }\n\n        .financial-summary-box {\n          background-color: #f8f9fa;\n          border-left: 4px solid #3498db;\n          padding: 20px;\n          margin-bottom: 30px;\n          border-radius: 0 8px 8px 0;\n        }\n\n        .financial-summary-title {\n          font-size: 18px;\n          font-weight: 600;\n          color: #2c3e50;\n          margin-bottom: 15px;\n        }\n\n        .financial-summary-content {\n          display: flex;\n          flex-wrap: wrap;\n          gap: 20px;\n        }\n\n        .financial-summary-item {\n          flex: 1;\n          min-width: 200px;\n        }\n\n        .financial-summary-label {\n          font-size: 14px;\n          color: #7f8c8d;\n          margin-bottom: 5px;\n        }\n\n        .financial-summary-value {\n          font-size: 22px;\n          font-weight: 700;\n          color: #2c3e50;\n        }\n\n        .financial-trend-indicator {\n          display: inline-flex;\n          align-items: center;\n          font-size: 14px;\n          margin-left: 10px;\n        }\n\n        .financial-trend-up {\n          color: #27ae60;\n        }\n\n        .financial-trend-down {\n          color: #e74c3c;\n        }\n\n        /* Print styles */\n        @media print {\n          body {\n            padding: 0;\n            font-size: 12pt;\n            background-color: white;\n          }\n\n          .report-container {\n            box-shadow: none;\n            padding: 0;\n            max-width: 100%;\n            margin: 0;\n          }\n\n          .chart-container {\n            break-inside: avoid;\n            page-break-inside: avoid;\n            box-shadow: none;\n            border: 1px solid #eee;\n          }\n\n          table {\n            break-inside: auto;\n            page-break-inside: auto;\n            box-shadow: none;\n            border: 1px solid #eee;\n          }\n\n          tr {\n            break-inside: avoid;\n            page-break-inside: avoid;\n          }\n\n          .no-print {\n            display: none;\n          }\n\n          .stat-card {\n            box-shadow: none;\n            border: 1px solid #eee;\n          }\n\n          svg {\n            box-shadow: none;\n          }\n        }\n      </style>\n    </head>\n    <body>\n      <div class=\"report-container\">\n        <div class=\"report-header\">\n          <h1 class=\"report-title\">MayCaiphus Livestock Management System</h1>\n          <h2 class=\"report-subtitle\">${getReportTitle()}</h2>\n          <p class=\"report-date\">Generated on: ${reportDate}</p>\n        </div>\n  `;\n\n  // Add summary section\n  html += `\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Summary</h3>\n      <div class=\"summary-stats\">\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Total Animals</div>\n          <div class=\"stat-value\">${totalAnimals}</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Active Animals</div>\n          <div class=\"stat-value\">${statusCounts['Active'] || 0}</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Healthy Animals</div>\n          <div class=\"stat-value\">${healthCounts['healthy'] || 0}</div>\n        </div>\n      </div>\n    </div>\n  `;\n\n  // Add financial summary section if financial report\n  if (params.type === 'financial') {\n    html += `\n      <div class=\"report-section\">\n        <h3 class=\"section-title\">Financial Summary</h3>\n        <div class=\"summary-stats\">\n          <div class=\"stat-card\">\n            <div class=\"stat-title\">Total Revenue</div>\n            <div class=\"stat-value\">R ${totalRevenue.toLocaleString()}</div>\n          </div>\n          <div class=\"stat-card\">\n            <div class=\"stat-title\">Total Expenses</div>\n            <div class=\"stat-value\">R ${totalExpenses.toLocaleString()}</div>\n          </div>\n          <div class=\"stat-card\">\n            <div class=\"stat-title\">Net Profit</div>\n            <div class=\"stat-value\">R ${netProfit.toLocaleString()}</div>\n          </div>\n          <div class=\"stat-card\">\n            <div class=\"stat-title\">ROI</div>\n            <div class=\"stat-value\">${roi.toFixed(2)}%</div>\n          </div>\n        </div>\n      </div>\n    `;\n  }\n\n  // Add feed usage summary section if feed report\n  if (params.type === 'analysis' || params.type === 'performance') {\n    html += `\n      <div class=\"report-section\">\n        <h3 class=\"section-title\">Feed Usage Summary</h3>\n        <div class=\"summary-stats\">\n          <div class=\"stat-card\">\n            <div class=\"stat-title\">Total Feed Cost</div>\n            <div class=\"stat-value\">R ${totalFeedCost.toLocaleString()}</div>\n          </div>\n          ${Object.entries(feedUsageByType).slice(0, 3).map(([feedType, quantity]) => `\n            <div class=\"stat-card\">\n              <div class=\"stat-title\">${feedType}</div>\n              <div class=\"stat-value\">${quantity.toLocaleString()} kg</div>\n            </div>\n          `).join('')}\n        </div>\n      </div>\n    `;\n  }\n\n  // Add charts section\n  html += `\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Charts</h3>\n      <div class=\"chart-container\">\n        <svg width=\"100%\" height=\"300\" viewBox=\"0 0 500 300\" xmlns=\"http://www.w3.org/2000/svg\">\n          <!-- Generate pie chart for species distribution -->\n          ${generateSVGPieChart(\n            Object.entries(speciesCounts).map(([species, count], index) => ({\n              name: species,\n              value: count,\n              color: `hsl(${index * 40}, 70%, 50%)`\n            })),\n            'Species Distribution'\n          )}\n        </svg>\n        <div class=\"chart-legend\">\n          ${Object.entries(speciesCounts).map(([species, count], index) => `\n            <div class=\"legend-item\">\n              <span class=\"legend-color\" style=\"background-color: hsl(${index * 40}, 70%, 50%);\"></span>\n              <span class=\"legend-label\">${species}: ${count} (${(count / totalAnimals * 100).toFixed(1)}%)</span>\n            </div>\n          `).join('')}\n        </div>\n      </div>\n      <div class=\"chart-container\">\n        <svg width=\"100%\" height=\"300\" viewBox=\"0 0 500 300\" xmlns=\"http://www.w3.org/2000/svg\">\n          <!-- Generate pie chart for health status distribution -->\n          ${generateSVGPieChart(\n            Object.entries(healthCounts).map(([status, count]) => ({\n              name: status,\n              value: count,\n              color: getHealthStatusColor(status)\n            })),\n            'Health Status Distribution'\n          )}\n        </svg>\n        <div class=\"chart-legend\">\n          ${Object.entries(healthCounts).map(([status, count]) => `\n            <div class=\"legend-item\">\n              <span class=\"legend-color\" style=\"background-color: ${getHealthStatusColor(status)};\"></span>\n              <span class=\"legend-label\">${status}: ${count} (${(count / totalAnimals * 100).toFixed(1)}%)</span>\n            </div>\n          `).join('')}\n        </div>\n      </div>\n    </div>\n  `;\n\n  // Add feed usage table if feed report\n  if (params.type === 'analysis' || params.type === 'performance') {\n    html += `\n      <div class=\"report-section\">\n        <h3 class=\"section-title\">Feed Usage Details</h3>\n        <table>\n          <thead>\n            <tr>\n              <th>Feed Type</th>\n              <th>Quantity Used</th>\n              <th>Cost</th>\n              <th>Animal Group</th>\n              <th>Date</th>\n            </tr>\n          </thead>\n          <tbody>\n    `;\n\n    // Add rows for each feeding record\n    feedingRecords.slice(0, 10).forEach(record => {\n      html += `\n        <tr>\n          <td>${record.feedType}</td>\n          <td>${record.quantity} ${record.unit || 'kg'}</td>\n          <td>R ${(record.totalCost || 0).toLocaleString()}</td>\n          <td>${record.animalGroupId || 'All'}</td>\n          <td>${new Date(record.date).toLocaleDateString('en-ZA')}</td>\n        </tr>\n      `;\n    });\n\n    // Close the table\n    html += `\n          </tbody>\n        </table>\n      </div>\n    `;\n  }\n\n  // Add data table based on report type\n  switch (params.type) {\n    case 'health':\n      html += generateDetailedHealthReport(animals, healthRecords);\n      break;\n    case 'financial':\n      html += generateDetailedFinancialReport(animals, financialRecords);\n      break;\n    case 'market':\n      html += generateDetailedMarketReport(animals);\n      break;\n    case 'performance':\n      html += generateDetailedPerformanceReport(animals);\n      break;\n    case 'analysis':\n      html += generateDetailedAnalysisReport(animals);\n      break;\n    default:\n      html += generateAnimalTable(animals);\n  }\n\n  // Add footer\n  html += `\n        <div class=\"footer\">\n          <p>MayCaiphus Livestock Management System</p>\n          <p>This report was generated automatically. For questions or support, please contact your system administrator.</p>\n        </div>\n      </div>\n    </body>\n  </html>\n  `;\n\n  return html;\n  } catch (error) {\n    console.error('Error generating HTML report:', error);\n    showSnackbar('Failed to generate report', 'error');\n\n    // Get detailed error message\n    let errorMessage = 'Unknown error';\n    if (error instanceof Error) {\n      errorMessage = error.message;\n    } else if (typeof error === 'string') {\n      errorMessage = error;\n    } else if (error && typeof error === 'object') {\n      errorMessage = JSON.stringify(error);\n    }\n\n    // Return a simple error report with more detailed information\n    return `\n      <!DOCTYPE html>\n      <html lang=\"en\">\n      <head>\n        <meta charset=\"UTF-8\">\n        <meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n        <title>Error Generating Report</title>\n        <style>\n          body { font-family: Arial, sans-serif; padding: 20px; }\n          .error { color: #d32f2f; }\n          .error-details { background-color: #f8f8f8; padding: 15px; border-left: 4px solid #d32f2f; margin-top: 20px; }\n          .error-message { font-family: monospace; white-space: pre-wrap; }\n          .suggestions { margin-top: 20px; background-color: #e8f5e9; padding: 15px; border-left: 4px solid #4caf50; }\n        </style>\n      </head>\n      <body>\n        <h1 class=\"error\">Error Generating Report</h1>\n        <p>There was an error generating the requested report. Please try again later or contact support.</p>\n\n        <div class=\"error-details\">\n          <h2>Error Details</h2>\n          <p class=\"error-message\">${errorMessage}</p>\n        </div>\n\n        <div class=\"suggestions\">\n          <h2>Suggestions</h2>\n          <ul>\n            <li>Check your internet connection</li>\n            <li>Verify that the MongoDB connection is properly configured</li>\n            <li>Try refreshing the page and generating the report again</li>\n            <li>If the error persists, contact your system administrator</li>\n          </ul>\n        </div>\n      </body>\n      </html>\n    `;\n  }\n};\n\n/**\n * Generate an HTML table for animal data\n */\nconst generateAnimalTable = (animals: Animal[]): string => {\n  let html = `\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Animal Data</h3>\n      <table>\n        <thead>\n          <tr>\n            <th>Tag</th>\n            <th>Name</th>\n            <th>Species</th>\n            <th>Breed</th>\n            <th>Gender</th>\n            <th>Birth Date</th>\n            <th>Status</th>\n            <th>Health</th>\n            <th>Location</th>\n          </tr>\n        </thead>\n        <tbody>\n  `;\n\n  // Add rows for each animal\n  animals.forEach(animal => {\n    html += `\n      <tr>\n        <td>${animal.tagNumber}</td>\n        <td>${animal.name}</td>\n        <td>${animal.species || animal.type}</td>\n        <td>${animal.breed}</td>\n        <td>${animal.gender}</td>\n        <td>${animal.birthDate || 'Unknown'}</td>\n        <td>${animal.status}</td>\n        <td>${animal.healthStatus}</td>\n        <td>${animal.location}</td>\n      </tr>\n    `;\n  });\n\n  // Close the table\n  html += `\n        </tbody>\n      </table>\n    </div>\n  `;\n\n  return html;\n};\n\n/**\n * Generate an HTML table for health data\n */\nconst generateHealthTable = (animals: Animal[]): string => {\n  let html = `\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Animal Health Data</h3>\n      <table>\n        <thead>\n          <tr>\n            <th>Tag</th>\n            <th>Name</th>\n            <th>Species</th>\n            <th>Health Status</th>\n            <th>Last Check-up</th>\n            <th>Next Check-up</th>\n            <th>Notes</th>\n          </tr>\n        </thead>\n        <tbody>\n  `;\n\n  // Add rows for each animal with mock health data\n  animals.forEach(animal => {\n    const lastCheckup = new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);\n    const nextCheckup = new Date(Date.now() + Math.random() * 30 * 24 * 60 * 60 * 1000);\n\n    html += `\n      <tr>\n        <td>${animal.tagNumber}</td>\n        <td>${animal.name}</td>\n        <td>${animal.species || animal.type}</td>\n        <td>${animal.healthStatus}</td>\n        <td>${lastCheckup.toLocaleDateString('en-ZA')}</td>\n        <td>${nextCheckup.toLocaleDateString('en-ZA')}</td>\n        <td>${animal.notes || '-'}</td>\n      </tr>\n    `;\n  });\n\n  // Close the table\n  html += `\n        </tbody>\n      </table>\n    </div>\n  `;\n\n  return html;\n};\n\n/**\n * Generate a detailed health report with comprehensive health data and visualizations\n */\nconst generateDetailedHealthReport = (animals: any[], healthRecords: any[]): string => {\n  // Calculate health statistics\n  const totalAnimals = animals.length;\n  const healthStatusCounts: Record<string, number> = {};\n  const treatmentTypeCounts: Record<string, number> = {};\n  const monthlyHealthIssues: Record<string, number> = {};\n  const vaccinationStatus: Record<string, number> = { 'Up to date': 0, 'Due soon': 0, 'Overdue': 0 };\n\n  // Process health records\n  healthRecords.forEach(record => {\n    // Count by treatment type\n    if (record.type) {\n      treatmentTypeCounts[record.type] = (treatmentTypeCounts[record.type] || 0) + 1;\n    }\n\n    // Count monthly health issues\n    if (record.date) {\n      const month = new Date(record.date).toLocaleString('en-ZA', { month: 'short', year: 'numeric' });\n      monthlyHealthIssues[month] = (monthlyHealthIssues[month] || 0) + 1;\n    }\n  });\n\n  // Process animals for health status\n  animals.forEach(animal => {\n    if (animal.healthStatus) {\n      healthStatusCounts[animal.healthStatus] = (healthStatusCounts[animal.healthStatus] || 0) + 1;\n    }\n\n    // Determine vaccination status (mock data for demonstration)\n    const randomStatus = Math.random();\n    if (randomStatus > 0.7) {\n      vaccinationStatus['Up to date']++;\n    } else if (randomStatus > 0.4) {\n      vaccinationStatus['Due soon']++;\n    } else {\n      vaccinationStatus['Overdue']++;\n    }\n  });\n\n  // Start building the detailed health report\n  let html = `\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Health Overview</h3>\n      <div class=\"summary-stats\">\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Total Animals</div>\n          <div class=\"stat-value\">${totalAnimals}</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Healthy Animals</div>\n          <div class=\"stat-value\">${healthStatusCounts['healthy'] || 0}</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Sick/Injured</div>\n          <div class=\"stat-value\">${(healthStatusCounts['sick'] || 0) + (healthStatusCounts['injured'] || 0)}</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Vaccination Status</div>\n          <div class=\"stat-value\">${vaccinationStatus['Up to date']} up to date</div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Health Status Distribution</h3>\n      <div class=\"chart-container\">\n        <svg width=\"100%\" height=\"300\" viewBox=\"0 0 500 300\" xmlns=\"http://www.w3.org/2000/svg\">\n          <!-- Generate pie chart for health status -->\n          ${generateSVGPieChart(\n            Object.entries(healthStatusCounts).map(([status, count]) => ({\n              name: status,\n              value: count,\n              color: getHealthStatusColor(status)\n            })),\n            'Health Status Distribution'\n          )}\n        </svg>\n        <div class=\"chart-legend\">\n          ${Object.entries(healthStatusCounts).map(([status, count]) => `\n            <div class=\"legend-item\">\n              <span class=\"legend-color\" style=\"background-color: ${getHealthStatusColor(status)};\"></span>\n              <span class=\"legend-label\">${status}: ${count} (${((count / totalAnimals) * 100).toFixed(1)}%)</span>\n            </div>\n          `).join('')}\n        </div>\n      </div>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Treatment Types</h3>\n      <div class=\"chart-container\">\n        <svg width=\"100%\" height=\"300\" viewBox=\"0 0 500 300\" xmlns=\"http://www.w3.org/2000/svg\">\n          <!-- Generate bar chart for treatment types -->\n          ${generateSVGBarChart(\n            Object.entries(treatmentTypeCounts).map(([type, count]) => ({\n              name: type,\n              value: count,\n              color: getHealthStatusColor(type)\n            })),\n            'Treatment Types Distribution',\n            (value) => value.toString()\n          )}\n        </svg>\n        <div class=\"chart-legend\">\n          ${Object.entries(treatmentTypeCounts).map(([type, count]) => `\n            <div class=\"legend-item\">\n              <span class=\"legend-color\" style=\"background-color: ${getHealthStatusColor(type)};\"></span>\n              <span class=\"legend-label\">${type}: ${count} (${((count / Object.values(treatmentTypeCounts).reduce((a, b) => a + b, 0)) * 100).toFixed(1)}%)</span>\n            </div>\n          `).join('')}\n        </div>\n      </div>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Monthly Health Trends</h3>\n      <div class=\"chart-container\">\n        <svg width=\"100%\" height=\"300\" viewBox=\"0 0 500 300\" xmlns=\"http://www.w3.org/2000/svg\">\n          <!-- Generate line chart for monthly health trends -->\n          ${generateSVGLineChart(\n            [{\n              name: 'Health Issues',\n              values: Object.entries(monthlyHealthIssues).map(([month, count]) => ({\n                key: month,\n                value: count,\n                color: '#e91e63'\n              }))\n            }],\n            'Monthly Health Issues',\n            (value) => value.toString()\n          )}\n        </svg>\n        <div class=\"chart-legend\">\n          <div class=\"legend-item\">\n            <span class=\"legend-color\" style=\"background-color: #e91e63;\"></span>\n            <span class=\"legend-label\">Health Issues</span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Vaccination Status</h3>\n      <div class=\"chart-container\">\n        <svg width=\"100%\" height=\"300\" viewBox=\"0 0 500 300\" xmlns=\"http://www.w3.org/2000/svg\">\n          <!-- Generate pie chart for vaccination status -->\n          ${generateSVGPieChart(\n            Object.entries(vaccinationStatus).map(([status, count]) => {\n              let color = '#4caf50'; // Default green for \"Up to date\"\n              if (status === 'Due soon') color = '#ff9800'; // Orange for due soon\n              if (status === 'Overdue') color = '#f44336'; // Red for overdue\n\n              return {\n                name: status,\n                value: count,\n                color: color\n              };\n            }),\n            'Vaccination Status'\n          )}\n        </svg>\n        <div class=\"chart-legend\">\n          ${Object.entries(vaccinationStatus).map(([status, count]) => {\n            let color = '#4caf50'; // Default green for \"Up to date\"\n            if (status === 'Due soon') color = '#ff9800'; // Orange for due soon\n            if (status === 'Overdue') color = '#f44336'; // Red for overdue\n\n            return `\n              <div class=\"legend-item\">\n                <span class=\"legend-color\" style=\"background-color: ${color};\"></span>\n                <span class=\"legend-label\">${status}: ${count} (${((count / totalAnimals) * 100).toFixed(1)}%)</span>\n              </div>\n            `;\n          }).join('')}\n        </div>\n      </div>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Recent Health Records</h3>\n      <table>\n        <thead>\n          <tr>\n            <th>Animal ID</th>\n            <th>Date</th>\n            <th>Type</th>\n            <th>Diagnosis</th>\n            <th>Treatment</th>\n            <th>Performed By</th>\n            <th>Follow-up Date</th>\n          </tr>\n        </thead>\n        <tbody>\n  `;\n\n  // Add rows for each health record (limit to most recent 15)\n  const sortedRecords = [...healthRecords].sort((a, b) =>\n    new Date(b.date).getTime() - new Date(a.date).getTime()\n  ).slice(0, 15);\n\n  sortedRecords.forEach(record => {\n    const animalInfo = animals.find(a => a.id === record.animalId) || {};\n    html += `\n      <tr>\n        <td>${record.animalId} (${animalInfo.name || 'Unknown'})</td>\n        <td>${new Date(record.date).toLocaleDateString('en-ZA')}</td>\n        <td>${record.type || 'Check-up'}</td>\n        <td>${record.diagnosis || '-'}</td>\n        <td>${record.treatment || '-'}</td>\n        <td>${record.performedBy || 'Staff'}</td>\n        <td>${record.followUpDate ? new Date(record.followUpDate).toLocaleDateString('en-ZA') : '-'}</td>\n      </tr>\n    `;\n  });\n\n  // Close the table\n  html += `\n        </tbody>\n      </table>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Animals Requiring Attention</h3>\n      <table>\n        <thead>\n          <tr>\n            <th>Tag</th>\n            <th>Name</th>\n            <th>Species</th>\n            <th>Health Status</th>\n            <th>Issue</th>\n            <th>Last Check-up</th>\n            <th>Next Check-up</th>\n          </tr>\n        </thead>\n        <tbody>\n  `;\n\n  // Add rows for animals requiring attention\n  const animalsNeedingAttention = animals.filter(animal =>\n    animal.healthStatus === 'sick' ||\n    animal.healthStatus === 'injured' ||\n    animal.healthStatus === 'critical'\n  );\n\n  animalsNeedingAttention.forEach(animal => {\n    const lastRecord = healthRecords\n      .filter(r => r.animalId === animal.id)\n      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())[0] || {};\n\n    const lastCheckup = lastRecord.date ? new Date(lastRecord.date) : new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000);\n    const nextCheckup = lastRecord.followUpDate ? new Date(lastRecord.followUpDate) : new Date(Date.now() + Math.random() * 10 * 24 * 60 * 60 * 1000);\n\n    html += `\n      <tr>\n        <td>${animal.tagNumber}</td>\n        <td>${animal.name}</td>\n        <td>${animal.species || animal.type}</td>\n        <td>${animal.healthStatus}</td>\n        <td>${lastRecord.diagnosis || 'Requires check-up'}</td>\n        <td>${lastCheckup.toLocaleDateString('en-ZA')}</td>\n        <td>${nextCheckup.toLocaleDateString('en-ZA')}</td>\n      </tr>\n    `;\n  });\n\n  // Close the table\n  html += `\n        </tbody>\n      </table>\n    </div>\n  `;\n\n  return html;\n};\n\n/**\n * Generate an SVG pie chart\n */\nconst generateSVGPieChart = (\n  data: { name: string; value: number; color: string }[],\n  title: string\n): string => {\n  const total = data.reduce((sum, item) => sum + item.value, 0);\n  if (total === 0) return '<text x=\"250\" y=\"150\" text-anchor=\"middle\">No data available</text>';\n\n  const centerX = 250;\n  const centerY = 150;\n  const radius = 100;\n\n  let startAngle = 0;\n  let paths = '';\n  let labels = '';\n\n  data.forEach((item, index) => {\n    const percentage = item.value / total;\n    const endAngle = startAngle + percentage * 2 * Math.PI;\n\n    // Calculate path\n    const x1 = centerX + radius * Math.cos(startAngle);\n    const y1 = centerY + radius * Math.sin(startAngle);\n    const x2 = centerX + radius * Math.cos(endAngle);\n    const y2 = centerY + radius * Math.sin(endAngle);\n\n    const largeArcFlag = percentage > 0.5 ? 1 : 0;\n\n    paths += `<path d=\"M ${centerX} ${centerY} L ${x1} ${y1} A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2} Z\"\n              fill=\"${item.color}\" stroke=\"white\" stroke-width=\"1\" />`;\n\n    // Add label if segment is large enough\n    if (percentage > 0.05) {\n      const labelAngle = startAngle + (endAngle - startAngle) / 2;\n      const labelRadius = radius * 0.7;\n      const labelX = centerX + labelRadius * Math.cos(labelAngle);\n      const labelY = centerY + labelRadius * Math.sin(labelAngle);\n\n      labels += `<text x=\"${labelX}\" y=\"${labelY}\" text-anchor=\"middle\" fill=\"white\" font-weight=\"bold\" font-size=\"12\">\n                  ${(percentage * 100).toFixed(0)}%\n                </text>`;\n    }\n\n    startAngle = endAngle;\n  });\n\n  return `\n    <g>\n      <text x=\"${centerX}\" y=\"30\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"bold\">${title}</text>\n      ${paths}\n      ${labels}\n    </g>\n  `;\n};\n\n/**\n * Generate an SVG bar chart\n */\nconst generateSVGBarChart = (\n  data: { name: string; value: number; color: string }[],\n  title: string,\n  formatValue?: (value: number) => string\n): string => {\n  if (data.length === 0) return '<text x=\"250\" y=\"150\" text-anchor=\"middle\">No data available</text>';\n\n  const width = 500;\n  const height = 300;\n  const margin = { top: 40, right: 20, bottom: 60, left: 60 };\n  const chartWidth = width - margin.left - margin.right;\n  const chartHeight = height - margin.top - margin.bottom;\n\n  const maxValue = Math.max(...data.map(d => d.value));\n  const barWidth = chartWidth / data.length * 0.8;\n  const barSpacing = chartWidth / data.length * 0.2;\n\n  let bars = '';\n  let xLabels = '';\n  let yLabels = '';\n\n  // Generate y-axis labels\n  for (let i = 0; i <= 5; i++) {\n    const value = maxValue * i / 5;\n    const y = margin.top + chartHeight - (chartHeight * i / 5);\n    yLabels += `\n      <line x1=\"${margin.left - 5}\" y1=\"${y}\" x2=\"${margin.left}\" y2=\"${y}\" stroke=\"#666\" />\n      <text x=\"${margin.left - 10}\" y=\"${y + 5}\" text-anchor=\"end\" font-size=\"12\">${formatValue ? formatValue(value) : value}</text>\n    `;\n  }\n\n  // Generate bars and x-axis labels\n  data.forEach((item, index) => {\n    const x = margin.left + (chartWidth / data.length) * index + barSpacing / 2;\n    const barHeight = (item.value / maxValue) * chartHeight;\n    const y = margin.top + chartHeight - barHeight;\n\n    bars += `<rect x=\"${x}\" y=\"${y}\" width=\"${barWidth}\" height=\"${barHeight}\" fill=\"${item.color}\" />`;\n\n    xLabels += `<text x=\"${x + barWidth/2}\" y=\"${margin.top + chartHeight + 20}\" text-anchor=\"middle\" font-size=\"12\">${item.name}</text>`;\n  });\n\n  return `\n    <g>\n      <text x=\"${width/2}\" y=\"20\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"bold\">${title}</text>\n\n      <!-- Y-axis -->\n      <line x1=\"${margin.left}\" y1=\"${margin.top}\" x2=\"${margin.left}\" y2=\"${margin.top + chartHeight}\" stroke=\"#666\" />\n      ${yLabels}\n\n      <!-- X-axis -->\n      <line x1=\"${margin.left}\" y1=\"${margin.top + chartHeight}\" x2=\"${margin.left + chartWidth}\" y2=\"${margin.top + chartHeight}\" stroke=\"#666\" />\n      ${xLabels}\n\n      <!-- Bars -->\n      ${bars}\n    </g>\n  `;\n};\n\n/**\n * Generate an SVG line chart\n */\nconst generateSVGLineChart = (\n  data: { name: string; values: { key: string; value: number; color: string }[] }[],\n  title: string,\n  formatValue?: (value: number) => string\n): string => {\n  if (data.length === 0) return '<text x=\"250\" y=\"150\" text-anchor=\"middle\">No data available</text>';\n\n  const width = 500;\n  const height = 300;\n  const margin = { top: 40, right: 20, bottom: 60, left: 60 };\n  const chartWidth = width - margin.left - margin.right;\n  const chartHeight = height - margin.top - margin.bottom;\n\n  // Find max value across all data series\n  const allValues = data.flatMap(series => series.values.map(v => v.value));\n  const maxValue = Math.max(...allValues);\n\n  let lines = '';\n  let points = '';\n  let xLabels = '';\n  let yLabels = '';\n  let legend = '';\n\n  // Generate y-axis labels\n  for (let i = 0; i <= 5; i++) {\n    const value = maxValue * i / 5;\n    const y = margin.top + chartHeight - (chartHeight * i / 5);\n    yLabels += `\n      <line x1=\"${margin.left - 5}\" y1=\"${y}\" x2=\"${margin.left}\" y2=\"${y}\" stroke=\"#666\" />\n      <text x=\"${margin.left - 10}\" y=\"${y + 5}\" text-anchor=\"end\" font-size=\"12\">${formatValue ? formatValue(value) : value}</text>\n    `;\n  }\n\n  // Generate x-axis labels\n  data[0].values.forEach((item, index) => {\n    const x = margin.left + (chartWidth / (data[0].values.length - 1)) * index;\n    xLabels += `<text x=\"${x}\" y=\"${margin.top + chartHeight + 20}\" text-anchor=\"middle\" font-size=\"12\">${item.key}</text>`;\n  });\n\n  // Generate lines and points for each data series\n  data.forEach((series, seriesIndex) => {\n    let pathData = '';\n\n    series.values.forEach((point, index) => {\n      const x = margin.left + (chartWidth / (series.values.length - 1)) * index;\n      const y = margin.top + chartHeight - (point.value / maxValue) * chartHeight;\n\n      if (index === 0) {\n        pathData += `M ${x} ${y}`;\n      } else {\n        pathData += ` L ${x} ${y}`;\n      }\n\n      points += `<circle cx=\"${x}\" cy=\"${y}\" r=\"4\" fill=\"${point.color}\" />`;\n    });\n\n    lines += `<path d=\"${pathData}\" stroke=\"${series.values[0].color}\" stroke-width=\"2\" fill=\"none\" />`;\n\n    // Add to legend\n    legend += `\n      <g transform=\"translate(${margin.left + seriesIndex * 100}, ${height - 20})\">\n        <line x1=\"0\" y1=\"0\" x2=\"20\" y2=\"0\" stroke=\"${series.values[0].color}\" stroke-width=\"2\" />\n        <text x=\"25\" y=\"5\" font-size=\"12\">${series.name}</text>\n      </g>\n    `;\n  });\n\n  return `\n    <g>\n      <text x=\"${width/2}\" y=\"20\" text-anchor=\"middle\" font-size=\"16\" font-weight=\"bold\">${title}</text>\n\n      <!-- Y-axis -->\n      <line x1=\"${margin.left}\" y1=\"${margin.top}\" x2=\"${margin.left}\" y2=\"${margin.top + chartHeight}\" stroke=\"#666\" />\n      ${yLabels}\n\n      <!-- X-axis -->\n      <line x1=\"${margin.left}\" y1=\"${margin.top + chartHeight}\" x2=\"${margin.left + chartWidth}\" y2=\"${margin.top + chartHeight}\" stroke=\"#666\" />\n      ${xLabels}\n\n      <!-- Grid lines -->\n      ${Array(6).fill(0).map((_, i) => {\n        const y = margin.top + chartHeight - (chartHeight * i / 5);\n        return `<line x1=\"${margin.left}\" y1=\"${y}\" x2=\"${margin.left + chartWidth}\" y2=\"${y}\" stroke=\"#ddd\" stroke-dasharray=\"5,5\" />`;\n      }).join('')}\n\n      <!-- Lines and points -->\n      ${lines}\n      ${points}\n\n      <!-- Legend -->\n      ${legend}\n    </g>\n  `;\n};\n\n/**\n * Get color for health status\n */\nconst getHealthStatusColor = (status: string): string => {\n  const statusLower = status.toLowerCase();\n  if (statusLower.includes('healthy') || statusLower === 'good') return '#4caf50';\n  if (statusLower.includes('sick') || statusLower.includes('ill')) return '#f44336';\n  if (statusLower.includes('injured')) return '#ff9800';\n  if (statusLower.includes('critical')) return '#d32f2f';\n  if (statusLower.includes('recovering')) return '#2196f3';\n  if (statusLower.includes('quarantine')) return '#9c27b0';\n  if (statusLower.includes('pregnant')) return '#e91e63';\n  if (statusLower.includes('treatment')) return '#ff5722';\n  return '#607d8b'; // Default color\n};\n\n/**\n * Get color for financial data\n */\nconst getFinancialColor = (type: string): string => {\n  const typeLower = type.toLowerCase();\n  if (typeLower.includes('revenue') || typeLower.includes('income') || typeLower.includes('profit')) return '#4caf50';\n  if (typeLower.includes('expense') || typeLower.includes('cost')) return '#f44336';\n  if (typeLower.includes('sales')) return '#2196f3';\n  if (typeLower.includes('feed')) return '#ff9800';\n  if (typeLower.includes('labor')) return '#9c27b0';\n  if (typeLower.includes('veterinary')) return '#e91e63';\n  if (typeLower.includes('equipment')) return '#00bcd4';\n  if (typeLower.includes('utilities')) return '#607d8b';\n  return '#3f51b5'; // Default color\n};\n\n/**\n * Get date range based on time period\n */\nconst getDateRange = (\n  timePeriod: TimePeriod,\n  startDateStr?: string,\n  endDateStr?: string\n): { startDate: string; endDate: string } => {\n  const now = new Date();\n  const endDate = endDateStr ? new Date(endDateStr) : now;\n  let startDate: Date;\n\n  if (timePeriod === 'custom' && startDateStr) {\n    startDate = new Date(startDateStr);\n  } else {\n    switch (timePeriod) {\n      case 'week':\n        startDate = new Date(now);\n        startDate.setDate(now.getDate() - 7);\n        break;\n      case 'month':\n        startDate = new Date(now);\n        startDate.setMonth(now.getMonth() - 1);\n        break;\n      case 'quarter':\n        startDate = new Date(now);\n        startDate.setMonth(now.getMonth() - 3);\n        break;\n      case 'year':\n        startDate = new Date(now);\n        startDate.setFullYear(now.getFullYear() - 1);\n        break;\n      default:\n        startDate = new Date(now);\n        startDate.setMonth(now.getMonth() - 1);\n    }\n  }\n\n  return {\n    startDate: startDate.toISOString().split('T')[0],\n    endDate: endDate.toISOString().split('T')[0]\n  };\n};\n\n/**\n * Generate an HTML table for financial data\n */\nconst generateFinancialTable = (animals: Animal[]): string => {\n  let html = `\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Financial Data</h3>\n      <table>\n        <thead>\n          <tr>\n            <th>Tag</th>\n            <th>Name</th>\n            <th>Species</th>\n            <th>Purchase Date</th>\n            <th>Purchase Price (R)</th>\n            <th>Current Value (R)</th>\n            <th>ROI (%)</th>\n          </tr>\n        </thead>\n        <tbody>\n  `;\n\n  // Add rows for each animal with real financial data\n  animals.forEach(animal => {\n    const purchasePrice = animal.purchasePrice || 0;\n    // Calculate current value based on age, weight, and market factors\n    // This is a simplified calculation - in a real app, this would be more complex\n    const ageInMonths = animal.birthDate ?\n      Math.floor((new Date().getTime() - new Date(animal.birthDate).getTime()) / (30 * 24 * 60 * 60 * 1000)) : 24;\n\n    // Base value calculation\n    let currentValue = purchasePrice;\n\n    // Add value based on age (younger animals may be worth more)\n    if (ageInMonths < 12) {\n      currentValue *= 1.2; // 20% premium for young animals\n    } else if (ageInMonths > 60) {\n      currentValue *= 0.8; // 20% discount for older animals\n    }\n\n    // Add value based on health status\n    if (animal.healthStatus === 'healthy') {\n      currentValue *= 1.1; // 10% premium for healthy animals\n    } else if (animal.healthStatus === 'sick' || animal.healthStatus === 'injured') {\n      currentValue *= 0.7; // 30% discount for sick/injured animals\n    }\n\n    // Calculate ROI\n    const roi = purchasePrice > 0 ? ((currentValue - purchasePrice) / purchasePrice * 100) : 0;\n\n    html += `\n      <tr>\n        <td>${animal.tagNumber}</td>\n        <td>${animal.name}</td>\n        <td>${animal.species || animal.type}</td>\n        <td>${animal.purchaseDate || '-'}</td>\n        <td>R ${purchasePrice.toLocaleString()}</td>\n        <td>R ${currentValue.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</td>\n        <td>${roi.toFixed(2)}%</td>\n      </tr>\n    `;\n  });\n\n  // Close the table\n  html += `\n        </tbody>\n      </table>\n    </div>\n  `;\n\n  return html;\n};\n\n/**\n * Generate a detailed financial report with comprehensive financial data and visualizations\n */\nconst generateDetailedFinancialReport = (animals: any[], financialRecords: any[]): string => {\n  // Calculate financial statistics\n  const totalAnimals = animals.length;\n\n  // Process financial records\n  const totalRevenue = financialRecords\n    .filter(record => record.type === 'income')\n    .reduce((sum, record) => sum + (record.amount || 0), 0);\n\n  const totalExpenses = financialRecords\n    .filter(record => record.type === 'expense')\n    .reduce((sum, record) => sum + (record.amount || 0), 0);\n\n  const netProfit = totalRevenue - totalExpenses;\n  const profitMargin = totalRevenue > 0 ? (netProfit / totalRevenue) * 100 : 0;\n\n  // Calculate expense categories\n  const expensesByCategory: Record<string, number> = {};\n  financialRecords\n    .filter(record => record.type === 'expense')\n    .forEach(record => {\n      if (record.category) {\n        expensesByCategory[record.category] = (expensesByCategory[record.category] || 0) + (record.amount || 0);\n      }\n    });\n\n  // Calculate revenue sources\n  const revenueBySource: Record<string, number> = {};\n  financialRecords\n    .filter(record => record.type === 'income')\n    .forEach(record => {\n      if (record.category) {\n        revenueBySource[record.category] = (revenueBySource[record.category] || 0) + (record.amount || 0);\n      }\n    });\n\n  // Calculate monthly financial data\n  const monthlyFinancialData: Record<string, { revenue: number, expenses: number, profit: number }> = {};\n\n  financialRecords.forEach(record => {\n    if (record.date) {\n      const month = new Date(record.date).toLocaleString('en-ZA', { month: 'short', year: 'numeric' });\n\n      if (!monthlyFinancialData[month]) {\n        monthlyFinancialData[month] = { revenue: 0, expenses: 0, profit: 0 };\n      }\n\n      if (record.type === 'income') {\n        monthlyFinancialData[month].revenue += (record.amount || 0);\n      } else if (record.type === 'expense') {\n        monthlyFinancialData[month].expenses += (record.amount || 0);\n      }\n\n      monthlyFinancialData[month].profit = monthlyFinancialData[month].revenue - monthlyFinancialData[month].expenses;\n    }\n  });\n\n  // Calculate animal asset values\n  const totalAssetValue = animals.reduce((sum, animal) => {\n    // Simple calculation based on purchase price and health status\n    let value = animal.purchasePrice || 0;\n\n    if (animal.healthStatus === 'healthy') {\n      value *= 1.1; // 10% premium for healthy animals\n    } else if (animal.healthStatus === 'sick' || animal.healthStatus === 'injured') {\n      value *= 0.7; // 30% discount for sick/injured animals\n    }\n\n    return sum + value;\n  }, 0);\n\n  // Start building the detailed financial report\n  let html = `\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Financial Overview</h3>\n      <div class=\"summary-stats\">\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Total Revenue</div>\n          <div class=\"stat-value\">R ${totalRevenue.toLocaleString()}</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Total Expenses</div>\n          <div class=\"stat-value\">R ${totalExpenses.toLocaleString()}</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Net Profit</div>\n          <div class=\"stat-value\">R ${netProfit.toLocaleString()}</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Profit Margin</div>\n          <div class=\"stat-value\">${profitMargin.toFixed(2)}%</div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Monthly Financial Performance</h3>\n      <div class=\"chart-container\">\n        <svg width=\"100%\" height=\"350\" viewBox=\"0 0 500 350\" xmlns=\"http://www.w3.org/2000/svg\">\n          <!-- Generate line chart for monthly financial performance -->\n          ${generateSVGLineChart(\n            [\n              {\n                name: 'Revenue',\n                values: Object.entries(monthlyFinancialData).map(([month, data]) => ({\n                  key: month,\n                  value: data.revenue,\n                  color: '#4caf50'  // Green for revenue\n                }))\n              },\n              {\n                name: 'Expenses',\n                values: Object.entries(monthlyFinancialData).map(([month, data]) => ({\n                  key: month,\n                  value: data.expenses,\n                  color: '#f44336'  // Red for expenses\n                }))\n              },\n              {\n                name: 'Profit',\n                values: Object.entries(monthlyFinancialData).map(([month, data]) => ({\n                  key: month,\n                  value: data.profit,\n                  color: '#2196f3'  // Blue for profit\n                }))\n              }\n            ],\n            'Monthly Financial Performance',\n            (value) => `R${value.toLocaleString()}`\n          )}\n        </svg>\n        <div class=\"chart-legend\">\n          <div class=\"legend-item\">\n            <span class=\"legend-color\" style=\"background-color: #4caf50;\"></span>\n            <span class=\"legend-label\">Revenue</span>\n          </div>\n          <div class=\"legend-item\">\n            <span class=\"legend-color\" style=\"background-color: #f44336;\"></span>\n            <span class=\"legend-label\">Expenses</span>\n          </div>\n          <div class=\"legend-item\">\n            <span class=\"legend-color\" style=\"background-color: #2196f3;\"></span>\n            <span class=\"legend-label\">Profit</span>\n          </div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Expense Distribution</h3>\n      <div class=\"chart-container\">\n        <svg width=\"100%\" height=\"300\" viewBox=\"0 0 500 300\" xmlns=\"http://www.w3.org/2000/svg\">\n          <!-- Generate pie chart for expense categories -->\n          ${generateSVGPieChart(\n            Object.entries(expensesByCategory).map(([category, amount]) => ({\n              name: category,\n              value: amount,\n              color: getFinancialColor(category)\n            })),\n            'Expense Categories'\n          )}\n        </svg>\n        <div class=\"chart-legend\">\n          ${Object.entries(expensesByCategory).map(([category, amount]) => `\n            <div class=\"legend-item\">\n              <span class=\"legend-color\" style=\"background-color: ${getFinancialColor(category)};\"></span>\n              <span class=\"legend-label\">${category}: R${amount.toLocaleString()} (${((amount / totalExpenses) * 100).toFixed(1)}%)</span>\n            </div>\n          `).join('')}\n        </div>\n      </div>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Revenue Sources</h3>\n      <div class=\"chart-container\">\n        <svg width=\"100%\" height=\"300\" viewBox=\"0 0 500 300\" xmlns=\"http://www.w3.org/2000/svg\">\n          <!-- Generate pie chart for revenue sources -->\n          ${generateSVGPieChart(\n            Object.entries(revenueBySource).map(([source, amount]) => ({\n              name: source,\n              value: amount,\n              color: getFinancialColor(source)\n            })),\n            'Revenue Sources'\n          )}\n        </svg>\n        <div class=\"chart-legend\">\n          ${Object.entries(revenueBySource).map(([source, amount]) => `\n            <div class=\"legend-item\">\n              <span class=\"legend-color\" style=\"background-color: ${getFinancialColor(source)};\"></span>\n              <span class=\"legend-label\">${source}: R${amount.toLocaleString()} (${((amount / totalRevenue) * 100).toFixed(1)}%)</span>\n            </div>\n          `).join('')}\n        </div>\n      </div>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Asset Valuation</h3>\n      <div class=\"summary-stats\">\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Total Asset Value</div>\n          <div class=\"stat-value\">R ${totalAssetValue.toLocaleString()}</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Average Asset Value</div>\n          <div class=\"stat-value\">R ${(totalAssetValue / (totalAnimals || 1)).toLocaleString(undefined, { maximumFractionDigits: 2 })}</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Asset to Revenue Ratio</div>\n          <div class=\"stat-value\">${(totalAssetValue / (totalRevenue || 1)).toFixed(2)}</div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Recent Financial Transactions</h3>\n      <table>\n        <thead>\n          <tr>\n            <th>Date</th>\n            <th>Type</th>\n            <th>Category</th>\n            <th>Description</th>\n            <th>Amount (R)</th>\n            <th>Payment Method</th>\n            <th>Reference</th>\n          </tr>\n        </thead>\n        <tbody>\n  `;\n\n  // Add rows for each financial record (limit to most recent 15)\n  const sortedRecords = [...financialRecords].sort((a, b) =>\n    new Date(b.date).getTime() - new Date(a.date).getTime()\n  ).slice(0, 15);\n\n  sortedRecords.forEach(record => {\n    html += `\n      <tr>\n        <td>${new Date(record.date).toLocaleDateString('en-ZA')}</td>\n        <td>${record.type === 'income' ? 'Income' : 'Expense'}</td>\n        <td>${record.category || '-'}</td>\n        <td>${record.description || '-'}</td>\n        <td>R ${(record.amount || 0).toLocaleString()}</td>\n        <td>${record.paymentMethod || '-'}</td>\n        <td>${record.reference || '-'}</td>\n      </tr>\n    `;\n  });\n\n  // Close the table\n  html += `\n        </tbody>\n      </table>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Budget Analysis</h3>\n      <table>\n        <thead>\n          <tr>\n            <th>Category</th>\n            <th>Budgeted Amount (R)</th>\n            <th>Actual Amount (R)</th>\n            <th>Variance (R)</th>\n            <th>Variance (%)</th>\n            <th>Status</th>\n          </tr>\n        </thead>\n        <tbody>\n  `;\n\n  // Generate mock budget data for demonstration\n  const budgetCategories = [\n    'Feed', 'Veterinary', 'Labor', 'Equipment', 'Maintenance', 'Utilities', 'Marketing'\n  ];\n\n  budgetCategories.forEach(category => {\n    const budgeted = Math.round(Math.random() * 50000) + 10000;\n    const actual = expensesByCategory[category] || Math.round(Math.random() * budgeted * 1.2);\n    const variance = budgeted - actual;\n    const variancePercent = (variance / budgeted) * 100;\n    const status = variancePercent > 10 ? 'Under Budget' : variancePercent < -10 ? 'Over Budget' : 'On Target';\n\n    html += `\n      <tr>\n        <td>${category}</td>\n        <td>R ${budgeted.toLocaleString()}</td>\n        <td>R ${actual.toLocaleString()}</td>\n        <td>R ${variance.toLocaleString()}</td>\n        <td>${variancePercent.toFixed(2)}%</td>\n        <td>${status}</td>\n      </tr>\n    `;\n  });\n\n  // Close the table\n  html += `\n        </tbody>\n      </table>\n    </div>\n  `;\n\n  return html;\n};\n\n/**\n * Generate an HTML table for market data\n */\nconst generateMarketTable = (animals: Animal[]): string => {\n  // Simplified implementation for market data\n  return generateFinancialTable(animals);\n};\n\n/**\n * Generate a detailed market report with comprehensive market data and visualizations\n */\nconst generateDetailedMarketReport = (animals: any[]): string => {\n  // Calculate market statistics\n  const totalAnimals = animals.length;\n  const speciesCounts: Record<string, number> = {};\n  const marketValueBySpecies: Record<string, number> = {};\n  const marketTrends: Record<string, { current: number, previous: number, change: number }> = {};\n\n  // Process animals for market data\n  animals.forEach(animal => {\n    if (animal.species) {\n      speciesCounts[animal.species] = (speciesCounts[animal.species] || 0) + 1;\n\n      // Calculate market value (simplified)\n      const marketValue = animal.purchasePrice ? animal.purchasePrice * 1.2 : 5000; // Default value if no purchase price\n      marketValueBySpecies[animal.species] = (marketValueBySpecies[animal.species] || 0) + marketValue;\n    }\n  });\n\n  // Generate mock market trend data\n  Object.keys(speciesCounts).forEach(species => {\n    const currentValue = marketValueBySpecies[species] / speciesCounts[species];\n    const previousValue = currentValue * (0.8 + Math.random() * 0.4); // Random previous value between 80% and 120% of current\n    const change = ((currentValue - previousValue) / previousValue) * 100;\n\n    marketTrends[species] = {\n      current: currentValue,\n      previous: previousValue,\n      change: change\n    };\n  });\n\n  // Start building the detailed market report\n  let html = `\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Market Overview</h3>\n      <div class=\"summary-stats\">\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Total Marketable Animals</div>\n          <div class=\"stat-value\">${totalAnimals}</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Total Market Value</div>\n          <div class=\"stat-value\">R ${Object.values(marketValueBySpecies).reduce((sum, value) => sum + value, 0).toLocaleString()}</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Average Value Per Animal</div>\n          <div class=\"stat-value\">R ${(Object.values(marketValueBySpecies).reduce((sum, value) => sum + value, 0) / totalAnimals).toLocaleString(undefined, { maximumFractionDigits: 2 })}</div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Market Value by Species</h3>\n      <div class=\"chart-container\">\n        <svg width=\"100%\" height=\"300\" viewBox=\"0 0 500 300\" xmlns=\"http://www.w3.org/2000/svg\">\n          <!-- Generate bar chart for market value by species -->\n          ${generateSVGBarChart(\n            Object.entries(marketValueBySpecies).map(([species, value], index) => ({\n              name: species,\n              value: value,\n              color: `hsl(${index * 40}, 70%, 50%)`\n            })),\n            'Market Value by Species',\n            (value) => `R${Math.round(value).toLocaleString()}`\n          )}\n        </svg>\n        <div class=\"chart-legend\">\n          ${Object.entries(marketValueBySpecies).map(([species, value], index) => `\n            <div class=\"legend-item\">\n              <span class=\"legend-color\" style=\"background-color: hsl(${index * 40}, 70%, 50%);\"></span>\n              <span class=\"legend-label\">${species}: R${value.toLocaleString()}</span>\n            </div>\n          `).join('')}\n        </div>\n      </div>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Market Price Trends</h3>\n      <div class=\"chart-container\">\n        <svg width=\"100%\" height=\"300\" viewBox=\"0 0 500 300\" xmlns=\"http://www.w3.org/2000/svg\">\n          <!-- Generate bar chart for market price trends -->\n          ${generateSVGBarChart(\n            Object.entries(marketTrends).map(([species, data], index) => ({\n              name: species,\n              value: data.change,\n              color: data.change >= 0 ? '#4caf50' : '#f44336'  // Green for positive, red for negative\n            })),\n            'Market Price Change (%)',\n            (value) => `${value > 0 ? '+' : ''}${value.toFixed(1)}%`\n          )}\n        </svg>\n        <div class=\"chart-legend\">\n          ${Object.entries(marketTrends).map(([species, data]) => `\n            <div class=\"legend-item\">\n              <span class=\"legend-color\" style=\"background-color: ${data.change >= 0 ? '#4caf50' : '#f44336'};\"></span>\n              <span class=\"legend-label\">${species}: Current R${data.current.toLocaleString()} (${data.change > 0 ? '+' : ''}${data.change.toFixed(2)}%)</span>\n            </div>\n          `).join('')}\n        </div>\n      </div>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Market Price Comparison</h3>\n      <table>\n        <thead>\n          <tr>\n            <th>Species</th>\n            <th>Current Price (R)</th>\n            <th>Previous Price (R)</th>\n            <th>Change (%)</th>\n            <th>Trend</th>\n          </tr>\n        </thead>\n        <tbody>\n  `;\n\n  // Add rows for each species\n  Object.entries(marketTrends).forEach(([species, data]) => {\n    html += `\n      <tr>\n        <td>${species}</td>\n        <td>R ${data.current.toLocaleString(undefined, { maximumFractionDigits: 2 })}</td>\n        <td>R ${data.previous.toLocaleString(undefined, { maximumFractionDigits: 2 })}</td>\n        <td>${data.change > 0 ? '+' : ''}${data.change.toFixed(2)}%</td>\n        <td>${data.change > 5 ? '↑ Strong Increase' : data.change > 0 ? '↗ Slight Increase' : data.change > -5 ? '↘ Slight Decrease' : '↓ Strong Decrease'}</td>\n      </tr>\n    `;\n  });\n\n  // Close the table\n  html += `\n        </tbody>\n      </table>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Market Ready Animals</h3>\n      <table>\n        <thead>\n          <tr>\n            <th>Tag</th>\n            <th>Name</th>\n            <th>Species</th>\n            <th>Age</th>\n            <th>Weight</th>\n            <th>Health Status</th>\n            <th>Estimated Value (R)</th>\n          </tr>\n        </thead>\n        <tbody>\n  `;\n\n  // Add rows for market-ready animals (simplified criteria)\n  const marketReadyAnimals = animals.filter(animal =>\n    animal.status === 'Active' &&\n    animal.healthStatus === 'healthy'\n  ).slice(0, 15); // Limit to 15 animals for brevity\n\n  marketReadyAnimals.forEach(animal => {\n    const ageInMonths = animal.birthDate ?\n      Math.floor((new Date().getTime() - new Date(animal.birthDate).getTime()) / (30 * 24 * 60 * 60 * 1000)) : 24;\n\n    const estimatedValue = animal.purchasePrice ? animal.purchasePrice * 1.2 : 5000;\n\n    html += `\n      <tr>\n        <td>${animal.tagNumber}</td>\n        <td>${animal.name}</td>\n        <td>${animal.species || animal.type}</td>\n        <td>${ageInMonths} months</td>\n        <td>${animal.weight || '---'} kg</td>\n        <td>${animal.healthStatus}</td>\n        <td>R ${estimatedValue.toLocaleString(undefined, { maximumFractionDigits: 2 })}</td>\n      </tr>\n    `;\n  });\n\n  // Close the table\n  html += `\n        </tbody>\n      </table>\n    </div>\n  `;\n\n  return html;\n};\n\n/**\n * Generate an HTML table for performance data\n */\nconst generatePerformanceTable = (animals: Animal[]): string => {\n  // Simplified implementation for performance data\n  return generateHealthTable(animals);\n};\n\n/**\n * Generate a detailed performance report with comprehensive performance data and visualizations\n */\nconst generateDetailedPerformanceReport = (animals: any[]): string => {\n  // Calculate performance statistics\n  const totalAnimals = animals.length;\n  const speciesPerformance: Record<string, { count: number, avgGrowth: number, avgProduction: number }> = {};\n  const performanceTrends: Record<string, number[]> = {\n    'Growth Rate': [3.2, 3.5, 3.8, 4.0, 4.2, 4.1],\n    'Feed Conversion': [2.1, 2.0, 1.9, 1.8, 1.7, 1.7],\n    'Production Yield': [85, 87, 89, 90, 92, 93]\n  };\n\n  // Process animals for performance data\n  animals.forEach(animal => {\n    if (animal.species) {\n      if (!speciesPerformance[animal.species]) {\n        speciesPerformance[animal.species] = { count: 0, avgGrowth: 0, avgProduction: 0 };\n      }\n\n      speciesPerformance[animal.species].count++;\n\n      // Generate random performance metrics for demonstration\n      const growthRate = 2 + Math.random() * 3; // Between 2 and 5\n      const productionRate = 70 + Math.random() * 30; // Between 70 and 100\n\n      // Update averages\n      speciesPerformance[animal.species].avgGrowth =\n        (speciesPerformance[animal.species].avgGrowth * (speciesPerformance[animal.species].count - 1) + growthRate) /\n        speciesPerformance[animal.species].count;\n\n      speciesPerformance[animal.species].avgProduction =\n        (speciesPerformance[animal.species].avgProduction * (speciesPerformance[animal.species].count - 1) + productionRate) /\n        speciesPerformance[animal.species].count;\n    }\n  });\n\n  // Start building the detailed performance report\n  let html = `\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Performance Overview</h3>\n      <div class=\"summary-stats\">\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Total Animals</div>\n          <div class=\"stat-value\">${totalAnimals}</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Average Growth Rate</div>\n          <div class=\"stat-value\">${Object.values(speciesPerformance).reduce((sum, data) => sum + data.avgGrowth * data.count, 0) / totalAnimals || 0}%</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Average Production</div>\n          <div class=\"stat-value\">${Object.values(speciesPerformance).reduce((sum, data) => sum + data.avgProduction * data.count, 0) / totalAnimals || 0}%</div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Performance Trends</h3>\n      <div class=\"chart-container\">\n        <svg width=\"100%\" height=\"350\" viewBox=\"0 0 500 350\" xmlns=\"http://www.w3.org/2000/svg\">\n          <!-- Generate line chart for performance trends -->\n          ${generateSVGLineChart(\n            Object.entries(performanceTrends).map(([metric, values]) => ({\n              name: metric,\n              values: values.map((value, index) => {\n                // Generate month labels (last 6 months)\n                const date = new Date();\n                date.setMonth(date.getMonth() - (5 - index));\n                const month = date.toLocaleString('en-ZA', { month: 'short' });\n\n                let color = '#4caf50'; // Default green\n                if (metric === 'Feed Conversion') color = '#f44336'; // Red\n                if (metric === 'Production Yield') color = '#2196f3'; // Blue\n\n                return {\n                  key: month,\n                  value: value,\n                  color: color\n                };\n              })\n            })),\n            'Performance Trends Over Time',\n            (value) => value.toString()\n          )}\n        </svg>\n        <div class=\"chart-legend\">\n          ${Object.entries(performanceTrends).map(([metric, values]) => {\n            let color = '#4caf50'; // Default green\n            if (metric === 'Feed Conversion') color = '#f44336'; // Red\n            if (metric === 'Production Yield') color = '#2196f3'; // Blue\n\n            return `\n              <div class=\"legend-item\">\n                <span class=\"legend-color\" style=\"background-color: ${color};\"></span>\n                <span class=\"legend-label\">${metric}: Current ${values[values.length - 1]}</span>\n              </div>\n            `;\n          }).join('')}\n        </div>\n      </div>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Performance by Species</h3>\n      <table>\n        <thead>\n          <tr>\n            <th>Species</th>\n            <th>Count</th>\n            <th>Avg Growth Rate (%)</th>\n            <th>Avg Production (%)</th>\n            <th>Feed Conversion Ratio</th>\n            <th>Performance Rating</th>\n          </tr>\n        </thead>\n        <tbody>\n  `;\n\n  // Add rows for each species\n  Object.entries(speciesPerformance).forEach(([species, data]) => {\n    // Generate a mock feed conversion ratio\n    const fcr = 1.5 + Math.random() * 1.0; // Between 1.5 and 2.5\n\n    // Calculate a performance rating (1-10)\n    const performanceRating = Math.round((10 - fcr) + (data.avgGrowth / 5) + (data.avgProduction / 10));\n\n    html += `\n      <tr>\n        <td>${species}</td>\n        <td>${data.count}</td>\n        <td>${data.avgGrowth.toFixed(2)}%</td>\n        <td>${data.avgProduction.toFixed(2)}%</td>\n        <td>${fcr.toFixed(2)}</td>\n        <td>${performanceRating}/10</td>\n      </tr>\n    `;\n  });\n\n  // Close the table\n  html += `\n        </tbody>\n      </table>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Top Performing Animals</h3>\n      <table>\n        <thead>\n          <tr>\n            <th>Tag</th>\n            <th>Name</th>\n            <th>Species</th>\n            <th>Age</th>\n            <th>Growth Rate (%)</th>\n            <th>Production (%)</th>\n            <th>Performance Rating</th>\n          </tr>\n        </thead>\n        <tbody>\n  `;\n\n  // Generate mock performance data for individual animals\n  const animalPerformance = animals.map(animal => {\n    const growthRate = 2 + Math.random() * 3; // Between 2 and 5\n    const productionRate = 70 + Math.random() * 30; // Between 70 and 100\n    const performanceRating = Math.round(growthRate * 1.5 + productionRate / 10);\n\n    return {\n      ...animal,\n      growthRate,\n      productionRate,\n      performanceRating\n    };\n  });\n\n  // Sort by performance rating and take top 10\n  const topPerformers = [...animalPerformance]\n    .sort((a, b) => b.performanceRating - a.performanceRating)\n    .slice(0, 10);\n\n  topPerformers.forEach(animal => {\n    const ageInMonths = animal.birthDate ?\n      Math.floor((new Date().getTime() - new Date(animal.birthDate).getTime()) / (30 * 24 * 60 * 60 * 1000)) : 24;\n\n    html += `\n      <tr>\n        <td>${animal.tagNumber}</td>\n        <td>${animal.name}</td>\n        <td>${animal.species || animal.type}</td>\n        <td>${ageInMonths} months</td>\n        <td>${animal.growthRate.toFixed(2)}%</td>\n        <td>${animal.productionRate.toFixed(2)}%</td>\n        <td>${animal.performanceRating}/10</td>\n      </tr>\n    `;\n  });\n\n  // Close the table\n  html += `\n        </tbody>\n      </table>\n    </div>\n  `;\n\n  return html;\n};\n\n/**\n * Generate an HTML table for analysis data\n */\nconst generateAnalysisTable = (animals: Animal[]): string => {\n  // Simplified implementation for analysis data\n  return generateAnimalTable(animals);\n};\n\n/**\n * Generate a detailed analysis report with comprehensive data analysis and visualizations\n */\nconst generateDetailedAnalysisReport = (animals: any[]): string => {\n  // Calculate analysis statistics\n  const totalAnimals = animals.length;\n  const speciesCounts: Record<string, number> = {};\n  const statusCounts: Record<string, number> = {};\n  const healthCounts: Record<string, number> = {};\n  const locationCounts: Record<string, number> = {};\n\n  // Process animals for analysis data\n  animals.forEach(animal => {\n    // Count by species\n    if (animal.species) {\n      speciesCounts[animal.species] = (speciesCounts[animal.species] || 0) + 1;\n    }\n\n    // Count by status\n    if (animal.status) {\n      statusCounts[animal.status] = (statusCounts[animal.status] || 0) + 1;\n    }\n\n    // Count by health status\n    if (animal.healthStatus) {\n      healthCounts[animal.healthStatus] = (healthCounts[animal.healthStatus] || 0) + 1;\n    }\n\n    // Count by location\n    if (animal.location) {\n      locationCounts[animal.location] = (locationCounts[animal.location] || 0) + 1;\n    }\n  });\n\n  // Calculate age distribution\n  const ageGroups: Record<string, number> = {\n    'Under 1 year': 0,\n    '1-2 years': 0,\n    '2-3 years': 0,\n    '3-5 years': 0,\n    'Over 5 years': 0\n  };\n\n  animals.forEach(animal => {\n    if (animal.birthDate) {\n      const ageInMonths = Math.floor((new Date().getTime() - new Date(animal.birthDate).getTime()) / (30 * 24 * 60 * 60 * 1000));\n\n      if (ageInMonths < 12) {\n        ageGroups['Under 1 year']++;\n      } else if (ageInMonths < 24) {\n        ageGroups['1-2 years']++;\n      } else if (ageInMonths < 36) {\n        ageGroups['2-3 years']++;\n      } else if (ageInMonths < 60) {\n        ageGroups['3-5 years']++;\n      } else {\n        ageGroups['Over 5 years']++;\n      }\n    }\n  });\n\n  // Start building the detailed analysis report\n  let html = `\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Livestock Analysis Overview</h3>\n      <div class=\"summary-stats\">\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Total Animals</div>\n          <div class=\"stat-value\">${totalAnimals}</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Species Count</div>\n          <div class=\"stat-value\">${Object.keys(speciesCounts).length}</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Healthy Animals</div>\n          <div class=\"stat-value\">${healthCounts['healthy'] || 0} (${((healthCounts['healthy'] || 0) / totalAnimals * 100).toFixed(1)}%)</div>\n        </div>\n        <div class=\"stat-card\">\n          <div class=\"stat-title\">Active Animals</div>\n          <div class=\"stat-value\">${statusCounts['Active'] || 0} (${((statusCounts['Active'] || 0) / totalAnimals * 100).toFixed(1)}%)</div>\n        </div>\n      </div>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Species Distribution</h3>\n      <div class=\"chart-container\">\n        <svg width=\"100%\" height=\"300\" viewBox=\"0 0 500 300\" xmlns=\"http://www.w3.org/2000/svg\">\n          <!-- Generate pie chart for species distribution -->\n          ${generateSVGPieChart(\n            Object.entries(speciesCounts).map(([species, count], index) => ({\n              name: species,\n              value: count,\n              color: `hsl(${index * 40}, 70%, 50%)`\n            })),\n            'Species Distribution'\n          )}\n        </svg>\n        <div class=\"chart-legend\">\n          ${Object.entries(speciesCounts).map(([species, count], index) => `\n            <div class=\"legend-item\">\n              <span class=\"legend-color\" style=\"background-color: hsl(${index * 40}, 70%, 50%);\"></span>\n              <span class=\"legend-label\">${species}: ${count} (${(count / totalAnimals * 100).toFixed(1)}%)</span>\n            </div>\n          `).join('')}\n        </div>\n      </div>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Health Status Distribution</h3>\n      <div class=\"chart-container\">\n        <svg width=\"100%\" height=\"300\" viewBox=\"0 0 500 300\" xmlns=\"http://www.w3.org/2000/svg\">\n          <!-- Generate pie chart for health status distribution -->\n          ${generateSVGPieChart(\n            Object.entries(healthCounts).map(([status, count]) => ({\n              name: status,\n              value: count,\n              color: getHealthStatusColor(status)\n            })),\n            'Health Status Distribution'\n          )}\n        </svg>\n        <div class=\"chart-legend\">\n          ${Object.entries(healthCounts).map(([status, count]) => `\n            <div class=\"legend-item\">\n              <span class=\"legend-color\" style=\"background-color: ${getHealthStatusColor(status)};\"></span>\n              <span class=\"legend-label\">${status}: ${count} (${(count / totalAnimals * 100).toFixed(1)}%)</span>\n            </div>\n          `).join('')}\n        </div>\n      </div>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Age Distribution</h3>\n      <div class=\"chart-container\">\n        <svg width=\"100%\" height=\"300\" viewBox=\"0 0 500 300\" xmlns=\"http://www.w3.org/2000/svg\">\n          <!-- Generate bar chart for age distribution -->\n          ${generateSVGBarChart(\n            Object.entries(ageGroups).map(([group, count], index) => ({\n              name: group,\n              value: count,\n              color: `hsl(${200 + index * 30}, 70%, 50%)`\n            })),\n            'Age Distribution',\n            (value) => value.toString()\n          )}\n        </svg>\n        <div class=\"chart-legend\">\n          ${Object.entries(ageGroups).map(([group, count], index) => `\n            <div class=\"legend-item\">\n              <span class=\"legend-color\" style=\"background-color: hsl(${200 + index * 30}, 70%, 50%);\"></span>\n              <span class=\"legend-label\">${group}: ${count} (${(count / totalAnimals * 100).toFixed(1)}%)</span>\n            </div>\n          `).join('')}\n        </div>\n      </div>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Location Distribution</h3>\n      <table>\n        <thead>\n          <tr>\n            <th>Location</th>\n            <th>Animal Count</th>\n            <th>Percentage</th>\n            <th>Species Breakdown</th>\n          </tr>\n        </thead>\n        <tbody>\n  `;\n\n  // Add rows for each location\n  Object.entries(locationCounts).forEach(([location, count]) => {\n    // Calculate species breakdown for this location\n    const speciesBreakdown: Record<string, number> = {};\n\n    animals.filter(animal => animal.location === location).forEach(animal => {\n      if (animal.species) {\n        speciesBreakdown[animal.species] = (speciesBreakdown[animal.species] || 0) + 1;\n      }\n    });\n\n    const speciesBreakdownStr = Object.entries(speciesBreakdown)\n      .map(([species, speciesCount]) => `${species}: ${speciesCount}`)\n      .join(', ');\n\n    html += `\n      <tr>\n        <td>${location}</td>\n        <td>${count}</td>\n        <td>${(count / totalAnimals * 100).toFixed(1)}%</td>\n        <td>${speciesBreakdownStr}</td>\n      </tr>\n    `;\n  });\n\n  // Close the table\n  html += `\n        </tbody>\n      </table>\n    </div>\n\n    <div class=\"report-section\">\n      <h3 class=\"section-title\">Comprehensive Animal Analysis</h3>\n      <table>\n        <thead>\n          <tr>\n            <th>Species</th>\n            <th>Count</th>\n            <th>Avg Age (months)</th>\n            <th>Health Rate</th>\n            <th>Active Rate</th>\n            <th>Primary Location</th>\n          </tr>\n        </thead>\n        <tbody>\n  `;\n\n  // Add rows for each species with detailed analysis\n  Object.entries(speciesCounts).forEach(([species, count]) => {\n    const speciesAnimals = animals.filter(animal => animal.species === species);\n\n    // Calculate average age\n    const totalAgeInMonths = speciesAnimals.reduce((sum, animal) => {\n      if (animal.birthDate) {\n        return sum + Math.floor((new Date().getTime() - new Date(animal.birthDate).getTime()) / (30 * 24 * 60 * 60 * 1000));\n      }\n      return sum + 24; // Default to 24 months if no birth date\n    }, 0);\n\n    const avgAge = totalAgeInMonths / count;\n\n    // Calculate health rate\n    const healthyCount = speciesAnimals.filter(animal => animal.healthStatus === 'healthy').length;\n    const healthRate = (healthyCount / count) * 100;\n\n    // Calculate active rate\n    const activeCount = speciesAnimals.filter(animal => animal.status === 'Active').length;\n    const activeRate = (activeCount / count) * 100;\n\n    // Find primary location\n    const locationCounts: Record<string, number> = {};\n    speciesAnimals.forEach(animal => {\n      if (animal.location) {\n        locationCounts[animal.location] = (locationCounts[animal.location] || 0) + 1;\n      }\n    });\n\n    const primaryLocation = Object.entries(locationCounts).sort((a, b) => b[1] - a[1])[0]?.[0] || 'Unknown';\n\n    html += `\n      <tr>\n        <td>${species}</td>\n        <td>${count}</td>\n        <td>${avgAge.toFixed(1)}</td>\n        <td>${healthRate.toFixed(1)}%</td>\n        <td>${activeRate.toFixed(1)}%</td>\n        <td>${primaryLocation}</td>\n      </tr>\n    `;\n  });\n\n  // Close the table\n  html += `\n        </tbody>\n      </table>\n    </div>\n  `;\n\n  return html;\n};\n\n// Helper function getHealthStatusColor is already defined above\n\n// The generateSVGPieChart, generateSVGBarChart, and generateSVGLineChart functions are already defined earlier in the file\n\nexport const getSavedReports = async (type: ReportType): Promise<any[]> => {\n  try {\n    // In a real implementation, this would call the backend API\n    // For now, we'll return mock data\n    await new Promise(resolve => setTimeout(resolve, 800));\n\n    // Mock saved reports data\n    const mockReports = [\n      {\n        id: '1',\n        name: `${type.charAt(0).toUpperCase() + type.slice(1)} Report - Monthly`,\n        createdAt: new Date().toISOString(),\n        format: 'pdf',\n        size: '1.2 MB'\n      },\n      {\n        id: '2',\n        name: `${type.charAt(0).toUpperCase() + type.slice(1)} Report - Quarterly`,\n        createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),\n        format: 'excel',\n        size: '3.5 MB'\n      },\n      {\n        id: '3',\n        name: `${type.charAt(0).toUpperCase() + type.slice(1)} Report - Annual`,\n        createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(),\n        format: 'pdf',\n        size: '5.8 MB'\n      },\n      {\n        id: '4',\n        name: `Custom ${type.charAt(0).toUpperCase() + type.slice(1)} Analysis`,\n        createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),\n        format: 'csv',\n        size: '0.9 MB'\n      }\n    ];\n\n    return mockReports;\n  } catch (error) {\n    console.error(`Error fetching saved ${type} reports:`, error);\n    throw error;\n  }\n};\n"], "mappings": "gJACA,OAASA,YAAY,KAAQ,eAAe,CAO5C;AACA,OAASC,YAAY,KAAQ,mBAAmB,CAEhD;AAeA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAC,cAAc,CAAG,KAAO,CAAAC,MAAoB,EAAsB,CAC7E,GAAI,CACF;AACA,GAAIA,MAAM,CAACC,MAAM,GAAK,MAAM,CAAE,CAC5B,KAAM,CAAAC,WAAW,CAAG,KAAM,CAAAC,kBAAkB,CAACH,MAAM,CAAC,CACpD,KAAM,CAAAI,QAAQ,IAAAC,MAAA,CAAML,MAAM,CAACM,IAAI,gBAAc,CAE7C;AACAC,YAAY,CAAC,EAAE,CAAEH,QAAQ,CAAEF,WAAW,CAAC,CAEvC,MAAO,uBAAuB,CAChC,CAEA;AACA;AAEA;AACA,KAAM,IAAI,CAAAM,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,IAAI,CAAC,CAAC,CAEvD;AACA,KAAM,CAAAE,YAAY,CAAG,CACnBC,OAAO,CAAE,IAAI,CACbC,WAAW,IAAAR,MAAA,CAAKR,YAAY,uBAAAQ,MAAA,CAAqBL,MAAM,CAACM,IAAI,MAAAD,MAAA,CAAIS,IAAI,CAACC,GAAG,CAAC,CAAC,MAAAV,MAAA,CAAIL,MAAM,CAACC,MAAM,GAAK,OAAO,CAAG,MAAM,CAAGD,MAAM,CAACC,MAAM,CAClI,CAAC,CAED;AACAM,YAAY,CAACI,YAAY,CAACE,WAAW,IAAAR,MAAA,CAAKL,MAAM,CAACM,IAAI,aAAAD,MAAA,CAAWL,MAAM,CAACC,MAAM,GAAK,OAAO,CAAG,MAAM,CAAGD,MAAM,CAACC,MAAM,CAAE,CAAC,CAErH,MAAO,CAAAU,YAAY,CAACE,WAAW,CACjC,CAAE,MAAOG,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,0BAA0B,CAAEA,KAAK,CAAC,CAChD,KAAM,CAAAA,KAAK,CACb,CACF,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAT,YAAY,CAAGA,CAACW,GAAW,CAAEd,QAAgB,CAAEe,OAAgB,GAAW,CACrF;AACA,GAAIA,OAAO,CAAE,CACX,KAAM,CAAAC,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACF,OAAO,CAAC,CAAE,CAAEb,IAAI,CAAEgB,cAAc,CAAClB,QAAQ,CAAE,CAAC,CAAC,CACpE,KAAM,CAAAmB,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC,CACrCG,IAAI,CAACM,QAAQ,CAAGzB,QAAQ,CACxBoB,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC,CAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC,CACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC,CAC/BI,GAAG,CAACO,eAAe,CAACX,IAAI,CAACG,IAAI,CAAC,CAC9B,OACF,CAEA;AACA;AAEA;AACA,KAAM,CAAAS,WAAW,mBAAA9B,MAAA,CAAqBD,QAAQ,wBAAAC,MAAA,CAAsB,GAAI,CAAAS,IAAI,CAAC,CAAC,CAACsB,cAAc,CAAC,CAAC,CAAE,CACjG,KAAM,CAAAhB,IAAI,CAAG,GAAI,CAAAC,IAAI,CAAC,CAACc,WAAW,CAAC,CAAE,CAAE7B,IAAI,CAAEgB,cAAc,CAAClB,QAAQ,CAAE,CAAC,CAAC,CAExE;AACA,KAAM,CAAAmB,IAAI,CAAGC,QAAQ,CAACC,aAAa,CAAC,GAAG,CAAC,CACxCF,IAAI,CAACG,IAAI,CAAGC,GAAG,CAACC,eAAe,CAACR,IAAI,CAAC,CACrCG,IAAI,CAACM,QAAQ,CAAGzB,QAAQ,CACxBoB,QAAQ,CAACM,IAAI,CAACC,WAAW,CAACR,IAAI,CAAC,CAC/BA,IAAI,CAACS,KAAK,CAAC,CAAC,CACZR,QAAQ,CAACM,IAAI,CAACG,WAAW,CAACV,IAAI,CAAC,CAC/BI,GAAG,CAACO,eAAe,CAACX,IAAI,CAACG,IAAI,CAAC,CAChC,CAAC,CAED;AACA;AACA;AACA;AACA,GACA,KAAM,CAAAJ,cAAc,CAAIlB,QAAgB,EAAa,CACnD,GAAIA,QAAQ,CAACiC,QAAQ,CAAC,MAAM,CAAC,CAAE,CAC7B,MAAO,iBAAiB,CAC1B,CAAC,IAAM,IAAIjC,QAAQ,CAACiC,QAAQ,CAAC,OAAO,CAAC,CAAE,CACrC,MAAO,mEAAmE,CAC5E,CAAC,IAAM,IAAIjC,QAAQ,CAACiC,QAAQ,CAAC,MAAM,CAAC,CAAE,CACpC,MAAO,UAAU,CACnB,CAAC,IAAM,IAAIjC,QAAQ,CAACiC,QAAQ,CAAC,OAAO,CAAC,CAAE,CACrC,MAAO,WAAW,CACpB,CACA,MAAO,YAAY,CACrB,CAAC,CAED;AACA;AACA;AACA;AACA,GACA;AACA;AACA;AACA;AACA,GACA,MAAO,MAAM,CAAAlC,kBAAkB,CAAG,KAAO,CAAAH,MAAoB,EAAsB,CACjF,GAAI,CACF;AACA,KAAM,CAAAsC,UAAU,CAAG,GAAI,CAAAxB,IAAI,CAAC,CAAC,CAACyB,kBAAkB,CAAC,OAAO,CAAE,CACxDC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,MAAM,CACbC,GAAG,CAAE,SAAS,CACdC,IAAI,CAAE,SAAS,CACfC,MAAM,CAAE,SACV,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,cAAc,CAAGA,CAAA,GAAM,CAC3B,OAAQ7C,MAAM,CAACM,IAAI,EACjB,IAAK,UAAU,CAAE,MAAO,iBAAiB,CACzC,IAAK,aAAa,CAAE,MAAO,oBAAoB,CAC/C,IAAK,QAAQ,CAAE,MAAO,eAAe,CACrC,IAAK,QAAQ,CAAE,MAAO,eAAe,CACrC,IAAK,WAAW,CAAE,MAAO,kBAAkB,CAC3C,IAAK,QAAQ,CAAE,MAAO,eAAe,CACrC,QAAS,MAAO,6BAA6B,CAC/C,CACF,CAAC,CAED;AACA,KAAM,CAAEwC,SAAS,CAAEC,OAAQ,CAAC,CAAGC,YAAY,CAAChD,MAAM,CAACiD,UAAU,CAAEjD,MAAM,CAAC8C,SAAS,CAAE9C,MAAM,CAAC+C,OAAO,CAAC,CAEhG;AACA,GAAI,CAAAG,OAAc,CAAG,EAAE,CACvB,GAAI,CAAAC,cAAqB,CAAG,EAAE,CAC9B,GAAI,CAAAC,aAAoB,CAAG,EAAE,CAC7B,GAAI,CAAAC,aAAoB,CAAG,EAAE,CAC7B,GAAI,CAAAC,eAAsB,CAAG,EAAE,CAC/B,GAAI,CAAAC,gBAAuB,CAAG,EAAE,CAEhC,GAAI,CACF;AACA,KAAM,CAAAC,iBAAiB,CAAG,KAAM,CAAAC,aAAa,CAAC,SAAS,CAAC,CACxD,KAAM,CAAAC,wBAAwB,CAAG,KAAM,CAAAD,aAAa,CAAC,iBAAiB,CAAC,CACvE,KAAM,CAAAE,uBAAuB,CAAG,KAAM,CAAAF,aAAa,CAAC,gBAAgB,CAAC,CACrE,KAAM,CAAAG,uBAAuB,CAAG,KAAM,CAAAH,aAAa,CAAC,gBAAgB,CAAC,CACrE,KAAM,CAAAI,yBAAyB,CAAG,KAAM,CAAAJ,aAAa,CAAC,kBAAkB,CAAC,CACzE,KAAM,CAAAK,0BAA0B,CAAG,KAAM,CAAAL,aAAa,CAAC,mBAAmB,CAAC,CAE3E;AACA,GAAI,CACF,KAAM,CAAAM,aAAa,CAAGP,iBAAiB,CAACQ,IAAI,CAAC,CAAC,CAAC,CAAC,CAChDd,OAAO,CAAGa,aAAa,EAAI,MAAO,CAAAA,aAAa,CAACE,OAAO,GAAK,UAAU,CAClE,KAAM,CAAAF,aAAa,CAACE,OAAO,CAAC,CAAC,CAC7B,EAAE,CACR,CAAE,MAAOjD,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,yBAAyB,CAAEA,KAAK,CAAC,CAC/CkC,OAAO,CAAG,EAAE,CACd,CAEA,GAAI,CACF,KAAM,CAAAgB,oBAAoB,CAAGR,wBAAwB,CAACM,IAAI,CAAC,CACzDG,IAAI,CAAE,CAAEC,IAAI,CAAE,GAAI,CAAAtD,IAAI,CAACgC,SAAS,CAAC,CAAEuB,IAAI,CAAE,GAAI,CAAAvD,IAAI,CAACiC,OAAO,CAAE,CAC7D,CAAC,CAAC,CACFI,cAAc,CAAGe,oBAAoB,EAAI,MAAO,CAAAA,oBAAoB,CAACD,OAAO,GAAK,UAAU,CACvF,KAAM,CAAAC,oBAAoB,CAACD,OAAO,CAAC,CAAC,CACpC,EAAE,CACR,CAAE,MAAOjD,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,iCAAiC,CAAEA,KAAK,CAAC,CACvDmC,cAAc,CAAG,EAAE,CACrB,CAEA,GAAI,CACF,KAAM,CAAAmB,mBAAmB,CAAGX,uBAAuB,CAACK,IAAI,CAAC,CAAC,CAAC,CAAC,CAC5DZ,aAAa,CAAGkB,mBAAmB,EAAI,MAAO,CAAAA,mBAAmB,CAACL,OAAO,GAAK,UAAU,CACpF,KAAM,CAAAK,mBAAmB,CAACL,OAAO,CAAC,CAAC,CACnC,EAAE,CACR,CAAE,MAAOjD,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtDoC,aAAa,CAAG,EAAE,CACpB,CAEA,GAAI,CACF,KAAM,CAAAmB,mBAAmB,CAAGX,uBAAuB,CAACI,IAAI,CAAC,CACvDG,IAAI,CAAE,CAAEC,IAAI,CAAE,GAAI,CAAAtD,IAAI,CAACgC,SAAS,CAAC,CAAEuB,IAAI,CAAE,GAAI,CAAAvD,IAAI,CAACiC,OAAO,CAAE,CAC7D,CAAC,CAAC,CACFM,aAAa,CAAGkB,mBAAmB,EAAI,MAAO,CAAAA,mBAAmB,CAACN,OAAO,GAAK,UAAU,CACpF,KAAM,CAAAM,mBAAmB,CAACN,OAAO,CAAC,CAAC,CACnC,EAAE,CACR,CAAE,MAAOjD,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAEA,KAAK,CAAC,CACtDqC,aAAa,CAAG,EAAE,CACpB,CAEA,GAAI,CACF,KAAM,CAAAmB,qBAAqB,CAAGX,yBAAyB,CAACG,IAAI,CAAC,CAC3DG,IAAI,CAAE,CAAEC,IAAI,CAAE,GAAI,CAAAtD,IAAI,CAACgC,SAAS,CAAC,CAAEuB,IAAI,CAAE,GAAI,CAAAvD,IAAI,CAACiC,OAAO,CAAE,CAC7D,CAAC,CAAC,CACFO,eAAe,CAAGkB,qBAAqB,EAAI,MAAO,CAAAA,qBAAqB,CAACP,OAAO,GAAK,UAAU,CAC1F,KAAM,CAAAO,qBAAqB,CAACP,OAAO,CAAC,CAAC,CACrC,EAAE,CACR,CAAE,MAAOjD,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,kCAAkC,CAAEA,KAAK,CAAC,CACxDsC,eAAe,CAAG,EAAE,CACtB,CAEA,GAAI,CACF,KAAM,CAAAmB,sBAAsB,CAAGX,0BAA0B,CAACE,IAAI,CAAC,CAC7DG,IAAI,CAAE,CAAEC,IAAI,CAAE,GAAI,CAAAtD,IAAI,CAACgC,SAAS,CAAC,CAAEuB,IAAI,CAAE,GAAI,CAAAvD,IAAI,CAACiC,OAAO,CAAE,CAC7D,CAAC,CAAC,CACFQ,gBAAgB,CAAGkB,sBAAsB,EAAI,MAAO,CAAAA,sBAAsB,CAACR,OAAO,GAAK,UAAU,CAC7F,KAAM,CAAAQ,sBAAsB,CAACR,OAAO,CAAC,CAAC,CACtC,EAAE,CACR,CAAE,MAAOjD,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,mCAAmC,CAAEA,KAAK,CAAC,CACzDuC,gBAAgB,CAAG,EAAE,CACvB,CACF,CAAE,MAAOvC,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,uCAAuC,CAAEA,KAAK,CAAC,CAC7D;AACAC,OAAO,CAACyD,GAAG,CAAC,uCAAuC,CAAC,CAEpD;AACA,KAAM,CAAEC,WAAY,CAAC,CAAG,KAAM,OAAM,CAAC,qBAAqB,CAAC,CAC3D,KAAM,CAAEC,kBAAmB,CAAC,CAAG,KAAM,OAAM,CAAC,sBAAsB,CAAC,CACnE,KAAM,CAAEC,iBAAkB,CAAC,CAAG,KAAM,OAAM,CAAC,qBAAqB,CAAC,CACjE,KAAM,CAAEC,mBAAoB,CAAC,CAAG,KAAM,OAAM,CAAC,uBAAuB,CAAC,CACrE,KAAM,CAAEC,oBAAqB,CAAC,CAAG,KAAM,OAAM,CAAC,wBAAwB,CAAC,CAEvE7B,OAAO,CAAGyB,WAAW,EAAI,EAAE,CAC3BxB,cAAc,CAAGyB,kBAAkB,EAAI,EAAE,CACzCxB,aAAa,CAAG,EAAE,CAClBC,aAAa,CAAGwB,iBAAiB,EAAI,EAAE,CACvCvB,eAAe,CAAGwB,mBAAmB,EAAI,EAAE,CAC3CvB,gBAAgB,CAAGwB,oBAAoB,EAAI,EAAE,CAC/C,CAEA;AACA,KAAM,CAAAC,YAAY,CAAG9B,OAAO,CAAC+B,MAAM,CACnC,KAAM,CAAAC,aAAqC,CAAG,CAAC,CAAC,CAChD,KAAM,CAAAC,YAAoC,CAAG,CAAC,CAAC,CAC/C,KAAM,CAAAC,YAAoC,CAAG,CAAC,CAAC,CAE/ClC,OAAO,CAACmC,OAAO,CAACC,MAAM,EAAI,CACxB;AACA,GAAIA,MAAM,CAACC,OAAO,CAAE,CAClBL,aAAa,CAACI,MAAM,CAACC,OAAO,CAAC,CAAG,CAACL,aAAa,CAACI,MAAM,CAACC,OAAO,CAAC,EAAI,CAAC,EAAI,CAAC,CAC1E,CAEA;AACAJ,YAAY,CAACG,MAAM,CAACE,MAAM,CAAC,CAAG,CAACL,YAAY,CAACG,MAAM,CAACE,MAAM,CAAC,EAAI,CAAC,EAAI,CAAC,CAEpE;AACAJ,YAAY,CAACE,MAAM,CAACG,YAAY,CAAC,CAAG,CAACL,YAAY,CAACE,MAAM,CAACG,YAAY,CAAC,EAAI,CAAC,EAAI,CAAC,CAClF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,eAAuC,CAAG,CAAC,CAAC,CAClDvC,cAAc,CAACkC,OAAO,CAACM,MAAM,EAAI,CAC/B,KAAM,CAAAC,QAAQ,CAAGD,MAAM,CAACC,QAAQ,CAChC,GAAI,CAACF,eAAe,CAACE,QAAQ,CAAC,CAAE,CAC9BF,eAAe,CAACE,QAAQ,CAAC,CAAG,CAAC,CAC/B,CACAF,eAAe,CAACE,QAAQ,CAAC,EAAID,MAAM,CAACE,QAAQ,CAC9C,CAAC,CAAC,CAEF;AACA,KAAM,CAAAC,aAAa,CAAG3C,cAAc,CAAC4C,MAAM,CAAC,CAACC,GAAG,CAAEL,MAAM,GAAKK,GAAG,EAAIL,MAAM,CAACM,SAAS,EAAI,CAAC,CAAC,CAAE,CAAC,CAAC,CAE9F;AACA,KAAM,CAAAC,iBAAyC,CAAG,CAAC,CAAC,CACpD/C,cAAc,CAACkC,OAAO,CAACM,MAAM,EAAI,CAC/B,KAAM,CAAAQ,WAAW,CAAGR,MAAM,CAACS,aAAa,CACxC,GAAI,CAACF,iBAAiB,CAACC,WAAW,CAAC,CAAE,CACnCD,iBAAiB,CAACC,WAAW,CAAC,CAAG,CAAC,CACpC,CACAD,iBAAiB,CAACC,WAAW,CAAC,EAAIR,MAAM,CAACM,SAAS,EAAI,CAAC,CACzD,CAAC,CAAC,CAEF;AACA,KAAM,CAAAI,YAAY,CAAG9C,gBAAgB,CAClC+C,MAAM,CAACX,MAAM,EAAIA,MAAM,CAACrF,IAAI,GAAK,QAAQ,CAAC,CAC1CyF,MAAM,CAAC,CAACC,GAAG,CAAEL,MAAM,GAAKK,GAAG,CAAGL,MAAM,CAACY,MAAM,CAAE,CAAC,CAAC,CAElD,KAAM,CAAAC,aAAa,CAAGjD,gBAAgB,CACnC+C,MAAM,CAACX,MAAM,EAAIA,MAAM,CAACrF,IAAI,GAAK,SAAS,CAAC,CAC3CyF,MAAM,CAAC,CAACC,GAAG,CAAEL,MAAM,GAAKK,GAAG,CAAGL,MAAM,CAACY,MAAM,CAAE,CAAC,CAAC,CAElD,KAAM,CAAAE,SAAS,CAAGJ,YAAY,CAAGG,aAAa,CAC9C,KAAM,CAAAE,GAAG,CAAGL,YAAY,CAAG,CAAC,CAAII,SAAS,CAAGJ,YAAY,CAAI,GAAG,CAAG,CAAC,CAErE;AACA,GAAI,CAAAM,IAAI,8LAAAtG,MAAA,CAMKwC,cAAc,CAAC,CAAC,4gQAAAxC,MAAA,CA4VSwC,cAAc,CAAC,CAAC,6DAAAxC,MAAA,CACPiC,UAAU,4BAExD,CAED;AACAqE,IAAI,gQAAAtG,MAAA,CAM8B2E,YAAY,8JAAA3E,MAAA,CAIZ8E,YAAY,CAAC,QAAQ,CAAC,EAAI,CAAC,+JAAA9E,MAAA,CAI3B+E,YAAY,CAAC,SAAS,CAAC,EAAI,CAAC,wDAI7D,CAED;AACA,GAAIpF,MAAM,CAACM,IAAI,GAAK,WAAW,CAAE,CAC/BqG,IAAI,wRAAAtG,MAAA,CAMgCgG,YAAY,CAACjE,cAAc,CAAC,CAAC,wKAAA/B,MAAA,CAI7BmG,aAAa,CAACpE,cAAc,CAAC,CAAC,oKAAA/B,MAAA,CAI9BoG,SAAS,CAACrE,cAAc,CAAC,CAAC,2JAAA/B,MAAA,CAI5BqG,GAAG,CAACE,OAAO,CAAC,CAAC,CAAC,iEAI/C,CACH,CAEA;AACA,GAAI5G,MAAM,CAACM,IAAI,GAAK,UAAU,EAAIN,MAAM,CAACM,IAAI,GAAK,aAAa,CAAE,CAC/DqG,IAAI,2RAAAtG,MAAA,CAMgCyF,aAAa,CAAC1D,cAAc,CAAC,CAAC,yCAAA/B,MAAA,CAE1DwG,MAAM,CAACC,OAAO,CAACpB,eAAe,CAAC,CAACqB,KAAK,CAAC,CAAC,CAAE,CAAC,CAAC,CAACC,GAAG,CAACC,IAAA,MAAC,CAACrB,QAAQ,CAAEC,QAAQ,CAAC,CAAAoB,IAAA,2FAAA5G,MAAA,CAEzCuF,QAAQ,qDAAAvF,MAAA,CACRwF,QAAQ,CAACzD,cAAc,CAAC,CAAC,+CAEtD,CAAC,CAAC8E,IAAI,CAAC,EAAE,CAAC,wCAGhB,CACH,CAEA;AACAP,IAAI,qTAAAtG,MAAA,CAMM8G,mBAAmB,CACnBN,MAAM,CAACC,OAAO,CAAC5B,aAAa,CAAC,CAAC8B,GAAG,CAAC,CAAAI,KAAA,CAAmBC,KAAK,OAAvB,CAAC9B,OAAO,CAAE+B,KAAK,CAAC,CAAAF,KAAA,OAAa,CAC9DG,IAAI,CAAEhC,OAAO,CACbiC,KAAK,CAAEF,KAAK,CACZG,KAAK,QAAApH,MAAA,CAASgH,KAAK,CAAG,EAAE,eAC1B,CAAC,EAAC,CAAC,CACH,sBACF,CAAC,uEAAAhH,MAAA,CAGCwG,MAAM,CAACC,OAAO,CAAC5B,aAAa,CAAC,CAAC8B,GAAG,CAAC,CAAAU,KAAA,CAAmBL,KAAK,OAAvB,CAAC9B,OAAO,CAAE+B,KAAK,CAAC,CAAAI,KAAA,8HAAArH,MAAA,CAESgH,KAAK,CAAG,EAAE,wEAAAhH,MAAA,CACvCkF,OAAO,OAAAlF,MAAA,CAAKiH,KAAK,OAAAjH,MAAA,CAAK,CAACiH,KAAK,CAAGtC,YAAY,CAAG,GAAG,EAAE4B,OAAO,CAAC,CAAC,CAAC,+CAE7F,CAAC,CAACM,IAAI,CAAC,EAAE,CAAC,sQAAA7G,MAAA,CAMT8G,mBAAmB,CACnBN,MAAM,CAACC,OAAO,CAAC1B,YAAY,CAAC,CAAC4B,GAAG,CAACW,KAAA,MAAC,CAACnC,MAAM,CAAE8B,KAAK,CAAC,CAAAK,KAAA,OAAM,CACrDJ,IAAI,CAAE/B,MAAM,CACZgC,KAAK,CAAEF,KAAK,CACZG,KAAK,CAAEG,oBAAoB,CAACpC,MAAM,CACpC,CAAC,EAAC,CAAC,CACH,4BACF,CAAC,uEAAAnF,MAAA,CAGCwG,MAAM,CAACC,OAAO,CAAC1B,YAAY,CAAC,CAAC4B,GAAG,CAACa,KAAA,MAAC,CAACrC,MAAM,CAAE8B,KAAK,CAAC,CAAAO,KAAA,0HAAAxH,MAAA,CAEOuH,oBAAoB,CAACpC,MAAM,CAAC,6DAAAnF,MAAA,CACrDmF,MAAM,OAAAnF,MAAA,CAAKiH,KAAK,OAAAjH,MAAA,CAAK,CAACiH,KAAK,CAAGtC,YAAY,CAAG,GAAG,EAAE4B,OAAO,CAAC,CAAC,CAAC,+CAE5F,CAAC,CAACM,IAAI,CAAC,EAAE,CAAC,kDAIlB,CAED;AACA,GAAIlH,MAAM,CAACM,IAAI,GAAK,UAAU,EAAIN,MAAM,CAACM,IAAI,GAAK,aAAa,CAAE,CAC/DqG,IAAI,oYAcH,CAED;AACAxD,cAAc,CAAC4D,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAAC1B,OAAO,CAACM,MAAM,EAAI,CAC5CgB,IAAI,mCAAAtG,MAAA,CAEMsF,MAAM,CAACC,QAAQ,0BAAAvF,MAAA,CACfsF,MAAM,CAACE,QAAQ,MAAAxF,MAAA,CAAIsF,MAAM,CAACmC,IAAI,EAAI,IAAI,4BAAAzH,MAAA,CACpC,CAACsF,MAAM,CAACM,SAAS,EAAI,CAAC,EAAE7D,cAAc,CAAC,CAAC,0BAAA/B,MAAA,CAC1CsF,MAAM,CAACS,aAAa,EAAI,KAAK,0BAAA/F,MAAA,CAC7B,GAAI,CAAAS,IAAI,CAAC6E,MAAM,CAACxB,IAAI,CAAC,CAAC5B,kBAAkB,CAAC,OAAO,CAAC,gCAE1D,CACH,CAAC,CAAC,CAEF;AACAoE,IAAI,8DAIH,CACH,CAEA;AACA,OAAQ3G,MAAM,CAACM,IAAI,EACjB,IAAK,QAAQ,CACXqG,IAAI,EAAIoB,4BAA4B,CAAC7E,OAAO,CAAEG,aAAa,CAAC,CAC5D,MACF,IAAK,WAAW,CACdsD,IAAI,EAAIqB,+BAA+B,CAAC9E,OAAO,CAAEK,gBAAgB,CAAC,CAClE,MACF,IAAK,QAAQ,CACXoD,IAAI,EAAIsB,4BAA4B,CAAC/E,OAAO,CAAC,CAC7C,MACF,IAAK,aAAa,CAChByD,IAAI,EAAIuB,iCAAiC,CAAChF,OAAO,CAAC,CAClD,MACF,IAAK,UAAU,CACbyD,IAAI,EAAIwB,8BAA8B,CAACjF,OAAO,CAAC,CAC/C,MACF,QACEyD,IAAI,EAAIyB,mBAAmB,CAAClF,OAAO,CAAC,CACxC,CAEA;AACAyD,IAAI,sRAQH,CAED,MAAO,CAAAA,IAAI,CACX,CAAE,MAAO3F,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,CAAC,+BAA+B,CAAEA,KAAK,CAAC,CACrDlB,YAAY,CAAC,2BAA2B,CAAE,OAAO,CAAC,CAElD;AACA,GAAI,CAAAuI,YAAY,CAAG,eAAe,CAClC,GAAIrH,KAAK,WAAY,CAAAsH,KAAK,CAAE,CAC1BD,YAAY,CAAGrH,KAAK,CAACuH,OAAO,CAC9B,CAAC,IAAM,IAAI,MAAO,CAAAvH,KAAK,GAAK,QAAQ,CAAE,CACpCqH,YAAY,CAAGrH,KAAK,CACtB,CAAC,IAAM,IAAIA,KAAK,EAAI,MAAO,CAAAA,KAAK,GAAK,QAAQ,CAAE,CAC7CqH,YAAY,CAAGG,IAAI,CAACC,SAAS,CAACzH,KAAK,CAAC,CACtC,CAEA;AACA,i/BAAAX,MAAA,CAqBiCgI,YAAY,udAe/C,CACF,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAD,mBAAmB,CAAIlF,OAAiB,EAAa,CACzD,GAAI,CAAAyD,IAAI,ycAkBP,CAED;AACAzD,OAAO,CAACmC,OAAO,CAACC,MAAM,EAAI,CACxBqB,IAAI,+BAAAtG,MAAA,CAEMiF,MAAM,CAACoD,SAAS,wBAAArI,MAAA,CAChBiF,MAAM,CAACiC,IAAI,wBAAAlH,MAAA,CACXiF,MAAM,CAACC,OAAO,EAAID,MAAM,CAAChF,IAAI,wBAAAD,MAAA,CAC7BiF,MAAM,CAACqD,KAAK,wBAAAtI,MAAA,CACZiF,MAAM,CAACsD,MAAM,wBAAAvI,MAAA,CACbiF,MAAM,CAACuD,SAAS,EAAI,SAAS,wBAAAxI,MAAA,CAC7BiF,MAAM,CAACE,MAAM,wBAAAnF,MAAA,CACbiF,MAAM,CAACG,YAAY,wBAAApF,MAAA,CACnBiF,MAAM,CAACwD,QAAQ,4BAExB,CACH,CAAC,CAAC,CAEF;AACAnC,IAAI,sDAIH,CAED,MAAO,CAAAA,IAAI,CACb,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAoC,mBAAmB,CAAI7F,OAAiB,EAAa,CACzD,GAAI,CAAAyD,IAAI,qaAgBP,CAED;AACAzD,OAAO,CAACmC,OAAO,CAACC,MAAM,EAAI,CACxB,KAAM,CAAA0D,WAAW,CAAG,GAAI,CAAAlI,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAGkI,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CACnF,KAAM,CAAAC,WAAW,CAAG,GAAI,CAAArI,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAGkI,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CAEnFvC,IAAI,+BAAAtG,MAAA,CAEMiF,MAAM,CAACoD,SAAS,wBAAArI,MAAA,CAChBiF,MAAM,CAACiC,IAAI,wBAAAlH,MAAA,CACXiF,MAAM,CAACC,OAAO,EAAID,MAAM,CAAChF,IAAI,wBAAAD,MAAA,CAC7BiF,MAAM,CAACG,YAAY,wBAAApF,MAAA,CACnB2I,WAAW,CAACzG,kBAAkB,CAAC,OAAO,CAAC,wBAAAlC,MAAA,CACvC8I,WAAW,CAAC5G,kBAAkB,CAAC,OAAO,CAAC,wBAAAlC,MAAA,CACvCiF,MAAM,CAAC8D,KAAK,EAAI,GAAG,4BAE5B,CACH,CAAC,CAAC,CAEF;AACAzC,IAAI,sDAIH,CAED,MAAO,CAAAA,IAAI,CACb,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAoB,4BAA4B,CAAGA,CAAC7E,OAAc,CAAEG,aAAoB,GAAa,CACrF;AACA,KAAM,CAAA2B,YAAY,CAAG9B,OAAO,CAAC+B,MAAM,CACnC,KAAM,CAAAoE,kBAA0C,CAAG,CAAC,CAAC,CACrD,KAAM,CAAAC,mBAA2C,CAAG,CAAC,CAAC,CACtD,KAAM,CAAAC,mBAA2C,CAAG,CAAC,CAAC,CACtD,KAAM,CAAAC,iBAAyC,CAAG,CAAE,YAAY,CAAE,CAAC,CAAE,UAAU,CAAE,CAAC,CAAE,SAAS,CAAE,CAAE,CAAC,CAElG;AACAnG,aAAa,CAACgC,OAAO,CAACM,MAAM,EAAI,CAC9B;AACA,GAAIA,MAAM,CAACrF,IAAI,CAAE,CACfgJ,mBAAmB,CAAC3D,MAAM,CAACrF,IAAI,CAAC,CAAG,CAACgJ,mBAAmB,CAAC3D,MAAM,CAACrF,IAAI,CAAC,EAAI,CAAC,EAAI,CAAC,CAChF,CAEA;AACA,GAAIqF,MAAM,CAACxB,IAAI,CAAE,CACf,KAAM,CAAA1B,KAAK,CAAG,GAAI,CAAA3B,IAAI,CAAC6E,MAAM,CAACxB,IAAI,CAAC,CAAC/B,cAAc,CAAC,OAAO,CAAE,CAAEK,KAAK,CAAE,OAAO,CAAED,IAAI,CAAE,SAAU,CAAC,CAAC,CAChG+G,mBAAmB,CAAC9G,KAAK,CAAC,CAAG,CAAC8G,mBAAmB,CAAC9G,KAAK,CAAC,EAAI,CAAC,EAAI,CAAC,CACpE,CACF,CAAC,CAAC,CAEF;AACAS,OAAO,CAACmC,OAAO,CAACC,MAAM,EAAI,CACxB,GAAIA,MAAM,CAACG,YAAY,CAAE,CACvB4D,kBAAkB,CAAC/D,MAAM,CAACG,YAAY,CAAC,CAAG,CAAC4D,kBAAkB,CAAC/D,MAAM,CAACG,YAAY,CAAC,EAAI,CAAC,EAAI,CAAC,CAC9F,CAEA;AACA,KAAM,CAAAgE,YAAY,CAAGR,IAAI,CAACC,MAAM,CAAC,CAAC,CAClC,GAAIO,YAAY,CAAG,GAAG,CAAE,CACtBD,iBAAiB,CAAC,YAAY,CAAC,EAAE,CACnC,CAAC,IAAM,IAAIC,YAAY,CAAG,GAAG,CAAE,CAC7BD,iBAAiB,CAAC,UAAU,CAAC,EAAE,CACjC,CAAC,IAAM,CACLA,iBAAiB,CAAC,SAAS,CAAC,EAAE,CAChC,CACF,CAAC,CAAC,CAEF;AACA,GAAI,CAAA7C,IAAI,uQAAAtG,MAAA,CAM0B2E,YAAY,+JAAA3E,MAAA,CAIZgJ,kBAAkB,CAAC,SAAS,CAAC,EAAI,CAAC,4JAAAhJ,MAAA,CAIlC,CAACgJ,kBAAkB,CAAC,MAAM,CAAC,EAAI,CAAC,GAAKA,kBAAkB,CAAC,SAAS,CAAC,EAAI,CAAC,CAAC,kKAAAhJ,MAAA,CAIxEmJ,iBAAiB,CAAC,YAAY,CAAC,+XAAAnJ,MAAA,CAUvD8G,mBAAmB,CACnBN,MAAM,CAACC,OAAO,CAACuC,kBAAkB,CAAC,CAACrC,GAAG,CAAC0C,KAAA,MAAC,CAAClE,MAAM,CAAE8B,KAAK,CAAC,CAAAoC,KAAA,OAAM,CAC3DnC,IAAI,CAAE/B,MAAM,CACZgC,KAAK,CAAEF,KAAK,CACZG,KAAK,CAAEG,oBAAoB,CAACpC,MAAM,CACpC,CAAC,EAAC,CAAC,CACH,4BACF,CAAC,uEAAAnF,MAAA,CAGCwG,MAAM,CAACC,OAAO,CAACuC,kBAAkB,CAAC,CAACrC,GAAG,CAAC2C,KAAA,MAAC,CAACnE,MAAM,CAAE8B,KAAK,CAAC,CAAAqC,KAAA,0HAAAtJ,MAAA,CAECuH,oBAAoB,CAACpC,MAAM,CAAC,6DAAAnF,MAAA,CACrDmF,MAAM,OAAAnF,MAAA,CAAKiH,KAAK,OAAAjH,MAAA,CAAK,CAAEiH,KAAK,CAAGtC,YAAY,CAAI,GAAG,EAAE4B,OAAO,CAAC,CAAC,CAAC,+CAE9F,CAAC,CAACM,IAAI,CAAC,EAAE,CAAC,qWAAA7G,MAAA,CAUTuJ,mBAAmB,CACnB/C,MAAM,CAACC,OAAO,CAACwC,mBAAmB,CAAC,CAACtC,GAAG,CAAC6C,KAAA,MAAC,CAACvJ,IAAI,CAAEgH,KAAK,CAAC,CAAAuC,KAAA,OAAM,CAC1DtC,IAAI,CAAEjH,IAAI,CACVkH,KAAK,CAAEF,KAAK,CACZG,KAAK,CAAEG,oBAAoB,CAACtH,IAAI,CAClC,CAAC,EAAC,CAAC,CACH,8BAA8B,CAC7BkH,KAAK,EAAKA,KAAK,CAACsC,QAAQ,CAAC,CAC5B,CAAC,uEAAAzJ,MAAA,CAGCwG,MAAM,CAACC,OAAO,CAACwC,mBAAmB,CAAC,CAACtC,GAAG,CAAC+C,KAAA,MAAC,CAACzJ,IAAI,CAAEgH,KAAK,CAAC,CAAAyC,KAAA,0HAAA1J,MAAA,CAEEuH,oBAAoB,CAACtH,IAAI,CAAC,6DAAAD,MAAA,CACnDC,IAAI,OAAAD,MAAA,CAAKiH,KAAK,OAAAjH,MAAA,CAAK,CAAEiH,KAAK,CAAGT,MAAM,CAACmD,MAAM,CAACV,mBAAmB,CAAC,CAACvD,MAAM,CAAC,CAACkE,CAAC,CAAEC,CAAC,GAAKD,CAAC,CAAGC,CAAC,CAAE,CAAC,CAAC,CAAI,GAAG,EAAEtD,OAAO,CAAC,CAAC,CAAC,+CAE7I,CAAC,CAACM,IAAI,CAAC,EAAE,CAAC,kXAAA7G,MAAA,CAUT8J,oBAAoB,CACpB,CAAC,CACC5C,IAAI,CAAE,eAAe,CACrByC,MAAM,CAAEnD,MAAM,CAACC,OAAO,CAACyC,mBAAmB,CAAC,CAACvC,GAAG,CAACoD,KAAA,MAAC,CAAC3H,KAAK,CAAE6E,KAAK,CAAC,CAAA8C,KAAA,OAAM,CACnEC,GAAG,CAAE5H,KAAK,CACV+E,KAAK,CAAEF,KAAK,CACZG,KAAK,CAAE,SACT,CAAC,EAAC,CACJ,CAAC,CAAC,CACF,uBAAuB,CACtBD,KAAK,EAAKA,KAAK,CAACsC,QAAQ,CAAC,CAC5B,CAAC,gnBAAAzJ,MAAA,CAgBC8G,mBAAmB,CACnBN,MAAM,CAACC,OAAO,CAAC0C,iBAAiB,CAAC,CAACxC,GAAG,CAACsD,KAAA,EAAqB,IAApB,CAAC9E,MAAM,CAAE8B,KAAK,CAAC,CAAAgD,KAAA,CACpD,GAAI,CAAA7C,KAAK,CAAG,SAAS,CAAE;AACvB,GAAIjC,MAAM,GAAK,UAAU,CAAEiC,KAAK,CAAG,SAAS,CAAE;AAC9C,GAAIjC,MAAM,GAAK,SAAS,CAAEiC,KAAK,CAAG,SAAS,CAAE;AAE7C,MAAO,CACLF,IAAI,CAAE/B,MAAM,CACZgC,KAAK,CAAEF,KAAK,CACZG,KAAK,CAAEA,KACT,CAAC,CACH,CAAC,CAAC,CACF,oBACF,CAAC,uEAAApH,MAAA,CAGCwG,MAAM,CAACC,OAAO,CAAC0C,iBAAiB,CAAC,CAACxC,GAAG,CAACuD,MAAA,EAAqB,IAApB,CAAC/E,MAAM,CAAE8B,KAAK,CAAC,CAAAiD,MAAA,CACtD,GAAI,CAAA9C,KAAK,CAAG,SAAS,CAAE;AACvB,GAAIjC,MAAM,GAAK,UAAU,CAAEiC,KAAK,CAAG,SAAS,CAAE;AAC9C,GAAIjC,MAAM,GAAK,SAAS,CAAEiC,KAAK,CAAG,SAAS,CAAE;AAE7C,6HAAApH,MAAA,CAE0DoH,KAAK,+DAAApH,MAAA,CAC9BmF,MAAM,OAAAnF,MAAA,CAAKiH,KAAK,OAAAjH,MAAA,CAAK,CAAEiH,KAAK,CAAGtC,YAAY,CAAI,GAAG,EAAE4B,OAAO,CAAC,CAAC,CAAC,kDAGjG,CAAC,CAAC,CAACM,IAAI,CAAC,EAAE,CAAC,wdAoBlB,CAED;AACA,KAAM,CAAAsD,aAAa,CAAG,CAAC,GAAGnH,aAAa,CAAC,CAACoH,IAAI,CAAC,CAACR,CAAC,CAAEC,CAAC,GACjD,GAAI,CAAApJ,IAAI,CAACoJ,CAAC,CAAC/F,IAAI,CAAC,CAACuG,OAAO,CAAC,CAAC,CAAG,GAAI,CAAA5J,IAAI,CAACmJ,CAAC,CAAC9F,IAAI,CAAC,CAACuG,OAAO,CAAC,CACxD,CAAC,CAAC3D,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAEdyD,aAAa,CAACnF,OAAO,CAACM,MAAM,EAAI,CAC9B,KAAM,CAAAgF,UAAU,CAAGzH,OAAO,CAACc,IAAI,CAACiG,CAAC,EAAIA,CAAC,CAACW,EAAE,GAAKjF,MAAM,CAACkF,QAAQ,CAAC,EAAI,CAAC,CAAC,CACpElE,IAAI,+BAAAtG,MAAA,CAEMsF,MAAM,CAACkF,QAAQ,OAAAxK,MAAA,CAAKsK,UAAU,CAACpD,IAAI,EAAI,SAAS,yBAAAlH,MAAA,CAChD,GAAI,CAAAS,IAAI,CAAC6E,MAAM,CAACxB,IAAI,CAAC,CAAC5B,kBAAkB,CAAC,OAAO,CAAC,wBAAAlC,MAAA,CACjDsF,MAAM,CAACrF,IAAI,EAAI,UAAU,wBAAAD,MAAA,CACzBsF,MAAM,CAACmF,SAAS,EAAI,GAAG,wBAAAzK,MAAA,CACvBsF,MAAM,CAACoF,SAAS,EAAI,GAAG,wBAAA1K,MAAA,CACvBsF,MAAM,CAACqF,WAAW,EAAI,OAAO,wBAAA3K,MAAA,CAC7BsF,MAAM,CAACsF,YAAY,CAAG,GAAI,CAAAnK,IAAI,CAAC6E,MAAM,CAACsF,YAAY,CAAC,CAAC1I,kBAAkB,CAAC,OAAO,CAAC,CAAG,GAAG,4BAE9F,CACH,CAAC,CAAC,CAEF;AACAoE,IAAI,+dAoBH,CAED;AACA,KAAM,CAAAuE,uBAAuB,CAAGhI,OAAO,CAACoD,MAAM,CAAChB,MAAM,EACnDA,MAAM,CAACG,YAAY,GAAK,MAAM,EAC9BH,MAAM,CAACG,YAAY,GAAK,SAAS,EACjCH,MAAM,CAACG,YAAY,GAAK,UAC1B,CAAC,CAEDyF,uBAAuB,CAAC7F,OAAO,CAACC,MAAM,EAAI,CACxC,KAAM,CAAA6F,UAAU,CAAG9H,aAAa,CAC7BiD,MAAM,CAAC8E,CAAC,EAAIA,CAAC,CAACP,QAAQ,GAAKvF,MAAM,CAACsF,EAAE,CAAC,CACrCH,IAAI,CAAC,CAACR,CAAC,CAAEC,CAAC,GAAK,GAAI,CAAApJ,IAAI,CAACoJ,CAAC,CAAC/F,IAAI,CAAC,CAACuG,OAAO,CAAC,CAAC,CAAG,GAAI,CAAA5J,IAAI,CAACmJ,CAAC,CAAC9F,IAAI,CAAC,CAACuG,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,EAAI,CAAC,CAAC,CAEnF,KAAM,CAAA1B,WAAW,CAAGmC,UAAU,CAAChH,IAAI,CAAG,GAAI,CAAArD,IAAI,CAACqK,UAAU,CAAChH,IAAI,CAAC,CAAG,GAAI,CAAArD,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAGkI,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CACjI,KAAM,CAAAC,WAAW,CAAGgC,UAAU,CAACF,YAAY,CAAG,GAAI,CAAAnK,IAAI,CAACqK,UAAU,CAACF,YAAY,CAAC,CAAG,GAAI,CAAAnK,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAGkI,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CAEjJvC,IAAI,+BAAAtG,MAAA,CAEMiF,MAAM,CAACoD,SAAS,wBAAArI,MAAA,CAChBiF,MAAM,CAACiC,IAAI,wBAAAlH,MAAA,CACXiF,MAAM,CAACC,OAAO,EAAID,MAAM,CAAChF,IAAI,wBAAAD,MAAA,CAC7BiF,MAAM,CAACG,YAAY,wBAAApF,MAAA,CACnB8K,UAAU,CAACL,SAAS,EAAI,mBAAmB,wBAAAzK,MAAA,CAC3C2I,WAAW,CAACzG,kBAAkB,CAAC,OAAO,CAAC,wBAAAlC,MAAA,CACvC8I,WAAW,CAAC5G,kBAAkB,CAAC,OAAO,CAAC,4BAEhD,CACH,CAAC,CAAC,CAEF;AACAoE,IAAI,sDAIH,CAED,MAAO,CAAAA,IAAI,CACb,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAQ,mBAAmB,CAAGA,CAC1BkE,IAAsD,CACtDC,KAAa,GACF,CACX,KAAM,CAAAC,KAAK,CAAGF,IAAI,CAACtF,MAAM,CAAC,CAACC,GAAG,CAAEwF,IAAI,GAAKxF,GAAG,CAAGwF,IAAI,CAAChE,KAAK,CAAE,CAAC,CAAC,CAC7D,GAAI+D,KAAK,GAAK,CAAC,CAAE,MAAO,qEAAqE,CAE7F,KAAM,CAAAE,OAAO,CAAG,GAAG,CACnB,KAAM,CAAAC,OAAO,CAAG,GAAG,CACnB,KAAM,CAAAC,MAAM,CAAG,GAAG,CAElB,GAAI,CAAAC,UAAU,CAAG,CAAC,CAClB,GAAI,CAAAC,KAAK,CAAG,EAAE,CACd,GAAI,CAAAC,MAAM,CAAG,EAAE,CAEfT,IAAI,CAAChG,OAAO,CAAC,CAACmG,IAAI,CAAEnE,KAAK,GAAK,CAC5B,KAAM,CAAA0E,UAAU,CAAGP,IAAI,CAAChE,KAAK,CAAG+D,KAAK,CACrC,KAAM,CAAAS,QAAQ,CAAGJ,UAAU,CAAGG,UAAU,CAAG,CAAC,CAAG9C,IAAI,CAACgD,EAAE,CAEtD;AACA,KAAM,CAAAC,EAAE,CAAGT,OAAO,CAAGE,MAAM,CAAG1C,IAAI,CAACkD,GAAG,CAACP,UAAU,CAAC,CAClD,KAAM,CAAAQ,EAAE,CAAGV,OAAO,CAAGC,MAAM,CAAG1C,IAAI,CAACoD,GAAG,CAACT,UAAU,CAAC,CAClD,KAAM,CAAAU,EAAE,CAAGb,OAAO,CAAGE,MAAM,CAAG1C,IAAI,CAACkD,GAAG,CAACH,QAAQ,CAAC,CAChD,KAAM,CAAAO,EAAE,CAAGb,OAAO,CAAGC,MAAM,CAAG1C,IAAI,CAACoD,GAAG,CAACL,QAAQ,CAAC,CAEhD,KAAM,CAAAQ,YAAY,CAAGT,UAAU,CAAG,GAAG,CAAG,CAAC,CAAG,CAAC,CAE7CF,KAAK,iBAAAxL,MAAA,CAAkBoL,OAAO,MAAApL,MAAA,CAAIqL,OAAO,QAAArL,MAAA,CAAM6L,EAAE,MAAA7L,MAAA,CAAI+L,EAAE,QAAA/L,MAAA,CAAMsL,MAAM,MAAAtL,MAAA,CAAIsL,MAAM,QAAAtL,MAAA,CAAMmM,YAAY,QAAAnM,MAAA,CAAMiM,EAAE,MAAAjM,MAAA,CAAIkM,EAAE,gCAAAlM,MAAA,CAC3FmL,IAAI,CAAC/D,KAAK,6CAAsC,CAElE;AACA,GAAIsE,UAAU,CAAG,IAAI,CAAE,CACrB,KAAM,CAAAU,UAAU,CAAGb,UAAU,CAAG,CAACI,QAAQ,CAAGJ,UAAU,EAAI,CAAC,CAC3D,KAAM,CAAAc,WAAW,CAAGf,MAAM,CAAG,GAAG,CAChC,KAAM,CAAAgB,MAAM,CAAGlB,OAAO,CAAGiB,WAAW,CAAGzD,IAAI,CAACkD,GAAG,CAACM,UAAU,CAAC,CAC3D,KAAM,CAAAG,MAAM,CAAGlB,OAAO,CAAGgB,WAAW,CAAGzD,IAAI,CAACoD,GAAG,CAACI,UAAU,CAAC,CAE3DX,MAAM,eAAAzL,MAAA,CAAgBsM,MAAM,YAAAtM,MAAA,CAAQuM,MAAM,wGAAAvM,MAAA,CAC5B,CAAC0L,UAAU,CAAG,GAAG,EAAEnF,OAAO,CAAC,CAAC,CAAC,8BACzB,CACpB,CAEAgF,UAAU,CAAGI,QAAQ,CACvB,CAAC,CAAC,CAEF,oCAAA3L,MAAA,CAEeoL,OAAO,8EAAApL,MAAA,CAAmEiL,KAAK,oBAAAjL,MAAA,CACxFwL,KAAK,aAAAxL,MAAA,CACLyL,MAAM,mBAGd,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAlC,mBAAmB,CAAGA,CAC1ByB,IAAsD,CACtDC,KAAa,CACbuB,WAAuC,GAC5B,CACX,GAAIxB,IAAI,CAACpG,MAAM,GAAK,CAAC,CAAE,MAAO,qEAAqE,CAEnG,KAAM,CAAA6H,KAAK,CAAG,GAAG,CACjB,KAAM,CAAAC,MAAM,CAAG,GAAG,CAClB,KAAM,CAAAC,MAAM,CAAG,CAAEC,GAAG,CAAE,EAAE,CAAEC,KAAK,CAAE,EAAE,CAAEC,MAAM,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAG,CAAC,CAC3D,KAAM,CAAAC,UAAU,CAAGP,KAAK,CAAGE,MAAM,CAACI,IAAI,CAAGJ,MAAM,CAACE,KAAK,CACrD,KAAM,CAAAI,WAAW,CAAGP,MAAM,CAAGC,MAAM,CAACC,GAAG,CAAGD,MAAM,CAACG,MAAM,CAEvD,KAAM,CAAAI,QAAQ,CAAGtE,IAAI,CAACuE,GAAG,CAAC,GAAGnC,IAAI,CAACrE,GAAG,CAACyG,CAAC,EAAIA,CAAC,CAACjG,KAAK,CAAC,CAAC,CACpD,KAAM,CAAAkG,QAAQ,CAAGL,UAAU,CAAGhC,IAAI,CAACpG,MAAM,CAAG,GAAG,CAC/C,KAAM,CAAA0I,UAAU,CAAGN,UAAU,CAAGhC,IAAI,CAACpG,MAAM,CAAG,GAAG,CAEjD,GAAI,CAAA2I,IAAI,CAAG,EAAE,CACb,GAAI,CAAAC,OAAO,CAAG,EAAE,CAChB,GAAI,CAAAC,OAAO,CAAG,EAAE,CAEhB;AACA,IAAK,GAAI,CAAAC,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAI,CAAC,CAAEA,CAAC,EAAE,CAAE,CAC3B,KAAM,CAAAvG,KAAK,CAAG+F,QAAQ,CAAGQ,CAAC,CAAG,CAAC,CAC9B,KAAM,CAAAC,CAAC,CAAGhB,MAAM,CAACC,GAAG,CAAGK,WAAW,CAAIA,WAAW,CAAGS,CAAC,CAAG,CAAE,CAC1DD,OAAO,wBAAAzN,MAAA,CACO2M,MAAM,CAACI,IAAI,CAAG,CAAC,aAAA/M,MAAA,CAAS2N,CAAC,aAAA3N,MAAA,CAAS2M,MAAM,CAACI,IAAI,aAAA/M,MAAA,CAAS2N,CAAC,4CAAA3N,MAAA,CACxD2M,MAAM,CAACI,IAAI,CAAG,EAAE,YAAA/M,MAAA,CAAQ2N,CAAC,CAAG,CAAC,6CAAA3N,MAAA,CAAsCwM,WAAW,CAAGA,WAAW,CAACrF,KAAK,CAAC,CAAGA,KAAK,iBACvH,CACH,CAEA;AACA6D,IAAI,CAAChG,OAAO,CAAC,CAACmG,IAAI,CAAEnE,KAAK,GAAK,CAC5B,KAAM,CAAA4G,CAAC,CAAGjB,MAAM,CAACI,IAAI,CAAIC,UAAU,CAAGhC,IAAI,CAACpG,MAAM,CAAIoC,KAAK,CAAGsG,UAAU,CAAG,CAAC,CAC3E,KAAM,CAAAO,SAAS,CAAI1C,IAAI,CAAChE,KAAK,CAAG+F,QAAQ,CAAID,WAAW,CACvD,KAAM,CAAAU,CAAC,CAAGhB,MAAM,CAACC,GAAG,CAAGK,WAAW,CAAGY,SAAS,CAE9CN,IAAI,eAAAvN,MAAA,CAAgB4N,CAAC,YAAA5N,MAAA,CAAQ2N,CAAC,gBAAA3N,MAAA,CAAYqN,QAAQ,iBAAArN,MAAA,CAAa6N,SAAS,eAAA7N,MAAA,CAAWmL,IAAI,CAAC/D,KAAK,SAAM,CAEnGoG,OAAO,eAAAxN,MAAA,CAAgB4N,CAAC,CAAGP,QAAQ,CAAC,CAAC,YAAArN,MAAA,CAAQ2M,MAAM,CAACC,GAAG,CAAGK,WAAW,CAAG,EAAE,gDAAAjN,MAAA,CAAyCmL,IAAI,CAACjE,IAAI,WAAS,CACvI,CAAC,CAAC,CAEF,oCAAAlH,MAAA,CAEeyM,KAAK,CAAC,CAAC,8EAAAzM,MAAA,CAAmEiL,KAAK,wDAAAjL,MAAA,CAG9E2M,MAAM,CAACI,IAAI,aAAA/M,MAAA,CAAS2M,MAAM,CAACC,GAAG,aAAA5M,MAAA,CAAS2M,MAAM,CAACI,IAAI,aAAA/M,MAAA,CAAS2M,MAAM,CAACC,GAAG,CAAGK,WAAW,kCAAAjN,MAAA,CAC7FyN,OAAO,iDAAAzN,MAAA,CAGG2M,MAAM,CAACI,IAAI,aAAA/M,MAAA,CAAS2M,MAAM,CAACC,GAAG,CAAGK,WAAW,aAAAjN,MAAA,CAAS2M,MAAM,CAACI,IAAI,CAAGC,UAAU,aAAAhN,MAAA,CAAS2M,MAAM,CAACC,GAAG,CAAGK,WAAW,kCAAAjN,MAAA,CACxHwN,OAAO,oCAAAxN,MAAA,CAGPuN,IAAI,mBAGZ,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAzD,oBAAoB,CAAGA,CAC3BkB,IAAiF,CACjFC,KAAa,CACbuB,WAAuC,GAC5B,CACX,GAAIxB,IAAI,CAACpG,MAAM,GAAK,CAAC,CAAE,MAAO,qEAAqE,CAEnG,KAAM,CAAA6H,KAAK,CAAG,GAAG,CACjB,KAAM,CAAAC,MAAM,CAAG,GAAG,CAClB,KAAM,CAAAC,MAAM,CAAG,CAAEC,GAAG,CAAE,EAAE,CAAEC,KAAK,CAAE,EAAE,CAAEC,MAAM,CAAE,EAAE,CAAEC,IAAI,CAAE,EAAG,CAAC,CAC3D,KAAM,CAAAC,UAAU,CAAGP,KAAK,CAAGE,MAAM,CAACI,IAAI,CAAGJ,MAAM,CAACE,KAAK,CACrD,KAAM,CAAAI,WAAW,CAAGP,MAAM,CAAGC,MAAM,CAACC,GAAG,CAAGD,MAAM,CAACG,MAAM,CAEvD;AACA,KAAM,CAAAgB,SAAS,CAAG9C,IAAI,CAAC+C,OAAO,CAACC,MAAM,EAAIA,MAAM,CAACrE,MAAM,CAAChD,GAAG,CAACsH,CAAC,EAAIA,CAAC,CAAC9G,KAAK,CAAC,CAAC,CACzE,KAAM,CAAA+F,QAAQ,CAAGtE,IAAI,CAACuE,GAAG,CAAC,GAAGW,SAAS,CAAC,CAEvC,GAAI,CAAAI,KAAK,CAAG,EAAE,CACd,GAAI,CAAAC,MAAM,CAAG,EAAE,CACf,GAAI,CAAAX,OAAO,CAAG,EAAE,CAChB,GAAI,CAAAC,OAAO,CAAG,EAAE,CAChB,GAAI,CAAAW,MAAM,CAAG,EAAE,CAEf;AACA,IAAK,GAAI,CAAAV,CAAC,CAAG,CAAC,CAAEA,CAAC,EAAI,CAAC,CAAEA,CAAC,EAAE,CAAE,CAC3B,KAAM,CAAAvG,KAAK,CAAG+F,QAAQ,CAAGQ,CAAC,CAAG,CAAC,CAC9B,KAAM,CAAAC,CAAC,CAAGhB,MAAM,CAACC,GAAG,CAAGK,WAAW,CAAIA,WAAW,CAAGS,CAAC,CAAG,CAAE,CAC1DD,OAAO,wBAAAzN,MAAA,CACO2M,MAAM,CAACI,IAAI,CAAG,CAAC,aAAA/M,MAAA,CAAS2N,CAAC,aAAA3N,MAAA,CAAS2M,MAAM,CAACI,IAAI,aAAA/M,MAAA,CAAS2N,CAAC,4CAAA3N,MAAA,CACxD2M,MAAM,CAACI,IAAI,CAAG,EAAE,YAAA/M,MAAA,CAAQ2N,CAAC,CAAG,CAAC,6CAAA3N,MAAA,CAAsCwM,WAAW,CAAGA,WAAW,CAACrF,KAAK,CAAC,CAAGA,KAAK,iBACvH,CACH,CAEA;AACA6D,IAAI,CAAC,CAAC,CAAC,CAACrB,MAAM,CAAC3E,OAAO,CAAC,CAACmG,IAAI,CAAEnE,KAAK,GAAK,CACtC,KAAM,CAAA4G,CAAC,CAAGjB,MAAM,CAACI,IAAI,CAAIC,UAAU,EAAIhC,IAAI,CAAC,CAAC,CAAC,CAACrB,MAAM,CAAC/E,MAAM,CAAG,CAAC,CAAC,CAAIoC,KAAK,CAC1EwG,OAAO,eAAAxN,MAAA,CAAgB4N,CAAC,YAAA5N,MAAA,CAAQ2M,MAAM,CAACC,GAAG,CAAGK,WAAW,CAAG,EAAE,gDAAAjN,MAAA,CAAyCmL,IAAI,CAACnB,GAAG,WAAS,CACzH,CAAC,CAAC,CAEF;AACAgB,IAAI,CAAChG,OAAO,CAAC,CAACgJ,MAAM,CAAEK,WAAW,GAAK,CACpC,GAAI,CAAAC,QAAQ,CAAG,EAAE,CAEjBN,MAAM,CAACrE,MAAM,CAAC3E,OAAO,CAAC,CAACuJ,KAAK,CAAEvH,KAAK,GAAK,CACtC,KAAM,CAAA4G,CAAC,CAAGjB,MAAM,CAACI,IAAI,CAAIC,UAAU,EAAIgB,MAAM,CAACrE,MAAM,CAAC/E,MAAM,CAAG,CAAC,CAAC,CAAIoC,KAAK,CACzE,KAAM,CAAA2G,CAAC,CAAGhB,MAAM,CAACC,GAAG,CAAGK,WAAW,CAAIsB,KAAK,CAACpH,KAAK,CAAG+F,QAAQ,CAAID,WAAW,CAE3E,GAAIjG,KAAK,GAAK,CAAC,CAAE,CACfsH,QAAQ,OAAAtO,MAAA,CAAS4N,CAAC,MAAA5N,MAAA,CAAI2N,CAAC,CAAE,CAC3B,CAAC,IAAM,CACLW,QAAQ,QAAAtO,MAAA,CAAU4N,CAAC,MAAA5N,MAAA,CAAI2N,CAAC,CAAE,CAC5B,CAEAQ,MAAM,kBAAAnO,MAAA,CAAmB4N,CAAC,aAAA5N,MAAA,CAAS2N,CAAC,uBAAA3N,MAAA,CAAiBuO,KAAK,CAACnH,KAAK,SAAM,CACxE,CAAC,CAAC,CAEF8G,KAAK,eAAAlO,MAAA,CAAgBsO,QAAQ,iBAAAtO,MAAA,CAAagO,MAAM,CAACrE,MAAM,CAAC,CAAC,CAAC,CAACvC,KAAK,0CAAmC,CAEnG;AACAgH,MAAM,sCAAApO,MAAA,CACsB2M,MAAM,CAACI,IAAI,CAAGsB,WAAW,CAAG,GAAG,OAAArO,MAAA,CAAK0M,MAAM,CAAG,EAAE,uEAAA1M,MAAA,CAC1BgO,MAAM,CAACrE,MAAM,CAAC,CAAC,CAAC,CAACvC,KAAK,+EAAApH,MAAA,CAC/BgO,MAAM,CAAC9G,IAAI,6BAElD,CACH,CAAC,CAAC,CAEF,oCAAAlH,MAAA,CAEeyM,KAAK,CAAC,CAAC,8EAAAzM,MAAA,CAAmEiL,KAAK,wDAAAjL,MAAA,CAG9E2M,MAAM,CAACI,IAAI,aAAA/M,MAAA,CAAS2M,MAAM,CAACC,GAAG,aAAA5M,MAAA,CAAS2M,MAAM,CAACI,IAAI,aAAA/M,MAAA,CAAS2M,MAAM,CAACC,GAAG,CAAGK,WAAW,kCAAAjN,MAAA,CAC7FyN,OAAO,iDAAAzN,MAAA,CAGG2M,MAAM,CAACI,IAAI,aAAA/M,MAAA,CAAS2M,MAAM,CAACC,GAAG,CAAGK,WAAW,aAAAjN,MAAA,CAAS2M,MAAM,CAACI,IAAI,CAAGC,UAAU,aAAAhN,MAAA,CAAS2M,MAAM,CAACC,GAAG,CAAGK,WAAW,kCAAAjN,MAAA,CACxHwN,OAAO,0CAAAxN,MAAA,CAGPwO,KAAK,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC,CAAC,CAAC9H,GAAG,CAAC,CAAC+H,CAAC,CAAEhB,CAAC,GAAK,CAC/B,KAAM,CAAAC,CAAC,CAAGhB,MAAM,CAACC,GAAG,CAAGK,WAAW,CAAIA,WAAW,CAAGS,CAAC,CAAG,CAAE,CAC1D,oBAAA1N,MAAA,CAAoB2M,MAAM,CAACI,IAAI,aAAA/M,MAAA,CAAS2N,CAAC,aAAA3N,MAAA,CAAS2M,MAAM,CAACI,IAAI,CAAGC,UAAU,aAAAhN,MAAA,CAAS2N,CAAC,mDACtF,CAAC,CAAC,CAAC9G,IAAI,CAAC,EAAE,CAAC,gDAAA7G,MAAA,CAGTkO,KAAK,aAAAlO,MAAA,CACLmO,MAAM,sCAAAnO,MAAA,CAGNoO,MAAM,mBAGd,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAA7G,oBAAoB,CAAIpC,MAAc,EAAa,CACvD,KAAM,CAAAwJ,WAAW,CAAGxJ,MAAM,CAACyJ,WAAW,CAAC,CAAC,CACxC,GAAID,WAAW,CAACE,QAAQ,CAAC,SAAS,CAAC,EAAIF,WAAW,GAAK,MAAM,CAAE,MAAO,SAAS,CAC/E,GAAIA,WAAW,CAACE,QAAQ,CAAC,MAAM,CAAC,EAAIF,WAAW,CAACE,QAAQ,CAAC,KAAK,CAAC,CAAE,MAAO,SAAS,CACjF,GAAIF,WAAW,CAACE,QAAQ,CAAC,SAAS,CAAC,CAAE,MAAO,SAAS,CACrD,GAAIF,WAAW,CAACE,QAAQ,CAAC,UAAU,CAAC,CAAE,MAAO,SAAS,CACtD,GAAIF,WAAW,CAACE,QAAQ,CAAC,YAAY,CAAC,CAAE,MAAO,SAAS,CACxD,GAAIF,WAAW,CAACE,QAAQ,CAAC,YAAY,CAAC,CAAE,MAAO,SAAS,CACxD,GAAIF,WAAW,CAACE,QAAQ,CAAC,UAAU,CAAC,CAAE,MAAO,SAAS,CACtD,GAAIF,WAAW,CAACE,QAAQ,CAAC,WAAW,CAAC,CAAE,MAAO,SAAS,CACvD,MAAO,SAAS,CAAE;AACpB,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAC,iBAAiB,CAAI7O,IAAY,EAAa,CAClD,KAAM,CAAA8O,SAAS,CAAG9O,IAAI,CAAC2O,WAAW,CAAC,CAAC,CACpC,GAAIG,SAAS,CAACF,QAAQ,CAAC,SAAS,CAAC,EAAIE,SAAS,CAACF,QAAQ,CAAC,QAAQ,CAAC,EAAIE,SAAS,CAACF,QAAQ,CAAC,QAAQ,CAAC,CAAE,MAAO,SAAS,CACnH,GAAIE,SAAS,CAACF,QAAQ,CAAC,SAAS,CAAC,EAAIE,SAAS,CAACF,QAAQ,CAAC,MAAM,CAAC,CAAE,MAAO,SAAS,CACjF,GAAIE,SAAS,CAACF,QAAQ,CAAC,OAAO,CAAC,CAAE,MAAO,SAAS,CACjD,GAAIE,SAAS,CAACF,QAAQ,CAAC,MAAM,CAAC,CAAE,MAAO,SAAS,CAChD,GAAIE,SAAS,CAACF,QAAQ,CAAC,OAAO,CAAC,CAAE,MAAO,SAAS,CACjD,GAAIE,SAAS,CAACF,QAAQ,CAAC,YAAY,CAAC,CAAE,MAAO,SAAS,CACtD,GAAIE,SAAS,CAACF,QAAQ,CAAC,WAAW,CAAC,CAAE,MAAO,SAAS,CACrD,GAAIE,SAAS,CAACF,QAAQ,CAAC,WAAW,CAAC,CAAE,MAAO,SAAS,CACrD,MAAO,SAAS,CAAE;AACpB,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAlM,YAAY,CAAGA,CACnBC,UAAsB,CACtBoM,YAAqB,CACrBC,UAAmB,GACwB,CAC3C,KAAM,CAAAvO,GAAG,CAAG,GAAI,CAAAD,IAAI,CAAC,CAAC,CACtB,KAAM,CAAAiC,OAAO,CAAGuM,UAAU,CAAG,GAAI,CAAAxO,IAAI,CAACwO,UAAU,CAAC,CAAGvO,GAAG,CACvD,GAAI,CAAA+B,SAAe,CAEnB,GAAIG,UAAU,GAAK,QAAQ,EAAIoM,YAAY,CAAE,CAC3CvM,SAAS,CAAG,GAAI,CAAAhC,IAAI,CAACuO,YAAY,CAAC,CACpC,CAAC,IAAM,CACL,OAAQpM,UAAU,EAChB,IAAK,MAAM,CACTH,SAAS,CAAG,GAAI,CAAAhC,IAAI,CAACC,GAAG,CAAC,CACzB+B,SAAS,CAACyM,OAAO,CAACxO,GAAG,CAACyO,OAAO,CAAC,CAAC,CAAG,CAAC,CAAC,CACpC,MACF,IAAK,OAAO,CACV1M,SAAS,CAAG,GAAI,CAAAhC,IAAI,CAACC,GAAG,CAAC,CACzB+B,SAAS,CAAC2M,QAAQ,CAAC1O,GAAG,CAAC2O,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CACtC,MACF,IAAK,SAAS,CACZ5M,SAAS,CAAG,GAAI,CAAAhC,IAAI,CAACC,GAAG,CAAC,CACzB+B,SAAS,CAAC2M,QAAQ,CAAC1O,GAAG,CAAC2O,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CACtC,MACF,IAAK,MAAM,CACT5M,SAAS,CAAG,GAAI,CAAAhC,IAAI,CAACC,GAAG,CAAC,CACzB+B,SAAS,CAAC6M,WAAW,CAAC5O,GAAG,CAAC6O,WAAW,CAAC,CAAC,CAAG,CAAC,CAAC,CAC5C,MACF,QACE9M,SAAS,CAAG,GAAI,CAAAhC,IAAI,CAACC,GAAG,CAAC,CACzB+B,SAAS,CAAC2M,QAAQ,CAAC1O,GAAG,CAAC2O,QAAQ,CAAC,CAAC,CAAG,CAAC,CAAC,CAC1C,CACF,CAEA,MAAO,CACL5M,SAAS,CAAEA,SAAS,CAAC+M,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAChD/M,OAAO,CAAEA,OAAO,CAAC8M,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAC7C,CAAC,CACH,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAC,sBAAsB,CAAI7M,OAAiB,EAAa,CAC5D,GAAI,CAAAyD,IAAI,4aAgBP,CAED;AACAzD,OAAO,CAACmC,OAAO,CAACC,MAAM,EAAI,CACxB,KAAM,CAAA0K,aAAa,CAAG1K,MAAM,CAAC0K,aAAa,EAAI,CAAC,CAC/C;AACA;AACA,KAAM,CAAAC,WAAW,CAAG3K,MAAM,CAACuD,SAAS,CAClCI,IAAI,CAACiH,KAAK,CAAC,CAAC,GAAI,CAAApP,IAAI,CAAC,CAAC,CAAC4J,OAAO,CAAC,CAAC,CAAG,GAAI,CAAA5J,IAAI,CAACwE,MAAM,CAACuD,SAAS,CAAC,CAAC6B,OAAO,CAAC,CAAC,GAAK,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CAAC,CAAG,EAAE,CAE7G;AACA,GAAI,CAAAyF,YAAY,CAAGH,aAAa,CAEhC;AACA,GAAIC,WAAW,CAAG,EAAE,CAAE,CACpBE,YAAY,EAAI,GAAG,CAAE;AACvB,CAAC,IAAM,IAAIF,WAAW,CAAG,EAAE,CAAE,CAC3BE,YAAY,EAAI,GAAG,CAAE;AACvB,CAEA;AACA,GAAI7K,MAAM,CAACG,YAAY,GAAK,SAAS,CAAE,CACrC0K,YAAY,EAAI,GAAG,CAAE;AACvB,CAAC,IAAM,IAAI7K,MAAM,CAACG,YAAY,GAAK,MAAM,EAAIH,MAAM,CAACG,YAAY,GAAK,SAAS,CAAE,CAC9E0K,YAAY,EAAI,GAAG,CAAE;AACvB,CAEA;AACA,KAAM,CAAAzJ,GAAG,CAAGsJ,aAAa,CAAG,CAAC,CAAI,CAACG,YAAY,CAAGH,aAAa,EAAIA,aAAa,CAAG,GAAG,CAAI,CAAC,CAE1FrJ,IAAI,+BAAAtG,MAAA,CAEMiF,MAAM,CAACoD,SAAS,wBAAArI,MAAA,CAChBiF,MAAM,CAACiC,IAAI,wBAAAlH,MAAA,CACXiF,MAAM,CAACC,OAAO,EAAID,MAAM,CAAChF,IAAI,wBAAAD,MAAA,CAC7BiF,MAAM,CAAC8K,YAAY,EAAI,GAAG,0BAAA/P,MAAA,CACxB2P,aAAa,CAAC5N,cAAc,CAAC,CAAC,0BAAA/B,MAAA,CAC9B8P,YAAY,CAAC/N,cAAc,CAACiO,SAAS,CAAE,CAAEC,qBAAqB,CAAE,CAAC,CAAEC,qBAAqB,CAAE,CAAE,CAAC,CAAC,wBAAAlQ,MAAA,CAChGqG,GAAG,CAACE,OAAO,CAAC,CAAC,CAAC,6BAEvB,CACH,CAAC,CAAC,CAEF;AACAD,IAAI,sDAIH,CAED,MAAO,CAAAA,IAAI,CACb,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAqB,+BAA+B,CAAGA,CAAC9E,OAAc,CAAEK,gBAAuB,GAAa,CAC3F;AACA,KAAM,CAAAyB,YAAY,CAAG9B,OAAO,CAAC+B,MAAM,CAEnC;AACA,KAAM,CAAAoB,YAAY,CAAG9C,gBAAgB,CAClC+C,MAAM,CAACX,MAAM,EAAIA,MAAM,CAACrF,IAAI,GAAK,QAAQ,CAAC,CAC1CyF,MAAM,CAAC,CAACC,GAAG,CAAEL,MAAM,GAAKK,GAAG,EAAIL,MAAM,CAACY,MAAM,EAAI,CAAC,CAAC,CAAE,CAAC,CAAC,CAEzD,KAAM,CAAAC,aAAa,CAAGjD,gBAAgB,CACnC+C,MAAM,CAACX,MAAM,EAAIA,MAAM,CAACrF,IAAI,GAAK,SAAS,CAAC,CAC3CyF,MAAM,CAAC,CAACC,GAAG,CAAEL,MAAM,GAAKK,GAAG,EAAIL,MAAM,CAACY,MAAM,EAAI,CAAC,CAAC,CAAE,CAAC,CAAC,CAEzD,KAAM,CAAAE,SAAS,CAAGJ,YAAY,CAAGG,aAAa,CAC9C,KAAM,CAAAgK,YAAY,CAAGnK,YAAY,CAAG,CAAC,CAAII,SAAS,CAAGJ,YAAY,CAAI,GAAG,CAAG,CAAC,CAE5E;AACA,KAAM,CAAAoK,kBAA0C,CAAG,CAAC,CAAC,CACrDlN,gBAAgB,CACb+C,MAAM,CAACX,MAAM,EAAIA,MAAM,CAACrF,IAAI,GAAK,SAAS,CAAC,CAC3C+E,OAAO,CAACM,MAAM,EAAI,CACjB,GAAIA,MAAM,CAAC+K,QAAQ,CAAE,CACnBD,kBAAkB,CAAC9K,MAAM,CAAC+K,QAAQ,CAAC,CAAG,CAACD,kBAAkB,CAAC9K,MAAM,CAAC+K,QAAQ,CAAC,EAAI,CAAC,GAAK/K,MAAM,CAACY,MAAM,EAAI,CAAC,CAAC,CACzG,CACF,CAAC,CAAC,CAEJ;AACA,KAAM,CAAAoK,eAAuC,CAAG,CAAC,CAAC,CAClDpN,gBAAgB,CACb+C,MAAM,CAACX,MAAM,EAAIA,MAAM,CAACrF,IAAI,GAAK,QAAQ,CAAC,CAC1C+E,OAAO,CAACM,MAAM,EAAI,CACjB,GAAIA,MAAM,CAAC+K,QAAQ,CAAE,CACnBC,eAAe,CAAChL,MAAM,CAAC+K,QAAQ,CAAC,CAAG,CAACC,eAAe,CAAChL,MAAM,CAAC+K,QAAQ,CAAC,EAAI,CAAC,GAAK/K,MAAM,CAACY,MAAM,EAAI,CAAC,CAAC,CACnG,CACF,CAAC,CAAC,CAEJ;AACA,KAAM,CAAAqK,oBAA2F,CAAG,CAAC,CAAC,CAEtGrN,gBAAgB,CAAC8B,OAAO,CAACM,MAAM,EAAI,CACjC,GAAIA,MAAM,CAACxB,IAAI,CAAE,CACf,KAAM,CAAA1B,KAAK,CAAG,GAAI,CAAA3B,IAAI,CAAC6E,MAAM,CAACxB,IAAI,CAAC,CAAC/B,cAAc,CAAC,OAAO,CAAE,CAAEK,KAAK,CAAE,OAAO,CAAED,IAAI,CAAE,SAAU,CAAC,CAAC,CAEhG,GAAI,CAACoO,oBAAoB,CAACnO,KAAK,CAAC,CAAE,CAChCmO,oBAAoB,CAACnO,KAAK,CAAC,CAAG,CAAEoO,OAAO,CAAE,CAAC,CAAEC,QAAQ,CAAE,CAAC,CAAEC,MAAM,CAAE,CAAE,CAAC,CACtE,CAEA,GAAIpL,MAAM,CAACrF,IAAI,GAAK,QAAQ,CAAE,CAC5BsQ,oBAAoB,CAACnO,KAAK,CAAC,CAACoO,OAAO,EAAKlL,MAAM,CAACY,MAAM,EAAI,CAAE,CAC7D,CAAC,IAAM,IAAIZ,MAAM,CAACrF,IAAI,GAAK,SAAS,CAAE,CACpCsQ,oBAAoB,CAACnO,KAAK,CAAC,CAACqO,QAAQ,EAAKnL,MAAM,CAACY,MAAM,EAAI,CAAE,CAC9D,CAEAqK,oBAAoB,CAACnO,KAAK,CAAC,CAACsO,MAAM,CAAGH,oBAAoB,CAACnO,KAAK,CAAC,CAACoO,OAAO,CAAGD,oBAAoB,CAACnO,KAAK,CAAC,CAACqO,QAAQ,CACjH,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAE,eAAe,CAAG9N,OAAO,CAAC6C,MAAM,CAAC,CAACC,GAAG,CAAEV,MAAM,GAAK,CACtD;AACA,GAAI,CAAAkC,KAAK,CAAGlC,MAAM,CAAC0K,aAAa,EAAI,CAAC,CAErC,GAAI1K,MAAM,CAACG,YAAY,GAAK,SAAS,CAAE,CACrC+B,KAAK,EAAI,GAAG,CAAE;AAChB,CAAC,IAAM,IAAIlC,MAAM,CAACG,YAAY,GAAK,MAAM,EAAIH,MAAM,CAACG,YAAY,GAAK,SAAS,CAAE,CAC9E+B,KAAK,EAAI,GAAG,CAAE;AAChB,CAEA,MAAO,CAAAxB,GAAG,CAAGwB,KAAK,CACpB,CAAC,CAAE,CAAC,CAAC,CAEL;AACA,GAAI,CAAAb,IAAI,4QAAAtG,MAAA,CAM4BgG,YAAY,CAACjE,cAAc,CAAC,CAAC,gKAAA/B,MAAA,CAI7BmG,aAAa,CAACpE,cAAc,CAAC,CAAC,4JAAA/B,MAAA,CAI9BoG,SAAS,CAACrE,cAAc,CAAC,CAAC,6JAAA/B,MAAA,CAI5BmQ,YAAY,CAAC5J,OAAO,CAAC,CAAC,CAAC,yYAAAvG,MAAA,CAU/C8J,oBAAoB,CACpB,CACE,CACE5C,IAAI,CAAE,SAAS,CACfyC,MAAM,CAAEnD,MAAM,CAACC,OAAO,CAAC8J,oBAAoB,CAAC,CAAC5J,GAAG,CAACiK,MAAA,MAAC,CAACxO,KAAK,CAAE4I,IAAI,CAAC,CAAA4F,MAAA,OAAM,CACnE5G,GAAG,CAAE5H,KAAK,CACV+E,KAAK,CAAE6D,IAAI,CAACwF,OAAO,CACnBpJ,KAAK,CAAE,SAAW;AACpB,CAAC,EAAC,CACJ,CAAC,CACD,CACEF,IAAI,CAAE,UAAU,CAChByC,MAAM,CAAEnD,MAAM,CAACC,OAAO,CAAC8J,oBAAoB,CAAC,CAAC5J,GAAG,CAACkK,MAAA,MAAC,CAACzO,KAAK,CAAE4I,IAAI,CAAC,CAAA6F,MAAA,OAAM,CACnE7G,GAAG,CAAE5H,KAAK,CACV+E,KAAK,CAAE6D,IAAI,CAACyF,QAAQ,CACpBrJ,KAAK,CAAE,SAAW;AACpB,CAAC,EAAC,CACJ,CAAC,CACD,CACEF,IAAI,CAAE,QAAQ,CACdyC,MAAM,CAAEnD,MAAM,CAACC,OAAO,CAAC8J,oBAAoB,CAAC,CAAC5J,GAAG,CAACmK,MAAA,MAAC,CAAC1O,KAAK,CAAE4I,IAAI,CAAC,CAAA8F,MAAA,OAAM,CACnE9G,GAAG,CAAE5H,KAAK,CACV+E,KAAK,CAAE6D,IAAI,CAAC0F,MAAM,CAClBtJ,KAAK,CAAE,SAAW;AACpB,CAAC,EAAC,CACJ,CAAC,CACF,CACD,+BAA+B,CAC9BD,KAAK,MAAAnH,MAAA,CAASmH,KAAK,CAACpF,cAAc,CAAC,CAAC,CACvC,CAAC,8/BAAA/B,MAAA,CAwBC8G,mBAAmB,CACnBN,MAAM,CAACC,OAAO,CAAC2J,kBAAkB,CAAC,CAACzJ,GAAG,CAACoK,MAAA,MAAC,CAACV,QAAQ,CAAEnK,MAAM,CAAC,CAAA6K,MAAA,OAAM,CAC9D7J,IAAI,CAAEmJ,QAAQ,CACdlJ,KAAK,CAAEjB,MAAM,CACbkB,KAAK,CAAE0H,iBAAiB,CAACuB,QAAQ,CACnC,CAAC,EAAC,CAAC,CACH,oBACF,CAAC,uEAAArQ,MAAA,CAGCwG,MAAM,CAACC,OAAO,CAAC2J,kBAAkB,CAAC,CAACzJ,GAAG,CAACqK,MAAA,MAAC,CAACX,QAAQ,CAAEnK,MAAM,CAAC,CAAA8K,MAAA,0HAAAhR,MAAA,CAEF8O,iBAAiB,CAACuB,QAAQ,CAAC,6DAAArQ,MAAA,CACpDqQ,QAAQ,QAAArQ,MAAA,CAAMkG,MAAM,CAACnE,cAAc,CAAC,CAAC,OAAA/B,MAAA,CAAK,CAAEkG,MAAM,CAAGC,aAAa,CAAI,GAAG,EAAEI,OAAO,CAAC,CAAC,CAAC,+CAErH,CAAC,CAACM,IAAI,CAAC,EAAE,CAAC,qWAAA7G,MAAA,CAUT8G,mBAAmB,CACnBN,MAAM,CAACC,OAAO,CAAC6J,eAAe,CAAC,CAAC3J,GAAG,CAACsK,MAAA,MAAC,CAACC,MAAM,CAAEhL,MAAM,CAAC,CAAA+K,MAAA,OAAM,CACzD/J,IAAI,CAAEgK,MAAM,CACZ/J,KAAK,CAAEjB,MAAM,CACbkB,KAAK,CAAE0H,iBAAiB,CAACoC,MAAM,CACjC,CAAC,EAAC,CAAC,CACH,iBACF,CAAC,uEAAAlR,MAAA,CAGCwG,MAAM,CAACC,OAAO,CAAC6J,eAAe,CAAC,CAAC3J,GAAG,CAACwK,MAAA,MAAC,CAACD,MAAM,CAAEhL,MAAM,CAAC,CAAAiL,MAAA,0HAAAnR,MAAA,CAEG8O,iBAAiB,CAACoC,MAAM,CAAC,6DAAAlR,MAAA,CAClDkR,MAAM,QAAAlR,MAAA,CAAMkG,MAAM,CAACnE,cAAc,CAAC,CAAC,OAAA/B,MAAA,CAAK,CAAEkG,MAAM,CAAGF,YAAY,CAAI,GAAG,EAAEO,OAAO,CAAC,CAAC,CAAC,+CAElH,CAAC,CAACM,IAAI,CAAC,EAAE,CAAC,0TAAA7G,MAAA,CAUiB2Q,eAAe,CAAC5O,cAAc,CAAC,CAAC,qKAAA/B,MAAA,CAIhC,CAAC2Q,eAAe,EAAIhM,YAAY,EAAI,CAAC,CAAC,EAAE5C,cAAc,CAACiO,SAAS,CAAE,CAAEE,qBAAqB,CAAE,CAAE,CAAC,CAAC,sKAAAlQ,MAAA,CAIjG,CAAC2Q,eAAe,EAAI3K,YAAY,EAAI,CAAC,CAAC,EAAEO,OAAO,CAAC,CAAC,CAAC,qeAoBnF,CAED;AACA,KAAM,CAAA4D,aAAa,CAAG,CAAC,GAAGjH,gBAAgB,CAAC,CAACkH,IAAI,CAAC,CAACR,CAAC,CAAEC,CAAC,GACpD,GAAI,CAAApJ,IAAI,CAACoJ,CAAC,CAAC/F,IAAI,CAAC,CAACuG,OAAO,CAAC,CAAC,CAAG,GAAI,CAAA5J,IAAI,CAACmJ,CAAC,CAAC9F,IAAI,CAAC,CAACuG,OAAO,CAAC,CACxD,CAAC,CAAC3D,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAEdyD,aAAa,CAACnF,OAAO,CAACM,MAAM,EAAI,CAC9BgB,IAAI,+BAAAtG,MAAA,CAEM,GAAI,CAAAS,IAAI,CAAC6E,MAAM,CAACxB,IAAI,CAAC,CAAC5B,kBAAkB,CAAC,OAAO,CAAC,wBAAAlC,MAAA,CACjDsF,MAAM,CAACrF,IAAI,GAAK,QAAQ,CAAG,QAAQ,CAAG,SAAS,wBAAAD,MAAA,CAC/CsF,MAAM,CAAC+K,QAAQ,EAAI,GAAG,wBAAArQ,MAAA,CACtBsF,MAAM,CAAC8L,WAAW,EAAI,GAAG,0BAAApR,MAAA,CACvB,CAACsF,MAAM,CAACY,MAAM,EAAI,CAAC,EAAEnE,cAAc,CAAC,CAAC,wBAAA/B,MAAA,CACvCsF,MAAM,CAAC+L,aAAa,EAAI,GAAG,wBAAArR,MAAA,CAC3BsF,MAAM,CAACgM,SAAS,EAAI,GAAG,4BAEhC,CACH,CAAC,CAAC,CAEF;AACAhL,IAAI,4cAmBH,CAED;AACA,KAAM,CAAAiL,gBAAgB,CAAG,CACvB,MAAM,CAAE,YAAY,CAAE,OAAO,CAAE,WAAW,CAAE,aAAa,CAAE,WAAW,CAAE,WAAW,CACpF,CAEDA,gBAAgB,CAACvM,OAAO,CAACqL,QAAQ,EAAI,CACnC,KAAM,CAAAmB,QAAQ,CAAG5I,IAAI,CAAC6I,KAAK,CAAC7I,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,KAAK,CAAC,CAAG,KAAK,CAC1D,KAAM,CAAA6I,MAAM,CAAGtB,kBAAkB,CAACC,QAAQ,CAAC,EAAIzH,IAAI,CAAC6I,KAAK,CAAC7I,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG2I,QAAQ,CAAG,GAAG,CAAC,CACzF,KAAM,CAAAG,QAAQ,CAAGH,QAAQ,CAAGE,MAAM,CAClC,KAAM,CAAAE,eAAe,CAAID,QAAQ,CAAGH,QAAQ,CAAI,GAAG,CACnD,KAAM,CAAArM,MAAM,CAAGyM,eAAe,CAAG,EAAE,CAAG,cAAc,CAAGA,eAAe,CAAG,CAAC,EAAE,CAAG,aAAa,CAAG,WAAW,CAE1GtL,IAAI,+BAAAtG,MAAA,CAEMqQ,QAAQ,0BAAArQ,MAAA,CACNwR,QAAQ,CAACzP,cAAc,CAAC,CAAC,0BAAA/B,MAAA,CACzB0R,MAAM,CAAC3P,cAAc,CAAC,CAAC,0BAAA/B,MAAA,CACvB2R,QAAQ,CAAC5P,cAAc,CAAC,CAAC,wBAAA/B,MAAA,CAC3B4R,eAAe,CAACrL,OAAO,CAAC,CAAC,CAAC,yBAAAvG,MAAA,CAC1BmF,MAAM,4BAEf,CACH,CAAC,CAAC,CAEF;AACAmB,IAAI,sDAIH,CAED,MAAO,CAAAA,IAAI,CACb,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAuL,mBAAmB,CAAIhP,OAAiB,EAAa,CACzD;AACA,MAAO,CAAA6M,sBAAsB,CAAC7M,OAAO,CAAC,CACxC,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAA+E,4BAA4B,CAAI/E,OAAc,EAAa,CAC/D;AACA,KAAM,CAAA8B,YAAY,CAAG9B,OAAO,CAAC+B,MAAM,CACnC,KAAM,CAAAC,aAAqC,CAAG,CAAC,CAAC,CAChD,KAAM,CAAAiN,oBAA4C,CAAG,CAAC,CAAC,CACvD,KAAM,CAAAC,YAAmF,CAAG,CAAC,CAAC,CAE9F;AACAlP,OAAO,CAACmC,OAAO,CAACC,MAAM,EAAI,CACxB,GAAIA,MAAM,CAACC,OAAO,CAAE,CAClBL,aAAa,CAACI,MAAM,CAACC,OAAO,CAAC,CAAG,CAACL,aAAa,CAACI,MAAM,CAACC,OAAO,CAAC,EAAI,CAAC,EAAI,CAAC,CAExE;AACA,KAAM,CAAA8M,WAAW,CAAG/M,MAAM,CAAC0K,aAAa,CAAG1K,MAAM,CAAC0K,aAAa,CAAG,GAAG,CAAG,IAAI,CAAE;AAC9EmC,oBAAoB,CAAC7M,MAAM,CAACC,OAAO,CAAC,CAAG,CAAC4M,oBAAoB,CAAC7M,MAAM,CAACC,OAAO,CAAC,EAAI,CAAC,EAAI8M,WAAW,CAClG,CACF,CAAC,CAAC,CAEF;AACAxL,MAAM,CAACyL,IAAI,CAACpN,aAAa,CAAC,CAACG,OAAO,CAACE,OAAO,EAAI,CAC5C,KAAM,CAAA4K,YAAY,CAAGgC,oBAAoB,CAAC5M,OAAO,CAAC,CAAGL,aAAa,CAACK,OAAO,CAAC,CAC3E,KAAM,CAAAgN,aAAa,CAAGpC,YAAY,EAAI,GAAG,CAAGlH,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,CAAC,CAAE;AAClE,KAAM,CAAAsJ,MAAM,CAAI,CAACrC,YAAY,CAAGoC,aAAa,EAAIA,aAAa,CAAI,GAAG,CAErEH,YAAY,CAAC7M,OAAO,CAAC,CAAG,CACtBkN,OAAO,CAAEtC,YAAY,CACrBuC,QAAQ,CAAEH,aAAa,CACvBC,MAAM,CAAEA,MACV,CAAC,CACH,CAAC,CAAC,CAEF;AACA,GAAI,CAAA7L,IAAI,kRAAAtG,MAAA,CAM0B2E,YAAY,oKAAA3E,MAAA,CAIVwG,MAAM,CAACmD,MAAM,CAACmI,oBAAoB,CAAC,CAACpM,MAAM,CAAC,CAACC,GAAG,CAAEwB,KAAK,GAAKxB,GAAG,CAAGwB,KAAK,CAAE,CAAC,CAAC,CAACpF,cAAc,CAAC,CAAC,0KAAA/B,MAAA,CAI3F,CAACwG,MAAM,CAACmD,MAAM,CAACmI,oBAAoB,CAAC,CAACpM,MAAM,CAAC,CAACC,GAAG,CAAEwB,KAAK,GAAKxB,GAAG,CAAGwB,KAAK,CAAE,CAAC,CAAC,CAAGxC,YAAY,EAAE5C,cAAc,CAACiO,SAAS,CAAE,CAAEE,qBAAqB,CAAE,CAAE,CAAC,CAAC,2XAAAlQ,MAAA,CAU7KuJ,mBAAmB,CACnB/C,MAAM,CAACC,OAAO,CAACqL,oBAAoB,CAAC,CAACnL,GAAG,CAAC,CAAA2L,MAAA,CAAmBtL,KAAK,OAAvB,CAAC9B,OAAO,CAAEiC,KAAK,CAAC,CAAAmL,MAAA,OAAa,CACrEpL,IAAI,CAAEhC,OAAO,CACbiC,KAAK,CAAEA,KAAK,CACZC,KAAK,QAAApH,MAAA,CAASgH,KAAK,CAAG,EAAE,eAC1B,CAAC,EAAC,CAAC,CACH,yBAAyB,CACxBG,KAAK,MAAAnH,MAAA,CAAS4I,IAAI,CAAC6I,KAAK,CAACtK,KAAK,CAAC,CAACpF,cAAc,CAAC,CAAC,CACnD,CAAC,uEAAA/B,MAAA,CAGCwG,MAAM,CAACC,OAAO,CAACqL,oBAAoB,CAAC,CAACnL,GAAG,CAAC,CAAA4L,MAAA,CAAmBvL,KAAK,OAAvB,CAAC9B,OAAO,CAAEiC,KAAK,CAAC,CAAAoL,MAAA,8HAAAvS,MAAA,CAEEgH,KAAK,CAAG,EAAE,wEAAAhH,MAAA,CACvCkF,OAAO,QAAAlF,MAAA,CAAMmH,KAAK,CAACpF,cAAc,CAAC,CAAC,6CAEnE,CAAC,CAAC8E,IAAI,CAAC,EAAE,CAAC,6WAAA7G,MAAA,CAUTuJ,mBAAmB,CACnB/C,MAAM,CAACC,OAAO,CAACsL,YAAY,CAAC,CAACpL,GAAG,CAAC,CAAA6L,MAAA,CAAkBxL,KAAK,OAAtB,CAAC9B,OAAO,CAAE8F,IAAI,CAAC,CAAAwH,MAAA,OAAa,CAC5DtL,IAAI,CAAEhC,OAAO,CACbiC,KAAK,CAAE6D,IAAI,CAACmH,MAAM,CAClB/K,KAAK,CAAE4D,IAAI,CAACmH,MAAM,EAAI,CAAC,CAAG,SAAS,CAAG,SAAW;AACnD,CAAC,EAAC,CAAC,CACH,yBAAyB,CACxBhL,KAAK,KAAAnH,MAAA,CAAQmH,KAAK,CAAG,CAAC,CAAG,GAAG,CAAG,EAAE,EAAAnH,MAAA,CAAGmH,KAAK,CAACZ,OAAO,CAAC,CAAC,CAAC,KACvD,CAAC,uEAAAvG,MAAA,CAGCwG,MAAM,CAACC,OAAO,CAACsL,YAAY,CAAC,CAACpL,GAAG,CAAC8L,MAAA,MAAC,CAACvN,OAAO,CAAE8F,IAAI,CAAC,CAAAyH,MAAA,0HAAAzS,MAAA,CAEOgL,IAAI,CAACmH,MAAM,EAAI,CAAC,CAAG,SAAS,CAAG,SAAS,6DAAAnS,MAAA,CACjEkF,OAAO,gBAAAlF,MAAA,CAAcgL,IAAI,CAACoH,OAAO,CAACrQ,cAAc,CAAC,CAAC,OAAA/B,MAAA,CAAKgL,IAAI,CAACmH,MAAM,CAAG,CAAC,CAAG,GAAG,CAAG,EAAE,EAAAnS,MAAA,CAAGgL,IAAI,CAACmH,MAAM,CAAC5L,OAAO,CAAC,CAAC,CAAC,+CAE1I,CAAC,CAACM,IAAI,CAAC,EAAE,CAAC,waAkBlB,CAED;AACAL,MAAM,CAACC,OAAO,CAACsL,YAAY,CAAC,CAAC/M,OAAO,CAAC0N,MAAA,EAAqB,IAApB,CAACxN,OAAO,CAAE8F,IAAI,CAAC,CAAA0H,MAAA,CACnDpM,IAAI,+BAAAtG,MAAA,CAEMkF,OAAO,0BAAAlF,MAAA,CACLgL,IAAI,CAACoH,OAAO,CAACrQ,cAAc,CAACiO,SAAS,CAAE,CAAEE,qBAAqB,CAAE,CAAE,CAAC,CAAC,0BAAAlQ,MAAA,CACpEgL,IAAI,CAACqH,QAAQ,CAACtQ,cAAc,CAACiO,SAAS,CAAE,CAAEE,qBAAqB,CAAE,CAAE,CAAC,CAAC,wBAAAlQ,MAAA,CACvEgL,IAAI,CAACmH,MAAM,CAAG,CAAC,CAAG,GAAG,CAAG,EAAE,EAAAnS,MAAA,CAAGgL,IAAI,CAACmH,MAAM,CAAC5L,OAAO,CAAC,CAAC,CAAC,yBAAAvG,MAAA,CACnDgL,IAAI,CAACmH,MAAM,CAAG,CAAC,CAAG,mBAAmB,CAAGnH,IAAI,CAACmH,MAAM,CAAG,CAAC,CAAG,mBAAmB,CAAGnH,IAAI,CAACmH,MAAM,CAAG,CAAC,CAAC,CAAG,mBAAmB,CAAG,mBAAmB,4BAErJ,CACH,CAAC,CAAC,CAEF;AACA7L,IAAI,qdAoBH,CAED;AACA,KAAM,CAAAqM,kBAAkB,CAAG9P,OAAO,CAACoD,MAAM,CAAChB,MAAM,EAC9CA,MAAM,CAACE,MAAM,GAAK,QAAQ,EAC1BF,MAAM,CAACG,YAAY,GAAK,SAC1B,CAAC,CAACsB,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAAE;AAEhBiM,kBAAkB,CAAC3N,OAAO,CAACC,MAAM,EAAI,CACnC,KAAM,CAAA2K,WAAW,CAAG3K,MAAM,CAACuD,SAAS,CAClCI,IAAI,CAACiH,KAAK,CAAC,CAAC,GAAI,CAAApP,IAAI,CAAC,CAAC,CAAC4J,OAAO,CAAC,CAAC,CAAG,GAAI,CAAA5J,IAAI,CAACwE,MAAM,CAACuD,SAAS,CAAC,CAAC6B,OAAO,CAAC,CAAC,GAAK,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CAAC,CAAG,EAAE,CAE7G,KAAM,CAAAuI,cAAc,CAAG3N,MAAM,CAAC0K,aAAa,CAAG1K,MAAM,CAAC0K,aAAa,CAAG,GAAG,CAAG,IAAI,CAE/ErJ,IAAI,+BAAAtG,MAAA,CAEMiF,MAAM,CAACoD,SAAS,wBAAArI,MAAA,CAChBiF,MAAM,CAACiC,IAAI,wBAAAlH,MAAA,CACXiF,MAAM,CAACC,OAAO,EAAID,MAAM,CAAChF,IAAI,wBAAAD,MAAA,CAC7B4P,WAAW,+BAAA5P,MAAA,CACXiF,MAAM,CAAC4N,MAAM,EAAI,KAAK,2BAAA7S,MAAA,CACtBiF,MAAM,CAACG,YAAY,0BAAApF,MAAA,CACjB4S,cAAc,CAAC7Q,cAAc,CAACiO,SAAS,CAAE,CAAEE,qBAAqB,CAAE,CAAE,CAAC,CAAC,4BAEjF,CACH,CAAC,CAAC,CAEF;AACA5J,IAAI,sDAIH,CAED,MAAO,CAAAA,IAAI,CACb,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAwM,wBAAwB,CAAIjQ,OAAiB,EAAa,CAC9D;AACA,MAAO,CAAA6F,mBAAmB,CAAC7F,OAAO,CAAC,CACrC,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAgF,iCAAiC,CAAIhF,OAAc,EAAa,CACpE;AACA,KAAM,CAAA8B,YAAY,CAAG9B,OAAO,CAAC+B,MAAM,CACnC,KAAM,CAAAmO,kBAA+F,CAAG,CAAC,CAAC,CAC1G,KAAM,CAAAC,iBAA2C,CAAG,CAClD,aAAa,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CAC7C,iBAAiB,CAAE,CAAC,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAE,GAAG,CAAC,CACjD,kBAAkB,CAAE,CAAC,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAAE,EAAE,CAC7C,CAAC,CAED;AACAnQ,OAAO,CAACmC,OAAO,CAACC,MAAM,EAAI,CACxB,GAAIA,MAAM,CAACC,OAAO,CAAE,CAClB,GAAI,CAAC6N,kBAAkB,CAAC9N,MAAM,CAACC,OAAO,CAAC,CAAE,CACvC6N,kBAAkB,CAAC9N,MAAM,CAACC,OAAO,CAAC,CAAG,CAAE+B,KAAK,CAAE,CAAC,CAAEgM,SAAS,CAAE,CAAC,CAAEC,aAAa,CAAE,CAAE,CAAC,CACnF,CAEAH,kBAAkB,CAAC9N,MAAM,CAACC,OAAO,CAAC,CAAC+B,KAAK,EAAE,CAE1C;AACA,KAAM,CAAAkM,UAAU,CAAG,CAAC,CAAGvK,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CAAE;AAC1C,KAAM,CAAAuK,cAAc,CAAG,EAAE,CAAGxK,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,EAAE,CAAE;AAEhD;AACAkK,kBAAkB,CAAC9N,MAAM,CAACC,OAAO,CAAC,CAAC+N,SAAS,CAC1C,CAACF,kBAAkB,CAAC9N,MAAM,CAACC,OAAO,CAAC,CAAC+N,SAAS,EAAIF,kBAAkB,CAAC9N,MAAM,CAACC,OAAO,CAAC,CAAC+B,KAAK,CAAG,CAAC,CAAC,CAAGkM,UAAU,EAC3GJ,kBAAkB,CAAC9N,MAAM,CAACC,OAAO,CAAC,CAAC+B,KAAK,CAE1C8L,kBAAkB,CAAC9N,MAAM,CAACC,OAAO,CAAC,CAACgO,aAAa,CAC9C,CAACH,kBAAkB,CAAC9N,MAAM,CAACC,OAAO,CAAC,CAACgO,aAAa,EAAIH,kBAAkB,CAAC9N,MAAM,CAACC,OAAO,CAAC,CAAC+B,KAAK,CAAG,CAAC,CAAC,CAAGmM,cAAc,EACnHL,kBAAkB,CAAC9N,MAAM,CAACC,OAAO,CAAC,CAAC+B,KAAK,CAC5C,CACF,CAAC,CAAC,CAEF;AACA,GAAI,CAAAX,IAAI,4QAAAtG,MAAA,CAM0B2E,YAAY,mKAAA3E,MAAA,CAIZwG,MAAM,CAACmD,MAAM,CAACoJ,kBAAkB,CAAC,CAACrN,MAAM,CAAC,CAACC,GAAG,CAAEqF,IAAI,GAAKrF,GAAG,CAAGqF,IAAI,CAACiI,SAAS,CAAGjI,IAAI,CAAC/D,KAAK,CAAE,CAAC,CAAC,CAAGtC,YAAY,EAAI,CAAC,mKAAA3E,MAAA,CAIjHwG,MAAM,CAACmD,MAAM,CAACoJ,kBAAkB,CAAC,CAACrN,MAAM,CAAC,CAACC,GAAG,CAAEqF,IAAI,GAAKrF,GAAG,CAAGqF,IAAI,CAACkI,aAAa,CAAGlI,IAAI,CAAC/D,KAAK,CAAE,CAAC,CAAC,CAAGtC,YAAY,EAAI,CAAC,mXAAA3E,MAAA,CAU7I8J,oBAAoB,CACpBtD,MAAM,CAACC,OAAO,CAACuM,iBAAiB,CAAC,CAACrM,GAAG,CAAC0M,MAAA,MAAC,CAACC,MAAM,CAAE3J,MAAM,CAAC,CAAA0J,MAAA,OAAM,CAC3DnM,IAAI,CAAEoM,MAAM,CACZ3J,MAAM,CAAEA,MAAM,CAAChD,GAAG,CAAC,CAACQ,KAAK,CAAEH,KAAK,GAAK,CACnC;AACA,KAAM,CAAAlD,IAAI,CAAG,GAAI,CAAArD,IAAI,CAAC,CAAC,CACvBqD,IAAI,CAACsL,QAAQ,CAACtL,IAAI,CAACuL,QAAQ,CAAC,CAAC,EAAI,CAAC,CAAGrI,KAAK,CAAC,CAAC,CAC5C,KAAM,CAAA5E,KAAK,CAAG0B,IAAI,CAAC/B,cAAc,CAAC,OAAO,CAAE,CAAEK,KAAK,CAAE,OAAQ,CAAC,CAAC,CAE9D,GAAI,CAAAgF,KAAK,CAAG,SAAS,CAAE;AACvB,GAAIkM,MAAM,GAAK,iBAAiB,CAAElM,KAAK,CAAG,SAAS,CAAE;AACrD,GAAIkM,MAAM,GAAK,kBAAkB,CAAElM,KAAK,CAAG,SAAS,CAAE;AAEtD,MAAO,CACL4C,GAAG,CAAE5H,KAAK,CACV+E,KAAK,CAAEA,KAAK,CACZC,KAAK,CAAEA,KACT,CAAC,CACH,CAAC,CACH,CAAC,EAAC,CAAC,CACH,8BAA8B,CAC7BD,KAAK,EAAKA,KAAK,CAACsC,QAAQ,CAAC,CAC5B,CAAC,uEAAAzJ,MAAA,CAGCwG,MAAM,CAACC,OAAO,CAACuM,iBAAiB,CAAC,CAACrM,GAAG,CAAC4M,MAAA,EAAsB,IAArB,CAACD,MAAM,CAAE3J,MAAM,CAAC,CAAA4J,MAAA,CACvD,GAAI,CAAAnM,KAAK,CAAG,SAAS,CAAE;AACvB,GAAIkM,MAAM,GAAK,iBAAiB,CAAElM,KAAK,CAAG,SAAS,CAAE;AACrD,GAAIkM,MAAM,GAAK,kBAAkB,CAAElM,KAAK,CAAG,SAAS,CAAE;AAEtD,6HAAApH,MAAA,CAE0DoH,KAAK,+DAAApH,MAAA,CAC9BsT,MAAM,eAAAtT,MAAA,CAAa2J,MAAM,CAACA,MAAM,CAAC/E,MAAM,CAAG,CAAC,CAAC,gDAG/E,CAAC,CAAC,CAACiC,IAAI,CAAC,EAAE,CAAC,6dAmBlB,CAED;AACAL,MAAM,CAACC,OAAO,CAACsM,kBAAkB,CAAC,CAAC/N,OAAO,CAACwO,MAAA,EAAqB,IAApB,CAACtO,OAAO,CAAE8F,IAAI,CAAC,CAAAwI,MAAA,CACzD;AACA,KAAM,CAAAC,GAAG,CAAG,GAAG,CAAG7K,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,GAAG,CAAE;AAEvC;AACA,KAAM,CAAA6K,iBAAiB,CAAG9K,IAAI,CAAC6I,KAAK,CAAE,EAAE,CAAGgC,GAAG,CAAKzI,IAAI,CAACiI,SAAS,CAAG,CAAE,CAAIjI,IAAI,CAACkI,aAAa,CAAG,EAAG,CAAC,CAEnG5M,IAAI,+BAAAtG,MAAA,CAEMkF,OAAO,wBAAAlF,MAAA,CACPgL,IAAI,CAAC/D,KAAK,wBAAAjH,MAAA,CACVgL,IAAI,CAACiI,SAAS,CAAC1M,OAAO,CAAC,CAAC,CAAC,yBAAAvG,MAAA,CACzBgL,IAAI,CAACkI,aAAa,CAAC3M,OAAO,CAAC,CAAC,CAAC,yBAAAvG,MAAA,CAC7ByT,GAAG,CAAClN,OAAO,CAAC,CAAC,CAAC,wBAAAvG,MAAA,CACd0T,iBAAiB,+BAE1B,CACH,CAAC,CAAC,CAEF;AACApN,IAAI,geAoBH,CAED;AACA,KAAM,CAAAqN,iBAAiB,CAAG9Q,OAAO,CAAC8D,GAAG,CAAC1B,MAAM,EAAI,CAC9C,KAAM,CAAAkO,UAAU,CAAG,CAAC,CAAGvK,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,CAAC,CAAE;AAC1C,KAAM,CAAAuK,cAAc,CAAG,EAAE,CAAGxK,IAAI,CAACC,MAAM,CAAC,CAAC,CAAG,EAAE,CAAE;AAChD,KAAM,CAAA6K,iBAAiB,CAAG9K,IAAI,CAAC6I,KAAK,CAAC0B,UAAU,CAAG,GAAG,CAAGC,cAAc,CAAG,EAAE,CAAC,CAE5E,OAAAQ,aAAA,CAAAA,aAAA,IACK3O,MAAM,MACTkO,UAAU,CACVC,cAAc,CACdM,iBAAiB,GAErB,CAAC,CAAC,CAEF;AACA,KAAM,CAAAG,aAAa,CAAG,CAAC,GAAGF,iBAAiB,CAAC,CACzCvJ,IAAI,CAAC,CAACR,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAAC6J,iBAAiB,CAAG9J,CAAC,CAAC8J,iBAAiB,CAAC,CACzDhN,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CAEfmN,aAAa,CAAC7O,OAAO,CAACC,MAAM,EAAI,CAC9B,KAAM,CAAA2K,WAAW,CAAG3K,MAAM,CAACuD,SAAS,CAClCI,IAAI,CAACiH,KAAK,CAAC,CAAC,GAAI,CAAApP,IAAI,CAAC,CAAC,CAAC4J,OAAO,CAAC,CAAC,CAAG,GAAI,CAAA5J,IAAI,CAACwE,MAAM,CAACuD,SAAS,CAAC,CAAC6B,OAAO,CAAC,CAAC,GAAK,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CAAC,CAAG,EAAE,CAE7G/D,IAAI,+BAAAtG,MAAA,CAEMiF,MAAM,CAACoD,SAAS,wBAAArI,MAAA,CAChBiF,MAAM,CAACiC,IAAI,wBAAAlH,MAAA,CACXiF,MAAM,CAACC,OAAO,EAAID,MAAM,CAAChF,IAAI,wBAAAD,MAAA,CAC7B4P,WAAW,+BAAA5P,MAAA,CACXiF,MAAM,CAACkO,UAAU,CAAC5M,OAAO,CAAC,CAAC,CAAC,yBAAAvG,MAAA,CAC5BiF,MAAM,CAACmO,cAAc,CAAC7M,OAAO,CAAC,CAAC,CAAC,yBAAAvG,MAAA,CAChCiF,MAAM,CAACyO,iBAAiB,+BAEjC,CACH,CAAC,CAAC,CAEF;AACApN,IAAI,sDAIH,CAED,MAAO,CAAAA,IAAI,CACb,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAwN,qBAAqB,CAAIjR,OAAiB,EAAa,CAC3D;AACA,MAAO,CAAAkF,mBAAmB,CAAClF,OAAO,CAAC,CACrC,CAAC,CAED;AACA;AACA,GACA,KAAM,CAAAiF,8BAA8B,CAAIjF,OAAc,EAAa,CACjE;AACA,KAAM,CAAA8B,YAAY,CAAG9B,OAAO,CAAC+B,MAAM,CACnC,KAAM,CAAAC,aAAqC,CAAG,CAAC,CAAC,CAChD,KAAM,CAAAC,YAAoC,CAAG,CAAC,CAAC,CAC/C,KAAM,CAAAC,YAAoC,CAAG,CAAC,CAAC,CAC/C,KAAM,CAAAgP,cAAsC,CAAG,CAAC,CAAC,CAEjD;AACAlR,OAAO,CAACmC,OAAO,CAACC,MAAM,EAAI,CACxB;AACA,GAAIA,MAAM,CAACC,OAAO,CAAE,CAClBL,aAAa,CAACI,MAAM,CAACC,OAAO,CAAC,CAAG,CAACL,aAAa,CAACI,MAAM,CAACC,OAAO,CAAC,EAAI,CAAC,EAAI,CAAC,CAC1E,CAEA;AACA,GAAID,MAAM,CAACE,MAAM,CAAE,CACjBL,YAAY,CAACG,MAAM,CAACE,MAAM,CAAC,CAAG,CAACL,YAAY,CAACG,MAAM,CAACE,MAAM,CAAC,EAAI,CAAC,EAAI,CAAC,CACtE,CAEA;AACA,GAAIF,MAAM,CAACG,YAAY,CAAE,CACvBL,YAAY,CAACE,MAAM,CAACG,YAAY,CAAC,CAAG,CAACL,YAAY,CAACE,MAAM,CAACG,YAAY,CAAC,EAAI,CAAC,EAAI,CAAC,CAClF,CAEA;AACA,GAAIH,MAAM,CAACwD,QAAQ,CAAE,CACnBsL,cAAc,CAAC9O,MAAM,CAACwD,QAAQ,CAAC,CAAG,CAACsL,cAAc,CAAC9O,MAAM,CAACwD,QAAQ,CAAC,EAAI,CAAC,EAAI,CAAC,CAC9E,CACF,CAAC,CAAC,CAEF;AACA,KAAM,CAAAuL,SAAiC,CAAG,CACxC,cAAc,CAAE,CAAC,CACjB,WAAW,CAAE,CAAC,CACd,WAAW,CAAE,CAAC,CACd,WAAW,CAAE,CAAC,CACd,cAAc,CAAE,CAClB,CAAC,CAEDnR,OAAO,CAACmC,OAAO,CAACC,MAAM,EAAI,CACxB,GAAIA,MAAM,CAACuD,SAAS,CAAE,CACpB,KAAM,CAAAoH,WAAW,CAAGhH,IAAI,CAACiH,KAAK,CAAC,CAAC,GAAI,CAAApP,IAAI,CAAC,CAAC,CAAC4J,OAAO,CAAC,CAAC,CAAG,GAAI,CAAA5J,IAAI,CAACwE,MAAM,CAACuD,SAAS,CAAC,CAAC6B,OAAO,CAAC,CAAC,GAAK,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CAAC,CAE1H,GAAIuF,WAAW,CAAG,EAAE,CAAE,CACpBoE,SAAS,CAAC,cAAc,CAAC,EAAE,CAC7B,CAAC,IAAM,IAAIpE,WAAW,CAAG,EAAE,CAAE,CAC3BoE,SAAS,CAAC,WAAW,CAAC,EAAE,CAC1B,CAAC,IAAM,IAAIpE,WAAW,CAAG,EAAE,CAAE,CAC3BoE,SAAS,CAAC,WAAW,CAAC,EAAE,CAC1B,CAAC,IAAM,IAAIpE,WAAW,CAAG,EAAE,CAAE,CAC3BoE,SAAS,CAAC,WAAW,CAAC,EAAE,CAC1B,CAAC,IAAM,CACLA,SAAS,CAAC,cAAc,CAAC,EAAE,CAC7B,CACF,CACF,CAAC,CAAC,CAEF;AACA,GAAI,CAAA1N,IAAI,mRAAAtG,MAAA,CAM0B2E,YAAY,6JAAA3E,MAAA,CAIZwG,MAAM,CAACyL,IAAI,CAACpN,aAAa,CAAC,CAACD,MAAM,+JAAA5E,MAAA,CAIjC+E,YAAY,CAAC,SAAS,CAAC,EAAI,CAAC,OAAA/E,MAAA,CAAK,CAAC,CAAC+E,YAAY,CAAC,SAAS,CAAC,EAAI,CAAC,EAAIJ,YAAY,CAAG,GAAG,EAAE4B,OAAO,CAAC,CAAC,CAAC,gKAAAvG,MAAA,CAIjG8E,YAAY,CAAC,QAAQ,CAAC,EAAI,CAAC,OAAA9E,MAAA,CAAK,CAAC,CAAC8E,YAAY,CAAC,QAAQ,CAAC,EAAI,CAAC,EAAIH,YAAY,CAAG,GAAG,EAAE4B,OAAO,CAAC,CAAC,CAAC,uXAAAvG,MAAA,CAUvH8G,mBAAmB,CACnBN,MAAM,CAACC,OAAO,CAAC5B,aAAa,CAAC,CAAC8B,GAAG,CAAC,CAAAsN,MAAA,CAAmBjN,KAAK,OAAvB,CAAC9B,OAAO,CAAE+B,KAAK,CAAC,CAAAgN,MAAA,OAAa,CAC9D/M,IAAI,CAAEhC,OAAO,CACbiC,KAAK,CAAEF,KAAK,CACZG,KAAK,QAAApH,MAAA,CAASgH,KAAK,CAAG,EAAE,eAC1B,CAAC,EAAC,CAAC,CACH,sBACF,CAAC,uEAAAhH,MAAA,CAGCwG,MAAM,CAACC,OAAO,CAAC5B,aAAa,CAAC,CAAC8B,GAAG,CAAC,CAAAuN,MAAA,CAAmBlN,KAAK,OAAvB,CAAC9B,OAAO,CAAE+B,KAAK,CAAC,CAAAiN,MAAA,8HAAAlU,MAAA,CAESgH,KAAK,CAAG,EAAE,wEAAAhH,MAAA,CACvCkF,OAAO,OAAAlF,MAAA,CAAKiH,KAAK,OAAAjH,MAAA,CAAK,CAACiH,KAAK,CAAGtC,YAAY,CAAG,GAAG,EAAE4B,OAAO,CAAC,CAAC,CAAC,+CAE7F,CAAC,CAACM,IAAI,CAAC,EAAE,CAAC,2XAAA7G,MAAA,CAUT8G,mBAAmB,CACnBN,MAAM,CAACC,OAAO,CAAC1B,YAAY,CAAC,CAAC4B,GAAG,CAACwN,MAAA,MAAC,CAAChP,MAAM,CAAE8B,KAAK,CAAC,CAAAkN,MAAA,OAAM,CACrDjN,IAAI,CAAE/B,MAAM,CACZgC,KAAK,CAAEF,KAAK,CACZG,KAAK,CAAEG,oBAAoB,CAACpC,MAAM,CACpC,CAAC,EAAC,CAAC,CACH,4BACF,CAAC,uEAAAnF,MAAA,CAGCwG,MAAM,CAACC,OAAO,CAAC1B,YAAY,CAAC,CAAC4B,GAAG,CAACyN,MAAA,MAAC,CAACjP,MAAM,CAAE8B,KAAK,CAAC,CAAAmN,MAAA,0HAAApU,MAAA,CAEOuH,oBAAoB,CAACpC,MAAM,CAAC,6DAAAnF,MAAA,CACrDmF,MAAM,OAAAnF,MAAA,CAAKiH,KAAK,OAAAjH,MAAA,CAAK,CAACiH,KAAK,CAAGtC,YAAY,CAAG,GAAG,EAAE4B,OAAO,CAAC,CAAC,CAAC,+CAE5F,CAAC,CAACM,IAAI,CAAC,EAAE,CAAC,uWAAA7G,MAAA,CAUTuJ,mBAAmB,CACnB/C,MAAM,CAACC,OAAO,CAACuN,SAAS,CAAC,CAACrN,GAAG,CAAC,CAAA0N,MAAA,CAAiBrN,KAAK,OAArB,CAACsN,KAAK,CAAErN,KAAK,CAAC,CAAAoN,MAAA,OAAa,CACxDnN,IAAI,CAAEoN,KAAK,CACXnN,KAAK,CAAEF,KAAK,CACZG,KAAK,QAAApH,MAAA,CAAS,GAAG,CAAGgH,KAAK,CAAG,EAAE,eAChC,CAAC,EAAC,CAAC,CACH,kBAAkB,CACjBG,KAAK,EAAKA,KAAK,CAACsC,QAAQ,CAAC,CAC5B,CAAC,uEAAAzJ,MAAA,CAGCwG,MAAM,CAACC,OAAO,CAACuN,SAAS,CAAC,CAACrN,GAAG,CAAC,CAAA4N,MAAA,CAAiBvN,KAAK,OAArB,CAACsN,KAAK,CAAErN,KAAK,CAAC,CAAAsN,MAAA,8HAAAvU,MAAA,CAEe,GAAG,CAAGgH,KAAK,CAAG,EAAE,wEAAAhH,MAAA,CAC7CsU,KAAK,OAAAtU,MAAA,CAAKiH,KAAK,OAAAjH,MAAA,CAAK,CAACiH,KAAK,CAAGtC,YAAY,CAAG,GAAG,EAAE4B,OAAO,CAAC,CAAC,CAAC,+CAE3F,CAAC,CAACM,IAAI,CAAC,EAAE,CAAC,qYAiBlB,CAED;AACAL,MAAM,CAACC,OAAO,CAACsN,cAAc,CAAC,CAAC/O,OAAO,CAACwP,MAAA,EAAuB,IAAtB,CAAC/L,QAAQ,CAAExB,KAAK,CAAC,CAAAuN,MAAA,CACvD;AACA,KAAM,CAAAC,gBAAwC,CAAG,CAAC,CAAC,CAEnD5R,OAAO,CAACoD,MAAM,CAAChB,MAAM,EAAIA,MAAM,CAACwD,QAAQ,GAAKA,QAAQ,CAAC,CAACzD,OAAO,CAACC,MAAM,EAAI,CACvE,GAAIA,MAAM,CAACC,OAAO,CAAE,CAClBuP,gBAAgB,CAACxP,MAAM,CAACC,OAAO,CAAC,CAAG,CAACuP,gBAAgB,CAACxP,MAAM,CAACC,OAAO,CAAC,EAAI,CAAC,EAAI,CAAC,CAChF,CACF,CAAC,CAAC,CAEF,KAAM,CAAAwP,mBAAmB,CAAGlO,MAAM,CAACC,OAAO,CAACgO,gBAAgB,CAAC,CACzD9N,GAAG,CAACgO,MAAA,MAAC,CAACzP,OAAO,CAAE0P,YAAY,CAAC,CAAAD,MAAA,UAAA3U,MAAA,CAAQkF,OAAO,OAAAlF,MAAA,CAAK4U,YAAY,GAAE,CAAC,CAC/D/N,IAAI,CAAC,IAAI,CAAC,CAEbP,IAAI,+BAAAtG,MAAA,CAEMyI,QAAQ,wBAAAzI,MAAA,CACRiH,KAAK,wBAAAjH,MAAA,CACL,CAACiH,KAAK,CAAGtC,YAAY,CAAG,GAAG,EAAE4B,OAAO,CAAC,CAAC,CAAC,yBAAAvG,MAAA,CACvC0U,mBAAmB,4BAE5B,CACH,CAAC,CAAC,CAEF;AACApO,IAAI,kdAmBH,CAED;AACAE,MAAM,CAACC,OAAO,CAAC5B,aAAa,CAAC,CAACG,OAAO,CAAC6P,MAAA,EAAsB,KAAAC,qBAAA,IAArB,CAAC5P,OAAO,CAAE+B,KAAK,CAAC,CAAA4N,MAAA,CACrD,KAAM,CAAAE,cAAc,CAAGlS,OAAO,CAACoD,MAAM,CAAChB,MAAM,EAAIA,MAAM,CAACC,OAAO,GAAKA,OAAO,CAAC,CAE3E;AACA,KAAM,CAAA8P,gBAAgB,CAAGD,cAAc,CAACrP,MAAM,CAAC,CAACC,GAAG,CAAEV,MAAM,GAAK,CAC9D,GAAIA,MAAM,CAACuD,SAAS,CAAE,CACpB,MAAO,CAAA7C,GAAG,CAAGiD,IAAI,CAACiH,KAAK,CAAC,CAAC,GAAI,CAAApP,IAAI,CAAC,CAAC,CAAC4J,OAAO,CAAC,CAAC,CAAG,GAAI,CAAA5J,IAAI,CAACwE,MAAM,CAACuD,SAAS,CAAC,CAAC6B,OAAO,CAAC,CAAC,GAAK,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CAAC,CACrH,CACA,MAAO,CAAA1E,GAAG,CAAG,EAAE,CAAE;AACnB,CAAC,CAAE,CAAC,CAAC,CAEL,KAAM,CAAAsP,MAAM,CAAGD,gBAAgB,CAAG/N,KAAK,CAEvC;AACA,KAAM,CAAAiO,YAAY,CAAGH,cAAc,CAAC9O,MAAM,CAAChB,MAAM,EAAIA,MAAM,CAACG,YAAY,GAAK,SAAS,CAAC,CAACR,MAAM,CAC9F,KAAM,CAAAuQ,UAAU,CAAID,YAAY,CAAGjO,KAAK,CAAI,GAAG,CAE/C;AACA,KAAM,CAAAmO,WAAW,CAAGL,cAAc,CAAC9O,MAAM,CAAChB,MAAM,EAAIA,MAAM,CAACE,MAAM,GAAK,QAAQ,CAAC,CAACP,MAAM,CACtF,KAAM,CAAAyQ,UAAU,CAAID,WAAW,CAAGnO,KAAK,CAAI,GAAG,CAE9C;AACA,KAAM,CAAA8M,cAAsC,CAAG,CAAC,CAAC,CACjDgB,cAAc,CAAC/P,OAAO,CAACC,MAAM,EAAI,CAC/B,GAAIA,MAAM,CAACwD,QAAQ,CAAE,CACnBsL,cAAc,CAAC9O,MAAM,CAACwD,QAAQ,CAAC,CAAG,CAACsL,cAAc,CAAC9O,MAAM,CAACwD,QAAQ,CAAC,EAAI,CAAC,EAAI,CAAC,CAC9E,CACF,CAAC,CAAC,CAEF,KAAM,CAAA6M,eAAe,CAAG,EAAAR,qBAAA,CAAAtO,MAAM,CAACC,OAAO,CAACsN,cAAc,CAAC,CAAC3J,IAAI,CAAC,CAACR,CAAC,CAAEC,CAAC,GAAKA,CAAC,CAAC,CAAC,CAAC,CAAGD,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,UAAAkL,qBAAA,iBAA7DA,qBAAA,CAAgE,CAAC,CAAC,GAAI,SAAS,CAEvGxO,IAAI,+BAAAtG,MAAA,CAEMkF,OAAO,wBAAAlF,MAAA,CACPiH,KAAK,wBAAAjH,MAAA,CACLiV,MAAM,CAAC1O,OAAO,CAAC,CAAC,CAAC,wBAAAvG,MAAA,CACjBmV,UAAU,CAAC5O,OAAO,CAAC,CAAC,CAAC,yBAAAvG,MAAA,CACrBqV,UAAU,CAAC9O,OAAO,CAAC,CAAC,CAAC,yBAAAvG,MAAA,CACrBsV,eAAe,4BAExB,CACH,CAAC,CAAC,CAEF;AACAhP,IAAI,sDAIH,CAED,MAAO,CAAAA,IAAI,CACb,CAAC,CAED;AAEA;AAEA,MAAO,MAAM,CAAAiP,eAAe,CAAG,KAAO,CAAAtV,IAAgB,EAAqB,CACzE,GAAI,CACF;AACA;AACA,KAAM,IAAI,CAAAE,OAAO,CAACC,OAAO,EAAIC,UAAU,CAACD,OAAO,CAAE,GAAG,CAAC,CAAC,CAEtD;AACA,KAAM,CAAAoV,WAAW,CAAG,CAClB,CACEjL,EAAE,CAAE,GAAG,CACPrD,IAAI,IAAAlH,MAAA,CAAKC,IAAI,CAACwV,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGzV,IAAI,CAACyG,KAAK,CAAC,CAAC,CAAC,qBAAmB,CACxEiP,SAAS,CAAE,GAAI,CAAAlV,IAAI,CAAC,CAAC,CAAC+O,WAAW,CAAC,CAAC,CACnC5P,MAAM,CAAE,KAAK,CACbgW,IAAI,CAAE,QACR,CAAC,CACD,CACErL,EAAE,CAAE,GAAG,CACPrD,IAAI,IAAAlH,MAAA,CAAKC,IAAI,CAACwV,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGzV,IAAI,CAACyG,KAAK,CAAC,CAAC,CAAC,uBAAqB,CAC1EiP,SAAS,CAAE,GAAI,CAAAlV,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,CAAC,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CAAC8O,WAAW,CAAC,CAAC,CACvE5P,MAAM,CAAE,OAAO,CACfgW,IAAI,CAAE,QACR,CAAC,CACD,CACErL,EAAE,CAAE,GAAG,CACPrD,IAAI,IAAAlH,MAAA,CAAKC,IAAI,CAACwV,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGzV,IAAI,CAACyG,KAAK,CAAC,CAAC,CAAC,oBAAkB,CACvEiP,SAAS,CAAE,GAAI,CAAAlV,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CAAC8O,WAAW,CAAC,CAAC,CACxE5P,MAAM,CAAE,KAAK,CACbgW,IAAI,CAAE,QACR,CAAC,CACD,CACErL,EAAE,CAAE,GAAG,CACPrD,IAAI,WAAAlH,MAAA,CAAYC,IAAI,CAACwV,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAGzV,IAAI,CAACyG,KAAK,CAAC,CAAC,CAAC,aAAW,CACvEiP,SAAS,CAAE,GAAI,CAAAlV,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,CAAG,CAAC,CAAG,EAAE,CAAG,EAAE,CAAG,EAAE,CAAG,IAAI,CAAC,CAAC8O,WAAW,CAAC,CAAC,CACvE5P,MAAM,CAAE,KAAK,CACbgW,IAAI,CAAE,QACR,CAAC,CACF,CAED,MAAO,CAAAJ,WAAW,CACpB,CAAE,MAAO7U,KAAK,CAAE,CACdC,OAAO,CAACD,KAAK,yBAAAX,MAAA,CAAyBC,IAAI,cAAaU,KAAK,CAAC,CAC7D,KAAM,CAAAA,KAAK,CACb,CACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}