{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.AuthProvider = exports.AuthContext = void 0;\nconst error_1 = require(\"../../error\");\n/**\n * Context used during authentication\n * @internal\n */\nclass AuthContext {\n  constructor(connection, credentials, options) {\n    /** If the context is for reauthentication. */\n    this.reauthenticating = false;\n    this.connection = connection;\n    this.credentials = credentials;\n    this.options = options;\n  }\n}\nexports.AuthContext = AuthContext;\n/**\n * Provider used during authentication.\n * @internal\n */\nclass AuthProvider {\n  /**\n   * Prepare the handshake document before the initial handshake.\n   *\n   * @param handshakeDoc - The document used for the initial handshake on a connection\n   * @param authContext - Context for authentication flow\n   */\n  async prepare(handshakeDoc, _authContext) {\n    return handshakeDoc;\n  }\n  /**\n   * Reauthenticate.\n   * @param context - The shared auth context.\n   */\n  async reauth(context) {\n    if (context.reauthenticating) {\n      throw new error_1.MongoRuntimeError('Reauthentication already in progress.');\n    }\n    try {\n      context.reauthenticating = true;\n      await this.auth(context);\n    } finally {\n      context.reauthenticating = false;\n    }\n  }\n}\nexports.AuthProvider = AuthProvider;", "map": {"version": 3, "names": ["error_1", "require", "AuthContext", "constructor", "connection", "credentials", "options", "reauthenticating", "exports", "<PERSON>th<PERSON><PERSON><PERSON>", "prepare", "handshakeDoc", "_authContext", "reauth", "context", "MongoRuntimeError", "auth"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\auth\\auth_provider.ts"], "sourcesContent": ["import type { Document } from '../../bson';\nimport { MongoRuntimeError } from '../../error';\nimport type { HandshakeDocument } from '../connect';\nimport type { Connection, ConnectionOptions } from '../connection';\nimport type { MongoCredentials } from './mongo_credentials';\n\n/**\n * Context used during authentication\n * @internal\n */\nexport class AuthContext {\n  /** The connection to authenticate */\n  connection: Connection;\n  /** The credentials to use for authentication */\n  credentials?: MongoCredentials;\n  /** If the context is for reauthentication. */\n  reauthenticating = false;\n  /** The options passed to the `connect` method */\n  options: ConnectionOptions;\n\n  /** A response from an initial auth attempt, only some mechanisms use this (e.g, SCRAM) */\n  response?: Document;\n  /** A random nonce generated for use in an authentication conversation */\n  nonce?: Buffer;\n\n  constructor(\n    connection: Connection,\n    credentials: MongoCredentials | undefined,\n    options: ConnectionOptions\n  ) {\n    this.connection = connection;\n    this.credentials = credentials;\n    this.options = options;\n  }\n}\n\n/**\n * Provider used during authentication.\n * @internal\n */\nexport abstract class AuthProvider {\n  /**\n   * Prepare the handshake document before the initial handshake.\n   *\n   * @param handshakeDoc - The document used for the initial handshake on a connection\n   * @param authContext - Context for authentication flow\n   */\n  async prepare(\n    handshakeDoc: HandshakeDocument,\n    _authContext: AuthContext\n  ): Promise<HandshakeDocument> {\n    return handshakeDoc;\n  }\n\n  /**\n   * Authenticate\n   *\n   * @param context - A shared context for authentication flow\n   */\n  abstract auth(context: AuthContext): Promise<void>;\n\n  /**\n   * Reauthenticate.\n   * @param context - The shared auth context.\n   */\n  async reauth(context: AuthContext): Promise<void> {\n    if (context.reauthenticating) {\n      throw new MongoRuntimeError('Reauthentication already in progress.');\n    }\n    try {\n      context.reauthenticating = true;\n      await this.auth(context);\n    } finally {\n      context.reauthenticating = false;\n    }\n  }\n}\n"], "mappings": ";;;;;;AACA,MAAAA,OAAA,GAAAC,OAAA;AAKA;;;;AAIA,MAAaC,WAAW;EAetBC,YACEC,UAAsB,EACtBC,WAAyC,EACzCC,OAA0B;IAb5B;IACA,KAAAC,gBAAgB,GAAG,KAAK;IActB,IAAI,CAACH,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,OAAO,GAAGA,OAAO;EACxB;;AAvBFE,OAAA,CAAAN,WAAA,GAAAA,WAAA;AA0BA;;;;AAIA,MAAsBO,YAAY;EAChC;;;;;;EAMA,MAAMC,OAAOA,CACXC,YAA+B,EAC/BC,YAAyB;IAEzB,OAAOD,YAAY;EACrB;EASA;;;;EAIA,MAAME,MAAMA,CAACC,OAAoB;IAC/B,IAAIA,OAAO,CAACP,gBAAgB,EAAE;MAC5B,MAAM,IAAIP,OAAA,CAAAe,iBAAiB,CAAC,uCAAuC,CAAC;IACtE;IACA,IAAI;MACFD,OAAO,CAACP,gBAAgB,GAAG,IAAI;MAC/B,MAAM,IAAI,CAACS,IAAI,CAACF,OAAO,CAAC;IAC1B,CAAC,SAAS;MACRA,OAAO,CAACP,gBAAgB,GAAG,KAAK;IAClC;EACF;;AAnCFC,OAAA,CAAAC,YAAA,GAAAA,YAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}