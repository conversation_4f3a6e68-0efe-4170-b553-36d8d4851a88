{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.formatSort = formatSort;\nconst error_1 = require(\"./error\");\n/** @internal */\nfunction prepareDirection(direction = 1) {\n  const value = `${direction}`.toLowerCase();\n  if (isMeta(direction)) return direction;\n  switch (value) {\n    case 'ascending':\n    case 'asc':\n    case '1':\n      return 1;\n    case 'descending':\n    case 'desc':\n    case '-1':\n      return -1;\n    default:\n      throw new error_1.MongoInvalidArgumentError(`Invalid sort direction: ${JSON.stringify(direction)}`);\n  }\n}\n/** @internal */\nfunction isMeta(t) {\n  return typeof t === 'object' && t != null && '$meta' in t && typeof t.$meta === 'string';\n}\n/** @internal */\nfunction isPair(t) {\n  if (Array.isArray(t) && t.length === 2) {\n    try {\n      prepareDirection(t[1]);\n      return true;\n    } catch {\n      return false;\n    }\n  }\n  return false;\n}\nfunction isDeep(t) {\n  return Array.isArray(t) && Array.isArray(t[0]);\n}\nfunction isMap(t) {\n  return t instanceof Map && t.size > 0;\n}\n/** @internal */\nfunction pairToMap(v) {\n  return new Map([[`${v[0]}`, prepareDirection([v[1]])]]);\n}\n/** @internal */\nfunction deepToMap(t) {\n  const sortEntries = t.map(([k, v]) => [`${k}`, prepareDirection(v)]);\n  return new Map(sortEntries);\n}\n/** @internal */\nfunction stringsToMap(t) {\n  const sortEntries = t.map(key => [`${key}`, 1]);\n  return new Map(sortEntries);\n}\n/** @internal */\nfunction objectToMap(t) {\n  const sortEntries = Object.entries(t).map(([k, v]) => [`${k}`, prepareDirection(v)]);\n  return new Map(sortEntries);\n}\n/** @internal */\nfunction mapToMap(t) {\n  const sortEntries = Array.from(t).map(([k, v]) => [`${k}`, prepareDirection(v)]);\n  return new Map(sortEntries);\n}\n/** converts a Sort type into a type that is valid for the server (SortForCmd) */\nfunction formatSort(sort, direction) {\n  if (sort == null) return undefined;\n  if (typeof sort === 'string') return new Map([[sort, prepareDirection(direction)]]);\n  if (typeof sort !== 'object') {\n    throw new error_1.MongoInvalidArgumentError(`Invalid sort format: ${JSON.stringify(sort)} Sort must be a valid object`);\n  }\n  if (!Array.isArray(sort)) {\n    return isMap(sort) ? mapToMap(sort) : Object.keys(sort).length ? objectToMap(sort) : undefined;\n  }\n  if (!sort.length) return undefined;\n  if (isDeep(sort)) return deepToMap(sort);\n  if (isPair(sort)) return pairToMap(sort);\n  return stringsToMap(sort);\n}", "map": {"version": 3, "names": ["exports", "formatSort", "error_1", "require", "prepareDirection", "direction", "value", "toLowerCase", "isMeta", "MongoInvalidArgumentError", "JSON", "stringify", "t", "$meta", "isPair", "Array", "isArray", "length", "isDeep", "isMap", "Map", "size", "pairToMap", "v", "deepToMap", "sortEntries", "map", "k", "stringsToMap", "key", "objectToMap", "Object", "entries", "mapToMap", "from", "sort", "undefined", "keys"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\sort.ts"], "sourcesContent": ["import { MongoInvalidArgumentError } from './error';\n\n/** @public */\nexport type SortDirection =\n  | 1\n  | -1\n  | 'asc'\n  | 'desc'\n  | 'ascending'\n  | 'descending'\n  | { $meta: string };\n\n/** @public */\nexport type Sort =\n  | string\n  | Exclude<SortDirection, { $meta: string }>\n  | string[]\n  | { [key: string]: SortDirection }\n  | Map<string, SortDirection>\n  | [string, SortDirection][]\n  | [string, SortDirection];\n\n/** Below stricter types were created for sort that correspond with type that the cmd takes  */\n\n/** @internal */\nexport type SortDirectionForCmd = 1 | -1 | { $meta: string };\n\n/** @internal */\nexport type SortForCmd = Map<string, SortDirectionForCmd>;\n\n/** @internal */\ntype SortPairForCmd = [string, SortDirectionForCmd];\n\n/** @internal */\nfunction prepareDirection(direction: any = 1): SortDirectionForCmd {\n  const value = `${direction}`.toLowerCase();\n  if (isMeta(direction)) return direction;\n  switch (value) {\n    case 'ascending':\n    case 'asc':\n    case '1':\n      return 1;\n    case 'descending':\n    case 'desc':\n    case '-1':\n      return -1;\n    default:\n      throw new MongoInvalidArgumentError(`Invalid sort direction: ${JSON.stringify(direction)}`);\n  }\n}\n\n/** @internal */\nfunction isMeta(t: SortDirection): t is { $meta: string } {\n  return typeof t === 'object' && t != null && '$meta' in t && typeof t.$meta === 'string';\n}\n\n/** @internal */\nfunction isPair(t: Sort): t is [string, SortDirection] {\n  if (Array.isArray(t) && t.length === 2) {\n    try {\n      prepareDirection(t[1]);\n      return true;\n    } catch {\n      return false;\n    }\n  }\n  return false;\n}\n\nfunction isDeep(t: Sort): t is [string, SortDirection][] {\n  return Array.isArray(t) && Array.isArray(t[0]);\n}\n\nfunction isMap(t: Sort): t is Map<string, SortDirection> {\n  return t instanceof Map && t.size > 0;\n}\n\n/** @internal */\nfunction pairToMap(v: [string, SortDirection]): SortForCmd {\n  return new Map([[`${v[0]}`, prepareDirection([v[1]])]]);\n}\n\n/** @internal */\nfunction deepToMap(t: [string, SortDirection][]): SortForCmd {\n  const sortEntries: SortPairForCmd[] = t.map(([k, v]) => [`${k}`, prepareDirection(v)]);\n  return new Map(sortEntries);\n}\n\n/** @internal */\nfunction stringsToMap(t: string[]): SortForCmd {\n  const sortEntries: SortPairForCmd[] = t.map(key => [`${key}`, 1]);\n  return new Map(sortEntries);\n}\n\n/** @internal */\nfunction objectToMap(t: { [key: string]: SortDirection }): SortForCmd {\n  const sortEntries: SortPairForCmd[] = Object.entries(t).map(([k, v]) => [\n    `${k}`,\n    prepareDirection(v)\n  ]);\n  return new Map(sortEntries);\n}\n\n/** @internal */\nfunction mapToMap(t: Map<string, SortDirection>): SortForCmd {\n  const sortEntries: SortPairForCmd[] = Array.from(t).map(([k, v]) => [\n    `${k}`,\n    prepareDirection(v)\n  ]);\n  return new Map(sortEntries);\n}\n\n/** converts a Sort type into a type that is valid for the server (SortForCmd) */\nexport function formatSort(\n  sort: Sort | undefined,\n  direction?: SortDirection\n): SortForCmd | undefined {\n  if (sort == null) return undefined;\n  if (typeof sort === 'string') return new Map([[sort, prepareDirection(direction)]]);\n  if (typeof sort !== 'object') {\n    throw new MongoInvalidArgumentError(\n      `Invalid sort format: ${JSON.stringify(sort)} Sort must be a valid object`\n    );\n  }\n  if (!Array.isArray(sort)) {\n    return isMap(sort) ? mapToMap(sort) : Object.keys(sort).length ? objectToMap(sort) : undefined;\n  }\n  if (!sort.length) return undefined;\n  if (isDeep(sort)) return deepToMap(sort);\n  if (isPair(sort)) return pairToMap(sort);\n  return stringsToMap(sort);\n}\n"], "mappings": ";;;;;AAiHAA,OAAA,CAAAC,UAAA,GAAAA,UAAA;AAjHA,MAAAC,OAAA,GAAAC,OAAA;AAiCA;AACA,SAASC,gBAAgBA,CAACC,SAAA,GAAiB,CAAC;EAC1C,MAAMC,KAAK,GAAG,GAAGD,SAAS,EAAE,CAACE,WAAW,EAAE;EAC1C,IAAIC,MAAM,CAACH,SAAS,CAAC,EAAE,OAAOA,SAAS;EACvC,QAAQC,KAAK;IACX,KAAK,WAAW;IAChB,KAAK,KAAK;IACV,KAAK,GAAG;MACN,OAAO,CAAC;IACV,KAAK,YAAY;IACjB,KAAK,MAAM;IACX,KAAK,IAAI;MACP,OAAO,CAAC,CAAC;IACX;MACE,MAAM,IAAIJ,OAAA,CAAAO,yBAAyB,CAAC,2BAA2BC,IAAI,CAACC,SAAS,CAACN,SAAS,CAAC,EAAE,CAAC;EAC/F;AACF;AAEA;AACA,SAASG,MAAMA,CAACI,CAAgB;EAC9B,OAAO,OAAOA,CAAC,KAAK,QAAQ,IAAIA,CAAC,IAAI,IAAI,IAAI,OAAO,IAAIA,CAAC,IAAI,OAAOA,CAAC,CAACC,KAAK,KAAK,QAAQ;AAC1F;AAEA;AACA,SAASC,MAAMA,CAACF,CAAO;EACrB,IAAIG,KAAK,CAACC,OAAO,CAACJ,CAAC,CAAC,IAAIA,CAAC,CAACK,MAAM,KAAK,CAAC,EAAE;IACtC,IAAI;MACFb,gBAAgB,CAACQ,CAAC,CAAC,CAAC,CAAC,CAAC;MACtB,OAAO,IAAI;IACb,CAAC,CAAC,MAAM;MACN,OAAO,KAAK;IACd;EACF;EACA,OAAO,KAAK;AACd;AAEA,SAASM,MAAMA,CAACN,CAAO;EACrB,OAAOG,KAAK,CAACC,OAAO,CAACJ,CAAC,CAAC,IAAIG,KAAK,CAACC,OAAO,CAACJ,CAAC,CAAC,CAAC,CAAC,CAAC;AAChD;AAEA,SAASO,KAAKA,CAACP,CAAO;EACpB,OAAOA,CAAC,YAAYQ,GAAG,IAAIR,CAAC,CAACS,IAAI,GAAG,CAAC;AACvC;AAEA;AACA,SAASC,SAASA,CAACC,CAA0B;EAC3C,OAAO,IAAIH,GAAG,CAAC,CAAC,CAAC,GAAGG,CAAC,CAAC,CAAC,CAAC,EAAE,EAAEnB,gBAAgB,CAAC,CAACmB,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACzD;AAEA;AACA,SAASC,SAASA,CAACZ,CAA4B;EAC7C,MAAMa,WAAW,GAAqBb,CAAC,CAACc,GAAG,CAAC,CAAC,CAACC,CAAC,EAAEJ,CAAC,CAAC,KAAK,CAAC,GAAGI,CAAC,EAAE,EAAEvB,gBAAgB,CAACmB,CAAC,CAAC,CAAC,CAAC;EACtF,OAAO,IAAIH,GAAG,CAACK,WAAW,CAAC;AAC7B;AAEA;AACA,SAASG,YAAYA,CAAChB,CAAW;EAC/B,MAAMa,WAAW,GAAqBb,CAAC,CAACc,GAAG,CAACG,GAAG,IAAI,CAAC,GAAGA,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC;EACjE,OAAO,IAAIT,GAAG,CAACK,WAAW,CAAC;AAC7B;AAEA;AACA,SAASK,WAAWA,CAAClB,CAAmC;EACtD,MAAMa,WAAW,GAAqBM,MAAM,CAACC,OAAO,CAACpB,CAAC,CAAC,CAACc,GAAG,CAAC,CAAC,CAACC,CAAC,EAAEJ,CAAC,CAAC,KAAK,CACtE,GAAGI,CAAC,EAAE,EACNvB,gBAAgB,CAACmB,CAAC,CAAC,CACpB,CAAC;EACF,OAAO,IAAIH,GAAG,CAACK,WAAW,CAAC;AAC7B;AAEA;AACA,SAASQ,QAAQA,CAACrB,CAA6B;EAC7C,MAAMa,WAAW,GAAqBV,KAAK,CAACmB,IAAI,CAACtB,CAAC,CAAC,CAACc,GAAG,CAAC,CAAC,CAACC,CAAC,EAAEJ,CAAC,CAAC,KAAK,CAClE,GAAGI,CAAC,EAAE,EACNvB,gBAAgB,CAACmB,CAAC,CAAC,CACpB,CAAC;EACF,OAAO,IAAIH,GAAG,CAACK,WAAW,CAAC;AAC7B;AAEA;AACA,SAAgBxB,UAAUA,CACxBkC,IAAsB,EACtB9B,SAAyB;EAEzB,IAAI8B,IAAI,IAAI,IAAI,EAAE,OAAOC,SAAS;EAClC,IAAI,OAAOD,IAAI,KAAK,QAAQ,EAAE,OAAO,IAAIf,GAAG,CAAC,CAAC,CAACe,IAAI,EAAE/B,gBAAgB,CAACC,SAAS,CAAC,CAAC,CAAC,CAAC;EACnF,IAAI,OAAO8B,IAAI,KAAK,QAAQ,EAAE;IAC5B,MAAM,IAAIjC,OAAA,CAAAO,yBAAyB,CACjC,wBAAwBC,IAAI,CAACC,SAAS,CAACwB,IAAI,CAAC,8BAA8B,CAC3E;EACH;EACA,IAAI,CAACpB,KAAK,CAACC,OAAO,CAACmB,IAAI,CAAC,EAAE;IACxB,OAAOhB,KAAK,CAACgB,IAAI,CAAC,GAAGF,QAAQ,CAACE,IAAI,CAAC,GAAGJ,MAAM,CAACM,IAAI,CAACF,IAAI,CAAC,CAAClB,MAAM,GAAGa,WAAW,CAACK,IAAI,CAAC,GAAGC,SAAS;EAChG;EACA,IAAI,CAACD,IAAI,CAAClB,MAAM,EAAE,OAAOmB,SAAS;EAClC,IAAIlB,MAAM,CAACiB,IAAI,CAAC,EAAE,OAAOX,SAAS,CAACW,IAAI,CAAC;EACxC,IAAIrB,MAAM,CAACqB,IAAI,CAAC,EAAE,OAAOb,SAAS,CAACa,IAAI,CAAC;EACxC,OAAOP,YAAY,CAACO,IAAI,CAAC;AAC3B", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}