{"ast": null, "code": "'use client';\n\nimport * as React from 'react';\nconst usePreviousProps = value => {\n  const ref = React.useRef({});\n  React.useEffect(() => {\n    ref.current = value;\n  });\n  return ref.current;\n};\nexport default usePreviousProps;", "map": {"version": 3, "names": ["React", "usePreviousProps", "value", "ref", "useRef", "useEffect", "current"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@mui/utils/esm/usePreviousProps/usePreviousProps.js"], "sourcesContent": ["'use client';\n\nimport * as React from 'react';\nconst usePreviousProps = value => {\n  const ref = React.useRef({});\n  React.useEffect(() => {\n    ref.current = value;\n  });\n  return ref.current;\n};\nexport default usePreviousProps;"], "mappings": "AAAA,YAAY;;AAEZ,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,MAAMC,gBAAgB,GAAGC,KAAK,IAAI;EAChC,MAAMC,GAAG,GAAGH,KAAK,CAACI,MAAM,CAAC,CAAC,CAAC,CAAC;EAC5BJ,KAAK,CAACK,SAAS,CAAC,MAAM;IACpBF,GAAG,CAACG,OAAO,GAAGJ,KAAK;EACrB,CAAC,CAAC;EACF,OAAOC,GAAG,CAACG,OAAO;AACpB,CAAC;AACD,eAAeL,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}