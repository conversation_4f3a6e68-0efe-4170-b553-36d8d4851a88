{
  service: 'ampd-livestock-api',
  errorLabelSet: Set(2) { 'HandshakeError', 'ResetPool' },
  errorResponse: {
    ok: 0,
    errmsg: 'bad auth : authentication failed',
    code: 8000,
    codeName: 'AtlasError'
  },
  ok: 0,
  code: 8000,
  codeName: 'AtlasError',
  connectionGeneration: 0,
  level: 'error',
  message: 'Database connection error: bad auth : authentication failed',
  stack: 'MongoServerError: bad auth : authentication failed\n' +
    '    at Connection.sendCommand (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\connection.js:299:27)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async Connection.command (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\connection.js:327:26)\n' +
    '    at async continueScramConversation (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:131:15)\n' +
    '    at async executeScram (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:80:5)\n' +
    '    at async ScramSHA1.auth (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:39:16)\n' +
    '    at async performInitialHandshake (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\connect.js:104:13)\n' +
    '    at async connect (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\connect.js:24:9)',
  timestamp: '2025-06-01 20:01:56'
}
{
  service: 'ampd-livestock-api',
  errorLabelSet: Set(2) { 'HandshakeError', 'ResetPool' },
  errorResponse: {
    ok: 0,
    errmsg: 'bad auth : authentication failed',
    code: 8000,
    codeName: 'AtlasError'
  },
  ok: 0,
  code: 8000,
  codeName: 'AtlasError',
  connectionGeneration: 0,
  level: 'error',
  message: 'Database connection error: bad auth : authentication failed',
  stack: 'MongoServerError: bad auth : authentication failed\n' +
    '    at Connection.sendCommand (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\connection.js:299:27)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async Connection.command (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\connection.js:327:26)\n' +
    '    at async continueScramConversation (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:131:15)\n' +
    '    at async executeScram (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:80:5)\n' +
    '    at async ScramSHA1.auth (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:39:16)\n' +
    '    at async performInitialHandshake (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\connect.js:104:13)\n' +
    '    at async connect (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\connect.js:24:9)',
  timestamp: '2025-06-01 20:04:11'
}
{
  service: 'ampd-livestock-api',
  errorLabelSet: Set(2) { 'HandshakeError', 'ResetPool' },
  errorResponse: {
    ok: 0,
    errmsg: 'bad auth : authentication failed',
    code: 8000,
    codeName: 'AtlasError'
  },
  ok: 0,
  code: 8000,
  codeName: 'AtlasError',
  connectionGeneration: 0,
  level: 'error',
  message: 'Database connection error: bad auth : authentication failed',
  stack: 'MongoServerError: bad auth : authentication failed\n' +
    '    at Connection.sendCommand (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\connection.js:299:27)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async Connection.command (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\connection.js:327:26)\n' +
    '    at async continueScramConversation (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:131:15)\n' +
    '    at async executeScram (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:80:5)\n' +
    '    at async ScramSHA1.auth (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:39:16)\n' +
    '    at async performInitialHandshake (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\connect.js:104:13)\n' +
    '    at async connect (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\connect.js:24:9)',
  timestamp: '2025-06-01 20:04:43'
}
{
  service: 'ampd-livestock-api',
  errorLabelSet: Set(2) { 'HandshakeError', 'ResetPool' },
  errorResponse: {
    ok: 0,
    errmsg: 'bad auth : authentication failed',
    code: 8000,
    codeName: 'AtlasError'
  },
  ok: 0,
  code: 8000,
  codeName: 'AtlasError',
  connectionGeneration: 0,
  level: 'error',
  message: 'Database connection error: bad auth : authentication failed',
  stack: 'MongoServerError: bad auth : authentication failed\n' +
    '    at Connection.sendCommand (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\connection.js:299:27)\n' +
    '    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n' +
    '    at async Connection.command (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\connection.js:327:26)\n' +
    '    at async continueScramConversation (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:131:15)\n' +
    '    at async executeScram (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:80:5)\n' +
    '    at async ScramSHA1.auth (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\auth\\scram.js:39:16)\n' +
    '    at async performInitialHandshake (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\connect.js:104:13)\n' +
    '    at async connect (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongodb\\lib\\cmap\\connect.js:24:9)',
  timestamp: '2025-06-01 20:06:01'
}
{
  service: 'ampd-livestock-api',
  message: 'Database connection error: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017',
  errorLabelSet: Set(0) {},
  reason: TopologyDescription {
    type: 'Unknown',
    servers: Map(1) {
      'localhost:27017' => ServerDescription {
        address: 'localhost:27017',
        type: 'Unknown',
        hosts: [],
        passives: [],
        arbiters: [],
        tags: {},
        minWireVersion: 0,
        maxWireVersion: 0,
        roundTripTime: -1,
        minRoundTripTime: 0,
        lastUpdateTime: 88546961,
        lastWriteDate: 0,
        error: MongoNetworkError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017
            at Socket.<anonymous> (C:\Users\<USER>\OneDrive\Desktop\AgriIntel\backend\node_modules\mongodb\lib\cmap\connect.js:285:44)
            at Object.onceWrapper (node:events:633:26)
            at Socket.emit (node:events:518:28)
            at emitErrorNT (node:internal/streams/destroy:170:8)
            at emitErrorCloseNT (node:internal/streams/destroy:129:3)
            at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
          errorLabelSet: Set(1) { 'ResetPool' },
          beforeHandshake: false,
          [cause]: AggregateError [ECONNREFUSED]: 
              at internalConnectMultiple (node:net:1139:18)
              at afterConnectMultiple (node:net:1714:7) {
            code: 'ECONNREFUSED',
            [errors]: [
              Error: connect ECONNREFUSED ::1:27017
                  at createConnectionError (node:net:1677:14)
                  at afterConnectMultiple (node:net:1707:16) {
                errno: -4078,
                code: 'ECONNREFUSED',
                syscall: 'connect',
                address: '::1',
                port: 27017
              },
              Error: connect ECONNREFUSED 127.0.0.1:27017
                  at createConnectionError (node:net:1677:14)
                  at afterConnectMultiple (node:net:1707:16) {
                errno: -4078,
                code: 'ECONNREFUSED',
                syscall: 'connect',
                address: '127.0.0.1',
                port: 27017
              }
            ]
          }
        },
        topologyVersion: null,
        setName: null,
        setVersion: null,
        electionId: null,
        logicalSessionTimeoutMinutes: null,
        maxMessageSizeBytes: null,
        maxWriteBatchSize: null,
        maxBsonObjectSize: null,
        primary: null,
        me: null,
        '$clusterTime': null,
        iscryptd: false
      }
    },
    stale: false,
    compatible: true,
    heartbeatFrequencyMS: 10000,
    localThresholdMS: 15,
    setName: null,
    maxElectionId: null,
    maxSetVersion: null,
    commonWireVersion: 0,
    logicalSessionTimeoutMinutes: null
  },
  code: undefined,
  cause: TopologyDescription {
    type: 'Unknown',
    servers: Map(1) {
      'localhost:27017' => ServerDescription {
        address: 'localhost:27017',
        type: 'Unknown',
        hosts: [],
        passives: [],
        arbiters: [],
        tags: {},
        minWireVersion: 0,
        maxWireVersion: 0,
        roundTripTime: -1,
        minRoundTripTime: 0,
        lastUpdateTime: 88546961,
        lastWriteDate: 0,
        error: MongoNetworkError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017
            at Socket.<anonymous> (C:\Users\<USER>\OneDrive\Desktop\AgriIntel\backend\node_modules\mongodb\lib\cmap\connect.js:285:44)
            at Object.onceWrapper (node:events:633:26)
            at Socket.emit (node:events:518:28)
            at emitErrorNT (node:internal/streams/destroy:170:8)
            at emitErrorCloseNT (node:internal/streams/destroy:129:3)
            at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
          errorLabelSet: Set(1) { 'ResetPool' },
          beforeHandshake: false,
          [cause]: AggregateError [ECONNREFUSED]: 
              at internalConnectMultiple (node:net:1139:18)
              at afterConnectMultiple (node:net:1714:7) {
            code: 'ECONNREFUSED',
            [errors]: [
              Error: connect ECONNREFUSED ::1:27017
                  at createConnectionError (node:net:1677:14)
                  at afterConnectMultiple (node:net:1707:16) {
                errno: -4078,
                code: 'ECONNREFUSED',
                syscall: 'connect',
                address: '::1',
                port: 27017
              },
              Error: connect ECONNREFUSED 127.0.0.1:27017
                  at createConnectionError (node:net:1677:14)
                  at afterConnectMultiple (node:net:1707:16) {
                errno: -4078,
                code: 'ECONNREFUSED',
                syscall: 'connect',
                address: '127.0.0.1',
                port: 27017
              }
            ]
          }
        },
        topologyVersion: null,
        setName: null,
        setVersion: null,
        electionId: null,
        logicalSessionTimeoutMinutes: null,
        maxMessageSizeBytes: null,
        maxWriteBatchSize: null,
        maxBsonObjectSize: null,
        primary: null,
        me: null,
        '$clusterTime': null,
        iscryptd: false
      }
    },
    stale: false,
    compatible: true,
    heartbeatFrequencyMS: 10000,
    localThresholdMS: 15,
    setName: null,
    maxElectionId: null,
    maxSetVersion: null,
    commonWireVersion: 0,
    logicalSessionTimeoutMinutes: null
  },
  level: 'error',
  stack: 'MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n' +
    '    at _handleConnectionErrors (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n' +
    '    at NativeConnection.openUri (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n' +
    '    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\src\\config\\database.js:16:18)\n' +
    '    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\src\\server.js:109:7)',
  timestamp: '2025-06-01 20:06:54'
}
{
  service: 'ampd-livestock-api',
  message: 'Database connection error: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017',
  errorLabelSet: Set(0) {},
  reason: TopologyDescription {
    type: 'Unknown',
    servers: Map(1) {
      'localhost:27017' => ServerDescription {
        address: 'localhost:27017',
        type: 'Unknown',
        hosts: [],
        passives: [],
        arbiters: [],
        tags: {},
        minWireVersion: 0,
        maxWireVersion: 0,
        roundTripTime: -1,
        minRoundTripTime: 0,
        lastUpdateTime: 88546960,
        lastWriteDate: 0,
        error: MongoNetworkError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017
            at Socket.<anonymous> (C:\Users\<USER>\OneDrive\Desktop\AgriIntel\backend\node_modules\mongodb\lib\cmap\connect.js:285:44)
            at Object.onceWrapper (node:events:633:26)
            at Socket.emit (node:events:518:28)
            at emitErrorNT (node:internal/streams/destroy:170:8)
            at emitErrorCloseNT (node:internal/streams/destroy:129:3)
            at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
          errorLabelSet: Set(1) { 'ResetPool' },
          beforeHandshake: false,
          [cause]: AggregateError [ECONNREFUSED]: 
              at internalConnectMultiple (node:net:1139:18)
              at afterConnectMultiple (node:net:1714:7) {
            code: 'ECONNREFUSED',
            [errors]: [
              Error: connect ECONNREFUSED ::1:27017
                  at createConnectionError (node:net:1677:14)
                  at afterConnectMultiple (node:net:1707:16) {
                errno: -4078,
                code: 'ECONNREFUSED',
                syscall: 'connect',
                address: '::1',
                port: 27017
              },
              Error: connect ECONNREFUSED 127.0.0.1:27017
                  at createConnectionError (node:net:1677:14)
                  at afterConnectMultiple (node:net:1707:16) {
                errno: -4078,
                code: 'ECONNREFUSED',
                syscall: 'connect',
                address: '127.0.0.1',
                port: 27017
              }
            ]
          }
        },
        topologyVersion: null,
        setName: null,
        setVersion: null,
        electionId: null,
        logicalSessionTimeoutMinutes: null,
        maxMessageSizeBytes: null,
        maxWriteBatchSize: null,
        maxBsonObjectSize: null,
        primary: null,
        me: null,
        '$clusterTime': null,
        iscryptd: false
      }
    },
    stale: false,
    compatible: true,
    heartbeatFrequencyMS: 10000,
    localThresholdMS: 15,
    setName: null,
    maxElectionId: null,
    maxSetVersion: null,
    commonWireVersion: 0,
    logicalSessionTimeoutMinutes: null
  },
  code: undefined,
  cause: TopologyDescription {
    type: 'Unknown',
    servers: Map(1) {
      'localhost:27017' => ServerDescription {
        address: 'localhost:27017',
        type: 'Unknown',
        hosts: [],
        passives: [],
        arbiters: [],
        tags: {},
        minWireVersion: 0,
        maxWireVersion: 0,
        roundTripTime: -1,
        minRoundTripTime: 0,
        lastUpdateTime: 88546960,
        lastWriteDate: 0,
        error: MongoNetworkError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017
            at Socket.<anonymous> (C:\Users\<USER>\OneDrive\Desktop\AgriIntel\backend\node_modules\mongodb\lib\cmap\connect.js:285:44)
            at Object.onceWrapper (node:events:633:26)
            at Socket.emit (node:events:518:28)
            at emitErrorNT (node:internal/streams/destroy:170:8)
            at emitErrorCloseNT (node:internal/streams/destroy:129:3)
            at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
          errorLabelSet: Set(1) { 'ResetPool' },
          beforeHandshake: false,
          [cause]: AggregateError [ECONNREFUSED]: 
              at internalConnectMultiple (node:net:1139:18)
              at afterConnectMultiple (node:net:1714:7) {
            code: 'ECONNREFUSED',
            [errors]: [
              Error: connect ECONNREFUSED ::1:27017
                  at createConnectionError (node:net:1677:14)
                  at afterConnectMultiple (node:net:1707:16) {
                errno: -4078,
                code: 'ECONNREFUSED',
                syscall: 'connect',
                address: '::1',
                port: 27017
              },
              Error: connect ECONNREFUSED 127.0.0.1:27017
                  at createConnectionError (node:net:1677:14)
                  at afterConnectMultiple (node:net:1707:16) {
                errno: -4078,
                code: 'ECONNREFUSED',
                syscall: 'connect',
                address: '127.0.0.1',
                port: 27017
              }
            ]
          }
        },
        topologyVersion: null,
        setName: null,
        setVersion: null,
        electionId: null,
        logicalSessionTimeoutMinutes: null,
        maxMessageSizeBytes: null,
        maxWriteBatchSize: null,
        maxBsonObjectSize: null,
        primary: null,
        me: null,
        '$clusterTime': null,
        iscryptd: false
      }
    },
    stale: false,
    compatible: true,
    heartbeatFrequencyMS: 10000,
    localThresholdMS: 15,
    setName: null,
    maxElectionId: null,
    maxSetVersion: null,
    commonWireVersion: 0,
    logicalSessionTimeoutMinutes: null
  },
  level: 'error',
  stack: 'MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n' +
    '    at _handleConnectionErrors (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n' +
    '    at NativeConnection.openUri (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n' +
    '    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\src\\config\\database.js:16:18)\n' +
    '    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\src\\server.js:109:7)',
  timestamp: '2025-06-01 20:06:54'
}
{
  service: 'ampd-livestock-api',
  level: 'warn',
  message: 'Database connection failed, starting server without database:',
  timestamp: '2025-06-01 20:06:54'
}
{
  message: 'Some features may not work properly without database connection',
  level: 'warn',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:06:54'
}
{
  message: '🚀 AMPD Livestock Management API server running on port 3001',
  level: 'info',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:06:54'
}
{
  message: '📊 Health check: http://localhost:3001/health',
  level: 'info',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:06:54'
}
{
  message: '📋 API status: http://localhost:3001/api/status',
  level: 'info',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:06:54'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:06:54'
}
{
  message: '💡 Note: If database is not connected, install MongoDB locally or update connection string',
  level: 'info',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:06:54'
}
{
  service: 'ampd-livestock-api',
  message: 'Database connection error: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017',
  errorLabelSet: Set(0) {},
  reason: TopologyDescription {
    type: 'Unknown',
    servers: Map(1) {
      'localhost:27017' => ServerDescription {
        address: 'localhost:27017',
        type: 'Unknown',
        hosts: [],
        passives: [],
        arbiters: [],
        tags: {},
        minWireVersion: 0,
        maxWireVersion: 0,
        roundTripTime: -1,
        minRoundTripTime: 0,
        lastUpdateTime: 88616095,
        lastWriteDate: 0,
        error: MongoNetworkError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017
            at Socket.<anonymous> (C:\Users\<USER>\OneDrive\Desktop\AgriIntel\backend\node_modules\mongodb\lib\cmap\connect.js:285:44)
            at Object.onceWrapper (node:events:633:26)
            at Socket.emit (node:events:518:28)
            at emitErrorNT (node:internal/streams/destroy:170:8)
            at emitErrorCloseNT (node:internal/streams/destroy:129:3)
            at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
          errorLabelSet: Set(1) { 'ResetPool' },
          beforeHandshake: false,
          [cause]: AggregateError [ECONNREFUSED]: 
              at internalConnectMultiple (node:net:1139:18)
              at afterConnectMultiple (node:net:1714:7) {
            code: 'ECONNREFUSED',
            [errors]: [
              Error: connect ECONNREFUSED ::1:27017
                  at createConnectionError (node:net:1677:14)
                  at afterConnectMultiple (node:net:1707:16) {
                errno: -4078,
                code: 'ECONNREFUSED',
                syscall: 'connect',
                address: '::1',
                port: 27017
              },
              Error: connect ECONNREFUSED 127.0.0.1:27017
                  at createConnectionError (node:net:1677:14)
                  at afterConnectMultiple (node:net:1707:16) {
                errno: -4078,
                code: 'ECONNREFUSED',
                syscall: 'connect',
                address: '127.0.0.1',
                port: 27017
              }
            ]
          }
        },
        topologyVersion: null,
        setName: null,
        setVersion: null,
        electionId: null,
        logicalSessionTimeoutMinutes: null,
        maxMessageSizeBytes: null,
        maxWriteBatchSize: null,
        maxBsonObjectSize: null,
        primary: null,
        me: null,
        '$clusterTime': null,
        iscryptd: false
      }
    },
    stale: false,
    compatible: true,
    heartbeatFrequencyMS: 10000,
    localThresholdMS: 15,
    setName: null,
    maxElectionId: null,
    maxSetVersion: null,
    commonWireVersion: 0,
    logicalSessionTimeoutMinutes: null
  },
  code: undefined,
  cause: TopologyDescription {
    type: 'Unknown',
    servers: Map(1) {
      'localhost:27017' => ServerDescription {
        address: 'localhost:27017',
        type: 'Unknown',
        hosts: [],
        passives: [],
        arbiters: [],
        tags: {},
        minWireVersion: 0,
        maxWireVersion: 0,
        roundTripTime: -1,
        minRoundTripTime: 0,
        lastUpdateTime: 88616095,
        lastWriteDate: 0,
        error: MongoNetworkError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017
            at Socket.<anonymous> (C:\Users\<USER>\OneDrive\Desktop\AgriIntel\backend\node_modules\mongodb\lib\cmap\connect.js:285:44)
            at Object.onceWrapper (node:events:633:26)
            at Socket.emit (node:events:518:28)
            at emitErrorNT (node:internal/streams/destroy:170:8)
            at emitErrorCloseNT (node:internal/streams/destroy:129:3)
            at process.processTicksAndRejections (node:internal/process/task_queues:90:21) {
          errorLabelSet: Set(1) { 'ResetPool' },
          beforeHandshake: false,
          [cause]: AggregateError [ECONNREFUSED]: 
              at internalConnectMultiple (node:net:1139:18)
              at afterConnectMultiple (node:net:1714:7) {
            code: 'ECONNREFUSED',
            [errors]: [
              Error: connect ECONNREFUSED ::1:27017
                  at createConnectionError (node:net:1677:14)
                  at afterConnectMultiple (node:net:1707:16) {
                errno: -4078,
                code: 'ECONNREFUSED',
                syscall: 'connect',
                address: '::1',
                port: 27017
              },
              Error: connect ECONNREFUSED 127.0.0.1:27017
                  at createConnectionError (node:net:1677:14)
                  at afterConnectMultiple (node:net:1707:16) {
                errno: -4078,
                code: 'ECONNREFUSED',
                syscall: 'connect',
                address: '127.0.0.1',
                port: 27017
              }
            ]
          }
        },
        topologyVersion: null,
        setName: null,
        setVersion: null,
        electionId: null,
        logicalSessionTimeoutMinutes: null,
        maxMessageSizeBytes: null,
        maxWriteBatchSize: null,
        maxBsonObjectSize: null,
        primary: null,
        me: null,
        '$clusterTime': null,
        iscryptd: false
      }
    },
    stale: false,
    compatible: true,
    heartbeatFrequencyMS: 10000,
    localThresholdMS: 15,
    setName: null,
    maxElectionId: null,
    maxSetVersion: null,
    commonWireVersion: 0,
    logicalSessionTimeoutMinutes: null
  },
  level: 'error',
  stack: 'MongooseServerSelectionError: connect ECONNREFUSED ::1:27017, connect ECONNREFUSED 127.0.0.1:27017\n' +
    '    at _handleConnectionErrors (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongoose\\lib\\connection.js:1165:11)\n' +
    '    at NativeConnection.openUri (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\node_modules\\mongoose\\lib\\connection.js:1096:11)\n' +
    '    at async connectDB (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\src\\config\\database.js:16:18)\n' +
    '    at async startServer (C:\\Users\\<USER>\\OneDrive\\Desktop\\AgriIntel\\backend\\src\\server.js:109:7)',
  timestamp: '2025-06-01 20:08:03'
}
{
  service: 'ampd-livestock-api',
  level: 'warn',
  message: 'Database connection failed, starting server without database:',
  timestamp: '2025-06-01 20:08:03'
}
{
  message: 'Some features may not work properly without database connection',
  level: 'warn',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:08:03'
}
{
  message: '🚀 AMPD Livestock Management API server running on port 3001',
  level: 'info',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:08:03'
}
{
  message: '📊 Health check: http://localhost:3001/health',
  level: 'info',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:08:03'
}
{
  message: '📋 API status: http://localhost:3001/api/status',
  level: 'info',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:08:03'
}
{
  message: '🌍 Environment: development',
  level: 'info',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:08:03'
}
{
  message: '💡 Note: If database is not connected, install MongoDB locally or update connection string',
  level: 'info',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:08:03'
}
{
  message: 'GET /health - ::1',
  level: 'info',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:09:24'
}
{
  message: 'GET /health - ::1',
  level: 'info',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:09:31'
}
{
  message: 'GET /api/status - ::1',
  level: 'info',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:09:39'
}
{
  message: 'GET /api/status - ::1',
  level: 'info',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:09:58'
}
{
  message: 'GET /favicon.ico - ::1',
  level: 'info',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:09:58'
}
{
  message: 'GET /favicon.ico - ::1',
  level: 'info',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:10:00'
}
{
  message: 'GET /favicon.ico - ::1',
  level: 'info',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:10:00'
}
{
  message: 'GET /manifest.json - ::1',
  level: 'info',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:10:00'
}
{
  message: 'GET /manifest.json - ::1',
  level: 'info',
  service: 'ampd-livestock-api',
  timestamp: '2025-06-01 20:10:01'
}
