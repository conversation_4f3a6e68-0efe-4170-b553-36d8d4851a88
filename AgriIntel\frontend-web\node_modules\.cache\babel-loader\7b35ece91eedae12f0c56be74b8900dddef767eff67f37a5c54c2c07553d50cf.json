{"ast": null, "code": "'use strict';\n\n/** @type {import('.')} */\nvar $gOPD = require('./gOPD');\nif ($gOPD) {\n  try {\n    $gOPD([], 'length');\n  } catch (e) {\n    // IE 8 has a broken gOPD\n    $gOPD = null;\n  }\n}\nmodule.exports = $gOPD;", "map": {"version": 3, "names": ["$gOPD", "require", "e", "module", "exports"], "sources": ["C:/Users/<USER>/node_modules/gopd/index.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('.')} */\nvar $gOPD = require('./gOPD');\n\nif ($gOPD) {\n\ttry {\n\t\t$gOPD([], 'length');\n\t} catch (e) {\n\t\t// IE 8 has a broken gOPD\n\t\t$gOPD = null;\n\t}\n}\n\nmodule.exports = $gOPD;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA,IAAIA,KAAK,GAAGC,OAAO,CAAC,QAAQ,CAAC;AAE7B,IAAID,KAAK,EAAE;EACV,IAAI;IACHA,KAAK,CAAC,EAAE,EAAE,QAAQ,CAAC;EACpB,CAAC,CAAC,OAAOE,CAAC,EAAE;IACX;IACAF,KAAK,GAAG,IAAI;EACb;AACD;AAEAG,MAAM,CAACC,OAAO,GAAGJ,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}