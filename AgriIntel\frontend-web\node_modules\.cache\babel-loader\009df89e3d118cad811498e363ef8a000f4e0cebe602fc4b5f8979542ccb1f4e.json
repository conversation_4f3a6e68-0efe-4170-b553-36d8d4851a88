{"ast": null, "code": "'use strict';\n\n/** @type {import('./functionApply')} */\nmodule.exports = Function.prototype.apply;", "map": {"version": 3, "names": ["module", "exports", "Function", "prototype", "apply"], "sources": ["C:/Users/<USER>/node_modules/call-bind-apply-helpers/functionApply.js"], "sourcesContent": ["'use strict';\n\n/** @type {import('./functionApply')} */\nmodule.exports = Function.prototype.apply;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACAA,MAAM,CAACC,OAAO,GAAGC,QAAQ,CAACC,SAAS,CAACC,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}