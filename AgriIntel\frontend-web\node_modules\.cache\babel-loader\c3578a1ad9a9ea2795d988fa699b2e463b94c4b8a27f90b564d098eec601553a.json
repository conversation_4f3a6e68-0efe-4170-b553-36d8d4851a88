{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.RemoveUserOperation = void 0;\nconst command_1 = require(\"./command\");\nconst operation_1 = require(\"./operation\");\n/** @internal */\nclass RemoveUserOperation extends command_1.CommandOperation {\n  constructor(db, username, options) {\n    super(db, options);\n    this.options = options;\n    this.username = username;\n  }\n  get commandName() {\n    return 'dropUser';\n  }\n  async execute(server, session, timeoutContext) {\n    await super.executeCommand(server, session, {\n      dropUser: this.username\n    }, timeoutContext);\n    return true;\n  }\n}\nexports.RemoveUserOperation = RemoveUserOperation;\n(0, operation_1.defineAspects)(RemoveUserOperation, [operation_1.Aspect.WRITE_OPERATION]);", "map": {"version": 3, "names": ["command_1", "require", "operation_1", "RemoveUserOperation", "CommandOperation", "constructor", "db", "username", "options", "commandName", "execute", "server", "session", "timeoutContext", "executeCommand", "dropUser", "exports", "defineAspects", "Aspect", "WRITE_OPERATION"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\operations\\remove_user.ts"], "sourcesContent": ["import type { Db } from '../db';\nimport type { Server } from '../sdam/server';\nimport type { ClientSession } from '../sessions';\nimport { type TimeoutContext } from '../timeout';\nimport { CommandOperation, type CommandOperationOptions } from './command';\nimport { Aspect, defineAspects } from './operation';\n\n/** @public */\nexport type RemoveUserOptions = CommandOperationOptions;\n\n/** @internal */\nexport class RemoveUserOperation extends CommandOperation<boolean> {\n  override options: RemoveUserOptions;\n  username: string;\n\n  constructor(db: Db, username: string, options: RemoveUserOptions) {\n    super(db, options);\n    this.options = options;\n    this.username = username;\n  }\n\n  override get commandName() {\n    return 'dropUser' as const;\n  }\n\n  override async execute(\n    server: Server,\n    session: ClientSession | undefined,\n    timeoutContext: TimeoutContext\n  ): Promise<boolean> {\n    await super.executeCommand(server, session, { dropUser: this.username }, timeoutContext);\n    return true;\n  }\n}\n\ndefineAspects(RemoveUserOperation, [Aspect.WRITE_OPERATION]);\n"], "mappings": ";;;;;;AAIA,MAAAA,SAAA,GAAAC,OAAA;AACA,MAAAC,WAAA,GAAAD,OAAA;AAKA;AACA,MAAaE,mBAAoB,SAAQH,SAAA,CAAAI,gBAAyB;EAIhEC,YAAYC,EAAM,EAAEC,QAAgB,EAAEC,OAA0B;IAC9D,KAAK,CAACF,EAAE,EAAEE,OAAO,CAAC;IAClB,IAAI,CAACA,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACD,QAAQ,GAAGA,QAAQ;EAC1B;EAEA,IAAaE,WAAWA,CAAA;IACtB,OAAO,UAAmB;EAC5B;EAES,MAAMC,OAAOA,CACpBC,MAAc,EACdC,OAAkC,EAClCC,cAA8B;IAE9B,MAAM,KAAK,CAACC,cAAc,CAACH,MAAM,EAAEC,OAAO,EAAE;MAAEG,QAAQ,EAAE,IAAI,CAACR;IAAQ,CAAE,EAAEM,cAAc,CAAC;IACxF,OAAO,IAAI;EACb;;AArBFG,OAAA,CAAAb,mBAAA,GAAAA,mBAAA;AAwBA,IAAAD,WAAA,CAAAe,aAAa,EAACd,mBAAmB,EAAE,CAACD,WAAA,CAAAgB,MAAM,CAACC,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}