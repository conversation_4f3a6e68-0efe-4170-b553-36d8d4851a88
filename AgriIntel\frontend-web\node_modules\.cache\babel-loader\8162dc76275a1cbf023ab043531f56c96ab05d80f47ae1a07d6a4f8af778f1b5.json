{"ast": null, "code": "\"use strict\";\n\nconst utf8Encoder = new TextEncoder();\nconst utf8Decoder = new TextDecoder(\"utf-8\", {\n  ignoreBOM: true\n});\nfunction utf8Encode(string) {\n  return utf8Encoder.encode(string);\n}\nfunction utf8DecodeWithoutBOM(bytes) {\n  return utf8Decoder.decode(bytes);\n}\nmodule.exports = {\n  utf8Encode,\n  utf8DecodeWithoutBOM\n};", "map": {"version": 3, "names": ["utf8Encoder", "TextEncoder", "utf8Decoder", "TextDecoder", "ignoreBOM", "utf8Encode", "string", "encode", "utf8DecodeWithoutBOM", "bytes", "decode", "module", "exports"], "sources": ["C:/Users/<USER>/node_modules/mongodb-connection-string-url/node_modules/whatwg-url/lib/encoding.js"], "sourcesContent": ["\"use strict\";\nconst utf8Encoder = new TextEncoder();\nconst utf8Decoder = new TextDecoder(\"utf-8\", { ignoreBOM: true });\n\nfunction utf8Encode(string) {\n  return utf8Encoder.encode(string);\n}\n\nfunction utf8DecodeWithoutBOM(bytes) {\n  return utf8Decoder.decode(bytes);\n}\n\nmodule.exports = {\n  utf8Encode,\n  utf8DecodeWithoutBOM\n};\n"], "mappings": "AAAA,YAAY;;AACZ,MAAMA,WAAW,GAAG,IAAIC,WAAW,CAAC,CAAC;AACrC,MAAMC,WAAW,GAAG,IAAIC,WAAW,CAAC,OAAO,EAAE;EAAEC,SAAS,EAAE;AAAK,CAAC,CAAC;AAEjE,SAASC,UAAUA,CAACC,MAAM,EAAE;EAC1B,OAAON,WAAW,CAACO,MAAM,CAACD,MAAM,CAAC;AACnC;AAEA,SAASE,oBAAoBA,CAACC,KAAK,EAAE;EACnC,OAAOP,WAAW,CAACQ,MAAM,CAACD,KAAK,CAAC;AAClC;AAEAE,MAAM,CAACC,OAAO,GAAG;EACfP,UAAU;EACVG;AACF,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}