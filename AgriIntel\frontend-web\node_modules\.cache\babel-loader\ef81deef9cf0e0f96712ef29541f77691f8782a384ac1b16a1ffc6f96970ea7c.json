{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";import{createSlice}from'@reduxjs/toolkit';const initialState={sidebarOpen:true,theme:'light',language:localStorage.getItem('language')||'en',notifications:[],loading:{global:false},modals:{},breadcrumbs:[],pageTitle:'Dashboard'};const uiSlice=createSlice({name:'ui',initialState,reducers:{toggleSidebar:state=>{state.sidebarOpen=!state.sidebarOpen;},setSidebarOpen:(state,action)=>{state.sidebarOpen=action.payload;},setTheme:(state,action)=>{state.theme=action.payload;localStorage.setItem('theme',action.payload);},setLanguage:(state,action)=>{state.language=action.payload;localStorage.setItem('language',action.payload);},addNotification:(state,action)=>{const notification=_objectSpread(_objectSpread({},action.payload),{},{id:Date.now().toString(),timestamp:new Date().toISOString(),read:false});state.notifications.unshift(notification);// Keep only last 50 notifications\nif(state.notifications.length>50){state.notifications=state.notifications.slice(0,50);}},removeNotification:(state,action)=>{state.notifications=state.notifications.filter(n=>n.id!==action.payload);},markNotificationAsRead:(state,action)=>{const notification=state.notifications.find(n=>n.id===action.payload);if(notification){notification.read=true;}},markAllNotificationsAsRead:state=>{state.notifications.forEach(n=>n.read=true);},clearNotifications:state=>{state.notifications=[];},setLoading:(state,action)=>{state.loading[action.payload.key]=action.payload.loading;},setGlobalLoading:(state,action)=>{state.loading.global=action.payload;},openModal:(state,action)=>{state.modals[action.payload]=true;},closeModal:(state,action)=>{state.modals[action.payload]=false;},toggleModal:(state,action)=>{state.modals[action.payload]=!state.modals[action.payload];},setBreadcrumbs:(state,action)=>{state.breadcrumbs=action.payload;},setPageTitle:(state,action)=>{state.pageTitle=action.payload;document.title=\"\".concat(action.payload,\" - AMPD Livestock Management\");}}});export const{toggleSidebar,setSidebarOpen,setTheme,setLanguage,addNotification,removeNotification,markNotificationAsRead,markAllNotificationsAsRead,clearNotifications,setLoading,setGlobalLoading,openModal,closeModal,toggleModal,setBreadcrumbs,setPageTitle}=uiSlice.actions;export default uiSlice.reducer;", "map": {"version": 3, "names": ["createSlice", "initialState", "sidebarOpen", "theme", "language", "localStorage", "getItem", "notifications", "loading", "global", "modals", "breadcrumbs", "pageTitle", "uiSlice", "name", "reducers", "toggleSidebar", "state", "setSidebarOpen", "action", "payload", "setTheme", "setItem", "setLanguage", "addNotification", "notification", "_objectSpread", "id", "Date", "now", "toString", "timestamp", "toISOString", "read", "unshift", "length", "slice", "removeNotification", "filter", "n", "markNotificationAsRead", "find", "markAllNotificationsAsRead", "for<PERSON>ach", "clearNotifications", "setLoading", "key", "setGlobalLoading", "openModal", "closeModal", "toggleModal", "setBreadcrumbs", "setPageTitle", "document", "title", "concat", "actions", "reducer"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/store/slices/uiSlice.ts"], "sourcesContent": ["import { createSlice, PayloadAction } from '@reduxjs/toolkit';\n\nexport interface Notification {\n  id: string;\n  type: 'success' | 'error' | 'warning' | 'info';\n  title: string;\n  message: string;\n  duration?: number;\n  timestamp: string;\n  read: boolean;\n}\n\nexport interface UIState {\n  sidebarOpen: boolean;\n  theme: 'light' | 'dark';\n  language: 'en' | 'af' | 'st' | 'tn' | 'zu';\n  notifications: Notification[];\n  loading: {\n    global: boolean;\n    [key: string]: boolean;\n  };\n  modals: {\n    [key: string]: boolean;\n  };\n  breadcrumbs: Array<{\n    label: string;\n    path?: string;\n  }>;\n  pageTitle: string;\n}\n\nconst initialState: UIState = {\n  sidebarOpen: true,\n  theme: 'light',\n  language: (localStorage.getItem('language') as any) || 'en',\n  notifications: [],\n  loading: {\n    global: false,\n  },\n  modals: {},\n  breadcrumbs: [],\n  pageTitle: 'Dashboard',\n};\n\nconst uiSlice = createSlice({\n  name: 'ui',\n  initialState,\n  reducers: {\n    toggleSidebar: (state) => {\n      state.sidebarOpen = !state.sidebarOpen;\n    },\n    setSidebarOpen: (state, action: PayloadAction<boolean>) => {\n      state.sidebarOpen = action.payload;\n    },\n    setTheme: (state, action: PayloadAction<'light' | 'dark'>) => {\n      state.theme = action.payload;\n      localStorage.setItem('theme', action.payload);\n    },\n    setLanguage: (state, action: PayloadAction<'en' | 'af' | 'st' | 'tn' | 'zu'>) => {\n      state.language = action.payload;\n      localStorage.setItem('language', action.payload);\n    },\n    addNotification: (state, action: PayloadAction<Omit<Notification, 'id' | 'timestamp' | 'read'>>) => {\n      const notification: Notification = {\n        ...action.payload,\n        id: Date.now().toString(),\n        timestamp: new Date().toISOString(),\n        read: false,\n      };\n      state.notifications.unshift(notification);\n      \n      // Keep only last 50 notifications\n      if (state.notifications.length > 50) {\n        state.notifications = state.notifications.slice(0, 50);\n      }\n    },\n    removeNotification: (state, action: PayloadAction<string>) => {\n      state.notifications = state.notifications.filter(n => n.id !== action.payload);\n    },\n    markNotificationAsRead: (state, action: PayloadAction<string>) => {\n      const notification = state.notifications.find(n => n.id === action.payload);\n      if (notification) {\n        notification.read = true;\n      }\n    },\n    markAllNotificationsAsRead: (state) => {\n      state.notifications.forEach(n => n.read = true);\n    },\n    clearNotifications: (state) => {\n      state.notifications = [];\n    },\n    setLoading: (state, action: PayloadAction<{ key: string; loading: boolean }>) => {\n      state.loading[action.payload.key] = action.payload.loading;\n    },\n    setGlobalLoading: (state, action: PayloadAction<boolean>) => {\n      state.loading.global = action.payload;\n    },\n    openModal: (state, action: PayloadAction<string>) => {\n      state.modals[action.payload] = true;\n    },\n    closeModal: (state, action: PayloadAction<string>) => {\n      state.modals[action.payload] = false;\n    },\n    toggleModal: (state, action: PayloadAction<string>) => {\n      state.modals[action.payload] = !state.modals[action.payload];\n    },\n    setBreadcrumbs: (state, action: PayloadAction<UIState['breadcrumbs']>) => {\n      state.breadcrumbs = action.payload;\n    },\n    setPageTitle: (state, action: PayloadAction<string>) => {\n      state.pageTitle = action.payload;\n      document.title = `${action.payload} - AMPD Livestock Management`;\n    },\n  },\n});\n\nexport const {\n  toggleSidebar,\n  setSidebarOpen,\n  setTheme,\n  setLanguage,\n  addNotification,\n  removeNotification,\n  markNotificationAsRead,\n  markAllNotificationsAsRead,\n  clearNotifications,\n  setLoading,\n  setGlobalLoading,\n  openModal,\n  closeModal,\n  toggleModal,\n  setBreadcrumbs,\n  setPageTitle,\n} = uiSlice.actions;\n\nexport default uiSlice.reducer;\n"], "mappings": "gJAAA,OAASA,WAAW,KAAuB,kBAAkB,CA+B7D,KAAM,CAAAC,YAAqB,CAAG,CAC5BC,WAAW,CAAE,IAAI,CACjBC,KAAK,CAAE,OAAO,CACdC,QAAQ,CAAGC,YAAY,CAACC,OAAO,CAAC,UAAU,CAAC,EAAY,IAAI,CAC3DC,aAAa,CAAE,EAAE,CACjBC,OAAO,CAAE,CACPC,MAAM,CAAE,KACV,CAAC,CACDC,MAAM,CAAE,CAAC,CAAC,CACVC,WAAW,CAAE,EAAE,CACfC,SAAS,CAAE,WACb,CAAC,CAED,KAAM,CAAAC,OAAO,CAAGb,WAAW,CAAC,CAC1Bc,IAAI,CAAE,IAAI,CACVb,YAAY,CACZc,QAAQ,CAAE,CACRC,aAAa,CAAGC,KAAK,EAAK,CACxBA,KAAK,CAACf,WAAW,CAAG,CAACe,KAAK,CAACf,WAAW,CACxC,CAAC,CACDgB,cAAc,CAAEA,CAACD,KAAK,CAAEE,MAA8B,GAAK,CACzDF,KAAK,CAACf,WAAW,CAAGiB,MAAM,CAACC,OAAO,CACpC,CAAC,CACDC,QAAQ,CAAEA,CAACJ,KAAK,CAAEE,MAAuC,GAAK,CAC5DF,KAAK,CAACd,KAAK,CAAGgB,MAAM,CAACC,OAAO,CAC5Bf,YAAY,CAACiB,OAAO,CAAC,OAAO,CAAEH,MAAM,CAACC,OAAO,CAAC,CAC/C,CAAC,CACDG,WAAW,CAAEA,CAACN,KAAK,CAAEE,MAAuD,GAAK,CAC/EF,KAAK,CAACb,QAAQ,CAAGe,MAAM,CAACC,OAAO,CAC/Bf,YAAY,CAACiB,OAAO,CAAC,UAAU,CAAEH,MAAM,CAACC,OAAO,CAAC,CAClD,CAAC,CACDI,eAAe,CAAEA,CAACP,KAAK,CAAEE,MAAsE,GAAK,CAClG,KAAM,CAAAM,YAA0B,CAAAC,aAAA,CAAAA,aAAA,IAC3BP,MAAM,CAACC,OAAO,MACjBO,EAAE,CAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC,CACzBC,SAAS,CAAE,GAAI,CAAAH,IAAI,CAAC,CAAC,CAACI,WAAW,CAAC,CAAC,CACnCC,IAAI,CAAE,KAAK,EACZ,CACDhB,KAAK,CAACV,aAAa,CAAC2B,OAAO,CAACT,YAAY,CAAC,CAEzC;AACA,GAAIR,KAAK,CAACV,aAAa,CAAC4B,MAAM,CAAG,EAAE,CAAE,CACnClB,KAAK,CAACV,aAAa,CAAGU,KAAK,CAACV,aAAa,CAAC6B,KAAK,CAAC,CAAC,CAAE,EAAE,CAAC,CACxD,CACF,CAAC,CACDC,kBAAkB,CAAEA,CAACpB,KAAK,CAAEE,MAA6B,GAAK,CAC5DF,KAAK,CAACV,aAAa,CAAGU,KAAK,CAACV,aAAa,CAAC+B,MAAM,CAACC,CAAC,EAAIA,CAAC,CAACZ,EAAE,GAAKR,MAAM,CAACC,OAAO,CAAC,CAChF,CAAC,CACDoB,sBAAsB,CAAEA,CAACvB,KAAK,CAAEE,MAA6B,GAAK,CAChE,KAAM,CAAAM,YAAY,CAAGR,KAAK,CAACV,aAAa,CAACkC,IAAI,CAACF,CAAC,EAAIA,CAAC,CAACZ,EAAE,GAAKR,MAAM,CAACC,OAAO,CAAC,CAC3E,GAAIK,YAAY,CAAE,CAChBA,YAAY,CAACQ,IAAI,CAAG,IAAI,CAC1B,CACF,CAAC,CACDS,0BAA0B,CAAGzB,KAAK,EAAK,CACrCA,KAAK,CAACV,aAAa,CAACoC,OAAO,CAACJ,CAAC,EAAIA,CAAC,CAACN,IAAI,CAAG,IAAI,CAAC,CACjD,CAAC,CACDW,kBAAkB,CAAG3B,KAAK,EAAK,CAC7BA,KAAK,CAACV,aAAa,CAAG,EAAE,CAC1B,CAAC,CACDsC,UAAU,CAAEA,CAAC5B,KAAK,CAAEE,MAAwD,GAAK,CAC/EF,KAAK,CAACT,OAAO,CAACW,MAAM,CAACC,OAAO,CAAC0B,GAAG,CAAC,CAAG3B,MAAM,CAACC,OAAO,CAACZ,OAAO,CAC5D,CAAC,CACDuC,gBAAgB,CAAEA,CAAC9B,KAAK,CAAEE,MAA8B,GAAK,CAC3DF,KAAK,CAACT,OAAO,CAACC,MAAM,CAAGU,MAAM,CAACC,OAAO,CACvC,CAAC,CACD4B,SAAS,CAAEA,CAAC/B,KAAK,CAAEE,MAA6B,GAAK,CACnDF,KAAK,CAACP,MAAM,CAACS,MAAM,CAACC,OAAO,CAAC,CAAG,IAAI,CACrC,CAAC,CACD6B,UAAU,CAAEA,CAAChC,KAAK,CAAEE,MAA6B,GAAK,CACpDF,KAAK,CAACP,MAAM,CAACS,MAAM,CAACC,OAAO,CAAC,CAAG,KAAK,CACtC,CAAC,CACD8B,WAAW,CAAEA,CAACjC,KAAK,CAAEE,MAA6B,GAAK,CACrDF,KAAK,CAACP,MAAM,CAACS,MAAM,CAACC,OAAO,CAAC,CAAG,CAACH,KAAK,CAACP,MAAM,CAACS,MAAM,CAACC,OAAO,CAAC,CAC9D,CAAC,CACD+B,cAAc,CAAEA,CAAClC,KAAK,CAAEE,MAA6C,GAAK,CACxEF,KAAK,CAACN,WAAW,CAAGQ,MAAM,CAACC,OAAO,CACpC,CAAC,CACDgC,YAAY,CAAEA,CAACnC,KAAK,CAAEE,MAA6B,GAAK,CACtDF,KAAK,CAACL,SAAS,CAAGO,MAAM,CAACC,OAAO,CAChCiC,QAAQ,CAACC,KAAK,IAAAC,MAAA,CAAMpC,MAAM,CAACC,OAAO,gCAA8B,CAClE,CACF,CACF,CAAC,CAAC,CAEF,MAAO,MAAM,CACXJ,aAAa,CACbE,cAAc,CACdG,QAAQ,CACRE,WAAW,CACXC,eAAe,CACfa,kBAAkB,CAClBG,sBAAsB,CACtBE,0BAA0B,CAC1BE,kBAAkB,CAClBC,UAAU,CACVE,gBAAgB,CAChBC,SAAS,CACTC,UAAU,CACVC,WAAW,CACXC,cAAc,CACdC,YACF,CAAC,CAAGvC,OAAO,CAAC2C,OAAO,CAEnB,cAAe,CAAA3C,OAAO,CAAC4C,OAAO", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}