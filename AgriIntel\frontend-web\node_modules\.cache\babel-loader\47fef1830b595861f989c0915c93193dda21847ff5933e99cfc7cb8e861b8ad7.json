{"ast": null, "code": "import { useIsoMorphicEffect as s } from './use-iso-morphic-effect.js';\nlet u = new Map(),\n  t = new Map();\nfunction b(r) {\n  let l = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : !0;\n  s(() => {\n    var o;\n    if (!l) return;\n    let e = typeof r == \"function\" ? r() : r.current;\n    if (!e) return;\n    function a() {\n      var d;\n      if (!e) return;\n      let i = (d = t.get(e)) != null ? d : 1;\n      if (i === 1 ? t.delete(e) : t.set(e, i - 1), i !== 1) return;\n      let n = u.get(e);\n      n && (n[\"aria-hidden\"] === null ? e.removeAttribute(\"aria-hidden\") : e.setAttribute(\"aria-hidden\", n[\"aria-hidden\"]), e.inert = n.inert, u.delete(e));\n    }\n    let f = (o = t.get(e)) != null ? o : 0;\n    return t.set(e, f + 1), f !== 0 || (u.set(e, {\n      \"aria-hidden\": e.getAttribute(\"aria-hidden\"),\n      inert: e.inert\n    }), e.setAttribute(\"aria-hidden\", \"true\"), e.inert = !0), a;\n  }, [r, l]);\n}\nexport { b as useInert };", "map": {"version": 3, "names": ["useIsoMorphicEffect", "s", "u", "Map", "t", "b", "r", "l", "arguments", "length", "undefined", "o", "e", "current", "a", "d", "i", "get", "delete", "set", "n", "removeAttribute", "setAttribute", "inert", "f", "getAttribute", "useInert"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@headlessui/react/dist/hooks/use-inert.js"], "sourcesContent": ["import{useIsoMorphicEffect as s}from'./use-iso-morphic-effect.js';let u=new Map,t=new Map;function b(r,l=!0){s(()=>{var o;if(!l)return;let e=typeof r==\"function\"?r():r.current;if(!e)return;function a(){var d;if(!e)return;let i=(d=t.get(e))!=null?d:1;if(i===1?t.delete(e):t.set(e,i-1),i!==1)return;let n=u.get(e);n&&(n[\"aria-hidden\"]===null?e.removeAttribute(\"aria-hidden\"):e.setAttribute(\"aria-hidden\",n[\"aria-hidden\"]),e.inert=n.inert,u.delete(e))}let f=(o=t.get(e))!=null?o:0;return t.set(e,f+1),f!==0||(u.set(e,{\"aria-hidden\":e.getAttribute(\"aria-hidden\"),inert:e.inert}),e.setAttribute(\"aria-hidden\",\"true\"),e.inert=!0),a},[r,l])}export{b as useInert};\n"], "mappings": "AAAA,SAAOA,mBAAmB,IAAIC,CAAC,QAAK,6BAA6B;AAAC,IAAIC,CAAC,GAAC,IAAIC,GAAG,CAAD,CAAC;EAACC,CAAC,GAAC,IAAID,GAAG,CAAD,CAAC;AAAC,SAASE,CAACA,CAACC,CAAC,EAAM;EAAA,IAALC,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAC,CAAC,CAAC;EAAEP,CAAC,CAAC,MAAI;IAAC,IAAIU,CAAC;IAAC,IAAG,CAACJ,CAAC,EAAC;IAAO,IAAIK,CAAC,GAAC,OAAON,CAAC,IAAE,UAAU,GAACA,CAAC,CAAC,CAAC,GAACA,CAAC,CAACO,OAAO;IAAC,IAAG,CAACD,CAAC,EAAC;IAAO,SAASE,CAACA,CAAA,EAAE;MAAC,IAAIC,CAAC;MAAC,IAAG,CAACH,CAAC,EAAC;MAAO,IAAII,CAAC,GAAC,CAACD,CAAC,GAACX,CAAC,CAACa,GAAG,CAACL,CAAC,CAAC,KAAG,IAAI,GAACG,CAAC,GAAC,CAAC;MAAC,IAAGC,CAAC,KAAG,CAAC,GAACZ,CAAC,CAACc,MAAM,CAACN,CAAC,CAAC,GAACR,CAAC,CAACe,GAAG,CAACP,CAAC,EAACI,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,KAAG,CAAC,EAAC;MAAO,IAAII,CAAC,GAAClB,CAAC,CAACe,GAAG,CAACL,CAAC,CAAC;MAACQ,CAAC,KAAGA,CAAC,CAAC,aAAa,CAAC,KAAG,IAAI,GAACR,CAAC,CAACS,eAAe,CAAC,aAAa,CAAC,GAACT,CAAC,CAACU,YAAY,CAAC,aAAa,EAACF,CAAC,CAAC,aAAa,CAAC,CAAC,EAACR,CAAC,CAACW,KAAK,GAACH,CAAC,CAACG,KAAK,EAACrB,CAAC,CAACgB,MAAM,CAACN,CAAC,CAAC,CAAC;IAAA;IAAC,IAAIY,CAAC,GAAC,CAACb,CAAC,GAACP,CAAC,CAACa,GAAG,CAACL,CAAC,CAAC,KAAG,IAAI,GAACD,CAAC,GAAC,CAAC;IAAC,OAAOP,CAAC,CAACe,GAAG,CAACP,CAAC,EAACY,CAAC,GAAC,CAAC,CAAC,EAACA,CAAC,KAAG,CAAC,KAAGtB,CAAC,CAACiB,GAAG,CAACP,CAAC,EAAC;MAAC,aAAa,EAACA,CAAC,CAACa,YAAY,CAAC,aAAa,CAAC;MAACF,KAAK,EAACX,CAAC,CAACW;IAAK,CAAC,CAAC,EAACX,CAAC,CAACU,YAAY,CAAC,aAAa,EAAC,MAAM,CAAC,EAACV,CAAC,CAACW,KAAK,GAAC,CAAC,CAAC,CAAC,EAACT,CAAC;EAAA,CAAC,EAAC,CAACR,CAAC,EAACC,CAAC,CAAC,CAAC;AAAA;AAAC,SAAOF,CAAC,IAAIqB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}