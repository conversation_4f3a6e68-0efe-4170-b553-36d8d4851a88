{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.OnDemandDocument = void 0;\nconst bson_1 = require(\"../../../bson\");\n/** @internal */\nclass OnDemandDocument {\n  constructor(/** BSON bytes, this document begins at offset */\n  bson, /** The start of the document */\n  offset = 0, /** If this is an embedded document, indicates if this was a BSON array */\n  isArray = false, /** If elements was already calculated */\n  elements) {\n    this.bson = bson;\n    this.offset = offset;\n    this.isArray = isArray;\n    /**\n     * Maps JS strings to elements and jsValues for speeding up subsequent lookups.\n     * - If `false` then name does not exist in the BSON document\n     * - If `CachedBSONElement` instance name exists\n     * - If `cache[name].value == null` jsValue has not yet been parsed\n     *   - Null/Undefined values do not get cached because they are zero-length values.\n     */\n    this.cache = Object.create(null);\n    /** Caches the index of elements that have been named */\n    this.indexFound = Object.create(null);\n    this.elements = elements ?? (0, bson_1.parseToElementsToArray)(this.bson, offset);\n  }\n  /** Only supports basic latin strings */\n  isElementName(name, element) {\n    const nameLength = element[2 /* BSONElementOffset.nameLength */];\n    const nameOffset = element[1 /* BSONElementOffset.nameOffset */];\n    if (name.length !== nameLength) return false;\n    const nameEnd = nameOffset + nameLength;\n    for (let byteIndex = nameOffset, charIndex = 0; charIndex < name.length && byteIndex < nameEnd; charIndex++, byteIndex++) {\n      if (this.bson[byteIndex] !== name.charCodeAt(charIndex)) return false;\n    }\n    return true;\n  }\n  /**\n   * Seeks into the elements array for an element matching the given name.\n   *\n   * @remarks\n   * Caching:\n   * - Caches the existence of a property making subsequent look ups for non-existent properties return immediately\n   * - Caches names mapped to elements to avoid reiterating the array and comparing the name again\n   * - Caches the index at which an element has been found to prevent rechecking against elements already determined to belong to another name\n   *\n   * @param name - a basic latin string name of a BSON element\n   * @returns\n   */\n  getElement(name) {\n    const cachedElement = this.cache[name];\n    if (cachedElement === false) return null;\n    if (cachedElement != null) {\n      return cachedElement;\n    }\n    if (typeof name === 'number') {\n      if (this.isArray) {\n        if (name < this.elements.length) {\n          const element = this.elements[name];\n          const cachedElement = {\n            element,\n            value: undefined\n          };\n          this.cache[name] = cachedElement;\n          this.indexFound[name] = true;\n          return cachedElement;\n        } else {\n          return null;\n        }\n      } else {\n        return null;\n      }\n    }\n    for (let index = 0; index < this.elements.length; index++) {\n      const element = this.elements[index];\n      // skip this element if it has already been associated with a name\n      if (!(index in this.indexFound) && this.isElementName(name, element)) {\n        const cachedElement = {\n          element,\n          value: undefined\n        };\n        this.cache[name] = cachedElement;\n        this.indexFound[index] = true;\n        return cachedElement;\n      }\n    }\n    this.cache[name] = false;\n    return null;\n  }\n  toJSValue(element, as) {\n    const type = element[0 /* BSONElementOffset.type */];\n    const offset = element[3 /* BSONElementOffset.offset */];\n    const length = element[4 /* BSONElementOffset.length */];\n    if (as !== type) {\n      return null;\n    }\n    switch (as) {\n      case bson_1.BSONType.null:\n      case bson_1.BSONType.undefined:\n        return null;\n      case bson_1.BSONType.double:\n        return (0, bson_1.getFloat64LE)(this.bson, offset);\n      case bson_1.BSONType.int:\n        return (0, bson_1.getInt32LE)(this.bson, offset);\n      case bson_1.BSONType.long:\n        return (0, bson_1.getBigInt64LE)(this.bson, offset);\n      case bson_1.BSONType.bool:\n        return Boolean(this.bson[offset]);\n      case bson_1.BSONType.objectId:\n        return new bson_1.ObjectId(this.bson.subarray(offset, offset + 12));\n      case bson_1.BSONType.timestamp:\n        return new bson_1.Timestamp((0, bson_1.getBigInt64LE)(this.bson, offset));\n      case bson_1.BSONType.string:\n        return (0, bson_1.toUTF8)(this.bson, offset + 4, offset + length - 1, false);\n      case bson_1.BSONType.binData:\n        {\n          const totalBinarySize = (0, bson_1.getInt32LE)(this.bson, offset);\n          const subType = this.bson[offset + 4];\n          if (subType === 2) {\n            const subType2BinarySize = (0, bson_1.getInt32LE)(this.bson, offset + 1 + 4);\n            if (subType2BinarySize < 0) throw new bson_1.BSONError('Negative binary type element size found for subtype 0x02');\n            if (subType2BinarySize > totalBinarySize - 4) throw new bson_1.BSONError('Binary type with subtype 0x02 contains too long binary size');\n            if (subType2BinarySize < totalBinarySize - 4) throw new bson_1.BSONError('Binary type with subtype 0x02 contains too short binary size');\n            return new bson_1.Binary(this.bson.subarray(offset + 1 + 4 + 4, offset + 1 + 4 + 4 + subType2BinarySize), 2);\n          }\n          return new bson_1.Binary(this.bson.subarray(offset + 1 + 4, offset + 1 + 4 + totalBinarySize), subType);\n        }\n      case bson_1.BSONType.date:\n        // Pretend this is correct.\n        return new Date(Number((0, bson_1.getBigInt64LE)(this.bson, offset)));\n      case bson_1.BSONType.object:\n        return new OnDemandDocument(this.bson, offset);\n      case bson_1.BSONType.array:\n        return new OnDemandDocument(this.bson, offset, true);\n      default:\n        throw new bson_1.BSONError(`Unsupported BSON type: ${as}`);\n    }\n  }\n  /**\n   * Returns the number of elements in this BSON document\n   */\n  size() {\n    return this.elements.length;\n  }\n  /**\n   * Checks for the existence of an element by name.\n   *\n   * @remarks\n   * Uses `getElement` with the expectation that will populate caches such that a `has` call\n   * followed by a `getElement` call will not repeat the cost paid by the first look up.\n   *\n   * @param name - element name\n   */\n  has(name) {\n    const cachedElement = this.cache[name];\n    if (cachedElement === false) return false;\n    if (cachedElement != null) return true;\n    return this.getElement(name) != null;\n  }\n  get(name, as, required) {\n    const element = this.getElement(name);\n    if (element == null) {\n      if (required === true) {\n        throw new bson_1.BSONError(`BSON element \"${name}\" is missing`);\n      } else {\n        return null;\n      }\n    }\n    if (element.value == null) {\n      const value = this.toJSValue(element.element, as);\n      if (value == null) {\n        if (required === true) {\n          throw new bson_1.BSONError(`BSON element \"${name}\" is missing`);\n        } else {\n          return null;\n        }\n      }\n      // It is important to never store null\n      element.value = value;\n    }\n    return element.value;\n  }\n  getNumber(name, required) {\n    const maybeBool = this.get(name, bson_1.BSONType.bool);\n    const bool = maybeBool == null ? null : maybeBool ? 1 : 0;\n    const maybeLong = this.get(name, bson_1.BSONType.long);\n    const long = maybeLong == null ? null : Number(maybeLong);\n    const result = bool ?? long ?? this.get(name, bson_1.BSONType.int) ?? this.get(name, bson_1.BSONType.double);\n    if (required === true && result == null) {\n      throw new bson_1.BSONError(`BSON element \"${name}\" is missing`);\n    }\n    return result;\n  }\n  /**\n   * Deserialize this object, DOES NOT cache result so avoid multiple invocations\n   * @param options - BSON deserialization options\n   */\n  toObject(options) {\n    return (0, bson_1.deserialize)(this.bson, {\n      ...options,\n      index: this.offset,\n      allowObjectSmallerThanBufferSize: true\n    });\n  }\n  /** Returns this document's bytes only */\n  toBytes() {\n    const size = (0, bson_1.getInt32LE)(this.bson, this.offset);\n    return this.bson.subarray(this.offset, this.offset + size);\n  }\n}\nexports.OnDemandDocument = OnDemandDocument;", "map": {"version": 3, "names": ["bson_1", "require", "OnDemandDocument", "constructor", "bson", "offset", "isArray", "elements", "cache", "Object", "create", "indexFound", "parseToElementsToArray", "isElementName", "name", "element", "name<PERSON><PERSON><PERSON>", "nameOffset", "length", "nameEnd", "byteIndex", "charIndex", "charCodeAt", "getElement", "cachedElement", "value", "undefined", "index", "toJSValue", "as", "type", "BSONType", "null", "double", "getFloat64LE", "int", "getInt32LE", "long", "getBigInt64LE", "bool", "Boolean", "objectId", "ObjectId", "subarray", "timestamp", "Timestamp", "string", "toUTF8", "binData", "totalBinarySize", "subType", "subType2BinarySize", "BSONError", "Binary", "date", "Date", "Number", "object", "array", "size", "has", "get", "required", "getNumber", "maybeBool", "maybe<PERSON>ong", "result", "toObject", "options", "deserialize", "allowObjectSmallerThanBufferSize", "toBytes", "exports"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\wire_protocol\\on_demand\\document.ts"], "sourcesContent": ["import {\n  Binary,\n  type BSONElement,\n  BSONError,\n  BSONType,\n  deserialize,\n  type DeserializeOptions,\n  getBigInt64LE,\n  getFloat64LE,\n  getInt32LE,\n  ObjectId,\n  parseToElementsToArray,\n  Timestamp,\n  toUTF8\n} from '../../../bson';\n\n// eslint-disable-next-line no-restricted-syntax\nconst enum BSONElementOffset {\n  type = 0,\n  nameOffset = 1,\n  nameLength = 2,\n  offset = 3,\n  length = 4\n}\n\n/** @internal */\nexport type JSTypeOf = {\n  [BSONType.null]: null;\n  [BSONType.undefined]: null;\n  [BSONType.double]: number;\n  [BSONType.int]: number;\n  [BSONType.long]: bigint;\n  [BSONType.timestamp]: Timestamp;\n  [BSONType.binData]: Binary;\n  [BSONType.bool]: boolean;\n  [BSONType.objectId]: ObjectId;\n  [BSONType.string]: string;\n  [BSONType.date]: Date;\n  [BSONType.object]: OnDemandDocument;\n  [BSONType.array]: OnDemandDocument;\n};\n\n/** @internal */\ntype CachedBSONElement = { element: BSONElement; value: any | undefined };\n\n/**\n * @internal\n *\n * Options for `OnDemandDocument.toObject()`. Validation is required to ensure\n * that callers provide utf8 validation options. */\nexport type OnDemandDocumentDeserializeOptions = Omit<DeserializeOptions, 'validation'> &\n  Required<Pick<DeserializeOptions, 'validation'>>;\n\n/** @internal */\nexport class OnDemandDocument {\n  /**\n   * Maps JS strings to elements and jsValues for speeding up subsequent lookups.\n   * - If `false` then name does not exist in the BSON document\n   * - If `CachedBSONElement` instance name exists\n   * - If `cache[name].value == null` jsValue has not yet been parsed\n   *   - Null/Undefined values do not get cached because they are zero-length values.\n   */\n  private readonly cache: Record<string, CachedBSONElement | false | undefined> =\n    Object.create(null);\n  /** Caches the index of elements that have been named */\n  private readonly indexFound: Record<number, boolean> = Object.create(null);\n\n  /** All bson elements in this document */\n  private readonly elements: ReadonlyArray<BSONElement>;\n\n  constructor(\n    /** BSON bytes, this document begins at offset */\n    protected readonly bson: Uint8Array,\n    /** The start of the document */\n    private readonly offset = 0,\n    /** If this is an embedded document, indicates if this was a BSON array */\n    public readonly isArray = false,\n    /** If elements was already calculated */\n    elements?: BSONElement[]\n  ) {\n    this.elements = elements ?? parseToElementsToArray(this.bson, offset);\n  }\n\n  /** Only supports basic latin strings */\n  private isElementName(name: string, element: BSONElement): boolean {\n    const nameLength = element[BSONElementOffset.nameLength];\n    const nameOffset = element[BSONElementOffset.nameOffset];\n\n    if (name.length !== nameLength) return false;\n\n    const nameEnd = nameOffset + nameLength;\n    for (\n      let byteIndex = nameOffset, charIndex = 0;\n      charIndex < name.length && byteIndex < nameEnd;\n      charIndex++, byteIndex++\n    ) {\n      if (this.bson[byteIndex] !== name.charCodeAt(charIndex)) return false;\n    }\n\n    return true;\n  }\n\n  /**\n   * Seeks into the elements array for an element matching the given name.\n   *\n   * @remarks\n   * Caching:\n   * - Caches the existence of a property making subsequent look ups for non-existent properties return immediately\n   * - Caches names mapped to elements to avoid reiterating the array and comparing the name again\n   * - Caches the index at which an element has been found to prevent rechecking against elements already determined to belong to another name\n   *\n   * @param name - a basic latin string name of a BSON element\n   * @returns\n   */\n  private getElement(name: string | number): CachedBSONElement | null {\n    const cachedElement = this.cache[name];\n    if (cachedElement === false) return null;\n\n    if (cachedElement != null) {\n      return cachedElement;\n    }\n\n    if (typeof name === 'number') {\n      if (this.isArray) {\n        if (name < this.elements.length) {\n          const element = this.elements[name];\n          const cachedElement = { element, value: undefined };\n          this.cache[name] = cachedElement;\n          this.indexFound[name] = true;\n          return cachedElement;\n        } else {\n          return null;\n        }\n      } else {\n        return null;\n      }\n    }\n\n    for (let index = 0; index < this.elements.length; index++) {\n      const element = this.elements[index];\n\n      // skip this element if it has already been associated with a name\n      if (!(index in this.indexFound) && this.isElementName(name, element)) {\n        const cachedElement = { element, value: undefined };\n        this.cache[name] = cachedElement;\n        this.indexFound[index] = true;\n        return cachedElement;\n      }\n    }\n\n    this.cache[name] = false;\n    return null;\n  }\n\n  /**\n   * Translates BSON bytes into a javascript value. Checking `as` against the BSON element's type\n   * this methods returns the small subset of BSON types that the driver needs to function.\n   *\n   * @remarks\n   * - BSONType.null and BSONType.undefined always return null\n   * - If the type requested does not match this returns null\n   *\n   * @param element - The element to revive to a javascript value\n   * @param as - A type byte expected to be returned\n   */\n  private toJSValue<T extends keyof JSTypeOf>(element: BSONElement, as: T): JSTypeOf[T];\n  private toJSValue(element: BSONElement, as: keyof JSTypeOf): any {\n    const type = element[BSONElementOffset.type];\n    const offset = element[BSONElementOffset.offset];\n    const length = element[BSONElementOffset.length];\n\n    if (as !== type) {\n      return null;\n    }\n\n    switch (as) {\n      case BSONType.null:\n      case BSONType.undefined:\n        return null;\n      case BSONType.double:\n        return getFloat64LE(this.bson, offset);\n      case BSONType.int:\n        return getInt32LE(this.bson, offset);\n      case BSONType.long:\n        return getBigInt64LE(this.bson, offset);\n      case BSONType.bool:\n        return Boolean(this.bson[offset]);\n      case BSONType.objectId:\n        return new ObjectId(this.bson.subarray(offset, offset + 12));\n      case BSONType.timestamp:\n        return new Timestamp(getBigInt64LE(this.bson, offset));\n      case BSONType.string:\n        return toUTF8(this.bson, offset + 4, offset + length - 1, false);\n      case BSONType.binData: {\n        const totalBinarySize = getInt32LE(this.bson, offset);\n        const subType = this.bson[offset + 4];\n\n        if (subType === 2) {\n          const subType2BinarySize = getInt32LE(this.bson, offset + 1 + 4);\n          if (subType2BinarySize < 0)\n            throw new BSONError('Negative binary type element size found for subtype 0x02');\n          if (subType2BinarySize > totalBinarySize - 4)\n            throw new BSONError('Binary type with subtype 0x02 contains too long binary size');\n          if (subType2BinarySize < totalBinarySize - 4)\n            throw new BSONError('Binary type with subtype 0x02 contains too short binary size');\n          return new Binary(\n            this.bson.subarray(offset + 1 + 4 + 4, offset + 1 + 4 + 4 + subType2BinarySize),\n            2\n          );\n        }\n\n        return new Binary(\n          this.bson.subarray(offset + 1 + 4, offset + 1 + 4 + totalBinarySize),\n          subType\n        );\n      }\n      case BSONType.date:\n        // Pretend this is correct.\n        return new Date(Number(getBigInt64LE(this.bson, offset)));\n\n      case BSONType.object:\n        return new OnDemandDocument(this.bson, offset);\n      case BSONType.array:\n        return new OnDemandDocument(this.bson, offset, true);\n\n      default:\n        throw new BSONError(`Unsupported BSON type: ${as}`);\n    }\n  }\n\n  /**\n   * Returns the number of elements in this BSON document\n   */\n  public size() {\n    return this.elements.length;\n  }\n\n  /**\n   * Checks for the existence of an element by name.\n   *\n   * @remarks\n   * Uses `getElement` with the expectation that will populate caches such that a `has` call\n   * followed by a `getElement` call will not repeat the cost paid by the first look up.\n   *\n   * @param name - element name\n   */\n  public has(name: string): boolean {\n    const cachedElement = this.cache[name];\n    if (cachedElement === false) return false;\n    if (cachedElement != null) return true;\n    return this.getElement(name) != null;\n  }\n\n  /**\n   * Turns BSON element with `name` into a javascript value.\n   *\n   * @typeParam T - must be one of the supported BSON types determined by `JSTypeOf` this will determine the return type of this function.\n   * @param name - the element name\n   * @param as - the bson type expected\n   * @param required - whether or not the element is expected to exist, if true this function will throw if it is not present\n   */\n  public get<const T extends keyof JSTypeOf>(\n    name: string | number,\n    as: T,\n    required?: boolean | undefined\n  ): JSTypeOf[T] | null;\n\n  /** `required` will make `get` throw if name does not exist or is null/undefined */\n  public get<const T extends keyof JSTypeOf>(\n    name: string | number,\n    as: T,\n    required: true\n  ): JSTypeOf[T];\n\n  public get<const T extends keyof JSTypeOf>(\n    name: string | number,\n    as: T,\n    required?: boolean\n  ): JSTypeOf[T] | null {\n    const element = this.getElement(name);\n    if (element == null) {\n      if (required === true) {\n        throw new BSONError(`BSON element \"${name}\" is missing`);\n      } else {\n        return null;\n      }\n    }\n\n    if (element.value == null) {\n      const value = this.toJSValue(element.element, as);\n      if (value == null) {\n        if (required === true) {\n          throw new BSONError(`BSON element \"${name}\" is missing`);\n        } else {\n          return null;\n        }\n      }\n      // It is important to never store null\n      element.value = value;\n    }\n\n    return element.value;\n  }\n\n  /**\n   * Supports returning int, double, long, and bool as javascript numbers\n   *\n   * @remarks\n   * **NOTE:**\n   * - Use this _only_ when you believe the potential precision loss of an int64 is acceptable\n   * - This method does not cache the result as Longs or booleans would be stored incorrectly\n   *\n   * @param name - element name\n   * @param required - throws if name does not exist\n   */\n  public getNumber<const Req extends boolean = false>(\n    name: string,\n    required?: Req\n  ): Req extends true ? number : number | null;\n  public getNumber(name: string, required: boolean): number | null {\n    const maybeBool = this.get(name, BSONType.bool);\n    const bool = maybeBool == null ? null : maybeBool ? 1 : 0;\n\n    const maybeLong = this.get(name, BSONType.long);\n    const long = maybeLong == null ? null : Number(maybeLong);\n\n    const result = bool ?? long ?? this.get(name, BSONType.int) ?? this.get(name, BSONType.double);\n\n    if (required === true && result == null) {\n      throw new BSONError(`BSON element \"${name}\" is missing`);\n    }\n\n    return result;\n  }\n\n  /**\n   * Deserialize this object, DOES NOT cache result so avoid multiple invocations\n   * @param options - BSON deserialization options\n   */\n  public toObject(options?: OnDemandDocumentDeserializeOptions): Record<string, any> {\n    return deserialize(this.bson, {\n      ...options,\n      index: this.offset,\n      allowObjectSmallerThanBufferSize: true\n    });\n  }\n\n  /** Returns this document's bytes only */\n  toBytes() {\n    const size = getInt32LE(this.bson, this.offset);\n    return this.bson.subarray(this.offset, this.offset + size);\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,MAAA,GAAAC,OAAA;AAqDA;AACA,MAAaC,gBAAgB;EAgB3BC,YACE;EACmBC,IAAgB,EACnC;EACiBC,MAAA,GAAS,CAAC,EAC3B;EACgBC,OAAA,GAAU,KAAK,EAC/B;EACAC,QAAwB;IANL,KAAAH,IAAI,GAAJA,IAAI;IAEN,KAAAC,MAAM,GAANA,MAAM;IAEP,KAAAC,OAAO,GAAPA,OAAO;IArBzB;;;;;;;IAOiB,KAAAE,KAAK,GACpBC,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IACrB;IACiB,KAAAC,UAAU,GAA4BF,MAAM,CAACC,MAAM,CAAC,IAAI,CAAC;IAexE,IAAI,CAACH,QAAQ,GAAGA,QAAQ,IAAI,IAAAP,MAAA,CAAAY,sBAAsB,EAAC,IAAI,CAACR,IAAI,EAAEC,MAAM,CAAC;EACvE;EAEA;EACQQ,aAAaA,CAACC,IAAY,EAAEC,OAAoB;IACtD,MAAMC,UAAU,GAAGD,OAAO,sCAA8B;IACxD,MAAME,UAAU,GAAGF,OAAO,sCAA8B;IAExD,IAAID,IAAI,CAACI,MAAM,KAAKF,UAAU,EAAE,OAAO,KAAK;IAE5C,MAAMG,OAAO,GAAGF,UAAU,GAAGD,UAAU;IACvC,KACE,IAAII,SAAS,GAAGH,UAAU,EAAEI,SAAS,GAAG,CAAC,EACzCA,SAAS,GAAGP,IAAI,CAACI,MAAM,IAAIE,SAAS,GAAGD,OAAO,EAC9CE,SAAS,EAAE,EAAED,SAAS,EAAE,EACxB;MACA,IAAI,IAAI,CAAChB,IAAI,CAACgB,SAAS,CAAC,KAAKN,IAAI,CAACQ,UAAU,CAACD,SAAS,CAAC,EAAE,OAAO,KAAK;IACvE;IAEA,OAAO,IAAI;EACb;EAEA;;;;;;;;;;;;EAYQE,UAAUA,CAACT,IAAqB;IACtC,MAAMU,aAAa,GAAG,IAAI,CAAChB,KAAK,CAACM,IAAI,CAAC;IACtC,IAAIU,aAAa,KAAK,KAAK,EAAE,OAAO,IAAI;IAExC,IAAIA,aAAa,IAAI,IAAI,EAAE;MACzB,OAAOA,aAAa;IACtB;IAEA,IAAI,OAAOV,IAAI,KAAK,QAAQ,EAAE;MAC5B,IAAI,IAAI,CAACR,OAAO,EAAE;QAChB,IAAIQ,IAAI,GAAG,IAAI,CAACP,QAAQ,CAACW,MAAM,EAAE;UAC/B,MAAMH,OAAO,GAAG,IAAI,CAACR,QAAQ,CAACO,IAAI,CAAC;UACnC,MAAMU,aAAa,GAAG;YAAET,OAAO;YAAEU,KAAK,EAAEC;UAAS,CAAE;UACnD,IAAI,CAAClB,KAAK,CAACM,IAAI,CAAC,GAAGU,aAAa;UAChC,IAAI,CAACb,UAAU,CAACG,IAAI,CAAC,GAAG,IAAI;UAC5B,OAAOU,aAAa;QACtB,CAAC,MAAM;UACL,OAAO,IAAI;QACb;MACF,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF;IAEA,KAAK,IAAIG,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAACpB,QAAQ,CAACW,MAAM,EAAES,KAAK,EAAE,EAAE;MACzD,MAAMZ,OAAO,GAAG,IAAI,CAACR,QAAQ,CAACoB,KAAK,CAAC;MAEpC;MACA,IAAI,EAAEA,KAAK,IAAI,IAAI,CAAChB,UAAU,CAAC,IAAI,IAAI,CAACE,aAAa,CAACC,IAAI,EAAEC,OAAO,CAAC,EAAE;QACpE,MAAMS,aAAa,GAAG;UAAET,OAAO;UAAEU,KAAK,EAAEC;QAAS,CAAE;QACnD,IAAI,CAAClB,KAAK,CAACM,IAAI,CAAC,GAAGU,aAAa;QAChC,IAAI,CAACb,UAAU,CAACgB,KAAK,CAAC,GAAG,IAAI;QAC7B,OAAOH,aAAa;MACtB;IACF;IAEA,IAAI,CAAChB,KAAK,CAACM,IAAI,CAAC,GAAG,KAAK;IACxB,OAAO,IAAI;EACb;EAcQc,SAASA,CAACb,OAAoB,EAAEc,EAAkB;IACxD,MAAMC,IAAI,GAAGf,OAAO,gCAAwB;IAC5C,MAAMV,MAAM,GAAGU,OAAO,kCAA0B;IAChD,MAAMG,MAAM,GAAGH,OAAO,kCAA0B;IAEhD,IAAIc,EAAE,KAAKC,IAAI,EAAE;MACf,OAAO,IAAI;IACb;IAEA,QAAQD,EAAE;MACR,KAAK7B,MAAA,CAAA+B,QAAQ,CAACC,IAAI;MAClB,KAAKhC,MAAA,CAAA+B,QAAQ,CAACL,SAAS;QACrB,OAAO,IAAI;MACb,KAAK1B,MAAA,CAAA+B,QAAQ,CAACE,MAAM;QAClB,OAAO,IAAAjC,MAAA,CAAAkC,YAAY,EAAC,IAAI,CAAC9B,IAAI,EAAEC,MAAM,CAAC;MACxC,KAAKL,MAAA,CAAA+B,QAAQ,CAACI,GAAG;QACf,OAAO,IAAAnC,MAAA,CAAAoC,UAAU,EAAC,IAAI,CAAChC,IAAI,EAAEC,MAAM,CAAC;MACtC,KAAKL,MAAA,CAAA+B,QAAQ,CAACM,IAAI;QAChB,OAAO,IAAArC,MAAA,CAAAsC,aAAa,EAAC,IAAI,CAAClC,IAAI,EAAEC,MAAM,CAAC;MACzC,KAAKL,MAAA,CAAA+B,QAAQ,CAACQ,IAAI;QAChB,OAAOC,OAAO,CAAC,IAAI,CAACpC,IAAI,CAACC,MAAM,CAAC,CAAC;MACnC,KAAKL,MAAA,CAAA+B,QAAQ,CAACU,QAAQ;QACpB,OAAO,IAAIzC,MAAA,CAAA0C,QAAQ,CAAC,IAAI,CAACtC,IAAI,CAACuC,QAAQ,CAACtC,MAAM,EAAEA,MAAM,GAAG,EAAE,CAAC,CAAC;MAC9D,KAAKL,MAAA,CAAA+B,QAAQ,CAACa,SAAS;QACrB,OAAO,IAAI5C,MAAA,CAAA6C,SAAS,CAAC,IAAA7C,MAAA,CAAAsC,aAAa,EAAC,IAAI,CAAClC,IAAI,EAAEC,MAAM,CAAC,CAAC;MACxD,KAAKL,MAAA,CAAA+B,QAAQ,CAACe,MAAM;QAClB,OAAO,IAAA9C,MAAA,CAAA+C,MAAM,EAAC,IAAI,CAAC3C,IAAI,EAAEC,MAAM,GAAG,CAAC,EAAEA,MAAM,GAAGa,MAAM,GAAG,CAAC,EAAE,KAAK,CAAC;MAClE,KAAKlB,MAAA,CAAA+B,QAAQ,CAACiB,OAAO;QAAE;UACrB,MAAMC,eAAe,GAAG,IAAAjD,MAAA,CAAAoC,UAAU,EAAC,IAAI,CAAChC,IAAI,EAAEC,MAAM,CAAC;UACrD,MAAM6C,OAAO,GAAG,IAAI,CAAC9C,IAAI,CAACC,MAAM,GAAG,CAAC,CAAC;UAErC,IAAI6C,OAAO,KAAK,CAAC,EAAE;YACjB,MAAMC,kBAAkB,GAAG,IAAAnD,MAAA,CAAAoC,UAAU,EAAC,IAAI,CAAChC,IAAI,EAAEC,MAAM,GAAG,CAAC,GAAG,CAAC,CAAC;YAChE,IAAI8C,kBAAkB,GAAG,CAAC,EACxB,MAAM,IAAInD,MAAA,CAAAoD,SAAS,CAAC,0DAA0D,CAAC;YACjF,IAAID,kBAAkB,GAAGF,eAAe,GAAG,CAAC,EAC1C,MAAM,IAAIjD,MAAA,CAAAoD,SAAS,CAAC,6DAA6D,CAAC;YACpF,IAAID,kBAAkB,GAAGF,eAAe,GAAG,CAAC,EAC1C,MAAM,IAAIjD,MAAA,CAAAoD,SAAS,CAAC,8DAA8D,CAAC;YACrF,OAAO,IAAIpD,MAAA,CAAAqD,MAAM,CACf,IAAI,CAACjD,IAAI,CAACuC,QAAQ,CAACtC,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,EAAEA,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG,CAAC,GAAG8C,kBAAkB,CAAC,EAC/E,CAAC,CACF;UACH;UAEA,OAAO,IAAInD,MAAA,CAAAqD,MAAM,CACf,IAAI,CAACjD,IAAI,CAACuC,QAAQ,CAACtC,MAAM,GAAG,CAAC,GAAG,CAAC,EAAEA,MAAM,GAAG,CAAC,GAAG,CAAC,GAAG4C,eAAe,CAAC,EACpEC,OAAO,CACR;QACH;MACA,KAAKlD,MAAA,CAAA+B,QAAQ,CAACuB,IAAI;QAChB;QACA,OAAO,IAAIC,IAAI,CAACC,MAAM,CAAC,IAAAxD,MAAA,CAAAsC,aAAa,EAAC,IAAI,CAAClC,IAAI,EAAEC,MAAM,CAAC,CAAC,CAAC;MAE3D,KAAKL,MAAA,CAAA+B,QAAQ,CAAC0B,MAAM;QAClB,OAAO,IAAIvD,gBAAgB,CAAC,IAAI,CAACE,IAAI,EAAEC,MAAM,CAAC;MAChD,KAAKL,MAAA,CAAA+B,QAAQ,CAAC2B,KAAK;QACjB,OAAO,IAAIxD,gBAAgB,CAAC,IAAI,CAACE,IAAI,EAAEC,MAAM,EAAE,IAAI,CAAC;MAEtD;QACE,MAAM,IAAIL,MAAA,CAAAoD,SAAS,CAAC,0BAA0BvB,EAAE,EAAE,CAAC;IACvD;EACF;EAEA;;;EAGO8B,IAAIA,CAAA;IACT,OAAO,IAAI,CAACpD,QAAQ,CAACW,MAAM;EAC7B;EAEA;;;;;;;;;EASO0C,GAAGA,CAAC9C,IAAY;IACrB,MAAMU,aAAa,GAAG,IAAI,CAAChB,KAAK,CAACM,IAAI,CAAC;IACtC,IAAIU,aAAa,KAAK,KAAK,EAAE,OAAO,KAAK;IACzC,IAAIA,aAAa,IAAI,IAAI,EAAE,OAAO,IAAI;IACtC,OAAO,IAAI,CAACD,UAAU,CAACT,IAAI,CAAC,IAAI,IAAI;EACtC;EAuBO+C,GAAGA,CACR/C,IAAqB,EACrBe,EAAK,EACLiC,QAAkB;IAElB,MAAM/C,OAAO,GAAG,IAAI,CAACQ,UAAU,CAACT,IAAI,CAAC;IACrC,IAAIC,OAAO,IAAI,IAAI,EAAE;MACnB,IAAI+C,QAAQ,KAAK,IAAI,EAAE;QACrB,MAAM,IAAI9D,MAAA,CAAAoD,SAAS,CAAC,iBAAiBtC,IAAI,cAAc,CAAC;MAC1D,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF;IAEA,IAAIC,OAAO,CAACU,KAAK,IAAI,IAAI,EAAE;MACzB,MAAMA,KAAK,GAAG,IAAI,CAACG,SAAS,CAACb,OAAO,CAACA,OAAO,EAAEc,EAAE,CAAC;MACjD,IAAIJ,KAAK,IAAI,IAAI,EAAE;QACjB,IAAIqC,QAAQ,KAAK,IAAI,EAAE;UACrB,MAAM,IAAI9D,MAAA,CAAAoD,SAAS,CAAC,iBAAiBtC,IAAI,cAAc,CAAC;QAC1D,CAAC,MAAM;UACL,OAAO,IAAI;QACb;MACF;MACA;MACAC,OAAO,CAACU,KAAK,GAAGA,KAAK;IACvB;IAEA,OAAOV,OAAO,CAACU,KAAK;EACtB;EAiBOsC,SAASA,CAACjD,IAAY,EAAEgD,QAAiB;IAC9C,MAAME,SAAS,GAAG,IAAI,CAACH,GAAG,CAAC/C,IAAI,EAAEd,MAAA,CAAA+B,QAAQ,CAACQ,IAAI,CAAC;IAC/C,MAAMA,IAAI,GAAGyB,SAAS,IAAI,IAAI,GAAG,IAAI,GAAGA,SAAS,GAAG,CAAC,GAAG,CAAC;IAEzD,MAAMC,SAAS,GAAG,IAAI,CAACJ,GAAG,CAAC/C,IAAI,EAAEd,MAAA,CAAA+B,QAAQ,CAACM,IAAI,CAAC;IAC/C,MAAMA,IAAI,GAAG4B,SAAS,IAAI,IAAI,GAAG,IAAI,GAAGT,MAAM,CAACS,SAAS,CAAC;IAEzD,MAAMC,MAAM,GAAG3B,IAAI,IAAIF,IAAI,IAAI,IAAI,CAACwB,GAAG,CAAC/C,IAAI,EAAEd,MAAA,CAAA+B,QAAQ,CAACI,GAAG,CAAC,IAAI,IAAI,CAAC0B,GAAG,CAAC/C,IAAI,EAAEd,MAAA,CAAA+B,QAAQ,CAACE,MAAM,CAAC;IAE9F,IAAI6B,QAAQ,KAAK,IAAI,IAAII,MAAM,IAAI,IAAI,EAAE;MACvC,MAAM,IAAIlE,MAAA,CAAAoD,SAAS,CAAC,iBAAiBtC,IAAI,cAAc,CAAC;IAC1D;IAEA,OAAOoD,MAAM;EACf;EAEA;;;;EAIOC,QAAQA,CAACC,OAA4C;IAC1D,OAAO,IAAApE,MAAA,CAAAqE,WAAW,EAAC,IAAI,CAACjE,IAAI,EAAE;MAC5B,GAAGgE,OAAO;MACVzC,KAAK,EAAE,IAAI,CAACtB,MAAM;MAClBiE,gCAAgC,EAAE;KACnC,CAAC;EACJ;EAEA;EACAC,OAAOA,CAAA;IACL,MAAMZ,IAAI,GAAG,IAAA3D,MAAA,CAAAoC,UAAU,EAAC,IAAI,CAAChC,IAAI,EAAE,IAAI,CAACC,MAAM,CAAC;IAC/C,OAAO,IAAI,CAACD,IAAI,CAACuC,QAAQ,CAAC,IAAI,CAACtC,MAAM,EAAE,IAAI,CAACA,MAAM,GAAGsD,IAAI,CAAC;EAC5D;;AAzSFa,OAAA,CAAAtE,gBAAA,GAAAA,gBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}