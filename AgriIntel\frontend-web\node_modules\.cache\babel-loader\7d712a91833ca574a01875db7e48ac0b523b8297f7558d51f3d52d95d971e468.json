{"ast": null, "code": "export default function pairs(values, pairof = pair) {\n  const pairs = [];\n  let previous;\n  let first = false;\n  for (const value of values) {\n    if (first) pairs.push(pairof(previous, value));\n    previous = value;\n    first = true;\n  }\n  return pairs;\n}\nexport function pair(a, b) {\n  return [a, b];\n}", "map": {"version": 3, "names": ["pairs", "values", "pairof", "pair", "previous", "first", "value", "push", "a", "b"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/d3-array/src/pairs.js"], "sourcesContent": ["export default function pairs(values, pairof = pair) {\n  const pairs = [];\n  let previous;\n  let first = false;\n  for (const value of values) {\n    if (first) pairs.push(pairof(previous, value));\n    previous = value;\n    first = true;\n  }\n  return pairs;\n}\n\nexport function pair(a, b) {\n  return [a, b];\n}\n"], "mappings": "AAAA,eAAe,SAASA,KAAKA,CAACC,MAAM,EAAEC,MAAM,GAAGC,IAAI,EAAE;EACnD,MAAMH,KAAK,GAAG,EAAE;EAChB,IAAII,QAAQ;EACZ,IAAIC,KAAK,GAAG,KAAK;EACjB,KAAK,MAAMC,KAAK,IAAIL,MAAM,EAAE;IAC1B,IAAII,KAAK,EAAEL,KAAK,CAACO,IAAI,CAACL,MAAM,CAACE,QAAQ,EAAEE,KAAK,CAAC,CAAC;IAC9CF,QAAQ,GAAGE,KAAK;IAChBD,KAAK,GAAG,IAAI;EACd;EACA,OAAOL,KAAK;AACd;AAEA,OAAO,SAASG,IAAIA,CAACK,CAAC,EAAEC,CAAC,EAAE;EACzB,OAAO,CAACD,CAAC,EAAEC,CAAC,CAAC;AACf", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}