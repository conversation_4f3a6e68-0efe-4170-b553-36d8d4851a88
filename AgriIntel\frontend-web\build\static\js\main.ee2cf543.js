/*! For license information please see main.ee2cf543.js.LICENSE.txt */
(()=>{var e={43:(e,t,n)=>{"use strict";e.exports=n(202)},153:(e,t,n)=>{"use strict";var r=n(43),o=Symbol.for("react.element"),a=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,s=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,a={},u=null,c=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)i.call(t,r)&&!l.hasOwnProperty(r)&&(a[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===a[r]&&(a[r]=t[r]);return{$$typeof:o,type:e,key:u,ref:c,props:a,_owner:s.current}}t.jsx=u,t.jsxs=u},202:(e,t)=>{"use strict";var n=Symbol.for("react.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),i=Symbol.for("react.profiler"),s=Symbol.for("react.provider"),l=Symbol.for("react.context"),u=Symbol.for("react.forward_ref"),c=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var h={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},g=Object.assign,m={};function y(e,t,n){this.props=e,this.context=t,this.refs=m,this.updater=n||h}function v(){}function b(e,t,n){this.props=e,this.context=t,this.refs=m,this.updater=n||h}y.prototype.isReactComponent={},y.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},y.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},v.prototype=y.prototype;var w=b.prototype=new v;w.constructor=b,g(w,y.prototype),w.isPureReactComponent=!0;var k=Array.isArray,S=Object.prototype.hasOwnProperty,x={current:null},E={key:!0,ref:!0,__self:!0,__source:!0};function C(e,t,r){var o,a={},i=null,s=null;if(null!=t)for(o in void 0!==t.ref&&(s=t.ref),void 0!==t.key&&(i=""+t.key),t)S.call(t,o)&&!E.hasOwnProperty(o)&&(a[o]=t[o]);var l=arguments.length-2;if(1===l)a.children=r;else if(1<l){for(var u=Array(l),c=0;c<l;c++)u[c]=arguments[c+2];a.children=u}if(e&&e.defaultProps)for(o in l=e.defaultProps)void 0===a[o]&&(a[o]=l[o]);return{$$typeof:n,type:e,key:i,ref:s,props:a,_owner:x.current}}function P(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var O=/\/+/g;function R(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function T(e,t,o,a,i){var s=typeof e;"undefined"!==s&&"boolean"!==s||(e=null);var l=!1;if(null===e)l=!0;else switch(s){case"string":case"number":l=!0;break;case"object":switch(e.$$typeof){case n:case r:l=!0}}if(l)return i=i(l=e),e=""===a?"."+R(l,0):a,k(i)?(o="",null!=e&&(o=e.replace(O,"$&/")+"/"),T(i,t,o,"",(function(e){return e}))):null!=i&&(P(i)&&(i=function(e,t){return{$$typeof:n,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}(i,o+(!i.key||l&&l.key===i.key?"":(""+i.key).replace(O,"$&/")+"/")+e)),t.push(i)),1;if(l=0,a=""===a?".":a+":",k(e))for(var u=0;u<e.length;u++){var c=a+R(s=e[u],u);l+=T(s,t,o,c,i)}else if(c=function(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=p&&e[p]||e["@@iterator"])?e:null}(e),"function"===typeof c)for(e=c.call(e),u=0;!(s=e.next()).done;)l+=T(s=s.value,t,o,c=a+R(s,u++),i);else if("object"===s)throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return l}function N(e,t,n){if(null==e)return e;var r=[],o=0;return T(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function A(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var L={current:null},_={transition:null},j={ReactCurrentDispatcher:L,ReactCurrentBatchConfig:_,ReactCurrentOwner:x};function D(){throw Error("act(...) is not supported in production builds of React.")}t.Children={map:N,forEach:function(e,t,n){N(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return N(e,(function(){t++})),t},toArray:function(e){return N(e,(function(e){return e}))||[]},only:function(e){if(!P(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=y,t.Fragment=o,t.Profiler=i,t.PureComponent=b,t.StrictMode=a,t.Suspense=c,t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=j,t.act=D,t.cloneElement=function(e,t,r){if(null===e||void 0===e)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var o=g({},e.props),a=e.key,i=e.ref,s=e._owner;if(null!=t){if(void 0!==t.ref&&(i=t.ref,s=x.current),void 0!==t.key&&(a=""+t.key),e.type&&e.type.defaultProps)var l=e.type.defaultProps;for(u in t)S.call(t,u)&&!E.hasOwnProperty(u)&&(o[u]=void 0===t[u]&&void 0!==l?l[u]:t[u])}var u=arguments.length-2;if(1===u)o.children=r;else if(1<u){l=Array(u);for(var c=0;c<u;c++)l[c]=arguments[c+2];o.children=l}return{$$typeof:n,type:e.type,key:a,ref:i,props:o,_owner:s}},t.createContext=function(e){return(e={$$typeof:l,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null}).Provider={$$typeof:s,_context:e},e.Consumer=e},t.createElement=C,t.createFactory=function(e){var t=C.bind(null,e);return t.type=e,t},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:u,render:e}},t.isValidElement=P,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:A}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=_.transition;_.transition={};try{e()}finally{_.transition=t}},t.unstable_act=D,t.useCallback=function(e,t){return L.current.useCallback(e,t)},t.useContext=function(e){return L.current.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e){return L.current.useDeferredValue(e)},t.useEffect=function(e,t){return L.current.useEffect(e,t)},t.useId=function(){return L.current.useId()},t.useImperativeHandle=function(e,t,n){return L.current.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return L.current.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return L.current.useLayoutEffect(e,t)},t.useMemo=function(e,t){return L.current.useMemo(e,t)},t.useReducer=function(e,t,n){return L.current.useReducer(e,t,n)},t.useRef=function(e){return L.current.useRef(e)},t.useState=function(e){return L.current.useState(e)},t.useSyncExternalStore=function(e,t,n){return L.current.useSyncExternalStore(e,t,n)},t.useTransition=function(){return L.current.useTransition()},t.version="18.3.1"},214:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r});const r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Number.MIN_SAFE_INTEGER,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:Number.MAX_SAFE_INTEGER;return Math.max(t,Math.min(e,n))}},219:(e,t,n)=>{"use strict";var r=n(763),o={childContextTypes:!0,contextType:!0,contextTypes:!0,defaultProps:!0,displayName:!0,getDefaultProps:!0,getDerivedStateFromError:!0,getDerivedStateFromProps:!0,mixins:!0,propTypes:!0,type:!0},a={name:!0,length:!0,prototype:!0,caller:!0,callee:!0,arguments:!0,arity:!0},i={$$typeof:!0,compare:!0,defaultProps:!0,displayName:!0,propTypes:!0,type:!0},s={};function l(e){return r.isMemo(e)?i:s[e.$$typeof]||o}s[r.ForwardRef]={$$typeof:!0,render:!0,defaultProps:!0,displayName:!0,propTypes:!0},s[r.Memo]=i;var u=Object.defineProperty,c=Object.getOwnPropertyNames,d=Object.getOwnPropertySymbols,f=Object.getOwnPropertyDescriptor,p=Object.getPrototypeOf,h=Object.prototype;e.exports=function e(t,n,r){if("string"!==typeof n){if(h){var o=p(n);o&&o!==h&&e(t,o,r)}var i=c(n);d&&(i=i.concat(d(n)));for(var s=l(t),g=l(n),m=0;m<i.length;++m){var y=i[m];if(!a[y]&&(!r||!r[y])&&(!g||!g[y])&&(!s||!s[y])){var v=f(n,y);try{u(t,y,v)}catch(b){}}}}return t}},234:(e,t)=>{"use strict";function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,o=e[r];if(!(0<a(o,t)))break e;e[r]=t,e[n]=o,n=r}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,o=e.length,i=o>>>1;r<i;){var s=2*(r+1)-1,l=e[s],u=s+1,c=e[u];if(0>a(l,n))u<o&&0>a(c,l)?(e[r]=c,e[u]=n,r=u):(e[r]=l,e[s]=n,r=s);else{if(!(u<o&&0>a(c,n)))break e;e[r]=c,e[u]=n,r=u}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if("object"===typeof performance&&"function"===typeof performance.now){var i=performance;t.unstable_now=function(){return i.now()}}else{var s=Date,l=s.now();t.unstable_now=function(){return s.now()-l}}var u=[],c=[],d=1,f=null,p=3,h=!1,g=!1,m=!1,y="function"===typeof setTimeout?setTimeout:null,v="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function w(e){for(var t=r(c);null!==t;){if(null===t.callback)o(c);else{if(!(t.startTime<=e))break;o(c),t.sortIndex=t.expirationTime,n(u,t)}t=r(c)}}function k(e){if(m=!1,w(e),!g)if(null!==r(u))g=!0,_(S);else{var t=r(c);null!==t&&j(k,t.startTime-e)}}function S(e,n){g=!1,m&&(m=!1,v(P),P=-1),h=!0;var a=p;try{for(w(n),f=r(u);null!==f&&(!(f.expirationTime>n)||e&&!T());){var i=f.callback;if("function"===typeof i){f.callback=null,p=f.priorityLevel;var s=i(f.expirationTime<=n);n=t.unstable_now(),"function"===typeof s?f.callback=s:f===r(u)&&o(u),w(n)}else o(u);f=r(u)}if(null!==f)var l=!0;else{var d=r(c);null!==d&&j(k,d.startTime-n),l=!1}return l}finally{f=null,p=a,h=!1}}"undefined"!==typeof navigator&&void 0!==navigator.scheduling&&void 0!==navigator.scheduling.isInputPending&&navigator.scheduling.isInputPending.bind(navigator.scheduling);var x,E=!1,C=null,P=-1,O=5,R=-1;function T(){return!(t.unstable_now()-R<O)}function N(){if(null!==C){var e=t.unstable_now();R=e;var n=!0;try{n=C(!0,e)}finally{n?x():(E=!1,C=null)}}else E=!1}if("function"===typeof b)x=function(){b(N)};else if("undefined"!==typeof MessageChannel){var A=new MessageChannel,L=A.port2;A.port1.onmessage=N,x=function(){L.postMessage(null)}}else x=function(){y(N,0)};function _(e){C=e,E||(E=!0,x())}function j(e,n){P=y((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){g||h||(g=!0,_(S))},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):O=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(u)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,o,a){var i=t.unstable_now();switch("object"===typeof a&&null!==a?a="number"===typeof(a=a.delay)&&0<a?i+a:i:a=i,e){case 1:var s=-1;break;case 2:s=250;break;case 5:s=1073741823;break;case 4:s=1e4;break;default:s=5e3}return e={id:d++,callback:o,priorityLevel:e,startTime:a,expirationTime:s=a+s,sortIndex:-1},a>i?(e.sortIndex=a,n(c,e),null===r(u)&&e===r(c)&&(m?(v(P),P=-1):m=!0,j(k,a-i))):(e.sortIndex=s,n(u,e),g||h||(g=!0,_(S))),e},t.unstable_shouldYield=T,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},266:(e,t,n)=>{"use strict";var r=n(994);t.e$=h,t.eM=function(e,t){const n=f(e),r=f(t);return(Math.max(n,r)+.05)/(Math.min(n,r)+.05)},t.a=g;var o=r(n(457)),a=r(n(214));function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:1;return(0,a.default)(e,t,n)}function s(e){e=e.slice(1);const t=new RegExp(".{1,".concat(e.length>=6?2:1,"}"),"g");let n=e.match(t);return n&&1===n[0].length&&(n=n.map((e=>e+e))),n?"rgb".concat(4===n.length?"a":"","(").concat(n.map(((e,t)=>t<3?parseInt(e,16):Math.round(parseInt(e,16)/255*1e3)/1e3)).join(", "),")"):""}function l(e){if(e.type)return e;if("#"===e.charAt(0))return l(s(e));const t=e.indexOf("("),n=e.substring(0,t);if(-1===["rgb","rgba","hsl","hsla","color"].indexOf(n))throw new Error((0,o.default)(9,e));let r,a=e.substring(t+1,e.length-1);if("color"===n){if(a=a.split(" "),r=a.shift(),4===a.length&&"/"===a[3].charAt(0)&&(a[3]=a[3].slice(1)),-1===["srgb","display-p3","a98-rgb","prophoto-rgb","rec-2020"].indexOf(r))throw new Error((0,o.default)(10,r))}else a=a.split(",");return a=a.map((e=>parseFloat(e))),{type:n,values:a,colorSpace:r}}const u=e=>{const t=l(e);return t.values.slice(0,3).map(((e,n)=>-1!==t.type.indexOf("hsl")&&0!==n?"".concat(e,"%"):e)).join(" ")};function c(e){const{type:t,colorSpace:n}=e;let{values:r}=e;return-1!==t.indexOf("rgb")?r=r.map(((e,t)=>t<3?parseInt(e,10):e)):-1!==t.indexOf("hsl")&&(r[1]="".concat(r[1],"%"),r[2]="".concat(r[2],"%")),r=-1!==t.indexOf("color")?"".concat(n," ").concat(r.join(" ")):"".concat(r.join(", ")),"".concat(t,"(").concat(r,")")}function d(e){e=l(e);const{values:t}=e,n=t[0],r=t[1]/100,o=t[2]/100,a=r*Math.min(o,1-o),i=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:(e+n/30)%12;return o-a*Math.max(Math.min(t-3,9-t,1),-1)};let s="rgb";const u=[Math.round(255*i(0)),Math.round(255*i(8)),Math.round(255*i(4))];return"hsla"===e.type&&(s+="a",u.push(t[3])),c({type:s,values:u})}function f(e){let t="hsl"===(e=l(e)).type||"hsla"===e.type?l(d(e)).values:e.values;return t=t.map((t=>("color"!==e.type&&(t/=255),t<=.03928?t/12.92:((t+.055)/1.055)**2.4))),Number((.2126*t[0]+.7152*t[1]+.0722*t[2]).toFixed(3))}function p(e,t){return e=l(e),t=i(t),"rgb"!==e.type&&"hsl"!==e.type||(e.type+="a"),"color"===e.type?e.values[3]="/".concat(t):e.values[3]=t,c(e)}function h(e,t){if(e=l(e),t=i(t),-1!==e.type.indexOf("hsl"))e.values[2]*=1-t;else if(-1!==e.type.indexOf("rgb")||-1!==e.type.indexOf("color"))for(let n=0;n<3;n+=1)e.values[n]*=1-t;return c(e)}function g(e,t){if(e=l(e),t=i(t),-1!==e.type.indexOf("hsl"))e.values[2]+=(100-e.values[2])*t;else if(-1!==e.type.indexOf("rgb"))for(let n=0;n<3;n+=1)e.values[n]+=(255-e.values[n])*t;else if(-1!==e.type.indexOf("color"))for(let n=0;n<3;n+=1)e.values[n]+=(1-e.values[n])*t;return c(e)}function m(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:.15;return f(e)>.5?h(e,t):g(e,t)}},330:(e,t,n)=>{"use strict";var r=n(43);var o="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},a=r.useState,i=r.useEffect,s=r.useLayoutEffect,l=r.useDebugValue;function u(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(r){return!0}}var c="undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=a({inst:{value:n,getSnapshot:t}}),o=r[0].inst,c=r[1];return s((function(){o.value=n,o.getSnapshot=t,u(o)&&c({inst:o})}),[e,n,t]),i((function(){return u(o)&&c({inst:o}),e((function(){u(o)&&c({inst:o})}))}),[e]),l(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},358:(e,t)=>{"use strict";var n,r=Symbol.for("react.element"),o=Symbol.for("react.portal"),a=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),s=Symbol.for("react.profiler"),l=Symbol.for("react.provider"),u=Symbol.for("react.context"),c=Symbol.for("react.server_context"),d=Symbol.for("react.forward_ref"),f=Symbol.for("react.suspense"),p=Symbol.for("react.suspense_list"),h=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),m=Symbol.for("react.offscreen");function y(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case a:case s:case i:case f:case p:return e;default:switch(e=e&&e.$$typeof){case c:case u:case d:case g:case h:case l:return e;default:return t}}case o:return t}}}n=Symbol.for("react.module.reference")},391:(e,t,n)=>{"use strict";var r=n(950);t.createRoot=r.createRoot,t.hydrateRoot=r.hydrateRoot},443:(e,t,n)=>{"use strict";e.exports=n(717)},457:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>r.A});var r=n(868)},461:(e,t,n)=>{"use strict";e.exports=n(330)},579:(e,t,n)=>{"use strict";e.exports=n(153)},706:(e,t,n)=>{"use strict";n(358)},717:(e,t,n)=>{"use strict";var r=n(43),o=n(461);var a="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t},i=o.useSyncExternalStore,s=r.useRef,l=r.useEffect,u=r.useMemo,c=r.useDebugValue;t.useSyncExternalStoreWithSelector=function(e,t,n,r,o){var d=s(null);if(null===d.current){var f={hasValue:!1,value:null};d.current=f}else f=d.current;d=u((function(){function e(e){if(!l){if(l=!0,i=e,e=r(e),void 0!==o&&f.hasValue){var t=f.value;if(o(t,e))return s=t}return s=e}if(t=s,a(i,e))return t;var n=r(e);return void 0!==o&&o(t,n)?(i=e,t):(i=e,s=n)}var i,s,l=!1,u=void 0===n?null:n;return[function(){return e(t())},null===u?void 0:function(){return e(u())}]}),[t,n,r,o]);var p=i(e,d[0],d[1]);return l((function(){f.hasValue=!0,f.value=p}),[p]),c(p),p}},730:(e,t,n)=>{"use strict";var r=n(43),o=n(853);function a(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=new Set,s={};function l(e,t){u(e,t),u(e+"Capture",t)}function u(e,t){for(s[e]=t,e=0;e<t.length;e++)i.add(t[e])}var c=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),d=Object.prototype.hasOwnProperty,f=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,p={},h={};function g(e,t,n,r,o,a,i){this.acceptsBooleans=2===t||3===t||4===t,this.attributeName=r,this.attributeNamespace=o,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=a,this.removeEmptyString=i}var m={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach((function(e){m[e]=new g(e,0,!1,e,null,!1,!1)})),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach((function(e){var t=e[0];m[t]=new g(t,1,!1,e[1],null,!1,!1)})),["contentEditable","draggable","spellCheck","value"].forEach((function(e){m[e]=new g(e,2,!1,e.toLowerCase(),null,!1,!1)})),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach((function(e){m[e]=new g(e,2,!1,e,null,!1,!1)})),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach((function(e){m[e]=new g(e,3,!1,e.toLowerCase(),null,!1,!1)})),["checked","multiple","muted","selected"].forEach((function(e){m[e]=new g(e,3,!0,e,null,!1,!1)})),["capture","download"].forEach((function(e){m[e]=new g(e,4,!1,e,null,!1,!1)})),["cols","rows","size","span"].forEach((function(e){m[e]=new g(e,6,!1,e,null,!1,!1)})),["rowSpan","start"].forEach((function(e){m[e]=new g(e,5,!1,e.toLowerCase(),null,!1,!1)}));var y=/[\-:]([a-z])/g;function v(e){return e[1].toUpperCase()}function b(e,t,n,r){var o=m.hasOwnProperty(t)?m[t]:null;(null!==o?0!==o.type:r||!(2<t.length)||"o"!==t[0]&&"O"!==t[0]||"n"!==t[1]&&"N"!==t[1])&&(function(e,t,n,r){if(null===t||"undefined"===typeof t||function(e,t,n,r){if(null!==n&&0===n.type)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return!r&&(null!==n?!n.acceptsBooleans:"data-"!==(e=e.toLowerCase().slice(0,5))&&"aria-"!==e);default:return!1}}(e,t,n,r))return!0;if(r)return!1;if(null!==n)switch(n.type){case 3:return!t;case 4:return!1===t;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}(t,n,o,r)&&(n=null),r||null===o?function(e){return!!d.call(h,e)||!d.call(p,e)&&(f.test(e)?h[e]=!0:(p[e]=!0,!1))}(t)&&(null===n?e.removeAttribute(t):e.setAttribute(t,""+n)):o.mustUseProperty?e[o.propertyName]=null===n?3!==o.type&&"":n:(t=o.attributeName,r=o.attributeNamespace,null===n?e.removeAttribute(t):(n=3===(o=o.type)||4===o&&!0===n?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach((function(e){var t=e.replace(y,v);m[t]=new g(t,1,!1,e,null,!1,!1)})),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach((function(e){var t=e.replace(y,v);m[t]=new g(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)})),["xml:base","xml:lang","xml:space"].forEach((function(e){var t=e.replace(y,v);m[t]=new g(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)})),["tabIndex","crossOrigin"].forEach((function(e){m[e]=new g(e,1,!1,e.toLowerCase(),null,!1,!1)})),m.xlinkHref=new g("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach((function(e){m[e]=new g(e,1,!1,e.toLowerCase(),null,!0,!0)}));var w=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,k=Symbol.for("react.element"),S=Symbol.for("react.portal"),x=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),C=Symbol.for("react.profiler"),P=Symbol.for("react.provider"),O=Symbol.for("react.context"),R=Symbol.for("react.forward_ref"),T=Symbol.for("react.suspense"),N=Symbol.for("react.suspense_list"),A=Symbol.for("react.memo"),L=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var _=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.cache"),Symbol.for("react.tracing_marker");var j=Symbol.iterator;function D(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=j&&e[j]||e["@@iterator"])?e:null}var I,F=Object.assign;function z(e){if(void 0===I)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);I=t&&t[1]||""}return"\n"+I+e}var M=!1;function B(e,t){if(!e||M)return"";M=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&"string"===typeof u.stack){for(var o=u.stack.split("\n"),a=r.stack.split("\n"),i=o.length-1,s=a.length-1;1<=i&&0<=s&&o[i]!==a[s];)s--;for(;1<=i&&0<=s;i--,s--)if(o[i]!==a[s]){if(1!==i||1!==s)do{if(i--,0>--s||o[i]!==a[s]){var l="\n"+o[i].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}}while(1<=i&&0<=s);break}}}finally{M=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?z(e):""}function U(e){switch(e.tag){case 5:return z(e.type);case 16:return z("Lazy");case 13:return z("Suspense");case 19:return z("SuspenseList");case 0:case 2:case 15:return e=B(e.type,!1);case 11:return e=B(e.type.render,!1);case 1:return e=B(e.type,!0);default:return""}}function V(e){if(null==e)return null;if("function"===typeof e)return e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case x:return"Fragment";case S:return"Portal";case C:return"Profiler";case E:return"StrictMode";case T:return"Suspense";case N:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case O:return(e.displayName||"Context")+".Consumer";case P:return(e._context.displayName||"Context")+".Provider";case R:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case A:return null!==(t=e.displayName||null)?t:V(e.type)||"Memo";case L:t=e._payload,e=e._init;try{return V(e(t))}catch(n){}}return null}function W(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=(e=t.render).displayName||e.name||"",t.displayName||(""!==e?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return V(t);case 8:return t===E?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if("function"===typeof t)return t.displayName||t.name||null;if("string"===typeof t)return t}return null}function H(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function $(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function K(e){e._valueTracker||(e._valueTracker=function(e){var t=$(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var o=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function q(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=$(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function Q(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}function G(e,t){var n=t.checked;return F({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:null!=n?n:e._wrapperState.initialChecked})}function J(e,t){var n=null==t.defaultValue?"":t.defaultValue,r=null!=t.checked?t.checked:t.defaultChecked;n=H(null!=t.value?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:"checkbox"===t.type||"radio"===t.type?null!=t.checked:null!=t.value}}function X(e,t){null!=(t=t.checked)&&b(e,"checked",t,!1)}function Y(e,t){X(e,t);var n=H(t.value),r=t.type;if(null!=n)"number"===r?(0===n&&""===e.value||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if("submit"===r||"reset"===r)return void e.removeAttribute("value");t.hasOwnProperty("value")?ee(e,t.type,n):t.hasOwnProperty("defaultValue")&&ee(e,t.type,H(t.defaultValue)),null==t.checked&&null!=t.defaultChecked&&(e.defaultChecked=!!t.defaultChecked)}function Z(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!("submit"!==r&&"reset"!==r||void 0!==t.value&&null!==t.value))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}""!==(n=e.name)&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,""!==n&&(e.name=n)}function ee(e,t,n){"number"===t&&Q(e.ownerDocument)===e||(null==n?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var te=Array.isArray;function ne(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+H(n),t=null,o=0;o<e.length;o++){if(e[o].value===n)return e[o].selected=!0,void(r&&(e[o].defaultSelected=!0));null!==t||e[o].disabled||(t=e[o])}null!==t&&(t.selected=!0)}}function re(e,t){if(null!=t.dangerouslySetInnerHTML)throw Error(a(91));return F({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function oe(e,t){var n=t.value;if(null==n){if(n=t.children,t=t.defaultValue,null!=n){if(null!=t)throw Error(a(92));if(te(n)){if(1<n.length)throw Error(a(93));n=n[0]}t=n}null==t&&(t=""),n=t}e._wrapperState={initialValue:H(n)}}function ae(e,t){var n=H(t.value),r=H(t.defaultValue);null!=n&&((n=""+n)!==e.value&&(e.value=n),null==t.defaultValue&&e.defaultValue!==n&&(e.defaultValue=n)),null!=r&&(e.defaultValue=""+r)}function ie(e){var t=e.textContent;t===e._wrapperState.initialValue&&""!==t&&null!==t&&(e.value=t)}function se(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function le(e,t){return null==e||"http://www.w3.org/1999/xhtml"===e?se(t):"http://www.w3.org/2000/svg"===e&&"foreignObject"===t?"http://www.w3.org/1999/xhtml":e}var ue,ce,de=(ce=function(e,t){if("http://www.w3.org/2000/svg"!==e.namespaceURI||"innerHTML"in e)e.innerHTML=t;else{for((ue=ue||document.createElement("div")).innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=ue.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}},"undefined"!==typeof MSApp&&MSApp.execUnsafeLocalFunction?function(e,t,n,r){MSApp.execUnsafeLocalFunction((function(){return ce(e,t)}))}:ce);function fe(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var pe={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},he=["Webkit","ms","Moz","O"];function ge(e,t,n){return null==t||"boolean"===typeof t||""===t?"":n||"number"!==typeof t||0===t||pe.hasOwnProperty(e)&&pe[e]?(""+t).trim():t+"px"}function me(e,t){for(var n in e=e.style,t)if(t.hasOwnProperty(n)){var r=0===n.indexOf("--"),o=ge(n,t[n],r);"float"===n&&(n="cssFloat"),r?e.setProperty(n,o):e[n]=o}}Object.keys(pe).forEach((function(e){he.forEach((function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),pe[t]=pe[e]}))}));var ye=F({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function ve(e,t){if(t){if(ye[e]&&(null!=t.children||null!=t.dangerouslySetInnerHTML))throw Error(a(137,e));if(null!=t.dangerouslySetInnerHTML){if(null!=t.children)throw Error(a(60));if("object"!==typeof t.dangerouslySetInnerHTML||!("__html"in t.dangerouslySetInnerHTML))throw Error(a(61))}if(null!=t.style&&"object"!==typeof t.style)throw Error(a(62))}}function be(e,t){if(-1===e.indexOf("-"))return"string"===typeof t.is;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var we=null;function ke(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var Se=null,xe=null,Ee=null;function Ce(e){if(e=wo(e)){if("function"!==typeof Se)throw Error(a(280));var t=e.stateNode;t&&(t=So(t),Se(e.stateNode,e.type,t))}}function Pe(e){xe?Ee?Ee.push(e):Ee=[e]:xe=e}function Oe(){if(xe){var e=xe,t=Ee;if(Ee=xe=null,Ce(e),t)for(e=0;e<t.length;e++)Ce(t[e])}}function Re(e,t){return e(t)}function Te(){}var Ne=!1;function Ae(e,t,n){if(Ne)return e(t,n);Ne=!0;try{return Re(e,t,n)}finally{Ne=!1,(null!==xe||null!==Ee)&&(Te(),Oe())}}function Le(e,t){var n=e.stateNode;if(null===n)return null;var r=So(n);if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(a(231,t,typeof n));return n}var _e=!1;if(c)try{var je={};Object.defineProperty(je,"passive",{get:function(){_e=!0}}),window.addEventListener("test",je,je),window.removeEventListener("test",je,je)}catch(ce){_e=!1}function De(e,t,n,r,o,a,i,s,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var Ie=!1,Fe=null,ze=!1,Me=null,Be={onError:function(e){Ie=!0,Fe=e}};function Ue(e,t,n,r,o,a,i,s,l){Ie=!1,Fe=null,De.apply(Be,arguments)}function Ve(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function We(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function He(e){if(Ve(e)!==e)throw Error(a(188))}function $e(e){return null!==(e=function(e){var t=e.alternate;if(!t){if(null===(t=Ve(e)))throw Error(a(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(null===o)break;var i=o.alternate;if(null===i){if(null!==(r=o.return)){n=r;continue}break}if(o.child===i.child){for(i=o.child;i;){if(i===n)return He(o),e;if(i===r)return He(o),t;i=i.sibling}throw Error(a(188))}if(n.return!==r.return)n=o,r=i;else{for(var s=!1,l=o.child;l;){if(l===n){s=!0,n=o,r=i;break}if(l===r){s=!0,r=o,n=i;break}l=l.sibling}if(!s){for(l=i.child;l;){if(l===n){s=!0,n=i,r=o;break}if(l===r){s=!0,r=i,n=o;break}l=l.sibling}if(!s)throw Error(a(189))}}if(n.alternate!==r)throw Error(a(190))}if(3!==n.tag)throw Error(a(188));return n.stateNode.current===n?e:t}(e))?Ke(e):null}function Ke(e){if(5===e.tag||6===e.tag)return e;for(e=e.child;null!==e;){var t=Ke(e);if(null!==t)return t;e=e.sibling}return null}var qe=o.unstable_scheduleCallback,Qe=o.unstable_cancelCallback,Ge=o.unstable_shouldYield,Je=o.unstable_requestPaint,Xe=o.unstable_now,Ye=o.unstable_getCurrentPriorityLevel,Ze=o.unstable_ImmediatePriority,et=o.unstable_UserBlockingPriority,tt=o.unstable_NormalPriority,nt=o.unstable_LowPriority,rt=o.unstable_IdlePriority,ot=null,at=null;var it=Math.clz32?Math.clz32:function(e){return e>>>=0,0===e?32:31-(st(e)/lt|0)|0},st=Math.log,lt=Math.LN2;var ut=64,ct=4194304;function dt(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194240&e;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return 130023424&e;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ft(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,o=e.suspendedLanes,a=e.pingedLanes,i=268435455&n;if(0!==i){var s=i&~o;0!==s?r=dt(s):0!==(a&=i)&&(r=dt(a))}else 0!==(i=n&~o)?r=dt(i):0!==a&&(r=dt(a));if(0===r)return 0;if(0!==t&&t!==r&&0===(t&o)&&((o=r&-r)>=(a=t&-t)||16===o&&0!==(4194240&a)))return t;if(0!==(4&r)&&(r|=16&n),0!==(t=e.entangledLanes))for(e=e.entanglements,t&=r;0<t;)o=1<<(n=31-it(t)),r|=e[n],t&=~o;return r}function pt(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function ht(e){return 0!==(e=-1073741825&e.pendingLanes)?e:1073741824&e?1073741824:0}function gt(){var e=ut;return 0===(4194240&(ut<<=1))&&(ut=64),e}function mt(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function yt(e,t,n){e.pendingLanes|=t,536870912!==t&&(e.suspendedLanes=0,e.pingedLanes=0),(e=e.eventTimes)[t=31-it(t)]=n}function vt(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-it(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}var bt=0;function wt(e){return 1<(e&=-e)?4<e?0!==(268435455&e)?16:536870912:4:1}var kt,St,xt,Et,Ct,Pt=!1,Ot=[],Rt=null,Tt=null,Nt=null,At=new Map,Lt=new Map,_t=[],jt="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Dt(e,t){switch(e){case"focusin":case"focusout":Rt=null;break;case"dragenter":case"dragleave":Tt=null;break;case"mouseover":case"mouseout":Nt=null;break;case"pointerover":case"pointerout":At.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Lt.delete(t.pointerId)}}function It(e,t,n,r,o,a){return null===e||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[o]},null!==t&&(null!==(t=wo(t))&&St(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==o&&-1===t.indexOf(o)&&t.push(o),e)}function Ft(e){var t=bo(e.target);if(null!==t){var n=Ve(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=We(n)))return e.blockedOn=t,void Ct(e.priority,(function(){xt(n)}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function zt(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Gt(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(null!==n)return null!==(t=wo(n))&&St(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);we=r,n.target.dispatchEvent(r),we=null,t.shift()}return!0}function Mt(e,t,n){zt(e)&&n.delete(t)}function Bt(){Pt=!1,null!==Rt&&zt(Rt)&&(Rt=null),null!==Tt&&zt(Tt)&&(Tt=null),null!==Nt&&zt(Nt)&&(Nt=null),At.forEach(Mt),Lt.forEach(Mt)}function Ut(e,t){e.blockedOn===t&&(e.blockedOn=null,Pt||(Pt=!0,o.unstable_scheduleCallback(o.unstable_NormalPriority,Bt)))}function Vt(e){function t(t){return Ut(t,e)}if(0<Ot.length){Ut(Ot[0],e);for(var n=1;n<Ot.length;n++){var r=Ot[n];r.blockedOn===e&&(r.blockedOn=null)}}for(null!==Rt&&Ut(Rt,e),null!==Tt&&Ut(Tt,e),null!==Nt&&Ut(Nt,e),At.forEach(t),Lt.forEach(t),n=0;n<_t.length;n++)(r=_t[n]).blockedOn===e&&(r.blockedOn=null);for(;0<_t.length&&null===(n=_t[0]).blockedOn;)Ft(n),null===n.blockedOn&&_t.shift()}var Wt=w.ReactCurrentBatchConfig,Ht=!0;function $t(e,t,n,r){var o=bt,a=Wt.transition;Wt.transition=null;try{bt=1,qt(e,t,n,r)}finally{bt=o,Wt.transition=a}}function Kt(e,t,n,r){var o=bt,a=Wt.transition;Wt.transition=null;try{bt=4,qt(e,t,n,r)}finally{bt=o,Wt.transition=a}}function qt(e,t,n,r){if(Ht){var o=Gt(e,t,n,r);if(null===o)Hr(e,t,r,Qt,n),Dt(e,r);else if(function(e,t,n,r,o){switch(t){case"focusin":return Rt=It(Rt,e,t,n,r,o),!0;case"dragenter":return Tt=It(Tt,e,t,n,r,o),!0;case"mouseover":return Nt=It(Nt,e,t,n,r,o),!0;case"pointerover":var a=o.pointerId;return At.set(a,It(At.get(a)||null,e,t,n,r,o)),!0;case"gotpointercapture":return a=o.pointerId,Lt.set(a,It(Lt.get(a)||null,e,t,n,r,o)),!0}return!1}(o,e,t,n,r))r.stopPropagation();else if(Dt(e,r),4&t&&-1<jt.indexOf(e)){for(;null!==o;){var a=wo(o);if(null!==a&&kt(a),null===(a=Gt(e,t,n,r))&&Hr(e,t,r,Qt,n),a===o)break;o=a}null!==o&&r.stopPropagation()}else Hr(e,t,r,null,n)}}var Qt=null;function Gt(e,t,n,r){if(Qt=null,null!==(e=bo(e=ke(r))))if(null===(t=Ve(e)))e=null;else if(13===(n=t.tag)){if(null!==(e=We(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Qt=e,null}function Jt(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Ye()){case Ze:return 1;case et:return 4;case tt:case nt:return 16;case rt:return 536870912;default:return 16}default:return 16}}var Xt=null,Yt=null,Zt=null;function en(){if(Zt)return Zt;var e,t,n=Yt,r=n.length,o="value"in Xt?Xt.value:Xt.textContent,a=o.length;for(e=0;e<r&&n[e]===o[e];e++);var i=r-e;for(t=1;t<=i&&n[r-t]===o[a-t];t++);return Zt=o.slice(e,1<t?1-t:void 0)}function tn(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function nn(){return!0}function rn(){return!1}function on(e){function t(t,n,r,o,a){for(var i in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=o,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(i)&&(t=e[i],this[i]=t?t(o):o[i]);return this.isDefaultPrevented=(null!=o.defaultPrevented?o.defaultPrevented:!1===o.returnValue)?nn:rn,this.isPropagationStopped=rn,this}return F(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=nn)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=nn)},persist:function(){},isPersistent:nn}),t}var an,sn,ln,un={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},cn=on(un),dn=F({},un,{view:0,detail:0}),fn=on(dn),pn=F({},dn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Cn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ln&&(ln&&"mousemove"===e.type?(an=e.screenX-ln.screenX,sn=e.screenY-ln.screenY):sn=an=0,ln=e),an)},movementY:function(e){return"movementY"in e?e.movementY:sn}}),hn=on(pn),gn=on(F({},pn,{dataTransfer:0})),mn=on(F({},dn,{relatedTarget:0})),yn=on(F({},un,{animationName:0,elapsedTime:0,pseudoElement:0})),vn=F({},un,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),bn=on(vn),wn=on(F({},un,{data:0})),kn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Sn={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},xn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function En(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=xn[e])&&!!t[e]}function Cn(){return En}var Pn=F({},dn,{key:function(e){if(e.key){var t=kn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=tn(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?Sn[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Cn,charCode:function(e){return"keypress"===e.type?tn(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?tn(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}}),On=on(Pn),Rn=on(F({},pn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),Tn=on(F({},dn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Cn})),Nn=on(F({},un,{propertyName:0,elapsedTime:0,pseudoElement:0})),An=F({},pn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Ln=on(An),_n=[9,13,27,32],jn=c&&"CompositionEvent"in window,Dn=null;c&&"documentMode"in document&&(Dn=document.documentMode);var In=c&&"TextEvent"in window&&!Dn,Fn=c&&(!jn||Dn&&8<Dn&&11>=Dn),zn=String.fromCharCode(32),Mn=!1;function Bn(e,t){switch(e){case"keyup":return-1!==_n.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Un(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var Vn=!1;var Wn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Hn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Wn[e.type]:"textarea"===t}function $n(e,t,n,r){Pe(r),0<(t=Kr(t,"onChange")).length&&(n=new cn("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Kn=null,qn=null;function Qn(e){zr(e,0)}function Gn(e){if(q(ko(e)))return e}function Jn(e,t){if("change"===e)return t}var Xn=!1;if(c){var Yn;if(c){var Zn="oninput"in document;if(!Zn){var er=document.createElement("div");er.setAttribute("oninput","return;"),Zn="function"===typeof er.oninput}Yn=Zn}else Yn=!1;Xn=Yn&&(!document.documentMode||9<document.documentMode)}function tr(){Kn&&(Kn.detachEvent("onpropertychange",nr),qn=Kn=null)}function nr(e){if("value"===e.propertyName&&Gn(qn)){var t=[];$n(t,qn,e,ke(e)),Ae(Qn,t)}}function rr(e,t,n){"focusin"===e?(tr(),qn=n,(Kn=t).attachEvent("onpropertychange",nr)):"focusout"===e&&tr()}function or(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Gn(qn)}function ar(e,t){if("click"===e)return Gn(t)}function ir(e,t){if("input"===e||"change"===e)return Gn(t)}var sr="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function lr(e,t){if(sr(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!d.call(t,o)||!sr(e[o],t[o]))return!1}return!0}function ur(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function cr(e,t){var n,r=ur(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=ur(r)}}function dr(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?dr(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function fr(){for(var e=window,t=Q();t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=Q((e=t.contentWindow).document)}return t}function pr(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function hr(e){var t=fr(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&dr(n.ownerDocument.documentElement,n)){if(null!==r&&pr(n))if(t=r.start,void 0===(e=r.end)&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if((e=(t=n.ownerDocument||document)&&t.defaultView||window).getSelection){e=e.getSelection();var o=n.textContent.length,a=Math.min(r.start,o);r=void 0===r.end?a:Math.min(r.end,o),!e.extend&&a>r&&(o=r,r=a,a=o),o=cr(n,a);var i=cr(n,r);o&&i&&(1!==e.rangeCount||e.anchorNode!==o.node||e.anchorOffset!==o.offset||e.focusNode!==i.node||e.focusOffset!==i.offset)&&((t=t.createRange()).setStart(o.node,o.offset),e.removeAllRanges(),a>r?(e.addRange(t),e.extend(i.node,i.offset)):(t.setEnd(i.node,i.offset),e.addRange(t)))}for(t=[],e=n;e=e.parentNode;)1===e.nodeType&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for("function"===typeof n.focus&&n.focus(),n=0;n<t.length;n++)(e=t[n]).element.scrollLeft=e.left,e.element.scrollTop=e.top}}var gr=c&&"documentMode"in document&&11>=document.documentMode,mr=null,yr=null,vr=null,br=!1;function wr(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;br||null==mr||mr!==Q(r)||("selectionStart"in(r=mr)&&pr(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},vr&&lr(vr,r)||(vr=r,0<(r=Kr(yr,"onSelect")).length&&(t=new cn("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=mr)))}function kr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Sr={animationend:kr("Animation","AnimationEnd"),animationiteration:kr("Animation","AnimationIteration"),animationstart:kr("Animation","AnimationStart"),transitionend:kr("Transition","TransitionEnd")},xr={},Er={};function Cr(e){if(xr[e])return xr[e];if(!Sr[e])return e;var t,n=Sr[e];for(t in n)if(n.hasOwnProperty(t)&&t in Er)return xr[e]=n[t];return e}c&&(Er=document.createElement("div").style,"AnimationEvent"in window||(delete Sr.animationend.animation,delete Sr.animationiteration.animation,delete Sr.animationstart.animation),"TransitionEvent"in window||delete Sr.transitionend.transition);var Pr=Cr("animationend"),Or=Cr("animationiteration"),Rr=Cr("animationstart"),Tr=Cr("transitionend"),Nr=new Map,Ar="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function Lr(e,t){Nr.set(e,t),l(t,[e])}for(var _r=0;_r<Ar.length;_r++){var jr=Ar[_r];Lr(jr.toLowerCase(),"on"+(jr[0].toUpperCase()+jr.slice(1)))}Lr(Pr,"onAnimationEnd"),Lr(Or,"onAnimationIteration"),Lr(Rr,"onAnimationStart"),Lr("dblclick","onDoubleClick"),Lr("focusin","onFocus"),Lr("focusout","onBlur"),Lr(Tr,"onTransitionEnd"),u("onMouseEnter",["mouseout","mouseover"]),u("onMouseLeave",["mouseout","mouseover"]),u("onPointerEnter",["pointerout","pointerover"]),u("onPointerLeave",["pointerout","pointerover"]),l("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),l("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),l("onBeforeInput",["compositionend","keypress","textInput","paste"]),l("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),l("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Dr="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Ir=new Set("cancel close invalid load scroll toggle".split(" ").concat(Dr));function Fr(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,function(e,t,n,r,o,i,s,l,u){if(Ue.apply(this,arguments),Ie){if(!Ie)throw Error(a(198));var c=Fe;Ie=!1,Fe=null,ze||(ze=!0,Me=c)}}(r,t,void 0,e),e.currentTarget=null}function zr(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var i=r.length-1;0<=i;i--){var s=r[i],l=s.instance,u=s.currentTarget;if(s=s.listener,l!==a&&o.isPropagationStopped())break e;Fr(o,s,u),a=l}else for(i=0;i<r.length;i++){if(l=(s=r[i]).instance,u=s.currentTarget,s=s.listener,l!==a&&o.isPropagationStopped())break e;Fr(o,s,u),a=l}}}if(ze)throw e=Me,ze=!1,Me=null,e}function Mr(e,t){var n=t[mo];void 0===n&&(n=t[mo]=new Set);var r=e+"__bubble";n.has(r)||(Wr(t,e,2,!1),n.add(r))}function Br(e,t,n){var r=0;t&&(r|=4),Wr(n,e,r,t)}var Ur="_reactListening"+Math.random().toString(36).slice(2);function Vr(e){if(!e[Ur]){e[Ur]=!0,i.forEach((function(t){"selectionchange"!==t&&(Ir.has(t)||Br(t,!1,e),Br(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Ur]||(t[Ur]=!0,Br("selectionchange",!1,t))}}function Wr(e,t,n,r){switch(Jt(t)){case 1:var o=$t;break;case 4:o=Kt;break;default:o=qt}n=o.bind(null,t,n,e),o=void 0,!_e||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(o=!0),r?void 0!==o?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):void 0!==o?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Hr(e,t,n,r,o){var a=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var i=r.tag;if(3===i||4===i){var s=r.stateNode.containerInfo;if(s===o||8===s.nodeType&&s.parentNode===o)break;if(4===i)for(i=r.return;null!==i;){var l=i.tag;if((3===l||4===l)&&((l=i.stateNode.containerInfo)===o||8===l.nodeType&&l.parentNode===o))return;i=i.return}for(;null!==s;){if(null===(i=bo(s)))return;if(5===(l=i.tag)||6===l){r=a=i;continue e}s=s.parentNode}}r=r.return}Ae((function(){var r=a,o=ke(n),i=[];e:{var s=Nr.get(e);if(void 0!==s){var l=cn,u=e;switch(e){case"keypress":if(0===tn(n))break e;case"keydown":case"keyup":l=On;break;case"focusin":u="focus",l=mn;break;case"focusout":u="blur",l=mn;break;case"beforeblur":case"afterblur":l=mn;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":l=hn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":l=gn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":l=Tn;break;case Pr:case Or:case Rr:l=yn;break;case Tr:l=Nn;break;case"scroll":l=fn;break;case"wheel":l=Ln;break;case"copy":case"cut":case"paste":l=bn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":l=Rn}var c=0!==(4&t),d=!c&&"scroll"===e,f=c?null!==s?s+"Capture":null:s;c=[];for(var p,h=r;null!==h;){var g=(p=h).stateNode;if(5===p.tag&&null!==g&&(p=g,null!==f&&(null!=(g=Le(h,f))&&c.push($r(h,g,p)))),d)break;h=h.return}0<c.length&&(s=new l(s,u,null,n,o),i.push({event:s,listeners:c}))}}if(0===(7&t)){if(l="mouseout"===e||"pointerout"===e,(!(s="mouseover"===e||"pointerover"===e)||n===we||!(u=n.relatedTarget||n.fromElement)||!bo(u)&&!u[go])&&(l||s)&&(s=o.window===o?o:(s=o.ownerDocument)?s.defaultView||s.parentWindow:window,l?(l=r,null!==(u=(u=n.relatedTarget||n.toElement)?bo(u):null)&&(u!==(d=Ve(u))||5!==u.tag&&6!==u.tag)&&(u=null)):(l=null,u=r),l!==u)){if(c=hn,g="onMouseLeave",f="onMouseEnter",h="mouse","pointerout"!==e&&"pointerover"!==e||(c=Rn,g="onPointerLeave",f="onPointerEnter",h="pointer"),d=null==l?s:ko(l),p=null==u?s:ko(u),(s=new c(g,h+"leave",l,n,o)).target=d,s.relatedTarget=p,g=null,bo(o)===r&&((c=new c(f,h+"enter",u,n,o)).target=p,c.relatedTarget=d,g=c),d=g,l&&u)e:{for(f=u,h=0,p=c=l;p;p=qr(p))h++;for(p=0,g=f;g;g=qr(g))p++;for(;0<h-p;)c=qr(c),h--;for(;0<p-h;)f=qr(f),p--;for(;h--;){if(c===f||null!==f&&c===f.alternate)break e;c=qr(c),f=qr(f)}c=null}else c=null;null!==l&&Qr(i,s,l,c,!1),null!==u&&null!==d&&Qr(i,d,u,c,!0)}if("select"===(l=(s=r?ko(r):window).nodeName&&s.nodeName.toLowerCase())||"input"===l&&"file"===s.type)var m=Jn;else if(Hn(s))if(Xn)m=ir;else{m=or;var y=rr}else(l=s.nodeName)&&"input"===l.toLowerCase()&&("checkbox"===s.type||"radio"===s.type)&&(m=ar);switch(m&&(m=m(e,r))?$n(i,m,n,o):(y&&y(e,s,r),"focusout"===e&&(y=s._wrapperState)&&y.controlled&&"number"===s.type&&ee(s,"number",s.value)),y=r?ko(r):window,e){case"focusin":(Hn(y)||"true"===y.contentEditable)&&(mr=y,yr=r,vr=null);break;case"focusout":vr=yr=mr=null;break;case"mousedown":br=!0;break;case"contextmenu":case"mouseup":case"dragend":br=!1,wr(i,n,o);break;case"selectionchange":if(gr)break;case"keydown":case"keyup":wr(i,n,o)}var v;if(jn)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else Vn?Bn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Fn&&"ko"!==n.locale&&(Vn||"onCompositionStart"!==b?"onCompositionEnd"===b&&Vn&&(v=en()):(Yt="value"in(Xt=o)?Xt.value:Xt.textContent,Vn=!0)),0<(y=Kr(r,b)).length&&(b=new wn(b,e,null,n,o),i.push({event:b,listeners:y}),v?b.data=v:null!==(v=Un(n))&&(b.data=v))),(v=In?function(e,t){switch(e){case"compositionend":return Un(t);case"keypress":return 32!==t.which?null:(Mn=!0,zn);case"textInput":return(e=t.data)===zn&&Mn?null:e;default:return null}}(e,n):function(e,t){if(Vn)return"compositionend"===e||!jn&&Bn(e,t)?(e=en(),Zt=Yt=Xt=null,Vn=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Fn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(r=Kr(r,"onBeforeInput")).length&&(o=new wn("onBeforeInput","beforeinput",null,n,o),i.push({event:o,listeners:r}),o.data=v))}zr(i,t)}))}function $r(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Kr(e,t){for(var n=t+"Capture",r=[];null!==e;){var o=e,a=o.stateNode;5===o.tag&&null!==a&&(o=a,null!=(a=Le(e,n))&&r.unshift($r(e,a,o)),null!=(a=Le(e,t))&&r.push($r(e,a,o))),e=e.return}return r}function qr(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag);return e||null}function Qr(e,t,n,r,o){for(var a=t._reactName,i=[];null!==n&&n!==r;){var s=n,l=s.alternate,u=s.stateNode;if(null!==l&&l===r)break;5===s.tag&&null!==u&&(s=u,o?null!=(l=Le(n,a))&&i.unshift($r(n,l,s)):o||null!=(l=Le(n,a))&&i.push($r(n,l,s))),n=n.return}0!==i.length&&e.push({event:t,listeners:i})}var Gr=/\r\n?/g,Jr=/\u0000|\uFFFD/g;function Xr(e){return("string"===typeof e?e:""+e).replace(Gr,"\n").replace(Jr,"")}function Yr(e,t,n){if(t=Xr(t),Xr(e)!==t&&n)throw Error(a(425))}function Zr(){}var eo=null,to=null;function no(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var ro="function"===typeof setTimeout?setTimeout:void 0,oo="function"===typeof clearTimeout?clearTimeout:void 0,ao="function"===typeof Promise?Promise:void 0,io="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof ao?function(e){return ao.resolve(null).then(e).catch(so)}:ro;function so(e){setTimeout((function(){throw e}))}function lo(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&8===o.nodeType)if("/$"===(n=o.data)){if(0===r)return e.removeChild(o),void Vt(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=o}while(n);Vt(t)}function uo(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t)break;if("/$"===t)return null}}return e}function co(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}var fo=Math.random().toString(36).slice(2),po="__reactFiber$"+fo,ho="__reactProps$"+fo,go="__reactContainer$"+fo,mo="__reactEvents$"+fo,yo="__reactListeners$"+fo,vo="__reactHandles$"+fo;function bo(e){var t=e[po];if(t)return t;for(var n=e.parentNode;n;){if(t=n[go]||n[po]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=co(e);null!==e;){if(n=e[po])return n;e=co(e)}return t}n=(e=n).parentNode}return null}function wo(e){return!(e=e[po]||e[go])||5!==e.tag&&6!==e.tag&&13!==e.tag&&3!==e.tag?null:e}function ko(e){if(5===e.tag||6===e.tag)return e.stateNode;throw Error(a(33))}function So(e){return e[ho]||null}var xo=[],Eo=-1;function Co(e){return{current:e}}function Po(e){0>Eo||(e.current=xo[Eo],xo[Eo]=null,Eo--)}function Oo(e,t){Eo++,xo[Eo]=e.current,e.current=t}var Ro={},To=Co(Ro),No=Co(!1),Ao=Ro;function Lo(e,t){var n=e.type.contextTypes;if(!n)return Ro;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var o,a={};for(o in n)a[o]=t[o];return r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=a),a}function _o(e){return null!==(e=e.childContextTypes)&&void 0!==e}function jo(){Po(No),Po(To)}function Do(e,t,n){if(To.current!==Ro)throw Error(a(168));Oo(To,t),Oo(No,n)}function Io(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,"function"!==typeof r.getChildContext)return n;for(var o in r=r.getChildContext())if(!(o in t))throw Error(a(108,W(e)||"Unknown",o));return F({},n,r)}function Fo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||Ro,Ao=To.current,Oo(To,e),Oo(No,No.current),!0}function zo(e,t,n){var r=e.stateNode;if(!r)throw Error(a(169));n?(e=Io(e,t,Ao),r.__reactInternalMemoizedMergedChildContext=e,Po(No),Po(To),Oo(To,e)):Po(No),Oo(No,n)}var Mo=null,Bo=!1,Uo=!1;function Vo(e){null===Mo?Mo=[e]:Mo.push(e)}function Wo(){if(!Uo&&null!==Mo){Uo=!0;var e=0,t=bt;try{var n=Mo;for(bt=1;e<n.length;e++){var r=n[e];do{r=r(!0)}while(null!==r)}Mo=null,Bo=!1}catch(o){throw null!==Mo&&(Mo=Mo.slice(e+1)),qe(Ze,Wo),o}finally{bt=t,Uo=!1}}return null}var Ho=[],$o=0,Ko=null,qo=0,Qo=[],Go=0,Jo=null,Xo=1,Yo="";function Zo(e,t){Ho[$o++]=qo,Ho[$o++]=Ko,Ko=e,qo=t}function ea(e,t,n){Qo[Go++]=Xo,Qo[Go++]=Yo,Qo[Go++]=Jo,Jo=e;var r=Xo;e=Yo;var o=32-it(r)-1;r&=~(1<<o),n+=1;var a=32-it(t)+o;if(30<a){var i=o-o%5;a=(r&(1<<i)-1).toString(32),r>>=i,o-=i,Xo=1<<32-it(t)+o|n<<o|r,Yo=a+e}else Xo=1<<a|n<<o|r,Yo=e}function ta(e){null!==e.return&&(Zo(e,1),ea(e,1,0))}function na(e){for(;e===Ko;)Ko=Ho[--$o],Ho[$o]=null,qo=Ho[--$o],Ho[$o]=null;for(;e===Jo;)Jo=Qo[--Go],Qo[Go]=null,Yo=Qo[--Go],Qo[Go]=null,Xo=Qo[--Go],Qo[Go]=null}var ra=null,oa=null,aa=!1,ia=null;function sa(e,t){var n=Au(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,null===(t=e.deletions)?(e.deletions=[n],e.flags|=16):t.push(n)}function la(e,t){switch(e.tag){case 5:var n=e.type;return null!==(t=1!==t.nodeType||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t)&&(e.stateNode=t,ra=e,oa=uo(t.firstChild),!0);case 6:return null!==(t=""===e.pendingProps||3!==t.nodeType?null:t)&&(e.stateNode=t,ra=e,oa=null,!0);case 13:return null!==(t=8!==t.nodeType?null:t)&&(n=null!==Jo?{id:Xo,overflow:Yo}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},(n=Au(18,null,null,0)).stateNode=t,n.return=e,e.child=n,ra=e,oa=null,!0);default:return!1}}function ua(e){return 0!==(1&e.mode)&&0===(128&e.flags)}function ca(e){if(aa){var t=oa;if(t){var n=t;if(!la(e,t)){if(ua(e))throw Error(a(418));t=uo(n.nextSibling);var r=ra;t&&la(e,t)?sa(r,n):(e.flags=-4097&e.flags|2,aa=!1,ra=e)}}else{if(ua(e))throw Error(a(418));e.flags=-4097&e.flags|2,aa=!1,ra=e}}}function da(e){for(e=e.return;null!==e&&5!==e.tag&&3!==e.tag&&13!==e.tag;)e=e.return;ra=e}function fa(e){if(e!==ra)return!1;if(!aa)return da(e),aa=!0,!1;var t;if((t=3!==e.tag)&&!(t=5!==e.tag)&&(t="head"!==(t=e.type)&&"body"!==t&&!no(e.type,e.memoizedProps)),t&&(t=oa)){if(ua(e))throw pa(),Error(a(418));for(;t;)sa(e,t),t=uo(t.nextSibling)}if(da(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(a(317));e:{for(e=e.nextSibling,t=0;e;){if(8===e.nodeType){var n=e.data;if("/$"===n){if(0===t){oa=uo(e.nextSibling);break e}t--}else"$"!==n&&"$!"!==n&&"$?"!==n||t++}e=e.nextSibling}oa=null}}else oa=ra?uo(e.stateNode.nextSibling):null;return!0}function pa(){for(var e=oa;e;)e=uo(e.nextSibling)}function ha(){oa=ra=null,aa=!1}function ga(e){null===ia?ia=[e]:ia.push(e)}var ma=w.ReactCurrentBatchConfig;function ya(e,t,n){if(null!==(e=n.ref)&&"function"!==typeof e&&"object"!==typeof e){if(n._owner){if(n=n._owner){if(1!==n.tag)throw Error(a(309));var r=n.stateNode}if(!r)throw Error(a(147,e));var o=r,i=""+e;return null!==t&&null!==t.ref&&"function"===typeof t.ref&&t.ref._stringRef===i?t.ref:(t=function(e){var t=o.refs;null===e?delete t[i]:t[i]=e},t._stringRef=i,t)}if("string"!==typeof e)throw Error(a(284));if(!n._owner)throw Error(a(290,e))}return e}function va(e,t){throw e=Object.prototype.toString.call(t),Error(a(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function ba(e){return(0,e._init)(e._payload)}function wa(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e,t){for(e=new Map;null!==t;)null!==t.key?e.set(t.key,t):e.set(t.index,t),t=t.sibling;return e}function o(e,t){return(e=_u(e,t)).index=0,e.sibling=null,e}function i(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=2,n):r:(t.flags|=2,n):(t.flags|=1048576,n)}function s(t){return e&&null===t.alternate&&(t.flags|=2),t}function l(e,t,n,r){return null===t||6!==t.tag?((t=Fu(n,e.mode,r)).return=e,t):((t=o(t,n)).return=e,t)}function u(e,t,n,r){var a=n.type;return a===x?d(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===a||"object"===typeof a&&null!==a&&a.$$typeof===L&&ba(a)===t.type)?((r=o(t,n.props)).ref=ya(e,t,n),r.return=e,r):((r=ju(n.type,n.key,n.props,null,e.mode,r)).ref=ya(e,t,n),r.return=e,r)}function c(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=zu(n,e.mode,r)).return=e,t):((t=o(t,n.children||[])).return=e,t)}function d(e,t,n,r,a){return null===t||7!==t.tag?((t=Du(n,e.mode,r,a)).return=e,t):((t=o(t,n)).return=e,t)}function f(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t)return(t=Fu(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case k:return(n=ju(t.type,t.key,t.props,null,e.mode,n)).ref=ya(e,null,t),n.return=e,n;case S:return(t=zu(t,e.mode,n)).return=e,t;case L:return f(e,(0,t._init)(t._payload),n)}if(te(t)||D(t))return(t=Du(t,e.mode,n,null)).return=e,t;va(e,t)}return null}function p(e,t,n,r){var o=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n)return null!==o?null:l(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case k:return n.key===o?u(e,t,n,r):null;case S:return n.key===o?c(e,t,n,r):null;case L:return p(e,t,(o=n._init)(n._payload),r)}if(te(n)||D(n))return null!==o?null:d(e,t,n,r,null);va(e,n)}return null}function h(e,t,n,r,o){if("string"===typeof r&&""!==r||"number"===typeof r)return l(t,e=e.get(n)||null,""+r,o);if("object"===typeof r&&null!==r){switch(r.$$typeof){case k:return u(t,e=e.get(null===r.key?n:r.key)||null,r,o);case S:return c(t,e=e.get(null===r.key?n:r.key)||null,r,o);case L:return h(e,t,n,(0,r._init)(r._payload),o)}if(te(r)||D(r))return d(t,e=e.get(n)||null,r,o,null);va(t,r)}return null}function g(o,a,s,l){for(var u=null,c=null,d=a,g=a=0,m=null;null!==d&&g<s.length;g++){d.index>g?(m=d,d=null):m=d.sibling;var y=p(o,d,s[g],l);if(null===y){null===d&&(d=m);break}e&&d&&null===y.alternate&&t(o,d),a=i(y,a,g),null===c?u=y:c.sibling=y,c=y,d=m}if(g===s.length)return n(o,d),aa&&Zo(o,g),u;if(null===d){for(;g<s.length;g++)null!==(d=f(o,s[g],l))&&(a=i(d,a,g),null===c?u=d:c.sibling=d,c=d);return aa&&Zo(o,g),u}for(d=r(o,d);g<s.length;g++)null!==(m=h(d,o,g,s[g],l))&&(e&&null!==m.alternate&&d.delete(null===m.key?g:m.key),a=i(m,a,g),null===c?u=m:c.sibling=m,c=m);return e&&d.forEach((function(e){return t(o,e)})),aa&&Zo(o,g),u}function m(o,s,l,u){var c=D(l);if("function"!==typeof c)throw Error(a(150));if(null==(l=c.call(l)))throw Error(a(151));for(var d=c=null,g=s,m=s=0,y=null,v=l.next();null!==g&&!v.done;m++,v=l.next()){g.index>m?(y=g,g=null):y=g.sibling;var b=p(o,g,v.value,u);if(null===b){null===g&&(g=y);break}e&&g&&null===b.alternate&&t(o,g),s=i(b,s,m),null===d?c=b:d.sibling=b,d=b,g=y}if(v.done)return n(o,g),aa&&Zo(o,m),c;if(null===g){for(;!v.done;m++,v=l.next())null!==(v=f(o,v.value,u))&&(s=i(v,s,m),null===d?c=v:d.sibling=v,d=v);return aa&&Zo(o,m),c}for(g=r(o,g);!v.done;m++,v=l.next())null!==(v=h(g,o,m,v.value,u))&&(e&&null!==v.alternate&&g.delete(null===v.key?m:v.key),s=i(v,s,m),null===d?c=v:d.sibling=v,d=v);return e&&g.forEach((function(e){return t(o,e)})),aa&&Zo(o,m),c}return function e(r,a,i,l){if("object"===typeof i&&null!==i&&i.type===x&&null===i.key&&(i=i.props.children),"object"===typeof i&&null!==i){switch(i.$$typeof){case k:e:{for(var u=i.key,c=a;null!==c;){if(c.key===u){if((u=i.type)===x){if(7===c.tag){n(r,c.sibling),(a=o(c,i.props.children)).return=r,r=a;break e}}else if(c.elementType===u||"object"===typeof u&&null!==u&&u.$$typeof===L&&ba(u)===c.type){n(r,c.sibling),(a=o(c,i.props)).ref=ya(r,c,i),a.return=r,r=a;break e}n(r,c);break}t(r,c),c=c.sibling}i.type===x?((a=Du(i.props.children,r.mode,l,i.key)).return=r,r=a):((l=ju(i.type,i.key,i.props,null,r.mode,l)).ref=ya(r,a,i),l.return=r,r=l)}return s(r);case S:e:{for(c=i.key;null!==a;){if(a.key===c){if(4===a.tag&&a.stateNode.containerInfo===i.containerInfo&&a.stateNode.implementation===i.implementation){n(r,a.sibling),(a=o(a,i.children||[])).return=r,r=a;break e}n(r,a);break}t(r,a),a=a.sibling}(a=zu(i,r.mode,l)).return=r,r=a}return s(r);case L:return e(r,a,(c=i._init)(i._payload),l)}if(te(i))return g(r,a,i,l);if(D(i))return m(r,a,i,l);va(r,i)}return"string"===typeof i&&""!==i||"number"===typeof i?(i=""+i,null!==a&&6===a.tag?(n(r,a.sibling),(a=o(a,i)).return=r,r=a):(n(r,a),(a=Fu(i,r.mode,l)).return=r,r=a),s(r)):n(r,a)}}var ka=wa(!0),Sa=wa(!1),xa=Co(null),Ea=null,Ca=null,Pa=null;function Oa(){Pa=Ca=Ea=null}function Ra(e){var t=xa.current;Po(xa),e._currentValue=t}function Ta(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function Na(e,t){Ea=e,Pa=Ca=null,null!==(e=e.dependencies)&&null!==e.firstContext&&(0!==(e.lanes&t)&&(bs=!0),e.firstContext=null)}function Aa(e){var t=e._currentValue;if(Pa!==e)if(e={context:e,memoizedValue:t,next:null},null===Ca){if(null===Ea)throw Error(a(308));Ca=e,Ea.dependencies={lanes:0,firstContext:e}}else Ca=Ca.next=e;return t}var La=null;function _a(e){null===La?La=[e]:La.push(e)}function ja(e,t,n,r){var o=t.interleaved;return null===o?(n.next=n,_a(t)):(n.next=o.next,o.next=n),t.interleaved=n,Da(e,r)}function Da(e,t){e.lanes|=t;var n=e.alternate;for(null!==n&&(n.lanes|=t),n=e,e=e.return;null!==e;)e.childLanes|=t,null!==(n=e.alternate)&&(n.childLanes|=t),n=e,e=e.return;return 3===n.tag?n.stateNode:null}var Ia=!1;function Fa(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function za(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Ma(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function Ba(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&Rl)){var o=r.pending;return null===o?t.next=t:(t.next=o.next,o.next=t),r.pending=t,Da(e,n)}return null===(o=r.interleaved)?(t.next=t,_a(r)):(t.next=o.next,o.next=t),r.interleaved=t,Da(e,n)}function Ua(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194240&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}function Va(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var o=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var i={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};null===a?o=a=i:a=a.next=i,n=n.next}while(null!==n);null===a?o=a=t:a=a.next=t}else o=a=t;return n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:a,shared:r.shared,effects:r.effects},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Wa(e,t,n,r){var o=e.updateQueue;Ia=!1;var a=o.firstBaseUpdate,i=o.lastBaseUpdate,s=o.shared.pending;if(null!==s){o.shared.pending=null;var l=s,u=l.next;l.next=null,null===i?a=u:i.next=u,i=l;var c=e.alternate;null!==c&&((s=(c=c.updateQueue).lastBaseUpdate)!==i&&(null===s?c.firstBaseUpdate=u:s.next=u,c.lastBaseUpdate=l))}if(null!==a){var d=o.baseState;for(i=0,c=u=l=null,s=a;;){var f=s.lane,p=s.eventTime;if((r&f)===f){null!==c&&(c=c.next={eventTime:p,lane:0,tag:s.tag,payload:s.payload,callback:s.callback,next:null});e:{var h=e,g=s;switch(f=t,p=n,g.tag){case 1:if("function"===typeof(h=g.payload)){d=h.call(p,d,f);break e}d=h;break e;case 3:h.flags=-65537&h.flags|128;case 0:if(null===(f="function"===typeof(h=g.payload)?h.call(p,d,f):h)||void 0===f)break e;d=F({},d,f);break e;case 2:Ia=!0}}null!==s.callback&&0!==s.lane&&(e.flags|=64,null===(f=o.effects)?o.effects=[s]:f.push(s))}else p={eventTime:p,lane:f,tag:s.tag,payload:s.payload,callback:s.callback,next:null},null===c?(u=c=p,l=d):c=c.next=p,i|=f;if(null===(s=s.next)){if(null===(s=o.shared.pending))break;s=(f=s).next,f.next=null,o.lastBaseUpdate=f,o.shared.pending=null}}if(null===c&&(l=d),o.baseState=l,o.firstBaseUpdate=u,o.lastBaseUpdate=c,null!==(t=o.shared.interleaved)){o=t;do{i|=o.lane,o=o.next}while(o!==t)}else null===a&&(o.shared.lanes=0);Il|=i,e.lanes=i,e.memoizedState=d}}function Ha(e,t,n){if(e=t.effects,t.effects=null,null!==e)for(t=0;t<e.length;t++){var r=e[t],o=r.callback;if(null!==o){if(r.callback=null,r=n,"function"!==typeof o)throw Error(a(191,o));o.call(r)}}}var $a={},Ka=Co($a),qa=Co($a),Qa=Co($a);function Ga(e){if(e===$a)throw Error(a(174));return e}function Ja(e,t){switch(Oo(Qa,t),Oo(qa,e),Oo(Ka,$a),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:le(null,"");break;default:t=le(t=(e=8===e?t.parentNode:t).namespaceURI||null,e=e.tagName)}Po(Ka),Oo(Ka,t)}function Xa(){Po(Ka),Po(qa),Po(Qa)}function Ya(e){Ga(Qa.current);var t=Ga(Ka.current),n=le(t,e.type);t!==n&&(Oo(qa,e),Oo(Ka,n))}function Za(e){qa.current===e&&(Po(Ka),Po(qa))}var ei=Co(0);function ti(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var ni=[];function ri(){for(var e=0;e<ni.length;e++)ni[e]._workInProgressVersionPrimary=null;ni.length=0}var oi=w.ReactCurrentDispatcher,ai=w.ReactCurrentBatchConfig,ii=0,si=null,li=null,ui=null,ci=!1,di=!1,fi=0,pi=0;function hi(){throw Error(a(321))}function gi(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!sr(e[n],t[n]))return!1;return!0}function mi(e,t,n,r,o,i){if(ii=i,si=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,oi.current=null===e||null===e.memoizedState?Zi:es,e=n(r,o),di){i=0;do{if(di=!1,fi=0,25<=i)throw Error(a(301));i+=1,ui=li=null,t.updateQueue=null,oi.current=ts,e=n(r,o)}while(di)}if(oi.current=Yi,t=null!==li&&null!==li.next,ii=0,ui=li=si=null,ci=!1,t)throw Error(a(300));return e}function yi(){var e=0!==fi;return fi=0,e}function vi(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===ui?si.memoizedState=ui=e:ui=ui.next=e,ui}function bi(){if(null===li){var e=si.alternate;e=null!==e?e.memoizedState:null}else e=li.next;var t=null===ui?si.memoizedState:ui.next;if(null!==t)ui=t,li=e;else{if(null===e)throw Error(a(310));e={memoizedState:(li=e).memoizedState,baseState:li.baseState,baseQueue:li.baseQueue,queue:li.queue,next:null},null===ui?si.memoizedState=ui=e:ui=ui.next=e}return ui}function wi(e,t){return"function"===typeof t?t(e):t}function ki(e){var t=bi(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=li,o=r.baseQueue,i=n.pending;if(null!==i){if(null!==o){var s=o.next;o.next=i.next,i.next=s}r.baseQueue=o=i,n.pending=null}if(null!==o){i=o.next,r=r.baseState;var l=s=null,u=null,c=i;do{var d=c.lane;if((ii&d)===d)null!==u&&(u=u.next={lane:0,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null}),r=c.hasEagerState?c.eagerState:e(r,c.action);else{var f={lane:d,action:c.action,hasEagerState:c.hasEagerState,eagerState:c.eagerState,next:null};null===u?(l=u=f,s=r):u=u.next=f,si.lanes|=d,Il|=d}c=c.next}while(null!==c&&c!==i);null===u?s=r:u.next=l,sr(r,t.memoizedState)||(bs=!0),t.memoizedState=r,t.baseState=s,t.baseQueue=u,n.lastRenderedState=r}if(null!==(e=n.interleaved)){o=e;do{i=o.lane,si.lanes|=i,Il|=i,o=o.next}while(o!==e)}else null===o&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Si(e){var t=bi(),n=t.queue;if(null===n)throw Error(a(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,i=t.memoizedState;if(null!==o){n.pending=null;var s=o=o.next;do{i=e(i,s.action),s=s.next}while(s!==o);sr(i,t.memoizedState)||(bs=!0),t.memoizedState=i,null===t.baseQueue&&(t.baseState=i),n.lastRenderedState=i}return[i,r]}function xi(){}function Ei(e,t){var n=si,r=bi(),o=t(),i=!sr(r.memoizedState,o);if(i&&(r.memoizedState=o,bs=!0),r=r.queue,Ii(Oi.bind(null,n,r,e),[e]),r.getSnapshot!==t||i||null!==ui&&1&ui.memoizedState.tag){if(n.flags|=2048,Ai(9,Pi.bind(null,n,r,o,t),void 0,null),null===Tl)throw Error(a(349));0!==(30&ii)||Ci(n,t,o)}return o}function Ci(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=si.updateQueue)?(t={lastEffect:null,stores:null},si.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Pi(e,t,n,r){t.value=n,t.getSnapshot=r,Ri(t)&&Ti(e)}function Oi(e,t,n){return n((function(){Ri(t)&&Ti(e)}))}function Ri(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!sr(e,n)}catch(r){return!0}}function Ti(e){var t=Da(e,1);null!==t&&nu(t,e,1,-1)}function Ni(e){var t=vi();return"function"===typeof e&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:wi,lastRenderedState:e},t.queue=e,e=e.dispatch=Qi.bind(null,si,e),[t.memoizedState,e]}function Ai(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},null===(t=si.updateQueue)?(t={lastEffect:null,stores:null},si.updateQueue=t,t.lastEffect=e.next=e):null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Li(){return bi().memoizedState}function _i(e,t,n,r){var o=vi();si.flags|=e,o.memoizedState=Ai(1|t,n,void 0,void 0===r?null:r)}function ji(e,t,n,r){var o=bi();r=void 0===r?null:r;var a=void 0;if(null!==li){var i=li.memoizedState;if(a=i.destroy,null!==r&&gi(r,i.deps))return void(o.memoizedState=Ai(t,n,a,r))}si.flags|=e,o.memoizedState=Ai(1|t,n,a,r)}function Di(e,t){return _i(8390656,8,e,t)}function Ii(e,t){return ji(2048,8,e,t)}function Fi(e,t){return ji(4,2,e,t)}function zi(e,t){return ji(4,4,e,t)}function Mi(e,t){return"function"===typeof t?(e=e(),t(e),function(){t(null)}):null!==t&&void 0!==t?(e=e(),t.current=e,function(){t.current=null}):void 0}function Bi(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,ji(4,4,Mi.bind(null,t,e),n)}function Ui(){}function Vi(e,t){var n=bi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&gi(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function Wi(e,t){var n=bi();t=void 0===t?null:t;var r=n.memoizedState;return null!==r&&null!==t&&gi(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function Hi(e,t,n){return 0===(21&ii)?(e.baseState&&(e.baseState=!1,bs=!0),e.memoizedState=n):(sr(n,t)||(n=gt(),si.lanes|=n,Il|=n,e.baseState=!0),t)}function $i(e,t){var n=bt;bt=0!==n&&4>n?n:4,e(!0);var r=ai.transition;ai.transition={};try{e(!1),t()}finally{bt=n,ai.transition=r}}function Ki(){return bi().memoizedState}function qi(e,t,n){var r=tu(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},Gi(e))Ji(t,n);else if(null!==(n=ja(e,t,n,r))){nu(n,e,r,eu()),Xi(n,t,r)}}function Qi(e,t,n){var r=tu(e),o={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(Gi(e))Ji(t,o);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var i=t.lastRenderedState,s=a(i,n);if(o.hasEagerState=!0,o.eagerState=s,sr(s,i)){var l=t.interleaved;return null===l?(o.next=o,_a(t)):(o.next=l.next,l.next=o),void(t.interleaved=o)}}catch(u){}null!==(n=ja(e,t,o,r))&&(nu(n,e,r,o=eu()),Xi(n,t,r))}}function Gi(e){var t=e.alternate;return e===si||null!==t&&t===si}function Ji(e,t){di=ci=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Xi(e,t,n){if(0!==(4194240&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,vt(e,n)}}var Yi={readContext:Aa,useCallback:hi,useContext:hi,useEffect:hi,useImperativeHandle:hi,useInsertionEffect:hi,useLayoutEffect:hi,useMemo:hi,useReducer:hi,useRef:hi,useState:hi,useDebugValue:hi,useDeferredValue:hi,useTransition:hi,useMutableSource:hi,useSyncExternalStore:hi,useId:hi,unstable_isNewReconciler:!1},Zi={readContext:Aa,useCallback:function(e,t){return vi().memoizedState=[e,void 0===t?null:t],e},useContext:Aa,useEffect:Di,useImperativeHandle:function(e,t,n){return n=null!==n&&void 0!==n?n.concat([e]):null,_i(4194308,4,Mi.bind(null,t,e),n)},useLayoutEffect:function(e,t){return _i(4194308,4,e,t)},useInsertionEffect:function(e,t){return _i(4,2,e,t)},useMemo:function(e,t){var n=vi();return t=void 0===t?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=vi();return t=void 0!==n?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=qi.bind(null,si,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},vi().memoizedState=e},useState:Ni,useDebugValue:Ui,useDeferredValue:function(e){return vi().memoizedState=e},useTransition:function(){var e=Ni(!1),t=e[0];return e=$i.bind(null,e[1]),vi().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=si,o=vi();if(aa){if(void 0===n)throw Error(a(407));n=n()}else{if(n=t(),null===Tl)throw Error(a(349));0!==(30&ii)||Ci(r,t,n)}o.memoizedState=n;var i={value:n,getSnapshot:t};return o.queue=i,Di(Oi.bind(null,r,i,e),[e]),r.flags|=2048,Ai(9,Pi.bind(null,r,i,n,t),void 0,null),n},useId:function(){var e=vi(),t=Tl.identifierPrefix;if(aa){var n=Yo;t=":"+t+"R"+(n=(Xo&~(1<<32-it(Xo)-1)).toString(32)+n),0<(n=fi++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=pi++).toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},es={readContext:Aa,useCallback:Vi,useContext:Aa,useEffect:Ii,useImperativeHandle:Bi,useInsertionEffect:Fi,useLayoutEffect:zi,useMemo:Wi,useReducer:ki,useRef:Li,useState:function(){return ki(wi)},useDebugValue:Ui,useDeferredValue:function(e){return Hi(bi(),li.memoizedState,e)},useTransition:function(){return[ki(wi)[0],bi().memoizedState]},useMutableSource:xi,useSyncExternalStore:Ei,useId:Ki,unstable_isNewReconciler:!1},ts={readContext:Aa,useCallback:Vi,useContext:Aa,useEffect:Ii,useImperativeHandle:Bi,useInsertionEffect:Fi,useLayoutEffect:zi,useMemo:Wi,useReducer:Si,useRef:Li,useState:function(){return Si(wi)},useDebugValue:Ui,useDeferredValue:function(e){var t=bi();return null===li?t.memoizedState=e:Hi(t,li.memoizedState,e)},useTransition:function(){return[Si(wi)[0],bi().memoizedState]},useMutableSource:xi,useSyncExternalStore:Ei,useId:Ki,unstable_isNewReconciler:!1};function ns(e,t){if(e&&e.defaultProps){for(var n in t=F({},t),e=e.defaultProps)void 0===t[n]&&(t[n]=e[n]);return t}return t}function rs(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:F({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}var os={isMounted:function(e){return!!(e=e._reactInternals)&&Ve(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=eu(),o=tu(e),a=Ma(r,o);a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=Ba(e,a,o))&&(nu(t,e,o,r),Ua(t,e,o))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=eu(),o=tu(e),a=Ma(r,o);a.tag=1,a.payload=t,void 0!==n&&null!==n&&(a.callback=n),null!==(t=Ba(e,a,o))&&(nu(t,e,o,r),Ua(t,e,o))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=eu(),r=tu(e),o=Ma(n,r);o.tag=2,void 0!==t&&null!==t&&(o.callback=t),null!==(t=Ba(e,o,r))&&(nu(t,e,r,n),Ua(t,e,r))}};function as(e,t,n,r,o,a,i){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,i):!t.prototype||!t.prototype.isPureReactComponent||(!lr(n,r)||!lr(o,a))}function is(e,t,n){var r=!1,o=Ro,a=t.contextType;return"object"===typeof a&&null!==a?a=Aa(a):(o=_o(t)?Ao:To.current,a=(r=null!==(r=t.contextTypes)&&void 0!==r)?Lo(e,o):Ro),t=new t(n,a),e.memoizedState=null!==t.state&&void 0!==t.state?t.state:null,t.updater=os,e.stateNode=t,t._reactInternals=e,r&&((e=e.stateNode).__reactInternalMemoizedUnmaskedChildContext=o,e.__reactInternalMemoizedMaskedChildContext=a),t}function ss(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&os.enqueueReplaceState(t,t.state,null)}function ls(e,t,n,r){var o=e.stateNode;o.props=n,o.state=e.memoizedState,o.refs={},Fa(e);var a=t.contextType;"object"===typeof a&&null!==a?o.context=Aa(a):(a=_o(t)?Ao:To.current,o.context=Lo(e,a)),o.state=e.memoizedState,"function"===typeof(a=t.getDerivedStateFromProps)&&(rs(e,t,a,n),o.state=e.memoizedState),"function"===typeof t.getDerivedStateFromProps||"function"===typeof o.getSnapshotBeforeUpdate||"function"!==typeof o.UNSAFE_componentWillMount&&"function"!==typeof o.componentWillMount||(t=o.state,"function"===typeof o.componentWillMount&&o.componentWillMount(),"function"===typeof o.UNSAFE_componentWillMount&&o.UNSAFE_componentWillMount(),t!==o.state&&os.enqueueReplaceState(o,o.state,null),Wa(e,n,o,r),o.state=e.memoizedState),"function"===typeof o.componentDidMount&&(e.flags|=4194308)}function us(e,t){try{var n="",r=t;do{n+=U(r),r=r.return}while(r);var o=n}catch(a){o="\nError generating stack: "+a.message+"\n"+a.stack}return{value:e,source:t,stack:o,digest:null}}function cs(e,t,n){return{value:e,source:null,stack:null!=n?n:null,digest:null!=t?t:null}}function ds(e,t){try{console.error(t.value)}catch(n){setTimeout((function(){throw n}))}}var fs="function"===typeof WeakMap?WeakMap:Map;function ps(e,t,n){(n=Ma(-1,n)).tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){Hl||(Hl=!0,$l=r),ds(0,t)},n}function hs(e,t,n){(n=Ma(-1,n)).tag=3;var r=e.type.getDerivedStateFromError;if("function"===typeof r){var o=t.value;n.payload=function(){return r(o)},n.callback=function(){ds(0,t)}}var a=e.stateNode;return null!==a&&"function"===typeof a.componentDidCatch&&(n.callback=function(){ds(0,t),"function"!==typeof r&&(null===Kl?Kl=new Set([this]):Kl.add(this));var e=t.stack;this.componentDidCatch(t.value,{componentStack:null!==e?e:""})}),n}function gs(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new fs;var o=new Set;r.set(t,o)}else void 0===(o=r.get(t))&&(o=new Set,r.set(t,o));o.has(n)||(o.add(n),e=Cu.bind(null,e,t,n),t.then(e,e))}function ms(e){do{var t;if((t=13===e.tag)&&(t=null===(t=e.memoizedState)||null!==t.dehydrated),t)return e;e=e.return}while(null!==e);return null}function ys(e,t,n,r,o){return 0===(1&e.mode)?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,1===n.tag&&(null===n.alternate?n.tag=17:((t=Ma(-1,1)).tag=2,Ba(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=o,e)}var vs=w.ReactCurrentOwner,bs=!1;function ws(e,t,n,r){t.child=null===e?Sa(t,null,n,r):ka(t,e.child,n,r)}function ks(e,t,n,r,o){n=n.render;var a=t.ref;return Na(t,o),r=mi(e,t,n,r,a,o),n=yi(),null===e||bs?(aa&&n&&ta(t),t.flags|=1,ws(e,t,r,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Hs(e,t,o))}function Ss(e,t,n,r,o){if(null===e){var a=n.type;return"function"!==typeof a||Lu(a)||void 0!==a.defaultProps||null!==n.compare||void 0!==n.defaultProps?((e=ju(n.type,null,r,t,t.mode,o)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,xs(e,t,a,r,o))}if(a=e.child,0===(e.lanes&o)){var i=a.memoizedProps;if((n=null!==(n=n.compare)?n:lr)(i,r)&&e.ref===t.ref)return Hs(e,t,o)}return t.flags|=1,(e=_u(a,r)).ref=t.ref,e.return=t,t.child=e}function xs(e,t,n,r,o){if(null!==e){var a=e.memoizedProps;if(lr(a,r)&&e.ref===t.ref){if(bs=!1,t.pendingProps=r=a,0===(e.lanes&o))return t.lanes=e.lanes,Hs(e,t,o);0!==(131072&e.flags)&&(bs=!0)}}return Ps(e,t,n,r,o)}function Es(e,t,n){var r=t.pendingProps,o=r.children,a=null!==e?e.memoizedState:null;if("hidden"===r.mode)if(0===(1&t.mode))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Oo(_l,Ll),Ll|=n;else{if(0===(1073741824&n))return e=null!==a?a.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Oo(_l,Ll),Ll|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=null!==a?a.baseLanes:n,Oo(_l,Ll),Ll|=r}else null!==a?(r=a.baseLanes|n,t.memoizedState=null):r=n,Oo(_l,Ll),Ll|=r;return ws(e,t,o,n),t.child}function Cs(e,t){var n=t.ref;(null===e&&null!==n||null!==e&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Ps(e,t,n,r,o){var a=_o(n)?Ao:To.current;return a=Lo(t,a),Na(t,o),n=mi(e,t,n,r,a,o),r=yi(),null===e||bs?(aa&&r&&ta(t),t.flags|=1,ws(e,t,n,o),t.child):(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~o,Hs(e,t,o))}function Os(e,t,n,r,o){if(_o(n)){var a=!0;Fo(t)}else a=!1;if(Na(t,o),null===t.stateNode)Ws(e,t),is(t,n,r),ls(t,n,r,o),r=!0;else if(null===e){var i=t.stateNode,s=t.memoizedProps;i.props=s;var l=i.context,u=n.contextType;"object"===typeof u&&null!==u?u=Aa(u):u=Lo(t,u=_o(n)?Ao:To.current);var c=n.getDerivedStateFromProps,d="function"===typeof c||"function"===typeof i.getSnapshotBeforeUpdate;d||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(s!==r||l!==u)&&ss(t,i,r,u),Ia=!1;var f=t.memoizedState;i.state=f,Wa(t,r,i,o),l=t.memoizedState,s!==r||f!==l||No.current||Ia?("function"===typeof c&&(rs(t,n,c,r),l=t.memoizedState),(s=Ia||as(t,n,s,r,f,l,u))?(d||"function"!==typeof i.UNSAFE_componentWillMount&&"function"!==typeof i.componentWillMount||("function"===typeof i.componentWillMount&&i.componentWillMount(),"function"===typeof i.UNSAFE_componentWillMount&&i.UNSAFE_componentWillMount()),"function"===typeof i.componentDidMount&&(t.flags|=4194308)):("function"===typeof i.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),i.props=r,i.state=l,i.context=u,r=s):("function"===typeof i.componentDidMount&&(t.flags|=4194308),r=!1)}else{i=t.stateNode,za(e,t),s=t.memoizedProps,u=t.type===t.elementType?s:ns(t.type,s),i.props=u,d=t.pendingProps,f=i.context,"object"===typeof(l=n.contextType)&&null!==l?l=Aa(l):l=Lo(t,l=_o(n)?Ao:To.current);var p=n.getDerivedStateFromProps;(c="function"===typeof p||"function"===typeof i.getSnapshotBeforeUpdate)||"function"!==typeof i.UNSAFE_componentWillReceiveProps&&"function"!==typeof i.componentWillReceiveProps||(s!==d||f!==l)&&ss(t,i,r,l),Ia=!1,f=t.memoizedState,i.state=f,Wa(t,r,i,o);var h=t.memoizedState;s!==d||f!==h||No.current||Ia?("function"===typeof p&&(rs(t,n,p,r),h=t.memoizedState),(u=Ia||as(t,n,u,r,f,h,l)||!1)?(c||"function"!==typeof i.UNSAFE_componentWillUpdate&&"function"!==typeof i.componentWillUpdate||("function"===typeof i.componentWillUpdate&&i.componentWillUpdate(r,h,l),"function"===typeof i.UNSAFE_componentWillUpdate&&i.UNSAFE_componentWillUpdate(r,h,l)),"function"===typeof i.componentDidUpdate&&(t.flags|=4),"function"===typeof i.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof i.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=h),i.props=r,i.state=h,i.context=l,r=u):("function"!==typeof i.componentDidUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof i.getSnapshotBeforeUpdate||s===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return Rs(e,t,n,r,a,o)}function Rs(e,t,n,r,o,a){Cs(e,t);var i=0!==(128&t.flags);if(!r&&!i)return o&&zo(t,n,!1),Hs(e,t,a);r=t.stateNode,vs.current=t;var s=i&&"function"!==typeof n.getDerivedStateFromError?null:r.render();return t.flags|=1,null!==e&&i?(t.child=ka(t,e.child,null,a),t.child=ka(t,null,s,a)):ws(e,t,s,a),t.memoizedState=r.state,o&&zo(t,n,!0),t.child}function Ts(e){var t=e.stateNode;t.pendingContext?Do(0,t.pendingContext,t.pendingContext!==t.context):t.context&&Do(0,t.context,!1),Ja(e,t.containerInfo)}function Ns(e,t,n,r,o){return ha(),ga(o),t.flags|=256,ws(e,t,n,r),t.child}var As,Ls,_s,js,Ds={dehydrated:null,treeContext:null,retryLane:0};function Is(e){return{baseLanes:e,cachePool:null,transitions:null}}function Fs(e,t,n){var r,o=t.pendingProps,i=ei.current,s=!1,l=0!==(128&t.flags);if((r=l)||(r=(null===e||null!==e.memoizedState)&&0!==(2&i)),r?(s=!0,t.flags&=-129):null!==e&&null===e.memoizedState||(i|=1),Oo(ei,1&i),null===e)return ca(t),null!==(e=t.memoizedState)&&null!==(e=e.dehydrated)?(0===(1&t.mode)?t.lanes=1:"$!"===e.data?t.lanes=8:t.lanes=1073741824,null):(l=o.children,e=o.fallback,s?(o=t.mode,s=t.child,l={mode:"hidden",children:l},0===(1&o)&&null!==s?(s.childLanes=0,s.pendingProps=l):s=Iu(l,o,0,null),e=Du(e,o,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Is(n),t.memoizedState=Ds,e):zs(t,l));if(null!==(i=e.memoizedState)&&null!==(r=i.dehydrated))return function(e,t,n,r,o,i,s){if(n)return 256&t.flags?(t.flags&=-257,Ms(e,t,s,r=cs(Error(a(422))))):null!==t.memoizedState?(t.child=e.child,t.flags|=128,null):(i=r.fallback,o=t.mode,r=Iu({mode:"visible",children:r.children},o,0,null),(i=Du(i,o,s,null)).flags|=2,r.return=t,i.return=t,r.sibling=i,t.child=r,0!==(1&t.mode)&&ka(t,e.child,null,s),t.child.memoizedState=Is(s),t.memoizedState=Ds,i);if(0===(1&t.mode))return Ms(e,t,s,null);if("$!"===o.data){if(r=o.nextSibling&&o.nextSibling.dataset)var l=r.dgst;return r=l,Ms(e,t,s,r=cs(i=Error(a(419)),r,void 0))}if(l=0!==(s&e.childLanes),bs||l){if(null!==(r=Tl)){switch(s&-s){case 4:o=2;break;case 16:o=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:o=32;break;case 536870912:o=268435456;break;default:o=0}0!==(o=0!==(o&(r.suspendedLanes|s))?0:o)&&o!==i.retryLane&&(i.retryLane=o,Da(e,o),nu(r,e,o,-1))}return gu(),Ms(e,t,s,r=cs(Error(a(421))))}return"$?"===o.data?(t.flags|=128,t.child=e.child,t=Ou.bind(null,e),o._reactRetry=t,null):(e=i.treeContext,oa=uo(o.nextSibling),ra=t,aa=!0,ia=null,null!==e&&(Qo[Go++]=Xo,Qo[Go++]=Yo,Qo[Go++]=Jo,Xo=e.id,Yo=e.overflow,Jo=t),t=zs(t,r.children),t.flags|=4096,t)}(e,t,l,o,r,i,n);if(s){s=o.fallback,l=t.mode,r=(i=e.child).sibling;var u={mode:"hidden",children:o.children};return 0===(1&l)&&t.child!==i?((o=t.child).childLanes=0,o.pendingProps=u,t.deletions=null):(o=_u(i,u)).subtreeFlags=14680064&i.subtreeFlags,null!==r?s=_u(r,s):(s=Du(s,l,n,null)).flags|=2,s.return=t,o.return=t,o.sibling=s,t.child=o,o=s,s=t.child,l=null===(l=e.child.memoizedState)?Is(n):{baseLanes:l.baseLanes|n,cachePool:null,transitions:l.transitions},s.memoizedState=l,s.childLanes=e.childLanes&~n,t.memoizedState=Ds,o}return e=(s=e.child).sibling,o=_u(s,{mode:"visible",children:o.children}),0===(1&t.mode)&&(o.lanes=n),o.return=t,o.sibling=null,null!==e&&(null===(n=t.deletions)?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=o,t.memoizedState=null,o}function zs(e,t){return(t=Iu({mode:"visible",children:t},e.mode,0,null)).return=e,e.child=t}function Ms(e,t,n,r){return null!==r&&ga(r),ka(t,e.child,null,n),(e=zs(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function Bs(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),Ta(e.return,t,n)}function Us(e,t,n,r,o){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=o)}function Vs(e,t,n){var r=t.pendingProps,o=r.revealOrder,a=r.tail;if(ws(e,t,r.children,n),0!==(2&(r=ei.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&Bs(e,n,t);else if(19===e.tag)Bs(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(Oo(ei,r),0===(1&t.mode))t.memoizedState=null;else switch(o){case"forwards":for(n=t.child,o=null;null!==n;)null!==(e=n.alternate)&&null===ti(e)&&(o=n),n=n.sibling;null===(n=o)?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),Us(t,!1,o,n,a);break;case"backwards":for(n=null,o=t.child,t.child=null;null!==o;){if(null!==(e=o.alternate)&&null===ti(e)){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}Us(t,!0,n,null,a);break;case"together":Us(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ws(e,t){0===(1&t.mode)&&null!==e&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Hs(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),Il|=t.lanes,0===(n&t.childLanes))return null;if(null!==e&&t.child!==e.child)throw Error(a(153));if(null!==t.child){for(n=_u(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=_u(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function $s(e,t){if(!aa)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Ks(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=14680064&o.subtreeFlags,r|=14680064&o.flags,o.return=e,o=o.sibling;else for(o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function qs(e,t,n){var r=t.pendingProps;switch(na(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Ks(t),null;case 1:case 17:return _o(t.type)&&jo(),Ks(t),null;case 3:return r=t.stateNode,Xa(),Po(No),Po(To),ri(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),null!==e&&null!==e.child||(fa(t)?t.flags|=4:null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==ia&&(iu(ia),ia=null))),Ls(e,t),Ks(t),null;case 5:Za(t);var o=Ga(Qa.current);if(n=t.type,null!==e&&null!=t.stateNode)_s(e,t,n,r,o),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(null===t.stateNode)throw Error(a(166));return Ks(t),null}if(e=Ga(Ka.current),fa(t)){r=t.stateNode,n=t.type;var i=t.memoizedProps;switch(r[po]=t,r[ho]=i,e=0!==(1&t.mode),n){case"dialog":Mr("cancel",r),Mr("close",r);break;case"iframe":case"object":case"embed":Mr("load",r);break;case"video":case"audio":for(o=0;o<Dr.length;o++)Mr(Dr[o],r);break;case"source":Mr("error",r);break;case"img":case"image":case"link":Mr("error",r),Mr("load",r);break;case"details":Mr("toggle",r);break;case"input":J(r,i),Mr("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!i.multiple},Mr("invalid",r);break;case"textarea":oe(r,i),Mr("invalid",r)}for(var l in ve(n,i),o=null,i)if(i.hasOwnProperty(l)){var u=i[l];"children"===l?"string"===typeof u?r.textContent!==u&&(!0!==i.suppressHydrationWarning&&Yr(r.textContent,u,e),o=["children",u]):"number"===typeof u&&r.textContent!==""+u&&(!0!==i.suppressHydrationWarning&&Yr(r.textContent,u,e),o=["children",""+u]):s.hasOwnProperty(l)&&null!=u&&"onScroll"===l&&Mr("scroll",r)}switch(n){case"input":K(r),Z(r,i,!0);break;case"textarea":K(r),ie(r);break;case"select":case"option":break;default:"function"===typeof i.onClick&&(r.onclick=Zr)}r=o,t.updateQueue=r,null!==r&&(t.flags|=4)}else{l=9===o.nodeType?o:o.ownerDocument,"http://www.w3.org/1999/xhtml"===e&&(e=se(n)),"http://www.w3.org/1999/xhtml"===e?"script"===n?((e=l.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):"string"===typeof r.is?e=l.createElement(n,{is:r.is}):(e=l.createElement(n),"select"===n&&(l=e,r.multiple?l.multiple=!0:r.size&&(l.size=r.size))):e=l.createElementNS(e,n),e[po]=t,e[ho]=r,As(e,t,!1,!1),t.stateNode=e;e:{switch(l=be(n,r),n){case"dialog":Mr("cancel",e),Mr("close",e),o=r;break;case"iframe":case"object":case"embed":Mr("load",e),o=r;break;case"video":case"audio":for(o=0;o<Dr.length;o++)Mr(Dr[o],e);o=r;break;case"source":Mr("error",e),o=r;break;case"img":case"image":case"link":Mr("error",e),Mr("load",e),o=r;break;case"details":Mr("toggle",e),o=r;break;case"input":J(e,r),o=G(e,r),Mr("invalid",e);break;case"option":default:o=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},o=F({},r,{value:void 0}),Mr("invalid",e);break;case"textarea":oe(e,r),o=re(e,r),Mr("invalid",e)}for(i in ve(n,o),u=o)if(u.hasOwnProperty(i)){var c=u[i];"style"===i?me(e,c):"dangerouslySetInnerHTML"===i?null!=(c=c?c.__html:void 0)&&de(e,c):"children"===i?"string"===typeof c?("textarea"!==n||""!==c)&&fe(e,c):"number"===typeof c&&fe(e,""+c):"suppressContentEditableWarning"!==i&&"suppressHydrationWarning"!==i&&"autoFocus"!==i&&(s.hasOwnProperty(i)?null!=c&&"onScroll"===i&&Mr("scroll",e):null!=c&&b(e,i,c,l))}switch(n){case"input":K(e),Z(e,r,!1);break;case"textarea":K(e),ie(e);break;case"option":null!=r.value&&e.setAttribute("value",""+H(r.value));break;case"select":e.multiple=!!r.multiple,null!=(i=r.value)?ne(e,!!r.multiple,i,!1):null!=r.defaultValue&&ne(e,!!r.multiple,r.defaultValue,!0);break;default:"function"===typeof o.onClick&&(e.onclick=Zr)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}null!==t.ref&&(t.flags|=512,t.flags|=2097152)}return Ks(t),null;case 6:if(e&&null!=t.stateNode)js(e,t,e.memoizedProps,r);else{if("string"!==typeof r&&null===t.stateNode)throw Error(a(166));if(n=Ga(Qa.current),Ga(Ka.current),fa(t)){if(r=t.stateNode,n=t.memoizedProps,r[po]=t,(i=r.nodeValue!==n)&&null!==(e=ra))switch(e.tag){case 3:Yr(r.nodeValue,n,0!==(1&e.mode));break;case 5:!0!==e.memoizedProps.suppressHydrationWarning&&Yr(r.nodeValue,n,0!==(1&e.mode))}i&&(t.flags|=4)}else(r=(9===n.nodeType?n:n.ownerDocument).createTextNode(r))[po]=t,t.stateNode=r}return Ks(t),null;case 13:if(Po(ei),r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(aa&&null!==oa&&0!==(1&t.mode)&&0===(128&t.flags))pa(),ha(),t.flags|=98560,i=!1;else if(i=fa(t),null!==r&&null!==r.dehydrated){if(null===e){if(!i)throw Error(a(318));if(!(i=null!==(i=t.memoizedState)?i.dehydrated:null))throw Error(a(317));i[po]=t}else ha(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Ks(t),i=!1}else null!==ia&&(iu(ia),ia=null),i=!0;if(!i)return 65536&t.flags?t:null}return 0!==(128&t.flags)?(t.lanes=n,t):((r=null!==r)!==(null!==e&&null!==e.memoizedState)&&r&&(t.child.flags|=8192,0!==(1&t.mode)&&(null===e||0!==(1&ei.current)?0===jl&&(jl=3):gu())),null!==t.updateQueue&&(t.flags|=4),Ks(t),null);case 4:return Xa(),Ls(e,t),null===e&&Vr(t.stateNode.containerInfo),Ks(t),null;case 10:return Ra(t.type._context),Ks(t),null;case 19:if(Po(ei),null===(i=t.memoizedState))return Ks(t),null;if(r=0!==(128&t.flags),null===(l=i.rendering))if(r)$s(i,!1);else{if(0!==jl||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(l=ti(e))){for(t.flags|=128,$s(i,!1),null!==(r=l.updateQueue)&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;null!==n;)e=r,(i=n).flags&=14680066,null===(l=i.alternate)?(i.childLanes=0,i.lanes=e,i.child=null,i.subtreeFlags=0,i.memoizedProps=null,i.memoizedState=null,i.updateQueue=null,i.dependencies=null,i.stateNode=null):(i.childLanes=l.childLanes,i.lanes=l.lanes,i.child=l.child,i.subtreeFlags=0,i.deletions=null,i.memoizedProps=l.memoizedProps,i.memoizedState=l.memoizedState,i.updateQueue=l.updateQueue,i.type=l.type,e=l.dependencies,i.dependencies=null===e?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Oo(ei,1&ei.current|2),t.child}e=e.sibling}null!==i.tail&&Xe()>Vl&&(t.flags|=128,r=!0,$s(i,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=ti(l))){if(t.flags|=128,r=!0,null!==(n=e.updateQueue)&&(t.updateQueue=n,t.flags|=4),$s(i,!0),null===i.tail&&"hidden"===i.tailMode&&!l.alternate&&!aa)return Ks(t),null}else 2*Xe()-i.renderingStartTime>Vl&&1073741824!==n&&(t.flags|=128,r=!0,$s(i,!1),t.lanes=4194304);i.isBackwards?(l.sibling=t.child,t.child=l):(null!==(n=i.last)?n.sibling=l:t.child=l,i.last=l)}return null!==i.tail?(t=i.tail,i.rendering=t,i.tail=t.sibling,i.renderingStartTime=Xe(),t.sibling=null,n=ei.current,Oo(ei,r?1&n|2:1&n),t):(Ks(t),null);case 22:case 23:return du(),r=null!==t.memoizedState,null!==e&&null!==e.memoizedState!==r&&(t.flags|=8192),r&&0!==(1&t.mode)?0!==(1073741824&Ll)&&(Ks(t),6&t.subtreeFlags&&(t.flags|=8192)):Ks(t),null;case 24:case 25:return null}throw Error(a(156,t.tag))}function Qs(e,t){switch(na(t),t.tag){case 1:return _o(t.type)&&jo(),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return Xa(),Po(No),Po(To),ri(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 5:return Za(t),null;case 13:if(Po(ei),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(a(340));ha()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return Po(ei),null;case 4:return Xa(),null;case 10:return Ra(t.type._context),null;case 22:case 23:return du(),null;default:return null}}As=function(e,t){for(var n=t.child;null!==n;){if(5===n.tag||6===n.tag)e.appendChild(n.stateNode);else if(4!==n.tag&&null!==n.child){n.child.return=n,n=n.child;continue}if(n===t)break;for(;null===n.sibling;){if(null===n.return||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},Ls=function(){},_s=function(e,t,n,r){var o=e.memoizedProps;if(o!==r){e=t.stateNode,Ga(Ka.current);var a,i=null;switch(n){case"input":o=G(e,o),r=G(e,r),i=[];break;case"select":o=F({},o,{value:void 0}),r=F({},r,{value:void 0}),i=[];break;case"textarea":o=re(e,o),r=re(e,r),i=[];break;default:"function"!==typeof o.onClick&&"function"===typeof r.onClick&&(e.onclick=Zr)}for(c in ve(n,r),n=null,o)if(!r.hasOwnProperty(c)&&o.hasOwnProperty(c)&&null!=o[c])if("style"===c){var l=o[c];for(a in l)l.hasOwnProperty(a)&&(n||(n={}),n[a]="")}else"dangerouslySetInnerHTML"!==c&&"children"!==c&&"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&"autoFocus"!==c&&(s.hasOwnProperty(c)?i||(i=[]):(i=i||[]).push(c,null));for(c in r){var u=r[c];if(l=null!=o?o[c]:void 0,r.hasOwnProperty(c)&&u!==l&&(null!=u||null!=l))if("style"===c)if(l){for(a in l)!l.hasOwnProperty(a)||u&&u.hasOwnProperty(a)||(n||(n={}),n[a]="");for(a in u)u.hasOwnProperty(a)&&l[a]!==u[a]&&(n||(n={}),n[a]=u[a])}else n||(i||(i=[]),i.push(c,n)),n=u;else"dangerouslySetInnerHTML"===c?(u=u?u.__html:void 0,l=l?l.__html:void 0,null!=u&&l!==u&&(i=i||[]).push(c,u)):"children"===c?"string"!==typeof u&&"number"!==typeof u||(i=i||[]).push(c,""+u):"suppressContentEditableWarning"!==c&&"suppressHydrationWarning"!==c&&(s.hasOwnProperty(c)?(null!=u&&"onScroll"===c&&Mr("scroll",e),i||l===u||(i=[])):(i=i||[]).push(c,u))}n&&(i=i||[]).push("style",n);var c=i;(t.updateQueue=c)&&(t.flags|=4)}},js=function(e,t,n,r){n!==r&&(t.flags|=4)};var Gs=!1,Js=!1,Xs="function"===typeof WeakSet?WeakSet:Set,Ys=null;function Zs(e,t){var n=e.ref;if(null!==n)if("function"===typeof n)try{n(null)}catch(r){Eu(e,t,r)}else n.current=null}function el(e,t,n){try{n()}catch(r){Eu(e,t,r)}}var tl=!1;function nl(e,t,n){var r=t.updateQueue;if(null!==(r=null!==r?r.lastEffect:null)){var o=r=r.next;do{if((o.tag&e)===e){var a=o.destroy;o.destroy=void 0,void 0!==a&&el(t,n,a)}o=o.next}while(o!==r)}}function rl(e,t){if(null!==(t=null!==(t=t.updateQueue)?t.lastEffect:null)){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function ol(e){var t=e.ref;if(null!==t){var n=e.stateNode;e.tag,e=n,"function"===typeof t?t(e):t.current=e}}function al(e){var t=e.alternate;null!==t&&(e.alternate=null,al(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&(delete t[po],delete t[ho],delete t[mo],delete t[yo],delete t[vo])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function il(e){return 5===e.tag||3===e.tag||4===e.tag}function sl(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||il(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function ll(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Zr));else if(4!==r&&null!==(e=e.child))for(ll(e,t,n),e=e.sibling;null!==e;)ll(e,t,n),e=e.sibling}function ul(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&null!==(e=e.child))for(ul(e,t,n),e=e.sibling;null!==e;)ul(e,t,n),e=e.sibling}var cl=null,dl=!1;function fl(e,t,n){for(n=n.child;null!==n;)pl(e,t,n),n=n.sibling}function pl(e,t,n){if(at&&"function"===typeof at.onCommitFiberUnmount)try{at.onCommitFiberUnmount(ot,n)}catch(s){}switch(n.tag){case 5:Js||Zs(n,t);case 6:var r=cl,o=dl;cl=null,fl(e,t,n),dl=o,null!==(cl=r)&&(dl?(e=cl,n=n.stateNode,8===e.nodeType?e.parentNode.removeChild(n):e.removeChild(n)):cl.removeChild(n.stateNode));break;case 18:null!==cl&&(dl?(e=cl,n=n.stateNode,8===e.nodeType?lo(e.parentNode,n):1===e.nodeType&&lo(e,n),Vt(e)):lo(cl,n.stateNode));break;case 4:r=cl,o=dl,cl=n.stateNode.containerInfo,dl=!0,fl(e,t,n),cl=r,dl=o;break;case 0:case 11:case 14:case 15:if(!Js&&(null!==(r=n.updateQueue)&&null!==(r=r.lastEffect))){o=r=r.next;do{var a=o,i=a.destroy;a=a.tag,void 0!==i&&(0!==(2&a)||0!==(4&a))&&el(n,t,i),o=o.next}while(o!==r)}fl(e,t,n);break;case 1:if(!Js&&(Zs(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(s){Eu(n,t,s)}fl(e,t,n);break;case 21:fl(e,t,n);break;case 22:1&n.mode?(Js=(r=Js)||null!==n.memoizedState,fl(e,t,n),Js=r):fl(e,t,n);break;default:fl(e,t,n)}}function hl(e){var t=e.updateQueue;if(null!==t){e.updateQueue=null;var n=e.stateNode;null===n&&(n=e.stateNode=new Xs),t.forEach((function(t){var r=Ru.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}}function gl(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var o=n[r];try{var i=e,s=t,l=s;e:for(;null!==l;){switch(l.tag){case 5:cl=l.stateNode,dl=!1;break e;case 3:case 4:cl=l.stateNode.containerInfo,dl=!0;break e}l=l.return}if(null===cl)throw Error(a(160));pl(i,s,o),cl=null,dl=!1;var u=o.alternate;null!==u&&(u.return=null),o.return=null}catch(c){Eu(o,t,c)}}if(12854&t.subtreeFlags)for(t=t.child;null!==t;)ml(t,e),t=t.sibling}function ml(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(gl(t,e),yl(e),4&r){try{nl(3,e,e.return),rl(3,e)}catch(m){Eu(e,e.return,m)}try{nl(5,e,e.return)}catch(m){Eu(e,e.return,m)}}break;case 1:gl(t,e),yl(e),512&r&&null!==n&&Zs(n,n.return);break;case 5:if(gl(t,e),yl(e),512&r&&null!==n&&Zs(n,n.return),32&e.flags){var o=e.stateNode;try{fe(o,"")}catch(m){Eu(e,e.return,m)}}if(4&r&&null!=(o=e.stateNode)){var i=e.memoizedProps,s=null!==n?n.memoizedProps:i,l=e.type,u=e.updateQueue;if(e.updateQueue=null,null!==u)try{"input"===l&&"radio"===i.type&&null!=i.name&&X(o,i),be(l,s);var c=be(l,i);for(s=0;s<u.length;s+=2){var d=u[s],f=u[s+1];"style"===d?me(o,f):"dangerouslySetInnerHTML"===d?de(o,f):"children"===d?fe(o,f):b(o,d,f,c)}switch(l){case"input":Y(o,i);break;case"textarea":ae(o,i);break;case"select":var p=o._wrapperState.wasMultiple;o._wrapperState.wasMultiple=!!i.multiple;var h=i.value;null!=h?ne(o,!!i.multiple,h,!1):p!==!!i.multiple&&(null!=i.defaultValue?ne(o,!!i.multiple,i.defaultValue,!0):ne(o,!!i.multiple,i.multiple?[]:"",!1))}o[ho]=i}catch(m){Eu(e,e.return,m)}}break;case 6:if(gl(t,e),yl(e),4&r){if(null===e.stateNode)throw Error(a(162));o=e.stateNode,i=e.memoizedProps;try{o.nodeValue=i}catch(m){Eu(e,e.return,m)}}break;case 3:if(gl(t,e),yl(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{Vt(t.containerInfo)}catch(m){Eu(e,e.return,m)}break;case 4:default:gl(t,e),yl(e);break;case 13:gl(t,e),yl(e),8192&(o=e.child).flags&&(i=null!==o.memoizedState,o.stateNode.isHidden=i,!i||null!==o.alternate&&null!==o.alternate.memoizedState||(Ul=Xe())),4&r&&hl(e);break;case 22:if(d=null!==n&&null!==n.memoizedState,1&e.mode?(Js=(c=Js)||d,gl(t,e),Js=c):gl(t,e),yl(e),8192&r){if(c=null!==e.memoizedState,(e.stateNode.isHidden=c)&&!d&&0!==(1&e.mode))for(Ys=e,d=e.child;null!==d;){for(f=Ys=d;null!==Ys;){switch(h=(p=Ys).child,p.tag){case 0:case 11:case 14:case 15:nl(4,p,p.return);break;case 1:Zs(p,p.return);var g=p.stateNode;if("function"===typeof g.componentWillUnmount){r=p,n=p.return;try{t=r,g.props=t.memoizedProps,g.state=t.memoizedState,g.componentWillUnmount()}catch(m){Eu(r,n,m)}}break;case 5:Zs(p,p.return);break;case 22:if(null!==p.memoizedState){kl(f);continue}}null!==h?(h.return=p,Ys=h):kl(f)}d=d.sibling}e:for(d=null,f=e;;){if(5===f.tag){if(null===d){d=f;try{o=f.stateNode,c?"function"===typeof(i=o.style).setProperty?i.setProperty("display","none","important"):i.display="none":(l=f.stateNode,s=void 0!==(u=f.memoizedProps.style)&&null!==u&&u.hasOwnProperty("display")?u.display:null,l.style.display=ge("display",s))}catch(m){Eu(e,e.return,m)}}}else if(6===f.tag){if(null===d)try{f.stateNode.nodeValue=c?"":f.memoizedProps}catch(m){Eu(e,e.return,m)}}else if((22!==f.tag&&23!==f.tag||null===f.memoizedState||f===e)&&null!==f.child){f.child.return=f,f=f.child;continue}if(f===e)break e;for(;null===f.sibling;){if(null===f.return||f.return===e)break e;d===f&&(d=null),f=f.return}d===f&&(d=null),f.sibling.return=f.return,f=f.sibling}}break;case 19:gl(t,e),yl(e),4&r&&hl(e);case 21:}}function yl(e){var t=e.flags;if(2&t){try{e:{for(var n=e.return;null!==n;){if(il(n)){var r=n;break e}n=n.return}throw Error(a(160))}switch(r.tag){case 5:var o=r.stateNode;32&r.flags&&(fe(o,""),r.flags&=-33),ul(e,sl(e),o);break;case 3:case 4:var i=r.stateNode.containerInfo;ll(e,sl(e),i);break;default:throw Error(a(161))}}catch(s){Eu(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function vl(e,t,n){Ys=e,bl(e,t,n)}function bl(e,t,n){for(var r=0!==(1&e.mode);null!==Ys;){var o=Ys,a=o.child;if(22===o.tag&&r){var i=null!==o.memoizedState||Gs;if(!i){var s=o.alternate,l=null!==s&&null!==s.memoizedState||Js;s=Gs;var u=Js;if(Gs=i,(Js=l)&&!u)for(Ys=o;null!==Ys;)l=(i=Ys).child,22===i.tag&&null!==i.memoizedState?Sl(o):null!==l?(l.return=i,Ys=l):Sl(o);for(;null!==a;)Ys=a,bl(a,t,n),a=a.sibling;Ys=o,Gs=s,Js=u}wl(e)}else 0!==(8772&o.subtreeFlags)&&null!==a?(a.return=o,Ys=a):wl(e)}}function wl(e){for(;null!==Ys;){var t=Ys;if(0!==(8772&t.flags)){var n=t.alternate;try{if(0!==(8772&t.flags))switch(t.tag){case 0:case 11:case 15:Js||rl(5,t);break;case 1:var r=t.stateNode;if(4&t.flags&&!Js)if(null===n)r.componentDidMount();else{var o=t.elementType===t.type?n.memoizedProps:ns(t.type,n.memoizedProps);r.componentDidUpdate(o,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var i=t.updateQueue;null!==i&&Ha(t,i,r);break;case 3:var s=t.updateQueue;if(null!==s){if(n=null,null!==t.child)switch(t.child.tag){case 5:case 1:n=t.child.stateNode}Ha(t,s,n)}break;case 5:var l=t.stateNode;if(null===n&&4&t.flags){n=l;var u=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":u.autoFocus&&n.focus();break;case"img":u.src&&(n.src=u.src)}}break;case 6:case 4:case 12:case 19:case 17:case 21:case 22:case 23:case 25:break;case 13:if(null===t.memoizedState){var c=t.alternate;if(null!==c){var d=c.memoizedState;if(null!==d){var f=d.dehydrated;null!==f&&Vt(f)}}}break;default:throw Error(a(163))}Js||512&t.flags&&ol(t)}catch(p){Eu(t,t.return,p)}}if(t===e){Ys=null;break}if(null!==(n=t.sibling)){n.return=t.return,Ys=n;break}Ys=t.return}}function kl(e){for(;null!==Ys;){var t=Ys;if(t===e){Ys=null;break}var n=t.sibling;if(null!==n){n.return=t.return,Ys=n;break}Ys=t.return}}function Sl(e){for(;null!==Ys;){var t=Ys;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{rl(4,t)}catch(l){Eu(t,n,l)}break;case 1:var r=t.stateNode;if("function"===typeof r.componentDidMount){var o=t.return;try{r.componentDidMount()}catch(l){Eu(t,o,l)}}var a=t.return;try{ol(t)}catch(l){Eu(t,a,l)}break;case 5:var i=t.return;try{ol(t)}catch(l){Eu(t,i,l)}}}catch(l){Eu(t,t.return,l)}if(t===e){Ys=null;break}var s=t.sibling;if(null!==s){s.return=t.return,Ys=s;break}Ys=t.return}}var xl,El=Math.ceil,Cl=w.ReactCurrentDispatcher,Pl=w.ReactCurrentOwner,Ol=w.ReactCurrentBatchConfig,Rl=0,Tl=null,Nl=null,Al=0,Ll=0,_l=Co(0),jl=0,Dl=null,Il=0,Fl=0,zl=0,Ml=null,Bl=null,Ul=0,Vl=1/0,Wl=null,Hl=!1,$l=null,Kl=null,ql=!1,Ql=null,Gl=0,Jl=0,Xl=null,Yl=-1,Zl=0;function eu(){return 0!==(6&Rl)?Xe():-1!==Yl?Yl:Yl=Xe()}function tu(e){return 0===(1&e.mode)?1:0!==(2&Rl)&&0!==Al?Al&-Al:null!==ma.transition?(0===Zl&&(Zl=gt()),Zl):0!==(e=bt)?e:e=void 0===(e=window.event)?16:Jt(e.type)}function nu(e,t,n,r){if(50<Jl)throw Jl=0,Xl=null,Error(a(185));yt(e,n,r),0!==(2&Rl)&&e===Tl||(e===Tl&&(0===(2&Rl)&&(Fl|=n),4===jl&&su(e,Al)),ru(e,r),1===n&&0===Rl&&0===(1&t.mode)&&(Vl=Xe()+500,Bo&&Wo()))}function ru(e,t){var n=e.callbackNode;!function(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,a=e.pendingLanes;0<a;){var i=31-it(a),s=1<<i,l=o[i];-1===l?0!==(s&n)&&0===(s&r)||(o[i]=pt(s,t)):l<=t&&(e.expiredLanes|=s),a&=~s}}(e,t);var r=ft(e,e===Tl?Al:0);if(0===r)null!==n&&Qe(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(null!=n&&Qe(n),1===t)0===e.tag?function(e){Bo=!0,Vo(e)}(lu.bind(null,e)):Vo(lu.bind(null,e)),io((function(){0===(6&Rl)&&Wo()})),n=null;else{switch(wt(r)){case 1:n=Ze;break;case 4:n=et;break;case 16:default:n=tt;break;case 536870912:n=rt}n=Tu(n,ou.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function ou(e,t){if(Yl=-1,Zl=0,0!==(6&Rl))throw Error(a(327));var n=e.callbackNode;if(Su()&&e.callbackNode!==n)return null;var r=ft(e,e===Tl?Al:0);if(0===r)return null;if(0!==(30&r)||0!==(r&e.expiredLanes)||t)t=mu(e,r);else{t=r;var o=Rl;Rl|=2;var i=hu();for(Tl===e&&Al===t||(Wl=null,Vl=Xe()+500,fu(e,t));;)try{vu();break}catch(l){pu(e,l)}Oa(),Cl.current=i,Rl=o,null!==Nl?t=0:(Tl=null,Al=0,t=jl)}if(0!==t){if(2===t&&(0!==(o=ht(e))&&(r=o,t=au(e,o))),1===t)throw n=Dl,fu(e,0),su(e,r),ru(e,Xe()),n;if(6===t)su(e,r);else{if(o=e.current.alternate,0===(30&r)&&!function(e){for(var t=e;;){if(16384&t.flags){var n=t.updateQueue;if(null!==n&&null!==(n=n.stores))for(var r=0;r<n.length;r++){var o=n[r],a=o.getSnapshot;o=o.value;try{if(!sr(a(),o))return!1}catch(s){return!1}}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}(o)&&(2===(t=mu(e,r))&&(0!==(i=ht(e))&&(r=i,t=au(e,i))),1===t))throw n=Dl,fu(e,0),su(e,r),ru(e,Xe()),n;switch(e.finishedWork=o,e.finishedLanes=r,t){case 0:case 1:throw Error(a(345));case 2:case 5:ku(e,Bl,Wl);break;case 3:if(su(e,r),(130023424&r)===r&&10<(t=Ul+500-Xe())){if(0!==ft(e,0))break;if(((o=e.suspendedLanes)&r)!==r){eu(),e.pingedLanes|=e.suspendedLanes&o;break}e.timeoutHandle=ro(ku.bind(null,e,Bl,Wl),t);break}ku(e,Bl,Wl);break;case 4:if(su(e,r),(4194240&r)===r)break;for(t=e.eventTimes,o=-1;0<r;){var s=31-it(r);i=1<<s,(s=t[s])>o&&(o=s),r&=~i}if(r=o,10<(r=(120>(r=Xe()-r)?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*El(r/1960))-r)){e.timeoutHandle=ro(ku.bind(null,e,Bl,Wl),r);break}ku(e,Bl,Wl);break;default:throw Error(a(329))}}}return ru(e,Xe()),e.callbackNode===n?ou.bind(null,e):null}function au(e,t){var n=Ml;return e.current.memoizedState.isDehydrated&&(fu(e,t).flags|=256),2!==(e=mu(e,t))&&(t=Bl,Bl=n,null!==t&&iu(t)),e}function iu(e){null===Bl?Bl=e:Bl.push.apply(Bl,e)}function su(e,t){for(t&=~zl,t&=~Fl,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-it(t),r=1<<n;e[n]=-1,t&=~r}}function lu(e){if(0!==(6&Rl))throw Error(a(327));Su();var t=ft(e,0);if(0===(1&t))return ru(e,Xe()),null;var n=mu(e,t);if(0!==e.tag&&2===n){var r=ht(e);0!==r&&(t=r,n=au(e,r))}if(1===n)throw n=Dl,fu(e,0),su(e,t),ru(e,Xe()),n;if(6===n)throw Error(a(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,ku(e,Bl,Wl),ru(e,Xe()),null}function uu(e,t){var n=Rl;Rl|=1;try{return e(t)}finally{0===(Rl=n)&&(Vl=Xe()+500,Bo&&Wo())}}function cu(e){null!==Ql&&0===Ql.tag&&0===(6&Rl)&&Su();var t=Rl;Rl|=1;var n=Ol.transition,r=bt;try{if(Ol.transition=null,bt=1,e)return e()}finally{bt=r,Ol.transition=n,0===(6&(Rl=t))&&Wo()}}function du(){Ll=_l.current,Po(_l)}function fu(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(-1!==n&&(e.timeoutHandle=-1,oo(n)),null!==Nl)for(n=Nl.return;null!==n;){var r=n;switch(na(r),r.tag){case 1:null!==(r=r.type.childContextTypes)&&void 0!==r&&jo();break;case 3:Xa(),Po(No),Po(To),ri();break;case 5:Za(r);break;case 4:Xa();break;case 13:case 19:Po(ei);break;case 10:Ra(r.type._context);break;case 22:case 23:du()}n=n.return}if(Tl=e,Nl=e=_u(e.current,null),Al=Ll=t,jl=0,Dl=null,zl=Fl=Il=0,Bl=Ml=null,null!==La){for(t=0;t<La.length;t++)if(null!==(r=(n=La[t]).interleaved)){n.interleaved=null;var o=r.next,a=n.pending;if(null!==a){var i=a.next;a.next=o,r.next=i}n.pending=r}La=null}return e}function pu(e,t){for(;;){var n=Nl;try{if(Oa(),oi.current=Yi,ci){for(var r=si.memoizedState;null!==r;){var o=r.queue;null!==o&&(o.pending=null),r=r.next}ci=!1}if(ii=0,ui=li=si=null,di=!1,fi=0,Pl.current=null,null===n||null===n.return){jl=1,Dl=t,Nl=null;break}e:{var i=e,s=n.return,l=n,u=t;if(t=Al,l.flags|=32768,null!==u&&"object"===typeof u&&"function"===typeof u.then){var c=u,d=l,f=d.tag;if(0===(1&d.mode)&&(0===f||11===f||15===f)){var p=d.alternate;p?(d.updateQueue=p.updateQueue,d.memoizedState=p.memoizedState,d.lanes=p.lanes):(d.updateQueue=null,d.memoizedState=null)}var h=ms(s);if(null!==h){h.flags&=-257,ys(h,s,l,0,t),1&h.mode&&gs(i,c,t),u=c;var g=(t=h).updateQueue;if(null===g){var m=new Set;m.add(u),t.updateQueue=m}else g.add(u);break e}if(0===(1&t)){gs(i,c,t),gu();break e}u=Error(a(426))}else if(aa&&1&l.mode){var y=ms(s);if(null!==y){0===(65536&y.flags)&&(y.flags|=256),ys(y,s,l,0,t),ga(us(u,l));break e}}i=u=us(u,l),4!==jl&&(jl=2),null===Ml?Ml=[i]:Ml.push(i),i=s;do{switch(i.tag){case 3:i.flags|=65536,t&=-t,i.lanes|=t,Va(i,ps(0,u,t));break e;case 1:l=u;var v=i.type,b=i.stateNode;if(0===(128&i.flags)&&("function"===typeof v.getDerivedStateFromError||null!==b&&"function"===typeof b.componentDidCatch&&(null===Kl||!Kl.has(b)))){i.flags|=65536,t&=-t,i.lanes|=t,Va(i,hs(i,l,t));break e}}i=i.return}while(null!==i)}wu(n)}catch(w){t=w,Nl===n&&null!==n&&(Nl=n=n.return);continue}break}}function hu(){var e=Cl.current;return Cl.current=Yi,null===e?Yi:e}function gu(){0!==jl&&3!==jl&&2!==jl||(jl=4),null===Tl||0===(268435455&Il)&&0===(268435455&Fl)||su(Tl,Al)}function mu(e,t){var n=Rl;Rl|=2;var r=hu();for(Tl===e&&Al===t||(Wl=null,fu(e,t));;)try{yu();break}catch(o){pu(e,o)}if(Oa(),Rl=n,Cl.current=r,null!==Nl)throw Error(a(261));return Tl=null,Al=0,jl}function yu(){for(;null!==Nl;)bu(Nl)}function vu(){for(;null!==Nl&&!Ge();)bu(Nl)}function bu(e){var t=xl(e.alternate,e,Ll);e.memoizedProps=e.pendingProps,null===t?wu(e):Nl=t,Pl.current=null}function wu(e){var t=e;do{var n=t.alternate;if(e=t.return,0===(32768&t.flags)){if(null!==(n=qs(n,t,Ll)))return void(Nl=n)}else{if(null!==(n=Qs(n,t)))return n.flags&=32767,void(Nl=n);if(null===e)return jl=6,void(Nl=null);e.flags|=32768,e.subtreeFlags=0,e.deletions=null}if(null!==(t=t.sibling))return void(Nl=t);Nl=t=e}while(null!==t);0===jl&&(jl=5)}function ku(e,t,n){var r=bt,o=Ol.transition;try{Ol.transition=null,bt=1,function(e,t,n,r){do{Su()}while(null!==Ql);if(0!==(6&Rl))throw Error(a(327));n=e.finishedWork;var o=e.finishedLanes;if(null===n)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(a(177));e.callbackNode=null,e.callbackPriority=0;var i=n.lanes|n.childLanes;if(function(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var o=31-it(n),a=1<<o;t[o]=0,r[o]=-1,e[o]=-1,n&=~a}}(e,i),e===Tl&&(Nl=Tl=null,Al=0),0===(2064&n.subtreeFlags)&&0===(2064&n.flags)||ql||(ql=!0,Tu(tt,(function(){return Su(),null}))),i=0!==(15990&n.flags),0!==(15990&n.subtreeFlags)||i){i=Ol.transition,Ol.transition=null;var s=bt;bt=1;var l=Rl;Rl|=4,Pl.current=null,function(e,t){if(eo=Ht,pr(e=fr())){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var o=r.anchorOffset,i=r.focusNode;r=r.focusOffset;try{n.nodeType,i.nodeType}catch(k){n=null;break e}var s=0,l=-1,u=-1,c=0,d=0,f=e,p=null;t:for(;;){for(var h;f!==n||0!==o&&3!==f.nodeType||(l=s+o),f!==i||0!==r&&3!==f.nodeType||(u=s+r),3===f.nodeType&&(s+=f.nodeValue.length),null!==(h=f.firstChild);)p=f,f=h;for(;;){if(f===e)break t;if(p===n&&++c===o&&(l=s),p===i&&++d===r&&(u=s),null!==(h=f.nextSibling))break;p=(f=p).parentNode}f=h}n=-1===l||-1===u?null:{start:l,end:u}}else n=null}n=n||{start:0,end:0}}else n=null;for(to={focusedElem:e,selectionRange:n},Ht=!1,Ys=t;null!==Ys;)if(e=(t=Ys).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,Ys=e;else for(;null!==Ys;){t=Ys;try{var g=t.alternate;if(0!==(1024&t.flags))switch(t.tag){case 0:case 11:case 15:case 5:case 6:case 4:case 17:break;case 1:if(null!==g){var m=g.memoizedProps,y=g.memoizedState,v=t.stateNode,b=v.getSnapshotBeforeUpdate(t.elementType===t.type?m:ns(t.type,m),y);v.__reactInternalSnapshotBeforeUpdate=b}break;case 3:var w=t.stateNode.containerInfo;1===w.nodeType?w.textContent="":9===w.nodeType&&w.documentElement&&w.removeChild(w.documentElement);break;default:throw Error(a(163))}}catch(k){Eu(t,t.return,k)}if(null!==(e=t.sibling)){e.return=t.return,Ys=e;break}Ys=t.return}g=tl,tl=!1}(e,n),ml(n,e),hr(to),Ht=!!eo,to=eo=null,e.current=n,vl(n,e,o),Je(),Rl=l,bt=s,Ol.transition=i}else e.current=n;if(ql&&(ql=!1,Ql=e,Gl=o),i=e.pendingLanes,0===i&&(Kl=null),function(e){if(at&&"function"===typeof at.onCommitFiberRoot)try{at.onCommitFiberRoot(ot,e,void 0,128===(128&e.current.flags))}catch(t){}}(n.stateNode),ru(e,Xe()),null!==t)for(r=e.onRecoverableError,n=0;n<t.length;n++)o=t[n],r(o.value,{componentStack:o.stack,digest:o.digest});if(Hl)throw Hl=!1,e=$l,$l=null,e;0!==(1&Gl)&&0!==e.tag&&Su(),i=e.pendingLanes,0!==(1&i)?e===Xl?Jl++:(Jl=0,Xl=e):Jl=0,Wo()}(e,t,n,r)}finally{Ol.transition=o,bt=r}return null}function Su(){if(null!==Ql){var e=wt(Gl),t=Ol.transition,n=bt;try{if(Ol.transition=null,bt=16>e?16:e,null===Ql)var r=!1;else{if(e=Ql,Ql=null,Gl=0,0!==(6&Rl))throw Error(a(331));var o=Rl;for(Rl|=4,Ys=e.current;null!==Ys;){var i=Ys,s=i.child;if(0!==(16&Ys.flags)){var l=i.deletions;if(null!==l){for(var u=0;u<l.length;u++){var c=l[u];for(Ys=c;null!==Ys;){var d=Ys;switch(d.tag){case 0:case 11:case 15:nl(8,d,i)}var f=d.child;if(null!==f)f.return=d,Ys=f;else for(;null!==Ys;){var p=(d=Ys).sibling,h=d.return;if(al(d),d===c){Ys=null;break}if(null!==p){p.return=h,Ys=p;break}Ys=h}}}var g=i.alternate;if(null!==g){var m=g.child;if(null!==m){g.child=null;do{var y=m.sibling;m.sibling=null,m=y}while(null!==m)}}Ys=i}}if(0!==(2064&i.subtreeFlags)&&null!==s)s.return=i,Ys=s;else e:for(;null!==Ys;){if(0!==(2048&(i=Ys).flags))switch(i.tag){case 0:case 11:case 15:nl(9,i,i.return)}var v=i.sibling;if(null!==v){v.return=i.return,Ys=v;break e}Ys=i.return}}var b=e.current;for(Ys=b;null!==Ys;){var w=(s=Ys).child;if(0!==(2064&s.subtreeFlags)&&null!==w)w.return=s,Ys=w;else e:for(s=b;null!==Ys;){if(0!==(2048&(l=Ys).flags))try{switch(l.tag){case 0:case 11:case 15:rl(9,l)}}catch(S){Eu(l,l.return,S)}if(l===s){Ys=null;break e}var k=l.sibling;if(null!==k){k.return=l.return,Ys=k;break e}Ys=l.return}}if(Rl=o,Wo(),at&&"function"===typeof at.onPostCommitFiberRoot)try{at.onPostCommitFiberRoot(ot,e)}catch(S){}r=!0}return r}finally{bt=n,Ol.transition=t}}return!1}function xu(e,t,n){e=Ba(e,t=ps(0,t=us(n,t),1),1),t=eu(),null!==e&&(yt(e,1,t),ru(e,t))}function Eu(e,t,n){if(3===e.tag)xu(e,e,n);else for(;null!==t;){if(3===t.tag){xu(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===Kl||!Kl.has(r))){t=Ba(t,e=hs(t,e=us(n,e),1),1),e=eu(),null!==t&&(yt(t,1,e),ru(t,e));break}}t=t.return}}function Cu(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),t=eu(),e.pingedLanes|=e.suspendedLanes&n,Tl===e&&(Al&n)===n&&(4===jl||3===jl&&(130023424&Al)===Al&&500>Xe()-Ul?fu(e,0):zl|=n),ru(e,t)}function Pu(e,t){0===t&&(0===(1&e.mode)?t=1:(t=ct,0===(130023424&(ct<<=1))&&(ct=4194304)));var n=eu();null!==(e=Da(e,t))&&(yt(e,t,n),ru(e,n))}function Ou(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),Pu(e,n)}function Ru(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;null!==o&&(n=o.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(a(314))}null!==r&&r.delete(t),Pu(e,n)}function Tu(e,t){return qe(e,t)}function Nu(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Au(e,t,n,r){return new Nu(e,t,n,r)}function Lu(e){return!(!(e=e.prototype)||!e.isReactComponent)}function _u(e,t){var n=e.alternate;return null===n?((n=Au(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=14680064&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function ju(e,t,n,r,o,i){var s=2;if(r=e,"function"===typeof e)Lu(e)&&(s=1);else if("string"===typeof e)s=5;else e:switch(e){case x:return Du(n.children,o,i,t);case E:s=8,o|=8;break;case C:return(e=Au(12,n,t,2|o)).elementType=C,e.lanes=i,e;case T:return(e=Au(13,n,t,o)).elementType=T,e.lanes=i,e;case N:return(e=Au(19,n,t,o)).elementType=N,e.lanes=i,e;case _:return Iu(n,o,i,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case P:s=10;break e;case O:s=9;break e;case R:s=11;break e;case A:s=14;break e;case L:s=16,r=null;break e}throw Error(a(130,null==e?e:typeof e,""))}return(t=Au(s,n,t,o)).elementType=e,t.type=r,t.lanes=i,t}function Du(e,t,n,r){return(e=Au(7,e,r,t)).lanes=n,e}function Iu(e,t,n,r){return(e=Au(22,e,r,t)).elementType=_,e.lanes=n,e.stateNode={isHidden:!1},e}function Fu(e,t,n){return(e=Au(6,e,null,t)).lanes=n,e}function zu(e,t,n){return(t=Au(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Mu(e,t,n,r,o){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=mt(0),this.expirationTimes=mt(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=mt(0),this.identifierPrefix=r,this.onRecoverableError=o,this.mutableSourceEagerHydrationData=null}function Bu(e,t,n,r,o,a,i,s,l){return e=new Mu(e,t,n,s,l),1===t?(t=1,!0===a&&(t|=8)):t=0,a=Au(3,null,null,t),e.current=a,a.stateNode=e,a.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},Fa(a),e}function Uu(e){if(!e)return Ro;e:{if(Ve(e=e._reactInternals)!==e||1!==e.tag)throw Error(a(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(_o(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(null!==t);throw Error(a(171))}if(1===e.tag){var n=e.type;if(_o(n))return Io(e,n,t)}return t}function Vu(e,t,n,r,o,a,i,s,l){return(e=Bu(n,r,!0,e,0,a,0,s,l)).context=Uu(null),n=e.current,(a=Ma(r=eu(),o=tu(n))).callback=void 0!==t&&null!==t?t:null,Ba(n,a,o),e.current.lanes=o,yt(e,o,r),ru(e,r),e}function Wu(e,t,n,r){var o=t.current,a=eu(),i=tu(o);return n=Uu(n),null===t.context?t.context=n:t.pendingContext=n,(t=Ma(a,i)).payload={element:e},null!==(r=void 0===r?null:r)&&(t.callback=r),null!==(e=Ba(o,t,i))&&(nu(e,o,i,a),Ua(e,o,i)),i}function Hu(e){return(e=e.current).child?(e.child.tag,e.child.stateNode):null}function $u(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Ku(e,t){$u(e,t),(e=e.alternate)&&$u(e,t)}xl=function(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps||No.current)bs=!0;else{if(0===(e.lanes&n)&&0===(128&t.flags))return bs=!1,function(e,t,n){switch(t.tag){case 3:Ts(t),ha();break;case 5:Ya(t);break;case 1:_o(t.type)&&Fo(t);break;case 4:Ja(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,o=t.memoizedProps.value;Oo(xa,r._currentValue),r._currentValue=o;break;case 13:if(null!==(r=t.memoizedState))return null!==r.dehydrated?(Oo(ei,1&ei.current),t.flags|=128,null):0!==(n&t.child.childLanes)?Fs(e,t,n):(Oo(ei,1&ei.current),null!==(e=Hs(e,t,n))?e.sibling:null);Oo(ei,1&ei.current);break;case 19:if(r=0!==(n&t.childLanes),0!==(128&e.flags)){if(r)return Vs(e,t,n);t.flags|=128}if(null!==(o=t.memoizedState)&&(o.rendering=null,o.tail=null,o.lastEffect=null),Oo(ei,ei.current),r)break;return null;case 22:case 23:return t.lanes=0,Es(e,t,n)}return Hs(e,t,n)}(e,t,n);bs=0!==(131072&e.flags)}else bs=!1,aa&&0!==(1048576&t.flags)&&ea(t,qo,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ws(e,t),e=t.pendingProps;var o=Lo(t,To.current);Na(t,n),o=mi(null,t,r,e,o,n);var i=yi();return t.flags|=1,"object"===typeof o&&null!==o&&"function"===typeof o.render&&void 0===o.$$typeof?(t.tag=1,t.memoizedState=null,t.updateQueue=null,_o(r)?(i=!0,Fo(t)):i=!1,t.memoizedState=null!==o.state&&void 0!==o.state?o.state:null,Fa(t),o.updater=os,t.stateNode=o,o._reactInternals=t,ls(t,r,e,n),t=Rs(null,t,r,!0,i,n)):(t.tag=0,aa&&i&&ta(t),ws(null,t,o,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ws(e,t),e=t.pendingProps,r=(o=r._init)(r._payload),t.type=r,o=t.tag=function(e){if("function"===typeof e)return Lu(e)?1:0;if(void 0!==e&&null!==e){if((e=e.$$typeof)===R)return 11;if(e===A)return 14}return 2}(r),e=ns(r,e),o){case 0:t=Ps(null,t,r,e,n);break e;case 1:t=Os(null,t,r,e,n);break e;case 11:t=ks(null,t,r,e,n);break e;case 14:t=Ss(null,t,r,ns(r.type,e),n);break e}throw Error(a(306,r,""))}return t;case 0:return r=t.type,o=t.pendingProps,Ps(e,t,r,o=t.elementType===r?o:ns(r,o),n);case 1:return r=t.type,o=t.pendingProps,Os(e,t,r,o=t.elementType===r?o:ns(r,o),n);case 3:e:{if(Ts(t),null===e)throw Error(a(387));r=t.pendingProps,o=(i=t.memoizedState).element,za(e,t),Wa(t,r,null,n);var s=t.memoizedState;if(r=s.element,i.isDehydrated){if(i={element:r,isDehydrated:!1,cache:s.cache,pendingSuspenseBoundaries:s.pendingSuspenseBoundaries,transitions:s.transitions},t.updateQueue.baseState=i,t.memoizedState=i,256&t.flags){t=Ns(e,t,r,n,o=us(Error(a(423)),t));break e}if(r!==o){t=Ns(e,t,r,n,o=us(Error(a(424)),t));break e}for(oa=uo(t.stateNode.containerInfo.firstChild),ra=t,aa=!0,ia=null,n=Sa(t,null,r,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(ha(),r===o){t=Hs(e,t,n);break e}ws(e,t,r,n)}t=t.child}return t;case 5:return Ya(t),null===e&&ca(t),r=t.type,o=t.pendingProps,i=null!==e?e.memoizedProps:null,s=o.children,no(r,o)?s=null:null!==i&&no(r,i)&&(t.flags|=32),Cs(e,t),ws(e,t,s,n),t.child;case 6:return null===e&&ca(t),null;case 13:return Fs(e,t,n);case 4:return Ja(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=ka(t,null,r,n):ws(e,t,r,n),t.child;case 11:return r=t.type,o=t.pendingProps,ks(e,t,r,o=t.elementType===r?o:ns(r,o),n);case 7:return ws(e,t,t.pendingProps,n),t.child;case 8:case 12:return ws(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,o=t.pendingProps,i=t.memoizedProps,s=o.value,Oo(xa,r._currentValue),r._currentValue=s,null!==i)if(sr(i.value,s)){if(i.children===o.children&&!No.current){t=Hs(e,t,n);break e}}else for(null!==(i=t.child)&&(i.return=t);null!==i;){var l=i.dependencies;if(null!==l){s=i.child;for(var u=l.firstContext;null!==u;){if(u.context===r){if(1===i.tag){(u=Ma(-1,n&-n)).tag=2;var c=i.updateQueue;if(null!==c){var d=(c=c.shared).pending;null===d?u.next=u:(u.next=d.next,d.next=u),c.pending=u}}i.lanes|=n,null!==(u=i.alternate)&&(u.lanes|=n),Ta(i.return,n,t),l.lanes|=n;break}u=u.next}}else if(10===i.tag)s=i.type===t.type?null:i.child;else if(18===i.tag){if(null===(s=i.return))throw Error(a(341));s.lanes|=n,null!==(l=s.alternate)&&(l.lanes|=n),Ta(s,n,t),s=i.sibling}else s=i.child;if(null!==s)s.return=i;else for(s=i;null!==s;){if(s===t){s=null;break}if(null!==(i=s.sibling)){i.return=s.return,s=i;break}s=s.return}i=s}ws(e,t,o.children,n),t=t.child}return t;case 9:return o=t.type,r=t.pendingProps.children,Na(t,n),r=r(o=Aa(o)),t.flags|=1,ws(e,t,r,n),t.child;case 14:return o=ns(r=t.type,t.pendingProps),Ss(e,t,r,o=ns(r.type,o),n);case 15:return xs(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,o=t.pendingProps,o=t.elementType===r?o:ns(r,o),Ws(e,t),t.tag=1,_o(r)?(e=!0,Fo(t)):e=!1,Na(t,n),is(t,r,o),ls(t,r,o,n),Rs(null,t,r,!0,e,n);case 19:return Vs(e,t,n);case 22:return Es(e,t,n)}throw Error(a(156,t.tag))};var qu="function"===typeof reportError?reportError:function(e){console.error(e)};function Qu(e){this._internalRoot=e}function Gu(e){this._internalRoot=e}function Ju(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}function Xu(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType&&(8!==e.nodeType||" react-mount-point-unstable "!==e.nodeValue))}function Yu(){}function Zu(e,t,n,r,o){var a=n._reactRootContainer;if(a){var i=a;if("function"===typeof o){var s=o;o=function(){var e=Hu(i);s.call(e)}}Wu(t,i,e,o)}else i=function(e,t,n,r,o){if(o){if("function"===typeof r){var a=r;r=function(){var e=Hu(i);a.call(e)}}var i=Vu(t,r,e,0,null,!1,0,"",Yu);return e._reactRootContainer=i,e[go]=i.current,Vr(8===e.nodeType?e.parentNode:e),cu(),i}for(;o=e.lastChild;)e.removeChild(o);if("function"===typeof r){var s=r;r=function(){var e=Hu(l);s.call(e)}}var l=Bu(e,0,!1,null,0,!1,0,"",Yu);return e._reactRootContainer=l,e[go]=l.current,Vr(8===e.nodeType?e.parentNode:e),cu((function(){Wu(t,l,n,r)})),l}(n,t,e,o,r);return Hu(i)}Gu.prototype.render=Qu.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(a(409));Wu(e,t,null,null)},Gu.prototype.unmount=Qu.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;cu((function(){Wu(null,e,null,null)})),t[go]=null}},Gu.prototype.unstable_scheduleHydration=function(e){if(e){var t=Et();e={blockedOn:null,target:e,priority:t};for(var n=0;n<_t.length&&0!==t&&t<_t[n].priority;n++);_t.splice(n,0,e),0===n&&Ft(e)}},kt=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=dt(t.pendingLanes);0!==n&&(vt(t,1|n),ru(t,Xe()),0===(6&Rl)&&(Vl=Xe()+500,Wo()))}break;case 13:cu((function(){var t=Da(e,1);if(null!==t){var n=eu();nu(t,e,1,n)}})),Ku(e,1)}},St=function(e){if(13===e.tag){var t=Da(e,134217728);if(null!==t)nu(t,e,134217728,eu());Ku(e,134217728)}},xt=function(e){if(13===e.tag){var t=tu(e),n=Da(e,t);if(null!==n)nu(n,e,t,eu());Ku(e,t)}},Et=function(){return bt},Ct=function(e,t){var n=bt;try{return bt=e,t()}finally{bt=n}},Se=function(e,t,n){switch(t){case"input":if(Y(e,n),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=So(r);if(!o)throw Error(a(90));q(r),Y(r,o)}}}break;case"textarea":ae(e,n);break;case"select":null!=(t=n.value)&&ne(e,!!n.multiple,t,!1)}},Re=uu,Te=cu;var ec={usingClientEntryPoint:!1,Events:[wo,ko,So,Pe,Oe,uu]},tc={findFiberByHostInstance:bo,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},nc={bundleType:tc.bundleType,version:tc.version,rendererPackageName:tc.rendererPackageName,rendererConfig:tc.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:w.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return null===(e=$e(e))?null:e.stateNode},findFiberByHostInstance:tc.findFiberByHostInstance||function(){return null},findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var rc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!rc.isDisabled&&rc.supportsFiber)try{ot=rc.inject(nc),at=rc}catch(ce){}}t.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ec,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!Ju(t))throw Error(a(200));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:S,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.createRoot=function(e,t){if(!Ju(e))throw Error(a(299));var n=!1,r="",o=qu;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onRecoverableError&&(o=t.onRecoverableError)),t=Bu(e,1,!1,null,0,n,0,r,o),e[go]=t.current,Vr(8===e.nodeType?e.parentNode:e),new Qu(t)},t.findDOMNode=function(e){if(null==e)return null;if(1===e.nodeType)return e;var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(a(188));throw e=Object.keys(e).join(","),Error(a(268,e))}return e=null===(e=$e(t))?null:e.stateNode},t.flushSync=function(e){return cu(e)},t.hydrate=function(e,t,n){if(!Xu(t))throw Error(a(200));return Zu(null,e,t,!0,n)},t.hydrateRoot=function(e,t,n){if(!Ju(e))throw Error(a(405));var r=null!=n&&n.hydratedSources||null,o=!1,i="",s=qu;if(null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(o=!0),void 0!==n.identifierPrefix&&(i=n.identifierPrefix),void 0!==n.onRecoverableError&&(s=n.onRecoverableError)),t=Vu(t,null,e,1,null!=n?n:null,o,0,i,s),e[go]=t.current,Vr(e),r)for(e=0;e<r.length;e++)o=(o=(n=r[e])._getVersion)(n._source),null==t.mutableSourceEagerHydrationData?t.mutableSourceEagerHydrationData=[n,o]:t.mutableSourceEagerHydrationData.push(n,o);return new Gu(t)},t.render=function(e,t,n){if(!Xu(t))throw Error(a(200));return Zu(null,e,t,!1,n)},t.unmountComponentAtNode=function(e){if(!Xu(e))throw Error(a(40));return!!e._reactRootContainer&&(cu((function(){Zu(null,null,e,!1,(function(){e._reactRootContainer=null,e[go]=null}))})),!0)},t.unstable_batchedUpdates=uu,t.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Xu(n))throw Error(a(200));if(null==e||void 0===e._reactInternals)throw Error(a(38));return Zu(e,t,n,!1,r)},t.version="18.3.1-next-f1338f8080-20240426"},763:(e,t,n)=>{"use strict";e.exports=n(983)},844:e=>{e.exports={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}},853:(e,t,n)=>{"use strict";e.exports=n(234)},868:(e,t,n)=>{"use strict";function r(e){let t="https://mui.com/production-error/?code="+e;for(let n=1;n<arguments.length;n+=1)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified MUI error #"+e+"; visit "+t+" for the full message."}n.d(t,{A:()=>r})},950:(e,t,n)=>{"use strict";!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(730)},983:(e,t)=>{"use strict";var n="function"===typeof Symbol&&Symbol.for,r=n?Symbol.for("react.element"):60103,o=n?Symbol.for("react.portal"):60106,a=n?Symbol.for("react.fragment"):60107,i=n?Symbol.for("react.strict_mode"):60108,s=n?Symbol.for("react.profiler"):60114,l=n?Symbol.for("react.provider"):60109,u=n?Symbol.for("react.context"):60110,c=n?Symbol.for("react.async_mode"):60111,d=n?Symbol.for("react.concurrent_mode"):60111,f=n?Symbol.for("react.forward_ref"):60112,p=n?Symbol.for("react.suspense"):60113,h=n?Symbol.for("react.suspense_list"):60120,g=n?Symbol.for("react.memo"):60115,m=n?Symbol.for("react.lazy"):60116,y=n?Symbol.for("react.block"):60121,v=n?Symbol.for("react.fundamental"):60117,b=n?Symbol.for("react.responder"):60118,w=n?Symbol.for("react.scope"):60119;function k(e){if("object"===typeof e&&null!==e){var t=e.$$typeof;switch(t){case r:switch(e=e.type){case c:case d:case a:case s:case i:case p:return e;default:switch(e=e&&e.$$typeof){case u:case f:case m:case g:case l:return e;default:return t}}case o:return t}}}function S(e){return k(e)===d}t.AsyncMode=c,t.ConcurrentMode=d,t.ContextConsumer=u,t.ContextProvider=l,t.Element=r,t.ForwardRef=f,t.Fragment=a,t.Lazy=m,t.Memo=g,t.Portal=o,t.Profiler=s,t.StrictMode=i,t.Suspense=p,t.isAsyncMode=function(e){return S(e)||k(e)===c},t.isConcurrentMode=S,t.isContextConsumer=function(e){return k(e)===u},t.isContextProvider=function(e){return k(e)===l},t.isElement=function(e){return"object"===typeof e&&null!==e&&e.$$typeof===r},t.isForwardRef=function(e){return k(e)===f},t.isFragment=function(e){return k(e)===a},t.isLazy=function(e){return k(e)===m},t.isMemo=function(e){return k(e)===g},t.isPortal=function(e){return k(e)===o},t.isProfiler=function(e){return k(e)===s},t.isStrictMode=function(e){return k(e)===i},t.isSuspense=function(e){return k(e)===p},t.isValidElementType=function(e){return"string"===typeof e||"function"===typeof e||e===a||e===d||e===s||e===i||e===p||e===h||"object"===typeof e&&null!==e&&(e.$$typeof===m||e.$$typeof===g||e.$$typeof===l||e.$$typeof===u||e.$$typeof===f||e.$$typeof===v||e.$$typeof===b||e.$$typeof===w||e.$$typeof===y)},t.typeOf=k},994:e=>{e.exports=function(e){return e&&e.__esModule?e:{default:e}},e.exports.__esModule=!0,e.exports.default=e.exports}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,n),a.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,o){if(1&o&&(r=this(r)),8&o)return r;if("object"===typeof r&&r){if(4&o&&r.__esModule)return r;if(16&o&&"function"===typeof r.then)return r}var a=Object.create(null);n.r(a);var i={};e=e||[null,t({}),t([]),t(t)];for(var s=2&o&&r;"object"==typeof s&&!~e.indexOf(s);s=t(s))Object.getOwnPropertyNames(s).forEach((e=>i[e]=()=>r[e]));return i.default=()=>r,n.d(a,i),a}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.g=function(){if("object"===typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"===typeof window)return window}}(),n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{"use strict";var e={};n.r(e),n.d(e,{hasBrowserEnv:()=>uu,hasStandardBrowserEnv:()=>du,hasStandardBrowserWebWorkerEnv:()=>fu,navigator:()=>cu,origin:()=>pu});var t=n(43),r=n.t(t,2),o=n(391),a=n(461),i=n(443),s=n(950),l=n.t(s,2);let u=function(e){e()};const c=()=>u,d=Symbol.for("react-redux-context"),f="undefined"!==typeof globalThis?globalThis:{};function p(){var e;if(!t.createContext)return{};const n=null!=(e=f[d])?e:f[d]=new Map;let r=n.get(t.createContext);return r||(r=t.createContext(null),n.set(t.createContext,r)),r}const h=p();function g(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:h;return function(){return(0,t.useContext)(e)}}const m=g();let y=()=>{throw new Error("uSES not initialized!")};const v=(e,t)=>e===t;function b(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:h;const n=e===h?m:g(e);return function(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{equalityFn:o=v,stabilityCheck:a,noopCheck:i}="function"===typeof r?{equalityFn:r}:r;const{store:s,subscription:l,getServerState:u,stabilityCheck:c,noopCheck:d}=n(),f=((0,t.useRef)(!0),(0,t.useCallback)({[e.name]:t=>e(t)}[e.name],[e,c,a])),p=y(l.addNestedSub,s.getState,u||s.getState,f,o);return(0,t.useDebugValue)(p),p}}const w=b();n(219),n(706);const k={notify(){},get:()=>[]};function S(e,t){let n,r=k,o=0,a=!1;function i(){u.onStateChange&&u.onStateChange()}function s(){o++,n||(n=t?t.addNestedSub(i):e.subscribe(i),r=function(){const e=c();let t=null,n=null;return{clear(){t=null,n=null},notify(){e((()=>{let e=t;for(;e;)e.callback(),e=e.next}))},get(){let e=[],n=t;for(;n;)e.push(n),n=n.next;return e},subscribe(e){let r=!0,o=n={callback:e,next:null,prev:n};return o.prev?o.prev.next=o:t=o,function(){r&&null!==t&&(r=!1,o.next?o.next.prev=o.prev:n=o.prev,o.prev?o.prev.next=o.next:t=o.next)}}}}())}function l(){o--,n&&0===o&&(n(),n=void 0,r.clear(),r=k)}const u={addNestedSub:function(e){s();const t=r.subscribe(e);let n=!1;return()=>{n||(n=!0,t(),l())}},notifyNestedSubs:function(){r.notify()},handleChangeWrapper:i,isSubscribed:function(){return a},trySubscribe:function(){a||(a=!0,s())},tryUnsubscribe:function(){a&&(a=!1,l())},getListeners:()=>r};return u}const x=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement)?t.useLayoutEffect:t.useEffect;let E=null;const C=function(e){let{store:n,context:r,children:o,serverState:a,stabilityCheck:i="once",noopCheck:s="once"}=e;const l=t.useMemo((()=>{const e=S(n);return{store:n,subscription:e,getServerState:a?()=>a:void 0,stabilityCheck:i,noopCheck:s}}),[n,a,i,s]),u=t.useMemo((()=>n.getState()),[n]);x((()=>{const{subscription:e}=l;return e.onStateChange=e.notifyNestedSubs,e.trySubscribe(),u!==n.getState()&&e.notifyNestedSubs(),()=>{e.tryUnsubscribe(),e.onStateChange=void 0}}),[l,u]);const c=r||h;return t.createElement(c.Provider,{value:l},o)};var P,O;function R(){return R=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},R.apply(this,arguments)}(e=>{y=e})(i.useSyncExternalStoreWithSelector),(e=>{E=e})(a.useSyncExternalStore),P=s.unstable_batchedUpdates,u=P,function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"}(O||(O={}));const T="popstate";function N(e,t){if(!1===e||null===e||"undefined"===typeof e)throw new Error(t)}function A(e,t){if(!e){"undefined"!==typeof console&&console.warn(t);try{throw new Error(t)}catch(n){}}}function L(e,t){return{usr:e.state,key:e.key,idx:t}}function _(e,t,n,r){return void 0===n&&(n=null),R({pathname:"string"===typeof e?e:e.pathname,search:"",hash:""},"string"===typeof t?D(t):t,{state:n,key:t&&t.key||r||Math.random().toString(36).substr(2,8)})}function j(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&"?"!==n&&(t+="?"===n.charAt(0)?n:"?"+n),r&&"#"!==r&&(t+="#"===r.charAt(0)?r:"#"+r),t}function D(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function I(e,t,n,r){void 0===r&&(r={});let{window:o=document.defaultView,v5Compat:a=!1}=r,i=o.history,s=O.Pop,l=null,u=c();function c(){return(i.state||{idx:null}).idx}function d(){s=O.Pop;let e=c(),t=null==e?null:e-u;u=e,l&&l({action:s,location:p.location,delta:t})}function f(e){let t="null"!==o.location.origin?o.location.origin:o.location.href,n="string"===typeof e?e:j(e);return n=n.replace(/ $/,"%20"),N(t,"No window.location.(origin|href) available to create URL for href: "+n),new URL(n,t)}null==u&&(u=0,i.replaceState(R({},i.state,{idx:u}),""));let p={get action(){return s},get location(){return e(o,i)},listen(e){if(l)throw new Error("A history only accepts one active listener");return o.addEventListener(T,d),l=e,()=>{o.removeEventListener(T,d),l=null}},createHref:e=>t(o,e),createURL:f,encodeLocation(e){let t=f(e);return{pathname:t.pathname,search:t.search,hash:t.hash}},push:function(e,t){s=O.Push;let r=_(p.location,e,t);n&&n(r,e),u=c()+1;let d=L(r,u),f=p.createHref(r);try{i.pushState(d,"",f)}catch(h){if(h instanceof DOMException&&"DataCloneError"===h.name)throw h;o.location.assign(f)}a&&l&&l({action:s,location:p.location,delta:1})},replace:function(e,t){s=O.Replace;let r=_(p.location,e,t);n&&n(r,e),u=c();let o=L(r,u),d=p.createHref(r);i.replaceState(o,"",d),a&&l&&l({action:s,location:p.location,delta:0})},go:e=>i.go(e)};return p}var F;!function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"}(F||(F={}));new Set(["lazy","caseSensitive","path","id","index","children"]);function z(e,t,n){return void 0===n&&(n="/"),M(e,t,n,!1)}function M(e,t,n,r){let o=Z(("string"===typeof t?D(t):t).pathname||"/",n);if(null==o)return null;let a=B(e);!function(e){e.sort(((e,t)=>e.score!==t.score?t.score-e.score:function(e,t){let n=e.length===t.length&&e.slice(0,-1).every(((e,n)=>e===t[n]));return n?e[e.length-1]-t[t.length-1]:0}(e.routesMeta.map((e=>e.childrenIndex)),t.routesMeta.map((e=>e.childrenIndex)))))}(a);let i=null;for(let s=0;null==i&&s<a.length;++s){let e=Y(o);i=J(a[s],e,r)}return i}function B(e,t,n,r){void 0===t&&(t=[]),void 0===n&&(n=[]),void 0===r&&(r="");let o=(e,o,a)=>{let i={relativePath:void 0===a?e.path||"":a,caseSensitive:!0===e.caseSensitive,childrenIndex:o,route:e};i.relativePath.startsWith("/")&&(N(i.relativePath.startsWith(r),'Absolute route path "'+i.relativePath+'" nested under path "'+r+'" is not valid. An absolute child route path must start with the combined path of all its parent routes.'),i.relativePath=i.relativePath.slice(r.length));let s=oe([r,i.relativePath]),l=n.concat(i);e.children&&e.children.length>0&&(N(!0!==e.index,'Index routes must not have child routes. Please remove all child routes from route path "'+s+'".'),B(e.children,t,l,s)),(null!=e.path||e.index)&&t.push({path:s,score:G(s,e.index),routesMeta:l})};return e.forEach(((e,t)=>{var n;if(""!==e.path&&null!=(n=e.path)&&n.includes("?"))for(let r of U(e.path))o(e,t,r);else o(e,t)})),t}function U(e){let t=e.split("/");if(0===t.length)return[];let[n,...r]=t,o=n.endsWith("?"),a=n.replace(/\?$/,"");if(0===r.length)return o?[a,""]:[a];let i=U(r.join("/")),s=[];return s.push(...i.map((e=>""===e?a:[a,e].join("/")))),o&&s.push(...i),s.map((t=>e.startsWith("/")&&""===t?"/":t))}const V=/^:[\w-]+$/,W=3,H=2,$=1,K=10,q=-2,Q=e=>"*"===e;function G(e,t){let n=e.split("/"),r=n.length;return n.some(Q)&&(r+=q),t&&(r+=H),n.filter((e=>!Q(e))).reduce(((e,t)=>e+(V.test(t)?W:""===t?$:K)),r)}function J(e,t,n){void 0===n&&(n=!1);let{routesMeta:r}=e,o={},a="/",i=[];for(let s=0;s<r.length;++s){let e=r[s],l=s===r.length-1,u="/"===a?t:t.slice(a.length)||"/",c=X({path:e.relativePath,caseSensitive:e.caseSensitive,end:l},u),d=e.route;if(!c&&l&&n&&!r[r.length-1].route.index&&(c=X({path:e.relativePath,caseSensitive:e.caseSensitive,end:!1},u)),!c)return null;Object.assign(o,c.params),i.push({params:o,pathname:oe([a,c.pathname]),pathnameBase:ae(oe([a,c.pathnameBase])),route:d}),"/"!==c.pathnameBase&&(a=oe([a,c.pathnameBase]))}return i}function X(e,t){"string"===typeof e&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=function(e,t,n){void 0===t&&(t=!1);void 0===n&&(n=!0);A("*"===e||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were "'+e.replace(/\*$/,"/*")+'" because the `*` character must always follow a `/` in the pattern. To get rid of this warning, please change the route path to "'+e.replace(/\*$/,"/*")+'".');let r=[],o="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,((e,t,n)=>(r.push({paramName:t,isOptional:null!=n}),n?"/?([^\\/]+)?":"/([^\\/]+)")));e.endsWith("*")?(r.push({paramName:"*"}),o+="*"===e||"/*"===e?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?o+="\\/*$":""!==e&&"/"!==e&&(o+="(?:(?=\\/|$))");let a=new RegExp(o,t?void 0:"i");return[a,r]}(e.path,e.caseSensitive,e.end),o=t.match(n);if(!o)return null;let a=o[0],i=a.replace(/(.)\/+$/,"$1"),s=o.slice(1);return{params:r.reduce(((e,t,n)=>{let{paramName:r,isOptional:o}=t;if("*"===r){let e=s[n]||"";i=a.slice(0,a.length-e.length).replace(/(.)\/+$/,"$1")}const l=s[n];return e[r]=o&&!l?void 0:(l||"").replace(/%2F/g,"/"),e}),{}),pathname:a,pathnameBase:i,pattern:e}}function Y(e){try{return e.split("/").map((e=>decodeURIComponent(e).replace(/\//g,"%2F"))).join("/")}catch(t){return A(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent encoding ('+t+")."),e}}function Z(e,t){if("/"===t)return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&"/"!==r?null:e.slice(n)||"/"}function ee(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified `to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the `to."+n+'` field. Alternatively you may provide the full path as a string in <Link to="..."> and the router will parse it for you.'}function te(e){return e.filter(((e,t)=>0===t||e.route.path&&e.route.path.length>0))}function ne(e,t){let n=te(e);return t?n.map(((e,t)=>t===n.length-1?e.pathname:e.pathnameBase)):n.map((e=>e.pathnameBase))}function re(e,t,n,r){let o;void 0===r&&(r=!1),"string"===typeof e?o=D(e):(o=R({},e),N(!o.pathname||!o.pathname.includes("?"),ee("?","pathname","search",o)),N(!o.pathname||!o.pathname.includes("#"),ee("#","pathname","hash",o)),N(!o.search||!o.search.includes("#"),ee("#","search","hash",o)));let a,i=""===e||""===o.pathname,s=i?"/":o.pathname;if(null==s)a=n;else{let e=t.length-1;if(!r&&s.startsWith("..")){let t=s.split("/");for(;".."===t[0];)t.shift(),e-=1;o.pathname=t.join("/")}a=e>=0?t[e]:"/"}let l=function(e,t){void 0===t&&(t="/");let{pathname:n,search:r="",hash:o=""}="string"===typeof e?D(e):e,a=n?n.startsWith("/")?n:function(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach((e=>{".."===e?n.length>1&&n.pop():"."!==e&&n.push(e)})),n.length>1?n.join("/"):"/"}(n,t):t;return{pathname:a,search:ie(r),hash:se(o)}}(o,a),u=s&&"/"!==s&&s.endsWith("/"),c=(i||"."===s)&&n.endsWith("/");return l.pathname.endsWith("/")||!u&&!c||(l.pathname+="/"),l}const oe=e=>e.join("/").replace(/\/\/+/g,"/"),ae=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),ie=e=>e&&"?"!==e?e.startsWith("?")?e:"?"+e:"",se=e=>e&&"#"!==e?e.startsWith("#")?e:"#"+e:"";Error;function le(e){return null!=e&&"number"===typeof e.status&&"string"===typeof e.statusText&&"boolean"===typeof e.internal&&"data"in e}const ue=["post","put","patch","delete"],ce=(new Set(ue),["get",...ue]);new Set(ce),new Set([301,302,303,307,308]),new Set([307,308]);Symbol("deferred");function de(){return de=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},de.apply(this,arguments)}const fe=t.createContext(null);const pe=t.createContext(null);const he=t.createContext(null);const ge=t.createContext(null);const me=t.createContext({outlet:null,matches:[],isDataRoute:!1});const ye=t.createContext(null);function ve(){return null!=t.useContext(ge)}function be(){return ve()||N(!1),t.useContext(ge).location}function we(e){t.useContext(he).static||t.useLayoutEffect(e)}function ke(){let{isDataRoute:e}=t.useContext(me);return e?function(){let{router:e}=Ne(Re.UseNavigateStable),n=Le(Te.UseNavigateStable),r=t.useRef(!1);return we((()=>{r.current=!0})),t.useCallback((function(t,o){void 0===o&&(o={}),r.current&&("number"===typeof t?e.navigate(t):e.navigate(t,de({fromRouteId:n},o)))}),[e,n])}():function(){ve()||N(!1);let e=t.useContext(fe),{basename:n,future:r,navigator:o}=t.useContext(he),{matches:a}=t.useContext(me),{pathname:i}=be(),s=JSON.stringify(ne(a,r.v7_relativeSplatPath)),l=t.useRef(!1);we((()=>{l.current=!0}));let u=t.useCallback((function(t,r){if(void 0===r&&(r={}),!l.current)return;if("number"===typeof t)return void o.go(t);let a=re(t,JSON.parse(s),i,"path"===r.relative);null==e&&"/"!==n&&(a.pathname="/"===a.pathname?n:oe([n,a.pathname])),(r.replace?o.replace:o.push)(a,r.state,r)}),[n,o,s,i,e]);return u}()}function Se(e,n,r,o){ve()||N(!1);let{navigator:a}=t.useContext(he),{matches:i}=t.useContext(me),s=i[i.length-1],l=s?s.params:{},u=(s&&s.pathname,s?s.pathnameBase:"/");s&&s.route;let c,d=be();if(n){var f;let e="string"===typeof n?D(n):n;"/"===u||(null==(f=e.pathname)?void 0:f.startsWith(u))||N(!1),c=e}else c=d;let p=c.pathname||"/",h=p;if("/"!==u){let e=u.replace(/^\//,"").split("/");h="/"+p.replace(/^\//,"").split("/").slice(e.length).join("/")}let g=z(e,{pathname:h});let m=Oe(g&&g.map((e=>Object.assign({},e,{params:Object.assign({},l,e.params),pathname:oe([u,a.encodeLocation?a.encodeLocation(e.pathname).pathname:e.pathname]),pathnameBase:"/"===e.pathnameBase?u:oe([u,a.encodeLocation?a.encodeLocation(e.pathnameBase).pathname:e.pathnameBase])}))),i,r,o);return n&&m?t.createElement(ge.Provider,{value:{location:de({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:O.Pop}},m):m}function xe(){let e=function(){var e;let n=t.useContext(ye),r=Ae(Te.UseRouteError),o=Le(Te.UseRouteError);if(void 0!==n)return n;return null==(e=r.errors)?void 0:e[o]}(),n=le(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),r=e instanceof Error?e.stack:null,o="rgba(200,200,200, 0.5)",a={padding:"0.5rem",backgroundColor:o};return t.createElement(t.Fragment,null,t.createElement("h2",null,"Unexpected Application Error!"),t.createElement("h3",{style:{fontStyle:"italic"}},n),r?t.createElement("pre",{style:a},r):null,null)}const Ee=t.createElement(xe,null);class Ce extends t.Component{constructor(e){super(e),this.state={location:e.location,revalidation:e.revalidation,error:e.error}}static getDerivedStateFromError(e){return{error:e}}static getDerivedStateFromProps(e,t){return t.location!==e.location||"idle"!==t.revalidation&&"idle"===e.revalidation?{error:e.error,location:e.location,revalidation:e.revalidation}:{error:void 0!==e.error?e.error:t.error,location:t.location,revalidation:e.revalidation||t.revalidation}}componentDidCatch(e,t){console.error("React Router caught the following error during render",e,t)}render(){return void 0!==this.state.error?t.createElement(me.Provider,{value:this.props.routeContext},t.createElement(ye.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Pe(e){let{routeContext:n,match:r,children:o}=e,a=t.useContext(fe);return a&&a.static&&a.staticContext&&(r.route.errorElement||r.route.ErrorBoundary)&&(a.staticContext._deepestRenderedBoundaryId=r.route.id),t.createElement(me.Provider,{value:n},o)}function Oe(e,n,r,o){var a;if(void 0===n&&(n=[]),void 0===r&&(r=null),void 0===o&&(o=null),null==e){var i;if(!r)return null;if(r.errors)e=r.matches;else{if(!(null!=(i=o)&&i.v7_partialHydration&&0===n.length&&!r.initialized&&r.matches.length>0))return null;e=r.matches}}let s=e,l=null==(a=r)?void 0:a.errors;if(null!=l){let e=s.findIndex((e=>e.route.id&&void 0!==(null==l?void 0:l[e.route.id])));e>=0||N(!1),s=s.slice(0,Math.min(s.length,e+1))}let u=!1,c=-1;if(r&&o&&o.v7_partialHydration)for(let t=0;t<s.length;t++){let e=s[t];if((e.route.HydrateFallback||e.route.hydrateFallbackElement)&&(c=t),e.route.id){let{loaderData:t,errors:n}=r,o=e.route.loader&&void 0===t[e.route.id]&&(!n||void 0===n[e.route.id]);if(e.route.lazy||o){u=!0,s=c>=0?s.slice(0,c+1):[s[0]];break}}}return s.reduceRight(((e,o,a)=>{let i,d=!1,f=null,p=null;var h;r&&(i=l&&o.route.id?l[o.route.id]:void 0,f=o.route.errorElement||Ee,u&&(c<0&&0===a?(h="route-fallback",!1||_e[h]||(_e[h]=!0),d=!0,p=null):c===a&&(d=!0,p=o.route.hydrateFallbackElement||null)));let g=n.concat(s.slice(0,a+1)),m=()=>{let n;return n=i?f:d?p:o.route.Component?t.createElement(o.route.Component,null):o.route.element?o.route.element:e,t.createElement(Pe,{match:o,routeContext:{outlet:e,matches:g,isDataRoute:null!=r},children:n})};return r&&(o.route.ErrorBoundary||o.route.errorElement||0===a)?t.createElement(Ce,{location:r.location,revalidation:r.revalidation,component:f,error:i,children:m(),routeContext:{outlet:null,matches:g,isDataRoute:!0}}):m()}),null)}var Re=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(Re||{}),Te=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Te||{});function Ne(e){let n=t.useContext(fe);return n||N(!1),n}function Ae(e){let n=t.useContext(pe);return n||N(!1),n}function Le(e){let n=function(){let e=t.useContext(me);return e||N(!1),e}(),r=n.matches[n.matches.length-1];return r.route.id||N(!1),r.route.id}const _e={};function je(e,t){null==e||e.v7_startTransition,void 0===(null==e?void 0:e.v7_relativeSplatPath)&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}r.startTransition;function De(e){let{to:n,replace:r,state:o,relative:a}=e;ve()||N(!1);let{future:i,static:s}=t.useContext(he),{matches:l}=t.useContext(me),{pathname:u}=be(),c=ke(),d=re(n,ne(l,i.v7_relativeSplatPath),u,"path"===a),f=JSON.stringify(d);return t.useEffect((()=>c(JSON.parse(f),{replace:r,state:o,relative:a})),[c,f,a,r,o]),null}function Ie(e){N(!1)}function Fe(e){let{basename:n="/",children:r=null,location:o,navigationType:a=O.Pop,navigator:i,static:s=!1,future:l}=e;ve()&&N(!1);let u=n.replace(/^\/*/,"/"),c=t.useMemo((()=>({basename:u,navigator:i,static:s,future:de({v7_relativeSplatPath:!1},l)})),[u,l,i,s]);"string"===typeof o&&(o=D(o));let{pathname:d="/",search:f="",hash:p="",state:h=null,key:g="default"}=o,m=t.useMemo((()=>{let e=Z(d,u);return null==e?null:{location:{pathname:e,search:f,hash:p,state:h,key:g},navigationType:a}}),[u,d,f,p,h,g,a]);return null==m?null:t.createElement(he.Provider,{value:c},t.createElement(ge.Provider,{children:r,value:m}))}function ze(e){let{children:t,location:n}=e;return Se(Me(t),n)}new Promise((()=>{}));t.Component;function Me(e,n){void 0===n&&(n=[]);let r=[];return t.Children.forEach(e,((e,o)=>{if(!t.isValidElement(e))return;let a=[...n,o];if(e.type===t.Fragment)return void r.push.apply(r,Me(e.props.children,a));e.type!==Ie&&N(!1),e.props.index&&e.props.children&&N(!1);let i={id:e.props.id||a.join("-"),caseSensitive:e.props.caseSensitive,element:e.props.element,Component:e.props.Component,index:e.props.index,path:e.props.path,loader:e.props.loader,action:e.props.action,errorElement:e.props.errorElement,ErrorBoundary:e.props.ErrorBoundary,hasErrorBoundary:null!=e.props.ErrorBoundary||null!=e.props.errorElement,shouldRevalidate:e.props.shouldRevalidate,handle:e.props.handle,lazy:e.props.lazy};e.props.children&&(i.children=Me(e.props.children,a)),r.push(i)})),r}new Set(["application/x-www-form-urlencoded","multipart/form-data","text/plain"]);try{window.__reactRouterVersion="6"}catch(af){}new Map;const Be=r.startTransition;l.flushSync,r.useId;function Ue(e){let{basename:n,children:r,future:o,window:a}=e,i=t.useRef();var s;null==i.current&&(i.current=(void 0===(s={window:a,v5Compat:!0})&&(s={}),I((function(e,t){let{pathname:n,search:r,hash:o}=e.location;return _("",{pathname:n,search:r,hash:o},t.state&&t.state.usr||null,t.state&&t.state.key||"default")}),(function(e,t){return"string"===typeof t?t:j(t)}),null,s)));let l=i.current,[u,c]=t.useState({action:l.action,location:l.location}),{v7_startTransition:d}=o||{},f=t.useCallback((e=>{d&&Be?Be((()=>c(e))):c(e)}),[c,d]);return t.useLayoutEffect((()=>l.listen(f)),[l,f]),t.useEffect((()=>je(o)),[o]),t.createElement(Fe,{basename:n,children:r,location:u.location,navigationType:u.action,navigator:l,future:o})}"undefined"!==typeof window&&"undefined"!==typeof window.document&&window.document.createElement;var Ve,We;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Ve||(Ve={})),function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"}(We||(We={}));function He(){return He=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)({}).hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},He.apply(null,arguments)}function $e(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(-1!==t.indexOf(r))continue;n[r]=e[r]}return n}var Ke=n(868);function qe(e){if("object"!==typeof e||null===e)return!1;const t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}function Qe(e){if(t.isValidElement(e)||!qe(e))return e;const n={};return Object.keys(e).forEach((t=>{n[t]=Qe(e[t])})),n}function Ge(e,n){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{clone:!0};const o=r.clone?He({},e):e;return qe(e)&&qe(n)&&Object.keys(n).forEach((a=>{t.isValidElement(n[a])?o[a]=n[a]:qe(n[a])&&Object.prototype.hasOwnProperty.call(e,a)&&qe(e[a])?o[a]=Ge(e[a],n[a],r):r.clone?o[a]=qe(n[a])?Qe(n[a]):n[a]:o[a]=n[a]})),o}const Je={xs:0,sm:600,md:900,lg:1200,xl:1536},Xe={keys:["xs","sm","md","lg","xl"],up:e=>"@media (min-width:".concat(Je[e],"px)")};function Ye(e,t,n){const r=e.theme||{};if(Array.isArray(t)){const e=r.breakpoints||Xe;return t.reduce(((r,o,a)=>(r[e.up(e.keys[a])]=n(t[a]),r)),{})}if("object"===typeof t){const e=r.breakpoints||Xe;return Object.keys(t).reduce(((r,o)=>{if(-1!==Object.keys(e.values||Je).indexOf(o)){r[e.up(o)]=n(t[o],o)}else{const e=o;r[e]=t[e]}return r}),{})}return n(t)}function Ze(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};var t;return(null==(t=e.keys)?void 0:t.reduce(((t,n)=>(t[e.up(n)]={},t)),{}))||{}}function et(e,t){return e.reduce(((e,t)=>{const n=e[t];return(!n||0===Object.keys(n).length)&&delete e[t],e}),t)}function tt(e){if("string"!==typeof e)throw new Error((0,Ke.A)(7));return e.charAt(0).toUpperCase()+e.slice(1)}function nt(e,t){let n=!(arguments.length>2&&void 0!==arguments[2])||arguments[2];if(!t||"string"!==typeof t)return null;if(e&&e.vars&&n){const n="vars.".concat(t).split(".").reduce(((e,t)=>e&&e[t]?e[t]:null),e);if(null!=n)return n}return t.split(".").reduce(((e,t)=>e&&null!=e[t]?e[t]:null),e)}function rt(e,t,n){let r,o=arguments.length>3&&void 0!==arguments[3]?arguments[3]:n;return r="function"===typeof e?e(n):Array.isArray(e)?e[n]||o:nt(e,n)||o,t&&(r=t(r,o,e)),r}const ot=function(e){const{prop:t,cssProperty:n=e.prop,themeKey:r,transform:o}=e,a=e=>{if(null==e[t])return null;const a=e[t],i=nt(e.theme,r)||{};return Ye(e,a,(e=>{let r=rt(i,o,e);return e===r&&"string"===typeof e&&(r=rt(i,o,"".concat(t).concat("default"===e?"":tt(e)),e)),!1===n?r:{[n]:r}}))};return a.propTypes={},a.filterProps=[t],a};const at=function(e,t){return t?Ge(e,t,{clone:!1}):e};const it={m:"margin",p:"padding"},st={t:"Top",r:"Right",b:"Bottom",l:"Left",x:["Left","Right"],y:["Top","Bottom"]},lt={marginX:"mx",marginY:"my",paddingX:"px",paddingY:"py"},ut=function(e){const t={};return n=>(void 0===t[n]&&(t[n]=e(n)),t[n])}((e=>{if(e.length>2){if(!lt[e])return[e];e=lt[e]}const[t,n]=e.split(""),r=it[t],o=st[n]||"";return Array.isArray(o)?o.map((e=>r+e)):[r+o]})),ct=["m","mt","mr","mb","ml","mx","my","margin","marginTop","marginRight","marginBottom","marginLeft","marginX","marginY","marginInline","marginInlineStart","marginInlineEnd","marginBlock","marginBlockStart","marginBlockEnd"],dt=["p","pt","pr","pb","pl","px","py","padding","paddingTop","paddingRight","paddingBottom","paddingLeft","paddingX","paddingY","paddingInline","paddingInlineStart","paddingInlineEnd","paddingBlock","paddingBlockStart","paddingBlockEnd"],ft=[...ct,...dt];function pt(e,t,n,r){var o;const a=null!=(o=nt(e,t,!1))?o:n;return"number"===typeof a?e=>"string"===typeof e?e:a*e:Array.isArray(a)?e=>"string"===typeof e?e:a[e]:"function"===typeof a?a:()=>{}}function ht(e){return pt(e,"spacing",8)}function gt(e,t){if("string"===typeof t||null==t)return t;const n=e(Math.abs(t));return t>=0?n:"number"===typeof n?-n:"-".concat(n)}function mt(e,t,n,r){if(-1===t.indexOf(n))return null;const o=function(e,t){return n=>e.reduce(((e,r)=>(e[r]=gt(t,n),e)),{})}(ut(n),r);return Ye(e,e[n],o)}function yt(e,t){const n=ht(e.theme);return Object.keys(e).map((r=>mt(e,t,r,n))).reduce(at,{})}function vt(e){return yt(e,ct)}function bt(e){return yt(e,dt)}function wt(e){return yt(e,ft)}vt.propTypes={},vt.filterProps=ct,bt.propTypes={},bt.filterProps=dt,wt.propTypes={},wt.filterProps=ft;const kt=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const r=t.reduce(((e,t)=>(t.filterProps.forEach((n=>{e[n]=t})),e)),{}),o=e=>Object.keys(e).reduce(((t,n)=>r[n]?at(t,r[n](e)):t),{});return o.propTypes={},o.filterProps=t.reduce(((e,t)=>e.concat(t.filterProps)),[]),o};function St(e){return"number"!==typeof e?e:"".concat(e,"px solid")}function xt(e,t){return ot({prop:e,themeKey:"borders",transform:t})}const Et=xt("border",St),Ct=xt("borderTop",St),Pt=xt("borderRight",St),Ot=xt("borderBottom",St),Rt=xt("borderLeft",St),Tt=xt("borderColor"),Nt=xt("borderTopColor"),At=xt("borderRightColor"),Lt=xt("borderBottomColor"),_t=xt("borderLeftColor"),jt=xt("outline",St),Dt=xt("outlineColor"),It=e=>{if(void 0!==e.borderRadius&&null!==e.borderRadius){const t=pt(e.theme,"shape.borderRadius",4),n=e=>({borderRadius:gt(t,e)});return Ye(e,e.borderRadius,n)}return null};It.propTypes={},It.filterProps=["borderRadius"];kt(Et,Ct,Pt,Ot,Rt,Tt,Nt,At,Lt,_t,It,jt,Dt);const Ft=e=>{if(void 0!==e.gap&&null!==e.gap){const t=pt(e.theme,"spacing",8),n=e=>({gap:gt(t,e)});return Ye(e,e.gap,n)}return null};Ft.propTypes={},Ft.filterProps=["gap"];const zt=e=>{if(void 0!==e.columnGap&&null!==e.columnGap){const t=pt(e.theme,"spacing",8),n=e=>({columnGap:gt(t,e)});return Ye(e,e.columnGap,n)}return null};zt.propTypes={},zt.filterProps=["columnGap"];const Mt=e=>{if(void 0!==e.rowGap&&null!==e.rowGap){const t=pt(e.theme,"spacing",8),n=e=>({rowGap:gt(t,e)});return Ye(e,e.rowGap,n)}return null};Mt.propTypes={},Mt.filterProps=["rowGap"];kt(Ft,zt,Mt,ot({prop:"gridColumn"}),ot({prop:"gridRow"}),ot({prop:"gridAutoFlow"}),ot({prop:"gridAutoColumns"}),ot({prop:"gridAutoRows"}),ot({prop:"gridTemplateColumns"}),ot({prop:"gridTemplateRows"}),ot({prop:"gridTemplateAreas"}),ot({prop:"gridArea"}));function Bt(e,t){return"grey"===t?t:e}kt(ot({prop:"color",themeKey:"palette",transform:Bt}),ot({prop:"bgcolor",cssProperty:"backgroundColor",themeKey:"palette",transform:Bt}),ot({prop:"backgroundColor",themeKey:"palette",transform:Bt}));function Ut(e){return e<=1&&0!==e?"".concat(100*e,"%"):e}const Vt=ot({prop:"width",transform:Ut}),Wt=e=>{if(void 0!==e.maxWidth&&null!==e.maxWidth){const t=t=>{var n,r;const o=(null==(n=e.theme)||null==(n=n.breakpoints)||null==(n=n.values)?void 0:n[t])||Je[t];return o?"px"!==(null==(r=e.theme)||null==(r=r.breakpoints)?void 0:r.unit)?{maxWidth:"".concat(o).concat(e.theme.breakpoints.unit)}:{maxWidth:o}:{maxWidth:Ut(t)}};return Ye(e,e.maxWidth,t)}return null};Wt.filterProps=["maxWidth"];const Ht=ot({prop:"minWidth",transform:Ut}),$t=ot({prop:"height",transform:Ut}),Kt=ot({prop:"maxHeight",transform:Ut}),qt=ot({prop:"minHeight",transform:Ut}),Qt=(ot({prop:"size",cssProperty:"width",transform:Ut}),ot({prop:"size",cssProperty:"height",transform:Ut}),kt(Vt,Wt,Ht,$t,Kt,qt,ot({prop:"boxSizing"})),{border:{themeKey:"borders",transform:St},borderTop:{themeKey:"borders",transform:St},borderRight:{themeKey:"borders",transform:St},borderBottom:{themeKey:"borders",transform:St},borderLeft:{themeKey:"borders",transform:St},borderColor:{themeKey:"palette"},borderTopColor:{themeKey:"palette"},borderRightColor:{themeKey:"palette"},borderBottomColor:{themeKey:"palette"},borderLeftColor:{themeKey:"palette"},outline:{themeKey:"borders",transform:St},outlineColor:{themeKey:"palette"},borderRadius:{themeKey:"shape.borderRadius",style:It},color:{themeKey:"palette",transform:Bt},bgcolor:{themeKey:"palette",cssProperty:"backgroundColor",transform:Bt},backgroundColor:{themeKey:"palette",transform:Bt},p:{style:bt},pt:{style:bt},pr:{style:bt},pb:{style:bt},pl:{style:bt},px:{style:bt},py:{style:bt},padding:{style:bt},paddingTop:{style:bt},paddingRight:{style:bt},paddingBottom:{style:bt},paddingLeft:{style:bt},paddingX:{style:bt},paddingY:{style:bt},paddingInline:{style:bt},paddingInlineStart:{style:bt},paddingInlineEnd:{style:bt},paddingBlock:{style:bt},paddingBlockStart:{style:bt},paddingBlockEnd:{style:bt},m:{style:vt},mt:{style:vt},mr:{style:vt},mb:{style:vt},ml:{style:vt},mx:{style:vt},my:{style:vt},margin:{style:vt},marginTop:{style:vt},marginRight:{style:vt},marginBottom:{style:vt},marginLeft:{style:vt},marginX:{style:vt},marginY:{style:vt},marginInline:{style:vt},marginInlineStart:{style:vt},marginInlineEnd:{style:vt},marginBlock:{style:vt},marginBlockStart:{style:vt},marginBlockEnd:{style:vt},displayPrint:{cssProperty:!1,transform:e=>({"@media print":{display:e}})},display:{},overflow:{},textOverflow:{},visibility:{},whiteSpace:{},flexBasis:{},flexDirection:{},flexWrap:{},justifyContent:{},alignItems:{},alignContent:{},order:{},flex:{},flexGrow:{},flexShrink:{},alignSelf:{},justifyItems:{},justifySelf:{},gap:{style:Ft},rowGap:{style:Mt},columnGap:{style:zt},gridColumn:{},gridRow:{},gridAutoFlow:{},gridAutoColumns:{},gridAutoRows:{},gridTemplateColumns:{},gridTemplateRows:{},gridTemplateAreas:{},gridArea:{},position:{},zIndex:{themeKey:"zIndex"},top:{},right:{},bottom:{},left:{},boxShadow:{themeKey:"shadows"},width:{transform:Ut},maxWidth:{style:Wt},minWidth:{transform:Ut},height:{transform:Ut},maxHeight:{transform:Ut},minHeight:{transform:Ut},boxSizing:{},fontFamily:{themeKey:"typography"},fontSize:{themeKey:"typography"},fontStyle:{themeKey:"typography"},fontWeight:{themeKey:"typography"},letterSpacing:{},textTransform:{},lineHeight:{},textAlign:{},typography:{cssProperty:!1,themeKey:"typography"}});const Gt=function(){function e(e,t,n,r){const o={[e]:t,theme:n},a=r[e];if(!a)return{[e]:t};const{cssProperty:i=e,themeKey:s,transform:l,style:u}=a;if(null==t)return null;if("typography"===s&&"inherit"===t)return{[e]:t};const c=nt(n,s)||{};if(u)return u(o);return Ye(o,t,(t=>{let n=rt(c,l,t);return t===n&&"string"===typeof t&&(n=rt(c,l,"".concat(e).concat("default"===t?"":tt(t)),t)),!1===i?n:{[i]:n}}))}return function t(n){var r;const{sx:o,theme:a={}}=n||{};if(!o)return null;const i=null!=(r=a.unstable_sxConfig)?r:Qt;function s(n){let r=n;if("function"===typeof n)r=n(a);else if("object"!==typeof n)return n;if(!r)return null;const o=Ze(a.breakpoints),s=Object.keys(o);let l=o;return Object.keys(r).forEach((n=>{const o=(s=r[n],u=a,"function"===typeof s?s(u):s);var s,u;if(null!==o&&void 0!==o)if("object"===typeof o)if(i[n])l=at(l,e(n,o,a,i));else{const e=Ye({theme:a},o,(e=>({[n]:e})));!function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const r=t.reduce(((e,t)=>e.concat(Object.keys(t))),[]),o=new Set(r);return t.every((e=>o.size===Object.keys(e).length))}(e,o)?l=at(l,e):l[n]=t({sx:o,theme:a})}else l=at(l,e(n,o,a,i))})),et(s,l)}return Array.isArray(o)?o.map(s):s(o)}}();Gt.filterProps=["sx"];const Jt=Gt,Xt=["values","unit","step"];function Yt(e){const{values:t={xs:0,sm:600,md:900,lg:1200,xl:1536},unit:n="px",step:r=5}=e,o=$e(e,Xt),a=(e=>{const t=Object.keys(e).map((t=>({key:t,val:e[t]})))||[];return t.sort(((e,t)=>e.val-t.val)),t.reduce(((e,t)=>He({},e,{[t.key]:t.val})),{})})(t),i=Object.keys(a);function s(e){const r="number"===typeof t[e]?t[e]:e;return"@media (min-width:".concat(r).concat(n,")")}function l(e){const o="number"===typeof t[e]?t[e]:e;return"@media (max-width:".concat(o-r/100).concat(n,")")}function u(e,o){const a=i.indexOf(o);return"@media (min-width:".concat("number"===typeof t[e]?t[e]:e).concat(n,") and ")+"(max-width:".concat((-1!==a&&"number"===typeof t[i[a]]?t[i[a]]:o)-r/100).concat(n,")")}return He({keys:i,values:a,up:s,down:l,between:u,only:function(e){return i.indexOf(e)+1<i.length?u(e,i[i.indexOf(e)+1]):s(e)},not:function(e){const t=i.indexOf(e);return 0===t?s(i[1]):t===i.length-1?l(i[t]):u(e,i[i.indexOf(e)+1]).replace("@media","@media not all and")},unit:n},o)}const Zt={borderRadius:4};function en(e,t){const n=this;if(n.vars&&"function"===typeof n.getColorSchemeSelector){const r=n.getColorSchemeSelector(e).replace(/(\[[^\]]+\])/,"*:where($1)");return{[r]:t}}return n.palette.mode===e?t:{}}const tn=["breakpoints","palette","spacing","shape"];const nn=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{breakpoints:t={},palette:n={},spacing:r,shape:o={}}=e,a=$e(e,tn),i=Yt(t),s=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:8;if(e.mui)return e;const t=ht({spacing:e}),n=function(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return(0===n.length?[1]:n).map((e=>{const n=t(e);return"number"===typeof n?"".concat(n,"px"):n})).join(" ")};return n.mui=!0,n}(r);let l=Ge({breakpoints:i,direction:"ltr",components:{},palette:He({mode:"light"},n),spacing:s,shape:He({},Zt,o)},a);l.applyStyles=en;for(var u=arguments.length,c=new Array(u>1?u-1:0),d=1;d<u;d++)c[d-1]=arguments[d];return l=c.reduce(((e,t)=>Ge(e,t)),l),l.unstable_sxConfig=He({},Qt,null==a?void 0:a.unstable_sxConfig),l.unstable_sx=function(e){return Jt({sx:e,theme:this})},l};function rn(e,t){return He({toolbar:{minHeight:56,[e.up("xs")]:{"@media (orientation: landscape)":{minHeight:48}},[e.up("sm")]:{minHeight:64}}},t)}var on=n(266);const an={black:"#000",white:"#fff"},sn={50:"#fafafa",100:"#f5f5f5",200:"#eeeeee",300:"#e0e0e0",400:"#bdbdbd",500:"#9e9e9e",600:"#757575",700:"#616161",800:"#424242",900:"#212121",A100:"#f5f5f5",A200:"#eeeeee",A400:"#bdbdbd",A700:"#616161"},ln={50:"#f3e5f5",100:"#e1bee7",200:"#ce93d8",300:"#ba68c8",400:"#ab47bc",500:"#9c27b0",600:"#8e24aa",700:"#7b1fa2",800:"#6a1b9a",900:"#4a148c",A100:"#ea80fc",A200:"#e040fb",A400:"#d500f9",A700:"#aa00ff"},un={50:"#ffebee",100:"#ffcdd2",200:"#ef9a9a",300:"#e57373",400:"#ef5350",500:"#f44336",600:"#e53935",700:"#d32f2f",800:"#c62828",900:"#b71c1c",A100:"#ff8a80",A200:"#ff5252",A400:"#ff1744",A700:"#d50000"},cn={50:"#fff3e0",100:"#ffe0b2",200:"#ffcc80",300:"#ffb74d",400:"#ffa726",500:"#ff9800",600:"#fb8c00",700:"#f57c00",800:"#ef6c00",900:"#e65100",A100:"#ffd180",A200:"#ffab40",A400:"#ff9100",A700:"#ff6d00"},dn={50:"#e3f2fd",100:"#bbdefb",200:"#90caf9",300:"#64b5f6",400:"#42a5f5",500:"#2196f3",600:"#1e88e5",700:"#1976d2",800:"#1565c0",900:"#0d47a1",A100:"#82b1ff",A200:"#448aff",A400:"#2979ff",A700:"#2962ff"},fn={50:"#e1f5fe",100:"#b3e5fc",200:"#81d4fa",300:"#4fc3f7",400:"#29b6f6",500:"#03a9f4",600:"#039be5",700:"#0288d1",800:"#0277bd",900:"#01579b",A100:"#80d8ff",A200:"#40c4ff",A400:"#00b0ff",A700:"#0091ea"},pn={50:"#e8f5e9",100:"#c8e6c9",200:"#a5d6a7",300:"#81c784",400:"#66bb6a",500:"#4caf50",600:"#43a047",700:"#388e3c",800:"#2e7d32",900:"#1b5e20",A100:"#b9f6ca",A200:"#69f0ae",A400:"#00e676",A700:"#00c853"},hn=["mode","contrastThreshold","tonalOffset"],gn={text:{primary:"rgba(0, 0, 0, 0.87)",secondary:"rgba(0, 0, 0, 0.6)",disabled:"rgba(0, 0, 0, 0.38)"},divider:"rgba(0, 0, 0, 0.12)",background:{paper:an.white,default:an.white},action:{active:"rgba(0, 0, 0, 0.54)",hover:"rgba(0, 0, 0, 0.04)",hoverOpacity:.04,selected:"rgba(0, 0, 0, 0.08)",selectedOpacity:.08,disabled:"rgba(0, 0, 0, 0.26)",disabledBackground:"rgba(0, 0, 0, 0.12)",disabledOpacity:.38,focus:"rgba(0, 0, 0, 0.12)",focusOpacity:.12,activatedOpacity:.12}},mn={text:{primary:an.white,secondary:"rgba(255, 255, 255, 0.7)",disabled:"rgba(255, 255, 255, 0.5)",icon:"rgba(255, 255, 255, 0.5)"},divider:"rgba(255, 255, 255, 0.12)",background:{paper:"#121212",default:"#121212"},action:{active:an.white,hover:"rgba(255, 255, 255, 0.08)",hoverOpacity:.08,selected:"rgba(255, 255, 255, 0.16)",selectedOpacity:.16,disabled:"rgba(255, 255, 255, 0.3)",disabledBackground:"rgba(255, 255, 255, 0.12)",disabledOpacity:.38,focus:"rgba(255, 255, 255, 0.12)",focusOpacity:.12,activatedOpacity:.24}};function yn(e,t,n,r){const o=r.light||r,a=r.dark||1.5*r;e[t]||(e.hasOwnProperty(n)?e[t]=e[n]:"light"===t?e.light=(0,on.a)(e.main,o):"dark"===t&&(e.dark=(0,on.e$)(e.main,a)))}function vn(e){const{mode:t="light",contrastThreshold:n=3,tonalOffset:r=.2}=e,o=$e(e,hn),a=e.primary||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:dn[200],light:dn[50],dark:dn[400]}:{main:dn[700],light:dn[400],dark:dn[800]}}(t),i=e.secondary||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:ln[200],light:ln[50],dark:ln[400]}:{main:ln[500],light:ln[300],dark:ln[700]}}(t),s=e.error||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:un[500],light:un[300],dark:un[700]}:{main:un[700],light:un[400],dark:un[800]}}(t),l=e.info||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:fn[400],light:fn[300],dark:fn[700]}:{main:fn[700],light:fn[500],dark:fn[900]}}(t),u=e.success||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:pn[400],light:pn[300],dark:pn[700]}:{main:pn[800],light:pn[500],dark:pn[900]}}(t),c=e.warning||function(){return"dark"===(arguments.length>0&&void 0!==arguments[0]?arguments[0]:"light")?{main:cn[400],light:cn[300],dark:cn[700]}:{main:"#ed6c02",light:cn[500],dark:cn[900]}}(t);function d(e){return(0,on.eM)(e,mn.text.primary)>=n?mn.text.primary:gn.text.primary}const f=e=>{let{color:t,name:n,mainShade:o=500,lightShade:a=300,darkShade:i=700}=e;if(t=He({},t),!t.main&&t[o]&&(t.main=t[o]),!t.hasOwnProperty("main"))throw new Error((0,Ke.A)(11,n?" (".concat(n,")"):"",o));if("string"!==typeof t.main)throw new Error((0,Ke.A)(12,n?" (".concat(n,")"):"",JSON.stringify(t.main)));return yn(t,"light",a,r),yn(t,"dark",i,r),t.contrastText||(t.contrastText=d(t.main)),t},p={dark:mn,light:gn};return Ge(He({common:He({},an),mode:t,primary:f({color:a,name:"primary"}),secondary:f({color:i,name:"secondary",mainShade:"A400",lightShade:"A200",darkShade:"A700"}),error:f({color:s,name:"error"}),warning:f({color:c,name:"warning"}),info:f({color:l,name:"info"}),success:f({color:u,name:"success"}),grey:sn,contrastThreshold:n,getContrastText:d,augmentColor:f,tonalOffset:r},p[t]),o)}const bn=["fontFamily","fontSize","fontWeightLight","fontWeightRegular","fontWeightMedium","fontWeightBold","htmlFontSize","allVariants","pxToRem"];const wn={textTransform:"uppercase"},kn='"Roboto", "Helvetica", "Arial", sans-serif';function Sn(e,t){const n="function"===typeof t?t(e):t,{fontFamily:r=kn,fontSize:o=14,fontWeightLight:a=300,fontWeightRegular:i=400,fontWeightMedium:s=500,fontWeightBold:l=700,htmlFontSize:u=16,allVariants:c,pxToRem:d}=n,f=$e(n,bn);const p=o/14,h=d||(e=>"".concat(e/u*p,"rem")),g=(e,t,n,o,a)=>{return He({fontFamily:r,fontWeight:e,fontSize:h(t),lineHeight:n},r===kn?{letterSpacing:"".concat((i=o/t,Math.round(1e5*i)/1e5),"em")}:{},a,c);var i},m={h1:g(a,96,1.167,-1.5),h2:g(a,60,1.2,-.5),h3:g(i,48,1.167,0),h4:g(i,34,1.235,.25),h5:g(i,24,1.334,0),h6:g(s,20,1.6,.15),subtitle1:g(i,16,1.75,.15),subtitle2:g(s,14,1.57,.1),body1:g(i,16,1.5,.15),body2:g(i,14,1.43,.15),button:g(s,14,1.75,.4,wn),caption:g(i,12,1.66,.4),overline:g(i,12,2.66,1,wn),inherit:{fontFamily:"inherit",fontWeight:"inherit",fontSize:"inherit",lineHeight:"inherit",letterSpacing:"inherit"}};return Ge(He({htmlFontSize:u,pxToRem:h,fontFamily:r,fontSize:o,fontWeightLight:a,fontWeightRegular:i,fontWeightMedium:s,fontWeightBold:l},m),f,{clone:!1})}function xn(){return["".concat(arguments.length<=0?void 0:arguments[0],"px ").concat(arguments.length<=1?void 0:arguments[1],"px ").concat(arguments.length<=2?void 0:arguments[2],"px ").concat(arguments.length<=3?void 0:arguments[3],"px rgba(0,0,0,").concat(.2,")"),"".concat(arguments.length<=4?void 0:arguments[4],"px ").concat(arguments.length<=5?void 0:arguments[5],"px ").concat(arguments.length<=6?void 0:arguments[6],"px ").concat(arguments.length<=7?void 0:arguments[7],"px rgba(0,0,0,").concat(.14,")"),"".concat(arguments.length<=8?void 0:arguments[8],"px ").concat(arguments.length<=9?void 0:arguments[9],"px ").concat(arguments.length<=10?void 0:arguments[10],"px ").concat(arguments.length<=11?void 0:arguments[11],"px rgba(0,0,0,").concat(.12,")")].join(",")}const En=["none",xn(0,2,1,-1,0,1,1,0,0,1,3,0),xn(0,3,1,-2,0,2,2,0,0,1,5,0),xn(0,3,3,-2,0,3,4,0,0,1,8,0),xn(0,2,4,-1,0,4,5,0,0,1,10,0),xn(0,3,5,-1,0,5,8,0,0,1,14,0),xn(0,3,5,-1,0,6,10,0,0,1,18,0),xn(0,4,5,-2,0,7,10,1,0,2,16,1),xn(0,5,5,-3,0,8,10,1,0,3,14,2),xn(0,5,6,-3,0,9,12,1,0,3,16,2),xn(0,6,6,-3,0,10,14,1,0,4,18,3),xn(0,6,7,-4,0,11,15,1,0,4,20,3),xn(0,7,8,-4,0,12,17,2,0,5,22,4),xn(0,7,8,-4,0,13,19,2,0,5,24,4),xn(0,7,9,-4,0,14,21,2,0,5,26,4),xn(0,8,9,-5,0,15,22,2,0,6,28,5),xn(0,8,10,-5,0,16,24,2,0,6,30,5),xn(0,8,11,-5,0,17,26,2,0,6,32,5),xn(0,9,11,-5,0,18,28,2,0,7,34,6),xn(0,9,12,-6,0,19,29,2,0,7,36,6),xn(0,10,13,-6,0,20,31,3,0,8,38,7),xn(0,10,13,-6,0,21,33,3,0,8,40,7),xn(0,10,14,-6,0,22,35,3,0,8,42,7),xn(0,11,14,-7,0,23,36,3,0,9,44,8),xn(0,11,15,-7,0,24,38,3,0,9,46,8)],Cn=["duration","easing","delay"],Pn={easeInOut:"cubic-bezier(0.4, 0, 0.2, 1)",easeOut:"cubic-bezier(0.0, 0, 0.2, 1)",easeIn:"cubic-bezier(0.4, 0, 1, 1)",sharp:"cubic-bezier(0.4, 0, 0.6, 1)"},On={shortest:150,shorter:200,short:250,standard:300,complex:375,enteringScreen:225,leavingScreen:195};function Rn(e){return"".concat(Math.round(e),"ms")}function Tn(e){if(!e)return 0;const t=e/36;return Math.round(10*(4+15*t**.25+t/5))}function Nn(e){const t=He({},Pn,e.easing),n=He({},On,e.duration);return He({getAutoHeightDuration:Tn,create:function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:["all"],r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const{duration:o=n.standard,easing:a=t.easeInOut,delay:i=0}=r;$e(r,Cn);return(Array.isArray(e)?e:[e]).map((e=>"".concat(e," ").concat("string"===typeof o?o:Rn(o)," ").concat(a," ").concat("string"===typeof i?i:Rn(i)))).join(",")}},e,{easing:t,duration:n})}const An={mobileStepper:1e3,fab:1050,speedDial:1050,appBar:1100,drawer:1200,modal:1300,snackbar:1400,tooltip:1500},Ln=["breakpoints","mixins","spacing","palette","transitions","typography","shape"];function _n(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const{mixins:t={},palette:n={},transitions:r={},typography:o={}}=e,a=$e(e,Ln);if(e.vars&&void 0===e.generateCssVars)throw new Error((0,Ke.A)(18));const i=vn(n),s=nn(e);let l=Ge(s,{mixins:rn(s.breakpoints,t),palette:i,shadows:En.slice(),typography:Sn(i,o),transitions:Nn(r),zIndex:He({},An)});l=Ge(l,a);for(var u=arguments.length,c=new Array(u>1?u-1:0),d=1;d<u;d++)c[d-1]=arguments[d];return l=c.reduce(((e,t)=>Ge(e,t)),l),l.unstable_sxConfig=He({},Qt,null==a?void 0:a.unstable_sxConfig),l.unstable_sx=function(e){return Jt({sx:e,theme:this})},l}const jn=_n;const Dn=t.createContext(null);function In(){return t.useContext(Dn)}const Fn="function"===typeof Symbol&&Symbol.for?Symbol.for("mui.nested"):"__THEME_NESTED__";var zn=n(579);const Mn=function(e){const{children:n,theme:r}=e,o=In(),a=t.useMemo((()=>{const e=null===o?r:function(e,t){if("function"===typeof t)return t(e);return He({},e,t)}(o,r);return null!=e&&(e[Fn]=null!==o),e}),[r,o]);return(0,zn.jsx)(Dn.Provider,{value:a,children:n})};var Bn=function(){function e(e){var t=this;this._insertTag=function(e){var n;n=0===t.tags.length?t.insertionPoint?t.insertionPoint.nextSibling:t.prepend?t.container.firstChild:t.before:t.tags[t.tags.length-1].nextSibling,t.container.insertBefore(e,n),t.tags.push(e)},this.isSpeedy=void 0===e.speedy||e.speedy,this.tags=[],this.ctr=0,this.nonce=e.nonce,this.key=e.key,this.container=e.container,this.prepend=e.prepend,this.insertionPoint=e.insertionPoint,this.before=null}var t=e.prototype;return t.hydrate=function(e){e.forEach(this._insertTag)},t.insert=function(e){this.ctr%(this.isSpeedy?65e3:1)===0&&this._insertTag(function(e){var t=document.createElement("style");return t.setAttribute("data-emotion",e.key),void 0!==e.nonce&&t.setAttribute("nonce",e.nonce),t.appendChild(document.createTextNode("")),t.setAttribute("data-s",""),t}(this));var t=this.tags[this.tags.length-1];if(this.isSpeedy){var n=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]}(t);try{n.insertRule(e,n.cssRules.length)}catch(af){}}else t.appendChild(document.createTextNode(e));this.ctr++},t.flush=function(){this.tags.forEach((function(e){var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)})),this.tags=[],this.ctr=0},e}(),Un=Math.abs,Vn=String.fromCharCode,Wn=Object.assign;function Hn(e){return e.trim()}function $n(e,t,n){return e.replace(t,n)}function Kn(e,t){return e.indexOf(t)}function qn(e,t){return 0|e.charCodeAt(t)}function Qn(e,t,n){return e.slice(t,n)}function Gn(e){return e.length}function Jn(e){return e.length}function Xn(e,t){return t.push(e),e}var Yn=1,Zn=1,er=0,tr=0,nr=0,rr="";function or(e,t,n,r,o,a,i){return{value:e,root:t,parent:n,type:r,props:o,children:a,line:Yn,column:Zn,length:i,return:""}}function ar(e,t){return Wn(or("",null,null,"",null,null,0),e,{length:-e.length},t)}function ir(){return nr=tr>0?qn(rr,--tr):0,Zn--,10===nr&&(Zn=1,Yn--),nr}function sr(){return nr=tr<er?qn(rr,tr++):0,Zn++,10===nr&&(Zn=1,Yn++),nr}function lr(){return qn(rr,tr)}function ur(){return tr}function cr(e,t){return Qn(rr,e,t)}function dr(e){switch(e){case 0:case 9:case 10:case 13:case 32:return 5;case 33:case 43:case 44:case 47:case 62:case 64:case 126:case 59:case 123:case 125:return 4;case 58:return 3;case 34:case 39:case 40:case 91:return 2;case 41:case 93:return 1}return 0}function fr(e){return Yn=Zn=1,er=Gn(rr=e),tr=0,[]}function pr(e){return rr="",e}function hr(e){return Hn(cr(tr-1,yr(91===e?e+2:40===e?e+1:e)))}function gr(e){for(;(nr=lr())&&nr<33;)sr();return dr(e)>2||dr(nr)>3?"":" "}function mr(e,t){for(;--t&&sr()&&!(nr<48||nr>102||nr>57&&nr<65||nr>70&&nr<97););return cr(e,ur()+(t<6&&32==lr()&&32==sr()))}function yr(e){for(;sr();)switch(nr){case e:return tr;case 34:case 39:34!==e&&39!==e&&yr(nr);break;case 40:41===e&&yr(e);break;case 92:sr()}return tr}function vr(e,t){for(;sr()&&e+nr!==57&&(e+nr!==84||47!==lr()););return"/*"+cr(t,tr-1)+"*"+Vn(47===e?e:sr())}function br(e){for(;!dr(lr());)sr();return cr(e,tr)}var wr="-ms-",kr="-moz-",Sr="-webkit-",xr="comm",Er="rule",Cr="decl",Pr="@keyframes";function Or(e,t){for(var n="",r=Jn(e),o=0;o<r;o++)n+=t(e[o],o,e,t)||"";return n}function Rr(e,t,n,r){switch(e.type){case"@layer":if(e.children.length)break;case"@import":case Cr:return e.return=e.return||e.value;case xr:return"";case Pr:return e.return=e.value+"{"+Or(e.children,r)+"}";case Er:e.value=e.props.join(",")}return Gn(n=Or(e.children,r))?e.return=e.value+"{"+n+"}":""}function Tr(e){return pr(Nr("",null,null,null,[""],e=fr(e),0,[0],e))}function Nr(e,t,n,r,o,a,i,s,l){for(var u=0,c=0,d=i,f=0,p=0,h=0,g=1,m=1,y=1,v=0,b="",w=o,k=a,S=r,x=b;m;)switch(h=v,v=sr()){case 40:if(108!=h&&58==qn(x,d-1)){-1!=Kn(x+=$n(hr(v),"&","&\f"),"&\f")&&(y=-1);break}case 34:case 39:case 91:x+=hr(v);break;case 9:case 10:case 13:case 32:x+=gr(h);break;case 92:x+=mr(ur()-1,7);continue;case 47:switch(lr()){case 42:case 47:Xn(Lr(vr(sr(),ur()),t,n),l);break;default:x+="/"}break;case 123*g:s[u++]=Gn(x)*y;case 125*g:case 59:case 0:switch(v){case 0:case 125:m=0;case 59+c:-1==y&&(x=$n(x,/\f/g,"")),p>0&&Gn(x)-d&&Xn(p>32?_r(x+";",r,n,d-1):_r($n(x," ","")+";",r,n,d-2),l);break;case 59:x+=";";default:if(Xn(S=Ar(x,t,n,u,c,o,s,b,w=[],k=[],d),a),123===v)if(0===c)Nr(x,t,S,S,w,a,d,s,k);else switch(99===f&&110===qn(x,3)?100:f){case 100:case 108:case 109:case 115:Nr(e,S,S,r&&Xn(Ar(e,S,S,0,0,o,s,b,o,w=[],d),k),o,k,d,s,r?w:k);break;default:Nr(x,S,S,S,[""],k,0,s,k)}}u=c=p=0,g=y=1,b=x="",d=i;break;case 58:d=1+Gn(x),p=h;default:if(g<1)if(123==v)--g;else if(125==v&&0==g++&&125==ir())continue;switch(x+=Vn(v),v*g){case 38:y=c>0?1:(x+="\f",-1);break;case 44:s[u++]=(Gn(x)-1)*y,y=1;break;case 64:45===lr()&&(x+=hr(sr())),f=lr(),c=d=Gn(b=x+=br(ur())),v++;break;case 45:45===h&&2==Gn(x)&&(g=0)}}return a}function Ar(e,t,n,r,o,a,i,s,l,u,c){for(var d=o-1,f=0===o?a:[""],p=Jn(f),h=0,g=0,m=0;h<r;++h)for(var y=0,v=Qn(e,d+1,d=Un(g=i[h])),b=e;y<p;++y)(b=Hn(g>0?f[y]+" "+v:$n(v,/&\f/g,f[y])))&&(l[m++]=b);return or(e,t,n,0===o?Er:s,l,u,c)}function Lr(e,t,n){return or(e,t,n,xr,Vn(nr),Qn(e,2,-2),0)}function _r(e,t,n,r){return or(e,t,n,Cr,Qn(e,0,r),Qn(e,r+1,-1),r)}var jr=function(e,t,n){for(var r=0,o=0;r=o,o=lr(),38===r&&12===o&&(t[n]=1),!dr(o);)sr();return cr(e,tr)},Dr=function(e,t){return pr(function(e,t){var n=-1,r=44;do{switch(dr(r)){case 0:38===r&&12===lr()&&(t[n]=1),e[n]+=jr(tr-1,t,n);break;case 2:e[n]+=hr(r);break;case 4:if(44===r){e[++n]=58===lr()?"&\f":"",t[n]=e[n].length;break}default:e[n]+=Vn(r)}}while(r=sr());return e}(fr(e),t))},Ir=new WeakMap,Fr=function(e){if("rule"===e.type&&e.parent&&!(e.length<1)){for(var t=e.value,n=e.parent,r=e.column===n.column&&e.line===n.line;"rule"!==n.type;)if(!(n=n.parent))return;if((1!==e.props.length||58===t.charCodeAt(0)||Ir.get(n))&&!r){Ir.set(e,!0);for(var o=[],a=Dr(t,o),i=n.props,s=0,l=0;s<a.length;s++)for(var u=0;u<i.length;u++,l++)e.props[l]=o[s]?a[s].replace(/&\f/g,i[u]):i[u]+" "+a[s]}}},zr=function(e){if("decl"===e.type){var t=e.value;108===t.charCodeAt(0)&&98===t.charCodeAt(2)&&(e.return="",e.value="")}};function Mr(e,t){switch(function(e,t){return 45^qn(e,0)?(((t<<2^qn(e,0))<<2^qn(e,1))<<2^qn(e,2))<<2^qn(e,3):0}(e,t)){case 5103:return Sr+"print-"+e+e;case 5737:case 4201:case 3177:case 3433:case 1641:case 4457:case 2921:case 5572:case 6356:case 5844:case 3191:case 6645:case 3005:case 6391:case 5879:case 5623:case 6135:case 4599:case 4855:case 4215:case 6389:case 5109:case 5365:case 5621:case 3829:return Sr+e+e;case 5349:case 4246:case 4810:case 6968:case 2756:return Sr+e+kr+e+wr+e+e;case 6828:case 4268:return Sr+e+wr+e+e;case 6165:return Sr+e+wr+"flex-"+e+e;case 5187:return Sr+e+$n(e,/(\w+).+(:[^]+)/,Sr+"box-$1$2"+wr+"flex-$1$2")+e;case 5443:return Sr+e+wr+"flex-item-"+$n(e,/flex-|-self/,"")+e;case 4675:return Sr+e+wr+"flex-line-pack"+$n(e,/align-content|flex-|-self/,"")+e;case 5548:return Sr+e+wr+$n(e,"shrink","negative")+e;case 5292:return Sr+e+wr+$n(e,"basis","preferred-size")+e;case 6060:return Sr+"box-"+$n(e,"-grow","")+Sr+e+wr+$n(e,"grow","positive")+e;case 4554:return Sr+$n(e,/([^-])(transform)/g,"$1"+Sr+"$2")+e;case 6187:return $n($n($n(e,/(zoom-|grab)/,Sr+"$1"),/(image-set)/,Sr+"$1"),e,"")+e;case 5495:case 3959:return $n(e,/(image-set\([^]*)/,Sr+"$1$`$1");case 4968:return $n($n(e,/(.+:)(flex-)?(.*)/,Sr+"box-pack:$3"+wr+"flex-pack:$3"),/s.+-b[^;]+/,"justify")+Sr+e+e;case 4095:case 3583:case 4068:case 2532:return $n(e,/(.+)-inline(.+)/,Sr+"$1$2")+e;case 8116:case 7059:case 5753:case 5535:case 5445:case 5701:case 4933:case 4677:case 5533:case 5789:case 5021:case 4765:if(Gn(e)-1-t>6)switch(qn(e,t+1)){case 109:if(45!==qn(e,t+4))break;case 102:return $n(e,/(.+:)(.+)-([^]+)/,"$1"+Sr+"$2-$3$1"+kr+(108==qn(e,t+3)?"$3":"$2-$3"))+e;case 115:return~Kn(e,"stretch")?Mr($n(e,"stretch","fill-available"),t)+e:e}break;case 4949:if(115!==qn(e,t+1))break;case 6444:switch(qn(e,Gn(e)-3-(~Kn(e,"!important")&&10))){case 107:return $n(e,":",":"+Sr)+e;case 101:return $n(e,/(.+:)([^;!]+)(;|!.+)?/,"$1"+Sr+(45===qn(e,14)?"inline-":"")+"box$3$1"+Sr+"$2$3$1"+wr+"$2box$3")+e}break;case 5936:switch(qn(e,t+11)){case 114:return Sr+e+wr+$n(e,/[svh]\w+-[tblr]{2}/,"tb")+e;case 108:return Sr+e+wr+$n(e,/[svh]\w+-[tblr]{2}/,"tb-rl")+e;case 45:return Sr+e+wr+$n(e,/[svh]\w+-[tblr]{2}/,"lr")+e}return Sr+e+wr+e+e}return e}var Br=[function(e,t,n,r){if(e.length>-1&&!e.return)switch(e.type){case Cr:e.return=Mr(e.value,e.length);break;case Pr:return Or([ar(e,{value:$n(e.value,"@","@"+Sr)})],r);case Er:if(e.length)return function(e,t){return e.map(t).join("")}(e.props,(function(t){switch(function(e,t){return(e=t.exec(e))?e[0]:e}(t,/(::plac\w+|:read-\w+)/)){case":read-only":case":read-write":return Or([ar(e,{props:[$n(t,/:(read-\w+)/,":-moz-$1")]})],r);case"::placeholder":return Or([ar(e,{props:[$n(t,/:(plac\w+)/,":"+Sr+"input-$1")]}),ar(e,{props:[$n(t,/:(plac\w+)/,":-moz-$1")]}),ar(e,{props:[$n(t,/:(plac\w+)/,wr+"input-$1")]})],r)}return""}))}}],Ur=function(e){var t=e.key;if("css"===t){var n=document.querySelectorAll("style[data-emotion]:not([data-s])");Array.prototype.forEach.call(n,(function(e){-1!==e.getAttribute("data-emotion").indexOf(" ")&&(document.head.appendChild(e),e.setAttribute("data-s",""))}))}var r,o,a=e.stylisPlugins||Br,i={},s=[];r=e.container||document.head,Array.prototype.forEach.call(document.querySelectorAll('style[data-emotion^="'+t+' "]'),(function(e){for(var t=e.getAttribute("data-emotion").split(" "),n=1;n<t.length;n++)i[t[n]]=!0;s.push(e)}));var l,u,c=[Rr,(u=function(e){l.insert(e)},function(e){e.root||(e=e.return)&&u(e)})],d=function(e){var t=Jn(e);return function(n,r,o,a){for(var i="",s=0;s<t;s++)i+=e[s](n,r,o,a)||"";return i}}([Fr,zr].concat(a,c));o=function(e,t,n,r){l=n,function(e){Or(Tr(e),d)}(e?e+"{"+t.styles+"}":t.styles),r&&(f.inserted[t.name]=!0)};var f={key:t,sheet:new Bn({key:t,container:r,nonce:e.nonce,speedy:e.speedy,prepend:e.prepend,insertionPoint:e.insertionPoint}),nonce:e.nonce,inserted:i,registered:{},insert:o};return f.sheet.hydrate(s),f};var Vr=function(e,t,n){var r=e.key+"-"+t.name;!1===n&&void 0===e.registered[r]&&(e.registered[r]=t.styles)},Wr=function(e,t,n){Vr(e,t,n);var r=e.key+"-"+t.name;if(void 0===e.inserted[t.name]){var o=t;do{e.insert(t===o?"."+r:"",o,e.sheet,!0),o=o.next}while(void 0!==o)}};var Hr={animationIterationCount:1,aspectRatio:1,borderImageOutset:1,borderImageSlice:1,borderImageWidth:1,boxFlex:1,boxFlexGroup:1,boxOrdinalGroup:1,columnCount:1,columns:1,flex:1,flexGrow:1,flexPositive:1,flexShrink:1,flexNegative:1,flexOrder:1,gridRow:1,gridRowEnd:1,gridRowSpan:1,gridRowStart:1,gridColumn:1,gridColumnEnd:1,gridColumnSpan:1,gridColumnStart:1,msGridRow:1,msGridRowSpan:1,msGridColumn:1,msGridColumnSpan:1,fontWeight:1,lineHeight:1,opacity:1,order:1,orphans:1,scale:1,tabSize:1,widows:1,zIndex:1,zoom:1,WebkitLineClamp:1,fillOpacity:1,floodOpacity:1,stopOpacity:1,strokeDasharray:1,strokeDashoffset:1,strokeMiterlimit:1,strokeOpacity:1,strokeWidth:1};function $r(e){var t=Object.create(null);return function(n){return void 0===t[n]&&(t[n]=e(n)),t[n]}}var Kr=/[A-Z]|^ms/g,qr=/_EMO_([^_]+?)_([^]*?)_EMO_/g,Qr=function(e){return 45===e.charCodeAt(1)},Gr=function(e){return null!=e&&"boolean"!==typeof e},Jr=$r((function(e){return Qr(e)?e:e.replace(Kr,"-$&").toLowerCase()})),Xr=function(e,t){switch(e){case"animation":case"animationName":if("string"===typeof t)return t.replace(qr,(function(e,t,n){return Zr={name:t,styles:n,next:Zr},t}))}return 1===Hr[e]||Qr(e)||"number"!==typeof t||0===t?t:t+"px"};function Yr(e,t,n){if(null==n)return"";var r=n;if(void 0!==r.__emotion_styles)return r;switch(typeof n){case"boolean":return"";case"object":var o=n;if(1===o.anim)return Zr={name:o.name,styles:o.styles,next:Zr},o.name;var a=n;if(void 0!==a.styles){var i=a.next;if(void 0!==i)for(;void 0!==i;)Zr={name:i.name,styles:i.styles,next:Zr},i=i.next;return a.styles+";"}return function(e,t,n){var r="";if(Array.isArray(n))for(var o=0;o<n.length;o++)r+=Yr(e,t,n[o])+";";else for(var a in n){var i=n[a];if("object"!==typeof i){var s=i;null!=t&&void 0!==t[s]?r+=a+"{"+t[s]+"}":Gr(s)&&(r+=Jr(a)+":"+Xr(a,s)+";")}else if(!Array.isArray(i)||"string"!==typeof i[0]||null!=t&&void 0!==t[i[0]]){var l=Yr(e,t,i);switch(a){case"animation":case"animationName":r+=Jr(a)+":"+l+";";break;default:r+=a+"{"+l+"}"}}else for(var u=0;u<i.length;u++)Gr(i[u])&&(r+=Jr(a)+":"+Xr(a,i[u])+";")}return r}(e,t,n);case"function":if(void 0!==e){var s=Zr,l=n(e);return Zr=s,Yr(e,t,l)}}var u=n;if(null==t)return u;var c=t[u];return void 0!==c?c:u}var Zr,eo=/label:\s*([^\s;{]+)\s*(;|$)/g;function to(e,t,n){if(1===e.length&&"object"===typeof e[0]&&null!==e[0]&&void 0!==e[0].styles)return e[0];var r=!0,o="";Zr=void 0;var a=e[0];null==a||void 0===a.raw?(r=!1,o+=Yr(n,t,a)):o+=a[0];for(var i=1;i<e.length;i++){if(o+=Yr(n,t,e[i]),r)o+=a[i]}eo.lastIndex=0;for(var s,l="";null!==(s=eo.exec(o));)l+="-"+s[1];var u=function(e){for(var t,n=0,r=0,o=e.length;o>=4;++r,o-=4)t=***********(65535&(t=255&e.charCodeAt(r)|(255&e.charCodeAt(++r))<<8|(255&e.charCodeAt(++r))<<16|(255&e.charCodeAt(++r))<<24))+(59797*(t>>>16)<<16),n=***********(65535&(t^=t>>>24))+(59797*(t>>>16)<<16)^***********(65535&n)+(59797*(n>>>16)<<16);switch(o){case 3:n^=(255&e.charCodeAt(r+2))<<16;case 2:n^=(255&e.charCodeAt(r+1))<<8;case 1:n=***********(65535&(n^=255&e.charCodeAt(r)))+(59797*(n>>>16)<<16)}return(((n=***********(65535&(n^=n>>>13))+(59797*(n>>>16)<<16))^n>>>15)>>>0).toString(36)}(o)+l;return{name:u,styles:o,next:Zr}}var no=!!r.useInsertionEffect&&r.useInsertionEffect,ro=no||function(e){return e()},oo=no||t.useLayoutEffect,ao=t.createContext("undefined"!==typeof HTMLElement?Ur({key:"css"}):null),io=(ao.Provider,function(e){return(0,t.forwardRef)((function(n,r){var o=(0,t.useContext)(ao);return e(n,o,r)}))}),so=t.createContext({});var lo={}.hasOwnProperty,uo="__EMOTION_TYPE_PLEASE_DO_NOT_USE__",co=function(e){var t=e.cache,n=e.serialized,r=e.isStringTag;return Vr(t,n,r),ro((function(){return Wr(t,n,r)})),null},fo=io((function(e,n,r){var o=e.css;"string"===typeof o&&void 0!==n.registered[o]&&(o=n.registered[o]);var a=e[uo],i=[o],s="";"string"===typeof e.className?s=function(e,t,n){var r="";return n.split(" ").forEach((function(n){void 0!==e[n]?t.push(e[n]+";"):n&&(r+=n+" ")})),r}(n.registered,i,e.className):null!=e.className&&(s=e.className+" ");var l=to(i,void 0,t.useContext(so));s+=n.key+"-"+l.name;var u={};for(var c in e)lo.call(e,c)&&"css"!==c&&c!==uo&&(u[c]=e[c]);return u.className=s,r&&(u.ref=r),t.createElement(t.Fragment,null,t.createElement(co,{cache:n,serialized:l,isStringTag:"string"===typeof a}),t.createElement(a,u))})),po=fo;const ho=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null;const n=t.useContext(so);return n&&(r=n,0!==Object.keys(r).length)?n:e;var r},go=["value"],mo=t.createContext();const yo=function(e){let{value:t}=e,n=$e(e,go);return(0,zn.jsx)(mo.Provider,He({value:null==t||t},n))};function vo(e,t){const n=He({},t);return Object.keys(e).forEach((r=>{if(r.toString().match(/^(components|slots)$/))n[r]=He({},e[r],n[r]);else if(r.toString().match(/^(componentsProps|slotProps)$/)){const o=e[r]||{},a=t[r];n[r]={},a&&Object.keys(a)?o&&Object.keys(o)?(n[r]=He({},a),Object.keys(o).forEach((e=>{n[r][e]=vo(o[e],a[e])}))):n[r]=a:n[r]=o}else void 0===n[r]&&(n[r]=e[r])})),n}const bo=t.createContext(void 0);function wo(e){let{props:n,name:r}=e;return function(e){const{theme:t,name:n,props:r}=e;if(!t||!t.components||!t.components[n])return r;const o=t.components[n];return o.defaultProps?vo(o.defaultProps,r):o.styleOverrides||o.variants?r:vo(o,r)}({props:n,name:r,theme:{components:t.useContext(bo)}})}const ko=function(e){let{value:t,children:n}=e;return(0,zn.jsx)(bo.Provider,{value:t,children:n})},So={};function xo(e,n,r){let o=arguments.length>3&&void 0!==arguments[3]&&arguments[3];return t.useMemo((()=>{const t=e&&n[e]||n;if("function"===typeof r){const a=r(t),i=e?He({},n,{[e]:a}):a;return o?()=>i:i}return He({},n,e?{[e]:r}:r)}),[e,n,r,o])}const Eo=function(e){const{children:t,theme:n,themeId:r}=e,o=ho(So),a=In()||So,i=xo(r,o,n),s=xo(r,a,n,!0),l="rtl"===i.direction;return(0,zn.jsx)(Mn,{theme:s,children:(0,zn.jsx)(so.Provider,{value:i,children:(0,zn.jsx)(yo,{value:l,children:(0,zn.jsx)(ko,{value:null==i?void 0:i.components,children:t})})})})},Co="$$material",Po=["theme"];function Oo(e){let{theme:t}=e,n=$e(e,Po);const r=t[Co];let o=r||t;return"function"!==typeof t&&(r&&!r.vars?o=He({},r,{vars:null}):t&&!t.vars&&(o=He({},t,{vars:null}))),(0,zn.jsx)(Eo,He({},n,{themeId:r?Co:void 0,theme:o}))}var Ro=function(e,n){var r=arguments;if(null==n||!lo.call(n,"css"))return t.createElement.apply(void 0,r);var o=r.length,a=new Array(o);a[0]=po,a[1]=function(e,t){var n={};for(var r in t)lo.call(t,r)&&(n[r]=t[r]);return n[uo]=e,n}(e,n);for(var i=2;i<o;i++)a[i]=r[i];return t.createElement.apply(null,a)};!function(e){var t;t||(t=e.JSX||(e.JSX={}))}(Ro||(Ro={}));var To=io((function(e,n){var r=to([e.styles],void 0,t.useContext(so)),o=t.useRef();return oo((function(){var e=n.key+"-global",t=new n.sheet.constructor({key:e,nonce:n.sheet.nonce,container:n.sheet.container,speedy:n.sheet.isSpeedy}),a=!1,i=document.querySelector('style[data-emotion="'+e+" "+r.name+'"]');return n.sheet.tags.length&&(t.before=n.sheet.tags[0]),null!==i&&(a=!0,i.setAttribute("data-emotion",e),t.hydrate([i])),o.current=[t,a],function(){t.flush()}}),[n]),oo((function(){var e=o.current,t=e[0];if(e[1])e[1]=!1;else{if(void 0!==r.next&&Wr(n,r.next,!0),t.tags.length){var a=t.tags[t.tags.length-1].nextElementSibling;t.before=a,t.flush()}n.insert("",r,t,!1)}}),[n,r.name]),null}));function No(e){const{styles:t,defaultTheme:n={}}=e,r="function"===typeof t?e=>{return t(void 0===(r=e)||null===r||0===Object.keys(r).length?n:e);var r}:t;return(0,zn.jsx)(To,{styles:r})}const Ao=nn();const Lo=function(){return ho(arguments.length>0&&void 0!==arguments[0]?arguments[0]:Ao)};const _o=function(e){let{styles:t,themeId:n,defaultTheme:r={}}=e;const o=Lo(r),a="function"===typeof t?t(n&&o[n]||o):t;return(0,zn.jsx)(No,{styles:a})},jo=jn();const Do=function(e){return(0,zn.jsx)(_o,He({},e,{defaultTheme:jo,themeId:Co}))},Io=(e,t)=>He({WebkitFontSmoothing:"antialiased",MozOsxFontSmoothing:"grayscale",boxSizing:"border-box",WebkitTextSizeAdjust:"100%"},t&&!e.vars&&{colorScheme:e.palette.mode}),Fo=e=>He({color:(e.vars||e).palette.text.primary},e.typography.body1,{backgroundColor:(e.vars||e).palette.background.default,"@media print":{backgroundColor:(e.vars||e).palette.common.white}});const zo=function(e){const n=wo({props:e,name:"MuiCssBaseline"}),{children:r,enableColorScheme:o=!1}=n;return(0,zn.jsxs)(t.Fragment,{children:[(0,zn.jsx)(Do,{styles:e=>function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];var n;const r={};t&&e.colorSchemes&&Object.entries(e.colorSchemes).forEach((t=>{let[n,o]=t;var a;r[e.getColorSchemeSelector(n).replace(/\s*&/,"")]={colorScheme:null==(a=o.palette)?void 0:a.mode}}));let o=He({html:Io(e,t),"*, *::before, *::after":{boxSizing:"inherit"},"strong, b":{fontWeight:e.typography.fontWeightBold},body:He({margin:0},Fo(e),{"&::backdrop":{backgroundColor:(e.vars||e).palette.background.default}})},r);const a=null==(n=e.components)||null==(n=n.MuiCssBaseline)?void 0:n.styleOverrides;return a&&(o=[o,a]),o}(e,o)}),r]})};function Mo(e){return Mo="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Mo(e)}function Bo(e){var t=function(e,t){if("object"!=Mo(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=Mo(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==Mo(t)?t:t+""}function Uo(e,t,n){return(t=Bo(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Vo(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function Wo(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?Vo(Object(n),!0).forEach((function(t){Uo(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Vo(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}const Ho=e=>"string"===typeof e,$o=()=>{let e,t;const n=new Promise(((n,r)=>{e=n,t=r}));return n.resolve=e,n.reject=t,n},Ko=e=>null==e?"":""+e,qo=/###/g,Qo=e=>e&&e.indexOf("###")>-1?e.replace(qo,"."):e,Go=e=>!e||Ho(e),Jo=(e,t,n)=>{const r=Ho(t)?t.split("."):t;let o=0;for(;o<r.length-1;){if(Go(e))return{};const t=Qo(r[o]);!e[t]&&n&&(e[t]=new n),e=Object.prototype.hasOwnProperty.call(e,t)?e[t]:{},++o}return Go(e)?{}:{obj:e,k:Qo(r[o])}},Xo=(e,t,n)=>{const{obj:r,k:o}=Jo(e,t,Object);if(void 0!==r||1===t.length)return void(r[o]=n);let a=t[t.length-1],i=t.slice(0,t.length-1),s=Jo(e,i,Object);for(;void 0===s.obj&&i.length;)a="".concat(i[i.length-1],".").concat(a),i=i.slice(0,i.length-1),s=Jo(e,i,Object),s&&s.obj&&"undefined"!==typeof s.obj["".concat(s.k,".").concat(a)]&&(s.obj=void 0);s.obj["".concat(s.k,".").concat(a)]=n},Yo=(e,t)=>{const{obj:n,k:r}=Jo(e,t);if(n)return n[r]},Zo=(e,t,n)=>{for(const r in t)"__proto__"!==r&&"constructor"!==r&&(r in e?Ho(e[r])||e[r]instanceof String||Ho(t[r])||t[r]instanceof String?n&&(e[r]=t[r]):Zo(e[r],t[r],n):e[r]=t[r]);return e},ea=e=>e.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&");var ta={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};const na=e=>Ho(e)?e.replace(/[&<>"'\/]/g,(e=>ta[e])):e;const ra=[" ",",","?","!",";"],oa=new class{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){const t=this.regExpMap.get(e);if(void 0!==t)return t;const n=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,n),this.regExpQueue.push(e),n}}(20),aa=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:".";if(!e)return;if(e[t])return e[t];const r=t.split(n);let o=e;for(let a=0;a<r.length;){if(!o||"object"!==typeof o)return;let e,t="";for(let i=a;i<r.length;++i)if(i!==a&&(t+=n),t+=r[i],e=o[t],void 0!==e){if(["string","number","boolean"].indexOf(typeof e)>-1&&i<r.length-1)continue;a+=i-a+1;break}o=e}return o},ia=e=>e&&e.replace("_","-"),sa={type:"logger",log(e){this.output("log",e)},warn(e){this.output("warn",e)},error(e){this.output("error",e)},output(e,t){console&&console[e]&&console[e].apply(console,t)}};class la{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.init(e,t)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||sa,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,n,r){return r&&!this.debug?null:(Ho(e[0])&&(e[0]="".concat(n).concat(this.prefix," ").concat(e[0])),this.logger[t](e))}create(e){return new la(this.logger,Wo(Wo({},{prefix:"".concat(this.prefix,":").concat(e,":")}),this.options))}clone(e){return(e=e||this.options).prefix=e.prefix||this.prefix,new la(this.logger,e)}}var ua=new la;class ca{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach((e=>{this.observers[e]||(this.observers[e]=new Map);const n=this.observers[e].get(t)||0;this.observers[e].set(t,n+1)})),this}off(e,t){this.observers[e]&&(t?this.observers[e].delete(t):delete this.observers[e])}emit(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];if(this.observers[e]){Array.from(this.observers[e].entries()).forEach((e=>{let[t,r]=e;for(let o=0;o<r;o++)t(...n)}))}if(this.observers["*"]){Array.from(this.observers["*"].entries()).forEach((t=>{let[r,o]=t;for(let a=0;a<o;a++)r.apply(r,[e,...n])}))}}}class da extends ca{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),void 0===this.options.ignoreJSONStructure&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){const t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const o=void 0!==r.keySeparator?r.keySeparator:this.options.keySeparator,a=void 0!==r.ignoreJSONStructure?r.ignoreJSONStructure:this.options.ignoreJSONStructure;let i;e.indexOf(".")>-1?i=e.split("."):(i=[e,t],n&&(Array.isArray(n)?i.push(...n):Ho(n)&&o?i.push(...n.split(o)):i.push(n)));const s=Yo(this.data,i);return!s&&!t&&!n&&e.indexOf(".")>-1&&(e=i[0],t=i[1],n=i.slice(2).join(".")),!s&&a&&Ho(n)?aa(this.data&&this.data[e]&&this.data[e][t],n,o):s}addResource(e,t,n,r){let o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{silent:!1};const a=void 0!==o.keySeparator?o.keySeparator:this.options.keySeparator;let i=[e,t];n&&(i=i.concat(a?n.split(a):n)),e.indexOf(".")>-1&&(i=e.split("."),r=t,t=i[1]),this.addNamespaces(t),Xo(this.data,i,r),o.silent||this.emit("added",e,t,n,r)}addResources(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{silent:!1};for(const o in n)(Ho(n[o])||Array.isArray(n[o]))&&this.addResource(e,t,o,n[o],{silent:!0});r.silent||this.emit("added",e,t,n)}addResourceBundle(e,t,n,r,o){let a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{silent:!1,skipCopy:!1},i=[e,t];e.indexOf(".")>-1&&(i=e.split("."),r=n,n=t,t=i[1]),this.addNamespaces(t);let s=Yo(this.data,i)||{};a.skipCopy||(n=JSON.parse(JSON.stringify(n))),r?Zo(s,n,o):s=Wo(Wo({},s),n),Xo(this.data,i,s),a.silent||this.emit("added",e,t,n)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return void 0!==this.getResource(e,t)}getResourceBundle(e,t){return t||(t=this.options.defaultNS),"v1"===this.options.compatibilityAPI?Wo(Wo({},{}),this.getResource(e,t)):this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){const t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find((e=>t[e]&&Object.keys(t[e]).length>0))}toJSON(){return this.data}}var fa={processors:{},addPostProcessor(e){this.processors[e.name]=e},handle(e,t,n,r,o){return e.forEach((e=>{this.processors[e]&&(t=this.processors[e].process(t,n,r,o))})),t}};const pa={};class ha extends ca{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};super(),((e,t,n)=>{e.forEach((e=>{t[e]&&(n[e]=t[e])}))})(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,void 0===this.options.keySeparator&&(this.options.keySeparator="."),this.logger=ua.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};if(void 0===e||null===e)return!1;const n=this.resolve(e,t);return n&&void 0!==n.res}extractFromKey(e,t){let n=void 0!==t.nsSeparator?t.nsSeparator:this.options.nsSeparator;void 0===n&&(n=":");const r=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator;let o=t.ns||this.options.defaultNS||[];const a=n&&e.indexOf(n)>-1,i=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!((e,t,n)=>{t=t||"",n=n||"";const r=ra.filter((e=>t.indexOf(e)<0&&n.indexOf(e)<0));if(0===r.length)return!0;const o=oa.getRegExp("(".concat(r.map((e=>"?"===e?"\\?":e)).join("|"),")"));let a=!o.test(e);if(!a){const t=e.indexOf(n);t>0&&!o.test(e.substring(0,t))&&(a=!0)}return a})(e,n,r);if(a&&!i){const t=e.match(this.interpolator.nestingRegexp);if(t&&t.length>0)return{key:e,namespaces:Ho(o)?[o]:o};const a=e.split(n);(n!==r||n===r&&this.options.ns.indexOf(a[0])>-1)&&(o=a.shift()),e=a.join(r)}return{key:e,namespaces:Ho(o)?[o]:o}}translate(e,t,n){if("object"!==typeof t&&this.options.overloadTranslationOptionHandler&&(t=this.options.overloadTranslationOptionHandler(arguments)),"object"===typeof t&&(t=Wo({},t)),t||(t={}),void 0===e||null===e)return"";Array.isArray(e)||(e=[String(e)]);const r=void 0!==t.returnDetails?t.returnDetails:this.options.returnDetails,o=void 0!==t.keySeparator?t.keySeparator:this.options.keySeparator,{key:a,namespaces:i}=this.extractFromKey(e[e.length-1],t),s=i[i.length-1],l=t.lng||this.language,u=t.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(l&&"cimode"===l.toLowerCase()){if(u){const e=t.nsSeparator||this.options.nsSeparator;return r?{res:"".concat(s).concat(e).concat(a),usedKey:a,exactUsedKey:a,usedLng:l,usedNS:s,usedParams:this.getUsedParamsDetails(t)}:"".concat(s).concat(e).concat(a)}return r?{res:a,usedKey:a,exactUsedKey:a,usedLng:l,usedNS:s,usedParams:this.getUsedParamsDetails(t)}:a}const c=this.resolve(e,t);let d=c&&c.res;const f=c&&c.usedKey||a,p=c&&c.exactUsedKey||a,h=Object.prototype.toString.apply(d),g=void 0!==t.joinArrays?t.joinArrays:this.options.joinArrays,m=!this.i18nFormat||this.i18nFormat.handleAsObject,y=!Ho(d)&&"boolean"!==typeof d&&"number"!==typeof d;if(!(m&&d&&y&&["[object Number]","[object Function]","[object RegExp]"].indexOf(h)<0)||Ho(g)&&Array.isArray(d))if(m&&Ho(g)&&Array.isArray(d))d=d.join(g),d&&(d=this.extendTranslation(d,e,t,n));else{let r=!1,i=!1;const u=void 0!==t.count&&!Ho(t.count),f=ha.hasDefaultValue(t),p=u?this.pluralResolver.getSuffix(l,t.count,t):"",h=t.ordinal&&u?this.pluralResolver.getSuffix(l,t.count,{ordinal:!1}):"",g=u&&!t.ordinal&&0===t.count&&this.pluralResolver.shouldUseIntlApi(),m=g&&t["defaultValue".concat(this.options.pluralSeparator,"zero")]||t["defaultValue".concat(p)]||t["defaultValue".concat(h)]||t.defaultValue;!this.isValidLookup(d)&&f&&(r=!0,d=m),this.isValidLookup(d)||(i=!0,d=a);const y=(t.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&i?void 0:d,v=f&&m!==d&&this.options.updateMissing;if(i||r||v){if(this.logger.log(v?"updateKey":"missingKey",l,s,a,v?m:d),o){const e=this.resolve(a,Wo(Wo({},t),{},{keySeparator:!1}));e&&e.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let e=[];const n=this.languageUtils.getFallbackCodes(this.options.fallbackLng,t.lng||this.language);if("fallback"===this.options.saveMissingTo&&n&&n[0])for(let t=0;t<n.length;t++)e.push(n[t]);else"all"===this.options.saveMissingTo?e=this.languageUtils.toResolveHierarchy(t.lng||this.language):e.push(t.lng||this.language);const r=(e,n,r)=>{const o=f&&r!==d?r:y;this.options.missingKeyHandler?this.options.missingKeyHandler(e,s,n,o,v,t):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(e,s,n,o,v,t),this.emit("missingKey",e,s,n,d)};this.options.saveMissing&&(this.options.saveMissingPlurals&&u?e.forEach((e=>{const n=this.pluralResolver.getSuffixes(e,t);g&&t["defaultValue".concat(this.options.pluralSeparator,"zero")]&&n.indexOf("".concat(this.options.pluralSeparator,"zero"))<0&&n.push("".concat(this.options.pluralSeparator,"zero")),n.forEach((n=>{r([e],a+n,t["defaultValue".concat(n)]||m)}))})):r(e,a,m))}d=this.extendTranslation(d,e,t,c,n),i&&d===a&&this.options.appendNamespaceToMissingKey&&(d="".concat(s,":").concat(a)),(i||r)&&this.options.parseMissingKeyHandler&&(d="v1"!==this.options.compatibilityAPI?this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?"".concat(s,":").concat(a):a,r?d:void 0):this.options.parseMissingKeyHandler(d))}else{if(!t.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");const e=this.options.returnedObjectHandler?this.options.returnedObjectHandler(f,d,Wo(Wo({},t),{},{ns:i})):"key '".concat(a," (").concat(this.language,")' returned an object instead of string.");return r?(c.res=e,c.usedParams=this.getUsedParamsDetails(t),c):e}if(o){const e=Array.isArray(d),n=e?[]:{},r=e?p:f;for(const a in d)if(Object.prototype.hasOwnProperty.call(d,a)){const e="".concat(r).concat(o).concat(a);n[a]=this.translate(e,Wo(Wo({},t),{joinArrays:!1,ns:i})),n[a]===e&&(n[a]=d[a])}d=n}}return r?(c.res=d,c.usedParams=this.getUsedParamsDetails(t),c):d}extendTranslation(e,t,n,r,o){var a=this;if(this.i18nFormat&&this.i18nFormat.parse)e=this.i18nFormat.parse(e,Wo(Wo({},this.options.interpolation.defaultVariables),n),n.lng||this.language||r.usedLng,r.usedNS,r.usedKey,{resolved:r});else if(!n.skipInterpolation){n.interpolation&&this.interpolator.init(Wo(Wo({},n),{interpolation:Wo(Wo({},this.options.interpolation),n.interpolation)}));const i=Ho(e)&&(n&&n.interpolation&&void 0!==n.interpolation.skipOnVariables?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables);let s;if(i){const t=e.match(this.interpolator.nestingRegexp);s=t&&t.length}let l=n.replace&&!Ho(n.replace)?n.replace:n;if(this.options.interpolation.defaultVariables&&(l=Wo(Wo({},this.options.interpolation.defaultVariables),l)),e=this.interpolator.interpolate(e,l,n.lng||this.language||r.usedLng,n),i){const t=e.match(this.interpolator.nestingRegexp);s<(t&&t.length)&&(n.nest=!1)}!n.lng&&"v1"!==this.options.compatibilityAPI&&r&&r.res&&(n.lng=this.language||r.usedLng),!1!==n.nest&&(e=this.interpolator.nest(e,(function(){for(var e=arguments.length,r=new Array(e),i=0;i<e;i++)r[i]=arguments[i];return o&&o[0]===r[0]&&!n.context?(a.logger.warn("It seems you are nesting recursively key: ".concat(r[0]," in key: ").concat(t[0])),null):a.translate(...r,t)}),n)),n.interpolation&&this.interpolator.reset()}const i=n.postProcess||this.options.postProcess,s=Ho(i)?[i]:i;return void 0!==e&&null!==e&&s&&s.length&&!1!==n.applyPostProcessor&&(e=fa.handle(s,e,t,this.options&&this.options.postProcessPassResolved?Wo({i18nResolved:Wo(Wo({},r),{},{usedParams:this.getUsedParamsDetails(n)})},n):n,this)),e}resolve(e){let t,n,r,o,a,i=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return Ho(e)&&(e=[e]),e.forEach((e=>{if(this.isValidLookup(t))return;const s=this.extractFromKey(e,i),l=s.key;n=l;let u=s.namespaces;this.options.fallbackNS&&(u=u.concat(this.options.fallbackNS));const c=void 0!==i.count&&!Ho(i.count),d=c&&!i.ordinal&&0===i.count&&this.pluralResolver.shouldUseIntlApi(),f=void 0!==i.context&&(Ho(i.context)||"number"===typeof i.context)&&""!==i.context,p=i.lngs?i.lngs:this.languageUtils.toResolveHierarchy(i.lng||this.language,i.fallbackLng);u.forEach((e=>{this.isValidLookup(t)||(a=e,!pa["".concat(p[0],"-").concat(e)]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(a)&&(pa["".concat(p[0],"-").concat(e)]=!0,this.logger.warn('key "'.concat(n,'" for languages "').concat(p.join(", "),'" won\'t get resolved as namespace "').concat(a,'" was not yet loaded'),"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),p.forEach((n=>{if(this.isValidLookup(t))return;o=n;const a=[l];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(a,l,n,e,i);else{let e;c&&(e=this.pluralResolver.getSuffix(n,i.count,i));const t="".concat(this.options.pluralSeparator,"zero"),r="".concat(this.options.pluralSeparator,"ordinal").concat(this.options.pluralSeparator);if(c&&(a.push(l+e),i.ordinal&&0===e.indexOf(r)&&a.push(l+e.replace(r,this.options.pluralSeparator)),d&&a.push(l+t)),f){const n="".concat(l).concat(this.options.contextSeparator).concat(i.context);a.push(n),c&&(a.push(n+e),i.ordinal&&0===e.indexOf(r)&&a.push(n+e.replace(r,this.options.pluralSeparator)),d&&a.push(n+t))}}let s;for(;s=a.pop();)this.isValidLookup(t)||(r=s,t=this.getResource(n,e,s,i))})))}))})),{res:t,usedKey:n,exactUsedKey:r,usedLng:o,usedNS:a}}isValidLookup(e){return void 0!==e&&!(!this.options.returnNull&&null===e)&&!(!this.options.returnEmptyString&&""===e)}getResource(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(e,t,n,r):this.resourceStore.getResource(e,t,n,r)}getUsedParamsDetails(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};const t=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],n=e.replace&&!Ho(e.replace);let r=n?e.replace:e;if(n&&"undefined"!==typeof e.count&&(r.count=e.count),this.options.interpolation.defaultVariables&&(r=Wo(Wo({},this.options.interpolation.defaultVariables),r)),!n){r=Wo({},r);for(const e of t)delete r[e]}return r}static hasDefaultValue(e){const t="defaultValue";for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t===n.substring(0,12)&&void 0!==e[n])return!0;return!1}}const ga=e=>e.charAt(0).toUpperCase()+e.slice(1);class ma{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=ua.create("languageUtils")}getScriptPartFromCode(e){if(!(e=ia(e))||e.indexOf("-")<0)return null;const t=e.split("-");return 2===t.length?null:(t.pop(),"x"===t[t.length-1].toLowerCase()?null:this.formatLanguageCode(t.join("-")))}getLanguagePartFromCode(e){if(!(e=ia(e))||e.indexOf("-")<0)return e;const t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(Ho(e)&&e.indexOf("-")>-1){if("undefined"!==typeof Intl&&"undefined"!==typeof Intl.getCanonicalLocales)try{let t=Intl.getCanonicalLocales(e)[0];if(t&&this.options.lowerCaseLng&&(t=t.toLowerCase()),t)return t}catch(af){}const t=["hans","hant","latn","cyrl","cans","mong","arab"];let n=e.split("-");return this.options.lowerCaseLng?n=n.map((e=>e.toLowerCase())):2===n.length?(n[0]=n[0].toLowerCase(),n[1]=n[1].toUpperCase(),t.indexOf(n[1].toLowerCase())>-1&&(n[1]=ga(n[1].toLowerCase()))):3===n.length&&(n[0]=n[0].toLowerCase(),2===n[1].length&&(n[1]=n[1].toUpperCase()),"sgn"!==n[0]&&2===n[2].length&&(n[2]=n[2].toUpperCase()),t.indexOf(n[1].toLowerCase())>-1&&(n[1]=ga(n[1].toLowerCase())),t.indexOf(n[2].toLowerCase())>-1&&(n[2]=ga(n[2].toLowerCase()))),n.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return("languageOnly"===this.options.load||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach((e=>{if(t)return;const n=this.formatLanguageCode(e);this.options.supportedLngs&&!this.isSupportedCode(n)||(t=n)})),!t&&this.options.supportedLngs&&e.forEach((e=>{if(t)return;const n=this.getLanguagePartFromCode(e);if(this.isSupportedCode(n))return t=n;t=this.options.supportedLngs.find((e=>e===n?e:e.indexOf("-")<0&&n.indexOf("-")<0?void 0:e.indexOf("-")>0&&n.indexOf("-")<0&&e.substring(0,e.indexOf("-"))===n||0===e.indexOf(n)&&n.length>1?e:void 0))})),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(e,t){if(!e)return[];if("function"===typeof e&&(e=e(t)),Ho(e)&&(e=[e]),Array.isArray(e))return e;if(!t)return e.default||[];let n=e[t];return n||(n=e[this.getScriptPartFromCode(t)]),n||(n=e[this.formatLanguageCode(t)]),n||(n=e[this.getLanguagePartFromCode(t)]),n||(n=e.default),n||[]}toResolveHierarchy(e,t){const n=this.getFallbackCodes(t||this.options.fallbackLng||[],e),r=[],o=e=>{e&&(this.isSupportedCode(e)?r.push(e):this.logger.warn("rejecting language code not found in supportedLngs: ".concat(e)))};return Ho(e)&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?("languageOnly"!==this.options.load&&o(this.formatLanguageCode(e)),"languageOnly"!==this.options.load&&"currentOnly"!==this.options.load&&o(this.getScriptPartFromCode(e)),"currentOnly"!==this.options.load&&o(this.getLanguagePartFromCode(e))):Ho(e)&&o(this.formatLanguageCode(e)),n.forEach((e=>{r.indexOf(e)<0&&o(this.formatLanguageCode(e))})),r}}let ya=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],va={1:e=>Number(e>1),2:e=>Number(1!=e),3:e=>0,4:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),5:e=>Number(0==e?0:1==e?1:2==e?2:e%100>=3&&e%100<=10?3:e%100>=11?4:5),6:e=>Number(1==e?0:e>=2&&e<=4?1:2),7:e=>Number(1==e?0:e%10>=2&&e%10<=4&&(e%100<10||e%100>=20)?1:2),8:e=>Number(1==e?0:2==e?1:8!=e&&11!=e?2:3),9:e=>Number(e>=2),10:e=>Number(1==e?0:2==e?1:e<7?2:e<11?3:4),11:e=>Number(1==e||11==e?0:2==e||12==e?1:e>2&&e<20?2:3),12:e=>Number(e%10!=1||e%100==11),13:e=>Number(0!==e),14:e=>Number(1==e?0:2==e?1:3==e?2:3),15:e=>Number(e%10==1&&e%100!=11?0:e%10>=2&&(e%100<10||e%100>=20)?1:2),16:e=>Number(e%10==1&&e%100!=11?0:0!==e?1:2),17:e=>Number(1==e||e%10==1&&e%100!=11?0:1),18:e=>Number(0==e?0:1==e?1:2),19:e=>Number(1==e?0:0==e||e%100>1&&e%100<11?1:e%100>10&&e%100<20?2:3),20:e=>Number(1==e?0:0==e||e%100>0&&e%100<20?1:2),21:e=>Number(e%100==1?1:e%100==2?2:e%100==3||e%100==4?3:0),22:e=>Number(1==e?0:2==e?1:(e<0||e>10)&&e%10==0?2:3)};const ba=["v1","v2","v3"],wa=["v4"],ka={zero:0,one:1,two:2,few:3,many:4,other:5};class Sa{constructor(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=ua.create("pluralResolver"),this.options.compatibilityJSON&&!wa.includes(this.options.compatibilityJSON)||"undefined"!==typeof Intl&&Intl.PluralRules||(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=(()=>{const e={};return ya.forEach((t=>{t.lngs.forEach((n=>{e[n]={numbers:t.nr,plurals:va[t.fc]}}))})),e})(),this.pluralRulesCache={}}addRule(e,t){this.rules[e]=t}clearCache(){this.pluralRulesCache={}}getRule(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(this.shouldUseIntlApi()){const r=ia("dev"===e?"en":e),o=t.ordinal?"ordinal":"cardinal",a=JSON.stringify({cleanedCode:r,type:o});if(a in this.pluralRulesCache)return this.pluralRulesCache[a];let i;try{i=new Intl.PluralRules(r,{type:o})}catch(n){if(!e.match(/-|_/))return;const r=this.languageUtils.getLanguagePartFromCode(e);i=this.getRule(r,t)}return this.pluralRulesCache[a]=i,i}return this.rules[e]||this.rules[this.languageUtils.getLanguagePartFromCode(e)]}needsPlural(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=this.getRule(e,t);return this.shouldUseIntlApi()?n&&n.resolvedOptions().pluralCategories.length>1:n&&n.numbers.length>1}getPluralFormsOfKey(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return this.getSuffixes(e,n).map((e=>"".concat(t).concat(e)))}getSuffixes(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=this.getRule(e,t);return n?this.shouldUseIntlApi()?n.resolvedOptions().pluralCategories.sort(((e,t)=>ka[e]-ka[t])).map((e=>"".concat(this.options.prepend).concat(t.ordinal?"ordinal".concat(this.options.prepend):"").concat(e))):n.numbers.map((n=>this.getSuffix(e,n,t))):[]}getSuffix(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const r=this.getRule(e,n);return r?this.shouldUseIntlApi()?"".concat(this.options.prepend).concat(n.ordinal?"ordinal".concat(this.options.prepend):"").concat(r.select(t)):this.getSuffixRetroCompatible(r,t):(this.logger.warn("no plural rule found for: ".concat(e)),"")}getSuffixRetroCompatible(e,t){const n=e.noAbs?e.plurals(t):e.plurals(Math.abs(t));let r=e.numbers[n];this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]&&(2===r?r="plural":1===r&&(r=""));const o=()=>this.options.prepend&&r.toString()?this.options.prepend+r.toString():r.toString();return"v1"===this.options.compatibilityJSON?1===r?"":"number"===typeof r?"_plural_".concat(r.toString()):o():"v2"===this.options.compatibilityJSON||this.options.simplifyPluralSuffix&&2===e.numbers.length&&1===e.numbers[0]?o():this.options.prepend&&n.toString()?this.options.prepend+n.toString():n.toString()}shouldUseIntlApi(){return!ba.includes(this.options.compatibilityJSON)}}const xa=function(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:".",o=!(arguments.length>4&&void 0!==arguments[4])||arguments[4],a=((e,t,n)=>{const r=Yo(e,n);return void 0!==r?r:Yo(t,n)})(e,t,n);return!a&&o&&Ho(n)&&(a=aa(e,n,r),void 0===a&&(a=aa(t,n,r))),a},Ea=e=>e.replace(/\$/g,"$$$$");class Ca{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=ua.create("interpolator"),this.options=e,this.format=e.interpolation&&e.interpolation.format||(e=>e),this.init(e)}init(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});const{escape:t,escapeValue:n,useRawValueToEscape:r,prefix:o,prefixEscaped:a,suffix:i,suffixEscaped:s,formatSeparator:l,unescapeSuffix:u,unescapePrefix:c,nestingPrefix:d,nestingPrefixEscaped:f,nestingSuffix:p,nestingSuffixEscaped:h,nestingOptionsSeparator:g,maxReplaces:m,alwaysFormat:y}=e.interpolation;this.escape=void 0!==t?t:na,this.escapeValue=void 0===n||n,this.useRawValueToEscape=void 0!==r&&r,this.prefix=o?ea(o):a||"{{",this.suffix=i?ea(i):s||"}}",this.formatSeparator=l||",",this.unescapePrefix=u?"":c||"-",this.unescapeSuffix=this.unescapePrefix?"":u||"",this.nestingPrefix=d?ea(d):f||ea("$t("),this.nestingSuffix=p?ea(p):h||ea(")"),this.nestingOptionsSeparator=g||",",this.maxReplaces=m||1e3,this.alwaysFormat=void 0!==y&&y,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){const e=(e,t)=>e&&e.source===t?(e.lastIndex=0,e):new RegExp(t,"g");this.regexp=e(this.regexp,"".concat(this.prefix,"(.+?)").concat(this.suffix)),this.regexpUnescape=e(this.regexpUnescape,"".concat(this.prefix).concat(this.unescapePrefix,"(.+?)").concat(this.unescapeSuffix).concat(this.suffix)),this.nestingRegexp=e(this.nestingRegexp,"".concat(this.nestingPrefix,"(.+?)").concat(this.nestingSuffix))}interpolate(e,t,n,r){let o,a,i;const s=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{},l=e=>{if(e.indexOf(this.formatSeparator)<0){const o=xa(t,s,e,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(o,void 0,n,Wo(Wo(Wo({},r),t),{},{interpolationkey:e})):o}const o=e.split(this.formatSeparator),a=o.shift().trim(),i=o.join(this.formatSeparator).trim();return this.format(xa(t,s,a,this.options.keySeparator,this.options.ignoreJSONStructure),i,n,Wo(Wo(Wo({},r),t),{},{interpolationkey:a}))};this.resetRegExp();const u=r&&r.missingInterpolationHandler||this.options.missingInterpolationHandler,c=r&&r.interpolation&&void 0!==r.interpolation.skipOnVariables?r.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:e=>Ea(e)},{regex:this.regexp,safeValue:e=>this.escapeValue?Ea(this.escape(e)):Ea(e)}].forEach((t=>{for(i=0;o=t.regex.exec(e);){const n=o[1].trim();if(a=l(n),void 0===a)if("function"===typeof u){const t=u(e,o,r);a=Ho(t)?t:""}else if(r&&Object.prototype.hasOwnProperty.call(r,n))a="";else{if(c){a=o[0];continue}this.logger.warn("missed to pass in variable ".concat(n," for interpolating ").concat(e)),a=""}else Ho(a)||this.useRawValueToEscape||(a=Ko(a));const s=t.safeValue(a);if(e=e.replace(o[0],s),c?(t.regex.lastIndex+=a.length,t.regex.lastIndex-=o[0].length):t.regex.lastIndex=0,i++,i>=this.maxReplaces)break}})),e}nest(e,t){let n,r,o,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};const i=(e,t)=>{const n=this.nestingOptionsSeparator;if(e.indexOf(n)<0)return e;const r=e.split(new RegExp("".concat(n,"[ ]*{")));let a="{".concat(r[1]);e=r[0],a=this.interpolate(a,o);const i=a.match(/'/g),s=a.match(/"/g);(i&&i.length%2===0&&!s||s.length%2!==0)&&(a=a.replace(/'/g,'"'));try{o=JSON.parse(a),t&&(o=Wo(Wo({},t),o))}catch(af){return this.logger.warn("failed parsing options string in nesting for key ".concat(e),af),"".concat(e).concat(n).concat(a)}return o.defaultValue&&o.defaultValue.indexOf(this.prefix)>-1&&delete o.defaultValue,e};for(;n=this.nestingRegexp.exec(e);){let s=[];o=Wo({},a),o=o.replace&&!Ho(o.replace)?o.replace:o,o.applyPostProcessor=!1,delete o.defaultValue;let l=!1;if(-1!==n[0].indexOf(this.formatSeparator)&&!/{.*}/.test(n[1])){const e=n[1].split(this.formatSeparator).map((e=>e.trim()));n[1]=e.shift(),s=e,l=!0}if(r=t(i.call(this,n[1].trim(),o),o),r&&n[0]===e&&!Ho(r))return r;Ho(r)||(r=Ko(r)),r||(this.logger.warn("missed to resolve ".concat(n[1]," for nesting ").concat(e)),r=""),l&&(r=s.reduce(((e,t)=>this.format(e,t,a.lng,Wo(Wo({},a),{},{interpolationkey:n[1].trim()}))),r.trim())),e=e.replace(n[0],r),this.regexp.lastIndex=0}return e}}const Pa=e=>{const t={};return(n,r,o)=>{let a=o;o&&o.interpolationkey&&o.formatParams&&o.formatParams[o.interpolationkey]&&o[o.interpolationkey]&&(a=Wo(Wo({},a),{},{[o.interpolationkey]:void 0}));const i=r+JSON.stringify(a);let s=t[i];return s||(s=e(ia(r),o),t[i]=s),s(n)}};class Oa{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};this.logger=ua.create("formatter"),this.options=e,this.formats={number:Pa(((e,t)=>{const n=new Intl.NumberFormat(e,Wo({},t));return e=>n.format(e)})),currency:Pa(((e,t)=>{const n=new Intl.NumberFormat(e,Wo(Wo({},t),{},{style:"currency"}));return e=>n.format(e)})),datetime:Pa(((e,t)=>{const n=new Intl.DateTimeFormat(e,Wo({},t));return e=>n.format(e)})),relativetime:Pa(((e,t)=>{const n=new Intl.RelativeTimeFormat(e,Wo({},t));return e=>n.format(e,t.range||"day")})),list:Pa(((e,t)=>{const n=new Intl.ListFormat(e,Wo({},t));return e=>n.format(e)}))},this.init(e)}init(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{interpolation:{}};this.formatSeparator=t.interpolation.formatSeparator||","}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=Pa(t)}format(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};const o=t.split(this.formatSeparator);if(o.length>1&&o[0].indexOf("(")>1&&o[0].indexOf(")")<0&&o.find((e=>e.indexOf(")")>-1))){const e=o.findIndex((e=>e.indexOf(")")>-1));o[0]=[o[0],...o.splice(1,e)].join(this.formatSeparator)}const a=o.reduce(((e,t)=>{const{formatName:o,formatOptions:a}=(e=>{let t=e.toLowerCase().trim();const n={};if(e.indexOf("(")>-1){const r=e.split("(");t=r[0].toLowerCase().trim();const o=r[1].substring(0,r[1].length-1);"currency"===t&&o.indexOf(":")<0?n.currency||(n.currency=o.trim()):"relativetime"===t&&o.indexOf(":")<0?n.range||(n.range=o.trim()):o.split(";").forEach((e=>{if(e){const[t,...r]=e.split(":"),o=r.join(":").trim().replace(/^'+|'+$/g,""),a=t.trim();n[a]||(n[a]=o),"false"===o&&(n[a]=!1),"true"===o&&(n[a]=!0),isNaN(o)||(n[a]=parseInt(o,10))}}))}return{formatName:t,formatOptions:n}})(t);if(this.formats[o]){let t=e;try{const i=r&&r.formatParams&&r.formatParams[r.interpolationkey]||{},s=i.locale||i.lng||r.locale||r.lng||n;t=this.formats[o](e,s,Wo(Wo(Wo({},a),r),i))}catch(i){this.logger.warn(i)}return t}return this.logger.warn("there was no format function for ".concat(o)),e}),e);return a}}class Ra extends ca{constructor(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=n,this.languageUtils=n.languageUtils,this.options=r,this.logger=ua.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=r.maxParallelReads||10,this.readingCalls=0,this.maxRetries=r.maxRetries>=0?r.maxRetries:5,this.retryTimeout=r.retryTimeout>=1?r.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(n,r.backend,r)}queueLoad(e,t,n,r){const o={},a={},i={},s={};return e.forEach((e=>{let r=!0;t.forEach((t=>{const i="".concat(e,"|").concat(t);!n.reload&&this.store.hasResourceBundle(e,t)?this.state[i]=2:this.state[i]<0||(1===this.state[i]?void 0===a[i]&&(a[i]=!0):(this.state[i]=1,r=!1,void 0===a[i]&&(a[i]=!0),void 0===o[i]&&(o[i]=!0),void 0===s[t]&&(s[t]=!0)))})),r||(i[e]=!0)})),(Object.keys(o).length||Object.keys(a).length)&&this.queue.push({pending:a,pendingCount:Object.keys(a).length,loaded:{},errors:[],callback:r}),{toLoad:Object.keys(o),pending:Object.keys(a),toLoadLanguages:Object.keys(i),toLoadNamespaces:Object.keys(s)}}loaded(e,t,n){const r=e.split("|"),o=r[0],a=r[1];t&&this.emit("failedLoading",o,a,t),!t&&n&&this.store.addResourceBundle(o,a,n,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2,t&&n&&(this.state[e]=0);const i={};this.queue.forEach((n=>{((e,t,n)=>{const{obj:r,k:o}=Jo(e,t,Object);r[o]=r[o]||[],r[o].push(n)})(n.loaded,[o],a),((e,t)=>{void 0!==e.pending[t]&&(delete e.pending[t],e.pendingCount--)})(n,e),t&&n.errors.push(t),0!==n.pendingCount||n.done||(Object.keys(n.loaded).forEach((e=>{i[e]||(i[e]={});const t=n.loaded[e];t.length&&t.forEach((t=>{void 0===i[e][t]&&(i[e][t]=!0)}))})),n.done=!0,n.errors.length?n.callback(n.errors):n.callback())})),this.emit("loaded",i),this.queue=this.queue.filter((e=>!e.done))}read(e,t,n){let r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:0,o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:this.retryTimeout,a=arguments.length>5?arguments[5]:void 0;if(!e.length)return a(null,{});if(this.readingCalls>=this.maxParallelReads)return void this.waitingReads.push({lng:e,ns:t,fcName:n,tried:r,wait:o,callback:a});this.readingCalls++;const i=(i,s)=>{if(this.readingCalls--,this.waitingReads.length>0){const e=this.waitingReads.shift();this.read(e.lng,e.ns,e.fcName,e.tried,e.wait,e.callback)}i&&s&&r<this.maxRetries?setTimeout((()=>{this.read.call(this,e,t,n,r+1,2*o,a)}),o):a(i,s)},s=this.backend[n].bind(this.backend);if(2!==s.length)return s(e,t,i);try{const n=s(e,t);n&&"function"===typeof n.then?n.then((e=>i(null,e))).catch(i):i(null,n)}catch(l){i(l)}}prepareLoading(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),r&&r();Ho(e)&&(e=this.languageUtils.toResolveHierarchy(e)),Ho(t)&&(t=[t]);const o=this.queueLoad(e,t,n,r);if(!o.toLoad.length)return o.pending.length||r(),null;o.toLoad.forEach((e=>{this.loadOne(e)}))}load(e,t,n){this.prepareLoading(e,t,{},n)}reload(e,t,n){this.prepareLoading(e,t,{reload:!0},n)}loadOne(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";const n=e.split("|"),r=n[0],o=n[1];this.read(r,o,"read",void 0,void 0,((n,a)=>{n&&this.logger.warn("".concat(t,"loading namespace ").concat(o," for language ").concat(r," failed"),n),!n&&a&&this.logger.log("".concat(t,"loaded namespace ").concat(o," for language ").concat(r),a),this.loaded(e,n,a)}))}saveMissing(e,t,n,r,o){let a=arguments.length>5&&void 0!==arguments[5]?arguments[5]:{},i=arguments.length>6&&void 0!==arguments[6]?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(t))this.logger.warn('did not save key "'.concat(n,'" as the namespace "').concat(t,'" was not yet loaded'),"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");else if(void 0!==n&&null!==n&&""!==n){if(this.backend&&this.backend.create){const l=Wo(Wo({},a),{},{isUpdate:o}),u=this.backend.create.bind(this.backend);if(u.length<6)try{let o;o=5===u.length?u(e,t,n,r,l):u(e,t,n,r),o&&"function"===typeof o.then?o.then((e=>i(null,e))).catch(i):i(null,o)}catch(s){i(s)}else u(e,t,n,r,i,l)}e&&e[0]&&this.store.addResource(e[0],t,n,r)}}}const Ta=()=>({debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:e=>{let t={};if("object"===typeof e[1]&&(t=e[1]),Ho(e[1])&&(t.defaultValue=e[1]),Ho(e[2])&&(t.tDescription=e[2]),"object"===typeof e[2]||"object"===typeof e[3]){const n=e[3]||e[2];Object.keys(n).forEach((e=>{t[e]=n[e]}))}return t},interpolation:{escapeValue:!0,format:e=>e,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}),Na=e=>(Ho(e.ns)&&(e.ns=[e.ns]),Ho(e.fallbackLng)&&(e.fallbackLng=[e.fallbackLng]),Ho(e.fallbackNS)&&(e.fallbackNS=[e.fallbackNS]),e.supportedLngs&&e.supportedLngs.indexOf("cimode")<0&&(e.supportedLngs=e.supportedLngs.concat(["cimode"])),e),Aa=()=>{};class La extends ca{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;var n;if(super(),this.options=Na(e),this.services={},this.logger=ua,this.modules={external:[]},n=this,Object.getOwnPropertyNames(Object.getPrototypeOf(n)).forEach((e=>{"function"===typeof n[e]&&(n[e]=n[e].bind(n))})),t&&!this.isInitialized&&!e.isClone){if(!this.options.initImmediate)return this.init(e,t),this;setTimeout((()=>{this.init(e,t)}),0)}}init(){var e=this;let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,"function"===typeof t&&(n=t,t={}),!t.defaultNS&&!1!==t.defaultNS&&t.ns&&(Ho(t.ns)?t.defaultNS=t.ns:t.ns.indexOf("translation")<0&&(t.defaultNS=t.ns[0]));const r=Ta();this.options=Wo(Wo(Wo({},r),this.options),Na(t)),"v1"!==this.options.compatibilityAPI&&(this.options.interpolation=Wo(Wo({},r.interpolation),this.options.interpolation)),void 0!==t.keySeparator&&(this.options.userDefinedKeySeparator=t.keySeparator),void 0!==t.nsSeparator&&(this.options.userDefinedNsSeparator=t.nsSeparator);const o=e=>e?"function"===typeof e?new e:e:null;if(!this.options.isClone){let t;this.modules.logger?ua.init(o(this.modules.logger),this.options):ua.init(null,this.options),this.modules.formatter?t=this.modules.formatter:"undefined"!==typeof Intl&&(t=Oa);const n=new ma(this.options);this.store=new da(this.options.resources,this.options);const a=this.services;a.logger=ua,a.resourceStore=this.store,a.languageUtils=n,a.pluralResolver=new Sa(n,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),!t||this.options.interpolation.format&&this.options.interpolation.format!==r.interpolation.format||(a.formatter=o(t),a.formatter.init(a,this.options),this.options.interpolation.format=a.formatter.format.bind(a.formatter)),a.interpolator=new Ca(this.options),a.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},a.backendConnector=new Ra(o(this.modules.backend),a.resourceStore,a,this.options),a.backendConnector.on("*",(function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];e.emit(t,...r)})),this.modules.languageDetector&&(a.languageDetector=o(this.modules.languageDetector),a.languageDetector.init&&a.languageDetector.init(a,this.options.detection,this.options)),this.modules.i18nFormat&&(a.i18nFormat=o(this.modules.i18nFormat),a.i18nFormat.init&&a.i18nFormat.init(this)),this.translator=new ha(this.services,this.options),this.translator.on("*",(function(t){for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];e.emit(t,...r)})),this.modules.external.forEach((e=>{e.init&&e.init(this)}))}if(this.format=this.options.interpolation.format,n||(n=Aa),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){const e=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);e.length>0&&"dev"!==e[0]&&(this.options.lng=e[0])}this.services.languageDetector||this.options.lng||this.logger.warn("init: no languageDetector is used and no lng is defined");["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach((t=>{this[t]=function(){return e.store[t](...arguments)}}));["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach((t=>{this[t]=function(){return e.store[t](...arguments),e}}));const a=$o(),i=()=>{const e=(e,t)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),a.resolve(t),n(e,t)};if(this.languages&&"v1"!==this.options.compatibilityAPI&&!this.isInitialized)return e(null,this.t.bind(this));this.changeLanguage(this.options.lng,e)};return this.options.resources||!this.options.initImmediate?i():setTimeout(i,0),a}loadResources(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Aa;const n=Ho(e)?e:this.language;if("function"===typeof e&&(t=e),!this.options.resources||this.options.partialBundledLanguages){if(n&&"cimode"===n.toLowerCase()&&(!this.options.preload||0===this.options.preload.length))return t();const e=[],r=t=>{if(!t)return;if("cimode"===t)return;this.services.languageUtils.toResolveHierarchy(t).forEach((t=>{"cimode"!==t&&e.indexOf(t)<0&&e.push(t)}))};if(n)r(n);else{this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach((e=>r(e)))}this.options.preload&&this.options.preload.forEach((e=>r(e))),this.services.backendConnector.load(e,this.options.ns,(e=>{e||this.resolvedLanguage||!this.language||this.setResolvedLanguage(this.language),t(e)}))}else t(null)}reloadResources(e,t,n){const r=$o();return"function"===typeof e&&(n=e,e=void 0),"function"===typeof t&&(n=t,t=void 0),e||(e=this.languages),t||(t=this.options.ns),n||(n=Aa),this.services.backendConnector.reload(e,t,(e=>{r.resolve(),n(e)})),r}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return"backend"===e.type&&(this.modules.backend=e),("logger"===e.type||e.log&&e.warn&&e.error)&&(this.modules.logger=e),"languageDetector"===e.type&&(this.modules.languageDetector=e),"i18nFormat"===e.type&&(this.modules.i18nFormat=e),"postProcessor"===e.type&&fa.addPostProcessor(e),"formatter"===e.type&&(this.modules.formatter=e),"3rdParty"===e.type&&this.modules.external.push(e),this}setResolvedLanguage(e){if(e&&this.languages&&!(["cimode","dev"].indexOf(e)>-1))for(let t=0;t<this.languages.length;t++){const e=this.languages[t];if(!(["cimode","dev"].indexOf(e)>-1)&&this.store.hasLanguageSomeTranslations(e)){this.resolvedLanguage=e;break}}}changeLanguage(e,t){var n=this;this.isLanguageChangingTo=e;const r=$o();this.emit("languageChanging",e);const o=e=>{this.language=e,this.languages=this.services.languageUtils.toResolveHierarchy(e),this.resolvedLanguage=void 0,this.setResolvedLanguage(e)},a=(e,a)=>{a?(o(a),this.translator.changeLanguage(a),this.isLanguageChangingTo=void 0,this.emit("languageChanged",a),this.logger.log("languageChanged",a)):this.isLanguageChangingTo=void 0,r.resolve((function(){return n.t(...arguments)})),t&&t(e,(function(){return n.t(...arguments)}))},i=t=>{e||t||!this.services.languageDetector||(t=[]);const n=Ho(t)?t:this.services.languageUtils.getBestMatchFromCodes(t);n&&(this.language||o(n),this.translator.language||this.translator.changeLanguage(n),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(n)),this.loadResources(n,(e=>{a(e,n)}))};return e||!this.services.languageDetector||this.services.languageDetector.async?!e&&this.services.languageDetector&&this.services.languageDetector.async?0===this.services.languageDetector.detect.length?this.services.languageDetector.detect().then(i):this.services.languageDetector.detect(i):i(e):i(this.services.languageDetector.detect()),r}getFixedT(e,t,n){var r=this;const o=function(e,t){let a;if("object"!==typeof t){for(var i=arguments.length,s=new Array(i>2?i-2:0),l=2;l<i;l++)s[l-2]=arguments[l];a=r.options.overloadTranslationOptionHandler([e,t].concat(s))}else a=Wo({},t);a.lng=a.lng||o.lng,a.lngs=a.lngs||o.lngs,a.ns=a.ns||o.ns,""!==a.keyPrefix&&(a.keyPrefix=a.keyPrefix||n||o.keyPrefix);const u=r.options.keySeparator||".";let c;return c=a.keyPrefix&&Array.isArray(e)?e.map((e=>"".concat(a.keyPrefix).concat(u).concat(e))):a.keyPrefix?"".concat(a.keyPrefix).concat(u).concat(e):e,r.t(c,a)};return Ho(e)?o.lng=e:o.lngs=e,o.ns=t,o.keyPrefix=n,o}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;const n=t.lng||this.resolvedLanguage||this.languages[0],r=!!this.options&&this.options.fallbackLng,o=this.languages[this.languages.length-1];if("cimode"===n.toLowerCase())return!0;const a=(e,t)=>{const n=this.services.backendConnector.state["".concat(e,"|").concat(t)];return-1===n||0===n||2===n};if(t.precheck){const e=t.precheck(this,a);if(void 0!==e)return e}return!!this.hasResourceBundle(n,e)||(!(this.services.backendConnector.backend&&(!this.options.resources||this.options.partialBundledLanguages))||!(!a(n,e)||r&&!a(o,e)))}loadNamespaces(e,t){const n=$o();return this.options.ns?(Ho(e)&&(e=[e]),e.forEach((e=>{this.options.ns.indexOf(e)<0&&this.options.ns.push(e)})),this.loadResources((e=>{n.resolve(),t&&t(e)})),n):(t&&t(),Promise.resolve())}loadLanguages(e,t){const n=$o();Ho(e)&&(e=[e]);const r=this.options.preload||[],o=e.filter((e=>r.indexOf(e)<0&&this.services.languageUtils.isSupportedCode(e)));return o.length?(this.options.preload=r.concat(o),this.loadResources((e=>{n.resolve(),t&&t(e)})),n):(t&&t(),Promise.resolve())}dir(e){if(e||(e=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),!e)return"rtl";const t=this.services&&this.services.languageUtils||new ma(Ta());return["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"].indexOf(t.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){return new La(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},arguments.length>1?arguments[1]:void 0)}cloneInstance(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Aa;const n=e.forkResourceStore;n&&delete e.forkResourceStore;const r=Wo(Wo(Wo({},this.options),e),{isClone:!0}),o=new La(r);void 0===e.debug&&void 0===e.prefix||(o.logger=o.logger.clone(e));return["store","services","language"].forEach((e=>{o[e]=this[e]})),o.services=Wo({},this.services),o.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},n&&(o.store=new da(this.store.data,r),o.services.resourceStore=o.store),o.translator=new ha(o.services,r),o.translator.on("*",(function(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];o.emit(e,...n)})),o.init(r,t),o.translator.options=r,o.translator.backendConnector.services.utils={hasLoadedNamespace:o.hasLoadedNamespace.bind(o)},o}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}}const _a=La.createInstance();_a.createInstance=La.createInstance;_a.createInstance,_a.dir,_a.init,_a.loadResources,_a.reloadResources,_a.use,_a.changeLanguage,_a.getFixedT,_a.t,_a.exists,_a.setDefaultNamespace,_a.hasLoadedNamespace,_a.loadNamespaces,_a.loadLanguages;n(844);Object.create(null);const ja=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,Da={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"\xa9","&#169;":"\xa9","&reg;":"\xae","&#174;":"\xae","&hellip;":"\u2026","&#8230;":"\u2026","&#x2F;":"/","&#47;":"/"},Ia=e=>Da[e];let Fa={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:e=>e.replace(ja,Ia)};let za;const Ma={type:"3rdParty",init(e){!function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Fa=Wo(Wo({},Fa),e)}(e.options.react),function(e){za=e}(e)}};(0,t.createContext)();function Ba(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,Bo(r.key),r)}}var Ua=[],Va=Ua.forEach,Wa=Ua.slice;var Ha=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,$a=function(e,t,n,r){var o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{path:"/",sameSite:"strict"};n&&(o.expires=new Date,o.expires.setTime(o.expires.getTime()+60*n*1e3)),r&&(o.domain=r),document.cookie=function(e,t,n){var r=n||{};r.path=r.path||"/";var o=encodeURIComponent(t),a="".concat(e,"=").concat(o);if(r.maxAge>0){var i=r.maxAge-0;if(Number.isNaN(i))throw new Error("maxAge should be a Number");a+="; Max-Age=".concat(Math.floor(i))}if(r.domain){if(!Ha.test(r.domain))throw new TypeError("option domain is invalid");a+="; Domain=".concat(r.domain)}if(r.path){if(!Ha.test(r.path))throw new TypeError("option path is invalid");a+="; Path=".concat(r.path)}if(r.expires){if("function"!==typeof r.expires.toUTCString)throw new TypeError("option expires is invalid");a+="; Expires=".concat(r.expires.toUTCString())}if(r.httpOnly&&(a+="; HttpOnly"),r.secure&&(a+="; Secure"),r.sameSite)switch("string"===typeof r.sameSite?r.sameSite.toLowerCase():r.sameSite){case!0:a+="; SameSite=Strict";break;case"lax":a+="; SameSite=Lax";break;case"strict":a+="; SameSite=Strict";break;case"none":a+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}return a}(e,encodeURIComponent(t),o)},Ka=function(e){for(var t="".concat(e,"="),n=document.cookie.split(";"),r=0;r<n.length;r++){for(var o=n[r];" "===o.charAt(0);)o=o.substring(1,o.length);if(0===o.indexOf(t))return o.substring(t.length,o.length)}return null},qa={name:"cookie",lookup:function(e){var t;if(e.lookupCookie&&"undefined"!==typeof document){var n=Ka(e.lookupCookie);n&&(t=n)}return t},cacheUserLanguage:function(e,t){t.lookupCookie&&"undefined"!==typeof document&&$a(t.lookupCookie,e,t.cookieMinutes,t.cookieDomain,t.cookieOptions)}},Qa={name:"querystring",lookup:function(e){var t;if("undefined"!==typeof window){var n=window.location.search;!window.location.search&&window.location.hash&&window.location.hash.indexOf("?")>-1&&(n=window.location.hash.substring(window.location.hash.indexOf("?")));for(var r=n.substring(1).split("&"),o=0;o<r.length;o++){var a=r[o].indexOf("=");if(a>0)r[o].substring(0,a)===e.lookupQuerystring&&(t=r[o].substring(a+1))}}return t}},Ga=null,Ja=function(){if(null!==Ga)return Ga;try{Ga="undefined"!==window&&null!==window.localStorage;var e="i18next.translate.boo";window.localStorage.setItem(e,"foo"),window.localStorage.removeItem(e)}catch(af){Ga=!1}return Ga},Xa={name:"localStorage",lookup:function(e){var t;if(e.lookupLocalStorage&&Ja()){var n=window.localStorage.getItem(e.lookupLocalStorage);n&&(t=n)}return t},cacheUserLanguage:function(e,t){t.lookupLocalStorage&&Ja()&&window.localStorage.setItem(t.lookupLocalStorage,e)}},Ya=null,Za=function(){if(null!==Ya)return Ya;try{Ya="undefined"!==window&&null!==window.sessionStorage;var e="i18next.translate.boo";window.sessionStorage.setItem(e,"foo"),window.sessionStorage.removeItem(e)}catch(af){Ya=!1}return Ya},ei={name:"sessionStorage",lookup:function(e){var t;if(e.lookupSessionStorage&&Za()){var n=window.sessionStorage.getItem(e.lookupSessionStorage);n&&(t=n)}return t},cacheUserLanguage:function(e,t){t.lookupSessionStorage&&Za()&&window.sessionStorage.setItem(t.lookupSessionStorage,e)}},ti={name:"navigator",lookup:function(e){var t=[];if("undefined"!==typeof navigator){if(navigator.languages)for(var n=0;n<navigator.languages.length;n++)t.push(navigator.languages[n]);navigator.userLanguage&&t.push(navigator.userLanguage),navigator.language&&t.push(navigator.language)}return t.length>0?t:void 0}},ni={name:"htmlTag",lookup:function(e){var t,n=e.htmlTag||("undefined"!==typeof document?document.documentElement:null);return n&&"function"===typeof n.getAttribute&&(t=n.getAttribute("lang")),t}},ri={name:"path",lookup:function(e){var t;if("undefined"!==typeof window){var n=window.location.pathname.match(/\/([a-zA-Z-]*)/g);if(n instanceof Array)if("number"===typeof e.lookupFromPathIndex){if("string"!==typeof n[e.lookupFromPathIndex])return;t=n[e.lookupFromPathIndex].replace("/","")}else t=n[0].replace("/","")}return t}},oi={name:"subdomain",lookup:function(e){var t="number"===typeof e.lookupFromSubdomainIndex?e.lookupFromSubdomainIndex+1:1,n="undefined"!==typeof window&&window.location&&window.location.hostname&&window.location.hostname.match(/^(\w{2,5})\.(([a-z0-9-]{1,63}\.[a-z]{2,6})|localhost)/i);if(n)return n[t]}},ai=!1;try{document.cookie,ai=!0}catch(af){}var ii=["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"];ai||ii.splice(1,1);var si=function(){return function(e,t,n){return t&&Ba(e.prototype,t),n&&Ba(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}((function e(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,e),this.type="languageDetector",this.detectors={},this.init(t,n)}),[{key:"init",value:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};this.services=e||{languageUtils:{}},this.options=function(e){return Va.call(Wa.call(arguments,1),(function(t){if(t)for(var n in t)void 0===e[n]&&(e[n]=t[n])})),e}(t,this.options||{},{order:ii,lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"],convertDetectedLanguage:function(e){return e}}),"string"===typeof this.options.convertDetectedLanguage&&this.options.convertDetectedLanguage.indexOf("15897")>-1&&(this.options.convertDetectedLanguage=function(e){return e.replace("-","_")}),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=n,this.addDetector(qa),this.addDetector(Qa),this.addDetector(Xa),this.addDetector(ei),this.addDetector(ti),this.addDetector(ni),this.addDetector(ri),this.addDetector(oi)}},{key:"addDetector",value:function(e){return this.detectors[e.name]=e,this}},{key:"detect",value:function(e){var t=this;e||(e=this.options.order);var n=[];return e.forEach((function(e){if(t.detectors[e]){var r=t.detectors[e].lookup(t.options);r&&"string"===typeof r&&(r=[r]),r&&(n=n.concat(r))}})),n=n.map((function(e){return t.options.convertDetectedLanguage(e)})),this.services.languageUtils.getBestMatchFromCodes?n:n.length>0?n[0]:null}},{key:"cacheUserLanguage",value:function(e,t){var n=this;t||(t=this.options.caches),t&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(e)>-1||t.forEach((function(t){n.detectors[t]&&n.detectors[t].cacheUserLanguage(e,n.options)})))}}])}();si.type="languageDetector";const li={en:{translation:JSON.parse('{"common":{"loading":"Loading...","save":"Save","cancel":"Cancel","delete":"Delete","edit":"Edit","add":"Add","search":"Search","filter":"Filter","export":"Export","import":"Import","refresh":"Refresh","back":"Back","next":"Next","previous":"Previous","submit":"Submit","reset":"Reset","clear":"Clear","close":"Close","open":"Open","view":"View","download":"Download","upload":"Upload","print":"Print","share":"Share","copy":"Copy","cut":"Cut","paste":"Paste","select":"Select","selectAll":"Select All","none":"None","all":"All","yes":"Yes","no":"No","ok":"OK","confirm":"Confirm","warning":"Warning","error":"Error","success":"Success","info":"Information","required":"Required","optional":"Optional","name":"Name","description":"Description","date":"Date","time":"Time","status":"Status","type":"Type","category":"Category","notes":"Notes","actions":"Actions"},"auth":{"login":"Login","logout":"Logout","register":"Register","username":"Username","email":"Email","password":"Password","confirmPassword":"Confirm Password","firstName":"First Name","lastName":"Last Name","phoneNumber":"Phone Number","department":"Department","role":"Role","forgotPassword":"Forgot Password?","resetPassword":"Reset Password","changePassword":"Change Password","currentPassword":"Current Password","newPassword":"New Password","loginSuccess":"Login successful","loginError":"Login failed","logoutSuccess":"Logout successful","registerSuccess":"Registration successful","registerError":"Registration failed","passwordChangeSuccess":"Password changed successfully","passwordChangeError":"Password change failed","invalidCredentials":"Invalid username or password","accountDeactivated":"Account is deactivated","sessionExpired":"Session expired. Please login again.","welcomeBack":"Welcome back","signInToContinue":"Sign in to continue to AMPD Livestock Management","dontHaveAccount":"Don\'t have an account?","alreadyHaveAccount":"Already have an account?","createAccount":"Create Account","signIn":"Sign In"},"navigation":{"dashboard":"Dashboard","animals":"Animals","breeding":"Breeding","health":"Health","feeding":"Feeding","financial":"Financial","compliance":"Compliance","inventory":"Inventory","analytics":"Analytics","reports":"Reports","settings":"Settings","profile":"Profile","users":"Users","help":"Help","support":"Support"},"dashboard":{"title":"Dashboard","welcome":"Welcome to AMPD Livestock Management","overview":"Overview","quickStats":"Quick Statistics","recentActivity":"Recent Activity","alerts":"Alerts","upcomingTasks":"Upcoming Tasks","totalAnimals":"Total Animals","healthyAnimals":"Healthy Animals","pregnantAnimals":"Pregnant Animals","upcomingBirths":"Upcoming Births","vaccinationsDue":"Vaccinations Due","healthAlerts":"Health Alerts","feedingAlerts":"Feeding Alerts","breedingAlerts":"Breeding Alerts"},"animals":{"title":"Animal Management","addAnimal":"Add Animal","editAnimal":"Edit Animal","animalDetails":"Animal Details","animalList":"Animal List","tagNumber":"Tag Number","species":"Species","breed":"Breed","gender":"Gender","dateOfBirth":"Date of Birth","age":"Age","weight":"Weight","color":"Color","markings":"Markings","location":"Location","healthStatus":"Health Status","breedingStatus":"Breeding Status","productionType":"Production Type","sire":"Sire","dam":"Dam","rfidTag":"RFID Tag","earTag":"Ear Tag","microchip":"Microchip ID","purchasePrice":"Purchase Price","purchaseDate":"Purchase Date","currentValue":"Current Value","weightHistory":"Weight History","productionRecords":"Production Records","images":"Images","documents":"Documents","male":"Male","female":"Female","cattle":"Cattle","sheep":"Sheep","goat":"Goat","pig":"Pig","chicken":"Chicken","horse":"Horse","other":"Other","active":"Active","sold":"Sold","deceased":"Deceased","transferred":"Transferred","quarantine":"Quarantine","healthy":"Healthy","sick":"Sick","injured":"Injured","recovering":"Recovering","available":"Available","pregnant":"Pregnant","lactating":"Lactating","breeding":"Breeding","retired":"Retired"},"health":{"title":"Health Management","healthRecords":"Health Records","addHealthRecord":"Add Health Record","editHealthRecord":"Edit Health Record","healthAlerts":"Health Alerts","vaccinationSchedule":"Vaccination Schedule","recordType":"Record Type","veterinarian":"Veterinarian","diagnosis":"Diagnosis","treatment":"Treatment","medication":"Medication","dosage":"Dosage","frequency":"Frequency","duration":"Duration","symptoms":"Symptoms","temperature":"Temperature","heartRate":"Heart Rate","respiratoryRate":"Respiratory Rate","bodyConditionScore":"Body Condition Score","followUpRequired":"Follow-up Required","followUpDate":"Follow-up Date","withdrawalPeriod":"Withdrawal Period","cost":"Cost","vaccination":"Vaccination","checkup":"Checkup","illness":"Illness","injury":"Injury","surgery":"Surgery","test":"Test","vaccine":"Vaccine","manufacturer":"Manufacturer","batchNumber":"Batch Number","expirationDate":"Expiration Date","route":"Route","intramuscular":"Intramuscular","subcutaneous":"Subcutaneous","oral":"Oral","nasal":"Nasal","intravenous":"Intravenous"},"breeding":{"title":"Breeding Management","breedingRecords":"Breeding Records","birthRecords":"Birth Records","heatDetection":"Heat Detection","addBreedingRecord":"Add Breeding Record","editBreedingRecord":"Edit Breeding Record","addBirthRecord":"Add Birth Record","editBirthRecord":"Edit Birth Record","breedingDate":"Breeding Date","breedingMethod":"Breeding Method","expectedDueDate":"Expected Due Date","actualBirthDate":"Actual Birth Date","pregnancyConfirmed":"Pregnancy Confirmed","pregnancyConfirmationDate":"Pregnancy Confirmation Date","pregnancyConfirmationMethod":"Pregnancy Confirmation Method","gestationPeriod":"Gestation Period","birthWeight":"Birth Weight","birthType":"Birth Type","complications":"Complications","offspring":"Offspring","placentaExpelled":"Placenta Expelled","postBirthTreatment":"Post-Birth Treatment","natural":"Natural","artificialInsemination":"Artificial Insemination","embryoTransfer":"Embryo Transfer","assisted":"Assisted","cesarean":"Cesarean","ultrasound":"Ultrasound","bloodTest":"Blood Test","physicalExam":"Physical Exam","planned":"Planned","completed":"Completed","failed":"Failed","aborted":"Aborted","heatDate":"Heat Date","heatIntensity":"Heat Intensity","heatDuration":"Heat Duration","behaviorSigns":"Behavior Signs","physicalSigns":"Physical Signs","breedingRecommended":"Breeding Recommended","breedingWindow":"Breeding Window","bred":"Bred","weak":"Weak","moderate":"Moderate","strong":"Strong"},"feeding":{"title":"Feed Management","feedingRecords":"Feeding Records","feedInventory":"Feed Inventory","feedingPlans":"Feeding Plans","addFeedingRecord":"Add Feeding Record","editFeedingRecord":"Edit Feeding Record","feedType":"Feed Type","quantity":"Quantity","unit":"Unit","feedingTime":"Feeding Time","feedCost":"Feed Cost","nutritionAnalysis":"Nutrition Analysis","protein":"Protein","carbohydrates":"Carbohydrates","fat":"Fat","fiber":"Fiber","vitamins":"Vitamins","minerals":"Minerals","calories":"Calories","stockLevel":"Stock Level","reorderLevel":"Reorder Level","supplier":"Supplier","orderDate":"Order Date","deliveryDate":"Delivery Date","kg":"kg","lbs":"lbs","tons":"tons","bags":"bags","liters":"liters","gallons":"gallons"},"financial":{"title":"Financial Management","transactions":"Transactions","income":"Income","expenses":"Expenses","budget":"Budget","reports":"Reports","addTransaction":"Add Transaction","editTransaction":"Edit Transaction","transactionType":"Transaction Type","amount":"Amount","transactionDate":"Transaction Date","paymentMethod":"Payment Method","reference":"Reference","vendor":"Vendor","customer":"Customer","invoice":"Invoice","receipt":"Receipt","profit":"Profit","loss":"Loss","revenue":"Revenue","costs":"Costs","roi":"Return on Investment","cash":"Cash","check":"Check","creditCard":"Credit Card","bankTransfer":"Bank Transfer","animalSale":"Animal Sale","productSale":"Product Sale","feedPurchase":"Feed Purchase","veterinaryExpense":"Veterinary Expense","equipmentPurchase":"Equipment Purchase","laborCost":"Labor Cost","utilities":"Utilities","insurance":"Insurance","taxes":"Taxes","other":"Other"},"reports":{"title":"Reports & Analytics","generateReport":"Generate Report","reportType":"Report Type","dateRange":"Date Range","animalReport":"Animal Report","healthReport":"Health Report","breedingReport":"Breeding Report","financialReport":"Financial Report","productionReport":"Production Report","inventoryReport":"Inventory Report","customReport":"Custom Report","exportToPDF":"Export to PDF","exportToExcel":"Export to Excel","exportToCSV":"Export to CSV","printReport":"Print Report","scheduleReport":"Schedule Report","reportScheduled":"Report Scheduled","daily":"Daily","weekly":"Weekly","monthly":"Monthly","quarterly":"Quarterly","yearly":"Yearly","custom":"Custom"},"settings":{"title":"Settings","generalSettings":"General Settings","userSettings":"User Settings","systemSettings":"System Settings","language":"Language","theme":"Theme","notifications":"Notifications","privacy":"Privacy","security":"Security","backup":"Backup","restore":"Restore","lightTheme":"Light Theme","darkTheme":"Dark Theme","emailNotifications":"Email Notifications","pushNotifications":"Push Notifications","smsNotifications":"SMS Notifications","twoFactorAuth":"Two-Factor Authentication","passwordPolicy":"Password Policy","sessionTimeout":"Session Timeout","dataRetention":"Data Retention","auditLog":"Audit Log"},"languages":{"en":"English","af":"Afrikaans","st":"Sesotho","tn":"Setswana","zu":"isiZulu"},"roles":{"admin":"Administrator","manager":"Manager","staff":"Staff","veterinarian":"Veterinarian","viewer":"Viewer"},"validation":{"required":"This field is required","email":"Please enter a valid email address","minLength":"Minimum length is {{min}} characters","maxLength":"Maximum length is {{max}} characters","numeric":"Please enter a valid number","positive":"Please enter a positive number","date":"Please enter a valid date","phone":"Please enter a valid phone number","passwordMatch":"Passwords do not match","passwordStrength":"Password must contain at least one uppercase letter, one lowercase letter, and one number"},"messages":{"saveSuccess":"Data saved successfully","saveError":"Failed to save data","deleteSuccess":"Data deleted successfully","deleteError":"Failed to delete data","updateSuccess":"Data updated successfully","updateError":"Failed to update data","loadError":"Failed to load data","networkError":"Network error. Please check your connection.","serverError":"Server error. Please try again later.","confirmDelete":"Are you sure you want to delete this item?","unsavedChanges":"You have unsaved changes. Are you sure you want to leave?","noData":"No data available","noResults":"No results found","searchPlaceholder":"Search...","selectOption":"Select an option","uploadSuccess":"File uploaded successfully","uploadError":"Failed to upload file","fileSizeError":"File size exceeds the maximum limit","fileTypeError":"Invalid file type"}}')},af:{translation:JSON.parse('{"common":{"loading":"Laai...","save":"Stoor","cancel":"Kanselleer","delete":"Verwyder","edit":"Wysig","add":"Voeg by","search":"Soek","filter":"Filter","export":"Uitvoer","import":"Invoer","refresh":"Verfris","back":"Terug","next":"Volgende","previous":"Vorige","submit":"Dien in","reset":"Herstel","clear":"Maak skoon","close":"Sluit","open":"Maak oop","view":"Bekyk","download":"Laai af","upload":"Laai op","print":"Druk","share":"Deel","copy":"Kopieer","cut":"Knip","paste":"Plak","select":"Kies","selectAll":"Kies alles","none":"Geen","all":"Alles","yes":"Ja","no":"Nee","ok":"OK","confirm":"Bevestig","warning":"Waarskuwing","error":"Fout","success":"Sukses","info":"Inligting","required":"Vereiste","optional":"Opsioneel","name":"Naam","description":"Beskrywing","date":"Datum","time":"Tyd","status":"Status","type":"Tipe","category":"Kategorie","notes":"Notas","actions":"Aksies"},"auth":{"login":"Meld aan","logout":"Meld af","register":"Registreer","username":"Gebruikersnaam","email":"E-pos","password":"Wagwoord","confirmPassword":"Bevestig wagwoord","firstName":"Voornaam","lastName":"Van","phoneNumber":"Telefoonnommer","department":"Departement","role":"Rol","forgotPassword":"Wagwoord vergeet?","resetPassword":"Herstel wagwoord","changePassword":"Verander wagwoord","currentPassword":"Huidige wagwoord","newPassword":"Nuwe wagwoord","loginSuccess":"Aanmelding suksesvol","loginError":"Aanmelding gefaal","logoutSuccess":"Afmelding suksesvol","registerSuccess":"Registrasie suksesvol","registerError":"Registrasie gefaal","passwordChangeSuccess":"Wagwoord suksesvol verander","passwordChangeError":"Wagwoord verandering gefaal","invalidCredentials":"Ongeldige gebruikersnaam of wagwoord","accountDeactivated":"Rekening is gedeaktiveer","sessionExpired":"Sessie verval. Meld asseblief weer aan.","welcomeBack":"Welkom terug","signInToContinue":"Meld aan om voort te gaan na AMPD Veestok Bestuur","dontHaveAccount":"Het jy nie \'n rekening nie?","alreadyHaveAccount":"Het jy reeds \'n rekening?","createAccount":"Skep rekening","signIn":"Meld aan"},"navigation":{"dashboard":"Paneelbord","animals":"Diere","breeding":"Teling","health":"Gesondheid","feeding":"Voeding","financial":"Finansieel","compliance":"Nakoming","inventory":"Voorraad","analytics":"Analise","reports":"Verslae","settings":"Instellings","profile":"Profiel","users":"Gebruikers","help":"Hulp","support":"Ondersteuning"},"dashboard":{"title":"Paneelbord","welcome":"Welkom by AMPD Veestok Bestuur","overview":"Oorsig","quickStats":"Vinnige Statistieke","recentActivity":"Onlangse Aktiwiteit","alerts":"Waarskuwings","upcomingTasks":"Komende Take","totalAnimals":"Totale Diere","healthyAnimals":"Gesonde Diere","pregnantAnimals":"Dragtige Diere","upcomingBirths":"Komende Geboortes","vaccinationsDue":"Inentings Verskuldig","healthAlerts":"Gesondheid Waarskuwings","feedingAlerts":"Voeding Waarskuwings","breedingAlerts":"Teling Waarskuwings"},"languages":{"en":"Engels","af":"Afrikaans","st":"Sesotho","tn":"Setswana","zu":"isiZulu"},"roles":{"admin":"Administrateur","manager":"Bestuurder","staff":"Personeel","veterinarian":"Veearts","viewer":"Kyker"}}')},st:{translation:JSON.parse('{"common":{"loading":"E nka nako...","save":"Boloka","cancel":"Hlakola","delete":"Hlakola","edit":"Fetola","add":"Eketsa","search":"Batla","filter":"Kgetha","export":"Ntsha","import":"Tsenya","refresh":"Nchafatsa","back":"Moraho","next":"Latelang","previous":"Pele","submit":"Romela","reset":"Qala hape","clear":"Hlakola","close":"Koala","open":"Bula","view":"Sheba","download":"Jarolla","upload":"Tsenya","print":"Hatisa","share":"Arolelana","copy":"Kopisa","cut":"Kuta","paste":"Kgomaretsa","select":"Kgetha","selectAll":"Kgetha tsohle","none":"Letho","all":"Tsohle","yes":"Ee","no":"Tjhe","ok":"Ho lokile","confirm":"Netefatsa","warning":"Temoso","error":"Phoso","success":"Katleho","info":"Tlhahisoleseding","required":"Ya hlokahalang","optional":"Ya ikgethang","name":"Lebitso","description":"Tlhaloso","date":"Letsatsi","time":"Nako","status":"Boemo","type":"Mofuta","category":"Sehlopha","notes":"Dintlha","actions":"Liketso"},"auth":{"login":"Kena","logout":"Tswa","register":"Ngodisa","username":"Lebitso la mosebedisi","email":"Imeile","password":"Phasewete","confirmPassword":"Netefatsa phasewete","firstName":"Lebitso la pele","lastName":"Lebitso la ho qetela","phoneNumber":"Nomoro ya mohala","department":"Lefapha","role":"Karolo","forgotPassword":"O lebetse phasewete?","resetPassword":"Beha phasewete hape","changePassword":"Fetola phasewete","currentPassword":"Phasewete ya hajwale","newPassword":"Phasewete e ncha","loginSuccess":"Ho kena ho atlehile","loginError":"Ho kena ho hloleha","logoutSuccess":"Ho tswa ho atlehile","registerSuccess":"Ho ngodisa ho atlehile","registerError":"Ho ngodisa ho hloleha","passwordChangeSuccess":"Phasewete e fetohile ka katleho","passwordChangeError":"Ho fetola phasewete ho hloleha","invalidCredentials":"Lebitso la mosebedisi kapa phasewete e fosahetseng","accountDeactivated":"Akhaonto e thibetswe","sessionExpired":"Sebaka se felile. Ka kopo kena hape.","welcomeBack":"Rea u amohela hape","signInToContinue":"Kena ho tswela pele ho AMPD Livestock Management","dontHaveAccount":"Ha u na akhaonto?","alreadyHaveAccount":"U se u na le akhaonto?","createAccount":"Theha akhaonto","signIn":"Kena"},"navigation":{"dashboard":"Boto ya taolo","animals":"Diphoofolo","breeding":"Pelehi","health":"Bophelo bo botle","feeding":"Ho fepa","financial":"Ditjhelete","compliance":"Ho latela melao","inventory":"Thepa","analytics":"Manollo","reports":"Dipego","settings":"Ditokiso","profile":"Profaele","users":"Basebedisi","help":"Thuso","support":"Tshehets\u043e"},"dashboard":{"title":"Boto ya taolo","welcome":"Rea u amohela ho AMPD Livestock Management","overview":"Kakaretso","quickStats":"Dipalopalo tse potlakang","recentActivity":"Mesebetsi ya morao-rao","alerts":"Ditemoso","upcomingTasks":"Mesebetsi e tlang","totalAnimals":"Diphoofolo kaofela","healthyAnimals":"Diphoofolo tse phetseng hantle","pregnantAnimals":"Diphoofolo tse imileng","upcomingBirths":"Ditswalo tse tlang","vaccinationsDue":"Dihlahlamiso tse hlokahalang","healthAlerts":"Ditemoso tsa bophelo bo botle","feedingAlerts":"Ditemoso tsa ho fepa","breedingAlerts":"Ditemoso tsa pelehi"},"languages":{"en":"Senyesemane","af":"Seafrikanse","st":"Sesotho","tn":"Setswana","zu":"isiZulu"},"roles":{"admin":"Motsamaisi","manager":"Molaodi","staff":"Basebetsi","veterinarian":"Ngaka ya diphoofolo","viewer":"Motshebetsi"}}')},tn:{translation:JSON.parse('{"common":{"loading":"E a loda...","save":"Boloka","cancel":"Khansela","delete":"Phimola","edit":"Baakanya","add":"Tsenya","search":"Batla","filter":"Kgetho","export":"Ntsha","import":"Tsenya","refresh":"Nt\u0161hafatsa","back":"Morago","next":"Latelang","previous":"Pele","submit":"Romela","reset":"Simolola","clear":"Phimola","close":"Tswala","open":"Bula","view":"Bona","download":"Kopolola","upload":"Tsenya","print":"Gatisa","share":"Abelana","copy":"Kopolola","cut":"Ripa","paste":"Kgomaretsa","select":"Tlhopha","selectAll":"Tlhopha tsotlhe","none":"Sepe","all":"Tsotlhe","yes":"Ee","no":"Nnyaa","ok":"Go siame","confirm":"Netefatsa","warning":"Temoso","error":"Phoso","success":"Katlego","info":"Tshedimosetso","required":"E a tlhokega","optional":"Ga e a tlhokega","name":"Leina","description":"Tlhaloso","date":"Letlha","time":"Nako","status":"Maemo","type":"Mofuta","category":"Setlhopha","notes":"Dintlha","actions":"Ditiro"},"auth":{"login":"Tsena","logout":"Tswa","register":"Kwala","username":"Leina la modirisi","email":"Imeile","password":"Phasewete","confirmPassword":"Netefatsa phasewete","firstName":"Leina la ntlha","lastName":"Leina la bofelo","phoneNumber":"Nomoro ya mogala","department":"Lefapha","role":"Seabe","forgotPassword":"O lebetse phasewete?","resetPassword":"Beha phasewete gape","changePassword":"Fetola phasewete","currentPassword":"Phasewete ya gone jaanong","newPassword":"Phasewete e nt\u0161ha","loginSuccess":"Go tsena go atlehile","loginError":"Go tsena ga palelwa","logoutSuccess":"Go tswa go atlehile","registerSuccess":"Go kwala go atlehile","registerError":"Go kwala ga palelwa","passwordChangeSuccess":"Phasewete e fetogile ka katlego","passwordChangeError":"Go fetola phasewete ga palelwa","invalidCredentials":"Leina la modirisi kgotsa phasewete e sa siamang","accountDeactivated":"Akhaonto e thibetswe","sessionExpired":"Sebaka se fedile. Ka kopo tsena gape.","welcomeBack":"Re go amogela gape","signInToContinue":"Tsena go tswelela go AMPD Livestock Management","dontHaveAccount":"Ga o na akhaonto?","alreadyHaveAccount":"O sena o na le akhaonto?","createAccount":"Dira akhaonto","signIn":"Tsena"},"navigation":{"dashboard":"Boto ya taolo","animals":"Diphologolo","breeding":"Pelehi","health":"Boitekanelo","feeding":"Go fepa","financial":"Dit\u0161helete","compliance":"Go latela melao","inventory":"Thepa","analytics":"Tshekatsheko","reports":"Dipego","settings":"Ditokiso","profile":"Profaele","users":"Badirisi","help":"Thuso","support":"Tshegetso"},"dashboard":{"title":"Boto ya taolo","welcome":"Re go amogela go AMPD Livestock Management","overview":"Kakaretso","quickStats":"Dipalopalo tse di bonolo","recentActivity":"Ditiro tsa bosheng","alerts":"Ditemoso","upcomingTasks":"Ditiro tse di tlang","totalAnimals":"Diphologolo tsotlhe","healthyAnimals":"Diphologolo tse di itekanetseng","pregnantAnimals":"Diphologolo tse di imileng","upcomingBirths":"Ditswalo tse di tlang","vaccinationsDue":"Dihlahlamiso tse di tlhokegang","healthAlerts":"Ditemoso tsa boitekanelo","feedingAlerts":"Ditemoso tsa go fepa","breedingAlerts":"Ditemoso tsa pelehi"},"languages":{"en":"Sekgowa","af":"Seafrikanse","st":"Sesotho","tn":"Setswana","zu":"isiZulu"},"roles":{"admin":"Motsamaisi","manager":"Molaodi","staff":"Badiri","veterinarian":"Ngaka ya diphologolo","viewer":"Mmoni"}}')},zu:{translation:JSON.parse('{"common":{"loading":"Iyalayisha...","save":"Londoloza","cancel":"Khansela","delete":"Susa","edit":"Hlela","add":"Engeza","search":"Sesha","filter":"Hlola","export":"Khipha","import":"Ngenisa","refresh":"Vuselela","back":"Emuva","next":"Okulandelayo","previous":"Okwangaphambili","submit":"Thumela","reset":"Setha kabusha","clear":"Sula","close":"Vala","open":"Vula","view":"Buka","download":"Dawuniloda","upload":"Layisha","print":"Phrinta","share":"Yabelana","copy":"Kopisha","cut":"Sika","paste":"Namathisela","select":"Khetha","selectAll":"Khetha konke","none":"Lutho","all":"Konke","yes":"Yebo","no":"Cha","ok":"Kulungile","confirm":"Qinisekisa","warning":"Isexwayiso","error":"Iphutha","success":"Impumelelo","info":"Ulwazi","required":"Kuyadingeka","optional":"Akuphoqelekile","name":"Igama","description":"Incazelo","date":"Usuku","time":"Isikhathi","status":"Isimo","type":"Uhlobo","category":"Isigaba","notes":"Amanothi","actions":"Izenzo"},"auth":{"login":"Ngena","logout":"Phuma","register":"Bhalisa","username":"Igama lomsebenzisi","email":"I-imeyili","password":"Iphasiwedi","confirmPassword":"Qinisekisa iphasiwedi","firstName":"Igama lokuqala","lastName":"Isibongo","phoneNumber":"Inombolo yocingo","department":"Umnyango","role":"Indima","forgotPassword":"Ukhohlwe iphasiwedi?","resetPassword":"Setha iphasiwedi kabusha","changePassword":"Shintsha iphasiwedi","currentPassword":"Iphasiwedi yamanje","newPassword":"Iphasiwedi entsha","loginSuccess":"Ukungenela kuphumelele","loginError":"Ukungenela kuhlulekile","logoutSuccess":"Ukuphuma kuphumelele","registerSuccess":"Ukubhalisa kuphumelele","registerError":"Ukubhalisa kuhlulekile","passwordChangeSuccess":"Iphasiwedi ishintshwe ngempumelelo","passwordChangeError":"Ukushintsha iphasiwedi kuhlulekile","invalidCredentials":"Igama lomsebenzisi noma iphasiwedi engalungile","accountDeactivated":"I-akhawunti ivaliwe","sessionExpired":"Isikhathi siphelelile. Sicela ungene futhi.","welcomeBack":"Sawubona futhi","signInToContinue":"Ngena ukuze uqhubeke ku-AMPD Livestock Management","dontHaveAccount":"Awunayo i-akhawunti?","alreadyHaveAccount":"Usunayo i-akhawunti?","createAccount":"Dala i-akhawunti","signIn":"Ngena"},"navigation":{"dashboard":"Ibhodi lokulawula","animals":"Izilwane","breeding":"Ukuzala","health":"Impilo","feeding":"Ukudla","financial":"Ezezimali","compliance":"Ukulandela imithetho","inventory":"Impahla","analytics":"Ukuhlaziya","reports":"Imibiko","settings":"Izilungiselelo","profile":"Iphrofayili","users":"Abasebenzisi","help":"Usizo","support":"Ukusekela"},"dashboard":{"title":"Ibhodi lokulawula","welcome":"Siyakwamukela ku-AMPD Livestock Management","overview":"Ukubuka konke","quickStats":"Izibalo ezisheshayo","recentActivity":"Umsebenzi wakamuva","alerts":"Izexwayiso","upcomingTasks":"Imisebenzi ezayo","totalAnimals":"Izilwane zonke","healthyAnimals":"Izilwane eziphilile","pregnantAnimals":"Izilwane ezikhulelwe","upcomingBirths":"Ukuzalwa okuzayo","vaccinationsDue":"Ukugoma okudingekayo","healthAlerts":"Izexwayiso zempilo","feedingAlerts":"Izexwayiso zokudla","breedingAlerts":"Izexwayiso zokuzala"},"languages":{"en":"IsiNgisi","af":"IsiBhunu","st":"IsiSuthu","tn":"IsiTswana","zu":"IsiZulu"},"roles":{"admin":"Umphathi","manager":"Umphathi","staff":"Abasebenzi","veterinarian":"Udokotela wezilwane","viewer":"Umbukeli"}}')}};_a.use(si).use(Ma).init({resources:li,fallbackLng:"en",debug:!1,interpolation:{escapeValue:!1},detection:{order:["localStorage","navigator","htmlTag"],caches:["localStorage"]},react:{useSuspense:!1}});const ui=()=>(0,zn.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,zn.jsxs)("div",{className:"max-w-md w-full space-y-8",children:[(0,zn.jsx)("h2",{className:"text-3xl font-bold text-center",children:"AMPD Livestock Management"}),(0,zn.jsx)("p",{className:"text-center",children:"Login page coming soon..."})]})}),ci=()=>(0,zn.jsxs)("div",{className:"min-h-screen bg-gray-50 p-8",children:[(0,zn.jsx)("h1",{className:"text-3xl font-bold mb-6",children:"Dashboard"}),(0,zn.jsx)("p",{children:"Dashboard content coming soon..."})]});const di=function(){const{isAuthenticated:e}=w((e=>e.auth));return(0,zn.jsx)("div",{className:"App",children:(0,zn.jsxs)(ze,{children:[(0,zn.jsx)(Ie,{path:"/login",element:e?(0,zn.jsx)(De,{to:"/dashboard",replace:!0}):(0,zn.jsx)(ui,{})}),(0,zn.jsx)(Ie,{path:"/dashboard",element:e?(0,zn.jsx)(ci,{}):(0,zn.jsx)(De,{to:"/login",replace:!0})}),(0,zn.jsx)(Ie,{index:!0,element:(0,zn.jsx)(De,{to:"/dashboard",replace:!0})}),(0,zn.jsx)(Ie,{path:"*",element:(0,zn.jsx)(De,{to:"/login",replace:!0})})]})})};function fi(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];throw Error("[Immer] minified error nr: "+e+(n.length?" "+n.map((function(e){return"'"+e+"'"})).join(","):"")+". Find the full error at: https://bit.ly/3cXEKWf")}function pi(e){return!!e&&!!e[ns]}function hi(e){var t;return!!e&&(function(e){if(!e||"object"!=typeof e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;var n=Object.hasOwnProperty.call(t,"constructor")&&t.constructor;return n===Object||"function"==typeof n&&Function.toString.call(n)===rs}(e)||Array.isArray(e)||!!e[ts]||!!(null===(t=e.constructor)||void 0===t?void 0:t[ts])||ki(e)||Si(e))}function gi(e,t,n){void 0===n&&(n=!1),0===mi(e)?(n?Object.keys:os)(e).forEach((function(r){n&&"symbol"==typeof r||t(r,e[r],e)})):e.forEach((function(n,r){return t(r,n,e)}))}function mi(e){var t=e[ns];return t?t.i>3?t.i-4:t.i:Array.isArray(e)?1:ki(e)?2:Si(e)?3:0}function yi(e,t){return 2===mi(e)?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function vi(e,t){return 2===mi(e)?e.get(t):e[t]}function bi(e,t,n){var r=mi(e);2===r?e.set(t,n):3===r?e.add(n):e[t]=n}function wi(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}function ki(e){return Xi&&e instanceof Map}function Si(e){return Yi&&e instanceof Set}function xi(e){return e.o||e.t}function Ei(e){if(Array.isArray(e))return Array.prototype.slice.call(e);var t=as(e);delete t[ns];for(var n=os(t),r=0;r<n.length;r++){var o=n[r],a=t[o];!1===a.writable&&(a.writable=!0,a.configurable=!0),(a.get||a.set)&&(t[o]={configurable:!0,writable:!0,enumerable:a.enumerable,value:e[o]})}return Object.create(Object.getPrototypeOf(e),t)}function Ci(e,t){return void 0===t&&(t=!1),Oi(e)||pi(e)||!hi(e)||(mi(e)>1&&(e.set=e.add=e.clear=e.delete=Pi),Object.freeze(e),t&&gi(e,(function(e,t){return Ci(t,!0)}),!0)),e}function Pi(){fi(2)}function Oi(e){return null==e||"object"!=typeof e||Object.isFrozen(e)}function Ri(e){var t=is[e];return t||fi(18,e),t}function Ti(e,t){is[e]||(is[e]=t)}function Ni(){return Gi}function Ai(e,t){t&&(Ri("Patches"),e.u=[],e.s=[],e.v=t)}function Li(e){_i(e),e.p.forEach(Di),e.p=null}function _i(e){e===Gi&&(Gi=e.l)}function ji(e){return Gi={p:[],l:Gi,h:e,m:!0,_:0}}function Di(e){var t=e[ns];0===t.i||1===t.i?t.j():t.g=!0}function Ii(e,t){t._=t.p.length;var n=t.p[0],r=void 0!==e&&e!==n;return t.h.O||Ri("ES5").S(t,e,r),r?(n[ns].P&&(Li(t),fi(4)),hi(e)&&(e=Fi(t,e),t.l||Mi(t,e)),t.u&&Ri("Patches").M(n[ns].t,e,t.u,t.s)):e=Fi(t,n,[]),Li(t),t.u&&t.v(t.u,t.s),e!==es?e:void 0}function Fi(e,t,n){if(Oi(t))return t;var r=t[ns];if(!r)return gi(t,(function(o,a){return zi(e,r,t,o,a,n)}),!0),t;if(r.A!==e)return t;if(!r.P)return Mi(e,r.t,!0),r.t;if(!r.I){r.I=!0,r.A._--;var o=4===r.i||5===r.i?r.o=Ei(r.k):r.o,a=o,i=!1;3===r.i&&(a=new Set(o),o.clear(),i=!0),gi(a,(function(t,a){return zi(e,r,o,t,a,n,i)})),Mi(e,o,!1),n&&e.u&&Ri("Patches").N(r,n,e.u,e.s)}return r.o}function zi(e,t,n,r,o,a,i){if(pi(o)){var s=Fi(e,o,a&&t&&3!==t.i&&!yi(t.R,r)?a.concat(r):void 0);if(bi(n,r,s),!pi(s))return;e.m=!1}else i&&n.add(o);if(hi(o)&&!Oi(o)){if(!e.h.D&&e._<1)return;Fi(e,o),t&&t.A.l||Mi(e,o)}}function Mi(e,t,n){void 0===n&&(n=!1),!e.l&&e.h.D&&e.m&&Ci(t,n)}function Bi(e,t){var n=e[ns];return(n?xi(n):e)[t]}function Ui(e,t){if(t in e)for(var n=Object.getPrototypeOf(e);n;){var r=Object.getOwnPropertyDescriptor(n,t);if(r)return r;n=Object.getPrototypeOf(n)}}function Vi(e){e.P||(e.P=!0,e.l&&Vi(e.l))}function Wi(e){e.o||(e.o=Ei(e.t))}function Hi(e,t,n){var r=ki(t)?Ri("MapSet").F(t,n):Si(t)?Ri("MapSet").T(t,n):e.O?function(e,t){var n=Array.isArray(e),r={i:n?1:0,A:t?t.A:Ni(),P:!1,I:!1,R:{},l:t,t:e,k:null,o:null,j:null,C:!1},o=r,a=ss;n&&(o=[r],a=ls);var i=Proxy.revocable(o,a),s=i.revoke,l=i.proxy;return r.k=l,r.j=s,l}(t,n):Ri("ES5").J(t,n);return(n?n.A:Ni()).p.push(r),r}function $i(e){return pi(e)||fi(22,e),function e(t){if(!hi(t))return t;var n,r=t[ns],o=mi(t);if(r){if(!r.P&&(r.i<4||!Ri("ES5").K(r)))return r.t;r.I=!0,n=Ki(t,o),r.I=!1}else n=Ki(t,o);return gi(n,(function(t,o){r&&vi(r.t,t)===o||bi(n,t,e(o))})),3===o?new Set(n):n}(e)}function Ki(e,t){switch(t){case 2:return new Map(e);case 3:return Array.from(e)}return Ei(e)}function qi(){function e(e,t){var n=o[e];return n?n.enumerable=t:o[e]=n={configurable:!0,enumerable:t,get:function(){var t=this[ns];return ss.get(t,e)},set:function(t){var n=this[ns];ss.set(n,e,t)}},n}function t(e){for(var t=e.length-1;t>=0;t--){var o=e[t][ns];if(!o.P)switch(o.i){case 5:r(o)&&Vi(o);break;case 4:n(o)&&Vi(o)}}}function n(e){for(var t=e.t,n=e.k,r=os(n),o=r.length-1;o>=0;o--){var a=r[o];if(a!==ns){var i=t[a];if(void 0===i&&!yi(t,a))return!0;var s=n[a],l=s&&s[ns];if(l?l.t!==i:!wi(s,i))return!0}}var u=!!t[ns];return r.length!==os(t).length+(u?0:1)}function r(e){var t=e.k;if(t.length!==e.t.length)return!0;var n=Object.getOwnPropertyDescriptor(t,t.length-1);if(n&&!n.get)return!0;for(var r=0;r<t.length;r++)if(!t.hasOwnProperty(r))return!0;return!1}var o={};Ti("ES5",{J:function(t,n){var r=Array.isArray(t),o=function(t,n){if(t){for(var r=Array(n.length),o=0;o<n.length;o++)Object.defineProperty(r,""+o,e(o,!0));return r}var a=as(n);delete a[ns];for(var i=os(a),s=0;s<i.length;s++){var l=i[s];a[l]=e(l,t||!!a[l].enumerable)}return Object.create(Object.getPrototypeOf(n),a)}(r,t),a={i:r?5:4,A:n?n.A:Ni(),P:!1,I:!1,R:{},l:n,t:t,k:o,o:null,g:!1,C:!1};return Object.defineProperty(o,ns,{value:a,writable:!0}),o},S:function(e,n,o){o?pi(n)&&n[ns].A===e&&t(e.p):(e.u&&function e(t){if(t&&"object"==typeof t){var n=t[ns];if(n){var o=n.t,a=n.k,i=n.R,s=n.i;if(4===s)gi(a,(function(t){t!==ns&&(void 0!==o[t]||yi(o,t)?i[t]||e(a[t]):(i[t]=!0,Vi(n)))})),gi(o,(function(e){void 0!==a[e]||yi(a,e)||(i[e]=!1,Vi(n))}));else if(5===s){if(r(n)&&(Vi(n),i.length=!0),a.length<o.length)for(var l=a.length;l<o.length;l++)i[l]=!1;else for(var u=o.length;u<a.length;u++)i[u]=!0;for(var c=Math.min(a.length,o.length),d=0;d<c;d++)a.hasOwnProperty(d)||(i[d]=!0),void 0===i[d]&&e(a[d])}}}}(e.p[0]),t(e.p))},K:function(e){return 4===e.i?n(e):r(e)}})}var Qi,Gi,Ji="undefined"!=typeof Symbol&&"symbol"==typeof Symbol("x"),Xi="undefined"!=typeof Map,Yi="undefined"!=typeof Set,Zi="undefined"!=typeof Proxy&&void 0!==Proxy.revocable&&"undefined"!=typeof Reflect,es=Ji?Symbol.for("immer-nothing"):((Qi={})["immer-nothing"]=!0,Qi),ts=Ji?Symbol.for("immer-draftable"):"__$immer_draftable",ns=Ji?Symbol.for("immer-state"):"__$immer_state",rs=("undefined"!=typeof Symbol&&Symbol.iterator,""+Object.prototype.constructor),os="undefined"!=typeof Reflect&&Reflect.ownKeys?Reflect.ownKeys:void 0!==Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames,as=Object.getOwnPropertyDescriptors||function(e){var t={};return os(e).forEach((function(n){t[n]=Object.getOwnPropertyDescriptor(e,n)})),t},is={},ss={get:function(e,t){if(t===ns)return e;var n=xi(e);if(!yi(n,t))return function(e,t,n){var r,o=Ui(t,n);return o?"value"in o?o.value:null===(r=o.get)||void 0===r?void 0:r.call(e.k):void 0}(e,n,t);var r=n[t];return e.I||!hi(r)?r:r===Bi(e.t,t)?(Wi(e),e.o[t]=Hi(e.A.h,r,e)):r},has:function(e,t){return t in xi(e)},ownKeys:function(e){return Reflect.ownKeys(xi(e))},set:function(e,t,n){var r=Ui(xi(e),t);if(null==r?void 0:r.set)return r.set.call(e.k,n),!0;if(!e.P){var o=Bi(xi(e),t),a=null==o?void 0:o[ns];if(a&&a.t===n)return e.o[t]=n,e.R[t]=!1,!0;if(wi(n,o)&&(void 0!==n||yi(e.t,t)))return!0;Wi(e),Vi(e)}return e.o[t]===n&&(void 0!==n||t in e.o)||Number.isNaN(n)&&Number.isNaN(e.o[t])||(e.o[t]=n,e.R[t]=!0),!0},deleteProperty:function(e,t){return void 0!==Bi(e.t,t)||t in e.t?(e.R[t]=!1,Wi(e),Vi(e)):delete e.R[t],e.o&&delete e.o[t],!0},getOwnPropertyDescriptor:function(e,t){var n=xi(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r?{writable:!0,configurable:1!==e.i||"length"!==t,enumerable:r.enumerable,value:n[t]}:r},defineProperty:function(){fi(11)},getPrototypeOf:function(e){return Object.getPrototypeOf(e.t)},setPrototypeOf:function(){fi(12)}},ls={};gi(ss,(function(e,t){ls[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}})),ls.deleteProperty=function(e,t){return ls.set.call(this,e,t,void 0)},ls.set=function(e,t,n){return ss.set.call(this,e[0],t,n,e[0])};var us=function(){function e(e){var t=this;this.O=Zi,this.D=!0,this.produce=function(e,n,r){if("function"==typeof e&&"function"!=typeof n){var o=n;n=e;var a=t;return function(e){var t=this;void 0===e&&(e=o);for(var r=arguments.length,i=Array(r>1?r-1:0),s=1;s<r;s++)i[s-1]=arguments[s];return a.produce(e,(function(e){var r;return(r=n).call.apply(r,[t,e].concat(i))}))}}var i;if("function"!=typeof n&&fi(6),void 0!==r&&"function"!=typeof r&&fi(7),hi(e)){var s=ji(t),l=Hi(t,e,void 0),u=!0;try{i=n(l),u=!1}finally{u?Li(s):_i(s)}return"undefined"!=typeof Promise&&i instanceof Promise?i.then((function(e){return Ai(s,r),Ii(e,s)}),(function(e){throw Li(s),e})):(Ai(s,r),Ii(i,s))}if(!e||"object"!=typeof e){if(void 0===(i=n(e))&&(i=e),i===es&&(i=void 0),t.D&&Ci(i,!0),r){var c=[],d=[];Ri("Patches").M(e,i,c,d),r(c,d)}return i}fi(21,e)},this.produceWithPatches=function(e,n){if("function"==typeof e)return function(n){for(var r=arguments.length,o=Array(r>1?r-1:0),a=1;a<r;a++)o[a-1]=arguments[a];return t.produceWithPatches(n,(function(t){return e.apply(void 0,[t].concat(o))}))};var r,o,a=t.produce(e,n,(function(e,t){r=e,o=t}));return"undefined"!=typeof Promise&&a instanceof Promise?a.then((function(e){return[e,r,o]})):[a,r,o]},"boolean"==typeof(null==e?void 0:e.useProxies)&&this.setUseProxies(e.useProxies),"boolean"==typeof(null==e?void 0:e.autoFreeze)&&this.setAutoFreeze(e.autoFreeze)}var t=e.prototype;return t.createDraft=function(e){hi(e)||fi(8),pi(e)&&(e=$i(e));var t=ji(this),n=Hi(this,e,void 0);return n[ns].C=!0,_i(t),n},t.finishDraft=function(e,t){var n=(e&&e[ns]).A;return Ai(n,t),Ii(void 0,n)},t.setAutoFreeze=function(e){this.D=e},t.setUseProxies=function(e){e&&!Zi&&fi(20),this.O=e},t.applyPatches=function(e,t){var n;for(n=t.length-1;n>=0;n--){var r=t[n];if(0===r.path.length&&"replace"===r.op){e=r.value;break}}n>-1&&(t=t.slice(n+1));var o=Ri("Patches").$;return pi(e)?o(e,t):this.produce(e,(function(e){return o(e,t)}))},e}(),cs=new us,ds=cs.produce;cs.produceWithPatches.bind(cs),cs.setAutoFreeze.bind(cs),cs.setUseProxies.bind(cs),cs.applyPatches.bind(cs),cs.createDraft.bind(cs),cs.finishDraft.bind(cs);const fs=ds;function ps(e){return"Minified Redux error #"+e+"; visit https://redux.js.org/Errors?code="+e+" for the full message or use the non-minified dev environment for full errors. "}var hs="function"===typeof Symbol&&Symbol.observable||"@@observable",gs=function(){return Math.random().toString(36).substring(7).split("").join(".")},ms={INIT:"@@redux/INIT"+gs(),REPLACE:"@@redux/REPLACE"+gs(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+gs()}};function ys(e){if("object"!==typeof e||null===e)return!1;for(var t=e;null!==Object.getPrototypeOf(t);)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function vs(e,t,n){var r;if("function"===typeof t&&"function"===typeof n||"function"===typeof n&&"function"===typeof arguments[3])throw new Error(ps(0));if("function"===typeof t&&"undefined"===typeof n&&(n=t,t=void 0),"undefined"!==typeof n){if("function"!==typeof n)throw new Error(ps(1));return n(vs)(e,t)}if("function"!==typeof e)throw new Error(ps(2));var o=e,a=t,i=[],s=i,l=!1;function u(){s===i&&(s=i.slice())}function c(){if(l)throw new Error(ps(3));return a}function d(e){if("function"!==typeof e)throw new Error(ps(4));if(l)throw new Error(ps(5));var t=!0;return u(),s.push(e),function(){if(t){if(l)throw new Error(ps(6));t=!1,u();var n=s.indexOf(e);s.splice(n,1),i=null}}}function f(e){if(!ys(e))throw new Error(ps(7));if("undefined"===typeof e.type)throw new Error(ps(8));if(l)throw new Error(ps(9));try{l=!0,a=o(a,e)}finally{l=!1}for(var t=i=s,n=0;n<t.length;n++){(0,t[n])()}return e}return f({type:ms.INIT}),(r={dispatch:f,subscribe:d,getState:c,replaceReducer:function(e){if("function"!==typeof e)throw new Error(ps(10));o=e,f({type:ms.REPLACE})}})[hs]=function(){var e,t=d;return(e={subscribe:function(e){if("object"!==typeof e||null===e)throw new Error(ps(11));function n(){e.next&&e.next(c())}return n(),{unsubscribe:t(n)}}})[hs]=function(){return this},e},r}function bs(e){for(var t=Object.keys(e),n={},r=0;r<t.length;r++){var o=t[r];0,"function"===typeof e[o]&&(n[o]=e[o])}var a,i=Object.keys(n);try{!function(e){Object.keys(e).forEach((function(t){var n=e[t];if("undefined"===typeof n(void 0,{type:ms.INIT}))throw new Error(ps(12));if("undefined"===typeof n(void 0,{type:ms.PROBE_UNKNOWN_ACTION()}))throw new Error(ps(13))}))}(n)}catch(af){a=af}return function(e,t){if(void 0===e&&(e={}),a)throw a;for(var r=!1,o={},s=0;s<i.length;s++){var l=i[s],u=n[l],c=e[l],d=u(c,t);if("undefined"===typeof d){t&&t.type;throw new Error(ps(14))}o[l]=d,r=r||d!==c}return(r=r||i.length!==Object.keys(e).length)?o:e}}function ws(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return 0===t.length?function(e){return e}:1===t.length?t[0]:t.reduce((function(e,t){return function(){return e(t.apply(void 0,arguments))}}))}function ks(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e){return function(){var n=e.apply(void 0,arguments),r=function(){throw new Error(ps(15))},o={getState:n.getState,dispatch:function(){return r.apply(void 0,arguments)}},a=t.map((function(e){return e(o)}));return r=ws.apply(void 0,a)(n.dispatch),Wo(Wo({},n),{},{dispatch:r})}}}function Ss(e){return function(t){var n=t.dispatch,r=t.getState;return function(t){return function(o){return"function"===typeof o?o(n,r,e):t(o)}}}}var xs=Ss();xs.withExtraArgument=Ss;const Es=xs;var Cs=function(){var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)Object.prototype.hasOwnProperty.call(t,n)&&(e[n]=t[n])},e(t,n)};return function(t,n){if("function"!==typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}(),Ps=function(e,t){var n,r,o,a,i={label:0,sent:function(){if(1&o[0])throw o[1];return o[1]},trys:[],ops:[]};return a={next:s(0),throw:s(1),return:s(2)},"function"===typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function s(a){return function(s){return function(a){if(n)throw new TypeError("Generator is already executing.");for(;i;)try{if(n=1,r&&(o=2&a[0]?r.return:a[0]?r.throw||((o=r.return)&&o.call(r),0):r.next)&&!(o=o.call(r,a[1])).done)return o;switch(r=0,o&&(a=[2&a[0],o.value]),a[0]){case 0:case 1:o=a;break;case 4:return i.label++,{value:a[1],done:!1};case 5:i.label++,r=a[1],a=[0];continue;case 7:a=i.ops.pop(),i.trys.pop();continue;default:if(!(o=(o=i.trys).length>0&&o[o.length-1])&&(6===a[0]||2===a[0])){i=0;continue}if(3===a[0]&&(!o||a[1]>o[0]&&a[1]<o[3])){i.label=a[1];break}if(6===a[0]&&i.label<o[1]){i.label=o[1],o=a;break}if(o&&i.label<o[2]){i.label=o[2],i.ops.push(a);break}o[2]&&i.ops.pop(),i.trys.pop();continue}a=t.call(e,i)}catch(af){a=[6,af],r=0}finally{n=o=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,s])}}},Os=function(e,t){for(var n=0,r=t.length,o=e.length;n<r;n++,o++)e[o]=t[n];return e},Rs=Object.defineProperty,Ts=Object.defineProperties,Ns=Object.getOwnPropertyDescriptors,As=Object.getOwnPropertySymbols,Ls=Object.prototype.hasOwnProperty,_s=Object.prototype.propertyIsEnumerable,js=function(e,t,n){return t in e?Rs(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},Ds=function(e,t){for(var n in t||(t={}))Ls.call(t,n)&&js(e,n,t[n]);if(As)for(var r=0,o=As(t);r<o.length;r++){n=o[r];_s.call(t,n)&&js(e,n,t[n])}return e},Is=function(e,t){return Ts(e,Ns(t))},Fs=function(e,t,n){return new Promise((function(r,o){var a=function(e){try{s(n.next(e))}catch(af){o(af)}},i=function(e){try{s(n.throw(e))}catch(af){o(af)}},s=function(e){return e.done?r(e.value):Promise.resolve(e.value).then(a,i)};s((n=n.apply(e,t)).next())}))},zs="undefined"!==typeof window&&window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__?window.__REDUX_DEVTOOLS_EXTENSION_COMPOSE__:function(){if(0!==arguments.length)return"object"===typeof arguments[0]?ws:ws.apply(null,arguments)};"undefined"!==typeof window&&window.__REDUX_DEVTOOLS_EXTENSION__&&window.__REDUX_DEVTOOLS_EXTENSION__;function Ms(e){if("object"!==typeof e||null===e)return!1;var t=Object.getPrototypeOf(e);if(null===t)return!0;for(var n=t;null!==Object.getPrototypeOf(n);)n=Object.getPrototypeOf(n);return t===n}function Bs(e,t){function n(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];if(t){var o=t.apply(void 0,n);if(!o)throw new Error("prepareAction did not return an object");return Ds(Ds({type:e,payload:o.payload},"meta"in o&&{meta:o.meta}),"error"in o&&{error:o.error})}return{type:e,payload:n[0]}}return n.toString=function(){return""+e},n.type=e,n.match=function(t){return t.type===e},n}var Us=function(e){function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var o=e.apply(this,n)||this;return Object.setPrototypeOf(o,t.prototype),o}return Cs(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.prototype.concat.apply(this,t)},t.prototype.prepend=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,Os([void 0],e[0].concat(this)))):new(t.bind.apply(t,Os([void 0],e.concat(this))))},t}(Array),Vs=function(e){function t(){for(var n=[],r=0;r<arguments.length;r++)n[r]=arguments[r];var o=e.apply(this,n)||this;return Object.setPrototypeOf(o,t.prototype),o}return Cs(t,e),Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var t=[],n=0;n<arguments.length;n++)t[n]=arguments[n];return e.prototype.concat.apply(this,t)},t.prototype.prepend=function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return 1===e.length&&Array.isArray(e[0])?new(t.bind.apply(t,Os([void 0],e[0].concat(this)))):new(t.bind.apply(t,Os([void 0],e.concat(this))))},t}(Array);function Ws(e){return hi(e)?fs(e,(function(){})):e}function Hs(){return function(e){return function(e){void 0===e&&(e={});var t=e.thunk,n=void 0===t||t,r=(e.immutableCheck,e.serializableCheck,e.actionCreatorCheck,new Us);n&&(!function(e){return"boolean"===typeof e}(n)?r.push(Es.withExtraArgument(n.extraArgument)):r.push(Es));0;return r}(e)}}function $s(e){var t,n={},r=[],o={addCase:function(e,t){var r="string"===typeof e?e:e.type;if(!r)throw new Error("`builder.addCase` cannot be called with an empty action type");if(r in n)throw new Error("`builder.addCase` cannot be called with two reducers for the same action type");return n[r]=t,o},addMatcher:function(e,t){return r.push({matcher:e,reducer:t}),o},addDefaultCase:function(e){return t=e,o}};return e(o),[n,r,t]}function Ks(e){var t=e.name;if(!t)throw new Error("`name` is a required option for createSlice");var n,r="function"==typeof e.initialState?e.initialState:Ws(e.initialState),o=e.reducers||{},a=Object.keys(o),i={},s={},l={};function u(){var t="function"===typeof e.extraReducers?$s(e.extraReducers):[e.extraReducers],n=t[0],o=void 0===n?{}:n,a=t[1],i=void 0===a?[]:a,l=t[2],u=void 0===l?void 0:l,c=Ds(Ds({},o),s);return function(e,t,n,r){void 0===n&&(n=[]);var o,a="function"===typeof t?$s(t):[t,n,r],i=a[0],s=a[1],l=a[2];if(function(e){return"function"===typeof e}(e))o=function(){return Ws(e())};else{var u=Ws(e);o=function(){return u}}function c(e,t){void 0===e&&(e=o());var n=Os([i[t.type]],s.filter((function(e){return(0,e.matcher)(t)})).map((function(e){return e.reducer})));return 0===n.filter((function(e){return!!e})).length&&(n=[l]),n.reduce((function(e,n){if(n){var r;if(pi(e))return void 0===(r=n(e,t))?e:r;if(hi(e))return fs(e,(function(e){return n(e,t)}));if(void 0===(r=n(e,t))){if(null===e)return e;throw Error("A case reducer on a non-draftable value must not return undefined")}return r}return e}),e)}return c.getInitialState=o,c}(r,(function(e){for(var t in c)e.addCase(t,c[t]);for(var n=0,r=i;n<r.length;n++){var o=r[n];e.addMatcher(o.matcher,o.reducer)}u&&e.addDefaultCase(u)}))}return a.forEach((function(e){var n,r,a=o[e],u=function(e,t){return e+"/"+t}(t,e);"reducer"in a?(n=a.reducer,r=a.prepare):n=a,i[e]=n,s[u]=n,l[e]=r?Bs(u,r):Bs(u)})),{name:t,reducer:function(e,t){return n||(n=u()),n(e,t)},actions:l,caseReducers:i,getInitialState:function(){return n||(n=u()),n.getInitialState()}}}var qs=function(e){void 0===e&&(e=21);for(var t="",n=e;n--;)t+="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW"[64*Math.random()|0];return t},Qs=["name","message","stack","code"],Gs=function(e,t){this.payload=e,this.meta=t},Js=function(e,t){this.payload=e,this.meta=t},Xs=function(e){if("object"===typeof e&&null!==e){for(var t={},n=0,r=Qs;n<r.length;n++){var o=r[n];"string"===typeof e[o]&&(t[o]=e[o])}return t}return{message:String(e)}},Ys=function(){function e(e,t,n){var r=Bs(e+"/fulfilled",(function(e,t,n,r){return{payload:e,meta:Is(Ds({},r||{}),{arg:n,requestId:t,requestStatus:"fulfilled"})}})),o=Bs(e+"/pending",(function(e,t,n){return{payload:void 0,meta:Is(Ds({},n||{}),{arg:t,requestId:e,requestStatus:"pending"})}})),a=Bs(e+"/rejected",(function(e,t,r,o,a){return{payload:o,error:(n&&n.serializeError||Xs)(e||"Rejected"),meta:Is(Ds({},a||{}),{arg:r,requestId:t,rejectedWithValue:!!o,requestStatus:"rejected",aborted:"AbortError"===(null==e?void 0:e.name),condition:"ConditionError"===(null==e?void 0:e.name)})}})),i="undefined"!==typeof AbortController?AbortController:function(){function e(){this.signal={aborted:!1,addEventListener:function(){},dispatchEvent:function(){return!1},onabort:function(){},removeEventListener:function(){},reason:void 0,throwIfAborted:function(){}}}return e.prototype.abort=function(){0},e}();return Object.assign((function(e){return function(s,l,u){var c,d=(null==n?void 0:n.idGenerator)?n.idGenerator(e):qs(),f=new i;function p(e){c=e,f.abort()}var h=function(){return Fs(this,null,(function(){var i,h,g,m,y,v;return Ps(this,(function(b){switch(b.label){case 0:return b.trys.push([0,4,,5]),m=null==(i=null==n?void 0:n.condition)?void 0:i.call(n,e,{getState:l,extra:u}),null===(w=m)||"object"!==typeof w||"function"!==typeof w.then?[3,2]:[4,m];case 1:m=b.sent(),b.label=2;case 2:if(!1===m||f.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};return y=new Promise((function(e,t){return f.signal.addEventListener("abort",(function(){return t({name:"AbortError",message:c||"Aborted"})}))})),s(o(d,e,null==(h=null==n?void 0:n.getPendingMeta)?void 0:h.call(n,{requestId:d,arg:e},{getState:l,extra:u}))),[4,Promise.race([y,Promise.resolve(t(e,{dispatch:s,getState:l,extra:u,requestId:d,signal:f.signal,abort:p,rejectWithValue:function(e,t){return new Gs(e,t)},fulfillWithValue:function(e,t){return new Js(e,t)}})).then((function(t){if(t instanceof Gs)throw t;return t instanceof Js?r(t.payload,d,e,t.meta):r(t,d,e)}))])];case 3:return g=b.sent(),[3,5];case 4:return v=b.sent(),g=v instanceof Gs?a(null,d,e,v.payload,v.meta):a(v,d,e),[3,5];case 5:return n&&!n.dispatchConditionRejection&&a.match(g)&&g.meta.condition||s(g),[2,g]}var w}))}))}();return Object.assign(h,{abort:p,requestId:d,arg:e,unwrap:function(){return h.then(Zs)}})}}),{pending:o,rejected:a,fulfilled:r,typePrefix:e})}return e.withTypes=function(){return e},e}();function Zs(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}Object.assign;var el="listenerMiddleware";Bs(el+"/add"),Bs(el+"/removeAll"),Bs(el+"/remove");"function"===typeof queueMicrotask&&queueMicrotask.bind("undefined"!==typeof window?window:"undefined"!==typeof n.g?n.g:globalThis);var tl,nl=function(e){return function(t){setTimeout(t,e)}};"undefined"!==typeof window&&window.requestAnimationFrame?window.requestAnimationFrame:nl(10);function rl(e,t){return function(){return e.apply(t,arguments)}}qi();const{toString:ol}=Object.prototype,{getPrototypeOf:al}=Object,{iterator:il,toStringTag:sl}=Symbol,ll=(ul=Object.create(null),e=>{const t=ol.call(e);return ul[t]||(ul[t]=t.slice(8,-1).toLowerCase())});var ul;const cl=e=>(e=e.toLowerCase(),t=>ll(t)===e),dl=e=>t=>typeof t===e,{isArray:fl}=Array,pl=dl("undefined");const hl=cl("ArrayBuffer");const gl=dl("string"),ml=dl("function"),yl=dl("number"),vl=e=>null!==e&&"object"===typeof e,bl=e=>{if("object"!==ll(e))return!1;const t=al(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(sl in e)&&!(il in e)},wl=cl("Date"),kl=cl("File"),Sl=cl("Blob"),xl=cl("FileList"),El=cl("URLSearchParams"),[Cl,Pl,Ol,Rl]=["ReadableStream","Request","Response","Headers"].map(cl);function Tl(e,t){let n,r,{allOwnKeys:o=!1}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(null!==e&&"undefined"!==typeof e)if("object"!==typeof e&&(e=[e]),fl(e))for(n=0,r=e.length;n<r;n++)t.call(null,e[n],n,e);else{const r=o?Object.getOwnPropertyNames(e):Object.keys(e),a=r.length;let i;for(n=0;n<a;n++)i=r[n],t.call(null,e[i],i,e)}}function Nl(e,t){t=t.toLowerCase();const n=Object.keys(e);let r,o=n.length;for(;o-- >0;)if(r=n[o],t===r.toLowerCase())return r;return null}const Al="undefined"!==typeof globalThis?globalThis:"undefined"!==typeof self?self:"undefined"!==typeof window?window:global,Ll=e=>!pl(e)&&e!==Al;const _l=(jl="undefined"!==typeof Uint8Array&&al(Uint8Array),e=>jl&&e instanceof jl);var jl;const Dl=cl("HTMLFormElement"),Il=(e=>{let{hasOwnProperty:t}=e;return(e,n)=>t.call(e,n)})(Object.prototype),Fl=cl("RegExp"),zl=(e,t)=>{const n=Object.getOwnPropertyDescriptors(e),r={};Tl(n,((n,o)=>{let a;!1!==(a=t(n,o,e))&&(r[o]=a||n)})),Object.defineProperties(e,r)};const Ml=cl("AsyncFunction"),Bl=(Ul="function"===typeof setImmediate,Vl=ml(Al.postMessage),Ul?setImmediate:Vl?((e,t)=>(Al.addEventListener("message",(n=>{let{source:r,data:o}=n;r===Al&&o===e&&t.length&&t.shift()()}),!1),n=>{t.push(n),Al.postMessage(e,"*")}))("axios@".concat(Math.random()),[]):e=>setTimeout(e));var Ul,Vl;const Wl="undefined"!==typeof queueMicrotask?queueMicrotask.bind(Al):"undefined"!==typeof process&&process.nextTick||Bl,Hl={isArray:fl,isArrayBuffer:hl,isBuffer:function(e){return null!==e&&!pl(e)&&null!==e.constructor&&!pl(e.constructor)&&ml(e.constructor.isBuffer)&&e.constructor.isBuffer(e)},isFormData:e=>{let t;return e&&("function"===typeof FormData&&e instanceof FormData||ml(e.append)&&("formdata"===(t=ll(e))||"object"===t&&ml(e.toString)&&"[object FormData]"===e.toString()))},isArrayBufferView:function(e){let t;return t="undefined"!==typeof ArrayBuffer&&ArrayBuffer.isView?ArrayBuffer.isView(e):e&&e.buffer&&hl(e.buffer),t},isString:gl,isNumber:yl,isBoolean:e=>!0===e||!1===e,isObject:vl,isPlainObject:bl,isReadableStream:Cl,isRequest:Pl,isResponse:Ol,isHeaders:Rl,isUndefined:pl,isDate:wl,isFile:kl,isBlob:Sl,isRegExp:Fl,isFunction:ml,isStream:e=>vl(e)&&ml(e.pipe),isURLSearchParams:El,isTypedArray:_l,isFileList:xl,forEach:Tl,merge:function e(){const{caseless:t}=Ll(this)&&this||{},n={},r=(r,o)=>{const a=t&&Nl(n,o)||o;bl(n[a])&&bl(r)?n[a]=e(n[a],r):bl(r)?n[a]=e({},r):fl(r)?n[a]=r.slice():n[a]=r};for(let o=0,a=arguments.length;o<a;o++)arguments[o]&&Tl(arguments[o],r);return n},extend:function(e,t,n){let{allOwnKeys:r}=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{};return Tl(t,((t,r)=>{n&&ml(t)?e[r]=rl(t,n):e[r]=t}),{allOwnKeys:r}),e},trim:e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),stripBOM:e=>(65279===e.charCodeAt(0)&&(e=e.slice(1)),e),inherits:(e,t,n,r)=>{e.prototype=Object.create(t.prototype,r),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),n&&Object.assign(e.prototype,n)},toFlatObject:(e,t,n,r)=>{let o,a,i;const s={};if(t=t||{},null==e)return t;do{for(o=Object.getOwnPropertyNames(e),a=o.length;a-- >0;)i=o[a],r&&!r(i,e,t)||s[i]||(t[i]=e[i],s[i]=!0);e=!1!==n&&al(e)}while(e&&(!n||n(e,t))&&e!==Object.prototype);return t},kindOf:ll,kindOfTest:cl,endsWith:(e,t,n)=>{e=String(e),(void 0===n||n>e.length)&&(n=e.length),n-=t.length;const r=e.indexOf(t,n);return-1!==r&&r===n},toArray:e=>{if(!e)return null;if(fl(e))return e;let t=e.length;if(!yl(t))return null;const n=new Array(t);for(;t-- >0;)n[t]=e[t];return n},forEachEntry:(e,t)=>{const n=(e&&e[il]).call(e);let r;for(;(r=n.next())&&!r.done;){const n=r.value;t.call(e,n[0],n[1])}},matchAll:(e,t)=>{let n;const r=[];for(;null!==(n=e.exec(t));)r.push(n);return r},isHTMLForm:Dl,hasOwnProperty:Il,hasOwnProp:Il,reduceDescriptors:zl,freezeMethods:e=>{zl(e,((t,n)=>{if(ml(e)&&-1!==["arguments","caller","callee"].indexOf(n))return!1;const r=e[n];ml(r)&&(t.enumerable=!1,"writable"in t?t.writable=!1:t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+n+"'")}))}))},toObjectSet:(e,t)=>{const n={},r=e=>{e.forEach((e=>{n[e]=!0}))};return fl(e)?r(e):r(String(e).split(t)),n},toCamelCase:e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,(function(e,t,n){return t.toUpperCase()+n})),noop:()=>{},toFiniteNumber:(e,t)=>null!=e&&Number.isFinite(e=+e)?e:t,findKey:Nl,global:Al,isContextDefined:Ll,isSpecCompliantForm:function(e){return!!(e&&ml(e.append)&&"FormData"===e[sl]&&e[il])},toJSONObject:e=>{const t=new Array(10),n=(e,r)=>{if(vl(e)){if(t.indexOf(e)>=0)return;if(!("toJSON"in e)){t[r]=e;const o=fl(e)?[]:{};return Tl(e,((e,t)=>{const a=n(e,r+1);!pl(a)&&(o[t]=a)})),t[r]=void 0,o}}return e};return n(e,0)},isAsyncFn:Ml,isThenable:e=>e&&(vl(e)||ml(e))&&ml(e.then)&&ml(e.catch),setImmediate:Bl,asap:Wl,isIterable:e=>null!=e&&ml(e[il])};function $l(e,t,n,r,o){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack,this.message=e,this.name="AxiosError",t&&(this.code=t),n&&(this.config=n),r&&(this.request=r),o&&(this.response=o,this.status=o.status?o.status:null)}Hl.inherits($l,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:Hl.toJSONObject(this.config),code:this.code,status:this.status}}});const Kl=$l.prototype,ql={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach((e=>{ql[e]={value:e}})),Object.defineProperties($l,ql),Object.defineProperty(Kl,"isAxiosError",{value:!0}),$l.from=(e,t,n,r,o,a)=>{const i=Object.create(Kl);return Hl.toFlatObject(e,i,(function(e){return e!==Error.prototype}),(e=>"isAxiosError"!==e)),$l.call(i,e.message,t,n,r,o),i.cause=e,i.name=e.name,a&&Object.assign(i,a),i};const Ql=$l;function Gl(e){return Hl.isPlainObject(e)||Hl.isArray(e)}function Jl(e){return Hl.endsWith(e,"[]")?e.slice(0,-2):e}function Xl(e,t,n){return e?e.concat(t).map((function(e,t){return e=Jl(e),!n&&t?"["+e+"]":e})).join(n?".":""):t}const Yl=Hl.toFlatObject(Hl,{},null,(function(e){return/^is[A-Z]/.test(e)}));const Zl=function(e,t,n){if(!Hl.isObject(e))throw new TypeError("target must be an object");t=t||new FormData;const r=(n=Hl.toFlatObject(n,{metaTokens:!0,dots:!1,indexes:!1},!1,(function(e,t){return!Hl.isUndefined(t[e])}))).metaTokens,o=n.visitor||u,a=n.dots,i=n.indexes,s=(n.Blob||"undefined"!==typeof Blob&&Blob)&&Hl.isSpecCompliantForm(t);if(!Hl.isFunction(o))throw new TypeError("visitor must be a function");function l(e){if(null===e)return"";if(Hl.isDate(e))return e.toISOString();if(!s&&Hl.isBlob(e))throw new Ql("Blob is not supported. Use a Buffer instead.");return Hl.isArrayBuffer(e)||Hl.isTypedArray(e)?s&&"function"===typeof Blob?new Blob([e]):Buffer.from(e):e}function u(e,n,o){let s=e;if(e&&!o&&"object"===typeof e)if(Hl.endsWith(n,"{}"))n=r?n:n.slice(0,-2),e=JSON.stringify(e);else if(Hl.isArray(e)&&function(e){return Hl.isArray(e)&&!e.some(Gl)}(e)||(Hl.isFileList(e)||Hl.endsWith(n,"[]"))&&(s=Hl.toArray(e)))return n=Jl(n),s.forEach((function(e,r){!Hl.isUndefined(e)&&null!==e&&t.append(!0===i?Xl([n],r,a):null===i?n:n+"[]",l(e))})),!1;return!!Gl(e)||(t.append(Xl(o,n,a),l(e)),!1)}const c=[],d=Object.assign(Yl,{defaultVisitor:u,convertValue:l,isVisitable:Gl});if(!Hl.isObject(e))throw new TypeError("data must be an object");return function e(n,r){if(!Hl.isUndefined(n)){if(-1!==c.indexOf(n))throw Error("Circular reference detected in "+r.join("."));c.push(n),Hl.forEach(n,(function(n,a){!0===(!(Hl.isUndefined(n)||null===n)&&o.call(t,n,Hl.isString(a)?a.trim():a,r,d))&&e(n,r?r.concat(a):[a])})),c.pop()}}(e),t};function eu(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,(function(e){return t[e]}))}function tu(e,t){this._pairs=[],e&&Zl(e,this,t)}const nu=tu.prototype;nu.append=function(e,t){this._pairs.push([e,t])},nu.toString=function(e){const t=e?function(t){return e.call(this,t,eu)}:eu;return this._pairs.map((function(e){return t(e[0])+"="+t(e[1])}),"").join("&")};const ru=tu;function ou(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function au(e,t,n){if(!t)return e;const r=n&&n.encode||ou;Hl.isFunction(n)&&(n={serialize:n});const o=n&&n.serialize;let a;if(a=o?o(t,n):Hl.isURLSearchParams(t)?t.toString():new ru(t,n).toString(r),a){const t=e.indexOf("#");-1!==t&&(e=e.slice(0,t)),e+=(-1===e.indexOf("?")?"?":"&")+a}return e}const iu=class{constructor(){this.handlers=[]}use(e,t,n){return this.handlers.push({fulfilled:e,rejected:t,synchronous:!!n&&n.synchronous,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){Hl.forEach(this.handlers,(function(t){null!==t&&e(t)}))}},su={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},lu={isBrowser:!0,classes:{URLSearchParams:"undefined"!==typeof URLSearchParams?URLSearchParams:ru,FormData:"undefined"!==typeof FormData?FormData:null,Blob:"undefined"!==typeof Blob?Blob:null},protocols:["http","https","file","blob","url","data"]},uu="undefined"!==typeof window&&"undefined"!==typeof document,cu="object"===typeof navigator&&navigator||void 0,du=uu&&(!cu||["ReactNative","NativeScript","NS"].indexOf(cu.product)<0),fu="undefined"!==typeof WorkerGlobalScope&&self instanceof WorkerGlobalScope&&"function"===typeof self.importScripts,pu=uu&&window.location.href||"http://localhost",hu=Wo(Wo({},e),lu);const gu=function(e){function t(e,n,r,o){let a=e[o++];if("__proto__"===a)return!0;const i=Number.isFinite(+a),s=o>=e.length;if(a=!a&&Hl.isArray(r)?r.length:a,s)return Hl.hasOwnProp(r,a)?r[a]=[r[a],n]:r[a]=n,!i;r[a]&&Hl.isObject(r[a])||(r[a]=[]);return t(e,n,r[a],o)&&Hl.isArray(r[a])&&(r[a]=function(e){const t={},n=Object.keys(e);let r;const o=n.length;let a;for(r=0;r<o;r++)a=n[r],t[a]=e[a];return t}(r[a])),!i}if(Hl.isFormData(e)&&Hl.isFunction(e.entries)){const n={};return Hl.forEachEntry(e,((e,r)=>{t(function(e){return Hl.matchAll(/\w+|\[(\w*)]/g,e).map((e=>"[]"===e[0]?"":e[1]||e[0]))}(e),r,n,0)})),n}return null};const mu={transitional:su,adapter:["xhr","http","fetch"],transformRequest:[function(e,t){const n=t.getContentType()||"",r=n.indexOf("application/json")>-1,o=Hl.isObject(e);o&&Hl.isHTMLForm(e)&&(e=new FormData(e));if(Hl.isFormData(e))return r?JSON.stringify(gu(e)):e;if(Hl.isArrayBuffer(e)||Hl.isBuffer(e)||Hl.isStream(e)||Hl.isFile(e)||Hl.isBlob(e)||Hl.isReadableStream(e))return e;if(Hl.isArrayBufferView(e))return e.buffer;if(Hl.isURLSearchParams(e))return t.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let a;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return function(e,t){return Zl(e,new hu.classes.URLSearchParams,Object.assign({visitor:function(e,t,n,r){return hu.isNode&&Hl.isBuffer(e)?(this.append(t,e.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}(e,this.formSerializer).toString();if((a=Hl.isFileList(e))||n.indexOf("multipart/form-data")>-1){const t=this.env&&this.env.FormData;return Zl(a?{"files[]":e}:e,t&&new t,this.formSerializer)}}return o||r?(t.setContentType("application/json",!1),function(e,t,n){if(Hl.isString(e))try{return(t||JSON.parse)(e),Hl.trim(e)}catch(af){if("SyntaxError"!==af.name)throw af}return(n||JSON.stringify)(e)}(e)):e}],transformResponse:[function(e){const t=this.transitional||mu.transitional,n=t&&t.forcedJSONParsing,r="json"===this.responseType;if(Hl.isResponse(e)||Hl.isReadableStream(e))return e;if(e&&Hl.isString(e)&&(n&&!this.responseType||r)){const n=!(t&&t.silentJSONParsing)&&r;try{return JSON.parse(e)}catch(af){if(n){if("SyntaxError"===af.name)throw Ql.from(af,Ql.ERR_BAD_RESPONSE,this,null,this.response);throw af}}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:hu.classes.FormData,Blob:hu.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};Hl.forEach(["delete","get","head","post","put","patch"],(e=>{mu.headers[e]={}}));const yu=mu,vu=Hl.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),bu=Symbol("internals");function wu(e){return e&&String(e).trim().toLowerCase()}function ku(e){return!1===e||null==e?e:Hl.isArray(e)?e.map(ku):String(e)}function Su(e,t,n,r,o){return Hl.isFunction(r)?r.call(this,t,n):(o&&(t=n),Hl.isString(t)?Hl.isString(r)?-1!==t.indexOf(r):Hl.isRegExp(r)?r.test(t):void 0:void 0)}class xu{constructor(e){e&&this.set(e)}set(e,t,n){const r=this;function o(e,t,n){const o=wu(t);if(!o)throw new Error("header name must be a non-empty string");const a=Hl.findKey(r,o);(!a||void 0===r[a]||!0===n||void 0===n&&!1!==r[a])&&(r[a||t]=ku(e))}const a=(e,t)=>Hl.forEach(e,((e,n)=>o(e,n,t)));if(Hl.isPlainObject(e)||e instanceof this.constructor)a(e,t);else if(Hl.isString(e)&&(e=e.trim())&&!/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim()))a((e=>{const t={};let n,r,o;return e&&e.split("\n").forEach((function(e){o=e.indexOf(":"),n=e.substring(0,o).trim().toLowerCase(),r=e.substring(o+1).trim(),!n||t[n]&&vu[n]||("set-cookie"===n?t[n]?t[n].push(r):t[n]=[r]:t[n]=t[n]?t[n]+", "+r:r)})),t})(e),t);else if(Hl.isObject(e)&&Hl.isIterable(e)){let n,r,o={};for(const t of e){if(!Hl.isArray(t))throw TypeError("Object iterator must return a key-value pair");o[r=t[0]]=(n=o[r])?Hl.isArray(n)?[...n,t[1]]:[n,t[1]]:t[1]}a(o,t)}else null!=e&&o(t,e,n);return this}get(e,t){if(e=wu(e)){const n=Hl.findKey(this,e);if(n){const e=this[n];if(!t)return e;if(!0===t)return function(e){const t=Object.create(null),n=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let r;for(;r=n.exec(e);)t[r[1]]=r[2];return t}(e);if(Hl.isFunction(t))return t.call(this,e,n);if(Hl.isRegExp(t))return t.exec(e);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,t){if(e=wu(e)){const n=Hl.findKey(this,e);return!(!n||void 0===this[n]||t&&!Su(0,this[n],n,t))}return!1}delete(e,t){const n=this;let r=!1;function o(e){if(e=wu(e)){const o=Hl.findKey(n,e);!o||t&&!Su(0,n[o],o,t)||(delete n[o],r=!0)}}return Hl.isArray(e)?e.forEach(o):o(e),r}clear(e){const t=Object.keys(this);let n=t.length,r=!1;for(;n--;){const o=t[n];e&&!Su(0,this[o],o,e,!0)||(delete this[o],r=!0)}return r}normalize(e){const t=this,n={};return Hl.forEach(this,((r,o)=>{const a=Hl.findKey(n,o);if(a)return t[a]=ku(r),void delete t[o];const i=e?function(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,((e,t,n)=>t.toUpperCase()+n))}(o):String(o).trim();i!==o&&delete t[o],t[i]=ku(r),n[i]=!0})),this}concat(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.constructor.concat(this,...t)}toJSON(e){const t=Object.create(null);return Hl.forEach(this,((n,r)=>{null!=n&&!1!==n&&(t[r]=e&&Hl.isArray(n)?n.join(", "):n)})),t}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map((e=>{let[t,n]=e;return t+": "+n})).join("\n")}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e){const t=new this(e);for(var n=arguments.length,r=new Array(n>1?n-1:0),o=1;o<n;o++)r[o-1]=arguments[o];return r.forEach((e=>t.set(e))),t}static accessor(e){const t=(this[bu]=this[bu]={accessors:{}}).accessors,n=this.prototype;function r(e){const r=wu(e);t[r]||(!function(e,t){const n=Hl.toCamelCase(" "+t);["get","set","has"].forEach((r=>{Object.defineProperty(e,r+n,{value:function(e,n,o){return this[r].call(this,t,e,n,o)},configurable:!0})}))}(n,e),t[r]=!0)}return Hl.isArray(e)?e.forEach(r):r(e),this}}xu.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]),Hl.reduceDescriptors(xu.prototype,((e,t)=>{let{value:n}=e,r=t[0].toUpperCase()+t.slice(1);return{get:()=>n,set(e){this[r]=e}}})),Hl.freezeMethods(xu);const Eu=xu;function Cu(e,t){const n=this||yu,r=t||n,o=Eu.from(r.headers);let a=r.data;return Hl.forEach(e,(function(e){a=e.call(n,a,o.normalize(),t?t.status:void 0)})),o.normalize(),a}function Pu(e){return!(!e||!e.__CANCEL__)}function Ou(e,t,n){Ql.call(this,null==e?"canceled":e,Ql.ERR_CANCELED,t,n),this.name="CanceledError"}Hl.inherits(Ou,Ql,{__CANCEL__:!0});const Ru=Ou;function Tu(e,t,n){const r=n.config.validateStatus;n.status&&r&&!r(n.status)?t(new Ql("Request failed with status code "+n.status,[Ql.ERR_BAD_REQUEST,Ql.ERR_BAD_RESPONSE][Math.floor(n.status/100)-4],n.config,n.request,n)):e(n)}const Nu=function(e,t){e=e||10;const n=new Array(e),r=new Array(e);let o,a=0,i=0;return t=void 0!==t?t:1e3,function(s){const l=Date.now(),u=r[i];o||(o=l),n[a]=s,r[a]=l;let c=i,d=0;for(;c!==a;)d+=n[c++],c%=e;if(a=(a+1)%e,a===i&&(i=(i+1)%e),l-o<t)return;const f=u&&l-u;return f?Math.round(1e3*d/f):void 0}};const Au=function(e,t){let n,r,o=0,a=1e3/t;const i=function(t){let a=arguments.length>1&&void 0!==arguments[1]?arguments[1]:Date.now();o=a,n=null,r&&(clearTimeout(r),r=null),e.apply(null,t)};return[function(){const e=Date.now(),t=e-o;for(var s=arguments.length,l=new Array(s),u=0;u<s;u++)l[u]=arguments[u];t>=a?i(l,e):(n=l,r||(r=setTimeout((()=>{r=null,i(n)}),a-t)))},()=>n&&i(n)]},Lu=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:3,r=0;const o=Nu(50,250);return Au((n=>{const a=n.loaded,i=n.lengthComputable?n.total:void 0,s=a-r,l=o(s);r=a;e({loaded:a,total:i,progress:i?a/i:void 0,bytes:s,rate:l||void 0,estimated:l&&i&&a<=i?(i-a)/l:void 0,event:n,lengthComputable:null!=i,[t?"download":"upload"]:!0})}),n)},_u=(e,t)=>{const n=null!=e;return[r=>t[0]({lengthComputable:n,total:e,loaded:r}),t[1]]},ju=e=>function(){for(var t=arguments.length,n=new Array(t),r=0;r<t;r++)n[r]=arguments[r];return Hl.asap((()=>e(...n)))},Du=hu.hasStandardBrowserEnv?((e,t)=>n=>(n=new URL(n,hu.origin),e.protocol===n.protocol&&e.host===n.host&&(t||e.port===n.port)))(new URL(hu.origin),hu.navigator&&/(msie|trident)/i.test(hu.navigator.userAgent)):()=>!0,Iu=hu.hasStandardBrowserEnv?{write(e,t,n,r,o,a){const i=[e+"="+encodeURIComponent(t)];Hl.isNumber(n)&&i.push("expires="+new Date(n).toGMTString()),Hl.isString(r)&&i.push("path="+r),Hl.isString(o)&&i.push("domain="+o),!0===a&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read:()=>null,remove(){}};function Fu(e,t,n){let r=!/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t);return e&&(r||0==n)?function(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}(e,t):t}const zu=e=>e instanceof Eu?Wo({},e):e;function Mu(e,t){t=t||{};const n={};function r(e,t,n,r){return Hl.isPlainObject(e)&&Hl.isPlainObject(t)?Hl.merge.call({caseless:r},e,t):Hl.isPlainObject(t)?Hl.merge({},t):Hl.isArray(t)?t.slice():t}function o(e,t,n,o){return Hl.isUndefined(t)?Hl.isUndefined(e)?void 0:r(void 0,e,0,o):r(e,t,0,o)}function a(e,t){if(!Hl.isUndefined(t))return r(void 0,t)}function i(e,t){return Hl.isUndefined(t)?Hl.isUndefined(e)?void 0:r(void 0,e):r(void 0,t)}function s(n,o,a){return a in t?r(n,o):a in e?r(void 0,n):void 0}const l={url:a,method:a,data:a,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:s,headers:(e,t,n)=>o(zu(e),zu(t),0,!0)};return Hl.forEach(Object.keys(Object.assign({},e,t)),(function(r){const a=l[r]||o,i=a(e[r],t[r],r);Hl.isUndefined(i)&&a!==s||(n[r]=i)})),n}const Bu=e=>{const t=Mu({},e);let n,{data:r,withXSRFToken:o,xsrfHeaderName:a,xsrfCookieName:i,headers:s,auth:l}=t;if(t.headers=s=Eu.from(s),t.url=au(Fu(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),l&&s.set("Authorization","Basic "+btoa((l.username||"")+":"+(l.password?unescape(encodeURIComponent(l.password)):""))),Hl.isFormData(r))if(hu.hasStandardBrowserEnv||hu.hasStandardBrowserWebWorkerEnv)s.setContentType(void 0);else if(!1!==(n=s.getContentType())){const[e,...t]=n?n.split(";").map((e=>e.trim())).filter(Boolean):[];s.setContentType([e||"multipart/form-data",...t].join("; "))}if(hu.hasStandardBrowserEnv&&(o&&Hl.isFunction(o)&&(o=o(t)),o||!1!==o&&Du(t.url))){const e=a&&i&&Iu.read(i);e&&s.set(a,e)}return t},Uu="undefined"!==typeof XMLHttpRequest&&function(e){return new Promise((function(t,n){const r=Bu(e);let o=r.data;const a=Eu.from(r.headers).normalize();let i,s,l,u,c,{responseType:d,onUploadProgress:f,onDownloadProgress:p}=r;function h(){u&&u(),c&&c(),r.cancelToken&&r.cancelToken.unsubscribe(i),r.signal&&r.signal.removeEventListener("abort",i)}let g=new XMLHttpRequest;function m(){if(!g)return;const r=Eu.from("getAllResponseHeaders"in g&&g.getAllResponseHeaders());Tu((function(e){t(e),h()}),(function(e){n(e),h()}),{data:d&&"text"!==d&&"json"!==d?g.response:g.responseText,status:g.status,statusText:g.statusText,headers:r,config:e,request:g}),g=null}g.open(r.method.toUpperCase(),r.url,!0),g.timeout=r.timeout,"onloadend"in g?g.onloadend=m:g.onreadystatechange=function(){g&&4===g.readyState&&(0!==g.status||g.responseURL&&0===g.responseURL.indexOf("file:"))&&setTimeout(m)},g.onabort=function(){g&&(n(new Ql("Request aborted",Ql.ECONNABORTED,e,g)),g=null)},g.onerror=function(){n(new Ql("Network Error",Ql.ERR_NETWORK,e,g)),g=null},g.ontimeout=function(){let t=r.timeout?"timeout of "+r.timeout+"ms exceeded":"timeout exceeded";const o=r.transitional||su;r.timeoutErrorMessage&&(t=r.timeoutErrorMessage),n(new Ql(t,o.clarifyTimeoutError?Ql.ETIMEDOUT:Ql.ECONNABORTED,e,g)),g=null},void 0===o&&a.setContentType(null),"setRequestHeader"in g&&Hl.forEach(a.toJSON(),(function(e,t){g.setRequestHeader(t,e)})),Hl.isUndefined(r.withCredentials)||(g.withCredentials=!!r.withCredentials),d&&"json"!==d&&(g.responseType=r.responseType),p&&([l,c]=Lu(p,!0),g.addEventListener("progress",l)),f&&g.upload&&([s,u]=Lu(f),g.upload.addEventListener("progress",s),g.upload.addEventListener("loadend",u)),(r.cancelToken||r.signal)&&(i=t=>{g&&(n(!t||t.type?new Ru(null,e,g):t),g.abort(),g=null)},r.cancelToken&&r.cancelToken.subscribe(i),r.signal&&(r.signal.aborted?i():r.signal.addEventListener("abort",i)));const y=function(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}(r.url);y&&-1===hu.protocols.indexOf(y)?n(new Ql("Unsupported protocol "+y+":",Ql.ERR_BAD_REQUEST,e)):g.send(o||null)}))},Vu=(e,t)=>{const{length:n}=e=e?e.filter(Boolean):[];if(t||n){let n,r=new AbortController;const o=function(e){if(!n){n=!0,i();const t=e instanceof Error?e:this.reason;r.abort(t instanceof Ql?t:new Ru(t instanceof Error?t.message:t))}};let a=t&&setTimeout((()=>{a=null,o(new Ql("timeout ".concat(t," of ms exceeded"),Ql.ETIMEDOUT))}),t);const i=()=>{e&&(a&&clearTimeout(a),a=null,e.forEach((e=>{e.unsubscribe?e.unsubscribe(o):e.removeEventListener("abort",o)})),e=null)};e.forEach((e=>e.addEventListener("abort",o)));const{signal:s}=r;return s.unsubscribe=()=>Hl.asap(i),s}};function Wu(e,t){this.v=e,this.k=t}function Hu(e){return function(){return new $u(e.apply(this,arguments))}}function $u(e){var t,n;function r(t,n){try{var a=e[t](n),i=a.value,s=i instanceof Wu;Promise.resolve(s?i.v:i).then((function(n){if(s){var l="return"===t?"return":"next";if(!i.k||n.done)return r(l,n);n=e[l](n).value}o(a.done?"return":"normal",n)}),(function(e){r("throw",e)}))}catch(e){o("throw",e)}}function o(e,o){switch(e){case"return":t.resolve({value:o,done:!0});break;case"throw":t.reject(o);break;default:t.resolve({value:o,done:!1})}(t=t.next)?r(t.key,t.arg):n=null}this._invoke=function(e,o){return new Promise((function(a,i){var s={key:e,arg:o,resolve:a,reject:i,next:null};n?n=n.next=s:(t=n=s,r(e,o))}))},"function"!=typeof e.return&&(this.return=void 0)}function Ku(e){return new Wu(e,0)}function qu(e){var t={},n=!1;function r(t,r){return n=!0,r=new Promise((function(n){n(e[t](r))})),{done:!1,value:new Wu(r,1)}}return t["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},t.next=function(e){return n?(n=!1,e):r("next",e)},"function"==typeof e.throw&&(t.throw=function(e){if(n)throw n=!1,e;return r("throw",e)}),"function"==typeof e.return&&(t.return=function(e){return n?(n=!1,e):r("return",e)}),t}function Qu(e){var t,n,r,o=2;for("undefined"!=typeof Symbol&&(n=Symbol.asyncIterator,r=Symbol.iterator);o--;){if(n&&null!=(t=e[n]))return t.call(e);if(r&&null!=(t=e[r]))return new Gu(t.call(e));n="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function Gu(e){function t(e){if(Object(e)!==e)return Promise.reject(new TypeError(e+" is not an object."));var t=e.done;return Promise.resolve(e.value).then((function(e){return{value:e,done:t}}))}return Gu=function(e){this.s=e,this.n=e.next},Gu.prototype={s:null,n:null,next:function(){return t(this.n.apply(this.s,arguments))},return:function(e){var n=this.s.return;return void 0===n?Promise.resolve({value:e,done:!0}):t(n.apply(this.s,arguments))},throw:function(e){var n=this.s.return;return void 0===n?Promise.reject(e):t(n.apply(this.s,arguments))}},new Gu(e)}$u.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},$u.prototype.next=function(e){return this._invoke("next",e)},$u.prototype.throw=function(e){return this._invoke("throw",e)},$u.prototype.return=function(e){return this._invoke("return",e)};const Ju=function*(e,t){let n=e.byteLength;if(!t||n<t)return void(yield e);let r,o=0;for(;o<n;)r=o+t,yield e.slice(o,r),o=r},Xu=function(){var e=Hu((function*(e,t){var n,r=!1,o=!1;try{for(var a,i=Qu(Yu(e));r=!(a=yield Ku(i.next())).done;r=!1){const e=a.value;yield*qu(Qu(Ju(e,t)))}}catch(s){o=!0,n=s}finally{try{r&&null!=i.return&&(yield Ku(i.return()))}finally{if(o)throw n}}}));return function(t,n){return e.apply(this,arguments)}}(),Yu=function(){var e=Hu((function*(e){if(e[Symbol.asyncIterator])return void(yield*qu(Qu(e)));const t=e.getReader();try{for(;;){const{done:e,value:n}=yield Ku(t.read());if(e)break;yield n}}finally{yield Ku(t.cancel())}}));return function(t){return e.apply(this,arguments)}}(),Zu=(e,t,n,r)=>{const o=Xu(e,t);let a,i=0,s=e=>{a||(a=!0,r&&r(e))};return new ReadableStream({async pull(e){try{const{done:t,value:r}=await o.next();if(t)return s(),void e.close();let a=r.byteLength;if(n){let e=i+=a;n(e)}e.enqueue(new Uint8Array(r))}catch(t){throw s(t),t}},cancel:e=>(s(e),o.return())},{highWaterMark:2})},ec="function"===typeof fetch&&"function"===typeof Request&&"function"===typeof Response,tc=ec&&"function"===typeof ReadableStream,nc=ec&&("function"===typeof TextEncoder?(rc=new TextEncoder,e=>rc.encode(e)):async e=>new Uint8Array(await new Response(e).arrayBuffer()));var rc;const oc=function(e){try{for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return!!e(...n)}catch(af){return!1}},ac=tc&&oc((()=>{let e=!1;const t=new Request(hu.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t})),ic=tc&&oc((()=>Hl.isReadableStream(new Response("").body))),sc={stream:ic&&(e=>e.body)};var lc;ec&&(lc=new Response,["text","arrayBuffer","blob","formData","stream"].forEach((e=>{!sc[e]&&(sc[e]=Hl.isFunction(lc[e])?t=>t[e]():(t,n)=>{throw new Ql("Response type '".concat(e,"' is not supported"),Ql.ERR_NOT_SUPPORT,n)})})));const uc=async(e,t)=>{const n=Hl.toFiniteNumber(e.getContentLength());return null==n?(async e=>{if(null==e)return 0;if(Hl.isBlob(e))return e.size;if(Hl.isSpecCompliantForm(e)){const t=new Request(hu.origin,{method:"POST",body:e});return(await t.arrayBuffer()).byteLength}return Hl.isArrayBufferView(e)||Hl.isArrayBuffer(e)?e.byteLength:(Hl.isURLSearchParams(e)&&(e+=""),Hl.isString(e)?(await nc(e)).byteLength:void 0)})(t):n},cc=ec&&(async e=>{let{url:t,method:n,data:r,signal:o,cancelToken:a,timeout:i,onDownloadProgress:s,onUploadProgress:l,responseType:u,headers:c,withCredentials:d="same-origin",fetchOptions:f}=Bu(e);u=u?(u+"").toLowerCase():"text";let p,h=Vu([o,a&&a.toAbortSignal()],i);const g=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let m;try{if(l&&ac&&"get"!==n&&"head"!==n&&0!==(m=await uc(c,r))){let e,n=new Request(t,{method:"POST",body:r,duplex:"half"});if(Hl.isFormData(r)&&(e=n.headers.get("content-type"))&&c.setContentType(e),n.body){const[e,t]=_u(m,Lu(ju(l)));r=Zu(n.body,65536,e,t)}}Hl.isString(d)||(d=d?"include":"omit");const o="credentials"in Request.prototype;p=new Request(t,Wo(Wo({},f),{},{signal:h,method:n.toUpperCase(),headers:c.normalize().toJSON(),body:r,duplex:"half",credentials:o?d:void 0}));let a=await fetch(p);const i=ic&&("stream"===u||"response"===u);if(ic&&(s||i&&g)){const e={};["status","statusText","headers"].forEach((t=>{e[t]=a[t]}));const t=Hl.toFiniteNumber(a.headers.get("content-length")),[n,r]=s&&_u(t,Lu(ju(s),!0))||[];a=new Response(Zu(a.body,65536,n,(()=>{r&&r(),g&&g()})),e)}u=u||"text";let y=await sc[Hl.findKey(sc,u)||"text"](a,e);return!i&&g&&g(),await new Promise(((t,n)=>{Tu(t,n,{data:y,headers:Eu.from(a.headers),status:a.status,statusText:a.statusText,config:e,request:p})}))}catch(y){if(g&&g(),y&&"TypeError"===y.name&&/Load failed|fetch/i.test(y.message))throw Object.assign(new Ql("Network Error",Ql.ERR_NETWORK,e,p),{cause:y.cause||y});throw Ql.from(y,y&&y.code,e,p)}}),dc={http:null,xhr:Uu,fetch:cc};Hl.forEach(dc,((e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch(af){}Object.defineProperty(e,"adapterName",{value:t})}}));const fc=e=>"- ".concat(e),pc=e=>Hl.isFunction(e)||null===e||!1===e,hc=e=>{e=Hl.isArray(e)?e:[e];const{length:t}=e;let n,r;const o={};for(let a=0;a<t;a++){let t;if(n=e[a],r=n,!pc(n)&&(r=dc[(t=String(n)).toLowerCase()],void 0===r))throw new Ql("Unknown adapter '".concat(t,"'"));if(r)break;o[t||"#"+a]=r}if(!r){const e=Object.entries(o).map((e=>{let[t,n]=e;return"adapter ".concat(t," ")+(!1===n?"is not supported by the environment":"is not available in the build")}));let n=t?e.length>1?"since :\n"+e.map(fc).join("\n"):" "+fc(e[0]):"as no adapter specified";throw new Ql("There is no suitable adapter to dispatch the request "+n,"ERR_NOT_SUPPORT")}return r};function gc(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Ru(null,e)}function mc(e){gc(e),e.headers=Eu.from(e.headers),e.data=Cu.call(e,e.transformRequest),-1!==["post","put","patch"].indexOf(e.method)&&e.headers.setContentType("application/x-www-form-urlencoded",!1);return hc(e.adapter||yu.adapter)(e).then((function(t){return gc(e),t.data=Cu.call(e,e.transformResponse,t),t.headers=Eu.from(t.headers),t}),(function(t){return Pu(t)||(gc(e),t&&t.response&&(t.response.data=Cu.call(e,e.transformResponse,t.response),t.response.headers=Eu.from(t.response.headers))),Promise.reject(t)}))}const yc="1.9.0",vc={};["object","boolean","number","function","string","symbol"].forEach(((e,t)=>{vc[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}}));const bc={};vc.transitional=function(e,t,n){function r(e,t){return"[Axios v1.9.0] Transitional option '"+e+"'"+t+(n?". "+n:"")}return(n,o,a)=>{if(!1===e)throw new Ql(r(o," has been removed"+(t?" in "+t:"")),Ql.ERR_DEPRECATED);return t&&!bc[o]&&(bc[o]=!0,console.warn(r(o," has been deprecated since v"+t+" and will be removed in the near future"))),!e||e(n,o,a)}},vc.spelling=function(e){return(t,n)=>(console.warn("".concat(n," is likely a misspelling of ").concat(e)),!0)};const wc={assertOptions:function(e,t,n){if("object"!==typeof e)throw new Ql("options must be an object",Ql.ERR_BAD_OPTION_VALUE);const r=Object.keys(e);let o=r.length;for(;o-- >0;){const a=r[o],i=t[a];if(i){const t=e[a],n=void 0===t||i(t,a,e);if(!0!==n)throw new Ql("option "+a+" must be "+n,Ql.ERR_BAD_OPTION_VALUE)}else if(!0!==n)throw new Ql("Unknown option "+a,Ql.ERR_BAD_OPTION)}},validators:vc},kc=wc.validators;class Sc{constructor(e){this.defaults=e||{},this.interceptors={request:new iu,response:new iu}}async request(e,t){try{return await this._request(e,t)}catch(n){if(n instanceof Error){let e={};Error.captureStackTrace?Error.captureStackTrace(e):e=new Error;const t=e.stack?e.stack.replace(/^.+\n/,""):"";try{n.stack?t&&!String(n.stack).endsWith(t.replace(/^.+\n.+\n/,""))&&(n.stack+="\n"+t):n.stack=t}catch(af){}}throw n}}_request(e,t){"string"===typeof e?(t=t||{}).url=e:t=e||{},t=Mu(this.defaults,t);const{transitional:n,paramsSerializer:r,headers:o}=t;void 0!==n&&wc.assertOptions(n,{silentJSONParsing:kc.transitional(kc.boolean),forcedJSONParsing:kc.transitional(kc.boolean),clarifyTimeoutError:kc.transitional(kc.boolean)},!1),null!=r&&(Hl.isFunction(r)?t.paramsSerializer={serialize:r}:wc.assertOptions(r,{encode:kc.function,serialize:kc.function},!0)),void 0!==t.allowAbsoluteUrls||(void 0!==this.defaults.allowAbsoluteUrls?t.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:t.allowAbsoluteUrls=!0),wc.assertOptions(t,{baseUrl:kc.spelling("baseURL"),withXsrfToken:kc.spelling("withXSRFToken")},!0),t.method=(t.method||this.defaults.method||"get").toLowerCase();let a=o&&Hl.merge(o.common,o[t.method]);o&&Hl.forEach(["delete","get","head","post","put","patch","common"],(e=>{delete o[e]})),t.headers=Eu.concat(a,o);const i=[];let s=!0;this.interceptors.request.forEach((function(e){"function"===typeof e.runWhen&&!1===e.runWhen(t)||(s=s&&e.synchronous,i.unshift(e.fulfilled,e.rejected))}));const l=[];let u;this.interceptors.response.forEach((function(e){l.push(e.fulfilled,e.rejected)}));let c,d=0;if(!s){const e=[mc.bind(this),void 0];for(e.unshift.apply(e,i),e.push.apply(e,l),c=e.length,u=Promise.resolve(t);d<c;)u=u.then(e[d++],e[d++]);return u}c=i.length;let f=t;for(d=0;d<c;){const e=i[d++],t=i[d++];try{f=e(f)}catch(p){t.call(this,p);break}}try{u=mc.call(this,f)}catch(p){return Promise.reject(p)}for(d=0,c=l.length;d<c;)u=u.then(l[d++],l[d++]);return u}getUri(e){return au(Fu((e=Mu(this.defaults,e)).baseURL,e.url,e.allowAbsoluteUrls),e.params,e.paramsSerializer)}}Hl.forEach(["delete","get","head","options"],(function(e){Sc.prototype[e]=function(t,n){return this.request(Mu(n||{},{method:e,url:t,data:(n||{}).data}))}})),Hl.forEach(["post","put","patch"],(function(e){function t(t){return function(n,r,o){return this.request(Mu(o||{},{method:e,headers:t?{"Content-Type":"multipart/form-data"}:{},url:n,data:r}))}}Sc.prototype[e]=t(),Sc.prototype[e+"Form"]=t(!0)}));const xc=Sc;class Ec{constructor(e){if("function"!==typeof e)throw new TypeError("executor must be a function.");let t;this.promise=new Promise((function(e){t=e}));const n=this;this.promise.then((e=>{if(!n._listeners)return;let t=n._listeners.length;for(;t-- >0;)n._listeners[t](e);n._listeners=null})),this.promise.then=e=>{let t;const r=new Promise((e=>{n.subscribe(e),t=e})).then(e);return r.cancel=function(){n.unsubscribe(t)},r},e((function(e,r,o){n.reason||(n.reason=new Ru(e,r,o),t(n.reason))}))}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){this.reason?e(this.reason):this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;const t=this._listeners.indexOf(e);-1!==t&&this._listeners.splice(t,1)}toAbortSignal(){const e=new AbortController,t=t=>{e.abort(t)};return this.subscribe(t),e.signal.unsubscribe=()=>this.unsubscribe(t),e.signal}static source(){let e;const t=new Ec((function(t){e=t}));return{token:t,cancel:e}}}const Cc=Ec;const Pc={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Pc).forEach((e=>{let[t,n]=e;Pc[n]=t}));const Oc=Pc;const Rc=function e(t){const n=new xc(t),r=rl(xc.prototype.request,n);return Hl.extend(r,xc.prototype,n,{allOwnKeys:!0}),Hl.extend(r,n,null,{allOwnKeys:!0}),r.create=function(n){return e(Mu(t,n))},r}(yu);Rc.Axios=xc,Rc.CanceledError=Ru,Rc.CancelToken=Cc,Rc.isCancel=Pu,Rc.VERSION=yc,Rc.toFormData=Zl,Rc.AxiosError=Ql,Rc.Cancel=Rc.CanceledError,Rc.all=function(e){return Promise.all(e)},Rc.spread=function(e){return function(t){return e.apply(null,t)}},Rc.isAxiosError=function(e){return Hl.isObject(e)&&!0===e.isAxiosError},Rc.mergeConfig=Mu,Rc.AxiosHeaders=Eu,Rc.formToJSON=e=>gu(Hl.isHTMLForm(e)?new FormData(e):e),Rc.getAdapter=hc,Rc.HttpStatusCode=Oc,Rc.default=Rc;const Tc=Rc,Nc={NODE_ENV:"production",PUBLIC_URL:"",WDS_SOCKET_HOST:void 0,WDS_SOCKET_PATH:void 0,WDS_SOCKET_PORT:void 0,FAST_REFRESH:!0}.REACT_APP_API_URL||"http://localhost:3001/api",Ac=Tc.create({baseURL:Nc,headers:{"Content-Type":"application/json"}});Ac.interceptors.request.use((e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization="Bearer ".concat(t)),e}),(e=>Promise.reject(e))),Ac.interceptors.response.use((e=>e),(async e=>{var t;const n=e.config;if(401===(null===(t=e.response)||void 0===t?void 0:t.status)&&!n._retry){n._retry=!0;try{const e=localStorage.getItem("refreshToken");if(e){const t=await Tc.post("".concat(Nc,"/auth/refresh-token"),{refreshToken:e}),{token:r,refreshToken:o}=t.data.data;return localStorage.setItem("token",r),localStorage.setItem("refreshToken",o),n.headers.Authorization="Bearer ".concat(r),Ac(n)}}catch(r){return localStorage.removeItem("token"),localStorage.removeItem("refreshToken"),window.location.href="/login",Promise.reject(r)}}return Promise.reject(e)}));const Lc=async e=>(await Ac.post("/auth/login",e)).data,_c=async e=>(await Ac.post("/auth/register",e)).data,jc=async()=>(await Ac.get("/auth/me")).data,Dc=async e=>(await Ac.post("/auth/refresh-token",{refreshToken:e})).data,Ic=async e=>(await Ac.post("/auth/logout",{refreshToken:e})).data,Fc=async e=>(await Ac.post("/auth/change-password",e)).data,zc={user:null,token:localStorage.getItem("token"),refreshToken:localStorage.getItem("refreshToken"),isAuthenticated:!!localStorage.getItem("token"),isLoading:!1,error:null},Mc=Ys("auth/login",(async(e,t)=>{let{rejectWithValue:n}=t;try{const t=await Lc(e);return localStorage.setItem("token",t.data.token),localStorage.setItem("refreshToken",t.data.refreshToken),t.data}catch(a){var r,o;return n((null===(r=a.response)||void 0===r||null===(o=r.data)||void 0===o?void 0:o.message)||"Login failed")}})),Bc=Ys("auth/register",(async(e,t)=>{let{rejectWithValue:n}=t;try{const t=await _c(e);return localStorage.setItem("token",t.data.token),localStorage.setItem("refreshToken",t.data.refreshToken),t.data}catch(a){var r,o;return n((null===(r=a.response)||void 0===r||null===(o=r.data)||void 0===o?void 0:o.message)||"Registration failed")}})),Uc=Ys("auth/getCurrentUser",(async(e,t)=>{let{rejectWithValue:n}=t;try{return(await jc()).data}catch(a){var r,o;return n((null===(r=a.response)||void 0===r||null===(o=r.data)||void 0===o?void 0:o.message)||"Failed to get user")}})),Vc=Ys("auth/refreshToken",(async(e,t)=>{let{getState:n,rejectWithValue:r}=t;try{const e=n().auth.refreshToken;if(!e)throw new Error("No refresh token available");const t=await Dc(e);return localStorage.setItem("token",t.data.token),localStorage.setItem("refreshToken",t.data.refreshToken),t.data}catch(i){var o,a;return r((null===(o=i.response)||void 0===o||null===(a=o.data)||void 0===a?void 0:a.message)||"Token refresh failed")}})),Wc=Ys("auth/logout",(async(e,t)=>{let{getState:n,rejectWithValue:r}=t;try{const e=n().auth.refreshToken;return e&&await Ic(e),localStorage.removeItem("token"),localStorage.removeItem("refreshToken"),null}catch(o){return localStorage.removeItem("token"),localStorage.removeItem("refreshToken"),null}})),Hc=Ys("auth/changePassword",(async(e,t)=>{let{rejectWithValue:n}=t;try{return await Fc(e)}catch(a){var r,o;return n((null===(r=a.response)||void 0===r||null===(o=r.data)||void 0===o?void 0:o.message)||"Password change failed")}})),$c=Ks({name:"auth",initialState:zc,reducers:{clearError:e=>{e.error=null},setCredentials:(e,t)=>{e.user=t.payload.user,e.token=t.payload.token,e.refreshToken=t.payload.refreshToken,e.isAuthenticated=!0,e.error=null},updateUser:(e,t)=>{e.user&&(e.user=Wo(Wo({},e.user),t.payload))}},extraReducers:e=>{e.addCase(Mc.pending,(e=>{e.isLoading=!0,e.error=null})).addCase(Mc.fulfilled,((e,t)=>{e.isLoading=!1,e.user=t.payload.user,e.token=t.payload.token,e.refreshToken=t.payload.refreshToken,e.isAuthenticated=!0,e.error=null})).addCase(Mc.rejected,((e,t)=>{e.isLoading=!1,e.error=t.payload,e.isAuthenticated=!1})).addCase(Bc.pending,(e=>{e.isLoading=!0,e.error=null})).addCase(Bc.fulfilled,((e,t)=>{e.isLoading=!1,e.user=t.payload.user,e.token=t.payload.token,e.refreshToken=t.payload.refreshToken,e.isAuthenticated=!0,e.error=null})).addCase(Bc.rejected,((e,t)=>{e.isLoading=!1,e.error=t.payload,e.isAuthenticated=!1})).addCase(Uc.pending,(e=>{e.isLoading=!0})).addCase(Uc.fulfilled,((e,t)=>{e.isLoading=!1,e.user=t.payload.user,e.isAuthenticated=!0,e.error=null})).addCase(Uc.rejected,((e,t)=>{e.isLoading=!1,e.error=t.payload,e.isAuthenticated=!1,e.user=null,e.token=null,e.refreshToken=null,localStorage.removeItem("token"),localStorage.removeItem("refreshToken")})).addCase(Vc.fulfilled,((e,t)=>{e.token=t.payload.token,e.refreshToken=t.payload.refreshToken,e.error=null})).addCase(Vc.rejected,(e=>{e.isAuthenticated=!1,e.user=null,e.token=null,e.refreshToken=null,localStorage.removeItem("token"),localStorage.removeItem("refreshToken")})).addCase(Wc.fulfilled,(e=>{e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1,e.error=null})).addCase(Hc.pending,(e=>{e.isLoading=!0,e.error=null})).addCase(Hc.fulfilled,(e=>{e.isLoading=!1,e.error=null,e.user=null,e.token=null,e.refreshToken=null,e.isAuthenticated=!1,localStorage.removeItem("token"),localStorage.removeItem("refreshToken")})).addCase(Hc.rejected,((e,t)=>{e.isLoading=!1,e.error=t.payload}))}}),{clearError:Kc,setCredentials:qc,updateUser:Qc}=$c.actions,Gc=$c.reducer,Jc=Ys("animals/fetchAnimals",(async(e,t)=>{let{rejectWithValue:n}=t;try{await new Promise((e=>setTimeout(e,1e3)));const t=[{_id:"1",tagNumber:"C001",name:"Bessie",species:"cattle",breed:"Holstein",gender:"female",dateOfBirth:"2022-03-15",color:"Black and White",currentWeight:450,birthWeight:35,status:"active",healthStatus:"healthy",breedingStatus:"available",isActive:!0,createdBy:"user1",createdAt:"2023-01-01",updatedAt:"2023-01-01",age:1,ageInDays:365,latestWeight:450},{_id:"2",tagNumber:"S001",name:"Woolly",species:"sheep",breed:"Merino",gender:"male",dateOfBirth:"2023-01-10",color:"White",currentWeight:75,birthWeight:4,status:"active",healthStatus:"healthy",breedingStatus:"available",isActive:!0,createdBy:"user1",createdAt:"2023-01-10",updatedAt:"2023-01-10",age:0,ageInDays:180,latestWeight:75}];return{animals:t,pagination:{page:e.page||1,limit:e.limit||20,total:t.length,totalPages:Math.ceil(t.length/(e.limit||20))}}}catch(r){return n(r.message||"Failed to fetch animals")}})),Xc=Ys("animals/fetchAnimalById",(async(e,t)=>{let{rejectWithValue:n}=t;try{await new Promise((e=>setTimeout(e,500)));return{_id:e,tagNumber:"C001",name:"Bessie",species:"cattle",breed:"Holstein",gender:"female",dateOfBirth:"2022-03-15",color:"Black and White",currentWeight:450,birthWeight:35,status:"active",healthStatus:"healthy",breedingStatus:"available",isActive:!0,createdBy:"user1",createdAt:"2023-01-01",updatedAt:"2023-01-01",age:1,ageInDays:365,latestWeight:450}}catch(r){return n(r.message||"Failed to fetch animal")}})),Yc=Ys("animals/fetchStatistics",(async(e,t)=>{let{rejectWithValue:n}=t;try{return await new Promise((e=>setTimeout(e,500))),{total:150,bySpecies:{cattle:75,sheep:45,goat:20,pig:10},byStatus:{active:140,sold:5,deceased:3,transferred:2},byHealthStatus:{healthy:135,sick:8,injured:4,recovering:3},byBreedingStatus:{available:80,pregnant:25,lactating:30,breeding:10,retired:5}}}catch(r){return n(r.message||"Failed to fetch statistics")}})),Zc=Ks({name:"animals",initialState:{animals:[],selectedAnimal:null,filters:{},pagination:{page:1,limit:20,total:0,totalPages:0},isLoading:!1,error:null,statistics:null},reducers:{setFilters:(e,t)=>{e.filters=Wo(Wo({},e.filters),t.payload)},clearFilters:e=>{e.filters={}},setSelectedAnimal:(e,t)=>{e.selectedAnimal=t.payload},setPagination:(e,t)=>{e.pagination=Wo(Wo({},e.pagination),t.payload)},clearError:e=>{e.error=null}},extraReducers:e=>{e.addCase(Jc.pending,(e=>{e.isLoading=!0,e.error=null})).addCase(Jc.fulfilled,((e,t)=>{e.isLoading=!1,e.animals=t.payload.animals,e.pagination=t.payload.pagination,e.error=null})).addCase(Jc.rejected,((e,t)=>{e.isLoading=!1,e.error=t.payload})).addCase(Xc.pending,(e=>{e.isLoading=!0,e.error=null})).addCase(Xc.fulfilled,((e,t)=>{e.isLoading=!1,e.selectedAnimal=t.payload,e.error=null})).addCase(Xc.rejected,((e,t)=>{e.isLoading=!1,e.error=t.payload})).addCase(Yc.fulfilled,((e,t)=>{e.statistics=t.payload}))}}),{setFilters:ed,clearFilters:td,setSelectedAnimal:nd,setPagination:rd,clearError:od}=Zc.actions,ad=Zc.reducer,id=Ks({name:"health",initialState:{records:[],alerts:[],selectedRecord:null,isLoading:!1,error:null,statistics:null},reducers:{setRecords:(e,t)=>{e.records=t.payload},addRecord:(e,t)=>{e.records.unshift(t.payload)},updateRecord:(e,t)=>{const n=e.records.findIndex((e=>e._id===t.payload._id));-1!==n&&(e.records[n]=t.payload)},deleteRecord:(e,t)=>{e.records=e.records.filter((e=>e._id!==t.payload))},setAlerts:(e,t)=>{e.alerts=t.payload},addAlert:(e,t)=>{e.alerts.unshift(t.payload)},resolveAlert:(e,t)=>{const n=e.alerts.find((e=>e._id===t.payload.id));n&&(n.isResolved=!0,n.resolvedDate=(new Date).toISOString(),n.resolvedBy=t.payload.resolvedBy,n.resolvedNotes=t.payload.notes)},setSelectedRecord:(e,t)=>{e.selectedRecord=t.payload},setLoading:(e,t)=>{e.isLoading=t.payload},setError:(e,t)=>{e.error=t.payload},clearError:e=>{e.error=null},setStatistics:(e,t)=>{e.statistics=t.payload}}}),{setRecords:sd,addRecord:ld,updateRecord:ud,deleteRecord:cd,setAlerts:dd,addAlert:fd,resolveAlert:pd,setSelectedRecord:hd,setLoading:gd,setError:md,clearError:yd,setStatistics:vd}=id.actions,bd=id.reducer,wd=Ks({name:"breeding",initialState:{breedingRecords:[],birthRecords:[],heatRecords:[],selectedBreedingRecord:null,selectedBirthRecord:null,isLoading:!1,error:null,statistics:null},reducers:{setBreedingRecords:(e,t)=>{e.breedingRecords=t.payload},addBreedingRecord:(e,t)=>{e.breedingRecords.unshift(t.payload)},updateBreedingRecord:(e,t)=>{const n=e.breedingRecords.findIndex((e=>e._id===t.payload._id));-1!==n&&(e.breedingRecords[n]=t.payload)},deleteBreedingRecord:(e,t)=>{e.breedingRecords=e.breedingRecords.filter((e=>e._id!==t.payload))},setBirthRecords:(e,t)=>{e.birthRecords=t.payload},addBirthRecord:(e,t)=>{e.birthRecords.unshift(t.payload)},updateBirthRecord:(e,t)=>{const n=e.birthRecords.findIndex((e=>e._id===t.payload._id));-1!==n&&(e.birthRecords[n]=t.payload)},deleteBirthRecord:(e,t)=>{e.birthRecords=e.birthRecords.filter((e=>e._id!==t.payload))},setHeatRecords:(e,t)=>{e.heatRecords=t.payload},addHeatRecord:(e,t)=>{e.heatRecords.unshift(t.payload)},updateHeatRecord:(e,t)=>{const n=e.heatRecords.findIndex((e=>e._id===t.payload._id));-1!==n&&(e.heatRecords[n]=t.payload)},deleteHeatRecord:(e,t)=>{e.heatRecords=e.heatRecords.filter((e=>e._id!==t.payload))},setSelectedBreedingRecord:(e,t)=>{e.selectedBreedingRecord=t.payload},setSelectedBirthRecord:(e,t)=>{e.selectedBirthRecord=t.payload},setLoading:(e,t)=>{e.isLoading=t.payload},setError:(e,t)=>{e.error=t.payload},clearError:e=>{e.error=null},setStatistics:(e,t)=>{e.statistics=t.payload}}}),{setBreedingRecords:kd,addBreedingRecord:Sd,updateBreedingRecord:xd,deleteBreedingRecord:Ed,setBirthRecords:Cd,addBirthRecord:Pd,updateBirthRecord:Od,deleteBirthRecord:Rd,setHeatRecords:Td,addHeatRecord:Nd,updateHeatRecord:Ad,deleteHeatRecord:Ld,setSelectedBreedingRecord:_d,setSelectedBirthRecord:jd,setLoading:Dd,setError:Id,clearError:Fd,setStatistics:zd}=wd.actions,Md=wd.reducer,Bd=Ks({name:"ui",initialState:{sidebarOpen:!0,theme:"light",language:localStorage.getItem("language")||"en",notifications:[],loading:{global:!1},modals:{},breadcrumbs:[],pageTitle:"Dashboard"},reducers:{toggleSidebar:e=>{e.sidebarOpen=!e.sidebarOpen},setSidebarOpen:(e,t)=>{e.sidebarOpen=t.payload},setTheme:(e,t)=>{e.theme=t.payload,localStorage.setItem("theme",t.payload)},setLanguage:(e,t)=>{e.language=t.payload,localStorage.setItem("language",t.payload)},addNotification:(e,t)=>{const n=Wo(Wo({},t.payload),{},{id:Date.now().toString(),timestamp:(new Date).toISOString(),read:!1});e.notifications.unshift(n),e.notifications.length>50&&(e.notifications=e.notifications.slice(0,50))},removeNotification:(e,t)=>{e.notifications=e.notifications.filter((e=>e.id!==t.payload))},markNotificationAsRead:(e,t)=>{const n=e.notifications.find((e=>e.id===t.payload));n&&(n.read=!0)},markAllNotificationsAsRead:e=>{e.notifications.forEach((e=>e.read=!0))},clearNotifications:e=>{e.notifications=[]},setLoading:(e,t)=>{e.loading[t.payload.key]=t.payload.loading},setGlobalLoading:(e,t)=>{e.loading.global=t.payload},openModal:(e,t)=>{e.modals[t.payload]=!0},closeModal:(e,t)=>{e.modals[t.payload]=!1},toggleModal:(e,t)=>{e.modals[t.payload]=!e.modals[t.payload]},setBreadcrumbs:(e,t)=>{e.breadcrumbs=t.payload},setPageTitle:(e,t)=>{e.pageTitle=t.payload,document.title="".concat(t.payload," - AMPD Livestock Management")}}}),{toggleSidebar:Ud,setSidebarOpen:Vd,setTheme:Wd,setLanguage:Hd,addNotification:$d,removeNotification:Kd,markNotificationAsRead:qd,markAllNotificationsAsRead:Qd,clearNotifications:Gd,setLoading:Jd,setGlobalLoading:Xd,openModal:Yd,closeModal:Zd,toggleModal:ef,setBreadcrumbs:tf,setPageTitle:nf}=Bd.actions,rf=function(e){var t,n=Hs(),r=e||{},o=r.reducer,a=void 0===o?void 0:o,i=r.middleware,s=void 0===i?n():i,l=r.devTools,u=void 0===l||l,c=r.preloadedState,d=void 0===c?void 0:c,f=r.enhancers,p=void 0===f?void 0:f;if("function"===typeof a)t=a;else{if(!Ms(a))throw new Error('"reducer" is a required argument, and must be a function or an object of functions that can be passed to combineReducers');t=bs(a)}var h=s;"function"===typeof h&&(h=h(n));var g=ks.apply(void 0,h),m=ws;u&&(m=zs(Ds({trace:!1},"object"===typeof u&&u)));var y=new Vs(g),v=y;return Array.isArray(p)?v=Os([g],p):"function"===typeof p&&(v=p(y)),vs(t,d,m.apply(void 0,v))}({reducer:{auth:Gc,animals:ad,health:bd,breeding:Md,ui:Bd.reducer},middleware:e=>e({serializableCheck:{ignoredActions:["persist/PERSIST"]}})}),of=jn({palette:{primary:{main:"#0f766e",light:"#14b8a6",dark:"#134e4a"},secondary:{main:"#374151",light:"#6b7280",dark:"#1f2937"},success:{main:"#059669",light:"#10b981",dark:"#047857"},warning:{main:"#d97706",light:"#f59e0b",dark:"#b45309"},error:{main:"#dc2626",light:"#ef4444",dark:"#b91c1c"},background:{default:"#f9fafb",paper:"#ffffff"}},typography:{fontFamily:'"Inter", "Roboto", "Helvetica", "Arial", sans-serif',h1:{fontWeight:700,fontSize:"2.5rem"},h2:{fontWeight:600,fontSize:"2rem"},h3:{fontWeight:600,fontSize:"1.75rem"},h4:{fontWeight:600,fontSize:"1.5rem"},h5:{fontWeight:600,fontSize:"1.25rem"},h6:{fontWeight:600,fontSize:"1rem"},body1:{fontSize:"1rem",lineHeight:1.5},body2:{fontSize:"0.875rem",lineHeight:1.43}},shape:{borderRadius:8},components:{MuiButton:{styleOverrides:{root:{textTransform:"none",fontWeight:500,borderRadius:8,padding:"8px 16px"}}},MuiCard:{styleOverrides:{root:{boxShadow:"0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)",borderRadius:12}}},MuiTextField:{styleOverrides:{root:{"& .MuiOutlinedInput-root":{borderRadius:8}}}}}});o.createRoot(document.getElementById("root")).render((0,zn.jsx)(t.StrictMode,{children:(0,zn.jsx)(C,{store:rf,children:(0,zn.jsxs)(Oo,{theme:of,children:[(0,zn.jsx)(zo,{}),(0,zn.jsx)(Ue,{children:(0,zn.jsx)(di,{})})]})})}))})()})();
//# sourceMappingURL=main.ee2cf543.js.map