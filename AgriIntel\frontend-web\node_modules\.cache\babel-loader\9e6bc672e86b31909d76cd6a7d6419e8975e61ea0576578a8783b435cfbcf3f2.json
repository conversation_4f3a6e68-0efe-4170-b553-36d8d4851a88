{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ScramSHA256 = exports.ScramSHA1 = void 0;\nconst saslprep_1 = require(\"@mongodb-js/saslprep\");\nconst crypto = require(\"crypto\");\nconst bson_1 = require(\"../../bson\");\nconst error_1 = require(\"../../error\");\nconst utils_1 = require(\"../../utils\");\nconst auth_provider_1 = require(\"./auth_provider\");\nconst providers_1 = require(\"./providers\");\nclass ScramSHA extends auth_provider_1.AuthProvider {\n  constructor(cryptoMethod) {\n    super();\n    this.cryptoMethod = cryptoMethod || 'sha1';\n  }\n  async prepare(handshakeDoc, authContext) {\n    const cryptoMethod = this.cryptoMethod;\n    const credentials = authContext.credentials;\n    if (!credentials) {\n      throw new error_1.MongoMissingCredentialsError('AuthContext must provide credentials.');\n    }\n    const nonce = await (0, utils_1.randomBytes)(24);\n    // store the nonce for later use\n    authContext.nonce = nonce;\n    const request = {\n      ...handshakeDoc,\n      speculativeAuthenticate: {\n        ...makeFirstMessage(cryptoMethod, credentials, nonce),\n        db: credentials.source\n      }\n    };\n    return request;\n  }\n  async auth(authContext) {\n    const {\n      reauthenticating,\n      response\n    } = authContext;\n    if (response?.speculativeAuthenticate && !reauthenticating) {\n      return await continueScramConversation(this.cryptoMethod, response.speculativeAuthenticate, authContext);\n    }\n    return await executeScram(this.cryptoMethod, authContext);\n  }\n}\nfunction cleanUsername(username) {\n  return username.replace('=', '=3D').replace(',', '=2C');\n}\nfunction clientFirstMessageBare(username, nonce) {\n  // NOTE: This is done b/c Javascript uses UTF-16, but the server is hashing in UTF-8.\n  // Since the username is not sasl-prep-d, we need to do this here.\n  return Buffer.concat([Buffer.from('n=', 'utf8'), Buffer.from(username, 'utf8'), Buffer.from(',r=', 'utf8'), Buffer.from(nonce.toString('base64'), 'utf8')]);\n}\nfunction makeFirstMessage(cryptoMethod, credentials, nonce) {\n  const username = cleanUsername(credentials.username);\n  const mechanism = cryptoMethod === 'sha1' ? providers_1.AuthMechanism.MONGODB_SCRAM_SHA1 : providers_1.AuthMechanism.MONGODB_SCRAM_SHA256;\n  // NOTE: This is done b/c Javascript uses UTF-16, but the server is hashing in UTF-8.\n  // Since the username is not sasl-prep-d, we need to do this here.\n  return {\n    saslStart: 1,\n    mechanism,\n    payload: new bson_1.Binary(Buffer.concat([Buffer.from('n,,', 'utf8'), clientFirstMessageBare(username, nonce)])),\n    autoAuthorize: 1,\n    options: {\n      skipEmptyExchange: true\n    }\n  };\n}\nasync function executeScram(cryptoMethod, authContext) {\n  const {\n    connection,\n    credentials\n  } = authContext;\n  if (!credentials) {\n    throw new error_1.MongoMissingCredentialsError('AuthContext must provide credentials.');\n  }\n  if (!authContext.nonce) {\n    throw new error_1.MongoInvalidArgumentError('AuthContext must contain a valid nonce property');\n  }\n  const nonce = authContext.nonce;\n  const db = credentials.source;\n  const saslStartCmd = makeFirstMessage(cryptoMethod, credentials, nonce);\n  const response = await connection.command((0, utils_1.ns)(`${db}.$cmd`), saslStartCmd, undefined);\n  await continueScramConversation(cryptoMethod, response, authContext);\n}\nasync function continueScramConversation(cryptoMethod, response, authContext) {\n  const connection = authContext.connection;\n  const credentials = authContext.credentials;\n  if (!credentials) {\n    throw new error_1.MongoMissingCredentialsError('AuthContext must provide credentials.');\n  }\n  if (!authContext.nonce) {\n    throw new error_1.MongoInvalidArgumentError('Unable to continue SCRAM without valid nonce');\n  }\n  const nonce = authContext.nonce;\n  const db = credentials.source;\n  const username = cleanUsername(credentials.username);\n  const password = credentials.password;\n  const processedPassword = cryptoMethod === 'sha256' ? (0, saslprep_1.saslprep)(password) : passwordDigest(username, password);\n  const payload = Buffer.isBuffer(response.payload) ? new bson_1.Binary(response.payload) : response.payload;\n  const dict = parsePayload(payload);\n  const iterations = parseInt(dict.i, 10);\n  if (iterations && iterations < 4096) {\n    // TODO(NODE-3483)\n    throw new error_1.MongoRuntimeError(`Server returned an invalid iteration count ${iterations}`);\n  }\n  const salt = dict.s;\n  const rnonce = dict.r;\n  if (rnonce.startsWith('nonce')) {\n    // TODO(NODE-3483)\n    throw new error_1.MongoRuntimeError(`Server returned an invalid nonce: ${rnonce}`);\n  }\n  // Set up start of proof\n  const withoutProof = `c=biws,r=${rnonce}`;\n  const saltedPassword = HI(processedPassword, Buffer.from(salt, 'base64'), iterations, cryptoMethod);\n  const clientKey = HMAC(cryptoMethod, saltedPassword, 'Client Key');\n  const serverKey = HMAC(cryptoMethod, saltedPassword, 'Server Key');\n  const storedKey = H(cryptoMethod, clientKey);\n  const authMessage = [clientFirstMessageBare(username, nonce), payload.toString('utf8'), withoutProof].join(',');\n  const clientSignature = HMAC(cryptoMethod, storedKey, authMessage);\n  const clientProof = `p=${xor(clientKey, clientSignature)}`;\n  const clientFinal = [withoutProof, clientProof].join(',');\n  const serverSignature = HMAC(cryptoMethod, serverKey, authMessage);\n  const saslContinueCmd = {\n    saslContinue: 1,\n    conversationId: response.conversationId,\n    payload: new bson_1.Binary(Buffer.from(clientFinal))\n  };\n  const r = await connection.command((0, utils_1.ns)(`${db}.$cmd`), saslContinueCmd, undefined);\n  const parsedResponse = parsePayload(r.payload);\n  if (!compareDigest(Buffer.from(parsedResponse.v, 'base64'), serverSignature)) {\n    throw new error_1.MongoRuntimeError('Server returned an invalid signature');\n  }\n  if (r.done !== false) {\n    // If the server sends r.done === true we can save one RTT\n    return;\n  }\n  const retrySaslContinueCmd = {\n    saslContinue: 1,\n    conversationId: r.conversationId,\n    payload: Buffer.alloc(0)\n  };\n  await connection.command((0, utils_1.ns)(`${db}.$cmd`), retrySaslContinueCmd, undefined);\n}\nfunction parsePayload(payload) {\n  const payloadStr = payload.toString('utf8');\n  const dict = {};\n  const parts = payloadStr.split(',');\n  for (let i = 0; i < parts.length; i++) {\n    const valueParts = (parts[i].match(/^([^=]*)=(.*)$/) ?? []).slice(1);\n    dict[valueParts[0]] = valueParts[1];\n  }\n  return dict;\n}\nfunction passwordDigest(username, password) {\n  if (typeof username !== 'string') {\n    throw new error_1.MongoInvalidArgumentError('Username must be a string');\n  }\n  if (typeof password !== 'string') {\n    throw new error_1.MongoInvalidArgumentError('Password must be a string');\n  }\n  if (password.length === 0) {\n    throw new error_1.MongoInvalidArgumentError('Password cannot be empty');\n  }\n  let md5;\n  try {\n    md5 = crypto.createHash('md5');\n  } catch (err) {\n    if (crypto.getFips()) {\n      // This error is (slightly) more helpful than what comes from OpenSSL directly, e.g.\n      // 'Error: error:060800C8:digital envelope routines:EVP_DigestInit_ex:disabled for FIPS'\n      throw new Error('Auth mechanism SCRAM-SHA-1 is not supported in FIPS mode');\n    }\n    throw err;\n  }\n  md5.update(`${username}:mongo:${password}`, 'utf8');\n  return md5.digest('hex');\n}\n// XOR two buffers\nfunction xor(a, b) {\n  if (!Buffer.isBuffer(a)) {\n    a = Buffer.from(a);\n  }\n  if (!Buffer.isBuffer(b)) {\n    b = Buffer.from(b);\n  }\n  const length = Math.max(a.length, b.length);\n  const res = [];\n  for (let i = 0; i < length; i += 1) {\n    res.push(a[i] ^ b[i]);\n  }\n  return Buffer.from(res).toString('base64');\n}\nfunction H(method, text) {\n  return crypto.createHash(method).update(text).digest();\n}\nfunction HMAC(method, key, text) {\n  return crypto.createHmac(method, key).update(text).digest();\n}\nlet _hiCache = {};\nlet _hiCacheCount = 0;\nfunction _hiCachePurge() {\n  _hiCache = {};\n  _hiCacheCount = 0;\n}\nconst hiLengthMap = {\n  sha256: 32,\n  sha1: 20\n};\nfunction HI(data, salt, iterations, cryptoMethod) {\n  // omit the work if already generated\n  const key = [data, salt.toString('base64'), iterations].join('_');\n  if (_hiCache[key] != null) {\n    return _hiCache[key];\n  }\n  // generate the salt\n  const saltedData = crypto.pbkdf2Sync(data, salt, iterations, hiLengthMap[cryptoMethod], cryptoMethod);\n  // cache a copy to speed up the next lookup, but prevent unbounded cache growth\n  if (_hiCacheCount >= 200) {\n    _hiCachePurge();\n  }\n  _hiCache[key] = saltedData;\n  _hiCacheCount += 1;\n  return saltedData;\n}\nfunction compareDigest(lhs, rhs) {\n  if (lhs.length !== rhs.length) {\n    return false;\n  }\n  if (typeof crypto.timingSafeEqual === 'function') {\n    return crypto.timingSafeEqual(lhs, rhs);\n  }\n  let result = 0;\n  for (let i = 0; i < lhs.length; i++) {\n    result |= lhs[i] ^ rhs[i];\n  }\n  return result === 0;\n}\nclass ScramSHA1 extends ScramSHA {\n  constructor() {\n    super('sha1');\n  }\n}\nexports.ScramSHA1 = ScramSHA1;\nclass ScramSHA256 extends ScramSHA {\n  constructor() {\n    super('sha256');\n  }\n}\nexports.ScramSHA256 = ScramSHA256;", "map": {"version": 3, "names": ["saslprep_1", "require", "crypto", "bson_1", "error_1", "utils_1", "auth_provider_1", "providers_1", "ScramSHA", "<PERSON>th<PERSON><PERSON><PERSON>", "constructor", "cryptoMethod", "prepare", "handshakeDoc", "authContext", "credentials", "MongoMissingCredentialsError", "nonce", "randomBytes", "request", "speculativeAuthenticate", "makeFirstMessage", "db", "source", "auth", "reauthenticating", "response", "continueScramConversation", "executeScram", "cleanUsername", "username", "replace", "clientFirstMessageBare", "<PERSON><PERSON><PERSON>", "concat", "from", "toString", "mechanism", "AuthMechanism", "MONGODB_SCRAM_SHA1", "MONGODB_SCRAM_SHA256", "saslStart", "payload", "Binary", "autoAuthorize", "options", "skipEmptyExchange", "connection", "MongoInvalidArgumentError", "saslStartCmd", "command", "ns", "undefined", "password", "processedPassword", "saslprep", "passwordDigest", "<PERSON><PERSON><PERSON><PERSON>", "dict", "parsePayload", "iterations", "parseInt", "i", "MongoRuntimeError", "salt", "s", "rnonce", "r", "startsWith", "without<PERSON><PERSON>f", "saltedPassword", "HI", "client<PERSON>ey", "HMAC", "server<PERSON>ey", "<PERSON><PERSON><PERSON>", "H", "authMessage", "join", "clientSignature", "clientProof", "xor", "clientFinal", "serverSignature", "saslContinueCmd", "saslContinue", "conversationId", "parsedResponse", "compareDigest", "v", "done", "retrySaslContinueCmd", "alloc", "payloadStr", "parts", "split", "length", "valueParts", "match", "slice", "md5", "createHash", "err", "getFips", "Error", "update", "digest", "a", "b", "Math", "max", "res", "push", "method", "text", "key", "createHmac", "_hiCache", "_hiCacheCount", "_hiCachePurge", "hiLengthMap", "sha256", "sha1", "data", "saltedData", "pbkdf2Sync", "lhs", "rhs", "timingSafeEqual", "result", "ScramSHA1", "exports", "ScramSHA256"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\auth\\scram.ts"], "sourcesContent": ["import { saslprep } from '@mongodb-js/saslprep';\nimport * as crypto from 'crypto';\n\nimport { Binary, type Document } from '../../bson';\nimport {\n  MongoInvalidArgumentError,\n  MongoMissingCredentialsError,\n  MongoRuntimeError\n} from '../../error';\nimport { ns, randomBytes } from '../../utils';\nimport type { HandshakeDocument } from '../connect';\nimport { type AuthContext, AuthProvider } from './auth_provider';\nimport type { MongoCredentials } from './mongo_credentials';\nimport { AuthMechanism } from './providers';\n\ntype CryptoMethod = 'sha1' | 'sha256';\n\nclass ScramSHA extends AuthProvider {\n  cryptoMethod: CryptoMethod;\n\n  constructor(cryptoMethod: CryptoMethod) {\n    super();\n    this.cryptoMethod = cryptoMethod || 'sha1';\n  }\n\n  override async prepare(\n    handshakeDoc: HandshakeDocument,\n    authContext: AuthContext\n  ): Promise<HandshakeDocument> {\n    const cryptoMethod = this.cryptoMethod;\n    const credentials = authContext.credentials;\n    if (!credentials) {\n      throw new MongoMissingCredentialsError('AuthContext must provide credentials.');\n    }\n\n    const nonce = await randomBytes(24);\n    // store the nonce for later use\n    authContext.nonce = nonce;\n\n    const request = {\n      ...handshakeDoc,\n      speculativeAuthenticate: {\n        ...makeFirstMessage(cryptoMethod, credentials, nonce),\n        db: credentials.source\n      }\n    };\n\n    return request;\n  }\n\n  override async auth(authContext: AuthContext) {\n    const { reauthenticating, response } = authContext;\n    if (response?.speculativeAuthenticate && !reauthenticating) {\n      return await continueScramConversation(\n        this.cryptoMethod,\n        response.speculativeAuthenticate,\n        authContext\n      );\n    }\n    return await executeScram(this.cryptoMethod, authContext);\n  }\n}\n\nfunction cleanUsername(username: string) {\n  return username.replace('=', '=3D').replace(',', '=2C');\n}\n\nfunction clientFirstMessageBare(username: string, nonce: Buffer) {\n  // NOTE: This is done b/c Javascript uses UTF-16, but the server is hashing in UTF-8.\n  // Since the username is not sasl-prep-d, we need to do this here.\n  return Buffer.concat([\n    Buffer.from('n=', 'utf8'),\n    Buffer.from(username, 'utf8'),\n    Buffer.from(',r=', 'utf8'),\n    Buffer.from(nonce.toString('base64'), 'utf8')\n  ]);\n}\n\nfunction makeFirstMessage(\n  cryptoMethod: CryptoMethod,\n  credentials: MongoCredentials,\n  nonce: Buffer\n) {\n  const username = cleanUsername(credentials.username);\n  const mechanism =\n    cryptoMethod === 'sha1' ? AuthMechanism.MONGODB_SCRAM_SHA1 : AuthMechanism.MONGODB_SCRAM_SHA256;\n\n  // NOTE: This is done b/c Javascript uses UTF-16, but the server is hashing in UTF-8.\n  // Since the username is not sasl-prep-d, we need to do this here.\n  return {\n    saslStart: 1,\n    mechanism,\n    payload: new Binary(\n      Buffer.concat([Buffer.from('n,,', 'utf8'), clientFirstMessageBare(username, nonce)])\n    ),\n    autoAuthorize: 1,\n    options: { skipEmptyExchange: true }\n  };\n}\n\nasync function executeScram(cryptoMethod: CryptoMethod, authContext: AuthContext): Promise<void> {\n  const { connection, credentials } = authContext;\n  if (!credentials) {\n    throw new MongoMissingCredentialsError('AuthContext must provide credentials.');\n  }\n  if (!authContext.nonce) {\n    throw new MongoInvalidArgumentError('AuthContext must contain a valid nonce property');\n  }\n  const nonce = authContext.nonce;\n  const db = credentials.source;\n\n  const saslStartCmd = makeFirstMessage(cryptoMethod, credentials, nonce);\n  const response = await connection.command(ns(`${db}.$cmd`), saslStartCmd, undefined);\n  await continueScramConversation(cryptoMethod, response, authContext);\n}\n\nasync function continueScramConversation(\n  cryptoMethod: CryptoMethod,\n  response: Document,\n  authContext: AuthContext\n): Promise<void> {\n  const connection = authContext.connection;\n  const credentials = authContext.credentials;\n  if (!credentials) {\n    throw new MongoMissingCredentialsError('AuthContext must provide credentials.');\n  }\n  if (!authContext.nonce) {\n    throw new MongoInvalidArgumentError('Unable to continue SCRAM without valid nonce');\n  }\n  const nonce = authContext.nonce;\n\n  const db = credentials.source;\n  const username = cleanUsername(credentials.username);\n  const password = credentials.password;\n\n  const processedPassword =\n    cryptoMethod === 'sha256' ? saslprep(password) : passwordDigest(username, password);\n\n  const payload: Binary = Buffer.isBuffer(response.payload)\n    ? new Binary(response.payload)\n    : response.payload;\n\n  const dict = parsePayload(payload);\n\n  const iterations = parseInt(dict.i, 10);\n  if (iterations && iterations < 4096) {\n    // TODO(NODE-3483)\n    throw new MongoRuntimeError(`Server returned an invalid iteration count ${iterations}`);\n  }\n\n  const salt = dict.s;\n  const rnonce = dict.r;\n  if (rnonce.startsWith('nonce')) {\n    // TODO(NODE-3483)\n    throw new MongoRuntimeError(`Server returned an invalid nonce: ${rnonce}`);\n  }\n\n  // Set up start of proof\n  const withoutProof = `c=biws,r=${rnonce}`;\n  const saltedPassword = HI(\n    processedPassword,\n    Buffer.from(salt, 'base64'),\n    iterations,\n    cryptoMethod\n  );\n\n  const clientKey = HMAC(cryptoMethod, saltedPassword, 'Client Key');\n  const serverKey = HMAC(cryptoMethod, saltedPassword, 'Server Key');\n  const storedKey = H(cryptoMethod, clientKey);\n  const authMessage = [\n    clientFirstMessageBare(username, nonce),\n    payload.toString('utf8'),\n    withoutProof\n  ].join(',');\n\n  const clientSignature = HMAC(cryptoMethod, storedKey, authMessage);\n  const clientProof = `p=${xor(clientKey, clientSignature)}`;\n  const clientFinal = [withoutProof, clientProof].join(',');\n\n  const serverSignature = HMAC(cryptoMethod, serverKey, authMessage);\n  const saslContinueCmd = {\n    saslContinue: 1,\n    conversationId: response.conversationId,\n    payload: new Binary(Buffer.from(clientFinal))\n  };\n\n  const r = await connection.command(ns(`${db}.$cmd`), saslContinueCmd, undefined);\n  const parsedResponse = parsePayload(r.payload);\n\n  if (!compareDigest(Buffer.from(parsedResponse.v, 'base64'), serverSignature)) {\n    throw new MongoRuntimeError('Server returned an invalid signature');\n  }\n\n  if (r.done !== false) {\n    // If the server sends r.done === true we can save one RTT\n    return;\n  }\n\n  const retrySaslContinueCmd = {\n    saslContinue: 1,\n    conversationId: r.conversationId,\n    payload: Buffer.alloc(0)\n  };\n\n  await connection.command(ns(`${db}.$cmd`), retrySaslContinueCmd, undefined);\n}\n\nfunction parsePayload(payload: Binary) {\n  const payloadStr = payload.toString('utf8');\n  const dict: Document = {};\n  const parts = payloadStr.split(',');\n  for (let i = 0; i < parts.length; i++) {\n    const valueParts = (parts[i].match(/^([^=]*)=(.*)$/) ?? []).slice(1);\n    dict[valueParts[0]] = valueParts[1];\n  }\n  return dict;\n}\n\nfunction passwordDigest(username: string, password: string) {\n  if (typeof username !== 'string') {\n    throw new MongoInvalidArgumentError('Username must be a string');\n  }\n\n  if (typeof password !== 'string') {\n    throw new MongoInvalidArgumentError('Password must be a string');\n  }\n\n  if (password.length === 0) {\n    throw new MongoInvalidArgumentError('Password cannot be empty');\n  }\n\n  let md5: crypto.Hash;\n  try {\n    md5 = crypto.createHash('md5');\n  } catch (err) {\n    if (crypto.getFips()) {\n      // This error is (slightly) more helpful than what comes from OpenSSL directly, e.g.\n      // 'Error: error:060800C8:digital envelope routines:EVP_DigestInit_ex:disabled for FIPS'\n      throw new Error('Auth mechanism SCRAM-SHA-1 is not supported in FIPS mode');\n    }\n    throw err;\n  }\n  md5.update(`${username}:mongo:${password}`, 'utf8');\n  return md5.digest('hex');\n}\n\n// XOR two buffers\nfunction xor(a: Buffer, b: Buffer) {\n  if (!Buffer.isBuffer(a)) {\n    a = Buffer.from(a);\n  }\n\n  if (!Buffer.isBuffer(b)) {\n    b = Buffer.from(b);\n  }\n\n  const length = Math.max(a.length, b.length);\n  const res = [];\n\n  for (let i = 0; i < length; i += 1) {\n    res.push(a[i] ^ b[i]);\n  }\n\n  return Buffer.from(res).toString('base64');\n}\n\nfunction H(method: CryptoMethod, text: Buffer) {\n  return crypto.createHash(method).update(text).digest();\n}\n\nfunction HMAC(method: CryptoMethod, key: Buffer, text: Buffer | string) {\n  return crypto.createHmac(method, key).update(text).digest();\n}\n\ninterface HICache {\n  [key: string]: Buffer;\n}\n\nlet _hiCache: HICache = {};\nlet _hiCacheCount = 0;\nfunction _hiCachePurge() {\n  _hiCache = {};\n  _hiCacheCount = 0;\n}\n\nconst hiLengthMap = {\n  sha256: 32,\n  sha1: 20\n};\n\nfunction HI(data: string, salt: Buffer, iterations: number, cryptoMethod: CryptoMethod) {\n  // omit the work if already generated\n  const key = [data, salt.toString('base64'), iterations].join('_');\n  if (_hiCache[key] != null) {\n    return _hiCache[key];\n  }\n\n  // generate the salt\n  const saltedData = crypto.pbkdf2Sync(\n    data,\n    salt,\n    iterations,\n    hiLengthMap[cryptoMethod],\n    cryptoMethod\n  );\n\n  // cache a copy to speed up the next lookup, but prevent unbounded cache growth\n  if (_hiCacheCount >= 200) {\n    _hiCachePurge();\n  }\n\n  _hiCache[key] = saltedData;\n  _hiCacheCount += 1;\n  return saltedData;\n}\n\nfunction compareDigest(lhs: Buffer, rhs: Uint8Array) {\n  if (lhs.length !== rhs.length) {\n    return false;\n  }\n\n  if (typeof crypto.timingSafeEqual === 'function') {\n    return crypto.timingSafeEqual(lhs, rhs);\n  }\n\n  let result = 0;\n  for (let i = 0; i < lhs.length; i++) {\n    result |= lhs[i] ^ rhs[i];\n  }\n\n  return result === 0;\n}\n\nexport class ScramSHA1 extends ScramSHA {\n  constructor() {\n    super('sha1');\n  }\n}\n\nexport class ScramSHA256 extends ScramSHA {\n  constructor() {\n    super('sha256');\n  }\n}\n"], "mappings": ";;;;;;AAAA,MAAAA,UAAA,GAAAC,OAAA;AACA,MAAAC,MAAA,GAAAD,OAAA;AAEA,MAAAE,MAAA,GAAAF,OAAA;AACA,MAAAG,OAAA,GAAAH,OAAA;AAKA,MAAAI,OAAA,GAAAJ,OAAA;AAEA,MAAAK,eAAA,GAAAL,OAAA;AAEA,MAAAM,WAAA,GAAAN,OAAA;AAIA,MAAMO,QAAS,SAAQF,eAAA,CAAAG,YAAY;EAGjCC,YAAYC,YAA0B;IACpC,KAAK,EAAE;IACP,IAAI,CAACA,YAAY,GAAGA,YAAY,IAAI,MAAM;EAC5C;EAES,MAAMC,OAAOA,CACpBC,YAA+B,EAC/BC,WAAwB;IAExB,MAAMH,YAAY,GAAG,IAAI,CAACA,YAAY;IACtC,MAAMI,WAAW,GAAGD,WAAW,CAACC,WAAW;IAC3C,IAAI,CAACA,WAAW,EAAE;MAChB,MAAM,IAAIX,OAAA,CAAAY,4BAA4B,CAAC,uCAAuC,CAAC;IACjF;IAEA,MAAMC,KAAK,GAAG,MAAM,IAAAZ,OAAA,CAAAa,WAAW,EAAC,EAAE,CAAC;IACnC;IACAJ,WAAW,CAACG,KAAK,GAAGA,KAAK;IAEzB,MAAME,OAAO,GAAG;MACd,GAAGN,YAAY;MACfO,uBAAuB,EAAE;QACvB,GAAGC,gBAAgB,CAACV,YAAY,EAAEI,WAAW,EAAEE,KAAK,CAAC;QACrDK,EAAE,EAAEP,WAAW,CAACQ;;KAEnB;IAED,OAAOJ,OAAO;EAChB;EAES,MAAMK,IAAIA,CAACV,WAAwB;IAC1C,MAAM;MAAEW,gBAAgB;MAAEC;IAAQ,CAAE,GAAGZ,WAAW;IAClD,IAAIY,QAAQ,EAAEN,uBAAuB,IAAI,CAACK,gBAAgB,EAAE;MAC1D,OAAO,MAAME,yBAAyB,CACpC,IAAI,CAAChB,YAAY,EACjBe,QAAQ,CAACN,uBAAuB,EAChCN,WAAW,CACZ;IACH;IACA,OAAO,MAAMc,YAAY,CAAC,IAAI,CAACjB,YAAY,EAAEG,WAAW,CAAC;EAC3D;;AAGF,SAASe,aAAaA,CAACC,QAAgB;EACrC,OAAOA,QAAQ,CAACC,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC,CAACA,OAAO,CAAC,GAAG,EAAE,KAAK,CAAC;AACzD;AAEA,SAASC,sBAAsBA,CAACF,QAAgB,EAAEb,KAAa;EAC7D;EACA;EACA,OAAOgB,MAAM,CAACC,MAAM,CAAC,CACnBD,MAAM,CAACE,IAAI,CAAC,IAAI,EAAE,MAAM,CAAC,EACzBF,MAAM,CAACE,IAAI,CAACL,QAAQ,EAAE,MAAM,CAAC,EAC7BG,MAAM,CAACE,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,EAC1BF,MAAM,CAACE,IAAI,CAAClB,KAAK,CAACmB,QAAQ,CAAC,QAAQ,CAAC,EAAE,MAAM,CAAC,CAC9C,CAAC;AACJ;AAEA,SAASf,gBAAgBA,CACvBV,YAA0B,EAC1BI,WAA6B,EAC7BE,KAAa;EAEb,MAAMa,QAAQ,GAAGD,aAAa,CAACd,WAAW,CAACe,QAAQ,CAAC;EACpD,MAAMO,SAAS,GACb1B,YAAY,KAAK,MAAM,GAAGJ,WAAA,CAAA+B,aAAa,CAACC,kBAAkB,GAAGhC,WAAA,CAAA+B,aAAa,CAACE,oBAAoB;EAEjG;EACA;EACA,OAAO;IACLC,SAAS,EAAE,CAAC;IACZJ,SAAS;IACTK,OAAO,EAAE,IAAIvC,MAAA,CAAAwC,MAAM,CACjBV,MAAM,CAACC,MAAM,CAAC,CAACD,MAAM,CAACE,IAAI,CAAC,KAAK,EAAE,MAAM,CAAC,EAAEH,sBAAsB,CAACF,QAAQ,EAAEb,KAAK,CAAC,CAAC,CAAC,CACrF;IACD2B,aAAa,EAAE,CAAC;IAChBC,OAAO,EAAE;MAAEC,iBAAiB,EAAE;IAAI;GACnC;AACH;AAEA,eAAelB,YAAYA,CAACjB,YAA0B,EAAEG,WAAwB;EAC9E,MAAM;IAAEiC,UAAU;IAAEhC;EAAW,CAAE,GAAGD,WAAW;EAC/C,IAAI,CAACC,WAAW,EAAE;IAChB,MAAM,IAAIX,OAAA,CAAAY,4BAA4B,CAAC,uCAAuC,CAAC;EACjF;EACA,IAAI,CAACF,WAAW,CAACG,KAAK,EAAE;IACtB,MAAM,IAAIb,OAAA,CAAA4C,yBAAyB,CAAC,iDAAiD,CAAC;EACxF;EACA,MAAM/B,KAAK,GAAGH,WAAW,CAACG,KAAK;EAC/B,MAAMK,EAAE,GAAGP,WAAW,CAACQ,MAAM;EAE7B,MAAM0B,YAAY,GAAG5B,gBAAgB,CAACV,YAAY,EAAEI,WAAW,EAAEE,KAAK,CAAC;EACvE,MAAMS,QAAQ,GAAG,MAAMqB,UAAU,CAACG,OAAO,CAAC,IAAA7C,OAAA,CAAA8C,EAAE,EAAC,GAAG7B,EAAE,OAAO,CAAC,EAAE2B,YAAY,EAAEG,SAAS,CAAC;EACpF,MAAMzB,yBAAyB,CAAChB,YAAY,EAAEe,QAAQ,EAAEZ,WAAW,CAAC;AACtE;AAEA,eAAea,yBAAyBA,CACtChB,YAA0B,EAC1Be,QAAkB,EAClBZ,WAAwB;EAExB,MAAMiC,UAAU,GAAGjC,WAAW,CAACiC,UAAU;EACzC,MAAMhC,WAAW,GAAGD,WAAW,CAACC,WAAW;EAC3C,IAAI,CAACA,WAAW,EAAE;IAChB,MAAM,IAAIX,OAAA,CAAAY,4BAA4B,CAAC,uCAAuC,CAAC;EACjF;EACA,IAAI,CAACF,WAAW,CAACG,KAAK,EAAE;IACtB,MAAM,IAAIb,OAAA,CAAA4C,yBAAyB,CAAC,8CAA8C,CAAC;EACrF;EACA,MAAM/B,KAAK,GAAGH,WAAW,CAACG,KAAK;EAE/B,MAAMK,EAAE,GAAGP,WAAW,CAACQ,MAAM;EAC7B,MAAMO,QAAQ,GAAGD,aAAa,CAACd,WAAW,CAACe,QAAQ,CAAC;EACpD,MAAMuB,QAAQ,GAAGtC,WAAW,CAACsC,QAAQ;EAErC,MAAMC,iBAAiB,GACrB3C,YAAY,KAAK,QAAQ,GAAG,IAAAX,UAAA,CAAAuD,QAAQ,EAACF,QAAQ,CAAC,GAAGG,cAAc,CAAC1B,QAAQ,EAAEuB,QAAQ,CAAC;EAErF,MAAMX,OAAO,GAAWT,MAAM,CAACwB,QAAQ,CAAC/B,QAAQ,CAACgB,OAAO,CAAC,GACrD,IAAIvC,MAAA,CAAAwC,MAAM,CAACjB,QAAQ,CAACgB,OAAO,CAAC,GAC5BhB,QAAQ,CAACgB,OAAO;EAEpB,MAAMgB,IAAI,GAAGC,YAAY,CAACjB,OAAO,CAAC;EAElC,MAAMkB,UAAU,GAAGC,QAAQ,CAACH,IAAI,CAACI,CAAC,EAAE,EAAE,CAAC;EACvC,IAAIF,UAAU,IAAIA,UAAU,GAAG,IAAI,EAAE;IACnC;IACA,MAAM,IAAIxD,OAAA,CAAA2D,iBAAiB,CAAC,8CAA8CH,UAAU,EAAE,CAAC;EACzF;EAEA,MAAMI,IAAI,GAAGN,IAAI,CAACO,CAAC;EACnB,MAAMC,MAAM,GAAGR,IAAI,CAACS,CAAC;EACrB,IAAID,MAAM,CAACE,UAAU,CAAC,OAAO,CAAC,EAAE;IAC9B;IACA,MAAM,IAAIhE,OAAA,CAAA2D,iBAAiB,CAAC,qCAAqCG,MAAM,EAAE,CAAC;EAC5E;EAEA;EACA,MAAMG,YAAY,GAAG,YAAYH,MAAM,EAAE;EACzC,MAAMI,cAAc,GAAGC,EAAE,CACvBjB,iBAAiB,EACjBrB,MAAM,CAACE,IAAI,CAAC6B,IAAI,EAAE,QAAQ,CAAC,EAC3BJ,UAAU,EACVjD,YAAY,CACb;EAED,MAAM6D,SAAS,GAAGC,IAAI,CAAC9D,YAAY,EAAE2D,cAAc,EAAE,YAAY,CAAC;EAClE,MAAMI,SAAS,GAAGD,IAAI,CAAC9D,YAAY,EAAE2D,cAAc,EAAE,YAAY,CAAC;EAClE,MAAMK,SAAS,GAAGC,CAAC,CAACjE,YAAY,EAAE6D,SAAS,CAAC;EAC5C,MAAMK,WAAW,GAAG,CAClB7C,sBAAsB,CAACF,QAAQ,EAAEb,KAAK,CAAC,EACvCyB,OAAO,CAACN,QAAQ,CAAC,MAAM,CAAC,EACxBiC,YAAY,CACb,CAACS,IAAI,CAAC,GAAG,CAAC;EAEX,MAAMC,eAAe,GAAGN,IAAI,CAAC9D,YAAY,EAAEgE,SAAS,EAAEE,WAAW,CAAC;EAClE,MAAMG,WAAW,GAAG,KAAKC,GAAG,CAACT,SAAS,EAAEO,eAAe,CAAC,EAAE;EAC1D,MAAMG,WAAW,GAAG,CAACb,YAAY,EAAEW,WAAW,CAAC,CAACF,IAAI,CAAC,GAAG,CAAC;EAEzD,MAAMK,eAAe,GAAGV,IAAI,CAAC9D,YAAY,EAAE+D,SAAS,EAAEG,WAAW,CAAC;EAClE,MAAMO,eAAe,GAAG;IACtBC,YAAY,EAAE,CAAC;IACfC,cAAc,EAAE5D,QAAQ,CAAC4D,cAAc;IACvC5C,OAAO,EAAE,IAAIvC,MAAA,CAAAwC,MAAM,CAACV,MAAM,CAACE,IAAI,CAAC+C,WAAW,CAAC;GAC7C;EAED,MAAMf,CAAC,GAAG,MAAMpB,UAAU,CAACG,OAAO,CAAC,IAAA7C,OAAA,CAAA8C,EAAE,EAAC,GAAG7B,EAAE,OAAO,CAAC,EAAE8D,eAAe,EAAEhC,SAAS,CAAC;EAChF,MAAMmC,cAAc,GAAG5B,YAAY,CAACQ,CAAC,CAACzB,OAAO,CAAC;EAE9C,IAAI,CAAC8C,aAAa,CAACvD,MAAM,CAACE,IAAI,CAACoD,cAAc,CAACE,CAAC,EAAE,QAAQ,CAAC,EAAEN,eAAe,CAAC,EAAE;IAC5E,MAAM,IAAI/E,OAAA,CAAA2D,iBAAiB,CAAC,sCAAsC,CAAC;EACrE;EAEA,IAAII,CAAC,CAACuB,IAAI,KAAK,KAAK,EAAE;IACpB;IACA;EACF;EAEA,MAAMC,oBAAoB,GAAG;IAC3BN,YAAY,EAAE,CAAC;IACfC,cAAc,EAAEnB,CAAC,CAACmB,cAAc;IAChC5C,OAAO,EAAET,MAAM,CAAC2D,KAAK,CAAC,CAAC;GACxB;EAED,MAAM7C,UAAU,CAACG,OAAO,CAAC,IAAA7C,OAAA,CAAA8C,EAAE,EAAC,GAAG7B,EAAE,OAAO,CAAC,EAAEqE,oBAAoB,EAAEvC,SAAS,CAAC;AAC7E;AAEA,SAASO,YAAYA,CAACjB,OAAe;EACnC,MAAMmD,UAAU,GAAGnD,OAAO,CAACN,QAAQ,CAAC,MAAM,CAAC;EAC3C,MAAMsB,IAAI,GAAa,EAAE;EACzB,MAAMoC,KAAK,GAAGD,UAAU,CAACE,KAAK,CAAC,GAAG,CAAC;EACnC,KAAK,IAAIjC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgC,KAAK,CAACE,MAAM,EAAElC,CAAC,EAAE,EAAE;IACrC,MAAMmC,UAAU,GAAG,CAACH,KAAK,CAAChC,CAAC,CAAC,CAACoC,KAAK,CAAC,gBAAgB,CAAC,IAAI,EAAE,EAAEC,KAAK,CAAC,CAAC,CAAC;IACpEzC,IAAI,CAACuC,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGA,UAAU,CAAC,CAAC,CAAC;EACrC;EACA,OAAOvC,IAAI;AACb;AAEA,SAASF,cAAcA,CAAC1B,QAAgB,EAAEuB,QAAgB;EACxD,IAAI,OAAOvB,QAAQ,KAAK,QAAQ,EAAE;IAChC,MAAM,IAAI1B,OAAA,CAAA4C,yBAAyB,CAAC,2BAA2B,CAAC;EAClE;EAEA,IAAI,OAAOK,QAAQ,KAAK,QAAQ,EAAE;IAChC,MAAM,IAAIjD,OAAA,CAAA4C,yBAAyB,CAAC,2BAA2B,CAAC;EAClE;EAEA,IAAIK,QAAQ,CAAC2C,MAAM,KAAK,CAAC,EAAE;IACzB,MAAM,IAAI5F,OAAA,CAAA4C,yBAAyB,CAAC,0BAA0B,CAAC;EACjE;EAEA,IAAIoD,GAAgB;EACpB,IAAI;IACFA,GAAG,GAAGlG,MAAM,CAACmG,UAAU,CAAC,KAAK,CAAC;EAChC,CAAC,CAAC,OAAOC,GAAG,EAAE;IACZ,IAAIpG,MAAM,CAACqG,OAAO,EAAE,EAAE;MACpB;MACA;MACA,MAAM,IAAIC,KAAK,CAAC,0DAA0D,CAAC;IAC7E;IACA,MAAMF,GAAG;EACX;EACAF,GAAG,CAACK,MAAM,CAAC,GAAG3E,QAAQ,UAAUuB,QAAQ,EAAE,EAAE,MAAM,CAAC;EACnD,OAAO+C,GAAG,CAACM,MAAM,CAAC,KAAK,CAAC;AAC1B;AAEA;AACA,SAASzB,GAAGA,CAAC0B,CAAS,EAAEC,CAAS;EAC/B,IAAI,CAAC3E,MAAM,CAACwB,QAAQ,CAACkD,CAAC,CAAC,EAAE;IACvBA,CAAC,GAAG1E,MAAM,CAACE,IAAI,CAACwE,CAAC,CAAC;EACpB;EAEA,IAAI,CAAC1E,MAAM,CAACwB,QAAQ,CAACmD,CAAC,CAAC,EAAE;IACvBA,CAAC,GAAG3E,MAAM,CAACE,IAAI,CAACyE,CAAC,CAAC;EACpB;EAEA,MAAMZ,MAAM,GAAGa,IAAI,CAACC,GAAG,CAACH,CAAC,CAACX,MAAM,EAAEY,CAAC,CAACZ,MAAM,CAAC;EAC3C,MAAMe,GAAG,GAAG,EAAE;EAEd,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkC,MAAM,EAAElC,CAAC,IAAI,CAAC,EAAE;IAClCiD,GAAG,CAACC,IAAI,CAACL,CAAC,CAAC7C,CAAC,CAAC,GAAG8C,CAAC,CAAC9C,CAAC,CAAC,CAAC;EACvB;EAEA,OAAO7B,MAAM,CAACE,IAAI,CAAC4E,GAAG,CAAC,CAAC3E,QAAQ,CAAC,QAAQ,CAAC;AAC5C;AAEA,SAASwC,CAACA,CAACqC,MAAoB,EAAEC,IAAY;EAC3C,OAAOhH,MAAM,CAACmG,UAAU,CAACY,MAAM,CAAC,CAACR,MAAM,CAACS,IAAI,CAAC,CAACR,MAAM,EAAE;AACxD;AAEA,SAASjC,IAAIA,CAACwC,MAAoB,EAAEE,GAAW,EAAED,IAAqB;EACpE,OAAOhH,MAAM,CAACkH,UAAU,CAACH,MAAM,EAAEE,GAAG,CAAC,CAACV,MAAM,CAACS,IAAI,CAAC,CAACR,MAAM,EAAE;AAC7D;AAMA,IAAIW,QAAQ,GAAY,EAAE;AAC1B,IAAIC,aAAa,GAAG,CAAC;AACrB,SAASC,aAAaA,CAAA;EACpBF,QAAQ,GAAG,EAAE;EACbC,aAAa,GAAG,CAAC;AACnB;AAEA,MAAME,WAAW,GAAG;EAClBC,MAAM,EAAE,EAAE;EACVC,IAAI,EAAE;CACP;AAED,SAASnD,EAAEA,CAACoD,IAAY,EAAE3D,IAAY,EAAEJ,UAAkB,EAAEjD,YAA0B;EACpF;EACA,MAAMwG,GAAG,GAAG,CAACQ,IAAI,EAAE3D,IAAI,CAAC5B,QAAQ,CAAC,QAAQ,CAAC,EAAEwB,UAAU,CAAC,CAACkB,IAAI,CAAC,GAAG,CAAC;EACjE,IAAIuC,QAAQ,CAACF,GAAG,CAAC,IAAI,IAAI,EAAE;IACzB,OAAOE,QAAQ,CAACF,GAAG,CAAC;EACtB;EAEA;EACA,MAAMS,UAAU,GAAG1H,MAAM,CAAC2H,UAAU,CAClCF,IAAI,EACJ3D,IAAI,EACJJ,UAAU,EACV4D,WAAW,CAAC7G,YAAY,CAAC,EACzBA,YAAY,CACb;EAED;EACA,IAAI2G,aAAa,IAAI,GAAG,EAAE;IACxBC,aAAa,EAAE;EACjB;EAEAF,QAAQ,CAACF,GAAG,CAAC,GAAGS,UAAU;EAC1BN,aAAa,IAAI,CAAC;EAClB,OAAOM,UAAU;AACnB;AAEA,SAASpC,aAAaA,CAACsC,GAAW,EAAEC,GAAe;EACjD,IAAID,GAAG,CAAC9B,MAAM,KAAK+B,GAAG,CAAC/B,MAAM,EAAE;IAC7B,OAAO,KAAK;EACd;EAEA,IAAI,OAAO9F,MAAM,CAAC8H,eAAe,KAAK,UAAU,EAAE;IAChD,OAAO9H,MAAM,CAAC8H,eAAe,CAACF,GAAG,EAAEC,GAAG,CAAC;EACzC;EAEA,IAAIE,MAAM,GAAG,CAAC;EACd,KAAK,IAAInE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGgE,GAAG,CAAC9B,MAAM,EAAElC,CAAC,EAAE,EAAE;IACnCmE,MAAM,IAAIH,GAAG,CAAChE,CAAC,CAAC,GAAGiE,GAAG,CAACjE,CAAC,CAAC;EAC3B;EAEA,OAAOmE,MAAM,KAAK,CAAC;AACrB;AAEA,MAAaC,SAAU,SAAQ1H,QAAQ;EACrCE,YAAA;IACE,KAAK,CAAC,MAAM,CAAC;EACf;;AAHFyH,OAAA,CAAAD,SAAA,GAAAA,SAAA;AAMA,MAAaE,WAAY,SAAQ5H,QAAQ;EACvCE,YAAA;IACE,KAAK,CAAC,QAAQ,CAAC;EACjB;;AAHFyH,OAAA,CAAAC,WAAA,GAAAA,WAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}