{"ast": null, "code": "import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { monthsInQuarter } from \"../constants/index.js\";\n/**\n * @name monthsToQuarters\n * @category Conversion Helpers\n * @summary Convert number of months to quarters.\n *\n * @description\n * Convert a number of months to a full number of quarters.\n *\n * @param {number} months - number of months to be converted.\n *\n * @returns {number} the number of months converted in quarters\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 6 months to quarters:\n * const result = monthsToQuarters(6)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = monthsToQuarters(7)\n * //=> 2\n */\nexport default function monthsToQuarters(months) {\n  requiredArgs(1, arguments);\n  var quarters = months / monthsInQuarter;\n  return Math.floor(quarters);\n}", "map": {"version": 3, "names": ["requiredArgs", "monthsInQuarter", "monthsToQuarters", "months", "arguments", "quarters", "Math", "floor"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/date-fns/esm/monthsToQuarters/index.js"], "sourcesContent": ["import requiredArgs from \"../_lib/requiredArgs/index.js\";\nimport { monthsInQuarter } from \"../constants/index.js\";\n/**\n * @name monthsToQuarters\n * @category Conversion Helpers\n * @summary Convert number of months to quarters.\n *\n * @description\n * Convert a number of months to a full number of quarters.\n *\n * @param {number} months - number of months to be converted.\n *\n * @returns {number} the number of months converted in quarters\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Convert 6 months to quarters:\n * const result = monthsToQuarters(6)\n * //=> 2\n *\n * @example\n * // It uses floor rounding:\n * const result = monthsToQuarters(7)\n * //=> 2\n */\nexport default function monthsToQuarters(months) {\n  requiredArgs(1, arguments);\n  var quarters = months / monthsInQuarter;\n  return Math.floor(quarters);\n}"], "mappings": "AAAA,OAAOA,YAAY,MAAM,+BAA+B;AACxD,SAASC,eAAe,QAAQ,uBAAuB;AACvD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,gBAAgBA,CAACC,MAAM,EAAE;EAC/CH,YAAY,CAAC,CAAC,EAAEI,SAAS,CAAC;EAC1B,IAAIC,QAAQ,GAAGF,MAAM,GAAGF,eAAe;EACvC,OAAOK,IAAI,CAACC,KAAK,CAACF,QAAQ,CAAC;AAC7B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}