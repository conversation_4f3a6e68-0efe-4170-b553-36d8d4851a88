{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.ServerCapabilities = exports.Topology = void 0;\nconst connection_string_1 = require(\"../connection_string\");\nconst constants_1 = require(\"../constants\");\nconst error_1 = require(\"../error\");\nconst mongo_logger_1 = require(\"../mongo_logger\");\nconst mongo_types_1 = require(\"../mongo_types\");\nconst read_preference_1 = require(\"../read_preference\");\nconst timeout_1 = require(\"../timeout\");\nconst utils_1 = require(\"../utils\");\nconst common_1 = require(\"./common\");\nconst events_1 = require(\"./events\");\nconst server_1 = require(\"./server\");\nconst server_description_1 = require(\"./server_description\");\nconst server_selection_1 = require(\"./server_selection\");\nconst server_selection_events_1 = require(\"./server_selection_events\");\nconst srv_polling_1 = require(\"./srv_polling\");\nconst topology_description_1 = require(\"./topology_description\");\n// Global state\nlet globalTopologyCounter = 0;\nconst stateTransition = (0, utils_1.makeStateMachine)({\n  [common_1.STATE_CLOSED]: [common_1.STATE_CLOSED, common_1.STATE_CONNECTING],\n  [common_1.STATE_CONNECTING]: [common_1.STATE_CONNECTING, common_1.STATE_CLOSING, common_1.STATE_CONNECTED, common_1.STATE_CLOSED],\n  [common_1.STATE_CONNECTED]: [common_1.STATE_CONNECTED, common_1.STATE_CLOSING, common_1.STATE_CLOSED],\n  [common_1.STATE_CLOSING]: [common_1.STATE_CLOSING, common_1.STATE_CLOSED]\n});\n/**\n * A container of server instances representing a connection to a MongoDB topology.\n * @internal\n */\nclass Topology extends mongo_types_1.TypedEventEmitter {\n  /**\n   * @param seedlist - a list of HostAddress instances to connect to\n   */\n  constructor(client, seeds, options) {\n    super();\n    this.on('error', utils_1.noop);\n    this.client = client;\n    // Options should only be undefined in tests, MongoClient will always have defined options\n    options = options ?? {\n      hosts: [utils_1.HostAddress.fromString('localhost:27017')],\n      ...Object.fromEntries(connection_string_1.DEFAULT_OPTIONS.entries())\n    };\n    if (typeof seeds === 'string') {\n      seeds = [utils_1.HostAddress.fromString(seeds)];\n    } else if (!Array.isArray(seeds)) {\n      seeds = [seeds];\n    }\n    const seedlist = [];\n    for (const seed of seeds) {\n      if (typeof seed === 'string') {\n        seedlist.push(utils_1.HostAddress.fromString(seed));\n      } else if (seed instanceof utils_1.HostAddress) {\n        seedlist.push(seed);\n      } else {\n        // FIXME(NODE-3483): May need to be a MongoParseError\n        throw new error_1.MongoRuntimeError(`Topology cannot be constructed from ${JSON.stringify(seed)}`);\n      }\n    }\n    const topologyType = topologyTypeFromOptions(options);\n    const topologyId = globalTopologyCounter++;\n    const selectedHosts = options.srvMaxHosts == null || options.srvMaxHosts === 0 || options.srvMaxHosts >= seedlist.length ? seedlist : (0, utils_1.shuffle)(seedlist, options.srvMaxHosts);\n    const serverDescriptions = new Map();\n    for (const hostAddress of selectedHosts) {\n      serverDescriptions.set(hostAddress.toString(), new server_description_1.ServerDescription(hostAddress));\n    }\n    this.waitQueue = new utils_1.List();\n    this.s = {\n      // the id of this topology\n      id: topologyId,\n      // passed in options\n      options,\n      // initial seedlist of servers to connect to\n      seedlist,\n      // initial state\n      state: common_1.STATE_CLOSED,\n      // the topology description\n      description: new topology_description_1.TopologyDescription(topologyType, serverDescriptions, options.replicaSet, undefined, undefined, undefined, options),\n      serverSelectionTimeoutMS: options.serverSelectionTimeoutMS,\n      heartbeatFrequencyMS: options.heartbeatFrequencyMS,\n      minHeartbeatFrequencyMS: options.minHeartbeatFrequencyMS,\n      // a map of server instances to normalized addresses\n      servers: new Map(),\n      credentials: options?.credentials,\n      clusterTime: undefined,\n      detectShardedTopology: ev => this.detectShardedTopology(ev),\n      detectSrvRecords: ev => this.detectSrvRecords(ev)\n    };\n    this.mongoLogger = client.mongoLogger;\n    this.component = 'topology';\n    if (options.srvHost && !options.loadBalanced) {\n      this.s.srvPoller = options.srvPoller ?? new srv_polling_1.SrvPoller({\n        heartbeatFrequencyMS: this.s.heartbeatFrequencyMS,\n        srvHost: options.srvHost,\n        srvMaxHosts: options.srvMaxHosts,\n        srvServiceName: options.srvServiceName\n      });\n      this.on(Topology.TOPOLOGY_DESCRIPTION_CHANGED, this.s.detectShardedTopology);\n    }\n    this.connectionLock = undefined;\n  }\n  detectShardedTopology(event) {\n    const previousType = event.previousDescription.type;\n    const newType = event.newDescription.type;\n    const transitionToSharded = previousType !== common_1.TopologyType.Sharded && newType === common_1.TopologyType.Sharded;\n    const srvListeners = this.s.srvPoller?.listeners(srv_polling_1.SrvPoller.SRV_RECORD_DISCOVERY);\n    const listeningToSrvPolling = !!srvListeners?.includes(this.s.detectSrvRecords);\n    if (transitionToSharded && !listeningToSrvPolling) {\n      this.s.srvPoller?.on(srv_polling_1.SrvPoller.SRV_RECORD_DISCOVERY, this.s.detectSrvRecords);\n      this.s.srvPoller?.start();\n    }\n  }\n  detectSrvRecords(ev) {\n    const previousTopologyDescription = this.s.description;\n    this.s.description = this.s.description.updateFromSrvPollingEvent(ev, this.s.options.srvMaxHosts);\n    if (this.s.description === previousTopologyDescription) {\n      // Nothing changed, so return\n      return;\n    }\n    updateServers(this);\n    this.emitAndLog(Topology.TOPOLOGY_DESCRIPTION_CHANGED, new events_1.TopologyDescriptionChangedEvent(this.s.id, previousTopologyDescription, this.s.description));\n  }\n  /**\n   * @returns A `TopologyDescription` for this topology\n   */\n  get description() {\n    return this.s.description;\n  }\n  get loadBalanced() {\n    return this.s.options.loadBalanced;\n  }\n  get serverApi() {\n    return this.s.options.serverApi;\n  }\n  get capabilities() {\n    return new ServerCapabilities(this.lastHello());\n  }\n  /** Initiate server connect */\n  async connect(options) {\n    this.connectionLock ??= this._connect(options);\n    try {\n      await this.connectionLock;\n      return this;\n    } finally {\n      this.connectionLock = undefined;\n    }\n  }\n  async _connect(options) {\n    options = options ?? {};\n    if (this.s.state === common_1.STATE_CONNECTED) {\n      return this;\n    }\n    stateTransition(this, common_1.STATE_CONNECTING);\n    // emit SDAM monitoring events\n    this.emitAndLog(Topology.TOPOLOGY_OPENING, new events_1.TopologyOpeningEvent(this.s.id));\n    // emit an event for the topology change\n    this.emitAndLog(Topology.TOPOLOGY_DESCRIPTION_CHANGED, new events_1.TopologyDescriptionChangedEvent(this.s.id, new topology_description_1.TopologyDescription(common_1.TopologyType.Unknown),\n    // initial is always Unknown\n    this.s.description));\n    // connect all known servers, then attempt server selection to connect\n    const serverDescriptions = Array.from(this.s.description.servers.values());\n    this.s.servers = new Map(serverDescriptions.map(serverDescription => [serverDescription.address, createAndConnectServer(this, serverDescription)]));\n    // In load balancer mode we need to fake a server description getting\n    // emitted from the monitor, since the monitor doesn't exist.\n    if (this.s.options.loadBalanced) {\n      for (const description of serverDescriptions) {\n        const newDescription = new server_description_1.ServerDescription(description.hostAddress, undefined, {\n          loadBalanced: this.s.options.loadBalanced\n        });\n        this.serverUpdateHandler(newDescription);\n      }\n    }\n    const serverSelectionTimeoutMS = this.client.s.options.serverSelectionTimeoutMS;\n    const readPreference = options.readPreference ?? read_preference_1.ReadPreference.primary;\n    const timeoutContext = timeout_1.TimeoutContext.create({\n      // TODO(NODE-6448): auto-connect ignores timeoutMS; potential future feature\n      timeoutMS: undefined,\n      serverSelectionTimeoutMS,\n      waitQueueTimeoutMS: this.client.s.options.waitQueueTimeoutMS\n    });\n    const selectServerOptions = {\n      operationName: 'ping',\n      ...options,\n      timeoutContext\n    };\n    try {\n      const server = await this.selectServer((0, server_selection_1.readPreferenceServerSelector)(readPreference), selectServerOptions);\n      const skipPingOnConnect = this.s.options.__skipPingOnConnect === true;\n      if (!skipPingOnConnect && this.s.credentials) {\n        await server.command((0, utils_1.ns)('admin.$cmd'), {\n          ping: 1\n        }, {\n          timeoutContext\n        });\n        stateTransition(this, common_1.STATE_CONNECTED);\n        this.emit(Topology.OPEN, this);\n        this.emit(Topology.CONNECT, this);\n        return this;\n      }\n      stateTransition(this, common_1.STATE_CONNECTED);\n      this.emit(Topology.OPEN, this);\n      this.emit(Topology.CONNECT, this);\n      return this;\n    } catch (error) {\n      this.close();\n      throw error;\n    }\n  }\n  /** Close this topology */\n  close() {\n    if (this.s.state === common_1.STATE_CLOSED || this.s.state === common_1.STATE_CLOSING) {\n      return;\n    }\n    for (const server of this.s.servers.values()) {\n      destroyServer(server, this);\n    }\n    this.s.servers.clear();\n    stateTransition(this, common_1.STATE_CLOSING);\n    drainWaitQueue(this.waitQueue, new error_1.MongoTopologyClosedError());\n    if (this.s.srvPoller) {\n      this.s.srvPoller.stop();\n      this.s.srvPoller.removeListener(srv_polling_1.SrvPoller.SRV_RECORD_DISCOVERY, this.s.detectSrvRecords);\n    }\n    this.removeListener(Topology.TOPOLOGY_DESCRIPTION_CHANGED, this.s.detectShardedTopology);\n    stateTransition(this, common_1.STATE_CLOSED);\n    // emit an event for close\n    this.emitAndLog(Topology.TOPOLOGY_CLOSED, new events_1.TopologyClosedEvent(this.s.id));\n  }\n  /**\n   * Selects a server according to the selection predicate provided\n   *\n   * @param selector - An optional selector to select servers by, defaults to a random selection within a latency window\n   * @param options - Optional settings related to server selection\n   * @param callback - The callback used to indicate success or failure\n   * @returns An instance of a `Server` meeting the criteria of the predicate provided\n   */\n  async selectServer(selector, options) {\n    let serverSelector;\n    if (typeof selector !== 'function') {\n      if (typeof selector === 'string') {\n        serverSelector = (0, server_selection_1.readPreferenceServerSelector)(read_preference_1.ReadPreference.fromString(selector));\n      } else {\n        let readPreference;\n        if (selector instanceof read_preference_1.ReadPreference) {\n          readPreference = selector;\n        } else {\n          read_preference_1.ReadPreference.translate(options);\n          readPreference = options.readPreference || read_preference_1.ReadPreference.primary;\n        }\n        serverSelector = (0, server_selection_1.readPreferenceServerSelector)(readPreference);\n      }\n    } else {\n      serverSelector = selector;\n    }\n    options = {\n      serverSelectionTimeoutMS: this.s.serverSelectionTimeoutMS,\n      ...options\n    };\n    if (this.client.mongoLogger?.willLog(mongo_logger_1.MongoLoggableComponent.SERVER_SELECTION, mongo_logger_1.SeverityLevel.DEBUG)) {\n      this.client.mongoLogger?.debug(mongo_logger_1.MongoLoggableComponent.SERVER_SELECTION, new server_selection_events_1.ServerSelectionStartedEvent(selector, this.description, options.operationName));\n    }\n    let timeout;\n    if (options.timeoutContext) timeout = options.timeoutContext.serverSelectionTimeout;else {\n      timeout = timeout_1.Timeout.expires(options.serverSelectionTimeoutMS ?? 0);\n    }\n    const isSharded = this.description.type === common_1.TopologyType.Sharded;\n    const session = options.session;\n    const transaction = session && session.transaction;\n    if (isSharded && transaction && transaction.server) {\n      if (this.client.mongoLogger?.willLog(mongo_logger_1.MongoLoggableComponent.SERVER_SELECTION, mongo_logger_1.SeverityLevel.DEBUG)) {\n        this.client.mongoLogger?.debug(mongo_logger_1.MongoLoggableComponent.SERVER_SELECTION, new server_selection_events_1.ServerSelectionSucceededEvent(selector, this.description, transaction.server.pool.address, options.operationName));\n      }\n      if (options.timeoutContext?.clearServerSelectionTimeout) timeout?.clear();\n      return transaction.server;\n    }\n    const {\n      promise: serverPromise,\n      resolve,\n      reject\n    } = (0, utils_1.promiseWithResolvers)();\n    const waitQueueMember = {\n      serverSelector,\n      topologyDescription: this.description,\n      mongoLogger: this.client.mongoLogger,\n      transaction,\n      resolve,\n      reject,\n      cancelled: false,\n      startTime: (0, utils_1.now)(),\n      operationName: options.operationName,\n      waitingLogged: false,\n      previousServer: options.previousServer\n    };\n    const abortListener = (0, utils_1.addAbortListener)(options.signal, function () {\n      waitQueueMember.cancelled = true;\n      reject(this.reason);\n    });\n    this.waitQueue.push(waitQueueMember);\n    processWaitQueue(this);\n    try {\n      timeout?.throwIfExpired();\n      const server = await (timeout ? Promise.race([serverPromise, timeout]) : serverPromise);\n      if (options.timeoutContext?.csotEnabled() && server.description.minRoundTripTime !== 0) {\n        options.timeoutContext.minRoundTripTime = server.description.minRoundTripTime;\n      }\n      return server;\n    } catch (error) {\n      if (timeout_1.TimeoutError.is(error)) {\n        // Timeout\n        waitQueueMember.cancelled = true;\n        const timeoutError = new error_1.MongoServerSelectionError(`Server selection timed out after ${timeout?.duration} ms`, this.description);\n        if (this.client.mongoLogger?.willLog(mongo_logger_1.MongoLoggableComponent.SERVER_SELECTION, mongo_logger_1.SeverityLevel.DEBUG)) {\n          this.client.mongoLogger?.debug(mongo_logger_1.MongoLoggableComponent.SERVER_SELECTION, new server_selection_events_1.ServerSelectionFailedEvent(selector, this.description, timeoutError, options.operationName));\n        }\n        if (options.timeoutContext?.csotEnabled()) {\n          throw new error_1.MongoOperationTimeoutError('Timed out during server selection', {\n            cause: timeoutError\n          });\n        }\n        throw timeoutError;\n      }\n      // Other server selection error\n      throw error;\n    } finally {\n      abortListener?.[utils_1.kDispose]();\n      if (options.timeoutContext?.clearServerSelectionTimeout) timeout?.clear();\n    }\n  }\n  /**\n   * Update the internal TopologyDescription with a ServerDescription\n   *\n   * @param serverDescription - The server to update in the internal list of server descriptions\n   */\n  serverUpdateHandler(serverDescription) {\n    if (!this.s.description.hasServer(serverDescription.address)) {\n      return;\n    }\n    // ignore this server update if its from an outdated topologyVersion\n    if (isStaleServerDescription(this.s.description, serverDescription)) {\n      return;\n    }\n    // these will be used for monitoring events later\n    const previousTopologyDescription = this.s.description;\n    const previousServerDescription = this.s.description.servers.get(serverDescription.address);\n    if (!previousServerDescription) {\n      return;\n    }\n    // Driver Sessions Spec: \"Whenever a driver receives a cluster time from\n    // a server it MUST compare it to the current highest seen cluster time\n    // for the deployment. If the new cluster time is higher than the\n    // highest seen cluster time it MUST become the new highest seen cluster\n    // time. Two cluster times are compared using only the BsonTimestamp\n    // value of the clusterTime embedded field.\"\n    const clusterTime = serverDescription.$clusterTime;\n    if (clusterTime) {\n      (0, common_1._advanceClusterTime)(this, clusterTime);\n    }\n    // If we already know all the information contained in this updated description, then\n    // we don't need to emit SDAM events, but still need to update the description, in order\n    // to keep client-tracked attributes like last update time and round trip time up to date\n    const equalDescriptions = previousServerDescription && previousServerDescription.equals(serverDescription);\n    // first update the TopologyDescription\n    this.s.description = this.s.description.update(serverDescription);\n    if (this.s.description.compatibilityError) {\n      this.emit(Topology.ERROR, new error_1.MongoCompatibilityError(this.s.description.compatibilityError));\n      return;\n    }\n    // emit monitoring events for this change\n    if (!equalDescriptions) {\n      const newDescription = this.s.description.servers.get(serverDescription.address);\n      if (newDescription) {\n        this.emit(Topology.SERVER_DESCRIPTION_CHANGED, new events_1.ServerDescriptionChangedEvent(this.s.id, serverDescription.address, previousServerDescription, newDescription));\n      }\n    }\n    // update server list from updated descriptions\n    updateServers(this, serverDescription);\n    // attempt to resolve any outstanding server selection attempts\n    if (this.waitQueue.length > 0) {\n      processWaitQueue(this);\n    }\n    if (!equalDescriptions) {\n      this.emitAndLog(Topology.TOPOLOGY_DESCRIPTION_CHANGED, new events_1.TopologyDescriptionChangedEvent(this.s.id, previousTopologyDescription, this.s.description));\n    }\n  }\n  auth(credentials, callback) {\n    if (typeof credentials === 'function') callback = credentials, credentials = undefined;\n    if (typeof callback === 'function') callback(undefined, true);\n  }\n  get clientMetadata() {\n    return this.s.options.metadata;\n  }\n  isConnected() {\n    return this.s.state === common_1.STATE_CONNECTED;\n  }\n  isDestroyed() {\n    return this.s.state === common_1.STATE_CLOSED;\n  }\n  // NOTE: There are many places in code where we explicitly check the last hello\n  //       to do feature support detection. This should be done any other way, but for\n  //       now we will just return the first hello seen, which should suffice.\n  lastHello() {\n    const serverDescriptions = Array.from(this.description.servers.values());\n    if (serverDescriptions.length === 0) return {};\n    const sd = serverDescriptions.filter(sd => sd.type !== common_1.ServerType.Unknown)[0];\n    const result = sd || {\n      maxWireVersion: this.description.commonWireVersion\n    };\n    return result;\n  }\n  get commonWireVersion() {\n    return this.description.commonWireVersion;\n  }\n  get logicalSessionTimeoutMinutes() {\n    return this.description.logicalSessionTimeoutMinutes;\n  }\n  get clusterTime() {\n    return this.s.clusterTime;\n  }\n  set clusterTime(clusterTime) {\n    this.s.clusterTime = clusterTime;\n  }\n}\nexports.Topology = Topology;\n/** @event */\nTopology.SERVER_OPENING = constants_1.SERVER_OPENING;\n/** @event */\nTopology.SERVER_CLOSED = constants_1.SERVER_CLOSED;\n/** @event */\nTopology.SERVER_DESCRIPTION_CHANGED = constants_1.SERVER_DESCRIPTION_CHANGED;\n/** @event */\nTopology.TOPOLOGY_OPENING = constants_1.TOPOLOGY_OPENING;\n/** @event */\nTopology.TOPOLOGY_CLOSED = constants_1.TOPOLOGY_CLOSED;\n/** @event */\nTopology.TOPOLOGY_DESCRIPTION_CHANGED = constants_1.TOPOLOGY_DESCRIPTION_CHANGED;\n/** @event */\nTopology.ERROR = constants_1.ERROR;\n/** @event */\nTopology.OPEN = constants_1.OPEN;\n/** @event */\nTopology.CONNECT = constants_1.CONNECT;\n/** @event */\nTopology.CLOSE = constants_1.CLOSE;\n/** @event */\nTopology.TIMEOUT = constants_1.TIMEOUT;\n/** Destroys a server, and removes all event listeners from the instance */\nfunction destroyServer(server, topology) {\n  for (const event of constants_1.LOCAL_SERVER_EVENTS) {\n    server.removeAllListeners(event);\n  }\n  server.destroy();\n  topology.emitAndLog(Topology.SERVER_CLOSED, new events_1.ServerClosedEvent(topology.s.id, server.description.address));\n  for (const event of constants_1.SERVER_RELAY_EVENTS) {\n    server.removeAllListeners(event);\n  }\n}\n/** Predicts the TopologyType from options */\nfunction topologyTypeFromOptions(options) {\n  if (options?.directConnection) {\n    return common_1.TopologyType.Single;\n  }\n  if (options?.replicaSet) {\n    return common_1.TopologyType.ReplicaSetNoPrimary;\n  }\n  if (options?.loadBalanced) {\n    return common_1.TopologyType.LoadBalanced;\n  }\n  return common_1.TopologyType.Unknown;\n}\n/**\n * Creates new server instances and attempts to connect them\n *\n * @param topology - The topology that this server belongs to\n * @param serverDescription - The description for the server to initialize and connect to\n */\nfunction createAndConnectServer(topology, serverDescription) {\n  topology.emitAndLog(Topology.SERVER_OPENING, new events_1.ServerOpeningEvent(topology.s.id, serverDescription.address));\n  const server = new server_1.Server(topology, serverDescription, topology.s.options);\n  for (const event of constants_1.SERVER_RELAY_EVENTS) {\n    server.on(event, e => topology.emit(event, e));\n  }\n  server.on(server_1.Server.DESCRIPTION_RECEIVED, description => topology.serverUpdateHandler(description));\n  server.connect();\n  return server;\n}\n/**\n * @param topology - Topology to update.\n * @param incomingServerDescription - New server description.\n */\nfunction updateServers(topology, incomingServerDescription) {\n  // update the internal server's description\n  if (incomingServerDescription && topology.s.servers.has(incomingServerDescription.address)) {\n    const server = topology.s.servers.get(incomingServerDescription.address);\n    if (server) {\n      server.s.description = incomingServerDescription;\n      if (incomingServerDescription.error instanceof error_1.MongoError && incomingServerDescription.error.hasErrorLabel(error_1.MongoErrorLabel.ResetPool)) {\n        const interruptInUseConnections = incomingServerDescription.error.hasErrorLabel(error_1.MongoErrorLabel.InterruptInUseConnections);\n        server.pool.clear({\n          interruptInUseConnections\n        });\n      } else if (incomingServerDescription.error == null) {\n        const newTopologyType = topology.s.description.type;\n        const shouldMarkPoolReady = incomingServerDescription.isDataBearing || incomingServerDescription.type !== common_1.ServerType.Unknown && newTopologyType === common_1.TopologyType.Single;\n        if (shouldMarkPoolReady) {\n          server.pool.ready();\n        }\n      }\n    }\n  }\n  // add new servers for all descriptions we currently don't know about locally\n  for (const serverDescription of topology.description.servers.values()) {\n    if (!topology.s.servers.has(serverDescription.address)) {\n      const server = createAndConnectServer(topology, serverDescription);\n      topology.s.servers.set(serverDescription.address, server);\n    }\n  }\n  // for all servers no longer known, remove their descriptions and destroy their instances\n  for (const entry of topology.s.servers) {\n    const serverAddress = entry[0];\n    if (topology.description.hasServer(serverAddress)) {\n      continue;\n    }\n    if (!topology.s.servers.has(serverAddress)) {\n      continue;\n    }\n    const server = topology.s.servers.get(serverAddress);\n    topology.s.servers.delete(serverAddress);\n    // prepare server for garbage collection\n    if (server) {\n      destroyServer(server, topology);\n    }\n  }\n}\nfunction drainWaitQueue(queue, drainError) {\n  while (queue.length) {\n    const waitQueueMember = queue.shift();\n    if (!waitQueueMember) {\n      continue;\n    }\n    if (!waitQueueMember.cancelled) {\n      if (waitQueueMember.mongoLogger?.willLog(mongo_logger_1.MongoLoggableComponent.SERVER_SELECTION, mongo_logger_1.SeverityLevel.DEBUG)) {\n        waitQueueMember.mongoLogger?.debug(mongo_logger_1.MongoLoggableComponent.SERVER_SELECTION, new server_selection_events_1.ServerSelectionFailedEvent(waitQueueMember.serverSelector, waitQueueMember.topologyDescription, drainError, waitQueueMember.operationName));\n      }\n      waitQueueMember.reject(drainError);\n    }\n  }\n}\nfunction processWaitQueue(topology) {\n  if (topology.s.state === common_1.STATE_CLOSED) {\n    drainWaitQueue(topology.waitQueue, new error_1.MongoTopologyClosedError());\n    return;\n  }\n  const isSharded = topology.description.type === common_1.TopologyType.Sharded;\n  const serverDescriptions = Array.from(topology.description.servers.values());\n  const membersToProcess = topology.waitQueue.length;\n  for (let i = 0; i < membersToProcess; ++i) {\n    const waitQueueMember = topology.waitQueue.shift();\n    if (!waitQueueMember) {\n      continue;\n    }\n    if (waitQueueMember.cancelled) {\n      continue;\n    }\n    let selectedDescriptions;\n    try {\n      const serverSelector = waitQueueMember.serverSelector;\n      const previousServer = waitQueueMember.previousServer;\n      selectedDescriptions = serverSelector ? serverSelector(topology.description, serverDescriptions, previousServer ? [previousServer] : []) : serverDescriptions;\n    } catch (selectorError) {\n      if (topology.client.mongoLogger?.willLog(mongo_logger_1.MongoLoggableComponent.SERVER_SELECTION, mongo_logger_1.SeverityLevel.DEBUG)) {\n        topology.client.mongoLogger?.debug(mongo_logger_1.MongoLoggableComponent.SERVER_SELECTION, new server_selection_events_1.ServerSelectionFailedEvent(waitQueueMember.serverSelector, topology.description, selectorError, waitQueueMember.operationName));\n      }\n      waitQueueMember.reject(selectorError);\n      continue;\n    }\n    let selectedServer;\n    if (selectedDescriptions.length === 0) {\n      if (!waitQueueMember.waitingLogged) {\n        if (topology.client.mongoLogger?.willLog(mongo_logger_1.MongoLoggableComponent.SERVER_SELECTION, mongo_logger_1.SeverityLevel.INFORMATIONAL)) {\n          topology.client.mongoLogger?.info(mongo_logger_1.MongoLoggableComponent.SERVER_SELECTION, new server_selection_events_1.WaitingForSuitableServerEvent(waitQueueMember.serverSelector, topology.description, topology.s.serverSelectionTimeoutMS !== 0 ? topology.s.serverSelectionTimeoutMS - ((0, utils_1.now)() - waitQueueMember.startTime) : -1, waitQueueMember.operationName));\n        }\n        waitQueueMember.waitingLogged = true;\n      }\n      topology.waitQueue.push(waitQueueMember);\n      continue;\n    } else if (selectedDescriptions.length === 1) {\n      selectedServer = topology.s.servers.get(selectedDescriptions[0].address);\n    } else {\n      const descriptions = (0, utils_1.shuffle)(selectedDescriptions, 2);\n      const server1 = topology.s.servers.get(descriptions[0].address);\n      const server2 = topology.s.servers.get(descriptions[1].address);\n      selectedServer = server1 && server2 && server1.s.operationCount < server2.s.operationCount ? server1 : server2;\n    }\n    if (!selectedServer) {\n      const serverSelectionError = new error_1.MongoServerSelectionError('server selection returned a server description but the server was not found in the topology', topology.description);\n      if (topology.client.mongoLogger?.willLog(mongo_logger_1.MongoLoggableComponent.SERVER_SELECTION, mongo_logger_1.SeverityLevel.DEBUG)) {\n        topology.client.mongoLogger?.debug(mongo_logger_1.MongoLoggableComponent.SERVER_SELECTION, new server_selection_events_1.ServerSelectionFailedEvent(waitQueueMember.serverSelector, topology.description, serverSelectionError, waitQueueMember.operationName));\n      }\n      waitQueueMember.reject(serverSelectionError);\n      return;\n    }\n    const transaction = waitQueueMember.transaction;\n    if (isSharded && transaction && transaction.isActive && selectedServer) {\n      transaction.pinServer(selectedServer);\n    }\n    if (topology.client.mongoLogger?.willLog(mongo_logger_1.MongoLoggableComponent.SERVER_SELECTION, mongo_logger_1.SeverityLevel.DEBUG)) {\n      topology.client.mongoLogger?.debug(mongo_logger_1.MongoLoggableComponent.SERVER_SELECTION, new server_selection_events_1.ServerSelectionSucceededEvent(waitQueueMember.serverSelector, waitQueueMember.topologyDescription, selectedServer.pool.address, waitQueueMember.operationName));\n    }\n    waitQueueMember.resolve(selectedServer);\n  }\n  if (topology.waitQueue.length > 0) {\n    // ensure all server monitors attempt monitoring soon\n    for (const [, server] of topology.s.servers) {\n      process.nextTick(function scheduleServerCheck() {\n        return server.requestCheck();\n      });\n    }\n  }\n}\nfunction isStaleServerDescription(topologyDescription, incomingServerDescription) {\n  const currentServerDescription = topologyDescription.servers.get(incomingServerDescription.address);\n  const currentTopologyVersion = currentServerDescription?.topologyVersion;\n  return (0, server_description_1.compareTopologyVersion)(currentTopologyVersion, incomingServerDescription.topologyVersion) > 0;\n}\n/** @public */\nclass ServerCapabilities {\n  constructor(hello) {\n    this.minWireVersion = hello.minWireVersion || 0;\n    this.maxWireVersion = hello.maxWireVersion || 0;\n  }\n  get hasAggregationCursor() {\n    return this.maxWireVersion >= 1;\n  }\n  get hasWriteCommands() {\n    return this.maxWireVersion >= 2;\n  }\n  get hasTextSearch() {\n    return this.minWireVersion >= 0;\n  }\n  get hasAuthCommands() {\n    return this.maxWireVersion >= 1;\n  }\n  get hasListCollectionsCommand() {\n    return this.maxWireVersion >= 3;\n  }\n  get hasListIndexesCommand() {\n    return this.maxWireVersion >= 3;\n  }\n  get supportsSnapshotReads() {\n    return this.maxWireVersion >= 13;\n  }\n  get commandsTakeWriteConcern() {\n    return this.maxWireVersion >= 5;\n  }\n  get commandsTakeCollation() {\n    return this.maxWireVersion >= 5;\n  }\n}\nexports.ServerCapabilities = ServerCapabilities;", "map": {"version": 3, "names": ["connection_string_1", "require", "constants_1", "error_1", "mongo_logger_1", "mongo_types_1", "read_preference_1", "timeout_1", "utils_1", "common_1", "events_1", "server_1", "server_description_1", "server_selection_1", "server_selection_events_1", "srv_polling_1", "topology_description_1", "globalTopologyCounter", "stateTransition", "makeStateMachine", "STATE_CLOSED", "STATE_CONNECTING", "STATE_CLOSING", "STATE_CONNECTED", "Topology", "TypedEventEmitter", "constructor", "client", "seeds", "options", "on", "noop", "hosts", "HostAddress", "fromString", "Object", "fromEntries", "DEFAULT_OPTIONS", "entries", "Array", "isArray", "seedlist", "seed", "push", "MongoRuntimeError", "JSON", "stringify", "topologyType", "topologyTypeFromOptions", "topologyId", "selectedHosts", "srvMaxHosts", "length", "shuffle", "serverDescriptions", "Map", "host<PERSON><PERSON><PERSON>", "set", "toString", "ServerDescription", "waitQueue", "List", "s", "id", "state", "description", "TopologyDescription", "replicaSet", "undefined", "serverSelectionTimeoutMS", "heartbeatFrequencyMS", "minHeartbeatFrequencyMS", "servers", "credentials", "clusterTime", "detectShardedTopology", "ev", "detectSrvRecords", "mongoLogger", "component", "srvHost", "loadBalanced", "srv<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "srvServiceName", "TOPOLOGY_DESCRIPTION_CHANGED", "connectionLock", "event", "previousType", "previousDescription", "type", "newType", "newDescription", "transitionToSharded", "TopologyType", "Sharded", "srvListeners", "listeners", "SRV_RECORD_DISCOVERY", "listeningToSrvPolling", "includes", "start", "previousTopologyDescription", "updateFromSrvPollingEvent", "updateServers", "emitAndLog", "TopologyDescriptionChangedEvent", "serverApi", "capabilities", "ServerCapabilities", "<PERSON><PERSON><PERSON>", "connect", "_connect", "TOPOLOGY_OPENING", "TopologyOpeningEvent", "Unknown", "from", "values", "map", "serverDescription", "address", "createAndConnectServer", "serverUpdateHandler", "readPreference", "ReadPreference", "primary", "timeoutContext", "TimeoutContext", "create", "timeoutMS", "waitQueueTimeoutMS", "selectServerOptions", "operationName", "server", "selectServer", "readPreferenceServerSelector", "skipPingOnConnect", "__skipPingOnConnect", "command", "ns", "ping", "emit", "OPEN", "CONNECT", "error", "close", "destroyServer", "clear", "drainWaitQueue", "MongoTopologyClosedError", "stop", "removeListener", "TOPOLOGY_CLOSED", "TopologyClosedEvent", "selector", "serverSelector", "translate", "<PERSON><PERSON><PERSON>", "MongoLoggableComponent", "SERVER_SELECTION", "SeverityLevel", "DEBUG", "debug", "ServerSelectionStartedEvent", "timeout", "serverSelectionTimeout", "Timeout", "expires", "isSharded", "session", "transaction", "ServerSelectionSucceededEvent", "pool", "clearServerSelectionTimeout", "promise", "serverPromise", "resolve", "reject", "promiseWithResolvers", "waitQueueMember", "topologyDescription", "cancelled", "startTime", "now", "waitingLogged", "previousServer", "abortListener", "addAbortListener", "signal", "reason", "processWaitQueue", "throwIfExpired", "Promise", "race", "csotEnabled", "minRoundTripTime", "TimeoutError", "is", "timeoutError", "MongoServerSelectionError", "duration", "ServerSelectionFailedEvent", "MongoOperationTimeoutError", "cause", "kDispose", "hasServer", "isStaleServerDescription", "previousServerDescription", "get", "$clusterTime", "_advanceClusterTime", "equalDescriptions", "equals", "update", "compatibilityError", "ERROR", "MongoCompatibilityError", "SERVER_DESCRIPTION_CHANGED", "ServerDescriptionChangedEvent", "auth", "callback", "clientMetadata", "metadata", "isConnected", "isDestroyed", "sd", "filter", "ServerType", "result", "maxWireVersion", "commonWireVersion", "logicalSessionTimeoutMinutes", "exports", "SERVER_OPENING", "SERVER_CLOSED", "CLOSE", "TIMEOUT", "topology", "LOCAL_SERVER_EVENTS", "removeAllListeners", "destroy", "ServerClosedEvent", "SERVER_RELAY_EVENTS", "directConnection", "Single", "ReplicaSetNoPrimary", "LoadBalanced", "ServerOpeningEvent", "Server", "e", "DESCRIPTION_RECEIVED", "incomingServerDescription", "has", "MongoError", "has<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MongoErrorLabel", "ResetPool", "interruptInUseConnections", "InterruptInUseConnections", "newTopologyType", "shouldMarkPoolReady", "isDataBearing", "ready", "entry", "serverAddress", "delete", "queue", "drainError", "shift", "membersToProcess", "i", "selectedDescriptions", "selectorError", "selectedServer", "INFORMATIONAL", "info", "WaitingForSuitableServerEvent", "descriptions", "server1", "server2", "operationCount", "serverSelectionError", "isActive", "pinServer", "process", "nextTick", "scheduleServerCheck", "requestCheck", "currentServerDescription", "currentTopologyVersion", "topologyVersion", "compareTopologyVersion", "hello", "minWireVersion", "hasAggregationCursor", "hasWriteCommands", "hasTextSearch", "hasAuthCommands", "hasListCollectionsCommand", "hasListIndexesCommand", "supportsSnapshotReads", "commandsTakeWriteConcern", "commandsTakeCollation"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\sdam\\topology.ts"], "sourcesContent": ["import type { BSONSerializeOptions, Document } from '../bson';\nimport type { MongoCredentials } from '../cmap/auth/mongo_credentials';\nimport type { ConnectionEvents } from '../cmap/connection';\nimport type { ConnectionPoolEvents } from '../cmap/connection_pool';\nimport type { ClientMetadata } from '../cmap/handshake/client_metadata';\nimport { DEFAULT_OPTIONS } from '../connection_string';\nimport {\n  CLOSE,\n  CONNECT,\n  ERROR,\n  LOCAL_SERVER_EVENTS,\n  OPEN,\n  SERVER_CLOSED,\n  SERVER_DESCRIPTION_CHANGED,\n  SERVER_OPENING,\n  SERVER_RELAY_EVENTS,\n  TIMEOUT,\n  TOPOLOGY_CLOSED,\n  TOPOLOGY_DESCRIPTION_CHANGED,\n  TOPOLOGY_OPENING\n} from '../constants';\nimport {\n  MongoCompatibilityError,\n  type MongoDriverError,\n  MongoError,\n  MongoErrorLabel,\n  MongoOperationTimeoutError,\n  MongoRuntimeError,\n  MongoServerSelectionError,\n  MongoTopologyClosedError\n} from '../error';\nimport type { MongoClient, ServerApi } from '../mongo_client';\nimport { MongoLoggableComponent, type MongoLogger, SeverityLevel } from '../mongo_logger';\nimport { type Abortable, TypedEventEmitter } from '../mongo_types';\nimport { ReadPreference, type ReadPreferenceLike } from '../read_preference';\nimport type { ClientSession } from '../sessions';\nimport { Timeout, TimeoutContext, TimeoutError } from '../timeout';\nimport type { Transaction } from '../transactions';\nimport {\n  addAbortListener,\n  type Callback,\n  type EventEmitterWithState,\n  HostAddress,\n  kDispose,\n  List,\n  makeStateMachine,\n  noop,\n  now,\n  ns,\n  promiseWithResolvers,\n  shuffle\n} from '../utils';\nimport {\n  _advanceClusterTime,\n  type ClusterTime,\n  ServerType,\n  STATE_CLOSED,\n  STATE_CLOSING,\n  STATE_CONNECTED,\n  STATE_CONNECTING,\n  TopologyType\n} from './common';\nimport {\n  ServerClosedEvent,\n  ServerDescriptionChangedEvent,\n  ServerOpeningEvent,\n  TopologyClosedEvent,\n  TopologyDescriptionChangedEvent,\n  TopologyOpeningEvent\n} from './events';\nimport type { ServerMonitoringMode } from './monitor';\nimport { Server, type ServerEvents, type ServerOptions } from './server';\nimport { compareTopologyVersion, ServerDescription } from './server_description';\nimport { readPreferenceServerSelector, type ServerSelector } from './server_selection';\nimport {\n  ServerSelectionFailedEvent,\n  ServerSelectionStartedEvent,\n  ServerSelectionSucceededEvent,\n  WaitingForSuitableServerEvent\n} from './server_selection_events';\nimport { SrvPoller, type SrvPollingEvent } from './srv_polling';\nimport { TopologyDescription } from './topology_description';\n\n// Global state\nlet globalTopologyCounter = 0;\n\nconst stateTransition = makeStateMachine({\n  [STATE_CLOSED]: [STATE_CLOSED, STATE_CONNECTING],\n  [STATE_CONNECTING]: [STATE_CONNECTING, STATE_CLOSING, STATE_CONNECTED, STATE_CLOSED],\n  [STATE_CONNECTED]: [STATE_CONNECTED, STATE_CLOSING, STATE_CLOSED],\n  [STATE_CLOSING]: [STATE_CLOSING, STATE_CLOSED]\n});\n\n/** @internal */\nexport type ServerSelectionCallback = Callback<Server>;\n\n/** @internal */\nexport interface ServerSelectionRequest {\n  serverSelector: ServerSelector;\n  topologyDescription: TopologyDescription;\n  mongoLogger: MongoLogger | undefined;\n  transaction?: Transaction;\n  startTime: number;\n  resolve: (server: Server) => void;\n  reject: (error: MongoError) => void;\n  cancelled: boolean;\n  operationName: string;\n  waitingLogged: boolean;\n  previousServer?: ServerDescription;\n}\n\n/** @internal */\nexport interface TopologyPrivate {\n  /** the id of this topology */\n  id: number;\n  /** passed in options */\n  options: TopologyOptions;\n  /** initial seedlist of servers to connect to */\n  seedlist: HostAddress[];\n  /** initial state */\n  state: string;\n  /** the topology description */\n  description: TopologyDescription;\n  serverSelectionTimeoutMS: number;\n  heartbeatFrequencyMS: number;\n  minHeartbeatFrequencyMS: number;\n  /** A map of server instances to normalized addresses */\n  servers: Map<string, Server>;\n  credentials?: MongoCredentials;\n  clusterTime?: ClusterTime;\n\n  /** related to srv polling */\n  srvPoller?: SrvPoller;\n  detectShardedTopology: (event: TopologyDescriptionChangedEvent) => void;\n  detectSrvRecords: (event: SrvPollingEvent) => void;\n}\n\n/** @internal */\nexport interface TopologyOptions extends BSONSerializeOptions, ServerOptions {\n  srvMaxHosts: number;\n  srvServiceName: string;\n  hosts: HostAddress[];\n  retryWrites: boolean;\n  retryReads: boolean;\n  /** How long to block for server selection before throwing an error */\n  serverSelectionTimeoutMS: number;\n  /** The name of the replica set to connect to */\n  replicaSet?: string;\n  srvHost?: string;\n  srvPoller?: SrvPoller;\n  /** Indicates that a client should directly connect to a node without attempting to discover its topology type */\n  directConnection: boolean;\n  loadBalanced: boolean;\n  metadata: ClientMetadata;\n  extendedMetadata: Promise<Document>;\n  serverMonitoringMode: ServerMonitoringMode;\n  /** MongoDB server API version */\n  serverApi?: ServerApi;\n  __skipPingOnConnect?: boolean;\n}\n\n/** @public */\nexport interface ConnectOptions {\n  readPreference?: ReadPreference;\n}\n\n/** @public */\nexport interface SelectServerOptions {\n  readPreference?: ReadPreferenceLike;\n  /** How long to block for server selection before throwing an error */\n  serverSelectionTimeoutMS?: number;\n  session?: ClientSession;\n  operationName: string;\n  previousServer?: ServerDescription;\n  /**\n   * @internal\n   * TODO(NODE-6496): Make this required by making ChangeStream use LegacyTimeoutContext\n   * */\n  timeoutContext?: TimeoutContext;\n}\n\n/** @public */\nexport type TopologyEvents = {\n  /** Top level MongoClient doesn't emit this so it is marked: @internal */\n  connect(topology: Topology): void;\n  serverOpening(event: ServerOpeningEvent): void;\n  serverClosed(event: ServerClosedEvent): void;\n  serverDescriptionChanged(event: ServerDescriptionChangedEvent): void;\n  topologyClosed(event: TopologyClosedEvent): void;\n  topologyOpening(event: TopologyOpeningEvent): void;\n  topologyDescriptionChanged(event: TopologyDescriptionChangedEvent): void;\n  error(error: Error): void;\n  /** @internal */\n  open(topology: Topology): void;\n  close(): void;\n  timeout(): void;\n} & Omit<ServerEvents, 'connect'> &\n  ConnectionPoolEvents &\n  ConnectionEvents &\n  EventEmitterWithState;\n/**\n * A container of server instances representing a connection to a MongoDB topology.\n * @internal\n */\nexport class Topology extends TypedEventEmitter<TopologyEvents> {\n  /** @internal */\n  s: TopologyPrivate;\n  /** @internal */\n  waitQueue: List<ServerSelectionRequest>;\n  /** @internal */\n  hello?: Document;\n  /** @internal */\n  _type?: string;\n\n  client!: MongoClient;\n\n  /** @internal */\n  private connectionLock?: Promise<Topology>;\n\n  /** @event */\n  static readonly SERVER_OPENING = SERVER_OPENING;\n  /** @event */\n  static readonly SERVER_CLOSED = SERVER_CLOSED;\n  /** @event */\n  static readonly SERVER_DESCRIPTION_CHANGED = SERVER_DESCRIPTION_CHANGED;\n  /** @event */\n  static readonly TOPOLOGY_OPENING = TOPOLOGY_OPENING;\n  /** @event */\n  static readonly TOPOLOGY_CLOSED = TOPOLOGY_CLOSED;\n  /** @event */\n  static readonly TOPOLOGY_DESCRIPTION_CHANGED = TOPOLOGY_DESCRIPTION_CHANGED;\n  /** @event */\n  static readonly ERROR = ERROR;\n  /** @event */\n  static readonly OPEN = OPEN;\n  /** @event */\n  static readonly CONNECT = CONNECT;\n  /** @event */\n  static readonly CLOSE = CLOSE;\n  /** @event */\n  static readonly TIMEOUT = TIMEOUT;\n\n  /**\n   * @param seedlist - a list of HostAddress instances to connect to\n   */\n  constructor(\n    client: MongoClient,\n    seeds: string | string[] | HostAddress | HostAddress[],\n    options: TopologyOptions\n  ) {\n    super();\n    this.on('error', noop);\n\n    this.client = client;\n    // Options should only be undefined in tests, MongoClient will always have defined options\n    options = options ?? {\n      hosts: [HostAddress.fromString('localhost:27017')],\n      ...Object.fromEntries(DEFAULT_OPTIONS.entries())\n    };\n\n    if (typeof seeds === 'string') {\n      seeds = [HostAddress.fromString(seeds)];\n    } else if (!Array.isArray(seeds)) {\n      seeds = [seeds];\n    }\n\n    const seedlist: HostAddress[] = [];\n    for (const seed of seeds) {\n      if (typeof seed === 'string') {\n        seedlist.push(HostAddress.fromString(seed));\n      } else if (seed instanceof HostAddress) {\n        seedlist.push(seed);\n      } else {\n        // FIXME(NODE-3483): May need to be a MongoParseError\n        throw new MongoRuntimeError(`Topology cannot be constructed from ${JSON.stringify(seed)}`);\n      }\n    }\n\n    const topologyType = topologyTypeFromOptions(options);\n    const topologyId = globalTopologyCounter++;\n\n    const selectedHosts =\n      options.srvMaxHosts == null ||\n      options.srvMaxHosts === 0 ||\n      options.srvMaxHosts >= seedlist.length\n        ? seedlist\n        : shuffle(seedlist, options.srvMaxHosts);\n\n    const serverDescriptions = new Map();\n    for (const hostAddress of selectedHosts) {\n      serverDescriptions.set(hostAddress.toString(), new ServerDescription(hostAddress));\n    }\n\n    this.waitQueue = new List();\n    this.s = {\n      // the id of this topology\n      id: topologyId,\n      // passed in options\n      options,\n      // initial seedlist of servers to connect to\n      seedlist,\n      // initial state\n      state: STATE_CLOSED,\n      // the topology description\n      description: new TopologyDescription(\n        topologyType,\n        serverDescriptions,\n        options.replicaSet,\n        undefined,\n        undefined,\n        undefined,\n        options\n      ),\n      serverSelectionTimeoutMS: options.serverSelectionTimeoutMS,\n      heartbeatFrequencyMS: options.heartbeatFrequencyMS,\n      minHeartbeatFrequencyMS: options.minHeartbeatFrequencyMS,\n      // a map of server instances to normalized addresses\n      servers: new Map(),\n      credentials: options?.credentials,\n      clusterTime: undefined,\n\n      detectShardedTopology: ev => this.detectShardedTopology(ev),\n      detectSrvRecords: ev => this.detectSrvRecords(ev)\n    };\n\n    this.mongoLogger = client.mongoLogger;\n    this.component = 'topology';\n\n    if (options.srvHost && !options.loadBalanced) {\n      this.s.srvPoller =\n        options.srvPoller ??\n        new SrvPoller({\n          heartbeatFrequencyMS: this.s.heartbeatFrequencyMS,\n          srvHost: options.srvHost,\n          srvMaxHosts: options.srvMaxHosts,\n          srvServiceName: options.srvServiceName\n        });\n\n      this.on(Topology.TOPOLOGY_DESCRIPTION_CHANGED, this.s.detectShardedTopology);\n    }\n    this.connectionLock = undefined;\n  }\n\n  private detectShardedTopology(event: TopologyDescriptionChangedEvent) {\n    const previousType = event.previousDescription.type;\n    const newType = event.newDescription.type;\n\n    const transitionToSharded =\n      previousType !== TopologyType.Sharded && newType === TopologyType.Sharded;\n    const srvListeners = this.s.srvPoller?.listeners(SrvPoller.SRV_RECORD_DISCOVERY);\n    const listeningToSrvPolling = !!srvListeners?.includes(this.s.detectSrvRecords);\n\n    if (transitionToSharded && !listeningToSrvPolling) {\n      this.s.srvPoller?.on(SrvPoller.SRV_RECORD_DISCOVERY, this.s.detectSrvRecords);\n      this.s.srvPoller?.start();\n    }\n  }\n\n  private detectSrvRecords(ev: SrvPollingEvent) {\n    const previousTopologyDescription = this.s.description;\n    this.s.description = this.s.description.updateFromSrvPollingEvent(\n      ev,\n      this.s.options.srvMaxHosts\n    );\n    if (this.s.description === previousTopologyDescription) {\n      // Nothing changed, so return\n      return;\n    }\n\n    updateServers(this);\n\n    this.emitAndLog(\n      Topology.TOPOLOGY_DESCRIPTION_CHANGED,\n      new TopologyDescriptionChangedEvent(\n        this.s.id,\n        previousTopologyDescription,\n        this.s.description\n      )\n    );\n  }\n\n  /**\n   * @returns A `TopologyDescription` for this topology\n   */\n  get description(): TopologyDescription {\n    return this.s.description;\n  }\n\n  get loadBalanced(): boolean {\n    return this.s.options.loadBalanced;\n  }\n\n  get serverApi(): ServerApi | undefined {\n    return this.s.options.serverApi;\n  }\n\n  get capabilities(): ServerCapabilities {\n    return new ServerCapabilities(this.lastHello());\n  }\n\n  /** Initiate server connect */\n  async connect(options?: ConnectOptions): Promise<Topology> {\n    this.connectionLock ??= this._connect(options);\n    try {\n      await this.connectionLock;\n      return this;\n    } finally {\n      this.connectionLock = undefined;\n    }\n  }\n\n  private async _connect(options?: ConnectOptions): Promise<Topology> {\n    options = options ?? {};\n    if (this.s.state === STATE_CONNECTED) {\n      return this;\n    }\n\n    stateTransition(this, STATE_CONNECTING);\n\n    // emit SDAM monitoring events\n    this.emitAndLog(Topology.TOPOLOGY_OPENING, new TopologyOpeningEvent(this.s.id));\n\n    // emit an event for the topology change\n    this.emitAndLog(\n      Topology.TOPOLOGY_DESCRIPTION_CHANGED,\n      new TopologyDescriptionChangedEvent(\n        this.s.id,\n        new TopologyDescription(TopologyType.Unknown), // initial is always Unknown\n        this.s.description\n      )\n    );\n\n    // connect all known servers, then attempt server selection to connect\n    const serverDescriptions = Array.from(this.s.description.servers.values());\n    this.s.servers = new Map(\n      serverDescriptions.map(serverDescription => [\n        serverDescription.address,\n        createAndConnectServer(this, serverDescription)\n      ])\n    );\n\n    // In load balancer mode we need to fake a server description getting\n    // emitted from the monitor, since the monitor doesn't exist.\n    if (this.s.options.loadBalanced) {\n      for (const description of serverDescriptions) {\n        const newDescription = new ServerDescription(description.hostAddress, undefined, {\n          loadBalanced: this.s.options.loadBalanced\n        });\n        this.serverUpdateHandler(newDescription);\n      }\n    }\n\n    const serverSelectionTimeoutMS = this.client.s.options.serverSelectionTimeoutMS;\n    const readPreference = options.readPreference ?? ReadPreference.primary;\n    const timeoutContext = TimeoutContext.create({\n      // TODO(NODE-6448): auto-connect ignores timeoutMS; potential future feature\n      timeoutMS: undefined,\n      serverSelectionTimeoutMS,\n      waitQueueTimeoutMS: this.client.s.options.waitQueueTimeoutMS\n    });\n    const selectServerOptions = {\n      operationName: 'ping',\n      ...options,\n      timeoutContext\n    };\n\n    try {\n      const server = await this.selectServer(\n        readPreferenceServerSelector(readPreference),\n        selectServerOptions\n      );\n      const skipPingOnConnect = this.s.options.__skipPingOnConnect === true;\n      if (!skipPingOnConnect && this.s.credentials) {\n        await server.command(ns('admin.$cmd'), { ping: 1 }, { timeoutContext });\n        stateTransition(this, STATE_CONNECTED);\n        this.emit(Topology.OPEN, this);\n        this.emit(Topology.CONNECT, this);\n\n        return this;\n      }\n\n      stateTransition(this, STATE_CONNECTED);\n      this.emit(Topology.OPEN, this);\n      this.emit(Topology.CONNECT, this);\n\n      return this;\n    } catch (error) {\n      this.close();\n      throw error;\n    }\n  }\n\n  /** Close this topology */\n  close(): void {\n    if (this.s.state === STATE_CLOSED || this.s.state === STATE_CLOSING) {\n      return;\n    }\n\n    for (const server of this.s.servers.values()) {\n      destroyServer(server, this);\n    }\n\n    this.s.servers.clear();\n\n    stateTransition(this, STATE_CLOSING);\n\n    drainWaitQueue(this.waitQueue, new MongoTopologyClosedError());\n\n    if (this.s.srvPoller) {\n      this.s.srvPoller.stop();\n      this.s.srvPoller.removeListener(SrvPoller.SRV_RECORD_DISCOVERY, this.s.detectSrvRecords);\n    }\n\n    this.removeListener(Topology.TOPOLOGY_DESCRIPTION_CHANGED, this.s.detectShardedTopology);\n\n    stateTransition(this, STATE_CLOSED);\n\n    // emit an event for close\n    this.emitAndLog(Topology.TOPOLOGY_CLOSED, new TopologyClosedEvent(this.s.id));\n  }\n\n  /**\n   * Selects a server according to the selection predicate provided\n   *\n   * @param selector - An optional selector to select servers by, defaults to a random selection within a latency window\n   * @param options - Optional settings related to server selection\n   * @param callback - The callback used to indicate success or failure\n   * @returns An instance of a `Server` meeting the criteria of the predicate provided\n   */\n  async selectServer(\n    selector: string | ReadPreference | ServerSelector,\n    options: SelectServerOptions & Abortable\n  ): Promise<Server> {\n    let serverSelector;\n    if (typeof selector !== 'function') {\n      if (typeof selector === 'string') {\n        serverSelector = readPreferenceServerSelector(ReadPreference.fromString(selector));\n      } else {\n        let readPreference;\n        if (selector instanceof ReadPreference) {\n          readPreference = selector;\n        } else {\n          ReadPreference.translate(options);\n          readPreference = options.readPreference || ReadPreference.primary;\n        }\n\n        serverSelector = readPreferenceServerSelector(readPreference as ReadPreference);\n      }\n    } else {\n      serverSelector = selector;\n    }\n\n    options = { serverSelectionTimeoutMS: this.s.serverSelectionTimeoutMS, ...options };\n    if (\n      this.client.mongoLogger?.willLog(MongoLoggableComponent.SERVER_SELECTION, SeverityLevel.DEBUG)\n    ) {\n      this.client.mongoLogger?.debug(\n        MongoLoggableComponent.SERVER_SELECTION,\n        new ServerSelectionStartedEvent(selector, this.description, options.operationName)\n      );\n    }\n    let timeout;\n    if (options.timeoutContext) timeout = options.timeoutContext.serverSelectionTimeout;\n    else {\n      timeout = Timeout.expires(options.serverSelectionTimeoutMS ?? 0);\n    }\n\n    const isSharded = this.description.type === TopologyType.Sharded;\n    const session = options.session;\n    const transaction = session && session.transaction;\n\n    if (isSharded && transaction && transaction.server) {\n      if (\n        this.client.mongoLogger?.willLog(\n          MongoLoggableComponent.SERVER_SELECTION,\n          SeverityLevel.DEBUG\n        )\n      ) {\n        this.client.mongoLogger?.debug(\n          MongoLoggableComponent.SERVER_SELECTION,\n          new ServerSelectionSucceededEvent(\n            selector,\n            this.description,\n            transaction.server.pool.address,\n            options.operationName\n          )\n        );\n      }\n      if (options.timeoutContext?.clearServerSelectionTimeout) timeout?.clear();\n      return transaction.server;\n    }\n\n    const { promise: serverPromise, resolve, reject } = promiseWithResolvers<Server>();\n\n    const waitQueueMember: ServerSelectionRequest = {\n      serverSelector,\n      topologyDescription: this.description,\n      mongoLogger: this.client.mongoLogger,\n      transaction,\n      resolve,\n      reject,\n      cancelled: false,\n      startTime: now(),\n      operationName: options.operationName,\n      waitingLogged: false,\n      previousServer: options.previousServer\n    };\n\n    const abortListener = addAbortListener(options.signal, function () {\n      waitQueueMember.cancelled = true;\n      reject(this.reason);\n    });\n\n    this.waitQueue.push(waitQueueMember);\n    processWaitQueue(this);\n\n    try {\n      timeout?.throwIfExpired();\n      const server = await (timeout ? Promise.race([serverPromise, timeout]) : serverPromise);\n      if (options.timeoutContext?.csotEnabled() && server.description.minRoundTripTime !== 0) {\n        options.timeoutContext.minRoundTripTime = server.description.minRoundTripTime;\n      }\n      return server;\n    } catch (error) {\n      if (TimeoutError.is(error)) {\n        // Timeout\n        waitQueueMember.cancelled = true;\n        const timeoutError = new MongoServerSelectionError(\n          `Server selection timed out after ${timeout?.duration} ms`,\n          this.description\n        );\n        if (\n          this.client.mongoLogger?.willLog(\n            MongoLoggableComponent.SERVER_SELECTION,\n            SeverityLevel.DEBUG\n          )\n        ) {\n          this.client.mongoLogger?.debug(\n            MongoLoggableComponent.SERVER_SELECTION,\n            new ServerSelectionFailedEvent(\n              selector,\n              this.description,\n              timeoutError,\n              options.operationName\n            )\n          );\n        }\n\n        if (options.timeoutContext?.csotEnabled()) {\n          throw new MongoOperationTimeoutError('Timed out during server selection', {\n            cause: timeoutError\n          });\n        }\n        throw timeoutError;\n      }\n      // Other server selection error\n      throw error;\n    } finally {\n      abortListener?.[kDispose]();\n      if (options.timeoutContext?.clearServerSelectionTimeout) timeout?.clear();\n    }\n  }\n  /**\n   * Update the internal TopologyDescription with a ServerDescription\n   *\n   * @param serverDescription - The server to update in the internal list of server descriptions\n   */\n  serverUpdateHandler(serverDescription: ServerDescription): void {\n    if (!this.s.description.hasServer(serverDescription.address)) {\n      return;\n    }\n\n    // ignore this server update if its from an outdated topologyVersion\n    if (isStaleServerDescription(this.s.description, serverDescription)) {\n      return;\n    }\n\n    // these will be used for monitoring events later\n    const previousTopologyDescription = this.s.description;\n    const previousServerDescription = this.s.description.servers.get(serverDescription.address);\n    if (!previousServerDescription) {\n      return;\n    }\n\n    // Driver Sessions Spec: \"Whenever a driver receives a cluster time from\n    // a server it MUST compare it to the current highest seen cluster time\n    // for the deployment. If the new cluster time is higher than the\n    // highest seen cluster time it MUST become the new highest seen cluster\n    // time. Two cluster times are compared using only the BsonTimestamp\n    // value of the clusterTime embedded field.\"\n    const clusterTime = serverDescription.$clusterTime;\n    if (clusterTime) {\n      _advanceClusterTime(this, clusterTime);\n    }\n\n    // If we already know all the information contained in this updated description, then\n    // we don't need to emit SDAM events, but still need to update the description, in order\n    // to keep client-tracked attributes like last update time and round trip time up to date\n    const equalDescriptions =\n      previousServerDescription && previousServerDescription.equals(serverDescription);\n\n    // first update the TopologyDescription\n    this.s.description = this.s.description.update(serverDescription);\n    if (this.s.description.compatibilityError) {\n      this.emit(Topology.ERROR, new MongoCompatibilityError(this.s.description.compatibilityError));\n      return;\n    }\n\n    // emit monitoring events for this change\n    if (!equalDescriptions) {\n      const newDescription = this.s.description.servers.get(serverDescription.address);\n      if (newDescription) {\n        this.emit(\n          Topology.SERVER_DESCRIPTION_CHANGED,\n          new ServerDescriptionChangedEvent(\n            this.s.id,\n            serverDescription.address,\n            previousServerDescription,\n            newDescription\n          )\n        );\n      }\n    }\n\n    // update server list from updated descriptions\n    updateServers(this, serverDescription);\n\n    // attempt to resolve any outstanding server selection attempts\n    if (this.waitQueue.length > 0) {\n      processWaitQueue(this);\n    }\n\n    if (!equalDescriptions) {\n      this.emitAndLog(\n        Topology.TOPOLOGY_DESCRIPTION_CHANGED,\n        new TopologyDescriptionChangedEvent(\n          this.s.id,\n          previousTopologyDescription,\n          this.s.description\n        )\n      );\n    }\n  }\n\n  auth(credentials?: MongoCredentials, callback?: Callback): void {\n    if (typeof credentials === 'function') (callback = credentials), (credentials = undefined);\n    if (typeof callback === 'function') callback(undefined, true);\n  }\n\n  get clientMetadata(): ClientMetadata {\n    return this.s.options.metadata;\n  }\n\n  isConnected(): boolean {\n    return this.s.state === STATE_CONNECTED;\n  }\n\n  isDestroyed(): boolean {\n    return this.s.state === STATE_CLOSED;\n  }\n\n  // NOTE: There are many places in code where we explicitly check the last hello\n  //       to do feature support detection. This should be done any other way, but for\n  //       now we will just return the first hello seen, which should suffice.\n  lastHello(): Document {\n    const serverDescriptions = Array.from(this.description.servers.values());\n    if (serverDescriptions.length === 0) return {};\n    const sd = serverDescriptions.filter(\n      (sd: ServerDescription) => sd.type !== ServerType.Unknown\n    )[0];\n\n    const result = sd || { maxWireVersion: this.description.commonWireVersion };\n    return result;\n  }\n\n  get commonWireVersion(): number | undefined {\n    return this.description.commonWireVersion;\n  }\n\n  get logicalSessionTimeoutMinutes(): number | null {\n    return this.description.logicalSessionTimeoutMinutes;\n  }\n\n  get clusterTime(): ClusterTime | undefined {\n    return this.s.clusterTime;\n  }\n\n  set clusterTime(clusterTime: ClusterTime | undefined) {\n    this.s.clusterTime = clusterTime;\n  }\n}\n\n/** Destroys a server, and removes all event listeners from the instance */\nfunction destroyServer(server: Server, topology: Topology) {\n  for (const event of LOCAL_SERVER_EVENTS) {\n    server.removeAllListeners(event);\n  }\n\n  server.destroy();\n  topology.emitAndLog(\n    Topology.SERVER_CLOSED,\n    new ServerClosedEvent(topology.s.id, server.description.address)\n  );\n\n  for (const event of SERVER_RELAY_EVENTS) {\n    server.removeAllListeners(event);\n  }\n}\n\n/** Predicts the TopologyType from options */\nfunction topologyTypeFromOptions(options?: TopologyOptions) {\n  if (options?.directConnection) {\n    return TopologyType.Single;\n  }\n\n  if (options?.replicaSet) {\n    return TopologyType.ReplicaSetNoPrimary;\n  }\n\n  if (options?.loadBalanced) {\n    return TopologyType.LoadBalanced;\n  }\n\n  return TopologyType.Unknown;\n}\n\n/**\n * Creates new server instances and attempts to connect them\n *\n * @param topology - The topology that this server belongs to\n * @param serverDescription - The description for the server to initialize and connect to\n */\nfunction createAndConnectServer(topology: Topology, serverDescription: ServerDescription) {\n  topology.emitAndLog(\n    Topology.SERVER_OPENING,\n    new ServerOpeningEvent(topology.s.id, serverDescription.address)\n  );\n\n  const server = new Server(topology, serverDescription, topology.s.options);\n  for (const event of SERVER_RELAY_EVENTS) {\n    server.on(event, (e: any) => topology.emit(event, e));\n  }\n\n  server.on(Server.DESCRIPTION_RECEIVED, description => topology.serverUpdateHandler(description));\n\n  server.connect();\n  return server;\n}\n\n/**\n * @param topology - Topology to update.\n * @param incomingServerDescription - New server description.\n */\nfunction updateServers(topology: Topology, incomingServerDescription?: ServerDescription) {\n  // update the internal server's description\n  if (incomingServerDescription && topology.s.servers.has(incomingServerDescription.address)) {\n    const server = topology.s.servers.get(incomingServerDescription.address);\n    if (server) {\n      server.s.description = incomingServerDescription;\n      if (\n        incomingServerDescription.error instanceof MongoError &&\n        incomingServerDescription.error.hasErrorLabel(MongoErrorLabel.ResetPool)\n      ) {\n        const interruptInUseConnections = incomingServerDescription.error.hasErrorLabel(\n          MongoErrorLabel.InterruptInUseConnections\n        );\n\n        server.pool.clear({ interruptInUseConnections });\n      } else if (incomingServerDescription.error == null) {\n        const newTopologyType = topology.s.description.type;\n        const shouldMarkPoolReady =\n          incomingServerDescription.isDataBearing ||\n          (incomingServerDescription.type !== ServerType.Unknown &&\n            newTopologyType === TopologyType.Single);\n        if (shouldMarkPoolReady) {\n          server.pool.ready();\n        }\n      }\n    }\n  }\n\n  // add new servers for all descriptions we currently don't know about locally\n  for (const serverDescription of topology.description.servers.values()) {\n    if (!topology.s.servers.has(serverDescription.address)) {\n      const server = createAndConnectServer(topology, serverDescription);\n      topology.s.servers.set(serverDescription.address, server);\n    }\n  }\n\n  // for all servers no longer known, remove their descriptions and destroy their instances\n  for (const entry of topology.s.servers) {\n    const serverAddress = entry[0];\n    if (topology.description.hasServer(serverAddress)) {\n      continue;\n    }\n\n    if (!topology.s.servers.has(serverAddress)) {\n      continue;\n    }\n\n    const server = topology.s.servers.get(serverAddress);\n    topology.s.servers.delete(serverAddress);\n\n    // prepare server for garbage collection\n    if (server) {\n      destroyServer(server, topology);\n    }\n  }\n}\n\nfunction drainWaitQueue(queue: List<ServerSelectionRequest>, drainError: MongoDriverError) {\n  while (queue.length) {\n    const waitQueueMember = queue.shift();\n    if (!waitQueueMember) {\n      continue;\n    }\n\n    if (!waitQueueMember.cancelled) {\n      if (\n        waitQueueMember.mongoLogger?.willLog(\n          MongoLoggableComponent.SERVER_SELECTION,\n          SeverityLevel.DEBUG\n        )\n      ) {\n        waitQueueMember.mongoLogger?.debug(\n          MongoLoggableComponent.SERVER_SELECTION,\n          new ServerSelectionFailedEvent(\n            waitQueueMember.serverSelector,\n            waitQueueMember.topologyDescription,\n            drainError,\n            waitQueueMember.operationName\n          )\n        );\n      }\n      waitQueueMember.reject(drainError);\n    }\n  }\n}\n\nfunction processWaitQueue(topology: Topology) {\n  if (topology.s.state === STATE_CLOSED) {\n    drainWaitQueue(topology.waitQueue, new MongoTopologyClosedError());\n    return;\n  }\n\n  const isSharded = topology.description.type === TopologyType.Sharded;\n  const serverDescriptions = Array.from(topology.description.servers.values());\n  const membersToProcess = topology.waitQueue.length;\n  for (let i = 0; i < membersToProcess; ++i) {\n    const waitQueueMember = topology.waitQueue.shift();\n    if (!waitQueueMember) {\n      continue;\n    }\n\n    if (waitQueueMember.cancelled) {\n      continue;\n    }\n\n    let selectedDescriptions;\n    try {\n      const serverSelector = waitQueueMember.serverSelector;\n      const previousServer = waitQueueMember.previousServer;\n      selectedDescriptions = serverSelector\n        ? serverSelector(\n            topology.description,\n            serverDescriptions,\n            previousServer ? [previousServer] : []\n          )\n        : serverDescriptions;\n    } catch (selectorError) {\n      if (\n        topology.client.mongoLogger?.willLog(\n          MongoLoggableComponent.SERVER_SELECTION,\n          SeverityLevel.DEBUG\n        )\n      ) {\n        topology.client.mongoLogger?.debug(\n          MongoLoggableComponent.SERVER_SELECTION,\n          new ServerSelectionFailedEvent(\n            waitQueueMember.serverSelector,\n            topology.description,\n            selectorError,\n            waitQueueMember.operationName\n          )\n        );\n      }\n      waitQueueMember.reject(selectorError);\n      continue;\n    }\n\n    let selectedServer: Server | undefined;\n    if (selectedDescriptions.length === 0) {\n      if (!waitQueueMember.waitingLogged) {\n        if (\n          topology.client.mongoLogger?.willLog(\n            MongoLoggableComponent.SERVER_SELECTION,\n            SeverityLevel.INFORMATIONAL\n          )\n        ) {\n          topology.client.mongoLogger?.info(\n            MongoLoggableComponent.SERVER_SELECTION,\n            new WaitingForSuitableServerEvent(\n              waitQueueMember.serverSelector,\n              topology.description,\n              topology.s.serverSelectionTimeoutMS !== 0\n                ? topology.s.serverSelectionTimeoutMS - (now() - waitQueueMember.startTime)\n                : -1,\n              waitQueueMember.operationName\n            )\n          );\n        }\n        waitQueueMember.waitingLogged = true;\n      }\n      topology.waitQueue.push(waitQueueMember);\n      continue;\n    } else if (selectedDescriptions.length === 1) {\n      selectedServer = topology.s.servers.get(selectedDescriptions[0].address);\n    } else {\n      const descriptions = shuffle(selectedDescriptions, 2);\n      const server1 = topology.s.servers.get(descriptions[0].address);\n      const server2 = topology.s.servers.get(descriptions[1].address);\n\n      selectedServer =\n        server1 && server2 && server1.s.operationCount < server2.s.operationCount\n          ? server1\n          : server2;\n    }\n\n    if (!selectedServer) {\n      const serverSelectionError = new MongoServerSelectionError(\n        'server selection returned a server description but the server was not found in the topology',\n        topology.description\n      );\n      if (\n        topology.client.mongoLogger?.willLog(\n          MongoLoggableComponent.SERVER_SELECTION,\n          SeverityLevel.DEBUG\n        )\n      ) {\n        topology.client.mongoLogger?.debug(\n          MongoLoggableComponent.SERVER_SELECTION,\n          new ServerSelectionFailedEvent(\n            waitQueueMember.serverSelector,\n            topology.description,\n            serverSelectionError,\n            waitQueueMember.operationName\n          )\n        );\n      }\n      waitQueueMember.reject(serverSelectionError);\n      return;\n    }\n    const transaction = waitQueueMember.transaction;\n    if (isSharded && transaction && transaction.isActive && selectedServer) {\n      transaction.pinServer(selectedServer);\n    }\n\n    if (\n      topology.client.mongoLogger?.willLog(\n        MongoLoggableComponent.SERVER_SELECTION,\n        SeverityLevel.DEBUG\n      )\n    ) {\n      topology.client.mongoLogger?.debug(\n        MongoLoggableComponent.SERVER_SELECTION,\n        new ServerSelectionSucceededEvent(\n          waitQueueMember.serverSelector,\n          waitQueueMember.topologyDescription,\n          selectedServer.pool.address,\n          waitQueueMember.operationName\n        )\n      );\n    }\n    waitQueueMember.resolve(selectedServer);\n  }\n\n  if (topology.waitQueue.length > 0) {\n    // ensure all server monitors attempt monitoring soon\n    for (const [, server] of topology.s.servers) {\n      process.nextTick(function scheduleServerCheck() {\n        return server.requestCheck();\n      });\n    }\n  }\n}\n\nfunction isStaleServerDescription(\n  topologyDescription: TopologyDescription,\n  incomingServerDescription: ServerDescription\n) {\n  const currentServerDescription = topologyDescription.servers.get(\n    incomingServerDescription.address\n  );\n  const currentTopologyVersion = currentServerDescription?.topologyVersion;\n  return (\n    compareTopologyVersion(currentTopologyVersion, incomingServerDescription.topologyVersion) > 0\n  );\n}\n\n/** @public */\nexport class ServerCapabilities {\n  maxWireVersion: number;\n  minWireVersion: number;\n\n  constructor(hello: Document) {\n    this.minWireVersion = hello.minWireVersion || 0;\n    this.maxWireVersion = hello.maxWireVersion || 0;\n  }\n\n  get hasAggregationCursor(): boolean {\n    return this.maxWireVersion >= 1;\n  }\n\n  get hasWriteCommands(): boolean {\n    return this.maxWireVersion >= 2;\n  }\n  get hasTextSearch(): boolean {\n    return this.minWireVersion >= 0;\n  }\n\n  get hasAuthCommands(): boolean {\n    return this.maxWireVersion >= 1;\n  }\n\n  get hasListCollectionsCommand(): boolean {\n    return this.maxWireVersion >= 3;\n  }\n\n  get hasListIndexesCommand(): boolean {\n    return this.maxWireVersion >= 3;\n  }\n\n  get supportsSnapshotReads(): boolean {\n    return this.maxWireVersion >= 13;\n  }\n\n  get commandsTakeWriteConcern(): boolean {\n    return this.maxWireVersion >= 5;\n  }\n\n  get commandsTakeCollation(): boolean {\n    return this.maxWireVersion >= 5;\n  }\n}\n"], "mappings": ";;;;;;AAKA,MAAAA,mBAAA,GAAAC,OAAA;AACA,MAAAC,WAAA,GAAAD,OAAA;AAeA,MAAAE,OAAA,GAAAF,OAAA;AAWA,MAAAG,cAAA,GAAAH,OAAA;AACA,MAAAI,aAAA,GAAAJ,OAAA;AACA,MAAAK,iBAAA,GAAAL,OAAA;AAEA,MAAAM,SAAA,GAAAN,OAAA;AAEA,MAAAO,OAAA,GAAAP,OAAA;AAcA,MAAAQ,QAAA,GAAAR,OAAA;AAUA,MAAAS,QAAA,GAAAT,OAAA;AASA,MAAAU,QAAA,GAAAV,OAAA;AACA,MAAAW,oBAAA,GAAAX,OAAA;AACA,MAAAY,kBAAA,GAAAZ,OAAA;AACA,MAAAa,yBAAA,GAAAb,OAAA;AAMA,MAAAc,aAAA,GAAAd,OAAA;AACA,MAAAe,sBAAA,GAAAf,OAAA;AAEA;AACA,IAAIgB,qBAAqB,GAAG,CAAC;AAE7B,MAAMC,eAAe,GAAG,IAAAV,OAAA,CAAAW,gBAAgB,EAAC;EACvC,CAACV,QAAA,CAAAW,YAAY,GAAG,CAACX,QAAA,CAAAW,YAAY,EAAEX,QAAA,CAAAY,gBAAgB,CAAC;EAChD,CAACZ,QAAA,CAAAY,gBAAgB,GAAG,CAACZ,QAAA,CAAAY,gBAAgB,EAAEZ,QAAA,CAAAa,aAAa,EAAEb,QAAA,CAAAc,eAAe,EAAEd,QAAA,CAAAW,YAAY,CAAC;EACpF,CAACX,QAAA,CAAAc,eAAe,GAAG,CAACd,QAAA,CAAAc,eAAe,EAAEd,QAAA,CAAAa,aAAa,EAAEb,QAAA,CAAAW,YAAY,CAAC;EACjE,CAACX,QAAA,CAAAa,aAAa,GAAG,CAACb,QAAA,CAAAa,aAAa,EAAEb,QAAA,CAAAW,YAAY;CAC9C,CAAC;AA6GF;;;;AAIA,MAAaI,QAAS,SAAQnB,aAAA,CAAAoB,iBAAiC;EAsC7D;;;EAGAC,YACEC,MAAmB,EACnBC,KAAsD,EACtDC,OAAwB;IAExB,KAAK,EAAE;IACP,IAAI,CAACC,EAAE,CAAC,OAAO,EAAEtB,OAAA,CAAAuB,IAAI,CAAC;IAEtB,IAAI,CAACJ,MAAM,GAAGA,MAAM;IACpB;IACAE,OAAO,GAAGA,OAAO,IAAI;MACnBG,KAAK,EAAE,CAACxB,OAAA,CAAAyB,WAAW,CAACC,UAAU,CAAC,iBAAiB,CAAC,CAAC;MAClD,GAAGC,MAAM,CAACC,WAAW,CAACpC,mBAAA,CAAAqC,eAAe,CAACC,OAAO,EAAE;KAChD;IAED,IAAI,OAAOV,KAAK,KAAK,QAAQ,EAAE;MAC7BA,KAAK,GAAG,CAACpB,OAAA,CAAAyB,WAAW,CAACC,UAAU,CAACN,KAAK,CAAC,CAAC;IACzC,CAAC,MAAM,IAAI,CAACW,KAAK,CAACC,OAAO,CAACZ,KAAK,CAAC,EAAE;MAChCA,KAAK,GAAG,CAACA,KAAK,CAAC;IACjB;IAEA,MAAMa,QAAQ,GAAkB,EAAE;IAClC,KAAK,MAAMC,IAAI,IAAId,KAAK,EAAE;MACxB,IAAI,OAAOc,IAAI,KAAK,QAAQ,EAAE;QAC5BD,QAAQ,CAACE,IAAI,CAACnC,OAAA,CAAAyB,WAAW,CAACC,UAAU,CAACQ,IAAI,CAAC,CAAC;MAC7C,CAAC,MAAM,IAAIA,IAAI,YAAYlC,OAAA,CAAAyB,WAAW,EAAE;QACtCQ,QAAQ,CAACE,IAAI,CAACD,IAAI,CAAC;MACrB,CAAC,MAAM;QACL;QACA,MAAM,IAAIvC,OAAA,CAAAyC,iBAAiB,CAAC,uCAAuCC,IAAI,CAACC,SAAS,CAACJ,IAAI,CAAC,EAAE,CAAC;MAC5F;IACF;IAEA,MAAMK,YAAY,GAAGC,uBAAuB,CAACnB,OAAO,CAAC;IACrD,MAAMoB,UAAU,GAAGhC,qBAAqB,EAAE;IAE1C,MAAMiC,aAAa,GACjBrB,OAAO,CAACsB,WAAW,IAAI,IAAI,IAC3BtB,OAAO,CAACsB,WAAW,KAAK,CAAC,IACzBtB,OAAO,CAACsB,WAAW,IAAIV,QAAQ,CAACW,MAAM,GAClCX,QAAQ,GACR,IAAAjC,OAAA,CAAA6C,OAAO,EAACZ,QAAQ,EAAEZ,OAAO,CAACsB,WAAW,CAAC;IAE5C,MAAMG,kBAAkB,GAAG,IAAIC,GAAG,EAAE;IACpC,KAAK,MAAMC,WAAW,IAAIN,aAAa,EAAE;MACvCI,kBAAkB,CAACG,GAAG,CAACD,WAAW,CAACE,QAAQ,EAAE,EAAE,IAAI9C,oBAAA,CAAA+C,iBAAiB,CAACH,WAAW,CAAC,CAAC;IACpF;IAEA,IAAI,CAACI,SAAS,GAAG,IAAIpD,OAAA,CAAAqD,IAAI,EAAE;IAC3B,IAAI,CAACC,CAAC,GAAG;MACP;MACAC,EAAE,EAAEd,UAAU;MACd;MACApB,OAAO;MACP;MACAY,QAAQ;MACR;MACAuB,KAAK,EAAEvD,QAAA,CAAAW,YAAY;MACnB;MACA6C,WAAW,EAAE,IAAIjD,sBAAA,CAAAkD,mBAAmB,CAClCnB,YAAY,EACZO,kBAAkB,EAClBzB,OAAO,CAACsC,UAAU,EAClBC,SAAS,EACTA,SAAS,EACTA,SAAS,EACTvC,OAAO,CACR;MACDwC,wBAAwB,EAAExC,OAAO,CAACwC,wBAAwB;MAC1DC,oBAAoB,EAAEzC,OAAO,CAACyC,oBAAoB;MAClDC,uBAAuB,EAAE1C,OAAO,CAAC0C,uBAAuB;MACxD;MACAC,OAAO,EAAE,IAAIjB,GAAG,EAAE;MAClBkB,WAAW,EAAE5C,OAAO,EAAE4C,WAAW;MACjCC,WAAW,EAAEN,SAAS;MAEtBO,qBAAqB,EAAEC,EAAE,IAAI,IAAI,CAACD,qBAAqB,CAACC,EAAE,CAAC;MAC3DC,gBAAgB,EAAED,EAAE,IAAI,IAAI,CAACC,gBAAgB,CAACD,EAAE;KACjD;IAED,IAAI,CAACE,WAAW,GAAGnD,MAAM,CAACmD,WAAW;IACrC,IAAI,CAACC,SAAS,GAAG,UAAU;IAE3B,IAAIlD,OAAO,CAACmD,OAAO,IAAI,CAACnD,OAAO,CAACoD,YAAY,EAAE;MAC5C,IAAI,CAACnB,CAAC,CAACoB,SAAS,GACdrD,OAAO,CAACqD,SAAS,IACjB,IAAInE,aAAA,CAAAoE,SAAS,CAAC;QACZb,oBAAoB,EAAE,IAAI,CAACR,CAAC,CAACQ,oBAAoB;QACjDU,OAAO,EAAEnD,OAAO,CAACmD,OAAO;QACxB7B,WAAW,EAAEtB,OAAO,CAACsB,WAAW;QAChCiC,cAAc,EAAEvD,OAAO,CAACuD;OACzB,CAAC;MAEJ,IAAI,CAACtD,EAAE,CAACN,QAAQ,CAAC6D,4BAA4B,EAAE,IAAI,CAACvB,CAAC,CAACa,qBAAqB,CAAC;IAC9E;IACA,IAAI,CAACW,cAAc,GAAGlB,SAAS;EACjC;EAEQO,qBAAqBA,CAACY,KAAsC;IAClE,MAAMC,YAAY,GAAGD,KAAK,CAACE,mBAAmB,CAACC,IAAI;IACnD,MAAMC,OAAO,GAAGJ,KAAK,CAACK,cAAc,CAACF,IAAI;IAEzC,MAAMG,mBAAmB,GACvBL,YAAY,KAAK/E,QAAA,CAAAqF,YAAY,CAACC,OAAO,IAAIJ,OAAO,KAAKlF,QAAA,CAAAqF,YAAY,CAACC,OAAO;IAC3E,MAAMC,YAAY,GAAG,IAAI,CAAClC,CAAC,CAACoB,SAAS,EAAEe,SAAS,CAAClF,aAAA,CAAAoE,SAAS,CAACe,oBAAoB,CAAC;IAChF,MAAMC,qBAAqB,GAAG,CAAC,CAACH,YAAY,EAAEI,QAAQ,CAAC,IAAI,CAACtC,CAAC,CAACe,gBAAgB,CAAC;IAE/E,IAAIgB,mBAAmB,IAAI,CAACM,qBAAqB,EAAE;MACjD,IAAI,CAACrC,CAAC,CAACoB,SAAS,EAAEpD,EAAE,CAACf,aAAA,CAAAoE,SAAS,CAACe,oBAAoB,EAAE,IAAI,CAACpC,CAAC,CAACe,gBAAgB,CAAC;MAC7E,IAAI,CAACf,CAAC,CAACoB,SAAS,EAAEmB,KAAK,EAAE;IAC3B;EACF;EAEQxB,gBAAgBA,CAACD,EAAmB;IAC1C,MAAM0B,2BAA2B,GAAG,IAAI,CAACxC,CAAC,CAACG,WAAW;IACtD,IAAI,CAACH,CAAC,CAACG,WAAW,GAAG,IAAI,CAACH,CAAC,CAACG,WAAW,CAACsC,yBAAyB,CAC/D3B,EAAE,EACF,IAAI,CAACd,CAAC,CAACjC,OAAO,CAACsB,WAAW,CAC3B;IACD,IAAI,IAAI,CAACW,CAAC,CAACG,WAAW,KAAKqC,2BAA2B,EAAE;MACtD;MACA;IACF;IAEAE,aAAa,CAAC,IAAI,CAAC;IAEnB,IAAI,CAACC,UAAU,CACbjF,QAAQ,CAAC6D,4BAA4B,EACrC,IAAI3E,QAAA,CAAAgG,+BAA+B,CACjC,IAAI,CAAC5C,CAAC,CAACC,EAAE,EACTuC,2BAA2B,EAC3B,IAAI,CAACxC,CAAC,CAACG,WAAW,CACnB,CACF;EACH;EAEA;;;EAGA,IAAIA,WAAWA,CAAA;IACb,OAAO,IAAI,CAACH,CAAC,CAACG,WAAW;EAC3B;EAEA,IAAIgB,YAAYA,CAAA;IACd,OAAO,IAAI,CAACnB,CAAC,CAACjC,OAAO,CAACoD,YAAY;EACpC;EAEA,IAAI0B,SAASA,CAAA;IACX,OAAO,IAAI,CAAC7C,CAAC,CAACjC,OAAO,CAAC8E,SAAS;EACjC;EAEA,IAAIC,YAAYA,CAAA;IACd,OAAO,IAAIC,kBAAkB,CAAC,IAAI,CAACC,SAAS,EAAE,CAAC;EACjD;EAEA;EACA,MAAMC,OAAOA,CAAClF,OAAwB;IACpC,IAAI,CAACyD,cAAc,KAAK,IAAI,CAAC0B,QAAQ,CAACnF,OAAO,CAAC;IAC9C,IAAI;MACF,MAAM,IAAI,CAACyD,cAAc;MACzB,OAAO,IAAI;IACb,CAAC,SAAS;MACR,IAAI,CAACA,cAAc,GAAGlB,SAAS;IACjC;EACF;EAEQ,MAAM4C,QAAQA,CAACnF,OAAwB;IAC7CA,OAAO,GAAGA,OAAO,IAAI,EAAE;IACvB,IAAI,IAAI,CAACiC,CAAC,CAACE,KAAK,KAAKvD,QAAA,CAAAc,eAAe,EAAE;MACpC,OAAO,IAAI;IACb;IAEAL,eAAe,CAAC,IAAI,EAAET,QAAA,CAAAY,gBAAgB,CAAC;IAEvC;IACA,IAAI,CAACoF,UAAU,CAACjF,QAAQ,CAACyF,gBAAgB,EAAE,IAAIvG,QAAA,CAAAwG,oBAAoB,CAAC,IAAI,CAACpD,CAAC,CAACC,EAAE,CAAC,CAAC;IAE/E;IACA,IAAI,CAAC0C,UAAU,CACbjF,QAAQ,CAAC6D,4BAA4B,EACrC,IAAI3E,QAAA,CAAAgG,+BAA+B,CACjC,IAAI,CAAC5C,CAAC,CAACC,EAAE,EACT,IAAI/C,sBAAA,CAAAkD,mBAAmB,CAACzD,QAAA,CAAAqF,YAAY,CAACqB,OAAO,CAAC;IAAE;IAC/C,IAAI,CAACrD,CAAC,CAACG,WAAW,CACnB,CACF;IAED;IACA,MAAMX,kBAAkB,GAAGf,KAAK,CAAC6E,IAAI,CAAC,IAAI,CAACtD,CAAC,CAACG,WAAW,CAACO,OAAO,CAAC6C,MAAM,EAAE,CAAC;IAC1E,IAAI,CAACvD,CAAC,CAACU,OAAO,GAAG,IAAIjB,GAAG,CACtBD,kBAAkB,CAACgE,GAAG,CAACC,iBAAiB,IAAI,CAC1CA,iBAAiB,CAACC,OAAO,EACzBC,sBAAsB,CAAC,IAAI,EAAEF,iBAAiB,CAAC,CAChD,CAAC,CACH;IAED;IACA;IACA,IAAI,IAAI,CAACzD,CAAC,CAACjC,OAAO,CAACoD,YAAY,EAAE;MAC/B,KAAK,MAAMhB,WAAW,IAAIX,kBAAkB,EAAE;QAC5C,MAAMsC,cAAc,GAAG,IAAIhF,oBAAA,CAAA+C,iBAAiB,CAACM,WAAW,CAACT,WAAW,EAAEY,SAAS,EAAE;UAC/Ea,YAAY,EAAE,IAAI,CAACnB,CAAC,CAACjC,OAAO,CAACoD;SAC9B,CAAC;QACF,IAAI,CAACyC,mBAAmB,CAAC9B,cAAc,CAAC;MAC1C;IACF;IAEA,MAAMvB,wBAAwB,GAAG,IAAI,CAAC1C,MAAM,CAACmC,CAAC,CAACjC,OAAO,CAACwC,wBAAwB;IAC/E,MAAMsD,cAAc,GAAG9F,OAAO,CAAC8F,cAAc,IAAIrH,iBAAA,CAAAsH,cAAc,CAACC,OAAO;IACvE,MAAMC,cAAc,GAAGvH,SAAA,CAAAwH,cAAc,CAACC,MAAM,CAAC;MAC3C;MACAC,SAAS,EAAE7D,SAAS;MACpBC,wBAAwB;MACxB6D,kBAAkB,EAAE,IAAI,CAACvG,MAAM,CAACmC,CAAC,CAACjC,OAAO,CAACqG;KAC3C,CAAC;IACF,MAAMC,mBAAmB,GAAG;MAC1BC,aAAa,EAAE,MAAM;MACrB,GAAGvG,OAAO;MACViG;KACD;IAED,IAAI;MACF,MAAMO,MAAM,GAAG,MAAM,IAAI,CAACC,YAAY,CACpC,IAAAzH,kBAAA,CAAA0H,4BAA4B,EAACZ,cAAc,CAAC,EAC5CQ,mBAAmB,CACpB;MACD,MAAMK,iBAAiB,GAAG,IAAI,CAAC1E,CAAC,CAACjC,OAAO,CAAC4G,mBAAmB,KAAK,IAAI;MACrE,IAAI,CAACD,iBAAiB,IAAI,IAAI,CAAC1E,CAAC,CAACW,WAAW,EAAE;QAC5C,MAAM4D,MAAM,CAACK,OAAO,CAAC,IAAAlI,OAAA,CAAAmI,EAAE,EAAC,YAAY,CAAC,EAAE;UAAEC,IAAI,EAAE;QAAC,CAAE,EAAE;UAAEd;QAAc,CAAE,CAAC;QACvE5G,eAAe,CAAC,IAAI,EAAET,QAAA,CAAAc,eAAe,CAAC;QACtC,IAAI,CAACsH,IAAI,CAACrH,QAAQ,CAACsH,IAAI,EAAE,IAAI,CAAC;QAC9B,IAAI,CAACD,IAAI,CAACrH,QAAQ,CAACuH,OAAO,EAAE,IAAI,CAAC;QAEjC,OAAO,IAAI;MACb;MAEA7H,eAAe,CAAC,IAAI,EAAET,QAAA,CAAAc,eAAe,CAAC;MACtC,IAAI,CAACsH,IAAI,CAACrH,QAAQ,CAACsH,IAAI,EAAE,IAAI,CAAC;MAC9B,IAAI,CAACD,IAAI,CAACrH,QAAQ,CAACuH,OAAO,EAAE,IAAI,CAAC;MAEjC,OAAO,IAAI;IACb,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd,IAAI,CAACC,KAAK,EAAE;MACZ,MAAMD,KAAK;IACb;EACF;EAEA;EACAC,KAAKA,CAAA;IACH,IAAI,IAAI,CAACnF,CAAC,CAACE,KAAK,KAAKvD,QAAA,CAAAW,YAAY,IAAI,IAAI,CAAC0C,CAAC,CAACE,KAAK,KAAKvD,QAAA,CAAAa,aAAa,EAAE;MACnE;IACF;IAEA,KAAK,MAAM+G,MAAM,IAAI,IAAI,CAACvE,CAAC,CAACU,OAAO,CAAC6C,MAAM,EAAE,EAAE;MAC5C6B,aAAa,CAACb,MAAM,EAAE,IAAI,CAAC;IAC7B;IAEA,IAAI,CAACvE,CAAC,CAACU,OAAO,CAAC2E,KAAK,EAAE;IAEtBjI,eAAe,CAAC,IAAI,EAAET,QAAA,CAAAa,aAAa,CAAC;IAEpC8H,cAAc,CAAC,IAAI,CAACxF,SAAS,EAAE,IAAIzD,OAAA,CAAAkJ,wBAAwB,EAAE,CAAC;IAE9D,IAAI,IAAI,CAACvF,CAAC,CAACoB,SAAS,EAAE;MACpB,IAAI,CAACpB,CAAC,CAACoB,SAAS,CAACoE,IAAI,EAAE;MACvB,IAAI,CAACxF,CAAC,CAACoB,SAAS,CAACqE,cAAc,CAACxI,aAAA,CAAAoE,SAAS,CAACe,oBAAoB,EAAE,IAAI,CAACpC,CAAC,CAACe,gBAAgB,CAAC;IAC1F;IAEA,IAAI,CAAC0E,cAAc,CAAC/H,QAAQ,CAAC6D,4BAA4B,EAAE,IAAI,CAACvB,CAAC,CAACa,qBAAqB,CAAC;IAExFzD,eAAe,CAAC,IAAI,EAAET,QAAA,CAAAW,YAAY,CAAC;IAEnC;IACA,IAAI,CAACqF,UAAU,CAACjF,QAAQ,CAACgI,eAAe,EAAE,IAAI9I,QAAA,CAAA+I,mBAAmB,CAAC,IAAI,CAAC3F,CAAC,CAACC,EAAE,CAAC,CAAC;EAC/E;EAEA;;;;;;;;EAQA,MAAMuE,YAAYA,CAChBoB,QAAkD,EAClD7H,OAAwC;IAExC,IAAI8H,cAAc;IAClB,IAAI,OAAOD,QAAQ,KAAK,UAAU,EAAE;MAClC,IAAI,OAAOA,QAAQ,KAAK,QAAQ,EAAE;QAChCC,cAAc,GAAG,IAAA9I,kBAAA,CAAA0H,4BAA4B,EAACjI,iBAAA,CAAAsH,cAAc,CAAC1F,UAAU,CAACwH,QAAQ,CAAC,CAAC;MACpF,CAAC,MAAM;QACL,IAAI/B,cAAc;QAClB,IAAI+B,QAAQ,YAAYpJ,iBAAA,CAAAsH,cAAc,EAAE;UACtCD,cAAc,GAAG+B,QAAQ;QAC3B,CAAC,MAAM;UACLpJ,iBAAA,CAAAsH,cAAc,CAACgC,SAAS,CAAC/H,OAAO,CAAC;UACjC8F,cAAc,GAAG9F,OAAO,CAAC8F,cAAc,IAAIrH,iBAAA,CAAAsH,cAAc,CAACC,OAAO;QACnE;QAEA8B,cAAc,GAAG,IAAA9I,kBAAA,CAAA0H,4BAA4B,EAACZ,cAAgC,CAAC;MACjF;IACF,CAAC,MAAM;MACLgC,cAAc,GAAGD,QAAQ;IAC3B;IAEA7H,OAAO,GAAG;MAAEwC,wBAAwB,EAAE,IAAI,CAACP,CAAC,CAACO,wBAAwB;MAAE,GAAGxC;IAAO,CAAE;IACnF,IACE,IAAI,CAACF,MAAM,CAACmD,WAAW,EAAE+E,OAAO,CAACzJ,cAAA,CAAA0J,sBAAsB,CAACC,gBAAgB,EAAE3J,cAAA,CAAA4J,aAAa,CAACC,KAAK,CAAC,EAC9F;MACA,IAAI,CAACtI,MAAM,CAACmD,WAAW,EAAEoF,KAAK,CAC5B9J,cAAA,CAAA0J,sBAAsB,CAACC,gBAAgB,EACvC,IAAIjJ,yBAAA,CAAAqJ,2BAA2B,CAACT,QAAQ,EAAE,IAAI,CAACzF,WAAW,EAAEpC,OAAO,CAACuG,aAAa,CAAC,CACnF;IACH;IACA,IAAIgC,OAAO;IACX,IAAIvI,OAAO,CAACiG,cAAc,EAAEsC,OAAO,GAAGvI,OAAO,CAACiG,cAAc,CAACuC,sBAAsB,CAAC,KAC/E;MACHD,OAAO,GAAG7J,SAAA,CAAA+J,OAAO,CAACC,OAAO,CAAC1I,OAAO,CAACwC,wBAAwB,IAAI,CAAC,CAAC;IAClE;IAEA,MAAMmG,SAAS,GAAG,IAAI,CAACvG,WAAW,CAACyB,IAAI,KAAKjF,QAAA,CAAAqF,YAAY,CAACC,OAAO;IAChE,MAAM0E,OAAO,GAAG5I,OAAO,CAAC4I,OAAO;IAC/B,MAAMC,WAAW,GAAGD,OAAO,IAAIA,OAAO,CAACC,WAAW;IAElD,IAAIF,SAAS,IAAIE,WAAW,IAAIA,WAAW,CAACrC,MAAM,EAAE;MAClD,IACE,IAAI,CAAC1G,MAAM,CAACmD,WAAW,EAAE+E,OAAO,CAC9BzJ,cAAA,CAAA0J,sBAAsB,CAACC,gBAAgB,EACvC3J,cAAA,CAAA4J,aAAa,CAACC,KAAK,CACpB,EACD;QACA,IAAI,CAACtI,MAAM,CAACmD,WAAW,EAAEoF,KAAK,CAC5B9J,cAAA,CAAA0J,sBAAsB,CAACC,gBAAgB,EACvC,IAAIjJ,yBAAA,CAAA6J,6BAA6B,CAC/BjB,QAAQ,EACR,IAAI,CAACzF,WAAW,EAChByG,WAAW,CAACrC,MAAM,CAACuC,IAAI,CAACpD,OAAO,EAC/B3F,OAAO,CAACuG,aAAa,CACtB,CACF;MACH;MACA,IAAIvG,OAAO,CAACiG,cAAc,EAAE+C,2BAA2B,EAAET,OAAO,EAAEjB,KAAK,EAAE;MACzE,OAAOuB,WAAW,CAACrC,MAAM;IAC3B;IAEA,MAAM;MAAEyC,OAAO,EAAEC,aAAa;MAAEC,OAAO;MAAEC;IAAM,CAAE,GAAG,IAAAzK,OAAA,CAAA0K,oBAAoB,GAAU;IAElF,MAAMC,eAAe,GAA2B;MAC9CxB,cAAc;MACdyB,mBAAmB,EAAE,IAAI,CAACnH,WAAW;MACrCa,WAAW,EAAE,IAAI,CAACnD,MAAM,CAACmD,WAAW;MACpC4F,WAAW;MACXM,OAAO;MACPC,MAAM;MACNI,SAAS,EAAE,KAAK;MAChBC,SAAS,EAAE,IAAA9K,OAAA,CAAA+K,GAAG,GAAE;MAChBnD,aAAa,EAAEvG,OAAO,CAACuG,aAAa;MACpCoD,aAAa,EAAE,KAAK;MACpBC,cAAc,EAAE5J,OAAO,CAAC4J;KACzB;IAED,MAAMC,aAAa,GAAG,IAAAlL,OAAA,CAAAmL,gBAAgB,EAAC9J,OAAO,CAAC+J,MAAM,EAAE;MACrDT,eAAe,CAACE,SAAS,GAAG,IAAI;MAChCJ,MAAM,CAAC,IAAI,CAACY,MAAM,CAAC;IACrB,CAAC,CAAC;IAEF,IAAI,CAACjI,SAAS,CAACjB,IAAI,CAACwI,eAAe,CAAC;IACpCW,gBAAgB,CAAC,IAAI,CAAC;IAEtB,IAAI;MACF1B,OAAO,EAAE2B,cAAc,EAAE;MACzB,MAAM1D,MAAM,GAAG,OAAO+B,OAAO,GAAG4B,OAAO,CAACC,IAAI,CAAC,CAAClB,aAAa,EAAEX,OAAO,CAAC,CAAC,GAAGW,aAAa,CAAC;MACvF,IAAIlJ,OAAO,CAACiG,cAAc,EAAEoE,WAAW,EAAE,IAAI7D,MAAM,CAACpE,WAAW,CAACkI,gBAAgB,KAAK,CAAC,EAAE;QACtFtK,OAAO,CAACiG,cAAc,CAACqE,gBAAgB,GAAG9D,MAAM,CAACpE,WAAW,CAACkI,gBAAgB;MAC/E;MACA,OAAO9D,MAAM;IACf,CAAC,CAAC,OAAOW,KAAK,EAAE;MACd,IAAIzI,SAAA,CAAA6L,YAAY,CAACC,EAAE,CAACrD,KAAK,CAAC,EAAE;QAC1B;QACAmC,eAAe,CAACE,SAAS,GAAG,IAAI;QAChC,MAAMiB,YAAY,GAAG,IAAInM,OAAA,CAAAoM,yBAAyB,CAChD,oCAAoCnC,OAAO,EAAEoC,QAAQ,KAAK,EAC1D,IAAI,CAACvI,WAAW,CACjB;QACD,IACE,IAAI,CAACtC,MAAM,CAACmD,WAAW,EAAE+E,OAAO,CAC9BzJ,cAAA,CAAA0J,sBAAsB,CAACC,gBAAgB,EACvC3J,cAAA,CAAA4J,aAAa,CAACC,KAAK,CACpB,EACD;UACA,IAAI,CAACtI,MAAM,CAACmD,WAAW,EAAEoF,KAAK,CAC5B9J,cAAA,CAAA0J,sBAAsB,CAACC,gBAAgB,EACvC,IAAIjJ,yBAAA,CAAA2L,0BAA0B,CAC5B/C,QAAQ,EACR,IAAI,CAACzF,WAAW,EAChBqI,YAAY,EACZzK,OAAO,CAACuG,aAAa,CACtB,CACF;QACH;QAEA,IAAIvG,OAAO,CAACiG,cAAc,EAAEoE,WAAW,EAAE,EAAE;UACzC,MAAM,IAAI/L,OAAA,CAAAuM,0BAA0B,CAAC,mCAAmC,EAAE;YACxEC,KAAK,EAAEL;WACR,CAAC;QACJ;QACA,MAAMA,YAAY;MACpB;MACA;MACA,MAAMtD,KAAK;IACb,CAAC,SAAS;MACR0C,aAAa,GAAGlL,OAAA,CAAAoM,QAAQ,CAAC,EAAE;MAC3B,IAAI/K,OAAO,CAACiG,cAAc,EAAE+C,2BAA2B,EAAET,OAAO,EAAEjB,KAAK,EAAE;IAC3E;EACF;EACA;;;;;EAKAzB,mBAAmBA,CAACH,iBAAoC;IACtD,IAAI,CAAC,IAAI,CAACzD,CAAC,CAACG,WAAW,CAAC4I,SAAS,CAACtF,iBAAiB,CAACC,OAAO,CAAC,EAAE;MAC5D;IACF;IAEA;IACA,IAAIsF,wBAAwB,CAAC,IAAI,CAAChJ,CAAC,CAACG,WAAW,EAAEsD,iBAAiB,CAAC,EAAE;MACnE;IACF;IAEA;IACA,MAAMjB,2BAA2B,GAAG,IAAI,CAACxC,CAAC,CAACG,WAAW;IACtD,MAAM8I,yBAAyB,GAAG,IAAI,CAACjJ,CAAC,CAACG,WAAW,CAACO,OAAO,CAACwI,GAAG,CAACzF,iBAAiB,CAACC,OAAO,CAAC;IAC3F,IAAI,CAACuF,yBAAyB,EAAE;MAC9B;IACF;IAEA;IACA;IACA;IACA;IACA;IACA;IACA,MAAMrI,WAAW,GAAG6C,iBAAiB,CAAC0F,YAAY;IAClD,IAAIvI,WAAW,EAAE;MACf,IAAAjE,QAAA,CAAAyM,mBAAmB,EAAC,IAAI,EAAExI,WAAW,CAAC;IACxC;IAEA;IACA;IACA;IACA,MAAMyI,iBAAiB,GACrBJ,yBAAyB,IAAIA,yBAAyB,CAACK,MAAM,CAAC7F,iBAAiB,CAAC;IAElF;IACA,IAAI,CAACzD,CAAC,CAACG,WAAW,GAAG,IAAI,CAACH,CAAC,CAACG,WAAW,CAACoJ,MAAM,CAAC9F,iBAAiB,CAAC;IACjE,IAAI,IAAI,CAACzD,CAAC,CAACG,WAAW,CAACqJ,kBAAkB,EAAE;MACzC,IAAI,CAACzE,IAAI,CAACrH,QAAQ,CAAC+L,KAAK,EAAE,IAAIpN,OAAA,CAAAqN,uBAAuB,CAAC,IAAI,CAAC1J,CAAC,CAACG,WAAW,CAACqJ,kBAAkB,CAAC,CAAC;MAC7F;IACF;IAEA;IACA,IAAI,CAACH,iBAAiB,EAAE;MACtB,MAAMvH,cAAc,GAAG,IAAI,CAAC9B,CAAC,CAACG,WAAW,CAACO,OAAO,CAACwI,GAAG,CAACzF,iBAAiB,CAACC,OAAO,CAAC;MAChF,IAAI5B,cAAc,EAAE;QAClB,IAAI,CAACiD,IAAI,CACPrH,QAAQ,CAACiM,0BAA0B,EACnC,IAAI/M,QAAA,CAAAgN,6BAA6B,CAC/B,IAAI,CAAC5J,CAAC,CAACC,EAAE,EACTwD,iBAAiB,CAACC,OAAO,EACzBuF,yBAAyB,EACzBnH,cAAc,CACf,CACF;MACH;IACF;IAEA;IACAY,aAAa,CAAC,IAAI,EAAEe,iBAAiB,CAAC;IAEtC;IACA,IAAI,IAAI,CAAC3D,SAAS,CAACR,MAAM,GAAG,CAAC,EAAE;MAC7B0I,gBAAgB,CAAC,IAAI,CAAC;IACxB;IAEA,IAAI,CAACqB,iBAAiB,EAAE;MACtB,IAAI,CAAC1G,UAAU,CACbjF,QAAQ,CAAC6D,4BAA4B,EACrC,IAAI3E,QAAA,CAAAgG,+BAA+B,CACjC,IAAI,CAAC5C,CAAC,CAACC,EAAE,EACTuC,2BAA2B,EAC3B,IAAI,CAACxC,CAAC,CAACG,WAAW,CACnB,CACF;IACH;EACF;EAEA0J,IAAIA,CAAClJ,WAA8B,EAAEmJ,QAAmB;IACtD,IAAI,OAAOnJ,WAAW,KAAK,UAAU,EAAGmJ,QAAQ,GAAGnJ,WAAW,EAAIA,WAAW,GAAGL,SAAU;IAC1F,IAAI,OAAOwJ,QAAQ,KAAK,UAAU,EAAEA,QAAQ,CAACxJ,SAAS,EAAE,IAAI,CAAC;EAC/D;EAEA,IAAIyJ,cAAcA,CAAA;IAChB,OAAO,IAAI,CAAC/J,CAAC,CAACjC,OAAO,CAACiM,QAAQ;EAChC;EAEAC,WAAWA,CAAA;IACT,OAAO,IAAI,CAACjK,CAAC,CAACE,KAAK,KAAKvD,QAAA,CAAAc,eAAe;EACzC;EAEAyM,WAAWA,CAAA;IACT,OAAO,IAAI,CAAClK,CAAC,CAACE,KAAK,KAAKvD,QAAA,CAAAW,YAAY;EACtC;EAEA;EACA;EACA;EACA0F,SAASA,CAAA;IACP,MAAMxD,kBAAkB,GAAGf,KAAK,CAAC6E,IAAI,CAAC,IAAI,CAACnD,WAAW,CAACO,OAAO,CAAC6C,MAAM,EAAE,CAAC;IACxE,IAAI/D,kBAAkB,CAACF,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;IAC9C,MAAM6K,EAAE,GAAG3K,kBAAkB,CAAC4K,MAAM,CACjCD,EAAqB,IAAKA,EAAE,CAACvI,IAAI,KAAKjF,QAAA,CAAA0N,UAAU,CAAChH,OAAO,CAC1D,CAAC,CAAC,CAAC;IAEJ,MAAMiH,MAAM,GAAGH,EAAE,IAAI;MAAEI,cAAc,EAAE,IAAI,CAACpK,WAAW,CAACqK;IAAiB,CAAE;IAC3E,OAAOF,MAAM;EACf;EAEA,IAAIE,iBAAiBA,CAAA;IACnB,OAAO,IAAI,CAACrK,WAAW,CAACqK,iBAAiB;EAC3C;EAEA,IAAIC,4BAA4BA,CAAA;IAC9B,OAAO,IAAI,CAACtK,WAAW,CAACsK,4BAA4B;EACtD;EAEA,IAAI7J,WAAWA,CAAA;IACb,OAAO,IAAI,CAACZ,CAAC,CAACY,WAAW;EAC3B;EAEA,IAAIA,WAAWA,CAACA,WAAoC;IAClD,IAAI,CAACZ,CAAC,CAACY,WAAW,GAAGA,WAAW;EAClC;;AAzkBF8J,OAAA,CAAAhN,QAAA,GAAAA,QAAA;AAeE;AACgBA,QAAA,CAAAiN,cAAc,GAAGvO,WAAA,CAAAuO,cAAc;AAC/C;AACgBjN,QAAA,CAAAkN,aAAa,GAAGxO,WAAA,CAAAwO,aAAa;AAC7C;AACgBlN,QAAA,CAAAiM,0BAA0B,GAAGvN,WAAA,CAAAuN,0BAA0B;AACvE;AACgBjM,QAAA,CAAAyF,gBAAgB,GAAG/G,WAAA,CAAA+G,gBAAgB;AACnD;AACgBzF,QAAA,CAAAgI,eAAe,GAAGtJ,WAAA,CAAAsJ,eAAe;AACjD;AACgBhI,QAAA,CAAA6D,4BAA4B,GAAGnF,WAAA,CAAAmF,4BAA4B;AAC3E;AACgB7D,QAAA,CAAA+L,KAAK,GAAGrN,WAAA,CAAAqN,KAAK;AAC7B;AACgB/L,QAAA,CAAAsH,IAAI,GAAG5I,WAAA,CAAA4I,IAAI;AAC3B;AACgBtH,QAAA,CAAAuH,OAAO,GAAG7I,WAAA,CAAA6I,OAAO;AACjC;AACgBvH,QAAA,CAAAmN,KAAK,GAAGzO,WAAA,CAAAyO,KAAK;AAC7B;AACgBnN,QAAA,CAAAoN,OAAO,GAAG1O,WAAA,CAAA0O,OAAO;AAwiBnC;AACA,SAAS1F,aAAaA,CAACb,MAAc,EAAEwG,QAAkB;EACvD,KAAK,MAAMtJ,KAAK,IAAIrF,WAAA,CAAA4O,mBAAmB,EAAE;IACvCzG,MAAM,CAAC0G,kBAAkB,CAACxJ,KAAK,CAAC;EAClC;EAEA8C,MAAM,CAAC2G,OAAO,EAAE;EAChBH,QAAQ,CAACpI,UAAU,CACjBjF,QAAQ,CAACkN,aAAa,EACtB,IAAIhO,QAAA,CAAAuO,iBAAiB,CAACJ,QAAQ,CAAC/K,CAAC,CAACC,EAAE,EAAEsE,MAAM,CAACpE,WAAW,CAACuD,OAAO,CAAC,CACjE;EAED,KAAK,MAAMjC,KAAK,IAAIrF,WAAA,CAAAgP,mBAAmB,EAAE;IACvC7G,MAAM,CAAC0G,kBAAkB,CAACxJ,KAAK,CAAC;EAClC;AACF;AAEA;AACA,SAASvC,uBAAuBA,CAACnB,OAAyB;EACxD,IAAIA,OAAO,EAAEsN,gBAAgB,EAAE;IAC7B,OAAO1O,QAAA,CAAAqF,YAAY,CAACsJ,MAAM;EAC5B;EAEA,IAAIvN,OAAO,EAAEsC,UAAU,EAAE;IACvB,OAAO1D,QAAA,CAAAqF,YAAY,CAACuJ,mBAAmB;EACzC;EAEA,IAAIxN,OAAO,EAAEoD,YAAY,EAAE;IACzB,OAAOxE,QAAA,CAAAqF,YAAY,CAACwJ,YAAY;EAClC;EAEA,OAAO7O,QAAA,CAAAqF,YAAY,CAACqB,OAAO;AAC7B;AAEA;;;;;;AAMA,SAASM,sBAAsBA,CAACoH,QAAkB,EAAEtH,iBAAoC;EACtFsH,QAAQ,CAACpI,UAAU,CACjBjF,QAAQ,CAACiN,cAAc,EACvB,IAAI/N,QAAA,CAAA6O,kBAAkB,CAACV,QAAQ,CAAC/K,CAAC,CAACC,EAAE,EAAEwD,iBAAiB,CAACC,OAAO,CAAC,CACjE;EAED,MAAMa,MAAM,GAAG,IAAI1H,QAAA,CAAA6O,MAAM,CAACX,QAAQ,EAAEtH,iBAAiB,EAAEsH,QAAQ,CAAC/K,CAAC,CAACjC,OAAO,CAAC;EAC1E,KAAK,MAAM0D,KAAK,IAAIrF,WAAA,CAAAgP,mBAAmB,EAAE;IACvC7G,MAAM,CAACvG,EAAE,CAACyD,KAAK,EAAGkK,CAAM,IAAKZ,QAAQ,CAAChG,IAAI,CAACtD,KAAK,EAAEkK,CAAC,CAAC,CAAC;EACvD;EAEApH,MAAM,CAACvG,EAAE,CAACnB,QAAA,CAAA6O,MAAM,CAACE,oBAAoB,EAAEzL,WAAW,IAAI4K,QAAQ,CAACnH,mBAAmB,CAACzD,WAAW,CAAC,CAAC;EAEhGoE,MAAM,CAACtB,OAAO,EAAE;EAChB,OAAOsB,MAAM;AACf;AAEA;;;;AAIA,SAAS7B,aAAaA,CAACqI,QAAkB,EAAEc,yBAA6C;EACtF;EACA,IAAIA,yBAAyB,IAAId,QAAQ,CAAC/K,CAAC,CAACU,OAAO,CAACoL,GAAG,CAACD,yBAAyB,CAACnI,OAAO,CAAC,EAAE;IAC1F,MAAMa,MAAM,GAAGwG,QAAQ,CAAC/K,CAAC,CAACU,OAAO,CAACwI,GAAG,CAAC2C,yBAAyB,CAACnI,OAAO,CAAC;IACxE,IAAIa,MAAM,EAAE;MACVA,MAAM,CAACvE,CAAC,CAACG,WAAW,GAAG0L,yBAAyB;MAChD,IACEA,yBAAyB,CAAC3G,KAAK,YAAY7I,OAAA,CAAA0P,UAAU,IACrDF,yBAAyB,CAAC3G,KAAK,CAAC8G,aAAa,CAAC3P,OAAA,CAAA4P,eAAe,CAACC,SAAS,CAAC,EACxE;QACA,MAAMC,yBAAyB,GAAGN,yBAAyB,CAAC3G,KAAK,CAAC8G,aAAa,CAC7E3P,OAAA,CAAA4P,eAAe,CAACG,yBAAyB,CAC1C;QAED7H,MAAM,CAACuC,IAAI,CAACzB,KAAK,CAAC;UAAE8G;QAAyB,CAAE,CAAC;MAClD,CAAC,MAAM,IAAIN,yBAAyB,CAAC3G,KAAK,IAAI,IAAI,EAAE;QAClD,MAAMmH,eAAe,GAAGtB,QAAQ,CAAC/K,CAAC,CAACG,WAAW,CAACyB,IAAI;QACnD,MAAM0K,mBAAmB,GACvBT,yBAAyB,CAACU,aAAa,IACtCV,yBAAyB,CAACjK,IAAI,KAAKjF,QAAA,CAAA0N,UAAU,CAAChH,OAAO,IACpDgJ,eAAe,KAAK1P,QAAA,CAAAqF,YAAY,CAACsJ,MAAO;QAC5C,IAAIgB,mBAAmB,EAAE;UACvB/H,MAAM,CAACuC,IAAI,CAAC0F,KAAK,EAAE;QACrB;MACF;IACF;EACF;EAEA;EACA,KAAK,MAAM/I,iBAAiB,IAAIsH,QAAQ,CAAC5K,WAAW,CAACO,OAAO,CAAC6C,MAAM,EAAE,EAAE;IACrE,IAAI,CAACwH,QAAQ,CAAC/K,CAAC,CAACU,OAAO,CAACoL,GAAG,CAACrI,iBAAiB,CAACC,OAAO,CAAC,EAAE;MACtD,MAAMa,MAAM,GAAGZ,sBAAsB,CAACoH,QAAQ,EAAEtH,iBAAiB,CAAC;MAClEsH,QAAQ,CAAC/K,CAAC,CAACU,OAAO,CAACf,GAAG,CAAC8D,iBAAiB,CAACC,OAAO,EAAEa,MAAM,CAAC;IAC3D;EACF;EAEA;EACA,KAAK,MAAMkI,KAAK,IAAI1B,QAAQ,CAAC/K,CAAC,CAACU,OAAO,EAAE;IACtC,MAAMgM,aAAa,GAAGD,KAAK,CAAC,CAAC,CAAC;IAC9B,IAAI1B,QAAQ,CAAC5K,WAAW,CAAC4I,SAAS,CAAC2D,aAAa,CAAC,EAAE;MACjD;IACF;IAEA,IAAI,CAAC3B,QAAQ,CAAC/K,CAAC,CAACU,OAAO,CAACoL,GAAG,CAACY,aAAa,CAAC,EAAE;MAC1C;IACF;IAEA,MAAMnI,MAAM,GAAGwG,QAAQ,CAAC/K,CAAC,CAACU,OAAO,CAACwI,GAAG,CAACwD,aAAa,CAAC;IACpD3B,QAAQ,CAAC/K,CAAC,CAACU,OAAO,CAACiM,MAAM,CAACD,aAAa,CAAC;IAExC;IACA,IAAInI,MAAM,EAAE;MACVa,aAAa,CAACb,MAAM,EAAEwG,QAAQ,CAAC;IACjC;EACF;AACF;AAEA,SAASzF,cAAcA,CAACsH,KAAmC,EAAEC,UAA4B;EACvF,OAAOD,KAAK,CAACtN,MAAM,EAAE;IACnB,MAAM+H,eAAe,GAAGuF,KAAK,CAACE,KAAK,EAAE;IACrC,IAAI,CAACzF,eAAe,EAAE;MACpB;IACF;IAEA,IAAI,CAACA,eAAe,CAACE,SAAS,EAAE;MAC9B,IACEF,eAAe,CAACrG,WAAW,EAAE+E,OAAO,CAClCzJ,cAAA,CAAA0J,sBAAsB,CAACC,gBAAgB,EACvC3J,cAAA,CAAA4J,aAAa,CAACC,KAAK,CACpB,EACD;QACAkB,eAAe,CAACrG,WAAW,EAAEoF,KAAK,CAChC9J,cAAA,CAAA0J,sBAAsB,CAACC,gBAAgB,EACvC,IAAIjJ,yBAAA,CAAA2L,0BAA0B,CAC5BtB,eAAe,CAACxB,cAAc,EAC9BwB,eAAe,CAACC,mBAAmB,EACnCuF,UAAU,EACVxF,eAAe,CAAC/C,aAAa,CAC9B,CACF;MACH;MACA+C,eAAe,CAACF,MAAM,CAAC0F,UAAU,CAAC;IACpC;EACF;AACF;AAEA,SAAS7E,gBAAgBA,CAAC+C,QAAkB;EAC1C,IAAIA,QAAQ,CAAC/K,CAAC,CAACE,KAAK,KAAKvD,QAAA,CAAAW,YAAY,EAAE;IACrCgI,cAAc,CAACyF,QAAQ,CAACjL,SAAS,EAAE,IAAIzD,OAAA,CAAAkJ,wBAAwB,EAAE,CAAC;IAClE;EACF;EAEA,MAAMmB,SAAS,GAAGqE,QAAQ,CAAC5K,WAAW,CAACyB,IAAI,KAAKjF,QAAA,CAAAqF,YAAY,CAACC,OAAO;EACpE,MAAMzC,kBAAkB,GAAGf,KAAK,CAAC6E,IAAI,CAACyH,QAAQ,CAAC5K,WAAW,CAACO,OAAO,CAAC6C,MAAM,EAAE,CAAC;EAC5E,MAAMwJ,gBAAgB,GAAGhC,QAAQ,CAACjL,SAAS,CAACR,MAAM;EAClD,KAAK,IAAI0N,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGD,gBAAgB,EAAE,EAAEC,CAAC,EAAE;IACzC,MAAM3F,eAAe,GAAG0D,QAAQ,CAACjL,SAAS,CAACgN,KAAK,EAAE;IAClD,IAAI,CAACzF,eAAe,EAAE;MACpB;IACF;IAEA,IAAIA,eAAe,CAACE,SAAS,EAAE;MAC7B;IACF;IAEA,IAAI0F,oBAAoB;IACxB,IAAI;MACF,MAAMpH,cAAc,GAAGwB,eAAe,CAACxB,cAAc;MACrD,MAAM8B,cAAc,GAAGN,eAAe,CAACM,cAAc;MACrDsF,oBAAoB,GAAGpH,cAAc,GACjCA,cAAc,CACZkF,QAAQ,CAAC5K,WAAW,EACpBX,kBAAkB,EAClBmI,cAAc,GAAG,CAACA,cAAc,CAAC,GAAG,EAAE,CACvC,GACDnI,kBAAkB;IACxB,CAAC,CAAC,OAAO0N,aAAa,EAAE;MACtB,IACEnC,QAAQ,CAAClN,MAAM,CAACmD,WAAW,EAAE+E,OAAO,CAClCzJ,cAAA,CAAA0J,sBAAsB,CAACC,gBAAgB,EACvC3J,cAAA,CAAA4J,aAAa,CAACC,KAAK,CACpB,EACD;QACA4E,QAAQ,CAAClN,MAAM,CAACmD,WAAW,EAAEoF,KAAK,CAChC9J,cAAA,CAAA0J,sBAAsB,CAACC,gBAAgB,EACvC,IAAIjJ,yBAAA,CAAA2L,0BAA0B,CAC5BtB,eAAe,CAACxB,cAAc,EAC9BkF,QAAQ,CAAC5K,WAAW,EACpB+M,aAAa,EACb7F,eAAe,CAAC/C,aAAa,CAC9B,CACF;MACH;MACA+C,eAAe,CAACF,MAAM,CAAC+F,aAAa,CAAC;MACrC;IACF;IAEA,IAAIC,cAAkC;IACtC,IAAIF,oBAAoB,CAAC3N,MAAM,KAAK,CAAC,EAAE;MACrC,IAAI,CAAC+H,eAAe,CAACK,aAAa,EAAE;QAClC,IACEqD,QAAQ,CAAClN,MAAM,CAACmD,WAAW,EAAE+E,OAAO,CAClCzJ,cAAA,CAAA0J,sBAAsB,CAACC,gBAAgB,EACvC3J,cAAA,CAAA4J,aAAa,CAACkH,aAAa,CAC5B,EACD;UACArC,QAAQ,CAAClN,MAAM,CAACmD,WAAW,EAAEqM,IAAI,CAC/B/Q,cAAA,CAAA0J,sBAAsB,CAACC,gBAAgB,EACvC,IAAIjJ,yBAAA,CAAAsQ,6BAA6B,CAC/BjG,eAAe,CAACxB,cAAc,EAC9BkF,QAAQ,CAAC5K,WAAW,EACpB4K,QAAQ,CAAC/K,CAAC,CAACO,wBAAwB,KAAK,CAAC,GACrCwK,QAAQ,CAAC/K,CAAC,CAACO,wBAAwB,IAAI,IAAA7D,OAAA,CAAA+K,GAAG,GAAE,GAAGJ,eAAe,CAACG,SAAS,CAAC,GACzE,CAAC,CAAC,EACNH,eAAe,CAAC/C,aAAa,CAC9B,CACF;QACH;QACA+C,eAAe,CAACK,aAAa,GAAG,IAAI;MACtC;MACAqD,QAAQ,CAACjL,SAAS,CAACjB,IAAI,CAACwI,eAAe,CAAC;MACxC;IACF,CAAC,MAAM,IAAI4F,oBAAoB,CAAC3N,MAAM,KAAK,CAAC,EAAE;MAC5C6N,cAAc,GAAGpC,QAAQ,CAAC/K,CAAC,CAACU,OAAO,CAACwI,GAAG,CAAC+D,oBAAoB,CAAC,CAAC,CAAC,CAACvJ,OAAO,CAAC;IAC1E,CAAC,MAAM;MACL,MAAM6J,YAAY,GAAG,IAAA7Q,OAAA,CAAA6C,OAAO,EAAC0N,oBAAoB,EAAE,CAAC,CAAC;MACrD,MAAMO,OAAO,GAAGzC,QAAQ,CAAC/K,CAAC,CAACU,OAAO,CAACwI,GAAG,CAACqE,YAAY,CAAC,CAAC,CAAC,CAAC7J,OAAO,CAAC;MAC/D,MAAM+J,OAAO,GAAG1C,QAAQ,CAAC/K,CAAC,CAACU,OAAO,CAACwI,GAAG,CAACqE,YAAY,CAAC,CAAC,CAAC,CAAC7J,OAAO,CAAC;MAE/DyJ,cAAc,GACZK,OAAO,IAAIC,OAAO,IAAID,OAAO,CAACxN,CAAC,CAAC0N,cAAc,GAAGD,OAAO,CAACzN,CAAC,CAAC0N,cAAc,GACrEF,OAAO,GACPC,OAAO;IACf;IAEA,IAAI,CAACN,cAAc,EAAE;MACnB,MAAMQ,oBAAoB,GAAG,IAAItR,OAAA,CAAAoM,yBAAyB,CACxD,6FAA6F,EAC7FsC,QAAQ,CAAC5K,WAAW,CACrB;MACD,IACE4K,QAAQ,CAAClN,MAAM,CAACmD,WAAW,EAAE+E,OAAO,CAClCzJ,cAAA,CAAA0J,sBAAsB,CAACC,gBAAgB,EACvC3J,cAAA,CAAA4J,aAAa,CAACC,KAAK,CACpB,EACD;QACA4E,QAAQ,CAAClN,MAAM,CAACmD,WAAW,EAAEoF,KAAK,CAChC9J,cAAA,CAAA0J,sBAAsB,CAACC,gBAAgB,EACvC,IAAIjJ,yBAAA,CAAA2L,0BAA0B,CAC5BtB,eAAe,CAACxB,cAAc,EAC9BkF,QAAQ,CAAC5K,WAAW,EACpBwN,oBAAoB,EACpBtG,eAAe,CAAC/C,aAAa,CAC9B,CACF;MACH;MACA+C,eAAe,CAACF,MAAM,CAACwG,oBAAoB,CAAC;MAC5C;IACF;IACA,MAAM/G,WAAW,GAAGS,eAAe,CAACT,WAAW;IAC/C,IAAIF,SAAS,IAAIE,WAAW,IAAIA,WAAW,CAACgH,QAAQ,IAAIT,cAAc,EAAE;MACtEvG,WAAW,CAACiH,SAAS,CAACV,cAAc,CAAC;IACvC;IAEA,IACEpC,QAAQ,CAAClN,MAAM,CAACmD,WAAW,EAAE+E,OAAO,CAClCzJ,cAAA,CAAA0J,sBAAsB,CAACC,gBAAgB,EACvC3J,cAAA,CAAA4J,aAAa,CAACC,KAAK,CACpB,EACD;MACA4E,QAAQ,CAAClN,MAAM,CAACmD,WAAW,EAAEoF,KAAK,CAChC9J,cAAA,CAAA0J,sBAAsB,CAACC,gBAAgB,EACvC,IAAIjJ,yBAAA,CAAA6J,6BAA6B,CAC/BQ,eAAe,CAACxB,cAAc,EAC9BwB,eAAe,CAACC,mBAAmB,EACnC6F,cAAc,CAACrG,IAAI,CAACpD,OAAO,EAC3B2D,eAAe,CAAC/C,aAAa,CAC9B,CACF;IACH;IACA+C,eAAe,CAACH,OAAO,CAACiG,cAAc,CAAC;EACzC;EAEA,IAAIpC,QAAQ,CAACjL,SAAS,CAACR,MAAM,GAAG,CAAC,EAAE;IACjC;IACA,KAAK,MAAM,GAAGiF,MAAM,CAAC,IAAIwG,QAAQ,CAAC/K,CAAC,CAACU,OAAO,EAAE;MAC3CoN,OAAO,CAACC,QAAQ,CAAC,SAASC,mBAAmBA,CAAA;QAC3C,OAAOzJ,MAAM,CAAC0J,YAAY,EAAE;MAC9B,CAAC,CAAC;IACJ;EACF;AACF;AAEA,SAASjF,wBAAwBA,CAC/B1B,mBAAwC,EACxCuE,yBAA4C;EAE5C,MAAMqC,wBAAwB,GAAG5G,mBAAmB,CAAC5G,OAAO,CAACwI,GAAG,CAC9D2C,yBAAyB,CAACnI,OAAO,CAClC;EACD,MAAMyK,sBAAsB,GAAGD,wBAAwB,EAAEE,eAAe;EACxE,OACE,IAAAtR,oBAAA,CAAAuR,sBAAsB,EAACF,sBAAsB,EAAEtC,yBAAyB,CAACuC,eAAe,CAAC,GAAG,CAAC;AAEjG;AAEA;AACA,MAAarL,kBAAkB;EAI7BnF,YAAY0Q,KAAe;IACzB,IAAI,CAACC,cAAc,GAAGD,KAAK,CAACC,cAAc,IAAI,CAAC;IAC/C,IAAI,CAAChE,cAAc,GAAG+D,KAAK,CAAC/D,cAAc,IAAI,CAAC;EACjD;EAEA,IAAIiE,oBAAoBA,CAAA;IACtB,OAAO,IAAI,CAACjE,cAAc,IAAI,CAAC;EACjC;EAEA,IAAIkE,gBAAgBA,CAAA;IAClB,OAAO,IAAI,CAAClE,cAAc,IAAI,CAAC;EACjC;EACA,IAAImE,aAAaA,CAAA;IACf,OAAO,IAAI,CAACH,cAAc,IAAI,CAAC;EACjC;EAEA,IAAII,eAAeA,CAAA;IACjB,OAAO,IAAI,CAACpE,cAAc,IAAI,CAAC;EACjC;EAEA,IAAIqE,yBAAyBA,CAAA;IAC3B,OAAO,IAAI,CAACrE,cAAc,IAAI,CAAC;EACjC;EAEA,IAAIsE,qBAAqBA,CAAA;IACvB,OAAO,IAAI,CAACtE,cAAc,IAAI,CAAC;EACjC;EAEA,IAAIuE,qBAAqBA,CAAA;IACvB,OAAO,IAAI,CAACvE,cAAc,IAAI,EAAE;EAClC;EAEA,IAAIwE,wBAAwBA,CAAA;IAC1B,OAAO,IAAI,CAACxE,cAAc,IAAI,CAAC;EACjC;EAEA,IAAIyE,qBAAqBA,CAAA;IACvB,OAAO,IAAI,CAACzE,cAAc,IAAI,CAAC;EACjC;;AA1CFG,OAAA,CAAA3H,kBAAA,GAAAA,kBAAA", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}