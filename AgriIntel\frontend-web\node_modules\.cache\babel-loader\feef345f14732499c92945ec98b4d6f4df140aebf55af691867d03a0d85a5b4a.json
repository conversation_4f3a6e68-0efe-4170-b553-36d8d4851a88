{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\Desktop\\\\AgriIntel\\\\frontend-web\\\\src\\\\index.tsx\";\nimport React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { Provider } from 'react-redux';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { ReactQueryDevtools } from 'react-query/devtools';\nimport { BrowserRouter } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\nimport './index.css';\nimport './i18n/i18n';\nimport App from './App';\nimport { store } from './store/store';\nimport reportWebVitals from './reportWebVitals';\n\n// Create React Query client\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000 // 5 minutes\n    }\n  }\n});\n\n// Create Material-UI theme\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#0f766e',\n      light: '#14b8a6',\n      dark: '#134e4a'\n    },\n    secondary: {\n      main: '#374151',\n      light: '#6b7280',\n      dark: '#1f2937'\n    },\n    success: {\n      main: '#059669',\n      light: '#10b981',\n      dark: '#047857'\n    },\n    warning: {\n      main: '#d97706',\n      light: '#f59e0b',\n      dark: '#b45309'\n    },\n    error: {\n      main: '#dc2626',\n      light: '#ef4444',\n      dark: '#b91c1c'\n    },\n    background: {\n      default: '#f9fafb',\n      paper: '#ffffff'\n    }\n  },\n  typography: {\n    fontFamily: '\"Inter\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontWeight: 700,\n      fontSize: '2.5rem'\n    },\n    h2: {\n      fontWeight: 600,\n      fontSize: '2rem'\n    },\n    h3: {\n      fontWeight: 600,\n      fontSize: '1.75rem'\n    },\n    h4: {\n      fontWeight: 600,\n      fontSize: '1.5rem'\n    },\n    h5: {\n      fontWeight: 600,\n      fontSize: '1.25rem'\n    },\n    h6: {\n      fontWeight: 600,\n      fontSize: '1rem'\n    },\n    body1: {\n      fontSize: '1rem',\n      lineHeight: 1.5\n    },\n    body2: {\n      fontSize: '0.875rem',\n      lineHeight: 1.43\n    }\n  },\n  shape: {\n    borderRadius: 8\n  },\n  components: {\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          fontWeight: 500,\n          borderRadius: 8,\n          padding: '8px 16px'\n        }\n      }\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          boxShadow: '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',\n          borderRadius: 12\n        }\n      }\n    },\n    MuiTextField: {\n      styleOverrides: {\n        root: {\n          '& .MuiOutlinedInput-root': {\n            borderRadius: 8\n          }\n        }\n      }\n    }\n  }\n});\nconst root = ReactDOM.createRoot(document.getElementById('root'));\nroot.render(/*#__PURE__*/_jsxDEV(React.StrictMode, {\n  children: /*#__PURE__*/_jsxDEV(Provider, {\n    store: store,\n    children: /*#__PURE__*/_jsxDEV(QueryClientProvider, {\n      client: queryClient,\n      children: [/*#__PURE__*/_jsxDEV(ThemeProvider, {\n        theme: theme,\n        children: [/*#__PURE__*/_jsxDEV(CssBaseline, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 138,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(BrowserRouter, {\n          children: /*#__PURE__*/_jsxDEV(App, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 140,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 139,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 137,\n        columnNumber: 9\n      }, this), process.env.NODE_ENV === 'development' && /*#__PURE__*/_jsxDEV(ReactQueryDevtools, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 143,\n        columnNumber: 52\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 136,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 135,\n    columnNumber: 5\n  }, this)\n}, void 0, false, {\n  fileName: _jsxFileName,\n  lineNumber: 134,\n  columnNumber: 3\n}, this));\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();", "map": {"version": 3, "names": ["React", "ReactDOM", "Provider", "QueryClient", "QueryClientProvider", "ReactQueryDevtools", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ThemeProvider", "createTheme", "CssBaseline", "App", "store", "reportWebVitals", "jsxDEV", "_jsxDEV", "queryClient", "defaultOptions", "queries", "retry", "refetchOnWindowFocus", "staleTime", "theme", "palette", "primary", "main", "light", "dark", "secondary", "success", "warning", "error", "background", "default", "paper", "typography", "fontFamily", "h1", "fontWeight", "fontSize", "h2", "h3", "h4", "h5", "h6", "body1", "lineHeight", "body2", "shape", "borderRadius", "components", "MuiB<PERSON>on", "styleOverrides", "root", "textTransform", "padding", "MuiCard", "boxShadow", "MuiTextField", "createRoot", "document", "getElementById", "render", "StrictMode", "children", "client", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "process", "env", "NODE_ENV"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/index.tsx"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { Provider } from 'react-redux';\nimport { QueryClient, QueryClientProvider } from 'react-query';\nimport { ReactQueryDevtools } from 'react-query/devtools';\nimport { <PERSON>rowserRouter } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\n\nimport './index.css';\nimport './i18n/i18n';\nimport App from './App';\nimport { store } from './store/store';\nimport reportWebVitals from './reportWebVitals';\n\n// Create React Query client\nconst queryClient = new QueryClient({\n  defaultOptions: {\n    queries: {\n      retry: 1,\n      refetchOnWindowFocus: false,\n      staleTime: 5 * 60 * 1000, // 5 minutes\n    },\n  },\n});\n\n// Create Material-UI theme\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#0f766e',\n      light: '#14b8a6',\n      dark: '#134e4a',\n    },\n    secondary: {\n      main: '#374151',\n      light: '#6b7280',\n      dark: '#1f2937',\n    },\n    success: {\n      main: '#059669',\n      light: '#10b981',\n      dark: '#047857',\n    },\n    warning: {\n      main: '#d97706',\n      light: '#f59e0b',\n      dark: '#b45309',\n    },\n    error: {\n      main: '#dc2626',\n      light: '#ef4444',\n      dark: '#b91c1c',\n    },\n    background: {\n      default: '#f9fafb',\n      paper: '#ffffff',\n    },\n  },\n  typography: {\n    fontFamily: '\"Inter\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontWeight: 700,\n      fontSize: '2.5rem',\n    },\n    h2: {\n      fontWeight: 600,\n      fontSize: '2rem',\n    },\n    h3: {\n      fontWeight: 600,\n      fontSize: '1.75rem',\n    },\n    h4: {\n      fontWeight: 600,\n      fontSize: '1.5rem',\n    },\n    h5: {\n      fontWeight: 600,\n      fontSize: '1.25rem',\n    },\n    h6: {\n      fontWeight: 600,\n      fontSize: '1rem',\n    },\n    body1: {\n      fontSize: '1rem',\n      lineHeight: 1.5,\n    },\n    body2: {\n      fontSize: '0.875rem',\n      lineHeight: 1.43,\n    },\n  },\n  shape: {\n    borderRadius: 8,\n  },\n  components: {\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          fontWeight: 500,\n          borderRadius: 8,\n          padding: '8px 16px',\n        },\n      },\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          boxShadow: '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',\n          borderRadius: 12,\n        },\n      },\n    },\n    MuiTextField: {\n      styleOverrides: {\n        root: {\n          '& .MuiOutlinedInput-root': {\n            borderRadius: 8,\n          },\n        },\n      },\n    },\n  },\n});\n\nconst root = ReactDOM.createRoot(\n  document.getElementById('root') as HTMLElement\n);\n\nroot.render(\n  <React.StrictMode>\n    <Provider store={store}>\n      <QueryClientProvider client={queryClient}>\n        <ThemeProvider theme={theme}>\n          <CssBaseline />\n          <BrowserRouter>\n            <App />\n          </BrowserRouter>\n        </ThemeProvider>\n        {process.env.NODE_ENV === 'development' && <ReactQueryDevtools />}\n      </QueryClientProvider>\n    </Provider>\n  </React.StrictMode>\n);\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\nreportWebVitals();\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,kBAAkB;AACvC,SAASC,QAAQ,QAAQ,aAAa;AACtC,SAASC,WAAW,EAAEC,mBAAmB,QAAQ,aAAa;AAC9D,SAASC,kBAAkB,QAAQ,sBAAsB;AACzD,SAASC,aAAa,QAAQ,kBAAkB;AAChD,SAASC,aAAa,EAAEC,WAAW,QAAQ,sBAAsB;AACjE,OAAOC,WAAW,MAAM,2BAA2B;AAEnD,OAAO,aAAa;AACpB,OAAO,aAAa;AACpB,OAAOC,GAAG,MAAM,OAAO;AACvB,SAASC,KAAK,QAAQ,eAAe;AACrC,OAAOC,eAAe,MAAM,mBAAmB;;AAE/C;AAAA,SAAAC,MAAA,IAAAC,OAAA;AACA,MAAMC,WAAW,GAAG,IAAIZ,WAAW,CAAC;EAClCa,cAAc,EAAE;IACdC,OAAO,EAAE;MACPC,KAAK,EAAE,CAAC;MACRC,oBAAoB,EAAE,KAAK;MAC3BC,SAAS,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI,CAAE;IAC5B;EACF;AACF,CAAC,CAAC;;AAEF;AACA,MAAMC,KAAK,GAAGb,WAAW,CAAC;EACxBc,OAAO,EAAE;IACPC,OAAO,EAAE;MACPC,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDC,SAAS,EAAE;MACTH,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDE,OAAO,EAAE;MACPJ,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDG,OAAO,EAAE;MACPL,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDI,KAAK,EAAE;MACLN,IAAI,EAAE,SAAS;MACfC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;IACR,CAAC;IACDK,UAAU,EAAE;MACVC,OAAO,EAAE,SAAS;MAClBC,KAAK,EAAE;IACT;EACF,CAAC;EACDC,UAAU,EAAE;IACVC,UAAU,EAAE,qDAAqD;IACjEC,EAAE,EAAE;MACFC,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;IACZ,CAAC;IACDC,EAAE,EAAE;MACFF,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;IACZ,CAAC;IACDE,EAAE,EAAE;MACFH,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;IACZ,CAAC;IACDG,EAAE,EAAE;MACFJ,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;IACZ,CAAC;IACDI,EAAE,EAAE;MACFL,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;IACZ,CAAC;IACDK,EAAE,EAAE;MACFN,UAAU,EAAE,GAAG;MACfC,QAAQ,EAAE;IACZ,CAAC;IACDM,KAAK,EAAE;MACLN,QAAQ,EAAE,MAAM;MAChBO,UAAU,EAAE;IACd,CAAC;IACDC,KAAK,EAAE;MACLR,QAAQ,EAAE,UAAU;MACpBO,UAAU,EAAE;IACd;EACF,CAAC;EACDE,KAAK,EAAE;IACLC,YAAY,EAAE;EAChB,CAAC;EACDC,UAAU,EAAE;IACVC,SAAS,EAAE;MACTC,cAAc,EAAE;QACdC,IAAI,EAAE;UACJC,aAAa,EAAE,MAAM;UACrBhB,UAAU,EAAE,GAAG;UACfW,YAAY,EAAE,CAAC;UACfM,OAAO,EAAE;QACX;MACF;IACF,CAAC;IACDC,OAAO,EAAE;MACPJ,cAAc,EAAE;QACdC,IAAI,EAAE;UACJI,SAAS,EAAE,2EAA2E;UACtFR,YAAY,EAAE;QAChB;MACF;IACF,CAAC;IACDS,YAAY,EAAE;MACZN,cAAc,EAAE;QACdC,IAAI,EAAE;UACJ,0BAA0B,EAAE;YAC1BJ,YAAY,EAAE;UAChB;QACF;MACF;IACF;EACF;AACF,CAAC,CAAC;AAEF,MAAMI,IAAI,GAAGnD,QAAQ,CAACyD,UAAU,CAC9BC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC;AAEDR,IAAI,CAACS,MAAM,cACT/C,OAAA,CAACd,KAAK,CAAC8D,UAAU;EAAAC,QAAA,eACfjD,OAAA,CAACZ,QAAQ;IAACS,KAAK,EAAEA,KAAM;IAAAoD,QAAA,eACrBjD,OAAA,CAACV,mBAAmB;MAAC4D,MAAM,EAAEjD,WAAY;MAAAgD,QAAA,gBACvCjD,OAAA,CAACP,aAAa;QAACc,KAAK,EAAEA,KAAM;QAAA0C,QAAA,gBAC1BjD,OAAA,CAACL,WAAW;UAAAwD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eACftD,OAAA,CAACR,aAAa;UAAAyD,QAAA,eACZjD,OAAA,CAACJ,GAAG;YAAAuD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EACfC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,aAAa,iBAAIzD,OAAA,CAACT,kBAAkB;QAAA4D,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC9C;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACd;AAAC;EAAAH,QAAA,EAAAC,YAAA;EAAAC,UAAA;EAAAC,YAAA;AAAA,OACK,CACpB,CAAC;;AAED;AACA;AACA;AACAxD,eAAe,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}