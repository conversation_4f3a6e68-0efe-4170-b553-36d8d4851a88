{"ast": null, "code": "import _objectWithoutProperties from \"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\";\nimport _objectSpread from \"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";\nconst _excluded = [\"format\"],\n  _excluded2 = [\"children\", \"count\", \"parent\", \"i18nKey\", \"context\", \"tOptions\", \"values\", \"defaults\", \"components\", \"ns\", \"i18n\", \"t\", \"shouldUnescape\"];\nimport _extends from \"@babel/runtime/helpers/extends\";\nimport React, { isValidElement, cloneElement, createElement, Children } from 'react';\nimport HTML from 'html-parse-stringify';\nimport { warn, warnOnce } from './utils.js';\nimport { getDefaults } from './defaults.js';\nimport { getI18n } from './i18nInstance.js';\nfunction hasChildren(node, checkLength) {\n  if (!node) return false;\n  const base = node.props ? node.props.children : node.children;\n  if (checkLength) return base.length > 0;\n  return !!base;\n}\nfunction getChildren(node) {\n  if (!node) return [];\n  const children = node.props ? node.props.children : node.children;\n  return node.props && node.props.i18nIsDynamicList ? getAsArray(children) : children;\n}\nfunction hasValidReactChildren(children) {\n  if (Object.prototype.toString.call(children) !== '[object Array]') return false;\n  return children.every(child => isValidElement(child));\n}\nfunction getAsArray(data) {\n  return Array.isArray(data) ? data : [data];\n}\nfunction mergeProps(source, target) {\n  const newTarget = _objectSpread({}, target);\n  newTarget.props = Object.assign(source.props, target.props);\n  return newTarget;\n}\nexport function nodesToString(children, i18nOptions) {\n  if (!children) return '';\n  let stringNode = '';\n  const childrenArray = getAsArray(children);\n  const keepArray = i18nOptions.transSupportBasicHtmlNodes && i18nOptions.transKeepBasicHtmlNodesFor ? i18nOptions.transKeepBasicHtmlNodesFor : [];\n  childrenArray.forEach((child, childIndex) => {\n    if (typeof child === 'string') {\n      stringNode += \"\".concat(child);\n    } else if (isValidElement(child)) {\n      const childPropsCount = Object.keys(child.props).length;\n      const shouldKeepChild = keepArray.indexOf(child.type) > -1;\n      const childChildren = child.props.children;\n      if (!childChildren && shouldKeepChild && childPropsCount === 0) {\n        stringNode += \"<\".concat(child.type, \"/>\");\n      } else if (!childChildren && (!shouldKeepChild || childPropsCount !== 0)) {\n        stringNode += \"<\".concat(childIndex, \"></\").concat(childIndex, \">\");\n      } else if (child.props.i18nIsDynamicList) {\n        stringNode += \"<\".concat(childIndex, \"></\").concat(childIndex, \">\");\n      } else if (shouldKeepChild && childPropsCount === 1 && typeof childChildren === 'string') {\n        stringNode += \"<\".concat(child.type, \">\").concat(childChildren, \"</\").concat(child.type, \">\");\n      } else {\n        const content = nodesToString(childChildren, i18nOptions);\n        stringNode += \"<\".concat(childIndex, \">\").concat(content, \"</\").concat(childIndex, \">\");\n      }\n    } else if (child === null) {\n      warn(\"Trans: the passed in value is invalid - seems you passed in a null child.\");\n    } else if (typeof child === 'object') {\n      const {\n          format\n        } = child,\n        clone = _objectWithoutProperties(child, _excluded);\n      const keys = Object.keys(clone);\n      if (keys.length === 1) {\n        const value = format ? \"\".concat(keys[0], \", \").concat(format) : keys[0];\n        stringNode += \"{{\".concat(value, \"}}\");\n      } else {\n        warn(\"react-i18next: the passed in object contained more than one variable - the object should look like {{ value, format }} where format is optional.\", child);\n      }\n    } else {\n      warn(\"Trans: the passed in value is invalid - seems you passed in a variable like {number} - please pass in variables for interpolation as full objects like {{number}}.\", child);\n    }\n  });\n  return stringNode;\n}\nfunction renderNodes(children, targetString, i18n, i18nOptions, combinedTOpts, shouldUnescape) {\n  if (targetString === '') return [];\n  const keepArray = i18nOptions.transKeepBasicHtmlNodesFor || [];\n  const emptyChildrenButNeedsHandling = targetString && new RegExp(keepArray.map(keep => \"<\".concat(keep)).join('|')).test(targetString);\n  if (!children && !emptyChildrenButNeedsHandling && !shouldUnescape) return [targetString];\n  const data = {};\n  function getData(childs) {\n    const childrenArray = getAsArray(childs);\n    childrenArray.forEach(child => {\n      if (typeof child === 'string') return;\n      if (hasChildren(child)) getData(getChildren(child));else if (typeof child === 'object' && !isValidElement(child)) Object.assign(data, child);\n    });\n  }\n  getData(children);\n  const ast = HTML.parse(\"<0>\".concat(targetString, \"</0>\"));\n  const opts = _objectSpread(_objectSpread({}, data), combinedTOpts);\n  function renderInner(child, node, rootReactNode) {\n    const childs = getChildren(child);\n    const mappedChildren = mapAST(childs, node.children, rootReactNode);\n    return hasValidReactChildren(childs) && mappedChildren.length === 0 || child.props && child.props.i18nIsDynamicList ? childs : mappedChildren;\n  }\n  function pushTranslatedJSX(child, inner, mem, i, isVoid) {\n    if (child.dummy) {\n      child.children = inner;\n      mem.push(cloneElement(child, {\n        key: i\n      }, isVoid ? undefined : inner));\n    } else {\n      mem.push(...Children.map([child], c => {\n        const props = _objectSpread({}, c.props);\n        delete props.i18nIsDynamicList;\n        return React.createElement(c.type, _extends({}, props, {\n          key: i,\n          ref: c.ref\n        }, isVoid ? {} : {\n          children: inner\n        }));\n      }));\n    }\n  }\n  function mapAST(reactNode, astNode, rootReactNode) {\n    const reactNodes = getAsArray(reactNode);\n    const astNodes = getAsArray(astNode);\n    return astNodes.reduce((mem, node, i) => {\n      const translationContent = node.children && node.children[0] && node.children[0].content && i18n.services.interpolator.interpolate(node.children[0].content, opts, i18n.language);\n      if (node.type === 'tag') {\n        let tmp = reactNodes[parseInt(node.name, 10)];\n        if (rootReactNode.length === 1 && !tmp) tmp = rootReactNode[0][node.name];\n        if (!tmp) tmp = {};\n        const child = Object.keys(node.attrs).length !== 0 ? mergeProps({\n          props: node.attrs\n        }, tmp) : tmp;\n        const isElement = isValidElement(child);\n        const isValidTranslationWithChildren = isElement && hasChildren(node, true) && !node.voidElement;\n        const isEmptyTransWithHTML = emptyChildrenButNeedsHandling && typeof child === 'object' && child.dummy && !isElement;\n        const isKnownComponent = typeof children === 'object' && children !== null && Object.hasOwnProperty.call(children, node.name);\n        if (typeof child === 'string') {\n          const value = i18n.services.interpolator.interpolate(child, opts, i18n.language);\n          mem.push(value);\n        } else if (hasChildren(child) || isValidTranslationWithChildren) {\n          const inner = renderInner(child, node, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (isEmptyTransWithHTML) {\n          const inner = mapAST(reactNodes, node.children, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (Number.isNaN(parseFloat(node.name))) {\n          if (isKnownComponent) {\n            const inner = renderInner(child, node, rootReactNode);\n            pushTranslatedJSX(child, inner, mem, i, node.voidElement);\n          } else if (i18nOptions.transSupportBasicHtmlNodes && keepArray.indexOf(node.name) > -1) {\n            if (node.voidElement) {\n              mem.push(createElement(node.name, {\n                key: \"\".concat(node.name, \"-\").concat(i)\n              }));\n            } else {\n              const inner = mapAST(reactNodes, node.children, rootReactNode);\n              mem.push(createElement(node.name, {\n                key: \"\".concat(node.name, \"-\").concat(i)\n              }, inner));\n            }\n          } else if (node.voidElement) {\n            mem.push(\"<\".concat(node.name, \" />\"));\n          } else {\n            const inner = mapAST(reactNodes, node.children, rootReactNode);\n            mem.push(\"<\".concat(node.name, \">\").concat(inner, \"</\").concat(node.name, \">\"));\n          }\n        } else if (typeof child === 'object' && !isElement) {\n          const content = node.children[0] ? translationContent : null;\n          if (content) mem.push(content);\n        } else {\n          pushTranslatedJSX(child, translationContent, mem, i, node.children.length !== 1 || !translationContent);\n        }\n      } else if (node.type === 'text') {\n        const wrapTextNodes = i18nOptions.transWrapTextNodes;\n        const content = shouldUnescape ? i18nOptions.unescape(i18n.services.interpolator.interpolate(node.content, opts, i18n.language)) : i18n.services.interpolator.interpolate(node.content, opts, i18n.language);\n        if (wrapTextNodes) {\n          mem.push(createElement(wrapTextNodes, {\n            key: \"\".concat(node.name, \"-\").concat(i)\n          }, content));\n        } else {\n          mem.push(content);\n        }\n      }\n      return mem;\n    }, []);\n  }\n  const result = mapAST([{\n    dummy: true,\n    children: children || []\n  }], ast, getAsArray(children || []));\n  return getChildren(result[0]);\n}\nexport function Trans(_ref) {\n  let {\n      children,\n      count,\n      parent,\n      i18nKey,\n      context,\n      tOptions = {},\n      values,\n      defaults,\n      components,\n      ns,\n      i18n: i18nFromProps,\n      t: tFromProps,\n      shouldUnescape\n    } = _ref,\n    additionalProps = _objectWithoutProperties(_ref, _excluded2);\n  const i18n = i18nFromProps || getI18n();\n  if (!i18n) {\n    warnOnce('You will need to pass in an i18next instance by using i18nextReactModule');\n    return children;\n  }\n  const t = tFromProps || i18n.t.bind(i18n) || (k => k);\n  if (context) tOptions.context = context;\n  const reactI18nextOptions = _objectSpread(_objectSpread({}, getDefaults()), i18n.options && i18n.options.react);\n  let namespaces = ns || t.ns || i18n.options && i18n.options.defaultNS;\n  namespaces = typeof namespaces === 'string' ? [namespaces] : namespaces || ['translation'];\n  const nodeAsString = nodesToString(children, reactI18nextOptions);\n  const defaultValue = defaults || nodeAsString || reactI18nextOptions.transEmptyNodeValue || i18nKey;\n  const {\n    hashTransKey\n  } = reactI18nextOptions;\n  const key = i18nKey || (hashTransKey ? hashTransKey(nodeAsString || defaultValue) : nodeAsString || defaultValue);\n  if (i18n.options && i18n.options.interpolation && i18n.options.interpolation.defaultVariables) {\n    values = values && Object.keys(values).length > 0 ? _objectSpread(_objectSpread({}, values), i18n.options.interpolation.defaultVariables) : _objectSpread({}, i18n.options.interpolation.defaultVariables);\n  }\n  const interpolationOverride = values ? tOptions.interpolation : {\n    interpolation: _objectSpread(_objectSpread({}, tOptions.interpolation), {}, {\n      prefix: '#$?',\n      suffix: '?$#'\n    })\n  };\n  const combinedTOpts = _objectSpread(_objectSpread(_objectSpread(_objectSpread({}, tOptions), {}, {\n    count\n  }, values), interpolationOverride), {}, {\n    defaultValue,\n    ns: namespaces\n  });\n  const translation = key ? t(key, combinedTOpts) : defaultValue;\n  if (components) {\n    Object.keys(components).forEach(c => {\n      const comp = components[c];\n      if (typeof comp.type === 'function' || !comp.props || !comp.props.children || translation.indexOf(\"\".concat(c, \"/>\")) < 0 && translation.indexOf(\"\".concat(c, \" />\")) < 0) return;\n      function Componentized() {\n        return React.createElement(React.Fragment, null, comp);\n      }\n      components[c] = React.createElement(Componentized, null);\n    });\n  }\n  const content = renderNodes(components || children, translation, i18n, reactI18nextOptions, combinedTOpts, shouldUnescape);\n  const useAsParent = parent !== undefined ? parent : reactI18nextOptions.defaultTransParent;\n  return useAsParent ? createElement(useAsParent, additionalProps, content) : content;\n}", "map": {"version": 3, "names": ["_extends", "React", "isValidElement", "cloneElement", "createElement", "Children", "HTML", "warn", "warnOnce", "getDefaults", "getI18n", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "node", "checkLength", "base", "props", "children", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "i18nIsDynamicList", "getAsArray", "hasValidReactChildren", "Object", "prototype", "toString", "call", "every", "child", "data", "Array", "isArray", "mergeProps", "source", "target", "newTarget", "_objectSpread", "assign", "nodesToString", "i18nOptions", "stringNode", "childrenA<PERSON>y", "keepArray", "transSupportBasicHtmlNodes", "transKeepBasicHtmlNodesFor", "for<PERSON>ach", "childIndex", "concat", "childPropsCount", "keys", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "indexOf", "type", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "content", "format", "clone", "_objectWithoutProperties", "_excluded", "value", "renderNodes", "targetString", "i18n", "combinedTOpts", "shouldUnescape", "emptyChildrenButNeedsHandling", "RegExp", "map", "keep", "join", "test", "getData", "childs", "ast", "parse", "opts", "renderInner", "rootReactNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mapAST", "pushTranslatedJSX", "inner", "mem", "i", "isVoid", "dummy", "push", "key", "undefined", "c", "ref", "reactNode", "astNode", "reactNodes", "astNodes", "reduce", "translationContent", "services", "interpolator", "interpolate", "language", "tmp", "parseInt", "name", "attrs", "isElement", "isValidTranslationWithChildren", "voidElement", "isEmptyTransWithHTML", "isKnownComponent", "hasOwnProperty", "Number", "isNaN", "parseFloat", "wrapTextNodes", "transWrapTextNodes", "unescape", "result", "Trans", "_ref", "count", "parent", "i18nKey", "context", "tOptions", "values", "defaults", "components", "ns", "i18nFromProps", "t", "tFromProps", "additionalProps", "_excluded2", "bind", "k", "reactI18nextOptions", "options", "react", "namespaces", "defaultNS", "nodeAsString", "defaultValue", "transEmptyNodeValue", "hashTransKey", "interpolation", "defaultVariables", "interpolationOverride", "prefix", "suffix", "translation", "comp", "Componentized", "Fragment", "useAsParent", "defaultTransParent"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-i18next/dist/es/TransWithoutContext.js"], "sourcesContent": ["import _extends from \"@babel/runtime/helpers/extends\";\nimport React, { isValidElement, cloneElement, createElement, Children } from 'react';\nimport HTML from 'html-parse-stringify';\nimport { warn, warnOnce } from './utils.js';\nimport { getDefaults } from './defaults.js';\nimport { getI18n } from './i18nInstance.js';\nfunction hasChildren(node, checkLength) {\n  if (!node) return false;\n  const base = node.props ? node.props.children : node.children;\n  if (checkLength) return base.length > 0;\n  return !!base;\n}\nfunction getChildren(node) {\n  if (!node) return [];\n  const children = node.props ? node.props.children : node.children;\n  return node.props && node.props.i18nIsDynamicList ? getAsArray(children) : children;\n}\nfunction hasValidReactChildren(children) {\n  if (Object.prototype.toString.call(children) !== '[object Array]') return false;\n  return children.every(child => isValidElement(child));\n}\nfunction getAsArray(data) {\n  return Array.isArray(data) ? data : [data];\n}\nfunction mergeProps(source, target) {\n  const newTarget = {\n    ...target\n  };\n  newTarget.props = Object.assign(source.props, target.props);\n  return newTarget;\n}\nexport function nodesToString(children, i18nOptions) {\n  if (!children) return '';\n  let stringNode = '';\n  const childrenArray = getAsArray(children);\n  const keepArray = i18nOptions.transSupportBasicHtmlNodes && i18nOptions.transKeepBasicHtmlNodesFor ? i18nOptions.transKeepBasicHtmlNodesFor : [];\n  childrenArray.forEach((child, childIndex) => {\n    if (typeof child === 'string') {\n      stringNode += `${child}`;\n    } else if (isValidElement(child)) {\n      const childPropsCount = Object.keys(child.props).length;\n      const shouldKeepChild = keepArray.indexOf(child.type) > -1;\n      const childChildren = child.props.children;\n      if (!childChildren && shouldKeepChild && childPropsCount === 0) {\n        stringNode += `<${child.type}/>`;\n      } else if (!childChildren && (!shouldKeepChild || childPropsCount !== 0)) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n      } else if (child.props.i18nIsDynamicList) {\n        stringNode += `<${childIndex}></${childIndex}>`;\n      } else if (shouldKeepChild && childPropsCount === 1 && typeof childChildren === 'string') {\n        stringNode += `<${child.type}>${childChildren}</${child.type}>`;\n      } else {\n        const content = nodesToString(childChildren, i18nOptions);\n        stringNode += `<${childIndex}>${content}</${childIndex}>`;\n      }\n    } else if (child === null) {\n      warn(`Trans: the passed in value is invalid - seems you passed in a null child.`);\n    } else if (typeof child === 'object') {\n      const {\n        format,\n        ...clone\n      } = child;\n      const keys = Object.keys(clone);\n      if (keys.length === 1) {\n        const value = format ? `${keys[0]}, ${format}` : keys[0];\n        stringNode += `{{${value}}}`;\n      } else {\n        warn(`react-i18next: the passed in object contained more than one variable - the object should look like {{ value, format }} where format is optional.`, child);\n      }\n    } else {\n      warn(`Trans: the passed in value is invalid - seems you passed in a variable like {number} - please pass in variables for interpolation as full objects like {{number}}.`, child);\n    }\n  });\n  return stringNode;\n}\nfunction renderNodes(children, targetString, i18n, i18nOptions, combinedTOpts, shouldUnescape) {\n  if (targetString === '') return [];\n  const keepArray = i18nOptions.transKeepBasicHtmlNodesFor || [];\n  const emptyChildrenButNeedsHandling = targetString && new RegExp(keepArray.map(keep => `<${keep}`).join('|')).test(targetString);\n  if (!children && !emptyChildrenButNeedsHandling && !shouldUnescape) return [targetString];\n  const data = {};\n  function getData(childs) {\n    const childrenArray = getAsArray(childs);\n    childrenArray.forEach(child => {\n      if (typeof child === 'string') return;\n      if (hasChildren(child)) getData(getChildren(child));else if (typeof child === 'object' && !isValidElement(child)) Object.assign(data, child);\n    });\n  }\n  getData(children);\n  const ast = HTML.parse(`<0>${targetString}</0>`);\n  const opts = {\n    ...data,\n    ...combinedTOpts\n  };\n  function renderInner(child, node, rootReactNode) {\n    const childs = getChildren(child);\n    const mappedChildren = mapAST(childs, node.children, rootReactNode);\n    return hasValidReactChildren(childs) && mappedChildren.length === 0 || child.props && child.props.i18nIsDynamicList ? childs : mappedChildren;\n  }\n  function pushTranslatedJSX(child, inner, mem, i, isVoid) {\n    if (child.dummy) {\n      child.children = inner;\n      mem.push(cloneElement(child, {\n        key: i\n      }, isVoid ? undefined : inner));\n    } else {\n      mem.push(...Children.map([child], c => {\n        const props = {\n          ...c.props\n        };\n        delete props.i18nIsDynamicList;\n        return React.createElement(c.type, _extends({}, props, {\n          key: i,\n          ref: c.ref\n        }, isVoid ? {} : {\n          children: inner\n        }));\n      }));\n    }\n  }\n  function mapAST(reactNode, astNode, rootReactNode) {\n    const reactNodes = getAsArray(reactNode);\n    const astNodes = getAsArray(astNode);\n    return astNodes.reduce((mem, node, i) => {\n      const translationContent = node.children && node.children[0] && node.children[0].content && i18n.services.interpolator.interpolate(node.children[0].content, opts, i18n.language);\n      if (node.type === 'tag') {\n        let tmp = reactNodes[parseInt(node.name, 10)];\n        if (rootReactNode.length === 1 && !tmp) tmp = rootReactNode[0][node.name];\n        if (!tmp) tmp = {};\n        const child = Object.keys(node.attrs).length !== 0 ? mergeProps({\n          props: node.attrs\n        }, tmp) : tmp;\n        const isElement = isValidElement(child);\n        const isValidTranslationWithChildren = isElement && hasChildren(node, true) && !node.voidElement;\n        const isEmptyTransWithHTML = emptyChildrenButNeedsHandling && typeof child === 'object' && child.dummy && !isElement;\n        const isKnownComponent = typeof children === 'object' && children !== null && Object.hasOwnProperty.call(children, node.name);\n        if (typeof child === 'string') {\n          const value = i18n.services.interpolator.interpolate(child, opts, i18n.language);\n          mem.push(value);\n        } else if (hasChildren(child) || isValidTranslationWithChildren) {\n          const inner = renderInner(child, node, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (isEmptyTransWithHTML) {\n          const inner = mapAST(reactNodes, node.children, rootReactNode);\n          pushTranslatedJSX(child, inner, mem, i);\n        } else if (Number.isNaN(parseFloat(node.name))) {\n          if (isKnownComponent) {\n            const inner = renderInner(child, node, rootReactNode);\n            pushTranslatedJSX(child, inner, mem, i, node.voidElement);\n          } else if (i18nOptions.transSupportBasicHtmlNodes && keepArray.indexOf(node.name) > -1) {\n            if (node.voidElement) {\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }));\n            } else {\n              const inner = mapAST(reactNodes, node.children, rootReactNode);\n              mem.push(createElement(node.name, {\n                key: `${node.name}-${i}`\n              }, inner));\n            }\n          } else if (node.voidElement) {\n            mem.push(`<${node.name} />`);\n          } else {\n            const inner = mapAST(reactNodes, node.children, rootReactNode);\n            mem.push(`<${node.name}>${inner}</${node.name}>`);\n          }\n        } else if (typeof child === 'object' && !isElement) {\n          const content = node.children[0] ? translationContent : null;\n          if (content) mem.push(content);\n        } else {\n          pushTranslatedJSX(child, translationContent, mem, i, node.children.length !== 1 || !translationContent);\n        }\n      } else if (node.type === 'text') {\n        const wrapTextNodes = i18nOptions.transWrapTextNodes;\n        const content = shouldUnescape ? i18nOptions.unescape(i18n.services.interpolator.interpolate(node.content, opts, i18n.language)) : i18n.services.interpolator.interpolate(node.content, opts, i18n.language);\n        if (wrapTextNodes) {\n          mem.push(createElement(wrapTextNodes, {\n            key: `${node.name}-${i}`\n          }, content));\n        } else {\n          mem.push(content);\n        }\n      }\n      return mem;\n    }, []);\n  }\n  const result = mapAST([{\n    dummy: true,\n    children: children || []\n  }], ast, getAsArray(children || []));\n  return getChildren(result[0]);\n}\nexport function Trans(_ref) {\n  let {\n    children,\n    count,\n    parent,\n    i18nKey,\n    context,\n    tOptions = {},\n    values,\n    defaults,\n    components,\n    ns,\n    i18n: i18nFromProps,\n    t: tFromProps,\n    shouldUnescape,\n    ...additionalProps\n  } = _ref;\n  const i18n = i18nFromProps || getI18n();\n  if (!i18n) {\n    warnOnce('You will need to pass in an i18next instance by using i18nextReactModule');\n    return children;\n  }\n  const t = tFromProps || i18n.t.bind(i18n) || (k => k);\n  if (context) tOptions.context = context;\n  const reactI18nextOptions = {\n    ...getDefaults(),\n    ...(i18n.options && i18n.options.react)\n  };\n  let namespaces = ns || t.ns || i18n.options && i18n.options.defaultNS;\n  namespaces = typeof namespaces === 'string' ? [namespaces] : namespaces || ['translation'];\n  const nodeAsString = nodesToString(children, reactI18nextOptions);\n  const defaultValue = defaults || nodeAsString || reactI18nextOptions.transEmptyNodeValue || i18nKey;\n  const {\n    hashTransKey\n  } = reactI18nextOptions;\n  const key = i18nKey || (hashTransKey ? hashTransKey(nodeAsString || defaultValue) : nodeAsString || defaultValue);\n  if (i18n.options && i18n.options.interpolation && i18n.options.interpolation.defaultVariables) {\n    values = values && Object.keys(values).length > 0 ? {\n      ...values,\n      ...i18n.options.interpolation.defaultVariables\n    } : {\n      ...i18n.options.interpolation.defaultVariables\n    };\n  }\n  const interpolationOverride = values ? tOptions.interpolation : {\n    interpolation: {\n      ...tOptions.interpolation,\n      prefix: '#$?',\n      suffix: '?$#'\n    }\n  };\n  const combinedTOpts = {\n    ...tOptions,\n    count,\n    ...values,\n    ...interpolationOverride,\n    defaultValue,\n    ns: namespaces\n  };\n  const translation = key ? t(key, combinedTOpts) : defaultValue;\n  if (components) {\n    Object.keys(components).forEach(c => {\n      const comp = components[c];\n      if (typeof comp.type === 'function' || !comp.props || !comp.props.children || translation.indexOf(`${c}/>`) < 0 && translation.indexOf(`${c} />`) < 0) return;\n      function Componentized() {\n        return React.createElement(React.Fragment, null, comp);\n      }\n      components[c] = React.createElement(Componentized, null);\n    });\n  }\n  const content = renderNodes(components || children, translation, i18n, reactI18nextOptions, combinedTOpts, shouldUnescape);\n  const useAsParent = parent !== undefined ? parent : reactI18nextOptions.defaultTransParent;\n  return useAsParent ? createElement(useAsParent, additionalProps, content) : content;\n}"], "mappings": ";;;;AAAA,OAAOA,QAAQ,MAAM,gCAAgC;AACrD,OAAOC,KAAK,IAAIC,cAAc,EAAEC,YAAY,EAAEC,aAAa,EAAEC,QAAQ,QAAQ,OAAO;AACpF,OAAOC,IAAI,MAAM,sBAAsB;AACvC,SAASC,IAAI,EAAEC,QAAQ,QAAQ,YAAY;AAC3C,SAASC,WAAW,QAAQ,eAAe;AAC3C,SAASC,OAAO,QAAQ,mBAAmB;AAC3C,SAASC,WAAWA,CAACC,IAAI,EAAEC,WAAW,EAAE;EACtC,IAAI,CAACD,IAAI,EAAE,OAAO,KAAK;EACvB,MAAME,IAAI,GAAGF,IAAI,CAACG,KAAK,GAAGH,IAAI,CAACG,KAAK,CAACC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;EAC7D,IAAIH,WAAW,EAAE,OAAOC,IAAI,CAACG,MAAM,GAAG,CAAC;EACvC,OAAO,CAAC,CAACH,IAAI;AACf;AACA,SAASI,WAAWA,CAACN,IAAI,EAAE;EACzB,IAAI,CAACA,IAAI,EAAE,OAAO,EAAE;EACpB,MAAMI,QAAQ,GAAGJ,IAAI,CAACG,KAAK,GAAGH,IAAI,CAACG,KAAK,CAACC,QAAQ,GAAGJ,IAAI,CAACI,QAAQ;EACjE,OAAOJ,IAAI,CAACG,KAAK,IAAIH,IAAI,CAACG,KAAK,CAACI,iBAAiB,GAAGC,UAAU,CAACJ,QAAQ,CAAC,GAAGA,QAAQ;AACrF;AACA,SAASK,qBAAqBA,CAACL,QAAQ,EAAE;EACvC,IAAIM,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACT,QAAQ,CAAC,KAAK,gBAAgB,EAAE,OAAO,KAAK;EAC/E,OAAOA,QAAQ,CAACU,KAAK,CAACC,KAAK,IAAIzB,cAAc,CAACyB,KAAK,CAAC,CAAC;AACvD;AACA,SAASP,UAAUA,CAACQ,IAAI,EAAE;EACxB,OAAOC,KAAK,CAACC,OAAO,CAACF,IAAI,CAAC,GAAGA,IAAI,GAAG,CAACA,IAAI,CAAC;AAC5C;AACA,SAASG,UAAUA,CAACC,MAAM,EAAEC,MAAM,EAAE;EAClC,MAAMC,SAAS,GAAAC,aAAA,KACVF,MAAM,CACV;EACDC,SAAS,CAACnB,KAAK,GAAGO,MAAM,CAACc,MAAM,CAACJ,MAAM,CAACjB,KAAK,EAAEkB,MAAM,CAAClB,KAAK,CAAC;EAC3D,OAAOmB,SAAS;AAClB;AACA,OAAO,SAASG,aAAaA,CAACrB,QAAQ,EAAEsB,WAAW,EAAE;EACnD,IAAI,CAACtB,QAAQ,EAAE,OAAO,EAAE;EACxB,IAAIuB,UAAU,GAAG,EAAE;EACnB,MAAMC,aAAa,GAAGpB,UAAU,CAACJ,QAAQ,CAAC;EAC1C,MAAMyB,SAAS,GAAGH,WAAW,CAACI,0BAA0B,IAAIJ,WAAW,CAACK,0BAA0B,GAAGL,WAAW,CAACK,0BAA0B,GAAG,EAAE;EAChJH,aAAa,CAACI,OAAO,CAAC,CAACjB,KAAK,EAAEkB,UAAU,KAAK;IAC3C,IAAI,OAAOlB,KAAK,KAAK,QAAQ,EAAE;MAC7BY,UAAU,OAAAO,MAAA,CAAOnB,KAAK,CAAE;IAC1B,CAAC,MAAM,IAAIzB,cAAc,CAACyB,KAAK,CAAC,EAAE;MAChC,MAAMoB,eAAe,GAAGzB,MAAM,CAAC0B,IAAI,CAACrB,KAAK,CAACZ,KAAK,CAAC,CAACE,MAAM;MACvD,MAAMgC,eAAe,GAAGR,SAAS,CAACS,OAAO,CAACvB,KAAK,CAACwB,IAAI,CAAC,GAAG,CAAC,CAAC;MAC1D,MAAMC,aAAa,GAAGzB,KAAK,CAACZ,KAAK,CAACC,QAAQ;MAC1C,IAAI,CAACoC,aAAa,IAAIH,eAAe,IAAIF,eAAe,KAAK,CAAC,EAAE;QAC9DR,UAAU,QAAAO,MAAA,CAAQnB,KAAK,CAACwB,IAAI,OAAI;MAClC,CAAC,MAAM,IAAI,CAACC,aAAa,KAAK,CAACH,eAAe,IAAIF,eAAe,KAAK,CAAC,CAAC,EAAE;QACxER,UAAU,QAAAO,MAAA,CAAQD,UAAU,SAAAC,MAAA,CAAMD,UAAU,MAAG;MACjD,CAAC,MAAM,IAAIlB,KAAK,CAACZ,KAAK,CAACI,iBAAiB,EAAE;QACxCoB,UAAU,QAAAO,MAAA,CAAQD,UAAU,SAAAC,MAAA,CAAMD,UAAU,MAAG;MACjD,CAAC,MAAM,IAAII,eAAe,IAAIF,eAAe,KAAK,CAAC,IAAI,OAAOK,aAAa,KAAK,QAAQ,EAAE;QACxFb,UAAU,QAAAO,MAAA,CAAQnB,KAAK,CAACwB,IAAI,OAAAL,MAAA,CAAIM,aAAa,QAAAN,MAAA,CAAKnB,KAAK,CAACwB,IAAI,MAAG;MACjE,CAAC,MAAM;QACL,MAAME,OAAO,GAAGhB,aAAa,CAACe,aAAa,EAAEd,WAAW,CAAC;QACzDC,UAAU,QAAAO,MAAA,CAAQD,UAAU,OAAAC,MAAA,CAAIO,OAAO,QAAAP,MAAA,CAAKD,UAAU,MAAG;MAC3D;IACF,CAAC,MAAM,IAAIlB,KAAK,KAAK,IAAI,EAAE;MACzBpB,IAAI,4EAA4E,CAAC;IACnF,CAAC,MAAM,IAAI,OAAOoB,KAAK,KAAK,QAAQ,EAAE;MACpC,MAAM;UACJ2B;QAEF,CAAC,GAAG3B,KAAK;QADJ4B,KAAK,GAAAC,wBAAA,CACN7B,KAAK,EAAA8B,SAAA;MACT,MAAMT,IAAI,GAAG1B,MAAM,CAAC0B,IAAI,CAACO,KAAK,CAAC;MAC/B,IAAIP,IAAI,CAAC/B,MAAM,KAAK,CAAC,EAAE;QACrB,MAAMyC,KAAK,GAAGJ,MAAM,MAAAR,MAAA,CAAME,IAAI,CAAC,CAAC,CAAC,QAAAF,MAAA,CAAKQ,MAAM,IAAKN,IAAI,CAAC,CAAC,CAAC;QACxDT,UAAU,SAAAO,MAAA,CAASY,KAAK,OAAI;MAC9B,CAAC,MAAM;QACLnD,IAAI,qJAAqJoB,KAAK,CAAC;MACjK;IACF,CAAC,MAAM;MACLpB,IAAI,uKAAuKoB,KAAK,CAAC;IACnL;EACF,CAAC,CAAC;EACF,OAAOY,UAAU;AACnB;AACA,SAASoB,WAAWA,CAAC3C,QAAQ,EAAE4C,YAAY,EAAEC,IAAI,EAAEvB,WAAW,EAAEwB,aAAa,EAAEC,cAAc,EAAE;EAC7F,IAAIH,YAAY,KAAK,EAAE,EAAE,OAAO,EAAE;EAClC,MAAMnB,SAAS,GAAGH,WAAW,CAACK,0BAA0B,IAAI,EAAE;EAC9D,MAAMqB,6BAA6B,GAAGJ,YAAY,IAAI,IAAIK,MAAM,CAACxB,SAAS,CAACyB,GAAG,CAACC,IAAI,QAAArB,MAAA,CAAQqB,IAAI,CAAE,CAAC,CAACC,IAAI,CAAC,GAAG,CAAC,CAAC,CAACC,IAAI,CAACT,YAAY,CAAC;EAChI,IAAI,CAAC5C,QAAQ,IAAI,CAACgD,6BAA6B,IAAI,CAACD,cAAc,EAAE,OAAO,CAACH,YAAY,CAAC;EACzF,MAAMhC,IAAI,GAAG,CAAC,CAAC;EACf,SAAS0C,OAAOA,CAACC,MAAM,EAAE;IACvB,MAAM/B,aAAa,GAAGpB,UAAU,CAACmD,MAAM,CAAC;IACxC/B,aAAa,CAACI,OAAO,CAACjB,KAAK,IAAI;MAC7B,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;MAC/B,IAAIhB,WAAW,CAACgB,KAAK,CAAC,EAAE2C,OAAO,CAACpD,WAAW,CAACS,KAAK,CAAC,CAAC,CAAC,KAAK,IAAI,OAAOA,KAAK,KAAK,QAAQ,IAAI,CAACzB,cAAc,CAACyB,KAAK,CAAC,EAAEL,MAAM,CAACc,MAAM,CAACR,IAAI,EAAED,KAAK,CAAC;IAC9I,CAAC,CAAC;EACJ;EACA2C,OAAO,CAACtD,QAAQ,CAAC;EACjB,MAAMwD,GAAG,GAAGlE,IAAI,CAACmE,KAAK,OAAA3B,MAAA,CAAOc,YAAY,SAAM,CAAC;EAChD,MAAMc,IAAI,GAAAvC,aAAA,CAAAA,aAAA,KACLP,IAAI,GACJkC,aAAa,CACjB;EACD,SAASa,WAAWA,CAAChD,KAAK,EAAEf,IAAI,EAAEgE,aAAa,EAAE;IAC/C,MAAML,MAAM,GAAGrD,WAAW,CAACS,KAAK,CAAC;IACjC,MAAMkD,cAAc,GAAGC,MAAM,CAACP,MAAM,EAAE3D,IAAI,CAACI,QAAQ,EAAE4D,aAAa,CAAC;IACnE,OAAOvD,qBAAqB,CAACkD,MAAM,CAAC,IAAIM,cAAc,CAAC5D,MAAM,KAAK,CAAC,IAAIU,KAAK,CAACZ,KAAK,IAAIY,KAAK,CAACZ,KAAK,CAACI,iBAAiB,GAAGoD,MAAM,GAAGM,cAAc;EAC/I;EACA,SAASE,iBAAiBA,CAACpD,KAAK,EAAEqD,KAAK,EAAEC,GAAG,EAAEC,CAAC,EAAEC,MAAM,EAAE;IACvD,IAAIxD,KAAK,CAACyD,KAAK,EAAE;MACfzD,KAAK,CAACX,QAAQ,GAAGgE,KAAK;MACtBC,GAAG,CAACI,IAAI,CAAClF,YAAY,CAACwB,KAAK,EAAE;QAC3B2D,GAAG,EAAEJ;MACP,CAAC,EAAEC,MAAM,GAAGI,SAAS,GAAGP,KAAK,CAAC,CAAC;IACjC,CAAC,MAAM;MACLC,GAAG,CAACI,IAAI,CAAC,GAAGhF,QAAQ,CAAC6D,GAAG,CAAC,CAACvC,KAAK,CAAC,EAAE6D,CAAC,IAAI;QACrC,MAAMzE,KAAK,GAAAoB,aAAA,KACNqD,CAAC,CAACzE,KAAK,CACX;QACD,OAAOA,KAAK,CAACI,iBAAiB;QAC9B,OAAOlB,KAAK,CAACG,aAAa,CAACoF,CAAC,CAACrC,IAAI,EAAEnD,QAAQ,CAAC,CAAC,CAAC,EAAEe,KAAK,EAAE;UACrDuE,GAAG,EAAEJ,CAAC;UACNO,GAAG,EAAED,CAAC,CAACC;QACT,CAAC,EAAEN,MAAM,GAAG,CAAC,CAAC,GAAG;UACfnE,QAAQ,EAAEgE;QACZ,CAAC,CAAC,CAAC;MACL,CAAC,CAAC,CAAC;IACL;EACF;EACA,SAASF,MAAMA,CAACY,SAAS,EAAEC,OAAO,EAAEf,aAAa,EAAE;IACjD,MAAMgB,UAAU,GAAGxE,UAAU,CAACsE,SAAS,CAAC;IACxC,MAAMG,QAAQ,GAAGzE,UAAU,CAACuE,OAAO,CAAC;IACpC,OAAOE,QAAQ,CAACC,MAAM,CAAC,CAACb,GAAG,EAAErE,IAAI,EAAEsE,CAAC,KAAK;MACvC,MAAMa,kBAAkB,GAAGnF,IAAI,CAACI,QAAQ,IAAIJ,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,IAAIJ,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,CAACqC,OAAO,IAAIQ,IAAI,CAACmC,QAAQ,CAACC,YAAY,CAACC,WAAW,CAACtF,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,CAACqC,OAAO,EAAEqB,IAAI,EAAEb,IAAI,CAACsC,QAAQ,CAAC;MACjL,IAAIvF,IAAI,CAACuC,IAAI,KAAK,KAAK,EAAE;QACvB,IAAIiD,GAAG,GAAGR,UAAU,CAACS,QAAQ,CAACzF,IAAI,CAAC0F,IAAI,EAAE,EAAE,CAAC,CAAC;QAC7C,IAAI1B,aAAa,CAAC3D,MAAM,KAAK,CAAC,IAAI,CAACmF,GAAG,EAAEA,GAAG,GAAGxB,aAAa,CAAC,CAAC,CAAC,CAAChE,IAAI,CAAC0F,IAAI,CAAC;QACzE,IAAI,CAACF,GAAG,EAAEA,GAAG,GAAG,CAAC,CAAC;QAClB,MAAMzE,KAAK,GAAGL,MAAM,CAAC0B,IAAI,CAACpC,IAAI,CAAC2F,KAAK,CAAC,CAACtF,MAAM,KAAK,CAAC,GAAGc,UAAU,CAAC;UAC9DhB,KAAK,EAAEH,IAAI,CAAC2F;QACd,CAAC,EAAEH,GAAG,CAAC,GAAGA,GAAG;QACb,MAAMI,SAAS,GAAGtG,cAAc,CAACyB,KAAK,CAAC;QACvC,MAAM8E,8BAA8B,GAAGD,SAAS,IAAI7F,WAAW,CAACC,IAAI,EAAE,IAAI,CAAC,IAAI,CAACA,IAAI,CAAC8F,WAAW;QAChG,MAAMC,oBAAoB,GAAG3C,6BAA6B,IAAI,OAAOrC,KAAK,KAAK,QAAQ,IAAIA,KAAK,CAACyD,KAAK,IAAI,CAACoB,SAAS;QACpH,MAAMI,gBAAgB,GAAG,OAAO5F,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,KAAK,IAAI,IAAIM,MAAM,CAACuF,cAAc,CAACpF,IAAI,CAACT,QAAQ,EAAEJ,IAAI,CAAC0F,IAAI,CAAC;QAC7H,IAAI,OAAO3E,KAAK,KAAK,QAAQ,EAAE;UAC7B,MAAM+B,KAAK,GAAGG,IAAI,CAACmC,QAAQ,CAACC,YAAY,CAACC,WAAW,CAACvE,KAAK,EAAE+C,IAAI,EAAEb,IAAI,CAACsC,QAAQ,CAAC;UAChFlB,GAAG,CAACI,IAAI,CAAC3B,KAAK,CAAC;QACjB,CAAC,MAAM,IAAI/C,WAAW,CAACgB,KAAK,CAAC,IAAI8E,8BAA8B,EAAE;UAC/D,MAAMzB,KAAK,GAAGL,WAAW,CAAChD,KAAK,EAAEf,IAAI,EAAEgE,aAAa,CAAC;UACrDG,iBAAiB,CAACpD,KAAK,EAAEqD,KAAK,EAAEC,GAAG,EAAEC,CAAC,CAAC;QACzC,CAAC,MAAM,IAAIyB,oBAAoB,EAAE;UAC/B,MAAM3B,KAAK,GAAGF,MAAM,CAACc,UAAU,EAAEhF,IAAI,CAACI,QAAQ,EAAE4D,aAAa,CAAC;UAC9DG,iBAAiB,CAACpD,KAAK,EAAEqD,KAAK,EAAEC,GAAG,EAAEC,CAAC,CAAC;QACzC,CAAC,MAAM,IAAI4B,MAAM,CAACC,KAAK,CAACC,UAAU,CAACpG,IAAI,CAAC0F,IAAI,CAAC,CAAC,EAAE;UAC9C,IAAIM,gBAAgB,EAAE;YACpB,MAAM5B,KAAK,GAAGL,WAAW,CAAChD,KAAK,EAAEf,IAAI,EAAEgE,aAAa,CAAC;YACrDG,iBAAiB,CAACpD,KAAK,EAAEqD,KAAK,EAAEC,GAAG,EAAEC,CAAC,EAAEtE,IAAI,CAAC8F,WAAW,CAAC;UAC3D,CAAC,MAAM,IAAIpE,WAAW,CAACI,0BAA0B,IAAID,SAAS,CAACS,OAAO,CAACtC,IAAI,CAAC0F,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;YACtF,IAAI1F,IAAI,CAAC8F,WAAW,EAAE;cACpBzB,GAAG,CAACI,IAAI,CAACjF,aAAa,CAACQ,IAAI,CAAC0F,IAAI,EAAE;gBAChChB,GAAG,KAAAxC,MAAA,CAAKlC,IAAI,CAAC0F,IAAI,OAAAxD,MAAA,CAAIoC,CAAC;cACxB,CAAC,CAAC,CAAC;YACL,CAAC,MAAM;cACL,MAAMF,KAAK,GAAGF,MAAM,CAACc,UAAU,EAAEhF,IAAI,CAACI,QAAQ,EAAE4D,aAAa,CAAC;cAC9DK,GAAG,CAACI,IAAI,CAACjF,aAAa,CAACQ,IAAI,CAAC0F,IAAI,EAAE;gBAChChB,GAAG,KAAAxC,MAAA,CAAKlC,IAAI,CAAC0F,IAAI,OAAAxD,MAAA,CAAIoC,CAAC;cACxB,CAAC,EAAEF,KAAK,CAAC,CAAC;YACZ;UACF,CAAC,MAAM,IAAIpE,IAAI,CAAC8F,WAAW,EAAE;YAC3BzB,GAAG,CAACI,IAAI,KAAAvC,MAAA,CAAKlC,IAAI,CAAC0F,IAAI,QAAK,CAAC;UAC9B,CAAC,MAAM;YACL,MAAMtB,KAAK,GAAGF,MAAM,CAACc,UAAU,EAAEhF,IAAI,CAACI,QAAQ,EAAE4D,aAAa,CAAC;YAC9DK,GAAG,CAACI,IAAI,KAAAvC,MAAA,CAAKlC,IAAI,CAAC0F,IAAI,OAAAxD,MAAA,CAAIkC,KAAK,QAAAlC,MAAA,CAAKlC,IAAI,CAAC0F,IAAI,MAAG,CAAC;UACnD;QACF,CAAC,MAAM,IAAI,OAAO3E,KAAK,KAAK,QAAQ,IAAI,CAAC6E,SAAS,EAAE;UAClD,MAAMnD,OAAO,GAAGzC,IAAI,CAACI,QAAQ,CAAC,CAAC,CAAC,GAAG+E,kBAAkB,GAAG,IAAI;UAC5D,IAAI1C,OAAO,EAAE4B,GAAG,CAACI,IAAI,CAAChC,OAAO,CAAC;QAChC,CAAC,MAAM;UACL0B,iBAAiB,CAACpD,KAAK,EAAEoE,kBAAkB,EAAEd,GAAG,EAAEC,CAAC,EAAEtE,IAAI,CAACI,QAAQ,CAACC,MAAM,KAAK,CAAC,IAAI,CAAC8E,kBAAkB,CAAC;QACzG;MACF,CAAC,MAAM,IAAInF,IAAI,CAACuC,IAAI,KAAK,MAAM,EAAE;QAC/B,MAAM8D,aAAa,GAAG3E,WAAW,CAAC4E,kBAAkB;QACpD,MAAM7D,OAAO,GAAGU,cAAc,GAAGzB,WAAW,CAAC6E,QAAQ,CAACtD,IAAI,CAACmC,QAAQ,CAACC,YAAY,CAACC,WAAW,CAACtF,IAAI,CAACyC,OAAO,EAAEqB,IAAI,EAAEb,IAAI,CAACsC,QAAQ,CAAC,CAAC,GAAGtC,IAAI,CAACmC,QAAQ,CAACC,YAAY,CAACC,WAAW,CAACtF,IAAI,CAACyC,OAAO,EAAEqB,IAAI,EAAEb,IAAI,CAACsC,QAAQ,CAAC;QAC5M,IAAIc,aAAa,EAAE;UACjBhC,GAAG,CAACI,IAAI,CAACjF,aAAa,CAAC6G,aAAa,EAAE;YACpC3B,GAAG,KAAAxC,MAAA,CAAKlC,IAAI,CAAC0F,IAAI,OAAAxD,MAAA,CAAIoC,CAAC;UACxB,CAAC,EAAE7B,OAAO,CAAC,CAAC;QACd,CAAC,MAAM;UACL4B,GAAG,CAACI,IAAI,CAAChC,OAAO,CAAC;QACnB;MACF;MACA,OAAO4B,GAAG;IACZ,CAAC,EAAE,EAAE,CAAC;EACR;EACA,MAAMmC,MAAM,GAAGtC,MAAM,CAAC,CAAC;IACrBM,KAAK,EAAE,IAAI;IACXpE,QAAQ,EAAEA,QAAQ,IAAI;EACxB,CAAC,CAAC,EAAEwD,GAAG,EAAEpD,UAAU,CAACJ,QAAQ,IAAI,EAAE,CAAC,CAAC;EACpC,OAAOE,WAAW,CAACkG,MAAM,CAAC,CAAC,CAAC,CAAC;AAC/B;AACA,OAAO,SAASC,KAAKA,CAACC,IAAI,EAAE;EAC1B,IAAI;MACFtG,QAAQ;MACRuG,KAAK;MACLC,MAAM;MACNC,OAAO;MACPC,OAAO;MACPC,QAAQ,GAAG,CAAC,CAAC;MACbC,MAAM;MACNC,QAAQ;MACRC,UAAU;MACVC,EAAE;MACFlE,IAAI,EAAEmE,aAAa;MACnBC,CAAC,EAAEC,UAAU;MACbnE;IAEF,CAAC,GAAGuD,IAAI;IADHa,eAAe,GAAA3E,wBAAA,CAChB8D,IAAI,EAAAc,UAAA;EACR,MAAMvE,IAAI,GAAGmE,aAAa,IAAItH,OAAO,CAAC,CAAC;EACvC,IAAI,CAACmD,IAAI,EAAE;IACTrD,QAAQ,CAAC,0EAA0E,CAAC;IACpF,OAAOQ,QAAQ;EACjB;EACA,MAAMiH,CAAC,GAAGC,UAAU,IAAIrE,IAAI,CAACoE,CAAC,CAACI,IAAI,CAACxE,IAAI,CAAC,KAAKyE,CAAC,IAAIA,CAAC,CAAC;EACrD,IAAIZ,OAAO,EAAEC,QAAQ,CAACD,OAAO,GAAGA,OAAO;EACvC,MAAMa,mBAAmB,GAAApG,aAAA,CAAAA,aAAA,KACpB1B,WAAW,CAAC,CAAC,GACZoD,IAAI,CAAC2E,OAAO,IAAI3E,IAAI,CAAC2E,OAAO,CAACC,KAAK,CACvC;EACD,IAAIC,UAAU,GAAGX,EAAE,IAAIE,CAAC,CAACF,EAAE,IAAIlE,IAAI,CAAC2E,OAAO,IAAI3E,IAAI,CAAC2E,OAAO,CAACG,SAAS;EACrED,UAAU,GAAG,OAAOA,UAAU,KAAK,QAAQ,GAAG,CAACA,UAAU,CAAC,GAAGA,UAAU,IAAI,CAAC,aAAa,CAAC;EAC1F,MAAME,YAAY,GAAGvG,aAAa,CAACrB,QAAQ,EAAEuH,mBAAmB,CAAC;EACjE,MAAMM,YAAY,GAAGhB,QAAQ,IAAIe,YAAY,IAAIL,mBAAmB,CAACO,mBAAmB,IAAIrB,OAAO;EACnG,MAAM;IACJsB;EACF,CAAC,GAAGR,mBAAmB;EACvB,MAAMjD,GAAG,GAAGmC,OAAO,KAAKsB,YAAY,GAAGA,YAAY,CAACH,YAAY,IAAIC,YAAY,CAAC,GAAGD,YAAY,IAAIC,YAAY,CAAC;EACjH,IAAIhF,IAAI,CAAC2E,OAAO,IAAI3E,IAAI,CAAC2E,OAAO,CAACQ,aAAa,IAAInF,IAAI,CAAC2E,OAAO,CAACQ,aAAa,CAACC,gBAAgB,EAAE;IAC7FrB,MAAM,GAAGA,MAAM,IAAItG,MAAM,CAAC0B,IAAI,CAAC4E,MAAM,CAAC,CAAC3G,MAAM,GAAG,CAAC,GAAAkB,aAAA,CAAAA,aAAA,KAC5CyF,MAAM,GACN/D,IAAI,CAAC2E,OAAO,CAACQ,aAAa,CAACC,gBAAgB,IAAA9G,aAAA,KAE3C0B,IAAI,CAAC2E,OAAO,CAACQ,aAAa,CAACC,gBAAgB,CAC/C;EACH;EACA,MAAMC,qBAAqB,GAAGtB,MAAM,GAAGD,QAAQ,CAACqB,aAAa,GAAG;IAC9DA,aAAa,EAAA7G,aAAA,CAAAA,aAAA,KACRwF,QAAQ,CAACqB,aAAa;MACzBG,MAAM,EAAE,KAAK;MACbC,MAAM,EAAE;IAAK;EAEjB,CAAC;EACD,MAAMtF,aAAa,GAAA3B,aAAA,CAAAA,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACdwF,QAAQ;IACXJ;EAAK,GACFK,MAAM,GACNsB,qBAAqB;IACxBL,YAAY;IACZd,EAAE,EAAEW;EAAU,EACf;EACD,MAAMW,WAAW,GAAG/D,GAAG,GAAG2C,CAAC,CAAC3C,GAAG,EAAExB,aAAa,CAAC,GAAG+E,YAAY;EAC9D,IAAIf,UAAU,EAAE;IACdxG,MAAM,CAAC0B,IAAI,CAAC8E,UAAU,CAAC,CAAClF,OAAO,CAAC4C,CAAC,IAAI;MACnC,MAAM8D,IAAI,GAAGxB,UAAU,CAACtC,CAAC,CAAC;MAC1B,IAAI,OAAO8D,IAAI,CAACnG,IAAI,KAAK,UAAU,IAAI,CAACmG,IAAI,CAACvI,KAAK,IAAI,CAACuI,IAAI,CAACvI,KAAK,CAACC,QAAQ,IAAIqI,WAAW,CAACnG,OAAO,IAAAJ,MAAA,CAAI0C,CAAC,OAAI,CAAC,GAAG,CAAC,IAAI6D,WAAW,CAACnG,OAAO,IAAAJ,MAAA,CAAI0C,CAAC,QAAK,CAAC,GAAG,CAAC,EAAE;MACvJ,SAAS+D,aAAaA,CAAA,EAAG;QACvB,OAAOtJ,KAAK,CAACG,aAAa,CAACH,KAAK,CAACuJ,QAAQ,EAAE,IAAI,EAAEF,IAAI,CAAC;MACxD;MACAxB,UAAU,CAACtC,CAAC,CAAC,GAAGvF,KAAK,CAACG,aAAa,CAACmJ,aAAa,EAAE,IAAI,CAAC;IAC1D,CAAC,CAAC;EACJ;EACA,MAAMlG,OAAO,GAAGM,WAAW,CAACmE,UAAU,IAAI9G,QAAQ,EAAEqI,WAAW,EAAExF,IAAI,EAAE0E,mBAAmB,EAAEzE,aAAa,EAAEC,cAAc,CAAC;EAC1H,MAAM0F,WAAW,GAAGjC,MAAM,KAAKjC,SAAS,GAAGiC,MAAM,GAAGe,mBAAmB,CAACmB,kBAAkB;EAC1F,OAAOD,WAAW,GAAGrJ,aAAa,CAACqJ,WAAW,EAAEtB,eAAe,EAAE9E,OAAO,CAAC,GAAGA,OAAO;AACrF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}