{"ast": null, "code": "'use strict';\n\n/** Highest positive signed 32-bit float value */\nconst maxInt = 2147483647; // aka. 0x7FFFFFFF or 2^31-1\n\n/** Bootstring parameters */\nconst base = 36;\nconst tMin = 1;\nconst tMax = 26;\nconst skew = 38;\nconst damp = 700;\nconst initialBias = 72;\nconst initialN = 128; // 0x80\nconst delimiter = '-'; // '\\x2D'\n\n/** Regular expressions */\nconst regexPunycode = /^xn--/;\nconst regexNonASCII = /[^\\0-\\x7F]/; // Note: U+007F DEL is excluded too.\nconst regexSeparators = /[\\x2E\\u3002\\uFF0E\\uFF61]/g; // RFC 3490 separators\n\n/** Error messages */\nconst errors = {\n  'overflow': 'Overflow: input needs wider integers to process',\n  'not-basic': 'Illegal input >= 0x80 (not a basic code point)',\n  'invalid-input': 'Invalid input'\n};\n\n/** Convenience shortcuts */\nconst baseMinusTMin = base - tMin;\nconst floor = Math.floor;\nconst stringFromCharCode = String.fromCharCode;\n\n/*--------------------------------------------------------------------------*/\n\n/**\n * A generic error utility function.\n * @private\n * @param {String} type The error type.\n * @returns {Error} Throws a `RangeError` with the applicable error message.\n */\nfunction error(type) {\n  throw new RangeError(errors[type]);\n}\n\n/**\n * A generic `Array#map` utility function.\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} callback The function that gets called for every array\n * item.\n * @returns {Array} A new array of values returned by the callback function.\n */\nfunction map(array, callback) {\n  const result = [];\n  let length = array.length;\n  while (length--) {\n    result[length] = callback(array[length]);\n  }\n  return result;\n}\n\n/**\n * A simple `Array#map`-like wrapper to work with domain name strings or email\n * addresses.\n * @private\n * @param {String} domain The domain name or email address.\n * @param {Function} callback The function that gets called for every\n * character.\n * @returns {String} A new string of characters returned by the callback\n * function.\n */\nfunction mapDomain(domain, callback) {\n  const parts = domain.split('@');\n  let result = '';\n  if (parts.length > 1) {\n    // In email addresses, only the domain name should be punycoded. Leave\n    // the local part (i.e. everything up to `@`) intact.\n    result = parts[0] + '@';\n    domain = parts[1];\n  }\n  // Avoid `split(regex)` for IE8 compatibility. See #17.\n  domain = domain.replace(regexSeparators, '\\x2E');\n  const labels = domain.split('.');\n  const encoded = map(labels, callback).join('.');\n  return result + encoded;\n}\n\n/**\n * Creates an array containing the numeric code points of each Unicode\n * character in the string. While JavaScript uses UCS-2 internally,\n * this function will convert a pair of surrogate halves (each of which\n * UCS-2 exposes as separate characters) into a single code point,\n * matching UTF-16.\n * @see `punycode.ucs2.encode`\n * @see <https://mathiasbynens.be/notes/javascript-encoding>\n * @memberOf punycode.ucs2\n * @name decode\n * @param {String} string The Unicode input string (UCS-2).\n * @returns {Array} The new array of code points.\n */\nfunction ucs2decode(string) {\n  const output = [];\n  let counter = 0;\n  const length = string.length;\n  while (counter < length) {\n    const value = string.charCodeAt(counter++);\n    if (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n      // It's a high surrogate, and there is a next character.\n      const extra = string.charCodeAt(counter++);\n      if ((extra & 0xFC00) == 0xDC00) {\n        // Low surrogate.\n        output.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n      } else {\n        // It's an unmatched surrogate; only append this code unit, in case the\n        // next code unit is the high surrogate of a surrogate pair.\n        output.push(value);\n        counter--;\n      }\n    } else {\n      output.push(value);\n    }\n  }\n  return output;\n}\n\n/**\n * Creates a string based on an array of numeric code points.\n * @see `punycode.ucs2.decode`\n * @memberOf punycode.ucs2\n * @name encode\n * @param {Array} codePoints The array of numeric code points.\n * @returns {String} The new Unicode string (UCS-2).\n */\nconst ucs2encode = codePoints => String.fromCodePoint(...codePoints);\n\n/**\n * Converts a basic code point into a digit/integer.\n * @see `digitToBasic()`\n * @private\n * @param {Number} codePoint The basic numeric code point value.\n * @returns {Number} The numeric value of a basic code point (for use in\n * representing integers) in the range `0` to `base - 1`, or `base` if\n * the code point does not represent a value.\n */\nconst basicToDigit = function (codePoint) {\n  if (codePoint >= 0x30 && codePoint < 0x3A) {\n    return 26 + (codePoint - 0x30);\n  }\n  if (codePoint >= 0x41 && codePoint < 0x5B) {\n    return codePoint - 0x41;\n  }\n  if (codePoint >= 0x61 && codePoint < 0x7B) {\n    return codePoint - 0x61;\n  }\n  return base;\n};\n\n/**\n * Converts a digit/integer into a basic code point.\n * @see `basicToDigit()`\n * @private\n * @param {Number} digit The numeric value of a basic code point.\n * @returns {Number} The basic code point whose value (when used for\n * representing integers) is `digit`, which needs to be in the range\n * `0` to `base - 1`. If `flag` is non-zero, the uppercase form is\n * used; else, the lowercase form is used. The behavior is undefined\n * if `flag` is non-zero and `digit` has no uppercase form.\n */\nconst digitToBasic = function (digit, flag) {\n  //  0..25 map to ASCII a..z or A..Z\n  // 26..35 map to ASCII 0..9\n  return digit + 22 + 75 * (digit < 26) - ((flag != 0) << 5);\n};\n\n/**\n * Bias adaptation function as per section 3.4 of RFC 3492.\n * https://tools.ietf.org/html/rfc3492#section-3.4\n * @private\n */\nconst adapt = function (delta, numPoints, firstTime) {\n  let k = 0;\n  delta = firstTime ? floor(delta / damp) : delta >> 1;\n  delta += floor(delta / numPoints);\n  for /* no initialization */\n  (; delta > baseMinusTMin * tMax >> 1; k += base) {\n    delta = floor(delta / baseMinusTMin);\n  }\n  return floor(k + (baseMinusTMin + 1) * delta / (delta + skew));\n};\n\n/**\n * Converts a Punycode string of ASCII-only symbols to a string of Unicode\n * symbols.\n * @memberOf punycode\n * @param {String} input The Punycode string of ASCII-only symbols.\n * @returns {String} The resulting string of Unicode symbols.\n */\nconst decode = function (input) {\n  // Don't use UCS-2.\n  const output = [];\n  const inputLength = input.length;\n  let i = 0;\n  let n = initialN;\n  let bias = initialBias;\n\n  // Handle the basic code points: let `basic` be the number of input code\n  // points before the last delimiter, or `0` if there is none, then copy\n  // the first basic code points to the output.\n\n  let basic = input.lastIndexOf(delimiter);\n  if (basic < 0) {\n    basic = 0;\n  }\n  for (let j = 0; j < basic; ++j) {\n    // if it's not a basic code point\n    if (input.charCodeAt(j) >= 0x80) {\n      error('not-basic');\n    }\n    output.push(input.charCodeAt(j));\n  }\n\n  // Main decoding loop: start just after the last delimiter if any basic code\n  // points were copied; start at the beginning otherwise.\n\n  for /* no final expression */\n  (let index = basic > 0 ? basic + 1 : 0; index < inputLength;) {\n    // `index` is the index of the next character to be consumed.\n    // Decode a generalized variable-length integer into `delta`,\n    // which gets added to `i`. The overflow checking is easier\n    // if we increase `i` as we go, then subtract off its starting\n    // value at the end to obtain `delta`.\n    const oldi = i;\n    for /* no condition */\n    (let w = 1, k = base;; k += base) {\n      if (index >= inputLength) {\n        error('invalid-input');\n      }\n      const digit = basicToDigit(input.charCodeAt(index++));\n      if (digit >= base) {\n        error('invalid-input');\n      }\n      if (digit > floor((maxInt - i) / w)) {\n        error('overflow');\n      }\n      i += digit * w;\n      const t = k <= bias ? tMin : k >= bias + tMax ? tMax : k - bias;\n      if (digit < t) {\n        break;\n      }\n      const baseMinusT = base - t;\n      if (w > floor(maxInt / baseMinusT)) {\n        error('overflow');\n      }\n      w *= baseMinusT;\n    }\n    const out = output.length + 1;\n    bias = adapt(i - oldi, out, oldi == 0);\n\n    // `i` was supposed to wrap around from `out` to `0`,\n    // incrementing `n` each time, so we'll fix that now:\n    if (floor(i / out) > maxInt - n) {\n      error('overflow');\n    }\n    n += floor(i / out);\n    i %= out;\n\n    // Insert `n` at position `i` of the output.\n    output.splice(i++, 0, n);\n  }\n  return String.fromCodePoint(...output);\n};\n\n/**\n * Converts a string of Unicode symbols (e.g. a domain name label) to a\n * Punycode string of ASCII-only symbols.\n * @memberOf punycode\n * @param {String} input The string of Unicode symbols.\n * @returns {String} The resulting Punycode string of ASCII-only symbols.\n */\nconst encode = function (input) {\n  const output = [];\n\n  // Convert the input in UCS-2 to an array of Unicode code points.\n  input = ucs2decode(input);\n\n  // Cache the length.\n  const inputLength = input.length;\n\n  // Initialize the state.\n  let n = initialN;\n  let delta = 0;\n  let bias = initialBias;\n\n  // Handle the basic code points.\n  for (const currentValue of input) {\n    if (currentValue < 0x80) {\n      output.push(stringFromCharCode(currentValue));\n    }\n  }\n  const basicLength = output.length;\n  let handledCPCount = basicLength;\n\n  // `handledCPCount` is the number of code points that have been handled;\n  // `basicLength` is the number of basic code points.\n\n  // Finish the basic string with a delimiter unless it's empty.\n  if (basicLength) {\n    output.push(delimiter);\n  }\n\n  // Main encoding loop:\n  while (handledCPCount < inputLength) {\n    // All non-basic code points < n have been handled already. Find the next\n    // larger one:\n    let m = maxInt;\n    for (const currentValue of input) {\n      if (currentValue >= n && currentValue < m) {\n        m = currentValue;\n      }\n    }\n\n    // Increase `delta` enough to advance the decoder's <n,i> state to <m,0>,\n    // but guard against overflow.\n    const handledCPCountPlusOne = handledCPCount + 1;\n    if (m - n > floor((maxInt - delta) / handledCPCountPlusOne)) {\n      error('overflow');\n    }\n    delta += (m - n) * handledCPCountPlusOne;\n    n = m;\n    for (const currentValue of input) {\n      if (currentValue < n && ++delta > maxInt) {\n        error('overflow');\n      }\n      if (currentValue === n) {\n        // Represent delta as a generalized variable-length integer.\n        let q = delta;\n        for /* no condition */\n        (let k = base;; k += base) {\n          const t = k <= bias ? tMin : k >= bias + tMax ? tMax : k - bias;\n          if (q < t) {\n            break;\n          }\n          const qMinusT = q - t;\n          const baseMinusT = base - t;\n          output.push(stringFromCharCode(digitToBasic(t + qMinusT % baseMinusT, 0)));\n          q = floor(qMinusT / baseMinusT);\n        }\n        output.push(stringFromCharCode(digitToBasic(q, 0)));\n        bias = adapt(delta, handledCPCountPlusOne, handledCPCount === basicLength);\n        delta = 0;\n        ++handledCPCount;\n      }\n    }\n    ++delta;\n    ++n;\n  }\n  return output.join('');\n};\n\n/**\n * Converts a Punycode string representing a domain name or an email address\n * to Unicode. Only the Punycoded parts of the input will be converted, i.e.\n * it doesn't matter if you call it on a string that has already been\n * converted to Unicode.\n * @memberOf punycode\n * @param {String} input The Punycoded domain name or email address to\n * convert to Unicode.\n * @returns {String} The Unicode representation of the given Punycode\n * string.\n */\nconst toUnicode = function (input) {\n  return mapDomain(input, function (string) {\n    return regexPunycode.test(string) ? decode(string.slice(4).toLowerCase()) : string;\n  });\n};\n\n/**\n * Converts a Unicode string representing a domain name or an email address to\n * Punycode. Only the non-ASCII parts of the domain name will be converted,\n * i.e. it doesn't matter if you call it with a domain that's already in\n * ASCII.\n * @memberOf punycode\n * @param {String} input The domain name or email address to convert, as a\n * Unicode string.\n * @returns {String} The Punycode representation of the given domain name or\n * email address.\n */\nconst toASCII = function (input) {\n  return mapDomain(input, function (string) {\n    return regexNonASCII.test(string) ? 'xn--' + encode(string) : string;\n  });\n};\n\n/*--------------------------------------------------------------------------*/\n\n/** Define the public API */\nconst punycode = {\n  /**\n   * A string representing the current Punycode.js version number.\n   * @memberOf punycode\n   * @type String\n   */\n  'version': '2.3.1',\n  /**\n   * An object of methods to convert from JavaScript's internal character\n   * representation (UCS-2) to Unicode code points, and back.\n   * @see <https://mathiasbynens.be/notes/javascript-encoding>\n   * @memberOf punycode\n   * @type Object\n   */\n  'ucs2': {\n    'decode': ucs2decode,\n    'encode': ucs2encode\n  },\n  'decode': decode,\n  'encode': encode,\n  'toASCII': toASCII,\n  'toUnicode': toUnicode\n};\nexport { ucs2decode, ucs2encode, decode, encode, toASCII, toUnicode };\nexport default punycode;", "map": {"version": 3, "names": ["maxInt", "base", "tMin", "tMax", "skew", "damp", "initialBias", "initialN", "delimiter", "regexPunycode", "regexNonASCII", "regexSeparators", "errors", "baseMinusTMin", "floor", "Math", "stringFromCharCode", "String", "fromCharCode", "error", "type", "RangeError", "map", "array", "callback", "result", "length", "mapDomain", "domain", "parts", "split", "replace", "labels", "encoded", "join", "ucs2decode", "string", "output", "counter", "value", "charCodeAt", "extra", "push", "ucs2encode", "codePoints", "fromCodePoint", "basicToDigit", "codePoint", "digitToBasic", "digit", "flag", "adapt", "delta", "numPoints", "firstTime", "k", "decode", "input", "inputLength", "i", "n", "bias", "basic", "lastIndexOf", "j", "index", "oldi", "w", "t", "baseMinusT", "out", "splice", "encode", "currentValue", "basicLength", "handledCPCount", "m", "handledCPCountPlusOne", "q", "qMinusT", "toUnicode", "test", "slice", "toLowerCase", "toASCII", "punycode"], "sources": ["C:/Users/<USER>/node_modules/punycode/punycode.es6.js"], "sourcesContent": ["'use strict';\n\n/** Highest positive signed 32-bit float value */\nconst maxInt = 2147483647; // aka. 0x7FFFFFFF or 2^31-1\n\n/** Bootstring parameters */\nconst base = 36;\nconst tMin = 1;\nconst tMax = 26;\nconst skew = 38;\nconst damp = 700;\nconst initialBias = 72;\nconst initialN = 128; // 0x80\nconst delimiter = '-'; // '\\x2D'\n\n/** Regular expressions */\nconst regexPunycode = /^xn--/;\nconst regexNonASCII = /[^\\0-\\x7F]/; // Note: U+007F DEL is excluded too.\nconst regexSeparators = /[\\x2E\\u3002\\uFF0E\\uFF61]/g; // RFC 3490 separators\n\n/** Error messages */\nconst errors = {\n\t'overflow': 'Overflow: input needs wider integers to process',\n\t'not-basic': 'Illegal input >= 0x80 (not a basic code point)',\n\t'invalid-input': 'Invalid input'\n};\n\n/** Convenience shortcuts */\nconst baseMinusTMin = base - tMin;\nconst floor = Math.floor;\nconst stringFromCharCode = String.fromCharCode;\n\n/*--------------------------------------------------------------------------*/\n\n/**\n * A generic error utility function.\n * @private\n * @param {String} type The error type.\n * @returns {Error} Throws a `RangeError` with the applicable error message.\n */\nfunction error(type) {\n\tthrow new RangeError(errors[type]);\n}\n\n/**\n * A generic `Array#map` utility function.\n * @private\n * @param {Array} array The array to iterate over.\n * @param {Function} callback The function that gets called for every array\n * item.\n * @returns {Array} A new array of values returned by the callback function.\n */\nfunction map(array, callback) {\n\tconst result = [];\n\tlet length = array.length;\n\twhile (length--) {\n\t\tresult[length] = callback(array[length]);\n\t}\n\treturn result;\n}\n\n/**\n * A simple `Array#map`-like wrapper to work with domain name strings or email\n * addresses.\n * @private\n * @param {String} domain The domain name or email address.\n * @param {Function} callback The function that gets called for every\n * character.\n * @returns {String} A new string of characters returned by the callback\n * function.\n */\nfunction mapDomain(domain, callback) {\n\tconst parts = domain.split('@');\n\tlet result = '';\n\tif (parts.length > 1) {\n\t\t// In email addresses, only the domain name should be punycoded. Leave\n\t\t// the local part (i.e. everything up to `@`) intact.\n\t\tresult = parts[0] + '@';\n\t\tdomain = parts[1];\n\t}\n\t// Avoid `split(regex)` for IE8 compatibility. See #17.\n\tdomain = domain.replace(regexSeparators, '\\x2E');\n\tconst labels = domain.split('.');\n\tconst encoded = map(labels, callback).join('.');\n\treturn result + encoded;\n}\n\n/**\n * Creates an array containing the numeric code points of each Unicode\n * character in the string. While JavaScript uses UCS-2 internally,\n * this function will convert a pair of surrogate halves (each of which\n * UCS-2 exposes as separate characters) into a single code point,\n * matching UTF-16.\n * @see `punycode.ucs2.encode`\n * @see <https://mathiasbynens.be/notes/javascript-encoding>\n * @memberOf punycode.ucs2\n * @name decode\n * @param {String} string The Unicode input string (UCS-2).\n * @returns {Array} The new array of code points.\n */\nfunction ucs2decode(string) {\n\tconst output = [];\n\tlet counter = 0;\n\tconst length = string.length;\n\twhile (counter < length) {\n\t\tconst value = string.charCodeAt(counter++);\n\t\tif (value >= 0xD800 && value <= 0xDBFF && counter < length) {\n\t\t\t// It's a high surrogate, and there is a next character.\n\t\t\tconst extra = string.charCodeAt(counter++);\n\t\t\tif ((extra & 0xFC00) == 0xDC00) { // Low surrogate.\n\t\t\t\toutput.push(((value & 0x3FF) << 10) + (extra & 0x3FF) + 0x10000);\n\t\t\t} else {\n\t\t\t\t// It's an unmatched surrogate; only append this code unit, in case the\n\t\t\t\t// next code unit is the high surrogate of a surrogate pair.\n\t\t\t\toutput.push(value);\n\t\t\t\tcounter--;\n\t\t\t}\n\t\t} else {\n\t\t\toutput.push(value);\n\t\t}\n\t}\n\treturn output;\n}\n\n/**\n * Creates a string based on an array of numeric code points.\n * @see `punycode.ucs2.decode`\n * @memberOf punycode.ucs2\n * @name encode\n * @param {Array} codePoints The array of numeric code points.\n * @returns {String} The new Unicode string (UCS-2).\n */\nconst ucs2encode = codePoints => String.fromCodePoint(...codePoints);\n\n/**\n * Converts a basic code point into a digit/integer.\n * @see `digitToBasic()`\n * @private\n * @param {Number} codePoint The basic numeric code point value.\n * @returns {Number} The numeric value of a basic code point (for use in\n * representing integers) in the range `0` to `base - 1`, or `base` if\n * the code point does not represent a value.\n */\nconst basicToDigit = function(codePoint) {\n\tif (codePoint >= 0x30 && codePoint < 0x3A) {\n\t\treturn 26 + (codePoint - 0x30);\n\t}\n\tif (codePoint >= 0x41 && codePoint < 0x5B) {\n\t\treturn codePoint - 0x41;\n\t}\n\tif (codePoint >= 0x61 && codePoint < 0x7B) {\n\t\treturn codePoint - 0x61;\n\t}\n\treturn base;\n};\n\n/**\n * Converts a digit/integer into a basic code point.\n * @see `basicToDigit()`\n * @private\n * @param {Number} digit The numeric value of a basic code point.\n * @returns {Number} The basic code point whose value (when used for\n * representing integers) is `digit`, which needs to be in the range\n * `0` to `base - 1`. If `flag` is non-zero, the uppercase form is\n * used; else, the lowercase form is used. The behavior is undefined\n * if `flag` is non-zero and `digit` has no uppercase form.\n */\nconst digitToBasic = function(digit, flag) {\n\t//  0..25 map to ASCII a..z or A..Z\n\t// 26..35 map to ASCII 0..9\n\treturn digit + 22 + 75 * (digit < 26) - ((flag != 0) << 5);\n};\n\n/**\n * Bias adaptation function as per section 3.4 of RFC 3492.\n * https://tools.ietf.org/html/rfc3492#section-3.4\n * @private\n */\nconst adapt = function(delta, numPoints, firstTime) {\n\tlet k = 0;\n\tdelta = firstTime ? floor(delta / damp) : delta >> 1;\n\tdelta += floor(delta / numPoints);\n\tfor (/* no initialization */; delta > baseMinusTMin * tMax >> 1; k += base) {\n\t\tdelta = floor(delta / baseMinusTMin);\n\t}\n\treturn floor(k + (baseMinusTMin + 1) * delta / (delta + skew));\n};\n\n/**\n * Converts a Punycode string of ASCII-only symbols to a string of Unicode\n * symbols.\n * @memberOf punycode\n * @param {String} input The Punycode string of ASCII-only symbols.\n * @returns {String} The resulting string of Unicode symbols.\n */\nconst decode = function(input) {\n\t// Don't use UCS-2.\n\tconst output = [];\n\tconst inputLength = input.length;\n\tlet i = 0;\n\tlet n = initialN;\n\tlet bias = initialBias;\n\n\t// Handle the basic code points: let `basic` be the number of input code\n\t// points before the last delimiter, or `0` if there is none, then copy\n\t// the first basic code points to the output.\n\n\tlet basic = input.lastIndexOf(delimiter);\n\tif (basic < 0) {\n\t\tbasic = 0;\n\t}\n\n\tfor (let j = 0; j < basic; ++j) {\n\t\t// if it's not a basic code point\n\t\tif (input.charCodeAt(j) >= 0x80) {\n\t\t\terror('not-basic');\n\t\t}\n\t\toutput.push(input.charCodeAt(j));\n\t}\n\n\t// Main decoding loop: start just after the last delimiter if any basic code\n\t// points were copied; start at the beginning otherwise.\n\n\tfor (let index = basic > 0 ? basic + 1 : 0; index < inputLength; /* no final expression */) {\n\n\t\t// `index` is the index of the next character to be consumed.\n\t\t// Decode a generalized variable-length integer into `delta`,\n\t\t// which gets added to `i`. The overflow checking is easier\n\t\t// if we increase `i` as we go, then subtract off its starting\n\t\t// value at the end to obtain `delta`.\n\t\tconst oldi = i;\n\t\tfor (let w = 1, k = base; /* no condition */; k += base) {\n\n\t\t\tif (index >= inputLength) {\n\t\t\t\terror('invalid-input');\n\t\t\t}\n\n\t\t\tconst digit = basicToDigit(input.charCodeAt(index++));\n\n\t\t\tif (digit >= base) {\n\t\t\t\terror('invalid-input');\n\t\t\t}\n\t\t\tif (digit > floor((maxInt - i) / w)) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\n\t\t\ti += digit * w;\n\t\t\tconst t = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);\n\n\t\t\tif (digit < t) {\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\tconst baseMinusT = base - t;\n\t\t\tif (w > floor(maxInt / baseMinusT)) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\n\t\t\tw *= baseMinusT;\n\n\t\t}\n\n\t\tconst out = output.length + 1;\n\t\tbias = adapt(i - oldi, out, oldi == 0);\n\n\t\t// `i` was supposed to wrap around from `out` to `0`,\n\t\t// incrementing `n` each time, so we'll fix that now:\n\t\tif (floor(i / out) > maxInt - n) {\n\t\t\terror('overflow');\n\t\t}\n\n\t\tn += floor(i / out);\n\t\ti %= out;\n\n\t\t// Insert `n` at position `i` of the output.\n\t\toutput.splice(i++, 0, n);\n\n\t}\n\n\treturn String.fromCodePoint(...output);\n};\n\n/**\n * Converts a string of Unicode symbols (e.g. a domain name label) to a\n * Punycode string of ASCII-only symbols.\n * @memberOf punycode\n * @param {String} input The string of Unicode symbols.\n * @returns {String} The resulting Punycode string of ASCII-only symbols.\n */\nconst encode = function(input) {\n\tconst output = [];\n\n\t// Convert the input in UCS-2 to an array of Unicode code points.\n\tinput = ucs2decode(input);\n\n\t// Cache the length.\n\tconst inputLength = input.length;\n\n\t// Initialize the state.\n\tlet n = initialN;\n\tlet delta = 0;\n\tlet bias = initialBias;\n\n\t// Handle the basic code points.\n\tfor (const currentValue of input) {\n\t\tif (currentValue < 0x80) {\n\t\t\toutput.push(stringFromCharCode(currentValue));\n\t\t}\n\t}\n\n\tconst basicLength = output.length;\n\tlet handledCPCount = basicLength;\n\n\t// `handledCPCount` is the number of code points that have been handled;\n\t// `basicLength` is the number of basic code points.\n\n\t// Finish the basic string with a delimiter unless it's empty.\n\tif (basicLength) {\n\t\toutput.push(delimiter);\n\t}\n\n\t// Main encoding loop:\n\twhile (handledCPCount < inputLength) {\n\n\t\t// All non-basic code points < n have been handled already. Find the next\n\t\t// larger one:\n\t\tlet m = maxInt;\n\t\tfor (const currentValue of input) {\n\t\t\tif (currentValue >= n && currentValue < m) {\n\t\t\t\tm = currentValue;\n\t\t\t}\n\t\t}\n\n\t\t// Increase `delta` enough to advance the decoder's <n,i> state to <m,0>,\n\t\t// but guard against overflow.\n\t\tconst handledCPCountPlusOne = handledCPCount + 1;\n\t\tif (m - n > floor((maxInt - delta) / handledCPCountPlusOne)) {\n\t\t\terror('overflow');\n\t\t}\n\n\t\tdelta += (m - n) * handledCPCountPlusOne;\n\t\tn = m;\n\n\t\tfor (const currentValue of input) {\n\t\t\tif (currentValue < n && ++delta > maxInt) {\n\t\t\t\terror('overflow');\n\t\t\t}\n\t\t\tif (currentValue === n) {\n\t\t\t\t// Represent delta as a generalized variable-length integer.\n\t\t\t\tlet q = delta;\n\t\t\t\tfor (let k = base; /* no condition */; k += base) {\n\t\t\t\t\tconst t = k <= bias ? tMin : (k >= bias + tMax ? tMax : k - bias);\n\t\t\t\t\tif (q < t) {\n\t\t\t\t\t\tbreak;\n\t\t\t\t\t}\n\t\t\t\t\tconst qMinusT = q - t;\n\t\t\t\t\tconst baseMinusT = base - t;\n\t\t\t\t\toutput.push(\n\t\t\t\t\t\tstringFromCharCode(digitToBasic(t + qMinusT % baseMinusT, 0))\n\t\t\t\t\t);\n\t\t\t\t\tq = floor(qMinusT / baseMinusT);\n\t\t\t\t}\n\n\t\t\t\toutput.push(stringFromCharCode(digitToBasic(q, 0)));\n\t\t\t\tbias = adapt(delta, handledCPCountPlusOne, handledCPCount === basicLength);\n\t\t\t\tdelta = 0;\n\t\t\t\t++handledCPCount;\n\t\t\t}\n\t\t}\n\n\t\t++delta;\n\t\t++n;\n\n\t}\n\treturn output.join('');\n};\n\n/**\n * Converts a Punycode string representing a domain name or an email address\n * to Unicode. Only the Punycoded parts of the input will be converted, i.e.\n * it doesn't matter if you call it on a string that has already been\n * converted to Unicode.\n * @memberOf punycode\n * @param {String} input The Punycoded domain name or email address to\n * convert to Unicode.\n * @returns {String} The Unicode representation of the given Punycode\n * string.\n */\nconst toUnicode = function(input) {\n\treturn mapDomain(input, function(string) {\n\t\treturn regexPunycode.test(string)\n\t\t\t? decode(string.slice(4).toLowerCase())\n\t\t\t: string;\n\t});\n};\n\n/**\n * Converts a Unicode string representing a domain name or an email address to\n * Punycode. Only the non-ASCII parts of the domain name will be converted,\n * i.e. it doesn't matter if you call it with a domain that's already in\n * ASCII.\n * @memberOf punycode\n * @param {String} input The domain name or email address to convert, as a\n * Unicode string.\n * @returns {String} The Punycode representation of the given domain name or\n * email address.\n */\nconst toASCII = function(input) {\n\treturn mapDomain(input, function(string) {\n\t\treturn regexNonASCII.test(string)\n\t\t\t? 'xn--' + encode(string)\n\t\t\t: string;\n\t});\n};\n\n/*--------------------------------------------------------------------------*/\n\n/** Define the public API */\nconst punycode = {\n\t/**\n\t * A string representing the current Punycode.js version number.\n\t * @memberOf punycode\n\t * @type String\n\t */\n\t'version': '2.3.1',\n\t/**\n\t * An object of methods to convert from JavaScript's internal character\n\t * representation (UCS-2) to Unicode code points, and back.\n\t * @see <https://mathiasbynens.be/notes/javascript-encoding>\n\t * @memberOf punycode\n\t * @type Object\n\t */\n\t'ucs2': {\n\t\t'decode': ucs2decode,\n\t\t'encode': ucs2encode\n\t},\n\t'decode': decode,\n\t'encode': encode,\n\t'toASCII': toASCII,\n\t'toUnicode': toUnicode\n};\n\nexport { ucs2decode, ucs2encode, decode, encode, toASCII, toUnicode };\nexport default punycode;\n"], "mappings": "AAAA,YAAY;;AAEZ;AACA,MAAMA,MAAM,GAAG,UAAU,CAAC,CAAC;;AAE3B;AACA,MAAMC,IAAI,GAAG,EAAE;AACf,MAAMC,IAAI,GAAG,CAAC;AACd,MAAMC,IAAI,GAAG,EAAE;AACf,MAAMC,IAAI,GAAG,EAAE;AACf,MAAMC,IAAI,GAAG,GAAG;AAChB,MAAMC,WAAW,GAAG,EAAE;AACtB,MAAMC,QAAQ,GAAG,GAAG,CAAC,CAAC;AACtB,MAAMC,SAAS,GAAG,GAAG,CAAC,CAAC;;AAEvB;AACA,MAAMC,aAAa,GAAG,OAAO;AAC7B,MAAMC,aAAa,GAAG,YAAY,CAAC,CAAC;AACpC,MAAMC,eAAe,GAAG,2BAA2B,CAAC,CAAC;;AAErD;AACA,MAAMC,MAAM,GAAG;EACd,UAAU,EAAE,iDAAiD;EAC7D,WAAW,EAAE,gDAAgD;EAC7D,eAAe,EAAE;AAClB,CAAC;;AAED;AACA,MAAMC,aAAa,GAAGZ,IAAI,GAAGC,IAAI;AACjC,MAAMY,KAAK,GAAGC,IAAI,CAACD,KAAK;AACxB,MAAME,kBAAkB,GAAGC,MAAM,CAACC,YAAY;;AAE9C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,KAAKA,CAACC,IAAI,EAAE;EACpB,MAAM,IAAIC,UAAU,CAACT,MAAM,CAACQ,IAAI,CAAC,CAAC;AACnC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,GAAGA,CAACC,KAAK,EAAEC,QAAQ,EAAE;EAC7B,MAAMC,MAAM,GAAG,EAAE;EACjB,IAAIC,MAAM,GAAGH,KAAK,CAACG,MAAM;EACzB,OAAOA,MAAM,EAAE,EAAE;IAChBD,MAAM,CAACC,MAAM,CAAC,GAAGF,QAAQ,CAACD,KAAK,CAACG,MAAM,CAAC,CAAC;EACzC;EACA,OAAOD,MAAM;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,SAASA,CAACC,MAAM,EAAEJ,QAAQ,EAAE;EACpC,MAAMK,KAAK,GAAGD,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC;EAC/B,IAAIL,MAAM,GAAG,EAAE;EACf,IAAII,KAAK,CAACH,MAAM,GAAG,CAAC,EAAE;IACrB;IACA;IACAD,MAAM,GAAGI,KAAK,CAAC,CAAC,CAAC,GAAG,GAAG;IACvBD,MAAM,GAAGC,KAAK,CAAC,CAAC,CAAC;EAClB;EACA;EACAD,MAAM,GAAGA,MAAM,CAACG,OAAO,CAACpB,eAAe,EAAE,MAAM,CAAC;EAChD,MAAMqB,MAAM,GAAGJ,MAAM,CAACE,KAAK,CAAC,GAAG,CAAC;EAChC,MAAMG,OAAO,GAAGX,GAAG,CAACU,MAAM,EAAER,QAAQ,CAAC,CAACU,IAAI,CAAC,GAAG,CAAC;EAC/C,OAAOT,MAAM,GAAGQ,OAAO;AACxB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,UAAUA,CAACC,MAAM,EAAE;EAC3B,MAAMC,MAAM,GAAG,EAAE;EACjB,IAAIC,OAAO,GAAG,CAAC;EACf,MAAMZ,MAAM,GAAGU,MAAM,CAACV,MAAM;EAC5B,OAAOY,OAAO,GAAGZ,MAAM,EAAE;IACxB,MAAMa,KAAK,GAAGH,MAAM,CAACI,UAAU,CAACF,OAAO,EAAE,CAAC;IAC1C,IAAIC,KAAK,IAAI,MAAM,IAAIA,KAAK,IAAI,MAAM,IAAID,OAAO,GAAGZ,MAAM,EAAE;MAC3D;MACA,MAAMe,KAAK,GAAGL,MAAM,CAACI,UAAU,CAACF,OAAO,EAAE,CAAC;MAC1C,IAAI,CAACG,KAAK,GAAG,MAAM,KAAK,MAAM,EAAE;QAAE;QACjCJ,MAAM,CAACK,IAAI,CAAC,CAAC,CAACH,KAAK,GAAG,KAAK,KAAK,EAAE,KAAKE,KAAK,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC;MACjE,CAAC,MAAM;QACN;QACA;QACAJ,MAAM,CAACK,IAAI,CAACH,KAAK,CAAC;QAClBD,OAAO,EAAE;MACV;IACD,CAAC,MAAM;MACND,MAAM,CAACK,IAAI,CAACH,KAAK,CAAC;IACnB;EACD;EACA,OAAOF,MAAM;AACd;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,UAAU,GAAGC,UAAU,IAAI3B,MAAM,CAAC4B,aAAa,CAAC,GAAGD,UAAU,CAAC;;AAEpE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,YAAY,GAAG,SAAAA,CAASC,SAAS,EAAE;EACxC,IAAIA,SAAS,IAAI,IAAI,IAAIA,SAAS,GAAG,IAAI,EAAE;IAC1C,OAAO,EAAE,IAAIA,SAAS,GAAG,IAAI,CAAC;EAC/B;EACA,IAAIA,SAAS,IAAI,IAAI,IAAIA,SAAS,GAAG,IAAI,EAAE;IAC1C,OAAOA,SAAS,GAAG,IAAI;EACxB;EACA,IAAIA,SAAS,IAAI,IAAI,IAAIA,SAAS,GAAG,IAAI,EAAE;IAC1C,OAAOA,SAAS,GAAG,IAAI;EACxB;EACA,OAAO9C,IAAI;AACZ,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+C,YAAY,GAAG,SAAAA,CAASC,KAAK,EAAEC,IAAI,EAAE;EAC1C;EACA;EACA,OAAOD,KAAK,GAAG,EAAE,GAAG,EAAE,IAAIA,KAAK,GAAG,EAAE,CAAC,IAAI,CAACC,IAAI,IAAI,CAAC,KAAK,CAAC,CAAC;AAC3D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA,MAAMC,KAAK,GAAG,SAAAA,CAASC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAE;EACnD,IAAIC,CAAC,GAAG,CAAC;EACTH,KAAK,GAAGE,SAAS,GAAGxC,KAAK,CAACsC,KAAK,GAAG/C,IAAI,CAAC,GAAG+C,KAAK,IAAI,CAAC;EACpDA,KAAK,IAAItC,KAAK,CAACsC,KAAK,GAAGC,SAAS,CAAC;EACjC,IAAK;EAAA,GAAyBD,KAAK,GAAGvC,aAAa,GAAGV,IAAI,IAAI,CAAC,EAAEoD,CAAC,IAAItD,IAAI,EAAE;IAC3EmD,KAAK,GAAGtC,KAAK,CAACsC,KAAK,GAAGvC,aAAa,CAAC;EACrC;EACA,OAAOC,KAAK,CAACyC,CAAC,GAAG,CAAC1C,aAAa,GAAG,CAAC,IAAIuC,KAAK,IAAIA,KAAK,GAAGhD,IAAI,CAAC,CAAC;AAC/D,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoD,MAAM,GAAG,SAAAA,CAASC,KAAK,EAAE;EAC9B;EACA,MAAMpB,MAAM,GAAG,EAAE;EACjB,MAAMqB,WAAW,GAAGD,KAAK,CAAC/B,MAAM;EAChC,IAAIiC,CAAC,GAAG,CAAC;EACT,IAAIC,CAAC,GAAGrD,QAAQ;EAChB,IAAIsD,IAAI,GAAGvD,WAAW;;EAEtB;EACA;EACA;;EAEA,IAAIwD,KAAK,GAAGL,KAAK,CAACM,WAAW,CAACvD,SAAS,CAAC;EACxC,IAAIsD,KAAK,GAAG,CAAC,EAAE;IACdA,KAAK,GAAG,CAAC;EACV;EAEA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,KAAK,EAAE,EAAEE,CAAC,EAAE;IAC/B;IACA,IAAIP,KAAK,CAACjB,UAAU,CAACwB,CAAC,CAAC,IAAI,IAAI,EAAE;MAChC7C,KAAK,CAAC,WAAW,CAAC;IACnB;IACAkB,MAAM,CAACK,IAAI,CAACe,KAAK,CAACjB,UAAU,CAACwB,CAAC,CAAC,CAAC;EACjC;;EAEA;EACA;;EAEA,IAAiE;EAAA,CAA5D,IAAIC,KAAK,GAAGH,KAAK,GAAG,CAAC,GAAGA,KAAK,GAAG,CAAC,GAAG,CAAC,EAAEG,KAAK,GAAGP,WAAW,GAA6B;IAE3F;IACA;IACA;IACA;IACA;IACA,MAAMQ,IAAI,GAAGP,CAAC;IACd,IAA0B;IAAA,CAArB,IAAIQ,CAAC,GAAG,CAAC,EAAEZ,CAAC,GAAGtD,IAAI,GAAsBsD,CAAC,IAAItD,IAAI,EAAE;MAExD,IAAIgE,KAAK,IAAIP,WAAW,EAAE;QACzBvC,KAAK,CAAC,eAAe,CAAC;MACvB;MAEA,MAAM8B,KAAK,GAAGH,YAAY,CAACW,KAAK,CAACjB,UAAU,CAACyB,KAAK,EAAE,CAAC,CAAC;MAErD,IAAIhB,KAAK,IAAIhD,IAAI,EAAE;QAClBkB,KAAK,CAAC,eAAe,CAAC;MACvB;MACA,IAAI8B,KAAK,GAAGnC,KAAK,CAAC,CAACd,MAAM,GAAG2D,CAAC,IAAIQ,CAAC,CAAC,EAAE;QACpChD,KAAK,CAAC,UAAU,CAAC;MAClB;MAEAwC,CAAC,IAAIV,KAAK,GAAGkB,CAAC;MACd,MAAMC,CAAC,GAAGb,CAAC,IAAIM,IAAI,GAAG3D,IAAI,GAAIqD,CAAC,IAAIM,IAAI,GAAG1D,IAAI,GAAGA,IAAI,GAAGoD,CAAC,GAAGM,IAAK;MAEjE,IAAIZ,KAAK,GAAGmB,CAAC,EAAE;QACd;MACD;MAEA,MAAMC,UAAU,GAAGpE,IAAI,GAAGmE,CAAC;MAC3B,IAAID,CAAC,GAAGrD,KAAK,CAACd,MAAM,GAAGqE,UAAU,CAAC,EAAE;QACnClD,KAAK,CAAC,UAAU,CAAC;MAClB;MAEAgD,CAAC,IAAIE,UAAU;IAEhB;IAEA,MAAMC,GAAG,GAAGjC,MAAM,CAACX,MAAM,GAAG,CAAC;IAC7BmC,IAAI,GAAGV,KAAK,CAACQ,CAAC,GAAGO,IAAI,EAAEI,GAAG,EAAEJ,IAAI,IAAI,CAAC,CAAC;;IAEtC;IACA;IACA,IAAIpD,KAAK,CAAC6C,CAAC,GAAGW,GAAG,CAAC,GAAGtE,MAAM,GAAG4D,CAAC,EAAE;MAChCzC,KAAK,CAAC,UAAU,CAAC;IAClB;IAEAyC,CAAC,IAAI9C,KAAK,CAAC6C,CAAC,GAAGW,GAAG,CAAC;IACnBX,CAAC,IAAIW,GAAG;;IAER;IACAjC,MAAM,CAACkC,MAAM,CAACZ,CAAC,EAAE,EAAE,CAAC,EAAEC,CAAC,CAAC;EAEzB;EAEA,OAAO3C,MAAM,CAAC4B,aAAa,CAAC,GAAGR,MAAM,CAAC;AACvC,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmC,MAAM,GAAG,SAAAA,CAASf,KAAK,EAAE;EAC9B,MAAMpB,MAAM,GAAG,EAAE;;EAEjB;EACAoB,KAAK,GAAGtB,UAAU,CAACsB,KAAK,CAAC;;EAEzB;EACA,MAAMC,WAAW,GAAGD,KAAK,CAAC/B,MAAM;;EAEhC;EACA,IAAIkC,CAAC,GAAGrD,QAAQ;EAChB,IAAI6C,KAAK,GAAG,CAAC;EACb,IAAIS,IAAI,GAAGvD,WAAW;;EAEtB;EACA,KAAK,MAAMmE,YAAY,IAAIhB,KAAK,EAAE;IACjC,IAAIgB,YAAY,GAAG,IAAI,EAAE;MACxBpC,MAAM,CAACK,IAAI,CAAC1B,kBAAkB,CAACyD,YAAY,CAAC,CAAC;IAC9C;EACD;EAEA,MAAMC,WAAW,GAAGrC,MAAM,CAACX,MAAM;EACjC,IAAIiD,cAAc,GAAGD,WAAW;;EAEhC;EACA;;EAEA;EACA,IAAIA,WAAW,EAAE;IAChBrC,MAAM,CAACK,IAAI,CAAClC,SAAS,CAAC;EACvB;;EAEA;EACA,OAAOmE,cAAc,GAAGjB,WAAW,EAAE;IAEpC;IACA;IACA,IAAIkB,CAAC,GAAG5E,MAAM;IACd,KAAK,MAAMyE,YAAY,IAAIhB,KAAK,EAAE;MACjC,IAAIgB,YAAY,IAAIb,CAAC,IAAIa,YAAY,GAAGG,CAAC,EAAE;QAC1CA,CAAC,GAAGH,YAAY;MACjB;IACD;;IAEA;IACA;IACA,MAAMI,qBAAqB,GAAGF,cAAc,GAAG,CAAC;IAChD,IAAIC,CAAC,GAAGhB,CAAC,GAAG9C,KAAK,CAAC,CAACd,MAAM,GAAGoD,KAAK,IAAIyB,qBAAqB,CAAC,EAAE;MAC5D1D,KAAK,CAAC,UAAU,CAAC;IAClB;IAEAiC,KAAK,IAAI,CAACwB,CAAC,GAAGhB,CAAC,IAAIiB,qBAAqB;IACxCjB,CAAC,GAAGgB,CAAC;IAEL,KAAK,MAAMH,YAAY,IAAIhB,KAAK,EAAE;MACjC,IAAIgB,YAAY,GAAGb,CAAC,IAAI,EAAER,KAAK,GAAGpD,MAAM,EAAE;QACzCmB,KAAK,CAAC,UAAU,CAAC;MAClB;MACA,IAAIsD,YAAY,KAAKb,CAAC,EAAE;QACvB;QACA,IAAIkB,CAAC,GAAG1B,KAAK;QACb,IAAmB;QAAA,CAAd,IAAIG,CAAC,GAAGtD,IAAI,GAAsBsD,CAAC,IAAItD,IAAI,EAAE;UACjD,MAAMmE,CAAC,GAAGb,CAAC,IAAIM,IAAI,GAAG3D,IAAI,GAAIqD,CAAC,IAAIM,IAAI,GAAG1D,IAAI,GAAGA,IAAI,GAAGoD,CAAC,GAAGM,IAAK;UACjE,IAAIiB,CAAC,GAAGV,CAAC,EAAE;YACV;UACD;UACA,MAAMW,OAAO,GAAGD,CAAC,GAAGV,CAAC;UACrB,MAAMC,UAAU,GAAGpE,IAAI,GAAGmE,CAAC;UAC3B/B,MAAM,CAACK,IAAI,CACV1B,kBAAkB,CAACgC,YAAY,CAACoB,CAAC,GAAGW,OAAO,GAAGV,UAAU,EAAE,CAAC,CAAC,CAC7D,CAAC;UACDS,CAAC,GAAGhE,KAAK,CAACiE,OAAO,GAAGV,UAAU,CAAC;QAChC;QAEAhC,MAAM,CAACK,IAAI,CAAC1B,kBAAkB,CAACgC,YAAY,CAAC8B,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;QACnDjB,IAAI,GAAGV,KAAK,CAACC,KAAK,EAAEyB,qBAAqB,EAAEF,cAAc,KAAKD,WAAW,CAAC;QAC1EtB,KAAK,GAAG,CAAC;QACT,EAAEuB,cAAc;MACjB;IACD;IAEA,EAAEvB,KAAK;IACP,EAAEQ,CAAC;EAEJ;EACA,OAAOvB,MAAM,CAACH,IAAI,CAAC,EAAE,CAAC;AACvB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM8C,SAAS,GAAG,SAAAA,CAASvB,KAAK,EAAE;EACjC,OAAO9B,SAAS,CAAC8B,KAAK,EAAE,UAASrB,MAAM,EAAE;IACxC,OAAO3B,aAAa,CAACwE,IAAI,CAAC7C,MAAM,CAAC,GAC9BoB,MAAM,CAACpB,MAAM,CAAC8C,KAAK,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC,GACrC/C,MAAM;EACV,CAAC,CAAC;AACH,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMgD,OAAO,GAAG,SAAAA,CAAS3B,KAAK,EAAE;EAC/B,OAAO9B,SAAS,CAAC8B,KAAK,EAAE,UAASrB,MAAM,EAAE;IACxC,OAAO1B,aAAa,CAACuE,IAAI,CAAC7C,MAAM,CAAC,GAC9B,MAAM,GAAGoC,MAAM,CAACpC,MAAM,CAAC,GACvBA,MAAM;EACV,CAAC,CAAC;AACH,CAAC;;AAED;;AAEA;AACA,MAAMiD,QAAQ,GAAG;EAChB;AACD;AACA;AACA;AACA;EACC,SAAS,EAAE,OAAO;EAClB;AACD;AACA;AACA;AACA;AACA;AACA;EACC,MAAM,EAAE;IACP,QAAQ,EAAElD,UAAU;IACpB,QAAQ,EAAEQ;EACX,CAAC;EACD,QAAQ,EAAEa,MAAM;EAChB,QAAQ,EAAEgB,MAAM;EAChB,SAAS,EAAEY,OAAO;EAClB,WAAW,EAAEJ;AACd,CAAC;AAED,SAAS7C,UAAU,EAAEQ,UAAU,EAAEa,MAAM,EAAEgB,MAAM,EAAEY,OAAO,EAAEJ,SAAS;AACnE,eAAeK,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}