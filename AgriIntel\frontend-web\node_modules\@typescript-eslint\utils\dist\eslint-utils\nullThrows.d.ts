/**
 * A set of common reasons for calling nullThrows
 */
declare const NullThrowsReasons: {
    readonly MissingParent: "Expected node to have a parent.";
    readonly MissingToken: (token: string, thing: string) => string;
};
/**
 * Assert that a value must not be null or undefined.
 * This is a nice explicit alternative to the non-null assertion operator.
 */
declare function nullThrows<T>(value: T | null | undefined, message: string): T;
export { nullThrows, NullThrowsReasons };
//# sourceMappingURL=nullThrows.d.ts.map