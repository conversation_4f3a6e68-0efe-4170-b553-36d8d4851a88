{"ast": null, "code": "\"use strict\";\n\nconst {\n  URL,\n  URLSearchParams\n} = require(\"./webidl2js-wrapper\");\nconst urlStateMachine = require(\"./lib/url-state-machine\");\nconst percentEncoding = require(\"./lib/percent-encoding\");\nconst sharedGlobalObject = {\n  Array,\n  Object,\n  Promise,\n  String,\n  TypeError\n};\nURL.install(sharedGlobalObject, [\"Window\"]);\nURLSearchParams.install(sharedGlobalObject, [\"Window\"]);\nexports.URL = sharedGlobalObject.URL;\nexports.URLSearchParams = sharedGlobalObject.URLSearchParams;\nexports.parseURL = urlStateMachine.parseURL;\nexports.basicURLParse = urlStateMachine.basicURLParse;\nexports.serializeURL = urlStateMachine.serializeURL;\nexports.serializePath = urlStateMachine.serializePath;\nexports.serializeHost = urlStateMachine.serializeHost;\nexports.serializeInteger = urlStateMachine.serializeInteger;\nexports.serializeURLOrigin = urlStateMachine.serializeURLOrigin;\nexports.setTheUsername = urlStateMachine.setTheUsername;\nexports.setThePassword = urlStateMachine.setThePassword;\nexports.cannotHaveAUsernamePasswordPort = urlStateMachine.cannotHaveAUsernamePasswordPort;\nexports.hasAnOpaquePath = urlStateMachine.hasAnOpaquePath;\nexports.percentDecodeString = percentEncoding.percentDecodeString;\nexports.percentDecodeBytes = percentEncoding.percentDecodeBytes;", "map": {"version": 3, "names": ["URL", "URLSearchParams", "require", "urlStateMachine", "percentEncoding", "sharedGlobalObject", "Array", "Object", "Promise", "String", "TypeError", "install", "exports", "parseURL", "basicURLParse", "serializeURL", "serializePath", "serializeHost", "serializeInteger", "serializeURLOrigin", "setTheUsername", "setThePassword", "cannotHaveAUsernamePasswordPort", "hasAnOpaquePath", "percentDecodeString", "percentDecodeBytes"], "sources": ["C:/Users/<USER>/node_modules/mongodb-connection-string-url/node_modules/whatwg-url/index.js"], "sourcesContent": ["\"use strict\";\n\nconst { URL, URLSearchParams } = require(\"./webidl2js-wrapper\");\nconst urlStateMachine = require(\"./lib/url-state-machine\");\nconst percentEncoding = require(\"./lib/percent-encoding\");\n\nconst sharedGlobalObject = { Array, Object, Promise, String, TypeError };\nURL.install(sharedGlobalObject, [\"Window\"]);\nURLSearchParams.install(sharedGlobalObject, [\"Window\"]);\n\nexports.URL = sharedGlobalObject.URL;\nexports.URLSearchParams = sharedGlobalObject.URLSearchParams;\n\nexports.parseURL = urlStateMachine.parseURL;\nexports.basicURLParse = urlStateMachine.basicURLParse;\nexports.serializeURL = urlStateMachine.serializeURL;\nexports.serializePath = urlStateMachine.serializePath;\nexports.serializeHost = urlStateMachine.serializeHost;\nexports.serializeInteger = urlStateMachine.serializeInteger;\nexports.serializeURLOrigin = urlStateMachine.serializeURLOrigin;\nexports.setTheUsername = urlStateMachine.setTheUsername;\nexports.setThePassword = urlStateMachine.setThePassword;\nexports.cannotHaveAUsernamePasswordPort = urlStateMachine.cannotHaveAUsernamePasswordPort;\nexports.hasAnOpaquePath = urlStateMachine.hasAnOpaquePath;\n\nexports.percentDecodeString = percentEncoding.percentDecodeString;\nexports.percentDecodeBytes = percentEncoding.percentDecodeBytes;\n"], "mappings": "AAAA,YAAY;;AAEZ,MAAM;EAAEA,GAAG;EAAEC;AAAgB,CAAC,GAAGC,OAAO,CAAC,qBAAqB,CAAC;AAC/D,MAAMC,eAAe,GAAGD,OAAO,CAAC,yBAAyB,CAAC;AAC1D,MAAME,eAAe,GAAGF,OAAO,CAAC,wBAAwB,CAAC;AAEzD,MAAMG,kBAAkB,GAAG;EAAEC,KAAK;EAAEC,MAAM;EAAEC,OAAO;EAAEC,MAAM;EAAEC;AAAU,CAAC;AACxEV,GAAG,CAACW,OAAO,CAACN,kBAAkB,EAAE,CAAC,QAAQ,CAAC,CAAC;AAC3CJ,eAAe,CAACU,OAAO,CAACN,kBAAkB,EAAE,CAAC,QAAQ,CAAC,CAAC;AAEvDO,OAAO,CAACZ,GAAG,GAAGK,kBAAkB,CAACL,GAAG;AACpCY,OAAO,CAACX,eAAe,GAAGI,kBAAkB,CAACJ,eAAe;AAE5DW,OAAO,CAACC,QAAQ,GAAGV,eAAe,CAACU,QAAQ;AAC3CD,OAAO,CAACE,aAAa,GAAGX,eAAe,CAACW,aAAa;AACrDF,OAAO,CAACG,YAAY,GAAGZ,eAAe,CAACY,YAAY;AACnDH,OAAO,CAACI,aAAa,GAAGb,eAAe,CAACa,aAAa;AACrDJ,OAAO,CAACK,aAAa,GAAGd,eAAe,CAACc,aAAa;AACrDL,OAAO,CAACM,gBAAgB,GAAGf,eAAe,CAACe,gBAAgB;AAC3DN,OAAO,CAACO,kBAAkB,GAAGhB,eAAe,CAACgB,kBAAkB;AAC/DP,OAAO,CAACQ,cAAc,GAAGjB,eAAe,CAACiB,cAAc;AACvDR,OAAO,CAACS,cAAc,GAAGlB,eAAe,CAACkB,cAAc;AACvDT,OAAO,CAACU,+BAA+B,GAAGnB,eAAe,CAACmB,+BAA+B;AACzFV,OAAO,CAACW,eAAe,GAAGpB,eAAe,CAACoB,eAAe;AAEzDX,OAAO,CAACY,mBAAmB,GAAGpB,eAAe,CAACoB,mBAAmB;AACjEZ,OAAO,CAACa,kBAAkB,GAAGrB,eAAe,CAACqB,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}