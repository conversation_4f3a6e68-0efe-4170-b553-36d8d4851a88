{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports.finishCommandDocument = finishCommandDocument;\nexports.startCommandDocument = startCommandDocument;\nconst bson_1 = require(\"../../../bson\");\nconst providers_1 = require(\"../providers\");\n/**\n * Generate the finishing command document for authentication. Will be a\n * saslStart or saslContinue depending on the presence of a conversation id.\n */\nfunction finishCommandDocument(token, conversationId) {\n  if (conversationId != null) {\n    return {\n      saslContinue: 1,\n      conversationId: conversationId,\n      payload: new bson_1.Binary(bson_1.BSON.serialize({\n        jwt: token\n      }))\n    };\n  }\n  // saslContinue requires a conversationId in the command to be valid so in this\n  // case the server allows \"step two\" to actually be a saslStart with the token\n  // as the jwt since the use of the cached value has no correlating conversating\n  // on the particular connection.\n  return {\n    saslStart: 1,\n    mechanism: providers_1.AuthMechanism.MONGODB_OIDC,\n    payload: new bson_1.Binary(bson_1.BSON.serialize({\n      jwt: token\n    }))\n  };\n}\n/**\n * Generate the saslStart command document.\n */\nfunction startCommandDocument(credentials) {\n  const payload = {};\n  if (credentials.username) {\n    payload.n = credentials.username;\n  }\n  return {\n    saslStart: 1,\n    autoAuthorize: 1,\n    mechanism: providers_1.AuthMechanism.MONGODB_OIDC,\n    payload: new bson_1.Binary(bson_1.BSON.serialize(payload))\n  };\n}", "map": {"version": 3, "names": ["exports", "finishCommandDocument", "startCommandDocument", "bson_1", "require", "providers_1", "token", "conversationId", "saslContinue", "payload", "Binary", "BSON", "serialize", "jwt", "saslStart", "mechanism", "AuthMechanism", "MONGODB_OIDC", "credentials", "username", "n", "autoAuthorize"], "sources": ["C:\\Users\\<USER>\\node_modules\\mongodb\\src\\cmap\\auth\\mongodb_oidc\\command_builders.ts"], "sourcesContent": ["import { Binary, BSON, type Document } from '../../../bson';\nimport { type MongoCredentials } from '../mongo_credentials';\nimport { AuthMechanism } from '../providers';\n\n/** @internal */\nexport interface OIDCCommand {\n  saslStart?: number;\n  saslContinue?: number;\n  conversationId?: number;\n  mechanism?: string;\n  autoAuthorize?: number;\n  db?: string;\n  payload: Binary;\n}\n\n/**\n * Generate the finishing command document for authentication. Will be a\n * saslStart or saslContinue depending on the presence of a conversation id.\n */\nexport function finishCommandDocument(token: string, conversationId?: number): OIDCCommand {\n  if (conversationId != null) {\n    return {\n      saslContinue: 1,\n      conversationId: conversationId,\n      payload: new Binary(BSON.serialize({ jwt: token }))\n    };\n  }\n  // saslContinue requires a conversationId in the command to be valid so in this\n  // case the server allows \"step two\" to actually be a saslStart with the token\n  // as the jwt since the use of the cached value has no correlating conversating\n  // on the particular connection.\n  return {\n    saslStart: 1,\n    mechanism: AuthMechanism.MONGODB_OIDC,\n    payload: new Binary(BSON.serialize({ jwt: token }))\n  };\n}\n\n/**\n * Generate the saslStart command document.\n */\nexport function startCommandDocument(credentials: MongoCredentials): OIDCCommand {\n  const payload: Document = {};\n  if (credentials.username) {\n    payload.n = credentials.username;\n  }\n  return {\n    saslStart: 1,\n    autoAuthorize: 1,\n    mechanism: AuthMechanism.MONGODB_OIDC,\n    payload: new Binary(BSON.serialize(payload))\n  };\n}\n"], "mappings": ";;;;;AAmBAA,OAAA,CAAAC,qBAAA,GAAAA,qBAAA;AAsBAD,OAAA,CAAAE,oBAAA,GAAAA,oBAAA;AAzCA,MAAAC,MAAA,GAAAC,OAAA;AAEA,MAAAC,WAAA,GAAAD,OAAA;AAaA;;;;AAIA,SAAgBH,qBAAqBA,CAACK,KAAa,EAAEC,cAAuB;EAC1E,IAAIA,cAAc,IAAI,IAAI,EAAE;IAC1B,OAAO;MACLC,YAAY,EAAE,CAAC;MACfD,cAAc,EAAEA,cAAc;MAC9BE,OAAO,EAAE,IAAIN,MAAA,CAAAO,MAAM,CAACP,MAAA,CAAAQ,IAAI,CAACC,SAAS,CAAC;QAAEC,GAAG,EAAEP;MAAK,CAAE,CAAC;KACnD;EACH;EACA;EACA;EACA;EACA;EACA,OAAO;IACLQ,SAAS,EAAE,CAAC;IACZC,SAAS,EAAEV,WAAA,CAAAW,aAAa,CAACC,YAAY;IACrCR,OAAO,EAAE,IAAIN,MAAA,CAAAO,MAAM,CAACP,MAAA,CAAAQ,IAAI,CAACC,SAAS,CAAC;MAAEC,GAAG,EAAEP;IAAK,CAAE,CAAC;GACnD;AACH;AAEA;;;AAGA,SAAgBJ,oBAAoBA,CAACgB,WAA6B;EAChE,MAAMT,OAAO,GAAa,EAAE;EAC5B,IAAIS,WAAW,CAACC,QAAQ,EAAE;IACxBV,OAAO,CAACW,CAAC,GAAGF,WAAW,CAACC,QAAQ;EAClC;EACA,OAAO;IACLL,SAAS,EAAE,CAAC;IACZO,aAAa,EAAE,CAAC;IAChBN,SAAS,EAAEV,WAAA,CAAAW,aAAa,CAACC,YAAY;IACrCR,OAAO,EAAE,IAAIN,MAAA,CAAAO,MAAM,CAACP,MAAA,CAAAQ,IAAI,CAACC,SAAS,CAACH,OAAO,CAAC;GAC5C;AACH", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}