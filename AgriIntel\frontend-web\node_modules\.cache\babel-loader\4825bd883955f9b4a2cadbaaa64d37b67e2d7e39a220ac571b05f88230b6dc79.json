{"ast": null, "code": "import _objectSpread from\"C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/@babel/runtime/helpers/esm/objectSpread2.js\";/**\n * Standardized CRUD Component\n * \n * This component provides a consistent interface for CRUD operations\n * across all modules in the application.\n */import React,{useState,useEffect}from'react';import{CustomButton}from'../common';import{Box,Paper,Typography,CircularProgress,Alert,Dialog,DialogTitle,DialogContent,DialogActions,IconButton,useTheme,alpha}from'@mui/material';import{Add,Close,Delete,Refresh}from'../../utils/iconImports';import{useTranslation}from'../../hooks/useTranslation';import{useSnackbar}from'../../contexts/SnackbarContext';import{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";function CrudComponent(_ref){let{title,subtitle,endpoint,fetchItems,createItem,updateItem,deleteItem,renderList,renderForm,validateForm,initialFormData,itemName,disableAdd=false,disableEdit=false,disableDelete=false,onRefreshData}=_ref;const[items,setItems]=useState([]);const[loading,setLoading]=useState(false);const[error,setError]=useState(null);const[formOpen,setFormOpen]=useState(false);const[formData,setFormData]=useState(initialFormData);const[editingItem,setEditingItem]=useState(null);const[formErrors,setFormErrors]=useState({});const[deleteConfirmOpen,setDeleteConfirmOpen]=useState(false);const[itemToDelete,setItemToDelete]=useState(null);const{translate}=useTranslation();const{showSnackbar}=useSnackbar();const theme=useTheme();// Load data on component mount\nuseEffect(()=>{loadData();},[]);// Load data from API\nconst loadData=async()=>{setLoading(true);setError(null);try{const data=await fetchItems();setItems(data);}catch(err){console.error(\"Error loading \".concat(itemName,\" data:\"),err);setError(\"Failed to load \".concat(itemName,\" data. Please try again.\"));}finally{setLoading(false);}};// Handle form field changes\nconst handleChange=(field,value)=>{setFormData(prev=>_objectSpread(_objectSpread({},prev),{},{[field]:value}));// Clear error for this field if it exists\nif(formErrors[field]){setFormErrors(prev=>{const newErrors=_objectSpread({},prev);delete newErrors[field];return newErrors;});}};// Open form for creating new item\nconst handleAddNew=()=>{setFormData(initialFormData);setEditingItem(null);setFormErrors({});setFormOpen(true);};// Open form for editing existing item\nconst handleEdit=item=>{setFormData(item);setEditingItem(item);setFormErrors({});setFormOpen(true);};// Open delete confirmation dialog\nconst handleDeleteClick=id=>{setItemToDelete(id);setDeleteConfirmOpen(true);};// Validate form and submit\nconst handleSubmit=async()=>{// Validate form if validation function is provided\nif(validateForm){const errors=validateForm(formData);if(Object.keys(errors).length>0){setFormErrors(errors);return;}}setLoading(true);try{if(editingItem&&updateItem){// Update existing item\nconst updated=await updateItem(editingItem.id,formData);setItems(prev=>prev.map(item=>item.id===editingItem.id?updated:item));showSnackbar(\"\".concat(itemName,\" updated successfully\"),'success');}else if(createItem){// Create new item\nconst created=await createItem(formData);setItems(prev=>[...prev,created]);showSnackbar(\"\".concat(itemName,\" created successfully\"),'success');}setFormOpen(false);}catch(err){console.error(\"Error saving \".concat(itemName,\":\"),err);showSnackbar(\"Failed to save \".concat(itemName),'error');}finally{setLoading(false);}};// Confirm and execute delete\nconst handleConfirmDelete=async()=>{if(!itemToDelete||!deleteItem)return;setLoading(true);try{await deleteItem(itemToDelete);setItems(prev=>prev.filter(item=>item.id!==itemToDelete));showSnackbar(\"\".concat(itemName,\" deleted successfully\"),'success');}catch(err){console.error(\"Error deleting \".concat(itemName,\":\"),err);showSnackbar(\"Failed to delete \".concat(itemName),'error');}finally{setLoading(false);setDeleteConfirmOpen(false);setItemToDelete(null);}};// Handle refresh button click\nconst handleRefresh=()=>{if(onRefreshData){onRefreshData();}else{loadData();}};return/*#__PURE__*/_jsxs(Box,{sx:{width:'100%'},children:[/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',justifyContent:'space-between',alignItems:'center',mb:3,px:3},children:[/*#__PURE__*/_jsxs(Box,{children:[/*#__PURE__*/_jsx(Typography,{variant:\"h5\",component:\"h1\",gutterBottom:true,children:title}),subtitle&&/*#__PURE__*/_jsx(Typography,{variant:\"subtitle1\",color:\"text.secondary\",children:subtitle})]}),/*#__PURE__*/_jsxs(Box,{sx:{display:'flex',gap:2},children:[/*#__PURE__*/_jsx(CustomButton,{variant:\"outlined\",startIcon:/*#__PURE__*/_jsx(Refresh,{}),onClick:handleRefresh,disabled:loading,children:translate?translate('common.refresh',{fallback:'Refresh'}):'Refresh'}),!disableAdd&&/*#__PURE__*/_jsxs(CustomButton,{variant:\"contained\",startIcon:/*#__PURE__*/_jsx(Add,{}),onClick:handleAddNew,disabled:loading,children:[translate?translate('common.add',{fallback:'Add'}):'Add',\" \",itemName]})]})]}),error&&/*#__PURE__*/_jsx(Alert,{severity:\"error\",sx:{mb:3},children:error}),loading&&/*#__PURE__*/_jsx(Box,{sx:{display:'flex',justifyContent:'center',my:4},children:/*#__PURE__*/_jsx(CircularProgress,{})}),!loading&&items.length===0?/*#__PURE__*/_jsxs(Paper,{sx:{p:4,textAlign:'center',bgcolor:alpha(theme.palette.background.paper,0.8),borderRadius:2},children:[/*#__PURE__*/_jsx(Typography,{variant:\"h6\",children:translate?translate('common.no_items',{fallback:'No items found'}):'No items found'}),!disableAdd&&/*#__PURE__*/_jsxs(CustomButton,{variant:\"contained\",startIcon:/*#__PURE__*/_jsx(Add,{}),onClick:handleAddNew,sx:{mt:2},children:[translate?translate('common.add_first',{fallback:'Add your first'}):'Add your first',\" \",itemName]})]}):/*#__PURE__*/_jsx(Box,{sx:{mb:4},children:renderList(items,handleEdit,handleDeleteClick)}),/*#__PURE__*/_jsxs(Dialog,{open:formOpen,onClose:()=>setFormOpen(false),maxWidth:\"md\",fullWidth:true,children:[/*#__PURE__*/_jsxs(DialogTitle,{children:[editingItem?\"\".concat(translate?translate('common.edit',{fallback:'Edit'}):'Edit',\" \").concat(itemName):\"\".concat(translate?translate('common.add',{fallback:'Add'}):'Add',\" \").concat(itemName),/*#__PURE__*/_jsx(IconButton,{\"aria-label\":\"close\",onClick:()=>setFormOpen(false),sx:{position:'absolute',right:8,top:8},children:/*#__PURE__*/_jsx(Close,{})})]}),/*#__PURE__*/_jsx(DialogContent,{dividers:true,children:renderForm(editingItem,handleChange,formData,formErrors)}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(CustomButton,{onClick:()=>setFormOpen(false),disabled:loading,children:translate?translate('common.cancel',{fallback:'Cancel'}):'Cancel'}),/*#__PURE__*/_jsx(CustomButton,{onClick:handleSubmit,variant:\"contained\",color:\"primary\",disabled:loading,children:loading?translate?translate('common.saving',{fallback:'Saving...'}):'Saving...':translate?translate('common.save',{fallback:'Save'}):'Save'})]})]}),/*#__PURE__*/_jsxs(Dialog,{open:deleteConfirmOpen,onClose:()=>setDeleteConfirmOpen(false),children:[/*#__PURE__*/_jsx(DialogTitle,{children:translate?translate('common.confirm_delete',{fallback:'Confirm Delete'}):'Confirm Delete'}),/*#__PURE__*/_jsx(DialogContent,{children:/*#__PURE__*/_jsx(Typography,{children:translate?translate('common.delete_confirmation',{fallback:\"Are you sure you want to delete this \".concat(itemName,\"? This action cannot be undone.\"),itemName}):\"Are you sure you want to delete this \".concat(itemName,\"? This action cannot be undone.\")})}),/*#__PURE__*/_jsxs(DialogActions,{children:[/*#__PURE__*/_jsx(CustomButton,{onClick:()=>setDeleteConfirmOpen(false),disabled:loading,children:translate?translate('common.cancel',{fallback:'Cancel'}):'Cancel'}),/*#__PURE__*/_jsx(CustomButton,{onClick:handleConfirmDelete,color:\"error\",variant:\"contained\",disabled:loading,startIcon:/*#__PURE__*/_jsx(Delete,{}),children:loading?translate?translate('common.deleting',{fallback:'Deleting...'}):'Deleting...':translate?translate('common.delete',{fallback:'Delete'}):'Delete'})]})]})]});}export default CrudComponent;", "map": {"version": 3, "names": ["React", "useState", "useEffect", "CustomButton", "Box", "Paper", "Typography", "CircularProgress", "<PERSON><PERSON>", "Dialog", "DialogTitle", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "DialogActions", "IconButton", "useTheme", "alpha", "Add", "Close", "Delete", "Refresh", "useTranslation", "useSnackbar", "jsx", "_jsx", "jsxs", "_jsxs", "CrudComponent", "_ref", "title", "subtitle", "endpoint", "fetchItems", "createItem", "updateItem", "deleteItem", "renderList", "renderForm", "validateForm", "initialFormData", "itemName", "disableAdd", "disableEdit", "disableDelete", "onRefreshData", "items", "setItems", "loading", "setLoading", "error", "setError", "formOpen", "setFormOpen", "formData", "setFormData", "editingItem", "setEditingItem", "formErrors", "setFormErrors", "deleteConfirmOpen", "setDeleteConfirmOpen", "itemToDelete", "setItemToDelete", "translate", "showSnackbar", "theme", "loadData", "data", "err", "console", "concat", "handleChange", "field", "value", "prev", "_objectSpread", "newErrors", "handleAddNew", "handleEdit", "item", "handleDeleteClick", "id", "handleSubmit", "errors", "Object", "keys", "length", "updated", "map", "created", "handleConfirmDelete", "filter", "handleRefresh", "sx", "width", "children", "display", "justifyContent", "alignItems", "mb", "px", "variant", "component", "gutterBottom", "color", "gap", "startIcon", "onClick", "disabled", "fallback", "severity", "my", "p", "textAlign", "bgcolor", "palette", "background", "paper", "borderRadius", "mt", "open", "onClose", "max<PERSON><PERSON><PERSON>", "fullWidth", "position", "right", "top", "dividers"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/components/common/CrudComponent.tsx"], "sourcesContent": ["/**\n * Standardized CRUD Component\n * \n * This component provides a consistent interface for CRUD operations\n * across all modules in the application.\n */\n\nimport React, { useState, useEffect } from 'react';\nimport { CustomButton } from '../common';\nimport { Box, Paper, Typography, CircularProgress, Alert, Dialog, DialogTitle, DialogContent, DialogActions, IconButton, Snackbar, useTheme, alpha } from '@mui/material';\nimport { Add, Close, Delete, Edit, Refresh } from '../../utils/iconImports';\nimport { useTranslation } from '../../hooks/useTranslation';\nimport { useSnackbar } from '../../contexts/SnackbarContext';\n\ninterface CrudComponentProps<T> {\n  title: string;\n  subtitle?: string;\n  endpoint: string;\n  fetchItems: () => Promise<T[]>;\n  createItem?: (data: Omit<T, 'id'>) => Promise<T>;\n  updateItem?: (id: string, data: Partial<T>) => Promise<T>;\n  deleteItem?: (id: string) => Promise<void>;\n  renderList: (items: T[], onEdit: (item: T) => void, onDelete: (id: string) => void) => React.ReactNode;\n  renderForm: (\n    item: Partial<T> | null,\n    onChange: (field: keyof T, value: any) => void,\n    formData: Partial<T>,\n    errors: Record<string, string>\n  ) => React.ReactNode;\n  validateForm?: (data: Partial<T>) => Record<string, string>;\n  initialFormData: Partial<T>;\n  itemName: string;\n  disableAdd?: boolean;\n  disableEdit?: boolean;\n  disableDelete?: boolean;\n  onRefreshData?: () => void;\n}\n\nfunction CrudComponent<T extends { id: string }>({\n  title,\n  subtitle,\n  endpoint,\n  fetchItems,\n  createItem,\n  updateItem,\n  deleteItem,\n  renderList,\n  renderForm,\n  validateForm,\n  initialFormData,\n  itemName,\n  disableAdd = false,\n  disableEdit = false,\n  disableDelete = false,\n  onRefreshData\n}: CrudComponentProps<T>) {\n  const [items, setItems] = useState<T[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [formOpen, setFormOpen] = useState(false);\n  const [formData, setFormData] = useState<Partial<T>>(initialFormData);\n  const [editingItem, setEditingItem] = useState<T | null>(null);\n  const [formErrors, setFormErrors] = useState<Record<string, string>>({});\n  const [deleteConfirmOpen, setDeleteConfirmOpen] = useState(false);\n  const [itemToDelete, setItemToDelete] = useState<string | null>(null);\n  const { translate } = useTranslation();\n  const { showSnackbar } = useSnackbar();\n  const theme = useTheme();\n\n  // Load data on component mount\n  useEffect(() => {\n    loadData();\n  }, []);\n\n  // Load data from API\n  const loadData = async () => {\n    setLoading(true);\n    setError(null);\n    try {\n      const data = await fetchItems();\n      setItems(data);\n    } catch (err) {\n      console.error(`Error loading ${itemName} data:`, err);\n      setError(`Failed to load ${itemName} data. Please try again.`);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Handle form field changes\n  const handleChange = (field: keyof T, value: any) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n    \n    // Clear error for this field if it exists\n    if (formErrors[field as string]) {\n      setFormErrors(prev => {\n        const newErrors = { ...prev };\n        delete newErrors[field as string];\n        return newErrors;\n      });\n    }\n  };\n\n  // Open form for creating new item\n  const handleAddNew = () => {\n    setFormData(initialFormData);\n    setEditingItem(null);\n    setFormErrors({});\n    setFormOpen(true);\n  };\n\n  // Open form for editing existing item\n  const handleEdit = (item: T) => {\n    setFormData(item);\n    setEditingItem(item);\n    setFormErrors({});\n    setFormOpen(true);\n  };\n\n  // Open delete confirmation dialog\n  const handleDeleteClick = (id: string) => {\n    setItemToDelete(id);\n    setDeleteConfirmOpen(true);\n  };\n\n  // Validate form and submit\n  const handleSubmit = async () => {\n    // Validate form if validation function is provided\n    if (validateForm) {\n      const errors = validateForm(formData);\n      if (Object.keys(errors).length > 0) {\n        setFormErrors(errors);\n        return;\n      }\n    }\n\n    setLoading(true);\n    try {\n      if (editingItem && updateItem) {\n        // Update existing item\n        const updated = await updateItem(editingItem.id, formData);\n        setItems(prev => prev.map(item => item.id === editingItem.id ? updated : item));\n        showSnackbar(`${itemName} updated successfully`, 'success');\n      } else if (createItem) {\n        // Create new item\n        const created = await createItem(formData as Omit<T, 'id'>);\n        setItems(prev => [...prev, created]);\n        showSnackbar(`${itemName} created successfully`, 'success');\n      }\n      setFormOpen(false);\n    } catch (err) {\n      console.error(`Error saving ${itemName}:`, err);\n      showSnackbar(`Failed to save ${itemName}`, 'error');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  // Confirm and execute delete\n  const handleConfirmDelete = async () => {\n    if (!itemToDelete || !deleteItem) return;\n    \n    setLoading(true);\n    try {\n      await deleteItem(itemToDelete);\n      setItems(prev => prev.filter(item => item.id !== itemToDelete));\n      showSnackbar(`${itemName} deleted successfully`, 'success');\n    } catch (err) {\n      console.error(`Error deleting ${itemName}:`, err);\n      showSnackbar(`Failed to delete ${itemName}`, 'error');\n    } finally {\n      setLoading(false);\n      setDeleteConfirmOpen(false);\n      setItemToDelete(null);\n    }\n  };\n\n  // Handle refresh button click\n  const handleRefresh = () => {\n    if (onRefreshData) {\n      onRefreshData();\n    } else {\n      loadData();\n    }\n  };\n\n  return (\n    <Box sx={{ width: '100%' }}>\n      {/* Header */}\n      <Box \n        sx={{ \n          display: 'flex', \n          justifyContent: 'space-between', \n          alignItems: 'center', \n          mb: 3,\n          px: 3\n        }}\n      >\n        <Box>\n          <Typography variant=\"h5\" component=\"h1\" gutterBottom>\n            {title}\n          </Typography>\n          {subtitle && (\n            <Typography variant=\"subtitle1\" color=\"text.secondary\">\n              {subtitle}\n            </Typography>\n          )}\n        </Box>\n        <Box sx={{ display: 'flex', gap: 2 }}>\n          <CustomButton \n            variant=\"outlined\"\n            startIcon={<Refresh />}\n            onClick={handleRefresh}\n            disabled={loading}\n          >\n            {translate ? translate('common.refresh', { fallback: 'Refresh' }) : 'Refresh'}\n          </CustomButton>\n          {!disableAdd && (\n            <CustomButton\n              variant=\"contained\"\n              startIcon={<Add />}\n              onClick={handleAddNew}\n              disabled={loading}\n            >\n              {translate ? translate('common.add', { fallback: 'Add' }) : 'Add'} {itemName}\n            </CustomButton>\n          )}\n        </Box>\n      </Box>\n\n      {/* Error message */}\n      {error && (\n        <Alert severity=\"error\" sx={{ mb: 3 }}>\n          {error}\n        </Alert>\n      )}\n\n      {/* Loading indicator */}\n      {loading && (\n        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>\n          <CircularProgress />\n        </Box>\n      )}\n\n      {/* List of items */}\n      {!loading && items.length === 0 ? (\n        <Paper \n          sx={{ \n            p: 4, \n            textAlign: 'center',\n            bgcolor: alpha(theme.palette.background.paper, 0.8),\n            borderRadius: 2\n          }}\n        >\n          <Typography variant=\"h6\">\n            {translate ? translate('common.no_items', { fallback: 'No items found' }) : 'No items found'}\n          </Typography>\n          {!disableAdd && (\n            <CustomButton\n              variant=\"contained\"\n              startIcon={<Add />}\n              onClick={handleAddNew}\n              sx={{ mt: 2 }}\n            >\n              {translate ? translate('common.add_first', { fallback: 'Add your first' }) : 'Add your first'} {itemName}\n            </CustomButton>\n          )}\n        </Paper>\n      ) : (\n        <Box sx={{ mb: 4 }}>\n          {renderList(items, handleEdit, handleDeleteClick)}\n        </Box>\n      )}\n\n      {/* Form dialog */}\n      <Dialog \n        open={formOpen} \n        onClose={() => setFormOpen(false)}\n        maxWidth=\"md\"\n        fullWidth\n      >\n        <DialogTitle>\n          {editingItem \n            ? `${translate ? translate('common.edit', { fallback: 'Edit' }) : 'Edit'} ${itemName}`\n            : `${translate ? translate('common.add', { fallback: 'Add' }) : 'Add'} ${itemName}`\n          }\n          <IconButton\n            aria-label=\"close\"\n            onClick={() => setFormOpen(false)}\n            sx={{ position: 'absolute', right: 8, top: 8 }}\n          >\n            <Close />\n          </IconButton>\n        </DialogTitle>\n        <DialogContent dividers>\n          {renderForm(editingItem, handleChange, formData, formErrors)}\n        </DialogContent>\n        <DialogActions>\n          <CustomButton onClick={() => setFormOpen(false)} disabled={loading}>\n            {translate ? translate('common.cancel', { fallback: 'Cancel' }) : 'Cancel'}\n          </CustomButton>\n          <CustomButton \n            onClick={handleSubmit} \n            variant=\"contained\" \n            color=\"primary\"\n            disabled={loading}\n          >\n            {loading \n              ? (translate ? translate('common.saving', { fallback: 'Saving...' }) : 'Saving...') \n              : (translate ? translate('common.save', { fallback: 'Save' }) : 'Save')\n            }\n          </CustomButton>\n        </DialogActions>\n      </Dialog>\n\n      {/* Delete confirmation dialog */}\n      <Dialog\n        open={deleteConfirmOpen}\n        onClose={() => setDeleteConfirmOpen(false)}\n      >\n        <DialogTitle>\n          {translate ? translate('common.confirm_delete', { fallback: 'Confirm Delete' }) : 'Confirm Delete'}\n        </DialogTitle>\n        <DialogContent>\n          <Typography>\n            {translate\n              ? translate('common.delete_confirmation', {\n                  fallback: `Are you sure you want to delete this ${itemName}? This action cannot be undone.`,\n                  itemName\n                })\n              : `Are you sure you want to delete this ${itemName}? This action cannot be undone.`\n            }\n          </Typography>\n        </DialogContent>\n        <DialogActions>\n          <CustomButton onClick={() => setDeleteConfirmOpen(false)} disabled={loading}>\n            {translate ? translate('common.cancel', { fallback: 'Cancel' }) : 'Cancel'}\n          </CustomButton>\n          <CustomButton \n            onClick={handleConfirmDelete} \n            color=\"error\" \n            variant=\"contained\"\n            disabled={loading}\n            startIcon={<Delete />}\n          >\n            {loading \n              ? (translate ? translate('common.deleting', { fallback: 'Deleting...' }) : 'Deleting...') \n              : (translate ? translate('common.delete', { fallback: 'Delete' }) : 'Delete')\n            }\n          </CustomButton>\n        </DialogActions>\n      </Dialog>\n    </Box>\n  );\n}\n\nexport default CrudComponent;\n"], "mappings": "gJAAA;AACA;AACA;AACA;AACA;AACA,GAEA,MAAO,CAAAA,KAAK,EAAIC,QAAQ,CAAEC,SAAS,KAAQ,OAAO,CAClD,OAASC,YAAY,KAAQ,WAAW,CACxC,OAASC,GAAG,CAAEC,KAAK,CAAEC,UAAU,CAAEC,gBAAgB,CAAEC,KAAK,CAAEC,MAAM,CAAEC,WAAW,CAAEC,aAAa,CAAEC,aAAa,CAAEC,UAAU,CAAYC,QAAQ,CAAEC,KAAK,KAAQ,eAAe,CACzK,OAASC,GAAG,CAAEC,KAAK,CAAEC,MAAM,CAAQC,OAAO,KAAQ,yBAAyB,CAC3E,OAASC,cAAc,KAAQ,4BAA4B,CAC3D,OAASC,WAAW,KAAQ,gCAAgC,CAAC,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBA0B7D,QAAS,CAAAC,aAAaA,CAAAC,IAAA,CAiBI,IAjBuB,CAC/CC,KAAK,CACLC,QAAQ,CACRC,QAAQ,CACRC,UAAU,CACVC,UAAU,CACVC,UAAU,CACVC,UAAU,CACVC,UAAU,CACVC,UAAU,CACVC,YAAY,CACZC,eAAe,CACfC,QAAQ,CACRC,UAAU,CAAG,KAAK,CAClBC,WAAW,CAAG,KAAK,CACnBC,aAAa,CAAG,KAAK,CACrBC,aACqB,CAAC,CAAAhB,IAAA,CACtB,KAAM,CAACiB,KAAK,CAAEC,QAAQ,CAAC,CAAG5C,QAAQ,CAAM,EAAE,CAAC,CAC3C,KAAM,CAAC6C,OAAO,CAAEC,UAAU,CAAC,CAAG9C,QAAQ,CAAC,KAAK,CAAC,CAC7C,KAAM,CAAC+C,KAAK,CAAEC,QAAQ,CAAC,CAAGhD,QAAQ,CAAgB,IAAI,CAAC,CACvD,KAAM,CAACiD,QAAQ,CAAEC,WAAW,CAAC,CAAGlD,QAAQ,CAAC,KAAK,CAAC,CAC/C,KAAM,CAACmD,QAAQ,CAAEC,WAAW,CAAC,CAAGpD,QAAQ,CAAaqC,eAAe,CAAC,CACrE,KAAM,CAACgB,WAAW,CAAEC,cAAc,CAAC,CAAGtD,QAAQ,CAAW,IAAI,CAAC,CAC9D,KAAM,CAACuD,UAAU,CAAEC,aAAa,CAAC,CAAGxD,QAAQ,CAAyB,CAAC,CAAC,CAAC,CACxE,KAAM,CAACyD,iBAAiB,CAAEC,oBAAoB,CAAC,CAAG1D,QAAQ,CAAC,KAAK,CAAC,CACjE,KAAM,CAAC2D,YAAY,CAAEC,eAAe,CAAC,CAAG5D,QAAQ,CAAgB,IAAI,CAAC,CACrE,KAAM,CAAE6D,SAAU,CAAC,CAAG1C,cAAc,CAAC,CAAC,CACtC,KAAM,CAAE2C,YAAa,CAAC,CAAG1C,WAAW,CAAC,CAAC,CACtC,KAAM,CAAA2C,KAAK,CAAGlD,QAAQ,CAAC,CAAC,CAExB;AACAZ,SAAS,CAAC,IAAM,CACd+D,QAAQ,CAAC,CAAC,CACZ,CAAC,CAAE,EAAE,CAAC,CAEN;AACA,KAAM,CAAAA,QAAQ,CAAG,KAAAA,CAAA,GAAY,CAC3BlB,UAAU,CAAC,IAAI,CAAC,CAChBE,QAAQ,CAAC,IAAI,CAAC,CACd,GAAI,CACF,KAAM,CAAAiB,IAAI,CAAG,KAAM,CAAAnC,UAAU,CAAC,CAAC,CAC/Bc,QAAQ,CAACqB,IAAI,CAAC,CAChB,CAAE,MAAOC,GAAG,CAAE,CACZC,OAAO,CAACpB,KAAK,kBAAAqB,MAAA,CAAkB9B,QAAQ,WAAU4B,GAAG,CAAC,CACrDlB,QAAQ,mBAAAoB,MAAA,CAAmB9B,QAAQ,4BAA0B,CAAC,CAChE,CAAC,OAAS,CACRQ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAAuB,YAAY,CAAGA,CAACC,KAAc,CAAEC,KAAU,GAAK,CACnDnB,WAAW,CAACoB,IAAI,EAAAC,aAAA,CAAAA,aAAA,IACXD,IAAI,MACP,CAACF,KAAK,EAAGC,KAAK,EACd,CAAC,CAEH;AACA,GAAIhB,UAAU,CAACe,KAAK,CAAW,CAAE,CAC/Bd,aAAa,CAACgB,IAAI,EAAI,CACpB,KAAM,CAAAE,SAAS,CAAAD,aAAA,IAAQD,IAAI,CAAE,CAC7B,MAAO,CAAAE,SAAS,CAACJ,KAAK,CAAW,CACjC,MAAO,CAAAI,SAAS,CAClB,CAAC,CAAC,CACJ,CACF,CAAC,CAED;AACA,KAAM,CAAAC,YAAY,CAAGA,CAAA,GAAM,CACzBvB,WAAW,CAACf,eAAe,CAAC,CAC5BiB,cAAc,CAAC,IAAI,CAAC,CACpBE,aAAa,CAAC,CAAC,CAAC,CAAC,CACjBN,WAAW,CAAC,IAAI,CAAC,CACnB,CAAC,CAED;AACA,KAAM,CAAA0B,UAAU,CAAIC,IAAO,EAAK,CAC9BzB,WAAW,CAACyB,IAAI,CAAC,CACjBvB,cAAc,CAACuB,IAAI,CAAC,CACpBrB,aAAa,CAAC,CAAC,CAAC,CAAC,CACjBN,WAAW,CAAC,IAAI,CAAC,CACnB,CAAC,CAED;AACA,KAAM,CAAA4B,iBAAiB,CAAIC,EAAU,EAAK,CACxCnB,eAAe,CAACmB,EAAE,CAAC,CACnBrB,oBAAoB,CAAC,IAAI,CAAC,CAC5B,CAAC,CAED;AACA,KAAM,CAAAsB,YAAY,CAAG,KAAAA,CAAA,GAAY,CAC/B;AACA,GAAI5C,YAAY,CAAE,CAChB,KAAM,CAAA6C,MAAM,CAAG7C,YAAY,CAACe,QAAQ,CAAC,CACrC,GAAI+B,MAAM,CAACC,IAAI,CAACF,MAAM,CAAC,CAACG,MAAM,CAAG,CAAC,CAAE,CAClC5B,aAAa,CAACyB,MAAM,CAAC,CACrB,OACF,CACF,CAEAnC,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,GAAIO,WAAW,EAAIrB,UAAU,CAAE,CAC7B;AACA,KAAM,CAAAqD,OAAO,CAAG,KAAM,CAAArD,UAAU,CAACqB,WAAW,CAAC0B,EAAE,CAAE5B,QAAQ,CAAC,CAC1DP,QAAQ,CAAC4B,IAAI,EAAIA,IAAI,CAACc,GAAG,CAACT,IAAI,EAAIA,IAAI,CAACE,EAAE,GAAK1B,WAAW,CAAC0B,EAAE,CAAGM,OAAO,CAAGR,IAAI,CAAC,CAAC,CAC/Ef,YAAY,IAAAM,MAAA,CAAI9B,QAAQ,0BAAyB,SAAS,CAAC,CAC7D,CAAC,IAAM,IAAIP,UAAU,CAAE,CACrB;AACA,KAAM,CAAAwD,OAAO,CAAG,KAAM,CAAAxD,UAAU,CAACoB,QAAyB,CAAC,CAC3DP,QAAQ,CAAC4B,IAAI,EAAI,CAAC,GAAGA,IAAI,CAAEe,OAAO,CAAC,CAAC,CACpCzB,YAAY,IAAAM,MAAA,CAAI9B,QAAQ,0BAAyB,SAAS,CAAC,CAC7D,CACAY,WAAW,CAAC,KAAK,CAAC,CACpB,CAAE,MAAOgB,GAAG,CAAE,CACZC,OAAO,CAACpB,KAAK,iBAAAqB,MAAA,CAAiB9B,QAAQ,MAAK4B,GAAG,CAAC,CAC/CJ,YAAY,mBAAAM,MAAA,CAAmB9B,QAAQ,EAAI,OAAO,CAAC,CACrD,CAAC,OAAS,CACRQ,UAAU,CAAC,KAAK,CAAC,CACnB,CACF,CAAC,CAED;AACA,KAAM,CAAA0C,mBAAmB,CAAG,KAAAA,CAAA,GAAY,CACtC,GAAI,CAAC7B,YAAY,EAAI,CAAC1B,UAAU,CAAE,OAElCa,UAAU,CAAC,IAAI,CAAC,CAChB,GAAI,CACF,KAAM,CAAAb,UAAU,CAAC0B,YAAY,CAAC,CAC9Bf,QAAQ,CAAC4B,IAAI,EAAIA,IAAI,CAACiB,MAAM,CAACZ,IAAI,EAAIA,IAAI,CAACE,EAAE,GAAKpB,YAAY,CAAC,CAAC,CAC/DG,YAAY,IAAAM,MAAA,CAAI9B,QAAQ,0BAAyB,SAAS,CAAC,CAC7D,CAAE,MAAO4B,GAAG,CAAE,CACZC,OAAO,CAACpB,KAAK,mBAAAqB,MAAA,CAAmB9B,QAAQ,MAAK4B,GAAG,CAAC,CACjDJ,YAAY,qBAAAM,MAAA,CAAqB9B,QAAQ,EAAI,OAAO,CAAC,CACvD,CAAC,OAAS,CACRQ,UAAU,CAAC,KAAK,CAAC,CACjBY,oBAAoB,CAAC,KAAK,CAAC,CAC3BE,eAAe,CAAC,IAAI,CAAC,CACvB,CACF,CAAC,CAED;AACA,KAAM,CAAA8B,aAAa,CAAGA,CAAA,GAAM,CAC1B,GAAIhD,aAAa,CAAE,CACjBA,aAAa,CAAC,CAAC,CACjB,CAAC,IAAM,CACLsB,QAAQ,CAAC,CAAC,CACZ,CACF,CAAC,CAED,mBACExC,KAAA,CAACrB,GAAG,EAACwF,EAAE,CAAE,CAAEC,KAAK,CAAE,MAAO,CAAE,CAAAC,QAAA,eAEzBrE,KAAA,CAACrB,GAAG,EACFwF,EAAE,CAAE,CACFG,OAAO,CAAE,MAAM,CACfC,cAAc,CAAE,eAAe,CAC/BC,UAAU,CAAE,QAAQ,CACpBC,EAAE,CAAE,CAAC,CACLC,EAAE,CAAE,CACN,CAAE,CAAAL,QAAA,eAEFrE,KAAA,CAACrB,GAAG,EAAA0F,QAAA,eACFvE,IAAA,CAACjB,UAAU,EAAC8F,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,IAAI,CAACC,YAAY,MAAAR,QAAA,CACjDlE,KAAK,CACI,CAAC,CACZC,QAAQ,eACPN,IAAA,CAACjB,UAAU,EAAC8F,OAAO,CAAC,WAAW,CAACG,KAAK,CAAC,gBAAgB,CAAAT,QAAA,CACnDjE,QAAQ,CACC,CACb,EACE,CAAC,cACNJ,KAAA,CAACrB,GAAG,EAACwF,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAES,GAAG,CAAE,CAAE,CAAE,CAAAV,QAAA,eACnCvE,IAAA,CAACpB,YAAY,EACXiG,OAAO,CAAC,UAAU,CAClBK,SAAS,cAAElF,IAAA,CAACJ,OAAO,GAAE,CAAE,CACvBuF,OAAO,CAAEf,aAAc,CACvBgB,QAAQ,CAAE7D,OAAQ,CAAAgD,QAAA,CAEjBhC,SAAS,CAAGA,SAAS,CAAC,gBAAgB,CAAE,CAAE8C,QAAQ,CAAE,SAAU,CAAC,CAAC,CAAG,SAAS,CACjE,CAAC,CACd,CAACpE,UAAU,eACVf,KAAA,CAACtB,YAAY,EACXiG,OAAO,CAAC,WAAW,CACnBK,SAAS,cAAElF,IAAA,CAACP,GAAG,GAAE,CAAE,CACnB0F,OAAO,CAAE9B,YAAa,CACtB+B,QAAQ,CAAE7D,OAAQ,CAAAgD,QAAA,EAEjBhC,SAAS,CAAGA,SAAS,CAAC,YAAY,CAAE,CAAE8C,QAAQ,CAAE,KAAM,CAAC,CAAC,CAAG,KAAK,CAAC,GAAC,CAACrE,QAAQ,EAChE,CACf,EACE,CAAC,EACH,CAAC,CAGLS,KAAK,eACJzB,IAAA,CAACf,KAAK,EAACqG,QAAQ,CAAC,OAAO,CAACjB,EAAE,CAAE,CAAEM,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CACnC9C,KAAK,CACD,CACR,CAGAF,OAAO,eACNvB,IAAA,CAACnB,GAAG,EAACwF,EAAE,CAAE,CAAEG,OAAO,CAAE,MAAM,CAAEC,cAAc,CAAE,QAAQ,CAAEc,EAAE,CAAE,CAAE,CAAE,CAAAhB,QAAA,cAC5DvE,IAAA,CAAChB,gBAAgB,GAAE,CAAC,CACjB,CACN,CAGA,CAACuC,OAAO,EAAIF,KAAK,CAACyC,MAAM,GAAK,CAAC,cAC7B5D,KAAA,CAACpB,KAAK,EACJuF,EAAE,CAAE,CACFmB,CAAC,CAAE,CAAC,CACJC,SAAS,CAAE,QAAQ,CACnBC,OAAO,CAAElG,KAAK,CAACiD,KAAK,CAACkD,OAAO,CAACC,UAAU,CAACC,KAAK,CAAE,GAAG,CAAC,CACnDC,YAAY,CAAE,CAChB,CAAE,CAAAvB,QAAA,eAEFvE,IAAA,CAACjB,UAAU,EAAC8F,OAAO,CAAC,IAAI,CAAAN,QAAA,CACrBhC,SAAS,CAAGA,SAAS,CAAC,iBAAiB,CAAE,CAAE8C,QAAQ,CAAE,gBAAiB,CAAC,CAAC,CAAG,gBAAgB,CAClF,CAAC,CACZ,CAACpE,UAAU,eACVf,KAAA,CAACtB,YAAY,EACXiG,OAAO,CAAC,WAAW,CACnBK,SAAS,cAAElF,IAAA,CAACP,GAAG,GAAE,CAAE,CACnB0F,OAAO,CAAE9B,YAAa,CACtBgB,EAAE,CAAE,CAAE0B,EAAE,CAAE,CAAE,CAAE,CAAAxB,QAAA,EAEbhC,SAAS,CAAGA,SAAS,CAAC,kBAAkB,CAAE,CAAE8C,QAAQ,CAAE,gBAAiB,CAAC,CAAC,CAAG,gBAAgB,CAAC,GAAC,CAACrE,QAAQ,EAC5F,CACf,EACI,CAAC,cAERhB,IAAA,CAACnB,GAAG,EAACwF,EAAE,CAAE,CAAEM,EAAE,CAAE,CAAE,CAAE,CAAAJ,QAAA,CAChB3D,UAAU,CAACS,KAAK,CAAEiC,UAAU,CAAEE,iBAAiB,CAAC,CAC9C,CACN,cAGDtD,KAAA,CAAChB,MAAM,EACL8G,IAAI,CAAErE,QAAS,CACfsE,OAAO,CAAEA,CAAA,GAAMrE,WAAW,CAAC,KAAK,CAAE,CAClCsE,QAAQ,CAAC,IAAI,CACbC,SAAS,MAAA5B,QAAA,eAETrE,KAAA,CAACf,WAAW,EAAAoF,QAAA,EACTxC,WAAW,IAAAe,MAAA,CACLP,SAAS,CAAGA,SAAS,CAAC,aAAa,CAAE,CAAE8C,QAAQ,CAAE,MAAO,CAAC,CAAC,CAAG,MAAM,MAAAvC,MAAA,CAAI9B,QAAQ,KAAA8B,MAAA,CAC/EP,SAAS,CAAGA,SAAS,CAAC,YAAY,CAAE,CAAE8C,QAAQ,CAAE,KAAM,CAAC,CAAC,CAAG,KAAK,MAAAvC,MAAA,CAAI9B,QAAQ,CAAE,cAErFhB,IAAA,CAACV,UAAU,EACT,aAAW,OAAO,CAClB6F,OAAO,CAAEA,CAAA,GAAMvD,WAAW,CAAC,KAAK,CAAE,CAClCyC,EAAE,CAAE,CAAE+B,QAAQ,CAAE,UAAU,CAAEC,KAAK,CAAE,CAAC,CAAEC,GAAG,CAAE,CAAE,CAAE,CAAA/B,QAAA,cAE/CvE,IAAA,CAACN,KAAK,GAAE,CAAC,CACC,CAAC,EACF,CAAC,cACdM,IAAA,CAACZ,aAAa,EAACmH,QAAQ,MAAAhC,QAAA,CACpB1D,UAAU,CAACkB,WAAW,CAAEgB,YAAY,CAAElB,QAAQ,CAAEI,UAAU,CAAC,CAC/C,CAAC,cAChB/B,KAAA,CAACb,aAAa,EAAAkF,QAAA,eACZvE,IAAA,CAACpB,YAAY,EAACuG,OAAO,CAAEA,CAAA,GAAMvD,WAAW,CAAC,KAAK,CAAE,CAACwD,QAAQ,CAAE7D,OAAQ,CAAAgD,QAAA,CAChEhC,SAAS,CAAGA,SAAS,CAAC,eAAe,CAAE,CAAE8C,QAAQ,CAAE,QAAS,CAAC,CAAC,CAAG,QAAQ,CAC9D,CAAC,cACfrF,IAAA,CAACpB,YAAY,EACXuG,OAAO,CAAEzB,YAAa,CACtBmB,OAAO,CAAC,WAAW,CACnBG,KAAK,CAAC,SAAS,CACfI,QAAQ,CAAE7D,OAAQ,CAAAgD,QAAA,CAEjBhD,OAAO,CACHgB,SAAS,CAAGA,SAAS,CAAC,eAAe,CAAE,CAAE8C,QAAQ,CAAE,WAAY,CAAC,CAAC,CAAG,WAAW,CAC/E9C,SAAS,CAAGA,SAAS,CAAC,aAAa,CAAE,CAAE8C,QAAQ,CAAE,MAAO,CAAC,CAAC,CAAG,MAAO,CAE7D,CAAC,EACF,CAAC,EACV,CAAC,cAGTnF,KAAA,CAAChB,MAAM,EACL8G,IAAI,CAAE7D,iBAAkB,CACxB8D,OAAO,CAAEA,CAAA,GAAM7D,oBAAoB,CAAC,KAAK,CAAE,CAAAmC,QAAA,eAE3CvE,IAAA,CAACb,WAAW,EAAAoF,QAAA,CACThC,SAAS,CAAGA,SAAS,CAAC,uBAAuB,CAAE,CAAE8C,QAAQ,CAAE,gBAAiB,CAAC,CAAC,CAAG,gBAAgB,CACvF,CAAC,cACdrF,IAAA,CAACZ,aAAa,EAAAmF,QAAA,cACZvE,IAAA,CAACjB,UAAU,EAAAwF,QAAA,CACRhC,SAAS,CACNA,SAAS,CAAC,4BAA4B,CAAE,CACtC8C,QAAQ,yCAAAvC,MAAA,CAA0C9B,QAAQ,mCAAiC,CAC3FA,QACF,CAAC,CAAC,yCAAA8B,MAAA,CACsC9B,QAAQ,mCAAiC,CAE3E,CAAC,CACA,CAAC,cAChBd,KAAA,CAACb,aAAa,EAAAkF,QAAA,eACZvE,IAAA,CAACpB,YAAY,EAACuG,OAAO,CAAEA,CAAA,GAAM/C,oBAAoB,CAAC,KAAK,CAAE,CAACgD,QAAQ,CAAE7D,OAAQ,CAAAgD,QAAA,CACzEhC,SAAS,CAAGA,SAAS,CAAC,eAAe,CAAE,CAAE8C,QAAQ,CAAE,QAAS,CAAC,CAAC,CAAG,QAAQ,CAC9D,CAAC,cACfrF,IAAA,CAACpB,YAAY,EACXuG,OAAO,CAAEjB,mBAAoB,CAC7Bc,KAAK,CAAC,OAAO,CACbH,OAAO,CAAC,WAAW,CACnBO,QAAQ,CAAE7D,OAAQ,CAClB2D,SAAS,cAAElF,IAAA,CAACL,MAAM,GAAE,CAAE,CAAA4E,QAAA,CAErBhD,OAAO,CACHgB,SAAS,CAAGA,SAAS,CAAC,iBAAiB,CAAE,CAAE8C,QAAQ,CAAE,aAAc,CAAC,CAAC,CAAG,aAAa,CACrF9C,SAAS,CAAGA,SAAS,CAAC,eAAe,CAAE,CAAE8C,QAAQ,CAAE,QAAS,CAAC,CAAC,CAAG,QAAS,CAEnE,CAAC,EACF,CAAC,EACV,CAAC,EACN,CAAC,CAEV,CAEA,cAAe,CAAAlF,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}