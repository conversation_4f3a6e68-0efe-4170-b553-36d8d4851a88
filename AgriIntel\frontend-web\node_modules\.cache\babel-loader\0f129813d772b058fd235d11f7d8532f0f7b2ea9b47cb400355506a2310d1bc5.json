{"ast": null, "code": "import React from 'react';\nimport { hydrate } from '../core';\nimport { useQueryClient } from './QueryClientProvider';\nexport function useHydrate(state, options) {\n  var queryClient = useQueryClient();\n  var optionsRef = React.useRef(options);\n  optionsRef.current = options; // Running hydrate again with the same queries is safe,\n  // it wont overwrite or initialize existing queries,\n  // relying on useMemo here is only a performance optimization.\n  // hydrate can and should be run *during* render here for SSR to work properly\n\n  React.useMemo(function () {\n    if (state) {\n      hydrate(queryClient, state, optionsRef.current);\n    }\n  }, [queryClient, state]);\n}\nexport var Hydrate = function Hydrate(_ref) {\n  var children = _ref.children,\n    options = _ref.options,\n    state = _ref.state;\n  useHydrate(state, options);\n  return children;\n};", "map": {"version": 3, "names": ["React", "hydrate", "useQueryClient", "useHydrate", "state", "options", "queryClient", "optionsRef", "useRef", "current", "useMemo", "Hydrate", "_ref", "children"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/react-query/es/react/Hydrate.js"], "sourcesContent": ["import React from 'react';\nimport { hydrate } from '../core';\nimport { useQueryClient } from './QueryClientProvider';\nexport function useHydrate(state, options) {\n  var queryClient = useQueryClient();\n  var optionsRef = React.useRef(options);\n  optionsRef.current = options; // Running hydrate again with the same queries is safe,\n  // it wont overwrite or initialize existing queries,\n  // relying on useMemo here is only a performance optimization.\n  // hydrate can and should be run *during* render here for SSR to work properly\n\n  React.useMemo(function () {\n    if (state) {\n      hydrate(queryClient, state, optionsRef.current);\n    }\n  }, [queryClient, state]);\n}\nexport var Hydrate = function Hydrate(_ref) {\n  var children = _ref.children,\n      options = _ref.options,\n      state = _ref.state;\n  useHydrate(state, options);\n  return children;\n};"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,OAAO,QAAQ,SAAS;AACjC,SAASC,cAAc,QAAQ,uBAAuB;AACtD,OAAO,SAASC,UAAUA,CAACC,KAAK,EAAEC,OAAO,EAAE;EACzC,IAAIC,WAAW,GAAGJ,cAAc,CAAC,CAAC;EAClC,IAAIK,UAAU,GAAGP,KAAK,CAACQ,MAAM,CAACH,OAAO,CAAC;EACtCE,UAAU,CAACE,OAAO,GAAGJ,OAAO,CAAC,CAAC;EAC9B;EACA;EACA;;EAEAL,KAAK,CAACU,OAAO,CAAC,YAAY;IACxB,IAAIN,KAAK,EAAE;MACTH,OAAO,CAACK,WAAW,EAAEF,KAAK,EAAEG,UAAU,CAACE,OAAO,CAAC;IACjD;EACF,CAAC,EAAE,CAACH,WAAW,EAAEF,KAAK,CAAC,CAAC;AAC1B;AACA,OAAO,IAAIO,OAAO,GAAG,SAASA,OAAOA,CAACC,IAAI,EAAE;EAC1C,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;IACxBR,OAAO,GAAGO,IAAI,CAACP,OAAO;IACtBD,KAAK,GAAGQ,IAAI,CAACR,KAAK;EACtBD,UAAU,CAACC,KAAK,EAAEC,OAAO,CAAC;EAC1B,OAAOQ,QAAQ;AACjB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}