{"ast": null, "code": "import startOfDay from \"../startOfDay/index.js\";\n/**\n * @name startOfToday\n * @category Day Helpers\n * @summary Return the start of today.\n * @pure false\n *\n * @description\n * Return the start of today.\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @returns {Date} the start of today\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfToday()\n * //=> Mon Oct 6 2014 00:00:00\n */\nexport default function startOfToday() {\n  return startOfDay(Date.now());\n}", "map": {"version": 3, "names": ["startOfDay", "startOfToday", "Date", "now"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/node_modules/date-fns/esm/startOfToday/index.js"], "sourcesContent": ["import startOfDay from \"../startOfDay/index.js\";\n/**\n * @name startOfToday\n * @category Day Helpers\n * @summary Return the start of today.\n * @pure false\n *\n * @description\n * Return the start of today.\n *\n * > ⚠️ Please note that this function is not present in the FP submodule as\n * > it uses `Date.now()` internally hence impure and can't be safely curried.\n *\n * @returns {Date} the start of today\n *\n * @example\n * // If today is 6 October 2014:\n * const result = startOfToday()\n * //=> Mon Oct 6 2014 00:00:00\n */\nexport default function startOfToday() {\n  return startOfDay(Date.now());\n}"], "mappings": "AAAA,OAAOA,UAAU,MAAM,wBAAwB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,eAAe,SAASC,YAAYA,CAAA,EAAG;EACrC,OAAOD,UAAU,CAACE,IAAI,CAACC,GAAG,CAAC,CAAC,CAAC;AAC/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}