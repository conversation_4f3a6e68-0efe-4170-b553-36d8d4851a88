{"ast": null, "code": "import React from'react';import ReactDOM from'react-dom/client';import{Provider}from'react-redux';// import { QueryClient, QueryClientProvider } from 'react-query';\n// import { ReactQueryDevtools } from 'react-query/devtools';\nimport{<PERSON><PERSON>erRouter}from'react-router-dom';import{ThemeProvider,createTheme}from'@mui/material/styles';import CssBaseline from'@mui/material/CssBaseline';import'./index.css';import'./i18n/i18n';import App from'./App';import{store}from'./store/store';// import reportWebVitals from './reportWebVitals';\n// Create React Query client\n// const queryClient = new QueryClient({\n//   defaultOptions: {\n//     queries: {\n//       retry: 1,\n//       refetchOnWindowFocus: false,\n//       staleTime: 5 * 60 * 1000, // 5 minutes\n//     },\n//   },\n// });\n// Create Material-UI theme\nimport{jsx as _jsx,jsxs as _jsxs}from\"react/jsx-runtime\";const theme=createTheme({palette:{primary:{main:'#0f766e',light:'#14b8a6',dark:'#134e4a'},secondary:{main:'#374151',light:'#6b7280',dark:'#1f2937'},success:{main:'#059669',light:'#10b981',dark:'#047857'},warning:{main:'#d97706',light:'#f59e0b',dark:'#b45309'},error:{main:'#dc2626',light:'#ef4444',dark:'#b91c1c'},background:{default:'#f9fafb',paper:'#ffffff'}},typography:{fontFamily:'\"Inter\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',h1:{fontWeight:700,fontSize:'2.5rem'},h2:{fontWeight:600,fontSize:'2rem'},h3:{fontWeight:600,fontSize:'1.75rem'},h4:{fontWeight:600,fontSize:'1.5rem'},h5:{fontWeight:600,fontSize:'1.25rem'},h6:{fontWeight:600,fontSize:'1rem'},body1:{fontSize:'1rem',lineHeight:1.5},body2:{fontSize:'0.875rem',lineHeight:1.43}},shape:{borderRadius:8},components:{MuiButton:{styleOverrides:{root:{textTransform:'none',fontWeight:500,borderRadius:8,padding:'8px 16px'}}},MuiCard:{styleOverrides:{root:{boxShadow:'0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',borderRadius:12}}},MuiTextField:{styleOverrides:{root:{'& .MuiOutlinedInput-root':{borderRadius:8}}}}}});const root=ReactDOM.createRoot(document.getElementById('root'));root.render(/*#__PURE__*/_jsx(React.StrictMode,{children:/*#__PURE__*/_jsx(Provider,{store:store,children:/*#__PURE__*/_jsxs(ThemeProvider,{theme:theme,children:[/*#__PURE__*/_jsx(CssBaseline,{}),/*#__PURE__*/_jsx(BrowserRouter,{children:/*#__PURE__*/_jsx(App,{})})]})})}));// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\n// reportWebVitals();", "map": {"version": 3, "names": ["React", "ReactDOM", "Provider", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ThemeProvider", "createTheme", "CssBaseline", "App", "store", "jsx", "_jsx", "jsxs", "_jsxs", "theme", "palette", "primary", "main", "light", "dark", "secondary", "success", "warning", "error", "background", "default", "paper", "typography", "fontFamily", "h1", "fontWeight", "fontSize", "h2", "h3", "h4", "h5", "h6", "body1", "lineHeight", "body2", "shape", "borderRadius", "components", "MuiB<PERSON>on", "styleOverrides", "root", "textTransform", "padding", "MuiCard", "boxShadow", "MuiTextField", "createRoot", "document", "getElementById", "render", "StrictMode", "children"], "sources": ["C:/Users/<USER>/OneDrive/Desktop/AgriIntel/frontend-web/src/index.tsx"], "sourcesContent": ["import React from 'react';\nimport ReactDOM from 'react-dom/client';\nimport { Provider } from 'react-redux';\n// import { QueryClient, QueryClientProvider } from 'react-query';\n// import { ReactQueryDevtools } from 'react-query/devtools';\nimport { <PERSON><PERSON>erRouter } from 'react-router-dom';\nimport { ThemeProvider, createTheme } from '@mui/material/styles';\nimport CssBaseline from '@mui/material/CssBaseline';\n\nimport './index.css';\nimport './i18n/i18n';\nimport App from './App';\nimport { store } from './store/store';\n// import reportWebVitals from './reportWebVitals';\n\n// Create React Query client\n// const queryClient = new QueryClient({\n//   defaultOptions: {\n//     queries: {\n//       retry: 1,\n//       refetchOnWindowFocus: false,\n//       staleTime: 5 * 60 * 1000, // 5 minutes\n//     },\n//   },\n// });\n\n// Create Material-UI theme\nconst theme = createTheme({\n  palette: {\n    primary: {\n      main: '#0f766e',\n      light: '#14b8a6',\n      dark: '#134e4a',\n    },\n    secondary: {\n      main: '#374151',\n      light: '#6b7280',\n      dark: '#1f2937',\n    },\n    success: {\n      main: '#059669',\n      light: '#10b981',\n      dark: '#047857',\n    },\n    warning: {\n      main: '#d97706',\n      light: '#f59e0b',\n      dark: '#b45309',\n    },\n    error: {\n      main: '#dc2626',\n      light: '#ef4444',\n      dark: '#b91c1c',\n    },\n    background: {\n      default: '#f9fafb',\n      paper: '#ffffff',\n    },\n  },\n  typography: {\n    fontFamily: '\"Inter\", \"Roboto\", \"Helvetica\", \"Arial\", sans-serif',\n    h1: {\n      fontWeight: 700,\n      fontSize: '2.5rem',\n    },\n    h2: {\n      fontWeight: 600,\n      fontSize: '2rem',\n    },\n    h3: {\n      fontWeight: 600,\n      fontSize: '1.75rem',\n    },\n    h4: {\n      fontWeight: 600,\n      fontSize: '1.5rem',\n    },\n    h5: {\n      fontWeight: 600,\n      fontSize: '1.25rem',\n    },\n    h6: {\n      fontWeight: 600,\n      fontSize: '1rem',\n    },\n    body1: {\n      fontSize: '1rem',\n      lineHeight: 1.5,\n    },\n    body2: {\n      fontSize: '0.875rem',\n      lineHeight: 1.43,\n    },\n  },\n  shape: {\n    borderRadius: 8,\n  },\n  components: {\n    MuiButton: {\n      styleOverrides: {\n        root: {\n          textTransform: 'none',\n          fontWeight: 500,\n          borderRadius: 8,\n          padding: '8px 16px',\n        },\n      },\n    },\n    MuiCard: {\n      styleOverrides: {\n        root: {\n          boxShadow: '0 2px 15px -3px rgba(0, 0, 0, 0.07), 0 10px 20px -2px rgba(0, 0, 0, 0.04)',\n          borderRadius: 12,\n        },\n      },\n    },\n    MuiTextField: {\n      styleOverrides: {\n        root: {\n          '& .MuiOutlinedInput-root': {\n            borderRadius: 8,\n          },\n        },\n      },\n    },\n  },\n});\n\nconst root = ReactDOM.createRoot(\n  document.getElementById('root') as HTMLElement\n);\n\nroot.render(\n  <React.StrictMode>\n    <Provider store={store}>\n      <ThemeProvider theme={theme}>\n        <CssBaseline />\n        <BrowserRouter>\n          <App />\n        </BrowserRouter>\n      </ThemeProvider>\n    </Provider>\n  </React.StrictMode>\n);\n\n// If you want to start measuring performance in your app, pass a function\n// to log results (for example: reportWebVitals(console.log))\n// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals\n// reportWebVitals();\n"], "mappings": "AAAA,MAAO,CAAAA,KAAK,KAAM,OAAO,CACzB,MAAO,CAAAC,QAAQ,KAAM,kBAAkB,CACvC,OAASC,QAAQ,KAAQ,aAAa,CACtC;AACA;AACA,OAASC,aAAa,KAAQ,kBAAkB,CAChD,OAASC,aAAa,CAAEC,WAAW,KAAQ,sBAAsB,CACjE,MAAO,CAAAC,WAAW,KAAM,2BAA2B,CAEnD,MAAO,aAAa,CACpB,MAAO,aAAa,CACpB,MAAO,CAAAC,GAAG,KAAM,OAAO,CACvB,OAASC,KAAK,KAAQ,eAAe,CACrC;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAAA,OAAAC,GAAA,IAAAC,IAAA,CAAAC,IAAA,IAAAC,KAAA,yBACA,KAAM,CAAAC,KAAK,CAAGR,WAAW,CAAC,CACxBS,OAAO,CAAE,CACPC,OAAO,CAAE,CACPC,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SACR,CAAC,CACDC,SAAS,CAAE,CACTH,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SACR,CAAC,CACDE,OAAO,CAAE,CACPJ,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SACR,CAAC,CACDG,OAAO,CAAE,CACPL,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SACR,CAAC,CACDI,KAAK,CAAE,CACLN,IAAI,CAAE,SAAS,CACfC,KAAK,CAAE,SAAS,CAChBC,IAAI,CAAE,SACR,CAAC,CACDK,UAAU,CAAE,CACVC,OAAO,CAAE,SAAS,CAClBC,KAAK,CAAE,SACT,CACF,CAAC,CACDC,UAAU,CAAE,CACVC,UAAU,CAAE,qDAAqD,CACjEC,EAAE,CAAE,CACFC,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,QACZ,CAAC,CACDC,EAAE,CAAE,CACFF,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,MACZ,CAAC,CACDE,EAAE,CAAE,CACFH,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,SACZ,CAAC,CACDG,EAAE,CAAE,CACFJ,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,QACZ,CAAC,CACDI,EAAE,CAAE,CACFL,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,SACZ,CAAC,CACDK,EAAE,CAAE,CACFN,UAAU,CAAE,GAAG,CACfC,QAAQ,CAAE,MACZ,CAAC,CACDM,KAAK,CAAE,CACLN,QAAQ,CAAE,MAAM,CAChBO,UAAU,CAAE,GACd,CAAC,CACDC,KAAK,CAAE,CACLR,QAAQ,CAAE,UAAU,CACpBO,UAAU,CAAE,IACd,CACF,CAAC,CACDE,KAAK,CAAE,CACLC,YAAY,CAAE,CAChB,CAAC,CACDC,UAAU,CAAE,CACVC,SAAS,CAAE,CACTC,cAAc,CAAE,CACdC,IAAI,CAAE,CACJC,aAAa,CAAE,MAAM,CACrBhB,UAAU,CAAE,GAAG,CACfW,YAAY,CAAE,CAAC,CACfM,OAAO,CAAE,UACX,CACF,CACF,CAAC,CACDC,OAAO,CAAE,CACPJ,cAAc,CAAE,CACdC,IAAI,CAAE,CACJI,SAAS,CAAE,2EAA2E,CACtFR,YAAY,CAAE,EAChB,CACF,CACF,CAAC,CACDS,YAAY,CAAE,CACZN,cAAc,CAAE,CACdC,IAAI,CAAE,CACJ,0BAA0B,CAAE,CAC1BJ,YAAY,CAAE,CAChB,CACF,CACF,CACF,CACF,CACF,CAAC,CAAC,CAEF,KAAM,CAAAI,IAAI,CAAG3C,QAAQ,CAACiD,UAAU,CAC9BC,QAAQ,CAACC,cAAc,CAAC,MAAM,CAChC,CAAC,CAEDR,IAAI,CAACS,MAAM,cACT3C,IAAA,CAACV,KAAK,CAACsD,UAAU,EAAAC,QAAA,cACf7C,IAAA,CAACR,QAAQ,EAACM,KAAK,CAAEA,KAAM,CAAA+C,QAAA,cACrB3C,KAAA,CAACR,aAAa,EAACS,KAAK,CAAEA,KAAM,CAAA0C,QAAA,eAC1B7C,IAAA,CAACJ,WAAW,GAAE,CAAC,cACfI,IAAA,CAACP,aAAa,EAAAoD,QAAA,cACZ7C,IAAA,CAACH,GAAG,GAAE,CAAC,CACM,CAAC,EACH,CAAC,CACR,CAAC,CACK,CACpB,CAAC,CAED;AACA;AACA;AACA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}